import logging

import sqlalchemy
from sqlalchemy.orm import RelationshipProperty

from server.db import session as session_factory
from server.db.models.base import Base

LOG = logging.getLogger(__name__)


class DBCommon(object):

    @staticmethod
    def get_session(autoflush=True, autocommit=True, expire_on_commit=False):
        session = session_factory.get_session(autoflush=autoflush, autocommit=autocommit,
                                              expire_on_commit=expire_on_commit)
        return session

    @staticmethod
    def clear_session():
        return session_factory.clear_session()

    @staticmethod
    def clear_all_sessions():
        session_factory.clear_all_sessions()

    @staticmethod
    def create():
        Base.metadata.create_all(session_factory.get_engine())

    @staticmethod
    def drop():
        Base.metadata.drop_all(session_factory.get_engine())

    def insert_or_update(self, obj, primary_key, session=None):
        session = session or self.get_session()
        assert obj is not None
        model = type(obj)
        with session.begin(subtransactions=True):
            key_value = getattr(obj, primary_key)
            db_obj = session.query(model).filter_by(**{primary_key: key_value}).first()
            if db_obj:
                for key, k_value in obj.make_dict().items():
                    if key == primary_key or not key_value:
                        continue
                    setattr(db_obj, key, k_value)
            else:
                session.add(obj)

    def insert(self, obj=None, session=None):
        if not session:
            session = self.get_session()
        assert obj is not None
        with session.begin(subtransactions=True):
            try:
                session.add(obj)
            except Exception as e:
                LOG.exception(e)

    @staticmethod
    def _model_query(session, model):
        query = session.query(model)
        # define basic filter condition for model query
        # NOTE(jkoelker) non-admin queries are scoped to their tenant_id
        # NOTE(salvatore-orlando): unless the model allows for shared objects
        query_filter = None

        # NOTE(salvatore-orlando): 'if query_filter' will try to evaluate the
        # condition, raising an exception
        if query_filter is not None:
            query = query.filter(query_filter)
        return query

    def _get_by_id(self, session, model, id):
        query = self._model_query(session, model)
        return query.filter(model.id == id).one()

    @staticmethod
    def _apply_filters_to_query(query, model, filters):
        if filters:
            for key, value in filters.items():
                column = getattr(model, key, None)
                if column:
                    query = query.filter(column.in_(value))
        return query

    def _get_collection_query(self, session, model, filters=None, sorts=None, limit=None):
        collection = self._model_query(session, model)
        collection = self._apply_filters_to_query(collection, model, filters)
        collection = self.paginate_query(collection, model, limit, sorts)
        return collection

    def _get_collection(self, session, model, filters=None, sorts=None, limit=None):
        """ common db method for query data
            :param session the db session
            :param model class name, eg Switch
            :param filters filter by which item eg Switch filters={'sn':['FD3E-FC3B-746A-01C4']} or
            filters={'sn': ['FD3E-FC3B-746A-01C4', 'FD3E-FC3B-746A-01C5']}
            :param sorts sort by which item eg Switch sorts = [('sn', False), ('version', True)]
            :param the num you want to get eg limit=10
        """
        query = self._get_collection_query(session, model, filters=filters,
                                           sorts=sorts,
                                           limit=limit)
        return query.all()

    def _get_collection_count(self, session, model, filters=None):
        return self._get_collection_query(session, model, filters).count()

    def _get_model(self, session, model, filters=None):
        query = self._get_collection_query(session, model, filters=filters)
        return query.first()

    @staticmethod
    def paginate_query(query, model, limit, sorts):
        if not sorts:
            return query

        # A primary key must be specified in sort keys
        assert not (limit and
                    len(set(dict(sorts).keys()) &
                        set(model.__table__.primary_key.columns.keys())) == 0)

        # Add sorting
        for sort_key, sort_direction in sorts:
            sort_dir_func = sqlalchemy.asc if sort_direction else sqlalchemy.desc
            try:
                sort_key_attr = getattr(model, sort_key)
            except AttributeError:
                # Extension attribute doesn't support for sorting. Because it
                # existed in attr_info, it will be catched at here
                msg = "%s is invalid attribute for sort_key" % sort_key
                raise Exception(msg)

            if isinstance(sort_key_attr.property, RelationshipProperty):
                msg = "The attribute '%(attr)s' is reference to other " \
                        "resource, can't used by sort " \
                        "'%(resource)s'" % {'attr': sort_key,
                                             'resource': model.__tablename__}
                raise Exception(msg)
            query = query.order_by(sort_dir_func(sort_key_attr))

        if limit:
            query = query.limit(limit)

        return query

    def merge(self, model_obj, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            session.merge(model_obj, load=True)

    def get_model(self, model, filters=None, session=None):
        if not session:
            session = self.get_session()
        return self._get_model(session, model, filters)

    def update_model(self, model, filters, updates, synchronize_session='fetch', session=None):
        if not session:
            session = self.get_session()
        # assert updates and filters
        with session.begin(subtransactions=True):
            query = self._get_collection_query(session, model, filters)
            return query.update(updates, synchronize_session=synchronize_session)

    def get_collection(self, model, filters=None, sorts=None, limit=None, session=None):
        if not session:
            session = self.get_session()
        return self._get_collection(session, model, filters=filters, sorts=sorts, limit=limit)

    def delete_collection(self, model, filters=None, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            return self._get_collection_query(session, model, filters=filters).delete(
                synchronize_session=False)

    def delete_model(self, model, key, value, session=None):
        session = session or self.get_session()
        column = getattr(model, key, None)
        assert column
        with session.begin(subtransactions=True):
            return session.query(model).filter(column == value).delete(synchronize_session=False)
