# -*- coding: utf-8 -*-
import time
from datetime import datetime, timedelta
from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    Enum,
    DateTime,
    Boolean
)

from server.db.db_common import DBCommon
from server.db.models.base import Base


class OperationLog(Base):
    __tablename__ = 'operation_log'
    id = Column(Integer, autoincrement=True, primary_key=True)
    user = Column(String(32), nullable=False)
    path = Column(String(255), nullable=False)
    method = Column(String(255))
    params_original = Column('params', Text(65535))
    content_original = Column('content', Text(65535), nullable=False)
    status = Column(String(256))

    @property
    def params(self):
        return self.params_original

    @property
    def content(self):
        return self.content_original

    @params.setter
    def params(self, params_original):
        self.params_original = params_original

    @content.setter
    def content(self, content_original):
        self.content_original = content_original


class Event(Base):
    __tablename__ = 'event'
    id = Column(Integer, autoincrement=True, primary_key=True)
    sn = Column(String(64))
    resource_id = Column(String(32))
    type = Column(Enum('info', 'warn', 'error'), nullable=False, default='info')
    msg = Column(String(255), nullable=False)
    count = Column(Integer, default=1)
    status = Column(Enum('unread', 'read', 'ignore', 'handled'), default='unread')
    operator_name = Column(String(32))
    operator_text = Column(Text(2048))
    operator_time = Column(DateTime())
    history_time = Column(Text(2048))

    def __init__(self, sn, type, msg, resource_id=None):
        self.sn = sn
        self.type = type
        self.msg = msg
        self.resource_id = resource_id


class DDMEvents(Base):
    __tablename__ = 'ddm_events'
    id = Column(Integer, autoincrement=True, primary_key=True)
    switch_sn = Column(String(64))
    interface = Column(String(32))
    channel = Column(String(32))
    
    module_name = Column(String(64))
    module_type = Column(String(64))
    
    alert_level = Column(Enum('info', 'warning', 'error'), nullable=False, default='info')
    alert_type = Column(Enum('SupplyVoltage', 'LaserTemperature', 'InputPower', 'OutputPower', 'LaserBiasCurrent'), nullable=False, default='SupplyVoltage')
    count = Column(Integer, default=1)
    alert_msg = Column(String(255), nullable=False)
    resolved = Column(Boolean, default=False)
    resolved_time = Column(DateTime())
    last_alert_time = Column(DateTime())
    
    
class ModulesLink(Base):
    __tablename__ = 'modules_links'
    id = Column(Integer, primary_key=True, autoincrement=True)
    source_sn = Column(String(64))
    source_port = Column(String(64))
    target_sn = Column(String(64))
    target_port = Column(String(64))
    target_mac = Column(String(64))
    light_attenuation_threshold = Column(Integer, default=5)
    link_status = Column(Boolean)


class LicenseCount(Base):
    """
    Deprecated (use LicenseStatisttic)
    """
    __tablename__ = 'license_count'
    id = Column(Integer, autoincrement=True, primary_key=True)
    speed_type = Column(String(32))
    feature_type = Column(String(32))
    remain = Column(Integer, nullable=False, default=0)
    total = Column(Integer, nullable=False, default=0)

class LicenseStatisttic(Base):
    __tablename__ = 'license_statistic'
    id = Column(Integer, autoincrement=True, primary_key=True)
    remain = Column(Integer, nullable=False, default=0)
    total = Column(Integer, nullable=False, default=0)

class MonitorDB(DBCommon):

    def add_event(self, sn, event_type, msg, resource_id=None, session=None):
        session = session or self.get_session()
        msg = msg if len(msg) < 255 else msg[-254:]
        with session.begin(subtransactions=True):
            event = session.query(Event).filter_by(sn=sn, type=event_type, msg=msg, status='unread').with_for_update().first()
            if event:
                event.count += 1
                if event.history_time:
                    history_time_list = event.history_time.split(';')
                    if len(history_time_list) > 12:
                        history_time_list = [history_time_list[0]] + history_time_list[-11:] + [time.strftime("%Y-%m-%d %H:%M:%S")]
                    else:
                        history_time_list = history_time_list + [time.strftime("%Y-%m-%d %H:%M:%S")]
                    history_time = ';'.join(history_time_list)
                else:
                    history_time = str(event.create_time) + ';' + time.strftime("%Y-%m-%d %H:%M:%S")
                event.history_time = history_time
            else:
                event = Event(sn, event_type, msg, resource_id)
                event.history_time = time.strftime("%Y-%m-%d %H:%M:%S")
                session.add(event)

    def update_event_status(self, event_id, status):
        session = self.get_session()
        with session.begin(subtransactions=True):
            event = session.query(Event).filter(Event.id == event_id).first()
            event.status = status

    def handle_event(self, sn, resource_id, msg):
        session = self.get_session()
        with session.begin(subtransactions=True):
            event = session.query(Event).filter(Event.sn == sn, Event.resource_id == resource_id, Event.status == "unread").first()
            event.status = "handled"
            # event.msg = msg

    def update_license_count(self, total, remaining):
        session = self.get_session()
        with session.begin(subtransactions=True):
            license_count = session.query(LicenseCount).first()
            if license_count:
                license_count.total = total
                license_count.remain = remaining
            else:
                license_count = LicenseCount(total=total, remain=remaining)
                session.add(license_count)

    def add_operation_log(self, user, path, method, status, params='', content='', session=None):
        session = session or self.get_session()
        with session.begin(subtransactions=True):
            operation_log = OperationLog(user=user, path=path, method=method, params=params, content=content,
                                         status=status)
            session.add(operation_log)
            
    def record_failed_auth_attempts(self, user, session=None):
        session = session or self.get_session()
        now = datetime.now()
        five_minutes_ago = now - timedelta(minutes=5)
        last_success = session.query(OperationLog).filter(OperationLog.user == user, OperationLog.method == 'login', OperationLog.status == 'success') \
                                                  .order_by(OperationLog.modified_time.desc()) \
                                                  .first()
                                                  
        if last_success and last_success.modified_time >= five_minutes_ago:
            nums = session.query(OperationLog).filter(OperationLog.user == user,
                                                    OperationLog.method == 'login',
                                                    OperationLog.modified_time >= last_success.modified_time,
                                                    OperationLog.status == 'error',
                                                    OperationLog.modified_time <= now).count()
        else:
            nums = session.query(OperationLog).filter(OperationLog.user == user,
                                                    OperationLog.method == 'login',
                                                    OperationLog.modified_time >= five_minutes_ago,
                                                    OperationLog.status == 'error',
                                                    OperationLog.modified_time <= now).count()
        if nums >= 3:
            return True
        return False
    
    def unlock_auth_attempts(self, user, session=None):
        session = session or self.get_session()
        now = datetime.now()
        five_minutes_ago = now - timedelta(minutes=5)
        session.query(OperationLog).filter(OperationLog.user == user,
                                           OperationLog.method == 'login',
                                           OperationLog.modified_time >= five_minutes_ago,
                                           OperationLog.status == 'error',
                                           OperationLog.modified_time <= now).update({OperationLog.status: 'unlock'}, synchronize_session=False)
    
    def add_ddm_event(self, switch_sn: str, interface: str, channel: str, module_name: str, module_type: str, alert_level: str, alert_type: str, alert_msg: str):
        """
        add or update ddm event
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            ddm_event: DDMEvents = session.query(DDMEvents).filter_by(switch_sn=switch_sn, 
                                                                      interface=interface, 
                                                                      channel=channel, 
                                                                      alert_type=alert_type,
                                                                      resolved=False).with_for_update().first()
            if ddm_event:
                ddm_event.count += 1
                ddm_event.alert_msg = alert_msg
                ddm_event.module_name = module_name
                ddm_event.module_type = module_type
                ddm_event.alert_level = alert_level
                ddm_event.last_alert_time = datetime.now()

            else:
                ddm_event = DDMEvents(switch_sn=switch_sn, 
                                      interface=interface, 
                                      channel=channel, 
                                      alert_level=alert_level, 
                                      alert_type=alert_type,
                                      module_name=module_name,
                                      module_type=module_type,
                                      alert_msg=alert_msg,
                                      last_alert_time=datetime.now())
                session.add(ddm_event)

    def update_ddm_event(self, switch_sn: str, interface: str, alert_type: str, channel: str, updates: dict):
        session = self.get_session()
        
        # 定义允许更新的字段
        allowed_fields = {'alert_level', 'alert_msg', 'resolved', 'resolved_time', 'count', 'module_name', 'module_type'}
        
        # 过滤掉非法字段
        filtered_updates = {k: v for k, v in updates.items() if k in allowed_fields}
        
        with session.begin(subtransactions=True):
            # 查询未解决的 DDM 事件
            ddm_event = session.query(DDMEvents).filter(
                DDMEvents.switch_sn == switch_sn,
                DDMEvents.interface == interface,
                DDMEvents.alert_type == alert_type,
                DDMEvents.channel == channel,
                DDMEvents.resolved == False
            ).with_for_update().first()
            
            if not ddm_event:
                raise ValueError(f"No unresolved DDM event found for switch_sn={switch_sn}, interface={interface}, alert_type={alert_type}, channel={channel}")
            
            # 更新字段
            for field, value in filtered_updates.items():
                setattr(ddm_event, field, value)
                
            # 如果标记为已解决，设置解决时间
            if filtered_updates.get('resolved') and not ddm_event.resolved_time:
                ddm_event.resolved_time = datetime.now()

    def resolve_ddm_event(self, switch_sn: str, interface: str, alert_type: str, channel: str):
        return self.update_ddm_event(switch_sn, interface, alert_type, channel, {'resolved': True})

monitor_db = MonitorDB()