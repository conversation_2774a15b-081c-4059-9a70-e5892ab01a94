import ipaddress
import ast
import datetime
from flask import jsonify, request
from sqlalchemy import (
    <PERSON>umn,
    Integer,
    String,
    Text,
    Foreign<PERSON>ey,
    Boolean,
    DateTime,
    Enum,
    JSON,
    Table,
    and_,
    case,
)
from sqlalchemy.orm import relationship, exc
from abc import ABC, abstractmethod

from server.util import utils
from server.constants import CloudPlatform

from server.db.db_common import DBCommon
from server.db.models.base import Base

# Cloud Resources start


class DCVirtualResourcePoolAZ(Base):
    __tablename__ = "virtual_resource_pool_az"
    id = Column(Integer, primary_key=True, autoincrement=True)
    az_name = Column(String(256), index=True)
    fabric_id = Column(Integer, ForeignKey(
        'fabric.id', ondelete='CASCADE', onupdate='CASCADE'))
    resource_type = Column(String(64), nullable=True)
    global_vlan = Column(Text(4096), nullable=True)
    global_vni_vlan = Column(Text(4096), nullable=True)
    auth_info = Column(Text(65535), nullable=True)
    connect_status = Column(Boolean, default=False)
    connect_active_time = Column(DateTime(), nullable=True) 
    fabric = relationship("Fabric", backref="virtual_resource_pool_az", uselist=False)

    @property
    def fabric_name(self):
        return self.fabric.fabric_name if self.fabric else None

    @property
    def auth_info_dict(self):
        try:
            return ast.literal_eval(self.auth_info)
        except Exception as _:
            return {}

class DCVirtualResourceVPC(Base):
    __tablename__ = "virtual_resource_vpc"
    id = Column(Integer, primary_key=True, autoincrement=True)
    vpc_id = Column(String(512))
    vpc_name = Column(String(512))
    tenant = Column(String(512))
    fabric_id = Column(Integer, ForeignKey(
        'fabric.id', ondelete='CASCADE', onupdate='CASCADE'))
    az_id = Column(Integer, ForeignKey(
        'virtual_resource_pool_az.id', ondelete='CASCADE', onupdate='CASCADE'))
    resource_create_time = Column(DateTime(), nullable=False)
    fabric = relationship("Fabric", backref="virtual_resource_vpc", uselist=False)
    az = relationship("DCVirtualResourcePoolAZ", backref="virtual_resource_vpc", uselist=False)

    @property
    def fabric_name(self):
        return self.fabric.fabric_name if self.fabric else None
    @property
    def az_name(self):
        return self.az.az_name if self.az else None


class DCVirtualResourceNetwork(Base):
    __tablename__ = "virtual_resource_network"
    id = Column(Integer, primary_key=True, autoincrement=True)
    network_name = Column(String(512))
    fabric_id = Column(Integer, ForeignKey(
        'fabric.id', ondelete='CASCADE', onupdate='CASCADE'))
    az_id = Column(Integer, ForeignKey(
        'virtual_resource_pool_az.id', ondelete='CASCADE', onupdate='CASCADE'))
    vpc_id = Column(Integer, ForeignKey(
        'virtual_resource_vpc.id', ondelete='CASCADE', onupdate='CASCADE'))
    host_name = Column(String(512))
    vm_count = Column(Integer)
    host_count = Column(Integer)
    vlan_id = Column(Integer, server_default='0', nullable=False)
    resource_create_time = Column(DateTime(), nullable=False)
    virtual_network_id = Column(Integer, ForeignKey('dc_virtual_network.id', ondelete='SET NULL', onupdate='CASCADE'), nullable=True) 
    fabric = relationship("Fabric", backref="virtual_resource_network", uselist=False)
    az = relationship("DCVirtualResourcePoolAZ", backref="virtual_resource_network", uselist=False)
    vpc = relationship("DCVirtualResourceVPC", backref="virtual_resource_network", uselist=False)
    virtual_network = relationship("DCVirtualNetwork", backref="virtual_resource_network", uselist=False)

    @property
    def fabric_name(self):
        return self.fabric.fabric_name if self.fabric else None
    @property
    def az_name(self):
        return self.az.az_name if self.az else None
    @property
    def vpc_name(self):
        return self.vpc.vpc_name if self.vpc else None


class DCVirtualResourcesVM(Base):
    __tablename__ = "virtual_resource_vm"
    id = Column(Integer, primary_key=True, autoincrement=True)
    vm_name = Column(String(1024))
    vm_ip_address = Column(String(64))
    host_ip_address = Column(String(64))
    network_name = Column(String(512))
    fabric_id = Column(Integer, ForeignKey(
        'fabric.id', ondelete='CASCADE', onupdate='CASCADE'))
    az_id = Column(Integer, ForeignKey(
        'virtual_resource_pool_az.id', ondelete='CASCADE', onupdate='CASCADE'))
    vpc_id = Column(Integer, ForeignKey(
        'virtual_resource_vpc.id', ondelete='CASCADE', onupdate='CASCADE'))
    power_status = Column(String(64))
    info = Column(Text(1024))
    fabric = relationship("Fabric", backref="virtual_resource_vm", uselist=False)
    az = relationship("DCVirtualResourcePoolAZ", backref="virtual_resource_vm", uselist=False)
    vpc = relationship("DCVirtualResourceVPC", backref="virtual_resource_vm", uselist=False)

    @property
    def fabric_name(self):
        return self.fabric.fabric_name if self.fabric else None
    @property
    def az_name(self):
        return self.az.az_name if self.az else None
    @property
    def vpc_name(self):
        return self.vpc.vpc_name if self.vpc else None


class DCVirtualResourceHost(Base):
    __tablename__ = 'virtual_resource_host'

    id = Column(Integer, primary_key=True, autoincrement=True)
    host_name = Column(String(length=512), nullable=False)
    description = Column(String(length=128), nullable=True)
    management_ip = Column(String(length=64), nullable=True)
    username = Column(String(length=128), nullable=True)
    password = Column(String(length=128), nullable=True)
    connect_status = Column(Boolean, default=False)
    az_id = Column(Integer, ForeignKey('virtual_resource_pool_az.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    status = Column(Enum('Not Deployed', 'Deploying', 'Deployed', 'Deploy Failed', 'Deleting', 'Delete Failed', name='port_status_enum'), nullable=False, server_default='Not Deployed')

    az = relationship("DCVirtualResourcePoolAZ", backref="virtual_resource_host", uselist=False)

class DCVirtualResourceHostLink(Base):
    __tablename__ = 'virtual_resource_host_link'

    id = Column(Integer, primary_key=True, autoincrement=True)
    host_id = Column(Integer, ForeignKey('virtual_resource_host.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    vlan_domain_id = Column(Integer, ForeignKey('resource_pool_vlan_domain.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    port_group_name = Column(String(length=128), nullable=True)
    connect_mode = Column(String(length=32), nullable=True)  # access || trunk
    link_type = Column(String(length=32), nullable=True) # MLAG leaf || Single leaf
    link_count = Column(String(length=32), nullable=True) # single || dual
    status = Column(Enum('Not Deployed', 'Deploying', 'Deployed', 'Deploy Failed', 'Deleting', 'Delete Failed', name='port_status_enum'), nullable=False, server_default='Not Deployed')

    host = relationship("DCVirtualResourceHost", backref="host_link", uselist=False)

class DCVirtualResourceHostLinkPort(Base):
    __tablename__ = 'virtual_resource_host_linkport'

    id = Column(Integer, primary_key=True, autoincrement=True)
    link_id = Column(Integer, ForeignKey('virtual_resource_host_link.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    switch_sn = Column(String(length=128), nullable=False)
    logic_device_id = Column(Integer, ForeignKey('dc_fabric_topology_node.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    port_name = Column(String(length=256), nullable=True)
    disable_port_list = Column(String(length=512), nullable=True)
    lag_id = Column(Integer, nullable=True)
    status = Column(Enum('Not Deployed', 'Deploying', 'Deployed', 'Deploy Failed', 'Deleting', 'Delete Failed', name='port_status_enum'), nullable=False, server_default='Not Deployed')

    link = relationship("DCVirtualResourceHostLink", backref="link_ports", uselist=False)
    logic_device = relationship("DCFabricTopologyNode", backref="virtual_resource_host_link", uselist=False)

class DCVirtualHostNetworkMapping(Base):
    __tablename__ = 'virtual_resource_hostlink_network_mapping'
    id = Column(Integer, primary_key=True, autoincrement=True)
    hostlink_id = Column(Integer, ForeignKey('virtual_resource_host_link.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    network_id = Column(Integer, ForeignKey('virtual_resource_network.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    status = Column(Enum('Connecting', 'Connect Successful', 'Connect Failed', 'Disconnecting', 'Disconnected', 'Disconnect Failed', name='status_enum'), nullable=False, server_default='Disconnected')  
    in_use = Column(Boolean, default=False)
    
    link = relationship("DCVirtualResourceHostLink", backref="link_mapping", uselist=False)
    network = relationship("DCVirtualResourceNetwork", backref="link_mapping", uselist=True)

class NodeTemplate(Base):
    __tablename__ = 'node_template'

    id = Column(Integer, primary_key=True, autoincrement=True)
    template_name = Column(String(128))
    total_ports = Column(Integer, nullable=False)
    template_info = Column(JSON)   


class NodeGroup(Base):
    __tablename__ = 'node_group'

    id = Column(Integer, primary_key=True, autoincrement=True)
    az_id = Column(Integer, ForeignKey('virtual_resource_pool_az.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    fabric_id = Column(Integer, ForeignKey('fabric.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    node_template_id = Column(Integer, ForeignKey('node_template.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    nodegroup_name = Column(String(128))
    description = Column(String(128))
    status = Column(Enum('Not Deployed', 'Deploying', 'Deployed', 'Deploy Failed', 'Deleting', 'Delete Failed', name='status_enum'), nullable=False, server_default='Not Deployed')
    
    fabric = relationship("Fabric", backref="node_group", uselist=False)
    az = relationship("DCVirtualResourcePoolAZ", backref="node_group", uselist=False)
    node_host = relationship("NodeHost", backref="node_group", uselist=True)
    switch_portgroup = relationship("SwitchPortgroup", backref="node_group", uselist=True)
    
    @property
    def fabric_name(self):
        return self.fabric.fabric_name if self.fabric else None
    @property
    def az_name(self):
        return self.az.az_name if self.az else None
    

class NodeHost(Base):
    __tablename__ = 'node_host'

    id = Column(Integer, primary_key=True, autoincrement=True)
    host_name = Column(String(512))
    node_group_id = Column(Integer, ForeignKey('node_group.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    ip_addr = Column(String(64))
    username = Column(String(128))
    password = Column(String(128))
    node_nic_portgroup = relationship("NodeNicPortgroup", backref="node_host", uselist=True)
    

class SwitchPortgroup(Base):
    __tablename__ = 'switch_portgroup'

    id = Column(Integer, primary_key=True, autoincrement=True)
    portgroup_name = Column(String(512))
    node_group_id = Column(Integer, ForeignKey('node_group.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    vlan_domain_id = Column(Integer, ForeignKey('resource_pool_vlan_domain.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    connect_mode = Column(Enum('access', 'trunk', name='connect_mode_enum'), nullable=False)
    link_type = Column(Enum('MLAG Leaf', 'Single Leaf', name='link_type_enum'), nullable=False)
    link_count = Column(Integer, nullable=False)
    status = Column(Enum('Not Deployed', 'Deploying', 'Deployed', 'Deploy Failed', 'Deleting', 'Delete Failed', name='status_enum'), nullable=False, server_default='Not Deployed')
    
    switch_portgroup_info = relationship("SwitchPortgroupInfo", backref="SwitchPortgroup", uselist=True)
    

class NodeNicPortgroup(Base):
    __tablename__ = 'node_nic_portgroup'

    id = Column(Integer, primary_key=True, autoincrement=True)
    portgroup_name = Column(String(512))
    node_host_id = Column(Integer, ForeignKey('node_host.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    switch_portgroup_id = Column(Integer, ForeignKey('switch_portgroup.id', ondelete='SET NULL', onupdate='CASCADE'), nullable=True)
    network_id = Column(Integer, ForeignKey('virtual_resource_network.id', ondelete='SET NULL', onupdate='CASCADE'), nullable=True) 
    status = Column(Enum('Connecting', 'Connect Successful', 'Connect Failed', 'Disconnecting', 'Disconnected', 'Disconnect Failed', name='status_enum'), nullable=False, server_default='Disconnected')  
    
    switch_portgroup = relationship("SwitchPortgroup", backref="nic_portgroup", uselist=False)
 
 
class SwitchPortgroupInfo(Base):
    __tablename__ = 'switch_portgroup_info'

    id = Column(Integer, primary_key=True, autoincrement=True)
    portgroup_id = Column(Integer, ForeignKey('switch_portgroup.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    logic_device_id = Column(Integer, ForeignKey('dc_fabric_topology_node.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    switch_sn = Column(String(128), nullable=False)
    port_info = Column(JSON)
    status = Column(Enum('Not Deployed', 'Deploying', 'Deployed', 'Deploy Failed', 'Deleting', 'Delete Failed', name='port_status_enum'), nullable=False, server_default='Not Deployed')   
    
    logic_device = relationship("DCFabricTopologyNode", backref="switch_portgroup_info", uselist=False)


class SwitchPortgroupConfig(Base):
    __tablename__ = 'switch_portgroup_config'

    id = Column(Integer, primary_key=True, autoincrement=True)
    logic_device_id = Column(Integer, ForeignKey('dc_fabric_topology_node.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False)
    switch_sn = Column(String(128), nullable=False)
    related_id = Column(Integer, nullable=False)
    related_type = Column(Enum('BareMetal', 'CloudPlatform', name='config_related_type'), nullable=False, server_default='BareMetal')
    config = Column(JSON)
    status = Column(Enum('Not Deployed', 'Deploying', 'Deployed', 'Deploy Failed', 'Deleting', 'Delete Failed', name='port_status_enum'), nullable=False, server_default='Not Deployed') 
    
    logic_device = relationship("DCFabricTopologyNode", backref="switch_portgroup_config", uselist=False)


class VlanDomainGroup(Base):
    __tablename__ = 'vlan_domain_group'

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_name = Column(String(64))
    description = Column(String(128))
    fabric_id = Column(Integer, ForeignKey('fabric.id', ondelete='CASCADE'))
    fabric = relationship("Fabric", backref="vlan_domain_group", uselist=False)

class DCVirtualResourceDB(DBCommon):
    # AZ start
    def update_virtual_resource_pool_az(self, az_id, az_name, fabric_id, resource_type,
                                        global_vlan, global_vni_vlan, auth_info):
        session = self.get_session()
        with session.begin(subtransactions=True):
            existing_az = None
            if az_id:
                existing_az = session.query(DCVirtualResourcePoolAZ).filter(
                    DCVirtualResourcePoolAZ.id == az_id).first()
                az = session.query(DCVirtualResourcePoolAZ).filter(
                    DCVirtualResourcePoolAZ.az_name == az_name).first()
                if az and az.id != az_id:
                    raise Exception(f"az_name: {az_name} already exists")
            else:
                existing_az = session.query(DCVirtualResourcePoolAZ).filter(
                    DCVirtualResourcePoolAZ.az_name == az_name).first()
                if existing_az:
                    raise Exception(f"az_name: {az_name} already exists")

            if existing_az:
                existing_az.az_name = az_name
                existing_az.fabric_id = fabric_id
                existing_az.resource_type = resource_type
                existing_az.global_vlan = global_vlan
                existing_az.global_vni_vlan = global_vni_vlan
                existing_az.auth_info = auth_info
            else:
                if resource_type == CloudPlatform.BAREMETAL:
                    az = DCVirtualResourcePoolAZ(az_name=az_name, fabric_id=fabric_id, resource_type=resource_type,
                                                global_vlan=global_vlan, global_vni_vlan=global_vni_vlan, auth_info=auth_info, connect_status=True)
                else:
                    az = DCVirtualResourcePoolAZ(az_name=az_name, fabric_id=fabric_id, resource_type=resource_type,
                                                global_vlan=global_vlan, global_vni_vlan=global_vni_vlan, auth_info=auth_info)
                session.add(az)

    def update_virtual_resource_pool_az_connect_status(self, az_id, connect_status, connect_active_time=None):
        session = self.get_session()
        with session.begin(subtransactions=True):
            existing_az = session.query(DCVirtualResourcePoolAZ).filter(
                DCVirtualResourcePoolAZ.id == az_id).first()
            if existing_az:
                if connect_status:
                     existing_az.connect_active_time = connect_active_time
                existing_az.connect_status = connect_status
                
    def update_virtual_resource_pool_host_connect_status(self, az_id, connect_status):
        session = self.get_session()
        with session.begin(subtransactions=True):
            existing_host = session.query(DCVirtualResourceHost).filter(
                DCVirtualResourceHost.az_id == az_id).first()
            if existing_host:
                existing_host.connect_status = connect_status

    def get_virtual_resource_pool_az_by_id(self, az_id):
        session = self.get_session()
        az = session.query(DCVirtualResourcePoolAZ).filter(
            DCVirtualResourcePoolAZ.id == az_id).first()
        return az

    def del_virtual_resource_pool_az_by_id(self, az_id):
        session = self.get_session()
        session.query(DCVirtualResourcePoolAZ).filter(
            DCVirtualResourcePoolAZ.id == az_id).delete()
        
    def get_cloud_virtual_resource_pool_az(self):
        session = self.get_session()
        result = session.query(DCVirtualResourcePoolAZ).filter(
            DCVirtualResourcePoolAZ.resource_type.in_([CloudPlatform.OPENSTACK,  CloudPlatform.VSPHERE])).all()
        return result

    # VPC start
    def update_virtual_resource_vpcs(self, vpc_list, az):
        session = self.get_session()
        with session.begin(subtransactions=True):
            existing_vpc_ids = {vpc.get('id') for vpc in vpc_list}
            existing_vpcs = session.query(DCVirtualResourceVPC).filter(
                DCVirtualResourceVPC.az_id == az.id).all()
            for vpc in existing_vpcs:
                if vpc.vpc_id not in existing_vpc_ids:
                    session.delete(vpc)
            for vpc in vpc_list:
                existing_vpc = session.query(DCVirtualResourceVPC).filter(
                    and_(DCVirtualResourceVPC.vpc_id == vpc.get('id'), DCVirtualResourceVPC.fabric_id == az.fabric.id, DCVirtualResourceVPC.az_id == az.id)).first()
                if existing_vpc:
                    existing_vpc.vpc_id = vpc.get('id')
                    existing_vpc.vpc_name = vpc.get('name')
                    existing_vpc.fabric_id = az.fabric.id
                    existing_vpc.az_id = az.id
                else:
                    new_vpc = DCVirtualResourceVPC(vpc_id=vpc.get('id'), vpc_name=vpc.get('name'), tenant="", fabric_id=az.fabric.id,
                                                   az_id=az.id, resource_create_time=datetime.datetime.now())
                    session.add(new_vpc)
                    
    def update_virtual_resource_vpc(self, id, az_id, fabric_id, vpc_name, vpc_id, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_vpc = session.query(DCVirtualResourceVPC).filter(DCVirtualResourceVPC.id == id).first()
            if existing_vpc:
                existing_vpc.vpc_id = vpc_id
                existing_vpc.vpc_name = vpc_name
                existing_vpc.fabric_id = fabric_id
                existing_vpc.az_id = az_id
                vpc=existing_vpc
            else:
                vpc = DCVirtualResourceVPC(vpc_id=vpc_id, vpc_name=vpc_name, tenant="", fabric_id=fabric_id,
                                                az_id=az_id, resource_create_time=datetime.datetime.now())
                session.add(vpc)
        return vpc

    def get_virtual_resource_vpc_by_name(self, vpc_name, fabric_id, az_id):
        session = self.get_session()
        vpc = session.query(DCVirtualResourceVPC).filter(
            and_(DCVirtualResourceVPC.vpc_name == vpc_name, DCVirtualResourceVPC.fabric_id == fabric_id, DCVirtualResourceVPC.az_id == az_id)).first()
        return vpc

    def del_virtual_resource_vpc_by_id(self, vpc_id):
        session = self.get_session()
        session.query(DCVirtualResourceVPC).filter(
            DCVirtualResourceVPC.id == vpc_id).delete()

    # Network start
    def update_virtual_resource_networks(self, network_list, az, resource_type):
        session = self.get_session()
        with session.begin(subtransactions=True):
            existing_network_names = {network.get('name') for network in network_list}
            existing_networks = session.query(DCVirtualResourceNetwork).filter(
                DCVirtualResourceNetwork.az_id == az.id).all()
            for network in existing_networks:
                if network.network_name not in existing_network_names:
                    session.delete(network)
            for network in network_list:
                vpc_ins = self.get_virtual_resource_vpc_by_name(vpc_name=network.get('project_name'), 
                    fabric_id=az.fabric.id, az_id=az.id)
                existing_network = None
                if resource_type == CloudPlatform.VSPHERE:
                    existing_network = session.query(DCVirtualResourceNetwork).filter(
                        and_(DCVirtualResourceNetwork.network_name == network.get('name'), DCVirtualResourceNetwork.az_id == az.id, DCVirtualResourceNetwork.vpc_id == vpc_ins.id, DCVirtualResourceNetwork.fabric_id == az.fabric.id, DCVirtualResourceNetwork.host_name == network.get('host_name')) ).first()
                elif resource_type == CloudPlatform.OPENSTACK:
                    existing_network = session.query(DCVirtualResourceNetwork).filter(
                        and_(DCVirtualResourceNetwork.network_name == network.get('name'), DCVirtualResourceNetwork.az_id == az.id, DCVirtualResourceNetwork.vpc_id == vpc_ins.id, DCVirtualResourceNetwork.fabric_id == az.fabric.id) ).first()

                if existing_network:
                    existing_network.fabric_id = az.fabric.id
                    existing_network.az_id = az.id
                    existing_network.vpc_id = vpc_ins.id
                    existing_network.vm_count = network.get('vm_counter')
                    existing_network.host_count = 1
                    existing_network.vlan_id = network.get('vlan_id')
                else:
                    new_network = DCVirtualResourceNetwork(network_name=network.get('name'), fabric_id=az.fabric.id, az_id=az.id,
                                                           vpc_id=vpc_ins.id, vm_count=network.get('vm_counter'), host_count=1, host_name=network.get('host_name',''),
                                                           resource_create_time=datetime.datetime.now(), vlan_id=network.get('vlan_id'))
                    session.add(new_network)
    
    def update_baremetal_vl2(self, network_id, name, fabric_id, az_id, vpc_id, vlan_id, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_network = None
            
            if network_id:
                existing_network = session.query(DCVirtualResourceNetwork).filter(
                    DCVirtualResourceNetwork.id == network_id).first()
                network = session.query(DCVirtualResourceNetwork).filter(
                    DCVirtualResourceNetwork.network_name == name, DCVirtualResourceNetwork.az_id == az_id).first()
                if network and network.id != network_id:
                    raise Exception(f"vl2 name: {name} already exists")
            else:
                existing_network = session.query(DCVirtualResourceNetwork).filter(
                    DCVirtualResourceNetwork.network_name == name, DCVirtualResourceNetwork.az_id == az_id).first()
                if existing_network:
                    raise Exception(f"vl2 name: {name} already exists")

            if existing_network:
                existing_network.network_name = name
                existing_network.fabric_id = fabric_id
                existing_network.az_id = az_id
                existing_network.vlan_id = vlan_id
                existing_network.vpc_id= vpc_id
                network = existing_network
            else:
                network = DCVirtualResourceNetwork(network_name=name, fabric_id=fabric_id, az_id=az_id, vpc_id=vpc_id, vlan_id=vlan_id, resource_create_time=datetime.datetime.now())
                session.add(network)
        return network
    
    def get_virtual_resource_network_by_id(self, network_id):
        session = self.get_session()
        network = session.query(DCVirtualResourceNetwork).filter(
            DCVirtualResourceNetwork.id == network_id).first()
        return network
    
    def get_virtual_resource_networks_by_az(self, az_id):
        session = self.get_session()
        networks = session.query(DCVirtualResourceNetwork).filter(
            DCVirtualResourceNetwork.az_id == az_id).all()
        return networks

    def get_virtual_resource_network_by_name(self, network_name):
        session = self.get_session()
        network = session.query(DCVirtualResourceNetwork).filter(
            DCVirtualResourceNetwork.network_name == network_name).first()
        return network

    def del_virtual_resource_network_by_name(self, network_name):
        session = self.get_session()
        session.query(DCVirtualResourceNetwork).filter(
            DCVirtualResourceNetwork.network_name == network_name).delete()
    
    def del_virtual_resource_network_by_id(self, network_id):
        session = self.get_session()
        session.query(DCVirtualResourceNetwork).filter(
            DCVirtualResourceNetwork.id == network_id).delete()

    # VM start
    def update_virtual_resources_vms(self, vm_list, az):
        session = self.get_session()
        with session.begin(subtransactions=True):
            existing_vm_names = {vm.get('name') for vm in vm_list}
            existing_vms = session.query(DCVirtualResourcesVM).filter(
                DCVirtualResourcesVM.az_id == az.id).all()
            for vm in existing_vms:
                if vm.vm_name not in existing_vm_names:
                    session.delete(vm)
            for vm in vm_list:
                vpc_ins = self.get_virtual_resource_vpc_by_name(vpc_name=vm.get('project_name'), 
                    fabric_id=az.fabric.id, az_id=az.id)
                existing_vm = session.query(DCVirtualResourcesVM).filter(
                    and_(DCVirtualResourcesVM.vm_name == vm.get("name"), DCVirtualResourcesVM.az_id == az.id, DCVirtualResourcesVM.vpc_id == vpc_ins.id, DCVirtualResourcesVM.fabric_id == az.fabric.id)).first()

                if existing_vm:
                    existing_vm.vm_ip_address = str(vm.get("vm_ip"))
                    existing_vm.host_ip_address = vm.get("hypervisor_hostname")
                    existing_vm.network_name = str(vm.get("networks"))
                    existing_vm.fabric_id = az.fabric.id
                    existing_vm.az_id = az.id
                    existing_vm.vpc_id = vpc_ins.id
                    existing_vm.power_status = vm.get("status")
                    existing_vm.info = str(vm)
                else:
                    new_vm = DCVirtualResourcesVM(vm_name=vm.get("name"), vm_ip_address=str(vm.get("vm_ip")), host_ip_address=vm.get("hypervisor_hostname"),
                                                  network_name=str(vm.get("networks")), fabric_id=az.fabric.id, az_id=az.id, vpc_id=vpc_ins.id,
                                                  power_status=vm.get("status"), info=str(vm))
                    session.add(new_vm)

    def get_virtual_resources_vm_by_name(self, vm_name):
        session = self.get_session()
        vm = session.query(DCVirtualResourcesVM).filter(
            DCVirtualResourcesVM.vm_name == vm_name).first()
        return vm

    def del_virtual_resources_vm_by_name(self, vm_name):
        session = self.get_session()
        session.query(DCVirtualResourcesVM).filter(
            DCVirtualResourcesVM.vm_name == vm_name).delete()
        
    def get_virtual_resource_pool_az_by_fabric(self, fabric_id, type_list=None):
        session = self.get_session()
        if type_list:
            az = session.query(DCVirtualResourcePoolAZ).filter(
                and_(DCVirtualResourcePoolAZ.fabric_id == fabric_id, DCVirtualResourcePoolAZ.resource_type.in_(type_list))).all()
        else:
            az = session.query(DCVirtualResourcePoolAZ).filter(
                DCVirtualResourcePoolAZ.fabric_id == fabric_id).all()
        return az
    
    def get_virtual_resource_pool_az(self):
        session = self.get_session()
        az_list = session.query(DCVirtualResourcePoolAZ).all()
        return az_list
        
    # cloud host start
    def update_virtual_resources_cloud_host(self, host_list, az):
        session = self.get_session()
        with session.begin(subtransactions=True):
            existing_host_ip = {host.get('host_ip') for host in host_list}
            print(existing_host_ip)
            existing_hosts = session.query(DCVirtualResourceHost).filter(
                DCVirtualResourceHost.az_id == az.id).all()
            for host in existing_hosts:
                # 没有学到的host 标记状态为断链
                if host.management_ip not in existing_host_ip:
                    host.connect_status = False
                else:
                    host.connect_status = True
                    
            for host in host_list:
                existing_host = session.query(DCVirtualResourceHost).filter(
                    and_(DCVirtualResourceHost.host_name == host.get("host_name"), DCVirtualResourceHost.az_id == az.id)).first()

                if existing_host:
                    existing_host.connect_status = True
                    existing_host.management_ip = host.get("host_ip")
                else:
                    new_host = DCVirtualResourceHost(host_name=host.get("host_name"), management_ip=str(host.get("host_ip")), az_id=az.id, 
                                                     connect_status=True, status='Not Deployed')
                    session.add(new_host)
        
    # Host start
    def update_virtual_resource_host(self, host_id=None, host_name=None, description=None, management_ip=None, 
                                     username=None, password=None, az_id=None, session=None, status=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_host=None
            if host_id:
                existing_host = session.query(DCVirtualResourceHost).filter(
                    DCVirtualResourceHost.id == host_id).first()
                host = session.query(DCVirtualResourceHost).filter(
                    DCVirtualResourceHost.host_name == host_name, DCVirtualResourceHost.az_id == az_id).first()
                if host and host.id != host_id:
                    raise Exception(f"node name: {host_name} already exists")
            else:
                existing_host = session.query(DCVirtualResourceHost).filter(
                    DCVirtualResourceHost.host_name == host_name, DCVirtualResourceHost.az_id == az_id).first()
                if existing_host:
                    raise Exception(f"node name: {host_name} already exists")

            if existing_host:
                if description is not None:
                    existing_host.description = description
                if status is not None:
                    existing_host.status = status
                if username is not None:
                    existing_host.username = username
                if password is not None:
                    existing_host.password = password    
                host = existing_host
            else:
                host = session.query(DCVirtualResourceHost).filter(
                    DCVirtualResourceHost.host_name == host_name, DCVirtualResourceHost.az_id == az_id).first()
                if host:
                    raise Exception(f"host_name: {host_name} az_id: {az_id} already exists")
                
                host = DCVirtualResourceHost(host_name=host_name, description=description,
                                             management_ip=management_ip, username=username,
                                             password=password, az_id=az_id, status='Not Deployed')
                session.add(host)
        return host
                
    def get_virtual_resource_host_by_az_list(self, az_id_list):
        session = self.get_session()
        host = session.query(DCVirtualResourceHost).filter(DCVirtualResourceHost.az_id.in_(az_id_list))
        return host

    def get_virtual_resource_host_by_id(self, host_id):
        session = self.get_session()
        host = session.query(DCVirtualResourceHost).filter(DCVirtualResourceHost.id == host_id).first()
        return host

    def del_virtual_resource_host_by_id(self, host_id):
        session = self.get_session()
        session.query(DCVirtualResourceHost).filter(DCVirtualResourceHost.id == host_id).delete()

    # Host Link start
    def update_virtual_resource_host_link(self, link_id=None, host_id=None, vlan_domain_id=None, port_group_name=None, connect_mode=None, 
                                          link_type=None, link_count=None, session=None, status=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_link = session.query(DCVirtualResourceHostLink).filter(
                DCVirtualResourceHostLink.id == link_id
            ).first()

            if existing_link:
                if port_group_name is not None:
                    existing_link.port_group_name = port_group_name
                if connect_mode is not None:
                    existing_link.connect_mode = connect_mode
                if link_type is not None:
                    existing_link.link_type = link_type
                if link_count is not None:
                    existing_link.link_count = link_count
                if status is not None:
                    existing_link.status = status
                link=existing_link
            else:
                link = DCVirtualResourceHostLink(host_id=host_id, vlan_domain_id=vlan_domain_id, 
                                                 port_group_name=port_group_name, connect_mode=connect_mode, 
                                                 link_type=link_type, link_count=link_count, status='Not Deployed')
                session.add(link)
        return link

    def get_virtual_resource_host_link(self, host_id):
        session = self.get_session()
        link = session.query(DCVirtualResourceHostLink).filter(
            DCVirtualResourceHostLink.host_id == host_id
        ).all()
        return link
    
    def get_virtual_resource_host_link_by_id(self, link_id):
        session = self.get_session()
        link = session.query(DCVirtualResourceHostLink).filter(
            DCVirtualResourceHostLink.id == link_id
        ).first()
        return link

    def del_virtual_resource_host_link(self, link_id):
        session = self.get_session()
        session.query(DCVirtualResourceHostLink).filter(
            DCVirtualResourceHostLink.id == link_id
        ).delete()
        
    def del_virtual_resource_host_link_list(self, link_id_list, session=None):
        if not session:
            session = self.get_session()
        session.query(DCVirtualResourceHostLink).filter(
            DCVirtualResourceHostLink.id.in_(link_id_list)
        ).delete()

    # Host Link Port start
    def update_virtual_resource_host_link_port(self, port_id=None, link_id=None, logic_device_id=None, switch_sn=None, 
                                               port_name=None, session=None, disable_port=None, lag_id=None, status=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_port = session.query(DCVirtualResourceHostLinkPort).filter(
                DCVirtualResourceHostLinkPort.id == port_id
            ).first()

            if existing_port:
                if logic_device_id is not None: 
                    existing_port.logic_device_id = logic_device_id
                if switch_sn is not None: 
                    existing_port.switch_sn = switch_sn
                if port_name is not None: 
                    existing_port.port_name = port_name
                if disable_port is not None: 
                    existing_port.disable_port_list = disable_port
                if lag_id is not None: 
                    existing_port.lag_id = lag_id
                if status is not None:
                    existing_port.status = status
            else:
                port = DCVirtualResourceHostLinkPort(link_id=link_id, logic_device_id=logic_device_id, switch_sn=switch_sn, port_name=port_name, 
                                                     disable_port_list=disable_port, lag_id=lag_id, status='Not Deployed')
                session.add(port)

    def get_virtual_resource_host_link_port(self, link_id):
        session = self.get_session()
        ports = session.query(DCVirtualResourceHostLinkPort).filter(
            DCVirtualResourceHostLinkPort.link_id == link_id
        ).all()
        return ports
    
    def get_virtual_resource_host_link_port_by_id(self, id):
        session = self.get_session()
        port = session.query(DCVirtualResourceHostLinkPort).filter(
            DCVirtualResourceHostLinkPort.id == id
        ).first()
        return port

    def del_virtual_resource_host_link_port(self, port_id):
        session = self.get_session()
        session.query(DCVirtualResourceHostLinkPort).filter(
            DCVirtualResourceHostLinkPort.id == port_id
        ).delete()
    
    def del_virtual_resource_host_link_port_list(self, port_id_list, session=None):
        if not session:
            session = self.get_session()
        session.query(DCVirtualResourceHostLinkPort).filter(
            DCVirtualResourceHostLinkPort.id.in_(port_id_list)
        ).delete()
        
    def update_virtual_resource_host_link_network_mapping(self, used_network_dict, az):
        session = self.get_session()
        for host_name, network_list in used_network_dict.items():            
            network_info = {}
            for item in network_list:
                network_name = item['name']
                network_info[network_name] = item
            
            networks = session.query(DCVirtualResourceNetwork).filter(DCVirtualResourceNetwork.network_name.in_(network_info.keys()), 
                                                                      DCVirtualResourceNetwork.host_name == host_name,
                                                                      DCVirtualResourceNetwork.az_id == az.id).all()
            
            # 当前只取一个
            host = session.query(DCVirtualResourceHost).filter(DCVirtualResourceHost.management_ip == host_name, DCVirtualResourceHost.az_id == az.id).first()
            host_link_id = None
            link_ids = [] # 存放所有linkid 用于查看是否需要删除mapping
            for link in host.host_link:
                link_ids.append(link.id)
                ## link限制只有一个 且必须为Deployed
                if link.status == "Deployed":
                    host_link_id = link.id 

            with session.begin(subtransactions=True):
                existing_mappings = session.query(DCVirtualHostNetworkMapping).filter(DCVirtualHostNetworkMapping.hostlink_id.in_(link_ids)).all()
                mapping_ids = []
                for network in networks:
                    in_use = True if network_info[network.network_name]["vm_counter"] else False
                    if host_link_id:
                        mapping = session.query(DCVirtualHostNetworkMapping).filter(DCVirtualHostNetworkMapping.hostlink_id == host_link_id,
                                                                                    DCVirtualHostNetworkMapping.network_id == network.id).first()
                        if mapping:
                            mapping_ids.append(mapping.id)
                            mapping.in_use = in_use
                        else:
                            mapping = DCVirtualHostNetworkMapping(hostlink_id=host_link_id, network_id=network.id, status="Disconnected", in_use=in_use)
                            session.add(mapping)
                            mapping_ids.append(mapping.id)
                        
                for mapping in existing_mappings:
                    if mapping.id not in mapping_ids:
                        ## 如果状态已经是Disconnected 则直接删除 否则将状态标记为Disconnecting 等待配置下发删除
                        if mapping.status == "Disconnected":
                            session.delete(mapping)
                        else:
                            mapping.status = "Disconnecting"
                            
    def get_virtual_resource_host_link_network_mapping(self, network_id, query_in_use=True):
        session = self.get_session()
        if query_in_use:
            link_mappings = session.query(DCVirtualHostNetworkMapping).filter(DCVirtualHostNetworkMapping.network_id == network_id, 
                                                                              DCVirtualHostNetworkMapping.in_use == True).all()
        else:
            link_mappings = session.query(DCVirtualHostNetworkMapping).filter(DCVirtualHostNetworkMapping.network_id == network_id).all()
        return link_mappings
        
    def update_node_template(self, template_name, total_ports, template_info, template_id=None, session=None):
        if not session:
            session = self.get_session()
        with session.begin(subtransactions=True):
            existing_template=None
            if template_id:
                existing_template = session.query(NodeTemplate).filter(
                    NodeTemplate.id == template_id).first()
                template = session.query(NodeTemplate).filter(
                    NodeTemplate.template_name == template_name).first()
                if template and template.id != template_id:
                    raise Exception(f"template_name: {template_name} already exists")
            else:
                existing_template = session.query(NodeTemplate).filter(
                    NodeTemplate.template_name == template_name).first()
                if existing_template:
                    raise Exception(f"template_name: {template_name} already exists")

            if existing_template:
                existing_template.template_name = template_name
                existing_template.total_ports = total_ports
                existing_template.template_info = template_info
                template = existing_template
            else:
                template = NodeTemplate(template_name=template_name, total_ports=total_ports,template_info = template_info)
                session.add(template)
        return template
    
    def get_all_node_template(self):
        session = self.get_session()
        templates = session.query(NodeTemplate, 
                                  case([(NodeGroup.id.isnot(None), True)], else_=False).label('is_referenced')  # 如果有关联的 NodeGroup 则标记为 True# 否则标记为 False
                                 ).outerjoin(NodeGroup, NodeTemplate.id == NodeGroup.node_template_id).group_by(NodeTemplate.id).all()
        return templates
    
    def get_node_template_by_id(self, template_id):
        session = self.get_session()
        template = session.query(NodeTemplate).filter(NodeTemplate.id == template_id).first()
        return template
    
    def del_template_by_id(self, template_id):
        session = self.get_session()
        session.query(NodeTemplate).filter(NodeTemplate.id == template_id).delete()
        
        
    def update_node_group(self, az_id=None, fabric_id=None, node_template_id=None, nodegroup_name=None, description=None, status=None, group_id=None, session=None):
        if not session:
            session = self.get_session()
        
        with session.begin(subtransactions=True):
            existing_group = None
            if group_id:
                existing_group = session.query(NodeGroup).filter(
                    NodeGroup.id == group_id).first()
                if nodegroup_name is not None:
                    group = session.query(NodeGroup).filter(
                        NodeGroup.nodegroup_name == nodegroup_name).first()
                    if group and group.id != group_id:
                        raise Exception(f"nodegroup_name: {nodegroup_name} already exists")
            else:
                existing_group = session.query(NodeGroup).filter(
                    NodeGroup.nodegroup_name == nodegroup_name).first()
                if existing_group:
                    raise Exception(f"nodegroup_name: {nodegroup_name} already exists")

            if existing_group:
                if nodegroup_name is not None:
                    existing_group.nodegroup_name = nodegroup_name
                if description is not None:
                    existing_group.description = description
                if status is not None:
                    existing_group.status = status
                group = existing_group
            else:
                group = NodeGroup(
                    az_id=az_id,
                    fabric_id=fabric_id,
                    node_template_id=node_template_id,
                    nodegroup_name=nodegroup_name,
                    description=description,
                    status='Not Deployed'
                )
                session.add(group)
        return group

    def get_node_group_by_id(self, group_id):
        session = self.get_session()
        return session.query(NodeGroup).filter(NodeGroup.id == group_id).first()
    
    def get_node_group_by_az_id(self, az_id):
        session = self.get_session()
        return session.query(NodeGroup).filter(NodeGroup.az_id == az_id).all()

    def delete_node_group(self, group_id):
        session = self.get_session()
        session.query(NodeGroup).filter(NodeGroup.id == group_id).delete()
        
        
    def update_node_host(self, host_name, node_group_id, ip_addr, username, password, host_id=None, session=None):
        if not session:
            session = self.get_session()
        
        with session.begin(subtransactions=True):
            existing_host = None
            dup_host = session.query(NodeHost).filter(
                    NodeHost.ip_addr == ip_addr, NodeHost.node_group_id != node_group_id).first()
            if dup_host:
                raise Exception(f"host_ip: {ip_addr} already exists in another node group")
            
            if host_id:
                existing_host = session.query(NodeHost).filter(
                    NodeHost.id == host_id).first()

            if existing_host:
                existing_host.host_name = host_name
                existing_host.node_group_id = node_group_id
                existing_host.ip_addr = ip_addr
                existing_host.username = username
                existing_host.password = password
                host = existing_host
            else:
                host = NodeHost(
                    host_name=host_name,
                    node_group_id=node_group_id,
                    ip_addr=ip_addr,
                    username=username,
                    password=password
                )
                session.add(host)
        return host

    def get_node_hosts_by_group_id(self, group_id):
        session = self.get_session()
        return session.query(NodeHost).filter(NodeHost.node_group_id == group_id).all()

    def get_node_host_by_id(self, host_id):
        session = self.get_session()
        return session.query(NodeHost).filter(NodeHost.id == host_id).first()

    def del_node_host(self, host_id):
        session = self.get_session()
        session.query(NodeHost).filter(NodeHost.id == host_id).delete()
    
    def update_switch_portgroup(self, portgroup_name=None, node_group_id=None, vlan_domain_id=None, connect_mode=None, link_type=None, 
                                link_count=None, status=None, portgroup_id=None, session=None):
        if not session:
            session = self.get_session()
        
        with session.begin(subtransactions=True):
            existing_portgroup = None
            if portgroup_id:
                existing_portgroup = session.query(SwitchPortgroup).filter(
                    SwitchPortgroup.id == portgroup_id).first()

            if existing_portgroup:
                if portgroup_name is not None:
                    existing_portgroup.portgroup_name = portgroup_name
                if vlan_domain_id is not None:
                    existing_portgroup.vlan_domain_id = vlan_domain_id
                if connect_mode is not None:
                    existing_portgroup.connect_mode = connect_mode
                if link_type is not None:
                    existing_portgroup.link_type = link_type
                if link_type is not None:
                    existing_portgroup.link_count = link_count
                if status is not None:
                    existing_portgroup.status = status
                portgroup = existing_portgroup
            else:
                portgroup = SwitchPortgroup(
                    portgroup_name=portgroup_name, node_group_id=node_group_id, vlan_domain_id=vlan_domain_id,
                    connect_mode=connect_mode, link_type=link_type, link_count=link_count, status='Not Deployed'
                )
                session.add(portgroup)
        return portgroup

    def get_switch_portgroup_by_nodegroup(self, node_group_id):
        session = self.get_session()
        link = session.query(SwitchPortgroup).filter(SwitchPortgroup.node_group_id == node_group_id).all()
        return link
    
    def get_switch_portgroup_by_id(self, pg_id):
        session = self.get_session()
        pg = session.query(SwitchPortgroup).filter(SwitchPortgroup.id == pg_id).first()
        return pg
    
    def del_switch_portgroup_by_id(self, portgroup_id):
        session = self.get_session()
        session.query(SwitchPortgroup).filter(
            SwitchPortgroup.id == portgroup_id
        ).delete()
        
    def del_switch_portgroup_list(self, portgroup_id_list, session=None):
        if not session:
            session = self.get_session()
        session.query(SwitchPortgroup).filter(
            SwitchPortgroup.id.in_(portgroup_id_list)
        ).delete()
        
    def update_node_nic_portgroup(self, portgroup_name=None, node_host_id=None, switch_portgroup_id=None, network_id=None, status=None, nic_id=None, session=None):
        if not session:
            session = self.get_session()
        
        with session.begin(subtransactions=True):
            existing_nic = None
            if nic_id:
                existing_nic = session.query(NodeNicPortgroup).filter(
                    NodeNicPortgroup.id == nic_id).first()
            else:
                existing_nic = session.query(NodeNicPortgroup).filter(
                    NodeNicPortgroup.portgroup_name == portgroup_name, NodeNicPortgroup.node_host_id == node_host_id).first()  

            if existing_nic:
                if portgroup_name is not None:
                    existing_nic.portgroup_name = portgroup_name
                if node_host_id is not None:
                    existing_nic.node_host_id = node_host_id
                if switch_portgroup_id is not None:
                    if existing_nic.switch_portgroup_id and existing_nic.switch_portgroup_id != switch_portgroup_id:
                        raise Exception(f"nic portgroup: {existing_nic.portgroup_name} already exists in another switch portgroup")
                    existing_nic.switch_portgroup_id = switch_portgroup_id
                if network_id is not None:
                    existing_nic.network_id = network_id
                if status is not None:
                    existing_nic.status = status
                nic = existing_nic
            else:
                nic = NodeNicPortgroup(
                    portgroup_name=portgroup_name,
                    node_host_id=node_host_id,
                    status="Disconnected" 
                )
                session.add(nic)
        return nic
    
    def get_node_nic_portgroup_by_id(self, nic_pg_id):
        session = self.get_session()
        return session.query(NodeNicPortgroup).filter(NodeNicPortgroup.id == nic_pg_id).first()
    
    def update_switch_portgroup_info(self, info_id=None, portgroup_id=None, logic_device_id=None, 
                                   switch_sn=None, port_info=None, status=None, session=None):
        if not session:
            session = self.get_session()
        
        with session.begin(subtransactions=True):
            existing_info = None
            if info_id:
                existing_info = session.query(SwitchPortgroupInfo).filter(
                    SwitchPortgroupInfo.id == info_id).first()

            if existing_info:
                if portgroup_id is not None:
                    existing_info.portgroup_id = portgroup_id
                if logic_device_id is not None:
                    existing_info.logic_device_id = logic_device_id
                if switch_sn is not None:
                    existing_info.switch_sn = switch_sn
                if port_info is not None:
                    existing_info.port_info = port_info
                if status is not None:
                    existing_info.status = status
                info = existing_info
            else:
                info = SwitchPortgroupInfo(portgroup_id=portgroup_id, logic_device_id=logic_device_id,
                                           switch_sn=switch_sn, port_info=port_info, status='Not Deployed')
                session.add(info)
        return info

    def get_switch_portgroup_info_by_pg(self, portgroup_id):
        session = self.get_session()
        return session.query(SwitchPortgroupInfo).filter(SwitchPortgroupInfo.portgroup_id == portgroup_id).all()
    
    def get_switch_portgroup_info_by_id(self, pg_info_id):
        session = self.get_session()
        return session.query(SwitchPortgroupInfo).filter(SwitchPortgroupInfo.id == pg_info_id).first()

    def del_switch_portgroup_info_list(self, info_id_list, session=None):
        if not session:
            session = self.get_session()
        session.query(SwitchPortgroupInfo).filter(SwitchPortgroupInfo.id.in_(info_id_list)).delete()
        
    def update_switch_portgroup_config(self, logic_device_id=None, switch_sn=None, related_id=None, 
                                       related_type=None, config=None, status=None, session=None):
        if not session:
            session = self.get_session()
        
        with session.begin(subtransactions=True):
            if logic_device_id:
                existing_config = session.query(SwitchPortgroupConfig).filter(SwitchPortgroupConfig.logic_device_id == logic_device_id, 
                                                                              SwitchPortgroupConfig.related_id == related_id,
                                                                              SwitchPortgroupConfig.related_type == related_type).first()
            elif switch_sn:
                existing_config = session.query(SwitchPortgroupConfig).filter(SwitchPortgroupConfig.switch_sn == switch_sn, 
                                                                              SwitchPortgroupConfig.related_id == related_id,
                                                                              SwitchPortgroupConfig.related_type == related_type).first()

            if existing_config:
                if config is not None:
                    existing_config.config = config
                if status is not None:
                    existing_config.status = status
                config = existing_config
            else:
                config = SwitchPortgroupConfig(logic_device_id=logic_device_id, switch_sn=switch_sn,
                                             related_id=related_id, related_type=related_type, status='Not Deployed')
                session.add(config)
        return config
    
    def update_vlan_domain_group(self, group_name=None, description=None, fabric_id=None, vd_group_id=None, session=None):
        if not session:
            session = self.get_session()
        
        with session.begin(subtransactions=True):
            if vd_group_id:
                existing_group = session.query(VlanDomainGroup).filter(VlanDomainGroup.id == vd_group_id).first()
                group = session.query(VlanDomainGroup).filter(VlanDomainGroup.group_name == group_name).first()
                if group and group.id != vd_group_id:
                    raise Exception(f"VlanDomain group name: {group_name} already exists")
            else:
                existing_group = session.query(VlanDomainGroup).filter(VlanDomainGroup.fabric_id == fabric_id).first()
                samename_group = session.query(VlanDomainGroup).filter(VlanDomainGroup.group_name == group_name).first()
                if samename_group:
                    raise Exception(f"VlanDomain group name: {group_name} already exists")

            if existing_group:
                if group_name is not None:
                    existing_group.group_name = group_name
                if description is not None:
                    existing_group.description = description
                group = existing_group
            else:
                group = VlanDomainGroup(group_name=group_name, description=description, fabric_id=fabric_id)
                session.add(group)
        return group

    def get_vlan_domain_group_by_fabric_id(self, fabric_id):
        session = self.get_session()
        return session.query(VlanDomainGroup).filter(VlanDomainGroup.fabric_id == fabric_id).first()
    
    def get_vlan_domain_group_by_id(self, group_id):
        session = self.get_session()
        return session.query(VlanDomainGroup).filter(VlanDomainGroup.id == group_id).first()

dc_virtual_resource_db = DCVirtualResourceDB()
