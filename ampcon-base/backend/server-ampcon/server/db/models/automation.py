# -*- coding: utf-8 -*-
import time

from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    ForeignKey,
    Boolean,
    DateTime,
    Enum,
    JSON,
    Table,
    and_
)
from sqlalchemy.orm import relationship, exc

from server.db.db_common import DBCommon
from server.db.models.base import Base
from server.util.encrypt_util import aes_cipher


class Playbook(Base):
    __tablename__ = "ansible_playbook"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(64), unique=True)
    create_user = Column(String(64))
    description = Column(String(64))
    internal = Column(Boolean, default=False)
    jobs = relationship("AnsibleJob", back_populates="playbook")


class PlaybookWithTag(Base):
    __tablename__ = "ansible_playbook_with_tag"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(64), unique=True)
    create_user = Column(String(64))
    description = Column(String(64))
    internal = Column(Boolean, default=False)
    tag = Column(String(255))


class AnsibleDevice(Base):
    __tablename__ = "ansible_device"
    id = Column(Integer, primary_key=True, autoincrement=True)
    ip = Column(String(255))
    device_name = Column(String(255), unique=True)
    device_user = Column(String(255))
    device_pwd_encrypted = Column('device_pwd', String(255))

    device_port = Column(String(32))
    device_ssh_key_path = Column(String(255))

    @property
    def device_pwd(self):
        return aes_cipher.decrypt(self.device_pwd_encrypted)

    @device_pwd.setter
    def device_pwd(self, device_pwd_str):
        self.device_pwd_encrypted = aes_cipher.encrypt(device_pwd_str)


class AnsibleJob(Base):
    __tablename__ = "ansible_job"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), unique=True)
    playbook_name = Column(String(64), ForeignKey('ansible_playbook.name', ondelete='CASCADE', onupdate='CASCADE'))
    playbook = relationship("Playbook", back_populates="jobs")
    playbook_path = Column(String(256), default='playbook.yml')
    create_user = Column(String(64))
    status = Column(String(64), default="IDLE")  # IDLE, RUNNING, EXECUTED
    
    schedule_type = Column(String(64), default="DIRECT")  # DIRECT, ONCE, SCHEDULED
    schedule_param = Column(String(2048))
    # crontab_expression = Column(String(64))
    results = relationship("AnsibleJobResult", back_populates="job")


class AnsibleJobResult(Base):
    __tablename__ = 'ansible_job_result'
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_name = Column(String(256))
    switch_sn = Column(String(64))

    job_name = Column(String(128), ForeignKey('ansible_job.name', ondelete='CASCADE', onupdate='CASCADE'))
    job = relationship("AnsibleJob", back_populates="results")
    
    start_time = Column(DateTime)
    duration = Column(String(64))
    
    state = Column(Boolean, default=False)
    
    result = Column(Text)


class SwitchCommonJob(Base):
    __tablename__ = 'switch_common_job'
    id = Column(Integer, primary_key=True, autoincrement=True)
    switch_sn = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE'))
    job_name = Column(String(128))
    job_type = Column(String(64))
    start_time = Column(Integer)
    end_time = Column(Integer)
    state = Column(String(64))
    image_version = Column(String(32))
    image_revision = Column(String(32))
    switch = relationship("Switch", primaryjoin="SwitchCommonJob.switch_sn == Switch.sn", passive_deletes=True,
                          lazy='joined')
    job_log = relationship("SwitchCommonJobLog", primaryjoin="SwitchCommonJob.id == SwitchCommonJobLog.switch_common_job_id", passive_deletes=True,
                          lazy='joined')


class SwitchCommonJobLog(Base):
    __tablename__ = 'switch_common_job_log'
    id = Column(Integer, primary_key=True, autoincrement=True)
    switch_common_job_id = Column(Integer, ForeignKey('switch_common_job.id', ondelete='CASCADE'))
    log = Column(Text)

    
class RoceTask(Base):  
    __tablename__ = 'roce_task'
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_name = Column(String(256))
    device_id = Column(Integer, ForeignKey('ansible_device.id', ondelete='CASCADE'))
    type = Column(String(32), default="RoCE Script")  # RoCE Script | RoCE Custom
    status = Column(String(32), default="RUNNING") # RUNNING SUCCESS FAILED
    result = Column(Text)
    device = relationship("AnsibleDevice", passive_deletes=True)
    
    
class HostInfo(Base):  
    __tablename__ = 'host_info'
    id = Column(Integer, primary_key=True, autoincrement=True)
    device_id = Column(Integer, ForeignKey('ansible_device.id', ondelete='CASCADE'))
    hostname = Column(String(256))
    os_version = Column(String(256))
    cpu = Column(String(256))
    memory = Column(String(32))
    storage = Column(String(32))
    last_seen = Column(DateTime)
    device = relationship("AnsibleDevice", passive_deletes=True)


class AutomationDB(DBCommon):
    
    def add_ansible_result(self, task_name, switch_sn, job_name, start_time, duration, state, result):
        ansible_result = AnsibleJobResult(task_name=task_name, switch_sn=switch_sn, job_name=job_name,
                                          start_time=start_time, duration=duration, state=state, result=result)
        session = self.get_session()
        with session.begin(subtransactions=True):
            session.add(ansible_result)
        # self.insert(ansible_result)
    
    def delete_ansible_job(self, job_name):
        self.delete_collection(AnsibleJob, filters={'name': [job_name]})
    
    def update_ansible_job_status(self, job_name, job_status):
        session = self.get_session()
        job_record = session.query(AnsibleJob).filter(AnsibleJob.name == job_name).first()
        if job_record:
            if job_status == 'FINISHED':
                job_status = 'IDLE' if job_record.schedule_type == 'SCHEDULED' else 'EXECUTED'
            with session.begin(subtransactions=True):
                job_record.status = job_status

    def add_switch_common_job(self, switch_sn, job_name, job_type, start_time, end_time, state, log, image_version,
                              image_revision):
        session = self.get_session()

        if session.query(SwitchCommonJob).filter(SwitchCommonJob.job_name == job_name).first():
            raise ValueError(f"Job with name {job_name} already exists.")

        schedule_job = session.query(SwitchCommonJob).filter(and_(SwitchCommonJob.switch_sn == switch_sn, SwitchCommonJob.state == "scheduled"))
        if schedule_job.first():
            with session.begin(subtransactions=True):
                schedule_job.delete()
        session = self.get_session()
        switch_common_job = SwitchCommonJob(switch_sn=switch_sn, job_name=job_name,
                                            job_type=job_type, start_time=start_time,
                                            end_time=end_time, state=state, image_version=image_version,
                                            image_revision=image_revision)
        switch_common_job.job_log = [SwitchCommonJobLog(log=log)]
        with session.begin(subtransactions=True):
            session.add(switch_common_job)

    def delete_switch_common_job(self, job_name):
        session = self.get_session()
        try:
            switch_common_job = session.query(SwitchCommonJob).filter(
                SwitchCommonJob.job_name == job_name
            ).one()
            with session.begin(subtransactions=True):
                session.delete(switch_common_job)
        except exc.NoResultFound:
            pass

    def update_switch_common_job(self, job_name, state, log, start_time=None, end_time=None):
        session = self.get_session()
        switch_common_job = session.query(SwitchCommonJob).filter(
            SwitchCommonJob.job_name == job_name
        ).first()
        if switch_common_job:
            with session.begin(subtransactions=True):
                switch_common_job.state = state
                if log:
                    switch_common_job.job_log.append(SwitchCommonJobLog(log=log))
                if start_time is not None:
                    switch_common_job.start_time = start_time
                if end_time is not None:
                    switch_common_job.end_time = end_time
        else:
            raise ValueError(f"No job found with name {job_name}")


automation_db = AutomationDB()


class AutomationTask(Base):
    __tablename__ = 'automation_task'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(String(255), unique=True)
    task_name = Column(String(255))
    schedule_type = Column(String(255))
    sn = Column(String(255))
    group = Column(String(64))
    task_status = Column(String(32))
    task_process = Column(Integer)
    retry_times = Column(Integer)
    args = Column(String(255), default='[]')
    kwargs = Column(String(255), default='{}')


if __name__ == '__main__':
    from server import cfg
    
    version = 0.1
    
    cfg.CONF(default_config_files=['../../automation.ini'])
    session = automation_db.get_session()
    
    nodes = session.query(AnsibleJob).all()
    print(nodes)
    
    nodes = session.query(AnsibleJobResult).all()
    print(nodes)
