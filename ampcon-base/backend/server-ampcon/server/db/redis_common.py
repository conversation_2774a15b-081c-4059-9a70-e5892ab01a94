import json
import pickle
import redis
import time
import threading
import uuid

from typing import Any, Optional
from redis.exceptions import ConnectionError, TimeoutError


class RedisSessionFactory:
    _pool = None
    _redis_client = None

    @classmethod
    def _get_client(cls):
        if cls._redis_client is None:
            cls._create_client()
        return cls._redis_client

    @classmethod
    def _create_client(cls):
        try:
            cls._pool = redis.ConnectionPool(host='redis-service', port=6379, db=0)
            cls._redis_client = redis.StrictRedis(connection_pool=cls._pool, decode_responses=True)
        except ConnectionError as e:
            print(f"Error creating Redis client: {e}")
            cls._redis_client = None
            raise

    @classmethod
    def _check_connection(cls):
        try:
            cls._redis_client.ping()
        except (ConnectionError, TimeoutError) as e:
            print(f"Connection error occurred: {e}. Reconnecting...")
            cls._create_client()

    @classmethod
    def get_client(cls, retries=3, delay=1):
        attempt = 0
        while attempt < retries:
            try:
                if cls._redis_client is None:
                    return cls._get_client()
                cls._check_connection()
                return cls._get_client()
            except (ConnectionError, TimeoutError) as e:
                attempt += 1
                print(f"Attempt {attempt} failed: {e}")
                if attempt < retries:
                    time.sleep(delay)
                else:
                    print("Max retries reached. Unable to reconnect to Redis.")
                    raise e
                

class RedisQueue:
    def __init__(self, name: str, namespace: str = 'queue'):
        self.client = RedisSessionFactory.get_client()
        self.key = f"{namespace}:{name}"

    def serialize(self, data: Any) -> bytes:
        try:
            return json.dumps(data).encode('utf-8')
        except:
            return pickle.dumps(data)
            
    def deserialize(self, data: bytes) -> Any:
        if not data:
            return None
            
        if isinstance(data, bytes):
            try:
                data = data.decode('utf-8')
            except:
                return data
                
        try:
            return json.loads(data)
        except:
            try:
                return pickle.loads(data)
            except:
                return data

    def qsize(self) -> int:
        return self.client.llen(self.key)

    def is_empty(self) -> bool:
        return self.qsize() == 0

    def put(self, data: Any, ttl: Optional[int] = None) -> None:
        pipe = self.client.pipeline()
        pipe.rpush(self.key, self.serialize(data))
        if ttl:
            pipe.expire(self.key, ttl)
        pipe.execute()

    def get(self, block: bool = True, timeout: Optional[int] = None) -> Any:
        if block:
            item = self.client.blpop(self.key, timeout=timeout)
            if item:
                return self.deserialize(item[1])
        else:
            item = self.client.lpop(self.key)
            if item:
                return self.deserialize(item)
        return None

    def get_without_pop(self) -> Any:
        data = self.client.lindex(self.key, 0)
        return self.deserialize(data) if data else None

    def put_many(self, items: list) -> None:
        if not items:
            return
        pipe = self.client.pipeline()
        for item in items:
            pipe.rpush(self.key, self.serialize(item))
        pipe.execute()

    def get_many(self, num: int, block: bool = True, timeout: int = 1) -> list:
        """
        获取多个队列项，支持阻塞模式
        
        Args:
            num: 要获取的项目数量
            block: 是否阻塞等待
            timeout: 阻塞超时时间(秒)
            
        Returns:
            list: 反序列化后的项目列表
        """
        # 首先检查队列长度
        queue_size = self.qsize()
        if queue_size == 0:
            if not block:
                return []
            # 阻塞等待第一个元素
            first_item = self.get(block=True, timeout=timeout)
            if not first_item:
                return []
            results = [first_item]
            if num > 1:
                # 获取剩余元素
                pipe = self.client.pipeline()
                pipe.lrange(self.key, 0, num - 2)  # -2 因为已经获取了一个
                pipe.ltrim(self.key, num - 1, -1)
                remaining, _ = pipe.execute()
                results.extend([self.deserialize(item) for item in remaining if item])
            return results
            
        # 有数据时的批量获取
        actual_num = min(num, queue_size)
        pipe = self.client.pipeline()
        pipe.lrange(self.key, 0, actual_num - 1)
        pipe.ltrim(self.key, actual_num, -1)
        results, _ = pipe.execute()
        return [self.deserialize(item) for item in results if item]


class LockAcquisitionError(Exception):
    """获取锁失败时抛出的异常"""
    pass


class RedisDistributedLockBase:
    def __init__(self, lock_key, ttl_ms=3000):
        self.lock_key = lock_key
        self.ttl_ms = ttl_ms
        self.lock_value = str(uuid.uuid4())  # 唯一标识符
        self.redis_client = RedisSessionFactory.get_client()
        self._lock_acquired = False

    def try_acquire(self):
        """
        尝试获取锁（非阻塞方式）
        成功返回True，失败返回False
        """
        try:
            acquired = self.redis_client.set(
                self.lock_key,
                self.lock_value,
                px=self.ttl_ms,
                nx=True
            )
            if acquired:
                self._lock_acquired = True
                print(f"get lock: {self.lock_key} {self.lock_value}")
                return True
            return False
        except Exception as e:
            print(f"Error acquiring lock: {e}")
            return False

    def acquire_or_raise(self):
        """
        尝试获取锁（非阻塞方式）
        成功返回True，失败抛出LockAcquisitionError异常
        """
        if not self.try_acquire():
            raise LockAcquisitionError(f"Failed to acquire lock for key: {self.lock_key}")
        return True

    def release(self):
        """释放锁（使用Lua脚本保证原子性）"""
        if not self._lock_acquired:
            raise RuntimeError("Cannot release a lock that wasn't acquired")
        
        # Lua脚本：检查值匹配后再删除
        lua_script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
        """
        try:
            self.redis_client.eval(lua_script, 1, self.lock_key, self.lock_value)
            self._lock_acquired = False
            print(f"release lock: {self.lock_key} {self.lock_value}")
        except Exception as e:
            print(f"Error releasing lock: {e}")
            # 即使释放失败，也标记为已释放
            self._lock_acquired = False
            raise

    def __enter__(self):
        """上下文管理器入口 - 尝试获取锁，失败则抛出异常"""
        self.acquire_or_raise()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口 - 释放锁"""
        self.release()

class AutoRenewalRedisLock(RedisDistributedLockBase):
    def __init__(self, lock_key, ttl_ms=3000, renewal_interval_s=None):
        super().__init__(lock_key, ttl_ms)
        # 续期间隔默认为TTL的1/3
        self.renewal_interval = renewal_interval_s or (ttl_ms / 3000)
        self._renewal_thread = None
        self._stop_event = threading.Event()

    def _renew_lock(self):
        """后台线程：定期续期锁"""
        lua_script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("pexpire", KEYS[1], ARGV[2])
        else
            return 0
        end
        """
        while not self._stop_event.is_set():
            try:
                # 续期锁（仅当值匹配时）
                self.redis_client.eval(
                    lua_script,
                    1,
                    self.lock_key,
                    self.lock_value,
                    self.ttl_ms
                )
            except Exception as e:
                print(f"Lock renewal failed: {e}")
                # 续期失败可能是连接问题，尝试重连
                try:
                    self.redis_client = RedisSessionFactory.get_client()
                except:
                    pass
            time.sleep(self.renewal_interval)

    def try_acquire(self):
        """尝试获取锁并启动续期线程"""
        acquired = super().try_acquire()
        if acquired:
            self._start_renewal_thread()
        return acquired

    def acquire_or_raise(self):
        """尝试获取锁（失败则抛出异常）并启动续期线程"""
        super().acquire_or_raise()
        self._start_renewal_thread()
        return True

    def _start_renewal_thread(self):
        """启动续期线程"""
        if not self._renewal_thread or not self._renewal_thread.is_alive():
            self._stop_event.clear()
            self._renewal_thread = threading.Thread(
                target=self._renew_lock,
                daemon=True
            )
            self._renewal_thread.start()

    def release(self):
        """释放锁并停止续期线程"""
        if not self._lock_acquired:
            return
            
        # 停止续期线程
        self._stop_event.set()
        if self._renewal_thread and self._renewal_thread.is_alive():
            self._renewal_thread.join(timeout=1.0)
        
        # 释放锁
        super().release()
        self._renewal_thread = None
