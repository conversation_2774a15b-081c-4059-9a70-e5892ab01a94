import logging

from confluent_kafka import Consumer, KafkaException
from server import cfg
from server.api.wireless_monitor_client_api import handler_create_monitor_client
from server.db.pg_engine import get_pg_session

LOG = logging.getLogger(__name__)

# Kafka 配置
conf = {
    'bootstrap.servers': cfg.CONF.kafka.bootstrap_servers,  # Kafka 服务器地址
    'group.id': 'wireless-client-consumer',  # 消费者组
    'auto.offset.reset': 'latest',  # latest从当前最新消息开始消费，earliest 从最早的消息开始消费
    'enable.auto.commit': cfg.CONF.kafka.enable_auto_commit  # 是否自动提交偏移量
}


def _process_message(sn, data, session=None):
    """
    处理单个Kafka消息
    """
    try:
        # 处理消息逻辑
        if "client.leave" in data or "client.join" in data:
            LOG.info(f"client join or leave event:{handler_create_monitor_client(1, sn, data, session)}")
        elif "associations" in data:
            LOG.info(f"cycle sync event:{handler_create_monitor_client(2, sn, data, session)}")
    except Exception as e:
        LOG.error(f"Error processing message for SN {sn}: {e}")


def monitor_client_consume_messages():
    """
    Kafka消费者主循环
    """
    consumer = None
    try:
        # 创建 Consumer
        consumer = Consumer(conf)
        # 订阅 Topic
        consumer.subscribe(['state', 'device_event_queue'])
        LOG.info("Starting wireless client consumer...")
        session = get_pg_session()
        while True:
            try:
                msg = consumer.poll(1.0)  # 拉取数据，1秒超时
                if msg is None:
                    continue

                if msg.error():
                    if msg.error().code() == KafkaException._PARTITION_EOF:
                        LOG.debug(f"Reached end of partition: {msg.error()}")
                        continue
                    else:
                        LOG.error(f"Consumer error: {msg.error()}")
                        break

                # 解析消息
                sn = msg.key().decode('utf-8') if msg.key() else ''
                data = msg.value().decode('utf-8')
                LOG.debug(f"Received message: {data} sn:{sn}")

                # 处理消息
                _process_message(sn, data, session)

                # 手动提交偏移量（如果启用）
                if not cfg.CONF.kafka.enable_auto_commit:
                    consumer.commit(msg)

            except Exception as e:
                LOG.error(f"Error in consumer loop: {e}")
    except KeyboardInterrupt:
        LOG.warning("Consumer interrupted by user")
    except Exception as e:
        LOG.error(f"Unexpected error in consumer: {e}")
    finally:
        if consumer:
            consumer.close()
        LOG.info("Wireless client consumer stopped")
