import logging

from confluent_kafka import Producer
from server import cfg

LOG = logging.getLogger(__name__)
# Kafka 配置
conf = {
    'bootstrap.servers': cfg.CONF.kafka.bootstrap_servers,  # Kafka 服务器地址
    'enable.idempotence': True,  # 启用幂等性确保消息不重复
}

# 创建 Producer
producer = Producer(conf)


# 发送消息
def delivery_report(err, msg):
    if err is not None:
        LOG.error(f'Message delivery failed: {err}')
    else:
        LOG.debug(f'Message delivered to {msg.topic()} [{msg.partition()}]')


def send_message(topic, key, value):
    producer.produce(
        topic=topic,
        key=key,
        value=value,
        callback=delivery_report
    )
    # 等待所有消息发送完成
    producer.flush()
