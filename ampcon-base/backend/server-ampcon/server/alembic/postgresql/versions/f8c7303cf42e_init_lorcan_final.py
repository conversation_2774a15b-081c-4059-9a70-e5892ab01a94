"""init lorcan final

Revision ID: f8c7303cf42e
Revises: smb_2025_q3
Create Date: 2025-09-30 06:48:25.047567

"""

# revision identifiers, used by Alembic.
revision = 'f8c7303cf42e'
down_revision = 'smb_2025_q3'
branch_labels = None
depends_on = None

from alembic import op
import sqlalchemy as sa


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sw',
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.Column('modified_time', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('email', sa.String(length=120), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('username')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('sw')
    # ### end Alembic commands ###
