# revision identifiers, used by Alembic.
revision = 'v6'
down_revision = 'v5'
branch_labels = None
depends_on = None

from datetime import datetime
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql


def upgrade():
    op.add_column('switch_parking', sa.Column('hardware_id', sa.String(length=64), nullable=True))


def downgrade():
    op.drop_column('switch_parking', 'hardware_id')
