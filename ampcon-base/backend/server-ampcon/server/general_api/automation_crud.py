#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: Pica8AmpCon
@file: automation_crud.py
@function:
@time: 2022/1/6 18:07
"""
import os
from datetime import datetime

from celery_app.automation_task import AmpConBaseTask, beat_task
from server.db.models.automation import AnsibleJob
import shutil
import logging
from server import constants
from server.ansible_lib.ansible_utils import ansible_job_start, add_job_record
from server.util import utils, osutil
from server.auth_jwt import Auth_Handles
from server import random_key
from .__init__ import traceback, request, jsonify, general_model, automation, automation_db, inven_db, inventory

DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
Playbook = automation.Playbook
PlaybookWithTag = automation.PlaybookWithTag
Switch = inventory.Switch
LOG = logging.getLogger(__name__)
auth_handles = Auth_Handles(random_key)


def playbook_to_dict(model):
    """
    :param model:
    name = Column(String(64), unique=True)
    create_user = Column(String(64))
    description = Column(String(64))
    jobs = relationship("AnsibleJob", back_populates= "playbook")
    """
    return dict(
        name=model.name,
        create_user=model.create_user,
        description=model.description,
        # jobs=model.jobs
    )


def playbook_to_dict_with_tag(model):
    """
    :param model:
    name = Column(String(64), unique=True)
    create_user = Column(String(64))
    description = Column(String(64))
    jobs = relationship("AnsibleJob", back_populates= "playbook")
    tag = Column(TEXT)
    """
    return dict(
        name=model.name,
        create_user=model.create_user,
        description=model.description,
        tag=model.tag
        # jobs=model.jobs
    )


def job_to_dict(model):
    """
    :param model:
    name = Column(String(64), unique=True)
    playbook_name = Column(String(64), ForeignKey('ansible_playbook.name', ondelete='CASCADE', onupdate='CASCADE'))
    playbook = relationship("Playbook", back_populates= "jobs")
    playbook_path = Column(String(256), default='playbook.yml')
    create_user = Column(String(64))
    status = Column(String(64), default="IDLE") # IDLE, RUNNING, EXECUTED
    
    schedule_type = Column(String(64), default="DIRECT") # DIRECT, ONCE, SCHEDULED
    schedule_param = Column(String(2048))
    # crontab_expression = Column(String(64))
    results = relationship("AnsibleJobResult", back_populates= "job")
    """
    return dict(
        name=model.name,
        playbook_name=model.playbook_name,
        playbook_path=model.playbook_path,
        create_user=model.create_user,
        status=model.status,
        schedule_type=model.schedule_type,
        schedule_param=model.schedule_param,
        # jobs=model.jobs
        
        results=list(map(result_to_dict, model.results))
    )


def result_to_dict(model):
    """
    :param model:
    task_name = Column(String(256))
    switch_sn = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'))
    start_time = Column(DateTime)
    duration = Column(String(64))
    state = Column(Boolean, default=False)
    result = Column(Text)
    """
    return dict(
        task_name=model.task_name,
        switch_sn=model.switch_sn,
        start_time=model.start_time.strftime('%Y-%m-%d %H:%M:%S'),
        duration=model.duration,
        state=model.state,
        result=model.result
    )


@general_model.route('/ansible/playbooks', methods=['GET'])
def playbooks():
    tag = request.args.get('tag')
    if not tag:
        playbook_info = automation_db.get_collection(PlaybookWithTag)
    else:
        playbook_info = []
        for playbook in automation_db.get_session().query(PlaybookWithTag).all():
            if playbook.tag and tag in playbook.tag.split(','):
                playbook_info.append(playbook)
    return jsonify(list(map(playbook_to_dict_with_tag, playbook_info)))


@general_model.route('/ansible/playbooks/update', methods=['POST'])
def update_playbooks():
    data = request.form
    playbook_name = data['playbook_name']
    description = data['playbook_description']
    
    token = request.headers.get('Authorization')
    create_user = auth_handles.decode_name(token.replace('Bearer ', ''))
    
    db_session = automation_db.get_session()
    playbook = db_session.query(Playbook).filter(Playbook.name == playbook_name).first()
    
    import_file = request.files['import_playbook_file']
    
    if import_file:
        dst_tmp_file = 'tmp/playbook/{0}'.format(playbook_name)
        osutil.ensure_path('tmp/playbook')
        import_file.save(dst_tmp_file)
        unzip_path = os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name)
        try:
            shutil.rmtree(unzip_path)
        except ValueError as v:
            LOG.info('Path not found: {0}'.format(unzip_path))
        try:
            utils.unzipFile(zip_file_path=dst_tmp_file, unzip_path=unzip_path)
        except Exception as e:
            msg = {'status': 500, 'msg': 'Can not unzip the upload playbook zip file.'}
            return jsonify(msg)
    
    if not playbook:
        try:
            new_playbook = Playbook()
            new_playbook.name = playbook_name
            new_playbook.description = description
            new_playbook.create_user = create_user
            new_playbook.playbook_path = os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name, 'playbook.yml')
            automation_db.insert(new_playbook, db_session)
        except Exception as e:
            LOG.error('Import playbook {0} failed when insert database: {1}'.format(playbook_name, e))
            shutil.rmtree(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name))
            msg = {'status': 500, 'msg': 'Can not insert the record into database.'}
            return jsonify(msg)
    else:
        with db_session.begin(subtransactions=True):
            playbook.description = description
            playbook.create_user = create_user
    
    msg = {'msg': 'Success to update playbook "{0}"'.format(playbook_name), 'status': '200'}
    return jsonify(msg)


@general_model.route('/ansible/playbooks/delete', methods=['POST'])
def delete_playbooks():
    data = request.get_json()
    playbook_name = data.get("playbook_name", "")
    db_session = automation_db.get_session()
    playbook = db_session.query(Playbook).filter(Playbook.name == playbook_name).first()
    if not playbook:
        msg = {'status': 500, 'msg': 'Name {0} does not exist in database.'.format(playbook_name)}
        return jsonify(msg)
    
    # delete related jobs in apsceduler
    running_jobs = AmpConBaseTask.get_running_jobs()
    if running_jobs:
        for job in running_jobs:
            if job.task_name.startswith("ansible-"):
                AmpConBaseTask.kill_process_by_task_name(job.task_name)
    
    # delete playbook folder
    filePath = os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name)
    try:
        if os.path.isdir(filePath):
            shutil.rmtree(filePath)
    except Exception as e:
        msg = {'status': 500, 'msg': 'Failed to delete playbook folder.'}
        return jsonify(msg)
    
    # delete playbook record
    try:
        automation_db.delete_collection(Playbook, {'name': [playbook_name]}, db_session)
    except Exception as e:
        msg = {'status': 500, 'msg': 'Fail to delete playbook record in database.'}
        return jsonify(msg)
    
    msg = {'status': '200', 'msg': 'Success to delete playbook.'}
    return jsonify(msg)


@general_model.route('/ansible/playbooks/run', methods=['POST'])
def run_playbooks():
    '''
        {
            'playbook_name': 'xxx',
            'playbook_dir': 'xxx/yyy',
            'switches': ['sn_xxxx', 'sn_yyyy'],
            'switch_checkall': True/False
            'group_list': ['group_1', 'group_2'],
            'vars': {},
            'scheduled': {
                'type': "DIRECT/ONCE/SCHEDULED",
                'params': {}
            }
        }
        '''
    data = request.get_json()
    playbook_name = data['playbook_name']
    playbook_dir = os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, data['playbook_dir'])
    _vars = data['vars']
    switches = data['switches']
    switch_checkall = data['switch_checkall']
    if switch_checkall:
        filter_switches = inven_db.get_collection(Switch, filters={
            'status': [constants.SwitchStatus.PROVISIONING_SUCCESS, constants.SwitchStatus.IMPORTED]})
        switches = [sw.sn for sw in filter_switches]
    
    groups = data['group_list']
    schedule_info = data['scheduled']
    
    token = request.headers.get('Authorization')
    create_user = auth_handles.decode_name(token.replace('Bearer ', ''))
    
    job_name = '{0}:::{1}'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'), playbook_name)
    
    def error_hanlder(e):
        LOG.error(":::Callback, There is error with %s", e)
    
    if schedule_info['type'] == 'DIRECT':
        add_job_record(job_name=job_name, playbook_name=playbook_name, playbook_path=playbook_dir,
                       schedule_type='DIRECT', schedule_params={}, create_user=create_user)
        ansible_job_start.delay(playbook_name, playbook_dir, job_name, 'DIRECT', {}, switches, groups, _vars,
                                celery_task_name=job_name)
    elif schedule_info['type'] == 'ONCE':
        start_time = schedule_info['params']['start_time']
        start_time = datetime.strptime(start_time, DATE_FORMAT)
        end_time = schedule_info['params']['end_time']
        end_time = datetime.strptime(end_time, DATE_FORMAT)
        add_job_record(job_name=job_name, playbook_name=playbook_name, playbook_path=playbook_dir, schedule_type='ONCE',
                       schedule_params=schedule_info['params'], create_user=create_user)
        
        beat_task.add_job("ansible-" + job_name, "ansible_job_start", args=(
            playbook_name, playbook_dir, job_name, 'ONCE', schedule_info['params'], switches, groups, _vars), once=True,
                          start_time=start_time, expires=end_time, job_desc="automation ansible playbook once",
                          kwargs={'celery_type': 'CRONTAB'})
    
    elif schedule_info['type'] == 'SCHEDULED':
        crontab_expression = schedule_info['params']['crontab_expression']
        add_job_record(job_name="ansible-" + job_name, playbook_name=playbook_name, playbook_path=playbook_dir,
                       schedule_type='SCHEDULED', schedule_params=schedule_info['params'], create_user=create_user)
        beat_task.add_job("ansible-" + job_name, "ansible_job_start", job_type="crontab",
                          job_schedule=crontab_expression,
                          args=(playbook_name, playbook_dir, job_name, 'SCHEDULED', {}, switches, groups, _vars),
                          start_time=datetime.now(), job_desc="automation ansible playbook scheduled",
                          kwargs={"celery_type": "CRONTAB"})
    
    msg = {'msg': 'The playbook task execute success in background', 'job_name': job_name, 'status': '200'}
    return jsonify(msg)


@general_model.route('/ansible/jobs', methods=['GET'])
def get_jobs():
    job_info = automation_db.get_collection(AnsibleJob)
    return jsonify(list(map(job_to_dict, job_info)))


@general_model.route('/ansible/jobs/delete', methods=['POST'])
def del_jobs():
    data = request.get_json()
    job_name = data.get("job_name", "")
    # delete related jobs in apsceduler
    db_session = automation_db.get_session()
    job = db_session.query(AnsibleJob).filter(AnsibleJob.name == job_name).first()
    if not job:
        msg = {'status': 500, 'msg': 'Can not find job {0}.'.format(job_name)}
        return jsonify(msg)
    AmpConBaseTask.kill_process_by_task_name(job_name)
    beat_task.remove_job(job_name)
    automation_db.delete_ansible_job(job_name)
    msg = {'status': '200', 'msg': 'Success to terminate and remove job {0}.'.format(job_name)}
    return jsonify(msg)
