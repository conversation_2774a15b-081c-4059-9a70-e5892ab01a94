#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: Pica8AmpCon
@file: switch_lifecycle_crud.py
@function:
@time: 2022/1/6 16:25
"""
import json
import logging
import datetime
import os
import re
import zlib
import time
from future.backports.datetime import timed<PERSON><PERSON>, date

from server import constants, cfg
from server.db.models.sdn_access import SdnAccessSwitch, sdn_access_db
from server.db.models.vtep import vtep_db, VtepControlSwitch
from server.service import upgrade_switch as upgrade_service
from server.db.models.inventory import TaskStatus, SystemConfig, SwitchConfigBackup, SwitchConfigSnapshot, License, \
    SwitchParking, AssociationGroup, SwitchLog, SwitchAutoConfig, Switch, SwitchSystemInfo
from server.util import osutil, http_client, utils
from server.service.upgrade_license import batch_upgrade_license
from server.ansible_lib.pica_lic import pica8_license
from server.vpn.vpn_utils import create_vpn_client
from server.util import ssh_util as conn_client
from server.celery_app.automation_task import beat_task, AmpConBaseTask, AutomationTask
from server.api.new_rma_api import enable_switch_vpn, enable_switch_without_vpn

from .__init__ import traceback, request, jsonify, general_model, inventory, inven_db, monitor

LOG = logging.getLogger(__name__)
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
GroupTask = inventory.GroupTask


def switch_to_dict(model):
    """
    sn = Column(String(64), index=True, unique=True)
    address = Column(String(64))
    status = Column(Enum('Init', 'Registered Not-staged', 'Configured', 'Staged', 'Registered', 'Provisioning Success',
                         'Provisioning Failed', 'Imported',
                         'DECOM', 'DECOM-Init', 'DECOM-Pending', 'DECOM-Manual', 'RMA'),
                    default='Configured')
    version = Column(String(32))
    revision = Column(String(32))
    hwid = Column(String(32))
    tmp_ip = Column(String(32))
    mgt_ip = Column(String(32))
    link_ip_addr = Column(String(32), default='')
    topology = Column(Enum('1', '2', '3'), default='1')
    step = Column(SmallInteger, default=0)
    enable = Column(Boolean, default=False)
    current_user = Column(String(32))
    current_password = Column(String(32))
    host_name = Column(String(64))
    domain = Column(String(64))
    remark = Column(Text(1024))
    # 0:deploy 1:import 2:rma
    import_type = Column(SmallInteger, default=0)
    # 0: upgrading 1: upgraded 2: upgrade failed 3: no upgrade
    upgrade_status = Column(SmallInteger, default=3)
    # 2:unknown 1:un-reachable 0:reachable  3 unmanageable
    reachable_status = Column(SmallInteger, default=0)
    #define a config after switch deployed, e.g. disable/enable VPN, collect config peridic
    post_deployed_config = Column(Text(1024), default='{}')
    platform_model = Column(String(32), ForeignKey('switch_systeminfo.model', ondelete='CASCADE'))
    config_parameters = Column(Text(65535))

    license = relationship("License", lazy='select', cascade='all, delete, delete-orphan',
                           passive_deletes=True, backref='switches')

    configs = relationship('SwitchAutoConfig', secondary='switch_switch_autoconfig', backref='switches',
                           lazy='select', order_by='SwitchAutoConfig.id', passive_deletes=True)

    switch_model = relationship('SwitchSystemInfo', lazy='select')
    gis = relationship("SwitchGis", lazy='select', cascade='all, delete, delete-orphan',
                       passive_deletes=True)
    switchStatus = relationship('SwitchStatus', lazy='select', cascade='all, delete, delete-orphan',
                                passive_deletes=True)
    ansibleJobs = relationship('AnsibleJobResult', lazy='select', cascade='all, delete, delete-orphan',
                           passive_deletes=True, backref='switch_sn_mapping')
    """
    return dict(
        sn=model.sn,
        address=model.address,
        status=model.status,
        version=model.version,
        hwid=model.hwid,
        tmp_ip=model.tmp_ip,
        mgt_ip=model.mgt_ip,
        link_ip_addr=model.link_ip_addr,
        topology=model.topology,
        step=model.step,
        current_user=model.current_user,
        current_password=model.current_password,
        host_name=model.host_name,
        domain=model.domain,
        remark=model.remark,
        model=model.switch_model.model if model.switch_model else ""
    )


def switch_park_to_dict(model):
    """
    sn = Column(String(64), index=True, unique=True)
    address = Column(String(64))
    model = Column(String(64))
    ip = Column(String(64))
    investigate = Column(Boolean, default=False)
    register_count = Column(SmallInteger, default=0)
    last_register = Column(DateTime, default=func.now())
    history_time = Column(Text(2048))
    remark = Column(Text(1024))
    """
    return dict(
        sn=model.sn,
        address=model.address,
        model=model.model,
        ip=model.ip,
        investigate=model.investigate,
        register_count=model.register_count,
        last_register=model.last_register,
        history_time=model.history_time,
        remark=model.remark,
    )


def switch_log_to_dict(model):
    """
    id = Column(Integer, autoincrement=True, primary_key=True)
    sn = Column(String(64))
    type = Column(Enum('msg', 'warn', 'error'), nullable=False, default='msg')
    msg = Column(String(255), nullable=False)
    count = Column(Integer, default=1)
    status = Column(Enum('unread', 'read', 'ignore', 'handled'), default='unread')
    history_time = Column(Text(2048))
    """
    return dict(
        sn=model.sn,
        type=model.type,
        msg=model.msg,
        count=model.count,
        status=model.status,
        history_time=model.history_time
    )


def strong_check_params(param_name, param_type, param_value):
    if not param_value:
        raise ValueError("The %s is required" % param_name)
    if not isinstance(param_value, param_type):
        raise ValueError("The %s type invalid" % param_name)


@general_model.route('/switch/all_switch_list', methods=['GET'])
def all_switch_list():
    return jsonify(list(map(switch_to_dict, inven_db.get_collection(inventory.Switch))))


@general_model.route('/switch/deployed_switch_list', methods=['GET'])
def get_deployed_switch_list():
    return jsonify(list(
        map(switch_to_dict, inven_db.get_collection(inventory.Switch, filters={'status': ['Provisioning Success']}))))


@general_model.route('/switch/parkinglot', methods=['GET'])
def get_switch_parking_lot():
    return jsonify(list(map(switch_park_to_dict, inven_db.get_collection(inventory.SwitchParking))))


@general_model.route('/switch/switch_list/<switch_sn>', methods=['GET'])
def get_switch_list_by_sn(switch_sn):
    """
    :param switch_sn: switch
    :return: [
                {
                    "address": null,
                    "current_password": null,
                    "current_user": null,
                    "domain": null,
                    "host_name": "Xorplus",
                    "hwid": "EB25-C850-899D-7A8D",
                    "link_ip_addr": "",
                    "mgt_ip": "*********",
                    "remark": null,
                    "sn": "581254X2053006",
                    "status": "Imported",
                    "step": 0,
                    "tmp_ip": null,
                    "topology": "1",
                    "version": "4.2.0"
                }
            ]
    """
    switch_sn = switch_sn.decode("utf-8") if switch_sn else ""
    return jsonify(list(map(switch_to_dict, inven_db.get_collection(inventory.Switch, filters={"sn": [switch_sn]}))))


@general_model.route('/switch/stage', methods=['POST'])
def switch_stage():
    """
    :param switch_sn: switch
    :return: msg
    """
    msg = dict()
    try:
        res = request.get_json()
        sn = res.get('sn')
        db_switch = inven_db.get_model(inventory.Switch, filters={'sn': [sn]})
        if not db_switch:
            raise ValueError('The %s switch not exist' % sn)
        db_switch.enable = True
        db_switch.status = 'Staged'
        inven_db.merge(db_switch)
        create_vpn_client(sn)
    except ValueError as v:
        LOG.exception(v)
        msg = {'status': 500, 'msg': str(v)}
    except Exception as e:
        LOG.exception(e)
        msg = {'status': 500, 'msg': 'The %s staged failed: %s' % (sn, str(e))}
    else:
        msg = {'status': 200, 'msg': 'The %s staged' % sn}
    finally:
        return jsonify(msg)


@general_model.route('/switch/decom', methods=['POST'])
def switch_decom():
    """
    :param hardware_id: switch hardware_id
    :return: msg
    """
    msg = dict()
    try:
        res = request.get_json()
        sn = res.get('sn')
        switch = inven_db.get_model(Switch, filters={'sn': [sn]})
        if not switch:
            raise ValueError('can not find switch %s' % sn)
        
        # check mac_vlan or vtep controller
        host_switch = sdn_access_db.get_model(SdnAccessSwitch, filters={'sn': [sn]})
        if host_switch:
            raise ValueError('The switch %s is in sdn access app, please delete first' % sn)
        
        vtep_switch = vtep_db.get_model(VtepControlSwitch, filters={'sn': [sn]})
        if vtep_switch:
            raise ValueError('The switch %s is in vtep manage app, please delete first' % sn)
        
        jobs = AmpConBaseTask.get_running_jobs()
        if jobs:
            for job in jobs:
                if sn in job.task_name:
                    raise ValueError('switch is in upgrade task, can not decom')
        
        if switch.upgrade_status == constants.outswitch_status.UPGRADING:
            raise ValueError('switch is upgrading, can not decom')
        
        if not switch.mgt_ip:
            host = switch.tmp_ip
        else:
            host = switch.mgt_ip
        
        if not host:
            raise ValueError('switch tmp_ip/mgt_ip is required')
        
        session = inven_db.get_session()
        decom_num = inven_db.get_next_decom_num(sn, session=session)
        decom_ip_num = inven_db.get_next_decom_ip_num(host)
        
        if decom_num >= 100:
            raise ValueError('more than 100 decom times, please delete some records')
        if decom_ip_num >= 100:
            raise ValueError('more than 100 decom times, please delete some records')
        
        # decom the license in the portal, otherwise just quit.
        # if not pica8_license.license_decom(switch.hwid):
        #     raise ValueError('can not decom the license in license portal')
        license_key = inven_db.get_switch_local_license(sn)
        if not license_key:
            pica8_license_instance = pica8_license(sn=sn)
            step, status, _ = pica8_license_instance.license_exists(switch.hwid)
            if step == 0 and status and not pica8_license_instance.license_decom(switch.hwid):
                raise ValueError('can not decom the license in license portal')
        
        cmd = 'sudo /opt/auto-deploy/restore_pica8_config.sh'
        user, password = utils.get_switch_default_user()
        content, status = conn_client.interactive_shell_linux(cmd, host, username=user, password=password)
        if status == constants.RMA_ACTIVE:
            msg = {'info': 'DECOM success', 'status': '200'}
            switch.status = constants.SwitchStatus.DECOM
            LOG.warning('%s DECOM success' % sn)
            inven_db.add_switch_log(sn, 'DECOM success', 'warn')
        
        elif status == constants.RMA_UN_REACHABLE:
            msg = {'info': 'cannot connect to switch, please check', 'status': '500'}
            switch.status = constants.SwitchStatus.DECOM_MANUAL
            LOG.warning('%s cannot connect to switch, please check', sn)
            inven_db.add_switch_log(sn, 'cannot connect to switch, please check', 'warn')
        else:
            msg = {'info': 'DECOM failed, please manually initialize the switch', 'status': '500'}
            switch.status = constants.SwitchStatus.DECOM_MANUAL
            LOG.warning('%s DECOM-PENDING and indicate you must manually run restore_pica8_config' % sn)
            inven_db.add_switch_log(sn, 'DECOM-PENDING and indicate you must manually run restore_pica8_config', 'warn')
        
        inven_db.delete_collection(License, filters={'sn_num': [sn]})
        
        num = '_' + str(decom_num) if decom_num > 0 else ''
        ip_num = '_' + str(decom_ip_num) if decom_ip_num > 0 else ''
        new_sn = sn + '_DECOM' + num
        new_ip = switch.mgt_ip + '_DECOM' + ip_num if switch.mgt_ip else new_sn
        switch.mgt_ip = new_ip
        switch.sn = new_sn
        inven_db.merge(switch)
        
        inven_db.update_model(SwitchConfigBackup, filters={'sn': [sn], 'ip': [host]}, updates={
            SwitchConfigBackup.sn: new_sn, SwitchConfigBackup.ip: new_ip
        })
        session.query(AssociationGroup).filter(AssociationGroup.switch_sn == sn).delete()
        session.query(SwitchLog).filter(SwitchLog.switch_id == sn).update({SwitchLog.switch_id: new_sn},
                                                                          synchronize_session=False)
        session.query(SwitchAutoConfig).filter(SwitchAutoConfig.name == sn + '_site_config').update({
            SwitchAutoConfig.name: new_sn + '_site_config'
        })
    except ValueError as v:
        LOG.exception(v)
        msg = {"status": 400, "msg": "The sn:%s switch decom failed, error: %s" % (sn, str(v))}
    except Exception as e:
        LOG.exception(e)
        msg = {"status": 500, "msg": "The sn:%s switch decom failed, error: %s" % (sn, str(e))}
    finally:
        return jsonify(msg)


@general_model.route('/switch/rma', methods=['POST'])
def switch_rma():
    """
    :param: old_sn, old_ip, new_sn, staged
    :return:
    """
    param = request.get_json()
    old_sn = param.get('old_sn', '')
    old_ip = param.get('old_ip', '')
    new_sn = param.get('new_sn', '')
    staged = param.get('staged', '')
    
    new_location = param.get('location', None)
    new_host_name = param.get('host_name', None)
    new_platform = param.get('platform', None)
    new_domain = param.get('domain', None)
    new_system_config_name = param.get('system_config_name', constants.GLOBAL_CONFIG_TAG)
    new_system_config_id = inven_db.get_system_config_by_config_name(new_system_config_name)

    if not new_system_config_id:
        return jsonify({'status': '400', 'info': 'System Config %s not found' % new_system_config_name})

    if old_sn == new_sn:
        return jsonify({'status': 400, 'msg': 'New sn number is same as original'})
    
    if 'RMA' in old_sn:
        return jsonify({'status': 400, 'msg': 'Switch have already did rma, can not do again'})
    
    db_session = inven_db.get_session()
    with db_session.begin():
        
        switch_new = inven_db.get_model(Switch, filters={'sn': [new_sn]}, session=db_session)
        if switch_new:
            msg = {'status': 400, 'msg': 'The new sn is already exist'}
            return jsonify(msg)
        
        rma_backup = inven_db.get_model(SwitchConfigBackup, filters={'sn': [old_sn]}, session=db_session)
        if not rma_backup:
            msg = {'status': 400, 'msg': 'Please back up or upload config the switch '}
            return jsonify(msg)
        
        golden_snapshot = inven_db.get_model(SwitchConfigSnapshot, filters={'sn': [old_sn], 'tag': ['GOLDEN_CONFIG']},
                                             session=db_session)
        
        if old_ip != '':
            pure_ip = re.split(r'[_(]', old_ip)[0]
            if "DECOM" in old_ip or "RMA" in old_ip:
                rma_back_check = inven_db.get_model(SwitchConfigBackup, filters={'ip': [pure_ip]}, session=db_session)
                if rma_back_check:
                    msg = {'status': 400, 'msg': 'The ip is used by switch %s , please check ' % rma_back_check.sn}
                    return jsonify(msg)
        else:
            pure_ip = ''
        
        # change old switch
        old_switch = inven_db.get_model(Switch, filters={'sn': [old_sn]}, session=db_session)
        if not old_switch:
            return jsonify({'status': 400, 'msg': 'Switch can not found'})
        
        # need to delete the entry in switch
        inven_db.delete_collection(Switch, filters={'sn': [old_sn + '_RMA']}, session=db_session)
        inven_db.delete_collection(SwitchConfigBackup, filters={'sn': [old_sn + '_RMA']}, session=db_session)
        inven_db.delete_collection(License, filters={'sn_num': [old_sn]}, session=db_session)
        old_switch.sn = old_sn + '_RMA'
        if old_ip != '':
            old_switch.mgt_ip = old_ip + '(RMA)'
        old_switch.status = constants.SwitchStatus.RMA
        rma_backup.sn = rma_backup.sn + '_RMA'
        if old_ip != '':
            rma_backup.ip = old_ip + '(RMA)'
        else:
            rma_backup.ip = old_sn + '(RMA)'
        
        # create new switch
        rma_switch = Switch()
        rma_switch.sn = new_sn
        rma_switch.mgt_ip = pure_ip
        rma_switch.step = 0
        rma_switch.import_type = constants.ImportType.RMA
        rma_switch.system_config_id = new_system_config_id.id
        if staged == 'true':
            rma_switch.status = constants.SwitchStatus.STAGED
            rma_switch.enable = True
        
        else:
            rma_switch.status = constants.SwitchStatus.CONFIGURED
            rma_switch.enable = False
        rma_switch.platform_model = new_platform if new_platform else old_switch.platform_model
        rma_switch.version = old_switch.version
        rma_switch.revision = old_switch.revision.split('-')[0]
        rma_switch.topology = old_switch.topology
        rma_switch.address = new_location if new_location else old_switch.address
        rma_switch.current_user = old_switch.current_user
        rma_switch.current_password = old_switch.current_password
        rma_switch.host_name = new_host_name if new_host_name else old_switch.host_name
        rma_switch.domain = new_domain if new_domain else old_switch.domain
        inven_db.insert_or_update(rma_switch, primary_key='sn', session=db_session)
        
        # create new backup
        rma_backup_new = SwitchConfigBackup()
        rma_backup_new.sn = new_sn
        rma_backup_new.ip = pure_ip
        # use golden config if exist
        if golden_snapshot:
            rma_backup_new.config = zlib.compress(golden_snapshot.archive_config)
        else:
            rma_backup_new.config = rma_backup.config
        rma_backup_new.back_up_type = constants.RMA_BACK_MANUAL
        inven_db.insert_or_update(rma_backup_new, primary_key='sn', session=db_session)
        
        # send <NAME_EMAIL>
        title = 'automation rma configured'
        msg = 'old sn %s, new sn %s' % (old_sn, new_sn)
        utils.send_email(title, msg, [constants.RMA_EMAIL])
        
        inven_db.delete_collection(SwitchParking, filters={'sn': [new_sn]}, session=db_session)
        msg = {'status': '200', 'msg': 'RMA Success'}
        
        db_session.query(AssociationGroup).filter(AssociationGroup.switch_sn == old_sn).delete()
        db_session.query(SwitchLog).filter(SwitchLog.switch_id == old_sn).update({SwitchLog.switch_id: old_sn + '_RMA'},
                                                                                 synchronize_session=False)
        db_session.query(SwitchAutoConfig).filter(SwitchAutoConfig.name == old_sn + '_site_config').update({
            SwitchAutoConfig.name: old_sn + '_RMA_site_config'
        })
    
    if staged == 'true':
        # generate vpn key
        try:
            create_vpn_client(new_sn)
        except Exception as e:
            return jsonify({'status': 500, 'msg': 'Fail to generate vpn key %s.' % str(e)})
    return jsonify(msg)


@general_model.route('/switch/log', methods=['GET'])
def switch_log():
    return jsonify(list(map(switch_log_to_dict, inven_db.get_collection(monitor.Event))))


@general_model.route('/switch/import', methods=['POST', 'GET'])
def switch_import():
    if request.method == "GET":
        switch_import_infos = inven_db.get_collection(Switch, filters={'import_type': [constants.ImportType.IMPORT]})
        return jsonify(list(map(switch_to_dict, switch_import_infos)))
    else:
        res = request.get_json()
        ip = res.get("ip", "")
        if not re.match(r"^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$",
                        ip):
            return jsonify({'status': '400', 'msg': 'IP invalid'})

        used_system_config_name = request.args.get('system_config_name')

        if not used_system_config_name:
            return jsonify({'status': '400', 'info': 'System config name cannot be empty!'})

        used_system_config = inven_db.get_system_config_by_config_name(used_system_config_name)

        if not used_system_config:
            return jsonify({'status': '400', 'info': 'Selected system config cannot be found!'})

        license_key = None

        if not used_system_config_name:
            used_system_config_name = constants.GLOBAL_CONFIG_TAG

        user, pw = utils.get_switch_default_user(system_config_name=used_system_config_name)

        sn_str, code = conn_client.interactive_shell_cli('show system serial-number | no-more',
                                                         ip, username=user, password=pw)
        if code != constants.RMA_ACTIVE:
            return jsonify({'msg': 'Cannot connect to switch', 'status': 500})
        sn = re.findall("MotherBoard Serial Number : (.*)", sn_str)[0].strip()
        cmdline_info, cmdline_code = conn_client.interactive_shell_linux('cat /proc/cmdline | grep platform=x86v', ip,
                                                                         username=user, password=pw)
        model_info, _ = conn_client.interactive_shell_linux('cat /sys/class/swmon/ctrl/product_id', ip,
                                                                         username=user, password=pw)
        # check PICOS-V flag
        is_need_to_skip_add_license = cmdline_info and cmdline_code == constants.RMA_ACTIVE
        if is_need_to_skip_add_license:
            LOG.info("No need to generate and add license")
        else:
            license_info, code = conn_client.interactive_shell_linux('license -s', ip, username=user, password=pw)
            if code != constants.RMA_ACTIVE:
                return jsonify({'status': 500, 'msg': 'Can not exec license show'})
            
            license_ret = re.findall("Type: (.*)", license_info)
            if not license_ret:
                tmp_info = json.loads(license_info)
                speed = tmp_info['Type'].strip()
                hardware_id = tmp_info['Hardware ID'].strip()
            else:
                speed = license_ret[0].strip()
                hardware_id = re.findall("Hardware ID: (.*)", license_info)[0].strip()

            pica8_license_instance = pica8_license(system_config_name=used_system_config_name)
            try:
                _, license_key = pica8_license_instance.license_get(hardware_id)
            except Exception as e:
                LOG.exception(e)

            if not license_key or 'error' in license_key:
                license_key = inven_db.get_switch_local_license(sn)
                if not license_key:
                    # now we can create a license
                    _, license_key = pica8_license_instance.license_create(speed=speed, hwid=hardware_id, name='PicOS_' + sn)
                    if not license_key or 'error' in license_key:
                        LOG.error('error in create license for %s switch', hardware_id)
                        msg = {'status': '500', 'info': 'Error in create license for %s switch' % hardware_id}
                        return jsonify(msg)
                    LOG.info(
                        "hardware_id: %s, license generate success, key len: %d\n %s" % (
                            hardware_id, len(license_key), license_key))
                    utils.update_db_license_count()
        
        db_session = inven_db.get_session()
        switch = db_session.query(Switch).filter(Switch.sn == sn).first()
        parking_lot = db_session.query(SwitchParking).filter(SwitchParking.sn == sn).first()
        
        if switch:
            msg = {'status': 400, 'msg': 'Sn %s already exists in AmpCon' % sn}
            return jsonify(msg)
        
        if parking_lot:
            msg = {'status': 400, 'msg': 'Sn %s already exists in parking lot' % sn}
            return jsonify(msg)
        
        new_switch = Switch(sn=sn, status=constants.SwitchStatus.IMPORTED, import_type=constants.ImportType.IMPORT,
                            reachable_status=constants.REACHABLE, system_config_id=used_system_config.id)
        db_session.add(new_switch)
        db_session.flush()
        
        if cfg.CONF.vpn_enable:
            from server.vpn.vpn_utils import create_vpn_client
            try:
                create_vpn_client(sn)
            except Exception as e:
                inven_db.delete_model(Switch, 'sn', sn)
                return jsonify({'status': '500', 'info': 'Import failed %s!' % str(e)})
            enable_switch_vpn.delay(is_need_to_skip_add_license, ip, license_key, used_system_config_name, celery_sn=sn,
                                    celery_task_name=f"enable_switch_vpn_{sn}")
        else:
            enable_switch_without_vpn.delay(is_need_to_skip_add_license, ip, license_key, used_system_config_name, celery_sn=sn,
                                            celery_task_name=f"enable_switch_without_vpn_{sn}")
        return jsonify({'status': '200', 'info': 'Import success!'})


def common_switch_upgrade(flag):
    msg = None
    try:
        info = request.form
        sn = info.get('sn', '')
        strong_check_params('Switch sn', str, sn)
        db_switch = inven_db.get_model(inventory.Switch, filters={'sn': [sn]})
        if not db_switch:
            raise ValueError('The %s switch not exist' % sn)
        
        task_name = 'upgrade::%s' % sn
        # jobs = scheduler.get_jobs()
        # for job in jobs:
        #     if sn in job.id:
        #         raise ValueError('upgrade in other task [%s]' % job.id)
        session = inven_db.get_session()
        task_statuses = session.query(TaskStatus).filter(TaskStatus.name.like('%::' + sn + '%'))
        if task_statuses:
            for task_status in task_statuses:
                if task_status.status == 'Not-start' or task_status.status == 'running':
                    raise ValueError('upgrade in other task [%s]' % task_status.name)
        
        files = request.files
        script_parent_dir = constants.UPGRADE_TMP_SCRIPTS + sn
        osutil.ensure_path(script_parent_dir)
        tmp_files = {}
        have_need_file = False
        sources = []
        if files:
            for script in files.values():
                if script.filename != '' and script.filename not in tmp_files:
                    LOG.info('save script %s', script)
                    if constants.POST_XORPLUS == script.filename:
                        have_need_file = True
                    path = script_parent_dir + '/' + script.filename
                    if os.path.exists(path):
                        os.remove(path)
                    script.save(path)
                    sources.append({'filename': script.filename, 'path': path, 'dest': path})
                    tmp_files[script.filename] = path
        else:
            have_need_file = True
        
        if not have_need_file and tmp_files:
            raise ValueError('%s must be existed' % constants.POST_XORPLUS)
        
        if sources:
            # push files to sync server
            http_client.start_transfer_file("", sources)
        LOG.info('upgrade files %s', files)
        switch = inven_db.get_switch_info_by_sn(sn)
        switch_platform = switch.switch_model
        mgt_ip = switch.mgt_ip

        system_config = inven_db.get_system_config_by_sn(sn)
        user, password = system_config.switch_op_user, system_config.switch_op_password
        
        if switch.upgrade_status == constants.outswitch_status.UPGRADING:
            raise ValueError('switch is upgrading')
        
        upgrade_switch = session.query(Switch).filter(Switch.sn == sn).first()
        system_switch = session.query(SwitchSystemInfo).filter(
            SwitchSystemInfo.model == upgrade_switch.platform_model).first()
        if system_switch and upgrade_switch.revision == system_switch.up_to_date_version.split("/")[1]:
            switch.upgrade_status = constants.outswitch_status.NO_UPGRADE
            inven_db.merge(switch)
            raise ValueError('version is current, please check')
        if flag:
            # scheduler.add_job(task_name, upgrade_service.start,
            #                   args=(mgt_ip, user, password, '',
            #                         '', sn, switch_platform.model, tmp_files),
            #                   executor='processpool',
            #                   misfire_grace_time=None)
            upgrade_service.start.delay(mgt_ip, user, password, '',
                                        '', sn, switch_platform.model, tmp_files, celery_task_name=task_name,
                                        celery_sn=sn)
            switch.upgrade_status = constants.outswitch_status.UPGRADING
            inven_db.merge(switch)
        else:
            if 'start_date' in info:
                start_date = info['start_date']
                start_date = datetime.datetime.strptime(start_date, DATE_FORMAT)
            else:
                start_date = datetime.datetime.now()
            if 'end_date' in info:
                end_date = info['end_date']
                end_date = datetime.datetime.strptime(end_date, DATE_FORMAT)
            else:
                end_date = None
            # trigger = DateWithEndTrigger(run_date=start_date, end_date=end_date)
            # scheduler.add_job(task_name, upgrade_service.start,
            #                   args=(mgt_ip, user, password, '',
            #                         '', sn, switch_platform.model, tmp_files),
            #                   trigger=trigger, executor='processpool',
            #                   misfire_grace_time=None)
            beat_task.add_job(task_name, "start_upgrade_service", args=(mgt_ip, user, password, '',
                                                                        '', sn, switch_platform.model, tmp_files),
                              kwargs={"celery_task_name": task_name, "celery_sn": sn, "schedule_type": "CRONTAB"},
                              once=True,
                              start_time=start_date, expires=end_date)
    except ValueError as v:
        msg = {'status': 500, 'message': 'Switch upgrade failed, error: %s' % str(v)}
    except Exception as e:
        msg = {'status': 500, 'message': 'Switch upgrade failed, error: %s' % str(e)}
    else:
        msg = {'status': 200, 'message': 'Switch upgrade success'}
    finally:
        return jsonify(msg)


@general_model.route('/switch/upgrade', methods=['POST'])
def api_switch_upgrade():
    """
    :param:
        sn:
        files: upgrade files
    :return:
    """
    return common_switch_upgrade(True)


@general_model.route('/switch/license_audit', methods=['POST'])
def switch_license_audit():
    msg = None
    try:
        data = request.get_json()
        sn = data.get("sn", "")
        strong_check_params('Switch sn', str, sn)
        switch = inven_db.get_model(Switch, filters={'sn': [sn]})
        if not switch:
            raise ValueError('can not find switch %s' % sn)
        date_time = (date.today() + timedelta(days=30)).strftime('%Y-%m-%d')
        switch_op_user, switch_op_password = utils.get_switch_default_user(sn=sn)
        switch_task_running = AmpConBaseTask.get_running_job_by_task_name(f"{sn}_audit")
        if switch_task_running:
            LOG.info('license is in upgrade')
            raise ValueError("license is in upgrade")
        batch_upgrade_license.delay(sn, '', '', '', switch_op_user,
                                    switch_op_password, "audit", date_time,
                                    celery_sn=sn, celery_task_name=f"{sn}_audit")
    except ValueError as v:
        msg = {'status': 400, 'message': str(v)}
    except Exception as e:
        msg = {'status': 500, 'message': str(e)}
    else:
        msg = {'status': 200, 'message': 'License audit successful in the background'}
    finally:
        return jsonify(msg)


@general_model.route('/switch/license_action', methods=['POST'])
def switch_license_action():
    msg = None
    try:
        data = request.get_json()
        sn = data.get("sn", "")
        strong_check_params('Switch sn', str, sn)
        switch = inven_db.get_model(Switch, filters={'sn': [sn]})
        if not switch:
            raise ValueError('can not find switch %s' % sn)
        date_time = (date.today() + timedelta(days=30)).strftime('%Y-%m-%d')
        switch_op_user, switch_op_password = utils.get_switch_default_user(sn=sn)
        switch_task_running = AmpConBaseTask.get_running_job_by_task_name(f"{sn}_action")
        if switch_task_running:
            LOG.info('license is in upgrade')
            raise ValueError("license is in upgrade")
        batch_upgrade_license.delay(sn, '', '', '', switch_op_user,
                                    switch_op_password, "action", date_time,
                                    celery_sn=sn, celery_task_name=f"{sn}_action")
    except ValueError as v:
        msg = {'status': 400, 'message': str(v)}
    except Exception as e:
        msg = {'status': 500, 'message': str(e)}
    else:
        msg = {'status': 200, 'message': 'License action successful in the background'}
    finally:
        return jsonify(msg)


def check_group(session, group_name, action):
    session_obj = session.query(inventory.Group).filter(inventory.Group.group_name == group_name)
    if not session_obj.first():
        return {'status': 400, 'message': '%s group does not exist' % group_name}
    if action == 'upgrade' and not session_obj.first().upgrading:
        return {'status': 400, 'message': '%s group not allowed upgrade' % group_name}
    if action == 'action' and not session_obj.first().action:
        return {'status': 400, 'message': '%s group not allowed action' % group_name}
    if action == 'audit' and not session_obj.first().audit:
        return {'status': 400, 'message': '%s group not allowed audit' % group_name}
    return


@general_model.route('/switch/groups', methods=['GET'])
def switch_groups():
    db_session = inven_db.get_session()
    group_infos = db_session.query(inventory.Group).group_by(inventory.Group.group_name).all()
    group_list = list()
    for group_info in group_infos:
        group_dict = dict()
        group_dict['name'] = group_info.group_name
        group_dict['description'] = group_info.description
        group_dict['action_array'] = list()
        if group_info.audit is True:
            group_dict['action_array'].append("audit")
        if group_info.action is True:
            group_dict['action_array'].append("action")
        if group_info.upgrading is True:
            group_dict['action_array'].append("upgrading")
        if group_info.retrieve_config is True:
            group_dict['action_array'].append("retrieve_config")
        group_dict['sn'] = [i.switch_sn for i in db_session.query(inventory.AssociationGroup).filter(
            inventory.AssociationGroup.group_name == group_info.group_name).all()]
        group_list.append(group_dict)
    return jsonify(group_list)


def common_groups_add_update(flag):
    msg = None
    try:
        params = request.get_json()
        strong_check_params("Request Body", dict, params)
        action_list = params.get('action_array')
        strong_check_params("action_array", list, action_list)
        for tmp_action in action_list:
            if tmp_action not in ["audit", "action", "upgrading", "retrieve_config"]:
                raise ValueError("The action_array is invalid")
        group_name = params.get('group_name', '')
        strong_check_params("group_name", str, group_name)
        description = params.get('description')
        new_switches = params.get('switches')
        strong_check_params("switches", list, new_switches)
        if "" in new_switches:
            raise ValueError("switches contains invalid params")
        db_session = inven_db.get_session()
        if flag:
            db_session.query(inventory.Group).filter(inventory.Group.group_name == group_name).delete()
        else:
            if db_session.query(inventory.Group).filter(inventory.Group.group_name == group_name).first():
                raise ValueError("%s group already exist" % group_name)
        
        with db_session.begin():
            group = inventory.Group()
            group.group_name = group_name
            group.description = description
            for attr in action_list:
                setattr(group, attr, True)
            for switch in new_switches:
                association_group = inventory.AssociationGroup()
                association_group.switch_sn = switch
                association_group.group_name = group.group_name
                group.association_group.append(association_group)
            db_session.add(group)
    except ValueError as v:
        msg = {"status": 400, "msg": str(v)}
    except Exception as e:
        msg = {"status": 500, "msg": str(e)}
    else:
        tmp_info = "group update success" if flag else "group add success"
        msg = {"status": 200, "msg": tmp_info}
    finally:
        return jsonify(msg)


@general_model.route('/switch/groups/add', methods=['POST'])
def add_switch_groups():
    """
        {
            "action_array":["audit", "action", "upgrading", "retrieve_config"],
            "group_name": "123test",
            "description": "cc's test",
            "switches": ["123"]
        }
    :return: {"status": 200, "msg": "group add success"}
    """
    return common_groups_add_update(False)


@general_model.route('/switch/groups/update', methods=['POST'])
def update_switch_groups():
    """
        {
            "action_array":["audit", "action", "upgrading", "retrieve_config"],
            "group_name": "123test",
            "description": "cc's test",
            "switches": ["123"]
        }
        :return: {"status": 200, "msg": "group update success"}
    """
    return common_groups_add_update(True)


@general_model.route('/switch/groups/delete', methods=['POST'])
def delete_switch_groups():
    msg = None
    try:
        params = request.get_json()
        group_name = params.get("group_name", "")
        db_session = inven_db.get_session()
        # check whether it is used in apscheduler tasks
        jobs = AmpConBaseTask.get_running_jobs()
        if jobs:
            for job in jobs:
                if 'group_upgrade::{0}::'.format(group_name) in job.task_name:
                    msg = {'info': 'Group {0} is used for group upgrade tasks'.format(group_name), 'status': '500'}
                    return jsonify(msg)
                if 'group_push_image::{0}::'.format(group_name) in job.task_name:
                    msg = {'info': 'Group {0} is used for group push image tasks'.format(group_name), 'status': '500'}
                    return jsonify(msg)
        
        session_obj = db_session.query(inventory.Group).filter(inventory.Group.group_name == group_name)
        if not session_obj.first():
            raise ValueError("%s group does not exist" % group_name)
        session_obj.delete()
    except ValueError as v:
        msg = {'msg': 'Delete group failed, error: %s' % str(v), 'status': 500}
    except Exception as e:
        msg = {'msg': 'Delete group failed, error: %s' % str(e), 'status': 500}
    else:
        msg = {'msg': 'Success to delete group %s' % group_name, 'status': 200}
    finally:
        return jsonify(msg)


@general_model.route('/switch/groups/switch_upgrade', methods=['POST'])
def switch_upgrade():
    info = request.form
    name = info['name']
    session = inven_db.get_session()
    check_status = check_group(session, name, "upgrade")
    if check_status:
        return jsonify(check_status)
    
    if 'start_date' in info:
        start_date = info['start_date']
        start_date = datetime.datetime.strptime(start_date, DATE_FORMAT)
    else:
        start_date = datetime.datetime.now()
    if 'end_date' in info:
        end_date = info['end_date']
        end_date = datetime.datetime.strptime(end_date, DATE_FORMAT)
    else:
        end_date = datetime.datetime.now() + datetime.timedelta(days=2)
    
    if start_date < datetime.datetime.now():
        return jsonify({'status': 400, 'message': '- start time must be later than now'})
    
    if end_date < start_date:
        return jsonify({'status': 400, 'message': '- end time must be later than start time'})
    
    switches = inven_db.get_group_switchs_new(name)
    delta = end_date - start_date
    if delta.total_seconds() / 60 / 3 < len(switches):
        return jsonify({'status': 400,
                        'message': '- the time between end_date and start_date not enough, should be %s' %
                                   (start_date + datetime.timedelta(minutes=3 * len(switches))).strftime(DATE_FORMAT)
                        })
    
    session = inven_db.get_session()
    group_task = session.query(GroupTask).filter(GroupTask.name == name, GroupTask.status != 'finished').first()
    if group_task:
        return jsonify({'status': 400, 'message': '- group is already scheduled'})
    
    files = request.files
    script_parent_dir = constants.UPGRADE_TMP_SCRIPTS + 'group_' + name
    osutil.ensure_path(script_parent_dir)
    tmp_files = {}
    have_need_file = False
    sources = []
    if files:
        for script in files.values():
            if script.filename != '' and script.filename not in tmp_files:
                LOG.info('save script %s', script)
                if constants.POST_XORPLUS == script.filename:
                    have_need_file = True
                path = script_parent_dir + '/' + script.filename
                if os.path.exists(path):
                    os.remove(path)
                script.save(path)
                sources.append({'filename': script.filename, 'path': path, 'dest': path})
                tmp_files[script.filename] = path
    else:
        have_need_file = True
    
    if not have_need_file and tmp_files:
        return jsonify({'status': 400, 'msg': '%s must be existed' % constants.POST_XORPLUS})
    
    if sources:
        # push files to sync server
        http_client.start_transfer_file("", sources)
    
    # clear previous group tasks status
    if session.query(AutomationTask).filter(AutomationTask.task_name.like('%group_upgrade::' + name + '%'),
                                            AutomationTask.task_status == "running").first():
        return jsonify({'status': 400, 'message': '- group task already running'})
    session.query(AutomationTask).filter(AutomationTask.task_name.like('%group_upgrade::' + name + '%')).delete(
        synchronize_session=False)
    beat_task.remove_like_job('group_upgrade::' + name)
    session.query(GroupTask).filter(GroupTask.name == name, GroupTask.type == 'upgrade').delete(
        synchronize_session=False)

    msgs = {}
    for switch in switches:
        sn = switch.sn

        system_config = inven_db.get_system_config_by_sn(sn)
        user, password = system_config.switch_op_user, system_config.switch_op_password

        task_name = 'group_upgrade::%s::%s' % (name, sn)
        
        switch_platform = switch.switch_model
        mgt_ip = switch.mgt_ip
        
        beat_task.add_job(task_name, 'start_upgrade_service', args=(mgt_ip, user, password, '',
                                                                    '', sn, switch_platform.model, tmp_files,
                                                                    task_name), once=True,
                          start_time=start_date, expires=end_date,
                          kwargs={'celery_sn': sn, "schedule_type": "CRONTAB", "celery_group": name})
        msgs[sn] = 'add task ok'
        # start_date += datetime.timedelta(minutes=3)
    
    msgs['msg'] = 'success'
    msgs['status'] = 200
    inven_db.insert(GroupTask(name=name, start_date=info['start_date'], end_date=end_date, status='Not-start'))
    return jsonify(msgs)


@general_model.route('/switch/groups/license_audit', methods=['POST'])
def license_audit():
    msg = {"status": 200, "msg": "license audit successful in the background"}
    try:
        db_session = inven_db.get_session()
        data = request.get_json()
        group = data.get("group", "")
        check_status = check_group(db_session, group, "audit")
        if check_status:
            return jsonify(check_status)
        date_time = (date.today() + timedelta(days=30)).strftime('%Y-%m-%d')
        report_time = time.strftime("%Y-%m-%d %H:%M", time.localtime()) + "<audit report> "
        switch_task_running = AmpConBaseTask.get_running_job_by_task_name('switch_audit_%s' % group)
        if switch_task_running:
            raise ValueError('license is in upgrade')
        else:
            switch_op_user, switch_op_password = utils.get_switch_default_user()
            batch_upgrade_license.delay('', group, '', '', switch_op_user,
                                        switch_op_password, 'audit', date_time,
                                        report_time=report_time, celery_task_name=f"switch_audit_{group}",
                                        celery_group=group)
    except Exception as e:
        msg = {"status": 500, "msg": str(e)}
    return jsonify(msg)


@general_model.route('/switch/groups/license_action', methods=['POST'])
def license_action():
    msg = {"status": 200, "msg": "license action successful in the background"}
    try:
        db_session = inven_db.get_session()
        data = request.get_json()
        group = data.get("group", "")
        check_status = check_group(db_session, group, "action")
        if check_status:
            raise ValueError(check_status["message"])
        date_time = (date.today() + timedelta(days=30)).strftime('%Y-%m-%d')
        report_time = time.strftime("%Y-%m-%d %H:%M", time.localtime()) + "<audit report>"
        switch_task_running = AmpConBaseTask.get_running_job_by_task_name('switch_action_%s' % group)
        if switch_task_running != 0:
            raise ValueError('license is in upgrade')
        else:
            switch_op_user, switch_op_password = utils.get_switch_default_user()
            batch_upgrade_license.delay('', group, '', '', switch_op_user,
                                        switch_op_password, 'action', date_time,
                                        report_time=report_time,
                                        celery_task_name=f"switch_action_{group}",
                                        celery_group=group)
    except ValueError as v:
        msg = {"status": 400, "msg": str(v)}
    except Exception as e:
        msg = {"status": 500, "msg": str(e)}
    finally:
        return jsonify(msg)
