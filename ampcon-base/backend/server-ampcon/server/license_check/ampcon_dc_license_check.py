import logging
import base64
import rsa
import hashlib
import random
import json
import datetime
import traceback
from rsa import transform, core, transform, common
from concurrent.futures import ThreadPoolExecutor, as_completed

from typing import Dict

from server.db.models.inventory import inven_db, LicenseInfo, Switch, SwitchConfigBackup, DeployedSecuritySwitch
from celery_app.automation_task import AmpConBaseTask
from server.license_check.license_check import License<PERSON>hecker
from sqlalchemy import func

Log = logging.getLogger(__name__)

PICA_PUBLIC_KEY_PATH = "./pica-public.key"
APMCON_PUBLIC_KEY_PATH = "./ampcon-public.key"
AMPCON_PRIVATE_KEY_PATH = "./ampcon-private.key"

class AmpConDCLicenseChecker(LicenseChecker):
    """
    实现类 AmpConDCLicenseChecker, 实现AmpCon DC license相关接口
    """
    def __init__(self):
        self.pica_public_key = self._load_public_key(PICA_PUBLIC_KEY_PATH)
        self.ampcon_private_key = self._load_private_key(AMPCON_PRIVATE_KEY_PATH)
        self.apmcon_public_key = self._load_public_key(APMCON_PUBLIC_KEY_PATH)
        
        self.rsa_len = 2048
        self.rsa_encrypt_len = int(self.rsa_len / 8)
        self.md5_digest_length = 16
        self.sha_digest_length = 20
        self.header_len = self.rsa_encrypt_len + self.md5_digest_length + self.sha_digest_length
        
    def encrypt(self, data):
        """
        基于pica public key 对license 加密
        """
        return self._encrypt_with_public_key(data, self.pica_public_key)

    def decrypt(self, data):
        """
        基于pica public key 对license 解密
        """
        # base64解码
        base64_data = base64.b64decode(data)
        encrypt_rc4_key = base64_data[:self.rsa_encrypt_len]

        # ras解密获取rc4key
        rc4_key = self._rsa_publickey_decrypt(encrypt_rc4_key, self.pica_public_key)
        # print(rc4_key, len(rc4_key))

        # rc4解密数据，lic数据在头部164位之后
        encrypy_data = base64_data[self.header_len:]
        data = self._rc4(encrypy_data, rc4_key)
        
        # 校验MD5
        md5 = self._calculate_md5(data)
        if md5 != base64_data[self.rsa_encrypt_len:self.rsa_encrypt_len+self.md5_digest_length].hex():
            raise ValueError("LICERR BAD MD5")
        return data
    
    def install_license(self, license_data):
        """
            1.license解密
            2.检查license Type是否是trial license, 如果是：
                1). 检查license是否已经失效, 失效则安装失败
                2). 检查当前是否有超过1条已安装license, 超过返回安装失败, 如果没有license, 则直接安装
                3). 如果只有一条license, 检查是否是正式license, 检查license id是否相同, 如果不满足则返回失败
                4). 满足上述, 对比已纳管的hwid, 如果存在diff, 安装失败, 返回需要清理的hwid
                5). 满足上述, 覆盖安装
            3.如果是正式license
                1). 解码所有license, 用于后续比对
                2). 检查已有license和新增license的hwid, 对比已纳管的hwid, 如果存在需要清理的hwid, 安装失败返回hwid
                3). 检查当前license的id是否唯一, 如果唯一直接安装, 不唯一覆盖安装
        Returns:
            Dict[str, str]: 安装检查结果.
                {
                    "status": "int: 安装成功200或失败400"
                    "msg": "str: 失败提示信息"
                    "invalidSwitch": "list: 已纳管的非法的switch":
                    "licenseFile": "str:licenseId"
                }
        """
        try:
            lic_data = json.loads(self.decrypt(license_data))
        except Exception as e:
            Log.exception(traceback.format_exc())
            info= {"status": 400, "msg": "license decrypt failed", "licenseFile": ""}
            return info

        try:
            Log.info(lic_data)
            software_type = lic_data.get("softwareType")
            if software_type != "Ampcon-DC":
                info= {"status": 400, "msg": "license software type does not match", "licenseFile": ""}
                return info
            lic_id = str(lic_data.get("licenseId"))
            lic_type = lic_data.get("licenseType")
            lic_details = lic_data.get("details")
            
            db_session = inven_db.get_session()
            local_licenses = db_session.query(LicenseInfo)
            
            if lic_type == "trial":
                # 对于trial license需要判断license是否失效 
                current_time = datetime.datetime.now().date()
                for detail in lic_details:
                    # trial license expireDate为14d 15-90d正常试用 91d-104d为宽限期
                    # trial license expireDate修改为 104D 90d为试用 14d为宽限期
                    expire_time = datetime.datetime.strptime(detail['expireDate'], "%Y-%m-%d").date()
                    if expire_time >= current_time:
                        break
                else:
                    return {"status": 400, "msg": "license is expired", "licenseFile": lic_id}
                
                # 检查是否超过一条license, 超过一条肯定已经安装商用license, 不能安装trial license
                if local_licenses.count() > 1:
                    return {"status": 400, "msg": "can not import trial license", "licenseFile": lic_id}
                
                if local_licenses.count() == 1:
                    # 如果只有一条license，检查是否为trial license, license_id是否相同, 都满足继续安装不满足安装失败
                    local_lic = json.loads(self._decrypt_with_ampcon_key(local_licenses.first().license_data))
                    if local_lic.get("license_type") != "trial" or local_lic.get("license_id") != lic_id:
                        return {"status": 400, "msg": "can not import another trial license", "licenseFile": lic_id}
                
            # 获取待安装license中的hwid
            new_hwid_list = []
            new_hwid_info = {}
            for detail in lic_details:
                hardware_ids = [item.upper() for item in detail['hardwareIds']]
                new_hwid_list.extend(hardware_ids)
                expire_time = datetime.datetime.strptime(detail['expireDate'], "%Y-%m-%d").date()
                if lic_type == "trial":
                    # trial license expireDate修改为 104D 90d为试用 14d为宽限期 过期时间展示为90D
                    expire_time = expire_time - datetime.timedelta(days=14)
                    detail['expireDate'] = expire_time.strftime("%Y-%m-%d")
                for hwid in hardware_ids:
                    current_time = datetime.datetime.now().date()
                    if expire_time < current_time:
                        new_hwid_info[hwid] = {
                            "status": "EXPIRED",
                            "expire_time": detail['expireDate']
                        }
                    else :
                        new_hwid_info[hwid] = {
                            "status": "VALID",
                            "expire_time": detail['expireDate']
                        }
                
            old_lic_list = []
            is_new_lic = True
            old_lic_id = 0
            # 遍历lic数据库解码 获取已有lic中的hwid
            # 遍历同时检查id是否相同 如果相同修改标志位, 同时old_lic_list不需要记录相同id的lic中的hwid
            for license in local_licenses:
                local_lic = json.loads(self._decrypt_with_ampcon_key(license.license_data))
                if local_lic.get("license_id") == lic_id:
                    is_new_lic = False
                    old_lic_id = license.id
                    continue
                # 如果存在trial license, trial license中的hwid不需要记录, 防止正式license中不包含trial license中的hwid
                if local_lic.get("license_type") == "trial":
                    continue
                old_lic_list.extend(list(local_lic['license_info'].keys()))  
            
            invalid_hwid_list = []
            switch_hwid_list = []
            # 获取纳管的switch hwid
            switch_query = db_session.query(Switch).filter(Switch.is_picos_v == False)
            for switch in switch_query:
                if switch.hwid:
                    switch_hwid_list.append(switch.hwid.strip())
            
            # diff lic hwid和纳管的hwid
            # Log.info(new_hwid_list)
            # Log.info(old_lic_list)
            # Log.info(switch_hwid_list)
            for hwid in switch_hwid_list:
                if hwid not in new_hwid_list and hwid not in old_lic_list:
                    invalid_hwid_list.append(hwid)
                    
            # Log.info(invalid_hwid_list)
            # invalid_hwid_list 不为空则返回
            if len(invalid_hwid_list) > 0 :
                query_switch = db_session.query(Switch).filter(func.trim(Switch.hwid).in_(invalid_hwid_list))
                return  {
                            "status": 400,
                            "msg": "import license after delete invalid hwids",
                            "invalidSwitch": [switch.make_dict() for switch in query_switch],
                            "licenseFile": lic_id
                        }
            
            # 安装lic
            ampcon_lic = {
                "license_id": lic_id,
                "license_type": lic_type,
                "create_time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 
                "license_info": new_hwid_info
            }
            Log.info(ampcon_lic)
        except Exception as e:
            Log.exception(traceback.format_exc())
            info= {"status": 400, "msg": "license import failed", "licenseFile": ""}
            return info    
        
        try:
            json_str = json.dumps(ampcon_lic)
            # print(json_str)
            lic_bytes = json_str.encode('utf-8')
            encrypted_data =self._encrypt_with_ampcon_key(lic_bytes)
            if is_new_lic:
                # 新lic直接插入数据库
                with db_session.begin(subtransactions=True):
                    db_session.add(LicenseInfo(license_data=encrypted_data, license_type=lic_type))
                    # 安装正式license时 如果存在trial lic 则删除
                    if lic_type != "trial":
                        trial_license = db_session.query(LicenseInfo).filter(LicenseInfo.license_type == "trial")
                        trial_license.delete()
            else:
                # 旧lic覆盖
                old_lic = db_session.query(LicenseInfo).filter(LicenseInfo.id == old_lic_id)
                old_lic.update({"license_data": encrypted_data, "license_type": lic_type})
        except Exception as e:
            Log.exception(traceback.format_exc())
            info= {"status": 400, "msg": "import license failed", "licenseFile": lic_id}
        else:
            info = {"status": 200, "msg": "import license succeed", "licenseFile": lic_id}
        finally:
            return info
          
    
    def check_license(self):
        """
        登录前检查license是否全部失效
        Returns:
            bool: license正常返回true, 所有license过期返回false
        """
        db_session = inven_db.get_session()
        local_licenses = db_session.query(LicenseInfo)
        
        # 没有license的时候允许登录
        if local_licenses.count() == 0:
            return {"status": 200, "msg": f"License check skipped "}
        
        # 如果存在商用lic 直接通过
        if local_licenses.filter(LicenseInfo.license_type != "trial").count() != 0:
            return {"status": 200, "msg": f"License check succeed "}
        
        # 仅解码校验trial license
        trial_license = local_licenses.filter(LicenseInfo.license_type == "trial").first()
        if trial_license:
            current_time = datetime.datetime.now().date()
            local_lic = json.loads(self._decrypt_with_ampcon_key(trial_license.license_data))
            for lic, license_info in local_lic["license_info"].items():
                # 找一个有效或过期的license 这里默认trial license 过期时间都一样 
                if license_info["status"] == "INVALID":
                    continue
                expire_time = datetime.datetime.strptime(license_info["expire_time"], "%Y-%m-%d").date()
                # trial license 过期时间改为104D expire_time存放的为前90D 宽限期为expire_time + 14
                grace_expire_time = expire_time + datetime.timedelta(days=14)
                if expire_time >= current_time:
                    return {"status": 200, "msg": f"License check succeed "}
                elif grace_expire_time >= current_time:
                    return {"status": 201, "msg": f"The trial license is expired, you have a 14-day grace period. If you need to continue to use it, please purchase a standard license"}
            return {"status": 400, "msg": f"License is expired "}
        else:
            return {"status": 400, "msg": f"License not imported"} 
    
    
    def check_hwid(self, hwids: list):
        """
        检查当前hwid 是否已授权.
            遍历所有license info, 找出包含hwid的license, 
        Args:
            hwid (list): hwid.
        Returns:
            Dict[str, str]: 检查结果 {"status": "int 校验是否完成200or400", msg: "str", "valid": "有效的hwid", "invalid": "无效的hwid"}.
        """
        invalid_list = [hwid.strip() for hwid in hwids]
        valid_list = []
        try:
            db_session = inven_db.get_session()
            local_licenses = db_session.query(LicenseInfo)
            for license in local_licenses:
                local_lic = json.loads(self._decrypt_with_ampcon_key(license.license_data))
                # 逐个判断hwid是否有效
                for hwid in invalid_list:
                    if hwid in local_lic["license_info"]:
                        # 如果一个hwid 存在于多个license文件中，只要存在有效的就可以被纳管
                        # 到期不限制纳管 不需要比较过期时间 只要不是无效的都可以纳管
                        if local_lic["license_info"][hwid]["status"] != "INVALID":
                            valid_list.append(hwid)
                            
                # 更新无效list
                valid_set = set(valid_list)
                invalid_list[:] = [item for item in invalid_list if item not in valid_set]     
        except Exception as e:
            Log.exception(traceback.format_exc())
            info= {"status": 400, "msg": "check hwid failed"}
        else:
            if len(invalid_list) > 0:
                info = {"status": 400, "valid": valid_list, "invalid": invalid_list, "msg": "part of hwid is invalid"}
            else:
                info = {"status": 200, "msg": "check hwid success"}
        finally:
            return info

    
    def get_license_info(self):
        """
        获取所有license信息,用于前端展示
        Returns:
            Dict[str, str]: license信息.
        """
        db_session = inven_db.get_session()
        local_licenses = db_session.query(LicenseInfo)
        license_info = []
        for license in local_licenses:
            local_lic = json.loads(self._decrypt_with_ampcon_key(license.license_data))
            local_lic["id"] = license.id
            for hwid, hwid_lic in local_lic["license_info"].items():
                # 关联查询switch model
                switch = db_session.query(Switch).filter(func.trim(Switch.hwid) == hwid).first()
                if switch:
                    hwid_lic["platform_model"] = switch.platform_model
                else:
                    hwid_lic["platform_model"] = "-"
                # 针对过期trial license 添加说明 用于alarm显示
                if local_lic.get("license_type") == "trial":
                    if hwid_lic["status"] == "INVALID":
                        continue
                    current_time = datetime.datetime.now().date()
                    expire_time = datetime.datetime.strptime(hwid_lic["expire_time"], "%Y-%m-%d").date()
                    # trial license 过期时间改为104D expire_time存放的为前90D 宽限期为expire_time + 14
                    grace_expire_time = expire_time + datetime.timedelta(days=14)
                    if grace_expire_time >= current_time:
                        hwid_lic["description"] = "license is expired, You won't be able to login after the 14-day grace period."
            # print(local_lic)
            license_info.append(local_lic)
            
        return {"status": 200, "data": license_info}
    
    def invalidate_hwids(self, license_id: int, hwids: list) -> Dict[str, str]:
        """
        对一批hwid失效.
            1.解码lic
            2.生成失效码
            3.开启事务
                1). 删除switch 中的设备
                2). 更新lic信息
            
        Args:
            license_id (int): license 的主键id
            hwids (list): 待失效的hwid list.
        Returns:
            Dict[str, str]: 失效结果. {"status": "bool:失效是否成功", "invalidCode": "bytes:失效码", "licenseFile": "str:licenseId" }
        """
        try:
            db_session = inven_db.get_session()
            local_license = db_session.query(LicenseInfo).filter(LicenseInfo.id == license_id).first()
            if not local_license:
                return {"status": 400, "msg": "license not found"}
            local_lic = json.loads(self._decrypt_with_ampcon_key(local_license.license_data))
        except Exception as e:
            Log.exception(traceback.format_exc())
            info= {"status": 400, "msg": "license decrypt failed", "licenseFile": ""}
            return info
        
        try:
            lic_id = local_lic.get("license_id")
            # 更新status
            for hwid in hwids:
                local_lic["license_info"][hwid]["status"] = "INVALID"
            data = json.dumps(local_lic).encode('utf-8')
            encrypted_data =self._encrypt_with_ampcon_key(data)
            
            # 生成失效码
            invalid_data = {
                "license_id": local_lic["license_id"],
                "create_time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "license_info": hwids
            }
            invalid_code = self.encrypt(json.dumps(invalid_data).encode('utf-8')).decode()

            with db_session.begin(subtransactions=True):
                local_license = db_session.query(LicenseInfo).filter(LicenseInfo.id == license_id)
                local_license.update({"license_data": encrypted_data})
                # 删除switch 及对应的config                
                query_switch = db_session.query(Switch).filter(func.trim(Switch.hwid).in_(hwids)).all()

                for switch in query_switch:
                    allow_config_list = ['global', 'regional']
                    for config in switch.configs:
                        if config.type not in allow_config_list:
                            db_session.delete(config)

                    if AmpConBaseTask.get_running_job_by_sn(switch.sn):
                        AmpConBaseTask.kill_process_by_sn(switch.sn)

                    db_session.delete(switch)
                    db_session.query(SwitchConfigBackup).filter(SwitchConfigBackup.sn == switch.sn).delete()
                    db_session.query(DeployedSecuritySwitch).filter(DeployedSecuritySwitch.sn == switch.sn).delete()
                
        except Exception as e:
            Log.exception(traceback.format_exc())
            info= {"status": 400, "msg": "invalidate license failed" , "licenseFile": lic_id}
        else:
            info = {"status": 200, "msg": "invalidate license succeed", "invalidCode": invalid_code, "licenseFile": lic_id}
        finally:
            return info
        
        
    def check_expire(self):
        """
        预留一个接口用于轮询检查license是否过期
        Returns:
            bool: 任务执行是否成功.
        """
        try:
            db_session = inven_db.get_session()
            local_licenses = db_session.query(LicenseInfo)
            current_time = datetime.datetime.now().date()
            for license in local_licenses:
                new_lic = {}
                local_lic = json.loads(self._decrypt_with_ampcon_key(license.license_data))
                for hwid, hwid_lic in local_lic["license_info"].items():
                    # 非有效的就不更新了
                    if hwid_lic["status"] != "VALID":
                        continue
                    expire_time = datetime.datetime.strptime(hwid_lic["expire_time"], "%Y-%m-%d").date()
                    if expire_time < current_time:
                        if not new_lic:
                            new_lic = {
                                "license_id": local_lic["license_id"],
                                "license_type": local_lic["license_type"],
                                "create_time": local_lic["create_time"], 
                                "license_info": local_lic["license_info"],
                            }
                        new_lic["license_info"][hwid]["status"] = "EXPIRED"
                
                # new_lic不为空 则需要更新数据库
                if new_lic:
                    data = json.dumps(new_lic).encode('utf-8')
                    encrypted_data =self._encrypt_with_ampcon_key(data)
                    old_license = db_session.query(LicenseInfo).filter(LicenseInfo.id == license.id)
                    old_license.update({"license_data": encrypted_data})
                    
            return True
        except Exception as e:
            Log.exception(traceback.format_exc())
            return False
    
    def _load_public_key(self, public_key_file):
        with open(public_key_file, "rb") as f:
            public_key = rsa.PublicKey.load_pkcs1(f.read())
        return public_key
    
    def _load_private_key(self, private_key_file):
        with open(private_key_file, "rb") as f:
            private_key = rsa.PrivateKey.load_pkcs1(f.read())
        return private_key
    
    def _encrypt_with_public_key(self, lic_data, public_key):
        # 生成随机rc4 key
        num = [random.randint(0, 255) for _ in range(self.rsa_encrypt_len)]
        num[0] = 0
        rc4_key = bytes(num)
        
        # rc4加密
        encrypt_lic_data = self._rc4(lic_data, rc4_key)
        
        # 计算元数据md5
        md5 = self._calculate_md5(lic_data)
        md5_bytes = bytes.fromhex(md5)
        
        # 计算元数据sha1
        sha1 = self._calculate_sha1(lic_data)
        sha1_bytes = bytes.fromhex(sha1)

        # 对rc4 key 用public key进行rsa no padding加密
        encrypt_rc4_key = self._rsa_encrypt_no_padding(rc4_key, public_key)
        
        # 加密后的128位rc4 key + MD5 + sha1 + rc4加密数据
        lic_data = encrypt_rc4_key + md5_bytes + sha1_bytes + encrypt_lic_data
        
        encrypt_data = base64.b64encode(lic_data)
        # 标准base64 每64位换行
        encoded_with_newlines = b'\n'.join([encrypt_data[i:i+64] for i in range(0, len(encrypt_data), 64)])
        return encoded_with_newlines
    
    def _decrypt_with_private_key(self, lic_data, private_key):
        # base64解密
        base64_data = base64.b64decode(lic_data)
        encrypt_rc4_key = base64_data[:self.rsa_encrypt_len]

        # 对前128位 用private key进行rsa no padding解密
        rc4_key = self._rsa_decrypt_no_padding(encrypt_rc4_key, private_key)
        
        # rc4解密元数据
        encrypy_data = base64_data[self.header_len:]
        data = self._rc4(encrypy_data, rc4_key)
        # print(data)
        
        # MD5校验
        md5 = self._calculate_md5(data)
        if md5 != base64_data[self.rsa_encrypt_len:self.rsa_encrypt_len+self.md5_digest_length].hex():
            raise ValueError("LICERR BAD MD5")
        return data
    
    def _encrypt_with_ampcon_key(self, license_info):
        """
        基于ampcon public key 对license 加密
        """
        return self._encrypt_with_public_key(license_info, self.apmcon_public_key)


    def _decrypt_with_ampcon_key(self, encrypted_license):
        """
        基于ampcon private key 对license 解密
        """
        return self._decrypt_with_private_key(encrypted_license, self.ampcon_private_key)
    
    
    def _rc4(self, ciphertext, key):
        S = list(range(256))
        j = 0
        for i in range(256):
            j = (j + S[i] + key[i % len(key)]) % 256
            S[i], S[j] = S[j], S[i]

        i = j = 0
        plaintext = bytearray()
        for byte in ciphertext:
            i = (i + 1) % 256
            j = (j + S[i]) % 256
            S[i], S[j] = S[j], S[i]
            plaintext.append(byte ^ S[(S[i] + S[j]) % 256])

        return bytes(plaintext)

    # md5
    def _calculate_md5(self, input_data):
        if isinstance(input_data, str):
            input_data = input_data.encode() 
        
        md5_hash = hashlib.md5()
        md5_hash.update(input_data)
        return md5_hash.hexdigest()

    # sha1
    def _calculate_sha1(self, input_data):
        if isinstance(input_data, str):
            input_data = input_data.encode() 
        sha1 = hashlib.sha1()
        sha1.update(input_data)    
        return sha1.hexdigest()

    def _rsa_publickey_decrypt(self, cipher, public_key):
        encrypted = transform.bytes2int(cipher)
        # 密文^e mod n
        decrypted = core.decrypt_int(encrypted, public_key.e, public_key.n)
        
        # 按位补零
        blocksize = common.byte_size(public_key.n)
        text = transform.int2bytes(decrypted, blocksize)
        return text

    def _rsa_encrypt_no_padding(self, message, public_key):    
        keylength = common.byte_size(public_key.n)
        text = transform.bytes2int(message)
        # 明文^e mod n
        encrypted = core.encrypt_int(text, public_key.e, public_key.n)
        ciphertext = transform.int2bytes(encrypted, keylength)
        return ciphertext

    def _rsa_decrypt_no_padding(self, ciphertext, private_key):    
        blocksize = common.byte_size(private_key.n)
        encrypted = transform.bytes2int(ciphertext)
        # 密文^d mod n
        decrypted = core.decrypt_int(encrypted, private_key.d, private_key.n)
        message = transform.int2bytes(decrypted, blocksize)
        return message 
    

if __name__ == '__main__':
    licensechecker = AmpConDCLicenseChecker()
    lic = "./pica2.lic"
    with open(lic, 'rb') as f:
        lic_data = f.read()
    print(lic_data)
    license_info = licensechecker.install_license(lic_data)
    print(license_info)
    
    # data = license_checker.encrypt(license_info)
    # print(data)
    
    # data = license_checker.encrypt_with_ampcon_key(license_info)
    # print(data)
    
    # data = license_checker.decrypt_with_ampcon_key(data)
    # print(data)