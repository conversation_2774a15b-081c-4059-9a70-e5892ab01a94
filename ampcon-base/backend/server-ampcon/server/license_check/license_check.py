import importlib
import os

from typing import Dict
import abc

class LicenseChecker(abc.ABC):
    """
    抽象基类 LicenseChecker, 定义license相关接口
    """

    @abc.abstractmethod
    def encrypt(self, data: bytes) -> bytes:
        """
        对需要向外输出的license info进行加密.
        Args:
            data (bytes): 待加密的数据.
        Returns:
            bytes: 加密后的数据.
        """
        pass

    @abc.abstractmethod
    def decrypt(self, data: bytes) -> bytes:
        """
        对license info加密数据进行解密.
        Args:
            data (bytes): 待解密的数据.
        Returns:
            bytes: 解密后的数据.
        """
        pass

    @abc.abstractmethod
    def install_license(self, license_data: bytes) -> Dict[str, str]:
        """
        license安装检查,无异常则安装license
        Args:
            license_data (bytes): license加密数据.
        Returns:
            Dict[str, str]: 安装检查结果，用于前端显示.
                {
                    "status": "bool: 安装成功或失败"
                    "msg": "str: 失败提示信息"
                    "invalidHwid": "list: 已纳管的非法的hwid":
                }
        """
        pass
    
    @abc.abstractmethod
    def check_license(self) -> Dict[str, str]:
        """
        登录前检查license是否全部失效
        Returns:
            Dict[str, str]: 检查结果 {"status": "int 所有授权可用200 or 400", msg: "str"}.
        """
        pass

    @abc.abstractmethod
    def check_hwid(self, hwids: list) -> Dict[str, str]:
        """
        检查当前hwid 是否已授权.
        Args:
            hwids (list): hwid集合 支持批量检查.
        Returns:
            Dict[str, str]: 检查结果 {"status": "int 授权可用200 or 400", msg: "str"}.
        """
        pass
    
    @abc.abstractmethod
    def get_license_info(self) -> Dict[str, str]:
        """
        获取所有license信息,用于前端展示
        Returns:
            Dict[str, str]: license信息.
        """
        pass
    
    @abc.abstractmethod
    def invalidate_hwids(self, license_id: int, hwids: list) -> Dict[str, str]:
        """
        对一批hwid失效.
        Args:
            license_id (int): license 的主键id
            hwids (list): 待失效的hwid list.
        Returns:
            Dict[str, str]: 失效结果. {"status": "bool:失效是否成功", "invalidCode": "bytes:失效码" }
        """
        pass
    
    @abc.abstractmethod
    def check_expire(self) -> bool:
        """
        预留一个接口用于轮询检查license是否过期
        Returns:
            bool: 任务执行是否成功.
        """
        pass

def load_modules(directory):
    modules = []
    for filename in os.listdir(directory):
        if filename.endswith('.py') or filename.endswith('.pyc') and not filename.startswith('__'):
            module_name = os.path.splitext(filename)[0]
            module = importlib.import_module(f'{directory.replace("/", ".")}.{module_name}')
            modules.append(module)
    return modules

# 此处为方便后续多版本不同licensecheck子类导入预留动态加载，编译打包时需要将多余的licensecheck删除
# 注意：开发调试时如果存在多个licensecheck 可能会随机导入一个
modules = load_modules('license_check')
print(modules)
for module in modules:
    module_items = list(vars(module).items())
    for name, obj in module_items:
        if isinstance(obj, type) and issubclass(obj, LicenseChecker) and obj != LicenseChecker:
            print("found class: ", obj)
            licensechecker = obj()