import logging
import base64
import rsa
import hashlib
import random
import json
import datetime
import traceback
import subprocess
from rsa import transform, core, transform, common

from typing import Dict

from server.db.models.inventory import inven_db, LicenseInfo, Switch
from server.license_check.license_check import LicenseChecker

Log = logging.getLogger(__name__)

PICA_PUBLIC_KEY_PATH = "./pica-public.key"
APMCON_PUBLIC_KEY_PATH = "./ampcon-public.key"
AMPCON_PRIVATE_KEY_PATH = "./ampcon-private.key"

class AmpConTLicenseChecker(LicenseChecker):
    """
    实现类 AmpConTLicenseChecker, 实现AmpCon T license相关接口
    """
    def __init__(self):
        self.pica_public_key = self._load_public_key(PICA_PUBLIC_KEY_PATH)
        self.ampcon_private_key = self._load_private_key(AMPCON_PRIVATE_KEY_PATH)
        self.apmcon_public_key = self._load_public_key(APMCON_PUBLIC_KEY_PATH)
        
        self.rsa_len = 2048
        self.rsa_encrypt_len = int(self.rsa_len / 8)
        self.md5_digest_length = 16
        self.sha_digest_length = 20
        self.header_len = self.rsa_encrypt_len + self.md5_digest_length + self.sha_digest_length
        
    def encrypt(self, data):
        """
        基于pica public key 对license 加密
        """
        return self._encrypt_with_public_key(data, self.pica_public_key)

    def decrypt(self, data):
        """
        基于pica public key 对license 解密
        """
        # base64解码
        base64_data = base64.b64decode(data)
        encrypt_rc4_key = base64_data[:self.rsa_encrypt_len]

        # ras解密获取rc4key
        rc4_key = self._rsa_publickey_decrypt(encrypt_rc4_key, self.pica_public_key)
        # print(rc4_key, len(rc4_key))

        # rc4解密数据，lic数据在头部164位之后
        encrypy_data = base64_data[self.header_len:]
        data = self._rc4(encrypy_data, rc4_key)
        
        # 校验MD5
        md5 = self._calculate_md5(data)
        if md5 != base64_data[self.rsa_encrypt_len:self.rsa_encrypt_len+self.md5_digest_length].hex():
            raise ValueError("LICERR BAD MD5")
        return data
    
    def install_license(self, license_data):
        """
            1.license解密
            2.检查license Type是否是trial license, 如果是：
                1). 检查license是否已经失效, 失效则安装失败
                2). 检查当前是否有超过1条已安装license, 超过返回安装失败, 如果没有license, 则直接安装
                3). 如果只有一条license, 检查是否是正式license, 检查license id是否相同, 如果不满足则返回失败
            3.license校验
                1). 比较deviceId和systemMac是否匹配
                2). 检查license是否过期
        Returns:
            Dict[str, str]: 安装检查结果.
                {
                    "status": "int: 安装成功200或失败400"
                    "msg": "str: 失败提示信息"
                    "licenseFile": "str:licenseId"
                }
        """
        info = {}
        try:
            lic_data = json.loads(self.decrypt(license_data))
        except Exception as e:
            Log.exception(traceback.format_exc())
            info= {"status": 400, "msg": "license decrypt failed", "licenseFile": ""}
            return info

        try:
            Log.info(lic_data)
            software_type = lic_data.get("softwareType")
            if software_type != "Ampcon-T":
                info= {"status": 400, "msg": "license software type does not match", "licenseFile": ""}
                return info
            lic_id = str(lic_data.get("licenseId"))
            lic_type = lic_data.get("licenseType")
            lic_device_id = lic_data.get("deviceId")
            lic_system_mac = lic_data.get("systemMac")
            lic_expire_date = lic_data.get("expireDate")
            current_time = datetime.datetime.now().date()
            expire_time = datetime.datetime.strptime(lic_expire_date, "%Y-%m-%d").date()
            
            db_session = inven_db.get_session()
            local_licenses = db_session.query(LicenseInfo)
            
            if lic_type == "trial":
                # trial license expireDate修改为 104D 90d为试用 14d为宽限期 过期时间展示为90D
                if expire_time < current_time:
                    return {"status": 400, "msg": "trial license is expired", "licenseFile": lic_id}
                
                expire_time = expire_time - datetime.timedelta(days=14)
                lic_expire_date = expire_time.strftime("%Y-%m-%d")
                
                # 检查是否超过一条license, 超过一条肯定已经安装商用license, 不能安装trial license
                if local_licenses.count() > 1:
                    return {"status": 400, "msg": "can not import trial license", "licenseFile": lic_id}
                
                if local_licenses.count() == 1:
                    # 如果只有一条license，检查是否为trial license, license_id是否相同, 都满足继续安装不满足安装失败
                    local_lic = json.loads(self._decrypt_with_ampcon_key(local_licenses.first().license_data))
                    if local_lic.get("license_type") != "trial" or local_lic.get("license_id") != lic_id:
                        return {"status": 400, "msg": "can not import another trial license", "licenseFile": lic_id}
                

            is_new_lic = True
            old_lic_id = 0
            # 遍历lic数据库解码 判断是否是新license
            for license in local_licenses:
                local_lic = json.loads(self._decrypt_with_ampcon_key(license.license_data))
                if local_lic.get("license_id") == lic_id:
                    is_new_lic = False
                    old_lic_id = license.id
                    break

            # 获取product_uuid
            try:
                with open(f'/usr/share/uuid', 'r') as secret_file:
                    secret = secret_file.read().strip()
                product_uuid = secret
            except FileNotFoundError:
                output = subprocess.check_output(['cat', '/sys/class/dmi/id/product_uuid'],
                                                 universal_newlines=True)
                product_uuid = output.strip()
            
            # 获取mac
            output = subprocess.check_output(['cat', './mac_address'], universal_newlines=True)
            mac_str = output.strip()
            
            Log.info(f"{mac_str}, {product_uuid}, {lic_device_id}, {lic_system_mac}")
            if mac_str and product_uuid:
                if product_uuid != lic_device_id or mac_str != lic_system_mac:
                    return {"status": 400, "msg": "system info mismatch", "licenseFile": lic_id}  
            else:
                return {"status": 400, "msg": "get system info failed", "licenseFile": lic_id}
            # 安装lic
            ampcon_lic = {
                "license_id": lic_id,
                "license_type": lic_type,
                "create_time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 
                "expire_time": lic_expire_date,
                "status": "VALID" if expire_time >= current_time else "EXPIRED"
            }
            Log.info(ampcon_lic)
            json_str = json.dumps(ampcon_lic)
            # print(json_str)
            lic_bytes = json_str.encode('utf-8')
            encrypted_data =self._encrypt_with_ampcon_key(lic_bytes)
            if is_new_lic:
                # 新lic直接插入数据库
                with db_session.begin(subtransactions=True):
                    db_session.add(LicenseInfo(license_data=encrypted_data, license_type=lic_type))
                    # 安装正式license时 如果存在trial lic 则删除
                    if lic_type != "trial":
                        trial_license = db_session.query(LicenseInfo).filter(LicenseInfo.license_type == "trial")
                        trial_license.delete()
            else:
                # 旧lic覆盖
                old_lic = db_session.query(LicenseInfo).filter(LicenseInfo.id == old_lic_id)
                old_lic.update({"license_data": encrypted_data, "license_type": lic_type})
        except Exception as e:
            Log.exception(traceback.format_exc())
            return {"status": 400, "msg": "import license failed", "licenseFile": lic_id}
        return {"status": 200, "msg": "import license succeed", "licenseFile": lic_id}
          
    
    def check_license(self):
        """
        登录前检查license是否全部失效
        Returns:
            bool: license正常返回true, 所有license过期返回false
        """
        db_session = inven_db.get_session()
        local_licenses = db_session.query(LicenseInfo)
        # 如果存在商用lic 直接通过
        if local_licenses.filter(LicenseInfo.license_type != "trial").count() != 0:
            return {"status": 200, "msg": f"License check succeed "}
        
        # 仅解码校验trial license
        trial_license = local_licenses.filter(LicenseInfo.license_type == "trial").first()
        if trial_license:
            current_time = datetime.datetime.now().date()
            local_lic = json.loads(self._decrypt_with_ampcon_key(trial_license.license_data))
            expire_time = datetime.datetime.strptime(local_lic["expire_time"], "%Y-%m-%d").date()
            # trial license 过期时间改为104D expire_time存放的为前90D 宽限期为expire_time + 14
            grace_expire_time = expire_time + datetime.timedelta(days=14)
            if expire_time >= current_time:
                return {"status": 200, "msg": f"License check succeed "}
            elif grace_expire_time >= current_time:
                return {"status": 201, "msg": f"The trial license is expired, you have a 14-day grace period. If you need to continue to use it, please purchase a standard license"}
            return {"status": 400, "msg": f"License is expired "}
        else:
            return {"status": 400, "msg": f"License not imported"} 
    
    
    def check_hwid(self, hwids: list):
        """
        检查当前hwid 是否已授权.
        Args:
            hwid (list): hwid.
        Returns:
            Dict[str, str]: 检查结果 {"status": "int 校验是否完成200or400", msg: "str", "valid": "有效的hwid", "invalid": "无效的hwid"}.
        """
        # ampcon-t 不需要check hwid    
        return {"status": 200, "msg": "check hwid success"}

    
    def get_license_info(self):
        """
        获取所有license信息,用于前端展示
        Returns:
            Dict[str, str]: license信息.
        """
        db_session = inven_db.get_session()
        local_licenses = db_session.query(LicenseInfo)
        license_info = []
        for license in local_licenses:
            local_lic = json.loads(self._decrypt_with_ampcon_key(license.license_data))
            local_lic["id"] = license.id
            # 针对过期trial license 添加说明 用于alarm显示
            if local_lic.get("license_type") == "trial" and local_lic.get("status") == "EXPIRED":
                current_time = datetime.datetime.now().date()
                expire_time = datetime.datetime.strptime(local_lic["expire_time"], "%Y-%m-%d").date()
                # trial license 过期时间改为104D expire_time存放的为前90D 宽限期为expire_time + 14
                grace_expire_time = expire_time + datetime.timedelta(days=14)
                if grace_expire_time >= current_time:
                    local_lic["description"] = "license is expired, You won't be able to login after the 14-day grace period."
            # print(local_lic)
            license_info.append(local_lic)
            
        return {"status": 200, "data": license_info}
    
    def invalidate_hwids(self, license_id: int, hwids: list) -> Dict[str, str]:
        """
        对一批hwid失效.
        Args:
            license_id (int): license 的主键id
            hwids (list): 待失效的hwid list.
        Returns:
            Dict[str, str]: 失效结果. {"status": "bool:失效是否成功", "invalidCode": "bytes:失效码", "licenseFile": "str:licenseId" }
        """
        # ampcon-t 没有失效码
        return {"status": 400, "msg": "invalidate license failed" }
        
        
    def check_expire(self):
        """
        预留一个接口用于轮询检查license是否过期
        Returns:
            bool: 任务执行是否成功.
        """
        try:
            db_session = inven_db.get_session()
            local_licenses = db_session.query(LicenseInfo)
            current_time = datetime.datetime.now().date()
            for license in local_licenses:
                local_lic = json.loads(self._decrypt_with_ampcon_key(license.license_data))
                # 非有效的就不更新了
                if local_lic["status"] != "VALID":
                    continue
                expire_time = datetime.datetime.strptime(local_lic["expire_time"], "%Y-%m-%d").date()
                if expire_time < current_time:
                    local_lic["status"] = "EXPIRED"
                    data = json.dumps(local_lic).encode('utf-8')
                    encrypted_data =self._encrypt_with_ampcon_key(data)
                    old_license = db_session.query(LicenseInfo).filter(LicenseInfo.id == license.id)
                    old_license.update({"license_data": encrypted_data})
            return True
        except Exception as e:
            Log.exception(traceback.format_exc())
            return False
    
    def _load_public_key(self, public_key_file):
        with open(public_key_file, "rb") as f:
            public_key = rsa.PublicKey.load_pkcs1(f.read())
        return public_key
    
    def _load_private_key(self, private_key_file):
        with open(private_key_file, "rb") as f:
            private_key = rsa.PrivateKey.load_pkcs1(f.read())
        return private_key
    
    def _encrypt_with_public_key(self, lic_data, public_key):
        # 生成随机rc4 key
        num = [random.randint(0, 255) for _ in range(self.rsa_encrypt_len)]
        num[0] = 0
        rc4_key = bytes(num)
        
        # rc4加密
        encrypt_lic_data = self._rc4(lic_data, rc4_key)
        
        # 计算元数据md5
        md5 = self._calculate_md5(lic_data)
        md5_bytes = bytes.fromhex(md5)
        
        # 计算元数据sha1
        sha1 = self._calculate_sha1(lic_data)
        sha1_bytes = bytes.fromhex(sha1)

        # 对rc4 key 用public key进行rsa no padding加密
        encrypt_rc4_key = self._rsa_encrypt_no_padding(rc4_key, public_key)
        
        # 加密后的128位rc4 key + MD5 + sha1 + rc4加密数据
        lic_data = encrypt_rc4_key + md5_bytes + sha1_bytes + encrypt_lic_data
        
        encrypt_data = base64.b64encode(lic_data)
        # 标准base64 每64位换行
        encoded_with_newlines = b'\n'.join([encrypt_data[i:i+64] for i in range(0, len(encrypt_data), 64)])
        return encoded_with_newlines
    
    def _decrypt_with_private_key(self, lic_data, private_key):
        # base64解密
        base64_data = base64.b64decode(lic_data)
        encrypt_rc4_key = base64_data[:self.rsa_encrypt_len]

        # 对前128位 用private key进行rsa no padding解密
        rc4_key = self._rsa_decrypt_no_padding(encrypt_rc4_key, private_key)
        
        # rc4解密元数据
        encrypy_data = base64_data[self.header_len:]
        data = self._rc4(encrypy_data, rc4_key)
        # print(data)
        
        # MD5校验
        md5 = self._calculate_md5(data)
        if md5 != base64_data[self.rsa_encrypt_len:self.rsa_encrypt_len+self.md5_digest_length].hex():
            raise ValueError("LICERR BAD MD5")
        return data
    
    def _encrypt_with_ampcon_key(self, license_info):
        """
        基于ampcon public key 对license 加密
        """
        return self._encrypt_with_public_key(license_info, self.apmcon_public_key)


    def _decrypt_with_ampcon_key(self, encrypted_license):
        """
        基于ampcon private key 对license 解密
        """
        return self._decrypt_with_private_key(encrypted_license, self.ampcon_private_key)
    
    
    def _rc4(self, ciphertext, key):
        S = list(range(256))
        j = 0
        for i in range(256):
            j = (j + S[i] + key[i % len(key)]) % 256
            S[i], S[j] = S[j], S[i]

        i = j = 0
        plaintext = bytearray()
        for byte in ciphertext:
            i = (i + 1) % 256
            j = (j + S[i]) % 256
            S[i], S[j] = S[j], S[i]
            plaintext.append(byte ^ S[(S[i] + S[j]) % 256])

        return bytes(plaintext)

    # md5
    def _calculate_md5(self, input_data):
        if isinstance(input_data, str):
            input_data = input_data.encode() 
        
        md5_hash = hashlib.md5()
        md5_hash.update(input_data)
        return md5_hash.hexdigest()

    # sha1
    def _calculate_sha1(self, input_data):
        if isinstance(input_data, str):
            input_data = input_data.encode() 
        sha1 = hashlib.sha1()
        sha1.update(input_data)    
        return sha1.hexdigest()

    def _rsa_publickey_decrypt(self, cipher, public_key):
        encrypted = transform.bytes2int(cipher)
        # 密文^e mod n
        decrypted = core.decrypt_int(encrypted, public_key.e, public_key.n)
        
        # 按位补零
        blocksize = common.byte_size(public_key.n)
        text = transform.int2bytes(decrypted, blocksize)
        return text

    def _rsa_encrypt_no_padding(self, message, public_key):    
        keylength = common.byte_size(public_key.n)
        text = transform.bytes2int(message)
        # 明文^e mod n
        encrypted = core.encrypt_int(text, public_key.e, public_key.n)
        ciphertext = transform.int2bytes(encrypted, keylength)
        return ciphertext

    def _rsa_decrypt_no_padding(self, ciphertext, private_key):    
        blocksize = common.byte_size(private_key.n)
        encrypted = transform.bytes2int(ciphertext)
        # 密文^d mod n
        decrypted = core.decrypt_int(encrypted, private_key.d, private_key.n)
        message = transform.int2bytes(decrypted, blocksize)
        return message 
    

if __name__ == '__main__':
    licensechecker = AmpConTLicenseChecker()
    lic = "./pica2.lic"
    with open(lic, 'rb') as f:
        lic_data = f.read()
    print(lic_data)
    license_info = licensechecker.install_license(lic_data)
    print(license_info)
    
    # data = license_checker.encrypt(license_info)
    # print(data)
    
    # data = license_checker.encrypt_with_ampcon_key(license_info)
    # print(data)
    
    # data = license_checker.decrypt_with_ampcon_key(data)
    # print(data)