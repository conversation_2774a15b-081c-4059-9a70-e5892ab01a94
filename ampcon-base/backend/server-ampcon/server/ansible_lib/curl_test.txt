
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRpdHVzIiwiZXhwaXJ5IjoiMjAxOC0xMC0yNSAwMDo1NDo0MS43MjY5MzIifQ.Agpx-afcdVUe1IM-nMBtmbDgrGpsBPu9qEFQsZEk4IM

#curl -k -H "Content-Type: application/json" -H "Content-Type: application/json" -d @user.json -X POST https://license.pica8.com/auth
#curl -k -H "Content-Type: application/json" -H "Content-Type: application/json" -d @user.json -X GET https://license.pica8.com/auth

# Following can use either POST or GET
#curl -k -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRpdHVzIiwiZXhwaXJ5IjoiMjAxOC0xMC0yNSAwMDo1NDo0MS43MjY5MzIifQ.Agpx-afcdVUe1IM-nMBtmbDgrGpsBPu9qEFQsZEk4IM" -X GET https://license.pica8.com/get_inventory/titus

#curl -k -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRpdHVzIiwiZXhwaXJ5IjoiMjAxOC0xMC0yNSAwMDo1NDo0MS43MjY5MzIifQ.Agpx-afcdVUe1IM-nMBtmbDgrGpsBPu9qEFQsZEk4IM" -X POST -d @hwid.json https://license.pica8.com/get_license_details

But without json file fails:
curl -k -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRpdHVzIiwiZXhwaXJ5IjoiMjAxOC0xMC0yNSAwMDo1NDo0MS43MjY5MzIifQ.Agpx-afcdVUe1IM-nMBtmbDgrGpsBPu9qEFQsZEk4IM" -X GET -d {"hardware_id":"3F60-1B2E-0390-B1F6"} https://license.pica8.com/get_license_details
{
    "error": "no json object provided"
}

#curl -k -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRpdHVzIiwiZXhwaXJ5IjoiMjAxOC0xMC0yNSAwMDo1NDo0MS43MjY5MzIifQ.Agpx-afcdVUe1IM-nMBtmbDgrGpsBPu9qEFQsZEk4IM" -X POST -d @hwid.json https://license.pica8.com/check_license

#curl -k -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRpdHVzIiwiZXhwaXJ5IjoiMjAxOC0xMC0yNSAwMDo1NDo0MS43MjY5MzIifQ.Agpx-afcdVUe1IM-nMBtmbDgrGpsBPu9qEFQsZEk4IM" -X POST -d @hwid.json https://license.pica8.com/license_exists

#curl -k -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRpdHVzIiwiZXhwaXJ5IjoiMjAxOC0xMC0yNSAwMDo1NDo0MS43MjY5MzIifQ.Agpx-afcdVUe1IM-nMBtmbDgrGpsBPu9qEFQsZEk4IM" -X POST -d @lic_type.json https://license.pica8.com/license_count

