from flask_login import UserMixin


class User(UserMixin):

    def __init__(self):
        self.id = ''
        self.type = ''
        self.role = ''
        self.name = ''
        self.user_type = ''
        self.group = ''

    @property
    def is_anonymous(self):
        return False

    @property
    def is_active(self):
        return True

    def get_id(self):
        # return super(User, self).get_id()
        return '{0}@@{1}@@{2}@@{3}@@{4}'.format(self.id, self.type, self.role, self.user_type, self.group)

    @property
    def is_authenticated(self):
        return True


logged_in_users = []


