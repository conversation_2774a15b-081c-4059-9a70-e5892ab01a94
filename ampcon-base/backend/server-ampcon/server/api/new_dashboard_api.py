import asyncio
import datetime
import json
import logging
import os
import re
import subprocess
import traceback

from flask import (
    Blueprint,
    request,
    jsonify,
    Response
)
from flask_login import login_user, logout_user, current_user
from sqlalchemy import asc, desc
from sqlalchemy import func

from server import cfg
from server import constants
from server import enable_debug, init_log_level
from server import random_key
from server.ansible_deploy_switch import AnsibleDeploySwitch as DeployDriver
from server.ansible_deploy_switch import celery_start_push_security_config, celery_start_ansible_deploy, celery_start_push_parking_security_config, celery_start_rma
from server.auth_jwt import Auth_Handles
from server.celery_app import my_celery_app
from server.celery_app.automation_task import AmpConBaseTask
from server.db.models import inventory, monitor, general, dc_blueprint
from server.db.models.automation import Playbook, AnsibleJob, automation_db
from server.db.models.dc_blueprint import DCFabricTopologyNode
from server.db.models.inventory import inven_db, Switch, SwitchAutoConfig, Snmp<PERSON>ev<PERSON>, SnmpImportDetail
from server.db.models.monitor import monitor_db, OperationLog
from server.db.models.sdn_access import SdnAccessSwitch, sdn_access_db
from server.db.models.user import user_db
from server.db.models.vtep import VtepControlSwitch, vtep_db
from server.license_check.license_check import licensechecker
from server.user import User, logged_in_users
from server.util import pure_utils, snmp_util
from server.util import utils, str_helper
from server.util.permission import admin_permission, super_user_permission
from server.util.utils import get_parking_security_config, is_name_valid

new_dashboard_mold = Blueprint("mold", __name__, template_folder='templates')
LOG = logging.getLogger(__name__)
MAX_AUTH_ERROR = 1
key_reg = re.compile('key\s+([^\s]+)')
password_reg = re.compile('password\s+([^\s]+)')
auth_handles = Auth_Handles(random_key)


@new_dashboard_mold.route('/debug/<int:flag>')
@super_user_permission.require(http_exception=403)
def enable_or_disable_debug(flag):
    if flag == 1:
        enable_debug()
        info = 'enable debug'
    else:
        init_log_level()
        info = 'disable debug'
    LOG.debug(info)
    return jsonify({'info': info, 'status': 200})


@new_dashboard_mold.route('/reg/<string:info>', methods=['GET'])
def reg_switch(info):
    info_list = info.split(';')
    if not info_list[0]:
        return 'bad params'

    info = dict(
        sn=info_list[0],
        ip=info_list[1],
        model=info_list[2],
        hwid=info_list[3],
        flag=int(info_list[4]) if len(info_list) >= 5 else 0,
        # 0: inband 1: manage 2:vpn
        uplink_type=int(info_list[5]) if len(info_list) >= 6 else 0,
        # for support dell switch service code or tag
        service_code=info_list[6] if len(info_list) >= 7 else ''
    )

    ret = licensechecker.check_hwid([info['hwid']])
    if ret["status"] != 200:
        LOG.error(f'{info["sn"]} The switch hardware_id not in license')
        monitor_db.add_event(info['sn'], 'warn',
                             'switch %s hardware_id not in license' % info['sn'])
        inven_db.add_switch_log(info['sn'], 'The switch sn %s hardware_id not in license' % info['sn'],
                                level='warn')
        return 'The switch hardware_id not in license'

    if info['flag'] == 0:
        inven_db.delete_collection(inventory.DeployedSecuritySwitch, filters={'sn': [info['sn']]})

    # handle service code
    if info['service_code'] != '':
        switch = inven_db.get_switch_info_by_sn(info['service_code'])
        if switch:
            check_switch = inven_db.get_switch_info_by_sn(info['sn'])
            if check_switch:
                inven_db.add_switch_log(info['service_code'], 'The switch sn %s have already exist' % info['sn'],
                                        level='warn')
                return Response('The switch sn %s have already exist' % info['sn'], status=500)

            # update switch sn and service code
            inven_db.update_model(Switch, {'sn': [info['service_code']]},
                                  {'sn': info['sn'], 'remark': info['service_code']})
            inven_db.update_model(SwitchAutoConfig, {'name': [info['service_code'] + '_site_config']},
                                  {'name': info['sn'] + '_site_config'})

    switch = inven_db.get_switch_info_by_sn(info['sn'])

    if info['flag'] == 3:
        switch = switch or inven_db.get_model(inventory.Switch, filters={'sn': [info['sn']]})

        if switch.upgrade_status == constants.outswitch_status.UPGRADING:
            return 'upgrading'
        elif switch.upgrade_status == constants.outswitch_status.UPGRADE_FAILED:
            return 'upgrade failed'
        elif switch.upgrade_status == constants.outswitch_status.UPGRADED:
            return 'upgraded'
        return 'not-upgraded'

    reg = DeployDriver(info)
    deployed_security_switch = inven_db.get_model(inventory.DeployedSecuritySwitch, filters={'sn': [info['sn']]})
    if not switch:
        LOG.info("switch %s haven't been configured", info['sn'])
        monitor_db.add_event(info['sn'], 'warn',
                             'switch %s registered, but have not been configured' % info['sn'])

        # push parking security file and execute it
        if not deployed_security_switch and get_parking_security_config(info['sn']):
            celery_start_push_parking_security_config.delay(info, celery_sn=info['sn'], celery_task_name=f"push_parking_security_config_{info['sn']}")

        inven_db.update_switch_lot(info['sn'], info['ip'], info['model'])
        inven_db.update_lot_hardware_id(info["ip"], hw_id)
        return "switch haven't been configured"

    if not switch.enable and switch.import_type == constants.ImportType.RMA:
        LOG.info("rma switch %s haven't staged", info['sn'])
        monitor_db.add_event(info['sn'], 'warn',
                             'switch %s registered, but have not been staged' % info['sn'])

        # push parking security file and execute it
        if not deployed_security_switch and get_parking_security_config(info['sn']):
            celery_start_push_parking_security_config.delay(info, celery_sn=info['sn'], celery_task_name=f"push_parking_security_config_{info['sn']}")

        return "switch haven't been staged"

    if not switch.enable:
        LOG.info("switch %s haven't staged", info['sn'])
        if switch.status != 'Registered Not-staged':
            monitor_db.add_event(info['sn'],
                                 'warn', 'switch [sn:%s,ip:%s,model:%s,hwid:%s] registered, but have not staged' %
                                 (info['sn'], info['ip'], info['model'], info['hwid']))
            inven_db.update_status(info['sn'], 'Registered Not-staged')

        if not deployed_security_switch:
            celery_start_push_security_config.delay(info, celery_sn=info['sn'], celery_task_name=f"push_security_config_{info['sn']}")

        return "switch haven't been staged"
    else:
        inven_db.delete_collection(inventory.SwitchParking, filters={'sn': [info['sn']]}, session=inven_db.get_session())
        LOG.info("switch %s have been removed from SwitchParking", info['sn'])

    if deployed_security_switch and deployed_security_switch.invalid_times >= MAX_AUTH_ERROR:
        return Response('invalid tacacs user, please reinit', status=400)

    inven_db.update_model(inventory.Switch, {'sn': [info['sn']]}, {'tmp_ip': info['ip']})

    task_running = AmpConBaseTask.get_running_job_by_sn(info['sn'])

    if task_running and info['flag'] != 0:
        LOG.info('switch %s is in deploying', info['sn'])
        return "switch deploy is running"

    step = switch.step
    if info['flag'] == 0:
        LOG.info('switch %s re inited, update status to 0', info['sn'])
        inven_db.update_step(info['sn'], 0)
        step = 0
        if task_running:
            # switch re inited kill child process, restart deploy
            try:
                AmpConBaseTask.kill_process_by_sn(info['sn'])
            except Exception as e:
                LOG.exception(e)

    inven_db.update_status(info['sn'], 'Registered')
    inven_db.update_switch_montior(info['sn'], 1)

    if step < 6:
        if switch.import_type == constants.ImportType.RMA or inven_db.get_switch_back_sn(info['sn']):
            celery_start_rma.delay(info, celery_sn=info['sn'], celery_task_name=f"start_rma_{info['sn']}")
        else:
            celery_start_ansible_deploy.delay(info, celery_sn=info['sn'], celery_task_name=f"start_ansbile_deploy_{info['sn']}")

    return jsonify('registered')


@new_dashboard_mold.route('/reg/vpn/time', methods=['GET'])
def system_time():
    return datetime.datetime.now(datetime.UTC).strftime('%Y-%m-%d %H:%M:%S')


@new_dashboard_mold.route('/deploy/failed', methods=['POST'])
def handle_deploy_fail():
    """
    this function should handle deploy failed message
    json eg:
    {'sn': 'test11',
     'task_name': 'push full config task',
     'reason': 'cmd not found'
    }
    :return:
    """
    params = request.get_json()
    sn = params['sn']
    task_name = params['task_name']
    reason = params['reason']
    inven_db.add_switch_log(sn, 'task %s failed, reason[%s]' % (task_name, reason), level='error')
    return 'handled'


@new_dashboard_mold.route('/vpn_update_ip/<string:info>', methods=['GET'])
def vpn_update_ip(info):
    info_list = info.split(';')
    if not info_list[0]:
        return 'bad params'

    info = dict(
        sn=info_list[0].encode('utf8'),
        ip=info_list[1].encode('utf8')
    )
    switches = inven_db.get_switch_info_by_sn(info['sn'])
    if not switches:
        LOG.info("Unknown switch %s try to update VPN IP", info['sn'])
        monitor_db.add_event(info['sn'], 'warn',
                             'Unknown switch %s try to update VPN IP' % info['sn'])
    elif switches.status == constants.PROVISIONING_SUCCESS or switches.status == constants.SwitchStatus.IMPORTED:
        inven_db.update_model(inventory.Switch, {'sn': [info['sn']]}, {'mgt_ip': info['ip']})
    else:
        LOG.info("Switch %s try to update VPN IP, but it is in deploying", info['sn'])
        monitor_db.add_event(info['sn'], 'warn',
                             'Unknown switch %s try to update VPN IP, but it is in deploying' % info['sn'])
    return jsonify('update_vpn_ip')


@new_dashboard_mold.route('/login', methods=['POST', 'GET'])
def login():
    if request.method == "GET":
        return jsonify({"url": "/login"}), 302
    else:
        form = request.json
        username = form.get('username', "")
        pwd = form.get('password', None)
        remember = form.get('remember', None)

        # 检查license
        res = licensechecker.check_license()
        if not (200 <= res["status"] < 300):
            return jsonify(res)

        if not is_name_valid(username):
            msg = {'msg': 'Username is invalid'}
            return jsonify({'status': 500, 'msg': msg})

        if username and pwd:
            tac_status = utils.tacacs_auth(username, pwd)
            if tac_status[0]:
                if tac_status[1][0]:
                    # authentication success
                    monitor_db.add_operation_log(username, '/login', 'login', 'success',
                                                 'username: {0} password:XXXXX'.format(username),
                                                 'User "{0}" success to login'.format(username))
                    auth_user = User()
                    auth_user.id = username
                    auth_user.role = 'tacacs'
                    auth_user.type = tac_status[1][1]
                    auth_user.user_type = tac_status[2][0]
                    auth_user.group = tac_status[2][1]
                    login_user(auth_user, remember=remember)

                    logged_in_users.append(username)
                    return jsonify({'status': 200, 'msg': 'auth successful',
                                    'data': {'username': username, 'role': 'TACACS+', 'type': tac_status[1][1],
                                             'userType': tac_status[2][0]}})
                else:
                    monitor_db.add_operation_log(username, '/login', 'login', 'error',
                                                 'username: {0} password:XXXXX'.format(username),
                                                 'User "{0}" failed to login'.format(username))
                    return jsonify(
                        {'status': 500, 'msg': 'Username or password is incorrect for TACACS+ authentication'})
            user = user_db.query_user(user=username)

            if not user:
                return jsonify({'status': 500, 'msg': 'Username or Password is incorrect'})

            if user.is_lock:
                monitor_db.add_operation_log(username, '/login', 'login', 'error',
                                             'username: {0} password:XXXXX'.format(username),
                                             'User "{0}" is locked'.format(username))
                return jsonify({'status': 500, 'msg': 'The User is locked, please contact Administrator'})

            if user.type == 'superuser' and monitor_db.record_failed_auth_attempts(username):
                return jsonify({'status': 500,
                                'msg': 'You have entered the wrong password 3 times in the last 5 minutes. Please try again later'})

            if user.check_password_hash(pwd):
                monitor_db.add_operation_log(username, '/login', 'login', 'success',
                                             'username: {0} password:XXXXX'.format(username),
                                             'User "{0}" success to login'.format(username))
                auth_user = User()
                auth_user.id = username
                auth_user.role = 'local'
                auth_user.type = user.type
                auth_user.user_type = user.user_type
                auth_user.group = ''
                login_user(auth_user, remember=remember)

                logged_in_users.append(username)
                return jsonify({'status': 200, 'msg': 'auth successful',
                                'data': {'username': username, 'role': 'LOCAL', 'type': user.type,
                                         'userType': user.user_type}})

            monitor_db.add_operation_log(username, '/login', 'login', 'error',
                                         'username: {0} password:XXXXX'.format(username),
                                         'User "{0}" failed to login'.format(username))

            attempts_tag = monitor_db.record_failed_auth_attempts(username)
            if attempts_tag:
                if user.type != 'superuser':
                    user_db.update_user_status(user=username, lock_status=1)
                    return jsonify({'status': 500,
                                    'msg': 'You have entered the wrong password 3 times in the last 5 minutes. User is Locked, please contact Administrator'})
                else:
                    return jsonify({'status': 500,
                                    'msg': 'You have entered the wrong password 3 times in the last 5 minutes. Please try again later'})
            return jsonify({'status': 500, 'msg': 'Username or Password is incorrect'})
        else:
            monitor_db.add_operation_log(username, '/login', 'login', 'error',
                                         'username: {0} password:XXXXX'.format(username),
                                         'User "{0}" failed to login'.format(username))
            return jsonify({'status': 500, 'msg': 'Username or Password is empty'})


@new_dashboard_mold.route('/logout', methods=['POST', 'GET'])
def login_out():
    if request.method == "GET":
        logged_in_users.remove(current_user.id)
        logout_user()
        return jsonify({"url": "/login"}), 302


@new_dashboard_mold.route("/check_status", methods=['POST'])
def check_status():
    return jsonify({"status": 200, "msg": "success",
                    "data": {'username': current_user.id, 'role': current_user.role, 'type': current_user.type,
                             'userType': current_user.user_type}})


@new_dashboard_mold.route('/all_switch_table/data', methods=['POST'])
def all_switch_table():
    all_switch_query = utils.query_switch()
    page_num, page_size, total_count, query_switch = utils.query_helper(Switch, pre_query=all_switch_query)
    return jsonify({"data": [switch.make_dict() for switch in query_switch], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_dashboard_mold.route('/all_switch_table_with_fabirc/data', methods=['POST'])
def all_switch_table_with_fabirc():
    data = request.get_json()
    filter_fields = data.get("filterFields", [])
    sort_fields = data.get("sortFields", [])

    all_switch_query = utils.query_switch(is_show_fabric=True)

    if filter_fields:
        new_filter_fields = []
        for field in filter_fields:
            if field["field"] == "fabric":
                for filter in field["filters"]:
                    all_switch_query = all_switch_query.filter(inventory.Fabric.fabric_name == filter["value"])
            elif field["field"] == "role":
                for filter in field["filters"]:
                    all_switch_query = all_switch_query.filter(dc_blueprint.DCFabricTopologyNode.type == filter["value"])
            else:
                new_filter_fields.append(field)
        data["filterFields"] = new_filter_fields

    if sort_fields:
        for field in sort_fields:
            if field["field"] == "fabric":
                if field["order"] == "asc":
                    all_switch_query = all_switch_query.order_by(asc(inventory.Fabric.fabric_name))
                elif field["order"] == "desc":
                    all_switch_query = all_switch_query.order_by(desc(inventory.Fabric.fabric_name))
                data["sortFields"] = []
            elif field["field"] == "role":
                if field["order"] == "asc":
                    all_switch_query = all_switch_query.order_by(asc(dc_blueprint.DCFabricTopologyNode.type))
                elif field["order"] == "desc":
                    all_switch_query = all_switch_query.order_by(desc(dc_blueprint.DCFabricTopologyNode.type))
                data["sortFields"] = []

    page_num, page_size, total_count, query_switch = utils.query_helper(Switch, pre_query=all_switch_query, data=data)
    res = []

    for switch, fabric, type in query_switch.all():
        info = switch.make_dict()
        info["fabric"] = fabric
        info["role"] = type
        res.append(info)

    return jsonify({"data": res, "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_dashboard_mold.route('/imported_and_provisioning_success_switch_table/data', methods=['POST'])
def imported_and_provisioning_success_switch_table():
    imported_and_provisioning_success_switch_query = utils.query_switch().filter(Switch.status.in_(['Provisioning Success', 'Imported']))
    page_num, page_size, total_count, query_switch = utils.query_helper(Switch, pre_query=imported_and_provisioning_success_switch_query)
    return jsonify({"data": [switch.make_dict() for switch in query_switch], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_dashboard_mold.route('/statistic', methods=['POST', 'GET'])
def get_statistic():
    db_session = inven_db.get_session()
    statistic = {}
    statistic.update({"sdn_access": db_session.query(SdnAccessSwitch).count()})
    statistic.update({"vtep_management": db_session.query(VtepControlSwitch).count()})
    system_config = inven_db.get_global_system_config()
    statistic.update({"license_portal_url": system_config.license_portal_url if system_config else ''})
    license_statistic = db_session.query(monitor.LicenseStatisttic).first()
    licenses = db_session.query(monitor.LicenseCount).all()

    total_remain = 0
    if license_statistic:
        total_remain = license_statistic.remain

    for license in licenses:
        total_remain += license.remain

    statistic.update({"license": total_remain})

    statistic.update({"cpu": float(utils.get_CPU_state()['cpu_percent'].strip("%"))})
    statistic.update({"mem": float(utils.get_memory_state()['sum_mem'].strip("%"))})
    statistic.update({"disk": float(utils.get_disk_satate()['sum_disk'].strip("%"))})

    return jsonify({'status': 200, 'data': statistic})


@new_dashboard_mold.route('/get_machine_history_info', methods=['POST', 'GET'])
def get_machine_history_info():
    db_session = inven_db.get_session()
    ma_info = db_session.query(inventory.MachineHistoryInfo).order_by(inventory.MachineHistoryInfo.create_time.desc()).limit(10).all()
    return jsonify({'status': 200, 'data': [info.make_dict() for info in ma_info]})


@new_dashboard_mold.route('/config_template', methods=['POST', 'GET'])
def get_config_template():
    db_session = inven_db.get_session()
    config_template = {}
    config_template.update({"global_file": db_session.query(inventory.SwitchAutoConfig).filter(
        inventory.SwitchAutoConfig.type == 'global').count()})
    config_template.update({"site_file": db_session.query(inventory.SwitchAutoConfig).filter(
        inventory.SwitchAutoConfig.type == 'site').count()})
    config_template.update({"retrieved_config": db_session.query(inventory.SwitchConfigBackup).count()})
    config_template.update({"configured_hardware": db_session.query(inventory.SwitchSystemInfo).filter(
        inventory.SwitchSystemInfo.up_to_date_onie_path != '').count()})
    config_template.update({"template_file": db_session.query(general.GeneralTemplate).count()})
    config_template.update(
        {"general_config": db_session.query(general.GeneralConfig).filter(general.GeneralConfig.content != '').count()})
    return jsonify({'status': 200, 'data': config_template})


@new_dashboard_mold.route('/deployment_task', methods=['POST', 'GET'])
def get_deployment_task():
    deployment_task = {}
    task_list = []
    running_jobs = AmpConBaseTask.get_running_jobs()
    if running_jobs:
        for task in running_jobs.slice(0, 6):
            task_list.append(
                [task.task_name, task.schedule_type, task.create_time.strftime("%Y-%m-%d %H:%M:%S"), task.task_status])
    deployment_task.update({"task": task_list})
    session = automation_db.get_session()
    deployment_task['playbook_count'] = session.query(Playbook.id).filter(Playbook.internal != 1).count()
    deployment_task['running_count'] = session.query(AnsibleJob.id).filter(AnsibleJob.status == 'RUNNING').count()
    deployment_task['executed_count'] = session.query(AnsibleJob.id).filter(AnsibleJob.status == 'EXECUTED').count()
    deployment_task['idle_count'] = session.query(AnsibleJob.id).filter(AnsibleJob.status == 'IDLE').count()

    return jsonify({'status': 200, 'data': deployment_task})


@new_dashboard_mold.route('/deployment_only_task', methods=['POST', 'GET'])
def get_deployment_only_task():
    task_list = []
    running_jobs = AmpConBaseTask.get_running_jobs()
    if running_jobs:
        for task in running_jobs.slice(0, 4):
            task_list.append(
                {"task_name": task.task_name, "type": task.schedule_type, "start_time": task.create_time.strftime("%Y-%m-%d %H:%M:%S"), "status": task.task_status})
    if len(task_list) == 0:
        task_list.append({"task_name": "No tasks running", "type": "N/A", "start_time": "N/A", "status": "N/A"})

    return jsonify({'status': 200, 'data': task_list})


@new_dashboard_mold.route('/switch_status', methods=['POST', 'GET'])
def get_switch_status():
    echar_res = {
        "configured": 0,
        "decom": 0,
        "deployed": 0,
        "imported": 0,
        "parking": 0,
        "staged": 0,
        "provisioning": 0,
        "deployfailed": 0
    }
    db_session = inven_db.get_session()
    switch_id_list = list(map(lambda x: x.id, utils.query_switch()))
    query_res = db_session.query(inventory.Switch.status, func.count(inventory.Switch.status).label("count")).filter(
        inventory.Switch.id.in_(switch_id_list)).group_by(
        inventory.Switch.status).all()
    # enum('Init','Registered Not-staged','Configured','Staged','Registered','Provisioning Success',
    # 'Provisioning Failed','Imported','DECOM','DECOM-Init','DECOM-Pending','DECOM-Manual','RMA')
    for row in query_res:
        if row.status == "Provisioning Success":
            echar_res["deployed"] = row.count
        elif row.status == "Imported":
            echar_res["imported"] = row.count
        elif row.status == "Staged":
            echar_res["staged"] = row.count
        elif row.status == "Registered":
            echar_res["configured"] += row.count
            echar_res["provisioning"] += row.count
        elif row.status in ["Configured", "Registered Not-staged"]:
            echar_res["configured"] += row.count
        elif row.status == "Provisioning Failed":
            echar_res["provisioning"] += row.count
            echar_res["deployfailed"] = row.count
        elif row.status in ["DECOM", "DECOM-Init", "DECOM-Pending", "DECOM-Manual", "RMA"]:
            echar_res["decom"] += row.count

    echar_res["parking"] = db_session.query(inventory.SwitchParking).count()
    return jsonify({'status': 200, 'data': echar_res})


@new_dashboard_mold.route('/platform_model', methods=['POST', 'GET'])
def get_platform_model():
    db_session = inven_db.get_session()
    switch_id_list = list(map(lambda x: x.id, utils.query_switch()))
    echar_model = db_session.query(inventory.Switch.platform_model,
                                   func.count(inventory.Switch.platform_model).label("count")).group_by(
        inventory.Switch.platform_model).filter(inventory.Switch.id.in_(switch_id_list)).all()
    echar_model = [{"value": row.count, "name": row.platform_model} for row in echar_model if
                   row.platform_model is not None]
    return jsonify({'status': 200, 'data': echar_model})


@new_dashboard_mold.route('/license/portal/status')
def license_portal_status():
    db_session = inven_db.get_session()

    license_statistic = db_session.query(monitor.LicenseStatisttic).first()
    licenses = db_session.query(monitor.LicenseCount).all()

    total_remain, total = 0, 0
    if license_statistic:
        total_remain = license_statistic.remain
        total = license_statistic.total

    for license in licenses:
        total_remain += license.remain
        total += license.total

    res = [
        {"value": total - total_remain, "name": 'Used'},
        {"value": total_remain, "name": 'Unused'}
    ]
    return jsonify({'status': 200, 'data': res})


@new_dashboard_mold.route('/switch/license/expiring')
def license_expiring():
    now_month = datetime.date.today()
    end_month = pure_utils.date_offset_by_month(now_month, 6)
    reses = utils.get_last_license_expired(now_month, end_month)
    db_month_c = dict(reses)
    show_months = [pure_utils.date_offset_by_month(now_month, i) for i in range(6)]
    keys = []
    values = []
    for show_month in show_months:
        m_str = show_month.strftime('%Y-%m')
        keys.append(show_month.strftime('%b'))
        values.append(db_month_c.get(m_str, 0))
    return jsonify({'keys': keys, 'values': values})


@new_dashboard_mold.route('/host/access/vlan/statics')
def host_access_statics():
    statics = sdn_access_db.get_vlan_statics()
    json_data = []
    for static in statics:
        json_data.append({'name': 'vlan_' + str(static.assigned_vlan_id), 'value': static.count})
    return jsonify({'status': 200, 'data': json_data})


@new_dashboard_mold.route('/virtual/statics')
def virtual_statics():
    statics = vtep_db.get_vxlan_statics()
    json_data = []
    for static in statics:
        json_data.append({'name': 'vxlan_' + static.vni, 'value': static.count})
    return jsonify({'status': 200, 'data': json_data})


@new_dashboard_mold.route('/switch/<string:sn>/stage', methods=['GET'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='enable_switch', contents='staged deploying switch {sn}')
def stage_switch(sn):
    try:
        db_switch = inven_db.get_model(inventory.Switch, filters={'sn': [sn]})
        if db_switch:
            db_switch.enable = True
            db_switch.status = 'Staged'
            inven_db.merge(db_switch)
            msg = {'status': 200, 'info': 'The %s staged' % sn}
            if cfg.CONF.vpn_enable:
                from server.vpn.vpn_utils import create_vpn_client
                create_vpn_client(sn)
        else:
            msg = {'status': 500, 'info': 'The %s switch not exist' % sn}
    except Exception as e:
        LOG.exception(e)
        msg = {'status': 500, 'info': 'The %s staged error' % sn}
    finally:
        return jsonify(msg)


@new_dashboard_mold.route('/switch/<string:sn>/unStage', methods=['GET'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='unstage_switch', contents='unstaged deploying switch {sn}')
def unstage_switch(sn):
    msg, flag = inven_db.unstage_switch(sn)
    if flag:
        return {'status': 200, 'info': 'The %s staged success， %s' % (sn, msg)}

    return {'status': 500, 'info': 'The %s staged error， %s' % (sn, msg)}


@new_dashboard_mold.route('/display_conf/<string:sn>')
def display_conf(sn):
    try:
        db_session = inven_db.get_session()
        if sn.endswith('.rma'):
            sn = sn.replace('.rma', '')
            config = inven_db.get_switch_back_sn(sn, session=db_session)
            return config or 'No RMA config'

        else:
            switch = db_session.query(inventory.Switch).filter(inventory.Switch.sn == sn).first()
            if switch.configs or switch.general_configs:
                global_config = regional_config = site_config = switch_config = ''
                for switch_config in switch.configs:
                    if switch_config.type == 'global':
                        global_config = switch_config.config
                    elif switch_config.type == 'site':
                        site_config = switch_config.config
                config = global_config + '\n' + site_config
                for general_config in switch.general_configs:
                    config += general_config.content
                config = key_reg.sub('key ***', config)
                config = password_reg.sub('password ***', config)
                return config
            else:
                config = '%s No Switch Config' % sn
                return config
    except Exception as e:
        config = '%s No Switch Config' % sn
        return config


@new_dashboard_mold.route('/switch/<string:sn>/agent_conf')
def display_agent_conf(sn):
    conf_str = inven_db.get_switch_agent_conf(sn)
    return conf_str if conf_str else 'no agent conf'


@new_dashboard_mold.route('/display_log/<string:sn>')
def display_log(sn):
    switch_logs = inven_db.get_switch_logs(sn)
    switch_log = ''
    if switch_logs:
        for switch in switch_logs:
            switch_log += str(switch.create_time) + ': ' + switch.content + '\n'
    switch_log = str_helper.mask_key_configuration(switch_log)
    return switch_log


@new_dashboard_mold.route('/report/<string:sn>')
def report(sn):
    db_session = inven_db.get_session()
    switch_logs = db_session.query(
        inventory.SwitchLog).filter(inventory.SwitchLog.switch_id == sn, inventory.SwitchLog.level == 'info').order_by(
        'create_time').all()

    for i, switch_log in enumerate(reversed(switch_logs)):
        if ':::::Register Start::::::register the switch begin' in switch_log.content:
            j = -(i + 1)
            switch_logs = switch_logs[j:]

    switch_log = ''
    if switch_logs:
        for switch in switch_logs:
            switch_log += str(switch.create_time) + ': ' + switch.content + '\n'

    return switch_log


@new_dashboard_mold.route('/del_switch', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='delete_deploying_switch', contents='delete deploying switch {sn}')
def del_switch():
    req_json = request.get_json()
    sn = req_json.get('sn')
    msg = None
    try:
        db_session = inven_db.get_session()
        db_session.begin()
        switch = db_session.query(inventory.Switch).filter(inventory.Switch.sn == sn).first()
        topo_node = db_session.query(DCFabricTopologyNode).filter(DCFabricTopologyNode.switch_sn == sn).first()

        if topo_node:
            msg = {'status': 400, 'info': 'The %s switch is uesd by fabric, cannot delete before release!' % sn}
        elif switch:
            allow_config_list = ['global', 'regional']
            for config in switch.configs:
                if config.type not in allow_config_list:
                    db_session.delete(config)

            if AmpConBaseTask.get_running_job_by_sn(sn):
                AmpConBaseTask.kill_process_by_sn(sn)

            db_session.delete(switch)
            db_session.commit()
            msg = {'status': 200, 'info': 'The %s switch is removed success!' % sn}

            db_session.query(inventory.SwitchConfigBackup).filter(inventory.SwitchConfigBackup.sn == sn).delete()
            db_session.query(inventory.DeployedSecuritySwitch).filter(inventory.DeployedSecuritySwitch.sn == sn).delete()
            db_session.query(monitor.DDMEvents).filter(monitor.DDMEvents.switch_sn == sn).delete()
        else:
            msg = {'status': 400, 'info': 'The %s switch is not exit' % sn}
    except Exception as e:
        LOG.exception(e)
        msg = {'status': 500, 'info': 'The %s switch del error' % sn}
    finally:
        return jsonify(msg)


### license相关
@new_dashboard_mold.route('/get_local_key', methods=['GET'])
def get_local_key():
    try:
        # 获取product_uuid
        if os.path.exists('/usr/share/uuid'):
            output = subprocess.check_output(['cat', '/usr/share/uuid'], universal_newlines=True)
            product_uuid = output.strip()
        else:
            output = subprocess.check_output(['cat', '/sys/class/dmi/id/product_uuid'], universal_newlines=True)
            product_uuid = output.strip()

        # 获取mac
        output = subprocess.check_output(['cat', './mac_address'], universal_newlines=True)
        mac_str = output.strip()

        if mac_str and product_uuid:
            local_key = {
                "device_id": product_uuid,
                "system_mac": mac_str
            }
            # print(f"local_key: {local_key}")
            encrypt_local_key = licensechecker.encrypt(json.dumps(local_key).encode('utf-8')).decode()
            # print(f"encrypt_local_key: {encrypt_local_key}")
            return jsonify({"status": 200, "localKey": encrypt_local_key})
        else:
            return jsonify({"status": 400, "msg": "get system info failed"})

    except subprocess.CalledProcessError as e:
        # 处理命令执行失败的情况
        print(f"Error: {e}")
        return jsonify({"status": 400, "msg": "get system info failed"})


@new_dashboard_mold.route('/install_lic', methods=['POST'])
def install_license():
    try:
        if 'licensekey' in request.files:
            file = request.files['licensekey']
            encrypt_data = file.read()
        else:
            req_data = request.get_json()
            encrypt_data = req_data.get("data", "")
            if not encrypt_data:
                return {"msg": "Invalid input", "status": 400}, 200
    except Exception as e:
        return {"msg": "Invalid input", "status": 400}, 200

    res = licensechecker.install_license(encrypt_data)

    # 记录操作日志
    try:
        if current_user.is_authenticated:
            user = current_user.id
        else:
            user = "Anonymous"
        status = "success" if res["status"] == 200 else "error"
        monitor_db.add_operation_log(user, "/install_lic", "import license", status, params=res["licenseFile"], content=res["msg"])
    except Exception as e:
        LOG.exception(e)

    return res, 200


@new_dashboard_mold.route('/get_lic', methods=['GET'])
def get_license_info():
    res = licensechecker.get_license_info()
    return res, 200


@new_dashboard_mold.route('/check_lic', methods=['GET'])
def check_license_info():
    res = licensechecker.check_license()
    return res, 200


@new_dashboard_mold.route('/check_hwid', methods=['POST'])
def check_hwid():
    req_data = request.get_json()
    hwid_list = req_data.get("hwids", [])
    res = licensechecker.check_hwid(hwid_list)
    return res, 200


@new_dashboard_mold.route('/invalidate_lic', methods=['POST'])
def invalidate_license():
    req_data = request.get_json()
    hwids = req_data.get("hwids", [])
    license_id = req_data.get("licenseId", "")
    if not hwids or not license_id:
        return {"msg": "Invalid input", "status": 400}, 200
    res = licensechecker.invalidate_hwids(license_id, hwids)

    try:
        user = current_user.id
        status = "success" if res["status"] == 200 else "error"
        monitor_db.add_operation_log(user, "/install_lic", "invalidate license", status, params=res["licenseFile"], content=res["msg"])
    except Exception as e:
        LOG.exception(e)

    return res, 200


@new_dashboard_mold.route('/get_lic_log', methods=['POST'])
def get_license_log():
    data = request.get_json()
    sort_fields = data.get("sortFields", [])
    if sort_fields:
        for field in sort_fields:
            if field["field"] == "params":
                field["field"] = "params_original"
    db_session = monitor_db.get_session()
    job_query = db_session.query(OperationLog).filter(OperationLog.method.in_(['import license', 'invalidate license']))
    page_num, page_size, total_count, query_log_ret = utils.query_helper(OperationLog, pre_query=job_query)
    return jsonify({"data": [log.make_dict() for log in query_log_ret], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_dashboard_mold.route('/del_switch_with_check', methods=['POST'])
def del_switch_with_check():
    req_json = request.get_json()
    hwid = req_json.get('hwid')
    license = req_json.get('license')

    try:
        lic_data = json.loads(licensechecker.decrypt(license))
    except Exception as e:
        LOG.exception(e)
        info = {"status": 400, "msg": "license decrypt failed", "licenseFile": ""}
        return info

    lic_details = lic_data.get("details")
    if hwid not in lic_details:
        try:
            db_session = inven_db.get_session()

            switchs = db_session.query(inventory.Switch).filter(inventory.Switch.hwid == hwid).all()

            if switchs:
                for switch in switchs:
                    db_session.begin()
                    allow_config_list = ['global', 'regional']
                    for config in switch.configs:
                        if config.type not in allow_config_list:
                            db_session.delete(config)

                    if AmpConBaseTask.get_running_job_by_sn(switch.sn):
                        AmpConBaseTask.kill_process_by_sn(switch.sn)

                    db_session.delete(switch)
                    db_session.query(inventory.SwitchConfigBackup).filter(inventory.SwitchConfigBackup.sn == switch.sn).delete()
                    db_session.query(inventory.DeployedSecuritySwitch).filter(inventory.DeployedSecuritySwitch.sn == switch.sn).delete()
                    db_session.commit()

                msg = {'status': 200, 'info': 'The %s switch is removed success!' % hwid}
            else:
                msg = {'status': 400, 'info': 'The %s switch is not exit' % hwid}
        except Exception as e:
            LOG.exception(e)
            msg = {'status': 400, 'info': 'The %s switch del error' % hwid}
    else:
        msg = {'status': 400, 'info': 'The %s switch in license' % hwid}
    return jsonify(msg)


@new_dashboard_mold.route('/get_snmp_view_data', methods=['POST'])
def get_snmp_view_data():
    page_num, page_size, total_count, query_ojb = utils.query_helper(SnmpDevice)
    return jsonify({"data": [snmp.make_dict() for snmp in query_ojb], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_dashboard_mold.route('/remove_snmp_view_data', methods=['POST'])
@admin_permission.require(http_exception=403)
def remove_snmp_view_data():
    try:
        info = request.get_json()
        snmp_id = info.get('snmpId')

        session = inven_db.get_session()
        snmp_device = session.query(SnmpDevice).filter(
            SnmpDevice.id == snmp_id).first()
        if not snmp_device:
            return jsonify({"status": 400, 'info': 'The snmp device is not exists!'})

        try:
            with session.begin(subtransactions=True):
                session.delete(snmp_device)
        except Exception as db_error:
            LOG.error(traceback.format_exc())
            session.rollback()
            return jsonify({"status": 500, 'info': 'Failed to remove the snmp device!'})

        result = {"status": 200, 'info': 'The snmp device is removed success!'}
    except Exception as e:
        result = {"status": 500, 'info': str(e)}
    return jsonify(result)


@new_dashboard_mold.route('/update_snmp_view_data', methods=['POST'])
async def update_snmp_view_data():
    data = request.json
    LOG.info(f"Received SNMP configuration import request: {data}")

    session = inven_db.get_session()
    # 创建导入详情记录
    import_detail = SnmpImportDetail(
        mgt_ip=data.get("ip", "Unknown IP"),
        status='failed'  # 默认为失败状态
    )

    try:
        session.add(import_detail)
        session.flush()

        try:
            config = snmp_util.build_snmp_config(data)
            full_config = await snmp_util.fetch_snmp_device_info(config)
        except Exception as e:
            LOG.error(f"SNMP operation failed: {str(e)}")
            return jsonify({
                "status": 400,
                "info": "Import failed, please check if device configuration matches input."
            })

        try:
            with session.begin():
                existing_device = session.query(SnmpDevice).filter(SnmpDevice.mgt_ip == full_config['mgt_ip']).first()

                if existing_device:
                    # 更新现有记录
                    for key, value in full_config.items():
                        if hasattr(existing_device, key):
                            setattr(existing_device, key, value)
                    existing_device.reachable_status = 0
                else:
                    # 新增记录
                    new_device = SnmpDevice(**full_config)
                    new_device.reachable_status = 0
                    session.add(new_device)

                # 更新导入详情状态为成功
                import_detail.status = 'success'

            return jsonify({
                "status": 200,
                "info": "Import success!",
            })

        except Exception as e:
            LOG.error(f"Error occurred while processing SNMP configuration import: {str(e)}")
            return jsonify({
                "status": 500,
                "info": f"Database operation failed: {str(e)}"
            })
    except Exception as e:
        LOG.error(f"Unexpected error during SNMP configuration import: {str(e)}")
        return jsonify({
            "status": 500,
            "info": str(e)
        })


@new_dashboard_mold.route('/batch_update_snmp_view_data', methods=['POST'])
async def batch_update_snmp_view_data():
    file = request.files['file']

    try:
        # 解析JSON
        file_content = file.read()
        configs = json.loads(file_content)

        if not isinstance(configs, list):
            raise ValueError("JSON content must be an array")

        if len(configs) == 0:
            raise ValueError("JSON array cannot be empty")

        LOG.info(f"Received batch import request, file: {file.filename}, containing {len(configs)} configurations")
    except json.JSONDecodeError as e:
        return jsonify({
            "status": 400,
            "info": f"JSON file format error: {str(e)}",
        })
    except Exception as e:
        return jsonify({
            "status": 400,
            "info": f"File parsing failed: {str(e)}",
        })

    # 预处理配置项
    valid_configs = []
    config_indices = []
    import_details = []
    valid_to_import_mapping = {}

    db_session = inven_db.get_session()
    with db_session.begin():
        for idx, item in enumerate(configs):
            import_detail = SnmpImportDetail(
                mgt_ip=item.get("ip", "Unknown IP"),
                status='pending'
            )

            try:
                valid_config = snmp_util.build_snmp_config(item)
                valid_configs.append(valid_config)
                valid_to_import_mapping[len(valid_configs) - 1] = import_detail
                config_indices.append(idx)
            except Exception:
                import_detail.status = 'failed'

            import_details.append(import_detail)

        db_session.add_all(import_details)
        db_session.flush()

    # 保存valid config对应的detail id
    valid_to_detail_id_mapping = {
        valid_idx: import_detail.id
        for valid_idx, import_detail in valid_to_import_mapping.items()
    }

    handle_batch_update_snmp_view_data.delay(valid_configs, config_indices, valid_to_detail_id_mapping)

    return jsonify({
        "status": 200,
        "info": "Batch import request submitted",
    })


@my_celery_app.task(name="handle_batch_update_snmp_view_data", base=AmpConBaseTask)
def handle_batch_update_snmp_view_data(valid_configs, config_indices, valid_to_detail_id_mapping):
    """
    处理批量SNMP设备信息更新任务
    """
    db_session = inven_db.get_session()
    try:
        # 更新detail状态为running
        with db_session.begin():
            running_ids = list(valid_to_detail_id_mapping.values())
            if running_ids:
                LOG.info("set import detail status to running")
                db_session.query(SnmpImportDetail).filter(
                    SnmpImportDetail.id.in_(running_ids)
                ).update({SnmpImportDetail.status: 'running'}, synchronize_session=False)
                LOG.info("set import detail status to running finish")

        # 执行SNMP操作
        if not valid_configs:
            LOG.info("No valid configs to process")
            return

        snmp_results = []
        for idx, _cfg in enumerate(valid_configs):
            import_detail_id = valid_to_detail_id_mapping[str(idx)]
            try:
                result = asyncio.run(snmp_util.fetch_snmp_device_info(_cfg))
                snmp_results.append(result)
            except Exception as e:
                snmp_results.append(e)
                # 立即更新detail为failed状态
                with db_session.begin():
                    db_session.query(SnmpImportDetail).filter(
                        SnmpImportDetail.id == import_detail_id
                    ).update({
                        SnmpImportDetail.status: 'failed',
                    })

        # 处理SNMP结果并执行数据库操作
        for i, res in enumerate(snmp_results):
            cfg_idx = config_indices[i]
            cfg = valid_configs[i]
            mgt_ip = cfg.get("mgt_ip", "Unknown IP")
            import_detail_id = valid_to_detail_id_mapping[str(i)]

            if isinstance(res, Exception):
                # SNMP导入交换机失败
                continue

            try:
                with db_session.begin():
                    existing_device = db_session.query(SnmpDevice).filter(
                        SnmpDevice.mgt_ip == mgt_ip
                    ).first()

                    if existing_device:
                        for key, value in res.items():
                            if hasattr(existing_device, key):
                                setattr(existing_device, key, value)
                        existing_device.reachable_status = 0
                    else:
                        new_device = SnmpDevice(**res)
                        new_device.reachable_status = 0
                        db_session.add(new_device)

                    db_session.query(SnmpImportDetail).filter(
                        SnmpImportDetail.id == import_detail_id
                    ).update({
                        SnmpImportDetail.status: 'success'
                    })
                LOG.info(f"Configuration {cfg_idx} (IP: {mgt_ip}) processed successfully")

            except Exception as e:
                db_session.rollback()
                with db_session.begin():
                    db_session.query(SnmpImportDetail).filter(
                        SnmpImportDetail.id == import_detail_id
                    ).update({
                        SnmpImportDetail.status: 'failed'
                    })
                LOG.error(f"Configuration {cfg_idx} (IP: {mgt_ip}) database operation failed: {str(e)}")
    except Exception as e:
        with db_session.begin():
            running_ids = list(valid_to_detail_id_mapping.values())
            db_session.query(SnmpImportDetail).filter(
                SnmpImportDetail.id.in_(running_ids),
                SnmpImportDetail.status == 'running'
            ).update({
                SnmpImportDetail.status: 'failed',
            }, synchronize_session=False)


@new_dashboard_mold.route('/get_snmp_import_details', methods=['POST'])
def get_snmp_import_details():
    db_session = inven_db.get_session()
    pre_query = db_session.query(SnmpImportDetail)
    page_num, page_size, total_count, q1 = utils.query_helper(SnmpImportDetail, pre_query=pre_query, default_order_by_func=lambda model: desc(getattr(model, "create_time")))

    data = [{
        "id": s.id,
        "create_time": s.create_time,
        "mgt_ip": s.mgt_ip,
        "status": s.status
    } for s in q1]

    return jsonify({"status": 200, "data": data, "total": total_count, "page": page_num, "pageSize": page_size})
