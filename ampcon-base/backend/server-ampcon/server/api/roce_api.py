import json
import logging
import traceback
import threading
from flask import Blueprint, jsonify, Response, request
from datetime import timedelta, datetime, date
from jinja2 import Environment, FileSystemLoader
import os
from sqlalchemy.orm import aliased
import copy
from sqlalchemy import func, Text, or_, text
import re
from functools import wraps
from concurrent.futures import ThreadPoolExecutor, as_completed

from server.util.permission import admin_permission, readonly_permission
from server.db.models.campus_blueprint import campus_blueprint_db, CampusTopologyConfig
from server.db.models import inventory
from server.db.models.inventory import inven_db
from server.db.models.roce import RoceDBCommon, PfcConfiguration, RoceEasyDeployConfiguration, DLBConfiguration, \
    PfcWdConfiguration, EcnConfiguration, EcnConfigurationDetail, QosConfiguration, QosIngressConfiguration, \
        QosEgressConfiguration, PfcBufferIngressConfiguration, PfcBufferEgressConfiguration, roce_db
from server.db.models.automation import AnsibleDevice, RoceTask
from ansible_lib.ansible_utils import execute_roce_script, roce_start_task_ansible
from server.util.prometheus_util import query_lldp_state
from celery_app import my_celery_app
from server.celery_app.roce_task import roce_check_task
from server.util import utils
from server.config_distribution import config_deploy
from server.db.models.inventory import Switch
from server.db.models.inventory import AssociationFabric, Fabric
from celery_app.config_distribution_task import config_distribution_roce

roce_mold = Blueprint("roce", __name__, template_folder="templates")
LOG = logging.getLogger(__name__)

@roce_mold.route("/server/configure", methods=["POST"])
@admin_permission.require(http_exception=403)
def server_configure():
    """
    {
        "portInfo": {
            "device_id": ["port_name", ]
        }
        "device_type": "nvidia",
        "script_params": {
            "pfc_param": "0,0,0,1,0,0,0,0",
            "dscp_param": "30,3",
            "ecn_np_param": "3",
            "ecn_rp_param": "3",
            "roce_np": "48",
            "mtu": "4096",
            "cma_roce_tos": "120",
            "traffic_class": "120"
        }
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"info": "Invalid request data", "status": 400})

        port_info = data.get("portInfo", {})
        script_params = data.get("script_params")
        device_type = data.get("device_type", "nvidia")

        # 设置模板目录
        template_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "monitor", "roce")
        env = Environment(loader=FileSystemLoader(template_dir))

        if device_type == "nvidia":
            # 获取模板
            template = env.get_template("nvidia_configure.j2")

            # 渲染模板，添加脚本头
            script_content = template.render(**script_params)

        elif device_type == "broadcom":
            # 获取模板
            template = env.get_template("broadcom_configure.j2")

            # 渲染模板，添加脚本头
            script_content = template.render(**script_params)

        session = inven_db.get_session()
        available_device_ids = list(map(lambda x: x.id, utils.query_host().all()))
        for device_id, port in port_info.items():
            # 获取设备信息
            device = session.query(AnsibleDevice).filter(AnsibleDevice.id == device_id, 
                                                         AnsibleDevice.id.in_(available_device_ids)).first()
            if not device:
                continue
            port_str= ' '.join(port)
            shell_env = ""
            if device_type == "nvidia":
                shell_env = f"ibdev='{port_str}'"
            elif device_type == "broadcom":
                shell_env = f"ethdev='{port_str}'"
            # 执行脚本
            my_celery_app.send_task("execute_roce_script",
                                    kwargs={
                                        "device_id": device.id,
                                        "type": "RoCE Script",
                                        "scripts": script_content,
                                        "task_name": f"Roce_Script:{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}:{device.device_name}",
                                        "shell_env": shell_env
                                    })

        return jsonify({"info": "Task submitted successfully", "status": 200})

    except Exception as e:
        LOG.error(f"Error in server_configure: {str(e)}")
        LOG.error(traceback.format_exc())
        return jsonify({"info": "Task submitted failed", "status": 400})


@roce_mold.route("/template/preview", methods=["POST"])
@admin_permission.require(http_exception=403)
def template_preview():
    """
    {
        "sever_type": "nvida"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return Response("Invalid request data", status=400)

        # 设置模板目录
        template_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "monitor", "roce")

        server_type = data.get("server_type")
        if server_type == "nvidia":
            script_params = {
                "pfc_param": "0,0,0,1,0,0,0,0",
                "dscp_param": "30,3",
                "cnp_802p_prio": "3",
                "roce_np": "48"
            }

            validation_rules = {
                "cnp_802p_prio": {"min": 0, "max": 7},
                "roce_np": {"min": 0, "max": 63},
                "dscp_param": {"pattern": r"^(6[0-3]|[0-5]?\d),(7|[0-6])$"},
                "pfc_param" : {"pattern": r"^\s*(0|1)(?:\s*,\s*(0|1)){7}\s*$"}
            }

            with open(os.path.join(template_dir, "nvidia_configure.j2"), 'r') as file:
                template_text = file.read()

            return jsonify({"data": {"template": template_text, "default_param": script_params, "validation_rules": validation_rules}, "status": 200})


        elif server_type == "broadcom":
            script_params = {
                "m_param": "3",
                "s_param": "26",
                "p_param": "48",
                "r_param": "3",
                "c_param": "7"
            }

            validation_rules = {
                "m_param": {"min": 1, "max": 3},
                "s_param": {"min": 0, "max": 63},
                "p_param": {"min": 0, "max": 63},
                "r_param": {"min": 0, "max": 7},
                "c_param": {"min": 0, "max": 7}
            }

            with open(os.path.join(template_dir, "broadcom_configure.j2"), 'r') as file:
                template_text = file.read()

            return jsonify({"data": {"template": template_text, "default_param": script_params,
                                     "validation_rules": validation_rules}, "status": 200})

        else:
            return Response("Unsupported server type", status=400)

    except Exception as e:
        LOG.error(f"Error in server_configure: {str(e)}")
        LOG.error(traceback.format_exc())
        return Response(f"Internal server error: {str(e)}", status=500)


@roce_mold.route("/server/roce_check_api", methods=["GET"])
@admin_permission.require(http_exception=403)
def roce_check_api():
    db_session = inven_db.get_session()
    try:
        id = request.args.get('id')
        ansibleDevice = db_session.query(AnsibleDevice).filter(AnsibleDevice.id == id).first()
        if ansibleDevice:
            info_dict = {
                "device_id": ansibleDevice.id,
                "device_ip": ansibleDevice.ip,
                "device_name": ansibleDevice.device_name,
                "device_user": ansibleDevice.device_user,
                "device_pwd": ansibleDevice.device_pwd,
                "task_name": f"check_{ansibleDevice.device_name}_{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                "type": "check",
                "status": "RUNNING",
                "result": ""
            }
            roce_check_task.delay(**info_dict)
            return jsonify({
                "code": 200,
                'msg': 'success',
                "data": None
            })
        else:
            return jsonify({
                "code": 404,
                'msg': 'No ansible_device record corresponding to this ID was found',
                "data": None
            })
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        db_session.close()


@roce_mold.route("/event/get_roce_task_table", methods=["POST"])
@readonly_permission.require(http_exception=403)
def get_roce_task_table():
    db_session = inven_db.get_session()
    available_device_ids = list(map(lambda x: x.id, utils.query_host().all()))
    roce_task = db_session.query(RoceTask).filter(RoceTask.type != "check", RoceTask.device_id.in_(available_device_ids))
    page_num, page_size, total_count, query_task = utils.query_helper(RoceTask, pre_query=roce_task)
    res = []
    for task in query_task:
        info = {
            "id": task.id,
            "device_ip": task.device.ip,
            "device_name": task.device.device_name,
            "device_user": task.device.device_user,
            "type": task.type,
            "status": task.status,
            "create_time": task.create_time.strftime("%Y-%m-%d %H:%M:%S")
        }
        res.append(info)
    return jsonify({"data": res, "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@roce_mold.route("/event/get_result", methods=["POST"])
@readonly_permission.require(http_exception=403)
def get_result():
    try:
        data = request.get_json()
        if not data:
            return jsonify({"info": "Invalid request data", "status": 400})

        task_id = data.get("task_id")
        session = inven_db.get_session()
        available_device_ids = list(map(lambda x: x.id, utils.query_host().all()))
        task = session.query(RoceTask).filter(RoceTask.id == task_id, RoceTask.device_id.in_(available_device_ids)).first()
        if task:
            return jsonify({"data": task.result, "status": 200})
        else:
            return jsonify({"info": "task result not found", "status": 400})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({"info": "get task result failed", "status": 500})


@roce_mold.route("/server/roce_batch_check_api", methods=["POST"])
@admin_permission.require(http_exception=403)
def roce_batch_check_api():
    db_session = inven_db.get_session()
    try:
        request_json = request.get_json()
        id_list = request_json.get("id_list")
        check_config_list = request_json.get("check_config")
        if id_list:
            for id in id_list:
                ansibleDevice = db_session.query(AnsibleDevice).filter(AnsibleDevice.id == id).first()
                if ansibleDevice:
                    info_dict = {
                        "device_id": ansibleDevice.id,
                        "device_ip": ansibleDevice.ip,
                        "device_name": ansibleDevice.device_name,
                        "device_user": ansibleDevice.device_user,
                        "device_pwd": ansibleDevice.device_pwd,
                        "task_name": f"check_{ansibleDevice.device_name}_{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                        "type": "check",
                        "status": "RUNNING",
                        "result": ""
                    }
                    if "all" in check_config_list:
                        roce_check_task.delay(**info_dict)
                        # TODO: check configuration 检查项预留
                    else:
                        if "roce" in check_config_list:
                            roce_check_task.delay(**info_dict)
                        if "other" in check_config_list:
                            pass
            return jsonify({
                "code": 200,
                'msg': 'success',
                "data": None
            })
        else:
            return jsonify({
                "code": 404,
                'msg': 'id_list is empty.',
                "data": None
            })
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        db_session.close()

@roce_mold.route("/server/get_roce_check_result", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_roce_check_result():
    db_session = inven_db.get_session()
    try:
        id = request.args.get("id")
        task = (db_session.query(RoceTask)
                .filter(RoceTask.device_id == id, RoceTask.type == "check")
                .order_by(RoceTask.create_time.desc())
                .first())
        if task:
            return jsonify({
                "status": 200,
                "info": 'success',
                "data": task.create_time.strftime("%Y-%m-%d %H:%M:%S") + "\n" + task.result
            })
        else:
            return jsonify({
                "status": 400,
                "info": "task result not found.",
                "data": None
            })
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({"info": "get check result failed", "status": 500})
    finally:
        db_session.close()


from flask_login import current_user

@roce_mold.route("/server/configuration_by_form", methods=["POST"])
@admin_permission.require(http_exception=403)
def roce_configuration_by_form():
    db_session = inven_db.get_session()
    try:
        data = request.get_json()
        if not data:
            return jsonify({"info": "Invalid request data", "status": 400})

        port_list = data.get("nic_ports", [])
        port_info = {}
        for item in port_list:
            prefix, port = item.split('-', 1)
            port_info.setdefault(prefix, []).append(port)

        script_params = data.get("script_params")
        device_type = data.get("nic_vendor", "")
        create_user = current_user.id

        dispatched_devices = []

        for device_id, ports in port_info.items():
            ansibleDevice = db_session.query(AnsibleDevice).filter(AnsibleDevice.id == device_id).first()
            if not ansibleDevice:
                continue

            task_name = f"RoCEv2_Config_for_{ansibleDevice.device_name}_{datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}"

            info_dict = {
                "device_id": ansibleDevice.id,
                "device_ip": ansibleDevice.ip,
                "device_name": ansibleDevice.device_name,
                "device_user": ansibleDevice.device_user,
                "device_pwd": ansibleDevice.device_pwd,
                "task_name": task_name,
                "type": "RoCE Form",
                "status": "RUNNING",
                "result": "",
                "device_ssh_key_path": ansibleDevice.device_ssh_key_path,
                "device_port": ansibleDevice.device_port,
                "device_sudo_pass": ansibleDevice.device_pwd,
                "create_user": create_user,
                "target_ports": ports,
                "device_type": device_type,
                "script_params": script_params
            }
            task_result = roce_start_task_ansible.apply_async(kwargs=info_dict)
            dispatched_devices.append(ansibleDevice.device_name)

        if dispatched_devices:
            return jsonify({
                "status": 200,
                "info": f"The RoCEv2 Configration execute success in background on {', '.join(dispatched_devices)}",
                "job_name": info_dict.get("task_name")
            })
        else:
            return jsonify({
                "status": 404,
                "info": "No valid device found to RoCEv2 Configration.",
            })

    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        db_session.close()

@roce_mold.route("/server/roce_batch_check_ansible_api", methods=["POST"])
@admin_permission.require(http_exception=403)
def roce_batch_check_ansible_api():
    db_session = inven_db.get_session()
    try:
        request_json = request.get_json()
        id_list = request_json.get("id_list")
        check_config_list = request_json.get("check_config")
        create_user = current_user.id
        if id_list:
            for id in id_list:
                ansibleDevice = db_session.query(AnsibleDevice).filter(AnsibleDevice.id == id).first()
                if ansibleDevice:
                    info_dict = {
                        "device_id": ansibleDevice.id,
                        "device_ip": ansibleDevice.ip,
                        "device_name": ansibleDevice.device_name,
                        "device_user": ansibleDevice.device_user,
                        "device_pwd": ansibleDevice.device_pwd,
                        "task_name": f"check_{ansibleDevice.device_name}_{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                        "type": "check",
                        "status": "RUNNING",
                        "result": "",
                        "device_ssh_key_path": ansibleDevice.device_ssh_key_path,
                        "device_port": ansibleDevice.device_port,
                        "device_sudo_pass": ansibleDevice.device_pwd,
                        "create_user": create_user
                    }
                    from server.ansible_lib.ansible_utils import roce_check_task_ansible
                    if "all" in check_config_list:
                        roce_check_task_ansible.delay(**info_dict)
                        # TODO: check configuration 检查项预留
                    else:
                        if "roce" in check_config_list:
                            roce_check_task_ansible.delay(**info_dict)
                        if "other" in check_config_list:
                            pass
            return jsonify({
                "code": 200,
                'msg': 'success',
                "data": {}
            })
        else:
            return jsonify({
                "code": 404,
                'msg': 'id_list is empty.',
                "data": None
            })
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        db_session.close()


def api_exception_handler(f):
    """
    Exception handler decorator for API view layer
    Provides unified exception handling and response formatting
    """
    @wraps(f)
    def wrapper(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            LOG.error(f"Unexpected error in {f.__name__}: {str(e)}", exc_info=True)
            return jsonify({
                'status': 400,
                'msg': f'Error: {str(e)}',
                'data': None
            })
    return wrapper

@roce_mold.route('/get_fabric_switches', methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def get_fabric_switches():
    """
    Get fabric switches information
    Args:
        query_model: str, Optional. Configuration model name to filter switches
    Returns:
        JSON response with fabric switches data organized by fabric
    """
    data = request.get_json() or {}
    query_model = data.get("query_model")
    
    # Get fabric switches data from service layer
    fabric_switches_data = roce_db.get_fabric_switches_data(query_model)
    
    return jsonify({
        "status": 200,
        "data": fabric_switches_data
    })

@roce_mold.route('/get_ports_by_switch_sn', methods=['POST'])
@admin_permission.require(http_exception=403)
@api_exception_handler
def get_ports_by_switch_sn():
    """
    Get ports by switch serial number
    Returns:
        JSON response with port data
    """
    data = request.get_json()
    switch_sn = data.get("switch_sn")
    
    # Get ports data from service layer
    port_list = roce_db.get_ports_by_switch_sn_data(switch_sn)
    
    return jsonify({
        "status": 200,
        "data": port_list
    })
    
@roce_mold.route('/get_filter_ports_by_switch_sn', methods=['POST'])
@admin_permission.require(http_exception=403)
@api_exception_handler
def get_filter_ports_by_switch_sn():
    """
    Get filtered (unused) ports by switch serial number and configuration model
    Returns:
        JSON response with filtered port data
    """
    data = request.get_json()
    switch_sn = data.get("switch_sn")
    query_model = data.get("query_model")
    
    # Get filtered ports data from service layer
    port_list = roce_db.get_filter_ports_by_switch_sn_data(switch_sn, query_model)
    
    return jsonify({
        "status": 200,
        "data": port_list
    })

@roce_mold.route('/get_filter_queues_by_switch_sn', methods=['POST'])
@admin_permission.require(http_exception=403)
@api_exception_handler
def get_filter_queues_by_switch_sn():
    data = request.get_json()
    switch_sn = data.get("switch_sn")
    query_model = data.get("query_model")
    
    # Get filtered ports data from service layer
    queue_list = roce_db.get_filter_queues_by_switch_sn_data(switch_sn, query_model)
    
    return jsonify({
        "status": 200,
        "data": queue_list
    })

def deploy_roce_configuration(switch, config_data):
    """
    Deploy RoCE configuration to switch
    Args:
        switch: Switch object
        config_data: dict, Configuration data to deploy
    Returns:
        dict: Deployment result
    """
    from server.config_distribution.config_deploy import CLIConfigDeployer
    
    cli_deployer = CLIConfigDeployer(
        host=switch.mgt_ip,
        username=switch.system_config.switch_op_user,
        password=switch.system_config.switch_op_password,
        port=22
    )
    
    return cli_deployer.deploy_cli_roce_config(**config_data)

def validate_easydeploy_uniqueness(f):
    """
    装饰器：验证 RoceEasyDeployConfiguration 的唯一性
    - 确保一台交换机只能有一条记录
    - config_id 为空时创建，不为空时更新
    - 创建时如果记录已存在返回 400 错误
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    "status": 400,
                    "msg": "No configuration data provided"
                })

            configurations = data.get("configurations", [])
            if not configurations:
                return jsonify({
                    "status": 400,
                    "msg": "No configurations provided"
                })

            session = roce_db.get_session()
            
            for config in configurations:
                switch_sns = config.get("switch_sn")
                config_id = config.get("config_id")
                
                if not switch_sns:
                    return jsonify({
                        "status": 400,
                        "msg": "switch_sn is required"
                    })

                for switch_sn in switch_sns:
                    # 查询是否已存在该交换机的配置
                    existing_config = session.query(RoceEasyDeployConfiguration).filter_by(
                        switch_sn=switch_sn
                    ).first()

                    if not config_id or config_id == "":
                        # 创建操作：检查是否已存在记录
                        if existing_config:
                            return jsonify({
                                "status": 400,
                                "msg": "config easydeploy exists"
                            })
                    else:
                        # 更新操作：检查 config_id 是否存在且属于该交换机
                        target_config = session.query(RoceEasyDeployConfiguration).filter_by(
                            id=config_id
                        ).first()
                        
                        if not target_config:
                            return jsonify({
                                "status": 404,
                                "msg": f"Configuration with ID {config_id} not found"
                            })
                        
                        if target_config.switch_sn != switch_sn:
                            return jsonify({
                                "status": 400,
                                "msg": f"Configuration ID {config_id} does not belong to switch {switch_sn}"
                            })

            # 验证通过，执行原函数
            return f(*args, **kwargs)
            
        except Exception as e:
            LOG.error(f"Validation failed: {str(e)}")
            LOG.error(traceback.format_exc())
            return jsonify({
                "status": 500,
                "msg": f"Validation failed: {str(e)}"
            })
    
    return decorated_function

@roce_mold.route("/easydeploy_config/validate", methods=["POST"])
@admin_permission.require(http_exception=403)
@validate_easydeploy_uniqueness  # 复用现有装饰器进行校验
def roce_easydeploy_config_validate():
    """
    校验 RoCE easy deploy 配置的唯一性
    Request body:
    {
        "configurations": [
            {
                "config_id": "",
                "sysname": "string",
                "switch_sn": "string"
            }
        ]
    }
    Response:
    {
        "status": 200,
        "msg": "Validation passed"
    }
    """
    # 如果装饰器校验通过，直接返回成功
    return jsonify({
        "status": 200,
        "msg": "RoCE easy deploy configuration validation passed"
    })
        
@roce_mold.route("/easydeploy_config/<string:type>", methods=["POST"])
@admin_permission.require(http_exception=403)
@validate_easydeploy_uniqueness
@api_exception_handler
def roce_easydeploy_config_save(type):
    """
    Save or preview RoCE EasyDeploy configurations based on type parameter
    Args:
        type: str, Operation type ('save' or 'preview')
    """
    data = request.get_json()
    if not data:
        raise ValueError("No configuration data provided")

    configurations = data.get("configurations", [])
    if not configurations:
        raise ValueError("No configurations provided")

    # Prepare processed configurations with common logic
    processed_configs = []
    for config in configurations:
        sysnames = config.get("sysname", [])
        switch_sns = config.get("switch_sn", [])
        
        for index, switch_sn in enumerate(switch_sns):
            # Build configuration data (common part)
            # Use safe list access with default value
            sysname = sysnames[index]
            
            db_config = {
                "sysname": sysname,
                "switch_sn": switch_sn,
                "port": config.get("ports", []),
                "queue": config.get("queue_num", []),
                "enabled": data.get("enable_pfc"),
                "mode": data.get("mode")
            }
            
            # Get database session and query data (common part)
            session = roce_db.get_session()
            switch = session.query(Switch).filter(Switch.sn == switch_sn).first()
            if not switch:
                raise ValueError(f"Switch {switch_sn} not found")
                
            existing_config = session.query(RoceEasyDeployConfiguration).filter(
                RoceEasyDeployConfiguration.switch_sn == switch_sn
            ).first()
            
            # Build deployment configuration (common part)
            config_data = _build_easy_config_data(db_config, existing_config)
            
            processed_configs.append({
                "db_config": db_config,
                "switch": switch,
                "existing_config": existing_config,
                "config_data": config_data,
                "switch_name": f"{sysname}:{switch_sn}"
            })

    # Process configurations based on type with processed data
    if type == "save":
        result = _process_easydeploy_save_operation(processed_configs)
    elif type == "preview":
        result = _process_easydeploy_preview_operation(processed_configs)
    else:
        raise ValueError(f"Unsupported operation type: {type}")
    
    return jsonify(result)

def _process_easydeploy_save_operation(processed_configs):
    """
    Process save operation for RoCE EasyDeploy configurations with preprocessed data
    Args:
        processed_configs: list, List of preprocessed configuration data
    Returns:
        dict: Save operation result
    """
    errors = []
    total_processed = len(processed_configs)
    config_dict = {}
    for config_item in processed_configs:
        switch = config_item.get("switch")
        config_data = config_item.get("config_data")
        config_dict[switch.sn] = config_data
        
    res = config_distribution_roce(config_dict)
    if res.get("status") == 500 and res.get("info", None):
        return res
    
    err_info = res.get("err_info", {})

    for config_item in processed_configs:
        db_config = config_item.get("db_config")
        switch = config_item.get("switch")
        existing_config = config_item.get("existing_config")
        config_data = config_item.get("config_data")
        
        if switch.sn not in err_info:
            # Save to database only after successful deployment
            if existing_config:
                roce_db.update_roce_easy_deploy_configuration(existing_config.id, db_config)
                config_id = existing_config.id
            else:
                config_id = roce_db.add_roce_easy_deploy_configuration(db_config)
                
            # Update configuration data
            roce_db.update_roce_easy_deploy_configuration(
                config_id,
                {"config_data": json.dumps(config_data.get("new_val"))}
            )
        else:
            
            errors.append({
                "status": 500,
                "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
                "switch": switch.sn
            })
                
    if errors:
        return {"status": 500, "msg": "Deploy easydeploy configuration failed", "data": errors}

    return {
        "status": 200,
        "msg": "All RoCE easy deploy configurations saved successfully"
    }

def _process_easydeploy_preview_operation(processed_configs):
    """
    Process preview operation for RoCE EasyDeploy configurations with preprocessed data
    Args:
        processed_configs: list, List of preprocessed configuration data
    Returns:
        dict: Preview operation result with CLI commands
    """
    cli_commands = []

    for config_item in processed_configs:
        db_config = config_item.get("db_config")
        switch = config_item.get("switch")
        config_data = config_item.get("config_data")
        
        # Generate CLI commands
        from server.config_distribution.config_deploy import CLIConfigDeployer
        cli_deployer = CLIConfigDeployer(
            host=switch.mgt_ip,
            username=switch.system_config.switch_op_user,
            password=switch.system_config.switch_op_password,
            port=22
        )
        
        command_result = {
            "sysname": db_config.get("sysname"),
            "switch_sn": db_config.get("switch_sn"),
            "cli": cli_deployer.build_cli_roce_config_commands("preview", **config_data)
        }
        cli_commands.append(command_result)

    return {
        "status": 200,
        "commands": cli_commands
    }


def _build_easy_config_data(config, existing_config=None):
    """
    {
        'sysname': str,
        'switch_sn': str,
        'port': list,      
        'queue': list,     
        'enabled': bool,
        'mode': str        
    }
    """

    # 构建配置字典
    config_data = {
        "new_val": {
            "roce_configuring": {
                "roce_mode": config.get('mode', None),
                "roce_ports": config.get('port', []),
                "queues": config.get('queue', [])
            }
        },
        "old_val": json.loads(existing_config.config_data) if existing_config and existing_config.config_data else {}
    }
    
        
    # 转换为 JSON 字符串
    return config_data

@roce_mold.route("/easydeploy_config/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def roce_easydeploy_config_list():
    """
    获取 RoCE easy deploy 配置列表
    """
    try:
        session = roce_db.get_session()
        data = request.get_json()
        if not data:
            return jsonify({
                "status": 400,
                "msg": "No query parameters provided"
            })

        # 关联表别名
        SwitchAlias = aliased(Switch)
        AssocAlias = aliased(AssociationFabric)
        FabricAlias = aliased(Fabric)

        # 构建基础查询
        pre_query = (
            session.query(
                RoceEasyDeployConfiguration,
                SwitchAlias,
                AssocAlias,
                FabricAlias
            )
            .outerjoin(SwitchAlias, RoceEasyDeployConfiguration.switch_sn == SwitchAlias.sn)
            .outerjoin(AssocAlias, SwitchAlias.id == AssocAlias.switch_id)
            .outerjoin(FabricAlias, AssocAlias.fabric_id == FabricAlias.id)
        )

        # # 搜索条件
        # search_conditions = []
        # searchFields = data.get("searchFields", {}).get("fields", [])
        # search_value = data.get("searchFields", {}).get("value", "")
        # if "port" in searchFields:
        #     from sqlalchemy import Text, or_
        #     column = getattr(RoceEasyDeployConfiguration, "port")
        #     search_conditions.append(column.cast(Text).ilike(f'%{search_value}%'))
        #     pre_query = pre_query.filter(or_(*search_conditions))


        filter_fields = data.get("filterFields", [])
        
        if filter_fields:
            for field in filter_fields:
                field_name = field.get("field")
                filters = field.get("filters", [])
                
                for filter_item in filters:
                    filter_value = filter_item.get("value")
                    
                    # Handle special fields that are not in DLBConfiguration table

                    if field_name == "enabled":
                        data.get("filterFields", []).remove(field)
                        # Convert search term to boolean based on enabled/disabled matching
                        filter_value_lower = filter_value.lower()
                        if filter_value_lower in "enabled":
                            pre_query = pre_query.filter(RoceEasyDeployConfiguration.enabled == True)
                        elif filter_value_lower in "disabled":
                            pre_query = pre_query.filter(RoceEasyDeployConfiguration.enabled == False)


        # 分页
        page_num, page_size, total_count, query_result = utils.query_helper(
            RoceEasyDeployConfiguration,
            pre_query=pre_query,
            data=request.get_json()
        )

        # 组装数据
        configs = []
        for config, switch, assoc, fabric in query_result:
            config_dict = config.make_dict()
            if fabric:
                config_dict["fabric"] = fabric.fabric_name
            else:
                config_dict["fabric"] = None
            
            if switch:
                config_dict["sysname"] = switch.host_name
                
            configs.append(config_dict)

        return jsonify({
            "status": 200,
            "total": total_count,
            "page": page_num,
            "pageSize": page_size,
            "data": configs
        })

    except Exception as e:
        LOG.error(f"Error in roce_easydeploy_config_list: {str(e)}")
        LOG.error(traceback.format_exc())
        return jsonify({
            "status": 500,
            "msg": f"Query RoCE easy deploy configurations failed: {str(e)}"
        })

@roce_mold.route("/easydeploy_config/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def roce_easydeploy_config_delete():
    """
    删除 RoCE easy deploy 配置
    Request body:
    {
        "config_id": str  # 配置ID
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "status": 400,
                "msg": "No configuration ID provided"
            })

        config_id = data.get("config_id")
        if not config_id:
            return jsonify({
                "status": 400,
                "msg": "Configuration ID is required"
            })

        errors = []
        # 查询配置是否存在
        session = roce_db.get_session()
        config = session.query(RoceEasyDeployConfiguration).filter_by(id=config_id).first()
        if not config:
            return jsonify({
                "status": 404,
                "msg": f"Configuration with ID {config_id} not found"
            })

        switch_sn = config.switch_sn
        switch = session.query(Switch).filter(Switch.sn == switch_sn).first()
        if switch:
            config_data = _build_easy_config_data({}, config)
            config_dict = {
                switch.sn : config_data  
            }
            res = config_distribution_roce(config_dict)

            if switch.reachable_status == 1:
                roce_db.delete_roce_easy_deploy_configuration(config_id)
                return jsonify({
                    "status": 200,
                    "msg": "Configuration deleted successfully"
                })

            if res.get("status") != 200:
                if res.get("info", None):
                    msg = res.get("info")
                else:
                    msg = res.get("err_info", {}).get(switch.sn, "")
                errors.append({
                    'status': 500,
                    'msg': f'{switch.host_name} ({switch.sn}): {msg}'
                })

                if errors:
                    return jsonify({
                        "status": 500,
                        "msg": "Delete RoCE easy deploy configuration failed",
                        "data": errors
                    })
            else:   
                roce_db.delete_roce_easy_deploy_configuration(config_id)
                return jsonify({
                    "status": 200,
                    "msg": "Configuration deleted successfully"
                })
        else:
            return jsonify({
                "status": 500,
                "msg": f"Switch with SN {switch_sn} not found"
            })

    except Exception as e:
        LOG.error(f"Error in roce_easydeploy_config_delete: {str(e)}")
        LOG.error(traceback.format_exc())
        return jsonify({
            "status": 500,
            "msg": f"Delete RoCE easy deploy configuration failed: {str(e)}"
        })

def parse_roce_config_output(raw_text: str) -> dict:
    # === Step 1: Preprocessing ===
    text = raw_text.replace('\r\r\n', '\n').strip()
    lines = text.splitlines()

    result = {}
    idx = 0
    n = len(lines)

    # === Step 2: Helpers ===
    def parse_key_value_block(start):
        block = {}
        i = start
        while i < n and lines[i].strip() and not re.match(r'^[^\s]', lines[i]):
            line = lines[i].strip()
            if re.search(r'\s{2,}', line):
                k, v = re.split(r'\s{2,}', line, maxsplit=1)
                block[k.strip().replace('-', '_')] = v.strip()
            else:
                block[line.strip()] = ""
            i += 1
        return block, i

    def parse_mapping_table(start, columns):
        mapping = {}
        i = start
        while i < n and re.match(r'^\s*\d+', lines[i]):
            line = lines[i].strip()
            parts = re.split(r'\s{2,}', line)
            key = parts[0]
            if len(parts) == 2:
                values = [int(x) for x in parts[1].split(',') if x.strip().isdigit()]
                mapping[key] = values
            elif len(parts) == 3:
                mapping[key] = dict(zip(columns[1:], parts[1:]))
            i += 1
        return mapping, i

    # === Step 3: Parse line by line ===
    while idx < n:
        line = lines[idx].strip()
        if not line:
            idx += 1
            continue

        if line.startswith('run '):
            result['run'] = line[4:]
            idx += 1
        elif line in ('congestion-control', 'pfc', 'trust'):
            block, idx = parse_key_value_block(idx + 1)
            if line == 'congestion-control' and 'enabled_queue' in block:
                block['enabled_queue'] = [int(x) for x in block['enabled_queue'].split(',') if x.strip().isdigit()]
            result[line.replace('-', '_')] = block
        elif 'status' in line or 'mode' in line:
            if re.search(r'\s{2,}', line):
                k, v = re.split(r'\s{2,}', line, maxsplit=1)
                result[k.strip().lower()] = v.strip()
            idx += 1
        elif line.startswith('RoCE PCP/DSCP->LP'):
            idx += 2  # skip title and === line
            idx += 1  # skip column header
            mapping, idx = parse_mapping_table(idx, ['local-priority', 'dscp'])
            result['pcp_dscp_mapping'] = mapping
        elif line.startswith('RoCE LP->FC'):
            idx += 2  # skip title and column header
            idx += 1  # skip dash line
            mapping, idx = parse_mapping_table(idx, ['local-priority', 'forwarding-class', 'scheduler-weight'])
            result['lp_fc_mapping'] = mapping
        else:
            idx += 1

    return result

@roce_mold.route("/easydeploy_config/overview", methods=["POST"])
@admin_permission.require(http_exception=403)
def easydeploy_overview():
    """
    获取 RoCE easy deploy 配置概览信息
    Request body:
    {
        "sysname": "string",
        "overview_type": "string"  # basic/pcp_dscp/lp
    }
    Response:
    {
        "status": 200,
        "info": "string"
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "status": 400,
                "msg": "No request data provided"
            })

        sysname = data.get("sysname")
        sn = data.get("switch_sn")
        overview_type = data.get("overview_type", "basic")

        if not sysname or not sn:
            return jsonify({
                "status": 400,
                "msg": "Sysname and SN is required"
            })

        if not overview_type or overview_type not in ["basic", "pcp_dscp", "lp"]:
            return jsonify({
                "status": 400,
                "msg": "Invalid overview type. Must be one of: basic, pcp_dscp, lp"
            })
        
        # 获取配置
        session = roce_db.get_session()
        switch = session.query(Switch).filter(Switch.sn == sn).first()
        if not switch:
            return jsonify({
                "status": 400,
                "msg": f"Switch with sn {sn} not found"
            })
        from server.util.ssh_util import interactive_shell_configure
        result, _ = interactive_shell_configure(cmd="run show class-of-service roce", hostname=switch.mgt_ip,
                                                username=switch.system_config.switch_op_user,
                                                password=switch.system_config.switch_op_password, port=22)
        LOG.info(f"RoCE easy deploy overview command output: {str(result)}")
        result_dict = parse_roce_config_output(str(result))
        basic_info = {
            "roce_easydeploy_status": result_dict.get("status", ""),
            "roce_mode": result_dict.get("mode", ""),
            "congestion_control": result_dict.get("congestion_control", {}),
            "pfc": result_dict.get("pfc", {}),
            "trust": result_dict.get("trust", {})
        }
        pcp_dscp_info = result_dict.get("pcp_dscp_mapping", {})
        lp_info = result_dict.get("lp_fc_mapping", {})

        config = roce_db.get_configuration_overview_by_sn(sn)

        if config:
            roce_db.update_configuration_overview(config.id, {
                "basic_info": basic_info,
                "pcp_dscp_info": pcp_dscp_info,
                "lp_info": lp_info
            })
        else:
            roce_db.add_configuration_overview(
                sysname=sysname,
                switch_sn=sn,
                basic_info=basic_info,
                pcp_dscp_info=pcp_dscp_info,
                lp_info=lp_info
            )

        info = {
            "sn": sn,
            "sysname": sysname,
            "basic_info": basic_info,
            "pcp_dscp_info": pcp_dscp_info,
            "lp_info": lp_info
        }

        return jsonify({
            "status": 200,
            "info": info
        })

    except Exception as e:
        LOG.error(f"Error in easydeploy_overview: {str(e)}")
        LOG.error(traceback.format_exc())
        return jsonify({
            "status": 500,
            "msg": f"Get overview information failed: {str(e)}"
        })


@roce_mold.route("/dlb_config/<string:type>", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def dlb_config_save(type):
    """
    Save or preview DLB configurations based on type parameter
    Args:
        type: str, Operation type ('save' or 'preview')
    """
    data = request.get_json()
    if not data:
        raise ValueError("No configuration data provided")

    switches = data.get("switch", [])
    configuration = data.get("configuration", {})

    if not switches:
        raise ValueError("No switches provided")
    if not configuration:
        raise ValueError("No configuration provided")

    # Prepare processed configurations with common logic
    processed_configs = []
    for switch in switches:
        sysname = switch.get("sysname")
        switch_sn = switch.get("switch_sn")
        
        # Build configuration data (common part)
        db_config = {
            "sysname": sysname,
            "switch_sn": switch_sn,
            "dlb_enabled": configuration.get("enabled"),
            "mode": configuration.get("dlb_mode", "dlb-normal")
        }
        
        # Get database session and query data (common part)
        session = roce_db.get_session()
        switch_obj = session.query(Switch).filter(Switch.sn == switch_sn).first()
        if not switch_obj:
            raise ValueError(f"Switch {switch_sn} not found")
            
        existing_config = roce_db.get_dlb_configuration_by_sn(switch_sn)
        
        # Build deployment configuration (common part)
        config_data = _build_dlb_config_data(db_config, existing_config)
        
        processed_configs.append({
            "db_config": db_config,
            "switch": switch_obj,
            "existing_config": existing_config,
            "config_data": config_data,
            "switch_name": f"{sysname}:{switch_sn}"
        })

    # Process configurations based on type with processed data
    if type == "save":
        result = _process_dlb_save_operation(processed_configs)
    elif type == "preview":
        result = _process_dlb_preview_operation(processed_configs)
    else:
        raise ValueError(f"Unsupported operation type: {type}")
    
    return jsonify(result)

def _process_dlb_save_operation(processed_configs):
    """
    Process save operation for DLB configurations with preprocessed data
    Args:
        processed_configs: list, List of preprocessed configuration data
    Returns:
        dict: Save operation result
    """
    errors = []
    total_processed = len(processed_configs)
    
    config_dict = {}
    for config_item in processed_configs:
        switch = config_item.get("switch")
        config_data = config_item.get("config_data")
        config_dict[switch.sn] = config_data
        
    res = config_distribution_roce(config_dict)
    if res.get("status") == 500 and res.get("info", None):
        return res
    
    err_info = res.get("err_info", {})

    for config_item in processed_configs:
        db_config = config_item.get("db_config")
        switch = config_item.get("switch")
        existing_config = config_item.get("existing_config")
        config_data = config_item.get("config_data")
        
        if switch.sn not in err_info:
            # Save to database only after successful deployment
            if existing_config:
                roce_db.update_dlb_configuration(existing_config.id, db_config)
                config_id = existing_config.id
            else:
                config_id = roce_db.add_dlb_configuration(db_config)
                
            # Update configuration data
            roce_db.update_dlb_configuration(
                config_id,
                {"config_data": json.dumps(config_data.get("new_val"))}
            )
        else:
            errors.append({
                "status": 500,
                "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
                "switch": switch.sn
            })

    if errors:
        return {"status": 500, "msg": "Deploy DLB configuration failed", "data": errors}

    return {
        "status": 200,
        "msg": "All DLB configurations saved successfully"
    }

def _process_dlb_preview_operation(processed_configs):
    """
    Process preview operation for DLB configurations with preprocessed data
    Args:
        processed_configs: list, List of preprocessed configuration data
    Returns:
        dict: Preview operation result with CLI commands
    """
    commands_list = []

    for config_item in processed_configs:
        db_config = config_item.get("db_config")
        switch = config_item.get("switch")
        config_data = config_item.get("config_data")
        
        # Generate CLI commands
        from server.config_distribution.config_deploy import CLIConfigDeployer
        cli_deployer = CLIConfigDeployer(
            host=switch.mgt_ip,
            username=switch.system_config.switch_op_user,
            password=switch.system_config.switch_op_password,
            port=22
        )
        
        command_result = {
            "sysname": db_config.get("sysname"),
            "cli": cli_deployer.build_cli_roce_config_commands("preview", **config_data)
        }
        commands_list.append(command_result)

    return {
        "status": 200,
        "commands": commands_list
    }

def _build_dlb_config_data(config, existing_config=None):
    """
    构建 DLB 配置数据
    Args:
        config: dict, 配置信息
            {
                "mode": str  # DLB 模式 ('dlb-normal', 'dlb-optimal', 'dlb-assigned')
            }
    Returns:
        dict: DLB 配置数据
    """
    # 构建配置字典
    config_data = {
        "new_val": {
            "dlb": {
                "dlb_mode": config.get('mode', 'dlb-normal')  # 默认模式为 'normal'
            }
        },
        "old_val": json.loads(existing_config.config_data) if existing_config and existing_config.config_data else {}
    }
    
    return config_data

@roce_mold.route("/dlb_config/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def dlb_config_list():
    try:
        session = roce_db.get_session()
        data = request.get_json()
        
        if not data:
            return jsonify({
                "status": 400,
                "msg": "No query parameters provided"
            })

        # Import required modules
        from sqlalchemy import func, text, asc, desc
        
        # 关联表别名
        SwitchAlias = aliased(Switch)
        AssocAlias = aliased(AssociationFabric)
        FabricAlias = aliased(Fabric)

        # 构建基础查询
        pre_query = (
            session.query(
                DLBConfiguration,
                SwitchAlias,
                AssocAlias,
                FabricAlias
            )
            .outerjoin(SwitchAlias, DLBConfiguration.switch_sn == SwitchAlias.sn)
            .outerjoin(AssocAlias, SwitchAlias.id == AssocAlias.switch_id)
            .outerjoin(FabricAlias, AssocAlias.fabric_id == FabricAlias.id)
        )
        
        # Handle custom filtering
        filter_fields = data.get("filterFields", [])
        
        if filter_fields:
            for field in filter_fields:
                field_name = field.get("field")
                filters = field.get("filters", [])
                
                for filter_item in filters:
                    match_mode = filter_item.get("matchMode", "exact")
                    filter_value = filter_item.get("value")
                    
                    # Handle special fields that are not in DLBConfiguration table
                    if field_name == "fabric":
                        data.get("filterFields", []).remove(field)

                        pre_query = pre_query.filter(FabricAlias.fabric_name.like(f'%{filter_value}%'))
                    elif field_name == "dlb_enabled":
                        data.get("filterFields", []).remove(field)
                        # Convert search term to boolean based on enabled/disabled matching
                        filter_value_lower = filter_value.lower()
                        if filter_value_lower in "enabled":
                            pre_query = pre_query.filter(DLBConfiguration.dlb_enabled == True)
                        elif filter_value_lower in "disabled":
                            pre_query = pre_query.filter(DLBConfiguration.dlb_enabled == False)

                    elif field_name == "online":
                        data.get("filterFields", []).remove(field)
                        # Convert search term to integer based on Online/Offline matching
                        filter_value_lower = filter_value.lower()
                        if filter_value_lower in "offline":
                            pre_query = pre_query.filter(SwitchAlias.reachable_status == 1)
                        elif filter_value_lower in "online":
                            pre_query = pre_query.filter(SwitchAlias.reachable_status == 0)


        # Handle custom sorting
        sort_fields = data.get("sortFields", [])
        
        if sort_fields:
            # Build order_by list
            for field in sort_fields:
                field_name = field.get("field")
                field_order = field.get("order", "asc")
                
                # Handle special fields that are not in DLBConfiguration table
                if field_name == "fabric":
                    if field_order == "asc":
                        pre_query = pre_query.order_by(FabricAlias.fabric_name.asc())
                    else:
                        pre_query = pre_query.order_by(FabricAlias.fabric_name.desc())
                elif field_name == "online":
                    if field_order == "asc":
                        pre_query = pre_query.order_by(SwitchAlias.reachable_status.asc())
                    else:
                        pre_query = pre_query.order_by(SwitchAlias.reachable_status.desc())
                # Handle regular fields in DLBConfiguration table
                elif hasattr(DLBConfiguration, field_name):
                    column = getattr(DLBConfiguration, field_name)
                    if field_order == "asc":
                        pre_query = pre_query.order_by(asc(column))
                    elif field_order == "desc":
                        pre_query = pre_query.order_by(desc(column))
        else:
            # Default sorting by id if no custom sort fields provided
            pre_query = pre_query.order_by(DLBConfiguration.id)

        # 获取分页和搜索结果
        page_num, page_size, total_count, query_result = utils.query_helper(
            DLBConfiguration,
            pre_query=pre_query,
            data=data,
            skip_sort=True
        )

        configs = []
        for config, switch, assoc, fabric in query_result:
            config_dict = config.make_dict()
            if fabric:
                config_dict["fabric"] = fabric.fabric_name
            else:
                config_dict["fabric"] = None
            
            if switch:
                config_dict["online"] = switch.reachable_status
                config_dict["sysname"] = switch.host_name
            else:
                config_dict["online"] = 2
            configs.append(config_dict)

        return jsonify({
            "status": 200,
            "data": configs,
            "total": total_count,
            "page": page_num,
            "pageSize": page_size
        })

    except Exception as e:
        LOG.error(f"Error in dlb_config_list: {str(e)}")
        LOG.error(traceback.format_exc())
        return jsonify({
            "status": 500,
            "msg": f"Query dlb configurations failed: {str(e)}"
        })

def validate_pfc_uniqueness(f):
    """
    Decorator: Validate PFC configuration uniqueness for creation
    - Ensure only one record per switch_sn + profile_name combination
    - Only for creation operations (no config_id should be provided)
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        data = request.get_json()
        if not data:
            raise ValueError("No configuration data provided")

        configurations = data.get("configurations", [])
        if not configurations:
            raise ValueError("No configurations provided")

        # Check for uniqueness using service layer
        for config in configurations:
            switch_sn = config.get("switch_sn")
            profile_name = config.get("profile_name")
            config_id = config.get("config_id")
            
            if not switch_sn:
                raise ValueError("switch_sn is required")
            
            if not profile_name:
                raise ValueError("profile_name is required")
            
            # For creation, config_id should not be provided
            if config_id and not roce_db.check_pfc_profile_exists(switch_sn, profile_name):
                raise ValueError(f"PFC profile '{profile_name}' not found on switch {switch_sn}")

            # Check if profile already exists on this switch
            if not config_id and roce_db.check_pfc_profile_exists(switch_sn, profile_name):
                raise ValueError(f"PFC profile '{profile_name}' already exists on switch {switch_sn}")

        # Validation passed, execute original function
        return f(*args, **kwargs)
            
    return decorated_function


def validate_ecn_uniqueness(f):
    """
    Decorator: Validate ECN main configuration uniqueness for creation
    - Ensure only one ECN main configuration per switch_sn
    - Only for creation operations (no config_id should be provided)
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        data = request.get_json()
        if not data:
            raise ValueError("No configuration data provided")

        configurations = data.get("configurations", [])
        if not configurations:
            raise ValueError("No configurations provided")

        # Check for uniqueness using service layer
        for config in configurations:
            switch_sn = config.get("switch_sn")
            config_id = config.get("config_id")
            
            if not switch_sn:
                raise ValueError("switch_sn is required")
            
            # For creation operations, config_id should not be provided
            if config_id:
                if not roce_db.check_ecn_main_config_exists(switch_sn):
                    raise ValueError(f"ECN main configuration not found on switch {switch_sn}.")
            
            # Check if ECN main configuration already exists on this switch
            if not config_id:
                if roce_db.check_ecn_main_config_exists(switch_sn):
                    raise ValueError(f"ECN main configuration already exists on switch {switch_sn}. Each switch can only have one ECN configuration.")

        # Validation passed, execute original function
        return f(*args, **kwargs)
            
    return decorated_function


@roce_mold.route("/pfc_config/validate", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
@validate_pfc_uniqueness
def roce_pfc_config_validate():
    """
    校验 PFC 配置的唯一性
    Request body:
    {
        "configurations": [
            {
                "config_id": "",
                "sysname": "string",
                "switch_sn": "string",
                "profile_name": "string"
            }
        ]
    }
    Response:
    {
        "status": 200,
        "msg": "Validation passed"
    }
    """
    # 如果装饰器校验通过，直接返回成功
    return jsonify({
        "status": 200,
        "msg": "PFC configuration validation passed"
        })


@roce_mold.route("/ecn_config/validate", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
@validate_ecn_uniqueness
def roce_ecn_config_validate():
    """
    校验 ECN 配置的唯一性
    Request body:
    {
        "configurations": [
            {
                "config_id": "",
                "sysname": "string",
                "switch_sn": "string"
            }
        ]
    }
    Response:
    {
        "status": 200,
        "msg": "Validation passed"
    }
    """
    # 如果装饰器校验通过，直接返回成功
    return jsonify({
        "status": 200,
        "msg": "ECN configuration validation passed"
        })

def _build_pfc_config_data(configs, old_val={"pfc": []}):
    """
    Build PFC configuration data for deployment
    Args:
        config: dict, PFC configuration input (empty for delete operations)
        existing_config: PfcConfiguration, Existing configuration object (None for creation)
    Returns:
        dict: Built configuration data
    """
    config_data = {
        "new_val": {
            "pfc": []
        },
        "old_val": {
            "pfc": []
        }
    }
    for config in configs:
        config_data["new_val"]["pfc"].append({
            "pfc_profile_name": config.get("profile_name", ""),
            "ports": config.get("port", []),
            "code_points": config.get("queue", []),
            "drop_enable": str(config.get("enabled")).lower(),
        })

    config_data["old_val"] = old_val
        
    return config_data

@roce_mold.route("/pfc_config/save", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
# @validate_pfc_uniqueness
def roce_pfc_config_save():
    """
    Save PFC configurations
    Request body:
    {
        "configurations": [
            {
                "sysname": "string",
                "switch_sn": "switch_sn",
                "port": ["string"],
                "queue": ["string"],
                "profile_name": "string",
                "enabled": true,
                "is_all_ports": false,
                "is_all_queues": false
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    configurations = data.get("configurations", [])
    
    if not configurations:
        raise ValueError("No configurations provided")
    
    # Group configurations by switch_sn and prepare for deployment
    switch_configs = {}
    
    for config in configurations:
        switch_sn = config.get("switch_sn")
        if not switch_sn:
            raise ValueError("switch_sn is required")
        
        if switch_sn not in switch_configs:
            switch_configs[switch_sn] = {
                "configs": []
            }
        
        switch_configs[switch_sn]["configs"].append(config)
    
    config_dict = {}
    db_configurations = {}
    errors = []

    for switch_sn, switch_data in switch_configs.items():
        # Get switch for deployment using service layer
        switch = roce_db.get_switch_by_sn(switch_sn)
        if not switch:
            raise ValueError(f"Switch with SN {switch_sn} not found")
    
        enhanced_configs = switch_data.get("configs")
        # Build configuration data using view layer tool function
        config_data = _build_pfc_config_data(enhanced_configs)

        config_dict[switch_sn] = config_data
        db_configurations[switch_sn] = []
        # Prepare database records for this switch
        for config in enhanced_configs:
            db_config = {
                "sysname": config.get("sysname"),
                "switch_sn": switch_sn,
                "profile_name": config.get("profile_name"),
                "port": config.get("port"),
                "queue": config.get("queue"),
                "enabled": config.get("enabled"),
                "is_all_ports": config.get("is_all_ports", False),
                "is_all_queues": config.get("is_all_queues", False),
                "config_data": json.dumps(config_data.get("new_val"))
            }
            db_configurations[switch_sn].append(db_config)

    res = config_distribution_roce(config_dict)
    LOG.info(f"PFC config save result: {res}")
    if res.get("status") == 500 and res.get("info", None):
        return res
    
    err_info = res.get("err_info", {})
    # Save all configurations to database via service layer
    for switch_sn, switch_data in switch_configs.items():
        if switch_sn not in err_info:

            roce_db.save_pfc_configurations(db_configurations.get(switch_sn))
        else:
            errors.append({
                "status": 500,
                "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
                "switch": switch_sn
            })

    if errors:
        return jsonify({
            "status": 500,
            "msg": "Deploy PFC configurations failed",
            "data": errors
        })
    
    return jsonify({
        "status": 200,
        "msg": "PFC configurations saved successfully",
        "data": {}
    })


@roce_mold.route("/pfc_config/update", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def roce_pfc_config_update():
    """
    Update PFC configurations for a specific switch (bulk update)
    Request body:
    {
        "switch_sn": "string",
        "configurations": [
            {
                "config_id": "string",  # for update/delete
                "sysname": "string",
                "profile_name": "string",
                "port": ["string"],
                "queue": ["string"],
                "enabled": true,
                "is_all_ports": false,
                "is_all_queues": false
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    switch_sn = data.get("switch_sn")
    configurations = data.get("configurations", [])

    if not switch_sn:
        raise ValueError("switch_sn is required")

    # Get switch for deployment using service layer
    switch = roce_db.get_switch_by_sn(switch_sn)
    if not switch:
        raise ValueError(f"Switch with SN {switch_sn} not found")

    # Get existing configurations for comparison
    existing_configs = roce_db.get_pfc_configurations_by_switch_sn(switch_sn)
    if len(existing_configs) > 0:
        old_val = json.loads(existing_configs[0].config_data)
    else:
        old_val = {"pfc": []}
    
    # Build configuration data for deployment
    config_data = _build_pfc_config_data(configurations, old_val)

    config_dict = {
        switch_sn: config_data
    }

    res = config_distribution_roce(config_dict)
    LOG.info(f"PFC config update result: {res}")
    if res.get("status") == 500 and res.get("info", None):
        return res
    
    err_info = res.get("err_info", {})
    errors = []
    
    # Check deployment result for this switch
    if switch_sn not in err_info:
        # Success: Update database configurations
        for config in configurations:
            config["config_data"] = json.dumps(config_data.get("new_val"))
            
        # Execute database operations after successful deployment
        roce_db.update_pfc_configurations(switch_sn, configurations)
        
        return jsonify({
            "status": 200,
            "msg": "PFC configurations updated successfully"
        })
    else:
        # Failed: Return error information
        errors.append({
            "status": 500,
            "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
            "switch": switch_sn
        })
        
        return jsonify({
            "status": 500,
            "msg": "Deploy PFC configurations failed",
            "data": errors
        })

@roce_mold.route("/pfc_config/detail_by_switch", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def roce_pfc_config_detail_by_switch():
    """
    Get all PFC configurations for a specific switch
    Request body:
    {
        "switch_sn": "string"
    }
    Response:
    {
        "status": 200,
        "msg": "Get pfc config detail successfully",
        "data": [configuration list]
    }
    """
    data = request.get_json()
    if not data:
        raise ValueError("No request data provided")
    
    switch_sn = data.get("switch_sn")
    if not switch_sn:
        raise ValueError("switch_sn is required")

    # Get PFC configurations via service layer
    configs = roce_db.get_pfc_configs_detail_by_switch_sn(switch_sn)

    return jsonify({
        "status": 200,
        "msg": "Get pfc config detail successfully",
        "data": configs
    })


@roce_mold.route('/pfc_config/list', methods=['post'])
@admin_permission.require(http_exception=403)
@api_exception_handler
def roce_pfc_config_list():
    """
    Get PFC configuration list with tree structure and pagination.
    
    Request body:
    {
        "searchFields": {
            "fields": ["port"],  
            "value": "search_value"
        },
        "page": 1,
        "pageSize": 20
    }
    """
    # Get request data
    request_data = request.get_json()
    
    # Call service layer to get paginated tree data
    result = roce_db.get_pfc_configs_list_data(request_data)
    
    return jsonify({
        "status": 200,
        "data": result.get("data"),
        "page": result.get("page"),
        "pageSize": result.get("pageSize"),
        "total": result.get("total")
    })

@roce_mold.route('/pfc_config/delete', methods=['post'])
@admin_permission.require(http_exception=403)
@api_exception_handler
def roce_pfc_config_delete():
    """
    Delete PFC configuration.
    
    Request body:
    {
        "config_id": "string"
    }
    """
    # Get request data
    request_data = request.get_json()
    config_id = request_data.get("config_id")
    
    if not config_id:
        raise ValueError("config_id is required")
    
    # Call service layer to get configuration and switch info for deletion
    result = roce_db.get_pfc_configuration_for_deletion(config_id)
    
    config = result.get("config")
    switch = result.get("switch")
    stored_config_data = json.loads(config.config_data)

    for c in stored_config_data.get("pfc"):
        if c.get("pfc_profile_name") == config.profile_name:
            old_val = {"pfc": [c]}
            stored_config_data.get("pfc", []).remove(c)
            break
    else:
        raise ValueError("Profile name not found")
    
    # Build delete configuration data using _build_pfc_config_data function
    config_data = _build_pfc_config_data([], old_val)

    config_dict = {
        switch.sn: config_data
    }

    res = config_distribution_roce(config_dict)
    LOG.info(f"PFC config delete result: {res}")

    if switch.reachable_status == 1:
        roce_db.delete_pfc_configuration(config_id, switch.sn, stored_config_data)
        
        return jsonify({
            "status": 200,
            "msg": "PFC configuration deleted successfully"
        })

    if res.get("status") == 500 and res.get("info", None):
        return res
    
    err_info = res.get("err_info", {})
    errors = []
    
    # Check deployment result for this switch
    if switch.sn not in err_info:
        # Success: Delete the configuration from database after successful deployment
        roce_db.delete_pfc_configuration(config_id, switch.sn, stored_config_data)
        
        return jsonify({
            "status": 200,
            "msg": "PFC configuration deleted successfully"
        })
    else:
        errors.append({
            "status": 500,
            "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
            "switch": switch.sn
        })

        return jsonify({
            "status": 500,
            "msg": "Deploy PFC configuration deletion failed",
            "data": errors
        })


@roce_mold.route("/pfc_buffer_config/save", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def roce_pfc_buffer_config_save():
    """
    Save PFC Buffer configurations
    Request body:
    {
        "configurations": [
            {
                "sysname": "string",
                "switch_sn": "string",
                "traffic_type": "ingress|egress",
                "port": ["string"],
                "queue": ["string"],
                "shared_ratio": float,
                "threshold": int,
                "guaranteed": int,      # ingress only
                "reset_offset": int,    # ingress only
                "headroom": int,        # ingress only
                "is_all_ports": bool,
                "is_all_queues": bool
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    configurations = data.get("configurations", [])
    
    if not configurations:
        raise ValueError("No configurations provided")
    
    # Group configurations by switch_sn and traffic_type for deployment
    switch_configs = {}
    
    for config in configurations:
        switch_sn = config.get("switch_sn")
        traffic_type = config.get("traffic_type", "").lower()
        
        if not switch_sn:
            raise ValueError("switch_sn is required")
        if traffic_type not in ["ingress", "egress"]:
            raise ValueError("traffic_type must be 'ingress' or 'egress'")
        
        if switch_sn not in switch_configs:
            switch_configs[switch_sn] = {
                "ingress_configs": [],
                "egress_configs": []
            }
        
        if traffic_type == "ingress":
            switch_configs[switch_sn]["ingress_configs"].append(config)
        else:
            switch_configs[switch_sn]["egress_configs"].append(config)
    
    config_dict = {}
    db_configurations = []

    for switch_sn, switch_data in switch_configs.items():
        # Get switch for deployment using service layer
        switch = roce_db.get_switch_by_sn(switch_sn)
        if not switch:
            raise ValueError(f"Switch with SN {switch_sn} not found")
    
        ingress_configs = switch_data.get("ingress_configs", [])
        egress_configs = switch_data.get("egress_configs", [])
        
        # Build configuration data using new unified view layer tool function
        config_data = _build_pfc_buffer_data(
            ingress_configs=ingress_configs,
            egress_configs=egress_configs,
            old_val={"pfc_buffer": []}
        )

        config_dict[switch_sn] = config_data
        
        # Prepare database records for this switch
        for config in ingress_configs + egress_configs:
            db_config = {
                "sysname": config.get("sysname"),
                "switch_sn": switch_sn,
                "traffic_type": config.get("traffic_type"),
                "port": config.get("port"),
                "queue": config.get("queue"),
                "shared_ratio": config.get("shared_ratio"),
                "threshold": config.get("threshold"),
                "is_all_ports": config.get("is_all_ports", False),
                "is_all_queues": config.get("is_all_queues", False),
                "config_data": json.dumps(config_data.get("new_val"))
            }
            
            # Add ingress-specific fields
            if config.get("traffic_type") == "ingress":
                db_config.update({
                    "guaranteed": config.get("guaranteed"),
                    "reset_offset": config.get("reset_offset"),
                    "headroom": config.get("headroom")
                })
            
            db_configurations.append(db_config)

    # Deploy configurations first (deploy first, then save to database)
    res = config_distribution_roce(config_dict)
    LOG.info(f"PFC Buffer config save result: {res}")
    if res.get("status") == 500 and res.get("info", None):
        return res
    
    err_info = res.get("err_info", {})
    errors = []
    
    # Group database configurations by switch_sn for conditional saving
    switch_db_configs = {}
    for db_config in db_configurations:
        switch_sn = db_config["switch_sn"]
        if switch_sn not in switch_db_configs:
            switch_db_configs[switch_sn] = []
        switch_db_configs[switch_sn].append(db_config)
    
    # Save configurations to database only for successful deployments
    for switch_sn, switch_data in switch_configs.items():
        if switch_sn not in err_info:
            # Success: Save configurations to database
            roce_db.save_pfc_buffer_configurations(switch_db_configs.get(switch_sn, []))
        else:
            # Failed: Record error information
            errors.append({
                "status": 500,
                "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
                "switch": switch_sn
            })
    
    if errors:
        return jsonify({
            "status": 500,
            "msg": "Deploy PFC Buffer configurations failed",
            "data": errors
        })
    
    return jsonify({
        "status": 200,
        "msg": "PFC Buffer configurations saved successfully",
        "data": {}
    })


@roce_mold.route("/pfc_buffer_config/update", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def roce_pfc_buffer_config_update():
    """
    Update PFC Buffer configurations for a specific switch (bulk update)
    Request body:
    {
        "switch_sn": "string",
        "configurations": [
            {
                "config_id": "string",        # for update/delete (optional for create)
                "traffic_type": "ingress|egress",
                "sysname": "string",
                "port": ["string"],
                "queue": ["string"],
                "shared_ratio": float,
                "threshold": int,
                "guaranteed": int,           # ingress only
                "reset_offset": int,         # ingress only
                "headroom": int,             # ingress only
                "is_all_ports": bool,
                "is_all_queues": bool
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    switch_sn = data.get("switch_sn")
    configurations = data.get("configurations", [])

    if not switch_sn:
        raise ValueError("switch_sn is required")

    # Get switch for deployment using service layer
    switch = roce_db.get_switch_by_sn(switch_sn)
    if not switch:
        raise ValueError(f"Switch with SN {switch_sn} not found")

    # Get existing configurations for comparison
    existing_configs = roce_db.get_pfc_buffer_configurations_by_switch_sn(switch_sn)
    if len(existing_configs) > 0:
        old_val = json.loads(existing_configs[0].config_data)
    else:
        old_val = {"pfc_buffer": []}
    
    # Separate configurations by traffic type
    ingress_configs = []
    egress_configs = []
    
    for config in configurations:
        traffic_type = config.get("traffic_type", "").lower()
        if traffic_type == "ingress":
            ingress_configs.append(config)
        elif traffic_type == "egress":
            egress_configs.append(config)
    
    # Build configuration data for deployment using new unified view layer tool function
    config_data = _build_pfc_buffer_data(
        ingress_configs=ingress_configs,
        egress_configs=egress_configs,
        old_val=old_val
    )

    config_dict = {
        switch_sn: config_data
    }

    # Deploy configurations first (deploy first, then save to database)
    res = config_distribution_roce(config_dict)
    LOG.info(f"PFC Buffer config update result: {res}")
    if res.get("status") == 500 and res.get("info", None):
        return res
    
    err_info = res.get("err_info", {})
    errors = []
    
    # Check deployment result for this switch
    if switch_sn not in err_info:
        # Success: Update database configurations
        # Add config_data to each configuration for database operations
        for config in configurations:
            config["config_data"] = json.dumps(config_data.get("new_val"))
            
        # Execute database operations after successful deployment
        # Use service layer for bulk update operations
        roce_db.update_pfc_buffer_configurations(switch_sn, configurations)
        
        return jsonify({
            "status": 200,
            "msg": "PFC Buffer configurations updated successfully"
        })
    else:
        # Failed: Return error information
        errors.append({
            "status": 500,
            "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
            "switch": switch_sn
        })
        
        return jsonify({
            "status": 500,
            "msg": "Deploy PFC Buffer configurations failed",
            "data": errors
        })

def _build_pfc_buffer_data(ingress_configs=[], egress_configs=[], old_val={"pfc_buffer": []}):
    """
    Build PFC Buffer configuration data for deployment
    Args:
        ingress_configs: list, List of ingress configuration dicts (optional)
        egress_configs: list, List of egress configuration dicts (optional)
        old_val: dict, Old configuration data for comparison (optional)
    Returns:
        dict: Built configuration data containing new_val and old_val
    """
    # Initialize configuration structure
    new_val = {
        "pfc_buffer": {}
    }
    
    ingress_list = []
    # Process ingress configurations
    for config in ingress_configs:
        ingress_item = {
            "port": config.get("port", []),
            "queue": config.get("queue", []),
            "ingress_params": {
                "guaranteed": config.get("guaranteed"),
                "shared_ratio": config.get("shared_ratio"),
                "threshold": config.get("threshold"),
                "reset_offset": config.get("reset_offset"),
                "headroom": config.get("headroom")
            }
        }
        ingress_list.append(ingress_item)
    
    new_val["pfc_buffer"]["ingress"] = ingress_list
    
    # Process egress configurations
    egress_list = []
    for config in egress_configs:
        egress_item = {
            "port": config.get("port", []),
            "queue": config.get("queue", []),
            "egress_params": {
                "shared_ratio": config.get("shared_ratio"),
                "threshold": config.get("threshold")
            }
        }
        egress_list.append(egress_item)
    
    new_val["pfc_buffer"]["egress"] = egress_list
    
    return {
        "new_val": new_val,
        "old_val": old_val or {}
    }
    

@roce_mold.route('/pfc_buffer_config/list', methods=['post'])
@admin_permission.require(http_exception=403)
@api_exception_handler
def roce_pfc_buffer_config_list():
    """
    Get PFC Buffer configurations list with filtering and tree structure
    Request body:
    {
        "traffic_type": "ingress|egress",  # default: "ingress"
        "switch_sn": ["string"],  # optional: filter by switch serial numbers
        "port": ["string"],  # optional: filter by port names
        "searchFields": {  # optional: search conditions
            "fields": ["port"],
            "value": "string"
        },
        "page": 1,  # pagination
        "pageSize": 10
    }
    Returns:
        JSON response with tree-structured data and pagination
    """
    data = request.get_json()
    
    # Get configurations list data via service layer
    result = roce_db.get_pfc_buffer_configs_list_data(data)
    
    return jsonify({
        "status": 200,
        "data": result.get("tree_data", []),
        "page": result.get("page"),
        "pageSize": result.get("pageSize"),
        "total": result.get("total")
    })
    
@roce_mold.route('/pfc_buffer_config/delete', methods=['POST'])
@admin_permission.require(http_exception=403)
@api_exception_handler
def pfc_buffer_config_delete():
    """
    Delete PFC Buffer configuration
    Request body:
    {
        "config_id": "string",
        "traffic_type": "ingress|egress"
    }
    """
    # Get request data
    request_data = request.get_json()
    config_id = request_data.get("config_id")
    traffic_type = request_data.get("traffic_type")
    
    if not config_id:
        raise ValueError("config_id is required")
    if not traffic_type:
        raise ValueError("traffic_type is required")
    
    # Call service layer to get configuration and switch info for deletion
    result = roce_db.get_pfc_buffer_config_for_deletion(config_id, traffic_type)
    
    config = result.get("config")
    switch = result.get("switch")

    if config.config_data:
    
        stored_config_data = json.loads(config.config_data)
        
        # Find matching configuration by traffic_type and port
        old_val = None
        if traffic_type == "ingress":
            for i, ingress_config in enumerate(stored_config_data.get("pfc_buffer", {}).get("ingress", [])):
                if ingress_config.get("port") == config.port:
                    old_val = {"pfc_buffer": {"ingress": [ingress_config]}}
                    # Remove the matching configuration from the original config_data
                    stored_config_data.get("pfc_buffer", {}).get("ingress", []).pop(i)
                    break
        elif traffic_type == "egress":
            for i, egress_config in enumerate(stored_config_data.get("pfc_buffer", {}).get("egress", [])):
                if egress_config.get("port") == config.port:
                    old_val = {"pfc_buffer": {"egress": [egress_config]}}
                    # Remove the matching configuration from the original config_data
                    stored_config_data.get("pfc_buffer", {}).get("egress", []).pop(i)
                    break

        
        # Build delete configuration data using unified view layer tool function
        config_data = _build_pfc_buffer_data(
            ingress_configs=[],
            egress_configs=[],
            old_val=old_val
        )

        config_dict = {
            switch.sn: config_data
        }

    # Deploy configurations first (deploy first, then save to database)
    res = config_distribution_roce(config_dict)
    LOG.info(f"PFC Buffer config delete result: {res}")

    if switch.reachable_status == 1:

        roce_db.execute_pfc_buffer_config_deletion(config_id, traffic_type, stored_config_data)
        
        return jsonify({
            "status": 200,
            "msg": "PFC Buffer configuration deleted successfully"
        })

    if res.get("status") == 500 and res.get("info", None):
        return res
    
    err_info = res.get("err_info", {})
    errors = []
    
    # Check deployment result for this switch
    if switch.sn not in err_info:
        # Success: Delete the configuration from database after successful deployment
        # Pass updated pfc_buffer_data to sync to remaining configurations
        roce_db.execute_pfc_buffer_config_deletion(config_id, traffic_type, stored_config_data)
        
        return jsonify({
            "status": 200,
            "msg": "PFC Buffer configuration deleted successfully"
        })
    else:
        errors.append({
            "status": 500,
            "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
            "switch": switch.sn
        })
        
        return jsonify({
            "status": 500,
            "msg": "Deploy PFC Buffer configuration deletion failed",
            "data": errors
        })

@roce_mold.route("/pfc_buffer_config/detail_by_switch", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def pfc_buffer_config_detail_by_switch():
    """
    Get all PFC Buffer configuration records for a specific switch
    Request body:
    {
        "switch_sn": "string"
    }
    Response:
    {
        "status": 200,
        "msg": "Get pfc buffer config detail successfully",
        "data": {
            "ingress": [configuration list],
            "egress": [configuration list]
        }
    }
    """
    request_json = request.get_json()
    
    if not request_json:
        raise ValueError("No request data provided")
    
    switch_sn = request_json.get("switch_sn")
    if not switch_sn:
        raise ValueError("switch_sn is required")
    
    # Get configurations detail via service layer
    result = roce_db.get_pfc_buffer_configs_detail_by_switch_sn(switch_sn)
    
    return jsonify({
        "status": 200,
        "msg": "Get pfc buffer config detail successfully",
        "data": {
            "ingress": result.get("ingress", []),
            "egress": result.get("egress", [])
        }
    })

@roce_mold.route("/pfc_wd_config/save", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def pfc_wd_config_save():
    data = request.get_json()

    if not data or "configurations" not in data:
        raise ValueError("No configuration data provided")
    
    configurations = data.get("configurations", [])
    
    if not configurations:
        raise ValueError("No configurations provided")
    
    # Group configurations by switch_sn and prepare for deployment
    switch_configs = {}
    
    for config in configurations:
        switch_sn = config.get("switch_sn")
        if not switch_sn:
            raise ValueError("switch_sn is required")
        
        if switch_sn not in switch_configs:
            switch_configs[switch_sn] = {
                "configs": []
            }
        
        switch_configs[switch_sn]["configs"].append(config)
    
    config_dict = {}
    db_configurations = {}
    errors = []

    for switch_sn, switch_data in switch_configs.items():
        # Get switch for deployment using service layer
        switch = roce_db.get_switch_by_sn(switch_sn)
        if not switch:
            raise ValueError(f"Switch with SN {switch_sn} not found")
    
        enhanced_configs = switch_data.get("configs")
        # Build configuration data using view layer tool function
        config_data = _build_pfc_wd_config_data(enhanced_configs)

        config_dict[switch_sn] = config_data
        db_configurations[switch_sn] = []
        # Prepare database records for this switch
        for config in enhanced_configs:
            db_config = {
                "sysname": config.get("sysname"),
                "switch_sn": switch_sn,
                "port": config.get("port"),
                "queue": config.get("queue"),
                "enabled": config.get("enabled"),
                "granularity": config.get("granularity"),
                "detection_interval": config.get("detection_interval"),
                "restore_interval": config.get("restore_interval"),
                "threshold_period": config.get("threshold_period"),
                "threshold_count": config.get("threshold_count"),
                "restore_mode": config.get("restore_mode"),
                "restore_action": config.get("restore_action"),
                "is_all_ports": config.get("is_all_ports", False),
                "is_all_queues": config.get("is_all_queues", False),
                "config_data": json.dumps(config_data.get("new_val"))
            }
            db_configurations[switch_sn].append(db_config)

    res = config_distribution_roce(config_dict)
    LOG.info(f"PFC WD config save result: {res}")
    if res.get("status") == 500 and res.get("info", None):
        return res
    
    err_info = res.get("err_info", {})
    # Save all configurations to database via service layer
    for switch_sn, switch_data in switch_configs.items():
        if switch_sn not in err_info:
            # Convert to format expected by save_pfc_wd_configurations
            configs_for_db = db_configurations.get(switch_sn, [])
            config_data_list = [{"new_val": config_dict[switch_sn]["new_val"]} for _ in configs_for_db]
            roce_db.save_pfc_wd_configurations(configs_for_db, config_data_list)
        else:
            errors.append({
                "status": 500,
                "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
                "switch": switch_sn
            })

    if errors:
        return jsonify({
            "status": 500,
            "msg": "Deploy PFC Watchdog configurations failed",
            "data": errors
        })
    
    return jsonify({
        "status": 200,
        "msg": "PFC Watchdog configurations saved successfully",
        "data": {}
    })


def pfc_wd_is_delete(config_data, existing_config):
    session = roce_db.get_session()
    deletable_fields = {
        "restore_action": ("forward", "is_delete_action"),
        "granularity": ("100", "is_delete_granularity"),
        "threshold_period": ("20", "is_delete_period"),
        "threshold_count": ("30", "is_delete_count")
    }
    for index, config in enumerate(config_data["old_val"]["pfc_watchdog"]):
        watchdog_params = config["watchdog_params"]

        for field, (default_val, flag_key) in deletable_fields.items():
            val = getattr(existing_config, field)
            watchdog_params[field] = val
            if val and val != default_val:
                count = session.query(PfcWdConfiguration).filter(
                    PfcWdConfiguration.switch_sn == existing_config.switch_sn,
                    getattr(PfcWdConfiguration, field) == val
                ).count()
                if count == 1:
                    config_data["old_val"]["pfc_watchdog"][index][flag_key] = True
    return config_data

@roce_mold.route("/pfc_wd_config/update", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def pfc_wd_config_update():
    """
    Update PFC Watchdog configurations for a specific switch (bulk update)
    Request body:
    {
        "switch_sn": "string",
        "configurations": [
            {
                "config_id": "string",  # for update/delete
                "sysname": "string",
                "port": ["string"],
                "queue": ["string"],
                "enabled": true,
                "granularity": "string",
                "detection_interval": "string",
                "restore_interval": "string",
                "threshold_period": "string",
                "threshold_count": "string",
                "restore_mode": "string",
                "restore_action": "string",
                "is_all_ports": false,
                "is_all_queues": false
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    switch_sn = data.get("switch_sn")
    configurations = data.get("configurations", [])

    if not switch_sn:
        raise ValueError("switch_sn is required")

    # Get switch for deployment using service layer
    switch = roce_db.get_switch_by_sn(switch_sn)
    if not switch:
        raise ValueError(f"Switch with SN {switch_sn} not found")

    # Get existing configurations for comparison
    existing_configs = roce_db.get_pfc_wd_configurations_by_switch_sn(switch_sn)
    if len(existing_configs) > 0:
        try:
            old_val = json.loads(existing_configs[0].config_data)
        except (json.JSONDecodeError, TypeError):
            old_val = {"pfc_watchdog": []}
    else:
        old_val = {"pfc_watchdog": []}
    
    # Build configuration data for deployment
    config_data = _build_pfc_wd_config_data(configurations, old_val)

    config_dict = {
        switch_sn: config_data
    }

    res = config_distribution_roce(config_dict)
    LOG.info(f"PFC WD config update result: {res}")
    if res.get("status") == 500 and res.get("info", None):
        return res
    
    err_info = res.get("err_info", {})
    errors = []
    
    # Check deployment result for this switch
    if switch_sn not in err_info:
        # Success: Update database configurations
        for config in configurations:
            config["config_data"] = json.dumps(config_data.get("new_val"))
            
        # Execute database operations after successful deployment
        roce_db.update_pfc_wd_configurations(switch_sn, configurations)
        
        return jsonify({
            "status": 200,
            "msg": "PFC Watchdog configurations updated successfully"
        })
    else:
        # Failed: Return error information
        errors.append({
            "status": 500,
            "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
            "switch": switch_sn
        })
        
        return jsonify({
            "status": 500,
            "msg": "Deploy PFC Watchdog configurations failed",
            "data": errors
        })

def _build_pfc_wd_config_data(configs, old_val={"pfc_watchdog": []}):
    """
    Build PFC Watchdog configuration data for deployment
    Args:
        configs: list, List of PFC Watchdog configurations (empty for delete operations)
        old_val: dict, Old configuration data for comparison
    Returns:
        dict: Built configuration data
    """
    config_data = {
        "new_val": {
            "pfc_watchdog": []
        },
        "old_val": old_val
    }
    
    for config in configs:
        config_data["new_val"]["pfc_watchdog"].append({
            "port": config.get("port", []),
            "code_point": config.get("queue", []),
            "enable": str(config.get("enabled")).lower(),
            "restore_mode": config.get("restore_mode", None),
            "watchdog_params": {
                "granularity": config.get("granularity", ""),
                "restore_action": config.get("restore_action", None),
                "detect_interval": config.get("detection_interval", ""),
                "restore_interval": config.get("restore_interval", ""),
                "threshold_period": config.get("threshold_period", ""),
                "threshold_count": config.get("threshold_count", "")
            }
        })
        
    return config_data

@roce_mold.route("/pfc_wd_config/list", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def pfc_wd_config_list():
    """
    Get PFC Watchdog configurations list with tree structure
    Request body:
    {
        "searchFields": {
            "fields": ["port"],
            "value": "search_value"
        },
        "page": 1,
        "pageSize": 10
    }
    Returns:
        JSON response with paginated tree structure data
    """
    data = request.get_json()
    
    if not data:
        raise ValueError("No query parameters provided")
    
    # Get configurations list via service layer
    result = roce_db.get_pfc_wd_configs_list_data(data)
    
    return jsonify({
        "status": 200,
        "data": result.get("tree_data", []),
        "page": result.get("page"),
        "pageSize": result.get("pageSize"),
        "total": result.get("total")
    })


@roce_mold.route("/pfc_wd_config/detail_by_switch", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def pfc_wd_config_detail_by_switch():
    """
    Get all PFC Watchdog configuration records for a specific switch
    Request body:
    {
        "switch_sn": "string"
    }
    Response:
    {
        "status": 200,
        "msg": "Get pfc wd config detail successfully",
        "data": [configuration list]
    }
    """
    data = request.get_json()
    
    if not data:
        raise ValueError("No request data provided")
    
    switch_sn = data.get("switch_sn")
    if not switch_sn:
        raise ValueError("switch_sn is required")
    
    # Get configurations detail via service layer
    configs = roce_db.get_pfc_wd_configs_detail_by_switch_sn(switch_sn)
    
    return jsonify({
        "status": 200,
        "msg": "Get pfc wd config detail successfully",
        "data": configs
    })


@roce_mold.route("/pfc_wd_config/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def pfc_wd_config_delete():
    """
    Delete PFC Watchdog configuration.
    
    Request body:
    {
        "config_id": "string"
    }
    """
    # Get request data
    request_data = request.get_json()
    config_id = request_data.get("config_id")
    
    if not config_id:
        raise ValueError("config_id is required")
    
    # Call service layer to get configuration and switch info for deletion
    result = roce_db.get_pfc_wd_configuration_for_deletion(config_id)
    
    config = result.get("config")
    switch = result.get("switch")
    
    if config.config_data:
        
        stored_config_data = json.loads(config.config_data)
        
        # Find matching configuration by port and queue
        old_val = None
        for wd_config in stored_config_data.get("pfc_watchdog", []):
            if (wd_config.get("port") == config.port and 
                wd_config.get("code_point") == config.queue):
                old_val = {"pfc_watchdog": [wd_config]}
                stored_config_data.get("pfc_watchdog", []).remove(wd_config)
                break
        
        if not old_val:
            raise ValueError("PFC Watchdog configuration not found in stored data")
            
    
    # Build delete configuration data using _build_pfc_wd_config_data function
    config_data = _build_pfc_wd_config_data([], old_val)

    config_dict = {
        switch.sn: config_data
    }

    res = config_distribution_roce(config_dict)
    LOG.info(f"PFC WD config delete result: {res}")

    if switch.reachable_status == 1:
        roce_db.delete_pfc_wd_configuration(config_id, switch.sn, stored_config_data)
        
        return jsonify({
            "status": 200,
            "msg": "Deploy PFC Watchdog configuration deleted successfully"
        })

    if res.get("status") == 500 and res.get("info", None):
        return res
    
    err_info = res.get("err_info", {})
    errors = []
    
    # Check deployment result for this switch
    if switch.sn not in err_info:
        # Success: Delete the configuration from database after successful deployment
        roce_db.delete_pfc_wd_configuration(config_id, switch.sn, stored_config_data)
        
        return jsonify({
            "status": 200,
            "msg": "Deploy PFC Watchdog configuration deleted successfully"
        })
    else:
        
        return jsonify({
            "status": 500,
            "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
            "data": errors
        })

@roce_mold.route("/ecn_config/save", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
@validate_ecn_uniqueness
def ecn_config_save():
    """
    Save ECN configurations
    Request body:
    {
        "configurations": [
            {
                "sysname": "string",
                "switch_sn": "string", 
                "enabled": bool,
                "mode": "string",
                "details": [
                    {
                        "port": ["string"],
                        "queue": ["string"],
                        "max_threshold": int,
                        "min_threshold": int,
                        "drop_probability": int,
                        "ecn_threshold": int,
                        "wred_enable": bool,
                        "is_all_ports": bool,
                        "is_all_queues": bool
                    }
                ]
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    configurations = data.get("configurations", [])
    
    if not configurations:
        raise ValueError("No configurations provided")
    
    # Group configurations by switch_sn and prepare for deployment
    switch_configs = {}
    
    for config in configurations:
        switch_sn = config.get("switch_sn")
        if not switch_sn:
            raise ValueError("switch_sn is required")
        
        if switch_sn not in switch_configs:
            switch_configs[switch_sn] = {
                "configs": []
            }
        
        switch_configs[switch_sn]["configs"].append(config)
    
    config_dict = {}
    db_configurations = []

    for switch_sn, switch_data in switch_configs.items():
        # Get switch for deployment using service layer
        switch = roce_db.get_switch_by_sn(switch_sn)
        if not switch:
            raise ValueError(f"Switch with SN {switch_sn} not found")
    
        enhanced_configs = switch_data.get("configs")
        
        # Process each configuration for this switch
        for config in enhanced_configs:
            details = config.get("details", [])
            
            # Build configuration data using view layer tool function
            config_data = _build_ecn_config_data(
                ecn_config=config, 
                ecn_config_details=details
            )

            config_dict[switch_sn] = config_data
            
            # Prepare database records
            db_config = {
                "sysname": config.get("sysname"),
                "switch_sn": switch_sn,
                "enabled": config.get("enabled"),
                "mode": config.get("mode"),
                "details": details,
                "config_data": json.dumps(config_data.get("new_val"))
            }
            db_configurations.append(db_config)

    # Deploy all configurations first (follow "deploy first, then save" pattern)
    res = config_distribution_roce(config_dict)
    LOG.info(f"ECN config save result: {res}")
    if res.get("status") == 500 and res.get("info", None):
        return res
    
    err_info = res.get("err_info", {})
    errors = []
    
    # Group database configurations by switch_sn for conditional saving
    switch_db_configs = {}
    for db_config in db_configurations:
        switch_sn = db_config["switch_sn"]
        if switch_sn not in switch_db_configs:
            switch_db_configs[switch_sn] = []
        switch_db_configs[switch_sn].append(db_config)
    
    # Save configurations to database only for successful deployments
    for switch_sn, switch_data in switch_configs.items():
        switch = roce_db.get_switch_by_sn(switch_sn)
        if switch_sn not in err_info:
            # Success: Save configurations to database
            roce_db.save_ecn_configurations(switch_db_configs.get(switch_sn, []))
        else:
            # Failed: Record error information
            errors.append({
                "status": 500,
                "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
                "switch": switch_sn
            })
    
    if errors:
        return jsonify({
            "status": 500,
            "msg": "Deploy ECN configurations failed",
            "data": errors
        })
    
    return jsonify({
        "status": 200,
        "msg": "ECN configurations saved successfully",
        "data": {}
    })


@roce_mold.route("/ecn_config/update", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
@validate_ecn_uniqueness
def ecn_config_update():
    """
    Update ECN configurations for a specific switch (bulk update)
    Request body:
    {
        "switch_sn": "string",
        "configurations": [
            {
                "config_id": "string",  # for update/delete
                "sysname": "string",
                "enabled": bool,
                "mode": "string",
                "details": [
                    {
                        "port": ["string"],
                        "queue": ["string"],
                        "max_threshold": int,
                        "min_threshold": int,
                        "drop_probability": int,
                        "ecn_threshold": int,
                        "wred_enable": bool,
                        "is_all_ports": bool,
                        "is_all_queues": bool
                    }
                ]
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    switch_sn = data.get("switch_sn")
    configurations = data.get("configurations", [])

    if not switch_sn:
        raise ValueError("switch_sn is required")

    # Get switch for deployment using service layer
    switch = roce_db.get_switch_by_sn(switch_sn)
    if not switch:
        raise ValueError(f"Switch with SN {switch_sn} not found")

    # Get existing configurations for comparison
    existing_config = roce_db.get_ecn_main_config_by_switch_sn(switch_sn)
    if not existing_config:
        raise ValueError(f"ECN configuration not found for switch {switch_sn}")

    if existing_config.config_data:
        old_val = json.loads(existing_config.config_data)
    else:
        old_val = {"ecn": {}}
    
    # Build configuration data for deployment using view layer tool function
    all_details = []
    for config in configurations:
        all_details.extend(config.get("details", []))
    
    config_data = _build_ecn_config_data(
        ecn_config=configurations[0],
        ecn_config_details=all_details,
        old_val=old_val
    )

    config_dict = {
        switch_sn: config_data
    }

    # Deploy configurations first (follow "deploy first, then save" pattern)
    res = config_distribution_roce(config_dict)
    LOG.info(f"ECN config update result: {res}")
    if res.get("status") == 500 and res.get("info", None):
        return res
    
    err_info = res.get("err_info", {})
    errors = []
    
    # Check deployment result for this switch
    if switch_sn not in err_info:
        # Success: Update database configurations
        # Add config_data to each configuration for database update
        for config in configurations:
            config["config_data"] = json.dumps(config_data.get("new_val"))
            
        # Execute database operations after successful deployment
        roce_db.update_ecn_configurations(switch_sn, configurations)
        
        return jsonify({
            "status": 200,
            "msg": "ECN configurations updated successfully"
        })
    else:
        # Failed: Return error information
        errors.append({
            "status": 500,
            "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
            "switch": switch_sn
        })
        
        return jsonify({
            "status": 500,
            "msg": "Deploy ECN configurations failed",
            "data": errors
        })


def _build_ecn_config_data(ecn_config={}, ecn_config_details=[], old_val={"ecn": {}}):
    """
    Build ECN configuration data for deployment
    Args:
        ecn_config: dict, ECN main configuration info
        ecn_config_detail: dict, ECN detail configuration info (can be None for main config only)
        existing_detail_config: object, Existing detail configuration object (can be None)
    Returns:
        dict: ECN configuration data for deployment
    """
    config_data = {
        "new_val": {    
            "ecn": {
                "mode": ecn_config.get("mode", None),
                "details": []
            }
        },
        "old_val": old_val
    }

    for detail in ecn_config_details:
        detail_data = {
            "port": detail.get("port", []),
            "queue": detail.get("queue", []),
            "enable": str(detail.get("wred_enable", False)).lower(),
            "ecn_params": {
                "max_thresh": detail.get("max_threshold", None),
                "min_thresh": detail.get("min_threshold", None),
                "drop_probability": detail.get("drop_probability", None),
                "ecn_thresh": detail.get("ecn_threshold", None)
            }
        }
        config_data["new_val"]["ecn"]["details"].append(detail_data)

    
    return config_data

@roce_mold.route("/ecn_config/list", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def ecn_config_list():
    """
    Get ECN configurations list with pagination
    Request body:
    {
        "search": "string",  # optional: search text
        "page": 1,           # optional: page number
        "pageSize": 10       # optional: page size
    }
    Returns:
        JSON response with flat list data and pagination
    """
    data = request.get_json()
    
    # Get configurations list data via service layer
    result = roce_db.get_ecn_configs_list_data(data)
    
    return jsonify({
        "status": 200,
        "data": result.get("data", []),
        "page": result.get("page", 1),
        "pageSize": result.get("pageSize", 10),
        "total": result.get("total", 0)
    })

@roce_mold.route("/ecn_config/port/list", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def ecn_port_config_list():
    """
    Get ECN port configurations list with tree structure and pagination
    Request body:
    {
        "page": 1,           # optional: page number
        "pageSize": 10       # optional: page size
    }
    Returns:
        JSON response with tree-structured data and pagination
    """
    data = request.get_json()
    
    # Get ECN port configurations list data via service layer
    result = roce_db.get_ecn_port_configs_list_data(data)
    
    return jsonify({
        "status": 200,
        "data": result.get("tree_data", []),
        "page": result.get("page", 1),
        "pageSize": result.get("pageSize", 10),
        "total": result.get("total", 0)
    })

@roce_mold.route("/ecn_config/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def ecn_config_delete():
    """
    Delete ECN configuration.
    
    Request body:
    {
        "config_id": "string"
    }
    """
    # Get request data
    request_data = request.get_json()
    config_id = request_data.get("config_id")
    
    if not config_id:
        raise ValueError("config_id is required")
    
    # Call service layer to get configuration and switch info for deletion
    result = roce_db.get_ecn_configuration_for_deletion(config_id)
    
    config = result.get("config")
    switch = result.get("switch")
    
    if config.config_data:
        old_val = json.loads(config.config_data)

        # Build delete configuration data using view layer tool function
        config_data = _build_ecn_config_data({}, [], old_val)

        config_dict = {
            switch.sn: config_data
        }

        # Deploy delete configuration first (follow "deploy first, then save" pattern)
        res = config_distribution_roce(config_dict)
        LOG.info(f"ECN config delete result: {res}")
        
        if switch.reachable_status == 1:
            roce_db.delete_ecn_configuration(config_id)
            
            return jsonify({
                "status": 200,
                "msg": "ECN configuration deleted successfully"
            })

        if res.get("status") == 500 and res.get("info", None):
            return res
        
        err_info = res.get("err_info", {})
        errors = []
        
        # Check deployment result for this switch
        if switch.sn not in err_info:
            # Success: Delete the configuration from database after successful deployment
            roce_db.delete_ecn_configuration(config_id)
            
            return jsonify({
                "status": 200,
                "msg": "ECN configuration deleted successfully"
            })
        else:
            # Failed: Return error information
            errors.append({
                "status": 500,
                "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
                "switch": switch.sn
            })
            
            return jsonify({
                "status": 500,
                "msg": "Deploy ECN configuration deletion failed",
                "data": errors
            })
    else:
        # No config data to deploy, just delete from database
        roce_db.delete_ecn_configuration(config_id)
        
        return jsonify({
            "status": 200,
            "msg": "ECN configuration deleted successfully"
        })

@roce_mold.route("/ecn_config/detail_by_switch", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def ecn_config_detail_by_switch():
    """
    Query all ECN configuration records for a specific switch by switch_sn
    Request body:
    {
        "switch_sn": "string"
    }
    Response:
    {
        "status": 200,
        "msg": "Get ecn config detail successfully",
        "data": [configuration list]
    }
    """
    data = request.get_json()
    
    if not data:
        raise ValueError("No request data provided")
    
    switch_sn = data.get("switch_sn")
    if not switch_sn:
        raise ValueError("switch_sn is required")

    # Get ECN configurations detail via service layer
    configs = roce_db.get_ecn_configs_detail_by_switch_sn(switch_sn)

    return jsonify({
        "status": 200,
        "msg": "Get ecn config detail successfully",
        "data": configs
    })


def validate_qos_basic_config(config):
    """
    Validate single QoS basic configuration
    Args:
        config: dict, Single QoS basic configuration
    Raises:
        ValueError: If validation fails
    """
    switch_sn = config.get("switch_sn")
    forwarding_class = config.get("forwarding_class")
    scheduler = config.get("scheduler")
    config_id = config.get("config_id")
    
    if not switch_sn:
        raise ValueError("switch_sn is required")
    if not forwarding_class:
        raise ValueError("forwarding_class is required")
    
    # Check if forwarding_class already exists
    if roce_db.check_qos_forwarding_class_exists(switch_sn, forwarding_class, config_id):
        raise ValueError(f"Forwarding class '{forwarding_class}' already exists on switch {switch_sn}")


def validate_qos_ingress_config(ingress_config, existing_forwarding_classes=None):
    """
    Validate single QoS ingress configuration
    Args:
        ingress_config: dict, Single QoS ingress configuration
        existing_forwarding_classes: list, List of existing forwarding classes for the switch
    Raises:
        ValueError: If validation fails
    """
    switch_sn = ingress_config.get("switch_sn")
    classifier = ingress_config.get("classifier")
    forwarding_class = ingress_config.get("forwarding_class")
    config_id = ingress_config.get("config_id")
    
    if not switch_sn:
        raise ValueError("switch_sn is required in ingress configuration")
    if not classifier:
        raise ValueError("classifier is required in ingress configuration")
    if not forwarding_class:
        raise ValueError("forwarding_class is required in ingress configuration")
    
    # Check if classifier-forwarding_class combination already exists (one-to-many relationship: one classifier can correspond to multiple forwarding_classes)
    if roce_db.check_qos_classifier_forwarding_class_exists(switch_sn, classifier, forwarding_class, config_id):
        raise ValueError(f"Classifier '{classifier}' and forwarding class '{forwarding_class}' combination already exists on switch {switch_sn}")
    
    # Check if corresponding forwarding_class exists
    if existing_forwarding_classes and forwarding_class not in existing_forwarding_classes:
        raise ValueError(f"Forwarding class '{forwarding_class}' referenced in ingress configuration does not exist in main configurations for switch {switch_sn}")


def validate_qos_egress_config(egress_config, scheduler_to_forwarding_classes=None):
    """
    Validate single QoS egress configuration
    Args:
        egress_config: dict, Single QoS egress configuration
        scheduler_to_forwarding_classes: dict, Mapping of scheduler to forwarding classes
    Raises:
        ValueError: If validation fails
    """
    switch_sn = egress_config.get("switch_sn")
    scheduler_profile = egress_config.get("scheduler_profile")
    scheduler = egress_config.get("scheduler")
    config_id = egress_config.get("config_id")
    egress_forwarding_class = egress_config.get("forwarding_class")
    
    if not switch_sn:
        raise ValueError("switch_sn is required in egress configuration")
    if not scheduler_profile:
        raise ValueError("scheduler_profile is required in egress configuration")
    if not scheduler:
        raise ValueError("scheduler is required in egress configuration")
    if not egress_forwarding_class:
        raise ValueError("forwarding_class is required in egress configuration")
    
    # Check if scheduler_profile-forwarding_class-scheduler combination already exists
    # Note: scheduler_profile can be repeated, but in the same scheduler_profile, one forwarding_class can only correspond to one scheduler
    if roce_db.check_qos_scheduler_profile_forwarding_class_exists(switch_sn, scheduler_profile, egress_forwarding_class, scheduler, config_id):
        raise ValueError(f"Scheduler profile '{scheduler_profile}' with forwarding class '{egress_forwarding_class}' and scheduler '{scheduler}' combination already exists on switch {switch_sn}")
    
    # Check if scheduler exists and is associated with the forwarding_class
    if scheduler_to_forwarding_classes:
        scheduler_found = False
        if scheduler in scheduler_to_forwarding_classes:
            if egress_forwarding_class in scheduler_to_forwarding_classes[scheduler]:
                scheduler_found = True
        
        if not scheduler_found:
            raise ValueError(f"Scheduler '{scheduler}' is not associated with forwarding class '{egress_forwarding_class}' on switch {switch_sn}")


def validate_qos_uniqueness(f):
    """
    Decorator: Validate QoS configuration uniqueness using the three separate validation functions
    Business Rules:
    1. forwarding_class cannot be duplicated on a single switch
    2. one scheduler can correspond to multiple forwarding_classes
    3. one forwarding_class can only correspond to one scheduler
    4. classifier in ingress configuration can correspond to multiple forwarding_classes (one-to-many relationship)
    5. scheduler_profile in egress configuration can be repeated, but in the same scheduler_profile, one forwarding_class can only correspond to one scheduler
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        data = request.get_json()
        if not data:
            raise ValueError("No configuration data provided")

        configurations = data.get("configurations", [])
        ingress_configurations = data.get("ingress_configurations", [])
        egress_configurations = data.get("egress_configurations", [])
        
        # Validate data types
        if not isinstance(configurations, list):
            raise ValueError("configurations must be a list")
        if not isinstance(ingress_configurations, list):
            raise ValueError("ingress_configurations must be a list")
        if not isinstance(egress_configurations, list):
            raise ValueError("egress_configurations must be a list")
        
        if not configurations:
            raise ValueError("No configurations provided")

        # Group configurations by switch
        switch_configs = {}
        switch_ingress = {}
        switch_egress = {}
        
        # Collect main configurations
        for config in configurations:
            switch_sn = config.get("switch_sn")
            forwarding_class = config.get("forwarding_class")
            scheduler = config.get("scheduler")
            config_id = config.get("config_id")
            
            if not switch_sn:
                raise ValueError("switch_sn is required")
            if not forwarding_class:
                raise ValueError("forwarding_class is required")
            
            if switch_sn not in switch_configs:
                switch_configs[switch_sn] = []
            switch_configs[switch_sn].append({
                "forwarding_class": forwarding_class,
                "scheduler": scheduler,
                "config_id": config_id
            })
        
        # Collect ingress configurations
        for ingress in ingress_configurations:
            switch_sn = ingress.get("switch_sn")
            classifier = ingress.get("classifier")
            forwarding_class = ingress.get("forwarding_class")
            config_id = ingress.get("config_id")
            
            if not switch_sn:
                raise ValueError("switch_sn is required in ingress configuration")
            if not classifier:
                raise ValueError("classifier is required in ingress configuration")
            if not forwarding_class:
                raise ValueError("forwarding_class is required in ingress configuration")
            
            if switch_sn not in switch_ingress:
                switch_ingress[switch_sn] = []
            switch_ingress[switch_sn].append({
                "classifier": classifier,
                "forwarding_class": forwarding_class,
                "config_id": config_id
            })
        
        # Collect egress configurations
        for egress in egress_configurations:
            switch_sn = egress.get("switch_sn")
            scheduler_profile = egress.get("scheduler_profile")
            scheduler = egress.get("scheduler")
            config_id = egress.get("config_id")
            
            if not switch_sn:
                raise ValueError("switch_sn is required in egress configuration")
            if not scheduler_profile:
                raise ValueError("scheduler_profile is required in egress configuration")
            if not scheduler:
                raise ValueError("scheduler is required in egress configuration")
            
            if switch_sn not in switch_egress:
                switch_egress[switch_sn] = []
            switch_egress[switch_sn].append({
                "scheduler_profile": scheduler_profile,
                "scheduler": scheduler,
                "config_id": config_id,
                "forwarding_class": egress.get("forwarding_class") # Include forwarding_class for validation
            })
        
        # Validate uniqueness for each switch configuration
        for switch_sn in set(list(switch_configs.keys()) + list(switch_ingress.keys()) + list(switch_egress.keys())):
            # 1. Validate forwarding_class uniqueness and scheduler relationship
            forwarding_classes = []
            scheduler_to_forwarding_classes = {}  # Track scheduler -> forwarding_classes mapping
            
            for config in switch_configs.get(switch_sn, []):
                fc = config["forwarding_class"]
                scheduler = config.get("scheduler")
                config_id = config.get("config_id")
                
                # Check if forwarding_class already exists
                if roce_db.check_qos_forwarding_class_exists(switch_sn, fc, config_id):
                    raise ValueError(f"Forwarding class '{fc}' already exists on switch {switch_sn}")
                
                if fc in forwarding_classes:
                    raise ValueError(f"Duplicate forwarding class '{fc}' found in configurations for switch {switch_sn}")
                forwarding_classes.append(fc)
                
                # Track scheduler to forwarding_class relationship
                if scheduler:
                    if scheduler not in scheduler_to_forwarding_classes:
                        scheduler_to_forwarding_classes[scheduler] = []
                    scheduler_to_forwarding_classes[scheduler].append(fc)
            
            # 2. Validate one-to-many correspondence between ingress classifier and forwarding_class
            ingress_configs = switch_ingress.get(switch_sn, [])
            for ingress in ingress_configs:
                classifier = ingress["classifier"]
                fc = ingress["forwarding_class"]
                config_id = ingress.get("config_id")
                
                # Check if classifier-forwarding_class combination already exists (one-to-many relationship)
                if roce_db.check_qos_classifier_forwarding_class_exists(switch_sn, classifier, fc, config_id):
                    raise ValueError(f"Classifier '{classifier}' and forwarding class '{fc}' combination already exists on switch {switch_sn}")
                
                # Check if corresponding forwarding_class exists
                if fc not in forwarding_classes:
                    raise ValueError(f"Forwarding class '{fc}' referenced in ingress configuration does not exist in main configurations for switch {switch_sn}")
            
            # 3. Validate egress scheduler_profile and scheduler relationship
            egress_configs = switch_egress.get(switch_sn, [])
            for egress in egress_configs:
                scheduler_profile = egress["scheduler_profile"]
                scheduler = egress["scheduler"]
                config_id = egress.get("config_id")
                egress_forwarding_class = egress.get("forwarding_class")
                
                # Check if scheduler_profile-forwarding_class-scheduler combination already exists
                # Note: scheduler_profile can be repeated, but in the same scheduler_profile, one forwarding_class can only correspond to one scheduler
                if roce_db.check_qos_scheduler_profile_forwarding_class_exists(switch_sn, scheduler_profile, egress_forwarding_class, scheduler, config_id):
                    raise ValueError(f"Scheduler profile '{scheduler_profile}' with forwarding class '{egress_forwarding_class}' and scheduler '{scheduler}' combination already exists on switch {switch_sn}")
                
                # Check if scheduler exists and is associated with the forwarding_class
                scheduler_found = False
                if scheduler in scheduler_to_forwarding_classes:
                    if egress_forwarding_class in scheduler_to_forwarding_classes[scheduler]:
                        scheduler_found = True
                
                if not scheduler_found:
                    raise ValueError(f"Scheduler '{scheduler}' is not associated with forwarding class '{egress_forwarding_class}' on switch {switch_sn}")

        # Validation passed, execute original function
        return f(*args, **kwargs)
            
    return decorated_function

@roce_mold.route("/qos_config/save", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
# @validate_qos_uniqueness
def qos_config_save():
    """
    Save QoS configurations with enhanced architecture
    Request body:
    {
        "configurations": [
            {
                "sysname": "switch-01",
                "switch_sn": "string",
                "forwarding_class": "fc1",
                "local_priority": 1,
                "scheduler": "Ethernet1/1",
                "mode": "SP",
                "weight": 10,
                "guaranteed_rate": 100
            }
        ],
        "ingress_configurations": [
            {
                "sysname": "switch-01",
                "switch_sn": "string",
                "classifier": "fc1",
                "trust_mode": "dscp",
                "port": ["Ethernet1/1"],
                "forwarding_class": "fc1",
                "queue": ["3"],
                "is_all_ports": false,
                "is_all_queues": false
            }
        ],
        "egress_configurations": [
            {
                "sysname": "switch-01",
                "switch_sn": "string",
                "scheduler_profile": "profile1",
                "scheduler": "scheduler1",
                "port": ["Ethernet1/1"],
                "forwarding_class": "fc1",
                "local_priority": 1,
                "is_all_ports": false
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    configurations = data.get("configurations", [])
    ingress_configurations = data.get("ingress_configurations", [])
    egress_configurations = data.get("egress_configurations", [])
    
    if not configurations:
        raise ValueError("No configurations provided")
    
    # Group configurations by switch_sn and prepare for deployment
    switch_configs = {}
    
    # Group main configurations by switch_sn
    for config in configurations:
        switch_sn = config.get("switch_sn")
        if not switch_sn:
            raise ValueError("switch_sn is required")
        
        if switch_sn not in switch_configs:
            switch_configs[switch_sn] = {
                "configurations": [],
                "ingress_configurations": [],
                "egress_configurations": []
            }
        
        switch_configs[switch_sn]["configurations"].append(config)
    
    # Group ingress configurations by switch_sn
    for ingress_config in ingress_configurations:
        switch_sn = ingress_config.get("switch_sn")
        if not switch_sn:
            raise ValueError("switch_sn is required in ingress configuration")
        
        if switch_sn not in switch_configs:
            switch_configs[switch_sn] = {
                "configurations": [],
                "ingress_configurations": [],
                "egress_configurations": []
            }
        
        switch_configs[switch_sn]["ingress_configurations"].append(ingress_config)
    
    # Group egress configurations by switch_sn
    for egress_config in egress_configurations:
        switch_sn = egress_config.get("switch_sn")
        if not switch_sn:
            raise ValueError("switch_sn is required in egress configuration")
        
        if switch_sn not in switch_configs:
            switch_configs[switch_sn] = {
                "configurations": [],
                "ingress_configurations": [],
                "egress_configurations": []
            }
        
        switch_configs[switch_sn]["egress_configurations"].append(egress_config)
    
    config_dict = {}
    db_configurations = {}
    errors = []

    for switch_sn, switch_data in switch_configs.items():
        # Get switch for deployment using service layer
        switch = roce_db.get_switch_by_sn(switch_sn)
        if not switch:
            raise ValueError(f"Switch with SN {switch_sn} not found")
    
        # Build configuration data using view layer tool function
        config_data = _build_qos_config_data({
            "configurations": switch_data.get("configurations", []),
            "ingress_configurations": switch_data.get("ingress_configurations", []),
            "egress_configurations": switch_data.get("egress_configurations", []),
            "config_data": {}  # Empty for creation operations
        })

        config_dict[switch_sn] = config_data
        
        # Prepare database records for this switch (include config_data directly)
        switch_config_data = {
            "configurations": switch_data.get("configurations", []),
            "ingress_configurations": switch_data.get("ingress_configurations", []),
            "egress_configurations": switch_data.get("egress_configurations", []),
            "config_data": json.dumps(config_data.get("new_val"))  # Include deployment data directly
        }
        db_configurations[switch_sn] = switch_config_data

    # Deploy configurations first (deploy first, then save to database)
    res = config_distribution_roce(config_dict)
    LOG.info(f"QoS config save result: {res}")
    if res.get("status") == 500 and res.get("info", None):
        return res
    
    err_info = res.get("err_info", {})
    
    # Save configurations to database only for successful deployments
    for switch_sn in switch_configs.keys():
        if switch_sn not in err_info:
            # Success: Save configurations to database via service layer (with config_data already included)
            roce_db.save_qos_configurations_with_config_data(db_configurations.get(switch_sn))
        else:
            # Failed: Record error information
            errors.append({
                "status": 500,
                "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
                "switch": switch_sn
            })
    
    if errors:
        return jsonify({
            "status": 500,
            "msg": "Deploy QoS configurations failed",
            "data": errors
        })
    
    return jsonify({
        "status": 200,
        "msg": "QoS configurations saved successfully",
        "data": {}
    })

@roce_mold.route("/qos_config/update", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
# @validate_qos_uniqueness
def qos_config_update():
    """
    Update QoS configurations for a specific switch (bulk update)
    Request body:
    {
        "switch_sn": "string",
        "configurations": [
            {
                "config_id": "string",  # for update/delete
                "sysname": "string",
                "forwarding_class": "string",
                "local_priority": int,
                "scheduler": "string",
                "mode": "string",
                "weight": int,
                "guaranteed_rate": int
            }
        ],
        "ingress_configurations": [
            {
                "ingress_id": "string",  # for update/delete
                "sysname": "string",
                "classifier": "string",
                "trust_mode": "string",
                "port": ["string"],
                "forwarding_class": "string",
                "queue": ["string"],
                "is_all_ports": bool,
                "is_all_queues": bool
            }
        ],
        "egress_configurations": [
            {
                "egress_id": "string",  # for update/delete
                "sysname": "string",
                "scheduler_profile": "string",
                "scheduler": "string",
                "port": ["string"],
                "forwarding_class": "string",
                "local_priority": int,
                "is_all_ports": bool
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    switch_sn = data.get("switch_sn")
    configurations = data.get("configurations", [])
    ingress_configurations = data.get("ingress_configurations", [])
    egress_configurations = data.get("egress_configurations", [])

    if not switch_sn:
        raise ValueError("switch_sn is required")

    # Get switch for deployment using service layer
    switch = roce_db.get_switch_by_sn(switch_sn)
    if not switch:
        raise ValueError(f"Switch with SN {switch_sn} not found")

    # Get existing configurations for comparison
    existing_config = roce_db.get_qos_config_by_switch_sn(switch_sn)
    old_val = json.loads(existing_config.config_data)
    
    # Build configuration data for deployment using view layer tool function
    config_data = _build_qos_config_data({
        "configurations": configurations,
        "ingress_configurations": ingress_configurations,
        "egress_configurations": egress_configurations,
        "config_data": old_val
    })

    config_dict = {
        switch_sn: config_data
    }

    # Prepare database update data with config_data included (before deployment)
    update_data = {
        "configurations": configurations,
        "ingress_configurations": ingress_configurations,
        "egress_configurations": egress_configurations,
        "config_data": json.dumps(config_data.get("new_val"))  # Include deployment data directly
    }

    # Deploy configurations first (deploy first, then save to database)
    res = config_distribution_roce(config_dict)
    LOG.info(f"QoS config update result: {res}")
    if res.get("status") == 500 and res.get("info", None):
        return res
    
    err_info = res.get("err_info", {})
    errors = []
    
    # Check deployment result for this switch
    if switch_sn not in err_info:
        # Success: Update database configurations (with config_data already included)
        roce_db.update_qos_configurations_with_config_data(switch_sn, update_data)
        
        return jsonify({
            "status": 200,
            "msg": "QoS configurations updated successfully"
        })
    else:
        # Failed: Return error information
        errors.append({
            "status": 500,
            "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
            "switch": switch_sn
        })
        
        return jsonify({
            "status": 500,
            "msg": "Deploy QoS configurations failed",
            "data": errors
        })

def _build_qos_config_data(config):
    """
    Build QoS configuration data for deployment
    Args:
        config: dict, Configuration data containing:
            - configurations: list, Main QoS configurations (optional for delete)
            - ingress_configurations: list, Ingress configurations (optional)
            - egress_configurations: list, Egress configurations (optional)  
            - config_data: dict, Existing configuration data (optional)
    Returns:
        dict: Built configuration data for deployment
    """
    # Get existing configuration data
    old_val = {}
    if config.get("config_data"):
        try:
            old_val = config.get("config_data")
            if isinstance(old_val, str):
                old_val = json.loads(old_val)
        except (json.JSONDecodeError, TypeError):
            old_val = {}

    # Build new configuration
    new_val = {
        "service_scheduler": {
            "configuration": [],
            "ingress": [],
            "egress": []
        }
    }

    # Add configurations
    for qos_config in config.get("configurations", []):
        new_val["service_scheduler"]["configuration"].append({
            "forwarding_class_name": qos_config.get("forwarding_class"),
            "local_priority": qos_config.get("local_priority"),
            "scheduler_name": qos_config.get("scheduler"),
            "mode": qos_config.get("mode"),
            "scheduler_params": {
                "weight": qos_config.get("weight"),
                "guaranteed_rate": qos_config.get("guaranteed_rate")
            }
        })

    # Add ingress configurations
    for ingress in config.get("ingress_configurations", []):
        new_val["service_scheduler"]["ingress"].append({
            "classifier_name": ingress.get("classifier"),
            "trust_mode": ingress.get("trust_mode"),
            "ports": ingress.get("port", []),
            "forwarding_class_name": ingress.get("forwarding_class"),
            "code_point": ingress.get("queue", [])
        })

    # Add egress configurations
    for egress in config.get("egress_configurations", []):
        new_val["service_scheduler"]["egress"].append({
            "scheduler_profile_name": egress.get("scheduler_profile"),
            "scheduler_name": egress.get("scheduler"),
            "ports": egress.get("port", []),
            "forwarding_class_name": egress.get("forwarding_class"),
            "local_priority": egress.get("local_priority")
        })

    return {
        "new_val": new_val,
        "old_val": old_val
    }


@roce_mold.route("/qos_config/list", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def qos_config_list():
    """
    Get QoS configurations list with tree structure and pagination
    Request body:
    {
        "switch_sn": ["string"],  # optional: filter by switch serial numbers
        "page": 1,                # optional: page number
        "pageSize": 10            # optional: page size
    }
    Returns:
        JSON response with tree-structured data and pagination
    """
    data = request.get_json()
    
    # Get configurations list data via service layer
    result = roce_db.get_qos_configs_list_data(data)
    
    return jsonify({
        "status": 200,
        "data": result.get("tree_data", []),
        "page": result.get("page", 1),
        "pageSize": result.get("pageSize", 10),
        "total": result.get("total", 0)
    })
    
        
@roce_mold.route("/qos_port_config/list", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def qos_port_config_list():
    """
    Get QoS port configurations list with tree structure and pagination
    Request body:
    {
        "traffic_type": "ingress|egress",  # required: traffic direction
        "switch_sn": ["string"],           # optional: filter by switch serial numbers
        "page": 1,                        # optional: page number
        "pageSize": 10                    # optional: page size
    }
    Returns:
        JSON response with tree-structured data and pagination
    """
    data = request.get_json()
    
    # Get port configurations list data via service layer
    result = roce_db.get_qos_port_configs_list_data(data)
    
    return jsonify({
        "status": 200,
        "data": result.get("tree_data", []),
        "page": result.get("page", 1),
        "pageSize": result.get("pageSize", 10),
        "total": result.get("total", 0)
    })

@roce_mold.route("/qos_config/forwarding_class_list", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def qos_forwarding_class_list():
    """
    Get QoS scheduler list for a specific switch
    Request body:
    {
        "switch_sn": "string"  # required: switch serial number
    }
    Returns:
        JSON response with scheduler list
    """
    data = request.get_json()
    
    # Get scheduler list data via service layer
    result = roce_db.get_pfc_forwarding_class_configs_list_data(data)
    
    return jsonify({
        "status": 200,
        "data": result.get("data", []),
        "page": result.get("page", 1),
        "pageSize": result.get("pageSize", 10),
        "total": result.get("total", 0)
    })

@roce_mold.route("/qos_config/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def qos_config_delete():
    """
    Delete QoS configuration.
    
    Request body:
    {
        "config_id": "string"
    }
    """
    # Get request data
    request_data = request.get_json()
    config_id = request_data.get("config_id")
    
    if not config_id:
        raise ValueError("config_id is required")
    
    # Call service layer to get configuration and switch info for deletion
    result = roce_db.get_qos_configuration_for_deletion(config_id)
    
    config = result.get("configuration")
    switch = result.get("switch")
    
    if config.config_data:
        
        stored_config_data = json.loads(config.config_data)
        target_forwarding_class = config.forwarding_class
        target_scheduler = config.scheduler
        session = inven_db.get_session()
        config_sch_count = session.query(QosConfiguration).filter(QosConfiguration.switch_sn == switch.sn,
                                                                  QosConfiguration.scheduler == target_scheduler
                                                                  ).count()
        
        # Parse historical config_data to extract configurations matching the forwarding_class
        parsed_configurations = []
        parsed_ingress_configurations = []
        parsed_egress_configurations = []
        
        # Extract main configurations matching forwarding_class
        if stored_config_data.get("service_scheduler", {}).get("configuration"):
            for conf in stored_config_data["service_scheduler"]["configuration"]:
                if conf.get("forwarding_class_name") == target_forwarding_class:
                    stored_config_data["service_scheduler"]["configuration"].remove(conf)
                    parsed_configurations.append(conf)
        
        # Extract ingress configurations matching forwarding_class
        if stored_config_data.get("service_scheduler", {}).get("ingress"):
            for ingress in stored_config_data["service_scheduler"]["ingress"]:
                if ingress.get("forwarding_class_name") == target_forwarding_class:
                    stored_config_data["service_scheduler"]["ingress"].remove(ingress)
                    parsed_ingress_configurations.append(ingress)
        
        # Extract egress configurations matching forwarding_class
        if stored_config_data.get("service_scheduler", {}).get("egress"):
            new_egress = []
            for egress in stored_config_data["service_scheduler"]["egress"]:
                if (egress.get("forwarding_class_name") == target_forwarding_class or
                        (config_sch_count == 1 and egress.get("scheduler_name") == target_scheduler)):
                    parsed_egress_configurations.append(egress)
                else:
                    new_egress.append(egress)

            stored_config_data["service_scheduler"]["egress"] = new_egress
    
        # Build delete configuration data directly from parsed historical data
        delete_config_data = {
            "old_val": {
                "service_scheduler": {
                    "configuration": parsed_configurations,
                    "ingress": parsed_ingress_configurations,
                    "egress": parsed_egress_configurations
                }
            },
            "new_val": {
                "service_scheduler": {
                    "configuration": [],
                    "ingress": [],
                    "egress": []
                }
            }
        }

        config_dict = {
            switch.sn: delete_config_data
        }

        res = config_distribution_roce(config_dict)
        LOG.info(f"QoS config delete result: {res}")
        if switch.reachable_status == 1:
            roce_db.delete_qos_configuration(config, stored_config_data)
            
            return jsonify({
                "status": 200,
                "msg": "QoS configuration deleted successfully"
            })

        if res.get("status") == 500 and res.get("info", None):
            return res
        
        err_info = res.get("err_info", {})
        errors = []
        
        # Check deployment result for this switch
        if switch.sn not in err_info:
            # Success: Delete the configuration from database after successful deployment
            roce_db.delete_qos_configuration(config, stored_config_data)
            
            return jsonify({
                "status": 200,
                "msg": "QoS configuration deleted successfully"
            })
        else:
            errors.append({
                "status": 500,
                "msg": f"{switch.host_name} ({switch.sn}): {err_info[switch.sn]}",
                "switch": switch.sn
            })
            return jsonify({
                "status": 500,
                "msg": "Deploy QoS configuration deletion failed",
                "data": errors
            })
    else:

        roce_db.delete_qos_configuration(config)
        
        return jsonify({
            "status": 200,
            "msg": "QoS configuration deleted successfully"
        })

@roce_mold.route("/qos_config/detail_by_switch", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def qos_config_detail_by_switch():
    """
    Get QoS configuration details for a specific switch with enhanced architecture
    Request body:
    {
        "switch_sn": "string"
    }
    Response:
    {
        "status": 200,
        "msg": "Get qos config detail successfully",
        "data": [configuration list with ingress/egress details]
    }
    """
    data = request.get_json()
    
    if not data:
        raise ValueError("No request data provided")
    
    switch_sn = data.get("switch_sn")
    if not switch_sn:
        raise ValueError("switch_sn is required")

    # Get QoS configurations detail via service layer
    configs = roce_db.get_qos_configs_detail_by_switch_sn(switch_sn)

    return jsonify({
        "status": 200,
        "msg": "Get qos config detail successfully",
        "data": configs
    })


@roce_mold.route("/qos_config/validate_basic", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def qos_config_validate_basic():
    """
    Validate single QoS basic configuration
    Request body:
    {
        "config": {
            "switch_sn": "string",
            "forwarding_class": "string",
            "scheduler": "string",
            "config_id": "string" (optional, for updates)
        }
    }
    Returns:
        JSON response with validation status
    """
    data = request.get_json()
    if not data:
        raise ValueError("No request data provided")
    
    config = data.get("config")
    if not config:
        raise ValueError("config is required")
    
    # Validate basic configuration using the separate validation function
    validate_qos_basic_config(config)
    
    return jsonify({
        "status": 200,
        "msg": "Basic configuration validation passed"
    })


@roce_mold.route("/qos_config/validate_ingress", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def qos_config_validate_ingress():
    """
    Validate single QoS ingress configuration
    Request body:
    {
        "ingress_config": {
            "switch_sn": "string",
            "classifier": "string",
            "forwarding_class": "string",
            "config_id": "string" (optional, for updates)
        },
        "existing_forwarding_classes": ["string"] (optional)
    }
    Returns:
        JSON response with validation status
    """
    data = request.get_json()
    if not data:
        raise ValueError("No request data provided")
    
    ingress_config = data.get("ingress_config")
    if not ingress_config:
        raise ValueError("ingress_config is required")
    
    existing_forwarding_classes = data.get("existing_forwarding_classes")
    
    # Validate ingress configuration using the separate validation function
    validate_qos_ingress_config(ingress_config, existing_forwarding_classes)
    
    return jsonify({
        "status": 200,
        "msg": "Ingress configuration validation passed"
    })


@roce_mold.route("/qos_config/validate_egress", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def qos_config_validate_egress():
    """
    Validate single QoS egress configuration
    Request body:
    {
        "egress_config": {
            "switch_sn": "string",
            "scheduler_profile": "string",
            "scheduler": "string",
            "forwarding_class": "string",
            "config_id": "string" (optional, for updates)
        },
        "scheduler_to_forwarding_classes": {
            "scheduler_name": ["forwarding_class1", "forwarding_class2"]
        } (optional)
    }
    Returns:
        JSON response with validation status
    """
    data = request.get_json()
    if not data:
        raise ValueError("No request data provided")
    
    egress_config = data.get("egress_config")
    if not egress_config:
        raise ValueError("egress_config is required")
    
    scheduler_to_forwarding_classes = data.get("scheduler_to_forwarding_classes")
    
    # Validate egress configuration using the separate validation function
    validate_qos_egress_config(egress_config, scheduler_to_forwarding_classes)
    
    return jsonify({
        "status": 200,
        "msg": "Egress configuration validation passed"
    })