import json
import logging
from datetime import datetime

from flask import Blueprint, request, jsonify
from server.db.models.wireless import WirelessClient
from server.db.models.wireless_openwifi import Inventory, Venues, TimePoints
from server.db.pg_engine import get_pg_session
from server.util.monitor_client_util import get_vendor_by_mac_redis
from sqlalchemy import case, null
from sqlalchemy.dialects.postgresql import insert as pg_insert

LOG = logging.getLogger(__name__)
wireless_monitor_client_mold = Blueprint("wireless_monitor_client_mold", __name__)


@wireless_monitor_client_mold.route('/client', methods=['POST'])
def create_monitor_client():
    data = request.get_json(force=True)
    type = data.get('type')  # 数据类型
    sn = data.get('sn', '')
    client_data = data.get('data', {})
    session = get_pg_session()
    result = handler_create_monitor_client(type, sn, client_data, session)
    return jsonify(result)


def handler_create_monitor_client(type, sn, data, session=None):
    msg = {'status': 200, 'info': 'Add client success.'}
    try:
        now_ts = datetime.now()  # 当前时间
        # 1. 终端上线、离线数据
        if type == 1:
            # 2.获取并检验请求参数是否符合条件
            client_data = json.loads(data) if isinstance(data, str) else data
            LOG.debug('[type=1][终端上下线] client_data: %s', client_data)
            client_data = client_data.get('payload', {})
            modified_time = datetime.fromtimestamp(client_data.get("timestamp", now_ts.timestamp()))
            event_type = client_data.get('type')  # 终端设备状态
            if not event_type:
                LOG.error('Missing type in event')
                return {'status': 400, 'info': 'Missing type in event'}

            payload = client_data.get('payload', {})
            if not payload:
                LOG.error('Missing payload in event')
                return {'status': 400, 'info': 'Missing payload in event'}

            client_mac = payload.get('client')  # 终端mac地址字段
            if not client_mac:
                LOG.error('Missing client in payload')
                return {'status': 400, 'info': 'Missing client in payload'}

            # 调用接口查找vendor
            res = get_vendor_by_mac_redis(client_mac)
            vendor = res[client_mac]
            # 准备基础数据
            base_data = {
                "mac": client_mac,
                "sn": sn or '',
                "host_name": client_mac,
                "vendor": vendor,
                "join_time": now_ts,
                "ip": '',
                "modified_time": modified_time,
            }
            # 根据事件类型添加特定字段
            if event_type == 'client.leave':  # 离线
                event_data = {
                    "status": 2,
                    "leave_time": now_ts,
                    "rx": payload.get('rx_bytes'),
                    "tx": payload.get('tx_bytes'),
                    "rx_packets": payload.get('rx_packets'),
                    "tx_packets": payload.get('tx_packets'),
                    "ssid": '',
                    "band": '',
                }
            elif event_type == 'client.join':  # 上线
                event_data = {
                    "status": 1,
                    "ssid": payload.get('ssid'),
                    "band": payload.get('band'),
                    "channel": payload.get('channel'),
                }
            else:
                LOG.error('type in event format error')
                return {'status': 400, 'info': 'type in event format error'}
            # 合并基础数据和事件数据
            full_data = {**base_data, **event_data}
            with session.begin():
                # 使用 ON CONFLICT 语法
                stmt = pg_insert(WirelessClient).values(**full_data)

                # 构建更新字典
                update_dict = {}
                # 特殊处理：上线事件需要清空 leave_time
                if event_type == 'client.join':
                    update_dict['leave_time'] = null()
                for k, v in full_data.items():
                    if k in ['mac', 'host_name', 'vendor']:
                        continue
                    if v is None or v == '':
                        continue

                    if k == "join_time":
                        # 如果 status == 2，保留原来的 join_time
                        update_dict[k] = case(
                            (stmt.excluded.status == 2, WirelessClient.join_time),
                            else_=stmt.excluded.join_time
                        )
                    else:
                        update_dict[k] = stmt.excluded[k]
                # 构建更新语句 - 仅当新值非空时才更新
                update_stmt = stmt.on_conflict_do_update(
                    index_elements=['mac'],
                    set_=update_dict
                )
                session.execute(update_stmt)
                session.commit()
        # 1. 周期上报数据
        elif type == 2:
            base_data_list = []  # 收集所有要处理的数据
            # 2.获取并检验请求参数是否符合条件
            cycle_data = json.loads(data) if isinstance(data, str) else data
            LOG.debug('[type=2][周期上报] cycle_data: %s', cycle_data)
            cycle_data = cycle_data.get('payload', {})
            modified_time = datetime.fromtimestamp(cycle_data.get('state', {}).get('recorded')) if cycle_data.get(
                'state', {}).get('recorded') else now_ts
            serial_number = cycle_data.get('serial')
            interfaces = cycle_data.get('state', {}).get('interfaces', [])
            if len(interfaces) == 0:
                LOG.error('cycle data interfaces parameter error!')
                return {'status': 400, 'info': "data interfaces parameter error!"}
            for interface in interfaces:
                ssidList = interface.get('ssids', [])
                radios = cycle_data.get('state', {}).get('radios', [])
                for ssid in ssidList:
                    associations = ssid.get("associations", [])
                    if not associations:
                        continue  # 如果没有就跳过
                    band = ssid.get('band')
                    if not band:
                        LOG.error('cycle data interfaces parameter error!')
                        return {'status': 400, 'info': "data interfaces parameter error!"}
                    radio = next((r for r in radios if band in r.get("band", [])), None)
                    for association in associations:
                        station_mac = association.get('station')
                        if not station_mac:
                            return {'status': 400, 'info': 'Missing station in associations'}
                        # 调用接口查找vendor
                        res = get_vendor_by_mac_redis(station_mac)
                        vendor = res[station_mac]
                        client_mac = next((r for r in interface.get("clients", []) if r.get('mac') == station_mac),
                                          None)

                        vlan = association.get('dynamic_vlan')
                        # 准备基础数据
                        base_data = {
                            "mac": station_mac,
                            "modified_time": modified_time,
                            "host_name": station_mac,
                            "vendor": vendor,
                            "join_time": now_ts,
                            "status": 1,
                            "rssi": association.get('rssi'),
                            "rx": association.get('rx_bytes'),
                            "tx": association.get('tx_bytes'),
                            "rx_packets": association.get('rx_packets'),
                            "tx_packets": association.get('tx_packets'),
                            "band": ssid.get('band'),
                            "ssid": ssid.get('ssid'),
                            "vlan": vlan,
                            "channel": radio.get('channel') if radio else None,
                            "channel_width": radio.get('channel_width') if radio else None,
                            "ip": client_mac.get('ipv4_addresses')[0] if client_mac and client_mac.get(
                                'ipv4_addresses') else '',
                            "sn": serial_number
                        }
                        base_data_list.append(base_data)
            # 3.进行数据更新
            with session.begin():
                for base_data in base_data_list:
                    # 使用 ON CONFLICT 语法
                    stmt = pg_insert(WirelessClient).values(**base_data)
                    # 构建更新字典
                    update_dict = {'leave_time': null()}
                    for k, v in base_data.items():
                        if k in ['mac', 'host_name', 'vendor', 'status', 'join_time']:
                            continue
                        if v is None or v == '':
                            continue
                        else:
                            update_dict[k] = stmt.excluded[k]
                    # 构建更新语句 - 仅当新值非空时才更新
                    update_stmt = stmt.on_conflict_do_update(
                        index_elements=['mac'],
                        set_=update_dict
                    )
                    session.execute(update_stmt)
        else:
            return {'status': 400, 'info': "type parameter error!"}
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    return msg


@wireless_monitor_client_mold.route('/client', methods=['GET'])
def list_monitor_client():
    try:
        status = request.args.get('status', type=int)
        search_value = request.args.get('searchValue')
        page_num = request.args.get('pageNum', 1, type=int)
        page_size = request.args.get('pageSize', 10, type=int)
        sort_by = request.args.get('sortBy')
        sort_type = request.args.get('sortType', 'desc')

        if not status and status not in [0, 1, 2]:
            return jsonify({'status': 400, 'info': "status is required."})
        if page_num < 1 or page_size < 1:
            return jsonify({'status': 400, 'info': 'pageNum and pageSize must be positive.'})

        with (get_pg_session() as session):
            with session.begin():
                # 1.过滤状态并联Inventory表和Venues表查询
                query = session.query(WirelessClient, Inventory.name, Venues.name, Venues.id).outerjoin(Inventory,
                                                                                                        WirelessClient.sn == Inventory.serialnumber).outerjoin(
                    Venues, Inventory.venue == Venues.id)
                if status != 0:
                    query = query.filter(WirelessClient.status == status)
                # 2.按搜索条件（STA、IP、AP、SSID、vendor）过滤
                if search_value:
                    like_pattern = f"%{search_value}%"
                    query = query.filter(
                        (WirelessClient.mac.ilike(like_pattern)) |
                        (WirelessClient.ip.ilike(like_pattern)) |
                        (Inventory.name.ilike(like_pattern)) |
                        (WirelessClient.ssid.ilike(like_pattern)) |
                        (WirelessClient.vendor.ilike(like_pattern)) |
                        (Venues.name.ilike(like_pattern)) |
                        (WirelessClient.sn.ilike(like_pattern))
                    )
                # 3.排序
                if sort_by:
                    if hasattr(WirelessClient, sort_by):
                        col = getattr(WirelessClient, sort_by)
                    elif sort_by == "connect_ap":
                        col = Inventory.name
                    elif sort_by == "venue":
                        col = Venues.name
                    else:
                        col = WirelessClient.create_time

                    if sort_type.lower() == 'asc':
                        if sort_by == "status":
                            query = query.order_by(col.asc())
                        else:
                            query = query.order_by(WirelessClient.status.asc(), col.asc())
                    else:
                        if sort_by == "status":
                            query = query.order_by(col.desc())
                        else:
                            query = query.order_by(WirelessClient.status.asc(), col.desc())
                else:
                    query = query.order_by(WirelessClient.status.asc())
                # 分页
                total = query.count()
                results = query.offset((page_num - 1) * page_size).limit(page_size).all()

                # 构造返回数据
                info = []
                for client, ap_name, venue_name, venue_id in results:
                    info.append({
                        'create_time': client.create_time,
                        'modified_time': client.modified_time,
                        'id': client.id,
                        'status': client.status,
                        'host_name': client.host_name,
                        'mac': client.mac,
                        'vendor': client.vendor,
                        'connect_ap': ap_name,
                        'venue': venue_name,
                        'siteId': venue_id,
                        'ssid': client.ssid,
                        'sn': client.sn,
                        'rssi': client.rssi,
                        'band': client.band,
                        'channel': client.channel,
                        'channel_width': client.channel_width,
                        'ip': client.ip,
                        'authentication': '',
                        'vlan': client.vlan,
                        'rx': client.rx,
                        'tx': client.tx,
                        'rx_packets': client.rx_packets,
                        'tx_packets': client.tx_packets,
                        'join_time': client.join_time,
                        'leave_time': client.leave_time
                    })
                response = {
                    'info': info,
                    'status': 200,
                    'pageNum': page_num,
                    'pageSize': page_size,
                    'total': total
                }


    except Exception as e:
        response = {'status': 500, 'info': str(e)}
    return jsonify(response)


@wireless_monitor_client_mold.route('/timepoints', methods=['GET'])
def get_timepoints():
    board_id = request.args.get('board_id')
    from_date = request.args.get('fromDate')
    end_date = request.args.get('endDate')
    if board_id:
        try:
            with (get_pg_session() as session):
                objs = session.query(TimePoints).filter(
                    TimePoints.boardid == board_id,
                    TimePoints.timestamp >= int(from_date))
                if end_date:
                    objs = objs.filter(TimePoints.timestamp <= int(end_date))
                objs = objs.all()
                data = {}
                for obj in objs:
                    if obj.serialnumber not in data:
                        data[obj.serialnumber] = []
                    data[obj.serialnumber].append({
                        'ap_data': json.loads(obj.ap_data),
                        'boardId': obj.boardid,
                        'device_info': json.loads(obj.device_info),
                        'id': obj.id,
                        'radio_data': json.loads(obj.radio_data),
                        'serialNumber': obj.serialnumber,
                        'ssid_data': json.loads(obj.ssid_data),
                        'timestamp': obj.timestamp
                    })
                response = {'status': 200, 'info': data}
        except Exception as e:
            response = {'status': 500, 'info': str(e)}
    else:
        response = {'status': 404, 'info': 'board_id empty'}
    return jsonify(response)
