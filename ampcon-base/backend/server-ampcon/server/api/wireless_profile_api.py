import logging
import time
import uuid

from flask import Blueprint, request, jsonify

from server.db.models.wireless import WirelessProfile, WirelessProfileUsage
from server.db.models.wireless_openwifi import Variables
from server.db.pg_engine import get_pg_session
from server.util import utils

LOG = logging.getLogger(__name__)
profile_mold = Blueprint("profile_mold", __name__, template_folder='templates')


@profile_mold.route('', methods=['POST'])
def create_profile():
    msg = {'status': 200, 'info': 'Create profile success.'}
    try:
        data = request.get_json(force=True)
        name = data.get('name')
        if not name:
            return jsonify({'status': 400, 'info': 'Name can not be empty.'})
        site_id = data.get('site_id')
        kind = data.get('type')
        with get_pg_session() as session:
            with session.begin():
                profile = session.query(WirelessProfile).filter(
                    WirelessProfile.name == name,
                    WirelessProfile.site_id == site_id,
                    WirelessProfile.type == kind
                ).first()
                if profile:
                    return jsonify({'status': 400, 'info': 'The name already exists.'})
                # variable_id自动生成且唯一
                while True:
                    variable_id = str(uuid.uuid4())
                    exists = session.query(Variables).filter(Variables.id == variable_id).first()
                    if not exists:
                        break
                profile = WirelessProfile()
                profile.site_id = site_id
                profile.variable_id = variable_id
                profile.type = kind
                profile.name = name
                profile.parameter = data.get('parameter')
                profile.description = data.get('description')
                session.add(profile)
                # 新增Variables
                now_ts = int(time.time())
                variables_obj = Variables()
                variables_obj.id = variable_id
                variables_obj.name = name
                variables_obj.description = profile.description
                variables_obj.venue = site_id
                variables_obj.variables = data.get('config_variables')
                variables_obj.created = now_ts
                variables_obj.modified = now_ts
                session.add(variables_obj)
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    return jsonify(msg)


@profile_mold.route('', methods=['PUT'])
def modify_profile():
    msg = {'status': 200, 'info': 'Modify profile success.'}
    try:
        data = request.get_json(force=True)
        name = data.get('name')
        with get_pg_session() as session:
            with session.begin():
                profile = session.query(WirelessProfile).filter(WirelessProfile.id == data.get('id')).first()
                if not profile:
                    return jsonify({'status': 404, 'info': 'Profile not found.'})
                if profile.name != name:
                    # 编辑同名校验
                    same_name_profile = session.query(WirelessProfile).filter(
                        WirelessProfile.name == name,
                        WirelessProfile.site_id == profile.site_id,
                        WirelessProfile.type == profile.type,
                        WirelessProfile.id != profile.id
                    ).first()
                    if same_name_profile:
                        return jsonify({'status': 400, 'info': 'The name already exists.'})
                    profile.name = name
                profile.parameter = data['parameter']
                profile.description = data['description']
                # 同步Variables
                variables_obj = session.query(Variables).filter(Variables.id == profile.variable_id).first()
                if variables_obj:
                    variables_obj.name = name
                    variables_obj.description = data['description']
                    variables_obj.venue = str(profile.site_id)
                    variables_obj.variables = data['config_variables']
                    variables_obj.modified = int(time.time())
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    return jsonify(msg)


@profile_mold.route('', methods=['DELETE'])
def delete_profile():
    msg = {'status': 200, 'info': 'Delete profile success.'}
    try:
        data = request.get_json(force=True)
        with get_pg_session() as session:
            with session.begin():
                profile = session.query(WirelessProfile).filter(WirelessProfile.id == data.get('id')).first()
                if not profile:
                    return jsonify({'status': 404, 'info': 'Profile not found.'})
                # 有资源引用的不允许删除
                usage_exist = session.query(WirelessProfileUsage).filter(
                    WirelessProfileUsage.profile_id == profile.id).first()
                if usage_exist:
                    return jsonify({'status': 400,
                                    'info': 'This profile is currently in use by one or more configurations and cannot be deleted. Please update the relevant configurations to use a different profile before proceeding.'})
                # 删除Variables
                session.query(Variables).filter(Variables.id == profile.variable_id).delete()
                session.delete(profile)
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    return jsonify(msg)


@profile_mold.route('/list', methods=['POST'])
def list_profiles():
    try:
        data = request.get_json(force=True)
        type_ = data.get('type')
        site_id = data.get('site_id')
        parameter_filter = data.get('parameterFilter')
        sort_fields = data.get('sortFields', [])
        with get_pg_session() as session:
            pre_query = session.query(WirelessProfile)
            if type_ is not None:
                pre_query = pre_query.filter(WirelessProfile.type == type_)
            if site_id is not None:
                pre_query = pre_query.filter(WirelessProfile.site_id == site_id)
            # 筛选方法
            if parameter_filter is not None:
                for pf in parameter_filter:
                    pre_query = pre_query.filter(
                        WirelessProfile.parameter[pf['field']].astext == pf['value']
                    )
            # 排序方法
            for field in sort_fields:
                # 支持parameter内key排序
                if field.get('field', '').startswith('parameter.'):
                    key = field['field'].split('.', 1)[1]
                    if field.get('order') == 'asc':
                        pre_query = pre_query.order_by(WirelessProfile.parameter[key].astext.asc())
                    else:
                        pre_query = pre_query.order_by(WirelessProfile.parameter[key].astext.desc())
                else:
                    # 兼容原有字段排序
                    if hasattr(WirelessProfile, field.get('field', '')):
                        col = getattr(WirelessProfile, field['field'])
                        if field.get('order') == 'asc':
                            pre_query = pre_query.order_by(col.asc())
                        else:
                            pre_query = pre_query.order_by(col.desc())
            page_num, page_size, total_count, query_obj = utils.query_helper(WirelessProfile, pre_query=pre_query, skip_sort=True)
            response = {
                "info": [profile.make_dict() for profile in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
    except Exception as e:
        response = {"status": 500, "info": str(e)}
    return jsonify(response)


@profile_mold.route('/<int:profile_id>', methods=['GET'])
def get_profile_detail(profile_id):
    try:
        with get_pg_session() as session:
            profile = session.query(WirelessProfile).filter(WirelessProfile.id == profile_id).first()
            if not profile:
                return jsonify({'status': 404, 'info': 'Profile not found.'})
            result = profile.make_dict()
            # 关联Variables表获取config_variables
            variables_obj = session.query(Variables).filter(Variables.id == profile.variable_id).first()
            result['config_variables'] = variables_obj.variables if variables_obj else None
            # 被引用数量
            usage_count = session.query(WirelessProfileUsage).filter(WirelessProfileUsage.profile_id == profile_id).count()
            result['usage_count'] = usage_count
            return jsonify({'status': 200, 'info': result})
    except Exception as e:
        return jsonify({'status': 500, 'info': str(e)})
