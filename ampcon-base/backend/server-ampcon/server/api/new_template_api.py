import base64
import copy
import json
import logging
import platform
import shutil
import uuid
import zipfile
import ast
import os, copy
import re
import configparser

import flask_login
import jinja2
from flask import Blueprint, render_template, request, jsonify, Response, send_from_directory, current_app, send_file

from api.new_config_api import download_img
from constants import DATABASE_USER, DATABASE_PASSWD
from server.south_api import ssh_api
from server import constants as C
from server.db.models import general, inventory
from server.db.models.automation import Playbook
from server.db.models.general import general_db, Tag
from server.db.models.inventory import inven_db
from server.db.models.inventory import Switch, SwitchAutoConfig, SwitchConfigSnapshot, SwitchConfigSnapshotWithTag, \
    SwitchAgentConf
from server.db.models.monitor import monitor_db
# from server.db.models import inventory
from server.db.session import get_session
from server.util import str_helper, utils, osutil, switch_template_util
from server.util.utils import get_latlong, String<PERSON>oader, remove_picos_v_in_switch_list, get_search_models, \
    is_name_valid, parse_param_bool_to_str, confuse_key_and_password
from server.util.permission import super_user_permission, super_admin_permission, admin_permission, readonly_permission
from server.vpn.vpn_utils import create_vpn_client
from server import cfg
from collections import OrderedDict
import datetime
from re import search
from server.constants import PICOS_V_SN


new_template_mold = Blueprint('new_template_mold', __name__, template_folder='templates')
LOG = logging.getLogger(__name__)

env = jinja2.Environment(loader=StringLoader(),
                         trim_blocks=True,
                         lstrip_blocks=True)

file_env = jinja2.Environment(loader=jinja2.FileSystemLoader('config_gen/'),
                              trim_blocks=True,
                              lstrip_blocks=True)


@new_template_mold.route('/platform/global/config', methods=['GET'])
@admin_permission.require(http_exception=403)
def site_config():
    global_configs = inven_db.get_collection(inventory.SwitchAutoConfig, {'type': ['global']})
    return jsonify({'data': [config.make_dict() for config in global_configs], 'status': 200})


@new_template_mold.route('/platform/site/templates')
@admin_permission.require(http_exception=403)
def get_platform_site_templates():
    templates = general_db.get_site_templates()
    return jsonify({'data': [template.make_dict() for template in templates], 'status': 200})


@new_template_mold.route('/config/site_template_params', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_site_template_params():
    info = json.loads(request.data)
    name = info.get('siteTemplateNameList', [])
    site_config_params = []
    for index, site_template_name in enumerate(name):
        str_params = general_db.get_params_by_name(site_template_name)
        try:
            temp = json.loads(str_params, object_pairs_hook=OrderedDict)
            for index, (k,v) in list(enumerate(temp.items())):
                temp[k]['index'] = index
                if temp[k].get('children'):
                    for index, (ck, cv) in list(enumerate(temp[k]['children'].items())):
                        temp[k]['children'][ck]['index'] = index
            site_config_params.append({'title': site_template_name, 'formJSON': temp})
        except Exception as e:
            return jsonify({'info': 'Format of template "{0}" is invalid.'.format(site_template_name), 'status': 400})
    return jsonify({'data': site_config_params, 'status': 200})


@new_template_mold.route('/generate_multiple_config', methods=['POST'])
@admin_permission.require(http_exception=403)
def generate_multiple_config():
    result = ''
    try:
        data = request.data
        for name, params in json.loads(data).items():
            for param_name, param in params.items():
                parsed_param = parse_param_bool_to_str(param)
                template_env = env.get_template(param_name)
                result += template_env.render(parsed_param).strip() + '\n'
    except Exception as e:
        LOG.exception(e)
        return jsonify({'info': 'Fail to generate config', 'status': 500})
    return jsonify({'data': result, 'status': 200})


@new_template_mold.route('/generate_config_template_verify', methods=['POST'])
def generate_config_template_verify():
    params = json.loads(request.data)
    sn = params['sn']
    global_config_name = params['globalConfigName']
    template_content = params['content']
    site_template = params['siteTemplateName']
    # need get the global config content
    global_config = inven_db.get_model(inventory.SwitchAutoConfig, filters={'name': [global_config_name]})
    if global_config:
        global_config_line = global_config.config
    else:
        global_config_line = ''
        # get the parameter when deploy
    params_str = inven_db.get_model(inventory.Switch, filters={'sn': [sn]}).config_parameters
    full_config = global_config_line
    try:
        params = ast.literal_eval(params_str)
        if not params.get('multiple_param'):
            template = jinja2.Template(template_content)
            full_config += '\n' + template.render(params)
        else:
            template = jinja2.Template(template_content)
            full_config += '\n' + template.render(params.get(site_template))
        # need to sort the config
        config_lines_list = full_config.split("\n")
        revise_config_lines_list = []
        for config_line in config_lines_list:
            if search(r'[0-9a-zA-Z]', config_line):
                revise_config_lines_list.append(" ".join(confuse_key_and_password(config_line).split()))

        final_full_config = "\n".join(sorted(revise_config_lines_list))
        return jsonify({'data': final_full_config, 'status': 200})
    except Exception as e:
        return jsonify({'info': 'Error of template: Can\'t generate config.' + str(e), 'status': 500})


@new_template_mold.route('/config/site/save', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='save_config', contents='create or update config')
def config_site_save():
    info = json.loads(request.data)
    group = info['generateTemplateInfo'].get('group')
    if '' in group:
        group.remove('')
    if "agentConfiguration" not in info.keys():
        return jsonify({'status': 500, 'info': 'Agent configuration is required'})
    if "generateTemplateInfo" not in info.keys():
        return jsonify({'status': 500, 'info': 'Generate template info is required'})
    if "selectedSystemConfig" not in info.keys():
        return jsonify({'status': 500, 'info': 'System Config info is required'})
    if "siteTemplateInfo" not in info.keys():
        return jsonify({'status': 500, 'info': 'Site templateInfo info is required'})
    # template_name = info['no_generate_template_name']
    config_platform = info['generateTemplateInfo'].get('generateConfigPlatform', '')
    system_config_name = info['selectedSystemConfig']
    system_config = inven_db.get_system_config_by_config_name(system_config_name)
    if not system_config:
        if info.get('system_config_name') == C.GLOBAL_CONFIG_TAG:
            return json.dumps({'status': 500, 'info': 'Please configure global config first.'})
        return json.dumps({'status': 500, 'info': 'Cannot found system config'})
    switch_sn = info['generateTemplateInfo'].get('generateSwitchSN', '')
    if not re.match('^[a-zA-z0-9]+$', switch_sn) or len(switch_sn) > 64:
        return json.dumps({'status': 500, 'info': 'no_generate_switch_sn is invalid'})
    sw_platform = info['generateTemplateInfo'].get('generateConfigPlatform', '')
    if not utils.is_valid_switch_model_name(sw_platform):
        return json.dumps({'status': 500, 'info': 'sw_platform name is invalid'})
    location = info['generateTemplateInfo'].get('generateConfigLocation', None)
    if len(location) > 64:
        return json.dumps({'status': 500, 'info': 'no_generate_location is too long'})
    vpn_option = info['agentConfiguration'].get('vpnEnable', None)
    retrieve_config = info['generateTemplateInfo'].get('isRetrieveConfig', None)

    if "selectedFabric" in info.keys():
        fabric = info['selectedFabric']
    else:
        fabric = "default"

    if "selectedSite" in info.keys():
        site = info['selectedSite']
    else:
        site = "default"

    session = general_db.get_session()

    if fabric != "default" and fabric not in list(map(lambda x: x[0], session.query(inventory.Fabric.fabric_name).all())):
        return json.dumps({'status': 500, 'info': 'Fabric %s not exist' % fabric})

    if site != "default" and site not in list(map(lambda x: x[0], session.query(inventory.Site.site_name).all())):
        return json.dumps({'status': 500, 'info': 'Site %s not exist' % site})

    switch = inven_db.get_model(Switch, filters={'sn': [switch_sn]})
    if switch:
        return json.dumps({'status': 500, 'info': 'Switch %s already exist' % switch_sn})
    # if db error should roll back
    with session.begin(subtransactions=True):
        config_content = ''
        params = {
            'multiple_param': True
        }
        name = info['generateTemplateInfo']['generateConfigName']
        description = info['generateTemplateInfo']['generateTemplateDescription']
        for template_name, params in info['siteTemplateInfo'].items():
            template_env = env.get_template(template_name)
            parsed_params = parse_param_bool_to_str(params)
            config_content += template_env.render(parsed_params).strip() + '\n'
        config = inventory.SwitchAutoConfig()
        config.name = name
        config.description = description
        config.config = config_content
        config.system_model = config_platform
        config.type = 'site'

        option_post_deployed = {'vpn_option': str(vpn_option), 'retrieve_config': str(retrieve_config)}
        switch = Switch(sn=switch_sn, platform_model=sw_platform,
                        address=location, status=C.SwitchStatus.CONFIGURED, config_parameters=str(params),
                        import_type=C.ImportType.DEPLOY, post_deployed_config=str(option_post_deployed),
                        system_config_id=system_config.id)
        # attach global config to switch
        global_config_name = info['generateTemplateInfo']['generateGlobalConfig']
        if global_config_name:
            global_config_content = inven_db.get_model(SwitchAutoConfig,
                                                       filters={'name': [global_config_name]})
            switch.configs.append(global_config_content)

        switch.configs.append(config)
        session.add(switch)

        switch_latlong = inventory.SwitchGis()
        g = get_latlong(location)
        switch_latlong.sn = switch_sn
        # need lat change to lng
        if g.lng and g.lat:
            switch_latlong.latitude = g.lng
            switch_latlong.longitude = g.lat
        else:
            switch_latlong.latitude = 0
            switch_latlong.longitude = 0
        db_switch_gis = inven_db.get_model(inventory.SwitchGis, filters={'sn': [switch_sn]}, session=session)
        if db_switch_gis:
            switch_latlong.id = db_switch_gis.id
            inven_db.merge(switch_latlong, session=session)
        else:
            inven_db.insert(switch_latlong, session=session)

        parking_switch = session.query(inventory.SwitchParking).filter(inventory.SwitchParking.sn == switch_sn).first()
        if parking_switch:
            switch.hwid = parking_switch.hardware_id
        inven_db.delete_collection(inventory.SwitchParking, filters={'sn': [switch_sn]}, session=session)

    hostname_prefix = info['agentConfiguration'].get('serverHostPrefix', 'ac')
    server_domain = info['agentConfiguration'].get('serverHostDomain', 'pica8.com')
    uplink_ports = info['agentConfiguration'].get('uplinkPorts', 'te-1/1/49,te-1/1/50')
    speed = info['agentConfiguration'].get('uplinkSpeed', '1000')
    vpn_enable = info['agentConfiguration'].get('vpnEnable', 'True')
    vpn_host = info['agentConfiguration'].get('ampconServer', 'vpn.pica8.com')
    vlan = info['agentConfiguration'].get('trunkVLANs', '4094')
    native_vlan = info['agentConfiguration'].get('nativeVLAN', '4094')
    agent_enable = info['agentConfiguration'].get('agentEnable', 'True')
    lacp_enable = info['agentConfiguration'].get('lacpEnable', 'False')
    new_dict = {'hostname_prefix': hostname_prefix,
                'uplink': uplink_ports,
                'uplink_speed': speed,
                'vpn_host': vpn_host,
                'vlan': vlan,
                'native_vlan': native_vlan,
                'vpn_enable': vpn_enable,
                'server_domain': server_domain,
                'enable': agent_enable,
                'platform': sw_platform,
                'lacp': lacp_enable,
                }

    agent_conf_temp = file_env.get_template('auto-deploy.j2')
    conf_str = agent_conf_temp.render(new_dict)
    inven_db.insert_or_update_agent_conf(switch_sn, conf_str, session=session)
    if group:
        inven_db.update_association_group(switch_sn, group)

    inven_db.update_switch_montior(switch_sn, 1)

    save_fabric_result, save_fabric_result_msg = utils.save_switch_to_fabric(fabric, [switch_sn], [])
    if not save_fabric_result:
        return jsonify({'status': 500, 'info': save_fabric_result_msg})

    save_site_result, save_site_result_msg = utils.save_switch_to_site(site, [switch_sn], [])
    if not save_site_result:
        return jsonify({'status': 500, 'info': save_site_result_msg})

    return json.dumps({'status': 200, 'info': 'Switch Configuration saved successfully'})


@new_template_mold.route('/template/list', methods=['POST'])
def template_list():
    info = json.loads(request.data)
    session = inven_db.get_session()
    if info['isShowPreBuiltTemplate']:
        pre_query = session.query(general.GeneralTemplateWithTag).order_by(general.GeneralTemplateWithTag.internal.desc())
    else:
        pre_query = session.query(general.GeneralTemplateWithTag).filter(general.GeneralTemplateWithTag.internal == False)
    page_num, page_size, total_count, query_obj = utils.query_helper(general.GeneralTemplateWithTag, pre_query=pre_query)
    # Format the response
    response = {
        "data": [{
            "id": template.id,
            "name": template.name,
            "description": template.description,
            "create_time": template.create_time.strftime('%Y-%m-%d %H:%M:%S') if template.create_time else "",
            "tag": template.tag,
            "internal": template.internal
        } for template in query_obj],
        "page": page_num,
        "pageSize": page_size,
        "total": total_count,
        "status": 200
    }
    return jsonify(response)


@new_template_mold.route('/upload', methods=['POST'])
@admin_permission.require(http_exception=403)
def upload_template():
    params = request.form
    name = params['templateName']
    description = params['templateDescription']
    template_file = request.files['templateFile']
    content_lines = template_file.readlines()
    if not is_name_valid(name):
        return jsonify({'status': 500, 'info': 'Template name is invalid.'})
    status, info = switch_template_util.upload_template_implement(name, description, content_lines).values()
    return jsonify({"info": info, "status": status})


@new_template_mold.route('/config/edit_template/<string:name>', methods=['GET'])
def template_content_view(name):
    template_rec = general_db.get_template_by_name(name)
    template_info = {'name': template_rec.name, 'templateContent':
        template_rec.j2_template, 'desc': template_rec.description, 'var': template_rec.params}
    msg = {'data': template_info, 'status': 200}
    return jsonify(msg)


@new_template_mold.route('/config/update_template', methods=['POST'])
@admin_permission.require(http_exception=403)
def update_template():
    info = json.loads(request.data)
    name = info.get('name')
    j2_template = info.get('templateContent')
    params = info.get('variableContent')
    if switch_template_util.is_internal_template(name):
        msg = {'info': 'Failed to update template, pre-built template cannot be edit', 'status': 500}
    else:
        general_db.update_entire_template_by_name(name, j2_template, params)
        msg = {'info': 'Success update template', 'status': 200}
    return jsonify(msg)


@new_template_mold.route('/export/<string:name>')
@admin_permission.require(http_exception=403)
def export_template(name):
    file_dir = None
    try:
        tmp_uuid = uuid.uuid4().hex[:8]
        template_j2 = file_env.get_template('template.j2')
        if name.lower() != 'all':

            template = general_db.get_template_by_name(name)
            data = template_j2.render({'name': template.name,
                                       'description': template.description,
                                       'content': template.j2_template,
                                       'param': template.params})

            filename = '%s.txt' % name
        else:
            templates = general_db.get_collection(general.GeneralTemplate)
            file_dir = 'tmp/template/%s' % tmp_uuid
            osutil.ensure_path(file_dir)
            file_path = '%s/templates.zip' % file_dir
            filename = 'templates.zip'
            z = zipfile.ZipFile(file_path, 'w')
            for template in templates:
                export_content = template_j2.render({'name': template.name,
                                                     'description': template.description,
                                                     'content': template.j2_template,
                                                     'param': template.params})
                tmp_file_path = '%s/%s.txt' % (file_dir, template.name)
                with open(tmp_file_path, 'w') as f:
                    f.write(export_content)
                z.write(tmp_file_path, arcname='%s.txt' % template.name)
            z.close()
            with open(file_path, 'rb') as f:
                data = f.read()
        return utils.wrap_file_stream(data, filename)
    finally:
        if file_dir:
            shutil.rmtree(file_dir)


@new_template_mold.route('/update_internal_template', methods=['POST'])
@admin_permission.require(http_exception=403)
def update_internal_template():
    success = 0
    failed = 0
    for template in switch_template_util.download_internal_templates():
        try:
            switch_template_util.template_remove_implement(template['name'])
            if switch_template_util.upload_template_implement(template['name'], template['description'],
                                                                   template['content_lines'], is_internal=True).get('status') == 200:
                success += 1
            else:
                failed += 1
        except:
            failed += 1
    if success == 0 and failed == 0:
        return jsonify({'status': 500,
                        'info': 'Update pre-built template failed, please check git connection'})
    elif failed == 0:
        return jsonify({'status': 200, 'info': 'Update pre-built template success.'})
    else:
        return jsonify({'status': 500,
                        'info': 'Update pre-built template success:[{}], failed:[{}].'.format(str(success),
                                                                                              str(failed))})


@new_template_mold.route('/template/remove', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='remove_template', contents='remove template name:{name}')
def template_remove():
    info = json.loads(request.data)
    name = info.get('templateName')
    if switch_template_util.is_internal_template(name):
        return jsonify({'info': 'Failed to update template, pre-built template cannot be remove.', 'status': 500})
    else:
        template = general_db.get_session().query(general.GeneralTemplate).filter(general.GeneralTemplate.name == name)
        if template.first():
            general_db.delete_collection(general.Tag, filters={'record_id': [template.first().id], 'record_type': ['template']})
            general_db.delete_collection(general.GeneralConfigParams, filters={'template_name': [name]})
            template.delete()
    return jsonify({'info': 'Deletion successful.', 'status': 200})


@new_template_mold.route('/get_snapshot_list/<string:sn>/data', methods=['POST'])
def get_snapshot_list_data(sn):    
    db_session = inven_db.get_session()
    snapshot = db_session.query(SwitchConfigSnapshotWithTag).filter(SwitchConfigSnapshotWithTag.sn.in_([sn]))
    page_num, page_size, total_count, query_snapshot = utils.query_helper(SwitchConfigSnapshotWithTag, snapshot)
    return jsonify({"data": [snapshot.make_dict() for snapshot in query_snapshot], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_template_mold.route('/get_avaliable_switch', methods=['POST'])
def get_avaliable_switch():
    deployed_switch = utils.query_switch().filter(Switch.status.in_(['Provisioning Success', 'Imported']), Switch.sn != PICOS_V_SN)
    page_num, page_size, total_count, query_switch = utils.query_helper(Switch, deployed_switch)
    return jsonify({"data": [switch.make_dict() for switch in query_switch], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_template_mold.route('/get_snapshot_list', methods=['POST'])
def get_snapshot_list():
    info = request.get_json()
    sn = info['sn']
    snapshot_entry = inven_db.get_collection(SwitchConfigSnapshot, filters={'sn': [sn]})
    snapshot_list = []
    for entry in snapshot_entry:
        snapshot_list.append(
            [entry.snapshot_time.strftime("%Y-%m-%d %H:%M:%S"), entry.tag, entry.description, entry.config_type])
    return jsonify(snapshot_list)


@new_template_mold.route('/get_snapshot_config', methods=['POST'])
def get_snapshot_config():
    info = request.get_json()
    sn = info['sn']
    snapshot_time_str = info['snapshotTime']
    if '-' not in snapshot_time_str:
        # 'Sat, 22 Feb 2020 23:52:51 GMT
        snapshot_time = datetime.datetime.strptime(snapshot_time_str, '%a, %d %b %Y %H:%M:%S GMT')
    else:
        snapshot_time = datetime.datetime.strptime(snapshot_time_str, '%Y-%m-%d %H:%M:%S')
    snapshot_entry = inventory.inven_db.get_model(inventory.SwitchConfigSnapshot,
                                                  filters={'sn': [sn], 'snapshot_time': [snapshot_time]})
    if snapshot_entry:
        return jsonify({'snapshotConfig': snapshot_entry.archive_config.decode(), 'description': snapshot_entry.description,
                        'tag': snapshot_entry.tag})
    else:
        return jsonify({'snapshotConfig': 'None', 'description': 'None', 'tag': 'None'})


@new_template_mold.route('/copy', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='copy_template', contents='copy template')
def template_copy():
    params = request.get_json()
    old_name = params.get("originTemplateName")
    template = general_db.get_model(general.GeneralTemplate, filters={'name': [old_name]})

    if not template:
        return json.dumps({'status': 500, 'info': 'template {0} not found'.format(old_name)})
    new_name = params['newTemplateName']

    if not is_name_valid(new_name):
        return json.dumps({'info': 'The new_name is invalid!', 'status': 500})

    template_description = params['newTemplateDescription']

    template_new_name = general_db.get_model(general.GeneralTemplate, filters={'name': [new_name]})
    if template_new_name:
        return json.dumps({'status': 500, 'info': 'template {0} exists'.format(new_name)})

    new_template = general.GeneralTemplate()
    new_template.name = new_name
    new_template.content = template.content
    new_template.description = template_description
    new_template.j2_template = template.j2_template
    new_template.params = template.params
    general_db.insert(new_template)

    old_template_tag = general_db.get_model(general.Tag,
                                            filters={'record_id': [template.id], 'record_type': ['template']})
    if old_template_tag:
        new_template_tag = general.Tag()
        new_template_tag.record_id = general_db.get_model(general.GeneralTemplate, filters={'name': [new_name]}).id
        new_template_tag.record_type = 'template'
        new_template_tag.tag_content = old_template_tag.tag_content
        general_db.insert(new_template_tag)

    # return 'copy template %s to %s success' % (old_name, template_platform)
    return jsonify({'status': 200, 'info': 'Copy template {0} to {1} success'.format(old_name, new_name)})


@new_template_mold.route('/set_golden_snapshot', methods=['POST'])
@admin_permission.require(http_exception=403)
def set_golden_snapshot():
    info = request.get_json()
    sn = info['sn']
    snapshot_time_str = info['snapshotTime']
    if '-' not in snapshot_time_str:
        # 'Sat, 22 Feb 2020 23:52:51 GMT
        snapshot_time = datetime.datetime.strptime(snapshot_time_str, '%a, %d %b %Y %H:%M:%S GMT')
    else:
        snapshot_time = datetime.datetime.strptime(snapshot_time_str, '%Y-%m-%d %H:%M:%S')
    # inven_db.delete_collection(SwitchConfigSnapshot, filters={'sn': [sn], 'snapshot_time': [snapshot_time]})
    golden_records = inven_db.get_collection(SwitchConfigSnapshot, filters={'sn': [sn], 'tag': ['GOLDEN_CONFIG']})
    set_golden_records = inven_db.get_collection(SwitchConfigSnapshot,
                                                 filters={'sn': [sn], 'snapshot_time': [snapshot_time]})
    session = inven_db.get_session()
    with session.begin(subtransactions=True):
        if golden_records:
            for record in golden_records:
                record.tag = 'None'
        if set_golden_records:
            for record in set_golden_records:
                record.tag = 'GOLDEN_CONFIG'

    msg = {'info': 'Selected config marked successfully as Golden Config', 'status': 200}
    return jsonify(msg)


@new_template_mold.route('/update_snapshot_desc_tag', methods=['POST'])
@admin_permission.require(http_exception=403)
def update_snapshot_desc_tag():
    info = request.get_json()
    sn = info['sn']
    snapshot_time_str = info['snapshotTime']
    description = info['description']
    tag = 'None' if info['tag'] != 'GOLDEN_CONFIG' else 'GOLDEN_CONFIG'
    if '-' not in snapshot_time_str:
        # 'Sat, 22 Feb 2020 23:52:51 GMT
        snapshot_time = datetime.datetime.strptime(snapshot_time_str, '%a, %d %b %Y %H:%M:%S GMT')
    else:
        snapshot_time = datetime.datetime.strptime(snapshot_time_str, '%Y-%m-%d %H:%M:%S')
    inven_db.update_model(SwitchConfigSnapshot, filters={'sn': [sn], 'snapshot_time': [snapshot_time]},
                          updates={SwitchConfigSnapshot.description: description, SwitchConfigSnapshot.tag: tag})
    msg = {'info': 'Update snapshot', 'status': 200}
    return jsonify(msg)


@new_template_mold.route('/del_snapshot', methods=['POST'])
@admin_permission.require(http_exception=403)
def del_snapshot():
    info = request.get_json()
    sn = info['sn']
    snapshot_time_str = info['snapshotTime']
    if '-' not in snapshot_time_str:
        # 'Sat, 22 Feb 2020 23:52:51 GMT
        snapshot_time = datetime.datetime.strptime(snapshot_time_str, '%a, %d %b %Y %H:%M:%S GMT')
    else:
        snapshot_time = datetime.datetime.strptime(snapshot_time_str, '%Y-%m-%d %H:%M:%S')
    snapshot = inven_db.get_collection(SwitchConfigSnapshot, filters={'sn': [sn], 'snapshot_time': [snapshot_time]})
    if not snapshot:
        msg = {'info': 'Cannot found snapshot which sn is {}, snapshot_time is {}'.format(sn, snapshot_time_str), 'status': 400}
    else:
        inven_db.delete_collection(SwitchConfigSnapshot, filters={'sn': [sn], 'snapshot_time': [snapshot_time]})
        inven_db.delete_collection(Tag, filters={'record_id': [snapshot[0].id], 'record_type': ['snapshot']})
        msg = {'info': 'Snapshot deleted successfully', 'status': 200}
    return jsonify(msg)


@new_template_mold.route("/update_tag", methods=["POST"])
@admin_permission.require(http_exception=403)
def update_tag():
    data = request.get_json()
    record_id = data.get('record_id', '')
    record_type = data.get('record_type', '')
    tag_content = data.get('tag_content', '')
    if record_type not in ['template', 'playbook', 'snapshot']:
        msg = {'info': 'record_type invalid', 'status': 400}
    elif record_type == 'template' and (not record_id or not inven_db.get_collection(general.GeneralTemplate, filters={'id': [record_id]})):
        msg = {'info': 'template id not found', 'status': 400}
    elif record_type == 'playbook' and (not record_id or not inven_db.get_collection(Playbook, filters={'id': [record_id]})):
        msg = {'info': 'playbook id not found', 'status': 400}
    elif record_type == 'snapshot' and (not record_id or not inven_db.get_collection(SwitchConfigSnapshotWithTag, filters={'id': [record_id]})):
        msg = {'info': 'snapshot id not found', 'status': 400}
    else:
        if record_type == 'snapshot' and 'GOLDEN_CONFIG' in tag_content:
            msg = {'info': 'snapshot tag cannot contain GOLDEN_CONFIG', 'status': 400}
        elif ' ' in tag_content or (tag_content and '' in tag_content.split(',')):
            msg = {'info': 'tag content is invalid', 'status': 400}
        elif not inven_db.get_collection(Tag, filters={'record_id': [record_id], 'record_type': [record_type]}):
            tag = Tag()
            tag.record_id = record_id
            tag.record_type = record_type
            tag.tag_content = tag_content
            inven_db.insert(tag)
            msg = {'info': 'Tag create success', 'status': 200}
        else:
            inven_db.update_model(Tag, filters={'record_id': [record_id], 'record_type': [record_type]},
                              updates={Tag.tag_content: tag_content})
            msg = {'info': 'Tag update success', 'status': 200}
    return jsonify(msg)


@new_template_mold.route('/get_running_config', methods=['POST'])
def get_running_config():
    form = request.get_json()
    sn = form.get('sn', None)
    format = form.get('format', 'tree')
    switches = inven_db.get_collection(Switch, filters={'sn': [sn]})
    if not switches:
        return jsonify("Can't found switch")
    host = switches[0].mgt_ip
    running_config = ssh_api.get_switch_running_config(sn=sn, host=host, format=format)['running_config']
    if format == "set":
        sort_running_config = "\n".join(sorted(running_config.split("\n")))
    else:
        sort_running_config = running_config
    if running_config != 'Null' and running_config != 'Can not get config':
        return jsonify({'data': sort_running_config, 'status': 200})
    else:
        return jsonify({'data': 'Can not get the config from switch', 'status': 200})


@new_template_mold.route('/config/site/save_list', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='config_site_list_save', contents='create or update config for {sn}')
def config_site_list_save():
    # Example Input JSON format
    # {
    #     "sn": ["XXXXX", "YYYYY"],
    #     "hardware_model": "",
    #     "location": "xxxx",
    #     "global_config_name": "",
    #     "site_template_name": "",
    #     "agent_config":{

    #     },
    #     "vpn": true,
    #     "retrieve_config": true,
    #     "default_config_param": {

    #     },
    #     "unique_config_param":{
    #         "XXXX":{
    #
    #         }
    #     }
    # }
    success_device_list = []
    fail_device_list = []
    try:
        info = json.loads(request.files.getlist('file')[0].read().decode())
    except Exception as _:
        return json.dumps({'status': '500', 'info': 'Invalid File Uploaded - please submit file in proper JSON Format',
                           'success': success_device_list, 'failed': fail_device_list})
    sn_list = info['sn']
    for sn in sn_list:
        switch = inven_db.get_model(Switch, filters={'sn': [sn]})
        if switch:
            fail_info = {'sn': sn, 'info': 'The SN is existed in switch list.'}
            fail_device_list.append(fail_info)
            continue

        name = sn + '_site_config'
        description = 'site_config'
        template_name = info['site_template_name']
        sw_platform = info['hardware_model']
        location = info.get('location', None)
        vpn_option = info.get('vpn', True)
        retrieve_config = info.get('retrieve_config', True)

        session = general_db.get_session()
        try:
            agent_conf = {
                'enable': True,
                'vpn_enable': True,
                'server_domain': 'pica8.com',
                'inband_native_vlan': '4094',
                'server_vpn_host': 'vpn.pica8.com',
                'inband_vlan': '4094',
                'server_hostname_prefix': 'ac',
                'inband_lacp': False,
                'uplink_ports': 'te-1/1/49,te-1/1/50',
                'uplink_speed': '1000'
            }
            if not sw_platform:
                pass
            elif os.path.exists('config_gen/{0}/auto-deploy.conf'.format(sw_platform)):
                parser = configparser.ConfigParser()
                parser.read('config_gen/{0}/auto-deploy.conf'.format(sw_platform))
                agent_conf.update(dict(parser.items('DEFAULT')))
                agent_conf.update(dict(parser.items(sw_platform)))
                agent_conf['uplink_ports'] = agent_conf['uplink'] if 'uplink' in agent_conf else agent_conf[
                    'uplink_ports']
            elif os.path.exists('agent/auto-deploy.conf'):
                parser = configparser.ConfigParser()
                parser.read('agent/auto-deploy.conf')
                agent_conf.update(dict(parser.items('DEFAULT')))

            if not 'agent_config' in info:
                info['agent_config'] = {}

            hostname_prefix = info['agent_config'].get('hostname_prefix', agent_conf['server_hostname_prefix'])
            server_domain = info['agent_config'].get('server_domain', agent_conf['server_domain'])
            uplink_ports = info['agent_config'].get('uplink_ports', agent_conf['uplink_ports'])
            speed = info['agent_config'].get('speed', agent_conf['uplink_speed'])
            vpn_host = info['agent_config'].get('vpn_host', agent_conf['server_vpn_host'])
            vlan = info['agent_config'].get('vlan', agent_conf['inband_vlan'])
            native_vlan = info['agent_config'].get('native_vlan', agent_conf['inband_native_vlan'])
            vpn_enable = info['agent_config'].get('vpn_enable', agent_conf['vpn_enable'])
            agent_enable = info['agent_config'].get('enable', agent_conf['enable'])
            lacp_enable = info['agent_config'].get('lacp', agent_conf['inband_lacp'])
            new_dict = {'hostname_prefix': hostname_prefix,
                        'uplink': uplink_ports,
                        'uplink_speed': speed,
                        'vpn_host': vpn_host,
                        'vlan': vlan,
                        'native_vlan': native_vlan,
                        'vpn_enable': vpn_enable,
                        'server_domain': server_domain,
                        'enable': agent_enable,
                        'platform': sw_platform,
                        'lacp': lacp_enable
                        }
            agent_conf_temp = file_env.get_template('auto-deploy.j2')
            conf_str = agent_conf_temp.render(new_dict)
            # inven_db.insert_or_update_agent_conf(sn, conf_str)
        except Exception as _:
            fail_info = {'sn': sn, 'info': 'Fail to generate agent configuration.'}
            fail_device_list.append(fail_info)
            continue

        config_content = ''
        params = None
        if type(template_name) != list and type(template_name) != tuple:
            fail_info = {'sn': sn, 'info': 'Fail to find the template file, please check the type of template name.'}
            fail_device_list.append(fail_info)
            continue

        def generate_site_template(config_content):
            for site_template in template_name:
                try:
                    template_env = env.get_template(site_template)
                except Exception as _:
                    fail_info = {'sn': sn, 'info': 'Fail to find the template file {0}.'.format(site_template)}
                    fail_device_list.append(fail_info)
                    return False

                try:

                    if sn in info['unique_config_param'].keys() and info['unique_config_param'].get(sn):
                        params = copy.deepcopy(info['default_config_param'].get(site_template))
                        if info['unique_config_param'][sn].get(site_template):
                            params.update(info['unique_config_param'][sn][site_template])
                    else:
                        params = copy.deepcopy(info['default_config_param'].get(site_template))
                    params = str_helper.parse_form_params(params)
                    config_content += template_env.render(params) + '\n'
                    return config_content
                except Exception as _:
                    fail_info = {'sn': sn, 'info': 'Fail to generate the template file with given params.'}
                    fail_device_list.append(fail_info)
                    return False

        config_content = generate_site_template(config_content)
        if not config_content:
            continue

        if sn in map(lambda x: x.get('sn', []), fail_device_list):
            continue

        try:
            # if db error should roll back
            with session.begin(subtransactions=True):

                config = inventory.SwitchAutoConfig()
                config.name = name
                config.description = description
                config.config = config_content
                config.system_model = sw_platform
                config.type = 'site'

                option_post_deployed = {'vpn_option': str(vpn_option), 'retrieve_config': str(retrieve_config)}
                # if global_config_name is empty, then use the default global config
                system_config_id = inven_db.get_system_config_by_config_name(C.GLOBAL_CONFIG_TAG).id
                if info.get('system_config_name') and info['system_config_name'].get(sn):
                    system_config_id = inven_db.get_system_config_by_config_name(info['system_config_name'][sn]).id

                switch = Switch(sn=sn, platform_model=sw_platform,
                                address=location, status=C.SwitchStatus.STAGED, config_parameters=str(params),
                                enable=True,
                                import_type=C.ImportType.DEPLOY, post_deployed_config=str(option_post_deployed),
                                system_config_id=system_config_id)
                if info['global_config_name']:
                    global_config_content = inven_db.get_model(SwitchAutoConfig,
                                                               filters={'name': [info['global_config_name']]})
                    if not global_config_content:
                        fail_info = {'sn': sn,
                                     'info': 'Fail to generate the template, can not find global config: {0}'.format(
                                         info['global_config_name'])}
                        fail_device_list.append(fail_info)
                        continue
                    switch.configs.append(global_config_content)

                switch.configs.append(config)
                session.add(switch)

                switch_latlong = inventory.SwitchGis()
                g = get_latlong(location)
                switch_latlong.sn = sn
                # need lat change to lng
                if g.lng and g.lat:
                    switch_latlong.latitude = g.lng
                    switch_latlong.longitude = g.lat
                else:
                    switch_latlong.latitude = 0
                    switch_latlong.longitude = 0
                db_switch_gis = inven_db.get_model(inventory.SwitchGis, filters={'sn': [sn]}, session=session)
                if db_switch_gis:
                    switch_latlong.id = db_switch_gis.id
                    inven_db.merge(switch_latlong, session=session)
                else:
                    inven_db.insert(switch_latlong, session=session)

                inven_db.delete_collection(inventory.SwitchParking, filters={'sn': [sn]}, session=session)
                # update agent str at last
                inven_db.insert_or_update_agent_conf(sn, conf_str, session)
                inven_db.update_switch_montior(sn, 1)
                # generate vpn key
                create_vpn_client(sn)
                if info.get('group_config', '') and info['group_config'].get(sn):
                    inventory.inven_db.update_association_group(sn, info['group_config'][sn])
        except Exception as _:
            fail_info = {'sn': sn, 'info': 'Fail to save records in database.'}
            fail_device_list.append(fail_info)
            continue

        success_device_list.append(sn)

    if fail_device_list:
        return json.dumps({'status': 500, 'success': success_device_list, 'failed': fail_device_list})

    if success_device_list:
        save_site_result, save_site_result_msg = utils.save_switch_to_site("default", success_device_list, [])
        if not save_site_result:
            return jsonify({'status': 500, 'info': save_site_result_msg})

        save_fabric_result, save_fabric_result_msg = utils.save_switch_to_fabric("default", success_device_list, [])
        if not save_fabric_result:
            return jsonify({'status': 500, 'info': save_fabric_result_msg})

    return json.dumps({'status': 200, 'success': success_device_list, 'failed': fail_device_list})


@new_template_mold.route('/platform_model')
@admin_permission.require(http_exception=403)
def get_platform_model():
    # active = ('configuring', 'template')
    # return render_template('general/create_template.html', models=utils.get_search_models(), active=active)
    models=utils.get_search_models()
    return jsonify({'data': models, 'status': 200})


@new_template_mold.route('/cli/platform_version')
@admin_permission.require(http_exception=403)
def get_platform_version():
    version = list(map(lambda x: x[0], general_db.get_session().query(general.Compatibility.version).distinct(general.Compatibility.version)))
    return jsonify({'data': sorted(version, reverse=True), 'status': 200})


@new_template_mold.route('/platform/<string:name>/<string:version>/cli/data')
def platform_cli_data(name, version):
    cli_nodes = general_db.get_platform_supports_clis(name, version)
    nodes = []
    for cli_node in cli_nodes:
        if cli_node.id == 0:
            continue
        node = {
            'id': cli_node.id,
            'pid': cli_node.pid,
            'name': cli_node.name,
            'title': cli_node.description,
            'nocheck': not cli_node.checkable,
            'checkable': cli_node.checkable,
            'viewable': cli_node.viewable,
            'path': cli_node.path[1:].replace('/', ' ') if cli_node.path else '',
        }
        nodes.append(node)
    return jsonify({'data': nodes, 'status': 200})


@new_template_mold.route('/create_template', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='create_template', contents='create template name:{name}')
def create_template():
    info = request.get_json()
    # param name check
    if "name" not in info or not info["name"]:
        return jsonify({'status': 500, 'info': 'Template name is required'})
    name = info['name']
    description = info.get('description', "")
    content = info['content']
    action = info['action'].lower()

    if not is_name_valid(name):
        return jsonify({'status': 500, 'info': 'Template name is invalid'})

    templ = general_db.get_model(general.GeneralTemplate, filters={'name': [name]})
    if templ:
        return jsonify({'status': 500, 'info': 'Template {0} exists'.format(name)})

    # content_lines = content.split('X')
    # LOG.info('content lines %s', content_lines)
    template_str, params = str_helper.generate_jinja2_template(content, action=action)
    LOG.info('template[%s], params:[%s]', template_str, params)
    
    session = get_session()
    with session.begin(subtransactions=True):
        template = general.GeneralTemplate(name=name, description=description, content=content,
                                           j2_template=template_str, params=json.dumps(params))
        session.add(template)
    
    return jsonify({'status': 200, 'info': 'Template created sucessfully'})


@new_template_mold.route('/cli/update', methods=['POST'])
@admin_permission.require(http_exception=403)
def update_cli_data():
    if os.path.exists(C.CLI_UPDATE_SQL_PATH):
        shutil.rmtree(C.CLI_UPDATE_SQL_PATH)
    os.makedirs(C.CLI_UPDATE_SQL_PATH)
    url = C.CLI_UPDATE_URL
    save_path = C.CLI_UPDATE_SQL_PATH
    proxies = cfg.CONF.license_portal_proxy if cfg.CONF.license_portal_proxy else None
    sys_config = inven_db.get_global_system_config()
    headers = {
        "Content-Type": "text/html;charset=UTF-8",
        "Authorization": "Basic %s" % base64.b64encode(
            "{}:{}".format(sys_config.license_portal_user, sys_config.license_portal_password).encode("utf-8")).decode()
    }
    cli_tree_zip_path = os.path.join(save_path, 'automation.zip')
    cli_tree_sql_path = os.path.join(save_path, 'automation.sql')
    try:
        download_img(url, cli_tree_zip_path, proxies=proxies, headers=headers)
    except Exception as e:
        msg = {'info': 'Due to {}, update cli tree failed'.format(str(e)), 'status': 500}
        return jsonify(msg)
    if not os.path.exists(cli_tree_zip_path):
        msg = {'info': 'failed to download cli tree sql file!', 'status': 500}
    else:
        r = zipfile.is_zipfile(cli_tree_zip_path)
        if r:
            fz = zipfile.ZipFile(cli_tree_zip_path, 'r')
            for file in fz.namelist():
                fz.extract(file, save_path)
            if not os.path.exists(cli_tree_sql_path):
                msg = {'info': 'Unzipped file not exists!', 'status': 500}
                return jsonify(msg)
            os.system('mysql -u{} -p{} -h {} automation < {}'.format(DATABASE_USER, DATABASE_PASSWD, 'mysql-service', cli_tree_sql_path))
            msg = {'info': 'Update cli tree sql file success!', 'status': 200}
        else:
            msg = {'info': 'Download file is not zip!', 'status': 500}
    return jsonify(msg)
