from flask import Blueprint, jsonify, send_file, request
from server.db.models.wireless_openwifi import Inventory
from server.db.pg_engine import get_pg_session
from server.util.device_operations import delete_devices_by_sn
from server.util.permission import super_user_permission
from server.util.tip_client_util import ServerType, post, put
from server.db.models.wireless_openwifi import Devices

wireless_inventory_mold = Blueprint("wireless_inventory_mold", __name__, template_folder='templates')


@wireless_inventory_mold.route('/import_template', methods=['GET'])
def download_inventory_template():
    try:
        template_path = "/usr/share/automation/server/wireless/templates/import_inventory.csv"
        return send_file(template_path, as_attachment=True, download_name="import_inventory.csv")
    except Exception as e:
        return jsonify({"status": 500, "info": f"Error downloading template: {str(e)}"}), 500


def clear_inventory_cache(serial_number, name, action="delete"):
    """清理OWGW中的库存缓存"""
    try:
        body = {
            "cacheMessage": f"{serial_number}|{name}",
            "action": action,
            "cacheKey": "owprov"
        }
        response = post(ServerType.OWGW, "/api/v1/cache", body)
        if not response:
            return False
        return True
    except Exception as e:
        return False


@wireless_inventory_mold.route('/batch_delete', methods=['DELETE'])
@super_user_permission.require(http_exception=403)
def batch_delete_devices():
    try:
        data = request.get_json()
        id_list = data.get("idList")
        if not id_list or not isinstance(id_list, list):
            return jsonify({"status": 400, "info": "Missing or invalid 'idList' parameter"})

        with get_pg_session(autocommit=False) as pg_session:
            transaction = pg_session.begin()
            # 步骤1：查询设备
            inventory_items = pg_session.query(Inventory).filter(
                Inventory.id.in_(id_list)
            ).all()
            if not inventory_items:
                transaction.rollback()
                return jsonify({"status": 404, "info": "No inventory items found"})

            sn_list = [item.serialnumber for item in inventory_items if item.serialnumber]
            inventory_map = {item.serialnumber: item for item in inventory_items}
            if not sn_list:
                transaction.rollback()
                return jsonify({"status": 404, "info": "No associated devices found"})

            # 步骤2：删除设备
            success, device_info, status_code = delete_devices_by_sn(sn_list, pg_session)
            if not success:
                transaction.rollback()
                return jsonify({"status": status_code, "info": device_info})

            # 步骤3：删除Inventory记录
            pg_session.query(Inventory).filter(Inventory.id.in_(id_list)).delete(synchronize_session=False)

            # 所有操作成功，提交事务
            transaction.commit()

        # 步骤4：清理缓存
        for sn in sn_list:
            if sn in inventory_map:
                clear_inventory_cache(sn, inventory_map[sn].name or "")

        return jsonify({
            "status": 200,
            "info": "Batch delete inventory success"
        })
    except Exception as e:
        return jsonify({"status": 500, "info": f"Error in batch delete: {str(e)}"})


@wireless_inventory_mold.route('/batch_switch_site', methods=['PUT'])
@super_user_permission.require(http_exception=403)
def batch_switch_site():
    try:
        data = request.get_json()
        id_list = data.get("idList")

        # 验证必填参数：idList不存在/非列表/空列表均返回400
        if not id_list or not isinstance(id_list, list) or len(id_list) == 0:
            return jsonify({
                "status": 400,
                "info": "Missing or invalid 'idList' parameter"
            }), 400

        # 获取站点ID，为空表示移出当前站点
        site_id = data.get("siteId") if "siteId" in data else None

        # 1. 一次查询获取inventory的serialnumber和对应devices
        with get_pg_session(autocommit=False) as pg_session:
            inventory_items = pg_session.query(Inventory.serialnumber).filter(
                Inventory.id.in_(id_list)
            ).all()

            if not inventory_items:
                return jsonify({
                    "status": 404,
                    "info": "No inventory items found"
                }), 404

            # 提取有效serialnumber
            valid_serials = [item.serialnumber for item in inventory_items if item.serialnumber]

            # 批量更新devices表的venue字段
            if valid_serials:
                pg_session.query(Devices).filter(
                    Devices.serialnumber.in_(valid_serials)
                ).update(
                    {"venue": site_id},
                    synchronize_session=False
                )
            # 提交devices表的更新事务
            pg_session.commit()

        # 2. 循环调用接口更新inventory表的venue字段
        for item in inventory_items:
            sn = item.serialnumber
            if sn:
                request_data = {
                    "serialNumber": sn,
                    "venue": site_id,
                    "devClass": "any"
                }
                # 调用inventory/{serialNumber}接口(服务类型OWPROV)
                put(
                    type=ServerType.OWPROV,
                    endPoint=f"/api/v1/inventory/{sn}",
                    data=request_data
                )
        return jsonify({
            "status": 200,
            "info": "Batch switch site success."
        })

    except Exception as e:
        return jsonify({
            "status": 500,
            "info": f"Error in batch switch site: {str(e)}"
        }), 500
