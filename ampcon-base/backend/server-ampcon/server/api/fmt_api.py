import copy
import json
import logging
import traceback
import threading
from sqlalchemy import and_, or_, func
from flask import Blueprint, jsonify, Response, request
from server.db.models import inventory
from server.db.models.monitor import Event
from server.db.models.inventory import SwitchMenuTreeInfo, SwitchNeInfo, SwitchGis
from server.db.models.otn import OtnTempData, OtnDeviceBasic, FmtDeviceCards
from server.util import fmt_util
from server.util.permission import admin_permission
from server.util import utils

invent_db = inventory.inven_db
fmt_module = Blueprint("fmt_module", __name__, template_folder="templates")
dcs_module = Blueprint("dcs_module", __name__, template_folder="templates")
LOG = logging.getLogger(__name__)


@fmt_module.route("/info/query", methods=["GET"])
@admin_permission.require(http_exception=403)
def query_info():
    id = request.args.get('id')
    ip = request.args.get('ip')
    if id is None and ip is None:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "At least one of IP and ID is required!"}),
                        mimetype="application/json")
    # 下发指令查询设备数据
    data = fmt_util.beat_sync_otn_device_info_single(id=id, ip=ip)
    errorCode = 0
    if data is None:
        errorCode = 1
    result = {"data": data, "errorCode": errorCode, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")

@dcs_module.route("/info/query", methods=["GET"])
@admin_permission.require(http_exception=403)
def query_info():
    id = request.args.get('id')
    ip = request.args.get('ip')
    if id is None and ip is None:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "At least one of IP and ID is required!"}),
                        mimetype="application/json")
    # 下发指令查询设备数据
    data = fmt_util.beat_sync_otn_device_info_single(id=id, ip=ip)
    errorCode = 0
    if data is None:
        errorCode = 1
    result = {"data": data, "errorCode": errorCode, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")


@fmt_module.route("/info/get", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_info():
    id = request.args.get('id')
    ip = request.args.get('ip')
    if id is None and ip is None:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "At least one of IP and ID is required!"}),
                        mimetype="application/json")

    db_session = invent_db.get_session()
    fmt_temp_data = db_session.query(OtnTempData)
    if id is None:
        data = fmt_temp_data.filter(OtnTempData.ip == ip).first()
    elif ip is None:
        data = fmt_temp_data.filter(OtnTempData.id == id).first()
    else:
        data = fmt_temp_data.filter(OtnTempData.id == id, OtnTempData.ip == ip).first()

    if not data:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Data is empty!"}),
                        mimetype="application/json")
    result = {"data": fmt_util.get_device_detail(data.id), "errorCode": 0, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")

@dcs_module.route("/info/get", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_info():
    id = request.args.get('id')
    ip = request.args.get('ip')
    if id is None and ip is None:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "At least one of IP and ID is required!"}),
                        mimetype="application/json")

    db_session = invent_db.get_session()
    fmt_temp_data = db_session.query(OtnTempData)
    if id is None:
        data = fmt_temp_data.filter(OtnTempData.ip == ip).first()
    elif ip is None:
        data = fmt_temp_data.filter(OtnTempData.id == id).first()
    else:
        data = fmt_temp_data.filter(OtnTempData.id == id, OtnTempData.ip == ip).first()

    if not data:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Data is empty!"}),
                        mimetype="application/json")
    result = {"data": fmt_util.get_device_detail(data.id, "DCS"), "errorCode": 0, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")

@fmt_module.route("/config/query", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_config():
    ip = request.args.get('ip')
    slotIndex = request.args.get('slotIndex')
    cardId = request.args.get('cardId')
    if ip is None or (slotIndex is None and cardId is None):
        return Response(json.dumps({"data": "", "errorCode": 1,
                                    "errorMsg": "Ip and at least one of slotIndex and cardId is required!"}),
                        mimetype="application/json")
    if slotIndex is None:
        slotIndex = -1
    else:
        slotIndex = int(slotIndex)
    print(f"slotIndex={slotIndex} , type={type(slotIndex)}")
    if cardId is None:
        cardId = ""
    db_session = invent_db.get_session()
    card = db_session.query(FmtDeviceCards).join(OtnDeviceBasic,
                                                 OtnDeviceBasic.id == FmtDeviceCards.device_id).filter(
        and_(OtnDeviceBasic.ip == ip,
             or_(FmtDeviceCards.slot_index == slotIndex, FmtDeviceCards.card_id == cardId))).first()
    if not card:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
                        mimetype="application/json")
    slotIndex = card.slot_index
    data, errorCode, errorMsg = fmt_util.get_config(ip, slotIndex)
    result = {"data": data, "errorCode": errorCode, "errorMsg": errorMsg}
    return Response(json.dumps(result), mimetype="application/json")


@fmt_module.route("/config/modify", methods=["PUT"])
@admin_permission.require(http_exception=403)
def modify_config():
    data = request.get_json()
    ip = data.get("ip", "")
    slotIndex = data.get("slotIndex", -1)
    cardId = data.get('cardId')
    key = data.get("key")
    value = data.get("value")
    print(f"parameters key:{key}")
    if ip is None or (slotIndex is None and cardId is None) or key is None or value is None:
        return Response(
            json.dumps({"data": "", "errorCode": 1, "errorMsg": "Ip, slotIndex, key and value is required!"}),
            mimetype="application/json")
    db_session = invent_db.get_session()
    card = db_session.query(FmtDeviceCards).join(OtnDeviceBasic,
                                                 OtnDeviceBasic.id == FmtDeviceCards.device_id).filter(
        and_(OtnDeviceBasic.ip == ip,
             or_(FmtDeviceCards.slot_index == slotIndex, FmtDeviceCards.card_id == cardId))).first()
    if not card:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
                        mimetype="application/json")

    slotIndex = str(card.slot_index)
    data, errorCode, errorMsg = fmt_util.modify_config(ip, slotIndex, key, value, "FMT")
    result = {"data": data, "errorCode": errorCode, "errorMsg": errorMsg}
    return Response(json.dumps(result), mimetype="application/json")


@fmt_module.route("/config/set_note", methods=["POST"])
@admin_permission.require(http_exception=403)
def modify_port_note():
    data = request.get_json()
    ip = data.get("ip")
    slotIndex = data.get("slotIndex")
    cardId = data.get('cardId')
    port = data.get("port")
    note = data.get("note")
    if (ip is None) or (slotIndex is None and cardId is None) or (port is None) or (note is None):
        return Response(
            json.dumps({"data": "", "errorCode": 1, "errorMsg": "Ip, slotIndex, port and note is required!"}),
            mimetype="application/json")
    db_session = invent_db.get_session()
    card = db_session.query(FmtDeviceCards).join(OtnDeviceBasic,
                                                 OtnDeviceBasic.id == FmtDeviceCards.device_id).filter(
        and_(OtnDeviceBasic.ip == ip,
             or_(FmtDeviceCards.slot_index == slotIndex, FmtDeviceCards.card_id == cardId))).first()
    if not card:
        return Response(json.dumps({"data": "", "errorCode": 1, "errorMsg": "Device is not exist!"}),
                        mimetype="application/json")

    if slotIndex == "0":
        innerKey = "Slot Note"
    elif card.type == "OEO":
        innerKey = "Service Notes"
    else:
        innerKey = "Port Note"

    newPortData = fmt_util.set_note(port, innerKey, note, card.ports_data)
    db_session.query(FmtDeviceCards).filter(FmtDeviceCards.card_id == card.card_id).update({
        FmtDeviceCards.ports_data: newPortData})

    result = {"data": newPortData, "errorCode": 0, "errorMsg": ""}
    return Response(json.dumps(result), mimetype="application/json")

@fmt_module.route("/get_fmt_device_card", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_fmt_device_card():
    """
    OA板卡类型小类:
    FMT20PA-EDFA
    FMT17BA-EDFA
    FMT26PA-51EDFA
    FMT17BA-51EDFA
    FMT22BA-EDFA
    FMTPA-Array
    FMTBA-Array
    HPA
    FS-SOA
    EDFA-LA
    """

    fmt_cards_ret = {"data": [], "errorMsg": ""}
    try:
        data = request.get_json()
        ip = data["ip"]
        filter_card = data["filterCard"]
        with invent_db.get_session() as db_session:
            device_obj = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
            if not device_obj:
                raise ValueError("Device is not exist!")

            fmt_device_cards_objs = db_session.query(FmtDeviceCards).filter(
                FmtDeviceCards.device_id == device_obj.id,
                FmtDeviceCards.slot_index != 0,
                FmtDeviceCards.ports_data != "{}")

            if filter_card == "OEO":
                fmt_device_cards_objs = fmt_device_cards_objs.filter(or_(
                    FmtDeviceCards.model.like('%OEO%'),
                    FmtDeviceCards.type.like('%OEO%')
                ))
            elif filter_card == "EDFA":
                fmt_device_cards_objs = fmt_device_cards_objs.filter(or_(
                    FmtDeviceCards.model.like('%EDFA%'),
                    FmtDeviceCards.model.like('%FMTPA-Array%'),
                    FmtDeviceCards.model.like('%FMTBA-Array%'),
                    FmtDeviceCards.model.like('%HPA%'),
                    FmtDeviceCards.model.like('%SOA%'),

                ))
        ret = [
            {obj.card_id: f"{obj.model or obj.type}-1-{obj.slot_index}"}
            for obj in fmt_device_cards_objs
            if fmt_device_cards_objs
        ]
    except Exception as e:
        fmt_cards_ret["errorCode"] = 500
        fmt_cards_ret["errorMsg"] = f"Error: {str(e)}"
    else:
        fmt_cards_ret["errorCode"] = 200
        fmt_cards_ret["data"] = ret
    finally:
        return jsonify(fmt_cards_ret)


@fmt_module.route("/get_fmt_device_port", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_fmt_device_port():
    ports_ret = {"data": {}, "errorMsg": ""}
    try:
        data = request.get_json()
        card_id = data["id"]
        tabType = data["type"]

        with invent_db.get_session() as db_session:
            fmt_device_cards_obj = db_session.query(FmtDeviceCards).filter(FmtDeviceCards.card_id == card_id).first()

        if not fmt_device_cards_obj:
            raise ValueError("Card is not exist!")

        try:
            ports_info = json.loads(fmt_device_cards_obj.ports_data)
        except json.JSONDecodeError:
            raise ValueError("Failed to parse ports data")

        if fmt_device_cards_obj.type == "EDFA":
            port_1 = ports_info.get("1", {})
            port_2 = ports_info.get("2", {})

            merged_port = {
                "No": port_1.get("No", ""),
                "Name": f"{'BA' if 'BA' in fmt_device_cards_obj.model else 'PA'}",
                "EDFA Gain Value": port_1.get("EDFA Gain Value", ""),
                "Expected EDFA Gain": port_1.get("Expected EDFA Gain", ""),
                "VOA Attenuation Value": port_1.get("VOA Attenuation Value", ""),
                "VOA Attenuation Expected": port_1.get("VOA Attenuation Expected", ""),
                "Gain Slope": port_1.get("Gain Slope", ""),
                "Input Optical Power": port_1.get("Input Optical Power", ""),
                "Output Optical Power": port_2.get("Output Optical Power", ""),
                "Input Warning Threshold": port_1.get("Input Warning Threshold", ""),
                "Output Warning Threshold": port_2.get("Output Warning Threshold", ""),
                "Port Note": None
            }

            ports_info = {"1": merged_port}
            fmt_device_cards_obj.ports_data = json.dumps(ports_info)

        ports_name = [v["Name"] for k, v in ports_info.items()]
        ports_ret["data"]["port_name"] = ports_name
        ports_ret["data"]["info"] = fmt_device_cards_obj.make_dict()
        ports_ret["data"]["ports_info"] = {"ports_data": fmt_device_cards_obj.ports_data}

    except Exception as e:
        ports_ret["errorCode"] = 500
        ports_ret["errorMsg"] = f"Error: {str(e)}"
    else:
        ports_ret["errorCode"] = 200
    finally:
        return jsonify(ports_ret)


@fmt_module.route("/get_fmt_device_single_port", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_fmt_device_single_port():
    single_ports_ret = {"data": {}, "errorMsg": ""}
    try:
        data = request.get_json()
        port_name = data["portName"]
        card_id = data["cardID"]
        with invent_db.get_session() as db_session:
            fmt_device_cards_obj = db_session.query(FmtDeviceCards).filter(FmtDeviceCards.card_id == card_id).first()
        if not fmt_device_cards_obj:
            raise ValueError("Card not existed!")
        try:
            ports_info = json.loads(fmt_device_cards_obj.ports_data)
        except json.JSONDecodeError:
            raise ValueError("Failed to parse ports data")
        new_ports_info = {}
        if fmt_device_cards_obj.type == "EDFA":
            port_1 = ports_info.get("1", {})
            port_2 = ports_info.get("2", {})

            merged_port = {
                "No": port_1.get("No", ""),
                "Name": f"{'BA' if 'BA' in fmt_device_cards_obj.model else 'PA'}",
                "EDFA Gain Value": port_1.get("EDFA Gain Value", ""),
                "Expected EDFA Gain": port_1.get("Expected EDFA Gain", ""),
                "VOA Attenuation Value": port_1.get("VOA Attenuation Value", ""),
                "VOA Attenuation Expected": port_1.get("VOA Attenuation Expected", ""),
                "Gain Slope": port_1.get("Gain Slope", ""),
                "Input Optical Power": port_1.get("Input Optical Power", ""),
                "Output Optical Power": port_2.get("Output Optical Power", ""),
                "Input Warning Threshold": port_1.get("Input Warning Threshold", ""),
                "Output Warning Threshold": port_2.get("Output Warning Threshold", ""),
                "Port Note": None
            }
            new_ports_info = {"1": merged_port}
        else:
            new_ports_info = {k: v for k, v in copy.deepcopy(ports_info).items() if v["Name"] == port_name}
        tmp_ret = fmt_device_cards_obj.make_dict()
        tmp_ret["ports_data"] = json.dumps(new_ports_info)
    except Exception as e:
        single_ports_ret["errorCode"] = 500
        single_ports_ret["errorMsg"] = f"Error: {str(e)}"
    else:
        single_ports_ret["errorCode"] = 200
        single_ports_ret["data"] = tmp_ret
    finally:
        return jsonify(single_ports_ret)


@fmt_module.route("/get_filter_fmt_event", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_fmt_all_event():
    data = request.get_json()
    info = {}
    try:
        neIp = data.get("NeIp", "")
        with invent_db.get_session() as db_session:
            pre_query = db_session.query(Event).filter(Event.sn == neIp)
            page_num, page_size, total_count, alarms_list = utils.query_helper(Event, pre_query=pre_query)
    except Exception as e:
        info = {"status": 500, "msg": f"Error: {e}"}
    else:
        info = {"data": [pk.make_dict() for pk in alarms_list],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200}
    finally:
        return jsonify(info)


@fmt_module.route('/get_card_list', methods=['GET'])
@admin_permission.require(http_exception=403)
def get_card_list():
    db_session = invent_db.get_session()
    result = db_session.query(FmtDeviceCards.type, func.count(FmtDeviceCards.type)).group_by(FmtDeviceCards.type).all()
    return jsonify({"status": 200, "data": {type_value: count for type_value, count in result}})
