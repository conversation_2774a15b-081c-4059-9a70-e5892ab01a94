import logging

from flask import Blueprint, jsonify, request
from server.db.pg_engine import get_pg_session
from server.util.device_operations import delete_devices_by_sn
from server.util.permission import super_user_permission

LOG = logging.getLogger(__name__)
wireless_device_mold = Blueprint("wireless_device_mold", __name__)


@wireless_device_mold.route('/batch_delete', methods=['DELETE'])
@super_user_permission.require(http_exception=403)
def batch_delete():
    try:
        data = request.get_json()
        sn_list = data.get("snList")
        with (get_pg_session() as pg_session):
            success, device_info, status_code = delete_devices_by_sn(sn_list, pg_session)
        return jsonify({"status": status_code, "device_info": device_info})
    except Exception as e:
        return jsonify({"status": 500, "info": f"Error batch delete: {str(e)}"})
