import logging
import os
import re
import shutil
import subprocess
import zipfile
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timezone

import paramiko
from flask import Blueprint, request, jsonify, json
from flask_login import current_user

from server import constants
from server.ansible_lib.ansible_utils import ansible_job_start, add_job_record, deploy_node_exporter, delete_deploy_node_exporter
from server.celery_app.automation_task import AmpConBaseTask, beat_task
from server.db.models import automation, inventory, general
from server.db.models.automation import Playbook, PlaybookWithTag, AnsibleJob, AnsibleJobResult, AnsibleDevice
from server.db.models.general import Tag
from server.db.models.inventory import HostGroupMapping, Switch, Group, AssociationGroup, SwitchConfigSnapshotWithTag
from server.db.models.inventory import inven_db
from server.db.models.user import user_db
from server.util import ssh_util as conn_client
from server.util import utils, osutil, switch_playbook_util, http_client
from server.util.permission import admin_permission
from server.util.utils import is_name_valid

automation_db = automation.automation_db

new_automation_model = Blueprint('automation_model', __name__, template_folder='templates')
LOG = logging.getLogger(__name__)
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'


@new_automation_model.route('/get_devices', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_devices():
    page_num, page_size, total_count, query_device = utils.query_helper(AnsibleDevice)
    ansible_device_data = [device.make_dict() for device in query_device]
    for data in ansible_device_data:
        if 'device_pwd' in data:
            data['device_pwd'] = ''
        if data.get("device_ssh_key_path", ""):
            data['type'] = 'pkey'
        else:
            data['type'] = 'pwd'
    return jsonify({"data": ansible_device_data, "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_automation_model.route("/del_device/<device_name>", methods=["GET"])
@admin_permission.require(http_exception=403)
def del_device(device_name):
    try:
        session = inven_db.get_session()
        device = session.query(AnsibleDevice).filter(AnsibleDevice.device_name == device_name).first()
        if device:
            device_pkey_volume = f"{constants.AUTOMATION_BASE_DIR}/server/ansible_device_ssh_key"
            device_pkey_path = f"{device_pkey_volume}/{device_name}"
            if os.path.exists(device_pkey_path):
                os.remove(device_pkey_path)
        with session.begin(subtransactions=True):
            query_device = session.query(AnsibleDevice).filter(AnsibleDevice.device_name == device_name)
            device = [
                {"device_ip": i.ip, "device_name": i.device_name, "device_user": i.device_user, "device_pwd": i.device_pwd,
                 "device_ssh_key_path": i.device_ssh_key_path, "device_port": i.device_port, "device_sudo_pass": i.device_pwd}
                for i in query_device]
            delete_deploy_node_exporter.delay(device)
            query_device.delete(synchronize_session=False)

    except Exception as e:
        LOG.error(str(e))
        return jsonify({"status": 500, "info": "Delete %s device failed" % device_name})
    else:
        return jsonify({"status": 200, "info": "Delete %s device success" % device_name})


@new_automation_model.route("/enable_device_monitor", methods=["POST"])
@admin_permission.require(http_exception=403)
def monitor_device():
    try:
        data = request.get_json()
        device_name = data.get("deviceName", "")
        device_sudo_pass = data.get('deviceSudoPass', "")
        db_session = automation_db.get_session()
        ansible_device = db_session.query(AnsibleDevice).filter(AnsibleDevice.device_name == device_name).first()
        if ansible_device.device_pwd:
            device_sudo_pass = ansible_device.device_pwd
        device = [
            {"device_ip": ansible_device.ip, "device_name": ansible_device.device_name, "device_user": ansible_device.device_user, "device_pwd": ansible_device.device_pwd,
             "device_ssh_key_path": ansible_device.device_ssh_key_path, "device_port": ansible_device.device_port, "device_sudo_pass": device_sudo_pass}]

        deploy_node_exporter.delay(device)

    except Exception as e:
        LOG.error(str(e))
        return jsonify({"status": 500, "info": "Enable monitor device failed"})
    else:
        return jsonify({"status": 200, "info": "Enable monitor device success"})


def check_ssh_status(host_ip, port, username, password, keypath, id):
    timeout = 10
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        if keypath:
            pkey = paramiko.RSAKey.from_private_key_file(keypath)
            ssh.connect(host_ip, port=port, username=username, pkey=pkey, timeout=timeout)
            return 200, id
        if password:
            ssh.connect(host_ip, port=port, username=username, password=password, timeout=timeout)
            return 200, id
    except Exception as e:
        LOG.error(str(e))
        return 500, id
    finally:
        ssh.close()


def batch_check_ssh_status(batch_dict):
    results = []
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = {executor.submit(check_ssh_status, info["ip"], info["port"], info["username"], info["password"], info["device_ssh_key_path"], id): id for id, info in
                   batch_dict.items()}
        for future in as_completed(futures):
            try:
                status, device_id = future.result()
                results.append({"status": status, "id": device_id})
            except Exception as e:
                LOG.error(str(e))
                results.append({"status": 500, "id": device_id})
    return results


def chunk_list(lst, n):
    for i in range(0, len(lst), n):
        yield lst[i:i + n]


@new_automation_model.route('/check_ping_devices', methods=['POST'])
@admin_permission.require(http_exception=403)
def check_ping_devices():
    data = request.get_json()
    host_list = []
    all_results = []

    db_session = automation_db.get_session()
    with db_session.begin(subtransactions=True):
        try:
            for host in data.get("deviceList", []):
                device = db_session.query(AnsibleDevice).filter(AnsibleDevice.id == host["id"]).first()
                if device:
                    host_list.append(device)
                else:
                    host_list.append(None)
        finally:
            db_session.close()

    batch_size = 5
    batches = []
    for host_batch in chunk_list(host_list, batch_size):
        batch_dict = {host.id: {"ip": host.ip, "port": host.device_port, "username": host.device_user, "password": host.device_pwd, "device_ssh_key_path": host.device_ssh_key_path}
                      for host in host_batch}
        batches.append(batch_dict)
        print(f"Processing batch: {batch_dict}")

    with ThreadPoolExecutor(max_workers=batch_size) as executor:
        future_batch = [executor.submit(batch_check_ssh_status, batch_dict) for batch_dict in batches]
        for future in as_completed(future_batch):
            results = future.result()
            all_results.extend(results)

    return jsonify({"status": 200, "data": all_results})


@new_automation_model.route('/reload_device/<string:device_name>', methods=['POST'])
@admin_permission.require(http_exception=403)
def reload_device(device_name):
    try:
        session = inven_db.get_session()
        device = session.query(AnsibleDevice).filter(AnsibleDevice.device_name == device_name).first()
        pre_reload_cmd = "sudo shutdown -r +5"
        reload_cmd = "sudo shutdown -r now"
        if not device:
            return jsonify({"status": 500, "info": "Device not found"})
        ssh_session, status, _ = conn_client.get_interactive_session(
            device.ip, username=device.device_user, password=device.device_pwd, timeout=10
        )
        if status != constants.RMA_ACTIVE:
            return jsonify({"status": 500, "info": "Failed to connect to device"})
        res, status_code = conn_client.interactive_shell_linux_with_conn(ssh_session, pre_reload_cmd)
        if status_code == constants.RMA_ACTIVE and "sudo" not in res:
            conn_client.interactive_shell_linux_with_conn(ssh_session, reload_cmd)
            LOG.info(f"Device {device_name} reload success: {res}")
            return jsonify({"status": 200, "info": "Device reload success"})
        else:
            LOG.error(f"Device {device_name} reload failed: {res}")
            return jsonify({"status": 500, "info": res})
    except Exception as e:
        LOG.error(f"Error reloading device {device_name}: {str(e)}")
        return jsonify({"status": 200, "info": str(e)})


@new_automation_model.route('/power_down_device/<string:device_name>', methods=['POST'])
@admin_permission.require(http_exception=403)
def power_down_device(device_name):
    try:
        session = inven_db.get_session()
        device = session.query(AnsibleDevice).filter(AnsibleDevice.device_name == device_name).first()
        pre_power_off_cmd = "sudo shutdown -h +5"
        power_off_cmd = "sudo shutdown -h now"
        if not device:
            return jsonify({"status": 500, "info": "Device not found"})
        ssh_session, status, _ = conn_client.get_interactive_session(
            device.ip, username=device.device_user, password=device.device_pwd, timeout=10
        )
        if status != constants.RMA_ACTIVE:
            return jsonify({"status": 500, "info": "Failed to connect to device"})
        res, status_code = conn_client.interactive_shell_linux_with_conn(ssh_session, pre_power_off_cmd)
        if status_code == constants.RMA_ACTIVE and "sudo" not in res:
            conn_client.interactive_shell_linux_with_conn(ssh_session, power_off_cmd)
            LOG.info(f"Device {device_name} power down success: {res}")
            return jsonify({"status": 200, "info": "Device power down success"})
        else:
            LOG.error(f"Device {device_name} power down failed: {res}")
            return jsonify({"status": 500, "info": res})
    except Exception as e:
        LOG.error(f"Error powering down device {device_name}: {str(e)}")
        return jsonify({"status": 200, "info": str(e)})


@new_automation_model.route('/power_up_device', methods=['POST'])
@admin_permission.require(http_exception=403)
def power_up_device():
    data = request.get_json()
    LOG.info(f"Power up device data: {data}")
    bmc_ip = data.get("bmcIp", "")
    bmc_username = data.get("bmcUsername", "")
    bmc_password = data.get("bmcPassword", "")
    power_on_cmd = "ipmitool -I lanplus -H {} -U {} -P {} chassis power on".format(bmc_ip, bmc_username, bmc_password)
    try:
        result = subprocess.run(power_on_cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            return jsonify({"status": 200, "info": result.stdout.strip()})
        else:
            LOG.error(f"Power up device failed: {result.stderr}")
            return jsonify({"status": 500, "info": result.stderr.strip()})
    except Exception as e:
        LOG.error(f"Power up device error: {str(e)}")
        return jsonify({"status": 500, "info": str(e)})


@new_automation_model.route("/create_device", methods=["POST"])
@admin_permission.require(http_exception=403)
def create_device(device_info=None):
    data = device_info if device_info else request.get_json()
    device_name = data['deviceName']
    device_ip = data['deviceIp']
    device_port = data['devicePort']
    device_user = data['deviceUser']
    device_pwd = data.get('devicePassword', '')
    device_pkey = data.get('devicePkey', '')
    device_sudo_pass = data.get('deviceSudoPass', device_pwd)
    device_pkey_path = ""
    enable_monitor = data['enableMonitor']
    groups = data.get('groups', [])

    if device_pkey:
        device_pkey_volume = f"{constants.AUTOMATION_BASE_DIR}/server/ansible_device_ssh_key"
        if not os.path.exists(device_pkey_volume):
            os.makedirs(device_pkey_volume)
        device_pkey_path = f"{device_pkey_volume}/{device_name}"
        with open(device_pkey_path, "w+") as f:
            f.write(device_pkey)
        os.chmod(device_pkey_path, 0o600)

    if not is_name_valid(device_name):
        return jsonify({'status': 500, 'info': 'Name is invalid.'})

    db_session = automation_db.get_session()
    with db_session.begin(subtransactions=True):
        playbook = db_session.query(AnsibleDevice).filter(AnsibleDevice.device_name == device_name).first()
        if playbook:
            msg = {'status': 500, 'info': 'Name {0} already exists in database.'.format(device_name)}
            return jsonify(msg)

        ad = AnsibleDevice(device_name=device_name, device_user=device_user, device_pwd=device_pwd, ip=device_ip, device_port=device_port, device_ssh_key_path=device_pkey_path)
        db_session.add(ad)

    if groups:
        inventory_db = inven_db.get_session()
        with inventory_db.begin(subtransactions=True):
            for group_name in groups:
                mapping = HostGroupMapping(device_name=device_name, group_name=group_name)
                inventory_db.add(mapping)

    if enable_monitor:
        query_devices = db_session.query(AnsibleDevice).filter(AnsibleDevice.device_name == device_name).first()
        device = [
            {"device_ip": query_devices.ip, "device_name": query_devices.device_name, "device_user": query_devices.device_user, "device_pwd": query_devices.device_pwd,
             "device_ssh_key_path": query_devices.device_ssh_key_path, "device_port": query_devices.device_port, "device_sudo_pass": device_sudo_pass}]

        deploy_node_exporter.delay(device)

    msg = {'status': 200, 'info': 'Add device success'}
    return jsonify(msg)


@new_automation_model.route("/batch_create_device", methods=["POST"])
@admin_permission.require(http_exception=403)
def batch_create_device():
    data = request.get_json()
    device_list = data['deviceList']
    enableMonitor = data.get('enableMonitor', False)
    groups = data.get('groups', [])
    errmsg = []
    visited_device_names = set()
    session = inven_db.get_session()
    existing_device_names = set(x.device_name.lower() for x in session.query(AnsibleDevice.device_name).all())

    # 预处理所有设备，收集错误，避免重复插入
    devices_to_create = []
    for device_file in device_list:
        for file_name, rows in device_file.items():
            for index, row in enumerate(rows):
                device_name = str(row.get("Device Name", "")).strip()
                device_ip = str(row.get("IP", "")).strip()
                device_user = str(row.get("Username", "")).strip()
                device_pwd = str(row.get("Password", "")).strip()
                device_port = str(row.get("Port", "")).strip()

                row_num = 0

                # 跳过模板行
                if device_name == "Required. The name of the device (1-255 characters in length)." and index == 0:
                    row_num = index + 3
                    continue
                else:
                    row_num = index + 2
                if not all([device_name, device_ip, device_user, device_pwd, device_port]):
                    errmsg.append(f"File \"{file_name}\" row {row_num}: You have unfilled sections.")
                    continue
                if not is_name_valid(device_name) or not (1 <= len(device_name) <= 255):
                    errmsg.append(f"File \"{file_name}\" row {row_num}: You entered the wrong Device Name.")
                if device_name.lower() in existing_device_names:
                    errmsg.append(f"File \"{file_name}\" row {row_num}: Device Name \"{device_name}\" already exists in database.")
                    continue
                if device_name in visited_device_names:
                    errmsg.append(f"File \"{file_name}\" row {row_num}: Duplicated Device Name \"{device_name}\" found in the file.")
                    continue
                visited_device_names.add(device_name)
                if not re.match(r"^\d{1,3}(\.\d{1,3}){3}$", device_ip):
                    errmsg.append(f"File \"{file_name}\" row {row_num}: You entered the wrong IP.")
                if not (device_port.isdigit() and 1 <= int(device_port) <= 65535):
                    errmsg.append(f"File \"{file_name}\" row {row_num}: You entered the wrong Port.")
                if not re.match(r"^[a-zA-Z0-9_]{1,255}$", device_user):
                    errmsg.append(f"File \"{file_name}\" row {row_num}: You entered the wrong Username.")
                if not re.match(r"^[a-zA-Z0-9_!@#$%^&*()\-+=]{1,255}$", device_pwd):
                    errmsg.append(f"File \"{file_name}\" row {row_num}: You entered the wrong Password.")

                # 校验通过，加入待创建列表
                devices_to_create.append({
                    "deviceName": device_name,
                    "deviceIp": device_ip,
                    "deviceUser": device_user,
                    "devicePassword": device_pwd,
                    "devicePort": device_port,
                    "enableMonitor": enableMonitor,
                    "groups": groups
                })

    if errmsg:
        return jsonify({"status": 500, "info": errmsg})

    for device_info in devices_to_create:
        create_device(device_info)

    msg = {'status': 200, 'info': 'Add devices success'}
    return jsonify(msg)


@new_automation_model.route('/get_job', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_job_list():
    page_num, page_size, total_count, query_job = utils.query_helper(AnsibleJob)
    return jsonify({"data": [job.make_dict() for job in query_job], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_automation_model.route('/get_job_by_switch', methods=['POST'])
def get_job_by_switch():
    db_session = user_db.get_session()
    switch_query = db_session.query(inventory.Switch).filter(inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                                                          constants.SwitchStatus.IMPORTED]))
    page_num, page_size, total_count, query_job = utils.query_helper(Switch, pre_query=switch_query)
    return jsonify({"data": [job.make_dict() for job in query_job], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_automation_model.route('/get_task_result_by_id/<job_id>', methods=['GET'])
@admin_permission.require(http_exception=403)
def get_task_result_by_id(job_id):
    db_session = inven_db.get_session()
    job_detail = db_session.query(AnsibleJobResult).filter(AnsibleJobResult.id == job_id).first()
    return jsonify({"info": job_detail.result, "status": 200})


@new_automation_model.route('/get_task_result/<string:job_name>', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_task_result(job_name):
    db_session = inven_db.get_session()
    job_query = db_session.query(AnsibleJobResult).filter(AnsibleJobResult.job_name == job_name)
    page_num, page_size, total_count, query_job_ret = utils.query_helper(AnsibleJobResult, pre_query=job_query)
    return jsonify({"data": [job.make_dict() for job in query_job_ret], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_automation_model.route('/get_task_result_by_sn/<string:sn>', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_job_results_by_sn(sn):
    db_session = inven_db.get_session()
    job_query = db_session.query(AnsibleJobResult).filter(AnsibleJobResult.switch_sn == sn)
    page_num, page_size, total_count, query_job_ret = utils.query_helper(AnsibleJobResult, pre_query=job_query)
    return jsonify({"data": [job.make_dict() for job in query_job_ret], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_automation_model.route('/get_task_result_output', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_task_result_output():
    data = request.get_json()
    session = automation_db.get_session()
    job_result_str = ''
    if 'sn' in data:
        job_result = session.query(AnsibleJobResult).filter(AnsibleJobResult.switch_sn == data['sn']).order_by(
            AnsibleJobResult.create_time.desc()).limit(10)
        for i in job_result:
            result_str = '''------------------------------------------------------------------\nRun Time: {5}\nJob Name: {0}\nTask Name: {1}\nTask Status: {4}\nSN: {2}\nTask Result:\n{3}\n\n\n''' \
                .format(i.job_name, i.task_name, i.switch_sn, i.result, 'Success' if i.state else 'Failed',
                        i.modified_time)
            job_result_str += result_str
    elif 'job_name' in data:
        job_info = session.query(AnsibleJob).filter(AnsibleJob.name == data['job_name']).first()
        if not job_info:
            return 'No Task results'
        job_schedule_type = job_info.schedule_type

        if job_schedule_type != 'DIRECT':
            job_result = session.query(AnsibleJobResult).filter(AnsibleJobResult.job_name == data['job_name']).order_by(
                AnsibleJobResult.create_time.desc()).limit(30)
            for i in job_result:
                result_str = '''------------------------------------------------------------------\nRun Time: {5}\nJob Name: {0}\nTask Name: {1}\nTask Status: {4}\nSN: {2}\nTask Result:\n{3}\n\n\n''' \
                    .format(i.job_name, i.task_name, i.switch_sn, i.result, 'Success' if i.state else 'Failed',
                            i.modified_time)
                job_result_str += result_str
        else:
            job_result = session.query(AnsibleJobResult).filter(AnsibleJobResult.job_name == data['job_name']).order_by(
                AnsibleJobResult.create_time.desc()).all()
            job_result_str += '''JOB NAME [{0}]\n\n'''.format(data['job_name'])

            status_counter = {}
            for i in job_result:
                if not i.switch_sn in status_counter:
                    status_counter[i.switch_sn] = {'ok': 0, 'changed': 0, 'unreachable': 0, 'failed': 0}

                task_status = 'OK' if i.state else 'FAILED'

                if i.state:
                    status_counter[i.switch_sn]['ok'] += 1
                else:
                    status_counter[i.switch_sn]['failed'] += 1

                stdout_in_json = ''
                try:
                    task_json = json.loads(i.result)
                    if 'unreachable' in task_json and task_json['unreachable'] == True:
                        task_status = 'UNREACHABLE'
                        status_counter[i.switch_sn]['unreachable'] += 1
                        status_counter[i.switch_sn]['failed'] -= 1
                    if 'changed' in task_json and task_json['changed'] == True:
                        status_counter[i.switch_sn]['changed'] += 1
                    if 'stdout' in task_json:
                        stdout_in_json += 'stdout:\n{0}\n'.format(
                            task_json['stdout'] if task_json['stdout'] else 'None')
                    if 'stderr' in task_json:
                        stdout_in_json += 'stderr: {0}\n'.format(task_json['stderr'] if task_json['stderr'] else 'None')
                except Exception as _:
                    pass

                job_result_str += 'TASK [{0}] {1}\n'.format(i.task_name, '*' * (120 - len(i.task_name) - 8))
                job_result_str += 'SN: {0}, Status: {1}\n'.format(i.switch_sn, task_status)
                job_result_str += stdout_in_json
                job_result_str += 'details:\n{0}\n'.format(i.result)

            job_result_str += 'PLAY RECAP {0}\n'.format('*' * (120 - 11))
            for sn in status_counter.keys():
                job_result_str += 'SN [{0}]:     OK={1}    CHANGED={2}    UNREACHABLE={3}    FAILED={4}\n'.format(
                    sn, status_counter[sn]['ok'], status_counter[sn]['changed'], status_counter[sn]['unreachable'],
                    status_counter[sn]['failed'])

    if job_result_str == '':
        job_result_str = 'No Task results'
    return jsonify({'status': 200, 'info': job_result_str})


@new_automation_model.route('/get_job_details', methods=['GET'])
@admin_permission.require(http_exception=403)
def get_job_details():
    user_name = current_user.id
    user_type = current_user.type

    db_session = automation_db.get_session()
    job_list = db_session.query(AnsibleJob).all()
    job_detail_list = []
    for job in job_list:
        if job.create_user == user_name or user_type == 'superuser':
            obj = {}
            obj['playbook_name'] = job.playbook_name
            obj['job_name'] = job.name
            obj['schedule_type'] = job.schedule_type
            if job.schedule_type == 'DIRECT':
                obj['execute_time'] = job.create_time
                job_detail_list.append(obj)
            elif job.schedule_type == 'ONCE':
                obj['execute_time'] = json.loads(job.schedule_param)['start_time']
                job_detail_list.append(obj)
            elif job.schedule_type == 'SCHEDULED':
                job_info = beat_task.get_active_job_by_name(job.name)
                if not isinstance(job_info, str):
                    obj['execute_time'] = job_info.last_run_at if job_info.last_run_at else job_info.start_time
                    job_detail_list.append(obj)

    msg = {'status': 200, 'info': job_detail_list}
    return jsonify(msg)


@new_automation_model.route('/terminate_job/<string:job_name>', methods=['GET'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='terminate_job', contents='remove & terminate job: {job_name}')
def terminate_job(job_name):
    beat_task.remove_job(job_name)
    AmpConBaseTask.kill_process_by_task_name(job_name)
    automation_db.delete_ansible_job(job_name)
    msg = {'status': 200, 'info': 'Success to terminate and remove job {0}.'.format(job_name)}
    return jsonify(msg)


@new_automation_model.route('/get_playbook_list', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_playbook_list():
    session = automation_db.get_session()
    data = request.get_json()
    show_pre_built_tag = data.get("showPreBuiltTag", False)
    if show_pre_built_tag:
        pre_query = session.query(PlaybookWithTag).order_by(PlaybookWithTag.internal.desc())
    else:
        pre_query = session.query(PlaybookWithTag).filter(PlaybookWithTag.internal == 0)
    page_num, page_size, total_count, playbooks = utils.query_helper(PlaybookWithTag, pre_query=pre_query)
    return jsonify({"data": [pk.make_dict() for pk in playbooks], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_automation_model.route('/update_internal_playbook', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='add_playbook', contents='update internal playbook')
def update_internal_playbook():
    success = 0
    failed = 0
    for playbook in switch_playbook_util.download_internal_playbooks():
        try:
            switch_playbook_util.delete_playbook_implement(playbook['name'], db_session=automation_db.get_session())
            if \
                    switch_playbook_util.add_internal_playbook(playbook['name'], playbook['description'],
                                                               playbook['path'], '',
                                                               True)['status'] == 200:
                success += 1
            else:
                failed += 1
        except:
            failed += 1
    if success == 0 and failed == 0:
        return jsonify({'status': 500,
                        'info': 'Update pre-built playbook failed, please check git connection'})
    if failed == 0:
        return jsonify({'status': 200, 'info': 'Update pre-built playbook success.'})
    else:
        return jsonify({'status': 500,
                        'info': 'Update pre-built playbook success:[{}], failed:[{}].'.format(str(success),
                                                                                              str(failed))})


@new_automation_model.route('/get_playbook_groups', methods=['POST'])
def get_playbook_groups():
    group_type = request.get_json().get('groupType', 'switch')
    page_num, page_size, total_count, groups = utils.query_helper(Group, pre_query=utils.get_user_group().filter(Group.group_name.notlike('vtep_tab%'),
                                                                                                                 Group.group_type == group_type))
    return jsonify({"data": [group.make_dict() for group in groups], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_automation_model.route('/pre_create_playbook', methods=['POST'])
@admin_permission.require(http_exception=403)
def init_create_playbook():
    data = request.get_json()
    name = data.get('name')

    if not name or not is_name_valid(name):
        return jsonify({'status': 500, 'info': 'Name is invalid.'})

    db_session = automation_db.get_session()
    playbook = db_session.query(Playbook).filter(Playbook.name == name).first()
    if playbook:
        return jsonify({'status': 500, 'info': f'Name {name} already exists.'})

    return jsonify({'status': 200, 'info': 'Add playbook success'})


@new_automation_model.route('/add_playbook', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='add_playbook', contents='add a playbook: {name}')
def add_playbook():
    data = request.get_json()
    name = data['name']
    description = data['description']
    nodelist = data['filelist']
    create_user = current_user.id

    if not is_name_valid(name):
        return jsonify({'status': 500, 'info': 'Name is invalid.'})

    return jsonify(switch_playbook_util.add_playbook_implement(name, description, nodelist, create_user))


@new_automation_model.route('/get_playbook_filelist/<string:playbook_name>', methods=['GET'])
@admin_permission.require(http_exception=403)
def get_playbook_filelist(playbook_name):
    def list_dir(path, res):
        for i in os.listdir(path):
            temp_dir = os.path.join(path, i)
            if os.path.isdir(temp_dir):
                temp = {'name': i, 'dirname': temp_dir.replace(constants.ANSIBLE_PLAYBOOK_DIR, ''), 'children': []}
                res['children'].append(list_dir(temp_dir, temp))
            else:
                res['children'].append({'name': i, 'dirname': temp_dir.replace(constants.ANSIBLE_PLAYBOOK_DIR, '')})
        return res

    playbook_path = os.path.realpath(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name))
    if playbook_path.startswith(constants.ANSIBLE_PLAYBOOK_DIR):
        res = {'name': playbook_name, 'dirname': playbook_path.replace(constants.ANSIBLE_PLAYBOOK_DIR, ''),
               'children': []}
        list_dir(playbook_path, res)
        return jsonify({'status': 200, 'info': [res]})
    else:
        return jsonify({'status': 500, 'info': 'Playbook file path is invalid'})


@new_automation_model.route('/get_playbook_file_content', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_playbook_file_content():
    params = request.get_json()
    filepath = params['filepath']
    filepath = os.path.realpath(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, filepath))
    if os.path.exists(filepath) and filepath.startswith(constants.ANSIBLE_PLAYBOOK_DIR):
        with open(filepath) as f:
            filecontent = f.read()
            return jsonify({'status': 200, 'info': filecontent})
    else:
        return jsonify({'status': 500, 'info': 'Playbook file path is invalid'})


@new_automation_model.route('/edit_playbook', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='edit_playbook', contents='edit a playbook: {name}')
def edit_playbook():
    data = request.get_json()
    name = data['name']
    description = data['description']
    edit_events = data['editEvents']

    if not is_name_valid(name):
        return jsonify({'status': 500, 'info': 'Name is invalid.'})

    if switch_playbook_util.is_internal_playbook(name):
        return jsonify({'status': 500, 'info': 'Name {0} is internal playbook, cannot be edited.'.format(name)})

    db_session = automation_db.get_session()
    playbook = db_session.query(Playbook).filter(Playbook.name == name).first()
    if not playbook:
        msg = {'status': 500, 'info': 'Name {0} does not exist in database.'.format(name)}
        return jsonify(msg)

    # param check file path
    # delete file param check
    for del_file in edit_events['delete_file']:
        del_file_path = os.path.realpath(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, del_file))
        if not del_file_path.startswith(constants.ANSIBLE_PLAYBOOK_DIR):
            return jsonify({'status': 500, 'info': 'Param edit_events->del_file_path is invalid'})

    # delete folder param check
    for del_folder in edit_events['delete_folder']:
        del_folder_path = os.path.realpath(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, del_folder))
        if not del_folder_path.startswith(constants.ANSIBLE_PLAYBOOK_DIR):
            return jsonify({'status': 500, 'info': 'Param edit_events->del_folder_path is invalid'})

    # add folder param check
    for add_folder in edit_events['add_folder']:
        add_folder_path = os.path.realpath(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, add_folder))
        if not add_folder_path.startswith(constants.ANSIBLE_PLAYBOOK_DIR):
            return jsonify({'status': 500, 'info': 'Param edit_events->add_folder_path is invalid'})

    # add file param check
    for add_file in edit_events['add_file']:
        add_file_path = os.path.realpath(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, add_file))
        if not add_file_path.startswith(constants.ANSIBLE_PLAYBOOK_DIR):
            return jsonify({'status': 500, 'info': 'Param edit_events->add_file_path is invalid'})

    # modify file param check
    for filePath, fileContent in edit_events['modify'].items():
        modify_file_path = os.path.realpath(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, filePath))
        if not modify_file_path.startswith(constants.ANSIBLE_PLAYBOOK_DIR):
            return jsonify({'status': 500, 'info': 'Param edit_events->modify_file_path is invalid'})

    # delete file
    for del_file in edit_events['delete_file']:
        del_file_path = os.path.realpath(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, del_file))
        if os.path.exists(del_file_path):
            os.remove(del_file_path)

    # delete folder
    for del_folder in edit_events['delete_folder']:
        del_folder_path = os.path.realpath(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, del_folder))
        if os.path.exists(del_folder_path):
            shutil.rmtree(del_folder_path)

    # add folder
    for add_folder in edit_events['add_folder']:
        add_folder_path = os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, add_folder)
        if not os.path.isdir(add_folder_path):
            os.makedirs(add_folder_path)

    # add file
    for add_file in edit_events['add_file']:
        add_file_path = os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, add_file)
        if not os.path.isfile(add_file_path):
            fd = open(add_file_path, 'w+')
            fd.close()

    # modify file
    for filePath, fileContent in edit_events['modify'].items():
        modify_file_path = os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, filePath)
        if os.path.exists(modify_file_path):
            os.chmod(modify_file_path, 493)  # 493 represent 0x755
            with open(modify_file_path, 'w+') as f:
                f.write(fileContent)

    with db_session.begin(subtransactions=True):
        playbook.description = description

    http_client.start_transfer_file('', [{'filename': name, 'path': os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, name),
                                          'dest': os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, name)}])

    msg = {'status': 200, 'info': 'Edit playbook success'}
    return jsonify(msg)


@new_automation_model.route('/check_playbook/<string:playbook_name>', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='check_playbook', contents='check a playbook: {playbook_name}')
def check_playbook(playbook_name):
    playbook_path = os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name, 'playbook.yml')
    if os.path.exists(playbook_path):
        env = os.environ.copy()
        env["ANSIBLE_LIBRARY"] = f"{constants.AUTOMATION_BASE_DIR}/server/ansible_lib"
        try:
            command = ['ansible-playbook', '--syntax-check', playbook_path]
            subprocess.run(command, env=env, capture_output=True, text=True, check=True)
            return jsonify({"status": 200, "info": "Playbook yaml is valid"})
        except Exception as e:
            LOG.error(e.stdout)
            LOG.error(e.stderr)
            return jsonify({"status": 500, 'info': 'Yaml error: {0}{1}'.format(e.stdout, e.stderr)})
    else:
        return jsonify({"status": 500, 'info': 'Playbook file path is invalid'})


@new_automation_model.route('/save_as_playbook', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='save_as_playbook', contents='save playbook {srcPlaybookName} as {playbookName}')
def save_as_playbook():
    """
    {
        'src_playbook_name': 'xxx',
        'playbook_name': 'xxx'
    }
    """
    data = request.get_json()
    src_playbook_name = data['srcPlaybookName']
    user_name = current_user.id
    playbook_name = data['playbookName']

    if not is_name_valid(playbook_name):
        return jsonify({'status': 500, 'info': 'Name is invalid.'})

    db_session = automation_db.get_session()
    src_playbook_entry = db_session.query(Playbook).filter(Playbook.name == src_playbook_name).first()
    if not src_playbook_entry:
        msg = {'status': 500, 'info': 'Playbook name {0} does not exist in the database.'.format(playbook_name)}
        return jsonify(msg)
    playbook_entry = db_session.query(Playbook).filter(Playbook.name == playbook_name).first()
    if playbook_entry:
        msg = {'status': 500, 'info': f'Name {playbook_name} already exists.'}
        return jsonify(msg)

    # copy files to dst
    src_path = os.path.realpath(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, src_playbook_name))
    if not src_path.startswith(constants.ANSIBLE_PLAYBOOK_DIR):
        return jsonify({'status': 500, 'info': 'Src_playbook_name is invalid.'})
    dst_path = os.path.realpath(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name))
    if not dst_path.startswith(constants.ANSIBLE_PLAYBOOK_DIR):
        return jsonify({'status': 500, 'info': 'Playbook_name is invalid.'})
    try:
        shutil.copytree(src_path, dst_path)
        http_client.start_transfer_file('', [{'filename': playbook_name, 'path': dst_path,
                                              'dest': dst_path}])
    except Exception as e:
        logging.error(str(e))
        msg = {'status': 500, 'info': 'Playbook {0} files can not be saved'.format(playbook_name)}
        return jsonify(msg)

    # insert into database
    try:
        new_playbook = Playbook()
        new_playbook.name = playbook_name
        new_playbook.description = src_playbook_entry.description
        new_playbook.create_user = user_name
        new_playbook.playbook_path = os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name, 'playbook.yml')
        automation_db.insert(new_playbook, db_session)
    except Exception as e:
        LOG.error('playbook {0} failed when insert database: {1}'.format(playbook_name, e))
        shutil.rmtree(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name))
        msg = {'status': 500, 'info': 'Can not insert the record into database.'}
        return jsonify(msg)

    msg = {'info': 'Success to save playbook "{0}"'.format(playbook_name), 'status': 200}
    return jsonify(msg)


@new_automation_model.route('/export_playbook/<string:playbook_name>', methods=['GET'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='export_playbook', contents='export playbook: {playbook_name}')
def export_playbook(playbook_name):
    zip_path = os.path.realpath(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name))
    if not zip_path.startswith(constants.ANSIBLE_PLAYBOOK_DIR):
        return jsonify({'info': 'Playbook_name is invalid', 'status': '500'})
    dst_tmp_file = 'tmp/playbook/{0}'.format(playbook_name)
    osutil.ensure_path('tmp/playbook')
    utils.zipFolder(zip_path=zip_path, zip_file_path=dst_tmp_file)

    with open(dst_tmp_file, 'rb') as f:
        data = f.read()

    return utils.wrap_file_stream(data, '{0}.zip'.format(playbook_name))


@new_automation_model.route('/import_playbook', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='import_playbook', contents='import playbook: {playbookName}')
def import_playbook():
    """
    form
        playbook_name: 'xxx',
        description: 'xxx',
        import_file: 'xxx'
    """
    data = request.form
    playbook_name = data['playbookName']
    description = data['playbookDesc']
    create_user = current_user.id

    match = bool(re.match(r'^\s+$', playbook_name))
    if match:
        msg = {'status': 500, 'info': 'Playbook_name cannot be empty.'}
        return jsonify(msg)

    if not is_name_valid(playbook_name):
        return jsonify({'status': 500, 'info': 'Name is invalid.'})

    db_session = automation_db.get_session()
    playbook = db_session.query(Playbook).filter(Playbook.name == playbook_name).first()
    if playbook:
        msg = {'status': 500, 'info': f'Name {playbook_name} already exists.'}
        return jsonify(msg)

    import_file = request.files['playbookFile']

    dst_tmp_file = 'tmp/playbook/{0}'.format(playbook_name)
    if not 'tmp/playbook/' in dst_tmp_file:
        return jsonify({'info': 'Playbook_name is invalid', 'status': 500})
    osutil.ensure_path('tmp/playbook')
    import_file.save(dst_tmp_file)

    if not zipfile.is_zipfile(dst_tmp_file):
        os.remove(dst_tmp_file)
        return jsonify({'info': 'This file is not a valid zip file.', 'status': 500})

    unzip_path = os.path.realpath(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name))
    if not unzip_path.startswith(constants.ANSIBLE_PLAYBOOK_DIR):
        return jsonify({'info': 'Playbook_name is invalid', 'status': 500})
    utils.unzipFile(zip_file_path=dst_tmp_file, unzip_path=unzip_path)

    try:
        new_playbook = Playbook()
        new_playbook.name = playbook_name
        new_playbook.description = description
        new_playbook.create_user = create_user
        new_playbook.playbook_path = os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name, 'playbook.yml')
        automation_db.insert(new_playbook, db_session)
    except Exception as e:
        LOG.error('Import playbook {0} failed when insert database: {1}'.format(playbook_name, e))
        shutil.rmtree(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name))
        msg = {'status': 500, 'info': 'Can not insert the record into database.'}
        return jsonify(msg)

    http_client.start_transfer_file('', [
        {'filename': playbook_name, 'path': os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name),
         'dest': os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, playbook_name)}])

    msg = {'info': 'Success to import playbook "{0}"'.format(playbook_name), 'status': 200}
    return jsonify(msg)


@new_automation_model.route('/delete_playbook/<string:playbook_name>', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='delete_playbook', contents='delete a playbook: {playbook_name}')
def delete_playbook(playbook_name):
    db_session = automation_db.get_session()
    if switch_playbook_util.is_internal_playbook(playbook_name):
        return jsonify(
            {'status': 500, 'info': 'Name {0} is internal playbook, cannot be deleted.'.format(playbook_name)})
    return jsonify(
        switch_playbook_util.delete_playbook_implement(playbook_name, db_session=db_session))


@new_automation_model.route("/update_tag", methods=["POST"])
@admin_permission.require(http_exception=403)
def update_tag():
    data = request.get_json()
    record_id = data.get('recordId', '')
    record_type = data.get('recordType', '')
    tag_content = data.get('tagContent', '')
    if record_type not in ['template', 'playbook', 'snapshot']:
        msg = {'info': 'record_type invalid', 'status': 500}
    elif record_type == 'template' and (not record_id or not inven_db.get_collection(general.GeneralTemplate, filters={'id': [record_id]})):
        msg = {'info': 'template id not found', 'status': 500}
    elif record_type == 'playbook' and (not record_id or not inven_db.get_collection(Playbook, filters={'id': [record_id]})):
        msg = {'info': 'playbook id not found', 'status': 500}
    elif record_type == 'snapshot' and (not record_id or not inven_db.get_collection(SwitchConfigSnapshotWithTag, filters={'id': [record_id]})):
        msg = {'info': 'snapshot id not found', 'status': 500}
    else:
        if record_type == 'snapshot' and 'GOLDEN_CONFIG' in tag_content:
            msg = {'info': 'snapshot tag cannot contain GOLDEN_CONFIG', 'status': 500}
        elif ' ' in tag_content or (tag_content and '' in tag_content.split(',')):
            msg = {'info': 'tag content is invalid', 'status': 500}
        elif not inven_db.get_collection(Tag, filters={'record_id': [record_id], 'record_type': [record_type]}):
            tag = Tag()
            tag.record_id = record_id
            tag.record_type = record_type
            tag.tag_content = tag_content
            inven_db.insert(tag)
            msg = {'info': 'Tag create success', 'status': 200}
        else:
            inven_db.update_model(Tag, filters={'record_id': [record_id], 'record_type': [record_type]},
                                  updates={Tag.tag_content: tag_content})
            msg = {'info': 'Tag update success', 'status': 200}
    return jsonify(msg)


@new_automation_model.route('/run_playbook', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='run_playbook', contents='run playbook')
def run_playbook():
    """
    {
        'playbook_name': 'xxx',
        'playbook_dir': '/xxx/yyy',
        'switches': ['sn_xxxx', 'sn_yyyy'],
        'switch_checkall': True/False
        'group_list': ['group_1', 'group_2'],
        'vars': {},
        'scheduled': {
            'type': "DIRECT/ONCE/SCHEDULED",
            'params': {}
        }
    }
    """
    data = request.get_json()
    playbook_name = data['playbookName']
    if switch_playbook_util.is_internal_playbook(playbook_name):
        return jsonify({'status': 500, 'info': 'Name {0} is internal playbook, cannot be run.'.format(playbook_name)})
    playbook_dir = os.path.realpath(os.path.join(constants.ANSIBLE_PLAYBOOK_DIR, data['playbookDir']))
    if not playbook_dir.startswith(constants.ANSIBLE_PLAYBOOK_DIR):
        msg = {'info': 'The playbook_dir is invalid', 'job_name': '', 'status': 500}
        return jsonify(msg)
    _vars = data['vars']
    switches = data.get('switches', [])
    switch_checkall = data.get('switch_checkall', False)
    group_checkall = data.get('group_checkall', False)
    device_checkall = data.get('device_checkall', False)
    if switch_checkall:
        filter_switches = inven_db.get_collection(Switch, filters={
            'status': [constants.SwitchStatus.PROVISIONING_SUCCESS, constants.SwitchStatus.IMPORTED]})
        switches = [sw.sn for sw in filter_switches]

    groups = data.get('groupList', [])
    if not groups and group_checkall:
        group_objs = inven_db.get_collection(AssociationGroup)
        groups = [group.group_name for group in group_objs]

    other_device = data.get("other_device", [])
    if not other_device and device_checkall:
        ansible_devices = inven_db.get_collection(AnsibleDevice)
        other_device = [
            {"device_ip": i.ip, "device_name": i.device_name, "device_user": i.device_user, "device_pwd": i.device_pwd, "device_ssh_key_path": i.device_ssh_key_path,
             "device_port": i.device_port}
            for i in ansible_devices]
    else:
        device_name_list = [i["device_name"] for i in other_device]
        ansible_devices = inven_db.get_collection(AnsibleDevice, filters={'device_name': device_name_list})
        other_device = [
            {"device_ip": i.ip, "device_name": i.device_name, "device_user": i.device_user, "device_pwd": i.device_pwd,
             "device_ssh_key_path": i.device_ssh_key_path, "device_port": i.device_port}
            for i in ansible_devices]

    schedule_info = data['scheduled']
    create_user = current_user.id
    job_name = '{0}:::{1}'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'), playbook_name)
    if other_device:
        job_name = f'[3rd-party-vendors]{job_name}'

    def error_hanlder(e):
        LOG.error(":::Callback, There is error with %s", e)

    if schedule_info['type'] == 'DIRECT':
        add_job_record(job_name=job_name, playbook_name=playbook_name, playbook_path=playbook_dir,
                       schedule_type='DIRECT', schedule_params={}, create_user=create_user)
        ansible_job_start.delay(playbook_name, playbook_dir, job_name, 'DIRECT', {}, switches, groups, _vars,
                                celery_task_name=job_name, other_device=other_device)
    elif schedule_info['type'] == 'ONCE':
        start_time = schedule_info['params']['start_time']
        start_time = datetime.strptime(start_time, DATE_FORMAT)
        end_time = schedule_info['params']['end_time']
        end_time = datetime.strptime(end_time, DATE_FORMAT)
        add_job_record(job_name=job_name, playbook_name=playbook_name, playbook_path=playbook_dir, schedule_type='ONCE',
                       schedule_params=schedule_info['params'], create_user=create_user)

        beat_task.add_job(job_name, "ansible_job_start", args=(
            playbook_name, playbook_dir, job_name, 'ONCE', schedule_info['params'], switches, groups, _vars), once=True,
                          start_time=start_time, expires=end_time, job_desc="automation ansible playbook once",
                          kwargs={"celery_type": "CRONTAB", "other_device": other_device})

    elif schedule_info['type'] == 'SCHEDULED':
        crontab_expression = schedule_info['params']['crontab_expression']
        add_job_record(job_name=job_name, playbook_name=playbook_name, playbook_path=playbook_dir,
                       schedule_type='SCHEDULED', schedule_params=schedule_info['params'], create_user=create_user)
        beat_task.add_job(job_name, "ansible_job_start", job_type="crontab",
                          job_schedule=crontab_expression,
                          args=(playbook_name, playbook_dir, job_name, 'SCHEDULED', {}, switches, groups, _vars),
                          start_time=datetime.now(timezone.utc).replace(tzinfo=None),
                          job_desc="automation ansible playbook scheduled",
                          kwargs={"celery_type": "CRONTAB", "other_device": other_device})
    msg = {'info': 'The playbook task execute success in background', 'job_name': job_name, 'status': 200}
    return jsonify(msg)
