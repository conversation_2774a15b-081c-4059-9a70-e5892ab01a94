from flask import Blueprint, request, jsonify
from server.db.models.wireless import WirelessRrmTaskLog, WirelessRrmTaskResult
from server.db.models.wireless_openwifi import Inventory
from server.db.pg_engine import get_pg_session  # PostgreSQL 会话
from server.db.session import get_session  # MySQL 会话
from sqlalchemy import desc

wireless_rrm_mold = Blueprint("wireless_rrm_mold", __name__)


@wireless_rrm_mold.route('/task/record', methods=['GET'])
def get_rrm_task_record():
    site_id = request.args.get('siteId')
    sort_by = request.args.get('sortBy', 'create_time')
    sort_type = request.args.get('sortType', 'desc')
    page_num = int(request.args.get('pageNum', 1))
    page_size = int(request.args.get('pageSize', 10))
    if not site_id:
        return jsonify({'status': 400, 'info': 'siteId required'}), 400
    with get_session() as session:
        query = session.query(WirelessRrmTaskLog).filter(WirelessRrmTaskLog.site_id == int(site_id))
        if hasattr(WirelessRrmTaskLog, sort_by):
            sort_col = getattr(WirelessRrmTaskLog, sort_by)
        else:
            sort_col = WirelessRrmTaskLog.create_time
        if sort_type == 'desc':
            query = query.order_by(desc(sort_col))
        else:
            query = query.order_by(sort_col)
        total = query.count()
        items = query.offset((page_num - 1) * page_size).limit(page_size).all()

        def to_dict(obj):
            return {
                'id': obj.id,
                'create_time': obj.create_time.strftime('%Y-%m-%d %H:%M:%S') if obj.create_time else '',
                'modified_time': obj.modified_time.strftime('%Y-%m-%d %H:%M:%S') if obj.modified_time else '',
                'site_id': obj.site_id,
                'trigger_time': obj.trigger_time.strftime('%Y-%m-%d %H:%M:%S') if obj.trigger_time else '',
                'is_schedule_task': obj.is_schedule_task,
                'online_num': obj.online_num,
                'success_num': obj.success_num,
                'failed_num': obj.failed_num
            }

        info = [to_dict(item) for item in items]
        return jsonify({'info': info, 'status': 200, 'pageNum': page_num, 'pageSize': page_size, 'total': total})


@wireless_rrm_mold.route('/task/result', methods=['GET'])
def get_rrm_task_result():
    site_id = request.args.get('siteId')
    task_id = request.args.get('taskId')
    sort_by = request.args.get('sortBy', 'create_time')
    sort_type = request.args.get('sortType', 'desc')
    page_num = int(request.args.get('pageNum', 1))
    page_size = int(request.args.get('pageSize', 10))
    if not site_id or not task_id:
        return jsonify({'status': 400, 'info': 'siteId and taskId required'}), 400
    # 查询 wireless_rrm_task_result 表 (MySQL)
    with get_session() as mysql_session:
        query = mysql_session.query(WirelessRrmTaskResult).filter(WirelessRrmTaskResult.task_id == task_id)
        if hasattr(WirelessRrmTaskResult, sort_by):
            sort_col = getattr(WirelessRrmTaskResult, sort_by)
        else:
            sort_col = WirelessRrmTaskResult.create_time
        if sort_type == 'desc':
            query = query.order_by(desc(sort_col))
        else:
            query = query.order_by(sort_col)
        total = query.count()
        items = query.offset((page_num - 1) * page_size).limit(page_size).all()

    # 查询 inventory 表获取设备信息 (PostgreSQL)
    sn_list = [item.sn for item in items]
    inventory_map = {}
    if sn_list:
        try:
            with get_pg_session() as pg_session:
                inventory_items = pg_session.query(
                    Inventory.serialnumber,
                    Inventory.name,
                    Inventory.devicetype
                ).filter(
                    Inventory.serialnumber.in_(sn_list)
                ).all()

                for inv in inventory_items:
                    inventory_map[inv.serialnumber] = {
                        'name': inv.name or '',
                        'devicetype': inv.devicetype or ''
                    }
        except Exception as e:
            print(f"Error querying inventory: {e}")
            import traceback
            traceback.print_exc()

    def to_dict(obj):
        inv = inventory_map.get(obj.sn, {})
        return {
            'task_id': obj.task_id,
            'sn': obj.sn,
            'create_time': obj.create_time.strftime('%Y-%m-%d %H:%M:%S') if obj.create_time else '',
            'modified_time': obj.modified_time.strftime('%Y-%m-%d %H:%M:%S') if obj.modified_time else '',
            'result_type': obj.result_type,
            'device_name': inv.get('name', ''),
            'device_mode': inv.get('devicetype', '')
        }

    info = [to_dict(item) for item in items]
    return jsonify({'info': info, 'status': 200, 'pageNum': page_num, 'pageSize': page_size, 'total': total})

# @wireless_rrm_mold.route('/channel', methods=['GET'])
# def get_wireless_device_channel():
#     sn = request.args.get('sn')
#     if not sn:
#         return jsonify({'status': 400, 'info': 'sn required'}), 400
#     with get_pg_session() as session:    
#         record = session.query(
#             WirelessDeviceChannel.sn,
#             WirelessDeviceChannel._2g_channel,
#             WirelessDeviceChannel._5g_channel,
#             WirelessDeviceChannel._6g_channel
#         ).filter(WirelessDeviceChannel.sn == sn).first()
#         if not record:
#             return jsonify({'status': 404, 'info': 'not found'}), 404
#         info = {
#             'sn': record.sn,
#             '2g_channel': record._2g_channel or '',
#             '5g_channel': record._5g_channel or '',
#             '6g_channel': record._6g_channel or ''
#         }
#         return jsonify({'info': info, 'status': 200})
