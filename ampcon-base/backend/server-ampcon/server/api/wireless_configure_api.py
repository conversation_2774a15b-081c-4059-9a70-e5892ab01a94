import json
import logging
import re
from datetime import datetime

from flask import Blueprint, request, jsonify
from server.api.upgrade_api import get_wireless_model_list
from server.db.models.wireless import WirelessConfigureSsid, WirelessEthernetPorts, WirelessProfileUsage, \
    WirelessProfile, WirelessChannel, WirelessDhcpService
from server.db.models.wireless_openwifi import Configurations, Inventory, Variables, Venues, Overrides
from server.db.pg_engine import get_pg_session
from server.util import utils, wireless_util
from sqlalchemy import or_, func

LOG = logging.getLogger(__name__)
configure_mold = Blueprint("configure_mold", __name__, template_folder='templates')


def get_profile_id(session, s):
    match = re.search(r'__variableBlock__<<(.+?)>>', s)
    if match:
        vid = match.group(1)
    else:
        vid = s
    profile = session.query(WirelessProfile).filter(WirelessProfile.variable_id == vid).first()
    if profile:
        return profile.id
    else:
        raise Exception(f'WirelessProfile can not find variable_id: {vid}')


def update_single_profile_usage(session, site_id, referee_id, s, is_variable=True):
    if s:
        if is_variable:
            profile_id = get_profile_id(session, s)
        else:
            profile_id = s
        wpu = WirelessProfileUsage()
        wpu.site_id = site_id
        wpu.referee_id = str(referee_id)
        wpu.profile_id = profile_id
        session.add(wpu)


# 更新引用方法，kind: 1 新增，2 编辑，3 删除
def update_profile_usage(session, kind, site_id, referee_id, configure=None):
    has_profile = False
    if kind != 1:
        # 清空所有
        session.query(WirelessProfileUsage).filter(
            WirelessProfileUsage.referee_id == str(referee_id),
            WirelessProfileUsage.site_id == site_id,
        ).delete()
    if kind == 3:
        return has_profile
    if configure:
        if isinstance(configure, str):
            config_json = json.loads(configure)
        else:
            config_json = configure
        if 'time-range-index' in config_json:
            update_single_profile_usage(session, site_id, referee_id, config_json['time-range-index'])
            has_profile = True
        if 'multi-psk' in config_json:
            update_single_profile_usage(session, site_id, referee_id, config_json['multi-psk'])
            has_profile = True
        if 'radius' in config_json:
            update_single_profile_usage(session, site_id, referee_id, config_json['radius'])
            has_profile = True
        if 'captive' in config_json and 'web-root' in config_json['captive']:
            update_single_profile_usage(session, site_id, referee_id, config_json['captive']['web-root'])
            has_profile = True
    return has_profile


@configure_mold.route('/ssid', methods=['POST'])
def create_configure():
    msg = {'status': 200, 'info': 'Create configure success.'}
    try:
        data = request.get_json(force=True)
        name = data.get('name')
        site_id = data.get('site_id')
        ssid_configure = data.get('ssid_configure')
        labels_name = data.get('labels_name')
        vlan_or_dhcp_name = data.get('vlan_or_dhcp_name')
        network_type = data.get('network_type')
        if not name:
            return jsonify({'status': 400, 'info': 'Name can not be empty.'})
        if site_id is None:
            return jsonify({'status': 400, 'info': 'siteId can not be empty.'})
        with get_pg_session() as session:
            with session.begin():
                configure = session.query(WirelessConfigureSsid).filter(
                    WirelessConfigureSsid.name == name,
                    WirelessConfigureSsid.site_id == site_id
                ).first()
                if configure:
                    return jsonify({'status': 400, 'info': 'The name already exists.'})
                configure = WirelessConfigureSsid()
                configure.name = name
                configure.site_id = site_id
                configure.security = data.get('security')
                configure.radio = data.get('radio')
                configure.group_name = 'all'
                configure.is_enable = 1
                configure.ssid_configure = ssid_configure
                configure.labels_name = labels_name
                configure.vlan_or_dhcp_name = vlan_or_dhcp_name
                configure.network_type = network_type
                session.add(configure)
                session.flush()
                update_profile_usage(session, 1, site_id, configure.id, ssid_configure)
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    return jsonify(msg)


@configure_mold.route('/ssid', methods=['PUT'])
def modify_configure():
    msg = {'status': 200, 'info': 'Modify configure success.'}
    try:
        data = request.get_json(force=True)
        name = data.get('name')
        is_enable = data.get('is_enable')
        labels_name = data.get('labels_name')
        ssid_configure = data.get('ssid_configure')
        vlan_or_dhcp_name = data.get('vlan_or_dhcp_name')
        network_type = data.get('network_type')
        with get_pg_session() as session:
            with session.begin():
                config = session.query(WirelessConfigureSsid).filter(WirelessConfigureSsid.id == data.get('id')).first()
                if not config:
                    return jsonify({'status': 404, 'info': 'Configure not found.'})
                # 开关
                if is_enable is not None:
                    config.is_enable = int(is_enable)
                # # 编辑组
                # elif labels_name is not None:
                #     config.labels_name = labels_name
                else:
                    if config.name != name:
                        # 编辑同名校验
                        same_name_config = session.query(WirelessConfigureSsid).filter(
                            WirelessConfigureSsid.name == name,
                            WirelessConfigureSsid.site_id == config.site_id,
                            WirelessConfigureSsid.id != config.id
                        ).first()
                        if same_name_config:
                            return jsonify({'status': 400, 'info': 'The name already exists.'})
                        config.name = name
                    config.security = data.get('security')
                    config.radio = data.get('radio')
                    config.ssid_configure = ssid_configure
                    config.vlan_or_dhcp_name = vlan_or_dhcp_name
                    config.labels_name = labels_name
                    config.network_type = network_type
                    update_profile_usage(session, 2, config.site_id, config.id, ssid_configure)
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    return jsonify(msg)


@configure_mold.route('/ssid', methods=['DELETE'])
def delete_configure():
    msg = {'status': 200, 'info': 'Delete configure success.'}
    try:
        data = request.get_json(force=True)
        with get_pg_session() as session:
            with session.begin():
                config = session.query(WirelessConfigureSsid).filter(WirelessConfigureSsid.id == data.get('id')).first()
                if not config:
                    return jsonify({'status': 404, 'info': 'Configure not found.'})
                update_profile_usage(session, 3, config.site_id, config.id)
                session.delete(config)
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    return jsonify(msg)


@configure_mold.route('/ssid/list', methods=['POST'])
def list_configures():
    try:
        data = request.get_json(force=True)
        site_id = data.get('site_id')
        with get_pg_session() as session:
            pre_query = session.query(WirelessConfigureSsid)
            if site_id is not None:
                pre_query = pre_query.filter(WirelessConfigureSsid.site_id == site_id)
            page_num, page_size, total_count, query_obj = utils.query_helper(WirelessConfigureSsid, pre_query=pre_query)
            response = {
                "info": [obj.make_dict() for obj in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
    except Exception as e:
        response = {"status": 500, "info": str(e)}
    return jsonify(response)


@configure_mold.route('/ssid', methods=['GET'])
def get_configure_detail():
    try:
        config_id = request.args.get('id')
        with get_pg_session() as session:
            config = session.query(WirelessConfigureSsid).filter(WirelessConfigureSsid.id == config_id).first()
            if not config:
                return jsonify({'status': 404, 'info': 'Configure not found.'})
            result = config.make_dict()
            return jsonify({'status': 200, 'info': result})
    except Exception as e:
        return jsonify({'status': 500, 'info': str(e)})


@configure_mold.route('/general', methods=['PUT'])
def update_configure_general():
    try:
        data = request.get_json(force=True)
        cid = data.get('id')
        site_id = data.get('site_id')
        config = data.get('config')
        if not cid:
            return jsonify({'status': 400, 'info': 'ID can not be empty.'})
        with get_pg_session() as session:
            with session.begin():
                c = session.query(Configurations).filter(Configurations.id == cid).first()
                if not c:
                    return jsonify({'status': 404, 'info': 'Configure not found.'})
                c.configuration = config
                # 处理引用关系
                config_json = json.loads(config)
                for cj in config_json:
                    content = json.loads(cj['configuration'])
                    if 'radios' in content:
                        for r in content['radios']:
                            has_profile = update_profile_usage(session, 2, site_id, cid, r)
                            if has_profile:
                                break
                return jsonify({'status': 200, 'info': 'Modify general config success.'})
    except Exception as e:
        return jsonify({'status': 500, 'info': str(e)})


@configure_mold.route('/channel', methods=['GET'])
def get_wireless_channel():
    country_code = request.args.get('countryCode', type=str)
    if not country_code:
        return jsonify({'status': 400, 'info': 'countryCode is required.'})
    try:
        with get_pg_session() as session:
            result = session.query(
                WirelessChannel.country_code,
                WirelessChannel._2g_channel,
                WirelessChannel._5g_channel,
                WirelessChannel._6g_channel,
            ).filter(
                WirelessChannel.country_code == country_code.upper()
            ).first()
            if not result:
                return jsonify({'status': 404, 'info': f"No channel found for country {country_code}."})
            return jsonify({
                "country_code": result.country_code,
                "2g_channel": result._2g_channel,
                "5g_channel": result._5g_channel,
                "6g_channel": result._6g_channel
            })

    except Exception as e:
        LOG.error(f"Failed to get channel config: {str(e)}")
        return jsonify({'status': 500, 'info': str(e)})


# DHCP服务接口 
# 新增DHCP服务接口
@configure_mold.route('/dhcp_service', methods=['POST'])
def create_dhcp_service():
    msg = {'status': 200, 'info': 'Add dhcp service success.'}
    try:
        data = request.get_json(force=True)
        name = data.get('name')
        site_id = data.get('site_id')
        subnet = data.get('subnet')
        vlan = data.get('vlan', '-')
        dhcp_configure = data.get('dhcp_configure')
        description = data.get('description')

        if not name:
            return jsonify({'status': 400, 'info': 'Name can not be empty.'})
        if site_id is None:
            return jsonify({'status': 400, 'info': 'site_id can not be empty.'})
        if not subnet:
            return jsonify({'status': 400, 'info': 'subnet can not be empty.'})
        if not dhcp_configure:
            return jsonify({'status': 400, 'info': 'dhcp_configure can not be empty.'})

        with get_pg_session() as session:
            with session.begin():
                # 检查名称是否已存在
                existing = session.query(WirelessDhcpService).filter(
                    WirelessDhcpService.name == name,
                    WirelessDhcpService.site_id == site_id
                ).first()
                if existing:
                    return jsonify({'status': 400, 'info': 'The name already exists.'})

                # 创建新的DHCP服务
                dhcp_service = WirelessDhcpService()
                dhcp_service.name = name
                dhcp_service.site_id = site_id
                dhcp_service.subnet = subnet
                dhcp_service.vlan = vlan
                dhcp_service.dhcp_configure = dhcp_configure if isinstance(dhcp_configure, dict) else json.loads(
                    dhcp_configure)
                dhcp_service.description = description
                session.add(dhcp_service)
                session.flush()

    except Exception as e:
        LOG.error(f"Failed to create dhcp service: {str(e)}")
        msg = {'status': 500, 'info': str(e)}
    return jsonify(msg)


# 分页查询DHCP服务列表接口
@configure_mold.route('/dhcp_service_list', methods=['GET'])
def list_dhcp_services():
    try:
        site_id = request.args.get('siteId', type=int)
        sort_by = request.args.get('sortBy')
        sort_type = request.args.get('sortType', 'desc')
        page_num = request.args.get('pageNum', 1, type=int)
        page_size = request.args.get('pageSize', 10, type=int)

        if site_id is None:
            return jsonify({'status': 400, 'info': 'siteId is required.'})

        with get_pg_session() as session:
            query = session.query(WirelessDhcpService).filter(
                WirelessDhcpService.site_id == site_id
            )

            # 排序
            if sort_by:
                if hasattr(WirelessDhcpService, sort_by):
                    sort_column = getattr(WirelessDhcpService, sort_by)
                    if sort_type.lower() == 'asc':
                        query = query.order_by(sort_column.asc())
                    else:
                        query = query.order_by(sort_column.desc())

            # 分页
            total = query.count()
            offset = (page_num - 1) * page_size
            results = query.offset(offset).limit(page_size).all()

            # 构造返回数据
            info = []
            for service in results:
                info.append({
                    'id': service.id,
                    'site_id': service.site_id,
                    'name': service.name,
                    'subnet': service.subnet,
                    'vlan': service.vlan,
                    'dhcp_configure': service.dhcp_configure,
                    'description': service.description,
                    'modified_time': service.modified_time
                })

            response = {
                'info': info,
                'status': 200,
                'pageNum': page_num,
                'pageSize': page_size,
                'total': total
            }

    except Exception as e:
        LOG.error(f"Failed to list dhcp services: {str(e)}")
        response = {'status': 500, 'info': str(e)}
    return jsonify(response)


# 模糊查询所有DHCP服务名称
@configure_mold.route('/dhcp_service_filter', methods=['GET'])
def filter_dhcp_services():
    try:
        site_id = request.args.get('siteId', type=int)
        key = request.args.get('key', '')

        if site_id is None:
            return jsonify({'status': 400, 'info': 'siteId is required.'})

        with get_pg_session() as session:
            query = session.query(WirelessDhcpService.name).filter(
                WirelessDhcpService.site_id == site_id
            )

            # 模糊查询
            if key:
                query = query.filter(WirelessDhcpService.name.ilike(f'%{key}%'))

            results = query.all()
            names = [result.name for result in results]

            response = {
                'info': names,
                'status': 200
            }

    except Exception as e:
        LOG.error(f"Failed to filter dhcp services: {str(e)}")
        response = {'status': 500, 'info': str(e)}
    return jsonify(response)


# 查询DHCP服务详情接口
@configure_mold.route('/dhcp_service', methods=['GET'])
def get_dhcp_service_detail():
    try:
        service_id = request.args.get('id', type=int)

        if service_id is None:
            return jsonify({'status': 400, 'info': 'id is required.'})

        with get_pg_session() as session:
            service = session.query(WirelessDhcpService).filter(
                WirelessDhcpService.id == service_id
            ).first()

            if not service:
                return jsonify({'status': 404, 'info': 'DHCP service not found.'})

            info = {
                'id': service.id,
                'site_id': service.site_id,
                'name': service.name,
                'subnet': service.subnet,
                'vlan': service.vlan,
                'dhcp_configure': service.dhcp_configure,
                'description': service.description
            }

            response = {
                'info': info,
                'status': 200
            }

    except Exception as e:
        LOG.error(f"Failed to get dhcp service detail: {str(e)}")
        response = {'status': 500, 'info': str(e)}
    return jsonify(response)


# 更新DHCP服务接口
@configure_mold.route('/dhcp_service', methods=['PUT'])
def update_dhcp_service():
    msg = {'status': 200, 'info': 'Modify dhcp service success.'}
    try:
        data = request.get_json(force=True)
        service_id = data.get('id')
        subnet = data.get('subnet')
        vlan = data.get('vlan')
        dhcp_configure = data.get('dhcp_configure')
        description = data.get('description')

        if service_id is None:
            return jsonify({'status': 400, 'info': 'id can not be empty.'})

        with get_pg_session() as session:
            with session.begin():
                service = session.query(WirelessDhcpService).filter(
                    WirelessDhcpService.id == service_id
                ).first()

                if not service:
                    return jsonify({'status': 404, 'info': 'DHCP service not found.'})

                # 更新字段
                if subnet is not None:
                    service.subnet = subnet
                if vlan is not None:
                    service.vlan = vlan
                if dhcp_configure is not None:
                    service.dhcp_configure = dhcp_configure if isinstance(dhcp_configure, dict) else json.loads(
                        dhcp_configure)
                if description is not None:
                    service.description = description

    except Exception as e:
        LOG.error(f"Failed to update dhcp service: {str(e)}")
        msg = {'status': 500, 'info': str(e)}
    return jsonify(msg)


#   删除DHCP服务接口
#  删除dhcp-service时，要根据其名字xx，去wireless_configure_ssid、wireless_ethernet_ports  这两张表中查network_type=3 and vlan_or_dhcp_name= 'xx' 是否存在，存在则不能删除此dhcp-serivce
@configure_mold.route('/dhcp_service', methods=['DELETE'])
def delete_dhcp_service():
    msg = {'status': 200, 'info': 'Delete dhcp service success.'}
    try:
        data = request.get_json(force=True)
        service_id = data.get('id')

        with get_pg_session() as session:
            with session.begin():

                # 根据id查询DHCP的name
                name = session.query(WirelessDhcpService.name).filter(
                    WirelessDhcpService.id == service_id
                ).scalar()

                # 在wireless_configure_ssid表中查询是否有network_type=3并且vlan_or_dhcp_name= name
                if session.query(WirelessConfigureSsid).filter(
                        WirelessConfigureSsid.network_type == 3,
                        WirelessConfigureSsid.vlan_or_dhcp_name == name
                ).first():
                    return jsonify({'status': 400, 'info': 'This DHCP service is used by a wireless configure ssid.'})

                # 在wireless_ethernet_ports表中查询是否有network_type=3并且vlan_or_dhcp_name= name
                if session.query(WirelessEthernetPorts).filter(
                        WirelessEthernetPorts.network_type == 3,
                        WirelessEthernetPorts.vlan_or_dhcp_name == name
                ).first():
                    return jsonify({'status': 400, 'info': 'This DHCP service is used by a wireless ethernet ports.'})

                if service_id is None:
                    return jsonify({'status': 400, 'info': 'id can not be empty.'})
                service = session.query(WirelessDhcpService).filter(
                    WirelessDhcpService.id == service_id
                ).first()

                if not service:
                    return jsonify({'status': 404, 'info': 'DHCP service not found.'})

                session.delete(service)

    except Exception as e:
        LOG.error(f"Failed to delete dhcp service: {str(e)}")
        msg = {'status': 500, 'info': str(e)}
    return jsonify(msg)


@configure_mold.route('/ethernet_ports', methods=['POST'])
def add_ethernet_port():
    msg = {'status': 200, 'info': 'Add ethernet port success.'}
    try:
        data = request.get_json(force=True)
        site_id = data.get('site_id')
        port_name = data.get('port')
        network_type = data.get('network_type')
        vlan_or_dhcp_name = data.get('vlan_or_dhcp_name', "")

        if site_id is None:
            return jsonify({'status': 400, 'info': 'site_id can not be empty.'})
        if not port_name:
            return jsonify({'status': 400, 'info': 'port can not be empty.'})
        if not network_type:
            return jsonify({'status': 400, 'info': 'network_type can not be empty.'})
        if vlan_or_dhcp_name is None:
            vlan_or_dhcp_name = ""

        with get_pg_session() as session:
            with session.begin():
                new_port = WirelessEthernetPorts()
                new_port.site_id = site_id
                new_port.port = port_name
                new_port.mac = data.get('mac')
                new_port.network_type = network_type
                new_port.vlan_or_dhcp_name = vlan_or_dhcp_name
                new_port.vlan_tag = data.get('vlan_tag', 1)
                new_port.create_time = datetime.now()
                new_port.modified_time = datetime.now()

                session.add(new_port)
                # session.commit()

    except Exception as e:
        msg = {'status': 500, 'info': str(e)}

    return jsonify(msg)


@configure_mold.route('/ethernet_ports', methods=['GET'])
def get_ethernet_ports():
    msg = {'status': 200, 'info': []}
    try:
        site_id = request.args.get('siteId', type=int)
        page_num = request.args.get('pageNum', 1, type=int)
        page_size = request.args.get('pageSize', 10, type=int)
        sort_by = request.args.get('sortBy', 'id')
        sort_type = request.args.get('sortType', 'desc')

        if site_id is None:
            return jsonify({'status': 400, 'info': 'site_id can not be empty.'})
        if page_num < 1 or page_size < 1:
            return jsonify({'status': 400, 'info': 'pageNum and pageSize must be positive.'})

        with get_pg_session() as session:
            query = session.query(WirelessEthernetPorts).filter(
                WirelessEthernetPorts.site_id == site_id
            )

            if sort_type.lower() == 'asc':
                query = query.order_by(getattr(WirelessEthernetPorts, sort_by).asc())
            else:
                query = query.order_by(getattr(WirelessEthernetPorts, sort_by).desc())

            ports = query.limit(page_size).offset((page_num - 1) * page_size).all()
            total = query.count()

            port_list = [{
                'id': port.id,
                'create_time': port.create_time.isoformat() if port.create_time else None,
                'modified_time': port.modified_time.isoformat() if port.modified_time else None,
                'site_id': port.site_id,
                'port': port.port,
                'mac': port.mac,
                'network_type': port.network_type,
                'vlan_or_dhcp_name': port.vlan_or_dhcp_name,
                'vlan_tag': port.vlan_tag
            } for port in ports]

            msg['info'] = port_list
            msg['pageNum'] = page_num
            msg['pageSize'] = page_size
            msg['total'] = total

    except Exception as e:
        msg = {'status': 500, 'info': str(e)}

    return jsonify(msg)


@configure_mold.route('/ethernet_ports', methods=['DELETE'])
def delete_ethernet_port():
    msg = {'status': 200, 'info': 'Delete ethernet port success.'}
    try:
        data = request.get_json(force=True)
        port_id = data.get('id')

        if not port_id:
            return jsonify({'status': 400, 'info': 'id is required'})

        with get_pg_session() as session:
            with session.begin():
                port = session.query(WirelessEthernetPorts).get(port_id)
                if not port:
                    return jsonify({'status': 404, 'info': 'Ethernet port not found'})

                session.delete(port)

    except Exception as e:
        msg = {'status': 500, 'info': str(e)}

    return jsonify(msg)


def is_wifi7_device(deviceType):
    # 如果设备款型形如：FS_AP-N716 数字部分以7开头的是wifi7设备
    return len(deviceType) > 7 and deviceType[7].isdigit() and deviceType[7] == '7'


def get_band_list(deviceType):
    allModels = get_wireless_model_list()
    return next((model['bands'] for model in allModels if model['model_name'] == deviceType), [])


@configure_mold.route('/query', methods=['GET'])
def get_inventory_configure():
    try:
        sn = request.args.get('sn')
        if not sn:
            return jsonify({'status': 400, 'info': 'Missing serial number.'})

        with get_pg_session() as session:
            inventory = session.query(Inventory).filter(Inventory.serialnumber == sn).first()
            if not inventory:
                return jsonify({'status': 404, 'info': 'Inventory not found.'})

            siteId = inventory.venue
            labelsName = inventory.labelsname
            isWifi7Device = is_wifi7_device(inventory.devicetype)
            bandList = get_band_list(inventory.devicetype)

            if not siteId:
                return jsonify({'status': 404, 'info': 'Inventory not add to site.'})

            site = session.query(Venues).filter(Venues.id == siteId).first()
            if not labelsName:
                ssidConfigures = session.query(WirelessConfigureSsid).filter(
                    WirelessConfigureSsid.site_id == siteId, WirelessConfigureSsid.is_enable == 1,
                    WirelessConfigureSsid.labels_name == '[]'
                ).order_by(WirelessConfigureSsid.id.asc()).all()
            else:
                label_list = [label.strip() for label in labelsName.split(",") if label.strip()]
                if not label_list:
                    return jsonify({'status': 400, 'info': 'Invalid labelsName.'})
                ssidConfigures = session.query(WirelessConfigureSsid).filter(
                    WirelessConfigureSsid.site_id == siteId, WirelessConfigureSsid.is_enable == 1,
                    or_(func.jsonb_exists_any(WirelessConfigureSsid.labels_name, label_list),
                        WirelessConfigureSsid.labels_name == '[]')
                ).order_by(WirelessConfigureSsid.id.asc()).all()

            ssidConfiguresList = []
            interfaceList = []
            interfaceNameList = []

            # 默认的ethernet配置
            defaultEthernet = [{
                "vlan-tag": "un-tagged",
                "isolate": False,
                "select-ports": ["WAN*"]
            }]

            upServices = []
            downServices = []

            # 默认up网络配置信息
            defaultUpNetwork = {
                "ssids": [],
                "ethernet": defaultEthernet,
                "role": "upstream",
                "name": "default"
            }

            # 组装general配置信息
            resultJson = {}
            configurationsList = session.query(Configurations).filter(
                Configurations.venue == siteId
            ).all()

            for configuration in configurationsList:
                try:
                    configureJson = json.loads(configuration.configuration)
                except json.JSONDecodeError:
                    continue
                for innerConfigureJson in configureJson:
                    try:
                        innerJson = json.loads(innerConfigureJson["configuration"])
                    except json.JSONDecodeError:
                        continue
                    first_key = next(iter(innerJson))
                    if first_key == "IP address":
                        defaultUpNetwork.update(innerJson[first_key])
                        continue
                    # services配置处理
                    if first_key == "services":
                        for serviceName, service in innerJson[first_key].items():
                            if serviceName in ["http", "lldp", "ssh", "ntp"]:
                                upServices.append(serviceName)
                            elif serviceName in ["mdns", "dhcp-snooping", "igmp"]:
                                upServices.append(serviceName)
                                downServices.append(serviceName)
                            else:
                                LOG.error("Invalid service name: %s" % serviceName)

                    resultJson[first_key] = innerJson[first_key]
            if "radios" in resultJson and bandList:
                radios = resultJson["radios"]
                for i in range(len(radios) - 1, -1, -1):
                    radio = radios[i]
                    band = radio.get('band', 'unknown')
                    if band in bandList:
                        # 填充channel-mode字段，wifi7款型设备配置EHT  其他款型是wifi6款型设备配置HE
                        radio["channel-mode"] = "EHT" if isWifi7Device else "HE"
                    else:
                        LOG.info(
                            f"SN:{inventory.serialnumber} not support radio band:{band}")
                        del radios[i]

            defaultUpNetwork["services"] = upServices
            defaultUpNetworkVlanId = defaultUpNetwork.get("vlan", {}).get("id")
            if defaultUpNetworkVlanId:
                defaultEthernet[0]["vlan-tag"] = "tagged"

            # 需要处理System-unit 下的hostname配置，如果hostname_enable为true则将设备名称赋值给hostname，最后去除hostname_enable节点
            if "unit" not in resultJson or "hostname_enable" not in resultJson["unit"]:
                return jsonify({'status': 500, 'info': 'Invalid JSON in unit_configure.'})
            if resultJson["unit"].get("hostname_enable"):
                resultJson["unit"].update({"hostname": inventory.name})
            if "timezone" in resultJson["unit"]:
                # 如果值包含#，则将#后面的内容去除
                unit = resultJson["unit"]
                unit.update({"timezone": resultJson["unit"].get("timezone").split("#")[0]})
                del unit["hostname_enable"]
            # 添加上name和location节点，分别对应设备和站点的名称
            resultJson["unit"].update({
                "name": inventory.name,
                "location": site.name
            })
            # 处理override配置
            handle_override_config(sn, resultJson, session)

            # 一定存在default的upstream网络
            interfaceNameList.append("default")
            interfaceList.append(defaultUpNetwork)

            # 遍历dhcp-service配置组装downstream网络
            dhcpServiceList = session.query(WirelessDhcpService).filter(
                WirelessDhcpService.site_id == siteId
            ).all()
            for dhcpService in dhcpServiceList:
                interfaceNameList.append(dhcpService.name)
                dhcpDownNetwork = dhcpService.dhcp_configure
                dhcpDownNetwork["services"] = downServices
                dhcpDownNetwork["ethernet"] = []
                interfaceList.append(dhcpDownNetwork)

            # 遍历物理口配置组装网络，填充网络的物理口信息
            wirelessEthernetPortsList = session.query(WirelessEthernetPorts).filter(
                WirelessEthernetPorts.site_id == siteId
            ).all()
            wirelessEthernetPortsMap = {}
            for port in wirelessEthernetPortsList:
                # 通过network_type和vlan_or_dhcp_name 联合分组
                vlanTag = {2: "tagged", 3: "un-tagged"}.get(port.vlan_tag, "auto")
                ethernetPort = {
                    "vlan-tag": vlanTag,
                    "select-ports": json.loads(port.port)
                }
                if port.mac:
                    ethernetPort.update({"macaddr": port.mac})
                wirelessEthernetPortsMap.setdefault(str(port.network_type) + "&&&" + port.vlan_or_dhcp_name, []).append(
                    ethernetPort
                )

            for key, value in wirelessEthernetPortsMap.items():
                keyList = key.split("&&&")
                if len(keyList) != 2 and keyList[0] not in ["1", "2", "3"]:
                    LOG.error("Invalid wirelessEthernetPortsMap key: %s" % key)
                    continue
                networkType = int(keyList[0])
                vlanOrDhcpName = keyList[1]
                if networkType == 1:
                    # 将物理口的信息填充到default网络中
                    defaultEthernet.extend(value)
                if networkType == 2:
                    # 如果vlan id和advance配置的ip中的vlan id一致，则将物理口信息填充到default网络中
                    if defaultUpNetworkVlanId and str(defaultUpNetworkVlanId) == vlanOrDhcpName:
                        defaultEthernet.extend(value)
                    else:
                        # 如果vlan id不等于default设置的，则新增upstream网络
                        vlanName = "vlan" + vlanOrDhcpName
                        interfaceNameList.append(vlanName)
                        vlanUpNetwork = {
                            "ssids": [],
                            "ethernet": [{
                                "vlan-tag": "tagged",
                                "isolate": False,
                                "select-ports": ["WAN*"]
                            }],
                            "services": upServices,
                            "role": "upstream",
                            "vlan": {"id": int(vlanOrDhcpName)},
                            "name": vlanName
                        }
                        vlanUpNetwork["ethernet"].extend(value)
                        interfaceList.append(vlanUpNetwork)
                if networkType == 3:
                    # 将物理口信息填充到downstream网络中
                    for downstream in interfaceList:
                        if downstream.get("role") == "downstream" and downstream.get("name") == vlanOrDhcpName:
                            downstream.setdefault("ethernet", []).extend(value)

            ssidMap = {}
            if ssidConfigures:
                # 存在ssid配置
                for ssid in ssidConfigures:
                    try:
                        ssidConfigure = json.loads(ssid.ssid_configure)
                    except json.JSONDecodeError:
                        return jsonify({'status': 500, 'info': 'Invalid JSON in ssid_configure.'})

                    networkType = ssid.network_type
                    vlanOrDhcpName = ssid.vlan_or_dhcp_name

                    ssidConfiguresList.append({
                        "id": ssid.id,
                        "name": ssid.name,
                        "networkType": networkType,
                        "vlanOrDhcpName": vlanOrDhcpName,
                        "json": ssidConfigure,
                    })

                    # 生成interface列表
                    if networkType == 1:
                        # 使用默认的up网络配置，
                        continue
                    elif networkType == 2:
                        # 使用vlan的up网络配置
                        vlanName = "vlan" + vlanOrDhcpName
                        if vlanName in interfaceNameList:
                            continue
                        # 如果vlan id和advance配置的ip中的vlan id一致，也是使用default网络
                        if defaultUpNetworkVlanId and str(defaultUpNetworkVlanId) == vlanOrDhcpName:
                            continue
                        else:
                            interfaceNameList.append(vlanName)
                            vlanUpNetwork = {
                                "ssids": [],
                                "ethernet": [{
                                    "vlan-tag": "tagged",
                                    "isolate": False,
                                    "select-ports": ["WAN*"]
                                }],
                                "services": upServices,
                                "role": "upstream",
                                "vlan": {"id": int(vlanOrDhcpName)},
                                "name": vlanName
                            }
                            interfaceList.append(vlanUpNetwork)
                    elif networkType == 3:
                        # 使用dhcp的down网络配置
                        if vlanOrDhcpName in interfaceNameList:
                            continue
                        LOG.error("dhcp-service downstream assembly issues")
                    else:
                        return jsonify({'status': 400, 'info': 'Invalid network_type.'})

                ssidList = handle_webroot_config(ssidConfiguresList, session)

                # 归类interface下的ssid
                for ssid in ssidList:
                    if ssid["networkType"] == 1:
                        interfaceName = "default"
                    elif ssid["networkType"] == 2:
                        if defaultUpNetworkVlanId and str(defaultUpNetworkVlanId) == ssid["vlanOrDhcpName"]:
                            interfaceName = "default"
                        else:
                            interfaceName = "vlan" + ssid["vlanOrDhcpName"]
                    else:
                        interfaceName = ssid["vlanOrDhcpName"]
                    ssidMap.setdefault(interfaceName, []).append(ssid)

            # 组装interface下的ssids
            for interface in interfaceList:
                interfaceName = interface["name"]
                interface["ssids"] = ssidMap.get(interfaceName, [])

            resultJson["interfaces"] = interfaceList
            # 添加ssids下的Time range节点
            timeRangeIndexMap = {}
            for interface in interfaceList:
                for ssid in interface.get("ssids", []):
                    ssid.update(ssid.get("json", {}))
                    del ssid["json"]
                    del ssid["id"]
                    del ssid["networkType"]
                    del ssid["vlanOrDhcpName"]
                    handle_timer_range(ssid, timeRangeIndexMap, session)

            # 添加radios下的Time range节点
            handle_timer_range(resultJson["radios"][0], timeRangeIndexMap, session)

            # 添加services下的Time range节点
            if timeRangeIndexMap:
                timeRange = []
                for timeRangeIndex, timeRangeIndexValue in timeRangeIndexMap.items():
                    timeRange.append({
                        "time-range-name": timeRangeIndexValue["name"],
                        "time-range-index": timeRangeIndexValue["index"],
                        "rules": json.loads(timeRangeIndexValue["value"])
                    })
                resultJson.setdefault("services", {}).update({"time-range": timeRange})

            # 替换配置中timeRangeIndex内容
            resultJsonStr = json.dumps(resultJson)
            if timeRangeIndexMap:
                for timeRangeIndex, timeRangeIndexValue in timeRangeIndexMap.items():
                    resultJsonStr = resultJsonStr.replace(f'"{timeRangeIndex}"', str(timeRangeIndexValue["index"]))

            # 替换profile内容
            profileIdList = extract_all_variables(resultJsonStr)
            for profileId in profileIdList:
                variables_obj = session.query(Variables).filter(Variables.id == profileId).first()
                profile = variables_obj.variables if variables_obj else None
                if profile:
                    resultJsonStr = resultJsonStr.replace(f'"__variableBlock__<<{profileId}>>"', profile)

            return jsonify({'status': 200, 'info': resultJsonStr})
    except Exception as e:
        return jsonify({'status': 500, 'info': str(e)})


def handle_override_config(sn, resultJson, session):
    # 获取配置覆盖设置数据
    overridesData = session.query(Overrides).filter(Overrides.serialnumber == sn).first()
    if overridesData:
        overridesList = json.loads(overridesData.overrides)
    else:
        return
    # 如果有设备的配置覆盖设置，则需要进行配置覆盖
    if overridesList:
        for override in overridesList:
            parameterName = override["parameterName"]
            parameterValue = override["parameterValue"]
            # parameterName通过字符串.分割后取第二个值
            parameterNameList = parameterName.split(".")
            if len(parameterNameList) > 2:
                keyOuter = parameterNameList[0]
                bandName = parameterNameList[1]
                keyInner = parameterNameList[2]
                if keyOuter not in resultJson:
                    continue
                # 目前只支持radio的配置覆盖
                for radio in resultJson[keyOuter]:
                    if bandName == radio["band"]:
                        radio[keyInner] = parameterValue
            else:
                LOG.error(f"Invalid parameterName:{parameterName}")


def handle_timer_range(originalMap, timeRangeIndexMap, session):
    if isinstance(originalMap, dict):
        timeRangeIndex = originalMap.get("time-range-index")
        if timeRangeIndex and timeRangeIndex not in timeRangeIndexMap:
            try:
                variableId = extract_all_variables(timeRangeIndex)[0]
                profile_obj = session.query(WirelessProfile).filter(WirelessProfile.variable_id == variableId).first()
                id = profile_obj.id if profile_obj else 1
                name = profile_obj.name if profile_obj else ""
                variables_obj = session.query(Variables).filter(Variables.id == variableId).first()
                value = variables_obj.variables if variables_obj else ""
                timeRangeIndexMap[timeRangeIndex] = {
                    "index": id,
                    "name": name,
                    "value": value
                }
            except Exception as e:
                logging.error(e)


def handle_webroot_config(ssidConfigures, session):
    unEnableCaptives = []
    clickCaptives = []
    credentialsCaptives = []
    radiusCaptives = []
    uamCaptives = []
    for ssid in ssidConfigures:
        # 根据captive和web-root配置，将ssid按顺序添加对应的列表中
        # \"captive\":{\"auth-mode\":\"off\"} 时，需要去掉captive节点
        if "captive" not in ssid["json"] or "auth-mode" not in ssid["json"]["captive"]:
            LOG.error(f"Captive auth-mode set error!")
            continue

        authMode = ssid["json"]["captive"]["auth-mode"]
        if authMode == 'off':
            unEnableCaptives.append(ssid)
        elif authMode == 'click-to-continue':
            clickCaptives.append(ssid)
        elif authMode == 'credentials':
            credentialsCaptives.append(ssid)
        elif authMode == 'radius':
            radiusCaptives.append(ssid)
        elif authMode == 'uam':
            uamCaptives.append(ssid)
        else:
            LOG.error(f"Captive auth-mode:{authMode} set error!")

    for ssid in unEnableCaptives:
        del ssid["json"]["captive"]

    remove_webroot_from_ssid(clickCaptives)
    remove_webroot_from_ssid(credentialsCaptives)
    remove_webroot_from_ssid(radiusCaptives)

    isNeedAppendHtml = len(unEnableCaptives) != len(ssidConfigures) - len(uamCaptives)
    result = []
    result.extend(unEnableCaptives)
    result.extend(uamCaptives)
    if clickCaptives and 'web-root' not in clickCaptives[-1]["json"]["captive"]:
        isNeedAppendHtml = False
        result.extend(clickCaptives)
        clickCaptives.clear()
    if credentialsCaptives and 'web-root' not in credentialsCaptives[-1]["json"]["captive"]:
        isNeedAppendHtml = False
        result.extend(credentialsCaptives)
        credentialsCaptives.clear()
    if radiusCaptives and 'web-root' not in radiusCaptives[-1]["json"]["captive"]:
        isNeedAppendHtml = False
        result.extend(radiusCaptives)
        radiusCaptives.clear()

    result.extend(clickCaptives)
    result.extend(credentialsCaptives)
    result.extend(radiusCaptives)

    # 遍历result
    for ssid in result:
        if ssid["json"].get("captive") is None or ssid["json"].get("captive").get('web-root') is None:
            continue
        webRoot = ssid["json"].get("captive").get('web-root')
        # 取profile进行填充
        profileId = extract_all_variables(webRoot)[0]
        variables_obj = session.query(Variables).filter(Variables.id == profileId).first()
        profile = variables_obj.variables if variables_obj else None
        ssid["json"]["captive"]["web-root"] = profile

    if isNeedAppendHtml:
        latestSsid = append_2html_to_ssid(result[-1])
        del result[-1]
        result.append(latestSsid)

    return result


def append_2html_to_ssid(ssidConfigure):
    webRoot = ssidConfigure["json"]["captive"]["web-root"]
    ssidConfigure["json"]["captive"]["web-root"] = wireless_util.tar_append_2html(webRoot)
    return ssidConfigure


def remove_webroot_from_ssid(ssidConfigures):
    # 如果ssidConfigures列表length n大于1则遍历0~n-2，去除web-root字段
    if len(ssidConfigures) > 1:
        for i in range(len(ssidConfigures) - 1):
            ssid = ssidConfigures[i]["json"]
            if 'captive' in ssid and 'web-root' in ssid['captive']:
                del ssid['captive']['web-root']


def extract_all_variables(text):
    pattern = r'__variableBlock__<<(.*?)>>'
    return re.findall(pattern, text)


def get_profile_by_id(profileId):
    with get_pg_session() as session:
        variables_obj = session.query(Variables).filter(Variables.id == profileId).first()
        return variables_obj.variables if variables_obj else None
