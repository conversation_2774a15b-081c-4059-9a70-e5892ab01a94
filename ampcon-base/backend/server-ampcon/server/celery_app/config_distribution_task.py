#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: cc_test
@file: snmp_trap_task.py
@function:
@time: 2024/11/25 18:15
"""
import json
import logging
import platform
import time
import traceback
from datetime import datetime, timedelta
import random
import string
import importlib

import flask_login
from flask import jsonify
import uuid
from sqlalchemy import text

from celery_app import my_celery_app
from celery_app.automation_task import AmpConBaseTask
from celery.exceptions import SoftTimeLimitExceeded
from server import constants
from server.db.models.dc_blueprint import DCFabricTopology
from server.config_distribution.config_deploy import C<PERSON><PERSON>onfigDeployer, NetConfConfigDeployer
from server.db.models.inventory import ConfigDistributionTaskForDC, ConfigDistributionTaskForCampus, inven_db
from server.db.models.inventory import Switch, SwitchConfigSnapshot
from server.util import utils
from server.util.redis_distributed_lock import DistributedLock
from server.db.models.campus_blueprint import CampusSiteNodes
if platform.system() != 'Windows':
    from server.collect.rma_collect import upload_rollback_config_paramiko, collect_backup_config_single
LOG = logging.getLogger(__name__)


def initialize_task(session, sn):
    """初始化任务并获取相关信息"""
    switch_obj = session.query(Switch).filter(Switch.sn == sn).first()
    if not switch_obj:
        raise ValueError(f"No switch found for SN: {sn}")
    switch_ip = switch_obj.mgt_ip
    switch_op_user = switch_obj.system_config.switch_op_user
    switch_op_password = switch_obj.system_config.switch_op_password
    return switch_obj, switch_ip, switch_op_user, switch_op_password


def switch_backup_config(ip, sn, current_time):
    try:
        if collect_backup_config_single(ip, sn, current_time) == constants.RMA_ACTIVE:
            inven_db.add_switch_log(sn, "Retrieve config success", level='info')
            return True
        else:
            inven_db.add_switch_log(sn, "Retrieve config failed", level='warn')
            return False
    except Exception as e:
        LOG.exception(f"Backup failed for SN {sn}: {e}")
        inven_db.add_switch_log(sn, "Retrieve config failed", level='warn')
        return False


def switch_rollback_config(sn, current_time):
    """执行回滚操作"""
    try:
        snapshot_entry = inven_db.get_model(SwitchConfigSnapshot, filters={'sn': [sn], 'snapshot_time': [current_time]})
        snapshot_content = snapshot_entry.archive_config.decode()
        switch_entry = inven_db.get_model(Switch, filters={'sn': [sn]})
        host_ip = switch_entry.mgt_ip
        user, pw = utils.get_switch_default_user(sn=sn)
        status, msg = upload_rollback_config_paramiko(host_ip, user, pw, snapshot_content, 240, 10)
        if status:
            LOG.info("Rollback success")
        else:
            raise Exception(str(msg))
    except Exception as rollback_error:
        LOG.error(f"Rollback failed: {rollback_error}")


def update_task_status(config_type, session, task_name, sn, task_status, task_traceback_info=None,
                       start_time=None):
    """更新任务状态"""
    meta_class = ConfigDistributionTaskForDC if config_type == 'dc' else ConfigDistributionTaskForCampus
    task_obj = session.query(meta_class).filter(meta_class.task_name == task_name,
                                                meta_class.sn == sn)
    task_obj.update(
        {"end_time": datetime.now(), "task_status": task_status, "task_traceback_info": task_traceback_info})
    if start_time:
        task_obj.update({"start_time": start_time})


@my_celery_app.task(name="config_distribution_task", base=AmpConBaseTask, soft_time_limit=10 * 60)
def handle_config_distribution(config_type, network_type, template_data, sn, meta_data, **kwargs):
    session = inven_db.get_session()
    task_time = kwargs.get('config_task_time', "")
    task_name = kwargs.get('config_task_name', "")
    task_role = kwargs.get('config_task_role', "")
    try:
        update_task_status(config_type, session, task_name, sn, 1, start_time=datetime.now())
        LOG.info("Set fabric network config data: %s" % template_data)
        switch_obj, switch_ip, switch_op_user, switch_op_password = initialize_task(session, sn)
        if not switch_backup_config(switch_obj.mgt_ip, sn, current_time=task_time):
            error_msg = "Backup configuration failed."
            LOG.error(error_msg)
            raise Exception(error_msg)
    except Exception as e:
        LOG.error(f"Error prepare for config distribution: {e}")
        update_task_status(config_type, session, task_name, sn, 3, task_traceback_info=f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: {str(e)}")
        return

    try:
        netconf_deployer = NetConfConfigDeployer(host=switch_ip, username=switch_op_user,
                                                    password=switch_op_password,
                                                    port=830, session=session)
        if config_type == "dc":
            if network_type in ['uplink_bm', 'uplink_cloud']:
                ret = netconf_deployer.deploy_netconf_config_for_dc_uplink(meta_data, **template_data)
            elif network_type in ['overlay_router', 'overlay_switch', 'overlay_link']:
                ret = netconf_deployer.deploy_netconf_config_for_dc_overlay(meta_data, **template_data)
            else:
                ret = netconf_deployer.deploy_netconf_config_for_dc(task_role, meta_data, **template_data)
        elif network_type == "mlag":
            ret = netconf_deployer.deploy_netconf_config_by_mlag(task_role, meta_data, **template_data)
        else:
            ret = netconf_deployer.deploy_netconf_config_by_clos(task_role, meta_data, **template_data)
        if ret["status"] != 200:
            raise ValueError(f"Error: {ret['msg']}")
    except SoftTimeLimitExceeded:
        update_task_status(config_type, session, task_name, sn, 3, task_traceback_info=f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: Soft time limit exceeded")
        inven_db.add_switch_log(sn, "Config soft time limit exceeded", level='warn')
        switch_rollback_config(sn, current_time=task_time)
    except ValueError as v:
        LOG.error(f"Occur error: {v}")
        update_task_status(config_type, session, task_name, sn, 3, task_traceback_info=f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: {str(v)}")
        inven_db.add_switch_log(sn, f"Config deploy failed: {str(v)}", level='warn')
        switch_rollback_config(sn, current_time=task_time)
    except Exception as e:
        LOG.error(f"Error during overall config distribution task: {e}")
        update_task_status(config_type, session, task_name, sn, 3, task_traceback_info=f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: {str(traceback.format_exc())}")
        inven_db.add_switch_log(sn, f"Confign deploy failed: {str(e)}", level='warn')
        switch_rollback_config(sn, current_time=task_time)
    else:
        LOG.info(f"Config deploy succeed")
        update_task_status(config_type, session, task_name, sn, 2, task_traceback_info=f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: Config deploy succeed")
        inven_db.add_switch_log(sn, f"Config deploy succeed", level='info')
    finally:
        session.close()


@my_celery_app.task(name="config_distribution_task_check_status", base=AmpConBaseTask)
def check_all_tasks_done(task_name, **kwargs):
    session = None
    end_time = time.time() + 600
    LOG.info(">>>>>>>>>>Start listening check task status ...")
    try:
        while time.time() < end_time:
            session = inven_db.get_session()
            config_task_objs = session.query(ConfigDistributionTaskForDC).filter(
                ConfigDistributionTaskForDC.task_name == task_name
            ).all()
            if not config_task_objs:
                break
            task_status_list = [task.task_status for task in config_task_objs if task.task_status is not None]
            task_time_list = [task.start_time for task in config_task_objs if task.start_time is not None]
            if not task_status_list:
                break
            LOG.info(f">>>>>>>>>>Listening check {task_name} task status, the task_status_list is {task_status_list}")
            unique_status = set(task_status_list)
            if unique_status == {2}:
                fabric_id = config_task_objs[0].fabric_id
                session.query(DCFabricTopology).filter(DCFabricTopology.id == fabric_id).update({"status": "Deployed"})
                break
            if unique_status.issubset({2, 3}):
                if 3 in unique_status:
                    fabric_id = config_task_objs[0].fabric_id
                    session.query(DCFabricTopology).filter(DCFabricTopology.id == fabric_id).update(
                        {"status": "Deploy Failed"})
                break
            if any((datetime.now() - start_time > timedelta(seconds=600)) for start_time in task_time_list):
                fabric_id = config_task_objs[0].fabric_id
                session.query(DCFabricTopology).filter(DCFabricTopology.id == fabric_id).update(
                    {"status": "Deploy Failed"})
                break
            if session:
                session.close()
            time.sleep(3)
    except Exception as e:
        LOG.error(traceback.format_exc())
    finally:
        if session:
            session.close()
        LOG.info(">>>>>>>>>>End listening check task status ...")

@my_celery_app.task(name="config_distribution_task_check_status_for_callback", base=AmpConBaseTask)
def check_all_tasks_done_for_callback(task_name, callback='', **kwargs):
    session = None
    end_time = time.time() + 600
    status = ""
    callback_kwargs = kwargs.get('callback_kwargs', {})
    LOG.info(">>>>>>>>>>Start listening check task status for callback ...")
    try:
        while time.time() < end_time:
            session = inven_db.get_session()
            config_task_objs = session.query(ConfigDistributionTaskForDC).filter(
                ConfigDistributionTaskForDC.task_name == task_name
            ).all()
            if not config_task_objs:
                status = "Deployed"
                break
            task_status_list = [task.task_status for task in config_task_objs if task.task_status is not None]
            task_time_list = [task.start_time for task in config_task_objs if task.start_time is not None]
            if not task_status_list:
                break
            LOG.info(f">>>>>>>>>>Listening check {task_name} task status, the task_status_list is {task_status_list}")
            unique_status = set(task_status_list)
            if unique_status == {2}:
                status = "Deployed"
                break
            if unique_status.issubset({2, 3}):
                if 3 in unique_status:
                    status = "Deploy Failed"
                break
            if any((datetime.now() - start_time > timedelta(seconds=600)) for start_time in task_time_list):
                status = "Deploy Failed"
                break
            if session:
                session.close()
            time.sleep(3)
    except Exception as e:
        LOG.error(traceback.format_exc())
    finally:
        if session:
            session.close()
        if callback:
            try:
                module_path, func_name = callback.rsplit('.', 1)
                module = importlib.import_module(module_path)
                callback_func = getattr(module, func_name)
                if callable(callback_func):
                    callback_func(status, **callback_kwargs)
            except Exception as e:
                LOG.error(f"Failed to execute callback: {str(e)}")
                LOG.error(traceback.format_exc())
    LOG.info(">>>>>>>>>>End listening check task status for callback ...")
    return {"status": 200, "res": status}

def categorize_tasks(old_val, new_val, sort_order):
    del_tasks = []
    update_tasks = []
    set_tasks = []

    for key in set(old_val) | set(new_val):
        if key in old_val and key in new_val:
            if old_val[key] != new_val[key]:
                update_tasks.append(f"update_{key}")
        elif key in old_val:
            del_tasks.append(f"del_{key}")
        elif key in new_val:
            set_tasks.append(f"set_{key}")

    def sort_tasks(tasks):
        return sorted(tasks, key=lambda x: sort_order.index(x.split('_')[1]) if x.split('_')[1] in sort_order else len(
            sort_order))

    sorted_tasks = sort_tasks(del_tasks) + sort_tasks(update_tasks) + sort_tasks(set_tasks)

    if not sorted_tasks:
        sorted_tasks = ["old_value same as new_value"]

    return sorted_tasks


def common_config_distribution(info, config_type, network_type=None, asynchronous=True, **kwargs):
    session = inven_db.get_session()
    msg = {}
    try:
        sort_order = ['hostname', 'link', 'mlag', 'bgp', 'ospf', 'overlay', 'inband', 'vlans', 'dhcp_relay', 'vrf',
                      'wan']
        task_name = "config-distribute-" + str(uuid.uuid4())
        for sn, data in info.items():
            old_val = data.get('old_val', {})
            new_val = data.get('new_val', {})
            meta_data = data.get('meta', {})
            trace_id = ""
            if config_type == 'dc':
                trace_id = meta_data.get('fabric_id') if network_type else meta_data.get('fabric_topo_id')
            else:
                trace_id = meta_data.get('site_topo_id')
            logic_name = meta_data.get('logic_name', '')
            role_name = meta_data.get('role', '')
            current_date = datetime.now().replace(microsecond=0)
            template_data = {
                'old_val': dict(sorted(old_val.items(),
                                       key=lambda item: sort_order.index(item[0]) if item[0] in sort_order else len(
                                           sort_order))),
                'new_val': dict(sorted(new_val.items(),
                                       key=lambda item: sort_order.index(item[0]) if item[0] in sort_order else len(
                                           sort_order)))
            }
            template_data = {k: v for k, v in template_data.items() if v}
            if not template_data:
                raise ValueError(
                    f"Error: template_data is empty for SN {sn}. Both old_val and new_val are empty or invalid.")
            task_type_all = ",".join(categorize_tasks(old_val, new_val, sort_order))
            # dc overlay 对于相同配置不做下发
            if "old_value same as new_value" in task_type_all and network_type in ['overlay_router', 'overlay_switch', 'overlay_link']:
                msg = {"status": 200, "res": "Deployed"}
                return msg
            task_data_class = ConfigDistributionTaskForDC if config_type == 'dc' else ConfigDistributionTaskForCampus
            type = network_type if network_type else 'underlay'
            extra_fields = (
                {"fabric_id": trace_id, "logic_name": logic_name, "type": type} if config_type == "dc" else {"site_id": trace_id}
            )

            task_data = task_data_class(
                task_name=task_name,
                sn=sn,
                task_role=role_name,
                task_type=task_type_all,
                config_data=json.dumps(template_data),
                task_status=0,
                assigned_to=flask_login.current_user.id if flask_login.current_user else None,
                create_time=current_date,
                modified_time=current_date,
                **extra_fields
            )
            inven_db.insert(task_data)
            task_id = handle_config_distribution.delay(config_type, network_type, template_data, sn, meta_data,
                                                       config_task_name=task_name,
                                                       config_task_time=current_date, config_task_role=role_name,
                                                       celery_task_name=f"{task_name}/{sn}", celery_sn=sn)
            task_obj = session.query(task_data_class).filter(task_data_class.task_name == task_name,
                                                             task_data_class.sn == sn)
            if task_obj:
                task_obj.update({"task_id": task_id})
        if config_type == 'dc':
            if asynchronous:
                if not network_type:
                    check_all_tasks_done.delay(task_name, celery_task_name=task_name)
                else:
                    check_all_tasks_done_for_callback.delay(task_name, celery_task_name=task_name, callback=kwargs.get('callback', ''),
                                                        callback_kwargs=kwargs.get('callback_kwargs', {}))
            else:
                if not network_type:
                    msg = check_all_tasks_done(task_name, celery_task_name=task_name)
                else:
                    msg = check_all_tasks_done_for_callback(task_name, celery_task_name=task_name, callback=kwargs.get('callback', ''),
                                                          callback_kwargs=kwargs.get('callback_kwargs', {}))
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {"status": 500, "info": f"error:{e}"}
    else:
        if asynchronous:
            msg = {"status": 200, "info": "Config distribution task started."}
    finally:
        if asynchronous:
            return jsonify(msg)
        else:
            return msg


def config_distribution_cli(info, asynchronous=True):
    # 获取请求数据
    # info = {
    #     "140E356C6E20": {
    #         'meta': {
    #             'role': 'leaf',
    #             'fabric_topo_id': 87,
    #             'logic_name': 'unittest-001-leaf1-1'
    #         },
    #         'new_val': {
    #             'hostname': 'unittest-001-leaf1-1',
    #             'mlag': {
    #                 'peer_vlan_id': '3966',
    #                 'mlag_l3_interface_ip_address': '**********/31',
    #                 'mlag_peer_lag_interface_name': 'ae48',
    #                 'mlag_interface_name': ['te-1/1/2', 'te-1/1/3'],
    #                 'peer_ipv4_address': '**********/31',
    #                 'domain_id': '1',
    #                 'domain_id_node': '0'
    #             },
    #             'ospf': {
    #                 'area_id': '0.0.0.0',
    #                 'ospf_router_id': '**********',
    #                 'vtep_interface': '**********',
    #                 'spine_link': [{
    #                     'interface_name': 'te-1/1/1',
    #                     'description': 'linking_unittest-001-leaf1-1_te-1/1/1',
    #                     'routed_interface_ip_address': '**********/31',
    #                     'routed_interface_network_address': '**********/31'
    #                 }
    #                 ],
    #                 'peer_link': {
    #                     'mlag_l3_interface_ip_address': '**********/31',
    #                     'peer_ipv4_address': '**********/31',
    #                     'mlag_l3_interface_network_address': '**********/31'
    #                 }
    #             },
    #             'overlay': {
    #                 'overlay_ibgp_asn': '584',
    #                 'ospf_router_id': '**********',
    #                 'vtep_interface': '**********',
    #                 'neighbor_router_id': ['**********']
    #             },
    #             'link': {
    #                 'reserved_vlan': '3967-4094',
    #                 'spine_link': [{
    #                     'interface_name': 'te-1/1/1',
    #                     'description': 'linking_unittest-001-leaf1-1_te-1/1/1',
    #                     'routed_interface_ip_address': '**********/31',
    #                     'routed_interface_network_address': '**********/31'
    #                 }
    #                 ]
    #             }
    #         },
    #         'old_val': {
    #
    #         }
    #     }
    # }
    return common_config_distribution(info, 'dc', asynchronous=asynchronous)


def config_distribution_netconf_by_mlag(info):
    # info = {
    #     "140E356C6E35": {
    #         "meta": {
    #             "role": "core",
    #             "site_topo_id": 149,
    #             "protocol": "BGP"
    #         },
    #         "old_val": {},
    #         "new_val": {
    #             "inband": {
    #                 "vlan_id": "3966",
    #                 "ip_address": "x.x.x.x/xx"
    #             },
    #             "vlans": {
    #                 "test": {
    #                     "vlan_id": "111",
    #                     "subnet": "**********/12"
    #                 },
    #                 "test1": {
    #                     "vlan_id": "1112",
    #                     "subnet": "**********/11"
    #                 }
    #             },
    #             "ospf": {
    #                 "router_id": "x.x.x.x",
    #                 "area_id": "xx"
    #             },
    #             "bgp": {
    #                 "asn": "",
    #                 "route_id": "",
    #                 "neighbour_ip_address": "",
    #                 "neighbour_asn": ""
    #             },
    #             "mlag": {
    #                 "domain_id": "250",
    #                 "domain_id_node": "0",
    #                 "peer_ipv4_address": "**********/31",
    #                 "mlag_peer_lag_interface_name": "ae49",
    #                 "mlag_peer_interface_name": ["te-1/1/1", "te-1/1/2"],
    #                 "mlag_peer_vlan_id": "3967",
    #                 "mlag_peer_l3_interface_ip": "x.x.x.x/30",
    #                 "mlag_access_links": [
    #                     {
    #                         "access_interface_name": ["ge-1/1/1"],
    #                         "access_lag_interface_name": "ae1",
    #                         "link_id": "1"
    #                     },
    #                     {
    #                         "access_interface_name": ["ge-1/1/2"],
    #                         "access_lag_interface_name": "ae2",
    #                         "link_id": "2"
    #                     }
    #                 ],
    #                 "vrrp_ip_address": ""
    #             },
    #             "dhcp_relay": [
    #                 {
    #                     "dhcp_network": "test",
    #                     "dhcp_server": "***********",
    #                     "vlan_id": "111"
    #                 }
    #             ],
    #             "vrf": {},
    #             "wan": {
    #                 "reserved_vlan": "3967-4094",
    #                 "wan_interface_name": ["ge-1/1/1", "ge-1/1/2"],
    #                 "wan_interface_ip": "x.x.x.x/30"
    #             }
    #         }
    #     }
    # }

    return common_config_distribution(info, 'campus', 'mlag')

def config_distribution_netconf_by_ip_clos(info):
    return common_config_distribution(info, 'campus', 'ip_clos')

def config_distribution_netconf_by_dc_overlay(info, type, asynchronous=True, **kwargs):
    return common_config_distribution(info, 'dc', type, asynchronous, **kwargs)

def switch_templates_config_deploy_start(switch_config_list, template_info):
    try:
        task_name = "template-" + str(uuid.uuid4())
        for switch_config in switch_config_list:
            sn = switch_config.get("sn", "")
            new_template = switch_config.get("new_template", {})
            old_template = switch_config.get("old_template", {})
            current_date = datetime.now().replace(microsecond=0)
            switch_new_and_old_template_config = {
                "new_template": new_template,
                "old_template": old_template
            }
            campus_task_log = ConfigDistributionTaskForCampus
            task_data = campus_task_log(
                task_name=task_name,
                task_id=template_info.get("id"),
                sn=sn,
                task_type="template",
                config_data=json.dumps(switch_new_and_old_template_config),
                task_status=0,
                site_id=1,
                assigned_to=flask_login.current_user.id if flask_login.current_user else None,
                create_time=current_date,
                modified_time=current_date
            )
            inven_db.insert(task_data)
            handle_switch_templates_config_deploy.delay(
                "campus",
                sn,
                switch_new_and_old_template_config,
                template_info,
                config_task_name=task_name,
                config_task_time=current_date,
                config_task_role="",
                celery_task_name=f"{task_name}/{sn}", celery_sn=sn)

    except Exception as e:
        LOG.error(traceback.format_exc())
        raise

@my_celery_app.task(name="config_distribution_switch_templates_task", base=AmpConBaseTask, soft_time_limit=10 * 60)
def handle_switch_templates_config_deploy(config_type, sn, switch_new_and_old_template_config, template_info, **kwargs):
    session = inven_db.get_session()
    task_time = kwargs.get('config_task_time', "")
    task_name = kwargs.get('config_task_name', "")
    xml_str = ""
    error_msg = ""

    try:
        # 更新 config_distribution_task_for_campus 表的状态字段 task_status 为 1
        update_task_status(config_type, session, task_name, sn, 1, start_time=datetime.now())
        LOG.info("Deploy switch template config data: %s" % switch_new_and_old_template_config)
        # 根据 sn 查 switch 表获取 host、username 和 password
        switch_obj, switch_ip, switch_op_user, switch_op_password = initialize_task(session, sn)
        # 打快照
        if not switch_backup_config(switch_obj.mgt_ip, sn, current_time=task_time):
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            error_msg = f"{current_time} : Before deploying the configuration, the switch needs to take a snapshot. This step failed. Please check whether the switch is running normally and try again later."
            LOG.error(error_msg)
            raise Exception(error_msg)
    except Exception as e:
        LOG.error(f"Error prepare for config distribution: {traceback.format_exc()}")
        update_task_status(config_type, session, task_name, sn, 3, task_traceback_info=str(e))
        return

    try:
        json_config = template_info["config"]
        template_info["config"] = {
            "json": json_config
        }
        netconf_deployer = NetConfConfigDeployer(host=switch_ip, username=switch_op_user,
                                                 password=switch_op_password,
                                                 port=830, session=session)
        deploy_status = netconf_deployer.deploy_template(switch_new_and_old_template_config)
        xml_str = deploy_status.get("xml")
        if deploy_status.get("status") != 200:
            error_msg = deploy_status.get("msg")
            raise ValueError(f"Error: {deploy_status['msg']}")
        else:
            archive_new_template_dict = deploy_status.get("archive_new_template_dict", {})
            archive_new_template_xml = deploy_status.get("archive_new_template_xml", {})
            switch_new_and_old_template_config["archive_new_template_dict"] = archive_new_template_dict
            switch_new_and_old_template_config["archive_new_template_xml"] = archive_new_template_xml
            template_info["archive_new_template_dict"] = archive_new_template_dict
            template_info["archive_new_template_xml"] = archive_new_template_xml
            update_site_node_and_task(switch_new_and_old_template_config, template_info, sn, task_name)

    except SoftTimeLimitExceeded as e:
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        LOG.error(f"Soft time limit exceeded error: {e}")
        update_task_status(config_type, session, task_name, sn, 3, task_traceback_info=f"{current_time} : [Timeout] The soft time limit for processing tasks has been exceeded!")
        inven_db.add_switch_log(sn, "Config soft time limit exceeded", level='warn')
        switch_rollback_config(sn, current_time=task_time)
    except ValueError as v:
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        LOG.error(f"Occur error: {v}")
        update_task_status(config_type, session, task_name, sn, 3, task_traceback_info=f"{current_time} : {error_msg}\n{xml_str}")
        LOG.info(f"----------------------{str(task_time)}---------------------------")
        inven_db.add_switch_log(sn, f"Confign deploy failed: {error_msg}", level='warn')
        switch_rollback_config(sn, current_time=task_time)
    except Exception as e:
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        LOG.error(f"Error during overall config distribution task: {e}")
        update_task_status(config_type, session, task_name, sn, 3, task_traceback_info=f"{current_time} : Switch template distribution error!\n{xml_str}")
        inven_db.add_switch_log(sn, f"Confign deploy failed: {str(e).split(':')[-1].strip()}", level='warn')
        switch_rollback_config(sn, current_time=task_time)
    else:
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        LOG.info(f"Config deploy succeed")
        update_task_status(config_type, session, task_name, sn, 2, task_traceback_info=f"{current_time} : Switch template distribution successful!\n{xml_str}")
        inven_db.add_switch_log(sn, f"Config deploy succeed", level='info')
    finally:
        session.close()


def update_site_node_and_task(switch_new_and_old_template_config, template_info, sn, task_name):
    try:
        old_template = switch_new_and_old_template_config.get("old_template")
        new_template = switch_new_and_old_template_config.get("new_template")
        archive_new_template_dict = switch_new_and_old_template_config.get("archive_new_template_dict")
        archive_new_template_xml = switch_new_and_old_template_config.get("archive_new_template_xml")
        template_id = template_info.get("id")
        session = inven_db.get_session()
        with session.begin():
            node = session.query(CampusSiteNodes).filter(
                CampusSiteNodes.topology_config_id == template_id,
                CampusSiteNodes.switch_sn == sn,
                CampusSiteNodes.type == "template"
            ).first()
            if node:
                archive_old_template_dict = node.node_info.get("archive_new_template_dict", {})
                archive_old_template_xml = node.node_info.get("archive_new_template_xml", {})
                new_node_info = {
                    "new_template": new_template,
                    "old_template": old_template,
                    "archive_new_template_dict": archive_new_template_dict,
                    "archive_new_template_xml": archive_new_template_xml,
                    "archive_old_template_dict": archive_old_template_dict,
                    "archive_old_template_xml": archive_old_template_xml
                }
                node.node_info = new_node_info
                task = session.query(ConfigDistributionTaskForCampus).filter(
                    ConfigDistributionTaskForCampus.task_id == template_id,
                    ConfigDistributionTaskForCampus.task_type == "template",
                    ConfigDistributionTaskForCampus.sn == sn,
                    ConfigDistributionTaskForCampus.task_name == task_name,
                ).order_by(ConfigDistributionTaskForCampus.create_time.desc()).first()
                if task:
                    task.config_data = json.dumps(new_node_info)
    except Exception as e:
        LOG.error(f"Error in update_site_node: {str(e)}")
        LOG.error(traceback.format_exc())
        raise
    

def config_distribution_roce(info):
    session = inven_db.get_session()
    msg = {}
    try:
        task_name = "config-distribute-" + str(uuid.uuid4())
        for sn, config_data in info.items():
            current_date = datetime.now().replace(microsecond=0)
            task_type_all = "set_roce"
            type = 'roce'
            extra_fields = (
                {"fabric_id": 0, "type": type}
            )

            task_data = ConfigDistributionTaskForDC(
                task_name=task_name,
                sn=sn,
                task_role="",
                task_type=task_type_all,
                config_data=json.dumps(config_data),
                task_status=0,
                assigned_to=flask_login.current_user.id if flask_login.current_user else None,
                create_time=current_date,
                modified_time=current_date,
                **extra_fields
            )
            inven_db.insert(task_data)
            task_id = handle_roce_config_distribution.delay(config_data, sn,
                                                       config_task_name=task_name,
                                                       config_task_time=current_date, config_task_role="",
                                                       celery_task_name=f"{task_name}/{sn}", celery_sn=sn)
            task_obj = session.query(ConfigDistributionTaskForDC).filter(ConfigDistributionTaskForDC.task_name == task_name,
                                                                         ConfigDistributionTaskForDC.sn == sn)
            if task_obj:
                task_obj.update({"task_id": task_id})


        status = check_all_config_tasks_done(task_name)
        if status != 1:
            config_task_objs = session.query(ConfigDistributionTaskForDC).filter(
                ConfigDistributionTaskForDC.task_name == task_name,
                ConfigDistributionTaskForDC.task_status != 2
            ).all()
            err_info ={}
            for task in config_task_objs:
                err_info[task.sn] = task.task_traceback_info
            msg = {"status": 500, "err_info": err_info}
          
        else:
            msg = {"status": 200}  

    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {"status": 500, "info": f"error:{e}"}
    return msg

@my_celery_app.task(name="config_distribution_roce_task", base=AmpConBaseTask, soft_time_limit=10 * 60)
def handle_roce_config_distribution(config_data, sn, **kwargs):
    config_type = "dc"
    session = inven_db.get_session()
    task_time = kwargs.get('config_task_time', "")
    task_name = kwargs.get('config_task_name', "")
    # task_role = kwargs.get('config_task_role', "")
    try:
        update_task_status(config_type, session, task_name, sn, 1, start_time=datetime.now())
        LOG.info("Set fabric network config data: %s" % config_data)
        switch_obj, switch_ip, switch_op_user, switch_op_password = initialize_task(session, sn)
        if not switch_backup_config(switch_obj.mgt_ip, sn, current_time=task_time):
            error_msg = "Backup configuration failed."
            LOG.error(error_msg)
            raise Exception(error_msg)
    except Exception as e:
        LOG.error(f"Error prepare for config distribution: {e}")
        update_task_status(config_type, session, task_name, sn, 3, task_traceback_info=f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: {str(e)}")
        return

    try:
        
        cli_deployer = CLIConfigDeployer(
            host=switch_ip,
            username=switch_op_user,
            password=switch_op_password,
            port=22
        )
        
        ret = cli_deployer.deploy_cli_roce_config(**config_data)
        
        if ret["status"] != 200:
            raise ValueError(f"Error: {ret['msg']}")
    except SoftTimeLimitExceeded:
        update_task_status(config_type, session, task_name, sn, 3, task_traceback_info=f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: Soft time limit exceeded")
        inven_db.add_switch_log(sn, "Config soft time limit exceeded", level='warn')
        switch_rollback_config(sn, current_time=task_time)
    except ValueError as v:
        LOG.error(f"Occur error: {v}")
        update_task_status(config_type, session, task_name, sn, 3, task_traceback_info=f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: {str(v)}")
        inven_db.add_switch_log(sn, f"Config deploy failed: {str(v)}", level='warn')
        switch_rollback_config(sn, current_time=task_time)
    except Exception as e:
        LOG.error(f"Error during overall config distribution task: {e}")
        update_task_status(config_type, session, task_name, sn, 3, task_traceback_info=f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: {str(traceback.format_exc())}")
        inven_db.add_switch_log(sn, f"Confign deploy failed: {str(e)}", level='warn')
        switch_rollback_config(sn, current_time=task_time)
    else:
        LOG.info(f"Config deploy succeed")
        update_task_status(config_type, session, task_name, sn, 2, task_traceback_info=f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: Config deploy succeed")
        inven_db.add_switch_log(sn, f"Config deploy succeed", level='info')
    finally:
        session.close()
      
  
def check_all_config_tasks_done(task_name):
    session = None
    end_time = time.time() + 600
    LOG.info(">>>>>>>>>>Start listening check task status ...")
    status = 0  # 0 unkwon  1 success 2 failed  3 timeout
    try:
        while time.time() < end_time:
            session = inven_db.get_session()
            config_task_objs = session.query(ConfigDistributionTaskForDC).filter(
                ConfigDistributionTaskForDC.task_name == task_name
            ).all()
            if not config_task_objs:
                break
            task_status_list = [task.task_status for task in config_task_objs if task.task_status is not None]
            task_time_list = [task.start_time for task in config_task_objs if task.start_time is not None]
            if not task_status_list:
                break
            LOG.info(f">>>>>>>>>>Listening check {task_name} task status, the task_status_list is {task_status_list}")
            unique_status = set(task_status_list)
            if unique_status == {2}:
                status = 1
                break
            if unique_status.issubset({2, 3}):
                status = 2
                break
            if any((datetime.now() - start_time > timedelta(seconds=600)) for start_time in task_time_list):
                status = 3
                break
            if session:
                session.close()
            time.sleep(3)
    except Exception as e:
        LOG.error(traceback.format_exc())
    finally:
        if session:
            session.close()
        LOG.info(">>>>>>>>>>End listening check task status ...")
        return status
    