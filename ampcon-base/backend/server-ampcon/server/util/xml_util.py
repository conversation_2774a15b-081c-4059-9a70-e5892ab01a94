from lxml import etree
from typing import List
import logging

LOG = logging.getLogger(__name__)

def merge_vlans_to_string(xml_strings: List[str], prepend: bool = True) -> str:
    """
    Merge multiple XML strings into the first XML string.

    :param xml_strings: List of XML strings, first one as base.
    :param prepend: True to insert nodes at the beginning, False to append at the end.
    :return: Merged XML string.
    """
    if not xml_strings:
        raise ValueError("xml_strings cannot be empty")

    # Parse the first XML string
    base_root = etree.fromstring(xml_strings[0].encode('utf-8'))
    base_tree = etree.ElementTree(base_root)

    # Get namespace
    nsmap = base_root.find(".//").nsmap
    vlans_base = base_root.find(".//v:vlans", namespaces={"v": nsmap[None]})
    if vlans_base is None:
        raise ValueError("No <vlans> node found in the first XML string")

    # Merge other XML strings
    for xml_str in xml_strings[1:]:
        root = etree.fromstring(xml_str.encode('utf-8'))
        vlans = root.find(".//v:vlans", namespaces={"v": nsmap[None]})
        if vlans is None:
            continue

        for vlan_id in vlans.findall("v:vlan-id", namespaces={"v": nsmap[None]}):
            if prepend:
                vlans_base.insert(0, vlan_id)
            else:
                vlans_base.append(vlan_id)

    merged_str = etree.tostring(base_tree, encoding='utf-8', xml_declaration=True, pretty_print=True).decode('utf-8')
    LOG.info("XML merge completed")
    return merged_str

if __name__ == "__main__":
    xml1 = """
    <config>
        <vlans xmlns="http://pica8.com/xorplus/vlans">
            <vlan-id>
                <id>3</id>
                <l3-interface>vlan3</l3-interface>
            </vlan-id>
        </vlans>
    </config>
    """

    xml2 = """
    <config>
        <vlans xmlns="http://pica8.com/xorplus/vlans">
            <vlan-id operation="delete">
                <id>2</id>
                <l3-interface>vlan2</l3-interface>
            </vlan-id>
        </vlans>
    </config>
    """
    merged_xml = merge_vlans_to_string([xml1, xml2], prepend=False)
