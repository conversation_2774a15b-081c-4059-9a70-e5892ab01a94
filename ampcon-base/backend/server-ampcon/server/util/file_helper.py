
import mimetypes
import os
import re
import stat

from flask import Response


def get_type(mode):
    if stat.S_ISDIR(mode) or stat.S_ISLNK(mode):
        type = 'dir'
    else:
        type = 'file'
    return type


def partial_response(path, start, end=None):
    file_size = os.path.getsize(path)

    if end is None:
        end = file_size
    end = min(end, file_size)
    length = end - start

    with open(path, 'rb') as fd:
        fd.seek(start)
        bytes = fd.read(length)
    assert len(bytes) == length

    response = Response(
        bytes,
        206,
        mimetype=mimetypes.guess_type(path)[0],
        direct_passthrough=True,
    )
    response.headers.add(
        'Content-Range', 'bytes {0}-{1}/{2}'.format(
            start, end, file_size,
        ),
    )
    response.headers.add(
        'Accept-Ranges', 'bytes'
    )
    return response


def get_range(request):
    range = request.headers.get('Range')
    m = re.match('bytes=(?P<start>\d+)-(?P<end>\d+)?', range)
    if m:
        start = m.group('start')
        end = m.group('end')
        start = int(start)
        if end is not None:
            end = int(end)
        return start, end
    else:
        return 0, None

if __name__ == '__main__':
    m = re.match('bytes=(?P<start>\d+)-(?P<end>\d+)?', 'bytes=1570-')
    if m:
        start = m.group('start')
        end = m.group('end')
        start = int(start)
        if end is not None:
            end = int(end)
        print(start)
        print(end)

    file_size = os.path.getsize('../config_gen/as4610_30p/auto-deploy.conf')
    print(file_size)
    if end is None:
        end = file_size
    end = min(end, file_size)

    length = end - start

    with open('../config_gen/as4610_30p/auto-deploy.conf', 'rb') as fd:
        fd.seek(start)
        bytes = fd.read(length)
    print(length)
    assert len(bytes) == length
