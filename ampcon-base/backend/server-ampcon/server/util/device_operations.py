
import logging
from server.db.models.wireless_openwifi import Devices
from server.util.tip_client_util import ServerType, post
LOG = logging.getLogger(__name__)
def delete_devices_by_sn(sn_list, pg_session):
    if not sn_list or not isinstance(sn_list, list):
        return False, "Missing or invalid 'snList' parameter", 400

    try:
        json_data = {"snList": sn_list}
        gw_data = post(ServerType.OWGW, "/api/v1/disconnect", json_data)
        if not gw_data:
            return False, "Failed to get response from GW disconnect API", 502
    except Exception as gw_error:
        return False, f"Failed to contact GW disconnect API: {str(gw_error)}", 502

    if gw_data.get("Code") != 0:
        return False, f"Some devices could not be disconnected. Cannot proceed with deletion.", 409

    try:

        deleted_count = pg_session.query(Devices).filter(
            Devices.serialnumber.in_(sn_list)
        ).delete(synchronize_session=False)
        return True, f"Batch delete device success.", 200
    except Exception as e:
        return False, f"Error batch delete: {str(e)}", 500