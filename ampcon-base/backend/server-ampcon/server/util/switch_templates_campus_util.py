import ipaddress
import re


def check_import_template(template):
    try:
        switch = template.get("switch")
        # 校验 network
        network = switch.get("network").get("networks")
        status, msg = check_network(network)
        if not status:
            return False, f"Failed to import template. {msg}"

        # 校验 vrf
        vfr = switch.get("vrf").get("networks")
        status, msg = check_vrf(vfr, network)
        if not status:
            return False, f"Failed to import template. {msg}"

        # 校验 ntp
        ntp = switch.get("ntp").get("ip")
        status, msg = check_ntp(ntp)
        if not status:
            return False, f"Failed to import template. {msg}"

        # 校验 dns
        dns = switch.get("dns").get("ip")
        status, msg = check_dns(dns)
        if not status:
            return False, f"Failed to import template. {msg}"

        # 校验 static route
        static_route_configuration = switch.get("staticRoute").get("configuration")
        static_route_list = switch.get("staticRoute").get("routes")
        status, msg = check_static_route(static_route_configuration, static_route_list)
        if not status:
            return False, f"Failed to import template. {msg}"

        # 校验 ospf
        ospf_configuration = switch.get("ospf").get("configuration")
        ospf_areas = switch.get("ospf").get("areas")
        status, msg = check_ospf(ospf_configuration, ospf_areas)
        if not status:
            return False, f"Failed to import template. {msg}"

        # 校验端口配置
        port_config = switch.get("portConfiguration")
        port_config_app = switch.get("portConfigApplication")
        status, msg = check_port_profile_and_application(port_config, port_config_app, network)
        if not status:
            return False, f"Failed to import template. {msg}"

        management = template.get("management")
        # 校验 local user
        local_user_list = management.get("localUser").get("users")
        status, msg = check_local_user(local_user_list)
        if not status:
            return False, f"Failed to import template. {msg}"

        # 校验 login banner
        login_banner = management.get("loginBanner")
        status, msg = check_login_banner(login_banner)
        if not status:
            return False, f"Failed to import template. {msg}"

        # 校验 idle timeout
        idle_timeout = management.get("timeout")
        status, msg = check_idle_timeout(idle_timeout)
        if not status:
            return False, f"Failed to import template. {msg}"

        return True, "Success."
    except Exception as e:
        return False, "Failed to import template."


def check_network(network_list):
    """
    Name：最长11字符，合法字符为大小写字母、数字、特殊符号（-、.、_、@、=、#）
    VLAN ID：2-4094
    Network Name不能重复，VLAN ID不能重复
    """
    try:
        id_set = set()
        name_set = set()
        for network in network_list:
            id, name = network["id"], network["name"]
            id_set.add(id)
            name_set.add(name)
            if not id.isdigit() or not 2 <= int(id) <= 4094:
                return False, "Error in Network : The vlan id range must be between 2 and 4094."
            pattern = re.compile(r'^[A-Za-z0-9\-._@=#]{1,11}$')
            if not bool(pattern.fullmatch(name)):
                return False, "Error in Network : Name format: max 11 chars, only A-Z a-z 0-9 -._@=# allowed."
        if len(id_set) != len(network_list):
            return False, "Error in Network : Duplicate id is not allowed."
        if len(name_set) != len(network_list):
            return False, "Error in Network : Duplicate name is not allowed."
        return True, "Check succeeded."
    except Exception as e:
        return False, "Error in Network."


def check_vrf(vrf_list, network_list):
    """
    VRF Name：最长 15 字符，合法字符为大小写字母、数字、特殊符号（-、.、_、@、=、#）
    VRF Name 不能重复且不能和 Network Name 重复，VRF 包含的 Network 不能重复
    特殊值 VRF Name
        不支持输入 mgmt-vrf，default，vlan.1,...,vlan.4094，eth0，eth1，vlan 和 vlan 开头加任何字符格式（如 vlan1）
        不支持输入 gretap0、erspan0、sit0、bridge0、bridge、pimreg、ipmr-lo、MGMT_VRF、tun0、lo
        不支持输入 te、ge、eth、ae、xe 开头带数字格式的值（如 ge1）
    """
    try:
        vrf_id_list = list()
        for vrf in vrf_list:
            ids, name = vrf["id"], vrf["name"]
            for id in ids:
                vrf_id_list.append(id)
            if not isinstance(name, str):
                return False, "Error in VRF : must be string"
            if len(name) > 15 or not re.fullmatch(r"[A-Za-z0-9\-._@=#]+", name):
                return False, "Error in VRF : Name format: max 15 chars, only A-Z a-z 0-9 -._@=# allowed."
            blacklist = {
                "mgmt-vrf", "default", "eth0", "eth1", "gretap0", "erspan0",
                "sit0", "bridge0", "bridge", "pimreg", "ipmr-lo",
                "tun0", "lo"
            }
            if (name in blacklist) or (name.startswith("vlan")) or (re.match(r"^(te|ge|eth|ae|xe)\d", name)):
                return False, f"Error in VRF : Name ({name}) is not allowed."

        if len(vrf_id_list) != len(set(vrf_id_list)):
            return False, "Error in VRF : Duplicate id is not allowed."

        network_name_id_list = [f"{d['name']}({d['id']})" for d in network_list]
        if not set(vrf_id_list) == set(network_name_id_list):
            return False, "Error in VRF : The VRF id needs to be in the Network."

        network_name_list = [d["name"] for d in network_list]
        vrf_name_list = [d["name"] for d in vrf_list]
        if set(network_name_list) & set(vrf_name_list):
            return False, "Error in VRF : The VRF name cannot be the same as the Network name."

        if len(set(vrf_name_list)) != len(vrf_name_list):
            return False, "Error in VRF : Duplicate name is not allowed."

        return True, "Check succeeded."
    except Exception as e:
        return False, "Error in VRF."


def check_ntp(ntp_list):
    try:
        for ip in ntp_list:
            if not ipaddress.IPv4Network(ip, strict=True):
                return False, "Error in NTP: The IPv4 format is illegal."
        return True, "Check succeeded."
    except Exception as e:
        return False, "Error in NTP: The IPv4 format is illegal."


def check_dns(dns_list):
    try:
        for ip in dns_list:
            if not ipaddress.IPv4Network(ip, strict=True):
                return False, "Error in DNS: The IPv4 format is illegal."
        return True, "Check succeeded."
    except Exception as e:
        return False, "Error in DNS: The IPv4 format is illegal."


def check_static_route(configuration, static_route_list):
    """
    Destination：合法格式为IPv4格式/掩码（xxx.xxx.xxx.xxx/xx）
    Next Hop：合法格式为Ipv4格式
    Destination不能重复，不同Destination的Next Hop可重复，同Destination的Next Hop不能重复
    """
    try:
        if not isinstance(configuration, bool):
            return False, "Error in Static Route : Configuration is not bool."
        if configuration and not static_route_list:
            return False, "Error in Static Route : Static Route has been enabled, but cannot be empty."
        if not configuration and static_route_list:
            return False, "Error in Static Route : Configuration setting error."
        dest_set = set()
        for route in static_route_list:
            destination = route.get("destination")
            if not ipaddress.IPv4Network(destination, strict=False):
                return False, "Error in Static Route : The IPv4 format is illegal."
            if destination in dest_set:
                return False, "Error in Static Route : Duplicate destination are not allowed."
            dest_set.add(destination)
            nexthop_set = set()
            for ip in route.get("nexthop"):
                if not ipaddress.IPv4Network(ip, strict=True):
                    return False, "Error in Static Route : The IPv4 format is illegal."
                if ip in nexthop_set:
                    return False, "Error in Static Route : Duplicate next hop are not allowed."
                nexthop_set.add(ip)
        return True, "Check succeeded."
    except Exception as e:
        return False, "Error in Static Route."


def check_ospf(configuration, areas):
    """
    IPv4/Prefixlen：合法格式为IPv4格式/掩码（xxx.xxx.xxx.xxx/xx）
    Area ID：0-4294967295或Ipv4格式
    Area Type：Default、Stub、NSSA
    IPv4/Prefixlen不能重复，Area ID、Area Type可重复，Area ID为0或0.0.0.0时Area Type只能为Default
    """

    def is_valid_ipv4_or_uint32(s: str) -> bool:
        try:
            # 先尝试作为数字
            if 0 <= int(s) <= 4294967295:
                return True
        except ValueError:
            pass

        try:
            # 再尝试作为 IPv4
            parts = s.split('.')
            if len(parts) == 4 and all(0 <= int(p) <= 255 for p in parts):
                return True
        except ValueError:
            pass

        return False

    try:
        if not isinstance(configuration, bool):
            return False, "Error in OSPF : Configuration is not bool."
        if configuration and not areas:
            return False, "Error in OSPF : OSPF has been enabled, but cannot be empty."
        if not configuration and areas:
            return False, "Error in OSPF : Configuration setting error."
        ipv4_set = set()
        for area in areas:
            if not is_valid_ipv4_or_uint32(area.get("id")):
                return False, "Error in OSPF : The id format is illegal."
            for ipv4 in area.get("ipv4"):
                if not ipaddress.IPv4Network(ipv4, strict=False):
                    return False, "Error in OSPF : The IPv4 format is illegal."
            if area.get("type") not in ["default", "stub", "nssa"]:
                return False, "Error in OSPF : The type is illegal."

            for ip in area.get("ipv4"):
                if ip in ipv4_set:
                    return False, "Error in OSPF : Duplicate IPv4 are not allowed."
                ipv4_set.add(ip)

            if (str(area.get("id")) == "0" or str(area.get("id")) == "0.0.0.0") and (not area.get("type") == "default"):
                return False, "Error in OSPF : When Area ID is 0 or 0.0.0.0, Area Type can only be Default."

        return True, "Check succeeded."
    except Exception as e:
        return False, "Error in OSPF."


def check_port_profile_and_application(port_config, port_config_app, network_list):
    """
    Port Profile
        Profile Name：最长32字符，合法字符为大小写字母、数字、任意符号、中文
        Description：无限制
        Port VLAN：可选参数为添加的Network Name
        Trunk VLAN：可选参数为添加的Network Name
        Speed：Auto，10M，100M，1G，2.5G，5G，10G，25G，40G，100G
        Type：Broadcast，Multicast，Unicast
        Packet Count Per Second：0-30000000
        Percentage of Physical Link：0-100
        Kilobits per Second：0-10000000
    Port Profile Application
        Ports：使用ge/te/ge/xe开头，格式为ge-1/1/1，或ge-1/1/1,ge-1/1/2，或ge-1/1/1-3
    """
    try:
        network_vlanid_list = [f"vlan{d['id']}" for d in network_list]
        Port_Profile_list = []
        for config in port_config:
            Profile_Name = config.get("name")
            Port_Profile_list.append(Profile_Name)
            pattern = re.compile(r'^[\w\W]{1,32}$')
            if not bool(pattern.fullmatch(Profile_Name)):
                return False, "Error in Port Profile : The name format is illegal."
            if Profile_Name in ["Default", "default"]:
                return False, "Error in Port Profile : The name format is illegal."

            Port_Enable = config.get("portEnable")
            if Port_Enable not in ["enable", "disable"]:
                return False, "Error in Port Profile : The Port Enable format is illegal."

            Description = config.get("description")
            if not isinstance(Description, str):
                return False, "Error in Port Profile : Description must be a string."

            Port_Mode = config.get("portMode")
            if Port_Mode not in ["trunk", "access"]:
                return False, "Error in Port Profile : The Port Mode format is illegal."

            if Port_Mode == "trunk":
                Trunk_VLAN = config.get("trunkNetwork")
                for vlan in Trunk_VLAN:
                    if vlan == "vlan1" or vlan == "all networks":
                        continue
                    if vlan not in network_vlanid_list:
                        return False, "Error in Port Profile : The vlan is illegal."

            Port_VLAN = config.get("portNetwork")
            if Port_VLAN != "vlan1":
                if Port_VLAN not in network_vlanid_list:
                    return False, "Error in Port Profile : The vlan is illegal."

            Speed = config.get("speed")
            speed_range = ["auto", "10", "100", "1000", "2500", "5000", "10000", "25000", "40000", "100000"]
            if Speed not in speed_range:
                return False, "Error in Port Profile : The Speed is illegal."

            PoE = config.get("poe")
            if PoE not in ["enable", "disable"]:
                return False, "Error in Port Profile : The PoE format is illegal."

            Storm_Control = config.get("stormControl")
            if Storm_Control not in ["enable", "disable"]:
                return False, "Error in Port Profile : The Storm_Control format is illegal."
            if Storm_Control == "enable":
                Type = config.get("modes")
                if not Type:
                    return False, "Error in Port Profile : The Type cannot be empty."
                if len(Type) > 3:
                    return False, "Error in Port Profile : The Type is illegal."
                if not set(Type).issubset(["broadcast", "multicast", "unicast"]):
                    return False, "Error in Port Profile : The Type is illegal."
                Control_Method = config.get("controlMethod")
                if Control_Method not in ["pps", "ratio", "kbps"]:
                    return False, "Error in Port Profile : The Control Method is illegal."
                if Control_Method == "pps":
                    if not 0 <= int(config.get("pcps")) <= 30000000:
                        return False, "Error in Port Profile : The range of PPS is from 0 to 30000000."
                elif Control_Method == "ratio":
                    if not 0 <= int(config.get("percentage")) <= 100:
                        return False, "Error in Port Profile : The range of Ratio is from 0 to 100."
                elif Control_Method == "kbps":
                    if not 0 <= int(config.get("kps")) <= 10000000:
                        return False, "Error in Port Profile : The range of Kbps is from 0 to 10000000."

        port_name_list = list()
        for apply in port_config_app:
            port = apply.get("port")
            profile = apply.get("profile")
            if not valid_port(port):
                return False, "Error in Port Profile Application : The Port is illegal."
            if profile == "Default":
                continue
            if not profile in Port_Profile_list:
                return False, "Error in Port Profile Application : The Port Profile is illegal."
            port_name_list.extend(get_ports(port))
        if len(port_name_list) != len(set(port_name_list)):
            return False, "Error in Port Profile Application : The Port cannot be repeated."

        return True, "Check succeeded."
    except Exception as e:
        return False, "Error in NTP: The IPv4 format is illegal."


def valid_port(s: str) -> bool:
    """
    判断字符串是否合法：
    - 单个端口：ge-1/1/1
    - 多个端口：ge-1/1/1,ge-1/1/2
    - 范围端口：xe-1/1/5-7
    """
    # 单个端口或范围的正则
    item_pattern = re.compile(
        r'^(?P<pre>ge|te|xe)-\d{1,4}/\d{1,4}/(?P<start>\d{1,4})(?:-(?P<end>\d{1,4}))?$',
        re.I
    )
    for part in s.strip().split(","):
        m = item_pattern.fullmatch(part)
        if not m:
            return False
        if m.group("end") and int(m.group("end")) < int(m.group("start")):
            return False
    return True


def normalize_port(port_str):
    """
    输入：te-1/1/1
          te-1/1/2,te-1/1/3
          te-1/1/4-6
    输出：依次产生单个端口字符串
    """
    # 去掉空格防手误
    port_str = port_str.replace(' ', '')

    # 情况1：逗号分隔
    if ',' in port_str:
        for item in port_str.split(','):
            yield from normalize_port(item)
        return

    # 情况2：连字符范围
    m = re.match(r'^(.*?)(\d+)-(\d+)$', port_str)
    if m:
        prefix, start, end = m.groups()
        for num in range(int(start), int(end) + 1):
            yield f'{prefix}{num}'
        return

    # 情况3：已经是单个端口
    yield port_str


def get_ports(s: str):
    return list(normalize_port(s))


def check_local_user(user_list):
    """
    Username：最长32字符，合法字符为大小写字母、数字、下划线_、连字符-、句点.
    User Level：read-only、super-user
    Password：最少6字符，合法字符为大小写字母、数字、特殊符号，且Password必须包含一个大写字母、小写字母、数字、特殊符号
    """
    try:
        for user in user_list:
            username = user.get("name")
            if username == "admin":
                return False, "Error in Local User : Name cannot be set to admin."
            pattern = re.compile(r'^[A-Za-z0-9_.-]{1,32}$')
            if not bool(pattern.fullmatch(username)):
                return False, "Error in Local User : Name: ≤32 chars, letters A–Z a–z, digits 0–9, underscore, hyphen or dot only."

            if user.get("level") not in ["super-user", "read-only"]:
                return False, "Error in Local User : Level can only be set to super-user and read-only."

            if user.get("password") != user.get("confirmpassword"):
                return False, "Error in Local User : Password and confirmation password do not match."

            pattern = re.compile(
                r'^(?=.*[A-Z])'  # 至少 1 个大写
                r'(?=.*[a-z])'  # 至少 1 个小写
                r'(?=.*\d)'  # 至少 1 个数字
                r'(?=.*[^A-Za-z0-9])'  # 至少 1 个特殊字符
                r'.{6,}$'  # 长度 ≥ 6
            )
            if not pattern.match(user.get("password")):
                return False, "Error in Local User : Password is invalid."
        return True, "Check succeeded."
    except Exception as e:
        return False, "Error in Local User."


def check_login_banner(login_banner):
    """
    Banner before Login：最大行数限制为20行
    Banner after Login：最大行数限制为20行
    """
    try:
        before = login_banner.get("before")
        after = login_banner.get("after")
        if before.count('\n') > 20:
            return False, "Error in Login Banner : Banner before Login has more than 20 lines"
        if after.count('\n') > 20:
            return False, "Error in Login Banner : Banner after Login has more than 20 lines"
        return True, "Check succeeded."
    except Exception as e:
        return False, "Error in Login Banner."


def check_idle_timeout(idle_timeout):
    """
    Idle Timeout：0-20000
    """
    try:
        if not 0 <= int(idle_timeout) <= 20000:
            return False, "Error in Idle Timeout : The time range is from 0 to 20000."
        return True, "Check succeeded."
    except Exception as e:
        return False, "Error in Idle Timeout."
