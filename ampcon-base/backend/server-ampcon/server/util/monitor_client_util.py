import logging
from datetime import datetime, timedelta, timezone

from server.db.models.wireless import WirelessClient
from server.db.pg_engine import get_pg_session
from server.db.redis_common import RedisSessionFactory
from server.util.tip_client_util import ServerType, get

LOG = logging.getLogger(__name__)
redis_client = RedisSessionFactory.get_client()
REDIS_VENDOR_KEY_PREFIX = "monitor:client:mac:vendor:"
CACHE_TTL = timedelta(days=1)  # 1 天
MAX_VENDOR_LENGTH = 64


def get_vendor_by_mac_redis(macList):
    """
       从 Redis 缓存或接口获取 MAC 对应的厂商信息
       :param mac_list: list[str] - 需要查询的 MAC 地址列表
       :return: dict[str, str] - MAC: vendor_name 映射
       """
    result_map = {}
    macs_to_query = []

    # 1. 先从 Redis 取
    for mac in macList.split(","):
        cache_key = f"{REDIS_VENDOR_KEY_PREFIX}{mac}"
        cached_value = redis_client.get(cache_key)
        if cached_value:
            result_map[mac] = cached_value.decode("utf-8")
        else:
            macs_to_query.append(mac)

    # 2. 对 Redis 缓存中没有的 MAC 调接口
    if macs_to_query:
        try:
            data = get(ServerType.OWGW, f"/api/v1/ouis?macList={','.join(macs_to_query)}")
            if not data:
                LOG.warning('ouis interface no response')
                return result_map
            vendor_list = data.get('tagList', [])
            if len(vendor_list) == 0:
                LOG.warning('ouis interface response error')
                return result_map
            # 3. 保存到 Redis，并更新 result_map
            for mac, vendor in zip(macs_to_query, vendor_list):
                vendor_value = vendor.get('value')
                if vendor_value:
                    # 截断到 64 个字符
                    vendor_value = vendor_value[:MAX_VENDOR_LENGTH]
                    result_map[mac] = vendor_value
                    redis_client.setex(
                        f"{REDIS_VENDOR_KEY_PREFIX}{mac}",
                        CACHE_TTL,
                        vendor_value
                    )
                else:
                    result_map[mac] = ""
                LOG.info('search mac: %s,result vendor: %s,Upcoming Storage vendor: %s', mac,
                         vendor.get('value'),
                         result_map[mac])
        except Exception as e:
            LOG.error(f"Error occurred while fetching vendor for MAC: {macs_to_query}", e)

    return result_map


def delete_wireless_client_timeout():
    seven_days_ago = datetime.now(timezone.utc) - timedelta(days=7)
    with get_pg_session() as session:
        with session.begin():
            # 查询超过 7 天的数据
            expired_count = session.query(WirelessClient) \
                .filter(WirelessClient.create_time < seven_days_ago) \
                .delete(synchronize_session=False)
    LOG.info(f"已清理 {expired_count} 个无线终端数据。")
    return f"已清理 {expired_count} 个无线终端数据。"
