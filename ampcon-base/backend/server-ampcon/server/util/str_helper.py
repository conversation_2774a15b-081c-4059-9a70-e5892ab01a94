import re
import logging
from server.db.models.general import general_db

LOG = logging.getLogger(__name__)

key_reg = re.compile(r'key\s+([^\s]+)')
password_reg = re.compile(r'password\s+([^\s]+)')


def parse_tree(tree_str):
    result = []
    tree_lines = tree_str.strip().split('\n')
    
    cur_prefix = []
    for i, line in enumerate(tree_lines):
        prefix_str = ''
        line = line.strip()
        if line.endswith('{'):
            cur_prefix.append(line.strip('{').strip())
            continue
        
        if line.endswith('}'):
            prefix_str = ' '.join(cur_prefix)
            if cur_prefix:
                cur_prefix.pop()
            pre_line = '' if i == 0 else tree_lines[i - 1].strip()
            if pre_line.endswith('{'):
                line = prefix_str
            else:
                continue
        
        if cur_prefix:
            config = '%s %s' % (' '.join(cur_prefix), line.strip())
        else:
            config = prefix_str
        
        if config != '':
            result.append(config)
    
    return result


def strip_cli_stdout(command, stdout):
    com = '%s\r\r\n.\r\r\n' % command
    index = stdout.find(com)
    r_index = stdout.rfind('}')
    return stdout[index + len(com):r_index + 1].strip().replace('\r', '')


def compare_tree_configs(src, des):
    dest = parse_tree(des)
    
    tree_lines = src.strip().split('\n')
    
    diff_lines = []
    cur_prefix = []

    aggregate_untagged_regex = r"set interface aggregate-ethernet (?P<name>.*) family ethernet-switching vlan members (?P<vlan_id>[0-9]{1}.*) untagged"
    aggregate_regex = r"set interface aggregate-ethernet (?P<name>.*) family ethernet-switching vlan members (?P<vlan_id>[0-9]{1}.*)"

    all_vlan_id_list = re.findall(
        r"set interface aggregate-ethernet .* family ethernet-switching vlan members \d+(?:-\d+)?(?:,\d+(?:-\d+)?)*(?: untagged)?",
        des)
    switch_untagged_list, switch_default_list = list(), list()
    if all_vlan_id_list:
        for vlan_info in all_vlan_id_list:
            if "untagged" in vlan_info:
                switch_untagged_info = re.search(aggregate_untagged_regex, des).groupdict()["vlan_id"]
                switch_untagged_list.append(switch_untagged_info)
            else:
                switch_default_info = re.search(aggregate_regex, des).groupdict()["vlan_id"]
                switch_default_list.append(switch_default_info)

    gigabit_regex = r"set interface gigabit-ethernet (?P<name>.*) family ethernet-switching vlan members (?P<vlan_id>[0-9]{1}.*)"
    gigabit_untagged_regex = r"set interface gigabit-ethernet (?P<name>.*) family ethernet-switching vlan members (?P<vlan_id>[0-9]{1}.*) untagged"

    all_git_vlan_id_list = re.findall(
        r"set interface gigabit-ethernet .* family ethernet-switching vlan members \d+(?:-\d+)?(?:,\d+(?:-\d+)?)*(?: untagged)?",
        des)
    switch_git_untagged_list, switch_git_default_list = list(), list()
    if all_vlan_id_list:
        for vlan_info in all_git_vlan_id_list:
            if "untagged" in vlan_info:
                switch_git_untagged_info = re.search(gigabit_untagged_regex, des).groupdict()["vlan_id"]
                switch_git_untagged_list.append(switch_git_untagged_info)
            else:
                switch_git_default_info = re.search(gigabit_regex, des).groupdict()["vlan_id"]
                switch_git_default_list.append(switch_git_default_info)
    
    for i, line in enumerate(tree_lines):
    
        aggregate_match_untagged = re.search(aggregate_untagged_regex, line)
        if aggregate_match_untagged and switch_untagged_list and len(switch_untagged_list) == 1:
            data_config = aggregate_match_untagged.groupdict()["vlan_id"]
            if compare_vlan_members(data_config, switch_untagged_list[0]):
                continue
            else:
                diff_lines.append(line)
                continue
    
        aggregate_match = re.search(aggregate_regex, line)
        if aggregate_match and switch_default_list and len(switch_default_list) == 1:
            data_config = aggregate_match.groupdict()["vlan_id"]
            if compare_vlan_members(data_config, switch_default_list[0]):
                continue
            else:
                diff_lines.append(line)
                continue
    
        gigabit_match_untagged = re.search(gigabit_untagged_regex, line)
        if gigabit_match_untagged and switch_git_untagged_list and len(switch_git_untagged_list) == 1:
            data_config = gigabit_match_untagged.groupdict()["vlan_id"]
            if compare_vlan_members(data_config, switch_git_untagged_list[0]):
                continue
            else:
                diff_lines.append(line)
                continue
    
        gigabit_match = re.search(gigabit_regex, line)
        if gigabit_match and switch_git_default_list and len(switch_git_default_list) == 1:
            data_config = gigabit_match.groupdict()["vlan_id"]
            if compare_vlan_members(data_config, switch_git_default_list[0]):
                continue
            else:
                diff_lines.append(line)
                continue
             
        prefix_str = ''
        line = line.strip()
        if line.lstrip().startswith('/*') or 'plain-text-password' in line:
            continue
        
        if line.endswith('{'):
            cur_prefix.append(line.strip('{').strip())
            continue
        
        if line.endswith('}'):
            prefix_str = ' '.join(cur_prefix)
            if cur_prefix:
                cur_prefix.pop()
            pre_line = '' if i == 0 else tree_lines[i - 1].strip()
            if pre_line.endswith('{'):
                line = prefix_str
            else:
                continue
        
        if cur_prefix:
            config = '%s %s' % (' '.join(cur_prefix), line.strip())
        else:
            config = prefix_str
        if config != '' and config not in dest:
            diff_lines.append(config)
    
    if len(diff_lines) > 0:
        return False, diff_lines
    
    return True, None


def get_vlan_members(vlan_id_info):
    vlan_list = []
    for vlan_id in vlan_id_info.split(","):
        if "-" in vlan_id:
            start = int(vlan_id.split("-")[0])
            end = int(vlan_id.split("-")[1]) + 1
            vlan_list += list(range(start, end))
        else:
            vlan_list.append(int(vlan_id))
    return vlan_list


def compare_vlan_members(data_info, switch_info):
    data_vlan_list = get_vlan_members(data_info)
    switch_vlan_list = get_vlan_members(switch_info)
    return set(data_vlan_list).issubset(set(switch_vlan_list))


def compare_cmd_lines(src, des):
    s = des.find('set\r\n')
    e = des.rfind('\r\n')
    des = des[s + 5:e].lstrip('.\r\n')
    
    src = src.replace('\r', '').rstrip('\n')
    src_lines = src.split('\n')
    max_diff_num = 0
    
    real_diff_lines = []

    aggregate_untagged_regex = r"set interface aggregate-ethernet (?P<name>.*) family ethernet-switching vlan members (?P<vlan_id>[0-9]{1}.*) untagged"
    aggregate_regex = r"set interface aggregate-ethernet (?P<name>.*) family ethernet-switching vlan members (?P<vlan_id>[0-9]{1}.*)"
    
    all_vlan_id_list = re.findall(r"set interface aggregate-ethernet .* family ethernet-switching vlan members \d+(?:-\d+)?(?:,\d+(?:-\d+)?)*(?: untagged)?", des)
    switch_untagged_list, switch_default_list = list(), list()
    if all_vlan_id_list:
        for vlan_info in all_vlan_id_list:
            if "untagged" in vlan_info:
                switch_untagged_info = re.search(aggregate_untagged_regex, des).groupdict()["vlan_id"]
                switch_untagged_list.append(switch_untagged_info)
            else:
                switch_default_info = re.search(aggregate_regex, des).groupdict()["vlan_id"]
                switch_default_list.append(switch_default_info)

    gigabit_regex = r"set interface gigabit-ethernet (?P<name>.*) family ethernet-switching vlan members (?P<vlan_id>[0-9]{1}.*)"
    gigabit_untagged_regex = r"set interface gigabit-ethernet (?P<name>.*) family ethernet-switching vlan members (?P<vlan_id>[0-9]{1}.*) untagged"

    all_git_vlan_id_list = re.findall( r"set interface gigabit-ethernet .* family ethernet-switching vlan members \d+(?:-\d+)?(?:,\d+(?:-\d+)?)*(?: untagged)?", des)
    switch_git_untagged_list, switch_git_default_list = list(), list()
    if all_vlan_id_list:
        for vlan_info in all_git_vlan_id_list:
            if "untagged" in vlan_info:
                switch_git_untagged_info = re.search(gigabit_untagged_regex, des).groupdict()["vlan_id"]
                switch_git_untagged_list.append(switch_git_untagged_info)
            else:
                switch_git_default_info = re.search(gigabit_regex, des).groupdict()["vlan_id"]
                switch_git_default_list.append(switch_git_default_info)
    
    for line in src_lines:
        
        if 'key' in line or 'password' in line or line == '':
            continue
        
        line = line.strip()
        
        if '\"' in line:
            pre, seg, aft = line.partition('\"')
            pre = re.sub(' {2,}', ' ', pre)
            line = pre + seg + aft
        else:
            line = re.sub(' {2,}', ' ', line)
            
        aggregate_match_untagged = re.search(aggregate_untagged_regex, line)
        if aggregate_match_untagged and switch_untagged_list and len(switch_untagged_list) == 1:
            data_config = aggregate_match_untagged.groupdict()["vlan_id"]
            if compare_vlan_members(data_config, switch_untagged_list[0]):
                continue
            else:
                real_diff_lines.append(line)
                continue

        aggregate_match = re.search(aggregate_regex, line)
        if aggregate_match and switch_default_list and len(switch_default_list) == 1:
            data_config = aggregate_match.groupdict()["vlan_id"]
            if compare_vlan_members(data_config, switch_default_list[0]):
                continue
            else:
                real_diff_lines.append(line)
                continue

        gigabit_match_untagged = re.search(gigabit_untagged_regex, line)
        if gigabit_match_untagged and switch_git_untagged_list and len(switch_git_untagged_list) == 1:
            data_config = gigabit_match_untagged.groupdict()["vlan_id"]
            if compare_vlan_members(data_config, switch_git_untagged_list[0]):
                continue
            else:
                real_diff_lines.append(line)
                continue

        gigabit_match = re.search(gigabit_regex, line)
        if gigabit_match and switch_git_default_list and len(switch_git_default_list) == 1:
            data_config = gigabit_match.groupdict()["vlan_id"]
            if compare_vlan_members(data_config, switch_git_default_list[0]):
                continue
            else:
                real_diff_lines.append(line)
                continue
        
        if line not in des:
            # if 'protocol' in line:
            #     line = line.replace('protocol ', 'protocols ')
            #     if line in des:
            #         continue
            
            if '\"' in line:
                real_diff_lines.append(line)
                continue
            
            pre, seg, aft = line.rpartition(' ')
            
            bak_line = pre + seg + "\"" + aft + "\""
            if bak_line not in des:
                real_diff_lines.append(line)
    
    if len(real_diff_lines) > max_diff_num:
        return False, real_diff_lines
    
    return True, real_diff_lines


def flat_to_tree(cmds, prefix=0):
    if isinstance(cmds, str):
        cmds = cmds.split('\n')
    
    if prefix == 0:
        cmds = [cmd.replace('set', '').strip() for cmd in cmds]
    cur_key = ''
    suffixs = []
    cur_prefix = ' ' * prefix
    res = ''
    for cmd in cmds:
        key, _, suffix = cmd.partition(' ')
        if ':' in key:
            key_value, _, suffix = suffix.partition(' ')
            key = key + ' ' + key_value
        
        if key == cur_key:
            if suffix != '':
                suffixs.append(suffix)
        else:
            if cur_key != '' and suffixs:
                res += flat_to_tree(suffixs, prefix=prefix + 2) + cur_prefix + '}\n'
            
            cur_key = key
            if suffix == '':
                res += cur_prefix + cur_key + '\n'
            else:
                res += cur_prefix + cur_key + ' {\n'
            
            suffixs = [suffix] if suffix != '' else []
    
    if suffixs:
        res += flat_to_tree(suffixs, prefix=prefix + 2) + cur_prefix + '}\n'
    
    return res


def generate_jinja2_template(content, line_seg='\n', action='config'):
    str_template = ''
    lines = content.split(line_seg)
    # remove empty line
    lines = [re.split('\s+', line.strip()) for line in lines if line.strip() != ""]
    cur_line = []
    params = {}
    cur_path = []
    action = 'set ' if action == 'config' else 'delete '
    for i, line in enumerate(lines):
        line_str = ' '.join(line)
        line_template = action
        j = len(cur_line) - 1
        max_compare = min(j, len(line) - 1)
        line_start = compare_line_same(cur_line, line, max_compare)
        line_end = ''
        while j >= line_start:
            
            pop_str = cur_line.pop()
            cur_path_pop = cur_path.pop()
            if '{{' in pop_str and '}}' in pop_str:
                param_type = cur_path_pop[1]
                if 'map_list' in param_type or 'list' in param_type or 'range' in param_type:
                    str_template += '{% endfor %}\n'
            j -= 1
        
        if ':' in line_str:
            for k, leaf in enumerate(line[j + 1:]):
                if leaf.count(':') == 1:
                    name, _, param_type = leaf.partition(':')
                    if name == 'param':
                        cur_line.append('{{ ' + cur_path[j + k + 1][0] + ' }}')
                        continue
                    cur_path.append((name, param_type))
                    if 'map_list' in leaf:
                        paths = name[:-9].split('.')
                        pre_line = '{% for ' + paths[-1] + ' in ' + name + ' %}\n'
                        line_template = pre_line + line_template
                        leaf_template = '{{ %s }}' % (paths[-1] + '.' + paths[-1])
                        cur_line.append(leaf_template)
                        _update_map_tree(paths, params, paths[-1], {"type": param_type,
                                                                    "children": {
                                                                        paths[-1]: {
                                                                            "type": param_type[:-9]
                                                                        }
                                                                    }}, cur_line, line)
                    elif 'list' in leaf:
                        paths = name[:-5].split('.')
                        pre_line = '{% for ' + paths[-1] + ' in ' + name + ' %}\n'
                        line_template = pre_line + line_template
                        leaf_template = '{{ %s }}' % paths[-1]
                        cur_line.append(leaf_template)
                        _update_map_tree(paths, params, paths[-1], {"type": param_type}, cur_line, line)
                    elif 'range' in leaf:
                        paths = name[:-5].split('.')
                        pre_line = '{% for ' + paths[-1] + ' in ' + name + ' %}\n'
                        line_template = pre_line + line_template
                        leaf_template = '{{ %s }}' % paths[-1]
                        cur_line.append(leaf_template)
                        _update_map_tree(paths, params, paths[-1], {"type": param_type}, cur_line, line)
                    else:
                        paths = name.split('.')
                        leaf_template = '{{ %s }}' % name
                        cur_line.append(leaf_template)
                        _update_map_tree(paths, params, paths[-1], {"type": param_type}, cur_line, line)
                else:
                    cur_line.append(leaf)
                    cur_path.append('leaf')
            # print params
            line_template, line_end_leaf = _check_parent_map_list_pop(line_template, cur_line, cur_path)
            line_end += line_end_leaf
            line_template += ' '.join(cur_line)
            line_template += line_end
        
        else:
            line_template += line_str
            cur_line = line
            for leaf_lin in line:
                cur_path.append('leaf')
        
        str_template += line_template + '\n'
    # print params
    
    j = len(cur_line) - 1
    while j >= 0:
        pop_str = cur_line.pop()
        cur_path_pop = cur_path.pop()
        if '{{' in pop_str and '}}' in pop_str:
            param_type = cur_path_pop[1]
            if 'map_list' in param_type or 'list' in param_type or 'range' in param_type:
                str_template += '{% endfor %}\n'
        j -= 1
    return str_template, params


def _check_parent_map_list_pop(line_template, cur_line, cur_path):
    line_end = ''
    cur_map_list_params = [path[0][:-9].split('.')[-1] for path in cur_path if
                           type(path) != str and path[1].endswith('_map_list')]
    for param_name in cur_line:
        if '{{' not in param_name or '}}' not in param_name or '.' not in param_name:
            continue
        map_path = param_name.strip('{ }').split('.')[:-1]
        path_len = len(map_path)
        reversed_map_path = list(reversed(map_path))
        for i, path in enumerate(reversed_map_path):
            if i < path_len - 1:
                prefix = reversed_map_path[i + 1] + '.' + path
            else:
                prefix = path
            if path not in cur_map_list_params and prefix + '_map_list' not in line_template:
                line_template = '{% for ' + path + ' in ' + path + '_map_list %}\n' + line_template
                line_end += '{% endfor %}\n'
    if line_end != '':
        line_end = '\n' + line_end
    return line_template, line_end


def generate_jinja2_template2(content, line_seg='\n', action='config'):
    str_template = ''
    lines = content.split(line_seg)
    # remove empty line
    lines = [line.strip().split(' ') for line in lines if line.strip() != ""]
    cur_line = []
    params = {}
    cur_path = []
    action = 'set ' if action == 'config' else 'delete '
    for i, line in enumerate(lines):
        line_str = ' '.join(line)
        line_template = action
        j = len(cur_line) - 1
        max_compare = min(j, len(line) - 1)
        line_start = compare_line_same(cur_line, line, max_compare)
        while j >= line_start:
            # pop previous status
            cur_line.pop()
            cur_path.pop()
            j -= 1
        
        if ':' in line_str:
            for k, leaf in enumerate(line[j + 1:]):
                if leaf.count(':') == 1:
                    name, _, param_type = leaf.partition(':')
                    if name == 'param':
                        cur_line.append('{{ ' + cur_path[j + k + 1][0] + ' }}')
                        continue
                    cur_path.append((name, param_type))
                    if 'map_list' in leaf:
                        paths = name[:-9].split('.')
                        pre_line = '{% for ' + paths[-1] + ' in ' + name + ' %}\n'
                        line_template = pre_line + line_template
                        leaf_template = '{{ %s }}' % (paths[-1] + '.' + paths[-1])
                        cur_line.append(leaf_template)
                        _update_map_tree(paths, params, paths[-1], {"type": param_type,
                                                                    "children": {
                                                                        paths[-1]: {
                                                                            "type": param_type[:-9]
                                                                        }
                                                                    }}, cur_line)
                    
                    elif 'list' in leaf:
                        paths = name[:-5].split('.')
                        pre_line = '{% for ' + paths[-1] + ' in ' + name + ' %}\n'
                        line_template = pre_line + line_template
                        leaf_template = '{{ %s }}' % paths[-1]
                        cur_line.append(leaf_template)
                        _update_map_tree(paths, params, paths[-1], {"type": param_type}, cur_line)
                    
                    elif 'range' in leaf:
                        paths = name[:-5].split('.')
                        pre_line = '{% for ' + paths[-1] + ' in ' + name + ' %}\n'
                        line_template = pre_line + line_template
                        leaf_template = '{{ %s }}' % paths[-1]
                        cur_line.append(leaf_template)
                        _update_map_tree(paths, params, paths[-1], {"type": param_type}, cur_line)
                    else:
                        paths = name.split('.')
                        leaf_template = '{{ %s }}' % name
                        cur_line.append(leaf_template)
                        _update_map_tree(paths, params, paths[-1], {"type": param_type}, cur_line)
                else:
                    cur_line.append(leaf)
                    cur_path.append('leaf')
            # print params
            line_template += ' '.join(cur_line)
        
        else:
            line_template += line_str
            cur_line = line
            for leaf_lin in line:
                cur_path.append('leaf')
        
        str_template += line_template + '\n'
    
    return str_template, params


def compare_line_same(src, dest, j):
    i = 0
    while i <= j:
        if ':' in dest[i]:
            if '_map_list' in dest[i]:
                param_name = dest[i].split(':')[0].rstrip('_map_list')
                param_name = param_name + '.' + param_name
            elif '_list' in dest[i]:
                param_name = dest[i].split(':')[0].rstrip('_list')
                param_name = param_name
            else:
                param_name = dest[i].split(':')[0]
            if param_name != src[i].strip('{ }'):
                return i
            i += 1
            continue
        if dest[i] != src[i]:
            return i
        i += 1
    return i


def _update_map_tree(paths, params, name, value, lines, line):
    def update_value():
        node = general_db.get_clinode_param_check(lines, line)
        default = node.default if node.default else ''
        check = node.param_check if node.param_check else ''
        description = node.description if node.description else ''
        if value['type'].endswith('_map_list'):
            value['children'][name].update(param_default=default, param_check=check, description=description)
        else:
            value.update(param_default=default, param_check=check, description=description)
    
    if len(paths) == 1:
        if name not in params:
            update_value()
            params[name] = value
        return
    
    index = 1
    tmp_params = params[paths[0]]
    while index < len(paths) - 1:
        key = paths[index]
        if key in tmp_params['children']:
            tmp_params = tmp_params['children'][key]
            index += 1
            continue
    
    if name not in tmp_params['children']:
        update_value()
        tmp_params['children'][name] = value


def parse_form_params(form_params):
    parsed = {}
    for key, value in form_params.items():
        if '.' not in key and '[' not in key:
            parsed[key] = value
            continue
        
        paths = key.split('.')
        tmp_parsed = parsed
        for path in paths[:-1]:
            name, _, num = path.rstrip(']').partition('[')
            if num and num != '':
                num = int(num)
                tmp_parsed[name] = tmp_parsed[name] if name in tmp_parsed else []
                l = len(tmp_parsed[name])
                while l <= num:
                    tmp_dict = {}
                    tmp_parsed[name].append(tmp_dict)
                    l += 1
                
                tmp_parsed = tmp_parsed[name][num]
            
            else:
                if name not in tmp_parsed:
                    tmp_parsed[name] = {}
                tmp_parsed = tmp_parsed[name]
        
        last = paths[-1]
        name, _, num = last.rstrip(']').partition('[')
        if num and num != '':
            num = int(num)
            tmp_parsed[name] = tmp_parsed[name] if name in tmp_parsed else []
            l = len(tmp_parsed[name])
            while l < num:
                tmp_parsed[name].append(None)
                l += 1
            tmp_parsed[name].append(value)
        else:
            tmp_parsed[name] = value
    _clear_none(parsed)
    return parsed


def _clear_none(parsed_params):
    for key, value in parsed_params.items():
        if type(value) == list:
            l = len(value)
            i = 0
            while i < l:
                if not value[i]:
                    del value[i]
                    i -= 1
                    l -= 1
                i += 1
        if type(value) == map:
            _clear_none(value)


def mask_key_configuration(config):
    config = key_reg.sub('key ***', config)
    config = password_reg.sub('password ***', config)
    return config


def mask_key_configuration_list(config_list):
    for i in range(len(config_list)):
        if config_list[i]:
            config_list[i] = key_reg.sub('key ***', config_list[i])
            config_list[i] = password_reg.sub('password ***', config_list[i])
    return config_list


if __name__ == '__main__':
    from server import cfg
    from server.db.models.inventory import Switch
    
    cfg.CONF(default_config_files=['../automation.ini'])
    # text = """vlans vlan-id data_vlan_map_list:uint_map_list X
    #         vlans vlan-id  data_vlan_map_list:uint_map_list description data_vlan.description:text X
    #         vlans vlan-id  data_vlan_map_list:uint_map_list l3-interface data_vlan.l3_interface:text X
    #         vlan-interface interface data_vlan.vlan_vif:text vif data_vlan.vlan_vif:text address data_vlan.vlan_vif_ipv4:IPv4 prefix-length data_vlan.vlan_vif_ipv4_prefix:int X
    #         vlan-interface interface data_vlan.vlan_vif:text vif data_vlan.vlan_vif:text description "Data Vlan" X
    #         vlan-interface interface data_vlan.vlan_vif:text vif data_vlan.vlan_vif:text address data_vlan.vlan_vif_ipv6:IPv6 prefix-length data_vlan.vlan_vif_ipv6_prefix:int X
    #         interface gigabit-ethernet port_name_list:te-x/x/x_list family ethernet-switching port-mode trunk X
    #         interface gigabit-ethernet port_name_list:te-x/x/x_list family ethernet-switching vlan members data_vlan_map_list:uint_map_list X
    #         protocols static route 0::0/0 next-hop ipv6_next_hop:IPv4 X """
    #     text = """vlans vlan-id 6 X vlans vlan-id param:uint description description:enum[1,2,3,4,5,6] X vlans vlan-id param:uint l3-interface l3_interface:text X vlans vlan-id param:uint vlan-name description:enum[1,2,3,4,5,6] X """
    #     text = """vlans vlan-id vlan_map_list:uint_map_list X
    # vlans vlan-id vlan_map_list:uint_map_list description vlan.description:text X
    # vlans vlan-id vlan_map_list:uint_map_list l3-interface vlan.l3_interface:text X
    # vlans vlan-id vlan_map_list:uint_map_list vlan-name vlan.vlan_name:text X
    # vlans vlan-id data_vlan_map_list:uint_map_list X
    # vlans vlan-id data_vlan_map_list:uint_map_list l3-interface data_vlan.l3_interface1:text X
    # vlans vlan-id data_vlan_map_list:uint_map_list vlan-name data_vlan.vlan_name1:text X
    # vlans vlan-id data_vlan_map_list:uint_map_list description data_vlan.description2:text X
    # interface gigabit-ethernet gigabit_ethernet_list:te-x/x/x_list family ethernet-switching vlan members data_vlan_map_list:uint_map_list X
    #     """
    text = """vlan-interface interface interface:text vif vif:text address address:IPv4 prefix-length prefix_length:int X """
    template, params = generate_jinja2_template(text, line_seg='X')
    print(template)
    print(params)
