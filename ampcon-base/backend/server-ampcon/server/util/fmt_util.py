import json
import logging
import time
import traceback

from server.db.models import inventory
from server.db.models.otn import OtnTempData, OtnDeviceBasic, FmtDeviceBasic, FmtDeviceCards, DcsDeviceBasic, DcsDeviceCards
from server.util.redis_distributed_lock import DistributedLock
from server.util.socket_client import SocketClient
import socket
import copy
import re
from server import cfg

invent_db = inventory.inven_db
LOG = logging.getLogger(__name__)

# 包含FMT和DCS设备类型
FMT_CONFIG_MODEL = {
    "FMT": {
        "nmuMapping": {
            "SN": "SN",
            "Model": "DTP",
            "Production date": "MD",
            "Software version": "SV",
            "Hardware version": "HV",
            "IP": "IP",
            "Mask": "MSK",
            "Gateway": "GW",
            "Mac": "MAC",
            "Key": "KEY",
            "BZC": "BZC",
            "BZS": "BZS",
            "FNC": "FNC",
            "FNS": "FNS",
            "PWR": "PWR"
        },
        "boardMapping": {
            "0": {
                "boardType": "NMU",
                "analysisMode": 0,
                "basicInfo": {
                    "Board model": "DTP",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "config": {
                        "trap_address": "SNMPMIP"
                    }
                }
            },
            "0101": {
                "boardType": "OLP",
                "analysisMode": 2,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "Power": {
                            "r1": "R1_P",
                            "r2": "R2_P",
                            "tx": "TX_P",
                            "ls": "LS_P",
                            "alarm": "ALM"
                        }
                    },
                    "config": {
                        "Alarm Limit": {
                            "r1": "R1_AP",
                            "r2": "R2_AP",
                            "tx": "TX_AP",
                            "ls": "LS_AP"
                        },
                        "Switch Parameter": {
                            "r1_switch_limit": "R1_SP",
                            "r2_switch_limit": "R2_SP",
                            "return_delay": "Q",
                            "switch_delay": "R"
                        },
                        "Working Parameter": {
                            "work_mode": "M",
                            "work_route": "S",
                            "return_mode": "ACC"
                        }
                    }
                }
            },
            "0102": {
                "boardType": "OLP",
                "analysisMode": 2,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "Power": {
                            "r1": "R1_P",
                            "r2": "R2_P",
                            "tx": "TX_P",
                            "ls": "LS_P",
                            "alarm": "ALM"
                        }
                    },
                    "config": {
                        "Alarm Limit": {
                            "r1": "R1_AP",
                            "r2": "R2_AP",
                            "tx": "TX_AP",
                            "ls": "LS_AP"
                        },
                        "Switch Parameter": {
                            "r1_switch_limit": "R1_SP",
                            "r2_switch_limit": "R2_SP",
                            "return_delay": "Q",
                            "switch_delay": "R"
                        },
                        "Working Parameter": {
                            "work_mode": "M",
                            "work_route": "S",
                            "return_mode": "ACC"
                        }
                    }
                }
            },
            # "1801": {
            #     "boardType": "DCM",
            #     "analysisMode": 1,
            #     "basicInfo": {
            #         "Board model": "DT",
            #         "Serial number": "SN",
            #         "Production date": "MD",
            #         "Hardware version": "HV",
            #         "Software version": "SV",
            #         "Temperature": "TMP"
            #     },
            #     "businessInfo": {
            #         "config": {
            #             "temperature": "TMP",
            #             "module_state": "STS",
            #             "tdc_value": "GSD",
            #             "tdc_setting_value": "GDS",
            #             "frequency_inter": "GCH",
            #             "tdc": "SDS",
            #             "frequency": "SCH"
            #         }
            #     }
            # },
            "2101": {
                "boardType": "TDCM",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "TMP"
                },
                "businessInfo": {
                    "config": {
                        "temperature": "TMP",
                        "module_state": "STS",
                        "tdc_value": "GSD",
                        "tdc_setting_value": "GDS",
                        "frequency_inter": "GCH",
                        "tdc": "SDS",
                        "frequency": "SCH"
                    }
                }
            },
            "2202": {
                "boardType": "OEO-100G",
                "analysisMode": 2,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "tx1": ["M@_TX1_POWER"],
                        "tx2": ["M@_TX2_POWER"],
                        "tx3": ["M@_TX3_POWER"],
                        "tx4": ["M@_TX4_POWER"],
                        "rx1": ["M@_RX1_POWER"],
                        "rx2": ["M@_RX2_POWER"],
                        "rx3": ["M@_RX3_POWER"],
                        "rx4": ["M@_RX4_POWER"],
                        "max_wavelength": ["M@_MAX"],
                        "min_wavelength": ["M@_MIN"],
                        "rate": ["M@_RATE"],
                        "tx_distance": ["M@_TD"],
                        "module_temp": ["M@_T"]
                    },
                    "config": {
                        "work_model_": ["M@_M"],
                        "control_mode_": ["M@_CONFIG"],
                        "alarm_limit_": ["M@_RX_ALARM"]
                    }
                }
            },
            "0301": {
                "boardType": "EDFA",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "State": {
                            "gain": "PGV",
                            "module_temperature": "MTV",
                            "supply_voltage": "TEC",
                            "work_model": "AGC",
                            "input_power": "PWI",
                            "output_power": "PWO",
                            "low_input_power": "PIA",
                            "low_output_power": "POA",
                            "input_warning_threshold": "PIA",
                            "output_warning_threshold": "POA",
                            "pin_alarm": "PIN",
                            "pout_alarm": "POU",
                            "mt_alarm": "MT",
                            "pt_alarm": "PT"
                        },
                        "Pump state": {
                            "pump1_work_current": "PPV",
                            "pump2_work_current": "PPV",
                            "pump1_power": "PPV",
                            "pump2_power": "PPV",
                            "pump1_temperature": "PTV",
                            "pump2_temperature": "PTV",
                            "pump1_cooling_electricity": "PIV",
                            "pump2_cooling_electricity": "PIV",
                            "upper_pump1_temperature": "PTU",
                            "upper_pump2_temperature": "PTU",
                            "low_pump1_temperature": "PTD",
                            "low_pump2_temperature": "PTD",
                            "upper_module_temperature": "MTU",
                            "low_module_temperature": "MTD"
                        },
                        "output_adjustment": "POV",
                        "pump2_state": "PSW"
                    },
                    "config": {
                        "Gain": {
                            "gain_adjustment": "PGV"
                        },
                        "Pump": {
                            "pump1_state": "PSW"
                        },
                        "rx_power_alarm_threshold": "PIA",
                        "tx_power_alarm_threshold": "POA"
                    }
                }
            },
            "0303": {
                "boardType": "DEDFA",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "State": {
                            "gain": "PGV",
                            "module_temperature": "MTV",
                            "supply_voltage": "TEC",
                            "work_model": "AGC",
                            "input_power1": "PWI#0",
                            "output_power1": "PWO#0",
                            "input_power2": "PWI#1",
                            "output_power2": "PWO#1",
                            "low_input_power": "PIA",
                            "low_output_power": "POA",
                            "input_warning_threshold": "PIA",
                            "output_warning_threshold": "POA",
                            "pin_alarm": "PIN",
                            "pout_alarm": "POU",
                            "mt_alarm": "MT",
                            "pt_alarm": "PT"
                        },
                        "Pump state": {
                            "pump1_work_current": "PPV",
                            "pump2_work_current": "PPV",
                            "pump1_power": "PPV",
                            "pump2_power": "PPV",
                            "pump1_temperature": "PTV",
                            "pump2_temperature": "PTV",
                            "pump1_cooling_electricity": "PIV",
                            "pump2_cooling_electricity": "PIV",
                            "upper_pump1_temperature": "PTU",
                            "upper_pump2_temperature": "PTU",
                            "low_pump1_temperature": "PTD",
                            "low_pump2_temperature": "PTD",
                            "upper_module_temperature": "MTU",
                            "low_module_temperature": "MTD"
                        },
                        "output_adjustment": "POV",
                        "pump2_state": "PSW"
                    },
                    "config": {
                        "Gain": {
                            "gain_adjustment": "PGV"
                        },
                        "Pump": {
                            "pump1_state": "PSW"
                        },
                        "rx_power_alarm_threshold": "PIA",
                        "tx_power_alarm_threshold": "POA"
                    }
                }
            },
            "0500": {
                "boardType": "OPD",
                "analysisMode": 2,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "power": ["CH@_P"],
                        "route_type": ["CH@_A"],
                        "channel_description": ["CH@_P"]
                    },
                    "config": {
                        "threshold_": ["CH@_S"],
                        "wavelength_": ["CH@_W"]
                    }
                }
            },
            "0501": {
                "boardType": "OPD",
                "analysisMode": 2,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "power": ["CH@_P"],
                        "route_type": ["CH@_S"]
                    },
                    "config": {
                        "wavelength_": ["CH@_W"]
                    }
                }
            },
            "0701": {
                "boardType": "OEO",
                "analysisMode": 2,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "wavelength": ["M@_W"],
                        "transmission_distance": ["M@_TD"],
                        "input_power": ["M@_RXP"],
                        "output_power": ["M@_TXP"],
                        "module_temperature": ["M@_T"],
                        "rate": ["M@_R"],
                        # "service_notes": ["M@_MS"]
                    },
                    "config": {
                        "control_mode_": ["M@_PC"],
                        "work_model_": ["M@_M"],
                        "input_alarm_threshold_": ["M@_RXA"]
                    }
                }
            },
            # "0801": {
            #     "boardType": "R/B",
            #     "analysisMode": 1,
            #     "basicInfo": {
            #         "Board model": "DT",
            #         "Serial number": "SN",
            #         "Production date": "MD",
            #         "Hardware version": "HV",
            #         "Software version": "SV",
            #         "Temperature": "MTV"
            #     },
            #     "businessInfo": {
            #     }
            # },
            "1605": {
                "boardType": "VOA",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "voa1_power": "CP1",
                        "voa1_attenuation": "CV1",
                        "voa2_power": "CP2",
                        "voa2_attenuation": "CV2",
                        "voa3_power": "CP3",
                        "voa3_attenuation": "CV3",
                        "voa4_power": "CP4",
                        "voa4_attenuation": "CV4"
                    },
                    "config": {
                        "voa1_power_configuration": "P1",
                        "voa1_attenuation_configuration": "V1",
                        "voa1_threshold": "RX1",
                        "voa1_work_mode": "M1",
                        "voa2_power_configuration": "P2",
                        "voa2_attenuation_configuration": "V2",
                        "voa2_threshold": "RX2",
                        "voa2_work_mode": "M2",
                        "voa3_power_configuration": "P3",
                        "voa3_attenuation_configuration": "V3",
                        "voa3_threshold": "RX3",
                        "voa3_work_mode": "M3",
                        "voa4_power_configuration": "P4",
                        "voa4_attenuation_configuration": "V4",
                        "voa4_threshold": "RX4",
                        "voa4_work_mode": "M4"
                    }
                }
            }
        }
    },
    "DCS": {
        "nmuMapping": {
            "SN": "SN",
            "Model": "DTP",
            "Production date": "MD",
            "Software version": "SV",
            "Hardware version": "HV",
            "IP": "IP",
            "Mask": "MSK",
            "Gateway": "GW",
            "Mac": "MAC",
            "Key": "KEY",
            "BZC": "BZC",
            "BZS": "BZS",
            "FNC": "FNC",
            "FNS": "FNS",
            "PWR": "PWR"
        },
        "boardMapping": {
            "0": {
                "boardType": "NMU",
                "analysisMode": 0,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "config": {
                        "trap_address": "SNMPMIP"
                    }
                }
            },
            "8001": {
                "boardType": "4M4",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "existence": ["S@"],
                        "wavelength": ["WAVE@"],
                        "rate": ["RATE@"],
                        "temperature": ["T@"],
                        "transmission_distance": ["TD@"],
                        "rx_power": ["RX@"],
                        "tx_power": ["TX@"],
                        "rx_alarm": ["RXPA@"],
                        "tx_alarm": ["TXPA@"],
                        "temperature_alarm": ["TA@"],
                        "OSNR": ["OSNR@"],
                        "dispersion": ["DIS@"],
                        "module_type": ["TYPE@"],
                        "pre_fec": ["PREBER@"],
                        "post_fec": ["POSTBER@"],
                    },
                    "config": {
                        "tx_switch_": ["TXC@"],
                        "work_mode_": ["M@"],
                        "modulation_": ["PM@"],
                        "rx_alarm_threshold_": ["RXA@"],
                        "fec_switch_": ["FEC@"],
                        "business_mode_": ["OTGE@"],
                    }
                }
            },
            "8601": {
                "boardType": "4T4",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "existence": ["S@"],
                        "wavelength": ["WAVE@"],
                        "rate": ["RATE@"],
                        "temperature": ["T@"],
                        "transmission_distance": ["TD@"],
                        "rx_power": ["RX@"],
                        "tx_power": ["TX@"],
                        "rx_alarm": ["RXPA@"],
                        "tx_alarm": ["TXPA@"],
                        "temperature_alarm": ["TA@"],
                        "OSNR": ["OSNR@"],
                        "dispersion": ["DIS@"],
                        "module_type": ["TYPE@"],
                        "pre_fec": ["PREBER@"],
                        "post_fec": ["POSTBER@"],
                    },
                    "config": {
                        "tx_switch_": ["TXC@"],
                        "work_mode_": ["M@"],
                        "modulation_": ["PM@"],
                        "rx_alarm_threshold_": ["RXA@"],
                        "fec_switch_": ["FEC@"],
                        "business_mode_": ["OTGE@"],
                    }
                }
            },
            "6601": {
                "boardType": "NMU",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "power_state1": "PN#0",
                        "power_state2": "PN#1",
                        "supply_mode1": "MP#0",
                        "supply_mode2": "MP#1",
                        "max_power1": "MRXP#0",
                        "max_power2": "MRXP#1",
                        "input_current1": "RXA#0",
                        "input_current2": "RXA#1",
                        "output_current1": "TXA#0",
                        "output_current2": "TXA#1",
                        "input_voltage1": "RXV#0",
                        "input_voltage2": "RXV#1",
                        "output_voltage1": "TXV#0",
                        "output_voltage2": "TXV#1",
                        "power_temperature1": "TE#0",
                        "power_temperature2": "TE#1",
                        "fan_state1": "BN#0",
                        "fan_state2": "BN#1",
                    },
                    "config": {
                        "power_switch1": "PS#0",
                        "power_switch2": "PS#1",
                        "fan_switch": "MF",
                        "fan_speed": "REV",
                    }
                }
            },
            "7601": {
                "boardType": "FAN",
                "analysisMode": 1,
                "basicInfo": {
                    "Board model": "DT",
                    "Serial number": "SN",
                    "Production date": "MD",
                    "Hardware version": "HV",
                    "Software version": "SV",
                    "Temperature": "MTV"
                },
                "businessInfo": {
                    "query": {
                        "rpm": "RPM",
                        "rpm2": "RPM2",
                    },
                    "config": {
                        "work_speed": "M",
                    }
                }
            }
        }
    },
    "DCP": {}
}

CONFIG_KEY_PORT_MAP = {
    "a1": "01", "a2": "02", "b1": "03", "b2": "04", "c1": "05", "c2": "06", "d1": "07", "d2": "08",
    "1": "01", "2": "02", "3": "03", "4": "04", "5": "05", "6": "06", "7": "07", "8": "08", "9": "09",
    "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"
}

SERIES2MODEL = {
    2: "FMT",
    3: "DCS"
}


def beat_sync_fmt_device_info_all():
    LOG.info('Start sync all fmt device info.')

    # 获取所有fmt设备，series为2
    db_session = invent_db.get_session()
    device_info_list = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.series == 2).all()
    for device_info in device_info_list:
        result, NMU = get_device_info_by_ip(device_info.ip, device_info.id, "FMT")
        fmtTempData = OtnTempData(id=device_info.id, ip=device_info.ip, nmu=json.dumps(NMU),
                                  data=json.dumps(result))
        db_session.merge(fmtTempData)

    LOG.info('End sync all fmt device info.')

def beat_sync_dcs_device_info_all():
    LOG.info('Start sync all dcs device info.')

    # 获取所有dcs设备，series为3
    db_session = invent_db.get_session()
    device_info_list = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.series == 3).all()
    for device_info in device_info_list:
        result, NMU = get_device_info_by_ip(device_info.ip, device_info.id, "DCS")
        dcsTempData = OtnTempData(id=device_info.id, ip=device_info.ip, nmu=json.dumps(NMU),
                                  data=json.dumps(result))
        db_session.merge(dcsTempData)

    LOG.info('End sync all dcs device info.')


def beat_sync_otn_device_info_single(id, ip):
    result, otnDeviceBasic = get_device_info(id, ip)
    otnData = result[0]
    NMU = result[1]
    if otnData is None:
        return None
    db_session = invent_db.get_session()
    with db_session.begin():
        otnTempData = OtnTempData(id=otnDeviceBasic.id, ip=otnDeviceBasic.ip, nmu=json.dumps(NMU),
                                  data=json.dumps(otnData))
        db_session.merge(otnTempData)

    return get_device_detail(otnDeviceBasic.id, SERIES2MODEL[otnDeviceBasic.series])


def subscribe_trap_message(ip, device_type="FMT"):
    # 从全局配置中获取控制器IP，然后下发设备，设置trap接收地址
    controllerIp = cfg.CONF.global_ip
    data, errorCode, errorMsg = modify_config(ip, "0", "trap_address", controllerIp, device_type)
    LOG.info(
        f"deviceIp:{ip} , controllerIp:{controllerIp} , result:{data} , errorCode:{errorCode}, errorMsg:{errorMsg}.")


def get_device_detail(deviceId, device_type="FMT"):
    # 从otn_temp_data、fmt_device_cards表中取出设备所有数据
    db_session = invent_db.get_session()
    otnTempData = db_session.query(OtnTempData).filter(OtnTempData.id == deviceId).first()
    if device_type == "FMT":
        cardsData = db_session.query(FmtDeviceCards).filter(FmtDeviceCards.device_id == deviceId).all()
    elif device_type == "DCS":
        cardsData = db_session.query(DcsDeviceCards).filter(DcsDeviceCards.device_id == deviceId).all()

    otnTempDataResult = otnTempData.data

    if otnTempDataResult is None or len(otnTempDataResult) == 0 or otnTempDataResult == '""':
        return ""

    resultData = json.loads(otnTempDataResult)
    if resultData.get("boardInfos") is not None:
        boardInfos = resultData["boardInfos"]
        for item in cardsData:
            slotIndex = item.slot_index
            card = get_card_by_slot_index(boardInfos, slotIndex)
            if card is None:
                LOG.info(f"Slot index:{slotIndex} have no card.")
                continue
            card["cardId"] = item.card_id
            card["Board model"] = item.model
            card["Serial number"] = item.serial_number
            card["Production date"] = item.production_date
            card["Hardware version"] = item.hardware_version
            card["Software version"] = item.software_version
            card["Module temperature"] = item.temperature
            card["ports_data"] = json.loads(item.ports_data)
            # 原始数据结构，删除不需要字段，新增需要的card的port内容
            if card.get("basicInfo") is not None:
                del card["basicInfo"]
            # if card.get("businessInfo") is not None:
            #     del card["businessInfo"]

    return resultData


def get_card_by_slot_index(boardInfos, slotIndex):
    for item in boardInfos:
        if item["slotIndex"] == slotIndex:
            return item
    return None


def get_device_info(id, ip=None):
    db_session = invent_db.get_session()
    if ip is not None:
        LOG.info("device ip:" + ip)
        otnDeviceBasic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).first()
        if otnDeviceBasic is None:
            return [None, None], None
        return get_device_info_by_ip(ip, otnDeviceBasic.id, SERIES2MODEL[otnDeviceBasic.series]), otnDeviceBasic

    LOG.info("device id:" + id)
    otnDeviceBasic = db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.id == id).first()
    if otnDeviceBasic is None:
        return [None, None], None

    LOG.info(str(otnDeviceBasic))
    ip = otnDeviceBasic.ip
    return get_device_info_by_ip(ip, id, SERIES2MODEL[otnDeviceBasic.series]), otnDeviceBasic


def get_device_info_by_ip(ip, id, device_type="FMT"):
    # 获取锁并查询设备基本信息
    startTime = time.time()
    distributed_lock = DistributedLock(ip)
    if distributed_lock.acquire():
        socket_client = None
        try:
            LOG.info("get lock total time:" + str(time.time() - startTime))
            socket_client = SocketClient(ip, time_out=5, model=device_type)
            socket_client.create_socket_client()
            recData, NMU = get_all_board_info(socket_client)
            LOG.info("get data total time:" + str(time.time() - startTime))
            if recData["boardInfos"] != []:
                update_reachable_status(ip, 1)
            else:
                update_reachable_status(ip, 0)
            if device_type == "FMT":
                update_fmt_device_info(id, recData)
            elif device_type == "DCS":
                update_dcs_device_info(id, recData)
            return recData, NMU
        except socket.timeout:
            LOG.error("socket timed out!")
            LOG.error(traceback.format_exc())
        except Exception as e:
            LOG.error(f"Exception:{e}")
            LOG.error(traceback.format_exc())
        finally:
            if socket_client is not None:
                socket_client.close_socket_client()
            distributed_lock.release()
            LOG.info("end work, release lock!")
    else:
        LOG.error("get lock fail!")
    update_reachable_status(ip, 0)
    if device_type == "FMT":
        update_fmt_device_info(id, None)
    elif device_type == "DCS":
        update_dcs_device_info(id, None)
    return "", ""


def update_fmt_device_info(id, recData):
    db_session = invent_db.get_session()
    with db_session.begin():
        if recData is not None:
            # 更新fmt设备表、单板表
            fmtDeviceBasic = FmtDeviceBasic(device_id=id, serial_number=recData.get("SN"),
                                            slot_number=recData.get("Slot number"),
                                            mask=recData.get("Mask"),
                                            gateway=recData.get("Gateway"),
                                            mac=recData.get("Mac"),
                                            key_lock_status=recData.get("Key"),
                                            bzc_status=recData.get("BZC"),
                                            bzs_status=recData.get("BZS"),
                                            fnc_status=recData.get("FNC"),
                                            fns_status=recData.get("FNS"),
                                            pwr_status=recData.get("PWR"),
                                            production_date=recData.get("Production date"),
                                            hardware_version=recData.get("Hardware version"),
                                            software_version=recData.get("Software version"),
                                            firmware_version=recData.get("Hardware version"),
                                            temperature="")
            fmtDeviceCardList = []
            lengthCards = len(recData.get("boardInfos"))
            if lengthCards > 0:
                # 取历史同步数据，获得各槽位上单板数据
                fmtCardsList = db_session.query(FmtDeviceCards).filter(FmtDeviceCards.device_id == id).all()
                for index in range(lengthCards):
                    cardInfo = recData.get("boardInfos")[index]
                    cardDetail = cardInfo.get("basicInfo")
                    boardType = cardInfo.get("boardType")
                    temperature = cardDetail.get("Temperature")
                    if temperature is None:
                        temperature = ""
                    if boardType == "DCM" or boardType == "TDCM":
                        if temperature:
                            temperature = f"{float(temperature.lstrip('0')) / 10:.1f}"
                        else:
                            temperature = "0.00"
                    portsData = json.dumps(build_fmt_ports_data(boardType, fmtCardsList, recData, index))
                    slotIndex = cardInfo.get("slotIndex")
                    fmtDeviceCard = FmtDeviceCards(card_id=f"{id}_{slotIndex}", device_id=id,
                                                   slot_index=slotIndex,
                                                   type=boardType, model=cardDetail.get("Board model"),
                                                   serial_number=cardDetail.get("Serial number"),
                                                   production_date=cardDetail.get("Production date"),
                                                   hardware_version=cardDetail.get("Hardware version"),
                                                   software_version=cardDetail.get("Software version"),
                                                   firmware_version=cardDetail.get("Hardware version"),
                                                   temperature=temperature,
                                                   ports_data=portsData)
                    fmtDeviceCardList.append(fmtDeviceCard)

            # 根据id清空fmt设备表数据，会级联删除单板表中设备对应数据
            db_session.query(FmtDeviceBasic).filter(FmtDeviceBasic.device_id == id).delete()
            db_session.add(fmtDeviceBasic)
            if len(fmtDeviceCardList) > 0:
                for item in fmtDeviceCardList:
                    db_session.add(item)

def update_dcs_device_info(id, recData):
    db_session = invent_db.get_session()
    with db_session.begin():
        if recData is not None:
            dcsDeviceBasic = DcsDeviceBasic(device_id=id, serial_number=recData.get("SN"),
                                            slot_number=recData.get("Slot number"),
                                            mask=recData.get("Mask"),
                                            gateway=recData.get("Gateway"),
                                            mac=recData.get("Mac"),
                                            key_lock_status=recData.get("Key"),
                                            bzc_status=recData.get("BZC"),
                                            bzs_status=recData.get("BZS"),
                                            fnc_status=recData.get("FNC"),
                                            fns_status=recData.get("FNS"),
                                            pwr_status=recData.get("PWR"),
                                            production_date=recData.get("Production date"),
                                            hardware_version=recData.get("Hardware version"),
                                            software_version=recData.get("Software version"),
                                            firmware_version=recData.get("Hardware version"),
                                            temperature="")
            dcsDeviceCardList = []
            lengthCards = len(recData.get("boardInfos"))
            if lengthCards > 0:
                # 取历史同步数据，获得各槽位上单板数据
                dcsCardsList = db_session.query(DcsDeviceCards).filter(DcsDeviceCards.device_id == id).all()
                for index in range(lengthCards):
                    cardInfo = recData.get("boardInfos")[index]
                    cardDetail = cardInfo.get("basicInfo")
                    boardType = cardInfo.get("boardType")
                    temperature = cardDetail.get("Temperature")
                    portsData = json.dumps(build_dcs_ports_data(boardType, dcsCardsList, recData, index))
                    slotIndex = cardInfo.get("slotIndex")
                    dcsDeviceCard = DcsDeviceCards(card_id=f"{id}_{slotIndex}", device_id=id,
                                                   slot_index=slotIndex,
                                                   type=boardType, model=cardDetail.get("Board model"),
                                                   serial_number=cardDetail.get("Serial number"),
                                                   production_date=cardDetail.get("Production date"),
                                                   hardware_version=cardDetail.get("Hardware version"),
                                                   software_version=cardDetail.get("Software version"),
                                                   firmware_version=cardDetail.get("Hardware version"),
                                                   temperature=temperature,
                                                   ports_data=portsData)
                    dcsDeviceCardList.append(dcsDeviceCard)

            # 根据id清空dcs设备表数据，会级联删除单板表中设备对应数据
            db_session.query(DcsDeviceBasic).filter(DcsDeviceBasic.device_id == id).delete()
            db_session.add(dcsDeviceBasic)
            if len(dcsDeviceCardList) > 0:
                for item in dcsDeviceCardList:
                    db_session.add(item)


def build_fmt_ports_data(boardType, fmtCardsList, recData, index):
    portsData = dict()
    cardInfo = recData.get("boardInfos")[index]
    slot_index = cardInfo.get("slotIndex")
    businessInfo = cardInfo.get("businessInfo")
    oldPortsData = get_old_card_ports_data(fmtCardsList, slot_index)

    queryData = None
    configData = None
    if businessInfo.get("query") is not None:
        queryData = businessInfo.get("query")
    if businessInfo.get("config") is not None:
        configData = businessInfo["config"]

    # 不同单板端口数据类型不一致
    if boardType == "NMU":
        slot_number = recData.get("Slot number")
        for item in range(slot_number):
            indexNo = str(item + 1)
            portsData[indexNo] = {"Slot No": indexNo, "Preconfigured boards": "", "Actual board": "",
                                  "Board status": "", "Slot Note": handle_note(indexNo, "Slot Note", oldPortsData)}

        # 赋值-当前单板类型
        lengthCards = len(recData.get("boardInfos"))
        if lengthCards > 0:
            for index in range(lengthCards):
                cardInfo = recData.get("boardInfos")[index]
                slot_index = cardInfo.get("slotIndex")
                if slot_index == 0:
                    continue
                boardType = cardInfo.get("boardType")
                portsData[str(slot_index)]["Actual board"] = boardType
                if boardType:
                    portsData[str(slot_index)]["Board status"] = "Reign"

        # 赋值-历史单板类型
        for item in fmtCardsList:
            slotIndex = item.slot_index
            if slotIndex == 0:
                continue
            portsData[str(slotIndex)]["Preconfigured boards"] = item.type

    elif boardType == "EDFA":
        stateData = queryData["State"]
        portsData["1"] = {"No": "1", "Name": "PORT-1-PIN", "EDFA Gain Value": stateData["gain"],
                          "Expected EDFA Gain": configData["Gain"]["gain_adjustment"],
                          "VOA Attenuation Value": "",
                          "VOA Attenuation Expected": "",
                          "Gain Slope": "",
                          "Input Optical Power": stateData["input_power"],
                          "Output Optical Power": "",
                          "Input Warning Threshold": stateData["input_warning_threshold"],
                          "Output Warning Threshold":"",
                          "Port Note": handle_note("1", "Port Note", oldPortsData)}

        portsData["2"] = {"No": "2", "Name": "PORT-2-POUT", "EDFA Gain Value": stateData["gain"],
                          "Expected EDFA Gain": configData["Gain"]["gain_adjustment"],
                          "VOA Attenuation Value": "",
                          "VOA Attenuation Expected": "",
                          "Gain Slope": "",
                          "Input Optical Power": "",
                          "Output Optical Power": stateData["output_power"],
                          "Input Warning Threshold": "",
                          "Output Warning Threshold": stateData["output_warning_threshold"],
                          "Port Note": handle_note("2", "Port Note", oldPortsData)}
    elif boardType == "DEDFA":
        stateData = queryData["State"]
        portsData["1"] = {"No": "1", "Name": "PORT-1-PIN1",
                          "Input Optical Power": stateData["input_power1"],
                          "Output Optical Power": "",
                          "Port Note": handle_note("1", "Port Note", oldPortsData)}
        portsData["2"] = {"No": "2", "Name": "PORT-2-POUT1",
                          "Input Optical Power": "",
                          "Output Optical Power": stateData["output_power1"],
                          "Port Note": handle_note("2", "Port Note", oldPortsData)}
        portsData["3"] = {"No": "3", "Name": "PORT-3-PIN2",
                          "Input Optical Power": stateData["input_power2"],
                          "Output Optical Power": "",
                          "Port Note": handle_note("3", "Port Note", oldPortsData)}
        portsData["4"] = {"No": "4", "Name": "PORT-4-POUT2",
                          "Input Optical Power": "",
                          "Output Optical Power": stateData["output_power2"],
                          "Port Note": handle_note("4", "Port Note", oldPortsData)}
    elif boardType == "OLP":
        workStatus = configData["Working Parameter"]["work_route"]
        if workStatus == "1":
            workStatus = "PRIMARY"
        else:
            workStatus = "SECONDARY"
        powerData = queryData["Power"]
        thresholdData = configData["Alarm Limit"]
        portsData["1"] = {"No": "1", "Name": "PORT-1-APSP-IN", "Work Status": workStatus,
                          "Optical Power": powerData["r1"], "Optical Power Threshold": thresholdData["r1"],
                          "Protection Group": "APS"}
        portsData["2"] = {"No": "2", "Name": "PORT-2-APSS-IN", "Work Status": workStatus,
                          "Optical Power": powerData["r2"], "Optical Power Threshold": thresholdData["r2"],
                          "Protection Group": "APS"}
        portsData["3"] = {"No": "3", "Name": "PORT-3-APSC-OUT", "Work Status": workStatus,
                          "Optical Power": powerData["tx"], "Optical Power Threshold": thresholdData["tx"],
                          "Protection Group": "APS"}
    elif boardType == "OEO":
        portsName = ["A1", "A2", "B1", "B2", "C1", "C2", "D1", "D2"]
        for index in range(8):
            indexNo = str(index + 1)
            portsData[indexNo] = {"No": indexNo, "Name": portsName[index],
                                  "Module Wavelength": (get_list_value(queryData["wavelength"], index)
                                                        if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  "Input Optical Power": (get_list_value(queryData["input_power"], index)
                                                          if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  "Output Optical Power": (get_list_value(queryData["output_power"], index)
                                                           if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  "Input Alarm Threshold": (get_list_value(configData["input_alarm_threshold_"], index)
                                                            if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  "Transmission Distance": (get_list_value(queryData["transmission_distance"],index)
                                                            if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  "Work Mode": (get_list_value(configData["control_mode_"], index)
                                                if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  "Module Temperature": (get_list_value(queryData["module_temperature"], index)
                                                         if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  "Rate": (get_list_value(queryData["rate"], index)
                                           if get_list_value(queryData["wavelength"], index) and float(
                                      get_list_value(queryData["wavelength"], index)) > 0 else ""),
                                  # "Module State": "Present",
                                  "Service Notes": handle_note(indexNo, "Service Notes", oldPortsData)}
    elif boardType == "VOA":
        for index in range(4):
            indexNo = str(index + 1)
            portsData[indexNo] = {"No": indexNo, "Name": f"VOA{indexNo}",
                                  "Work Mode": configData[f"voa{indexNo}_work_mode"],
                                  "Actual Power": queryData[f"voa{indexNo}_power"], "Expected Power"
                                  : configData[f"voa{indexNo}_power_configuration"],
                                  "Actual Attenuation": queryData[f"voa{indexNo}_attenuation"],
                                  "Expected Attenuation": configData[f"voa{indexNo}_attenuation_configuration"],
                                  "Threshold": configData[f"voa{indexNo}_threshold"]}
    elif boardType == "OPD":
        for index in range(16):
            indexNo = str(index + 1)
            portsData[indexNo] = {"No": indexNo, "Name": f"OPD-PORT{indexNo}",
                                  "Power": get_list_value(queryData["power"], index),
                                  "Route Type": get_list_value(queryData["route_type"], index),
                                  "Wavelength": get_list_value(configData["wavelength_"], index),
                                  "Port Note": handle_note(indexNo, "Port Note", oldPortsData)}
    else:
        pass
    return portsData

def build_dcs_ports_data(boardType, dcsCardsList, recData, index):
    portsData = dict()
    cardInfo = recData.get("boardInfos")[index]
    slot_index = cardInfo.get("slotIndex")
    businessInfo = cardInfo.get("businessInfo")
    basicInfo = cardInfo.get("basicInfo")
    oldPortsData = get_old_card_ports_data(dcsCardsList, slot_index)

    queryData = None
    configData = None
    if businessInfo.get("query") is not None:
        queryData = businessInfo.get("query")
    if businessInfo.get("config") is not None:
        configData = businessInfo["config"]

    # 不同单板端口数据类型不一致
    if boardType == "NMU":
        slot_number = recData.get("Slot number")
        for item in range(slot_number):
            indexNo = str(item + 1)
            portsData[indexNo] = {"slot_no": indexNo, "preconfigured_boards": "", "actual_board": "",
                                  "board_status": "", "slot_note": handle_note(indexNo, "slot_note", oldPortsData)}

        # 赋值-当前单板类型
        lengthCards = len(recData.get("boardInfos"))
        if lengthCards > 0:
            for index in range(lengthCards):
                cardInfo = recData.get("boardInfos")[index]
                slot_index = cardInfo.get("slotIndex")
                if slot_index == 0:
                    continue
                boardType = cardInfo.get("boardType")
                portsData[str(slot_index)]["actual_board"] = boardType
                if boardType:
                    portsData[str(slot_index)]["board_status"] = "Reign"

        # 赋值-历史单板类型
        for item in dcsCardsList:
            slotIndex = item.slot_index
            if slotIndex == 0:
                continue
            portsData[str(slotIndex)]["preconfigured_boards"] = item.type

    elif boardType == "4M4":
        portsName = ["L1", "C1", "C2", "C3", "C4"]
        for index, port in enumerate(portsName):
            indexNo = str(index + 1)
            portsData[indexNo] = {"no": indexNo, "name": portsName[index],
                                  "module_wavelength": get_list_value(queryData["wavelength"], index),
                                  "input_optical_power": get_list_value(queryData["rx_power"], index),
                                  "output_optical_power": get_list_value(queryData["tx_power"], index),
                                  "input_alarm_threshold": get_list_value(configData["rx_alarm_threshold_"], index),
                                  "transmission_distance": get_list_value(queryData["transmission_distance"],index),
                                  "module_state": get_list_value(queryData["existence"], index),
                                  "work_mode": get_list_value(configData["work_mode_"], index),
                                  "module_type": get_list_value(queryData["module_type"], index),
                                  "temperature": get_list_value(queryData["temperature"], index),
                                  "business_mode": get_list_value(configData["business_mode_"], index),
                                  "modulation": get_list_value(configData["modulation_"], index),
                                  "fec": get_list_value(configData["fec_switch_"], index),
                                  "pre_fec": get_list_value(queryData["pre_fec"], index),
                                  "post_fec": get_list_value(queryData["post_fec"], index),
                                  "serial-no": basicInfo.get("Serial number", ""),
                                  "board-model": basicInfo.get("Board model", ""),
                                  "port_note": handle_note(indexNo, "port_note", oldPortsData)}
    elif boardType == "4T4":
        portsName = ["L1", "L2", "L3", "L4", "C1", "C2", "C3", "C4"]
        for index, port in enumerate(portsName):
            indexNo = str(index + 1)
            portsData[indexNo] = {"no": indexNo, "name": portsName[index],
                                  "module_wavelength": get_list_value(queryData["wavelength"], index),
                                  "input_optical_power": get_list_value(queryData["rx_power"], index),
                                  "output_optical_power": get_list_value(queryData["tx_power"], index),
                                  "input_alarm_threshold": get_list_value(configData["rx_alarm_threshold_"], index),
                                  "transmission_distance": get_list_value(queryData["transmission_distance"],index),
                                  "module_state": get_list_value(queryData["existence"], index),
                                  "work_mode": get_list_value(configData["work_mode_"], index),
                                  "module_type": get_list_value(queryData["module_type"], index),
                                  "temperature": get_list_value(queryData["temperature"], index),
                                  "business_mode": get_list_value(configData["business_mode_"], index),
                                  "modulation": get_list_value(configData["modulation_"], index),
                                  "fec": get_list_value(configData["fec_switch_"], index),
                                  "pre_fec": get_list_value(queryData["pre_fec"], index),
                                  "post_fec": get_list_value(queryData["post_fec"], index),
                                  "serial-no": basicInfo.get("Serial number", ""),
                                  "board-model": basicInfo.get("Board model", ""),
                                  "port_note": handle_note(indexNo, "port_note", oldPortsData)}
    else:
        pass
    return portsData


def get_list_value(data_list, index):
    if data_list is not None and len(data_list) > index:
        return data_list[index]
    return ""


def get_old_card_ports_data(deviceCardList, slotIndex):
    for item in deviceCardList:
        if item.slot_index == slotIndex:
            return json.loads(item.ports_data)
    return dict()


def handle_note(outKey, innerKey, oldNote):
    paramTemp = oldNote.get(outKey)
    if paramTemp is not None:
        return paramTemp.get(innerKey)


def set_note(outKey, innerKey, newNote, jsonData):
    _data = json.loads(jsonData)
    if _data is not None:
        _data[outKey][innerKey] = newNote
        return json.dumps(_data)
    else:
        return jsonData


def update_reachable_status(ip, status):
    db_session = invent_db.get_session()

    db_session.query(OtnDeviceBasic).filter(OtnDeviceBasic.ip == ip).update(
        {OtnDeviceBasic.reachable_status: status})


def normalize_number(input, digit=2):
    if input in ["0", "1", "2", "3"]:
        return input

    if isinstance(input, str):
        if len(input) > 10:
            return input

        cleaned_str = input.replace('.', '', 1).replace('-', '', 1).replace('+', '')
        if cleaned_str.isdigit():
            try:
                return format(float(input), f'.{digit}f')
            except ValueError:
                return ''
    elif isinstance(input, (int, float)):
        return format(input, f'.{digit}f')

    return input


# 查询数据指令
def get_all_board_info(socket_client):
    socket_client.result = {"boardInfos": []}
    strSend = f"<C00_[PSWD_{socket_client.password}][CS_?][SN_?][DTP_?][MD_?][SV_?][HV_?][IP_?][MSK_?][GW_?]\
    [MAC_?][KEY_?][BZC_?][BZS_?][FNC_?][FNS_?][PWR_?][SNMPMIP_?]>"
    str_back = socket_client.send_command_by_long_connection(strSend)

    if len(str_back) > 0:
        index = 0
        fromIndex = 0
        deviceBasic = {}
        while index < len(str_back):
            if str_back[index] == '[':
                fromIndex = index
            if str_back[index] == ']':
                toIndex = index
                strData = str_back[fromIndex + 1: toIndex]
                update_device_info(socket_client, strData, deviceBasic)
            index += 1
        result = socket_client.result
        for key, value in FMT_CONFIG_MODEL.get(socket_client.model).get("nmuMapping").items():
            result[key] = deviceBasic.get(value)

        # 填充0号槽位NMU单板信息
        update_nmu(socket_client, deviceBasic)
        socket_client.NMU["0"] = "0"

    return socket_client.result, socket_client.NMU


# 指令解析
def update_device_info(socket_client, str_order, device_basic):
    OrderArray = str_order.split('_')
    orderKey = OrderArray[0]
    orderValue = OrderArray[1]
    if orderKey == 'CS':
        # 设备槽位号总数特殊处理
        socket_client.result["Slot number"] = int(orderValue)
        HasInfo = ""
        if len(OrderArray) > 2:
            HasInfo = OrderArray[2]
        index = 3
        while index < len(OrderArray) and index - 3 < len(HasInfo):
            if HasInfo[index - 3] == '1':
                strTemp = OrderArray[index]
                if strTemp != '0000':
                    update_board(socket_client, index - 2, strTemp)
            index += 1
    # 设备基本信息解析
    else:
        device_basic[orderKey] = orderValue


def update_nmu(socket_client, device_basic):
    board_config_temp = copy.deepcopy(FMT_CONFIG_MODEL.get(socket_client.model).get("boardMapping").get("0"))
    if board_config_temp is None:
        LOG.error("Error, nmu have no config!")
        return
    boardType = board_config_temp.get("boardType")
    analysisMode = board_config_temp.get("analysisMode")
    scan_board_config(board_config_temp, device_basic, analysisMode)
    board_config_temp["slotIndex"] = 0
    board_config_temp["boardType"] = boardType
    result = socket_client.result.get("boardInfos")
    result.append(board_config_temp)


def update_board(socket_client, slot_index, board_type):
    socket_client.NMU[str(slot_index)] = board_type
    board_config_temp = copy.deepcopy(FMT_CONFIG_MODEL.get(socket_client.model).get("boardMapping").get(board_type))
    if board_config_temp is None:
        LOG.error("Error, board type:{} have no config!".format(board_type))
        return
    boardType = board_config_temp.get("boardType")
    strSend = f"<C{str(slot_index).zfill(2)}_[PSWD_{socket_client.password}][STA_?][B_?]>"
    str_back = socket_client.send_command_by_long_connection(strSend)
    if len(str_back) > 0 and str_back != f"<C{str(slot_index).zfill(2)}_[PSWD_ERROR]>":
        result = socket_client.result.get("boardInfos")
        analysisMode = board_config_temp.get("analysisMode")
        if analysisMode == 1:
            dictionary = initial_analysis_ext(str_back)
        elif analysisMode == 2:
            dictionary = initial_analysis_special(str_back)
        else:
            dictionary = {}

        scan_board_config(board_config_temp, dictionary, analysisMode)

        board_config_temp["slotIndex"] = slot_index
        board_config_temp["boardType"] = boardType
        result.append(board_config_temp)


def scan_board_config(dict_data, mapping_data, analysisMode):
    for key, value in dict_data.items():
        if isinstance(value, str):
            if "#" not in value:
                result = mapping_data.get(value)
                if result is not None:
                    if analysisMode == 0:
                        dict_data[key] = normalize_number(result)
                    elif analysisMode == 1:
                        dict_data[key] = normalize_number(result[0])
                    elif analysisMode == 2:
                        dict_data[key] = normalize_number(result)
                    else:
                        pass
                else:
                    dict_data[key] = ""
            else:
                dict_array = value.split("#")
                result = mapping_data.get(dict_array[0])
                if result is not None:
                    dict_data[key] = normalize_number(result[int(dict_array[1])])
                else:
                    dict_data[key] = ""
        elif isinstance(value, list):
            # 包含@，则需要按槽位号从1开始递增拼接key来获取值
            if "@" in value[0]:
                index = 1
                getResult = []
                if analysisMode == 2:
                    # 目前单板最多16个端口
                    while index < 17:
                        getKey = value[0].replace("@", str(index).zfill(2))
                        getValue = mapping_data.get(getKey)
                        if getValue is None:
                            break
                        else:
                            getResult.append(getValue)
                        index += 1
                elif analysisMode == 1:
                    # 目前单板最多16个端口
                    while index < 17:
                        getKey = value[0].replace("@", str(index))
                        getValue = mapping_data.get(getKey)
                        if getValue is None:
                            break
                        if len(getValue) == 1:
                            getResult.append(getValue[0])
                        else:
                            getResult.append(getValue)
                        index += 1
                dict_data[key] = getResult
            else:
                pass
        elif isinstance(value, dict):
            scan_board_config(value, mapping_data, analysisMode)
        else:
            pass


def get_config(ip, slotIndex):
    recData = {}
    allBoardInfo = beat_sync_otn_device_info_single(None, ip)
    if allBoardInfo == "" or allBoardInfo.get("boardInfos") is None:
        return recData, 1, "Failed to synchronize device data!"
    card = get_card_by_slot_index(allBoardInfo.get("boardInfos"), slotIndex)
    if card is None:
        LOG.error(f"Slot index:{slotIndex} have no card.")
        return recData, 1, f"Slot index:{slotIndex} have no card!"
    config = card["businessInfo"].get("config")
    if config is None:
        LOG.error(f"Slot index:{slotIndex} have no config data.")
        return recData, 1, f"Slot index:{slotIndex} have no config data!"

    get_config_data(config, recData)

    return {"configData": recData, "boardInfo": {"type": card["boardType"], "model": card["Board model"]}}, 0, "success"


def get_config_data(dictData, matchData):
    for key, value in dictData.items():
        if isinstance(value, dict):
            get_config_data(value, matchData)
        # 多端口的配置，需要从CONFIG_KEY_PORT_MAP拿到对应的端口index填充value中的@位置
        elif isinstance(value, list):
            # TODO 列表的特殊处理
            matchData[key] = value
        else:
            matchData[key] = value


def modify_config(ip, slotIndex, key, value, device_type="FMT"):
    recData = {}
    nmu = get_nmu(ip)
    startTime = time.time()
    distributed_lock = DistributedLock(ip)
    if distributed_lock.acquire():
        socket_client = None
        try:
            LOG.info("get lock total time:" + str(time.time() - startTime))
            socket_client = SocketClient(ip, time_out=5, model=device_type)
            socket_client.create_socket_client()
            recData = handle_modify_config(socket_client, slotIndex, nmu, key, value)
            LOG.info("set config total time:" + str(time.time() - startTime))
            if recData == "":
                return recData, 1, "Failed, configuration result not obtained!"
        except socket.timeout:
            LOG.error("socket timed out!")
            LOG.error(traceback.format_exc())
            return recData, 1, "Failed, device connection timed out!"
        except Exception as e:
            LOG.error(f"Exception:{e}")
            LOG.error(traceback.format_exc())
            return recData, 1, "Failed, configuration failed!"
        finally:
            if socket_client is not None:
                socket_client.close_socket_client()
            distributed_lock.release()
            LOG.info("end modify config, release lock!")
    else:
        LOG.error("get lock fail!")
    return recData, 0, "success"


def get_nmu(ip):
    db_session = invent_db.get_session()
    data = db_session.query(OtnTempData).filter(OtnTempData.ip == ip).first()
    return json.loads(data.nmu) if data else {}


def handle_modify_config(socket_client, slot_index, nmu, key, value):
    if not isinstance(nmu, dict):
        raise Exception("nmu info is empty!")
    boardIndex = nmu.get(slot_index)
    if boardIndex is None:
        raise Exception("this slot is empty!")
    # 通过FMT单板配置文件校验key是否合法，并获取真实下发配置的key
    boardConfig = FMT_CONFIG_MODEL.get(socket_client.model).get("boardMapping").get(boardIndex)
    LOG.info(f"boardConfig:{boardConfig}")
    if boardConfig is None:
        raise Exception("get board config failed!")

    boardConfig = boardConfig.get("businessInfo").get("config")
    if boardConfig is None:
        raise Exception("board config is empty!")

    matchValue = []
    get_config_key(boardConfig, key, matchValue)
    if len(matchValue) == 0:
        raise Exception("board config not have this key!")

    command = f"<C{str(slot_index).zfill(2)}_[PSWD_{socket_client.password}][{matchValue[0]}_{value}]>"
    LOG.info("config command:%s" % command)
    str_back = socket_client.send_command_by_long_connection(command)
    LOG.info("config result:%s" % str_back)
    if len(str_back) > 0 and "_ERR" not in str_back:
        return str_back
    return ''


def get_config_key(dictData, matchKey, matchValue):
    for key, value in dictData.items():
        if key == matchKey:
            matchValue.append(value)
            return
        # 多端口的配置，需要从CONFIG_KEY_PORT_MAP拿到对应的端口index填充value中的@位置
        elif key in matchKey and isinstance(value, list):
            matchValue.append(value[0].replace("@", CONFIG_KEY_PORT_MAP.get(matchKey.replace(key, ""))))
            return
        elif isinstance(value, dict):
            get_config_key(value, matchKey, matchValue)
        else:
            pass


def initial_analysis_special(str_back):
    result = {}
    index = 0
    strStart = 0
    while index < len(str_back):
        if str_back[index] == '[':
            strStart = index
        if str_back[index] == ']':
            strEnd = index
            strData = str_back[strStart + 1: strEnd]
            if strData.startswith('B_'):
                OrderArray = strData.split('_')
                if len(OrderArray) >= 6:
                    SV = OrderArray[4]
                    HV = OrderArray[5]
                    result['SV'] = SV
                    result['HV'] = HV
            elif re.search(r"CH\d+_P_([-+]?\d+\.\d+)_\d+", strData):
                result[strData.split("_")[0] + "_" + strData.split("_")[1]] = strData.split("_")[2]
            else:
                strData = str_back[strStart + 1: strEnd]
                # 取最后一个_进行切割
                last_index = strData.rfind('_')
                key = strData[0:last_index]
                value = strData[last_index + 1:]
                result[key] = value
        index += 1
    return result


def initial_analysis_ext(str_back):
    result = {}
    index = 0
    strStart = 0
    while index < len(str_back):
        if str_back[index] == '[':
            strStart = index
        if str_back[index] == ']':
            strEnd = index
            strData = str_back[strStart + 1: strEnd]
            if strData.startswith('B_'):
                OrderArray = strData.split('_')
                if len(OrderArray) >= 6:
                    SV = OrderArray[4]
                    HV = OrderArray[5]
                    result['SV'] = [SV]
                    result['HV'] = [HV]
            else:
                OrderArray = strData.split('_')
                temp = []
                for i in range(1, len(OrderArray)):
                    temp.append(OrderArray[i])
                if OrderArray[0] in result:
                    array = [result[OrderArray[0]], temp]
                    result[OrderArray[0]] = array
                else:
                    result[OrderArray[0]] = temp
        index += 1
    return result
