import asyncio
import re
import ipaddress
import logging
from pysnmp.hlapi.v3arch.asyncio import *
from server.constants import OID_CONFIG, RJ_OID_CONFIG, SK_OID_CONFIG, HH_OID_CONFIG, BD_OID_CONFIG, MODEL_VENDOR_MAPPING, MODEL_MAX_OIDS_MAPPING, BRACKET_PATTERN, MODEL_PATTERN

# import csv
# import matplotlib.pyplot as plt
# import matplotlib.dates as mdates
# import numpy as np
# from datetime import datetime, timedelta

logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S' 
)
logger = logging.getLogger(__name__)


class SnmpConnector:
    MODEL_MAX_OIDS_MAPPING = MODEL_MAX_OIDS_MAPPING

    def __init__(
        self,
        snmp_version,
        ip=None,
        community=None,
        user_name=None,
        security_level=None,
        context_name=None,
        auth_protocol=None,
        auth_key=None,
        priv_protocol=None,
        priv_key=None,
        model=None, 
        max_oids_per_get=None,
        engine_id=None,
    ):
        """
        Initialize SNMP connection, supporting v1/v2c/v3 versions

        Parameters:
            snmp_version: SNMP version ('v1', 'v2c', 'v3')
            community: Community string (used for v1/v2c)
            user_name: Username (used for v3)
            security_level: SNMPv3 security level ('noAuthNoPriv', 'authNoPriv', 'authPriv')
            auth_protocol: Authentication protocol (used for v3)
            auth_key: Authentication key (used for v3)
            priv_protocol: Privacy protocol (used for v3)
            priv_key: Privacy key (used for v3)
            engine_id: Engine ID (used for v3, optional)
        """
        self.oid_config = OID_CONFIG.copy()
        self.ip = ip
        self.security_level = security_level or "noAuthNoPriv"
        self.context_name = context_name or ""
        self.auth_protocol = auth_protocol or "NONE"
        self.priv_protocol = priv_protocol or "NONE"
        self.snmp_engine = SnmpEngine()
        self.target = None
        self.auth = None
        self.model = model


        if max_oids_per_get is not None:
            self.max_oids_per_get = max_oids_per_get
        else:
            self.max_oids_per_get = self.MODEL_MAX_OIDS_MAPPING.get(model, None) 

        if snmp_version in ["v1", "v2c"]:
            if not community:
                raise ValueError("SnmpConnector v1/v2c requires community string")
            self.auth = CommunityData(
                community, mpModel=0 if snmp_version == "v1" else 1
            )
        elif snmp_version == "v3":
            auth_proto = {
                "NONE": usmNoAuthProtocol,
                "MD5": usmHMACMD5AuthProtocol,
                "SHA": usmHMACSHAAuthProtocol,
                "SHA-224": usmHMAC128SHA224AuthProtocol,
                "SHA-256": usmHMAC192SHA256AuthProtocol,
                "SHA-384": usmHMAC256SHA384AuthProtocol,
                "SHA-512": usmHMAC384SHA512AuthProtocol,
            }.get(auth_protocol, usmNoAuthProtocol)

            priv_proto = {
                "NONE": usmNoPrivProtocol,
                "DES": usmDESPrivProtocol,
                "3DES": usm3DESEDEPrivProtocol,
                "AES-128": usmAesCfb128Protocol,
                "AES-192": usmAesCfb192Protocol,
                "AES-256": usmAesCfb256Protocol,
                "AES-192-BLUMENTHAL": usmAesBlumenthalCfb192Protocol,
                "AES-256-BLUMENTHAL": usmAesBlumenthalCfb256Protocol,
            }.get(priv_protocol, usmNoPrivProtocol)

            if security_level == "noAuthNoPriv":
                self.auth = UsmUserData(user_name)
            elif security_level == "authNoPriv":
                self.auth = UsmUserData(
                    user_name, authProtocol=auth_proto, authKey=auth_key
                )
            elif security_level == "authPriv":
                self.auth = UsmUserData(
                    user_name,
                    authProtocol=auth_proto,
                    authKey=auth_key,
                    privProtocol=priv_proto,
                    privKey=priv_key,
                )
            else:
                raise ValueError(
                    "Unsupported security level, supported levels: noAuthNoPriv, authNoPriv, authPriv"
                )
        else:
            raise ValueError(
                "Unsupported SNMP version, supported versions: v1, v2c, v3"
            )
            
    def _validate_ip(self):
        try:
            ipaddress.ip_address(self.ip)
            return True
        except ValueError:
            return False

    async def connect(self, port=161, timeout=2, retries=1):
        """Connect to device"""
        if not self._validate_ip():
            raise ValueError(
                f"SNMP connection failed: missing or invalid IP address -> {self.ip}"
            )
        
        self.target = await UdpTransportTarget.create(
            (self.ip, port), timeout=timeout, retries=retries
        )
        logger.info(f"Successfully connected to device {self.ip}")
        return self

    async def get(self, oid, mode="single"):
        """Execute SNMP GET request"""
        if not self.target:
            raise Exception("Please connect to the device via connect first")
        if not oid:
            raise ValueError("oid cannot be empty")
        
        if mode == "single":
            if not isinstance(oid, str):
                raise TypeError("In 'single' mode, oid must be str")
            oids = [oid]
        elif mode == "batch":
            if not isinstance(oid, (list, tuple)):
                raise TypeError("In 'batch' mode, oid must be list/tuple of str")
            if self.max_oids_per_get and len(oid) > self.max_oids_per_get:
                result = {}
                for i in range(0, len(oid), self.max_oids_per_get):
                    chunk = oid[i:i+self.max_oids_per_get]
                    chunk_result = await self.get(chunk, mode="batch")
                    result.update(chunk_result)
                return result
            oids = oid
        else:
            raise ValueError("mode must be 'single' or 'batch'")
        
        var_binds = [ObjectType(ObjectIdentity(oid)) for oid in oids]

        try:
            iterator = get_cmd(
                self.snmp_engine,
                self.auth,
                self.target,
                ContextData(contextName=self.context_name),
                *var_binds
            )

            error_indication, error_status, error_index, var_binds_result = await iterator

            if error_indication:
                raise Exception(f"SNMP error: {error_indication}")
            elif error_status:
                raise Exception(
                    f"SNMP status error: {error_status.prettyPrint()} at {error_index}"
                )
            
            result = {}
            for oid_obj, value_obj in var_binds_result:
                result_oid = str(oid_obj)
                result_value = str(value_obj)
                result[result_oid] = result_value
                logger.info(
                    f"[{self.ip}] GET request successful: {result_oid} = {result_value}"
                )
                if not result_value:
                    logger.warning(
                        f"[{self.ip}] GET request returned empty value: {result_oid} = {result_value}"
                    )
            return result if mode == "batch" else result[oid] 

        except Exception as e:
            logger.error(f"[{self.ip}] SNMP GET request execution failed: {e}")
            raise e


    async def walk(self, oid, max_repetitions=10):
        """Execute SNMP WALK request"""
        if not self.target:
            raise Exception("Please connect to the device via connect first")

        result = {}
        last_oid = None
        var_binds = [ObjectType(ObjectIdentity(oid))]
        should_continue = True

        try:
            while should_continue:
                error_indication, error_status, error_index, var_binds = await bulk_cmd(
                    self.snmp_engine,
                    self.auth,
                    self.target,
                    ContextData(contextName=self.context_name),
                    0,
                    max_repetitions,  # 每次请求的最大OID数量
                    *var_binds,
                    lookupMib=False,  # 禁用MIB解析，提高性能
                )

                if error_indication:
                    raise Exception(f"SNMP error: {error_indication}")
                elif error_status:
                    raise Exception(
                        f"SNMP status error: {error_status.prettyPrint()} at {error_index}"
                    )

                if not var_binds:
                    break

                # 处理批量返回的所有OID
                for var_bind in var_binds:
                    current_oid, value = var_bind
                    current_oid_str = str(current_oid)
                    value_str = str(value)

                    # 检查是否超出初始OID范围
                    if current_oid_str[0:len(oid)] != oid:
                        should_continue = False
                        logger.info(
                            f"[{self.ip}] Exceeded OID range: {current_oid_str}, terminating WALK"
                        )
                        break

                    # 防止重复OID导致死循环
                    if current_oid_str == last_oid:
                        should_continue = False
                        logger.error(
                            f"[{self.ip}] Duplicate OID detected: {current_oid_str}, terminating WALK"
                        )
                        break

                    # print({current_oid_str: value_str})

                    result[current_oid_str] = value_str

                    # 更新上一次的OID
                    last_oid = current_oid_str

                # 更新下一个要查询的OID
                var_binds = [var_binds[-1]]

            return result

        except Exception as e:
            logger.error(f"[{self.ip}] SNMP WALK request execution exception: {e}")
            raise e

    
    async def walk_get_data(self, index_oid, target_oid_list, max_concurrent=10):
        """
        Obtain index values via index_oid, combine them with OIDs in target_oid_list to form actual OIDs, and fetch values in batches.

        Parameters:
            index_oid: OID used to obtain index values
            target_oid_list: List of parent OIDs that need to be combined with indexes

        Returns:
            Structure example:
            {
                "1": {
                    "*******.*******.1.2": {
                        "oid": "*******.*******.1.2.1", 
                        "value": "eth-0-1"
                    },
                    "*******.********.1.1.6": {
                        "oid": "*******.********.*******",
                        "value": 13064119124
                    },
                    ...
                },
                "2": {
                    "*******.*******.1.2": {
                        "oid": "*******.*******.1.2.2",
                        "value": "eth-0-2"
                    },
                    "*******.********.1.1.6": {
                        "oid": "*******.********.*******",
                        "value": 0
                    },
                    ...
                },
                ...
            }
        """
        try:
            index_result = await self.walk(index_oid)
            indexes = list(index_result.values())
            if not indexes:
                logger.warning(f"[{self.ip}] No indexes found for index_oid: {index_oid}")
                return {}
            logger.info(f"[{self.ip}] Found {len(indexes)} indexes from index_oid: {index_oid}")
        except Exception as e:
            logger.error(f"[{self.ip}] Failed to walk index_oid {index_oid}: {e}")
            raise e

        semaphore = asyncio.Semaphore(max_concurrent)

        async def bounded_batch_get(target_oid):
            async with semaphore:
                full_oids = [f"{target_oid}.{idx}" for idx in indexes]
                return target_oid, await self.get(full_oids, mode="batch")

        tasks = [bounded_batch_get(target_oid) for target_oid in target_oid_list]

        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            logger.error(f"[{self.ip}] Failed to execute batch get requests: {e}")
            raise e

        final_result = {}
        for res in results:
            if isinstance(res, Exception):
                logger.warning(f"[{self.ip}] Batch get failed: {res}")
                continue

            target_oid, oid_values = res
            for full_oid, value in oid_values.items():
                idx = full_oid.split(".")[-1]
                if idx not in final_result:
                    final_result[idx] = {}
                final_result[idx][target_oid] = {
                    "oid": full_oid,
                    "value": value
                }

        logger.info(f"[{self.ip}] Successfully processed {len(tasks)} OID requests (index_oid: {index_oid})")
        return final_result

    async def get_system_description(self):
        logger.info(f"[{self.ip}] Obtaining device system description")
        return await self.get(self.oid_config["system_description"])

    async def get_version(self):
        logger.info(f"[{self.ip}] Obtaining device version information")
        return await self.get(self.oid_config["version"])

    async def get_model(self):
        if self.model:
            logger.info(f"[{self.ip}] Model already provided: {self.model}")
            return self.model
        
        description = await self.get_system_description()
        if not description:
            logger.warning(f"[{self.ip}] System description is empty, unable to extract model")
            return None

        bracket_match = re.search(BRACKET_PATTERN, description)

        if bracket_match:
            model = bracket_match.group(1).strip().replace(" ", "-")
            return model

        model_match = re.search(MODEL_PATTERN, description)

        if model_match:
            return model_match.group(1).strip()
        logger.warning(f"[{self.ip}] Unable to extract model from system description")
        return None

    async def get_system_name(self):
        logger.info(f"[{self.ip}] Obtaining system name")
        return await self.get(self.oid_config["system_name"])

    async def get_serial_number(self):
        logger.info(f"[{self.ip}] Obtaining device serial number")
        return await self.get(self.oid_config["serial_number"])

    async def get_interfaces(self):
        """Obtain all interface information (using walk_get_data)"""
        if not self.target:
            raise Exception("Please connect to the device via connect first")

        try:
            index_oid = self.oid_config["if_index"]
            target_oid_list = [
                self.oid_config["if_name"],        # 接口名称
                self.oid_config["if_oper_status"], # 接口状态
                self.oid_config["if_speed"],       # 接口速度
                self.oid_config["if_phys_address"], # 接口物理地址(MAC)
                self.oid_config["if_in_errors"],   # 输入错误数
                self.oid_config["if_in_discards"], # 输入丢弃数
                self.oid_config["if_out_errors"],  # 输出错误数
                self.oid_config["if_out_discards"],# 输出丢弃数
                self.oid_config["if_hc_in_octets"],# 输入字节数
                self.oid_config["if_hc_out_octets"]# 输出字节数
            ]

            oid_results = await self.walk_get_data(index_oid, target_oid_list)
            if not oid_results:
                logger.warning(f"[{self.ip}] No OID results returned from walk_get_data")
                return {}

            oid_index_map = {}
            for idx, target_oid_data in oid_results.items():
                for target_oid, oid_info in target_oid_data.items():
                    if target_oid not in oid_index_map:
                        oid_index_map[target_oid] = {}
                    oid_index_map[target_oid][idx] = oid_info

            if_name_oid = self.oid_config["if_name"]
            if_name_map = oid_index_map.get(if_name_oid, {})
            if not if_name_map: 
                logger.warning(f"[{self.ip}] No interface names found in OID results")
                return {}

            status_map = {
                '1': 'up', '2': 'down', '3': 'testing',
                '4': 'unknown', '5': 'dormant', '6': 'notPresent', '7': 'lowerLayerDown'
            }

            interfaces = {}
            for idx, name_oid_info in if_name_map.items():
                interface_name = name_oid_info["value"]
                if not interface_name:
                    continue

                interface_info = {"index": idx}

                status_oid = self.oid_config["if_oper_status"]
                status_oid_info = oid_index_map.get(status_oid, {}).get(idx)
                interface_info["status"] = status_map.get(
                    status_oid_info["value"] if status_oid_info else None,
                    'unknown'
                )

                speed_oid = self.oid_config["if_speed"]
                speed_oid_info = oid_index_map.get(speed_oid, {}).get(idx)
                interface_info["speed"] = speed_oid_info["value"] if speed_oid_info else 'unknown'

                mac_oid = self.oid_config["if_phys_address"]
                mac_info = oid_index_map.get(mac_oid, {}).get(idx)
                interface_info["mac_address"] = mac_info["value"] if mac_info else 'unknown'

                in_err_oid = self.oid_config["if_in_errors"]
                in_err_info = oid_index_map.get(in_err_oid, {}).get(idx)
                interface_info["in_errors"] = in_err_info["value"] if in_err_info else '0'

                in_disc_oid = self.oid_config["if_in_discards"]
                in_disc_info = oid_index_map.get(in_disc_oid, {}).get(idx)
                interface_info["in_discards"] = in_disc_info["value"] if in_disc_info else '0'

                out_err_oid = self.oid_config["if_out_errors"]
                out_err_info = oid_index_map.get(out_err_oid, {}).get(idx)
                interface_info["out_errors"] = out_err_info["value"] if out_err_info else '0'

                out_disc_oid = self.oid_config["if_out_discards"]
                out_disc_info = oid_index_map.get(out_disc_oid, {}).get(idx)
                interface_info["out_discards"] = out_disc_info["value"] if out_disc_info else '0'

                in_bytes_oid = self.oid_config["if_hc_in_octets"]
                in_bytes_info = oid_index_map.get(in_bytes_oid, {}).get(idx)
                interface_info["in_bytes"] = in_bytes_info["value"] if in_bytes_info else '0'

                out_bytes_oid = self.oid_config["if_hc_out_octets"]
                out_bytes_info = oid_index_map.get(out_bytes_oid, {}).get(idx)
                interface_info["out_bytes"] = out_bytes_info["value"] if out_bytes_info else '0'

                interfaces[interface_name] = interface_info

            logger.info(f"[{self.ip}] Successfully obtained {len(interfaces)} interfaces information (via walk_get_data)")
            return interfaces

        except Exception as e:
            logger.error(f"[{self.ip}] Failed to obtain interface information: {e}")
            raise e


    def close(self):
        self.snmp_engine.close_dispatcher()
        logger.info(f"[{self.ip}] SNMP connection closed")


class RJSnmpConnector(SnmpConnector):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.vendor = "RJ"
        self.oid_config.update(RJ_OID_CONFIG.get(self.model, {}))

    async def get_version(self):
        logger.info(f"[{self.ip}] {self.vendor} device: Obtaining version information")
        info = await super().get_version()
        if info:
            version = info.split(",")[0].split(" ")[-1]
            logger.info(f"[{self.ip}] Extracted version information: {version}")
            return version
        logger.error(f"[{self.ip}] Standard OID failed to obtain version information")
        return None


class SKSnmpConnector(SnmpConnector):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.vendor = "SK"
        self.oid_config.update(SK_OID_CONFIG.get(self.model, {}))


class HHSnmpConnector(SnmpConnector):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.vendor = "HH"
        self.oid_config.update(HH_OID_CONFIG.get(self.model, {}))


class BDSnmpConnector(SnmpConnector):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.vendor = "BD"
        self.oid_config.update(BD_OID_CONFIG.get(self.model, {}))

    async def get_version(self):
        logger.info(f"[{self.ip}] {self.vendor} device: Obtaining version information")
        info = await super().get_version()
        if info:
            version = info.split(" ")[0]
            logger.info(f"[{self.ip}] Extracted version information: {version}")
            return version
        logger.error(f"[{self.ip}] Standard OID failed to obtain version information")
        return None

class SnmpVendor:
    # 设备型号到厂商的映射表
    MODEL_VENDOR_MAPPING = MODEL_VENDOR_MAPPING

    def __init__(self, ip=None, snmp_version=None, community=None, **kwargs):
        self.ip = ip
        self.snmp_version = snmp_version
        self.community = community
        self.connector_kwargs = kwargs
        self.model = None
        self.vendor = None

    async def _identify_vendor(self):
        """
        Connect to the device and obtain the model to identify the vendor
        """
        try:
            # 临时连接器用于识别厂商
            temp_connector = SnmpConnector(
                snmp_version=self.snmp_version,
                ip = self.ip,
                community=self.community,
                **self.connector_kwargs,
            )
            await temp_connector.connect()

            self.model = await temp_connector.get_model()
            if not self.model:
                logger.error(f"[{self.ip}] Initial model retrieval failed")

            logger.info(f"[{self.ip}] Detected device model: {self.model}")

            # 根据型号映射表确定厂商
            self.vendor = self.MODEL_VENDOR_MAPPING.get(self.model, "Unknown")
            logger.info(f"[{self.ip}] Identified device vendor: {self.vendor}")

        except Exception as e:
            logger.error(f"[{self.ip}] Device vendor identification failed: {e}")
            raise e
        finally:
            if "temp_connector" in locals():
                temp_connector.close()

    async def create_connector(self):
        await self._identify_vendor()

        base_kwargs = dict(
            snmp_version=self.snmp_version,
            ip=self.ip,
            community=self.community,
            model=self.model,
            **self.connector_kwargs,
        )

        if self.vendor == "RJ":
            return RJSnmpConnector(**base_kwargs)
        elif self.vendor == "SK":
            return SKSnmpConnector(**base_kwargs)
        elif self.vendor == "HH":
            return HHSnmpConnector(**base_kwargs)
        elif self.vendor == "BD":
            return BDSnmpConnector(**base_kwargs)
        else:
            # 未知厂商返回通用连接器
            return SnmpConnector(**base_kwargs)

        

def build_snmp_config(data):
    """
    Build basic SNMP configuration by extracting parameters from input data
    
    Args:
        data (dict): Input data containing SNMP configuration parameters
    
    Returns:
        dict: Basic SNMP configuration with required parameters
    """
    ip = data.get('ip')
    snmp_version = data.get('snmpVersion')
    
    config = {
        "mgt_ip": ip,
        "snmp_version": snmp_version,
    }
    
    if snmp_version in ['v1', 'v2c']:
        config["community"] = data.get('community')
    

    elif snmp_version == 'v3':
        config.update({
            "security_level": data.get('securityLevel'),
            "security_user": data.get('securityUser'),
            "context_name": data.get('contextName', '')
        })
        
        if config.get('security_level') in ['authNoPriv', 'authPriv']:
            config.update({
                "auth_protocol": data.get('authProtocol'),
                "auth_key": data.get('authPassword')
            })
        
        if config.get('security_level') == 'authPriv':
            config.update({
                "priv_protocol": data.get('privProtocol'),
                "priv_key": data.get('privPassword')
            })
    
    return config

async def fetch_snmp_device_info(config):
    """
    Retrieve device information (model, serial number, etc.) via SNMP and enrich the configuration
    
    Args:
        config (dict): Basic SNMP configuration containing connection parameters
    
    Returns:
        dict: Enriched configuration with device information retrieved via SNMP
    """
    try:
        if config['snmp_version'] in ['v1', 'v2c']:
            vender = SnmpVendor(
                ip=config['mgt_ip'],
                snmp_version=config['snmp_version'],
                community=config['community']
            )
        else:
            vender = SnmpVendor(
                ip=config['mgt_ip'],
                snmp_version=config['snmp_version'],
                user_name=config['security_user'],
                security_level=config['security_level'],
                context_name=config.get('context_name', ''),
                auth_protocol=config.get('auth_protocol', 'none'),
                auth_key=config.get('auth_key'),
                priv_protocol=config.get('priv_protocol', 'none'),
                priv_key=config.get('priv_key')
            )
        
        snmp = await vender.create_connector()
        await snmp.connect()
        
        tasks = [
            asyncio.create_task(snmp.get_model()),
            asyncio.create_task(snmp.get_serial_number()),  
            asyncio.create_task(snmp.get_version()),
            asyncio.create_task(snmp.get_system_name()),
        ]
        model, sn, version, sysname = await asyncio.gather(*tasks)
        
        if not model or not sn:
            raise ValueError("Failed to retrieve device model or serial number!")
        
        config.update({
            'model': model,
            'sn': sn,
            'version': version,
            'sysname': sysname
        })
        
        snmp.close()
        return config
    
    except Exception as e:
        raise e

async def get_device_info(snmp):
    # 同时创建所有SNMP请求任务
    tasks = [
        asyncio.create_task(snmp.get_system_description()),
        asyncio.create_task(snmp.get_serial_number()),
        asyncio.create_task(snmp.get_model()),
        asyncio.create_task(snmp.get_version()),
        asyncio.create_task(snmp.get_system_name())
    ]
    
    results = await asyncio.gather(*tasks)
    print(f"device info: {results}")
    return {
        "system_info": results[0],
        "serial_number": results[1],
        "model": results[2],
        "version": results[3],
        "name": results[4]
    }

# async def collect_interface_stats(snmp, duration=600, interval=1):
#     """
#     Continuously collect interface byte count data (default 10 minutes, once per second)
#     :param snmp: SNMP connector instance
#     :param duration: Collection duration in seconds (10 minutes = 600 seconds)
#     :param interval: Collection interval in seconds
#     :return: Collected statistical data
#     """
#     stats = {}
#     start_time = datetime.now()
#     end_time = start_time + timedelta(seconds=duration)
    
#     print(f"Starting interface data collection, duration {duration} seconds (10 minutes), collecting every {interval} seconds...")
#     print(f"Collection start time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
#     print(f"Estimated end time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
#     while datetime.now() < end_time:
#         current_time = datetime.now()
#         try:
#             interfaces = await snmp.get_interfaces()  # 获取接口信息
            
#             for if_name, if_details in interfaces.items():
#                 # 初始化接口数据结构
#                 if if_name not in stats:
#                     stats[if_name] = {
#                         "timestamps": [],  # 时间戳列表
#                         "if_in_errors": [],  # 输入错误数列表
#                         "if_in_discards": [],  # 输入丢弃数列表
#                         "if_out_errors": [],  # 输出错误数列表
#                         "if_out_discards": [],  # 输出丢弃数列表
#                         "in_bytes": [],    # 输入字节数列表
#                         "out_bytes": []    # 输出字节数列表
#                     }
                
#                 # 存储当前数据（转换为整数，避免字符串）
#                 stats[if_name]["timestamps"].append(current_time)
#                 stats[if_name]["if_in_errors"].append(int(if_details["in_errors"]))
#                 stats[if_name]["if_in_discards"].append(int(if_details["in_discards"]))
#                 stats[if_name]["if_out_errors"].append(int(if_details["out_errors"]))
#                 stats[if_name]["if_out_discards"].append(int(if_details["out_discards"]))
#                 stats[if_name]["in_bytes"].append(int(if_details["in_bytes"]))
#                 stats[if_name]["out_bytes"].append(int(if_details["out_bytes"]))
            
#             # 打印进度（每60秒打印一次，避免刷屏）
#             if (current_time - start_time).total_seconds() % 60 == 0:
#                 elapsed = int((current_time - start_time).total_seconds())
#                 print(f"Collected {elapsed} seconds, remaining {int((end_time - current_time).total_seconds())} seconds...")
        
#         except Exception as e:
#             print(f"Error occurred during collection: {str(e)}, will continue to next collection")
        
#         # 等待下一个采集周期
#         await asyncio.sleep(interval)
    
#     print(f"Interface byte count data collection completed, collected {len(stats)} interfaces, total duration {int((datetime.now() - start_time).total_seconds())} seconds")
#     return stats


# def save_stats_to_csv(stats, device_name, output_file=None):
#     """
#     Save the collected interface byte count data to a CSV file
#     :param stats: Collected statistical data
#     :param device_name: Device name (used to generate the file name)
#     :param output_file: Output file path, automatically generated by default
#     """
#     # 生成默认文件名（包含设备名和当前时间）
#     if not output_file:
#         timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
#         output_file = f"{device_name}_bytes_stats_{timestamp}.csv"
    
#     with open(output_file, mode='w', newline='', encoding='utf-8') as file:
#         writer = csv.writer(file)
#         # 写入表头
#         writer.writerow(["Interface Name", "Timestamp", "if_in_errors", "if_in_discards", "if_out_errors", "if_out_discards", "In Bytes", "Out Bytes"])
        
#         # 遍历接口数据
#         for if_name, if_stats in stats.items():
#             # 确保时间戳、in_bytes、out_bytes长度一致
#             num_records = len(if_stats["timestamps"])
#             for i in range(num_records):
#                 # 时间戳格式化为ISO格式
#                 timestamp_str = if_stats["timestamps"][i].isoformat()
#                 in_bytes = if_stats["in_bytes"][i]
#                 out_bytes = if_stats["out_bytes"][i]
#                 writer.writerow([if_name, timestamp_str, in_bytes, out_bytes])
    
#     print(f"Data successfully saved to CSV file: {output_file}")
#     print(f"File contains {sum(len(if_stats['timestamps']) for if_stats in stats.values())} records")

async def main():
    # 创建SNMP连接实例
    vender = SnmpVendor(
        ip="************",
        snmp_version="v2c",
        community="Public",
        # 以下v3参数在使用v2c时可省略
        # user_name='usr2',
        # security_level='authPriv',
        # auth_protocol='md5',
        # auth_key='Fscom123',
        # priv_protocol='aes128',
        # priv_key='Fscom123'
    )

    # 建立连接
    snmp = await vender.create_connector()
    await snmp.connect()

    try:
        # 1. Get device basic information
        device_info = await get_device_info(snmp)
        test = await snmp.get("*******.4.1.52642.1.1")
        print(f"test: {test}")
        print("===== Device Basic Information =====")
        print(f"Device Name: {device_info['name']}")
        print(f"Device Model: {device_info['model']}")
        print(f"Serial Number: {device_info['serial_number']}")
        print(f"Firmware Version: {device_info['version']}")
        print(f"System Description: {device_info['system_info']}\n")
        
        # time_start = time.time()
        # result = await snmp.get(["*******.********.*******.1", "*******.********.********.1", "*******.********.********.1", "*******.********.********.1", "*******.********.********.1", "*******.********.********.1"], mode="batch")
        # result1 = await snmp.get(["*******.********.********.67108992", "*******.4.1.52642.********.6.0"], mode="batch")
        # print(result1)
        # time_end = time.time()
        # print(f"get cost time: {time_end - time_start}")
        # print(result)


        # # 2. Get interface details
        # time_start = time.time()
        # interfaces = await snmp.get_interfaces()
        # time_end = time.time()
        # print(f"new get_interfaces cost time: {time_end - time_start}")
        # # mac_addr = await snmp.get("*******.4.1.52642.********.********.5")
        # # print(f"Device Physical Address: {mac_addr}")
        # print("===== Interface Details =====")
        # print(f"Total detected {len(interfaces)} interfaces:")
        # for if_name, if_details in interfaces.items():
        #     # phys_addr = if_details['phys_address']
        #     # phys_addr_bytes = phys_addr.encode('latin-1')
        #     # mac_add = ':'.join(f'{b:02x}' for b in phys_addr_bytes)
        #     print(f"\nInterface Name: {if_name}")  # 接口名称
        #     print(f"  Index: {if_details['index']}")  # 接口索引
        #     print(f"  Status: {if_details['status']}")  # 接口状态
        #     print(f"  Speed: {if_details['speed']}")  # 接口速度
        #     print(f"  Physical Address: {if_details['mac_address']}")  # 物理地址
        #     print(f"  Input Error Packets: {if_details['in_errors']}")  # 输入错误包数
        #     print(f"  Input Discarded Packets: {if_details['in_discards']}")  # 输入丢弃包数
        #     print(f"  Output Error Packets: {if_details['out_errors']}")  # 输出错误包数
        #     print(f"  Output Discarded Packets: {if_details['out_discards']}")  # 输出丢弃包数
        #     print(f"  Input Bytes: {if_details['in_bytes']}")  # 输入字节数
        #     print(f"  Output Bytes: {if_details['out_bytes']}")  # 输出字节数


        # # 3. 持续采集10分钟接口字节数数据（duration=600秒）
        # stats = await collect_interface_stats(snmp, duration=600, interval=1)

        # # 3. 保存数据到CSV
        # save_stats_to_csv(stats, device_name=device_info['name'])
        # oids_to_get = ["*******.*******.1.2.1", "*******.*******.1.7.1"]
        # result = await snmp.get_multiple_oids(oids_to_get)
        # print(f"Bulk GET multiple OIDs result: {result}")

        
    except Exception as e:
        raise e
            
    finally:
        # 关闭连接
        snmp.close()
        print("\n===== connection closed =====")


if __name__ == "__main__":
    asyncio.run(main())

