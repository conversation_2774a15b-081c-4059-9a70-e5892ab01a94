import logging
import json

from server import cfg
from server.db.models import upgrade, inventory
from server.util.tip_client_util import ServerType, post
LOG = logging.getLogger(__name__)
SERVERIP = cfg.CONF.global_ip

# 后端调用smb owgw服务升级固件
def smb_upgrade_api(sn, image_name, scheduled=False):
    try:
        # 定时任务升级中状态
        if scheduled:
            session = inventory.inven_db.get_session()
            with session.begin(subtransactions=True):
                obj = session.query(upgrade.DeviceLatestUpgradeStatus).filter(
                    upgrade.DeviceLatestUpgradeStatus.sn == sn).first()
                if obj:
                    obj.upgrade_status = 4

        download_url = 'http://{}/wireless/upgrade/image/action/download/{}'.format(SERVERIP, image_name)
        LOG.info('download_url: {}'.format(download_url))
        data = {
            "serialNumber": sn,
            "when": 0,
            "keepRedirector": False,
            "uri": download_url
        }

        response = post(ServerType.OWGW, f"/api/v1/device/{sn}/upgrade", data)
        if not response:
            LOG.error('smb_upgrade_api error: no response')
            return
        LOG.info(json.dumps(response))
    except Exception as e:
        LOG.error('smb_upgrade_api error:{}'.format(e))


# 查询正在升级状态的设备的版本是否匹配判断状态完成
def smb_upgrading_result():
    session = inventory.inven_db.get_session()
    with session.begin(subtransactions=True):
        objs = session.query(upgrade.DeviceLatestUpgradeStatus).filter(
            upgrade.DeviceLatestUpgradeStatus.upgrade_status == 4).all()
        for obj in objs:
            try:
                LOG.info('upgrading sn: {}'.format(obj.sn))
                url = 'https://owgw:17002/api/v1/device/{}/script'.format(obj.sn)
                data = {
                    "serialNumber": obj.sn,
                    "type": "shell",
                    "timeout": 30,
                    "script": "dWJ1cyBjYWxsIHN5c3RlbSBib2FyZA==",
                    "deferred": False,
                    "when": 0
                }
                res = post(ServerType.OWGW, f"/api/v1/device/{obj.sn}/script", data)
                if not res:
                    LOG.error(f"smb_upgrading_result error: no response, sn: {obj.sn}")
                    continue
                result_obj = json.loads(res['results']['status']['result'])
                description = result_obj['release']['description']
                LOG.info('description: {}'.format(description))
                LOG.info('image: {}'.format(obj.image.image_name))
                if description in obj.image.image_name:
                    obj.upgrade_status = 1
                    log_info = 'backend upgrade successful'
                else:
                    obj.upgrade_status = 2
                    log_info = 'backend upgrade failed'
                # log
                log_obj = upgrade.DeviceuUgradeOperationLog()
                log_obj.sn = obj.sn
                log_obj.log_info = '{} {}'.format(obj.image.image_name, log_info)
                session.add(log_obj)
            except Exception as e:
                LOG.error('smb_upgrading_result error:{}'.format(e))
