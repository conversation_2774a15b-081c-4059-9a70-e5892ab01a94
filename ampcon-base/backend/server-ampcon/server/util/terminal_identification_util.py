import os
import json


def dhcp_option_55_to_hex(fingerprint, size=255):

    print(fingerprint)
    res = ['0'] * size

    input_list = list(map(int, fingerprint.split(',')))

    sorted_input = sorted(input_list)

    for num in sorted_input:
        res[num - 1] = '1'

    res.reverse()

    bin_str = ''.join(res)

    hex_value = hex(int(bin_str, 2))[2:]

    print(hex_value)
    return hex_value


def parse_dhcp_fingerprint():
    res = {}
    with open('dhcp-db.txt', 'r') as file:
        lines = file.readlines()
        for line in lines[1:]:
            line = line.strip()
            if not line:
                continue
            fields = line.split('\t')
            if len(fields) < 4:
                continue

            dhcp_fingerprint = dhcp_option_55_to_hex(fields[1])

            res[dhcp_fingerprint] = {
                'device_name': fields[2],
                'score': int(fields[3])
            }
    return res


def write_to_file(data, filename='dhcp_fingerprint.json'):
    with open(filename, 'w') as file:
        json.dump(data, file, indent=4)


with open('./static/mac_oui_mapping.json', errors='ignore', encoding='utf-8') as f1, \
     open('./static/dhcp_fingerprint.json', errors='ignore', encoding='utf-8') as f2:
    mac_oui_dict = json.load(f1)
    fingerprint_dict = json.load(f2)


def format_mac_address(mac):
    return ':'.join(mac[i:i+2] for i in range(0, len(mac), 2))


def get_organization_name_by_mac(mac):
    return mac_oui_dict.get(mac.replace(':', '').replace('-', '').upper()[:6], None)


def get_device_name_by_dhcp_55_fingerprint(fingerprint):
    hex_fingerprint = dhcp_option_55_to_hex(fingerprint)
    return fingerprint_dict.get(hex_fingerprint, {}).get('device_name', 'UNKNOWN')


if __name__ == '__main__':
    write_to_file(parse_dhcp_fingerprint())
