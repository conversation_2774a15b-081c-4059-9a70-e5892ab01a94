import random
import string
import base64
import time
import os
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend


def random_string(min_len, max_len=None):
    """Generate a random string of specified length."""
    length = random.randint(min_len, max_len) if max_len else min_len
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))


def base64_encode(data):
    """Encode a string to Base64."""
    return base64.b64encode(data.encode()).decode()


def encrypt(key, plaintext):
    """Encrypt plaintext using AES encryption."""
    key = key.ljust(32)[:32].encode()  # Ensure the key is 32 bytes long
    iv = os.urandom(16)
    cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
    encryptor = cipher.encryptor()

    padder = padding.PKCS7(algorithms.AES.block_size).padder()
    padded_data = padder.update(plaintext.encode()) + padder.finalize()

    encrypted = encryptor.update(padded_data) + encryptor.finalize()

    return base64.b64encode(iv + encrypted).decode()


def random_object(company, period, expire, version):
    """Generate a random object as per the given logic."""
    confound_key = "</>"
    confound_prefix = 9
    confound_suffix = 11

    expire_time = int(time.time() * 1000) + expire * 24 * 3600 * 1000
    map_name = [company, period, expire_time, version]
    map_index = [random.randint(2, 3), random.randint(4, 5), random.randint(6, 7), random.randint(8, 9)]

    s = random_string(confound_prefix) + ''.join(map(str, map_index)) + random_string(confound_suffix)
    obj = [base64_encode(s)]

    for i in range(1, 9):
        index = i + 1
        if index not in map_index:
            obj.append(base64_encode(random_string(20, 35)))
        else:
            value = map_name[map_index.index(index)]
            obj.append(base64_encode(random_string(confound_prefix) + str(value) + random_string(confound_suffix)))

    return obj


def generate_key(ase_key, company, period, expire, version):
    """Generate the encrypted key."""
    s = random_object(company, period, expire, version)
    print (1111, s)
    return encrypt(ase_key, '</>'.join(s))


def generate(company, period, expire, version):
    """Generate the key and the encrypted code."""
    ase_key = random_string(32)
    code = generate_key(ase_key, company, period, expire, version)
    return ase_key, code


if __name__ == "__main__":
    # Example inputs
    company = "ExampleCompany"
    period = "30"
    expire = 30  # Expire in days
    version = "2.0.0"

    key, code = generate(company, period, expire, version)
    print(f"Key: {key}")
    print(f"Encrypted Code: {code}")
