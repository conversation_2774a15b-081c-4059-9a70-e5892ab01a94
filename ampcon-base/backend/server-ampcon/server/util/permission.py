from flask_principal import Permission, RoleNeed

super_user_permission = Permission(<PERSON><PERSON><PERSON>('superuser'))
super_admin_permission = super_user_permission.union(Permission(<PERSON><PERSON><PERSON>('superadmin')))
admin_permission = super_admin_permission.union(Permission(<PERSON><PERSON><PERSON>('admin')))
readonly_permission = admin_permission.union(Permission(<PERSON><PERSON><PERSON>('readonly')))
