import logging
import re

from server import constants

LOG = logging.getLogger(__name__)


def get_pro_type(file_path=f"{constants.AUTOMATION_BASE_DIR}/server/.env"):
    """
    从.env中 PRO_TYPE字段获取Ampcon Type
    :param file_path: .env文件路径
    :return: ampcon-dc | ampcon-campus | ampcon-smb
    """
    pro_type = None
    try:
        with open(file_path, 'r') as file:
            for line in file:
                match = re.match(r'^\s*PRO_TYPE\s*=\s*(.*)\s*$', line)
                if match:
                    pro_type = match.group(1)
                    break
    except Exception as e:
        LOG.info(f": {e}")
    return pro_type


ampcon_pro_type = get_pro_type()
