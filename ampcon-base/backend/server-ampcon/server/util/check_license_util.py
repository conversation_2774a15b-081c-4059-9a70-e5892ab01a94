from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend
import json
import base64
import os
from server.license_check.license_check import licensechecker

ENCRYPT_KEY="SGxrNHlMWVBIczJFREc1N0JnL3NZPTI4TmJIVUk1SmZSUUo1RHNVR3FyOUo="


class LicenseChecker:
    def __init__(self):
        self.key = base64.b64decode(self.__decrypt_key(ENCRYPT_KEY).encode("utf-8"))
        self.backend = default_backend()
        self.block_size = algorithms.AES.block_size

    @staticmethod
    def swap_half(key):
        half_length = len(key) // 2
        swapped_key = key[half_length:] + key[:half_length]
        return swapped_key

    def __encrypt_key(self, decrypt_root_key):
        return base64.b64encode(self.swap_half(decrypt_root_key).encode("utf-8")).decode("utf-8")

    def __decrypt_key(self, encrypt_root_key):
        return self.swap_half(base64.b64decode(encrypt_root_key.encode("utf-8")).decode("utf-8"))

    def encrypt(self, plaintext):
        iv = os.urandom(16)
        cipher = Cipher(algorithms.AES(self.key), modes.CBC(iv), backend=self.backend)
        encryptor = cipher.encryptor()

        padder = padding.PKCS7(self.block_size).padder()
        padded_data = padder.update(plaintext.encode('utf-8')) + padder.finalize()

        ciphertext = encryptor.update(padded_data) + encryptor.finalize()
        return base64.b64encode(iv + ciphertext).decode('utf-8')

    def decrypt(self, ciphertext):
        data = base64.b64decode(ciphertext.encode('utf-8'))
        iv = data[:16]
        actual_ciphertext = data[16:]
        cipher = Cipher(algorithms.AES(self.key), modes.CBC(iv), backend=self.backend)
        decryptor = cipher.decryptor()
        padded_plaintext = decryptor.update(actual_ciphertext) + decryptor.finalize()

        unpadder = padding.PKCS7(self.block_size).unpadder()
        plaintext = unpadder.update(padded_plaintext) + unpadder.finalize()
        return plaintext.decode('utf-8')

def beat_check_license_expire_time():
    licensechecker.check_expire()

if __name__ == '__main__':
    aa = {"sn": "KSC", "mac": "de:cc:fe:12:e3:12"}
    license_checker = LicenseChecker()
    bb = license_checker.encrypt(json.dumps(aa))
    print(bb)
    cc = license_checker.decrypt(bb)
    print(json.loads(cc))
