# from ansible_lib import api_ans
import json
import logging
import os
import re, yaml
import time
from datetime import datetime

from server import deploy_switch, constants
from server.ansible_lib.ansible_common import file_task, lineinfile_task, shell_task, copy_task, ResultCallback, \
    run_playbook, run_tasks, blockinfile_task, run_ping
from server.ansible_lib.pica_lic import pica8_license
from server.constants import BASE_DIR, CONFIG_PATH, FLAG_FILE, ANSIBLE_TMP, REBOOT_SLEEP_TIME, AGENT_CONF, BACKUP_FILE
from server.db.models import inventory
from server.db.models.inventory import inven_db
from server.util import utils, encrypt_util
from server.util import ssh_util as conn_client
from server import cfg
from celery_app import my_celery_app
from celery_app.automation_task import AmpConBaseTask

LOG = logging.getLogger(__name__)


class AnsibleDeploySwitch(deploy_switch.RegSwitchApi):
    """
        deploy configs to switch
        all phases
        1. push security config
        2. config final ip
        3. install license
        4. upgrade image
        5. push tar file and execute install.sh
        6. push full config

        first should push 1,2 because switch ip and user name will change we will

        need reboot steps: parking security, patch tar, upgrade image
    """

    def __init__(self, info):
        super(AnsibleDeploySwitch, self).__init__(info)
        self.full_config = ''

    def add_automation_user(self):
        user = self.mgt_sys_config.switch_op_user
        task_name = "add " + user + " user"
        # return [shell_task(task_name, "sudo useradd " + user + " -g xorp &>/dev/null")]
        return [shell_task(task_name, '(sudo adduser --disabled-password --gecos "" {0} || true) &>/dev/null'.format(user))]

    def add_automation_in_xorp(self):
        user = self.mgt_sys_config.switch_op_user
        task_name = "add " + user + " into xorp group"
        # return [shell_task(task_name, "sleep 5 && sudo usermod -a -G xorp " + user)]
        return [shell_task(task_name, "(sleep 5 && sudo adduser {0} xorp || true) &>/dev/null".format(user) )]

    def upgrading_agent(self):
        task_name = "upgrade agent code"
        current_path = os.path.dirname(os.path.abspath(__file__))
        agent_code_path = current_path + "/agent/"
        remote_agent_path = "/opt/auto-deploy/"
        conf_str = inven_db.get_switch_agent_conf(self.switch_info.sn)

        conf_path = remote_agent_path + 'auto-deploy.conf'
        del_task = file_task('delete old auto_conf file',
                             {"path": conf_path, "state": "absent"})
        if conf_str:
            copy_conf_task = blockinfile_task('copy auto-deploy to agent', args=dict(
                path=conf_path, block=conf_str,
                insertafter='EOF', create='yes', marker=''
            ))
        else:
            copy_conf_task = copy_task('copy auto-deploy to agent',
                                       'config_gen/' + self.switch_info.platform_model + '/auto-deploy.conf',
                                       conf_path, "admin", mode='0777')
        # add function of upgrade save_file list for vpn config files to dell switch
        if utils.switch_need_save_config(self.model):
            change_backup_permission_task = shell_task('chmod /etc/picos', 'sudo chmod 777 /etc/picos')
            add_vpn_in_backup_task = lineinfile_task('upgrade backup list',
                                                     args={"path": BACKUP_FILE, "line": '/etc/openvpn/',
                                                           "regexp": '^/etc/openvpn/', "backup": "false"})
            add_itself_in_backup_task = lineinfile_task('upgrade backup list', args={"path": BACKUP_FILE,
                                                                                     "line": '/etc/picos/backup_files.lst',
                                                                                     "regexp": '^/etc/picos/backup_files.lst',
                                                                                     "backup": "false"})
            run_save_config_task = shell_task('run save_config task', 'sudo save_config')
            return [copy_task(task_name, agent_code_path, remote_agent_path, "admin"), del_task, copy_conf_task,
                    change_backup_permission_task,
                    add_vpn_in_backup_task, add_itself_in_backup_task, run_save_config_task]
        return [copy_task(task_name, agent_code_path, remote_agent_path, "admin"), del_task, copy_conf_task]

    def change_file_flag(self, regexp, value, tags):
        return [file_task('delete auto flag', {"path": FLAG_FILE, "state": "absent"}),
                lineinfile_task('change auto file flag', args={"path": FLAG_FILE, "line": value,
                                                               "regexp": regexp, "create": "yes"}, tags=tags)]

    def push_ip_configs(self):
        mgt_ip_config = filter(lambda config: config.type == 'mgt_ip', self.switch_info.configs)
        if not mgt_ip_config:
            raise deploy_switch.DeployError(self.sn, 'push ip configs', 'no mgt ip config')

        return [lineinfile_task('push mgt ip config',
                                args={"path": CONFIG_PATH, "line": mgt_ip_config[0].config, "insertafter": 'EOF'})]

    def remove_ansible_temp(self):
        # before get license, we neeed configure /cftmp/automation/.ansible can be write by TACACS user
        task_name = "remove ansible tmp file"
        return [shell_task(task_name, "rm -rf %s" % ANSIBLE_TMP)]

    def chmod_ansible_temp(self):
        # before get license, we neeed configure /cftmp/automation/.ansible can be write by TACACS user
        task_name = "modify ansible temp directory RW"
        return [shell_task(task_name, "sudo chmod -R 777 %s" % ANSIBLE_TMP)]

    def push_security_configs(self):
        security_file_path = self.mgt_sys_config.get_decrypted_security_config_path()
        return [file_task('delete config file', {"path": CONFIG_PATH, "state": "absent"}),
                copy_task('push_security_file', security_file_path, CONFIG_PATH, "admin", tags='1')], security_file_path

    def push_regional_configs(self):
        regional_config = filter(lambda config: config.type == 'regional', self.switch_info.configs)
        if not regional_config:
            raise deploy_switch.DeployError(self.sn, 'push regional config', 'no regional config')

        return [lineinfile_task('push regional config',
                                args={"path": CONFIG_PATH, "line": regional_config[0].config, "insertafter": 'EOF'})]

    def run_cmd(self, task_name, cmd):
        play_source = dict(
            name="PicOS switch play",
            hosts="all",
            gather_facts='no',
            tasks=[shell_task(task_name, cmd)]
        )

        callback = ResultCallback(self.sn)
        run_playbook(self.user, self.pw, play_source, [self.ip], callback=callback)
        return callback.result[task_name]['result']

    def install_license(self):

        def add_license_tasks(speed):
            license_key = self._ensure_license_available(speed)
            remote_file = BASE_DIR + 'switch.lic'
            # before create a license file, ansible need that file
            delete_license_file_task = file_task('delete old license file', {"path": remote_file, "state": "absent"})
            push_license_task = lineinfile_task('push_lic_file', args={"path": remote_file, "line": license_key,
                                                                       "insertafter": "EOF",
                                                                       "create": "yes"
                                                                       })
            # after copy license to switch, need to install license
            install_license_task = shell_task('install license', 'sudo /usr/sbin/license -i ' + remote_file)
            # restart_picos_task = service_task('restart picos', 'picos', 'restarted', tags='3')
            
            if self.model in constants.BUSY_BOX_MODEL_LIST:
                restart_picos_task = shell_task('restart picos', 'sudo /etc/init.d/picos restart && sleep 10')
            else:
                restart_picos_task = shell_task('restart picos', 'sudo service picos restart && sleep 10')
            show_license_task = shell_task('show license', '/usr/sbin/license -s', tags='3')
            return [delete_license_file_task, push_license_task, install_license_task, restart_picos_task,
                    show_license_task]

        # first get switch local license
        task_name = "get local license"
        result = self.run_cmd(task_name, "/usr/sbin/license -s")
        if 'stderr' not in result or result['stderr'] == '':

            if 'stdout' not in result:
                msg = result['msg'] if 'msg' in result else 'unreachable'
                raise deploy_switch.DeployError(self.sn, 'get local license', msg)

            result = result['stdout']
            result = json.loads(result)
            expire_date = result['Support End Date']
            expr_date = expire_date.split('-')

            if datetime(int(expr_date[0]), int(expr_date[1]), int(expr_date[2])) > datetime.now():
                name = ('PicOS_' + self.sn) if 'Site Name' not in result else result['Site Name']
                LOG.info('update local license info [sn:%s,name:%s,hwid:%s,expr_date:%s] for %s',
                         self.sn, name, self.hwid, result['Support End Date'], self.sn)
                inven_db.update_lic(self.sn, name=name, expr_date=result['Support End Date'],
                                    status='Active')
                inven_db.update_step(self.sn, 3)
                return []
            else:
                speed = result['Type'].strip()
                return add_license_tasks(speed)
        else:
            speed = re.findall('Type:\s+(.*)', result['stdout'])
            speed = speed[0].strip() if speed else '1G'
            return add_license_tasks(speed)

    def _ensure_license_available(self, speed):
        LOG.info('start install switch license %s', self.sn)
        # ensure whether has a license
        license_key = None
        idx = 0
        try:
            idx, license_key = pica8_license(self.sn).license_get(self.hwid)
        except Exception as e:
            LOG.exception(e)
            # raise deploy_switch.DeployError(self.sn, 'Get license from portal Failed',
            #                                 'Get license for %s Failed' % self.sn)

        if not license_key or 'error' in license_key:

            license_key = inven_db.get_switch_local_license(self.sn)
            if not license_key:
                # now we can create a license
                idx, license_key = pica8_license(self.sn).license_create(hwid=self.hwid, name='PicOS_' + self.sn, speed=speed)
                if not license_key or 'error' in license_key:
                    LOG.error('error in create license for %s switch', self.sn)
                    raise deploy_switch.DeployError(self.sn, 'install license',
                                                    'error in create license %s switch' % self.sn)

                utils.update_db_license_count()
                LOG.warn('success create a license for %s, key len: %d\n %s', self.sn, len(license_key), license_key)
            else:
                return license_key

        if not license_key:
            return license_key

        # update db
        license_detail = pica8_license(self.sn).license_details(self.hwid, idx)
        if not license_detail:
            raise deploy_switch.DeployError(self.sn, 'install license', 'get %s license detail failed' % self.sn)
        if 'error' in license_detail:
            raise deploy_switch.DeployError(self.sn, 'install license', 'get %s license detail failed with error %s'
                                            % (self.sn, license_detail['error']))
        LOG.info('update license info [sn:%s,key:%s,hwid:%s,expr_date:%s,name:%s] for %s',
                 self.sn, license_key, license_detail['hardware_id'], license_detail['expiry_date'],
                 license_detail['licence_name'], self.sn)
        inven_db.update_lic(self.sn, key=license_key, expr_date=license_detail['expiry_date'],
                            name=license_detail['licence_name'],
                            status='Active', speed=speed)

        return license_key

    def conform_switch_version(self):
        result = self.run_cmd('get switch version', 'version')
        if 'stdout' not in result:
            msg = result['msg'] if 'msg' in result else 'unreachable'
            raise deploy_switch.DeployError(self.sn, 'get switch version', msg)

        result = result['stdout']
        # get hardware model by ssh version
        hardware_model = re.findall("Model.*:\s+(.*)\n", result)
        if hardware_model and 'as' in hardware_model[0].lower():
            hardware_model = hardware_model[0].lower()
        current_version = list(map(lambda x: x[0] if x[0] else x[1] if x[1] else x[2], re.findall("L2/L3 Version/Revision.*:\s*(.*?)\n|PICOS Release/Commit.*:\s*(.*?)\n|Software Version.*:\s*(.*?)\n", result)))

        if not current_version:
            raise deploy_switch.DeployError(self.sn, 'push image', '%s::::%s error in find version from stdout %s'
                                            % (utils.time_now(), self.sn, result))

        current_version = current_version[0]
        version, seg, reversion = current_version.rpartition('/')
        reversion = reversion.split('-')[0]
        inven_db.update_version(self.sn, version, reversion)

        # return current_version == self.model_info.up_to_date_version, hardware_model
        # image name version not equals switch version
        # return reversion == self.model_info.up_to_date_version.split('/')[1], hardware_model if self.model_info.up_to_date_version.split('/')[1] else True
        
    def update_switch_mac(self):
        result = self.run_cmd('get switch mac', 'cat /sys/class/swmon/ctrl/asic_mac')
        if 'stdout' not in result:
            msg = result['msg'] if 'msg' in result else 'unreachable'
            raise deploy_switch.DeployError(self.sn, 'get switch mac', msg)

        mac_addr = result['stdout']
        inven_db.update_mac_addr(self.sn, mac_addr.strip().lower())

    def push_full_configs(self):
        # first delete before auto.config
        delete_file_task = file_task('delete auto.config', args=dict(path=CONFIG_PATH, state='absent'))

        security_config_path = self.mgt_sys_config.get_decrypted_security_config_path()
        push_security_task = copy_task('push_security_file', security_config_path,
                                       CONFIG_PATH, None)

        config_str = ''
        switch_configs = self.switch_info.configs
        for config in switch_configs:
            # if config.type != 'mgt_ip' and config.type != 'regional':
            config_str += config.config + '\n'

        general_configs = self.switch_info.general_configs
        for config in general_configs:
            config_str += config.content

        self.full_config = config_str
        push_full_config_task = blockinfile_task('push full config task', args=dict(
            path=CONFIG_PATH, block=config_str, insertafter='EOF', create='yes', marker=''
        ))

        return [delete_file_task, push_security_task, push_full_config_task], security_config_path

    def push_rma_full_configs(self):
        # first delete before auto.config
        delete_file_task = file_task('delete auto.config', args=dict(path=CONFIG_PATH, state='absent'))

        # config_rma = db.get_model(model.SwitchConfigBackup, filters={'sn': [self.sn]})
        #
        # if not config_rma:
        config = inven_db.get_switch_back_sn(self.sn)
        if not config:
            config = inven_db.get_switch_back_by_sn(self.sn)
        if not config:
            raise deploy_switch.DeployError(self.sn, 'push full config', 'no rma config file found')

        push_full_config_task = blockinfile_task('push full config task', args=dict(
            path=CONFIG_PATH, block=config, insertafter='EOF', create='yes', marker=''
        ))
        # load_config_task = shell_task('load rma config',
        #                               'sudo start-stop-daemon --pidfile /home/<USER>/config.pid -bSa'
        #                               ' "/pica/bin/pica_sh" -- -c "configure;load override /pica/bin/pica_default.boot;load merge %s;commit"'
        #                               % CONFIG_PATH)

        return [delete_file_task, push_full_config_task]

    def install_patched_tar_file(self):
        save_config_tasks = []
        if utils.switch_need_save_config(self.model):
            run_save_config_task = shell_task('run save_config task', 'sudo save_config')
            save_config_tasks = [run_save_config_task]

        if not self.model_info:
            return []

        tar_file_path = self.model_info.patched_tar_file
        if not tar_file_path:
            return []
            # LOG.error('pathed tar file have not been configured, please config by gui')
            # raise deploy_switch.DeployError(self.sn, 'install patched tar file',
            #                                 'patched tar file have not been configured, please config by gui')
        tar_file_remote_path = BASE_DIR + os.path.basename(tar_file_path)
        patched_install_script = self.model_info.patched_install_script
        scripts = re.split(",|;", patched_install_script)
        if '/' in scripts[0]:
            tar_dir = scripts[0].rpartition(os.path.sep)[0] + '/'
        else:
            tar_dir = ''
        untar_dir = BASE_DIR + os.path.dirname(scripts[0])
        rm_patched_tar = file_task('del patched tar file', args=dict(path=tar_file_remote_path, state='absent'))
        rm_con_dir = file_task('del configuration files',
                               args=dict(path=untar_dir, state='absent'))
        copy_tar_task = copy_task('copy tar file', tar_file_path, tar_file_remote_path, None)
        cmd = 'tar -xvf ' + tar_file_remote_path + ' -C ' + BASE_DIR
        untar_cmd_task = shell_task('untar tar file', cmd)

        # domain = self.switch_info.domain
        # resolv_path = BASE_DIR + tar_dir + 'resolv.conf.bak'
        # change_domain_task = lineinfile_task('change domain task',
        #                                      args={"path": resolv_path, "line": 'domain %s' % domain,
        #                                            "regexp": '^domain.*', "create": "yes"})
        execute_script_tasks = []
        for i, script in enumerate(scripts):
            execute_script_tasks.append(shell_task('chmod %s' % script, 'chmod u+x ' + BASE_DIR + script))
            execute_script_tasks.append(shell_task('execute %s' % script,
                                                   'cd %s && sudo bash %s' % (untar_dir, os.path.basename(script))))

        execute_script_tasks.extend(self.change_file_flag('.*', '', []))
        if save_config_tasks:
            execute_script_tasks.append(shell_task('chmod %s' % BACKUP_FILE, 'sudo chmod 666 %s' % BACKUP_FILE))
            execute_script_tasks.append(shell_task('append %s into %s' % (untar_dir, BACKUP_FILE),
                                                   'sudo echo %s >> %s' % (untar_dir, BACKUP_FILE)))
            execute_script_tasks.extend(save_config_tasks)
        execute_script_tasks.append(shell_task('restart picos after patch config the system ',
                                               'nohup sh -c "sleep 3 && sudo reboot" &'))
        return [rm_patched_tar, rm_con_dir, copy_tar_task, untar_cmd_task] + execute_script_tasks
        # return [rm_patched_tar, rm_con_dir, copy_tar_task, untar_cmd_task, change_domain_task] + execute_script_tasks

    def validate_tar_install(self):
        if not self.model_info:
            return []
        if not self.model_info.patched_tar_file:
            return []

        scripts = re.split(",|;", self.model_info.patched_install_script)
        untar_dir = BASE_DIR + os.path.dirname(scripts[0])
        # add validate script to validate patch tar files
        validate_tar_tasks = []
        validte_script_name = os.path.basename(self.model_info.script_file_path)
        validate_tar_tasks.append(copy_task('copy validate tar script', self.model_info.script_file_path,
                                            untar_dir, None))
        validate_tar_tasks.append(shell_task('patch tar validate', 'cd %s && bash %s' % (untar_dir,
                                                                                         validte_script_name)))
        return validate_tar_tasks

    def validate_full_configs(self, name='full config validate'):
        # reboot_picos_task = shell_task('restart picos after exec full config',
        #                                'sudo service picos restart && sleep 10')
        if name == 'full config validate':
            show_configs_task = shell_task(name,
                                           '/pica/bin/pica_sh -c "configure;show all|display set"',
                                           tags='5')
        else:
            show_configs_task = shell_task(name, '/pica/bin/pica_sh -c "configure;show all | no-more"',
                                           tags='5')

        # return [reboot_picos_task, show_configs_task]
        return [show_configs_task]

    def retrive_switch_config(self):
        retrive_task = shell_task('retrieve config', 'cat /pica/config/pica_startup.boot')
        return [retrive_task]

    def start_push_security_config(self):
        tasks = self.add_automation_user()
        tasks += self.add_automation_in_xorp()
        tasks += self.upgrading_agent()
        task, security_file_path = self.push_security_configs()
        tasks += task
        # tasks += self.chmod_ansible_temp()
        # tasks += self.push_regional_configs()
        # tasks += self.push_ip_configs()
        tasks += self.change_file_flag('.*', '', tags=[])
        tasks.append(shell_task('execute security config',
                                '/pica/bin/pica_sh -c "configure;execute %s;commit"' % CONFIG_PATH))

        tasks.append(shell_task('security validate',
                                '/pica/bin/pica_sh -c "configure;show all|display set"', tags='2'))

        tasks.append(shell_task('restart agent after security config the system ',
                                'sudo /opt/auto-deploy/agent.sh restart'))
        callback = ResultCallback(self.sn)

        ssh_session, status, _ = conn_client.get_interactive_session(self.ip, username=self.user,
                                                                     password=self.pw,
                                                                     timeout=15)
        if status == constants.RMA_ACTIVE:
            p = run_tasks(self.user, self.pw, tasks, [self.ip], callback=callback)
        else:
            ssh_session, status, _ = conn_client.get_interactive_session(self.ip, username=self.global_config.switch_op_user,
                                                                         password=self.global_config.switch_op_password,
                                                                         timeout=15)
            if status == constants.RMA_ACTIVE:
                p = run_tasks(self.global_config.switch_op_user, self.global_config.switch_op_password, tasks, [self.ip], callback=callback)
            else:
                p = run_tasks(self.mgt_sys_config.switch_op_user, self.mgt_sys_config.switch_op_password, tasks, [self.ip], callback=callback)

        encrypt_util.aes_cipher.remove_temp_file(security_file_path)
        if p != 0:
            raise deploy_switch.DeployError(self.sn, 'push security configs',
                                            'failed in deployed with error code %s' % p)
        # time.sleep(150)
        # db.update_step(self.sn, 2)

    def start_push_parking_security_config(self):
        tasks = self.add_automation_user()
        tasks += self.add_automation_in_xorp()
        # in case switch cannot be found in SWITCH table
        # tasks += self.upgrading_agent()
        parking_security_config_path = self.mgt_sys_config.get_decrypted_parking_security_config_path()
        tasks.append(copy_task('push_security_file', parking_security_config_path, CONFIG_PATH, None))
        tasks += self.change_file_flag('.*', '', tags=[])
        tasks.append(shell_task('execute security config',
                                '/pica/bin/pica_sh -c "configure;execute %s;commit"' % CONFIG_PATH))

        tasks.append(shell_task('pt security validate',
                                '/pica/bin/pica_sh -c "configure;show all|display set"', tags='2'))

        callback = ResultCallback(self.sn)
        p = run_tasks(self.user, self.pw, tasks, [self.ip], callback=callback)
        encrypt_util.aes_cipher.remove_temp_file(parking_security_config_path)
        if p != 0:
            raise deploy_switch.DeployError(self.sn, 'push security configs',
                                            'failed in deployed with error code %s' % p)

    def start_push_full_config(self):
        callback = ResultCallback(self.sn)

        tasks = self.install_license()

        if tasks:
            p1 = run_tasks(self.user, self.pw, tasks, [self.ip], callback=callback)
            if p1 != 0:
                raise deploy_switch.DeployError(self.sn, 'install license',
                                                'failed in deployed with error code %s' % p1)
            # db.update_step(self.sn, 3)
        # upgrade_tasks = self.upgrade_image()
        # tasks = upgrade_tasks
        update_switch_version_task = self.conform_switch_version()
        if update_switch_version_task:
            p1 = run_tasks(self.user, self.pw, update_switch_version_task, [self.ip], callback=callback)
            if p1 != 0:
                raise deploy_switch.DeployError(self.sn, 'update switch version', 'failed in deployed with error code %s' % p1)
            # db.update_step(self.sn, 4)
        
        self.update_switch_mac()
        #
        #     tasks = []
        #     if upgrade_tasks:
        #         # first sleep 240s continue
        #         self.wait_switch()
        #         show_image_version_task = shell_task('show image version', 'version', tags='4')
        #         tasks.append(show_image_version_task)
        task, security_config_path = self.push_full_configs()
        tasks += task
        tasks += self.change_file_flag('.*', 'push_config', [])
        p2 = run_tasks(self.user, self.pw, tasks, [self.ip], callback=callback)
        # remove decrypted security config
        encrypt_util.aes_cipher.remove_temp_file(security_config_path)
        if p2 != 0:
            raise deploy_switch.DeployError(self.sn, 'push full configs', 'failed in deployed with error code %s' % p2)
        # db.update_step(self.sn, 5)

        # if uplink type is manage no need change ip
        if self.uplink_type == 0:
            self.ip = self.switch_info.mgt_ip
        # if the IP is VPN IP, in future, the VPN IP should be the mgt-ip
        if self.uplink_type == 2:
            inven_db.update_model(inventory.Switch, {'sn': [self.sn]}, {'mgt_ip': self.ip})

        config_lines_num = len(self.full_config.split('\n')) if self.full_config else 0
        sleep_time = 240 if config_lines_num <= 500 else 300
        self.wait_switch(timeout=sleep_time, use_specified_mgt_ip_tag=True)
        tasks = self.validate_full_configs()
        patch_task = self.install_patched_tar_file()
        tasks += patch_task
        # enable gnmi
        if self.switch_info.platform_model not in constants.BUSY_BOX_MODEL_LIST:
            enable_gnmi_task = shell_task('enable gnmi task',
                                            '/pica/bin/pica_sh -c "configure;set protocols grpc enable true;set protocols lldp enable true;set protocols netconf;commit"')
            tasks += [enable_gnmi_task]
        p3 = run_tasks(self.user, self.pw, tasks, [self.ip], callback=callback)
        if p3 != 0:
            raise deploy_switch.DeployError(self.sn, 'tar install',
                                            'failed in deployed with error code %s' % p3)
        if patch_task:
            self.wait_switch(timeout=REBOOT_SLEEP_TIME * 2, use_specified_mgt_ip_tag=True)
        tasks = self.validate_tar_install()
        tasks += self.retrive_switch_config()
        tasks += self.update_switch_hostname()
        tasks += self.change_file_flag('.*', 'deployed', '6')
        # stop vpn if not vpn keepalive
        if self.uplink_type == 2:
            tasks += self.stop_switch_vpn()
        # tasks += self.ensure_save_config_success()
        p4 = run_tasks(self.user, self.pw, tasks, [self.ip], callback=callback)
        if p4 != 0:
            raise deploy_switch.DeployError(self.sn, 'validate tar install',
                                            'failed in deployed with error code %s' % p4)
        # db.update_step(self.sn, 6)
        # tasks += self.remove_ansible_temp()

    def ensure_save_config_success(self):
        save_config_tasks = []
        if utils.switch_need_save_config(self.model):
            run_save_config_task = shell_task('save_config after complete', 'sudo save_config',
                                              register='save_re', ignore_errors=True,
                                              retries=3, delay=10, until='save_re | success')
            delete_auto_flag_if_save_failed = file_task('delete auto flag', {"path": FLAG_FILE, "state": "absent"},
                                                        when='save_re : failed')
            reboot_switch_if_save_failed = shell_task('reboot switch after save failed',
                                                      "nohup sh -c 'sleep 3 && sudo reboot' &",
                                                      when='save_re | failed')
            save_config_tasks = [run_save_config_task, delete_auto_flag_if_save_failed, reboot_switch_if_save_failed]
        return save_config_tasks

    def update_switch_hostname(self):
        return [shell_task('show hostname', 'hostname')]

    def stop_switch_vpn(self):
        LOG.info('Processing VPN config after deployed!')
        tasks = []
        # if cfg.CONF.vpn_keepalive:
        #     return tasks
        if 'vpn_option' in yaml.full_load(self.switch_info.post_deployed_config).keys():
            vpn_option = yaml.full_load(self.switch_info.post_deployed_config)['vpn_option']
        else:
            vpn_option = 'Persistent VPN'

        if vpn_option == 'Persistent VPN':
            LOG.info('Keep VPN Connection in later!')
            return tasks
        elif vpn_option == 'Disable VPN':
            LOG.info('Will disable the VPN in later bootup')
            tasks.append(lineinfile_task('disable vpn', args={"path": FLAG_FILE, "line": 'vpn_enable = False',
                                                              "regexp": '^vpn_enable', "create": "yes"}))
            tasks.append(shell_task('change RW for /opt/auto-deploy/', "sudo chmod -R 777 /opt/auto-deploy"))
            tasks.append(
                lineinfile_task('disable vpn in auto_config', args={"path": AGENT_CONF, "line": 'vpn_enable = False',
                                                                    "regexp": '^vpn_enable'}))
            if utils.switch_need_save_config(self.model):
                tasks.append(shell_task('run save_config task', 'sudo save_config'))
            tasks.append(shell_task('stop vpn', 'sudo service openvpn stop'))
            return tasks
        elif vpn_option == 'Polling VPN':
            LOG.info('Keep Polling VPN mode')
            tasks.append(shell_task('stop vpn', 'sudo service openvpn stop'))
            return tasks
        else:
            return tasks

    def start(self):
        # avoid this child process deadlock, clear log handler lock
        # log.clear_handler_lock()
        self.switch_info = inven_db.get_model(inventory.Switch, filters={'sn': [self.sn]})

        try:
            self.running = True
            if self.switch_info.step < 2:
                self.start_push_security_config()
                # time.sleep(100)
                # after push security configs change switch user,password
                self.user = self.mgt_sys_config.switch_op_user
                self.pw = self.mgt_sys_config.switch_op_password
            self.wait_switch(timeout=20)
            self.start_push_full_config()
        finally:
            self.running = False

    def start_rma(self):
        # avoid this child process deadlock, clear log handler lock
        # log.clear_handler_lock()
        self.switch_info = inven_db.get_model(inventory.Switch, filters={'sn': [self.sn]})

        try:
            self.running = True
            if not inven_db.get_model(inventory.DeployedSecuritySwitch, filters={'sn': [self.sn]}):
                self.user = constants.DEFAULT_USER
                self.pw = constants.DEFAULT_PASSWORD

            tasks = self.install_license()
            # tasks += self.chmod_ansible_temp()
            # upgrade_tasks = self.upgrade_image()
            # tasks += upgrade_tasks
            callback = ResultCallback(self.sn)
            if tasks:
                p1 = run_tasks(self.user, self.pw, tasks, [self.ip], callback=callback)
                if p1 != 0:
                    raise deploy_switch.DeployError(self.sn, 'install license or upgrade image', 'failed in deployed')

            update_switch_version_task = self.conform_switch_version()
            if update_switch_version_task:
                p1 = run_tasks(self.user, self.pw, update_switch_version_task, [self.ip], callback=callback)
                if p1 != 0:
                    raise deploy_switch.DeployError(self.sn, 'update switch version',
                                                    'failed in deployed with error code %s' % p1)

            self.update_switch_mac()
                # if upgrade_tasks:
                #     # first sleep 240s continue
                #     self.wait_switch()

            tasks += self.push_rma_full_configs()
            tasks += self.change_file_flag('.*', 'rma_push_config', [])
            p2 = run_tasks(self.user, self.pw, tasks, [self.ip], callback=callback)
            if p2 != 0:
                raise deploy_switch.DeployError(self.sn, 'load overwrite rma config', 'failed in deployed')

            # if uplink type is manage no need change ip
            if self.uplink_type == 0:
                self.ip = self.switch_info.mgt_ip
            # if the IP is VPN IP, in future, the VPN IP should be the mgt-ip
            if self.uplink_type == 2:
                inven_db.update_model(inventory.Switch, {'sn': [self.sn]}, {'mgt_ip': self.ip})

            self.user = self.mgt_sys_config.switch_op_user
            self.pw = self.mgt_sys_config.switch_op_password

            self.wait_switch(timeout=300, use_specified_mgt_ip_tag=True)

            # config user
            tasks = []
            # update switch user
            for line in self.mgt_sys_config.get_security_config_content().split('\n'):
                finds = re.findall('user\s+([^\s]+).*plain-text-password\s+(.*)$', line)
                if finds and len(finds[0]) == 2:
                    config_user_task = shell_task('execute security config',
                                                  '/pica/bin/pica_sh -c "configure;%s;commit"'% line)
                    tasks.append(config_user_task)

            tasks += self.validate_full_configs(name='rma full config validate')
            inven_db.add_deployed_security(self.sn)
            patch_task = self.install_patched_tar_file()
            tasks += patch_task
            # enable gnmi
            if self.switch_info.platform_model not in constants.BUSY_BOX_MODEL_LIST:
                enable_gnmi_task = shell_task('enable gnmi task',
                                                '/pica/bin/pica_sh -c "configure;set protocols grpc enable true;set protocols lldp enable true;set protocols netconf;commit"')
                tasks += [enable_gnmi_task]
            p3 = run_tasks(self.user, self.pw, tasks, [self.ip], callback=callback)
            if p3 != 0:
                raise deploy_switch.DeployError(self.sn, 'tar install',
                                                'failed in deployed with error code %s' % p3)
            if patch_task:
                self.wait_switch(timeout=REBOOT_SLEEP_TIME * 2, use_specified_mgt_ip_tag=True)

            tasks = self.validate_tar_install()
            tasks += self.retrive_switch_config()
            tasks += self.update_switch_hostname()
            tasks += self.change_file_flag('.*', 'deployed', '6')
            # stop vpn if not vpn keepalive
            tasks += self.stop_switch_vpn()
            p4 = run_tasks(self.user, self.pw, tasks, [self.ip], callback=callback)
            if p4 != 0:
                raise deploy_switch.DeployError(self.sn, 'validate rma full configs', 'failed in deployed')
        finally:
            self.running = False

    def update_mgt_ip(self, use_specified_mgt_ip_tag):
        ip = inven_db.get_model(inventory.Switch, filters={'sn': [self.sn]}).tmp_ip
        if use_specified_mgt_ip_tag and not cfg.CONF.vpn_enable:
            self.ip = self.switch_info.mgt_ip
        else:
            if ip != self.ip:
                LOG.warn('The switch IP is changed to %s', ip)
                self.ip = ip

    def wait_switch(self, timeout=REBOOT_SLEEP_TIME, use_specified_mgt_ip_tag=False):
        LOG.info('%s: start to wait switch %s seconds', self.sn, timeout)
        time.sleep(timeout)
        LOG.info('%s wake up', self.sn)
        # The first things is to update the mgt IP from database, maybe it is changed after reboot
        self.update_mgt_ip(use_specified_mgt_ip_tag)
        fail_count = 0
        while fail_count < 10:
            if self.ensure_switch_ready([self.ip], self.user, self.pw, self.sn,
                                        use_specified_mgt_ip_tag=use_specified_mgt_ip_tag, retry_times=2):
                return

            # tmp_ip = inven_db.get_model(inventory.Switch, filters={'sn': [self.sn]}).tmp_ip
            # if tmp_ip == self.ip:
            #    raise deploy_switch.DeployError(self.sn, 'push full configs',
            #                                    'failed in deployed with error [%s unreachable]' % self.ip)

            # self.ip = tmp_ip
            fail_count += 1

        raise deploy_switch.DeployError(self.sn, 'waitting for reboot or push full config',
                                        'failed in deployed with error [%s unreachable]' % self.ip)

    def ensure_switch_ready(self, host, username, password, sn, retry_interval=30, retry_times=-1, use_specified_mgt_ip_tag=False):
        # We need check whether the ip is changed, otherwise, use new ip
        tmp_ip = inven_db.get_model(inventory.Switch, filters={'sn': [sn]}).tmp_ip
        if not (use_specified_mgt_ip_tag and not cfg.CONF.vpn_enable):
            if host != [tmp_ip]:
                LOG.warn('The switch IP is changed to %s, change to new IP', tmp_ip)
                host = [tmp_ip]
                self.ip = tmp_ip
        while retry_times != 0:
            flag = run_ping(username, password, host, sn)
            if flag:
                LOG.warn('The switch IP %s in %s is reachable now.', host, sn)
                return True
            retry_times -= 1
            time.sleep(retry_interval)

        return False


@my_celery_app.task(name="start_push_security_config", base=AmpConBaseTask)
def celery_start_push_security_config(info, **kwargs):
    ansible_deploy_switch = AnsibleDeploySwitch(info)
    ansible_deploy_switch.check_register()
    ansible_deploy_switch.start_push_security_config()


@my_celery_app.task(name="start_push_parking_security_config", base=AmpConBaseTask)
def celery_start_push_parking_security_config(info, **kwargs):
    ansible_deploy_switch = AnsibleDeploySwitch(info)
    ansible_deploy_switch.start_push_parking_security_config()


@my_celery_app.task(name="start_ansible_deploy", base=AmpConBaseTask)
def celery_start_ansible_deploy(info, **kwargs):
    ansible_deploy_switch = AnsibleDeploySwitch(info)
    ansible_deploy_switch.check_register()
    ansible_deploy_switch.start()


@my_celery_app.task(name="start_rma", base=AmpConBaseTask)
def celery_start_rma(info, **kwargs):
    ansible_deploy_switch = AnsibleDeploySwitch(info)
    ansible_deploy_switch.check_register()
    ansible_deploy_switch.start_rma()
