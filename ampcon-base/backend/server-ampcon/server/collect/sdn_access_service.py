import os
# from ryu.cmd import manager
# import logging
# import requests

base_dir = os.path.dirname(os.path.abspath(__file__))

def sdn_access_service():
    os.system('ryu-manager ryu.app.ofctl_rest sdn_access/sdn_access_rest.py &')
    # manager.main(['ryu.app.ofctl_rest', 'sdn_access/sdn_access_rest.py'])

def sdn_access_sync_service():
    pass
    #sync_url = 'http://localhost:8080/v1.0/HostAccessController/sync_all_switch_status'
    #r = requests.get(sync_url)