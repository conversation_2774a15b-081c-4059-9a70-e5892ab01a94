import os
import sys
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(BASE_DIR)

import time
import logging
import re

from server.south_api.ovsdb import ovsdb
from server.south_api.ovsdb import ovsdb_client
from server.db.models.sdn_access import sdn_access_db, HostAccessControl

logging.basicConfig(format='%(asctime)s - %(pathname)s[line:%(lineno)d] - %(levelname)s: %(message)s', level=logging.DEBUG)
LOG = logging.getLogger('mac_learn_sync')

mac_reg = re.compile('^([0-9a-f]{2}:){5}[0-9a-f]{2}$', re.I)
mac_l_hosts = {}
interval = 10
pool = None


def sync_main():
    global pool
    import eventlet
    eventlet.monkey_patch()
    pool = eventlet.GreenPool(1000)
    while True:
        sync_mac_l()
        time.sleep(10)


def sync_mac_l():
    LOG.debug('start sync all switches mac_l table')
    start = time.time()
    switches = sdn_access_db.get_switches()
    config_hosts = sdn_access_db.get_config_hosts()
    config_hosts = dict((config_host.smac, config_host.id) for config_host in config_hosts)
    for switch in switches:
        pool.spawn_n(sync_switch_mac_l, switch.sn, switch.mgt_ip, config_hosts)
    pool.waitall()
    LOG.info('finish sync all switches mac_l table, consume time %d', time.time() - start)


def sync_switch_mac_l(sn, ip, config_hosts):
    global mac_l_hosts
    LOG.debug('start sync %s ip:%s mac_l table', sn, ip)
    start = time.time()

    try:
        mac_l = mac_l_hosts.get(sn, None)
        if not mac_l:
            mac_l = ovsdb.MacLearning(sn, ip)
            mac_l_hosts[sn] = mac_l

        mac_vlans = mac_l.get_mac_learning()
        finish_sync_ovsdb = time.time()
        LOG.debug('%s ip:%s finish sync ovsdb, consume time %d, mac_vlans:%d', sn, ip,
                 finish_sync_ovsdb - start, len(mac_vlans))
        db_mac_vlans, db_macs, db_mac_ids = sdn_access_db.get_switch_mac_ls(sn)
        LOG.debug('%s ip:%s db macs %d', sn, ip, len(db_macs))
        add = []
        update = []
        have_macs = set()
        switch_ovsdb_client = ovsdb_client.get_mac_vlan_client(sn, ip)
        for mac_vlan_row in mac_vlans:
            ifname = mac_vlan_row.ifname
            mac = mac_vlan_row.MAC
            if mac_reg.match(mac) is None or mac in have_macs:
                continue
            # type = mac_vlan_row.type
            vlan = int(mac_vlan_row.vlan)

            have_macs.add(mac)
            if mac in config_hosts:
                LOG.debug('update mac %s, vlan %d, ifname %s', mac, vlan, ifname)
                update.append({'id': config_hosts[mac], 'smac': mac, 'located_switch': sn,
                               'assigned_vlan_id': vlan, 'port_num': ifname,
                               'online': True})
            elif mac not in db_macs:
                # insert
                LOG.debug('insert mac %s, vlan %d, ifname %s', mac, vlan, ifname)
                add.append({'smac': mac, 'located_switch': sn, 'assigned_vlan_id': vlan, 'port_num': ifname,
                            'online': True})
                if len(add) >= 1000:
                    sdn_access_db.bulk_insert_mac_l(add)
                    add = []
            elif (mac, sn, vlan, ifname, True) not in db_mac_vlans:
                # update
                LOG.debug('update mac %s, vlan %d, ifname %s', mac, vlan, ifname)
                host_db_id, d_switch, d_vlan, qos = db_mac_ids[mac]
                update.append({'id': host_db_id, 'smac': mac, 'located_switch': sn,
                               'assigned_vlan_id': vlan, 'port_num': ifname,
                               'online': True})

                # if l_switch != sn, host located_switch had changed
                if d_switch != sn:
                    switch_ovsdb_client.update_mac_vlan(mac, d_vlan, priority=int(qos))

            if len(update) >= 1000:
                sdn_access_db.bulk_update_mac_l(update)
                update = []

        if len(add) > 0:
            sdn_access_db.bulk_insert_mac_l(add)
        if len(update) > 0:
            sdn_access_db.bulk_update_mac_l(update)
        if len(db_macs) > 0:
            no_use_macs = db_macs - have_macs
            for mac in no_use_macs:
                if db_mac_ids[mac][2] is not None:
                    switch_ovsdb_client.del_mac_vlan(mac)
            sdn_access_db.del_no_use_macs(db_macs - have_macs)
        LOG.debug('%s ip:%s finish sync db, consume time %d', sn, ip, time.time() - finish_sync_ovsdb)
    except Exception as e:
        LOG.exception(e)


if __name__ == '__main__':
    from server.db.models.automation import AnsibleJobResult
    sync_main()
