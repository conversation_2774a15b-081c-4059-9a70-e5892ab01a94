# coding=utf-8
import logging
import re
import re, json, datetime
from server.ansible_lib import ansible_common
from server.db.models.inventory import Switch, SwitchGlobalStatus
from server.db.models.inventory import inven_db
from ansible.plugins.callback import CallbackBase

from server.util import ssh_util as client, http_client
from server.util import utils
from server import constants
import concurrent.futures
from functools import partial


LOG = logging.getLogger(__name__)


class CollectStatusCallback(CallbackBase):
    def __init__(self, host_map):
        super(CollectStatusCallback, self).__init__()
        self.host_map = host_map
        self.handler = dict()

    def register_handler(self, task_name, handler):
        self.handler.update({task_name: handler})

    def v2_runner_on_ok(self, result, **kwargs):
        host = str(result._host)
        sn = self.host_map[host]
        LOG.info('collect switch [sn:%s,host:%s] lldp and status success', sn, host)
        inven_db.update_switch_ansible_status(sn, "reachable")
        handler = self.handler.get(result.task_name)
        if callable(handler):
            handler(sn, result)

    def v2_runner_on_failed(self, result, **kwargs):
        host = result._host
        sn = self.host_map[host]
        msg = result._result['msg'] or result._result['stderr']
        LOG.info('collect switch [sn:%s,host:%s] configs failed [%s]', sn, host, msg)
        inven_db.update_switch_ansible_status(sn, "un-reachable")

    def v2_runner_on_unreachable(self, result, **kwargs):
        host = result._host
        sn = self.host_map[host]
        LOG.info('collect switch [sn:%s,host:%s] configs failed, switch unreachable', sn, host)
        inven_db.update_switch_ansible_status(sn, "un-reachable")


def update_port_status(sn, result):
    port_info = {}
    port_info_str_list = result._result["stdout"].split("\nPhysical ")
    for port_info_str in port_info_str_list:
        port_name = re.findall("interface: (.e-1/1/[0-9]+)", port_info_str)[0]
        link = re.findall("Physical link is (Down|Up)", port_info_str)[0]
        speed = re.findall("Speed: (Auto|100M|1Gb|10Gb|25Gb|40Gb|100Gb)", port_info_str)[0]
        input_oct = re.findall("Input Octets.............................([0-9]+)", port_info_str)[0]
        output_oct = re.findall("Output Octets............................([0-9]+)", port_info_str)[0]
        input_drop_oct = re.findall("Discarded Packets......................([0-9]+)", port_info_str)[1]
        output_drop_oct = re.findall("Discarded Packets......................([0-9]+)", port_info_str)[0]
        if_type = "RJ45"
        port_info.update({port_name: {"link": link,
                                      "speed": speed,
                                      "inputOct": input_oct,
                                      "outputOct": output_oct,
                                      "inputDropOct": input_drop_oct,
                                      "outputDropOct": output_drop_oct,
                                      "type": if_type}})
    inven_db.update_switch_status(sn, port_info)
    return


def update_lldp_neighbor(sn, result):
    lldp_info = {}
    lldp_info_str_list = result._result["stdout"].split("\nTotal entries displayed: ")
    for lldp_info_str in lldp_info_str_list:
        if re.findall("Local Port: (.e-1/1/[0-9]+)", lldp_info_str):
            port_name = re.findall("Local Port: (.e-1/1/[0-9]+)", lldp_info_str)[0]
            lldp_neighbor = lldp_info_str
            lldp_info.update({port_name: {"lldpNeighbor": lldp_neighbor}})
    inven_db.update_switch_lldp(sn, lldp_info)
    return


def update_mac_table(sn, result):
    total_mac = 0
    total_mac = int(re.findall("Total entries in switching table:   ([0-9]+)", result._result["stdout"])[0])
    session = inven_db.get_session()
    with session.begin(subtransactions=True):
            global_status = session.query(SwitchGlobalStatus).filter(SwitchGlobalStatus.sn == sn).first()
            if global_status:
                global_status.mac_learned = total_mac
            else:
                global_status = SwitchGlobalStatus()
                global_status.sn = sn
                global_status.copp = ''
                global_status.voice_device = 0
                global_status.dot1x = ""
                global_status.mac_learned = total_mac
                session.add(global_status)
    return


def update_copp_status(sn, result):
    copp_status = {}
    copp_arry = result._result["stdout"].split("\n\n")
    copp_summary = ''
    for copp_entry in copp_arry:
        traffic_class = re.findall("(.*) Traffic statistics", copp_entry)[0]
        input_pps = re.findall("bits/sec, ([0-9]+) packets/sec", copp_entry)[0]
        input_drop_pps = re.findall("bits/sec, ([0-9]+) packets/sec", copp_entry)[1]
        copp_status.update({traffic_class: [input_pps,input_drop_pps]})
        if traffic_class == 'All Copp':
            copp_summary = re.findall("bits/sec, ([0-9]+) packets/sec", copp_entry)[0]
            #copp_summary += ';'
            #copp_summary += re.findall("bits/sec, ([0-9]+) packets/sec", copp_entry)[1]
            #copp_summary += ';'
            #copp_summary += re.findall("Input Packets............................([0-9]+)", copp_entry)[0]
            #copp_summary += ';'
            #copp_summary += re.findall("Drop Packets.............................([0-9]+)", copp_entry)[0]
    session = inven_db.get_session()
    with session.begin(subtransactions=True):
        global_status = session.query(SwitchGlobalStatus).filter(SwitchGlobalStatus.sn == sn).first()
        if global_status:
            # currently, just save copp summary
            # global_status.copp = json.dumps(copp_status)
            global_status.copp = copp_summary
        else:
            global_status = SwitchGlobalStatus()
            global_status.sn = sn
            global_status.voice_device = 0
            global_status.dot1x = ""
            global_status.mac_learned = 0
            # global_status.copp = json.dumps(copp_status)
            global_status.copp = copp_summary
            session.add(global_status)
    return

# year (int|str) – 4-digit year
# month (int|str) – month (1-12)
# day (int|str) – day of the (1-31)
# week (int|str) – ISO week (1-53)
# day_of_week (int|str) – number or name of weekday (0-6 or mon,tue,wed,thu,fri,sat,sun)
# hour (int|str) – hour (0-23)
# minute (int|str) – minute (0-59)
# econd (int|str) – second (0-59)
#
# start_date (datetime|str) – earliest possible date/time to trigger on (inclusive)
# end_date (datetime|str) – latest possible date/time to trigger on (inclusive)
# timezone (datetime.tzinfo|str) – time zone to use for the date/time calculations (defaults to scheduler timezone)
#
# *    any    Fire on every value
# */a    any    Fire every a values, starting from the minimum
# a-b    any    Fire on any value within the a-b range (a must be smaller than b)
# a-b/c    any    Fire every c values within the a-b range
# xth y    day    Fire on the x -th occurrence of weekday y within the month
# last x    day    Fire on the last occurrence of weekday x within the month
# last    day    Fire on the last day within the month
# x,y,z    any    Fire on any matching expression; can combine any number of any of the above expressions
# every 3 days 22:00 start to run
# @scheduler.task('cron', id='collect_switch_status', day='*/3', hour='22', minute='0', second=0, executor='processpool')


def collect_switch_status():
    # switches = inven_db.get_collection(Switch, filters={'status': ['Provisioning Success']})
    # clear some old history data in DB
    session = inven_db.get_session()
    current_time = datetime.datetime.now()
    session.query(SwitchGlobalStatus).filter(SwitchGlobalStatus.upload_time < current_time - datetime.timedelta(minutes=120))\
        .delete(synchronize_session=False)
    switches = inven_db.get_collection(Switch, filters={'status': [constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                                   constants.SwitchStatus.IMPORTED]}, session=session)
    if not switches:
        return
    # host_map = dict()
    # map(lambda switch: host_map.update({switch.mgt_ip: switch.sn}), switches)
    # tasks = [ansible_common.command_task('get port status',
    #                                      '/pica/bin/sif/tools/print_interfaces -d'),
    #          ansible_common.command_task('get lldp',
    #                                      '/pica/bin/lldp/tools/print_lldp -m LLDP_NEIGHBOR -i all -d detail'),
    #          ansible_common.command_task('get mac address brief',
    #                                      '/pica/bin/lcmgr/tools/print_fdb -b -f 2'),
    #          ansible_common.command_task('get copp',
    #                                      '/pica/bin/sif/tools/print_copp -s')]
    # callback = CollectStatusCallback(host_map)
    # callback.register_handler('get port status', update_port_status)
    # callback.register_handler('get lldp', update_lldp_neighbor)
    # callback.register_handler('get mac address brief', update_mac_table)
    # callback.register_handler('get copp', update_copp_status)
    #ansible_common.run_tasks(user, pw, tasks, host_map.keys(), callback)
    #current, use parameko lib to get status, infutrue, maybe ansible is better
    # result = {}
    # session = inven_db.get_session()
    #
    # threads = []
    # count = 0
    pool = concurrent.futures.ThreadPoolExecutor(15)
    sns = []
    ips = []
    for switch in switches:
        sns.append(switch.sn)
        ips.append(switch.mgt_ip)
    LOG.debug('get need collect status switches %s', sns)
    pool.map(switch_status_collector_api, sns, ips)
    pool.shutdown(wait=True)
    # clear pool
    pool = None
    # for switch in switches:
        # t_collect = threading.Thread(target=switch_status_collector_api,
        #                              args=(switch.sn,switch.mgt_ip,user,pw))
        # threads.append(t_collect)
        # count+=1
        # if len(threads)<15 and count != len(switches):
        #     continue
        # else:
        #     for t in threads:
        #         t.setDaemon(True)
        #         t.start()
        #     t.join()
        #     #clear the threads finished
        #     threads = []

        # retry_times = 0
        # done = False
        # while not done and retry_times < 2:
        #     content, code = client.interactive_shell_linux('python /opt/auto-deploy/status_collector.py',
        #                                                         switch.mgt_ip, username=user, password=pw)
        #     if code == constants.RMA_ACTIVE:
        #         done = True
        #         db.update_model(Switch, filters={'sn': [switch.sn]}, updates={Switch.rma_status: constants.RMA_ACTIVE},
        #                         session=session)
        #         break
        #     retry_times += 1
        # if not done:
        #     result[switch.sn] = constants.RMA_FAILED
        #     continue
        #
        # if code == constants.RMA_UN_REACHABLE:
        #     db.update_model(Switch, filters={'sn': [switch.sn]}, updates={Switch.rma_status: constants.RMA_UN_REACHABLE},
        #                     session=session)
        #     result[switch.sn] = constants.RMA_UN_REACHABLE
        #     continue


def switch_status_collector_api(sn, ip):
    LOG.debug('start collect switch %s,ip %s', sn, ip)
    retry_times = 0
    done = False
    while not done and retry_times < 2:
        config = inven_db.get_system_config_by_sn(sn)
        content, code = client.interactive_shell_linux('python3 /opt/auto-deploy/status_collector.py',
                                                       ip, username=config.switch_op_user, password=config.switch_op_password)
        if code == constants.RMA_ACTIVE:
            done = True
            LOG.debug('collect switch %s status ok, result:[%s]', sn, content)
            inven_db.update_model(Switch, filters={'sn': [sn]}, updates={Switch.reachable_status: constants.REACHABLE})
            break
        retry_times += 1

    LOG.warning('collect switch %s failed %s', sn, content)
    if code == constants.RMA_UN_REACHABLE:
        inven_db.update_model(Switch, filters={'sn': [sn]}, updates={Switch.reachable_status: constants.UN_REACHABLE})