import jinja2
import re

from server.db.models.inventory import inven_db, SwitchAutoConfig
from server.db.models.automation import Ansible<PERSON><PERSON>R<PERSON>ult
from server.db.models import general
from server.util import str_helper


env = jinja2.Environment(loader=jinja2.FileSystemLoader(searchpath="."),
                         trim_blocks=True,
                         lstrip_blocks=True)


class StringLoader(jinja2.BaseLoader):
    def get_source(self, environment, template):

        db_temp = general.general_db.get_model(general.GeneralTemplate, filters={"name": [template]})

        if not db_temp or db_temp == '':
            raise jinja2.TemplateNotFound(template)

        def uptodate():
            try:
                return False
            except OSError:
                return False

        return db_temp.j2_template, None, uptodate


def generate_multiple_config_list(info):
    result = []
    if not info or not (info.get('data') or info.get('type')):
        return result
    string_env = jinja2.Environment(loader=StringLoader(),
                                    trim_blocks=True,
                                    lstrip_blocks=True)
    # for page Config/Templates > Template List > Create Config
    if info.get('type') == 'generate_config':
        name = info['no_generate_template_name']
        template_env = string_env.get_template(name)
        params = str_helper.parse_form_params(info)
        result.extend(template_env.render(params).split('\n'))
    # for Config/Templates > Config Files View > select a config file
    elif info.get('type') == 'config_file':
        config = general.general_db.get_model(general.GeneralConfig, filters={'name': [info['config_name']]})
        result.extend(config.content.split('\n'))
    # for Deployment > Switch Configuration
    else:
        # global
        config = inven_db.get_model(SwitchAutoConfig, filters={'name': [info.get('data')[0].get('no_generate_global_config')]})
        config_str = config.config
        result.extend(config_str.split('\n'))
        # site
        for i in info.get('data'):
            name = i['no_generate_template_name']
            template_env = string_env.get_template(name)
            params = str_helper.parse_form_params(i)
            config_str = template_env.render(params)
            result.extend(config_str.split('\n'))
    return result


def get_switch_model_by_template_name(name):
    return re.search('glob-(.*?)-', name).group(1)
