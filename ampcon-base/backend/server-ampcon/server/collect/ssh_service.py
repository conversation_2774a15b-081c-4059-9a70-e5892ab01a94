import json
import logging
import os
import re
import struct
import sys
import time
import weakref
from urllib import parse

import paramiko
import tornado.ioloop
import tornado.web
from tornado.ioloop import <PERSON><PERSON>oop
from tornado.options import options
from webssh import main
from webssh.handler import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, InvalidValueError
from webssh.settings import (
    get_app_settings, get_host_keys_settings, get_policy_setting,
    get_ssl_context, get_server_settings)
from webssh.utils import UnicodeType
from webssh.worker import clients

BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(BASE_DIR)

from server import cfg
from server import constants
from server.collect.utils import generate_multiple_config_list
from server.db.models import inventory
from server.db.models.inventory import inven_db
from server.db.models.monitor import monitor_db

try:
    from json.decoder import JSONDecodeError
except ImportError:
    JSONDecodeError = ValueError

base_dir = os.path.dirname(os.path.abspath(__file__))

LOG = logging.getLogger(__name__)


def get_switch_model_by_hardware_model(hardware_model):
    hardware_mapping_query = inventory.inven_db.get_session().query(inventory.HardwareMapping)
    hardware_mapping = {}
    for hardware_mapping_item in hardware_mapping_query:
        hardware_mapping[hardware_mapping_item.hardware_model] = hardware_mapping_item.switch_model
    known_hardware_list = hardware_mapping.keys()
    for model in known_hardware_list:
        if model in hardware_model:
            return hardware_mapping[model]
    return hardware_model


def parse_url_param_to_dict(url):
    if '&' not in url:
        return None
    # for http://*****&{} json format
    elif '{' in url:
        param = url.split('?')[-1].split('&')[-1]
        return json.loads(param.split(';')[-1])
    # for http://*****&**&** form format
    else:
        params = url.split('?')[-1].split('&')[1:]
        return {param.split('=')[0]: param.split('=')[1] for param in params}


class My_IndexHandler(IndexHandler, tornado.web.RequestHandler):

    def get(self):
        self.render('ssh_service.html', debug=self.debug)


class download_script(IndexHandler, tornado.web.RequestHandler):
    def get(self):
        path = 'onie_install/start.sh'
        with open(path, 'rb') as f:
            while 1:
                data = f.read(16384)  # or some other nice-sized chunk
                if not data: break
                self.write(data)
        self.finish()


class download_file(IndexHandler, tornado.web.RequestHandler):
    def post(self):
        file_name = self.get_argument("file_name", "")
        sn_info = self.get_argument("sn_eeprom", None)
        if sn_info and file_name == 'auto-deploy.conf':
            conf_str = inven_db.get_switch_agent_conf(sn_info)
            if conf_str:
                self.write(conf_str)
                self.finish()
                return

        hardware_model = self.get_argument("hardware", "").strip()
        sysinfo = self.get_argument("sysinfo", "").strip()
        switch_model = get_switch_model_by_hardware_model(hardware_model)

        LOG.info('download_file %s %s', file_name, switch_model)
        if switch_model == "":
            # will try the sysinfo parameter to get hardware model
            hardware_model = re.findall("((as|n)[0-9a-zA-Z]+(_|-)[0-9a-zA-Z]+)", sysinfo)[0][0]
            switch_model = get_switch_model_by_hardware_model(hardware_model)

            if switch_model == "":
                self.finish('Can not get the model information from eeprom string!')
                return

        try:
            if file_name == 'auto-deploy.py':
                path = f'{constants.AUTOMATION_BASE_DIR}/server/agent/auto-deploy.py'
            else:
                path = 'config_gen/' + switch_model + '/' + file_name
            with open(path, 'rb') as f:
                while 1:
                    data = f.read(16384)  # or some other nice-sized chunk
                    if not data: break
                    self.write(data)
            self.finish()
        except Exception as e:
            return str(e)


class download_onie(IndexHandler, tornado.web.RequestHandler):
    def post(self):
        sn_info = self.get_argument("sn_eeprom", "")
        sysinfo = self.get_argument("sysinfo", "").strip()
        hardware_model = self.get_argument("hardware", "").strip()
        switch_model = get_switch_model_by_hardware_model(hardware_model)
        LOG.info('download_onie %s %s', sn_info, switch_model)
        if switch_model == "":
            # will try the sysinfo parameter to get hardware model
            hardware_model = re.findall("((as|n|s)[0-9a-zA-Z]+(_|-)[0-9a-zA-Z]+)", sysinfo)[0][0]
            switch_model = get_switch_model_by_hardware_model(hardware_model)

            if switch_model == "":
                self.finish('Can not get the model information from eeprom string!')
        try:
            sn = re.findall("([0-9a-zA-Z]{8,25})", sn_info)[0]
        except Exception as e:
            LOG.info("Can not get switch SN, will continue to install ONIE")

        # check whether the switch is configured
        if 'sn' in dir():
            switch = inven_db.get_switch_info_by_sn(sn)
            if not switch:
                LOG.info("from ONIE mode, switch %s haven't been configured", sn)
                monitor_db.add_event(sn, 'warn',
                                     'from ONIE mode, switch %s registered, but have not been configured' % sn)
                inven_db.update_switch_lot(sn, '0.0.0.0', switch_model)

        # we still start to install onie image to that switch, send the image and patch script
        model_info = inven_db.get_model(inventory.SwitchSystemInfo, filters={'model': [switch_model]})
        onie_file_name = model_info.up_to_date_onie_path

        try:
            path = onie_file_name
            with open(path, 'rb') as f:
                while 1:
                    data = f.read(16384)  # or some other nice-sized chunk
                    if not data: break
                    self.write(data)
            self.finish()
        except Exception as e:
            LOG.error(e)


class WsockHandlerLocal(WsockHandler):
    def open(self):
        self.src_addr = self.get_client_addr()
        logging.info('Connected from {}:{}'.format(*self.src_addr))

        workers = clients.get(self.src_addr[0])
        if not workers:
            self.close(reason='Connected from {}:{}'.format(*self.src_addr))
            return

        try:
            worker_id = self.get_value('id')
        except (tornado.web.MissingArgumentError, InvalidValueError) as exc:
            self.close(reason=str(exc))
        else:
            worker = workers.get(worker_id)
            if worker:
                workers[worker_id] = None
                self.set_nodelay(True)
                worker.set_handler(self)
                self.worker_ref = weakref.ref(worker)
                self.loop.add_handler(worker.fd, worker, IOLoop.READ)
            else:
                self.close(reason='Websocket authentication failed.')
        # generate_config_list(parse_url_param_to_dict(self.request.full_url()))
        cmd_list = generate_multiple_config_list(parse_url_param_to_dict(parse.unquote(self.request.full_url())))
        if cmd_list:
            self.on_message(json.dumps({"data": "configure \r"}))
            self.on_message(json.dumps({"data": "rollback default \r"}))
        for cmd in cmd_list:
            self.on_message(json.dumps({"data": "{}\r".format(str(cmd))}))
        if cmd_list:
            self.on_message(json.dumps({"data": "commit confirmed 60\r"}))

    def on_message(self, message):
        logging.debug('{!r} from {}:{}'.format(message, *self.src_addr))
        worker = self.worker_ref()
        try:
            msg = json.loads(message)
        except JSONDecodeError:
            return

        if not isinstance(msg, dict):
            return

        resize = msg.get('resize')
        if resize and len(resize) == 2:
            try:
                worker.chan.resize_pty(*resize)
            except (TypeError, struct.error, paramiko.SSHException):
                pass

        data = msg.get('data')
        if data and isinstance(data, UnicodeType):
            worker.data_to_dst.append(data)
            worker.on_write()
            if data == 'configure \r' or data == 'rollback default \r':
                time.sleep(2)


def make_handlers(loop, options):
    host_keys_settings = get_host_keys_settings(options)
    policy = get_policy_setting(options, host_keys_settings)

    handlers = [
        (r'/', My_IndexHandler, dict(loop=loop, policy=policy,
                                     host_keys_settings=host_keys_settings)),
        (r'/onie', download_script, dict(loop=loop, policy=policy,
                                         host_keys_settings=host_keys_settings)),
        (r'/onie-installer', download_script, dict(loop=loop, policy=policy,
                                                   host_keys_settings=host_keys_settings)),
        (r'/onie/download_file', download_file, dict(loop=loop, policy=policy,
                                                     host_keys_settings=host_keys_settings)),
        (r'/onie/download_onie', download_onie, dict(loop=loop, policy=policy,
                                                     host_keys_settings=host_keys_settings)),
        (r'/ws', WsockHandlerLocal, dict(loop=loop)),
        (r'/ssh/(.*)', tornado.web.StaticFileHandler, {"path": os.path.join(base_dir, 'static')}),
    ]
    return handlers


def change_onie_install_script_host():
    path = 'onie_install/start.sh'
    file_data = ''
    with open(path, 'r') as f:
        for line in f:
            if 'server_host=' in line:
                line = 'server_host="' + cfg.CONF.global_ip + '"'
            file_data += line

    with open(path, 'w') as f:
        f.write(file_data)


def ssh_service():
    # change_onie_install_script_host()
    options.parse_command_line(args=[])

    options['address'] = '0.0.0.0'
    options['port'] = 80
    options['fbidhttp'] = False

    app_options = get_app_settings(options)
    app_options['template_path'] = os.path.join(base_dir, 'templates')
    app_options['static_path'] = os.path.join(base_dir, 'static')
    app_options['xsrf_cookies'] = False

    loop = tornado.ioloop.IOLoop.current()
    app = main.make_app(make_handlers(loop, options), app_options)
    ssl_ctx = get_ssl_context(options)
    server_settings = get_server_settings(options)
    main.app_listen(app, options.port, options.address, server_settings)
    if ssl_ctx:
        server_settings.update(ssl_options=ssl_ctx)
        main.app_listen(app, options.sslport, options.ssladdress, server_settings)
    loop.start()


ssh_service()
