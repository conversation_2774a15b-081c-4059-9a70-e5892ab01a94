import asyncio
import logging
import hashlib
from celery import Celery
import redis.asyncio as redis
import re
import json
import cfg
import threading
import traceback
from datetime import datetime
from urllib.parse import quote

from pysnmp.carrier.asyncio.dispatch import AsyncioDispatcher
from pysnmp.carrier.asyncio.dgram import udp, udp6
from pyasn1.codec.ber import decoder
from pysnmp.proto import api
from pysnmp.hlapi.v1arch.asyncio import SnmpDispatcher, CommunityData, UdpTransportTarget, send_notification
from pysnmp.hlapi.v1arch.asyncio import ObjectIdentity, NotificationType, ObjectType, OctetString

logging.basicConfig(format='%(asctime)s - %(pathname)s[line:%(lineno)d] - %(levelname)s: %(message)s',
                    level=logging.INFO)
LOG = logging.getLogger(__name__)

_, mysql_password = re.search('//(.*)@mysql-service', cfg.CONF.database.connection).group(1).split(':')

trusted_ips = set()
PROCESSED_MESSAGES_KEY = 'processed_messages'

snmp_celery_app = Celery(
    'snmp_celery_app',
    broker="amqp://admin:{}@rabbitmq-service:5672/".format(quote(cfg.CONF.rabbitmq_pwd)),
    backend='db+mysql://automation:{}@mysql-service/celery-schedule'.format(quote(mysql_password))
)


class AlarmTrapOriginalModel:
    def __init__(self, oid, source_ip, occurrence_time, value):
        self.oid = oid
        self.source_ip = source_ip
        self.occurrence_time = occurrence_time
        self.value = value


class GeneralAlarmModel:
    __ALARM_LEVEL = {1: "error", 2: "warn", 3: "info"}

    __TRAP_MIB_DICT = {
        "*******.4.1.40989.10.16.{slotNum}.2.{moduleNum}.10": {
            "name": "Module {moduleNum} Tx of OEO equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.2.\\d+.10",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"},
                "moduleNum": {"11": "A1", "12": "A2", "13": "B1", "14": "B2", "15": "C1", "16": "C2", "17": "D1",
                              "18": "D2"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "OEO tx alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.2.{moduleNum}.11": {
            "name": "Module {moduleNum} Rx of OEO equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.2.\\d+.11",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"},
                "moduleNum": {"11": "A1", "12": "A2", "13": "B1", "14": "B2", "15": "C1", "16": "C2", "17": "D1",
                              "18": "D2"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "OEO rx alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.1.16": {
            "name": "Rx power of EDFA equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.1.16",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "EDFA rx power alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.1.17": {
            "name": "Tx power of EDFA equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.1.17",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "EDFA tx power alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.1.18": {
            "name": "Module temperature of EDFA equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.1.18",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "EDFA module temperature alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.1.19": {
            "name": "Pumping temperature of EDFA equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.1.19",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "EDFA pumping temperature alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.1.20": {
            "name": "Pumping current electric of EDFA equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.1.20",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "SOA pumping current electric alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.3.9": {
            "name": "Working channel of OLP equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.3.9",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"100": "is switched from the backup path to the main path",
                                         "200": "switched from the main path to the backup path"}},
            "description": "OLP working channel alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.3.26": {
            "name": "R1 power of OLP equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.3.26",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "alarm", "0": "normal"}},
            "description": "OLP r1 power alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.3.27": {
            "name": "R2 power of OLP equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.3.27",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "alarm", "0": "normal"}},
            "description": "OLP r2 power alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.3.28": {
            "name": "Tx power of OLP equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.3.28",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "alarm", "0": "normal"}},
            "description": "OLP tx power alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.13.23": {
            "name": "Rx power of SOA equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.13.23",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "SOA rx power alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.13.24": {
            "name": "Tx power of SOA equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.13.24",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "SOA tx power alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.13.25": {
            "name": "Module temperature of SOA equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.13.25",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "SOA module temperature alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.13.26": {
            "name": "Pumping temperature of SOA equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.13.26",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "SOA pumping temperature alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.13.27": {
            "name": "Pumping current electric of SOA equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.13.27",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "SOA pumping current electric alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.11.29": {
            "name": "Input power of Raman equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.11.29",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "Raman input power alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.11.30": {
            "name": "Output power of Raman equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.11.30",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "Raman output power alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.11.31": {
            "name": "Module temperature of Raman equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.11.31",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "Raman module temperature alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.11.32": {
            "name": "Pumping1 temperature of Raman equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.11.32",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "Raman pumping1 temperature alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.11.33": {
            "name": "Pumping2 temperature of Raman equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.11.33",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "Raman pumping2 temperature alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.11.34": {
            "name": "Pumping1 current electric of Raman equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.11.34",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "Raman pumping1 current electric alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.11.35": {
            "name": "Pumping2 current electric of Raman equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.11.35",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "Raman pumping2 current electric alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.23.9": {
            "name": "Working channel of OBP equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.23.9",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "switch from backup route to main route",
                                         "2": "switch from main route to backup route"}},
            "description": "OBP working channel alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.23.35": {
            "name": "R1 current power of OBP equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.23.35",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "OBP r1 current power alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.23.36": {
            "name": "R2 current power of OBP equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.23.36",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "OBP r2 current power alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.23.37": {
            "name": "R3 current power of OBP equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.23.37",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "OBP r3 current power alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.23.38": {
            "name": "R4 current power of OBP equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.23.38",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "OBP r4 current power alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.23.27": {
            "name": "Bypass conditions of OBP equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.23.27",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {
                "result": {"1": "r1 alarm", "2": "r2 alarm", "3": "r1 and r2 alarm", "4": "r1 or r2 alarm"}},
            "description": "OBP bypass conditions alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.23.8": {
            "name": "Work mode of OBP equipment in slot {slotNum} status is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.23.8",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "automatic", "0": "manual"}},
            "description": "OBP work mode alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.23.28": {
            "name": "Heartbeat of OBP equipment in slot {slotNum} status is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.23.28",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "close", "0": "open"}},
            "description": "OBP heartbeat alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.23.23": {
            "name": "Return mode of OBP equipment in slot {slotNum} status is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.23.23",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "automatic", "0": "manual"}},
            "description": "OBP return mode alarm"
        },
        "*******.4.1.40989.10.16.{slotNum}.23.29": {
            "name": "Heartbeat status of OBP equipment in slot {slotNum} is {result}",
            "alarm_type": 1,
            "alarm_level": 2,
            "match_regular": "*******.4.1.40989.10.16.\\d+.23.29",
            "sub_oid_mapping": {
                "slotNum": {"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9",
                            "10": "10", "11": "11", "12": "12", "13": "13", "14": "14", "15": "15", "16": "16"}},
            "value_mapping": {"result": {"1": "normal", "0": "alarm"}},
            "description": "OBP heartbeat status alarm"
        }
    }

    def __init__(self, alarm_name, sn, resource_id, occurrence_time, alarm_level):
        self.alarm_name = alarm_name
        self.sn = sn
        self.resource_id = resource_id
        self.occurrence_time = occurrence_time
        self.alarm_level = alarm_level

    @staticmethod
    def build_model(source_ip, uptime, var_binds):
        print(f"sourceIp:{source_ip} , varBinds:{var_binds}")
        model_list = {"GeneralAlarmModel": [], "AlarmTrapOriginalModel": []}

        occurrence_time = datetime.now()
        if uptime is None or len(uptime) == 0:
            occurrence_str = var_binds.get("*******.*******.0")
            if occurrence_str is not None and occurrence_str != "0":
                occurrence_time = datetime.fromtimestamp(int(occurrence_str))
        else:
            occurrence_time = datetime.fromtimestamp(int(uptime))

        occurrenceTime = occurrence_time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"occurrenceTime:{occurrenceTime}")

        # 删除varBinds中的时间戳
        if var_binds.get('*******.*******.0') is not None:
            del var_binds['*******.*******.0']

        # 删除varBinds中的trap时间类型
        if var_binds.get('*******.*******.4.1.0') is not None:
            del var_binds['*******.*******.4.1.0']

        for oid, value in var_binds.items():
            print(f"oid:{oid} , value:{value}")
            model_list.get("AlarmTrapOriginalModel").append(
                GeneralAlarmModel.get_json(AlarmTrapOriginalModel(oid, source_ip, occurrenceTime, value)))
            metaData = GeneralAlarmModel.__TRAP_MIB_DICT.get(oid)
            isMatch = True
            metaDataOid = None
            if metaData is None:
                isMatch = False
                for metaKey, metaValue in GeneralAlarmModel.__TRAP_MIB_DICT.items():
                    if re.match(metaValue["match_regular"], oid):
                        metaData = metaValue
                        metaDataOid = metaKey

            if metaData is None:
                print(f"Error, oid:{oid} can not match any mib metadate, discard alarm!")
                continue

            oidMappingObj = metaData.get("sub_oid_mapping")
            valueMappingObj = metaData.get("value_mapping")

            # oid完全匹配
            if isMatch:
                alarmStr = GeneralAlarmModel.build_alarm_name(metaData["name"], value, valueMappingObj)
            else:
                # oid中有占位符情况
                strSrc = metaDataOid.split(".")
                strDes = oid.split(".")
                resultDict = {}
                for index in range(len(strSrc)):
                    if "{" in strSrc[index]:
                        resultDict[strSrc[index][1:len(strSrc[index]) - 1]] = strDes[index]
                print(f"resultDict:{resultDict}")

                alarmStr = metaData["name"]
                for keyMeta, valueMeta in resultDict.items():
                    exceptValue = oidMappingObj.get(keyMeta)
                    if exceptValue is None or exceptValue.get(valueMeta) is None:
                        print(f"Error, filed:{keyMeta} not config mapping!")
                        continue
                    alarmStr = alarmStr.replace("{" + keyMeta + "}", exceptValue.get(valueMeta))

                alarmStr = GeneralAlarmModel.build_alarm_name(alarmStr, value, valueMappingObj)

            print(f"Alarm text is:{alarmStr}")
            alarmLevel = GeneralAlarmModel.__ALARM_LEVEL.get(metaData["alarm_level"])
            model_list.get("GeneralAlarmModel").append(
                GeneralAlarmModel.get_json(GeneralAlarmModel(alarmStr, source_ip, oid, occurrenceTime, alarmLevel)))

        return model_list

    @staticmethod
    def build_alarm_name(alarmStr, value, valueMappingObj):
        for metaKey, metaValue in valueMappingObj.items():
            for reserveKey, reserveValue in metaValue.items():
                if reserveKey == value:
                    return alarmStr.replace("{" + metaKey + "}", reserveValue)

        print(f"Error, value:{value} hase no config mapping!")
        return alarmStr

    @staticmethod
    def get_json(alarm_model):
        # 转换为JSON字符串
        json_str = json.dumps(alarm_model.__dict__)
        # 转换为JSON对象
        return json.loads(json_str)


@snmp_celery_app.task(name='snmp_trap_fmt_alarm')
def send_fmt_alarm(trap_info):
    return trap_info


async def process_key_value(redis_client, key, event=None, flag=False):
    try:
        if flag:
            key = key.decode().replace("__keyspace@0__:", "").encode()

        value_dict = await redis_client.json().get(key)
        if value_dict:
            ip_type = value_dict.get("type")
            ip = key.decode().split(':')[2]
            if ip_type in ["2", "5"]:
                if not event or event == "json.set":
                    trusted_ips.add(ip)
        else:
            if event == "del":
                trusted_ips.discard(key.decode().split(':')[2])
    except json.JSONDecodeError as e:
        LOG.error(f"Failed to parse JSON value for key {key}: {e}")


def generate_message_id(key, event):
    message_str = f"{key}:{event}"
    return hashlib.sha256(message_str.encode()).hexdigest()


async def handle_message(redis_client, message):
    key = message['channel']
    event = message['data'].decode()
    message_id = generate_message_id(key, event)

    if await redis_client.sismember(PROCESSED_MESSAGES_KEY, message_id):
        return

    await process_key_value(redis_client, key, event, True)

    pipe = redis_client.pipeline()
    pipe.sadd(PROCESSED_MESSAGES_KEY, message_id)
    pipe.expire(PROCESSED_MESSAGES_KEY, 1)
    await pipe.execute()
    LOG.info("Current trusted IPs: %s" % str(trusted_ips))


async def scan_keys(redis_client, pattern):
    cursor = 0
    keys = []
    while True:
        cursor, partial_keys = await redis_client.scan(cursor, match=pattern)
        keys.extend(partial_keys)
        if cursor == 0:
            break
    return keys


async def scan_and_process_keys(redis_client, pattern):
    keys = await scan_keys(redis_client, pattern)
    if keys:
        for key in keys:
            await process_key_value(redis_client, key, None, False)


async def subscribe_to_redis():
    redis_client = redis.Redis(
        host='tnms_redis',
        port=6379,
        db=0,
        username='redis',
        password='fssecret'
    )

    await scan_and_process_keys(redis_client, 'config:ne*')
    LOG.info("Started subscribe redis server, Initial trusted IPs: %s" % str(trusted_ips))
    pubsub = redis_client.pubsub()
    await pubsub.psubscribe('__keyspace@0__:config:ne*')

    while True:
        message = await pubsub.get_message(ignore_subscribe_messages=True)
        if message:
            asyncio.create_task(handle_message(redis_client, message))
        await asyncio.sleep(0.01)


async def forward_trap_msg(source_ip, original_oid, var_binds):
    if not var_binds:
        LOG.error("No var_binds received, skipping notification.")
        return

    source_ip_varbind = ObjectType(ObjectIdentity("*******.4.1.9999.*******"), OctetString(source_ip))
    var_binds.append(source_ip_varbind)

    snmpDispatcher = SnmpDispatcher()

    try:
        notification = NotificationType(ObjectIdentity(original_oid))
        notification.add_varbinds(*var_binds)

        iterator = await send_notification(
            snmpDispatcher,
            CommunityData("public", mpModel=1),
            await UdpTransportTarget.create(("tnms_ne_2", 162)),
            "trap",
            notification
        )
        error_indication, error_status, error_index, var_binds = iterator
        if error_indication:
            LOG.error(error_indication)
    except Exception as e:
        LOG.error(f"Error forwarding trap message: {e} {traceback.format_exc()}")
    finally:
        snmpDispatcher.transport_dispatcher.close_dispatcher()


def __callback(transportDispatcher, transportDomain, transportAddress, wholeMsg):
    msgVer = int(api.decodeMessageVersion(wholeMsg))
    if msgVer in api.PROTOCOL_MODULES:
        pMod = api.PROTOCOL_MODULES[msgVer]
    else:
        LOG.error("Unsupported SNMP version %s" % msgVer)
        return

    reqMsg, wholeMsg = decoder.decode(
        wholeMsg,
        asn1Spec=pMod.Message(),
    )

    LOG.info(
        "Notification message from {}:{}: ".format(
            transportDomain, transportAddress
        )
    )

    reqPDU = pMod.apiMessage.get_pdu(reqMsg)
    if reqPDU.isSameTypeWith(pMod.TrapPDU()):
        uptime = None
        if msgVer == api.SNMP_VERSION_1:
            LOG.info(
                f"Enterprise: {pMod.apiTrapPDU.get_enterprise(reqPDU).prettyPrint()}"
            )
            LOG.info(
                f"Agent Address: {pMod.apiTrapPDU.get_agent_address(reqPDU).prettyPrint()}"
            )
            LOG.info(
                f"Generic Trap: {pMod.apiTrapPDU.get_generic_trap(reqPDU).prettyPrint()}"
            )
            LOG.info(
                f"Specific Trap: {pMod.apiTrapPDU.get_specific_trap(reqPDU).prettyPrint()}"
            )
            uptime = pMod.apiTrapPDU.get_timestamp(reqPDU).prettyPrint()
            LOG.info(
                f"Uptime: {uptime}"
            )
            var_binds = pMod.apiTrapPDU.get_varbinds(reqPDU)
        else:
            # SNMPv2c or SNMPv3
            var_binds = pMod.apiPDU.get_varbinds(reqPDU)

        main_oid = var_binds[0][0].prettyPrint()
        LOG.info(f"Main OID: {main_oid}")
        LOG.info("Var-binds:")
        json_var_binds = {}
        for oid, val in var_binds:
            LOG.info(f"{oid.prettyPrint()} = {val.prettyPrint()}")
            json_var_binds[str(oid)] = str(val)

        if transportAddress[0] in trusted_ips:
            LOG.info(f"Source IP {transportAddress[0]} is trusted, forwarding Trap...")
            asyncio.create_task(forward_trap_msg(transportAddress[0], main_oid, var_binds))
        else:
            trap_info = GeneralAlarmModel.build_model(transportAddress[0], uptime, json_var_binds)
            send_fmt_alarm.apply_async(args=(trap_info,), queue="snmp_trap_alarm")
            LOG.info(f"Send msg to MQ => {trap_info}")
    return wholeMsg


def snmp_server():
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    transportDispatcher = AsyncioDispatcher()
    transportDispatcher.register_recv_callback(__callback)
    transportDispatcher.register_transport(
        udp.DOMAIN_NAME, udp.UdpAsyncioTransport().open_server_mode(("0.0.0.0", 162))
    )
    transportDispatcher.job_started(1)

    try:
        LOG.info("Started SNMP server. Press Ctrl-C to stop.")
        loop.run_until_complete(transportDispatcher.run_dispatcher())
    except KeyboardInterrupt:
        LOG.error("Shutting down SNMP server...")
    finally:
        transportDispatcher.close_dispatcher()
        loop.close()


async def main():
    LOG.info("This program needs to run as root/administrator to monitor port 162.")
    snmp_server_thread = threading.Thread(target=snmp_server)
    snmp_server_thread.daemon = True
    snmp_server_thread.start()
    await asyncio.gather(subscribe_to_redis())


if __name__ == "__main__":
    asyncio.run(main())
