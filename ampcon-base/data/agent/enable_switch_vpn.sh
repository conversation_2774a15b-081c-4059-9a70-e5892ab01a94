#!/bin/bash

server_ip=$1
vpn_ip="********"
config_file_path="/opt/auto-deploy/auto-deploy.conf"

# get vpn_ip from /opt/auto-deploy/auto-deploy.conf
server_vpn_output=`grep -o -P '(?<=server_vpn_ip =).*\b' ${config_file_path} | sed 's/[ =]//g'`
if [ ! "$server_vpn_output" = "" ]; then
   vpn_ip=$server_vpn_output
fi


if [ -z $server_ip ]; then
    echo "please enter ampcon server ip eg: ./enable_switch_vpn.sh ***********"
    exit
fi

vrf_command=""

vrf_enable=`sudo ip link show | grep -c "mgmt-vrf"`
if [ $vrf_enable -ne 0 ]; then
    vrf_command="ip vrf exec mgmt-vrf"
fi

base_url="https://${server_ip}"
# get switch sn
sn=`cat /sys/class/swmon/hwinfo/serial_number`

file_dir="/etc/openvpn"
mkdir -p ${file_dir}

config_url="${base_url}/management/vpn/${sn}"
static_config_url="${base_url}/management/vpn"
file_download_url="${base_url}/rma/file/"

# manual_deploy
if [ -f "/opt/auto-deploy/manual_deploy.sh" ]; then
    sudo /opt/auto-deploy/manual_deploy.sh
fi

#download
echo "download client.conf to ${file_dir}/client.conf"
sudo ${vrf_command} curl -o ${file_dir}/client.conf -k -v ${static_config_url}/client.conf
if [ $? -ne 0 ]; then
   echo "download vpn file failed"
   exit
fi
echo "download automation.up to ${file_dir}/automation.up"
sudo ${vrf_command} curl -o ${file_dir}/automation.up -k -v ${static_config_url}/automation.up
echo "download update_vpn_ip.sh to ${file_dir}/update_vpn_ip.sh"
sudo ${vrf_command} curl -o ${file_dir}/update_vpn_ip.sh -k -v ${static_config_url}/update_vpn_ip.sh
echo "download client.key to ${file_dir}/client.key"
sudo ${vrf_command} curl -o ${file_dir}/client.key -k -v ${config_url}/client.key
if [ $? -ne 0 ]; then
   echo "download vpn file failed"
   exit
fi
echo "download client.crt to ${file_dir}/client.crt"
sudo ${vrf_command} curl -o ${file_dir}/client.crt -k -v ${config_url}/client.crt
echo "download ca.crt to ${file_dir}/ca.crt"
sudo ${vrf_command} curl -o ${file_dir}/ca.crt -k -v ${config_url}/ca.crt

sudo sed -i -e 's/\r$//' /etc/openvpn/automation.up
sudo sed -i -e 's/\r$//' /etc/openvpn/update_vpn_ip.sh
sudo chmod +x /etc/openvpn/automation.up /etc/openvpn/update_vpn_ip.sh

echo "download restart_ovpn.sh to /opt/auto-deploy/restart_ovpn.sh"
sudo ${vrf_command} curl -o /opt/auto-deploy/restart_ovpn.sh -k -v ${file_download_url}agent/restart_ovpn.sh
sudo chmod +x /opt/auto-deploy/restart_ovpn.sh
# 
echo "sync datetime with server"
datetime=`sudo ${vrf_command} curl -k -m 5 ${base_url}/reg/vpn/time`
status_404=$(echo $datetime | grep -c '404 Not Found')
if [ ! -n "$datetime" ] || [ "$status_404" -gt 1 ]; then
    sudo ${vrf_command} ntpdate pool.ntp.org
else
    echo "Server datetime: $datetime"
    sudo date -s "$datetime"
fi

if [ $vrf_enable -ne 0 ]; then
    sudo /opt/auto-deploy/restart_ovpn.sh mgmt-vrf
else
    sudo /opt/auto-deploy/restart_ovpn.sh default
fi

# need sleep
sleep 120

tun_ip=`ip addr show tun0 | grep 'POINTOPOINT' -A2 | tail -n1 | awk '{print $2}' | cut -f1  -d'/'`

function set_vpn_route() {
    if [ $(sudo iptables -nvL | grep -c tun0) == 0 ]; then
        sudo iptables -I INPUT 4 -i tun0 -p tcp -m multiport --dport 22,23,8080 -j ACCEPT
    fi

    ip_route="ip route show table 252 | grep ${vpn_ip}"
    if [ $(ip route show table 252 | grep -c ${vpn_ip}) == 0 ]; then
        sudo ip route add table 252 ${vpn_ip} dev tun0
    fi
}

if [[ -n $tun_ip ]]; then
    set_vpn_route
    echo "get tun_ip ${tun_ip}"
    url="https://${vpn_ip}:443/vpn_update_ip/${sn};${tun_ip}"
    sudo ${vrf_command} curl -k $url

    sudo ${vrf_command} curl -k https://${vpn_ip}:443/rma/vpn/${sn}/${tun_ip}
fi
