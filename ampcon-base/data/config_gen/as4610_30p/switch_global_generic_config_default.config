
set poe interface all enable true
set poe interface all threshold-mode 1
set poe interface all max-power 32
set poe power management-mode 4
set poe interface ge-1/1/1 detection-type 3
set poe interface ge-1/1/1 enable true
set poe interface ge-1/1/2 detection-type 3
set poe interface ge-1/1/2 enable true
set poe interface ge-1/1/3 detection-type 3
set poe interface ge-1/1/3 enable true
set poe interface ge-1/1/4 detection-type 3
set poe interface ge-1/1/4 enable true
set poe interface ge-1/1/5 detection-type 3
set poe interface ge-1/1/5 enable true
set poe interface ge-1/1/6 detection-type 3
set poe interface ge-1/1/6 enable true
set poe interface ge-1/1/7 detection-type 3
set poe interface ge-1/1/7 enable true
set poe interface ge-1/1/8 detection-type 3
set poe interface ge-1/1/8 enable true
set poe interface ge-1/1/9 detection-type 3
set poe interface ge-1/1/9 enable true
set poe interface ge-1/1/10 detection-type 3
set poe interface ge-1/1/10 enable true
set poe interface ge-1/1/11 detection-type 3
set poe interface ge-1/1/11 enable true
set poe interface ge-1/1/12 detection-type 3
set poe interface ge-1/1/12 enable true
set poe interface ge-1/1/13 detection-type 3
set poe interface ge-1/1/13 enable true
set poe interface ge-1/1/14 detection-type 3
set poe interface ge-1/1/14 enable true
set poe interface ge-1/1/15 detection-type 3
set poe interface ge-1/1/15 enable true
set poe interface ge-1/1/16 detection-type 3
set poe interface ge-1/1/16 enable true
set poe interface ge-1/1/17 detection-type 3
set poe interface ge-1/1/17 enable true
set poe interface ge-1/1/18 detection-type 3
set poe interface ge-1/1/18 enable true
set poe interface ge-1/1/19 detection-type 3
set poe interface ge-1/1/19 enable true
set poe interface ge-1/1/20 detection-type 3
set poe interface ge-1/1/20 enable true
set poe interface ge-1/1/21 detection-type 3
set poe interface ge-1/1/21 enable true
set poe interface ge-1/1/22 detection-type 3
set poe interface ge-1/1/22 enable true
set poe interface ge-1/1/23 detection-type 3
set poe interface ge-1/1/23 enable true
set poe interface ge-1/1/24 detection-type 3
set poe interface ge-1/1/24 enable true
set system ntp-server-ip ***********
set system ntp-server-ip ***********
set system syslog server-ip ************
set system syslog server-ip ************
set protocols snmp v3 mib-view readall subtree 1 mask "ff"
set protocols snmp v3 mib-view writeall subtree 1 mask "ff"
set protocols snmp v3 group VINET-TOOLS-RO security-level "AuthPriv"
set protocols snmp v3 group VINET-TOOLS-RO read-view "readall"
set protocols snmp v3 group VINET-TOOLS-RW security-level "AuthPriv"
set protocols snmp v3 group VINET-TOOLS-RW read-view "readall"
set protocols snmp v3 group VINET-TOOLS-RW write-view "writeall"
set protocols snmp v3 usm-user test1 group "VINET-TOOLS-RO"
set protocols snmp v3 usm-user test1 authentication-mode "sha"
set protocols snmp v3 usm-user test1 authentication-key SECREVALUE1
set protocols snmp v3 usm-user test1 privacy-mode "aes128"
set protocols snmp v3 usm-user test1 privacy-key SECREVALUE1
set protocols snmp v3 usm-user test2 group "VINET-TOOLS-RW"
set protocols snmp v3 usm-user test2 authentication-mode "sha"
set protocols snmp v3 usm-user test2 authentication-key SECREVALUE2
set protocols snmp v3 usm-user test2 privacy-mode "aes128"
set protocols snmp v3 usm-user test2 privacy-key SECREVALUE2
set system aaa tacacs-plus disable false
set interface stm firewall-table ingress 200 
set protocols spanning-tree force-version 4
set protocols spanning-tree pvst interface ge-1/1/1
set protocols spanning-tree pvst interface ge-1/1/2
set protocols spanning-tree pvst interface ge-1/1/3
set protocols spanning-tree pvst interface ge-1/1/4
set protocols spanning-tree pvst interface ge-1/1/5
set protocols spanning-tree pvst interface ge-1/1/6
set protocols spanning-tree pvst interface ge-1/1/7
set protocols spanning-tree pvst interface ge-1/1/8
set protocols spanning-tree pvst interface ge-1/1/9
set protocols spanning-tree pvst interface ge-1/1/10
set protocols spanning-tree pvst interface ge-1/1/11
set protocols spanning-tree pvst interface ge-1/1/12
set protocols spanning-tree pvst interface ge-1/1/13
set protocols spanning-tree pvst interface ge-1/1/14
set protocols spanning-tree pvst interface ge-1/1/15
set protocols spanning-tree pvst interface ge-1/1/16
set protocols spanning-tree pvst interface ge-1/1/17
set protocols spanning-tree pvst interface ge-1/1/18
set protocols spanning-tree pvst interface ge-1/1/19
set protocols spanning-tree pvst interface ge-1/1/20
set protocols spanning-tree pvst interface ge-1/1/21
set protocols spanning-tree pvst interface ge-1/1/22
set protocols spanning-tree pvst interface ge-1/1/23
set protocols spanning-tree pvst interface ge-1/1/24
set protocols spanning-tree pvst interface ge-1/1/1 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/2 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/3 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/4 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/5 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/6 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/7 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/8 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/9 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/10 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/11 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/12 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/13 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/14 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/15 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/16 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/17 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/18 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/19 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/20 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/21 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/22 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/23 bpdu-guard true
set protocols spanning-tree pvst interface ge-1/1/24 bpdu-guard true
set xovs enable true
set interface gigabit-ethernet ge-1/1/1 description "User Port"
set interface gigabit-ethernet ge-1/1/2 description "User Port"
set interface gigabit-ethernet ge-1/1/3 description "User Port"
set interface gigabit-ethernet ge-1/1/4 description "User Port"
set interface gigabit-ethernet ge-1/1/5 description "User Port"
set interface gigabit-ethernet ge-1/1/6 description "User Port"
set interface gigabit-ethernet ge-1/1/7 description "User Port"
set interface gigabit-ethernet ge-1/1/8 description "User Port"
set interface gigabit-ethernet ge-1/1/9 description "User Port"
set interface gigabit-ethernet ge-1/1/10 description "User Port"
set interface gigabit-ethernet ge-1/1/11 description "User Port"
set interface gigabit-ethernet ge-1/1/12 description "User Port"
set interface gigabit-ethernet ge-1/1/13 description "User Port"
set interface gigabit-ethernet ge-1/1/14 description "User Port"
set interface gigabit-ethernet ge-1/1/15 description "User Port"
set interface gigabit-ethernet ge-1/1/16 description "User Port"
set interface gigabit-ethernet ge-1/1/17 description "User Port"
set interface gigabit-ethernet ge-1/1/18 description "User Port"
set interface gigabit-ethernet ge-1/1/19 description "User Port"
set interface gigabit-ethernet ge-1/1/20 description "User Port"
set interface gigabit-ethernet ge-1/1/21 description "User Port"
set interface gigabit-ethernet ge-1/1/22 description "User Port"
set interface gigabit-ethernet ge-1/1/23 description "User Port"
set interface gigabit-ethernet ge-1/1/24 description "User Port"
set protocols lldp enable true
set protocol lldp compliance cdp true
set protocols lldp interface ge-1/1/1 compliance cdp true
set protocols lldp interface ge-1/1/2 compliance cdp true
set protocols lldp interface ge-1/1/3 compliance cdp true
set protocols lldp interface ge-1/1/4 compliance cdp true
set protocols lldp interface ge-1/1/5 compliance cdp true
set protocols lldp interface ge-1/1/6 compliance cdp true
set protocols lldp interface ge-1/1/7 compliance cdp true
set protocols lldp interface ge-1/1/8 compliance cdp true
set protocols lldp interface ge-1/1/9 compliance cdp true
set protocols lldp interface ge-1/1/10 compliance cdp true
set protocols lldp interface ge-1/1/11 compliance cdp true
set protocols lldp interface ge-1/1/12 compliance cdp true
set protocols lldp interface ge-1/1/13 compliance cdp true
set protocols lldp interface ge-1/1/14 compliance cdp true
set protocols lldp interface ge-1/1/15 compliance cdp true
set protocols lldp interface ge-1/1/16 compliance cdp true
set protocols lldp interface ge-1/1/17 compliance cdp true
set protocols lldp interface ge-1/1/18 compliance cdp true
set protocols lldp interface ge-1/1/19 compliance cdp true
set protocols lldp interface ge-1/1/20 compliance cdp true
set protocols lldp interface ge-1/1/21 compliance cdp true
set protocols lldp interface ge-1/1/22 compliance cdp true
set protocols lldp interface ge-1/1/23 compliance cdp true
set protocols lldp interface ge-1/1/24 compliance cdp true
set protocols lldp interface te-1/1/25 compliance cdp true
set protocols lldp interface te-1/1/26 compliance cdp true
set protocols lldp interface te-1/1/27 compliance cdp true
set protocols lldp interface te-1/1/28 compliance cdp true
set system log-level trace
set protocols udld disable false
set protocols udld interface te-1/1/27 disable false
set protocols udld interface te-1/1/28 disable false
set interface gigabit-ethernet ge-1/1/1 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/2 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/3 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/4 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/5 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/6 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/7 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/8 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/9 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/10 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/11 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/12 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/13 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/14 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/15 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/16 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/17 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/18 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/19 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/20 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/21 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/22 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/23 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/24 storm-control broadcast ratio 1
set interface gigabit-ethernet te-1/1/25 storm-control broadcast ratio 1
set interface gigabit-ethernet te-1/1/26 storm-control broadcast ratio 1
set interface gigabit-ethernet te-1/1/27 storm-control broadcast ratio 1
set interface gigabit-ethernet te-1/1/28 storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/1 crossflow enable true
set interface gigabit-ethernet ge-1/1/2 crossflow enable true
set interface gigabit-ethernet ge-1/1/3 crossflow enable true
set interface gigabit-ethernet ge-1/1/4 crossflow enable true
set interface gigabit-ethernet ge-1/1/5 crossflow enable true
set interface gigabit-ethernet ge-1/1/6 crossflow enable true
set interface gigabit-ethernet ge-1/1/7 crossflow enable true
set interface gigabit-ethernet ge-1/1/8 crossflow enable true
set interface gigabit-ethernet ge-1/1/9 crossflow enable true
set interface gigabit-ethernet ge-1/1/10 crossflow enable true
set interface gigabit-ethernet ge-1/1/11 crossflow enable true
set interface gigabit-ethernet ge-1/1/12 crossflow enable true
set interface gigabit-ethernet ge-1/1/13 crossflow enable true
set interface gigabit-ethernet ge-1/1/14 crossflow enable true
set interface gigabit-ethernet ge-1/1/15 crossflow enable true
set interface gigabit-ethernet ge-1/1/16 crossflow enable true
set interface gigabit-ethernet ge-1/1/17 crossflow enable true
set interface gigabit-ethernet ge-1/1/18 crossflow enable true
set interface gigabit-ethernet ge-1/1/19 crossflow enable true
set interface gigabit-ethernet ge-1/1/20 crossflow enable true
set interface gigabit-ethernet ge-1/1/21 crossflow enable true
set interface gigabit-ethernet ge-1/1/22 crossflow enable true
set interface gigabit-ethernet ge-1/1/23 crossflow enable true
set interface gigabit-ethernet ge-1/1/24 crossflow enable true

set class-of-service forwarding-class voip local-priority 7
set class-of-service forwarding-class routing local-priority 6 
set class-of-service forwarding-class video local-priority 5 
set class-of-service forwarding-class control local-priority 4
set class-of-service forwarding-class mc local-priority 3  
set class-of-service forwarding-class bulkdata local-priority 1
set class-of-service forwarding-class default local-priority 0 

set class-of-service classifier c1 trust-mode dscp
set class-of-service classifier c1 forwarding-class voip code-point 46
set class-of-service classifier c1 forwarding-class routing code-point 48
set class-of-service classifier c1 forwarding-class routing code-point 56
set class-of-service classifier c1 forwarding-class video code-point 32
set class-of-service classifier c1 forwarding-class video code-point 34
set class-of-service classifier c1 forwarding-class control code-point 24
set class-of-service classifier c1 forwarding-class control code-point 26
set class-of-service classifier c1 forwarding-class bulkdata code-point 10
set class-of-service classifier c1 forwarding-class bulkdata code-point 14
set class-of-service classifier c1 forwarding-class bulkdata code-point 22
set class-of-service classifier c1 forwarding-class mc code-point 16 
set class-of-service classifier c1 forwarding-class mc code-point 28
set class-of-service classifier c1 forwarding-class mc code-point 30
set class-of-service classifier c1 forwarding-class mc code-point 36
set class-of-service classifier c1 forwarding-class default code-point 0

set class-of-service interface te-1/1/25 classifier c1
set class-of-service interface te-1/1/26 classifier c1
set class-of-service interface te-1/1/27 classifier c1
set class-of-service interface te-1/1/28 classifier c1

set class-of-service scheduler svoip10g guaranteed-rate 3000000
set class-of-service scheduler srouting10g guaranteed-rate 490000
set class-of-service scheduler svideo10g guaranteed-rate 980000
set class-of-service scheduler scontrol10g guaranteed-rate 490000
set class-of-service scheduler sbulkdata10g guaranteed-rate 980000
set class-of-service scheduler smc10g guaranteed-rate 3150000
set class-of-service scheduler sdefault10g guaranteed-rate 910000

set class-of-service scheduler srouting10g mode WRR
set class-of-service scheduler svideo10g mode WRR
set class-of-service scheduler scontrol10g mode WRR
set class-of-service scheduler sbulkdata10g mode WRR
set class-of-service scheduler smc10g mode WRR
set class-of-service scheduler sdefault10g mode WRR

set class-of-service scheduler svoip1g guaranteed-rate 300000
set class-of-service scheduler srouting1g guaranteed-rate 49000
set class-of-service scheduler svideo1g guaranteed-rate 98000
set class-of-service scheduler scontrol1g guaranteed-rate 49000
set class-of-service scheduler sbulkdata1g guaranteed-rate 98000
set class-of-service scheduler smc1g guaranteed-rate 315000
set class-of-service scheduler sdefault1g guaranteed-rate 91000

set class-of-service scheduler srouting1g mode WRR
set class-of-service scheduler svideo1g mode WRR
set class-of-service scheduler scontrol1g mode WRR
set class-of-service scheduler sbulkdata1g mode WRR
set class-of-service scheduler smc1g mode WRR
set class-of-service scheduler sdefault1g mode  WRR

set class-of-service scheduler-profile 1G forwarding-class voip scheduler svoip1g
set class-of-service scheduler-profile 1G forwarding-class routing scheduler srouting1g
set class-of-service scheduler-profile 1G forwarding-class video scheduler svideo1g
set class-of-service scheduler-profile 1G forwarding-class control scheduler scontrol1g
set class-of-service scheduler-profile 1G forwarding-class bulkdata scheduler sbulkdata1g
set class-of-service scheduler-profile 1G forwarding-class mc scheduler smc1g
set class-of-service scheduler-profile 1G forwarding-class default scheduler sdefault1g

set class-of-service scheduler-profile 10G forwarding-class voip scheduler svoip10g
set class-of-service scheduler-profile 10G forwarding-class routing scheduler srouting10g
set class-of-service scheduler-profile 10G forwarding-class video scheduler svideo10g
set class-of-service scheduler-profile 10G forwarding-class control scheduler scontrol10g
set class-of-service scheduler-profile 10G forwarding-class bulkdata scheduler sbulkdata10g
set class-of-service scheduler-profile 10G forwarding-class mc scheduler smc10g
set class-of-service scheduler-profile 10G forwarding-class default scheduler sdefault10g

set class-of-service interface ge-1/1/1 scheduler-profile 1G
set class-of-service interface ge-1/1/2 scheduler-profile 1G
set class-of-service interface ge-1/1/3 scheduler-profile 1G
set class-of-service interface ge-1/1/4 scheduler-profile 1G
set class-of-service interface ge-1/1/5 scheduler-profile 1G
set class-of-service interface ge-1/1/6 scheduler-profile 1G
set class-of-service interface ge-1/1/7 scheduler-profile 1G
set class-of-service interface ge-1/1/8 scheduler-profile 1G
set class-of-service interface ge-1/1/9 scheduler-profile 1G
set class-of-service interface ge-1/1/10 scheduler-profile 1G
set class-of-service interface ge-1/1/11 scheduler-profile 1G
set class-of-service interface ge-1/1/12 scheduler-profile 1G
set class-of-service interface ge-1/1/13 scheduler-profile 1G
set class-of-service interface ge-1/1/14 scheduler-profile 1G
set class-of-service interface ge-1/1/15 scheduler-profile 1G
set class-of-service interface ge-1/1/16 scheduler-profile 1G
set class-of-service interface ge-1/1/17 scheduler-profile 1G
set class-of-service interface ge-1/1/18 scheduler-profile 1G
set class-of-service interface ge-1/1/19 scheduler-profile 1G
set class-of-service interface ge-1/1/20 scheduler-profile 1G
set class-of-service interface ge-1/1/21 scheduler-profile 1G
set class-of-service interface ge-1/1/22 scheduler-profile 1G
set class-of-service interface ge-1/1/23 scheduler-profile 1G
set class-of-service interface ge-1/1/24 scheduler-profile 1G
