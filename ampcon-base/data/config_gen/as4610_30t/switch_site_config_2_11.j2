
{# !Data Vlan interface configuration #}
{# site config #}
{% for data_vlan in ldata_vlan %}
set vlans vlan-id {{ data_vlan.id }} l3-interface "vlan{{ data_vlan.id }}"
set vlans vlan-id {{ data_vlan.id }} description "{{ data_vlan.desc }}"
set vlan-interface interface vlan{{ data_vlan.id }} vif vlan{{ data_vlan.id }} address {{ data_vlan.ip_add }} prefix-length {{ data_vlan.prefix_length }}
set vlan-interface interface vlan{{ data_vlan.id }} vif vlan{{ data_vlan.id }} description "Data Vlan"
{# !IPV6 addressing, code as below #}
set vlan-interface interface vlan{{ data_vlan.id }} vif vlan{{ data_vlan.id }} address {{ data_vlan.ipv6_add }} prefix-length {{ data_vlan.prefix_length_v6 }}
{% endfor %}


{# !Configure port native-vlan (Access vlan), Port needs to be “trunk” to support voice vlan #}
set interface gigabit-ethernet ge-1/1/1 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/2 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/3 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/4 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/5 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/6 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/7 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/8 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/9 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/10 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/11 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/12 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/13 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/14 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/15 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/16 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/17 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/18 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/19 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/20 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/21 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/22 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/23 family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/24 family ethernet-switching port-mode "trunk"
{% for data_vlan in ldata_vlan %}
{% if data_vlan.native %}
set interface gigabit-ethernet ge-1/1/1 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/2 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/3 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/4 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/5 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/6 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/7 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/8 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/9 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/10 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/11 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/12 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/13 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/14 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/15 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/16 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/17 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/18 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/19 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/20 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/21 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/22 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/23 family ethernet-switching native-vlan-id {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/24 family ethernet-switching native-vlan-id {{ data_vlan.id }}
{% else %}
set interface gigabit-ethernet ge-1/1/1 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/2 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/3 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/4 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/5 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/6 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/7 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/8 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/9 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/10 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/11 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/12 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/13 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/14 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/15 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/16 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/17 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/18 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/19 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/20 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/21 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/22 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/23 family ethernet-switching vlan members {{ data_vlan.id }}
set interface gigabit-ethernet ge-1/1/24 family ethernet-switching vlan members {{ data_vlan.id }}
{% endif %}
{% endfor %}

{# !IPV6 Default route #}
{% if 'FE80' in default_gw.ipv6_next_hop|upper %}
set protocols static interface-route 0::0/0 next-hop-interface vlan{{ data_vlan.id }}
set protocols static interface-route 0::0/0 next-hop-vif vlan{{ data_vlan.id }}
set protocols static interface-route 0::0/0 next-hop-router {{ default_gw.ipv6_next_hop }}
{% else %}
set protocols static route 0::0/0 next-hop {{ default_gw.ipv6_next_hop }}
{% endif %}