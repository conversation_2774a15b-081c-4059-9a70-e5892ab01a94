for i in $ethdev
do
    # Get PCI device ID
    ETH_NAME=$(ethtool -i "$i" 2>/dev/null | awk '/bus-info/ {print $2}') 
    if [ -z "$ETH_NAME" ]; then
        echo ">>> Error: Unable to get the PCI device ID for NIC $i, check if the NIC exists!"
        continue
    fi
    # Get RDMA device name
    RDMA_NAME=$(readlink -f /sys/class/infiniband/* 2>/dev/null | grep -i "$ETH_NAME" | awk -F "/" '{print $NF}')
    # Check if RDMA device already exists
    if [ -n "$RDMA_NAME" ]; then
        echo "RDMA device ($RDMA_NAME) already exists, skipping RoCEv2 enable step."
    else
        echo "RDMA device does not exist, enabling RoCEv2 mode..."
        niccli -pci "$ETH_NAME" nvm -setoption support_rdma -scope 0 -value 1
        sleep 5  # Wait for RDMA device initialization
        RDMA_NAME=$(readlink -f /sys/class/infiniband/* 2>/dev/null | grep -i "$ETH_NAME" | awk -F "/" '{print $NF}')
        if [ -n "$RDMA_NAME" ]; then
            echo "RoCEv2 enabled successfully, RDMA device ($RDMA_NAME) has been created."
        else
            echo "RoCEv2 enabling failed, please check the network card support or driver status."
            exit 1
        fi
    fi
    # Configure default parameters for RDMA device
    echo "Configuring parameters for RDMA device ($RDMA_NAME)..."
    bnxt_setupcc.sh -d "$RDMA_NAME" -i "$i" -m {{ m_param }} -s {{ s_param }} -p {{ p_param }} -r {{ r_param }} -c {{ c_param }} -u 3
    echo "RDMA device configuration completed!"
done
