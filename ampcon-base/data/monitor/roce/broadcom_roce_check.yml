---
- name: Broadcom Check Playbook
  hosts: all
  become: yes
  become_method: sudo
  gather_facts: no
  tasks:
    - name: IB toolkit detection
      ignore_errors: no
      shell: "{{ item }}"
      register: cmd_output1
      loop:
        - "dpkg -l | grep -i infiniband-diags"
        - "dpkg -l | grep -i ibutils"
        - "dpkg -l | grep -i rdma-core"

    - name: Drive detection
      ignore_errors: no
      shell: "{{ item }}"
      register: cmd_output2
      loop:
        - "lsmod | grep bntx_en"
        - "lsmod | grep bntx_re"

    - name: Tool set check
      ignore_errors: no
      shell: "{{ item }}"
      register: cmd_output3
      loop:
        - "niccli --version|awk '{print $3}'"
        - "dpkg -l|grep -i bntx-re-config"
        - "dpkg -l|grep -i bcm-sosreport"

    - name: Check all RoCE devices with Ethernet link status
      ignore_errors: no
      shell: ibstatus|grep -i -B 8 'ethernet' |grep -i "infiniband device"|awk -F "'" '{print $2}'
      register: cmd_output4

    - name: Get Device Name
      set_fact:
        devices: "{{ cmd_output4.stdout.splitlines() | default([]) }}"
      when: cmd_output4.rc == 0 and cmd_output4.stdout_lines is defined

    - name: Determine PCI address
      ignore_errors: no
      shell: >
        readlink /sys/class/infiniband/{{ item }}/device
      loop: "{{ devices }}"
      register: cmd_output5
      when: devices | length > 0

    - name: Get PCI Addresses
      set_fact:
        pci_addresses: "{{ pci_addresses | default([]) + [item.stdout | regex_search('[0-9a-f]{4}:[0-9a-f]{2}:[0-9a-f]{2}\\.\\d')] | select('defined') | list }}"
      loop: "{{ cmd_output5.results }}"
      when: item.stdout is defined and item.stdout | length > 0 and item.stdout | regex_search('[0-9a-f]{4}:[0-9a-f]{2}:[0-9a-f]{2}\\.[0-9]') is not none

    - name: RoCE function check
      ignore_errors: no
      shell: >
        cat /sys/class/infiniband/{{ item }}/ports/1/gid_attrs/types/3
      loop: "{{ devices }}"
      register: cmd_output6
      when: devices | length > 0

    - name: RoCE configuration query part 1
      ignore_errors: no
      shell: sysctl net.ipv4.tcp_ecn
      register: cmd_output7

    - name: RoCE configuration query part 2
      ignore_errors: no
      shell: >
        sudo niccli -pci {{ item }} getqos
      loop: "{{ ports }}"
      register: cmd_output8
      when: ports | length > 0

    - name: Display inspection equipment
      ignore_errors: no
      shell: >
        sudo lspci -vvvs {{ item }} |grep -i "part number"
      loop: "{{ pci_addresses }}"
      register: cmd_output9
      when: devices | length > 0