<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Packet Loss Alert</title>
  </head>
  <body
    style="
      font-family: Arial, sans-serif;
      background-color: #ffffff;
      color: #333;
      padding: 20px;
    "
  >
    <div
      style="
        background: #ffffff;
        border-radius: 10px;
        width: 600px;
        min-height: 814px;
        margin: auto;
        border: 1px solid #e0e0e0;
      "
    >
      <table style="margin: auto">
        <tr style="height: 64px; width: 100%">
          <td>
            <table width="100%" border="0" cellpadding="0" cellspacing="0">
              <tr>
                <td align="center" valign="middle">
                  <img
                    src="cid:logo"
                    alt="Logo"
                    width="186"
                    height="33px"
                  />
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td>
            <hr
              style="
                width: 100%;
                border: 1px solid #e7e7e7;
                margin-top: -5px;
                margin-bottom: 17px;
              "
            />
          </td>
        </tr>
        <tr>
          <td
            style="
              width: 540px;
              height: 200px;
              background: linear-gradient(
                180deg,
                rgba(20, 201, 187, 0.1) 0%,
                rgba(216, 216, 216, 0) 100%
              );
              border-radius: 8px 8px 8px 8px;
            "
          >
            <table width="100%" border="0" cellpadding="0" cellspacing="0">
              <tr>
                <td align="center">
                  <img
                    src="cid:background"
                    alt="logo"
                    width="200px"
                    height="120px"
                    style="margin-top: 24px"
                  />
                </td>
              </tr>
              <tr>
                <td align="center" style="padding-top: 16px">
                  <h1
                    style="
                      font-size: 20px;
                      font-weight: 600;
                      margin-right: 8px;
                      display: inline;
                    "
                  >
                    {{ alert_title | capitalize }}
                  </h1>
                  <span
                    style="
                      background-color: {{ 
                        ('rgba(245, 63, 63, 0.1)' if alert_level == 'Error' else 
                        'rgba(20, 201, 187, 0.1)' if alert_level == 'Info' else 
                        'rgba(255, 187, 0, 0.1)' if alert_level == 'Warn' else 
                        'transparent') | trim
                      }};
                      color: {{ 
                        ('#f53f3f' if alert_level == 'Error' else 
                        '#14c9bb' if alert_level == 'Info' else 
                        '#FFBB00' if alert_level == 'Warn' else 
                        'black') | trim
                      }};
                      border: 1px solid {{ 
                        ('#f53f3f' if alert_level == 'Error' else 
                        '#14c9bb' if alert_level == 'Info' else 
                        '#FFBB00' if alert_level == 'Warn' else 
                        'transparent') | trim
                      }};
                      padding: 3px 8px;
                      border-radius: 2px;
                      width: 47px;
                      height: 20px;
                      margin-top: 15px;
                      text-align: center;
                      line-height: 20px;
                      "
                    >{{ alert_level }}</span
                  >
                  <p style="padding-top: 8px; padding-bottom: 0px">{{ msg }}</p>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td>
            <hr
              style="
                width: 100%;
                border: 1px solid #e7e7e7;
                margin-bottom: 17px;
              "
            />
          </td>
        </tr>
        <tr>
          <td style="padding-top: 5px">
            <div style="font-size: 18px; font-weight: 600; margin-bottom: 20px">
              Alert Details
            </div>
            <table
              style="
                border-collapse: collapse;
                /* margin-top: 0px;
              margin-right: 24px;
              margin-bottom: 0px;
              margin-left: 24px; */
                margin: 0 auto;
              "
            >
              <tr style="background-color: #f8fafb">
                <th
                  style="
                    color: #929a9e;
                    font-size: 14px;
                    font-weight: 500;
                    width: 135px;
                    text-align: left;
                    padding-left: 10px;
                  "
                >
                  Switch SN
                </th>
                <td style="padding: 10px 24px; text-align: right">
                  {{ switch_sn }}
                </td>
              </tr>
              <tr style="background-color: #ffffff">
                <th
                  style="
                    color: #929a9e;
                    font-size: 14px;
                    font-weight: 500;
                    width: 135px;
                    text-align: left;
                    padding-left: 10px;
                  "
                >
                  Switch Model
                </th>
                <td style="padding: 10px 24px; text-align: right">
                  {{ switch_model }}
                </td>
              </tr>
              <tr style="background-color: #f8fafb">
                <th
                  style="
                    color: #929a9e;
                    font-size: 14px;
                    font-weight: 500;
                    width: 135px;
                    text-align: left;
                    padding-left: 10px;
                  "
                >
                  Alert Scope
                </th>
                <td style="padding: 10px 24px; text-align: right">
                  {{ alert_scope }}
                </td>
              </tr>
              <tr style="background-color: #ffffff">
                <th
                  style="
                    color: #929a9e;
                    font-size: 14px;
                    font-weight: 500;
                    width: 135px;
                    text-align: left;
                    padding-left: 10px;
                  "
                >
                  Alert Level
                </th>
                <td style="padding: 10px 24px; text-align: right">
                  <span
                    style="
                    background-color: {{ 
                    ('rgba(245, 63, 63, 0.1)' if alert_level == 'Error' else 
                    'rgba(20, 201, 187, 0.1)' if alert_level == 'Info' else 
                    'rgba(255, 187, 0, 0.1)' if alert_level == 'Warn' else 
                    'transparent') | trim
                      }};
                    color: {{ 
                      ('#f53f3f' if alert_level == 'Error' else 
                      '#14c9bb' if alert_level == 'Info' else 
                      '#FFBB00' if alert_level == 'Warn' else 
                      'black') | trim
                    }};
                    border: 1px solid {{ 
                      ('#f53f3f' if alert_level == 'Error' else 
                      '#14c9bb' if alert_level == 'Info' else 
                      '#FFBB00' if alert_level == 'Warn' else 
                      'transparent') | trim
                    }};
                    padding: 3px 8px;
                    border-radius: 2px;
                  "
                    >{{ alert_level }}</span
                  >
                </td>
              </tr>
              <tr style="background-color: #f8fafb">
                <th
                  style="
                    color: #929a9e;
                    font-size: 14px;
                    font-weight: 500;
                    width: 135px;
                    text-align: left;
                    padding-left: 10px;
                  "
                >
                  Alert Type
                </th>
                <td style="padding: 10px 24px; text-align: right">
                  {{ alert_type }} > {{ alert_title }}
                </td>
              </tr>
              <tr style="background-color: #ffffff">
                <th
                  style="
                    color: #929a9e;
                    font-size: 14px;
                    font-weight: 500;
                    width: 135px;
                    text-align: left;
                    padding-left: 10px;
                  "
                >
                  Alert Content
                </th>
                <td style="padding: 10px 24px; text-align: right">{{ msg }}</td>
              </tr>
              <tr style="background-color: #f8fafb">
                <th
                  style="
                    color: #929a9e;
                    font-size: 14px;
                    font-weight: 500;
                    width: 135px;
                    text-align: left;
                    padding-left: 10px;
                  "
                >
                  Alert Time
                </th>
                <td style="padding: 10px 24px; text-align: right">
                  {{ alert_time }} GMT
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td>
            <hr
              style="width: 90%; border: 1px solid #e7e7e7; margin-top: 30px"
            />
          </td>
        </tr>
        <tr>
          <td>
            <div style="text-align: center">
              <p style="margin: 0 0 6px; color: #929a9e; font-size: 14px; line-height: 1.4">
                Please take appropriate action based or the severity of the
                alert.
              </p>
              <p style="margin: 0; color: #929a9e; font-size: 14px; line-height: 1.4">
                For further assistance, please visit
                <a
                  href="https://www.fs.com/tool/tool-home"
                  style="color: #14c9bb"
                  >AmpCon Support</a
                >.
              </p>
            </div>
          </td>
        </tr>
      </table>
    </div>
  </body>
</html>