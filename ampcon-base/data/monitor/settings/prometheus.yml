global:
  scrape_interval: 15s
  evaluation_interval: 5s

alerting:
  alertmanagers:
    - static_configs:
        - targets: ["alertmanager:9093"]

rule_files:
  - /etc/prometheus/rules/*.yml

scrape_configs:
  - job_name: "prometheus"
    static_configs:
      - targets: ["127.0.0.1:9090"]

  - job_name: "gnmijob"
    scrape_interval: 15s
    honor_timestamps: true
    static_configs:
      - targets: ["gnmi-exporter:5000"]

  - job_name: "snmpjob"
    scrape_interval: 15s
    honor_timestamps: true
    static_configs:
      - targets: [ "snmp-exporter:8000" ]

  - job_name: "node_exporter"
    scrape_interval: 15s
    file_sd_configs:
      - files: ['/etc/prometheus/settings/node_target.json']
        refresh_interval: 5s  # 每隔5秒检查一次