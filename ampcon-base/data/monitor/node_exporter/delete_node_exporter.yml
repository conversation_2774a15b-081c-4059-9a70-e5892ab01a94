---
- name: Setup Ampcon Exporter
  hosts: all
  become: yes
  tasks:
    - name: Stop ampcon_exporter service
      systemd:
        name: ampcon_exporter
        state: stopped
        enabled: no

    - name: Stop ampcon_exporter_agent service
      systemd:
        name: ampcon_exporter_agent
        state: stopped
        enabled: no

    - name: Remove systemd service file for ampcon_exporter
      file:
        path: /lib/systemd/system/ampcon_exporter.service
        state: absent

    - name: Remove systemd service file for ampcon_exporter_agent
      file:
        path: /lib/systemd/system/ampcon_exporter_agent.service
        state: absent

    - name: Reload systemd to pick up new service
      systemd:
        daemon_reload: yes

    - name: Remove ampcon_exporter binary
      file:
        path: /usr/local/bin/ampcon_exporter
        state: absent

    - name: Remove ampcon_exporter_agent binary
      file:
        path: /usr/local/bin/ampcon_exporter_agent
        state: absent

    - name: Remove textfile directory
      file:
        path: /etc/ampcon_exporter/textfile
        state: absent

    - name: Remove the nic info script
      file:
        path: /etc/ampcon_exporter/nic_info.sh
        state: absent
