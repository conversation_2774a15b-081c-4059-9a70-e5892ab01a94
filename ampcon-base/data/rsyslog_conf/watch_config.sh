#!/bin/bash

CONFIG_FILE="/etc/rsyslog.d/pica8.conf"
PREV_MD5=""

while true; do
  CUR_MD5=$(md5sum "$CONFIG_FILE" | awk '{ print $1 }')

  if [ "$PREV_MD5" != "$CUR_MD5" ]; then
    echo "Configuration file changed, reloading rsyslog..."

    pkill rsyslog
    sleep 1
    pkill -9 rsyslog

    if [ -f /run/rsyslogd.pid ]; then
      rm -rf /run/rsyslogd.pid
    fi

    rsyslogd
    PREV_MD5="$CUR_MD5"
  fi

  sleep 2
done
