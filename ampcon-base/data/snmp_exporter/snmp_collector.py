import os.path
import subprocess
from const import COLLECT_INTERVAL, EXPIRE_SEC, FINISH_COLLECT_RESULT_FILE_NAME, EXPORTER_EXPIRE_SEC, AMPCON_ADDRESS, IS_ENABLE_SN_VERIFY
from util.snmp_const import NUM_IGNORE_UNIT_PATTERN
from datetime import datetime
from util.logger import logger
import aiohttp
from util.snmp_util import *
from util.decrypt_utils import decrypt

import asyncio
import copy
import json
import time
import shutil
import aiofiles
import random


queue: asyncio.Queue[str] = asyncio.Queue()


async def cleanup_old_collect_dirs(base_path: str = './', prefix: str = 'collect_'):
    now = time.time()
    for name in os.listdir(base_path):
        if not name.startswith(prefix):
            continue
        dir_path = os.path.join(base_path, name)
        if not os.path.isdir(dir_path):
            continue

        ts_str = name[len(prefix):]
        try:
            ts = float(ts_str)
        except ValueError:
            logger.error(f"skip invalid timestamp dir: {name}")
            continue

        if now - ts > EXPIRE_SEC:
            try:
                await asyncio.to_thread(shutil.rmtree, dir_path, ignore_errors=True)
                logger.info(f"deleted old dir: {dir_path}")
            except Exception as e:
                logger.error(f"failed to delete {dir_path}: {e}")


async def scheduler(task_queues):
    is_first_start = True
    while True:
        if is_first_start:
            await asyncio.sleep(5)
            is_first_start = False
            continue
        start = time.time()
        await producer(task_queues)
        elapsed = time.time() - start
        await asyncio.sleep(max(0, COLLECT_INTERVAL - elapsed))


async def worker_scheduler(task_queue):
    is_first_start = True
    while True:
        if is_first_start:
            await asyncio.sleep(5)
            is_first_start = False
            continue
        start = time.time()
        await worker_producer(task_queue)
        elapsed = time.time() - start
        await asyncio.sleep(max(0, int(5 - elapsed)))


async def fetch_snmp_targets(retries: int = 3, delay: float = 1.0) -> dict:
    url = f"https://{AMPCON_ADDRESS}/ampcon/monitor/get_snmp_target"

    for attempt in range(1, retries + 1):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, ssl=False) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data
                    else:
                        logger.warning(f"[Attempt {attempt}] Failed with status code: {response.status}")
        except Exception as e:
            logger.warning(f"[Attempt {attempt}] Exception: {e}")

        if attempt < retries:
            await asyncio.sleep(delay * attempt) 

    logger.error("Failed to fetch snmp targets after all retries")
    return {}


async def producer(task_queues):
    timestamp = round(time.time())
    logger.info(f"Producer started at {datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')} with timestamp {timestamp}")
    device_collect_dict = await fetch_snmp_targets()

    logger.info(f"Fetched device collect dict: {device_collect_dict}")

    if not device_collect_dict:
        logger.warning("No devices fetched from get_snmp_target API")
        return

    if device_collect_dict['status'] == 200:
        for i, device in enumerate(device_collect_dict['data']):
            device_copy = copy.deepcopy(device)
            if device_copy.get('device_authentication_info', {}).get('snmp_version', '') == 'v3':
                device_copy['device_authentication_info']['auth_key'] = decrypt(device_copy['device_authentication_info']['auth_key']) if device_copy['device_authentication_info'].get('auth_key') else ''
                device_copy['device_authentication_info']['priv_key'] = decrypt(device_copy['device_authentication_info']['priv_key']) if device_copy['device_authentication_info'].get('priv_key') else ''
            device_copy['timestamp'] = timestamp
            q = task_queues[i % len(task_queues)]
            q.put(json.dumps(device_copy))
    else:
        logger.warning("Get target error, skipping device processing")
    os.makedirs(f"collect_{timestamp}", exist_ok=True)
    await cleanup_old_collect_dirs()


def fetch_all_tasks(q):
    tasks = []
    n = q.qsize()
    for _ in range(n):
        task = q.get()
        if task is None:
            break
        tasks.append(task)
    return tasks


async def worker_producer(task_queue):
    timestamp = round(time.time())
    logger.info(f"Worker producer started at {datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')} with timestamp {timestamp}")

    device_collect_list = fetch_all_tasks(task_queue)

    logger.info(f"Fetched device collect list: {device_collect_list}")

    if not device_collect_list:
        logger.warning("No devices fetched from queue")
        return

    for device in device_collect_list:
        await queue.put(device)

    logger.info(f"Queue size after adding devices: {queue.qsize()}")
    await cleanup_old_collect_dirs()


def build_mapping_func(mapping_type, mapping_content):
    if mapping_type == "static" and mapping_content:
        mapping_json = json.loads(mapping_content)
        return lambda x: mapping_json.get(x.strip(), "")
    elif mapping_type == "expression" and mapping_content:
        mapping_fuc = eval(mapping_content)
        return lambda x: mapping_fuc(x.strip())
    else:
        return lambda x: x 


async def data_generator(auth_info: dict, collect_oids: list, is_enable_sn_verify: bool = True) -> dict:
    """
    Parameters:
        auth_info: Device authentication information dictionary
        collect_oids: List of OIDs to be collected
    """
    snmp_connector = None
    mgt_ip = auth_info.get("mgt_ip", "unknown")
    current_sn = ""
    get_batch_params = []

    try:
        logger.info(f"[{mgt_ip}] Start data collection for device")

        vendor_handler = SnmpVendor(
            ip=mgt_ip,
            snmp_version=auth_info.get("snmp_version"),
            community=auth_info.get("community"),
            user_name=auth_info.get("username"),
            security_level=auth_info.get("security_level"),
            context_name=auth_info.get("context_name"),
            auth_protocol=auth_info.get("auth_protocol"),
            auth_key=auth_info.get("auth_key"),
            priv_protocol=auth_info.get("priv_protocol"),
            priv_key=auth_info.get("priv_key")
        )

        result = {
            "enable": str(auth_info.get("enable")),
            "sn": "",
            "mgt_ip": auth_info.get("mgt_ip"),
            "labels": {},
            "labels_info": {},
            "metrics": {},
            "metrics_info": {},
            "index_metrics": {}
        }

        try:
            snmp_connector = await asyncio.wait_for(vendor_handler.create_connector(), timeout=15)
        except asyncio.TimeoutError:
            logger.error(f"Device SN {auth_info.get('sn')} IP {mgt_ip} SNMP connector creation timed out")
            return result
        except Exception as e:
            logger.error(f"Device SN {auth_info.get('sn')} IP {mgt_ip} SNMP connector creation failed: {e}")
            return result

        await snmp_connector.connect()

        logger.info(f"Device SN {auth_info.get('sn')} IP {mgt_ip} SNMP connected successfully")

        logger.info(f"[{mgt_ip}] SNMP connected to device")
        current_sn = await snmp_connector.get_serial_number()

        if is_enable_sn_verify:
            result["sn"] = auth_info.get("sn") if current_sn and auth_info.get("sn") == current_sn else ""
        else:
            result["sn"] = auth_info.get("sn") if current_sn else ""

        if is_enable_sn_verify and current_sn and auth_info.get("sn") != current_sn:
            return result
        
        for param in collect_oids:
            metric_name = param.get("metric_name", "").lower()
            base_oid = param.get("base_oid", "")
            method = param.get("method", "")
            description = param.get("description", "") or ""
            metrics = param.get("metrics", [])
            mapping_type = param.get("mapping_type", "")
            mapping_content = param.get("mapping_content", "")

            mapper = build_mapping_func(mapping_type, mapping_content)

            try:
                if not metrics:
                    data_type = param.get("data_type", "")
                    unit = param.get("unit") or ""
                    snmp_metric_name = ""
                    if data_type == "DIMENSION":
                        result["labels_info"][metric_name] = {
                            "base_oid": base_oid,
                            "data_type": data_type,
                            "description": description,
                            "method": method,
                            "unit": unit
                        }
                    elif data_type == "METRIC":
                        snmp_metric_name = f"snmp_{metric_name}"
                        result["metrics_info"][snmp_metric_name] = {
                            "base_oid": base_oid,
                            "data_type": data_type,
                            "description": description,
                            "method": method,
                            "unit": unit
                        }
                    else:
                        logger.error(f"[{mgt_ip}] Unknown data_type '{data_type}' for '{metric_name}' (OID: {base_oid}), skipping...")
                        continue

                    if not base_oid:
                        continue

                    if method == "get":
                        get_batch_params.append(
                            (base_oid, metric_name, data_type, snmp_metric_name, mapper)
                        )
                    elif method == "walk":
                        walk_result = await snmp_connector.walk(base_oid)
                        if walk_result:
                            first_value = next(iter(walk_result.values()))
                            if data_type == "DIMENSION":
                                first_value = mapper(first_value)
                                result["labels"][metric_name] = first_value
                                logger.info(f"[{mgt_ip}] WALK DIMENSION: {metric_name} (OID: {base_oid})")
                            elif data_type == "METRIC":
                                first_value = first_value.strip()
                                match = NUM_IGNORE_UNIT_PATTERN.search(first_value)
                                metric_value = match.group() if match else first_value
                                result["metrics"][snmp_metric_name] = metric_value
                                logger.info(f"[{mgt_ip}] WALK METRIC: {metric_name} (OID: {base_oid})")
                    else:
                        raise ValueError(
                            f"[{mgt_ip}] Unsupported method '{method}' for '{metric_name}' (OID: {base_oid}). "
                            f"Only 'get' and 'walk' are allowed."
                        )

                else:
                    index_type = f"snmp_{param.get('index_name', '').lower()}"

                    if index_type not in result["index_metrics"]:
                        result["index_metrics"][index_type] = {
                            "labels": [],
                            "labels_info": {},
                            "metrics": [],
                            "metrics_info": {}
                        }
                    current_index_group = result["index_metrics"][index_type]
                    current_index_group["labels_info"][index_type] = {
                        "base_oid": base_oid,
                        "description": description,
                        "method": method,
                    }

                    target_oid_list = []
                    oid_to_subparam = {}
                    for sub_param in metrics:
                        metric_name = sub_param.get("metric_name", "").lower()
                        sub_metric_name = f"{index_type}_{metric_name}"
                        sub_base_oid = sub_param.get("base_oid", "")
                        sub_data_type = sub_param.get("data_type", "")
                        sub_description = sub_param.get("description", "") or ""
                        sub_method = sub_param.get("method", "")
                        sub_unit = sub_param.get("unit") or ""

                        if sub_data_type == "DIMENSION":
                            current_index_group["labels_info"][metric_name] = {
                                "base_oid": sub_base_oid,
                                "data_type": sub_data_type,
                                "description": sub_description,
                                "method": sub_method,
                                "unit": sub_unit
                            }
                        elif sub_data_type == "METRIC":
                            current_index_group["metrics_info"][sub_metric_name] = {
                                "base_oid": sub_base_oid,
                                "data_type": sub_data_type,
                                "description": sub_description,
                                "method": sub_method,
                                "unit": sub_unit
                            }
                        else:
                            logger.error(f"[{mgt_ip}] Unknown sub_data_type '{sub_data_type}' for '{sub_metric_name}' (OID: {sub_base_oid}), skipping...")
                            continue
                        
                        if sub_base_oid:
                            target_oid_list.append(sub_base_oid)
                            oid_to_subparam[sub_base_oid] = sub_param
                    
                    try:
                        walk_result = await snmp_connector.walk_get_data(
                            index_oid=base_oid,
                            target_oid_list=target_oid_list
                        )
                    except Exception as e:
                        logger.error(f"[{mgt_ip}] Walk get data failed (index oid: {base_oid}): {e}")
                        continue

                    index_labels_data_map = {}
                    # print("walk_result", walk_result)
                    for index_value, target_oid_data in walk_result.items():
                        if not index_value:
                            logger.warning(f"[{mgt_ip}] Skip empty index_value (OID: {base_oid})")
                            continue

                        dimension_entry = {
                            # "index_name": f"snmp_{index_type}",
                            "index_value": index_value
                        }
                        metric_entry = {
                            # "index_name": f"snmp_{index_type}",
                            "index_value": index_value
                        }

                        for target_oid, oid_info in target_oid_data.items():
                            sub_param = oid_to_subparam.get(target_oid)
                            if not sub_param:
                                logger.warning(f"[{mgt_ip}] Skip invalid target_oid (no sub_param): {target_oid}")
                                continue
                            
                            metric_name = sub_param.get("metric_name", "").lower()
                            sub_metric_name = f"{index_type}_{metric_name}"
                            sub_data_type = sub_param.get("data_type", "")
                            sub_method = sub_param.get("method", "")
                            sub_mapper = build_mapping_func(sub_param.get("mapping_type", ""), sub_param.get("mapping_content", ""))

                            value = oid_info.get("value")
                            full_oid = oid_info.get("oid")
                            
                            if value:
                                if sub_data_type == "DIMENSION":
                                    dimension_entry[metric_name] = sub_mapper(value)
                                    logger.info(
                                        f"[{mgt_ip}] Regular: Batch Walk (walk_get_data) DIMENSION | index: {index_value} "
                                        f"| metric: {metric_name} | OID: {full_oid} | Value: {value}"
                                    )
                                elif sub_data_type == "METRIC":
                                    value = value.strip()
                                    match = NUM_IGNORE_UNIT_PATTERN.search(value)
                                    processed_value = match.group() if match else value
                                    metric_entry[sub_metric_name] = processed_value
                                    logger.info(
                                        f"[{mgt_ip}] Regular: Batch Walk (walk_get_data) METRIC | index: {index_value} "
                                        f"| metric: {sub_metric_name} | OID: {full_oid} "
                                        f"| Original value (batch get): {value} | Processed value: {processed_value} "
                                        f"| Regex match: {'Success' if match else 'Failed/Empty'}"
                                    )
                            elif sub_method == "walk" and full_oid:
                                try:
                                    index_walk_result = await snmp_connector.walk(full_oid)
                                except Exception as e:
                                    logger.error(
                                        f"[{mgt_ip}] Fallback failed: Single walk OID {full_oid} error | {str(e)}"
                                    )
                                    continue

                                if index_walk_result:
                                    first_value = next(iter(index_walk_result.values()))
                                    if sub_data_type == "DIMENSION":
                                        dimension_entry[metric_name] = sub_mapper(first_value)
                                        logger.info(
                                            f"[{mgt_ip}] Fallback: Single Walk DIMENSION success | index: {index_value} "
                                            f"| metric: {metric_name} | OID: {full_oid} | Value (single walk): {first_value}"
                                        )
                                    elif sub_data_type == "METRIC":
                                        first_value = first_value.strip()
                                        match = NUM_IGNORE_UNIT_PATTERN.search(first_value)
                                        processed_value = match.group() if match else first_value
                                        metric_entry[sub_metric_name] = processed_value
                                        logger.info(
                                            f"[{mgt_ip}] Fallback: Single Walk METRIC success | index: {index_value} "
                                            f"| metric: {sub_metric_name} | OID: {full_oid} "
                                            f"| Original value (batch get): {value} (empty) | Value (single walk): {first_value} "
                                            f"| Processed value: {processed_value} | Regex match: {'Success' if match else 'Failed/Empty'}"
                                        )

                        index_labels_data_map[index_value] = dimension_entry
                        current_index_group["metrics"].append(metric_entry)

                    current_index_group["labels"] = list(index_labels_data_map.values())

            except Exception as e:
                logger.error(f"[{mgt_ip}] Collect OID {base_oid} ({metric_name}) failed: {e}")
                continue

        if get_batch_params:
            oids_to_get = [item[0] for item in get_batch_params]
            batch_get_result = await snmp_connector.get(oids_to_get, mode="batch")
            for (base_oid, metric_name, data_type, snmp_metric_name, mapper) in get_batch_params:
                value = batch_get_result.get(base_oid)
                if value:
                    if data_type == "DIMENSION":
                        result["labels"][metric_name] = mapper(value)
                        logger.info(f"[{mgt_ip}] BATCH GET DIMENSION: {metric_name} (OID: {base_oid})")
                    elif data_type == "METRIC":
                        value = value.strip()
                        match = NUM_IGNORE_UNIT_PATTERN.search(value)
                        metric_value = match.group() if match else value
                        result["metrics"][snmp_metric_name] = metric_value
                        logger.info(f"[{mgt_ip}] BATCH GET METRIC: {metric_name} (OID: {base_oid})")
                else:
                    logger.warning(f"[{mgt_ip}] BATCH GET no value: {metric_name} (OID: {base_oid})")

        logger.info(f"[{mgt_ip}] Data collection for device {auth_info['mgt_ip']} completed")
        return result 
    except Exception as e:
        logger.error(f"[{mgt_ip}] Data collection failed: {e}")
        return {
            "enable": str(auth_info.get("enable")),
            "sn": auth_info.get("sn") if auth_info.get("sn") == current_sn else "",
            "mgt_ip": auth_info.get("mgt_ip"),
            "labels": {},
            "labels_info": {},
            "metrics": {},
            "metrics_info": {}, 
            "index_metrics": {},
            "error": str(e)
        }
    finally:
        if snmp_connector:
            snmp_connector.close()
            logger.info(f"[{mgt_ip}] SNMP connection closed")


async def read_one_file(file_path):
    try:
        async with aiofiles.open(file_path, mode="r", encoding="utf-8") as f:
            text = await f.read()
        return json.loads(text)
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return {}


async def get_latest_data(path):
    res = []
    tasks = []

    if not path:
        return res
    for entry in os.scandir(path):
        if not entry.is_file():
            continue
        if FINISH_COLLECT_RESULT_FILE_NAME.split('.')[0] in entry.name:
            continue

        file_path = os.path.join(path, entry.name)
        tasks.append(read_one_file(file_path))
    return await asyncio.gather(*tasks, return_exceptions=True)


async def write_file_with_replace(file_path, content):
    tmp_file_path = file_path + str(time.time()) + ".tmp"
    try:
        async with aiofiles.open(tmp_file_path, mode="w", encoding="utf-8") as f:
            await f.write(content)
        os.replace(tmp_file_path, file_path)
    except Exception as e:
        logger.error(f"Error writing file {file_path}: {e}")
        if os.path.exists(tmp_file_path):
            os.remove(tmp_file_path)


async def worker(worker_id):
    while True:
        raw = None
        try:
            raw = await queue.get()
            if raw is None:
                continue
            job = json.loads(raw)
            auth_info = job['device_authentication_info']
            collect_oids = job['device_collect_oid_params']
            timestamp = job['timestamp']

            logger.info(f"Checking existence of directory collect_{timestamp}: {os.path.exists(f'collect_{timestamp}')}")
            collect_dir = f"collect_{timestamp}"
            device_sn = auth_info.get('sn', f"unknown_{auth_info['mgt_ip']}")
            if os.path.exists(collect_dir):
                device_collect_result_file_path = f"{collect_dir}/device_{device_sn}.json"
                if os.path.exists(device_collect_result_file_path):
                   logger.error(f"worker {worker_id} found existing file for job: {job}, skipping")
                else:
                    collect_res = {}
                    try:
                        collect_res = await asyncio.wait_for(data_generator(auth_info, collect_oids, is_enable_sn_verify=IS_ENABLE_SN_VERIFY), timeout=45)
                    except Exception as e:
                        collect_res = {
                            "enable": "1",
                            "sn": device_sn,
                            "mgt_ip": auth_info.get("mgt_ip", "unknown"),
                            "labels": {},
                            "labels_info": {},
                            "metrics": {},
                            "metrics_info": {},
                            "index_metrics": {},
                            "error": "SNMP error: No SNMP response received before timeout"
                        }
                        logger.error(f"worker {worker_id} failed to get data for job: {job}, error: {e}")
                    finally:
                        await write_file_with_replace(device_collect_result_file_path, json.dumps(collect_res, ensure_ascii=False, indent=4))
                    logger.info(f"worker {worker_id} processed job: {job}")
                await asyncio.sleep(3)
                device_collect_finish_flag_path = f"{collect_dir}/{FINISH_COLLECT_RESULT_FILE_NAME}"
                await asyncio.sleep(random.uniform(0, 10))
                current_file_count = sum(1 for entry in os.scandir(collect_dir) if entry.is_file() and not entry.name.endswith(".tmp"))
                logger.info("device_count: {}, current file count: {}".format(job['device_count'], current_file_count))
                if time.time() - timestamp > EXPORTER_EXPIRE_SEC / 2 or (not os.path.exists(device_collect_finish_flag_path) and (current_file_count >= int(job['device_count']))):
                    await asyncio.sleep(random.uniform(1, 3))
                    try:
                        latest_data = await get_latest_data(collect_dir)
                        if latest_data:
                            await write_file_with_replace(device_collect_finish_flag_path, json.dumps([item for item in latest_data if item != {}], indent=4))
                    except Exception as e:
                        logger.error(f"worker {worker_id} failed to get latest data for job: {job}, error: {e}")
                        continue
                    await asyncio.sleep(random.uniform(0, 1))
                    logger.info(f"worker {worker_id} finished collecting for timestamp {timestamp}")
        except Exception as e:
            logger.error(f"worker {worker_id} encountered an error: {e}")
            continue
        finally:
            if raw is not None:
                queue.task_done()
            if queue.empty():
                await asyncio.sleep(10)
