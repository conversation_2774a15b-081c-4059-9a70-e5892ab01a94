import logging
from logging.config import dictConfig

dictConfig({
    'version': 1,
    'formatters': {
        'default': {
            # 'format': '[%(process)d] %(asctime)s %(levelname)s [%(filename)s:%(lineno)s] %(message)s',
            'format': '%(asctime)s %(levelname)s %(process)d %(thread)d %(module)s[line:%(lineno)d] %(message)s',
        }
    },
    'handlers': {
        'wsgi': {
            'class': 'logging.StreamHandler',
            'stream': 'ext://sys.stdout',
            'formatter': 'default'
        },
        # 'file': {
        #     'class': 'logging.handlers.RotatingFileHandler',
        #     'filename': 'app.log',
        #     'maxBytes': 10 * 1024 * 1024,  # 10 MB
        #     'backupCount': 5,
        #     'formatter': 'default',
        #     'level': 'INFO'
        # }
    },
    'root': {
        'level': 'INFO',
        'handlers': ['wsgi']
    }
})

logger = logging.getLogger(__name__)