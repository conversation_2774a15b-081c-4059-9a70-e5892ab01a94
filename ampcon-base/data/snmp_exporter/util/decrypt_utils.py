import base64
from Crypto.Cipher import AES
from Crypto import Random
import os


KEY = "pica8pica8                      "
BS = 16


def decrypt(enc):
    if not enc:
        return ''
    # First base64 decode the ciphertext
    if len(enc) % 4:
        # not a multiple of 4, add padding:
        enc += '=' * (4 - len(enc) % 4)
    enc = base64.b64decode(enc)
    # get iv
    iv = enc[:16]
    cipher = AES.new(KEY.encode(), AES.MODE_CBC, iv)
    # Returns data in utf-8
    drop_padding = lambda s: s[:-ord(s[len(s) - 1:])]
    return drop_padding((cipher.decrypt(enc[BS:])).decode())


if __name__ == '__main__':
    print(decrypt('SSyn9Hi61hw1hwxsvof1hl3uEpaLKlmauDun3ueJkq4='))
