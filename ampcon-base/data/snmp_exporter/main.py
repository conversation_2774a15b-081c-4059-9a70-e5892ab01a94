from snmp_collector import scheduler, worker, worker_scheduler
from const import NUM_WORKERS, EXPORTER_EXPIRE_SEC, FINISH_COLLECT_RESULT_FILE_NAME
from util.logger import logger

from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, Response
from prometheus_client import Counter, generate_latest, CONTENT_TYPE_LATEST, REGISTRY, GC_COLLECTOR, PROCESS_COLLECTOR, PLATFORM_COLLECTOR, Gauge
from prometheus_client.core import GaugeMetricFamily
import multiprocessing
import uvicorn
import asyncio
import os
import glob
import re
import time
import json
import sys


BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(BASE_DIR)

queue: asyncio.Queue[str] = asyncio.Queue()


REGISTRY.unregister(GC_COLLECTOR)
REGISTRY.unregister(PROCESS_COLLECTOR)
REGISTRY.unregister(PLATFORM_COLLECTOR)


task_queues = []
processes = []


def worker_main(worker_task_queue):
    # start up
    async def worker_main_async():
        logger.info("Starting up worker scheduler and workers")
        tasks = [asyncio.create_task(worker_scheduler(worker_task_queue), name="scheduler")]
        for i in range(NUM_WORKERS):
            tasks.append(asyncio.create_task(worker(i), name=f"worker-{i}"))
        await asyncio.sleep(0.5)
        await asyncio.gather(*tasks, return_exceptions=True)
        logger.info("Shutdown complete")

    asyncio.run(worker_main_async())


@asynccontextmanager
async def lifespan(app: FastAPI):
    # start up
    logger.info("Starting up scheduler and workers")
    tasks = [asyncio.create_task(scheduler(task_queues), name="scheduler")]
    # create sub-process for worker_scheduler
    for i in range(max(2, 2 * (os.cpu_count() - 1))):
        q = multiprocessing.Queue()
        task_queues.append(q)
        p = multiprocessing.Process(target=worker_main, args=(task_queues[i],))
        processes.append(p)
        p.start()
    await asyncio.sleep(0.5)
    yield
    logger.info("Shutting down workers and scheduler")
    for p in processes:
        p.join()
    for t in tasks:
        t.cancel()
    await asyncio.gather(*tasks, return_exceptions=True)
    logger.info("Shutdown complete")


app = FastAPI(lifespan=lifespan)


def get_recent_finish_collect_res():
    res_path = None
    time_gap = EXPORTER_EXPIRE_SEC

    pattern = os.path.join('./', f"collect_*")
    dirs = [d for d in glob.glob(pattern) if os.path.isdir(d)]
    if not dirs:
        return {}
    for dir_path in dirs:
        dir_timestamp_str = os.path.basename(dir_path).split('_')[-1]
        if re.match(r'^\d+$', dir_timestamp_str):
            dir_timestamp = int(dir_timestamp_str)
        else:
            continue

        temp_time_gap = time.time() - dir_timestamp
        if temp_time_gap < time_gap and os.path.exists(os.path.join(dir_path, FINISH_COLLECT_RESULT_FILE_NAME)):
            time_gap = temp_time_gap
            res_path = dir_path
    if res_path is None or not os.path.exists(os.path.join(res_path, FINISH_COLLECT_RESULT_FILE_NAME)):
        return {}
    try:
        with open(os.path.join(res_path, FINISH_COLLECT_RESULT_FILE_NAME), 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logger.error(f"Error reading collect result file: {e}")
        return {}


def get_collect_unique(collect_data_list, fn):
    res = set()
    for single_collect_data in collect_data_list:
        res.update(list(fn(single_collect_data)))
    return list(res)


def get_first_not_empty_item(data_list, fn):
    if not data_list:
        return {}
    for i in data_list:
        if fn(i):
            return i
    return {}


class MetricsCollector:

    def __init__(self):
        pass

    def collect(self):
        collect_data_res = get_recent_finish_collect_res()
        if not collect_data_res:
            logger.warning("No recent collect results found")
            return
        # -------------------------- 1. 处理设备级标签 --------------------------
        label_info_list = get_collect_unique(collect_data_res, lambda x: x.get("labels_info", {}).keys())
        device_label = GaugeMetricFamily(
            "snmp_device_label",
            "Basic information of SNMP-managed switch",
            labels=["sn", "mgt_ip"] + label_info_list,
        )
        for device_data in collect_data_res:
            # enable = device_data.get("enable", "")
            sn = device_data.get("sn", "")
            ip = device_data.get("mgt_ip", "")

            device_labels = device_data.get("labels", {})
            dynamic_label_values = [device_labels.get(key, "") for key in label_info_list]

            if len(dynamic_label_values) < len(label_info_list):
                dynamic_label_values += [""] * (len(label_info_list) - len(dynamic_label_values))
            elif len(dynamic_label_values) > len(label_info_list):
                dynamic_label_values = dynamic_label_values[:len(label_info_list)]

            if sn:
                device_label.add_metric(
                    [sn, ip] + dynamic_label_values,
                    1
                )
        yield device_label

        # -------------------------- 2. 处理设备级 metrics --------------------------
        device_metric_list = []
        device_metric_label_list = ["sn", "mgt_ip", "base_oid", "data_type", "description", "method", "unit"]

        metrics_info_sample = get_first_not_empty_item(collect_data_res, lambda x: x.get("metrics_info"))

        for metric_key_name in metrics_info_sample.get("metrics_info", {}).keys():
            # if metric_key_name in metric_label_list:
            #     continue
            metric_desc = metrics_info_sample.get("metrics_info", {}).get(metric_key_name, {}).get("description")
            device_metric_list.append(GaugeMetricFamily(
                metric_key_name,
                metric_desc,
                labels=device_metric_label_list
            ))
        for device_data in collect_data_res:
            # enable = device_data.get("enable", "")
            sn = device_data.get("sn", "")
            ip = device_data.get("mgt_ip", "")
            for metric in device_metric_list:
                metric_info = device_data.get("metrics_info", {}).get(metric.name, {})
                base_oid = metric_info.get("base_oid", "")
                data_type = metric_info.get("data_type", "")
                description = metric_info.get("description", "")
                method = metric_info.get("method", "")
                unit = metric_info.get("unit", "")
                metric.add_metric(
                    [sn, ip, base_oid, data_type, description, method, unit],
                    device_data.get("metrics", {}).get(metric.name)
                ) if device_data.get("metrics", {}).get(metric.name) is not None else None
        for metric in device_metric_list:
            yield metric

        # -------------------------- 3. 处理多索引数据 --------------------------
        index_metrics_sample = get_first_not_empty_item(collect_data_res, lambda x: x.get("index_metrics"))

        if not index_metrics_sample.get("index_metrics"):
            logger.warning("No index metrics found in collect results")
            return
        first_device_index_metrics = index_metrics_sample.get("index_metrics", {})
        all_index_types = list(first_device_index_metrics.keys())
        for index_type in all_index_types:
            first_index_group = first_device_index_metrics.get(index_type, {})
            first_index_labels_info = first_index_group.get("labels_info", {})
            first_index_metrics_info = first_index_group.get("metrics_info", {})

            index_labels_info_list = ["sn", "mgt_ip", "index_value"]
            for label_key in first_index_labels_info.keys():
                if label_key != index_type:
                    index_labels_info_list.append(label_key)

            index_desc = first_index_labels_info.get(index_type, {}).get("description", "")
            index_labels = GaugeMetricFamily(
                index_type,
                index_desc,
                labels=index_labels_info_list
            )

            index_metrics_list = []
            index_metrics_info_list = ["sn", "mgt_ip", "index_name", "index_value", "base_oid", "data_type", "description", "method", "unit"]

            for metric_key in first_index_metrics_info.keys():

                metric_desc = first_index_metrics_info.get(metric_key, {}).get("description", "")
                index_metrics_list.append(GaugeMetricFamily(
                    metric_key,
                    metric_desc,
                    labels=index_metrics_info_list
                ))

            # -------------------------- 填充当前索引的所有设备数据 --------------------------
            for device_data in collect_data_res:
                # enable = device_data.get("enable", "")
                sn = device_data.get("sn", "")
                ip = device_data.get("mgt_ip", "")

                device_index_group = device_data.get("index_metrics", {}).get(index_type, {})
                device_index_labels = device_index_group.get("labels", [])
                device_index_metrics = device_index_group.get("metrics", [])
                device_index_metrics_info = device_index_group.get("metrics_info", {})

                for label in device_index_labels:
                    label_values = [sn, ip, str(label.get("index_value", ""))] + [
                        label.get(key, "") for key in index_labels_info_list
                        if key not in ["sn", "mgt_ip", "index_value"]
                    ]
                    if label.get("index_value", -1) != -1:
                        index_labels.add_metric(label_values, label.get("index_value"))

                for metric_entry in device_index_metrics:
                    index_name = index_type
                    index_value = metric_entry.get("index_value", "-1")

                    for index_metric in index_metrics_list:
                        metric_key = index_metric.name

                        metric_info = device_index_metrics_info.get(metric_key, {})
                        base_oid = metric_info.get("base_oid", "")
                        data_type = metric_info.get("data_type", "")
                        description = metric_info.get("description", "")
                        method = metric_info.get("method", "")
                        unit = metric_info.get("unit", "")

                        index_metric.add_metric(
                            [sn, ip, index_name, index_value, base_oid, data_type, description, method, unit],
                            metric_entry.get(metric_key)
                        ) if metric_entry.get(metric_key) is not None else None

            yield index_labels
            for metric in index_metrics_list:
                yield metric


collector = MetricsCollector()
REGISTRY.register(collector)


@app.get("/metrics")
async def metrics():
    logger.info('Metrics endpoint called')
    return Response(content=generate_latest(), media_type=CONTENT_TYPE_LATEST)


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)