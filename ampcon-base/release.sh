#!/bin/bash
set -e
source .env

AMPCON_VERSION=""

if [ -n "$CI_COMMIT_BRANCH" ]; then
    AMPCON_BRANCH_NAME="$CI_COMMIT_BRANCH"
else
    AMPCON_BRANCH_NAME=$(git rev-parse --abbrev-ref HEAD)
fi

DOWNLOAD_TEMPLATE_URL="http://************:8000/template/Jinja_Templates-main.zip"
TEMP_TEMPLATE_ZIP="Jinja_Templates-main.zip"
EXTRACTED_TEMPLATE_DIR="./Jinja_Templates-main/configuration_templates"

DOWNLOAD_PLAYBOOK_URL="http://************:8000/playbook/Ansible-main.zip"
TEMP_PLAYBOOK_ZIP="Ansible-main.zip"
EXTRACTED_PLAYBOOK_DIR="./Ansible-main/playbooks"

TARGET_DIR="./data/pre-built"

MODULE_TYPE=""
FULL_PACKAGE=""
if [ "$2" == "--full" ]; then
    FULL_PACKAGE="full"
  fi
if [ "$2" == "--clear" ]; then
    FULL_PACKAGE="clear"
  fi
declare -a RELEASE_PACK_FILES

RELEASE_PACK_FILES=("./data" "./docker_images" ".env" "./docker-dependencies/docker-scripts/for-linux/install_or_upgrade.sh" "./docker-dependencies/docker-scripts/for-linux/restore.sh")


check_build_module_type() {
  if [ "$#" -lt 1 ]; then
    echo "Usage: $0 {--ampcon-super|--ampcon-t|--ampcon-dc|--ampcon-campus|--ampcon-smb}"
    exit 1
  fi

  case "$1" in
    --ampcon-super)
        MODULE_TYPE="ampcon-super"
        RELEASE_PACK_FILES+=(
            "./backend/server-otn"
            "./docker-dependencies/docker-compose/backend/docker-compose-ampcon-super.yml"
        )
        ;;
    --ampcon-t)
        MODULE_TYPE="ampcon-t"
        RELEASE_PACK_FILES+=(
            "./backend/server-otn"
            "./docker-dependencies/docker-compose/backend/docker-compose-ampcon-t.yml"
        )
        ;;
    --ampcon-dc)
        MODULE_TYPE="ampcon-dc"
        RELEASE_PACK_FILES+=(
            "./docker-dependencies/docker-compose/backend/docker-compose-ampcon-dc.yml"
        )
        ;;
    --ampcon-campus)
        MODULE_TYPE="ampcon-campus"
        RELEASE_PACK_FILES+=(
            "./smb"
            "./docker-dependencies/docker-compose/backend/docker-compose-ampcon-campus.yml"
        )
        ;;
    --ampcon-smb)
        MODULE_TYPE="ampcon-smb"
        RELEASE_PACK_FILES+=(
            "./smb"
            "./docker-dependencies/docker-compose/backend/docker-compose-ampcon-smb.yml"
        )
        ;;
    *)
        echo "Invalid argument: $1"
        echo "Usage: $0 {--ampcon-super|--ampcon-t|--ampcon-dc|--ampcon-campus|--ampcon-smb}"
        exit 1
        ;;
  esac
  AMPCON_VERSION=$(python3 -c "import json; print(json.load(open('release.json'))['${MODULE_TYPE}']['version'])")
}

change_mode_file() {
  chmod 777 *.sh
  chmod 777 ./docker-dependencies/docker-scripts/for-linux/*.sh
  chmod 777 ./data/init_sql/init.sh
  chmod 777 ./data/rsyslog_conf/rsyslog_healthcheck.sh
  chmod 777 ./data/rabbitmq/rabbitmq_health_check.sh
  chmod 777 ./data/celery_app/celery_worker_health_check.sh
  chmod 777 ./data/rsyslog_conf/start_rsyslog.sh
  chmod 777 ./data/rsyslog_conf/watch_config.sh
}

clean_download_files() {
  echo ">>>>>>>>>>>>>>>>>>>  Clean download initial data start         <<<<<<<<<<<<<<<<<<<<<"
  rm -rf ./data/ansible_playbook/*
  rm -rf ./data/pre-built/playbook/*
  rm -rf ./data/pre-built/template/*
  # rm -rf ./data/img/*
  rm -rf ./frontend/.env
  #rm -rf ./data/monitor/rules/*
  ls ./data/monitor/rules/ | grep -v 'common_rules.yml' | sed 's|^|./data/monitor/rules/|' | xargs -r rm
  echo ">>>>>>>>>>>>>>>>>>>  Clean download initial data successfully  <<<<<<<<<<<<<<<<<<<<<"
}

download_playbook_template() {
#  if [ "${MODULE_TYPE}" == "ampcon-t" ]; then
#    return
#  fi
  echo ">>>>>>>>>>>>>>>>>>>  Download initial playbook templates start <<<<<<<<<<<<<<<<<<<<<"
  wget --timeout=20 --tries=3 "$DOWNLOAD_TEMPLATE_URL" -O "$TEMP_TEMPLATE_ZIP"
  if [ $? -eq 0 ]; then
    unzip -q "$TEMP_TEMPLATE_ZIP"
    mv "$EXTRACTED_TEMPLATE_DIR"/* "$TARGET_DIR"/template
    rm -rf "$TEMP_TEMPLATE_ZIP" $(dirname "$EXTRACTED_TEMPLATE_DIR")
  else
    exit 0
  fi

  wget --timeout=20 --tries=3 "$DOWNLOAD_PLAYBOOK_URL" -O "$TEMP_PLAYBOOK_ZIP"
  if [ $? -eq 0 ]; then
    unzip -q "$TEMP_PLAYBOOK_ZIP"
    mv "$EXTRACTED_PLAYBOOK_DIR"/* "$TARGET_DIR"/playbook
    rm -rf "$TEMP_PLAYBOOK_ZIP" $(dirname "$EXTRACTED_PLAYBOOK_DIR")
    echo ">>>>>>>>>>>>>>>>>>>  Download initial playbook template successfully  <<<<<<<<<<<<<<"
  else
    echo ">>>>>>>>>>>>>>>>>>>  Download initial playbook template failed  <<<<<<<<<<<<<<<<<<<<"
  fi
}

download_image() {
  if [ "${MODULE_TYPE}" == "ampcon-t" ]; then
    return
  elif [ "${MODULE_TYPE}" == "ampcon-dc" ]; then
    json_file="./data/settings/inner/default_imgs_for_dc.json"
  elif [ "${MODULE_TYPE}" == "ampcon-campus" ]; then
    json_file="./data/settings/inner/default_imgs_for_campus.json"
  else
    json_file="./data/settings/inner/default_imgs.json"
  fi
  echo ">>>>>>>>>>>>>>>>>>>  Download initial picos image start ...    <<<<<<<<<<<<<<<<<<<<<<<"
  target_folder="./data/img"
  
  # 创建一个临时文件来存储需要保留的文件名
  temp_file_list=$(mktemp)
  
  # 获取需要下载的文件列表
  grep -o '"http[^"]*"' "$json_file" | tr -d '"' | while read -r url; do
    filename=$(basename "$url")
    echo "$filename" >> "$temp_file_list"
    # 检查文件是否已存在，如果存在则跳过下载
    if [ -f "$target_folder/$filename" ]; then
      echo "File $target_folder/$filename already exists, skipping download"
    else
      echo "curl -o $target_folder/$filename --connect-timeout 30 --retry 3 $url"
      curl -o "$target_folder/$filename" --connect-timeout 30 --retry 3 "$url"
    fi
  done
  
  # 删除不在下载列表中的文件
  if [ -d "$target_folder" ]; then
    for file in "$target_folder"/*; do
      if [ -f "$file" ]; then
        filename=$(basename "$file")
        if ! grep -q "^$filename$" "$temp_file_list"; then
          echo "Removing file not in download list: $file"
          rm -f "$file"
        fi
      fi
    done
  fi
  
  # 清理临时文件
  rm -f "$temp_file_list"
  
  echo ">>>>>>>>>>>>>>>>>>>  Download initial picos image completed    <<<<<<<<<<<<<<<<<<<<<<<"
}

update_version(){
  version=$(git rev-parse HEAD | cut -c1-10)
  echo ">>>>>>>>>>>>>>> current export package version: ${version}    <<<<<<<<<<<<<<<<<<<<<"
  sed -i "s/^PRO_TYPE=.*/PRO_TYPE=${MODULE_TYPE}/g" .env
  sed -i "s/^REACT_APP_VERSION=.*/REACT_APP_VERSION=${version}/g" .env
  sed -i "s/^REACT_APP_BRANCH=.*/REACT_APP_BRANCH=${AMPCON_BRANCH_NAME}/g" .env
  sed -i "s/^REACT_APP_MAJOR_VERSION=.*/REACT_APP_MAJOR_VERSION=${AMPCON_VERSION}/g" .env
  python3 ./docker-dependencies/docker-scripts/encrypt_tool.py "${version}"
}

clean_all_docker_images(){
  if [ "$(docker ps -q)" ]; then
    sudo docker rm -f $(sudo docker ps -aq)
  fi
  if [ "$FULL_PACKAGE" == "clear" ]; then
    echo ">>>>>>>>>>>>>>>>>>>  Clear all docker images start ...    <<<<<<<<<<<<<<<<<<<<<<<"
    yes | sudo docker system prune -a
    echo ">>>>>>>>>>>>>>>>>>>  Clear all docker images successfully <<<<<<<<<<<<<<<<<<<<<<"
  else
    # clean all docker images with not used
    yes | sudo docker system prune
  fi
}

download_task(){
  clean_download_files
  download_playbook_template
  download_image
}

prepare_docker_env(){
  if grep -q "${IMG_CENTER_PROXY_IP} harbor.ampcon.com" /etc/hosts; then
    echo ">>>>>>>>>>>>>>>>>>>   add harbor.ampcon.com to hosts     <<<<<<<<<<<<<<<<<<<<<"
  else
      echo "${IMG_CENTER_PROXY_IP} harbor.ampcon.com" | sudo tee -a /etc/hosts > /dev/null
  fi

  if [ ! -d "/etc/docker/certs.d" ]; then
      sudo mkdir -p /etc/docker/certs.d
  fi
  sudo cp -r ./docker-dependencies/docker-scripts/harbor.ampcon.com /etc/docker/certs.d/
}

build_web(){
  echo ">>>>>>>>>>>>>>>>>>>  Build AmpCon-Web dist volumes start ...    <<<<<<<<<<<<<<<<<<<<<<<"
  sudo docker pull harbor.ampcon.com/ampcon-t-base/node:alpine
  rm -rf ./data/dist
  mkdir ./data/dist
  cp .env ./frontend/.env
  cp ./docker-dependencies/docker-compose/frontend/docker-compose-web.yml docker-compose-web.yml

  export MODULE_TYPE=${MODULE_TYPE}
  echo "MODULE_TYPE=${MODULE_TYPE}" > .temp.env
  # docker compose -f ./docker-compose-web.yml build --build-arg MODULE_TYPE=${MODULE_TYPE} --build-arg IMG_CENTER_PROXY_DOMAIN=${IMG_CENTER_PROXY_DOMAIN}
  docker compose -f ./docker-compose-web.yml --env-file .temp.env up
  rm -f docker-compose-web.yml
  rm -f .temp.env
  echo ">>>>>>>>>>>>>>>>>>>  Build AmpCon-Web dist volumes successfully <<<<<<<<<<<<<<<<<<<<<<"
}

common_operation() {
  download_task
  change_mode_file
  update_version
  prepare_docker_env
  build_web
  clean_all_docker_images
  ./docker-dependencies/docker-scripts/for-linux/export_images.sh $MODULE_TYPE $FULL_PACKAGE
}

tar_package(){
  RELEASE_PACKAGE="${MODULE_TYPE}-${AMPCON_VERSION}-release-${version}"
  mkdir $RELEASE_PACKAGE
  cp ./docker-dependencies/docker-scripts/for-linux/import_images.sh "${RELEASE_PACKAGE}/.import_images.sh"
  cp ./docker-dependencies/docker-scripts/for-linux/start.sh "${RELEASE_PACKAGE}/.start.sh"
  cp ./docker-dependencies/docker-scripts/for-linux/stop.sh "${RELEASE_PACKAGE}/.stop.sh"
  cp ./docker-dependencies/docker-scripts/for-linux/update_openvpn_subnet.sh "${RELEASE_PACKAGE}/.update_openvpn_subnet.sh"
  cp ./docker-dependencies/docker-scripts/for-linux/fix_openvpn_subnet_mask.sh "${RELEASE_PACKAGE}/.fix_openvpn_subnet_mask.sh"
  mv ./release "${RELEASE_PACKAGE}/.release"

  for FILE in "${RELEASE_PACK_FILES[@]}"; do
    cp -r "${FILE}" "${RELEASE_PACKAGE}"
  done

  mkdir -p ${RELEASE_PACKAGE}/data/nginx/sites-enabled
  rm -rf ${RELEASE_PACKAGE}/data/nginx/sites-enabled/*
  cp ./data/settings/outer/pica8-site-${MODULE_TYPE} ${RELEASE_PACKAGE}/data/nginx/sites-enabled/pica8-site

  touch ${RELEASE_PACKAGE}/data/settings/inner/upgrade_automation.ini

  mkdir -p $RELEASE_PACKAGE/docker-dependencies/dockerfile
  cp -r ./docker-dependencies/dockerfile ${RELEASE_PACKAGE}/docker-dependencies

  mv ${RELEASE_PACKAGE}/docker-compose-${MODULE_TYPE}.yml ${RELEASE_PACKAGE}/docker-compose.yml

  rm -rf ${RELEASE_PACKAGE}/data/snmp_exporter

  tar -zcvf "${RELEASE_PACKAGE}.tar.gz" $RELEASE_PACKAGE > /dev/null 2>&1
  rm -rf $RELEASE_PACKAGE
}

main(){
  start_time=$(date +%s)
  check_build_module_type "$@"
  echo ">>>>>>>>>>>>>>>>>>>  Release Ampcon packages start   <<<<<<<<<<<<<<<<<<<<<"
  common_operation
  tar_package
  echo ">>>>>>>>>>>>>>>>>>> Release Ampcon packages successfully   <<<<<<<<<<<<<<<<"

  end_time=$(date +%s)
  duration=$((end_time - start_time))
  hours=$((duration / 3600))
  minutes=$(( (duration % 3600) / 60))
  seconds=$((duration % 60))
  time_taken=$(printf "%02d:%02d:%02d" $hours $minutes $seconds)
  echo ">>>>>>>>>>>>>>>>>>> Congratulations! release took time $time_taken      <<<<<<<<<<<<<<<<"
}

main "$@"



