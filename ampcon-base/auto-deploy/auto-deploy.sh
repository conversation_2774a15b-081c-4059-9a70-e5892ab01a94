#!/bin/bash

PACKAGE_NAME="$1"

FILENAME="${PACKAGE_NAME%.*.*}"
TARGET_SERVER_INSTALL_PATH='/home/<USER>/automation/'


echo ">>>>>>>>>>>>>>>>>>>   start install docker. !!!         <<<<<<<<<<<<<<<<<<<<<<"
DOCKER_COMPOSE_VERSION=$(docker compose version | cut -d' ' -f4 | tr -d ',')
if [ -n "$DOCKER_COMPOSE_VERSION" ]; then
    echo "Docker Compose : $DOCKER_COMPOSE_VERSION, skip install docker"
else
  sudo apt-get remove docker docker-engine docker.io containerd runc
  sudo apt-get update
  sudo apt-get install ca-certificates curl gnupg lsb-release -y
  sudo mkdir -m 0755 -p /etc/apt/keyrings
  curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
  echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
  sudo chmod a+r /etc/apt/keyrings/docker.gpg
  sudo apt-get update
  sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin -y
fi



echo ">>>>>>>>>>>>>>>>>>>   start install ampcon. !!!         <<<<<<<<<<<<<<<<<<<<<<"

cd $TARGET_SERVER_INSTALL_PATH && tar -zxvf $PACKAGE_NAME

cd $TARGET_SERVER_INSTALL_PATH/$FILENAME && sudo ./install_or_upgrade.sh
