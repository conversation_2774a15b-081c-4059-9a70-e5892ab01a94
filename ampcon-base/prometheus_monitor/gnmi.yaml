global:
  update_target_interval: 30
  ampcon_address: "nginx-service:443"
  redis:
    addr: "redis-service:6379"
    password: ""
    db: 0

scrape:
  scrape_interval: 15
  target:
    # - target_name: '***********'
    #   address:  '***********:9339'
    #   username: 'admin'
    #   password: '12345678'
    # - target_name: '**********'
    #   address:  '**********:9339'
    #   username: 'admin'
    #   password: "pica8"
    - target_name: "*********"
      address: "*********:9339"
      username: "admin"
      password: "12345678"
      enable: 15
