# This is the main input for unit testing.
# Only this file is passed as command line argument.

rule_files:
    - alerts.yml

evaluation_interval: 1m

tests:
    # Test 1.
    - interval: 1m
      # Series data.
      input_series:
          - series: 'openconfig_interfaces:interfaces_interface_state{target="G1R626U000313"}'
            values: '0+0x10'
          - series: 'openconfig_lldp:lldp_interfaces_interface_state_counters_frame_discard{target="G1R626U000313"}'
            values: '0+1x7 0+1x3'  

      # Unit test for alerting rules.
      alert_rule_test:
          # Unit test 1.
          - eval_time: 10m
            alertname: interface_down
            exp_alerts:
                # Alert 1.
                - exp_labels:
                      severity: page
                  exp_annotations:
                      summary: "interface down"
                      description: "interface has been down for more than 5 minutes."
          - eval_time: 10m
            alertname: frame_discard_increase
            exp_alerts:
                # Alert 1.
                - exp_labels:
                      severity: page
                  exp_annotations:
                      summary: "frame_discard_increase"
                      description: "interface frame_discard has been increased for more than 5 minutes."
      # Unit tests for promql expressions.
      # promql_expr_test:
          # Unit test 1.
          # - expr: go_goroutines > 5
            # eval_time: 4m
            # exp_samples:
                # Sample 1.
                # - labels: 'go_goroutines{job="prometheus",instance="localhost:9090"}'
                  # value: 50
                # Sample 2.
                # - labels: 'go_goroutines{job="node_exporter",instance="localhost:9100"}'
                  # value: 50