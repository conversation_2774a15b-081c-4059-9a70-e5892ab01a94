package collector

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"gnmi_exporter/config"

	"github.com/redis/go-redis/v9"
)

const (
	RedisQueueKey = "queue:ddm_msg"
)

var (
	// Global Redis client instance
	globalRedisClient *RedisClient
	redisOnce        sync.Once
	redisError       error
)

// DDMStateMessage represents the formatted DDM state message
type DDMStateMessage struct {
	Interface        string                  `json:"interface"`
	SerialNo        string                  `json:"serialNo"`
	SupplyVoltage   float64                 `json:"supplyVoltage"`
	LaserTemperature float64                 `json:"laserTemperature"`
	ChannelsState   map[string]ChannelState `json:"channelsState"`
	WarnThreshold   DDMThreshold            `json:"warnThreshold"`
	AlarmThreshold  DDMThreshold            `json:"alarmThreshold"`
	HasThreshold    bool                    `json:"hasThreshold"`
	IsAbnormal      bool                    `json:"isAbnormal"`
	TransportType   string                  `json:"transportType"`
}

// RedisClient wraps the Redis client with custom functionality
type RedisClient struct {
	client *redis.Client
	ctx    context.Context
}

// InitRedisClient initializes the global Redis client instance
func InitRedisClient(cfg config.RedisConfig) error {
	redisOnce.Do(func() {
		var client *RedisClient
		client, redisError = NewRedisClient(cfg.Addr, cfg.Password, cfg.DB)
		if redisError == nil {
			globalRedisClient = client
			log.Printf("Successfully connected to Redis at %s", cfg.Addr)
		}
	})
	return redisError
}

// GetRedisClient returns the global Redis client instance
func GetRedisClient() *RedisClient {
	return globalRedisClient
}

// NewRedisClient creates a new Redis client instance
func NewRedisClient(addr, password string, db int) (*RedisClient, error) {
	ctx := context.Background()
	client := redis.NewClient(&redis.Options{
		Addr:         addr,
		Password:     password,
		DB:           db,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
	})

	// Test connection
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %v", err)
	}

	return &RedisClient{
		client: client,
		ctx:    ctx,
	}, nil
}

// Close closes the Redis connection
func (rc *RedisClient) Close() error {
	return rc.client.Close()
}

// formatDDMState formats the DDM state into the required structure
func (r *RedisClient) formatDDMState(interfaceName string, state DDMTransceiverState) map[string]interface{} {
	return map[string]interface{}{
		"interface":        interfaceName,
		"serialNo":        state.SerialNo,
		"supplyVoltage":   state.SupplyVoltage,
		"laserTemperature": state.LaserTemperature,
		"channelsState":   state.ChannelsState,
		"warnThreshold":   state.WarnThreshold,
		"alarmThreshold":  state.AlarmThreshold,
		"hasThreshold":    state.HasThreshold,
		"isAbnormal":      state.IsAbnormal,
		"transportType":   state.TransportType,
		"switch_sn":       state.TargetName,
	}
}

// PushDDMState pushes a DDM state message to Redis queue
func (rc *RedisClient) PushDDMState(interfaceName string, state DDMTransceiverState) error {
	// Format the message
	msg := rc.formatDDMState(interfaceName, state)

	// Convert to JSON
	jsonData, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("failed to marshal DDM state: %v", err)
	}

	// Push to Redis queue
	err = rc.client.RPush(rc.ctx, RedisQueueKey, jsonData).Err()
	if err != nil {
		return fmt.Errorf("failed to push to Redis queue: %v", err)
	}

	if log.Default() != nil {
		log.Printf("Successfully pushed DDM state for interface %s to Redis queue", interfaceName)
	}

	return nil
}

// BatchPushDDMState pushes multiple DDM state messages to Redis queue
func (rc *RedisClient) BatchPushDDMState(states map[string]DDMTransceiverState) error {
	if len(states) == 0 {
		return nil
	}

	pipe := rc.client.Pipeline()
	
	for interfaceName, state := range states {
		msg := rc.formatDDMState(interfaceName, state)
		jsonData, err := json.Marshal(msg)
		if err != nil {
			return fmt.Errorf("failed to marshal DDM state for interface %s: %v", interfaceName, err)
		}
		pipe.RPush(rc.ctx, RedisQueueKey, jsonData)
	}

	// Execute pipeline
	_, err := pipe.Exec(rc.ctx)
	if err != nil {
		return fmt.Errorf("failed to execute Redis pipeline: %v", err)
	}

	if log.Default() != nil {
		log.Printf("Successfully pushed %d DDM states to Redis queue", len(states))
	}

	return nil
} 