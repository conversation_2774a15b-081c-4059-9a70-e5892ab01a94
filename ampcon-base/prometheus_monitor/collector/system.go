package collector

import (
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
)

type SystemCollectorWorker struct {
	*CollectorWorker
}

func NewSystemCollectorWorker(base *CollectorWorker) BaseCollectorWorker {
	return &SystemCollectorWorker{
		CollectorWorker: base,
	}
}

func (w *SystemCollectorWorker) ResultToSamples(result map[string]BaseMetric, targetName string, ts time.Time) []prometheus.Metric {
	samples := []prometheus.Metric{}
	newBaseMetric := make(map[string]BaseMetric)
	for _, base := range result {
		// baseMetric := updateToBaseMetric(ts, update)
		if strings.Contains(base.Name, "memory") || strings.Contains(base.Name, "cpus") {
			// 过滤不需要的cpu参数
			if strings.Contains(base.Name, "cpus") &&
				(strings.Contains(base.Name, "hardware-interrupt") || strings.Contains(base.Name, "nice") ||
					strings.Contains(base.Name, "idle") || strings.Contains(base.Name, "wait") ||
					strings.Contains(base.Name, "user") || strings.Contains(base.Name, "kernel") || strings.Contains(base.Name, "software-interrupt")) {
				continue
			}
			metric := buildMetric(base.Name, base.Value, targetName, base.Label, base.Timestamp)
			samples = append(samples, metric)
		} else {
			// 对于非counters类型的指标，根据base路径和label进行合并，dir作为指标名称 name作为label
			w.ProcessGroupedMetric(&newBaseMetric, base, ts)
		}
	}

	samples = append(samples, w.BuildGroupedMetrics(newBaseMetric, targetName)...)

	return samples
}
