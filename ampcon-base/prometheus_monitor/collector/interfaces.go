package collector

import (
	"fmt"
	"gnmi_exporter/config"
	"log"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/openconfig/gnmi/proto/gnmi"
	"github.com/prometheus/client_golang/prometheus"
)

type OpenconfigInterfaces struct {
	adminStatus   map[string]string
	operStatus    map[string]string
	mtu           map[string]string
	loopbackMode  map[string]string
	duplexMode    map[string]string
	portSpeed     map[string]string
	ampconAddress string
	targetName    string
}

func NewOpenconfigInterfaces(targetName string) Hook {
	var ampconAddress string
	config, err := config.LoadFile([]string{"gnmi.yaml"})
	if err != nil {
		log.Fatalln("load config failed")
		ampconAddress = "nginx-service:443"
	} else {
		ampconAddress = config.GlobalConfig.AmpconAddress
	}

	return OpenconfigInterfaces{
		adminStatus:   map[string]string{},
		operStatus:    map[string]string{},
		mtu:           map[string]string{},
		loopbackMode:  map[string]string{},
		duplexMode:    map[string]string{},
		portSpeed:     map[string]string{},
		ampconAddress: ampconAddress,
		targetName:    targetName,
	}
}

func (i OpenconfigInterfaces) AfterSubscribe(result interface{}) {
	var alerts []Alert
	for _, update := range result.(*gnmi.Notification).Update {
		baseMetric := updateToBaseMetric(time.Now(), update)

		name := filepath.Base(baseMetric.Name)
		parentDir := filepath.Dir(baseMetric.Name)
		parent := filepath.Base(parentDir)
		if parent != "state" {
			continue
		}
		// fmt.Println(name, parentDir, parent, baseMetric.Name)
		switch name {
		case "admin-status":
			interfaceName := baseMetric.Label["interface_name"]
			isAlert, alert := i.updateStatus(i.adminStatus, interfaceName, name, baseMetric.Value)
			if isAlert {
				alerts = append(alerts, alert)
			}
		case "oper-status":
			interfaceName := baseMetric.Label["interface_name"]
			isAlert, alert := i.updateStatus(i.operStatus, interfaceName, name, baseMetric.Value)
			if isAlert {
				alerts = append(alerts, alert)
			}
		case "mtu":
			interfaceName := baseMetric.Label["interface_name"]
			isAlert, alert := i.updateStatus(i.mtu, interfaceName, name, baseMetric.Value)
			if isAlert {
				alerts = append(alerts, alert)
			}
		case "loopback-mode":
			interfaceName := baseMetric.Label["interface_name"]
			isAlert, alert := i.updateStatus(i.loopbackMode, interfaceName, name, baseMetric.Value)
			if isAlert {
				alerts = append(alerts, alert)
			}
		case "duplex-mode", "negotiated-duplex-mode":
			interfaceName := baseMetric.Label["interface_name"]
			isAlert, alert := i.updateStatus(i.duplexMode, interfaceName, "duplex-mode", baseMetric.Value)
			if isAlert {
				alerts = append(alerts, alert)
			}
		case "port-speed", "negotiated-port-speed":
			interfaceName := baseMetric.Label["interface_name"]
			var portSpeed string
			parts := strings.Split(baseMetric.Value, ":")
			if len(parts) == 2 {
				speedParts := strings.Split(parts[1], "_")
				if len(speedParts) > 1 {
					portSpeed = speedParts[1]
				} else {
					portSpeed = parts[1]
				}
			} else {
				portSpeed = baseMetric.Value
			}

			isAlert, alert := i.updateStatus(i.portSpeed, interfaceName, "port-speed", portSpeed)
			if isAlert {
				alerts = append(alerts, alert)
			}
		default:
		}
	}

	if len(alerts) > 0 {
		err := sendAlertLog("https://"+i.ampconAddress+"/ampcon/monitor/alert_log", alerts)
		if err != nil {
			log.Println("Error sending alert:", err)
		}
	}
}

func (i OpenconfigInterfaces) updateStatus(statusMap map[string]string, interfaceName, name, value string) (bool, Alert) {
	if oldStatus, exists := statusMap[interfaceName]; exists {
		if oldStatus != value {
			statusMap[interfaceName] = value
			severity := "warn"
			if name == "admin-status" || name == "mtu" || name == "loopback-mode" {
				severity = "info"
			}
			alert := Alert{
				Labels: map[string]string{
					"target":    i.targetName,
					"severity":  severity,
					"alertname": name,
				},
				Annotations: map[string]string{
					"description": fmt.Sprintf("Found interface: %s on switch %s %s change to %s", interfaceName, i.targetName, name, value),
					"summary":     name,
				},
			}
			log.Printf("found interface: %s on switch %s %s  %s change to %s, severity: %s", interfaceName, i.targetName, name, oldStatus, value, severity)
			return true, alert
		}
	} else {
		statusMap[interfaceName] = value
		// fmt.Printf("Added key '%s' '%s' to new value: '%s'.\n", interfaceName, name, value)
	}
	return false, Alert{}
}

type InterfaceCollectorWorker struct {
	*CollectorWorker
}

func NewInterfaceCollectorWorker(base *CollectorWorker) BaseCollectorWorker {
	return &InterfaceCollectorWorker{
		CollectorWorker: base,
	}
}

func (w *InterfaceCollectorWorker) ResultToSamples(result map[string]BaseMetric, targetName string, ts time.Time) []prometheus.Metric {
	samples := []prometheus.Metric{}
	newBaseMetric := make(map[string]BaseMetric)
	for _, base := range result {
		// baseMetric := updateToBaseMetric(ts, update)
		if strings.Contains(base.Name, "counters") || strings.Contains(base.Name, "rates") {
			metric := buildMetric(base.Name, base.Value, targetName, base.Label, base.Timestamp)
			samples = append(samples, metric)
		} else {
			// 对于非counters类型的指标，根据base路径和label进行合并，dir作为指标名称 name作为label
			w.ProcessGroupedMetric(&newBaseMetric, base, ts)

			// 由于告警需求增加特殊处理
			if (strings.Contains(base.Name, "port-speed") || strings.Contains(base.Name, "negotiated-port-speed")) && strings.Contains(base.Name, "state") {
				// fmt.Println(base.Name)
				baseName := strings.Replace(base.Name, "negotiated-port-speed", "port-speed", -1)

				speedInBps, err := valueToFloat(base.Value)
				if err != nil {
					log.Println(base.Name, base.Value, err)
					continue
				}
				newLabel := make(map[string]string)
				for key, value := range base.Label {
					newLabel[key] = value
				}

				speedStr := strconv.FormatFloat(speedInBps, 'f', -1, 64)
				metric := buildMetric(baseName, fmt.Sprintf("%v", speedStr), targetName, newLabel, base.Timestamp)
				samples = append(samples, metric)
			}
		}
	}

	samples = append(samples, w.BuildGroupedMetrics(newBaseMetric, targetName)...)
	return samples
}
