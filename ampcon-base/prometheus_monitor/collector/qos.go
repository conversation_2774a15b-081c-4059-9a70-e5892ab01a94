package collector

import (
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
)

type QosCollectorWorker struct {
	*CollectorWorker
}

func NewQosCollectorWorker(base *CollectorWorker) BaseCollectorWorker {
	return &QosCollectorWorker{
		CollectorWorker: base,
	}
}

func (w *QosCollectorWorker) ResultToSamples(result map[string]BaseMetric, targetName string, ts time.Time) []prometheus.Metric {
	samples := []prometheus.Metric{}
	newBaseMetric := make(map[string]BaseMetric)
	for _, base := range result {
		// baseMetric := updateToBaseMetric(ts, update)
		if strings.Contains(base.Name, "fsconfig-qos-ai-extensions") {
			metric := buildMetric(base.Name, base.Value, targetName, base.Label, base.Timestamp)
			samples = append(samples, metric)
		} else {
			// 对于非counters类型的指标，根据base路径和label进行合并，dir作为指标名称 name作为label
			w.ProcessGroupedMetric(&newBaseMetric, base, ts)
		}
	}

	samples = append(samples, w.BuildGroupedMetrics(newBaseMetric, targetName)...)

	return samples
}
