package collector

import (
	"encoding/json"
	"gnmi_exporter/config"
	"log"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
)

var (
	factories = map[string]string{
		"interfaces":        "openconfig-interfaces:interfaces",
		"lldp":              "openconfig-lldp:lldp",
		"components":        "openconfig-platform:components",
		"ai":                "openconfig-qos:qos",
		"system":            "openconfig-system:system",
		"network-instances": "openconfig-network-instance:network-instances",
		"dhcp-snooping":     "fsconfig-dhcp-snooping:dhcp-snooping",
		"mlag":              "fsconfig-mlag:mlag",
	}

	ConfigFile = "gnmi.yaml"
)

type CollectTarget struct {
	Name     string `json:"name"`
	Address  string `json:"address"`
	Username string `json:"username"`
	Password string `json:"password"`
	Enable   int    `json:"enable"`
}

type GNMICollector struct {
	collectors      map[string]Collector
	config          config.Config
	collectorTarget sync.Map
}

type Collector interface {
	// 用于获取所有子collector的指标
	Update(ch chan<- prometheus.Metric) error
	// 用于更新子collector的target
	UpdateTarget(targets []CollectTarget)
}

func NewGNMICollector(config config.Config) *GNMICollector {

	g := &GNMICollector{
		collectors:      make(map[string]Collector),
		config:          config,
		collectorTarget: sync.Map{},
	}

	g.FetchTarget()
	// g.FetchTargetByConfig()

	for key, path := range factories {
		value, ok := g.collectorTarget.Load(key)
		if ok {
			targets := value.([]CollectTarget)
			g.collectors[key] = NewBaseCollector(targets, path, g.config.ScrapeConfig.Interval)
		} else {
			g.collectors[key] = NewBaseCollector([]CollectTarget{}, path, g.config.ScrapeConfig.Interval)
		}
	}

	go g.startUpdateTargetLoop()

	return g
}

// 获取监控target
func (g *GNMICollector) FetchTarget() {

	var interfacesTargets []CollectTarget
	var lldpTargets []CollectTarget
	var componentsTargets []CollectTarget
	var aiTargets []CollectTarget
	var systemTargets []CollectTarget
	var networkInstancesTargets []CollectTarget
	var dhcpSnoopingInstancesTargets []CollectTarget
	var mlagInstancesTargets []CollectTarget

	apiResponse, err := callAPI("GET", "https://"+g.config.GlobalConfig.AmpconAddress+"/ampcon/monitor/get_montior_target", nil)
	if err != nil {
		log.Println("API call error:", err)
		return
	}
	log.Println("get montior target success")
	var resp APIResponse
	err = json.Unmarshal(apiResponse, &resp)
	if err != nil {
		log.Println("Error:", err)
		return
	}

	if resp.Status != 200 {
		log.Println(resp.Info)
		return
	}

	// fmt.Println("Data:", resp.Data)
	// fmt.Println("Status:", resp.Status)

	for _, target := range resp.Data {
		// 十进制转二进制  从右往左 0: interfaces 1: lldp
		interfaces := target.Enable&0b001 > 0
		lldp := target.Enable&0b010 > 0
		components := target.Enable&0b100 > 0
		ai := target.Enable&0b1000 > 0
		// log.Println(target.Name, "interfaces:", interfaces, "lldp:", lldp, "components:", components, "ai:", ai)
		// 解码target密码
		password, err := aesDecrypt(target.Password)
		if err != nil {
			log.Println("decode password error")
			continue
		} else {
			target.Password = password
		}

		if interfaces {
			interfacesTargets = append(interfacesTargets, target)
			systemTargets = append(systemTargets, target)
			networkInstancesTargets = append(networkInstancesTargets, target)
			dhcpSnoopingInstancesTargets = append(dhcpSnoopingInstancesTargets, target)
			mlagInstancesTargets = append(mlagInstancesTargets, target)
		}
		if lldp {
			lldpTargets = append(lldpTargets, target)
		}
		if components {
			componentsTargets = append(componentsTargets, target)
		}
		if ai {
			aiTargets = append(aiTargets, target)
		}
	}

	g.collectorTarget.Store("interfaces", interfacesTargets)
	g.collectorTarget.Store("lldp", lldpTargets)
	g.collectorTarget.Store("components", componentsTargets)
	g.collectorTarget.Store("ai", aiTargets)
	g.collectorTarget.Store("system", systemTargets)
	g.collectorTarget.Store("network-instances", networkInstancesTargets)
	g.collectorTarget.Store("dhcp-snooping", dhcpSnoopingInstancesTargets)
	g.collectorTarget.Store("mlag", mlagInstancesTargets)
}

func (g *GNMICollector) FetchTargetByConfig() {

	var interfacesTargets []CollectTarget
	var lldpTargets []CollectTarget
	var componentsTargets []CollectTarget
	var aiTargets []CollectTarget
	var systemTargets []CollectTarget
	var networkInstancesTargets []CollectTarget

	config, err := config.LoadFile([]string{ConfigFile})
	if err != nil {
		log.Println("fetch target failed")
		return
	}

	for _, target := range config.ScrapeConfig.Target {
		collectTarget := CollectTarget{
			Name:     target.TargetName,
			Address:  target.Address,
			Username: target.Username,
			Password: target.Password,
			Enable:   target.Enable,
		}
		// 十进制转二进制  从右往左 0: interfaces 1: lldp
		interfaces := collectTarget.Enable&0b001 > 0
		lldp := collectTarget.Enable&0b010 > 0
		components := collectTarget.Enable&0b100 > 0
		ai := target.Enable&0b1000 > 0
		log.Println(target.TargetName, "interfaces:", interfaces, "lldp:", lldp, "components:", components, "ai:", ai)
		if interfaces {
			interfacesTargets = append(interfacesTargets, collectTarget)
			systemTargets = append(systemTargets, collectTarget)
			networkInstancesTargets = append(networkInstancesTargets, collectTarget)
		}
		if lldp {
			lldpTargets = append(lldpTargets, collectTarget)
		}
		if components {
			componentsTargets = append(componentsTargets, collectTarget)
		}
		if ai {
			aiTargets = append(aiTargets, collectTarget)
		}
	}

	g.collectorTarget.Store("interfaces", interfacesTargets)
	g.collectorTarget.Store("lldp", lldpTargets)
	g.collectorTarget.Store("components", componentsTargets)
	g.collectorTarget.Store("ai", aiTargets)
	g.collectorTarget.Store("system", systemTargets)
	g.collectorTarget.Store("network-instances", networkInstancesTargets)
}

func (g *GNMICollector) startUpdateTargetLoop() {
	ticker := time.NewTicker(time.Duration(g.config.GlobalConfig.Interval) * time.Second)
	go func() {
		for range ticker.C {
			g.FetchTarget()
			// g.FetchTargetByConfig()
			for key, collector := range g.collectors {
				value, ok := g.collectorTarget.Load(key)
				if ok {
					targets := value.([]CollectTarget)
					collector.UpdateTarget(targets)
				}

			}
		}
	}()
}

func (n *GNMICollector) Describe(ch chan<- *prometheus.Desc) {
	ch <- prometheus.NewDesc("dummy", "dummy", nil, nil)
}

// Collect implements the prometheus.Collector interface.
func (n *GNMICollector) Collect(ch chan<- prometheus.Metric) {
	// log.Println("collect start")
	wg := sync.WaitGroup{}
	wg.Add(len(n.collectors))
	for name, c := range n.collectors {
		go func(name string, c Collector) {
			c.Update(ch)
			wg.Done()
		}(name, c)
	}
	wg.Wait()
	// log.Println("collect end")
}

type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}
