package main

import (
	"gnmi_exporter/collector"
	"gnmi_exporter/config"
	"log"
	"net/http"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"

	_ "net/http/pprof"
)

func main() {
	go http.ListenAndServe(":9999", nil)
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	cfg, err := config.LoadFile([]string{collector.ConfigFile})
	if err != nil {
		log.Fatalln("Failed to load config:", err)
	}

	// 初始化 Redis 客户端
	if err := collector.InitRedisClient(cfg.GlobalConfig.Redis); err != nil {
		log.Printf("Warning: Failed to initialize Redis client: %v", err)
	}

	gnmicollector := collector.NewGNMICollector(*cfg)
	prometheus.Register(gnmicollector)

	http.Handle("/metrics", promhttp.Handler())
	if err := http.ListenAndServe(":5000", nil); err != nil {
		log.Fatalln(err)
	}
}
