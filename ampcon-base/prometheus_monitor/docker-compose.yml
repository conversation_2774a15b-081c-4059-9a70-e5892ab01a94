 
networks:
  server_custom_net:  # TODO ampcon-t的network也需要改成外部的
    external: true
 
services:
  prometheus:
    image: prometheus:latest
    container_name: prometheus
    hostname: prometheus
    restart: always
    environment:
      TZ: "UTC"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml                      # prometheus监控配置文件
      - ./rules:/etc/prometheus/rules                                        # 告警规则配置文件
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--web.enable-lifecycle'                        # 用于配置文件热更新，调用curl -X POST http://localhost:9090/-/reload更新配置文件
      - '--storage.tsdb.retention.time=30d'             # 数据过期时间
      - '--query.lookback-delta=30s'                    # 查询追溯时长，最多追溯30s前的数据
    ports:
      - "9090:9090"
    networks:
      - server_custom_net

  alertmanager:
    image: alertmanager:latest
    container_name: alertmanager
    hostname: alertmanager
    restart: always
    environment:
      TZ: "UTC"
    ports:
      - 9093:9093
    networks:
      - server_custom_net
    volumes:  # 可以增加alertmanager配置告警发送规则，可发送至邮箱钉钉等
      - ./alertmanager.yml:/etc/alertmanager/alertmanager.yml
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'

  gnmi-exporter:
    image: gnmi-exporter:latest
    build:
      context: .
      dockerfile: ./Dockerfile
    container_name: gnmi-exporter
    privileged: true
    environment:
      TZ: "UTC"
    hostname: gnmi-exporter
    restart: always
    volumes:
      - ./gnmi.yaml:/app/gnmi.yaml:rw
    ports:
      - "5000:5000"
    command: ["sh", "-c", "ip route add ********/20 via $(getent hosts openvpn-service | awk '{ print $1 }') && ./gnmi_exporter"]
    networks:
      - server_custom_net
