debug: false
custom_thresholds:
  supplyVoltage:
    metric_type: "supplyVoltage"
    warn_upper: 2.1
    warn_lower: 1.9
    alarm_upper: 2.0
    alarm_lower: 1.8
  laserTemperature:
    metric_type: "laserTemperature"
    warn_upper: 10.0
    warn_lower: 0
    alarm_upper: 20
    alarm_lower: -10.0
  outputPower:
    metric_type: "outputPower"
    warn_upper: -1.0
    warn_lower: -9.0
    alarm_upper: 0.0
    alarm_lower: -12.0
  inputPower:
    metric_type: "inputPower"
    warn_upper: -1.0
    warn_lower: -17.0
    alarm_upper: 0.0
    alarm_lower: -20.0
  laserBiasCurrent:
    metric_type: "laserBiasCurrent"
    warn_upper: 50.0
    warn_lower: 0.0
    alarm_upper: 60.0
    alarm_lower: 0.0
