import React, {useEffect, useLayoutEffect, Suspense} from "react";
import ReactDOM from "react-dom/client";
import {QueryClientProvider, QueryClient} from "@tanstack/react-query";
import {AuthProvider} from "@/modules-smb/contexts/AuthProvider";
import {RouterProvider} from "react-router-dom";
import router from "@/router/route";
import {Provider, useDispatch} from "react-redux";
import store from "@/store/store";
import {ConfigProvider, App, Modal, Empty} from "antd";
import EmptyPic from "@/assets/images/App/empty.png";
import "normalize.css";
import "@/index.css";
import {Spinner} from "@chakra-ui/react";
import {HashRouter} from "react-router-dom";

import { ChakraProvider } from '@chakra-ui/react';
import theme from '@/modules-smb/theme/theme';
import '@/modules-smb/i18n';
import '@/modules-smb/index.css';
import 'rc-tree/assets/index.css';

import {ControllerSocketProvider} from "@/modules-smb/contexts/ControllerSocketProvider";
// import {FirmwareSocketProvider} from "@/modules-smb/contexts/FirmwareSocketProvider";
import {ProvisioningSocketProvider} from "@/modules-smb/contexts/ProvisioningSocketProvider";
import {SecuritySocketProvider} from "@/modules-smb/contexts/SecuritySocketProvider";
import { FavoritesProvider } from '@/modules-smb/contexts/FavoritesProvider';

const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            retry: 0,
            refetchOnWindowFocus: false
        }
    }
});

const root = ReactDOM.createRoot(document.getElementById("root"));

document.title = import.meta.env.VITE_APP_EXPORT_MODULE;
function useAppInitialization() {
    const dispatch = useDispatch();
    const [modal, contextHolder] = Modal.useModal();

    useEffect(() => {
        // if (["AmpCon-SUPER", "AmpCon-T"].includes(import.meta.env.VITE_APP_EXPORT_MODULE)) {
        //     import("@/modules-otn/utils/util").then(({setRootModal}) => {
        //         setRootModal(modal);
        //     });
        // }
    }, [modal]);

    useLayoutEffect(() => {
        // if (["AmpCon-SUPER", "AmpCon-T"].includes(import.meta.env.VITE_APP_EXPORT_MODULE)) {
        //     import("@/modules-otn/apis/api").then(({initAxios}) => {
        //         initAxios(dispatch);
        //     });
        // }
    }, [dispatch]);

    return contextHolder;
}

const AppMain = () => {
    const contextHolder = useAppInitialization();
    console.log("----localStorage storageToken----", localStorage.getItem("access_token"));
    console.log("----sessionStorage storageToken----", sessionStorage.getItem("access_token"));
    const storageToken = localStorage.getItem("access_token") ?? sessionStorage.getItem("access_token");
    console.log("----storageToken----", storageToken);
    return (
        <QueryClientProvider client={queryClient}>
            {/* <HashRouter> */}
            <Suspense fallback={<Spinner />}>
                <AuthProvider token={storageToken !== null ? storageToken : undefined}>
                <FavoritesProvider>
                    <SecuritySocketProvider>
                        {/* <FirmwareSocketProvider> */}
                            <ProvisioningSocketProvider>
                                <ControllerSocketProvider>
                                    <ChakraProvider theme={theme} resetCSS={false}>
                                        <App>
                                            <RouterProvider router={router} />
                                            {contextHolder}
                                        </App>
                                    </ChakraProvider>
                                </ControllerSocketProvider>
                            </ProvisioningSocketProvider>
                        {/* </FirmwareSocketProvider> */}
                    </SecuritySocketProvider>
                    </FavoritesProvider>
                </AuthProvider>
            </Suspense>
            {/* </HashRouter> */}
        </QueryClientProvider>
    );
};

const AppRender = () => {
    return (
        <Provider store={store}>
            <ConfigProvider
                theme={{
                    token: {
                        colorPrimary: "#14C9BB",
                        borderRadiusLG: "5px",
                        fontFamily: "Lato",
                        borderRadius: 5
                    },
                    components: {
                        Menu: {
                            itemColor: "#FFFFFF",
                            itemSelectedColor: "#FFFFFF"
                        },
                        Tabs: {
                            itemColor: "#929A9E",
                            borderRadius: 2
                        },
                        Button: {
                            defaultBorderColor: "#14C9BB",
                            colorText: "#14C9BB",
                            height: "60px",
                            borderRadius: 2
                        },
                        Input: {borderRadius: 2},
                        Select: {borderRadius: 2, colorTextDisabled: "#212519"},
                        InputNumber: {borderRadius: 2},
                        Radio: {borderRadius: 2},
                        DatePicker: {borderRadius: 2},
                        Table: {
                            rowSelectedBg: "#ffffff !important",
                            rowSelectedHoverBg: "#F8FAFB !important"
                        }
                    }
                }}
                renderEmpty={customizeRenderEmpty}
            >
                <AppMain />
            </ConfigProvider>
        </Provider>
    );
};

root.render(<AppRender />);

const customizeRenderEmpty = () => {
    return (
        <div className="custom-empty-container">
            <Empty image={EmptyPic} description="No Data" imageStyle={{marginTop: 16, marginBottom: 0}} />
        </div>
    );
};
