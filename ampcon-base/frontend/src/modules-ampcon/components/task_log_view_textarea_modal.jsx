import {Button, Divider, Flex, Input, Modal} from "antd";
import {forwardRef, useImperativeHandle, useState} from "react";
import {ReloadOutlined} from "@ant-design/icons";
import {fetchUpgradeTaskLog} from "@/modules-ampcon/apis/rma_api";

const TaskLogViewTextareaModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showTaskLogViewTextareaModal: ({sn, jobId}) => {
            setSelectedJobId(jobId);
            setSelectedSwitchSN(sn);
            fetchUpgradeTaskLog(jobId).then(response => {
                setLogContent(response.logContent);
            });
            setIsShowModal(true);
        },
        hideTaskLogViewTextareaModal: () => {
            resetModal();
        }
    }));

    const readonlyStyle = {
        minHeight: "330px",
        height: "58vh",
        resize: "vertical",
        border: "none",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        maxHeight: "calc(100vh - 500px)",
        backgroundColor: "#F8FAFB",
        paddingTop: "16px",
        paddingLeft: "16px"
    };

    const [isShowModal, setIsShowModal] = useState(false);
    const [selectedSwitchSN, setSelectedSwitchSN] = useState("");
    const [selectedJobId, setSelectedJobId] = useState(null);
    const [logContent, setLogContent] = useState("");

    const resetModal = () => {
        setIsShowModal(false);
        setSelectedJobId(null);
        setSelectedSwitchSN("");
        setLogContent("");
    };

    return (
        <Modal
            className="ampcon-middle-modal"
            title={
                <>
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                        {`${selectedSwitchSN} Upgrade Task Logs`}
                        <Button
                            type="text"
                            className="ant-modal-close"
                            style={{marginRight: "30px"}}
                            icon={<ReloadOutlined className="anticon anticon-close ant-modal-close-icon" />}
                            onClick={() => {
                                fetchUpgradeTaskLog(selectedJobId).then(response => {
                                    setLogContent(response.logContent);
                                });
                            }}
                        />
                    </div>
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </>
            }
            open={isShowModal}
            onCancel={() => {
                resetModal();
            }}
            footer={null}
        >
            <Flex vertical style={{flex: 1}}>
                <Input.TextArea style={readonlyStyle} value={logContent} rows={19} readOnly />
            </Flex>
        </Modal>
    );
});

export default TaskLogViewTextareaModal;
