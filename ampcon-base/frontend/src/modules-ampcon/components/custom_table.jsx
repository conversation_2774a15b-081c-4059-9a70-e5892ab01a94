import {
    Button,
    Divider,
    Flex,
    Form,
    Input,
    Modal,
    Select,
    Spin,
    Table,
    Tabs,
    Checkbox,
    Row,
    Col,
    Tag,
    DatePicker
} from "antd";
import SearchOutlined from "@ant-design/icons/SearchOutlined";
import { useTableInitialElement } from "@/modules-ampcon/hooks/useModalTable";
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState, message } from "react";
import { useForm } from "antd/es/form/Form";
import Icon from "@ant-design/icons";
import { searchSvg } from "@/utils/common/iconSvg";
import { useDispatch } from "react-redux";
import { updateAlarmSearch, updateAlarmSearchStatus } from "@/store/modules/common/alarm_slice";
import settingGreenSvg from "../pages/Topo/Topology/resource/site_green.svg?react";
import settingGreySvg from "../pages/Topo/Topology/resource/site_grey.svg?react";
import styles from "@/modules-ampcon/pages/Monitor/Alarm/alarm.module.scss";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import ColumnSelector from '@/modules-smb/Wireless/components/Button/ColumnSelectorProps';
import DoubleArrowSorter from '@/modules-smb/Wireless/components/Arrow/DoubleArrowSorter';
dayjs.extend(utc);
dayjs.extend(timezone);
const { RangePicker } = DatePicker;
const handleSearch = (selectedKeys, confirm) => {
    confirm();
};

const { Option } = Select;
// this is search style component.
export const TableSelectDropdown = ({
    setSelectedKeys,
    selectedKeys,
    confirm,
    clearFilters,
    defaultValue,
    searchCount
}) => {
    const dispatch = useDispatch();

    useEffect(() => {
        if (defaultValue) {
            setSelectedKeys([defaultValue]);
            handleSearch([defaultValue], confirm);
        } else {
            clearFilters();
            handleSearch(selectedKeys, confirm);
        }
    }, [searchCount + defaultValue]);

    const handleSearch = (keys, confirmFunc) => {
        confirmFunc();
        dispatch(updateAlarmSearch(keys[0]));
    };

    return (
        <div style={{ padding: 8 }}>
            <Select
                placeholder="Select Type"
                value={selectedKeys}
                onChange={value => {
                    setSelectedKeys(value ? [value] : []);
                }}
                allowClear
                style={{ width: 188, marginBottom: 8, display: "block" }}
            >
                <Option value="error">
                    <Tag className={styles.errorStyle}>Error</Tag>
                </Option>
                <Option value="warn">
                    <Tag className={styles.warnStyle}>Warning</Tag>
                </Option>
                <Option value="info">
                    <Tag className={styles.infoStyle}>Info</Tag>
                </Option>
            </Select>
            <Button
                type="primary"
                onClick={() => {
                    dispatch(updateAlarmSearchStatus(false));
                    handleSearch(selectedKeys, confirm);
                }}
                icon={<SearchOutlined />}
                size="small"
                style={{ width: 90, marginRight: 8 }}
            >
                Search
            </Button>
            <Button
                onClick={() => {
                    clearFilters();
                    dispatch(updateAlarmSearch(""));
                    handleSearch("", confirm);
                }}
                size="small"
                style={{ width: 90 }}
            >
                Reset
            </Button>
        </div>
    );
};

export const TableTimeFilterDropdown = ({
    setSelectedKeys,
    selectedKeys,
    confirm,
    clearFilters,
    defaultValue,
    searchCount
}) => {
    const [value, setValue] = useState([]);

    useEffect(() => {
        if (defaultValue && defaultValue.length === 2) {
            const [start, end] = defaultValue;
            const formatted = [dayjs(start), dayjs(end)];
            setSelectedKeys([formatted]);
            setValue(formatted);
            handleSearch([formatted], confirm);
        } else {
            clearFilters();
            handleSearch(selectedKeys, confirm);
        }
    }, [searchCount, defaultValue?.[0], defaultValue?.[1]]);

    const handleSearch = (keys, confirmFunc) => {
        const range = keys[0];
        if (!range || range.length !== 2) return;

        const start = dayjs(range[0]);
        const end = dayjs(range[1]);

        if (!start.isValid() || !end.isValid()) return;
        if (start.isAfter(end)) {
            message.error("End time must be after start time.");
            return;
        }
        if (end.diff(start, "hour", true) > 12) {
            message.error("Time range cannot exceed 12 hours.");
            return;
        }
        const startTime = dayjs(start).tz("Asia/Shanghai").format("YYYY-MM-DD HH:mm:ss");
        const endTime = dayjs(end).tz("Asia/Shanghai").format("YYYY-MM-DD HH:mm:ss");

        setSelectedKeys([[startTime, endTime]]);
        confirmFunc();
    };

    const handleReset = confirmFunc => {
        setSelectedKeys([]);
        setValue([]);
        clearFilters();
        confirmFunc();
    };

    return (
        <div style={{ padding: 8, width: 210 }}>
            <RangePicker
                value={value}
                onChange={val => {
                    setSelectedKeys(val ? [val] : []);
                    setValue(val || []);
                }}
                disabledDate={current => current && current > dayjs()}
                disabledTime={current => {
                    if (!current || !current.isSame(dayjs(), "day")) return {};

                    const now = dayjs();
                    const genDisabled = (total, currentVal) =>
                        Array.from({ length: total }, (_, i) => i).filter(i => i > currentVal);

                    return {
                        disabledHours: () => genDisabled(24, now.hour()),
                        disabledMinutes: hour => (hour === now.hour() ? genDisabled(60, now.minute()) : []),
                        disabledSeconds: (hour, minute) =>
                            hour === now.hour() && minute === now.minute() ? genDisabled(60, now.second()) : []
                    };
                }}
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                placeholder={["Start Time", "End Time"]}
                style={{ width: "100%", marginBottom: 8 }}
            />
            <div style={{ display: "flex", justifyContent: "space-between" }}>
                <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    size="small"
                    style={{ width: "48%" }}
                    onClick={() => handleSearch(selectedKeys, confirm)}
                >
                    Search
                </Button>
                <Button size="small" style={{ width: "48%" }} onClick={() => handleReset(confirm)}>
                    Reset
                </Button>
            </div>
        </div>
    );
};

// this is search style component.
export const TableFilterDropdown = ({
    setSelectedKeys,
    selectedKeys,
    confirm,
    clearFilters,
    defaultValue,
    searchCount
}) => {
    const dispatch = useDispatch();
    useEffect(() => {
        if (defaultValue) {
            setSelectedKeys([defaultValue]);
            handleSearch([defaultValue], confirm);
        } else {
            clearFilters();
            handleSearch(selectedKeys, confirm);
        }
    }, [searchCount + defaultValue]);
    return (
        <div style={{ padding: 8 }}>
            <Input
                placeholder="Search name"
                value={selectedKeys[0]}
                onChange={e => {
                    dispatch(updateAlarmSearchStatus(false));
                    setSelectedKeys(e.target.value ? [e.target.value] : []);
                }}
                onPressEnter={() => {
                    dispatch(updateAlarmSearchStatus(false));
                    handleSearch(selectedKeys, confirm);
                }}
                style={{ width: 188, marginBottom: 8, display: "block" }}
            />
            <Button
                type="primary"
                onClick={() => {
                    dispatch(updateAlarmSearchStatus(false));
                    handleSearch(selectedKeys, confirm);
                }}
                icon={<SearchOutlined />}
                size="small"
                style={{ width: 90, marginRight: 8 }}
            >
                Search
            </Button>
            <Button
                onClick={() => {
                    clearFilters();
                    dispatch(updateAlarmSearch(""));
                    handleSearch(selectedKeys, confirm);
                }}
                size="small"
                style={{ width: 90 }}
            >
                Reset
            </Button>
        </div>
    );
};

export const TableSelectFilterDropdown = ({
    setSelectedKeys,
    selectedKeys,
    confirm,
    clearFilters,
    filterOptions,
    filterDefaultValue
}) => {
    useEffect(() => {
        if (filterDefaultValue) {
            setSelectedKeys([filterDefaultValue]);
            handleSearch([filterDefaultValue], confirm);
        } else {
            clearFilters();
            handleSearch(selectedKeys, confirm);
        }
    }, [filterDefaultValue]);
    return (
        <div style={{ padding: 8 }}>
            <Select
                showSearch
                size="small"
                style={{ width: 130, marginRight: 8 }}
                onChange={value => {
                    setSelectedKeys(value ? [value] : []);
                    handleSearch(selectedKeys, confirm);
                }}
                // getPopupContainer={trigger => trigger.parentNode}
                // options={[
                //     {
                //         label: "Manager",
                //         options: [
                //             {
                //                 label: "Jack",
                //                 value: "jack"
                //             },
                //             {
                //                 label: "tom3",
                //                 value: "tom3"
                //             }
                //         ]
                //     },
                //     {
                //         label: "Engineer",
                //         options: [
                //             {
                //                 label: "tom6",
                //                 value: "tom6"
                //             }
                //         ]
                //     }
                // ]}
                options={filterOptions}
                value={selectedKeys[0]}
            />
            <Button
                onClick={() => {
                    clearFilters();
                    handleSearch(selectedKeys, confirm);
                }}
                type="primary"
                size="small"
                style={{ width: 90 }}
            >
                Reset
            </Button>
        </div>
    );
};

// add other style component.

export const createMatchMode = fields => {
    const matchModes = {};
    fields.forEach(field => {
        matchModes[field.name] = field.matchMode;
    });
    return matchModes;
};

export const createFilterFields = (filters, matchModes) => {
    return Object.keys(filters).map(field => {
        const matchMode = matchModes[field] || "exact";
        const fieldFilters = filters[field] || [];
        return {
            field,
            filters: fieldFilters.map(value => ({ value, matchMode }))
        };
    });
};
// table change component

export const handleTableChange = async (
    pagination,
    filters,
    sorter,
    setPagination,
    searchFields,
    fetchDataAPI,
    fetchAPIParams,
    setData,
    matchModes,
    setLoading,
    tableSelectedRowKey = null,
    tableSelectedRows = null,
    setTableSelectedRowKey = null,
    setTableSelectedRows = null,
    tableRemovedRowKey = null
) => {
    try {
        setLoading(true);
        const filterFields = createFilterFields(filters, matchModes);
        const sortFields = [];
        if (sorter.field && sorter.order) {
            sortFields.push({
                field: sorter.field,
                order: sorter.order === "ascend" ? "asc" : "desc"
            });
        }

        const response = await fetchDataAPI(
            ...(fetchAPIParams
                ? [...fetchAPIParams, pagination.current, pagination.pageSize, filterFields, sortFields, searchFields]
                : [pagination.current, pagination.pageSize, filterFields, sortFields, searchFields])
        );

        setData(response.data);
        if (tableSelectedRowKey !== null && tableSelectedRows !== null) {
            response.data.forEach(item => {
                if (tableRemovedRowKey.indexOf(item.id) === -1 && tableSelectedRowKey.indexOf(item.id) > -1) {
                    item.selected = true;
                }
            });
            if (response.data.every(item => "selected" in item)) {
                const backendSelectedRowKeys = response.data.filter(item => item.selected).map(item => item.id);
                const frontendSelectedRowKeys = response.data
                    .filter(item => tableSelectedRowKey.indexOf(item.id) > -1)
                    .map(item => item.id);
                const removedRowKeys = response.data
                    .filter(item => tableRemovedRowKey.indexOf(item.id) > -1)
                    .map(item => item.id);
                const selectedRowKeys = Array.from(
                    new Set([...tableSelectedRowKey, ...backendSelectedRowKeys, ...frontendSelectedRowKeys])
                ).filter(itemId => {
                    return removedRowKeys.indexOf(itemId) === -1;
                });
                setTableSelectedRowKey(selectedRowKeys);

                const keysFlag = {};
                const selectedRows = [...response.data, ...(tableSelectedRows || [])].reduce((acc, cur) => {
                    if (!keysFlag[cur.id] && selectedRowKeys.includes(cur.id)) {
                        keysFlag[cur.id] = true;
                        acc.push({
                            ...cur,
                            selected: true
                        });
                    }
                    return acc;
                }, []);

                setTableSelectedRows(selectedRows);
            }
        }
        setPagination(prev => ({
            ...prev,
            total: response.total,
            current: response.page,
            pageSize: response.pageSize
        }));
    } catch (error) {
        // error
    } finally {
        setLoading(false);
    }
};

export const createColumnWithoutFilter = (title, dataIndex, sorter = true) => ({
    title,
    dataIndex,
    onFilter: () => true,
    sorter
});

export const createColumnConfig = (
    title,
    dataIndex,
    filterDropdownComponent,
    defaultValue = "",
    width = null,
    defaultSortOrder = null
) =>
    width === null
        ? {
            title,
            dataIndex,
            filterDropdown: filterDropdownComponent
                ? props => filterDropdownComponent({ ...props, defaultValue })
                : null,
            onFilter: () => true,
            sorter: true,
            defaultSortOrder: defaultSortOrder || null
        }
        : {
            title,
            dataIndex,
            filterDropdown: filterDropdownComponent
                ? props => filterDropdownComponent({ ...props, defaultValue })
                : null,
            onFilter: () => true,
            sorter: true,
            width,
            defaultSortOrder: defaultSortOrder || null
        };

export const createColumnConfigMultipleParams = ({
    title,
    dataIndex,
    enableFilter = true,
    enableSorter = true,
    filterDropdownComponent,
    defaultSortOrder = null,
    ...otherProps
}) =>
    enableFilter
        ? {
            title,
            dataIndex,
            filterDropdown: props => filterDropdownComponent({ ...props, defaultValue: otherProps.defaultValue || "" }),
            onFilter: () => enableFilter,
            sorter: enableSorter,
            defaultSortOrder: defaultSortOrder || null,
            ...otherProps
        }
        : {
            title,
            dataIndex,
            onFilter: () => enableFilter,
            sorter: enableSorter,
            defaultSortOrder: defaultSortOrder || null,
            ...otherProps
        };

export const GlobalSearchInput = ({ onChange }) => (
    <Input
        placeholder="Search"
        prefix={<Icon component={searchSvg} />}
        allowClear
        onChange={onChange}
        style={{ width: 280, height: "32px", float: "right", borderRadius: "2px" }}
    />
);

export const AmpConCustomTable = forwardRef(
    (
        {
            preSavedSelectedRowKeys = false,
            quantity,
            columns,
            rowSelection,
            matchFieldsList,
            searchFieldsList,
            extraButton,
            helpDraw,
            fetchAPIInfo,
            fetchAPIParams,
            isShowPagination,
            disableInternalRowSelection,
            ...props
        },
        ref
    ) => {
        // eslint-disable-next-line no-unused-vars
        const [_, __, searchFields, setSearchFields, data, setData, loading, setLoading, pagination, setPagination] =
            useTableInitialElement(searchFieldsList, false);

        const matchModes = createMatchMode(matchFieldsList || []);

        const [tableSelectedRowKey, setTableSelectedRowKey] = useState(
            rowSelection ? rowSelection.selectedRowKeys : []
        );
        const [tableSelectedRows, setTableSelectedRows] = useState(rowSelection ? rowSelection.selectedRows : []);
        const [tableRemovedRowKey, setTableRemovedRowKey] = useState([]);
        const [tableRemovedRows, setTableRemovedRows] = useState([]);
        const [operations, setOperations] = useState({});
        const [operationRowsMappings, setOperationRowsMappings] = useState({});
        const dispatch = useDispatch();

        const [sorter, setSorter] = useState({});
        const checkSortedColumn = columns => {
            for (const columnKey in columns) {
                if (Object.prototype.hasOwnProperty.call(columns, columnKey)) {
                    const columnConfig = columns[columnKey];
                    // check each column has defaultSortOrder or not
                    if (columnConfig.defaultSortOrder) {
                        return [columnConfig.dataIndex, columnConfig.defaultSortOrder];
                    }
                }
            }
            return [undefined, undefined];
        };
        const [filters, setFilters] = useState({});

        const handleSelect = (record, selected) => {
            const keys = selected
                ? tableSelectedRowKey.concat([record.id])
                : tableSelectedRowKey.filter(item => item !== record.id);

            if (!selected) {
                setTableRemovedRowKey([...tableRemovedRowKey, record.id]);
                setTableRemovedRows([...tableRemovedRows, record]);
            } else {
                setTableRemovedRowKey(tableRemovedRowKey.filter(item => item !== record.id));
                setTableRemovedRows(tableRemovedRows.filter(item => item.id !== record.id));
            }

            const rows = selected
                ? [...tableSelectedRows, record]
                : tableSelectedRows.filter(item => item.id !== record.id);

            if (Object.prototype.hasOwnProperty.call(record, "selected")) {
                const operationsTemp = operations;
                if (record.selected === false && selected) {
                    operationsTemp[record.id] = "add";
                } else if (record.selected === false && !selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && !selected) {
                    operationsTemp[record.id] = "remove";
                }
                setOperations(operationsTemp);
                setOperationRowsMappings(prev => {
                    const newMappings = {...prev};
                    newMappings[record.id] = record;
                    return newMappings;
                });
            }

            rows.forEach(row => {
                if (row.children) {
                    const isInChildren = row.children.some(child => child.id === record.id);
                    if (isInChildren) {
                        const rowIndex = rows.findIndex(r => r.id === row.id);
                        if (rowIndex > -1) {
                            rows.splice(rowIndex, 1);
                        }

                        const keyIndex = keys.findIndex(k => k === row.id);
                        if (keyIndex > -1) {
                            keys.splice(keyIndex, 1);
                        }
                    }
                }
            });

            if (Object.prototype.hasOwnProperty.call(record, "children")) {
                if (selected) {
                    record.children.forEach(child => {
                        if (!keys.includes(child.id)) {
                            keys.push(child.id);
                        }
                        if (!rows.some(row => row.id === child.id)) {
                            rows.push(child);
                        }
                    });
                } else {
                    const needRemoveRows = record.children.map(child => child.id);
                    needRemoveRows.forEach(id => {
                        const rowIndex = rows.findIndex(r => r.id === id);
                        rows.splice(rowIndex, 1);
                        const keyIndex = keys.findIndex(k => k === id);
                        keys.splice(keyIndex, 1);
                    });
                }
            }
            setTableSelectedRowKey(keys);
            setTableSelectedRows(rows);

            if (rowSelection && rowSelection.onChange) {
                rowSelection.onChange(keys, rows);
            }
        };

        const handleSelectAll = (selected, selectedRows, changeRows) => {
            if (quantity) {
                const currentCount = tableSelectedRowKey.length;
                const remaining = quantity - currentCount;

                if (selected && remaining <= 0) {
                    return; // 已满，不允许再选
                }

                const limitedRows = selected ? changeRows.slice(0, remaining) : changeRows;
                const limitedIds = limitedRows.map(item => item.id);

                const keys = selected
                    ? Array.from(new Set([...tableSelectedRowKey, ...limitedIds]))
                    : tableSelectedRowKey.filter(item => !limitedIds.includes(item));
                setTableSelectedRowKey(keys);

                const rows = selected
                    ? Array.from(new Set([...tableSelectedRows, ...limitedRows]))
                    : tableSelectedRows.filter(item => !limitedIds.includes(item.id));
                setTableSelectedRows(rows);

                if (!selected) {
                    setTableRemovedRowKey([...tableRemovedRowKey, ...limitedIds]);
                    setTableRemovedRows([...tableRemovedRows, ...limitedRows]);
                } else {
                    setTableRemovedRowKey(tableRemovedRowKey.filter(item => !limitedIds.includes(item)));
                    setTableRemovedRows(tableRemovedRows.filter(item => !limitedIds.includes(item.id)));
                }

                if (Object.prototype.hasOwnProperty.call(changeRows[0], "selected")) {
                    const operationsTemp = { ...operations };
                    limitedRows.forEach(record => {
                        if (record.selected === false && selected) {
                            operationsTemp[record.id] = "add";
                        } else if (record.selected === false && !selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && !selected) {
                            operationsTemp[record.id] = "remove";
                        }
                    });
                    setOperations(operationsTemp);
                }

                if (rowSelection && rowSelection.onChange) {
                    rowSelection.onChange(keys, rows);
                }
            } else {
                const ids = changeRows.map(item => item.id);
                const keys = selected
                    ? tableSelectedRowKey.concat(ids)
                    : tableSelectedRowKey.filter(item => !ids.includes(item));
                setTableSelectedRowKey(keys);

                if (!selected) {
                    setTableRemovedRowKey([...tableRemovedRowKey, ...ids]);
                    setTableRemovedRows([...tableRemovedRows, ...changeRows]);
                } else {
                    setTableRemovedRowKey(tableRemovedRowKey.filter(item => !ids.includes(item)));
                    setTableRemovedRows(tableRemovedRows.filter(item => !ids.includes(item.id)));
                }
                const rows = selected
                    ? [...tableSelectedRows, ...changeRows]
                    : tableSelectedRows.filter(item => !ids.includes(item.id));
                setTableSelectedRows(rows);

                // for default selected rows
                if (Object.prototype.hasOwnProperty.call(changeRows[0], "selected")) {
                    changeRows.map(record => {
                        const operationsTemp = operations;
                        if (record.selected === false && selected) {
                            operationsTemp[record.id] = "add";
                        } else if (record.selected === false && !selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && !selected) {
                            operationsTemp[record.id] = "remove";
                        }
                        setOperations(operationsTemp);
                        setOperationRowsMappings(prev => {
                            const newMappings = {...prev};
                            newMappings[record.id] = record;
                            return newMappings;
                        });
                    });
                }

                if (rowSelection && rowSelection.onChange) {
                    rowSelection.onChange(keys, rows);
                }
            }
        };

        const tableRowSelection = useMemo(() => {
            if (rowSelection?.type === "radio") {
                return rowSelection;
            }

            if (disableInternalRowSelection && rowSelection) {
                return rowSelection;
            }

            return {
                selectedRowKeys: tableSelectedRowKey,
                onSelect: handleSelect,
                onSelectAll: handleSelectAll,
                getCheckboxProps: rowSelection?.getCheckboxProps,
                fixed: rowSelection?.fixed,
                checkStrictly: rowSelection?.checkStrictly
            };
        }, [
            disableInternalRowSelection,
            rowSelection?.type,
            rowSelection?.getCheckboxProps,
            rowSelection?.fixed,
            rowSelection?.checkStrictly,
            tableSelectedRowKey,
            handleSelect,
            handleSelectAll
        ]);

        useImperativeHandle(ref, () => ({
            refreshTable() {
                fetchData().then();
            },
            setTableLoading(value) {
                setLoading(value);
            },
            getSelectedRow: () => {
                return { tableSelectedRowKey, tableSelectedRows };
            },
            getRemovedRow: () => {
                return { tableRemovedRowKey, tableRemovedRows };
            },
            clearSelectedRow: () => {
                setTableSelectedRowKey([]);
                setTableSelectedRows([]);
            },
            getOperations: () => {
                return operations;
            },
            getOperationRowsMappings: () => {
                return operationRowsMappings;
            },
            getTableData: () => {
                return data;
            },
            clearAndRefresh: () => {
                setTableSelectedRowKey([]);
                setTableSelectedRows([]);
                fetchData(true);
            },
            refreshAndSaveSelectedRow: () => {
                fetchData(true);
            }
        }));

        const fetchData = async (ignoreSelection = false) => {
            setLoading(true);
            const filterFields = filters ? createFilterFields(filters, matchModes) : [];
            const sortFields = [];
            if (sorter.field && sorter.order) {
                sortFields.push({
                    field: sorter.field,
                    order: sorter.order === "ascend" ? "asc" : "desc"
                });
            }

            try {
                let response = await fetchAPIInfo(
                    ...(fetchAPIParams
                        ? [
                            ...fetchAPIParams,
                            pagination.current,
                            pagination.pageSize,
                            filterFields,
                            sortFields,
                            searchFields
                        ]
                        : [pagination.current, pagination.pageSize, filterFields, sortFields, searchFields])
                );
                if (response.data.length === 0 && response.total !== 0) {
                    response = await fetchAPIInfo(
                        ...(fetchAPIParams
                            ? [
                                ...fetchAPIParams,
                                Math.ceil(response.total / response.pageSize),
                                pagination.pageSize,
                                [],
                                [],
                                searchFields
                            ]
                            : [
                                Math.ceil(response.total / response.pageSize),
                                pagination.pageSize,
                                [],
                                [],
                                searchFields
                            ])
                    );
                }

                const responseDataTemp = JSON.parse(JSON.stringify(response.data));
                if (!ignoreSelection) {
                    responseDataTemp.forEach(item => {
                        item.selected = operations[item.id] === "add" ? true : item.selected;
                    });

                    if (responseDataTemp.every(item => "selected" in item)) {
                        const backendSelectedRowKeys = responseDataTemp
                            .filter(item => item.selected)
                            .map(item => item.id);
                        const frontendSelectedRowKeys = tableSelectedRowKey
                            ? responseDataTemp
                                .filter(item => tableSelectedRowKey.indexOf(item.id) > -1)
                                .map(item => item.id)
                            : [];
                        const removedRowKeys = tableRemovedRowKey
                            ? responseDataTemp
                                .filter(item => tableRemovedRowKey.indexOf(item.id) > -1)
                                .map(item => item.id)
                            : [];
                        const selectedRowKeys = Array.from(
                            new Set([
                                ...(tableSelectedRowKey || []),
                                ...backendSelectedRowKeys,
                                ...frontendSelectedRowKeys
                            ])
                        ).filter(itemId => removedRowKeys.indexOf(itemId) === -1);
                        setTableSelectedRowKey(selectedRowKeys);
                        setTableSelectedRows(responseDataTemp.filter(item => item.selected));
                    }
                }

                setData(responseDataTemp);
                setPagination(prev => ({
                    ...prev,
                    total: response.total,
                    current: response.page,
                    pageSize: response.pageSize
                }));
            } catch (error) {
                console.log("error", error);
            } finally {
                setLoading(false);
            }
        };

        useEffect(() => {
            const [sortedColumn, sortedOrder] = checkSortedColumn(columns);
            if (sortedColumn) {
                sorter.field = sortedColumn;
                sorter.order = sortedOrder;
                // tableChange("", "", sorter);
            }
        }, []);

        useEffect(() => {
            // if (!props.readTag)
            fetchData().then();
        }, [JSON.stringify(fetchAPIParams), JSON.stringify(searchFields)]);

        const handleSearchChange = e => {
            dispatch(updateAlarmSearchStatus(false));
            dispatch(updateAlarmSearch(""));
            if (!preSavedSelectedRowKeys) {
                if (tableRowSelection && tableRowSelection.selectedRowKeys) {
                    tableRowSelection.selectedRowKeys = [];
                }
                setTableSelectedRows([]);
                setTableSelectedRowKey([]);
            }

            setSearchFields({
                fields: searchFieldsList,
                value: e.target.value
            });
        };

        const tableChange = async (pagination, filters, sorter) => {
            const delay = ms =>
                new Promise(resolve => {
                    setTimeout(resolve, ms);
                });
            await delay(100);
            setSorter(sorter);
            setFilters(filters);
            await handleTableChange(
                pagination,
                filters,
                sorter,
                setPagination,
                searchFields,
                fetchAPIInfo,
                fetchAPIParams,
                setData,
                matchModes,
                setLoading,
                tableSelectedRowKey,
                tableSelectedRows,
                setTableSelectedRowKey,
                setTableSelectedRows,
                tableRemovedRowKey
            );
        };

        return (
            <div>
                <Flex vertical>
                    <Flex gap="middle" style={{ marginBottom: "20px" }}>
                        {extraButton}
                        <div style={{ flexGrow: 1 }} />
                        {searchFieldsList ? <GlobalSearchInput onChange={handleSearchChange} /> : null}
                        {helpDraw}
                    </Flex>
                    <Table
                        rowSelection={rowSelection ? tableRowSelection : null}
                        columns={columns}
                        bordered
                        rowKey={record => record.id}
                        loading={loading}
                        dataSource={data}
                        pagination={searchFieldsList || isShowPagination ? pagination : false}
                        onChange={tableChange}
                        {...props}
                    />
                </Flex>
            </div>
        );
    }
);

export const AmpConCustomModalTable = forwardRef(
    (
        {
            title,
            selectModalOpen,
            onCancel,
            columns,
            matchFieldsList,
            searchFieldsList,
            buttonProps,
            fetchAPIInfo,
            fetchAPIParams,
            rowSelection,
            footer,
            modalClass = ""
        },
        ref
    ) => {
        const tableRef = useRef(null);
        useImperativeHandle(ref, () => ({
            getTableRef() {
                return tableRef;
            }
        }));

        return (
            <Modal
                className={modalClass || ""}
                title={
                    typeof title === "string" ? (
                        <div>
                            {title}
                            <Divider style={{ marginTop: 8, marginBottom: 0 }} />
                        </div>
                    ) : (
                        title
                    )
                }
                open={selectModalOpen}
                onCancel={onCancel}
                destroyOnClose
                footer={footer || null}
            >
                <AmpConCustomTable
                    extraButton={buttonProps}
                    columns={columns}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    fetchAPIInfo={fetchAPIInfo}
                    fetchAPIParams={fetchAPIParams}
                    rowSelection={rowSelection}
                    ref={tableRef}
                />
            </Modal>
        );
    }
);

export const WirelessCustomTable = forwardRef(
    (
        {
            quantity,
            columns,
            rowSelection,
            matchFieldsList,
            searchFieldsList,
            extraButton,
            helpDraw,
            fetchAPIInfo,
            fetchAPIParams,
            isShowPagination,
            disableInternalRowSelection,
            showColumnSelector = false,
            ...props
        },
        ref
    ) => {
        const [visibleColumns, setVisibleColumns] = useState(columns.map(col => col.key));
        const filteredColumns = useMemo(() => {
            return columns.filter(col => visibleColumns.includes(col.key));
        }, [columns, visibleColumns]);

        const handleVisibleColumnsChange = (newVisibleColumns) => {
            const fixedKeys = columns.filter(col => col.columnsFix).map(col => col.key);
            const merged = Array.from(new Set([...newVisibleColumns, ...fixedKeys]));
            setVisibleColumns(merged);
        };
      const finalColumns = useMemo(() => {
  let cols = showColumnSelector
    ? [
        ...columns.filter(col => visibleColumns.includes(col.key)),
        {
          title: (
            <div>
              <ColumnSelector
                columns={columns}
                visibleColumns={visibleColumns}
                onChange={handleVisibleColumnsChange}
              
              />
            </div>
          ),
          key: 'columnSelector',
          width: '32px',
          fixed: 'right',
          // 表头单元格样式 - 关键优化
          onHeaderCell: () => ({
            style: {
              width: '32px',
              padding: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              
            }
          }),
      
        }
      ]
    : columns.filter(col => visibleColumns.includes(col.key));
  return cols;
}, [columns, showColumnSelector, visibleColumns, handleVisibleColumnsChange]);


        const [_, __, searchFields, setSearchFields, data, setData, loading, setLoading, pagination, setPagination] =
            useTableInitialElement(searchFieldsList, false);

        const matchModes = createMatchMode(matchFieldsList || []);

        const [tableSelectedRowKey, setTableSelectedRowKey] = useState(
            rowSelection ? rowSelection.selectedRowKeys : []
        );
        const [tableSelectedRows, setTableSelectedRows] = useState(rowSelection ? rowSelection.selectedRows : []);
        const [tableRemovedRowKey, setTableRemovedRowKey] = useState([]);
        const [tableRemovedRows, setTableRemovedRows] = useState([]);
        const [operations, setOperations] = useState({});
        const dispatch = useDispatch();

        const [sorter, setSorter] = useState({});
        const checkSortedColumn = columns => {
            for (const columnKey in columns) {
                if (Object.prototype.hasOwnProperty.call(columns, columnKey)) {
                    const columnConfig = columns[columnKey];
                    // check each column has defaultSortOrder or not
                    if (columnConfig.defaultSortOrder !== null) {
                        return [columnConfig.dataIndex, columnConfig.defaultSortOrder];
                    }
                }
            }
            return [undefined, undefined];
        };
        const [filters, setFilters] = useState({});

        const handleSelect = (record, selected) => {
            const keys = selected
                ? tableSelectedRowKey.concat([record.id])
                : tableSelectedRowKey.filter(item => item !== record.id);

            if (!selected) {
                setTableRemovedRowKey([...tableRemovedRowKey, record.id]);
                setTableRemovedRows([...tableRemovedRows, record]);
            } else {
                setTableRemovedRowKey(tableRemovedRowKey.filter(item => item !== record.id));
                setTableRemovedRows(tableRemovedRows.filter(item => item.id !== record.id));
            }

            const rows = selected
                ? [...tableSelectedRows, record]
                : tableSelectedRows.filter(item => item.id !== record.id);

            if (Object.prototype.hasOwnProperty.call(record, "selected")) {
                const operationsTemp = operations;
                if (record.selected === false && selected) {
                    operationsTemp[record.id] = "add";
                } else if (record.selected === false && !selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && !selected) {
                    operationsTemp[record.id] = "remove";
                }
                setOperations(operationsTemp);
            }

            rows.forEach(row => {
                if (row.children) {
                    const isInChildren = row.children.some(child => child.id === record.id);
                    if (isInChildren) {
                        const rowIndex = rows.findIndex(r => r.id === row.id);
                        if (rowIndex > -1) {
                            rows.splice(rowIndex, 1);
                        }

                        const keyIndex = keys.findIndex(k => k === row.id);
                        if (keyIndex > -1) {
                            keys.splice(keyIndex, 1);
                        }
                    }
                }
            });

            if (Object.prototype.hasOwnProperty.call(record, "children")) {
                if (selected) {
                    record.children.forEach(child => {
                        if (!keys.includes(child.id)) {
                            keys.push(child.id);
                        }
                        if (!rows.some(row => row.id === child.id)) {
                            rows.push(child);
                        }
                    });
                } else {
                    const needRemoveRows = record.children.map(child => child.id);
                    needRemoveRows.forEach(id => {
                        const rowIndex = rows.findIndex(r => r.id === id);
                        rows.splice(rowIndex, 1);
                        const keyIndex = keys.findIndex(k => k === id);
                        keys.splice(keyIndex, 1);
                    });
                }
            }
            setTableSelectedRowKey(keys);
            setTableSelectedRows(rows);

            if (rowSelection && rowSelection.onChange) {
                rowSelection.onChange(keys, rows);
            }
        };

        const handleSelectAll = (selected, selectedRows, changeRows) => {
            if (quantity) {
                const currentCount = tableSelectedRowKey.length;
                const remaining = quantity - currentCount;

                if (selected && remaining <= 0) {
                    return; // 已满，不允许再选
                }

                const limitedRows = selected ? changeRows.slice(0, remaining) : changeRows;
                const limitedIds = limitedRows.map(item => item.id);

                const keys = selected
                    ? Array.from(new Set([...tableSelectedRowKey, ...limitedIds]))
                    : tableSelectedRowKey.filter(item => !limitedIds.includes(item));
                setTableSelectedRowKey(keys);

                const rows = selected
                    ? Array.from(new Set([...tableSelectedRows, ...limitedRows]))
                    : tableSelectedRows.filter(item => !limitedIds.includes(item.id));
                setTableSelectedRows(rows);

                if (!selected) {
                    setTableRemovedRowKey([...tableRemovedRowKey, ...limitedIds]);
                    setTableRemovedRows([...tableRemovedRows, ...limitedRows]);
                } else {
                    setTableRemovedRowKey(tableRemovedRowKey.filter(item => !limitedIds.includes(item)));
                    setTableRemovedRows(tableRemovedRows.filter(item => !limitedIds.includes(item.id)));
                }

                if (Object.prototype.hasOwnProperty.call(changeRows[0], "selected")) {
                    const operationsTemp = { ...operations };
                    limitedRows.forEach(record => {
                        if (record.selected === false && selected) {
                            operationsTemp[record.id] = "add";
                        } else if (record.selected === false && !selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && !selected) {
                            operationsTemp[record.id] = "remove";
                        }
                    });
                    setOperations(operationsTemp);
                }

                if (rowSelection && rowSelection.onChange) {
                    rowSelection.onChange(keys, rows);
                }
            } else {
                const ids = changeRows.map(item => item.id);
                const keys = selected
                    ? tableSelectedRowKey.concat(ids)
                    : tableSelectedRowKey.filter(item => !ids.includes(item));
                setTableSelectedRowKey(keys);

                if (!selected) {
                    setTableRemovedRowKey([...tableRemovedRowKey, ...ids]);
                    setTableRemovedRows([...tableRemovedRows, ...changeRows]);
                } else {
                    setTableRemovedRowKey(tableRemovedRowKey.filter(item => !ids.includes(item)));
                    setTableRemovedRows(tableRemovedRows.filter(item => !ids.includes(item.id)));
                }
                const rows = selected
                    ? [...tableSelectedRows, ...changeRows]
                    : tableSelectedRows.filter(item => !ids.includes(item.id));
                setTableSelectedRows(rows);

                // for default selected rows
                if (Object.prototype.hasOwnProperty.call(changeRows[0], "selected")) {
                    changeRows.map(record => {
                        const operationsTemp = operations;
                        if (record.selected === false && selected) {
                            operationsTemp[record.id] = "add";
                        } else if (record.selected === false && !selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && !selected) {
                            operationsTemp[record.id] = "remove";
                        }
                        setOperations(operationsTemp);
                    });
                }

                if (rowSelection && rowSelection.onChange) {
                    rowSelection.onChange(keys, rows);
                }
            }
        };

        const tableRowSelection = useMemo(() => {
            if (rowSelection?.type === "radio") {
                return rowSelection;
            }

            if (disableInternalRowSelection && rowSelection) {
                return rowSelection;
            }

            return {
                selectedRowKeys: tableSelectedRowKey,
                onSelect: handleSelect,
                onSelectAll: handleSelectAll,
                getCheckboxProps: rowSelection?.getCheckboxProps,
                fixed: rowSelection?.fixed,
                checkStrictly: rowSelection?.checkStrictly
            };
        }, [
            disableInternalRowSelection,
            rowSelection?.type,
            rowSelection?.getCheckboxProps,
            rowSelection?.fixed,
            rowSelection?.checkStrictly,
            tableSelectedRowKey,
            handleSelect,
            handleSelectAll
        ]);

        useImperativeHandle(ref, () => ({
            refreshTable() {
                fetchData().then();
            },
            setTableLoading(value) {
                setLoading(value);
            },
            getSelectedRow: () => {
                return { tableSelectedRowKey, tableSelectedRows };
            },
            getRemovedRow: () => {
                return { tableRemovedRowKey, tableRemovedRows };
            },
            clearSelectedRow: () => {
                setTableSelectedRowKey([]);
                setTableSelectedRows([]);
            },
            getOperations: () => {
                return operations;
            },
            getTableData: () => {
                return data;
            }
        }));

        const fetchData = async () => {
            setLoading(true);

            const filterFields = filters ? createFilterFields(filters, matchModes) : [];
            const sortFields = [];
            if (sorter.field && sorter.order) {
                sortFields.push({
                    field: sorter.field,
                    order: sorter.order === "ascend" ? "asc" : "desc"
                });
            }

            try {
                let response = await fetchAPIInfo(
                    ...(fetchAPIParams
                        ? [
                            ...fetchAPIParams,
                            pagination.current,
                            pagination.pageSize,
                            filterFields,
                            sortFields,
                            searchFields
                        ]
                        : [pagination.current, pagination.pageSize, filterFields, sortFields, searchFields])
                );
                if (response.data.length === 0 && response.total !== 0) {
                    response = await fetchAPIInfo(
                        ...(fetchAPIParams
                            ? [
                                ...fetchAPIParams,
                                Math.ceil(response.total / response.pageSize),
                                pagination.pageSize,
                                [],
                                [],
                                searchFields
                            ]
                            : [
                                Math.ceil(response.total / response.pageSize),
                                pagination.pageSize,
                                [],
                                [],
                                searchFields
                            ])
                    );
                }

                // for default selected rows
                const responseDataTemp = JSON.parse(JSON.stringify(response.data));
                responseDataTemp.forEach(item => {
                    item.selected = operations[item.id] === "add" ? true : item.selected;
                });

                setData(responseDataTemp);
                if (responseDataTemp.every(item => "selected" in item)) {
                    const backendSelectedRowKeys = responseDataTemp.filter(item => item.selected).map(item => item.id);
                    const frontendSelectedRowKeys = responseDataTemp
                        .filter(item => tableSelectedRowKey.indexOf(item.id) > -1)
                        .map(item => item.id);
                    const removedRowKeys = responseDataTemp
                        .filter(item => tableRemovedRowKey.indexOf(item.id) > -1)
                        .map(item => item.id);
                    const selectedRowKeys = Array.from(
                        new Set([...tableSelectedRowKey, ...backendSelectedRowKeys, ...frontendSelectedRowKeys])
                    ).filter(itemId => {
                        return removedRowKeys.indexOf(itemId) === -1;
                    });
                    setTableSelectedRowKey(selectedRowKeys);
                    setTableSelectedRows(responseDataTemp.filter(item => item.selected));
                }
                setPagination(prev => ({
                    ...prev,
                    total: response.total,
                    current: response.page,
                    pageSize: response.pageSize
                }));
            } catch (error) {
                // error
            } finally {
                setLoading(false);
            }
        };

        useEffect(() => {
            const [sortedColumn, sortedOrder] = checkSortedColumn(columns);
            if (sortedColumn) {
                sorter.field = sortedColumn;
                sorter.order = sortedOrder;
                // tableChange("", "", sorter);
            }
        }, []);

        useEffect(() => {
            // if (!props.readTag)
            fetchData().then();
        }, [JSON.stringify(fetchAPIParams), JSON.stringify(searchFields)]);

        const handleSearchChange = e => {
            dispatch(updateAlarmSearchStatus(false));
            dispatch(updateAlarmSearch(""));
            if (tableRowSelection && tableRowSelection.selectedRowKeys) {
                tableRowSelection.selectedRowKeys = [];
            }
            setTableSelectedRows([]);
            setTableSelectedRowKey([]);
            setSearchFields({
                fields: searchFieldsList,
                value: e.target.value
            });
        };

        const tableChange = async (pagination, filters, sorter) => {
            const delay = ms =>
                new Promise(resolve => {
                    setTimeout(resolve, ms);
                });
            await delay(100);
            setSorter(sorter);
            setFilters(filters);
            await handleTableChange(
                pagination,
                filters,
                sorter,
                setPagination,
                searchFields,
                fetchAPIInfo,
                fetchAPIParams,
                setData,
                matchModes,
                setLoading,
                tableSelectedRowKey,
                tableSelectedRows,
                setTableSelectedRowKey,
                setTableSelectedRows,
                tableRemovedRowKey
            );
        };

        return (
            <div >
                <Flex vertical>
                    <Flex gap="middle" style={{ marginBottom: "20px" }}>
                        {extraButton}
                        <div style={{ flexGrow: 1 }} />
                        {searchFieldsList ? <GlobalSearchInput onChange={handleSearchChange} /> : null}
                        {helpDraw}
                    </Flex>
                    <Table
                        rowSelection={rowSelection ? tableRowSelection : null}
                        columns={finalColumns}
                        bordered
                        rowKey={record => record.id}
                        loading={loading}
                        dataSource={data}
                        pagination={searchFieldsList || isShowPagination ? pagination : false}
                        // onChange={tableChange}
                        {...props}
                    />
                </Flex>
            </div>
        );
    }
);

export const AmpConCustomModalForm = ({
    title,
    isModalOpen,
    formInstance,
    layoutProps,
    CustomFormItems,
    onCancel,
    onSubmit,
    isShowSpin = false,
    modalClass = "",
    footer
}) => (
    <Modal
        title={
            typeof title === "string" ? (
                <div>
                    {title}
                    <Divider style={{ marginTop: 8, marginBottom: 0 }} />
                </div>
            ) : (
                title
            )
        }
        open={isModalOpen}
        onCancel={onCancel}
        onOk={formInstance.submit}
        destroyOnClose
        className={modalClass || ""}
        footer={
            footer || [
                <Divider style={{ marginTop: 0, marginBottom: 20 }} />,
                <Button key="cancel" onClick={onCancel}>
                    Cancel
                </Button>,
                <Button key="ok" type="primary" onClick={formInstance.submit}>
                    Apply
                </Button>
            ]
        }
    >
        {/* <Divider /> */}
        <Form
            layout="horizontal"
            form={formInstance}
            onFinish={onSubmit}
            {...layoutProps}
            validateTrigger="onBlur"
            labelAlign="left"
            style={{
                minHeight: modalClass === "ampcon-middle-modal" ? "268px" : "auto"
            }}
        >
            {CustomFormItems}
        </Form>
        <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
    </Modal>
);

export const AmpConCustomModalTABTable = ({
    title,
    selectModalOpen,
    onCancel,
    items,
    modalClass = "",
    footer = null
}) => (
    <Modal
        title={
            typeof title === "string" ? (
                <div>
                    {title}
                    <Divider style={{ marginTop: 8, marginBottom: 0 }} />
                </div>
            ) : (
                title
            )
        }
        open={selectModalOpen}
        onCancel={onCancel}
        destroyOnClose
        footer={footer || null}
        className={modalClass || ""}
    >
        <Tabs className="radioGroupTabs" items={items} />
    </Modal>
);

export const AmpConCustomModal = ({ title, childItems, isModalOpen, onCancel, modalClass = "", footer }) => (
    <Modal
        title={
            typeof title === "string" ? (
                <div>
                    {title}
                    <Divider style={{ marginTop: 8, marginBottom: 0 }} />
                </div>
            ) : (
                title
            )
        }
        open={isModalOpen}
        onCancel={onCancel}
        destroyOnClose
        footer={footer || null}
        className={modalClass || ""}
    >
        {childItems}
    </Modal>
);

export const AmpConCustomStaticTable = forwardRef(
    ({ columns, rowSelection, extraButton, helpDraw, data, filterFunc, ...props }, ref) => {
        // eslint-disable-next-line no-unused-vars
        const [_, __, searchFields, setSearchFields, ___, ____, loading, setLoading, pagination, setPagination] =
            useTableInitialElement([], false);

        const [tableData, setTableData] = useState([]);

        const [tableSelectedRowKey, setTableSelectedRowKey] = useState(
            rowSelection ? rowSelection.selectedRowKeys : []
        );
        const [tableSelectedRows, setTableSelectedRows] = useState(rowSelection ? rowSelection.selectedRows : []);
        const [operations, setOperations] = useState({});

        const handleSelect = (record, selected) => {
            const keys = selected
                ? tableSelectedRowKey.concat([record.id])
                : tableSelectedRowKey.filter(item => item !== record.id);
            setTableSelectedRowKey(keys);

            const rows = selected
                ? [...tableSelectedRows, record]
                : tableSelectedRows.filter(item => item.id !== record.id);
            setTableSelectedRows(rows);

            if (Object.prototype.hasOwnProperty.call(record, "selected")) {
                const operationsTemp = operations;
                if (record.selected === false && selected) {
                    operationsTemp[record.id] = "add";
                } else if (record.selected === false && !selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && !selected) {
                    operationsTemp[record.id] = "remove";
                }
                setOperations(operationsTemp);
            }

            if (rowSelection && rowSelection.onChange) {
                rowSelection.onChange(keys, rows);
            }
        };

        const handleSelectAll = (selected, selectedRows, changeRows) => {
            const ids = changeRows.map(item => item.id);
            const keys = selected
                ? tableSelectedRowKey.concat(ids)
                : tableSelectedRowKey.filter(item => !ids.includes(item));
            setTableSelectedRowKey(keys);

            const rows = selected
                ? [...tableSelectedRows, ...changeRows]
                : tableSelectedRows.filter(item => !ids.includes(item.id));
            setTableSelectedRows(rows);

            // for default selected rows
            if (Object.prototype.hasOwnProperty.call(changeRows[0], "selected")) {
                changeRows.map(record => {
                    const operationsTemp = operations;
                    if (record.selected === false && selected) {
                        operationsTemp[record.id] = "add";
                    } else if (record.selected === false && !selected) {
                        delete operationsTemp[record.id];
                    } else if (record.selected === true && selected) {
                        delete operationsTemp[record.id];
                    } else if (record.selected === true && !selected) {
                        operationsTemp[record.id] = "remove";
                    }
                    setOperations(operationsTemp);
                });
            }

            if (rowSelection && rowSelection.onChange) {
                rowSelection.onChange(keys, rows);
            }
        };

        const tableRowSelection =
            rowSelection && rowSelection.type === "radio"
                ? rowSelection
                : {
                    selectedRowKeys: tableSelectedRowKey,
                    onSelect: handleSelect,
                    onSelectAll: handleSelectAll,
                    getCheckboxProps:
                        rowSelection === null || rowSelection === undefined ? null : rowSelection.getCheckboxProps
                };

        const formatData = async () => {
            setLoading(true);
            try {
                const total = data?.total || 0;
                const {pageSize, current} = pagination;

                const maxPage = Math.ceil(total / pageSize) || 1;
                const newCurrent = Math.min(current, maxPage);

                const paginated = data?.data?.slice((newCurrent - 1) * pageSize, newCurrent * pageSize) || [];
                setTableData(paginated);
                setPagination(prev => ({
                    ...prev,
                    total,
                    current: newCurrent
                }));
            } catch (error) {
                // error
            } finally {
                setLoading(false);
            }
        };

        useEffect(() => {
            formatData().then();
            // console.log(data);
        }, [data]);

        const handleSearchChange = e => {
            const searchData = data.data.filter(obj => {
                return Object.values(obj).some(value => {
                    if (typeof value === "string") {
                        return value.toLowerCase().includes(e.target.value.toLowerCase());
                    }
                });
            });
            setTableData(searchData);
        };

        const tableChange = async (pagination, filters, sorter) => {
            if (data.data) {
                const sortedData = data.data.sort((a, b) => {
                    if (sorter.column) {
                        if (sorter.order === "ascend") {
                            return a[sorter.column] - b[sorter.column];
                        }
                        return b[sorter.column] - a[sorter.column];
                    }
                    return 0;
                });

                let filterData = sortedData;
                if (filterFunc) {
                    filterData = filterFunc(sortedData, filters);
                    // console.log(filterData);
                }

                setPagination(prev => ({
                    ...prev,
                    current: pagination.current,
                    pageSize: pagination.pageSize
                }));

                const paginated = filterData.slice(
                    (pagination.current - 1) * pagination.pageSize,
                    pagination.current * pagination.pageSize
                );
                setTableData(paginated);
            }
        };

        useImperativeHandle(ref, () => ({
            getSelectedRow: () => {
                return { tableSelectedRowKey, tableSelectedRows };
            },
            clearSelectedRow: () => {
                setTableSelectedRowKey([]);
                setTableSelectedRows([]);
            }
        }));

        return (
            <div>
                <Flex vertical>
                    <Flex gap="middle" style={{ marginBottom: "20px" }}>
                        {extraButton}
                        <div style={{ flexGrow: 1 }} />
                        <GlobalSearchInput onChange={handleSearchChange} />
                        {helpDraw}
                    </Flex>
                    <Table
                        rowSelection={rowSelection ? tableRowSelection : null}
                        columns={columns}
                        bordered
                        rowKey={record => record.id}
                        loading={loading}
                        dataSource={tableData}
                        pagination={pagination}
                        onChange={tableChange}
                        {...props}
                    />
                </Flex>
            </div>
        );
    }
);

export const AmpConCustomTelemteryTable = ({
    columnsConfig,
    data,
    tableWidth,
    showSetting = true,
    showSettingColumn = false
}) => {
    const defaultCheckedList = columnsConfig.map(item => item.dataIndex);
    const [form] = useForm();
    const [isSelectColumnsModalOpen, setSelectColumnsModalOpen] = useState(false);
    const [columnsList, setColumnsList] = useState(defaultCheckedList);
    const [selectedColumns, setSelectedColumns] = useState(defaultCheckedList);
    const containerRef = useRef(null);
    const [tableScrollY, setTableScrollY] = useState(700);

    const handleSelectAllColumns = () => {
        if (selectedColumns.length === columnsConfig.length) {
            setSelectedColumns(
                columnsConfig.filter(column => column.dataIndex === "name").map(column => column.dataIndex)
            );
        } else {
            setSelectedColumns(columnsConfig.map(column => column.dataIndex));
        }
    };

    const handleSelectColumn = value => {
        setSelectedColumns(value);
    };

    useEffect(() => {
        const updateScrollY = () => {
            if (containerRef.current) {
                const offsetTop = containerRef.current.getBoundingClientRect().top;
                const windowHeight = window.innerHeight;

                const padding = 100;
                const availableHeight = Math.max(windowHeight - offsetTop - padding, 300);
                setTableScrollY(availableHeight);
            }
        };

        const resizeObserver = new ResizeObserver(updateScrollY);
        if (containerRef.current) resizeObserver.observe(containerRef.current);
        window.addEventListener("resize", updateScrollY);

        updateScrollY();

        return () => {
            resizeObserver.disconnect();
            window.removeEventListener("resize", updateScrollY);
        };
    }, []);

    const formItems = () => {
        return (
            <Form.Item name="columns" label=" ">
                <Checkbox
                    indeterminate={selectedColumns.length > 0 && selectedColumns.length < columnsConfig.length}
                    checked={selectedColumns.length === columnsConfig.length}
                    onChange={handleSelectAllColumns}
                    style={{ marginBottom: 8 }}
                >
                    Select All
                </Checkbox>
                <Checkbox.Group value={selectedColumns} onChange={handleSelectColumn}>
                    <Row gutter={[16, 8]}>
                        <>
                            {columnsConfig.map(column => (
                                <Col span={12}>
                                    <Checkbox value={column.dataIndex} disabled={column.dataIndex === "name"}>
                                        {column.title}
                                    </Checkbox>
                                </Col>
                            ))}
                        </>
                    </Row>
                </Checkbox.Group>
            </Form.Item>
        );
    };

    const onChangeColumns = () => {
        setColumnsList(selectedColumns);
        setSelectColumnsModalOpen(false);
    };

    const [isHovering, setIsHovering] = useState(false);

    const settingColumn = [
        {
            title: <Icon style={{ fontSize: 20 }} component={isHovering ? settingGreenSvg : settingGreySvg} />,
            key: "setting",
            width: 50,
            fixed: "right",
            onHeaderCell: () => {
                return {
                    onClick: () => {
                        setSelectColumnsModalOpen(true);
                    },
                    onMouseEnter: () => {
                        setIsHovering(true);
                    },
                    onMouseLeave: () => {
                        setIsHovering(false);
                    },
                    style: {
                        cursor: "pointer",
                        textAlign: "center"
                    }
                };
            }
        }
    ];
    return (
        <div ref={containerRef}>
            <Flex vertical>
                {showSetting ? (
                    <div style={{ display: "flex", justifyContent: "flex-end", marginBottom: "10px" }}>
                        <Button
                            icon={<Icon component={settingGreenSvg} />}
                            onClick={() => setSelectColumnsModalOpen(true)}
                        />
                    </div>
                ) : null}
                <Table
                    columns={columnsConfig
                        .filter(item => columnsList.includes(item.dataIndex))
                        .concat(showSettingColumn ? settingColumn : [])}
                    bordered
                    dataSource={data}
                    scroll={{
                        y: data.length > 0 ? tableScrollY : undefined,
                        x: "max-content"
                    }}
                    sticky
                    style={{ width: tableWidth, marginBottom: "24px" }}
                    pagination={false}
                />
            </Flex>
            <AmpConCustomModalForm
                title="All Columns"
                isModalOpen={isSelectColumnsModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 3
                    }
                }}
                CustomFormItems={formItems}
                onCancel={() => {
                    setSelectedColumns(columnsList);
                    setSelectColumnsModalOpen(false);
                }}
                onSubmit={onChangeColumns}
                modalClass="ampcon-middle-modal"
            />
        </div>
    );
};
