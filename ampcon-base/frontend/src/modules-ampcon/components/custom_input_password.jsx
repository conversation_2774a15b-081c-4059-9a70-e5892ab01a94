import React, {forwardRef, useImperativeHandle, useState} from "react";
import {Input} from "antd";
import {EyeOutlined, EyeInvisibleOutlined} from "@ant-design/icons";

const CustomPasswordInput = forwardRef((props, ref) => {
    const {value, onChange, ...restProps} = props;
    const [isInit, setIsInit] = useState(true);
    const [passwordVisible, setPasswordVisible] = useState(false);

    useImperativeHandle(ref, () => ({}));

    const handleVisibilityToggle = () => {
        if (!isInit) {
            setPasswordVisible(!passwordVisible);
        } else {
            setIsInit(false);
            onChange({target: {value: ""}});
        }
        if (props.suffix && props.suffix.props && typeof props.suffix.props.onClick === "function") {
            props.suffix.props.onClick();
        }
    };

    const handleChange = e => {
        setIsInit(false);
        if (onChange) onChange(e);
    };

    const customOnClickCallback = e => {
        if (isInit && value === "********") {
            onChange({target: {value: ""}});
            setIsInit(false);
        }
        if (restProps.onChange) restProps.onChange(e);
    };

    return (
        <Input
            {...restProps}
            type={passwordVisible ? "text" : "password"}
            value={value}
            onChange={handleChange}
            onClick={customOnClickCallback}
            suffix={
                passwordVisible ? (
                    <EyeOutlined style={{color: "#c5c5c5"}} onClick={handleVisibilityToggle} />
                ) : (
                    <EyeInvisibleOutlined style={{color: "#c5c5c5"}} onClick={handleVisibilityToggle} />
                )
            }
        />
    );
});

export default CustomPasswordInput;
