import {Button, Divider, Flex, Input, Modal, Tooltip} from "antd";
import {useState} from "react";

const ConfigContentTextareaModal = ({
    title,
    content,
    saveCallback,
    handleFileContentChange,
    handleCancel,
    isOpen,
    saveConfigButtonToolTip,
    editConfigButtonTooltip,
    cancelEditConfigButtonTooltip
}) => {
    const [isEdit, setIsEdit] = useState(false);
    const readonlyStyle = {
        height: "35vh",
        border: "none",
        backgroundColor: "#F8FAFB",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none"
    };
    const editStyle = {
        height: "35vh",
        border: "#14c9bb 1px solid",
        backgroundColor: "#F8FAFB",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none"
    };

    const saveButtonCallback = () => {
        saveCallback();
        setIsEdit(false);
    };

    const cancelButtonCallback = () => {
        handleCancel();
        setIsEdit(false);
    };

    const editCallback = () => {
        setIsEdit(true);
    };

    const cancelEditCallBack = () => {
        setIsEdit(false);
    };

    return (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isOpen}
            onOk={saveCallback}
            onCancel={cancelButtonCallback}
            footer={
                isEdit
                    ? [
                          <Divider style={{marginTop: 20, marginBottom: 20}} />,
                          <Tooltip title={cancelEditConfigButtonTooltip} placement="topRight">
                              <Button key="cancel edit" onClick={cancelEditCallBack}>
                                  Cancel Edit
                              </Button>
                          </Tooltip>,
                          <Tooltip title={saveConfigButtonToolTip} placement="topRight">
                              <Button key="submit" type="primary" onClick={saveButtonCallback}>
                                  Apply
                              </Button>
                          </Tooltip>
                      ]
                    : [
                          <Divider style={{marginTop: 20, marginBottom: 20}} />,
                          <Tooltip title={editConfigButtonTooltip} placement="topRight">
                              <Button key="edit" onClick={editCallback}>
                                  Edit
                              </Button>
                          </Tooltip>,
                          <Tooltip title={saveConfigButtonToolTip} placement="topRight">
                              <Button key="submit" type="primary" onClick={saveButtonCallback}>
                                  Apply
                              </Button>
                          </Tooltip>
                      ]
            }
        >
            <Flex flex={1} layout="horizontal" style={{minHeight: "260.23px"}}>
                {isEdit ? (
                    <Input.TextArea style={editStyle} value={content} onChange={handleFileContentChange} />
                ) : (
                    <Input.TextArea style={readonlyStyle} value={content} readOnly />
                )}
            </Flex>
        </Modal>
    );
};

export default ConfigContentTextareaModal;
