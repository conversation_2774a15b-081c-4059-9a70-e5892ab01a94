import React, {forwardRef, useEffect, useImperativeHandle, useRef} from "react";
import * as monaco from "monaco-editor";

const TextEditor = forwardRef(({code, isEditable = true, language = "yaml"}, ref) => {
    const editorRef = useRef(null);
    const monacoInstanceRef = useRef(null);

    useEffect(() => {
        monacoInstanceRef.current = monaco.editor.create(editorRef.current, {
            value: code,
            automaticLayout: true,
            language
        });

        return () => {
            monacoInstanceRef.current.dispose();
        };
    }, []);

    useEffect(() => {
        if (monacoInstanceRef.current) {
            monacoInstanceRef.current.updateOptions({
                readOnly: !isEditable
            });
        }
    }, [isEditable]);

    useEffect(() => {
        if (monacoInstanceRef.current) {
            monacoInstanceRef.current.setValue(code);
        }
    }, [code]);

    useEffect(() => {
        const model = monacoInstanceRef.current.getModel();
        monaco.editor.setModelLanguage(model, language);
    }, [language]);

    useImperativeHandle(ref, () => ({
        getValue: () => {
            return monacoInstanceRef.current ? monacoInstanceRef.current.getValue() : "";
        }
    }));

    return (
        <div
            ref={editorRef}
            style={{
                height: "60vh",
                maxHeight: "calc(100vh - 472px)",
                minHeight: "320px",
                width: "100%",
                border: "1px solid #ccc"
            }}
        />
    );
});
export default TextEditor;
