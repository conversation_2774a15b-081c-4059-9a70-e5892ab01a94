import {Button, Form, Input, Select, Space, Tooltip} from "antd";
import React, {useEffect, useState} from "react";
import {QuestionCircleOutlined} from "@ant-design/icons";

import {searchSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";

const {Option} = Select;

export const CustomSelect = ({onChange, options, defaultValue, style, allowClear, placeholder}) => {
    const [searchValue, setSearchValue] = useState("");
    const [selectedGroup, setSelectedGroup] = useState("");

    const handleSelectChange = value => {
        setSelectedGroup(value);
        onChange(value);
        setSearchValue("");
        setFilteredOptions(options);
    };

    const [filteredOptions, setFilteredOptions] = useState(options);

    useEffect(() => {
        setFilteredOptions(options);
    }, [options]);
    useEffect(() => {
        setSelectedGroup(defaultValue);
    }, [defaultValue]);
    const handleSearch = value => {
        setSearchValue(value);
        const filtered = options.filter(option => option.toLowerCase().includes(value.toLowerCase()));
        setFilteredOptions(filtered);
    };

    const dropDownRender = menu => {
        return (
            <div>
                <div>
                    <Input
                        value={searchValue}
                        onChange={e => handleSearch(e.target.value)}
                        placeholder="Search"
                        prefix={<Icon component={searchSvg} />}
                        allowClear
                        style={{width: "100%", height: "32px", marginBottom: "3px"}}
                    />
                </div>
                {menu}
            </div>
        );
    };
    return (
        <Select
            value={selectedGroup}
            onChange={value => {
                handleSelectChange(value);
            }}
            placeholder={placeholder ? placeholder : "Select GroupName"}
            style={style ? style : null}
            allowClear={allowClear ? allowClear : false}
            dropdownRender={menu => dropDownRender(menu)}
        >
            {filteredOptions.map(option => (
                <Option key={option} value={option}>
                    {option}
                </Option>
            ))}
        </Select>
    );
};

export const LoadGroupForm = ({groups, buttonText, onSelectChange, onFinish, toolTip}) => {
    const [selectedGroup, setSelectedGroup] = useState("");

    const handleSelectChange = value => {
        setSelectedGroup(value);
        onSelectChange(value);
    };
    return (
        <Form layout="inline" onFinish={onFinish} style={{flexWrap: "nowrap"}}>
            <Form.Item
                label="Group Name"
                name="groupName"
                required="true"
                wrapperCol={{style: {width: 180}}}
                style={{marginRight: "6px"}}
            >
                <CustomSelect onChange={handleSelectChange} options={groups} />
            </Form.Item>
            <Form.Item>
                <Space size={6}>
                    <Button type="primary" htmlType="submit" disabled={selectedGroup === "" || selectedGroup === "All"}>
                        {buttonText}
                    </Button>
                    {toolTip && (
                        <Tooltip title={toolTip} placement="right">
                            <QuestionCircleOutlined className="questioncircle-color" />
                        </Tooltip>
                    )}
                </Space>
            </Form.Item>
        </Form>
    );
};

export const ViewReportForm = ({reportTime, onFinish}) => {
    const [selectedReportTime, setSelectedReportTime] = useState("");
    return (
        <Form layout="inline" onFinish={onFinish} style={{flexWrap: "nowrap"}}>
            <Form.Item
                name="reportTime"
                required="true"
                wrapperCol={{style: {width: 180}}}
                style={{marginRight: "6px"}}
            >
                <Select
                    value={selectedReportTime}
                    // style={{width: "280px"}}
                    onChange={value => {
                        setSelectedReportTime(value);
                    }}
                    placeholder="Select Time"
                    // showSearch
                    // optionFilterProp="children"
                >
                    {reportTime.map(name => (
                        <Option key={name} value={name}>
                            {name}
                        </Option>
                    ))}
                </Select>
            </Form.Item>
            <Form.Item>
                <Button type="primary" htmlType="submit" style={{width: 110}} disabled={selectedReportTime === ""}>
                    View Report
                </Button>
            </Form.Item>
        </Form>
    );
};
