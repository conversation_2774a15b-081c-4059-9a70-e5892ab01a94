import React, {forwardRef, useRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useState} from "react";
import {Card, Empty, Flex, Select, message} from "antd";
import {AmpConCustomTreeSelect} from "@/modules-ampcon/components/custom_tree";
import EmptyPic from "@/assets/images/App/empty.png";
import * as echarts from "echarts";

const {Option} = Select;

export const PieEcharts = memo(
    ({
        name,
        fetchDataApi,
        refreshInterval = 600000,
        chartType = "pie",
        colorList = [],
        height = "28vh",
        maxWidth = "450px",
        showPercent = true
    }) => {
        const [seriesData, setSeriesData] = useState([]);

        const fetchData = async () => {
            await fetchDataApi().then(res => {
                if (res.status === 200) {
                    setSeriesData(
                        res.data.map(item => ({
                            ...item,
                            name: item.name
                        }))
                    );
                }
            });
        };

        useEffect(() => {
            fetchData();
            const intervalId = setInterval(fetchData, refreshInterval);
            return () => {
                clearInterval(intervalId);
            };
        }, [name, fetchDataApi]);

        return (
            <BasePieEcharts
                seriesData={seriesData}
                name={name}
                chartType={chartType}
                colorList={colorList}
                height={height}
                maxWidth={maxWidth}
                showPercent={showPercent}
            />
        );
    }
);

export const BasePieEcharts = ({
    seriesData,
    name,
    chartType = "pie",
    colorList = [],
    height = "28vh",
    maxWidth = "430px",
    onClicked = null,
    showPercent = true
}) => {
    let chartRadius;
    switch (chartType) {
        case "pie":
            chartRadius = ["0%", "69%"];
            break;
        case "ring":
            chartRadius = ["45%", "65%"];
            break;
        default:
            chartRadius = ["0%", "70%"];
    }

    const total = seriesData.reduce((sum, item) => sum + item.value, 0);
    const chartRef = useRef();
    useEffect(() => {
        const myChart = echarts.init(chartRef.current);
        const option = {
            tooltip: {
                trigger: "item",
                backgroundColor: "rgba(0, 0, 0, 0.6)", // 设置提示框背景颜色为黑色，透明度为0.6
                textStyle: {
                    color: "#fff" // 设置提示框中的字体颜色为白色
                },
                formatter(params) {
                    const parts = params.name.split(" ");
                    if (parts.length > 1) {
                        parts[parts.length - 1] = parts[parts.length - 1].replace(/\d+(\.\d+)?/g, "");
                    }
                    const bWithoutNumbers = parts.join(" ");
                    return showPercent
                        ? `${params.seriesName} <br/>${bWithoutNumbers}: ${params.value}%`
                        : `${params.seriesName} <br/>${bWithoutNumbers}: ${params.value}`;
                }
            },
            legend: {
                type: "scroll",
                orient: "vertical",
                top: "center",
                left: maxWidth === "450px" ? "10px" : "0px",
                itemGap: 15,
                itemWidth: 12, // 设置图例 item 的宽度
                itemHeight: 12, // 设置图例 item 的高度
                formatter(name) {
                    const nameSplit = name.split(/\s+/);
                    if (
                        nameSplit[0] === "Usage" ||
                        nameSplit[0] === "Free" ||
                        nameSplit[0] === "Used" ||
                        nameSplit[0] === "Unused" ||
                        nameSplit[0] === "Normal" ||
                        nameSplit[0] === "Abnormal" ||
                        nameSplit[0] === "Expired"
                    ) {
                        return showPercent
                            ? `{name|${nameSplit[0]}}{count|${nameSplit[1]}%}`
                            : `{name|${nameSplit[0]}}{count|${nameSplit[1]}}`;
                    }
                    return name;
                }
            },
            textStyle: {
                rich: {
                    name: {
                        color: "#929A9E",
                        lineHeight: 20,
                        textAlign: "center",
                        display: "inline-block",
                        width: maxWidth === "450px" ? 65 : 40
                    },
                    count: {
                        fontSize: "14px",
                        fontWeight: 700,
                        lineHeight: 20,
                        textAlign: "center",
                        display: "inline-block",
                        width: 10
                    }
                }
            },
            series: [
                {
                    name,
                    type: "pie",
                    center: ["70%", "50%"],
                    radius: chartRadius,
                    label: {
                        show: chartType === "ring",
                        position: "center",
                        formatter: () => {
                            if (showPercent) {
                                return "";
                            }
                            return `{value|${total}}\n{total|Total}`;
                        },
                        rich: {
                            value: {
                                color: "#333",
                                fontSize: maxWidth === "450px" ? 20 : "16px",
                                fontWeight: "bold",
                                lineHeight: 30
                            },
                            total: {
                                color: "#333",
                                fontSize: maxWidth === "450px" ? 16 : "14px",
                                lineHeight: 20
                            }
                        }
                    },
                    data: seriesData,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: "rgba(0, 0, 0, 0.5)"
                        }
                    }
                }
            ]
        };
        if (colorList.length !== 0) {
            option.color = colorList;
        }
        myChart.setOption(option);
        myChart.on("click", params => {
            if (onClicked) {
                onClicked(params);
            }
        });

        const handleResize = () => {
            myChart.resize();
        };

        window.addEventListener("resize", handleResize);

        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, [seriesData]);
    return (
        <Flex justify="center" align="center">
            <div style={{height, width: "100%", maxWidth}} ref={chartRef} />
        </Flex>
    );
};

export const BarEcharts = memo(({fetchDataApi, refreshInterval = 600000, colorList = []}) => {
    const [xAxis, setXAxis] = useState([]);
    const [yAxisData, setYAxisData] = useState([]);

    const fetchData = async () => {
        await fetchDataApi().then(res => {
            // if (res.status === 200) {
            //     setXAxis(res.key);
            //     setYAxisData(res.value);
            // }
            setXAxis(res.keys);
            setYAxisData(res.values);
        });
    };

    useEffect(() => {
        fetchData();
        const intervalId = setInterval(fetchData, refreshInterval);
        return () => {
            clearInterval(intervalId);
        };
    }, []);

    return <BaseBarEcharts xAxis={xAxis} yAxisData={yAxisData} colorList={colorList} />;
});

export const StaticBarEcharts = ({data, colorList = [], width = "", onClicked, tooltipStyle = {}, title = ""}) => {
    const [xAxis, setXAxis] = useState([]);
    const [yAxisData, setYAxisData] = useState([]);

    const formatData = () => {
        setXAxis(data.keys);
        setYAxisData(data.values);
    };

    useEffect(() => {
        formatData();
    }, [data]);

    return (
        <BaseBarEcharts
            xAxis={xAxis}
            yAxisData={yAxisData}
            colorList={colorList}
            width={width}
            onClicked={onClicked}
            tooltipStyle={tooltipStyle}
            title={title} // 传递 title
        />
    );
};

export const BaseBarEcharts = ({
    xAxis,
    yAxisData,
    colorList = [],
    width = "",
    onClicked = null,
    tooltipStyle = {},
    title = ""
}) => {
    const chartRef = useRef();
    useEffect(() => {
        const myChart = echarts.init(chartRef.current);

        const defaultTooltip = {
            trigger: "axis",
            formatter: params => {
                const {name} = params[0];
                const {value} = params[0];
                return `${name}: ${value}`;
            }
        };

        const option = {
            title: {
                text: title,
                left: "left",
                top: 12,
                textStyle: {
                    fontSize: 12,
                    fontWeight: 400,
                    color: "#212519"
                }
            },
            tooltip: tooltipStyle.trigger ? tooltipStyle : defaultTooltip,
            xAxis: {
                data: xAxis,
                axisLabel: {
                    interval: 0
                }
            },
            yAxis: {},
            grid: {
                left: 25,
                right: "3%",
                top: title ? "20%" : "8%",
                bottom: "10%"
            },
            series: [
                {
                    type: "bar",
                    barWidth: width || "40%",
                    data: yAxisData
                }
            ]
        };

        if (colorList.length !== 0) {
            option.color = colorList;
        }

        myChart.setOption(option);

        if (onClicked) {
            myChart.on("click", params => {
                if (onClicked) {
                    onClicked(params);
                }
            });
        }

        const handleResize = () => {
            myChart.resize();
        };

        window.addEventListener("resize", handleResize);

        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, [yAxisData, tooltipStyle, title]);

    return (
        <div style={{display: "flex", justifyContent: "center", alignItems: "center", height: "100%"}}>
            <div style={{height: "100%", width: "100%"}} ref={chartRef} />
        </div>
    );
};

export const Static2DBarEcharts = ({
    data,
    colorList = [],
    width = "",
    onClicked,
    tooltipStyle = {},
    title = "",
    tooltipOnItemClick
}) => {
    const [xAxis, setXAxis] = useState([]);
    const [yAxisData, setYAxisData] = useState([]);
    const [seriesNames, setSeriesNames] = useState([]);

    const formatData = () => {
        setXAxis(data.keys);

        const formattedData = data.values[0].map((_, index) => {
            return data.values.map(item => item[index]);
        });
        setYAxisData(formattedData);
        setSeriesNames(data.seriesNames || []);
    };

    useEffect(() => {
        formatData();
        if (tooltipOnItemClick) {
            window.tooltipItemClicked = name => {
                tooltipOnItemClick(name);
            };
        }
    }, [data]);

    return (
        <Base2DBarEcharts
            xAxis={xAxis}
            yAxisData={yAxisData}
            colorList={colorList}
            width={width}
            onClicked={onClicked}
            tooltipStyle={tooltipStyle}
            title={title}
            seriesNames={seriesNames}
            tooltipOnItemClick={tooltipOnItemClick}
        />
    );
};

export const Base2DBarEcharts = ({
    xAxis,
    yAxisData = [],
    colorList = [],
    width = "",
    onClicked = null,
    tooltipStyle = {},
    seriesNames = [],
    title = "",
    tooltipOnItemClick = null
}) => {
    const chartRef = useRef();

    useEffect(() => {
        const myChart = echarts.init(chartRef.current);

        const defaultTooltip = {
            trigger: "axis",
            formatter: params => {
                const {name, value} = params[0];
                return `${name}: ${value}`;
            }
        };

        const hoverColors = ["#FFD35A", "#F98383"];

        const series = yAxisData.map((data, index) => {
            const baseColor = colorList[index] || `hsl(${(index * 360) / yAxisData.length}, 100%, 50%)`;
            const hoverColor = hoverColors[index] || baseColor; // fallback 用 baseColor

            return {
                name: seriesNames[index] || `series ${index + 1}`,
                type: "bar",
                barWidth: width || "40%",
                data,
                itemStyle: {
                    color: baseColor
                },
                emphasis: {
                    itemStyle: {
                        color: hoverColor
                    }
                }
            };
        });

        const option = {
            title: {
                text: title,
                left: "left",
                top: 12,
                textStyle: {
                    fontSize: 12,
                    fontWeight: 400,
                    color: "#212519"
                }
            },
            tooltip: tooltipStyle.trigger ? tooltipStyle : defaultTooltip,
            xAxis: {
                data: xAxis,
                axisLabel: {
                    interval: 0
                }
            },
            yAxis: {},
            grid: {
                left: 25,
                right: "3%",
                top: title ? "20%" : "8%",
                bottom: "10%"
            },
            series
        };

        myChart.setOption(option);

        setTimeout(() => {
            const tooltipContent = document.getElementById("sysname-list");
            if (tooltipContent) {
                tooltipContent.querySelectorAll(".sysname-item").forEach(item => {
                    const name = item.getAttribute("data-name");
                    item.addEventListener("click", e => {
                        e.stopPropagation();
                        if (tooltipOnItemClick) {
                            tooltipOnItemClick(name);
                        }
                    });
                });
            }
        }, 0);

        if (onClicked) {
            myChart.on("click", params => {
                if (onClicked) {
                    onClicked(params);
                }
            });
        }

        const handleResize = () => {
            myChart.resize();
        };

        window.addEventListener("resize", handleResize);

        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, [xAxis, yAxisData, tooltipStyle, title, colorList, width, seriesNames, tooltipOnItemClick, onClicked]);

    return (
        <div style={{display: "flex", justifyContent: "center", alignItems: "center", height: "100%"}}>
            <div style={{height: "100%", width: "100%"}} ref={chartRef} />
        </div>
    );
};

export const Linechart = ({title, chartData, chartXAxis}) => {
    const LinechartRef = useRef();
    useEffect(() => {
        const myzhChart = echarts.init(LinechartRef.current);
        const LineOption = {
            tooltip: {
                trigger: "axis",
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                    color: "#fff"
                },
                formatter(params) {
                    let tooltipContent = `${params[0].axisValueLabel}<br/>`;
                    params.forEach(item => {
                        tooltipContent += `${item.marker} ${item.seriesName}: ${item.value}%<br/>`;
                    });
                    return tooltipContent;
                }
            },
            xAxis: {
                type: "category",
                data: chartData,
                axisLabel: {
                    interval: 0
                }
            },
            yAxis: {
                type: "value",
                min: 0,
                max: 100,
                axisLabel: {
                    formatter: "{value} "
                }
            },
            grid: {
                left: "9%",
                right: "2%",
                top: "5%",
                bottom: "12%"
            },
            series: [
                {
                    name: `${title}`,
                    type: "line",
                    data: chartXAxis,
                    symbol: "none",
                    itemStyle: {
                        color: "#14C9BB"
                    }
                }
            ]
        };
        myzhChart.setOption(LineOption);
        const handleResize = () => {
            myzhChart.resize();
        };

        window.addEventListener("resize", handleResize);

        return () => {
            if (myzhChart) myzhChart.dispose();
            window.removeEventListener("resize", handleResize);
        };
    }, [chartData, chartXAxis]);
    return (
        <div>
            <div style={{height: "95%", width: "100%"}} ref={LinechartRef} />
        </div>
    );
};

export const MultiLineChart = ({title, chartData, chartXAxis}) => {
    const chartRef = useRef(null);
    // 生成静态数据的函数（示例数据）
    const generateRankingData = () => {
        const rankingMap = new Map();

        rankingMap.set("te1", [0, 100, 200, 300, 200, 500, 600, 600, 300, 300]);
        rankingMap.set("te2", [500, 600, 700, 600, 400, 400, 400, 400, 400, 400]);
        rankingMap.set("te3", [200, 200, 200, 400, 100, 400, 300, 200, 500, 400]);
        rankingMap.set("te4", [200, 300, 100, 200, 500, 200, 600, 700, 500, 600]);
        rankingMap.set("te5", [500, 500, 500, 500, 500, 500, 500, 500, 500, 500]);

        return rankingMap;
    };

    // 为每个系列指定颜色
    const lineColors = {
        te1: "#F67B7B",
        te2: "#F9A07A",
        te3: "#F9D779",
        te4: "#77D2F6",
        te5: "green"
    };
    const generateSeriesList = () => {
        const seriesList = [];
        const rankingMap = generateRankingData();
        rankingMap.forEach((data, name) => {
            const lineColor = lineColors[name]; // 获取每条线的颜色
            const series = {
                name,
                type: "line",
                // smooth: true,
                symbol: "circle",
                symbolSize: 10,
                showSymbol: false,
                emphasis: {
                    itemStyle: {
                        color: lineColor,
                        borderWidth: 2,
                        borderColor: "#FFFFFF",
                        shadowBlur: 10,
                        shadowColor: lineColor
                    }
                },
                lineStyle: {
                    width: 2,
                    color: lineColor
                },
                itemStyle: {
                    color: lineColor
                },
                data
            };
            seriesList.push(series);
        });
        return seriesList;
    };
    useEffect(() => {
        const myChart = echarts.init(chartRef.current);

        const option = {
            tooltip: {
                trigger: "axis"
            },
            legend: {
                orient: "horizontal",
                bottom: 0,
                top: "93%",
                data: ["te1", "te2", "te3", "te4", "te5"],
                itemWidth: 10,
                itemHeight: 10,
                icon: "rect",
                itemGap: 35
            },
            grid: {
                left: "2%",
                right: "2%",
                top: "4%",
                bottom: "8%",
                containLabel: true
            },
            xAxis: {
                type: "category",
                boundaryGap: false,
                data: chartData,
                // data: ["0:00", "1:00", "2:00", "3:00", "4:00", "5:00", "6:00", "7:00", "8:00", "9:00"]
                axisLabel: {
                    interval: 0
                }
            },
            yAxis: {
                type: "value",
                axisLabel: {
                    formatter: "{value}"
                },
                min: 0,
                max: 1000,
                interval: 200,
                splitLine: {
                    show: true
                }
            },
            series: generateSeriesList()
        };

        // 渲染图表
        myChart.setOption(option);

        // 窗口尺寸变化时调整图表
        const handleResize = () => {
            myChart.resize();
        };

        window.addEventListener("resize", handleResize);

        // 清理工作：销毁图表实例
        return () => {
            window.removeEventListener("resize", handleResize);
            myChart.dispose();
        };
    }, [chartData, chartXAxis]);

    return (
        <div style={{width: "100%", height: "100%"}}>
            <div ref={chartRef} style={{height: "290px"}} />
        </div>
    );
};

export const CustomLineChart = ({chartOption}) => {
    const chartRef = useRef(null);

    useEffect(() => {
        let myChart;
        const observer = new ResizeObserver(() => {
            if (myChart) {
                myChart.resize();
            }
        });

        const refreshChart = async () => {
            if (chartRef.current) {
                if (myChart) {
                    myChart.dispose();
                }
                await new Promise(resolve => {
                    setTimeout(resolve, 0);
                });

                // 确保容器已渲染后再初始化图表
                setTimeout(() => {
                    myChart = echarts.init(chartRef.current);
                    myChart.setOption(chartOption, true);
                }, 0);
                observer.observe(chartRef.current);
            }
        };

        refreshChart();

        const handleResize = () => {
            if (myChart) {
                myChart.resize();
            }
        };

        window.addEventListener("resize", handleResize);

        return () => {
            window.removeEventListener("resize", handleResize);
            if (myChart) {
                myChart.dispose();
            }
        };
    }, [chartOption]);

    return <div ref={chartRef} style={{width: "100%"}} />;
};

export const TelemetryChart = ({chartData, xAxisData, xAxisInterval, timeRange}) => {
    const option = useMemo(
        () => ({
            tooltip: {
                trigger: "axis",
                extraCssText: "width:240px",
                formatter: params => {
                    const sortedParams = params.sort((a, b) => b.value[1] - a.value[1]);

                    let content = `
                        <div style="width: 100%; margin: 0; padding: 0;">
                            <div style="background-color: #F8FAFB; width: calc(100% + 22px); padding: 8px 0 10px 8px; margin: -11px -12px 10px -11px; border-bottom: 1px solid #F2F2F2;">
                                <div style="font-size: 16px; color: #212519;">
                                    ${params[0].name}
                                </div>
                            </div>
                    `;
                    sortedParams.forEach(item => {
                        const [interfaceName, channel = null] = item.seriesName.split("_");

                        content += `
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <span style="display:inline-block; margin-right:5px; border-radius:1px; width:12px; height:12px; background-color:${item.color};"></span>
                                    <span style="font-weight: 400; font-size: 14px; color: #929A9E; margin-left: 2px;">
                                        ${interfaceName}${channel ? ` C${channel}` : ""}
                                    </span>
                                </div>
                                <span style="margin-left: 20px; font-weight: 400; font-size: 14px; color: #212519;">
                                    ${item.value[1]}
                                </span>
                            </div>
                        `;
                    });

                    content += `</div>`;
                    return content;
                },

                position(pos, params, el, elRect, size) {
                    const obj = {};
                    const [x, y] = pos;
                    const tooltipWidth = el.getBoundingClientRect().width;
                    const parentRect = el.parentElement.getBoundingClientRect();
                    const rightSpace = parentRect.width - x;
                    if (y > window.innerHeight / 2) {
                        obj.bottom = "30px";
                        delete obj.top;
                    }
                    if (rightSpace < x - 10 - tooltipWidth) {
                        obj.left = `${x - tooltipWidth - 10}px`;
                    } else {
                        obj.left = `${x + 10}px`;
                    }

                    return obj;
                }
            },
            legend: {
                data: chartData.map(item => item.name),
                orient: "horizontal", // 设置图例的方向为水平
                top: "90%", // 设置图例的垂直位置
                left: "center", // 设置图例的水平位置
                right: "5%",
                textStyle: {
                    // 图例文字样式
                    fontSize: 15
                },
                itemWidth: 10, // 图例图形的宽度
                itemHeight: 10, // 图例图形的高度
                type: "scroll",
                itemGap: 30,
                pageIconColor: "#A2ACB2", // 默认可点击色值
                pageIconInactiveColor: "#E3E5EB", // 不可点击色值
                width: "95%",
                icon: "rect"
            },
            grid: {
                left: "3%",
                right: "3%",
                top: "5%",
                bottom: "10%",
                containLabel: true,
                width: "95%",
                height: "75%"
            },
            xAxis: {
                type: "category",
                data: xAxisData,
                axisLabel: {
                    interval: xAxisInterval,
                    formatter(value) {
                        const date = new Date(value);
                        const startDate = new Date(timeRange[0] || Date.now() - 5 * 60 * 1000);
                        const endDate = new Date(timeRange[1] || Date.now());
                        const hour = date.getHours().toString().padStart(2, "0");
                        const minute = date.getMinutes().toString().padStart(2, "0");
                        const second = date.getSeconds().toString().padStart(2, "0");

                        const showFullDate =
                            startDate.getMonth() !== endDate.getMonth() || startDate.getDate() !== endDate.getDate();

                        if (showFullDate) {
                            const formatted = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${hour}:${minute}`;
                            return `{yearLabel|${formatted}}`; // 使用富文本样式
                        }

                        return `${hour}:${minute}:${second}`;
                    },
                    rich: {
                        yearLabel: {
                            padding: [0, 0, 0, 25] // 上 右 下 左，向右偏移12px
                        }
                    }
                },
                splitLine: {
                    show: true
                }
            },
            yAxis: {
                type: "value",
                axisLabel: {
                    formatter(value) {
                        if (value > 1e9) {
                            return `${value.toExponential(2)}`;
                        }
                        if (value >= 1000000) {
                            return `${value / 1000000}M`;
                        }
                        if (value >= 1000) {
                            return `${value / 1000}k`;
                        }
                        return value;
                    }
                }
            },
            series: chartData.map(item => ({
                name: item.name,
                type: "line",
                data: item.data,
                symbol: "none"
            })),
            width: "100%",
            height: "180px"
        }),
        [chartData]
    );

    return option.series.length === 0 ? (
        <div style={{display: "flex", justifyContent: "center", alignItems: "center"}}>
            <Empty image={EmptyPic} description="No Data" imageStyle={{display: "block", margin: 0}} />
        </div>
    ) : (
        <CustomLineChart chartOption={option} />
    );
};

export const TelemetryLimitChart = ({
    chartData,
    xAxisData,
    highMajor,
    lowMajor,
    highWarning,
    lowWarning,
    xAxisInterval,
    timeRange,
    selectedChannel
}) => {
    const upperMajorThreshold = highMajor;
    const upperWarningThreshold = highWarning;
    const lowerWarningThreshold = lowWarning;
    const lowerMajorThreshold = lowMajor;
    const thresholds = [upperMajorThreshold, upperWarningThreshold, lowerWarningThreshold, lowerMajorThreshold];

    const allValue = chartData.length !== 0 ? [...chartData[0].data, ...thresholds] : [];
    const maxValue = Math.max(...allValue);
    const minValue = Math.min(...allValue);

    const nonNullValues = thresholds.filter(threshold => threshold !== null && threshold !== undefined);
    if (nonNullValues.length === 1) {
        message.error("cannot have only one threshold value.");
    }
    if (nonNullValues.length > 1) {
        for (let i = 0; i < nonNullValues.length - 1; i++) {
            if (nonNullValues[i] <= nonNullValues[i + 1]) {
                message.error("fetch some wrong threshold values.");
            }
        }
    }

    const generateMarkLine = () => {
        const markLine = [];

        if (lowerMajorThreshold) {
            markLine.push({
                yAxis: lowerMajorThreshold,
                lineStyle: {
                    type: "dashed",
                    color: "#ff0000",
                    width: 1
                },
                label: {
                    show: false
                }
            });
        }
        if (upperMajorThreshold) {
            markLine.push({
                yAxis: upperMajorThreshold,
                lineStyle: {
                    type: "dashed",
                    color: "#ff0000",
                    width: 1
                },
                label: {
                    show: false
                }
            });
        }
        if (lowerWarningThreshold) {
            markLine.push({
                yAxis: lowerWarningThreshold,
                lineStyle: {
                    type: "dashed",
                    color: "#ffbb00",
                    width: 1
                },
                label: {
                    show: false
                }
            });
        }
        if (upperWarningThreshold) {
            markLine.push({
                yAxis: upperWarningThreshold,
                lineStyle: {
                    type: "dashed",
                    color: "#ffbb00",
                    width: 1
                },
                label: {
                    show: false
                }
            });
        }

        return markLine;
    };
    const generatePieces = () => {
        const pieces = [];

        if (upperMajorThreshold) {
            pieces.push({
                gt: upperMajorThreshold,
                color: "#ff0000"
            });
            if (upperWarningThreshold) {
                pieces.push({
                    gt: upperWarningThreshold,
                    lte: upperMajorThreshold,
                    color: "#ffbb00"
                });
            }
        } else if (upperWarningThreshold) {
            pieces.push({
                gt: upperWarningThreshold,
                color: "#ffbb00"
            });
        }
        if (lowerMajorThreshold) {
            pieces.push({
                lt: lowerMajorThreshold,
                color: "#ff0000"
            });
            if (lowerWarningThreshold) {
                pieces.push({
                    gte: lowerMajorThreshold,
                    lt: lowerWarningThreshold,
                    color: "#ffbb00"
                });
            }
        } else if (lowerWarningThreshold) {
            pieces.push({
                lt: lowerWarningThreshold,
                color: "#ffbb00"
            });
        }
        if ((lowerWarningThreshold || lowerMajorThreshold) && (upperWarningThreshold || upperMajorThreshold)) {
            pieces.push({
                gte: lowerWarningThreshold || lowerMajorThreshold,
                lte: upperWarningThreshold || upperMajorThreshold,
                color: "#14C9BB"
            });
        } else if (lowerWarningThreshold || lowerMajorThreshold) {
            pieces.push({
                gte: lowerWarningThreshold || lowerMajorThreshold,
                color: "#14C9BB"
            });
        } else if (upperWarningThreshold || upperMajorThreshold) {
            pieces.push({
                lte: upperWarningThreshold || upperMajorThreshold,
                color: "#14C9BB"
            });
        }

        return pieces;
    };

    const option = useMemo(
        () => ({
            tooltip: {
                trigger: "axis",
                formatter: params => {
                    const sortedParams = params.sort((a, b) => b.value[1] - a.value[1]);

                    let content = `
                        <div style="width: 100%; margin: 0; padding: 0; line-height: 1.7;">
                            <div style="background-color: #F8FAFB; width: calc(100% + 22px); padding: 5px;padding-left:14px; margin: -11px -12px 10px -11px;border-bottom: 1px solid #F2F2F2;">
                                <div style="font-size: 16px;front-weight: 600 ; color: #212519">${params[0].name}</div>
                            </div>
                    `;
                    const defaultColor = "#929A9E";
                    sortedParams.forEach(item => {
                        content += `
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <span style="display:inline-block;margin-right:5px;border-radius:1px;width:12px;height:12px;background-color:${item.color || defaultColor};"></span>
                                    <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">${item.seriesName} ${selectedChannel ? `C${selectedChannel}` : ""}</span>
                                </div>
                                <span style="margin-left: 20px;front-weight: 400; font-size: 14px; color: #212519">${item.value}</span>
                            </div>
                        `;
                        if (item.color === "#14C9BB") {
                            content += `
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; align-items: center;">
                                        <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">Status</span>
                                    </div>
                                    <span style="padding: 0 4px; border-radius: 2px; margin-left: 20px; front-weight: 400; font-size: 14px; color: #14C9BB; border: 1px solid #14C9BB; background: rgba(43, 193, 116, 0.1)">Info</span>
                                </div>
                            `;
                        } else if (item.color === "#ff0000") {
                            content += `
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; align-items: center;">
                                        <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">Status</span>
                                    </div>
                                    <span style="padding: 0 4px; border-radius: 2px; margin-left: 20px;front-weight: 400; font-size: 14px; color: #F53F3F; border: 1px solid #F53F3F; background: rgba(245,63,63,0.1)">Error</span>
                                </div>
                            `;
                        } else if (item.color === "#ffbb00") {
                            content += `
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; align-items: center;">
                                        <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">Status</span>
                                    </div>
                                    <span style="padding: 0 4px; border-radius: 2px; margin-left: 20px;front-weight: 400; font-size: 14px; color: #FFBB00; border: 1px solid #FFBB00; background: rgba(255,187,0,0.1)">Warn</span>
                                </div>
                            `;
                        } else {
                            content += `
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; align-items: center;">
                                        <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">Status</span>
                                    </div>
                                    <span style="padding: 0 4px; border-radius: 2px; margin-left: 20px; front-weight: 400; font-size: 14px; color: #14C9BB; border: 1px solid #14C9BB; background: rgba(43, 193, 116, 0.1)">Info</span>
                                </div>
                            `;
                        }
                    });
                    content += `
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center;">
                                <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">Alarm Upper Limit Value</span>
                            </div>
                            <span style="margin-left: 20px;front-weight: 400; font-size: 14px; color: #212519">${upperMajorThreshold || "--"}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center;">
                                <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">Warning Upper Limit Value</span>
                            </div>
                            <span style="margin-left: 20px;front-weight: 400; font-size: 14px; color: #212519">${upperWarningThreshold || "--"}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center;">
                                <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">Warning Lower Limit Value</span>
                            </div>
                            <span style="margin-left: 20px;front-weight: 400; font-size: 14px; color: #212519">${lowerWarningThreshold || "--"}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center;">
                                <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">Alarm Lower Limit Value</span>
                            </div>
                            <span style="margin-left: 20px;front-weight: 400; font-size: 14px; color: #212519">${lowerMajorThreshold || "--"}</span>
                        </div>
                    `;
                    content += `</div>`;
                    return content;
                },
                position(pos, params, el, elRect, size) {
                    const obj = {};
                    const [x, y] = pos;
                    const tooltipWidth = el.getBoundingClientRect().width;
                    const parentRect = el.parentElement.getBoundingClientRect();
                    const rightSpace = parentRect.width - x;
                    if (y > window.innerHeight / 2) {
                        obj.bottom = "30px";
                        delete obj.top;
                    }
                    if (rightSpace < x - 10 - tooltipWidth) {
                        obj.left = `${x - tooltipWidth - 10}px`;
                    } else {
                        obj.left = `${x + 10}px`;
                    }

                    return obj;
                }
            },
            series: chartData.map(item => ({
                name: item.name,
                type: "line",
                data: item.data,
                symbol: "none",
                markLine: {
                    silent: true, // 不触发事件
                    symbol: "none",
                    data: generateMarkLine()
                }
            })),
            grid: {
                left: "3%",
                right: "3%",
                top: "5%",
                bottom: "10%",
                containLabel: true,
                width: "95%",
                height: "75%"
            },
            xAxis: {
                type: "category",
                data: xAxisData,
                axisLabel: {
                    interval: xAxisInterval,
                    formatter(value) {
                        const date = new Date(value);
                        const startDate = new Date(timeRange[0] || Date.now() - 5 * 60 * 1000);
                        const endDate = new Date(timeRange[1] || Date.now());
                        const hour = date.getHours().toString().padStart(2, "0");
                        const minute = date.getMinutes().toString().padStart(2, "0");
                        const second = date.getSeconds().toString().padStart(2, "0");
                        if (startDate.getMonth() !== endDate.getMonth() || startDate.getDate() !== endDate.getDate()) {
                            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${hour}:${minute}`;
                        }
                        return `${hour}:${minute}:${second}`;
                    }
                },
                splitLine: {
                    show: true
                }
            },
            yAxis: {
                type: "value",
                axisLabel: {
                    formatter(value) {
                        return value;
                    }
                },
                min: Math.round(minValue > 0 ? minValue * 8 : minValue * 12) / 10,
                max: Math.round(maxValue * 12) / 10
            },
            graphic: [
                // 第一个提示
                {
                    type: "line",
                    left: "12%",
                    top: "92%",
                    shape: {
                        x1: 4,
                        y1: 10,
                        x2: 30,
                        y2: 10
                    },
                    style: {
                        stroke: "#14C9BB",
                        lineWidth: 2,
                        lineDash: [] // 设置为实线
                    }
                },
                {
                    type: "text",
                    left: "19%",
                    top: "90%",
                    style: {
                        text: "Measured Value",
                        fill: "#333",
                        fontSize: 14
                    }
                },
                // 第二个提示
                {
                    type: "line",
                    left: "44%",
                    top: "92%",
                    shape: {
                        x1: 4,
                        y1: 10,
                        x2: 30,
                        y2: 10
                    },
                    style: {
                        stroke: "#ff0000",
                        lineWidth: 2,
                        lineDash: [5, 5] // 设置为虚线
                    }
                },
                {
                    type: "text",
                    left: "50%",
                    top: "90%",
                    style: {
                        text: "Upper Limit",
                        fill: "#333",
                        fontSize: 14
                    }
                },
                // 第三个提示
                {
                    type: "line",
                    left: "73%",
                    top: "92%",
                    shape: {
                        x1: 4,
                        y1: 10,
                        x2: 30,
                        y2: 10
                    },
                    style: {
                        stroke: "#ffbb00",
                        lineWidth: 2,
                        lineDash: [5, 5]
                    }
                },
                {
                    type: "text",
                    left: "79%",
                    top: "90%",
                    style: {
                        text: "Lower Limit",
                        fill: "#333",
                        fontSize: 14
                    }
                }
            ],
            width: "100%",
            height: "180px"
        }),
        [chartData]
    );
    if (nonNullValues.length > 1) {
        option.visualMap = {
            type: "piecewise",
            show: false,
            pieces: generatePieces(),
            // pieces: [
            //     {gt: upperMajorThreshold, lt: lowerMajorThreshold, color: "#ff0000"},
            //     {gte: lowerWarningThreshold, lte: upperWarningThreshold, color: "#14C9BB"},
            //     {gt: upperWarningThreshold, lte: upperMajorThreshold, color: "#ffbb00"},
            //     {gte: lowerMajorThreshold, lt: lowerWarningThreshold, color: "#ffbb00"}
            // ],
            dimension: 1 // 表示对y轴值进行判断
        };
    }

    return option.series.length === 0 || chartData[0].data.length === 0 ? (
        <div style={{display: "flex", justifyContent: "center", alignItems: "center"}}>
            <Empty image={EmptyPic} description="No Data" imageStyle={{display: "block", margin: 0}} />
        </div>
    ) : (
        <CustomLineChart chartOption={option} />
    );
};

export const CustomTelemetryCard = forwardRef(
    (
        {label, timeRange, fetchApi, type, cardstyle, target = "", filterList = [], showFilter = true, showTopK = true},
        ref
    ) => {
        const [useTopN, setUseTopN] = useState(false);
        const [chartData, setChartData] = useState([]);
        const [xAxisData, setXAxisData] = useState([]);
        const [topK, setTopK] = useState(5);
        const [selectedInterface, setSelectedInterface] = useState([]);
        const [xAxisInterval, setXAxisInterval] = useState(1);

        const onSelectChange = value => {
            if (value.length === 0) {
                setSelectedInterface([]);
                setUseTopN(false);
                setTopK(5);
                return;
            }
            setSelectedInterface(value);
        };

        const getSelectValue = () => {
            if (selectedInterface.length === 0) {
                return `Top ${topK}`;
            }
            if (useTopN) {
                return `Total ${selectedInterface.length}`;
            }
            return `Top ${topK}`;
        };

        const fetchData = useCallback(async () => {
            const finalTopK = useTopN && selectedInterface.length > 0 ? selectedInterface.length : topK;
            const {chartData, xAxisData} = await fetchApi(label, finalTopK, target, timeRange, selectedInterface, type);
            setChartData(chartData);
            setXAxisData(xAxisData);
            if (timeRange[0] && timeRange[1]) {
                const totalPoints = xAxisData.length;
                const interval = Math.floor(totalPoints / 5);
                setXAxisInterval(interval);
            } else {
                setXAxisInterval(1);
            }
        }, [label, timeRange, topK, useTopN, target, selectedInterface, type]);

        useEffect(() => {
            fetchData();
        }, [label, timeRange, topK, useTopN, selectedInterface, target, type]);

        useImperativeHandle(ref, () => ({
            refreshTelemetry: () => {
                fetchData();
            }
        }));

        return (
            <Card
                title={
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                        <span>{label}</span>
                        <div style={{display: "flex", gap: "10px"}}>
                            {showFilter ? (
                                <AmpConCustomTreeSelect onChange={onSelectChange} treeData={filterList} />
                            ) : null}
                            {showTopK ? (
                                <Select
                                    style={{width: 120}}
                                    value={getSelectValue()}
                                    onChange={value => {
                                        const isTopN = value === `Total ${selectedInterface.length}`;
                                        setUseTopN(isTopN);
                                        if (!isTopN) {
                                            const num = parseInt(value.split(" ")[1], 10);
                                            setTopK(num);
                                        }
                                    }}
                                    defaultValue="Top 5"
                                >
                                    <Option value="Top 5">Top 5</Option>
                                    <Option value="Top 10">Top 10</Option>
                                    <Option value="Top 25">Top 25</Option>
                                    {selectedInterface.length > 0 && (
                                        <Option value={`Total ${selectedInterface.length}`}>
                                            {`Total ${selectedInterface.length}`}
                                        </Option>
                                    )}
                                </Select>
                            ) : null}
                        </div>
                    </div>
                }
                bordered={false}
                style={{
                    height: "350px",
                    width: "100%",
                    ...(cardstyle ?? {})
                }}
                className="linechart"
            >
                <TelemetryChart
                    chartData={chartData}
                    xAxisData={xAxisData}
                    xAxisInterval={xAxisInterval}
                    timeRange={timeRange}
                />
            </Card>
        );
    }
);

export const CustomTelemetryLimitCard = forwardRef(
    ({label, timeRange, fetchApi, type, port, cardstyle, target = "", filterList = [], showFilter = true}, ref) => {
        const [highMajor, setHighMajor] = useState();
        const [lowMajor, setLowMajor] = useState();
        const [highWarning, setHighWarning] = useState();
        const [lowWarning, setLowWarning] = useState();
        const [chartData, setChartData] = useState([]);
        const [xAxisData, setXAxisData] = useState([]);
        const [selectedChannel, setselectedChannel] = useState();
        const [xAxisInterval, setXAxisInterval] = useState(1);
        const [channelIndex, setChannelIndex] = useState();

        const fetchData = useCallback(async () => {
            const data = await fetchApi(label, target, timeRange, selectedChannel, type, port);
            const {channelIndex, chartData, xAxisData, highMajor, lowMajor, highWarning, lowWarning} = data;
            setChannelIndex(channelIndex);
            setHighMajor(highMajor);
            setLowMajor(lowMajor);
            setHighWarning(highWarning);
            setLowWarning(lowWarning);
            setChartData(chartData);
            setXAxisData(xAxisData);
            if (timeRange[0] && timeRange[1]) {
                const totalPoints = xAxisData.length;
                const interval = Math.floor(totalPoints / 5);
                setXAxisInterval(interval);
            } else {
                setXAxisInterval(1);
            }
        }, [label, timeRange, target, selectedChannel, type, port]);

        useEffect(() => {
            fetchData();
        }, [label, timeRange, selectedChannel, target, type, port]);

        useImperativeHandle(ref, () => ({
            refreshTelemetry: () => {
                fetchData();
            }
        }));

        return (
            <Card
                title={
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                        <span>{label}</span>
                        <div style={{display: "flex", gap: "10px"}}>
                            {showFilter ? (
                                <Select
                                    defaultValue="1"
                                    style={{width: 120}}
                                    onChange={value => setselectedChannel(value)}
                                >
                                    {filterList.map(option => (
                                        <Option key={option.split(" ")[1]} value={option.split(" ")[1]}>
                                            {option}
                                        </Option>
                                    ))}
                                </Select>
                            ) : null}
                        </div>
                    </div>
                }
                bordered={false}
                style={{
                    height: "350px",
                    width: "100%",
                    ...(cardstyle ?? {})
                }}
                className="linechart"
            >
                <TelemetryLimitChart
                    chartData={chartData}
                    xAxisData={xAxisData}
                    highMajor={highMajor}
                    lowMajor={lowMajor}
                    highWarning={highWarning}
                    lowWarning={lowWarning}
                    xAxisInterval={xAxisInterval}
                    timeRange={timeRange}
                    selectedChannel={channelIndex}
                />
            </Card>
        );
    }
);

export const TelemetryLinkChart = ({
    title = "",
    label,
    portInfo,
    channelIndex,
    chartData,
    xAxisData,
    xAxisInterval,
    timeRange
}) => {
    const generatePieces = item => {
        const pieces = [];

        const {threshold} = portInfo.find(port => port.source_port === item.name) || 5;
        const allValue = item.data.map(item => item[1]);
        const maxValue = Math.max(...allValue);
        const minValue = Math.min(...allValue);

        // 扩大范围适应边界渲染
        if (threshold >= maxValue) {
            pieces.push({min: minValue - Math.abs(minValue), max: threshold + Math.abs(threshold), color: "#14C9BB"});
            return pieces;
        }
        if (threshold <= minValue) {
            pieces.push({min: threshold - Math.abs(threshold), max: maxValue + Math.abs(maxValue), color: "#ff0000"});
            return pieces;
        }

        pieces.push(
            {min: threshold, max: maxValue + Math.abs(threshold), color: "#ff0000"},
            {min: minValue - Math.abs(minValue), max: threshold, color: "#14C9BB"}
        );
        return pieces;
    };

    const option = useMemo(
        () => ({
            title: {
                text: title,
                left: "left",
                top: 0,
                textStyle: {
                    fontSize: 12,
                    fontWeight: 400,
                    color: "#212519"
                }
            },
            tooltip: {
                trigger: "axis",
                formatter: params => {
                    const sortedParams = params.sort((a, b) => b.value[1] - a.value[1]);

                    let content = `
                        <div style="width: 100%; margin: 0; padding: 0;">
                            <div style="background-color: #F8FAFB; width: calc(100% + 22px); padding: 5px;padding-left:14px; margin: -11px -12px 10px -11px;border-bottom: 1px solid #F2F2F2;">
                                <div style="font-size: 16px;front-weight: 600 ; color: #212519">${params[0].name}</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">Optical Fiber Link</span>
                                </div>
                                <span style="margin-left: 20px;front-weight: 400; font-size: 14px; color: #212519">${label}</span>
                            </div>
                    `;
                    sortedParams.forEach(item => {
                        const {threshold} = portInfo.find(port => port.source_port === item.seriesName) || 5;
                        const isExeed = threshold !== null && item.value[1] > threshold ? "Yes" : "No";
                        content += `
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">Channel</span>
                                </div>
                                <div style="display: flex; align-items: center;">
                                    <span style="display:inline-block;margin-right:5px;border-radius:1px;width:12px;height:12px;background-color:${item.color};"></span>
                                    <span style="front-weight: 400; font-size: 14px; color: #212519; margin-letf: 2px;">${item.seriesName} ${channelIndex[item.seriesIndex] ? `C${channelIndex[item.seriesIndex]}` : ""}</span>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">Light Attenuation(dB)</span>
                                </div>
                                <span style="margin-left: 20px;front-weight: 400; font-size: 14px; color: #212519">${item.value[1]}</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">Threshold</span>
                                </div>
                                <span style="margin-left: 20px;front-weight: 400; font-size: 14px; color: #212519">${threshold === null ? "" : threshold}</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="display: flex; align-items: center;">
                                    <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">Exceeds the Threshold</span>
                                </div>
                                <span style="margin-left: 20px;front-weight: 400; font-size: 14px; color: #212519">${isExeed}</span>
                            </div>
                        `;
                    });

                    content += `</div>`;
                    return content;
                },
                position(pos, params, el, elRect, size) {
                    const obj = {};
                    const [x, y] = pos;
                    const tooltipWidth = el.getBoundingClientRect().width;
                    const parentRect = el.parentElement.getBoundingClientRect();
                    const rightSpace = parentRect.width - x;
                    if (y > window.innerHeight / 2) {
                        obj.bottom = "30px";
                        delete obj.top;
                    }
                    if (rightSpace < x - 10 - tooltipWidth) {
                        obj.left = `${x - tooltipWidth - 10}px`;
                    } else {
                        obj.left = `${x + 10}px`;
                    }

                    return obj;
                }
            },
            legend: {
                formatter(name) {
                    const item = chartData.find(item => item.name === name);
                    const index = chartData.findIndex(item => item.name === name);

                    if (item && channelIndex[index]) {
                        return `${name}_${channelIndex[index]}`;
                    }
                    return name;
                },
                data: chartData.map(item => item.name),
                orient: "horizontal", // 设置图例的方向为水平
                top: "90%", // 设置图例的垂直位置
                left: "center", // 设置图例的水平位置
                right: "5%",
                textStyle: {
                    // 图例文字样式
                    fontSize: 15
                },
                itemWidth: 10, // 图例图形的宽度
                itemHeight: 10, // 图例图形的高度
                type: "scroll",
                itemGap: 30,
                pageIconColor: "#A2ACB2", // 默认可点击色值
                pageIconInactiveColor: "#E3E5EB", // 不可点击色值
                width: "95%",
                icon: "rect"
            },
            grid: {
                left: "3%",
                right: "2%",
                top: "10%",
                bottom: "10%",
                containLabel: true,
                width: "95%",
                height: "75%"
            },
            xAxis: {
                type: "category",
                data: xAxisData,
                axisLabel: {
                    interval: xAxisInterval,
                    formatter(value) {
                        const date = new Date(value);
                        const startDate = new Date(timeRange[0] || Date.now() - 5 * 60 * 1000);
                        const endDate = new Date(timeRange[1] || Date.now());
                        const hour = date.getHours().toString().padStart(2, "0");
                        const minute = date.getMinutes().toString().padStart(2, "0");
                        const second = date.getSeconds().toString().padStart(2, "0");
                        if (startDate.getMonth() !== endDate.getMonth() || startDate.getDate() !== endDate.getDate()) {
                            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${hour}:${minute}`;
                        }
                        return `${hour}:${minute}:${second}`;
                    }
                },
                splitLine: {
                    show: true
                }
            },
            yAxis: {
                type: "value",
                axisLabel: {
                    formatter(value) {
                        if (value > 1e9) {
                            return `${value.toExponential(2)}`;
                        }
                        if (value >= 1000000) {
                            return `${value / 1000000}M`;
                        }
                        if (value >= 1000) {
                            return `${value / 1000}k`;
                        }
                        return value;
                    }
                }
            },
            visualMap: chartData.map((item, index) => {
                return {
                    type: "piecewise",
                    show: false,
                    pieces: generatePieces(item),
                    outOfRange: {color: "transparent"},
                    dimension: 1,
                    seriesIndex: index // 映射到第index个series
                };
            }),
            series: chartData.map(item => ({
                name: item.name,
                type: "line",
                smooth: true,
                data: item.data,
                symbol: "none",
                itemStyle: {color: "#14C9BB"}
            })),
            width: "100%",
            height: "180px"
        }),
        [chartData]
    );

    return option.series.length === 0 ? (
        <div style={{display: "flex", justifyContent: "center", alignItems: "center"}}>
            <Empty image={EmptyPic} description="No Data" imageStyle={{display: "block", margin: 0}} />
        </div>
    ) : (
        <CustomLineChart chartOption={option} />
    );
};

export const CustomTelemetryLinkCard = forwardRef(
    (
        {
            title,
            label,
            timeRange,
            fetchApi,
            type,
            portInfo,
            cardstyle,
            target = "",
            filterList = [],
            showFilter = true,
            showTopK = true
        },
        ref
    ) => {
        const [useTopN, setUseTopN] = useState(false);
        const [chartData, setChartData] = useState([]);
        const [xAxisData, setXAxisData] = useState([]);
        const [topK, setTopK] = useState(5);
        const [selectedInterface, setSelectedInterface] = useState([]);
        // const [selectedChannel, setSelectedChannel] = useState();
        const [xAxisInterval, setXAxisInterval] = useState(1);
        const [channelIndex, setChannelIndex] = useState([]);

        const onSelectChange = value => {
            if (value.length === 0) {
                setSelectedInterface([]);
                setUseTopN(false);
                setTopK(5);
                return;
            }
            setSelectedInterface(value);
        };

        const getSelectValue = () => {
            if (selectedInterface.length === 0) {
                return `Top ${topK}`;
            }
            if (useTopN) {
                return `Total ${selectedInterface.length}`;
            }
            return `Top ${topK}`;
        };

        const fetchData = useCallback(async () => {
            const finalTopK = useTopN && selectedInterface.length > 0 ? selectedInterface.length : topK;
            const {channelIndex, chartData, xAxisData} = await fetchApi(
                label,
                finalTopK,
                target,
                portInfo,
                timeRange,
                selectedInterface,
                type
            );
            setChannelIndex(channelIndex);
            setChartData(chartData);
            setXAxisData(xAxisData);
            if (timeRange[0] && timeRange[1]) {
                const totalPoints = xAxisData.length;
                const interval = Math.floor(totalPoints / 5);
                setXAxisInterval(interval);
            } else {
                setXAxisInterval(1);
            }
        }, [label, timeRange, topK, useTopN, target, selectedInterface, type, portInfo]);

        useEffect(() => {
            fetchData();
        }, [label, timeRange, topK, useTopN, selectedInterface, target, type, portInfo]);

        useImperativeHandle(ref, () => ({
            refreshTelemetry: () => {
                fetchData();
            }
        }));

        return (
            <Card
                title={
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                        <span>{label}</span>
                        <div style={{display: "flex", gap: "10px"}}>
                            {showFilter ? (
                                <AmpConCustomTreeSelect onChange={onSelectChange} treeData={filterList} />
                            ) : null}
                            {showTopK ? (
                                <Select
                                    style={{width: 120}}
                                    value={getSelectValue()}
                                    onChange={value => {
                                        const isTopN = value === `Total ${selectedInterface.length}`;
                                        setUseTopN(isTopN);
                                        if (!isTopN) {
                                            const num = parseInt(value.split(" ")[1], 10);
                                            setTopK(num);
                                        }
                                    }}
                                    defaultValue="Top 5"
                                >
                                    <Option value="Top 5">Top 5</Option>
                                    <Option value="Top 10">Top 10</Option>
                                    <Option value="Top 25">Top 25</Option>
                                    {selectedInterface.length > 0 && (
                                        <Option value={`Total ${selectedInterface.length}`}>
                                            {`Total ${selectedInterface.length}`}
                                        </Option>
                                    )}
                                </Select>
                            ) : null}
                        </div>
                    </div>
                }
                bordered={false}
                style={{
                    height: "350px",
                    width: "100%",
                    ...(cardstyle ?? {})
                }}
                className="linechart"
            >
                <TelemetryLinkChart
                    title={title}
                    label={label}
                    portInfo={portInfo}
                    channelIndex={channelIndex}
                    chartData={chartData}
                    xAxisData={xAxisData}
                    xAxisInterval={xAxisInterval}
                    timeRange={timeRange}
                />
            </Card>
        );
    }
);
