import shrinkHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink_hover.svg?react";
import shrinkSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink.svg?react";
import unfoldHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold_hover.svg?react";
import unfoldSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold.svg?react";
import Icon from "@ant-design/icons";
import {useState} from "react";

const ExpandIcon = ({expanded, onExpand, record, preCallback = null, isTable = true}) => {
    const [isHovered, setIsHovered] = useState(false);

    if (!isTable && (!record.children || record.children.length === 0)) {
        // isTable 为 false，且没有子节点时，不渲染展开按钮
        return null;
    }
    // eslint-disable-next-line no-nested-ternary
    const IconComponent = expanded ? (isHovered ? shrinkHoverSvg : shrinkSvg) : isHovered ? unfoldHoverSvg : unfoldSvg;

    return (
        <span
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={e => {
                if (preCallback) {
                    preCallback();
                }
                e.stopPropagation();
                onExpand(record, e);
            }}
            style={{cursor: "pointer", display: "inline-flex", alignItems: "center"}}
        >
            <Icon component={IconComponent} />
        </span>
    );
};

export default ExpandIcon;
