import {Row, Col, Avatar} from "antd";
import {DatabaseOutlined} from "@ant-design/icons";

const PanelStatisticComponent = ({
    title = "",
    perCent = "100",
    iconColor,
    iconBackgroundColor,
    icon = <DatabaseOutlined />,
    onClick
}) => {
    return (
        <Row justify="center" align="middle" style={{height: "100%"}}>
            <Col span="8">
                <Row justify="center">
                    <Col span="24" align="middle">
                        <Avatar
                            style={{backgroundColor: iconBackgroundColor, color: iconColor}}
                            size={64}
                            icon={icon}
                        />
                    </Col>
                </Row>
            </Col>
            <Col span="16">
                {onClick ? (
                    <a style={{color: "black"}} onClick={onClick}>
                        {title}
                    </a>
                ) : (
                    <p>{title}</p>
                )}
                <p style={{fontSize: "20px", fontWeight: 700, marginBottom: 0}}>{perCent}</p>
            </Col>
        </Row>
    );
};

export default PanelStatisticComponent;
