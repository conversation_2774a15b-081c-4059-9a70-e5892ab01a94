import {But<PERSON>, Di<PERSON><PERSON>, Flex, Modal} from "antd";
import <PERSON><PERSON> from "antd/es/upload/Dragger";
import {UploadTemplateSvg} from "@/utils/common/iconSvg";

const UploadSingleFileModal = ({
    uploadByJsonModalTitle,
    isShowUploadJsonModal,
    preForm,
    fileList,
    uploadButtonCallback,
    beforeUploadCallback,
    onRemoveCallback,
    cancelCallback
}) => {
    return (
        <Modal
            className="switch-config-upload"
            title={
                <div>
                    {uploadByJsonModalTitle}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowUploadJsonModal}
            onCancel={cancelCallback}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />

                    <Flex justify="flex-end">
                        <Button key="cancel" onClick={cancelCallback}>
                            Cancel
                        </Button>
                        <Button key="export" type="primary" onClick={uploadButtonCallback}>
                            Upload
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <div style={{minHeight: "268px"}}>
                {preForm || null}
                <div style={{display: "flex", justifyContent: "star"}}>
                    <div style={{display: "flex", alignItems: "flex-start"}}>
                        <div
                            style={{
                                lineHeight: "32px",
                                textAlign: "right",
                                marginRight: "8px"
                            }}
                        >
                            Upload
                        </div>
                        <Dragger
                            name="file"
                            beforeUpload={file => {
                                return beforeUploadCallback([file]);
                            }}
                            fileList={fileList}
                            multiple={false}
                            style={{marginBottom: "20px", marginLeft: 53, width: "280px"}}
                            onRemove={onRemoveCallback}
                        >
                            <p className="ant-upload-drag-icon">
                                <UploadTemplateSvg />
                            </p>
                            <p className="ant-upload-text" style={{fontSize: 13}}>
                                Click or drag file to this area to upload
                            </p>
                            <p className="ant-upload-hint" style={{fontSize: 13}}>
                                Support for a single upload.
                            </p>
                        </Dragger>
                    </div>
                </div>
            </div>
        </Modal>
    );
};

export default UploadSingleFileModal;
