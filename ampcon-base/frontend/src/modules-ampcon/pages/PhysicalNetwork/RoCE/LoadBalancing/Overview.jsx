import React from "react";
import {Card} from "antd";

import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {roceDlbConfigList} from "@/modules-ampcon/apis/roce_api";
import "./deployment.scss";

const Overview = () => {
    const columns = [
        createColumnConfig("Fabric", "fabric", TableFilterDropdown),
        createColumnConfig("Sysname", "sysname", TableFilterDropdown),
        {
            ...createColumnConfig("DLB", "dlb_enabled", TableFilterDropdown),
            render: (_, record) => (record.dlb_enabled ? "Enabled" : "Disabled")
        },
        {
            ...createColumnConfig("DLB Mode", "mode", TableFilterDropdown),
            render: (_, record) => {
                if (record.mode === "dlb-normal") return "Normal";
                if (record.mode === "dlb-optimal") return "Optimal";
                if (record.mode === "dlb-assigned") return "Assigned";
                return record.mode || "--";
            }
        },
        {
            ...createColumnConfig("Device Status", "online", TableFilterDropdown),
            render: (_, record) => {
                let status;
                if (record.online === 0) {
                    status = "online";
                } else if (record.online === 1) {
                    status = "offline";
                } else {
                    status = "unkown";
                }
                const statusText = {
                    online: "Online",
                    offline: "Offline",
                    unkown: "Unkown"
                };
                return <span className={`device-status device-status--${status}`}>{statusText[status]}</span>;
            }
        }
    ];
    const matchFieldsList = [
        {name: "fabric", matchMode: "fuzzy"},
        {name: "sysname", matchMode: "fuzzy"},
        {name: "dlb_enabled", matchMode: "fuzzy"},
        {name: "mode", matchMode: "fuzzy"},
        {name: "online", matchMode: "fuzzy"}
    ];
    // useEffect(() => {
    //     fetchData();
    // }, [overviewRefresh]);
    // const fetchData = async (current, pageSize, filters, sorter, searchFields) => {
    //     try {
    //         const response = await roceDlbConfigList(current, pageSize, filters, sorter, searchFields);
    //         const configList = response.data.configs;
    //         const mode_mapping = {
    //             "dlb-normal": "Normal",
    //             "dlb-optimal": "Optimal",
    //             "dlb-assigned": "Assigned"
    //         };
    //         const data = configList.map(config => ({
    //             id: config.id,
    //             fabric: "default",
    //             sysname: config.sysname,
    //             dlb: config.dlb_enabled ? "Enabled" : "Disabled",
    //             dlb_mode: mode_mapping[config.mode] ?? config.mode
    //         }));
    //         setConfigs(data);
    //     } catch (error) {
    //         console.error("Failed to get data:", error);
    //     }
    // };
    // console.log(configs);
    // const searchFieldsList = ["fabric", "sysname", "dlb_enabled", "mode", "online"];
    return (
        <div style={{display: "flex", flex: 1}}>
            <div style={{width: "100%"}}>
                <h3 style={{marginBottom: "4px"}}>Dynamic Load Balancing Configuration</h3>
                <AmpConCustomTable
                    columns={columns}
                    fetchAPIInfo={roceDlbConfigList}
                    rowKey={record => record.id}
                    matchFieldsList={matchFieldsList}
                    // searchFieldsList={searchFieldsList}
                    isShowPagination
                />
            </div>
        </div>
    );
};
export default Overview;
