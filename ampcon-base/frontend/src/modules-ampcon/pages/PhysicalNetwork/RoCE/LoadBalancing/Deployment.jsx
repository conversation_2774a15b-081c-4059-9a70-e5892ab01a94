import React, {useState, useEffect, useMemo} from "react";
import {TreeSelect, Radio, Button, Tooltip, Form, Modal, Alert, message, Spin} from "antd";
// import {QuestionCircleOutlined} from "@ant-design/icons";
import unfoldSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold.svg?react";
import unfoldHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold_hover.svg?react";
import shrinkSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink.svg?react";
import shrinkHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink_hover.svg?react";
import {roceGetFabricSwitches, saveDlbConfiguration} from "@/modules-ampcon/apis/roce_api";
import DlbConfiguration from "./resource/dlb_configuration.svg?react";
import "./deployment.scss";
import {useNavigate} from "react-router-dom";
import {QuestionCircleOutlined} from "@ant-design/icons";
import {showCustomErrorMessage} from "@/modules-ampcon/pages/PhysicalNetwork/RoCE/RoCEEasydeploy/custom_message";

const Deployment = () => {
    const [treeData, setTreeData] = useState([]);
    const [hoveredIcons, setHoveredIcons] = useState({});
    const [dlbValue, setDlbValue] = useState(true);
    const [modeValue, setModeValue] = useState("dlb-normal");
    const [isConfirmVisible, setIsConfirmVisible] = useState(false);
    const [isNoteVisible, setIsNoteVisible] = useState(false);
    const [selectedSwitches, setSelectedSwitches] = useState([{}]);
    // const [selectedSysname, setSelectedSysname] = useState([]);
    const [previewData, setPreviewData] = useState([]);
    // const [loading, setLoading] = useState(false);
    const [isShowSpin, setIsShowSpin] = useState(false);
    // const [message, setMessage] = useState("");
    const [form] = Form.useForm();
    const navigate = useNavigate();

    const handleApply = () => {
        setIsNoteVisible(true);
    };
    const handleCancel = () => {
        form.resetFields();

        // 重置状态到默认值
        setDlbValue(true); // DLB 默认为 Enabled
        setModeValue("dlb-normal"); // Mode 默认为 Normal
        // setSelectedSwitches([]);
        // setSelectedSysname([]);
    };
    const handleNo = () => {
        setIsNoteVisible(false);
    };
    const handleConfirmCancel = () => {
        // 处理取消逻辑
        setIsConfirmVisible(false);
    };
    const result = dlbValue ? modeValue : null;
    const config = {enabled: dlbValue, dlb_mode: result};
    const handleYes = async () => {
        try {
            const previewRes = await saveDlbConfiguration(selectedSwitches, config, "preview");
            if (previewRes && previewRes.commands) {
                setPreviewData(previewRes.commands);
            } else {
                setPreviewData([]);
            }
            setIsConfirmVisible(true);
            // setisNoteVisible(false)
        } catch (error) {
            setPreviewData([]);
        }
        setIsNoteVisible(false);
    };
    const handleConfirmApply = async () => {
        setIsShowSpin(true);
        const response = await saveDlbConfiguration(selectedSwitches, config, "save");
        setIsShowSpin(false);

        if (response.status === 200) {
            message.success(response.msg);
            setIsConfirmVisible(false);
            const currentPath = location.pathname;
            const parentPath = currentPath.replace(/\/deployment$/, "");
            navigate(`${parentPath}/overview`);
        } else {
            // 处理多分段错误 (Handle multi-segment errors)
            if (response.data && Array.isArray(response.data) && response.data.length > 0) {
                showCustomErrorMessage(response.msg, response.data, 6);
            } else {
                message.error(response.msg);
            }
            setIsConfirmVisible(false);
        }
    };

    const nodeMap = useMemo(() => {
        const map = {};
        const traverse = nodes => {
            nodes.forEach(node => {
                map[node.value] = {
                    sysname: node.title,
                    switch_sn: node.value
                    // key: node.key
                };
                if (node.children) {
                    traverse(node.children);
                }
            });
        };
        traverse(treeData);
        return map;
    }, [treeData]);

    const handleMouseEnter = id => {
        setHoveredIcons(prev => ({...prev, [id]: true}));
    };

    const handleMouseLeave = id => {
        setHoveredIcons(prev => ({...prev, [id]: false}));
    };
    const getIconComponent = (expanded, isHovered) => {
        if (expanded) {
            return isHovered ? shrinkHoverSvg : shrinkSvg;
        }
        return isHovered ? unfoldHoverSvg : unfoldSvg;
    };
    const switcherIcon = ({expanded, id}) => {
        const IconComponent = getIconComponent(expanded, hoveredIcons[id]);
        return (
            <IconComponent
                style={{width: "16px", height: "16px", marginTop: "4px", marginRight: "8px", marginLeft: "8px"}}
                alt={expanded ? "shrink" : "unfold"}
                onMouseEnter={() => handleMouseEnter(id)}
                onMouseLeave={() => handleMouseLeave(id)}
            />
        );
    };
    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await roceGetFabricSwitches({query_model: null});
                // const switches = response.data.default;
                const keys = Object.keys(response.data);
                const formattedTreeData = keys
                    .map(key => {
                        const switches = response.data[key] || [];
                        const filteredSwitches = switches.filter(s => s.sysname !== null && s.enabled);
                        return {
                            title: key,
                            key,
                            value: key,
                            disabled: !(filteredSwitches.length > 0),
                            children: filteredSwitches.map(s => ({
                                key: s.switch_sn,
                                value: s.switch_sn,
                                title: `${s.sysname} ${s.switch_sn}`
                            }))
                        };
                    })
                    .filter(key => key.children && key.children.length > 0);
                setTreeData(formattedTreeData);
            } catch (error) {
                console.error("Failed to obtain data:", error);
            }
        };
        fetchData();
    }, []);

    const onChange = selectedValues => {
        // 从映射中获取选中节点的完整信息
        const nodesInfo = selectedValues.map(value => {
            return (
                nodeMap[value] || {
                    value,
                    title: `unknown node (${value})`,
                    key: value
                }
            );
        });
        setSelectedSwitches(nodesInfo);
        // console.log("当前选中的节点:", nodesInfo);
        // const sysnameList = nodesInfo.map(node => node.sysname).filter(Boolean);
        // console.log("选中的 sysname 列表:", sysnameList);
        // setSelectedSysname(sysnameList);
    };

    // const onFinish = values => {
    //     handleApply();
    // };
    return (
        <div>
            <h3>
                DLB Configuration
                <Tooltip
                    title="Configure dynamic load balancing (DLB) 
                           to distribute the traffic of ECMP routing
                           across different member links, maximizing
                           load balancing among the member links."
                    placement="top"
                >
                    <QuestionCircleOutlined style={{color: "#999", marginLeft: 4, cursor: "pointer", fontSize: 16}} />
                </Tooltip>
            </h3>
            <div style={{marginBottom: 24}}>
                <Alert
                    message="Note: Reboot the switch for the configuration to take effect."
                    type="info"
                    showIcon
                    closable
                    style={{color: "#367EFF", background: "#F3F8FF", border: 0, height: 40}}
                />
            </div>
            <Form form={form} onFinish={handleApply}>
                <Form.Item
                    label="Sysname"
                    name="sysname"
                    labelAlign="left"
                    labelCol={{flex: "90px"}}
                    rules={[
                        {
                            required: true,
                            message: "Please select at least one sysname"
                        }
                    ]}
                >
                    <TreeSelect
                        onChange={onChange}
                        treeData={treeData}
                        treeCheckable
                        switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
                        style={{width: 280}}
                        allowClear
                        virtual={false}
                        treeDefaultExpandAll
                    />
                </Form.Item>
                <div style={{marginBottom: 26, marginTop: 25}}>
                    <span style={{marginRight: 47}}>
                        DLB
                        <Tooltip
                            title="Configure dynamic load balancing (DLB) 
                            to distribute the traffic of ECMP routing 
                            across different member links, maximizing 
                            load balancing among the member links."
                            placement="top"
                        >
                            <QuestionCircleOutlined style={{color: "#999", marginLeft: 4, cursor: "pointer"}} />
                        </Tooltip>
                    </span>

                    <Radio.Group name="dlb" value={dlbValue} onChange={e => setDlbValue(e.target.value)}>
                        <Radio value>Enable</Radio>
                        <Radio value={false} style={{marginLeft: 26}}>
                            Disable
                        </Radio>
                    </Radio.Group>
                </div>
                {dlbValue && (
                    <div>
                        <span style={{marginRight: 45}}>Mode</span>
                        <Radio.Group
                            name="mode"
                            value={modeValue} // 设置默认选中
                            onChange={e => setModeValue(e.target.value)}
                        >
                            <Radio value="dlb-normal" style={{marginLeft: 9}}>
                                Normal
                                <Tooltip
                                    title="In Normal mode, packets in the same data
                                    flow whose time interval is less than or
                                    equal to flowset-inactive-time form a
                                    FlowSet. If the Flowset inactive time times
                                    out, the optimal port is used, otherwise
                                    the specified port is used."
                                    placement="top"
                                >
                                    <QuestionCircleOutlined style={{color: "#999", marginLeft: 4, cursor: "pointer"}} />
                                </Tooltip>
                            </Radio>
                            <Radio value="dlb-optimal" style={{marginLeft: 14}}>
                                Optimal
                                <Tooltip
                                    title="In Optimal uses, a packet-by-packet load
                                    sharing mechanism is used. The device selects the
                                    path with the lightest load in the current
                                    equal-cost routing group for forwarding
                                    based on the data packet."
                                    placement="top"
                                >
                                    <QuestionCircleOutlined style={{color: "#999", marginLeft: 4, cursor: "pointer"}} />
                                </Tooltip>
                            </Radio>
                            <Radio value="dlb-assigned" style={{marginLeft: 15}}>
                                <span>
                                    Assigned
                                    <Tooltip
                                        title="In Assigned mode, the device selects a path with a lighter load for the first packet of a data stream,
                                         and subsequent packets of the same data stream use the forwarding path of the first packet."
                                        placement="top"
                                    >
                                        <QuestionCircleOutlined
                                            style={{color: "#999", marginLeft: 4, cursor: "pointer"}}
                                        />
                                    </Tooltip>
                                </span>
                            </Radio>
                        </Radio.Group>
                    </div>
                )}
                <div style={{marginTop: 33}}>
                    <Button onClick={handleCancel} style={{marginLeft: 92, marginRight: 15, width: 100, height: 36}}>
                        Cancel
                    </Button>
                    <Button type="primary" htmlType="submit" style={{width: 100, height: 36}}>
                        Apply
                    </Button>
                </div>
            </Form>
            <Modal title="Note" open={isNoteVisible} onCancel={handleNo} footer={null} width={500} height={240}>
                <div>
                    <hr
                        style={{
                            margin: "16px 0",
                            border: "none",
                            borderTop: "1px solid #E7E7E7",
                            width: "100%"
                        }}
                    />
                    <Alert
                        message="Reboot the switch for the DLB configuration to take effect. 
                                    The ongoing services on the switch might be interrupted. 
                                    Do you want to continue?"
                        type="warning"
                        showIcon
                        style={{border: 0, background: "#fff", display: "flex", alignItems: "baseline"}}
                    />
                    <hr
                        style={{
                            margin: "16px 0",
                            border: "none",
                            borderTop: "1px solid #E7E7E7",
                            width: "100%"
                        }}
                    />
                </div>
                <div style={{marginTop: 16, display: "flex", justifyContent: "flex-end"}}>
                    <Button onClick={handleNo} style={{marginRight: 10, width: 100, height: 36}}>
                        NO
                    </Button>
                    <Button type="primary" onClick={handleYes} style={{width: 100, height: 36}}>
                        Yes
                    </Button>
                </div>
            </Modal>
            <Modal
                title="Confirm DLB Configurations to Be Applied"
                open={isConfirmVisible}
                onCancel={handleConfirmCancel}
                footer={null}
                width={680}
                height={480}
            >
                <div style={{height: 300}}>
                    <hr
                        style={{
                            margin: "16px 0",
                            border: "none",
                            borderTop: "1px solid #E7E7E7",
                            width: "100%"
                        }}
                    />
                    <div style={{background: "#F8FAFB", width: 600, height: 260, overflow: "auto"}}>
                        {previewData.length > 0 ? (
                            previewData.map((item, idx) => (
                                <div key={idx} style={{marginBottom: 16}}>
                                    {/* <b>Sysname:</b> {item.sysname}
                                    <br />
                                    <b>CLI:</b> */}
                                    <pre>{item.cli && item.cli.length > 0 ? item.cli.join("\n") : "No order"}</pre>
                                </div>
                            ))
                        ) : (
                            <p>There is no command preview for the moment</p>
                        )}
                    </div>
                </div>
                <hr
                    style={{
                        margin: "16px 0",
                        border: "none",
                        borderTop: "1px solid #E7E7E7",
                        width: "100%"
                    }}
                />
                <div style={{marginTop: 16, display: "flex", justifyContent: "flex-end"}}>
                    <Button onClick={handleConfirmCancel} style={{marginRight: 10, width: 100, height: 36}}>
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={handleConfirmApply}
                        style={{width: 100, height: 36, marginRight: 17}}
                    >
                        Apply
                    </Button>
                </div>
            </Modal>
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
        </div>
    );
};
export default Deployment;
