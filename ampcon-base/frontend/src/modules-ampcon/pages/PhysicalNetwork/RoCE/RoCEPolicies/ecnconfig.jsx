import React, {useState, useEffect, useRef} from "react";
import {
    Button,
    Modal,
    Form,
    Select,
    Radio,
    Row,
    Col,
    Input,
    Space,
    Tooltip,
    message,
    TreeSelect,
    Divider,
    Spin,
    InputNumber
} from "antd";
import Icon, {PlusOutlined, MinusOutlined, QuestionCircleOutlined} from "@ant-design/icons";
import {
    createColumnConfig,
    TableFilterDropdown,
    AmpConCustomTable,
    AmpConCustomModalForm
} from "@/modules-ampcon/components/custom_table";
import {
    getEcnConfigList,
    getFabricSwitches,
    getSwitchPorts,
    saveEcnConfig,
    deleteEcnConfig,
    getFilterSwitchPorts,
    getEcnConfigDetailBySwitch,
    updateEcnConfig
} from "@/modules-ampcon/apis/roce_api";
import CustomQuestionIcon from "./QuestionIcon";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import CustomTreeSelect from "./customTreeSelect";
import {PaginationTable} from "./ext_table";
import style from "./roce_policies.module.scss";
import {fetchFabricSwitchList} from "./utils";
import {addSvg} from "@/utils/common/iconSvg";
import {AmpConTreeSelect} from "@/modules-ampcon/components/custom_tree";
import {showCustomErrorMessage} from "@/modules-ampcon/pages/PhysicalNetwork/RoCE/RoCEEasydeploy/custom_message";

const {Option} = Select;

const ECNConfig = () => {
    const [selectedSysnames, setSelectedSysnames] = useState([]);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [form] = Form.useForm();

    const [fabricTreeData, setFabricTreeData] = useState([]);
    const [portTreeData, setPortTreeData] = useState([]);
    const [portDataCache, setPortDataCache] = useState(new Map());
    const [editRecord, setEditRecord] = useState(null);
    const [isEditMode, setIsEditMode] = useState(false);
    const [currentSwitchSn, setCurrentSwitchSn] = useState(null);
    const [easyEcnValue, setEasyEcnValue] = useState("Enabled");
    const [editPortData, setEditPortData] = useState([]); // 添加缺失的editPortData状态
    const [expandedKeys, setExpandedKeys] = useState([]); // 控制展开的节点keys
    const [portTreeDataByRow, setPortTreeDataByRow] = useState(new Map()); // 为每行存储独立的端口树数据
    const [expandedKeysByRow, setExpandedKeysByRow] = useState(new Map()); // 为每行存储独立的展开状态

    const queueLists = ["0", "1", "2", "3", "4", "5", "6", "7"];

    const tableRef = useRef(null);
    const [isShowSpin, setIsShowSpin] = useState(false);

    const fetchFabricTreeData = async () => {
        const tree = await fetchFabricSwitchList("EcnConfiguration");
        if (tree) {
            setFabricTreeData(tree);
        }
    };

    // 初始化端口树数据，默认所有端口都为enabled状态
    const initPortTreeData = (portData, rowIndex = null) => {
        console.log("ECN initPortTreeData - portData:", portData, "rowIndex:", rowIndex);

        const allPortsForDisplay = portData.map(item => ({
            title: item.port_name,
            value: item.port_name,
            key: item.port_name,
            disabled: false // 初始化时所有端口都可用
        }));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.port_name)),
                disabled: false, // 初始化时All Ports也可用
                children: allPortsForDisplay
            }
        ];

        if (rowIndex !== null) {
            // 为特定行存储端口树数据
            setPortTreeDataByRow(prev => new Map(prev).set(rowIndex, tree));

            // 设置该行的展开状态
            const allKeys = [
                tree[0].value, // 父节点 key
                ...tree[0].children.map(child => child.key) // 所有子节点 key
            ];
            setExpandedKeysByRow(prev => new Map(prev).set(rowIndex, allKeys));
        } else {
            // 保持原有的全局设置（向后兼容）
            setPortTreeData(tree);
            setExpandedKeys([tree[0].value, ...tree[0].children.map(child => child.key)]);
        }
    };

    // 处理端口选择器获得焦点时的逻辑
    const handlePortFocus = name => {
        const currentSysname = form.getFieldValue(["portConfigurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.sn) {
                    const portData = portDataCache.get(sysObj.sn);
                    if (portData) {
                        // 收集当前sn下非当前行占用的端口
                        const occupiedPorts = new Set();
                        const currentFormValues = form.getFieldsValue();

                        if (currentFormValues.portConfigurations) {
                            currentFormValues.portConfigurations.forEach((config, configIndex) => {
                                // 跳过当前行
                                if (configIndex === name) return;

                                if (config.sysname && config.ports) {
                                    try {
                                        const configSysObj = JSON.parse(config.sysname);
                                        // 只处理相同sn的配置
                                        if (configSysObj.sn === sysObj.sn) {
                                            let selectedPorts = [];
                                            if (Array.isArray(config.ports) && config.ports.length > 0) {
                                                const allPortsValue = JSON.stringify(
                                                    portData.map(item => item.port_name)
                                                );
                                                if (config.ports[0] === allPortsValue) {
                                                    selectedPorts = JSON.parse(config.ports[0]);
                                                } else {
                                                    selectedPorts = config.ports;
                                                }
                                            }
                                            selectedPorts.forEach(port => occupiedPorts.add(port));
                                        }
                                    } catch (e) {
                                        console.error("Error parsing config sysname:", e);
                                    }
                                }
                            });
                        }

                        // 构建新的树形数据，将占用的端口置为disabled
                        const allPortsForDisplay = portData.map(item => ({
                            title: item.port_name,
                            value: item.port_name,
                            key: item.port_name,
                            disabled: occupiedPorts.has(item.port_name)
                        }));

                        // 如果有任意端口被占用，All Ports也置为disabled
                        const hasAnyPortOccupied = occupiedPorts.size > 0;

                        const tree = [
                            {
                                title: "All Ports",
                                value: JSON.stringify(portData.map(item => item.port_name)),
                                disabled: hasAnyPortOccupied,
                                children: allPortsForDisplay
                            }
                        ];

                        // 为当前行存储端口树数据
                        setPortTreeDataByRow(prev => new Map(prev).set(name, tree));

                        // 设置当前行的展开状态
                        const allKeys = [
                            tree[0].value, // 父节点 key (使用 value 作为 key)
                            ...tree[0].children.map(child => child.key) // 所有子节点 key
                        ];
                        setExpandedKeysByRow(prev => new Map(prev).set(name, allKeys));
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    // 处理端口选择器失去焦点时的逻辑
    const handlePortBlur = name => {
        const currentSysname = form.getFieldValue(["portConfigurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.sn) {
                    const portData = portDataCache.get(sysObj.sn);
                    if (portData) {
                        // onBlur时将所有option置为enable
                        const allPortsForDisplay = portData.map(item => ({
                            title: item.port_name,
                            value: item.port_name,
                            key: item.port_name,
                            disabled: false
                        }));

                        const tree = [
                            {
                                title: "All Ports",
                                value: JSON.stringify(portData.map(item => item.port_name)),
                                disabled: false,
                                children: allPortsForDisplay
                            }
                        ];

                        // 为当前行存储端口树数据
                        setPortTreeDataByRow(prev => new Map(prev).set(name, tree));
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    // 简化 fetchPortsBySn
    const fetchPortsBySn = async (sn, forceRefresh = false) => {
        try {
            const res = await getFilterSwitchPorts({
                switch_sn: sn,
                query_model: "EcnConfigurationDetail"
            });

            if (res && res.status === 200 && Array.isArray(res.data)) {
                const portData = res.data;
                setPortDataCache(prev => new Map(prev).set(sn, portData));
                initPortTreeData(portData);
            } else {
                message.error(res.msg);
            }
        } catch (error) {
            console.error("Failed to fetch filtered ports:", error);
            message.error("Failed to fetch ports");
            setPortTreeData([]);
        }
    };

    const handleCreate = () => {
        setIsModalVisible(true);
        setEditRecord(null);
        setIsEditMode(false);
        setCurrentSwitchSn(null);
        setEasyEcnValue("Enabled");
        form.resetFields();
        setSelectedSysnames([]);
        form.setFieldsValue({
            easy_ecn: "Enabled",
            ecn_mode: "latency-first",
            sysname: []
        });

        fetchFabricTreeData();
    };

    const handleModalCancel = () => {
        setIsModalVisible(false);
        setIsEditMode(false);
        setCurrentSwitchSn(null);
        setEasyEcnValue("Enabled");
        form.resetFields();
        setPortTreeData([]);
        setEditPortData([]); // 重置editPortData状态
        setExpandedKeys([]); // 重置展开状态
        setPortTreeDataByRow(new Map()); // 重置按行存储的端口树数据
        setExpandedKeysByRow(new Map()); // 重置按行存储的展开状态
    };

    const [selectSwicth, setSelectSwitch] = useState([]);

    // 修改编辑处理函数 - 根据switch_sn加载数据
    const handleECNConfigEdit = async record => {
        try {
            setIsEditMode(true);
            setEditRecord(record); // 设置当前编辑的记录
            setCurrentSwitchSn(record.switch_sn);
            setIsModalVisible(true);

            // fetchFabricTreeData();
            console.log(record);
            const sysnameValue = JSON.stringify({sn: record.switch_sn, sysname: record.sysname});
            const editFabricTreeData = [
                {
                    title: record.fabric,
                    value: record.fabric,
                    key: record.fabric,
                    children: [
                        {
                            title: `${record.sysname} ${record.switch_sn}`,
                            value: sysnameValue,
                            key: sysnameValue
                        }
                    ]
                }
            ];

            setFabricTreeData(editFabricTreeData);
            setSelectSwitch(editFabricTreeData);

            await fetchPortsBySn(record.switch_sn, true);

            // 根据 switch_sn 查询该交换机下所有的 ECN 配置记录
            const detailResponse = await getEcnConfigDetailBySwitch({
                switch_sn: record.switch_sn
            });

            if (detailResponse && detailResponse.status === 200) {
                const configs = detailResponse.data || [];

                // 初始化配置数据
                let mainConfig = null;
                const portConfigurations = [];

                // 处理每个主配置及其详细配置
                configs.forEach(config => {
                    // 记录第一个主配置用于表单初始化
                    if (!mainConfig) {
                        mainConfig = config;
                    }

                    // 处理详细配置数据
                    if (config.details && Array.isArray(config.details) && config.details.length > 0) {
                        config.details.forEach(detail => {
                            // 处理端口数据
                            let ports = [];
                            if (detail.is_all_ports && detail.port) {
                                // 全选端口时，port字段存储的是JSON字符串数组
                                ports = Array.isArray(detail.port) ? [JSON.stringify(detail.port)] : [detail.port];
                            } else {
                                // 非全选时，直接使用port数组
                                ports = Array.isArray(detail.port) ? detail.port : [];
                            }

                            // 处理队列数据
                            let queues = [];
                            if (detail.is_all_queues && detail.queue) {
                                // 全选队列时，queue字段存储的是JSON字符串数组
                                queues = Array.isArray(detail.queue) ? [JSON.stringify(detail.queue)] : [detail.queue];
                            } else {
                                // 非全选时，直接使用queue数组
                                queues = Array.isArray(detail.queue) ? detail.queue : [];
                            }

                            portConfigurations.push({
                                detail_id: detail.id,
                                config_id: config.id,
                                sysname: sysnameValue,
                                ports,
                                queues,
                                min_threshold: detail.min_threshold,
                                max_threshold: detail.max_threshold,
                                drop_probability: detail.drop_probability,
                                ecn_threshold: detail.ecn_threshold,
                                wred_status: detail.wred_enable ? "True" : "False",
                                is_all_ports: detail.is_all_ports,
                                is_all_queues: detail.is_all_queues
                            });
                        });
                    }
                });

                // 如果没有详细配置但有主配置，创建一个空的配置项供编辑
                if (portConfigurations.length === 0 && mainConfig) {
                    portConfigurations.push({
                        detail_id: null,
                        config_id: mainConfig.id,
                        sysname: sysnameValue,
                        ports: [],
                        queues: [],
                        min_threshold: "",
                        max_threshold: "",
                        drop_probability: "",
                        ecn_threshold: null,
                        wred_status: null,
                        is_all_ports: false,
                        is_all_queues: false
                    });
                }

                // 确定Easy ECN状态
                const easyEcnStatus = mainConfig?.enabled ? "Enabled" : "Disabled";

                form.setFieldsValue({
                    sysname: [sysnameValue],
                    easy_ecn: easyEcnStatus,
                    ecn_mode: mainConfig.mode,
                    portConfigurations
                });

                // 同步更新Easy ECN状态到组件状态
                setEasyEcnValue(easyEcnStatus);

                console.log("ECN Detail loaded:", {
                    configs,
                    mainConfig,
                    easyEcnStatus,
                    portConfigurations
                });
            } else {
                message.error("Failed to load ECN configurations");
                setIsModalVisible(false);
                setEditRecord(null);
                setIsEditMode(false);
                setCurrentSwitchSn(null);
            }
        } catch (error) {
            console.error("Failed to load ECN configurations:", error);
            message.error("Failed to load configurations");
            setIsModalVisible(false);
            setEditRecord(null);
            setIsEditMode(false);
            setCurrentSwitchSn(null);
        }
    };

    const handleSubmit = async values => {
        try {
            const isEasyEcnEnabled = values.easy_ecn === "Enabled";
            let groupedConfigurations = [];
            const configBySwitch = {};

            const portConfigurations = values.portConfigurations || [];
            let selectedSwitches = values.sysname || [];
            if (typeof selectedSwitches === "string") {
                selectedSwitches = [selectedSwitches];
            }

            selectedSwitches.forEach(switchValue => {
                let sysObj = {};
                try {
                    sysObj = JSON.parse(switchValue);
                } catch {
                    /* empty */
                }

                if (sysObj.sysname && sysObj.sn) {
                    configBySwitch[sysObj.sn] = {
                        enabled: isEasyEcnEnabled,
                        mode: isEasyEcnEnabled ? values.ecn_mode : null,
                        sysname: sysObj.sysname,
                        switch_sn: sysObj.sn,
                        config_id: editRecord?.id || null,
                        details: []
                    };
                }
            });

            portConfigurations.forEach(item => {
                if (!item.sysname) return;

                let sysObj = {};
                try {
                    sysObj = JSON.parse(item.sysname);
                } catch {
                    sysObj = JSON.parse(values.sysname);
                }

                if (sysObj.sysname && sysObj.sn && configBySwitch[sysObj.sn]) {
                    const portData = portDataCache.get(sysObj.sn);
                    const allPortsValue = portData ? JSON.stringify(portData.map(item => item.port_name)) : null;
                    const allQueuesValue = JSON.stringify(queueLists);

                    // 增加安全检查
                    const is_all_ports =
                        item.ports && item.ports.length === 1 && allPortsValue && item.ports[0] === allPortsValue;

                    const is_all_queues = item.queues && item.queues.length === 1 && item.queues[0] === allQueuesValue;

                    let ports = [];
                    let queues = [];

                    if (is_all_ports && allPortsValue) {
                        try {
                            ports = JSON.parse(item.ports[0]);
                        } catch (e) {
                            console.error("Failed to parse all ports:", e);
                            ports = item.ports || [];
                        }
                    } else {
                        ports = item.ports || [];
                    }

                    if (is_all_queues) {
                        try {
                            queues = JSON.parse(item.queues[0]);
                        } catch (e) {
                            console.error("Failed to parse all queues:", e);
                            queues = item.queues || [];
                        }
                    } else {
                        queues = item.queues || [];
                    }

                    if (!isEasyEcnEnabled) {
                        // 如果item有config_id且当前没有设置，则使用item的config_id
                        if (item.config_id && !configBySwitch[sysObj.sn].config_id) {
                            configBySwitch[sysObj.sn].config_id = item.config_id;
                        }
                        configBySwitch[sysObj.sn].details.push({
                            detail_id: item.detail_id || null,
                            enabled: false,
                            mode: values.ecn_mode,
                            port: ports,
                            queue: queues,
                            max_threshold: item.max_threshold,
                            min_threshold: item.min_threshold,
                            drop_probability: item.drop_probability,
                            ecn_threshold: item.ecn_threshold,
                            wred_enable: item.wred_status === "True",
                            is_all_ports,
                            is_all_queues
                        });
                    }
                }
            });

            groupedConfigurations = Object.values(configBySwitch);

            const params = {
                configurations: groupedConfigurations
            };

            if (!isEasyEcnEnabled && groupedConfigurations.length === 0) {
                message.warning("Please select at least one switch when Easy ECN is disabled");
                return;
            }

            // 编辑模式下需要传递 switch_sn 参数
            if (isEditMode && currentSwitchSn) {
                params.switch_sn = currentSwitchSn;
            }

            // 根据编辑模式选择不同的API函数
            setIsShowSpin(true);
            const ret = isEditMode ? await updateEcnConfig(params) : await saveEcnConfig(params);
            setIsShowSpin(false);
            if (ret.status === 200) {
                message.success(ret.msg);
                setIsModalVisible(false);
                setEditRecord(null);
                setIsEditMode(false);
                setCurrentSwitchSn(null);
                setEasyEcnValue("Enabled");
                form.resetFields();
                setPortTreeData([]);
                setEditPortData([]); // 重置editPortData状态
                setExpandedKeys([]); // 重置展开状态
                setPortTreeDataByRow(new Map()); // 重置按行存储的端口树数据
                setExpandedKeysByRow(new Map()); // 重置按行存储的展开状态
                tableRef.current.refreshTable();
            } else {
                // 处理多分段错误 (Handle multi-segment errors)
                if (ret.data && Array.isArray(ret.data) && ret.data.length > 0) {
                    showCustomErrorMessage(ret.msg, ret.data, 6);
                } else {
                    message.error(ret.msg);
                }
                setIsModalVisible(false);
                setEditRecord(null);
                setIsEditMode(false);
                setCurrentSwitchSn(null);
                setEasyEcnValue("Enabled");
                form.resetFields();
                setPortTreeData([]);
                setEditPortData([]); // 重置editPortData状态
                setExpandedKeys([]); // 重置展开状态
                setPortTreeDataByRow(new Map()); // 重置按行存储的端口树数据
                setExpandedKeysByRow(new Map()); // 重置按行存储的展开状态
                tableRef.current.refreshTable();
            }
        } catch (error) {
            console.error("Save failed:", error);
            message.error("Save failed");
        }
    };

    const handleDeleteConfirm = async record => {
        try {
            setIsShowSpin(true);
            const ret = await deleteEcnConfig({config_id: record.id});
            setIsShowSpin(false);

            if (ret.status === 200) {
                message.success(ret.msg);
                tableRef.current.refreshTable();
            } else {
                // 处理多分段错误 (Handle multi-segment errors)
                if (ret.data && Array.isArray(ret.data) && ret.data.length > 0) {
                    showCustomErrorMessage(ret.msg, ret.data, 6);
                } else {
                    message.error(ret.msg);
                }
                tableRef.current.refreshTable();
            }
        } catch (e) {
            message.error("deleted failed");
        }
    };

    const formItems = () => {
        return (
            <>
                <Form.Item
                    className={style.formItem}
                    name="sysname"
                    label="Sysname"
                    labelAlign="left"
                    labelCol={{flex: "114px"}}
                    rules={[{required: true, message: "Please select a switch!"}]}
                    initialValue={[]}
                >
                    <CustomTreeSelect
                        disabled={isEditMode}
                        treeData={fabricTreeData}
                        placeholder="Please select switches"
                        style={{width: 280}}
                        multiple
                        treeCheckable
                        showCheckedStrategy={TreeSelect.SHOW_CHILD}
                        onChange={(values, labels) => {
                            setSelectedSysnames(values);
                            const filterChildren = fabricTreeData.map(fabric => ({
                                ...fabric,
                                children: fabric.children.filter(item => values.includes(item.value))
                            }));
                            setSelectSwitch(filterChildren);

                            values.forEach(value => {
                                try {
                                    const obj = JSON.parse(value);
                                    if (obj.sn) {
                                        fetchPortsBySn(obj.sn);
                                    }
                                } catch (e) {
                                    console.error("Failed to parse switch value:", e);
                                }
                            });

                            if (easyEcnValue === "Disabled") {
                                const currentConfigurations = form.getFieldValue("portConfigurations") || [];
                                const newConfigurations = values.map(value => {
                                    const existingConfig = currentConfigurations.find(
                                        config => config.sysname === value
                                    );
                                    return (
                                        existingConfig || {
                                            sysname: value,
                                            ports: [],
                                            queues: [],
                                            min_threshold: "",
                                            max_threshold: "",
                                            drop_probability: "",
                                            ecn_threshold: null,
                                            wred_status: null
                                        }
                                    );
                                });
                                form.setFieldsValue({portConfigurations: newConfigurations});
                            }
                        }}
                        treeDefaultExpandAll
                        maxTagCount={1}
                        maxTagTextLength={10}
                        maxTagPlaceholder={omittedValues => `+${omittedValues.length} more`}
                        allowClear
                    />
                </Form.Item>
                <Form.Item
                    className={style.formItem}
                    name="easy_ecn"
                    label={
                        <div style={{display: "flex", alignItems: "center", gap: "4px"}}>
                            Easy ECN
                            <Tooltip
                                title={
                                    <>
                                        Configure ECN for congestion management through Easy ECN or standard ECN: <br />
                                        · To use Easy ECN for globally default ECN configurations with a click, select
                                        Enable. <br />· To use standard ECN for fine-tuning of ECN configurations on
                                        specific interface queues, select Disable.
                                    </>
                                }
                                placement="right"
                            >
                                <QuestionCircleOutlined
                                    style={{color: "#999", marginLeft: 4, cursor: "pointer", fontSize: 14}}
                                />
                            </Tooltip>
                        </div>
                    }
                    labelAlign="left"
                    labelCol={{flex: "114px"}}
                    // rules={[{required: true, message: "Please select Easy ECN status!"}]}
                    initialValue="Enabled"
                >
                    <Radio.Group
                        onChange={e => {
                            const newValue = e.target.value;
                            setEasyEcnValue(newValue);

                            if (newValue === "Disabled" && selectedSysnames.length > 0) {
                                const portConfigurations = selectedSysnames.map(value => ({
                                    sysname: value,
                                    ports: [],
                                    queues: [],
                                    min_threshold: "",
                                    max_threshold: "",
                                    drop_probability: "",
                                    ecn_threshold: null,
                                    wred_status: null
                                }));
                                form.setFieldsValue({portConfigurations});
                            }
                        }}
                    >
                        <Radio value="Enabled">{isEditMode ? "Enabled" : "Enable"}</Radio>
                        <Radio value="Disabled">{isEditMode ? "Disabled" : "Disable"}</Radio>
                    </Radio.Group>
                </Form.Item>
                {easyEcnValue === "Enabled" && (
                    <Form.Item
                        name="ecn_mode"
                        label="ECN Mode"
                        labelAlign="left"
                        labelCol={{flex: "114px"}}
                        // rules={[{required: true, message: "Please select ECN mode!"}]}
                        initialValue="latency-first"
                    >
                        <Radio.Group>
                            <Radio value="latency-first">
                                Latency First{" "}
                                <Tooltip
                                    title="This mode prioritizes maximizing the amount of data transferred across the network, making it ideal for use cases such as bulk data transfers or content delivery systems."
                                    placement="right"
                                >
                                    <QuestionCircleOutlined
                                        style={{color: "#999", marginLeft: 4, cursor: "pointer", fontSize: 14}}
                                    />
                                </Tooltip>
                            </Radio>
                            <Radio value="throughput-first">
                                Throughput First{" "}
                                <Tooltip
                                    title="This mode prioritizes minimizing latency, ensuring that packets experience the least possible delay. This is particularly important for real-time applications such as VoIP, video conferencing, or online gaming, where even small delays can negatively impact performance."
                                    placement="right"
                                >
                                    <QuestionCircleOutlined
                                        style={{color: "#999", marginLeft: 4, cursor: "pointer", fontSize: 14}}
                                    />
                                </Tooltip>
                            </Radio>
                        </Radio.Group>
                    </Form.Item>
                )}

                {easyEcnValue === "Disabled" && (
                    <>
                        <div
                            style={{
                                fontSize: "18px",
                                fontWeight: "bold",
                                borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
                                paddingBottom: "10px",
                                marginBottom: "20px",
                                marginTop: "20px"
                            }}
                        >
                            Port Configuration
                        </div>
                        <Form.List
                            name="portConfigurations"
                            initialValue={selectedSysnames.map(value => ({
                                sysname: value,
                                ports: [],
                                queues: [],
                                min_threshold: "",
                                max_threshold: "",
                                drop_probability: "",
                                ecn_threshold: null,
                                wred_status: null
                            }))}
                            rules={[{required: false}]}
                        >
                            {(fields, {add, remove}) => (
                                <>
                                    {fields.map(({key, name}, index) => (
                                        <Row key={key} gutter={[8, 16]}>
                                            <Col span={23}>
                                                <Row gutter={8}>
                                                    <Form.Item name={[name, "detail_id"]} hidden>
                                                        <Input type="hidden" />
                                                    </Form.Item>
                                                    <Col span={3}>
                                                        <Form.Item
                                                            className={style.formItem}
                                                            name={[name, "sysname"]}
                                                            label={
                                                                index === 0 ? (
                                                                    <>
                                                                        Sysname{" "}
                                                                        <span className={style.requiredIcon1}>*</span>
                                                                    </>
                                                                ) : (
                                                                    ""
                                                                )
                                                            }
                                                            labelCol={{span: 24}}
                                                            wrapperCol={{span: 24}}
                                                            rules={[
                                                                {required: true, message: "Please select switches!"}
                                                            ]}
                                                        >
                                                            {/* <TreeSelect
                                                        disabled={isEditMode}
                                                        style={{width: "100%"}}
                                                        treeData={selectSwicth}
                                                        placeholder="Select a switch"
                                                        dropdownStyle={{
                                                            maxHeight: 200,
                                                            overflow: "auto"
                                                        }}
                                                        treeDefaultExpandAll
                                                        onChange={value => {
                                                            console.log("value", value);
                                                            if (value !== null) {
                                                                try {
                                                                    const obj = JSON.parse(value);
                                                                    fetchPortsBySn(obj.sn, true);
                                                                } catch (e) {
                                                                    console.error("Error parsing sysname:", e);
                                                                }
                                                            }

                                                            form.setFields([
                                                                {
                                                                    name: ["portConfigurations", name, "ports"],
                                                                    value: []
                                                                }
                                                            ]);
                                                        }}
                                                        allowClear
                                                    /> */}
                                                            <AmpConTreeSelect
                                                                onChange={value => {
                                                                    console.log("value", value);
                                                                    if (value) {
                                                                        try {
                                                                            const obj = JSON.parse(value);
                                                                            fetchPortsBySn(obj.sn, true);
                                                                        } catch (e) {
                                                                            console.error("Error parsing sysname:", e);
                                                                        }
                                                                    } else {
                                                                        setPortTreeData([]);
                                                                    }

                                                                    form.setFields([
                                                                        {
                                                                            name: ["portConfigurations", name, "ports"],
                                                                            value: []
                                                                        }
                                                                    ]);
                                                                }}
                                                                treeData={selectSwicth}
                                                                placeholder="Select a switch"
                                                                disabled={isEditMode}
                                                                treeDefaultExpandAll
                                                            />
                                                        </Form.Item>
                                                    </Col>
                                                    <Col span={3}>
                                                        <Form.Item
                                                            className={style.formItem}
                                                            name={[name, "ports"]}
                                                            label={
                                                                index === 0 ? (
                                                                    <>
                                                                        Ports{" "}
                                                                        <span className={style.requiredIcon1}>*</span>
                                                                    </>
                                                                ) : (
                                                                    ""
                                                                )
                                                            }
                                                            labelCol={{span: 24}}
                                                            wrapperCol={{span: 24}}
                                                            rules={[{required: true, message: "Please select port!"}]}
                                                        >
                                                            <CustomTreeSelect
                                                                popupClassName="custom-popup"
                                                                treeData={
                                                                    form.getFieldValue([
                                                                        "portConfigurations",
                                                                        name,
                                                                        "sysname"
                                                                    ])
                                                                        ? portTreeDataByRow.get(name) || portTreeData
                                                                        : []
                                                                }
                                                                treeExpandedKeys={
                                                                    expandedKeysByRow.get(name) || expandedKeys
                                                                }
                                                                onTreeExpand={expandedKeys => {
                                                                    // 更新当前行的展开状态
                                                                    setExpandedKeysByRow(prev =>
                                                                        new Map(prev).set(name, expandedKeys)
                                                                    );
                                                                }}
                                                                placeholder="Ports"
                                                                maxTagCount={1}
                                                                onFocus={() => handlePortFocus(name)}
                                                                onBlur={() => handlePortBlur(name)}
                                                                onChange={(value, label) => {
                                                                    console.log("value", value);
                                                                    setEditPortData(value);
                                                                }}
                                                            />
                                                        </Form.Item>
                                                    </Col>
                                                    <Col span={3}>
                                                        <Form.Item
                                                            className={style.formItem}
                                                            name={[name, "queues"]}
                                                            label={
                                                                index === 0 ? (
                                                                    <>
                                                                        Queues{" "}
                                                                        <span className={style.requiredIcon1}>*</span>
                                                                    </>
                                                                ) : (
                                                                    ""
                                                                )
                                                            }
                                                            labelCol={{span: 24}}
                                                            wrapperCol={{span: 24}}
                                                            rules={[{required: true, message: "Please select queue!"}]}
                                                        >
                                                            <CustomTreeSelect
                                                                popupClassName="custom-popup"
                                                                maxTagCount={1}
                                                                treeData={[
                                                                    {
                                                                        title: "All Queues",
                                                                        value: JSON.stringify(queueLists),
                                                                        children: queueLists.map(queue => ({
                                                                            title: queue.toString(),
                                                                            value: queue.toString(),
                                                                            key: queue.toString()
                                                                        }))
                                                                    }
                                                                ]}
                                                                placeholder="Queues"
                                                            />
                                                        </Form.Item>
                                                    </Col>
                                                    <Col span={3}>
                                                        <Form.Item
                                                            className={style.formItem}
                                                            name={[name, "min_threshold"]}
                                                            label={
                                                                index === 0 ? (
                                                                    <>
                                                                        Min Threshold
                                                                        <span className={style.requiredIcon1}>*</span>
                                                                    </>
                                                                ) : (
                                                                    ""
                                                                )
                                                            }
                                                            labelCol={{span: 24}}
                                                            wrapperCol={{span: 24}}
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: "Please enter min threshold!"
                                                                },
                                                                ({getFieldValue}) => ({
                                                                    validator(_, value) {
                                                                        if (!value) {
                                                                            return Promise.resolve();
                                                                        }
                                                                        const maxThreshold = getFieldValue([
                                                                            "portConfigurations",
                                                                            name,
                                                                            "max_threshold"
                                                                        ]);
                                                                        if (maxThreshold && value >= maxThreshold) {
                                                                            return Promise.reject(
                                                                                new Error(
                                                                                    "Min threshold must be less than max threshold!"
                                                                                )
                                                                            );
                                                                        }
                                                                        return Promise.resolve();
                                                                    }
                                                                })
                                                            ]}
                                                        >
                                                            <InputNumber
                                                                placeholder="Min Threshold"
                                                                controls={false}
                                                                style={{width: "100%"}}
                                                            />
                                                        </Form.Item>
                                                    </Col>
                                                    <Col span={3}>
                                                        <Form.Item
                                                            className={style.formItem}
                                                            name={[name, "max_threshold"]}
                                                            label={
                                                                index === 0 ? (
                                                                    <>
                                                                        Max Threshold
                                                                        <span className={style.requiredIcon1}>*</span>
                                                                    </>
                                                                ) : (
                                                                    ""
                                                                )
                                                            }
                                                            labelCol={{span: 24}}
                                                            wrapperCol={{span: 24}}
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: "Please enter max threshold!"
                                                                },
                                                                ({getFieldValue}) => ({
                                                                    validator(_, value) {
                                                                        if (!value) {
                                                                            return Promise.resolve();
                                                                        }
                                                                        const minThreshold = getFieldValue([
                                                                            "portConfigurations",
                                                                            name,
                                                                            "min_threshold"
                                                                        ]);
                                                                        if (minThreshold && value <= minThreshold) {
                                                                            return Promise.reject(
                                                                                new Error(
                                                                                    "Max threshold must be greater than min threshold!"
                                                                                )
                                                                            );
                                                                        }
                                                                        return Promise.resolve();
                                                                    }
                                                                })
                                                            ]}
                                                        >
                                                            <InputNumber
                                                                placeholder="Max Threshold"
                                                                controls={false}
                                                                style={{width: "100%"}}
                                                            />
                                                        </Form.Item>
                                                    </Col>
                                                    <Col span={3}>
                                                        <Form.Item
                                                            className={style.formItem}
                                                            name={[name, "drop_probability"]}
                                                            label={
                                                                index === 0 ? (
                                                                    <>
                                                                        Drop Probability (%)
                                                                        <span className={style.requiredIcon1}>*</span>
                                                                    </>
                                                                ) : (
                                                                    ""
                                                                )
                                                            }
                                                            labelCol={{
                                                                span: 24,
                                                                style: {
                                                                    whiteSpace: "nowrap",
                                                                    overflow: "hidden",
                                                                    textOverflow: "ellipsis"
                                                                }
                                                            }}
                                                            wrapperCol={{span: 24}}
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: "Please enter drop probability!"
                                                                },
                                                                {
                                                                    type: "number",
                                                                    message: "Please enter a valid number: 0-100!",
                                                                    min: 0,
                                                                    max: 100
                                                                }
                                                            ]}
                                                        >
                                                            <InputNumber
                                                                placeholder="0 - 100"
                                                                controls={false}
                                                                style={{width: "100%"}}
                                                            />
                                                        </Form.Item>
                                                    </Col>
                                                    <Col span={3}>
                                                        <Form.Item
                                                            className={style.formItem}
                                                            name={[name, "ecn_threshold"]}
                                                            label={
                                                                index === 0 ? (
                                                                    <>
                                                                        ECN Threshold
                                                                        <span className={style.requiredIcon1}>*</span>
                                                                    </>
                                                                ) : (
                                                                    ""
                                                                )
                                                            }
                                                            labelCol={{
                                                                span: 24,
                                                                style: {
                                                                    whiteSpace: "nowrap",
                                                                    overflow: "hidden",
                                                                    textOverflow: "ellipsis"
                                                                }
                                                            }}
                                                            wrapperCol={{span: 24}}
                                                            rules={[
                                                                {required: true, message: "Please enter ecn threshold!"}
                                                            ]}
                                                        >
                                                            <Select placeholder="ECN Threshold" style={{width: "100%"}}>
                                                                <Option value="0">0</Option>
                                                                <Option value="1">1</Option>
                                                            </Select>
                                                        </Form.Item>
                                                    </Col>
                                                    <Col span={3}>
                                                        <Form.Item
                                                            className={style.formItem}
                                                            name={[name, "wred_status"]}
                                                            label={
                                                                index === 0 ? (
                                                                    <>
                                                                        WRED Status
                                                                        <span className={style.requiredIcon1}>*</span>
                                                                    </>
                                                                ) : (
                                                                    ""
                                                                )
                                                            }
                                                            labelCol={{span: 24}}
                                                            wrapperCol={{span: 24}}
                                                            rules={[
                                                                {required: true, message: "Please select wred status!"}
                                                            ]}
                                                        >
                                                            <Select placeholder="WRED Status" style={{width: "100%"}}>
                                                                <Option value="True">Enabled</Option>
                                                                <Option value="False">Disabled</Option>
                                                            </Select>
                                                        </Form.Item>
                                                    </Col>
                                                </Row>
                                            </Col>
                                            <Col>
                                                {index === 0 ? (
                                                    <Button
                                                        onClick={() => {
                                                            // 获取当前顶层选择的交换机
                                                            const currentSysnames = form.getFieldValue("sysname") || [];
                                                            const currentConfigurations =
                                                                form.getFieldValue("portConfigurations") || [];

                                                            // 确定新行要使用的交换机
                                                            const defaultSysname =
                                                                currentSysnames.length > 0 ? currentSysnames[0] : null;

                                                            // 添加新的配置行
                                                            add({
                                                                detail_id: null,
                                                                sysname: editRecord ? defaultSysname : null, // 默认使用第一个选择的交换机
                                                                ports: [],
                                                                queues: [],
                                                                min_threshold: "",
                                                                max_threshold: "",
                                                                drop_probability: "",
                                                                ecn_threshold: null,
                                                                wred_status: null
                                                            });

                                                            if (defaultSysname) {
                                                                if (editRecord === null) {
                                                                    const parsed = JSON.parse(defaultSysname);
                                                                    if (parsed.sn) {
                                                                        // 检查缓存中是否已有该交换机的端口数据
                                                                        if (!portDataCache.has(parsed.sn)) {
                                                                            // 如果没有缓存，则获取端口数据
                                                                            fetchPortsBySn(parsed.sn, false);
                                                                        } else {
                                                                            // 如果有缓存，可选择强制刷新获取最新数据
                                                                            fetchPortsBySn(parsed.sn, true);
                                                                        }
                                                                    }
                                                                }

                                                                // 获取添加后的所有配置的sysname
                                                                const updatedConfigurations = [
                                                                    ...currentConfigurations,
                                                                    {
                                                                        sysname: defaultSysname
                                                                        // ... other fields
                                                                    }
                                                                ];

                                                                // 提取所有唯一的sysname
                                                                const allSysnames = [
                                                                    ...new Set(
                                                                        updatedConfigurations
                                                                            .map(config => config.sysname)
                                                                            .filter(Boolean)
                                                                    )
                                                                ];

                                                                // 同步更新顶层选择器
                                                                const finalSysnames = [
                                                                    ...new Set([...currentSysnames, ...allSysnames])
                                                                ];
                                                                form.setFieldsValue({sysname: finalSysnames});
                                                            }
                                                        }}
                                                        style={{
                                                            backgroundColor: "transparent",
                                                            color: "#BFBFBF",
                                                            marginBottom: "12px",
                                                            marginTop: "29px",
                                                            width: "auto"
                                                        }}
                                                        type="link"
                                                        icon={<PlusOutlined />}
                                                    />
                                                ) : (
                                                    <Button
                                                        style={{
                                                            backgroundColor: "transparent",
                                                            color: "#BFBFBF",
                                                            marginBottom: "12px",
                                                            marginTop: index === 0 ? "40px" : "0",
                                                            width: "auto"
                                                        }}
                                                        type="link"
                                                        icon={<MinusOutlined />}
                                                        onClick={() => {
                                                            const currentValues = form.getFieldsValue();
                                                            const configToRemove =
                                                                currentValues.portConfigurations[name];

                                                            remove(name);

                                                            // 由于新的机制是在onFocus时动态计算占用状态，删除行时不需要特殊处理
                                                            // 端口状态会在下次onFocus时重新计算
                                                            console.log(
                                                                "ECN item removed, ports will be recalculated on next focus"
                                                            );

                                                            // 更新顶层选择器的逻辑保持不变
                                                            if (configToRemove && configToRemove.sysname) {
                                                                const currentConfigurations =
                                                                    form.getFieldValue("portConfigurations") || [];
                                                                const remainingSysnames = currentConfigurations
                                                                    .filter((_, idx) => idx !== name)
                                                                    .map(config => config.sysname)
                                                                    .filter(Boolean);

                                                                form.setFieldsValue({sysname: remainingSysnames});
                                                            }
                                                        }}
                                                    />
                                                )}
                                            </Col>
                                        </Row>
                                    ))}
                                </>
                            )}
                        </Form.List>
                    </>
                )}
            </>
        );
    };

    const ecnColumns = [
        createColumnConfig("Sysname", "sysname"),
        {
            ...createColumnConfig("Easy ECN", "enabled"),
            render: (_, record) => {
                return <div className="force-wrap-text">{record.enabled ? "Enabled" : "Disabled"}</div>;
            }
        },
        {
            ...createColumnConfig("ECN Mode", "mode"),
            render: text => {
                if (text === "throughput-first") {
                    return "Throughput First";
                }
                if (text === "latency-first") {
                    return "Latency First";
                }
                return "--";
            }
        },
        {
            title: "Operation",
            key: "operation",
            render: (_, record) => (
                <Space size="large" className="actionLink">
                    <a onClick={() => handleECNConfigEdit(record)}>Edit</a>
                    <a
                        onClick={() =>
                            confirmModalAction("Are you sure you want to delete the configuration items?", () =>
                                handleDeleteConfirm(record)
                            )
                        }
                    >
                        Delete
                    </a>
                </Space>
            )
        }
    ];

    return (
        <div>
            <h3>
                ECN Configuration
                <Tooltip
                    title="Configure ECN for end-to-end notification of network congestion without dropping packets, and make dynamic adjustments to ECN thresholds as needed to respond to changing network conditions."
                    placement="right"
                >
                    <QuestionCircleOutlined style={{color: "#999", marginLeft: 4, cursor: "pointer", fontSize: 14}} />
                </Tooltip>
            </h3>
            <div style={{marginBottom: 4}}>
                <Button type="primary" onClick={handleCreate}>
                    <Icon component={addSvg} />
                    Configuration
                </Button>
            </div>
            <PaginationTable columns={ecnColumns} fetchAPIInfo={getEcnConfigList} ref={tableRef} bordered />

            <AmpConCustomModalForm
                title={editRecord ? "Edit ECN Configuration" : "Create ECN Configuration"}
                isModalOpen={isModalVisible}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 4
                    }
                }}
                CustomFormItems={formItems}
                onCancel={handleModalCancel}
                onSubmit={handleSubmit}
                modalClass="ampcon-max-modal"
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button key="cancel" onClick={handleModalCancel}>
                        Cancel
                    </Button>,
                    <Button key="ok" type="primary" onClick={form.submit}>
                        Apply
                    </Button>
                ]}
            />
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
        </div>
    );
};

export default ECNConfig;
