import React, {useState} from "react";
import {Table} from "antd";
import Icon from "@ant-design/icons";

import ExpandIcon from "@/modules-ampcon/components/expand_icon";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import shrinkHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink_hover.svg?react";
import shrinkSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink.svg?react";
import unfoldHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold_hover.svg?react";
import unfoldSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold.svg?react";

export const ExtendedTable = React.forwardRef((props, ref) => {
    const [expandedKeys, setExpandedKeys] = useState([]);
    const [total, setTotal] = useState(0);

    // 包装原始的 fetchAPIInfo 函数
    const wrappedFetchAPI = async (...args) => {
        const result = await props.fetchAPIInfo(...args);
        if (result?.status === 200) {
            // // 格式化数据
            // if (result.data) {
            //     result.data = formatData(result.data);
            // }

            // 删除原本子项
            result.data.map(item => {
                item.record = item.children;
                delete item.children;
            });
            const keys = result.data.filter(item => item.record?.length > 0).map(item => item.id);
            setExpandedKeys(keys);
            setTotal(result.total || 0);
        }
        return result;
    };

    // 处理展开的Table Columns
    const columns = props.columns
        .filter(column => column.key !== "expand" && column.title !== "Sysname")
        .map(column => {
            return {
                ...column,
                filterDropdown: null
            };
        });
    const expandedRowRender = record => {
        return (
            <div>
                <Table
                    style={{
                        paddingLeft: 33,
                        paddingRight: 30,
                        paddingBottom: 12,
                        paddingTop: 12,
                        backgroundColor: "#f7f7f7"
                    }}
                    columns={columns}
                    searchFieldsList={props.searchFieldsList || null}
                    matchFieldsList={props.matchFieldsList || null}
                    dataSource={record.record || null}
                    bordered
                    pagination={false}
                    components={{
                        header: {
                            cell: props => (
                                <th
                                    {...props}
                                    style={{
                                        ...props.style,
                                        backgroundColor: "#FAFAFA"
                                    }}
                                />
                            )
                        },
                        body: {
                            cell: props => (
                                <td
                                    {...props}
                                    style={{
                                        ...props.style,
                                        backgroundColor: "#FFFFFF"
                                    }}
                                />
                            )
                        }
                    }}
                />
            </div>
        );
    };

    const expandIcon = props => {
        const {expanded, onExpand, record} = props;

        // if (expanded) {
        //     if (record.children && record.children.length > 0) {
        //         return <MinusCircleTwoTone onClick={e => onExpand(record, e)} />;
        //     }
        //     return <MinusCircleTwoTone onClick={e => onExpand(record, e)} disabled />;
        // }
        // if (record.children && record.children.length > 0) {
        //     return <PlusCircleTwoTone onClick={e => onExpand(record, e)} />;
        // }
        // return <PlusCircleTwoTone onClick={e => onExpand(record, e)} disabled />;

        if (record.record && record.record.length > 0) {
            return <ExpandIcon {...props} />;
        }
        if (record.record && record.record.length === 0) {
            return (
                <span style={{cursor: "not-allowed", display: "inline-flex", alignItems: "center"}}>
                    <Icon component={shrinkSvg} />
                </span>
            );
        }
    };

    // 处理 expandable 属性
    const expandableConfig = {
        ...props.expandable,
        expandedRowKeys: expandedKeys,
        onExpandedRowsChange: keys => {
            setExpandedKeys(keys);
            props.expandable?.onExpandedRowsChange?.(keys);
        },
        expandedRowRender,
        expandIcon
    };

    // 处理分页属性
    const paginationConfig = {
        ...props.pagination,
        total,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: total => {
            return `Total ${total} items`;
        }
    };

    return (
        <AmpConCustomTable
            {...props}
            fetchAPIInfo={wrappedFetchAPI}
            expandable={expandableConfig}
            isShowPagination
            pagination={paginationConfig}
            ref={ref}
        />
    );
});

export const PaginationTable = React.forwardRef((props, ref) => {
    const [total, setTotal] = useState(0);

    // 包装原始的 fetchAPIInfo 函数
    const wrappedFetchAPI = async (...args) => {
        const result = await props.fetchAPIInfo(...args);
        if (result?.status === 200) {
            setTotal(result.total || 0);
        }
        return result;
    };

    // 处理分页属性
    const paginationConfig = {
        ...props.pagination,
        total,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: total => {
            return `Total ${total} items`;
        }
    };

    return (
        <AmpConCustomTable
            {...props}
            fetchAPIInfo={wrappedFetchAPI}
            isShowPagination
            pagination={paginationConfig}
            ref={ref}
        />
    );
});
