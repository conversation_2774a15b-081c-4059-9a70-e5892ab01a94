/* CustomTreeSelect 自定义样式 */
.custom-tree-select-dropdown {
    /* 隐藏横向滚动条 */
    .ant-select-tree-list-holder-inner {
        overflow-x: hidden !important;
    }

    /* 确保树节点内容不换行并显示省略号 */
    .ant-select-tree-node-content-wrapper {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
    }

    /* 隐藏整个树组件的横向滚动条 */
    .ant-select-tree {
        overflow-x: hidden !important;
    }

    /* 确保下拉框容器也不显示横向滚动条 */
    .ant-select-tree-list-holder {
        overflow-x: hidden !important;
    }
}

/* 全局样式，确保所有 TreeSelect 都不显示横向滚动条 */
.ant-select-dropdown .ant-select-tree-list-holder-inner {
    overflow-x: hidden !important;
}

.ant-select-dropdown .ant-select-tree {
    overflow-x: hidden !important;
}

.ant-select-dropdown .ant-select-tree-list-holder {
    overflow-y: visible !important;
    overflow-x: hidden !important;
    text-overflow: ellipsis !important;
}

.ant-select-tree-switcher {
    margin-inline-end: 6px !important;
}

.ant-select-dropdown {
    overflow-y: hidden !important;
}

/* 隐藏默认滚动条 */
.ant-select-tree-list-scrollbar.ant-select-tree-list-scrollbar-vertical {
    visibility: hidden !important;
}

/* TreeSelect 多选模式溢出控制 */
.custom-tree-select-multiple .ant-select-selection-overflow {
    flex-wrap: nowrap;
    overflow: hidden;
}

.ant-select-tree-node-content-wrapper {
    overflow: hidden;
    display: flex;
    align-items: center;
}

.ant-select-tree-title {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}
