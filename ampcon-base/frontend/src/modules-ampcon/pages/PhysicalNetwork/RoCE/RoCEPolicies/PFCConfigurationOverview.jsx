import React, {useEffect, useState, useRef} from "react";
import {Tooltip} from "antd";
import {QuestionCircleOutlined} from "@ant-design/icons";
import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {getPfcConfigList} from "@/modules-ampcon/apis/roce_api";
import CustomQuestionIcon from "./QuestionIcon";
import {ExtendedTable} from "./ext_table";
import {renderArrayColumn} from "./utils";

// PFC Configuration Overview 组件
const PFCConfigurationOverview = () => {
    // 添加表格引用
    const tableRef = useRef(null);

    const searchFieldsList = ["sysname", "port"];
    const matchFieldsList = [
        {name: "sysname", matchMode: "fuzzy"},
        {name: "port", matchMode: "fuzzy"}
    ];

    // 修改表格列配置，保持与PFCConfiguration一致
    const pfcColumns = [
        // {
        //     title: "",
        //     key: "expand",
        //     width: 50,
        //     render: (_, record) => {}
        // },
        {
            ...createColumnConfig("Sysname", "sysname", TableFilterDropdown),
            ellipsis: true
        },
        // createColumnConfig("PFC Profile", "profile_name"),
        {
            ...createColumnConfig("Ports", "port", TableFilterDropdown),
            sorter: (a, b) => a.port.join("").localeCompare(b.port.join("")),
            render: (_, record) => {
                if (record.is_all_ports) {
                    return "All Ports";
                }
                return renderArrayColumn(record.port);
            }
        },
        {
            ...createColumnConfig("Queues", "queue"),
            sorter: (a, b) => a.queue.join("").localeCompare(b.queue.join("")),
            render: (_, record) => {
                if (record.is_all_queues) {
                    return "All Queues";
                }
                return renderArrayColumn(record.queue);
            }
        },
        {
            ...createColumnConfig("PFC Enabled", "enabled"),
            sorter: (a, b) => a.enabled - b.enabled,
            render: (_, record) => {
                return <div>{record.enabled ? "Disabled" : "Enabled"}</div>;
            }
        }
    ];

    return (
        <div>
            <h3 style={{marginBottom: 4}}>
                PFC Information
                <Tooltip
                    title="Configure Priority Flow Control (PFC) to manage congestion on Ethernet networks. 
                        PFC helps maintain performance and reliability for critical applications."
                    placement="right"
                >
                    <QuestionCircleOutlined style={{color: "#999", marginLeft: 4, cursor: "pointer", fontSize: 14}} />
                </Tooltip>
            </h3>
            <ExtendedTable
                columns={pfcColumns}
                fetchAPIInfo={getPfcConfigList} // 直接使用API函数
                // loading={loading}
                // isShowPagination={false}
                ref={tableRef}
                bordered
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
            />
        </div>
    );
};

export default PFCConfigurationOverview;
