import React, {useState, useEffect, useRef} from "react";
import {
    <PERSON>ton,
    Tooltip,
    Form,
    Space,
    Row,
    Col,
    Select,
    Input,
    TreeSelect,
    Modal,
    Divider,
    message,
    InputNumber,
    Spin
} from "antd";
import Icon, {PlusOutlined, MinusOutlined, QuestionCircleOutlined} from "@ant-design/icons";
import {
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown,
    AmpConCustomModalForm
} from "@/modules-ampcon/components/custom_table";
import {
    getPfcWdConfigList,
    getFabricSwitches,
    getSwitchPorts,
    savePfcWdConfig,
    updatePfcWdConfig,
    deletePfcWdConfig,
    getFilterSwitchPorts,
    getPfcWdConfigDetailBySwitch,
    getFilterSwitchQueues
} from "@/modules-ampcon/apis/roce_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import CustomQuestionIcon from "./QuestionIcon";
import CustomTreeSelect from "./customTreeSelect";
import {ExtendedTable} from "./ext_table";
import style from "./roce_policies.module.scss";
import {fetchFabricSwitchList, renderArrayColumn} from "./utils";
import {addSvg} from "@/utils/common/iconSvg";
import {AmpConTreeSelect} from "@/modules-ampcon/components/custom_tree";
import {showCustomErrorMessage} from "@/modules-ampcon/pages/PhysicalNetwork/RoCE/RoCEEasydeploy/custom_message";

const PFCWDConfiguration = () => {
    const [isWatchdogModalVisible, setIsWatchdogModalVisible] = useState(false);
    const [watchdogForm] = Form.useForm();
    const [fabricTreeData, setFabricTreeData] = useState([]);
    const [portTreeData, setPortTreeData] = useState([]);
    const [portExpandedKeys, setPortExpandedKeys] = useState([]);
    const [portDataCache, setPortDataCache] = useState(new Map());
    const [isEditMode, setIsEditMode] = useState(false);
    const [currentSwitchSn, setCurrentSwitchSn] = useState(null);
    // 添加editPortData状态，参考PFCConfiguration
    const [editPortData, setEditPortData] = useState([]);

    const tableRef = useRef(null);
    const [isShowSpin, setIsShowSpin] = useState(false);

    const queueLists = ["0", "1", "2", "3", "4", "5", "6", "7"];

    const searchFieldsList = ["sysname", "port"];
    const matchFieldsList = [
        {name: "sysname", matchMode: "fuzzy"},
        {name: "port", matchMode: "fuzzy"}
    ];

    // 需要在相同SN配置间保持一致的全局字段 // Fields that need to be consistent across same SN configurations
    const globalSyncFields = ["granularity", "restore_action", "threshold_period", "threshold_count"];
    // const pfcEnabledOptions = ["True", "False"];

    // 同步相同SN配置的全局字段 // Sync global fields for same SN configurations
    const syncGlobalFieldsForSameSN = (changedFieldName, changedValue, currentRowName) => {
        if (!globalSyncFields.includes(changedFieldName)) {
            return;
        }

        const currentFormValues = watchdogForm.getFieldsValue();
        if (!currentFormValues.configurations) {
            return;
        }

        // 获取当前行的sysname来确定SN // Get current row's sysname to determine SN
        const currentSysname = watchdogForm.getFieldValue(["configurations", currentRowName, "sysname"]);
        if (!currentSysname) {
            return;
        }

        let currentSN = null;
        try {
            const sysObj = JSON.parse(currentSysname);
            currentSN = sysObj.sn;
        } catch (e) {
            console.error("Error parsing sysname:", e);
            return;
        }

        if (!currentSN) {
            return;
        }

        // 更新相同SN的所有配置行 // Update all configuration rows with same SN
        const updatedConfigurations = currentFormValues.configurations.map((config, index) => {
            if (index === currentRowName) {
                return config; // 跳过当前行，避免重复更新 // Skip current row to avoid duplicate update
            }

            if (!config.sysname) {
                return config;
            }

            let configSN = null;
            try {
                const configSysObj = JSON.parse(config.sysname);
                configSN = configSysObj.sn;
            } catch (e) {
                console.error("Error parsing config sysname:", e);
                return config;
            }

            // 如果是相同SN，则更新该字段 // If same SN, update the field
            if (configSN === currentSN) {
                return {
                    ...config,
                    [changedFieldName]: changedValue
                };
            }

            return config;
        });

        // 批量更新表单字段 // Batch update form fields
        watchdogForm.setFieldsValue({
            configurations: updatedConfigurations
        });
    };

    const fetchFabricTreeData = async () => {
        const tree = await fetchFabricSwitchList("PfcWdConfiguration");
        if (tree) {
            setFabricTreeData(tree);
        }
    };

    // 初始化端口树数据，默认所有端口都为enabled状态
    const initPortTreeData = portData => {
        console.log("PFCWD initPortTreeData - portData:", portData);

        const allPortsForDisplay = portData.map(item => ({
            title: item.port_name,
            value: item.port_name,
            key: item.port_name,
            disabled: false // 初始化时所有端口都可用
        }));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.port_name)),
                disabled: false, // 初始化时All Ports也可用
                children: allPortsForDisplay
            }
        ];

        setPortTreeData(tree);
        setPortExpandedKeys([tree[0].value, ...tree[0].children.map(child => child.key)]);
    };

    const initQueueTreeData = queueData => {
        const allQueuesForDisplay = queueData.map(item => ({
            title: item.queue_name,
            value: item.queue_name,
            key: item.queue_name,
            disabled: false
        }));

        const tree = [
            {
                title: "All Queues",
                value: JSON.stringify(queueData.map(item => item.queue_name)),
                disabled: false,
                children: allQueuesForDisplay
            }
        ];

        setQueueTreeData(tree);
    };

    // 处理端口选择器获得焦点时的逻辑
    const handlePortFocus = name => {
        const currentSysname = watchdogForm.getFieldValue(["configurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.sn) {
                    const portData = portDataCache.get(sysObj.sn);
                    if (portData) {
                        // 收集当前sn下非当前行占用的端口
                        const occupiedPorts = new Set();
                        const currentFormValues = watchdogForm.getFieldsValue();

                        if (currentFormValues.configurations) {
                            currentFormValues.configurations.forEach((config, configIndex) => {
                                // 跳过当前行
                                if (configIndex === name) return;

                                if (config.sysname && config.port) {
                                    try {
                                        const configSysObj = JSON.parse(config.sysname);
                                        // 只处理相同sn的配置
                                        if (configSysObj.sn === sysObj.sn) {
                                            let selectedPorts = [];
                                            if (Array.isArray(config.port) && config.port.length > 0) {
                                                const allPortsValue = JSON.stringify(
                                                    portData.map(item => item.port_name)
                                                );
                                                if (config.port[0] === allPortsValue) {
                                                    selectedPorts = JSON.parse(config.port[0]);
                                                } else {
                                                    selectedPorts = config.port;
                                                }
                                            }
                                            selectedPorts.forEach(port => occupiedPorts.add(port));
                                        }
                                    } catch (e) {
                                        console.error("Error parsing config sysname:", e);
                                    }
                                }
                            });
                        }

                        // 构建新的树形数据，将占用的端口置为disabled
                        const allPortsForDisplay = portData.map(item => ({
                            title: item.port_name,
                            value: item.port_name,
                            key: item.port_name,
                            disabled: occupiedPorts.has(item.port_name)
                        }));

                        // 如果有任意端口被占用，All Ports也置为disabled
                        const hasAnyPortOccupied = occupiedPorts.size > 0;

                        const tree = [
                            {
                                title: "All Ports",
                                value: JSON.stringify(portData.map(item => item.port_name)),
                                disabled: hasAnyPortOccupied,
                                children: allPortsForDisplay
                            }
                        ];

                        setPortTreeData(tree);
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    // 处理端口选择器失去焦点时的逻辑
    const handlePortBlur = name => {
        const currentSysname = watchdogForm.getFieldValue(["configurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.sn) {
                    const portData = portDataCache.get(sysObj.sn);
                    if (portData) {
                        // onBlur时将所有option置为enable
                        const allPortsForDisplay = portData.map(item => ({
                            title: item.port_name,
                            value: item.port_name,
                            key: item.port_name,
                            disabled: false
                        }));

                        const tree = [
                            {
                                title: "All Ports",
                                value: JSON.stringify(portData.map(item => item.port_name)),
                                disabled: false,
                                children: allPortsForDisplay
                            }
                        ];

                        setPortTreeData(tree);
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    const [queueDataCache, setQueueDataCache] = useState(new Map());
    const [queueTreeData, setQueueTreeData] = useState([]);

    const handleQueueFocus = name => {
        const currentSysname = watchdogForm.getFieldValue(["configurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.sn) {
                    const queueData = queueDataCache.get(sysObj.sn);
                    if (queueData) {
                        // 收集当前sn下非当前行占用的端口
                        const occupiedQueues = new Set();
                        const currentFormValues = watchdogForm.getFieldsValue();

                        if (currentFormValues.configurations) {
                            currentFormValues.configurations.forEach((config, configIndex) => {
                                // 跳过当前行
                                if (configIndex === name) return;

                                if (config.sysname && config.queue) {
                                    try {
                                        const configSysObj = JSON.parse(config.sysname);
                                        // 只处理相同sn的配置
                                        if (configSysObj.sn === sysObj.sn) {
                                            let selectedQueues = [];
                                            if (Array.isArray(config.queue) && config.queue.length > 0) {
                                                const allQueuesValue = JSON.stringify(
                                                    queueData.map(item => item.queue_name)
                                                );
                                                if (config.queue[0] === allQueuesValue) {
                                                    selectedQueues = JSON.parse(config.queue[0]);
                                                } else {
                                                    selectedQueues = config.queue;
                                                }
                                            }
                                            selectedQueues.forEach(queue => occupiedQueues.add(queue));
                                        }
                                    } catch (e) {
                                        console.error("Error parsing config sysname:", e);
                                    }
                                }
                            });
                        }

                        const allQueuesForDisplay = queueData.map(item => ({
                            title: item.queue_name,
                            value: item.queue_name,
                            key: item.queue_name,
                            disabled: occupiedQueues.has(item.queue_name)
                        }));

                        const hasAnyQueueOccupied = occupiedQueues.size > 0;

                        const tree = [
                            {
                                title: "All Queues",
                                value: JSON.stringify(queueData.map(item => item.queue_name)),
                                disabled: hasAnyQueueOccupied,
                                children: allQueuesForDisplay
                            }
                        ];

                        setQueueTreeData(tree);
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    const handleQueueBlur = name => {
        const currentSysname = watchdogForm.getFieldValue(["configurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.sn) {
                    const queueData = queueDataCache.get(sysObj.sn);
                    if (queueData) {
                        // onBlur时将所有option置为enable
                        const allQueuesForDisplay = queueData.map(item => ({
                            title: item.queue_name,
                            value: item.queue_name,
                            key: item.queue_name,
                            disabled: false
                        }));

                        const tree = [
                            {
                                title: "All Queues",
                                value: JSON.stringify(queueData.map(item => item.queue_name)),
                                disabled: false,
                                children: allQueuesForDisplay
                            }
                        ];

                        setQueueTreeData(tree);
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    const fetchPortsBySn = async (sn, forceRefresh = false) => {
        try {
            // 检查缓存
            let portData = portDataCache.get(sn);

            // 如果缓存中没有数据或者强制刷新，则调用接口
            if (!portData || forceRefresh) {
                const res = await getFilterSwitchPorts({
                    switch_sn: sn,
                    query_model: "PfcWdConfiguration"
                });

                if (res && Array.isArray(res.data)) {
                    portData = res.data;
                    // 更新缓存
                    setPortDataCache(prev => new Map(prev).set(sn, portData));
                } else {
                    setPortTreeData([]);
                    return;
                }
            }

            // 使用新的初始化函数
            initPortTreeData(portData);
        } catch (error) {
            console.error("Failed to fetch filtered ports:", error);
            message.error("Failed to fetch ports");
            setPortTreeData([]);
        }
    };

    const fetchQueuesBySn = async (sn, forceRefresh = false) => {
        try {
            // 检查缓存
            let queueData = queueDataCache.get(sn);

            // 如果缓存中没有数据或者强制刷新，则调用接口
            if (!queueData || forceRefresh) {
                const res = await getFilterSwitchQueues({
                    switch_sn: sn,
                    query_model: "PfcWdConfiguration"
                });

                if (res && Array.isArray(res.data)) {
                    queueData = res.data.map(item => ({
                        queue_name: item.queue_name,
                        enabled: item.enabled
                    }));
                    // 更新缓存
                    setQueueDataCache(prev => new Map(prev).set(sn, queueData));
                } else {
                    setQueueTreeData([]);
                    return;
                }
            }

            // 初始化队列状态
            initQueueTreeData(queueData);
        } catch (error) {
            console.error("Failed to fetch PFC WD queues:", error);
            message.error("Failed to fetch queues");
            setQueueTreeData([]);
        }
    };

    // 修改编辑处理函数 - 根据switch_sn加载数据
    const handleWatchdogEdit = async record => {
        try {
            setIsEditMode(true);
            setCurrentSwitchSn(record.switch_sn);
            setIsWatchdogModalVisible(true);

            await fetchPortsBySn(record.switch_sn, true);
            await fetchQueuesBySn(record.switch_sn, true);

            // 根据 switch_sn 查询该交换机下所有的配置
            const response = await getPfcWdConfigDetailBySwitch({
                switch_sn: record.switch_sn
            });

            let defaultSysname = null;
            let {sysname} = record;
            let {switch_sn} = record;

            if (response.status === 200 && response.data) {
                const configs = response.data;

                if (configs && configs.length > 0) {
                    console.log("PFC WD configs:", configs);
                    const firstConfig = configs[0];
                    console.log("First PFC WD config:", firstConfig);
                    sysname = firstConfig.sysname;
                    switch_sn = firstConfig.switch_sn;
                }

                defaultSysname = JSON.stringify({sn: switch_sn, sysname});
                console.log("PFC WD defaultSysname:", defaultSysname);

                setFabricTreeData([
                    {
                        title: record.fabric,
                        value: record.fabric,
                        key: record.fabric,
                        children: [
                            {
                                title: `${sysname} ${switch_sn}`,
                                value: defaultSysname,
                                key: defaultSysname
                            }
                        ]
                    }
                ]);

                // 将配置数据转换为表单格式
                const formConfigurations = configs.map(config => {
                    return {
                        config_id: config.id,
                        sysname: defaultSysname,
                        port: config.is_all_ports ? [JSON.stringify(config.port)] : config.port,
                        queue: config.is_all_queues ? [JSON.stringify(config.queue)] : config.queue,
                        enable: config.enabled ? "True" : "False",
                        granularity: config.granularity,
                        restore_mode: config.restore_mode,
                        restore_action: config.restore_action,
                        detection_interval: config.detection_interval,
                        restore_interval: config.restore_interval,
                        threshold_period: config.threshold_period,
                        threshold_count: config.threshold_count,
                        is_all_ports: config.is_all_ports,
                        is_all_queues: config.is_all_queues
                    };
                });

                watchdogForm.setFieldsValue({
                    configurations: formConfigurations
                });
            } else {
                message.warning("Failed to load PFC WD configurations");
                setIsWatchdogModalVisible(false);
                setIsEditMode(false);
                setCurrentSwitchSn(null);
            }
        } catch (error) {
            console.error("Error loading PFC WD configurations:", error);
            message.error("Failed to load configurations");
            setIsWatchdogModalVisible(false);
            setIsEditMode(false);
            setCurrentSwitchSn(null);
        }
    };

    // 简化取消处理函数
    const handleWatchdogModalCancel = () => {
        setIsWatchdogModalVisible(false);
        setIsEditMode(false);
        setCurrentSwitchSn(null);
        watchdogForm.resetFields();
        setPortTreeData([]);
        setQueueTreeData([]);
        setEditPortData([]);
    };

    const defaultValue = {
        enable: "True",
        granularity: 100,
        restore_mode: "auto",
        restore_action: "forward",
        detection_interval: 15,
        restore_interval: 15,
        threshold_period: 20,
        threshold_count: 30
    };

    // 简化创建按钮的处理函数
    const handleWatchdogCreate = () => {
        setIsEditMode(false);
        setCurrentSwitchSn(null);
        setIsWatchdogModalVisible(true);
        watchdogForm.resetFields();
        setPortTreeData([]);
        setQueueTreeData([]);
        setEditPortData([]);

        fetchFabricTreeData();

        watchdogForm.setFieldsValue({
            configurations: [
                {
                    sysname: null,
                    port: [],
                    queue: [],
                    ...defaultValue,
                    is_all_ports: false,
                    is_all_queues: false
                }
            ]
        });
    };

    // 保存
    const handleWatchdogSubmit = async values => {
        try {
            const configurationsArray = Array.isArray(values.configurations) ? values.configurations : [];

            const configs = configurationsArray.map(config => {
                let sysObj = {};
                try {
                    sysObj = JSON.parse(config.sysname);
                } catch (e) {
                    console.error("Failed to parse sysname:", e);
                    return;
                }

                // 获取对应交换机的端口数据来判断是否是全选
                const portData = portDataCache.get(sysObj.sn);
                const allPortsValue = portData ? JSON.stringify(portData.map(item => item.port_name)) : null;
                const allQueuesValue = JSON.stringify(queueLists);

                // 计算 is_all_ports - 严格匹配全选逻辑
                // 只有当选择数组长度为1且第一个元素是所有端口的JSON字符串时才认为是全选
                // 额外检查：确保不是单个端口被误判为全选
                let is_all_ports = false;
                if (config.port && config.port.length === 1 && allPortsValue !== null) {
                    const selectedValue = config.port[0];
                    // 检查是否是JSON字符串格式（全选时的格式）
                    try {
                        const parsedValue = JSON.parse(selectedValue);
                        // 如果能解析为数组，且与所有端口匹配，才认为是全选
                        if (Array.isArray(parsedValue) && selectedValue === allPortsValue) {
                            is_all_ports = true;
                        }
                    } catch (e) {
                        // 如果不能解析为JSON，说明是单个端口选择，不是全选
                        is_all_ports = false;
                    }
                }

                // 计算 is_all_queues - 使用相同的安全检查逻辑
                let is_all_queues = false;
                if (config.queue && config.queue.length === 1) {
                    const selectedValue = config.queue[0];
                    try {
                        const parsedValue = JSON.parse(selectedValue);
                        // 如果能解析为数组，且与所有队列匹配，才认为是全选
                        if (Array.isArray(parsedValue) && selectedValue === allQueuesValue) {
                            is_all_queues = true;
                        }
                    } catch (e) {
                        // 如果不能解析为JSON，说明是单个队列选择，不是全选
                        is_all_queues = false;
                    }
                }

                let ports = [];
                let queues = [];

                if (is_all_ports) {
                    ports = JSON.parse(config.port[0]);
                } else {
                    ports = config.port;
                }

                if (is_all_queues) {
                    queues = JSON.parse(config.queue[0]);
                } else {
                    queues = config.queue;
                }

                const configData = {
                    sysname: sysObj.sysname,
                    switch_sn: sysObj.sn,
                    port: ports,
                    queue: queues,
                    enabled: config.enable === "True",
                    granularity: config.granularity,
                    restore_mode: config.restore_mode,
                    restore_action: config.restore_action,
                    detection_interval: config.detection_interval,
                    restore_interval: config.restore_interval,
                    threshold_period: config.threshold_period,
                    threshold_count: config.threshold_count,
                    is_all_ports,
                    is_all_queues
                };

                // 如果是编辑模式，添加config_id
                if (config.config_id) {
                    configData.config_id = config.config_id;
                }

                return configData;
            });

            const params = {configurations: configs.filter(Boolean)};

            // 编辑模式下需要传递 switch_sn 参数
            if (isEditMode && currentSwitchSn) {
                params.switch_sn = currentSwitchSn;
            }
            setIsShowSpin(true);
            // 根据编辑模式选择不同的API函数
            const ret = isEditMode ? await updatePfcWdConfig(params) : await savePfcWdConfig(params);
            setIsShowSpin(false);

            if (ret.status === 200) {
                message.success(ret.msg);
                setIsWatchdogModalVisible(false);
                setIsEditMode(false);
                setCurrentSwitchSn(null);
                watchdogForm.resetFields();
                setPortTreeData([]);
                setQueueTreeData([]);
                setEditPortData([]);
                tableRef.current.refreshTable();
            } else {
                // 处理多分段错误 (Handle multi-segment errors)
                if (ret.data && Array.isArray(ret.data) && ret.data.length > 0) {
                    showCustomErrorMessage(ret.msg, ret.data, 6);
                } else {
                    message.error(ret.msg);
                }

                setIsWatchdogModalVisible(false);
                setIsEditMode(false);
                setCurrentSwitchSn(null);
                watchdogForm.resetFields();
                setPortTreeData([]);
                setQueueTreeData([]);
                setEditPortData([]);

                tableRef.current.refreshTable();
            }
        } catch (error) {
            console.error("Error saving PFC WD configuration:", error);
            message.error("保存失败");
        }
    };

    // 删除确认
    const handleDelete = record => {
        confirmModalAction("Are you sure you want to delete the configuration items?", async () => {
            try {
                setIsShowSpin(true);
                const ret = await deletePfcWdConfig({config_id: record.id});
                setIsShowSpin(false);
                if (ret.status === 200) {
                    message.success(ret.msg);
                    tableRef.current.refreshTable();
                } else {
                    // 处理多分段错误 (Handle multi-segment errors)
                    if (ret.data && Array.isArray(ret.data) && ret.data.length > 0) {
                        showCustomErrorMessage(ret.msg, ret.data, 6);
                    } else {
                        message.error(ret.msg);
                    }
                    tableRef.current.refreshTable();
                }
            } catch (error) {
                console.error("Error deleting PFC WD configuration:", error);
                message.error("Delete Failed");
            }
        });
    };

    const releasePortsFromRemovedRow = ({formInstance, name, portDataCache, portTreeData}) => {
        try {
            // 由于新的机制是在onFocus时动态计算占用状态，删除行时不需要特殊处理
            // 端口状态会在下次onFocus时重新计算
            console.log("PFCWD item removed, ports will be recalculated on next focus");
        } catch (e) {
            console.error("Error releasing ports on row delete:", e);
        }
    };

    // 表单项
    const watchdogFormItems = () => (
        <Form.List name="configurations">
            {(fields, {add, remove}) => (
                <>
                    {fields.map(({key, name}, index) => (
                        <div key={key}>
                            {index !== 0 && (
                                <Divider style={{marginTop: "0px !important", marginLeft: 0, width: "100%"}} />
                            )}
                            <Row gutter={8}>
                                <Col span={3}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "sysname"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    Sysname <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[{required: true, message: "Please select sysname!"}]}
                                    >
                                        <AmpConTreeSelect
                                            onChange={value => {
                                                if (value) {
                                                    let obj = {};
                                                    try {
                                                        obj = JSON.parse(value);
                                                    } catch (e) {
                                                        console.error("Failed to parse sysname:", e);
                                                        return;
                                                    }
                                                    if (obj.sn) {
                                                        // 每次选择sysname都调用接口，强制刷新端口数据
                                                        fetchPortsBySn(obj.sn, true);
                                                        fetchQueuesBySn(obj.sn, true);
                                                    }
                                                    const currentFormValues =
                                                        watchdogForm.getFieldsValue().configurations;
                                                    const firstValueObj = currentFormValues.find(
                                                        obj => obj.sysname === value
                                                    );
                                                    watchdogForm.setFieldsValue({
                                                        configurations: {
                                                            [name]: {
                                                                granularity: firstValueObj.granularity,
                                                                threshold_period: firstValueObj.threshold_period,
                                                                threshold_count: firstValueObj.threshold_count,
                                                                restore_action: firstValueObj.restore_action
                                                            }
                                                        }
                                                    });
                                                } else {
                                                    // 如果清空sysname，也清空端口树数据
                                                    setPortTreeData([]);
                                                    setQueueTreeData([]);
                                                }

                                                watchdogForm.setFields([
                                                    {
                                                        name: ["configurations", name, "port"],
                                                        value: []
                                                    },
                                                    {
                                                        name: ["configurations", name, "queue"],
                                                        value: []
                                                    }
                                                ]);
                                            }}
                                            treeData={fabricTreeData}
                                            placeholder="Sysname"
                                            disabled={isEditMode}
                                            treeDefaultExpandAll
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={3}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "port"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    Ports <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[{required: true, message: "Please select port!"}]}
                                        validateTrigger={["onChange", "onBlur"]}
                                    >
                                        <CustomTreeSelect
                                            popupClassName="custom-popup"
                                            treeData={
                                                watchdogForm.getFieldValue(["configurations", name, "sysname"])
                                                    ? portTreeData
                                                    : []
                                            }
                                            treeExpandedKeys={portExpandedKeys}
                                            onTreeExpand={keys => setPortExpandedKeys(keys)}
                                            placeholder="Ports"
                                            onFocus={() => handlePortFocus(name)}
                                            onBlur={() => handlePortBlur(name)}
                                            onChange={(value, label) => {
                                                setEditPortData(value);
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={3}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "queue"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    Queues <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[{required: true, message: "Please select queue!"}]}
                                        validateTrigger={["onChange", "onBlur"]}
                                    >
                                        <CustomTreeSelect
                                            popupClassName="custom-popup"
                                            treeData={
                                                watchdogForm.getFieldValue(["configurations", name, "sysname"])
                                                    ? queueTreeData
                                                    : []
                                            }
                                            placeholder="Queues"
                                            onFocus={() => handleQueueFocus(name)}
                                            onBlur={() => handleQueueBlur(name)}
                                            onChange={(value, label) => {
                                                watchdogForm.setFieldsValue({
                                                    configurations: {
                                                        [name]: {
                                                            queue: value
                                                        }
                                                    }
                                                });
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={3}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "enable"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    Enabled <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[{required: true, message: "Please select enable status!"}]}
                                    >
                                        <Select placeholder="Enable" style={{width: "100%"}}>
                                            <Select.Option value="True">Enabled</Select.Option>
                                            <Select.Option value="False">Disabled</Select.Option>
                                        </Select>
                                    </Form.Item>
                                </Col>
                                <Col span={3}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "granularity"]}
                                        label={index === 0 ? "Granularity (ms)" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                    >
                                        <Select
                                            placeholder="Granularity"
                                            style={{width: "100%"}}
                                            onChange={value => {
                                                syncGlobalFieldsForSameSN("granularity", value, name);
                                            }}
                                            allowClear
                                        >
                                            <Select.Option value={10}>10</Select.Option>
                                            <Select.Option value={100}>100</Select.Option>
                                        </Select>
                                    </Form.Item>
                                </Col>
                                <Col span={3}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "detection_interval"]}
                                        label={index === 0 ? "Detect Interval (ms)" : ""}
                                        // labelCol={{span: 24}}
                                        labelCol={{
                                            span: 24,
                                            style: {
                                                whiteSpace: "nowrap",
                                                overflow: "hidden",
                                                textOverflow: "ellipsis"
                                            }
                                        }}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please input detection interval!"}]}
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (value === undefined || value === null || value === "") {
                                                        return Promise.resolve();
                                                    }
                                                    if (value < 1 || value > 15) {
                                                        return Promise.reject(
                                                            new Error("Detection Interval must between 1 to 15")
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                }
                                            }
                                        ]}
                                    >
                                        <InputNumber
                                            placeholder="1-15, def: 15"
                                            controls={false}
                                            style={{width: "100%"}}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={3}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "restore_interval"]}
                                        label={index === 0 ? "Restore Interval (ms)" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please input restore interval!"}]}
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (value === undefined || value === null || value === "") {
                                                        return Promise.resolve();
                                                    }
                                                    if (value < 1 || value > 15) {
                                                        return Promise.reject(
                                                            new Error("Restore Interval must between 1 to 15")
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                }
                                            }
                                        ]}
                                    >
                                        <InputNumber
                                            placeholder="1-15, def: 15"
                                            controls={false}
                                            style={{width: "100%"}}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={3}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "threshold_period"]}
                                        label={index === 0 ? "Threshold Period (s)" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please input threshold period!"}]}
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (value === undefined || value === null || value === "") {
                                                        return Promise.resolve();
                                                    }
                                                    if (value < 1 || value > 60) {
                                                        return Promise.reject(
                                                            new Error("Threshold Period must between 1 to 60")
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                }
                                            }
                                        ]}
                                    >
                                        <InputNumber
                                            placeholder="1-60, def: 20"
                                            controls={false}
                                            style={{width: "100%"}}
                                            onChange={value => {
                                                syncGlobalFieldsForSameSN("threshold_period", value, name);
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={3}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "threshold_count"]}
                                        label={index === 0 ? "Threshold Count" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please input threshold count!"}]}
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (value === undefined || value === null || value === "") {
                                                        return Promise.resolve();
                                                    }
                                                    if (value < 1 || value > 500) {
                                                        return Promise.reject(
                                                            new Error("Threshold Count must between 1 to 500")
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                }
                                            }
                                        ]}
                                    >
                                        <InputNumber
                                            placeholder="1-500, def: 30"
                                            controls={false}
                                            style={{width: "100%"}}
                                            onChange={value => {
                                                syncGlobalFieldsForSameSN("threshold_count", value, name);
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={3}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "restore_action"]}
                                        label={index === 0 ? <span>Restore Action</span> : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please select restore action!"}]}
                                    >
                                        <Select
                                            placeholder="Restore Action"
                                            style={{width: "100%"}}
                                            onChange={value => {
                                                syncGlobalFieldsForSameSN("restore_action", value, name);
                                            }}
                                            allowClear
                                        >
                                            <Select.Option value="forward">Forward</Select.Option>
                                            <Select.Option value="drop">Drop</Select.Option>
                                        </Select>
                                    </Form.Item>
                                </Col>
                                <Col span={3}>
                                    <Form.Item
                                        className={style.formItem}
                                        name={[name, "restore_mode"]}
                                        label={index === 0 ? "Restore Mode" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}

                                        // rules={[{required: true, message: "Please select restore mode!"}]}
                                    >
                                        <Select placeholder="Restore Mode" style={{width: "100%"}} allowClear>
                                            <Select.Option value="manual">Manual</Select.Option>
                                            <Select.Option value="auto">Auto</Select.Option>
                                        </Select>
                                    </Form.Item>
                                </Col>
                                <Col>
                                    {index === 0 ? (
                                        <Button
                                            onClick={() => {
                                                if (isEditMode === false) {
                                                    setPortTreeData([]);
                                                    setQueueTreeData([]);
                                                    add({
                                                        ...defaultValue
                                                    });
                                                } else {
                                                    const currentSysname = watchdogForm.getFieldValue([
                                                        "configurations",
                                                        name,
                                                        "sysname"
                                                    ]);

                                                    // 获取同一SN现有配置的全局字段值 // Get global field values from existing configurations with same SN
                                                    const globalFieldValues = {};
                                                    if (currentSysname) {
                                                        try {
                                                            const sysObj = JSON.parse(currentSysname);
                                                            const currentFormValues = watchdogForm.getFieldsValue();

                                                            if (currentFormValues.configurations) {
                                                                const existingConfig =
                                                                    currentFormValues.configurations.find(config => {
                                                                        if (!config.sysname) return false;
                                                                        try {
                                                                            const configSysObj = JSON.parse(
                                                                                config.sysname
                                                                            );
                                                                            return configSysObj.sn === sysObj.sn;
                                                                        } catch (e) {
                                                                            return false;
                                                                        }
                                                                    });

                                                                if (existingConfig) {
                                                                    globalSyncFields.forEach(field => {
                                                                        if (existingConfig[field] !== undefined) {
                                                                            globalFieldValues[field] =
                                                                                existingConfig[field];
                                                                        }
                                                                    });
                                                                }
                                                            }
                                                        } catch (e) {
                                                            console.error("Error parsing sysname:", e);
                                                        }
                                                    }

                                                    add({
                                                        sysname: currentSysname,
                                                        ...defaultValue,
                                                        ...globalFieldValues // 使用现有的全局字段值 // Use existing global field values
                                                    });
                                                }
                                            }}
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "12px",
                                                marginTop: "29px",
                                                width: "auto"
                                            }}
                                            type="link"
                                            icon={<PlusOutlined />}
                                        />
                                    ) : (
                                        <Button
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "12px",
                                                marginTop: index === 0 ? "40px" : "0",
                                                width: "auto"
                                            }}
                                            type="link"
                                            icon={<MinusOutlined />}
                                            onClick={() => {
                                                remove(name);
                                            }}
                                        />
                                    )}
                                </Col>
                            </Row>
                            <Form.Item name={[name, "is_all_ports"]} hidden initialValue={false}>
                                <Input type="hidden" />
                            </Form.Item>
                            <Form.Item name={[name, "is_all_queues"]} hidden initialValue={false}>
                                <Input type="hidden" />
                            </Form.Item>
                            <Form.Item name={[name, "config_id"]} hidden>
                                <Input type="hidden" />
                            </Form.Item>
                        </div>
                    ))}
                </>
            )}
        </Form.List>
    );

    // 修改表格列定义
    const watchdogColumns = [
        // {
        //     title: "",
        //     key: "expand",
        //     width: 50,
        //     render: (_, record) => {}
        // },
        {
            ...createColumnConfig("Sysname", "sysname", TableFilterDropdown),
            ellipsis: true
        },
        {
            ...createColumnConfig("Ports", "port", TableFilterDropdown),
            sorter: (a, b) => a.port.join("").localeCompare(b.port.join("")),
            render: (_, record) => {
                if (record.is_all_ports) {
                    return "All Ports";
                }
                return renderArrayColumn(record.port);
            }
        },
        {
            ...createColumnConfig("Queues", "queue"),
            sorter: (a, b) => a.queue.join("").localeCompare(b.queue.join("")),
            render: (_, record) => {
                if (record.is_all_queues) {
                    return "All Queues";
                }
                return renderArrayColumn(record.queue);
            }
        },
        {
            ...createColumnConfig("PFC Watchdog Enabled", "enabled"),
            sorter: (a, b) => a.enabled - b.enabled,
            render: (_, record) => {
                return <div>{record.enabled ? "Enabled" : "Disabled"}</div>;
            }
        },
        {
            ...createColumnConfig("Granularity (ms)", "granularity"),
            sorter: (a, b) => a.granularity - b.granularity,
            render: (_, record) => {
                return <div>{record.granularity || "--"}</div>;
            }
        },
        {
            ...createColumnConfig("Restore Action", "restore_action"),
            sorter: (a, b) => a.restore_action.localeCompare(b.restore_action),
            render: (_, record) => {
                return <div>{record.restore_action || "--"}</div>;
            }
        },
        {
            ...createColumnConfig("Restore Mode", "restore_mode"),
            sorter: (a, b) => a.restore_mode.localeCompare(b.restore_mode),
            render: (_, record) => {
                return <div>{record.restore_mode || "--"}</div>;
            }
        },
        {
            title: "Operation",
            key: "operation",
            render: (_, record) => (
                <Space size="large" className="actionLink">
                    <a onClick={() => handleWatchdogEdit(record)}>Edit</a>
                    <a onClick={() => handleDelete(record)}>Delete</a>
                </Space>
            )
        }
    ];

    return (
        <div style={{marginBottom: 30}}>
            <h3>
                PFC WatchDog Configuration
                <Tooltip
                    title="Enable PFC watchdog to detect and resolve PFC deadlocks. PFC watchdog monitors the duration of PFC pause frames and takes corrective actions if a potential deadlock is detected."
                    placement="right"
                >
                    <QuestionCircleOutlined style={{color: "#999", marginLeft: 4, cursor: "pointer", fontSize: 14}} />
                </Tooltip>
            </h3>
            <ExtendedTable
                columns={watchdogColumns}
                fetchAPIInfo={getPfcWdConfigList}
                ref={tableRef}
                bordered
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={
                    <div style={{marginBottom: 4}}>
                        <Button type="primary" onClick={handleWatchdogCreate}>
                            <Icon component={addSvg} />
                            Configuration
                        </Button>
                    </div>
                }
            />

            <AmpConCustomModalForm
                title={isEditMode ? "Edit PFC WatchDog Configuration" : "Create PFC WatchDog Configuration"}
                isModalOpen={isWatchdogModalVisible}
                formInstance={watchdogForm}
                layoutProps={{
                    labelCol: {
                        span: 4
                    }
                }}
                CustomFormItems={watchdogFormItems}
                onCancel={handleWatchdogModalCancel}
                onSubmit={handleWatchdogSubmit}
                modalClass="ampcon-max-modal"
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button key="cancel" onClick={handleWatchdogModalCancel}>
                        Cancel
                    </Button>,
                    <Button key="ok" type="primary" onClick={watchdogForm.submit}>
                        Apply
                    </Button>
                ]}
            />
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
        </div>
    );
};

export default PFCWDConfiguration;
