import React, {useState, useEffect, useRef} from "react";
import {
    <PERSON>ton,
    Tooltip,
    Form,
    Space,
    Row,
    Col,
    Select,
    Input,
    TreeSelect,
    message,
    Divider,
    InputNumber,
    Spin
} from "antd";
import Icon, {PlusOutlined, MinusOutlined, QuestionCircleOutlined} from "@ant-design/icons";
import CustomTreeSelect from "./customTreeSelect";
import {
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown,
    AmpConCustomModalForm
} from "@/modules-ampcon/components/custom_table";
import {
    getFabricSwitches,
    getSwitchPorts,
    getFilterSwitchPorts,
    getPfcBufferTrafficConfigList,
    savePfcBufferConfig,
    updatePfcBufferConfig,
    deletePfcBufferConfig,
    getPfcBufferConfigDetailBySwitch
} from "@/modules-ampcon/apis/roce_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import CustomQuestionIcon from "./QuestionIcon";
import {ExtendedTable} from "./ext_table";
import {fetchFabricSwitchList, renderArrayColumn} from "./utils";
import {addSvg} from "@/utils/common/iconSvg";
import {AmpConTreeSelect} from "@/modules-ampcon/components/custom_tree";
import {showCustomErrorMessage} from "@/modules-ampcon/pages/PhysicalNetwork/RoCE/RoCEEasydeploy/custom_message";

import style from "./roce_policies.module.scss";

const PFCBufferConfiguration = () => {
    const [isBufferModalVisible, setIsBufferModalVisible] = useState(false);
    const [bufferForm] = Form.useForm();

    const [headerPortTreeData, setHeaderPortTreeData] = useState([]); // 表头的端口选项数据

    // 模态框的状态
    const [fabricTreeData, setFabricTreeData] = useState([]); // 模态框的交换机树形数据
    const [headerFabricTreeData, setHeaderFabricTreeData] = useState([]); // 表头的交换机树形数据
    // 添加统一的端口树数据状态，参考PFCConfiguration
    const [ingressPortTreeData, setIngressPortTreeData] = useState([]);
    const [egressPortTreeData, setEgressPortTreeData] = useState([]);
    const [ingressExpandedKeys, setIngressExpandedKeys] = useState([]);
    const [egressExpandedKeys, setEgressExpandedKeys] = useState([]);
    const [editPortData, setEditPortData] = useState([]);

    const [trafficType, setTrafficType] = useState("Ingress Queue");
    const [forceUpdate, setForceUpdate] = useState(0);

    // 为两种类型的表格分别创建 ref
    const ingressTableRef = useRef(null);
    const egressTableRef = useRef(null);

    // 在组件开头添加状态变量来区分是创建还是编辑模式
    const [isEditMode, setIsEditMode] = useState(false);

    const queueList = Array.from({length: 8}, (_, i) => i);

    // 为表头选择器添加专门的状态
    const [selectHeaderSysname, setSelectHeaderSysname] = useState(null);
    const [selectHeaderPort, setSelectHeaderPort] = useState([]);

    const matchFieldsList = [
        {name: "sysname", matchMode: "fuzzy"},
        {name: "port", matchMode: "fuzzy"}
    ];

    // 添加端口数据缓存，参考SchedulingConfig
    const [portDataCache, setPortDataCache] = useState(new Map());

    const [isShowSpin, setIsShowSpin] = useState(false);

    // 表头 sysname 变化处理函数
    const handleHeaderSysnameChange = value => {
        setSelectHeaderSysname(value);
        // 清空端口选择
        setSelectHeaderPort([]);
    };

    // 表头 port 变化处理函数
    const handleHeaderPortChange = (value, label) => {
        let headerPorts = [];
        if (label[0] === "All Ports") {
            // 如果选择了 "All Ports"，解析其值获取所有端口
            headerPorts = JSON.parse(value);
        } else {
            headerPorts = value;
        }
        setSelectHeaderPort(headerPorts);
    };

    // 监听表头 sysname 变化
    useEffect(() => {
        const updateHeaderPorts = async () => {
            if (selectHeaderSysname) {
                try {
                    const parsed = JSON.parse(selectHeaderSysname);
                    if (parsed.sn) {
                        const portTree = await fetchHeaderPorts(parsed.sn);
                        setHeaderPortTreeData(portTree);
                    }
                } catch (error) {
                    setHeaderPortTreeData([]);
                }
            } else {
                setHeaderPortTreeData([]);
                setSelectHeaderPort([]);
            }
        };
        updateHeaderPorts();
    }, [selectHeaderSysname]);

    // 监听 trafficType 变化，更新 currentTableRef
    useEffect(() => {
        // 延迟执行，确保状态更新完成
        const timer = setTimeout(() => {
            if (trafficType === "Ingress Queue") {
                if (ingressTableRef.current) {
                    ingressTableRef.current.refreshTable();
                }
            } else if (egressTableRef.current) {
                egressTableRef.current.refreshTable();
            }
        }, 100); // 延迟100ms确保DOM更新完成

        return () => clearTimeout(timer);
    }, [trafficType, selectHeaderPort, forceUpdate]);

    // 修改获取端口的函数，使用普通的getSwitchPorts（不过滤）
    const fetchHeaderPorts = async switchSn => {
        if (switchSn) {
            try {
                const res = await getSwitchPorts({switch_sn: switchSn});
                if (res && Array.isArray(res.data)) {
                    const tree = [
                        {
                            title: "All Ports",
                            value: JSON.stringify(res.data.map(item => item.port_name)),
                            children: res.data.map(item => ({
                                title: item.port_name,
                                value: item.port_name,
                                key: item.port_name
                            }))
                        }
                    ];
                    return tree;
                }
            } catch (error) {
                message.error("Failed to get ports");
            }
        }
        return [];
    };

    // 初始化端口树数据，默认所有端口都为enabled状态
    const initPortTreeData = (portData, configType) => {
        console.log("initPortTreeData - configType:", configType, "portData:", portData);

        const allPortsForDisplay = portData.map(item => ({
            title: item.port_name,
            value: item.port_name,
            key: item.port_name,
            disabled: false // 初始化时所有端口都可用
        }));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.port_name)),
                disabled: false, // 初始化时All Ports也可用
                children: allPortsForDisplay
            }
        ];

        if (configType === "ingress") {
            setIngressPortTreeData(tree);
            setIngressExpandedKeys([tree[0].value, ...tree[0].children.map(child => child.key)]);
        } else {
            setEgressPortTreeData(tree);
            setEgressExpandedKeys([tree[0].value, ...tree[0].children.map(child => child.key)]);
        }
    };

    // 处理Ingress端口选择器获得焦点时的逻辑
    const handleIngressPortFocus = name => {
        const currentSysname = bufferForm.getFieldValue(["ingressConfigurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.sn) {
                    const cacheKey = `${sysObj.sn}_PfcBufferIngressConfiguration`;
                    const portData = portDataCache.get(cacheKey);
                    if (portData) {
                        // 收集当前sn下非当前行占用的端口
                        const occupiedPorts = new Set();
                        const currentFormValues = bufferForm.getFieldsValue();

                        if (currentFormValues.ingressConfigurations) {
                            currentFormValues.ingressConfigurations.forEach((config, configIndex) => {
                                // 跳过当前行
                                if (configIndex === name) return;

                                if (config.sysname && config.ports) {
                                    try {
                                        const configSysObj = JSON.parse(config.sysname);
                                        // 只处理相同sn的配置
                                        if (configSysObj.sn === sysObj.sn) {
                                            let selectedPorts = [];
                                            if (Array.isArray(config.ports) && config.ports.length > 0) {
                                                const allPortsValue = JSON.stringify(
                                                    portData.map(item => item.port_name)
                                                );
                                                if (config.ports[0] === allPortsValue) {
                                                    selectedPorts = JSON.parse(config.ports[0]);
                                                } else {
                                                    selectedPorts = config.ports;
                                                }
                                            }
                                            selectedPorts.forEach(port => occupiedPorts.add(port));
                                        }
                                    } catch (e) {
                                        console.error("Error parsing config sysname:", e);
                                    }
                                }
                            });
                        }

                        // 构建新的树形数据，将占用的端口置为disabled
                        const allPortsForDisplay = portData.map(item => ({
                            title: item.port_name,
                            value: item.port_name,
                            key: item.port_name,
                            disabled: occupiedPorts.has(item.port_name)
                        }));

                        // 如果有任意端口被占用，All Ports也置为disabled
                        const hasAnyPortOccupied = occupiedPorts.size > 0;

                        const tree = [
                            {
                                title: "All Ports",
                                value: JSON.stringify(portData.map(item => item.port_name)),
                                disabled: hasAnyPortOccupied,
                                children: allPortsForDisplay
                            }
                        ];

                        setIngressPortTreeData(tree);
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    // 处理Ingress端口选择器失去焦点时的逻辑
    const handleIngressPortBlur = name => {
        const currentSysname = bufferForm.getFieldValue(["ingressConfigurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.sn) {
                    const cacheKey = `${sysObj.sn}_PfcBufferIngressConfiguration`;
                    const portData = portDataCache.get(cacheKey);
                    if (portData) {
                        // onBlur时将所有option置为enable
                        const allPortsForDisplay = portData.map(item => ({
                            title: item.port_name,
                            value: item.port_name,
                            key: item.port_name,
                            disabled: false
                        }));

                        const tree = [
                            {
                                title: "All Ports",
                                value: JSON.stringify(portData.map(item => item.port_name)),
                                disabled: false,
                                children: allPortsForDisplay
                            }
                        ];

                        setIngressPortTreeData(tree);
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    // 处理Egress端口选择器获得焦点时的逻辑
    const handleEgressPortFocus = name => {
        const currentSysname = bufferForm.getFieldValue(["egressConfigurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.sn) {
                    const cacheKey = `${sysObj.sn}_PfcBufferEgressConfiguration`;
                    const portData = portDataCache.get(cacheKey);
                    if (portData) {
                        // 收集当前sn下非当前行占用的端口
                        const occupiedPorts = new Set();
                        const currentFormValues = bufferForm.getFieldsValue();

                        if (currentFormValues.egressConfigurations) {
                            currentFormValues.egressConfigurations.forEach((config, configIndex) => {
                                // 跳过当前行
                                if (configIndex === name) return;

                                if (config.sysname && config.ports) {
                                    try {
                                        const configSysObj = JSON.parse(config.sysname);
                                        // 只处理相同sn的配置
                                        if (configSysObj.sn === sysObj.sn) {
                                            let selectedPorts = [];
                                            if (Array.isArray(config.ports) && config.ports.length > 0) {
                                                const allPortsValue = JSON.stringify(
                                                    portData.map(item => item.port_name)
                                                );
                                                if (config.ports[0] === allPortsValue) {
                                                    selectedPorts = JSON.parse(config.ports[0]);
                                                } else {
                                                    selectedPorts = config.ports;
                                                }
                                            }
                                            selectedPorts.forEach(port => occupiedPorts.add(port));
                                        }
                                    } catch (e) {
                                        console.error("Error parsing config sysname:", e);
                                    }
                                }
                            });
                        }

                        // 构建新的树形数据，将占用的端口置为disabled
                        const allPortsForDisplay = portData.map(item => ({
                            title: item.port_name,
                            value: item.port_name,
                            key: item.port_name,
                            disabled: occupiedPorts.has(item.port_name)
                        }));

                        // 如果有任意端口被占用，All Ports也置为disabled
                        const hasAnyPortOccupied = occupiedPorts.size > 0;

                        const tree = [
                            {
                                title: "All Ports",
                                value: JSON.stringify(portData.map(item => item.port_name)),
                                disabled: hasAnyPortOccupied,
                                children: allPortsForDisplay
                            }
                        ];

                        setEgressPortTreeData(tree);
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    // 处理Egress端口选择器失去焦点时的逻辑
    const handleEgressPortBlur = name => {
        const currentSysname = bufferForm.getFieldValue(["egressConfigurations", name, "sysname"]);

        if (currentSysname) {
            try {
                const sysObj = JSON.parse(currentSysname);
                if (sysObj.sn) {
                    const cacheKey = `${sysObj.sn}_PfcBufferEgressConfiguration`;
                    const portData = portDataCache.get(cacheKey);
                    if (portData) {
                        // onBlur时将所有option置为enable
                        const allPortsForDisplay = portData.map(item => ({
                            title: item.port_name,
                            value: item.port_name,
                            key: item.port_name,
                            disabled: false
                        }));

                        const tree = [
                            {
                                title: "All Ports",
                                value: JSON.stringify(portData.map(item => item.port_name)),
                                disabled: false,
                                children: allPortsForDisplay
                            }
                        ];

                        setEgressPortTreeData(tree);
                    }
                }
            } catch (e) {
                console.error("Error parsing sysname:", e);
            }
        }
    };

    // 添加获取过滤端口数据的函数，参考PFCConfiguration
    const fetchFilteredPortsBySn = async (sn, configType, forceRefresh = false) => {
        try {
            // 确定query_model参数
            const queryModel =
                configType === "ingress" ? "PfcBufferIngressConfiguration" : "PfcBufferEgressConfiguration";

            // 检查缓存
            const cacheKey = `${sn}_${queryModel}`;
            let portData = portDataCache.get(cacheKey);

            // 如果缓存中没有数据或者强制刷新，则调用接口
            if (!portData || forceRefresh) {
                const res = await getFilterSwitchPorts({
                    switch_sn: sn,
                    query_model: queryModel
                });

                if (res && Array.isArray(res.data)) {
                    portData = res.data;
                    // 更新缓存
                    setPortDataCache(prev => new Map(prev).set(cacheKey, portData));
                } else {
                    if (configType === "ingress") {
                        setIngressPortTreeData([]);
                    } else {
                        setEgressPortTreeData([]);
                    }
                    return;
                }
            }

            // 初始化端口树数据
            initPortTreeData(portData, configType);
        } catch (error) {
            console.error("Failed to fetch filtered ports:", error);
            message.error("Failed to fetch ports");
            if (configType === "ingress") {
                setIngressPortTreeData([]);
            } else {
                setEgressPortTreeData([]);
            }
        }
    };

    // 修改获取配置列表的包装函数
    const getBufferIngressConfigListWrapper = async (page, pageSize, filterFields, sortFields, searchFields) => {
        const parsed = selectHeaderSysname ? JSON.parse(selectHeaderSysname) : null;
        const switchSn = parsed?.sn;

        const extraParams = {
            ...(switchSn ? {switch_sn: [switchSn]} : {}),
            traffic_type: "ingress",
            ...(selectHeaderPort.length > 0 ? {port: selectHeaderPort} : {})
        };

        return await getPfcBufferTrafficConfigList(page, pageSize, filterFields, sortFields, searchFields, extraParams);
    };

    const getBufferEgressConfigListWrapper = async (page, pageSize, filterFields, sortFields, searchFields) => {
        const parsed = selectHeaderSysname ? JSON.parse(selectHeaderSysname) : null;
        const switchSn = parsed?.sn;

        const extraParams = {
            ...(switchSn ? {switch_sn: [switchSn]} : {}),
            traffic_type: "egress"
        };

        if (selectHeaderPort && selectHeaderPort.length > 0) {
            if (selectHeaderPort.includes("all_ports")) {
                // 如果选择了 "All Ports"，不需要指定具体端口
                extraParams.port = ["all_ports"];
            } else {
                extraParams.port = selectHeaderPort;
            }
        }

        console.log("Egress extraParams:", extraParams);
        return await getPfcBufferTrafficConfigList(page, pageSize, filterFields, sortFields, searchFields, extraParams);
    };

    const fetchFabricTreeData = async () => {
        const tree = await fetchFabricSwitchList("PfcBufferIngressConfiguration");
        if (tree) {
            setFabricTreeData(tree);
        }
    };

    const fetchHeaderFabricTreeData = async () => {
        const tree = await fetchFabricSwitchList();
        if (tree) {
            setHeaderFabricTreeData(tree);
        }
    };

    // 修改删除处理函数
    const handleDeleteBufferConfig = async record => {
        setIsShowSpin(true);
        const ret = await deletePfcBufferConfig({
            config_id: record.id,
            traffic_type: trafficType === "Ingress Queue" ? "ingress" : "egress"
        });
        setIsShowSpin(false);
        if (ret.status === 200) {
            message.success(ret.msg);
            // 使用当前活动表格刷新
            if (trafficType === "Ingress Queue") {
                ingressTableRef.current.refreshTable();
            } else {
                egressTableRef.current.refreshTable();
            }
        } else {
            // 处理多分段错误 (Handle multi-segment errors)
            if (ret.data && Array.isArray(ret.data) && ret.data.length > 0) {
                showCustomErrorMessage(ret.msg, ret.data, 6);
            } else {
                message.error(ret.msg);
            }
            // 使用当前活动表格刷新
            if (trafficType === "Ingress Queue") {
                ingressTableRef.current.refreshTable();
            } else {
                egressTableRef.current.refreshTable();
            }
        }
    };

    // 初始化时获取Fabric数据
    useEffect(() => {
        fetchFabricTreeData();
        fetchHeaderFabricTreeData();
    }, []);

    // 修改编辑处理函数，根据switch_sn加载所有相关配置
    const handleBufferEdit = async record => {
        try {
            setIsEditMode(true);
            setIsBufferModalVisible(true);

            // 根据switch_sn获取该交换机下的所有PFC Buffer配置
            const result = await getPfcBufferConfigDetailBySwitch({
                switch_sn: record.switch_sn
            });

            if (result.status === 200 && result.data) {
                // 从任意一条ingress或egress配置中获取sysname和switch_sn来构建defaultSysname
                let defaultSysname = null;
                const fabricName = record.fabric;
                let {sysname} = record;
                let {switch_sn} = record;

                // 优先从ingress配置中获取
                if (result.data.ingress && result.data.ingress.length > 0) {
                    const firstIngress = result.data.ingress[0];
                    sysname = firstIngress.sysname;
                    switch_sn = firstIngress.switch_sn;
                }
                // 如果ingress没有，则从egress配置中获取
                else if (result.data.egress && result.data.egress.length > 0) {
                    const firstEgress = result.data.egress[0];
                    sysname = firstEgress.sysname;
                    switch_sn = firstEgress.switch_sn;
                }

                defaultSysname = JSON.stringify({sn: switch_sn, sysname});

                // 设置Fabric树数据
                setFabricTreeData([
                    {
                        title: fabricName,
                        value: fabricName,
                        key: fabricName,
                        children: [
                            {
                                title: `${sysname} ${switch_sn}`,
                                value: defaultSysname,
                                key: defaultSysname
                            }
                        ]
                    }
                ]);

                // 处理ingress配置
                const ingressConfigurations = result.data.ingress.map(config => {
                    let ports = [];
                    let queues = [];
                    try {
                        ports = Array.isArray(config.port) ? config.port : JSON.parse(config.port);
                        queues = Array.isArray(config.queue) ? config.queue : JSON.parse(config.queue);
                    } catch (e) {
                        console.error("Failed to parse port or queue data", e);
                        ports = config.port;
                        queues = config.queue;
                    }

                    return {
                        config_id: config.id,
                        sysname: defaultSysname,
                        ports: config.is_all_ports ? [JSON.stringify(ports)] : ports,
                        ingress_queues: config.is_all_queues ? [JSON.stringify(queues)] : queues,
                        shared_ratio: config.shared_ratio ? parseInt(config.shared_ratio) : config.shared_ratio,
                        guaranteed: config.guaranteed ? parseInt(config.guaranteed) : config.guaranteed,
                        reset_offset: config.reset_offset ? parseInt(config.reset_offset) : config.reset_offset,
                        headroom: config.headroom ? parseInt(config.headroom) : config.headroom,
                        threshold: config.threshold ? parseInt(config.threshold) : config.threshold,
                        is_all_ports: config.is_all_ports,
                        is_all_queues: config.is_all_queues
                    };
                });

                // 处理egress配置
                const egressConfigurations = result.data.egress.map(config => {
                    let ports = [];
                    let queues = [];
                    try {
                        ports = Array.isArray(config.port) ? config.port : JSON.parse(config.port);
                        queues = Array.isArray(config.queue) ? config.queue : JSON.parse(config.queue);
                    } catch (e) {
                        console.error("Failed to parse port or queue data", e);
                        ports = config.port;
                        queues = config.queue;
                    }

                    return {
                        config_id: config.id,
                        sysname: defaultSysname,
                        ports: config.is_all_ports ? [JSON.stringify(ports)] : ports,
                        egress_queues: config.is_all_queues ? [JSON.stringify(queues)] : queues,
                        shared_ratio: config.shared_ratio ? parseInt(config.shared_ratio) : config.shared_ratio,
                        threshold: config.threshold ? parseInt(config.threshold) : config.threshold,
                        is_all_ports: config.is_all_ports,
                        is_all_queues: config.is_all_queues
                    };
                });

                // 设置表单值
                bufferForm.setFieldsValue({
                    ingressConfigurations:
                        ingressConfigurations.length > 0
                            ? ingressConfigurations
                            : [{config_id: null, sysname: defaultSysname}],
                    egressConfigurations:
                        egressConfigurations.length > 0
                            ? egressConfigurations
                            : [{config_id: null, sysname: defaultSysname}]
                });

                if (ingressConfigurations) {
                    await fetchFilteredPortsBySn(switch_sn, "ingress", true);
                }
                if (egressConfigurations) {
                    await fetchFilteredPortsBySn(switch_sn, "egress", true);
                }
            } else {
                message.error(result.msg);
                setIsEditMode(false);
                setIsBufferModalVisible(false);
            }
        } catch (error) {
            console.error("Failed to load PFC Buffer configurations:", error);
            message.error("Failed to load configurations");
            setIsEditMode(false);
            setIsBufferModalVisible(false);
        }
    };

    // 修改取消处理函数
    const handleBufferModalCancel = () => {
        setIsBufferModalVisible(false);
        setIsEditMode(false);
        setIngressPortTreeData([]); // 清空端口数据
        setEgressPortTreeData([]); // 清空端口数据
        setEditPortData([]);
        bufferForm.resetFields();
    };

    const ingressDefaultValue = {
        shared_ratio: 5,
        guaranteed: 16,
        headroom: 768
    };

    // 修改创建按钮的处理函数
    const handleBufferCreate = () => {
        setIsEditMode(false);
        setIsBufferModalVisible(true);
        setIngressPortTreeData([]); // 清空端口数据
        setEgressPortTreeData([]); // 清空端口数据
        setEditPortData([]);
        fetchFabricTreeData();

        // 根据当前选中的 trafficType 设置不同的初始值
        bufferForm.setFieldsValue({
            ingressConfigurations: [
                {
                    ...ingressDefaultValue
                }
            ],
            egressConfigurations: [
                {
                    shared_ratio: 5
                }
            ]
        });
    };

    // 修改表格的操作列
    const getOperationColumn = () => ({
        title: "Operation",
        key: "operation",
        render: (_, record) => (
            <Space size="large" className="actionLink">
                <a onClick={() => handleBufferEdit(record)}>Edit</a>
                <a
                    onClick={() =>
                        confirmModalAction("Are you sure you want to delete the configuration items?", () =>
                            handleDeleteBufferConfig(record)
                        )
                    }
                >
                    Delete
                </a>
            </Space>
        )
    });

    // 修改保存提交函数，参考SchedulingConfig的处理方式
    const handleBufferSubmit = async values => {
        try {
            const configurations = [];
            let switch_sn = "";

            // 处理 ingressConfigurations
            (values.ingressConfigurations || []).forEach(item => {
                let sysObj = {};
                try {
                    sysObj = JSON.parse(item.sysname);
                } catch (e) {
                    console.error("Failed to parse sysname:", e);
                    return;
                }

                // 编辑模式下：如果 sysname 不为空但其他字段都为空，则忽略这条数据
                if (!sysObj.sysname || !sysObj.sn) return;
                if (isEditMode) {
                    if (
                        !item.ports?.length &&
                        !item.ingress_queues?.length &&
                        !item.shared_ratio &&
                        !item.threshold &&
                        !item.guaranteed &&
                        !item.reset_offset &&
                        !item.headroom
                    ) {
                        return; // 忽略这条数据，不添加到 configurations 中
                    }
                }

                // 参考SchedulingConfig: 根据实际值动态计算is_all_ports和is_all_queues
                const portData = portDataCache.get(`${sysObj.sn}_PfcBufferIngressConfiguration`);
                const allPortsValue = portData ? JSON.stringify(portData.map(port => port.port_name)) : null;
                const allQueuesValue = JSON.stringify(queueList);

                // 计算is_all_ports - 根据实际值判断
                const is_all_ports = item.ports && item.ports.length === 1 && item.ports[0] === allPortsValue;

                // 计算is_all_queues - 根据实际值判断
                const is_all_queues =
                    item.ingress_queues &&
                    item.ingress_queues.length === 1 &&
                    item.ingress_queues[0] === allQueuesValue;

                let ports = [];
                let queues = [];

                if (is_all_ports) {
                    ports = JSON.parse(item.ports[0]);
                } else {
                    ports = item.ports || [];
                }

                if (is_all_queues) {
                    queues = JSON.parse(item.ingress_queues[0]);
                } else {
                    queues = item.ingress_queues || [];
                }

                switch_sn = sysObj.sn;

                configurations.push({
                    config_id: item.config_id,
                    sysname: sysObj.sysname,
                    switch_sn: sysObj.sn,
                    traffic_type: "ingress",
                    port: ports,
                    queue: queues,
                    is_all_ports, // 动态计算
                    is_all_queues, // 动态计算
                    shared_ratio: item.shared_ratio,
                    threshold: item.threshold,
                    guaranteed: item.guaranteed,
                    reset_offset: item.reset_offset,
                    headroom: item.headroom
                });
            });

            // 处理 egressConfigurations
            (values.egressConfigurations || []).forEach(item => {
                let sysObj = {};
                try {
                    sysObj = JSON.parse(item.sysname);
                } catch (e) {
                    console.error("Failed to parse sysname:", e);
                    return;
                }

                // 编辑模式下：如果 sysname 不为空但其他字段都为空，则忽略这条数据
                if (!sysObj.sysname || !sysObj.sn) return;
                if (isEditMode) {
                    if (!item.ports?.length && !item.egress_queues?.length && !item.shared_ratio && !item.threshold) {
                        return; // 忽略这条数据，不添加到 configurations 中
                    }
                }

                // 参考SchedulingConfig: 根据实际值动态计算is_all_ports和is_all_queues
                const portData = portDataCache.get(`${sysObj.sn}_PfcBufferEgressConfiguration`);
                const allPortsValue = portData ? JSON.stringify(portData.map(port => port.port_name)) : null;
                const allQueuesValue = JSON.stringify(queueList);

                // 计算is_all_ports - 根据实际值判断
                const is_all_ports = item.ports && item.ports.length === 1 && item.ports[0] === allPortsValue;

                // 计算is_all_queues - 根据实际值判断
                const is_all_queues =
                    item.egress_queues && item.egress_queues.length === 1 && item.egress_queues[0] === allQueuesValue;

                let ports = [];
                let queues = [];

                if (is_all_ports) {
                    ports = JSON.parse(item.ports[0]);
                } else {
                    ports = item.ports || [];
                }

                if (is_all_queues) {
                    queues = JSON.parse(item.egress_queues[0]);
                } else {
                    queues = item.egress_queues || [];
                }

                switch_sn = sysObj.sn;

                configurations.push({
                    config_id: item.config_id,
                    sysname: sysObj.sysname,
                    switch_sn: sysObj.sn,
                    traffic_type: "egress",
                    port: ports,
                    queue: queues,
                    is_all_ports, // 动态计算
                    is_all_queues, // 动态计算
                    shared_ratio: item.shared_ratio,
                    threshold: item.threshold
                });
            });

            // 根据是否处于编辑模式来判断调用哪个接口
            let res;
            setIsShowSpin(true);
            if (isEditMode) {
                // 编辑模式：使用 update 接口
                res = await updatePfcBufferConfig({switch_sn, configurations});
            } else {
                // 创建模式：使用 save 接口
                res = await savePfcBufferConfig({configurations});
            }
            setIsShowSpin(false);
            if (res.status === 200) {
                message.success(res.msg);
                setIsBufferModalVisible(false);
                setIngressPortTreeData([]); // 清空端口数据
                setEgressPortTreeData([]); // 清空端口数据
                setEditPortData([]);
                bufferForm.resetFields();

                // 根据流量类型刷新对应的表格
                if (trafficType === "Ingress Queue") {
                    ingressTableRef.current.refreshTable();
                } else {
                    egressTableRef.current.refreshTable();
                }
            } else {
                // 处理多分段错误 (Handle multi-segment errors)
                if (res.data && Array.isArray(res.data) && res.data.length > 0) {
                    showCustomErrorMessage(res.msg, res.data, 6);
                } else {
                    message.error(res.msg);
                }
                setIsBufferModalVisible(false);
                setIngressPortTreeData([]); // 清空端口数据
                setEgressPortTreeData([]); // 清空端口数据
                setEditPortData([]);
                bufferForm.resetFields();

                // 根据流量类型刷新对应的表格
                if (trafficType === "Ingress Queue") {
                    ingressTableRef.current.refreshTable();
                } else {
                    egressTableRef.current.refreshTable();
                }
            }
        } catch (e) {
            console.error("Submit error:", e);
            message.error("Failed to save configuration");
        }
        // finally {

        // }
    };

    // 修改表格列定义
    const bufferColumnsIngress = [
        // {
        //     title: "",
        //     key: "expand",
        //     width: 50,
        //     render: (_, record) => {}
        // },
        {
            ...createColumnConfig("Sysname", "sysname", TableFilterDropdown)
        },
        {
            ...createColumnConfig("Ports", "port", TableFilterDropdown),
            ellipsis: true,
            sorter: (a, b) => a.port.join("").localeCompare(b.port.join("")),
            render: (_, record) => {
                if (record.is_all_ports) {
                    return "All Ports";
                }
                return renderArrayColumn(record.port);
            }
        },
        {
            ...createColumnConfig("Ingress Queues", "queue"),
            ellipsis: true,
            sorter: (a, b) => a.queue.join("").localeCompare(b.queue.join("")),
            render: (_, record) => {
                if (record.is_all_queues) {
                    return "All Queues";
                }
                return renderArrayColumn(record.queue);
            }
        },
        {
            ...createColumnConfig("Shared Ratio (%)", "shared_ratio"),
            sorter: (a, b) => a.shared_ratio - b.shared_ratio,
            render: (_, record) => {
                return <span>{record.shared_ratio || "--"}</span>;
            }
        },
        {
            ...createColumnConfig("Guaranteed", "guaranteed"),
            sorter: (a, b) => a.guaranteed - b.guaranteed,
            render: (_, record) => {
                return <span>{record.guaranteed || "--"}</span>;
            }
        },
        {
            ...createColumnConfig("Reset Offset", "reset_offset"),
            sorter: (a, b) => a.reset_offset - b.reset_offset,
            render: (_, record) => {
                return <span>{record.reset_offset || "--"}</span>;
            }
        },
        {
            ...createColumnConfig("Headroom", "headroom"),
            sorter: (a, b) => a.headroom - b.headroom,
            render: (_, record) => {
                return <span>{record.headroom || "--"}</span>;
            }
        },
        {
            ...createColumnConfig("Threshold", "threshold"),
            sorter: (a, b) => a.threshold - b.threshold,
            render: (_, record) => {
                return <span>{record.threshold || "--"}</span>;
            }
        },
        getOperationColumn()
    ];

    const bufferColumnsEgress = [
        // {
        //     title: "",
        //     key: "expand",
        //     width: 50,
        //     render: (_, record) => {}
        // },
        {
            ...createColumnConfig("Sysname", "sysname", TableFilterDropdown),
            ellipsis: true
        },
        {
            ...createColumnConfig("Ports", "port", TableFilterDropdown),
            sorter: (a, b) => a.port.join("").localeCompare(b.port.join("")),
            render: (_, record) => {
                if (record.is_all_ports) {
                    return "All Ports";
                }
                return renderArrayColumn(record.port);
            }
        },
        {
            ...createColumnConfig("Egress Queues", "queue"),
            sorter: (a, b) => a.queue.join("").localeCompare(b.queue.join("")),
            render: (_, record) => {
                if (record.is_all_queues) {
                    return "All Queues";
                }
                return renderArrayColumn(record.queue);
            }
        },
        {
            ...createColumnConfig("Shared Ratio (%)", "shared_ratio"),
            sorter: (a, b) => a.shared_ratio - b.shared_ratio,
            render: (_, record) => {
                return <span>{record.shared_ratio || "--"}</span>;
            }
        },
        {
            ...createColumnConfig("Threshold", "threshold"),
            sorter: (a, b) => a.threshold - b.threshold,
            render: (_, record) => {
                return <span>{record.threshold || "--"}</span>;
            }
        },
        getOperationColumn()
    ];

    // 表单项
    const bufferFormItems = () => (
        <Form.Item
            noStyle
            shouldUpdate
            rules={[
                {
                    validator: async (_, value) => {
                        const values = bufferForm.getFieldsValue();

                        // 编辑模式下：允许 sysname 不为空但其他字段为空的情况
                        if (isEditMode) {
                            // 检查是否有有效的数据需要提交
                            const hasValidIngressData = values.ingressConfigurations?.some(
                                item =>
                                    item.sysname &&
                                    (item.ports?.length ||
                                        item.ingress_queues?.length ||
                                        item.shared_ratio ||
                                        item.threshold ||
                                        item.guaranteed ||
                                        item.reset_offset ||
                                        item.headroom)
                            );
                            const hasValidEgressData = values.egressConfigurations?.some(
                                item =>
                                    item.sysname &&
                                    (item.ports?.length ||
                                        item.egress_queues?.length ||
                                        item.shared_ratio ||
                                        item.threshold)
                            );

                            // 编辑模式下，至少需要有一个有效的配置项
                            if (!hasValidIngressData && !hasValidEgressData) {
                                return Promise.reject(
                                    new Error("Please complete at least one valid configuration item")
                                );
                            }
                        } else {
                            // 创建模式下：保持原有逻辑
                            const hasIngressData = values.ingressConfigurations?.some(
                                item => item.sysname && item.ports?.length && item.ingress_queues?.length
                            );
                            const hasEgressData = values.egressConfigurations?.some(
                                item => item.sysname && item.ports?.length && item.egress_queues?.length
                            );

                            if (!hasIngressData && !hasEgressData) {
                                return Promise.reject(
                                    new Error("Please complete either Ingress or Egress configuration")
                                );
                            }
                        }
                        return Promise.resolve();
                    }
                }
            ]}
        >
            {/* Ingress Buffer Configuration */}
            <div
                style={{
                    fontSize: "18px",
                    fontWeight: "bold",
                    borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
                    paddingBottom: "10px",
                    marginBottom: "20px"
                }}
            >
                Ingress Buffer Configuration
            </div>
            <Form.List name="ingressConfigurations" initialValue={[{}]}>
                {(fields, {add, remove}) => (
                    <>
                        {fields.map(({key, name}, index) => (
                            <Row key={key} gutter={[8, 16]}>
                                <Col span={23}>
                                    <Row gutter={8}>
                                        {/* Sysname */}
                                        <Col span={3}>
                                            <Form.Item
                                                className={style.formItem}
                                                name={[name, "sysname"]}
                                                label={
                                                    index === 0 ? (
                                                        <>
                                                            Sysname <span className={style.requiredIcon1}>*</span>
                                                        </>
                                                    ) : (
                                                        ""
                                                    )
                                                }
                                                labelCol={{span: 24}}
                                                wrapperCol={{span: 24}}
                                                rules={[
                                                    // {required: true, message: "Please select sysname!"},
                                                    {
                                                        validator: async (_, value) => {
                                                            // 如果其他字段有值，则 sysname 必填
                                                            const currentConfig = bufferForm.getFieldValue([
                                                                "ingressConfigurations",
                                                                name
                                                            ]);
                                                            if (
                                                                currentConfig.ports?.length ||
                                                                currentConfig.ingress_queues?.length
                                                            ) {
                                                                if (!value) {
                                                                    return Promise.reject(
                                                                        new Error("Please select sysname!")
                                                                    );
                                                                }
                                                            }
                                                            return Promise.resolve();
                                                        }
                                                    }
                                                ]}
                                            >
                                                <AmpConTreeSelect
                                                    onChange={value => handleModalSysnameChange(value, name, "ingress")}
                                                    treeData={fabricTreeData}
                                                    placeholder="Sysname"
                                                    disabled={isEditMode}
                                                    treeDefaultExpandAll
                                                />
                                            </Form.Item>
                                        </Col>
                                        {/* Ports */}
                                        <Col span={3}>
                                            <Form.Item
                                                className={style.formItem}
                                                name={[name, "ports"]}
                                                label={
                                                    index === 0 ? (
                                                        <>
                                                            Ports <span className={style.requiredIcon1}>*</span>
                                                        </>
                                                    ) : (
                                                        ""
                                                    )
                                                }
                                                labelCol={{span: 24}}
                                                wrapperCol={{span: 24}}
                                                validateTrigger={["onChange", "onBlur"]}
                                                rules={[
                                                    // {required: true, message: "Please select ports!"},
                                                    {
                                                        validator: async (_, value) => {
                                                            const currentConfig = bufferForm.getFieldValue([
                                                                "ingressConfigurations",
                                                                name
                                                            ]);
                                                            if (currentConfig.sysname) {
                                                                // 编辑模式下：检查是否除了 sysname 之外的所有字段都为空
                                                                if (isEditMode) {
                                                                    const hasOtherFields =
                                                                        currentConfig.ingress_queues?.length ||
                                                                        currentConfig.shared_ratio ||
                                                                        currentConfig.threshold ||
                                                                        currentConfig.guaranteed ||
                                                                        currentConfig.reset_offset ||
                                                                        currentConfig.headroom;

                                                                    console.log("hasOtherFields", hasOtherFields);
                                                                    console.log("value", value);
                                                                    // 如果其他字段有值，则 ports 必填
                                                                    if (hasOtherFields && !value?.length) {
                                                                        return Promise.reject(
                                                                            new Error("Please select ports!")
                                                                        );
                                                                    }
                                                                    // 如果其他字段都为空，则允许 ports 为空
                                                                    return Promise.resolve();
                                                                }
                                                                // 创建模式下：ports 必填
                                                                if (!value?.length) {
                                                                    return Promise.reject(
                                                                        new Error("Please select ports!")
                                                                    );
                                                                }
                                                            }
                                                            return Promise.resolve();
                                                        }
                                                    }
                                                ]}
                                            >
                                                <CustomTreeSelect
                                                    popupClassName="custom-popup"
                                                    style={{width: "100%"}}
                                                    treeData={
                                                        bufferForm.getFieldValue([
                                                            "ingressConfigurations",
                                                            name,
                                                            "sysname"
                                                        ])
                                                            ? ingressPortTreeData
                                                            : []
                                                    }
                                                    treeExpandedKeys={ingressExpandedKeys}
                                                    onTreeExpand={keys => setIngressExpandedKeys(keys)}
                                                    placeholder="Ports"
                                                    onFocus={() => handleIngressPortFocus(name)}
                                                    onBlur={() => handleIngressPortBlur(name)}
                                                    onChange={(value, label) => {
                                                        console.log("ingress value", value);
                                                        setEditPortData(value);
                                                    }}
                                                />
                                            </Form.Item>
                                        </Col>
                                        {/* Ingress Queues */}
                                        <Col span={3}>
                                            <Form.Item
                                                className={style.formItem}
                                                name={[name, "ingress_queues"]}
                                                label={
                                                    index === 0 ? (
                                                        <>
                                                            Ingress Queues{" "}
                                                            <span className={style.requiredIcon1}>*</span>
                                                        </>
                                                    ) : (
                                                        ""
                                                    )
                                                }
                                                labelCol={{span: 24}}
                                                wrapperCol={{span: 24}}
                                                validateTrigger={["onChange", "onBlur"]}
                                                rules={[
                                                    // {required: true, message: "Please select ports!"},
                                                    {
                                                        validator: async (_, value) => {
                                                            const currentConfig = bufferForm.getFieldValue([
                                                                "ingressConfigurations",
                                                                name
                                                            ]);
                                                            if (currentConfig.sysname) {
                                                                // 编辑模式下：检查是否除了 sysname 之外的所有字段都为空
                                                                if (isEditMode) {
                                                                    const hasOtherFields =
                                                                        currentConfig.ports?.length ||
                                                                        currentConfig.shared_ratio ||
                                                                        currentConfig.threshold ||
                                                                        currentConfig.guaranteed ||
                                                                        currentConfig.reset_offset ||
                                                                        currentConfig.headroom;

                                                                    // 如果其他字段有值，则 ingress_queues 必填
                                                                    if (hasOtherFields && !value?.length) {
                                                                        return Promise.reject(
                                                                            new Error("Please select queues!")
                                                                        );
                                                                    }
                                                                    // 如果其他字段都为空，则允许 ingress_queues 为空
                                                                    return Promise.resolve();
                                                                }
                                                                // 创建模式下：ingress_queues 必填
                                                                if (!value?.length) {
                                                                    return Promise.reject(
                                                                        new Error("Please select queues!")
                                                                    );
                                                                }
                                                            }
                                                            return Promise.resolve();
                                                        }
                                                    }
                                                ]}
                                            >
                                                <CustomTreeSelect
                                                    popupClassName="custom-popup"
                                                    treeData={[
                                                        {
                                                            title: "All Queues",
                                                            value: JSON.stringify(queueList),
                                                            children: queueList.map(queue => ({
                                                                title: queue,
                                                                value: queue,
                                                                key: queue
                                                            }))
                                                        }
                                                    ]}
                                                    placeholder="Ingress Queues"
                                                    onChange={(value, label) => {
                                                        // 简化：只设置实际的队列值，不设置is_all_queues
                                                        bufferForm.setFieldValue(
                                                            ["ingressConfigurations", name, "ingress_queues"],
                                                            value
                                                        );
                                                    }}
                                                />
                                            </Form.Item>
                                        </Col>

                                        <Form.Item
                                            shouldUpdate={(prev, curr) =>
                                                prev?.ingressConfigurations?.[name]?.shared_ratio !==
                                                    curr?.ingressConfigurations?.[name]?.shared_ratio ||
                                                prev?.ingressConfigurations?.[name]?.threshold !==
                                                    curr?.ingressConfigurations?.[name]?.threshold ||
                                                prev?.ingressConfigurations?.[name]?.reset_offset !==
                                                    curr?.ingressConfigurations?.[name]?.reset_offset
                                            }
                                            noStyle
                                        >
                                            {({getFieldValue}) => {
                                                const sharedRatio = getFieldValue([
                                                    "ingressConfigurations",
                                                    name,
                                                    "shared_ratio"
                                                ]);
                                                const threshold = getFieldValue([
                                                    "ingressConfigurations",
                                                    name,
                                                    "threshold"
                                                ]);
                                                const resetOffset = getFieldValue([
                                                    "ingressConfigurations",
                                                    name,
                                                    "reset_offset"
                                                ]);

                                                return (
                                                    <>
                                                        {/* Shared Ratio */}
                                                        <Col span={3}>
                                                            <Form.Item
                                                                className={style.formItem}
                                                                name={[name, "shared_ratio"]}
                                                                label={
                                                                    index === 0 ? (
                                                                        <>
                                                                            Shared Ratio (%){" "}
                                                                            <span className={style.requiredIcon1}>
                                                                                *
                                                                            </span>
                                                                        </>
                                                                    ) : (
                                                                        ""
                                                                    )
                                                                }
                                                                labelCol={{span: 24}}
                                                                wrapperCol={{span: 24}}
                                                                rules={[
                                                                    {
                                                                        type: "number",
                                                                        min: 1,
                                                                        max: 100,
                                                                        message: "Shared Ratio must between 1 to 100"
                                                                    },
                                                                    {
                                                                        validator: async (_, value) => {
                                                                            // 检查是否为整数
                                                                            if (value && value % 1 !== 0) {
                                                                                return Promise.reject(
                                                                                    new Error(
                                                                                        "Decimal numbers are not allowed"
                                                                                    )
                                                                                );
                                                                            }

                                                                            const currentConfig =
                                                                                bufferForm.getFieldValue([
                                                                                    "ingressConfigurations",
                                                                                    name
                                                                                ]);
                                                                            if (currentConfig.sysname) {
                                                                                // 编辑模式下：检查是否除了 sysname 之外的所有字段都为空
                                                                                if (isEditMode) {
                                                                                    const hasOtherFields =
                                                                                        currentConfig.ports?.length ||
                                                                                        currentConfig.ingress_queues
                                                                                            ?.length ||
                                                                                        currentConfig.guaranteed ||
                                                                                        currentConfig.reset_offset ||
                                                                                        currentConfig.headroom;

                                                                                    // 如果其他字段有值，则 shared_ratio 或 threshold 必填其一
                                                                                    if (
                                                                                        hasOtherFields &&
                                                                                        !value &&
                                                                                        !currentConfig.threshold
                                                                                    ) {
                                                                                        return Promise.reject(
                                                                                            new Error(
                                                                                                "Please input shared ratio!"
                                                                                            )
                                                                                        );
                                                                                    }
                                                                                    // 如果其他字段都为空，则允许 shared_ratio 为空
                                                                                    return Promise.resolve();
                                                                                }
                                                                                // 创建模式下：shared_ratio 或 threshold 必填其一
                                                                                if (
                                                                                    !value &&
                                                                                    !currentConfig.threshold
                                                                                ) {
                                                                                    return Promise.reject(
                                                                                        new Error(
                                                                                            "Please input shared ratio!"
                                                                                        )
                                                                                    );
                                                                                }
                                                                            }
                                                                            return Promise.resolve();
                                                                        }
                                                                    }
                                                                ]}
                                                                onBlur={() => {
                                                                    const value = bufferForm.getFieldValue([
                                                                        "ingressConfigurations",
                                                                        name,
                                                                        "shared_ratio"
                                                                    ]);
                                                                    if (value) {
                                                                        setTimeout(() => {
                                                                            bufferForm
                                                                                .validateFields([
                                                                                    [
                                                                                        "ingressConfigurations",
                                                                                        name,
                                                                                        "threshold"
                                                                                    ]
                                                                                ])
                                                                                .catch(() => {
                                                                                    // 忽略验证错误，因为我们只是想清除错误提示
                                                                                });
                                                                        }, 0);
                                                                    }
                                                                }}
                                                            >
                                                                <InputNumber
                                                                    placeholder="1-100, def: 5"
                                                                    style={{width: "100%"}}
                                                                    disabled={!!threshold || threshold === 0}
                                                                    controls={false}
                                                                />
                                                            </Form.Item>
                                                        </Col>

                                                        {/* Guaranteed */}
                                                        <Col span={3}>
                                                            <Form.Item
                                                                className={style.formItem}
                                                                name={[name, "guaranteed"]}
                                                                label={
                                                                    index === 0 ? (
                                                                        <>
                                                                            Guaranteed{" "}
                                                                            <span className={style.requiredIcon1}>
                                                                                *
                                                                            </span>
                                                                        </>
                                                                    ) : (
                                                                        ""
                                                                    )
                                                                }
                                                                labelCol={{span: 24}}
                                                                wrapperCol={{span: 24}}
                                                                // rules={[{required: true, message: "Please input guaranteed value!"}]}
                                                                rules={[
                                                                    {
                                                                        type: "number",
                                                                        min: 1,
                                                                        max: 65535,
                                                                        message: "Guaranteed must between 1 to 65535"
                                                                    },
                                                                    {
                                                                        validator: async (_, value) => {
                                                                            // 检查是否为整数
                                                                            if (value && value % 1 !== 0) {
                                                                                return Promise.reject(
                                                                                    new Error(
                                                                                        "Decimal numbers are not allowed"
                                                                                    )
                                                                                );
                                                                            }

                                                                            const currentConfig =
                                                                                bufferForm.getFieldValue([
                                                                                    "ingressConfigurations",
                                                                                    name
                                                                                ]);
                                                                            if (currentConfig.sysname) {
                                                                                // 编辑模式下：检查是否除了 sysname 之外的所有字段都为空
                                                                                if (isEditMode) {
                                                                                    const hasOtherFields =
                                                                                        currentConfig.ports?.length ||
                                                                                        currentConfig.ingress_queues
                                                                                            ?.length ||
                                                                                        currentConfig.shared_ratio ||
                                                                                        currentConfig.threshold ||
                                                                                        currentConfig.reset_offset ||
                                                                                        currentConfig.headroom;

                                                                                    // 如果其他字段有值，则 guaranteed 必填
                                                                                    if (hasOtherFields && !value) {
                                                                                        return Promise.reject(
                                                                                            new Error(
                                                                                                "Please select guaranteed!"
                                                                                            )
                                                                                        );
                                                                                    }
                                                                                    // 如果其他字段都为空，则允许 guaranteed 为空
                                                                                    return Promise.resolve();
                                                                                }
                                                                                // 创建模式下：guaranteed 必填
                                                                                if (!value) {
                                                                                    return Promise.reject(
                                                                                        new Error(
                                                                                            "Please select guaranteed!"
                                                                                        )
                                                                                    );
                                                                                }
                                                                            }
                                                                            return Promise.resolve();
                                                                        }
                                                                    }
                                                                ]}
                                                            >
                                                                <InputNumber
                                                                    placeholder="1-65535, def:16"
                                                                    style={{width: "100%"}}
                                                                    controls={false}
                                                                />
                                                            </Form.Item>
                                                        </Col>
                                                        {/* Reset Offset */}
                                                        <Col span={3}>
                                                            <Form.Item
                                                                className={style.formItem}
                                                                name={[name, "reset_offset"]}
                                                                label={
                                                                    index === 0 ? (
                                                                        <>
                                                                            Reset Offset{" "}
                                                                            <span className={style.requiredIcon1}>
                                                                                *
                                                                            </span>
                                                                        </>
                                                                    ) : (
                                                                        ""
                                                                    )
                                                                }
                                                                labelCol={{span: 24}}
                                                                wrapperCol={{span: 24}}
                                                                // rules={[{required: true, message: "Please input reset offset!"}]}
                                                                rules={[
                                                                    {
                                                                        type: "number",
                                                                        min: 1,
                                                                        max: 65535,
                                                                        message: "Reset Offset must between 1 to 65535"
                                                                    },
                                                                    {
                                                                        validator: async (_, value) => {
                                                                            // 检查是否为整数
                                                                            if (value && value % 1 !== 0) {
                                                                                return Promise.reject(
                                                                                    new Error(
                                                                                        "Decimal numbers are not allowed"
                                                                                    )
                                                                                );
                                                                            }
                                                                            const currentConfig =
                                                                                bufferForm.getFieldValue([
                                                                                    "ingressConfigurations",
                                                                                    name
                                                                                ]);
                                                                            if (currentConfig.sysname) {
                                                                                // 编辑模式下：检查是否除了 sysname 之外的所有字段都为空
                                                                                if (isEditMode) {
                                                                                    const hasOtherFields =
                                                                                        currentConfig.ports?.length ||
                                                                                        currentConfig.ingress_queues
                                                                                            ?.length ||
                                                                                        currentConfig.shared_ratio ||
                                                                                        currentConfig.threshold ||
                                                                                        currentConfig.guaranteed ||
                                                                                        currentConfig.headroom;

                                                                                    // 如果其他字段有值，则 reset_offset 必填
                                                                                    if (hasOtherFields && !value) {
                                                                                        return Promise.reject(
                                                                                            new Error(
                                                                                                "Please input reset offset!"
                                                                                            )
                                                                                        );
                                                                                    }
                                                                                    // 如果其他字段都为空，则允许 reset_offset 为空
                                                                                    return Promise.resolve();
                                                                                }
                                                                                // 创建模式下：reset_offset 必填
                                                                                if (!value) {
                                                                                    return Promise.reject(
                                                                                        new Error(
                                                                                            "Please input reset offset!"
                                                                                        )
                                                                                    );
                                                                                }
                                                                            }
                                                                            return Promise.resolve();
                                                                        }
                                                                    }
                                                                ]}
                                                            >
                                                                <InputNumber
                                                                    placeholder="1-65535"
                                                                    style={{width: "100%"}}
                                                                    controls={false}
                                                                />
                                                            </Form.Item>
                                                        </Col>
                                                        {/* Headroom */}
                                                        <Col span={3}>
                                                            <Form.Item
                                                                className={style.formItem}
                                                                name={[name, "headroom"]}
                                                                label={
                                                                    index === 0 ? (
                                                                        <>
                                                                            Headroom{" "}
                                                                            <span className={style.requiredIcon1}>
                                                                                *
                                                                            </span>
                                                                        </>
                                                                    ) : (
                                                                        ""
                                                                    )
                                                                }
                                                                labelCol={{span: 24}}
                                                                wrapperCol={{span: 24}}
                                                                // rules={[{required: true, message: "Please input headroom!"}]}
                                                                rules={[
                                                                    {
                                                                        type: "number",
                                                                        min: 1,
                                                                        max: 65535,
                                                                        message: "Headroom must between 1 to 65535"
                                                                    },
                                                                    {
                                                                        validator: async (_, value) => {
                                                                            // 检查是否为整数
                                                                            if (value && value % 1 !== 0) {
                                                                                return Promise.reject(
                                                                                    new Error(
                                                                                        "Decimal numbers are not allowed"
                                                                                    )
                                                                                );
                                                                            }

                                                                            const currentConfig =
                                                                                bufferForm.getFieldValue([
                                                                                    "ingressConfigurations",
                                                                                    name
                                                                                ]);
                                                                            if (currentConfig.sysname) {
                                                                                // 编辑模式下：检查是否除了 sysname 之外的所有字段都为空
                                                                                if (isEditMode) {
                                                                                    const hasOtherFields =
                                                                                        currentConfig.ports?.length ||
                                                                                        currentConfig.ingress_queues
                                                                                            ?.length ||
                                                                                        currentConfig.shared_ratio ||
                                                                                        currentConfig.threshold ||
                                                                                        currentConfig.guaranteed ||
                                                                                        currentConfig.reset_offset;

                                                                                    console.log(
                                                                                        "hasOtherFields",
                                                                                        hasOtherFields
                                                                                    );
                                                                                    console.log("!value", !value);
                                                                                    console.log(
                                                                                        "currentConfig.shared_ratio",
                                                                                        currentConfig.shared_ratio
                                                                                    );
                                                                                    // 如果其他字段有值，则 headroom 必填
                                                                                    if (hasOtherFields && !value) {
                                                                                        return Promise.reject(
                                                                                            new Error(
                                                                                                "Please input headroom!"
                                                                                            )
                                                                                        );
                                                                                    }
                                                                                    // 如果其他字段都为空，则允许 headroom 为空
                                                                                    return Promise.resolve();
                                                                                }
                                                                                // 创建模式下：headroom 必填
                                                                                if (!value) {
                                                                                    return Promise.reject(
                                                                                        new Error(
                                                                                            "Please input headroom!"
                                                                                        )
                                                                                    );
                                                                                }
                                                                            }
                                                                            return Promise.resolve();
                                                                        }
                                                                    }
                                                                ]}
                                                            >
                                                                <InputNumber
                                                                    placeholder="1-65535, def: 768"
                                                                    style={{width: "100%"}}
                                                                    controls={false}
                                                                />
                                                            </Form.Item>
                                                        </Col>

                                                        {/* Threshold */}
                                                        <Col span={3}>
                                                            <Form.Item
                                                                className={style.formItem}
                                                                name={[name, "threshold"]}
                                                                label={
                                                                    index === 0 ? (
                                                                        <>
                                                                            Threshold{" "}
                                                                            <span className={style.requiredIcon1}>
                                                                                *
                                                                            </span>
                                                                        </>
                                                                    ) : (
                                                                        ""
                                                                    )
                                                                }
                                                                labelCol={{span: 24}}
                                                                wrapperCol={{span: 24}}
                                                                rules={[
                                                                    {
                                                                        type: "number",
                                                                        min: 1,
                                                                        max: 65535,
                                                                        message: "Threshold must between 1 to 65535"
                                                                    },
                                                                    {
                                                                        validator: async (_, value) => {
                                                                            // 检查是否为整数
                                                                            if (value && value % 1 !== 0) {
                                                                                return Promise.reject(
                                                                                    new Error(
                                                                                        "Decimal numbers are not allowed"
                                                                                    )
                                                                                );
                                                                            }

                                                                            const currentConfig =
                                                                                bufferForm.getFieldValue([
                                                                                    "ingressConfigurations",
                                                                                    name
                                                                                ]);
                                                                            if (currentConfig.sysname) {
                                                                                // 编辑模式下：检查是否除了 sysname 之外的所有字段都为空
                                                                                if (isEditMode) {
                                                                                    const hasOtherFields =
                                                                                        currentConfig.ports?.length ||
                                                                                        currentConfig.ingress_queues
                                                                                            ?.length ||
                                                                                        currentConfig.guaranteed ||
                                                                                        currentConfig.reset_offset ||
                                                                                        currentConfig.headroom;

                                                                                    // 如果其他字段有值，则 threshold 或 shared_ratio 必填其一
                                                                                    console.log(
                                                                                        "hasOtherFields",
                                                                                        hasOtherFields
                                                                                    );
                                                                                    console.log("value", value);
                                                                                    console.log(
                                                                                        "currentConfig.shared_ratio",
                                                                                        currentConfig.shared_ratio
                                                                                    );
                                                                                    if (
                                                                                        hasOtherFields &&
                                                                                        !value &&
                                                                                        !currentConfig.shared_ratio
                                                                                    ) {
                                                                                        return Promise.reject(
                                                                                            new Error(
                                                                                                "Please input threshold!"
                                                                                            )
                                                                                        );
                                                                                    }
                                                                                    // 如果其他字段都为空，则允许 threshold 为空
                                                                                    return Promise.resolve();
                                                                                }
                                                                                // 创建模式下：保持原有逻辑
                                                                                if (
                                                                                    !value &&
                                                                                    !currentConfig.shared_ratio
                                                                                ) {
                                                                                    return Promise.reject(
                                                                                        new Error(
                                                                                            "Please input threshold!"
                                                                                        )
                                                                                    );
                                                                                }
                                                                            }
                                                                            return Promise.resolve();
                                                                        }
                                                                    }
                                                                ]}
                                                                onBlur={() => {
                                                                    const value = bufferForm.getFieldValue([
                                                                        "ingressConfigurations",
                                                                        name,
                                                                        "threshold"
                                                                    ]);
                                                                    if (value) {
                                                                        setTimeout(() => {
                                                                            bufferForm
                                                                                .validateFields([
                                                                                    [
                                                                                        "ingressConfigurations",
                                                                                        name,
                                                                                        "shared_ratio"
                                                                                    ]
                                                                                ])
                                                                                .catch(() => {});
                                                                        }, 0);
                                                                    }
                                                                }}
                                                            >
                                                                <InputNumber
                                                                    placeholder="1-65535"
                                                                    style={{width: "100%"}}
                                                                    controls={false}
                                                                    disabled={!!sharedRatio || sharedRatio === 0}
                                                                />
                                                            </Form.Item>
                                                        </Col>
                                                    </>
                                                );
                                            }}
                                        </Form.Item>

                                        <Form.Item name={[name, "config_id"]} hidden>
                                            <Input type="hidden" />
                                        </Form.Item>
                                    </Row>
                                </Col>
                                <Col>
                                    {index === 0 ? (
                                        <Button
                                            onClick={() => {
                                                if (isEditMode === false) {
                                                    setIngressPortTreeData([]);
                                                    add({
                                                        ...ingressDefaultValue
                                                    });
                                                } else {
                                                    const currentSysname = bufferForm.getFieldValue([
                                                        "ingressConfigurations",
                                                        name,
                                                        "sysname"
                                                    ]);
                                                    add({
                                                        sysname: currentSysname,
                                                        ...ingressDefaultValue
                                                    });
                                                }
                                            }}
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "12px",
                                                marginTop: "29px",
                                                width: "auto"
                                            }}
                                            type="link"
                                            icon={<PlusOutlined />}
                                        />
                                    ) : (
                                        <Button
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "12px",
                                                marginTop: index === 0 ? "40px" : "0",
                                                width: "auto"
                                            }}
                                            type="link"
                                            icon={<MinusOutlined />}
                                            onClick={() => {
                                                remove(name);
                                            }}
                                        />
                                    )}
                                </Col>
                            </Row>
                        ))}
                    </>
                )}
            </Form.List>

            {/* Egress Buffer Configuration */}
            <div
                style={{
                    fontSize: "18px",
                    fontWeight: "bold",
                    borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
                    paddingBottom: "10px",
                    marginBottom: "20px",
                    marginTop: "40px"
                }}
            >
                Egress Buffer Configuration
            </div>
            <Form.List name="egressConfigurations" initialValue={[{}]}>
                {(fields, {add, remove}) => (
                    <>
                        {fields.map(({key, name}, index) => (
                            <Row key={key} gutter={[8, 16]}>
                                <Col span={23}>
                                    <Row gutter={8}>
                                        {/* Sysname */}
                                        <Col span={3}>
                                            <Form.Item
                                                className={style.formItem}
                                                name={[name, "sysname"]}
                                                label={
                                                    index === 0 ? (
                                                        <>
                                                            Sysname <span className={style.requiredIcon1}>*</span>
                                                        </>
                                                    ) : (
                                                        ""
                                                    )
                                                }
                                                labelCol={{span: 24}}
                                                wrapperCol={{span: 24}}
                                                rules={[
                                                    // {required: true, message: "Please select sysname!"},
                                                    {
                                                        validator: async (_, value) => {
                                                            // 如果其他字段有值，则 sysname 必填
                                                            const currentConfig = bufferForm.getFieldValue([
                                                                "egressConfigurations",
                                                                name
                                                            ]);
                                                            if (
                                                                currentConfig.ports?.length ||
                                                                currentConfig.egress_queues?.length
                                                            ) {
                                                                if (!value) {
                                                                    return Promise.reject(
                                                                        new Error("Please select sysname!")
                                                                    );
                                                                }
                                                            }
                                                            return Promise.resolve();
                                                        }
                                                    }
                                                ]}
                                            >
                                                <AmpConTreeSelect
                                                    onChange={value => handleModalSysnameChange(value, name, "egress")}
                                                    treeData={fabricTreeData}
                                                    placeholder="Sysname"
                                                    disabled={isEditMode}
                                                    treeDefaultExpandAll
                                                />
                                            </Form.Item>
                                        </Col>
                                        {/* Ports */}
                                        <Col span={3}>
                                            <Form.Item
                                                className={style.formItem}
                                                name={[name, "ports"]}
                                                label={
                                                    index === 0 ? (
                                                        <>
                                                            Ports <span className={style.requiredIcon1}>*</span>
                                                        </>
                                                    ) : (
                                                        ""
                                                    )
                                                }
                                                labelCol={{span: 24}}
                                                wrapperCol={{span: 24}}
                                                validateTrigger={["onChange", "onBlur"]}
                                                rules={[
                                                    // {required: true, message: "Please select ports!"},
                                                    {
                                                        validator: async (_, value) => {
                                                            const currentConfig = bufferForm.getFieldValue([
                                                                "egressConfigurations",
                                                                name
                                                            ]);
                                                            if (currentConfig.sysname) {
                                                                // 编辑模式下：检查是否除了 sysname 之外的所有字段都为空
                                                                if (isEditMode) {
                                                                    const hasOtherFields =
                                                                        currentConfig.egress_queues?.length ||
                                                                        currentConfig.shared_ratio ||
                                                                        currentConfig.threshold;

                                                                    console.log("hasOtherFields", hasOtherFields);
                                                                    console.log("value", value);
                                                                    // 如果其他字段有值，则 ports 必填
                                                                    if (hasOtherFields && !value?.length) {
                                                                        return Promise.reject(
                                                                            new Error("Please select ports!")
                                                                        );
                                                                    }
                                                                    // 如果其他字段都为空，则允许 ports 为空
                                                                    return Promise.resolve();
                                                                }
                                                                // 创建模式下：ports 必填
                                                                if (!value?.length) {
                                                                    return Promise.reject(
                                                                        new Error("Please select ports!")
                                                                    );
                                                                }
                                                            }
                                                            return Promise.resolve();
                                                        }
                                                    }
                                                ]}
                                            >
                                                <CustomTreeSelect
                                                    popupClassName="custom-popup"
                                                    style={{width: "100%"}}
                                                    treeData={
                                                        bufferForm.getFieldValue([
                                                            "egressConfigurations",
                                                            name,
                                                            "sysname"
                                                        ])
                                                            ? egressPortTreeData
                                                            : []
                                                    }
                                                    treeExpandedKeys={egressExpandedKeys}
                                                    onTreeExpand={keys => setEgressExpandedKeys(keys)}
                                                    placeholder="Ports"
                                                    onFocus={() => handleEgressPortFocus(name)}
                                                    onBlur={() => handleEgressPortBlur(name)}
                                                    onChange={(value, label) => {
                                                        console.log("egress value", value);
                                                        setEditPortData(value);
                                                    }}
                                                />
                                            </Form.Item>
                                        </Col>
                                        {/* Egress Queues */}
                                        <Col span={3}>
                                            <Form.Item
                                                className={style.formItem}
                                                name={[name, "egress_queues"]}
                                                label={
                                                    index === 0 ? (
                                                        <>
                                                            Egress Queues <span className={style.requiredIcon1}>*</span>
                                                        </>
                                                    ) : (
                                                        ""
                                                    )
                                                }
                                                labelCol={{span: 24}}
                                                wrapperCol={{span: 24}}
                                                validateTrigger={["onChange", "onBlur"]}
                                                rules={[
                                                    // {required: true, message: "Please select egress queues!"},
                                                    {
                                                        validator: async (_, value) => {
                                                            const currentConfig = bufferForm.getFieldValue([
                                                                "egressConfigurations",
                                                                name
                                                            ]);
                                                            if (currentConfig.sysname) {
                                                                // 编辑模式下：检查是否除了 sysname 之外的所有字段都为空
                                                                if (isEditMode) {
                                                                    const hasOtherFields =
                                                                        currentConfig.ports?.length ||
                                                                        currentConfig.shared_ratio ||
                                                                        currentConfig.threshold;

                                                                    // 如果其他字段有值，则 egress_queues 必填
                                                                    if (hasOtherFields && !value?.length) {
                                                                        return Promise.reject(
                                                                            new Error("Please select queues!")
                                                                        );
                                                                    }
                                                                    // 如果其他字段都为空，则允许 egress_queues 为空
                                                                    return Promise.resolve();
                                                                }
                                                                // 创建模式下：egress_queues 必填
                                                                if (!value?.length) {
                                                                    return Promise.reject(
                                                                        new Error("Please select queues!")
                                                                    );
                                                                }
                                                            }
                                                            return Promise.resolve();
                                                        }
                                                    }
                                                ]}
                                            >
                                                <CustomTreeSelect
                                                    popupClassName="custom-popup"
                                                    treeData={[
                                                        {
                                                            title: "All Queues",
                                                            value: JSON.stringify(queueList),
                                                            children: queueList.map(queue => ({
                                                                title: queue,
                                                                value: queue,
                                                                key: queue
                                                            }))
                                                        }
                                                    ]}
                                                    placeholder="Egress Queues"
                                                    onChange={(value, label) => {
                                                        // 简化：只设置实际的队列值，不设置is_all_queues
                                                        bufferForm.setFieldValue(
                                                            ["egressConfigurations", name, "egress_queues"],
                                                            value
                                                        );
                                                    }}
                                                />
                                            </Form.Item>
                                        </Col>

                                        <Form.Item
                                            shouldUpdate={(prev, curr) =>
                                                prev?.egressConfigurations?.[name]?.shared_ratio !==
                                                    curr?.egressConfigurations?.[name]?.shared_ratio ||
                                                prev?.egressConfigurations?.[name]?.threshold !==
                                                    curr?.egressConfigurations?.[name]?.threshold
                                            }
                                            noStyle
                                        >
                                            {({getFieldValue}) => {
                                                const sharedRatio = getFieldValue([
                                                    "egressConfigurations",
                                                    name,
                                                    "shared_ratio"
                                                ]);
                                                const threshold = getFieldValue([
                                                    "egressConfigurations",
                                                    name,
                                                    "threshold"
                                                ]);

                                                return (
                                                    <>
                                                        {/* Shared Ratio */}
                                                        <Col span={3}>
                                                            <Form.Item
                                                                className={style.formItem}
                                                                name={[name, "shared_ratio"]}
                                                                label={
                                                                    index === 0 ? (
                                                                        <>
                                                                            Shared Ratio (%){" "}
                                                                            <span className={style.requiredIcon1}>
                                                                                *
                                                                            </span>
                                                                        </>
                                                                    ) : (
                                                                        ""
                                                                    )
                                                                }
                                                                labelCol={{span: 24}}
                                                                wrapperCol={{span: 24}}
                                                                rules={[
                                                                    {
                                                                        type: "number",
                                                                        min: 1,
                                                                        max: 100,
                                                                        message: "Shared Ratio must between 1 to 100"
                                                                    },
                                                                    {
                                                                        validator: async (_, value) => {
                                                                            // 检查是否为整数
                                                                            if (value && value % 1 !== 0) {
                                                                                return Promise.reject(
                                                                                    new Error(
                                                                                        "Decimal numbers are not allowed"
                                                                                    )
                                                                                );
                                                                            }

                                                                            const currentConfig =
                                                                                bufferForm.getFieldValue([
                                                                                    "egressConfigurations",
                                                                                    name
                                                                                ]);
                                                                            if (currentConfig.sysname) {
                                                                                // 编辑模式下：检查是否除了 sysname 之外的所有字段都为空
                                                                                if (isEditMode) {
                                                                                    const hasOtherFields =
                                                                                        currentConfig.ports?.length ||
                                                                                        currentConfig.egress_queues
                                                                                            ?.length;

                                                                                    // 如果其他字段有值，则 shared_ratio 或 threshold 必填其一
                                                                                    if (
                                                                                        hasOtherFields &&
                                                                                        !value &&
                                                                                        !currentConfig.threshold
                                                                                    ) {
                                                                                        return Promise.reject(
                                                                                            new Error(
                                                                                                "Please input shared ratio!"
                                                                                            )
                                                                                        );
                                                                                    }
                                                                                    // 如果其他字段都为空，则允许 shared_ratio 为空
                                                                                    return Promise.resolve();
                                                                                }
                                                                                // 创建模式下：保持原有逻辑
                                                                                if (
                                                                                    !value &&
                                                                                    !currentConfig.threshold
                                                                                ) {
                                                                                    return Promise.reject(
                                                                                        new Error(
                                                                                            "Please input shared ratio!"
                                                                                        )
                                                                                    );
                                                                                }
                                                                            }
                                                                            return Promise.resolve();
                                                                        }
                                                                    }
                                                                ]}
                                                                onBlur={() => {
                                                                    const value = bufferForm.getFieldValue([
                                                                        "egressConfigurations",
                                                                        name,
                                                                        "shared_ratio"
                                                                    ]);
                                                                    if (value) {
                                                                        setTimeout(() => {
                                                                            bufferForm
                                                                                .validateFields([
                                                                                    [
                                                                                        "egressConfigurations",
                                                                                        name,
                                                                                        "threshold"
                                                                                    ]
                                                                                ])
                                                                                .catch(() => {
                                                                                    // 忽略验证错误，因为我们只是想清除错误提示
                                                                                });
                                                                        }, 0);
                                                                    }
                                                                }}
                                                            >
                                                                <InputNumber
                                                                    placeholder="1-100, def: 5"
                                                                    style={{width: "100%"}}
                                                                    controls={false}
                                                                    disabled={!!threshold || threshold === 0}
                                                                />
                                                            </Form.Item>
                                                        </Col>

                                                        {/* Threshold */}
                                                        <Col span={3}>
                                                            <Form.Item
                                                                className={style.formItem}
                                                                name={[name, "threshold"]}
                                                                label={
                                                                    index === 0 ? (
                                                                        <>
                                                                            Threshold{" "}
                                                                            <span className={style.requiredIcon1}>
                                                                                *
                                                                            </span>
                                                                        </>
                                                                    ) : (
                                                                        ""
                                                                    )
                                                                }
                                                                labelCol={{span: 24}}
                                                                wrapperCol={{span: 24}}
                                                                rules={[
                                                                    {
                                                                        type: "number",
                                                                        min: 1,
                                                                        max: 65535,
                                                                        message: "Threshold must between 1 to 65535"
                                                                    },
                                                                    {
                                                                        validator: async (_, value) => {
                                                                            // 检查是否为整数
                                                                            if (value && value % 1 !== 0) {
                                                                                return Promise.reject(
                                                                                    new Error(
                                                                                        "Decimal numbers are not allowed"
                                                                                    )
                                                                                );
                                                                            }

                                                                            const currentConfig =
                                                                                bufferForm.getFieldValue([
                                                                                    "egressConfigurations",
                                                                                    name
                                                                                ]);
                                                                            if (currentConfig.sysname) {
                                                                                // 编辑模式下：检查是否除了 sysname 之外的所有字段都为空
                                                                                if (isEditMode) {
                                                                                    const hasOtherFields =
                                                                                        currentConfig.ports?.length ||
                                                                                        currentConfig.egress_queues
                                                                                            ?.length;

                                                                                    // 如果其他字段有值，则 threshold 或 shared_ratio 必填其一
                                                                                    if (
                                                                                        hasOtherFields &&
                                                                                        !value &&
                                                                                        !currentConfig.shared_ratio
                                                                                    ) {
                                                                                        return Promise.reject(
                                                                                            new Error(
                                                                                                "Please input threshold!"
                                                                                            )
                                                                                        );
                                                                                    }
                                                                                    // 如果其他字段都为空，则允许 threshold 为空
                                                                                    return Promise.resolve();
                                                                                }
                                                                                // 创建模式下：保持原有逻辑
                                                                                if (
                                                                                    !value &&
                                                                                    !currentConfig.shared_ratio
                                                                                ) {
                                                                                    return Promise.reject(
                                                                                        new Error(
                                                                                            "Please input threshold!"
                                                                                        )
                                                                                    );
                                                                                }
                                                                            }
                                                                            return Promise.resolve();
                                                                        }
                                                                    }
                                                                ]}
                                                                onBlur={() => {
                                                                    const value = bufferForm.getFieldValue([
                                                                        "egressConfigurations",
                                                                        name,
                                                                        "threshold"
                                                                    ]);
                                                                    if (value) {
                                                                        setTimeout(() => {
                                                                            bufferForm
                                                                                .validateFields([
                                                                                    [
                                                                                        "egressConfigurations",
                                                                                        name,
                                                                                        "shared_ratio"
                                                                                    ]
                                                                                ])
                                                                                .catch(() => {
                                                                                    // 忽略验证错误，因为我们只是想清除错误提示
                                                                                });
                                                                        }, 0);
                                                                    }
                                                                }}
                                                            >
                                                                <InputNumber
                                                                    placeholder="1-65535"
                                                                    style={{width: "100%"}}
                                                                    controls={false}
                                                                    disabled={!!sharedRatio || sharedRatio === 0}
                                                                />
                                                            </Form.Item>
                                                        </Col>
                                                    </>
                                                );
                                            }}
                                        </Form.Item>
                                        <Form.Item name={[name, "config_id"]} hidden>
                                            <Input type="hidden" />
                                        </Form.Item>
                                        <Col>
                                            {index === 0 ? (
                                                <Button
                                                    onClick={() => {
                                                        if (isEditMode === false) {
                                                            setEgressPortTreeData([]);
                                                            add({
                                                                shared_ratio: 5
                                                            });
                                                        } else {
                                                            const currentSysname = bufferForm.getFieldValue([
                                                                "egressConfigurations",
                                                                name,
                                                                "sysname"
                                                            ]);
                                                            add({
                                                                sysname: currentSysname,
                                                                shared_ratio: 5
                                                            });
                                                        }
                                                    }}
                                                    style={{
                                                        backgroundColor: "transparent",
                                                        color: "#BFBFBF",
                                                        marginBottom: "12px",
                                                        marginTop: "29px",
                                                        width: "auto"
                                                    }}
                                                    type="link"
                                                    icon={<PlusOutlined />}
                                                />
                                            ) : (
                                                <Button
                                                    style={{
                                                        backgroundColor: "transparent",
                                                        color: "#BFBFBF",
                                                        marginBottom: "12px",
                                                        marginTop: index === 0 ? "40px" : "0",
                                                        width: "auto"
                                                    }}
                                                    type="link"
                                                    icon={<MinusOutlined />}
                                                    onClick={() => {
                                                        remove(name);
                                                    }}
                                                />
                                            )}
                                        </Col>
                                    </Row>
                                </Col>
                            </Row>
                        ))}
                    </>
                )}
            </Form.List>
        </Form.Item>
    );

    // 修改模态框的 sysname 变化处理函数，参考PFCConfiguration
    const handleModalSysnameChange = async (value, rowIndex, configType) => {
        // 根据配置类型设置表单字段值
        const formField = configType === "ingress" ? "ingressConfigurations" : "egressConfigurations";

        // 清空端口选择
        bufferForm.setFields([
            {
                name: [formField, rowIndex, "ports"],
                value: []
            }
        ]);

        if (value) {
            let obj = {};
            try {
                obj = JSON.parse(value);
            } catch (e) {
                console.error("Failed to parse sysname:", e);
                return;
            }

            if (obj.sn) {
                try {
                    // 使用过滤后的端口数据，根据配置类型使用不同的query_model
                    await fetchFilteredPortsBySn(obj.sn, configType, true);
                } catch (error) {
                    message.error("Failed to get ports");
                }
            }
        }
        if (!value) {
            // 如果清空sysname，也清空相应的端口树数据
            if (configType === "ingress") {
                setIngressPortTreeData([]);
            } else {
                setEgressPortTreeData([]);
            }
        }
    };

    const handleTrafficTypeChange = value => {
        setTrafficType(value);
        // 强制更新状态，触发重新渲染
        setForceUpdate(prev => prev + 1);
        console.log(value);
        console.log(trafficType);
    };

    return (
        <div style={{marginBottom: 30}}>
            <h3>
                PFC Buffer Configuration
                <Tooltip
                    placement="right"
                    title="Configure PFC buffer to implement traffic control and PFC watchdog, which is based on interface and priority queue. 
                    The storage space of each interface is divided into different buffers independently and a certain action will be executed after the number of accumulated packets reaches the buffer threshold (the unit is cell)."
                >
                    <QuestionCircleOutlined style={{color: "#999", marginLeft: 4, cursor: "pointer", fontSize: 14}} />
                </Tooltip>
            </h3>
            <div style={{marginBottom: 4, display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                <Button type="primary" onClick={handleBufferCreate}>
                    <Icon component={addSvg} />
                    Configuration
                </Button>
                <Space>
                    <Form.Item label="Traffic" style={{marginBottom: 0, marginRight: 32}}>
                        <Select style={{width: 280}} value={trafficType} onChange={handleTrafficTypeChange}>
                            <Select.Option value="Ingress Queue">Ingress Queue</Select.Option>
                            <Select.Option value="Egress Queue">Egress Queue</Select.Option>
                        </Select>
                    </Form.Item>
                    <Form.Item label="Sysname" style={{marginBottom: 0, marginRight: 32}}>
                        <CustomTreeSelect
                            style={{width: 280}}
                            onChange={handleHeaderSysnameChange}
                            value={selectHeaderSysname}
                            placeholder="Please select"
                            multiple={false}
                            treeCheckable={false}
                            allowClear
                            treeData={headerFabricTreeData}
                            treeDefaultExpandAll
                        />
                    </Form.Item>
                    <Form.Item label="Port" style={{marginBottom: 0}}>
                        <CustomTreeSelect
                            style={{width: 280}}
                            value={selectHeaderPort}
                            onChange={handleHeaderPortChange}
                            placeholder="Please select"
                            treeData={headerPortTreeData}
                            treeExpandedKeys={
                                trafficType === "Ingress Queue" ? ingressExpandedKeys : egressExpandedKeys
                            }
                            onTreeExpand={keys => {
                                if (trafficType === "Ingress Queue") {
                                    setIngressExpandedKeys(keys);
                                } else {
                                    setEgressExpandedKeys(keys);
                                }
                            }}
                        />
                    </Form.Item>
                </Space>
            </div>
            {trafficType === "Ingress Queue" ? (
                <ExtendedTable
                    key={`ingress-table-${forceUpdate}`}
                    columns={bufferColumnsIngress}
                    fetchAPIInfo={getBufferIngressConfigListWrapper}
                    // loading={loading}
                    isShowPagination
                    ref={ingressTableRef}
                    bordered
                    matchFieldsList={matchFieldsList}
                />
            ) : (
                <ExtendedTable
                    key={`egress-table-${forceUpdate}`}
                    columns={bufferColumnsEgress}
                    fetchAPIInfo={getBufferEgressConfigListWrapper}
                    // loading={loading}
                    isShowPagination
                    ref={egressTableRef}
                    bordered
                    matchFieldsList={matchFieldsList}
                />
            )}

            <AmpConCustomModalForm
                title={isEditMode ? "Edit PFC Buffer Configuration" : "Create PFC Buffer Configuration"}
                isModalOpen={isBufferModalVisible}
                formInstance={bufferForm}
                layoutProps={{
                    labelCol: {
                        span: 4
                    }
                }}
                CustomFormItems={bufferFormItems}
                onCancel={handleBufferModalCancel}
                onSubmit={handleBufferSubmit}
                modalClass="ampcon-max-modal"
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button key="cancel" onClick={handleBufferModalCancel}>
                        Cancel
                    </Button>,
                    <Button key="ok" type="primary" onClick={bufferForm.submit}>
                        Apply
                    </Button>
                ]}
            />
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
        </div>
    );
};

export default PFCBufferConfiguration;
