import React from "react";
import {Tooltip} from "antd";
import {QuestionCircleFilled} from "@ant-design/icons";

const CustomQuestionIcon = ({title, placement = "right", style = {}}) => {
    const defaultStyle = {
        fontSize: "16px",
        borderRadius: "50%",
        borderColor: "rgba(0, 0, 0, 0.25)",
        padding: "2px",
        color: "rgba(0, 0, 0, 0.25)",
        cursor: "pointer",
        transition: "all 0.3s",
        marginLeft: 5,
        ...style
    };

    const handleMouseEnter = e => {
        e.currentTarget.style.color = "#14c9bb";
        e.currentTarget.style.borderColor = "#14c9bb";
    };

    const handleMouseLeave = e => {
        e.currentTarget.style.color = "rgba(0, 0, 0, 0.25)";
        e.currentTarget.style.borderColor = "rgba(0, 0, 0, 0.25)";
    };

    return (
        <Tooltip placement={placement} title={title}>
            <QuestionCircleFilled
                style={defaultStyle}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
            />
        </Tooltip>
    );
};

export default CustomQuestionIcon;
