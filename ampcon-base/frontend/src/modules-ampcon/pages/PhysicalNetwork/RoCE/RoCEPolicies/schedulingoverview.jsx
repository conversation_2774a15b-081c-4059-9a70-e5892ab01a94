import React, {useEffect, useState, useRef} from "react";
import {Tooltip, message, Space, Form, TreeSelect} from "antd";
import {QuestionCircleOutlined} from "@ant-design/icons";
import unfoldSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold.svg?react";
import unfoldHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold_hover.svg?react";
import shrinkSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink.svg?react";
import shrinkHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink_hover.svg?react";
import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {
    getSchedulingConfigList,
    getSchedulingTrafficConfigList,
    getFabricSwitches,
    getForwardingClassConfigList
} from "@/modules-ampcon/apis/roce_api";
import CustomQuestionIcon from "./QuestionIcon";
import {PaginationTable} from "./ext_table";
import {fetchFabricSwitchList, renderArrayColumn} from "./utils";

// 统一的表格单元格溢出样式
const cellOverflowStyle = {
    maxWidth: "200px",
    // lineHeight: "48px",
    overflow: "hidden",
    textOverflow: "ellipsis",
    // whiteSpace: "nowrap",
    cursor: "pointer"
};

const SchedulingOverview = () => {
    // Create refs for three tables
    const schedulingTableRef = useRef(null);
    const ingressTableRef = useRef(null);
    const egressTableRef = useRef(null);

    const [selectedSysname, setSelectedSysname] = useState([]);
    const [switchList, setSwitchList] = useState([]);
    const [hoveredIcons, setHoveredIcons] = useState({});

    const matchFieldsList = [{name: "sysname", matchMode: "fuzzy"}];

    const ingressMatchFieldsList = [
        {name: "classifier", matchMode: "fuzzy"},
        {name: "trust_mode", matchMode: "fuzzy"},
        {name: "port", matchMode: "fuzzy"}
    ];
    const egressMatchFieldsList = [
        {name: "scheduler_profile", matchMode: "fuzzy"},
        {name: "scheduler", matchMode: "fuzzy"},
        {name: "port", matchMode: "fuzzy"}
    ];

    useEffect(() => {
        fetchSwitchList();
    }, []);

    useEffect(() => {
        schedulingTableRef.current.refreshTable();
        ingressTableRef.current.refreshTable();
        egressTableRef.current.refreshTable();
    }, [selectedSysname]);

    const getSchedulingConfigListWrapper = async (page, pageSize, filterFields, sortFields, searchFields) => {
        const switchSnList = selectedSysname
            .map(value => {
                const parsed = value ? JSON.parse(value) : null;
                return parsed?.sn;
            })
            .filter(sn => sn !== null);

        const extraParams = switchSnList.length > 0 ? {switch_sn: switchSnList} : {};
        return await getForwardingClassConfigList(page, pageSize, filterFields, sortFields, searchFields, extraParams);
    };

    const getSchedulingIngressConfigListWrapper = async (page, pageSize, filterFields, sortFields, searchFields) => {
        const switchSnList = selectedSysname
            .map(value => {
                const parsed = value ? JSON.parse(value) : null;
                return parsed?.sn;
            })
            .filter(sn => sn !== null);

        const extraParams = {
            ...(switchSnList.length > 0 ? {switch_sn: switchSnList} : {}),
            traffic_type: "ingress"
        };
        return await getSchedulingTrafficConfigList(
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields,
            extraParams
        );
    };

    const getSchedulingEgressConfigListWrapper = async (page, pageSize, filterFields, sortFields, searchFields) => {
        const switchSnList = selectedSysname
            .map(value => {
                const parsed = value ? JSON.parse(value) : null;
                return parsed?.sn;
            })
            .filter(sn => sn !== null);

        const extraParams = {
            ...(switchSnList.length > 0 ? {switch_sn: switchSnList} : {}),
            traffic_type: "egress"
        };
        return await getSchedulingTrafficConfigList(
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields,
            extraParams
        );
    };

    const fetchSwitchList = async () => {
        const tree = await fetchFabricSwitchList();
        if (tree) {
            setSwitchList(tree);
        }
    };

    // Handle sysname/port changes and set searchFields
    const handleSysnameChange = values => {
        setSelectedSysname(values);
        if (values && values.length > 0) {
            // Process multi-select values, extract all sysnames
            const sysnameList = [];
            values.forEach(value => {
                try {
                    // If value is JSON string, it's a specific switch selection
                    const parsed = JSON.parse(value);
                    if (parsed.sysname && !sysnameList.includes(parsed.sysname)) {
                        sysnameList.push(parsed.sysname);
                    }
                } catch {
                    // If not JSON string, might be fabric name
                    if (!sysnameList.includes(value)) {
                        sysnameList.push(value);
                    }
                }
            });
        }
    };

    // Reference to Deployment hover handling method
    const handleMouseEnter = id => {
        setHoveredIcons(prev => ({...prev, [id]: true}));
    };

    const handleMouseLeave = id => {
        setHoveredIcons(prev => ({...prev, [id]: false}));
    };

    // Reference to Deployment icon selection method
    const getIconComponent = (expanded, isHovered) => {
        if (expanded) {
            return isHovered ? shrinkHoverSvg : shrinkSvg;
        }
        return isHovered ? unfoldHoverSvg : unfoldSvg;
    };

    // Reference to Deployment switcherIcon implementation
    const switcherIcon = ({expanded, id}) => {
        const IconComponent = getIconComponent(expanded, hoveredIcons[id]);
        return (
            <IconComponent
                style={{width: "16px", height: "16px", marginTop: "4px", marginRight: "8px", marginLeft: "8px"}}
                alt={expanded ? "shrink" : "unfold"}
                onMouseEnter={() => handleMouseEnter(id)}
                onMouseLeave={() => handleMouseLeave(id)}
            />
        );
    };

    // Service Scheduling table column configuration
    const columns = [
        createColumnConfig("Sysname", "sysname", TableFilterDropdown),
        createColumnConfig("Forwarding Class", "forwarding_class"),
        createColumnConfig("Local Priority", "local_priority"),
        createColumnConfig("Scheduler", "scheduler"),
        createColumnConfig("QoS mode", "mode"),
        {
            ...createColumnConfig("Weight", "weight"),
            ellipsis: true,
            render: text => text || "--"
        },
        {
            ...createColumnConfig("Guaranteed Rate", "guaranteed_rate"),
            ellipsis: true,
            render: text => text || "--"
        }
    ];

    // Egress Port Information table column configuration
    const egressPortColumns = [
        {
            ...createColumnConfig("Scheduler Profile", "scheduler_profile", TableFilterDropdown),
            ellipsis: true
        },
        {
            ...createColumnConfig("Scheduler", "scheduler", TableFilterDropdown),
            ellipsis: true
        },
        {
            ...createColumnConfig("Ports", "port", TableFilterDropdown),
            render: (_, record) => {
                if (record.is_all_ports) {
                    return "All Ports";
                }
                return renderArrayColumn(record.port);
            }
        },
        createColumnConfig("Forwarding Class", "forwarding_class", TableFilterDropdown),
        createColumnConfig("Local Priority", "local_priority", TableFilterDropdown)
    ];

    // Ingress Port Information table column configuration
    const ingressPortColumns = [
        {
            ...createColumnConfig("Classifier", "classifier", TableFilterDropdown),
            ellipsis: true
        },
        {
            ...createColumnConfig("Trust Mode", "trust_mode", TableFilterDropdown),
            ellipsis: true
        },
        {
            ...createColumnConfig("Ports", "port", TableFilterDropdown),
            render: (_, record) => {
                if (record.is_all_ports) {
                    return "All Ports";
                }
                return renderArrayColumn(record.port);
            }
        },
        createColumnConfig("Forwarding Class", "forwarding_class", TableFilterDropdown),
        {
            ...createColumnConfig("Queues", "queue", TableFilterDropdown),
            render: (_, record) => {
                if (record.is_all_queues) {
                    return "All Queues";
                }
                return renderArrayColumn(record.queue);
            }
        }
    ];

    return (
        <div style={{marginBottom: 24}}>
            <h3>
                Service Scheduling
                <Tooltip
                    title="Configure Quality of Service (QoS) to ensure different levels of quality and performance for different types of traffic in a network."
                    placement="right"
                >
                    <QuestionCircleOutlined style={{color: "#999", marginLeft: 4, cursor: "pointer", fontSize: 16}} />
                </Tooltip>
            </h3>
            <div style={{marginBottom: 24, display: "flex", alignItems: "center"}}>
                <Space>
                    <Form.Item label="Sysname" style={{marginBottom: 0}}>
                        <TreeSelect
                            style={{width: 280}}
                            placeholder="Please select"
                            allowClear
                            value={selectedSysname}
                            onChange={handleSysnameChange}
                            treeData={switchList}
                            switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
                            treeNodeFilterProp="title"
                            treeIcon={false}
                            treeDefaultExpandAll
                            multiple
                            treeCheckable
                            showCheckedStrategy={TreeSelect.SHOW_CHILD}
                            maxTagCount={2}
                            maxTagTextLength={10}
                            maxTagPlaceholder={omittedValues => `+${omittedValues.length} more`}
                        />
                    </Form.Item>
                </Space>
            </div>
            <div style={{fontSize: 16, fontWeight: 700, marginBottom: 4}}>Configuration Information</div>
            <PaginationTable
                columns={columns}
                fetchAPIInfo={getSchedulingConfigListWrapper}
                // loading={loading}
                isShowPagination
                ref={schedulingTableRef}
                // fetchAPIParams={{aaa: "bbb"}}
                bordered
                matchFieldsList={matchFieldsList}
            />
            <div style={{fontSize: 16, fontWeight: 700, marginBottom: 4, marginTop: 24}}>Ingress Port Information</div>
            <PaginationTable
                columns={ingressPortColumns}
                fetchAPIInfo={getSchedulingIngressConfigListWrapper}
                // loading={loading}
                isShowPagination
                ref={ingressTableRef}
                bordered
                matchFieldsList={ingressMatchFieldsList}
            />
            <div style={{fontSize: 16, fontWeight: 700, marginBottom: 4, marginTop: 24}}>Egress Port Information</div>
            <PaginationTable
                columns={egressPortColumns}
                fetchAPIInfo={getSchedulingEgressConfigListWrapper}
                // loading={loading}
                isShowPagination
                ref={egressTableRef}
                bordered
                matchFieldsList={egressMatchFieldsList}
            />
        </div>
    );
};

export default SchedulingOverview;
