import {message} from "antd";
import {roceGetFabricSwitches} from "@/modules-ampcon/apis/roce_api";

export const fetchFabricSwitchList = async (query_model = null) => {
    console.log("query_model", query_model);
    const res = await roceGetFabricSwitches({query_model});
    if (res && res.data) {
        const fabricKeys = Object.keys(res.data);
        const fabricValues = Object.values(res.data);

        const tree = fabricKeys
            .map((fabricName, index) => {
                const switches = fabricValues[index];

                // Filter out switches with enabled=false
                const enabledSwitches = switches.filter(sw => sw.enabled === true);

                return {
                    title: fabricName, // "default"
                    value: fabricName,
                    key: fabricName,
                    disabled: true,
                    children: enabledSwitches.map(sw => ({
                        title: `${sw.sysname} ${sw.switch_sn}` || "Unknown",
                        value: JSON.stringify({sn: sw.switch_sn, sysname: sw.sysname}),
                        key: JSON.stringify({sn: sw.switch_sn, sysname: sw.sysname})
                    }))
                };
            })
            .filter(fabric => fabric.children && fabric.children.length > 0);

        return tree;
    }
    message.error("Failed to get fabric switches");
    return [];
};

export const fetchQosFabricSwitchList = async (query_model = null) => {
    console.log("query_model", query_model);
    const res = await roceGetFabricSwitches({query_model});
    if (res && res.data) {
        const fabricKeys = Object.keys(res.data);
        const fabricValues = Object.values(res.data);

        const tree = fabricKeys
            .map((fabricName, index) => {
                const switches = fabricValues[index];

                // Filter out switches with enabled=false
                const enabledSwitches = switches.filter(sw => sw.enabled === true);

                return {
                    title: fabricName, // "default"
                    value: fabricName,
                    key: fabricName,
                    disabled: true,
                    children: enabledSwitches.map(sw => ({
                        title: `${sw.sysname} ${sw.switch_sn}` || "Unknown",
                        value: JSON.stringify({switch_sn: sw.switch_sn, sysname: sw.sysname}),
                        key: JSON.stringify({switch_sn: sw.switch_sn, sysname: sw.sysname})
                    }))
                };
            })
            .filter(fabric => fabric.children && fabric.children.length > 0);

        return tree;
    }
    message.error("Failed to get fabric switches");
    return [];
};

export const renderArrayColumn = (text, maxItems = 2) => {
    try {
        const arr = Array.isArray(text) ? text : JSON.parse(text);
        if (!Array.isArray(arr)) return text;

        // 处理特殊值
        if (arr.includes("all_ports")) return "All Ports";
        if (arr.includes("all_queues")) return "All Queues";

        return (
            <div style={{whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis"}} title={arr.join(", ")}>
                {arr.join(", ")}
            </div>
        );
    } catch {
        return (
            <div style={{whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis"}} title={text}>
                {text}
            </div>
        );
    }
};
