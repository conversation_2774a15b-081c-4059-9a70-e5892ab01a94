import React, {useEffect, useState} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import RoCEEasydeploy from "./roce_easydeploy";
import HostOverview from "./overview";
import ProtectedRoute from "@/modules-ampcon/utils/util";

const RoCEEasydeployManager = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();

    useEffect(() => {
        const currentPath = location.pathname;
        if (/(deployment|overview)$/.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(/(deployment|overview)$/)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (/(deployment|overview)$/.test(currentPath)) {
            const matchLength = currentPath.match(/(deployment|overview)$/)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    const items = [
        {
            key: "deployment",
            label: "Deployment",
            children: <ProtectedRoute component={RoCEEasydeploy} onSwitchTab={onChange} />
        },
        {
            key: "overview",
            label: "Overview",
            children: <ProtectedRoute component={HostOverview} />
        }
    ];

    return (
        <div className="easydeployTabs">
            <Tabs activeKey={currentActiveKey} items={items} onChange={onChange} destroyInactiveTabPane />
        </div>
    );
};

export default RoCEEasydeployManager;
