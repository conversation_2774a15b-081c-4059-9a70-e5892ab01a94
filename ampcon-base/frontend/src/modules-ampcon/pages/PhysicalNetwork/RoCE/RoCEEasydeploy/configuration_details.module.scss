:global {
    // overview 选择框靠左
    .ant-tabs .ant-tabs-extra-content {
        margin-right: auto;
    }

    //去掉checkable前的空白
    // .ant-tree-select-dropdown .ant-select-tree .ant-select-tree-switcher {
    //     display: none;
    // }
    // .ant-tree-select-dropdown .ant-select-tree .ant-select-tree-indent {
    //     display: none;
    // }

    .custom-popup {
        .ant-select-tree-switcher,
        .ant-select-tree-indent {
            display: none;
        }
    }
}

.requiredIcon1 {
    color: rgb(245, 63, 63);
    font-size: 23px;
    vertical-align: middle;
    height: 23px;
    margin-left: 3px;
}

.formItem {
    :global {
        margin-bottom: 12px;

        .ant-form-item .ant-form-item-label>label::after {
            // visibility: visible;
        }

        .ant-col-24.ant-form-item-label {
            padding: 0 0 6px;
        }

        .ant-col-24.ant-form-item-label>label {
            height: auto;
        }
    }
}

.modalForm {
    :global {
        .ant-modal-body {
            padding-top: 8px;
        }
    }
}
