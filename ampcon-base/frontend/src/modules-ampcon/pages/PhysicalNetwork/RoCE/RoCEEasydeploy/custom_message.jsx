import React from "react";
import {message} from "antd";
import {CloseCircleFilled} from "@ant-design/icons";

/**
 * 自定义错误消息组件 (Custom Error Message Component)
 * 显示带有标题和错误列表的错误消息
 * @param {string} title - 错误标题
 * @param {Array} errors - 错误列表
 * @param {number} duration - 显示持续时间（秒）
 */
export const showCustomErrorMessage = (title, errors = [], duration = 6) => {
    // 如果没有错误列表，使用普通的 message.error
    if (!errors || !Array.isArray(errors) || errors.length === 0) {
        message.error(title);
        return;
    }

    // 显示自定义的错误消息
    message.open({
        type: "error",
        icon: <></>, // 移除自带图标
        content: (
            <div style={{textAlign: "left", width: "100%", padding: "4px 0"}}>
                {/* 标题行 (Title Row) */}
                <div style={{display: "flex", alignItems: "flex-start", marginBottom: "8px"}}>
                    <CloseCircleFilled
                        style={{
                            color: "#ff4d4f",
                            fontSize: "16px",
                            marginRight: "8px",
                            marginTop: "2px"
                        }}
                    />
                    <span style={{lineHeight: "1.5"}}>{title}</span>
                </div>
                {/* 错误列表 (Error List) */}
                <div style={{paddingLeft: "24px"}}>
                    {errors.map((error, index) => (
                        <div
                            key={index}
                            style={{
                                margin: "6px 0 0 0",
                                lineHeight: "1.5",
                                fontSize: "14px"
                            }}
                        >
                            • {typeof error === "object" ? error.msg || error.message || error : error}
                        </div>
                    ))}
                </div>
            </div>
        ),
        duration
    });
};

export default showCustomErrorMessage;
