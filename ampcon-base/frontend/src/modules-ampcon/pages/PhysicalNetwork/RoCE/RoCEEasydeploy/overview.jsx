import React, {useEffect, useState} from "react";
import {Tabs, Form, Input, Flex, message, TreeSelect, Spin} from "antd";

import {easydeployConfigList, easydeployConfigOverview} from "@/modules-ampcon/apis/roce_api";
import {AmpConTreeSelect} from "@/modules-ampcon/components/custom_tree";

import "./configuration_details.module.scss";

const HostOverview = () => {
    const [activeTab, setActiveTab] = useState("basic_configuration");
    const [sysnameLists, setSysnameLists] = useState([]);
    const [selectSysname, setSelectSysname] = useState();
    const [selectSN, setSelectSN] = useState();

    const tabItems = [
        {
            key: "basic_configuration",
            label: "Basic Configuration",
            children: <BasicConfiguration selectSysname={selectSysname} selectSN={selectSN} />
        },
        {
            key: "RoCE_PCP_DSCP",
            label: "RoCE PCP/DSCP",
            children: <RoCEPCP selectSysname={selectSysname} selectSN={selectSN} />
        },
        {
            key: "RoCE_LP",
            label: "RoCE LP",
            children: <RoCEIP selectSysname={selectSysname} selectSN={selectSN} />
        }
    ];

    const fetchSysnameLists = async () => {
        try {
            const response = await easydeployConfigList(1, 10, [], [], {});
            if (response.status === 200) {
                let treeData = [];
                const configs = response.data;
                if (configs && configs.length > 0) {
                    const fabricMap = {};

                    configs.forEach(config => {
                        const {fabric, switch_sn, sysname} = config;
                        if (!fabricMap[fabric]) {
                            fabricMap[fabric] = [];
                        }
                        const existingIndex = fabricMap[fabric].findIndex(item => item.switch_sn === switch_sn);
                        if (existingIndex === -1) {
                            const deviceInfo = {switch_sn, sysname};
                            fabricMap[fabric].push(deviceInfo);
                        }
                    });

                    treeData = Object.keys(fabricMap).map(fabric => ({
                        key: fabric,
                        value: fabric,
                        title: fabric,
                        disabled: true,
                        children: Array.from(fabricMap[fabric]).map(fabric => ({
                            key: `${fabric.sysname} ${fabric.switch_sn}`,
                            value: `${fabric.sysname} ${fabric.switch_sn}`,
                            title: `${fabric.sysname} ${fabric.switch_sn}`
                        }))
                    }));
                }
                setSysnameLists(treeData);
            } else {
                setSysnameLists([]);
                message.error(response.info);
            }
        } catch (error) {
            message.error("Failed to obtain sysname information.");
        }
    };

    useEffect(() => {
        fetchSysnameLists();
    }, []);

    return (
        <div>
            <Tabs
                className="radioGroupTabs customTab"
                tabBarStyle={{
                    marginBottom: "24px",
                    display: "flex",
                    flexDirection: "column",
                    background: "white"
                }}
                defaultActiveKey="basic_configuration"
                activeKey={activeTab}
                onChange={setActiveTab}
                items={tabItems}
                tabBarExtraContent={
                    <Form layout="inline" style={{flexWrap: "nowrap", marginTop: "24px"}}>
                        <Form.Item
                            name="sysname"
                            label="Sysname"
                            required="true"
                            wrapperCol={{style: {marginLeft: 20, width: 280}}}
                        >
                            <AmpConTreeSelect
                                onChange={value => {
                                    setSelectSysname(value.split(" ")[0]);
                                    setSelectSN(value.split(" ")[1]);
                                }}
                                treeData={sysnameLists}
                                placeholder="Sysname"
                                disabled={false}
                                treeDefaultExpandAll
                            />
                        </Form.Item>
                    </Form>
                }
            />
        </div>
    );
};

const BasicConfiguration = ({selectSysname, selectSN}) => {
    const [ApplyDetails, setApplyDetails] = useState(null);
    const [isShowSpin, setIsShowSpin] = useState(false);

    // 定义分组结构
    const groups = [
        {
            title: null,
            fields: ["roce_easydeploy_status", "roce_mode"]
        },
        {
            title: "Congestion Control",
            fields: [
                "congestion_control.congestion_mode",
                "congestion_control.enabled_queue",
                "congestion_control.max_threshold",
                "congestion_control.min_threshold",
                "congestion_control.probability"
            ]
        },
        {
            title: "PFC",
            fields: ["pfc.pfc_priority", "pfc.rx_enabled", "pfc.tx_enabled"]
        },
        {
            title: "Trust",
            fields: ["trust.trust_mode"]
        }
    ];

    const generateFormattedText = basicInfo => {
        const result = [];

        groups.forEach(group => {
            if (group.title) {
                result.push(`\n${group.title}`);
            }
            group.fields.forEach(fieldKey => {
                // 处理嵌套字段（如 trust.trust_mode）
                const keys = fieldKey.split(".");
                let label;
                let value;
                if (keys.length > 1) {
                    label = keys[1];
                    value = basicInfo[keys[0]]?.[keys[1]];
                } else {
                    label = fieldKey;
                    value = basicInfo[fieldKey];
                }
                const formattedLabel = label
                    .replace(/[_-]/g, " ")
                    .padEnd(40, " ")
                    .split(" ")
                    .map(word => word.replace("pfc", "PFC").replace("roce", "RoCE"))
                    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(" ");
                let formattedValue;
                if (Array.isArray(value)) {
                    formattedValue = value.join(",");
                } else {
                    const temp = String(value).replace("dscp", "DSCP");
                    formattedValue = temp.charAt(0).toUpperCase() + temp.slice(1);
                }
                result.push(`${formattedLabel}${formattedValue}`);
            });
        });

        return result.join("\n").trim();
    };

    const fetchSysnameDetails = async () => {
        const data = {
            sysname: selectSysname,
            switch_sn: selectSN,
            overview_type: "basic"
        };
        try {
            setIsShowSpin(true);
            const ret = await easydeployConfigOverview(data);
            setIsShowSpin(false);
            if (ret.status === 200) {
                const details = ret.info.basic_info;
                setApplyDetails(generateFormattedText(details));
            } else {
                message.error(ret.msg);
            }
        } catch (error) {
            message.error("Failed to obtain sysname details.");
        }
    };

    useEffect(() => {
        if (selectSN) {
            fetchSysnameDetails();
        }
    }, [selectSN]);

    return (
        <>
            <Flex>
                <Input.TextArea
                    style={{
                        height: "calc(100vh - 340px)",
                        border: "none",
                        backgroundColor: "#F8FAFB",
                        fontSize: "16px",
                        borderRadius: "4px",
                        boxShadow: "none",
                        resize: "none",
                        padding: "16px",
                        whiteSpace: "pre",
                        fontFamily: "monospace"
                    }}
                    value={ApplyDetails}
                    readOnly
                />
            </Flex>
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
        </>
    );
};

const RoCEPCP = ({selectSysname, selectSN}) => {
    const [ApplyDetails, setApplyDetails] = useState(null);
    const [isShowSpin, setIsShowSpin] = useState(false);

    const fetchSysnameDetails = async () => {
        const data = {
            sysname: selectSysname,
            switch_sn: selectSN,
            overview_type: "pcp_dscp"
        };
        try {
            setIsShowSpin(true);
            const ret = await easydeployConfigOverview(data);
            setIsShowSpin(false);
            if (ret.status === 200) {
                const details = ret.info.pcp_dscp_info;
                const result = [];
                result.push(
                    `ROCE PCP/DSCP->LP mapping configurations\n====================================================\nlocal-priority dscp\n-------------------------------------\n`
                );
                for (const key in details) {
                    if (Object.prototype.hasOwnProperty.call(details, key)) {
                        result.push(`${key.padEnd(10, " ")} ${details[key].join(", ")}`);
                    }
                }
                setApplyDetails(result.join("\n"));
            } else {
                message.error(ret.msg);
            }
        } catch (error) {
            message.error("Failed to obtain sysname details.");
        }
    };

    useEffect(() => {
        if (selectSN) {
            fetchSysnameDetails();
        }
    }, [selectSN]);

    return (
        <>
            <Flex>
                <Input.TextArea
                    style={{
                        height: "calc(100vh - 340px)",
                        border: "none",
                        backgroundColor: "#F8FAFB",
                        fontSize: "16px",
                        borderRadius: "4px",
                        boxShadow: "none",
                        resize: "none",
                        padding: "16px",
                        whiteSpace: "pre",
                        fontFamily: "monospace"
                    }}
                    value={ApplyDetails}
                    readOnly
                />
            </Flex>
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
        </>
    );
};

const RoCEIP = ({selectSysname, selectSN}) => {
    const [ApplyDetails, setApplyDetails] = useState(null);
    const [isShowSpin, setIsShowSpin] = useState(false);

    const fetchSysnameDetails = async () => {
        const data = {
            sysname: selectSysname,
            switch_sn: selectSN,
            overview_type: "lp"
        };
        try {
            setIsShowSpin(true);
            const ret = await easydeployConfigOverview(data);
            setIsShowSpin(false);
            if (ret.status === 200) {
                const details = ret.info.lp_info;
                const result = [];
                result.push(
                    `RoCE LP->FC mapping and ETS configurations\n====================================================\nlocal-priority forwarding-class scheduler-weight\n-------------------------------------\n`
                );
                for (const key in details) {
                    if (Object.prototype.hasOwnProperty.call(details, key)) {
                        const entry = details[key];
                        const forwarding = entry["forwarding-class"].padEnd(10, " ");
                        result.push(
                            `${key.padEnd(10, " ")} ${forwarding.charAt(0).toUpperCase() + forwarding.slice(1)} ${entry["scheduler-weight"].padEnd(10, " ")}`
                        );
                    }
                }
                setApplyDetails(result.join("\n"));
            } else {
                message.error(ret.msg);
            }
        } catch (error) {
            message.error("Failed to obtain sysname details.");
        }
    };

    useEffect(() => {
        if (selectSN) {
            fetchSysnameDetails();
        }
    }, [selectSN]);

    return (
        <>
            <Flex>
                <Input.TextArea
                    style={{
                        height: "calc(100vh - 340px)",
                        border: "none",
                        backgroundColor: "#F8FAFB",
                        fontSize: "16px",
                        borderRadius: "4px",
                        boxShadow: "none",
                        resize: "none",
                        padding: "16px",
                        whiteSpace: "pre",
                        fontFamily: "monospace"
                    }}
                    value={ApplyDetails}
                    readOnly
                />
            </Flex>
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
        </>
    );
};

export default HostOverview;
