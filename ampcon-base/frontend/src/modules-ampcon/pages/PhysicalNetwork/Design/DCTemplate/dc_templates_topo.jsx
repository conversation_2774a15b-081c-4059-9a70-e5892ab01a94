import {Graph} from "@antv/x6";
import {MiniMap} from "@antv/x6-plugin-minimap";
import {register} from "@antv/x6-react-shape?react";
import {useEffect, useState, useRef, useMemo, useCallback, forwardRef, useImperativeHandle} from "react";
import {unitTopoLayout} from "@/utils/topo_layout_utils";
import DeviceBriefTooltip from "../unit_tooltip/device_brief_tooltip";
import LinkBriefTooltip from "../unit_tooltip/link_brief_tooltip";
import {message} from "antd";

const baseStyle = {
    body: {
        stroke: "transparent",
        rx: 4,
        ry: 4
    },
    label: {
        fontSize: 14,
        fontWeight: 500,
        textAnchor: "middle",
        verticalAnchor: "middle"
    }
};

const registerSpineNode = () => {
    register({
        shape: "spine",
        inherit: "rect",
        attrs: {
            body: {
                fill: "#90D7F9",
                ...baseStyle.body
            },
            label: {
                fill: "#0E7CB0 ",
                ...baseStyle.label
            }
        },
        ports: {
            groups: {
                out: {
                    position: "bottom",
                    attrs: {
                        circle: {
                            r: 1,
                            magnet: false,
                            stroke: "none",
                            strokeWidth: 1,
                            fill: "none"
                        }
                    }
                }
            },
            items: [
                {
                    id: "spinePort",
                    group: "out"
                }
            ]
        },
        zIndex: 1000
    });
};

const registerLeafNode = () => {
    register({
        shape: "leafNode",
        zIndex: 1000,
        inherit: "rect",
        ports: {
            groups: {
                out: {
                    position: "bottom",
                    attrs: {
                        circle: {r: 1, magnet: false, stroke: "none", strokeWidth: 1, fill: "none"}
                    }
                },
                in: {
                    position: "top",
                    attrs: {
                        circle: {r: 1, magnet: false, stroke: "none", strokeWidth: 1, fill: "none"}
                    }
                }
            },
            items: [
                {group: "out", id: "leafBottomPort"},
                {group: "in", id: "leafTopPort"}
            ]
        },
        attrs: {
            body: {
                fill: "#B0E6FF",
                fillOpacity: 0.5,
                ...baseStyle.body
            },
            label: {
                fill: "#26A5E1",
                ...baseStyle.label
            }
        }
    });
};

const DCTemplatesTopo = forwardRef(({unitNodes, podNodes, type, spineCount, superSpineCount}, ref) => {
    const containerRef = useRef(null);
    const graphRef = useRef(null);
    const miniMapContainerRef = useRef(null);
    const deviceBriefTooltipRef = useRef(null);
    const linkBriefTooltipRef = useRef(null);

    const isFabricTopo = useRef(false);
    const warningMessageRef = useRef(null);

    const [isRenderTopo, setIsRenderTopo] = useState(true);

    let x = 100;
    const spineY = 200;

    let dottedFrameId = 1;
    let dottedFrameX = 70;
    const dottedFrameInterval = 40;
    let dottedFrameWidth;

    const dottedFrameInfo = [];

    const leafNodesDetailedInfo = [];
    let spineNodesDetailedInfo;

    let unitWidth = 0;

    let leafNodesCount = 0;
    const nodeWidth = 100;

    const noneNodeWidth = 225;
    const mlagNodeWidth = 100;
    const nodeInterval = 30;
    const nodeHeight = 30;

    const groupX = 100;
    let groupY = 300;

    let rowLeafNodesCount = {};

    let allRowLeafNodesCount = 0;

    let dottedFrameCount = 0;

    let leafWidth = 0;

    let leafNodeID = 0;

    let edgeNum;

    useImperativeHandle(ref, () => ({
        handleIsSaveTopo: () => {
            return isRenderTopo;
        }
    }));

    const clearAllTooltips = useCallback(() => {
        deviceBriefTooltipRef?.current.hideDeviceBriefTooltip();
        linkBriefTooltipRef?.current.hideLinkBriefTooltip();
    }, []);

    const mouseEnterNode = useCallback(deviceInfo => {
        if (deviceInfo.node.store.data.type === "dottedFrame" || deviceInfo.node.id === "warning") return;
        deviceBriefTooltipRef.current.showDeviceBriefTooltip(deviceInfo.node.store.data);
    }, []);

    const mouseEnterEdge = useCallback(({edge}) => {
        if (edge.id === "splitter1") return;

        const sourceNode = edge.getSourceNode();
        const targetNode = edge.getTargetNode();

        linkBriefTooltipRef.current.showLinkBriefTooltip(null, sourceNode.store.data, targetNode.store.data);
    }, []);

    const rowleafNodesCountAndWidth = useMemo(() => {
        if (type === "3-stage" && unitNodes?.length > 0) {
            unitNodes.forEach((unitGroup, index) => {
                if (unitGroup.id === "") return;

                if (unitGroup?.unit_info?.leaf?.length !== 0 || unitGroup?.unit_info?.leaf?.length !== undefined) {
                    const leafNodesLength = unitGroup?.unit_info?.leaf?.length;

                    dottedFrameCount += unitGroup.count;

                    unitGroup?.unit_info?.leaf?.forEach(item => {
                        leafNodesCount += (item.strategy === "MLAG" ? 2 : 1) * unitGroup.count;
                    });

                    if (leafNodesLength >= 4) {
                        leafWidth += unitGroup.count * ((noneNodeWidth + nodeInterval) * 4 + nodeInterval);
                        rowLeafNodesCount[index] = 4;
                        allRowLeafNodesCount += 4 * unitGroup.count;
                    } else {
                        leafWidth +=
                            unitGroup.count * ((noneNodeWidth + nodeInterval) * leafNodesLength + nodeInterval);
                        rowLeafNodesCount[index] = leafNodesLength;
                        allRowLeafNodesCount += leafNodesLength * unitGroup.count;
                    }
                }
                leafWidth += (unitGroup.count - 1) * dottedFrameInterval;
            });

            leafWidth += (unitNodes.length - 1) * dottedFrameInterval;
        }

        return {leafWidth, dottedFrameCount, leafNodesCount, rowLeafNodesCount, allRowLeafNodesCount};
    }, [unitNodes]);

    leafWidth = rowleafNodesCountAndWidth.leafWidth;
    dottedFrameCount = rowleafNodesCountAndWidth.dottedFrameCount;
    leafNodesCount = rowleafNodesCountAndWidth.leafNodesCount;
    rowLeafNodesCount = rowleafNodesCountAndWidth.rowLeafNodesCount;
    allRowLeafNodesCount = rowleafNodesCountAndWidth.allRowLeafNodesCount;

    useEffect(() => {
        graphRef.current = new Graph({
            container: containerRef.current,
            width: containerRef.current.clientWidth,
            height: containerRef.current.clientHeight,

            grid: true,
            rotating: {
                enabled: true
            },
            panning: {
                enabled: true,
                eventTypes: ["leftMouseDown", "mouseWheel"]
            },
            interacting: {
                nodeMovable: false,
                edgeMovable: false
            },
            connecting: {
                connector: {
                    name: "smooth"
                }
            },
            mousewheel: {
                enabled: true,
                modifiers: "ctrl"
            },
            async: true,
            virtual: {
                enabled: true,
                renderArea: {
                    visibleArea: 1.5
                },
                removeTimeout: 3000
            }
        });

        registerSpineNode();
        registerLeafNode();

        graphRef.current.use(
            new MiniMap({
                container: miniMapContainerRef.current,
                width: 200,
                height: 180,
                padding: 10,
                scalable: true,
                scaling: 1,
                minScale: 0.1,
                maxScale: 1,
                graphOptions: {
                    async: true,
                    createCellView(cell) {
                        if (cell.isEdge()) {
                            return null;
                        }
                    }
                }
            })
        );

        if (type === "3-stage") {
            if (spineCount > 128 || leafNodesCount > 128) {
                graphRef.current.clearCells();

                if (!warningMessageRef.current) {
                    warningMessageRef.current = message.warning({
                        content: "The number of nodes exceeds the limit, please reduce the number of nodes",
                        duration: 0
                    });
                }

                setIsRenderTopo(false);
            } else if (unitNodes?.length > 0) {
                edgeNum = leafNodesCount * spineCount;
                if (warningMessageRef.current) {
                    warningMessageRef.current();
                    warningMessageRef.current = null;
                }
                setIsRenderTopo(true);
                if (spineCount !== "") {
                    let spineWidth;
                    if (spineCount >= 8) {
                        spineWidth = (leafWidth - nodeInterval * 7) / 8;
                        if (spineWidth <= nodeWidth) {
                            spineWidth = nodeWidth;
                            leafWidth = spineWidth * 8 + nodeInterval * 7;
                        }
                    } else if (spineCount > 0) {
                        spineWidth = (leafWidth - nodeInterval * (spineCount - 1)) / spineCount;
                        if (spineWidth <= nodeWidth) {
                            spineWidth = nodeWidth;
                            leafWidth = spineWidth * spineCount + nodeInterval * (spineCount - 1);
                        }
                    }
                    groupY = spineY + Math.ceil(spineCount / 8) * (nodeHeight + nodeInterval) + 1.5 * nodeInterval;

                    spineNodesDetailedInfo = Array.from({length: spineCount}, (_, index) => {
                        return {
                            id: `spine_${index + 1}`,
                            shape: "spine",
                            label: `spine_${index + 1}`,
                            name: `spine_${index + 1}`,
                            type: "spine",
                            x: x + (index % 8) * (spineWidth + nodeInterval) - nodeInterval,
                            y: spineY + Math.floor(index / 8) * (nodeHeight + nodeInterval),
                            width: spineWidth,
                            height: nodeHeight
                        };
                    });
                }

                unitNodes?.forEach((unitGroup, index) => {
                    const leafNodes = unitGroup.unit_info?.leaf;
                    const accessNodes = unitGroup.unit_info?.access;

                    if (leafNodes === undefined && accessNodes === undefined) {
                        return;
                    }

                    const dottedFrameHeight =
                        Math.ceil(leafNodes.length / 4) * (nodeHeight + nodeInterval) + 1 * nodeInterval;

                    dottedFrameWidth =
                        (leafWidth - (dottedFrameCount - 1) * dottedFrameInterval) *
                        (rowLeafNodesCount[index] / allRowLeafNodesCount);

                    const unitInfo = {leafNodes, accessNodes};

                    for (let i = 0; i < unitGroup.count; i++) {
                        const point = {x, y: groupY};

                        leafNodesDetailedInfo.push(...unitTopoLayout(leafNodeID, point, unitInfo, dottedFrameWidth));

                        leafNodeID = leafNodesDetailedInfo.pop();

                        dottedFrameInfo.push({
                            id: `group_${dottedFrameId++}`,
                            shape: "rect",
                            label: unitGroup.name,
                            x: dottedFrameX,
                            y: groupY - nodeInterval * 1.5,
                            width: dottedFrameWidth,
                            height: dottedFrameHeight,
                            type: "dottedFrame",
                            attrs: {
                                body: {
                                    borderRadius: 4,
                                    fill: "none",
                                    stroke: "grey",
                                    strokeWidth: 2,
                                    strokeDasharray: "4,5"
                                },
                                label: {
                                    refX: 10,
                                    refY: 10,
                                    textAnchor: "start",
                                    textVerticalAnchor: "top"
                                }
                            },
                            zIndex: 1
                        });
                        x += dottedFrameWidth + dottedFrameInterval;
                        dottedFrameX = x - nodeInterval;
                    }
                    if (unitGroup.count >= 1) {
                        unitWidth += dottedFrameInterval * (unitGroup.count - 1);
                    }
                });

                const edges = [];
                for (const spineNode of spineNodesDetailedInfo) {
                    for (const leafNode of leafNodesDetailedInfo) {
                        edges.push({
                            source: {
                                cell: spineNode.id,
                                port: "spinePort"
                            },
                            target: {
                                cell: leafNode.id,
                                port: "leafTopPort"
                            },
                            zIndex: 0,
                            attrs: {
                                line: {
                                    stroke: "#D8D8D8",
                                    strokeWidth: 1,
                                    targetMarker: null
                                }
                            }
                        });
                    }
                }
                graphRef.current.on("edge:mouseenter", ({edge}) => {
                    edge.attr("line/stroke", "#14c9bb"); // 悬停时变更颜色
                });

                graphRef.current.on("edge:mouseleave", ({edge}) => {
                    edge.attr("line/stroke", "#D8D8D8"); // 恢复原色
                });

                graphRef.current.on("node:mouseenter", ({node}) => {
                    if (node.shape === "spine" || node.shape === "leafNode") {
                        node.attr("body/stroke", "#26A5E1"); // 设置边框颜色
                        node.attr("body/strokeWidth", 1); // 设置边框宽度
                    }
                });
                graphRef.current.on("node:mouseleave", ({node}) => {
                    if (node.shape === "spine" || node.shape === "leafNode") {
                        node.attr("body/stroke", "transparent"); // 恢复原样式
                        node.attr("body/strokeWidth", 1);
                    }
                });

                graphRef.current.startBatch("renderTopo");
                try {
                    graphRef.current.fromJSON({
                        nodes: [...spineNodesDetailedInfo, ...leafNodesDetailedInfo, ...dottedFrameInfo],
                        edges
                    });
                } finally {
                    graphRef.current.stopBatch("renderTopo");
                }
            }

            // if (type === "5-stage") {
            //     if (podNodes?.length > 0) {
            //         podNodes.forEach((podNode, pod_index) => {
            //             podNode.unit.forEach((unitGroup, unit_index) => {
            //                 const leafNodes = unitGroup.unit_info?.leaf;
            //                 const accessNodes = unitGroup.unit_info?.access;

            //                 if (leafNodes === undefined && accessNodes === undefined) {
            //                     return;
            //                 }

            //                 const leafNodes_1 = [];
            //                 const accessNodes_1 = [];

            //                 leafNodes?.forEach(item => {
            //                     if (item.name === "") {
            //                         if (item.strategy === "MLAG") {
            //                             leafNodes_1.push({...item});
            //                             leafNodes_1.push({...item});
            //                         } else {
            //                             leafNodes_1.push({...item});
            //                         }
            //                     } else if (item.strategy === "MLAG") {
            //                         leafNodes_1.push({...item, name: `${item.name}_1`});
            //                         leafNodes_1.push({...item, name: `${item.name}_2`});
            //                     } else {
            //                         leafNodes_1.push({...item, name: `${item.name}_1`});
            //                     }
            //                 });

            //                 accessNodes?.forEach(item => {
            //                     if (item.name === "") {
            //                         accessNodes_1.push({...item});
            //                     } else {
            //                         accessNodes_1.push({...item, name: `${item.name}_1`, leaf: `${item.leaf}_1`});
            //                     }
            //                 });

            //                 // 隐藏access节点,实际上accessNodes_1为需要渲染的节点
            //                 const accessNodes_2 = [];

            //                 const unitInfo = {leafNodes_1, accessNodes_2};

            //                 for (let i = 0; i < unitGroup.count; i++) {
            //                     const point = {x, y};
            //                     leafNodesDetailedInfo.push(...unitTopoLayout(graphRef.current, point, unitInfo));

            //                     const NodeNumMax = Math.max(leafNodes_1.length, accessNodes_1.length);

            //                     dottedFrameWidth = 60 + NodeNumMax * nodeWidth + (NodeNumMax - 1) * nodeInterval;

            //                     podWidth += dottedFrameWidth;

            //                     graphRef.current.addNode({
            //                         id: `group_${dottedFrameId++}`,
            //                         shape: "rect",
            //                         label: unitGroup.name,
            //                         x: dottedFrameX,
            //                         y: dottedFrameY,
            //                         width: dottedFrameWidth,
            //                         height: dottedFrameHeight,
            //                         type: "dottedFrame",
            //                         attrs: {
            //                             body: {
            //                                 borderRadius: 4,
            //                                 fill: "none",
            //                                 stroke: "grey",
            //                                 strokeWidth: 2,
            //                                 strokeDasharray: "4,5"
            //                             },
            //                             label: {
            //                                 refX: 10,
            //                                 refY: 10,
            //                                 textAnchor: "start",
            //                                 textVerticalAnchor: "top"
            //                             }
            //                         },
            //                         zIndex: 1
            //                     });
            //                     x += dottedFrameWidth + 50;
            //                     dottedFrameX = x - 30;
            //                 }
            //                 if (unitGroup.count >= 1) {
            //                     podWidth += dottedFrameInterval * (unitGroup.count - 1);
            //                 }

            //                 if (unit_index !== podNode.unit.length - 1) {
            //                     podWidth += dottedFrameInterval;
            //                 }
            //             });

            //             const spineCount = podNode.spine_count;
            //             if (spineCount !== "" && podWidth !== 0) {
            //                 const spineWidth = (podWidth - nodeInterval * (spineCount - 1)) / spineCount;
            //                 if (spineWidth < 0) {
            //                     message.error(`Please check the number of spine nodes in the pod ${pod_index + 1}"`);
            //                 } else {
            //                     for (let i = 1; i <= spineCount; i++) {
            //                         spineNodesDetailedInfo.push(
            //                             graphRef.current.addNode({
            //                                 id: `pod_${pod_index + 1}_spine_${i}`,
            //                                 shape: "rect",
            //                                 label: `pod_${pod_index + 1}_spine_${i}`,
            //                                 name: `pod_${pod_index + 1}_spine_${i}`,
            //                                 x: spineX + (i - 1) * (nodeInterval + spineWidth),
            //                                 y: spineY,
            //                                 width: spineWidth,
            //                                 height: 32,
            //                                 type: "spine",
            //                                 attrs: {
            //                                     body: {
            //                                         fill: "#90D7F9",
            //                                         stroke: "transparent",
            //                                         fillOpacity: 0.5,
            //                                         rx: 4,
            //                                         ry: 4
            //                                     },
            //                                     label: {
            //                                         fill: "#0E7CB0 ",
            //                                         fontSize: 14,
            //                                         fontWeight: 500,
            //                                         textAnchor: "middle",
            //                                         verticalAnchor: "middle"
            //                                     }
            //                                 },
            //                                 ports: {
            //                                     groups: {
            //                                         out: {
            //                                             position: "bottom",
            //                                             attrs: {
            //                                                 circle: {
            //                                                     r: 1,
            //                                                     magnet: false,
            //                                                     stroke: "none",
            //                                                     strokeWidth: 1,
            //                                                     fill: "none"
            //                                                 }
            //                                             }
            //                                         }
            //                                     },
            //                                     items: Array.from({length: leafNodesDetailedInfo.length}, (_, index) => {
            //                                         return {
            //                                             id: `spinePort-${index}`,
            //                                             group: "out"
            //                                         };
            //                                     })
            //                                 },
            //                                 zIndex: 1
            //                             }).id
            //                         );
            //                     }
            //                     spineX += podWidth + dottedFrameInterval;
            //                     allWidth += podWidth;
            //                     podWidth = 0;
            //                 }
            //             }

            //             spineNodesDetailedInfo.forEach(spineNodeID => {
            //                 leafNodesDetailedInfo.forEach((leafNodeID, index) => {
            //                     graphRef.current.addEdge({
            //                         source: spineNodeID,
            //                         target: leafNodeID,
            //                         sourcePort: `spinePort-${index}`,
            //                         targetPort: "leafTopPort",
            //                         attrs: {
            //                             line: {
            //                                 stroke: "#D8D8D8",
            //                                 strokeWidth: 1.5,
            //                                 targetMarker: null
            //                             }
            //                         }
            //                     });
            //                 });
            //             });

            //             spineNodesDetailedInfo = [];
            //             leafNodesDetailedInfo = [];
            //         });
            //     }

            //     allWidth += dottedFrameInterval * (podNodes.length - 1);

            //     if (superSpineCount !== "" && allWidth !== 0) {
            //         const superSpineWidth = (allWidth - nodeInterval * (superSpineCount - 1)) / superSpineCount;
            //         if (superSpineWidth < 0) {
            //             message.error("Please check the number of super spine nodes");
            //         } else {
            //             for (let i = 1; i <= superSpineCount; i++) {
            //                 allSuperSpineNodesID.push(
            //                     graphRef.current.addNode({
            //                         id: `super_spine_${i}`,
            //                         shape: "rect",
            //                         label: `super_spine_${i}`,
            //                         name: `super_spine_${i}`,
            //                         x: superSpineX + (i - 1) * (nodeInterval + superSpineWidth),
            //                         y: superSpineY,
            //                         width: superSpineWidth,
            //                         height: 32,
            //                         type: "superSpine",
            //                         attrs: {
            //                             body: {
            //                                 fill: "#23A7E5",
            //                                 stroke: "transparent",
            //                                 fillOpacity: 0.5,
            //                                 rx: 4,
            //                                 ry: 4
            //                             },
            //                             label: {
            //                                 fill: "#FFFFFF",
            //                                 fontSize: 14,
            //                                 fontWeight: 500,
            //                                 textAnchor: "middle",
            //                                 verticalAnchor: "middle"
            //                             }
            //                         },
            //                         ports: {
            //                             groups: {
            //                                 out: {
            //                                     position: "bottom",
            //                                     attrs: {
            //                                         circle: {
            //                                             r: 1,
            //                                             magnet: false,
            //                                             stroke: "none",
            //                                             strokeWidth: 1,
            //                                             fill: "none"
            //                                         }
            //                                     }
            //                                 }
            //                             },
            //                             items: Array.from({length: leafNodesDetailedInfo.length}, (_, index) => {
            //                                 return {
            //                                     id: `superSpinePort-${index}`,
            //                                     group: "out"
            //                                 };
            //                             })
            //                         },
            //                         zIndex: 1
            //                     }).id
            //                 );
            //             }
            //         }
            //     }
            // }

            //     if (dottedFrameX !== 70) {
            //         graphRef.current.addEdge({
            //             id: "splitter1",
            //             source: {x: 10, y: 420},
            //             target: {x: dottedFrameX + 10, y: 420},
            //             attrs: {
            //                 line: {
            //                     stroke: "#B4BECD",
            //                     strokeWidth: 1,
            //                     strokeDasharray: "10, 5",
            //                     targetMarker: null,
            //                     sourceMarker: null
            //                 }
            //             }
            //         });
            //     }
        }

        graphRef.current.centerContent();

        graphRef.current.startBatch("addListener");
        graphRef.current.on("node:mouseenter", mouseEnterNode);
        graphRef.current.on("node:mouseleave", clearAllTooltips);
        graphRef.current.on("edge:mouseenter", mouseEnterEdge);
        graphRef.current.on("edge:mouseleave", clearAllTooltips);
        graphRef.current.stopBatch("addListener");

        return () => {
            graphRef.current.startBatch("dispose");
            graphRef.current.off("edge:mouseleave", clearAllTooltips);
            graphRef.current.off("edge:mouseenter", mouseEnterEdge);
            graphRef.current.off("node:mouseleave", clearAllTooltips);
            graphRef.current.off("node:mouseenter", mouseEnterNode);
            graphRef.current.dispose();
            graphRef.current.stopBatch("dispose");
            graphRef.current = null;
        };
    }, [unitNodes, podNodes, type, spineCount, superSpineCount]);

    useEffect(() => {
        return () => {
            if (warningMessageRef.current) {
                warningMessageRef.current();
                warningMessageRef.current = null;
            }
        };
    }, []);

    return (
        <>
            <div
                style={{
                    position: "relative",
                    flex: "1",
                    width: "100%",
                    height: "100%"
                }}
            >
                <div ref={containerRef} style={{width: "100%", height: "100%"}} />
                <div
                    ref={miniMapContainerRef}
                    style={{
                        position: "absolute",
                        right: 0,
                        bottom: 0,
                        zIndex: 999,
                        width: "200px",
                        height: "50px",
                        backgroundColor: "#F0F0F0"
                    }}
                />
            </div>
            <DeviceBriefTooltip ref={deviceBriefTooltipRef} offsetX={10} offsetY={10} topoType="dc_template" />
            <LinkBriefTooltip ref={linkBriefTooltipRef} offsetX={10} offsetY={10} isFabricTopo={isFabricTopo.current} />
        </>
    );
});

export default DCTemplatesTopo;
