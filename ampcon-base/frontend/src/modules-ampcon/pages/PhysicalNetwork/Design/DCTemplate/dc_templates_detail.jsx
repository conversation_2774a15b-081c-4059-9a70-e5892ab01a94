import React, {useRef, useEffect, useState} from "react";
import {useNavigate, useLocation} from "react-router-dom";
import {Divider, Space, Card, message, Form, Input, Radio, Select, Button, InputNumber} from "antd";
import Icon, {PlusOutlined, LineOutlined, ArrowLeftOutlined} from "@ant-design/icons";
import {deleteGreySvg, backUpGraySvg} from "@/utils/common/iconSvg";
import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/DCTemplate/dc_templates_form.module.scss";
import {addDCTemplateInfo, viewDCTemplateInfo} from "@/modules-ampcon/apis/dc_template_api";
import {fetchUnitListInfo} from "@/modules-ampcon/apis/unit_api";
import {getResourcePoolDropdownList} from "@/modules-ampcon/apis/resource_pool_api";
import DCTemplatesTopo from "./dc_templates_topo";

import CreatePoolModal from "@/modules-ampcon/pages/Resource/Pool/IpPool/create_pool_modal";
import CreateASNPoolModal from "@/modules-ampcon/pages/Resource/Pool/AsnPool/create_pool_modal";
import CreateAreaPoolModal from "@/modules-ampcon/pages/Resource/Pool/AreaPool/create_area_pool_modal";

// 自定义防抖函数（支持取消）
const debounce = (func, wait) => {
    let timeout;
    const debounced = (...args) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
    debounced.cancel = () => clearTimeout(timeout);
    return debounced;
};

const DCTemplatesDetail = () => {
    const NAME_MATCH_REGEX = /^[\w-]+$/;
    const navigate = useNavigate();
    const {state} = useLocation(); // 访问整个 state
    const templateFormData = state?.data; // 安全提取 record，默认值为 undefined
    const [templateTitle, setTemplateTitle] = useState(state?.actionType);
    const [editDisabled, setEditDisabled] = useState(false);

    const [routingProtocalList, setRoutingProtocalList] = useState(["BGP", "OSPF"]);
    const [form] = Form.useForm(); // 创建 Form 实例

    const [type, setStageValue] = useState("3-stage");
    const [name, setTemplateName] = useState("");
    const [description, setDescription] = useState("");
    const [super_spine_count, setSuperSpineCount] = useState("");
    const [spine_count, setSpinedCount] = useState(1);
    const [unit, setUnits] = useState([{id: "", name: "", count: 1, unit_info: {}}]);

    const [pod, setPods] = useState([
        {
            name: "",
            spine_count: "",
            link_pre_superspine_count: 1,
            unit: [{id: "", name: "", count: "", unit_info: {}}]
        }
    ]);

    const [super_spine_areaid, setSuperSpineAreaID] = useState("");
    const [pods_areaid, setPodsAreaid] = useState("");
    const [dcTemplateData, setDCTemplateData] = useState({
        name: "",
        overlay_control_protocol: "MP-BGP EVPN",
        template_info: {
            areaid: "0.0.0.0",
            bgp_router_id: "",
            overlay_ibgp_asn: "",
            pod: [
                {
                    name: "",
                    spine_count: "",
                    link_pre_superspine_count: 1,
                    unit: [{id: "", name: "", count: "", unit_info: {}}]
                }
            ],
            pods_areaid: "",
            spine_count: 1,
            super_spine_areaid: "",
            super_spine_count: "",
            type: "3-stage",
            underlay_ebgp_asn: "",
            underlay_routing_protocol: "",
            unit: [
                {
                    count: 1,
                    id: "",
                    name: "",
                    unit_info: {}
                }
            ],
            vtep_interface: ""
        },
        type: "3-stage",
        underlay_routing_protocol: "BGP"
    });

    const [unitDropdownList, setUnitDropdownList] = useState([]);
    const [resourcePoolDropdownList, setResourcePoolDropdownList] = useState({});

    const createPoolModalRef = useRef(null);
    const createASNPoolModalRef = useRef(null);
    const createAreaPoolModalRef = useRef(null);

    const DCTemplateTopoRef = useRef(null);

    // Spine count 防抖处理
    const debouncedSetSpineCount = useRef(
        debounce(value => {
            setSpinedCount(value);
        }, 500)
    ).current;

    // Unit count 防抖映射表
    const unitDebounceMap = useRef({});

    // 初始化 Unit 防抖器
    useEffect(() => {
        unit.forEach((_, index) => {
            if (!unitDebounceMap.current[index]) {
                unitDebounceMap.current[index] = debounce(value => {
                    unitCount(index, value);
                }, 500);
            }
        });
    }, [unit.length]);

    // 清理所有防抖器
    useEffect(() => {
        return () => {
            // 清理 spine count
            debouncedSetSpineCount.cancel();
            // 清理 unit counts
            Object.values(unitDebounceMap.current).forEach(debouncer => {
                debouncer.cancel();
            });
        };
    }, []);

    // 使用传递的数据设置表单值
    React.useEffect(() => {
        const fetchData = async () => {
            if (templateTitle === "View") {
                await getDCTemplateInfo();
                setEditDisabled(true);
            }
            if (templateTitle === "Edit") {
                await getDCTemplateInfo();
                setEditDisabled(false);
            }
            if (templateTitle === "Create") {
                const currentValues = form.getFieldsValue();
                const updatedValues = {...currentValues, ...dcTemplateData};
                form.setFieldsValue(updatedValues);
            }
            await getUnitList();
            await getResourcePoolList();
        };
        fetchData();
    }, []);

    const getDCTemplateInfo = async () => {
        const result = await viewDCTemplateInfo({template_id: templateFormData.id});
        if (result.status === 200) {
            const updatedData = {...dcTemplateData, ...result.data};
            if (updatedData.underlay_routing_protocol === "OSPF") {
                updatedData.template_info.areaid = "0.0.0.0";
            }
            form.setFieldsValue(updatedData);
            setDCTemplateData(updatedData);

            if (result.data.template_info.unit && result.data.template_info.unit.length > 0) {
                setUnits(result.data.template_info.unit);
                result.data.template_info.unit.forEach((item, index) => {
                    form.setFieldsValue({[`unit_${index}`]: item.name});
                });
            }
            if (result.data.template_info.pod && result.data.template_info.pod.length > 0) {
                setPods(result.data.template_info.pod);
            }

            if (result.data.template_info.spine_count && result.data.template_info.spine_count > 0) {
                setSpinedCount(result.data?.template_info.spine_count);
            }
        }
    };
    const getResourcePoolList = async () => {
        const result = await getResourcePoolDropdownList({poolTypeList: ["asn", "area", "ipv4", "ip"]});
        if (result.status === 200) {
            setResourcePoolDropdownList(result.data);
        }
    };

    const getUnitList = async () => {
        let allData = [];
        let currentPage = 1;
        let total = 0;

        do {
            const response = await fetchUnitListInfo(currentPage, 10);

            if (response.status === 200) {
                total = response.total;
                const currentPageData = response.data;
                allData = [...allData, ...currentPageData];
                currentPage++;
            } else {
                message.error(response.info);
                return;
            }
        } while (allData.length < total);

        setUnitDropdownList(allData);
    };

    // 更新form数据
    const handleFormChange = (name1, name2, value, key) => {
        const formData = form.getFieldValue() || [];
        if (name1) {
            formData[name1][name2] = value;
        } else {
            formData[name2] = value;
        }
        if (name2 === "underlay_routing_protocol") formData.template_info.areaid = "0.0.0.0";
        setDCTemplateData(formData);
        form.setFieldsValue(formData);
    };

    // 更新对应索引的 Unit 值
    const handleUnitChange = (index, value) => {
        const unitListCheckValue = unitDropdownList.find(item => item.id === value);
        const newUnits = [...unit];
        newUnits[index] = {
            id: value,
            name: unitListCheckValue.name,
            count: unit[index].count ? unit[index].count : 1,
            unit_info: unitListCheckValue.unit_info
        };
        setUnits(newUnits);
        form.setFieldsValue({[`unit_${index}`]: value});
    };
    const unitCount = (index, value) => {
        setUnits(prevUnits => {
            const newUnits = [...prevUnits];
            newUnits[index].count = value;
            return newUnits;
        });
    };
    // 删除指定索引的 unit
    const removeUnit = index => {
        if (unit.length > 1) {
            const newUnits = unit.filter((_, i) => i !== index);
            setUnits(newUnits);
        }
    };

    // 新增一条新的 Pod
    const addNewPod = () => {
        const newPod = {
            name: `pod ${parseInt(pod.length + 1)}`,
            spine_count: "",
            link_pre_superspine_count: "",
            unit: [{id: "", name: "", count: "", unit_info: {}}]
        };
        setPods([...pod, newPod]);
    };
    // 克隆一条新的 Pod
    const clonePod = index => {
        const currentPod = pod[index];
        const newPod = {
            ...currentPod,
            name: `pod${parseInt(pod.length + 1)}`,
            unit: [...currentPod.unit]
        };
        setPods([...pod, newPod]);
    };
    // 删除指定 pod 中的 unit，并确保至少有一个 Pod
    const removePod = index => {
        if (pod.length > 1) {
            const newPods = pod.filter((_, i) => i !== index);
            setPods(newPods);
        }
    };
    // 更新指定 Pod 的 unit 值
    const handlePodUnitChange = (podIndex, unitIndex, value) => {
        const newPods = [...pod];
        newPods[podIndex].name = `pod${parseInt(podIndex + 1)}`;
        const unitListCheckValue = unitDropdownList.find(item => item.id === value);
        newPods[podIndex].unit[unitIndex] = {
            id: value,
            name: unitListCheckValue.name,
            unit_info: unitListCheckValue.unit_info
        };
        setPods(newPods);
    };
    // 更新指定pod中指定count
    const handlePodUnitCount = (podIndex, unitIndex, unitId, value) => {
        const newPods = [...pod];
        newPods[podIndex].name = `pod${parseInt(podIndex + 1)}`;
        const unitListCheckValue = unitDropdownList.find(item => item.id === unitId);
        newPods[podIndex].unit[unitIndex] = {
            id: unitId,
            name: unitListCheckValue.name,
            count: value,
            unit_info: unitListCheckValue.unit_info
        };
        setPods(newPods);
    };

    // 向指定 Pod 中添加新的 unit 输入
    const addPodUnit = (podIndex, unitIndex) => {
        const newPods = [...pod];
        newPods[podIndex].unit.push({id: "", name: "", count: "", unit_info: {}});
        setPods(newPods);
    };
    // 删除指定 pod 中的 unit
    const removePodUnit = (podIndex, unitIndex) => {
        const newPods = [...pod];
        newPods[podIndex].unit.splice(unitIndex, 1);
        setPods(newPods);
    };

    const mergeUnitsByIdAndContent = unitList => {
        const mergedMap = new Map();

        unitList.forEach(item => {
            const {id, name, unit_info} = item;
            if (id === "") return;

            const key = `${id}-${name}-${JSON.stringify(unit_info)}`;

            if (mergedMap.has(key)) {
                mergedMap.get(key).count += item.count;
            } else {
                mergedMap.set(key, {...item});
            }
        });

        return Array.from(mergedMap.values());
    };

    // 点击Save按钮，提交表单
    const handleFormSubmit = async values => {
        if (!DCTemplateTopoRef.current.handleIsSaveTopo()) {
            message.error("The number of nodes is too large, please reduce the number of nodes");
            return;
        }

        const mergedUnit = type === "3-stage" ? mergeUnitsByIdAndContent(unit) : [];

        const formData = {
            id: templateTitle === "Edit" ? templateFormData.id : "",
            name: values.name,
            description: values.description,
            type: values.type,
            underlay_routing_protocol: values.underlay_routing_protocol || "",
            overlay_control_protocol: values.overlay_control_protocol || "",
            template_info: {
                spine_count: type === "3-stage" ? values.template_info.spine_count : 1,
                super_spine_count: type === "5-stage" ? values.template_info.super_spine_count : "",
                unit: mergedUnit,
                pod: type === "5-stage" ? pod.filter(item => item.name !== "") : [],
                underlay_ebgp_asn:
                    values.underlay_routing_protocol === "BGP" ? values.template_info.underlay_ebgp_asn : "",
                areaid:
                    dcTemplateData.underlay_routing_protocol === "OSPF" && type === "3-stage"
                        ? values.template_info.areaid
                        : "",
                pods_areaid:
                    dcTemplateData.underlay_routing_protocol === "OSPF" && type === "5-stage"
                        ? values.template_info.pods_areaid
                        : "",
                super_spine_areaid:
                    dcTemplateData.underlay_routing_protocol === "OSPF" && type === "5-stage"
                        ? values.template_info.super_spine_areaid
                        : "",
                bgp_router_id: values.template_info.bgp_router_id || "",
                vtep_interface: values.template_info.vtep_interface || "",
                overlay_ibgp_asn: values.template_info.overlay_ibgp_asn || ""
            }
        };

        const ret = await addDCTemplateInfo(formData);
        if (ret.status === 200) {
            message.success(ret.info);
            navigate("/physical_network/design/dc_templates");
        } else {
            message.error(ret.info);
        }
    };

    return (
        <Card style={{display: "flex", flex: 1, position: "relative"}}>
            <h2 style={{marginTop: "8px"}}>{templateTitle} Template </h2>
            <div className={styles.editBox}>
                <div className={styles.addFabricBox}>
                    <Form
                        layout="horizontal"
                        form={form}
                        validateTrigger="onBlur"
                        labelAlign="left"
                        onFinish={handleFormSubmit}
                        className={styles.formBox}
                    >
                        <h3>Basic Info</h3>
                        <div className={styles.formItemsBox}>
                            <Form.Item name="type" label="Stage" labelCol={{style: {width: 175}}}>
                                <Radio.Group
                                    value={type}
                                    defaultValue="3-stage"
                                    onChange={e => {
                                        setStageValue(e.target.value);
                                    }}
                                    disabled={editDisabled}
                                >
                                    <Radio value="3-stage">3-stage</Radio>
                                    {/* <Radio value="5-stage">5-stage</Radio> */}
                                </Radio.Group>
                            </Form.Item>

                            <Form.Item
                                name="name"
                                label="Template Name"
                                labelCol={{style: {width: 175}}}
                                rules={[
                                    {required: true, message: "Please enter the  template name!"},
                                    {max: 64, message: "Enter a maximum of 64 characters"},
                                    {
                                        validator: (_, value) => {
                                            if (value === "All") {
                                                return Promise.reject(new Error("Please input a valid template name!"));
                                            }
                                            if (!NAME_MATCH_REGEX.test(value)) {
                                                return Promise.reject(
                                                    new Error(
                                                        "Template name can only contain letters, numbers, underscores, hyphens."
                                                    )
                                                );
                                            }
                                            return Promise.resolve();
                                        }
                                    }
                                ]}
                            >
                                <Input
                                    value={name}
                                    maxLength="64"
                                    disabled={editDisabled}
                                    placeholder="Template Name"
                                    className={styles.formWidth}
                                />
                            </Form.Item>

                            <Form.Item name="description" label="Description" labelCol={{style: {width: 175}}}>
                                <Input value={description} disabled={editDisabled} className={styles.formWidth} />
                            </Form.Item>

                            {type === "5-stage" && (
                                <>
                                    <h3>Superspine Layer</h3>
                                    <Form.Item
                                        name={["template_info", "super_spine_count"]}
                                        label="Superspine Count"
                                        labelCol={{style: {width: 175}}}
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (!value || /^\d+$/.test(value)) {
                                                        return Promise.resolve();
                                                    }
                                                    return Promise.reject(new Error("Please entry  a number"));
                                                }
                                            }
                                        ]}
                                    >
                                        <InputNumber
                                            value={super_spine_count}
                                            disabled={editDisabled}
                                            min={0}
                                            className={styles.formWidth}
                                            onChange={value => {
                                                setSuperSpineCount(value);
                                            }}
                                        />
                                    </Form.Item>
                                </>
                            )}

                            {type === "3-stage" && (
                                <>
                                    <h3>Spine Layer</h3>
                                    <Form.Item
                                        name={["template_info", "spine_count"]}
                                        label="Spine Count"
                                        labelCol={{style: {width: 175}}}
                                        rules={[
                                            {required: true, message: "Please enter the Spine Count!"},
                                            {
                                                validator: (_, value) => {
                                                    if (!value || /^\d+$/.test(value)) {
                                                        return Promise.resolve();
                                                    }
                                                    return Promise.reject(new Error("Please entry  a number"));
                                                }
                                            }
                                        ]}
                                    >
                                        <InputNumber
                                            value={spine_count}
                                            disabled={editDisabled}
                                            defaultValue="1"
                                            min={1}
                                            onChange={e => {
                                                debouncedSetSpineCount(e);
                                            }}
                                            className={styles.formWidth}
                                        />
                                    </Form.Item>

                                    <h3>Unit</h3>
                                    {unit.map((unitItem, index) => (
                                        <Form.Item
                                            label="Unit"
                                            key={index}
                                            name={`unit_${index}`}
                                            labelCol={{style: {width: 175}}}
                                            rules={[{required: true, message: "Please select a unit!"}]}
                                        >
                                            <Select
                                                value={unitItem.id}
                                                onChange={e => {
                                                    handleUnitChange(index, e);
                                                }}
                                                disabled={editDisabled}
                                                style={{width: 134}}
                                                options={unitDropdownList?.map(item => ({
                                                    value: item.id,
                                                    label: item.name
                                                }))}
                                            />
                                            <InputNumber
                                                value={unitItem.count}
                                                disabled={editDisabled || unitItem.id === ""}
                                                defaultValue="1"
                                                min={1}
                                                onChange={e => {
                                                    unitDebounceMap.current[index]?.(e);
                                                }}
                                                style={{width: 134, marginLeft: 12}}
                                            />

                                            {!editDisabled && (
                                                <Space>
                                                    {index === 0 ? (
                                                        <PlusOutlined
                                                            onClick={() => {
                                                                setUnits([
                                                                    ...unit,
                                                                    {id: "", name: "", count: 1, unit_info: {}}
                                                                ]);
                                                            }}
                                                            style={{marginLeft: 8}}
                                                        />
                                                    ) : (
                                                        <LineOutlined
                                                            onClick={() => removeUnit(index)}
                                                            style={{marginLeft: 8}}
                                                        />
                                                    )}
                                                </Space>
                                            )}
                                        </Form.Item>
                                    ))}
                                </>
                            )}
                            {type === "5-stage" && (
                                <>
                                    <h3>Pods</h3>
                                    <div className={styles.podContainerBox}>
                                        {pod.map((podItem, podIndex) => (
                                            <div key={podIndex} className={styles.podContainer}>
                                                <h4>
                                                    <span>pod {podIndex + 1}</span>
                                                    {!editDisabled && (
                                                        <i>
                                                            <Icon
                                                                component={backUpGraySvg}
                                                                onClick={() => clonePod(podIndex)}
                                                                style={{marginLeft: 8, cursor: "pointer"}}
                                                            />
                                                            {podIndex > 0 && (
                                                                <Icon
                                                                    component={deleteGreySvg}
                                                                    onClick={() => removePod(podIndex)}
                                                                    style={{marginLeft: 8, cursor: "pointer"}}
                                                                />
                                                            )}
                                                        </i>
                                                    )}
                                                </h4>
                                                <Form.Item
                                                    label="Spine Count"
                                                    labelCol={{style: {width: 150}}}
                                                    rules={[
                                                        {
                                                            validator: (_, value) => {
                                                                if (!value || /^\d+$/.test(value)) {
                                                                    return Promise.resolve();
                                                                }
                                                                return Promise.reject(
                                                                    new Error("Please enter a valid number")
                                                                );
                                                            }
                                                        }
                                                    ]}
                                                >
                                                    <InputNumber
                                                        value={podItem.spine_count}
                                                        disabled={editDisabled}
                                                        className={styles.formWidth}
                                                        onChange={value => {
                                                            const newPods = [...pod];
                                                            newPods[podIndex].name = `pod${podIndex + 1}`;
                                                            newPods[podIndex].spine_count = value;
                                                            setPods(newPods);
                                                        }}
                                                    />
                                                </Form.Item>

                                                {podItem.unit.map((unit, unitIndex) => (
                                                    <Form.Item
                                                        label="Unit"
                                                        key={unitIndex}
                                                        labelCol={{style: {width: 150}}}
                                                        rules={[{required: true, message: "Please select a unit!"}]}
                                                    >
                                                        <Select
                                                            value={unit.id}
                                                            onChange={e => {
                                                                handlePodUnitChange(podIndex, unitIndex, e);
                                                            }}
                                                            disabled={editDisabled}
                                                            style={{width: 134}}
                                                            options={unitDropdownList?.map(item => ({
                                                                value: item.id,
                                                                label: item.name
                                                            }))}
                                                        />
                                                        <InputNumber
                                                            value={unit.count}
                                                            disabled={editDisabled || unit.id === ""}
                                                            defaultValue="0"
                                                            min={0}
                                                            onChange={e => {
                                                                handlePodUnitCount(podIndex, unitIndex, unit.id, e);
                                                            }}
                                                            style={{width: 134, marginLeft: 12}}
                                                        />
                                                        {!editDisabled && (
                                                            <Space>
                                                                {unitIndex === 0 ? (
                                                                    <PlusOutlined
                                                                        onClick={() => {
                                                                            addPodUnit(podIndex, unitIndex);
                                                                        }}
                                                                        style={{marginLeft: 8, cursor: "pointer"}}
                                                                    />
                                                                ) : (
                                                                    <LineOutlined
                                                                        onClick={() =>
                                                                            removePodUnit(podIndex, unitIndex)
                                                                        }
                                                                        style={{marginLeft: 8, cursor: "pointer"}}
                                                                    />
                                                                )}
                                                            </Space>
                                                        )}
                                                    </Form.Item>
                                                ))}
                                            </div>
                                        ))}
                                    </div>
                                    {!editDisabled && (
                                        <button
                                            type="button"
                                            onClick={() => {
                                                addNewPod();
                                            }}
                                            className={styles.addNewPodBtn}
                                        >
                                            <PlusOutlined style={{marginRight: 8}} />
                                            Add
                                        </button>
                                    )}
                                </>
                            )}

                            <h3>Underlay</h3>
                            <Form.Item
                                name="underlay_routing_protocol"
                                label="Routing Protocol"
                                labelCol={{style: {width: 175}}}
                                rules={[{required: true, message: "Please select the Routing Protocol!"}]}
                            >
                                <Select
                                    defaultValue="BGP"
                                    onChange={e => {
                                        handleFormChange("", "underlay_routing_protocol", e);
                                    }}
                                    disabled={editDisabled}
                                    style={{width: 280}}
                                    options={[
                                        {value: "BGP", label: "BGP"},
                                        {value: "OSPF", label: "OSPF"}
                                    ]}
                                />
                            </Form.Item>

                            {dcTemplateData.underlay_routing_protocol === "OSPF" && type === "3-stage" && (
                                <Space>
                                    <Form.Item
                                        name={["template_info", "areaid"]}
                                        label="Area ID"
                                        labelCol={{style: {width: 175}}}
                                    >
                                        <Input defaultValue="0.0.0.0" disabled className={styles.formWidth} />
                                    </Form.Item>
                                </Space>
                            )}

                            {dcTemplateData.underlay_routing_protocol === "OSPF" && type === "5-stage" && (
                                <>
                                    <Form.Item
                                        name={["template_info", "super_spine_areaid"]}
                                        label="Superspine Area ID"
                                        labelCol={{style: {width: 175}}}
                                    >
                                        <Select
                                            value={super_spine_areaid}
                                            onChange={e => {
                                                handleFormChange("template_info", "super_spine_areaid", e);
                                            }}
                                            disabled={editDisabled}
                                            allowClear
                                            style={{width: 280}}
                                            dropdownRender={menu => (
                                                <>
                                                    {menu}
                                                    <Divider style={{margin: "8px 0"}} />
                                                    <Button
                                                        type="text"
                                                        icon={
                                                            <PlusOutlined
                                                                display={
                                                                    resourcePoolDropdownList?.super_spine_areaid
                                                                        ?.length >= 0
                                                                }
                                                            />
                                                        }
                                                        className={styles.selectCreateBtn}
                                                        onClick={() => {
                                                            createAreaPoolModalRef.current.showCreateAreaPoolModal();
                                                        }}
                                                    >
                                                        Add Pool
                                                    </Button>
                                                </>
                                            )}
                                            options={resourcePoolDropdownList?.area?.map(item => ({
                                                value: item.id,
                                                label: item.name
                                            }))}
                                        />
                                    </Form.Item>

                                    <Form.Item
                                        name={["template_info", "pods_areaid"]}
                                        label="Pods Area ID"
                                        labelCol={{style: {width: 175}}}
                                    >
                                        <Select
                                            value={pods_areaid}
                                            onChange={e => {
                                                handleFormChange("template_info", "pods_areaid", e);
                                            }}
                                            disabled={editDisabled}
                                            allowClear
                                            style={{width: 280}}
                                            dropdownRender={menu => (
                                                <>
                                                    {menu}
                                                    <Divider style={{margin: "8px 0"}} />
                                                    <Button
                                                        type="text"
                                                        icon={
                                                            <PlusOutlined
                                                                display={
                                                                    resourcePoolDropdownList?.pods_areaid?.length >= 0
                                                                }
                                                            />
                                                        }
                                                        className={styles.selectCreateBtn}
                                                        onClick={() => {
                                                            createAreaPoolModalRef.current.showCreateAreaPoolModal();
                                                        }}
                                                    >
                                                        Add Pool
                                                    </Button>
                                                </>
                                            )}
                                            options={resourcePoolDropdownList?.area?.map(item => ({
                                                value: item.id,
                                                label: item.name
                                            }))}
                                        />
                                    </Form.Item>
                                </>
                            )}

                            {dcTemplateData.underlay_routing_protocol === "BGP" && (
                                <Space>
                                    <Form.Item
                                        name={["template_info", "underlay_ebgp_asn"]}
                                        label="EBGP ASN"
                                        rules={[{required: true, message: "Please select the  EBGP ASN!"}]}
                                        labelCol={{style: {width: 175}}}
                                    >
                                        <Select
                                            onChange={e => {
                                                handleFormChange("template_info", "underlay_ebgp_asn", e);
                                            }}
                                            disabled={editDisabled}
                                            allowClear
                                            style={{width: 280}}
                                            dropdownRender={menu => (
                                                <>
                                                    {menu}
                                                    <Divider style={{margin: "8px 0"}} />
                                                    <Button
                                                        type="text"
                                                        icon={
                                                            <PlusOutlined
                                                                display={resourcePoolDropdownList?.asn?.length >= 0}
                                                            />
                                                        }
                                                        className={styles.selectCreateBtn}
                                                        onClick={() => {
                                                            createASNPoolModalRef.current.showCreatePoolModal();
                                                        }}
                                                    >
                                                        Add Pool
                                                    </Button>
                                                </>
                                            )}
                                            options={resourcePoolDropdownList?.asn?.map(item => ({
                                                value: item.id,
                                                label: item.name
                                            }))}
                                        />
                                    </Form.Item>
                                </Space>
                            )}

                            <Form.Item
                                name={["template_info", "bgp_router_id"]}
                                label="Router ID"
                                rules={[{required: true, message: "Please select the  Router ID!"}]}
                                labelCol={{style: {width: 175}}}
                            >
                                <Select
                                    onChange={e => {
                                        handleFormChange("template_info", "bgp_router_id", e);
                                    }}
                                    disabled={editDisabled}
                                    allowClear
                                    style={{width: 280}}
                                    dropdownRender={menu => (
                                        <>
                                            {menu}
                                            <Divider style={{margin: "8px 0"}} />
                                            <Button
                                                type="text"
                                                icon={
                                                    <PlusOutlined
                                                        display={resourcePoolDropdownList?.router_id?.length >= 0}
                                                    />
                                                }
                                                className={styles.selectCreateBtn}
                                                onClick={() => {
                                                    createPoolModalRef.current.showCreatePoolModal();
                                                }}
                                            >
                                                Add Pool
                                            </Button>
                                        </>
                                    )}
                                    options={resourcePoolDropdownList?.ipv4?.map(item => ({
                                        value: item.id,
                                        label: item.name
                                    }))}
                                />
                            </Form.Item>

                            <Form.Item
                                name={["template_info", "vtep_interface"]}
                                label="VTEP Interface"
                                rules={[{required: true, message: "Please select the VTEP Interface!"}]}
                                labelCol={{style: {width: 175}}}
                            >
                                <Select
                                    onChange={e => {
                                        handleFormChange("template_info", "vtep_interface", e);
                                    }}
                                    disabled={editDisabled}
                                    allowClear
                                    style={{width: 280}}
                                    dropdownRender={menu => (
                                        <>
                                            {menu}
                                            <Divider style={{margin: "8px 0"}} />
                                            <Button
                                                type="text"
                                                icon={
                                                    <PlusOutlined
                                                        display={resourcePoolDropdownList?.vtep_interface?.length >= 0}
                                                    />
                                                }
                                                className={styles.selectCreateBtn}
                                                onClick={() => {
                                                    createPoolModalRef.current.showCreatePoolModal();
                                                }}
                                            >
                                                Add Pool
                                            </Button>
                                        </>
                                    )}
                                    options={resourcePoolDropdownList?.ipv4?.map(item => ({
                                        value: item.id,
                                        label: item.name
                                    }))}
                                />
                            </Form.Item>

                            {dcTemplateData.underlay_routing_protocol === "OSPF" && (
                                <Form.Item
                                    name={["template_info", "overlay_ibgp_asn"]}
                                    label="Overlay IBGP ASN"
                                    rules={[{required: true, message: "Please select the Overlay IBGP ASN!"}]}
                                    labelCol={{style: {width: 175}}}
                                >
                                    <Select
                                        onChange={e => {
                                            handleFormChange("template_info", "overlay_ibgp_asn", e);
                                        }}
                                        disabled={editDisabled}
                                        allowClear
                                        style={{width: 280}}
                                        dropdownRender={menu => (
                                            <>
                                                {menu}
                                                <Divider style={{margin: "8px 0"}} />
                                                <Button
                                                    type="text"
                                                    icon={
                                                        <PlusOutlined
                                                            display={resourcePoolDropdownList?.asn?.length >= 0}
                                                        />
                                                    }
                                                    className={styles.selectCreateBtn}
                                                    onClick={() => {
                                                        createASNPoolModalRef.current.showCreatePoolModal();
                                                    }}
                                                >
                                                    Add Pool
                                                </Button>
                                            </>
                                        )}
                                        options={resourcePoolDropdownList?.asn?.map(item => ({
                                            value: item.id,
                                            label: item.name
                                        }))}
                                    />
                                </Form.Item>
                            )}

                            <Form.Item
                                name="overlay_control_protocol"
                                label="Overlay Control Protocol"
                                labelCol={{style: {width: 175}}}
                            >
                                <Radio.Group
                                    defaultValue="MP-BGP EVPN"
                                    onChange={e => {
                                        handleFormChange("", "overlay_control_protocol", e.target.value);
                                    }}
                                    disabled={editDisabled}
                                >
                                    <Radio value="MP-BGP EVPN">MP-BGP EVPN</Radio>
                                </Radio.Group>
                            </Form.Item>
                        </div>
                    </Form>

                    <div className={styles.topology}>
                        <h3>Topology</h3>
                        <div id="topology_container" style={{width: "100%", height: "calc(100% - 75px)"}}>
                            <DCTemplatesTopo
                                ref={DCTemplateTopoRef}
                                unitNodes={unit}
                                podNodes={pod}
                                type={type}
                                spineCount={spine_count}
                                superSpineCount={super_spine_count}
                            />
                        </div>
                    </div>
                </div>
            </div>

            <div className={styles.button_box}>
                <Button
                    key="cancel"
                    onClick={() => {
                        navigate("/physical_network/design/dc_templates");
                    }}
                >
                    Cancel
                </Button>
                {editDisabled ? (
                    <Button
                        key="Edit"
                        type="primary"
                        style={{margin: "0 16px"}}
                        onClick={() => {
                            setEditDisabled(false);
                            setTemplateTitle("Edit");
                        }}
                    >
                        Edit
                    </Button>
                ) : (
                    <Button key="Save" type="primary" style={{margin: "0 16px"}} onClick={form.submit}>
                        Apply
                    </Button>
                )}
            </div>

            <CreatePoolModal
                ref={createPoolModalRef}
                saveCallback={() => {
                    getResourcePoolList();
                }}
            />
            <CreateASNPoolModal
                ref={createASNPoolModalRef}
                saveCallback={() => {
                    getResourcePoolList();
                }}
            />
            <CreateAreaPoolModal
                ref={createAreaPoolModalRef}
                saveCallback={() => {
                    getResourcePoolList();
                }}
            />
        </Card>
    );
};

export default DCTemplatesDetail;
