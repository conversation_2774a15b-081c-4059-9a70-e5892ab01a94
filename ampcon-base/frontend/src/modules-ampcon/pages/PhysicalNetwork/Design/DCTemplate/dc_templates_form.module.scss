h3:nth-child(1) {
    margin: 0 0 24px 0;
}

h3 {
    font-size: 18px;
    margin: 40px 0 24px 0;
    font-weight: 700;
    color: #212519;
    text-align: left;
    font-style: normal;
    text-transform: none;
}

.formWidth {
    width: 280px !important;
}

.createBtn {
    margin-left: 164px;

    button {
        margin-right: 16px;
    }
}

.editBox {
    overflow-y: hidden;
    // height: calc(100vh - 282px);
    margin-bottom: 20px;
}

.formBox {
    width: 505px;
    // height: calc(100vh - 292px);
    // overflow-y: auto
}

.formItemsBox {
    height: calc(100vh - 331px);
    overflow-y: auto;
}

.podContainerBox {
    margin-bottom: 24px;
}

.addNewPodBtn {
    width: 100%;
    height: 48px;
    border-radius: 4px 4px 4px 4px;
    border: 1px dotted #14C9BB;
    color: #14C9BB;
    display: block;
    background: none;
    cursor: pointer;
    margin-top: 24px;
}

.podContainer {
    width: 500px;
    padding: 24px 20px;
    border: 1px dotted #DCDCDC;
    margin-bottom: 24px;

    h4 {
        font-size: 18px;
        margin: 0 0 24px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}

.addFabricBox {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    gap: 60px;
}

.topology {
    width: calc(100% - 570px);
    min-width: 500px;
    min-height: 500px;
}

.button_box {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #E7E7E7;
    padding: 16px 0;
    background: #fff;

}

.selectCreateBtn {
    border: 1px solid #B2B2B2;
    color: #B2B2B2;
    width: calc(100% - 24px);
    margin: 8px 12px;
    background: #fff;
}

.selectCreateBtn:hover {
    background: rgba(20, 201, 187, 0.1) !important;
}

@media screen and (max-width: 1200px) {
    .editBox {
        overflow-y: auto;
    }

    .formBox {
        height: 100%;
        overflow-y: auto
    }
}
