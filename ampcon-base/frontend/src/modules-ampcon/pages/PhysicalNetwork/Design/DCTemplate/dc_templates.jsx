import React, {useEffect, useState} from "react";
import {useNavigate} from "react-router-dom";
import {Space, Table, Button, message, Card, Modal, Form, Input, Divider, Row} from "antd";
import Icon from "@ant-design/icons";
import {
    fetchDCTemplateInfo,
    cloneDCTemplateInfo,
    deleteDCTemplateInfo,
    viewDCTemplateInfo
} from "@/modules-ampcon/apis/dc_template_api";
import {
    TableFilterDropdown,
    handleTableChange,
    createMatchMode,
    createColumnConfig,
    GlobalSearchInput,
    createFilterFields
} from "@/modules-ampcon/components/custom_table";
import styles from "@/modules-ampcon/pages/Topo/Topology/topo.module.scss";
import {useTableInitialElement} from "@/modules-ampcon/hooks/useModalTable";
import {addSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import DCTemplatesTopo from "./dc_templates_topo";

const DCTemplates = () => {
    const navigate = useNavigate();
    const [
        isModalOpen,
        setIsModalOpen,
        searchFields,
        setSearchFields,
        data,
        setData,
        loading,
        setLoading,
        pagination,
        setPagination
    ] = useTableInitialElement([], true);

    const [cloneModalOpen, setCloneModalOpen] = useState(false);
    const [topoModalOpen, setTopoModalOpen] = useState(false);

    const [topoData, setTopoData] = useState({});
    const [groups, setGroups] = useState([]);
    const [groupSelects, setGroupSelects] = useState([]);

    const checkSortedColumn = columns => {
        for (const columnKey in columns) {
            if (Object.prototype.hasOwnProperty.call(columns, columnKey)) {
                const columnConfig = columns[columnKey];
                if (columnConfig.defaultSortOrder !== null) {
                    return [columnConfig.dataIndex, columnConfig.defaultSortOrder];
                }
            }
        }
        return [undefined, undefined];
    };
    const [sorter, setSorter] = useState({});
    const [filters, setFilters] = useState({});

    const fetchData = async () => {
        setLoading(true);

        const filterFields = filters ? createFilterFields(filters, matchModes) : [];
        const sortFields = [];
        if (sorter.field && sorter.order) {
            sortFields.push({
                field: sorter.field,
                order: sorter.order === "ascend" ? "asc" : "desc"
            });
        }

        try {
            const response = await fetchDCTemplateInfo(
                pagination.current,
                pagination.pageSize,
                filterFields,
                sortFields,
                searchFields
            );

            setGroups(response.allGroups);
            setData(response.data);
            setPagination(prev => ({
                ...prev,
                total: response.total,
                current: response.page,
                pageSize: response.pageSize
            }));
        } catch (error) {
            // error
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchData().then(() => {
            const [sortedColumn, sortedOrder] = checkSortedColumn(userColumns);
            if (sortedColumn) {
                sorter.field = sortedColumn;
                sorter.order = sortedOrder;
                tableChange("", "", sorter);
            }
        });
    }, []);

    useEffect(() => {
        fetchData().then();
    }, [searchFields]);

    const edit_template = record => {
        navigate(`/physical_network/design/dc_templates/${record.name}`, {
            state: {actionType: "Edit", data: record}
        });
    };

    const view_template = record => {
        navigate(`/physical_network/design/dc_templates/${record.name}`, {
            state: {actionType: "View", data: record}
        });
    };

    const [cloneData, setCloneData] = useState({});
    const clone_template = async formData => {
        const ret = await cloneDCTemplateInfo(formData);
        if (ret.status === 200) {
            setGroupSelects([]);
            message.success(ret.info);
            setCloneModalOpen(false);
        } else {
            message.error(ret.info);
        }
        fetchData();
    };

    const delete_template = async data => {
        const ret = await deleteDCTemplateInfo({template_id: data});
        if (ret.status === 200) {
            message.success(ret.info);
        } else {
            message.error(ret.info);
        }
        await fetchData();
    };

    const handleSearchChange = e => {
        setSearchFields({
            fields: ["name"],
            value: e.target.value
        });
    };

    const userColumns = [
        {
            ...createColumnConfig("Template Name", "name"),
            render: (text, record) => (
                <Space>
                    <a
                        onClick={() => {
                            setTopoModalOpen(true);
                            setTopoData(record);
                        }}
                        style={{color: "#14C9BB", textDecoration: "none"}}
                    >
                        {text}
                    </a>
                </Space>
            )
        },
        {title: "Description", dataIndex: "description", width: 200},
        {title: "Type", dataIndex: "type"},
        {...createColumnConfig("Underlay Routing Protocol", "underlay_routing_protocol")},
        {...createColumnConfig("Overlay Control Protocol", "overlay_control_protocol")},

        {
            title: "Operation",
            render: (_, record) => (
                <Space size="middle" className={styles.actionLink}>
                    <a onClick={() => edit_template(record)}>Edit</a>
                    <a onClick={() => view_template(record)}>View</a>
                    <a
                        onClick={() => {
                            setCloneModalOpen(true);
                            setCloneData(record);
                        }}
                    >
                        Copy
                    </a>
                    <a
                        onClick={() =>
                            confirmModalAction("Are you sure you want to delete the template name?", () =>
                                delete_template(record.id)
                            )
                        }
                    >
                        Delete
                    </a>
                </Space>
            )
        }
    ];

    const matchModes = createMatchMode([
        {name: "name", matchMode: "exact"},
        {name: "type", matchMode: "fuzzy"},
        {name: "description", matchMode: "fuzzy"},
        {name: "underlayRoutingProtocol", matchMode: "fuzzy"},
        {name: "overlayControlProtocol", matchMode: "fuzzy"}
    ]);

    const tableChange = async (pagination, filters, sorter) => {
        setSorter(sorter);
        setFilters(filters);
        await handleTableChange(
            pagination,
            filters,
            sorter,
            setPagination,
            searchFields,
            fetchDCTemplateInfo,
            "",
            setData,
            matchModes,
            setLoading
        );
    };

    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{margin: "8px 0 20px"}}>DC Templates</h2>
            <Space size={16} style={{marginBottom: "20px"}}>
                <Button
                    type="primary"
                    block
                    onClick={() =>
                        navigate(`/physical_network/design/dc_templates/create`, {state: {actionType: "Create"}})
                    }
                >
                    <Icon component={addSvg} />
                    Template
                </Button>
            </Space>
            <GlobalSearchInput onChange={handleSearchChange} />
            <div>
                <Table
                    columns={userColumns}
                    bordered
                    rowKey={record => record.id}
                    loading={loading}
                    dataSource={data}
                    pagination={pagination}
                    onChange={tableChange}
                />
            </div>

            <CloneModal
                title="Copy Template"
                cloneModalOpen={cloneModalOpen}
                setCloneModalOpen={setCloneModalOpen}
                onCancel={() => setCloneModalOpen(false)}
                cloneData={cloneData}
                onSubmit={clone_template}
                modalClass="ampcon-middle-modal"
            />

            <TopoModal
                title="View Topology"
                topoModalOpen={topoModalOpen}
                setTopoModalOpen={setTopoModalOpen}
                onCancel={() => setTopoModalOpen(false)}
                topoData={topoData}
                modalClass="ampcon-max-modal"
            />
        </Card>
    );
};

export default DCTemplates;

const CloneModal = ({
    title,
    cloneModalOpen,
    // setCloneModalOpen,
    modalClass,
    cloneData,
    onCancel,
    onSubmit
}) => {
    const [form] = Form.useForm();
    const NAME_MATCH_REGEX = /^[\s\w:-]+$/;
    useEffect(() => {
        if (cloneData) {
            form.setFieldsValue({
                clone_template_id: cloneData.id,
                name: cloneData.name,
                description: cloneData.description
            });
        }
    }, [cloneData, form]);

    const handleOk = async () => {
        const formData = await form.validateFields();
        await onSubmit(formData);
    };

    return (
        <Space>
            <Modal
                className="ampcon-middle-modal"
                title={
                    <div>
                        {title}
                        <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                open={cloneModalOpen}
                onCancel={onCancel}
                footer={
                    <>
                        <Divider style={{marginBottom: "20px", marginTop: "0px"}} />
                        <Row justify="end">
                            <Space>
                                <Button onClick={onCancel}> Cancel </Button>
                                <Button type="primary" onClick={handleOk}>
                                    Apply
                                </Button>
                            </Space>
                        </Row>
                    </>
                }
            >
                <Form form={form} labelAlign="left" style={{minHeight: "267.23px"}}>
                    <Form.Item name="clone_template_id" label="Clone ID" labelCol={{style: {width: 164}}}>
                        <Input placeholder="Clone ID" disabled className={styles.formWidth} />
                    </Form.Item>

                    <Form.Item
                        name="name"
                        label="Template Name"
                        labelCol={{style: {width: 164}}}
                        rules={[
                            {required: true, message: "Please enter the  template name!"},
                            {pattern: NAME_MATCH_REGEX, message: "Invalid template name!"}
                        ]}
                    >
                        <Input placeholder="Template Name" className={styles.formWidth} />
                    </Form.Item>

                    <Form.Item name="description" label="Description" labelCol={{style: {width: 164}}}>
                        <Input.TextArea
                            placeholder="Enter English, numbers, or English characters"
                            className={styles.formWidth}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </Space>
    );
};

const TopoModal = ({title, topoModalOpen, topoData, onCancel}) => {
    const [templateData, setTemplateData] = useState({});

    useEffect(() => {
        getDCTemplateInfo();
    }, [topoData]);

    const getDCTemplateInfo = async () => {
        const result = await viewDCTemplateInfo({template_id: topoData.id});
        if (result.status === 200) {
            setTemplateData(result.data);
        }
    };

    return (
        <Space>
            <Modal
                className="ampcon-max-modal"
                title={
                    <div>
                        {title}
                        <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                open={topoModalOpen}
                onCancel={onCancel}
                footer={null}
            >
                <div className={styles.topology}>
                    <div id="topology_container" style={{minWidth: "500px", height: "500px"}}>
                        <DCTemplatesTopo
                            unitNodes={templateData?.template_info?.unit}
                            podNodes={templateData?.template_info?.pod}
                            type={templateData?.type}
                            spineCount={templateData?.template_info?.spine_count}
                            superSpineCount={templateData?.template_info?.super_spine_count}
                        />
                    </div>
                </div>
            </Modal>
        </Space>
    );
};
