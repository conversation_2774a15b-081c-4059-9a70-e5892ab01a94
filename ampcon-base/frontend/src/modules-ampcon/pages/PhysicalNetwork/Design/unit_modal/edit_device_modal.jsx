import {forwardRef, useImperativeHandle, useState} from "react";
import {Button, Divider, Form, Input, Modal, Select, message} from "antd";

const EditDeviceModal = forwardRef(({deviceChange}, ref) => {
    const title = "Edit Device";
    const editDeviceHostName = "Sysname";
    const editDevicePhysicalDevice = "Physical Device";

    const [physicalDeviceData, setPhysicalDeviceData] = useState([]);
    const [nodeInfo, setNodeInfo] = useState();
    const [isShowModal, setIsShowModal] = useState(false);
    const [nodePhysicalDevice, setNodePhysicalDevice] = useState();

    const [editDeviceForm] = Form.useForm();

    useImperativeHandle(ref, () => ({
        showEditDeviceModal: (nodeInfo, physicalDeviceData) => {
            setIsShowModal(true);
            setPhysicalDeviceData(physicalDeviceData);
            setNodeInfo(nodeInfo);
        },
        hideEditDeviceModal: () => {
            setIsShowModal(false);
        }
    }));

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 20}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                editDeviceForm.resetFields();
            }}
            footer={
                <div style={{marginBottom: "4px"}}>
                    <Divider style={{marginTop: 20, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            editDeviceForm.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            editDeviceForm
                                .validateFields()
                                .then(values => {
                                    setIsShowModal(false);
                                    deviceChange(nodeInfo?.node.store.data, nodePhysicalDevice);
                                    message.success("Edit device successfully!");
                                })
                                .catch(errorInfo => {
                                    message.error("Please input valid data");
                                    return;
                                });
                            editDeviceForm.resetFields();
                        }}
                    >
                        Apply
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 7}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={editDeviceForm}
            >
                <Form.Item
                    name="hostName"
                    label={editDeviceHostName}
                    style={{
                        fontFamily: "Lato, Lato",
                        fontWeight: 400,
                        fontSize: "14px",
                        color: "#212519",
                        lineHeight: "17px",
                        fontStyle: "normal",
                        textTransform: "none"
                    }}
                    rules={[
                        {
                            validator: (_, value) => {
                                if (value && /^\s*$/.test(value)) {
                                    return Promise.reject(new Error("Host Name should not only contain spaces."));
                                }
                                return Promise.resolve();
                            }
                        },
                        {
                            max: 128,
                            message: "hostName cannot exceed 128 characters!"
                        }
                    ]}
                >
                    <Input
                        style={{
                            width: "280px",
                            background: "#F4F5F7",
                            borderRadius: "2px 2px 2px 2px",
                            border: "1px solid #DADCE1",
                            color: "#212529"
                        }}
                        disabled
                        defaultValue={nodeInfo?.node.store.data.logic_device}
                    />
                </Form.Item>
                <Form.Item name="physicalDevice" label={editDevicePhysicalDevice}>
                    <Select
                        defaultValue={nodeInfo.node.store.data.switch_sn}
                        style={{
                            width: "280px",
                            background: "#FFFFFF"
                        }}
                        onChange={value => {
                            setNodePhysicalDevice(value);
                        }}
                        options={physicalDeviceData?.map(item => ({
                            value: item.sn,
                            label: `${item.sn} (${item.mgt_ip})`,
                            disabled: item.disabled
                        }))}
                    />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default EditDeviceModal;
