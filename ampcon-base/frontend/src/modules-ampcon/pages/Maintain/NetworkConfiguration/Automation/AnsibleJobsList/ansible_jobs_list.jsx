import React, {useEffect, useState} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import SwitchView from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/AnsibleJobsList/SwitchView/switch_view";
import JobView from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/AnsibleJobsList/JobView/job_view";
import ProtectedRoute from "@/modules-ampcon/utils/util";

const items = [
    {
        key: "job_view",
        label: "Job View",
        children: (
            <ProtectedRoute component={JobView} url="maintain/network_config/automation/ansible_jobs_list/job_view" />
        )
    },
    {
        key: "switch_view",
        label: "Switch View",
        children: (
            <ProtectedRoute
                component={SwitchView}
                url="maintain/network_config/automation/ansible_jobs_list/switch_view"
            />
        )
    }
];

const AnsibleJobsList = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();

    useEffect(() => {
        const currentPath = location.pathname;
        if (/(switch_view|job_view)$/.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(/(switch_view|job_view)$/)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (/(switch_view|job_view)$/.test(currentPath)) {
            const matchLength = currentPath.match(/(switch_view|job_view)$/)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        <div className="scrollable-container">
            <Tabs activeKey={currentActiveKey} items={items} onChange={onChange} destroyInactiveTabPane />
        </div>
    );
};

export default AnsibleJobsList;
