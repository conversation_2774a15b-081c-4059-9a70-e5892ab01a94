import {useForm} from "antd/es/form/Form";
import {getBackupDB, addBackupDB, updateBackupDB, delBackupDB, restoreBackupDB} from "@/modules-ampcon/apis/config_api";
import {Button, Card, Form, Input, message, Space} from "antd";
import {
    AmpConCustomModalForm,
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {useEffect, useRef, useState} from "react";
import {useSelector} from "react-redux";
import {backupSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const DBBackupButton = ({setIsModalOpen}) => {
    return (
        <Button
            type="primary"
            htmlType="button"
            icon={<Icon component={backupSvg} />}
            onClick={() => {
                setIsModalOpen(true);
            }}
        >
            Backup
        </Button>
    );
};

const DbBackupFormCreateItems = currentVersion => {
    return (
        <>
            <Form.Item
                name="name"
                label="Backup Name"
                rules={[
                    {required: true, message: "Please input your db backup name!"},
                    {
                        validator: (_, value) => {
                            if (!value || !/\s/.test(value)) {
                                return Promise.resolve();
                            }
                            return Promise.reject(new Error("DB backup name cannot contain spaces"));
                        }
                    }
                ]}
                style={{display: "block"}}
            >
                <Input placeholder="Backup Name" style={{width: "280px"}} />
            </Form.Item>

            <Form.Item
                name="encryptKey"
                label="Encrypt Key"
                rules={[{required: true, message: "Please input your encrypt key!"}]}
            >
                <Input placeholder="Encrypt Key" style={{width: "280px"}} />
            </Form.Item>

            <Form.Item
                name="version"
                label="AmpCon Version"
                rules={[{required: true, message: "Please input your version!"}]}
                initialValue={currentVersion}
            >
                <Input placeholder="Ampcon Version" style={{width: "280px", color: "#212529"}} disabled />
            </Form.Item>
        </>
    );
};

const DbBackupFormEditItems = currentVersion => {
    return (
        <>
            <Form.Item
                name="modified_name"
                label="Backup Name"
                rules={[
                    {required: true, message: "Please input your db backup name!"},
                    {
                        validator: (_, value) => {
                            if (!value || !/\s/.test(value)) {
                                return Promise.resolve();
                            }
                            return Promise.reject(new Error("Db backup name cannot contain spaces"));
                        }
                    }
                ]}
                style={{display: "block"}}
            >
                <Input placeholder="Device Name" style={{width: "280px"}} />
            </Form.Item>

            <Form.Item
                name="version"
                label="Ampcon Version"
                rules={[{required: true, message: "Please input your version!"}]}
                initialValue={currentVersion}
            >
                <Input placeholder="Ampcon Version" style={{width: "280px", color: "#212529"}} disabled />
            </Form.Item>
        </>
    );
};

const DbBackupFormRestoreItems = currentVersion => {
    return (
        <>
            <Form.Item
                name="name"
                label="Backup Name"
                rules={[{required: true, message: "Please input your db backup name!"}]}
                style={{display: "none"}}
            >
                <Input placeholder="Device Name" style={{width: "280px", color: "#212529"}} disabled />
            </Form.Item>
            <Form.Item
                name="encryptKey"
                label="Encrypt Key"
                rules={[{required: true, message: "Please input your encrypt key!"}]}
            >
                <Input placeholder="Encrypt Key" style={{width: "280px"}} />
            </Form.Item>

            <Form.Item
                name="version"
                label="Ampcon Version"
                rules={[{required: true, message: "Please input your version!"}]}
                initialValue={currentVersion}
            >
                <Input placeholder="Ampcon Version" style={{width: "280px", color: "#212529"}} disabled />
            </Form.Item>
        </>
    );
};

const SystemBackup = () => {
    const currentVersion = useSelector(state => state.version.currentVersionInfo);
    const [isShowSpin, setIsShowSpin] = useState(false);

    const [form] = useForm();
    const [isModalOpen, setIsModalOpen] = useState();
    const [isModalEditOpen, setIsModalEditOpen] = useState();
    const [isModalRestoreOpen, setIsModalRestoreOpen] = useState(false);
    const [currentDBName, setCurrentDBName] = useState("");
    const tableRef = useRef(null);

    useEffect(() => {
        if (isModalEditOpen && currentDBName) {
            form.setFieldsValue({modified_name: currentDBName});
        }
        if (isModalRestoreOpen && currentDBName) {
            form.setFieldsValue({name: currentDBName});
        }
    }, [isModalEditOpen, isModalRestoreOpen]);

    const onSubmit = async values => {
        setIsShowSpin(true);
        try {
            const ret = await addBackupDB(values);
            if (ret.status === 200) {
                form.resetFields();
                setIsModalOpen(false);
                message.success(ret.info);
            } else {
                message.error(ret.info);
            }
            setIsShowSpin(false);
            tableRef.current.refreshTable();
        } catch (e) {
            message.error("An error occurred during the process of submit");
        } finally {
            setIsShowSpin(false);
        }
    };

    const onRestore = async values => {
        setIsShowSpin(true);
        try {
            const ret = await restoreBackupDB(values);
            if (ret.status === 200) {
                form.resetFields();
                setIsModalRestoreOpen(false);
                message.success(ret.info);
            } else {
                message.error(ret.info);
            }
            setIsShowSpin(false);
            tableRef.current.refreshTable();
        } catch (e) {
            message.error("An error occurred during the process of restore");
        } finally {
            setIsShowSpin(false);
        }
    };

    const onEdit = async values => {
        const newValues = {...values, name: currentDBName};
        setIsShowSpin(true);
        try {
            const ret = await updateBackupDB(newValues);
            if (ret.status === 200) {
                form.resetFields();
                setIsModalEditOpen(false);
                message.success(ret.info);
            } else {
                message.error(ret.info);
            }
            setIsShowSpin(false);
            tableRef.current.refreshTable();
        } catch (e) {
            message.error("An error occurred during the process of edit");
        } finally {
            setIsShowSpin(false);
        }
    };

    const matchFieldsList = [
        {name: "name", matchMode: "fuzzy"},
        {name: "version", matchMode: "fuzzy"},
        {name: "create_time", matchMode: "fuzzy"},
        {name: "modified_time", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["name", "version"];

    const delDbBackup = async db_name => {
        const ret = await delBackupDB(db_name);
        if (ret.status === 200) {
            message.success(ret.info);
        } else {
            message.error(ret.info);
        }
        tableRef.current.refreshTable();
    };

    const columns = [
        createColumnConfig("DB File Name", "name", TableFilterDropdown),
        createColumnConfig("AmpCon Version", "version", TableFilterDropdown),
        createColumnConfig("Create Time", "create_time", TableFilterDropdown),
        createColumnConfig("Update Time", "modified_time", TableFilterDropdown, "", "", "descend"),
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    setIsModalEditOpen(true);
                                    setCurrentDBName(record.name);
                                }}
                            >
                                Edit
                            </a>
                            <a
                                onClick={() =>
                                    confirmModalAction(
                                        `This action will delete backup: ${record.name}, Do you want to continue?`,
                                        () => delDbBackup(record.name)
                                    )
                                }
                            >
                                Remove
                            </a>
                            <a
                                onClick={() => {
                                    setIsModalRestoreOpen(true);
                                    setCurrentDBName(record.name);
                                }}
                            >
                                Restore
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{margin: "8px 0 20px"}}>System Backup</h2>
            <AmpConCustomModalForm
                title="Create Backup Config"
                isModalOpen={isModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 6
                    }
                }}
                CustomFormItems={DbBackupFormCreateItems(currentVersion)}
                onCancel={() => {
                    form.resetFields();
                    setIsModalOpen(false);
                }}
                onSubmit={onSubmit}
                isShowSpin={isShowSpin}
                modalClass="ampcon-middle-modal"
            />

            <AmpConCustomModalForm
                title="Edit Backup Config"
                isModalOpen={isModalEditOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 8
                    },
                    wrapperCol: {
                        span: 19
                    }
                }}
                CustomFormItems={DbBackupFormEditItems(currentVersion, "edit")}
                onCancel={() => {
                    form.resetFields();
                    setIsModalEditOpen(false);
                    setCurrentDBName("");
                }}
                onSubmit={onEdit}
                isShowSpin={isShowSpin}
                modalClass="ampcon-middle-modal"
            />

            <AmpConCustomModalForm
                title="Restore Backup Config"
                isModalOpen={isModalRestoreOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 8
                    },
                    wrapperCol: {
                        span: 19
                    }
                }}
                CustomFormItems={DbBackupFormRestoreItems}
                onCancel={() => {
                    form.resetFields();
                    setIsModalRestoreOpen(false);
                    setCurrentDBName("");
                }}
                onSubmit={onRestore}
                isShowSpin={isShowSpin}
                modalClass="ampcon-middle-modal"
            />

            <AmpConCustomTable
                columns={columns}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={<DBBackupButton setIsModalOpen={setIsModalOpen} />}
                fetchAPIInfo={getBackupDB}
                ref={tableRef}
            />
        </Card>
    );
};

export default SystemBackup;
