import React, {useEffect, useRef, useState, forwardRef} from "react";
import {Dnd} from "@antv/x6-plugin-dnd";
import {MiniMap} from "@antv/x6-plugin-minimap";
import {Snapline} from "@antv/x6-plugin-snapline";
import {usePopper} from "react-popper";
import {useMouse, useWhyDidYouUpdate} from "ahooks";

import {
    Button,
    Space,
    Card,
    Progress,
    Table,
    message,
    Tag,
    Switch,
    Form,
    Flex,
    Input,
    Select,
    Tooltip,
    Radio,
    Spin,
    Row,
    Col,
    Divider
} from "antd";
import {
    AmpConCustomTable,
    createColumnConfigMultipleParams,
    TableFilterDropdown,
    createColumnConfig,
    TableSelectFilterDropdown,
    AmpConCustomModalForm,
    AmpConCustomModalTable,
    AmpConCustomModal
} from "@/modules-ampcon/components/custom_table";
import {TimeSelector} from "@/modules-ampcon/pages/Service/Switch/health_status";
import {addSvg, FileSvg, refreshSvg} from "@/utils/common/iconSvg";
import {useNavigate, useLocation} from "react-router-dom";
import Icon, {PlusOutlined, ArrowLeftOutlined, DeleteOutlined, RightOutlined, LeftOutlined} from "@ant-design/icons";
import {
    fetchLogicalNetworksTableData,
    saveLogicalNetwork,
    deleteLogicalNetwork,
    createLogicalRouter,
    createLogicalSwitch,
    fetchLogicalNetworkDetail,
    fetchLogicalNetworkHistoryConfig,
    deleteLogicalNetworkConfiguration,
    saveLogicalNetworkConfiguration,
    fetchNetworkAccessTableData,
    fetchAZInfo,
    saveVirtualNetwork,
    deleteLogicalRouter,
    deleteLogicalSwitch,
    deleteVirtualNetwork,
    deleteLogicalInterface,
    deleteLogicalPort,
    saveLogicalInterface,
    saveLogicalPort,
    updateTopoPosition,
    generateVirtualIPrange,
    fetchConnectableVirtualNetworks,
    fetchLogicalNetworkCurrentConfig
} from "@/modules-ampcon/apis/dc_template_api";
import {fetchFabricInfo} from "@/modules-ampcon/apis/node_addition_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {Graph, Shape} from "@antv/x6";
import {register} from "@antv/x6-react-shape?react";
import logicalRouterSvg from "./resource/lr_normal.png?react";
import logicalSwitchSvg from "./resource/ls_normal.png?react";
import virtualNetworkSvg from "./resource/vn_normal.png?react";
import logicalRouterErrSvg from "./resource/lr_error.png?react";
import logicalSwitchErrSvg from "./resource/ls_error.png?react";
import virtualNetworkErrSvg from "./resource/vn_error.png?react";
import logicalRouterExampleSvg from "./resource/logical_router_example.svg";
import logicalSwitchExampleSvg from "./resource/logical_switch_example.svg";
import virtualNetworkExampleSvg from "./resource/virtual_network_example.svg";
import ShrinkedIcon from "./resource/shrinked.svg?react";
import ExpandedIcon from "./resource/expanded.svg?react";
import FullscreenExitSvg from "@/modules-ampcon/pages/Topo/Topology/resource/fullscreen_exit.svg?react";
import ReloadSvg from "@/modules-ampcon/pages/Topo/Topology/resource/reload.svg?react";
import ZoomInSvg from "@/modules-ampcon/pages/Topo/Topology/resource/zoom_in.svg?react";
import ZoomOutSvg from "@/modules-ampcon/pages/Topo/Topology/resource/zoom_out.svg?react";
import DisconnectedSvg from "./resource/add_connection_green_icon.svg?react";
import styles from "./logical_networks.module.scss";
import RightClickPopUpMenu from "./right_click_pop_up_menu";
import {formValidateRules} from "@/modules-ampcon/utils/util";
// import {calculateVertices, calculateSwitchViewTopoVertices} from "@/utils/topo_layout_utils";
import LogicalSwitchDetail from "@/modules-ampcon/pages/Service Provision/Logical Switches/logical_switches_detail";
import LogicalRouterDetail from "@/modules-ampcon/pages/Service Provision/Logical Routers/logical_router_detail";

const shape_svg_mapping = {
    "logical-router": logicalRouterSvg,
    "logical-switch": logicalSwitchSvg,
    "virtual-network": virtualNetworkSvg
};

const shape_example_svg_mapping = {
    "logical-router": logicalRouterExampleSvg,
    "logical-switch": logicalSwitchExampleSvg,
    "virtual-network": virtualNetworkExampleSvg
};

const shape_label_mapping = {
    "logical-router": "Logical Router",
    "logical-switch": "Logical Switch",
    "virtual-network": "Virtual Network"
};

const nodeWidth = 40;
const nodeHeight = 54;

const ipv4NetRegex =
    /^((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\/([4-9]|[12]\d|3[0-2])$/;

const delay = ms =>
    new Promise(resolve => {
        setTimeout(resolve, ms);
    });

const nameValidator = {
    pattern: /^[\w:-]+$/,
    message: (
        <span>
            Name can only contain letters, numbers, underscores,
            <br />
            hyphens and colons.
        </span>
    )
};

const DeviceManagementModal = ({
    showDeviceManagementModal,
    setShowDeviceManagementModal,
    deviceID,
    deviceType,
    ls_info,
    lr_info
}) => {
    const childItems =
        deviceType === "logical-router"
            ? [
                  <div style={{}}>
                      <LogicalRouterDetail showModal lr_id={deviceID} lr_info={lr_info} />
                  </div>
              ]
            : [
                  <div style={{}}>
                      <LogicalSwitchDetail showModal ls_id={deviceID} ls_info={ls_info} />
                  </div>
              ];

    return (
        <AmpConCustomModal
            title="Device Management"
            isModalOpen={showDeviceManagementModal}
            modalClass="ampcon-max-modal"
            onCancel={() => {
                setShowDeviceManagementModal(false);
            }}
            footer={[
                <Button
                    type="primary"
                    onClick={() => {
                        setShowDeviceManagementModal(false);
                    }}
                >
                    Cancel
                </Button>
            ]}
            childItems={childItems}
        />
    );
};

const CreateLogicalSwitchModal = ({
    showCreateSwitchModal,
    setShowCreateSwitchModal,
    fabricList,
    addedNodePosition,
    refreshGraph,
    ln_id,
    successAddedCallback,
    errorAddedCallback
}) => {
    const [form] = Form.useForm();

    const handleSubmit = async values => {
        const data = {
            ...values,
            arpNdSuppress: true,
            type: "Manual",
            positionX: addedNodePosition.x,
            positionY: addedNodePosition.y,
            logicalNetworkId: ln_id // 使用传递的逻辑网络ID
        };
        try {
            const response = await createLogicalSwitch(data);
            if (response.status === 200) {
                message.success("Logical Switch created successfully");
                setShowCreateSwitchModal(false);
                form.resetFields();
                successAddedCallback();
                // refreshGraph();
            } else {
                message.error(`Failed to create Logical Switch: ${response.info}`);
                // errorAddedCallback();
            }
        } catch (error) {
            console.log("Error creating Logical Switch:", error);
        }
    };
    const handleCancel = () => {
        setShowCreateSwitchModal(false);
        form.resetFields();
        errorAddedCallback();
    };
    const customFormItems = () => {
        return (
            <div>
                <Form.Item
                    name="resourceType"
                    label="Resource Type"
                    rules={[{required: true, message: "Please select a resource type"}]}
                    initialValue="Logical Switch"
                >
                    <Input disabled style={{width: "280px", color: "#212519"}} />
                </Form.Item>
                <Form.Item
                    name="name"
                    label="Name"
                    rules={[
                        {required: true, message: "Please input the name"},
                        {max: 64, message: "Name cannot exceed 64 characters"},
                        nameValidator
                    ]}
                >
                    <Input placeholder="Enter Logical Switch Name" style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="description"
                    label="Description"
                    rules={[{max: 256, message: "Description cannot exceed 256 characters"}]}
                >
                    <Input.TextArea placeholder="Enter Description" rows={3} style={{width: "280px"}} />
                </Form.Item>
                <Form.Item name="fabricId" label="Fabric" rules={[{required: true, message: "Please select a fabric"}]}>
                    <Select placeholder="Select Fabric" style={{width: "280px"}} options={fabricList} />
                </Form.Item>
                {/* <Form.Item name="arpNdSuppress" label="ARP/ND Suppression">
                    <Switch defaultChecked />
                </Form.Item> */}
            </div>
        );
    };
    return (
        <AmpConCustomModalForm
            modalClass="ampcon-middle-modal"
            title="Create Logical Switch"
            isModalOpen={showCreateSwitchModal}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 6
                }
            }}
            CustomFormItems={customFormItems}
            onCancel={handleCancel}
            onSubmit={handleSubmit}
        />
    );
};

const CreateLogicalRouterModal = ({
    showCreateRouterModal,
    setShowCreateRouterModal,
    fabricList,
    addedNodePosition,
    refreshGraph,
    ln_id,
    successAddedCallback,
    errorAddedCallback
}) => {
    const [form] = Form.useForm();
    const handleSubmit = async values => {
        const data = {
            ...values,
            type: "Manual",
            positionX: addedNodePosition.x,
            positionY: addedNodePosition.y,
            logicalNetworkId: ln_id // 使用传递的逻辑网络ID
        };
        try {
            const response = await createLogicalRouter(data);
            if (response.status === 200) {
                message.success("Logical Router created successfully");
                setShowCreateRouterModal(false);
                form.resetFields();
                successAddedCallback();
                // refreshGraph();
            } else {
                message.error(`Failed to create Logical Router: ${response.info}`);
                // errorAddedCallback();
            }
        } catch (error) {
            console.log("Error creating Logical Router:", error);
        }
    };
    const handleCancel = () => {
        setShowCreateRouterModal(false);
        form.resetFields();
        errorAddedCallback();
    };
    const customFormItems = () => {
        return (
            <div>
                <Form.Item
                    name="resourceType"
                    label="Resource Type"
                    rules={[{required: true, message: "Please select a resource type"}]}
                    initialValue="Logical Router"
                >
                    <Input disabled style={{width: "280px", color: "#212519"}} />
                </Form.Item>
                <Form.Item
                    name="name"
                    label="Name"
                    rules={[
                        {required: true, message: "Please input the name"},
                        {max: 64, message: "Name cannot exceed 64 characters"},
                        nameValidator
                    ]}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="description"
                    label="Description"
                    initialValue=""
                    rules={[{max: 256, message: "Description cannot exceed 256 characters"}]}
                >
                    <Input.TextArea rows={3} style={{width: "280px"}} />
                </Form.Item>
                <Form.Item name="vrfMode" label="VRF Mode" initialValue="auto">
                    <Radio.Group
                        onChange={e => {
                            const vrfNameField = form.getFieldInstance("vrfName");
                            if (e.target.value === "auto") {
                                form.setFieldsValue({vrfName: ""}); // 清空 VRF 名称
                                form.validateFields(["vrfName"]); // 触发验证
                                vrfNameField.disabled = true;
                            } else {
                                vrfNameField.disabled = false;
                            }
                        }}
                        style={{width: "280px"}}
                    >
                        <Radio value="auto">Auto</Radio>
                        <Radio value="manual">Manual</Radio>
                    </Radio.Group>
                </Form.Item>
                <Form.Item
                    name="vrfName"
                    label="VRF Name"
                    required={form.getFieldValue("vrfMode") !== "auto"}
                    rules={[
                        {
                            pattern: /^[\dA-Za-z-]{1,15}$/,
                            message: (
                                <div>
                                    VRF Name must be 1-15 characters long and can
                                    <br /> only contain letters, numbers, and hyphens
                                </div>
                            )
                        },
                        form.getFieldValue("vrfMode") === "manual" && {
                            required: true,
                            message: "Please input the VRF Name"
                        }
                    ]}
                >
                    <Input style={{width: "280px"}} disabled={form.getFieldValue("vrfMode") === "auto"} />
                </Form.Item>
                <Form.Item
                    name="l3_mac_range"
                    label="L3 Anycast MAC Range"
                    required
                    initialValue="02:00:00:00:00:01 - 02:00:00:00:FF:FF"
                    tooltip="The range needs to be large enough to avoid insufficient allocation of switches after expansion. Modification is not supported."
                >
                    <Input style={{width: "280px", color: "#212519"}} disabled />
                </Form.Item>
                <Form.Item name="fabricId" label="Fabric" rules={[{required: true, message: "Please select a fabric"}]}>
                    <Select placeholder="Select Fabric" style={{width: "280px"}} options={fabricList} />
                </Form.Item>
            </div>
        );
    };
    return (
        <AmpConCustomModalForm
            modalClass="ampcon-middle-modal"
            title="Create Logical Router"
            isModalOpen={showCreateRouterModal}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 7
                }
            }}
            CustomFormItems={customFormItems}
            onCancel={handleCancel}
            onSubmit={handleSubmit}
        />
    );
};

const FilterForm = ({fabricList, onFilterChange}) => {
    const [form] = Form.useForm();
    // const [form2] = Form.useForm();
    const [podList, setPodList] = useState([]);
    const fetchPodList = async fabricId => {
        try {
            const response = await fetchAZInfo({fabric_id: fabricId});
            if (response.status === 200) {
                const pods = response.data.map(pod => ({
                    label: pod.az_name,
                    value: pod.id
                }));
                setPodList(pods);
            } else {
                message.error(`Failed to fetch AZ info: ${response.info}`);
            }
        } catch (error) {
            console.error("Error fetching AZ info:", error);
        }
    };
    const layout = {
        labelCol: {span: 6}, // 标签占 6 列
        wrapperCol: {span: 18} // 输入框占 18 列
    };
    return (
        <div style={{display: "flex", flexDirection: "row", gap: "5px"}}>
            <Form form={form} style={{width: "900px"}} {...layout} layout="horizontal">
                <Row gutter={16}>
                    <Col span={12}>
                        <Form.Item
                            layout="inline"
                            // name="Resource Type"
                            label="Resource Type"
                            labelAlign="left"
                            required
                            initialValue="Virtual Network"
                        >
                            <Input disabled value="Virtual Network" style={{width: "280px", color: "#212519"}} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            layout="inline"
                            name="fabricId"
                            labelAlign="left"
                            label="Fabric"
                            rules={[{required: true, message: "Please select a fabric"}]}
                        >
                            <Select
                                placeholder="Select Fabric"
                                style={{width: "280px"}}
                                options={fabricList}
                                onChange={value => {
                                    // 当选择 Fabric 时，可以在这里处理逻辑，比如更新其他表单项的选项
                                    fetchPodList(value);
                                    form.setFieldsValue({azId: undefined}); // 清空 Pod 选择
                                }}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            name="azId"
                            label="PoD"
                            labelAlign="left"
                            rules={[{required: true, message: "Please select a pod"}]}
                        >
                            <Select placeholder="Select PoD" style={{width: "280px"}} options={podList} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item name="vpcName" label="VPC Name" labelAlign="left">
                            <Input placeholder="Enter VPC Name" style={{width: "280px"}} />
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
            <Button
                type="primary"
                style={{marginTop: "56px", marginLeft: "-20px"}}
                onClick={async () => {
                    await form.validateFields().then(() => {
                        const values = form.getFieldsValue();
                        onFilterChange(values);
                    });
                }}
            >
                Search
            </Button>
        </div>
    );
};

const EdgeInfoPanel = ({referenceElement, showEdgeInfoPanel, edgePanelInfo, setShowEdgeInfoPanel}) => {
    const [popperElement, setPopperElement] = useState(null);
    const {styles, attributes} = usePopper(referenceElement, popperElement, {
        placement: "right-start",
        modifiers: [
            {
                name: "offset",
                options: {
                    // [y, x]
                    offset: [5, 5]
                }
            },
            {
                name: "eventListeners",
                options: {scroll: false, resize: false}
            },
            {
                name: "preventOverflow",
                options: {
                    padding: 5,
                    altAxis: true,
                    boundary: "viewport"
                }
            }
        ]
    });
    const headerStyle = {
        fontSize: "14px",
        color: "#929A9E",
        lineHeight: "17px",
        textAlign: "left",
        fontStyle: "normal",
        fontWeight: 400,
        justifyContent: "center !important"
    };

    const contentStyle = {
        fontSize: "14px",
        color: "#212519",
        lineHeight: "17px",
        textAlign: "left",
        fontStyle: "normal",
        fontWeight: 400
    };
    return (
        showEdgeInfoPanel && (
            <div
                ref={setPopperElement}
                style={{
                    ...styles.popper,
                    background: "#ffffff",
                    border: "1px solid #ccc",
                    padding: "16px",
                    borderRadius: "4px",
                    boxShadow: "0 1px 12px 1px #E6E8EA",
                    zIndex: 1000
                }}
                {...attributes.popper}
            >
                <Flex horizontal gap="16px">
                    <Flex vertical gap="16px" style={{justifyContent: "center"}}>
                        <div style={headerStyle}>Status</div>
                    </Flex>
                    <Flex vertical gap="16px" style={{}}>
                        <div>
                            <Tag
                                className={
                                    edgePanelInfo.status === 0 || edgePanelInfo.status === 2
                                        ? "successTag"
                                        : "failedTag"
                                }
                            >
                                {edgePanelInfo.status === 0 || edgePanelInfo.status === 2 ? "Normal" : "Error"}
                            </Tag>
                        </div>
                    </Flex>
                </Flex>
                {edgePanelInfo.errMsg.length > 0 &&
                    [1, 3].includes(edgePanelInfo.status) &&
                    edgePanelInfo.errMsg.map((err, index) => (
                        <div key={index} style={{marginTop: "8px"}}>
                            {err}
                        </div>
                    ))}
            </div>
        )
    );
};

const NodeInfoPanel = ({
    showNodeInfoPanel,
    referenceElement,
    vnPanelInfo,
    setShowVnDetailModal,
    setShowNodeInfoPanel
}) => {
    const [popperElement, setPopperElement] = useState(null);
    const {styles, attributes} = usePopper(referenceElement, popperElement, {
        placement: "right-start",
        modifiers: [
            {
                name: "offset",
                options: {
                    // [y, x]
                    offset: [5, 5]
                }
            },
            {
                name: "eventListeners",
                options: {scroll: false, resize: false}
            },
            {
                name: "preventOverflow",
                options: {
                    padding: 5,
                    altAxis: true,
                    boundary: "viewport"
                }
            }
        ]
    });
    const headerStyle = {
        fontSize: "14px",
        color: "#929A9E",
        lineHeight: "17px",
        textAlign: "left",
        fontStyle: "normal",
        fontWeight: 400
    };

    const contentStyle = {
        fontSize: "14px",
        color: "#212519",
        lineHeight: "17px",
        textAlign: "left",
        fontStyle: "normal",
        fontWeight: 400
    };

    return (
        showNodeInfoPanel && (
            <div
                ref={setPopperElement}
                className="nodeInfoPanel"
                onMouseLeave={() => {
                    setShowNodeInfoPanel(false);
                }}
                onMouseEnter={() => {}}
                style={{
                    ...styles.popper,
                    background: "#ffffff",
                    padding: "16px",
                    borderRadius: "4px",
                    boxShadow: "0 1px 12px 1px #E6E8EA",
                    zIndex: 1000
                }}
                {...attributes.popper}
            >
                <Flex className="nodeInfoPanel" horizontal gap="16px">
                    <Flex className="nodeInfoPanel" vertical gap="16px" style={{}}>
                        <div className="nodeInfoPanel" style={headerStyle}>
                            Name
                        </div>
                        <div className="nodeInfoPanel" style={headerStyle}>
                            PoD Resource
                        </div>
                        <div className="nodeInfoPanel" style={headerStyle}>
                            Host Nums
                            <br className="nodeInfoPanel" />
                            (Abnormal/Total)
                        </div>
                    </Flex>
                    <Flex className="nodeInfoPanel" vertical gap="16px" style={{}}>
                        <div className="nodeInfoPanel" style={contentStyle}>
                            {vnPanelInfo.vl2}
                        </div>
                        <div className="nodeInfoPanel" style={contentStyle}>
                            {vnPanelInfo.podResource}
                        </div>
                        <div className="nodeInfoPanel" style={contentStyle}>
                            {vnPanelInfo.hostNums}
                            <br className="nodeInfoPanel" />
                            <span className="actionLink nodeInfoPanel" style={{color: "#FF4D4F"}}>
                                <a
                                    className="nodeInfoPanel"
                                    onClick={() => {
                                        setShowVnDetailModal(true);
                                    }}
                                >
                                    Detail
                                </a>
                            </span>
                        </div>
                    </Flex>
                </Flex>
            </div>
        )
    );
};
const CreateVirtualNetworkModal = ({
    showCreateVirtualNetworkModal,
    setShowCreateVirtualNetworkModal,
    addedNodePosition,
    errorAddedCallback,
    successAddedCallback,
    fabricList,
    ln_id
}) => {
    const tableRef = useRef(null);
    const [showSearchTable, setshowSearchTable] = useState(false);
    const [filterFields, setFilterFields] = useState({});
    const [selectedRows, setselectedRows] = useState([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [disableAdd, setDisableAdd] = useState(false);
    const columns = [
        createColumnConfigMultipleParams({
            title: "ID",
            dataIndex: "id",
            enableFilter: false,
            enableSorter: true
        }),
        createColumnConfigMultipleParams({
            title: "Name",
            dataIndex: "network_name",
            enableFilter: false,
            enableSorter: true
        }),
        createColumnConfigMultipleParams({
            title: "VLAN ID",
            dataIndex: "vlan_id",
            enableFilter: false,
            enableSorter: true
        }),
        createColumnConfigMultipleParams({
            title: "VPC Name",
            dataIndex: "vpc_name",
            enableFilter: false,
            enableSorter: false
        })
    ];
    const handleCancel = () => {
        setShowCreateVirtualNetworkModal(false);
        setFilterFields(["", "", ""]);
        setshowSearchTable(false);
        errorAddedCallback();
    };

    function onFilterChange(values) {
        console.log("Filter Values:", values);
        setshowSearchTable(true);
        setFilterFields({
            fabricId: values.fabricId || "",
            azId: values.azId || "",
            vpcName: values.vpcName || ""
        });
        tableRef.current?.clearSelectedRow();
        setDisableAdd(true);
        console.log("Filter Fields:", filterFields);
        // refreshTable();
    }

    const handleAddVirtualNetwork = async () => {
        const tableSelectedRowKeys = tableRef.current?.getSelectedRow().tableSelectedRowKey || [];
        const data = {
            logicalNetworkId: ln_id,
            networkList: tableSelectedRowKeys,
            fabricId: filterFields.fabricId,
            azId: filterFields.azId,
            positionX: addedNodePosition.x,
            positionY: addedNodePosition.y
        };

        try {
            const response = await saveVirtualNetwork(data);
            if (response.status === 200) {
                message.success("Virtual Network created successfully");
                setShowCreateVirtualNetworkModal(false);
                tableRef.current?.clearSelectedRow();
                setshowSearchTable(false);
                setFilterFields({});
                setDisableAdd(true);
                successAddedCallback();
            } else {
                message.error(`Failed to create Virtual Network: ${response.info}`);
                // errorAddedCallback();
            }
        } catch (error) {
            console.error("Error creating Virtual Network:", error);
        }
    };

    const childItems = showSearchTable
        ? [
              <FilterForm fabricList={fabricList} onFilterChange={onFilterChange} />,
              <div
                  style={{
                      borderTop: "1px solid #f0f0f0",
                      margin: "8px 0 12px 0",
                      width: "100%"
                  }}
              />,
              <AmpConCustomTable
                  ref={tableRef}
                  columns={columns}
                  fetchAPIInfo={fetchNetworkAccessTableData}
                  fetchAPIParams={[filterFields.fabricId, filterFields.azId, filterFields.vpcName]}
                  //   rowKey="id"
                  extraButton={[
                      <Button
                          type="primary"
                          disabled={disableAdd}
                          onClick={handleAddVirtualNetwork}
                          icon={<Icon component={PlusOutlined} />}
                      >
                          Add
                      </Button>
                  ]}
                  rowSelection={{
                      selectedRowKeys,
                      selectedRows,
                      onChange: (selectedRowKeys, selectedRows) => {
                          if (selectedRowKeys.length === 0) {
                              setDisableAdd(true);
                          } else {
                              setDisableAdd(false);
                          }
                      }
                  }}
                  searchFieldsList={["network_name"]}
              />
          ]
        : [
              <FilterForm fabricList={fabricList} onFilterChange={onFilterChange} />,
              <div
                  style={{
                      borderTop: "1px solid #f0f0f0",
                      margin: "8px 0 12px 0",
                      width: "100%"
                  }}
              />,
              <AmpConCustomTable columns={columns} dataSource={[]} />
          ];
    return (
        <AmpConCustomModal
            title="Create Virtual Network"
            isModalOpen={showCreateVirtualNetworkModal}
            modalClass="ampcon-max-modal"
            onCancel={handleCancel}
            childItems={childItems}
        />
    );
};

const EditConfigurationModal = ({
    showEditModal,
    setShowEditModal,
    inEditConfigInfo,
    refreshTable,
    setInEditConfigInfo
}) => {
    const [form] = Form.useForm();
    useEffect(() => {
        if (showEditModal) {
            form.setFieldsValue({
                description: inEditConfigInfo?.description || ""
            });
        }
    }, [showEditModal]);
    const handleSubmit = async values => {
        const data = {
            config_id: inEditConfigInfo.id,
            description: values.description
        };
        try {
            const response = await saveLogicalNetworkConfiguration(data);
            if (response.status === 200) {
                message.success("Configuration updated successfully");
                setShowEditModal(false);
                form.resetFields();
                refreshTable();
            } else {
                message.error(`Failed to update configuration: ${response.info}`);
            }
        } catch (error) {
            console.error("Error updating configuration:", error);
        }
    };
    const handleCancel = () => {
        setShowEditModal(false);
        setInEditConfigInfo({});
        form.resetFields();
    };
    const customFormItems = () => {
        return (
            <div>
                <Form.Item name="description" label="Description">
                    <Input.TextArea rows={3} style={{width: "280px"}} />
                </Form.Item>
            </div>
        );
    };
    return (
        <AmpConCustomModalForm
            modalClass="ampcon-middle-modal"
            title="Edit Config"
            isModalOpen={showEditModal}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 6
                }
            }}
            CustomFormItems={customFormItems}
            onCancel={handleCancel}
            onSubmit={handleSubmit}
        />
    );
};

const ConfigurationListModal = ({showConfigurationModal, setShowConfigurationModal, ln_id}) => {
    const tableRef = useRef(null);
    const [timeRange, setTimeRange] = useState(["", ""]);
    const [showEditModal, setShowEditModal] = useState(false);
    const [inEditConfigInfo, setInEditConfigInfo] = useState({});
    const [showCurrentConfigModal, setShowCurrentConfigModal] = useState(false);
    const [currentConfig, setCurrentConfig] = useState("");
    const [currentConfigModalTitle, setCurrentConfigModalTitle] = useState("Current Configuration");
    const [disableEdit, setDisableEdit] = useState(false);
    const [showConfigDetailModal, setShowConfigDetailModal] = useState(false);

    useEffect(() => {
        const loadCurrentConfig = async () => {
            try {
                const response = await fetchLogicalNetworkCurrentConfig({id: ln_id});
                if (response.status === 200) {
                    setCurrentConfig(
                        Object.entries(response.data)
                            .map(([key, value]) => `${key}: ${value}`)
                            .join("\n") || ""
                    );
                } else {
                    message.error(`Failed to fetch current configuration: ${response.info}`);
                }
            } catch (error) {
                console.error("Error fetching current configuration:", error);
            }
        };
        if (showCurrentConfigModal) {
            loadCurrentConfig();
        }
        return () => {
            // setCurrentConfig("configuration content here...");
        };
    }, [showCurrentConfigModal, ln_id]);

    const deleteConfiguration = async record => {
        try {
            const response = await deleteLogicalNetworkConfiguration(record.id);
            if (response.status === 200) {
                message.success("Configuration deleted successfully");
                refreshTable();
            } else {
                message.error(`Failed to delete configuration: ${response.info}`);
            }
        } catch (error) {
            console.error("Error deleting configuration:", error);
        }
    };
    const exportConfig = (configContent, filename) => {
        // 导出currentconfig为txt文件，文件名为create time_config
        const blob = new Blob([configContent || "No configuration content available"], {
            type: "text/plain;charset=utf-8"
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `${filename || "unknown"}.txt`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    };

    const columns = [
        createColumnConfigMultipleParams({
            title: "ID",
            dataIndex: "id",
            enableFilter: false,
            enableSorter: true
        }),
        createColumnConfigMultipleParams({
            title: "Create Time",
            dataIndex: "create_time",
            enableFilter: false,
            enableSorter: true
        }),
        createColumnConfigMultipleParams({
            title: "Description",
            dataIndex: "description",
            enableFilter: false,
            enableSorter: true
        }),
        {
            title: "Operation",
            render: (_, record) => (
                <Space size="large" className="actionLink">
                    <a
                        onClick={() => {
                            setInEditConfigInfo(record);
                            console.log("Editing configuration:", record);
                            setShowEditModal(true);
                        }}
                    >
                        Edit
                    </a>
                    <a
                        onClick={() => {
                            confirmModalAction(`Are you sure you want to delete configuration ${record.id}?`, () => {
                                deleteConfiguration(record);
                            });
                        }}
                    >
                        Delete
                    </a>
                    <a
                        onClick={() => {
                            exportConfig(record.config_detail, `${record.create_time}_config`);
                        }}
                    >
                        Export
                    </a>
                    <a
                        onClick={() => {
                            setInEditConfigInfo(record);
                            setShowConfigDetailModal(true);
                        }}
                    >
                        Detail
                    </a>
                </Space>
            )
        }
    ];
    const searchFieldsList = ["create_time", "description"];
    function refreshTable() {
        if (tableRef.current) {
            tableRef.current.getTableRef().current.refreshTable();
        }
    }
    return (
        <>
            <AmpConCustomModalTable
                ref={tableRef}
                title="Configuration List"
                modalClass="ampcon-max-modal"
                columns={columns}
                selectModalOpen={showConfigurationModal}
                fetchAPIInfo={fetchLogicalNetworkHistoryConfig}
                fetchAPIParams={[ln_id, timeRange[0], timeRange[1]]}
                buttonProps={[
                    <Button
                        type="primary"
                        onClick={() => {
                            setShowCurrentConfigModal(true);
                            setCurrentConfigModalTitle("Current Config");
                            setDisableEdit(false);
                        }}
                    >
                        <Icon component={FileSvg} />
                        Current Config
                    </Button>,
                    <TimeSelector
                        setTimeRange={setTimeRange}
                        timeRange={timeRange}
                        disableRange
                        label={<span style={{visibility: "hidden", width: "400px"}}>Time Range</span>}
                    />
                ]}
                searchFieldsList={searchFieldsList}
                onCancel={() => {
                    setTimeRange(["", ""]);
                    setShowConfigurationModal(false);
                }}
            />
            <EditConfigurationModal
                showEditModal={showEditModal}
                setShowEditModal={setShowEditModal}
                inEditConfigInfo={inEditConfigInfo}
                setInEditConfigInfo={setInEditConfigInfo}
                refreshTable={refreshTable}
            />
            <CurrentConfigModal
                showCurrentConfigModal={showCurrentConfigModal}
                setShowCurrentConfigModal={setShowCurrentConfigModal}
                currentConfig={currentConfig}
                exportConfig={exportConfig}
                ln_id={ln_id}
                refreshTable={refreshTable}
            />
            <ConfigDetailModal
                showConfigDetailModal={showConfigDetailModal}
                setShowConfigDetailModal={setShowConfigDetailModal}
                inEditConfigInfo={inEditConfigInfo}
                exportConfig={exportConfig}
            />
        </>
    );
};

const ConfigDetailModal = ({showConfigDetailModal, setShowConfigDetailModal, inEditConfigInfo, exportConfig}) => {
    const [form] = Form.useForm();
    return (
        <AmpConCustomModal
            title="Configuration Detail"
            isModalOpen={showConfigDetailModal}
            modalClass="ampcon-middle-modal"
            onCancel={() => {
                setShowConfigDetailModal(false);
            }}
            childItems={
                <Flex flex={1} layout="horizontal" style={{minHeight: "260.23px", paddingBottom: "-20px !important"}}>
                    <Input.TextArea
                        style={{
                            height: `${window.innerHeight / 2}px`,
                            border: "none",
                            backgroundColor: "#F8FAFB",
                            fontSize: "16px",
                            borderRadius: "4px",
                            boxShadow: "none",
                            resize: "none",
                            padding: "16px"
                        }}
                        value={inEditConfigInfo.config_detail}
                        readOnly
                    />
                </Flex>
            }
            footer={[
                <Button
                    onClick={() => {
                        // "2025-07-03_config
                        exportConfig(
                            inEditConfigInfo.config_detail,
                            `${inEditConfigInfo.create_time.replace(" ", "_")}_config`
                        );
                    }}
                >
                    Export
                </Button>
            ]}
        />
    );
};

const CurrentConfigModal = ({
    ln_id,
    showCurrentConfigModal,
    setShowCurrentConfigModal,
    currentConfig,
    refreshTable,
    exportConfig
}) => {
    const [form] = Form.useForm();
    return (
        <AmpConCustomModal
            title="Current Config"
            isModalOpen={showCurrentConfigModal}
            modalClass="ampcon-middle-modal"
            onCancel={() => {
                setShowCurrentConfigModal(false);
            }}
            childItems={
                <Input.TextArea
                    style={{
                        height: `${window.innerHeight / 2}px`,
                        border: "none",
                        backgroundColor: "#F8FAFB",
                        fontSize: "16px",
                        borderRadius: "4px",
                        boxShadow: "none",
                        resize: "none",
                        padding: "16px",
                        marginBottom: "-20px"
                    }}
                    value={currentConfig}
                    readOnly
                />
            }
            footer={[
                <Divider style={{marginTop: 0, marginBottom: 20}} />,
                <Button
                    onClick={() => {
                        // 2025-07-08_19:24_config
                        exportConfig(
                            currentConfig,
                            `${new Date().toISOString().replace(/[:T]/g, "_").slice(0, 19)}_config`
                        );
                    }}
                >
                    Export
                </Button>,
                <Button
                    type="primary"
                    onClick={async () => {
                        try {
                            const res = await saveLogicalNetworkConfiguration({
                                ln_id,
                                config_detail: currentConfig
                            });
                            if (res.status === 200) {
                                message.success("Configuration saved successfully");
                                refreshTable();
                                setShowCurrentConfigModal(false);
                            } else {
                                message.error(`Failed to save configuration: ${res.info}`);
                            }
                        } catch (error) {
                            console.log("Error saving configuration:", error);
                        }
                    }}
                >
                    Save
                </Button>
            ]}
        />
    );
};

const TopoLeftFloatMenu = ({zoomInCallback, zoomOutCallback, zoomResetCallback, reloadCallback, containerWidth}) => {
    const btnTooltip = {
        logicalRouter: "Add Logical Router",
        logicalSwitch: "Add Logical Switch",
        virtualNetwork: "Add Virtual Network",
        zoomin: "Zoom In",
        zoomout: "Zoom Out",
        zoomreset: "Reset Zoom",
        reload: "Reload Topology",
        reset: "Reset Topology"
    };

    return (
        <div className={styles.leftFloatMenu} style={{top: "104px", right: containerWidth / 2 - 71}}>
            <Tooltip title={btnTooltip.reload} placement="top">
                <Button className={styles.reloadBtn} onClick={reloadCallback} icon={<Icon component={ReloadSvg} />} />
            </Tooltip>
            <div style={{boxShadow: "0 1px 12px 1px #E6E8EA"}}>
                <Tooltip title={btnTooltip.zoomin} placement="top">
                    <Button
                        className={styles.zoomInBtn}
                        onClick={zoomInCallback}
                        icon={<Icon component={ZoomInSvg} />}
                    />
                </Tooltip>
                <Tooltip title={btnTooltip.zoomreset} placement="top">
                    <Button
                        className={styles.zoomResetBtn}
                        onClick={zoomResetCallback}
                        icon={<Icon component={FullscreenExitSvg} />}
                    />
                </Tooltip>
                <Tooltip title={btnTooltip.zoomout} placement="top">
                    <Button
                        className={styles.zoomOutBtn}
                        onClick={zoomOutCallback}
                        icon={<Icon component={ZoomOutSvg} />}
                    />
                </Tooltip>
            </div>
        </div>
    );
};

const TopoLegend = () => {
    return (
        <Flex
            vertical
            align="fix-end"
            style={{position: "absolute", top: "95px", right: "43px", zIndex: 1000, alignItems: "flex-end"}}
        >
            <Flex style={{gap: "14px"}}>
                <Flex style={{alignItems: "center", gap: "8px"}}>
                    <span style={{width: "10px", height: "10px", backgroundColor: "#14C9BB", borderRadius: "2px"}} />
                    <span>Normal</span>
                </Flex>
                <Flex style={{alignItems: "center", gap: "8px"}}>
                    <span style={{width: "10px", height: "10px", backgroundColor: "#F53F3F", borderRadius: "2px"}} />
                    <span>Error</span>
                </Flex>
            </Flex>
            <Flex style={{gap: "14px"}}>
                <Flex style={{alignItems: "center", gap: "8px"}}>
                    <span style={{width: "12px", height: "2px", backgroundColor: "#2BC174", borderRadius: "2px"}} />
                    <span>Success</span>
                </Flex>
                <Flex style={{alignItems: "center", gap: "8px"}}>
                    <span style={{width: "12px", height: "2px", backgroundColor: "#F53F3F", borderRadius: "2px"}} />
                    <span>Failed</span>
                </Flex>
            </Flex>
        </Flex>
    );
};

const LsAddConnectionModal = ({
    showLsAddConnectionModal,
    setShowLsAddConnectionModal,
    connectionInfo,
    vnInfo,
    refreshGraph
}) => {
    const [form] = Form.useForm();
    const [azList, setAzList] = useState([]);
    const [networkList, setNetworkList] = useState([]);

    useEffect(() => {
        console.log(vnInfo);
        const options = vnInfo.map(item => ({
            label: item.az_name,
            value: item.az_id,
            virtual_network: item.virtual_network || []
        }));
        setAzList(options);

        return () => {};
    }, [vnInfo]);

    const handleAzChange = azId => {
        const selectedAz = azList.find(az => az.value === azId);
        if (selectedAz) {
            const vns = selectedAz.virtual_network.map(vn => ({
                label: vn.name,
                value: vn.vn_id
            }));
            setNetworkList(vns);
            form.setFieldsValue({virtualNetworkId: undefined}); // 重置 vn 字段
        }
    };

    const handleSubmit = async values => {
        const data = {
            logicalNetworkId: connectionInfo.ln_id,
            logicalSwitchId: connectionInfo.ls_id,
            azId: values.azId,
            virtualNetworkId: values.virtualNetworkId
        };
        try {
            const response = await saveLogicalPort(data);
            if (response.status === 200) {
                message.success("Connection created successfully");
                refreshGraph();
                handleCancel();
            } else {
                message.error(`Failed to add the connection: ${response.info}`);
            }
        } catch (error) {
            console.error("Error submitting form:", error);
        }
    };
    const handleCancel = () => {
        setShowLsAddConnectionModal(false);
        form.resetFields();
        setAzList([]);
        setNetworkList([]);
    };

    const customFormItems = (
        <>
            <Form.Item name="azId" label="PoD Name" rules={[{required: true, message: "Please select a PoD"}]}>
                <Select onChange={handleAzChange} placeholder="Select PoD" style={{width: "280px"}} options={azList} />
            </Form.Item>
            <Form.Item
                name="virtualNetworkId"
                label="Network Name"
                rules={[{required: true, message: "Please select a network"}]}
            >
                <Select placeholder="Select Network" style={{width: "280px"}} options={networkList} />
            </Form.Item>
        </>
    );

    return (
        <AmpConCustomModalForm
            modalClass="ampcon-middle-modal"
            title={`Add Connection - [Logical Switch --- ${connectionInfo?.label}]`}
            isModalOpen={showLsAddConnectionModal}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 6
                }
            }}
            CustomFormItems={customFormItems}
            onCancel={handleCancel}
            onSubmit={handleSubmit}
        />
    );
};

const ConnectionModal = ({
    showConnectionModal,
    setShowConnectionModal,
    connectionInfo,
    inEditingConnection,
    setInEditingConnection,
    refreshGraph,
    vnInfo
}) => {
    const [form] = Form.useForm();
    const [modalTitle, setModalTitle] = useState("");
    const [customItems, setCustomItems] = useState(null);
    const [ipRanges, setIpRanges] = useState("");

    const [azList, setAzList] = useState([]);
    const [networkList, setNetworkList] = useState([]);

    const handleAzChange = azId => {
        const selectedAz = azList.find(az => az.value === azId);
        if (selectedAz) {
            const vns = selectedAz.virtual_network.map(vn => ({
                label: vn.name,
                value: vn.vn_id
            }));
            setNetworkList(vns);
            form.setFieldsValue({virtualNetworkId: undefined}); // 重置 vn 字段
        }
    };
    const disconnectedLogicalRouter = async id => {
        const res = await deleteLogicalInterface({id});
        if (res.status === 200) {
            message.success("Disconnect task start...");
            // refreshGraph();
        } else {
            message.error(`Failed to disconnect: ${res.info}`);
        }
    };
    const disconnectedLogicalSwitch = async (id, linkId, resourceType) => {
        const data = {
            id,
            link_id: linkId,
            type: resourceType
        };
        const res = await deleteLogicalPort(data);
        if (res.status === 200) {
            message.success("Disconnect task start...");
            // refreshGraph();
        } else {
            message.error(`Failed to disconnect: ${res.info}`);
        }
    };

    useEffect(() => {
        console.log(vnInfo);
        const options = vnInfo.map(item => ({
            label: item.az_name,
            value: item.az_id,
            virtual_network: item.virtual_network || []
        }));
        setAzList(options);

        return () => {};
    }, [vnInfo]);

    const getIpRange = dataStr => {
        const ipList = dataStr
            .split(",") // 分割每个条目
            .map(ip => ip.trim().split("/")[0]) // 去掉 `/24`
            .sort((a, b) => {
                // 排序 IP
                const ipToNum = ip => ip.split(".").reduce((acc, octet) => acc * 256 + parseInt(octet), 0);
                return ipToNum(a) - ipToNum(b);
            });

        const startIp = ipList[0];
        const endIp = ipList[ipList.length - 1];
        return `${startIp} - ${endIp}`; // 返回格式化的 IP 范围
    };

    useEffect(() => {
        if (!showConnectionModal) {
            return;
        }
        if (inEditingConnection) {
            setModalTitle("Edit Connection");
            if (connectionInfo.routerConnections?.length === 0 || connectionInfo.switchConnections?.length === 0) {
                setCustomItems(noneConnectionItem);
                return;
            }
            if (connectionInfo.sourceNode.shape === "logical-router") {
                setCustomItems(routerEditConnectionItem);
            } else if (connectionInfo.sourceNode.shape === "logical-switch") {
                setCustomItems(switchEditConnectionItem);
            }
            return;
        }
        if (connectionInfo.sourceNode.shape === "logical-router") {
            setModalTitle(`Add Connection - [Logical Router --- ${connectionInfo.sourceNode.store.data.label}]`);
            setCustomItems(routerAddConnectionItem);
        } else if (connectionInfo.sourceNode.shape === "logical-switch") {
            setModalTitle(`Add Connection - [Logical Switch --- ${connectionInfo.sourceNode.store.data.label}]`);
            setCustomItems(switchAddConnectionItem);
        }
    }, [showConnectionModal]);

    const handleSubmit = async values => {
        const node = connectionInfo.sourceNode;
        let data;
        if (node.shape === "logical-router") {
            data = {
                logicalNetworkId: connectionInfo.ln_id,
                logicalSwitchId: values.connectionDevice,
                logicalRouterId: node.store.data.db_id,
                anycast_ipv4: values.anycast_ipv4,
                virtual_ipv4_range: ipRanges,
                anycast_mac: values.anycast_mac
            };
        } else if (node.shape === "logical-switch") {
            data = {
                logicalNetworkId: connectionInfo.ln_id,
                logicalSwitchId: node.store.data.db_id,
                azId: values.azId,
                virtualNetworkId: values.virtualNetworkId
            };
        }
        try {
            let response;
            if (node.shape === "logical-router") {
                response = await saveLogicalInterface(data);
            } else if (node.shape === "logical-switch") {
                response = await saveLogicalPort(data);
            }
            if (response.status === 200) {
                setShowConnectionModal(false);
                message.success("Connection created successfully");
                setTimeout(() => {
                    handleCancel();
                }, 100);
                refreshGraph();
            } else {
                message.error(`Failed to add the connection: ${response.info}`);
            }
        } catch (error) {
            console.error("Error creating connection:", error);
        }
        console.log("Connection Form Data:", data);
    };
    const handleCancel = () => {
        setShowConnectionModal(false);
        setInEditingConnection(false);
        setAzList([]);
        setNetworkList([]);
        form.resetFields();
    };

    const noneConnectionItem = <div style={{marginBottom: "24px"}}>No connection information available.</div>;

    const routerAddConnectionItem = (
        <>
            <Form.Item name="connectionType" label="Connection Type" required initialValue="Logical Switch">
                <Input disabled style={{width: "280px", color: "#212519"}} />
            </Form.Item>
            <Form.Item
                name="connectionDevice"
                label="Connection Device"
                rules={[{required: true, message: "Please select a connection device"}]}
                initialValue={null}
            >
                <Select
                    placeholder="Select Connection Device"
                    style={{width: "280px"}}
                    options={connectionInfo.lsList}
                    onChange={value => {
                        const allocatedNetwork =
                            connectionInfo.lsList.find(item => item.value === value)?.allocatedNetwork || null;
                        form.setFieldsValue({allocatedNetwork});
                    }}
                />
            </Form.Item>
            <Form.Item name="allocatedNetwork" label="Allocated Network" initialValue={null}>
                <Input style={{width: "280px", color: "#212519"}} disabled />
            </Form.Item>
            <div className="ant-modal-title" style={{marginBottom: "24px"}}>
                Logical Router Config
            </div>
            <Form.Item
                name="anycast_ipv4"
                label="Anycast IPv4"
                initialValue={null}
                validateFirst
                rules={[
                    {
                        required: true,
                        pattern: ipv4NetRegex,
                        message: (
                            <span>
                                The Anycast IPv4 value must be in XXX.XXX.XXX.XXX/XX <br />
                                format and can&apos;t overlap with other Anycast IPv4 values.
                                <br />
                                The subnet mask length must be between /4 and /32.
                            </span>
                        )
                    },
                    {
                        validator: async (_, value) => {
                            const data = {
                                fabricId: connectionInfo.sourceNode.store.data.fabricId,
                                AnycastIp: form.getFieldValue("anycast_ipv4")
                            };
                            const res = await generateVirtualIPrange(data);
                            if (res.status === 200) {
                                setIpRanges(res.data);
                                form.setFieldsValue({
                                    virtual_ipv4_range: getIpRange(res.data)
                                });
                            } else {
                                form.setFieldsValue({
                                    virtual_ipv4_range: ""
                                });
                                const errMsg = res.info;
                                return Promise.reject(new Error(errMsg));
                            }
                            return Promise.resolve();
                        }
                    }
                ]}
                tooltip="The gateway mask needs to be large enough to avoid insufficient virtual IP allocation."
            >
                <Input placeholder="Enter Anycast IPv4" style={{width: "280px"}} />
            </Form.Item>
            <Form.Item
                name="virtual_ipv4_range"
                label="Virtual IPv4 Range"
                initialValue={null}
                rules={[
                    {
                        required: true,
                        message: "Please input Virtual IPv4 Range"
                    }
                ]}
                tooltip="The range of virtual IP addresses, which doesn't overlap with IP addresses of node devices."
            >
                <Input disabled style={{width: "280px", color: "#212519"}} />
            </Form.Item>
            <Form.Item
                name="anycast_mac"
                label="Anycast MAC"
                tooltip="Modification is not recommended."
                initialValue="00:02:00:02:00:02"
                rules={[
                    {
                        required: true,
                        pattern: /^([\dA-Fa-f][02468ACEace])(:[\dA-Fa-f]{2}){5}$/,
                        message: "Please input a valid unicast MAC address"
                    }
                ]}
            >
                <Input placeholder="Enter Anycast MAC" style={{width: "280px"}} />
            </Form.Item>
        </>
    );
    const routerEditConnectionItem = (
        <Flex vertical gap={30}>
            {connectionInfo?.routerConnections?.map((item, index) => (
                <div>
                    <div className="actionLink">
                        <span
                            style={{
                                fontSize: "18px",
                                fontWeight: "600",
                                color: "#212519",
                                marginRight: "25px"
                            }}
                        >
                            {item.connectionName}
                        </span>

                        <a onClick={() => disconnectedLogicalRouter(item.id)}>
                            <DisconnectedSvg style={{marginRight: "4px"}} />
                            Disconnect
                        </a>
                    </div>

                    <Row gutter={[0, 0]} style={{marginTop: "10px", width: "100%"}}>
                        <RenderRow label="Connection Type" value={item.connectionType} />
                        <RenderRow label="Connection Device" value={item.connectionDevice} bg="none" />
                        <RenderRow label="Interface IP" value={item.interfaceIP} />
                    </Row>
                </div>
            ))}
        </Flex>
    );
    const switchAddConnectionItem = (
        <>
            <Form.Item name="azId" label="PoD Name" rules={[{required: true, message: "Please select a PoD"}]}>
                <Select onChange={handleAzChange} placeholder="Select PoD" style={{width: "280px"}} options={azList} />
            </Form.Item>
            <Form.Item
                name="virtualNetworkId"
                label="Network Name"
                rules={[{required: true, message: "Please select a network"}]}
            >
                <Select placeholder="Select Network" style={{width: "280px"}} options={networkList} />
            </Form.Item>
        </>
    );

    const switchEditConnectionItem = (
        <Flex vertical gap={30}>
            {connectionInfo?.switchConnections?.map((item, index) => (
                <div>
                    <div className="actionLink">
                        <span
                            style={{
                                fontSize: "18px",
                                fontWeight: "600",
                                color: "#212519",
                                marginRight: "25px"
                            }}
                        >
                            {item.connectionName}
                        </span>
                        <a onClick={() => disconnectedLogicalSwitch(item.id, item.link_id, item.resourceType)}>
                            <DisconnectedSvg style={{marginRight: "4px"}} />
                            Disconnect
                        </a>
                    </div>

                    <Row gutter={[0, 0]} style={{marginTop: "10px", width: "100%"}}>
                        <RenderRow label="PoD Name" value={item.podName} />
                        <RenderRow label="Connection Device" value={item.connectionDevice} bg="none" />
                        <RenderRow label="Allocated Network" value={item.allocatedNetwork} />
                        <RenderRow label="Host IP" value={item.hostIP} bg="none" />
                        <RenderRow label="Connection Mode" value={item.connectionMode} />
                        <RenderRow label="Connection VLAN" value={item.connectionVLAN} bg="none" />
                    </Row>
                </div>
            ))}
        </Flex>
    );

    const editFooter = [
        <Divider style={{marginTop: 0, marginBottom: 20}} />,
        <Button
            key="cancel"
            type="primary"
            onClick={() => {
                handleCancel();
            }}
            style={{marginRight: 8}}
        >
            Back
        </Button>
    ];

    return (
        <AmpConCustomModalForm
            modalClass="ampcon-middle-modal"
            title={modalTitle}
            isModalOpen={showConnectionModal}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 6
                }
            }}
            CustomFormItems={customItems}
            onCancel={handleCancel}
            onSubmit={handleSubmit}
            footer={inEditingConnection ? editFooter : null}
        />
    );
};

const RenderRow = ({label, value, bg = "#F8FAFB"}) => (
    <>
        <Col
            span={8}
            style={{
                background: bg,
                height: "40px",
                paddingLeft: "24px",
                color: "#929A9E",
                display: "flex",
                alignItems: "center"
            }}
        >
            {label}
        </Col>
        <Col
            span={16}
            style={{
                background: bg,
                height: "40px",
                display: "flex",
                alignItems: "center"
            }}
        >
            {value}
        </Col>
    </>
);

const VnDetailModal = ({showVnDetailModal, setShowVnDetailModal, vnConnectionInfo}) => {
    const tableRef = useRef(null);
    const columns = [
        {
            title: "ID",
            dataIndex: "link_id",
            key: "link_id",
            sorter: (a, b) => a.link_id - b.link_id
        },
        {
            title: "Sysname",
            dataIndex: "host_name",
            key: "host_name",
            sorter: (a, b) => a.host_name.localeCompare(b.host_name)
        },
        {
            title: "Attachment Type",
            dataIndex: "attachment_type",
            key: "attachment_type",
            sorter: (a, b) => a.attachment_type.localeCompare(b.attachment_type)
        },
        {
            title: "NIC Port Group",
            dataIndex: "nic_port_group",
            key: "nic_port_group",
            sorter: (a, b) => a.nic_port_group.localeCompare(b.nic_port_group)
        },
        {
            title: "NIC Port",
            dataIndex: "nic_port",
            key: "nic_port",
            render: (text, record) => {
                if (record.nic_port.length === 0) {
                    return <span style={{display: "block"}}>--</span>;
                }
                return record.nic_port.map((item, index) => (
                    <span key={index} style={{display: "block"}}>
                        {item}
                    </span>
                ));
            }
        },
        {
            title: "Switch Port Group",
            dataIndex: "switch_port_group",
            key: "switch_port_group",
            sorter: (a, b) => a.switch_port_group.localeCompare(b.switch_port_group)
        },
        {
            title: "Connected Leaf Info",
            dataIndex: "connect_leaf_info",
            key: "connection_leaf_info",
            render: (text, record) => {
                return record.connect_leaf_info.map((item, index) => (
                    <span key={index} style={{display: "block"}}>
                        {item}
                    </span>
                ));
            }
        },
        {
            title: "Port Mode",
            dataIndex: "connect_mode",
            key: "connect_mode",
            sorter: (a, b) => a.connect_mode.localeCompare(b.connect_mode),
            render: (text, record) => {
                return <span>{text.charAt(0).toUpperCase() + text.slice(1)}</span>;
            }
        },
        {
            title: "VLAN",
            dataIndex: "vlan_id",
            key: "vlan_id",
            sorter: (a, b) => a.vlan_id - b.vlan_id
        }
    ];
    const searchFieldsList = ["az_name", "virtual_network_name"];

    return (
        <AmpConCustomModal
            title="Connection Details"
            isModalOpen={showVnDetailModal}
            modalClass="ampcon-max-modal"
            onCancel={() => setShowVnDetailModal(false)}
            childItems={
                <AmpConCustomTable
                    ref={tableRef}
                    modalClass="ampcon-max-modal"
                    columns={columns}
                    dataSource={vnConnectionInfo}
                    searchFieldsList={searchFieldsList}
                />
            }
            footer={null}
        />
    );
};

const HoverableIcon = ({collapse, handleClick}) => {
    const [icon, setIcon] = useState(collapse ? <ExpandedIcon /> : <ShrinkedIcon />);

    return (
        <div
            onClick={e => {
                handleClick(e);
                setIcon(collapse ? <ShrinkedIcon /> : <ExpandedIcon />);
            }}
            style={{position: "fixed", top: "49px", cursor: "pointer"}}
            className={styles.baseHover}
        >
            {icon}
        </div>
    );
};

const BaseNode = ({node, svgComponent, truncateStr = true, handlePortClick, ...props}) => {
    // const {label = "PICOS"} = node?.store?.data || {};
    const label = node?.store?.data?.label;
    // const displayLabel = label.length > 10 ? `${label.slice(0, 10)}...` : label;
    const displayLabel = truncateStr && label.length > 14 ? `${label.slice(0, 14)}...` : label;
    const [isLabelHover, setIsLabelHover] = useState(false);

    const containerStyle = {
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        height: "100%",
        width: "100%",
        textAlign: "center",
        zIndex: 500
    };

    const labelStyle = {
        fontFamily: "Lato",
        fontWeight: 400,
        fontSize: "14px",
        color: "#212519",
        marginTop: "-2px",
        whiteSpace: "nowrap",
        textOverflow: "ellipsis"
    };

    return (
        <div style={containerStyle} {...props}>
            <img src={svgComponent} alt="ss" className="nodeImage" />
            <div
                style={labelStyle}
                className="nodeLabel"
                onMouseEnter={() => setIsLabelHover(true)}
                onMouseLeave={() => setIsLabelHover(false)}
            >
                {isLabelHover || node.store.data.data?.temptNode ? label : displayLabel}
            </div>
            {node.shape === "logical-router" && node.store.data.hasEdge && (
                <HoverableIcon
                    handleClick={e => {
                        handlePortClick(node, e);
                    }}
                    collapse={node.store.data.collapse}
                />
            )}
        </div>
    );
};

const LogicalNetworkDetails = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const {name, id} = location.state || {};
    const [showCreateSwitchModal, setShowCreateSwitchModal] = useState(false);
    const [showCreateRouterModal, setShowCreateRouterModal] = useState(false);
    const [showCreateVirtualNetworkModal, setShowCreateVirtualNetworkModal] = useState(false);
    const [showConfigurationModal, setShowConfigurationModal] = useState(false);
    const [addedNodePosition, setAddedNodePosition] = useState({x: 0, y: 0});
    const [addedNodeID, setAddedNodeID] = useState(null);
    const [forceUpdate, setForceUpdate] = useState(false);
    const [fabricList, setFabricList] = useState([]);
    const [showConnectionModal, setShowConnectionModal] = useState(false);
    const [connectionInfo, setConnectionInfo] = useState({});
    const [inEditingConnection, setInEditingConnection] = useState(false);
    const [collapsed, setCollapsed] = useState(false);
    const [showSpin, setShowSpin] = useState(false);
    const [showNodeInfoPanel, setShowNodeInfoPanel] = useState(false);
    const [showEdgeInfoPanel, setShowEdgeInfoPanel] = useState(false);
    const [referenceNode, setReferenceNode] = useState(null);
    const [referenceEdge, setReferenceEdge] = useState(null);
    const [showLsAddConnectionModal, setShowLsAddConnectionModal] = useState(false);
    const [showDeviceManagementModal, setShowDeviceManagementModal] = useState(false);
    const [showVnDetailModal, setShowVnDetailModal] = useState(false);
    const [deviceID, setDeviceID] = useState(null);
    const [deviceType, setDeviceType] = useState(null);
    const [lsInfo, setLsInfo] = useState({});
    const [lrInfo, setLrInfo] = useState({});
    const [vnPanelInfo, setVnPanelInfo] = useState({});
    const [edgePanelInfo, setEdgePanelInfo] = useState({});
    const [vnConnectionInfo, setVnConnectionInfo] = useState([]);
    const [isNodeHover, setIsNodeHover] = useState(false);

    // useWhyDidYouUpdate("useWhyDidYouUpdateComponent", {isNodeHover});

    const mouse = useMouse();
    const [containerWidth, setContainerWidth] = useState(null);
    const [containerHeight, setContainerHeight] = useState(null);

    let lastMousePos = {x: 0, y: 0};
    let timer = null;

    function onMouseStable(e) {
        // console.log(e.target.className.includes("nodeInfoPanel"));
        if (
            (e.target && e.target.className === "nodeImage") ||
            (e.target && e.target.className === "nodeLabel") ||
            (e.target && e.target.className.toString().includes("nodeInfoPanel"))
        ) {
            return; // 如果鼠标在节点上，直接返回，不触发 hover 逻辑
        }

        setShowNodeInfoPanel(false);
    }

    const handleMouseMove = e => {
        const newPos = {x: e.clientX, y: e.clientY};

        setReferenceEdge({
            getBoundingClientRect: () => ({
                width: 0,
                height: 0,
                top: e.clientY,
                left: e.clientX,
                right: e.clientX,
                bottom: e.clientY
            }),
            contextElement: document.body
        });

        clearTimeout(timer);

        timer = setTimeout(() => {
            if (newPos.x === lastMousePos.x && newPos.y === lastMousePos.y) {
                onMouseStable(e);
            }
        }, 200);

        lastMousePos = newPos;
    };

    useEffect(() => {
        handleResize(); // 初始化时设置宽高
        window.addEventListener("resize", handleResize);
        document.addEventListener("mousemove", handleMouseMove);
        return () => {
            window.removeEventListener("resize", handleResize);
            document.removeEventListener("mousemove", handleMouseMove);
        };
    }, []);

    const handleResize = () => {
        const container = document.querySelector(".ant-card-body");
        if (container) {
            setContainerWidth(container.clientWidth - 48); // 减去左右 padding
            setContainerHeight(window.innerHeight - 220); // 减去顶部和底部的高度
        }
    };

    function refreshGraph() {
        fetchGraphData(graphRef.current, id);
    }

    useEffect(() => {
        const fetchFabricList = async () => {
            try {
                const response = await fetchFabricInfo();
                if (response.status === 200) {
                    setFabricList(
                        response.data
                            .filter(fabric => fabric.has_vlan_domain === true)
                            .map(fabric => ({
                                label: fabric.fabric_name,
                                value: fabric.id
                            }))
                    );
                } else {
                    message.error(response.info);
                }
            } catch (error) {
                message.error("Failed to fetch fabric list.");
            }
        };
        fetchFabricList();
        return () => {
            setFabricList([]); // 清理状态
        };
    }, []);

    const registerCustomNode = () => {
        register({
            shape: "logical-router",
            width: nodeWidth,
            height: nodeHeight,
            component: props => {
                const {status} = props.node.store.data;
                return (
                    <BaseNode
                        {...props}
                        handlePortClick={handlePortClick}
                        svgComponent={status === 0 || status === 2 ? logicalRouterSvg : logicalRouterErrSvg}
                    />
                );
            }
        });
        register({
            shape: "logical-switch",
            width: nodeWidth,
            height: nodeHeight,
            // component: <BaseNode svgComponent={logicalSwitchSvg} />
            component: props => {
                const {status} = props.node.store.data;
                return (
                    <BaseNode
                        {...props}
                        svgComponent={status === 0 || status === 2 ? logicalSwitchSvg : logicalSwitchErrSvg}
                    />
                );
            }
        });
        register({
            shape: "virtual-network",
            width: nodeWidth,
            height: nodeHeight,
            component: props => {
                const {status} = props.node.store.data;
                return (
                    <BaseNode
                        {...props}
                        svgComponent={status === 0 || status === 2 ? virtualNetworkSvg : virtualNetworkErrSvg}
                    />
                );
            }
        });
    };

    const registerEventlistener = (container, graph) => {
        graph.on("node:contextmenu", ({node, e}) => {
            e.preventDefault();
            menuRef.current?.showDeviceRightClickPopUpMenu(node, e);
        });
        graph.on("blank:mousedown", () => {
            menuRef.current?.hideDeviceRightClickPopUpMenu();
            setShowNodeInfoPanel(false);
            setShowEdgeInfoPanel(false);
        });

        graph.on("blank:click", ({e, x, y}) => {
            menuRef.current?.hideDeviceRightClickPopUpMenu();
            setShowNodeInfoPanel(false);
            setShowEdgeInfoPanel(false);
            // setShowMenu(false); // 点击空白处隐藏菜单
        });
        graph.on("node:move", async ({x, y, node}) => {
            if (!node.store.data.collapse) return;
            graph.once("node:moved", async ({e, x, y, node}) => {
                await updateTopoPosition({
                    id: node.store.data.db_id,
                    type: node.shape.replace("-", "_"),
                    position_x: node.position().x,
                    position_y: node.position().y
                });

                const successors = graph.getSuccessors(node);

                setShowSpin(true);
                successors.forEach(async connectedNode => {
                    const data = {
                        id: connectedNode.store.data.db_id,
                        type: connectedNode.shape.replace("-", "_"),
                        position_x: connectedNode.position().x,
                        position_y: connectedNode.position().y
                    };
                    await updateTopoPosition(data);
                });
                setShowSpin(false);
            });
        });
        graph.on("node:moving", ({node}) => {
            updateEdgeVertices(node);
        });
        graph.on("node:change:position", ({node}) => {
            updateEdgeVertices(node);
        });
        graph.on("cell:click", ({cell, e}) => {
            console.log(cell.prop());
        });
        graph.on("edge:mouseenter", ({edge, e}) => {
            setShowEdgeInfoPanel(true);
            setShowNodeInfoPanel(false);
            const info = {
                status: edge.store.data.status,
                errMsg: edge.store.data.errMsg || []
            };
            setEdgePanelInfo(info);
        });
        graph.on("edge:mouseleave", () => {
            setShowEdgeInfoPanel(false);
        });
        graph.on("node:mouseenter", ({node, e, view}) => {
            if (node.shape !== "virtual-network") {
                setShowNodeInfoPanel(false);
                return;
            }
            setIsNodeHover(true);
            const connectedEdge = graph.getConnectedEdges(node);
            if (connectedEdge.length === 0) {
                return;
            }
            const edge = connectedEdge[0];
            const info = {
                vl2: edge.store.data.allocatedNetwork || "",
                podResource: edge.store.data.azName || "",
                hostNums: edge.store.data.abnormalRate
            };
            setVnPanelInfo(info);
            setVnConnectionInfo(edge.store.data.connectInfo || []);

            setReferenceNode({
                getBoundingClientRect: () => ({
                    width: 0,
                    height: 0,
                    top: e.clientY,
                    left: e.clientX,
                    right: e.clientX,
                    bottom: e.clientY
                }),
                contextElement: container
            });
            setShowNodeInfoPanel(true);
        });

        graph.on("node:moved", async ({e, x, y, node}) => {
            if (node.store.data.collapse) {
                return;
            }
            const data = {
                id: node.store.data.db_id,
                type: node.shape.replace("-", "_"),
                position_x: node.position().x,
                position_y: node.position().y
            };
            setShowSpin(true);
            try {
                const response = await updateTopoPosition(data);
                if (response.status === 200) {
                    setShowSpin(false);
                    message.success("Node position updated successfully");
                } else {
                    setShowSpin(false);
                    message.error(`Failed to update node position: ${response.info}`);
                }
            } catch (error) {
                console.log("Error updating node position:", error);
            }
            console.log("Node moved:", data);
        });
    };

    const addPlungin = graph => {
        const snapline = new Snapline({
            enabled: true
            // sharp: true
        });
        const minimap = new MiniMap({
            container: minimapRef.current,
            width: 200,
            height: 180,
            padding: 10,
            scalable: true,
            scaling: 1,
            minScale: 0.1,
            maxScale: 1,
            snapline: true,
            zIndex: 500,
            minimapContainerStyle: {
                position: "absolute",
                bottom: 10,
                right: 10,
                zIndex: 1000,
                border: "1px solid #d9d9d9",
                borderRadius: "4px",
                boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)"
            }
        });
        const dnd = new Dnd({
            target: graph,
            dndContainer: dndContainer.current,
            scaled: true,
            getDropNode: draggingNode => {
                const newNode = draggingNode.clone();
                newNode.setSize(nodeWidth, nodeHeight);
                return newNode;
            }
        });
        dndRef.current = dnd;
        graph.use(dnd);
        graph.use(snapline);
        graph.use(minimap);
    };

    const containerRef = useRef(null);
    const graphRef = useRef(null);
    const dndContainer = useRef(null);
    const dndRef = useRef(null);
    const minimapRef = useRef(null);
    const menuRef = useRef(null);

    function buildNodeData(node, connectedNodes) {
        const shapeMapping = {
            logical_router: "logical-router",
            logical_switch: "logical-switch",
            virtual_network: "virtual-network"
        };
        const lrPort = {
            groups: {
                out: {
                    position: "bottom",
                    attrs: {
                        circle: {
                            r: 0,
                            magnet: false,
                            stroke: "#31D0C6",
                            strokeWidth: 1,
                            fill: "white"
                        }
                    }
                },
                in: {
                    position: {
                        name: "absolute",
                        args: {x: 100, y: 100}
                    },
                    attrs: {
                        circle: {
                            r: 0,
                            magnet: false,
                            stroke: "#31D0C6",
                            strokeWidth: 1,
                            fill: "none"
                        }
                    }
                }
            },
            items: [
                {
                    id: "1",
                    group: "out",
                    args: {
                        dy: 10
                    }
                },
                {
                    id: "2",
                    group: "in",
                    args: {
                        x: "60%",
                        y: 320
                    }
                }
            ]
        };
        const otherPort = {
            groups: {
                out: {
                    position: "bottom",
                    attrs: {
                        circle: {
                            r: 0,
                            magnet: false,
                            stroke: "#31D0C6",
                            strokeWidth: 1,
                            fill: "white"
                        }
                    }
                },
                in: {
                    position: {
                        name: "absolute",
                        args: {x: 20, y: -2}
                    },
                    attrs: {
                        circle: {
                            r: 0,
                            magnet: false,
                            stroke: "#31D0C6",
                            strokeWidth: 1,
                            fill: "none"
                        }
                    }
                }
            },
            items: [
                {
                    id: "1",
                    group: "out",
                    args: {
                        dy: 5
                    }
                },
                {
                    id: "2",
                    group: "in"
                    // args: {
                    //     x: "60%",
                    //     y: 320
                    // }
                }
            ]
        };
        const {
            id,
            topology_id,
            status,
            type,
            position_x,
            position_y,
            name,
            port_count,
            fabric_id,
            arp_nd_suppress,
            l3vni,
            vrf_name
        } = node;
        const nodeData = {
            id: topology_id,
            shape: shapeMapping[type],
            x: position_x,
            y: position_y,
            label: name || "",
            fabricId: fabric_id || "",
            db_id: id || "",
            status,
            portCount: port_count || null,
            l3vni: l3vni || null,
            vrfName: vrf_name || null,
            ports: type === "logical_router" ? lrPort : otherPort,
            collapse: false,
            hasEdge: connectedNodes.has(topology_id),
            arpNdSuppress: arp_nd_suppress || false
        };
        return nodeData;
    }
    function buildEdgeData(edge) {
        const {id, topology_id, source, status, target, vlan, anycast_ipv4, connect_info, az_name, allocated_network} =
            edge;
        const edgeData = {
            id: topology_id,
            db_id: id,
            source: {
                cell: source,
                port: "1"
            },
            target: {
                cell: target,
                port: "2"
            },
            // router: "normal",
            connector: {
                name: "rounded",
                args: {
                    radius: 20
                }
            },
            zIndex: 0,
            status,
            interactable: false,
            allocatedNetwork: allocated_network || "Unknown Network",
            interfaceIP: anycast_ipv4 || "Unknown IP",
            azName: az_name || "Unknown Pod",
            connectionVLAN: vlan || "Unknown VLAN",
            connectInfo: connect_info || [],
            errMsg: edge.err_msgs || [],
            abnormalRate: connect_info
                ? `${
                      connect_info.filter(
                          item => item.status === "Disconnect Failed" || item.status === "Connect Failed"
                      ).length
                  }/${connect_info.length}`
                : "0/0",
            attrs: {
                line: {
                    stroke: status === 0 || status === 2 ? "#14C9BB" : "#FF4D4F",
                    strokeWidth: 1,
                    targetMarker: {
                        name: "path",
                        width: 12,
                        height: 8
                    }
                }
            }
        };
        return edgeData;
    }

    const fetchGraphData = async (graph, id) => {
        const cells = graph.getCells();
        if (cells.length > 0) {
            graph.clearCells();
        }

        try {
            const response = await fetchLogicalNetworkDetail({id});
            if (response.status === 200) {
                const {nodes = [], edges = []} = response.data || {};
                const connectedNodes = new Set();
                if (edges.length > 0) {
                    edges.forEach(edge => {
                        connectedNodes.add(edge.source);
                        connectedNodes.add(edge.target);
                    });
                }
                const graphNodes = [];
                const graphEdges = [];
                nodes.forEach(node => {
                    graphNodes.push(buildNodeData(node, connectedNodes));
                });
                graph.addNodes(graphNodes);
                edges.forEach(edge => {
                    graphEdges.push(buildEdgeData(edge));
                });
                graph.addEdges(graphEdges);
            }
        } catch (error) {
            console.error("Error fetching graph data:", error);
        }
        updateEdgeVertices();
    };

    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        const graph = new Graph({
            container,
            width: containerWidth || 1200,
            height: containerHeight || 800,
            background: {
                color: "#F8FAFB"
            },
            grid: false,
            // connector: "smooth",
            mousewheel: {
                enabled: true,
                modifiers: "ctrl"
            },
            panning: true,
            resizing: true,
            interacting: {
                nodeMovable: true,
                edgeMovable: false
            }
        });
        graphRef.current = graph;
        registerCustomNode();
        addPlungin(graph);
        registerEventlistener(container, graph);
        fetchGraphData(graph, id);
        return () => {
            if (graphRef.current) {
                graphRef.current.dispose();
                graphRef.current = null;
            }
        };
    }, [forceUpdate]);

    function handlePortClick(node, e) {
        e.stopPropagation();
        const {shape, store} = node;
        const {data} = store;
        if (shape === "logical-router") {
            const {collapse} = data;
            console.log("collapse:", collapse);
            const tempt = !collapse;
            data.collapse = tempt;
            if (!collapse) {
                collapseConnected(node);
            } else {
                expandConnected(node);
            }
        }
    }

    useEffect(() => {
        const updateStatus = setInterval(() => {
            if (graphRef.current) {
                updateCellsStatus();
                console.log("test");
            }
        }, 20000);

        return () => clearInterval(updateStatus); // 清除定时器
    }, []);

    async function updateCellsStatus() {
        try {
            const response = await fetchLogicalNetworkDetail({id});
            if (response.status === 200) {
                const {nodes, edges} = response.data;
                const nodeids = nodes.map(node => node.topology_id);
                const edgeids = edges.map(edge => edge.topology_id);
                const allCells = graphRef.current.getCells();
                const connectedNodes = new Set();
                if (edges.length > 0) {
                    edges.forEach(edge => {
                        connectedNodes.add(edge.source);
                        connectedNodes.add(edge.target);
                    });
                }
                allCells.forEach(cell => {
                    if (!nodeids.includes(cell.id) && !edgeids.includes(cell.id) && !cell.store.data.data?.temptNode) {
                        graphRef.current.removeCell(cell);
                        return;
                    }
                    if (cell.isNode() && !cell.store.data.data?.temptNode) {
                        const db_status = nodes.find(node => node.topology_id === cell.id)?.status;
                        const topo_status = cell.store.data.status;
                        if (db_status !== topo_status) {
                            cell.prop("status", db_status);
                        }
                    }
                });
                nodes.forEach(node => {
                    if (!graphRef.current.hasCell(node.topology_id)) {
                        graphRef.current.addNode(buildNodeData(node, connectedNodes));
                    }
                });
                edges.forEach(edge => {
                    const {
                        id,
                        topology_id,
                        source,
                        status,
                        target,
                        vlan,
                        anycast_ipv4,
                        connect_info,
                        az_name,
                        allocated_network
                    } = edge;
                    const topoEdge = graphRef.current.getCellById(topology_id);
                    if (!topoEdge) {
                        graphRef.current.addEdge(buildEdgeData(edge));
                        return; // 如果边不存在，直接添加
                    }
                    topoEdge.prop("status", status);
                    topoEdge.prop("db_id", id);
                    topoEdge.prop("allocatedNetwork", allocated_network || "Unknown Network");
                    topoEdge.prop("interfaceIP", anycast_ipv4 || "Unknown IP");
                    topoEdge.prop("azName", az_name || "Unknown Pod");
                    topoEdge.prop("connectionVLAN", vlan || "Unknown VLAN");
                    topoEdge.prop("connectInfo", connect_info || []);
                    topoEdge.prop("errMsg", edge.err_msgs || []);
                    topoEdge.prop(
                        "abnormalRate",
                        connect_info
                            ? `${
                                  connect_info.filter(
                                      item => item.status === "Disconnect Failed" || item.status === "Connect Failed"
                                  ).length
                              }/${connect_info.length}`
                            : "0/0"
                    );
                    topoEdge.setAttrs({
                        line: {
                            stroke: status === 0 || status === 2 ? "#14C9BB" : "#FF4D4F",
                            strokeWidth: 1,
                            targetMarker: {
                                name: "path",
                                width: 12,
                                height: 8
                            }
                        }
                    });
                });
                updateLrCollapseStatus();
                updateEdgeVertices();
            } else {
                message.error(`Failed to update cells status: ${response.info}`);
            }
        } catch (error) {
            console.error("Error updating cells status:", error);
        }
    }

    function updateEdgeVertices(cell) {
        if (!cell) {
            graphRef.current.getEdges().forEach(edge => {
                const sourcePoint = edge.getSourcePoint();
                const targetPoint = edge.getTargetPoint();
                edge.setVertices(calculateVertices(sourcePoint, targetPoint));
            });
            return;
        }
        if (cell.isEdge()) {
            const sourcePoint = cell.getSourcePoint();
            const targetPoint = cell.getTargetPoint();
            cell.setVertices(calculateVertices(sourcePoint, targetPoint));
        } else {
            const edges = graphRef.current.getConnectedEdges(cell);
            edges.forEach(edge => {
                const sourcePoint = edge.getSourcePoint();
                const targetPoint = edge.getTargetPoint();
                edge.setVertices(calculateVertices(sourcePoint, targetPoint));
            });
        }
    }

    function calculateVertices(sourcePoint, targetPoint) {
        const midY = (sourcePoint.y + targetPoint.y) / 2;

        const vertex1 = {
            x: sourcePoint.x,
            y: midY
        };

        const vertex2 = {
            x: targetPoint.x,
            y: midY
        };

        return [vertex1, vertex2];
    }

    function collapseConnected(node) {
        const successors = graphRef.current.getSuccessors(node);
        successors.forEach(successor => {
            node.addChild(successor);
            successor.hide();
        });
    }

    function expandConnected(rootNode) {
        const successors = rootNode.getChildren();
        successors.forEach(successor => {
            rootNode.setChildren([]);
            successor.show();
        });
    }
    function zoomInCallback() {
        if (graphRef.current) {
            const currentZoom = graphRef.current.zoom();
            graphRef.current.zoom(currentZoom * 0.1);
        }
    }
    function zoomOutCallback() {
        if (graphRef.current) {
            const currentZoom = graphRef.current.zoom();
            graphRef.current.zoom(currentZoom * -0.1);
        }
    }
    function zoomResetCallback() {
        if (graphRef.current) {
            graphRef.current.zoomTo(1);
        }
    }
    function reloadCallback() {
        refreshGraph();
    }
    function resetCallback() {}

    function successAddedCallback() {
        refreshGraph();
    }
    function errorAddedCallback() {
        graphRef.current.removeNode(addedNodeID);
    }
    function get_vn_by_ls(node) {
        const vn = graphRef.current
            .getNeighbors(node, {outcoming: true})
            .filter(neighbor => neighbor.shape === "virtual-network");
        if (vn.length > 0) {
            return vn[0].store.data.label || "Unnamed Virtual Network";
        }
        return null;
    }

    function updateLrCollapseStatus() {
        const nodes = graphRef.current.getNodes();
        nodes.forEach(node => {
            if (node.shape === "logical-router") {
                const hasEdge = graphRef.current.getConnectedEdges(node).length > 0;
                node.prop("hasEdge", hasEdge);
            }
        });
    }

    const [vnInfo, setVnInfo] = useState([]);
    const fetchVNlist = async id => {
        try {
            const response = await fetchConnectableVirtualNetworks({logicalSwitchId: id});
            if (response.status === 200) {
                console.log("Connectable virtual networks:", response.data);
                setVnInfo(response.data);
            } else {
                message.error(`Failed to fetch connectable virtual networks: ${response.info}`);
            }
        } catch (error) {
            console.log("Error fetching connectable virtual networks:", error);
        }
    };

    async function addConnectionCallback(node) {
        if (node.shape === "logical-switch") {
            if (graphRef.current.getConnectedEdges(node, {outgoing: true}).length > 0) {
                message.error("Logical Switch already connected to a Virtual Network.");
                return;
            }

            await fetchVNlist(node.store.data.db_id);
            setConnectionInfo({
                deviceType: node.shape,
                sourceNode: node,
                ln_id: id,
                ls_id: node.store.data.db_id,
                label: node.store.data.label || "Unnamed Switch"
            });
            setShowLsAddConnectionModal(true);
            return;
        }
        const availableNodes = graphRef.current
            .getNodes()
            .filter(
                n =>
                    n.shape === "logical-switch" &&
                    n.store.data.fabricId === node.store.data.fabricId &&
                    graphRef.current.getNeighbors(n, {incoming: true}).length === 0
            );
        const info = availableNodes?.map(n => ({
            value: n.store.data.db_id,
            label: n.store.data.label || "Unnamed Switch",
            allocatedNetwork: get_vn_by_ls(n) // 假设有一个函数获取虚拟网络
        }));
        setConnectionInfo({
            deviceType: node.shape,
            sourceNode: node,
            ln_id: id,
            lsList: info
        });

        setInEditingConnection(false);
        setShowConnectionModal(true);
    }
    function editConnectionCallback(node) {
        setInEditingConnection(true);
        setShowConnectionModal(true);
        const connections = [];
        const deviceType = node.shape;
        if (deviceType === "logical-router") {
            const connectedEdges = graphRef.current.getConnectedEdges(node);

            connectedEdges.forEach((connectedEdge, index) => {
                const targetNode = connectedEdge.getTargetNode();
                connections.push({
                    id: connectedEdge.store.data.db_id,
                    connectionDevice: targetNode?.store.data.label,
                    interfaceIP: connectedEdge.store.data.interfaceIP,
                    connectionType: "Logical Switch",
                    connectionName: `Connection ${index + 1}`
                });
            });
            setConnectionInfo({
                deviceType,
                sourceNode: node,
                ln_id: id,

                routerConnections: connections
            });
        } else if (deviceType === "logical-switch") {
            const connectedEdge = graphRef.current.getConnectedEdges(node, {outgoing: true})[0];
            if (!connectedEdge) {
                setConnectionInfo({
                    deviceType,
                    sourceNode: node,
                    ln_id: id,
                    switchConnections: []
                });
                return;
            }
            connectedEdge.store.data.connectInfo.forEach((conn, index) => {
                connections.push({
                    id: connectedEdge.store.data.db_id,
                    connectionDevice: conn.host_name,
                    podName: connectedEdge.store.data.azName,
                    allocatedNetwork: connectedEdge.store.data.allocatedNetwork,
                    hostIP: conn.host_ip,
                    connectionMode: conn.connect_mode,
                    connectionVLAN: connectedEdge.store.data.connectionVLAN,
                    connectionName: `Connection ${index + 1}`,
                    resourceType: conn.type,
                    link_id: conn.link_id
                });
            });

            setConnectionInfo({
                deviceType,
                sourceNode: node,
                ln_id: id,
                switchConnections: connections
            });
        }

        console.log("Edit connection for node:", node);
    }
    function deviceManagementCallback(node) {
        setShowDeviceManagementModal(true);
        setDeviceID(node.store.data.db_id);
        setDeviceType(node.shape);
        if (node.shape === "logical-router") {
            setLrInfo({
                name: node.store.data.label,
                status: node.store.data.status,
                l3vni: node.store.data.l3vni,
                vrf_name: node.store.data.vrfName,
                ports: node.store.data.portCount
            });
        } else if (node.shape === "logical-switch") {
            setLsInfo({
                name: node.store.data.label,
                status: node.store.data.status,
                arp_nd_suppress: node.store.data.arpNdSuppress
            });
        }
        console.log("Device management for node:", node);
    }
    function deleteNodeCallback(node) {
        confirmModalAction(`Are you sure you want to delete node ${node.store.data.label}?`, async () => {
            const isConnected = graphRef.current.getConnectedEdges(node).length > 0;
            if (isConnected) {
                message.error("Cannot delete node that has connections.");
                return;
            }
            let res;
            if (node.shape === "logical-router") {
                res = await deleteLogicalRouter({id: node.store.data.db_id});
            } else if (node.shape === "logical-switch") {
                res = await deleteLogicalSwitch({id: node.store.data.db_id});
            } else if (node.shape === "virtual-network") {
                res = await deleteVirtualNetwork({id: node.store.data.db_id});
            }
            if (res.status === 200) {
                // graphRef.current.removeNode(node.id);
                refreshGraph();
                message.success("Node deleted successfully.");
            } else {
                message.error(res.info || "Failed to delete node.");
            }
        });
    }

    function startDrag(e) {
        const shape = e.currentTarget.getAttribute("deviceType");
        const node = graphRef.current.createNode({
            shape,
            width: nodeWidth + 50,
            height: nodeHeight,
            component: <BaseNode truncateStr={false} svgComponent={shape_svg_mapping[shape]} />,
            label: shape_label_mapping[shape],
            status: 0,
            data: {
                temptNode: true
            }
        });
        graphRef.current.once("node:added", ({node}) => {
            if (!node.getData("temptNode")) return;
            setAddedNodeID(node.id);
            setAddedNodePosition({
                x: node.position().x,
                y: node.position().y
            });
            switch (node.shape) {
                case "logical-router":
                    setShowCreateRouterModal(true);
                    break;
                case "logical-switch":
                    setShowCreateSwitchModal(true);
                    break;
                case "virtual-network":
                    setShowCreateVirtualNetworkModal(true);
                    break;
                default:
                    console.warn("Unknown shape type:", node.shape);
            }
        });
        dndRef.current.start(node, e);
    }

    return (
        <div style={{minHeight: "100%"}}>
            <Card style={{display: "flex", flex: 1, minHeight: "100%", paddingBottom: "12px"}}>
                {/* <div className="actionLink">
                    <a onClick={() => navigate("/service_provision/logical_networks")}>
                        <ArrowLeftOutlined style={{marginRight: "8px"}} />
                        <span>Back</span>
                    </a>
                </div> */}
                <Button
                    type="primary"
                    style={{margin: "12px 16px 24px 0"}}
                    onClick={() => setShowConfigurationModal(true)}
                >
                    <Icon component={FileSvg} />
                    Configuration List
                </Button>
                {/* <Button
                    icon={<Icon component={refreshSvg} />}
                    onClick={() => {
                        refreshGraph();
                    }}
                >
                    Refresh
                </Button> */}
                <Flex align="center" style={{width: "100%", backgroundColor: "#f8fafb"}}>
                    <div
                        className={`${styles.leftSider} ${collapsed ? styles.collapsedLeftSider : ""}`}
                        style={{
                            height: containerHeight || "100%"
                        }}
                    >
                        {!collapsed &&
                            Object.keys(shape_svg_mapping).map(shape => (
                                <BaseNode
                                    key={shape}
                                    node={{store: {data: {label: shape_label_mapping[shape]}}}}
                                    svgComponent={shape_example_svg_mapping[shape]}
                                    truncateStr={false}
                                    onMouseDown={startDrag}
                                    deviceType={shape}
                                    style={{cursor: "move", marginBottom: "32px"}}
                                />
                            ))}
                    </div>
                    <div
                        style={{
                            backgroundColor: "white",
                            pointerEvents: "all",
                            cursor: "pointer",
                            width: "14px",
                            height: "51px",
                            borderRadius: "0 10px 10px 0",
                            alignContent: "center",
                            border: "1px solid #d9d9d9",
                            borderLeft: "none"
                        }}
                        onClick={() => {
                            setCollapsed(!collapsed);
                            handleResize(); // 切换折叠状态时重新计算容器宽高
                        }}
                    >
                        {collapsed ? (
                            <RightOutlined style={{color: "#ccd1d1"}} />
                        ) : (
                            <LeftOutlined style={{color: "#ccd1d1"}} />
                        )}
                    </div>
                    <div
                        ref={containerRef}
                        style={{
                            // flex: 1,
                            width: containerWidth || "100%",
                            height: containerHeight || "100%",
                            overflow: "hidden",
                            position: "relative"
                        }}
                    />
                </Flex>
                <Spin spinning={showSpin} style={{position: "absolute", top: "50%", left: "50%"}} />
                <NodeInfoPanel
                    showNodeInfoPanel={showNodeInfoPanel}
                    referenceElement={referenceNode}
                    vnPanelInfo={vnPanelInfo}
                    setShowVnDetailModal={setShowVnDetailModal}
                    setShowNodeInfoPanel={setShowNodeInfoPanel}
                />
                <EdgeInfoPanel
                    referenceElement={referenceEdge}
                    showEdgeInfoPanel={showEdgeInfoPanel}
                    edgePanelInfo={edgePanelInfo}
                />
                <TopoLegend />
                <div ref={minimapRef} style={{position: "absolute", bottom: 10, right: 10, zIndex: 999}} />
                <TopoLeftFloatMenu
                    zoomInCallback={zoomInCallback}
                    zoomOutCallback={zoomOutCallback}
                    zoomResetCallback={zoomResetCallback}
                    reloadCallback={reloadCallback}
                    collapsed={collapsed}
                    containerWidth={containerWidth}
                />
            </Card>
            <CreateLogicalSwitchModal
                showCreateSwitchModal={showCreateSwitchModal}
                setShowCreateSwitchModal={setShowCreateSwitchModal}
                fabricList={fabricList}
                addedNodePosition={addedNodePosition}
                refreshGraph={refreshGraph}
                ln_id={id}
                successAddedCallback={successAddedCallback}
                errorAddedCallback={errorAddedCallback}
            />
            <CreateLogicalRouterModal
                showCreateRouterModal={showCreateRouterModal}
                setShowCreateRouterModal={setShowCreateRouterModal}
                fabricList={fabricList}
                addedNodePosition={addedNodePosition}
                refreshGraph={refreshGraph}
                ln_id={id}
                successAddedCallback={successAddedCallback}
                errorAddedCallback={errorAddedCallback}
            />
            <CreateVirtualNetworkModal
                showCreateVirtualNetworkModal={showCreateVirtualNetworkModal}
                setShowCreateVirtualNetworkModal={setShowCreateVirtualNetworkModal}
                fabricList={fabricList}
                addedNodePosition={addedNodePosition}
                ln_id={id}
                successAddedCallback={successAddedCallback}
                errorAddedCallback={errorAddedCallback}
            />
            <ConfigurationListModal
                showConfigurationModal={showConfigurationModal}
                setShowConfigurationModal={setShowConfigurationModal}
                ln_id={id}
            />
            <RightClickPopUpMenu
                ref={menuRef}
                addConnectionCallback={addConnectionCallback}
                editConnectionCallback={editConnectionCallback}
                deviceManagementCallback={deviceManagementCallback}
                deleteNodeCallback={deleteNodeCallback}
            />
            <ConnectionModal
                showConnectionModal={showConnectionModal}
                setShowConnectionModal={setShowConnectionModal}
                connectionInfo={connectionInfo}
                inEditingConnection={inEditingConnection}
                setInEditingConnection={setInEditingConnection}
                refreshGraph={refreshGraph}
                vnInfo={vnInfo}
            />
            <LsAddConnectionModal
                showLsAddConnectionModal={showLsAddConnectionModal}
                setShowLsAddConnectionModal={setShowLsAddConnectionModal}
                connectionInfo={connectionInfo}
                refreshGraph={refreshGraph}
                vnInfo={vnInfo}
                setVnInfo={setVnInfo}
            />
            <DeviceManagementModal
                showDeviceManagementModal={showDeviceManagementModal}
                setShowDeviceManagementModal={setShowDeviceManagementModal}
                deviceID={deviceID}
                deviceType={deviceType}
                ls_info={lsInfo}
                lr_info={lrInfo}
            />
            <VnDetailModal
                showVnDetailModal={showVnDetailModal}
                setShowVnDetailModal={setShowVnDetailModal}
                vnConnectionInfo={vnConnectionInfo}
            />
        </div>
    );
};
export default LogicalNetworkDetails;
