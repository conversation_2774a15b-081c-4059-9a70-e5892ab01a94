import Icon, {ArrowLeftOutlined} from "@ant-design/icons";
import React, {useMemo, useState, useEffect, useRef} from "react";
import {searchSvg, addSvg} from "@/utils/common/iconSvg";
import {createColumnConfigMultipleParams, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {message, Space, Flex, Table, Card, Button, Input} from "antd";
import {
    fetchNetworkIAccessDeviceInfo,
    dissociateAssociate,
    fetchGroupList
} from "@/modules-ampcon/apis/dc_virtual_resource_api";
import AssociateDeviceModal from "./add_associate_device_modal";
import ConnectDetailModal from "./connect_detail_modal";
import ExpandIcon from "@/modules-ampcon/components/expand_icon";
import {useLocation} from "react-router-dom";

const CreateAssociateDevice = () => {
    const location = useLocation();
    const {record} = location.state || {}; // 防御性解构
    const vl2 = record || {};
    const AssociateDeviceModalRef = useRef(null);
    const ConnectDetailModalRef = useRef(null);
    const tableRef = useRef();

    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [nodeGroupOptions, setNodeGroupOptions] = useState([]);
    const [expandedRowKeys, setExpandedRowKeys] = useState([]);
    const [searchKeyword, setSearchKeyword] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);

    const filteredData = useMemo(() => {
        if (!searchKeyword) return data;
        const keyword = searchKeyword.toLowerCase();
        return data
            .map(parent => {
                const matchedChildren = (parent.children || []).filter(child =>
                    [child.host_name, child.link_type, child.pg_name, child.switch_pg_name, child.connect_mode].some(
                        field => field?.toLowerCase().includes(keyword)
                    )
                );
                const isParentMatch = [parent.nodegroup_name].some(field => field?.toLowerCase().includes(keyword));
                if (isParentMatch || matchedChildren.length > 0) {
                    return {
                        ...parent,
                        children: matchedChildren.length > 0 ? matchedChildren : parent.children
                    };
                }
                return null;
            })
            .filter(item => item !== null);
    }, [searchKeyword, data]);

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setLoading(true);
        try {
            const res = await fetchNetworkIAccessDeviceInfo(vl2.id);
            const nodeGroupOptions = await fetchNodeGroupList();
            if (Array.isArray(res?.data)) {
                const transformedData = res.data.map((item, index) => {
                    const matchedGroup = nodeGroupOptions.find(g => g.nodegroup_name === item.nodegroup_name);

                    const children = Array.isArray(item.pg_info)
                        ? item.pg_info.map((pg, i) => ({
                              key: `${index}-${i}`,
                              nodegroup_name: item.nodegroup_name || `UnnamedGroup-${index}`,
                              ...pg
                          }))
                        : [];
                    return {
                        key: index,
                        nodegroup_name: item.nodegroup_name || `UnnamedGroup-${index}`,
                        nodegroup_id: matchedGroup?.id ?? null,
                        children
                    };
                });
                setData(transformedData);
            } else {
                setData([]);
            }
        } catch (error) {
            setData([]);
        } finally {
            setLoading(false);
        }
    };

    const fetchNodeGroupList = async () => {
        try {
            const response = await fetchGroupList(vl2.az_id);
            setNodeGroupOptions(response.data || []);
            return response.data;
        } catch (error) {
            message.error("Failed to fetch node group list.");
            return [];
        }
    };

    const dissociateAssociateCallback = record => {
        if (record.children && Array.isArray(record.children)) {
            const disconnectedChildren = record.children.filter(child => child.status === "Disconnected");
            const notDisconnectedChildren = record.children.filter(child => child.status !== "Disconnected");

            if (disconnectedChildren.length === 0) {
                message.warning(
                    "All node devices in the node group are connected to a logical switch through VL2 and thus can't be dissociated. "
                );
                return;
            }

            Promise.all(
                disconnectedChildren.map(child =>
                    dissociateAssociate(vl2.id, null, child.pg_id).then(response => {
                        if (response.status === 200) {
                            message.success("All node devices in the node group are dissociated successfully.");
                        } else {
                            message.error(`Failed to dissociate ${child.host_name}: ${response.info}`);
                        }
                    })
                )
            ).then(() => {
                fetchData();
            });

            if (notDisconnectedChildren.length > 0) {
                const names = notDisconnectedChildren.map(child => child.host_name).join(", ");
                message.warning(
                    `Some node devices (${names}) in the node group are connected to a logical switch through VL2 and thus can't be dissociated. The other node devices in the node group are dissociated successfully. `
                );
            }
        } else {
            if (record.status !== "Disconnected") {
                message.warning(
                    "The device is connected to a logical switch through VL2 and thus can't be dissociated. Please disconnect the device with the logical switch first."
                );
                return;
            }

            dissociateAssociate(vl2.id, record.nodegroup_id || null, record.pg_id || null).then(response => {
                if (response.status === 200) {
                    fetchData();
                    message.success("The device is dissociated successfully.");
                } else {
                    message.error(response.info);
                }
            });
        }
    };

    useEffect(() => {
        setCurrentPage(1);
    }, [searchKeyword]);

    const networkAccessColumns = [
        createColumnConfigMultipleParams({
            title: " ",
            dataIndex: " ",
            filterDropdownComponent: TableFilterDropdown,
            enableFilter: false,
            enableSorter: false,
            width: "1%"
        }),
        {
            ...createColumnConfigMultipleParams({
                title: "ID",
                dataIndex: "pg_id",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "8%",
            sorter: (a, b) => String(a.pg_id).localeCompare(String(b.pg_id)),
            render: (_, record) => (record.children ? null : record.pg_id)
        },
        {
            ...createColumnConfigMultipleParams({
                title: "Node Group",
                dataIndex: "nodegroup_name",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "16%",
            sorter: (a, b) => a.nodegroup_name?.localeCompare(b.nodegroup_name),
            render: (_, record) => (record.children ? record.nodegroup_name : null)
        },
        {
            ...createColumnConfigMultipleParams({
                title: "Sysname",
                dataIndex: "host_name",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "16%",
            sorter: (a, b) => a.host_name?.localeCompare(b.host_name),
            render: (_, record) => (record.children ? null : record.host_name)
        },
        {
            ...createColumnConfigMultipleParams({
                title: "Attachment Type",
                dataIndex: "link_type",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "16%",
            sorter: (a, b) => a.link_type?.localeCompare(b.link_type),
            render: (_, record) => (record.children ? null : record.link_type)
        },
        {
            ...createColumnConfigMultipleParams({
                title: "NIC Port Group",
                dataIndex: "pg_name",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "16%",
            sorter: (a, b) => a.pg_name?.localeCompare(b.pg_name),
            render: (_, record) => (record.children ? null : record.pg_name)
        },
        {
            ...createColumnConfigMultipleParams({
                title: "Switch Port Group",
                dataIndex: "",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "16%",
            sorter: (a, b) => a.switch_pg_name?.localeCompare(b.switch_pg_name),
            render: (_, record) => (record.children ? null : record.switch_pg_name)
        },
        {
            ...createColumnConfigMultipleParams({
                title: "Auto-Connect Mode",
                dataIndex: "connect_mode",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "16%",
            sorter: (a, b) => a.connect_mode?.localeCompare(b.connect_mode),
            render: (_, record) => (record.children ? null : record.connect_mode)
        },
        {
            title: "Operation",
            dataIndex: "operation",
            width: "14%",
            render: (_, record) => {
                const isParent = Array.isArray(record.children);
                return (
                    <Space size="large" className="actionLink">
                        {isParent ? (
                            <a
                                style={{marginRight: "48px"}}
                                onClick={() =>
                                    confirmModalAction("Are you sure you want to dissociate the devices?", () => {
                                        dissociateAssociateCallback(record);
                                    })
                                }
                            >
                                Dissociate
                            </a>
                        ) : (
                            <>
                                <a
                                    onClick={() =>
                                        confirmModalAction("Are you sure you want to dissociate the devices?", () => {
                                            dissociateAssociateCallback(record);
                                        })
                                    }
                                >
                                    Dissociate
                                </a>
                                <a
                                    onClick={() => {
                                        ConnectDetailModalRef.current.showConnectDetailModal(
                                            vl2.id,
                                            record.nodegroup_name,
                                            record.pg_id
                                        );
                                    }}
                                >
                                    Detail
                                </a>
                            </>
                        )}
                    </Space>
                );
            }
        }
    ];

    return (
        <Card style={{display: "flex", flex: 1}}>
            <AssociateDeviceModal
                ref={AssociateDeviceModalRef}
                saveCallback={() => {
                    fetchData();
                }}
            />
            <ConnectDetailModal ref={ConnectDetailModalRef} />
            <Flex justify="space-between" align="center" style={{marginBottom: 20, marginTop: 16}}>
                <Button
                    type="primary"
                    onClick={() => {
                        AssociateDeviceModalRef.current.showAssociateDeviceModal(vl2.id, nodeGroupOptions);
                    }}
                >
                    <Icon component={addSvg} />
                    Associate Device
                </Button>

                <Input
                    placeholder="Search Sysname / Attachment Type / NIC Port / Leaf Info"
                    allowClear
                    onSearch={value => setSearchKeyword(value)}
                    onChange={e => setSearchKeyword(e.target.value)}
                    style={{width: 280}}
                    prefix={<Icon component={searchSvg} />}
                />
            </Flex>

            <Table
                columns={networkAccessColumns}
                loading={loading}
                ref={tableRef}
                dataSource={filteredData}
                bordered
                expandable={{
                    expandIcon: props => <ExpandIcon {...props} isTable={false} />,
                    rowExpandable: record => !!record.children,
                    expandedRowKeys,
                    onExpand: (expanded, record) => {
                        if (expanded) {
                            setExpandedRowKeys(prev => [...prev, record.key]);
                        } else {
                            setExpandedRowKeys(prev => prev.filter(k => k !== record.key));
                        }
                    }
                }}
                pagination={{
                    current: currentPage,
                    pageSize,
                    showSizeChanger: true,
                    pageSizeOptions: ["10", "20", "50", "100"],
                    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
                    total: filteredData.length,
                    onChange: (page, size) => {
                        setCurrentPage(page);
                        setPageSize(size);
                    }
                }}
            />
        </Card>
    );
};

export default CreateAssociateDevice;
