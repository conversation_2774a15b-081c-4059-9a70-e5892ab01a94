import {forwardRef, useImperativeHandle, useState, useRef, useEffect, useMemo} from "react";
import {<PERSON><PERSON>, Divider, Flex, Table, Modal, Input} from "antd";
import {createColumnConfigMultipleParams, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {fetchNetworkIAccessDeviceInfo} from "@/modules-ampcon/apis/dc_virtual_resource_api";
import {searchSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";

const ConnectDetailModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showConnectDetailModal: (vl2ID, targetNodeGroupName, targetPgId) => {
            setIsShowModal(true);
            setVl2ID(vl2ID);
            setTargetNodeGroupName(targetNodeGroupName);
            setTargetPgId(targetPgId);
        },
        hideConnectDetailModal: () => {
            setIsShowModal(false);
        }
    }));
    const [isShowModal, setIsShowModal] = useState(false);
    const [loading, setLoading] = useState(false);
    const [vl2ID, setVl2ID] = useState(null);
    const [targetNodeGroupName, setTargetNodeGroupName] = useState(null);
    const [targetPgId, setTargetPgId] = useState(null);
    const [data, setData] = useState([]);
    const tableModalRef = useRef(null);
    const [searchKeyword, setSearchKeyword] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);

    const filteredData = useMemo(() => {
        if (!searchKeyword) return data;
        return data.filter(item =>
            [item.host_name, item.link_type, item.nic_port, item.switch_info].some(field =>
                field?.toLowerCase().includes(searchKeyword.toLowerCase())
            )
        );
    }, [searchKeyword, data]);

    const fetchTableData = async () => {
        try {
            setLoading(true);
            const res = await fetchNetworkIAccessDeviceInfo(vl2ID);
            const result = [];

            if (!res?.data) {
                setData([]);
                return;
            }

            for (const group of res.data) {
                if (group.nodegroup_name === targetNodeGroupName) {
                    for (const pg of group.pg_info) {
                        if (pg.pg_id === targetPgId) {
                            for (const detail of pg.connect_detail) {
                                result.push({
                                    pg_id: pg.pg_id,
                                    host_name: pg.host_name,
                                    link_type: pg.link_type,
                                    nic_port: detail.nic_port,
                                    switch_info: detail.switch_info
                                });
                            }
                            break;
                        }
                    }
                    break;
                }
            }

            setData(result);
        } catch (error) {
            console.error("Failed to fetch table data:", error);
            setData([]);
        } finally {
            setLoading(false);
        }
    };
    useEffect(() => {
        if (vl2ID && targetNodeGroupName && targetPgId !== undefined) {
            fetchTableData();
        }
    }, [vl2ID, targetNodeGroupName, targetPgId]);

    useEffect(() => {
        setCurrentPage(1);
    }, [searchKeyword]);

    const columns = [
        {
            ...createColumnConfigMultipleParams({
                title: "ID",
                dataIndex: "pg_id",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "10%",
            sorter: (a, b) => String(a.pg_id).localeCompare(String(b.pg_id))
        },
        {
            ...createColumnConfigMultipleParams({
                title: "Sysname",
                dataIndex: "host_name",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "20%",
            sorter: (a, b) => a.host_name.localeCompare(b.host_name)
        },
        {
            ...createColumnConfigMultipleParams({
                title: "Attachment Type",
                dataIndex: "link_type",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "20%",
            sorter: (a, b) => a.link_type.localeCompare(b.link_type)
        },
        {
            ...createColumnConfigMultipleParams({
                title: "NIC Port",
                dataIndex: "nic_port",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "20%",
            sorter: (a, b) => a.nic_port.localeCompare(b.nic_port)
        },
        {
            ...createColumnConfigMultipleParams({
                title: "Connected Leaf Info",
                dataIndex: "switch_info",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "30%",
            sorter: (a, b) => a.switch_info.localeCompare(b.switch_info)
        }
    ];

    return (
        <div>
            <Modal
                className="ampcon-max-modal"
                title={
                    <div>
                        Connect Detail
                        <Divider style={{margin: "16px -24px 16px -24px"}} />
                    </div>
                }
                open={isShowModal}
                onCancel={() => setIsShowModal(false)}
                footer={
                    <Flex vertical>
                        <Divider style={{margin: "16px -24px 16px -24px"}} />
                        <Flex justify="flex-end">
                            <Button
                                onClick={() => {
                                    setIsShowModal(false);
                                }}
                            >
                                Cancel
                            </Button>
                        </Flex>
                    </Flex>
                }
            >
                <Flex justify="flex-end" style={{marginBottom: 20, marginTop: -12}}>
                    <Input
                        placeholder="Search Sysname / Attachment Type / NIC Port / Leaf Info"
                        allowClear
                        onSearch={value => setSearchKeyword(value)}
                        onChange={e => setSearchKeyword(e.target.value)}
                        style={{width: 280}}
                        prefix={<Icon component={searchSvg} />}
                    />
                </Flex>

                <Table
                    ref={tableModalRef}
                    columns={columns}
                    bordered
                    dataSource={filteredData}
                    pagination={{
                        current: currentPage,
                        pageSize,
                        showSizeChanger: true,
                        pageSizeOptions: ["10", "20", "50", "100"],
                        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
                        total: filteredData.length,
                        onChange: (page, size) => {
                            setCurrentPage(page);
                            setPageSize(size);
                        }
                    }}
                />
            </Modal>
        </div>
    );
});
export default ConnectDetailModal;
