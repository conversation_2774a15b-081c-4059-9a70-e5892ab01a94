.successTag {
  color: #2BC174;
  background: rgba(43, 193, 116, 0.1);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #2BC174;
}

.failedTag {
  color: #F53F3F;
  background: rgba(245, 63, 63, 0.1);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #F53F3F;
}

.uncheckedTag {
  color: #929A9E;
  background: #F4F5F7;
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #DADCE1;
}

.runningTag {
  color: #FFBB00;
  background: rgba(255, 187, 0, 0.1);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #FFBB00
}

.pendingTag {
  color: #929A9E;
  background: #F4F5F7;
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #929A9E
}

.logicalTab {
  :global {
    .ant-tabs-tabpane {
      padding: 0px 0px !important;
      flex-direction: column !important;
    }

    .ant-tabs-nav-wrap {
      background: #FFFFFF;
    }


    .ant-tabs .ant-tabs-tab+.ant-tabs-tab {
      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
    }

    .ant-tabs-nav {
      min-height: 32px;
      background: #FFFFFF;

      .ant-tabs-tab:first-child {
        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
      }

      .ant-tabs-tab-active {
        background-color: rgba(20, 201, 187, 0.1) !important;
        border: 1px solid #14C9BB !important;
      }

      .ant-tabs-ink-bar {
        top: 0;
        display: none;
      }

      .ant-tabs-tab {
        border-radius: 0;
        padding: 8px 20px;
        font-weight: 400;
        font-size: 14px;
        background: #FFFFFF;
        border: 1px solid #ccc;
      }
    }
  }
}

.goBack {
  width: 70px;
  font-weight: 500;
  font-size: 14px;
  color: #14C9BB;
  text-align: left;
  font-style: normal;
  text-transform: none;
  cursor: pointer;
}