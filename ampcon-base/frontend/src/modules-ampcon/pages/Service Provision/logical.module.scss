.logical-network-details {
    #container {
        height: 90vh;
        display: flex;

        #stencil {
            width: 300px;
            height: 90vh;
            background-color: #fff;
            position: relative;
            border-right: 1px solid #dfe3e8;

            .x6-widget-stencil {
                background: #fff;
            }

            #graph-container {
                width: calc(100% - 180px);
                height: 100%;
            }

            .x6-widget-stencil {
                background-color: #fff;
            }

            .x6-widget-stencil-title {
                background-color: #fff;
            }

            .x6-widget-stencil-group-title {
                background-color: #fff !important;
            }

            .x6-widget-transform {
                margin: -1px 0 0 -1px;
                padding: 0px;
                border: 1px solid #239edd;
            }

            .x6-widget-transform>div {
                border: 1px solid #239edd;
            }

            .x6-widget-transform>div:hover {
                background-color: #3dafe4;
            }

            .x6-widget-transform-active-handle {
                background-color: #3dafe4;
            }

            .x6-widget-transform-resize {
                border-radius: 0;
            }

            .x6-widget-selection-inner {
                border: 1px solid #239edd;
            }

            .x6-widget-selection-box {
                opacity: 0;
            }
        }

        #graph-container {
            width: 100% !important;
            height: 100% !important;
        }
    }

    [class*="contextMenu"]{
        width: 300px;

        button{
            width: 100%;
        }

    }
}