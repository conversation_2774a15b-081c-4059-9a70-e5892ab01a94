import React, {useEffect, useState} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import {useSelector} from "react-redux";
import ProtectedRoute from "@/modules-ampcon/utils/util";
import ServerPage from "./server";
import BasicConfiguration from "./basic_configuration";

const allItems = [
    {
        key: "form_deployment",
        label: "Form Deployment",
        children: <ProtectedRoute component={BasicConfiguration} />
    },
    {
        key: "script_deployment",
        label: "Script Deployment",
        children: <ProtectedRoute component={ServerPage} />
    }
];
const RoceConfigManager = () => {
    const currentUser = useSelector(state => state.user.userInfo);

    const items = currentUser.type === "readonly" ? [] : allItems;

    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState(items[0].key);

    useEffect(() => {
        const match = location.pathname.match(/(form_deployment|script_deployment)$/);
        if (match) {
            setCurrentActiveKey(match[0]);
        } else {
            const basePath = location.pathname.endsWith("/") ? location.pathname : `${location.pathname}/`;
            navigate(`${basePath}${items[0].key}`, {replace: true});
        }
    }, [location.pathname, items, navigate]);

    const onChange = key => {
        const currentPath = location.pathname;
        const match = currentPath.match(/(form_deployment|script_deployment)$/);
        const parentPath = match
            ? currentPath.slice(0, -match[0].length).replace("nic_configurations", "NIC_configurations")
            : currentPath.replace("nic_configurations", "NIC_configurations");

        navigate(`${parentPath}${key}`);
    };

    return (
        <div style={{display: "flex", flex: 1}}>
            <Tabs
                style={{flex: 1}}
                activeKey={currentActiveKey}
                items={items}
                onChange={onChange}
                destroyInactiveTabPane
                tabBarStyle={{marginBottom: "8px"}}
            />
        </div>
    );
};

export default RoceConfigManager;
