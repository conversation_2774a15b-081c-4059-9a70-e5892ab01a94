.bracketStyles {
    color: black !important;
    font-size: 16px !important;
    line-height: 24px !important;  
    font-family: "Roboto Condensed" !important;
    display: inline-block;
    font-weight: 100;
    transform: translateY(2px);  // 微调垂直位置
}

//input styles
.customInput {
    border: none;
    outline: none;
    font-weight: bold;
    width: var(--input-width); /* 使用 CSS 变量来动态设置宽度 */
    text-align: center;
    background-color: transparent;
    //color: #14c9bb;
    padding: 0;
    margin: 0;
    font-family: monospace;
    vertical-align: middle;
    line-height: 24px;
    transform: translateY(1.5px);
}
//Footer styles
.fixedFooter {
    position: fixed !important;
    bottom: 0 !important;
    left: 200px !important;
    width: calc(100% - 200px) !important;
    padding: 10px 20px !important;
    background: #fff !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 20px;
    border-top: 1px solid #ddd !important;
    height: 50px !important;
    z-index: 9999 !important;
}

.tipIcon {
    width: 16px;
    height: 16px;
    background-color: #555;
    color: white; // 感叹号颜色
    border-radius: 50%; // 圆形
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px; // 感叹号大小
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: -20px;
}

.tipIconBroadcom {
    width: 14px;
    height: 14px;
    margin-left: 1px;
    margin-top: -5px;
    vertical-align: middle;
    cursor: pointer;
}

.mainCard {
    display: flex;
    height: 100%;
    border-radius: 0;
    flex-direction: column;
    border: none;
    gap: large;
    :global(.ant-card-body) {
        display: flex;
        flex-direction: column;
        height: 100%;
    }
}

.nicVendorsDiv {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 20px;
    gap: 20px;
}

.nicPortsDiv {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 20px;
    gap: 38px;
}

.mainDiv {
    display: flex;
    flex-direction: row;
    // border: 1px solid black;
    margin-top: 10px;
    gap: 24px;
    height: 100%;
    margin-left: 0px;
    margin-right: 0px;
    margin-top: 0px;
}

.leftCard {
    width: 25%;
    border-radius: 0;
    :global(.ant-card-body) {
        display: flex;
        flex-direction: column;
        height: 100%;
        margin-top: 5%;
        margin-right: 40px;
        width: 100%;
    }
}

.rightCard {
    width: 75%;
    height: 100%;
    border-radius: 0;
    margin: 0;
    :global(.ant-card-body) {
        height: 100%;
        padding: 30px;
        line-height: 1.3;
    }
}

.nicPortsTip {
    width: 14px;
    height: 14px;
    // position: absolute;
    right: -17px;
}

.treeSelectStyle {
    width: 100%;
    max-width: 280px;
    height: auto;
    font-size: 10px;
    flex: 1 1 120px;
    flex-grow: 1; 
}

.selectStyle {
    width: 100%;
    max-width: 280px;
    flex: 1 1 120px;
    flex-grow: 1;
}

.buttomCard {
    //width: 100%;
    margin-left: -24px;
    margin-right: -24px;
    margin-bottom: 0px;
    margin-top: 24px;
    border-radius: 0;
    border-left: none;  // ✅ 去掉左边框
    border-right: none; // ✅ 去掉右边框
    :global(.ant-card-body) {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        padding-right: 0;
        gap: 20px;
    }
}

.frombuttomCard {
    width: calc(100vw - 200px - 54px); ;
    position: fixed;
    bottom: 28px;
    margin-left: -24px;
    margin-right: -24px;
    margin-bottom: 0px;
    border-radius: 0;
    border-left: none;
    border-right: none;
    border-bottom: none; 

    :global(.ant-card-body) {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        padding-right: 0;
        gap: 20px;
    }
}

.treeSelectAndTip {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: flex-start;
    position: relative;
}

.divider {
    margin: 24px 24px 24px -24px;
    padding: 0;
    border: none;
    border-top: 1px solid #F2F2F2;
    width: calc(100vw - 200px - 50px); ;
}

.deploymentPage :global(.ant-card.ant-card-bordered) {
    height: calc(100% + 48px) !important;
    border: none !important;
    box-shadow: none !important;
}


/* 适配屏幕14寸笔记本 */
@media (max-width: 1450px) {
    .leftCard {
        width: 40%;  
        margin-right: 10px;  
    }
}

@media (max-width: 768px) {
    .mainDiv {
        flex-direction: column; 
    }
    .leftCard {
        width: 100%;
        margin-right: 0;
    }
}

.label {
  :global {
    .ant-form-item .ant-form-item-control-input {
      min-height: 16px;
    }

    .ant-form-item-label > label {
      height: 16px;
    }

    .ant-form-item:last-child {
      margin-bottom: 0;
    }
  }
}


