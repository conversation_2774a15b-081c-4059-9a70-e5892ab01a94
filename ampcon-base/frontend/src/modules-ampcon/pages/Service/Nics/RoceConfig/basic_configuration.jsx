import React, {useState, useEffect} from "react";
import {Card, Select, Checkbox, Button, TreeSelect, message, Tooltip, Form, Input, Radio, Divider, Space} from "antd";
import styles from "./server.module.scss";
import Icon, {QuestionCircleOutlined} from "@ant-design/icons";
import {fetchRoceNicPort} from "@/modules-ampcon/apis/monitor_api";
import {roceConfigurationByForm} from "@/modules-ampcon/apis/roce_api";
import {useForm} from "antd/es/form/Form";
import filterSvg from "../Monitoring/resource/filter.svg?react";
import shrinkSvg from "../Monitoring/resource/shrink.svg?react";
import unfoldSvg from "../Monitoring/resource/unfold.svg?react";
import shrinkHoverSvg from "../Monitoring/resource/shrink_hover.svg?react";
import unfoldHoverSvg from "../Monitoring/resource/unfold_hover.svg?react";

const BasicConfiguration = () => {
    const [form] = useForm();

    const [nicVendor, setNicVendor] = useState("nvidia");
    const [nicPort, setNicPort] = useState([]);
    const [treeData, setTreeData] = useState();

    const [hoveredIcons, setHoveredIcons] = useState({});

    // // nVidia 模式变量
    const rocev2 = Form.useWatch("rocev2", form) || "enable";
    const trustStatus = Form.useWatch("trustStatus", form) || "dscp";
    const algorithms = Form.useWatch([], form) || {};

    // Broadcom 模式变量
    const type = Form.useWatch("type", form) || "Niccli";
    const [selectMode, setSelectMode] = useState("");

    const handleNicVendorChange = value => {
        setNicVendor(value);
        setNicPort([]);
        form.setFieldsValue({nicports: []});
        if (value === "nvidia") {
            // 切换到 NVIDIA，清除 Broadcom 的所有字段
            form.resetFields([
                "type",
                "selectMode",
                "cnpDscp",
                "cnpPriority",
                "roceDscp",
                "rocePriority",
                ...Array.from({length: 3}, (_, i) => `broadcomTAS${i}`),
                ...Array.from({length: 3}, (_, i) => `broadcomTcbw${i}`),
                ...Array.from({length: 8}, (_, i) => `broadcomPriority2TC${i}`),
                "broadcomPFC"
            ]);
        } else if (value === "broadcom") {
            // 切换到 Broadcom，清除 NVIDIA 字段
            form.resetFields([
                "rocev2",
                "trustStatus",
                "ecn",
                "cnp_dscp",
                "cnp_802p_prio",
                ...Array.from({length: 8}, (_, i) => `nvidia_upToTCs${i}`),
                ...Array.from({length: 8}, (_, i) => `nvidiaAlgorithmForTC${i}`),
                ...Array.from({length: 8}, (_, i) => `nvidiaTcbw${i}`),
                "nvidiaPFC"
            ]);
        }
    };

    const handleNicPortChange = value => {
        setNicPort(value);
    };

    const handleCancel = () => {
        form.resetFields();
        setNicVendor("nvidia");
        setNicPort([]);
    };

    const onValuesChange = changedValues => {
        // 切换tsa模式后清空百分比
        const mappings = [{algPrefix: "nvidiaAlgorithmForTC", bwPrefix: "nvidiaTcbw"}];

        Object.keys(changedValues).forEach(key => {
            mappings.forEach(({algPrefix, bwPrefix}) => {
                const regex = new RegExp(`^${algPrefix}(\\d)$`);
                const match = key.match(regex);
                if (match) {
                    const index = match[1];
                    const algorithm = changedValues[key];
                    if (algorithm !== "ets") {
                        const fieldName = `${bwPrefix}${index}`;
                        form.setFieldsValue({[fieldName]: undefined});
                    }
                }
            });
        });
    };

    const handleMouseEnter = id => {
        setHoveredIcons(prev => ({...prev, [id]: true}));
    };

    const handleMouseLeave = id => {
        setHoveredIcons(prev => ({...prev, [id]: false}));
    };

    const getIconComponent = (expanded, isHovered) => {
        if (expanded) {
            return isHovered ? shrinkHoverSvg : shrinkSvg;
        }
        return isHovered ? unfoldHoverSvg : unfoldSvg;
    };

    const switcherIcon = ({expanded, id}) => {
        const IconComponent = getIconComponent(expanded, hoveredIcons[id]);

        return (
            <IconComponent
                style={{width: "16px", height: "16px", marginTop: "4px", marginRight: "8px", marginLeft: "8px"}}
                alt={expanded ? "shrink" : "unfold"}
                onMouseEnter={() => handleMouseEnter(id)}
                onMouseLeave={() => handleMouseLeave(id)}
            />
        );
    };

    const parseBandwidth = (values, prefix, algorithms, count) => {
        return Array.from({length: count}, (_, i) => {
            const algo = algorithms[i];
            let val = values[`${prefix}Tcbw${i}`];
            if (algo === "ets") {
                val = val === undefined || val === "" ? 0 : val;
                if (!/^\d+$/.test(val)) {
                    throw new Error(`TC ${i}: ETS bandwidth must be a number`);
                }
                return Number(val);
            }
            return 0;
        });
    };

    const extractPFC = selectedUPs => {
        const result = Array(8).fill(0);
        selectedUPs?.forEach(item => {
            const match = item.match(/\d+/);
            if (match) {
                const index = Number(match[0]);
                if (index >= 0 && index < 8) {
                    result[index] = 1;
                }
            }
        });
        return result;
    };

    const validateETSbw = (tc_bw, tc_algorithm) => {
        const etsIndices = tc_algorithm.map((a, i) => (a === "ets" ? i : null)).filter(i => i !== null);
        const total = etsIndices.reduce((sum, i) => sum + tc_bw[i], 0);
        const prefix = tc_bw.length === 8 ? "nvidiaTcbw" : "broadcomTcbw";
        if (etsIndices.length > 0 && total !== 100) {
            const errors = etsIndices.map(i => ({
                name: `${prefix}${i}`,
                errors: [""]
            }));
            form.setFields(errors);
            message.warning("The sum of BW must be 100%");
            return false;
        }
        return true;
    };

    const onSave = async () => {
        try {
            await form.validateFields();
            const values = form.getFieldsValue();
            let playBookValue = {};

            if (values.nicVendor === "nvidia") {
                if (values.rocev2 === "disable") {
                    playBookValue = {
                        nic_vendor: values.nicVendor,
                        nic_ports: values.nicports,
                        script_params: {
                            roce_v2: values.rocev2
                        }
                    };
                }
                if (values.rocev2 === "enable") {
                    const up_to_tc = Array.from({length: 8}, (_, i) => {
                        const val = values[`nvidia_upToTCs${i}`];
                        return /^\d+$/.test(val) ? Number(val) : i;
                    });

                    const tc_algorithm = Array.from({length: 8}, (_, i) => values[`nvidiaAlgorithmForTC${i}`]);

                    // Reset non-ETS bandwidth
                    tc_algorithm.forEach((algo, i) => {
                        if (algo !== "ets") form.setFieldValue(`nvidiaTcbw${i}`, 0);
                    });

                    const tc_bw = parseBandwidth(values, "nvidia", tc_algorithm, 8);
                    if (!validateETSbw(tc_bw, tc_algorithm)) return;

                    const pfc_enabled_tc = extractPFC(values.nvidiaPFC);

                    playBookValue = {
                        nic_vendor: values.nicVendor,
                        nic_ports: values.nicports,
                        script_params: {
                            roce_v2: values.rocev2,
                            trust_mode: values.trustStatus,
                            up_to_tc,
                            tc_algorithm,
                            tc_bw,
                            pfc_enabled_tc,
                            ecn: values.ecn,
                            ...(values.trustStatus === "dscp" && {cnp_dscp: Number(values.cnp_dscp)}),
                            ...(values.trustStatus === "pcp" && {cnp_802p_prio: Number(values.cnp_802p_prio)})
                        }
                    };
                }
            } else if (values.nicVendor === "broadcom") {
                if (values.type === "Niccli") {
                    const tc_algorithm = Array.from({length: 3}, (_, i) => values[`broadcomTAS${i}`]);

                    const tc_bw = [0, 1, 2].map(i => {
                        const val = form.getFieldValue(`broadcomTcbw${i}`);
                        return Number(val || 0);
                    });

                    const total = tc_bw.reduce((sum, bw) => sum + bw, 0);

                    if (total !== 100) {
                        const errors = tc_bw.map((_, i) => ({
                            name: `broadcomTcbw${i}`,
                            errors: [""]
                        }));
                        form.setFields(errors);
                        message.warning("The sum of BW must be 100%");
                        return false;
                    }

                    const cleared = tc_bw.map((_, i) => ({
                        name: [`broadcomTcbw${i}`],
                        errors: []
                    }));
                    form.setFields(cleared);

                    const defaultPriority2TC = [0, 0, 0, 1, 0, 0, 0, 2];
                    const priority2TC = Array.from({length: 8}, (_, i) => {
                        const val = values[`broadcomPriority2TC${i}`];
                        return val === undefined || val === "" ? defaultPriority2TC[i] : Number(val);
                    });

                    const pfc_enabled_tc = values.broadcomPFC?.map(v => Number(v.replace("broadcomPFC", ""))) || [];

                    playBookValue = {
                        nic_vendor: values.nicVendor,
                        nic_ports: values.nicports,
                        script_params: {
                            type: values.type,
                            tc_algorithm,
                            tc_bw,
                            pfc_enabled_tc,
                            priority2TC
                        }
                    };
                }
                if (values.type === "Bnxtsetupcc.sh") {
                    const modeMap = {
                        pfc_only: 1,
                        cc_only: 2,
                        pfc_cc: 3
                    };
                    const toolMAp = {
                        lldptool: 2,
                        niccli: 3
                    };
                    playBookValue = {
                        nic_vendor: values.nicVendor,
                        nic_ports: values.nicports,
                        script_params: {
                            type: values.type,
                            select_mode: modeMap[values.selectMode],
                            tool_type: toolMAp[values.toolSelect],
                            cnp_dscp: Number(values.cnpDscp),
                            cnp_priority: Number(values.cnpPriority),
                            roce_dscp: Number(values.roceDscp),
                            roce_priority: Number(values.rocePriority)
                        }
                    };
                }
            }

            const response = await roceConfigurationByForm(playBookValue);
            if (response.status === 200) {
                message.success("Configuration submission successful!");
            }
            // console.log("playBookValue", playBookValue);
        } catch (error) {
            message.error(error.errorFields?.[0]?.errors?.[0] || error.message || "Validation failed");
        }
    };

    useEffect(() => {
        form.setFieldsValue({nicVendor: "nvidia"});
    }, []);

    useEffect(() => {
        const fectchPort = async () => {
            try {
                const response = await fetchRoceNicPort(nicVendor);
                const bankendData = response.data;
                const transformTreeData = data => {
                    return data.map(node => ({
                        title: node.nodename,
                        value: node.device_id,
                        children:
                            node.children?.map(port => ({
                                title: port.port_title,
                                value: `${node.device_id}-${port.port_value}`
                            })) || []
                    }));
                };
                const frontData = transformTreeData(bankendData);
                setTreeData(frontData);
            } catch (error) {
                message.error({
                    content: `${error.message}`,
                    duration: 2
                });
            }
        };
        if (nicVendor) {
            fectchPort();
        }
    }, [nicVendor]);

    const upsToTcsTooltip = "Example: 0,0,0,0,1,1,1,1 Maps UPs 0-3 to TC0, and UPs 4-7 to TC1";
    const tcbwTooltip = "The sum of BW must be 100%";
    const broadcomTooltip =
        "For Broadcom NICs, only NICs with the chip model of BCM57608 can use AmpCon-DC to push RoCE configurations.";
    const defaultValues = [0, 0, 0, 1, 0, 0, 0, 2];

    return (
        <>
            <div className={styles.deploymentPage}>
                <Card style={{display: "flex", flex: 1, marginLeft: -24}}>
                    <h3 style={{margin: "-12px 0 20px"}}>Deployment</h3>

                    <Form
                        layout="horizontal"
                        labelAlign="left"
                        requiredMark={false}
                        labelCol={{span: 6}}
                        wrapperCol={{span: 17}}
                        // labelWrap
                        onValuesChange={onValuesChange}
                        form={form}
                        style={{width: "840px"}}
                        className={styles.label}
                        initialValues={{
                            nicVendor: "nvidia",
                            rocev2: "enable",
                            trustStatus: "dscp",
                            type: "Niccli",
                            toolSelect: "niccli",
                            selectMode: "pfc_only",
                            cnpPriority: 0,
                            rocePriority: 0,
                            broadcomTcbw0: "50",
                            broadcomTcbw1: "50",
                            broadcomTcbw2: "0",
                            nvidiaPFC: ["nvidiaPFC3"],
                            broadcomPFC: ["broadcomPFC3"],
                            ...Object.fromEntries(Array.from({length: 8}, (_, i) => [`broadcomTAS${i}`, "strict"])),
                            ...Object.fromEntries(
                                Array.from({length: 8}, (_, i) => [`broadcomPriority2TC${i}`, defaultValues[i]])
                            ),
                            ...Object.fromEntries(Array.from({length: 8}, (_, i) => [`nvidia_upToTCs${i}`, [i]]))
                        }}
                    >
                        <Form.Item
                            label="NIC Vendors"
                            name="nicVendor"
                            required
                            style={{maxWidth: "100%"}}
                            tooltip={broadcomTooltip}
                        >
                            <Select className={styles.selectStyle} value={nicVendor} onChange={handleNicVendorChange}>
                                <Select.Option value="nvidia">Nvidia</Select.Option>
                                <Select.Option value="broadcom">Broadcom</Select.Option>
                            </Select>
                        </Form.Item>

                        <Form.Item
                            label="NIC Ports"
                            name="nicports"
                            required
                            style={{maxWidth: "100%"}}
                            rules={[{required: true, message: "Please select at least one NIC port"}]}
                        >
                            <TreeSelect
                                className={styles.treeSelectStyle}
                                treeData={treeData}
                                value={nicPort}
                                onChange={handleNicPortChange}
                                dropdownStyle={{minWidth: "180px"}}
                                treeCheckable
                                showSearch={false}
                                allowClear
                                switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
                                suffixIcon={<Icon component={filterSvg} />}
                                virtual={false}
                            />
                        </Form.Item>

                        <Divider className={styles.divider} />

                        <h3 style={{margin: "8px 0 20px"}}>RoCEv2 Configration</h3>

                        {nicVendor === "nvidia" && (
                            <>
                                <Form.Item name="rocev2" label="RoCEv2">
                                    <Radio.Group
                                        options={[
                                            {value: "enable", label: "Enable"},
                                            {value: "disable", label: "Disable"}
                                        ]}
                                        value={rocev2}
                                    />
                                </Form.Item>

                                {rocev2 === "enable" && (
                                    <>
                                        <Form.Item name="trustStatus" label="Trust Status">
                                            <Radio.Group
                                                options={[
                                                    {value: "pcp", label: "PCP"},
                                                    {value: "dscp", label: "DSCP"}
                                                ]}
                                                value={trustStatus}
                                            />
                                        </Form.Item>

                                        <Form.Item label="Maps UPs to TCs" tooltip={upsToTcsTooltip}>
                                            <Space.Compact
                                                style={{
                                                    display: "inline-flex",
                                                    flexDirection: "row",
                                                    flexWrap: "wrap",
                                                    alignContent: "center",
                                                    width: "100%",
                                                    height: 90
                                                }}
                                            >
                                                {Array.from({length: 8}, (_, i) => {
                                                    const fieldName = `nvidia_upToTCs${i}`;
                                                    return (
                                                        <Form.Item
                                                            noStyle
                                                            shouldUpdate={(prev, curr) =>
                                                                prev[fieldName] !== curr[fieldName]
                                                            }
                                                            key={fieldName}
                                                        >
                                                            {({getFieldValue, setFieldsValue}) => (
                                                                <Form.Item
                                                                    name={fieldName}
                                                                    validateTrigger="onBlur"
                                                                    rules={[
                                                                        {
                                                                            validator(_, value) {
                                                                                if (
                                                                                    value === undefined ||
                                                                                    value === ""
                                                                                ) {
                                                                                    return Promise.resolve();
                                                                                }
                                                                                if (!/^\d+$/.test(value)) {
                                                                                    return Promise.reject(
                                                                                        new Error(
                                                                                            "Must be an integer between 0 and 7"
                                                                                        )
                                                                                    );
                                                                                }
                                                                                const intVal = parseInt(value, 10);
                                                                                if (intVal < 0 || intVal > 7) {
                                                                                    return Promise.reject(
                                                                                        new Error(
                                                                                            "Must be an integer between 0 and 7"
                                                                                        )
                                                                                    );
                                                                                }
                                                                                return Promise.resolve();
                                                                            }
                                                                        }
                                                                    ]}
                                                                    style={{
                                                                        width: "calc(25% - 12px)",
                                                                        marginBottom: 24
                                                                    }}
                                                                >
                                                                    <div>
                                                                        <label
                                                                            htmlFor={fieldName}
                                                                            style={{
                                                                                width: 40,
                                                                                minWidth: 40,
                                                                                marginRight: 12,
                                                                                userSelect: "none",
                                                                                cursor: "default",
                                                                                fontWeight: 400,
                                                                                color: "#212519"
                                                                            }}
                                                                        >
                                                                            UP {i}
                                                                        </label>
                                                                        <Input
                                                                            style={{flex: 1, width: 48}}
                                                                            aria-label={`UP${i}`}
                                                                            placeholder={i}
                                                                            value={getFieldValue(fieldName)}
                                                                            onChange={e => {
                                                                                setFieldsValue({
                                                                                    [fieldName]: e.target.value
                                                                                });
                                                                            }}
                                                                        />
                                                                    </div>
                                                                </Form.Item>
                                                            )}
                                                        </Form.Item>
                                                    );
                                                })}
                                            </Space.Compact>
                                        </Form.Item>

                                        <Form.Item
                                            label={
                                                <span>
                                                    Transmission Algorithm for <br />
                                                    Each TC
                                                </span>
                                            }
                                        >
                                            {Array.from({length: 8}, (_, i) => (
                                                <Form.Item
                                                    key={`nvidiaAlgorithmForTC${i}`}
                                                    label={`TC ${i}`}
                                                    name={`nvidiaAlgorithmForTC${i}`}
                                                    initialValue="vendor"
                                                >
                                                    <Radio.Group>
                                                        <Radio value="vendor">Vendor</Radio>
                                                        <Radio value="strict">Strict</Radio>
                                                        <Radio value="ets">ETS</Radio>
                                                    </Radio.Group>
                                                </Form.Item>
                                            ))}
                                        </Form.Item>

                                        {/* Minimally Guaranteed BW */}
                                        <Form.Item
                                            label={
                                                <span>
                                                    <div>Minimally Guaranteed BW for</div>
                                                    <div>
                                                        ETS TCs
                                                        <Tooltip title={tcbwTooltip}>
                                                            <QuestionCircleOutlined
                                                                style={{color: "rgba(0, 0, 0, 0.45)", marginLeft: 5}}
                                                            />
                                                        </Tooltip>
                                                    </div>
                                                </span>
                                            }
                                        >
                                            <Space.Compact
                                                style={{
                                                    display: "inline-flex",
                                                    flexDirection: "row",
                                                    flexWrap: "wrap",
                                                    alignContent: "center",
                                                    height: "60px"
                                                }}
                                            >
                                                {Array.from({length: 8}, (_, i) => {
                                                    const isEts = algorithms?.[`nvidiaAlgorithmForTC${i}`] === "ets";
                                                    return (
                                                        <Form.Item
                                                            name={`nvidiaTcbw${i}`}
                                                            key={`nvidiaTcbw${i}`}
                                                            validateTrigger="onBlur"
                                                            rules={[
                                                                {
                                                                    validator(_, value) {
                                                                        if (!isEts) return Promise.resolve();
                                                                        if (value === undefined || value === "") {
                                                                            return Promise.resolve(); // 允许空，后面补默认值
                                                                        }
                                                                        if (!/^\d+$/.test(value)) {
                                                                            return Promise.reject(
                                                                                new Error("Must be a number")
                                                                            );
                                                                        }
                                                                        return Promise.resolve();
                                                                    }
                                                                }
                                                            ]}
                                                            style={{
                                                                width: "calc(25% - 12px)",
                                                                marginBottom: 24
                                                            }}
                                                        >
                                                            <div>
                                                                <label
                                                                    htmlFor={`nvidiaTcbw${i}`}
                                                                    style={{
                                                                        width: 40,
                                                                        minWidth: 40,
                                                                        marginRight: 12,
                                                                        userSelect: "none",
                                                                        cursor: "default",
                                                                        fontWeight: 400,
                                                                        color: "#212519"
                                                                    }}
                                                                >
                                                                    TC {i}
                                                                </label>
                                                                <Input
                                                                    disabled={!isEts}
                                                                    placeholder="0"
                                                                    style={{flex: 1, width: 60}}
                                                                    suffix="%"
                                                                    value={form.getFieldValue(`nvidiaTcbw${i}`)}
                                                                    onChange={e => {
                                                                        form.setFieldsValue({
                                                                            [`nvidiaTcbw${i}`]: e.target.value
                                                                        });
                                                                    }}
                                                                />
                                                            </div>
                                                        </Form.Item>
                                                    );
                                                })}
                                            </Space.Compact>
                                        </Form.Item>

                                        <Form.Item label="PFC" name="nvidiaPFC">
                                            <Checkbox.Group>
                                                {Array.from({length: 8}, (_, i) => (
                                                    <Checkbox
                                                        value={`nvidiaPFC${i}`}
                                                        key={`nvidiaPFC${i}`}
                                                    >{`PFC${i}`}</Checkbox>
                                                ))}
                                            </Checkbox.Group>
                                        </Form.Item>

                                        <Form.Item name="ecn" label="ECN" initialValue="enable">
                                            <Checkbox
                                                checked={form.getFieldValue("ecn") === "enable"}
                                                onChange={e =>
                                                    form.setFieldsValue({ecn: e.target.checked ? "enable" : "disable"})
                                                }
                                            >
                                                Enable
                                            </Checkbox>
                                        </Form.Item>

                                        {trustStatus === "dscp" && (
                                            <Form.Item
                                                name="cnp_dscp"
                                                label="CNP_dscp"
                                                validateTrigger="onBlur"
                                                rules={[
                                                    {required: true, message: "Please input value"},
                                                    {pattern: /^\d+$/, message: "Must be an integer"},
                                                    {
                                                        validator: (_, value) => {
                                                            if (value === undefined || value === "")
                                                                return Promise.resolve();
                                                            const num = Number(value);
                                                            return num >= 0 && num <= 63
                                                                ? Promise.resolve()
                                                                : Promise.reject(new Error("Range must be 0-63"));
                                                        }
                                                    }
                                                ]}
                                            >
                                                <Input style={{width: 280}} placeholder="Value Range (0-63)" />
                                            </Form.Item>
                                        )}

                                        {trustStatus === "pcp" && (
                                            <Form.Item
                                                name="cnp_802p_prio"
                                                label="CNP_802p_prio"
                                                validateTrigger="onBlur"
                                                rules={[
                                                    {required: true, message: "Please input value"},
                                                    {pattern: /^\d+$/, message: "Must be an integer"},
                                                    {
                                                        validator: (_, value) => {
                                                            if (value === undefined || value === "")
                                                                return Promise.resolve();
                                                            const num = Number(value);
                                                            return num >= 0 && num <= 7
                                                                ? Promise.resolve()
                                                                : Promise.reject(new Error("Range must be 0-7"));
                                                        }
                                                    }
                                                ]}
                                            >
                                                <Input style={{width: 280}} placeholder="Value Range (0-7)" />
                                            </Form.Item>
                                        )}
                                    </>
                                )}
                            </>
                        )}

                        {nicVendor === "broadcom" && (
                            <>
                                <Form.Item label="Type" name="type">
                                    <Radio.Group
                                        options={[
                                            {value: "Niccli", label: "NICCLI"},
                                            {value: "Bnxtsetupcc.sh", label: "Bnxtsetupcc.sh"}
                                        ]}
                                        value={type}
                                    />
                                </Form.Item>

                                {type === "Niccli" && (
                                    <>
                                        <Form.Item label="TSA" name="broadcomTAS">
                                            <div>
                                                {Array.from({length: 3}, (_, i) => (
                                                    <Form.Item
                                                        key={`broadcomTAS${i}`}
                                                        label={`TC ${i}`}
                                                        name={`broadcomTAS${i}`}
                                                        preserve={false}
                                                    >
                                                        <Radio.Group>
                                                            <Radio value="strict">Strict</Radio>
                                                            <Radio value="ets">ETS</Radio>
                                                        </Radio.Group>
                                                    </Form.Item>
                                                ))}
                                            </div>
                                        </Form.Item>

                                        <Form.Item label="Priority2TC" name="broadcomPriority2TC">
                                            <Space.Compact
                                                style={{
                                                    display: "inline-flex",
                                                    flexDirection: "row",
                                                    flexWrap: "wrap",
                                                    alignContent: "center",
                                                    height: "100px"
                                                }}
                                            >
                                                {Array.from({length: 8}, (_, i) => {
                                                    const defaultValues = [0, 0, 0, 1, 0, 0, 0, 2];
                                                    const fieldName = `broadcomPriority2TC${i}`;
                                                    return (
                                                        <Form.Item
                                                            noStyle
                                                            shouldUpdate={(prev, curr) =>
                                                                prev[fieldName] !== curr[fieldName]
                                                            }
                                                            key={fieldName}
                                                        >
                                                            {({getFieldValue, setFieldsValue}) => {
                                                                return (
                                                                    <Form.Item
                                                                        name={fieldName}
                                                                        validateTrigger="onBlur"
                                                                        preserve={false}
                                                                        rules={[
                                                                            {
                                                                                validator(_, value) {
                                                                                    if (
                                                                                        value === undefined ||
                                                                                        value === ""
                                                                                    )
                                                                                        return Promise.resolve();
                                                                                    if (!/^\d+$/.test(value))
                                                                                        return Promise.reject(
                                                                                            new Error(
                                                                                                "Must be an integer between 0 and 2"
                                                                                            )
                                                                                        );
                                                                                    const intVal = parseInt(value, 10);
                                                                                    if (intVal < 0 || intVal > 2)
                                                                                        return Promise.reject(
                                                                                            new Error(
                                                                                                "Must be an integer between 0 and 2"
                                                                                            )
                                                                                        );
                                                                                    return Promise.resolve();
                                                                                }
                                                                            }
                                                                        ]}
                                                                        style={{
                                                                            width: "calc(25% - 12px)",
                                                                            marginBottom: 24
                                                                        }}
                                                                    >
                                                                        <div>
                                                                            <label
                                                                                htmlFor={fieldName}
                                                                                style={{
                                                                                    width: 40,
                                                                                    minWidth: 40,
                                                                                    marginRight: 12,
                                                                                    userSelect: "none",
                                                                                    cursor: "default",
                                                                                    fontWeight: 400,
                                                                                    color: "#212519"
                                                                                }}
                                                                            >
                                                                                Prio {i}
                                                                            </label>
                                                                            <Input
                                                                                style={{flex: 1, width: 60}}
                                                                                aria-label={`Prio${i}`}
                                                                                value={getFieldValue(fieldName)}
                                                                                placeholder={defaultValues[i]}
                                                                                onChange={e => {
                                                                                    const val = e.target.value;
                                                                                    setFieldsValue({[fieldName]: val});
                                                                                }}
                                                                            />
                                                                        </div>
                                                                    </Form.Item>
                                                                );
                                                            }}
                                                        </Form.Item>
                                                    );
                                                })}
                                            </Space.Compact>
                                        </Form.Item>

                                        <Form.Item name="broadcomTcbw" label="TCBW" tooltip={tcbwTooltip}>
                                            <Space.Compact
                                                style={{
                                                    display: "flex",
                                                    flexDirection: "row",
                                                    flexWrap: "nowrap",
                                                    alignContent: "center",
                                                    justifyContent: "flex-start",
                                                    height: 30
                                                }}
                                            >
                                                {Array.from({length: 3}, (_, i) => {
                                                    const fieldName = `broadcomTcbw${i}`;
                                                    return (
                                                        <Form.Item
                                                            shouldUpdate={(prev, curr) =>
                                                                prev[fieldName] !== curr[fieldName]
                                                            }
                                                            key={fieldName}
                                                            name={fieldName}
                                                            // validateTrigger="onBlur"
                                                            preserve={false}
                                                            // rules={[
                                                            //     {
                                                            //         validator(_, value) {
                                                            //             if (!/^\d+$/.test(value)) {
                                                            //                 return Promise.reject(
                                                            //                     new Error("Must be a number")
                                                            //                 );
                                                            //             }
                                                            //             return Promise.resolve();
                                                            //         }
                                                            //     }
                                                            // ]}
                                                            style={{
                                                                width: "calc(25% - 12px)",
                                                                marginBottom: 0
                                                            }}
                                                        >
                                                            <div>
                                                                <label
                                                                    htmlFor={fieldName}
                                                                    style={{
                                                                        width: 40,
                                                                        minWidth: 40,
                                                                        marginRight: 21,
                                                                        userSelect: "none",
                                                                        cursor: "default",
                                                                        fontWeight: 400,
                                                                        color: "#212519"
                                                                    }}
                                                                >
                                                                    TC {i}
                                                                </label>
                                                                <Input
                                                                    style={{flex: 1, width: 60}}
                                                                    suffix="%"
                                                                    placeholder="0"
                                                                    value={form.getFieldValue(fieldName)}
                                                                    onChange={e => {
                                                                        form.setFieldsValue({
                                                                            [fieldName]: e.target.value
                                                                        });
                                                                    }}
                                                                />
                                                            </div>
                                                        </Form.Item>
                                                    );
                                                })}
                                            </Space.Compact>
                                        </Form.Item>

                                        <Form.Item
                                            label="PFC"
                                            name="broadcomPFC"
                                            preserve={false}
                                            rules={[
                                                {
                                                    validator: async (_, value) => {
                                                        if (!value || value.length === 0) {
                                                            return Promise.resolve();
                                                        }

                                                        const defaultTC = [0, 0, 0, 1, 0, 0, 0, 2];

                                                        const priority2TC = form.getFieldsValue(
                                                            Array.from({length: 8}, (_, i) => `broadcomPriority2TC${i}`)
                                                        );

                                                        const tcList = Array.from({length: 8}, (_, i) => {
                                                            const raw = priority2TC[`broadcomPriority2TC${i}`];
                                                            const parsed = parseInt(raw, 10);
                                                            return isNaN(parsed) ? defaultTC[i] : parsed;
                                                        });

                                                        const selectedIndexes = value.map(v =>
                                                            parseInt(v.replace("broadcomPFC", ""), 10)
                                                        );

                                                        const selectedTCs = new Set(
                                                            selectedIndexes.map(i => tcList[i])
                                                        );

                                                        if (selectedTCs.size > 1) {
                                                            return Promise.reject(
                                                                new Error(
                                                                    "Hardware doesn't support more than 1 lossless queue to configure PFC."
                                                                )
                                                            );
                                                        }

                                                        return Promise.resolve();
                                                    }
                                                }
                                            ]}
                                        >
                                            <Checkbox.Group>
                                                {Array.from({length: 8}, (_, i) => (
                                                    <Checkbox value={`broadcomPFC${i}`} key={`broadcomPFC${i}`}>
                                                        {`PFC${i}`}
                                                    </Checkbox>
                                                ))}
                                            </Checkbox.Group>
                                        </Form.Item>
                                    </>
                                )}

                                {type === "Bnxtsetupcc.sh" && (
                                    <>
                                        <Form.Item name="selectMode" label="Select Mode">
                                            <Radio.Group
                                                options={[
                                                    {label: "PFC only", value: "pfc_only"},
                                                    {label: "CC only", value: "cc_only"},
                                                    {label: "PFC+CC mode", value: "pfc_cc"}
                                                ]}
                                                onChange={e => setSelectMode(e.target.value)}
                                            />
                                        </Form.Item>

                                        <Form.Item label="Tool Type" name="toolSelect" required hidden>
                                            <Select placeholder="Please select" style={{width: 280}} disabled>
                                                {/* <Select.Option value="lldptool">Lldptool</Select.Option> */}
                                                <Select.Option value="niccli">Niccli Utility</Select.Option>
                                            </Select>
                                        </Form.Item>

                                        <Form.Item
                                            label="CNP DSCP"
                                            preserve={false}
                                            name="cnpDscp"
                                            required
                                            rules={[
                                                {required: true, message: "Please input value"},
                                                {pattern: /^\d+$/, message: "Must be an integer"},
                                                {
                                                    validator: (_, value) => {
                                                        if (value === undefined || value === "")
                                                            return Promise.resolve();
                                                        const num = Number(value);
                                                        return num >= 0 && num <= 63
                                                            ? Promise.resolve()
                                                            : Promise.reject(new Error("Range must be 0-63"));
                                                    }
                                                }
                                            ]}
                                        >
                                            <Input placeholder="Value Range (0-63)" style={{width: 280}} />
                                        </Form.Item>

                                        <Form.Item label="CNP packet priority" name="cnpPriority" initialValue={1}>
                                            <Radio.Group>
                                                {Array.from({length: 8}, (_, i) => (
                                                    <Radio value={i} key={`cnp-${i}`}>
                                                        {i}
                                                    </Radio>
                                                ))}
                                            </Radio.Group>
                                        </Form.Item>

                                        <Form.Item
                                            label="RoCE DSCP"
                                            name="roceDscp"
                                            preserve={false}
                                            required
                                            rules={[
                                                {required: true, message: "Please input value"},
                                                {pattern: /^\d+$/, message: "Must be a integer"},
                                                {
                                                    validator: (_, value) => {
                                                        if (value === undefined || value === "")
                                                            return Promise.resolve();
                                                        const num = Number(value);
                                                        return num >= 0 && num <= 63
                                                            ? Promise.resolve()
                                                            : Promise.reject(new Error("Range must be 0-63"));
                                                    }
                                                }
                                            ]}
                                        >
                                            <Input placeholder="Value Range (0-63)" style={{width: 280}} />
                                        </Form.Item>

                                        <Form.Item label="RoCE packet priority" name="rocePriority">
                                            <Radio.Group>
                                                {Array.from({length: 8}, (_, i) => (
                                                    <Radio value={i} key={`roce-${i}`}>
                                                        {i}
                                                    </Radio>
                                                ))}
                                            </Radio.Group>
                                        </Form.Item>
                                    </>
                                )}
                            </>
                        )}
                    </Form>
                </Card>
            </div>
            <Card className={styles.frombuttomCard}>
                <Button type="default" style={{width: "80px"}} onClick={handleCancel}>
                    Cancel
                </Button>
                <Button type="primary" style={{width: "80px"}} onClick={onSave}>
                    Apply
                </Button>
            </Card>
        </>
    );
};

export default BasicConfiguration;
