import {
    <PERSON><PERSON>,
    <PERSON>,
    Col,
    Divider,
    Flex,
    Form,
    Input,
    Row,
    Space,
    message,
    Table,
    Modal,
    Tabs,
    Select,
    Tooltip
} from "antd";
import {PlusOutlined, FileOutlined, FolderOutlined, MinusOutlined} from "@ant-design/icons";
import {useEffect, useRef, useState} from "react";
import {useForm} from "antd/es/form/Form";
import {AmpConCustomSearchTree} from "@/modules-ampcon/components/custom_tree";
import {
    AmpConCustomTable,
    AmpConCustomModalForm,
    createColumnConfig,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import DynamicConfigGen from "@/modules-ampcon/components/dynamic_config_gen";
import {
    getConfigsTree,
    addConfig,
    delGeneralConfig,
    getGeneralConfig,
    updateConfig,
    saveAsConfig,
    moveConfig
} from "@/modules-ampcon/apis/config_api";
import {
    fetchPushConfigTask,
    getConfigContent,
    readPushConfig,
    delPushConfig,
    fetchTaskList,
    getSwitchPushLog
} from "@/modules-ampcon/apis/monitor_api";
import {fetchSwitchWithPicosV} from "@/modules-ampcon/apis/lifecycle_api";
import {fetchPlaybookGroupInfo} from "@/modules-ampcon/apis/automation_api";
import {batchApplyConfig} from "@/modules-ampcon/apis/inventory_api";
import {
    queryAllSiteTemplates,
    querySiteConfigParams,
    queryGenerateMultipleConfig
} from "@/modules-ampcon/apis/template_api";
import Icon from "@ant-design/icons/lib/components/Icon";
import {
    plusAddSvg,
    deleteSvg,
    readSvg,
    generateEnableSvg,
    generateDisableSvg,
    offlineSvg,
    onlineSvg,
    exclamationSvg
} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const {TextArea} = Input;
const {Option} = Select;

const rowSelection = {
    selectedRowKeys: [],
    selectedRows: []
};

const switchMatchFieldsList = [
    {name: "sn", matchMode: "fuzzy"},
    {name: "mgt_ip", matchMode: "fuzzy"},
    {name: "platform_model", matchMode: "fuzzy"},
    {name: "address", matchMode: "fuzzy"},
    {name: "version", matchMode: "fuzzy"}
];

const switchSearchFieldsList = ["sn", "mgt_ip", "platform_model", "version"];

const switchColumns = [
    createColumnConfig("Switch SN", "sn", TableFilterDropdown),
    {
        ...createColumnConfig("IP address", "mgt_ip", TableFilterDropdown),
        render: (_, record) => {
            let iconComponent;
            if (record.reachable_status === 0) {
                iconComponent = onlineSvg;
            } else if (record.reachable_status === 1) {
                iconComponent = offlineSvg;
            } else {
                iconComponent = exclamationSvg;
            }

            return (
                <Space>
                    <Icon component={iconComponent} />
                    {record.mgt_ip}
                </Space>
            );
        }
    },
    {
        ...createColumnConfig("Model", "platform_model", TableFilterDropdown, "", "20%"),
        render: text => text || "--"
    },
    {
        ...createColumnConfig("Version", "version", TableFilterDropdown, "", "20%"),
        render: text => text || "--"
    },
    {
        ...createColumnConfig("Location", "address", TableFilterDropdown, "", "20%"),
        render: text => text || "--"
    }
];

const groupColumns = [
    createColumnConfig("Group Name", "group_name", TableFilterDropdown),
    {
        ...createColumnConfig("Group Description", "description", TableFilterDropdown, "", "50%"),
        render: text => text || "--"
    }
];
const groupSearchFieldsList = ["group_name", "description"];
const groupMatchFieldsList = [
    {name: "group_name", matchMode: "exact"},
    {name: "description", matchMode: "fuzzy"}
];

const transformToTreeData = treeData => {
    const nodesMap = {};
    const rootNodes = [];

    treeData.forEach(item => {
        nodesMap[item.id] = {...item, key: item.id, children: [], path: item.name};
    });

    treeData.forEach(item => {
        nodesMap[item.id].desc = nodesMap[item.id].title;
        nodesMap[item.id].title = nodesMap[item.id].name;
        nodesMap[item.id].icon = nodesMap[item.id].isParent || item.pid === -1 ? <FolderOutlined /> : <FileOutlined />;
        nodesMap[item.id].isLeaf = !(nodesMap[item.id].isParent || item.pid === -1);

        if (item.pid === -1) {
            rootNodes.push(nodesMap[item.id]);
        }
        if (nodesMap[item.pid]) {
            nodesMap[item.id].path = `${nodesMap[item.pid].path} ${item.name}`;
            nodesMap[item.pid].children.push(nodesMap[item.id]);
        }
    });

    return rootNodes;
};

const handleSearch = (selectedKeys, confirm) => {
    confirm();
};

export const TableSelectFilterDropdown = ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
    <div style={{padding: 8}}>
        <Select
            showSearch
            style={{width: 130, marginRight: 8}}
            onChange={value => {
                setSelectedKeys(value ? [value] : []);
                handleSearch(selectedKeys, confirm);
            }}
            // getPopupContainer={trigger => trigger.parentNode}
            options={[
                {
                    label: "Ready",
                    value: "0"
                },
                {
                    label: "Running",
                    value: "1"
                },
                {
                    label: "Finshed",
                    value: "2"
                }
            ]}
        />
        <Button
            onClick={() => {
                clearFilters();
                handleSearch(selectedKeys, confirm);
            }}
            type="primary"
            style={{width: 90}}
        >
            Reset
        </Button>
    </div>
);

const PushConfig = () => {
    const [treeData, setTreeData] = useState([]);
    const [treeNodes, setTreeNodes] = useState([]);
    const [selectedTreeNode, setSelectedTreeNode] = useState();
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [isSaveModalOpen, setIsSaveModalOpen] = useState(false);
    const [isPushTaskLogModalOpen, setIsPushTaskLogModalOpen] = useState(false);
    const [isChooseDeviceModalOpen, setIsChooseDeviceModalOpen] = useState(false);
    const [isGenerateModalOpen, setIsGenerateModalOpen] = useState(false);
    const [configform] = useForm();
    const [isEdit, setIsEdit] = useState(false);
    const [isRootNode, setIsRootNode] = useState(true);
    const [isSaved, setIsSaved] = useState(false);

    const onSelectTree = async (selectedKeys, info) => {
        const response = await getGeneralConfig(info.node.id);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            configform.setFieldsValue({
                name: info.node.name,
                create_time: response.data.create_time,
                config: response.data.config
            });
        }
        if (info.node.name === "configs") {
            setIsRootNode(true);
        } else {
            setIsRootNode(false);
        }
        setIsEdit(false);
        setSelectedTreeNode(info.node);
    };

    const onDragDrop = async info => {
        const response = await moveConfig(info.dragNode.name, info.node.id);
        // 判断response
        if (response.status !== 200) {
            message.error("Config move failed");
            return;
        }
        const newTree = treeData;
        // 被拖动node原来所在的pnode，children数--，并校验isParent是否需要修改
        const srcNodeIndex = newTree.findIndex(item => item.id === info.dragNode.pid);
        newTree[srcNodeIndex].count -= 1;
        newTree[srcNodeIndex].isParent = newTree[srcNodeIndex].count > 0;
        // 被拖动的dragNode，需要修改pid为目标node的pid
        const dragNodeIndex = newTree.findIndex(item => item.id === info.dragNode.id);
        newTree[dragNodeIndex].pid = info.node.id;
        // 目标node修改isParent为true
        const destnodeIndex = newTree.findIndex(item => item.id === info.node.id);
        newTree[destnodeIndex].count += 1;
        newTree[destnodeIndex].isParent = true;
        const nodes = transformToTreeData(newTree);
        setTreeData(newTree);
        setTreeNodes(nodes);
    };

    const addNode = async value => {
        if (selectedTreeNode) {
            const response = await addConfig(value.name, value.desc, selectedTreeNode.id, selectedTreeNode.level + 1);
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                message.success(response.info);
                addToNode(response.id, value.name, value.desc, selectedTreeNode.id, selectedTreeNode.level + 1);
            }
        } else {
            message.error("Please select a node first");
        }
    };

    const delNode = async () => {
        if (!selectedTreeNode) {
            message.error("Please select a node first");
            return;
        }
        if (selectedTreeNode.isParent) {
            message.error("Cannot delete a parent node");
            return;
        }
        const response = await delGeneralConfig(selectedTreeNode.name);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(`Node ${selectedTreeNode.name} delete successfully`);
            const newTree = treeData.filter(item => item.id !== selectedTreeNode.id);
            const pNodeIndex = newTree.findIndex(item => item.id === selectedTreeNode.pid);
            if (pNodeIndex !== -1) {
                newTree[pNodeIndex].count -= 1;
                newTree[pNodeIndex].isParent = newTree[pNodeIndex].count > 0;
            }
            const nodes = transformToTreeData(newTree);
            setTreeData(newTree);
            setTreeNodes(nodes);
            setSelectedTreeNode(null);
            configform.resetFields();
            setIsEdit(false);
            setIsSaved(false);
            setIsSaveModalOpen(false);
            const currentRoot = newTree.find(item => item.isRoot);
            setIsRootNode(!currentRoot);
            // 选择性地获取最新配置树
            fetchConfigsTree();
        }
    };

    const updateNode = async () => {
        const configvalues = configform.getFieldValue();
        const response = await updateConfig(configvalues.name, configvalues.config);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
        }
        setIsEdit(false);
    };

    const saveAsNode = async value => {
        const configvalues = configform.getFieldValue();
        const response = await saveAsConfig(value.name, value.desc, 0, 1, configvalues.config);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
            addToNode(response.id, value.name, value.desc, 0, 1);
        }
    };

    const addToNode = (id, name, desc, pid, level) => {
        const newTree = treeData;
        const newNode = {
            id,
            pid,
            name,
            title: desc,
            count: 0,
            isParent: false,
            level
        };
        const pNodeIndex = newTree.findIndex(item => item.id === pid);
        newTree[pNodeIndex].isParent = true;
        newTree.push(newNode);
        const nodes = transformToTreeData(newTree);
        setTreeData(newTree);
        setTreeNodes(nodes);
    };

    const fetchConfigsTree = async () => {
        const response = await getConfigsTree();
        if (response.status !== 200) {
            message.error("Get configs tree failed");
        } else {
            const nodes = transformToTreeData(response.data);
            setTreeData(response.data);
            setTreeNodes(nodes);
        }
    };

    const handlePushConfig = async (switchNodes, groupNodes) => {
        const configValues = configform.getFieldValue();
        // console.log("handlePushConfig", switchNodes, groupNodes, configValues.name, configValues.config);
        const response = await batchApplyConfig(configValues.config, configValues.name, switchNodes, groupNodes);
        if (response.status !== 200) {
            message.error("Push configs task create failed");
        } else {
            message.success(response.info);
        }
    };

    useEffect(() => {
        fetchConfigsTree().then();
    }, []);

    return (
        <div style={{display: "flex", flex: 1}}>
            <div style={{display: "flex", flex: 1}}>
                <Card style={{display: "flex", flex: 1, width: "15%", marginRight: "20px", marginBottom: "32px"}}>
                    <Space size="middle">
                        <Button
                            type="primary"
                            style={{marginTop: "12px"}}
                            icon={<Icon component={plusAddSvg} style={{height: 20}} />}
                            onClick={() => {
                                setIsAddModalOpen(true);
                            }}
                        >
                            Create
                        </Button>
                        <Button
                            style={{marginTop: "12px"}}
                            htmlType="button"
                            icon={<Icon component={deleteSvg} style={{height: 20}} />}
                            onClick={() => confirmModalAction("Are you sure want to delete?", () => delNode())}
                        >
                            Delete
                        </Button>
                    </Space>
                    <Divider orientation="left" />
                    <AmpConCustomSearchTree treeNodes={treeNodes} onSelect={onSelectTree} onDragDrop={onDragDrop} />
                </Card>
            </div>
            <div style={{width: "85%", minHeight: "100%", minWidth: "750px", marginBottom: "32px"}}>
                <Card style={{display: "flex", flex: 1, height: "calc(100% - 32px)"}}>
                    <Row justify="start">
                        <Space size="middle">
                            <Button
                                type="primary"
                                style={{marginTop: "12px"}}
                                icon={
                                    isRootNode ? (
                                        <Icon component={generateDisableSvg} style={{height: 20}} />
                                    ) : (
                                        <Icon component={generateEnableSvg} style={{height: 20}} />
                                    )
                                }
                                disabled={isRootNode}
                                onClick={() => {
                                    setIsGenerateModalOpen(true);
                                }}
                            >
                                Generate Config
                            </Button>
                            <Button
                                style={{marginTop: "12px"}}
                                htmlType="button"
                                icon={<Icon component={readSvg} style={{height: 20}} />}
                                onClick={() => {
                                    setIsPushTaskLogModalOpen(true);
                                }}
                            >
                                Push Config Logs
                            </Button>
                        </Space>
                    </Row>
                    <Divider />
                    <Form form={configform} labelAlign="left">
                        <Row>
                            <Col style={{marginRight: "90px"}}>
                                <Form.Item name="name" label="Name" labelCol={{flex: "90px"}}>
                                    <Input disabled style={{width: "280px"}} />
                                </Form.Item>
                            </Col>
                            <Col>
                                <Form.Item name="create_time" label="Create Time" labelCol={{flex: "90px"}}>
                                    <Input disabled style={{width: "280px"}} />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Form.Item name="config">
                            <TextArea
                                rows={26}
                                readOnly={!isEdit}
                                className={isEdit ? "custom-textarea editable" : "custom-textarea"}
                                style={{border: "none", background: "#F8FAFB", borderRadius: "4px"}}
                            />
                        </Form.Item>
                    </Form>
                    <Divider />
                    <Flex justify="flex-end">
                        <Space>
                            <Button
                                type="primary"
                                style={{width: "120px"}}
                                disabled={isRootNode || isEdit || isSaved || !selectedTreeNode}
                                onClick={() => {
                                    setIsEdit(true);
                                }}
                            >
                                Edit
                            </Button>
                            <Button type="primary" style={{width: "120px"}} disabled={!isEdit} onClick={updateNode}>
                                Save
                            </Button>
                            <Button
                                type="primary"
                                style={{width: "120px"}}
                                disabled={isRootNode}
                                onClick={() => {
                                    setIsSaveModalOpen(true);
                                }}
                            >
                                Save As
                            </Button>
                            <Button
                                type="primary"
                                style={{width: "120px"}}
                                disabled={isRootNode}
                                onClick={() => {
                                    setIsChooseDeviceModalOpen(true);
                                }}
                            >
                                Push Config
                            </Button>
                            <Button
                                style={{width: "120px"}}
                                htmlType="button"
                                disabled={isRootNode}
                                onClick={() => confirmModalAction("Are you sure want to delete?", () => delNode())}
                            >
                                Delete
                            </Button>
                        </Space>
                    </Flex>
                </Card>
            </div>
            <AddConfigModal
                title="Create Config"
                isModalOpen={isAddModalOpen}
                onCancel={() => {
                    TextArea;
                    setIsAddModalOpen(false);
                }}
                onSubmit={addNode}
            />
            <AddConfigModal
                title="Save As"
                isModalOpen={isSaveModalOpen}
                onCancel={() => {
                    setIsSaveModalOpen(false);
                }}
                onSubmit={saveAsNode}
            />
            <PushConfigTasksLogModal
                isModalOpen={isPushTaskLogModalOpen}
                onCancel={() => {
                    setIsPushTaskLogModalOpen(false);
                }}
            />
            <ChooseDeviceModal
                isModalOpen={isChooseDeviceModalOpen}
                onCancel={() => {
                    setIsChooseDeviceModalOpen(false);
                }}
                handleOk={handlePushConfig}
            />
            <GenerateConfigModal
                isModalOpen={isGenerateModalOpen}
                onCancel={() => {
                    setIsGenerateModalOpen(false);
                }}
                handleConfigContent={content => {
                    setIsEdit(true);
                    const configValues = configform.getFieldValue();
                    const newContent = `${configValues.config}\n${content}`;
                    configform.setFieldsValue({
                        config: newContent
                    });
                }}
            />
        </div>
    );
};

const AddConfigModal = ({title, isModalOpen, onCancel, onSubmit}) => {
    const [form] = useForm();

    const formItems = () => {
        return (
            <>
                <Form.Item
                    name="name"
                    label="Name"
                    rules={[
                        {required: true, message: "Please input your config name!"},
                        {max: 32, message: "Enter a maximum of 32 characters"},
                        {
                            validator: (_, value) => {
                                if (value === "All") {
                                    return Promise.reject(new Error("Please input a valid config name!"));
                                }
                                if (value.trim() !== value) {
                                    return Promise.reject(
                                        new Error("Config name should not have leading or trailing spaces.")
                                    );
                                }
                                if (value.includes(" ")) {
                                    return Promise.reject(new Error("Config name should not have internal spaces."));
                                }
                                if (!/^[\s\w:-]+$/.test(value)) {
                                    return Promise.reject(
                                        new Error(
                                            "Config name can only contain letters, numbers, underscores, hyphens and colons."
                                        )
                                    );
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                >
                    <Input placeholder="Config Name" style={{width: "280px"}} />
                </Form.Item>
                <Form.Item name="desc" label="Description">
                    <Input.TextArea placeholder="Description" style={{width: "280px"}} />
                </Form.Item>
            </>
        );
    };

    const onSubmitForm = async values => {
        onSubmit(values);
        onCancelModal();
    };

    const onCancelModal = () => {
        form.resetFields();
        onCancel();
    };

    return (
        <AmpConCustomModalForm
            title={title}
            isModalOpen={isModalOpen}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 6
                }
            }}
            CustomFormItems={formItems}
            onCancel={onCancelModal}
            onSubmit={onSubmitForm}
            modalClass="ampcon-middle-modal"
        />
    );
};

// TODO 已读标记？
const PushConfigTasksLogModal = ({isModalOpen, onCancel}) => {
    const tableRef = useRef(null);
    const [isPushTaskModalOpen, setIsPushTaskModalOpen] = useState(false);
    const [isTaskListModalOpen, setIsTaskListModalOpen] = useState(false);
    const [pushTaskContent, setPushTaskContent] = useState("");
    const [modalTitle, setModalTitle] = useState("");
    const [taskListData, setTaskListData] = useState([]);
    const [selectedTaskName, setSelectedTaskName] = useState("");

    const searchFieldsList = ["task_name", "create_user"];
    const matchFieldsList = [
        {name: "task_name", matchMode: "fuzzy"},
        {name: "create_user", matchMode: "fuzzy"},
        {name: "create_time", matchMode: "fuzzy"}
    ];

    const getTaskList = async record => {
        const response = await fetchTaskList(record.task_name);
        // console.log(response);
        if (response.status !== 200) {
            setTaskListData([]);
        } else {
            setTaskListData(response.data);
        }
        setIsTaskListModalOpen(true);
        setSelectedTaskName(record.task_name);
    };

    const markAsRead = async record => {
        const response = await readPushConfig(record.id);
        if (response.status !== 200) {
            message.error("Mark as read failed");
        } else {
            tableRef.current.refreshTable();
        }
    };

    const getContent = async record => {
        const response = await getConfigContent(record.task_name);
        if (response.status !== 200) {
            setPushTaskContent("");
        } else {
            setPushTaskContent(response.data);
        }
        setIsPushTaskModalOpen(true);
        setModalTitle("Config Content");
    };

    const delConfigTask = async record => {
        const response = await delPushConfig(record.task_name);
        if (response.status !== 200) {
            message.error("Delete Push Config Task failed");
        } else {
            tableRef.current.refreshTable();
        }
    };

    const showPushLog = async record => {
        const response = await getSwitchPushLog(selectedTaskName, record.sn);
        if (response.status !== 200) {
            setPushTaskContent("");
        } else {
            setPushTaskContent(response.data);
        }
        setIsPushTaskModalOpen(true);
        setModalTitle("Push Log");
    };

    const columns = [
        createColumnConfig("Task Name", "task_name", TableFilterDropdown),
        {
            ...createColumnConfig("Status", "task_status", TableSelectFilterDropdown),
            render: task_status => {
                if (task_status === 0) {
                    task_status = "Ready";
                } else if (task_status === 1) {
                    task_status = "Running";
                } else {
                    task_status = "Finished";
                }
                return task_status;
            }
        },
        createColumnConfig("Create User", "create_user", TableFilterDropdown),
        createColumnConfig("Create Time", "create_time", TableFilterDropdown),
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size={24} className="actionLink">
                        <a onClick={() => getTaskList(record)}>Task List</a>
                        <a onClick={() => getContent(record)}>Config Content</a>
                        <a
                            style={{
                                color: record.read_tag ? "#14C9BB" : "#B3BBC8",
                                cursor: record.read_tag ? "pointer" : "not-allowed"
                            }}
                            onClick={
                                record.read_tag
                                    ? () =>
                                          confirmModalAction("Are you sure want to delete?", () =>
                                              delConfigTask(record)
                                          )
                                    : null
                            }
                        >
                            Delete
                        </a>
                        <a
                            style={{
                                color: !record.read_tag ? "#14C9BB" : "#B3BBC8",
                                cursor: !record.read_tag ? "pointer" : "not-allowed"
                            }}
                            disabled={record.read_tag}
                            onClick={() => markAsRead(record)}
                        >
                            Mark As Read
                        </a>
                    </Space>
                );
            }
        }
    ];

    const taskListColumns = [
        {
            title: "SN",
            dataIndex: "sn"
        },
        {
            title: "Push Status",
            dataIndex: "status",
            render: status => {
                if (status === 0) {
                    status = "Not-Start";
                } else if (status === 2) {
                    status = "Success";
                } else if (status === 1) {
                    status = "Running";
                } else {
                    status = "Failed";
                }
                return status;
            }
        },
        {
            title: "End Time",
            dataIndex: "push_time"
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size={15} className="actionLink">
                        <a onClick={() => showPushLog(record)}>Push Log</a>
                    </Space>
                );
            }
        }
    ];

    return (
        <div>
            <Modal
                title={
                    <div>
                        Push Config Tasks Log
                        <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                open={isModalOpen}
                onCancel={onCancel}
                destroyOnClose
                footer={null}
                className="ampcon-max-modal"
            >
                <AmpConCustomTable
                    ref={tableRef}
                    extraButton={[]}
                    columns={columns}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    fetchAPIInfo={fetchPushConfigTask}
                    // rowClassName={record => {
                    //     if (record.read_tag === 0) {
                    //         return PushConfigStyle.tableUnreadedColor;
                    //     }
                    // }}
                />
                <Modal
                    title={
                        <div>
                            Task List
                            <Divider style={{marginTop: 8, marginBottom: 0}} />
                        </div>
                    }
                    open={isTaskListModalOpen}
                    onCancel={() => {
                        setIsTaskListModalOpen(false);
                    }}
                    footer={null}
                    className="ampcon-middle-modal"
                >
                    <Table rowKey={record => record.sn} bordered columns={taskListColumns} dataSource={taskListData} />
                </Modal>
                <PushConfigModalTemplate
                    title={modalTitle}
                    isModalOpen={isPushTaskModalOpen}
                    onCancel={() => {
                        setIsPushTaskModalOpen(false);
                    }}
                    content={pushTaskContent}
                />
            </Modal>
        </div>
    );
};

const ChooseDeviceModal = ({isModalOpen, onCancel, handleOk}) => {
    const switchRef = useRef(null);
    const groupRef = useRef(null);
    const items = [
        {
            key: "configSwitch",
            label: "Config Switch",
            children: (
                <AmpConCustomTable
                    ref={switchRef}
                    rowSelection={rowSelection}
                    columns={switchColumns}
                    searchFieldsList={switchSearchFieldsList}
                    matchFieldsList={switchMatchFieldsList}
                    fetchAPIInfo={fetchSwitchWithPicosV}
                />
            )
        },
        {
            key: "configGroup",
            label: "Config Group",
            children: (
                <AmpConCustomTable
                    ref={groupRef}
                    rowSelection={rowSelection}
                    columns={groupColumns}
                    searchFieldsList={groupSearchFieldsList}
                    matchFieldsList={groupMatchFieldsList}
                    fetchAPIInfo={fetchPlaybookGroupInfo}
                    fetchAPIParams={["switch"]}
                />
            )
        }
    ];

    const onHandleOk = () => {
        const switchNodes = switchRef.current ? switchRef.current.getSelectedRow().tableSelectedRows : null;
        const groupNodes = groupRef.current ? groupRef.current.getSelectedRow().tableSelectedRows : null;
        // console.log(switchNodes, groupNodes);
        if ((!switchNodes || switchNodes.length === 0) && (!groupNodes || groupNodes.length === 0)) {
            message.error("Please select switch or group");
            return;
        }
        handleOk(switchNodes, groupNodes);
        onCancel();
    };

    return (
        <Modal
            title={
                <div>
                    Choose Devices
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            onOk={onHandleOk}
            onCancel={onCancel}
            destroyOnClose
            className="ampcon-max-modal"
            footer={[
                <Divider style={{marginTop: 0, marginBottom: 20}} />,
                <Button key="cancel" onClick={onCancel}>
                    Cancel
                </Button>,
                <Button key="ok" type="primary" onClick={onHandleOk}>
                    Apply
                </Button>
            ]}
        >
            <Tabs className="radioGroupTabs" items={items} />
        </Modal>
    );
};

const PushConfigModalTemplate = ({title, isModalOpen, onCancel, content}) => {
    return (
        <Modal
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            onCancel={onCancel}
            footer={null}
            className="ampcon-middle-modal"
            zIndex={9999}
            destroyOnClose
        >
            <TextArea
                readOnly
                style={{resize: "none", backgroundColor: "#F8FAFB", borderRadius: "4px", border: "1px solid #FFFFFF"}}
                value={content}
                rows={20}
            />
        </Modal>
    );
};

const GenerateConfigModal = ({isModalOpen, onCancel, handleConfigContent}) => {
    const addSiteTemplateButtonTooltip = "Add site template";
    const deleteSiteTemplateButtonTooltip = "Delete site template";
    const goConfigNextButtonTooltip = "Click Next, Enter values for switch specific parameters.";

    const [form] = useForm(null);
    const [siteTemplateList, setSiteTemplateList] = useState([]);
    const [siteTemplateListIndex, setSiteTemplateListIndex] = useState(0);
    const [additionalSiteTemplateObject, setAdditionalSiteTemplateObject] = useState({});
    const [rootSiteTemplateObject, setRootSiteTemplateObject] = useState({
        valid: true
    });
    const [isNextButtonDisabled, setIsNextButtonDisabled] = useState(true);
    const [isShowDynamicForm, setIsShowDynamicForm] = useState(false);
    const [dynamicForm] = Form.useForm();
    const [formList, setFormList] = useState([]);

    const onHandleCancel = () => {
        onCancel();
        setSiteTemplateListIndex(0);
        setAdditionalSiteTemplateObject({});
        form.resetFields();
        setIsNextButtonDisabled(true);
        setIsShowDynamicForm(false);
        dynamicForm.resetFields();
    };

    const queryAllSiteTemplate = async () => {
        queryAllSiteTemplates().then(response => {
            setSiteTemplateList(response.data);
        });
    };

    // 动态删除指定的Select Site Template，更新form并校验
    const handleRemoveSiteTemplateCallback = (selectedItem, siteTemplateKey) => {
        const newAdditionalSiteTemplateObject = JSON.parse(JSON.stringify(additionalSiteTemplateObject));
        delete newAdditionalSiteTemplateObject[siteTemplateKey];
        setAdditionalSiteTemplateObject(newAdditionalSiteTemplateObject);
        const formValues = form.getFieldsValue();
        delete formValues[siteTemplateKey];
        form.setFieldsValue(formValues);
        siteTemplateValueCheck(formValues, newAdditionalSiteTemplateObject);
    };

    // 选择select项后，更细表单并进行校验
    const handleSiteTemplateChangeCallback = (selectedItem, siteTemplateKey) => {
        const formValues = form.getFieldsValue();
        formValues[siteTemplateKey] = selectedItem.children;
        form.setFieldsValue(formValues);
        const newAdditionalSiteTemplateObject = JSON.parse(JSON.stringify(additionalSiteTemplateObject));
        siteTemplateValueCheck(formValues, newAdditionalSiteTemplateObject);
    };

    // 校验所有的select,重复项的valid置为false, 用于校验信息提示
    const siteTemplateValueCheck = (formValues, newAdditionalSiteTemplateObject) => {
        const keysWithValuesMoreThanTwice = getKeysWithValuesMoreThanTwice(formValues);
        // set all keys to valid
        setRootSiteTemplateObject({valid: true});
        Object.keys(newAdditionalSiteTemplateObject).forEach(key => {
            newAdditionalSiteTemplateObject[key].valid = true;
        });
        keysWithValuesMoreThanTwice.forEach(key => {
            if (key === "siteTemplate0") {
                setRootSiteTemplateObject({valid: false});
            } else {
                newAdditionalSiteTemplateObject[key].valid = false;
            }
        });
        setAdditionalSiteTemplateObject(newAdditionalSiteTemplateObject);
        formOnChangeCallback(formValues);
    };

    // 检查select中重复选择的项
    const getKeysWithValuesMoreThanTwice = formValues => {
        const filteredFormValues = Object.keys(formValues)
            .filter(key => key.startsWith("siteTemplate"))
            .reduce((result, key) => {
                result[key] = formValues[key];
                return result;
            }, {});
        const valueCounts = Object.values(filteredFormValues).reduce((counts, value) => {
            counts[value] = (counts[value] || 0) + 1;
            return counts;
        }, {});
        const valuesMoreThanTwice = Object.keys(valueCounts).filter(value => valueCounts[value] >= 2);
        return Object.keys(formValues).filter(key => valuesMoreThanTwice.includes(formValues[key]));
    };

    // 校验是否有空项重复项，正常则可以进行下一步
    const formOnChangeCallback = formValues => {
        formValues = formValues || form.getFieldsValue();
        const emptyCount = Object.values(formValues).filter(value => Boolean(value) === false).length;
        if (emptyCount > 0) {
            setIsNextButtonDisabled(true);
            return;
        }
        const keysWithValuesMoreThanTwice = getKeysWithValuesMoreThanTwice(formValues);
        if (keysWithValuesMoreThanTwice.length > 0) {
            setIsNextButtonDisabled(true);
        } else {
            setIsNextButtonDisabled(false);
        }
    };

    // 获取模版参数，显示动态表单
    const handleNextButtonCallback = () => {
        const formValues = form.getFieldsValue();
        setIsShowDynamicForm(false);
        const siteTemplateValues = Object.keys(formValues)
            .filter(key => key.startsWith("siteTemplate"))
            .map(key => formValues[key]);
        querySiteConfigParams(siteTemplateValues).then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                const dataList = [];
                response.data.forEach(item => {
                    const parseFormJSON = (jsonData, dataList) => {
                        const temp = {};
                        const result = [];
                        Object.entries(jsonData).forEach(([key, value]) => {
                            value.name = key;
                            if (value.type.includes("_map_list")) {
                                value.key = key;
                                value.type = value.type.replace("_map_list", "");
                                value.isMapList = true;
                                value.isList = false;
                                value.mapListChildren = [];
                                parseFormJSON(value.children, value.mapListChildren);
                            } else if (value.type.includes("_list")) {
                                value.key = key;
                                value.type = value.type.replace("_list", "");
                                value.isMapList = false;
                                value.isList = true;
                            } else {
                                value.isList = false;
                                value.key = key;
                            }
                            result[value.index] = value;
                        });
                        temp.title = item.title;
                        temp.formData = result;
                        dataList.push(temp);
                    };
                    parseFormJSON(item.formJSON, dataList);
                    dynamicForm.resetFields();
                    setIsShowDynamicForm(true);
                });
                setFormList(dataList);
            }
        });
    };

    const onDynamicResetCallback = () => {
        dynamicForm.resetFields();
    };

    const onDynamicSaveCallback = async () => {
        try {
            // Perform all validations
            await dynamicForm.validateFields();
            // If no errors, proceed with saving the form
            queryGenerateMultipleConfig(dynamicForm.getFieldsValue()).then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    handleConfigContent(response.data);
                    onHandleCancel();
                }
            });
        } catch (errorInfo) {
            message.error("There are some errors in the form, please check and submit again!");
        }
    };

    useEffect(() => {
        queryAllSiteTemplate().then(() => {});
    }, []);

    return (
        <Modal
            title={
                <div>
                    Generate Config
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            // onOk={onHandleOk}
            onCancel={onHandleCancel}
            footer={
                isShowDynamicForm ? (
                    <>
                        <Divider style={{marginTop: 0, marginBottom: 20}} />
                        <Flex justify="flex-end">
                            <Button htmlType="button" style={{minWidth: "80px"}} onClick={onDynamicResetCallback}>
                                Reset
                            </Button>
                            <Button type="primary" onClick={onDynamicSaveCallback} style={{minWidth: "80px"}}>
                                Save
                            </Button>
                        </Flex>
                    </>
                ) : null
            }
            destroyOnClose
            // className="ampcon-middle-modal"
            className="ampcon-custom-modal-style"
        >
            <Form
                style={{minHeight: "278.23px"}}
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                wrapperCol={{span: 18}}
                form={form}
                onChange={() => {
                    formOnChangeCallback();
                }}
            >
                <Form.Item
                    name="siteTemplate0"
                    label="Select Site Template"
                    style={{marginTop: "-8px"}}
                    validateStatus={rootSiteTemplateObject.valid ? "success" : "error"}
                    help={rootSiteTemplateObject.valid ? "" : "The selected site template cannot be selected."}
                    validateTrigger={["onChange", "onBlur"]}
                >
                    <div>
                        <Select
                            style={{width: "280px"}}
                            placeholder="Please select a site template"
                            onChange={(value, selectedItem) => {
                                handleSiteTemplateChangeCallback(selectedItem, "siteTemplate0");
                            }}
                        >
                            {siteTemplateList.map(template => {
                                return (
                                    <Option key={template.id} value={template.name}>
                                        {template.name}
                                    </Option>
                                );
                            })}
                        </Select>

                        <Button
                            style={{
                                backgroundColor: "transparent",
                                color: "#BFBFBF"
                            }}
                            type="link"
                            onClick={() => {
                                // 动态添加Select Site Template
                                const newAdditionalSiteTemplateObject = JSON.parse(
                                    JSON.stringify(additionalSiteTemplateObject)
                                );
                                newAdditionalSiteTemplateObject[`siteTemplate${siteTemplateListIndex + 1}`] = {
                                    valid: true
                                };
                                setSiteTemplateListIndex(siteTemplateListIndex + 1);
                                setAdditionalSiteTemplateObject(newAdditionalSiteTemplateObject);
                                setIsNextButtonDisabled(true);
                            }}
                            icon={<PlusOutlined />}
                        />
                    </div>
                </Form.Item>
                {Object.entries(additionalSiteTemplateObject).map(([siteTemplateKey, siteTemplateValue]) => {
                    return (
                        <Form.Item
                            key={siteTemplateKey}
                            name={siteTemplateKey}
                            label="Select Site Template"
                            validateStatus={siteTemplateValue.valid ? "success" : "error"}
                            help={siteTemplateValue.valid ? "" : "The selected site template cannot be selected."}
                            validateTrigger={["onChange", "onBlur"]}
                        >
                            <div>
                                <Select
                                    style={{width: "280px"}}
                                    placeholder="Please select a site template"
                                    onChange={(value, selectedItem) => {
                                        handleSiteTemplateChangeCallback(selectedItem, siteTemplateKey);
                                    }}
                                >
                                    {siteTemplateList.map(template => {
                                        return (
                                            <Option key={template.id} value={template.id}>
                                                {template.name}
                                            </Option>
                                        );
                                    })}
                                </Select>

                                <Button
                                    style={{
                                        backgroundColor: "transparent",
                                        color: "#BFBFBF"
                                    }}
                                    type="link"
                                    onClick={(value, selectedItem) => {
                                        handleRemoveSiteTemplateCallback(selectedItem, siteTemplateKey);
                                    }}
                                    icon={<MinusOutlined />}
                                />
                            </div>
                        </Form.Item>
                    );
                })}
            </Form>
            <Divider style={{marginLeft: "-32px"}} />
            <Flex justify="flex-end">
                <Tooltip title={goConfigNextButtonTooltip} placement="topRight">
                    <Button
                        type="primary"
                        htmlType="submit"
                        // icon={<CheckCircleOutlined />}
                        style={{width: "80px", marginTop: -6}}
                        onClick={handleNextButtonCallback}
                        disabled={isNextButtonDisabled}
                    >
                        Next
                    </Button>
                </Tooltip>
            </Flex>
            {isShowDynamicForm ? (
                <Flex style={{flex: 1}}>
                    <Flex vertical gap="middle" style={{flex: 1}}>
                        <DynamicConfigGen
                            title="generate_config"
                            physicPorts={[]}
                            form={dynamicForm}
                            formList={formList}
                            setFormList={setFormList}
                            style={{flex: 1}}
                        />
                    </Flex>
                </Flex>
            ) : null}
        </Modal>
    );
};

export default PushConfig;
