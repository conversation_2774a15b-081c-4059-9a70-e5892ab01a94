import TextContentDiffComponent from "@/modules-ampcon/components/text_content_diff";
import {Col, Row, Form, Input, Select, Button, Space, Spin, message} from "antd";
import {useState} from "react";
import {
    AmpConCustomModalTable,
    createColumnConfig,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";

import {
    fetchSnapshotConfigAPI,
    fetchSnapshotListAPI,
    fetchRunningConfigAPI,
    fetchSwitchInfo
} from "@/modules-ampcon/apis/template_api";
import styles from "@/modules-ampcon/pages/System/user_management.module.scss";
import {convertConfigToStr} from "@/modules-ampcon/utils/util";
import {onlineSvg, offlineSvg, exclamationSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";

const ConfigArchiveDiff = () => {
    const [originalContent, setOriginalContent] = useState("");
    const [modifiedContent, setModifiedContent] = useState("");
    const [isShowSpin, setIsShowSpin] = useState(false);

    const selectChangeOriginal = value => {
        setIsShowSpin(true);
        if (value === "running_config") {
            try {
                fetchRunningConfigAPI(leftSn, "tree").then(response => {
                    setOriginalContent(response.data);
                    setIsShowSpin(false);
                });
            } catch (e) {
                message.error("An error occurred during the process of loading config");
                setIsShowSpin(false);
            }
        } else {
            try {
                fetchSnapshotConfigAPI(leftSn, value).then(data => {
                    setOriginalContent(convertConfigToStr(data.snapshotConfig));
                    setIsShowSpin(false);
                });
            } catch (e) {
                message.error("An error occurred during the process of loading config");
                setIsShowSpin(false);
            }
        }
    };

    const selectChangeModified = value => {
        setIsShowSpin(true);
        if (value === "running_config") {
            try {
                fetchRunningConfigAPI(rightSn, "tree").then(response => {
                    setModifiedContent(response.data);
                    setIsShowSpin(false);
                });
            } catch (e) {
                message.error("An error occurred during the process of loading config");
                setIsShowSpin(false);
            }
        } else {
            try {
                fetchSnapshotConfigAPI(rightSn, value).then(data => {
                    setModifiedContent(convertConfigToStr(data.snapshotConfig));
                    setIsShowSpin(false);
                });
            } catch (e) {
                message.error("An error occurred during the process of loading config");
                setIsShowSpin(false);
            }
        }
    };

    const [selectModalOpen, setSelectModalOpen] = useState(false);
    const [title, setTitle] = useState("");
    const [leftSn, setLeftSn] = useState();
    const [rightSn, setRightSn] = useState();
    const [leftConfigName, setLeftConfigName] = useState();
    const [rightConfigName, setRightConfigName] = useState();

    const searchFieldsList = ["sn", "host_name", "mgt_ip", "platform_model"];

    const matchFieldsList = [
        {name: "sn", matchMode: "fuzzy"},
        {name: "host_name", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"}
    ];

    const onCancel = () => {
        setSelectModalOpen(false);
    };

    const columns = [
        createColumnConfig("Switch SN", "sn", TableFilterDropdown),
        createColumnConfig("Sysname", "host_name", TableFilterDropdown),
        {
            ...createColumnConfig("Mgt IP", "mgt_ip", TableFilterDropdown),
            render: (_, record) => {
                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.mgt_ip}
                    </Space>
                );
            }
        },
        createColumnConfig("Model", "platform_model", TableFilterDropdown)
        // {
        //     title: "Operation",
        //     render: (_, record) => (
        //         <Space size="middle" className={styles.actionLink}>
        //             <a
        //                 onClick={() => {
        //                     fetchSnapshotListAPI(record.sn).then(data => {
        //                         if (title === "Select the switch A") {
        //                             setLeftSn(record.sn);
        //                             setLeftConfigName([
        //                                 {
        //                                     label: <span>Running Config(tree format)</span>,
        //                                     options: [{label: <span>Running Config</span>, value: "running_config"}]
        //                                 },
        //                                 {
        //                                     label: <span>Backup Config</span>,
        //                                     options: data.map(item => ({
        //                                         label: <span>{`${item[0]} - ${item[1]}`}</span>,
        //                                         value: `${item[0]}`
        //                                     }))
        //                                 }
        //                             ]);
        //                         } else if (title === "Select the switch B") {
        //                             setRightSn(record.sn);
        //                             setRightConfigName([
        //                                 {
        //                                     label: <span>Running Config(tree format)</span>,
        //                                     options: [{label: <span>Running Config</span>, value: "running_config"}]
        //                                 },
        //                                 {
        //                                     label: <span>Backup Config</span>,
        //                                     options: data.map(item => ({
        //                                         label: <span>{`${item[0]} - ${item[1]}`}</span>,
        //                                         value: `${item[0]}`
        //                                     }))
        //                                 }
        //                             ]);
        //                         }
        //                     });
        //                     onCancel();
        //                 }}
        //             >
        //                 {(leftSn === record.sn && title === "Select the switch A") ||
        //                 (rightSn === record.sn && title === "Select the switch B")
        //                     ? "Selected"
        //                     : "Select"}
        //             </a>
        //         </Space>
        //     )
        // }
    ];

    return (
        <div>
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
            <AmpConCustomModalTable
                title={title}
                rowSelection={{
                    type: "radio",
                    selectedRowKeys: title === "Select the switch A" ? [leftSn] : [rightSn],
                    onChange: async (_, selectedRows) => {
                        const record = selectedRows[0];
                        if (!record) return;

                        const data = await fetchSnapshotListAPI(record.sn);

                        const runningOption = {
                            label: <span>Running Config(tree format)</span>,
                            options: [{label: <span>Running Config</span>, value: "running_config"}]
                        };

                        const backupOption = {
                            label: <span>Backup Config</span>,
                            options: data.map(item => ({
                                label: <span>{`${item[0]} - ${item[1]}`}</span>,
                                value: `${item[0]}`
                            }))
                        };

                        if (title === "Select the switch A") {
                            setLeftSn(record.sn);
                            setLeftConfigName([runningOption, backupOption]);
                        } else if (title === "Select the switch B") {
                            setRightSn(record.sn);
                            setRightConfigName([runningOption, backupOption]);
                        }

                        onCancel(); // 关闭弹窗等操作
                    }
                }}
                selectModalOpen={selectModalOpen}
                onCancel={onCancel}
                columns={columns}
                matchFieldsList={matchFieldsList}
                searchFieldsList={searchFieldsList}
                buttonProps={[]}
                fetchAPIInfo={fetchSwitchInfo}
                modalClass="ampcon-max-modal"
            />
            <Row style={{minWidth: "1200px", marginBottom: 24}}>
                <Col span={24}>
                    <Form layout="inline" style={{flexWrap: "nowrap"}}>
                        <Form.Item label="SN" style={{marginRight: 0}}>
                            <Input
                                placeholder="AutoFill SN"
                                disabled
                                value={leftSn}
                                style={{
                                    width: 220,
                                    marginLeft: 20,
                                    borderTopRightRadius: 0,
                                    borderBottomRightRadius: 0,
                                    color: leftSn ? "#212529" : undefined // 条件设置颜色
                                }}
                            />
                        </Form.Item>
                        <Form.Item>
                            <Button
                                onClick={() => {
                                    setTitle("Select the switch A");
                                    setSelectModalOpen(true);
                                }}
                                style={{borderTopLeftRadius: 0, borderBottomLeftRadius: 0}}
                            >
                                Select
                            </Button>
                        </Form.Item>
                        <Form.Item label="Select Config" style={{marginLeft: 8}}>
                            <Select
                                options={leftConfigName}
                                style={{width: 280, marginLeft: 12}}
                                onChange={selectChangeOriginal}
                            />
                        </Form.Item>
                        <Form.Item label="SN" style={{marginRight: 0, marginLeft: 15}}>
                            <Input
                                placeholder="AutoFill SN"
                                disabled
                                value={rightSn}
                                style={{
                                    width: 220,
                                    marginLeft: 20,
                                    borderTopRightRadius: 0,
                                    borderBottomRightRadius: 0
                                }}
                            />
                        </Form.Item>
                        <Form.Item>
                            <Button
                                onClick={() => {
                                    setTitle("Select the switch B");
                                    setSelectModalOpen(true);
                                }}
                                style={{borderTopLeftRadius: 0, borderBottomLeftRadius: 0}}
                            >
                                Select
                            </Button>
                        </Form.Item>
                        <Form.Item label="Select Config" style={{marginLeft: 8}}>
                            <Select
                                options={rightConfigName}
                                style={{width: 280, marginLeft: 12}}
                                onChange={selectChangeModified}
                            />
                        </Form.Item>
                    </Form>
                </Col>
            </Row>
            <Row>
                <Col span={24}>
                    <TextContentDiffComponent text1={originalContent} text2={modifiedContent} />
                </Col>
            </Row>
        </div>
    );
};
export default ConfigArchiveDiff;
