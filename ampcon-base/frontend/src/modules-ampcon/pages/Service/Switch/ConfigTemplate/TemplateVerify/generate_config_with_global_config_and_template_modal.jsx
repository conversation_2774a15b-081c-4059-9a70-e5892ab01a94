import {Button, Divider, Flex, Form, Input, message, Modal, Select} from "antd";
import {forwardRef, useEffect, useImperativeHandle, useState} from "react";
import {getTemplateInfo, queryAllGlobalConfigs, queryAllSiteTemplates} from "@/modules-ampcon/apis/template_api";

const GenerateConfigWithGlobalConfigAndTemplateModal = forwardRef((props, ref) => {
    const title = "Generate Config with Global config and Template";
    const editStyle = {
        height: `${window.innerHeight / 2}px`,
        border: "1px solid #FFFFFF",
        backgroundColor: "#F8FAFB",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none"
    };

    const {Option} = Select;

    const [isShowModal, setIsShowModal] = useState(false);
    const [globalConfigList, setGlobalConfigList] = useState([]);
    const [selectedGlobalConfigName, setSelectedGlobalConfigName] = useState("");
    const [siteTemplateList, setSiteTemplateList] = useState([]);
    const [selectedSiteTemplateName, setSelectedSiteTemplateName] = useState("");
    const [content, setContent] = useState("");

    const {generateCallback} = props;

    useImperativeHandle(ref, () => ({
        showGenerateConfigWithGlobalConfigAndTemplateModal: () => {
            setIsShowModal(true);
        },
        hideGenerateConfigWithGlobalConfigAndTemplateModal: () => {
            setIsShowModal(false);
        }
    }));

    useEffect(() => {
        queryAllGlobalConfigName().then();
        queryAllSiteTemplate().then();
    }, []);

    const handleGenerateButtonCallback = () => {
        generateCallback(selectedGlobalConfigName, selectedSiteTemplateName, content);
        setIsShowModal(false);
    };

    const queryAllGlobalConfigName = async () => {
        queryAllGlobalConfigs().then(response => {
            setGlobalConfigList(response.data);
        });
    };

    const queryAllSiteTemplate = async () => {
        queryAllSiteTemplates().then(response => {
            setSiteTemplateList(response.data);
        });
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
            }}
            footer={
                <>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                        }}
                    >
                        Cancel
                    </Button>
                    <Button type="primary" onClick={handleGenerateButtonCallback}>
                        Apply
                    </Button>
                </>
            }
        >
            <Flex flex={1} vertical style={{minHeight: "268px"}}>
                <Form layout="inline">
                    <Form.Item label="Global_Config" style={{paddingBottom: "1rem"}}>
                        <Select
                            style={{width: "200px"}}
                            value={selectedGlobalConfigName}
                            onChange={value => {
                                setSelectedGlobalConfigName(value);
                            }}
                        >
                            {globalConfigList.map(config => {
                                return (
                                    <Option key={config.id} value={config.id}>
                                        {config.name}
                                    </Option>
                                );
                            })}
                        </Select>
                    </Form.Item>
                    <Form.Item label="Site_Template">
                        <Select
                            style={{width: "200px"}}
                            value={selectedSiteTemplateName}
                            onChange={value => {
                                setSelectedSiteTemplateName(value);
                                getTemplateInfo(value).then(response => {
                                    if (response.status !== 200) {
                                        message.error(response.info);
                                    } else {
                                        setContent(response.data.templateContent);
                                    }
                                });
                            }}
                        >
                            {siteTemplateList.map(template => {
                                return (
                                    <Option key={template.id} value={template.name}>
                                        {template.name}
                                    </Option>
                                );
                            })}
                        </Select>
                    </Form.Item>
                </Form>
                <Input.TextArea
                    style={editStyle}
                    value={content}
                    onChange={e => {
                        setContent(e.target.value);
                    }}
                />
            </Flex>
        </Modal>
    ) : null;
});

export default GenerateConfigWithGlobalConfigAndTemplateModal;
