import {
    Button,
    Checkbox,
    Divider,
    Flex,
    Form,
    Input,
    message,
    Modal,
    Select,
    Space,
    Spin,
    theme,
    Tooltip,
    Tree
} from "antd";
import React, {useEffect, useState} from "react";
import {useLocation} from "react-router-dom";
import SwitchModelSelector from "@/modules-ampcon/components/switch_model_selector";
import ConfigContentTextareaModal from "@/modules-ampcon/components/config_content_textarea_modal";
import Icon, {PlusOutlined, FolderOutlined, FileOutlined, MinusOutlined} from "@ant-design/icons";
import {useSelector} from "react-redux";
import {
    editConfigFile,
    getAllSystemConfigBrief,
    getModelDefaultConfig,
    queryAllPlatforms,
    queryConfigFileContent,
    queryDeployConfigsTree
} from "@/modules-ampcon/apis/config_api";
import {
    queryGenerateMultipleConfig,
    queryAllGlobalConfigs,
    queryAllSiteTemplates,
    querySiteConfigParams,
    querySiteConfigSaveList,
    saveSiteConfig
} from "@/modules-ampcon/apis/template_api";
import DynamicConfigGen from "@/modules-ampcon/components/dynamic_config_gen";
import ConfigFilePreviewModal from "@/modules-ampcon/components/config_file_preveiw_modal";
import UploadSingleFileModal from "@/modules-ampcon/components/upload_singel_file_modal";
import {getUserGroup, queryModelPhysicPorts} from "@/modules-ampcon/apis/inventory_api";
import {searchSvg} from "@/utils/common/iconSvg";
import {getFabric, getSite} from "@/modules-ampcon/apis/lifecycle_api";
import switchConfigurationStyle from "./switch_configuration.module.scss";

const {Option} = Select;
let platformLocal = [];

const SwitchConfiguration = () => {
    const {
        token: {colorPrimary}
    } = theme.useToken();

    const switchSNServiceTagTooltip = "Enter a value for Switch SN or Service Tag.";
    const selectSwitchModelTooltip = "Select your Switch Model from the drop down list.";
    const deploymentLocationTooltip = "Enter values for Deployment Location.";
    const globalConfigTooltip = "Select Global Config for your switch model from the pull-down menu";
    const siteTemplateTooltip = "Select other templates";
    const groupToolTip = "Select a group";
    const uploadJsonButtonTooltip =
        'Click Upload by JSON Tab and In the pop-up "Upload by JSON" box, select the JSON file to be uploaded, then click the “Upload” button to complete JSON file import.';
    const goConfigNextButtonTooltip = "Click Next, Enter values for switch specific parameters.";
    const saveButtonModalTooltip = "Click Save to configure Switch.";
    const switchConfigEditFormTitle = "Switch Config Edit";
    const editConfigButtonTooltip = "Click Edit for Edit in Generate ConfigTemplate.";
    const cancelEditConfigButtonTooltip = "Click to cancel editing";
    const changeSystemConfigButtonModal = "System Config";
    const changeSystemConfigSelectLabel = "System Configuration:";
    const changeAgentConfigButtonModalTitle = "Pushing Agent Configuration";
    const agentConfigModalAgentEnableLabel = "Agent Enable";
    const agentConfigModalVPNEnableLabel = "VPN Enable";
    const agentConfigModalLacpEnableLabel = "Lacp Enable";
    const agentConfigModalAmpconServerLabel = "Ampcon Server";
    const agentConfigModalServerHostPrefixLabel = "Server Host Prefix";
    const agentConfigModalServerHostDomainLabel = "Server Host Domain";
    const agentConfigModalUplinkPortsLabel = "Uplink Ports";
    const agentConfigModalUplinkSpeedLabel = "Uplink Speed";
    const agentConfigModalTrunkVLANsLabel = "Trunk VLANs";
    const agentConfigModalNativeVLANLabel = "Native VLAN";
    const addSiteTemplateButtonTooltip = "Add site template";
    const addGroupButtonTooltip = "Add group";
    const deleteSiteTemplateButtonTooltip = "Delete site template";
    const deleteGroupButtonTooltip = "Delete group";
    const uploadByJsonModalTitle = "Upload by JSON";
    const previewConfigModalTitle = "Preview Config";
    const fabricSelectTooltip =
        "Select the fabric configuration for the switch. This determines the network topology and connectivity.";
    const siteSelectTooltip =
        "Select the site where the switch will be deployed. This helps in organizing and managing the network devices.";

    let timeoutId = null;
    let treeDataSearchArgs = null;

    const currentUser = useSelector(state => state.user.userInfo);

    const [siteTemplateListIndex, setSiteTemplateListIndex] = useState(0);
    const [groupListIndex, setGroupListIndex] = useState(0);

    const [form] = Form.useForm();
    const [systemConfigForm] = Form.useForm();
    const [agentConfigForm] = Form.useForm();

    // for switch config edit form
    const [platforms, setPlatforms] = useState([]);
    const [globalConfigList, setGlobalConfigList] = useState([]);
    const [siteTemplateList, setSiteTemplateList] = useState([]);

    const [groupList, setGroupList] = useState([]);
    const [systemConfigList, setSystemConfigList] = useState([]);

    const [selectedModel, setSelectedModel] = useState("");
    const [selectedGlobalConfigName, setSelectedGlobalConfigName] = useState("");
    const [rootSiteTemplateObject, setRootSiteTemplateObject] = useState({
        valid: true
    });
    const [additionalSiteTemplateObject, setAdditionalSiteTemplateObject] = useState({});
    const [rootGroupObject, setRootGroupObject] = useState({
        valid: true
    });
    const [additionalGroupObject, setAdditionalGroupObject] = useState({});
    const [isBackupConfig, setIsBackupConfig] = useState(true);
    const [selectedSystemConfig, setSelectedSystemConfig] = useState("Global");
    const [agentConfigFormValues, setAgentConfigFormValues] = useState({});

    const [isNextButtonDisabled, setIsNextButtonDisabled] = useState(true);
    const [physicPorts, setPhysicPorts] = useState({});

    // for tree building
    const [treeHeight, setTreeHeight] = useState(0);
    const [treeDataSearchInputValue, setTreeDataSearchInputValue] = useState("");
    const [treeData, setTreeData] = useState([]);
    const [selectedKeys, setSelectedKeys] = useState({});
    const [, setExpandedKeys] = useState([]);

    // for modal file preview
    const [isShowFileModal, setIsShowFileModal] = useState(false);
    const [isShowChangeSystemConfigModal, setIsShowChangeSystemConfigModal] = useState(false);
    const [isShowAgentConfigModal, setIsShowAgentConfigModal] = useState(false);
    const [configContent, setConfigContent] = useState("");

    // upload json modal
    const [isShowUploadJsonModal, setIsShowUploadJsonModal] = useState(false);
    const [fileList, setFileList] = useState([]);

    // for dynamic ref
    const [dynamicForm] = Form.useForm();
    const [isShowDynamicForm, setIsShowDynamicForm] = useState(false);

    // for preview config modal
    const [isShowPreviewConfigModal, setIsShowPreviewConfigModal] = useState(false);
    const [previewConfigModalGlobalConfigContent, setPreviewConfigModalGlobalConfigContent] = useState("");
    const [previewConfigModalSiteConfigContent, setPreviewConfigModalSiteConfigContent] = useState("");

    const [fabricList, setFabricList] = useState([]);
    const [siteList, setSiteList] = useState([]);
    const [selectedFabric, setSelectedFabric] = useState("default");
    const [selectedSite, setSelectedSite] = useState("default");

    const [formList, setFormList] = useState([]);

    // spin
    const [isShowSpin, setIsShowSpin] = useState(false);

    const location = useLocation();

    useEffect(() => {
        fetchData().then(() => {
            if (location.state?.sn || location.state?.model) {
                const formValues = form.getFieldsValue();
                formValues.serialNumber = location.state?.sn || "";
                formValues.switchModel = location.state?.model || selectedModel;
                formValues.switchModel = platformLocal.includes(formValues.switchModel)
                    ? formValues.switchModel
                    : "ag5648";
                setSelectedModel(formValues.switchModel);
                form.setFieldsValue(formValues);
            }
        });
        calculateTreeHeight();
        window.addEventListener("resize", calculateTreeHeight);
        return () => {
            window.removeEventListener("resize", calculateTreeHeight);
        };
    }, []);

    useEffect(() => {
        if (isGroupUser()) {
            getUserGroup().then(response => {
                setGroupList(response.data.filter(item => item.group_type === "switch"));
            });
        }
    }, [currentUser]);

    const isGroupUser = () => {
        return currentUser.userType === "group";
    };

    const fetchData = async () => {
        const platformResponse = await queryAllPlatforms();

        if (import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC") {
            await getFabric().then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    setFabricList(response.data);
                    setSiteList(["default"]);
                }
            });
        } else if (import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-CAMPUS") {
            await getSite().then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    setFabricList(["default"]);
                    setSiteList(response.data);
                }
            });
        }
        setPlatforms(platformResponse.data);
        platformLocal = Object.values(platformResponse.data).flat();
        setSelectedModel(platformResponse.data.x86[0]);
        refreshTree().then();
        queryAllGlobalConfigName().then();
        queryAllSiteTemplate().then();
        getAllSystemConfigBrief().then(response => {
            setSystemConfigList(response.data);
        });
        const formValues = form.getFieldsValue();
        [formValues.switchModel] = platformResponse.data.x86;
        form.setFieldsValue(formValues);
        refreshAgentConfigModal(platformResponse.data.x86[0]).then();
    };

    const queryAllGlobalConfigName = async () => {
        queryAllGlobalConfigs().then(response => {
            setGlobalConfigList(response.data);
        });
    };

    const queryAllSiteTemplate = async () => {
        queryAllSiteTemplates().then(response => {
            setSiteTemplateList(response.data);
        });
    };

    const systemConfigButtonCallback = () => {
        setIsShowChangeSystemConfigModal(true);
    };

    const getSiteTemplateKeysWithValuesMoreThanTwice = formValues => {
        const filteredFormValues = Object.keys(formValues)
            .filter(key => key.startsWith("siteTemplate"))
            .reduce((result, key) => {
                result[key] = formValues[key];
                return result;
            }, {});
        const valueCounts = Object.values(filteredFormValues).reduce((counts, value) => {
            counts[value] = (counts[value] || 0) + 1;
            return counts;
        }, {});
        const valuesMoreThanTwice = Object.keys(valueCounts).filter(value => valueCounts[value] >= 2);
        return Object.keys(formValues).filter(key => valuesMoreThanTwice.includes(formValues[key]));
    };

    const getGroupKeysWithValuesMoreThanTwice = formValues => {
        const filteredFormValues = Object.keys(formValues)
            .filter(key => key.startsWith("group"))
            .reduce((result, key) => {
                result[key] = formValues[key];
                return result;
            }, {});
        const valueCounts = Object.values(filteredFormValues).reduce((counts, value) => {
            counts[value] = (counts[value] || 0) + 1;
            return counts;
        }, {});
        const valuesMoreThanTwice = Object.keys(valueCounts).filter(value => valueCounts[value] >= 2);
        return Object.keys(formValues).filter(key => valuesMoreThanTwice.includes(formValues[key]));
    };

    const agentConfigButtonCallback = () => {
        setIsShowAgentConfigModal(true);
    };

    const refreshAgentConfigModal = async model => {
        const getModel = model || selectedModel;
        getModelDefaultConfig(getModel).then(response => {
            const formData = {
                agentEnable: response.data.enable,
                vpnEnable: response.data.vpn_enable,
                lacpEnable: response.data.inband_lacp,
                ampconServer: response.data.server_vpn_host,
                serverHostPrefix: response.data.server_hostname_prefix,
                serverHostDomain: response.data.server_domain,
                uplinkPorts: response.data.uplink_ports,
                uplinkSpeed: response.data.uplink_speed,
                trunkVLANs: response.data.inband_vlan,
                nativeVLAN: response.data.inband_native_vlan
            };
            setAgentConfigFormValues(formData);
            agentConfigForm.setFieldsValue(formData);
        });
        queryModelPhysicPorts(model).then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                setPhysicPorts(response.data);
            }
        });
    };

    const siteTemplateValueCheck = (formValues, newAdditionalSiteTemplateObject) => {
        const keysWithValuesMoreThanTwice = getSiteTemplateKeysWithValuesMoreThanTwice(formValues);
        // set all keys to valid
        setRootSiteTemplateObject({valid: true});
        Object.keys(newAdditionalSiteTemplateObject).forEach(key => {
            newAdditionalSiteTemplateObject[key].valid = true;
        });
        keysWithValuesMoreThanTwice.forEach(key => {
            if (!key.startsWith("siteTemplate")) {
                return;
            }
            if (key === "siteTemplate0") {
                setRootSiteTemplateObject({valid: false});
            } else {
                newAdditionalSiteTemplateObject[key].valid = false;
            }
        });
        setAdditionalSiteTemplateObject(newAdditionalSiteTemplateObject);
        formSiteTemplateOnChangeCallback(formValues);
    };

    const groupValueCheck = (formValues, newAdditionalGroupObject) => {
        const keysWithValuesMoreThanTwice = getGroupKeysWithValuesMoreThanTwice(formValues);
        // set all keys to valid
        setRootGroupObject({valid: true});
        Object.keys(newAdditionalGroupObject).forEach(key => {
            newAdditionalGroupObject[key].valid = true;
        });
        keysWithValuesMoreThanTwice.forEach(key => {
            if (!key.startsWith("group")) {
                return;
            }
            if (key === "group0") {
                setRootGroupObject({valid: false});
            } else {
                newAdditionalGroupObject[key].valid = false;
            }
        });
        setAdditionalGroupObject(newAdditionalGroupObject);
        formGroupOnChangeCallback(formValues);
    };

    const handleSiteTemplateChangeCallback = (selectedItem, siteTemplateKey) => {
        const formValues = form.getFieldsValue();
        formValues[siteTemplateKey] = selectedItem.children;
        form.setFieldsValue(formValues);
        const newAdditionalSiteTemplateObject = JSON.parse(JSON.stringify(additionalSiteTemplateObject));
        siteTemplateValueCheck(formValues, newAdditionalSiteTemplateObject);
    };

    const handleGroupChangeCallback = (selectedItem, siteGroupKey) => {
        const formValues = form.getFieldsValue();
        formValues[siteGroupKey] = selectedItem.children;
        form.setFieldsValue(formValues);
        const newAdditionalGroupObject = JSON.parse(JSON.stringify(additionalGroupObject));
        groupValueCheck(formValues, newAdditionalGroupObject);
    };

    const formSiteTemplateOnChangeCallback = formValues => {
        formValues = formValues || form.getFieldsValue();
        const emptyCount = Object.values(formValues).filter(value => Boolean(value) === false).length;
        // Option Post-Deployed is always empty
        if (emptyCount > 1) {
            setIsNextButtonDisabled(true);
            return;
        }
        const keysWithValuesMoreThanTwice = getSiteTemplateKeysWithValuesMoreThanTwice(formValues);
        if (keysWithValuesMoreThanTwice.length > 0) {
            setIsNextButtonDisabled(true);
        } else {
            setIsNextButtonDisabled(false);
        }
    };

    const formGroupOnChangeCallback = formValues => {
        formValues = formValues || form.getFieldsValue();
        const emptyCount = Object.values(formValues).filter(value => Boolean(value) === false).length;
        // Option Post-Deployed is always empty
        if (emptyCount > 1) {
            setIsNextButtonDisabled(true);
            return;
        }
        const keysWithValuesMoreThanTwice = getGroupKeysWithValuesMoreThanTwice(formValues);
        if (keysWithValuesMoreThanTwice.length > 0) {
            setIsNextButtonDisabled(true);
        } else {
            setIsNextButtonDisabled(false);
        }
    };

    const handleRemoveSiteTemplateCallback = (selectedItem, siteTemplateKey) => {
        const newAdditionalSiteTemplateObject = JSON.parse(JSON.stringify(additionalSiteTemplateObject));
        delete newAdditionalSiteTemplateObject[siteTemplateKey];
        setAdditionalSiteTemplateObject(newAdditionalSiteTemplateObject);
        const formValues = form.getFieldsValue();
        delete formValues[siteTemplateKey];
        form.setFieldsValue(formValues);
        siteTemplateValueCheck(formValues, newAdditionalSiteTemplateObject);
    };

    const handleRemoveGroupCallback = (selectedItem, groupKey) => {
        const newAdditionalGroupObject = JSON.parse(JSON.stringify(additionalGroupObject));
        delete newAdditionalGroupObject[groupKey];
        setAdditionalSiteTemplateObject(newAdditionalGroupObject);
        const formValues = form.getFieldsValue();
        delete formValues[groupKey];
        form.setFieldsValue(formValues);
        groupValueCheck(formValues, newAdditionalGroupObject);
    };

    const handleNextButtonCallback = () => {
        const formValues = form.getFieldsValue();
        setIsShowDynamicForm(false);
        const siteTemplateValues = Object.keys(formValues)
            .filter(key => key.startsWith("siteTemplate"))
            .map(key => formValues[key]);
        querySiteConfigParams(siteTemplateValues).then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                const dataList = [];
                response.data.forEach(item => {
                    const parseFormJSON = (jsonData, dataList) => {
                        const temp = {};
                        const result = [];
                        Object.entries(jsonData).forEach(([key, value]) => {
                            value.name = key;
                            if (value.type.includes("_map_list")) {
                                value.key = key;
                                value.type = value.type.replace("_map_list", "");
                                value.isMapList = true;
                                value.isList = false;
                                value.mapListChildren = [];
                                parseFormJSON(value.children, value.mapListChildren);
                            } else if (value.type.includes("_list")) {
                                value.key = key;
                                value.type = value.type.replace("_list", "");
                                value.isMapList = false;
                                value.isList = true;
                            } else {
                                value.isList = false;
                                value.key = key;
                            }
                            result[value.index] = value;
                        });
                        temp.title = item.title;
                        temp.formData = result;
                        dataList.push(temp);
                    };
                    parseFormJSON(item.formJSON, dataList);
                    dynamicForm.resetFields();
                    setIsShowDynamicForm(true);
                });
                setFormList(dataList);
            }
        });
    };

    const calculateTreeHeight = () => {
        setTreeHeight(210 + (window.innerHeight - 916) * 0.603 < 200 ? 200 : 210 + (window.innerHeight - 916) * 0.603);
    };

    function updateNodesWithIcons(nodes) {
        return nodes.map(node => {
            const updatedNode = {...node, icon: node.is_folder ? <FolderOutlined /> : <FileOutlined />};
            if (node.children && node.children.length > 0) {
                updatedNode.children = updateNodesWithIcons(node.children);
            }
            return updatedNode;
        });
    }

    const refreshTree = async () => {
        const SiteTemplateTreeResponse = await queryDeployConfigsTree("site", 1, 100, treeDataSearchArgs);
        const treeTempData = updateNodesWithIcons(SiteTemplateTreeResponse);
        setTreeData(treeTempData);
        const keys = treeTempData.reduce(
            (acc, node) => [...acc, node.key, ...(node.children ? node.children.map(child => child.key) : [])],
            []
        );
        setExpandedKeys(keys);
    };

    const refreshConfigContentModalTextarea = () => {
        setIsShowFileModal(false);
        setConfigContent("");
    };

    const onSelectTreeNodeCallback = (selectedKeys, info) => {
        setSelectedKeys({
            name: info.node.key,
            model: info.node.model
        });
        if (info.node.is_folder) {
            setIsShowFileModal(false);
        } else {
            refreshConfigContentModalTextarea();
            queryConfigFileContent(info.node.key).then(response => {
                if (response.status !== 200) {
                    message.error("Error fetching file content");
                } else {
                    setIsShowFileModal(true);
                    setConfigContent(response.data);
                }
            });
        }
    };

    const searchTreeInputCallback = e => {
        treeDataSearchArgs = e.target.value;
        setTreeDataSearchInputValue(e.target.value);
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(() => {
            refreshTree().then(() => {});
        }, 1000);
    };

    const onDynamicSaveCallback = async () => {
        try {
            await form.validateFields();
        } catch (errorInfo) {
            message.error("Please check the switch config edit form fields.");
            return;
        }
        try {
            // Perform all validations
            await dynamicForm.validateFields();
            // If no errors, proceed with saving the form
            queryConfigFileContent(selectedGlobalConfigName).then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    setPreviewConfigModalGlobalConfigContent(response.data);
                    queryGenerateMultipleConfig(dynamicForm.getFieldsValue()).then(response => {
                        if (response.status !== 200) {
                            message.error(response.info);
                        } else {
                            setPreviewConfigModalSiteConfigContent(response.data);
                            setIsShowPreviewConfigModal(true);
                        }
                    });
                }
            });
        } catch (errorInfo) {
            message.error("There are some errors in the form, please check and submit again!");
        }
    };

    const uploadByJsonCallback = async () => {
        setIsShowUploadJsonModal(false);
        setIsShowSpin(true);
        try {
            await querySiteConfigSaveList(fileList[0]).then(response => {
                if (response.status !== 200) {
                    if (response.success.length === 0 && response.failed.length === 0) {
                        message.error("An error occurred during the process of upload");
                    } else {
                        if (response.success.length !== 0) {
                            message.success(`Upload ${response.success.join(", ")} success\n`);
                        }
                        if (response.failed.length !== 0) {
                            response.failed.forEach(error => {
                                message.error(`Upload ${error.sn} failed, ${error.info}\n`);
                            });
                        }
                    }
                } else {
                    message.success(`Upload ${response.success.join(", ")} success`);
                }
            });
            form.resetFields();
            fetchData();
            setIsShowSpin(false);
            setFileList([]);
        } catch (e) {
            message.error("An error occurred during the process of upload");
        } finally {
            setIsShowSpin(false);
        }
    };

    const onDynamicResetCallback = () => {
        dynamicForm.resetFields();
    };

    const handleModalSaveButtonCallback = async () => {
        setIsShowPreviewConfigModal(false);
        const data = {};
        const formValues = form.getFieldsValue();
        data.selectedSystemConfig = selectedSystemConfig;
        data.agentConfiguration = agentConfigForm.getFieldValue();
        data.generateTemplateInfo = {
            generateTemplateDescription: "site_config",
            generateGlobalConfig: selectedGlobalConfigName,
            generateConfigName: `${form.getFieldsValue().serialNumber}_site_config`,
            generateConfigPlatform: selectedModel,
            generateSwitchSN: form.getFieldsValue().serialNumber,
            generateConfigLocation: form.getFieldsValue().deploymentLocation,
            isRetrieveConfig: isBackupConfig,
            group: Object.keys(formValues)
                .filter(key => key.startsWith("group"))
                .map(key => formValues[key])
        };
        data.siteTemplateInfo = dynamicForm.getFieldsValue()[data.generateTemplateInfo.generateConfigName];
        data.selectedFabric = selectedFabric;
        data.selectedSite = selectedSite;
        setIsShowSpin(true);
        try {
            await saveSiteConfig(data).then(response => {
                try {
                    if (response.status !== 200) {
                        message.error(response.info);
                    } else {
                        message.success(response.info);
                        form.resetFields();
                        setIsNextButtonDisabled(true);
                        fetchData();
                        setIsShowDynamicForm(false);
                    }
                } finally {
                    setIsShowSpin(false);
                }
            });
            setIsShowSpin(false);
        } catch (e) {
            message.error("An error occurred during the process of saving site config");
        } finally {
            setIsShowSpin(false);
        }
    };

    const handleExportButtonCallback = () => {
        const filename = `${form.getFieldsValue().serialNumber}_site_config.txt`;
        const blob = new Blob([`${previewConfigModalGlobalConfigContent}${previewConfigModalSiteConfigContent}`], {
            type: "octet/stream"
        });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.style = "display: none";
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    };

    return (
        <>
            <div>
                <Spin spinning={isShowSpin} tip="Loading..." fullscreen />

                <ConfigContentTextareaModal
                    title={selectedKeys.name}
                    content={configContent}
                    saveCallback={() => {
                        editConfigFile(selectedKeys.name, configContent).then(response => {
                            if (response.status !== 200) {
                                message.error(response.info);
                            } else {
                                setIsShowFileModal(false);
                                message.success(response.info);
                            }
                        });
                    }}
                    handleFileContentChange={e => {
                        setConfigContent(e.target.value);
                    }}
                    handleCancel={() => {
                        setIsShowFileModal(false);
                    }}
                    isOpen={isShowFileModal}
                    saveConfigButtonToolTip={saveButtonModalTooltip}
                    editConfigButtonTooltip={editConfigButtonTooltip}
                    cancelEditConfigButtonTooltip={cancelEditConfigButtonTooltip}
                />
                <Modal
                    className="ampcon-middle-modal"
                    title={
                        <div>
                            {changeSystemConfigButtonModal}
                            <Divider style={{marginTop: 8, marginBottom: 0}} />
                        </div>
                    }
                    open={isShowChangeSystemConfigModal}
                    onCancel={() => {
                        setIsShowChangeSystemConfigModal(false);
                    }}
                    footer={null}
                >
                    <Form
                        layout="horizontal"
                        labelAlign="left"
                        labelCol={{flex: "174px"}}
                        wrapperCol={{flex: "280px"}}
                        form={systemConfigForm}
                        style={{marginBottom: "40px"}}
                    >
                        <Form.Item name="systemConfig" label={changeSystemConfigSelectLabel}>
                            <Select
                                placeholder="Please select system configuration for the switch"
                                defaultValue={selectedSystemConfig}
                                onChange={value => {
                                    setSelectedSystemConfig(value);
                                }}
                            >
                                {systemConfigList.map(systemConfig => {
                                    return (
                                        <Option
                                            key={systemConfig.system_config_id}
                                            value={systemConfig.system_config_name}
                                        >
                                            {systemConfig.system_config_name}
                                        </Option>
                                    );
                                })}
                            </Select>
                        </Form.Item>
                    </Form>
                </Modal>
                <Modal
                    className="ampcon-middle-modal"
                    title={
                        <div>
                            {changeAgentConfigButtonModalTitle}
                            <Divider style={{marginTop: 8, marginBottom: 0}} />
                        </div>
                    }
                    open={isShowAgentConfigModal}
                    onCancel={() => {
                        setIsShowAgentConfigModal(false);
                    }}
                    footer={null}
                >
                    <Form
                        layout="horizontal"
                        labelAlign="left"
                        labelCol={{flex: "166px"}}
                        wrapperCol={{flex: "280px"}}
                        form={agentConfigForm}
                        initialValues={agentConfigFormValues}
                        onChange={() => {
                            setAgentConfigFormValues(agentConfigForm.getFieldsValue());
                        }}
                    >
                        <Form.Item name="agentEnable" label={agentConfigModalAgentEnableLabel}>
                            <Select>
                                <Option key="True" value="True">
                                    True
                                </Option>
                                <Option key="False" value="False">
                                    False
                                </Option>
                            </Select>
                        </Form.Item>
                        <Form.Item name="vpnEnable" label={agentConfigModalVPNEnableLabel}>
                            <Input disabled />
                        </Form.Item>
                        <Form.Item name="lacpEnable" label={agentConfigModalLacpEnableLabel}>
                            <Select>
                                <Option key="True" value="True">
                                    True
                                </Option>
                                <Option key="False" value="False">
                                    False
                                </Option>
                            </Select>
                        </Form.Item>
                        <Form.Item name="ampconServer" label={agentConfigModalAmpconServerLabel}>
                            <Input />
                        </Form.Item>
                        <Form.Item name="serverHostPrefix" label={agentConfigModalServerHostPrefixLabel}>
                            <Input />
                        </Form.Item>
                        <Form.Item name="serverHostDomain" label={agentConfigModalServerHostDomainLabel}>
                            <Input />
                        </Form.Item>
                        <Form.Item name="uplinkPorts" label={agentConfigModalUplinkPortsLabel}>
                            <Input />
                        </Form.Item>
                        <Form.Item name="uplinkSpeed" label={agentConfigModalUplinkSpeedLabel}>
                            <Input />
                        </Form.Item>
                        <Form.Item name="trunkVLANs" label={agentConfigModalTrunkVLANsLabel}>
                            <Input />
                        </Form.Item>
                        <Form.Item name="nativeVLAN" label={agentConfigModalNativeVLANLabel}>
                            <Input />
                        </Form.Item>
                    </Form>
                </Modal>
                <UploadSingleFileModal
                    uploadByJsonModalTitle={uploadByJsonModalTitle}
                    isShowUploadJsonModal={isShowUploadJsonModal}
                    fileList={fileList}
                    uploadButtonCallback={uploadByJsonCallback}
                    beforeUploadCallback={file => {
                        setFileList(file);
                        return false;
                    }}
                    onRemoveCallback={() => {
                        setFileList([]);
                    }}
                    cancelCallback={() => {
                        setIsShowUploadJsonModal(false);
                    }}
                />
                <ConfigFilePreviewModal
                    title={previewConfigModalTitle}
                    isOpen={isShowPreviewConfigModal}
                    globalConfigContent={previewConfigModalGlobalConfigContent}
                    siteConfigContent={previewConfigModalSiteConfigContent}
                    saveCallback={handleModalSaveButtonCallback}
                    cancelButtonCallback={() => {
                        setIsShowPreviewConfigModal(false);
                    }}
                    exportButtonCallback={handleExportButtonCallback}
                />
            </div>
            <Flex
                layout="horizontal"
                className={switchConfigurationStyle.tile}
                style={{minWidth: "fit-content", flex: 1}}
            >
                <Flex
                    gap="large"
                    vertical
                    style={{
                        minWidth: "600px",
                        maxWidth: "800px",
                        width: "40%",
                        height: "100%"
                    }}
                >
                    <Flex gap="middle" vertical style={{width: "600px", minHeight: "40%", backgroundColor: "#FFFFFF"}}>
                        <Flex vertical justify="space-between">
                            {/* <h2 style={{margin: "0", marginBottom: "20px"}}>Switch Configuartion</h2> */}
                            <Flex layout="horizontal" justify="space-between">
                                <h3 style={{margin: "-12px 0px 20px 0px"}}>{switchConfigEditFormTitle}</h3>
                                <Tooltip title={uploadJsonButtonTooltip} placement="topRight">
                                    <Button
                                        type="primary"
                                        htmlType="submit"
                                        style={{marginRight: "120px", minWidth: "80px"}}
                                        onClick={() => {
                                            setIsShowUploadJsonModal(true);
                                            setFileList([]);
                                        }}
                                    >
                                        Upload by JSON
                                    </Button>
                                </Tooltip>
                            </Flex>
                        </Flex>
                        <div>
                            <Form
                                layout="horizontal"
                                labelAlign="left"
                                labelCol={{span: 8}}
                                wrapperCol={{span: 14}}
                                form={form}
                                onChange={() => {
                                    formSiteTemplateOnChangeCallback();
                                }}
                                initialValues={{fabric: "default", site: "default"}}
                            >
                                <Form.Item
                                    name="serialNumber"
                                    label="Switch SN / Service Tag"
                                    tooltip={switchSNServiceTagTooltip}
                                    rules={[
                                        {required: true, message: "Please input your serial number!"},
                                        {
                                            pattern: /^[\dA-Za-z]+$/,
                                            message: "Serial number can only contain letters and numbers!"
                                        }
                                    ]}
                                >
                                    <Input style={{width: "280px"}} placeholder="Please input your serial number" />
                                </Form.Item>
                                <Form.Item
                                    label="Switch Model"
                                    name="switchModel"
                                    required="true"
                                    tooltip={selectSwitchModelTooltip}
                                >
                                    <SwitchModelSelector
                                        placeholder="Select model"
                                        handleChange={value => {
                                            setSelectedModel(value);
                                            const formValues = form.getFieldsValue();
                                            formValues.switchModel = value;
                                            form.setFieldsValue(formValues);
                                            refreshAgentConfigModal(value).then(() => {});
                                        }}
                                        data={platforms}
                                        selectedModel={selectedModel}
                                        style={{width: "280px"}}
                                    />
                                </Form.Item>
                                <Form.Item
                                    name="deploymentLocation"
                                    label="Deployment Location"
                                    tooltip={deploymentLocationTooltip}
                                    rules={[
                                        {required: true, message: "Please input your deployment location!"},
                                        {
                                            pattern: /^\w+$/,
                                            message: "Deployment location can only contain letters, numbers and _!"
                                        }
                                    ]}
                                >
                                    <Input
                                        style={{width: "280px"}}
                                        placeholder="Please input your deployment location!"
                                    />
                                </Form.Item>

                                {import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC" && (
                                    <Form.Item
                                        name="fabric"
                                        label="Fabric"
                                        tooltip={fabricSelectTooltip}
                                        rules={[{required: true, message: "Please select a fabric!"}]}
                                    >
                                        <Select
                                            style={{width: "280px"}}
                                            placeholder="Please select a fabirc"
                                            defaultValue={selectedFabric}
                                            onChange={value => {
                                                setSelectedFabric(value);
                                            }}
                                        >
                                            {fabricList.map(fabric => {
                                                return (
                                                    <Option key={fabric} value={fabric}>
                                                        {fabric}
                                                    </Option>
                                                );
                                            })}
                                        </Select>
                                    </Form.Item>
                                )}
                                {import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-CAMPUS" && (
                                    <Form.Item
                                        name="site"
                                        label="Site"
                                        tooltip={siteSelectTooltip}
                                        rules={[{required: true, message: "Please select a site!"}]}
                                    >
                                        <Select
                                            style={{width: "280px"}}
                                            placeholder="Please select a site"
                                            defaultValue={selectedSite}
                                            onChange={value => {
                                                setSelectedSite(value);
                                            }}
                                        >
                                            {siteList.map(site => {
                                                return (
                                                    <Option key={site} value={site}>
                                                        {site}
                                                    </Option>
                                                );
                                            })}
                                        </Select>
                                    </Form.Item>
                                )}

                                <Form.Item
                                    name="globalConfig"
                                    label="Select Global Config"
                                    tooltip={globalConfigTooltip}
                                    rules={[{required: true, message: "Please select a global configuration!"}]}
                                >
                                    <Select
                                        style={{width: "280px"}}
                                        placeholder="Please select a global configuration"
                                        onChange={(value, selectedItem) => {
                                            setSelectedGlobalConfigName(selectedItem.children);
                                            formSiteTemplateOnChangeCallback();
                                        }}
                                    >
                                        {globalConfigList.map(config => {
                                            return (
                                                <Option key={config.id} value={config.id}>
                                                    {config.name}
                                                </Option>
                                            );
                                        })}
                                    </Select>
                                </Form.Item>
                                <Form.Item
                                    name="siteTemplate0"
                                    label="Select Site Template"
                                    tooltip={siteTemplateTooltip}
                                    validateStatus={rootSiteTemplateObject.valid ? "success" : "error"}
                                    help={
                                        rootSiteTemplateObject.valid
                                            ? ""
                                            : "The selected site template cannot be selected."
                                    }
                                    validateTrigger={["onChange", "onBlur"]}
                                    required
                                >
                                    <Select
                                        style={{width: "280px"}}
                                        placeholder="Please select a site template"
                                        onChange={(value, selectedItem) => {
                                            handleSiteTemplateChangeCallback(selectedItem, "siteTemplate0");
                                        }}
                                    >
                                        {siteTemplateList.map(template => {
                                            return (
                                                <Option key={template.id} value={template.name}>
                                                    {template.name}
                                                </Option>
                                            );
                                        })}
                                    </Select>
                                    {/* <Tooltip title={addSiteTemplateButtonTooltip} placement="topRight"> */}
                                    <Button
                                        style={{
                                            backgroundColor: "transparent",
                                            color: "#BFBFBF"
                                        }}
                                        type="link"
                                        onClick={() => {
                                            const newAdditionalSiteTemplateObject = JSON.parse(
                                                JSON.stringify(additionalSiteTemplateObject)
                                            );
                                            newAdditionalSiteTemplateObject[
                                                `siteTemplate${siteTemplateListIndex + 1}`
                                            ] = {
                                                valid: true
                                            };
                                            setSiteTemplateListIndex(siteTemplateListIndex + 1);
                                            setAdditionalSiteTemplateObject(newAdditionalSiteTemplateObject);
                                            setIsNextButtonDisabled(true);
                                        }}
                                        icon={<PlusOutlined />}
                                    />
                                    {/* </Tooltip> */}
                                </Form.Item>
                                {Object.entries(additionalSiteTemplateObject).map(
                                    ([siteTemplateKey, siteTemplateValue]) => {
                                        return (
                                            <Form.Item
                                                name={siteTemplateKey}
                                                label="Select Site Template"
                                                tooltip={siteTemplateTooltip}
                                                validateStatus={siteTemplateValue.valid ? "success" : "error"}
                                                help={
                                                    siteTemplateValue.valid
                                                        ? ""
                                                        : "The selected site template cannot be selected."
                                                }
                                                validateTrigger={["onChange", "onBlur"]}
                                                required
                                            >
                                                <Select
                                                    style={{width: "280px"}}
                                                    placeholder="Please select a site template"
                                                    onChange={(value, selectedItem) => {
                                                        handleSiteTemplateChangeCallback(selectedItem, siteTemplateKey);
                                                    }}
                                                >
                                                    {siteTemplateList.map(template => {
                                                        return (
                                                            <Option key={template.id} value={template.id}>
                                                                {template.name}
                                                            </Option>
                                                        );
                                                    })}
                                                </Select>
                                                {/* <Tooltip title={deleteSiteTemplateButtonTooltip} placement="topRight"> */}
                                                <Button
                                                    style={{
                                                        backgroundColor: "transparent",
                                                        color: "#BFBFBF"
                                                    }}
                                                    type="link"
                                                    onClick={(value, selectedItem) => {
                                                        handleRemoveSiteTemplateCallback(selectedItem, siteTemplateKey);
                                                    }}
                                                    icon={<MinusOutlined />}
                                                />
                                                {/* </Tooltip> */}
                                            </Form.Item>
                                        );
                                    }
                                )}

                                {isGroupUser() ? (
                                    <>
                                        <Form.Item
                                            name="group0"
                                            label="Select Group"
                                            tooltip={groupToolTip}
                                            rules={[{required: true, message: "Please select a group!"}]}
                                            validateStatus={rootGroupObject.valid ? "success" : "error"}
                                            help={rootGroupObject.valid ? "" : "The selected group cannot be selected."}
                                            validateTrigger={["onChange", "onBlur"]}
                                        >
                                            <Select
                                                style={{width: "280px"}}
                                                placeholder="Please select group"
                                                onChange={(value, selectedItem) => {
                                                    handleGroupChangeCallback(selectedItem, "group0");
                                                }}
                                            >
                                                {groupList.map(group => {
                                                    return (
                                                        <Option key={group.id} value={group.name}>
                                                            {group.name}
                                                        </Option>
                                                    );
                                                })}
                                            </Select>
                                            <Tooltip title={addGroupButtonTooltip} placement="topRight">
                                                <Button
                                                    style={{
                                                        backgroundColor: "transparent",
                                                        color: "#BFBFBF"
                                                    }}
                                                    type="link"
                                                    onClick={() => {
                                                        const newAdditionalGroupObject = JSON.parse(
                                                            JSON.stringify(additionalGroupObject)
                                                        );
                                                        newAdditionalGroupObject[`group${groupListIndex + 1}`] = {
                                                            valid: true
                                                        };
                                                        setGroupListIndex(groupListIndex + 1);
                                                        setAdditionalGroupObject(newAdditionalGroupObject);
                                                        setIsNextButtonDisabled(true);
                                                    }}
                                                    icon={<PlusOutlined />}
                                                />
                                            </Tooltip>
                                        </Form.Item>
                                        {Object.entries(additionalGroupObject).map(([groupKey, groupValue]) => {
                                            return (
                                                <Form.Item
                                                    name={groupKey}
                                                    label="Select Group"
                                                    tooltip={groupToolTip}
                                                    validateStatus={groupValue.valid ? "success" : "error"}
                                                    help={
                                                        groupValue.valid ? "" : "The selected group cannot be selected."
                                                    }
                                                    validateTrigger={["onChange", "onBlur"]}
                                                >
                                                    <Select
                                                        style={{width: "280px"}}
                                                        placeholder="Please select a group"
                                                        onChange={(value, selectedItem) => {
                                                            handleGroupChangeCallback(selectedItem, groupKey);
                                                        }}
                                                    >
                                                        {groupList.map(group => {
                                                            return (
                                                                <Option key={group.id} value={group.id}>
                                                                    {group.name}
                                                                </Option>
                                                            );
                                                        })}
                                                    </Select>
                                                    <Tooltip title={deleteGroupButtonTooltip} placement="topRight">
                                                        <Button
                                                            style={{
                                                                backgroundColor: "transparent",
                                                                color: "black"
                                                            }}
                                                            type="minus"
                                                            onClick={(value, selectedItem) => {
                                                                handleRemoveGroupCallback(selectedItem, groupKey);
                                                            }}
                                                            icon={<MinusOutlined />}
                                                        />
                                                    </Tooltip>
                                                </Form.Item>
                                            );
                                        })}
                                    </>
                                ) : null}

                                <Form.Item name="optionPostDeployed" label="Option Post-Deployed">
                                    <Flex justify="space-between" style={{width: "100%"}}>
                                        <Checkbox
                                            checked={isBackupConfig}
                                            onChange={() => {
                                                setIsBackupConfig(!isBackupConfig);
                                            }}
                                            style={{display: "flex", alignItems: "center"}}
                                        >
                                            Backup Config
                                        </Checkbox>
                                    </Flex>
                                </Form.Item>
                                <Form.Item label=" ">
                                    <Button type="primary" onClick={systemConfigButtonCallback}>
                                        System config
                                    </Button>
                                    <Button
                                        // type="link"
                                        hidden="true"
                                        style={{marginLeft: 16, color: colorPrimary}}
                                        onClick={agentConfigButtonCallback}
                                    >
                                        Agent
                                    </Button>
                                </Form.Item>
                                {/* <Divider /> */}
                                <div style={{marginTop: "50px"}} />
                                <Flex justify="space-between" style={{marginBottom: "20px"}}>
                                    <h3 style={{margin: "0"}}>Historical Switch Config Edit</h3>
                                    <Tooltip title={goConfigNextButtonTooltip} placement="topRight">
                                        <Button
                                            type="primary"
                                            htmlType="submit"
                                            style={{marginRight: "120px", minWidth: "80px"}}
                                            onClick={handleNextButtonCallback}
                                            disabled={isNextButtonDisabled}
                                        >
                                            Next
                                        </Button>
                                    </Tooltip>
                                </Flex>
                                <Flex vertical>
                                    <Space.Compact style={{width: "100%"}}>
                                        <Input
                                            placeholder="Search"
                                            prefix={<Icon component={searchSvg} />}
                                            allowClear
                                            onChange={searchTreeInputCallback}
                                            value={treeDataSearchInputValue}
                                            style={{float: "right", marginBottom: "20px", width: "280px"}}
                                        />
                                    </Space.Compact>
                                    {treeData.length !== 0 ? (
                                        <Tree
                                            showIcon
                                            showLine
                                            treeData={treeData}
                                            // selectedKeys={selectedKeys.name}
                                            onSelect={onSelectTreeNodeCallback}
                                            // expandedKeys={expandedKeys}
                                            // onExpand={setExpandedKeys}
                                            // autoExpandParent
                                            height={treeHeight}
                                        />
                                    ) : (
                                        <Flex justify="center">
                                            <Flex vertical>
                                                <div className="imageStyle" />
                                                <p style={{margin: "0 auto", color: "#9DA6B3", fontSize: "12px"}}>
                                                    No Data
                                                </p>
                                            </Flex>
                                        </Flex>
                                    )}
                                </Flex>
                            </Form>
                        </div>
                    </Flex>
                </Flex>
                {isShowDynamicForm ? (
                    <Flex vertical style={{flex: 1, borderLeft: "1px solid #F2F2F2", minWidth: "580px"}}>
                        <div className={switchConfigurationStyle.tile2}>
                            <Flex vertical>
                                <div>
                                    <Flex justify="space-between">
                                        <h2
                                            style={{marginTop: 0}}
                                        >{`${form.getFieldsValue().serialNumber}_site_config`}</h2>
                                        <Flex>
                                            <Button
                                                type="primary"
                                                onClick={onDynamicSaveCallback}
                                                style={{marginRight: "16px"}}
                                            >
                                                Save
                                            </Button>
                                            <Button
                                                htmlType="button"
                                                style={{minWidth: "80px"}}
                                                onClick={onDynamicResetCallback}
                                            >
                                                Reset
                                            </Button>
                                        </Flex>
                                    </Flex>
                                    <DynamicConfigGen
                                        title={`${form.getFieldsValue().serialNumber}_site_config`}
                                        physicPorts={physicPorts}
                                        form={dynamicForm}
                                        formList={formList}
                                        setFormList={setFormList}
                                        style={{flex: 1}}
                                    />
                                    {/* <Divider /> */}
                                </div>
                            </Flex>
                        </div>
                    </Flex>
                ) : null}
            </Flex>
        </>
    );
};

export default SwitchConfiguration;
