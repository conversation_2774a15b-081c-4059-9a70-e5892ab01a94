import {
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown,
    AmpConCustomModalTable
} from "@/modules-ampcon/components/custom_table";
import {Space, message, Button, Card, Modal, Input, Divider, Form, Flex} from "antd";
import {fetchAllSwitch, fetchAllSwitchWithFabric} from "@/modules-ampcon/apis/dashboard_api";
import {fetchParkingSwitch, delParkingSwitch, investigateSwitch, editSysname} from "@/modules-ampcon/apis/config_api";
import {useRef, useState, useEffect} from "react";
import {useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import LifecycleAction from "@/modules-ampcon/pages/Service/Switch/lifecycle_action";
import SSHAction from "@/modules-ampcon/pages/Service/Switch/ssh_action";
import ImportDropdownAction from "@/modules-ampcon/pages/Service/Switch/import_dropdown_action";
import LifecycletDropdownAction from "@/modules-ampcon/pages/Service/Switch/lifecycle_dropdown_action";
import {onlineSvg, offlineSvg, exclamationSvg, exportSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";
import dayjs from "dayjs";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import StageAction from "./stage_action";
import LogAction from "./log_action";
import ConfigAction from "./config_action";
import ConfigurationAction from "./configuration_action";

const DCSwitch = () => {
    const switchRef = useRef(null);
    const fetchIntervalRef = useRef();
    const currentUser = useSelector(state => state.user.userInfo);
    const [isForbidden, setIsForbidden] = useState(false);
    const navigate = useNavigate();
    const [sysnameModalVisible, setSysnameModalVisible] = useState(false);
    const [currentRecord, setCurrentRecord] = useState(null);
    const [form] = Form.useForm();

    const switchColumns = [
        {
            ...createColumnConfig("Sysname", "host_name", TableFilterDropdown),
            render: (text, record) => {
                if (record.reachable_status === 1) {
                    return text;
                }

                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                setSysnameModalVisible(true);
                                setCurrentRecord(record);
                                form.setFieldsValue({
                                    os: record.host_name
                                });
                            }}
                        >
                            {text}
                        </a>
                    </Space>
                );
            }
        },

        {
            ...createColumnConfig("SN/Service Tag", "sn", TableFilterDropdown),
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                navigate(`/device/switches/${record.sn}`);
                            }}
                        >
                            {record.sn}
                        </a>
                    </Space>
                );
            }
        },
        createColumnConfig("Model", "platform_model", TableFilterDropdown),
        createColumnConfig("Version", "version", TableFilterDropdown),
        import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC"
            ? createColumnConfig("Fabric", "fabric", TableFilterDropdown)
            : null,
        createColumnConfig("Status", "status", TableFilterDropdown),

        {
            ...createColumnConfig("VPN IP", "mgt_ip", TableFilterDropdown),
            render: (_, record) => {
                if (!record.mgt_ip) {
                    return null;
                }

                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.mgt_ip}
                    </Space>
                );
            }
        },

        {
            ...createColumnConfig("Mgmt IP", "link_ip_addr", TableFilterDropdown),
            render: (_, record) => {
                return record.link_ip_addr || null;
            }
        },

        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            {isForbidden ? null : <StageAction record={record} tableRef={switchRef} />}
                            <SSHAction record={record} tableRef={switchRef} />
                            <LogAction record={record} tableRef={switchRef} />
                            {isForbidden ? null : <ConfigurationAction record={record} tableRef={switchRef} />}
                            <ConfigAction record={record} isForbidden={isForbidden} tableRef={switchRef} />
                            {isForbidden ? null : <LifecycleAction record={record} tableRef={switchRef} />}
                        </Space>
                    </div>
                );
            }
        }
    ].filter(Boolean);

    const switchSearchFieldsList = ["sn", "host_name", "mgt_ip", "link_ip_addr", "platform_model"];
    const switchMatchFieldsList = [
        {name: "sn", matchMode: "fuzzy"},
        {name: "host_name", matchMode: "fuzzy"},
        {name: "link_ip_addr", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"},
        {name: "create_time", matchMode: "fuzzy"}
    ];

    useEffect(() => {
        const userType = currentUser?.type;
        if (userType === "readonly") {
            setIsForbidden(true);
        } else {
            setIsForbidden(false);
        }
    }, []);

    useEffect(() => {
        fetchIntervalRef.current = setInterval(() => {
            if (switchRef.current) {
                switchRef.current.refreshTable();
            }
        }, 60000);
        return () => {
            if (fetchIntervalRef.current) {
                clearInterval(fetchIntervalRef.current);
            }
        };
    }, []);

    const handleSave = async () => {
        try {
            const values = await form.validateFields();
            const response = await editSysname({
                sn: currentRecord.sn,
                mgt_ip: currentRecord.mgt_ip,
                new_sysname: values.ns
            });

            if (response.status === 200) {
                message.success("Sysname modified successfully.");
                setSysnameModalVisible(false);
                form.resetFields();
                switchRef.current?.refreshTable();
            } else {
                message.error(response.msg || "Failed to modify sysname.");
            }
        } catch (error) {
            if (error instanceof Error) {
                message.error(`Verification failed: ${error.message}`);
            }
        }
    };
    return (
        <div style={{minHeight: "100%"}}>
            <Card style={{display: "flex", flex: 1, minHeight: "100%"}}>
                <h2 style={{marginTop: "8px", marginBottom: "20px"}}>Switches</h2>
                <AmpConCustomTable
                    columns={switchColumns}
                    searchFieldsList={switchSearchFieldsList}
                    extraButton={<ExtraButton isForbidden={isForbidden} tableRef={switchRef} />}
                    matchFieldsList={switchMatchFieldsList}
                    fetchAPIInfo={
                        import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC"
                            ? fetchAllSwitchWithFabric
                            : fetchAllSwitch
                    }
                    ref={switchRef}
                />
                <Modal
                    className="ampcon-middle-modal"
                    title={
                        <div>
                            Edit Sysname
                            <Divider style={{marginTop: 8, marginBottom: 0}} />
                        </div>
                    }
                    open={sysnameModalVisible}
                    onCancel={() => {
                        setSysnameModalVisible(false);
                        form.resetFields();
                        setCurrentRecord(null);
                    }}
                    footer={
                        <Flex vertical>
                            <Divider style={{marginTop: 0, marginBottom: 20}} />
                            <Flex justify="flex-end">
                                <Button
                                    onClick={() => {
                                        setSysnameModalVisible(false);
                                        form.resetFields();
                                        setCurrentRecord(null);
                                    }}
                                >
                                    Cancel
                                </Button>
                                <Button type="primary" onClick={handleSave}>
                                    Apply
                                </Button>
                            </Flex>
                        </Flex>
                    }
                >
                    <Form form={form} style={{minHeight: "267.23px"}} labelAlign="left" layout="horizontal">
                        <Form.Item name="os" label="Origin Sysname" labelCol={{style: {marginRight: "32px"}}}>
                            <Input style={{width: "280px"}} disabled />
                        </Form.Item>
                        <Form.Item
                            name="ns"
                            label="New Sysname"
                            labelCol={{style: {marginRight: "32px"}}}
                            rules={[
                                {required: true, message: "Please input Sysname!"},
                                {
                                    max: 63,
                                    message: "Max length: 63 characters!"
                                },
                                {
                                    pattern: /^[\dA-Za-z-]+$/,
                                    message: "Only letters, numbers, and hyphens (-) are allowed!"
                                }
                            ]}
                        >
                            <Input style={{width: "280px"}} />
                        </Form.Item>
                    </Form>
                </Modal>
            </Card>
        </div>
    );
};

const ExtraButton = ({isForbidden, tableRef}) => {
    const navigate = useNavigate();
    return isForbidden ? null : (
        <Space size="middle">
            <ImportDropdownAction tableRef={tableRef} />
            <LifecycletDropdownAction tableRef={tableRef} />
            <ParkingSwicthButton />
            {/* <Button onClick={() => navigate("/service/snmp_view")}>Snmp View</Button> */}
        </Space>
    );
};

const ExportButton = ({tableRef}) => {
    const exportToJson = data => {
        const currentDateTime = dayjs().format("YYYY_MM_DD_HH_mm_ss");
        const filename = `switch_${currentDateTime}.json`;

        const blob = new Blob([data], {type: "text/json"});
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.setAttribute("href", url);
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };
    const exportmsg = () => {
        // console.log(tableRef.current.getTableRef().current.getTableData());
        const {length} = tableRef.current.getTableRef().current.getTableData();
        if (length >= 1) {
            exportToJson(JSON.stringify(tableRef.current.getTableRef().current.getTableData()));
            message.success("Success!");
        } else {
            message.error("There is no data in the table.");
        }
    };
    return (
        <Button onClick={() => exportmsg()} style={{display: "flex", alignItems: "center"}}>
            <Icon component={exportSvg} />
            Export
        </Button>
    );
};
const ParkingSwicthButton = () => {
    const navigate = useNavigate();
    const tableRef = useRef(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const searchFieldsList = ["sn", "ip", "hardware_id", "model", "investigate"];
    const matchFieldsList = [
        {name: "sn", matchMode: "fuzzy"},
        {name: "ip", matchMode: "fuzzy"},
        {name: "model", matchMode: "fuzzy"},
        {name: "hardware_id", matchMode: "fuzzy"},
        {name: "register_count", matchMode: "fuzzy"},
        {name: "create_time", matchMode: "fuzzy"},
        {name: "last_register", matchMode: "fuzzy"}
    ];
    const columns = [
        createColumnConfig("SN", "sn", TableFilterDropdown),
        createColumnConfig("Hardware ID", "hardware_id", TableFilterDropdown),
        createColumnConfig("IP Address", "ip", TableFilterDropdown),
        createColumnConfig("Model", "model", TableFilterDropdown),
        createColumnConfig("Register Count", "register_count", TableFilterDropdown),
        createColumnConfig("Time In", "create_time", TableFilterDropdown),
        createColumnConfig("Latest Time", "last_register", TableFilterDropdown),
        {
            ...createColumnConfig("Flag", "investigate", TableFilterDropdown),
            render: investigate => {
                if (investigate) {
                    investigate = "I";
                } else {
                    investigate = "";
                }
                return investigate;
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    navigate("/device/device_profiles/switch_configuration", {
                                        state: {sn: record.sn, model: record.model}
                                    });
                                }}
                            >
                                Create Config
                            </a>
                            <a
                                onClick={() => {
                                    investigateSwitch(record.sn).then(res => {
                                        if (res.status === 200) {
                                            message.success(res.info);
                                            tableRef.current.getTableRef().current.refreshTable();
                                        } else {
                                            message.error("Failed to investigate parking switch");
                                        }
                                    });
                                }}
                            >
                                Investigated
                            </a>
                            <a
                                onClick={() => {
                                    confirmModalAction("Are you sure want to delete?", () => {
                                        delParkingSwitch(record.sn).then(res => {
                                            if (res.status === 200) {
                                                message.success(res.info);
                                                tableRef.current.getTableRef().current.refreshTable();
                                            } else {
                                                message.error("Failed to delete parking switch");
                                            }
                                        });
                                    });
                                }}
                            >
                                Remove
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];
    return (
        <div>
            <Button
                htmlType="button"
                onClick={() => {
                    setIsModalOpen(true);
                }}
            >
                Parking Lot
            </Button>
            <AmpConCustomModalTable
                ref={tableRef}
                title="Parking Lot"
                selectModalOpen={isModalOpen}
                onCancel={() => {
                    setIsModalOpen(false);
                }}
                columns={columns}
                matchFieldsList={matchFieldsList}
                searchFieldsList={searchFieldsList}
                buttonProps={[<ExportButton tableRef={tableRef} />]}
                fetchAPIInfo={fetchParkingSwitch}
                modalClass="ampcon-max-modal"
            />
        </div>
    );
};

export default DCSwitch;
