import {But<PERSON>, Col, Flex, Form, Input, message, Row, Select, Space, Spin, Tooltip} from "antd";
import React, {useState, useEffect} from "react";
import {getPlatformModel} from "@/modules-ampcon/apis/template_api";
import {getModelConfigInfo, getImageInfo, createModel, updateSwitchModel} from "@/modules-ampcon/apis/config_api";
import SwitchModelSelector from "@/modules-ampcon/components/switch_model_selector";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import switchModelStyle from "./switch_model.module.scss";

const selectSwitchModelTooltip = "Select Switch Model name from the drop down list.";
const gePortToolTip = "Enter the values of the GE port range specific to your switch model";
const tePortToolTip = "Enter the values of the TE port range specific to your switch model";
const qePortToolTip = "Enter the values of the QE port range specific to your switch model";
const xePortToolTip = "Enter the values of the XE port range specific to your switch model";
const saveButtonTooltip = "Click Save to Configure Switch Model.";
const resetButtonTooltip = "Click Reset to set Value default.";

const SwitchModel = () => {
    const [form] = Form.useForm();
    const [modelOptions, setModelOptions] = useState([]);
    const [imageOptions, setImageOptions] = useState([]);
    const [defaultModelValues, setDefaultModelValues] = useState({});
    const [spinning, setSpinning] = useState(false);
    const [selectedModel, setSelectedModel] = useState("");
    const fetchModelOptions = async () => {
        const modelResponse = await getPlatformModel();
        if (modelResponse.status !== 200) {
            message.error("Fetch platform model failed");
        }
        //  else {
        //     for (const key in modelResponse.data) {
        //         if (Object.prototype.hasOwnProperty.call(modelResponse.data, key)) {
        //             const options = modelResponse.data[key].map(item => {
        //                 return {value: item};
        //             });
        //             const modelOption = {label: key, options};
        //             modelOptionsList.push(modelOption);
        //         }
        //     }
        // }
        setModelOptions(modelResponse.data);
        const defaultModel = modelResponse.data.as4610[0];
        // model: modelOptionsList[0]?.options[0] ? modelOptionsList[0].options[0].label : ""
        setSelectedModel(modelResponse.data.as4610[0]);
        form.setFieldsValue(defaultModel);

        onSelectedModelChange(defaultModel);
    };

    const onSelectedModelChange = async value => {
        const modelValues = {};
        const defaultValues = {};
        modelValues.name = value;
        defaultValues.name = value;

        const configRequest = getModelConfigInfo(value);
        const imageRequest = getImageInfo(value);
        const [configResponse, imageResponse] = await Promise.all([configRequest, imageRequest]);
        // const configResponse = await getModelConfigInfo(value);
        if (configResponse.status !== 200) {
            message.error("Fetch platform model failed");
        } else {
            modelValues.geStart = configResponse.data.ge_start ? configResponse.data.ge_start : 0;
            modelValues.geEnd = configResponse.data.ge_end ? configResponse.data.ge_end : 0;
            modelValues.teStart = configResponse.data.te_start ? configResponse.data.te_start : 0;
            modelValues.teEnd = configResponse.data.te_end ? configResponse.data.te_end : 0;
            modelValues.qeStart = configResponse.data.qe_start ? configResponse.data.qe_start : 0;
            modelValues.qeEnd = configResponse.data.qe_end ? configResponse.data.qe_end : 0;
            modelValues.xeStart = configResponse.data.xe_start ? configResponse.data.xe_start : 0;
            modelValues.xeEnd = configResponse.data.xe_end ? configResponse.data.xe_end : 0;
        }
        // const imageResponse = await getImageInfo(value);
        if (imageResponse.status !== 200) {
            message.error("Fetch image failed");
        } else {
            const imageOptionsList = [];
            // console.log(imageResponse.data.options);
            for (const key in imageResponse.data.options) {
                if (Object.prototype.hasOwnProperty.call(imageResponse.data.options, key)) {
                    const options = imageResponse.data.options[key].map(item => {
                        return {label: item.replace(/^img\//, ""), value: item};
                    });
                    const modelOption = {label: key, title: key, options};
                    imageOptionsList.push(modelOption);
                }
            }
            if (imageOptionsList.length === 0) {
                const options = {
                    label: configResponse.data.up_to_date_onie_path.replace(/^img\//, ""),
                    value: configResponse.data.up_to_date_onie_path
                };
                imageOptionsList.push(options);
            }
            setImageOptions(imageOptionsList);
            modelValues.oniePath = configResponse.data.up_to_date_onie_path;
            defaultValues.oniePath = configResponse.data.up_to_date_onie_path;
            defaultValues.geStart = 0;
            defaultValues.geEnd = 0;
            defaultValues.teStart = 0;
            defaultValues.teEnd = 0;
            defaultValues.qeStart = 0;
            defaultValues.qeEnd = 0;
            defaultValues.xeStart = 0;
            defaultValues.xeEnd = 0;
        }
        setDefaultModelValues(defaultValues);
        form.setFieldsValue(modelValues);
    };

    const handleCreateModel = async () => {
        setSpinning(true);
        try {
            const response = await createModel(form.getFieldsValue());
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                message.success(response.info);
            }
            setSpinning(false);
        } catch (e) {
            message.error("An error occurred during the process of create");
        } finally {
            setSpinning(false);
        }
    };

    useEffect(() => {
        fetchModelOptions().then();
    }, []);

    return (
        <Flex gap="large" vertical style={{flex: 1}} className={switchModelStyle.tile}>
            {/* <h2 style={{margin: 0}}>Switch Model</h2> */}
            <div style={{margin: "-12px 0px 20px 0px"}}>
                <Form
                    form={form}
                    labelCol={{style: {width: 200}}}
                    // wrapperCol={{span: 18}}
                    labelAlign="left"
                    onFinish={handleCreateModel}
                >
                    <Form.Item label="Switch Model" name="name" tooltip={selectSwitchModelTooltip}>
                        <SwitchModelSelector
                            placeholder="Select model"
                            handleChange={value => {
                                setSelectedModel(value);
                                onSelectedModelChange(value);
                            }}
                            data={modelOptions}
                            selectedModel={selectedModel}
                            style={{width: "280px"}}
                        />
                    </Form.Item>
                    <Form.Item label="GE Port Num Range" tooltip={gePortToolTip}>
                        <Row>
                            <Col>
                                <Form.Item name="geStart" noStyle>
                                    <Input style={{width: 132}} />
                                </Form.Item>
                            </Col>
                            <Col align="center">
                                <div style={{width: 16}}>-</div>
                            </Col>
                            <Col>
                                <Form.Item name="geEnd" noStyle>
                                    <Input style={{width: 132}} />
                                </Form.Item>
                            </Col>
                        </Row>
                    </Form.Item>
                    <Form.Item label="TE Port Num Range" tooltip={tePortToolTip}>
                        <Row>
                            <Col>
                                <Form.Item name="teStart" noStyle>
                                    <Input style={{width: 132}} />
                                </Form.Item>
                            </Col>
                            <Col align="center">
                                <div style={{width: 16}}>-</div>
                            </Col>
                            <Col>
                                <Form.Item name="teEnd" noStyle>
                                    <Input style={{width: 132}} />
                                </Form.Item>
                            </Col>
                        </Row>
                    </Form.Item>
                    <Form.Item label="QE Port Num Range" tooltip={qePortToolTip}>
                        <Row>
                            <Col>
                                <Form.Item name="qeStart" noStyle>
                                    <Input style={{width: 132}} />
                                </Form.Item>
                            </Col>
                            <Col align="center">
                                <div style={{width: 16}}>-</div>
                            </Col>
                            <Col>
                                <Form.Item name="qeEnd" noStyle>
                                    <Input style={{width: 132}} />
                                </Form.Item>
                            </Col>
                        </Row>
                    </Form.Item>
                    <Form.Item label="XE Port Num Range" tooltip={xePortToolTip}>
                        <Row>
                            <Col>
                                <Form.Item name="xeStart" noStyle>
                                    <Input style={{width: 132}} />
                                </Form.Item>
                            </Col>
                            <Col align="center">
                                <div style={{width: 16}}>-</div>
                            </Col>
                            <Col>
                                <Form.Item name="xeEnd" noStyle>
                                    <Input style={{width: 132}} />
                                </Form.Item>
                            </Col>
                        </Row>
                    </Form.Item>
                    <Form.Item label="Deployed ONIE Image" name="oniePath">
                        <Select options={imageOptions} style={{width: 280}} />
                    </Form.Item>
                </Form>
                <Flex gap="32px" vertical>
                    <Tooltip title="" placement="topRight">
                        <Button
                            type="primary"
                            htmlType="button"
                            style={{marginLeft: "200px", width: 216}}
                            onClick={() => {
                                confirmModalAction("Are you sure you want to update the switch model ?", async () => {
                                    setSpinning(true);
                                    try {
                                        const response = await updateSwitchModel();
                                        if (response.status !== 200) {
                                            message.error(response.info);
                                        } else {
                                            message.success(response.info);
                                        }
                                        setSpinning(false);
                                    } catch (e) {
                                        message.error("An error occurred during the process of update");
                                    } finally {
                                        setSpinning(false);
                                    }
                                });
                            }}
                        >
                            Update Switch Model
                        </Button>
                    </Tooltip>
                    <Space style={{marginLeft: "200px"}}>
                        <Tooltip title={saveButtonTooltip} placement="topRight">
                            <Button
                                type="primary"
                                htmlType="button"
                                style={{marginRight: "8px", width: 100}}
                                onClick={() => {
                                    form.submit();
                                }}
                            >
                                Apply
                            </Button>
                        </Tooltip>
                        <Tooltip title={resetButtonTooltip} placement="topRight">
                            <Button
                                htmlType="button"
                                style={{width: 100}}
                                onClick={() => {
                                    form.setFieldsValue(defaultModelValues);
                                }}
                            >
                                Reset
                            </Button>
                        </Tooltip>
                    </Space>
                </Flex>
            </div>
            <Spin spinning={spinning} tip="Loading..." size="large" fullscreen />
        </Flex>
    );
};
export default SwitchModel;
