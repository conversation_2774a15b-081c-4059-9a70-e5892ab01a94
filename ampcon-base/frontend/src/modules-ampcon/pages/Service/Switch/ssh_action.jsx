import {useState, useRef} from "react";
import {AmpConCustomModalForm} from "@/modules-ampcon/components/custom_table";
import {useForm} from "antd/es/form/Form";
import {Form, Input, Radio} from "antd";
import {EyeOutlined, EyeInvisibleOutlined} from "@ant-design/icons";
import PrivkeyFileUploader from "@/modules-ampcon/pages/Maintain/CliConfig/priv_key_file_upload";

const SSHAction = ({viewType, record, type}) => {
    const [sshModal, setSSHModal] = useState(false);
    const [sshForm] = useForm();
    const isCampusModule = import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-CAMPUS";
    const viewTypeState = viewType === "vpnView";
    const [authMethod, setAuthMethod] = useState(isCampusModule ? "password" : null);

    const sshSubmit = values => {
        const password = authMethod === "password" ? values.password : "";
        const key = authMethod === "key" ? values.privkey : "";
        const passphrase = authMethod === "key" ? values.passphrase : "";

        const encoded = btoa(`${record.mgt_ip};22;${values.username};${password};${key};${passphrase};`);
        window.open(`/ssh/?${encoded}`, "_blank", "noopener,noreferrer");

        setSSHModal(false);
        sshForm.resetFields();
    };

    const handleKeyFileRead = (privateKey, fileName) => {
        sshForm.setFieldsValue({privkey: privateKey});
        sshForm.setFieldsValue({filename: fileName});
    };

    const sshFormRender = () => {
        return (
            <>
                {isCampusModule && viewTypeState && (
                    <Form.Item name="authMethod" label="Select Authentication" initialValue="password">
                        <Radio.Group onChange={e => setAuthMethod(e.target.value)} value={authMethod}>
                            <Radio value="password">Password</Radio>
                            <Radio value="key">SSH Key</Radio>
                        </Radio.Group>
                    </Form.Item>
                )}

                <Form.Item
                    name="username"
                    label="Username"
                    rules={[
                        {required: true, message: "Please input username."},
                        {max: 32, message: "Username cannot be longer than 32 characters!"},
                        { pattern: /^[^\u4e00-\u9fa5]*$/, message: "Username is not in a valid format." }
                    ]}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>

                {(!isCampusModule || !viewTypeState || authMethod === "password") && (
                    <Form.Item
                        name="password"
                        label="Password"
                        rules={[{required: true, message: "Please input password."}]}
                    >
                        <Input.Password
                            iconRender={visible =>
                                visible ? (
                                    <EyeOutlined style={{color: "#c5c5c5"}} />
                                ) : (
                                    <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                )
                            }
                            style={{width: "280px"}}
                        />
                    </Form.Item>
                )}

                {isCampusModule && viewTypeState && authMethod === "key" && (
                    <div style={{display: "flex", gap: "70px"}}>
                        <Form.Item name="privkey" style={{display: "none"}}>
                            <Input type="hidden" />
                        </Form.Item>
                        <Form.Item
                            label="Key"
                            name="filename"
                            className="ssh-action-key-label"
                            style={{margin: 0}}
                            rules={[{required: true, message: "This field is required."}]}
                        >
                            <Input
                                placeholder="SSH Key"
                                style={{width: "280px", marginLeft: "62px", color: "#1F1F1F"}}
                                disabled
                            />
                        </Form.Item>
                        <PrivkeyFileUploader onFileRead={handleKeyFileRead} />
                    </div>
                )}

                {isCampusModule && viewTypeState && authMethod === "key" && (
                    <Form.Item name="passphrase" label="Passphrase" style={{marginTop: "20px"}}>
                        <Input.Password
                            iconRender={visible =>
                                visible ? (
                                    <EyeOutlined style={{color: "#c5c5c5"}} />
                                ) : (
                                    <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                )
                            }
                            style={{width: "280px"}}
                        />
                    </Form.Item>
                )}
            </>
        );
    };

    return (
        <>
            {(type === "fsos" || record.status === "Provisioning Success" || record.status === "Imported") && (
                <a
                    onClick={() => {
                        setSSHModal(true);
                        setAuthMethod("password");
                    }}
                >
                    SSH
                </a>
            )}
            <AmpConCustomModalForm
                title="SSH"
                isModalOpen={sshModal}
                formInstance={sshForm}
                layoutProps={{
                    labelCol: {
                        span: 6
                    }
                }}
                CustomFormItems={sshFormRender}
                onCancel={() => {
                    sshForm.resetFields();
                    setSSHModal(false);
                }}
                onSubmit={sshSubmit}
                modalClass="ampcon-middle-modal"
            />
        </>
    );
};

export default SSHAction;
