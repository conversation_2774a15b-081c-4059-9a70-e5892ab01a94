import {Space, Modal, message, Divider, Card, Input} from "antd";
import React, {useState, useEffect, useRef} from "react";
import {useSelector} from "react-redux";
import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";

import {
    fetchSwitchConfig,
    getConfig,
    getConfigAttach,
    delConfig,
    editConfigFile
} from "@/modules-ampcon/apis/config_api";
import ConfigContentTextareaModal from "@/modules-ampcon/components/config_content_textarea_modal";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const ConfigFileView = () => {
    const tableRef = useRef(null);
    const [isAssociatModalOpen, setIsAssociatModalOpen] = useState(false);
    const [isShowConfigModal, setIsShowConfigModal] = useState(false);
    const [selectedrecord, setSelectedRecord] = useState({});
    const [configContent, setConfigContent] = useState("");
    const currentUser = useSelector(state => state.user.userInfo);
    const [isForbidden, setIsForbidden] = useState(false);

    const configColumns = [
        createColumnConfig("Config File Name", "name", TableFilterDropdown),
        createColumnConfig("Model", "system_model", TableFilterDropdown),
        createColumnConfig("Type", "type", TableFilterDropdown),
        createColumnConfig("Last Update Time", "modified_time", TableFilterDropdown, "", "", "descend"),
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={async () => {
                                    getConfig(record.id).then(response => {
                                        setConfigContent(response);
                                    });
                                    setIsShowConfigModal(true);
                                    setSelectedRecord(record);
                                }}
                            >
                                View
                            </a>
                            <a
                                onClick={() => {
                                    setIsAssociatModalOpen(true);
                                    setSelectedRecord(record);
                                }}
                            >
                                Associated
                            </a>
                            {isForbidden ? null : (
                                <a
                                    onClick={() => {
                                        confirmModalAction("Are you sure want to delete?", async () => {
                                            const response = await delConfig(record.name);
                                            if (response.status !== 200) {
                                                message.error(response.info);
                                            } else {
                                                message.success(response.info);
                                                tableRef.current.refreshTable();
                                            }
                                        });
                                    }}
                                >
                                    Delete
                                </a>
                            )}
                        </Space>
                    </div>
                );
            }
        }
    ];

    const configSearchFieldsList = ["name", "system_model", "type", "modified_time"];
    const configMatchFieldsList = [
        {name: "name", matchMode: "fuzzy"},
        {name: "system_model", matchMode: "fuzzy"},
        {name: "type", matchMode: "fuzzy"},
        {name: "modified_time", matchMode: "fuzzy"}
    ];

    useEffect(() => {
        const userType = currentUser?.type;
        if (userType === "readonly") {
            setIsForbidden(true);
        } else {
            setIsForbidden(false);
        }
    }, []);

    return (
        <Card style={{display: "flex", flexDirection: "column", flex: 1, border: "none"}}>
            {/* <h2 style={{marginBottom: "-25px", marginTop: "0px"}}>Config File View</h2> */}
            <AmpConCustomTable
                columns={configColumns}
                searchFieldsList={configSearchFieldsList}
                matchFieldsList={configMatchFieldsList}
                fetchAPIInfo={fetchSwitchConfig}
                ref={tableRef}
            />
            <AssociatedModal
                isModalOpen={isAssociatModalOpen}
                onCancel={() => {
                    setIsAssociatModalOpen(false);
                }}
                record={selectedrecord}
            />
            <ConfigContentTextareaModal
                title={selectedrecord.name}
                content={configContent}
                saveCallback={async () => {
                    try {
                        const response = await editConfigFile(selectedrecord.name, configContent);
                        if (response.status !== 200) {
                            message.error(response.info);
                        } else {
                            setIsShowConfigModal(false);
                            message.success(response.info);
                        }
                    } catch (error) {
                        message.error("You don't have permission to access this action");
                    }
                }}
                handleFileContentChange={e => {
                    setConfigContent(e.target.value);
                }}
                handleCancel={() => {
                    setIsShowConfigModal(false);
                }}
                isOpen={isShowConfigModal}
            />
        </Card>
    );
};

const AssociatedModal = ({isModalOpen, onCancel, record}) => {
    const [model, setModel] = useState("");
    const [sn, setSn] = useState("");
    useEffect(() => {
        if (record.id) {
            getConfigAttach(record.id).then(response => {
                if (response.status === 200) {
                    const newModel = response.data.map(item => item.platform_model).join("  ");
                    const newSN = response.data.map(item => item.sn).join("  ");
                    setModel(newModel);
                    setSn(newSN);
                }
            });
        }
    }, [record]);

    const readonlyStyle = {
        minHeight: "350px",
        resize: "vertical",
        border: "none",
        backgroundColor: "#F8FAFB",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        marginBottom: "-16px"
    };

    return (
        <Modal
            title={
                <div>
                    {`Associated Switch ${record.name}`}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            onCancel={onCancel}
            footer={null}
            className="ampcon-middle-modal"
        >
            <Input.TextArea value={`Switch Serial: \t ${sn} \nModel: \t ${model} \n`} readOnly style={readonlyStyle} />
        </Modal>
    );
};

export default ConfigFileView;
