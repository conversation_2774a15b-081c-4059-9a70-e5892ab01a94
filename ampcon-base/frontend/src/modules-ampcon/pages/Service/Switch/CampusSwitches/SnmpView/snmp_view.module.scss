.pendingStyle {
  background-color: #F4F5F7;
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #DADCE1;
  color: #929A9E;;
  font-size: 14px;
}

.runningStyle {
  background-color: rgba(255, 187, 0, 0.1);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #FFBB00;
  font-size: 14px;
  color: #FFBB00;
}

.successStyle {
  background-color: rgba(43, 193, 116, 0.1);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #2BC174;
  color: #2BC174;
  font-size: 14px;
}

.failedStyle {
  background-color: rgba(245, 63, 63, 0.1);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #F53F3F;
  color: #F53F3F;
  font-size: 14px;
}


.deviceStatus {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 2px;
  font-size: 14px;
}

.pending {
  background-color: #F4F5F7;
  border-radius: 2px;
  border: 1px solid #DADCE1;
  color: #929A9E;
}

.running {
  background-color: rgba(255, 187, 0, 0.1);
  border-radius: 2px;
  border: 1px solid #FFBB00;
  color: #FFBB00;
}

.success {
  background-color: rgba(43, 193, 116, 0.1);
  border-radius: 2px;
  border: 1px solid #2BC174;
  color: #2BC174;
}

.failed {
  background-color: rgba(245, 63, 63, 0.1);
  border-radius: 2px;
  border: 1px solid #F53F3F;
  color: #F53F3F;
}
