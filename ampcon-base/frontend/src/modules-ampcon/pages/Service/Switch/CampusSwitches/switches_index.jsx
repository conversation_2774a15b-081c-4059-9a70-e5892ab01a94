import React, {useEffect, useState} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import VpnView from "@/modules-ampcon/pages/Service/Switch/CampusSwitches/VpnView/vpn_view";
import SnmpView from "@/modules-ampcon/pages/Service/Switch/CampusSwitches/SnmpView/snmp_view";
import {useSelector} from "react-redux";
import {isRouteForbidden} from "@/modules-ampcon/utils/util";
import ForbiddenPage from "@/modules-ampcon/pages/ForbiddenPage";

const SwitchesIndex = () => {
    const currentUser = useSelector(state => state.user.userInfo);
    const userType = currentUser?.type;
    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();

    const allItems = [
        {
            key: "picos",
            label: "PicOS",
            children: <VpnView />
        },
        {
            key: "fsos",
            label: "FSOS",
            children: <SnmpView />
        }
    ];

    const pathReg = /(picos|fsos)$/;

    useEffect(() => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(pathReg)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            const matchLength = currentPath.match(pathReg)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    if (isRouteForbidden(location.pathname, userType)) {
        return <ForbiddenPage />;
    }

    return (
        <div style={{display: "flex", flex: 1}}>
            <Tabs
                activeKey={currentActiveKey}
                items={allItems}
                onChange={onChange}
                style={{flex: 1}}
                destroyInactiveTabPane
            />
        </div>
    );
};

export default SwitchesIndex;
