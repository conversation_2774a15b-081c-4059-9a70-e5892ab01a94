import React, {useEffect, useState} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import GlobalConfiguration from "./GlobalConfiguration/global_configuration";
import SwitchConfiguration from "./SwitchConfiguration/switch_configuration";
import ConfigFileView from "./ConfigFileView/configfile_view";
import SwitchModel from "./SwitchModel/switch_model";
import AmpConSystemConfig from "./SystemConfig/system_config";
import ProtectedRoute, {isRouteForbidden} from "@/modules-ampcon/utils/util";
import {useSelector} from "react-redux";
import ForbiddenPage from "@/modules-ampcon/pages/ForbiddenPage";
import styles from "./device_profile_index.module.scss";
// import {isRouteForbidden} from "@/modules-ampcon/utils/util";

const DeviceProfileIndex = () => {
    const currentUser = useSelector(state => state.user.userInfo);
    const userType = currentUser?.type;
    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();

    const allItems = [
        {
            key: "global_configuration",
            label: "Global Configuration",
            children: <ProtectedRoute component={GlobalConfiguration} />
        },
        {
            key: "switch_configuration",
            label: "Switch Configuartion",
            children: <ProtectedRoute component={SwitchConfiguration} />
        },
        {
            key: "config_file_view",
            label: "Config File View",
            children: <ProtectedRoute component={ConfigFileView} />
        },
        {
            key: "switch_model",
            label: "Switch Model",
            children: <ProtectedRoute component={SwitchModel} />
        },
        {
            key: "system_configuration",
            label: "System Configuration",
            children: <ProtectedRoute component={AmpConSystemConfig} />
        }
    ];

    let pathReg;
    let items = [];

    if (currentUser.type === "readonly") {
        items = allItems.filter(item => item.key === "config_file_view");
        pathReg = /(config_file_view)$/;
    } else if (currentUser.type === "admin" || currentUser.type === "superadmin") {
        items = allItems.filter(item =>
            ["global_configuration", "switch_configuration", "config_file_view"].includes(item.key)
        );
        pathReg = /(global_configuration|switch_configuration|config_file_view)$/;
    } else {
        // 默认访问所有项
        items = allItems;
        pathReg = /(global_configuration|switch_configuration|config_file_view|switch_model|system_configuration)$/;
    }

    useEffect(() => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(pathReg)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            const matchLength = currentPath.match(pathReg)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    if (isRouteForbidden(location.pathname, userType)) {
        return <ForbiddenPage />;
    }

    return (
        <div className={styles.deviceProfileTabPageDC} style={{display: "flex", flex: 1}}>
            <Tabs
                activeKey={currentActiveKey}
                items={items}
                onChange={onChange}
                style={{flex: 1}}
                destroyInactiveTabPane
            />
        </div>
    );
};

export default DeviceProfileIndex;
