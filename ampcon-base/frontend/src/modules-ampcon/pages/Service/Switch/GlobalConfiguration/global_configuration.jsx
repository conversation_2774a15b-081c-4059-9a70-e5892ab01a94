import {Button, Flex, Form, Input, message, Space, Tooltip, Tree} from "antd";
import React, {useEffect, useState} from "react";
import SwitchModelSelector from "@/modules-ampcon/components/switch_model_selector";
import {
    checkGlobalFile,
    generateGlobalConfig,
    queryAllPlatforms,
    queryConfigFileContent,
    queryDeployConfigsTree,
    saveGenerateConfig
} from "@/modules-ampcon/apis/config_api";
import Icon, {EyeOutlined, FileOutlined, FolderOutlined} from "@ant-design/icons";
import ConfigContentTextarea from "@/modules-ampcon/components/config_content_textarea";
import ConfigFilePreview from "@/modules-ampcon/components/config_file_preveiw";
import {searchSvg} from "@/utils/common/iconSvg";
import globalConfigurationStyle from "./global_configuration.module.scss";

const get_data = () => {
    const myDate = new Date();
    return `${myDate.getFullYear()}-${myDate.getMonth() + 1}-${myDate.getDate()}-`;
};

const GlobalConfiguration = () => {
    const selectSwitchModelTooltip = "Select your Switch Model from the drop down list.";
    const globalConfigNameTooltip = "Enter a name for the Global Configuration file.";
    const genericGlobalFileTooltip =
        "Create a text file called GlobalConfig.txt and add PICOS configuration commands in the set command format that are common among all your PICOS switches. Click the Browse button in the Generic Global File field and upload the newly created GlobalConfig.txt file.";
    const securityGlobalFileTooltip =
        "Create a text file called SecurityGlobalFile.txt and add PICOS Security-related configuration commands in the set command format that are common among all your PICOS switches. Click the Browse button in Security Global File field and upload the newly created SecurityGlobalFile.txt file.";
    const generateButtonTooltip =
        "Click Generate and then review the configuration. This includes both Global and Security PICOS configuration commands that are common among all the switches.";
    const resetButtonTooltip = "Click Reset for default value.";
    const editConfigButtonTooltip = "Click Edit for Edit in Generate ConfigTemplate.";
    const saveConfigButtonToolTip = "Click Save, to save new Global Config file.";
    const cancelEditConfigButtonTooltip = "Click to cancel editing";
    const configCheckTitle = "Admin Global Config Preview";
    const fileCheckTitle = "File Check";

    // Used for search tree input box to achieve anti-shake
    let timeoutId = null;

    const [form] = Form.useForm();
    const [isDisabled, setIsDisabled] = useState(true);

    const [selectedModel, setSelectedModel] = useState("");
    const [platforms, setPlatforms] = useState([]);
    const [isGenericGlobalConfigValid, setIsGenericGlobalConfigValid] = useState(true);
    const [isSecurityGlobalConfigValid, setIsSecurityGlobalConfigValid] = useState(true);
    let treeDataSearchArgs = null;
    // for tree building
    const [treeDataSearchInputValue, setTreeDataSearchInputValue] = useState("");
    const [treeData, setTreeData] = useState([]);
    const [, setSelectedKeys] = useState({});
    const [, setExpandedKeys] = useState([]);

    const [globalConfigName, setGlobalConfigName] = useState(null);
    const [switchModel, setSwitchModel] = useState(null);
    const [isShowFile, setIsShowFile] = useState(false);
    const [isFileCheck, setIsFileCheck] = useState(false);
    const [fileContent, setFileContent] = useState("");

    // upload file
    const [genericFile, setGenericFile] = useState(null);
    const [securityFile, setSecurityFile] = useState(null);

    const [treeHeight, setTreeHeight] = useState(0);

    // check isClickTree
    const [isClickTree, setIsClickTree] = useState(false);

    const calculateTreeHeight = () => {
        setTreeHeight(210 + (window.innerHeight - 916) * 0.603 < 200 ? 200 : 210 + (window.innerHeight - 916) * 0.603);
    };

    useEffect(() => {
        const fetchData = async () => {
            const platformResponse = await queryAllPlatforms();
            setPlatforms(platformResponse.data);
            setSelectedModel(platformResponse.data.x86[0]);
            await refreshTree();
        };
        fetchData().then(() => {});
        calculateTreeHeight();
        window.addEventListener("resize", calculateTreeHeight);
        return () => {
            window.removeEventListener("resize", calculateTreeHeight);
        };
    }, []);

    const refreshTree = async () => {
        const globalConfigTreeResponse = await queryDeployConfigsTree("global", 1, 100, treeDataSearchArgs);
        const treeTempData = updateNodesWithIcons(globalConfigTreeResponse);
        setTreeData(treeTempData);
        const keys = treeTempData.reduce(
            (acc, node) => [...acc, node.key, ...(node.children ? node.children.map(child => child.key) : [])],
            []
        );
        setExpandedKeys(keys);
    };

    const refreshFileCheck = () => {
        setIsShowFile(false);
        setIsFileCheck(false);
        setFileContent("");
    };

    const glob_name_val = () => {
        return `${get_data()}glob-${selectedModel}-`;
    };

    const glob_name_component = () => {
        const result = `${get_data()}glob-${selectedModel}-`;
        const truncated = result.length > 20 ? `${result.slice(0, 20)}...` : result;

        return (
            <Tooltip title={result}>
                <span>{truncated}</span>
            </Tooltip>
        );
    };

    function updateNodesWithIcons(nodes) {
        return nodes.map(node => {
            const updatedNode = {...node, icon: node.is_folder ? <FolderOutlined /> : <FileOutlined />};
            if (node.children && node.children.length > 0) {
                updatedNode.children = updateNodesWithIcons(node.children);
            }
            return updatedNode;
        });
    }

    const onSelectTreeNodeCallback = (selectedKeys, info) => {
        setSelectedKeys({
            name: info.node.key,
            model: info.node.model
        });
        setGlobalConfigName(info.node.key);
        setSwitchModel(info.node.model);
        if (info.node.is_folder) {
            setIsShowFile(false);
            setIsFileCheck(false);
        } else {
            setIsClickTree(true);
            refreshFileCheck();
            queryConfigFileContent(info.node.key).then(response => {
                if (response.status !== 200) {
                    message.error("Error fetching file content");
                } else {
                    setIsShowFile(true);
                    setIsFileCheck(false);
                    setFileContent(response.data);
                }
            });
        }
    };

    const fileCheckSaveCallback = async () => {
        if (!isClickTree) {
            try {
                await form.validateFields();
            } catch (errorInfo) {
                message.error("Please check the form fields.");
                return;
            }
        }
        saveGenerateConfig(globalConfigName, fileContent, switchModel, "global").then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                saveSuccessInitPageCallback().then(() => {
                    message.success(response.info);
                });
            }
        });
    };

    const handleFileContentChange = e => {
        setFileContent(e.target.value);
    };

    const saveSuccessInitPageCallback = async () => {
        await refreshTree();
        setIsShowFile(false);
        setIsFileCheck(false);
        setTreeDataSearchInputValue("");
        formResetCallback();
    };

    const genericGlobalFilePreviewCallback = async () => {
        setIsShowFile(false);
        setIsFileCheck(false);
        checkGlobalFile(selectedModel, "generic").then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                setIsShowFile(true);
                setIsFileCheck(true);
                setFileContent(response.data);
            }
        });
    };

    const securityGlobalFilePreviewCallback = async () => {
        setIsShowFile(false);
        setIsFileCheck(false);
        checkGlobalFile(selectedModel, "security").then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                setIsShowFile(true);
                setIsFileCheck(true);
                setFileContent(response.data);
            }
        });
    };

    const isGenericGlobalFileValid = file => {
        if (!file) {
            return true;
        }
        setIsGenericGlobalConfigValid(true);
        const reader = new FileReader();
        reader.onload = e => {
            const content = e.target.result;
            const lines = content.split(/\r?\n/);
            setIsGenericGlobalConfigValid(
                lines.every(line => {
                    return !(
                        line.trim() !== "" &&
                        line.trim().substring(0, 6).indexOf("set") < 0 &&
                        line.trim().substring(0, 6).indexOf("delete") < 0
                    );
                })
            );
        };
        reader.readAsText(file);
    };

    const isSecurityGlobalFileValid = file => {
        if (!file) {
            return true;
        }
        setIsSecurityGlobalConfigValid(true);
        const reader = new FileReader();
        reader.onload = e => {
            const content = e.target.result;
            const lines = content.split(/\r?\n/);
            setIsSecurityGlobalConfigValid(
                lines.every(line => {
                    return !(
                        line.trim() !== "" &&
                        line.trim().substring(0, 6).indexOf("set") < 0 &&
                        line.trim().substring(0, 6).indexOf("delete") < 0
                    );
                })
            );
        };
        reader.readAsText(file);
    };

    const handleGenericGlobalFileChange = e => {
        if (e.target.files.length === 0) {
            setIsGenericGlobalConfigValid(true);
            setGenericFile(null);
            checkFormEmpty();
            return;
        }
        isGenericGlobalFileValid(e.target.files[0]);
        setGenericFile(e.target.files[0]);
    };

    const handleSecurityGlobalFileChange = e => {
        if (e.target.files.length === 0) {
            setIsSecurityGlobalConfigValid(true);
            setSecurityFile(null);
            checkFormEmpty();
            return;
        }
        isSecurityGlobalFileValid(e.target.files[0]);
        setSecurityFile(e.target.files[0]);
    };

    const checkFormEmpty = () => {
        const values = form.getFieldValue();
        setIsDisabled(!(values.globalConfigName && values.genericGlobalFile && values.securityGlobalFile));
    };

    const generateConfigCallback = async () => {
        setIsClickTree(false);
        if (isGenericGlobalConfigValid === false || isSecurityGlobalConfigValid === false) {
            message.error("Please input valid configuration file with SET format.");
            return;
        }
        const values = form.getFieldValue();
        const name = glob_name_val() + values.globalConfigName;
        setGlobalConfigName(name);
        setSwitchModel(selectedModel);
        setIsShowFile(false);
        setIsFileCheck(false);
        generateGlobalConfig(selectedModel, name, genericFile, securityFile).then(data => {
            if (data.status !== 200) {
                message.error(data.info);
            } else {
                setIsShowFile(true);
                setIsFileCheck(false);
                setFileContent(data.data);
            }
        });
    };

    const formResetCallback = () => {
        form.resetFields();
        setIsDisabled(true);
        setIsGenericGlobalConfigValid(true);
        setIsSecurityGlobalConfigValid(true);
        setSelectedModel(platforms.x86[0]);
    };

    const searchTreeInputCallback = e => {
        treeDataSearchArgs = e.target.value;
        setTreeDataSearchInputValue(e.target.value);
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(() => {
            refreshTree();
        }, 1000);
    };

    return (
        <Flex layout="horizontal" className={globalConfigurationStyle.tile} style={{minWidth: "fit-content", flex: 1}}>
            <Flex
                // gap="large"
                vertical
                style={{flex: 1, minWidth: "600px", maxWidth: "800px", width: "40%"}}
            >
                {/* <h2 style={{margin: "0", marginBottom: "20px"}}>Global Configuration</h2> */}
                <h3 style={{margin: "-12px 0px 20px 0px"}}>Admin Global Config Edit</h3>
                <div>
                    <Form
                        layout="horizontal"
                        labelAlign="left"
                        labelCol={{span: 8}}
                        wrapperCol={{span: 14}}
                        form={form}
                        onFieldsChange={checkFormEmpty}
                        style={{width: "600px", minWidth: "600px"}}
                    >
                        <Form.Item
                            label="Switch Model"
                            name="switchModel"
                            required="true"
                            tooltip={selectSwitchModelTooltip}
                            style={{maxWidth: "100%"}}
                        >
                            <SwitchModelSelector
                                placeholder="Select model"
                                handleChange={value => {
                                    setSelectedModel(value);
                                }}
                                data={platforms}
                                selectedModel={selectedModel}
                                style={{width: "280px"}}
                            />
                        </Form.Item>
                        <Form.Item
                            label="Global Config Name"
                            name="globalConfigName"
                            required="true"
                            tooltip={globalConfigNameTooltip}
                            style={{maxWidth: "100%"}}
                            rules={[
                                {required: true, message: "Please input global config name!"},
                                {
                                    max: 32,
                                    message: "Global config name must be 32 characters or fewer."
                                },
                                {
                                    pattern: /^[\s\w:-]+$/,
                                    message: "Please input valid global config name without special characters"
                                }
                            ]}
                        >
                            <Input
                                addonBefore={glob_name_component()}
                                style={{width: "280px"}}
                                placeholder="Enter Global Config Name"
                            />
                        </Form.Item>
                        <Form.Item
                            label="Generic Global File"
                            name="genericGlobalFile"
                            required="true"
                            onChange={handleGenericGlobalFileChange}
                            rules={[{required: true, message: "Please input global config name!"}]}
                            tooltip={genericGlobalFileTooltip}
                            validateStatus={isGenericGlobalConfigValid ? "success" : "error"}
                            help={
                                isGenericGlobalConfigValid
                                    ? ""
                                    : "Please input valid configuration file with SET format."
                            }
                        >
                            <Input
                                type="file"
                                aria-required="true"
                                style={{width: "280px"}}
                                suffix={<EyeOutlined onClick={genericGlobalFilePreviewCallback} />}
                            />
                        </Form.Item>
                        <Form.Item
                            label="Security Global File"
                            name="securityGlobalFile"
                            required="true"
                            onChange={handleSecurityGlobalFileChange}
                            rules={[{required: true, message: "Please input global config name!"}]}
                            tooltip={securityGlobalFileTooltip}
                            validateStatus={isSecurityGlobalConfigValid ? "success" : "error"}
                            help={
                                isSecurityGlobalConfigValid
                                    ? ""
                                    : "Please input valid configuration file with SET format."
                            }
                        >
                            <Input
                                type="file"
                                aria-required="true"
                                style={{width: "280px"}}
                                suffix={<EyeOutlined onClick={securityGlobalFilePreviewCallback} />}
                            />
                        </Form.Item>
                        <Form.Item label=" " style={{margin: 0}}>
                            <Tooltip title={generateButtonTooltip} placement="topRight">
                                <Button
                                    type="primary"
                                    htmlType="submit"
                                    style={{marginRight: "20px", minWidth: "80px"}}
                                    disabled={isDisabled}
                                    onClick={generateConfigCallback}
                                >
                                    Generate
                                </Button>
                            </Tooltip>
                            <Tooltip title={resetButtonTooltip} placement="topRight">
                                <Button htmlType="button" style={{minWidth: "80px"}} onClick={formResetCallback}>
                                    Reset
                                </Button>
                            </Tooltip>
                        </Form.Item>
                    </Form>
                </div>
                <div style={{marginTop: "50px"}} />
                <Flex vertical style={{flex: 1, width: "600px"}}>
                    <h3 style={{margin: "0", marginBottom: "20px"}}>Historical Configuration</h3>
                    <Space.Compact style={{width: "100%"}}>
                        <Input
                            placeholder="Search"
                            prefix={<Icon component={searchSvg} />}
                            allowClear
                            onChange={searchTreeInputCallback}
                            value={treeDataSearchInputValue}
                            style={{float: "right", marginBottom: "20px", width: "280px"}}
                        />
                    </Space.Compact>

                    {treeData.length !== 0 ? (
                        <Tree
                            showIcon
                            showLine
                            treeData={treeData}
                            // selectedKeys={selectedKeys.name}
                            onSelect={onSelectTreeNodeCallback}
                            // expandedKeys={expandedKeys}
                            // onExpand={onExpand}
                            // autoExpandParent
                            height={treeHeight}
                        />
                    ) : (
                        <Flex justify="center">
                            <Flex vertical>
                                <div className="imageStyle" />
                                <p style={{margin: "0 auto", color: "#9DA6B3", fontSize: "12px"}}>No Data</p>
                            </Flex>
                        </Flex>
                    )}
                </Flex>
            </Flex>
            {(() => {
                if (isShowFile) {
                    return isFileCheck ? (
                        <Flex style={{flex: 1}} className={globalConfigurationStyle.tile2}>
                            <ConfigFilePreview title={fileCheckTitle} content={fileContent} />
                        </Flex>
                    ) : (
                        <Flex style={{flex: 1}} className={globalConfigurationStyle.tile2}>
                            <ConfigContentTextarea
                                title={configCheckTitle}
                                content={fileContent}
                                saveCallback={fileCheckSaveCallback}
                                handleFileContentChange={handleFileContentChange}
                                saveConfigButtonToolTip={saveConfigButtonToolTip}
                                editConfigButtonTooltip={editConfigButtonTooltip}
                                cancelEditConfigButtonTooltip={cancelEditConfigButtonTooltip}
                            />
                        </Flex>
                    );
                }
                return null;
            })()}
        </Flex>
    );
};

export default GlobalConfiguration;
