import TemplateEditor from "@/modules-ampcon/components/template_editor";
import {<PERSON><PERSON>, <PERSON>, Divider, Flex, Form, Input, Select, Space, Tree, message, Spin} from "antd";
import {useEffect, useRef, useState} from "react";
import {FileAddOutlined, FolderOutlined} from "@ant-design/icons";
import SwitchModelSelector from "@/modules-ampcon/components/switch_model_selector";
import {
    getPlatformModel,
    getPlatformVersion,
    getCliTrees,
    createTemplate,
    updateCliTree
} from "@/modules-ampcon/apis/template_api";
import Icon from "@ant-design/icons/lib/components/Icon";
import {searchSvg, updateSvg, saveSvg} from "@/utils/common/iconSvg";
import NewTemplateStyle from "./new_template.module.scss";

const NewTemplate = () => {
    const templateEditorRef = useRef();
    const [treeNodes, setTreeNodes] = useState([]);
    const [currTreeNodes, setCurrTreeNodes] = useState([]);
    const [expandedNodes, setExpandedNodes] = useState([]);
    const [modelOptions, setModelOptions] = useState([]);
    const [versionOptions, setVersionOptions] = useState([]);
    const [form] = Form.useForm();
    const [spinning, setSpinning] = useState(false);
    const [selectedModel, setSelectedModel] = useState("");

    const fetchFormData = async () => {
        const versionOptionList = [];
        const modelResponse = await getPlatformModel();
        if (modelResponse.status !== 200) {
            message.error("Fetch platform model failed");
        }
        // else {
        //     for (const key in modelResponse.data) {
        //         if (Object.prototype.hasOwnProperty.call(modelResponse.data, key)) {
        //             const options = modelResponse.data[key].map(item => {
        //                 return {value: item};
        //             });
        //             const modelOption = {label: key, options};
        //             modelOptionsList.push(modelOption);
        //         }
        //     }
        // }

        const versionResponse = await getPlatformVersion();
        if (versionResponse.status !== 200) {
            message.error("Fetch platform version failed");
        } else {
            versionResponse.data.map(item => {
                versionOptionList.push({label: item, value: item});
            });
        }

        setModelOptions(modelResponse.data);
        setVersionOptions(versionOptionList);
        const values = {
            model: modelResponse.data.as4610[0],
            // model: modelOptionsList[0]?.options[0] ? modelOptionsList[0].options[0].label : "",
            version: versionOptionList[0]?.label ? versionOptionList[0].label : "",
            action: "Config"
        };
        setSelectedModel(modelResponse.data.as4610[0]);
        form.setFieldsValue(values);

        fetchTreeData();
    };

    const fetchTreeData = async () => {
        const values = form.getFieldValue();
        const treeResponse = await getCliTrees(values.model, values.version);
        if (treeResponse.status !== 200) {
            message.error("Fetch platform cli node failed");
        } else {
            const nodes = transformToTreeData(treeResponse.data);
            setTreeNodes(treeResponse.data);
            setCurrTreeNodes([...nodes]);
        }
    };

    useEffect(() => {
        fetchFormData().then();
        // const nodes = transformToTreeData(treenodes);
        // setTreeNodes(treenodes);
        // setCurrTreeNodes([...nodes]);
        setExpandedNodes([]);
    }, []);

    const transformToTreeData = treeData => {
        const nodesMap = {};
        const rootNodes = [];

        treeData.forEach(item => {
            nodesMap[item.id] = {...item, key: item.id, children: []};
        });

        treeData.forEach(item => {
            nodesMap[item.id].desc = nodesMap[item.id].title;
            nodesMap[item.id].title = nodesMap[item.id].name;
            nodesMap[item.id].icon = nodesMap[item.id].checkable ? null : <FolderOutlined />;

            if (item.pid === 0) {
                rootNodes.push(nodesMap[item.id]);
            }
            if (nodesMap[item.pid]) {
                nodesMap[item.id].path = item.path;
                nodesMap[item.pid].children.push(nodesMap[item.id]);
            }
            if (nodesMap[item.id].checkable) {
                nodesMap[item.id].title = (
                    <>
                        <FileAddOutlined style={{marginRight: 3}} />
                        {nodesMap[item.id].name}
                        <Button
                            type="text"
                            style={{marginLeft: 3}}
                            onClick={() => {
                                addNewLine(nodesMap[item.id].path);
                            }}
                        >
                            +
                        </Button>
                    </>
                );
            }
        });

        return rootNodes;
    };

    const onSearchTree = e => {
        const {value} = e.target;
        if (value) {
            const nodes = transformToTreeData(treeNodes);
            const {filteredNodes, expandedKeys} = searchTree(nodes, value);
            setCurrTreeNodes(filteredNodes);
            setExpandedNodes(expandedKeys);
        } else {
            setCurrTreeNodes(transformToTreeData(treeNodes));
            setExpandedNodes([]);
        }
    };

    const searchTree = (nodes, searchContent) => {
        const expandedKeys = new Set();

        function dfs(node, path = []) {
            const newPath = path.concat(node.key);

            let isMatch = node.name.toLowerCase().includes(searchContent.toLowerCase());

            if (isMatch) {
                newPath.forEach(key => expandedKeys.add(key));
            }

            if (node.children) {
                node.children.forEach(child => {
                    const childMatch = dfs(child, newPath);
                    isMatch = isMatch || childMatch;
                });
            }
            node.match = isMatch;
            if (isMatch) {
                node.title = <span style={{color: "red"}}>{node.title}</span>;
            }
            return isMatch;
        }
        const filteredNodes = nodes.filter(node => dfs(node));
        return {filteredNodes, expandedKeys: Array.from(expandedKeys)};
    };

    // Template Editor START
    const addNewLine = nodeContent => {
        const nodePathList = nodeContent.split(" ").filter(i => i !== "");
        nodePathList.forEach((item, index, arr) => {
            if (item.indexOf("param:") >= 0 && index - 1 >= 0) {
                arr[index] = item.replace("param", arr[index - 1]).replaceAll("-", "_");
            }
        });
        templateEditorRef.current.addNewLine(nodePathList.join(" "));
    };

    const getTemplateEditorValue = async () => {
        form.validateFields()
            .then(async values => {
                const response = await createTemplate(
                    values.name,
                    values.descr,
                    templateEditorRef.current.getContentValue(),
                    values.action
                );
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                }
                const newValues = {
                    name: "",
                    descr: ""
                };
                form.setFieldsValue(newValues);
                templateEditorRef.current.clearContentValue();
            })
            .catch(() => {});
    };
    // Template Editor END
    return (
        <Flex
            gap="middle"
            style={{
                height: "calc(100vh - 300px)",
                minHeight: "700px",
                width: "100.5%",
                minWidth: "1100px",
                flex: 1,
                marginBottom: "24px"
            }}
        >
            <Flex gap="middle " vertical style={{width: "25%", minWidth: "350px"}}>
                <Card style={{display: "flex", flex: 1}}>
                    {/* <Space direction="vertical">
                        <Space.Compact block>
                            <Input style={{width: "100%"}} addonBefore="Name" />
                        </Space.Compact>
                        <Space.Compact block>
                            <Input style={{width: "100%"}} addonBefore="Description" />
                        </Space.Compact>
                        <Space.Compact block>
                            <Input style={{width: "100%"}} addonBefore="Platform" />
                        </Space.Compact>
                        <Space.Compact block>
                            <Input style={{width: "100%"}} addonBefore="Version" />
                        </Space.Compact>
                        <Space.Compact block>
                            <Input style={{width: "100%"}} addonBefore="Action" />
                        </Space.Compact>
                        <Space.Compact block>
                            <Input style={{width: "100%"}} addonBefore="Search" />
                        </Space.Compact>
                    </Space> */}
                    <Form
                        form={form}
                        labelCol={{style: {width: 60}}}
                        // wrapperCol={{style: {width: 280}}}
                        size="small"
                        labelAlign="left"
                        labelWrap
                    >
                        <Form.Item
                            name="name"
                            label="Name"
                            rules={[
                                {required: true},
                                {
                                    validator: (_, value) => {
                                        if (value.includes(" ")) {
                                            return Promise.reject(new Error("Name cannot contain spaces"));
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                            className={NewTemplateStyle.templateFormItem}
                        >
                            <Input style={{width: 245, height: 32, borderRadius: "2px"}} />
                        </Form.Item>
                        <Form.Item name="descr" label="Descr" className={NewTemplateStyle.templateFormItem}>
                            <Input style={{width: 245, height: 32, borderRadius: "2px"}} />
                        </Form.Item>
                        <Form.Item name="model" label="Model" className={NewTemplateStyle.templateFormItem}>
                            <SwitchModelSelector
                                placeholder="Select model"
                                handleChange={value => {
                                    setSelectedModel(value);
                                    const formValues = form.getFieldsValue();
                                    formValues.model = value;
                                    form.setFieldsValue(formValues);
                                    fetchTreeData();
                                }}
                                data={modelOptions}
                                selectedModel={selectedModel}
                                style={{width: 245, height: 32}}
                            />
                        </Form.Item>
                        <Form.Item name="version" label="Version" className={NewTemplateStyle.templateFormItem}>
                            <Select
                                options={versionOptions}
                                onChange={fetchTreeData}
                                style={{width: 245, height: 32}}
                            />
                        </Form.Item>
                        <Form.Item name="action" label="Action" className={NewTemplateStyle.templateFormItem}>
                            <Select
                                options={[
                                    {value: "config", label: "Config"},
                                    {value: "delete", label: "Delete"}
                                ]}
                                style={{width: 245, height: 32}}
                            />
                        </Form.Item>
                    </Form>
                    <Divider orientation="left" orientationMargin="0">
                        CLI Tree
                    </Divider>
                    <Flex gap="middle" vertical>
                        <Input
                            placeholder="Search"
                            prefix={<Icon component={searchSvg} />}
                            allowClear
                            onChange={onSearchTree}
                            style={{width: "280px", height: "32px", float: "right", borderRadius: "2px"}}
                        />
                        <Tree
                            className={NewTemplateStyle.templateTreeModule}
                            showIcon
                            defaultExpandedKeys={expandedNodes}
                            autoExpandParent
                            treeData={currTreeNodes}
                            style={{overflow: "auto"}}
                        />
                    </Flex>
                </Card>
            </Flex>
            <Flex gap="middle" vertical style={{width: "75%", minHeight: "100%", marginRight: "0.5%"}}>
                <Space size={16}>
                    <Button
                        type="primary"
                        icon={<Icon component={saveSvg} style={{height: 20}} />}
                        onClick={getTemplateEditorValue}
                    >
                        Save
                    </Button>
                    <Button
                        htmlType="button"
                        icon={<Icon component={updateSvg} style={{height: 20}} />}
                        onClick={async () => {
                            setSpinning(true);
                            try {
                                const response = await updateCliTree();
                                if (response.status !== 200) {
                                    message.error(response.info);
                                } else {
                                    message.success(response.info);
                                }
                                setSpinning(false);
                                setTimeout(() => {
                                    window.location.reload();
                                }, 3000);
                            } catch (e) {
                                message.error("An error occurred during the process of update");
                            } finally {
                                setSpinning(false);
                            }
                        }}
                    >
                        Update CLI Tree
                    </Button>
                </Space>
                <TemplateEditor ref={templateEditorRef} style={{height: "100%"}} />
            </Flex>
            <Spin spinning={spinning} tip="Loading..." size="large" fullscreen />
        </Flex>
    );
};
export default NewTemplate;
