.tile {
  background: #ffffff;
  border-radius: 3px;
  padding: 20px;
  // -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);
  // box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);
  -webkit-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  flex: 1;
}
.tile1 {
  background: #ffffff;
  // padding-bottom: 12px;
  -webkit-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.tile2 {
  background: #ffffff;
  border-radius: 3px;
  padding: 24px 0px;
  // border-left: 1px solid #F2F2F2;
  // -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);
  // box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);
  -webkit-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  border-left: 1px solid #F2F2F2;
}
.tile2 > div {
  padding-left: 40px;
}
.tile2 > div > div {
  // padding-left: 24px;
  flex: none !important;
  margin-bottom: 24px;
}

.buttonStyle {
  margin-right: 16px
};

.buttonStyle2 {
  // margin-right: 16px
};
.inputFileFilled input{
  background: #f0f0f0;
}

.systemTitle {
  padding-bottom: 20px;
  width: 538px;
}
.systemTitle > div > div > div > h2 {
  display: inline-block;
}

.cardHeight {
  display: flex;
  flex-grow: 1;
}