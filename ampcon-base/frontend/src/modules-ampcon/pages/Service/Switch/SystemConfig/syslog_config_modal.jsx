import {But<PERSON>, <PERSON><PERSON><PERSON>, Flex, Form, Input, InputNumber, message, Modal, Select} from "antd";
import {forwardRef, useImperativeHandle, useRef, useState} from "react";
import {deleteSyslogConfigInfo, querySyslogConfigInfo, updateSyslogConfigInfo} from "@/modules-ampcon/apis/config_api";
import {formValidateRules} from "@/modules-ampcon/utils/util";
import {AmpConCustomTable, createColumnConfig} from "@/modules-ampcon/components/custom_table";

const SyslogConfigModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showSyslogConfigModal: () => {
            setIsShowModal(true);
        },
        hideSyslogConfigModal: () => {}
    }));

    const title = "Syslog Config";
    const formIPLabel = "IP";
    const formPortLabel = "Port";
    const formProtocolLabel = "Protocol";
    const formLevelLabel = "Level";

    const [syslogConfigForm] = Form.useForm();
    const syslogTableRef = useRef(null);

    const [isShowModal, setIsShowModal] = useState(false);

    const tableColumns = [
        createColumnConfig("Ip", "ip"),
        createColumnConfig("Port", "port"),
        createColumnConfig("Protocol", "protocol"),
        {
            ...createColumnConfig("Level", "level"),
            render: (_, record) => {
                if (record.level === "warning") {
                    return "ERROR";
                }
                if (record.level === "info") {
                    return "SUCCESS";
                }
                return "";
            }
        },
        {
            title: "Operation",
            render: (_, record) => (
                <span className="actionLink">
                    <a
                        onClick={() => {
                            deleteSyslogConfigInfo(record.ip).then(response => {
                                if (response.status !== 200) {
                                    message.error(response.info);
                                } else {
                                    message.success(response.info);
                                    querySyslogConfigInfo().then(async response => {
                                        if (response.status !== 200) {
                                            message.error(response.info);
                                        } else {
                                            syslogTableRef.current.refreshTable();
                                        }
                                    });
                                }
                            });
                        }}
                    >
                        Delete
                    </a>
                </span>
            )
        }
    ];
    const tableMatchFieldList = [{name: "ip"}];

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            // className="ampcon-custom-modal-style syslog-config-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={async () => {
                            try {
                                await syslogConfigForm.validateFields();
                            } catch (errorInfo) {
                                return;
                            }
                            updateSyslogConfigInfo(
                                syslogConfigForm.getFieldValue("ip"),
                                syslogConfigForm.getFieldValue("port"),
                                syslogConfigForm.getFieldValue("protocol"),
                                syslogConfigForm.getFieldValue("level")
                            ).then(response => {
                                if (response.status !== 200) {
                                    message.error(response.info);
                                } else {
                                    querySyslogConfigInfo().then(async response => {
                                        if (response.status !== 200) {
                                            message.error(response.info);
                                        } else {
                                            syslogTableRef.current.refreshTable();
                                        }
                                    });
                                }
                            });
                        }}
                    >
                        Add
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 5}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={syslogConfigForm}
            >
                <Form.Item
                    name="ip"
                    label={formIPLabel}
                    rules={[
                        {
                            required: true
                        },
                        formValidateRules.ipv4()
                    ]}
                    initialValue=""
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="port"
                    label={formPortLabel}
                    rules={[
                        {
                            required: true
                        }
                    ]}
                    initialValue=""
                >
                    <InputNumber style={{width: "280px"}} min={1} max={65535} />
                </Form.Item>
                <Form.Item
                    name="protocol"
                    label={formProtocolLabel}
                    initialValue="----------"
                    rules={[
                        {
                            required: true,
                            pattern: /^(UDP|TCP)$/,
                            message: "This field is required."
                        }
                    ]}
                >
                    <Select
                        style={{width: "280px"}}
                        defaultValue={{value: "----------", label: "----------"}}
                        options={[
                            {value: "----------", label: "----------"},
                            {value: "UDP", label: "UDP"},
                            {value: "TCP", label: "TCP"}
                        ]}
                    />
                </Form.Item>
                <Form.Item
                    name="level"
                    label={formLevelLabel}
                    initialValue="----------"
                    rules={[
                        {
                            required: true,
                            pattern: /^(info|warning)$/,
                            message: "This field is required."
                        }
                    ]}
                >
                    <Select
                        style={{width: "280px"}}
                        defaultValue={{value: "----------", label: "----------"}}
                        options={[
                            {value: "----------", label: "----------"},
                            {value: "info", label: "SUCCESS"},
                            {value: "warning", label: "ERROR"}
                        ]}
                    />
                </Form.Item>
            </Form>
            <Divider style={{marginLeft: "-32px"}} />

            <Flex vertical style={{marginTop: "-24px"}}>
                <AmpConCustomTable
                    fetchAPIInfo={querySyslogConfigInfo}
                    columns={tableColumns}
                    matchFieldsList={tableMatchFieldList}
                    ref={syslogTableRef}
                />
            </Flex>
        </Modal>
    ) : null;
});

export default SyslogConfigModal;
