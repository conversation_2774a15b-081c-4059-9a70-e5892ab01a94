import {Button, Dropdown, message, Spin} from "antd";
import React, {useState} from "react";
import {DownOutlined, UpOutlined} from "@ant-design/icons";
import {updateLinkIp, updateHostname} from "@/modules-ampcon/apis/lifecycle_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const LifecycletDropdownAction = ({tableRef}) => {
    const [spinning, setSpinning] = useState(false);
    const [hoverStatus, setHoverStatus] = useState(false);

    const handeUpdateHostname = async () => {
        setSpinning(true);
        try {
            const snMgtIpMap = tableRef.current.getTableData().reduce((acc, cur) => {
                acc[cur.sn] = cur.mgt_ip;
                return acc;
            }, {});
            updateHostname(snMgtIpMap)
                .then(response => {
                    setSpinning(false);
                    if (response.status !== 200) {
                        message.error(response.info);
                    } else {
                        message.success(response.info);
                        tableRef.current.refreshTable();
                    }
                })
                .catch(() => {
                    setSpinning(false);
                    message.error("Update hostname failed");
                });
        } catch (e) {
            message.error("An error occurred during the process of update");
        } finally {
            setSpinning(false);
        }
    };

    const handleUpdateLinkIp = async () => {
        setSpinning(true);
        try {
            const response = await updateLinkIp();
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                message.success(response.info);
                tableRef.current?.refreshTable();
            }
        } catch (e) {
            message.error("Update link ip address failed");
        } finally {
            setSpinning(false);
        }
    };

    const dropdownMenu = [
        {
            label: "Sync Sysname",
            onClick: () => {
                confirmModalAction("Are you sure want to update the hostname?", () => {
                    handeUpdateHostname();
                });
            }
        },
        {
            label: "Sync Mgmt IP Address",
            onClick: () => {
                confirmModalAction("Are you sure want to update the link MGMT IP address?", () => {
                    handleUpdateLinkIp();
                });
            }
        }
    ];

    return (
        <div>
            <Dropdown menu={{items: dropdownMenu}} trigger={["hover"]} onOpenChange={val => setHoverStatus(val)}>
                <Button htmlType="button" style={{width: "160px"}}>
                    Lifecycle Actions
                    {hoverStatus ? (
                        <UpOutlined style={{fontSize: "12px", marginLeft: "8px"}} />
                    ) : (
                        <DownOutlined style={{fontSize: "12px", marginLeft: "8px"}} />
                    )}
                </Button>
            </Dropdown>
            <Spin spinning={spinning} tip="Loading..." size="large" fullscreen />
        </div>
    );
};
export default LifecycletDropdownAction;
