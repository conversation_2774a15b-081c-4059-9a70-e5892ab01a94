import {Space, Form, Input, Divider, message, Tooltip} from "antd";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {useState, useEffect, forwardRef, useImperativeHandle, useRef} from "react";
import Icon from "@ant-design/icons";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import NetworkModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/network_modal";
import style from "@/modules-ampcon/pages/Topo/SwitchTemplates/switch_templates.module.scss";

const NetworkForm = forwardRef(({data, tableStyle, setSwitchTemplateData, onDeleteNetwork, vrfData}, ref) => {
    const [form] = Form.useForm();
    const networkModalRef = useRef(null);
    const [dataSource, setDataSource] = useState([]);
    useEffect(() => {
        setDataSource(data || []);
        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                network: {
                    networks: data || []
                }
            }
        }));
    }, [data, form]);
    useImperativeHandle(ref, () => ({}));
    const updateNetworks = newNetworks => {
        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                network: {
                    networks: newNetworks
                }
            }
        }));
    };
    const updateLinkedFields = (oldNetwork, newNetwork) => {
        const oldIdentifier = `${oldNetwork.name}(${oldNetwork.id})`;
        const newIdentifier = `${newNetwork.name}(${newNetwork.id})`;
        setSwitchTemplateData(prev => {
            const updatedVrfNetworks = (prev.switch.vrf.networks || []).map(vrf => {
                const updatedIds = vrf.id?.map(id => (id === oldIdentifier ? newIdentifier : id));
                return {...vrf, id: updatedIds};
            });
            const updatedPortConfig = (prev.switch.portConfiguration || []).map(port => {
                const newPort = {...port};
                if (port.portNetwork === oldIdentifier) {
                    newPort.portNetwork = newIdentifier;
                }
                if (Array.isArray(port.trunkNetwork)) {
                    newPort.trunkNetwork = port.trunkNetwork.map(tn => (tn === oldIdentifier ? newIdentifier : tn));
                } else if (typeof port.trunkNetwork === "string" && port.trunkNetwork === oldIdentifier) {
                    newPort.trunkNetwork = newIdentifier;
                }
                return newPort;
            });
            return {
                ...prev,
                switch: {
                    ...prev.switch,
                    vrf: {networks: updatedVrfNetworks},
                    portConfiguration: updatedPortConfig
                }
            };
        });
    };

    const columns = [
        {
            title: "VLAN Name",
            dataIndex: "name",
            width: "30%",
            render: (_, record) => {
                return record.name;
            },
            sorter: (a, b) => a.name.localeCompare(b.name)
        },
        {
            title: "VLAN ID",
            dataIndex: "id",
            width: "30%",
            render: (_, record) => {
                return record.id;
            },
            sorter: (a, b) => a.id.localeCompare(b.id)
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                networkModalRef.current.showNetworkModal({mode: "edit"}, record);
                            }}
                        >
                            Edit
                        </a>
                        <a
                            onClick={() => {
                                confirmModalAction("Are you sure want to delete?", () => {
                                    try {
                                        onDeleteNetwork(record);
                                        message.success("Delete Successful");
                                    } catch (error) {
                                        message.error("Delete Failed");
                                        console.error(error);
                                    }
                                });
                            }}
                        >
                            Delete
                        </a>
                    </Space>
                );
            }
        }
    ];

    return (
        <>
            <h2 className={style.title}>Network</h2>
            <NetworkModal
                ref={networkModalRef}
                serverData={dataSource}
                vrfData={vrfData}
                handleAddNetwork={data => {
                    setDataSource([...dataSource, data]);
                    updateNetworks([...dataSource, data]);
                }}
                handleEditNetwork={data => {
                    const oldRecord = dataSource.find(item => item.name === data.name);
                    setDataSource(dataSource.map(item => (item.name === data.name ? data : item)));
                    updateNetworks(dataSource.map(item => (item.name === data.name ? data : item)));
                    updateLinkedFields(oldRecord, data);
                }}
            />
            <Form
                ref={ref}
                form={form}
                validateTrigger="onBlur"
                labelAlign="left"
                style={{width: 505, marginBottom: "-10px"}}
            >
                <Form.Item style={{marginBottom: "0px"}} labelCol={{style: {width: 175}}}>
                    <a
                        style={{
                            display: "inline-flex",
                            alignItems: "center",
                            border: "none",
                            borderRadius: "4px",
                            color: "#14c9bb"
                        }}
                        onClick={() => {
                            networkModalRef.current.showNetworkModal({mode: "create"});
                        }}
                    >
                        <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                        Network
                    </a>
                </Form.Item>
            </Form>
            {dataSource.length > 0 && (
                <AmpConCustomTable
                    dataSource={dataSource}
                    columns={columns}
                    style={tableStyle}
                    pagination={{
                        defaultPageSize: 10,
                        showSizeChanger: true,
                        hideOnSinglePage: dataSource.length <= 10
                    }}
                />
            )}
            <Divider className={style.divider} />
        </>
    );
});
export default NetworkForm;
