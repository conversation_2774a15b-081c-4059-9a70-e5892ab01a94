import {Space, Form, message, Divider, Radio} from "antd";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import DhcpSnoopingModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/dhcp_snooping_modal";
import Icon from "@ant-design/icons";
import {useState, useRef, useEffect, forwardRef, useImperativeHandle} from "react";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const DhcpSnoopingForm = forwardRef(({data, tableStyle, networkData, setSwitchTemplateData}, ref) => {
    const [isEnable, setIsEnable] = useState(false);
    const [form] = Form.useForm();
    const dhcpSnoopingModalRef = useRef(null);
    const [dataSource, setDataSource] = useState([]);
    useEffect(() => {
        if (data) {
            form.setFieldsValue({
                enable: data.configuration ? "enable" : "disable"
            });
            setIsEnable(data.configuration);
            setDataSource(data.networks || []);
        }
    }, [data]);
    const updataDataSnooping = newNetworks => {
        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                dhcpSnooping: {
                    configuration: isEnable,
                    networks: newNetworks
                }
            }
        }));
    };
    const handleAddServer = newData => {
        const newSnooping = to_structure_data(newData);
        const updatedData = [...dataSource, newSnooping];
        setDataSource(updatedData);
        updataDataSnooping(updatedData);
    };
    const handleEditServer = (newData, currentRecord) => {
        const updatedData = dataSource.map(item => (item.id === currentRecord.id ? to_structure_data(newData) : item));
        setDataSource(updatedData);
        updataDataSnooping(updatedData);
    };

    const handleDeleteServer = record => {
        const updatedData = dataSource.filter(item => item.id !== record.id);
        setDataSource(updatedData);
        updataDataSnooping(updatedData);
    };
    const handleConfigChange = e => {
        const enabled = e.target.value === "enable";
        setIsEnable(enabled);

        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                dhcpSnooping: {
                    configuration: enabled,
                    networks: enabled ? dataSource : []
                }
            }
        }));
    };
    function to_structure_data(data) {
        return {
            network: `${data.network}(${data.id})`,
            id: data.id
        };
    }

    const columns = [
        {
            title: "Network",
            dataIndex: "network",
            width: "30%",
            render: (_, record) => {
                return record.network;
            },
            sorter: (a, b) => a.network.localeCompare(b.network)
        },
        {
            title: "VLAN ID",
            dataIndex: "id",
            width: "30%",
            render: (_, record) => {
                return record.id;
            },
            sorter: (a, b) => a.id.localeCompare(b.id)
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                dhcpSnoopingModalRef.current.showDhcpSnoopingModal({mode: "edit"}, record);
                            }}
                        >
                            Edit
                        </a>
                        <a
                            onClick={() => {
                                confirmModalAction("Are you sure want to delete?", () => {
                                    try {
                                        handleDeleteServer(record);
                                        message.success("Delete Successful");
                                    } catch (error) {
                                        message.error("Delete Failed");
                                        console.error(error);
                                    }
                                });
                            }}
                        >
                            Delete
                        </a>
                    </Space>
                );
            }
        }
    ];

    return (
        <>
            <h2>DHCP Snooping</h2>
            <DhcpSnoopingModal
                ref={dhcpSnoopingModalRef}
                handleAddServer={handleAddServer}
                handleEditServer={handleEditServer}
                serverData={data}
                networkData={networkData}
            />
            <Form ref={ref} form={form} validateTrigger="onBlur" labelAlign="left" style={{width: 505}}>
                <Form.Item
                    name="enable"
                    label="Configuration"
                    labelCol={{style: {width: 175}}}
                    initialValue={isEnable ? "enable" : "disable"}
                >
                    <Radio.Group onChange={handleConfigChange}>
                        <Radio value="enable">Enable</Radio>
                        <Radio value="disable">Disable</Radio>
                    </Radio.Group>
                </Form.Item>
                {isEnable && (
                    <Form.Item style={{marginBottom: "0px"}} label="DHCP Network" labelCol={{style: {width: 175}}}>
                        <a
                            style={{
                                border: "none",
                                borderRadius: "4px",
                                color: "#14c9bb"
                            }}
                            onClick={() => {
                                dhcpSnoopingModalRef.current.showDhcpSnoopingModal({mode: "create"});
                            }}
                        >
                            <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                            Add
                        </a>
                    </Form.Item>
                )}
            </Form>
            {isEnable && (
                <div>
                    <AmpConCustomTable
                        dataSource={dataSource}
                        columns={columns}
                        style={tableStyle}
                        // pagination={{
                        //     defaultPageSize: 10,
                        //     showSizeChanger: true,
                        //     hideOnSinglePage: serverData.length <= 10
                        // }}
                    />
                </div>
            )}

            <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
        </>
    );
});
export default DhcpSnoopingForm;
