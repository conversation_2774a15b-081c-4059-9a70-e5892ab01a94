import {Space, Form, Divider, Radio, message} from "antd";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {useState, useEffect, forwardRef, useImperativeHandle, useRef} from "react";
import Icon from "@ant-design/icons";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import OspfModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/ospf_modal";
import style from "@/modules-ampcon/pages/Topo/SwitchTemplates/switch_templates.module.scss";

const OspfForm = forwardRef(({ospData, tableStyle, setSwitchTemplateData}, ref) => {
    const [isEnable, setIsEnable] = useState(false);
    const [form] = Form.useForm();
    const ospfModalRef = useRef(null);
    const [dataSource, setDataSource] = useState([]);
    const ridRef = useRef(1);
    const genRowId = () => `ospf_${ridRef.current++}`;
    const toIpList = v =>
        Array.isArray(v)
            ? v
            : (v || "")
                  .split(",")
                  .map(x => x.trim())
                  .filter(Boolean);
    const extToInner = (areas = []) =>
        (areas || []).map(item => ({
            id: genRowId(),
            areaId: item.id,
            type: item.type,
            ipv4: toIpList(item.ipv4)
        }));
    const innerToExt = (rows = []) =>
        rows.map(({areaId, type, ipv4}) => ({
            id: String(areaId ?? ""),
            type,
            ipv4: toIpList(ipv4)
        }));
    const displayTypes = {default: "Default", stub: "Stub", nssa: "NSSA"};
    useEffect(() => {
        if (ospData) {
            form.setFieldsValue({enable: ospData.configuration ? "enable" : "disable"});
            setIsEnable(ospData.configuration);
            setDataSource(extToInner(ospData.areas || []));
        }
    }, [ospData, form]);

    useImperativeHandle(ref, () => ({
        validate: () => {
            if (isEnable && dataSource.length === 0) {
                Promise.reject(new Error("Please add at least one OSPF configuration."));
                message.error("Please add at least one OSPF configuration.");
                return false;
            }
            return true;
        }
    }));

    const updateOspf = innerRows => {
        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                ospf: {
                    configuration: isEnable,
                    areas: innerToExt(innerRows)
                }
            }
        }));
    };
    const handleAddServer = newData => {
        const row = {
            id: genRowId(),
            areaId: newData.id,
            type: newData.type,
            ipv4: toIpList(newData.ipv4)
        };
        const updated = [...dataSource, row];
        setDataSource(updated);
        updateOspf(updated);
    };
    const handleEditServer = (newData, currentRecord) => {
        const rowId = currentRecord._rowId;
        const updated = dataSource.map(item =>
            item.id === rowId
                ? {
                      ...item,
                      areaId: newData.id,
                      type: newData.type,
                      ipv4: toIpList(newData.ipv4)
                  }
                : item
        );
        setDataSource(updated);
        updateOspf(updated);
    };

    const handleDeleteServer = record => {
        const updated = dataSource.filter(item => item.id !== record.id);
        setDataSource(updated);
        updateOspf(updated);
    };
    const handleConfigChange = e => {
        const enabled = e.target.value === "enable";
        setIsEnable(enabled);
        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                ospf: {
                    configuration: enabled,
                    areas: enabled ? innerToExt(dataSource) : []
                }
            }
        }));
    };
    const columns = [
        {
            title: "IPv4/Prefixlen",
            dataIndex: "ipv4",
            width: "25%",
            render: (_, record) => (
                <div>
                    {toIpList(record.ipv4).map((ip, i) => (
                        <div key={`${record.id}_${i}`}>{ip}</div>
                    ))}
                </div>
            ),
            sorter: (a, b) => (toIpList(a.ipv4)[0] || "").localeCompare(toIpList(b.ipv4)[0] || "")
        },
        {
            title: "Area ID",
            dataIndex: "areaId",
            width: "25%",
            render: (_, record) => record.areaId,
            sorter: (a, b) => String(a.areaId || "").localeCompare(String(b.areaId || ""))
        },
        {
            title: "Area Type",
            dataIndex: "type",
            width: "25%",
            render: (_, record) => displayTypes[String(record.type).toLowerCase()] || record.type,
            sorter: (a, b) => String(a.type || "").localeCompare(String(b.type || ""))
        },
        {
            title: "Operation",
            render: (_, record) => (
                <Space size="large" className="actionLink">
                    <a
                        onClick={() => {
                            ospfModalRef.current.showOspfModal(
                                {mode: "edit"},
                                {...record, _rowId: record.id, id: record.areaId}
                            );
                        }}
                    >
                        Edit
                    </a>
                    <a
                        onClick={() => {
                            confirmModalAction("Are you sure want to delete?", () => {
                                try {
                                    handleDeleteServer(record);
                                    message.success("Delete Successful");
                                } catch (error) {
                                    message.error("Delete Failed");
                                }
                            });
                        }}
                    >
                        Delete
                    </a>
                </Space>
            )
        }
    ];

    return (
        <>
            <h2 className={style.title}>OSPF</h2>
            <OspfModal
                ref={ospfModalRef}
                ospData={ospData}
                handleAddServer={handleAddServer}
                handleEditServer={handleEditServer}
            />
            <Form
                ref={ref}
                form={form}
                validateTrigger="onBlur"
                labelAlign="left"
                style={{width: 505, marginBottom: "-10px"}}
            >
                <Form.Item
                    name="enable"
                    label="Configuration"
                    labelCol={{style: {width: 175}}}
                    initialValue={isEnable ? "enable" : "disable"}
                    style={{marginBottom: "-1px"}}
                >
                    <Radio.Group onChange={handleConfigChange}>
                        <Radio value="enable">Enable</Radio>
                        <Radio value="disable">Disable</Radio>
                    </Radio.Group>
                </Form.Item>
                {isEnable && (
                    <Form.Item style={{marginBottom: "0px"}} labelCol={{style: {width: 175}}}>
                        <a
                            style={{
                                display: "inline-flex",
                                alignItems: "center",
                                border: "none",
                                borderRadius: "4px",
                                color: "#14c9bb"
                            }}
                            onClick={() => {
                                ospfModalRef.current.showOspfModal({mode: "create"});
                            }}
                        >
                            <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                            OSPF
                        </a>
                    </Form.Item>
                )}
            </Form>
            {isEnable && dataSource.length > 0 && (
                <div>
                    <AmpConCustomTable
                        dataSource={dataSource}
                        columns={columns}
                        style={tableStyle}
                        pagination={{
                            defaultPageSize: 10,
                            showSizeChanger: true,
                            hideOnSinglePage: dataSource.length <= 10
                        }}
                    />
                </div>
            )}
            <Divider className={style.divider} />
        </>
    );
});
export default OspfForm;
