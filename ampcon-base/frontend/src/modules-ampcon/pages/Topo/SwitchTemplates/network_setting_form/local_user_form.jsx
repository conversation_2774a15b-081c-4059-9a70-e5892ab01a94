import {Space, Form, Divider, message} from "antd";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import LocalUserModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/local_user_modal";
import Icon from "@ant-design/icons";
import {useState, useRef, useEffect, forwardRef, useImperativeHandle} from "react";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import style from "@/modules-ampcon/pages/Topo/SwitchTemplates/switch_templates.module.scss";

const LocalUserForm = forwardRef(({userData, tableStyle, setSwitchTemplateData}, ref) => {
    const [form] = Form.useForm();
    const localUserModalRef = useRef(null);
    const [dataSource, setDataSource] = useState([]);
    useEffect(() => {
        if (userData) {
            setDataSource(userData.users || []);
        }
    }, [userData]);
    const updateUser = newData => {
        setSwitchTemplateData(prev => ({
            ...prev,
            management: {
                ...prev.management,
                localUser: {
                    users: newData
                }
            }
        }));
    };
    const handleAddServer = newData => {
        const updatedData = [...dataSource, newData];
        setDataSource(updatedData);
        updateUser(updatedData);
    };
    const handleEditServer = (newData, currentRecord) => {
        const updatedData = dataSource.map(item => (item.name === currentRecord.name ? newData : item));
        setDataSource(updatedData);
        updateUser(updatedData);
    };

    const handleDeleteServer = record => {
        const updatedData = dataSource.filter(item => !(item.name === record.name));
        setDataSource(updatedData);
        updateUser(updatedData);
    };
    const columns = [
        {
            title: "Username",
            dataIndex: "name",
            width: "30%",
            render: (_, record) => {
                return record.name;
            },
            sorter: (a, b) => a.name.localeCompare(b.name)
        },
        {
            title: "User Level",
            dataIndex: "level",
            width: "30%",
            render: (_, record) => {
                return record.level;
            },
            sorter: (a, b) => a.level.localeCompare(b.level)
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                localUserModalRef.current.showLocalUserModal({mode: "edit"}, record);
                            }}
                        >
                            Edit
                        </a>
                        <a
                            onClick={() => {
                                confirmModalAction("Are you sure you want to delete this user?", () => {
                                    try {
                                        handleDeleteServer(record);
                                        message.success("Delete Successful");
                                    } catch (error) {
                                        message.error("Delete Failed");
                                    }
                                });
                            }}
                        >
                            Delete
                        </a>
                    </Space>
                );
            }
        }
    ];

    return (
        <>
            <h2 className={style.title}>Local User</h2>
            <LocalUserModal
                ref={localUserModalRef}
                userData={userData}
                handleAddServer={handleAddServer}
                handleEditServer={handleEditServer}
            />
            <Form
                ref={ref}
                form={form}
                validateTrigger="onBlur"
                labelAlign="left"
                style={{width: 505, marginBottom: "-10px"}}
            >
                <Form.Item style={{marginBottom: "0px"}}>
                    <a
                        style={{
                            display: "inline-flex",
                            alignItems: "center",
                            border: "none",
                            borderRadius: "4px",
                            color: "#14c9bb"
                        }}
                        onClick={() => {
                            localUserModalRef.current.showLocalUserModal({mode: "create"});
                        }}
                    >
                        <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                        Local User
                    </a>
                </Form.Item>
            </Form>
            {dataSource.length > 0 && (
                <div>
                    <AmpConCustomTable
                        dataSource={dataSource}
                        columns={columns}
                        style={tableStyle}
                        pagination={{
                            defaultPageSize: 10,
                            showSizeChanger: true,
                            hideOnSinglePage: dataSource.length <= 10
                        }}
                    />
                </div>
            )}
            <Divider className={style.divider} />
        </>
    );
});
export default LocalUserForm;
