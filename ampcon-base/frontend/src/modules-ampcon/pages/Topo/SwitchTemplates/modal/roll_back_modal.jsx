import React, {forwardRef, useEffect, useImperativeHandle, useState, useRef} from "react";
import {<PERSON>lide<PERSON>, Card, Flex, Modal, Button, Divider, Form, Alert, Select} from "antd";
import {formatDate} from "@/utils/topo_layout_utils";
import {InfoSvg} from "@/utils/common/iconSvg";

const RollBackModal = forwardRef((props, ref) => {
    const [form] = Form.useForm();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const timeSliderRef = useRef(null);
    useImperativeHandle(ref, () => ({
        showRollBackModal: () => {
            form.resetFields();
            setIsModalOpen(true);
            setTimeout(() => {
                if (timeSliderRef.current) {
                    const endDate = new Date();
                    const startDate = new Date(endDate.getTime() - 60 * 60 * 1000);
                    timeSliderRef.current.showTimeSlider(startDate, endDate);
                }
            }, 0);
        }
    }));

    return isModalOpen ? (
        <Modal
            className="ampcon-max-modal"
            title="Roll Back"
            open={isModalOpen}
            o
            onCancel={() => {
                form.resetFields();
                setIsModalOpen(false);
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsModalOpen(false);
                                form.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                setIsModalOpen(false);
                                form.resetFields();
                            }}
                        >
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Divider />
            <Alert
                message={
                    <div style={{paddingLeft: "8px"}}>
                        <span style={{fontWeight: 700}}>Note: </span>
                        The rollback operation will revert the switch configurations to the selected version.
                    </div>
                }
                type="info"
                showIcon
                icon={<InfoSvg />}
                closable
                style={{
                    marginBottom: 16,
                    backgroundColor: "#F3F8FF",
                    border: "1px solid #F3F8FF",
                    color: "#367EFF",
                    width: 1304,
                    height: 40,
                    borderRadius: 2
                }}
            />
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 3}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={form}
                style={{minHeight: "167.23px"}}
                ref={form}
                validateTrigger={["onBlur", "onSubmit"]}
                onFinish=""
            >
                <Form.Item name="version" label="Rollback Version" initialValue="">
                    <Select style={{width: "280px"}} />
                </Form.Item>
            </Form>
            <TimeSlider refreshTopoCallback={date => console.log("Selected:", date)} ref={timeSliderRef} />
            {/* <div style={{marginTop: 24}} /> */}
        </Modal>
    ) : null;
});
export default RollBackModal;
const TimeSlider = forwardRef(({refreshTopoCallback}, ref) => {
    const maxNodesCount = 70;
    const oneMinuteInterval = 60 * 1000;
    const tenMinutesInterval = 10 * 60 * 1000;

    const [value, setValue] = useState(0);
    const [isShowTimeSlider, setIsShowTimeSlider] = useState(true);
    const [marks, setMarks] = useState({});
    const [fullMarks, setFullMarks] = useState({});
    const timeoutRef = useRef(null); // 修复：使用useRef替代全局变量

    useEffect(() => {
        // 初始化数据
        const endDate = new Date();
        const startDate = new Date(endDate.getTime() - 60 * 60 * 1000);
        const marksData = generateMarks(startDate, endDate);
        setMarks(marksData[0]);
        setFullMarks(marksData[1]);
        setValue(65);

        return () => {
            // 清理定时器
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    useImperativeHandle(ref, () => ({
        showTimeSlider: (startDate, endDate) => {
            const marksData = generateMarks(
                new Date(startDate.getTime() - 5 * 60 * 1000),
                new Date(endDate.getTime() - 5 * 60 * 1000)
            );
            setMarks(marksData[0]);
            setFullMarks(marksData[1]);
            setIsShowTimeSlider(true);
            setValue(65);
        },
        getSelectedDate: () => (fullMarks[value] ? `${fullMarks[value]}:00` : null)
    }));

    const generateMarks = (startDate, endDate) => {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diff = Math.abs(end - start);

        const marksTemp = {};
        const fullMarksTemp = {};
        const interval = tenMinutesInterval;

        // 生成标记
        for (let i = 0; i <= diff; i += interval) {
            const date = new Date(start.getTime() + i);
            marksTemp[i / (60 * 1000) + 5] = formatDate(date);
        }

        // 生成完整时间点
        for (let i = 0; i <= diff; i += oneMinuteInterval) {
            const date = new Date(start.getTime() + i);
            fullMarksTemp[i / oneMinuteInterval + 5] = formatDate(date);
        }

        return [marksTemp, fullMarksTemp];
    };

    const handleSliderChange = newValue => {
        setValue(newValue);
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }
        timeoutRef.current = setTimeout(() => {
            refreshTopoCallback(`${fullMarks[newValue]}:00`);
        }, 500);
    };

    return isShowTimeSlider && Object.keys(marks).length > 0 ? (
        <Slider
            min={0}
            max={maxNodesCount}
            marks={marks}
            value={value}
            onChange={handleSliderChange}
            tooltip={{
                formatter: value => fullMarks[value] || "",
                open: true
            }}
        />
    ) : null;
});
