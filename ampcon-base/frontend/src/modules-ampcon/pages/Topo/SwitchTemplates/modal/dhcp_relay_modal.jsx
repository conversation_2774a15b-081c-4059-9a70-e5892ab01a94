import {Button, Form, message, Divider, Select, Modal, Input} from "antd";
import {useState, forwardRef, useImperativeHandle} from "react";
import {formValidateRules} from "@/modules-ampcon/utils/util";

const DhcpRelayModal = forwardRef(({handleAddServer, handleEditServer, dhcpData, networkData}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [form] = Form.useForm();
    const [mode, setMode] = useState("");
    const [currentRecord, setCurrentRecord] = useState(null);
    const [existingEntries, setExistingEntries] = useState([]);
    const networkOptions =
        networkData.map(item => ({
            label: `${item.name}(${item.id})`,
            value: `${item.name}(${item.id})`,
            id: item.id
        })) || [];
    useImperativeHandle(ref, () => ({
        showNetworkModal: ({mode}, record) => {
            setMode(mode);
            if (mode === "edit") {
                form.setFieldsValue({
                    ...record,
                    network: record.network
                });
                setExistingEntries(
                    dhcpData.networks
                        .filter(item => !(item.network === record.network && item.ip === record.ip))
                        .map(item => ({network: item.network, ip: item.ip}))
                );
                setCurrentRecord(record);
            } else {
                setCurrentRecord(null);
                setExistingEntries(dhcpData.networks?.map(item => ({network: item.network, ip: item.ip})) || []);
                form.setFieldsValue({
                    network: undefined,
                    id: ""
                });
            }
            setIsShowModal(true);
        }
    }));
    const validateEntryUnique = (_, value) => {
        const network = form.getFieldValue("network");
        if (!network) {
            return Promise.reject(new Error("Please select a network first"));
        }
        const isDuplicate = existingEntries.some(entry => entry.network === network && entry.ip === value);
        if (isDuplicate) {
            return Promise.reject(new Error("This network and IP combination already exists"));
        }

        return Promise.resolve();
    };
    const handleNetworkChange = (value, option) => {
        form.setFieldsValue({
            id: option.id
        });
        if (form.getFieldValue("ip")) {
            form.validateFields(["ip"]);
        }
    };
    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {mode === "create" ? "Create DHCP Relay" : "Edit"}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            form.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            form.submit();
                        }}
                    >
                        Apply
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={form}
                style={{minHeight: "267.23px"}}
                ref={form}
                validateTrigger={["onBlur", "onSubmit"]}
                onFinish={() => {
                    try {
                        if (mode === "create") {
                            handleAddServer(form.getFieldsValue());
                            message.success("Server created successfully");
                        }
                        if (mode === "edit") {
                            console.log("Editing server with values:", form.getFieldsValue(), currentRecord);
                            handleEditServer(form.getFieldsValue(), currentRecord);
                            message.success("Server edited successfully");
                        }
                    } catch (error) {
                        message.error("An error occurred while processing the server");
                        console.error(error);
                    } finally {
                        setIsShowModal(false);
                        form.resetFields();
                    }
                }}
            >
                <Form.Item
                    name="network"
                    rules={[{required: true, message: "Please input server address"}, {validator: validateEntryUnique}]}
                    label="Network"
                >
                    <Select style={{width: "280px"}} options={networkOptions} onChange={handleNetworkChange} />
                </Form.Item>
                <Form.Item name="id" validateFirst rules={[{required: true}]} label="VLAN ID" initialValue="">
                    <Input style={{width: "280px"}} disabled />
                </Form.Item>
                <Form.Item
                    name="ip"
                    validateFirst
                    rules={[
                        {required: true, message: "Please input IP address"},
                        {validator: validateEntryUnique},
                        formValidateRules.ipv4()
                    ]}
                    label="DHCP Server"
                    initialValue=""
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default DhcpRelayModal;
