import {Button, Form, Input, Modal, Flex, Divider, message} from "antd";
import {useState, useImperativeHandle, forwardRef, useRef} from "react";
import {copyTemplate} from "@/modules-ampcon/apis/campus_blueprint_api";
import dayjs from "dayjs";
import {checkDuplicateName} from "@/modules-ampcon/apis/campus_blueprint_api";
import {debounce} from "lodash";
const CopyModal = forwardRef((props, ref) => {
    const [templateId, setTemplateId] = useState();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [createCopyForm] = Form.useForm();
    const copyModalSuccessCallback = useRef(null);
    useImperativeHandle(ref, () => ({
        showCreateCopyModal: (id, copyName, onSuccess) => {
            setTemplateId(id);
            createCopyForm.resetFields();
            setIsModalOpen(true);
            createCopyForm.setFieldsValue({name: `${copyName}:::${getDate()}`, src_name: copyName});
            copyModalSuccessCallback.current = onSuccess;
        }
    }));
    const handleCreateCopy = async () => {
        const values = await createCopyForm.validateFields();
        const currentTs = getDate();
        const res = await copyTemplate(templateId, values.name, currentTs);
        if (res.status === 200) {
            setIsModalOpen(false);
            createCopyForm.resetFields();
            message.success(res.msg);
        } else {
            message.error(res.msg);
        }
        if (copyModalSuccessCallback.current) {
            copyModalSuccessCallback.current();
        }
    };
    const checkNameApiDebounced = debounce(async (name, resolve, reject) => {
        try {
            const res = await checkDuplicateName(name, null);
            if (res.status === 200) {
                resolve();
            } else if (res.status === 400) {
                reject(new Error(res.info || "The template name already exists!"));
            } else {
                reject(new Error("The template name is invalid."));
            }
        } catch (e) {
            reject(new Error("The template name is invalid."));
        }
    }, 100);

    const validateName = (_, value) => {
        if (!value) return Promise.resolve();
        return new Promise((resolve, reject) => {
            checkNameApiDebounced(value, resolve, reject);
        });
    };
    return isModalOpen ? (
        <Modal
            title={
                <div>
                    Copy
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            onOk={() => {}}
            onCancel={() => {
                createCopyForm.resetFields();
                setIsModalOpen(false);
            }}
            className="ampcon-middle-modal"
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsModalOpen(false);
                                createCopyForm.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleCreateCopy}>
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form
                labelAlign="left"
                labelCol={{flex: "140px"}}
                wrapperCol={{flex: "280px"}}
                form={createCopyForm}
                validateTrigger={["onChange", "onSubmit"]}
            >
                <Form.Item
                    name="src_name"
                    label="Src Template Name"
                    rules={[{required: true, message: "The src template name is required."}]}
                    labelCol={{flex: "0 0 138px", style: {textAlign: "left"}}}
                    wrapperCol={{flex: "0 0 312px", style: {paddingLeft: 32}}}
                >
                    <Input disabled />
                </Form.Item>
                <Form.Item
                    name="name"
                    label="Template Name"
                    rules={[
                        {required: true, message: "The template name is required."},
                        {validator: validateName},
                        {
                            max: 64,
                            message: "The template name cannot exceed 64 characters."
                        },
                        {
                            validator: (_, value) => {
                                if (/\s/.test(value)) {
                                    return Promise.reject(new Error("The template name cannot contain spaces."));
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    labelCol={{flex: "0 0 138px", style: {textAlign: "left"}}}
                    wrapperCol={{flex: "0 0 312px", style: {paddingLeft: 32}}}
                >
                    <Input />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default CopyModal;
const getDate = () => {
    return dayjs().utc().format("YYYY-MM-DD_HH:mm:ss");
};
