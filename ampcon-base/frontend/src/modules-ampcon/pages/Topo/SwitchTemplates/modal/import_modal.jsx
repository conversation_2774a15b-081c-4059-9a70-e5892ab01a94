import {Button, message, Form, Input, Modal, Flex, Divider, Alert} from "antd";
import {UploadSvg, InfoSvg} from "@/utils/common/iconSvg";
import {useState, useImperativeHandle, forwardRef, useRef} from "react";
import <PERSON>agger from "antd/es/upload/Dragger";
import {importTemplate, viewTemplate} from "@/modules-ampcon/apis/campus_blueprint_api";
import {useNavigate} from "react-router-dom";
import downloadSVG from "@/modules-ampcon/pages/Service/Switch/CampusSwitches/resource/download_template.svg?react";
import Icon from "@ant-design/icons";
import {checkDuplicateName} from "@/modules-ampcon/apis/campus_blueprint_api";
import {debounce} from "lodash";
const ImporModal = forwardRef((props, ref) => {
    const navigate = useNavigate();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [form] = Form.useForm();
    const [fileList, setFileList] = useState([]);
    const [error, setError] = useState(null);
    useImperativeHandle(ref, () => ({
        showCreateImportModal: () => {
            form.resetFields();
            setIsModalOpen(true);
        }
    }));
    const beforeUpload = file => {
        const isJson = file.name.endsWith(".json") || file.type === "application/json";
        setFileList([file]);
        if (!isJson) {
            setError("The file format is invalid. Only .json files are supported.");
        } else {
            setError(null);
        }
        return false;
    };
    const onRemove = () => {
        setFileList([]);
        setError(null);
    };
    const checkNameApiDebounced = debounce(async (name, resolve, reject) => {
        try {
            const res = await checkDuplicateName(name, null);
            if (res.status === 200) {
                resolve();
            } else if (res.status === 400) {
                reject(new Error(res.info || "The template name already exists!"));
            } else {
                reject(new Error("The template name is invalid."));
            }
        } catch (e) {
            reject(new Error("The template name is invalid."));
        }
    }, 100);

    const validateName = (_, value) => {
        if (!value) return Promise.resolve();
        return new Promise((resolve, reject) => {
            checkNameApiDebounced(value, resolve, reject);
        });
    };

    const handleDownload = () => {
        const templateExamples = {
            switch: {
                dns: {
                    ip: ["************", "************"]
                },
                ntp: {
                    ip: ["************", "************"]
                },
                vrf: {
                    networks: [
                        {
                            id: ["vlan20(20)"],
                            name: "n01"
                        },
                        {
                            id: ["vlan30(30)"],
                            name: "n02"
                        }
                    ]
                },
                ospf: {
                    areas: [
                        {
                            id: "*******",
                            ipv4: ["***********/24", "***********/24"],
                            type: "stub"
                        },
                        {
                            id: "*******",
                            ipv4: ["***********/24"],
                            type: "nssa"
                        },
                        {
                            id: "*******",
                            ipv4: ["***********/24"],
                            type: "default"
                        }
                    ],
                    configuration: true
                },
                network: {
                    networks: [
                        {
                            id: "20",
                            name: "vlan20"
                        },
                        {
                            id: "30",
                            name: "vlan30"
                        }
                    ]
                },
                staticRoute: {
                    routes: [
                        {
                            nexthop: ["***********", "***********"],
                            destination: "***********/24"
                        },
                        {
                            nexthop: ["***********", "***********"],
                            destination: "***********/24"
                        }
                    ],
                    configuration: true
                },
                portConfiguration: [
                    {
                        poe: "enable",
                        name: "port1",
                        pcps: "1000",
                        modes: ["broadcast", "multicast", "unicast"],
                        speed: "auto",
                        portMode: "trunk",
                        portEnable: "enable",
                        description: "This is description.",
                        portNetwork: "vlan30",
                        stormControl: "enable",
                        trunkNetwork: ["vlan20", "vlan30"],
                        controlMethod: "pps"
                    }
                ],
                portConfigApplication: [
                    {
                        port: "ge-1/1/8",
                        profile: "port1"
                    }
                ]
            },
            management: {
                timeout: "20",
                localUser: {
                    users: [
                        {
                            name: "admin123",
                            level: "super-user",
                            password: "Admin123!",
                            confirmpassword: "Admin123!"
                        }
                    ]
                },
                loginBanner: {
                    after: "The content here is the prompt after login.",
                    before: "The content here is the prompt information before login."
                }
            }
        };
        const jsonString = JSON.stringify(templateExamples, null, 2);
        const blob = new Blob([jsonString], {type: "application/json"});
        const url = URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = url;
        link.download = "switch_templates.json";
        link.click();
        URL.revokeObjectURL(url);
    };
    return isModalOpen ? (
        <Modal
            title={
                <div>
                    Import Config
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            className="ampcon-middle-modal"
            onOk={() => {}}
            onCancel={() => {
                form.resetFields();
                setIsModalOpen(false);
                setFileList([]);
                setError(null);
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                form.resetFields();
                                setFileList([]);
                                setError(null);
                                setIsModalOpen(false);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={async () => {
                                try {
                                    const values = await form.validateFields();
                                    if (fileList.length === 0) {
                                        message.error("Please upload a configuration file.");
                                        return;
                                    }
                                    const formData = new FormData();
                                    formData.append("template_name", values.name);
                                    formData.append("json_file", fileList[0]);
                                    const res = await importTemplate(formData);
                                    if (res?.status === 200) {
                                        message.success("Import successful");
                                        setIsModalOpen(false);
                                        setFileList([]);
                                        setError(null);
                                        form.resetFields();
                                        const viewRes = await viewTemplate(res.id);
                                        if (viewRes?.status === 200) {
                                            navigate("/network_design/switch_templates/view", {
                                                state: {
                                                    id: res?.id,
                                                    name: values.name,
                                                    templateData: viewRes.data
                                                }
                                            });
                                        } else {
                                            throw new Error(viewRes?.msg || "Template validation failed");
                                        }
                                    } else {
                                        message.error("Import failed");
                                    }
                                } catch (err) {
                                    console.error(err);
                                    // message.error("Import failed. Please check input fields.");
                                }
                            }}
                        >
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Alert
                message={
                    <div style={{paddingLeft: "8px"}}>
                        <span style={{fontWeight: 700}}>Note: </span>
                        Only the functions supported by the current template are delivered.
                    </div>
                }
                type="info"
                showIcon
                icon={<InfoSvg />}
                closable
                style={{
                    marginBottom: 20,
                    backgroundColor: "#F3F8FF",
                    border: "1px solid #F3F8FF",
                    color: "#367EFF",
                    width: 635,
                    height: 40,
                    borderRadius: 2
                }}
            />
            <Form
                labelAlign="left"
                labelCol={{flex: "140px"}}
                wrapperCol={{flex: "320px"}}
                form={form}
                validateTrigger={["onChange", "onSubmit"]}
            >
                <Form.Item name="downloadTemplate" label="Download Template" className="actionLink">
                    <a onClick={handleDownload}>
                        <Icon component={downloadSVG} style={{marginRight: 4}} />
                        JSON Template
                    </a>
                </Form.Item>
                <Form.Item
                    name="name"
                    label="Template Name"
                    rules={[
                        {required: true, message: "The template name is required."},
                        {validator: validateName},
                        {
                            max: 64,
                            message: "The template name cannot exceed 64 characters."
                        },
                        {
                            validator: (_, value) => {
                                if (/\s/.test(value)) {
                                    return Promise.reject(new Error("The template name cannot contain spaces."));
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                >
                    <Input />
                </Form.Item>
                <Form.Item label="Config File" required>
                    <Dragger
                        name="file"
                        beforeUpload={beforeUpload}
                        onRemove={onRemove}
                        multiple={false}
                        fileList={fileList}
                        maxCount={1}
                    >
                        <p style={{marginBottom: 2}}>
                            <UploadSvg />
                        </p>
                        <p className="ant-upload-text" style={{fontSize: 13}}>
                            Drag and drop a config file to this area or
                            <br />
                            click <span style={{color: "#14C9BB", fontSize: 13}}>Choose File</span> to upload it
                        </p>
                        <p style={{color: "#B8BFBF", fontSize: 13}}>Support uploading only JSON files</p>
                    </Dragger>
                    {error && (
                        <div
                            style={{
                                color: "red",
                                marginTop: 8,
                                fontSize: 14
                            }}
                        >
                            {error}
                        </div>
                    )}
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default ImporModal;
