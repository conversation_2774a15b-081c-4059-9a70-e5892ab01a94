import {Space, Form, Flex, Input, Divider, Modal, Button, message, Table} from "antd";
import {addGreenSvg, onlineSvg, offlineSvg, exclamationSvg, searchSvg} from "@/utils/common/iconSvg";
import AssignTemplateAddModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/assign_template_add_modal";
import Icon from "@ant-design/icons";
import {useState, useRef, forwardRef, useImperativeHandle, useMemo, useEffect} from "react";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const AssignTemplateModal = forwardRef(({data, tableStyle, onApply}, ref) => {
    const [templateName, setTemplateName] = useState("");
    const [form] = Form.useForm();
    const AssignTemplateAddModalRef = useRef(null);
    const [isShowModal, setIsShowModal] = useState(false);
    const [switchList, setSwitchList] = useState([]);
    const assignTableRef = useRef();
    const [searchValue, setSearchValue] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    useImperativeHandle(ref, () => ({
        showAssignTemplateModal: (switchList, name) => {
            setSwitchList(switchList || []);
            setTemplateName(name || "");
            setIsShowModal(true);
        }
    }));
    const searchFieldsList = ["host_name", "sn", "mgt_ip", "platform_model", "site"];
    const handleDeleteServer = record => {
        const updatedData = switchList.filter(item => item.id !== record.id);
        setSwitchList(updatedData || []);
    };
    const handleAddSwitch = (selectedSwitches = []) => {
        const normalized = selectedSwitches.map(item => ({
            host_name: item.host_name,
            sn: item.sn,
            mgt_ip: item.mgt_ip,
            site: item.site,
            platform_model: item.platform_model,
            id: item.id,
            reachable_status: item.reachable_status
        }));
        setSwitchList(normalized);
    };

    const handleApply = () => {
        const snList = switchList.map(item => item.sn);
        if (onApply) {
            onApply(snList);
        }
        setIsShowModal(false);
        form.resetFields();
    };
    const filteredSwitchList = useMemo(() => {
        if (!searchValue) return switchList;
        return switchList.filter(item =>
            searchFieldsList.some(field =>
                String(item[field] || "")
                    .toLowerCase()
                    .includes(searchValue.toLowerCase())
            )
        );
    }, [switchList, searchValue]);

    useEffect(() => {
        setCurrentPage(1);
    }, [searchValue]);

    const pagedData = useMemo(() => {
        return filteredSwitchList.slice((currentPage - 1) * pageSize, currentPage * pageSize);
    }, [filteredSwitchList, currentPage, pageSize]);

    const columns = [
        {
            title: "Sysname",
            dataIndex: "host_name",
            width: "19%",
            render: (_, record) => <span>{record.host_name || "--"}</span>,
            sorter: (a, b) => a.host_name.localeCompare(b.host_name)
        },
        {
            title: "SN",
            dataIndex: "sn",
            width: "13%",
            render: (_, record) => <span>{record.sn || "--"}</span>,
            sorter: (a, b) => a.sn.localeCompare(b.sn)
        },
        {
            title: "Mgmt IP",
            dataIndex: "mgt_ip",
            width: "19%",
            render: (_, record) => {
                if (!record.mgt_ip) return <span>--</span>;
                let iconComponent;
                if (record?.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record?.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }
                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.mgt_ip}
                    </Space>
                );
            },
            sorter: (a, b) => a.mgt_ip.localeCompare(b.mgt_ip)
        },
        {
            title: "Model",
            dataIndex: "platform_model",
            width: "17%",
            render: (_, record) => <span>{record.platform_model || "--"}</span>,
            sorter: (a, b) => a.platform_model.localeCompare(b.platform_model)
        },
        {
            title: "Site",
            dataIndex: "site",
            width: "13%",
            render: (_, record) => <span>{record.site || "--"}</span>,
            sorter: (a, b) => a.site.localeCompare(b.site)
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                confirmModalAction("Are you sure you want to delete this switch?", () => {
                                    try {
                                        handleDeleteServer(record);
                                        message.success("Delete Successful");
                                    } catch (error) {
                                        message.error("Delete Failed");
                                    }
                                });
                            }}
                        >
                            Delete
                        </a>
                    </Space>
                );
            }
        }
    ];

    return isShowModal ? (
        <Modal
            className="ampcon-max-modal"
            title={
                <div>
                    Assign Template
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            form.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button type="primary" onClick={handleApply}>
                        Apply
                    </Button>
                </div>
            }
        >
            <Flex vertical flex={1} style={{height: "100%", paddingRight: "5px"}}>
                <div
                    style={{
                        display: "flex",
                        flexDirection: "column",
                        backgroundColor: "#F8FAFB",
                        width: "455px",
                        paddingLeft: "18px",
                        paddingBottom: "35px"
                    }}
                >
                    <h2>{templateName}</h2>
                    <p>Switches Assosicated with the Template</p>
                    <Flex direction="column" gap="8px" style={{marginTop: "25px"}}>
                        <div
                            style={{
                                width: 206,
                                background: "#F2F6F7",
                                minHeight: 32,
                                padding: "4px 11px",
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                                fontSize: 14,
                                color: "rgba(0, 0, 0, 0.88)"
                            }}
                        >
                            <span>{switchList?.length ?? 0}</span>
                            <span>Switches</span>
                        </div>
                    </Flex>
                </div>
                <h2 style={{fontSize: "18px"}}>Switches</h2>
                <AssignTemplateAddModal
                    ref={AssignTemplateAddModalRef}
                    switchData={switchList}
                    handleAddSwitch={handleAddSwitch}
                    templateName={templateName}
                />
                <div style={{marginBottom: "-8px"}}>
                    <a
                        style={{
                            display: "inline-flex",
                            alignItems: "center",
                            border: "none",
                            borderRadius: "4px",
                            color: "#14c9bb"
                        }}
                        onClick={() => {
                            AssignTemplateAddModalRef.current.showAddModal();
                        }}
                    >
                        <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                        Switch
                    </a>
                </div>
                <div style={{marginTop: "21px"}}>
                    {/* <Flex justify="flex-end" style={{marginBottom: 20, marginTop: -12}}>
                        <Input
                            placeholder="Search"
                            allowClear
                            onSearch={value => setSearchValue(value)}
                            onChange={e => setSearchValue(e.target.value)}
                            style={{width: 280}}
                            prefix={<Icon component={searchSvg} />}
                        />
                    </Flex> */}
                    <Table
                        ref={assignTableRef}
                        columns={columns}
                        bordered
                        style={tableStyle}
                        dataSource={pagedData}
                        pagination={{
                            current: currentPage,
                            pageSize,
                            showSizeChanger: true,
                            pageSizeOptions: ["10", "20", "50", "100"],
                            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
                            total: filteredSwitchList.length,
                            onChange: (page, size) => {
                                setCurrentPage(page);
                                setPageSize(size);
                            }
                        }}
                    />
                </div>
                <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
            </Flex>
        </Modal>
    ) : null;
});
export default AssignTemplateModal;
