import {useState, useRef, useEffect} from "react";
import {But<PERSON>, Card, Form, Input, Flex, Divider, Collapse, message, Spin} from "antd";
import style from "@/modules-ampcon/pages/Topo/SwitchTemplates/switch_templates.module.scss";
import NtpForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/ntp_form";
import DnsForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/dns_form";
import StaticRouteForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/static_route_form";
import OspfForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/ospf_form";
import NetworkForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/network_form";
import VrfForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/vrf_form";
import PortConfigForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/port_config_form";
import PortConfigAppForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/port_config_app_form";
import LocalUserForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/local_user_form";
import AssignTemplateModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/assign_template_modal";
import ReactDOM from "react-dom";
import {configDeploy, getAddedSwitch} from "@/modules-ampcon/apis/campus_blueprint_api";
import {useNavigate, useLocation} from "react-router-dom";
import {checkDuplicateName} from "@/modules-ampcon/apis/campus_blueprint_api";
import {debounce} from "lodash";
const SwitchTemplatesCreate = () => {
    const ntpFormRef = useRef(null);
    const dnsFormRef = useRef(null);
    const staticRouteFormRef = useRef(null);
    const ospfFormRef = useRef(null);
    const networkFormRef = useRef(null);
    const vrfFormRef = useRef(null);
    const portConfigFormRef = useRef(null);
    const portConfigAppFormRef = useRef(null);
    const localUserFormRef = useRef(null);
    const assignTemplateModalRef = useRef(null);
    const textAreaBeforeRef = useRef(null);
    const textAreaAfterRef = useRef(null);
    const [maxContainerHeight, setMaxContainerHeight] = useState(null);
    const [isFocused, setIsFocused] = useState(false);
    const inputRef = useRef(null);
    const tableStyle = {
        maxWidth: "45%",
        minWidth: "650px"
    };
    const {TextArea} = Input;
    const [form] = Form.useForm();
    const [nameForm] = Form.useForm();
    const [isLoading, setIsLoading] = useState(false);
    const navigate = useNavigate();
    const {state} = useLocation();
    const templateName = state?.name || "";
    const [switchTemplateData, setSwitchTemplateData] = useState({
        name: "",
        scope: {
            switches: 0,
            switchList: []
        },
        switch: {
            network: {
                networks: []
            },
            ntp: {
                ip: []
            },
            dns: {
                ip: []
            },
            staticRoute: {
                configuration: false,
                routes: []
            },
            ospf: {
                configuration: false,
                areas: []
            },
            vrf: {
                networks: []
            },
            portConfiguration: [
                {
                    name: "Default",
                    portEnable: "enable",
                    description: "",
                    portMode: "access",
                    portNetwork: "vlan1",
                    speed: "auto",
                    poe: "disable",
                    stormControl: "disable"
                }
            ],
            portConfigApplication: []
        },
        management: {
            localUser: {
                users: []
            },
            loginBanner: {},
            timeout: ""
        }
    });
    const handleDeleteNetwork = deleteNetwork => {
        setSwitchTemplateData(prev => {
            const networkIdentifier = `${deleteNetwork.name}(${deleteNetwork.id})`;

            const updatedNetworks = prev.switch.network.networks.filter(network => network.name !== deleteNetwork.name);
            const updatedVrf = {
                networks: (prev.switch.vrf.networks || []).filter(vrf => !vrf.id?.some(id => id === networkIdentifier))
            };

            const deletedPortProfiles = (prev.switch.portConfiguration || [])
                .filter(port => {
                    if (port.portNetwork === networkIdentifier) return true;
                    if (port.trunkNetwork) {
                        return Array.isArray(port.trunkNetwork)
                            ? port.trunkNetwork.includes(networkIdentifier)
                            : port.trunkNetwork === networkIdentifier;
                    }
                    return false;
                })
                .map(port => port.name);

            const updatedPortConfig = (prev.switch.portConfiguration || []).filter(
                port => !deletedPortProfiles.includes(port.name)
            );
            const updatedPortApp = (prev.switch.portConfigApplication || []).filter(
                app => !deletedPortProfiles.includes(app.profile)
            );
            return {
                ...prev,
                switch: {
                    ...prev.switch,
                    network: {networks: updatedNetworks},
                    vrf: updatedVrf,
                    portConfiguration: updatedPortConfig,
                    portConfigApplication: updatedPortApp
                }
            };
        });
    };
    const backToTableViewCallback = () => {
        navigate("/network_design/switch_templates");
    };
    const items = [
        {
            key: "1",
            label: <h2 style={{marginLeft: "8px"}}>Switch</h2>,
            children: (
                <>
                    <NetworkForm
                        ref={networkFormRef}
                        tableStyle={tableStyle}
                        setSwitchTemplateData={setSwitchTemplateData}
                        data={switchTemplateData.switch.network?.networks}
                        onDeleteNetwork={handleDeleteNetwork}
                        vrfData={switchTemplateData.switch.vrf}
                    />
                    <VrfForm
                        ref={vrfFormRef}
                        vrfData={switchTemplateData.switch.vrf}
                        tableStyle={tableStyle}
                        networkData={switchTemplateData.switch.network?.networks}
                        setSwitchTemplateData={setSwitchTemplateData}
                    />
                    <NtpForm
                        ref={ntpFormRef}
                        data={switchTemplateData.switch.ntp}
                        setSwitchTemplateData={setSwitchTemplateData}
                    />
                    <DnsForm
                        ref={dnsFormRef}
                        data={switchTemplateData.switch.dns}
                        setSwitchTemplateData={setSwitchTemplateData}
                    />
                    <StaticRouteForm
                        ref={staticRouteFormRef}
                        staticRouteData={switchTemplateData.switch.staticRoute}
                        tableStyle={tableStyle}
                        setSwitchTemplateData={setSwitchTemplateData}
                    />
                    <OspfForm
                        ref={ospfFormRef}
                        ospData={switchTemplateData.switch.ospf}
                        tableStyle={tableStyle}
                        setSwitchTemplateData={setSwitchTemplateData}
                    />
                    <PortConfigForm
                        ref={portConfigFormRef}
                        portConfigData={switchTemplateData.switch.portConfiguration}
                        tableStyle={tableStyle}
                        setSwitchTemplateData={setSwitchTemplateData}
                        networkData={switchTemplateData.switch.network?.networks}
                    />
                    <PortConfigAppForm
                        ref={portConfigAppFormRef}
                        portConfigAppData={switchTemplateData.switch.portConfigApplication}
                        tableStyle={tableStyle}
                        setSwitchTemplateData={setSwitchTemplateData}
                        portConfigData={switchTemplateData.switch.portConfiguration}
                    />
                </>
            )
        },
        {
            key: "2",
            label: <h2 style={{marginLeft: "8px"}}>Management</h2>,
            children: (
                <>
                    <LocalUserForm
                        ref={localUserFormRef}
                        userData={switchTemplateData.management.localUser}
                        tableStyle={tableStyle}
                        setSwitchTemplateData={setSwitchTemplateData}
                    />
                    <h2 className={style.title}>Login Banner</h2>
                    <Form
                        layout="horizontal"
                        labelAlign="left"
                        labelCol={{flex: "140px"}}
                        wrapperCol={{flex: "250px"}}
                        labelWrap
                        className="label-wrap"
                        form={form}
                        style={{minHeight: "267.23px"}}
                        ref={form}
                        validateTrigger={["onBlur", "onSubmit"]}
                    >
                        <Form.Item
                            name="before"
                            validateFirst
                            label="Banner before Login"
                            rules={[
                                {
                                    validator: (_, value) => {
                                        if (!value) return Promise.resolve();
                                        const el = textAreaBeforeRef.current?.resizableTextArea?.textArea;
                                        if (el) {
                                            if (el.scrollHeight > 450) {
                                                return Promise.reject(
                                                    new Error("The Banner before Login cannot exceed 20 lines.")
                                                );
                                            }
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        >
                            <TextArea style={{width: "280px", height: "102px"}} ref={textAreaBeforeRef} />
                        </Form.Item>
                        <Form.Item
                            name="after"
                            validateFirst
                            label="Banner after Login"
                            rules={[
                                {
                                    validator: (_, value) => {
                                        if (!value) return Promise.resolve();
                                        const el = textAreaAfterRef.current?.resizableTextArea?.textArea;
                                        if (el) {
                                            if (el.scrollHeight > 450) {
                                                return Promise.reject(
                                                    new Error("The Banner after Login cannot exceed 20 lines.")
                                                );
                                            }
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        >
                            <TextArea style={{width: "280px", height: "102px"}} ref={textAreaAfterRef} />
                        </Form.Item>
                        <Divider className={style.divider} />
                        <h2 style={{marginTop: "-4px", fontSize: "18px"}}>
                            Idle Timeout <span style={{fontWeight: "300"}}>(in Minutes)</span>
                        </h2>
                        <Form.Item
                            name="timeout"
                            validateFirst
                            label="Idle Timeout"
                            rules={[
                                {
                                    validator: (_, value) => {
                                        if (value === undefined || value === "") {
                                            return Promise.resolve();
                                        }
                                        const valueStr = String(value).trim();
                                        if (!/^(0|[1-9]\d{0,4})$/.test(valueStr)) {
                                            return Promise.reject(
                                                new Error(
                                                    "The Idle Timeout must be between 0 and 20000. If you do not specify it, the default is 0."
                                                )
                                            );
                                        }
                                        const numberValue = Number(valueStr);
                                        if (numberValue < 0 || numberValue > 20000) {
                                            return Promise.reject(
                                                new Error(
                                                    "The Idle Timeout must be between 0 and 20000. If you do not specify it, the default is 0."
                                                )
                                            );
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        >
                            <Input
                                style={{width: "280px"}}
                                ref={inputRef}
                                onFocus={() => setIsFocused(true)}
                                onBlur={() => setIsFocused(false)}
                            />
                        </Form.Item>
                        <CustomTooltip visible={isFocused} targetRef={inputRef} />
                    </Form>
                </>
            )
        }
    ];
    const checkNameApiDebounced = debounce(async (name, resolve, reject) => {
        try {
            const res = await checkDuplicateName(name, null);
            if (res.status === 200) {
                resolve();
            } else if (res.status === 400) {
                reject(new Error(res.info || "The template name already exists!"));
            } else {
                reject(new Error("The template name is invalid."));
            }
        } catch (e) {
            reject(new Error("The template name is invalid."));
        }
    }, 100);

    const validateName = (_, value) => {
        if (!value) return Promise.resolve();
        return new Promise((resolve, reject) => {
            checkNameApiDebounced(value, resolve, reject);
        });
    };
    useEffect(() => {
        if (templateName) {
            nameForm.setFieldsValue({template_name: templateName});
        }
        const handleResize = () => {
            setMaxContainerHeight("calc(100vh - 200px)");
        };
        handleResize();
        window.addEventListener("resize", handleResize);
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, [templateName]);

    const handleApplySwitchList = updatedSwitchList => {
        setSwitchTemplateData(prev => ({
            ...prev,
            scope: {
                ...prev.scope,
                switchList: updatedSwitchList,
                switches: updatedSwitchList.length
            }
        }));
    };
    const handleSave = async () => {
        try {
            if (!switchTemplateData.scope.switchList || switchTemplateData.scope.switchList.length === 0) {
                message.error("Please select at least one switch before saving the template.");
                return;
            }
            const isNtpValid = await ntpFormRef.current?.validate();
            const isDnsValid = await dnsFormRef.current?.validate();
            const isStaticRouteValid = await staticRouteFormRef.current?.validate();
            const isOspfValid = await ospfFormRef.current?.validate();
            if (!isNtpValid || !isDnsValid || !isStaticRouteValid || !isOspfValid) {
                return;
            }
            const [nameValues, bannerValues] = await Promise.all([nameForm.validateFields(), form.validateFields()]);
            setIsLoading(true);
            const cleanedNtpIps = (switchTemplateData.switch.ntp?.ip || []).filter(
                ip => typeof ip === "string" && ip.trim() !== ""
            );
            const cleanedDnsIps = (switchTemplateData.switch.dns?.ip || []).filter(
                ip => typeof ip === "string" && ip.trim() !== ""
            );
            const updatedOspfAreas = switchTemplateData.switch.ospf.areas.map(area => ({
                ...area,
                type: area.type.toLowerCase()
            }));
            const defaultRecord = {
                name: "Default",
                portEnable: "enable",
                description: "",
                mode: "access",
                portNetwork: "vlan1",
                speed: "auto",
                poe: "disable",
                stormControl: "disable"
            };
            const portConfiguration = switchTemplateData.switch.portConfiguration || [];
            const hasDefault = portConfiguration.some(port => port.name === "Default");
            const updatedPortConfig = hasDefault ? portConfiguration : [...portConfiguration, defaultRecord];
            const updatedData = {
                ...switchTemplateData,
                name: nameValues.template_name,
                scope: {
                    ...switchTemplateData.scope,
                    switchList: switchTemplateData.scope.switchList
                },
                switch: {
                    ...switchTemplateData.switch,
                    ntp: {
                        ...switchTemplateData.switch.ntp,
                        ip: cleanedNtpIps
                    },
                    dns: {
                        ...switchTemplateData.switch.dns,
                        ip: cleanedDnsIps
                    },
                    ospf: {
                        ...switchTemplateData.switch.ospf,
                        areas: updatedOspfAreas
                    },
                    portConfiguration: updatedPortConfig
                },
                management: {
                    ...switchTemplateData.management,
                    loginBanner: {
                        before: bannerValues.before,
                        after: bannerValues.after
                    },
                    timeout: bannerValues.timeout || 0
                }
            };
            const res = await configDeploy(updatedData);
            if (res.status === 200) {
                setIsLoading(false);
                message.success("Template saved successfully");
                setSwitchTemplateData(updatedData);
                navigate("/network_design/switch_templates");
            } else {
                setIsLoading(false);
                message.error(res.info);
            }
        } catch (error) {
            setIsLoading(false);
            console.error("Error saving template:", error);
        }
    };
    return (
        <>
            <Spin spinning={isLoading} tip="Loading..." fullscreen />
            <Card style={{display: "flex", flexDirection: "column", flex: 1}}>
                <Flex
                    vertical
                    flex={1}
                    style={{height: "100%", maxHeight: maxContainerHeight, overflowY: "scroll", paddingRight: "5px"}}
                >
                    <h2 style={{margin: "8px 0 20px"}}>Template Application</h2>
                    <Form
                        labelAlign="left"
                        labelCol={{flex: "120px"}}
                        wrapperCol={{flex: "280px"}}
                        form={nameForm}
                        validateTrigger={["onChange", "onSubmit"]}
                    >
                        <Form.Item
                            name="template_name"
                            label="Template Name"
                            rules={[
                                {required: true, message: "Template Name is required."},
                                {
                                    max: 64,
                                    message: "Template name cannot exceed 64 characters."
                                },
                                {
                                    validator: (_, value) => {
                                        if (/\s/.test(value)) {
                                            return Promise.reject(new Error("Template name cannot contain spaces!"));
                                        }
                                        return Promise.resolve();
                                    }
                                },
                                {validator: validateName}
                            ]}
                        >
                            <Input />
                        </Form.Item>
                    </Form>
                    <Divider style={{marginTop: "1px "}} />
                    <h2 style={{margin: "-5px 0 20px"}}>Template Application Scope</h2>
                    <Form labelAlign="left">
                        <Flex direction="column" gap="8px">
                            <div
                                style={{
                                    width: 206,
                                    background: "#F8FAFB",
                                    minHeight: 40,
                                    padding: "4px 11px",
                                    display: "flex",
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                    fontSize: 14,
                                    color: "rgba(0, 0, 0, 0.88)"
                                }}
                            >
                                <span>{switchTemplateData.scope.switches ?? 0}</span>
                                <span>Switches</span>
                            </div>
                        </Flex>
                    </Form>
                    <Button
                        type="primary"
                        onClick={async () => {
                            const snList = switchTemplateData.scope.switchList;
                            try {
                                const res = await getAddedSwitch(snList, 1, snList.length, {
                                    fields: ["host_name", "sn", "mgt_ip", "platform_model"],
                                    value: ""
                                });
                                if (res?.status === 200 && Array.isArray(res.data)) {
                                    assignTemplateModalRef.current.showAssignTemplateModal(res.data, templateName);
                                } else {
                                    message.error("Failed to fetch switches");
                                }
                            } catch (error) {
                                console.error("Error fetching switches:", error);
                                message.error("Error occurred while fetching switches");
                            }
                        }}
                        style={{marginTop: "24px", width: "133px"}}
                    >
                        Assign Template
                    </Button>
                    <Divider />
                    <Collapse
                        size="small"
                        items={items}
                        defaultActiveKey={["1", "2"]}
                        expandIconPosition="end"
                        style={{background: "#F8FAFB", marginBottom: 30}}
                        destroyInactivePanel={false}
                        className="customCollapse"
                    />
                    <div
                        style={{
                            borderTop: "1px solid #F0F0F0",
                            margin: "12px auto 0",
                            position: "absolute",
                            bottom: "63px",
                            left: 0,
                            right: 0
                        }}
                    />
                    <Flex
                        style={{
                            marginTop: 24,
                            position: "absolute",
                            bottom: "16px",
                            right: "10px",
                            flexDirection: "row-reverse",
                            width: "100%",
                            paddingRight: "24px",
                            paddingTop: "16px",
                            gap: "16px"
                        }}
                    >
                        <Button key="ok" type="primary" onClick={handleSave}>
                            Apply
                        </Button>
                        <Button key="cancel" onClick={backToTableViewCallback}>
                            Cancel
                        </Button>
                    </Flex>
                </Flex>
                <AssignTemplateModal
                    ref={assignTemplateModalRef}
                    onApply={handleApplySwitchList}
                    data={switchTemplateData.scope}
                />
            </Card>
        </>
    );
};

export default SwitchTemplatesCreate;
const CustomTooltip = ({visible, targetRef}) => {
    const [position, setPosition] = useState({top: 0, left: 0});

    const updatePosition = () => {
        if (targetRef?.current?.input) {
            const rect = targetRef.current.input.getBoundingClientRect();
            setPosition({
                top: rect.top + window.scrollY - 15,
                left: rect.right + 15
            });
        }
    };

    useEffect(() => {
        if (!visible) return;
        updatePosition();
        window.addEventListener("resize", updatePosition);
        window.addEventListener("scroll", updatePosition);
        return () => {
            window.removeEventListener("resize", updatePosition);
            window.removeEventListener("scroll", updatePosition);
        };
    }, [visible]);

    if (!visible) return null;
    const style = {
        position: "absolute",
        top: position.top,
        left: position.left,
        zIndex: 9999,
        width: "184px",
        background: "#646569",
        color: "#fff",
        padding: "8px 12px",
        borderRadius: 6,
        fontSize: 12,
        boxShadow: "0 3px 6px rgba(0,0,0,0.2)",
        lineHeight: 1.6
    };
    return ReactDOM.createPortal(
        <div style={style}>
            The Idle Timeout must be between 0 and 20000. If you do not specify it, the default is 0.
        </div>,
        document.body
    );
};
