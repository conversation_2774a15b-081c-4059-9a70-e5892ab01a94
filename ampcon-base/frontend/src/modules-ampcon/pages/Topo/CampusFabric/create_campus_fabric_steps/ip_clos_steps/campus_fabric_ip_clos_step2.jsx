import React, {useRef, useEffect, useState, forwardRef, useImperativeHandle} from "react";
import {Card, message, Flex, Checkbox} from "antd";
import BoarderNodeSelector from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/border_node_selector";
import CoreNodeSelector from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/core_node_selector";
import PodsPanel from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/pods_panel";
import DeviceBriefTooltip from "@/modules-ampcon/pages/Topo/CampusFabric/tooltip/device_brief_tooltip";
import AddDeviceModal from "@/modules-ampcon/pages/Topo/CampusFabric/modal/add_ipclos_device_modal";
import {fetchTopologyToBeAddedSwitch} from "@/modules-ampcon/apis/monitor_api";
import ip from "ip";
import {viewSiteTopo, getSwitchStatus} from "@/modules-ampcon/apis/campus_blueprint_api";

const CampusFabricIPClosStep2 = forwardRef(
    ({campusFabricData, setCampusFabricData, allModelPhysicPortInfo, isEditing}, ref) => {
        const selectCampusFabricNodesTitle = "Select Campus Fabric Nodes";

        const podPanelRef = useRef();
        const deviceBriefTooltipRef = useRef();
        const addDeviceModalRef = useRef(null);
        const [useCoreAsBorder, setUseCoreAsBorder] = useState(campusFabricData.nodes.border.length === 0);

        const isStep2DataEmpty = () => {
            return (
                campusFabricData.nodes.border.length === 0 &&
                campusFabricData.nodes.core.length === 0 &&
                campusFabricData.nodes.pods.filter(pod => pod !== null).length === 1 &&
                campusFabricData.nodes.pods.filter(pod => pod !== null)[0].access.length === 0 &&
                campusFabricData.nodes.pods.filter(pod => pod !== null)[0].distribution.length === 0
            );
        };

        const [isHidePodValid, setIsHidePodValid] = useState(isStep2DataEmpty());

        useEffect(() => {
            setUseCoreAsBorder(campusFabricData.nodes.border.length === 0);
        }, [campusFabricData.nodes.border.length]);

        const onCheckboxChange = e => {
            const {checked} = e.target;
            setUseCoreAsBorder(checked);

            if (checked) {
                const temp = {
                    ...campusFabricData,
                    nodes: {
                        ...campusFabricData.nodes,
                        border: [],
                        core: campusFabricData.nodes.core.map(coreNode => ({
                            ...coreNode,
                            links: {
                                ...coreNode.links,
                                to_border: {}
                            }
                        }))
                    }
                };
                setCampusFabricData(temp);
            }
        };

        let snList = [];
        let otnList = [];

        const distributeIPForAllNodes = () => {
            const subnet = ip.cidrSubnet(campusFabricData.topology.loopback_prefix);
            let startIP = ip.toLong(subnet.networkAddress) + 1;

            const getNextIP = currentIP => {
                let nextIP = currentIP + 1;
                while ((nextIP & 0xff) === 0) {
                    nextIP++;
                }
                return nextIP;
            };

            if (campusFabricData.nodes.border.length > 0) {
                campusFabricData.nodes.border.forEach(node => {
                    node.router_id = ip.fromLong(startIP);
                    startIP = getNextIP(startIP);
                });
            }

            campusFabricData.nodes.core.forEach(node => {
                node.router_id = ip.fromLong(startIP);
                startIP = getNextIP(startIP);
            });

            campusFabricData.nodes.pods.forEach(pod => {
                if (pod) {
                    pod.distribution.forEach(device => {
                        device.router_id = ip.fromLong(startIP);
                        startIP = getNextIP(startIP);
                    });
                    pod.access.forEach(device => {
                        device.router_id = ip.fromLong(startIP);
                        startIP = getNextIP(startIP);
                    });
                }
            });
        };

        useImperativeHandle(ref, () => ({
            validate: async () => {
                setIsHidePodValid(false);
                campusFabricData.nodes.pods = campusFabricData.nodes.pods.filter(pod => pod !== null);

                if (!useCoreAsBorder && campusFabricData.nodes.border.length === 0) {
                    message.error("Please add at least one border switch.");
                    return false;
                }

                if (campusFabricData.nodes.core.length !== 2) {
                    message.error("Please add two core switches.");
                    return false;
                }

                distributeIPForAllNodes();
                return podPanelRef.current.validate();
            }
        }));

        const addDeviceCallback = (type, index) => {
            campusFabricData.nodes.border.forEach(node => snList.push(node.switch_sn));
            campusFabricData.nodes.core.forEach(node => snList.push(node.switch_sn));

            campusFabricData.nodes.pods
                .filter(pod => pod != null)
                .forEach(pod => {
                    pod.distribution.forEach(device => snList.push(device.switch_sn));
                    pod.access.forEach(device => snList.push(device.switch_sn));
                });

            snList = snList.filter(sn => sn != null);
            otnList = otnList.filter(otn => otn != null);

            snList = [...new Set(snList)];
            otnList = [...new Set(otnList)];

            const coreNodesCount = campusFabricData.nodes.core.length;

            addDeviceModalRef.current.showAddDeviceModal(
                snList,
                otnList,
                campusFabricData.site_id,
                type,
                coreNodesCount,
                index
            );
        };

        const updateSwitchStatus = async () => {
            const snlist = campusFabricData.nodes.border
                .concat(campusFabricData.nodes.core)
                .concat(campusFabricData.nodes.pods.filter(pod => pod !== null).flatMap(pod => pod.distribution))
                .concat(campusFabricData.nodes.pods.filter(pod => pod !== null).flatMap(pod => pod.access))
                .map(node => node.switch_sn);
            const res = await getSwitchStatus(snlist);
            if (res.status === 200) {
                const switchStatus = res.data;
                const temp = JSON.parse(JSON.stringify(campusFabricData));
                temp.nodes.border.forEach(node => {
                    const status = switchStatus[node.switch_sn];
                    if (status) {
                        node.status = status === 0 ? "online" : "offline";
                    }
                });
                temp.nodes.core.forEach(node => {
                    const status = switchStatus[node.switch_sn];
                    if (status) {
                        node.status = status === 0 ? "online" : "offline";
                    }
                });
                temp.nodes.pods.forEach(pod => {
                    if (pod === null) {
                        return;
                    }
                    pod.distribution.forEach(node => {
                        const status = switchStatus[node.switch_sn];
                        if (status) {
                            node.status = status === 0 ? "online" : "offline";
                        }
                    });
                    pod.access.forEach(node => {
                        const status = switchStatus[node.switch_sn];
                        if (status) {
                            node.status = status === 0 ? "online" : "offline";
                        }
                    });
                });

                setCampusFabricData(temp);
            } else {
                message.error(res.info);
            }
        };

        useEffect(() => {
            if (isEditing) {
                updateSwitchStatus();
            }
        }, []);

        const updateNodesCallback = async (actions, type, index) => {
            const addIdList = [];
            for (const [id, action] of Object.entries(actions)) {
                if (action === "add") {
                    addIdList.push(id);
                }
            }

            if (addIdList.length === 0) {
                if (type === "border") {
                    if (campusFabricData.nodes.border.length === 0) {
                        message.error("Please add at least one border switch.");
                        return;
                    }
                    addDeviceModalRef.current.hideAddDeviceModal();
                    return;
                }
                if (type === "core") {
                    if (campusFabricData.nodes.core.length === 0) {
                        message.error("Please add at least one core switch.");
                        return;
                    }
                    addDeviceModalRef.current.hideAddDeviceModal();
                    return;
                }
                if (type === "access" && campusFabricData.nodes.pods[index]) {
                    if (campusFabricData.nodes.pods[index].access.length === 0) {
                        if (campusFabricData.nodes.pods.length === 1) {
                            message.error("Please add at least two access switches.");
                            return;
                        }
                        message.error("Please add at least one access switch.");
                        return;
                    }
                    addDeviceModalRef.current.hideAddDeviceModal();
                    return;
                }
                if (type === "distribution" && campusFabricData.nodes.pods[index]) {
                    if (campusFabricData.nodes.pods[index].distribution.length === 0) {
                        message.error("Please add at least one distribution switch.");
                        return;
                    }
                    addDeviceModalRef.current.hideAddDeviceModal();
                }
            } else if (addIdList.length > 0) {
                if (
                    type === "access" &&
                    addIdList.length === 1 &&
                    campusFabricData.nodes.pods[index] &&
                    campusFabricData.nodes.pods.length === 1
                ) {
                    if (campusFabricData.nodes.pods[index].access.length === 0) {
                        message.error("Please add at least two access switches.");
                        return;
                    }
                    addDeviceModalRef.current.hideAddDeviceModal();
                }
                try {
                    const response = await fetchTopologyToBeAddedSwitch(addIdList);
                    if (response.status !== 200) {
                        message.error(response.info);
                        return;
                    }
                    const newNode = response.data;
                    const temp = JSON.parse(JSON.stringify(campusFabricData));

                    const isPodType = type === "access" || type === "distribution";
                    const targetNode = isPodType ? temp.nodes.pods[index][type] : temp.nodes[type];

                    newNode.forEach(node => {
                        node.type = type;
                    });

                    if (targetNode) {
                        targetNode.push(...newNode);
                    }
                    // clear all links
                    setCampusFabricData(getClearedAllLinksData(temp));
                    addDeviceModalRef.current.hideAddDeviceModal();
                } catch (error) {
                    message.error("Failed to add nodes. Please try again.");
                }
            }
        };

        const addBorderSwitchCallback = async () => {
            addDeviceCallback("border");
            hideDeviceBriefTooltipCallback();
        };

        const deleteBorderSwitchCallback = nodeSN => {
            const temp = JSON.parse(JSON.stringify(campusFabricData));
            temp.nodes.border = temp.nodes.border.filter(node => node.switch_sn !== nodeSN);
            setCampusFabricData(getClearedAllLinksData(temp));
            hideDeviceBriefTooltipCallback();
        };

        const addCoreSwitchCallback = () => {
            addDeviceCallback("core");
            hideDeviceBriefTooltipCallback();
        };

        const deleteCoreSwitchCallback = nodeSN => {
            const temp = JSON.parse(JSON.stringify(campusFabricData));
            temp.nodes.core = temp.nodes.core.filter(node => node.switch_sn !== nodeSN);
            setCampusFabricData(getClearedAllLinksData(temp));
            hideDeviceBriefTooltipCallback();
        };

        const addDistributionSwitchCallback = index => {
            addDeviceCallback("distribution", index);
            hideDeviceBriefTooltipCallback();
        };

        const deleteDistributionSwitchCallback = (index, nodeSN) => {
            const temp = JSON.parse(JSON.stringify(campusFabricData));
            temp.nodes.pods[index].distribution = temp.nodes.pods[index].distribution.filter(
                node => node.switch_sn !== nodeSN
            );
            setCampusFabricData(getClearedAllLinksData(temp));
            hideDeviceBriefTooltipCallback();
        };

        const addAccessSwitchCallback = index => {
            addDeviceCallback("access", index);
            hideDeviceBriefTooltipCallback();
        };

        const deleteAccessSwitchCallback = (index, nodeSN) => {
            const temp = JSON.parse(JSON.stringify(campusFabricData));
            temp.nodes.pods[index].access = temp.nodes.pods[index].access.filter(node => node.switch_sn !== nodeSN);
            setCampusFabricData(getClearedAllLinksData(temp));
            hideDeviceBriefTooltipCallback();
        };

        const editPodNameCallback = (index, podName) => {
            const temp = JSON.parse(JSON.stringify(campusFabricData));
            temp.nodes.pods[index].podName = podName;
            setCampusFabricData(temp);
            hideDeviceBriefTooltipCallback();
        };

        const addPodCallback = () => {
            const emptyPod = {
                podName: "",
                distribution: [],
                access: []
            };
            const temp = JSON.parse(JSON.stringify(campusFabricData));
            temp.nodes.pods.push(emptyPod);
            setCampusFabricData(temp);
            hideDeviceBriefTooltipCallback();
            setTimeout(() => {
                if (podPanelRef.current) {
                    podPanelRef.current.validatePodNameField();
                }
            }, 50);
        };

        const deletePodCallback = index => {
            if (campusFabricData.nodes.pods.filter(pod => pod !== null).length === 1) {
                message.error("At least one pod is required");
                return;
            }
            const temp = JSON.parse(JSON.stringify(campusFabricData));
            temp.nodes.pods[index] = null;
            setCampusFabricData(getClearedAllLinksData(temp));
            hideDeviceBriefTooltipCallback();
            setTimeout(() => {
                if (podPanelRef.current) {
                    podPanelRef.current.validatePodNameField();
                }
            }, 50);
        };

        const showDeviceBriefTooltipCallback = node => {
            deviceBriefTooltipRef.current.showDeviceBriefTooltip(
                {
                    "Switch Name": node.label,
                    "MAC address": node.mac_addr,
                    Model: node.model,
                    Status: node.status,
                    Site: campusFabricData.site_id,
                    SN: node.switch_sn
                },
                {type: "create", topologyType: "ip-clos"}
            );
        };

        const hideDeviceBriefTooltipCallback = () => {
            deviceBriefTooltipRef.current.hideDeviceBriefTooltip();
        };

        const getClearedBorderAllLinksData = data => {
            data.nodes.border.forEach(node => {
                node.links = {
                    to_core: {},
                    to_wan: {}
                };
            });
            return data;
        };

        const getClearedCoreAllLinksData = data => {
            data.nodes.core.forEach(node => {
                node.links = {
                    to_border: {},
                    to_access: {},
                    to_dis: {},
                    to_wan: {},
                    to_core: {}
                };
            });
            return data;
        };

        const getClearedPodAllLinksData = data => {
            data.nodes.pods.forEach(pod => {
                if (pod === null) {
                    return;
                }
                pod.distribution.forEach(distribution => {
                    distribution.links = {
                        to_core: {},
                        to_access: {}
                    };
                });
                pod.access.forEach(access => {
                    access.links = {
                        to_dis: {},
                        to_access: {}
                    };
                });
            });
            return data;
        };

        const getClearedAllLinksData = data => {
            let temp = JSON.parse(JSON.stringify(data));
            temp = getClearedBorderAllLinksData(temp);
            temp = getClearedCoreAllLinksData(temp);
            temp = getClearedPodAllLinksData(temp);
            return temp;
        };

        const isSinglePodAccessNumValid = pod => {
            return !(
                (pod.access.length === 1 || pod.access.length === 0) &&
                campusFabricData.nodes.pods.filter(pod => {
                    return pod !== null;
                }).length === 1
            );
        };

        return (
            <>
                <DeviceBriefTooltip ref={deviceBriefTooltipRef} />
                <AddDeviceModal ref={addDeviceModalRef} updateNodesCallback={updateNodesCallback} />
                <Card style={{position: "relative"}}>
                    <h2>{selectCampusFabricNodesTitle}</h2>
                    <div style={{visibility: "hidden", height: "0px"}}>
                        <Flex align="center">
                            <h3 style={{marginRight: "10px"}}>Border</h3>
                            <Checkbox
                                checked={useCoreAsBorder}
                                onChange={onCheckboxChange}
                                style={{marginTop: "-20px", marginLeft: "30px"}}
                            >
                                Use core as border
                            </Checkbox>
                        </Flex>
                        {!useCoreAsBorder && (
                            <BoarderNodeSelector
                                nodes={campusFabricData.nodes.border}
                                addBorderSwitchCallback={addBorderSwitchCallback}
                                deleteBorderSwitchCallback={deleteBorderSwitchCallback}
                                showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                                hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
                            />
                        )}
                    </div>
                    <CoreNodeSelector
                        nodes={campusFabricData.nodes.core}
                        addCoreSwitchCallback={addCoreSwitchCallback}
                        deleteCoreSwitchCallback={deleteCoreSwitchCallback}
                        showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                        hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
                    />
                    <PodsPanel
                        ref={podPanelRef}
                        pods={campusFabricData.nodes.pods}
                        addDistributionSwitchCallback={addDistributionSwitchCallback}
                        deleteDistributionSwitchCallback={deleteDistributionSwitchCallback}
                        addAccessSwitchCallback={addAccessSwitchCallback}
                        deleteAccessSwitchCallback={deleteAccessSwitchCallback}
                        addPodCallback={addPodCallback}
                        editPodNameCallback={editPodNameCallback}
                        deletePodCallback={deletePodCallback}
                        showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                        hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
                        isSinglePodAccessNumValid={isSinglePodAccessNumValid}
                        isHidePodValid={isHidePodValid}
                    />
                </Card>
            </>
        );
    }
);

export default CampusFabricIPClosStep2;
