import React, {useState, useEffect, forwardRef, useImperativeHandle} from "react";
import {Form, Input, Divider, message, Radio} from "antd";
import {getAllSiteTopoInfo} from "@/modules-ampcon/apis/campus_blueprint_api";

const labelStyle = {
    fontFamily: "Lato, sans-serif",
    fontWeight: "400",
    fontSize: "14px",
    color: "#212519",
    textAlign: "left",
    fontStyle: "normal",
    textTransform: "none",
    width: "175px"
};

const inputStyle = {
    width: "280px",
    height: "36px"
};

const LoopbackPrefixTooltip = `The IP address range used to allocate Router IDs for each device in the fabric.
 Router IDs are loopback interfaces (lo0.0) for the route protocol peering between devices.`;

const SubnetTooltip = `IP Address pool for allocation to
underlay physical interfaces.`;

const ipv4NetRegex = /^((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}0\/(\d|[12]\d|3[0-2])$/;

let fetchDataTimeoutId = null;

const CampusFabricMLAGStep1 = forwardRef(
    ({campusFabricData, setCampusFabricData, isEditing, Header, siteForm, initialProtocol}, ref) => {
        const [isProtocol, setIsProtocol] = useState(campusFabricData.topology.protocol === "BGP");
        const [response, setResponse] = useState(null);
        const [originalTopologyName, setOriginalTopologyName] = useState("");
        const [inputValue, setInputValue] = useState("");
        const [hasModifiedASBase, setHasModifiedASBase] = useState(false);

        useEffect(() => {
            if ((initialProtocol === "OSPF" && isProtocol) || (initialProtocol === "BSP" && !isProtocol)) {
                setHasModifiedASBase(true);
            }
            if ((initialProtocol === "OSPF" && !isProtocol) || (initialProtocol === "BSP" && isProtocol)) {
                setHasModifiedASBase(false);
            }
        }, [isProtocol]);

        const fetchData = async () => {
            try {
                // 获取topoList数据
                const topoListResponse = await getAllSiteTopoInfo();
                const siteConfigId = campusFabricData.site_config_id;

                // 确保topoListResponse在被处理之前有数据
                if (topoListResponse && topoListResponse.data) {
                    const matchingConfig = topoListResponse.data.find(config => config.id === siteConfigId);
                    if (matchingConfig) {
                        setOriginalTopologyName(matchingConfig.topology_name); // 设置originalTopologyName
                    } else {
                        setOriginalTopologyName(""); // 未找到设置为空
                    }

                    setResponse(topoListResponse);
                } else {
                    message.error("Failed to fetch topo list data.");
                }
            } catch (error) {
                console.error("Error fetching topo list data:", error);
                message.error("An error occurred while fetching topo list data.");
            }
        };

        useEffect(() => {
            if (fetchDataTimeoutId) {
                clearTimeout(fetchDataTimeoutId);
            }
            fetchDataTimeoutId = setTimeout(() => {
                fetchData();
            }, 100);
        }, [campusFabricData]); // 在运行effect之前确保campusFabricData is available

        const initializeFormValues = data => {
            if (!data) return;
            siteForm.setFieldsValue({
                // site: data.site_id,
                topologyName: data.topology_name,
                site_config_id: data.site_config_id,
                asBase: data.topology.as_base,
                loopbackPrefix: data.topology.loopback_prefix,
                subnet: data.topology.subnet,
                protocol: data.topology.protocol,
                area_id: data.topology.area_id,
                inband_subnet: data.topology.inband_subnet
            });

            setIsProtocol(data.topology.protocol === "BGP");

            // 填充area_id，避免disabled情况下校验失败
            if (!isProtocol) {
                siteForm.setFieldsValue({area_id: "0.0.0.0"});
            }
        };

        useEffect(() => {
            initializeFormValues(campusFabricData);
        }, [campusFabricData, initialProtocol]);

        const handleFormChange = (changedValues, allValues) => {
            const isProtocolChanged =
                changedValues.topology?.protocol !== undefined &&
                changedValues.topology.protocol !== (isProtocol ? "OSPF" : "BGP");

            if (isProtocolChanged) {
                setIsProtocol(allValues.topology.protocol === "BGP");
            }

            const updatedData = {
                ...campusFabricData,
                topology_name: allValues.topologyName,
                site_config_id: campusFabricData.site_config_id,
                topology: {
                    ...campusFabricData.topology,
                    protocol: allValues.protocol,
                    area_id: allValues.area_id,
                    as_base: allValues.asBase !== undefined ? allValues.asBase : campusFabricData.topology.as_base, // 如果 asBase 未定义，则使用原始值
                    loopback_prefix: allValues.loopbackPrefix,
                    subnet: allValues.subnet,
                    inband_subnet: allValues.inband_subnet
                }
            };

            setCampusFabricData(updatedData);
        };

        useImperativeHandle(ref, () => ({
            validate: async () => {
                try {
                    await siteForm.validateFields();
                    // await siteForm.validateFields();
                    return true;
                } catch (error) {
                    return false;
                }
            }
        }));

        const ipToInt = ip => {
            const parts = ip.split(".").map(Number);
            return parts.reduce((acc, part, index) => acc + part * 256 ** (3 - index), 0);
        };

        const subnetToRange = subnet => {
            if (!subnet || !ipv4NetRegex.test(subnet)) {
                return {startInt: 0, endInt: 0};
            }
            const [baseIp, prefixLength] = subnet.split("/");
            const baseInt = ipToInt(baseIp);
            const maskSize = parseInt(prefixLength, 10);
            const totalIps = 2 ** (32 - maskSize);

            const mask = 2 ** maskSize - 1;
            const startInt = Math.floor(baseInt / 2 ** (32 - maskSize)) * 2 ** (32 - maskSize);

            const endInt = startInt + totalIps - 1;

            return {startInt, endInt};
        };

        const validateSubnets = (subnets, value) => {
            const validSubnets = [...new Set(subnets.filter(s => s && ipv4NetRegex.test(s)))];

            if (validSubnets.length < 2) return null;

            const ranges = validSubnets.map(subnet => {
                const range = subnetToRange(subnet);
                return {...range, subnet};
            });

            for (let i = 0; i < ranges.length; i++) {
                for (let j = i + 1; j < ranges.length; j++) {
                    const {startInt: s1, endInt: e1, subnet: subnet1} = ranges[i];
                    const {startInt: s2, endInt: e2, subnet: subnet2} = ranges[j];

                    if (s1 <= e2 && s2 <= e1) {
                        if (subnet1 !== value && subnet2 !== value) continue;

                        const fieldNames = {
                            [subnets[0]]: "Subnet",
                            [subnets[1]]: "Inband Subnet",
                            [subnets[2]]: "Route ID Subnet"
                        };
                        return `${fieldNames[subnet1]} and ${fieldNames[subnet2]} cannot overlap!`;
                    }
                }
            }

            return null;
        };

        const validateASNum = (_, value) => {
            if (!value) return Promise.reject(new Error("Please input the AS Base!"));

            if (value && typeof value === "string" && value.slice(0, 1) === "0") {
                return Promise.reject(new Error("As Base must be an integer without decimal or extra symbols!"));
            }

            const integerRegex = /^\d+$/;

            if (!integerRegex.test(value)) {
                return Promise.reject(new Error("As Base must be an integer without decimal or extra symbols!"));
            }

            const numberValue = Number(value);

            // 校验字节范围
            const isValid16Bit = numberValue >= 64512 && numberValue <= 65534;
            const isValid32Bit = numberValue >= 4200000000 && numberValue <= 4294967294;

            return isValid16Bit || isValid32Bit
                ? Promise.resolve()
                : Promise.reject(new Error("Valid ranges: 64512-65534 (16-bit) or 4200000000-4294967294 (32-bit)!"));
        };

        const handleChangeWithSmartValidation = (fieldName, others) => e => {
            const {value} = e.target;
            siteForm.setFieldValue(fieldName, value);

            const nonEmptyFields = others.filter(name => {
                const val = siteForm.getFieldValue(name);
                return val !== undefined && val !== null && val !== "";
            });

            if (nonEmptyFields.length > 0) {
                siteForm.validateFields(nonEmptyFields);
            }
        };

        return (
            <Form
                form={siteForm}
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                style={{width: "505px"}}
                onValuesChange={handleFormChange}
            >
                <Header level={3}>Configuration</Header>

                <Form.Item
                    label="Topology Name"
                    labelStyle={labelStyle}
                    labelCol={{style: {width: "175px"}}}
                    name="topologyName"
                    rules={[
                        {required: true, message: "Please input the topology name!"},
                        {max: 64, message: "Topology name cannot exceed 64 characters!"},
                        {
                            validator: async (_, value) => {
                                if (response !== null) {
                                    const trimmedValue = value?.trim();

                                    // 检查是否包含空格或非字母数字或下划线字符
                                    if (/\s/.test(value) || /\W/.test(value)) {
                                        return Promise.reject(
                                            new Error("Only letters, numbers and underscores allowed!")
                                        );
                                    }
                                    const filterResponse = isEditing
                                        ? response.data.filter(item => item.topology_name !== originalTopologyName)
                                        : response.data;

                                    // 检查是否有重复的 topologyName
                                    if (filterResponse.some(item => item.topology_name.trim() === trimmedValue)) {
                                        return Promise.reject(new Error("Topology name must be unique!"));
                                    }
                                }
                            }
                        }
                    ]}
                    validateTrigger="onBlur"
                >
                    <Input placeholder="Enter topology name" style={inputStyle} />
                </Form.Item>

                <Divider style={{marginTop: "32px", marginBottom: "32px"}} />

                <Header level={3}>Underlay Settings</Header>

                <Form.Item
                    label="Routing Protocols"
                    labelStyle={labelStyle}
                    labelCol={{style: {width: "175px"}}}
                    name="protocol"
                    validateTrigger="onBlur"
                    rules={[{required: true, message: "Please select a protocol!"}]}
                >
                    <Radio.Group
                        value={campusFabricData.topology.protocol}
                        onChange={e => {
                            // setIsProtocol(e.target.value === "OSPF");
                            siteForm.setFieldsValue({
                                area_id: e.target.value === "OSPF" ? "0.0.0.0" : undefined
                            });
                            setIsProtocol(e.target.value === "BGP");
                        }}
                        options={[
                            {value: "BGP", label: "BGP"},
                            {value: "OSPF", label: "OSPF"}
                        ]}
                    />
                </Form.Item>

                {!isProtocol && (
                    <Form.Item
                        label="Area ID"
                        labelStyle={labelStyle}
                        labelCol={{style: {width: "175px"}}}
                        name="area_id"
                        validateTrigger="onBlur"
                        rules={[{required: true, message: "Please input the Area ID!"}]}
                    >
                        <Input
                            placeholder="0.0.0.0"
                            disabled
                            style={{
                                ...inputStyle,
                                color: "#1F1F1F"
                            }}
                        />
                    </Form.Item>
                )}

                {isProtocol && (
                    <Form.Item
                        label="AS Base"
                        labelStyle={labelStyle}
                        labelCol={{style: {width: "175px"}}}
                        name="asBase"
                        validateTrigger="onBlur"
                        rules={[{required: true, validator: validateASNum}]}
                    >
                        <Input
                            placeholder="Enter AS Base"
                            style={{
                                ...inputStyle,
                                color: "#1F1F1F"
                            }}
                            value={inputValue}
                            onChange={e => {
                                setInputValue(e.target.value);
                                setCampusFabricData({
                                    ...campusFabricData,
                                    topology: {
                                        ...campusFabricData.topology,
                                        as_base: e.target.value
                                    }
                                });
                            }}
                            disabled={isEditing && !hasModifiedASBase} // 修改逻辑，有值才禁用，避免编辑模式空值无法修改校验失败
                        />
                    </Form.Item>
                )}

                <Form.Item
                    label="Subnet"
                    labelStyle={labelStyle}
                    labelCol={{style: {width: "175px"}}}
                    name="subnet"
                    tooltip={SubnetTooltip}
                    validateTrigger="onBlur"
                    rules={[
                        {
                            pattern: ipv4NetRegex,
                            message: "The subnet must be XXX.XXX.XXX.0/XX"
                        },
                        {required: true, message: "Please input the Subnet!"},
                        {
                            validator: (_, value) => {
                                if (!value) return Promise.resolve();
                                const inbandSubnet = siteForm.getFieldValue("inband_subnet");
                                const loopbackPrefix = siteForm.getFieldValue("loopbackPrefix");

                                if (value === inbandSubnet) {
                                    return Promise.reject(new Error("Subnet and Inband Subnet cannot be the same!"));
                                }
                                if (value === loopbackPrefix) {
                                    return Promise.reject(new Error("Subnet and Route ID Subnet cannot be the same!"));
                                }
                                const subnetsToValidate = [value, inbandSubnet, loopbackPrefix].filter(Boolean); // 过滤掉 null/undefined/空字符串

                                if (subnetsToValidate.length >= 2) {
                                    const error = validateSubnets(subnetsToValidate, value);
                                    if (error) return Promise.reject(error);
                                }

                                return Promise.resolve();
                            }
                        }
                    ]}
                >
                    <Input
                        placeholder="Enter Subnet"
                        onChange={handleChangeWithSmartValidation("subnet", ["inband_subnet", "loopbackPrefix"])}
                        style={{...inputStyle, color: "#1F1F1F"}}
                        disabled={isEditing}
                    />
                </Form.Item>

                <Form.Item
                    label="Inband Subnet"
                    labelStyle={labelStyle}
                    labelCol={{style: {width: "175px"}}}
                    name="inband_subnet"
                    validateTrigger="onBlur"
                    shouldUpdate={(prevValues, currentValues) => {
                        return (
                            prevValues.subnet !== currentValues.subnet ||
                            prevValues.loopbackPrefix !== currentValues.loopbackPrefix
                        );
                    }}
                    rules={[
                        {
                            pattern: ipv4NetRegex,
                            message: "The inband subnet must be XXX.XXX.XXX.0/XX"
                        },
                        {required: true, message: "Please input the Inband Subnet!"},
                        {
                            validator: (_, value) => {
                                if (!value) return Promise.resolve();
                                const subnet = siteForm.getFieldValue("subnet");
                                const loopbackPrefix = siteForm.getFieldValue("loopbackPrefix");

                                if (value === subnet) {
                                    return Promise.reject(new Error("Inband Subnet and Subnet cannot be the same!"));
                                }
                                if (value === loopbackPrefix) {
                                    return Promise.reject(
                                        new Error("Inband Subnet and Route ID Subnet cannot be the same!")
                                    );
                                }
                                const subnetsToValidate = [subnet, value, loopbackPrefix].filter(Boolean); // 过滤掉 null/undefined/空字符串

                                if (subnetsToValidate.length >= 2) {
                                    const error = validateSubnets(subnetsToValidate, value);
                                    if (error) return Promise.reject(error);
                                }

                                return Promise.resolve();
                            }
                        }
                    ]}
                >
                    <Input
                        placeholder="Enter Inband Subnet"
                        style={{...inputStyle, color: "#1F1F1F"}}
                        onChange={handleChangeWithSmartValidation("inband_subnet", ["subnet", "loopbackPrefix"])}
                        disabled={isEditing}
                    />
                </Form.Item>

                <Form.Item
                    label="Route ID Subnet"
                    labelStyle={labelStyle}
                    labelCol={{style: {width: "175px"}}}
                    name="loopbackPrefix"
                    tooltip={LoopbackPrefixTooltip}
                    validateTrigger="onBlur"
                    shouldUpdate={(prevValues, currentValues) =>
                        prevValues.subnet !== currentValues.subnet ||
                        prevValues.inband_subnet !== currentValues.inband_subnet
                    }
                    rules={[
                        {
                            pattern: ipv4NetRegex,
                            message: "The subnet must be XXX.XXX.XXX.0/XX"
                        },
                        {required: true, message: "Please input the Subnet!"},
                        {
                            validator: (_, value) => {
                                if (!value) return Promise.resolve();
                                const subnet = siteForm.getFieldValue("subnet");
                                const inbandSubnet = siteForm.getFieldValue("inband_subnet");

                                if (value === subnet) {
                                    return Promise.reject(new Error("Route ID Subnet and Subnet cannot be the same!"));
                                }
                                if (value === inbandSubnet) {
                                    return Promise.reject(
                                        new Error("Route ID Subnet and Inband Subnet cannot be the same!")
                                    );
                                }
                                const subnetsToValidate = [subnet, inbandSubnet, value].filter(Boolean); // 过滤掉 null/undefined/空字符串

                                if (subnetsToValidate.length >= 2) {
                                    const error = validateSubnets(subnetsToValidate, value);
                                    if (error) return Promise.reject(error);
                                }

                                return Promise.resolve();
                            }
                        }
                    ]}
                >
                    <Input
                        placeholder="Enter Route ID Subnet"
                        onChange={handleChangeWithSmartValidation("loopbackPrefix", ["subnet", "inband_subnet"])}
                        style={inputStyle}
                    />
                </Form.Item>
            </Form>
        );
    }
);

export default CampusFabricMLAGStep1;
