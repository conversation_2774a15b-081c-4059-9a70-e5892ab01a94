import {forwardRef} from "react";
import CampusFabricMLAGStep5 from "@/modules-ampcon/pages/Topo/CampusFabric/create_campus_fabric_steps/mlag_steps/campus_fabric_mlag_step5";
import CampusFabricIPClosStep5 from "@/modules-ampcon/pages/Topo/CampusFabric/create_campus_fabric_steps/ip_clos_steps/campus_fabric_ip_clos_step5";

const CampusFabricStep5 = forwardRef(
    ({currentFabricTopologyType, campusFabricData, lldpData, setCampusFabricData}, ref) => {
        return currentFabricTopologyType === "ip-clos" ? (
            <CampusFabricIPClosStep5
                ref={ref}
                campusFabricData={campusFabricData}
                lldpData={lldpData}
                setCampusFabricData={setCampusFabricData}
            />
        ) : (
            <CampusFabricMLAGStep5
                ref={ref}
                campusFabricData={campusFabricData}
                lldpData={lldpData}
                setCampusFabricData={setCampusFabricData}
            />
        );
    }
);
export default CampusFabricStep5;
