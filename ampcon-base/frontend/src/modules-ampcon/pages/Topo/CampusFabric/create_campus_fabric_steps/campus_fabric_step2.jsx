import {forwardRef} from "react";
import CampusFabricMLAGStep2 from "@/modules-ampcon/pages/Topo/CampusFabric/create_campus_fabric_steps/mlag_steps/campus_fabric_mlag_step2";
import CampusFabricIPClosStep2 from "@/modules-ampcon/pages/Topo/CampusFabric/create_campus_fabric_steps/ip_clos_steps/campus_fabric_ip_clos_step2";

const CampusFabricStep2 = forwardRef(
    ({currentFabricTopologyType, campusFabricData, setCampusFabricData, allModelPhysicPortInfo, isEditing}, ref) => {
        return currentFabricTopologyType === "ip-clos" ? (
            <CampusFabricIPClosStep2
                ref={ref}
                campusFabricData={campusFabricData}
                setCampusFabricData={setCampusFabricData}
                allModelPhysicPortInfo={allModelPhysicPortInfo}
                isEditing={isEditing}
            />
        ) : (
            <CampusFabricMLAGStep2
                ref={ref}
                campusFabricData={campusFabricData}
                setCampusFabricData={setCampusFabricData}
                allModelPhysicPortInfo={allModelPhysicPortInfo}
            />
        );
    }
);
export default CampusFabricStep2;
