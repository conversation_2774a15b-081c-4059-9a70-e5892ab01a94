import {forwardRef} from "react";
import CampusFabricMLAGStep3 from "@/modules-ampcon/pages/Topo/CampusFabric/create_campus_fabric_steps/mlag_steps/campus_fabric_mlag_step3";
import CampusFabricIPClosStep3 from "@/modules-ampcon/pages/Topo/CampusFabric/create_campus_fabric_steps/ip_clos_steps/campus_fabric_ip_clos_step3";

const CampusFabricStep3 = forwardRef(({currentFabricTopologyType, campusFabricData, setCampusFabricData}, ref) => {
    return currentFabricTopologyType === "ip-clos" ? (
        <CampusFabricIPClosStep3
            ref={ref}
            nodes={campusFabricData.nodes}
            networks={campusFabricData.networks}
            setCampusFabricData={setCampusFabricData}
            usedSubnets={[campusFabricData.topology.subnet, campusFabricData.topology.loopback_prefix]}
            vir_tech={campusFabricData.topology.virtualization_technology}
        />
    ) : (
        <CampusFabricMLAGStep3
            ref={ref}
            nodes={campusFabricData.nodes}
            networks={campusFabricData.networks}
            setCampusFabricData={setCampusFabricData}
            usedSubnets={[
                campusFabricData.topology.subnet,
                campusFabricData.topology.inband_subnet,
                campusFabricData.topology.loopback_prefix
            ]}
        />
    );
});
export default CampusFabricStep3;
