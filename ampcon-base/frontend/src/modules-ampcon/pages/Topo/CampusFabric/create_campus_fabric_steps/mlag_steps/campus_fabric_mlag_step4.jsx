import {message, Table, Tabs} from "antd";
import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from "react";
import ExpandIcon from "@/modules-ampcon/components/expand_icon";
import SwitchPortInfoLogicSelectPanel from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/switch_port_info_logic_select_panel";
import SwitchPortInfoPhysicSelectPanel from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/switch_port_info_physic_select_panel";
import CoreTabSelectedPortCard from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/mlag/core_tab_select_port_card";
import {usePopper} from "react-popper";
import AccessTabSelectedPortCard from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/mlag/access_tab_select_port_card";
import SwitchPortPhysicData from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/switch_port_physic_data";
import topoStyle from "@/modules-ampcon/pages/Topo/CampusFabric/campus_fabric.module.scss";

const CampusFabricMLAGStep4 = forwardRef(
    ({campusFabricData, setCampusFabricData, allModelPhysicPortInfo, currentFabricTopologyType}, ref) => {
        const coreTableRef = useRef();
        const accessTableRef = useRef();
        const coreTabSelectedPortCardRef = useRef();
        const accessTabSelectedPortCardRef = useRef();
        const expandedRowRefs = useRef({});

        const [referenceElement, setReferenceElement] = useState(null);
        const [popperElement, setPopperElement] = useState(null);
        const [tableCoreData, setTableCoreData] = useState([]);
        const [tableAccessData, setTableAccessData] = useState([]);
        const [isMenuCollapsed, setIsMenuCollapsed] = useState(false);
        const [activeTabKey, setActiveTabKey] = useState("core");
        const [coreTabExpandedRowKeys, setCoreTabExpandedRowKeys] = useState([]);
        const [accessTabExpandedRowKeys, setAccessTabExpandedRowKeys] = useState([]);

        const [linkToWanExist, setLinkToWanExist] = useState(false);

        const {styles, attributes} = usePopper(referenceElement, popperElement, {
            placement: "bottom"
        });

        const coreSwitchTableColumns = [
            {
                title: "Switch",
                dataIndex: "sn",
                key: "sn",
                width: "20%"
            },
            {
                title: "Model",
                dataIndex: "model",
                key: "model",
                width: "20%"
            },
            {
                title: "Link to WAN",
                render: (text, record) => {
                    if (!linkToWanExist) {
                        return <div>{`${record.currentLinkToWANCount}`}</div>;
                    }
                    return (
                        <div
                            style={{
                                color: record.currentLinkToWANCount === record.maxLinkToWanCount ? "#2BC174" : "#F53F3F"
                            }}
                        >
                            {`${record.currentLinkToWANCount}/${record.maxLinkToWanCount}`}
                        </div>
                    );
                },
                key: "currentLinkToWANCount",
                width: "20%"
            },
            {
                title: "Link to Core",
                render: (text, record) => {
                    return (
                        <div
                            style={{
                                color:
                                    record.currentLinkToCoreCount === record.maxLinkToCoreCount ? "#2BC174" : "#F53F3F"
                            }}
                        >
                            {`${record.currentLinkToCoreCount}/${record.maxLinkToCoreCount}`}
                        </div>
                    );
                },
                key: "linkToCoreCount",
                width: "20%"
            },
            {
                title: "Link to Access",
                render: (text, record) => {
                    return (
                        <div
                            style={{
                                color:
                                    record.currentLinkToAccessCount === record.maxLinkToAccessCount
                                        ? "#2BC174"
                                        : "#F53F3F"
                            }}
                        >
                            {`${record.currentLinkToAccessCount}/${record.maxLinkToAccessCount}`}
                        </div>
                    );
                },
                key: "linkToAccessCount",
                width: "20%"
            }
        ];

        const accessSwitchTableColumns = [
            {
                title: "Switch",
                dataIndex: "sn",
                key: "sn",
                width: "33%"
            },
            {
                title: "Model",
                dataIndex: "model",
                key: "model",
                width: "33%"
            },
            {
                title: "Link to Core",
                render: (text, record) => {
                    return (
                        <div
                            style={{
                                color:
                                    record.currentLinkToCoreCount === record.maxLinkToCoreCount ? "#2BC174" : "#F53F3F"
                            }}
                        >
                            {`${record.currentLinkToCoreCount}/${record.maxLinkToCoreCount}`}
                        </div>
                    );
                },
                key: "linkToCoreCount",
                width: "34%"
            }
        ];

        useImperativeHandle(ref, () => ({
            validate: () => {
                const coreTabValidateResult = coreTabValidate();
                const accessTabValidateResult = accessTabValidate();
                let isCoreTabValid = true;
                let isAccessTabValid = true;
                if (Object.keys(coreTabValidateResult).length > 0) {
                    setCoreTabExpandedRowKeys([
                        ...new Set([...coreTabExpandedRowKeys, ...Object.keys(coreTabValidateResult)])
                    ]);
                    isCoreTabValid = false;
                }
                if (Object.keys(accessTabValidateResult).length > 0) {
                    setAccessTabExpandedRowKeys([
                        ...new Set([...accessTabExpandedRowKeys, ...Object.keys(accessTabValidateResult)])
                    ]);
                    isAccessTabValid = false;
                }
                if (activeTabKey === "core" && isCoreTabValid && !isAccessTabValid) {
                    setActiveTabKey("access");
                } else if (activeTabKey === "access" && !isCoreTabValid && isAccessTabValid) {
                    setActiveTabKey("core");
                }
                return isCoreTabValid && isAccessTabValid;
            }
        }));

        useEffect(() => {
            const campusFabricDataLocal = filterNodesPortNotExists();

            // init table data
            const coreOriginData = campusFabricDataLocal.nodes.core;
            const accessOriginData = campusFabricDataLocal.nodes.access;

            const coreData = [];
            const accessData = [];
            for (let i = 0; i < coreOriginData.length; i++) {
                const item = coreOriginData[i];
                if (item.links === undefined) {
                    item.links = {
                        to_core: item.links?.to_core ? item.links?.to_core : {},
                        to_access: item.links?.to_access ? item.links?.to_access : {},
                        to_wan: item.links?.to_wan ? item.links?.to_wan : {}
                    };
                }
                coreData.push({
                    key: item.switch_sn,
                    sn: item.switch_sn,
                    model: item.model,
                    maxLinkToCoreCount: 2,
                    maxLinkToAccessCount: coreMaxLinkToAccessCount,
                    maxLinkToWanCount: 1,
                    currentLinkToCoreCount: item.links.to_core ? Object.keys(item.links.to_core).length : 0,
                    currentLinkToAccessCount: item.links.to_access ? Object.keys(item.links.to_access).length : 0,
                    currentLinkToWANCount: item.links.to_wan ? Object.keys(item.links.to_wan).length : 0,
                    currentLinkToCore: item.links.to_core,
                    currentLinkToAccess: item.links.to_access,
                    currentLinkToWAN: item.links.to_wan,
                    isRowExpanded: false
                });
            }
            for (let i = 0; i < accessOriginData.length; i++) {
                const item = accessOriginData[i];
                if (item.links === undefined) {
                    item.links = {
                        to_core: item.links?.to_core ? item.links?.to_core : {}
                    };
                }
                accessData.push({
                    key: item.switch_sn,
                    sn: item.switch_sn,
                    model: item.model,
                    currentLinkToCoreCount: item.links.to_core ? Object.keys(item.links.to_core).length : 0,
                    maxLinkToCoreCount: accessMaxLinkToCoreCount,
                    currentLinkToAccessCount: item.links.to_access ? Object.keys(item.links.to_access).length : 0,
                    currentLinkToCore: item.links.to_core,
                    isRowExpanded: false
                });
            }
            checkLinkToWanExist(coreData);
            setTableCoreData(coreData);
            setTableAccessData(accessData);

            document.addEventListener("click", hideSelectedPortCardCallback);
            const getCardOffsetInterval = setInterval(() => {
                if (document.querySelectorAll(".ant-menu-inline-collapsed").length > 0) {
                    setIsMenuCollapsed(true);
                } else {
                    setIsMenuCollapsed(false);
                }
            }, 1000);
            document.addEventListener("wheel", hideSelectedPortCardCallback);

            return () => {
                document.removeEventListener("click", hideSelectedPortCardCallback);
                clearInterval(getCardOffsetInterval);
                document.removeEventListener("wheel", hideSelectedPortCardCallback);
            };
        }, []);

        const checkLinkToWanExist = newTableData => {
            const allLinksEmpty = newTableData.every(item => Object.keys(item.currentLinkToWAN).length === 0);

            if (allLinksEmpty) {
                setLinkToWanExist(false);
            } else {
                setLinkToWanExist(true);
            }
        };

        const filterNodesPortNotExists = () => {
            const campusFabricDataTemp = JSON.parse(JSON.stringify(campusFabricData));
            campusFabricDataTemp.nodes.core.map(node => {
                if (node.links !== undefined) {
                    for (const key in node.links.to_core) {
                        if (!allModelPhysicPortInfo[node.model][key.slice(0, 2)].includes(key)) {
                            delete node.links.to_core[key];
                        }
                    }
                    for (const key in node.links.to_access) {
                        if (!allModelPhysicPortInfo[node.model][key.slice(0, 2)].includes(key)) {
                            delete node.links.to_access[key];
                        }
                    }
                    for (const key in node.links.to_wan) {
                        if (!allModelPhysicPortInfo[node.model][key.slice(0, 2)].includes(key)) {
                            delete node.links.to_wan[key];
                        }
                    }
                }
            });
            campusFabricDataTemp.nodes.access.map(node => {
                if (node.links !== undefined) {
                    for (const key in node.links.to_core) {
                        if (!allModelPhysicPortInfo[node.model][key.slice(0, 2)].includes(key)) {
                            delete node.links.to_core[key];
                        }
                    }
                }
            });
            setCampusFabricData(campusFabricDataTemp);
            return campusFabricDataTemp;
        };

        const getCoreMaxLinkToAccessCount = () => {
            return campusFabricData.nodes.access.length;
        };

        const getAccessMaxLinkToCoreCount = () => {
            return campusFabricData.nodes.core.length;
        };

        const coreMaxLinkToAccessCount = getCoreMaxLinkToAccessCount();
        const accessMaxLinkToCoreCount = getAccessMaxLinkToCoreCount();

        const showSelectedPortCardCallback = (record, portName, portPosX, portPosY, selectedPortType) => {
            let selectedPortCardRef;
            if (activeTabKey === "core") {
                selectedPortCardRef = coreTabSelectedPortCardRef;
            } else {
                selectedPortCardRef = accessTabSelectedPortCardRef;
            }
            selectedPortCardRef.current.showSelectedPortCard(record, portName, portPosX, portPosY, selectedPortType);
        };

        const hideSelectedPortCardCallback = e => {
            if (
                e &&
                coreTabSelectedPortCardRef.current.isSelectedPortCardShown() &&
                !coreTabSelectedPortCardRef.current.isMouseClickOnPortCard(e)
            ) {
                coreTabSelectedPortCardRef.current.hideSelectedPortCard();
                for (const key in expandedRowRefs.current) {
                    if (expandedRowRefs.current[key].current) {
                        expandedRowRefs.current[key].current.clearAllPortsSelectedStatus();
                    }
                }
            }
            if (
                e &&
                accessTabSelectedPortCardRef.current.isSelectedPortCardShown() &&
                !accessTabSelectedPortCardRef.current.isMouseClickOnPortCard(e)
            ) {
                accessTabSelectedPortCardRef.current.hideSelectedPortCard();
                for (const key in expandedRowRefs.current) {
                    if (expandedRowRefs.current[key].current) {
                        expandedRowRefs.current[key].current.clearAllPortsSelectedStatus();
                    }
                }
            }
        };

        const selectedPortCardFunc = {
            showSelectedPortCard: showSelectedPortCardCallback,
            hideSelectedPortCard: hideSelectedPortCardCallback
        };

        const coreTabUpdateCorePortStatusCallback = (sn, portName, selectedPortType) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            let isPortSelectedCountSmallerThanMax = true;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToAccess && Object.keys(item.currentLinkToAccess).includes(portName)) {
                        item.currentLinkToAccessCount -= 1;
                        delete item.currentLinkToAccess[portName];
                    } else if (item.currentLinkToWAN && Object.keys(item.currentLinkToWAN).includes(portName)) {
                        item.currentLinkToWANCount -= 1;
                        delete item.currentLinkToWAN[portName];
                    } else if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCore[portName] = {port_type: selectedPortType};
                        return item;
                    }
                    if (item.currentLinkToCoreCount + 1 > item.maxLinkToCoreCount) {
                        isPortSelectedCountSmallerThanMax = false;
                    } else {
                        item.currentLinkToCoreCount += 1;
                        item.currentLinkToCore[portName] = {port_type: selectedPortType};
                    }
                }
                return item;
            });
            if (!isPortSelectedCountSmallerThanMax) {
                message.error("The number of selected ports has reached the maximum limit.");
                return;
            }
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    node.links.to_core[portName] = {port_type: selectedPortType};
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "selectedToCore");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        const coreTabRemoveCorePortStatusCallback = (sn, portName) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCoreCount -= 1;
                        delete item.currentLinkToCore[portName];
                    }
                }
                return item;
            });
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    delete node.links.to_core[portName];
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "common");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        const coreTabUpdateAccessPortStatusCallback = (sn, portName, selectedPortType) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            let isPortSelectedCountSmallerThanMax = true;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCoreCount -= 1;
                        delete item.currentLinkToCore[portName];
                    } else if (item.currentLinkToWAN && Object.keys(item.currentLinkToWAN).includes(portName)) {
                        item.currentLinkToWANCount -= 1;
                        delete item.currentLinkToWAN[portName];
                    } else if (item.currentLinkToAccess && Object.keys(item.currentLinkToAccess).includes(portName)) {
                        item.currentLinkToAccess[portName] = {port_type: selectedPortType};
                        return item;
                    }
                    if (item.currentLinkToAccessCount + 1 > item.maxLinkToAccessCount) {
                        isPortSelectedCountSmallerThanMax = false;
                    } else {
                        item.currentLinkToAccessCount += 1;
                        item.currentLinkToAccess[portName] = {port_type: selectedPortType};
                    }
                }
                return item;
            });
            if (!isPortSelectedCountSmallerThanMax) {
                message.error("The number of selected ports has reached the maximum limit.");
                return;
            }
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    node.links.to_access[portName] = {port_type: selectedPortType};
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "selectedToAccess");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        const coreTabRemoveAccessPortStatusCallback = (sn, portName) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToAccess && Object.keys(item.currentLinkToAccess).includes(portName)) {
                        item.currentLinkToAccessCount -= 1;
                        delete item.currentLinkToAccess[portName];
                    }
                }
                return item;
            });
            checkLinkToWanExist(newTableData);
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    delete node.links.to_access[portName];
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "common");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        const coreTabUpdateWANPortStatusCallback = (sn, portName, selectedPortType) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            setLinkToWanExist(true);
            let isPortSelectedCountSmallerThanMax = true;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCoreCount -= 1;
                        delete item.currentLinkToCore[portName];
                    } else if (item.currentLinkToAccess && Object.keys(item.currentLinkToAccess).includes(portName)) {
                        item.currentLinkToAccessCount -= 1;
                        delete item.currentLinkToAccess[portName];
                    } else if (item.currentLinkToWAN && Object.keys(item.currentLinkToWAN).includes(portName)) {
                        item.currentLinkToWAN[portName] = {port_type: selectedPortType};
                        return item;
                    }
                    if (item.currentLinkToWANCount + 1 > item.maxLinkToWanCount) {
                        isPortSelectedCountSmallerThanMax = false;
                    } else {
                        item.currentLinkToWANCount += 1;
                        item.currentLinkToWAN[portName] = {port_type: selectedPortType};
                    }
                }
                return item;
            });
            if (!isPortSelectedCountSmallerThanMax) {
                message.error("The number of selected ports has reached the maximum limit.");
                return;
            }
            checkLinkToWanExist(newTableData);
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    node.links.to_wan[portName] = {port_type: selectedPortType};
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "selectedToWAN");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        const coreTabRemoveWANPortStatusCallBack = (sn, portName) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToWAN && Object.keys(item.currentLinkToWAN).includes(portName)) {
                        item.currentLinkToWANCount -= 1;
                        delete item.currentLinkToWAN[portName];
                    }
                }
                return item;
            });
            checkLinkToWanExist(newTableData);
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    delete node.links.to_wan[portName];
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "common");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        const accessTabUpdateCorePortStatusCallback = (sn, portName, selectedPortType, selectedCoreSwitchSN) => {
            const tableData = tableAccessData;
            const setTableData = setTableAccessData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    item.currentLinkToCoreCount += 1;
                    item.currentLinkToCore[portName] = {port_type: selectedPortType, core_sn: selectedCoreSwitchSN};
                }
                return item;
            });
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.access.map(node => {
                if (node.switch_sn === sn) {
                    node.links.to_core[portName] = {port_type: selectedPortType, core_sn: selectedCoreSwitchSN};
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "selectedToCore");
            accessTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        const accessTabRemoveCorePortStatusCallback = (sn, portName) => {
            const tableData = tableAccessData;
            const setTableData = setTableAccessData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCoreCount -= 1;
                        delete item.currentLinkToCore[portName];
                    }
                }
                return item;
            });
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.access.map(node => {
                if (node.switch_sn === sn) {
                    delete node.links.to_core[portName];
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "common");
            accessTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        const getCoreSwitchSNOptions = sn => {
            return tableCoreData.map(coreItem => {
                return {
                    value: coreItem.sn,
                    label: coreItem.sn,
                    disabled:
                        campusFabricData.nodes.access.filter(node => {
                            return node.switch_sn === sn;
                        }).length !== 0
                            ? Object.values(
                                  campusFabricData.nodes.access.filter(node => {
                                      return node.switch_sn === sn;
                                  })[0].links
                              )
                                  .map(item => {
                                      return Object.values(item);
                                  })[0]
                                  .map(i => {
                                      return i.core_sn;
                                  })
                                  .includes(coreItem.sn)
                            : false
                };
            });
        };

        const coreTabValidate = () => {
            const coreTabValidateResult = {};
            const accessSwitchCount = campusFabricData.nodes.access.length;
            const wanSwitchCount = campusFabricData.nodes.core.some(node => Object.keys(node.links.to_wan).length > 0)
                ? 1
                : 0;
            for (let i = 0; i < campusFabricData.nodes.core.length; i++) {
                const item = campusFabricData.nodes.core[i];
                if (wanSwitchCount > 0 && Object.keys(item.links.to_wan).length < wanSwitchCount) {
                    if (coreTabValidateResult[item.switch_sn] === undefined) {
                        coreTabValidateResult[item.switch_sn] = {};
                    }
                    coreTabValidateResult[item.switch_sn].toWANInvalid = true;
                }
                if (Object.keys(item.links.to_core).length < 2) {
                    if (coreTabValidateResult[item.switch_sn] === undefined) {
                        coreTabValidateResult[item.switch_sn] = {};
                    }
                    coreTabValidateResult[item.switch_sn].toCoreInvalid = true;
                }
                if (Object.keys(item.links.to_access).length < accessSwitchCount) {
                    if (coreTabValidateResult[item.switch_sn] === undefined) {
                        coreTabValidateResult[item.switch_sn] = {};
                    }
                    coreTabValidateResult[item.switch_sn].toAccessInvalid = true;
                }
            }
            return coreTabValidateResult;
        };

        const updateCurrentPortConfigForTheSameModelSwitch = ({
            currentLinkToCore,
            currentLinkToAccess,
            currentLinkToWAN,
            model
        }) => {
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            const currentTableCoreData = JSON.parse(JSON.stringify(tableCoreData));
            const currentTableAccessData = JSON.parse(JSON.stringify(tableAccessData));
            if (activeTabKey === "core") {
                currentCampusFabricData.nodes.core.map(node => {
                    if (node.model === model) {
                        node.links = {
                            to_core: JSON.parse(JSON.stringify(currentLinkToCore)),
                            to_access: JSON.parse(JSON.stringify(currentLinkToAccess)),
                            to_wan: JSON.parse(JSON.stringify(currentLinkToWAN))
                        };
                    }
                });
                currentTableCoreData.map(item => {
                    if (item.model === model) {
                        item.currentLinkToCore = JSON.parse(JSON.stringify(currentLinkToCore));
                        item.currentLinkToCoreCount = Object.keys(currentLinkToCore).length;
                        item.currentLinkToAccess = JSON.parse(JSON.stringify(currentLinkToAccess));
                        item.currentLinkToAccessCount = Object.keys(currentLinkToAccess).length;
                        item.currentLinkToWAN = JSON.parse(JSON.stringify(currentLinkToWAN));
                        item.currentLinkToWANCount = Object.keys(currentLinkToWAN).length;
                    }
                });
                setTableCoreData(currentTableCoreData);
            } else if (activeTabKey === "access") {
                currentCampusFabricData.nodes.access.map(node => {
                    if (node.model === model) {
                        node.links = {
                            to_core: JSON.parse(JSON.stringify(currentLinkToCore))
                        };
                    }
                });
                currentTableAccessData.map(item => {
                    if (item.model === model) {
                        item.currentLinkToCore = JSON.parse(JSON.stringify(currentLinkToCore));
                        item.currentLinkToCoreCount = Object.keys(currentLinkToCore).length;
                    }
                });
                setTableAccessData(currentTableAccessData);
            }
            setCampusFabricData(currentCampusFabricData);
            setTimeout(() => {
                Object.keys(expandedRowRefs.current).forEach(sn => {
                    expandedRowRefs.current[sn].current.refreshAllPortStatus();
                });
            }, 200);
        };

        const accessTabValidate = () => {
            const accessTabValidateResult = {};
            const coreSwitchCount = campusFabricData.nodes.core.length;
            for (let i = 0; i < campusFabricData.nodes.access.length; i++) {
                const item = campusFabricData.nodes.access[i];
                if (Object.keys(item.links.to_core).length < coreSwitchCount) {
                    if (accessTabValidateResult[item.switch_sn] === undefined) {
                        accessTabValidateResult[item.switch_sn] = {};
                    }
                    accessTabValidateResult[item.switch_sn].toCoreInvalid = true;
                }
            }
            return accessTabValidateResult;
        };

        const expandedRowRender = record => {
            expandedRowRefs.current[record.sn] = React.createRef();
            return Object.keys(SwitchPortPhysicData).includes(record.model) ? (
                <SwitchPortInfoPhysicSelectPanel
                    ref={expandedRowRefs.current[record.sn]}
                    physicPortData={SwitchPortPhysicData[record.model].portData}
                    portLabelComponent={SwitchPortPhysicData[record.model].portLabelComponent}
                    linkToCorePorts={record.currentLinkToCore}
                    linkToAccessPorts={record.currentLinkToAccess ? record.currentLinkToAccess : {}}
                    linkToWANPorts={record.currentLinkToWAN ? record.currentLinkToWAN : {}}
                    selectedPortCardFunc={selectedPortCardFunc}
                    updateCurrentPortConfigForTheSameModelSwitch={updateCurrentPortConfigForTheSameModelSwitch}
                    record={record}
                    tabType={activeTabKey}
                    currentFabricTopologyType={currentFabricTopologyType}
                />
            ) : (
                <SwitchPortInfoLogicSelectPanel
                    ref={expandedRowRefs.current[record.sn]}
                    physicPortInfo={allModelPhysicPortInfo[record.model]}
                    linkToCorePorts={record.currentLinkToCore}
                    linkToAccessPorts={record.currentLinkToAccess ? record.currentLinkToAccess : {}}
                    linkToWANPorts={record.currentLinkToWAN ? record.currentLinkToWAN : {}}
                    selectedPortCardFunc={selectedPortCardFunc}
                    updateCurrentPortConfigForTheSameModelSwitch={updateCurrentPortConfigForTheSameModelSwitch}
                    record={record}
                    tabType={activeTabKey}
                    currentFabricTopologyType={currentFabricTopologyType}
                />
            );
        };

        const handleCoreExpand = (expanded, record) => {
            setCoreTabExpandedRowKeys(
                expanded
                    ? [...coreTabExpandedRowKeys, record.key]
                    : coreTabExpandedRowKeys.filter(key => key !== record.key)
            );
        };

        const handleAccessExpand = (expanded, record) => {
            setAccessTabExpandedRowKeys(
                expanded
                    ? [...accessTabExpandedRowKeys, record.key]
                    : accessTabExpandedRowKeys.filter(key => key !== record.key)
            );
        };

        const tabItems = [
            {
                key: "core",
                label: "Core",
                children: (
                    <Table
                        ref={coreTableRef}
                        columns={coreSwitchTableColumns}
                        expandable={{
                            expandedRowRender,
                            expandIcon: props => (
                                <ExpandIcon
                                    {...props}
                                    preCallback={() => {
                                        coreTabSelectedPortCardRef.current.hideSelectedPortCard();
                                    }}
                                />
                            ),
                            expandedRowKeys: coreTabExpandedRowKeys,
                            onExpand: handleCoreExpand
                        }}
                        bordered
                        pagination={false}
                        dataSource={tableCoreData}
                    />
                )
            },
            {
                key: "access",
                label: "Access",
                children: (
                    <Table
                        ref={accessTableRef}
                        columns={accessSwitchTableColumns}
                        expandable={{
                            expandedRowRender,
                            expandIcon: props => (
                                <ExpandIcon
                                    {...props}
                                    preCallback={() => {
                                        accessTabSelectedPortCardRef.current.hideSelectedPortCard();
                                    }}
                                />
                            ),
                            expandedRowKeys: accessTabExpandedRowKeys,
                            onExpand: handleAccessExpand
                        }}
                        bordered
                        pagination={false}
                        dataSource={tableAccessData}
                    />
                )
            }
        ];

        return (
            <>
                <div
                    id="campusFabricStep4"
                    ref={setPopperElement}
                    style={{
                        ...styles.popper
                    }}
                    {...attributes.popper}
                >
                    <CoreTabSelectedPortCard
                        coreTabUpdateCorePortStatusCallback={coreTabUpdateCorePortStatusCallback}
                        coreTabUpdateAccessPortStatusCallback={coreTabUpdateAccessPortStatusCallback}
                        coreTabRemoveCorePortStatusCallback={coreTabRemoveCorePortStatusCallback}
                        coreTabRemoveAccessPortStatusCallback={coreTabRemoveAccessPortStatusCallback}
                        coreTabUpdateWANPortStatusCallback={coreTabUpdateWANPortStatusCallback}
                        coreTabRemoveWANPortStatusCallBack={coreTabRemoveWANPortStatusCallBack}
                        ref={coreTabSelectedPortCardRef}
                        isMenuCollapsed={isMenuCollapsed}
                    />
                    <AccessTabSelectedPortCard
                        accessTabUpdateCorePortStatusCallback={accessTabUpdateCorePortStatusCallback}
                        accessTabRemoveCorePortStatusCallback={accessTabRemoveCorePortStatusCallback}
                        getCoreSwitchSNOptions={getCoreSwitchSNOptions}
                        ref={accessTabSelectedPortCardRef}
                        isMenuCollapsed={isMenuCollapsed}
                    />
                </div>
                <Tabs
                    style={{maxHeight: "calc(100vh - 350px)", overflow: "auto"}}
                    activeKey={activeTabKey}
                    rootClassName={topoStyle.unitComponent}
                    onChange={key => {
                        setActiveTabKey(key);
                        coreTabSelectedPortCardRef.current.hideSelectedPortCard();
                        accessTabSelectedPortCardRef.current.hideSelectedPortCard();
                    }}
                    items={tabItems}
                />
            </>
        );
    }
);

export default CampusFabricMLAGStep4;
