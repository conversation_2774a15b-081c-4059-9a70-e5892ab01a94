import {useState, useRef, useEffect, forwardRef, useImperative<PERSON>andle} from "react";
import {Space, Form, message, Divider} from "antd";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {createColumnConfig, AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import Icon from "@ant-design/icons";
import {NetworkModal} from "../../modal/network_modal";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {NACConfigurationForm} from "../../network_setting_form/nac_configuration_form";
import {DHCPSnoopingForm} from "../../network_setting_form/dhcp_snooping_form";
import {DHCPRelayForm} from "../../network_setting_form/dhcp_relay_form";
import {MLAGConfigurationForm} from "../../network_setting_form/mlag_configuration_form";
import ip from "ip";
import styles from "../../network_setting_form/form_style.module.scss";

const tableStyle = {
    maxWidth: "45%",
    minWidth: "650px"
};

const CampusFabricMLAGStep3 = forwardRef(({nodes, networks, setCampusFabricData, usedSubnets}, ref) => {
    function to_frontend_structure(data) {
        return Object.keys(data).map(key => {
            return {
                name: key,
                vlan_id: data[key].vlan_id,
                subnet: data[key].subnet
            };
        });
    }
    function to_backend_structure(data) {
        return data.map(item => {
            return {
                [item.name]: {
                    vlan_id: item.vlan_id,
                    subnet: item.subnet
                }
            };
        });
    }

    const [networkData, setNetworkData] = useState(to_frontend_structure(networks.vlans));

    const mlagConfigurationFormRef = useRef(null);
    const nacConfigurationFormRef = useRef(null);
    const dhcpSnoopingRef = useRef(null);
    const dhcpRelayRef = useRef(null);

    const networkModalRef = useRef(null);

    useEffect(() => {
        const vlans = to_backend_structure(networkData);
        setCampusFabricData(prev => {
            return {
                ...prev,
                networks: {
                    ...prev.networks,
                    vlans: vlans.reduce((acc, cur) => {
                        return {...acc, ...cur};
                    }, {})
                }
            };
        });
    }, [networkData]);

    useImperativeHandle(ref, () => ({
        validate: () => {
            if (networkData.length === 0) {
                message.error("Please create network first");
                return false;
            }
            const result = [
                mlagConfigurationFormRef.current.validate(),
                nacConfigurationFormRef.current.validate(),
                dhcpSnoopingRef.current.validate(),
                dhcpRelayRef.current.validate(),
                checkIPAvailability(),
                checkDuplicateSubnet()
            ].every(item => item === true);
            if (result) {
                distributeIPForCoreNodes();
                return true;
            }
            return false;
        }
    }));

    function checkIPAvailability() {
        const requiredIPCounts = (nodes.core.length / 2) * 3;
        const insufficientIPVlans = networkData
            .filter(network => {
                const subnetInfo = ip.cidrSubnet(network.subnet);
                const availableIPs = subnetInfo.numHosts;
                return availableIPs < requiredIPCounts;
            })
            .map(network => network.name);

        if (insufficientIPVlans.length > 0) {
            message.error(`Insufficient IPs in vlans: ${insufficientIPVlans.join(", ")}`);
            return false;
        }
        return true;
    }

    function checkDuplicateSubnet() {
        const duplicateSubnets = networkData.filter(network => usedSubnets.includes(network.subnet));

        if (duplicateSubnets.length > 0) {
            message.error(`Duplicate subnets found: ${duplicateSubnets.map(network => network.subnet).join(", ")}`);
            return false;
        }
        return true;
    }

    function distributeIPForCoreNodes() {
        const newNodes = structuredClone(nodes);
        let peerCount = 0;
        let count = 0;

        newNodes.core.forEach(coreNode => {
            const other_ip_config = networkData.map(network => {
                const subnetInfo = ip.cidrSubnet(network.subnet);
                const {lastAddress} = subnetInfo;
                return {
                    vlan_name: network.name,
                    vlan_id: network.vlan_id,
                    vrrp_ip: ip.fromLong(ip.toLong(lastAddress) - peerCount * 3),
                    ip_address: ip.fromLong(ip.toLong(lastAddress) - peerCount * 3 - count - 1)
                };
            });
            coreNode.other_ip_config = other_ip_config;
            count++;
            if (count === 2) {
                peerCount++;
                count = 0;
            }
        });

        newNodes.access.forEach(accessNode => {
            const other_ip_config = networkData.map(network => {
                return {
                    vlan_name: network.name,
                    vlan_id: network.vlan_id,
                    ip_address: ""
                };
            });
            accessNode.other_ip_config = other_ip_config;
        });

        setCampusFabricData(prev => ({
            ...prev,
            nodes: newNodes
        }));
    }

    const updateRelatedItems = networkName => {
        dhcpSnoopingRef.current.removeRelatedNetwork(networkName);
        dhcpRelayRef.current.removeRelatedNetwork(networkName);
    };

    const columns = [
        {...createColumnConfig("Name", "name", "", "", "25%"), sorter: (a, b) => a.name.localeCompare(b.name)},
        {...createColumnConfig("VLAN ID", "vlan_id", "", "", "25%"), sorter: (a, b) => a.vlan_id - b.vlan_id},
        {
            ...createColumnConfig("Subnet", "subnet", "", "", "25%"),
            sorter: (a, b) => a.subnet.localeCompare(b.subnet)
        },

        {
            title: "Operation",
            fixed: "right",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    networkModalRef.current.showNetworkModal({mode: "edit"}, record);
                                }}
                            >
                                Edit
                            </a>
                            <a
                                onClick={() => {
                                    confirmModalAction("Are you sure want to delete?", () => {
                                        try {
                                            setNetworkData(networkData.filter(item => item.name !== record.name));
                                            updateRelatedItems(record.name);

                                            message.success("Network deleted successfully");
                                        } catch (error) {
                                            message.error("An error occurred while deleting the network");
                                            console.error(error);
                                        }
                                    });
                                }}
                            >
                                Delete
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    return (
        <div className={styles.container}>
            <h2 style={{fontSize: "21px"}}>Configure Networks</h2>
            <h3>Network</h3>
            <Form.Item labelAlign="left" style={{marginBottom: "0px"}} label="Network" labelCol={{style: {width: 175}}}>
                <a
                    style={{
                        border: "none",
                        borderRadius: "4px",
                        color: "#14c9bb"
                    }}
                    onClick={() => {
                        if (networkData.length === 255) {
                            message.error("The maximum number of networks is 255");
                            return;
                        }
                        networkModalRef.current.showNetworkModal({mode: "create"});
                    }}
                >
                    <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                    Add
                </a>
            </Form.Item>
            <NetworkModal
                ref={networkModalRef}
                networkData={networkData}
                handleAddNetwork={data => {
                    setNetworkData([...networkData, data]);
                }}
                handleEditNetwork={data => {
                    setNetworkData(networkData.map(item => (item.name === data.name ? data : item)));
                }}
                requiredIPCounts={(nodes.core.length / 2) * 3}
                usedSubnets={usedSubnets}
            />
            <AmpConCustomTable
                dataSource={networkData}
                columns={columns}
                style={tableStyle}
                pagination={{
                    defaultPageSize: 10,
                    showSizeChanger: true,
                    hideOnSinglePage: networkData.length <= 10
                }}
            />
            <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
            <MLAGConfigurationForm
                ref={mlagConfigurationFormRef}
                networks={networks}
                setCampusFabricData={setCampusFabricData}
            />
            <NACConfigurationForm
                ref={nacConfigurationFormRef}
                networkData={networkData}
                networks={networks}
                setCampusFabricData={setCampusFabricData}
                tableStyle={tableStyle}
            />
            <DHCPSnoopingForm
                ref={dhcpSnoopingRef}
                networkData={networkData}
                networks={networks}
                setCampusFabricData={setCampusFabricData}
                tableStyle={tableStyle}
            />
            <DHCPRelayForm
                ref={dhcpRelayRef}
                networkData={networkData}
                networks={networks}
                setCampusFabricData={setCampusFabricData}
                tableStyle={tableStyle}
            />
        </div>
    );
});

export default CampusFabricMLAGStep3;
