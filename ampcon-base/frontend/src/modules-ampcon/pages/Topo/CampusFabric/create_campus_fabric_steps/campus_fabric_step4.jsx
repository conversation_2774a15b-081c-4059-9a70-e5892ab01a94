import {forwardRef} from "react";
import CampusFabricMLAGStep4 from "@/modules-ampcon/pages/Topo/CampusFabric/create_campus_fabric_steps/mlag_steps/campus_fabric_mlag_step4";
import CampusFabricIPClosStep4 from "@/modules-ampcon/pages/Topo/CampusFabric/create_campus_fabric_steps/ip_clos_steps/campus_fabric_ip_clos_step4";

const CampusFabricStep4 = forwardRef(
    ({currentFabricTopologyType, campusFabricData, setCampusFabricData, allModelPhysicPortInfo}, ref) => {
        return currentFabricTopologyType === "ip-clos" ? (
            <CampusFabricIPClosStep4
                ref={ref}
                campusFabricData={campusFabricData}
                setCampusFabricData={setCampusFabricData}
                allModelPhysicPortInfo={allModelPhysicPortInfo}
                currentFabricTopologyType={currentFabricTopologyType}
            />
        ) : (
            <CampusFabricMLAGStep4
                ref={ref}
                campusFabricData={campusFabricData}
                setCampusFabricData={setCampusFabricData}
                allModelPhysicPortInfo={allModelPhysicPortInfo}
                currentFabricTopologyType={currentFabricTopologyType}
            />
        );
    }
);
export default CampusFabricStep4;
