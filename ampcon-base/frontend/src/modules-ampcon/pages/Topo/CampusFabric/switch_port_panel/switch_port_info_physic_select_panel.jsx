import {But<PERSON>, <PERSON>lex} from "antd";
import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from "react";
import PortNode from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/port_node";

const SwitchPortInfoPhysicSelectPanel = forwardRef(
    (
        {
            physicPortData,
            portLabelComponent,
            linkToCorePorts,
            linkToAccessPorts,
            linkToWANPorts,
            linkToDisPorts,
            linkToBorderPorts,
            selectedPortCardFunc,
            updateCurrentPortConfigForTheSameModelSwitch,
            record,
            tabType,
            podType,
            currentFabricTopologyType,
            isUseCoreAsBorder,
            isDisNodeExists,
            linkToWANExist = false
        },
        ref
    ) => {
        const [displayPairPhysicPortInfo, setDisplayPairPhysicPortInfo] = useState([]);
        const [hoveredPortIndex, setHoveredPortIndex] = useState(null);
        const portNodeRefs = useRef({});

        useImperativeHandle(ref, () => ({
            refreshPort: (portName, newStatus) => {
                portNodeRefs.current[portName].current.updateNodeStatus(newStatus);
            },
            refreshAllPortStatus: () => {
                refreshDisplayPairPhysicPortInfo(true);
            },
            clearAllPortsSelectedStatus: () => {
                Object.entries(portNodeRefs.current).forEach(([_, ref]) => {
                    ref.current.clearSelectedStatus();
                });
            }
        }));

        useEffect(() => {
            refreshDisplayPairPhysicPortInfo(false);
        }, []);

        const refreshDisplayPairPhysicPortInfo = (isForce = false) => {
            const physicPortDataTemp = JSON.parse(JSON.stringify(physicPortData));
            const portNameStatusMappings = {};
            for (let i = 0; i < physicPortDataTemp.length; i++) {
                for (let j = 0; j < physicPortDataTemp[i].length; j++) {
                    if (physicPortDataTemp[i][j] !== null) {
                        const tempStatus = calculatePortStatus(physicPortDataTemp[i][j].portName);
                        physicPortDataTemp[i][j].status = tempStatus;
                        portNameStatusMappings[physicPortDataTemp[i][j].portName] = tempStatus;
                    }
                }
            }

            if (isForce === true) {
                Object.keys(portNodeRefs.current).forEach(portName => {
                    portNodeRefs.current[portName].current.updateNodeStatus(portNameStatusMappings[portName]);
                });
            } else {
                setDisplayPairPhysicPortInfo(physicPortDataTemp);
            }
        };

        const handleMouseEnter = index => {
            setHoveredPortIndex(index);
        };

        const handleMouseLeave = () => {
            setHoveredPortIndex(null);
        };

        const calculatePortStatus = status => {
            if (Object.keys(linkToCorePorts).includes(status)) {
                return "selectedToCore";
            }
            if (Object.keys(linkToAccessPorts).includes(status)) {
                return "selectedToAccess";
            }
            if (linkToWANPorts && Object.keys(linkToWANPorts).includes(status)) {
                return "selectedToWAN";
            }
            if (linkToBorderPorts && Object.keys(linkToBorderPorts).includes(status)) {
                return "selectedToBorder";
            }
            if (linkToDisPorts && Object.keys(linkToDisPorts).includes(status)) {
                return "selectedToDis";
            }
            return "common";
        };

        const calculateDisplayPortStatus = (status, index) => {
            if (status === "common") {
                return hoveredPortIndex === index ? "hover" : "common";
            }
            return status;
        };

        return (
            <>
                <Flex
                    gap="16px"
                    style={{
                        position: "absolute",
                        right: "16px",
                        top: "16px"
                    }}
                >
                    <Flex>
                        <Button
                            disabled={(() => {
                                if (currentFabricTopologyType === "mlag") {
                                    if (tabType === "core") {
                                        return (
                                            record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length ||
                                            record.maxLinkToAccessCount !== Object.keys(linkToAccessPorts).length
                                        );
                                    }
                                    return record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length;
                                }

                                if (currentFabricTopologyType === "ip-clos") {
                                    if (tabType === "border") {
                                        return record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length;
                                    }

                                    if (tabType === "core") {
                                        if (linkToWANExist && Object.keys(linkToWANPorts).length !== 1) {
                                            return true;
                                        }
                                        if (!isUseCoreAsBorder) {
                                            if (isDisNodeExists) {
                                                return (
                                                    record.maxLinkToBorderCount !==
                                                        Object.keys(linkToBorderPorts).length ||
                                                    record.maxLinkToDisCount !== Object.keys(linkToDisPorts).length ||
                                                    record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length
                                                );
                                            }
                                            return (
                                                record.maxLinkToBorderCount !== Object.keys(linkToBorderPorts).length ||
                                                record.maxLinkToAccessCount !== Object.keys(linkToAccessPorts).length ||
                                                record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length
                                            );
                                        }

                                        if (isDisNodeExists) {
                                            return (
                                                record.maxLinkToDisCount !== Object.keys(linkToDisPorts).length ||
                                                record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length
                                            );
                                        }
                                        return (
                                            record.maxLinkToAccessCount !== Object.keys(linkToAccessPorts).length ||
                                            record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length
                                        );
                                    }

                                    if (tabType === "pod") {
                                        if (
                                            record.maxLinkToAccessCount &&
                                            record.maxLinkToCoreCount &&
                                            !record.maxLinkToDisCount
                                        ) {
                                            return (
                                                record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length ||
                                                record.maxLinkToAccessCount !== Object.keys(linkToAccessPorts).length
                                            );
                                        }
                                        return isDisNodeExists
                                            ? record.maxLinkToDisCount !== Object.keys(linkToDisPorts).length
                                            : record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length;
                                    }
                                }
                                return false;
                            })()}
                            onClick={() => {
                                updateCurrentPortConfigForTheSameModelSwitch({
                                    currentLinkToCore: linkToCorePorts,
                                    currentLinkToAccess: linkToAccessPorts,
                                    currentLinkToWAN: linkToWANPorts,
                                    currentLinkToDis: linkToDisPorts,
                                    currentLinkToBorder: linkToBorderPorts,
                                    model: record.model,
                                    podName: record.podName,
                                    podType
                                });
                            }}
                        >
                            Apply to All {record.model} Models
                        </Button>
                    </Flex>
                </Flex>
                <Flex style={{marginTop: "32px"}}>{portLabelComponent}</Flex>
                <Flex wrap gap="small" style={{paddingLeft: "50px", paddingBottom: "32px", width: "100%"}}>
                    <Flex horizontal>
                        {displayPairPhysicPortInfo.map(ports => {
                            return (
                                <Flex vertical gap="8px">
                                    {ports.map(port => {
                                        if (port === null) {
                                            return <div style={{height: "39.71px"}}> </div>;
                                        }
                                        portNodeRefs.current[port.portName] = React.createRef();
                                        return (
                                            <Flex gap={port.isGap ? "32px" : "0px"}>
                                                <PortNode
                                                    ref={portNodeRefs.current[port.portName]}
                                                    index={port.label}
                                                    portName={port.portName}
                                                    portType={port.portType}
                                                    status={calculateDisplayPortStatus(port.status, port.label)}
                                                    isSvgReverse={port.isReverse}
                                                    handleMouseEnter={() => {
                                                        handleMouseEnter(port.label);
                                                    }}
                                                    handleMouseLeave={handleMouseLeave}
                                                    selectedPortCardFunc={selectedPortCardFunc}
                                                    record={record}
                                                />
                                                <div style={{width: "8px"}} />
                                            </Flex>
                                        );
                                    })}
                                </Flex>
                            );
                        })}
                    </Flex>
                </Flex>
            </>
        );
    }
);

export default SwitchPortInfoPhysicSelectPanel;
