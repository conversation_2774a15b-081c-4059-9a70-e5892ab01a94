import PortTypeLabel from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/port_type_label";

const SwitchPortPhysicData = {
    "N8550-32C": {
        portData: [
            [
                {
                    label: 1,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "xe-1/1/3",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "xe-1/1/4",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "xe-1/1/5",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "xe-1/1/6",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "xe-1/1/7",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "xe-1/1/8",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "xe-1/1/9",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "xe-1/1/10",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "xe-1/1/11",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "xe-1/1/12",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "xe-1/1/13",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "xe-1/1/14",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "xe-1/1/15",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "xe-1/1/16",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "xe-1/1/17",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "xe-1/1/18",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "xe-1/1/19",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "xe-1/1/20",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "xe-1/1/21",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "xe-1/1/22",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "xe-1/1/23",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "xe-1/1/24",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "xe-1/1/25",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "xe-1/1/26",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "xe-1/1/27",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "xe-1/1/28",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "xe-1/1/29",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "xe-1/1/30",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "xe-1/1/31",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "xe-1/1/32",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 33,
                    portName: "te-1/1/1",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 34,
                    portName: "te-1/1/2",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="100G QSFP28" portNum={16} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="10G SFP+" portNum={1} paddingLeft={32} paddingTop={32} />
            </>
        )
    },
    "S4720-48BC": {
        portData: [
            [
                {
                    label: 1,
                    portName: "te-1/1/1",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "te-1/1/2",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 3,
                    portName: "te-1/1/3",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 4,
                    portName: "te-1/1/4",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 5,
                    portName: "te-1/1/5",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 6,
                    portName: "te-1/1/6",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "te-1/1/7",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "te-1/1/8",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 9,
                    portName: "te-1/1/9",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 10,
                    portName: "te-1/1/10",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 11,
                    portName: "te-1/1/11",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 12,
                    portName: "te-1/1/12",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "te-1/1/13",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "te-1/1/14",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 15,
                    portName: "te-1/1/15",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 16,
                    portName: "te-1/1/16",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 17,
                    portName: "te-1/1/17",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 18,
                    portName: "te-1/1/18",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "te-1/1/19",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "te-1/1/20",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 21,
                    portName: "te-1/1/21",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 22,
                    portName: "te-1/1/22",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 23,
                    portName: "te-1/1/23",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 24,
                    portName: "te-1/1/24",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "te-1/1/25",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "te-1/1/26",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 27,
                    portName: "te-1/1/27",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 28,
                    portName: "te-1/1/28",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 29,
                    portName: "te-1/1/29",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 30,
                    portName: "te-1/1/30",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "te-1/1/31",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "te-1/1/32",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 33,
                    portName: "te-1/1/33",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 34,
                    portName: "te-1/1/34",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 35,
                    portName: "te-1/1/35",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 36,
                    portName: "te-1/1/36",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 37,
                    portName: "te-1/1/37",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "te-1/1/38",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 39,
                    portName: "te-1/1/39",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 40,
                    portName: "te-1/1/40",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 41,
                    portName: "te-1/1/41",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 42,
                    portName: "te-1/1/42",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 43,
                    portName: "te-1/1/43",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "te-1/1/44",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 45,
                    portName: "te-1/1/45",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 46,
                    portName: "te-1/1/46",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 47,
                    portName: "te-1/1/47",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                },
                {
                    label: 48,
                    portName: "te-1/1/48",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 49,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 50,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 51,
                    portName: "xe-1/1/3",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 52,
                    portName: "xe-1/1/4",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 53,
                    portName: "xe-1/1/5",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 54,
                    portName: "xe-1/1/6",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 55,
                    portName: "xe-1/1/7",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                },
                {
                    label: 56,
                    portName: "xe-1/1/8",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 57,
                    portName: "te-1/1/49",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 58,
                    portName: "te-1/1/50",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="25G SFP28" portNum={16} paddingLeft={50} paddingTop={32} />
                <div style={{height: "0px"}}>
                    <PortTypeLabel portLabel="100G QSFP28" portNum={4} paddingLeft={46} paddingTop={72.71} />
                </div>
                <PortTypeLabel portLabel="10G SFP+" portNum={1} paddingLeft={31} paddingTop={29.29} />
            </>
        )
    },
    "S5580-48Y": {
        portData: [
            [
                {
                    label: 1,
                    portName: "te-1/1/1",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "te-1/1/2",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 3,
                    portName: "te-1/1/3",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 4,
                    portName: "te-1/1/4",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 5,
                    portName: "te-1/1/5",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 6,
                    portName: "te-1/1/6",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "te-1/1/7",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "te-1/1/8",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 9,
                    portName: "te-1/1/9",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 10,
                    portName: "te-1/1/10",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 11,
                    portName: "te-1/1/11",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 12,
                    portName: "te-1/1/12",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "te-1/1/13",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "te-1/1/14",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 15,
                    portName: "te-1/1/15",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 16,
                    portName: "te-1/1/16",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 17,
                    portName: "te-1/1/17",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 18,
                    portName: "te-1/1/18",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "te-1/1/19",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "te-1/1/20",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 21,
                    portName: "te-1/1/21",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 22,
                    portName: "te-1/1/22",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 23,
                    portName: "te-1/1/23",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 24,
                    portName: "te-1/1/24",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "te-1/1/25",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "te-1/1/26",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 27,
                    portName: "te-1/1/27",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 28,
                    portName: "te-1/1/28",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 29,
                    portName: "te-1/1/29",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 30,
                    portName: "te-1/1/30",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "te-1/1/31",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "te-1/1/32",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 33,
                    portName: "te-1/1/33",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 34,
                    portName: "te-1/1/34",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 35,
                    portName: "te-1/1/35",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 36,
                    portName: "te-1/1/36",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 37,
                    portName: "te-1/1/37",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "te-1/1/38",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 39,
                    portName: "te-1/1/39",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 40,
                    portName: "te-1/1/40",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 41,
                    portName: "te-1/1/41",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 42,
                    portName: "te-1/1/42",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 43,
                    portName: "te-1/1/43",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "te-1/1/44",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 45,
                    portName: "te-1/1/45",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 46,
                    portName: "te-1/1/46",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 47,
                    portName: "te-1/1/47",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                },
                {
                    label: 48,
                    portName: "te-1/1/48",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 49,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 50,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 51,
                    portName: "xe-1/1/3",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 52,
                    portName: "xe-1/1/4",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 53,
                    portName: "xe-1/1/5",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 54,
                    portName: "xe-1/1/6",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 55,
                    portName: "xe-1/1/7",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                },
                {
                    label: 56,
                    portName: "xe-1/1/8",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 57,
                    portName: "te-1/1/49",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 58,
                    portName: "te-1/1/50",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="25G SFP28" portNum={16} paddingLeft={50} paddingTop={32} />
                <div style={{height: "0px"}}>
                    <PortTypeLabel portLabel="100G QSFP28" portNum={4} paddingLeft={46} paddingTop={72.71} />
                </div>
                <PortTypeLabel portLabel="10G SFP+" portNum={1} paddingLeft={31} paddingTop={29.29} />
            </>
        )
    },
    "N8550-48B8C": {
        portData: [
            [
                {
                    label: 1,
                    portName: "te-1/1/1",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "te-1/1/2",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 3,
                    portName: "te-1/1/3",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 4,
                    portName: "te-1/1/4",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 5,
                    portName: "te-1/1/5",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 6,
                    portName: "te-1/1/6",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "te-1/1/7",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "te-1/1/8",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 9,
                    portName: "te-1/1/9",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 10,
                    portName: "te-1/1/10",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 11,
                    portName: "te-1/1/11",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 12,
                    portName: "te-1/1/12",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "te-1/1/13",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "te-1/1/14",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 15,
                    portName: "te-1/1/15",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 16,
                    portName: "te-1/1/16",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 17,
                    portName: "te-1/1/17",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 18,
                    portName: "te-1/1/18",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "te-1/1/19",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "te-1/1/20",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 21,
                    portName: "te-1/1/21",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 22,
                    portName: "te-1/1/22",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 23,
                    portName: "te-1/1/23",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 24,
                    portName: "te-1/1/24",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "te-1/1/25",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "te-1/1/26",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 27,
                    portName: "te-1/1/27",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 28,
                    portName: "te-1/1/28",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 29,
                    portName: "te-1/1/29",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 30,
                    portName: "te-1/1/30",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "te-1/1/31",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "te-1/1/32",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 33,
                    portName: "te-1/1/33",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 34,
                    portName: "te-1/1/34",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 35,
                    portName: "te-1/1/35",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 36,
                    portName: "te-1/1/36",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 37,
                    portName: "te-1/1/37",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "te-1/1/38",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 39,
                    portName: "te-1/1/39",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 40,
                    portName: "te-1/1/40",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 41,
                    portName: "te-1/1/41",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 42,
                    portName: "te-1/1/42",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 43,
                    portName: "te-1/1/43",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "te-1/1/44",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 45,
                    portName: "te-1/1/45",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 46,
                    portName: "te-1/1/46",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 47,
                    portName: "te-1/1/47",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                },
                {
                    label: 48,
                    portName: "te-1/1/48",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 49,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 50,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 51,
                    portName: "xe-1/1/3",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 52,
                    portName: "xe-1/1/4",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 53,
                    portName: "xe-1/1/5",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 54,
                    portName: "xe-1/1/6",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 55,
                    portName: "xe-1/1/7",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                },
                {
                    label: 56,
                    portName: "xe-1/1/8",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 57,
                    portName: "te-1/1/49",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 58,
                    portName: "te-1/1/50",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="25G SFP28" portNum={16} paddingLeft={50} paddingTop={32} />
                <div style={{height: "0px"}}>
                    <PortTypeLabel portLabel="100G QSFP28" portNum={4} paddingLeft={46} paddingTop={72.71} />
                </div>
                <PortTypeLabel portLabel="10G SFP+" portNum={1} paddingLeft={31} paddingTop={29.29} />
            </>
        )
    },
    "N8550-64C": {
        portData: [
            [
                {
                    label: 1,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 33,
                    portName: "xe-1/1/33",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 34,
                    portName: "xe-1/1/34",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "xe-1/1/3",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "xe-1/1/4",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 35,
                    portName: "xe-1/1/35",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 36,
                    portName: "xe-1/1/36",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "xe-1/1/5",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "xe-1/1/6",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 37,
                    portName: "xe-1/1/37",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "xe-1/1/38",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "xe-1/1/7",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "xe-1/1/8",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 39,
                    portName: "xe-1/1/39",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 40,
                    portName: "xe-1/1/40",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "xe-1/1/9",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "xe-1/1/10",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 41,
                    portName: "xe-1/1/41",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 42,
                    portName: "xe-1/1/42",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "xe-1/1/11",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "xe-1/1/12",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 43,
                    portName: "xe-1/1/43",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "xe-1/1/44",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "xe-1/1/13",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "xe-1/1/14",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 45,
                    portName: "xe-1/1/45",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 46,
                    portName: "xe-1/1/46",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "xe-1/1/15",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "xe-1/1/16",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 47,
                    portName: "xe-1/1/47",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 48,
                    portName: "xe-1/1/48",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "xe-1/1/17",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "xe-1/1/18",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 49,
                    portName: "xe-1/1/49",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 50,
                    portName: "xe-1/1/50",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "xe-1/1/19",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "xe-1/1/20",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 51,
                    portName: "xe-1/1/51",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 52,
                    portName: "xe-1/1/52",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "xe-1/1/21",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "xe-1/1/22",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 53,
                    portName: "xe-1/1/53",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 54,
                    portName: "xe-1/1/54",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "xe-1/1/23",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "xe-1/1/24",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 55,
                    portName: "xe-1/1/55",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 56,
                    portName: "xe-1/1/56",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "xe-1/1/25",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "xe-1/1/26",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 57,
                    portName: "xe-1/1/57",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 58,
                    portName: "xe-1/1/58",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "xe-1/1/27",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "xe-1/1/28",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 59,
                    portName: "xe-1/1/59",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 60,
                    portName: "xe-1/1/60",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "xe-1/1/29",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "xe-1/1/30",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 61,
                    portName: "xe-1/1/61",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 62,
                    portName: "xe-1/1/62",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "xe-1/1/31",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "xe-1/1/32",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                },
                {
                    label: 63,
                    portName: "xe-1/1/63",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 64,
                    portName: "xe-1/1/64",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: <PortTypeLabel portLabel="100G QSFP28" portNum={16} paddingLeft={50} paddingTop={32} />
    },
    "N8560-32C": {
        portData: [
            [
                {
                    label: 1,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "xe-1/1/3",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "xe-1/1/4",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "xe-1/1/5",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "xe-1/1/6",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "xe-1/1/7",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "xe-1/1/8",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "xe-1/1/9",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "xe-1/1/10",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "xe-1/1/11",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "xe-1/1/12",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "xe-1/1/13",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "xe-1/1/14",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "xe-1/1/15",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "xe-1/1/16",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "xe-1/1/17",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "xe-1/1/18",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "xe-1/1/19",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "xe-1/1/20",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "xe-1/1/21",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "xe-1/1/22",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "xe-1/1/23",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "xe-1/1/24",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "xe-1/1/25",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "xe-1/1/26",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "xe-1/1/27",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "xe-1/1/28",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "xe-1/1/29",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "xe-1/1/30",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "xe-1/1/31",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "xe-1/1/32",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: <PortTypeLabel portLabel="100G QSFP28" portNum={16} paddingLeft={50} paddingTop={32} />
    },
    "S4520-48X6C": {
        portData: [
            [
                {
                    label: 1,
                    portName: "te-1/1/1",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "te-1/1/2",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "te-1/1/3",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "te-1/1/4",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "te-1/1/5",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "te-1/1/6",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "te-1/1/7",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "te-1/1/8",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "te-1/1/9",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "te-1/1/10",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "te-1/1/11",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "te-1/1/12",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "te-1/1/13",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "te-1/1/14",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "te-1/1/15",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "te-1/1/16",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "te-1/1/17",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "te-1/1/18",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "te-1/1/19",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "te-1/1/20",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "te-1/1/21",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "te-1/1/22",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "te-1/1/23",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "te-1/1/24",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "te-1/1/25",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "te-1/1/26",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "te-1/1/27",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "te-1/1/28",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "te-1/1/29",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "te-1/1/30",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "te-1/1/31",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "te-1/1/32",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 33,
                    portName: "te-1/1/33",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 34,
                    portName: "te-1/1/34",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 35,
                    portName: "te-1/1/35",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 36,
                    portName: "te-1/1/36",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 37,
                    portName: "te-1/1/37",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "te-1/1/38",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 39,
                    portName: "te-1/1/39",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 40,
                    portName: "te-1/1/40",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 41,
                    portName: "te-1/1/41",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 42,
                    portName: "te-1/1/42",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 43,
                    portName: "te-1/1/43",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "te-1/1/44",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 45,
                    portName: "te-1/1/45",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 46,
                    portName: "te-1/1/46",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 47,
                    portName: "te-1/1/47",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 48,
                    portName: "te-1/1/48",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 49,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 50,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 51,
                    portName: "xe-1/1/3",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 52,
                    portName: "xe-1/1/4",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 53,
                    portName: "xe-1/1/5",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                },
                {
                    label: 54,
                    portName: "xe-1/1/6",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="10G RJ45" portNum={24} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="100G QSFP28" portNum={2} paddingLeft={42} paddingTop={32} />
            </>
        )
    },
    "N5850-48X6C": {
        portData: [
            [
                {
                    label: 1,
                    portName: "te-1/1/1",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "te-1/1/2",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "te-1/1/3",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "te-1/1/4",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "te-1/1/5",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "te-1/1/6",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "te-1/1/7",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "te-1/1/8",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "te-1/1/9",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "te-1/1/10",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "te-1/1/11",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "te-1/1/12",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "te-1/1/13",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "te-1/1/14",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "te-1/1/15",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "te-1/1/16",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "te-1/1/17",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "te-1/1/18",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "te-1/1/19",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "te-1/1/20",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "te-1/1/21",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "te-1/1/22",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "te-1/1/23",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "te-1/1/24",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "te-1/1/25",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "te-1/1/26",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "te-1/1/27",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "te-1/1/28",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "te-1/1/29",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "te-1/1/30",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "te-1/1/31",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "te-1/1/32",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 33,
                    portName: "te-1/1/33",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 34,
                    portName: "te-1/1/34",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 35,
                    portName: "te-1/1/35",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 36,
                    portName: "te-1/1/36",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 37,
                    portName: "te-1/1/37",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "te-1/1/38",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 39,
                    portName: "te-1/1/39",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 40,
                    portName: "te-1/1/40",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 41,
                    portName: "te-1/1/41",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 42,
                    portName: "te-1/1/42",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 43,
                    portName: "te-1/1/43",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "te-1/1/44",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 45,
                    portName: "te-1/1/45",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 46,
                    portName: "te-1/1/46",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 47,
                    portName: "te-1/1/47",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 48,
                    portName: "te-1/1/48",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 49,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 50,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                },
                {
                    label: 51,
                    portName: "xe-1/1/3",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 52,
                    portName: "xe-1/1/4",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 53,
                    portName: "xe-1/1/5",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                },
                {
                    label: 54,
                    portName: "xe-1/1/6",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="10G RJ45" portNum={24} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="100G QSFP28" portNum={2} paddingLeft={42} paddingTop={32} />
            </>
        )
    },
    "S5890-32C": {
        portData: [
            [
                {
                    label: 1,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "xe-1/1/3",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "xe-1/1/4",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "xe-1/1/5",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "xe-1/1/6",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "xe-1/1/7",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "xe-1/1/8",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "xe-1/1/9",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "xe-1/1/10",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "xe-1/1/11",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "xe-1/1/12",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "xe-1/1/13",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "xe-1/1/14",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "xe-1/1/15",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "xe-1/1/16",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "xe-1/1/17",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "xe-1/1/18",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "xe-1/1/19",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "xe-1/1/20",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "xe-1/1/21",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "xe-1/1/22",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "xe-1/1/23",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "xe-1/1/24",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "xe-1/1/25",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "xe-1/1/26",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "xe-1/1/27",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "xe-1/1/28",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "xe-1/1/29",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "xe-1/1/30",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "xe-1/1/31",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "xe-1/1/32",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: <PortTypeLabel portLabel="100G QSFP28" portNum={16} paddingLeft={50} paddingTop={32} />
    },
    "S5870-48T6BC-U": {
        portData: [
            [
                {
                    label: 1,
                    portName: "ge-1/1/1",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "ge-1/1/2",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "ge-1/1/3",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "ge-1/1/4",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "ge-1/1/5",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "ge-1/1/6",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "ge-1/1/7",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "ge-1/1/8",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "ge-1/1/9",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "ge-1/1/10",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "ge-1/1/11",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "ge-1/1/12",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "ge-1/1/13",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "ge-1/1/14",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "ge-1/1/15",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "ge-1/1/16",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "ge-1/1/17",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "ge-1/1/18",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "ge-1/1/19",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "ge-1/1/20",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "ge-1/1/21",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "ge-1/1/22",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "ge-1/1/23",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "ge-1/1/24",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "ge-1/1/25",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "ge-1/1/26",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "ge-1/1/27",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "ge-1/1/28",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "ge-1/1/29",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "ge-1/1/30",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "ge-1/1/31",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "ge-1/1/32",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 33,
                    portName: "ge-1/1/33",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 34,
                    portName: "ge-1/1/34",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 35,
                    portName: "ge-1/1/35",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 36,
                    portName: "ge-1/1/36",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 37,
                    portName: "ge-1/1/37",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "ge-1/1/38",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 39,
                    portName: "ge-1/1/39",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 40,
                    portName: "ge-1/1/40",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 41,
                    portName: "ge-1/1/41",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 42,
                    portName: "ge-1/1/42",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 43,
                    portName: "ge-1/1/43",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "ge-1/1/44",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 45,
                    portName: "ge-1/1/45",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 46,
                    portName: "ge-1/1/46",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 47,
                    portName: "ge-1/1/47",
                    portType: "ge",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 48,
                    portName: "ge-1/1/48",
                    portType: "ge",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 49,
                    portName: "te-1/1/1",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 50,
                    portName: "te-1/1/2",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 51,
                    portName: "te-1/1/3",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 52,
                    portName: "te-1/1/4",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 53,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 54,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="1G RJ45" portNum={24} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="25G SFP28" portNum={2} paddingLeft={49} paddingTop={32} />
                <PortTypeLabel portLabel="100G QSFP28" portNum={1} paddingLeft={77} paddingTop={32} />
            </>
        )
    },
    "S6860-24CD8D": {
        portData: [
            [
                {
                    label: 1,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "xe-1/1/3",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "xe-1/1/4",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "xe-1/1/5",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "xe-1/1/6",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "xe-1/1/7",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "xe-1/1/8",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "xe-1/1/9",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "xe-1/1/10",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "xe-1/1/11",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "xe-1/1/12",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "xe-1/1/13",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "xe-1/1/14",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "xe-1/1/15",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "xe-1/1/16",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "xe-1/1/17",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "xe-1/1/18",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "xe-1/1/19",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "xe-1/1/20",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "xe-1/1/21",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "xe-1/1/22",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "xe-1/1/23",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "xe-1/1/24",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "xe-1/1/25",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "xe-1/1/26",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "xe-1/1/27",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "xe-1/1/28",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "xe-1/1/29",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "xe-1/1/30",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "xe-1/1/31",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "xe-1/1/32",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="200G QSFP56" portNum={12} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="400G QSFP-DD" portNum={4} paddingLeft={46} paddingTop={32} />
            </>
        )
    },
    "N8550-24CD8D": {
        portData: [
            [
                {
                    label: 1,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "xe-1/1/3",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "xe-1/1/4",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "xe-1/1/5",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "xe-1/1/6",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "xe-1/1/7",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "xe-1/1/8",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "xe-1/1/9",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "xe-1/1/10",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "xe-1/1/11",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "xe-1/1/12",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "xe-1/1/13",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "xe-1/1/14",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "xe-1/1/15",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "xe-1/1/16",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "xe-1/1/17",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "xe-1/1/18",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "xe-1/1/19",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "xe-1/1/20",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "xe-1/1/21",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "xe-1/1/22",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "xe-1/1/23",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "xe-1/1/24",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "xe-1/1/25",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "xe-1/1/26",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "xe-1/1/27",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "xe-1/1/28",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "xe-1/1/29",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "xe-1/1/30",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "xe-1/1/31",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "xe-1/1/32",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="200G QSFP56" portNum={12} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="400G QSFP-DD" portNum={4} paddingLeft={46} paddingTop={32} />
            </>
        )
    },
    "S5870-48MX6BC-U": {
        portData: [
            [
                {
                    label: 1,
                    portName: "ge-1/1/1",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "ge-1/1/2",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "ge-1/1/3",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "ge-1/1/4",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "ge-1/1/5",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "ge-1/1/6",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "ge-1/1/7",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "ge-1/1/8",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "ge-1/1/9",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "ge-1/1/10",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "ge-1/1/11",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "ge-1/1/12",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "ge-1/1/13",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "ge-1/1/14",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "ge-1/1/15",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "ge-1/1/16",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "ge-1/1/17",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "ge-1/1/18",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "ge-1/1/19",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "ge-1/1/20",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "ge-1/1/21",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "ge-1/1/22",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "ge-1/1/23",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "ge-1/1/24",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "ge-1/1/25",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "ge-1/1/26",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "ge-1/1/27",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "ge-1/1/28",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "ge-1/1/29",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "ge-1/1/30",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "ge-1/1/31",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "ge-1/1/32",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 33,
                    portName: "ge-1/1/33",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 34,
                    portName: "ge-1/1/34",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 35,
                    portName: "ge-1/1/35",
                    portType: "ge",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 36,
                    portName: "ge-1/1/36",
                    portType: "ge",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 37,
                    portName: "ge-1/1/37",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "ge-1/1/38",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 39,
                    portName: "ge-1/1/39",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 40,
                    portName: "ge-1/1/40",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 41,
                    portName: "ge-1/1/41",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 42,
                    portName: "ge-1/1/42",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 43,
                    portName: "ge-1/1/43",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "ge-1/1/44",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 45,
                    portName: "ge-1/1/45",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 46,
                    portName: "ge-1/1/46",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 47,
                    portName: "ge-1/1/47",
                    portType: "ge",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 48,
                    portName: "ge-1/1/48",
                    portType: "ge",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 49,
                    portName: "te-1/1/1",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 50,
                    portName: "te-1/1/2",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 51,
                    portName: "te-1/1/3",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 52,
                    portName: "te-1/1/4",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 53,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 54,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="2.5G RJ45" portNum={18} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="10G RJ45" portNum={6} paddingLeft={46} paddingTop={32} />
                <PortTypeLabel portLabel="25G SFP28" portNum={2} paddingLeft={49} paddingTop={32} />
                <PortTypeLabel portLabel="100G QSFP28" portNum={1} paddingLeft={77} paddingTop={32} />
            </>
        )
    },
    "S5870-48T6BC": {
        portData: [
            [
                {
                    label: 1,
                    portName: "ge-1/1/1",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "ge-1/1/2",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "ge-1/1/3",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "ge-1/1/4",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "ge-1/1/5",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "ge-1/1/6",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "ge-1/1/7",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "ge-1/1/8",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "ge-1/1/9",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "ge-1/1/10",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "ge-1/1/11",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "ge-1/1/12",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "ge-1/1/13",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "ge-1/1/14",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "ge-1/1/15",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "ge-1/1/16",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "ge-1/1/17",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "ge-1/1/18",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "ge-1/1/19",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "ge-1/1/20",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "ge-1/1/21",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "ge-1/1/22",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "ge-1/1/23",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "ge-1/1/24",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "ge-1/1/25",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "ge-1/1/26",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "ge-1/1/27",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "ge-1/1/28",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "ge-1/1/29",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "ge-1/1/30",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "ge-1/1/31",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "ge-1/1/32",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 33,
                    portName: "ge-1/1/33",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 34,
                    portName: "ge-1/1/34",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 35,
                    portName: "ge-1/1/35",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 36,
                    portName: "ge-1/1/36",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 37,
                    portName: "ge-1/1/37",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "ge-1/1/38",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 39,
                    portName: "ge-1/1/39",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 40,
                    portName: "ge-1/1/40",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 41,
                    portName: "ge-1/1/41",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 42,
                    portName: "ge-1/1/42",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 43,
                    portName: "ge-1/1/43",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "ge-1/1/44",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 45,
                    portName: "ge-1/1/45",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 46,
                    portName: "ge-1/1/46",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 47,
                    portName: "ge-1/1/47",
                    portType: "ge",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 48,
                    portName: "ge-1/1/48",
                    portType: "ge",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 49,
                    portName: "te-1/1/1",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 50,
                    portName: "te-1/1/2",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 51,
                    portName: "te-1/1/3",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 52,
                    portName: "te-1/1/4",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 53,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 54,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="1G RJ45" portNum={24} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="25G SFP28" portNum={2} paddingLeft={49} paddingTop={32} />
                <PortTypeLabel portLabel="100G QSFP28" portNum={1} paddingLeft={77} paddingTop={32} />
            </>
        )
    },
    "S5580-48S": {
        portData: [
            [
                {
                    label: 1,
                    portName: "te-1/1/1",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "te-1/1/2",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "te-1/1/3",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "te-1/1/4",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "te-1/1/5",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "te-1/1/6",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "te-1/1/7",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "te-1/1/8",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "te-1/1/9",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "te-1/1/10",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "te-1/1/11",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "te-1/1/12",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "te-1/1/13",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "te-1/1/14",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "te-1/1/15",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "te-1/1/16",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "te-1/1/17",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "te-1/1/18",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "te-1/1/19",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "te-1/1/20",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "te-1/1/21",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "te-1/1/22",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "te-1/1/23",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "te-1/1/24",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "te-1/1/25",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "te-1/1/26",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "te-1/1/27",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "te-1/1/28",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "te-1/1/29",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "te-1/1/30",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "te-1/1/31",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "te-1/1/32",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 33,
                    portName: "te-1/1/33",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 34,
                    portName: "te-1/1/34",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 35,
                    portName: "te-1/1/35",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 36,
                    portName: "te-1/1/36",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 37,
                    portName: "te-1/1/37",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "te-1/1/38",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 39,
                    portName: "te-1/1/39",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 40,
                    portName: "te-1/1/40",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 41,
                    portName: "te-1/1/41",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 42,
                    portName: "te-1/1/42",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 43,
                    portName: "te-1/1/43",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "te-1/1/44",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 45,
                    portName: "te-1/1/45",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 46,
                    portName: "te-1/1/46",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 47,
                    portName: "te-1/1/47",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 48,
                    portName: "te-1/1/48",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 49,
                    portName: "xe-1/1/49",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 50,
                    portName: "xe-1/1/50",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 51,
                    portName: "xe-1/1/51",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 52,
                    portName: "xe-1/1/52",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 53,
                    portName: "xe-1/1/53",
                    portType: "xe",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 54,
                    portName: "xe-1/1/54",
                    portType: "xe",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 55,
                    portName: "xe-1/1/55",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 56,
                    portName: "xe-1/1/56",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="10G SFP+" portNum={24} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="100G QSFP28" portNum={4} paddingLeft={46} paddingTop={32} />
            </>
        )
    },
    "S5810-28FS": {
        portData: [
            [
                {
                    label: 1,
                    portName: "ge-1/1/1",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "ge-1/1/2",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "ge-1/1/3",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "ge-1/1/4",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "ge-1/1/5",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "ge-1/1/6",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "ge-1/1/7",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "ge-1/1/8",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 1,
                    portName: "te-1/1/1",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "te-1/1/2",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "ge-1/1/3",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "te-1/1/4",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "te-1/1/5",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "te-1/1/6",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "te-1/1/7",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "te-1/1/8",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "te-1/1/9",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "te-1/1/10",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "te-1/1/11",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "te-1/1/12",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "te-1/1/13",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "te-1/1/14",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "te-1/1/15",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "te-1/1/16",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "te-1/1/17",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "te-1/1/18",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "te-1/1/19",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "te-1/1/20",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "te-1/1/21",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "te-1/1/22",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "te-1/1/23",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "te-1/1/24",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "te-1/1/25",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "te-1/1/26",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "te-1/1/27",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "te-1/1/28",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "te-1/1/29",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "te-1/1/30",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "te-1/1/31",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "te-1/1/32",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="1G RJ45/SFP Combo" portNum={8} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="1G SFP" portNum={10} paddingLeft={46} paddingTop={32} />
                <PortTypeLabel portLabel="10G SFP+" portNum={2} paddingLeft={52} paddingTop={32} />
            </>
        )
    },
    "S5810-28TS": {
        portData: [
            [
                {
                    label: 1,
                    portName: "ge-1/1/1",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "ge-1/1/2",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "ge-1/1/3",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "ge-1/1/4",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "ge-1/1/5",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "ge-1/1/6",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "ge-1/1/7",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "ge-1/1/8",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "ge-1/1/9",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "ge-1/1/10",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "ge-1/1/11",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "ge-1/1/12",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "ge-1/1/13",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "ge-1/1/14",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "ge-1/1/15",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "ge-1/1/16",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "ge-1/1/17",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "ge-1/1/18",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "ge-1/1/19",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "ge-1/1/20",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "ge-1/1/21",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "ge-1/1/22",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "ge-1/1/23",
                    portType: "ge",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "ge-1/1/24",
                    portType: "ge",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "ge-1/1/25",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "ge-1/1/26",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "ge-1/1/27",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "ge-1/1/28",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "te-1/1/25",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "te-1/1/26",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "te-1/1/27",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "te-1/1/28",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "te-1/1/29",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "te-1/1/30",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "te-1/1/31",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "te-1/1/32",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="1G RJ45" portNum={12} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="1G RJ45/SFP Combo" portNum={4} paddingLeft={46} paddingTop={32} />
                <PortTypeLabel portLabel="10G SFP+" portNum={2} paddingLeft={50} paddingTop={32} />
            </>
        )
    },
    "S5810-48FS": {
        portData: [
            [
                {
                    label: 1,
                    portName: "te-1/1/1",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "te-1/1/2",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "te-1/1/3",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "te-1/1/4",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "te-1/1/5",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "te-1/1/6",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "te-1/1/7",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "te-1/1/8",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "te-1/1/9",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "te-1/1/10",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "te-1/1/11",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "te-1/1/12",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "te-1/1/13",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "te-1/1/14",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "te-1/1/15",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "te-1/1/16",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "te-1/1/17",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "te-1/1/18",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "te-1/1/19",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "te-1/1/20",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "te-1/1/21",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "te-1/1/22",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "te-1/1/23",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "te-1/1/24",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "te-1/1/25",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "te-1/1/26",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "te-1/1/27",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "te-1/1/28",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "te-1/1/29",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "te-1/1/30",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "te-1/1/31",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "te-1/1/32",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 33,
                    portName: "te-1/1/33",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 34,
                    portName: "te-1/1/34",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 35,
                    portName: "te-1/1/35",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 36,
                    portName: "te-1/1/36",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 37,
                    portName: "te-1/1/37",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "te-1/1/38",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 39,
                    portName: "te-1/1/39",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 40,
                    portName: "te-1/1/40",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 41,
                    portName: "te-1/1/41",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 42,
                    portName: "te-1/1/42",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 43,
                    portName: "te-1/1/43",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "te-1/1/44",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 45,
                    portName: "te-1/1/45",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 46,
                    portName: "te-1/1/46",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 47,
                    portName: "te-1/1/47",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 48,
                    portName: "te-1/1/48",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 49,
                    portName: "te-1/1/49",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 50,
                    portName: "te-1/1/50",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 51,
                    portName: "te-1/1/51",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 52,
                    portName: "te-1/1/52",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="1G SFP" portNum={24} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="10G SFP+" portNum={2} paddingLeft={52} paddingTop={32} />
            </>
        )
    },
    "S5810-48TS": {
        portData: [
            [
                {
                    label: 1,
                    portName: "ge-1/1/1",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "ge-1/1/2",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "ge-1/1/3",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "ge-1/1/4",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "ge-1/1/5",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "ge-1/1/6",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "ge-1/1/7",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "ge-1/1/8",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "ge-1/1/9",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "ge-1/1/10",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "ge-1/1/11",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "ge-1/1/12",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "ge-1/1/13",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "ge-1/1/14",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "ge-1/1/15",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "ge-1/1/16",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "ge-1/1/17",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "ge-1/1/18",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "ge-1/1/19",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "ge-1/1/20",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "ge-1/1/21",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "ge-1/1/22",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "ge-1/1/23",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "ge-1/1/24",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "ge-1/1/25",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "ge-1/1/26",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "ge-1/1/27",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "ge-1/1/28",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "ge-1/1/29",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "ge-1/1/30",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "ge-1/1/31",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "ge-1/1/32",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 33,
                    portName: "ge-1/1/33",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 34,
                    portName: "ge-1/1/34",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 35,
                    portName: "ge-1/1/35",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 36,
                    portName: "ge-1/1/36",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 37,
                    portName: "ge-1/1/37",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "ge-1/1/38",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 39,
                    portName: "ge-1/1/39",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 40,
                    portName: "ge-1/1/40",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 41,
                    portName: "ge-1/1/41",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 42,
                    portName: "ge-1/1/42",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 43,
                    portName: "ge-1/1/43",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "ge-1/1/44",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 45,
                    portName: "ge-1/1/45",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 46,
                    portName: "ge-1/1/46",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 47,
                    portName: "ge-1/1/47",
                    portType: "ge",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 48,
                    portName: "ge-1/1/48",
                    portType: "ge",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 49,
                    portName: "te-1/1/49",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 50,
                    portName: "te-1/1/50",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 51,
                    portName: "te-1/1/51",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 52,
                    portName: "te-1/1/52",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="1G RJ45" portNum={24} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="10G SFP+" portNum={2} paddingLeft={50} paddingTop={32} />
            </>
        )
    },
    "S5810-48TS-P": {
        portData: [
            [
                {
                    label: 1,
                    portName: "ge-1/1/1",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "ge-1/1/2",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "ge-1/1/3",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "ge-1/1/4",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "ge-1/1/5",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "ge-1/1/6",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "ge-1/1/7",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "ge-1/1/8",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "ge-1/1/9",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "ge-1/1/10",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "ge-1/1/11",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "ge-1/1/12",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "ge-1/1/13",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "ge-1/1/14",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "ge-1/1/15",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "ge-1/1/16",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "ge-1/1/17",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "ge-1/1/18",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "ge-1/1/19",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "ge-1/1/20",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "ge-1/1/21",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "ge-1/1/22",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "ge-1/1/23",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "ge-1/1/24",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "ge-1/1/25",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "ge-1/1/26",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "ge-1/1/27",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "ge-1/1/28",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "ge-1/1/29",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "ge-1/1/30",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "ge-1/1/31",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "ge-1/1/32",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 33,
                    portName: "ge-1/1/33",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 34,
                    portName: "ge-1/1/34",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 35,
                    portName: "ge-1/1/35",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 36,
                    portName: "ge-1/1/36",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 37,
                    portName: "ge-1/1/37",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "ge-1/1/38",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 39,
                    portName: "ge-1/1/39",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 40,
                    portName: "ge-1/1/40",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 41,
                    portName: "ge-1/1/41",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 42,
                    portName: "ge-1/1/42",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 43,
                    portName: "ge-1/1/43",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "ge-1/1/44",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 45,
                    portName: "ge-1/1/45",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 46,
                    portName: "ge-1/1/46",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 47,
                    portName: "ge-1/1/47",
                    portType: "ge",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 48,
                    portName: "ge-1/1/48",
                    portType: "ge",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 49,
                    portName: "te-1/1/49",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 50,
                    portName: "te-1/1/50",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 51,
                    portName: "te-1/1/51",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 52,
                    portName: "te-1/1/52",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="1G RJ45" portNum={24} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="10G SFP+" portNum={2} paddingLeft={50} paddingTop={32} />
            </>
        )
    },
    "S5860-20SQ": {
        portData: [
            [
                {
                    label: 1,
                    portName: "ge-1/1/1",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "ge-1/1/2",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "ge-1/1/3",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "ge-1/1/4",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "ge-1/1/5",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "ge-1/1/6",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "ge-1/1/7",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "ge-1/1/8",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "ge-1/1/9",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "ge-1/1/10",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "ge-1/1/11",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "ge-1/1/12",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "ge-1/1/13",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "ge-1/1/14",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "ge-1/1/15",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "ge-1/1/16",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "ge-1/1/17",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "ge-1/1/18",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "ge-1/1/19",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "ge-1/1/20",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "ge-1/1/21",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "ge-1/1/22",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "ge-1/1/23",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "ge-1/1/24",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="10G SFP+" portNum={10} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="25G SFP28" portNum={2} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="40G QSFP+" portNum={1} paddingLeft={82} paddingTop={32} />
            </>
        )
    },
    "S5860-24MG-U": {
        portData: [
            [
                {
                    label: 1,
                    portName: "ge-1/1/1",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "ge-1/1/2",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "ge-1/1/3",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "ge-1/1/4",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "ge-1/1/5",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "ge-1/1/6",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "ge-1/1/7",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "ge-1/1/8",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "ge-1/1/9",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "ge-1/1/10",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "ge-1/1/11",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "ge-1/1/12",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "ge-1/1/13",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "ge-1/1/14",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "ge-1/1/15",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "ge-1/1/16",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "ge-1/1/17",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "ge-1/1/18",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "ge-1/1/19",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "ge-1/1/20",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "ge-1/1/21",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "ge-1/1/22",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "ge-1/1/23",
                    portType: "ge",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "ge-1/1/24",
                    portType: "ge",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 25,
                    portName: "te-1/1/1",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 26,
                    portName: "te-1/1/2",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 27,
                    portName: "te-1/1/3",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 28,
                    portName: "te-1/1/4",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="5G RJ45" portNum={12} paddingLeft={50} paddingTop={32} />
                <div style={{height: "0px"}}>
                    <PortTypeLabel portLabel="25G SFP28" portNum={4} paddingLeft={47} paddingTop={75} />
                </div>
            </>
        )
    },
    "S5860-24XB-U": {
        portData: [
            [
                {
                    label: 1,
                    portName: "te-1/1/1",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "te-1/1/2",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "te-1/1/3",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "te-1/1/4",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "te-1/1/5",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "te-1/1/6",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "te-1/1/7",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "te-1/1/8",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "te-1/1/9",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "te-1/1/10",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "te-1/1/11",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "te-1/1/12",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "te-1/1/13",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "te-1/1/14",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "te-1/1/15",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "te-1/1/16",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "te-1/1/17",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "te-1/1/18",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "te-1/1/19",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "te-1/1/20",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "te-1/1/21",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "te-1/1/22",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "te-1/1/23",
                    portType: "ge",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "te-1/1/24",
                    portType: "ge",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "te-1/1/25",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "te-1/1/26",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "te-1/1/27",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "te-1/1/28",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "te-1/1/29",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "te-1/1/30",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "te-1/1/31",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "te-1/1/32",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="10G RJ45" portNum={12} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="10G SFP+" portNum={2} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="25G SFP28" portNum={2} paddingLeft={100} paddingTop={32} />
            </>
        )
    },
    "S5860-24XMG": {
        portData: [
            [
                {
                    label: 1,
                    portName: "te-1/1/1",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "te-1/1/2",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "te-1/1/3",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "te-1/1/4",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "te-1/1/5",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "te-1/1/6",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "te-1/1/7",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "te-1/1/8",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "te-1/1/9",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "te-1/1/10",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "te-1/1/11",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "te-1/1/12",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "te-1/1/13",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "te-1/1/14",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "te-1/1/15",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "te-1/1/16",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "te-1/1/17",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "te-1/1/18",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "te-1/1/19",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "te-1/1/20",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "te-1/1/21",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "te-1/1/22",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "te-1/1/23",
                    portType: "ge",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "te-1/1/24",
                    portType: "ge",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "te-1/1/25",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "te-1/1/26",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "te-1/1/27",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "te-1/1/28",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 29,
                    portName: "te-1/1/29",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 30,
                    portName: "te-1/1/30",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 31,
                    portName: "te-1/1/31",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                null,
                {
                    label: 32,
                    portName: "te-1/1/32",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="10G RJ45" portNum={12} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="10G SFP+" portNum={2} paddingLeft={50} paddingTop={32} />
                <div style={{height: "0px"}}>
                    <PortTypeLabel portLabel="25G SFP28" portNum={4} paddingLeft={97} paddingTop={75} />
                </div>
            </>
        )
    },
    "S5860-48MG-U": {
        portData: [
            [
                {
                    label: 1,
                    portName: "ge-1/1/1",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "ge-1/1/2",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "ge-1/1/3",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "ge-1/1/4",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "ge-1/1/5",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "ge-1/1/6",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "ge-1/1/7",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "ge-1/1/8",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "ge-1/1/9",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "ge-1/1/10",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "ge-1/1/11",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "ge-1/1/12",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "ge-1/1/13",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "ge-1/1/14",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "ge-1/1/15",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "ge-1/1/16",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "ge-1/1/17",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "ge-1/1/18",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "ge-1/1/19",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "ge-1/1/20",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "ge-1/1/21",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "ge-1/1/22",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "ge-1/1/23",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "ge-1/1/24",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "ge-1/1/25",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "ge-1/1/26",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "ge-1/1/27",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "ge-1/1/28",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "ge-1/1/29",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "ge-1/1/30",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "ge-1/1/31",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "ge-1/1/32",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 33,
                    portName: "ge-1/1/33",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 34,
                    portName: "ge-1/1/34",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 35,
                    portName: "ge-1/1/35",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 36,
                    portName: "ge-1/1/36",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 37,
                    portName: "ge-1/1/37",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "ge-1/1/38",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 39,
                    portName: "ge-1/1/39",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 40,
                    portName: "ge-1/1/40",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 41,
                    portName: "ge-1/1/41",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 42,
                    portName: "ge-1/1/42",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 43,
                    portName: "ge-1/1/43",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "ge-1/1/44",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 45,
                    portName: "ge-1/1/45",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 46,
                    portName: "ge-1/1/46",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 47,
                    portName: "ge-1/1/47",
                    portType: "ge",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 48,
                    portName: "ge-1/1/48",
                    portType: "ge",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 49,
                    portName: "te-1/1/1",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 50,
                    portName: "te-1/1/2",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 51,
                    portName: "te-1/1/3",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 52,
                    portName: "te-1/1/4",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 53,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 54,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="5G RJ45" portNum={24} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="25G SFP28" portNum={2} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="40G QSFP+" portNum={1} paddingLeft={80} paddingTop={32} />
            </>
        )
    },
    "S5860-48XMG": {
        portData: [
            [
                {
                    label: 1,
                    portName: "te-1/1/1",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "te-1/1/2",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "te-1/1/3",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "te-1/1/4",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "te-1/1/5",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "te-1/1/6",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "te-1/1/7",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "te-1/1/8",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "te-1/1/9",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "te-1/1/10",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "te-1/1/11",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "te-1/1/12",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "te-1/1/13",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "te-1/1/14",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "te-1/1/15",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "te-1/1/16",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "te-1/1/17",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "te-1/1/18",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "te-1/1/19",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "te-1/1/20",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "te-1/1/21",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "te-1/1/22",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "te-1/1/23",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "te-1/1/24",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "te-1/1/25",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "te-1/1/26",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "te-1/1/27",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "te-1/1/28",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "te-1/1/29",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "te-1/1/30",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "te-1/1/31",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "te-1/1/32",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 33,
                    portName: "te-1/1/33",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 34,
                    portName: "te-1/1/34",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 35,
                    portName: "te-1/1/35",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 36,
                    portName: "te-1/1/36",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 37,
                    portName: "te-1/1/37",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "te-1/1/38",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 39,
                    portName: "te-1/1/39",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 40,
                    portName: "te-1/1/40",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 41,
                    portName: "te-1/1/41",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 42,
                    portName: "te-1/1/42",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 43,
                    portName: "te-1/1/43",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "te-1/1/44",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 45,
                    portName: "te-1/1/45",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 46,
                    portName: "te-1/1/46",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 47,
                    portName: "te-1/1/47",
                    portType: "ge",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 48,
                    portName: "te-1/1/48",
                    portType: "ge",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 49,
                    portName: "te-1/1/49",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 50,
                    portName: "te-1/1/50",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 51,
                    portName: "te-1/1/51",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 52,
                    portName: "te-1/1/52",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 53,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 54,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="10G RJ45" portNum={24} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="25G SFP28" portNum={2} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="40G QSFP+" portNum={1} paddingLeft={80} paddingTop={32} />
            </>
        )
    },
    "S5860-48XMG-U": {
        portData: [
            [
                {
                    label: 1,
                    portName: "te-1/1/1",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 2,
                    portName: "te-1/1/2",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 3,
                    portName: "te-1/1/3",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 4,
                    portName: "te-1/1/4",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 5,
                    portName: "te-1/1/5",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 6,
                    portName: "te-1/1/6",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 7,
                    portName: "te-1/1/7",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 8,
                    portName: "te-1/1/8",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 9,
                    portName: "te-1/1/9",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 10,
                    portName: "te-1/1/10",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 11,
                    portName: "te-1/1/11",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 12,
                    portName: "te-1/1/12",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 13,
                    portName: "te-1/1/13",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 14,
                    portName: "te-1/1/14",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 15,
                    portName: "te-1/1/15",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 16,
                    portName: "te-1/1/16",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 17,
                    portName: "te-1/1/17",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 18,
                    portName: "te-1/1/18",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 19,
                    portName: "te-1/1/19",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 20,
                    portName: "te-1/1/20",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 21,
                    portName: "te-1/1/21",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 22,
                    portName: "te-1/1/22",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 23,
                    portName: "te-1/1/23",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 24,
                    portName: "te-1/1/24",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 25,
                    portName: "te-1/1/25",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 26,
                    portName: "te-1/1/26",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 27,
                    portName: "te-1/1/27",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 28,
                    portName: "te-1/1/28",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 29,
                    portName: "te-1/1/29",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 30,
                    portName: "te-1/1/30",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 31,
                    portName: "te-1/1/31",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 32,
                    portName: "te-1/1/32",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 33,
                    portName: "te-1/1/33",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 34,
                    portName: "te-1/1/34",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 35,
                    portName: "te-1/1/35",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 36,
                    portName: "te-1/1/36",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 37,
                    portName: "te-1/1/37",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 38,
                    portName: "te-1/1/38",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 39,
                    portName: "te-1/1/39",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 40,
                    portName: "te-1/1/40",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 41,
                    portName: "te-1/1/41",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 42,
                    portName: "te-1/1/42",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 43,
                    portName: "te-1/1/43",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 44,
                    portName: "te-1/1/44",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 45,
                    portName: "te-1/1/45",
                    portType: "ge",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 46,
                    portName: "te-1/1/46",
                    portType: "ge",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 47,
                    portName: "te-1/1/47",
                    portType: "ge",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 48,
                    portName: "te-1/1/48",
                    portType: "ge",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 49,
                    portName: "te-1/1/49",
                    portType: "te",
                    isGap: false,
                    isReverse: false
                },
                {
                    label: 50,
                    portName: "te-1/1/50",
                    portType: "te",
                    isGap: false,
                    isReverse: true
                }
            ],
            [
                {
                    label: 51,
                    portName: "te-1/1/51",
                    portType: "te",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 52,
                    portName: "te-1/1/52",
                    portType: "te",
                    isGap: true,
                    isReverse: true
                }
            ],
            [
                {
                    label: 53,
                    portName: "xe-1/1/1",
                    portType: "xe",
                    isGap: true,
                    isReverse: false
                },
                {
                    label: 54,
                    portName: "xe-1/1/2",
                    portType: "xe",
                    isGap: true,
                    isReverse: true
                }
            ]
        ],
        portLabelComponent: (
            <>
                <PortTypeLabel portLabel="10G RJ45" portNum={24} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="25G SFP28" portNum={2} paddingLeft={50} paddingTop={32} />
                <PortTypeLabel portLabel="40G QSFP+" portNum={1} paddingLeft={80} paddingTop={32} />
            </>
        )
    }
};

export default SwitchPortPhysicData;
