import {<PERSON><PERSON>, <PERSON>, Flex, Radio} from "antd";
import {forwardRef, useImperativeHandle, useRef, useState} from "react";

const CoreTabSelectedPortCard = forwardRef(
    (
        {
            coreTabUpdateCorePortStatusCallback,
            coreTabUpdateAccessPortStatusCallback,
            coreTabRemoveCorePortStatusCallback,
            coreTabRemoveAccessPortStatusCallback,
            coreTabUpdateWANPortStatusCallback,
            coreTabRemoveWANPortStatusCallBack,
            isMenuCollapsed
        },
        ref
    ) => {
        const selectedPortCardRef = useRef(null);

        const [isShowPortCard, setIsShowPortCard] = useState(false);
        const [displayPos, setDisplayPos] = useState([0, 0]);
        const [displayPortName, setDisplayPortName] = useState("");
        const [selectedPortType, setSelectedPortType] = useState("ge");
        const [selectedSN, setSelectedSN] = useState("");
        const [isPortSelectedWithCore, setIsPortSelectedWithCore] = useState(false);
        const [isPortSelectedWithAccess, setIsPortSelectedWithAccess] = useState(false);
        const [isPortSelectedWithWAN, setIsPortSelectedWithWAN] = useState(false);

        useImperativeHandle(ref, () => ({
            showSelectedPortCard: (record, portName, portPosX, portPosY) => {
                setSelectedSN(record.sn);
                setDisplayPortName(portName);
                setDisplayPos([portPosX, portPosY]);
                setIsShowPortCard(true);
                updateCurrentPortStatus(record, portName);
            },
            hideSelectedPortCard: () => {
                setIsShowPortCard(false);
            },
            isSelectedPortCardShown: () => {
                return isShowPortCard;
            },
            isMouseClickOnPortCard: e => {
                const selectedPortCardRect = selectedPortCardRef.current.getBoundingClientRect();
                return (
                    selectedPortCardRect.x <= e.clientX &&
                    e.clientX <= selectedPortCardRect.x + selectedPortCardRect.width &&
                    selectedPortCardRect.y <= e.clientY &&
                    e.clientY <= selectedPortCardRect.y + selectedPortCardRect.height
                );
            }
        }));

        const updateCurrentPortStatus = (record, portName) => {
            if (Object.keys(record.currentLinkToAccess).includes(portName)) {
                setIsPortSelectedWithAccess(true);
                setIsPortSelectedWithCore(false);
                setIsPortSelectedWithWAN(false);
                setSelectedPortType(record.currentLinkToAccess[portName].port_type);
            } else if (Object.keys(record.currentLinkToCore).includes(portName)) {
                setIsPortSelectedWithAccess(false);
                setIsPortSelectedWithCore(true);
                setIsPortSelectedWithWAN(false);
                setSelectedPortType(record.currentLinkToCore[portName].port_type);
            } else if (Object.keys(record.currentLinkToWAN).includes(portName)) {
                setIsPortSelectedWithCore(false);
                setIsPortSelectedWithAccess(false);
                setIsPortSelectedWithWAN(true);
                setSelectedPortType(record.currentLinkToWAN[portName].port_type);
            } else {
                setIsPortSelectedWithAccess(false);
                setIsPortSelectedWithCore(false);
                setIsPortSelectedWithWAN(false);
                const portTypeName = portName.substring(0, 2).toLowerCase();
                setSelectedPortType(["ge", "te", "xe"].includes(portTypeName) ? portTypeName : "ge");
            }
        };

        const calculateCardOffsetTop = () => {
            return displayPos[1] + 90 + 270 > window.innerHeight ? displayPos[1] - 90 - 310 : displayPos[1] - 90;
        };

        const calculateCardOffsetLeft = () => {
            const offsetLeft = isMenuCollapsed ? displayPos[0] - 110 : displayPos[0] - 290;
            if (offsetLeft + 600 > window.innerWidth) {
                return offsetLeft - 290 + 5;
            }
            return offsetLeft;
        };

        const getCoreLinkConnectionComponent = () => {
            if (isPortSelectedWithCore) {
                return (
                    <Button
                        id="removeLinkToCore"
                        onClick={() => {
                            coreTabRemoveCorePortStatusCallback(selectedSN, displayPortName);
                        }}
                    >
                        Remove Link to Core
                    </Button>
                );
            }
            if (isPortSelectedWithAccess || isPortSelectedWithWAN) {
                return null;
            }
            return (
                <Button
                    id="linkToCore"
                    onClick={() => {
                        coreTabUpdateCorePortStatusCallback(selectedSN, displayPortName, selectedPortType);
                    }}
                >
                    Link to Core
                </Button>
            );
        };

        const getAccessLinkConnectionComponent = () => {
            if (isPortSelectedWithAccess) {
                return (
                    <Button
                        id="removeLinkToAccess"
                        onClick={() => {
                            coreTabRemoveAccessPortStatusCallback(selectedSN, displayPortName);
                        }}
                    >
                        Remove Link to Access
                    </Button>
                );
            }
            if (isPortSelectedWithCore || isPortSelectedWithWAN) {
                return null;
            }
            return (
                <Button
                    id="linkToAccess"
                    onClick={() => {
                        coreTabUpdateAccessPortStatusCallback(selectedSN, displayPortName, selectedPortType);
                    }}
                >
                    Link to Access
                </Button>
            );
        };

        const getAccessWANConnectionComponent = () => {
            if (isPortSelectedWithWAN) {
                return (
                    <Button
                        id="removeLinkToWAN"
                        onClick={() => {
                            coreTabRemoveWANPortStatusCallBack(selectedSN, displayPortName);
                        }}
                    >
                        Remove Link to WAN
                    </Button>
                );
            }
            if (isPortSelectedWithCore || isPortSelectedWithAccess) {
                return null;
            }
            return (
                <Button
                    id="linkToWAN"
                    onClick={() => {
                        coreTabUpdateWANPortStatusCallback(selectedSN, displayPortName, selectedPortType);
                    }}
                >
                    Link to WAN
                </Button>
            );
        };

        return isShowPortCard ? (
            <div
                style={{
                    position: "absolute",
                    top: `${calculateCardOffsetTop()}px`,
                    left: `${calculateCardOffsetLeft()}px`,
                    transition: "background-color 0.3s ease",
                    width: "275px",
                    zIndex: 1000
                }}
            >
                <Card ref={selectedPortCardRef} title={displayPortName} bordered={false}>
                    <Flex vertical>
                        <Flex style={{fontSize: "14px", paddingBottom: "12px"}}>Port Type</Flex>
                        <Flex style={{paddingBottom: "12px"}}>
                            <Radio.Group
                                onChange={e => {
                                    setSelectedPortType(e.target.value);
                                }}
                                value={selectedPortType}
                                // disabled={isPortSelectedWithCore || isPortSelectedWithAccess || isPortSelectedWithWAN}
                                disabled
                                options={[
                                    {value: "ge", label: "ge"},
                                    {value: "te", label: "te"},
                                    {value: "xe", label: "xe"}
                                ]}
                            />
                        </Flex>
                        <Flex style={{fontSize: "14px", paddingBottom: "12px"}}>Port Connection</Flex>
                        <Flex style={{paddingBottom: "12px"}}>{getAccessWANConnectionComponent()}</Flex>
                        <Flex style={{paddingBottom: "12px"}}>{getCoreLinkConnectionComponent()}</Flex>
                        <Flex style={{paddingBottom: "12px"}}>{getAccessLinkConnectionComponent()}</Flex>
                    </Flex>
                </Card>
            </div>
        ) : null;
    }
);

export default CoreTabSelectedPortCard;
