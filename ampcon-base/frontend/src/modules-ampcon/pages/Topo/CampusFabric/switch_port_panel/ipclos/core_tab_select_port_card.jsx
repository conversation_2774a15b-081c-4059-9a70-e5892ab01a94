import {<PERSON><PERSON>, <PERSON>, Flex, Radio, Select} from "antd";
import {forwardRef, useImperativeHandle, useRef, useState} from "react";

const CoreTabSelectedPortCard = forwardRef(
    (
        {
            coreTabUpdateAccessPortStatusCallback,
            coreTabRemoveAccessPortStatusCallback,
            coreTabUpdateBorderPortStatusCallback,
            coreTabRemoveBorderPortStatusCallback,
            coreTabUpdateWANPortStatusCallback,
            coreTabRemoveWANPortStatusCallback,
            coreTabUpdateDisPortStatusCallback,
            coreTabRemoveDisPortStatusCallback,
            coreTabUpdateCorePortStatusCallback,
            coreTabRemoveCorePortStatusCallback,
            isUseCoreAsBorder,
            isDisNodeExists,
            isMenuCollapsed,
            isMultihoming
        },
        ref
    ) => {
        const selectedPortCardRef = useRef(null);

        const [isShowPortCard, setIsShowPortCard] = useState(false);
        const [displayPos, setDisplayPos] = useState([0, 0]);
        const [displayPortName, setDisplayPortName] = useState("");
        const [selectedPortType, setSelectedPortType] = useState("ge");
        const [selectedSN, setSelectedSN] = useState("");

        // 端口被选中
        const [isPortSelectedWithAccess, setIsPortSelectedWithAccess] = useState(false);
        const [isPortSelectedWithBorder, setIsPortSelectedWithBorder] = useState(false);
        const [isPortSelectedWithDis, setIsPortSelectedWithDis] = useState(false);
        const [isPortSelectedWithWAN, setIsPortSelectedWithWAN] = useState(false);
        const [isPortSelectedWithCore, setIsPortSelectedWithCore] = useState(false);

        const [showAccessLinkOptions, setShowAccessLinkOptions] = useState(false);
        const [showBorderLinkOptions, setShowBorderLinkOptions] = useState(false);
        const [showDisLinkOptions, setShowDisLinkOptions] = useState(false);
        const [showWANLinkOptions, setShowWANLinkOptions] = useState(false);

        useImperativeHandle(ref, () => ({
            showSelectedPortCard: (record, portName, portPosX, portPosY) => {
                setSelectedSN(record.sn);
                setDisplayPortName(portName);
                setDisplayPos([portPosX, portPosY]);

                setIsShowPortCard(true);
                updateCurrentPortStatus(record, portName);
                showDiffSwitchLinkOptions();
            },
            hideSelectedPortCard: () => {
                setIsShowPortCard(false);
            },
            isSelectedPortCardShown: () => {
                return isShowPortCard;
            },
            isMouseClickOnPortCard: e => {
                const selectedPortCardRect = selectedPortCardRef.current.getBoundingClientRect();
                return (
                    selectedPortCardRect.x <= e.clientX &&
                    e.clientX <= selectedPortCardRect.x + selectedPortCardRect.width &&
                    selectedPortCardRect.y <= e.clientY &&
                    e.clientY <= selectedPortCardRect.y + selectedPortCardRect.height
                );
            }
        }));

        const updateCurrentPortStatus = (record, portName) => {
            if (record.currentLinkToAccess && Object.keys(record.currentLinkToAccess).includes(portName)) {
                setIsPortSelectedWithAccess(true);
                setIsPortSelectedWithBorder(false);
                setIsPortSelectedWithDis(false);
                setIsPortSelectedWithWAN(false);
                setIsPortSelectedWithCore(false);
                setSelectedPortType(record.currentLinkToAccess[portName].port_type);
            } else if (record.currentLinkToBorder && Object.keys(record.currentLinkToBorder).includes(portName)) {
                setIsPortSelectedWithAccess(false);
                setIsPortSelectedWithBorder(true);
                setIsPortSelectedWithDis(false);
                setIsPortSelectedWithWAN(false);
                setIsPortSelectedWithCore(false);
                setSelectedPortType(record.currentLinkToBorder[portName].port_type);
            } else if (record.currentLinkToDis && Object.keys(record.currentLinkToDis).includes(portName)) {
                setIsPortSelectedWithAccess(false);
                setIsPortSelectedWithBorder(false);
                setIsPortSelectedWithDis(true);
                setIsPortSelectedWithWAN(false);
                setIsPortSelectedWithCore(false);
                setSelectedPortType(record.currentLinkToDis[portName].port_type);
            } else if (record.currentLinkToWAN && Object.keys(record.currentLinkToWAN).includes(portName)) {
                setIsPortSelectedWithAccess(false);
                setIsPortSelectedWithBorder(false);
                setIsPortSelectedWithDis(false);
                setIsPortSelectedWithWAN(true);
                setIsPortSelectedWithCore(false);
                setSelectedPortType(record.currentLinkToWAN[portName].port_type);
            } else if (record.currentLinkToCore && Object.keys(record.currentLinkToCore).includes(portName)) {
                setIsPortSelectedWithAccess(false);
                setIsPortSelectedWithBorder(false);
                setIsPortSelectedWithDis(false);
                setIsPortSelectedWithWAN(false);
                setIsPortSelectedWithCore(true);
                setSelectedPortType(record.currentLinkToCore[portName].port_type);
            } else {
                setIsPortSelectedWithAccess(false);
                setIsPortSelectedWithBorder(false);
                setIsPortSelectedWithDis(false);
                setIsPortSelectedWithWAN(false);
                setIsPortSelectedWithCore(false);
                const portTypeName = portName.substring(0, 2).toLowerCase();
                setSelectedPortType(["ge", "te", "xe"].includes(portTypeName) ? portTypeName : "ge");
            }
        };

        const showDiffSwitchLinkOptions = () => {
            if (!isUseCoreAsBorder && isDisNodeExists) {
                setShowBorderLinkOptions(true);
                setShowDisLinkOptions(true);
                setShowAccessLinkOptions(false);
                setShowWANLinkOptions(false);
            }

            if (!isUseCoreAsBorder && !isDisNodeExists) {
                setShowBorderLinkOptions(true);
                setShowDisLinkOptions(false);
                setShowAccessLinkOptions(true);
                setShowWANLinkOptions(false);
            }

            if (isUseCoreAsBorder && isDisNodeExists) {
                setShowBorderLinkOptions(false);
                setShowDisLinkOptions(true);
                setShowAccessLinkOptions(false);
                setShowWANLinkOptions(true);
            }

            if (isUseCoreAsBorder && !isDisNodeExists) {
                setShowBorderLinkOptions(false);
                setShowDisLinkOptions(false);
                setShowAccessLinkOptions(true);
                setShowWANLinkOptions(true);
            }
        };

        const calculateCardOffsetTop = () => {
            return displayPos[1] + 90 + 270 > window.innerHeight ? displayPos[1] - 90 - 310 : displayPos[1] - 90;
        };

        const calculateCardOffsetLeft = () => {
            const offsetLeft = isMenuCollapsed ? displayPos[0] - 110 : displayPos[0] - 290;
            if (offsetLeft + 600 > window.innerWidth) {
                return offsetLeft - 290 + 5;
            }
            return offsetLeft;
        };

        // border不存在的情况下，存在wan连接
        const getWANLinkConnectionComponent = () => {
            if (isUseCoreAsBorder) {
                if (isPortSelectedWithWAN) {
                    return (
                        <Button
                            id="removeLinkToWAN"
                            onClick={() => {
                                coreTabRemoveWANPortStatusCallback(selectedSN, displayPortName);
                            }}
                        >
                            Remove Link to WAN
                        </Button>
                    );
                }
                if (
                    isPortSelectedWithAccess ||
                    isPortSelectedWithDis ||
                    isPortSelectedWithBorder ||
                    isPortSelectedWithCore
                ) {
                    return null;
                }
                return (
                    <Button
                        id="linkToWAN"
                        onClick={() => {
                            coreTabUpdateWANPortStatusCallback(selectedSN, displayPortName, selectedPortType);
                        }}
                    >
                        Link to WAN
                    </Button>
                );
            }
            return null;
        };

        // border存在的情况下，core与border连接
        const getBorderLinkConnectionComponent = () => {
            if (!isUseCoreAsBorder) {
                if (isPortSelectedWithBorder) {
                    return (
                        <Button
                            id="removeLinkToBorder"
                            onClick={() => {
                                coreTabRemoveBorderPortStatusCallback(selectedSN, displayPortName);
                            }}
                        >
                            Remove Link to Border
                        </Button>
                    );
                }
                if (
                    isPortSelectedWithAccess ||
                    isPortSelectedWithDis ||
                    isPortSelectedWithWAN ||
                    isPortSelectedWithCore
                ) {
                    return null;
                }
                return (
                    <Button
                        id="linkToBorder"
                        onClick={() => {
                            coreTabUpdateBorderPortStatusCallback(selectedSN, displayPortName, selectedPortType);
                        }}
                    >
                        Link to Border
                    </Button>
                );
            }
            return null;
        };

        // dis存在的情况下，core与dis连接
        const getDisLinkConnectionComponent = () => {
            if (coreTabUpdateDisPortStatusCallback) {
                if (isPortSelectedWithDis) {
                    return (
                        <Button
                            id="removeLinkToDis"
                            onClick={() => {
                                coreTabRemoveDisPortStatusCallback(selectedSN, displayPortName);
                            }}
                        >
                            Remove Link to Distribution
                        </Button>
                    );
                }
                if (
                    isPortSelectedWithBorder ||
                    isPortSelectedWithAccess ||
                    isPortSelectedWithWAN ||
                    isPortSelectedWithCore
                ) {
                    return null;
                }
                return (
                    <Button
                        id="linkToDis"
                        onClick={() => {
                            coreTabUpdateDisPortStatusCallback(selectedSN, displayPortName, selectedPortType);
                        }}
                    >
                        Link to Distribution
                    </Button>
                );
            }
            return null;
        };

        // dis不存在的情况下，core与access连接
        const getAccessLinkConnectionComponent = () => {
            if (coreTabUpdateAccessPortStatusCallback) {
                if (isPortSelectedWithAccess) {
                    return (
                        <Button
                            id="removeLinkToAccess"
                            onClick={() => {
                                coreTabRemoveAccessPortStatusCallback(selectedSN, displayPortName);
                            }}
                        >
                            Remove Link to Access
                        </Button>
                    );
                }
                if (
                    isPortSelectedWithBorder ||
                    isPortSelectedWithDis ||
                    isPortSelectedWithWAN ||
                    isPortSelectedWithCore
                ) {
                    return null;
                }
                return (
                    <Button
                        id="linkToAccess"
                        onClick={() => {
                            coreTabUpdateAccessPortStatusCallback(selectedSN, displayPortName, selectedPortType);
                        }}
                    >
                        Link to Access
                    </Button>
                );
            }
            return null;
        };

        const getCoreLinkConnectionComponent = () => {
            if (isPortSelectedWithCore) {
                return (
                    <Button
                        id="removeLinkToCore"
                        onClick={() => {
                            coreTabRemoveCorePortStatusCallback(selectedSN, displayPortName);
                        }}
                    >
                        Remove Link to Core
                    </Button>
                );
            }
            if (
                isPortSelectedWithAccess ||
                isPortSelectedWithWAN ||
                isPortSelectedWithDis ||
                isPortSelectedWithBorder ||
                isMultihoming
            ) {
                return null;
            }
            return (
                <Button
                    id="linkToCore"
                    onClick={() => {
                        coreTabUpdateCorePortStatusCallback(selectedSN, displayPortName, selectedPortType);
                    }}
                >
                    Link to Core
                </Button>
            );
        };

        return isShowPortCard ? (
            <div
                style={{
                    position: "absolute",
                    top: `${calculateCardOffsetTop()}px`,
                    left: `${calculateCardOffsetLeft()}px`,
                    transition: "background-color 0.3s ease",
                    width: "275px",
                    zIndex: 1000
                }}
            >
                <Card ref={selectedPortCardRef} title={displayPortName} bordered={false}>
                    <Flex vertical>
                        <Flex style={{fontSize: "14px", paddingBottom: "12px"}}>Port Type</Flex>
                        <Flex style={{paddingBottom: "12px"}}>
                            <Radio.Group
                                onChange={e => {
                                    setSelectedPortType(e.target.value);
                                }}
                                value={selectedPortType}
                                // disabled={
                                //     isPortSelectedWithBorder ||
                                //     isPortSelectedWithAccess ||
                                //     isPortSelectedWithDis ||
                                //     isPortSelectedWithWAN
                                // }
                                disabled
                                options={[
                                    {value: "ge", label: "ge"},
                                    {value: "te", label: "te"},
                                    {value: "xe", label: "xe"}
                                ]}
                            />
                        </Flex>

                        <>
                            <Flex style={{fontSize: "14px", paddingBottom: "12px"}}>Port Connection</Flex>
                            {/* 如果 WAN 连接存在，显示 WAN */}
                            {showWANLinkOptions ? (
                                <Flex style={{paddingBottom: "12px"}}>{getWANLinkConnectionComponent()}</Flex>
                            ) : null}
                            {/* 如果 Border 连接存在，显示 Border */}
                            {showBorderLinkOptions ? (
                                <Flex style={{paddingBottom: "12px"}}>{getBorderLinkConnectionComponent()}</Flex>
                            ) : null}
                            {/* Core 连接一定存在，显示 Core */}
                            <Flex style={{paddingBottom: "12px"}}>{getCoreLinkConnectionComponent()}</Flex>
                            {/* 如果 Dis 连接存在，显示 Dis */}
                            {showDisLinkOptions ? (
                                <Flex style={{paddingBottom: "12px"}}>{getDisLinkConnectionComponent()}</Flex>
                            ) : null}
                            {/* 如果 Access 连接存在，显示 Access */}
                            {showAccessLinkOptions ? (
                                <Flex style={{paddingBottom: "12px"}}>{getAccessLinkConnectionComponent()}</Flex>
                            ) : null}
                        </>
                    </Flex>
                </Card>
            </div>
        ) : null;
    }
);

export default CoreTabSelectedPortCard;
