import {<PERSON><PERSON>, <PERSON>, Flex, Radio} from "antd";
import {forwardRef, useImperativeHandle, useRef, useState} from "react";

const PodDisTabSelectedPortCard = forwardRef(
    (
        {
            podDisTabUpdateCorePortStatusCallback,
            podDisTabRemoveCorePortStatusCallback,
            podDisTabUpdateAccessPortStatusCallback,
            podDisTabRemoveAccessPortStatusCallback,
            isMenuCollapsed
        },
        ref
    ) => {
        const selectedPortCardRef = useRef(null);

        const [isShowPortCard, setIsShowPortCard] = useState(false);
        const [displayPos, setDisplayPos] = useState([0, 0]);
        const [displayPortName, setDisplayPortName] = useState("");
        const [selectedPortType, setSelectedPortType] = useState("ge");
        const [selectedSN, setSelectedSN] = useState("");
        const [isPortSelectedWithAccess, setIsPortSelectedWithAccess] = useState(false); // dis连接access
        const [isPortSelectedWithCore, setIsPortSelectedWithCore] = useState(false); // dis连接core

        useImperativeHandle(ref, () => ({
            showSelectedPortCard: (record, portName, portPosX, portPosY) => {
                setSelectedSN(record.sn);
                setDisplayPortName(portName);
                setDisplayPos([portPosX, portPosY]);

                setIsShowPortCard(true);
                updateCurrentPortStatus(record, portName);
            },
            hideSelectedPortCard: () => {
                setIsShowPortCard(false);
            },
            isSelectedPortCardShown: () => {
                return isShowPortCard;
            },
            isMouseClickOnPortCard: e => {
                const selectedPortCardRect = selectedPortCardRef.current.getBoundingClientRect();
                return (
                    selectedPortCardRect.x <= e.clientX &&
                    e.clientX <= selectedPortCardRect.x + selectedPortCardRect.width &&
                    selectedPortCardRect.y <= e.clientY &&
                    e.clientY <= selectedPortCardRect.y + selectedPortCardRect.height
                );
            }
        }));

        const updateCurrentPortStatus = (record, portName) => {
            if (Object.keys(record.currentLinkToAccess).includes(portName)) {
                setIsPortSelectedWithAccess(true);
                setIsPortSelectedWithCore(false);
                setSelectedPortType(record.currentLinkToAccess[portName].port_type);
            } else if (Object.keys(record.currentLinkToCore).includes(portName)) {
                setIsPortSelectedWithAccess(false);
                setIsPortSelectedWithCore(true);
                setSelectedPortType(record.currentLinkToCore[portName].port_type);
            } else {
                setIsPortSelectedWithAccess(false);
                setIsPortSelectedWithCore(false);
                const portTypeName = portName.substring(0, 2).toLowerCase();
                setSelectedPortType(["ge", "te", "xe"].includes(portTypeName) ? portTypeName : "ge");
            }
        };

        const calculateCardOffsetTop = () => {
            return displayPos[1] + 90 + 270 > window.innerHeight ? displayPos[1] - 90 - 310 : displayPos[1] - 90;
        };

        const calculateCardOffsetLeft = () => {
            const offsetLeft = isMenuCollapsed ? displayPos[0] - 110 : displayPos[0] - 290;
            if (offsetLeft + 600 > window.innerWidth) {
                return offsetLeft - 290 + 5;
            }
            return offsetLeft;
        };

        const getCoreLinkConnectionComponent = () => {
            if (isPortSelectedWithCore) {
                return (
                    <Button
                        id="removeLinkToCore"
                        onClick={() => {
                            podDisTabRemoveCorePortStatusCallback(selectedSN, displayPortName);
                        }}
                    >
                        Remove Link to Core
                    </Button>
                );
            }
            if (isPortSelectedWithAccess) {
                return null;
            }
            return (
                <Button
                    id="linkToCore"
                    onClick={() => {
                        podDisTabUpdateCorePortStatusCallback(selectedSN, displayPortName, selectedPortType);
                    }}
                >
                    Link to Core
                </Button>
            );
        };

        const getAccessLinkConnectionComponent = () => {
            if (isPortSelectedWithAccess) {
                return (
                    <Button
                        id="removeLinkToAccess"
                        onClick={() => {
                            podDisTabRemoveAccessPortStatusCallback(selectedSN, displayPortName);
                        }}
                    >
                        Remove Link to Access
                    </Button>
                );
            }
            if (isPortSelectedWithCore) {
                return null;
            }
            return (
                <Button
                    id="linkToAccess"
                    onClick={() => {
                        podDisTabUpdateAccessPortStatusCallback(selectedSN, displayPortName, selectedPortType);
                    }}
                >
                    Link to Access
                </Button>
            );
        };

        return isShowPortCard ? (
            <div
                style={{
                    position: "absolute",
                    top: `${calculateCardOffsetTop()}px`,
                    left: `${calculateCardOffsetLeft()}px`,
                    transition: "background-color 0.3s ease",
                    width: "275px",
                    zIndex: 1000
                }}
            >
                <Card ref={selectedPortCardRef} title={displayPortName} bordered={false}>
                    <Flex vertical>
                        <Flex style={{fontSize: "14px", paddingBottom: "12px"}}>Port Type</Flex>
                        <Flex style={{paddingBottom: "12px"}}>
                            <Radio.Group
                                onChange={e => {
                                    setSelectedPortType(e.target.value);
                                }}
                                value={selectedPortType}
                                // disabled={isPortSelectedWithCore || isPortSelectedWithAccess}
                                disabled
                                options={[
                                    {value: "ge", label: "ge"},
                                    {value: "te", label: "te"},
                                    {value: "xe", label: "xe"}
                                ]}
                            />
                        </Flex>
                        <div>
                            <Flex style={{fontSize: "14px", paddingBottom: "12px"}}>Port Connection</Flex>
                            <Flex style={{paddingBottom: "12px"}}>{getCoreLinkConnectionComponent()}</Flex>
                            <Flex style={{paddingBottom: "12px"}}>{getAccessLinkConnectionComponent()}</Flex>
                        </div>
                    </Flex>
                </Card>
            </div>
        ) : null;
    }
);

export default PodDisTabSelectedPortCard;
