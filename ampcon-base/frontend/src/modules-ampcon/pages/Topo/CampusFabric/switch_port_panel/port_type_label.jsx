import Icon from "@ant-design/icons";
import {useRef, useState, useEffect} from "react";

const Port2TypeLabelSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="73" height="6" viewBox="0 0 73 6">
            <g>
                <g />
                <g>
                    <path
                        d="M5.20711,1.5L10,1.5L10,0.5L4.79289,0.5L0.646447,4.64645L1.353553,5.35355L5.20711,1.5Z"
                        fillRule="evenodd"
                        fill="#C1CBD8"
                        fillOpacity="1"
                    />
                </g>
                <g transform="matrix(-1,0,0,1,144,0)">
                    <path
                        d="M81,1.5L134,1.5L134,0.5L75.79289,0.5L71.646447,4.64645L72.353553,5.35355L76.20711,1.5L81,1.5Z"
                        fillRule="evenodd"
                        fill="#C1CBD8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};

const Port4TypeLabelSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="128" height="6" viewBox="0 0 128 6">
            <g>
                <g />
                <g>
                    <path
                        d="M25,1.5L103.5,1.5L103.5,0.5L4.79289,0.5L0.646447,4.64645L1.353553,5.35355L5.20711,1.5L25,1.5Z"
                        fillRule="evenodd"
                        fill="#C1CBD8"
                        fillOpacity="1"
                    />
                </g>
                <g transform="matrix(-1,0,0,1,254,0)">
                    <path
                        d="M131.20711,1.5L151,1.5L151,0.5L130.79289,0.5L126.646447,4.64645L127.353553,5.35355L131.20711,1.5Z"
                        fillRule="evenodd"
                        fill="#C1CBD8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};

const Port6TypeLabelSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="198" height="6" viewBox="0 0 198 6">
            <g>
                <g />
                <g>
                    <path
                        d="M70,1.5L128.5,1.5L128.5,0.5L4.79289,0.5L0.646447,4.64645L1.353553,5.35355L5.20711,1.5L70,1.5Z"
                        fillRule="evenodd"
                        fill="#C1CBD8"
                        fillOpacity="1"
                    />
                </g>
                <g transform="matrix(-1,0,0,1,394,0)">
                    <path
                        d="M201.20711,1.5L266,1.5L266,0.5L200.79289,0.5L196.646447,4.64645L197.353553,5.35355L201.20711,1.5Z"
                        fillRule="evenodd"
                        fill="#C1CBD8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};

const Port8TypeLabelSvg = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            version="1.1"
            width="270.7109375"
            height="4.853515625"
            viewBox="0 0 270.7109375 4.853515625"
        >
            <g>
                <g>
                    <path
                        d="M78.35546875,1L192.35546875,1L192.35546875,0L4.14835875,0L0.0019157499999999938,4.14645L0.70902175,4.85355L4.56257875,1L78.35546875,1Z"
                        fillRule="evenodd"
                        fill="#C1CBD8"
                        fillOpacity="1"
                    />
                </g>
                <g transform="matrix(-1,0,0,1,540.7109375,0)">
                    <path
                        d="M274.56257875,1L348.35546875,1L348.35546875,0L274.14835875,0L270.00191575,4.14645L270.70902175,4.85355L274.56257875,1Z"
                        fillRule="evenodd"
                        fill="#C1CBD8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};

const Port10TypeLabelSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="344" height="6" viewBox="0 0 344 6">
            <g>
                <g />
                <g>
                    <path
                        d="M138,1.5L208,1.5L208,0.5L4.79289,0.5L0.646447,4.64645L1.353553,5.35355L5.20711,1.5L138,1.5Z"
                        fillRule="evenodd"
                        fill="#C1CBD8"
                        fillOpacity="1"
                    />
                </g>
                <g transform="matrix(-1,0,0,1,686,0)">
                    <path
                        d="M347.20711,1.5L480,1.5L480,0.5L346.79289,0.5L342.646447,4.64645L343.353553,5.35355L347.20711,1.5Z"
                        fillRule="evenodd"
                        fill="#C1CBD8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};

const Port12TypeLabelSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="416" height="6" viewBox="0 0 416 6">
            <g>
                <g />
                <g>
                    <path
                        d="M5.20711,1.5L168,1.5L168,0.5L4.79289,0.5L0.646447,4.64645L1.353553,5.35355L5.20711,1.5Z"
                        fillRule="evenodd"
                        fill="#C1CBD8"
                        fillOpacity="1"
                    />
                </g>
                <g transform="matrix(-1,0,0,1,830,0)">
                    <path
                        d="M419.20711,1.5L582,1.5L582,0.5L418.79289,0.5L414.646447,4.64645L415.353553,5.35355L419.20711,1.5Z"
                        fillRule="evenodd"
                        fill="#C1CBD8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};

const Port16TypeLabelSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="560" height="6" viewBox="0 0 560 6">
            <g>
                <g />
                <g>
                    <g>
                        <path
                            d="M5.20711,1.5L238,1.5L238,0.5L4.79289,0.5L0.646447,4.64645L1.353553,5.35355L5.20711,1.5Z"
                            fillRule="evenodd"
                            fill="#C1CBD8"
                            fillOpacity="1"
                        />
                    </g>
                    <g transform="matrix(-1,0,0,1,1118,0)">
                        <path
                            d="M796,1.5L880,1.5L880,0.5L562.7928899999999,0.5L558.646447,4.64645L559.353553,5.35355L563.2071100000001,1.5L796,1.5Z"
                            fillRule="evenodd"
                            fill="#C1CBD8"
                            fillOpacity="1"
                        />
                    </g>
                </g>
            </g>
        </svg>
    );
};

const Port18TypeLabelSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="632" height="6" viewBox="0 0 632 6">
            <g>
                <g />
                <g>
                    <path
                        d="M284,1.5L350,1.5L350,0.5L4.79289,0.5L0.646447,4.64645L1.353553,5.35355L5.20711,1.5L284,1.5Z"
                        fillRule="evenodd"
                        fill="#C1CBD8"
                        fillOpacity="1"
                    />
                </g>
                <g transform="matrix(-1,0,0,1,1262,0)">
                    <path
                        d="M635.2071100000001,1.5L912,1.5L912,0.5L634.7928899999999,0.5L630.646447,4.64645L631.353553,5.35355L635.2071100000001,1.5Z"
                        fillRule="evenodd"
                        fill="#C1CBD8"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};

const Port24TypeLabelSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="847" height="6" viewBox="0 0 847 6">
            <g>
                <g />
                <g>
                    <g>
                        <path
                            d="M5.20711,1.5L388,1.5L388,0.5L4.79289,0.5L0.646447,4.64645L1.353553,5.35355L5.20711,1.5Z"
                            fillRule="evenodd"
                            fill="#C1CBD8"
                            fillOpacity="1"
                        />
                    </g>
                    <g transform="matrix(-1,0,0,1,1692,0)">
                        <path
                            d="M1233,1.5L1304,1.5L1304,0.5L849.7928899999999,0.5L845.646447,4.64645L846.353553,5.35355L850.2071100000001,1.5L1233,1.5Z"
                            fillRule="evenodd"
                            fill="#C1CBD8"
                            fillOpacity="1"
                        />
                    </g>
                </g>
            </g>
        </svg>
    );
};

const PortTypeLabel = ({portLabel, portNum, paddingLeft, paddingTop}) => {
    const textRef = useRef(null);
    const [textWidth, setTextWidth] = useState(0);

    useEffect(() => {
        if (textRef.current) {
            const width = textRef.current.offsetWidth;
            setTextWidth(width);
        }
    }, [portLabel]);

    const portLabelSvgMap = {
        2: Port2TypeLabelSvg,
        4: Port4TypeLabelSvg,
        6: Port6TypeLabelSvg,
        8: Port8TypeLabelSvg,
        10: Port10TypeLabelSvg,
        12: Port12TypeLabelSvg,
        16: Port16TypeLabelSvg,
        18: Port18TypeLabelSvg,
        24: Port24TypeLabelSvg
    };

    const getPortLabelSvg = () => {
        if (portNum <= 2) {
            return null;
        }
        return portLabelSvgMap[portNum];
    };

    // SVG宽度映射表
    const svgWidthMap = {
        4: 128,
        6: 198,
        8: 270.71,
        10: 344,
        12: 416,
        16: 560,
        18: 632,
        24: 847
    };

    const calculatePaddingLeft = paddingLeft + Math.max(((svgWidthMap[portNum] || 0) - textWidth) / 2, 0);

    return (
        <div>
            {/* 隐藏的测量文本 */}
            <span
                ref={textRef}
                style={{
                    position: "absolute",
                    visibility: "hidden",
                    whiteSpace: "nowrap",
                    fontSize: "10px"
                }}
            >
                {portLabel}
            </span>

            {/* 实际显示内容 */}
            <div
                style={{
                    position: "absolute",
                    paddingLeft: `${calculatePaddingLeft}px`,
                    paddingTop: `${paddingTop - 7}px`
                }}
            >
                <div style={{fontSize: "10px", backgroundColor: "#F7F7F7"}}>&nbsp;&nbsp;{portLabel}&nbsp;&nbsp;</div>
            </div>
            <Icon
                component={getPortLabelSvg()}
                style={{paddingLeft: `${paddingLeft + 3}px`, paddingTop: `${paddingTop}px`}}
            />
        </div>
    );
};

export default PortTypeLabel;
