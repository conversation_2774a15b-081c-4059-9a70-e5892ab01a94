import {But<PERSON>, <PERSON>, Flex, message, Radio, Select} from "antd";
import {forwardRef, useImperativeHandle, useRef, useState} from "react";

const AccessTabSelectedPortCard = forwardRef(
    (
        {
            accessTabUpdateCorePortStatusCallback,
            accessTabRemoveCorePortStatusCallback,
            getCoreSwitchSNOptions,
            isMenuCollapsed
        },
        ref
    ) => {
        const selectedPortCardRef = useRef(null);

        const [isShowPortCard, setIsShowPortCard] = useState(false);
        const [displayPos, setDisplayPos] = useState([0, 0]);
        const [displayPortName, setDisplayPortName] = useState("");
        const [selectedPortType, setSelectedPortType] = useState("ge");
        const [selectedSN, setSelectedSN] = useState("");
        const [selectedCoreSwitchSN, setSelectedCoreSwitchSN] = useState("");
        const [isPortSelectedWithCore, setIsPortSelectedWithCore] = useState(false);
        const [isSelectedCoreValid, setIsSelectedCoreValid] = useState(true);
        const [currentCoreSwitchSNOptions, setCurrentCoreSwitchSNOptions] = useState([]);

        useImperativeHandle(ref, () => ({
            showSelectedPortCard: (record, portName, portPosX, portPosY) => {
                setSelectedSN(record.sn);
                setDisplayPortName(portName);
                setDisplayPos([portPosX, portPosY]);
                setCurrentCoreSwitchSNOptions(getCoreSwitchSNOptions(record.sn));
                setIsSelectedCoreValid(true);
                setIsShowPortCard(true);
                updateCurrentPortStatus(record, portName);
            },
            hideSelectedPortCard: () => {
                setIsShowPortCard(false);
            },
            isSelectedPortCardShown: () => {
                return isShowPortCard;
            },
            isMouseClickOnPortCard: e => {
                const selectedPortCardRect = selectedPortCardRef.current.getBoundingClientRect();
                return (
                    selectedPortCardRect.x <= e.clientX &&
                    e.clientX <= selectedPortCardRect.x + selectedPortCardRect.width &&
                    selectedPortCardRect.y <= e.clientY &&
                    e.clientY <= selectedPortCardRect.y + selectedPortCardRect.height + 50
                );
            }
        }));

        const updateCurrentPortStatus = (record, portName) => {
            if (record.currentLinkToCore && Object.keys(record.currentLinkToCore).includes(portName)) {
                setIsPortSelectedWithCore(true);
                setSelectedPortType(record.currentLinkToCore[portName].port_type);
                setSelectedCoreSwitchSN(record.currentLinkToCore[portName].core_sn);
            } else {
                setIsPortSelectedWithCore(false);
                const portTypeName = portName.substring(0, 2).toLowerCase();
                setSelectedPortType(["ge", "te", "xe"].includes(portTypeName) ? portTypeName : "ge");
                const enabledSNOptions = getCoreSwitchSNOptions(record.sn).filter(item => {
                    return !item.disabled;
                });
                setSelectedCoreSwitchSN(enabledSNOptions.length !== 0 ? enabledSNOptions[0].value : "");
            }
        };

        const calculateCardOffsetTop = () => {
            return displayPos[1] + 90 + 270 > window.innerHeight ? displayPos[1] - 90 - 310 : displayPos[1] - 90;
        };

        const calculateCardOffsetLeft = () => {
            const offsetLeft = isMenuCollapsed ? displayPos[0] - 110 : displayPos[0] - 290;
            if (offsetLeft + 600 > window.innerWidth) {
                return offsetLeft - 290 + 5;
            }
            return offsetLeft;
        };

        return isShowPortCard ? (
            <div
                style={{
                    position: "absolute",
                    top: `${calculateCardOffsetTop()}px`,
                    left: `${calculateCardOffsetLeft()}px`,
                    transition: "background-color 0.3s ease",
                    width: "275px",
                    zIndex: 1000
                }}
            >
                <Card ref={selectedPortCardRef} title={displayPortName} bordered={false}>
                    <Flex vertical>
                        <Flex style={{fontSize: "14px", paddingBottom: "12px"}}>Port Type</Flex>
                        <Flex style={{paddingBottom: "12px"}}>
                            <Radio.Group
                                onChange={e => {
                                    setSelectedPortType(e.target.value);
                                }}
                                value={selectedPortType}
                                // disabled={isPortSelectedWithCore}
                                disabled
                                options={[
                                    {value: "ge", label: "ge"},
                                    {value: "te", label: "te"},
                                    {value: "xe", label: "xe"}
                                ]}
                            />
                        </Flex>
                        <Flex style={{fontSize: "14px", paddingBottom: "12px"}}>Switch SN</Flex>
                        <Flex style={{paddingBottom: "12px"}}>
                            <Select
                                style={{width: "100%"}}
                                value={selectedCoreSwitchSN}
                                onChange={key => {
                                    setSelectedCoreSwitchSN(key);
                                }}
                                options={currentCoreSwitchSNOptions}
                                disabled={isPortSelectedWithCore}
                                status={isSelectedCoreValid ? "" : "error"}
                            />
                        </Flex>
                        <Flex style={{fontSize: "14px", paddingBottom: "12px"}}>Port Connection</Flex>
                        <Flex style={{paddingBottom: "12px"}}>
                            {isPortSelectedWithCore ? (
                                <Button
                                    id="removeLinkToCore"
                                    onClick={() => {
                                        accessTabRemoveCorePortStatusCallback(selectedSN, displayPortName);
                                    }}
                                >
                                    Remove Link to Core
                                </Button>
                            ) : (
                                <Button
                                    id="linkToCore"
                                    onClick={() => {
                                        if (!selectedCoreSwitchSN) {
                                            setIsSelectedCoreValid(false);
                                            message.error("Please select a core switch.");
                                            return;
                                        }
                                        accessTabUpdateCorePortStatusCallback(
                                            selectedSN,
                                            displayPortName,
                                            selectedPortType,
                                            selectedCoreSwitchSN
                                        );
                                    }}
                                >
                                    Link to Core
                                </Button>
                            )}
                        </Flex>
                    </Flex>
                </Card>
            </div>
        ) : null;
    }
);

export default AccessTabSelectedPortCard;
