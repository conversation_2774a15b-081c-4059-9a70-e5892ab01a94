import PodNodeSelector from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/pod/pod_node_selector";
import {Button, Flex} from "antd";
import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from "react";
import {PlusOutlined} from "@ant-design/icons";

const PodsPanel = forwardRef(
    (
        {
            pods,
            addDistributionSwitchCallback,
            deleteDistributionSwitchCallback,
            addAccessSwitchCallback,
            deleteAccessSwitchCallback,
            addPodCallback,
            editPodNameCallback,
            deletePodCallback,
            showDeviceBriefTooltipCallback,
            hideDeviceBriefTooltipCallback,
            isSinglePodAccessNumValid,
            isHidePodValid
        },
        ref
    ) => {
        const [colCounts, setColCounts] = useState(1);

        const podRefs = useRef({});
        const [podsLocal, setPodsLocal] = useState(pods);

        useEffect(() => {
            window.addEventListener("resize", updateColCounts);
            return () => window.removeEventListener("resize", updateColCounts);
        }, []);

        useEffect(() => {
            updateColCounts();
            setPodsLocal(pods);
        }, [pods]);

        useImperativeHandle(ref, () => ({
            validate: async () => {
                const podNameList = [];
                const validPodRefs = Object.keys(podRefs.current).filter(key => podRefs.current[key]?.current !== null);
                for (const key of validPodRefs) {
                    if (!(await podRefs.current[key].current.validate())) {
                        return false;
                    }
                    const tempPodName = podRefs.current[key].current?.getPodName() || "";
                    if (tempPodName === "") {
                        return false;
                    }
                    if (podNameList.includes(tempPodName)) {
                        return false;
                    }
                    podNameList.push(tempPodName);
                }
                return true;
            },
            validatePodNameField: () => {
                const podIndexNameMappings = {};
                const podNameInvalidIndexList = [];
                Object.keys(pods).forEach((key, i) => {
                    let tempPodName = null;
                    if (podRefs.current[key].current === null) {
                        return;
                    }
                    tempPodName = pods[key].podName || podRefs.current[key].current.getPodName();
                    const valueList = Object.keys(podIndexNameMappings).map(key => podIndexNameMappings[key]);
                    if (valueList.includes(tempPodName)) {
                        valueList.forEach((podName, j) => {
                            if (podName === tempPodName) {
                                podNameInvalidIndexList.push(j);
                            }
                        });
                        podNameInvalidIndexList.push(i);
                    } else {
                        podIndexNameMappings[i] = tempPodName;
                    }
                });
                Object.keys(podRefs.current).forEach((key, i) => {
                    if (podNameInvalidIndexList.includes(i)) {
                        podRefs.current[key]?.current?.setPodNameInvalid();
                    } else {
                        podRefs.current[key]?.current?.updateIsPodValid();
                    }
                });
            }
        }));

        const updateColCounts = () => {
            const podsWithoutNull = pods.filter(pod => pod !== null);
            const podCount = podsWithoutNull.length === 0 ? 1 : podsWithoutNull.length;
            const windowWidth = window.innerWidth;
            const colCountsTemp = Math.floor((windowWidth - 565) / 500);
            setColCounts(colCountsTemp > podCount ? podCount : colCountsTemp);
        };

        const podNameChangeCallback = (index, value) => {
            const podIndexNameMappings = {};
            const podNameInvalidIndexList = [];
            Object.keys(podRefs.current).forEach((key, i) => {
                let tempPodName = null;
                if (podRefs.current[key].current === null) {
                    return;
                }
                if (i !== index) {
                    tempPodName = podRefs.current[key].current.getPodName();
                } else {
                    tempPodName = value;
                }
                const valueList = Object.keys(podIndexNameMappings).map(key => podIndexNameMappings[key]);
                if (valueList.includes(tempPodName)) {
                    valueList.forEach((podName, j) => {
                        if (podName === tempPodName) {
                            podNameInvalidIndexList.push(j);
                        }
                    });
                    podNameInvalidIndexList.push(i);
                } else {
                    podIndexNameMappings[i] = tempPodName;
                }
            });
            Object.keys(podRefs.current).forEach((key, i) => {
                if (podNameInvalidIndexList.includes(i)) {
                    podRefs.current[key]?.current?.setPodNameInvalid();
                } else {
                    podRefs.current[key]?.current?.updateIsPodValid();
                }
            });
        };

        return (
            <Flex vertical style={{marginBottom: "30px"}}>
                <Flex>
                    <h3>Pods</h3>
                    <Button
                        style={{
                            backgroundColor: "transparent",
                            color: "#14c9bb"
                        }}
                        type="link"
                        icon={<PlusOutlined />}
                        onClick={() => {
                            addPodCallback();
                        }}
                    >
                        Add
                    </Button>
                </Flex>
                <Flex gap="20px" wrap="wrap">
                    {podsLocal.map((pod, index) => {
                        podRefs.current[index] = React.createRef();
                        return pod !== null ? (
                            <PodNodeSelector
                                ref={podRefs.current[index]}
                                pod={pod}
                                index={index}
                                podsLocal={podsLocal}
                                colCounts={colCounts}
                                addDistributionSwitchCallback={addDistributionSwitchCallback}
                                deleteDistributionSwitchCallback={deleteDistributionSwitchCallback}
                                addAccessSwitchCallback={addAccessSwitchCallback}
                                deleteAccessSwitchCallback={deleteAccessSwitchCallback}
                                editPodNameCallback={editPodNameCallback}
                                deletePodCallback={deletePodCallback}
                                showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                                hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
                                podNameChangeCallback={podNameChangeCallback}
                                isSinglePodAccessNumValid={isSinglePodAccessNumValid}
                                isHidePodValid={isHidePodValid}
                            />
                        ) : null;
                    })}
                </Flex>
            </Flex>
        );
    }
);

export default PodsPanel;
