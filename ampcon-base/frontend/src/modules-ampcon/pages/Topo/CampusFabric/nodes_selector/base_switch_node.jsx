import hoverSwitchSvg from "@/modules-ampcon/pages/Topo/CampusFabric/resource/hover_switch.svg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import switchSvg from "@/modules-ampcon/pages/Topo/CampusFabric/resource/switch.svg";
import {useState} from "react";

const BaseSwitchNode = ({
    node,
    index,
    deleteNodeCallback,
    showDeviceBriefTooltipCallback,
    hideDeviceBriefTooltipCallback
}) => {
    const [isHover, setIsHover] = useState(false);
    const label = node?.label || "PICOS";
    const displayLabel = label.length > 10 ? `${label.slice(0, 10)}...` : label;
    const nodeIcon = isHover ? (
        <img
            src={hoverSwitchSvg}
            alt="hover switch"
            onClick={e => {
                const clickX = e.clientX;
                const clickY = e.clientY;
                const iconRect = e.target.getBoundingClientRect();
                const rightTopX = iconRect.right;
                const rightTopY = iconRect.top;
                const isInTargetArea =
                    clickX >= rightTopX - 20 &&
                    clickX <= rightTopX + 10 &&
                    clickY >= rightTopY - 20 &&
                    clickY <= rightTopY + 10;
                if (isInTargetArea) {
                    if (index === undefined) {
                        confirmModalAction("Are you sure you want to delete the switch?", () => {
                            deleteNodeCallback(node.switch_sn);
                        });
                    } else {
                        confirmModalAction("Are you sure you want to delete the switch?", () => {
                            deleteNodeCallback(index, node.switch_sn);
                        });
                    }
                }
            }}
        />
    ) : (
        <img src={switchSvg} alt="switch" />
    );

    return (
        <div
            style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
                textAlign: "center"
            }}
            onMouseEnter={() => {
                setIsHover(true);
                showDeviceBriefTooltipCallback(node);
            }}
            onMouseLeave={() => {
                setIsHover(false);
                hideDeviceBriefTooltipCallback();
            }}
        >
            {nodeIcon}
            <div
                style={{
                    fontFamily: "Lato",
                    fontWeight: 600,
                    fontSize: "14px",
                    color: "#212519",
                    marginTop: "2px",
                    whiteSpace: "nowrap"
                }}
            >
                {displayLabel}
            </div>
        </div>
    );
};

export default BaseSwitchNode;
