import BaseNodeSelector from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/base_node_selector";
import {Flex} from "antd";

const AccessNodeSelector = ({
    nodes,
    index,
    addAccessSwitchCallback,
    deleteAccessSwitchCallback,
    showDeviceBriefTooltipCallback,
    hideDeviceBriefTooltipCallback
}) => {
    return (
        <Flex vertical>
            <h4>Access</h4>
            <BaseNodeSelector
                nodes={nodes}
                index={index}
                addNodeCallback={addAccessSwitchCallback}
                deleteNodeCallback={deleteAccessSwitchCallback}
                showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
            />
        </Flex>
    );
};

export default AccessNodeSelector;
