import {Flex} from "antd";
import BaseSwitchNodeViewer from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/base_switch_node_viewer";

const BaseNodeDisplayItem = ({nodes, index, showDeviceBriefTooltipCallback, hideDeviceBriefTooltipCallback}) => {
    return (
        <Flex justify="center" align="center" style={{backgroundColor: "#F8FAFB", minHeight: "140px"}}>
            <Flex gap="50px" wrap="wrap" style={{padding: "30px"}}>
                {nodes &&
                    nodes.map(node => (
                        <BaseSwitchNodeViewer
                            node={node}
                            index={index}
                            showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                            hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
                        />
                    ))}
            </Flex>
        </Flex>
    );
};

export default BaseNodeDisplayItem;
