import {Flex} from "antd";
import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from "react";
import PodNodeDisplayItem from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/pod/pod_node_display_item";

const PodsDisplayPanel = forwardRef(
    (
        {pods, showDeviceBriefTooltipCallback, hideDeviceBriefTooltipCallback, viewPodNodeCallback, isPodNodeValid},
        ref
    ) => {
        const [colCounts, setColCounts] = useState(1);

        const podRefs = useRef({});
        const [podsLocal, setPodsLocal] = useState(pods);

        useEffect(() => {
            window.addEventListener("resize", updateColCounts);
            return () => window.removeEventListener("resize", updateColCounts);
        }, []);

        useEffect(() => {
            updateColCounts();
            setPodsLocal(pods);
        }, [pods]);

        useImperativeHandle(ref, () => ({
            validate: async () => {}
        }));

        const updateColCounts = () => {
            const podsWithoutNull = pods.filter(pod => pod !== null);
            const podCount = podsWithoutNull.length === 0 ? 1 : podsWithoutNull.length;
            const windowWidth = window.innerWidth;
            const colCountsTemp = Math.floor((windowWidth - 565) / 500);
            setColCounts(colCountsTemp > podCount ? podCount : colCountsTemp);
        };

        return (
            <Flex vertical style={{marginBottom: "30px"}}>
                <Flex>
                    <h3>Pods</h3>
                </Flex>
                <Flex gap="20px" wrap="wrap">
                    {podsLocal.map((pod, index) => {
                        podRefs.current[index] = React.createRef();
                        return (
                            <PodNodeDisplayItem
                                ref={podRefs.current[index]}
                                pod={pod}
                                index={index}
                                colCounts={colCounts}
                                showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                                hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
                                viewPodNodeCallback={viewPodNodeCallback}
                                isPodNodeValid={isPodNodeValid}
                            />
                        );
                    })}
                </Flex>
            </Flex>
        );
    }
);

export default PodsDisplayPanel;
