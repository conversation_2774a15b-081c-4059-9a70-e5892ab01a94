import {
    AmpConCustomTable,
    createColumnConfig,
    createColumnWithoutFilter,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {useState, useEffect, useRef, forwardRef, useImperativeHandle} from "react";
import {useSelector} from "react-redux";
import {useParams} from "react-router-dom";
import {Card, Space, Tag, message, Button, Divider, Input, Flex, Modal} from "antd";
import Icon from "@ant-design/icons/lib/components/Icon";
import {ArrowLeftOutlined, ReloadOutlined} from "@ant-design/icons";
import {getDeploymentStatus, viewSiteTopo} from "@/modules-ampcon/apis/campus_blueprint_api";
import backStyles from "@/modules-ampcon/pages/Topo/CampusFabric/campus_fabric.module.scss";
import tagStyles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";
import {onlineSvg, offlineSvg, exclamationSvg} from "@/utils/common/iconSvg";

const LogAction = ({record}) => {
    const logViewTextareaModalRef = useRef(null);

    return (
        <>
            <a
                onClick={() => {
                    logViewTextareaModalRef.current.showLogViewTextareaModal(record.sn, record.task_log);
                }}
            >
                Log
            </a>
            <LogViewTextareaModal ref={logViewTextareaModalRef} />
        </>
    );
};

const LogViewTextareaModal = forwardRef((props, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [selectedSwitchSN, setSelectedSwitchSN] = useState("");
    const [logContent, setLogContent] = useState("");

    useImperativeHandle(ref, () => ({
        showLogViewTextareaModal: (sn, task_log) => {
            setSelectedSwitchSN(sn);
            setLogContent(task_log);
            setIsShowModal(true);
        },
        hideLogViewTextareaModal: () => {
            resetModal();
        }
    }));

    const readonlyStyle = {
        minHeight: "330px",
        height: "58vh",
        resize: "vertical",
        backgroundColor: "#F8FAFB",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        maxHeight: "calc(100vh - 500px)",
        border: "none"
    };

    const resetModal = () => {
        setIsShowModal(false);
        setSelectedSwitchSN("");
        setLogContent("");
    };

    return (
        <Modal
            className="ampcon-middle-modal"
            title={
                <>
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                        {`${selectedSwitchSN} logs`}
                        <Button
                            type="text"
                            className="ant-modal-close"
                            style={{marginRight: "30px"}}
                            icon={<ReloadOutlined className="anticon anticon-close ant-modal-close-icon" />}
                            onClick={() => {
                                setLogContent(logContent);
                            }}
                        />
                    </div>
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </>
            }
            open={isShowModal}
            onCancel={() => {
                resetModal();
            }}
            footer={null}
        >
            <Flex vertical style={{flex: 1}}>
                <Input.TextArea style={readonlyStyle} value={logContent} rows={19} readOnly />
            </Flex>
        </Modal>
    );
});

const CampusFabricSwitchStatus = () => {
    const [isForbidden, setIsForbidden] = useState(false);
    const taskStatusRef = useRef(null);
    const fetchIntervalRef = useRef();
    const currentUser = useSelector(state => state.user.userInfo);
    const [campusFabricData, setCampusFabricData] = useState([]);
    const {id} = useParams();

    const fetchSiteTopo = async recordId => {
        if (recordId) {
            try {
                const res = await viewSiteTopo(recordId);
                setCampusFabricData(res);
            } catch (error) {
                console.error(error);
            }
        }
    };

    useEffect(() => {
        fetchSiteTopo(id);
    }, [id]);
    // 定义表格列
    const switchColumns = [
        {...createColumnConfig("Sysname", "host_name", TableFilterDropdown)},
        {...createColumnConfig("SN", "sn", TableFilterDropdown)},
        {
            ...createColumnConfig("VPN IP", "mgt_ip"),
            sorter: false,
            render: (_, record) => {
                if (!record.mgt_ip) {
                    return null;
                }

                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.mgt_ip}
                    </Space>
                );
            }
        },
        {
            ...createColumnConfig("Mgmt IP", "link_ip_addr"),
            sorter: false,
            render: (_, record) => {
                return record.link_ip_addr || null;
            }
        },
        {...createColumnConfig("Device Type", "task_role", TableFilterDropdown)},
        {
            ...createColumnConfig("Deployment Status", "task_status", TableFilterDropdown),
            sorter: true,
            render: (text, record) => {
                let tagClass;
                switch (record.task_status) {
                    case "SUCCEED":
                        tagClass = tagStyles.successTag;
                        break;
                    case "FAILED":
                        tagClass = tagStyles.failedTag;
                        break;
                    case "RUNNING":
                        tagClass = tagStyles.runningTag;
                        break;
                    default:
                        tagClass = tagStyles.pendingTag;
                        break;
                }
                return <Tag className={tagClass}>{record.task_status}</Tag>;
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <LogAction record={record} />
                        </Space>
                    </div>
                );
            }
        }
    ].filter(Boolean);

    const switchSearchFieldsList = ["host_name", "mgt_ip", "device_type", "sn", "task_status"];
    const switchMatchFieldsList = [
        {name: "host_name", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "device_type", matchMode: "fuzzy"},
        {name: "create_time", matchMode: "fuzzy"},
        {name: "task_status", matchMode: "fuzzy"}
    ];

    useEffect(() => {
        const userType = currentUser?.type;
        setIsForbidden(userType === "readonly");
    }, [currentUser]);

    useEffect(() => {
        fetchIntervalRef.current = setInterval(() => {
            if (taskStatusRef.current) {
                taskStatusRef.current.refreshTable();
            }
        }, 10000);
        return () => clearInterval(fetchIntervalRef.current);
    }, []);

    return (
        <div className="campus-fabric-switch-status-container" style={{minHeight: "100%"}}>
            <Card style={{display: "flex", flex: 1, minHeight: "100%"}}>
                {/* <p className={backStyles.goBack} onClick={backToTableViewCallback} style={{marginBottom: "23px"}}>
                    <ArrowLeftOutlined style={{marginRight: "8px"}} />
                    <span>Back</span>
                </p> */}
                <h2 style={{marginTop: "8px", marginBottom: "20px"}}>{campusFabricData.topology_name}</h2>
                <AmpConCustomTable
                    tableRef={taskStatusRef}
                    columns={switchColumns}
                    searchFieldsList={switchSearchFieldsList}
                    matchFieldsList={switchMatchFieldsList}
                    fetchAPIInfo={getDeploymentStatus}
                    fetchAPIParams={[campusFabricData.site_config_id]}
                    ref={taskStatusRef}
                />
            </Card>
        </div>
    );
};

export default CampusFabricSwitchStatus;
