export const deleteSvg = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16">
            <defs>
                <clipPath id="master_svg0_166_20152">
                    <rect x="0" y="0" width="16" height="16" rx="0" />
                </clipPath>
            </defs>
            <g clipPath="url(#master_svg0_166_20152)">
                <g>
                    <path
                        d="M9.49994,12C9.22346,12,9.00002,11.7765,9.00002,11.5L9.00002,5.99998C9.00002,5.72349,9.22346,5.49994,9.49994,5.49994C9.77643,5.49994,9.99998,5.72351,9.99998,5.99998L9.99998,11.5C9.99998,11.7765,9.77644,12,9.49994,12L9.49994,12ZM6.50001,12C6.22343,12,5.99998,11.7765,5.99998,11.5L5.99998,5.99998C5.99998,5.72349,6.22343,5.49994,6.50001,5.49994C6.77649,5.49994,6.99993,5.72351,6.99993,5.99998L6.99993,11.5C6.99993,11.7765,6.77649,12,6.50001,12L6.50001,12ZM14.4999,3.50001L11.9999,3.50001L11.9999,2.50001C11.9999,1.672981,11.3324,1.000000359596,10.51145,1.000000359596L5.49993,1.000000359596C4.67299,1.000000359596,4,1.672996,4,2.50001L4,3.50001L1.5000339999999999,3.50001C1.223458,3.50001,1,3.72345,1,4.000030000000001C1,4.27651,1.223457,4.49995,1.5000339999999999,4.49995L14.5,4.49995C14.7764,4.49995,15,4.27651,15,4.000030000000001C15,3.72345,14.7764,3.5,14.5,3.5L14.4999,3.50001ZM4.99999,2.50001C4.99999,2.2245,5.22445,1.999989,5.49993,1.999989L10.51146,1.999989C10.78553,1.999989,10.99998,2.2195,10.99998,2.50001L10.99998,3.50001L4.99999,3.50001L4.99999,2.50001ZM11.502,15L4.50094,15C3.67399,15,3.00101,14.327,3.00101,13.5L3.00101,5.99251C3.00101,5.71649,3.22501,5.4925,3.50097,5.4925C3.77701,5.4925,4.00099,5.71649,4.00099,5.99251L4.00099,13.5C4.00099,13.776,4.22547,14,4.50096,14L11.502,14C11.7779,14,12.002,13.776,12.002,13.5L12.002,6.01297C12.002,5.73701,12.2255,5.513,12.502,5.513C12.7785,5.513,13.002,5.73701,13.002,6.01297L13.002,13.5C13.002,14.327,12.3289,15,11.502,15Z"
                        fill="currentColor"
                        fillOpacity="1"
                    />
                    <path
                        d="M15.25,4.000030000000001Q15.25,3.25,14.5,3.25L14.5,3.75Q14.75,3.75,14.75,4.000030000000001Q14.75,4.24995,14.5,4.24995L1.5000339999999999,4.24995Q1.25,4.24995,1.25,4.000030000000001Q1.25,3.75001,1.5000339999999999,3.75001L4.25,3.75001L4.25,2.50001Q4.25,1.983306,4.61664,1.616648Q4.983280000000001,1.25,5.49993,1.25L10.51145,1.25Q11.023,1.25,11.3861,1.61595Q11.7499,1.982661,11.7499,2.50001L11.7499,3.75001L14.4999,3.75001L14.4999,3.25001L12.2499,3.25001L12.2499,2.50001Q12.2499,1.776703,11.741,1.263788Q11.2313,0.75,10.51145,0.75L5.49993,0.75Q4.77616,0.75,4.26308,1.263101Q3.75,1.776204,3.75,2.50001L3.75,3.25001L1.5000339999999999,3.25001Q0.75,3.25001,0.75,4.000030000000001Q0.75,4.31096,0.9695394,4.53046Q1.189066,4.74995,1.5000339999999999,4.74995L14.5,4.74995Q14.8109,4.74995,15.0304,4.53048Q15.25,4.31096,15.25,4.000030000000001ZM4.74999,2.50001L4.74999,3.75001L11.25,3.75001L11.25,2.50001Q11.25,1.749989,10.51146,1.749989L5.49993,1.749989Q5.18975,1.749989,4.969860000000001,1.969921Q4.74999,2.1898299999999997,4.74999,2.50001ZM5.24999,3.25001L10.74998,3.25001L10.74998,2.50001Q10.74998,2.24999,10.51146,2.24999L5.49993,2.24999Q5.24999,2.24999,5.24999,2.50001L5.24999,3.25001ZM11.502,15.25Q13.252,15.25,13.252,13.5L13.252,6.01297Q13.252,5.7024,13.0325,5.48277Q12.8128,5.263,12.502,5.263Q12.1911,5.263,11.9714,5.48277Q11.752,5.7024,11.752,6.01297L11.752,13.5Q11.752,13.75,11.502,13.75L4.50096,13.75Q4.25099,13.75,4.25099,13.5L4.25099,5.99251Q4.25099,5.68194,4.03127,5.46222Q3.81155,5.2425,3.50097,5.2425Q2.75101,5.2425,2.75101,5.99251L2.75101,13.5Q2.75101,15.25,4.50094,15.25L11.502,15.25ZM6.50002,12.25Q7.24993,12.2499,7.24993,11.5L7.24993,5.99998Q7.24993,5.24994,6.50001,5.24994Q5.74998,5.24994,5.74998,5.99998L5.74998,11.5Q5.74998,11.811,5.96951,12.0305Q6.18904,12.25,6.50002,12.25ZM9.49996,12.25Q10.24998,12.2499,10.24998,11.5L10.24998,5.99998Q10.24998,5.24994,9.49994,5.24994Q9.18902,5.24994,8.9695,5.46953Q8.75002,5.68907,8.75002,5.99998L8.75002,11.5Q8.75002,12.25,9.49996,12.25ZM12.752,6.01297L12.752,13.5Q12.752,14.75,11.502,14.75L4.50094,14.75Q3.25101,14.75,3.25101,13.5L3.25101,5.99251Q3.25101,5.7425,3.50097,5.7425Q3.75099,5.7425,3.75099,5.99251L3.75099,13.5Q3.75099,13.8105,3.97094,14.0304Q4.19074,14.25,4.50096,14.25L11.502,14.25Q12.252,14.25,12.252,13.5L12.252,6.01297Q12.252,5.763,12.502,5.763Q12.752,5.763,12.752,6.01297ZM6.74993,5.99998L6.74993,11.5Q6.74993,11.75,6.49999,11.75Q6.24998,11.75,6.24998,11.5L6.24998,5.99998Q6.24998,5.74994,6.50001,5.74994Q6.74993,5.74994,6.74993,5.99998ZM9.74998,5.99998L9.74998,11.5Q9.74998,11.75,9.49993,11.75Q9.25002,11.75,9.25002,11.5L9.25002,5.99998Q9.25002,5.74994,9.49994,5.74994Q9.74998,5.74994,9.74998,5.99998Z"
                        fillRule="evenodd"
                        fill="currentColor"
                        fillOpacity="1"
                    />
                </g>
            </g>
        </svg>
    );
};
