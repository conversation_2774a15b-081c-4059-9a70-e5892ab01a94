import {<PERSON><PERSON>, <PERSON>, <PERSON>, message} from "antd";
import {
    AmpConCustomTable,
    createColumnConfig,
    createColumnWithoutFilter,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import Icon from "@ant-design/icons";
import {addSvg} from "@/utils/common/iconSvg";
import {useRef} from "react";
import {useNavigate} from "react-router-dom";
import {getSiteTopoList, deleteSiteTopo} from "@/modules-ampcon/apis/campus_blueprint_api";
import styles from "@/modules-ampcon/pages/Topo/Topology/topo.module.scss";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const CampusFabricTableView = () => {
    const tableRef = useRef();
    const navigate = useNavigate();
    const matchFieldsList = [
        {name: "create_time", matchMode: "fuzzy"},
        {name: "topology_name", matchMode: "fuzzy"},
        {name: "site_id", matchMode: "fuzzy"},
        {name: "id", matchMode: "fuzzy"},
        {name: "modified_time", matchMode: "fuzzy"},
        {name: "site_name", matchMode: "fuzzy"},
        {name: "type", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["topology_name", "site_id", "create_time", "id", "modified_time", "site_name", "type"];
    const view_fabric = record => {
        if (record.type === "mlag") {
            navigate(`/network_design/campus_fabric/mlag_view/${record.id}`);
        } else if (record.type === "ip-clos") {
            navigate(`/network_design/campus_fabric/ipclos_view/${record.id}`);
        }
    };
    const delete_fabric = record => {
        confirmModalAction("Are you sure want to delete?", () => {
            deleteSiteTopo(record.id).then(res => {
                if (res.status === 200) {
                    message.success(res.info);
                    tableRef.current.refreshTable();
                } else {
                    message.error(res.info);
                }
            });
        });
    };

    const columns = [
        createColumnConfig("Name", "topology_name", TableFilterDropdown),
        createColumnConfig("Site", "site_name", TableFilterDropdown),
        {
            title: "Topology Type",
            dataIndex: "type",
            render: (_, record) => {
                if (record.type === "mlag") {
                    return "MLAG";
                }
                if (record.type === "ip-clos") {
                    return "IP Clos";
                }
            }
        },
        createColumnWithoutFilter("Date Created", "create_time"),
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="middle" className={styles.actionLink}>
                            <a onClick={() => view_fabric(record)}>View</a>
                            <a onClick={() => delete_fabric(record)}> Delete</a>
                        </Space>
                    </div>
                );
            }
        }
    ];
    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{margin: "8px 0 20px"}}>Campus Fabric</h2>
            <AmpConCustomTable
                ref={tableRef}
                columns={columns}
                fetchAPIInfo={getSiteTopoList}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={
                    <Button
                        type="primary"
                        // onClick={createCampusFabricCallback}
                        onClick={() => {
                            navigate("/network_design/campus_fabric/create");
                        }}
                    >
                        <Icon component={addSvg} />
                        Campus Fabric
                    </Button>
                }
            />
        </Card>
    );
};

export default CampusFabricTableView;
