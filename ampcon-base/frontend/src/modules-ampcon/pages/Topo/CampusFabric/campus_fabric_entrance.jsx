import {useState} from "react";
import CampusFabricTableView from "@/modules-ampcon/pages/Topo/CampusFabric/campus_fabric_table_view";
import CampusFabricCreateAndEditView from "@/modules-ampcon/pages/Topo/CampusFabric/campus_fabric_create_and_edit_view";

const CampusFabric = () => {
    const [isInTableView, setIsInTableView] = useState(true);
    const [isInCreateView, setIsInCreateView] = useState(false);
    const [isInEditView, setIsInEditView] = useState(false);

    const createCampusFabricCallback = () => {
        setIsInTableView(false);
        setIsInEditView(false);
        setIsInCreateView(true);
    };

    const backToTableViewCallback = () => {
        setIsInEditView(false);
        setIsInCreateView(false);
        setIsInTableView(true);
    };

    const getCampusFabricComponent = () => {
        if (isInTableView) {
            return <CampusFabricTableView createCampusFabricCallback={createCampusFabricCallback} />;
        }
        if (isInCreateView || isInEditView) {
            return <CampusFabricCreateAndEditView backToTableViewCallback={backToTableViewCallback} />;
        }
    };

    return getCampusFabricComponent();
};

export default CampusFabric;
