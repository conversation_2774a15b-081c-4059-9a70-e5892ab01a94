import {Space, Form, message, Radio} from "antd";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";

import {DHCPModal} from "../modal/dhcp_modal";

import Icon from "@ant-design/icons";

import {useState, useRef, useEffect, forwardRef, useImperativeHandle} from "react";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {useDeepCompareEffect} from "ahooks";

export const DHCPRelayForm = forwardRef(({networkData, networks, setCampusFabricData, tableStyle}, ref) => {
    const [isEnable, setIsEnable] = useState(networks.enable_dhcp_relay === "enable");
    const [form] = Form.useForm();
    const dhcpModalref = useRef(null);
    const [dhcpData, setDhcpData] = useState(networks.dhcp_relay);

    useDeepCompareEffect(() => {
        const newData = dhcpData.map(item => {
            return {
                ...item,
                vlan_id: networkData.find(network => network.name === item.dhcp_network)?.vlan_id
            };
        });
        setDhcpData(newData);
    }, [networkData]);

    useEffect(() => {
        setCampusFabricData(prev => {
            return {
                ...prev,
                networks: {
                    ...prev.networks,
                    dhcp_relay: dhcpData
                }
            };
        });
    }, [dhcpData]);

    useImperativeHandle(ref, () => ({
        getConfiguration: () => {
            return {
                dhcp_snooping: dhcpData,
                dhcp_snooping_enable: isEnable
            };
        },
        removeRelatedNetwork: networkName => {
            const newDhcpData = dhcpData.filter(item => item.dhcp_network !== networkName);
            setDhcpData(newDhcpData);
        },
        validate: () => {
            if (isEnable && dhcpData.length === 0) {
                Promise.reject(new Error("Please add DHCP Relay"));
                message.error("Please add DHCP Relay");
                return false;
            }
            return true;
        }
    }));

    const columns = [
        {
            title: "Network",
            dataIndex: "dhcp_network",
            render: (_, record) => {
                return record.dhcp_network;
            },
            sorter: (a, b) => a.dhcp_network.localeCompare(b.dhcp_network)
        },
        {
            title: "VLAN ID",
            dataIndex: "vlan_id",
            render: (_, record) => {
                return record.vlan_id;
            },
            sorter: (a, b) => a.vlan_id - b.vlan_id
        },
        {
            title: "DHCP Server",
            dataIndex: "dhcp_server",
            render: (_, record) => {
                return record.dhcp_server;
            },
            sorter: (a, b) => a.dhcp_server.localeCompare(b.dhcp_server)
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                dhcpModalref.current.showNetworkModal({mode: "edit"}, record);
                            }}
                        >
                            Edit
                        </a>
                        <a
                            onClick={() => {
                                confirmModalAction("Are you sure want to delete?", () => {
                                    try {
                                        setDhcpData(dhcpData.filter(item => item !== record));
                                        message.success("DHCP deleted successfully");
                                    } catch (error) {
                                        message.error("An error occurred while deleting the DHCP");
                                    }
                                });
                            }}
                        >
                            Delete
                        </a>
                    </Space>
                );
            }
        }
    ];

    return (
        <>
            <h3>DHCP Relay</h3>
            <DHCPModal
                type="relay"
                ref={dhcpModalref}
                dhcpData={dhcpData}
                setDhcpData={setDhcpData}
                networkData={networkData}
                CreateLabel="Create DHCP Relay"
                EditLabel="Edit DHCP Relay"
            />
            <Form ref={ref} form={form} validateTrigger="onBlur" labelAlign="left" style={{width: 505}}>
                <Form.Item
                    name="enable"
                    label="Configuration"
                    labelCol={{style: {width: 175}}}
                    initialValue={isEnable ? "enable" : "disable"}
                >
                    <Radio.Group
                        onChange={e => {
                            setIsEnable(e.target.value === "enable");
                            if (e.target.value === "disable") {
                                setDhcpData([]);
                            }
                            setCampusFabricData(prev => {
                                return {
                                    ...prev,
                                    networks: {
                                        ...prev.networks,
                                        enable_dhcp_relay: e.target.value
                                    }
                                };
                            });
                        }}
                    >
                        <Radio value="enable">Enable</Radio>
                        <Radio value="disable">Disable</Radio>
                    </Radio.Group>
                </Form.Item>
                {isEnable && (
                    <Form.Item style={{marginBottom: "0px"}} label="DHCP Relay" labelCol={{style: {width: 175}}}>
                        <a
                            style={{
                                border: "none",
                                borderRadius: "4px",
                                color: "#14c9bb"
                            }}
                            onClick={() => {
                                dhcpModalref.current.showNetworkModal({mode: "create"});
                            }}
                        >
                            <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                            Add
                        </a>
                    </Form.Item>
                )}
            </Form>

            {isEnable && (
                <div>
                    <AmpConCustomTable
                        dataSource={dhcpData}
                        columns={columns}
                        style={tableStyle}
                        pagination={{
                            defaultPageSize: 10,
                            showSizeChanger: true,
                            hideOnSinglePage: dhcpData.length <= 10
                        }}
                    />
                </div>
            )}
            {/* <Divider style={{marginTop: "32px", marginBottom: "32px"}} /> */}
        </>
    );
});
