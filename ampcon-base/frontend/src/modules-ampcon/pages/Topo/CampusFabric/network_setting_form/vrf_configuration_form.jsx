import {Space, Form, message, Divider, Radio} from "antd";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {createColumnConfig, AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {VRFModal} from "../modal/vrf_modal";

import Icon from "@ant-design/icons";

import {useState, useRef, useEffect, forwardRef, useImperativeHandle} from "react";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

export const VRFConfigurationForm = forwardRef(
    ({networkData, networks, setCampusFabricData, tableStyle, type}, ref) => {
        useEffect(() => {
            if (type) {
                setCampusFabricData(prev => {
                    return {
                        ...prev,
                        networks: {
                            ...prev.networks,
                            enable_vrf: "enable"
                        }
                    };
                });
            }
        }, []);

        const [isVRFenable, setIsVRFenable] = useState(type ? true : networks.enable_vrf === "enable");

        const vrfModalRef = useRef(null);

        const columns = [
            {...createColumnConfig("Name", "name", "", "", "33%"), sorter: (a, b) => a.name.localeCompare(b.name)},

            {
                title: "Networks",
                dataIndex: "networks",
                width: "33%",
                render: (_, record) => {
                    return record.networks?.map(item => <div key={item}>{item}</div>);
                },
                filters: networkData.map(item => ({text: item.name, value: item.name})),
                onFilter: (value, record) => record.networks.includes(value)
            },
            // {
            //     title: "Route",
            //     dataIndex: "extra_routes_route",
            //     width: "20%",
            //     render: (_, record) => {
            //         return (
            //             <div>
            //                 {record.extra_routes?.map(item => (
            //                     <div key={item.route}>{item.route}</div>
            //                 ))}
            //             </div>
            //         );
            //     }
            // },
            // {
            //     title: "Via",
            //     dataIndex: "extra_routes_via",
            //     width: "20%",
            //     render: (_, record) => {
            //         return (
            //             <div>
            //                 {record.extra_routes?.map(item => (
            //                     <div key={item.via}>{item.via}</div>
            //                 ))}
            //             </div>
            //         );
            //     }
            // },

            {
                title: "Operation",
                fixed: "right",
                render: (_, record) => {
                    return (
                        <div>
                            <Space size="large" className="actionLink">
                                <a
                                    onClick={() => {
                                        vrfModalRef.current.showVRFModal({mode: "edit"}, record);
                                    }}
                                >
                                    Edit
                                </a>
                                <a
                                    onClick={() => {
                                        confirmModalAction("Are you sure want to delete?", () => {
                                            try {
                                                setVRF_data(VRF_data.filter(item => item.name !== record.name));
                                                message.success("VRF deleted successfully");
                                            } catch (error) {
                                                message.error("An error occurred while deleting the VRF");
                                            }
                                        });
                                    }}
                                >
                                    Delete
                                </a>
                            </Space>
                        </div>
                    );
                }
            }
        ];

        function to_frontend_structure(data) {
            return Object.keys(data).map(key => {
                return {
                    name: key,
                    networks: data[key].network
                    // extra_routes: data[key].extra_routes
                };
            });
        }

        function to_backend_structure(data) {
            return data.map(item => {
                return {
                    [item.name]: {
                        network: item.networks
                        // extra_routes: item.extra_routes
                    }
                };
            });
        }

        const [VRF_data, setVRF_data] = useState(to_frontend_structure(networks.vrfs));
        useEffect(() => {
            const vrfs = to_backend_structure(VRF_data.filter(item => item.networks.length > 0));
            const newVRFData = vrfs.reduce((acc, cur) => {
                return {...acc, ...cur};
            }, {});
            setCampusFabricData(prev => {
                return {
                    ...prev,
                    networks: {
                        ...prev.networks,
                        vrfs: newVRFData
                    }
                };
            });
        }, [VRF_data]);

        const [form] = Form.useForm();
        const tableRef = useRef(null);

        useImperativeHandle(ref, () => ({
            removeRelatedNetwork: networkName => {
                const newVRFData = VRF_data.map(item => {
                    return {
                        ...item,
                        networks: item.networks.filter(network => network !== networkName)
                    };
                }).filter(item => item.networks.length > 0);

                setVRF_data(newVRFData);
            },

            getConfiguration: () => {
                return {...VRF_data, vrf_enable: isVRFenable};
            },
            validate: () => {
                if (isVRFenable && VRF_data.length === 0) {
                    Promise.reject(new Error("Please add VRF"));
                    message.error("Please add VRF");
                    return false;
                }
                return true;
            }
        }));

        return (
            <div>
                <h3>VRF</h3>
                <Form ref={ref} form={form} validateTrigger="onBlur" labelAlign="left" style={{width: 505}}>
                    <div style={{visibility: "hidden", height: "0px"}}>
                        <Form.Item
                            name="vrf_enable"
                            label="Configuration"
                            labelCol={{style: {width: 175}}}
                            initialValue={isVRFenable}
                        >
                            <Radio.Group
                                disabled={type}
                                onChange={e => {
                                    if (!e.target.value) {
                                        setVRF_data([]);
                                    }
                                    setCampusFabricData(prev => {
                                        return {
                                            ...prev,
                                            networks: {
                                                ...prev.networks,
                                                enable_vrf: e.target.value === true ? "enable" : "disable"
                                            }
                                        };
                                    });
                                    setIsVRFenable(e.target.value);
                                }}
                            >
                                <Radio value>Enable</Radio>
                                <Radio value={false}>Disable</Radio>
                            </Radio.Group>
                        </Form.Item>
                    </div>
                    {isVRFenable && (
                        <Form.Item style={{marginBottom: "0px"}} label="VRF" labelCol={{style: {width: 175}}}>
                            <a
                                style={{
                                    border: "none",
                                    borderRadius: "4px",
                                    color: "#14c9bb"
                                }}
                                onClick={() => {
                                    if (VRF_data.length >= 128) {
                                        message.error("You can only add up to 128 VRFs!");
                                        return;
                                    }
                                    vrfModalRef.current.showVRFModal({mode: "create"});
                                }}
                            >
                                <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                                Add
                            </a>
                        </Form.Item>
                    )}
                </Form>
                {isVRFenable && (
                    <AmpConCustomTable
                        ref={tableRef}
                        pagination={{
                            defaultPageSize: 10,
                            showSizeChanger: true,
                            hideOnSinglePage: VRF_data.length <= 10
                        }}
                        dataSource={VRF_data}
                        columns={columns}
                        style={tableStyle}
                    />
                )}
                <VRFModal
                    ref={vrfModalRef}
                    handleAddVRF={data => {
                        setVRF_data([...VRF_data, data]);
                    }}
                    handleEditVRF={data => {
                        setVRF_data(VRF_data.map(item => (item.name === data.name ? data : item)));
                    }}
                    networkData={networkData}
                    VRF_data={VRF_data}
                />
                <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
            </div>
        );
    }
);
