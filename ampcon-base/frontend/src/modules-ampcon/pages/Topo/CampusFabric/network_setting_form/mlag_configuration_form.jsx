import {Form, message, Divider, Radio, Input} from "antd";
import {formValidateRules} from "@/modules-ampcon/utils/util";

import {forwardRef, useImperativeHandle} from "react";

export const MLAGConfigurationForm = forwardRef(({networks, setCampusFabricData}, ref) => {
    const [form] = Form.useForm();

    useImperativeHandle(ref, () => ({
        validate: () => {
            form.validateFields();
            const domain_id = form.getFieldValue("domain_id");
            if (domain_id >= 1 && domain_id <= 255 && domain_id && domain_id.slice(0, 1) !== "0") {
                return true;
            }
            return false;
        }
    }));

    return (
        <>
            <h3>MLAG</h3>
            <Form
                ref={ref}
                form={form}
                validateTrigger="onBlur"
                labelAlign="left"
                onValuesChange={changedValues => {
                    setCampusFabricData(prev => {
                        return {
                            ...prev,
                            networks: {
                                ...prev.networks,
                                domain_id: changedValues.domain_id
                            }
                        };
                    });
                }}
            >
                <Form.Item
                    name="domain_id"
                    label="Domain ID"
                    validateFirst
                    labelCol={{style: {width: 175}}}
                    rules={[
                        {
                            required: true,
                            message: "Please input Domain ID"
                        },
                        formValidateRules.int(),
                        {
                            validator: (rule, value) => {
                                if (!value) {
                                    return Promise.resolve();
                                }
                                if (value && typeof value === "string" && value.slice(0, 1) === "0") {
                                    return Promise.reject(new Error("Invalid Domain ID"));
                                }
                                if (value < 1 || value > 255) {
                                    return Promise.reject(new Error("Domain ID must be between 1 and 255"));
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    initialValue={networks.domain_id}
                >
                    <Input style={{width: "280px"}} placeholder="Range(1-255)" />
                </Form.Item>
            </Form>
            <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
        </>
    );
});
