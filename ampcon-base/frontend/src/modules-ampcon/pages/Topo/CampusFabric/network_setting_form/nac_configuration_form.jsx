import {Space, Form, message, Divider, Radio} from "antd";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {NACServerModal} from "../modal/nac_server_modal";

import Icon from "@ant-design/icons";

import {useState, useRef, useEffect, forwardRef, useImperativeHandle} from "react";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

export const NACConfigurationForm = forwardRef(({networks, setCampusFabricData, tableStyle}, ref) => {
    const [isEnable, setIsEnable] = useState(networks.enable_nac === "enable");
    const [form] = Form.useForm();
    const nacServerModalRef = useRef(null);
    const [serverData, setServerData] = useState(networks?.nac_servers);

    useEffect(() => {
        setCampusFabricData(prev => {
            return {
                ...prev,
                networks: {
                    ...prev.networks,
                    nac_servers: serverData
                }
            };
        });
    }, [serverData]);

    useImperativeHandle(ref, () => ({
        validate: () => {
            if (isEnable && serverData.length === 0) {
                Promise.reject(new Error("Please add NAC Server"));
                message.error("Please add NAC Server");
                return false;
            }
            return true;
        }
    }));

    const columns = [
        {
            title: "Server Name",
            dataIndex: "server_name",
            width: "25%",
            render: (_, record) => {
                return record.server_name;
            },
            sorter: (a, b) => a.server_name.localeCompare(b.server_name)
        },
        {
            title: "Server Address",
            dataIndex: "server_address",
            width: "25%",
            render: (_, record) => {
                return record.server_address;
            },
            sorter: (a, b) => a.server_address.localeCompare(b.server_address)
        },
        {
            title: "Port",

            dataIndex: "port",

            width: "25%",
            render: (_, record) => {
                return record.port;
            },
            sorter: (a, b) => a.port - b.port
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                nacServerModalRef.current.showNetworkModal({mode: "edit"}, record);
                            }}
                        >
                            Edit
                        </a>
                        <a
                            onClick={() => {
                                confirmModalAction("Are you sure want to delete?", () => {
                                    try {
                                        setServerData(
                                            serverData.filter(item => item.server_name !== record.server_name)
                                        );
                                        message.success("Server deleted successfully");
                                    } catch (error) {
                                        message.error("An error occurred while deleting the server");
                                    }
                                });
                            }}
                        >
                            Delete
                        </a>
                    </Space>
                );
            }
        }
    ];

    return (
        <>
            <h3>NAC</h3>
            <NACServerModal
                ref={nacServerModalRef}
                handleAddServer={data => {
                    setServerData([...serverData, data]);
                }}
                handleEditServer={data => {
                    setServerData(serverData.map(item => (item.server_name === data.server_name ? data : item)));
                }}
                serverData={serverData}
            />
            <Form ref={ref} form={form} validateTrigger="onBlur" labelAlign="left" style={{width: 505}}>
                <Form.Item
                    name="enable"
                    label="Configuration"
                    labelCol={{style: {width: 175}}}
                    initialValue={isEnable ? "enable" : "disable"}
                >
                    <Radio.Group
                        onChange={e => {
                            if (e.target.value === "disable") {
                                setServerData([]);
                            }
                            setIsEnable(e.target.value === "enable");
                            setCampusFabricData(prev => {
                                return {
                                    ...prev,
                                    networks: {
                                        ...prev.networks,
                                        enable_nac: e.target.value
                                    }
                                };
                            });
                        }}
                    >
                        <Radio value="enable">Enable</Radio>
                        <Radio value="disable">Disable</Radio>
                    </Radio.Group>
                </Form.Item>
                {isEnable && (
                    <Form.Item style={{marginBottom: "0px"}} label="Server" labelCol={{style: {width: 175}}}>
                        <a
                            style={{
                                border: "none",
                                borderRadius: "4px",
                                color: "#14c9bb"
                            }}
                            onClick={() => {
                                nacServerModalRef.current.showNetworkModal({mode: "create"});
                            }}
                        >
                            <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                            Add
                        </a>
                    </Form.Item>
                )}
            </Form>
            {isEnable && (
                <div>
                    <AmpConCustomTable
                        dataSource={serverData}
                        columns={columns}
                        style={tableStyle}
                        pagination={{
                            defaultPageSize: 10,
                            showSizeChanger: true,
                            hideOnSinglePage: serverData.length <= 10
                        }}
                    />
                </div>
            )}

            <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
        </>
    );
});
