import {Space, Form, message, Divider, Radio} from "antd";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {createColumnConfig, AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {VNIModal} from "../modal/vni_modal";

import Icon from "@ant-design/icons";

import {useState, useRef, useEffect, forwardRef, useImperativeHandle} from "react";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

export const VNIConfigurationForm = forwardRef(
    ({networkData, networks, setCampusFabricData, tableStyle, type}, ref) => {
        useEffect(() => {
            if (type) {
                setCampusFabricData(prev => {
                    return {
                        ...prev,
                        networks: {
                            ...prev.networks,
                            enable_vni: "enable"
                        }
                    };
                });
            }
        }, []);

        const [isVNIenable, setIsVNIenable] = useState(type ? true : networks.enable_vni === "enable");

        const vniModalRef = useRef(null);

        const columns = [
            {...createColumnConfig("VNI", "vni", "", "", "33%"), sorter: (a, b) => a.vni.localeCompare(b.vni)},

            {
                title: "Networks",
                dataIndex: "networks",
                width: "33%",
                render: (_, record) => {
                    return record.networks?.map(item => <div key={item}>{item}</div>);
                },
                filters: networkData.map(item => ({text: item.name, value: item.name})),
                onFilter: (value, record) => record.networks.includes(value)
            },
            // {
            //     title: "Route",
            //     dataIndex: "extra_routes_route",
            //     width: "20%",
            //     render: (_, record) => {
            //         return (
            //             <div>
            //                 {record.extra_routes?.map(item => (
            //                     <div key={item.route}>{item.route}</div>
            //                 ))}
            //             </div>
            //         );
            //     }
            // },
            // {
            //     title: "Via",
            //     dataIndex: "extra_routes_via",
            //     width: "20%",
            //     render: (_, record) => {
            //         return (
            //             <div>
            //                 {record.extra_routes?.map(item => (
            //                     <div key={item.via}>{item.via}</div>
            //                 ))}
            //             </div>
            //         );
            //     }
            // },

            {
                title: "Operation",
                fixed: "right",
                render: (_, record) => {
                    return (
                        <div>
                            <Space size="large" className="actionLink">
                                <a
                                    onClick={() => {
                                        vniModalRef.current.showVNIModal({mode: "edit"}, record);
                                    }}
                                >
                                    Edit
                                </a>
                                <a
                                    onClick={() => {
                                        confirmModalAction("Are you sure you want to delete the VNI?", () => {
                                            try {
                                                setVNI_data(VNI_data.filter(item => item.vni !== record.vni));
                                                message.success("VNI deleted successfully");
                                            } catch (error) {
                                                message.error("An error occurred while deleting the VNI");
                                            }
                                        });
                                    }}
                                >
                                    Delete
                                </a>
                            </Space>
                        </div>
                    );
                }
            }
        ];

        function to_frontend_structure(data) {
            return Object.keys(data).map(key => {
                return {
                    vni: key,
                    networks: data[key].network
                    // extra_routes: data[key].extra_routes
                };
            });
        }

        function to_backend_structure(data) {
            return data.map(item => {
                return {
                    [item.vni]: {
                        network: item.networks
                        // extra_routes: item.extra_routes
                    }
                };
            });
        }

        const [VNI_data, setVNI_data] = useState(to_frontend_structure(networks.vnis));
        useEffect(() => {
            const vnis = to_backend_structure(VNI_data.filter(item => item.networks.length > 0));
            const newVNIData = vnis.reduce((acc, cur) => {
                return {...acc, ...cur};
            }, {});
            setCampusFabricData(prev => {
                return {
                    ...prev,
                    networks: {
                        ...prev.networks,
                        vnis: newVNIData
                    }
                };
            });
        }, [VNI_data]);

        const [form] = Form.useForm();
        const tableRef = useRef(null);

        useImperativeHandle(ref, () => ({
            removeRelatedNetwork: networkName => {
                const newVNIData = VNI_data.map(item => {
                    return {
                        ...item,
                        networks: item.networks.filter(network => network !== networkName)
                    };
                }).filter(item => item.networks.length > 0);

                setVNI_data(newVNIData);
            },

            getConfiguration: () => {
                return {...VNI_data, vni_enable: isVNIenable};
            },
            validate: () => {
                if (isVNIenable && VNI_data.length === 0) {
                    Promise.reject(new Error("Please add VNI"));
                    message.error("Please add VNI");
                    return false;
                }
                return true;
            }
        }));

        return (
            <div>
                <h3>VNI</h3>
                <Form ref={ref} form={form} validateTrigger="onBlur" labelAlign="left" style={{width: 505}}>
                    <div style={{visibility: "hidden", height: "0px"}}>
                        <Form.Item
                            name="vni_enable"
                            label="Configuration"
                            labelCol={{style: {width: 175}}}
                            initialValue={isVNIenable}
                        >
                            <Radio.Group
                                disabled={type}
                                onChange={e => {
                                    if (!e.target.value) {
                                        setVNI_data([]);
                                    }
                                    setCampusFabricData(prev => {
                                        return {
                                            ...prev,
                                            networks: {
                                                ...prev.networks,
                                                enable_vni: e.target.value === true ? "enable" : "disable"
                                            }
                                        };
                                    });
                                    setIsVNIenable(e.target.value);
                                }}
                            >
                                <Radio value>Enable</Radio>
                                <Radio value={false}>Disable</Radio>
                            </Radio.Group>
                        </Form.Item>
                    </div>
                    {isVNIenable && (
                        <Form.Item style={{marginBottom: "0px"}} label="VNI" labelCol={{style: {width: 175}}}>
                            <a
                                style={{
                                    border: "none",
                                    borderRadius: "4px",
                                    color: "#14c9bb"
                                }}
                                onClick={() => {
                                    if (VNI_data.length >= 128) {
                                        message.error("You can only add up to 128 VNIs!");
                                        return;
                                    }
                                    vniModalRef.current.showVNIModal({mode: "create"});
                                }}
                            >
                                <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                                Add
                            </a>
                        </Form.Item>
                    )}
                </Form>
                {isVNIenable && (
                    <AmpConCustomTable
                        ref={tableRef}
                        pagination={{
                            defaultPageSize: 10,
                            showSizeChanger: true,
                            hideOnSinglePage: VNI_data.length <= 10
                        }}
                        dataSource={VNI_data}
                        columns={columns}
                        style={tableStyle}
                    />
                )}
                <VNIModal
                    ref={vniModalRef}
                    handleAddVNI={data => {
                        setVNI_data([...VNI_data, data]);
                    }}
                    handleEditVNI={data => {
                        setVNI_data(VNI_data.map(item => (item.vni === data.vni ? data : item)));
                    }}
                    networkData={networkData}
                    VNI_data={VNI_data}
                />
                <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
            </div>
        );
    }
);
