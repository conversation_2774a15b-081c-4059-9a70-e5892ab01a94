.goBack {
    width: 70px;
    font-weight: 500;
    font-size: 14px;
    color: #14C9BB;
    text-align: left;
    font-style: normal;
    text-transform: none;
    cursor: pointer;
}

.topology {
    width: calc(100% - 570px);
    min-width: 500px;
    min-height: 500px;
}

.fabricTopo {
    width: 100%;
    height: 380px;
    background: #F8FAFB;
    border-radius: 4px 4px 4px 4px;
    margin-bottom: 24px;
    overflow: hidden;
    position: relative;
}

.unitComponent [class*="ant-tabs-nav"] {
    background-color: transparent;
    margin: 0;
}

.unitComponent [class*="ant-tabs-nav"]::before {
    border: none !important;
}

.unitComponent :global(.ant-tabs-tab),
.unitComponent :global(.ant-tabs-tab-active) {
  padding: 8px 0 !important;
  background: #fff !important;
  border: none !important;
}

.unitComponent :global(.ant-tabs-tab-btn) {
  padding: initial !important; 
  background: initial !important;
}

.unitComponent [class*="ant-tabs-tabpane"] {
    padding-right: 0;
    padding-left: 0;
}

.unitComponent [class*="ant-tabs-tab"]+[class*="ant-tabs-tab"]:not([class*="ant-tabs-tabpane"]) {
    margin: 0 0 0 32px !important;
}

.unitComponent [class*="ant-tabs-ink-bar"] {
    top: auto !important;
}