import {forwardRef, useImperativeHandle, useState, useEffect, useRef} from "react";
import {
    AmpConCustomTable,
    createColumnConfig,
    createColumnConfigMultipleParams,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {message, Space, Tag, Tabs, Table} from "antd";
import styles from "@/modules-ampcon/pages/Topo/Topology/topo.module.scss";
import {
    fetchInterfaceInfo,
    fetchOTNPortInfo,
    fetchInterfaceNicInfo,
    fetchSwitchClientInfo
} from "@/modules-ampcon/apis/monitor_api";
import {convertToUTCString} from "@/utils/topo_layout_utils";
import CloseSvg from "./resource/portInfo_close.svg?react";

const DeviceDetailedBottomInformation = forwardRef(({containerHeight}, ref) => {
    const [isBottomInformationVisible, setBottomInformationVisible] = useState(false);
    const [formattedData, setFormattedData] = useState([]);
    const [nicFormattedData, setNicFormattedData] = useState([]);
    const [clientFormattedData, setClientFormattedData] = useState([]);
    const [height, setHeight] = useState("420");
    const [isDragging, setIsDragging] = useState(false);
    const dragRef = useRef(null);
    const [deviceType, setDeviceType] = useState(1);

    const startDrag = e => {
        e.preventDefault();
        setIsDragging(true);
        dragRef.current.initialY = e.clientY;
        dragRef.current.initialHeight = height;
    };

    const onDrag = e => {
        if (isDragging) {
            const newHeight = dragRef.current.initialHeight - (e.clientY - dragRef.current.initialY);
            setHeight(newHeight > 250 ? Math.min(newHeight, containerHeight * 0.75) : 250);
        }
    };

    const stopDrag = () => {
        setIsDragging(false);
    };

    useEffect(() => {
        const handleMouseMove = e => {
            if (isDragging) {
                onDrag(e);
            }
        };

        const handleMouseUp = () => {
            stopDrag();
        };

        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", handleMouseUp);

        return () => {
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mouseup", handleMouseUp);
        };
    }, [isDragging]);

    const extractParts = str => {
        const parts = str.split(/[/-]/);
        return {
            prefix: parts[0],
            numbers: parts.slice(1).map(num => parseInt(num, 10))
        };
    };

    const compareNumbers = (a, b) => {
        const aParts = extractParts(a);
        const bParts = extractParts(b);
        if (aParts.prefix !== bParts.prefix) {
            return aParts.prefix.localeCompare(bParts.prefix);
        }
        for (let i = 0; i < Math.min(aParts.numbers.length, bParts.numbers.length); i++) {
            if (aParts.numbers[i] !== bParts.numbers[i]) {
                return aParts.numbers[i] - bParts.numbers[i];
            }
        }
        return aParts.numbers.length - bParts.numbers.length;
    };

    const loadData = async (sn, date) => {
        const response = await fetchInterfaceInfo(sn, date);
        if (response.status !== 200) {
            message.error("Failed to fetch device port info");
            throw new Error("Failed to fetch device port info.");
        }
        const {data} = response;
        const formattedData = Object.keys(data).map(key => {
            const port = data[key];
            const port_speed =
                port?.ethernet_state?.negotiated_port_speed?.match(/SPEED_(.*)/)?.[1] ||
                port?.ethernet_state?.port_speed?.match(/SPEED_(.*)/)?.[1] ||
                "-";
            return {
                name: port?.config?.name || "-",
                oper_status: port?.state?.oper_status || "-",
                port_speed: port_speed === "2500MB" ? "2.5GB" : port_speed,
                in_octets: port?.state?.counters?.in_octets || 0,
                in_pkts: port?.state?.counters?.in_pkts || 0,
                in_discards: port?.state?.counters?.in_discards || 0,
                in_errors: port?.state?.counters?.in_errors || 0,
                out_octets: port?.state?.counters?.out_octets || 0,
                out_pkts: port?.state?.counters?.out_pkts || 0,
                out_discards: port?.state?.counters?.out_discards || 0,
                out_errors: port?.state?.counters?.out_errors || 0
            };
        });
        formattedData.sort((a, b) => compareNumbers(a.name, b.name));
        setFormattedData(formattedData);
    };

    const loadOtnData = async otnIp => {
        const response = await fetchOTNPortInfo(otnIp);
        if (response.status !== 200) {
            message.error("Failed to fetch device port info");
            throw new Error("Failed to fetch device port info.");
        }
        const {data} = response;
        const formattedData = data.sort((a, b) => a.slot_no - b.slot_no);
        setFormattedData(formattedData);
    };

    const loadNicData = async (sn, date) => {
        const response = await fetchInterfaceNicInfo(sn, date);
        if (response.status !== 200) {
            message.error("Failed to fetch device nic host info");
            throw new Error("Failed to fetch device nic host info.");
        }
        const {data} = response;
        setNicFormattedData(data);
    };

    const loadClientData = async (sn, date) => {
        const response = await fetchSwitchClientInfo(sn, date);
        if (response.status !== 200) {
            message.error("Failed to fetch device client info");
            throw new Error("Failed to fetch device client info.");
        }
        const {data} = response;
        setClientFormattedData(data);
    };

    useImperativeHandle(ref, () => ({
        showDeviceDetailedBottomInformation: async (sn, device_type, queryDate = null) => {
            setHeight("420");
            setDeviceType(device_type);
            setFormattedData([]);
            setBottomInformationVisible(true);
            if (device_type === 2) {
                await loadOtnData(sn);
            } else {
                await loadData(sn, queryDate);
                await loadNicData(sn, queryDate);
                await loadClientData(sn, queryDate);
            }
        },
        hideDeviceDetailedBottomInformation: () => {
            setBottomInformationVisible(false);
        }
    }));

    const tabItems =
        import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC"
            ? [
                  {
                      key: "port_info",
                      label: "Port Info",
                      children: <PortInfoTable height={height} deviceType={deviceType} formattedData={formattedData} />
                  },
                  {
                      key: "host_info",
                      label: "Host Info",
                      children: (
                          <HostInfoTable height={height} deviceType={deviceType} formattedData={nicFormattedData} />
                      )
                  }
              ]
            : [
                  {
                      key: "port_info",
                      label: "Port Info",
                      children: <PortInfoTable height={height} deviceType={deviceType} formattedData={formattedData} />
                  },
                  {
                      key: "client_info",
                      label: "Client Info",
                      children: (
                          <ClientInfoTable
                              height={height}
                              deviceType={deviceType}
                              formattedData={clientFormattedData}
                          />
                      )
                  }
              ];

    return isBottomInformationVisible ? (
        <div
            ref={ref}
            style={{
                position: "absolute",
                display: "block",
                padding: "5px",
                bottom: "0px",
                left: "0px",
                backgroundColor: "white",
                color: "black",
                borderRadius: "8px",
                pointerEvents: "true",
                zIndex: 1000,
                width: "99.9%",
                height: `${height}px`
            }}
        >
            <div
                ref={dragRef}
                onMouseDown={startDrag}
                style={{
                    height: "5px",
                    backgroundColor: "transparent",
                    cursor: "ns-resize"
                }}
            />
            <div>
                <h2
                    style={{
                        position: "sticky",
                        top: 0,
                        backgroundColor: "white",
                        zIndex: 1,
                        margin: "5px 10px 5px 10px",
                        height: "39px",
                        fontSize: "18px"
                    }}
                >
                    Device Port Info
                    <CloseSvg
                        className={styles.closeIcon}
                        style={{
                            position: "absolute",
                            right: "3px",
                            top: "6px",
                            cursor: "pointer",
                            width: "16px",
                            height: "16px"
                        }}
                        onClick={() => setBottomInformationVisible(false)}
                    />
                </h2>
                <div style={{borderBottom: "1px solid #e0e0e0", marginBottom: 12}} />
                <Tabs className="radioGroupTabs" items={tabItems} style={{marginLeft: 10}} />
            </div>
        </div>
    ) : null;
});

const PortInfoTable = ({height, deviceType, formattedData}) => {
    const [filteredData, setFilteredData] = useState([]);

    const handleTableChange = (pagination, filters, sorter) => {
        let updatedFilteredData = formattedData;
        if (filters.name && filters.name.length > 0) {
            const filterValue = String(filters.name[0]).toLowerCase();
            updatedFilteredData = formattedData.filter(item => item.name.toLowerCase().includes(filterValue));
        }
        setFilteredData(updatedFilteredData);
    };

    const tagStyle = operStatus => {
        if (operStatus === "UP") {
            return <Tag className={styles.UpStyle}>Up</Tag>;
        }
        if (operStatus === "DOWN") {
            return <Tag className={styles.DownStyle}>Down</Tag>;
        }
    };

    const createColumn = (title, dataIndex, key, sorter) => ({
        title,
        dataIndex,
        key,
        sorter,
        sortDirections: ["ascend", "descend"]
    });

    const columns = [
        {
            title: "Port Name",
            dataIndex: "name",
            ...createColumnConfig("Port Name", "name", TableFilterDropdown),
            sorter: false,
            sortDirections: []
        },
        {
            title: "Status",
            dataIndex: "oper_status",
            key: "oper_status",
            render: (_, record) => <Space>{tagStyle(record.oper_status)}</Space>,
            sorter: (a, b) => a.oper_status.localeCompare(b.oper_status)
        },
        {
            title: "Port Speed",
            dataIndex: "port_speed",
            key: "port_speed",
            sorter: (a, b) => a.port_speed - b.port_speed
        },
        createColumn("In Octets", "in_octets", "in_octets", (a, b) => a.in_octets - b.in_octets),
        createColumn("In Pkts", "in_pkts", "in_pkts", (a, b) => a.in_pkts - b.in_pkts),
        createColumn("In Discards", "in_discards", "in_discards", (a, b) => a.in_discards - b.in_discards),
        createColumn("In Errors", "in_errors", "in_errors", (a, b) => a.in_errors - b.in_errors),
        createColumn("Out Octets", "out_octets", "out_octets", (a, b) => a.out_octets - b.out_octets),
        createColumn("Out Pkts", "out_pkts", "out_pkts", (a, b) => a.out_pkts - b.out_pkts),
        createColumn("Out Discards", "out_discards", "out_discards", (a, b) => a.out_discards - b.out_discards),
        createColumn("Out Errors", "out_errors", "out_errors", (a, b) => a.out_errors - b.out_errors)
    ];

    const otnColumns = [
        {title: "Slot No", dataIndex: "slot_no", sorter: (a, b) => a.slot_no - b.slot_no},
        {title: "Empty", dataIndex: "empty", sorter: (a, b) => a.empty.localeCompare(b.empty)},
        {
            title: "Equipment Mismatch",
            dataIndex: "equipment_mismatch",
            sorter: (a, b) => a.equipment_mismatch.localeCompare(b.equipment_mismatch)
        },
        {title: "Card Name", dataIndex: "card_name", sorter: (a, b) => a.card_name.localeCompare(b.card_name)},
        {title: "Slot Note", dataIndex: "slot_note", sorter: (a, b) => a.slot_note.localeCompare(b.slot_note)}
    ];

    useEffect(() => {
        setFilteredData(formattedData);
    }, [deviceType, formattedData]);

    return (
        <Table
            style={{margin: "-10px 10px 0px 0px"}}
            columns={deviceType === 2 ? otnColumns : columns}
            dataSource={filteredData}
            onChange={handleTableChange}
            bordered
            // style={{margin: "10px"}}
            scroll={{y: height - 245}}
            sticky
        />
    );
};

const HostInfoTable = ({height, deviceType, formattedData}) => {
    const [filteredData, setFilteredData] = useState([]);

    const tagStyle = operStatus => {
        if (operStatus === "UP" || operStatus === "up") {
            return <Tag className={styles.UpStyle}>Up</Tag>;
        }
        if (operStatus === "DOWN" || operStatus === "down") {
            return <Tag className={styles.DownStyle}>Down</Tag>;
        }
    };

    const handleTableChange = (pagination, filters, sorter) => {
        let updatedFilteredData = formattedData;
        if (filters.name && filters.name.length > 0) {
            const filterValue = String(filters.name[0]).toLowerCase();
            updatedFilteredData = formattedData.filter(item => item.name.toLowerCase().includes(filterValue));
        }
        setFilteredData(updatedFilteredData);
    };

    const createColumn = (title, dataIndex, key, sorter, width) => ({
        title,
        dataIndex,
        key,
        sorter,
        sortDirections: ["ascend", "descend"],
        width
    });

    const getMaxDataLength = (data, field) => {
        if (!data || data.length === 0) return 100;
        const maxLength = Math.max(...data.map(item => (item[field] ? item[field].length : 0)));
        return maxLength * 12;
    };

    const columns = [
        {
            title: "Port Name",
            dataIndex: "interface",
            ...createColumnConfig("Port Name", "interface", TableFilterDropdown),
            sorter: false,
            sortDirections: []
        },
        {
            title: "Port State",
            dataIndex: "oper_status",
            key: "oper_status",
            render: (_, record) => <Space>{tagStyle(record.oper_status)}</Space>,
            sorter: (a, b) => a.oper_status.localeCompare(b.oper_status)
        },
        createColumn("MTU", "mtu", "mtu", (a, b) => a.mtu - b.mtu),
        createColumn("Vlan", "entry_vlan", "entry_vlan", (a, b) => a.entry_vlan.localeCompare(b.entry_vlan)),
        createColumn("Host IP", "instance", "instance", (a, b) => a.instance.localeCompare(b.instance)),
        createColumn(
            "NIC Name",
            "name",
            "name",
            (a, b) => a.name.localeCompare(b.name),
            getMaxDataLength(formattedData, "name")
        ),
        createColumn("Interface ", "device", "device", (a, b) => a.device.localeCompare(b.device)),
        {
            title: "Status",
            dataIndex: "operstate",
            key: "operstate",
            render: (_, record) => <Space>{tagStyle(record.operstate)}</Space>,
            sorter: (a, b) => a.operstate.localeCompare(b.operstate)
        },
        createColumn("MAC Address", "address", "address", (a, b) => a.address.localeCompare(b.address))
    ];

    useEffect(() => {
        setFilteredData(formattedData);
    }, [formattedData]);

    return (
        <Table
            style={{margin: "-10px 10px 0px 0px"}}
            columns={columns}
            dataSource={filteredData}
            onChange={handleTableChange}
            bordered
            // style={{margin: "10px"}}
            scroll={{y: height - 245}}
            sticky
        />
    );
};

const ClientInfoTable = ({height, deviceType, formattedData}) => {
    const [filteredData, setFilteredData] = useState([]);

    const tagStyle = state => {
        if (state === "online" || state === true) {
            return <Tag className={styles.UpStyle}>Online</Tag>;
        }
        return <Tag className={styles.DownStyle}>Offline</Tag>;
    };
    const handleTableChange = (pagination, filters, sorter) => {
        let updatedFilteredData = JSON.parse(JSON.stringify(formattedData));
        for (const key in filters) {
            if (filters[key] !== null) {
                const value = filters[key];
                updatedFilteredData = updatedFilteredData.filter(item => item[key].includes(value[0]));
            }
        }
        setFilteredData(updatedFilteredData);
    };

    useEffect(() => {
        setFilteredData(formattedData);
    }, [formattedData]);

    const columns = [
        {
            title: "Client Name",
            dataIndex: "client_name",
            ...createColumnConfig("Client Name", "client_name", TableFilterDropdown),
            width: "15%",
            sorter: (a, b) => a.client_name.localeCompare(b.client_name),
            render: (_, record) => <div style={{"text-wrap": "auto", whiteSpace: "normal"}}>{record.client_name}</div>
        },
        {
            title: "Status",
            dataIndex: "state",
            ...createColumnConfig("Status", "state", TableFilterDropdown),
            width: "10%",
            sorter: (a, b) => (a.state || "").toString().localeCompare((b.state || "").toString()),
            render: (_, record) => <Space>{tagStyle(record.state)}</Space>
        },
        {
            title: "MAC Address",
            dataIndex: "mac_address",
            ...createColumnConfig("MAC Address", "mac_address", TableFilterDropdown),
            width: "15%",
            sorter: (a, b) => a.mac_address.localeCompare(b.mac_address)
        },
        {
            title: "IP Address",
            dataIndex: "ip_address",
            ...createColumnConfig("IP Address", "ip_address", TableFilterDropdown),
            width: "10%",
            sorter: (a, b) => a.ip_address.localeCompare(b.ip_address)
        },
        {
            title: "Port",
            dataIndex: "port",
            ...createColumnConfig("Port", "port", TableFilterDropdown),
            width: "10%",
            sorter: (a, b) => (a.port || "").localeCompare(b.port || "")
        },
        {
            title: "Manufacturer",
            dataIndex: "manufacturer",
            width: "15%",
            render: (_, record) => <div style={{"text-wrap": "auto", whiteSpace: "normal"}}>{record.manufacturer}</div>
        },
        {
            title: "Terminal Type",
            dataIndex: "terminal_type",
            width: "15%",
            render: (_, record) => <div style={{"text-wrap": "auto", whiteSpace: "normal"}}>{record.terminal_type}</div>
        }
    ];

    useEffect(() => {
        setFilteredData(formattedData);
    }, [formattedData]);
    return (
        <Table
            columns={columns}
            dataSource={filteredData}
            onChange={handleTableChange}
            // style={{margin: "10px"}}
            scroll={{y: height - 245}}
            sticky
        />
    );
};

export default DeviceDetailedBottomInformation;
