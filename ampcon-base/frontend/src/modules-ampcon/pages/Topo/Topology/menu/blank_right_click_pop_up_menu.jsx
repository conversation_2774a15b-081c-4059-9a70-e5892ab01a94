import {usePopper} from "react-popper";
import {forwardRef, useImperativeHandle, useState} from "react";
import {Menu} from "@antv/x6-react-components";
import "@antv/x6-react-components/es/menu/style/index.css";

const BlankRightClickPopUpMenu = forwardRef(
    (
        {
            addDeviceCallback,
            saveTopoCallback,
            zoomInCallback,
            zoomResetCallback,
            zoomOutCallback,
            reloadCallback,
            cancelEditCallback,
            autoDiscoverCallback,
            topoHistoryAttrs,
            isEditMode,
            setIsEditMode,
            isTopoLegend,
            autolayoutAttrs,
            setIsTopoLegend,
            deleteCallback,
            canTreeLayout,
            currentTopoType
        },
        ref
    ) => {
        const [referenceElement, setReferenceElement] = useState(null);
        const [menuElement, setMenuElement] = useState(null);

        const {styles, attributes} = usePopper(referenceElement, menuElement, {
            placement: "right-start"
        });

        const MenuItem = Menu.Item;
        const {SubMenu} = Menu;
        const {Divider} = Menu;
        const [isBlankRightClickPopUpMenuVisible, setIsBlankRightClickPopUpMenuVisible] = useState(false);
        const [multiSelected, setmultiSelected] = useState(false);
        const [isSubMenuRenderLeft, setIsSubMenuRenderLeft] = useState(false);

        useImperativeHandle(ref, () => ({
            showBlankRightClickPopUpMenu: (selectedCounts, e) => {
                if (isEditMode) {
                    if (selectedCounts > 1) {
                        setmultiSelected(true);
                    }
                    setReferenceElement({
                        getBoundingClientRect: () => ({
                            width: 0,
                            height: 0,
                            top: e.clientY,
                            left: e.clientX,
                            right: e.clientX,
                            bottom: e.clientY
                        }),
                        contextElement: document.body
                    });
                    setIsBlankRightClickPopUpMenuVisible(true);
                }
                doPopUpPostAction();
            },
            hideBlankRightClickPopUpMenu: () => {
                setIsBlankRightClickPopUpMenuVisible(false);
                setmultiSelected(false);
            },
            isShowBlankRightClickPopUpMenu: () => {
                return isBlankRightClickPopUpMenuVisible;
            }
        }));

        const doPopUpPostAction = () => {
            setTimeout(() => {
                if (menuElement && isBlankRightClickPopUpMenuVisible) {
                    const topoElement = document.querySelector(".x6-graph-grid");
                    if (topoElement) {
                        const menuRect = menuElement.getBoundingClientRect();
                        const topoRect = topoElement.getBoundingClientRect();
                        const distanceFromRight = topoRect.right - menuRect.right;
                        if (distanceFromRight > 200) {
                            setIsSubMenuRenderLeft(false);
                        } else {
                            setIsSubMenuRenderLeft(true);
                        }
                    }
                }
            }, 50);
        };

        return isBlankRightClickPopUpMenuVisible && isEditMode ? (
            <div
                ref={setMenuElement}
                style={{
                    ...styles.popper,
                    zIndex: 1000,
                    overflow: "none"
                }}
                {...attributes.popper}
            >
                {multiSelected ? (
                    <Menu>
                        {currentTopoType === "topology" && (
                            <MenuItem
                                name="delete"
                                text="Delete"
                                hotkey="Delete"
                                onClick={() => {
                                    deleteCallback();
                                    setIsBlankRightClickPopUpMenuVisible(false);
                                }}
                            />
                        )}
                        <SubMenu text="Auto Layout" className={isSubMenuRenderLeft ? "sub-menu-render-left" : ""}>
                            <MenuItem
                                name="auto hierarchy layout"
                                text="Auto Hierarchy Layout"
                                onClick={() => {
                                    autolayoutAttrs.autoHierarchyLayout();
                                    setIsBlankRightClickPopUpMenuVisible(false);
                                }}
                                disabled={!autolayoutAttrs.isHierarchyLayoutValid() || !canTreeLayout}
                            />
                            <MenuItem
                                name="auto gird layout"
                                text="Auto Gird Layout"
                                onClick={() => {
                                    autolayoutAttrs.autoGirdLayout();
                                    setIsBlankRightClickPopUpMenuVisible(false);
                                }}
                                disabled={!autolayoutAttrs.hasNodes()}
                            />
                            <MenuItem
                                name="auto circular layout"
                                text="Auto Circular Layout"
                                onClick={() => {
                                    autolayoutAttrs.autoCircularLayout();
                                    setIsBlankRightClickPopUpMenuVisible(false);
                                }}
                                disabled={!autolayoutAttrs.hasNodes()}
                            />
                            <MenuItem
                                name="auto elliptical layout"
                                text="Auto Elliptical Layout"
                                onClick={() => {
                                    autolayoutAttrs.autoEllipticalLayout();
                                    setIsBlankRightClickPopUpMenuVisible(false);
                                }}
                                disabled={!autolayoutAttrs.hasNodes()}
                            />
                        </SubMenu>
                    </Menu>
                ) : (
                    <Menu>
                        <MenuItem
                            name="undo"
                            text="Undo"
                            hotkey="Ctrl + Z"
                            onClick={() => {
                                topoHistoryAttrs.undo();
                                setIsBlankRightClickPopUpMenuVisible(false);
                            }}
                            disabled={!topoHistoryAttrs.canUndo}
                        />
                        <MenuItem
                            name="redo"
                            text="Redo"
                            hotkey="Ctrl + Y"
                            onClick={() => {
                                topoHistoryAttrs.redo();
                                setIsBlankRightClickPopUpMenuVisible(false);
                            }}
                            disabled={!topoHistoryAttrs.canRedo}
                        />
                        <Divider />
                        <MenuItem
                            name="zoom in"
                            text="Zoom In"
                            onClick={() => {
                                zoomInCallback();
                                setIsBlankRightClickPopUpMenuVisible(false);
                            }}
                        />
                        <MenuItem
                            name="zoom reset"
                            text="Zoom Reset"
                            onClick={() => {
                                zoomResetCallback();
                                setIsBlankRightClickPopUpMenuVisible(false);
                            }}
                        />
                        <MenuItem
                            name="zoom out"
                            text="Zoom Out"
                            onClick={() => {
                                zoomOutCallback();
                                setIsBlankRightClickPopUpMenuVisible(false);
                            }}
                        />
                        {currentTopoType === "topology" && <Divider />}
                        {currentTopoType === "topology" && (
                            <MenuItem
                                name="add"
                                text="Add Device"
                                onClick={() => {
                                    addDeviceCallback();
                                    setIsBlankRightClickPopUpMenuVisible(false);
                                }}
                            />
                        )}
                        <Divider />
                        <MenuItem
                            name="exit edit"
                            text="Exit Edit"
                            hotkey="Ctrl + X"
                            onClick={async () => {
                                await cancelEditCallback();
                                setIsBlankRightClickPopUpMenuVisible(false);
                                setIsEditMode(false);
                            }}
                        />
                        <MenuItem
                            name="save"
                            text="Save"
                            hotkey="Ctrl + S"
                            onClick={async () => {
                                await saveTopoCallback();
                                setIsBlankRightClickPopUpMenuVisible(false);
                                setIsEditMode(false);
                            }}
                        />
                        <Divider />
                        <MenuItem
                            name="auto discover"
                            text="Auto Discover"
                            onClick={() => {
                                autoDiscoverCallback();
                                setIsBlankRightClickPopUpMenuVisible(false);
                            }}
                        />
                        <SubMenu text="Auto Layout" className={isSubMenuRenderLeft ? "sub-menu-render-left" : ""}>
                            <MenuItem
                                name="auto hierarchy layout"
                                text="Auto Hierarchy Layout"
                                onClick={() => {
                                    autolayoutAttrs.autoHierarchyLayout();
                                    setIsBlankRightClickPopUpMenuVisible(false);
                                }}
                                disabled={!autolayoutAttrs.isHierarchyLayoutValid() || !canTreeLayout}
                            />
                            <MenuItem
                                name="auto gird layout"
                                text="Auto Gird Layout"
                                onClick={() => {
                                    autolayoutAttrs.autoGirdLayout();
                                    setIsBlankRightClickPopUpMenuVisible(false);
                                }}
                                disabled={!autolayoutAttrs.hasNodes()}
                            />
                            <MenuItem
                                name="auto circular layout"
                                text="Auto Circular Layout"
                                onClick={() => {
                                    autolayoutAttrs.autoCircularLayout();
                                    setIsBlankRightClickPopUpMenuVisible(false);
                                }}
                                disabled={!autolayoutAttrs.hasNodes()}
                            />
                            <MenuItem
                                name="auto elliptical layout"
                                text="Auto Elliptical Layout"
                                onClick={() => {
                                    autolayoutAttrs.autoEllipticalLayout();
                                    setIsBlankRightClickPopUpMenuVisible(false);
                                }}
                                disabled={!autolayoutAttrs.hasNodes()}
                            />
                        </SubMenu>
                        <Divider />
                        <MenuItem
                            name="refresh"
                            text="Refresh"
                            onClick={() => {
                                reloadCallback();
                                setIsBlankRightClickPopUpMenuVisible(false);
                            }}
                        />
                        <Divider />
                        {!isTopoLegend && (
                            <MenuItem
                                name="show legend"
                                text="Show Legend"
                                onClick={() => {
                                    setIsTopoLegend(!isTopoLegend);
                                    setIsBlankRightClickPopUpMenuVisible(false);
                                }}
                            />
                        )}
                        {isTopoLegend && (
                            <MenuItem
                                name="hide legend"
                                text="Hide Legend"
                                onClick={() => {
                                    setIsTopoLegend(!isTopoLegend);
                                    setIsBlankRightClickPopUpMenuVisible(false);
                                }}
                            />
                        )}
                    </Menu>
                )}
            </div>
        ) : null;
    }
);

export default BlankRightClickPopUpMenu;
