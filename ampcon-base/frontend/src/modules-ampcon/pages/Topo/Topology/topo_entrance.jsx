import {But<PERSON>, Di<PERSON>r, Flex, Input, message, Space, Spin, Tooltip, Tree} from "antd";
import topoStyle from "@/modules-ampcon/pages/Topo/Topology/topo.module.scss";
import Icon, {DeleteOutlined, EditOutlined, HomeOutlined, PlusOutlined} from "@ant-design/icons";
import {searchSvg} from "@/utils/common/iconSvg";
import {useEffect, useRef, useState} from "react";
import {useSelector} from "react-redux";
import {delTopology, getTopologyList, setDefaultTopology} from "@/modules-ampcon/apis/monitor_api";
import AddTopoModal from "@/modules-ampcon/pages/Topo/Topology/modal/add_topo_modal";
import EditTopoModal from "@/modules-ampcon/pages/Topo/Topology/modal/edit_topo_modal";
import TopoLayout from "@/modules-ampcon/pages/Topo/Topology/topo_layout";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const getTitle = item => {
    return (
        <>
            {item.name}
            {item.isShowDefault && (
                <>
                    &nbsp;&nbsp;
                    <HomeOutlined />
                </>
            )}
        </>
    );
};

let initTreeData = null;
if (import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC") {
    initTreeData = [
        {
            title: "All",
            key: "all",
            children: [
                {title: "Fabric", key: "fabric"},
                {title: "Topology", key: "topology"}
            ]
        }
    ];
} else if (import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-CAMPUS") {
    initTreeData = [
        {
            title: "All",
            key: "all",
            children: [
                {title: "Site", key: "site"},
                {title: "Topology", key: "topology"}
            ]
        }
    ];
} else {
    initTreeData = [
        {
            title: "All",
            key: "all",
            children: [{title: "Topology", key: "topology"}]
        }
    ];
}

const TopoEntrance = () => {
    const addTopoModalRef = useRef(null);
    const editTopoModalRef = useRef(null);
    const treeRef = useRef(null);
    const topoLayoutRef = useRef(null);

    const [isShowSpin, setIsShowSpin] = useState(false);
    const [editButtonEnable, setEditButtonEnable] = useState(false);
    const [deleteButtonEnable, setDeleteButtonEnable] = useState(false);
    const [homeButtonEnable, setHomeButtonEnable] = useState(false);
    const [topoTreeData, setTopoTreeData] = useState(null);
    const [filteredTreeData, setFilteredTreeData] = useState(initTreeData);
    const [searchValue, setSearchValue] = useState("");
    const [selectedNode, setSelectedNode] = useState(null);
    const [treeHeight, setTreeHeight] = useState(window.innerHeight - 350);
    const [isScrollbarVisible, setIsScrollbarVisible] = useState(true);

    const currentUser = useSelector(state => state.user.userInfo);

    useEffect(() => {
        refreshTopoTree();
        window.addEventListener("resize", handleResize);
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    useEffect(() => {
        handleResize();
    }, [filteredTreeData]);

    useEffect(() => {
        selectedNodeChangeCallback(selectedNode);
    }, [selectedNode]);

    const handleResize = () => {
        setTreeHeight(window.innerHeight - 350);
        setTimeout(() => {
            setIsScrollbarVisible(document.querySelector(".ant-tree-list-holder").scrollHeight > treeHeight);
        }, 100);
    };

    const selectedNodeChangeCallback = node => {
        if (
            node === null ||
            node.key === "all" ||
            node.key === "site" ||
            node.key === "fabric" ||
            node.key === "topology"
        ) {
            setEditButtonEnable(false);
            setDeleteButtonEnable(false);
            setHomeButtonEnable(false);
            topoLayoutRef.current.cleanTopoLayout();
        } else {
            if (node.nodeType === "topology") {
                setEditButtonEnable(true);
                setDeleteButtonEnable(true);
            } else {
                setEditButtonEnable(false);
                setDeleteButtonEnable(false);
            }
            setHomeButtonEnable(true);
            topoLayoutRef.current.renderTopoLayout(node.key, node.title.props.children[0], node.nodeType);
        }
    };

    const refreshTopoTree = (type = "refresh", addTopoName = null) => {
        getTopologyList().then(response => {
            if (response.status !== 200) {
                message.error("Get topology list failed");
                return;
            }
            setSelectedNode(null);
            const topologyTreeData = [];
            const fabricTreeData = [];
            const siteTreeData = [];
            let defaultShowTopologyItem = null;
            response.data.topology.forEach(item => {
                const tempItem = {
                    title: getTitle(item),
                    key: item.id,
                    description: item.description,
                    isShowDefault: item.isShowDefault,
                    nodeType: "topology"
                };
                topologyTreeData.push(tempItem);
                if (item.isShowDefault === true) {
                    defaultShowTopologyItem = tempItem;
                }
            });
            response.data.fabric.forEach(item => {
                const tempItem = {
                    title: getTitle(item),
                    key: item.id,
                    description: item.description,
                    isShowDefault: item.isShowDefault,
                    nodeType: "fabric"
                };
                fabricTreeData.push(tempItem);
                if (item.isShowDefault === true) {
                    defaultShowTopologyItem = tempItem;
                }
            });
            response.data.site.forEach(item => {
                const tempItem = {
                    title: getTitle(item),
                    key: item.id,
                    description: item.description,
                    isShowDefault: item.isShowDefault,
                    nodeType: "site"
                };
                siteTreeData.push(tempItem);
                if (item.isShowDefault === true) {
                    defaultShowTopologyItem = tempItem;
                }
            });
            let initialTreeData = null;
            if (import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC") {
                initialTreeData = [
                    {
                        title: "All",
                        key: "all",
                        children: [
                            {title: "Fabric", key: "fabric", children: fabricTreeData},
                            {title: "Topology", key: "topology", children: topologyTreeData}
                        ]
                    }
                ];
            } else if (import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-CAMPUS") {
                initialTreeData = [
                    {
                        title: "All",
                        key: "all",
                        children: [
                            {title: "Site", key: "site", children: siteTreeData},
                            {title: "Topology", key: "topology", children: topologyTreeData}
                        ]
                    }
                ];
            } else if (import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-SUPER") {
                initialTreeData = [
                    {
                        title: "All",
                        key: "all",
                        children: [{title: "Topology", key: "topology", children: topologyTreeData}]
                    }
                ];
            }
            setTopoTreeData(initialTreeData);
            setFilteredTreeData(initialTreeData);
            setSearchValue("");
            if (type === "add") {
                const filterTree = (data, name) => {
                    return data
                        .map(item => {
                            if (item.children) {
                                const children = filterTree(item.children, name);
                                if (children.length) {
                                    return {...item, children};
                                }
                            }
                            if (item.title.props?.children[0] === name) {
                                return item;
                            }
                            return null;
                        })
                        .filter(item => item);
                };
                const item = filterTree(initialTreeData, addTopoName)[0]?.children[0]?.children[0];
                defaultShowTopologyItem = {
                    title: (
                        <>
                            {item.title.props.children[0]}
                            {item.isShowDefault && (
                                <>
                                    &nbsp;&nbsp;
                                    <HomeOutlined />
                                </>
                            )}
                        </>
                    ),
                    key: item.key,
                    description: item.description,
                    isShowDefault: item.isShowDefault,
                    nodeType: item.nodeType
                };
            }
            setSelectedNode(defaultShowTopologyItem);
        });
    };

    const onSearchChange = e => {
        const {value} = e.target;
        setSearchValue(value);
        if (!value) {
            setFilteredTreeData(topoTreeData);
            return;
        }
        const filterTree = data => {
            return data
                .map(item => {
                    if (item.children) {
                        const children = filterTree(item.children);
                        if (children.length) {
                            return {...item, children};
                        }
                    }
                    if (item.title.props?.children[0].toLowerCase().includes(value.toLowerCase())) {
                        return item;
                    }
                    return null;
                })
                .filter(item => item);
        };
        setFilteredTreeData(filterTree(topoTreeData));
    };

    const onSelectTreeNodeCallback = (selectedKeys, info) => {
        if (topoLayoutRef.current.getIsEditMode() === true) {
            confirmModalAction("The current topology is in edit mode, do you want to exit?", () => {
                if (selectedKeys.length === 0) {
                    setSelectedNode(null);
                } else {
                    setSelectedNode(info.node);
                }
            });
            return;
        }
        if (selectedKeys.length === 0) {
            setSelectedNode(null);
        } else {
            setSelectedNode(info.node);
        }
    };

    return (
        <>
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
            <AddTopoModal
                saveCallback={name => {
                    refreshTopoTree("add", name);
                }}
                ref={addTopoModalRef}
            />
            <EditTopoModal
                saveCallback={() => {
                    refreshTopoTree();
                }}
                ref={editTopoModalRef}
            />
            <Flex horizontal style={{flex: 1}}>
                <Flex vertical style={{width: "300px", marginRight: "15px"}} className={topoStyle.tile}>
                    <div>
                        <h2>Topologies List</h2>
                    </div>
                    <Divider style={{height: "0px", marginTop: "0px", marginBottom: "16px"}} />
                    <Space.Compact style={{width: "100%"}}>
                        <Input
                            placeholder="Search"
                            prefix={<Icon component={searchSvg} />}
                            allowClear
                            value={searchValue}
                            onChange={onSearchChange}
                            style={{float: "right", marginBottom: "24px", width: "280px"}}
                        />
                    </Space.Compact>

                    {currentUser.type !== "readonly" ? (
                        <Flex gap={10} style={{marginBottom: "20px"}}>
                            <Tooltip title="Add Topology">
                                <Button
                                    icon={<PlusOutlined />}
                                    className={topoStyle.treeButton}
                                    onClick={() => {
                                        addTopoModalRef.current.showAddTopoModal();
                                    }}
                                />
                            </Tooltip>
                            <Tooltip title="Edit Topology">
                                <Button
                                    icon={<EditOutlined />}
                                    className={topoStyle.treeButton}
                                    disabled={!editButtonEnable}
                                    onClick={() => {
                                        editTopoModalRef.current.showEditTopoModal(selectedNode);
                                    }}
                                />
                            </Tooltip>
                            <Tooltip title="Delete Topology">
                                <Button
                                    icon={<DeleteOutlined />}
                                    className={topoStyle.treeButton}
                                    disabled={!deleteButtonEnable}
                                    onClick={() => {
                                        confirmModalAction(
                                            `This action will delete topology:${selectedNode.title.props.children[0]}, Do you want to continue?`,
                                            () => {
                                                setIsShowSpin(true);
                                                try {
                                                    delTopology(selectedNode.key).then(res => {
                                                        if (res.status !== 200) {
                                                            message.error(res.info);
                                                        } else {
                                                            message.success(res.info);
                                                            refreshTopoTree();
                                                        }
                                                    });
                                                } catch (e) {
                                                    message.error("An error occurred during the process of delete");
                                                } finally {
                                                    setIsShowSpin(false);
                                                }
                                            }
                                        );
                                    }}
                                />
                            </Tooltip>
                            <Tooltip title="Set Default Topology">
                                <Button
                                    icon={<HomeOutlined />}
                                    className={topoStyle.treeButton}
                                    disabled={!homeButtonEnable}
                                    onClick={() => {
                                        try {
                                            const isShowDefault = !selectedNode.isShowDefault;
                                            setDefaultTopology({topologyId: selectedNode.key, isShowDefault}).then(
                                                res => {
                                                    if (res.status !== 200) {
                                                        message.error(res.info);
                                                    } else {
                                                        message.success(res.info);
                                                        refreshTopoTree();
                                                    }
                                                }
                                            );
                                        } catch (e) {
                                            message.error(
                                                "An error occurred during the process of set default topology"
                                            );
                                        }
                                    }}
                                />
                            </Tooltip>
                        </Flex>
                    ) : null}

                    <Tree
                        className={isScrollbarVisible ? topoStyle.treeContainer : topoStyle.treeNoScrollbar}
                        showLine
                        virtual={false}
                        treeData={filteredTreeData}
                        onSelect={onSelectTreeNodeCallback}
                        defaultExpandedKeys={["all", "site", "fabric", "topology"]}
                        height={isScrollbarVisible ? treeHeight : null}
                        selectedKeys={selectedNode ? [selectedNode.key] : []}
                        ref={treeRef}
                    />
                </Flex>
                <TopoLayout ref={topoLayoutRef} selectedNode={selectedNode} />
            </Flex>
        </>
    );
};

export default TopoEntrance;
