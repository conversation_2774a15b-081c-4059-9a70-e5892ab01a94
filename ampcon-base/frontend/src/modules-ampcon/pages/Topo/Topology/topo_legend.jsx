import React, {forwardRef, useImperativeHandle, useState} from "react";

const TopoLegend = forwardRef((props, ref) => {
    const [isTopoLegendVisible, setTopoLegendVisible] = useState(true);

    useImperativeHandle(ref, () => ({
        showTopoLegend: () => {
            setTopoLegendVisible(true);
        },
        hideTopoLegend: () => {
            setTopoLegendVisible(false);
        }
    }));

    return isTopoLegendVisible ? (
        <div
            ref={ref}
            style={{
                position: "absolute",
                top: "0px",
                right: "0px",
                zIndex: 1,
                display: "inline-block",
                backgroundColor: "rgba(255,255,255,0)",
                pointerEvents: "none",
                width: "220px",
                height: "62px"
            }}
        >
            <div
                style={{
                    position: "absolute",
                    top: "20px",
                    right: "148px",
                    width: "10px",
                    height: "10px",
                    backgroundColor: "#14C9BB"
                }}
            >
                {" "}
            </div>
            <div
                style={{
                    position: "absolute",
                    top: "16px",
                    right: "99px",
                    width: "41px",
                    height: "17px",
                    fontFamily: "Lato",
                    fontWeight: 400,
                    fontSize: "14px",
                    color: "#212519",
                    lineHeight: "17px",
                    textAlign: "left",
                    fontStyle: "normal",
                    textTransform: "none"
                }}
            >
                Online
            </div>
            <div
                style={{
                    position: "absolute",
                    top: "20px",
                    right: "75px",
                    width: "10px",
                    height: "10px",
                    backgroundColor: "#BEBFBF"
                }}
            >
                {" "}
            </div>
            <div
                style={{
                    position: "absolute",
                    top: "16px",
                    right: "24px",
                    width: "41px",
                    height: "17px",
                    fontFamily: "Lato",
                    fontWeight: 400,
                    fontSize: "14px",
                    color: "#212519",
                    lineHeight: "17px",
                    textAlign: "left",
                    fontStyle: "normal",
                    textTransform: "none"
                }}
            >
                Offline
            </div>
            <div
                style={{
                    position: "absolute",
                    top: "53px",
                    right: "199px",
                    width: "12px",
                    height: "2px",
                    backgroundColor: "#2BC174",
                    borderRadius: "205px 205px 205px 205px"
                }}
            >
                {" "}
            </div>
            <div
                style={{
                    position: "absolute",
                    top: "45px",
                    right: "173px",
                    width: "18px",
                    height: "17px",
                    fontFamily: "Lato",
                    fontWeight: 400,
                    fontSize: "14px",
                    color: "#212519",
                    lineHeight: "17px",
                    textAlign: "left",
                    fontStyle: "normal",
                    textTransform: "none"
                }}
            >
                Up
            </div>
            <div
                style={{
                    position: "absolute",
                    top: "53px",
                    right: "147px",
                    width: "12px",
                    height: "2px",
                    backgroundColor: "#F53F3F",
                    borderradius: "205px 205px 205px 205px"
                }}
            >
                {" "}
            </div>
            <div
                style={{
                    position: "absolute",
                    top: "45px",
                    right: "99px",
                    width: "38px",
                    height: "17px",
                    fontFamily: "Lato",
                    fontWeight: 400,
                    fontSize: "14px",
                    color: "#212519",
                    lineHeight: "17px",
                    textAlign: "left",
                    fontStyle: "normal",
                    textTransform: "none"
                }}
            >
                Down
            </div>
            <div
                style={{
                    position: "absolute",
                    top: "53px",
                    right: "73px",
                    width: "12px",
                    height: "2px",
                    backgroundColor: "#FFBB00",
                    borderRadius: "205px 205px 205px 205px"
                }}
            >
                {" "}
            </div>
            <div
                style={{
                    position: "absolute",
                    top: "45px",
                    right: "24px",
                    width: "41px",
                    height: "17px",
                    fontFamily: "Lato",
                    fontWeight: 400,
                    fontSize: "14px",
                    color: "#212519",
                    lineHeight: "17px",
                    textAlign: "left",
                    fontStyle: "normal",
                    textTransform: "none"
                }}
            >
                Mixed
            </div>
        </div>
    ) : null;
});

export default TopoLegend;
