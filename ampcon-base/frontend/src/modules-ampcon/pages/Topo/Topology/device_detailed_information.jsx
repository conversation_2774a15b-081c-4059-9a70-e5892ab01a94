import React, {forwardRef, useImperativeHandle, useState} from "react";
import {Flex} from "antd";
import styles from "@/modules-ampcon/pages/Topo/Topology/topo.module.scss";
import CloseSvg from "./resource/deviceInfo_close.svg?react";

const MLAGDeviceDetailedInformation = forwardRef((props, ref) => {
    const [isDeviceDetailedInformationVisible, setDeviceDetailedInformationVisible] = useState(false);
    const [deviceInfo, setDeviceInfo] = useState({});
    const [statusIcon, setStatusIcon] = useState(null);

    const getStatusIcon = value => {
        value = value?.toString();
        return (
            <svg
                style={{
                    width: "8px",
                    height: "8px",
                    borderRadius: "50%",
                    backgroundColor: value?.toLowerCase() === "online" ? "#14C9BB" : "#F53F3F",
                    marginRight: "4px"
                }}
            />
        );
    };

    useImperativeHandle(ref, () => ({
        showDeviceDetailedInformation: deviceInfo => {
            setDeviceDetailedInformationVisible(true);
            setStatusIcon(getStatusIcon(deviceInfo["Reachable Status"]));
            const {"Reachable Status": _, ...cleanedInfo} = deviceInfo;
            setDeviceInfo(cleanedInfo);
        },
        hideDeviceDetailedInformation: () => {
            setDeviceDetailedInformationVisible(false);
        }
    }));
    const handleCancel = () => {
        setDeviceDetailedInformationVisible(false);
    };

    return isDeviceDetailedInformationVisible ? (
        <div
            ref={ref}
            style={{
                position: "absolute",
                top: "79px",
                right: "24px",
                backgroundColor: "white",
                color: "black",
                zIndex: 1000,
                boxSizing: "border-box",
                display: "inline-block",
                boxShadow: "0px 1px 12px 1px #E6E8EA",
                borderRadius: " 4px 4px 4px 4px",
                fontFamily: "Lato"
            }}
        >
            <div
                style={{
                    position: "absolute",
                    top: "12px",
                    right: "16px",
                    cursor: "pointer"
                }}
                onClick={handleCancel}
            >
                <CloseSvg className={styles.closeIcon} />
            </div>
            <Flex style={{backgroundColor: "#F8FAFB ", width: "100%", height: "40px", top: 0}}>
                <div
                    style={{
                        marginTop: "0",
                        borderRadius: "4px 4px 0px 0px",
                        paddingLeft: 16,
                        paddingTop: 11,
                        paddingBottom: 12,
                        margin: 0,
                        fontSize: 14,
                        color: "#212519",
                        fontWeight: 600
                    }}
                >
                    Device Info
                </div>
            </Flex>
            <div style={{paddingTop: 15}}>
                <Flex>
                    <Flex vertical style={{textAlign: "Left", color: "#929A9E ", paddingLeft: 16, fontWeight: 400}}>
                        {Object.keys(deviceInfo).map((key, index) => (
                            <div style={{marginBottom: index === Object.keys(deviceInfo).length - 1 ? 16 : 12}}>
                                {key}
                            </div>
                        ))}
                    </Flex>

                    <Flex vertical style={{textAlign: "center", width: "10px"}}>
                        {Object.keys(deviceInfo).map((key, index) => (
                            <div style={{marginBottom: index === Object.keys(deviceInfo).length - 1 ? 16 : 12}} />
                        ))}
                    </Flex>
                    <Flex vertical style={{paddingRight: 17, left: 117}}>
                        {Object.keys(deviceInfo).map((key, index) => {
                            const value = deviceInfo[key];
                            return (
                                <div
                                    key={key}
                                    style={{
                                        display: "flex"
                                    }}
                                >
                                    {key === "Mgmt IP" && <div>{statusIcon}</div>}
                                    <div
                                        style={{
                                            marginBottom: index === Object.keys(deviceInfo).length - 1 ? 16 : 12,
                                            fontWeight: "bold"
                                        }}
                                    >
                                        {value === undefined || value === null ? <br /> : value}
                                    </div>
                                </div>
                            );
                        })}
                    </Flex>
                </Flex>
            </div>
        </div>
    ) : null;
});

export default MLAGDeviceDetailedInformation;
