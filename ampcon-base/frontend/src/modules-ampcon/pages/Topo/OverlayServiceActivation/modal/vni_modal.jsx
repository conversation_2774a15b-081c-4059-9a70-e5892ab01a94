import {Button, Form, message, Divider, Input, Modal, Select} from "antd";
import {useState, forwardRef, useImperativeHandle, useEffect} from "react";

const VniModal = forwardRef(({handleAddServer, handleEditServer, networkData, vniData}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [form] = Form.useForm();
    const [mode, setMode] = useState("");
    const [existingData, setExistingData] = useState([]);
    const [currentRecord, setCurrentRecord] = useState(null);
    const [title, setTitle] = useState("Create VNI");
    useImperativeHandle(ref, () => ({
        showVniModal: ({mode}, record) => {
            setMode(mode);
            if (mode === "edit") {
                setTitle("Edit");
                form.setFieldsValue(record);
                setCurrentRecord(record);
            } else {
                setCurrentRecord(null);
                setTitle("Create VNI");
                form.resetFields();
            }
            setIsShowModal(true);
        }
    }));
    useEffect(() => {
        if (isShowModal && vniData) {
            setExistingData(vniData);
        }
    }, [isShowModal, networkData, vniData]);
    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                mode === "view" ? null : (
                    <div>
                        <Divider style={{marginTop: 0, marginBottom: 20}} />
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                form.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                form.submit();
                            }}
                        >
                            Apply
                        </Button>
                    </div>
                )
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={form}
                style={{minHeight: "267.23px"}}
                ref={form}
                validateTrigger={["onBlur", "onSubmit"]}
                onFinish={() => {
                    try {
                        if (mode === "create") {
                            handleAddServer(form.getFieldsValue());
                            message.success("VNI created successfully");
                        }
                        if (mode === "edit") {
                            handleEditServer(form.getFieldsValue(), currentRecord);
                            message.success("VNI edited successfully");
                        }
                    } catch (error) {
                        message.error("An error occurred while processing the VNI");
                        console.error(error);
                    } finally {
                        setIsShowModal(false);
                        form.resetFields();
                    }
                }}
            >
                <Form.Item
                    name="id"
                    validateFirst
                    rules={[
                        {
                            required: true,
                            message: "Please input VNI"
                        },
                        {
                            validator: (_, value) => {
                                if (!value) return Promise.resolve();
                                if (!/^(0|[1-9]\d*)$/.test(value)) {
                                    return Promise.reject(new Error("VNI must be between 1 and 16777086"));
                                }
                                const num = Number(value);
                                if (num < 1 || num > 16777086) {
                                    return Promise.reject(new Error("VNI must be between 1 and 16777086"));
                                }
                                const conflict = existingData.some(item => {
                                    if (mode === "edit" && item.id === currentRecord?.id) return false;
                                    return Number(item.id) === num;
                                });
                                return conflict
                                    ? Promise.reject(new Error("This VNI is already used"))
                                    : Promise.resolve();
                            }
                        }
                    ]}
                    label="VNI"
                >
                    <Input style={{width: "280px"}} disabled={mode === "edit"} />
                </Form.Item>
                <Form.Item
                    name="network"
                    label="Network"
                    rules={[
                        {required: true, message: "Please select networks"},
                        {
                            validator: (_, value) => {
                                if (!value) return Promise.resolve();
                                const isUsed = existingData.some(item => {
                                    if (mode === "edit" && item.id === currentRecord?.id) return false;
                                    return Array.isArray(item.network) && item.network.includes(value);
                                });
                                return isUsed
                                    ? Promise.reject(new Error("This Network is already bound to another VNI"))
                                    : Promise.resolve();
                            }
                        }
                    ]}
                >
                    <Select style={{width: "280px"}}>
                        {networkData.map(item => {
                            const isUsed = existingData.some(vni => {
                                if (mode === "edit" && vni.id === currentRecord?.id) {
                                    return false;
                                }
                                return Array.isArray(vni.network) && vni.network.includes(item.name);
                            });

                            return (
                                <Select.Option key={item.name} value={item.name} disabled={isUsed}>
                                    {item.name}
                                </Select.Option>
                            );
                        })}
                    </Select>
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default VniModal;
