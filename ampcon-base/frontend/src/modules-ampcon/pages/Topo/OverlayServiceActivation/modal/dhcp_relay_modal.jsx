import {Button, Form, message, Divider, Select, Modal, Input} from "antd";
import {useState, forwardRef, useImperativeHandle} from "react";
import {formValidateRules} from "@/modules-ampcon/utils/util";

const DhcpRelayModal = forwardRef(({handleAddServer, handleEditServer, dhcpData, networkData}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [form] = Form.useForm();
    const [mode, setMode] = useState("");
    const [currentRecord, setCurrentRecord] = useState(null);
    const [existingEntries, setExistingEntries] = useState([]);
    const networkOptions =
        networkData.map(item => ({
            label: item.name,
            value: item.name,
            vlan_id: item.vlan_id
        })) || [];
    useImperativeHandle(ref, () => ({
        showNetworkModal: ({mode}, record) => {
            setMode(mode);
            if (mode === "edit") {
                form.setFieldsValue({
                    ...record,
                    dhcp_network: record.dhcp_network,
                    vlan_id: record.vlan_id
                });
                setExistingEntries(
                    dhcpData
                        .filter(
                            item =>
                                !(item.dhcp_network === record.dhcp_network && item.dhcp_server === record.dhcp_server)
                        )
                        .map(item => ({
                            dhcp_network: item.dhcp_network,
                            dhcp_server: item.dhcp_server
                        }))
                );
                setCurrentRecord(record);
            } else {
                setCurrentRecord(null);
                setExistingEntries(
                    dhcpData.map(item => ({
                        dhcp_network: item.dhcp_network,
                        dhcp_server: item.dhcp_server
                    })) || []
                );
                form.setFieldsValue({
                    dhcp_network: undefined,
                    vlan_id: ""
                });
            }
            setIsShowModal(true);
        }
    }));
    const handleNetworkChange = value => {
        const selected = networkOptions.find(opt => opt.value === value);
        if (selected) {
            form.setFieldsValue({
                vlan_id: selected.vlan_id
            });
        }
        if (form.getFieldValue("dhcp_server")) {
            form.validateFields(["dhcp_server"]);
        }
    };
    const filteredNetworkOptions = networkData
        .filter(item => {
            if (mode === "edit" && currentRecord?.dhcp_network === item.name) {
                return true;
            }
            return !dhcpData.some(dhcp => dhcp.dhcp_network === item.name);
        })
        .map(item => ({
            label: item.name,
            value: item.name,
            vlan_id: item.vlan_id
        }));
    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {mode === "create" ? "Create DHCP Relay" : "Edit"}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            form.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            form.submit();
                        }}
                    >
                        Apply
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={form}
                style={{minHeight: "267.23px"}}
                ref={form}
                validateTrigger={["onBlur", "onSubmit"]}
                onFinish={() => {
                    try {
                        if (mode === "create") {
                            handleAddServer(form.getFieldsValue());
                            message.success("Server created successfully");
                        }
                        if (mode === "edit") {
                            handleEditServer(form.getFieldsValue(), currentRecord);
                            message.success("Server edited successfully");
                        }
                    } catch (error) {
                        message.error("An error occurred while processing the server");
                        console.error(error);
                    } finally {
                        setIsShowModal(false);
                        form.resetFields();
                    }
                }}
            >
                <Form.Item
                    name="dhcp_network"
                    rules={[{required: true, message: "Please input server address"}]}
                    label="Network"
                >
                    <Select style={{width: "280px"}} options={filteredNetworkOptions} onChange={handleNetworkChange} />
                </Form.Item>
                <Form.Item name="vlan_id" validateFirst rules={[{required: true}]} label="VLAN ID" initialValue="">
                    <Input style={{width: "280px"}} disabled />
                </Form.Item>
                <Form.Item
                    name="dhcp_server"
                    validateFirst
                    rules={[{required: true, message: "Please input IP address"}, formValidateRules.ipv4()]}
                    label="DHCP Server"
                    initialValue=""
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default DhcpRelayModal;
