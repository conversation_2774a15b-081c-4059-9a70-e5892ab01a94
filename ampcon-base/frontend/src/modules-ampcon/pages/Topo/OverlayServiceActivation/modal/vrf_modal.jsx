import {Button, Form, message, Divider, Input, Modal, Checkbox} from "antd";
import {useState, forwardRef, useImperativeHandle, useEffect} from "react";

const VrfModal = forwardRef(({handleAddServer, handleEditServer, networkData, vrfData}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [form] = Form.useForm();
    const [mode, setMode] = useState("");
    const [existingData, setExistingData] = useState([]);
    const [currentRecord, setCurrentRecord] = useState(null);
    const [title, setTitle] = useState("Create VRF");
    useImperativeHandle(ref, () => ({
        showVrfModal: ({mode}, record) => {
            setMode(mode);
            if (mode === "edit") {
                setTitle("Edit");
                form.setFieldsValue(record);
                setCurrentRecord(record);
            } else {
                setCurrentRecord(null);
                setTitle("Create VRF");
            }
            setIsShowModal(true);
        }
    }));
    const validateNameUnique = (_, value) => {
        if (!value) return Promise.resolve();
        const isDuplicate = existingData.some(item => {
            if (mode === "edit" && item.id === currentRecord?.id) return false;
            return item.name === value;
        });

        return isDuplicate ? Promise.reject(new Error("Name already exists")) : Promise.resolve();
    };
    const EmptyStringValidator = msg => {
        return {
            validator: (_, value) => {
                if (value && /\s/.test(value)) {
                    return Promise.reject(new Error(msg));
                }
                return Promise.resolve();
            }
        };
    };
    useEffect(() => {
        if (isShowModal && vrfData) {
            setExistingData(vrfData);
        }
    }, [isShowModal, networkData, vrfData]);
    const isNetworkUsed = network => {
        const selectedNetworks = form.getFieldValue("network") || [];
        if (mode === "edit" && selectedNetworks.includes(network)) return false;
        return (vrfData || []).some(vrf => {
            const networks = vrf.network || [];
            return networks.includes(network);
        });
    };
    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                mode === "view" ? null : (
                    <div>
                        <Divider style={{marginTop: 0, marginBottom: 20}} />
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                form.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                form.submit();
                            }}
                        >
                            Apply
                        </Button>
                    </div>
                )
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={form}
                style={{minHeight: "267.23px"}}
                ref={form}
                validateTrigger={["onBlur", "onSubmit"]}
                onFinish={() => {
                    try {
                        if (mode === "create") {
                            handleAddServer(form.getFieldsValue());
                            message.success("Vrf created successfully");
                        }
                        if (mode === "edit") {
                            handleEditServer(form.getFieldsValue(), currentRecord);
                            message.success("Vrf edited successfully");
                        }
                    } catch (error) {
                        message.error("An error occurred while processing the Vrf");
                        console.error(error);
                    } finally {
                        setIsShowModal(false);
                        form.resetFields();
                    }
                }}
            >
                <Form.Item
                    name="name"
                    validateFirst
                    rules={[
                        {
                            required: true,
                            message: "Please input name"
                        },
                        {max: 15, message: "VRF name cannot exceed 15 characters!"},
                        {validator: validateNameUnique},
                        EmptyStringValidator("Name cannot contain spaces"),
                        {
                            validator: (_, value) => {
                                if (!value) return Promise.resolve();
                                const reservedNames = [
                                    "mgmt-vrf",
                                    "default",
                                    "eth0",
                                    "eth1",
                                    "gretap0",
                                    "erspan0",
                                    "sit0",
                                    "bridge0",
                                    "bridge",
                                    "pimreg",
                                    "ipmr-lo",
                                    "tun0",
                                    "lo"
                                ];
                                if (reservedNames.includes(value)) {
                                    return Promise.reject(
                                        new Error(
                                            "Keywords tun0, lo, mgmt-vrf, default, eth0, eth1, gretap0, bridge0, erspan0, bridge, sit0, pimreg, and ipmr-lo cannot be configured as VRF names."
                                        )
                                    );
                                }
                                if (/^vlan.*$/.test(value)) {
                                    return Promise.reject(
                                        new Error(
                                            "Keywords starting with vlan followed by any characters cannot be configured as VRF names."
                                        )
                                    );
                                }
                                const regex = /^(te\d+|ge\d+|eth\d+|ae\d+|xe\d+)(?:\.\d+)?$/;
                                if (regex.test(value)) {
                                    return Promise.reject(
                                        new Error(
                                            "Keywords starting with te, ge, eth, ae, and xe followed by numbers cannot be configured as VRF names."
                                        )
                                    );
                                }
                                const regexValidName = /^[A-Za-z0-9\-._@=#]{1,15}$/;
                                if (!regexValidName.test(value)) {
                                    return Promise.reject(
                                        new Error(
                                            "The VRF name must be a string of 1 to 15 characters. Only letters, digits, and special characters (- . _ @ = #) are allowed. Spaces are not allowed."
                                        )
                                    );
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    label="VRF Name"
                    initialValue=""
                >
                    <Input style={{width: "280px"}} disabled={mode === "edit"} />
                </Form.Item>
                <Form.Item
                    name="network"
                    label="Network"
                    initialValue={[]}
                    rules={[{required: true, message: "Please select networks"}]}
                >
                    <Checkbox.Group style={{width: "100%"}}>
                        {networkData?.map(item => (
                            <Checkbox
                                key={`${item.name}`}
                                value={`${item.name}`}
                                disabled={isNetworkUsed(`${item.name}`)}
                            >
                                {`${item.name}`}
                            </Checkbox>
                        ))}
                    </Checkbox.Group>
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default VrfModal;
