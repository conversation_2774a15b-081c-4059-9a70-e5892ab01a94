import {Form, Divider, Space, message} from "antd";
import {useState, useRef, useEffect, forwardRef, useImperativeHandle} from "react";
import Icon from "@ant-design/icons";
import {addGreenSvg, addGreySvg} from "@/utils/common/iconSvg";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import VrfModal from "@/modules-ampcon/pages/Topo/OverlayServiceActivation/modal/vrf_modal";
import style from "@/modules-ampcon/pages/Topo/OverlayServiceActivation/overlay.module.scss";

const VrfForm = forwardRef(({vrfData, networkData, tableStyle, setTopoData, disabled}, ref) => {
    const [form] = Form.useForm();
    const [dataSource, setDataSource] = useState([]);
    const vrfModalRef = useRef();
    useEffect(() => {
        setDataSource(vrfData || []);
    }, [vrfData]);

    useImperativeHandle(ref, () => ({
        validate: () => {
            if (dataSource.length === 0) {
                Promise.reject(new Error("Please add at least one vrf."));
                message.error("Please add at least one vrf.");
                return false;
            }
            return true;
        },
        resetFormData: vrfArray => {
            form.resetFields();
            setDataSource(vrfArray || []);
        }
    }));
    const columns = [
        {
            title: "VRF Name",
            dataIndex: "name",
            width: "30%",
            render: (_, record) => {
                return record.name;
            },
            sorter: (a, b) => a.name.localeCompare(b.name)
        },
        {
            title: "Networks",
            dataIndex: "network",
            width: "30%",
            render: (_, record) => {
                const ids = Array.isArray(record.network) ? record.network : [record.network];

                return <div style={{whiteSpace: "pre-line"}}>{ids.join("\n")}</div>;
            },
            sorter: (a, b) => {
                const aIds = Array.isArray(a.network) ? a.network.join(",") : a.network;
                const bIds = Array.isArray(b.network) ? b.network.join(",") : b.network;
                return aIds.localeCompare(bIds);
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                const isDefaultVlan = record.name.toLowerCase() === "default vlan";

                return (
                    <Space size="large" className="actionLink">
                        {isDefaultVlan ? (
                            <a
                                onClick={() => {
                                    vrfModalRef.current.showVrfModal({mode: "view"}, record);
                                }}
                            >
                                View
                            </a>
                        ) : (
                            <>
                                <a
                                    onClick={() => {
                                        vrfModalRef.current.showVrfModal({mode: "edit"}, record);
                                    }}
                                >
                                    Edit
                                </a>
                                <a
                                    onClick={() => {
                                        confirmModalAction("Are you sure want to delete?", () => {
                                            try {
                                                handleDeleteServer(record);
                                                message.success("Delete Successful");
                                            } catch (error) {
                                                console.error(error);
                                                message.error("Delete Failed");
                                            }
                                        });
                                    }}
                                >
                                    Delete
                                </a>
                            </>
                        )}
                    </Space>
                );
            }
        }
    ];
    const updateVrf = newData => {
        setTopoData(prev => ({
            ...prev,
            vrfs: newData
        }));
    };
    const handleAddServer = newData => {
        if (dataSource.length >= 128) {
            message.warning("You can add up to 128 VRFs.");
            return;
        }
        const updatedData = [...dataSource, newData];
        setDataSource(updatedData);
        updateVrf(updatedData);
    };
    const handleEditServer = (newData, currentRecord) => {
        const updatedData = dataSource.map(item => (item.name === currentRecord.name ? newData : item));
        setDataSource(updatedData);
        updateVrf(updatedData);
    };

    const handleDeleteServer = record => {
        const updatedData = dataSource.filter(item => item.name !== record.name);
        setDataSource(updatedData);
        updateVrf(updatedData);
    };
    return (
        <>
            <h3 className={style.title}>VRF</h3>
            <VrfModal
                ref={vrfModalRef}
                networkData={networkData}
                vrfData={vrfData}
                handleAddServer={handleAddServer}
                handleEditServer={handleEditServer}
            />
            <Form form={form} validateTrigger="onBlur" labelAlign="left" style={{width: 505, marginBottom: "-10px"}}>
                <Form.Item style={{marginBottom: "0px"}} labelCol={{style: {width: 175}}} required>
                    <a
                        style={{
                            display: "inline-flex",
                            alignItems: "center",
                            border: "none",
                            borderRadius: "4px",
                            color: disabled ? "#B3BBC8" : "#14c9bb",
                            pointerEvents: disabled ? "none" : "auto"
                        }}
                        onClick={() => {
                            vrfModalRef.current.showVrfModal({mode: "create"});
                        }}
                    >
                        <Icon component={disabled ? addGreySvg : addGreenSvg} style={{marginRight: "8px"}} />
                        VRF
                    </a>
                </Form.Item>
            </Form>
            {dataSource.length > 0 && (
                <div>
                    <AmpConCustomTable
                        dataSource={dataSource}
                        columns={columns}
                        style={tableStyle}
                        pagination={{
                            defaultPageSize: 10,
                            showSizeChanger: true,
                            hideOnSinglePage: dataSource.length <= 10
                        }}
                    />
                </div>
            )}
            <Divider style={{marginTop: "23px", marginBottom: "3px"}} />
        </>
    );
});
export default VrfForm;
