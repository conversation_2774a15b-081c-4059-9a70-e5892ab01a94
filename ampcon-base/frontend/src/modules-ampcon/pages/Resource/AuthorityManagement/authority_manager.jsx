import React, {useEffect, useState} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import LicenseAction from "@/modules-ampcon/pages/Resource/AuthorityManagement/DeviceLicenseManagement/LicenseAction/license_action";
import LicenseAudit from "@/modules-ampcon/pages/Resource/AuthorityManagement/DeviceLicenseManagement/LicenseAudit/license_audit";
import LicenseLocal from "@/modules-ampcon/pages/Resource/AuthorityManagement/DeviceLicenseManagement/LicenseLocal/license_local";
import {useSelector} from "react-redux";
import ProtectedRoute from "@/modules-ampcon/utils/util";

let items = [
    {
        key: "license_audit",
        label: "License Audit",
        children: <ProtectedRoute component={LicenseAudit} />
    },
    {
        key: "license_action",
        label: "License Action",
        children: <ProtectedRoute component={LicenseAction} />
    },
    {
        key: "local_license",
        label: "Local License",
        children: <ProtectedRoute component={LicenseLocal} />
    }
];

const SwitchManager = () => {
    const currentUser = useSelector(state => state.user.userInfo);
    items =
        currentUser.type === "readonly"
            ? items.filter(
                  item => item.key !== "license_audit" && item.key !== "license_action" && item.key !== "local_license"
              )
            : items;

    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();

    useEffect(() => {
        const currentPath = location.pathname;
        if (/(license_audit|license_action|local_license)$/.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(/(license_audit|license_action|local_license)$/)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (/(license_audit|license_action|local_license)$/.test(currentPath)) {
            const matchLength = currentPath.match(/(license_audit|license_action|local_license)$/)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        <div style={{display: "flex", flex: 1}}>
            <Tabs
                style={{flex: 1}}
                activeKey={currentActiveKey}
                items={items}
                onChange={onChange}
                destroyInactiveTabPane
            />
        </div>
    );
};

export default SwitchManager;
