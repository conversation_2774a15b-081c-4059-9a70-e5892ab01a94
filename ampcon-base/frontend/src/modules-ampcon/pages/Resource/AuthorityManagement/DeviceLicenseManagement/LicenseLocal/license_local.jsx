import {createColumnConfig, AmpConCustomTable, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {Button, Space, Tooltip, Modal, Form, Input, Divider, message} from "antd";
import React, {useRef, useState} from "react";
import {QuestionCircleOutlined} from "@ant-design/icons";
import {fetchLocalLicense, editLocalLicense, delLocalLicense} from "@/modules-ampcon/apis/lifecycle_api";
import Icon from "@ant-design/icons/lib/components/Icon";
import {plusAddSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const {TextArea} = Input;

const ActionButton = ({onClick}) => {
    return (
        <Space size={6}>
            <Button type="primary" icon={<Icon component={plusAddSvg} style={{height: 20}} />} onClick={onClick}>
                License
            </Button>
            <Tooltip
                title="When AmpCon cannot access license portal, this screen allows user to generate license key manually for the switch and add it here."
                placement="right"
            >
                <QuestionCircleOutlined className="questioncircle-color" />
            </Tooltip>
        </Space>
    );
};

const LicenseLocal = () => {
    const [useForm] = Form.useForm();
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const tableRef = useRef(null);
    const [title, setTitle] = useState("Edit License");
    const matchFieldsList = [
        {name: "sn_num", matchMode: "fuzzy"},
        {name: "create_time", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["sn_num"];

    const editLicense = record => {
        setIsEditModalOpen(true);
        setTitle("Edit License");
        useForm.setFieldValue("sn", record.sn_num);
        useForm.setFieldValue("license", record.local_lic);
    };

    const delLicense = async record => {
        const response = await delLocalLicense(record.sn_num);
        if (response.status === 200) {
            message.success(response.info);
            tableRef.current.refreshTable();
        } else {
            message.error("The license delete failed");
        }
    };

    const handleSubmit = async values => {
        const response = await editLocalLicense(values.sn, values.license);
        if (response.status === 200) {
            message.success(response.info);
            handleCloseModal();
            tableRef.current.refreshTable();
        } else {
            message.error(response.info);
        }
    };

    const handleCloseModal = () => {
        useForm.resetFields();
        setIsEditModalOpen(false);
    };

    const columns = [
        {...createColumnConfig("Switch SN", "sn_num", TableFilterDropdown), fixed: "left"},
        {...createColumnConfig("License", "local_lic", TableFilterDropdown), ellipsis: true, tooltip: true},
        createColumnConfig("Create Time", "create_time", TableFilterDropdown, "", "", "descend"),
        {
            title: "Operation",
            fixed: "right",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a onClick={() => editLicense(record)}>Edit</a>
                            <a
                                onClick={() =>
                                    confirmModalAction("Are you sure want to delete?", () => delLicense(record))
                                }
                            >
                                Delete
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    return (
        <div style={{marginBottom: "32px"}}>
            <AmpConCustomTable
                columns={columns}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={
                    <ActionButton
                        onClick={() => {
                            useForm.resetFields();
                            setTitle("Create License");
                            setIsEditModalOpen(true);
                        }}
                    />
                }
                fetchAPIInfo={fetchLocalLicense}
                ref={tableRef}
                // scroll={{x: "100vw"}}
            />
            <EditLicenseModal
                formInstance={useForm}
                isModalOpen={isEditModalOpen}
                onCancel={handleCloseModal}
                onSubmit={handleSubmit}
                title={title}
            />
        </div>
    );
};

const EditLicenseModal = ({formInstance, isModalOpen, onCancel, onSubmit, title}) => {
    return (
        <Modal
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            onOk={formInstance.submit}
            okText="Save"
            onCancel={onCancel}
            destroyOnClose
            className="ampcon-middle-modal"
            footer={[
                <Divider style={{marginTop: 0, marginBottom: 20}} />,
                <Button key="cancel" onClick={onCancel}>
                    Cancel
                </Button>,
                <Button key="ok" type="primary" onClick={formInstance.submit}>
                    Apply
                </Button>
            ]}
        >
            <Form
                layout="horizontal"
                form={formInstance}
                onFinish={onSubmit}
                labelCol={{span: 5}}
                // wrapperCol={{span: 18}}
                labelAlign="left"
                style={{minHeight: "268px"}}
            >
                <Form.Item
                    name="sn"
                    label="SN"
                    tooltip="Enter the Serial Number of the switch"
                    rules={[{required: true, message: "Please input SN"}]}
                    initialValue={formInstance.getFieldValue("sn") !== "" ? formInstance.getFieldValue("sn") : ""}
                >
                    <Input
                        style={{
                            width: "280px",
                            ...(title === "Edit License" ? {color: "#212529"} : {})
                        }}
                        disabled={formInstance.getFieldValue("sn")}
                        readOnly={title === "Edit License"}
                    />
                </Form.Item>
                <Form.Item
                    name="license"
                    label="License"
                    tooltip="Enter Local License details"
                    rules={[{required: true, message: "Please input local license!"}]}
                >
                    <TextArea rows={10} style={{width: "280px", height: "160px"}} />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default LicenseLocal;
