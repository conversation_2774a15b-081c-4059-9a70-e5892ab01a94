import {
    AmpConCustomModalForm,
    AmpConCustomModalTable,
    AmpConCustomTable,
    createColumnConfig,
    createColumnWithoutFilter,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {Button, Checkbox, Divider, Flex, Form, Input, message, Space, Tabs, Tree, Radio, Row, Col} from "antd";
import {forwardRef, memo, useEffect, useImperativeHandle, useRef, useState} from "react";
import {useForm} from "antd/es/form/Form";
import {
    createGroup,
    deleteGroup,
    editGroupTableData,
    fetchGroupInfo,
    fetchGroups,
    loadGroupSwitch,
    loadGroup,
    fetchHostInfo,
    editHostGroup,
    saveGroup,
    groupManagementData
} from "@/modules-ampcon/apis/lifecycle_api";
import Icon from "@ant-design/icons/lib/components/Icon";
import {
    deleteDisabledSvg,
    deleteSvg,
    exclamationSvg,
    offlineSvg,
    onlineSvg,
    plusAddDisableSvg,
    plusAddSvg,
    EditSvg
} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import groupManagerStyle from "./group_manager.module.scss";
import Typography from "antd/es/typography";
import {fetchUserGroupInfo} from "@/modules-ampcon/apis/user_api";

const {TextArea} = Input;

const roleMapping = {
    readonly: "Readonly",
    admin: "Operator",
    superadmin: "Admin",
    superuser: "SuperAdmin"
};

const GroupButton = forwardRef(({refreshGroup, deleteGroup}, ref) => {
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [isDeleteButtonEnable, setIsDeleteButtonEnable] = useState(false);

    useImperativeHandle(ref, () => ({
        enableDeleteButton: enable => setIsDeleteButtonEnable(enable)
    }));

    return (
        <div>
            <Space size="middle">
                <Button
                    type="primary"
                    block
                    icon={<Icon component={plusAddSvg} style={{height: 20}} />}
                    onClick={() => {
                        setIsCreateModalOpen(true);
                    }}
                >
                    Create
                </Button>
                <Button
                    htmlType="button"
                    block
                    disabled={!isDeleteButtonEnable}
                    icon={
                        isDeleteButtonEnable ? (
                            <Icon component={deleteSvg} style={{height: 20}} />
                        ) : (
                            <Icon component={deleteDisabledSvg} style={{height: 20}} />
                        )
                    }
                    onClick={() =>
                        confirmModalAction(
                            "Are you sure you want to delete the group? This action deletes all data related to the group permanently.",
                            () => deleteGroup()
                        )
                    }
                >
                    Delete
                </Button>
            </Space>
            <CreateGroupModal
                isModalOpen={isCreateModalOpen}
                onCancel={() => {
                    setIsCreateModalOpen(false);
                    refreshGroup();
                }}
            />
        </div>
    );
});

const GroupManagement = () => {
    const [selectedGroup, setSelectedGroup] = useState("All Switches");
    const [selectedGroupType, setSelectedGroupType] = useState("switch");

    const [groupInfo, setGroupInfo] = useState({});

    useEffect(() => {}, []);

    const fetchGroupClassData = async groupName => {
        if (groupName !== "All Switches") {
            const response = await fetchGroupInfo(groupName);
            setGroupInfo({
                action: response.data.action,
                audit: response.data.audit,
                retrieve_config: response.data.retrieve_config,
                selectAll: response.data.action && response.data.audit && response.data.retrieve_config
            });
        } else {
            setGroupInfo({});
        }
    };

    return (
        <div style={{display: "flex", flex: 1}}>
            <Flex
                gap="large"
                vertical
                style={{minWidth: "280px", width: "18%", marginRight: "15px"}}
                className={groupManagerStyle.tile}
            >
                <GroupList
                    selectedGroup={selectedGroup}
                    setSelectedGroup={setSelectedGroup}
                    onSelectGroup={groupName => {
                        fetchGroupClassData(groupName).then();
                    }}
                    selectedGroupType={selectedGroupType}
                    setSelectedGroupType={setSelectedGroupType}
                />
            </Flex>

            <GroupManageTabs
                selectedGroupType={selectedGroupType}
                groupName={selectedGroup}
                groupInfo={groupInfo}
                setGroupInfo={setGroupInfo}
            />
        </div>
    );
};

const GroupList = ({selectedGroup, setSelectedGroup, onSelectGroup, selectedGroupType, setSelectedGroupType}) => {
    // switch group
    const [selectedKeys, setSelectedKeys] = useState([0]);
    const [treeData, setTreeData] = useState([
        {
            title: "All Switches",
            key: 0
        }
    ]);

    // host group
    const [selectedHostKeys, setSelectedHostKeys] = useState([]);
    const [treeHostData, setTreeHostData] = useState([
        {
            title: "All Hosts",
            key: 0
        }
    ]);

    const [widthValue, setWidthValue] = useState(window.innerWidth < 2000 ? "180px" : "12vw");

    const groupButtonRef = useRef(null);

    const listGroupTree = async () => {
        const response = await fetchGroups();
        const treeChild = response.data.switch.map((item, index) => {
            return {title: item, key: index + 1};
        });
        const treeTempData = [
            {
                title: "All Switches",
                key: 0,
                children: treeChild
            }
        ];
        setTreeData(treeTempData);

        // host tree data
        const hostTreeChild = response.data.host.map((item, index) => {
            return {title: item, key: index + 1};
        });
        const hostTreeTempData = [
            {
                title: "All Hosts",
                key: 0,
                children: hostTreeChild
            }
        ];
        setTreeHostData(hostTreeTempData);
    };

    const handleDeleteGroup = async () => {
        if (selectedGroup && selectedGroup !== "All Switches" && selectedGroup !== "All Hosts") {
            const response = await deleteGroup(selectedGroup);
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                message.success(response.info);
                listGroupTree().then();
            }
        } else {
            message.error("Please select a Group");
        }
        if (selectedGroupType === "switch") {
            setSelectedGroup("All Switches");
            onSelectGroup("All Switches");
            setSelectedKeys([0]);
        } else if (selectedGroupType === "host") {
            setSelectedGroup("All Hosts");
            setSelectedHostKeys([0]);
        }
    };

    useEffect(() => {
        listGroupTree().then();
    }, []);

    useEffect(() => {
        function handleResize() {
            setWidthValue(window.innerWidth < 2000 ? "180px" : "12vw");
        }

        window.addEventListener("resize", handleResize);

        return () => window.removeEventListener("resize", handleResize);
    }, []);

    return (
        <Flex vertical>
            <Flex gap="middle" style={{marginBottom: "20px"}}>
                <GroupButton ref={groupButtonRef} refreshGroup={listGroupTree} deleteGroup={handleDeleteGroup} />
            </Flex>
            {/* Switch Group Tree */}
            <Tree
                showLine
                treeData={treeData}
                onSelect={(selectedKeys, info) => {
                    setSelectedGroupType("switch");
                    setSelectedHostKeys([]);

                    onSelectGroup(info.node.title);
                    setSelectedGroup(info.node.title);
                    setSelectedKeys(selectedKeys);
                    if (info.node.title === "All Switches") {
                        groupButtonRef.current.enableDeleteButton(false);
                    } else {
                        groupButtonRef.current.enableDeleteButton(true);
                    }
                }}
                defaultExpandedKeys={[0]}
                selectedKeys={selectedKeys}
                rootStyle={{fontSize: "16px"}}
                // eslint-disable-next-line react/no-unstable-nested-components
                titleRender={nodeData => (
                    <div
                        style={{
                            display: "block",
                            width: widthValue,
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap", // 一定要加，不然省略号不起作用
                            verticalAlign: "middle"
                        }}
                        title={
                            nodeData.title === "All Switches" && import.meta.env.VITE_APP_EXPORT_MODULE !== "AmpCon-DC"
                                ? "All"
                                : nodeData.title
                        } // 鼠标悬停时显示完整内容
                    >
                        {nodeData.title === "All Switches" && import.meta.env.VITE_APP_EXPORT_MODULE !== "AmpCon-DC"
                            ? "All"
                            : nodeData.title}
                    </div>
                )}
            />
            <div style={{margin: "12px 0 0 0"}} />
            {/* Host Group Tree */}
            {import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC" ? (
                <Tree
                    showLine
                    treeData={treeHostData}
                    onSelect={(selectedKeys, info) => {
                        setSelectedGroupType("host");
                        setSelectedKeys([]);

                        setSelectedGroup(info.node.title);
                        setSelectedHostKeys(selectedKeys);
                        if (info.node.title === "All Hosts") {
                            groupButtonRef.current.enableDeleteButton(false);
                        } else {
                            groupButtonRef.current.enableDeleteButton(true);
                        }
                    }}
                    defaultExpandedKeys={[0]}
                    selectedKeys={selectedHostKeys}
                    rootStyle={{fontSize: "16px"}}
                    // eslint-disable-next-line react/no-unstable-nested-components
                    titleRender={nodeData => (
                        <div
                            style={{
                                display: "block",
                                width: widthValue,
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap", // 一定要加，不然省略号不起作用
                                verticalAlign: "middle"
                            }}
                            title={nodeData.title} // 鼠标悬停时显示完整内容
                        >
                            {nodeData.title}
                        </div>
                    )}
                />
            ) : null}
        </Flex>
    );
};

const GroupManagementTable = ({groupName, groupInfo, setGroupInfo}) => {
    const tableRef = useRef(null);
    const modalTableRef = useRef(null);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [checkedValues, setCheckedValues] = useState([]);

    const checkboxOptions = [
        {label: <span style={{lineHeight: "32px"}}>Select All</span>, value: "selectAll"},
        {label: <span style={{lineHeight: "32px"}}>License Audit</span>, value: "audit"},
        {label: <span style={{lineHeight: "32px"}}>License Action</span>, value: "action"},
        {label: <span style={{lineHeight: "32px"}}>Retrieve Config</span>, value: "retrieve_config"}
    ];

    useEffect(() => {
        if (groupInfo && isEditModalOpen) {
            const initialValues = checkboxOptions.filter(option => groupInfo[option.value]).map(option => option.value);
            setCheckedValues(initialValues);
        }
    }, [isEditModalOpen]);

    const delSwitch = async record => {
        const response = await saveGroup(groupInfo, groupName, [record.sn], []);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
            tableRef.current.refreshTable();
        }
    };

    const moreColumn = [
        {
            title: "Operation",
            fixed: "right",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() =>
                                    confirmModalAction("Are you sure want to delete it?", () => delSwitch(record))
                                }
                            >
                                Delete
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const onSave = async () => {
        const allFalse = checkedValues.length === 0;
        if (allFalse) {
            message.error("Please select at least one group class");
            return;
        }
        setIsEditModalOpen(false);

        const actions = modalTableRef.current.getTableRef().current.getOperations();
        const actionData = modalTableRef.current.getTableRef().current.getOperationRowsMappings();
        const removedData = modalTableRef.current.getTableRef().current.getRemovedRow().tableRemovedRows;
        const removeSnList = [];
        const addSnList = [];

        for (const [id, action] of Object.entries(actions)) {
            if (action === "remove") {
                removeSnList.push(removedData.find(temp => temp.id === parseInt(id)).sn);
            } else if (action === "add") {
                addSnList.push(actionData[id].sn);
            }
        }

        const checkedGroupInfo = {
            action: checkedValues.indexOf("action") !== -1,
            audit: checkedValues.indexOf("audit") !== -1,
            retrieve_config: checkedValues.indexOf("retrieve_config") !== -1,
            selectAll:
                checkedValues.indexOf("action") !== -1 &&
                checkedValues.indexOf("audit") !== -1 &&
                checkedValues.indexOf("retrieve_config") !== -1
        };

        const response = await saveGroup(checkedGroupInfo, groupName, removeSnList, addSnList);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
            setGroupInfo(checkedGroupInfo);
            tableRef.current.refreshTable();
        }
    };

    const [matchFieldsList, searchFieldsList, columns] = InitSwitchTableParams({
        moreColumn: groupName && groupName !== "All Switches" ? moreColumn : []
    });

    const [matchFieldsListModal, searchFieldsListModal, columnsModal] = InitSwitchTableParams({
        moreColumn: []
    });

    const handleCheckboxChange = value => {
        let tempCheckValues = [];
        if (value.includes("selectAll") && !checkedValues.includes("selectAll")) {
            // 勾选select all
            tempCheckValues = checkboxOptions.map(option => option.value);
        } else if (!value.includes("selectAll") && checkedValues.includes("selectAll")) {
            // 取消select all
            tempCheckValues = [];
        } else if (!value.includes("selectAll") && value.length === 4) {
            tempCheckValues = checkboxOptions.map(option => option.value);
        } else {
            tempCheckValues = value.filter(value => value !== "selectAll");
        }

        setCheckedValues(tempCheckValues);
    };

    return (
        <>
            <Space>
                <Typography aria-level={2}>Group Class:</Typography>
                <Checkbox checked={groupInfo.audit} disabled={!groupInfo.audit} />
                <p>License Audit</p>
                <Checkbox checked={groupInfo.action} disabled={!groupInfo.action} />
                <p>License Action</p>
                <Checkbox checked={groupInfo.retrieve_config} disabled={!groupInfo.retrieve_config} />
                <p>Retrieve Config</p>
            </Space>
            <div style={{marginTop: "0.5%"}}>
                <AmpConCustomTable
                    ref={tableRef}
                    columns={columns}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    fetchAPIInfo={groupName !== "All Switches" ? loadGroupSwitch : groupManagementData}
                    fetchAPIParams={[groupName]}
                    extraButton={
                        <Button
                            type="primary"
                            icon={
                                groupName && groupName !== "All Switches" ? (
                                    <Icon component={EditSvg} style={{height: 20, color: "#ffffff"}} />
                                ) : (
                                    <Icon component={EditSvg} style={{height: 20, color: "#B3BBC8"}} />
                                )
                            }
                            onClick={() => {
                                setIsEditModalOpen(true);
                            }}
                            disabled={!(groupName && groupName !== "All Switches")}
                        >
                            Edit Group
                        </Button>
                    }
                />
            </div>
            <AmpConCustomModalTable
                ref={modalTableRef}
                title="Edit Group"
                modalClass="ampcon-max-modal"
                columns={columnsModal}
                matchFieldsList={matchFieldsListModal}
                searchFieldsList={searchFieldsListModal}
                selectModalOpen={isEditModalOpen}
                fetchAPIInfo={editGroupTableData}
                buttonProps={
                    <Checkbox.Group options={checkboxOptions} value={checkedValues} onChange={handleCheckboxChange} />
                }
                fetchAPIParams={[groupName]}
                onCancel={() => {
                    setIsEditModalOpen(false);
                }}
                rowSelection={{
                    selectedRowKeys: [],
                    selectedRows: [],
                    onChange: () => {}
                }}
                footer={
                    <div>
                        <Divider />
                        <Button
                            onClick={() => {
                                setIsEditModalOpen(false);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={onSave}>
                            Save
                        </Button>
                    </div>
                }
            />
        </>
    );
};

const GroupManagementHostTable = ({groupName}) => {
    const tableRef = useRef(null);
    const modalTableRef = useRef(null);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);

    const searchFieldsList = ["device_name", "ip", "device_user"];

    const matchFieldsList = [
        {name: "device_name", matchMode: "fuzzy"},
        {name: "ip", matchMode: "fuzzy"},
        {name: "device_user", matchMode: "fuzzy"}
    ];

    const delHost = async record => {
        const response = await editHostGroup(groupName, [record.device_name], []);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
            tableRef.current.refreshTable();
        }
    };

    const columns = [
        createColumnConfig("Sysname", "device_name", TableFilterDropdown),
        createColumnConfig("Device IP", "ip", TableFilterDropdown),
        createColumnConfig("Device User", "device_user", TableFilterDropdown),
        createColumnConfig("Create Time", "create_time", null)
    ];

    columns.forEach(col => {
        if (!Object.prototype.hasOwnProperty.call(col, "render")) {
            col.render = text => {
                if (!text) {
                    return <span>--</span>;
                }
                return <span>{text}</span>;
            };
        }
    });

    const moreColumn = [
        {
            title: "Operation",
            fixed: "right",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() =>
                                    confirmModalAction("Are you sure want to delete it?", () => delHost(record))
                                }
                            >
                                Delete
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const onSave = async () => {
        setIsEditModalOpen(false);

        const actions = modalTableRef.current.getTableRef().current.getOperations();
        const actionData = modalTableRef.current.getTableRef().current.getOperationRowsMappings();
        const removedData = modalTableRef.current.getTableRef().current.getRemovedRow().tableRemovedRows;
        const removeNameList = [];
        const addNameList = [];

        for (const [id, action] of Object.entries(actions)) {
            if (action === "remove") {
                removeNameList.push(removedData.find(temp => temp.id === parseInt(id)).device_name);
            } else if (action === "add") {
                addNameList.push(actionData[id].device_name);
            }
        }

        const response = await editHostGroup(groupName, removeNameList, addNameList);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
            tableRef.current.refreshTable();
        }
    };

    return (
        <>
            <div style={{marginTop: "0.5%"}}>
                <AmpConCustomTable
                    ref={tableRef}
                    columns={columns.concat(moreColumn)}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    fetchAPIInfo={groupName !== "All Hosts" ? loadGroup : fetchHostInfo}
                    fetchAPIParams={[groupName]}
                    extraButton={
                        <Button
                            type="primary"
                            icon={
                                groupName && groupName !== "All Hosts" ? (
                                    <Icon component={EditSvg} style={{height: 20, color: "#ffffff"}} />
                                ) : (
                                    <Icon component={EditSvg} style={{height: 20, color: "#B3BBC8"}} />
                                )
                            }
                            onClick={() => {
                                setIsEditModalOpen(true);
                            }}
                            disabled={!(groupName && groupName !== "All Hosts")}
                        >
                            Edit Group
                        </Button>
                    }
                />
            </div>
            <AmpConCustomModalTable
                ref={modalTableRef}
                title="Edit Group"
                modalClass="ampcon-max-modal"
                columns={columns}
                matchFieldsList={matchFieldsList}
                searchFieldsList={searchFieldsList}
                selectModalOpen={isEditModalOpen}
                fetchAPIInfo={fetchHostInfo}
                fetchAPIParams={[groupName]}
                onCancel={() => {
                    setIsEditModalOpen(false);
                }}
                rowSelection={{
                    selectedRowKeys: [],
                    selectedRows: [],
                    onChange: () => {}
                }}
                footer={
                    <div>
                        <Divider />
                        <Button
                            onClick={() => {
                                setIsEditModalOpen(false);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={onSave}>
                            Save
                        </Button>
                    </div>
                }
            />
        </>
    );
};

const GroupClassCheckbox = forwardRef(({onChange, groupInfo}, ref) => {
    const checkboxOptions = [
        {label: <span style={{lineHeight: "32px"}}>Select All</span>, value: "selectAll"},
        {label: <span style={{lineHeight: "32px"}}>License Audit</span>, value: "audit"},
        {label: <span style={{lineHeight: "32px"}}>License Action</span>, value: "action"},
        {label: <span style={{lineHeight: "32px"}}>Retrieve Config</span>, value: "retrieve_config"}
    ];
    const [checkedValues, setCheckedValues] = useState([]);

    useImperativeHandle(ref, () => ({
        getCheckedValues: () => {
            return checkedValues;
        }
    }));

    const handleCheckboxChange = value => {
        let tempCheckValues = [];
        if (value.includes("selectAll") && !checkedValues.includes("selectAll")) {
            // 勾选select all
            tempCheckValues = checkboxOptions.map(option => option.value);
        } else if (!value.includes("selectAll") && checkedValues.includes("selectAll")) {
            // 取消select all
            tempCheckValues = [];
        } else if (!value.includes("selectAll") && value.length === 4) {
            tempCheckValues = checkboxOptions.map(option => option.value);
        } else {
            tempCheckValues = value.filter(value => value !== "selectAll");
        }
        setCheckedValues(tempCheckValues);
        if (typeof onChange === "function") {
            onChange(tempCheckValues);
        }
    };

    useEffect(() => {
        if (groupInfo) {
            const initialValues = checkboxOptions.filter(option => groupInfo[option.value]).map(option => option.value);
            setCheckedValues(initialValues);
        }
    }, [groupInfo]);

    const renderCheckbox = option => (
        <label
            key={option.value}
            htmlFor={`checkbox-${option.value}`}
            className="group-checkbox-label"
            style={{display: "block", textAlign: "left"}}
        >
            <Checkbox
                id={`checkbox-${option.value}`}
                value={option.value}
                checked={checkedValues.includes(option.value)}
                onChange={e =>
                    handleCheckboxChange(
                        e.target.checked
                            ? [...checkedValues, option.value]
                            : checkedValues.filter(v => v !== option.value)
                    )
                }
                className="group-checkbox"
            >
                {option.label}
            </Checkbox>
        </label>
    );

    return (
        <div
            className="group-checkbox-container"
            style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start"
            }}
        >
            <Row gutter={[10, 10]} style={{rowGap: "2px"}}>
                {checkboxOptions.map((option, index) => (
                    <Col span={10} key={option.value} style={{width: "90px"}}>
                        {renderCheckbox(option, index)}
                    </Col>
                ))}
            </Row>
        </div>
    );
});

const CreateGroupModal = ({isModalOpen, onCancel}) => {
    const [form] = useForm();
    const [groupType, setGroupType] = useState("switch");

    const formItems = () => {
        return (
            <>
                {import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC" && (
                    <Form.Item name="groupType" label="Device Type" initialValue="switch">
                        <Radio.Group
                            value={groupType}
                            options={[
                                {
                                    value: "switch",
                                    label: "Switch"
                                },
                                {value: "host", label: "Host"}
                            ]}
                            onChange={e => {
                                setGroupType(e.target.value);
                            }}
                        />
                    </Form.Item>
                )}
                <Form.Item
                    name="groupName"
                    label="Group Name"
                    validateFirst
                    rules={[
                        {required: true, message: "Please input your group name!"},
                        {max: 32, message: "Enter a maximum of 32 characters"},
                        {
                            validator: (_, value) => {
                                if (value === "All Switches" || value === "All Hosts") {
                                    return Promise.reject(new Error("Please input a valid group name!"));
                                }
                                if (value.trim() !== value) {
                                    return Promise.reject(
                                        new Error("Group name should not have leading or trailing spaces.")
                                    );
                                }
                                if (value.includes(" ")) {
                                    return Promise.reject(new Error("Group name should not have internal spaces."));
                                }
                                if (!/^[\s\w:-]+$/.test(value)) {
                                    return Promise.reject(
                                        new Error(
                                            "Group name can only contain letters, numbers, underscores, hyphens and colons."
                                        )
                                    );
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                >
                    <Input placeholder="Group Name" style={{width: "280px"}} />
                </Form.Item>

                {groupType === "switch" && (
                    <Form.Item
                        name="groupClass"
                        label="Group Class"
                        rules={[{required: true, message: "Please choose group class!"}]}
                    >
                        <GroupClassCheckbox />
                    </Form.Item>
                )}
                <Form.Item
                    name="description"
                    label="Description"
                    rules={[{max: 256, message: "Enter a maximum of 256 characters"}]}
                >
                    <TextArea rows={5} style={{width: "280px"}} />
                </Form.Item>
            </>
        );
    };

    const onSubmit = async values => {
        const response = await createGroup(values.groupClass, values.groupName, values.description, values.groupType);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
            onCancelModal();
        }
    };

    const onCancelModal = () => {
        form.resetFields();
        setGroupType("switch");
        onCancel();
    };

    return (
        <AmpConCustomModalForm
            title="Create Group"
            isModalOpen={isModalOpen}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 6
                }
            }}
            CustomFormItems={formItems}
            onCancel={onCancelModal}
            onSubmit={onSubmit}
            modalClass="ampcon-middle-modal"
        />
    );
};

const InitSwitchTableParams = ({moreColumn = []}) => {
    const matchFieldsList = [
        {name: "host_name", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"},
        {name: "address", matchMode: "fuzzy"},
        {name: "version", matchMode: "fuzzy"},
        {name: "license_status", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["sn", "host_name", "mgt_ip", "platform_model"];

    const columns = [
        {...createColumnWithoutFilter("Switch SN", "sn"), fixed: "left"},
        {
            ...createColumnConfig("IP address", "mgt_ip", TableFilterDropdown),
            render: (_, record) => {
                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.mgt_ip}
                    </Space>
                );
            }
        },
        createColumnConfig("Host Name", "host_name", TableFilterDropdown),
        createColumnConfig("Deployed Location", "address", TableFilterDropdown),
        createColumnConfig("Version", "version", TableFilterDropdown),
        createColumnWithoutFilter("License Expiry", "license_expired", false),
        createColumnWithoutFilter("License Status", "license_status", false),
        createColumnConfig("Model", "platform_model", TableFilterDropdown),
        ...(moreColumn || [])
    ];

    columns.forEach(col => {
        if (!Object.prototype.hasOwnProperty.call(col, "render")) {
            col.render = text => {
                if (!text) {
                    return <span>--</span>;
                }
                return <span>{text}</span>;
            };
        }
    });
    const switchTableParams = [matchFieldsList, searchFieldsList, columns];

    return switchTableParams;
};

const GroupManagementUserTable = memo(({groupName, groupInfo, selectedGroupType}) => {
    const matchFieldsList = [
        {name: "name", matchMode: "fuzzy"},
        {name: "type", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["name"];

    const columns = [
        createColumnConfig("Group Username", "name", TableFilterDropdown),
        {
            ...createColumnWithoutFilter("Role", "type"),
            render: (text, record) => <Space>{roleMapping[record.type]}</Space>
        },
        {title: "Added On", dataIndex: "add_on"},
        {title: "Group Name", dataIndex: "group_name"}
    ];

    return (
        <Flex vertical>
            {selectedGroupType === "switch" && (
                <Space>
                    <Typography aria-level={2}>Group Class:</Typography>
                    <Checkbox checked={groupInfo.audit} disabled={!groupInfo.audit} />
                    <p>License Audit</p>
                    <Checkbox checked={groupInfo.action} disabled={!groupInfo.action} />
                    <p>License Action</p>
                    <Checkbox checked={groupInfo.retrieve_config} disabled={!groupInfo.retrieve_config} />
                    <p>Retrieve Config</p>
                </Space>
            )}
            <div style={{marginTop: "0.5%"}}>
                <AmpConCustomTable
                    columns={columns}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    fetchAPIInfo={fetchUserGroupInfo}
                    fetchAPIParams={groupName === "All Switches" || groupName === "All Hosts" ? ["All"] : [groupName]}
                />
            </div>
        </Flex>
    );
});

const GroupManageTabs = ({groupName, groupInfo, setGroupInfo, selectedGroupType}) => {
    const isInitialMount = useRef(true);
    const items =
        selectedGroupType === "switch"
            ? [
                  {
                      key: "switchView",
                      label: "Switch View",
                      children: (
                          <GroupManagementTable
                              groupName={groupName}
                              groupInfo={groupInfo}
                              setGroupInfo={setGroupInfo}
                          />
                      )
                  },
                  {
                      key: "userView",
                      label: "User View",
                      children: (
                          <GroupManagementUserTable
                              groupName={groupName}
                              groupInfo={groupInfo}
                              selectedGroupType={selectedGroupType}
                          />
                      )
                  }
              ]
            : [
                  {
                      key: "hostView",
                      label: "Host View",
                      children: <GroupManagementHostTable groupName={groupName} />
                  },
                  {
                      key: "userView",
                      label: "User View",
                      children: (
                          <GroupManagementUserTable
                              groupName={groupName}
                              groupInfo={groupInfo}
                              selectedGroupType={selectedGroupType}
                          />
                      )
                  }
              ];

    const fetchData = async () => {
        if (isInitialMount.current) {
            isInitialMount.current = false;
            return;
        }
        if (groupName !== "All Switches") {
            const response = await fetchGroupInfo(groupName);
            setGroupInfo({
                action: response.data.action,
                audit: response.data.audit,
                retrieve_config: response.data.retrieve_config,
                selectAll: response.data.action && response.data.audit && response.data.retrieve_config
            });
        } else {
            setGroupInfo({});
        }
    };

    return (
        <Tabs
            className="group-management-tabs"
            items={items}
            onChange={() => {
                fetchData().then(() => {});
            }}
        />
    );
};

export default GroupManagement;
