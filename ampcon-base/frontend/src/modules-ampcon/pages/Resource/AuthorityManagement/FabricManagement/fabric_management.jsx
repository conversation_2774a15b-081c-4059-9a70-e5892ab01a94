import {
    createColumnConfig,
    AmpConCustomTable,
    TableFilterDropdown,
    createColumnWithoutFilter,
    AmpConCustomModalForm,
    AmpConCustomModalTable
} from "@/modules-ampcon/components/custom_table";
import {Button, Divider, Space, Flex, Tree, Tabs, Form, Input, message} from "antd";
import {useState, useEffect, useRef, forwardRef, useImperativeHandle} from "react";
import {useForm} from "antd/es/form/Form";
import {
    getFabric,
    loadFabricSwitch,
    editFabricTableData,
    fabricManagementData,
    deleteFabric,
    createFabric,
    saveFabric
} from "@/modules-ampcon/apis/lifecycle_api";
import Icon from "@ant-design/icons/lib/components/Icon";
import {
    plusAddSvg,
    plusAddDisableSvg,
    deleteSvg,
    onlineSvg,
    offlineSvg,
    exclamationSvg,
    deleteDisabledSvg
} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import fabricManagerStyle from "./fabric_management.module.scss";

const {TextArea} = Input;

const FabricButton = forwardRef(({refreshFabric, deleteFabric}, ref) => {
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [isDeleteButtonEnable, setIsDeleteButtonEnable] = useState(false);

    useImperativeHandle(ref, () => ({
        enableDeleteButton: enable => setIsDeleteButtonEnable(enable)
    }));

    return (
        <div>
            <Space size="middle">
                <Button
                    type="primary"
                    block
                    icon={<Icon component={plusAddSvg} style={{height: 20}} />}
                    onClick={() => {
                        setIsCreateModalOpen(true);
                    }}
                >
                    Create
                </Button>
                <Button
                    htmlType="button"
                    block
                    disabled={!isDeleteButtonEnable}
                    icon={
                        isDeleteButtonEnable ? (
                            <Icon component={deleteSvg} style={{height: 20}} />
                        ) : (
                            <Icon component={deleteDisabledSvg} style={{height: 20}} />
                        )
                    }
                    onClick={() =>
                        confirmModalAction(
                            "Are you sure you want to delete the fabric? This action deletes all data related to the fabric permanently.",
                            () => deleteFabric()
                        )
                    }
                >
                    Delete
                </Button>
            </Space>
            <CreateFabricModal
                isModalOpen={isCreateModalOpen}
                onCancel={() => {
                    setIsCreateModalOpen(false);
                    refreshFabric();
                }}
            />
        </div>
    );
});

const FabricManagement = () => {
    const [selectedFabric, setSelectedFabric] = useState("All");
    const items = [
        {
            key: "",
            children: <FabricManagementTable fabricName={selectedFabric} />
        }
    ];

    return (
        <div style={{display: "flex", flex: 1}}>
            <Flex
                gap="large"
                vertical
                style={{minWidth: "280px", width: "18%", marginRight: "15px"}}
                className={fabricManagerStyle.tile}
            >
                <FabricList onSelectFabric={setSelectedFabric} />
            </Flex>
            <Tabs className="group-management-tabs" items={items} style={{marginTop: -31}} />
        </div>
    );
};

const FabricList = ({onSelectFabric}) => {
    const [selectedFabric, setSelectedFabric] = useState("");
    const [selectedKeys, setSelectedKeys] = useState([0]);
    const [treeData, setTreeData] = useState([
        {
            title: "All",
            key: 0
        }
    ]);
    const fabricButtonRef = useRef(null);

    const listFabricTree = async () => {
        const response = await getFabric();
        const treeChild = response?.data.map((item, index) => {
            const updatedNode = {title: item, key: index + 1};
            return updatedNode;
        });
        const treeTempData = [
            {
                title: "All",
                key: 0,
                children: treeChild
            }
        ];
        setTreeData(treeTempData);
    };

    const handleDeleteFabric = async () => {
        if (selectedFabric && selectedFabric !== "All") {
            const response = await deleteFabric(selectedFabric);
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                message.success(response.info);
                listFabricTree().then();
            }
        } else {
            message.error("Please select a fabric");
        }
        setSelectedFabric("All");
        onSelectFabric("All");
        setSelectedKeys([0]);
    };

    useEffect(() => {
        listFabricTree().then();
    }, []);

    return (
        <Flex vertical>
            <Flex gap="middle" style={{marginBottom: "20px"}}>
                <FabricButton ref={fabricButtonRef} refreshFabric={listFabricTree} deleteFabric={handleDeleteFabric} />
            </Flex>
            <Tree
                showLine
                treeData={treeData}
                onSelect={(selectedKeys, info) => {
                    onSelectFabric(info.node.title);
                    setSelectedFabric(info.node.title);
                    setSelectedKeys(selectedKeys);
                    if (info.node.title === "All" || info.node.title === "default") {
                        fabricButtonRef.current.enableDeleteButton(false);
                    } else {
                        fabricButtonRef.current.enableDeleteButton(true);
                    }
                }}
                defaultExpandedKeys={[0]}
                selectedKeys={selectedKeys}
                rootStyle={{fontSize: "16px"}}
            />
        </Flex>
    );
};

const FabricManagementTable = ({fabricName}) => {
    const tableRef = useRef(null);
    const modalTableRef = useRef(null);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);

    const delSwitch = async record => {
        const response = await saveFabric(fabricName, [record.sn], []);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
            tableRef.current.refreshTable();
        }
    };

    const moreColumn = [
        {
            title: "Operation",
            fixed: "right",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() =>
                                    confirmModalAction("Are you sure want to delete it?", () => delSwitch(record))
                                }
                            >
                                Delete
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const onSave = async () => {
        setIsEditModalOpen(false);

        const actions = modalTableRef.current.getTableRef().current.getOperations();
        const actionData = modalTableRef.current.getTableRef().current.getOperationRowsMappings();
        const removedData = modalTableRef.current.getTableRef().current.getRemovedRow().tableRemovedRows;
        const removeSnList = [];
        const addSnList = [];

        for (const [id, action] of Object.entries(actions)) {
            if (action === "remove") {
                removeSnList.push(removedData.find(temp => temp.id === parseInt(id)).sn);
            } else if (action === "add" && actionData[id]) {
                addSnList.push(actionData[id].sn);
            }
        }

        const response = await saveFabric(fabricName, removeSnList, addSnList);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
            tableRef.current.refreshTable();
        }
    };

    const [matchFieldsList, searchFieldsList, columns] = InitSwitchTableParams({
        moreColumn: fabricName && fabricName !== "All" ? moreColumn : []
    });

    const [matchFieldsListModal, searchFieldsListModal, columnsModal] = InitSwitchTableParams({
        moreColumn: []
    });

    return (
        <>
            <AmpConCustomModalTable
                ref={modalTableRef}
                title="Edit Fabric"
                modalClass="ampcon-max-modal"
                columns={columnsModal}
                matchFieldsList={matchFieldsListModal}
                searchFieldsList={searchFieldsListModal}
                selectModalOpen={isEditModalOpen}
                fetchAPIInfo={editFabricTableData}
                buttonProps={[]}
                fetchAPIParams={[fabricName]}
                onCancel={() => {
                    setIsEditModalOpen(false);
                }}
                rowSelection={{
                    selectedRowKeys: [],
                    selectedRows: [],
                    onChange: () => {}
                }}
                footer={
                    <div>
                        <Divider />
                        <Button
                            onClick={() => {
                                setIsEditModalOpen(false);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={onSave}>
                            Save
                        </Button>
                    </div>
                }
            />
            <div style={{marginTop: "0.5%"}}>
                <AmpConCustomTable
                    ref={tableRef}
                    columns={columns}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    fetchAPIInfo={fabricName !== "All" ? loadFabricSwitch : fabricManagementData}
                    fetchAPIParams={[fabricName]}
                    extraButton={
                        <Button
                            type="primary"
                            icon={
                                fabricName && fabricName !== "All" ? (
                                    <Icon component={plusAddSvg} style={{height: 20}} />
                                ) : (
                                    <Icon component={plusAddDisableSvg} style={{height: 20}} />
                                )
                            }
                            onClick={() => {
                                setIsEditModalOpen(true);
                            }}
                            disabled={!(fabricName && fabricName !== "All")}
                        >
                            Edit Fabric
                        </Button>
                    }
                />
            </div>
        </>
    );
};

const CreateFabricModal = ({isModalOpen, onCancel}) => {
    const [form] = useForm();

    const formItems = () => {
        return (
            <>
                <Form.Item
                    name="fabricName"
                    label="Fabric Name"
                    rules={[
                        {required: true, message: "Please input your fabric name!"},
                        {max: 32, message: "Enter a maximum of 32 characters"},
                        {
                            validator: (_, value) => {
                                if (value === "All") {
                                    return Promise.reject(new Error("Please input a valid fabric name!"));
                                }
                                if (value.trim() !== value) {
                                    return Promise.reject(
                                        new Error("Fabric name should not have leading or trailing spaces.")
                                    );
                                }
                                if (value.includes(" ")) {
                                    return Promise.reject(new Error("Fabric name should not have internal spaces."));
                                }
                                if (!/^[\s\w:-]+$/.test(value)) {
                                    return Promise.reject(
                                        new Error(
                                            "Fabric name can only contain letters, numbers, underscores, hyphens and colons."
                                        )
                                    );
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                >
                    <Input placeholder="Fabric Name" style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="description"
                    label="Description"
                    rules={[{max: 256, message: "Enter a maximum of 256 characters"}]}
                >
                    <TextArea rows={5} style={{width: "280px"}} />
                </Form.Item>
            </>
        );
    };

    const onSubmit = async values => {
        const response = await createFabric(values.fabricName, values.description);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
            onCancelModal();
        }
    };

    const onCancelModal = () => {
        form.resetFields();
        onCancel();
    };

    return (
        <AmpConCustomModalForm
            title="Create Fabric"
            isModalOpen={isModalOpen}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 6
                }
            }}
            CustomFormItems={formItems}
            onCancel={onCancelModal}
            onSubmit={onSubmit}
            modalClass="ampcon-middle-modal"
        />
    );
};

const InitSwitchTableParams = ({moreColumn = []}) => {
    const matchFieldsList = [
        {name: "host_name", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"},
        {name: "address", matchMode: "fuzzy"},
        {name: "version", matchMode: "fuzzy"},
        {name: "license_status", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["sn", "host_name", "mgt_ip", "platform_model"];

    const columns = [
        {...createColumnWithoutFilter("Switch SN", "sn"), fixed: "left"},
        {
            ...createColumnConfig("IP address", "mgt_ip", TableFilterDropdown),
            render: (_, record) => {
                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.mgt_ip}
                    </Space>
                );
            }
        },
        createColumnConfig("Host Name", "host_name", TableFilterDropdown),
        createColumnConfig("Deployed Location", "address", TableFilterDropdown),
        createColumnConfig("Version", "version", TableFilterDropdown),
        createColumnWithoutFilter("License Expiry", "license_expired"),
        createColumnConfig("License Status", "license_status", TableFilterDropdown),
        createColumnConfig("Platform Model", "platform_model", TableFilterDropdown),
        ...(moreColumn || [])
    ];

    const switchTableParams = [matchFieldsList, searchFieldsList, columns];

    return switchTableParams;
};

export default FabricManagement;
