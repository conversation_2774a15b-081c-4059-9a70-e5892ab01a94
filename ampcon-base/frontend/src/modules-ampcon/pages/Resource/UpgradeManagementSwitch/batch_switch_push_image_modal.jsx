import {Button, Descriptions, Divider, Flex, Form, Input, message, Modal} from "antd";
import {forwardRef, useImperativeHandle, useState} from "react";
import {batchPushImageSwitch} from "@/modules-ampcon/apis/rma_api";

const BatchSwitchPushImageModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showBatchSwitchPushImageModal: (useImageInfo, selectedSwitchListInfo) => {
            if (useImageInfo.length === 0) {
                return;
            }
            setUseImageInfo(useImageInfo[0]);
            setSelectedSwitchListInfo(selectedSwitchListInfo.tableSelectedRows);
            batchUpgradeForm.setFieldValue("usedImage", useImageInfo[0].image_name);
            setIsShowModal(true);
        },
        hideBatchSwitchPushImageModal: () => {
            setIsShowModal(false);
            resetModal();
        }
    }));

    const title = "Push Image Task";
    const usedImageLabel = "Use Image";
    const selectedSwitchLabel = "Switches for Push Image";

    const {saveCallback} = props;

    const [batchUpgradeForm] = Form.useForm();

    const [isShowModal, setIsShowModal] = useState(false);
    const [useImageInfo, setUseImageInfo] = useState({});
    const [selectedSwitchListInfo, setSelectedSwitchListInfo] = useState([]);

    const resetModal = () => {
        batchUpgradeForm.resetFields();
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                resetModal();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                return new Promise((resolve, reject) => {
                                    batchPushImageSwitch(
                                        useImageInfo.image_name,
                                        selectedSwitchListInfo.map(switchItem => switchItem.sn)
                                    )
                                        .then(response => {
                                            if (response.status !== 200) {
                                                message.error(response.info);
                                                reject();
                                            } else {
                                                message.success(response.info);
                                                resolve();
                                            }
                                        })
                                        .finally(() => {
                                            resolve();
                                            setIsShowModal(false);
                                            resetModal();
                                            saveCallback();
                                        });
                                });
                            }}
                        >
                            Push Image
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 7}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={batchUpgradeForm}
                style={{minHeight: "267.23px"}}
            >
                <Form.Item name="usedImage" label={usedImageLabel}>
                    <Input style={{backgroundColor: "#f0f0f0"}} readOnly />
                </Form.Item>
                <Form.Item label={selectedSwitchLabel}>
                    <Descriptions bordered size="small" column={1}>
                        {selectedSwitchListInfo.map(switchItem => (
                            <Descriptions.Item label={switchItem.host_name}>{switchItem.sn}</Descriptions.Item>
                        ))}
                    </Descriptions>
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default BatchSwitchPushImageModal;
