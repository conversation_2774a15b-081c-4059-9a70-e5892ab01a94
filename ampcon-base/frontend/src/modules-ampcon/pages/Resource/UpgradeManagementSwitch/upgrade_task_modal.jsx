import {Di<PERSON><PERSON>, Modal, <PERSON>, Flex, Tag, message, <PERSON><PERSON>, <PERSON><PERSON>} from "antd";
import React, {forwardRef, useImperativeHandle, useState, useRef, useEffect} from "react";
import {AmpConCustomTable, createColumnConfigMultipleParams} from "@/modules-ampcon/components/custom_table";
import Icon from "@ant-design/icons";
import {
    exclamationSvg,
    offlineSvg,
    onlineSvg,
    editWhiteSvg,
    editGreySvg,
    cancelUpgradeSvg,
    deleteSvg,
    noteSvg,
    cancelUpgradeGreySvg,
    deleteGreySvg
} from "@/utils/common/iconSvg";
import {
    cancelUpgradeTask,
    deleteUpgradeTask,
    fetchUpgradeTaskTableData,
    upgradeTaskExecuteNow
} from "@/modules-ampcon/apis/rma_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import TaskLogViewTextareaModal from "@/modules-ampcon/components/task_log_view_textarea_modal";
import UpgradeTaskEditModal from "@/modules-ampcon/pages/Resource/UpgradeManagementSwitch/upgrade_task_edit_modal";

const stateFontStyle = {
    fontFamily: "Lato,Lato",
    fontWeight: 400,
    fontSize: "14px",
    lineHeight: "17px",
    textAlign: "left",
    fontStyle: "normal",
    textTransform: "none"
};

const buttonFontStyle = {
    fontFamily: "Lato,Lato",
    fontWeight: 600,
    fontSize: "14px",
    lineHeight: "17px",
    textAlign: "left",
    fontStyle: "normal",
    textTransform: "none"
};

const UpgradeTaskModal = forwardRef((props, ref) => {
    const {refreshUpgradeSwitchTable} = props;

    const upgradeTaskTableRef = useRef();
    const taskLogModalRef = useRef();
    const taskEditModalRef = useRef();

    const [isShowModal, setIsShowModal] = useState(false);
    const taskLogViewTextareaModalRef = useRef(null);
    const [isNeedToRefreshUpgradeSwitchTable, setIsNeedToRefreshUpgradeSwitchTable] = useState(false);
    const [isEditAndCancelUpgradeDisabled, setIsEditAndCancelUpgradeDisabled] = useState(true);
    const [isDeleteDisabled, setIsDeleteDisabled] = useState(true);

    const title = "Upgrade Task";

    useImperativeHandle(ref, () => ({
        showUpgradeTaskModal: () => {
            setIsShowModal(true);
        }
    }));

    useEffect(() => {
        if (!isShowModal && isNeedToRefreshUpgradeSwitchTable) {
            refreshUpgradeSwitchTable();
        }
    }, [isShowModal]);

    const clearAndRefresh = () => {
        upgradeTaskTableRef.current.clearAndRefresh();
        setIsEditAndCancelUpgradeDisabled(true);
        setIsDeleteDisabled(true);
    };

    const handleCancelModal = () => {
        setIsShowModal(false);
        setIsEditAndCancelUpgradeDisabled(true);
        setIsDeleteDisabled(true);
        setIsNeedToRefreshUpgradeSwitchTable(true);
        upgradeTaskTableRef.current.clearSelectedRow();
    };

    const setAllButtonDisabled = () => {
        setIsEditAndCancelUpgradeDisabled(true);
        setIsDeleteDisabled(true);
    };

    const columns = [
        createColumnConfigMultipleParams({
            title: "Upgrade Time",
            dataIndex: "start_time",
            enableFilter: false,
            enableSorter: true,
            width: "16%"
        }),
        createColumnConfigMultipleParams({
            title: "Completion Time",
            dataIndex: "end_time",
            enableFilter: false,
            enableSorter: true,
            width: "16%"
        }),
        createColumnConfigMultipleParams({
            title: "Sysname",
            dataIndex: "host_name",
            enableFilter: false,
            enableSorter: false
        }),
        {
            ...createColumnConfigMultipleParams({
                title: "IP Address",
                enableFilter: false,
                enableSorter: false,
                width: "16%"
            }),
            render: (_, record) => {
                if (!record.mgt_ip) {
                    return null;
                }

                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.link_ip_addr ? `${record.mgt_ip}/${record.link_ip_addr}` : record.mgt_ip}
                    </Space>
                );
            }
        },
        createColumnConfigMultipleParams({
            title: "Switch SN",
            dataIndex: "switch_sn",
            enableFilter: false,
            enableSorter: true,
            width: "16%"
        }),
        {
            ...createColumnConfigMultipleParams({
                title: "State",
                enableSorter: false,
                enableFilter: false,
                width: "10%"
            }),
            render: (_, record) => {
                if (record.state === "scheduled") {
                    return (
                        <Tag
                            style={{
                                ...stateFontStyle,
                                color: "#367EFF",
                                backgroundColor: "rgba(54, 126, 255, 0.1)",
                                borderRadius: "2px 2px 2px 2px",
                                border: " 1px solid #367EFF"
                            }}
                        >
                            To be Executed
                        </Tag>
                    );
                }
                if (record.state === "running") {
                    return (
                        <Tag
                            style={{
                                ...stateFontStyle,
                                color: "#FFBB00",
                                backgroundColor: "rgba(255,187,0,0.1)",
                                borderRadius: "2px 2px 2px 2px",
                                border: "1px solid #FFBB00"
                            }}
                        >
                            Running
                        </Tag>
                    );
                }
                if (record.state === "failed") {
                    return (
                        <Tag
                            style={{
                                ...stateFontStyle,
                                color: "#F53F3F",
                                backgroundColor: "rgba(245,63,63,0.1)",
                                borderRadius: "2px 2px 2px 2px",
                                border: "1px solid #F53F3F"
                            }}
                        >
                            Failed
                        </Tag>
                    );
                }
                if (record.state === "success") {
                    return (
                        <Tag
                            style={{
                                ...stateFontStyle,
                                color: "#2BC174",
                                backgroundColor: "rgba(43, 193, 116, 0.1)",
                                borderRadius: "2px 2px 2px 2px",
                                border: "1px solid #2BC174"
                            }}
                        >
                            Success
                        </Tag>
                    );
                }
                if (record.state === "cancelled") {
                    return (
                        <Tag
                            style={{
                                ...stateFontStyle,
                                color: "#B3BBC8",
                                backgroundColor: "#F4F5F7",
                                borderRadius: "2px 2px 2px 2px",
                                border: "1px solid #DADCE1"
                            }}
                        >
                            Cancel Upgrade
                        </Tag>
                    );
                }
                return (
                    <Tag
                        style={{
                            ...stateFontStyle,
                            color: "#FF4D4F",
                            backgroundColor: "rgba(255, 77, 79, 0.1)",
                            borderRadius: "2px 2px 2px 2px",
                            border: "1px solid #FF4D4F"
                        }}
                    >
                        Unknown
                    </Tag>
                );
            }
        },
        {
            ...createColumnConfigMultipleParams({
                title: "Image Version",
                enableSorter: false,
                enableFilter: false,
                width: "16%"
            }),
            render: (_, record) => {
                return record.version && record.revision ? `${record.version}/${record.revision}` : "";
            }
        },
        {
            ...createColumnConfigMultipleParams({
                title: "Operation",
                enableSorter: false,
                enableFilter: false,
                width: "16%"
            }),
            render: (_, record) => {
                return (
                    <Flex style={{flexWrap: "nowrap", columnGap: "24px", rowGap: "5px"}} className="actionLink">
                        {record.state !== "scheduled" && record.state !== "running" ? (
                            <a
                                onClick={() => {
                                    const JobID = [record.id];
                                    confirmModalAction("Are you sure you want to delete the upgrade task?", () => {
                                        deleteUpgradeTask(JobID).then(res => {
                                            if (res.status === 200) {
                                                message.success("Delete Successful");
                                                clearAndRefresh();
                                            } else {
                                                message.error("Delete Failed");
                                            }
                                        });
                                    });
                                }}
                            >
                                Delete
                            </a>
                        ) : null}
                        {record.state === "scheduled" ? (
                            <a
                                onClick={() => {
                                    confirmModalAction("Are you sure want to execute upgrade task right now?", () => {
                                        upgradeTaskExecuteNow([record.id]).then(res => {
                                            if (res.status === 200) {
                                                message.success(res.info);
                                                setIsNeedToRefreshUpgradeSwitchTable(true);
                                                clearAndRefresh();
                                            } else {
                                                message.error(res.info);
                                            }
                                        });
                                    });
                                }}
                            >
                                Upgrade Now
                            </a>
                        ) : null}
                        <a
                            onClick={() => {
                                taskLogViewTextareaModalRef.current.showTaskLogViewTextareaModal({
                                    sn: record.switch_sn,
                                    jobId: record.id
                                });
                            }}
                        >
                            Log
                        </a>
                    </Flex>
                );
            }
        }
    ];

    return isShowModal ? (
        <Modal
            className="ampcon-max-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={handleCancelModal}
            footer={null}
        >
            <Alert
                message={
                    <div
                        style={{
                            textAlign: "left",
                            fontFamily: "Lato, Lato",
                            fontWeight: 600,
                            fontSize: "1rpx",
                            color: "#367EFF",
                            lineHeight: "1rpx",
                            fontStyle: "normal",
                            textTransform: "none",
                            marginBottom: "10px"
                        }}
                    >
                        Note:
                    </div>
                }
                description={
                    <div
                        style={{
                            textAlign: "left",
                            fontFamily: "Lato, Lato",
                            fontWeight: 600,
                            fontSize: "1rpx",
                            color: "#367EFF",
                            lineHeight: "1rpx",
                            fontStyle: "normal",
                            textTransform: "none"
                        }}
                    >
                        <div>
                            <span style={{display: "inline-block", marginRight: "10px", color: "#4A97FF"}}>·</span>
                            Edit: Applicable to tasks that have not been executed.
                        </div>
                        <div>
                            <span style={{display: "inline-block", marginRight: "10px", color: "#4A97FF"}}>·</span>
                            Cancel Upgrade: Applicable to tasks that have not been executed.
                        </div>
                        <div>
                            <span style={{display: "inline-block", marginRight: "10px", color: "#4A97FF"}}>·</span>
                            Delete: Applicable to tasks that have been canceled, successfully upgraded, or failed to
                            upgrade.
                        </div>
                    </div>
                }
                style={{
                    border: "none",
                    boxShadow: "none",
                    backgroundColor: "#F3F8FF",
                    paddingTop: "16px",
                    paddingLeft: "16px",
                    marginBottom: "32px"
                }}
                type="info"
                showIcon
                icon={<Icon component={noteSvg} style={{paddingTop: "4px"}} />}
                closable
            />
            <AmpConCustomTable
                ref={upgradeTaskTableRef}
                columns={columns}
                fetchAPIInfo={fetchUpgradeTaskTableData}
                isShowPagination
                rowSelection={{
                    selectedRowKeys: [],
                    selectedRows: [],
                    onChange: (_, selectedRows) => {
                        if (selectedRows.length === 0) {
                            setAllButtonDisabled();
                        } else {
                            setIsEditAndCancelUpgradeDisabled(!selectedRows.every(item => item.state === "scheduled"));
                            setIsDeleteDisabled(
                                !selectedRows.every(item => item.state !== "scheduled" && item.state !== "running")
                            );
                        }
                    }
                }}
                extraButton={
                    <>
                        <Button
                            type="primary"
                            onClick={() => {
                                const selectedRowsJobID = [];

                                upgradeTaskTableRef.current
                                    .getSelectedRow()
                                    .tableSelectedRows.forEach(row => selectedRowsJobID.push(row.id));

                                taskEditModalRef.current.showTaskEditModal(selectedRowsJobID);
                            }}
                            disabled={isEditAndCancelUpgradeDisabled}
                            icon={
                                isEditAndCancelUpgradeDisabled ? (
                                    <Icon component={editGreySvg} />
                                ) : (
                                    <Icon component={editWhiteSvg} />
                                )
                            }
                            style={buttonFontStyle}
                        >
                            Edit
                        </Button>
                        <Button
                            onClick={() => {
                                const selectedRowsJobID = [];

                                upgradeTaskTableRef.current
                                    .getSelectedRow()
                                    .tableSelectedRows.forEach(row => selectedRowsJobID.push(row.id));

                                confirmModalAction(
                                    `Are you sure want to cancel the upgrade task${selectedRowsJobID.length === 1 ? "?" : "s?"}`,
                                    () => {
                                        cancelUpgradeTask(selectedRowsJobID).then(res => {
                                            if (res.status === 200) {
                                                message.success(res.info);
                                                setIsNeedToRefreshUpgradeSwitchTable(true);
                                                clearAndRefresh();
                                            } else {
                                                message.error(res.info);
                                            }
                                        });
                                    }
                                );
                            }}
                            disabled={isEditAndCancelUpgradeDisabled}
                            icon={
                                isEditAndCancelUpgradeDisabled ? (
                                    <Icon component={cancelUpgradeGreySvg} />
                                ) : (
                                    <Icon component={cancelUpgradeSvg} />
                                )
                            }
                            style={buttonFontStyle}
                        >
                            Cancel Upgrade
                        </Button>
                        <Button
                            onClick={() => {
                                const selectedRowsJobID = [];

                                upgradeTaskTableRef.current
                                    .getSelectedRow()
                                    .tableSelectedRows.forEach(row => selectedRowsJobID.push(row.id));

                                confirmModalAction(
                                    `Are you sure want to delete the upgrade task${selectedRowsJobID.length === 1 ? "?" : "s?"}`,
                                    () => {
                                        deleteUpgradeTask(selectedRowsJobID).then(res => {
                                            if (res.status === 200) {
                                                message.success("Delete Successful");
                                                setIsNeedToRefreshUpgradeSwitchTable(true);
                                                clearAndRefresh();
                                            } else {
                                                message.error("Delete Failed");
                                            }
                                        });
                                    }
                                );
                            }}
                            disabled={isDeleteDisabled}
                            icon={
                                isDeleteDisabled ? <Icon component={deleteGreySvg} /> : <Icon component={deleteSvg} />
                            }
                            style={buttonFontStyle}
                        >
                            Delete
                        </Button>
                    </>
                }
            />
            <TaskLogViewTextareaModal ref={taskLogViewTextareaModalRef} />
            <UpgradeTaskEditModal ref={taskEditModalRef} clearAndRefresh={clearAndRefresh} />
        </Modal>
    ) : null;
});

export default UpgradeTaskModal;
