import {But<PERSON>, <PERSON><PERSON><PERSON>, Flex, Form, Modal, DatePicker, message, Alert} from "antd";
import Icon from "@ant-design/icons";
import {forwardRef, useImperativeHandle, useState} from "react";
import dayjs from "dayjs";
import {updateUpgradeTask, upgradeTaskExecuteNow} from "@/modules-ampcon/apis/rma_api";
import {noteSvg} from "@/utils/common/iconSvg";

const UpgradeTaskEditModal = forwardRef((props, ref) => {
    const {clearAndRefresh} = props;

    useImperativeHandle(ref, () => ({
        showTaskEditModal: jobIDs => {
            setJobIDs(jobIDs);
            setIsShowModal(true);
        },
        hideTaskEditModal: () => {
            resetModal();
        }
    }));

    const [isShowModal, setIsShowModal] = useState(false);
    const [upgradeDate, setUpgradeDate] = useState("");

    const [jobIDs, setJobIDs] = useState(null);

    const [upgradeTaskEditForm] = Form.useForm();

    const resetModal = () => {
        setIsShowModal(false);
        upgradeTaskEditForm.resetFields();
        setJobIDs(null);
        setUpgradeDate("");
    };

    const disabledDate = current => {
        return current && current.isBefore(dayjs().subtract(1, "day").startOf("day"));
    };

    const handleApplyUpgradeTime = async () => {
        try {
            await upgradeTaskEditForm.validateFields();
            if (upgradeDate !== "") {
                await updateUpgradeTask(jobIDs, upgradeDate).then(res => {
                    if (res.status === 200) {
                        message.success("Upgrade time updated successfully!");
                        resetModal();
                        clearAndRefresh();
                    } else {
                        message.error(res.info);
                    }
                });
            } else {
                upgradeTaskExecuteNow(jobIDs).then(res => {
                    if (res.status === 200) {
                        message.success(res.info);
                        resetModal();
                        clearAndRefresh();
                    } else {
                        message.error(res.info);
                    }
                });
            }
        } catch (error) {
            message.error("Upgrade Time is required!");
            return;
        }
    };

    const handleUpgradeTimeChange = (date, dateString) => {
        setUpgradeDate(dateString);
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <>
                    <div
                        style={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            fontFamily: "Lato, Lato",
                            fontWeight: 700,
                            fontSize: 20,
                            color: "#323333",
                            lineHeight: "24px",
                            textAlign: "left",
                            fontStyle: "normal",
                            textTransform: "none"
                        }}
                    >
                        Edit
                    </div>
                    <Divider style={{marginTop: 16, marginBottom: 8}} />
                </>
            }
            open={isShowModal}
            onCancel={resetModal}
            footer={
                <>
                    <Divider style={{marginTop: 0, marginBottom: 16}} />
                    <Button onClick={resetModal}>Cancel</Button>
                    <Button type="primary" onClick={handleApplyUpgradeTime}>
                        Apply
                    </Button>
                </>
            }
        >
            <Alert
                message={
                    <div
                        style={{
                            textAlign: "left",
                            fontFamily: "Lato, Lato",
                            fontSize: "12px",
                            color: "#367EFF",
                            lineHeight: "14px",
                            fontStyle: "normal",
                            textTransform: "none"
                        }}
                    >
                        <span style={{fontWeight: 700, marginRight: "4px"}}>Note:</span>
                        <span style={{fontWeight: 400}}>
                            If no upgrade time is selected, the upgrade will be executed immediately.
                        </span>
                    </div>
                }
                type="info"
                style={{
                    border: "none",
                    boxShadow: "none",
                    backgroundColor: "#F3F8FF"
                }}
                showIcon
                icon={<Icon component={noteSvg} />}
                closable
            />
            <br />
            <Flex vertical style={{flex: 1}}>
                <Form form={upgradeTaskEditForm}>
                    <Form.Item
                        name="upgradeTime"
                        label="Upgrade Time"
                        labelAlign="left"
                        labelCol={{span: 5}}
                        wrapperCol={{span: 10}}
                    >
                        <DatePicker
                            value={upgradeDate}
                            placeholder="Start Time"
                            style={{width: "100%"}}
                            disabledDate={disabledDate}
                            showTime={{format: "YYYY-MM-DD HH:mm"}}
                            showNow={false}
                            format="YYYY-MM-DD HH:mm"
                            onChange={handleUpgradeTimeChange}
                        />
                    </Form.Item>
                </Form>
            </Flex>
        </Modal>
    ) : null;
});

export default UpgradeTaskEditModal;
