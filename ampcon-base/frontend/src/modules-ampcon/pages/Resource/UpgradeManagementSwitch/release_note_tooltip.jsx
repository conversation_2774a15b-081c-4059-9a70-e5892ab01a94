import {Tooltip} from "antd";
import {TipsSvg} from "@/utils/common/iconSvg";
import {QuestionCircleOutlined} from "@ant-design/icons";
import Icon from "@ant-design/icons/lib/components/Icon";

const ReleaseNoteTooltip = ({version, new_features, fixed_issues, full_release_notes}) => {
    return (
        <Tooltip
            placement="right"
            overlayStyle={{maxWidth: 500}}
            title={
                <div style={{width: 480, paddingRight: 12, maxHeight: 400, overflowY: "auto"}}>
                    <div
                        style={{
                            fontWeight: 700,
                            marginBottom: 12
                        }}
                    >
                        PicOS {version} Release Notes
                    </div>
                    {new_features.length > 0 && (
                        <div style={{marginBottom: 12}}>
                            <div
                                style={{
                                    fontWeight: 400,
                                    marginBottom: 8
                                }}
                            >
                                New Features and Changes:
                            </div>
                            <ul
                                style={{
                                    paddingLeft: 32,
                                    listStyle: "disc"
                                }}
                            >
                                {new_features.map((feat, i) => (
                                    <li
                                        key={i}
                                        style={{
                                            lineHeight: 1.6
                                        }}
                                    >
                                        {feat}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    )}
                    {fixed_issues.length > 0 && (
                        <div style={{marginBottom: 12}}>
                            <div
                                style={{
                                    fontWeight: 400,
                                    marginBottom: 8
                                }}
                            >
                                Fixed Issues:
                            </div>
                            <ul
                                style={{
                                    paddingLeft: 32,
                                    listStyle: "disc"
                                }}
                            >
                                {fixed_issues.map((issue, i) => (
                                    <li
                                        key={i}
                                        style={{
                                            lineHeight: 1.6
                                        }}
                                    >
                                        {issue}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    )}
                    {full_release_notes && (
                        <div style={{marginTop: 8}}>
                            <div
                                style={{
                                    fontWeight: 400,
                                    marginBottom: 8
                                }}
                            >
                                Full Release Notes:
                            </div>
                            <a
                                href={full_release_notes}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{
                                    color: "#14C9BB",
                                    wordBreak: "break-all",
                                    textDecoration: "underline"
                                }}
                            >
                                {full_release_notes}
                            </a>
                        </div>
                    )}
                </div>
            }
        >
            <span
                style={{
                    display: "inline-flex",
                    alignItems: "center",
                    cursor: "pointer"
                }}
            >
                <QuestionCircleOutlined style={{color: "#8C8C8C"}} />
            </span>
        </Tooltip>
    );
};
export default ReleaseNoteTooltip;
