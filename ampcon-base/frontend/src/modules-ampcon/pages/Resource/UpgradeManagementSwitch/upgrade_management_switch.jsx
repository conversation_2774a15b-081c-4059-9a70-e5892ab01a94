import React, {useEffect, useState} from "react";
import {Tabs, Breadcrumb} from "antd";
import {useSelector} from "react-redux";
import {useLocation, useNavigate} from "react-router-dom";
import ProtectedRoute from "@/modules-ampcon/utils/util";
import APUpgrade from "@/modules-ampcon/pages/Resource/UpgradeManagementSwitch/AP/ap_upgrade";
import SwitchUpgrade from "@/modules-ampcon/pages/Resource/UpgradeManagementSwitch/Switch/switch_upgrade";

const UpgradeManagementSwitch = () => {
    const currentUser = useSelector(state => state.user.userInfo);
    const location = useLocation();
    const navigate = useNavigate();
    const [currentActiveKey, setCurrentActiveKey] = useState("");

    const allItems =
        import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-SMB"
            ? [
                  {
                      key: "ap",
                      label: "AP",
                      children: <ProtectedRoute component={APUpgrade} />
                  }
              ]
            : [
                  {
                      key: "ap",
                      label: "AP",
                      children: <ProtectedRoute component={APUpgrade} />
                  },
                  {
                      key: "switch",
                      label: "Switch",
                      children: <ProtectedRoute component={SwitchUpgrade} />
                  }
              ];

    const items =
        currentUser.type === "readonly"
            ? [] // 只读用户不显示任何 tab
            : allItems;

    useEffect(() => {
        const match = location.pathname.match(/(switch|ap)$/);
        if (match) {
            setCurrentActiveKey(match[0]);
        } else if (items.length > 0) {
            setCurrentActiveKey(items[0].key);
            navigate(`${location.pathname.replace(/\/$/, "")}/${items[0].key}`);
        }
    }, [location.pathname, items]);

    // 点击 tab 更新 URL
    const onChange = key => {
        const pathWithoutTab = location.pathname.replace(/(switch|ap)$/, "");
        navigate(`${pathWithoutTab.replace(/\/$/, "")}/${key}`);
    };

    return (
        <div style={{display: "flex", flex: 1}}>
            <Tabs
                style={{flex: 1}}
                activeKey={currentActiveKey}
                onChange={onChange}
                destroyInactiveTabPane
                items={items}
                className="upgrade-manage-container"
            />
        </div>
    );
};

export default UpgradeManagementSwitch;
