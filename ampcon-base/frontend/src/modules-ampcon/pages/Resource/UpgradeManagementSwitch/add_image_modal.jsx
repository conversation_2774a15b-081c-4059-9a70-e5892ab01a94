import {Button, Checkbox, Divider, Flex, Form, Input, message, Modal, Radio, Select, Spin, Tabs} from "antd";
import React, {forwardRef, useImperativeHandle, useState, useEffect} from "react";
import {NoteSvg, DownArrowSvg, RightArrowSvg, CloseSvg} from "@/utils/common/iconSvg";
import {
    getLatestImageForCampus,
    getLatestImage,
    uploadImageFileByFile,
    uploadImageFileByLatestLink,
    uploadImageFileByLink
} from "@/modules-ampcon/apis/config_api";
import {uploadAPImage, useGetDeviceTypes} from "@/modules-ampcon/apis/upgrade_api";
import {getImageInfo} from "@/modules-ampcon/utils/util";
import ReleaseNoteTooltip from "@/modules-ampcon/pages/Resource/UpgradeManagementSwitch/release_note_tooltip";

const AddImageModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showAddImageModal: type => {
            setSourceType(type);
            setIsShowModal(true);
        },
        hideAddImageModal: () => {}
    }));

    const {saveCallback} = props;

    const [sourceType, setSourceType] = useState(null); // store sourcetype ap
    const title = "Upload Image";
    const uploadImageFileLabel = "Image File";
    const uploadMd5ImageFileLabel = "MD5 File(Opt)";
    const uploadImageLinkLabel = "Img URL";
    const uploadMd5ImageLinkLabel = "MD5 URL(Opt):";
    const PlatformList = ["as4610", "n3000", "n3100", "x86", "s5810", "s5860", "s3410", "other"];

    const [isShowModal, setIsShowModal] = useState(false);
    const [isShowSpin, setIsShowSpin] = useState(false);
    const [currentActiveKey, setCurrentActiveKey] = useState("file");

    // upload by file
    const [uploadImageByFileForm] = Form.useForm();
    const [uploadImageByFileParseInfoForm] = Form.useForm();
    const [uploadImageFile, setUploadImageFile] = useState(null);
    const [uploadMd5File, setUploadMd5File] = useState(null);
    const [isHideFileNameParseInfo, setIsHideFileNameParseInfo] = useState(true);

    // upload by link
    const [uploadImageByLinkForm] = Form.useForm();
    const [uploadImageByLinkParseInfoForm] = Form.useForm();
    const [isHideLinkParseInfo, setIsHideLinkParseInfo] = useState(true);

    // upload by latest link
    const [uploadImageLatestLinkObject, setUploadImageLatestLinkObject] = useState({});
    const [selectedImageList, setSelectedImageList] = useState([]);

    const [collapsedVersions, setCollapsedVersions] = useState({});
    const toggleCollapse = version => {
        setCollapsedVersions(prev => ({...prev, [version]: !prev[version]}));
    };
    const [showAlert, setShowAlert] = useState(true);

    // get image model
    const {data, isError} = useGetDeviceTypes();
    const [modelList, setModelList] = useState([]);
    const isCampusEnvironment = import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-CAMPUS";
    useEffect(() => {
        if (data) {
            const updatedList = [...data, "other"];
            setModelList(updatedList);
        }
        if (isError) {
            message.error("Error fetching model list");
        }
    }, [data, isError]);

    const resetModal = () => {
        setCurrentActiveKey("file");
        resetFileTab();
        resetLinkTab();
        resetLatestTab();
    };

    const resetFileTab = () => {
        uploadImageByFileForm.resetFields();
        setIsHideFileNameParseInfo(true);
        setUploadImageFile(null);
        setUploadMd5File(null);
    };

    const resetLinkTab = () => {
        uploadImageByLinkForm.resetFields();
        uploadImageByLinkParseInfoForm.resetFields();
        setIsHideLinkParseInfo(true);
    };

    const resetLatestTab = () => {
        setUploadImageLatestLinkObject({});
    };

    return (
        <>
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
            {isShowModal ? (
                <Modal
                    className="ampcon-middle-modal"
                    title={
                        <div>
                            {title}
                            <Divider style={{marginTop: 8, marginBottom: 0}} />
                        </div>
                    }
                    open={isShowModal}
                    onOk={() => {}}
                    onCancel={() => {
                        setIsShowModal(false);
                        resetModal();
                    }}
                    footer={
                        <>
                            <Divider style={{marginTop: 0, marginBottom: 20}} />
                            <Button
                                onClick={() => {
                                    setIsShowModal(false);
                                    resetModal();
                                }}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="primary"
                                onClick={async () => {
                                    try {
                                        if (currentActiveKey === "file") {
                                            await uploadImageByFileForm.validateFields();
                                            await uploadImageByFileParseInfoForm.validateFields();
                                            setIsShowSpin(true);
                                            setIsShowModal(false);

                                            const uploadFunc =
                                                sourceType === "ap" ? uploadAPImage : uploadImageFileByFile;

                                            const args =
                                                sourceType === "ap"
                                                    ? [
                                                          uploadImageFile,
                                                          uploadImageByFileParseInfoForm.getFieldValue(
                                                              "imgFileVersion"
                                                          ),
                                                          uploadImageByFileParseInfoForm.getFieldValue(
                                                              "imgFileRevision"
                                                          ),
                                                          uploadImageByFileParseInfoForm.getFieldValue("imgFileModel")
                                                      ]
                                                    : [
                                                          uploadImageFile,
                                                          uploadImageByFileParseInfoForm.getFieldValue(
                                                              "imgFileVersion"
                                                          ),
                                                          uploadImageByFileParseInfoForm.getFieldValue(
                                                              "imgFileRevision"
                                                          ),
                                                          uploadImageByFileParseInfoForm.getFieldValue(
                                                              "imgFilePlatform"
                                                          ),
                                                          uploadMd5File
                                                      ];
                                            await uploadFunc(...args).then(response => {
                                                if (response.status !== 200) {
                                                    message.error(response.info);
                                                    resetModal();
                                                } else {
                                                    message.success(response.info);
                                                    resetModal();
                                                    saveCallback();
                                                }
                                            });
                                        } else if (currentActiveKey === "link") {
                                            await uploadImageByLinkForm.validateFields();
                                            await uploadImageByLinkParseInfoForm.validateFields();
                                            setIsShowSpin(true);
                                            setIsShowModal(false);

                                            const uploadFunc =
                                                sourceType === "ap" ? uploadAPImage : uploadImageFileByLink;

                                            const args =
                                                sourceType === "ap"
                                                    ? [
                                                          uploadImageByLinkForm.getFieldValue("imgLink"),
                                                          uploadImageByLinkParseInfoForm.getFieldValue(
                                                              "imgLinkVersion"
                                                          ),
                                                          uploadImageByLinkParseInfoForm.getFieldValue(
                                                              "imgLinkRevision"
                                                          ),
                                                          uploadImageByLinkParseInfoForm.getFieldValue("imgFileModel")
                                                      ]
                                                    : [
                                                          uploadImageByLinkForm.getFieldValue("imgLink"),
                                                          uploadImageByLinkParseInfoForm.getFieldValue(
                                                              "imgLinkVersion"
                                                          ),
                                                          uploadImageByLinkParseInfoForm.getFieldValue(
                                                              "imgLinkRevision"
                                                          ),
                                                          uploadImageByLinkParseInfoForm.getFieldValue(
                                                              "imgLinkPlatform"
                                                          ),
                                                          uploadImageByLinkForm.getFieldValue("md5Link")
                                                      ];

                                            await uploadFunc(...args).then(response => {
                                                if (response.status !== 200) {
                                                    message.error(response.info);
                                                    resetModal();
                                                } else {
                                                    message.success(response.info);
                                                    resetModal();
                                                    saveCallback();
                                                }
                                            });
                                        } else if (currentActiveKey === "latest") {
                                            setIsShowSpin(true);
                                            if (selectedImageList.length === 0) {
                                                message.error("Please select at least a image.");
                                                return;
                                            }
                                            setIsShowModal(false);
                                            await uploadImageFileByLatestLink(selectedImageList).then(response => {
                                                if (response.status !== 200) {
                                                    message.error(response.info);
                                                    resetModal();
                                                } else {
                                                    message.success(response.info);
                                                    resetModal();
                                                    saveCallback();
                                                }
                                            });
                                        }
                                    } catch (errorInfo) {
                                        // pass
                                    } finally {
                                        setIsShowSpin(false);
                                    }
                                }}
                            >
                                Upload
                            </Button>
                        </>
                    }
                >
                    <Flex
                        vertical
                        style={{display: "flex", flex: 1, minHeight: "260.23px"}}
                        className="upload-image-by-link"
                    >
                        <Radio.Group
                            onChange={e => {
                                const key = e.target.value;
                                if (key === "file") {
                                    resetLinkTab();
                                } else if (key === "link") {
                                    resetFileTab();
                                } else if (key === "latest") {
                                    setSelectedImageList([]);
                                    resetLinkTab();
                                    resetFileTab();
                                    if (isCampusEnvironment) {
                                        getLatestImageForCampus().then(response => {
                                            if (response.status !== 200) {
                                                const errorMsg =
                                                    response.info ||
                                                    "Fail to get latest image, please try later again.";
                                                message.error(errorMsg);
                                            } else {
                                                setUploadImageLatestLinkObject(response.data);
                                            }
                                        });
                                    } else {
                                        getLatestImage().then(response => {
                                            if (response.status !== 200) {
                                                message.error("Fail to get latest image, please try later again.");
                                            } else {
                                                setUploadImageLatestLinkObject(response.data);
                                            }
                                        });
                                    }
                                }
                                setCurrentActiveKey(e.target.value);
                                resetFileTab();
                                resetLinkTab();
                                resetLatestTab();
                            }}
                            defaultValue={currentActiveKey}
                        >
                            <Radio.Button
                                style={{height: "32px", width: "100px", textAlign: "center", lineHeight: "32px"}}
                                value="file"
                            >
                                File
                            </Radio.Button>
                            <Radio.Button
                                style={{height: "32px", width: "100px", textAlign: "center", lineHeight: "32px"}}
                                value="link"
                            >
                                Link
                            </Radio.Button>
                            {sourceType !== "ap" && (
                                <Radio.Button
                                    style={{height: "32px", width: "100px", textAlign: "center", lineHeight: "32px"}}
                                    value="latest"
                                >
                                    Latest
                                </Radio.Button>
                            )}
                        </Radio.Group>
                        <Tabs
                            tabBarStyle={{display: "none", height: 0}}
                            activeKey={currentActiveKey}
                            items={[
                                {
                                    key: "file",
                                    label: "File",
                                    children: (
                                        <div style={{minHeight: "232.22px"}}>
                                            <Form
                                                layout="horizontal"
                                                labelAlign="left"
                                                labelCol={{span: 5}}
                                                wrapperCol={{span: 17}}
                                                labelWrap
                                                className="label-wrap"
                                                form={uploadImageByFileForm}
                                                style={{paddingTop: "8px"}}
                                            >
                                                <Form.Item
                                                    name="imgFile"
                                                    label={uploadImageFileLabel}
                                                    rules={[
                                                        {
                                                            required: true
                                                        },
                                                        {
                                                            validator: (_, value) => {
                                                                if (value) {
                                                                    if (sourceType === "ap") {
                                                                        if (
                                                                            !value.endsWith(".bin") &&
                                                                            !value.endsWith(".tar")
                                                                        ) {
                                                                            return Promise.reject(
                                                                                new Error(
                                                                                    "File must end with .bin or .tar"
                                                                                )
                                                                            );
                                                                        }
                                                                    } else if (
                                                                        !value.endsWith(".bin") &&
                                                                        !value.endsWith(".tar.gz")
                                                                    ) {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "File must end with .bin or .tar.gz"
                                                                            )
                                                                        );
                                                                    }
                                                                }
                                                                return Promise.resolve();
                                                            }
                                                        }
                                                    ]}
                                                    initialValue=""
                                                >
                                                    <Input
                                                        id="fileInput"
                                                        type="file"
                                                        style={{width: "280px"}}
                                                        onChange={e => {
                                                            if (e.target.files.length !== 0) {
                                                                const parseInfo = getImageInfo(e.target.files[0].name);
                                                                if (parseInfo === null) {
                                                                    uploadImageByFileParseInfoForm.setFieldsValue({
                                                                        imgFileVersion: "",
                                                                        imgFileRevision: "",
                                                                        imgFilePlatform: "other",
                                                                        imgFileModel: "other"
                                                                    });
                                                                } else {
                                                                    uploadImageByFileParseInfoForm.setFieldsValue({
                                                                        imgFileVersion: parseInfo.version || "",
                                                                        imgFileRevision: parseInfo.revision || "",
                                                                        imgFilePlatform: parseInfo.platform || "other",
                                                                        imgFileModel: parseInfo.model || "other"
                                                                    });
                                                                }
                                                                setUploadImageFile(e.target.files[0]);
                                                                setIsHideFileNameParseInfo(false);
                                                            } else {
                                                                uploadImageByFileParseInfoForm.setFieldsValue({
                                                                    imgFileVersion: "",
                                                                    imgFileRevision: "",
                                                                    imgFilePlatform: "",
                                                                    imgFileModel: ""
                                                                });
                                                                setIsHideFileNameParseInfo(true);
                                                                setUploadImageFile(null);
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                                {sourceType !== "ap" && (
                                                    <Form.Item
                                                        name="md5File"
                                                        label={uploadMd5ImageFileLabel}
                                                        rules={[
                                                            {
                                                                validator: (_, value) => {
                                                                    if (value && !value.endsWith(".md5")) {
                                                                        return Promise.reject(
                                                                            new Error("File must end with .md5")
                                                                        );
                                                                    }
                                                                    return Promise.resolve();
                                                                }
                                                            }
                                                        ]}
                                                        initialValue=""
                                                    >
                                                        <Input
                                                            id="fileInput"
                                                            type="file"
                                                            style={{width: "280px"}}
                                                            onChange={e => {
                                                                setUploadMd5File(e.target.files[0]);
                                                            }}
                                                        />
                                                    </Form.Item>
                                                )}
                                            </Form>
                                            <div style={{marginTop: "30px"}} hidden={isHideFileNameParseInfo}>
                                                <Divider />
                                                <Form form={uploadImageByFileParseInfoForm}>
                                                    <Flex horizontal>
                                                        <Flex
                                                            vertical
                                                            style={{
                                                                flexBasis: "194.6px",
                                                                paddingRight: "24px"
                                                            }}
                                                        >
                                                            <div>Version</div>
                                                            <Form.Item
                                                                name="imgFileVersion"
                                                                rules={[
                                                                    {required: true, message: "Please input version"},
                                                                    {
                                                                        max: 32,
                                                                        message: "Version cannot exceed 32 characters!"
                                                                    }
                                                                ]}
                                                            >
                                                                <Input style={{width: "194.6px"}} />
                                                            </Form.Item>
                                                        </Flex>
                                                        <Flex
                                                            vertical
                                                            style={{
                                                                flexBasis: "194.6px",
                                                                paddingRight: "24px"
                                                            }}
                                                        >
                                                            <div>Revision</div>
                                                            <Form.Item
                                                                name="imgFileRevision"
                                                                rules={[
                                                                    {required: true, message: "Please input revision"},
                                                                    {
                                                                        max: 32,
                                                                        message: "Revision cannot exceed 32 characters!"
                                                                    }
                                                                ]}
                                                            >
                                                                <Input style={{width: "194.6px"}} />
                                                            </Form.Item>
                                                        </Flex>
                                                        <Flex
                                                            vertical
                                                            style={{
                                                                flexBasis: "194.6px"
                                                            }}
                                                        >
                                                            {sourceType !== "ap" ? (
                                                                // 如果 sourceType 不是 'ap'，显示 Model 选择框
                                                                <>
                                                                    <div>Platform</div>
                                                                    <Form.Item
                                                                        name="imgFilePlatform"
                                                                        rules={[
                                                                            {
                                                                                required: true,
                                                                                message: "Please select platform"
                                                                            }
                                                                        ]}
                                                                    >
                                                                        <Select style={{width: "194.6px"}}>
                                                                            {PlatformList.map(platform => (
                                                                                <Select.Option
                                                                                    key={platform}
                                                                                    value={platform}
                                                                                >
                                                                                    {platform}
                                                                                </Select.Option>
                                                                            ))}
                                                                        </Select>
                                                                    </Form.Item>
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <div>Model</div>
                                                                    <Form.Item
                                                                        name="imgFileModel"
                                                                        rules={[
                                                                            {
                                                                                required: true,
                                                                                message: "Please select model"
                                                                            }
                                                                        ]}
                                                                    >
                                                                        <Select style={{width: "194.6px"}}>
                                                                            {modelList.map(model => (
                                                                                <Select.Option
                                                                                    key={model}
                                                                                    value={model}
                                                                                >
                                                                                    {model}
                                                                                </Select.Option>
                                                                            ))}
                                                                        </Select>
                                                                    </Form.Item>
                                                                </>
                                                            )}
                                                        </Flex>
                                                    </Flex>
                                                </Form>
                                            </div>
                                        </div>
                                    )
                                },
                                {
                                    key: "link",
                                    label: "Link",
                                    children: (
                                        <div style={{minHeight: "232.22px"}}>
                                            <Form
                                                layout="horizontal"
                                                labelAlign="left"
                                                labelCol={{span: 5}}
                                                wrapperCol={{span: 17}}
                                                labelWrap
                                                className="label-wrap"
                                                form={uploadImageByLinkForm}
                                                style={{paddingTop: "8px"}}
                                            >
                                                <Form.Item
                                                    name="imgLink"
                                                    label={uploadImageLinkLabel}
                                                    rules={[
                                                        {
                                                            required: true
                                                        },
                                                        {
                                                            validator: (_, value) => {
                                                                if (value) {
                                                                    if (sourceType === "ap") {
                                                                        if (
                                                                            value &&
                                                                            /^(http|https):\/\/.*(\.bin|\.tar)$/.test(
                                                                                value
                                                                            ) === false
                                                                        ) {
                                                                            return Promise.reject(
                                                                                new Error(
                                                                                    "Link must be a valid url ending with .bin or .tar"
                                                                                )
                                                                            );
                                                                        }
                                                                    } else if (
                                                                        value &&
                                                                        /^(http|https):\/\/.*(\.bin|\.tar\.gz)$/.test(
                                                                            value
                                                                        ) === false
                                                                    ) {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "Link must be a valid url ending with .bin or .tar.gz"
                                                                            )
                                                                        );
                                                                    }
                                                                }
                                                                return Promise.resolve();
                                                            }
                                                        }
                                                    ]}
                                                    initialValue=""
                                                >
                                                    <Input.TextArea
                                                        // placeholder="http://xxx/xxx.bin or https://xxx/xxx.tar.gz"
                                                        placeholder={
                                                            sourceType === "ap"
                                                                ? "http://xxx/xxx.bin or https://xxx/xxx.tar"
                                                                : "http://xxx/xxx.bin or https://xxx/xxx.tar.gz"
                                                        }
                                                        rows={4}
                                                        style={{width: "280px"}}
                                                        onChange={e => {
                                                            const {value} = e.target;
                                                            const parts = value.split("/");
                                                            if (value !== "") {
                                                                const parseInfo = getImageInfo(parts[parts.length - 1]);
                                                                if (parseInfo === null) {
                                                                    uploadImageByLinkParseInfoForm.setFieldsValue({
                                                                        imgLinkVersion: "",
                                                                        imgLinkRevision: "",
                                                                        imgLinkPlatform: "other"
                                                                    });
                                                                } else {
                                                                    uploadImageByLinkParseInfoForm.setFieldsValue({
                                                                        imgLinkVersion: parseInfo.version || "",
                                                                        imgLinkRevision: parseInfo.revision || "",
                                                                        imgLinkPlatform: parseInfo.platform || "other"
                                                                    });
                                                                }
                                                                setIsHideLinkParseInfo(false);
                                                            } else {
                                                                uploadImageByLinkParseInfoForm.setFieldsValue({
                                                                    imgLinkVersion: "",
                                                                    imgLinkRevision: "",
                                                                    imgLinkPlatform: ""
                                                                });
                                                                setIsHideLinkParseInfo(true);
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                                {sourceType !== "ap" && (
                                                    <Form.Item
                                                        name="md5Link"
                                                        label={uploadMd5ImageLinkLabel}
                                                        rules={[
                                                            {
                                                                validator: (_, value) => {
                                                                    if (
                                                                        value &&
                                                                        /^(http|https):\/\/.*(\.bin\.md5|\.tar\.gz\.md5)$/.test(
                                                                            value
                                                                        ) === false
                                                                    ) {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "Link must be a valid url ending with .md5"
                                                                            )
                                                                        );
                                                                    }
                                                                    return Promise.resolve();
                                                                }
                                                            }
                                                        ]}
                                                        initialValue=""
                                                    >
                                                        <Input.TextArea
                                                            placeholder="http://xxx/xxx.bin.md5 or https://xxx/xxx.tar.gz.md5"
                                                            style={{width: "280px"}}
                                                            rows={4}
                                                        />
                                                    </Form.Item>
                                                )}
                                            </Form>
                                            <div style={{marginTop: "30px"}} hidden={isHideLinkParseInfo}>
                                                <Divider />
                                                <Form form={uploadImageByLinkParseInfoForm}>
                                                    <Flex horizontal>
                                                        <Flex
                                                            vertical
                                                            style={{flexBasis: "194.6px", paddingRight: "24px"}}
                                                        >
                                                            <div>Version</div>
                                                            <Form.Item
                                                                name="imgLinkVersion"
                                                                rules={[
                                                                    {required: true, message: "Please input version"},
                                                                    {
                                                                        max: 32,
                                                                        message: "Version cannot exceed 32 characters!"
                                                                    }
                                                                ]}
                                                            >
                                                                <Input style={{width: "194.6px"}} />
                                                            </Form.Item>
                                                        </Flex>
                                                        <Flex
                                                            vertical
                                                            style={{flexBasis: "194.6px", paddingRight: "24px"}}
                                                        >
                                                            <div>Revision</div>
                                                            <Form.Item
                                                                name="imgLinkRevision"
                                                                rules={[
                                                                    {required: true, message: "Please input revision"},
                                                                    {
                                                                        max: 32,
                                                                        message: "Revision cannot exceed 32 characters!"
                                                                    }
                                                                ]}
                                                            >
                                                                <Input style={{width: "194.6px"}} />
                                                            </Form.Item>
                                                        </Flex>
                                                        <Flex vertical style={{flexBasis: "194.6px"}}>
                                                            {sourceType !== "ap" ? (
                                                                <>
                                                                    <div>Platform</div>
                                                                    <Form.Item
                                                                        name="imgLinkPlatform"
                                                                        rules={[
                                                                            {
                                                                                required: true,
                                                                                message: "Please select platform"
                                                                            }
                                                                        ]}
                                                                    >
                                                                        <Select style={{width: "194.6px"}}>
                                                                            {PlatformList.map(platform => (
                                                                                <Select.Option
                                                                                    key={platform}
                                                                                    value={platform}
                                                                                >
                                                                                    {platform}
                                                                                </Select.Option>
                                                                            ))}
                                                                        </Select>
                                                                    </Form.Item>
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <div>Model</div>
                                                                    <Form.Item
                                                                        name="imgFileModel"
                                                                        rules={[
                                                                            {
                                                                                required: true,
                                                                                message: "Please select model"
                                                                            }
                                                                        ]}
                                                                    >
                                                                        <Select style={{width: "194.6px"}}>
                                                                            {modelList.map(model => (
                                                                                <Select.Option
                                                                                    key={model}
                                                                                    value={model}
                                                                                >
                                                                                    {model}
                                                                                </Select.Option>
                                                                            ))}
                                                                        </Select>
                                                                    </Form.Item>
                                                                </>
                                                            )}
                                                        </Flex>
                                                    </Flex>
                                                </Form>
                                            </div>
                                        </div>
                                    )
                                },
                                {
                                    key: "latest",
                                    label: "Latest",
                                    children: isCampusEnvironment ? (
                                        <div style={{minHeight: "226px", maxWidth: "610px"}}>
                                            {showAlert && (
                                                <div
                                                    style={{
                                                        background: "#F3F8FF",
                                                        borderRadius: 2,
                                                        height: 40,
                                                        padding: "0px 24px",
                                                        marginBottom: 24,
                                                        fontSize: 12,
                                                        color: "#367EFF",
                                                        display: "flex",
                                                        alignItems: "center",
                                                        justifyContent: "space-between"
                                                    }}
                                                >
                                                    <span style={{display: "inline-flex", alignItems: "center"}}>
                                                        <div
                                                            style={{
                                                                display: "flex",
                                                                marginRight: 4,
                                                                alignItems: "center"
                                                            }}
                                                        >
                                                            <NoteSvg />
                                                        </div>
                                                        <strong style={{marginRight: "8px"}}>Note</strong>: The selected
                                                        PicOS images will be downloaded to the AmpCon-Campus server.
                                                    </span>
                                                    <div
                                                        onClick={() => setShowAlert(false)}
                                                        style={{
                                                            cursor: "pointer",
                                                            display: "flex",
                                                            alignItems: "center"
                                                        }}
                                                    >
                                                        <CloseSvg />
                                                    </div>
                                                </div>
                                            )}

                                            <div style={{maxHeight: 480}}>
                                                {!uploadImageLatestLinkObject?.data ||
                                                uploadImageLatestLinkObject.data.length === 0 ? (
                                                    <h3 style={{textAlign: "center"}}>Loading...</h3>
                                                ) : (
                                                    uploadImageLatestLinkObject.data.map(verObj => {
                                                        const version = verObj.major_version;
                                                        const collapsed = !!collapsedVersions[version];
                                                        const {new_features, fixed_issues, full_release_notes} =
                                                            verObj.release_note;

                                                        return (
                                                            <div key={version} style={{marginBottom: 12}}>
                                                                <div
                                                                    onClick={() => toggleCollapse(version)}
                                                                    style={{
                                                                        cursor: "pointer",
                                                                        display: "flex",
                                                                        alignItems: "center",
                                                                        justifyContent: "space-between",
                                                                        background: "#F8FAFB",
                                                                        padding: "0px 8px",
                                                                        height: 36
                                                                    }}
                                                                >
                                                                    <div
                                                                        style={{
                                                                            display: "flex",
                                                                            alignItems: "center",
                                                                            gap: 4
                                                                        }}
                                                                    >
                                                                        <span
                                                                            style={{
                                                                                fontSize: 18,
                                                                                fontWeight: 600,
                                                                                padding: "4px"
                                                                            }}
                                                                        >
                                                                            {version}
                                                                        </span>
                                                                        <ReleaseNoteTooltip
                                                                            version={version}
                                                                            new_features={new_features}
                                                                            fixed_issues={fixed_issues}
                                                                            full_release_notes={full_release_notes}
                                                                        />
                                                                    </div>
                                                                    {collapsed ? <RightArrowSvg /> : <DownArrowSvg />}
                                                                </div>
                                                                {!collapsed && (
                                                                    <div style={{paddingLeft: 16, paddingTop: 8}}>
                                                                        {verObj.images.map(plt => {
                                                                            const key = `${version}/${plt.name}`;
                                                                            return (
                                                                                <div
                                                                                    key={key}
                                                                                    style={{padding: "4px 0"}}
                                                                                >
                                                                                    <Checkbox
                                                                                        checked={
                                                                                            selectedImageList.includes(
                                                                                                key
                                                                                            ) || plt.disabled
                                                                                        }
                                                                                        disabled={plt.disabled}
                                                                                        onChange={e => {
                                                                                            const {checked} = e.target;
                                                                                            setSelectedImageList(
                                                                                                prev => {
                                                                                                    return checked
                                                                                                        ? [...prev, key]
                                                                                                        : prev.filter(
                                                                                                              item =>
                                                                                                                  item !==
                                                                                                                  key
                                                                                                          );
                                                                                                }
                                                                                            );
                                                                                        }}
                                                                                    >
                                                                                        {plt.name}
                                                                                    </Checkbox>
                                                                                </div>
                                                                            );
                                                                        })}
                                                                    </div>
                                                                )}
                                                            </div>
                                                        );
                                                    })
                                                )}
                                            </div>
                                        </div>
                                    ) : (
                                        <div style={{minHeight: "226px"}}>
                                            {Object.keys(uploadImageLatestLinkObject).length === 0 ? (
                                                <h3 style={{textAlign: "center"}}>loading</h3>
                                            ) : (
                                                <Flex vertical style={{paddingBottom: "25px"}}>
                                                    <h3>Images for Pica8 server will be download to local Ampcon</h3>
                                                    {uploadImageLatestLinkObject &&
                                                        Object.entries(uploadImageLatestLinkObject).map(
                                                            ([key, value]) => (
                                                                <>
                                                                    <h5>{key.split("/")[0]}</h5>
                                                                    <Checkbox
                                                                        value={key}
                                                                        disabled={value}
                                                                        defaultChecked={value}
                                                                        onChange={e => {
                                                                            if (e.target.checked) {
                                                                                setSelectedImageList([
                                                                                    ...selectedImageList,
                                                                                    key
                                                                                ]);
                                                                            } else {
                                                                                setSelectedImageList(
                                                                                    selectedImageList.filter(
                                                                                        item => item !== key
                                                                                    )
                                                                                );
                                                                            }
                                                                        }}
                                                                    >
                                                                        {key.split("/")[1]}
                                                                    </Checkbox>
                                                                </>
                                                            )
                                                        )}
                                                </Flex>
                                            )}
                                        </div>
                                    )
                                }
                            ]}
                            style={{flex: 1}}
                            destroyInactiveTabPane
                        />
                    </Flex>
                </Modal>
            ) : null}
        </>
    );
});

export default AddImageModal;
