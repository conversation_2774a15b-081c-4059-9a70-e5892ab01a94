import {Button, Form, Input, message, Modal, Spin, Tabs, Radio, Flex, Divider} from "antd";
import {forwardRef, useImperativeHandle, useState} from "react";
import {uploadImageMd5FileByFile, uploadImageMd5FileByLink} from "@/modules-ampcon/apis/config_api";

const UploadMd5Modal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showUploadMd5Modal: id => {
            setImageId(id);
            setIsShowModal(true);
        },
        hideUploadMd5Modal: () => {}
    }));

    const title = "Upload MD5 Image";
    const uploadMd5ImageFileLabel = "MD5 File";
    const uploadMd5ImageLinkLabel = "MD5 URL";

    const {saveCallback} = props;

    const [isShowModal, setIsShowModal] = useState(false);
    const [isShowSpin, setIsShowSpin] = useState(false);
    const [imageId, setImageId] = useState(-1);
    const [currentActiveKey, setCurrentActiveKey] = useState("file");

    // upload by file
    const [uploadMd5ImageByFileForm] = Form.useForm();
    const [uploadMd5File, setUploadMd5File] = useState(null);

    // upload by link
    const [uploadMd5ImageByLinkForm] = Form.useForm();

    const resetModal = () => {
        setImageId(-1);
        setCurrentActiveKey("file");
        resetFileTab();
        resetLinkTab();
    };

    const resetFileTab = () => {
        uploadMd5ImageByFileForm.resetFields();
        setUploadMd5File("");
    };

    const resetLinkTab = () => {
        uploadMd5ImageByLinkForm.resetFields();
    };

    return (
        <>
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
            {isShowModal ? (
                <Modal
                    className="ampcon-middle-modal"
                    title={
                        <div>
                            {title}
                            <Divider style={{marginTop: 8, marginBottom: 0}} />
                        </div>
                    }
                    open={isShowModal}
                    onOk={() => {}}
                    onCancel={() => {
                        setIsShowModal(false);
                    }}
                    footer={
                        <>
                            <Divider />
                            <Button
                                onClick={() => {
                                    setIsShowModal(false);
                                    resetModal();
                                }}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="primary"
                                onClick={async () => {
                                    if (currentActiveKey === "file") {
                                        try {
                                            await uploadMd5ImageByFileForm.validateFields();
                                        } catch (errorInfo) {
                                            return;
                                        }
                                    } else if (currentActiveKey === "link") {
                                        try {
                                            await uploadMd5ImageByLinkForm.validateFields();
                                        } catch (errorInfo) {
                                            return;
                                        }
                                    }
                                    setIsShowSpin(true);
                                    try {
                                        if (currentActiveKey === "file") {
                                            await uploadImageMd5FileByFile(imageId, uploadMd5File).then(response => {
                                                if (response.status !== 200) {
                                                    message.error(response.info);
                                                } else {
                                                    message.success(response.info);
                                                    setIsShowModal(false);
                                                    resetModal();
                                                    saveCallback();
                                                }
                                            });
                                        } else if (currentActiveKey === "link") {
                                            await uploadImageMd5FileByLink(
                                                imageId,
                                                uploadMd5ImageByLinkForm.getFieldValue("link")
                                            ).then(response => {
                                                if (response.status !== 200) {
                                                    message.error(response.info);
                                                } else {
                                                    message.success(response.info);
                                                    setIsShowModal(false);
                                                    resetModal();
                                                    saveCallback();
                                                }
                                            });
                                        }
                                    } catch (errorInfo) {
                                        // empty
                                    } finally {
                                        setIsShowSpin(false);
                                    }
                                }}
                            >
                                Apply
                            </Button>
                        </>
                    }
                >
                    <Flex vertical style={{display: "flex", flex: 1}}>
                        <Radio.Group
                            onChange={e => {
                                setCurrentActiveKey(e.target.value);
                            }}
                            defaultValue={currentActiveKey}
                        >
                            <Radio.Button
                                style={{height: "32px", width: "100px", textAlign: "center", lineHeight: "32px"}}
                                value="file"
                            >
                                File
                            </Radio.Button>
                            <Radio.Button
                                style={{height: "32px", width: "100px", textAlign: "center", lineHeight: "32px"}}
                                value="link"
                            >
                                Link
                            </Radio.Button>
                        </Radio.Group>
                        <Tabs
                            tabBarStyle={{display: "none", height: 0}}
                            activeKey={currentActiveKey}
                            items={[
                                {
                                    key: "file",
                                    label: "File",
                                    children: (
                                        <div style={{minHeight: "195.22px"}}>
                                            <Form
                                                layout="horizontal"
                                                labelAlign="left"
                                                labelCol={{span: 5}}
                                                wrapperCol={{span: 17}}
                                                labelWrap
                                                className="label-wrap"
                                                form={uploadMd5ImageByFileForm}
                                                style={{paddingTop: "8px"}}
                                            >
                                                <Form.Item
                                                    name="file"
                                                    label={uploadMd5ImageFileLabel}
                                                    rules={[
                                                        {
                                                            required: true
                                                        },
                                                        {
                                                            validator: (_, value) => {
                                                                if (!value) {
                                                                    return Promise.reject(
                                                                        new Error("File must be selected")
                                                                    );
                                                                }
                                                                if (!value.endsWith(".md5")) {
                                                                    return Promise.reject(
                                                                        new Error("File must end with .md5")
                                                                    );
                                                                }
                                                                return Promise.resolve();
                                                            }
                                                        }
                                                    ]}
                                                    initialValue=""
                                                >
                                                    <Input
                                                        id="fileInput"
                                                        type="file"
                                                        style={{width: "280px"}}
                                                        onChange={e => {
                                                            setUploadMd5File(e.target.files[0]);
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Form>
                                        </div>
                                    )
                                },
                                {
                                    key: "link",
                                    label: "Link",
                                    children: (
                                        <div style={{minHeight: "195.22px"}}>
                                            <Form
                                                layout="horizontal"
                                                labelAlign="left"
                                                labelCol={{span: 5}}
                                                wrapperCol={{span: 17}}
                                                labelWrap
                                                className="label-wrap"
                                                form={uploadMd5ImageByLinkForm}
                                                style={{paddingTop: "8px"}}
                                            >
                                                <Form.Item
                                                    name="link"
                                                    label={uploadMd5ImageLinkLabel}
                                                    rules={[
                                                        {
                                                            required: true
                                                        },
                                                        {
                                                            validator: (_, value) => {
                                                                if (
                                                                    /^(http|https):\/\/.*(\.bin\.md5|\.tar\.gz\.md5)$/.test(
                                                                        value
                                                                    ) === false
                                                                ) {
                                                                    return Promise.reject(
                                                                        new Error(
                                                                            "Link must be a valid url ending with .md5"
                                                                        )
                                                                    );
                                                                }
                                                                return Promise.resolve();
                                                            }
                                                        }
                                                    ]}
                                                    initialValue=""
                                                >
                                                    <Input.TextArea
                                                        placeholder="http://xxx/xxx.bin.md5 or https://xxx/xxx.tar.gz.md5"
                                                        style={{width: "280px"}}
                                                        rows={8}
                                                    />
                                                </Form.Item>
                                            </Form>
                                        </div>
                                    )
                                }
                            ]}
                            onChange={key => {
                                if (key === "file") {
                                    resetLinkTab();
                                } else {
                                    resetFileTab();
                                }
                                setCurrentActiveKey(key);
                            }}
                            style={{flex: 1}}
                            destroyInactiveTabPane
                        />
                    </Flex>
                </Modal>
            ) : null}
        </>
    );
});

export default UploadMd5Modal;
