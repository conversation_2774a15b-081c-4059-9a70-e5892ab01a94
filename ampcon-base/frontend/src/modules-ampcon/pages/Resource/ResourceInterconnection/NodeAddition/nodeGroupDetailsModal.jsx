import React, {useRef, useEffect, useState} from "react";
import {useLocation} from "react-router-dom";
import {Row, Space, message, Table, Modal, Divider, Button} from "antd";
import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";
import {EyeOutlined, EyeInvisibleOutlined} from "@ant-design/icons";

// import API
import {getNodeTemplateList} from "@/modules-ampcon/apis/node_addition_api";

const NodeGroupDetailModal = ({title, nodeType, nodesInfo, isNodeGroupDetailModal, onCancel}) => {
    const {state} = useLocation();
    const [customNodeList, setCustomNodeList] = useState([]);
    const [currentCustomNode, setCurrentCustomNode] = useState([]);

    const [nodeDetails, setNodeDetails] = useState([]);
    const [isShowPassword, setIsShowPassword] = useState(false);

    const nodeInfoColumns = [
        {
            title: "Node Name",
            dataIndex: "host_name",
            key: "host_name"
        },
        {
            title: "IP Address",
            dataIndex: "ip_addr",
            key: "ip_addr"
        },
        {
            title: "User",
            dataIndex: "username",
            key: "username",
            render: (text, record, key) => {
                return <p>{record.username || "--"}</p>;
            }
        },
        {
            title: "Password",
            dataIndex: "password",
            key: "password",
            render: (text, record, key) => {
                return (
                    <>
                        {record.password && (
                            <>
                                {isShowPassword && (
                                    <>
                                        <span>{record.password || "--"}</span>
                                        <EyeInvisibleOutlined
                                            twoToneColor="#B8BFBF"
                                            style={{marginLeft: 8, width: 12, cursor: "pointer"}}
                                            onClick={() => setIsShowPassword(false)}
                                        />
                                    </>
                                )}
                                {!isShowPassword && (
                                    <>
                                        <span>******</span>
                                        <EyeOutlined
                                            twoToneColor="#B8BFBF"
                                            style={{marginLeft: 8, width: 12, cursor: "pointer"}}
                                            onClick={() => setIsShowPassword(true)}
                                        />
                                    </>
                                )}
                            </>
                        )}
                        {!record.password && <span>--</span>}
                    </>
                );
            }
        },
        {
            title: "Total Number",
            dataIndex: "total_num",
            key: "total_num",
            render: (text, record, key) => {
                return <p>{currentCustomNode.total_ports}</p>;
            }
        }
    ];

    const [NICPortGroupInfo, setNICPortGroupInfo] = useState([]);
    const NICPortGroupInfoColumns = [
        {
            title: "NIC Port Group",
            dataIndex: "nic_portgroup_name",
            key: "nic_portgroup_name"
        },
        {
            title: "Speed",
            dataIndex: "speed",
            key: "speed",
            render: (text, record, key) => {
                const legendClass = `fontColor_${record.speed}G`;
                return (
                    <p className={`${styles.legendClass} ${styles[legendClass]}`}>
                        {record.port_num} x {record.speed} Gbps
                    </p>
                );
            }
        },
        {
            title: "Port Member",
            dataIndex: "port_num",
            key: "port_num",
            render: (text, record, key) => {
                return (
                    <>
                        {Array.from({length: record.port_num}, (_, index) => (
                            <span key={index} style={{marginRight: 5}}>
                                {record.nic_portgroup_name}_{index + 1}
                                {index + 1 < record.port_num && <>,</>}
                            </span>
                        ))}
                    </>
                );
            }
        }
    ];
    const getCustomNode = async () => {
        setCustomNodeList([]);
        const result = await getNodeTemplateList();
        if (result.status === 200) {
            setCustomNodeList(result.data);
        } else message.error(result.info);
    };

    const updatePanel = () => {
        // 当 total_ports 为空或无效时，不渲染
        if (!currentCustomNode.total_ports || currentCustomNode.total_ports < 1) return null;

        // 封装生成端口元素的逻辑
        const generatePortElements = () => {
            const groups = Object.values(currentCustomNode.template_info);
            const portElements = [];
            let portIndex = 0;

            groups.forEach(group => {
                const {port_num = 0, speed = 1} = group;
                const legendClass = `legend_${speed}G`;
                for (let i = 0; i < port_num; i++) {
                    if (portIndex < currentCustomNode.total_ports) {
                        portElements.push(
                            <li key={portIndex}>
                                <div className={`${styles.legend_default} ${styles[legendClass]}`}>{portIndex + 1}</div>
                            </li>
                        );
                        portIndex++;
                    }
                }
                if (portIndex < currentCustomNode.total_ports) {
                    portElements.push(
                        <hr
                            key={`divider-${portIndex}`}
                            style={{
                                width: 1,
                                height: 24,
                                background: "#B2B2B2",
                                margin: "0 3px"
                            }}
                        />
                    );
                }
            });

            // 填充剩余端口
            while (portIndex < currentCustomNode.total_ports) {
                portElements.push(
                    <li key={portIndex}>
                        <div className={styles.legend_default}>{portIndex + 1}</div>
                    </li>
                );
                portIndex++;
            }
            return portElements;
        };

        return (
            <ul className={`${styles.portLegend} ${styles.CustomNodeModal}`} style={{minHeight: 20}}>
                {generatePortElements()}
            </ul>
        );
    };

    useEffect(() => {
        const findNICInfo = customNodeList.find(obj => obj.id === nodesInfo.node_template_id) || {};
        setCurrentCustomNode(findNICInfo);

        const templateArray =
            findNICInfo && findNICInfo.template_info
                ? Object.entries(findNICInfo.template_info).map(([nic_portgroup_name, details]) => ({
                      nic_portgroup_name,
                      ...details
                  }))
                : [];
        setNICPortGroupInfo(templateArray);
    }, [customNodeList]);

    useEffect(() => {
        if (currentCustomNode) {
            updatePanel();
        }
    }, [currentCustomNode]);

    useEffect(() => {
        if (isNodeGroupDetailModal && customNodeList) {
            getCustomNode();
            setNodeDetails(nodesInfo.node_host);
        }
    }, [nodesInfo, isNodeGroupDetailModal]);

    return (
        <Space>
            <Modal
                className="ampcon-middle-modal"
                title={
                    <div>
                        {title}
                        <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                open={isNodeGroupDetailModal}
                onCancel={onCancel}
                footer={
                    <>
                        <Divider style={{marginBottom: "20px", marginTop: "0px"}} />
                        <Row justify="end">
                            <Space>
                                <Button onClick={onCancel}> Cancel </Button>
                            </Space>
                        </Row>
                    </>
                }
            >
                {nodeType === "Cloud" && (
                    <ul className={styles.nodeDetails_box}>
                        <li>
                            <div>
                                <span>Fabric</span>
                                <span title={nodesInfo.fabric_name}>{nodesInfo.fabric_name}</span>
                            </div>
                            <div>
                                <span>PoD</span>
                                <span title={nodesInfo.az_name}>{nodesInfo.az_name}</span>
                            </div>
                        </li>

                        <li>
                            <div>
                                <span>Node Name</span>
                                <span title={nodesInfo.host_name}>{nodesInfo.host_name || "--"}</span>
                            </div>
                            <div>
                                <span>User</span>
                                <span title={nodesInfo.username}>{nodesInfo.username || "--"}</span>
                            </div>
                        </li>

                        <li>
                            <div>
                                <span>Password</span>
                                {nodesInfo.password && (
                                    <>
                                        {isShowPassword && (
                                            <>
                                                <span title={nodesInfo.password}>{nodesInfo.password || "--"}</span>
                                                <EyeInvisibleOutlined
                                                    twoToneColor="#B8BFBF"
                                                    style={{marginLeft: 8, width: 12, cursor: "pointer"}}
                                                    onClick={() => setIsShowPassword(false)}
                                                />
                                            </>
                                        )}
                                        {!isShowPassword && (
                                            <>
                                                <span>******</span>
                                                <EyeOutlined
                                                    twoToneColor="#B8BFBF"
                                                    style={{marginLeft: 8, width: 12, cursor: "pointer"}}
                                                    onClick={() => setIsShowPassword(true)}
                                                />
                                            </>
                                        )}
                                    </>
                                )}
                                {!nodesInfo.password && <span>--</span>}
                            </div>
                            <div>
                                <span>IP Address</span>
                                <span title={nodesInfo.management_ip}>{nodesInfo.management_ip || "--"}</span>
                            </div>
                        </li>

                        <li style={{minHeight: 40, height: "auto"}}>
                            <div>
                                <span>Description</span>
                                <span
                                    title={nodesInfo.description}
                                    style={{overflow: "clip", textOverflow: "clip", whiteSpace: "normal"}}
                                >
                                    {nodesInfo.description || "--"}
                                </span>
                            </div>
                        </li>
                    </ul>
                )}

                {nodeType === "BareMetal" && (
                    <>
                        <p className={styles.titleF18}>Node Group Info</p>
                        <ul className={styles.nodeDetails_box}>
                            <li>
                                <div>
                                    <span>Fabric</span>
                                    <span title={nodesInfo.fabric_name}>{nodesInfo.fabric_name}</span>
                                </div>
                                <div>
                                    <span>PoD</span>
                                    <span title={nodesInfo.az_name}>{nodesInfo.az_name}</span>
                                </div>
                            </li>

                            <li>
                                <div>
                                    <span>Node Group Name</span>
                                    <span title={nodesInfo.nodegroup_name}>{nodesInfo.nodegroup_name || "--"}</span>
                                </div>
                                <div>
                                    <span>Node Count</span>
                                    <span title={nodesInfo.node_count}>{nodesInfo.node_count || "--"}</span>
                                </div>
                            </li>

                            <li>
                                <div>
                                    <span>User</span>
                                    <span>admin</span>
                                    {/* <span>{nodesInfo.username || "--"}</span> */}
                                </div>
                                <div>
                                    <span>Node Template Name</span>
                                    <span title={currentCustomNode.template_name}>
                                        {currentCustomNode.template_name || "--"}
                                    </span>
                                </div>
                            </li>

                            <li>
                                <div>
                                    <span>Total Ports</span>
                                    <span>{currentCustomNode.total_ports || "--"}</span>
                                </div>
                            </li>

                            <li style={{minHeight: 40, height: "auto"}}>
                                <div>
                                    <span>Description</span>
                                    <span
                                        title={nodesInfo.description}
                                        style={{overflow: "clip", textOverflow: "clip", whiteSpace: "normal"}}
                                    >
                                        {nodesInfo.description || "--"}
                                    </span>
                                </div>
                            </li>
                        </ul>

                        <p className={styles.titleF18}>Node Info</p>
                        <Table
                            bordered
                            columns={nodeInfoColumns}
                            dataSource={nodeDetails}
                            pagination={{
                                showSizeChanger: true,
                                showTotal: (total, range) => {
                                    const start = range[0];
                                    const end = range[1];
                                    return `${start}-${end} of ${total} items`;
                                },
                                total: nodeDetails?.length
                            }}
                        />

                        <p className={styles.titleF18}>NIC Port Group Info</p>
                        <p>{currentCustomNode.total_ports || 0} Ports</p>
                        {updatePanel()}
                        <Table
                            bordered
                            columns={NICPortGroupInfoColumns}
                            dataSource={NICPortGroupInfo}
                            pagination={{
                                showSizeChanger: true,
                                showTotal: (total, range) => {
                                    const start = range[0];
                                    const end = range[1];
                                    return `${start}-${end} of ${total} items`;
                                },
                                total: NICPortGroupInfo?.length
                            }}
                        />
                    </>
                )}
            </Modal>
        </Space>
    );
};
export default NodeGroupDetailModal;
