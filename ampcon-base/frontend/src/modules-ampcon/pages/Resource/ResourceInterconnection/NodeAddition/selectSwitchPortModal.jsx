import React, {useEffect, useState} from "react";
import {useLocation} from "react-router-dom";
import {Space, Row, message, Table, Modal, Divider, Form, Radio, Button} from "antd";

import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";

import {node_group_list_device_port_info} from "@/modules-ampcon/apis/node_addition_api";
/**
 * Select Switch Ports
 */
const SelectSwitchPortModal = ({
    title,
    nodeType,
    portgroup_info,
    nodeDetails,
    isSelectSwitchPortModal,
    onCancel,
    onSubmit
}) => {
    const {state} = useLocation();
    const [currentSwitchPortgroup, setCurrentSwitchPortgroup] = useState([]);
    const [switchDetails, setSwitchDetails] = useState([]);
    const [portList, setPortList] = useState([]);
    const [upperPorts, setUpperPorts] = useState([]);
    const [belowPorts, setBelowPorts] = useState([]);
    const [disabledPorts, setDisabledPorts] = useState([]);
    const [usedPorts, setUesdPorts] = useState({
        disabledPort: [],
        upPorts: []
    });
    const [allocatedType, setAllocatedType] = useState("automatic");
    const [selectPostMaxNum, setSelectPostMaxNum] = useState(0);

    const portUsageColumns = [
        {
            title: "Total Ports",
            dataIndex: "total_ports",
            key: "total_ports"
        },
        {
            title: "Used Ports",
            dataIndex: "used_ports",
            key: "used_ports"
        },
        {
            title: "Unused Ports",
            dataIndex: "unused_ports",
            key: "unused_ports"
        },
        {
            title: "Disabled Ports",
            dataIndex: "disabled_ports",
            key: "disabled_ports"
        }
    ];

    const data = [
        {
            total_ports: portList?.length || 0,
            used_ports: usedPorts.disabledPort.length + usedPorts.upPorts.length || 0,
            unused_ports:
                portList.length - disabledPorts.length - usedPorts.disabledPort.length - usedPorts.upPorts.length,
            disabled_ports: disabledPorts?.length || 0
        }
    ];
    /**
     * 读取端口情况
     */
    const fetchNodeGroupListDevicePortInfo = async () => {
        try {
            const res = await node_group_list_device_port_info({
                logicDeviceIdList: [portgroup_info.logic_device_id]
            });
            if (res.status === 200) {
                setSwitchDetails(res.data);
                const portListData = res.data[0].all_ports;
                const mergedArray = [...portListData.ge, ...portListData.qe, ...portListData.te, ...portListData.xe];
                setPortList(mergedArray);
                // 分离上下端口
                const oddPorts = JSON.parse(JSON.stringify(mergedArray)).filter(
                    (port, index) => parseInt(index) % 2 === 0
                );
                const evenPorts = JSON.parse(JSON.stringify(mergedArray)).filter(
                    (port, index) => parseInt(index) % 2 !== 0
                );
                setUpperPorts(oddPorts);
                setBelowPorts(evenPorts);
                setUesdPorts(prevState => ({
                    ...prevState,
                    disabledPort: [...new Set(res.data[0].used_ports)]
                }));
            }
        } catch (error) {
            // error
        }
    };

    /**
     * 禁止端口重复选择
     * @param {*} portName
     * @returns
     */
    const extractAndDeduplicatePortInfo = (portgroup_info, nodeDetails) => {
        const result = [];
        const seenPorts = new Set();
        nodeDetails.switch_portgroup.forEach(group => {
            if (
                group?.vlan_domain_id === portgroup_info?.vlan_domain_id &&
                group?.portgroup_name !== portgroup_info?.portgroup_name
            ) {
                group.portgroup_info.forEach(portGroup => {
                    if (portGroup.logic_device_id === portgroup_info.logic_device_id) {
                        if (portGroup && portGroup.port_list) {
                            portGroup?.port_list?.forEach(port => {
                                if (!seenPorts.has(port)) {
                                    seenPorts.add(port);
                                    result.push(port);
                                }
                            });
                        }
                        if (portGroup.port_list) {
                            portGroup?.port_list?.forEach(port => {
                                if (!seenPorts.has(port)) {
                                    seenPorts.add(port);
                                    result.push(port);
                                }
                            });
                        }
                    }
                });
            }
        });
        return result;
    };

    const disabledPort = portName => {
        if (!portName) return;
        if (state?.data?.usage_state && portgroup_info?.id) {
            return;
        }
        if (usedPorts?.disabledPort?.includes(portName)) {
            message.warning("The port is currently in use and cannot be disabled");
            return;
        }
        if (usedPorts.upPorts.includes(portName)) {
            message.error("The port you disabled is already in use. Please cancel its use first");
            return;
        }
        setDisabledPorts(prevPorts => {
            if (prevPorts.includes(portName)) {
                return prevPorts.filter(p => p !== portName);
            }
            return [...prevPorts, portName];
        });
    };

    const allocatedPort = portName => {
        if (data[0].total_ports === data[0].used_ports) return;
        if (state?.data?.usage_state && portgroup_info?.id) return;
        if (disabledPorts?.includes(portName)) {
            message.warning("The port you have assigned has been disabled. Please first disable it");
            return;
        }

        if (allocatedType === "automatic") {
            const startIndex = portList.indexOf(portName);
            if (startIndex === -1) {
                message.error("Selected port not found in port list");
                return;
            }
            const isPortUsed = usedPorts.upPorts.includes(portName);
            setUesdPorts(prevState => ({
                ...prevState,
                upPorts: []
            }));
            if (isPortUsed) {
                return;
            }

            // 自动分配模式：从选中的端口开始查找可用端口
            const availablePorts = [];
            let currentIndex = startIndex;
            while (availablePorts.length < selectPostMaxNum && availablePorts.length <= portList.length) {
                const port = portList[currentIndex % portList.length];
                if (!disabledPorts.includes(port) && !usedPorts.disabledPort.includes(port)) {
                    availablePorts.push(port);
                }
                currentIndex++;
            }
            if (availablePorts.length > 0) {
                setUesdPorts(prevPorts => ({
                    ...prevPorts,
                    upPorts: [...new Set([...prevPorts.upPorts, ...availablePorts])]
                }));
            } else {
                message.error("No available ports found");
            }
        } else {
            // 手动分配模式：切换端口选中状态
            const isPortUsed = usedPorts.upPorts.includes(portName);
            if (isPortUsed) {
                // 取消选择端口
                setUesdPorts(prevPorts => ({
                    ...prevPorts,
                    upPorts: prevPorts.upPorts.filter(p => p !== portName)
                }));
                return;
            }
            // 检查是否超过最大选择数量
            if (usedPorts.upPorts.length >= selectPostMaxNum) {
                message.error(`You can select up to ${selectPostMaxNum} ports manually`);
                return;
            }
            // 添加新选择的端口
            setUesdPorts(prevPorts => ({
                ...prevPorts,
                upPorts: [...prevPorts.upPorts, portName]
            }));
        }
    };

    const onChange = e => {
        setAllocatedType(e.target.value);
    };
    const handleOk = async () => {
        if (nodeType === "BareMetal") {
            await onSubmit({
                id: portgroup_info.id,
                group_index: portgroup_info.group_index,
                portgroup_name: portgroup_info.portgroup_name,
                vlan_domain_id: portgroup_info.vlan_domain_id,
                logic_device_id: portgroup_info.logic_device_id,
                switch_sn: portgroup_info.switch_sn,
                disable_port_list: disabledPorts,
                port_list: usedPorts?.upPorts,
                nic_port_list: portgroup_info.nic_port_list
            });
        }
        if (nodeType === "Cloud") {
            await onSubmit({
                port_id: portgroup_info.port_id,
                group_index: portgroup_info.group_index,
                port_group_name: portgroup_info.port_group_name,
                vlan_domain_id: portgroup_info.vlan_domain_id,
                link_count: portgroup_info.link_count,
                logic_device_id: portgroup_info.logic_device_id,
                switch_sn: portgroup_info.switch_sn,
                disable_port_list: disabledPorts,
                port_name: usedPorts?.upPorts
            });
        }
    };

    useEffect(() => {
        if (isSelectSwitchPortModal) {
            setUesdPorts({
                disabledPort: [],
                upPorts: []
            });
            setUpperPorts([]);
            setBelowPorts([]);
            setDisabledPorts([]);
            setPortList([]);
            fetchNodeGroupListDevicePortInfo();
        }
    }, [isSelectSwitchPortModal]);

    useEffect(() => {
        if (nodeType === "BareMetal") {
            if (portgroup_info && nodeDetails?.switch_portgroup?.length > 0) {
                const findSwitchPortgroup = nodeDetails.switch_portgroup.find(
                    key => key.portgroup_name === portgroup_info.portgroup_name
                );
                const extractAndDeduplicatePortInfoData = extractAndDeduplicatePortInfo(portgroup_info, nodeDetails);
                setUesdPorts(prevState => {
                    const updatedDisabledPort = prevState.disabledPort.filter(
                        port => !portgroup_info?.port_list?.includes(port)
                    );

                    return {
                        ...prevState,
                        disabledPort: [...new Set([...updatedDisabledPort, ...extractAndDeduplicatePortInfoData])],
                        upPorts: portgroup_info?.port_list
                    };
                });
                setDisabledPorts(portgroup_info?.disable_port_list || []);
                setCurrentSwitchPortgroup(findSwitchPortgroup);

                let allocatedPortNum = nodeDetails.node_count * findSwitchPortgroup.nicPortUpNum || 0;
                if (
                    findSwitchPortgroup.link_type === "MLAG Leaf" &&
                    findSwitchPortgroup.access_mlag_mode === "Dual-Homed"
                ) {
                    allocatedPortNum = (nodeDetails.node_count * findSwitchPortgroup.nicPortUpNum) / 2;
                }
                setSelectPostMaxNum(allocatedPortNum);

                if (portgroup_info.id) portgroup_info.nic_port_list = [];
                else portgroup_info.nic_port_list = [];
                for (let i = 0; i < findSwitchPortgroup.nicSelectPortGroupNum; i++) {
                    if (findSwitchPortgroup.link_type === "MLAG Leaf") {
                        if (portgroup_info.group_index === 1) {
                            if (i < findSwitchPortgroup.nicPortUpNum / 2) {
                                if (portgroup_info.id)
                                    portgroup_info?.nic_port_list.push(
                                        `${findSwitchPortgroup.nic_portgroup_name}_${i + 1}`
                                    );
                                else
                                    portgroup_info?.nic_port_list.push(
                                        `${findSwitchPortgroup.nic_portgroup_name}_${i + 1}`
                                    );
                            }
                        } else if (portgroup_info.group_index === 2) {
                            if (i >= findSwitchPortgroup.nicPortUpNum / 2 && i < findSwitchPortgroup.nicPortUpNum) {
                                if (portgroup_info.id)
                                    portgroup_info?.nic_port_list.push(
                                        `${findSwitchPortgroup.nic_portgroup_name}_${i + 1}`
                                    );
                                else
                                    portgroup_info?.nic_port_list.push(
                                        `${findSwitchPortgroup.nic_portgroup_name}_${i + 1}`
                                    );
                            }
                        }
                    } else {
                        portgroup_info?.nic_port_list.push(`${findSwitchPortgroup.nic_portgroup_name}_${i + 1}`);
                    }
                }
            }
        }
        if (nodeType === "Cloud") {
            if (portgroup_info && nodeDetails?.host_link?.length > 0) {
                const findLinkHost = nodeDetails.host_link.find(
                    key => key.port_group_name === portgroup_info.port_group_name
                );
                setUesdPorts(prevState => {
                    const updatedDisabledPort = prevState.disabledPort.filter(
                        port => !portgroup_info?.port_name?.includes(port)
                    );

                    return {
                        ...prevState,
                        disabledPort: updatedDisabledPort,
                        upPorts: portgroup_info?.port_name || []
                    };
                });
                setDisabledPorts(portgroup_info?.disable_port_list || []);
                setCurrentSwitchPortgroup(findLinkHost);
                setSelectPostMaxNum(findLinkHost.link_count);
            }
        }
    }, [nodeType, portgroup_info, nodeDetails, portList]);

    return (
        <Modal
            className="ampcon-max-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isSelectSwitchPortModal}
            onCancel={onCancel}
            footer={
                <>
                    <Divider style={{marginBottom: "20px", marginTop: "0px"}} />
                    <Row justify="end">
                        <Space>
                            <Button onClick={onCancel}> Cancel </Button>
                            {!state?.data?.usage_state && (
                                <Button
                                    type="primary"
                                    onClick={handleOk}
                                    disabled={
                                        usedPorts?.upPorts.length < selectPostMaxNum ||
                                        usedPorts.disabledPort.length === portList?.length
                                    }
                                >
                                    Apply
                                </Button>
                            )}
                        </Space>
                    </Row>
                </>
            }
        >
            <ul className={styles.select_switch_ports_box}>
                {state.nodeType === "BareMetal" && (
                    <li>
                        <h2>NIC Port Group Information</h2>
                        <div className={styles.liITem}>
                            <div>
                                <span>NIC Port Group</span>
                                <span>{currentSwitchPortgroup?.nic_portgroup_name}</span>
                            </div>

                            <div>
                                <span>Select NIC Ports</span>
                                <ul className={styles.NICPortsli}>
                                    {Array.from(
                                        {
                                            length: currentSwitchPortgroup?.nicSelectPortGroupNum || 0
                                        },
                                        (_, i) => {
                                            let isPortUsed =
                                                currentSwitchPortgroup.nicPortUpNum <=
                                                    currentSwitchPortgroup.nicSelectPortGroupNum &&
                                                i < currentSwitchPortgroup.nicPortUpNum;

                                            if (
                                                currentSwitchPortgroup.link_type === "MLAG Leaf" &&
                                                currentSwitchPortgroup.access_mlag_mode === "Dual-Homed"
                                            ) {
                                                if (portgroup_info.group_index === 1) {
                                                    isPortUsed = i < currentSwitchPortgroup.nicPortUpNum / 2;
                                                } else if (portgroup_info.group_index === 2) {
                                                    isPortUsed =
                                                        i >= currentSwitchPortgroup.nicPortUpNum / 2 &&
                                                        i < currentSwitchPortgroup.nicPortUpNum;
                                                }
                                            }
                                            return (
                                                <li
                                                    key={i}
                                                    className={styles.NICPortsliItemDefault}
                                                    style={isPortUsed ? {background: "#D0DC42"} : {}}
                                                >
                                                    <span
                                                        style={isPortUsed ? {background: "#D0DC42", color: "#FFF"} : {}}
                                                    >
                                                        {i + 1}
                                                    </span>
                                                </li>
                                            );
                                        }
                                    )}
                                </ul>
                            </div>
                        </div>
                    </li>
                )}

                <li>
                    <h2>Port Usage</h2>
                    <Table columns={portUsageColumns} dataSource={data} pagination={false} />
                </li>

                <li>
                    <h2>Select Disabled Ports</h2>
                    <div className={styles.select_ports_box}>
                        <ul>
                            {upperPorts?.map((portName, index) => (
                                <li key={index}>
                                    <span>{index * 2 + 1}</span>
                                    <div
                                        className={disabledPorts?.includes(portName) ? styles.isDisabled : ""}
                                        onClick={() => disabledPort(portName)}
                                    />
                                </li>
                            ))}
                        </ul>
                        <ul>
                            {belowPorts?.map((portName, index) => (
                                <li key={index}>
                                    <div
                                        className={disabledPorts?.includes(portName) ? styles.isDisabled : ""}
                                        onClick={() => disabledPort(portName)}
                                    />
                                    <span>{index * 2 + 2}</span>
                                </li>
                            ))}
                        </ul>
                    </div>
                </li>

                <li>
                    <h2>Select Allocated Ports</h2>
                    <Radio.Group
                        disabled={state?.actionType === "Edit" && state?.data?.usage_state && portgroup_info.id}
                        onChange={onChange}
                        value={allocatedType}
                        options={[
                            {value: "automatic", label: "Automatic Allocation"},
                            {value: "manual", label: "Manual Allocation"}
                        ]}
                    />
                    <div className={styles.select_ports_box}>
                        <ul>
                            {upperPorts.map((portName, index) => (
                                <li key={index}>
                                    <span>{index * 2 + 1}</span>
                                    <div
                                        className={`
                                                ${usedPorts.upPorts.includes(portName) ? styles.isUsed : ""}
                                                ${usedPorts.disabledPort?.includes(portName) ? styles.isDisabled : ""}
                                            `}
                                        onClick={() => allocatedPort(portName)}
                                    />
                                </li>
                            ))}
                        </ul>
                        <ul>
                            {belowPorts.map((portName, index) => (
                                <li key={index}>
                                    <div
                                        className={`
                                                ${usedPorts.upPorts.includes(portName) ? styles.isUsed : ""}
                                                ${usedPorts.disabledPort?.includes(portName) ? styles.isDisabled : ""}
                                            `}
                                        onClick={() => allocatedPort(portName)}
                                    />
                                    <span>{index * 2 + 2}</span>
                                </li>
                            ))}
                        </ul>
                    </div>
                </li>
            </ul>
        </Modal>
    );
};
export default SelectSwitchPortModal;
