import React, {useEffect, useState} from "react";
import {Row, Space, message, Tag, Table, Modal, Divider, Button} from "antd";
import {GlobalSearchInput} from "@/modules-ampcon/components/custom_table";
import {useTableInitialElement} from "@/modules-ampcon/hooks/useModalTable";
import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";

// import API
import {fabricLinkInfo, cloudLinkInfo} from "@/modules-ampcon/apis/node_addition_api";
/**
 * Custom Node Template
 */
const NodeLinksInfoModal = ({title, nodeType, nodesInfo, linksInfoModal, setLinksInfoModal, onCancel}) => {
    const [searchFields, setSearchFields, linkInfoData, setLinkInfoData] = useTableInitialElement([], true);
    const [BareMetalFilteredData, setBareMetalFilteredData] = useState([]);
    const [CloudFilteredData, setCloudFilteredData] = useState([]);

    const getLinksInfo = async () => {
        const result = await fabricLinkInfo({id: nodesInfo.id});
        if (result.status === 200) {
            setLinkInfoData(result.data);
            setBareMetalFilteredData(result.data);
            setCloudFilteredData(result.data);
        } else message.error(result.info);
    };
    const getCloudLinksInfo = async () => {
        const result = await cloudLinkInfo({id: nodesInfo.id});
        if (result.status === 200) {
            setLinkInfoData(result.data);
            setBareMetalFilteredData(result.data);
            setCloudFilteredData(result.data);
        } else message.error(result.info);
    };

    const BareMetalLinkInfoColumns = [
        {title: "Node", dataIndex: "node_name"},
        {title: "NIC Port Group", dataIndex: "nic_port_group"},
        {title: "Port Count", dataIndex: "port_count"},
        {title: "Port Name", dataIndex: "port_name"},
        {title: "Switch Port Group", dataIndex: "switch_port_group"},
        {title: "VLAN Domain", dataIndex: "vlan_domain"},
        {title: "Attachment Type", dataIndex: "link_type"},
        {title: "Switch", dataIndex: "switch_sn"},
        {title: "Switch Port Location", dataIndex: "switch_port"},
        {title: "Port Mode", dataIndex: "port_mode"},
        {
            title: "Configuration Status",
            dataIndex: "status",
            render: (text, record) => {
                const statusClassMap = {
                    "Not Deployed": styles.notDeployedTag,
                    Deploying: styles.runningTag,
                    Deployed: styles.successTag,
                    "Deploy Failed": styles.failedTag,
                    Deleting: styles.runningTag,
                    "Delete Failed": styles.failedTag
                };
                return <Tag className={statusClassMap[record?.status] || styles.uncheckedTag}>{record?.status}</Tag>;
            }
        }
    ];

    const CloudLinkInfoColumns = [
        {title: "Node", dataIndex: "node_name"},
        {title: "Switch Port Group", dataIndex: "switch_port_group"},
        {title: "VLAN Domain", dataIndex: "vlan_domain"},
        {title: "Attachment Type", dataIndex: "link_type"},
        {title: "Switch", dataIndex: "switch_sn"},
        {title: "Switch Port Location", dataIndex: "switch_port"},
        {title: "Port Mode", dataIndex: "port_mode"},
        {
            title: "Configuration Status",
            dataIndex: "status",
            render: (text, record) => {
                const statusClassMap = {
                    "Not Deployed": styles.notDeployedTag,
                    Deploying: styles.runningTag,
                    Deployed: styles.successTag,
                    "Deploy Failed": styles.failedTag,
                    Deleting: styles.runningTag,
                    "Delete Failed": styles.failedTag
                };
                return <Tag className={statusClassMap[record?.status] || styles.uncheckedTag}>{record?.status}</Tag>;
            }
        }
    ];

    useEffect(() => {
        if (linksInfoModal) {
            if (nodeType === "BareMetal") getLinksInfo();
            if (nodeType === "Cloud") getCloudLinksInfo();
        }
    }, [nodesInfo, nodeType, linksInfoModal]);

    const handleSearchChange = e => {
        const searchValue = e.target.value.toLowerCase();
        if (nodeType === "BareMetal") {
            setSearchFields({
                fields: [
                    "node_name",
                    "nic_port_group",
                    "port_count",
                    "switch_port_group",
                    "vlan_domain",
                    "link_type",
                    "switch_sn",
                    "switch_port",
                    "port_mode",
                    "status"
                ],
                value: searchValue
            });
        } else if (nodeType === "Cloud") {
            setSearchFields({
                fields: [
                    "node_name",
                    "switch_port_group",
                    "vlan_domain",
                    "link_type",
                    "switch_sn",
                    "switch_port",
                    "port_mode",
                    "status"
                ],
                value: searchValue
            });
        }

        const filtered = linkInfoData.filter(item => {
            return searchFields.fields.some(field => {
                if (item[field] && typeof item[field] === "string") {
                    return item[field].toLowerCase().includes(searchValue);
                }
                if (typeof item[field] === "number") {
                    return item[field].toString().includes(searchValue);
                }
                return false;
            });
        });

        setBareMetalFilteredData(filtered);
    };

    return (
        <Space>
            <Modal
                className="ampcon-max-modal"
                title={
                    <div>
                        {title}
                        <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                open={linksInfoModal}
                onCancel={onCancel}
                footer={
                    <>
                        <Divider style={{marginBottom: "20px", marginTop: "0px"}} />
                        <Row justify="end">
                            <Space>
                                <Button onClick={onCancel}> Cancel </Button>
                            </Space>
                        </Row>
                    </>
                }
            >
                {nodeType === "Cloud" && (
                    <>
                        <div style={{display: "flex", justifyContent: "space-between", marginBottom: 24}}>
                            <div />
                            <GlobalSearchInput onChange={handleSearchChange} />
                        </div>
                        <Table
                            bordered
                            columns={CloudLinkInfoColumns}
                            dataSource={BareMetalFilteredData}
                            pagination={{
                                showSizeChanger: true,
                                showTotal: (total, range) => {
                                    const start = range[0];
                                    const end = range[1];
                                    return `${start}-${end} of ${total} items`;
                                },
                                total: CloudFilteredData?.length
                            }}
                            rowKey={record => record.portgroup_name}
                        />
                    </>
                )}
                {nodeType === "BareMetal" && (
                    <>
                        <div style={{display: "flex", justifyContent: "space-between", marginBottom: 24}}>
                            <div>
                                <span style={{color: "#929A9E", marginRight: 24}}>Node Group Name:</span>
                                {nodesInfo.nodegroup_name}
                            </div>
                            <GlobalSearchInput onChange={handleSearchChange} />
                        </div>
                        <Table
                            bordered
                            columns={BareMetalLinkInfoColumns}
                            dataSource={BareMetalFilteredData}
                            pagination={{
                                showSizeChanger: true,
                                showTotal: (total, range) => {
                                    const start = range[0];
                                    const end = range[1];
                                    return `${start}-${end} of ${total} items`;
                                },
                                total: BareMetalFilteredData?.length
                            }}
                            rowKey={record => record.portgroup_name}
                        />
                    </>
                )}
            </Modal>
        </Space>
    );
};

export default NodeLinksInfoModal;
