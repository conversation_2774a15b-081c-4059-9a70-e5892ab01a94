import React, {useEffect, useState} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import VLANDomain from "./VlanDomain/vlanDomain";
import NodeFabricator from "./NodeAddition/nodeAddition";
import AZPool from "../../../../pages/Resource/AZ/az_pool";
import {useSelector} from "react-redux";
import ProtectedRoute, {isRouteForbidden} from "@/modules-ampcon/utils/util";
import ForbiddenPage from "@/modules-ampcon/pages/ForbiddenPage";

const AZManager = () => {
    const allItems = [
        {
            key: "PoD",
            label: "PoD",
            children: <ProtectedRoute component={AZPool} />
        },
        {
            key: "vlan_domain",
            label: "VLAN Domain",
            children: <ProtectedRoute component={VLANDomain} />
        },
        {
            key: "node_addition",
            label: "Node Addition",
            children: <ProtectedRoute component={NodeFabricator} />
        }
    ];
    const currentUser = useSelector(state => state.user.userInfo);
    const userType = currentUser?.type;
    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();

    const pathReg = /(PoD|vlan_domain|node_addition)$/;

    useEffect(() => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(pathReg)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            const matchLength = currentPath.match(pathReg)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    if (isRouteForbidden(location.pathname, userType)) {
        return <ForbiddenPage />;
    }

    return (
        <div style={{display: "flex", flex: 1}}>
            <Tabs
                style={{flex: 1}}
                onChange={onChange}
                activeKey={currentActiveKey}
                items={allItems}
                destroyInactiveTabPane
            />
        </div>
    );
};

export default AZManager;
