import React, {useEffect, useState} from "react";
import {useNavigate} from "react-router-dom";
import {Table, Space, Button, message} from "antd";
import Icon from "@ant-design/icons";
import {useTableInitialElement} from "@/modules-ampcon/hooks/useModalTable";
import {
    handleTableChange,
    createColumnConfig,
    createMatchMode,
    createFilterFields,
    GlobalSearchInput
} from "@/modules-ampcon/components/custom_table";
import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";
import {addSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {fetchVlanDomainInfo, delete_vlan_domain} from "@/modules-ampcon/apis/node_addition_api";

const VLANDomain = () => {
    const navigate = useNavigate();
    const [
        isModalOpen,
        setIsModalOpen,
        searchFields,
        setSearchFields,
        data,
        setData,
        loading,
        setLoading,
        pagination,
        setPagination
    ] = useTableInitialElement([], true);

    const [sorter, setSorter] = useState({});
    const [filters, setFilters] = useState({});
    const [createNodeModal, setCreateNodeModal] = useState(false);
    const [nodesInfo, setNodesInfo] = useState({});
    const [fabricOptions, setFabricOptions] = useState([]);

    const checkSortedColumn = columns => {
        for (const columnKey in columns) {
            if (Object.prototype.hasOwnProperty.call(columns, columnKey)) {
                const columnConfig = columns[columnKey];
                if (columnConfig.defaultSortOrder !== null) {
                    return [columnConfig.dataIndex, columnConfig.defaultSortOrder];
                }
            }
        }
        return [undefined, undefined];
    };

    const fetchData = async () => {
        setLoading(true);

        const filterFields = filters ? createFilterFields(filters, matchModes) : [];
        const sortFields = [];
        if (sorter.field && sorter.order) {
            sortFields.push({
                field: sorter.field,
                order: sorter.order === "ascend" ? "asc" : "desc"
            });
        }

        try {
            const response = await fetchVlanDomainInfo(
                pagination.current,
                pagination.pageSize,
                filterFields,
                sortFields,
                searchFields
            );

            setData(response.data);
            setPagination(prev => ({
                ...prev,
                total: response.total,
                current: response.page,
                pageSize: response.pageSize
            }));
        } catch (error) {
            // error
        } finally {
            setLoading(false);
        }
    };

    const edit_template = (type, record) => {
        // setCreateNodeModal(true);
        // setNodesInfo(record);
        navigate(`/resource/resource_interconnection/vlan_domain/${record.group_name}`, {
            state: {actionType: type, data: record}
        });
    };

    const delete_template = async data => {
        const ret = await delete_vlan_domain({vdGroupId: data});
        if (ret.status === 200) {
            message.success(ret.info);
            fetchData();
        } else {
            message.error(ret.info);
        }
        await fetchData();
    };

    const handleSearchChange = e => {
        setSearchFields({
            fields: ["group_name"],
            value: e.target.value
        });
    };

    const userColumns = [
        {
            ...createColumnConfig("Group Name", "group_name"),
            // title: "Group Name",
            // dataIndex: "group_name",
            render: (text, record) => (
                <Space>
                    <a
                        onClick={() => {
                            edit_template("View", record);
                        }}
                        style={{color: "#14C9BB", textDecoration: "none"}}
                    >
                        {text}
                    </a>
                </Space>
            )
        },
        {
            ...createColumnConfig("Description", "description"),
            // title: "Description",
            // dataIndex: "description",
            width: 200
        },
        {...createColumnConfig("Fabric", "fabric_name")},
        {...createColumnConfig("Last Modified Time", "modified_time")},
        {
            title: "Operation",
            render: (_, record) => (
                <Space size="middle" className={styles.actionLink}>
                    <a onClick={() => edit_template("Edit", record)}>Edit</a>
                    <a
                        onClick={() =>
                            confirmModalAction("Are you sure you want to delete the VLAN Domain Group?", () =>
                                delete_template(record.id)
                            )
                        }
                    >
                        Delete
                    </a>
                </Space>
            )
        }
    ];

    const matchModes = createMatchMode([
        {name: "name", matchMode: "exact"},
        {name: "last_modified_time", matchMode: "fuzzy"}
    ]);

    const tableChange = async (pagination, filters, sorter) => {
        setSorter(sorter);
        setFilters(filters);
        await handleTableChange(
            pagination,
            filters,
            sorter,
            setPagination,
            searchFields,
            fetchVlanDomainInfo,
            "",
            setData,
            matchModes,
            setLoading
        );
    };

    useEffect(() => {
        fetchData().then(() => {
            const [sortedColumn, sortedOrder] = checkSortedColumn(userColumns);
            if (sortedColumn) {
                sorter.field = sortedColumn;
                sorter.order = sortedOrder;
                tableChange("", "", sorter);
            }
        });
    }, []);

    useEffect(() => {
        fetchData().then();
    }, [searchFields]);

    return (
        <div>
            <Space size={16} style={{marginBottom: "20px"}}>
                <Button
                    type="primary"
                    block
                    onClick={() => {
                        // setNodesInfo({});
                        // setCreateNodeModal(true)
                        navigate(`/resource/resource_interconnection/vlan_domain/create_vlan_domain_group`, {
                            state: {actionType: "Create"}
                        });
                    }}
                >
                    <Icon component={addSvg} />
                    VLAN Domain Group
                </Button>
            </Space>
            <GlobalSearchInput onChange={handleSearchChange} />
            <div>
                <Table
                    columns={userColumns}
                    bordered
                    rowKey={record => record.id}
                    loading={loading}
                    dataSource={data}
                    pagination={pagination}
                    onChange={tableChange}
                />
            </div>
        </div>
    );
};

export default VLANDomain;
