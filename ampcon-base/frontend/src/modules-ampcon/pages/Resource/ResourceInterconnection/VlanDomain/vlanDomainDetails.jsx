import React, {useRef, useEffect, useState, forwardRef} from "react";
import {useNavigate, useLocation} from "react-router-dom";
import {
    Card,
    Form,
    Select,
    Tooltip,
    Tag,
    Space,
    Button,
    Table,
    Modal,
    Divider,
    Input,
    InputNumber,
    message,
    Steps
} from "antd";
import Icon, {
    ArrowLeftOutlined,
    QuestionCircleOutlined,
    PlusOutlined,
    LineOutlined,
    FileAddFilled
} from "@ant-design/icons";
import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";
import {createColumnConfig} from "@/modules-ampcon/components/custom_table";
import SyncSvg from "@/modules-ampcon/pages/Topo/Topology/resource/sync.svg?react";
import SyncSvgDis from "@/modules-ampcon/pages/Topo/Topology/resource/sync_disabled.svg?react";

import FabricTopo from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_topo";
// import API Topo
import {
    viewFabric,
    list_vlan_domain,
    add_vlan_domain,
    edit_vlan_domain,
    fetchFabricInfo
} from "@/modules-ampcon/apis/node_addition_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

/**
 *  VLAN Domain
 */
const VLANDomainDetails = () => {
    const navigate = useNavigate();
    const {state} = useLocation();
    const [form] = Form.useForm(); // 创建 Form 实例
    const [vlanDomainData, setVlanDomainData] = useState([]);
    const [vlanDomainMlagData, setVlanDomainMlagData] = useState([]);
    const [vlanDomainCopyData, setVlanDomainCopyData] = useState([]);
    const [editDisabled, setEditDisabled] = useState(false);
    const [editModalopen, setEditModalOpen] = useState(false);
    const [editModalData, setEditModalData] = useState([]);

    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRow, setSelectedRow] = useState([]);

    const [templateTitle, setTemplateTitle] = useState(state?.actionType);
    const [fabricList, setFabricList] = useState([]);

    const [topoInfo, setTopoInfo] = useState({});
    const [nodesData, setNodesData] = useState([]);

    const [fabricTopoId, setFabricTopoId] = useState();
    const [vlanDomainGroupId, setVlanDomainGroupId] = useState(0);
    const [syncDisalbed, setSyncDisabled] = useState(false);
    const [recordName, setRecordName] = useState();

    const NAME_MATCH_REGEX = /^[\s\w-]+$/;
    const [saveStatus, setSaveStatus] = useState(false);

    // 表格分页数据
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [sortedInfo, setSortedInfo] = useState({});

    const deviceAllocationStepRef = useRef(null);

    const fetchData = async () => {
        try {
            const response = await fetchFabricInfo();
            const newFabricList = response.data.filter(
                item => item.has_vlan_domain === false && templateTitle === "Create"
            );
            setFabricList(newFabricList);
        } catch (error) {
            // error
        }
    };

    const handleSort = (dataIndex, getValueFn) => {
        return (a, b) => {
            const getValue = (obj, keyPath) => {
                const keys = keyPath.split(".");
                let value = obj;
                for (const key of keys) {
                    if (key.includes("[")) {
                        const [prop, indexStr] = key.split("[");
                        const index = parseInt(indexStr.replace("]", ""), 10);
                        value = value && value[prop] && Array.isArray(value[prop]) && value[prop][index];
                    } else {
                        value = value ? value[key] : null;
                    }
                    if (!value) break;
                }
                return value;
            };

            const valueA = getValueFn ? getValueFn(a) : getValue(a, dataIndex);
            const valueB = getValueFn ? getValueFn(b) : getValue(b, dataIndex);

            if (typeof valueA === "string" && typeof valueB === "string") {
                return valueA.localeCompare(valueB);
            }
            return (valueA || 0) - (valueB || 0);
        };
    };

    const mlagCalculation = data => {
        const newArr = [];
        const processedIndices = new Set();
        const vlanDomainGroup = {};
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (item.vlan_domain_id) {
                if (!vlanDomainGroup[item.vlan_domain_id]) {
                    vlanDomainGroup[item.vlan_domain_id] = [];
                }
                vlanDomainGroup[item.vlan_domain_id].push(item);
            }
        }
        for (const group of Object.values(vlanDomainGroup)) {
            if (group.length > 1) {
                const logicNames = group.map(item => item.logic_name);
                newArr.push(logicNames);
                group.forEach(item => processedIndices.add(data.indexOf(item)));
            }
        }
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (item.vlan_domain_id && !processedIndices.has(i)) {
                newArr.push([item.logic_name]);
                processedIndices.add(i);
            }
        }

        // 处理 vlan_domain_id 不存在的情况
        const prefixGroup = {};
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (!item.vlan_domain_id && !processedIndices.has(i)) {
                if (item.strategy === "MLAG" && item.type === "leaf") {
                    const prefix = item.logic_name.slice(0, -2);
                    if (!prefixGroup[prefix]) {
                        prefixGroup[prefix] = [];
                    }
                    prefixGroup[prefix].push(item);
                }
            }
        }
        for (const group of Object.values(prefixGroup)) {
            const validGroup = group.filter(item => /_\d$/.test(item.logic_name));
            if (validGroup.length > 1) {
                const logicNames = validGroup.map(item => item.logic_name);
                newArr.push(logicNames);
                validGroup.forEach(item => processedIndices.add(data.indexOf(item)));
            }
        }
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (!item.vlan_domain_id && !processedIndices.has(i)) {
                newArr.push([item.logic_name]);
                processedIndices.add(i);
            }
        }

        setVlanDomainMlagData(newArr);
        return newArr;
    };

    // 定义合并单元格的逻辑
    const getMergedCellData = data => {
        const mergedData = [];
        const addedLogicNames = new Set();
        const vlanIdGroups = {};
        const logicNamePrefixGroups = {};

        // 处理 vlan_domain_id 有值的情况
        for (const item of data) {
            if (item.vlan_domain_id !== null) {
                if (!vlanIdGroups[item.vlan_domain_id]) {
                    vlanIdGroups[item.vlan_domain_id] = [];
                }
                vlanIdGroups[item.vlan_domain_id].push(item);
            }
        }

        for (const vlanId in vlanIdGroups) {
            if (Object.hasOwn(vlanIdGroups, vlanId)) {
                const group = vlanIdGroups[vlanId];
                if (group.length > 1) {
                    group[0].rowSpan = group.length;
                    for (let i = 1; i < group.length; i++) {
                        group[i].rowSpan = 0;
                    }
                }
                for (const obj of group) {
                    if (!addedLogicNames.has(obj.logic_name)) {
                        mergedData.push(obj);
                        addedLogicNames.add(obj.logic_name);
                    }
                }
            }
        }

        // 处理 vlan_domain_id 为 null 的情况
        for (const item of data) {
            if (item.vlan_domain_id === null) {
                if (item.strategy === "MLAG" && item.type === "leaf") {
                    const prefix = item.logic_name.slice(0, -2);
                    const suffix = item.logic_name.slice(-2);
                    if (suffix === "_1" || suffix === "_2") {
                        if (!logicNamePrefixGroups[prefix]) {
                            logicNamePrefixGroups[prefix] = [];
                        }
                        logicNamePrefixGroups[prefix].push(item);
                    } else if (!addedLogicNames.has(item.logic_name)) {
                        mergedData.push(item);
                        addedLogicNames.add(item.logic_name);
                    }
                } else if (!addedLogicNames.has(item.logic_name)) {
                    mergedData.push(item);
                    addedLogicNames.add(item.logic_name);
                }
            }
        }

        for (const prefix in logicNamePrefixGroups) {
            if (Object.hasOwn(logicNamePrefixGroups, prefix)) {
                const group = logicNamePrefixGroups[prefix];
                if (group.length === 2) {
                    group[0].rowSpan = 2;
                    group[1].rowSpan = 0;
                }
                for (const obj of group) {
                    if (!addedLogicNames.has(obj.logic_name)) {
                        mergedData.push(obj);
                        addedLogicNames.add(obj.logic_name);
                    }
                }
            }
        }

        // 重新按原数据顺序排列
        const finalMergedData = [];
        for (const item of data) {
            for (const mergedItem of mergedData) {
                if (mergedItem.logic_name === item.logic_name) {
                    finalMergedData.push(mergedItem);
                    break;
                }
            }
        }
        return finalMergedData;
    };

    // 无vlan_domain_id的VD需要拼接vlan_domain_name新增并保存
    const addDataFormat = data => {
        const mergedData = [];
        const processed = new Set();
        const existingVlanDomainIds = data.filter(obj => !obj.vlan_domain_id) || [];
        for (let i = 0; i < existingVlanDomainIds.length; i++) {
            if (processed.has(i)) continue;
            const currentItem = existingVlanDomainIds[i];
            if (currentItem.strategy === "MLAG" && currentItem.type === "leaf") {
                const prefix = currentItem.logic_name.slice(0, -2);
                const suffix = currentItem.logic_name.slice(-2);
                if (suffix === "_1" || suffix === "_2") {
                    let pairIndex = -1;
                    for (let j = i + 1; j < existingVlanDomainIds.length; j++) {
                        if (processed.has(j)) continue;
                        const nextItem = existingVlanDomainIds[j];
                        if (nextItem.strategy === "MLAG" && nextItem.type === "leaf") {
                            const nextPrefix = nextItem.logic_name.slice(0, -2);
                            const nextSuffix = nextItem.logic_name.slice(-2);
                            if (
                                nextPrefix === prefix &&
                                ((suffix === "_1" && nextSuffix === "_2") || (suffix === "_2" && nextSuffix === "_1"))
                            ) {
                                pairIndex = j;
                                break;
                            }
                        }
                    }

                    if (pairIndex !== -1) {
                        const pairItem = existingVlanDomainIds[pairIndex];
                        mergedData.push({
                            fabric_topo_id: fabricTopoId,
                            vlan_domain_name: currentItem.vlan_domain_name,
                            node_logic_names: [currentItem.logic_name, pairItem.logic_name],
                            bridge_domain_pool_range: [],
                            vrf_vlan_pool_range: [],
                            device_type: "server"
                        });
                        processed.add(pairIndex);
                        continue;
                    }
                }
            }

            mergedData.push({
                fabric_topo_id: fabricTopoId,
                vlan_domain_name: currentItem.vlan_domain_name,
                node_logic_names: [currentItem.logic_name],
                bridge_domain_pool_range: [],
                vrf_vlan_pool_range: [],
                device_type: "server"
            });
        }

        return mergedData;
    };

    // 处理添加的下发数据,需要拼接vlan_domain_name新增并保存
    const transformArray = data => {
        const resultMap = new Map();

        // Process each item in array1
        data.forEach(item => {
            const {vlan_domain_name, logic, bridge_domain, vrf_vlan} = item;

            // Initialize the group if it doesn't exist
            if (!resultMap.has(vlan_domain_name)) {
                resultMap.set(vlan_domain_name, {
                    fabric_topo_id: fabricTopoId,
                    vlan_domain_name,
                    node_logic_names: [],
                    bridge_domain_pool_range: [],
                    vrf_vlan_pool_range: [],
                    device_type: "server"
                });
            }

            const group = resultMap.get(vlan_domain_name);

            item.logic?.map((item, i) => {
                if (!group.node_logic_names.includes(item.logic_name)) {
                    group.node_logic_names.push(item.logic_name);
                }
            });
            // if (!group.node_logic_names.includes(logic_name)) {
            //     group.node_logic_names.push(logic_name);
            // }

            if (bridge_domain?.ranges?.length > 0) {
                bridge_domain.ranges.forEach(range => {
                    if (range.status === "success") {
                        const newRange = {
                            start: range.start_value,
                            end: range.end_value
                        };

                        const exists = group.bridge_domain_pool_range.some(
                            r => r.start === newRange.start && r.end === newRange.end
                        );

                        if (!exists) {
                            group.bridge_domain_pool_range.push(newRange);
                        }
                    }
                });
            }

            if (vrf_vlan?.ranges?.length > 0) {
                vrf_vlan.ranges.forEach(range => {
                    if (range.status === "success") {
                        const newRange = {
                            start: range.start_value,
                            end: range.end_value
                        };

                        const exists = group.vrf_vlan_pool_range.some(
                            r => r.start === newRange.start && r.end === newRange.end
                        );

                        if (!exists) {
                            group.vrf_vlan_pool_range.push(newRange);
                        }
                    }
                });
            }
        });

        const result = Array.from(resultMap.values()).map(group => ({
            ...group,
            bridge_domain_pool_range: group.bridge_domain_pool_range.sort((a, b) => a.start - b.start),
            vrf_vlan_pool_range: group.vrf_vlan_pool_range.sort((a, b) => a.start - b.start)
        }));

        return result;
    };

    const vlanDomainColumns = [
        {
            title: "VLAN Domain Name",
            dataIndex: "vlan_domain_name",
            // ...createColumnConfig("VLAN Domain Name", "vlan_domain_name"),
            // sorter: handleSort("vlan_domain_name"),
            // onCell: (record, index) => ({rowSpan: record.rowSpan}),
            render: (text, record, index) => {
                const isValid = NAME_MATCH_REGEX.test(record.vlan_domain_name);
                let tipText = "";
                if (!isValid) {
                    tipText = "VLAN Domain name can only contain letters, numbers, underscores, hyphens and spaces.";
                }

                // 检查选中的selectRowkeys中是否含有record.vlan_domian_name的值
                // const hasCommonValue = selectedRowKeys.some(item => item === record.vlan_domain_name);

                // eslint-disable-next-line no-nested-ternary
                return record.rowSpan > 0 ? (
                    templateTitle !== "View" ? (
                        <div>
                            <Input
                                maxLength="64"
                                value={record.vlan_domain_name}
                                onChange={e => {
                                    handleInputChange(index, record, "vlan_domain_name", null, null, e);
                                }}
                                style={{
                                    width: 200,
                                    borderColor: !isValid ? "red" : ""
                                }}
                            />
                            {!isValid && (
                                <p
                                    style={{
                                        width: 200,
                                        height: 20,
                                        margin: 0,
                                        overflow: "hidden",
                                        textOverflow: "ellipsis",
                                        color: !isValid ? "red" : undefined
                                    }}
                                    title={tipText}
                                >
                                    {tipText}
                                </p>
                            )}
                        </div>
                    ) : (
                        <div className={styles.overflowP} title={text}>
                            {text}
                        </div>
                    )
                ) : null;
            }
        },
        {
            title: "Sysname",
            render: (text, record, index) => {
                // const name = filterTopoValue(record.logic_name, "logic_device");
                // return (
                //     <p key={index} className={styles.overflowP} title={name}>
                //         {name}
                //     </p>
                // );
                return (
                    <div style={{margin: "8px 0"}}>
                        {record.logic?.map((line, i) => (
                            <div style={{marginBottom: 4}} key={i}>
                                {filterTopoValue(line.logic_name, "hostname")}
                            </div>
                        ))}
                    </div>
                );
            }
        },
        {title: "Role", dataIndex: "type"},
        {
            // ...createColumnConfig("Bridge Domain Range", "bridge_domain"),
            // sorter: handleSort("bridge_domain.ranges[0].start_value"),
            title: (
                <Tooltip
                    placement="right"
                    title="
                       Indicates the VLAN ranges reserved by Bridge Domain for L2VNI on the switch. The available Bridge Domain values range from 2 to 3965. The values in the Bridge Domain Range cannot be duplicated with the values in the VRF VLAN Range.Example Bridge Domain Range: 30-300,400,509,1600-2000
                    "
                >
                    <span>
                        Bridge Domain Range <QuestionCircleOutlined />
                    </span>
                </Tooltip>
            ),
            dataIndex: "bridge_domain",
            // onCell: (record, index) => ({rowSpan: record.rowSpan}),
            render: (text, record, index) => {
                const rangeStrings = record.bridge_domain?.ranges?.map(
                    range => `${range.start_value}-${range.end_value}`
                );
                const value = rangeStrings?.join(",");

                // 检查selectedRowKeys中是否含有record.vlan_domian_name的值
                // const hasCommonValue = selectedRowKeys.some(item => item === record.vlan_domain_name);

                // eslint-disable-next-line no-nested-ternary
                return record.rowSpan > 0 ? (
                    // eslint-disable-next-line no-nested-ternary
                    templateTitle !== "View" ? (
                        // eslint-disable-next-line no-nested-ternary
                        JSON.stringify(record?.bridge_domain) === "{}" ? (
                            <PlusOutlined
                                onClick={() => {
                                    addRange("bridge_domain", record, index);
                                }}
                                style={{marginLeft: 8, color: "#B2B2B2"}}
                            />
                        ) : record?.bridge_domain?.ranges.length === 0 ? (
                            <PlusOutlined
                                onClick={() => {
                                    addRange("bridge_domain", record, index);
                                }}
                                style={{marginLeft: 8, color: "#B2B2B2"}}
                            />
                        ) : (
                            <ul>
                                {record?.bridge_domain?.ranges?.map((item, rangeIndex) => (
                                    <li style={{marginBottom: 8, listStyle: "none"}} key={rangeIndex}>
                                        <InputNumber
                                            min={2}
                                            max={3965}
                                            name={item.id + isNaN(item.start_value) ? " " : item.start_value}
                                            value={item.start_value}
                                            status={item.status}
                                            onChange={e => {
                                                handleInputChange(
                                                    index,
                                                    record,
                                                    "bridge_domain",
                                                    "start_value",
                                                    rangeIndex,
                                                    e
                                                );
                                                rangeInputValidator(
                                                    index,
                                                    rangeIndex,
                                                    "bridge_domain",
                                                    "start_value",
                                                    e
                                                );
                                            }}
                                            style={{width: 140}}
                                            placeholder="Start Value,Range(2 - 3965)"
                                        />
                                        &nbsp; — &nbsp;
                                        <InputNumber
                                            min={2}
                                            max={3965}
                                            name={item.id + isNaN(item.end_value) ? " " : item.end_value}
                                            value={item.end_value}
                                            status={item.status}
                                            onChange={e => {
                                                handleInputChange(
                                                    index,
                                                    record,
                                                    "bridge_domain",
                                                    "end_value",
                                                    rangeIndex,
                                                    e
                                                );
                                                rangeInputValidator(index, rangeIndex, "bridge_domain", "end_value", e);
                                            }}
                                            style={{width: 140}}
                                            placeholder="End Value,Range(2 - 3965)"
                                        />
                                        <PlusOutlined
                                            onClick={() => {
                                                addRange("bridge_domain", record, index);
                                            }}
                                            style={{marginLeft: 8, color: "#B2B2B2"}}
                                        />
                                        <LineOutlined
                                            onClick={() => {
                                                deleteRange("bridge_domain", index, rangeIndex);
                                            }}
                                            style={{marginLeft: 8, color: "#B2B2B2"}}
                                        />
                                        {item?.errorTips !== "" && (
                                            <p className={styles.errorTips}>{item?.errorTips}</p>
                                        )}
                                    </li>
                                ))}
                            </ul>
                        )
                    ) : (
                        <div rowSpan={record.rowSpan}>{value || `--`}</div>
                    )
                ) : null;
            }
        },
        {
            // ...createColumnConfig("VRF VLAN Range", "vrf_vlan"),
            // sorter: handleSort("vrf_vlan.ranges[0].start_value"),
            title: (
                <Tooltip
                    placement="right"
                    title="
                        Indicates the VLAN ranges reserved by VRF VLAN for L3VNI on the switch. The available VRF VLAN values range from 2 to 3965. The values in the VRF VLAN Range cannot be duplicated with the values in the Bridge Domain Range.Example Bridge Domain Range: 500-505,600,66
                    "
                >
                    <span>
                        VRF VLAN Range <QuestionCircleOutlined />
                    </span>
                </Tooltip>
            ),
            dataIndex: "vrf_vlan",
            // onCell: (record, index) => ({rowSpan: record.rowSpan}),
            render: (text, record, index) => {
                const rangeStrings = record.vrf_vlan?.ranges?.map(range => `${range.start_value}-${range.end_value}`);
                const value = rangeStrings?.join(",");

                // 检查selectedRowKeys中是否含有record.vlan_domian_name的值
                // const hasCommonValue = selectedRowKeys.some(item => item === record.vlan_domain_name);

                // eslint-disable-next-line no-nested-ternary
                return record.rowSpan > 0 ? (
                    // eslint-disable-next-line no-nested-ternary
                    templateTitle !== "View" ? (
                        // eslint-disable-next-line no-nested-ternary
                        JSON.stringify(record?.vrf_vlan) === "{}" ? (
                            <PlusOutlined
                                onClick={() => {
                                    addRange("vrf_vlan", record, index);
                                }}
                                style={{marginLeft: 8, color: "#B2B2B2"}}
                            />
                        ) : record?.vrf_vlan?.ranges?.length === 0 ? (
                            <PlusOutlined
                                onClick={() => {
                                    addRange("vrf_vlan", record, index);
                                }}
                                style={{marginLeft: 8, color: "#B2B2B2"}}
                            />
                        ) : (
                            <ul>
                                {record?.vrf_vlan?.ranges?.map((item, rangeIndex) => (
                                    <li style={{marginBottom: 8, listStyle: "none"}} key={rangeIndex}>
                                        <InputNumber
                                            min={2}
                                            max={3965}
                                            name={item.id + isNaN(item.start_value) ? " " : item.start_value}
                                            value={item.start_value}
                                            status={item.status}
                                            onChange={e => {
                                                handleInputChange(
                                                    index,
                                                    record,
                                                    "vrf_vlan",
                                                    "start_value",
                                                    rangeIndex,
                                                    e
                                                );
                                                rangeInputValidator(index, rangeIndex, "vrf_vlan", "start_value", e);
                                            }}
                                            style={{width: 140}}
                                            placeholder="Start Value,Range(2 - 3965)"
                                        />
                                        &nbsp; — &nbsp;
                                        <InputNumber
                                            min={2}
                                            max={3965}
                                            name={item.id + isNaN(item.end_value) ? " " : item.end_value}
                                            value={item.end_value}
                                            status={item.status}
                                            onChange={e => {
                                                handleInputChange(
                                                    index,
                                                    record,
                                                    "vrf_vlan",
                                                    "end_value",
                                                    rangeIndex,
                                                    e
                                                );
                                                rangeInputValidator(index, rangeIndex, "vrf_vlan", "end_value", e);
                                            }}
                                            style={{width: 140}}
                                            placeholder="End Value,Range(2 - 3965)"
                                        />
                                        <PlusOutlined
                                            onClick={() => {
                                                addRange("vrf_vlan", record, index);
                                            }}
                                            style={{marginLeft: 8, color: "#B2B2B2"}}
                                        />
                                        <LineOutlined
                                            onClick={() => {
                                                deleteRange("vrf_vlan", index, rangeIndex);
                                            }}
                                            style={{marginLeft: 8, color: "#B2B2B2"}}
                                        />
                                        {item?.errorTips !== "" && (
                                            <p className={styles.errorTips}>{item?.errorTips}</p>
                                        )}
                                    </li>
                                ))}
                            </ul>
                        )
                    ) : (
                        <div rowSpan={record.rowSpan}>{value || `--`}</div>
                    )
                ) : null;
            }
        },
        {
            // ...createColumnConfig("Status", "status"),
            // sorter: handleSort("status"),
            title: "Status",
            dataIndex: "status",
            render: (text, record) => {
                let val = "";
                if (record?.bridge_domain?.is_in_use || record?.vrf_vlan?.is_in_use) val = "Used";
                else val = "Unused";
                return <Tag className={val === "Used" ? styles.successTag : styles.uncheckedTag}>{val}</Tag>;
            }
        }
        // {
        //     title: "Operation",
        //     // onCell: (record, index) => ({rowSpan: record.rowSpan}),
        //     render: (text, record) => {
        //         return templateTitle === "Check" ? (
        //             <span style={{cursor: "not-allowed", color: "#b2b2b2"}}>Edit</span>
        //         ) : (
        //             <Space size="middle" className={styles.actionLink}>
        //                 <a
        //                     onClick={() => {
        //                         openEditModalModal("edit", record);
        //                     }}
        //                 >
        //                     Edit
        //                 </a>
        //             </Space>
        //         );
        //     }
        // }
    ];

    const rowSelection = {
        selectedRowKeys,
        selectedRow,
        onChange: (keys, rows) => {
            const newKeys = [];
            const allLogicNames = vlanDomainMlagData.flat();

            // keys.forEach(key => {
            //     vlanDomainMlagData.forEach(subArray => {
            //         if (subArray.includes(key)) {
            //             subArray.forEach(data => {
            //                 if (!newKeys.includes(data)) {
            //                     newKeys.push(data);
            //                 }
            //             });
            //         }
            //     });
            // });

            // // 处理取消选中的情况
            // const deselectedKeys = selectedRowKeys.filter(k => !keys.includes(k));
            // deselectedKeys.forEach(key => {
            //     vlanDomainMlagData.forEach(subArray => {
            //         if (subArray.includes(key)) {
            //             subArray.forEach(data => {
            //                 const index = newKeys.indexOf(data);
            //                 if (index > -1) {
            //                     newKeys.splice(index, 1);
            //                 }
            //             });
            //         }
            //     });
            // });

            // const newRows = [];
            // rows.forEach(item => {
            //     if (item.vlan_domain_id) {
            //         const parentVD = vlanDomainData.filter(obj => obj.vlan_domain_id === item.vlan_domain_id);
            //         parentVD?.forEach(item => {
            //             newRows.push(item);
            //         });
            //     } else if (item.strategy === "MLAG" && item.type === "leaf") {
            //         newRows.push(item);
            //         const name = `${item.logic_name.slice(0, -1)}2`;
            //         const parentVD = vlanDomainData.find(obj => obj.logic_name === name);
            //         if (parentVD) {
            //             newRows.push(parentVD);
            //         }
            //     } else if (item.strategy !== "MLAG") {
            //         newRows.push(item);
            //     }
            // });

            // setSelectedRow(newRows);
            // setSelectedRowKeys(newKeys);

            setSelectedRowKeys(keys);
        },
        getCheckboxProps: record => ({
            disabled: templateTitle === "View" ? true : record.disabled,
            name: record.vlan_domain_name
        })
        // onCell: record => ({rowSpan: record.rowSpan})
    };

    const openEditModalModal = (type, record) => {
        // setEditModalData([]);
        // setEditModalOpen(true);
        if (type === "edit") {
            // const currentSelectRow = vlanDomainData.filter(key => key.vlan_domain_id === record.vlan_domain_id);
            // setEditModalData(currentSelectRow);
            // console.log(currentSelectRow);
            const currentSelectRow = vlanDomainData
                .filter(key => key.vlan_domain_name === record.vlan_domain_name)
                .map(item => item.vlan_domain_name);
            setSelectedRowKeys(currentSelectRow);
        } else if (type === "batch") {
            const uniqueData = Array.from(new Map(selectedRow.map(item => [item.logic_name, item])).values());
            // setEditModalData(uniqueData);

            const newSelectRows = uniqueData.map(item => item.vlan_domain_name);
            setSelectedRowKeys(newSelectRows);
        }
    };

    /* 
    处理获取的vlan domain数据，将logic_name进行合并
    */
    const transformData = data => {
        const result = [];
        const domainMap = {};

        // 首先按vlan_domain_name分组
        data.forEach(item => {
            const domainName = item.vlan_domain_name;

            if (!domainMap[domainName]) {
                // 创建新的VLAN Domain条目
                domainMap[domainName] = {
                    bridge_domain: item.bridge_domain,
                    vlan_domain_id: item.vlan_domain_id,
                    vlan_domain_name: domainName,
                    vrf_vlan: item.vrf_vlan,
                    rowSpan: item.rowSpan,
                    index: item.index,
                    type: item.type,
                    strategy: item.strategy,
                    logic: [] // 初始化logic数组
                };
                result.push(domainMap[domainName]);
            }

            // 添加设备到logic数组
            domainMap[domainName].logic.push({
                logic_name: item.logic_name,
                logic_device_id: item.logic_device_id
            });
        });

        return result;
    };

    // 获取VLAN Domain数据
    const fetchVlanDomainData = async () => {
        setSelectedRowKeys([]);
        setSelectedRow([]);
        try {
            const res = await list_vlan_domain({fabric_topo_id: fabricTopoId});
            if (res.status === 200) {
                const newData = res?.data.map((record, index) => {
                    if (record.vlan_domain_name === "") {
                        const groupValue = filterTopoValue(record.logic_name, "group");
                        const leafNameValue = filterTopoValue(record.logic_name, "leaf_name") || "";
                        record.vlan_domain_name = leafNameValue ? `${groupValue}_${leafNameValue}` : groupValue;
                    }
                    record.type = filterTopoValue(record.logic_name, "type");
                    record.hostname = filterTopoValue(record.logic_name, "hostname");
                    record.strategy = filterTopoValue(record.logic_name, "strategy");
                    record.rowSpan = 1;
                    record.index = index;
                    return record;
                });
                mlagCalculation(newData);
                // setVlanDomainData(getMergedCellData(newData));
                setVlanDomainData(transformData(getMergedCellData(newData)));
                setVlanDomainCopyData(JSON.parse(JSON.stringify(newData)));
                // console.log(transformData(getMergedCellData(newData)));
                // const newAddData = JSON.parse(JSON.stringify(newData));
                // if (addDataFormat(newAddData).length > 0) add_vlanDomain(addDataFormat(newAddData),form.getFieldsValue());
            }
        } catch (error) {
            // error
        }
    };

    // 获取topoInfo信息
    const fetchTopoData = async () => {
        try {
            const res = await viewFabric({fabric_topo_id: fabricTopoId});
            if (res.status === 200) {
                setTopoInfo(res.data);
                setNodesData(res.data?.fabric_config?.topology?.nodes);
            }
        } catch (error) {
            // error
        }
        const nodes = topoInfo?.fabric_config?.topology?.nodes || [];
        setVlanDomainData(nodes);
    };

    // 根据logic_name从nodesData中获取指定的数据
    const filterTopoValue = (logicName, name) => {
        let value = "";

        nodesData?.forEach(item => {
            if (item.logic_device === logicName)
                if (item[name]) {
                    value = item[name];
                } else {
                    value = item.node_info[name];
                }
        });

        return value;
    };

    /**
     * 进入VLAN Domain，对于缺少vlan_domain_id和vlan_domain_name的数据默认进行新增
     * vrf_vlan_pool_range、bridge_domain_pool_range为空
     */
    const add_vlanDomain = async (data, values) => {
        const sendData = {
            vlan_domain_group_name: values.vdGroupName,
            description: values.description,
            vlan_domain_info: data
        };
        try {
            const res = await add_vlan_domain(sendData);
            if (res.status === 200) {
                message.success(res.info);
                navigate("/resource/resource_interconnection/vlan_domain");
            } else {
                message.error(res.info);
            }
        } catch (error) {
            // error
        }
    };

    /**
     * 点击Edit VLAN Domain弹框的Save按钮，调用edit_vlanDomain编辑方法
     */
    const edit_vlanDomain = async (data, values) => {
        const sendData = {
            vlan_domain_group_name: values.vdGroupName,
            description: values.description,
            vlan_domain_group_id: vlanDomainGroupId,
            vlan_domain_info: []
        };

        data.forEach((editItem, editIndex) => {
            const currentObj = vlanDomainCopyData?.find(obj => obj.vlan_domain_id === editItem.vlan_domain_id);

            const obj = {
                pool_name: editItem.vlan_domain_name,
                vlan_domain_id: editItem.vlan_domain_id,
                bridge_domain_ranges: {
                    old_range: currentObj?.bridge_domain?.ranges,
                    new_range: editItem?.bridge_domain?.ranges?.map(item => ({
                        end: item.end_value,
                        start: item.start_value
                    }))
                },
                vrf_vlan_ranges: {
                    old_range: currentObj?.vrf_vlan?.ranges,
                    new_range: editItem?.vrf_vlan?.ranges?.map(item => ({
                        end: item.end_value,
                        start: item.start_value
                    }))
                }
            };
            sendData.vlan_domain_info.push(obj);
        });

        try {
            const res = await edit_vlan_domain(sendData);
            if (res.status === 200) {
                message.success(res.info);
                // setEditModalOpen(false);
                // fetchVlanDomainData();
                navigate("/resource/resource_interconnection/vlan_domain");
            } else {
                message.error(res.info);
            }
        } catch (error) {
            // error
        }
    };

    const paginationConfig = {
        current: currentPage,
        pageSize,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50", "100"],
        showTotal: (total, range) => {
            const start = range[0];
            const end = range[1];
            return `${start}-${end} of ${total} items`;
        },
        onShowSizeChange: (current, size) => {
            setPageSize(size);
            setCurrentPage(1);
        },
        onChange: page => setCurrentPage(page),
        total: vlanDomainData?.length
    };

    // fabric select
    const handleFabricChange = e => {
        const fabricTopoId = fabricList.filter(item => item.id === e).map(item => item.fabric_topo_id)[0];
        setFabricTopoId(fabricTopoId);
    };

    // 点击save按钮，提交表单
    const handleFormSubmit = async values => {
        if (templateTitle === "Create") {
            add_vlanDomain(transformArray(vlanDomainData), values);
        } else {
            edit_vlanDomain(vlanDomainData, values);
        }
    };

    // 当添加时，第一列有数据时点击可以填充表格后面的列的数据
    const syncData = () => {
        if (!vlanDomainData[0]) return;

        const recordBridge = JSON.parse(JSON.stringify(vlanDomainData[0].bridge_domain));
        const recordVrf = JSON.parse(JSON.stringify(vlanDomainData[0].vrf_vlan));
        const newData = JSON.parse(JSON.stringify(vlanDomainData));
        const newData1 = newData.map((item, index) => {
            if (index === 0) return {...item};

            return {
                ...item,
                bridge_domain: {...recordBridge},
                vrf_vlan: {...recordVrf}
            };
        });
        setVlanDomainData(newData1);
    };

    // 表格交互
    const addRange = (name, record, index, logicName) => {
        const newData = [...vlanDomainData];
        // 根据页码和页码条数生成新的index
        const newIndex = index + (currentPage - 1) * pageSize;

        if (!newData[newIndex][name].ranges) {
            newData[newIndex][name].ranges = [];
        }
        newData[newIndex][name].ranges.push({start_value: "", end_value: ""});

        setVlanDomainData(newData);
    };

    const handleInputChange = (index, record, recordName, rangeName, rangeIndex, e) => {
        // 根据页码和页码条数生成新的index
        const newIndex = index + (currentPage - 1) * pageSize;
        const newData = [...vlanDomainData];
        if (recordName === "vlan_domain_name") {
            // record[recordName] = e.target.value;
            newData[newIndex][recordName] = e.target.value;
        } else {
            // record[recordName].ranges[rangeIndex][rangeName] = e;

            // 深拷贝当前行的 recordName 对象（如 bridge_domain 或 vrf_vlan）
            newData[newIndex][recordName] = {
                ...newData[newIndex][recordName],
                ranges: newData[newIndex][recordName].ranges.map((range, i) =>
                    i === rangeIndex ? {...range, [rangeName]: e} : range
                )
            };

            // if (
            //     (rangeName === "start_value" && !record[recordName].ranges[rangeIndex].end_value) ||
            //     record[recordName].ranges[rangeIndex].end_value < record[recordName].ranges[rangeIndex].start_value
            // ) {
            //     record[recordName].ranges[rangeIndex].end_value = e;
            // }

            // 处理 end_value 逻辑
            if (rangeName === "start_value") {
                const currentRange = newData[newIndex][recordName].ranges[rangeIndex];
                if (!currentRange.end_value || currentRange.end_value < currentRange.start_value) {
                    currentRange.end_value = e;
                }
            }
        }
        // newData.splice(newIndex, 1, record);
        setVlanDomainData(newData);
    };

    // Bridge Domain Range和VRF VLAN Range输入检验
    const rangeInputValidator = (recordIndex, rangeIndex, recordName, rangeName, e) => {
        // 根据页码和页码条数生成新的index
        const newIndex = recordIndex + (currentPage - 1) * pageSize;
        const newData = JSON.parse(JSON.stringify(vlanDomainData));
        newData[newIndex][recordName].ranges[rangeIndex].status = "success";
        newData[newIndex][recordName].ranges[rangeIndex].errorTips = "";

        const current_rangObj = newData[newIndex][recordName].ranges[rangeIndex];
        const current_rangValue = [current_rangObj.start_value, current_rangObj.end_value];

        // 1. 如果end_value存在有效值，start_value不能为空
        if (current_rangValue[1] !== "" && !current_rangValue[0]) {
            newData[newIndex][recordName].ranges[rangeIndex].status = "error";
            newData[newIndex][recordName].ranges[rangeIndex].errorTips = "Start value cannot be empty";
            setVlanDomainData(newData);
            return;
        }

        // 2. 如果end_value小于start_value
        if (current_rangValue[1] < current_rangValue[0]) {
            newData[newIndex][recordName].ranges[rangeIndex].status = "error";
            newData[newIndex][recordName].ranges[rangeIndex].errorTips = "End value must be greater than start value";
            setVlanDomainData(newData);
            return;
        }
        newData[newIndex][recordName].ranges[rangeIndex].status = "success";
        newData[newIndex][recordName].ranges[rangeIndex].errorTips = "";
        setVlanDomainData(newData);

        // 3.当前输入的值是否存在于Bridge Domain Range和VRF VLAN Range某段区间内
        // 排除当前rangeIndex
        let new_range = [];
        if (recordName === "bridge_domain") {
            const newArray = newData.flatMap(data => {
                return (
                    data.bridge_domain?.ranges
                        ?.filter((_, index) => index !== rangeIndex)
                        .map(item => [item.start_value, item.end_value]) || []
                );
            });

            const newArray2 = newData.flatMap(data => {
                return data.vrf_vlan?.ranges?.map(item => [item.start_value, item.end_value]) || [];
            });

            new_range = [...newArray, ...newArray2];
        }

        if (recordName === "vrf_vlan") {
            const newArray = newData.flatMap(data => {
                return data.bridge_domain?.ranges?.map(item => [item.start_value, item.end_value]) || [];
            });
            const newArray2 = newData.flatMap(data => {
                return (
                    data.vrf_vlan?.ranges
                        ?.filter((_, index) => index !== rangeIndex)
                        .map(item => [item.start_value, item.end_value]) || []
                );
            });
            new_range = [...newArray, ...newArray2];
        }

        // 提取 current_rangValue 中的唯一值以便于比较
        const uniqueA = [...new Set(current_rangValue)];

        // // 排除与 currentValue 重复的数据
        // const filteredRange = new_range.filter(range => {
        //     return range[0] !== current_rangObj.start_value || range[1] !== current_rangObj.end_value;
        // });

        // 判断 currentValue 是否和某个数字区间重叠
        let isOverlap = false;
        new_range.forEach(range => {
            const [start1, end1] = [current_rangObj.start_value, current_rangObj.end_value];
            const [start2, end2] = range;
            if (start1 <= end2 && start2 <= end1) {
                isOverlap = true;
            }
        });
        if (isOverlap) {
            newData[newIndex][recordName].ranges[rangeIndex].status = "error";
            newData[newIndex][recordName].ranges[rangeIndex].errorTips = "Conflict with other ranges";
        }
        // 遍历 current_rangValue，检查与 current_rangValue 重复的次数
        const duplicates = new_range.filter(item => uniqueA.some(value => item[0] === value && item[1] === value));
        // 根据重复的数量进行相应处理
        if (duplicates.length > 1) {
            newData[newIndex][recordName].ranges[rangeIndex].status = "error";
            newData[newIndex][recordName].ranges[rangeIndex].errorTips = "Conflict with other ranges";
        } else if (duplicates.length === 1) {
            // 删除 new_range 中重复的数据
            const index = new_range.indexOf(duplicates[0]);
            if (index !== -1) {
                new_range.splice(index, 1);
            }
        } else {
            return;
        }

        // 遍历 current_rangValue 数组，检查每个值是否在 ranges 中
        const results = current_rangValue.map(value => {
            const isInRange = new_range.some(range => value >= range[0] && value <= range[1]);
            return {value, inRange: isInRange};
        });
        results.forEach(result => {
            if (result.inRange) {
                newData[newIndex][recordName].ranges[rangeIndex].status = "error";
                newData[newIndex][recordName].ranges[rangeIndex].errorTips = "Conflict with other ranges";
                return;
            }
        });
        setVlanDomainData(newData);
    };

    const deleteRange = (name, index, rangeIndex, logicName) => {
        const newData = [...vlanDomainData];
        // 根据页码和页码条数生成新的index
        const newIndex = index + (currentPage - 1) * pageSize;

        newData[newIndex][name].ranges.splice(rangeIndex, 1);
        setVlanDomainData(newData);
    };

    const handleNodeClick = node => {
        deviceAllocationStepRef?.current?.showEditDeviceModal(node);
    };

    /**
     * 监听弹框的table数据，如果VLAN Domain Name为空，则无法进行Save
     */
    useEffect(() => {
        if (vlanDomainData) {
            setSaveStatus(false);

            // 去掉数组中vlan_domain_name相同的数组
            const uniqueArray = vlanDomainData.reduce((acc, current) => {
                const x = acc.find(item => item.vlan_domain_name === current.vlan_domain_name);
                if (!x) {
                    return acc.concat([current]);
                }
                return acc;
            }, []);

            const vlanDomainNameError = uniqueArray.some(item => {
                const isVlanDomainNameInvalid = !item.vlan_domain_name || item.vlan_domain_name.trim() === "";
                const isBridgeDomainEmpty = !item.bridge_domain || Object.keys(item.bridge_domain).length === 0; // bridge_domain不存在或空对象
                const isBridgeDomainRangesEmpty =
                    !isBridgeDomainEmpty && (!item.bridge_domain.ranges || item.bridge_domain.ranges.length === 0);
                const isVrfVlanEmpty = !item.vrf_vlan || Object.keys(item.vrf_vlan).length === 0; // vrf_vlan不存在或空对象
                const isVrfVlanRangesEmpty =
                    !isVrfVlanEmpty && (!item.vrf_vlan.ranges || item.vrf_vlan.ranges.length === 0);

                return (
                    isVlanDomainNameInvalid ||
                    isBridgeDomainEmpty ||
                    isBridgeDomainRangesEmpty ||
                    isVrfVlanEmpty ||
                    isVrfVlanRangesEmpty
                );
            });

            const vlanDomainIDError =
                // eslint-disable-next-line eqeqeq
                templateTitle != "Create" ? vlanDomainData?.some(item => !item.vlan_domain_id) : false;

            const hasError = vlanDomainData.some(item => {
                const nameError = !NAME_MATCH_REGEX.test(item.vlan_domain_name);
                const vrfError =
                    item.vrf_vlan?.ranges?.length > 0
                        ? item.vrf_vlan?.ranges?.some(range => range.status === "error")
                        : false;
                const bridgeError =
                    item.bridge_domain?.ranges?.length > 0
                        ? item.bridge_domain?.ranges?.some(range => range.status === "error")
                        : false;
                return nameError || vrfError || bridgeError;
            });

            if (vlanDomainNameError || vlanDomainIDError || hasError) {
                setSaveStatus(true);
            }
        }
    }, [vlanDomainData]);

    useEffect(() => {
        fetchData().then();
    }, []);

    useEffect(() => {
        if (state.data?.group_name) {
            form.setFieldsValue({vdGroupName: state.data.group_name});
        }
        if (state.data?.description) {
            form.setFieldsValue({description: state.data.description});
        }
        if (state.data?.fabric_name) {
            form.setFieldsValue({fabricName: state.data.fabric_name});
        }

        if (state.data?.fabric_topo_id) {
            setFabricTopoId(state.data.fabric_topo_id);
        }

        if (state.data?.id) {
            setVlanDomainGroupId(state.data.id);
        }

        if (fabricTopoId !== undefined) {
            const fetchData = async () => {
                await fetchTopoData();
                fetchTopoData();
            };
            fetchData();
        }
    }, []);

    useEffect(() => {
        if (fabricTopoId !== undefined) {
            fetchTopoData();
            fetchTopoData();
        }
    }, [fabricTopoId]);

    useEffect(() => {
        if (fabricTopoId !== undefined) {
            fetchVlanDomainData();
        }
    }, [topoInfo, fabricTopoId]);

    useEffect(() => {}, [vlanDomainData]);

    useEffect(() => {
        setSyncDisabled(true);
        if (selectedRowKeys.length > 0 && vlanDomainData.length > 0) {
            const syncDisalbedError = selectedRowKeys[0] !== vlanDomainData[0].vlan_domain_name;
            setSyncDisabled(syncDisalbedError);
        }
    }, [selectedRowKeys]);

    return (
        <Card style={{display: "flex", flex: 1, position: "relative"}}>
            <div className={styles.editVlanDomainBox}>
                {/* <p
                    className={[styles.goBack, styles.goBackCursor].join(" ")}
                    onClick={() => navigate("/resource/resource_interconnection/vlan_domain")}
                    style={{marginBottom: "24px", width: 60}}
                >
                    <ArrowLeftOutlined style={{marginRight: "8px"}} />
                    <span>Back</span>
                </p> */}
                <div className={styles.editCount} style={{height: "auto", marginTop: 14}}>
                    <Form
                        layout="horizontal"
                        form={form}
                        labelAlign="left"
                        className={styles.formBox}
                        onFinish={handleFormSubmit}
                    >
                        <div className={styles.formItemsBox}>
                            <Form.Item
                                name="vdGroupName"
                                label="Group Name"
                                labelCol={{style: {width: 105}}}
                                className={styles.nodeItem}
                                rules={[
                                    {required: true, message: "Please enter the  node group name!"},
                                    {max: 64, message: "Enter a maximum of 64 characters"},
                                    {
                                        validator: (_, value) => {
                                            if (!NAME_MATCH_REGEX.test(value)) {
                                                return Promise.reject(
                                                    new Error(
                                                        "Group name can only contain letters, numbers, underscores, hyphens and spaces."
                                                    )
                                                );
                                            }
                                            return Promise.resolve();
                                        }
                                    }
                                ]}
                            >
                                <Input style={{width: 250}} disabled={templateTitle === "View"} />
                            </Form.Item>
                            <Form.Item
                                name="fabricName"
                                label="Fabric"
                                labelCol={{style: {width: 70}}}
                                className={styles.nodeItem}
                                rules={[{required: true}]}
                            >
                                <Select
                                    style={{width: 250}}
                                    disabled={templateTitle === "Edit" || templateTitle === "View"}
                                    onChange={e => {
                                        handleFabricChange(e);
                                    }}
                                    options={fabricList?.map(item => ({
                                        value: item.id,
                                        label: item.fabric_name
                                    }))}
                                />
                            </Form.Item>
                            <Form.Item
                                name="description"
                                label="Description"
                                labelCol={{style: {width: 90}}}
                                className={styles.nodeItem}
                            >
                                <Input style={{width: 250}} disabled={templateTitle === "View"} />
                            </Form.Item>
                        </div>
                    </Form>

                    <div style={{overflowY: "auto"}}>
                        <div className={styles.fabricTopo}>
                            <FabricTopo topoInfo={topoInfo} onNodeClick={handleNodeClick} />
                        </div>
                    </div>
                    <div style={{borderTop: "1px solid #E7E7E7", paddingTop: 24}}>
                        <div
                            className={styles.button_box2}
                            style={{
                                border: "none",
                                display: templateTitle === "Create" ? "block" : "none",
                                marginLeft: 0
                            }}
                        >
                            <Button
                                type="primary"
                                // icon={<Icon component={syncDisalbed ? SyncSvgDis : SyncSvg} />}
                                icon={<Icon component={SyncSvg} />}
                                // onClick={() => openEditModalModal("batch", selectedRowKeys)}
                                // onClick={() => syncData()}
                                onClick={() =>
                                    confirmModalAction(
                                        <p>
                                            This operation will batch-sync the first row of data (Bridge Domain Range
                                            and VRF VLAN Range) to other VLAN Domains and existing data will be
                                            overwritten.
                                            <br />
                                            Continue the operation?
                                        </p>,
                                        () => syncData()
                                    )
                                }
                                // disabled={syncDisalbed}
                                style={{marginBottom: 20}}
                            >
                                Apply to Others
                            </Button>
                        </div>
                        <div>
                            <Table
                                columns={vlanDomainColumns}
                                dataSource={vlanDomainData}
                                bordered
                                // rowSelection={templateTitle !== "Create" ? undefined : rowSelection}
                                rowSelection={undefined}
                                pagination={paginationConfig}
                                rowKey={record => record.vlan_domain_id}
                            />
                        </div>

                        {/* { <EditVlanDomainModal
                            title="Edit VLAN Domain"
                            editModalData={editModalData}
                            setEditModalData={setEditModalData}
                            nodesData={nodesData}
                            editModalopen={editModalopen}
                            setEditModalOpen={setEditModalOpen}
                            onCancel={() => setEditModalOpen(false)}
                            onSubmit={edit_vlanDomain}
                            modalClass="ampcon-max-modal"
                        /> } */}
                    </div>
                </div>

                <div className={styles.IssueOperationBar}>
                    <Button
                        key="cancel"
                        onClick={() => {
                            navigate("/resource/resource_interconnection/vlan_domain");
                        }}
                    >
                        Back
                    </Button>
                    {/* {editDisabled ? (
                        <Button
                            key="Edit"
                            type="primary"
                            style={{margin: "0 16px"}}
                            onClick={() => {
                                setEditDisabled(false);
                                setTemplateTitle("Edit");
                            }}
                        >
                            Edit
                        </Button>
                    ) : (
                        <Button key="Save" type="primary" style={{margin: "0 16px"}} onClick={form.submit}>
                            Save
                        </Button>
                    )} */}
                    <Button
                        key="Save"
                        type="primary"
                        style={{display: templateTitle === "View" ? "none" : "block"}}
                        onClick={form.submit}
                        disabled={templateTitle === "View" ? true : saveStatus}
                    >
                        Save
                    </Button>
                </div>
            </div>
        </Card>
    );
};

export default VLANDomainDetails;

const EditVlanDomainModal = ({
    title,
    editModalData,
    setEditModalData,
    nodesData,
    editModalopen,
    // setEditModalOpen,
    onCancel,
    onSubmit
}) => {
    const [data, setData] = useState([]);
    const [saveStatus, setSaveStatus] = useState(false);
    const NAME_MATCH_REGEX = /^[\s\w-]+$/;

    const handleSort = dataIndex => {
        return (a, b) => {
            const getValue = (obj, keyPath) => {
                return keyPath.split(".").reduce((o, key) => (o ? o[key] : ""), obj);
            };

            const valueA = getValue(a, dataIndex);
            const valueB = getValue(b, dataIndex);

            if (typeof valueA === "string" && typeof valueB === "string") {
                return valueA.localeCompare(valueB);
            }
            return (valueA || 0) - (valueB || 0);
        };
    };

    // 定义合并单元格的逻辑
    const getMergedCellData = data => {
        // 根据logic_name判定
        const newData = [];
        const processedIndices = new Set();

        for (let i = 0; i < data.length; i++) {
            if (processedIndices.has(i)) {
                continue;
            }
            const currentItem = {...data[i]};
            currentItem.logic_name = [currentItem.logic_name];
            currentItem.statue = "success";

            // 处理 bridge_domain.ranges 和 vrf_vlan.ranges
            if (currentItem.bridge_domain.ranges) {
                currentItem.bridge_domain.ranges.forEach(ele => {
                    ele.statue = "success";
                });
            }
            if (currentItem.vrf_vlan.ranges) {
                currentItem.vrf_vlan.ranges.forEach(ele => {
                    ele.statue = "success";
                });
            }

            if (currentItem.strategy === "MLAG" && currentItem.type === "leaf") {
                const prefix = currentItem.logic_name[0].slice(0, -2);
                const suffix = currentItem.logic_name[0].slice(-2);
                if (suffix === "_1" || suffix === "_2") {
                    for (let j = i + 1; j < data.length; j++) {
                        if (processedIndices.has(j)) {
                            continue;
                        }
                        const nextItem = data[j];
                        if (nextItem.strategy === "MLAG" && nextItem.type === "leaf") {
                            const nextPrefix = nextItem.logic_name.slice(0, -2);
                            const nextSuffix = nextItem.logic_name.slice(-2);
                            if (
                                nextPrefix === prefix &&
                                ((suffix === "_1" && nextSuffix === "_2") || (suffix === "_2" && nextSuffix === "_1"))
                            ) {
                                if (!currentItem.logic_name.includes(nextItem.logic_name)) {
                                    currentItem.logic_name.push(nextItem.logic_name);
                                }
                                processedIndices.add(j);
                            }
                        }
                    }
                }
            }

            // 处理 logic_device_id 重复情况
            for (let k = i + 1; k < data.length; k++) {
                if (processedIndices.has(k)) {
                    continue;
                }
                const otherItem = data[k];
                if (otherItem.logic_device_id === currentItem.logic_device_id) {
                    if (!currentItem.logic_name.includes(otherItem.logic_name)) {
                        currentItem.logic_name.push(otherItem.logic_name);
                    }
                    processedIndices.add(k);
                }
            }

            newData.push(currentItem);
            processedIndices.add(i);
        }

        return newData;
    };
    // 根据logic_name从nodesData中获取指定的数据
    const filterTopoValue = (logicName, name) => {
        let value = "";
        nodesData.forEach(item => {
            if (item.logic_device === logicName)
                if (item[name]) {
                    value = item[name];
                } else {
                    value = item.node_info[name];
                }
        });
        return value;
    };

    // Bridge Domain Range和VRF VLAN Range输入检验
    const rangeInputValidator = (recordIndex, rangeIndex, recordName, rangeName, e) => {
        const newData = JSON.parse(JSON.stringify(data));
        newData[recordIndex][recordName].ranges[rangeIndex].status = "success";
        newData[recordIndex][recordName].ranges[rangeIndex].errorTips = "";

        const current_rangObj = newData[recordIndex][recordName].ranges[rangeIndex];
        const current_rangValue = [current_rangObj.start_value, current_rangObj.end_value];

        // 1. 如果end_value存在有效值，start_value不能为空
        if (current_rangValue[1] !== "" && !current_rangValue[0]) {
            newData[recordIndex][recordName].ranges[rangeIndex].status = "error";
            newData[recordIndex][recordName].ranges[rangeIndex].errorTips = "Start value cannot be empty";
            setData(newData);
            return;
        }

        // 2. 如果end_value小于start_value
        if (current_rangValue[1] < current_rangValue[0]) {
            newData[recordIndex][recordName].ranges[rangeIndex].status = "error";
            newData[recordIndex][recordName].ranges[rangeIndex].errorTips =
                "End value must be greater than start value";
            setData(newData);
            return;
        }
        newData[recordIndex][recordName].ranges[rangeIndex].status = "success";
        newData[recordIndex][recordName].ranges[rangeIndex].errorTips = "";
        setData(newData);

        // 3.当前输入的值是否存在于Bridge Domain Range和VRF VLAN Range某段区间内
        // 排除当前rangeIndex
        let new_range = [];
        if (recordName === "bridge_domain") {
            const newArray = newData.flatMap(data => {
                return (
                    data.bridge_domain?.ranges
                        ?.filter((_, index) => index !== rangeIndex)
                        .map(item => [item.start_value, item.end_value]) || []
                );
            });

            const newArray2 = newData.flatMap(data => {
                return data.vrf_vlan?.ranges?.map(item => [item.start_value, item.end_value]) || [];
            });

            new_range = [...newArray, ...newArray2];
        }

        if (recordName === "vrf_vlan") {
            const newArray = newData.flatMap(data => {
                return data.bridge_domain?.ranges?.map(item => [item.start_value, item.end_value]) || [];
            });
            const newArray2 = newData.flatMap(data => {
                return (
                    data.vrf_vlan?.ranges
                        ?.filter((_, index) => index !== rangeIndex)
                        .map(item => [item.start_value, item.end_value]) || []
                );
            });
            new_range = [...newArray, ...newArray2];
        }

        // 提取 current_rangValue 中的唯一值以便于比较
        const uniqueA = [...new Set(current_rangValue)];

        // // 排除与 currentValue 重复的数据
        // const filteredRange = new_range.filter(range => {
        //     return range[0] !== current_rangObj.start_value || range[1] !== current_rangObj.end_value;
        // });

        // 判断 currentValue 是否和某个数字区间重叠
        let isOverlap = false;
        new_range.forEach(range => {
            const [start1, end1] = [current_rangObj.start_value, current_rangObj.end_value];
            const [start2, end2] = range;
            if (start1 <= end2 && start2 <= end1) {
                isOverlap = true;
            }
        });
        if (isOverlap) {
            newData[recordIndex][recordName].ranges[rangeIndex].status = "error";
            newData[recordIndex][recordName].ranges[rangeIndex].errorTips = "Conflict with other ranges";
        }
        // 遍历 current_rangValue，检查与 current_rangValue 重复的次数
        const duplicates = new_range.filter(item => uniqueA.some(value => item[0] === value && item[1] === value));
        // 根据重复的数量进行相应处理
        if (duplicates.length > 1) {
            newData[recordIndex][recordName].ranges[rangeIndex].status = "error";
            newData[recordIndex][recordName].ranges[rangeIndex].errorTips = "Conflict with other ranges";
        } else if (duplicates.length === 1) {
            // 删除 new_range 中重复的数据
            const index = new_range.indexOf(duplicates[0]);
            if (index !== -1) {
                new_range.splice(index, 1);
            }
        } else {
            return;
        }

        // 遍历 current_rangValue 数组，检查每个值是否在 ranges 中
        const results = current_rangValue.map(value => {
            const isInRange = new_range.some(range => value >= range[0] && value <= range[1]);
            return {value, inRange: isInRange};
        });
        results.forEach(result => {
            if (result.inRange) {
                newData[recordIndex][recordName].ranges[rangeIndex].status = "error";
                newData[recordIndex][recordName].ranges[rangeIndex].errorTips = "Conflict with other ranges";
                return;
            }
        });
        setData(newData);
    };

    const handleInputChange = (index, record, recordName, rangeName, rangeIndex, e) => {
        const newData = [...data];
        if (recordName === "vlan_domain_name") {
            record[recordName] = e.target.value;
        } else {
            record[recordName].ranges[rangeIndex][rangeName] = e;
            if (
                (rangeName === "start_value" && !record[recordName].ranges[rangeIndex].end_value) ||
                record[recordName].ranges[rangeIndex].end_value < record[recordName].ranges[rangeIndex].start_value
            ) {
                record[recordName].ranges[rangeIndex].end_value = e;
            }
        }
        newData.splice(index, 1, record);

        setData(newData);
    };

    const vlanDomainColumns = [
        {
            ...createColumnConfig("VLAN Domain Name", "vlan_domain_name"),
            sorter: handleSort("vlan_domain_name"),
            render: (text, record, index) => {
                const isValid = NAME_MATCH_REGEX.test(record.vlan_domain_name);
                let tipText = "";
                if (!isValid) {
                    tipText = "VLAN Domain name can only contain letters, numbers, underscores, hyphens and spaces.";
                }

                return (
                    <div>
                        <Input
                            maxLength="64"
                            value={record.vlan_domain_name}
                            onChange={e => {
                                handleInputChange(index, record, "vlan_domain_name", null, null, e);
                            }}
                            style={{
                                width: 200,
                                borderColor: !isValid ? "red" : ""
                            }}
                        />
                        {!isValid && (
                            <p
                                style={{
                                    width: 200,
                                    height: 20,
                                    margin: 0,
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                    color: !isValid ? "red" : undefined
                                }}
                                title={tipText}
                            >
                                {tipText}
                            </p>
                        )}
                    </div>
                );
            }
        },
        // {
        //     ...createColumnConfig("Device Index", "logic_name"),
        //     sorter: handleSort("logic_name"),
        //     render: (text, record) => {
        //         return (
        //             <p>
        //                 {record?.logic_name?.map(item => {
        //                     const groupValue = filterTopoValue(item, "group");
        //                     const logicDevice = item || "";
        //                     const group = `${groupValue}_` || "";
        //                     const result = logicDevice?.replace(group, "").trim();
        //                     return <p key={item}>{result}</p>;
        //                 })}
        //             </p>
        //         );
        //     }
        // },
        {
            ...createColumnConfig("Sysname", "sysname"),
            sorter: handleSort("sysname"),
            render: (text, record) => {
                return (
                    <p>
                        {record?.logic_name?.map(item => {
                            const name = filterTopoValue(item, "sysname");
                            return <p key={item}>{name}</p>;
                        })}
                    </p>
                );
            }
        },
        {
            ...createColumnConfig("Role", "type"),
            sorter: handleSort("type")
        },
        {
            ...createColumnConfig("Bridge Domain Range", "bridge_domain"),
            sorter: handleSort("bridge_domain"),
            title: (
                <Tooltip
                    placement="right"
                    title="
                        Indicates the VLAN ranges reserved by Bridge Domain for L2VNI on the switch. The available Bridge Domain values range from 2 to 3965. The values in the Bridge Domain Range cannot be duplicated with the values in the VRF VLAN Range. Example Bridge Domain Range: 30-300, 400, 509, 1600-2000.
                    "
                >
                    <span>
                        Bridge Domain Range <QuestionCircleOutlined />
                    </span>
                </Tooltip>
            ),
            render: (text, record, index) => (
                <ul>
                    <PlusOutlined
                        onClick={() => {
                            addRange("bridge_domain", record, index);
                        }}
                        style={{marginLeft: 8}}
                    />
                    {record?.bridge_domain?.ranges?.map((item, rangeIndex) => (
                        <li style={{marginBottom: 8, height: 50, listStyle: "none"}}>
                            <InputNumber
                                min={2}
                                max={3965}
                                name={item.id + item.start_value}
                                value={item.start_value}
                                status={item.status}
                                onChange={e => {
                                    handleInputChange(index, record, "bridge_domain", "start_value", rangeIndex, e);
                                    rangeInputValidator(index, rangeIndex, "bridge_domain", "start_value", e);
                                }}
                                style={{width: 140}}
                                placeholder="Start Value,Range(2 - 3965)"
                            />
                            &nbsp; — &nbsp;
                            <InputNumber
                                min={2}
                                max={3965}
                                name={item.id + item.end_value}
                                value={item.end_value}
                                status={item.status}
                                onChange={e => {
                                    handleInputChange(index, record, "bridge_domain", "end_value", rangeIndex, e);
                                    rangeInputValidator(index, rangeIndex, "bridge_domain", "end_value", e);
                                }}
                                style={{width: 140}}
                                placeholder="End Value,Range(2 - 3965)"
                            />
                            <LineOutlined
                                onClick={() => {
                                    deleteRange("bridge_domain", index, rangeIndex);
                                }}
                                style={{marginLeft: 8}}
                            />
                            {item?.errorTips !== "" && <p className={styles.errorTips}>{item?.errorTips}</p>}
                        </li>
                    ))}
                </ul>
            )
        },
        {
            ...createColumnConfig("VRF VLAN Range", "vrf_vlan"),
            sorter: handleSort("vrf_vlan"),
            title: (
                <Tooltip
                    placement="right"
                    title="Indicates the VLAN ranges reserved by VRF VLAN for L3VNI on the switch. The available VRF VLAN values range from 2 to 3965. The values in the VRF VLAN Range cannot be duplicated with the values in the Bridge Domain Range. Example Bridge Domain Range: 500-505, 600, 66."
                >
                    <span>
                        VRF VLAN Range <QuestionCircleOutlined />
                    </span>
                </Tooltip>
            ),
            render: (text, record, index) => (
                <ul>
                    <PlusOutlined
                        onClick={() => {
                            addRange("vrf_vlan", record, index);
                        }}
                        style={{marginLeft: 8}}
                    />
                    {record?.vrf_vlan?.ranges?.map((item, rangeIndex) => (
                        <li style={{marginBottom: 8, height: 50, listStyle: "none"}}>
                            <InputNumber
                                min={2}
                                max={3965}
                                name={item.id + item.vrf_vlan}
                                value={item.start_value}
                                status={item.status}
                                onChange={e => {
                                    handleInputChange(index, record, "vrf_vlan", "start_value", rangeIndex, e);
                                    rangeInputValidator(index, rangeIndex, "vrf_vlan", "start_value", e);
                                }}
                                style={{width: 140}}
                                placeholder="Start Value,Range(2 - 3965)"
                            />
                            &nbsp; — &nbsp;
                            <InputNumber
                                min={2}
                                max={3965}
                                name={item.id + item.vrf_vlan}
                                value={item.end_value}
                                status={item.status}
                                onChange={e => {
                                    handleInputChange(index, record, "vrf_vlan", "end_value", rangeIndex, e);
                                    rangeInputValidator(index, rangeIndex, "vrf_vlan", "end_value", e);
                                }}
                                style={{width: 140}}
                                placeholder="End Value,Range(2 - 3965)"
                            />
                            <LineOutlined
                                onClick={() => {
                                    deleteRange("vrf_vlan", index, rangeIndex);
                                }}
                                style={{marginLeft: 8}}
                            />
                            {item?.errorTips !== "" && <p className={styles.errorTips}>{item?.errorTips}</p>}
                        </li>
                    ))}
                </ul>
            )
        },
        {
            ...createColumnConfig("Status", "status"),
            sorter: handleSort("status"),
            render: (text, record) => {
                let val = "";
                if (record?.bridge_domain?.is_in_use || record?.vrf_vlan?.is_in_use) val = "using";
                else val = "Unused";
                return <Tag className={val === "using" ? styles.successTag : styles.uncheckedTag}>{val}</Tag>;
            }
        }
    ];

    const addRange = (name, record, index) => {
        const newData = [...data];
        if (!newData[index][name].ranges) {
            newData[index][name].ranges = [];
        }
        newData[index][name].ranges.push({start_value: "", end_value: ""});
        setData(newData);
    };
    const deleteRange = (name, index, rangeIndex) => {
        const newData = [...data];
        newData[index][name].ranges.splice(rangeIndex, 1);
        setData(newData);
    };

    const handleOk = async () => {
        await onSubmit(data);
    };
    useEffect(() => {
        let updatedData = [];
        if (editModalData) {
            const newData = JSON.parse(JSON.stringify(editModalData)) || [];
            updatedData = getMergedCellData(newData);
            setData(updatedData);
        }
    }, [editModalData]);

    /**
     * 监听弹框的table数据，如果VLAN Domain Name为空，则无法进行Save
     */
    useEffect(() => {
        if (data) {
            setSaveStatus(false);
            const vlanDomainNameError = data.some(item => {
                const isVlanDomainNameInvalid = !item.vlan_domain_name || item.vlan_domain_name.trim() === "";
                const isBridgeDomainEmpty = !item.bridge_domain || Object.keys(item.bridge_domain).length === 0; // bridge_domain不存在或空对象
                const isBridgeDomainRangesEmpty =
                    !isBridgeDomainEmpty && (!item.bridge_domain.ranges || item.bridge_domain.ranges.length === 0);
                const isVrfVlanEmpty = !item.vrf_vlan || Object.keys(item.vrf_vlan).length === 0; // vrf_vlan不存在或空对象
                const isVrfVlanRangesEmpty =
                    !isVrfVlanEmpty && (!item.vrf_vlan.ranges || item.vrf_vlan.ranges.length === 0);

                return (
                    isVlanDomainNameInvalid ||
                    isBridgeDomainEmpty ||
                    isBridgeDomainRangesEmpty ||
                    isVrfVlanEmpty ||
                    isVrfVlanRangesEmpty
                );
            });

            const vlanDomainIDError = data?.some(item => !item.vlan_domain_id);

            const hasError = data.some(item => {
                const nameError = !NAME_MATCH_REGEX.test(item.vlan_domain_name);
                const vrfError =
                    item.vrf_vlan?.ranges?.length > 0
                        ? item.vrf_vlan?.ranges?.some(range => range.status === "error")
                        : false;
                const bridgeError =
                    item.bridge_domain?.ranges?.length > 0
                        ? item.bridge_domain?.ranges?.some(range => range.status === "error")
                        : false;
                return nameError || vrfError || bridgeError;
            });

            if (vlanDomainNameError || vlanDomainIDError || hasError) {
                setSaveStatus(true);
            }
        }
    }, [data]);

    return (
        <Space>
            <Modal
                className="ampcon-max-modal"
                title={
                    <div>
                        {title}
                        <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                open={editModalopen}
                onCancel={onCancel}
                footer={
                    <>
                        <Divider style={{marginBottom: "20px", marginTop: "0px"}} />
                        <Button onClick={onCancel}> Cancel </Button>
                        <Button type="primary" onClick={handleOk} disabled={saveStatus}>
                            Apply
                        </Button>
                    </>
                }
            >
                <Table columns={vlanDomainColumns} rowKey={record => record.id} dataSource={data} />
            </Modal>
        </Space>
    );
};
