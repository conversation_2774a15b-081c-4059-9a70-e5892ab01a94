.wrap {
    width: 100%;
    height: 100%;
    position: relative;
}

.loading {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalWrap {
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.modalLoading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.deviceDisplay {
  display: grid;
  height: 100%;
  width: 100%;
  background-color: #f0f2f5;
  overflow: auto;
  gap: 18px 24px;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: repeat(8, 1fr);
  > div {
        &:nth-child(1) {
            grid-column: 1 / 13;
        }
        &:nth-child(2) {
            grid-column: 1 / 13;
        }
        &:nth-child(3) {
            grid-column: 1 / 13;
        }
        &:nth-child(4) {
            grid-column: 1 / 13;
        }
        &:nth-child(5) {
            grid-column: 1 / 13;
        }
        &:nth-child(6) {
            grid-column: span 6;
        }
        &:nth-child(7) {
            grid-column: span 6;
        }
        &:nth-child(8) {
            grid-column: 1 / 13;
        }
        &:nth-child(9) {
            grid-column: span 6;
        }
        &:nth-child(10) {
            grid-column: span 6;
        }
    }
}

.viewDisplay {
  width: 100%;
  background-color: #FFFFFF;
  padding-top: 10px;
  padding-bottom: 24px;
}

.customRadioGroup {
  :global {
    .ant-radio-button-wrapper {
      width: 100px;
      text-align: center;
      color: #B3BBC8;
      &:hover {
        color: #14C9BB;
      }
      &-checked {
        color: #14C9BB;
      }
    }
  }
}

.customText {
  display: inline-block;
  width: 200px;
  margin-right: 32px;
}

.customInput {
  width: 280px;
}

.normalTag {
  color: #2BC174;
  background-color: rgba(43, 193, 116, 0.1);
  border-color: #2BC174;
  margin-right: 0;
}

.abnormalTag {
  color: #F53F3F;
  background-color: rgba(245, 63 ,63 , 0.1);
  border-color: #F53F3F;
  margin-right: 0;
}

.testingTag {
  color: #FFBB00;
  background-color: rgba(255, 187, 0, 0.1);
  border-color: #FFBB00;
  margin-right: 0;
}

.stopTag {
  color: #FF7B43;
  background-color: rgba(255, 123, 67, 0.1);
  border-color: #FF7B43;
  margin-right: 0;
}

.flexibleNormalTag {
  @extend .normalTag;
}

.flexibleAbnormalTag {
  @extend .abnormalTag;
}

@media (max-width: 1721px) {
  .flexibleNormalTag,
  .flexibleAbnormalTag {
    margin-left: -15px
  }
}

.textContainer {
  display: inline-block;
  align-items: center;
  margin-right: 32px;
  &_bullet {
    display: inline-block;
    vertical-align: middle;
    width: 6px;
    height: 6px;
    background-color: #14C9BB;
    border-radius: 50%;
    margin-right: 10px;
  }
}

.listItem {
  display: flex;
  align-items: center;
  padding-left: 16px;
}

.listItem::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: #14C9BB;
  border-radius: 50%;
  margin-right: 10px;
}

.listEvenRow {
  background-color: #FFFFFF;
}

.listOddRow {
  background-color: #F7F9F9;
}

.listText {
  margin-right: 60%
}

@media (max-width: 1920px) {
  .listText {
    margin-right: 30%
  }
}

// 行高调不了40，因为全局样式表index.css中用important调整为了48
.tableEvenRow,
.tableOddRow {
  background-color: #FFFFFF;
  height: 40px;
  :global {
    td {
      height: 40px;
      line-height: 40px;
      border-bottom: none !important;

      &:nth-child(n+2) {
          width: calc((100% - 200px) / (9 - 1));
      }
    }
  }
}

.tableEvenRow {
    background-color: #FFFFFF;
}

.tableOddRow {
    background-color: #F7F9F9;
}

.chassisFront {
  width: 1850px;
  height: 197px;
  background-image: url("img/front_view.png");
  background-size: 100% 100%;
  position: relative;
}

.chassisRear {
  width: 1850px;
  height: 197px;
  background-image: url("img/rear_view.png");
  background-size: 100% 100%;
  position: relative;
}

.networkCard {
  width: 228px;
  height: 187px;
  background-image: url("img/network_card.png");
  background-size: 100% 100%;
  position: absolute;
}

.statusCard {
  width: 1480px;
  height: 187px;
  background-image: url("img/status_card.png");
  background-size: 100% 100%;
  position: absolute;
}

.powerCard {
  width: 471px;
  height: 187px;
  background-image: url("img/power.png");
  background-size: 100% 100%;
  position: absolute;
}

.ledRunning {
    box-shadow: none;
    width: 8px;
    height: 8px;
    border-radius: 4px;
    position: absolute;
    background-color: #2bbf2b;
}

.fanCard {
  width: 197px;
  height: 187px;
  background-image: url("img/fan_card.png");
  background-size: 100% 100%;
  position: absolute;
}

.fan {
  width: 57px;
  height: 57px;
  background-image: url("img/fan.svg");
  background-size: 100% 100%;
  position: absolute;
}

@keyframes loading_circle {
  100% {
    transform: rotate(360deg);
  }
}

.fanSpeedLow {
  animation: loading_circle 1s infinite linear;
  color: rgb(104 104 126 / 84%);
}

.fanSpeedMiddle {
  animation: loading_circle 0.5s infinite linear;
  color: rgb(159 154 122 / 84%);
}

.fanSpeedHigh {
  opacity: 0.7;
  animation: loading_circle 0.3s infinite linear;
  color: rgba(192, 131, 131, 0.68);
}

.activePort {
  width: 61.5px;
  height: 44.3px;
  background-image: url("img/port_2.png");
  background-size: 100% 100%;
  position: absolute;
}

.activeInversionPort {
  width: 61.5px;
  height: 44.3px;
  background-image: url("img/port_2.png");
  background-size: 100% 100%;
  transform: scaleY(-1);
  position: absolute;
}

.model {
  font-size: 9px;
  background-color: #9da6b2;
  position: absolute;
  color: #FFFFFF;
  padding: 0 5px;
}

.logo {
  width: 48px;
  height: 24px;
  background-image: url("img/logo.png");
  background-size: 100% 100%;
  position: absolute;
}

.flexibleItem {
  flex: 0 1 calc(25% - 10px);
  white-space: nowrap;
}

@media (max-width: 1792px) {
  .flexibleItem {
    flex: 0 1 calc(50% - 10px);
    white-space: nowrap;
  }
  .flexibleItem:nth-child(n+3) {
    margin-top: 16px;
  }
}