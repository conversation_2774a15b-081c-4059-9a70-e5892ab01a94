import React, {memo, useEffect, useState, forwardRef, useRef, useImperativeHandle, Fragment} from "react";
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Divider,
    Flex,
    Input,
    List,
    Progress,
    Radio,
    Row,
    Spin,
    Table,
    Tag,
    Typography,
    message,
    Dropdown,
    Modal,
    Form,
    Switch,
    Select,
    Tabs
} from "antd";
import classNames from "classnames";
import {useSelector} from "react-redux";
import {debounce} from "lodash";
import Icon from "@ant-design/icons";
import {normalizeNumber} from "@/modules-ampcon/utils/util";
import {
    getDCP920Info,
    modifyDCP920Config,
    queryDCP920Config,
    queryDCP920Info,
    queryDCP920Test
} from "@/modules-ampcon/apis/dcp920";
import {useForm} from "antd/es/form/Form";
import {PowerSvg, DispersionSvg} from "./img/svg";
import styles from "./device_display.module.scss";

const {Text} = Typography;

const DeviceDisplay = () => {
    const viewRadioOptions = [
        {label: "Front", value: "Front"},
        {label: "Rear", value: "Rear"}
    ];
    const modelMap = {"OAP-M4": "DCP920-D08P"};
    const [loading, setLoading] = useState(false);
    const [viewSelect, setViewSelect] = useState("Front");
    const chassisRef = useRef();
    const chassisWrapRef = useRef();
    const containerRef = useRef();
    const equipmentInfoRef = useRef();
    const {selectedItem} = useSelector(state => state.map);
    const onChangeViewSelect = ({target: {value}}) => {
        setViewSelect(value);
    };
    const sync = () => {
        syncData(selectedItem?.value?.host).then();
    };

    const redirect = () => {
        if (selectedItem?.value?.host) {
            const url = `http://${selectedItem.value.host}`;
            window.open(url, "DCP920");
        }
    };

    const [deviceInfo, setDeviceInfo] = useState({
        business_info: {
            power: "00",
            client: [{}],
            line: [{}],
            "TX-Directional-power": {},
            "RX-Directional-power": {},
            "The-dispersion-information": {},
            "TX-Directional-attenuation": {},
            "RX-Directional-attenuation": {}
        }
    });
    const refreshInterval = 30000;
    const fetchData = async ip => {
        if (ip) {
            await getDCP920Info(ip).then(res => {
                if (res.errorCode === 0) {
                    setDeviceInfo(res.data);
                }
            });
        }
    };
    const syncData = async ip => {
        if (ip) {
            setLoading(true);
            try {
                await queryDCP920Info(ip).then(res => {
                    if (res.errorCode === 0) {
                        setDeviceInfo(res.data);
                        equipmentInfoRef.current.setClock(res.data?.clock);
                        message.success("Sync dcp920 device info success");
                    } else {
                        message.error("Sync dcp920 device info error");
                    }
                });
            } catch (error) {
                message.error("Sync dcp920 device info error");
            } finally {
                setLoading(false);
            }
        }
    };
    useEffect(() => {
        fetchData(selectedItem?.value?.host);
        const intervalId = setInterval(fetchData, refreshInterval, selectedItem?.value?.host);
        return () => {
            clearInterval(intervalId);
        };
    }, [selectedItem]);

    useEffect(() => {
        if (chassisWrapRef.current && chassisRef.current) {
            chassisRef.current.style = `zoom: ${(chassisWrapRef?.current.clientWidth || 0) / 1856}`;
        }

        const observer = new ResizeObserver(
            debounce(() => {
                if (chassisRef.current)
                    chassisRef.current.style = `zoom: ${(chassisWrapRef?.current.clientWidth || 0) / 1856}`;
            }, 5)
        );
        observer.observe(containerRef.current);

        return () => {
            observer.disconnect();
        };
    }, []);

    const txPowerData = [
        {
            title: "Input Power(dBm)",
            value: normalizeNumber(deviceInfo.business_info?.["TX-Directional-power"]?.InputPower),
            specialColor:
                deviceInfo.business_info?.["TX-Directional-power"]?.isInputAlarm !== "0" ? "#F53F3F" : "#2BC174"
        },
        {
            title: "Low Input Power(dBm)",
            value: normalizeNumber(deviceInfo.business_info?.["TX-Directional-power"]?.LowInputPower)
        },
        {
            title: "Output Power(dBm)",
            value: normalizeNumber(deviceInfo.business_info?.["TX-Directional-power"]?.OutputPower),
            specialColor:
                deviceInfo.business_info?.["TX-Directional-power"]?.isOutputAlarm !== "0" ? "#F53F3F" : "#2BC174"
        },
        {
            title: "Low Output Power(dBm)",
            value: normalizeNumber(deviceInfo.business_info?.["TX-Directional-power"]?.LowOutputPower)
        },
        {
            title: "Work Model",
            value: deviceInfo.business_info?.["TX-Directional-power"]?.WorkModel === "1" ? "AGC" : "Manual"
        }
    ];
    const rxPowerData = [
        {
            title: "Input Power(dBm)",
            value: normalizeNumber(deviceInfo.business_info?.["RX-Directional-power"]?.InputPower),
            specialColor:
                deviceInfo.business_info?.["RX-Directional-power"]?.isInputAlarm !== "0" ? "#F53F3F" : "#2BC174"
        },
        {
            title: "Low Input Power(dBm)",
            value: normalizeNumber(deviceInfo.business_info?.["RX-Directional-power"]?.LowInputPower)
        },
        {
            title: "Output Power(dBm)",
            value: normalizeNumber(deviceInfo.business_info?.["RX-Directional-power"]?.OutputPower),
            specialColor:
                deviceInfo.business_info?.["RX-Directional-power"]?.isOutputAlarm !== "0" ? "#F53F3F" : "#2BC174"
        },
        {
            title: "Low Output Power(dBm)",
            value: normalizeNumber(deviceInfo.business_info?.["RX-Directional-power"]?.LowOutputPower)
        },
        {
            title: "Work Model",
            value: deviceInfo.business_info?.["RX-Directional-power"]?.WorkModel === "1" ? "AGC" : "Manual"
        }
    ];
    const txAttenuationData = [
        {
            title: "Work Model",
            value: deviceInfo.business_info?.["TX-Directional-attenuation"]?.WorkModel === "1" ? "Auto" : "Manual"
        },
        {
            title: "Power(dBm)",
            value: normalizeNumber(deviceInfo.business_info?.["TX-Directional-attenuation"]?.Power),
            specialColor:
                deviceInfo.business_info?.["TX-Directional-attenuation"]?.Alarm !== "1" ? "#F53F3F" : "#2BC174"
        },
        {
            title: "Attenuation(dB)",
            value: normalizeNumber(deviceInfo.business_info?.["TX-Directional-attenuation"]?.Attenuation)
        },
        {
            title: "Alarm Threshold(dBm)",
            value: normalizeNumber(deviceInfo.business_info?.["TX-Directional-attenuation"]?.AlarmThreshold)
        },
        {
            title: "Model Wave Len(nm)",
            value: normalizeNumber(deviceInfo.business_info?.["TX-Directional-attenuation"]?.ModelWaveLen)
        }
    ];
    const rxAttenuationData = [
        {
            title: "Work Model",
            value: deviceInfo.business_info?.["RX-Directional-attenuation"]?.WorkModel === "1" ? "Auto" : "Manual"
        },
        {
            title: "Power(dBm)",
            value: normalizeNumber(deviceInfo.business_info?.["RX-Directional-attenuation"]?.Power),
            specialColor:
                deviceInfo.business_info?.["RX-Directional-attenuation"]?.Alarm !== "1" ? "#F53F3F" : "#2BC174"
        },
        {
            title: "Attenuation(dB)",
            value: normalizeNumber(deviceInfo.business_info?.["RX-Directional-attenuation"]?.Attenuation)
        },
        {
            title: "Alarm Threshold(dBm)",
            value: normalizeNumber(deviceInfo.business_info?.["RX-Directional-attenuation"]?.AlarmThreshold)
        },
        {
            title: "Model Wave Len(nm)",
            value: normalizeNumber(deviceInfo.business_info?.["RX-Directional-attenuation"]?.ModelWaveLen)
        }
    ];

    const powerLedData = [
        {status: deviceInfo?.power?.charAt(0) ?? "" === "1", top: 35, left: 114}, // P1
        {status: deviceInfo?.power?.charAt(1) ?? "" === "1", top: 35, left: 135}, // P2
        {status: +deviceInfo.power, top: 35, left: 198} // RUNNING
    ];

    const isClientPortLinked = client => {
        return (
            parseFloat(client?.MaxWaveLength) ?? parseFloat(client?.MinWaveLength) ?? parseFloat(client?.Rate) ?? null
        );
    };

    const isClientPortActive = client => {
        return (
            parseFloat(client?.RX1) > -15 ??
            parseFloat(client?.RX2) > -15 ??
            parseFloat(client?.RX3) > -15 ??
            parseFloat(client?.RX4) > -15 ??
            null
        );
    };

    const clientLedData = [
        {status: isClientPortActive(deviceInfo.business_info?.client?.[0]), top: 86, left: 747},
        {status: isClientPortActive(deviceInfo.business_info?.client?.[1]), top: 86, left: 771},
        {status: isClientPortActive(deviceInfo.business_info?.client?.[2]), top: 86, left: 795},
        {status: isClientPortActive(deviceInfo.business_info?.client?.[3]), top: 86, left: 819},
        {status: isClientPortActive(deviceInfo.business_info?.client?.[4]), top: 101, left: 747},
        {status: isClientPortActive(deviceInfo.business_info?.client?.[5]), top: 101, left: 771},
        {status: isClientPortActive(deviceInfo.business_info?.client?.[6]), top: 101, left: 795},
        {status: isClientPortActive(deviceInfo.business_info?.client?.[7]), top: 101, left: 819}
    ];

    const portData = [
        {status: isClientPortLinked(deviceInfo.business_info?.client?.[0]), top: 56, left: 338},
        {status: isClientPortLinked(deviceInfo.business_info?.client?.[1]), top: 56, left: 433},
        {status: isClientPortLinked(deviceInfo.business_info?.client?.[2]), top: 56, left: 529},
        {status: isClientPortLinked(deviceInfo.business_info?.client?.[3]), top: 56, left: 624},
        {status: isClientPortLinked(deviceInfo.business_info?.client?.[4]), top: 110, left: 338},
        {status: isClientPortLinked(deviceInfo.business_info?.client?.[5]), top: 110, left: 433},
        {status: isClientPortLinked(deviceInfo.business_info?.client?.[6]), top: 110, left: 529},
        {status: isClientPortLinked(deviceInfo.business_info?.client?.[7]), top: 110, left: 624},
        {
            // status: deviceInfo.business_info?.line?.some(item => parseFloat(item.Rate) !== 0) ? 1 : 0,
            status: 1,
            top: 47,
            left: 1562
        }, // LINE port
        {status: "inversion", top: 102, left: 1562}
    ];

    const fanSpeedMap = {
        10: "fanSpeedLow",
        50: "fanSpeedMiddle",
        100: "fanSpeedHigh"
    };

    const fanSpeed = fanSpeedMap[deviceInfo?.fan_gear] ?? "";

    const fanData = [
        {status: (deviceInfo?.fan_status?.charAt(0) ?? "" === "0") && fanSpeed, top: 66, left: 150},
        {status: (deviceInfo?.fan_status?.charAt(1) ?? "" === "0") && fanSpeed, top: 66, left: 447},
        {status: (deviceInfo?.fan_status?.charAt(2) ?? "" === "0") && fanSpeed, top: 66, left: 666},
        {status: (deviceInfo?.fan_status?.charAt(3) ?? "" === "0") && fanSpeed, top: 66, left: 900},
        {status: (deviceInfo?.fan_status?.charAt(4) ?? "" === "0") && fanSpeed, top: 66, left: 1130},
        {status: (deviceInfo?.fan_status?.charAt(5) ?? "" === "0") && fanSpeed, top: 66, left: 1352},
        {status: (deviceInfo?.fan_status?.charAt(6) ?? "" === "0") && fanSpeed, top: 66, left: 1650}
    ];

    const clientKeys = Object.keys(deviceInfo.business_info?.client?.[0] ?? []).filter(
        key => !["port", "TXC", "RXPA", "TXPA", "TA"].includes(key)
    );
    const lineKeys = Object.keys(deviceInfo.business_info?.line?.[0] ?? []).filter(
        key => !["port", "TXC", "RXPA", "TXPA", "TA"].includes(key)
    );

    // 将数据转换为适合表格的格式
    const transformedClientData = clientKeys.map(key => {
        const row = {key};
        deviceInfo.business_info?.client.forEach(item => {
            row[item.port] = item[key] !== "" ? normalizeNumber(item[key]) : "NA";
        });
        return row;
    });

    const transformedLineData = lineKeys.map(key => {
        const row = {key};
        deviceInfo.business_info?.line.forEach(item => {
            row[item.port] = item[key] !== "" ? normalizeNumber(item[key]) : "NA";
        });
        return row;
    });

    const tableUnit = label => {
        switch (label) {
            case "MaxWaveLength":
            case "MinWaveLength":
                return `${label}(nm)`;
            case "Rate":
                return `${label}(G/s)`;
            case "ModuleTemp":
                return `Module Temp(℃)`;
            case "ControlMode":
                return `Control Mode`;
            case "InputAlarmThreshold":
                return `Input Alarm Threshold(dBm)`;
            default:
                return label;
        }
    };

    const controlModeMap = {
        "1.00": {label: "Open", style: styles.normalTag},
        "2.00": {label: "Auto", style: styles.normalTag},
        "0.00": {label: "Close", style: styles.abnormalTag},
        "3.00": {label: "Unknown", style: styles.abnormalTag}
    };

    // 配置表格的列
    const clientColumns = [
        {
            title: "Client side",
            dataIndex: "key",
            key: "key",
            width: 190,
            render: text => <strong>{tableUnit(text)}</strong>
        },
        ...(Array.isArray(deviceInfo.business_info?.client)
            ? deviceInfo.business_info.client.map(item => {
                  return {
                      title: item.port,
                      dataIndex: item.port,
                      key: item.port,
                      align: "right",
                      render: (text, record, _) => {
                          if (record.key === "ControlMode" && ["0.00", "1.00", "2.00", "3.00"].includes(text)) {
                              const {label, style} = controlModeMap[text];
                              return <Tag className={style}>{label}</Tag>;
                          }
                          if (/^(RX|TX)\d/.test(record.key) && text !== "NA") {
                              const rxGreen = record.key.startsWith("RX") && item.RXPA === "1";
                              const txGreen = record.key.startsWith("TX") && item.TXPA === "1";
                              const textStyle = {color: rxGreen || txGreen ? "#2BC174" : "#F53F3F"};
                              return <span style={textStyle}>{text}</span>;
                          }
                          if (record.key.startsWith("ModuleTemp") && text !== "NA") {
                              const tempGreen = item.TA === "1";
                              const textStyle = {color: tempGreen ? "#2BC174" : "#F53F3F"};
                              return <span style={textStyle}>{text}</span>;
                          }
                          return text;
                      }
                  };
              })
            : [])
    ];

    const lineColumns = [
        {
            title: "Line side",
            dataIndex: "key",
            key: "key",
            width: 190,
            render: text => <strong>{tableUnit(text)}</strong>
        },
        ...(Array.isArray(deviceInfo.business_info?.line)
            ? deviceInfo.business_info.line.map(item => {
                  return {
                      title: item.port,
                      dataIndex: item.port,
                      key: item.port,
                      align: "right",
                      render: (text, record, _) => {
                          if (record.key === "ControlMode" && ["1.00", "0.00"].includes(text)) {
                              return (
                                  <Tag className={text === "1.00" ? styles.normalTag : styles.abnormalTag}>
                                      {text === "1.00" ? "Open" : "Close"}
                                  </Tag>
                              );
                          }
                          if (/^(RX|TX)\d/.test(record.key) && text !== "NA") {
                              const rxGreen = record.key.startsWith("RX") && item.RXPA === "1";
                              const txGreen = record.key.startsWith("TX") && item.TXPA === "1";
                              const textStyle = {color: rxGreen || txGreen ? "#2BC174" : "#F53F3F"};
                              return <span style={textStyle}>{text}</span>;
                          }
                          if (record.key.startsWith("ModuleTemp") && text !== "NA") {
                              const tempGreen = item.TA === "1";
                              const textStyle = {color: tempGreen ? "#2BC174" : "#F53F3F"};
                              return <span style={textStyle}>{text}</span>;
                          }
                          return text;
                      }
                  };
              })
            : [])
    ];

    const fanStatus = (deviceInfo?.fan_status ?? "").split("");

    const fanColumns = [
        {
            title: "Fan",
            dataIndex: "fan",
            key: "fan"
        },
        ...fanStatus.map((_, index) => ({
            title: `Fan${index + 1}`,
            dataIndex: `fan${index + 1}`,
            key: `fan${index + 1}`,
            render: status => <Tag className={status === "Open" ? styles.normalTag : styles.abnormalTag}>{status}</Tag>
        }))
    ];

    const fanTabledata = [
        {
            key: "1",
            fan: "Fan Status",
            ...fanStatus.reduce((acc, status, index) => {
                acc[`fan${index + 1}`] = status === "0" ? "Open" : "Off";
                return acc;
            }, {})
        }
    ];

    return (
        <div className={styles.wrap}>
            {loading && (
                <div className={styles.loading}>
                    <Spin />
                </div>
            )}
            <div className={styles.deviceDisplay}>
                <div className={styles.viewDisplay}>
                    <Card style={{margin: "0 24px"}} ref={containerRef}>
                        <Flex vertical gap="middle" ref={chassisWrapRef}>
                            <Flex justify="space-between" style={{marginTop: "12px"}}>
                                <Radio.Group
                                    options={viewRadioOptions}
                                    optionType="button"
                                    value={viewSelect}
                                    onChange={onChangeViewSelect}
                                    className={styles.customRadioGroup}
                                />
                                <div>
                                    <Button onClick={redirect}>Configure</Button>
                                    <Button onClick={sync} style={{marginLeft: "10px"}}>
                                        Synchronize
                                    </Button>
                                </div>
                            </Flex>
                            <ViewCard
                                viewSelect={viewSelect}
                                name={deviceInfo.name}
                                model={modelMap[deviceInfo.model]}
                                ip={
                                    deviceInfo.ipv4 &&
                                    deviceInfo.ipv4
                                        .split(".")
                                        .map(part => String(Number(part)))
                                        .join(".")
                                }
                                powerLedData={powerLedData}
                                clientLedData={clientLedData}
                                portData={portData}
                                fanData={fanData}
                                ref={chassisRef}
                            />
                        </Flex>
                    </Card>
                </div>
                {viewSelect === "Front" ? (
                    <>
                        <EquipmentInfoCard
                            power={deviceInfo.power}
                            backupInterval={deviceInfo.backup_interval}
                            recordInterval={deviceInfo.record_interval}
                            recordQTY={deviceInfo.record_qty}
                            clock={deviceInfo.clock}
                            ref={equipmentInfoRef}
                        />
                        <EquipmentResourceCard
                            cpuState={deviceInfo.cpu_state}
                            memoryState={deviceInfo.memory_state}
                            cpuUsed={deviceInfo.cpu_used}
                            memoryUsed={deviceInfo.memory_used}
                            cpuThreshold={deviceInfo.cpu_overload_threshold}
                            memoryThreshold={deviceInfo.memory_overload_threshold}
                        />
                        <NTPInfoCard
                            state={deviceInfo.ntp_state}
                            ip={deviceInfo.ntp_server_address}
                            timezone={deviceInfo.device_timezone}
                            interval={deviceInfo.time_synchronization_interval}
                        />
                        <PortTableCard
                            title={modelMap[deviceInfo.model]}
                            clientColumns={clientColumns}
                            clientData={transformedClientData}
                            lineColumns={lineColumns}
                            lineData={transformedLineData}
                        />
                        <TxRxListCard title="TX Derectional Power" data={txPowerData} />
                        <TxRxListCard title="RX Derectional Power" data={rxPowerData} />
                        <DispersionInfoCard
                            state={deviceInfo.business_info?.["The-dispersion-information"]?.ModuleStatus}
                            compensation={
                                deviceInfo.business_info?.["The-dispersion-information"]?.DynamicCompensationValue
                            }
                            temp={deviceInfo.business_info?.["The-dispersion-information"]?.ModuleTemp}
                            interval={deviceInfo.business_info?.["The-dispersion-information"]?.FrequencyInterval}
                        />
                        <TxRxListCard title="TX Derectional Attenuation" data={txAttenuationData} />
                        <TxRxListCard title="RX Directional Attenuation" data={rxAttenuationData} />
                    </>
                ) : (
                    <FanCard
                        fanGear={deviceInfo.fan_gear}
                        fanMode={deviceInfo.fan_mode}
                        fanData={fanTabledata}
                        fanColumns={fanColumns}
                    />
                )}
            </div>
        </div>
    );
};

const LightStatus = ({status, top, left}) => (
    <div className={parseFloat(status) ? styles.ledRunning : ""} style={{top, left}} />
);

const LightPanel = ({ledData}) => (
    <div>
        {ledData.map((led, index) => (
            <LightStatus key={index} status={led.status} top={led.top} left={led.left} />
        ))}
    </div>
);

const PortStatus = ({status, top, left}) => (
    <div
        className={classNames({
            [styles.activeInversionPort]: status === "inversion",
            [styles.activePort]: status && status !== "inversion",
            "": !status
        })}
        style={{top, left}}
    />
);

const PortPanel = ({portData}) => (
    <div>
        {portData.map((port, index) => (
            <PortStatus key={index} status={port.status} top={port.top} left={port.left} />
        ))}
    </div>
);

const FanStatus = ({status, top, left}) => (
    <div className={classNames(styles[status], styles.fan)} style={{top, left}} />
);

const FanPanel = ({fanData}) => (
    <div>
        {fanData.map((fan, index) => (
            <FanStatus key={index} status={fan.status} top={fan.top} left={fan.left} />
        ))}
    </div>
);

const TextWithBullet = ({text}) => {
    return (
        <div className={styles.textContainer}>
            <div className={styles.textContainer_bullet} />
            <Text>{text}</Text>
        </div>
    );
};

const opticalModule = [
    {
        value: "7",
        label: "L1"
    },
    {
        value: "15",
        label: "L2"
    },
    {
        value: "5",
        label: "L3"
    },
    {
        value: "13",
        label: "L4"
    },
    {
        value: "3",
        label: "L5"
    },
    {
        value: "11",
        label: "L6"
    },
    {
        value: "1",
        label: "L7"
    },
    {
        value: "9",
        label: "L8"
    }
];

const remoteOpticalModule = [
    {
        value: "7",
        label: "L1"
    },
    {
        value: "15",
        label: "L2"
    },
    {
        value: "5",
        label: "L3"
    },
    {
        value: "13",
        label: "L4"
    },
    {
        value: "3",
        label: "L5"
    },
    {
        value: "11",
        label: "L6"
    },
    {
        value: "1",
        label: "L7"
    },
    {
        value: "9",
        label: "L8"
    }
];

const cableType = [
    {
        value: "0",
        label: "G.652"
    },
    {
        value: "1",
        label: "G.655"
    }
];

const selectOptions = [
    {
        label: <span>Close</span>,
        value: "0"
    },
    {
        label: <span>Open</span>,
        value: "1"
    },
    {
        label: <span>Auto</span>,
        value: "2"
    }
];

const sideList = [
    {
        siteType: "Line Side",
        modeType: "Control Mode",
        name: "L1",
        name2: "L3",
        name3: "L5",
        name4: "L7"
    },
    {
        siteType: "Line Side",
        modeType: "Control Mode",
        name: "L2",
        name2: "L4",
        name3: "L6",
        name4: "L8"
    },
    {
        siteType: "Client Side",
        modeType: "Control Mode",
        name: "C1",
        name2: "C3",
        name3: "C5",
        name4: "C7"
    },
    {
        siteType: "Line Side",
        modeType: "Control Mode",
        name: "C2",
        name2: "C4",
        name3: "C6",
        name4: "C8"
    }
];

const CustomFormItemButton = ({
    label,
    onSend,
    itemName,
    FormComponent,
    isCheckbox = false,
    initialActive = false,
    buttonActive = false,
    initialContent = "Set",
    stateLoading = false,
    colSpan = 6,
    colOffset = 0,
    colSpan2 = 4,
    colOffset2 = 2,
    hideLabel = false,
    layout = "horizontal"
}) => {
    const [contentActive, setContentActive] = useState(initialActive);
    const [btnContent, setBtnContent] = useState(initialContent);
    const [btnLoading, setBtnLoading] = useState(false);

    const handleClick = async () => {
        if (!contentActive) {
            setContentActive(true);
            setBtnContent("Send");
        } else {
            setBtnLoading(true);
            await onSend();
            setBtnLoading(false);
            setContentActive(false);
            setBtnContent("Set");
        }
    };

    return layout === "horizontal" ? (
        <>
            <Col span={colSpan} offset={colOffset}>
                <Form.Item
                    label={!hideLabel ? <span style={{fontWeight: "bold"}}>{label}</span> : null}
                    name={itemName}
                    valuePropName={isCheckbox ? "checked" : "value"}
                >
                    {React.cloneElement(FormComponent, {disabled: !contentActive})}
                </Form.Item>
            </Col>
            <Col span={colSpan2} offset={colOffset2}>
                <Button
                    type="primary"
                    style={{width: 98}}
                    loading={stateLoading ? true : btnLoading}
                    onClick={handleClick}
                    disabled={buttonActive}
                >
                    {btnContent}
                </Button>
            </Col>
        </>
    ) : (
        <Col span={colSpan} offset={colOffset}>
            <div style={{display: "flex", flexDirection: "column", marginLeft: -90, marginTop: 16}}>
                <Form.Item
                    label={!hideLabel ? <span style={{fontWeight: "bold"}}>{label}</span> : null}
                    name={itemName}
                >
                    {React.cloneElement(FormComponent, {disabled: !contentActive})}
                </Form.Item>
                <Form.Item>
                    <Button
                        type="primary"
                        style={{width: 98, marginLeft: 0}}
                        loading={btnLoading}
                        onClick={handleClick}
                        disabled={buttonActive}
                    >
                        {btnContent}
                    </Button>
                </Form.Item>
            </div>
        </Col>
    );
};

const AutoAdjustment = ({ip, isShowDispersionModal}) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const [testLoading, setTestLoading] = useState(false);
    const [LBTV, setLBTV] = useState("N/A");
    const [TDCMV, setTDCMV] = useState("N/A");
    const [TS, setTS] = useState("N/A");
    const [RBTV, setRBTV] = useState("N/A");

    const itemList = [
        {
            label: "Cable Length(km)",
            name: "Cable_length",
            label2: "Fixed The Dispersion",
            name2: "Fixed_the_dispersion"
        },
        {label: "Dispersion Scope", name: "Dispersion_scope", label2: "Dispersion Slot", name2: "Dispersion_slot"},
        {
            label: "Optical Module",
            name: "Optical_module",
            label2: "Remote Optical Module",
            name2: "Remote_optical_module"
        },

        {label: "Cable Type", name: "Cable_type", label2: "Remote IPV4", name2: "Remote_IPV4"}
    ];

    const dispersionSlot = Array.from({length: 16}, (_, index) => ({
        value: (index + 1).toString(),
        label: (index + 1).toString()
    }));

    const formComponentsMap = {
        Dispersion_slot: <Select style={{width: 280}} options={dispersionSlot} />,
        Optical_module: <Select style={{width: 280}} options={opticalModule} />,
        Remote_optical_module: <Select style={{width: 280}} options={remoteOpticalModule} />,
        Cable_type: <Select style={{width: 280}} options={cableType} />
    };

    const fetchConfigData = async () => {
        await queryDCP920Config(ip, "dispersion_automatic").then(res => {
            if (res.errorCode === 0) {
                form.setFieldsValue({
                    // Adjust_state: res.data.Adjust_state === "1",
                    Cable_length: res.data.Cable_length,
                    Fixed_the_dispersion: res.data.Fixed_the_dispersion,
                    Dispersion_scope: res.data.Dispersion_scope,
                    Dispersion_slot: res.data.Dispersion_slot,
                    Optical_module: res.data.Optical_module,
                    Remote_optical_module: res.data.Remote_optical_module,
                    Cable_type: res.data.Cable_type,
                    Remote_IPV4: res.data.Remote_IPV4
                });
                res.data?.Control_mode?.line.split("").forEach((item, index) => {
                    form.setFieldValue(`L${index + 1}`, item);
                });
                res.data?.Control_mode?.client.split("").forEach((item, index) => {
                    form.setFieldValue(`C${index + 1}`, item);
                });
            } else {
                console.log("error");
            }
        });
        setLoading(false);
    };

    const fetchTestResult = async () => {
        const statusInfo = {
            0: "Stop",
            1: "Testing",
            2: "Finish",
            3: "Error Exit"
        };
        await queryDCP920Test(ip).then(res => {
            if (res.errorCode === 0) {
                form.setFieldValue("Adjust_state", res.data.test_status === "1");
                setLBTV(res.data.left_border_tdcm_value);
                setRBTV(res.data.right_border_tdcm_value);
                setTDCMV(res.data.tdcm_value);
                setTS(statusInfo[res.data.test_status]);
            }
            setTestLoading(false);
        });
    };

    useEffect(() => {
        setLoading(true);
        setTestLoading(true);

        fetchConfigData().then();
        fetchTestResult().then();
    }, [ip, isShowDispersionModal]);

    return (
        <Form form={form} labelCol={{span: 12}} wrapperCol={{span: 12}} labelAlign="left" style={{marginTop: "24px"}}>
            <Row gutter={32}>
                <CustomFormItemButton
                    label="Adjust State"
                    itemName="Adjust_state"
                    isCheckbox
                    onSend={async () => {
                        await modifyDCP920Config(ip, {
                            config_type: "dispersion_automatic",
                            key: "Adjust_state",
                            value: form.getFieldValue("Adjust_state") ? "1" : "0"
                        }).then(rs => {
                            if (rs.errorCode === 0) {
                                fetchConfigData();
                                message.success("Configuration has been successfully delivered");
                            } else {
                                message.error("Configuration delivery failed");
                            }
                        });
                    }}
                    FormComponent={<Switch loading={testLoading} />}
                    stateLoading={testLoading}
                />
            </Row>
            <Spin spinning={loading}>
                {itemList.map((item, index) => (
                    <Row gutter={32}>
                        <CustomFormItemButton
                            label={item.label}
                            itemName={item.name}
                            onSend={async () => {
                                await modifyDCP920Config(ip, {
                                    config_type: "dispersion_automatic",
                                    key: item.name,
                                    value: form.getFieldValue(item.name)
                                }).then(rs => {
                                    if (rs.errorCode === 0) {
                                        fetchConfigData();
                                        message.success("Configuration has been successfully delivered");
                                    } else {
                                        message.error("Configuration delivery failed");
                                    }
                                });
                            }}
                            FormComponent={formComponentsMap[item.name] || <Input style={{width: 280}} />}
                        />

                        <CustomFormItemButton
                            label={item.label2}
                            itemName={item.name2}
                            onSend={async () => {
                                await modifyDCP920Config(ip, {
                                    config_type: "dispersion_automatic",
                                    key: item.name2,
                                    value: form.getFieldValue(item.name2)
                                }).then(rs => {
                                    if (rs.errorCode === 0) {
                                        fetchConfigData();
                                        message.success("Configuration has been successfully delivered");
                                    }
                                });
                            }}
                            FormComponent={formComponentsMap[item.name2] || <Input style={{width: 280}} />}
                        />
                    </Row>
                ))}
                <div style={{margin: "16px 24px 40px 24px"}}>
                    <Divider />
                </div>
                {sideList.map((item, index) => (
                    <>
                        <Row>
                            {[
                                [item.siteType, 0],
                                [item.name, -30],
                                [item.name2, 0],
                                [item.name3, 0],
                                [item.name4, 0]
                            ].map(([name, offset], index) => (
                                <Col key={index} span={3} style={{marginLeft: offset}}>
                                    <span style={{fontWeight: "bold"}}>{name}</span>
                                </Col>
                            ))}
                        </Row>
                        <Row>
                            <Col span={4} style={{marginTop: 16}}>
                                <Form.Item label={<span style={{fontWeight: "bold"}}>{item.modeType}</span>} />
                            </Col>
                            <CustomFormItemButton
                                label={item.name}
                                itemName={item.name}
                                hideLabel
                                buttonActive={form.getFieldValue(item.name) === "3"}
                                onSend={() => {
                                    const keyPrefix = item.name.startsWith("L")
                                        ? `Control_mode@line@${item.name.slice(-1)}`
                                        : `Control_mode@client@${item.name.slice(-1)}`;

                                    modifyDCP920Config(ip, {
                                        config_type: "dispersion_automatic",
                                        key: keyPrefix,
                                        value: form.getFieldValue(item.name)
                                    }).then(rs => {
                                        if (rs.errorCode === 0) {
                                            fetchConfigData();
                                            message.success("Configuration has been successfully delivered");
                                        } else {
                                            message.error("Configuration delivery failed");
                                        }
                                    });
                                }}
                                colSpan={3}
                                layout="vertical"
                                FormComponent={
                                    <Select
                                        style={{width: 140}}
                                        options={
                                            form.getFieldValue(item.name) === "3"
                                                ? [{label: <span>Select</span>, value: "3"}, ...selectOptions]
                                                : selectOptions
                                        }
                                    />
                                }
                            />
                            <CustomFormItemButton
                                label={item.name2}
                                itemName={item.name2}
                                buttonActive={form.getFieldValue(item.name2) === "3"}
                                hideLabel
                                onSend={async () => {
                                    const keyPrefix = item.name2.startsWith("L")
                                        ? `Control_mode@line@${item.name2.slice(-1)}`
                                        : `Control_mode@client@${item.name2.slice(-1)}`;

                                    await modifyDCP920Config(ip, {
                                        config_type: "dispersion_automatic",
                                        key: keyPrefix,
                                        value: form.getFieldValue(item.name2)
                                    }).then(rs => {
                                        if (rs.errorCode === 0) {
                                            fetchConfigData();
                                            message.success("Configuration has been successfully delivered");
                                        } else {
                                            message.error("Configuration delivery failed");
                                        }
                                    });
                                }}
                                colSpan={3}
                                layout="vertical"
                                FormComponent={
                                    <Select
                                        style={{width: 140}}
                                        options={
                                            form.getFieldValue(item.name2) === "3"
                                                ? [{label: <span>Select</span>, value: "3"}, ...selectOptions]
                                                : selectOptions
                                        }
                                    />
                                }
                            />
                            <CustomFormItemButton
                                label={item.name3}
                                itemName={item.name3}
                                buttonActive={form.getFieldValue(item.name3) === "3"}
                                hideLabel
                                onSend={async () => {
                                    const keyPrefix = item.name3.startsWith("L")
                                        ? `Control_mode@line@${item.name3.slice(-1)}`
                                        : `Control_mode@client@${item.name3.slice(-1)}`;

                                    await modifyDCP920Config(ip, {
                                        config_type: "dispersion_automatic",
                                        key: keyPrefix,
                                        value: form.getFieldValue(item.name3)
                                    }).then(rs => {
                                        if (rs.errorCode === 0) {
                                            fetchConfigData();
                                            message.success("Configuration has been successfully delivered");
                                        } else {
                                            message.error("Configuration delivery failed");
                                        }
                                    });
                                }}
                                colSpan={3}
                                layout="vertical"
                                FormComponent={
                                    <Select
                                        style={{width: 140}}
                                        options={
                                            form.getFieldValue(item.name3) === "3"
                                                ? [{label: <span>Select</span>, value: "3"}, ...selectOptions]
                                                : selectOptions
                                        }
                                    />
                                }
                            />
                            <CustomFormItemButton
                                label={item.name4}
                                itemName={item.name4}
                                buttonActive={form.getFieldValue(item.name4) === "3"}
                                hideLabel
                                onSend={async () => {
                                    const keyPrefix = item.name4.startsWith("L")
                                        ? `Control_mode@line@${item.name4.slice(-1)}`
                                        : `Control_mode@client@${item.name4.slice(-1)}`;

                                    await modifyDCP920Config(ip, {
                                        config_type: "dispersion_automatic",
                                        key: keyPrefix,
                                        value: form.getFieldValue(item.name4)
                                    }).then(rs => {
                                        if (rs.errorCode === 0) {
                                            fetchConfigData();
                                            message.success("Configuration has been successfully delivered");
                                        } else {
                                            message.error("Configuration delivery failed");
                                        }
                                    });
                                }}
                                colSpan={3}
                                layout="vertical"
                                FormComponent={
                                    <Select
                                        style={{width: 140}}
                                        options={
                                            form.getFieldValue(item.name4) === "3"
                                                ? [{label: <span>Select</span>, value: "3"}, ...selectOptions]
                                                : selectOptions
                                        }
                                    />
                                }
                            />
                        </Row>
                    </>
                ))}
            </Spin>
            <Spin spinning={testLoading}>
                <div style={{margin: "24px 24px 40px 24px"}}>
                    <Divider />
                </div>
                <h3>The Test Results</h3>
                <div style={{fontWeight: "bold", marginBottom: 24}}>
                    <Row style={{marginBottom: 24, marginTop: 24}}>
                        <Col span={4}>
                            <TextWithBullet text="Left Border TDCM Value" />
                        </Col>
                        <Col span={2}>
                            <span>{LBTV}</span>
                        </Col>
                        <Col span={4}>
                            <TextWithBullet text="TDCM Value" />
                        </Col>
                        <Col span={2}>
                            <span>{TDCMV}</span>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={4}>
                            <TextWithBullet text="Test Status" />
                        </Col>
                        <Col span={2}>
                            <Tag
                                className={classNames({
                                    [styles.normalTag]: TS === "Finish",
                                    [styles.stopTag]: TS === "Stop",
                                    [styles.testingTag]: TS === "Testing",
                                    [styles.abnormalTag]: TS !== "Finish" && TS !== "Stop" && TS !== "Testing"
                                })}
                            >
                                {TS}
                            </Tag>
                        </Col>
                        <Col span={4}>
                            <TextWithBullet text="Right Border TDCM Value" />
                        </Col>
                        <Col span={2}>
                            <span>{RBTV}</span>
                        </Col>
                    </Row>
                </div>
            </Spin>
        </Form>
    );
};

const ManaualAdjustment = ({ip, isShowDispersionModal}) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);
    const selectOptions = [
        {
            label: "100",
            value: "196.000"
        },
        {
            label: "50",
            value: "196.050"
        }
    ];

    const frequencyInterval = [
        {
            value: "196.050",
            label: "50"
        },
        {
            value: "196.000",
            label: "100"
        }
    ];

    const fetchData = async () => {
        setLoading(true);
        queryDCP920Config(ip, "dispersion_manual").then(res => {
            if (res.errorCode === 0) {
                form.setFieldsValue({
                    dcvs: String(Number(res.data.Dynamic_compensation)),
                    fis: `${res.data.Frequency_interval.slice(0, 3)}.${res.data.Frequency_interval.slice(3)}`
                });
                setLoading(false);
            } else {
                console.log("error");
            }
        });
    };

    useEffect(() => {
        fetchData();
    }, [ip, isShowDispersionModal]);

    return (
        <Spin spinning={loading}>
            <Form
                form={form}
                labelCol={{span: 16}}
                wrapperCol={{span: 8}}
                labelAlign="left"
                style={{marginTop: "24px", width: "70%"}}
            >
                <Row gutter={[16, 8]}>
                    <CustomFormItemButton
                        label="Dynamic Compensation Value Setting(ps/nm)"
                        itemName="dcvs"
                        onSend={async () => {
                            await modifyDCP920Config(ip, {
                                config_type: "dispersion_manual",
                                key: "Dynamic_compensation",
                                value: form.getFieldValue("dcvs")
                            }).then(rs => {
                                if (rs.errorCode === 0) {
                                    queryDCP920Info(ip);
                                    message.success("Configuration has been successfully delivered");
                                } else {
                                    message.error("Configuration delivery failed");
                                }
                            });
                        }}
                        colSpan={13}
                        colSpan2={7}
                        colOffset2={3}
                        FormComponent={<Input style={{width: 280}} />}
                    />
                </Row>
                <Row gutter={[16, 8]}>
                    <CustomFormItemButton
                        label="Frequency Interval Setting(GHz)"
                        itemName="fis"
                        onSend={async () => {
                            await modifyDCP920Config(ip, {
                                config_type: "dispersion_manual",
                                key: "Frequency_interval",
                                value: form.getFieldValue("fis")
                            }).then(rs => {
                                if (rs.errorCode === 0) {
                                    queryDCP920Info(ip);
                                    message.success("Configuration has been successfully delivered");
                                } else {
                                    message.error("Configuration delivery failed");
                                }
                            });
                        }}
                        colSpan={13}
                        colSpan2={7}
                        colOffset2={3}
                        FormComponent={<Select style={{width: 280}} options={frequencyInterval} />}
                    />
                </Row>
            </Form>
        </Spin>
    );
};

const PowerConfigModal = ({ip, isShowPowerModal, setIsShowPowerModal}) => {
    const [form] = useForm();
    const [loading, setLoading] = useState(false);

    const [txAttenuationDisabled, setTxAttenuationDisabled] = useState(false);
    const [rxAttenuationDisabled, setRxAttenuationDisabled] = useState(false);
    const [txPowerDisabled, setTxPowerDisabled] = useState(false);
    const [rxPowerDisabled, setRxPowerDisabled] = useState(false);

    const itemList = [
        {label: "TX Direction Power Adjustment(dB):", name: "TX_direction_power"},
        {label: "RX Direction Power Adjustment(dB):", name: "RX_direction_power"},
        {label: "TX Work Model Configuration:", name: "TX_Work_model"},
        {label: "TX Alarm Threshold Configuration:", name: "TX_Alarm_threshold"},
        {label: "Tx Power Configuration:", name: "TX_Power"},
        {label: "TX Attenuation Configuration:", name: "TX_Attenuation"},
        {label: "Rx Work Model Configuration:", name: "RX_Work_model"},
        {label: "RX Alarm Threshold Configuration:", name: "RX_Alarm_threshold"},
        {label: "RX Power Configuration:", name: "RX_Power"},
        {label: "RX Attenuation Configuration:", name: "RX_Attenuation"}
    ];
    // const workModeMap = {1: "Auto", 0: "Manual"};
    const txPowerAdjOptions = Array.from({length: 7}, (_, i) => ({
        value: (11 + i).toString(),
        label: i - 3 > 0 ? `+${i - 3}` : (i - 3).toString()
    }));
    const rxPowerAdjOptions = Array.from({length: 7}, (_, i) => ({
        value: (19 + i).toString(),
        label: i - 3 > 0 ? `+${i - 3}` : (i - 3).toString()
    }));

    const workModeOptions = [
        {
            value: "1",
            label: "Auto"
        },
        {
            value: "0",
            label: "Manual"
        }
    ];

    const buttonStates = {
        TX_Attenuation: txAttenuationDisabled,
        TX_Power: txPowerDisabled,
        RX_Attenuation: rxAttenuationDisabled,
        RX_Power: rxPowerDisabled
    };

    const formComponentsMap = {
        TX_direction_power: <Select style={{width: 280}} options={txPowerAdjOptions} />,
        RX_direction_power: <Select style={{width: 280}} options={rxPowerAdjOptions} />,
        TX_Work_model: <Select style={{width: 280}} options={workModeOptions} />,
        RX_Work_model: <Select style={{width: 280}} options={workModeOptions} />
    };

    const fetchConfigData = async () => {
        await queryDCP920Config(ip, "power").then(res => {
            if (res.errorCode === 0) {
                form.setFieldsValue({
                    TX_direction_power: normalizeNumber(res.data.TX_direction_power, 0),
                    RX_direction_power: normalizeNumber(res.data.RX_direction_power, 0),
                    TX_Work_model: res.data.TX_Work_model,
                    TX_Alarm_threshold: normalizeNumber(res.data.TX_Alarm_threshold),
                    TX_Power: normalizeNumber(res.data.TX_Power),
                    TX_Attenuation: normalizeNumber(res.data.TX_Attenuation),
                    RX_Work_model: res.data.RX_Work_model,
                    RX_Alarm_threshold: normalizeNumber(res.data.RX_Alarm_threshold),
                    RX_Power: normalizeNumber(res.data.RX_Power),
                    RX_Attenuation: normalizeNumber(res.data.RX_Attenuation)
                });
            } else {
                console.log(res.errorMsg);
            }
            const txWorkModel = res.data.TX_Work_model;
            const rxWorkModel = res.data.RX_Work_model;

            if (txWorkModel === "0") {
                // Manual
                setTxAttenuationDisabled(false);
                setTxPowerDisabled(true);
            } else {
                // Auto
                setTxAttenuationDisabled(true);
                setTxPowerDisabled(false);
            }

            // RX Work Model Logic
            if (rxWorkModel === "0") {
                // Manual
                setRxAttenuationDisabled(false);
                setRxPowerDisabled(true);
            } else {
                // Auto
                setRxAttenuationDisabled(true);
                setRxPowerDisabled(false);
            }
            setLoading(false);
        });
    };

    useEffect(() => {
        if (isShowPowerModal) {
            setLoading(true);
            fetchConfigData();
        }
    }, [isShowPowerModal, ip]);

    return (
        <Modal
            className={classNames("ampcon-max-modal")}
            title={
                <div>
                    Power Config
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowPowerModal}
            onCancel={() => {
                setIsShowPowerModal(false);
                form.resetFields();
            }}
            destroyOnClose
            footer={null}
        >
            <Spin spinning={loading}>
                <Form
                    form={form}
                    style={{width: "70%"}}
                    labelCol={{span: 12}}
                    wrapperCol={{span: 12}}
                    labelAlign="left"
                >
                    {itemList.map((item, index) => (
                        <Row gutter={16} key={item.name}>
                            <CustomFormItemButton
                                label={item.label}
                                itemName={item.name}
                                onSend={async () => {
                                    await modifyDCP920Config(ip, {
                                        config_type: "power",
                                        key: item.name,
                                        value: form.getFieldValue(item.name)
                                    }).then(rs => {
                                        if (rs.errorCode === 0) {
                                            message.success("Configuration has been successfully delivered");
                                            fetchConfigData();
                                        } else {
                                            message.error("Configuration delivery failed");
                                        }
                                    });
                                }}
                                colSpan={15}
                                colSpan2={2}
                                colOffset2={0}
                                FormComponent={formComponentsMap[item.name] || <Input style={{width: 280}} />}
                                buttonActive={buttonStates[item.name]}
                            />
                        </Row>
                    ))}
                </Form>
            </Spin>
        </Modal>
    );
};

const DispersionConfigModal = ({ip, isShowDispersionModal, setIsShowDispersionModal}) => {
    const itemTab = [
        {
            key: "auto",
            label: "Automatic Adjustment",
            children: <AutoAdjustment ip={ip} isShowDispersionModal={isShowDispersionModal} />
        },
        {
            key: "manual",
            label: "Manual Adjustment",
            children: <ManaualAdjustment ip={ip} isShowDispersionModal={isShowDispersionModal} />
        }
    ];

    return (
        <Modal
            className={classNames("ampcon-max-modal", styles.modalWrap)}
            title={
                <div>
                    Dispersion Config
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowDispersionModal}
            onCancel={() => {
                setIsShowDispersionModal(false);
            }}
            destroyOnClose
            footer={null}
        >
            <Tabs className="radioGroupTabs" items={itemTab} />
        </Modal>
    );
};

const ViewCard = memo(
    forwardRef(({viewSelect, name, model = "", ip = "", powerLedData, clientLedData, portData, fanData}, ref) => {
        const [open, setOpen] = useState(false);
        const [isShowPowerModal, setIsShowPowerModal] = useState(false);
        const [isShowDispersionModal, setIsShowDispersionModal] = useState(false);
        const handleContextMenu = e => {
            e.preventDefault();
            setOpen(true);
        };

        const items = [
            {
                label: "Power Config",
                key: "0",
                icon: <Icon component={PowerSvg} />,
                onClick: () => {
                    setIsShowPowerModal(true);
                }
            },
            {
                label: "Dispersion Config",
                key: "1",
                icon: <Icon component={DispersionSvg} />,
                onClick: () => {
                    setIsShowDispersionModal(true);
                }
            }
        ];

        return (
            <Flex vertical align="center" justify="center" gap="24px">
                <PowerConfigModal
                    ip={ip}
                    isShowPowerModal={isShowPowerModal}
                    setIsShowPowerModal={setIsShowPowerModal}
                />
                <DispersionConfigModal
                    ip={ip}
                    isShowDispersionModal={isShowDispersionModal}
                    setIsShowDispersionModal={setIsShowDispersionModal}
                />
                <Dropdown
                    menu={{items}}
                    trigger={["contextMenu"]}
                    open={open}
                    onOpenChange={flag => setOpen(flag)}
                    onContextMenu={handleContextMenu}
                >
                    {viewSelect === "Front" ? (
                        <div ref={ref} className={styles.chassisFront}>
                            <div className={styles.networkCard} style={{top: 4, left: 60}} />
                            <div className={styles.statusCard} style={{top: 4, left: 305}} />
                            <LightPanel ledData={powerLedData} />
                            <LightPanel ledData={clientLedData} />
                            <PortPanel portData={portData} />
                            <span className={styles.model} style={{top: 160, right: 90}}>
                                {model}
                            </span>
                            <div className={styles.logo} style={{top: 15, right: 90}} />
                        </div>
                    ) : (
                        <div ref={ref} className={styles.chassisRear}>
                            <div className={styles.powerCard} style={{top: 4, left: 87}} />
                            <div className={styles.powerCard} style={{top: 4, right: 90}} />
                            <FanPanel fanData={fanData} />
                        </div>
                    )}
                </Dropdown>
                <Text style={{paddingBottom: "20px"}}>{ip ? `${model} (${ip})` : ""}</Text>
            </Flex>
        );
    })
);

const EquipmentInfoCard = forwardRef(({power, backupInterval, recordInterval, recordQTY, clock}, ref) => {
    const [time, setTime] = useState(clock || new Date().toISOString().slice(0, 19).replace("T", " "));
    useImperativeHandle(ref, () => ({
        setClock(clock) {
            setTime(clock);
        }
    }));
    useEffect(() => {
        const intervalId = setInterval(() => {
            setTime(prevTime => {
                if (prevTime) {
                    const date = new Date(`${prevTime.replace(" ", "T")}Z`);
                    date.setSeconds(date.getSeconds() + 1);
                    return date.toISOString().slice(0, 19).replace("T", " ");
                }
                return clock || new Date().toISOString().slice(0, 19).replace("T", " ");
            });
        }, 1000);

        return () => {
            clearInterval(intervalId);
        };
    }, []);
    useEffect(() => {
        setTime(clock);
    }, [clock]);

    return (
        <Card title="Equipment Information" style={{width: "100%"}}>
            <Flex vertical gap="middle" style={{paddingTop: "12px", paddingBottom: "12px"}}>
                <Row gutter={[24, 16]} justify="start" align="middle">
                    <Col span={5}>
                        <Text style={{marginRight: "32px"}}>Power1</Text>
                        <Tag
                            className={
                                parseFloat(power?.charAt(0) ?? "" === "1") ? styles.normalTag : styles.abnormalTag
                            }
                        >
                            {parseFloat(power?.charAt(0) ?? "" === "1") ? "Open" : "Off"}
                        </Tag>
                    </Col>
                    <Col span={5}>
                        <Text style={{marginRight: "32px"}}>Power2</Text>
                        <Tag
                            className={
                                parseFloat(power?.charAt(1) ?? "" === "1") ? styles.normalTag : styles.abnormalTag
                            }
                        >
                            {parseFloat(power?.charAt(1) ?? "" === "1") ? "Open" : "Off"}
                        </Tag>
                    </Col>
                    <Col span={14} />
                    <Col span={12}>
                        <Text className={styles.customText}>Backup Interval (Sec)</Text>
                        <Input className={styles.customInput} value={backupInterval} disabled="0" />
                    </Col>
                    <Col span={12}>
                        <Text className={styles.customText}>Record Interval (Sec)</Text>
                        <Input className={styles.customInput} value={recordInterval} disabled="0" />
                    </Col>
                    <Col span={12}>
                        <Text className={styles.customText}>Record QTY (Units)</Text>
                        <Input className={styles.customInput} value={recordQTY} disabled="0" />
                    </Col>
                    <Col span={12}>
                        <Text className={styles.customText}>Clock</Text>
                        <Input className={styles.customInput} value={time} disabled="0" />
                    </Col>
                </Row>
            </Flex>
        </Card>
    );
});

const EquipmentResourceCard = memo(({cpuState, memoryState, cpuUsed, memoryUsed, cpuThreshold, memoryThreshold}) => {
    return (
        <Card title="Equipment Resources" style={{width: "100%"}}>
            <Flex vertical gap="middle" style={{paddingTop: "12px", paddingBottom: "12px"}}>
                <Row gutter={[24, 16]} justify="start" align="middle">
                    <Col span={12}>
                        <Text className={styles.customText}>CPU State</Text>
                        <Tag className={cpuState === "1" ? styles.flexibleNormalTag : styles.flexibleAbnormalTag}>
                            {cpuState === "1" ? "Normal" : "Overload"}
                        </Tag>
                    </Col>
                    <Col span={12}>
                        <Text className={styles.customText}>Memory State</Text>
                        <Tag className={memoryState === "1" ? styles.flexibleNormalTag : styles.flexibleAbnormalTag}>
                            {memoryState === "1" ? "Normal" : "Overload"}
                        </Tag>
                    </Col>
                    <Col span={12}>
                        <Text className={styles.customText}>CPU Used</Text>
                        <Progress
                            style={{width: "315px", marginBottom: "0"}}
                            percent={cpuUsed}
                            strokeColor={+cpuThreshold && +cpuUsed >= +cpuThreshold ? "#F53F3F" : "#14C9BB"}
                            format={percent => {
                                if (+cpuThreshold && +percent >= +cpuThreshold) {
                                    return <span style={{color: "#F53F3F"}}>{percent}%</span>;
                                }
                                return <span style={{color: "#14C9BB"}}>{percent}%</span>;
                            }}
                        />
                    </Col>
                    <Col span={12}>
                        <Text className={styles.customText}>Memory Used</Text>
                        <Progress
                            style={{width: "315px", marginBottom: "0"}}
                            percent={memoryUsed}
                            strokeColor={+memoryThreshold && +memoryUsed >= +memoryThreshold ? "#F53F3F" : "#14C9BB"}
                            format={percent => {
                                if (+memoryThreshold && +percent >= +memoryThreshold) {
                                    return <span style={{color: "#F53F3F"}}>{percent}%</span>;
                                }
                                return <span style={{color: "#14C9BB"}}>{percent}%</span>;
                            }}
                        />
                    </Col>
                    <Col span={12}>
                        <Text className={styles.customText}>CPU Overload Threshold(%)</Text>
                        <Input className={styles.customInput} value={cpuThreshold} disabled="0" />
                    </Col>
                    <Col span={12}>
                        <Text className={styles.customText}>Memory Overload Threshold(%)</Text>
                        <Input className={styles.customInput} value={memoryThreshold} disabled="0" />
                    </Col>
                </Row>
            </Flex>
        </Card>
    );
});

const NTPInfoCard = memo(({state, ip, timezone = "GMT+8", interval}) => {
    return (
        <Card title="NTP Information" style={{width: "100%"}}>
            <Flex vertical gap="middle" style={{paddingTop: "12px", paddingBottom: "12px"}}>
                <Row gutter={[24, 16]} justify="start" align="middle">
                    <Col span={12}>
                        <Text className={styles.customText}>NTP State</Text>
                        <Tag className={state === "1" ? styles.flexibleNormalTag : styles.flexibleAbnormalTag}>
                            {state === "1" ? "Normal" : "Abnormal"}
                        </Tag>
                    </Col>
                    <Col span={12} />
                    <Col span={12}>
                        <Text className={styles.customText}>NTP Server Address</Text>
                        <Input
                            className={styles.customInput}
                            value={
                                ip &&
                                ip
                                    .split(".")
                                    .map(part => String(Number(part)))
                                    .join(".")
                            }
                            disabled="0"
                        />
                    </Col>
                    <Col span={12}>
                        <Text className={styles.customText}>Device Timezone</Text>
                        <Input className={styles.customInput} value={timezone} disabled="0" />
                    </Col>
                    <Col span={12}>
                        <Text className={styles.customText}>Time Synchronization Interval(s)</Text>
                        <Input className={styles.customInput} value={interval} disabled="0" />
                    </Col>
                </Row>
            </Flex>
        </Card>
    );
});

const DispersionInfoCard = memo(({state, compensation, temp, interval}) => {
    return (
        <Card title="The Dispersion Information" style={{width: "100%"}}>
            <Flex
                horizontal
                wrap
                justify="space-between"
                align="middle"
                style={{padding: "12px 0", width: "100%", flexWrap: "wrap"}}
            >
                <div className={styles.flexibleItem}>
                    <TextWithBullet text="Module Status" />
                    <Tag className={state === "0" ? styles.normalTag : styles.abnormalTag}>
                        {state === "0" ? "Normal" : "Abnormal"}
                    </Tag>
                </div>
                <div className={styles.flexibleItem}>
                    <TextWithBullet text="Dynamic Compensation Value(ps/nm)" />
                    <Text>{normalizeNumber(compensation)}</Text>
                </div>
                <div className={styles.flexibleItem}>
                    <TextWithBullet text="Module Temp(℃)" />
                    <Text>{normalizeNumber(temp)}</Text>
                </div>
                <div className={styles.flexibleItem}>
                    <TextWithBullet text="Frequency Interval(GHz)" />
                    <Text>{interval}</Text>
                </div>
            </Flex>
        </Card>
    );
});

const PortTableCard = memo(({title, clientColumns, clientData, lineColumns, lineData}) => {
    return (
        <Card title={title} style={{width: "100%"}}>
            <Flex vertical gap="middle" style={{paddingTop: "12px", paddingBottom: "12px"}}>
                <Table
                    dataSource={clientData}
                    columns={clientColumns}
                    rowClassName={(record, index) => (index % 2 === 0 ? styles.tableEvenRow : styles.tableOddRow)}
                    pagination={false}
                    tableLayout="fixed"
                    style={{width: "100%"}}
                />
                <Divider />
                <Table
                    dataSource={lineData}
                    columns={lineColumns}
                    rowClassName={(record, index) => (index % 2 === 0 ? styles.tableEvenRow : styles.tableOddRow)}
                    pagination={false}
                    tableLayout="fixed"
                    style={{width: "100%"}}
                />
            </Flex>
        </Card>
    );
});

const TxRxListCard = memo(({title, data}) => {
    return (
        <Card title={title} style={{width: "100%"}}>
            <List
                dataSource={data}
                renderItem={(item, index) => (
                    <List.Item
                        style={{border: "none"}}
                        key={item.title}
                        className={index % 2 === 0 ? styles.listEvenRow : styles.listOddRow}
                    >
                        <div className={styles.listItem}>
                            <Text>{item.title}</Text>
                        </div>
                        <Text
                            style={{
                                color: item.specialColor,
                                textAlign: "right"
                            }}
                            className={styles.listText}
                        >
                            {item.value}
                        </Text>
                    </List.Item>
                )}
            />
        </Card>
    );
});

const FanCard = memo(({fanGear, fanMode, fanData, fanColumns}) => {
    const fanSpeedMap = {10: "LOW", 50: "MIDDLE", 100: "HIGH"};
    return (
        <Card title="Fan Information" style={{width: "100%"}}>
            <Flex vertical gap="middle" style={{paddingTop: "12px", paddingBottom: "12px"}}>
                <Row gutter={[24, 16]} justify="start" align="middle">
                    <Col span={12}>
                        <Text style={{marginRight: "32px"}}>Fan Gear</Text>
                        <Input className={styles.customInput} value={fanSpeedMap[fanGear]} disabled="0" />
                    </Col>
                    <Col span={12}>
                        <Text style={{marginRight: "32px"}}>Fan Mode</Text>
                        <Input className={styles.customInput} value={fanMode === 1 ? "Auto" : "Manual"} disabled="0" />
                    </Col>
                </Row>
                <Table dataSource={fanData} columns={fanColumns} pagination={false} />
            </Flex>
        </Card>
    );
});

export default DeviceDisplay;
