.card {
  //margin-top: 20px;
  // min-width: 1377px;
  display: flex;
  height: 100%;
  border-radius: 10px;

  &_tag {
    font-size: 14px;
    background: rgba(43, 193, 116, 0.1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #2BC174;
    color: #2BC174
  }

  &_tagColor {
    font-size: 14px;
    color: #F53F3F;
    background: #fff0f6;
    border-color: #ffadd2;
  }

  &_renderButton {
    color: #14c9bb;
    font-weight:bold;
    cursor: default;
  }

  &_renderButton:hover {
    color: var(--primary-color-hover);
  }

  &_more_group {
    margin-top:24px;
  }

  &_more_group_title {
    font-weight: bold;
    font-size: large;
    padding-bottom: 5px;
  }

  &_more_group_content {
    padding-bottom: 36px;
    border-bottom: 1px solid #f0f2f5;
  }

  &_more_group_content_row {
    display:flex;
    align-items: center;
    padding: 10px;
  }

  :global {
    .ant-typography {
      div:first-child {
        width: calc(100%);
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }

    .ant-typography-edit-content {
      margin: 0;
      inset-inline-start: 0;

      .ant-input {
        padding-right: 20px;
        overflow-y: hidden;
      }
    }

    .ant-typography-edit-content .ant-typography-edit-content-confirm {
      inset-block-end: 40%;
    }

    .ant-card-body {
      padding: 24px !important;
    }
  }
}

.tableContainer {
    height: 100%;
    overflow: auto;
}

.item {
  width: calc(25% - 20px);
  display: flex;
  align-items: center;
  height: 40px;
  margin-right: 20px;

  .dot {
    width: 8px;
    height: 8px;
    background: #14c9bb;
    border-radius: 50%;
    margin-right: 5px;
  }

  &_label {
    text-wrap: nowrap;
  }

  &_value {
    text-wrap: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
  }
}

.item:nth-child(4n) {
  margin-right: 0;
}


.operate {
  font-size: 14px;
  color: #14C9BB;
  text-align: center;
  cursor: pointer;
}

.cycle_div {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  font-weight: bold;
}

.customSelect {
    :global {
      .ant-select {
        border: none !important;
        box-shadow: none !important;
        padding: 0 !important;
        text-align: left;
      }

      .ant-select-selector {
        border: none !important;
        background-color: transparent !important;
        text-align: left !important;
        padding-left: 0 !important;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }

      .ant-select-arrow {
        font-size: 10px !important;
      }
  }
}

.normalTag {
  color: #2BC174;
  background-color: rgba(43, 193, 116, 0.1);
  border-color: #2BC174;
  margin-right: 0;
}

.abnormalTag {
  color: #F53F3F;
  background-color: rgba(245, 63 ,63 , 0.1);
  border-color: #F53F3F;
  margin-right: 0;
}
