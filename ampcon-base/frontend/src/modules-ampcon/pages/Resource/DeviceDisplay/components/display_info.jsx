import React, {useEffect, useLayoutEffect, useRef, useState} from "react";
import {middleModal} from "./custom_modal";
import {Col, Row} from "antd";
import styles from "./card.module.scss";
import {getText, NULL_VALUE} from "../utils";

const DisplayInfo = ({columns, getData}) => {
    const [data, setData] = useState({});
    const [tableColumns, setTableColumns] = useState([]);
    const [labelWidth, setLabelWidth] = useState(0);
    const containerRef = useRef(null);

    useEffect(() => {
        getData().then(rs => {
            setData(rs);
            if (typeof columns === "function") {
                setTableColumns(columns(rs));
            }
        });
    }, []);

    const createItemRow = (item, index, padding = false) => {
        return (
            <Row
                style={{
                    backgroundColor: index && index % 2 === 1 ? "#F8FAFB" : "",
                    paddingLeft: padding ? 24 : 0,
                    visibility: labelWidth ? "visible" : "hidden"
                }}
            >
                {item.map(i => {
                    return (
                        <Col
                            key={`${i.dataIndex}_col`}
                            className={styles.card_more_group_content_row}
                            span={24 / columnNum}
                        >
                            {i.dot && <div className={styles.dot} style={{marginRight: 7}} />}
                            <div
                                key={`${i.dataIndex}_label`}
                                className="itemLabel"
                                style={{fontWeight: "bold", width: labelWidth || "auto"}}
                            >{`${getText(i.label ?? i.dataIndex) + (i.unit ? ` (${i.unit})` : "")}`}</div>
                            <div key={`${i.dataIndex}_value`} style={{marginLeft: 20}}>
                                {data[i.dataIndex] ?? NULL_VALUE}
                            </div>
                        </Col>
                    );
                })}
            </Row>
        );
    };

    let _columns = columns;
    let columnNum = 1;
    if (typeof columns === "function") {
        _columns = tableColumns;
    }
    if (_columns instanceof Array) {
        const firstColumn = _columns?.[0];
        if (firstColumn instanceof Array) {
            columnNum = firstColumn.length;
        } else if (firstColumn?.group) {
            columnNum = firstColumn.columns.length;
        }
    }

    useLayoutEffect(() => {
        setLabelWidth(0);

        const catchFunc = () => {
            setLabelWidth(140);
        };
        const startTime = Date.now();
        const timeout = 2000; // 2s
        const timer = setInterval(() => {
            try {
                const labelElements = Array.from(document.querySelectorAll(".itemLabel"));
                if (Date.now() - startTime > timeout) {
                    throw new Error("clac timeout!!!");
                }
                if (Array.isArray(labelElements) && labelElements.length) {
                    const maxWidth =
                        parseInt(
                            labelElements.reduce((max, labelElement) => {
                                const labelWidth = labelElement.offsetWidth;
                                return Math.max(max, labelWidth);
                            }, 0)
                        ) + 4;
                    if (typeof maxWidth === "number") {
                        setLabelWidth(maxWidth);
                        clearInterval(timer);
                    } else {
                        throw new Error("calc error, maxWidth is not a number!");
                    }
                }
            } catch (e) {
                catchFunc();
            }
        }, 16);
        return () => {
            clearInterval(timer);
        };
    }, [_columns]);

    return (
        <div ref={containerRef}>
            {_columns.map((item, index) => {
                if (item instanceof Array) {
                    return createItemRow(item, index);
                }
                if (item?.group) {
                    return (
                        <div className={index !== 0 ? styles.card_more_group : ""}>
                            <div className={styles.card_more_group_title}>{item.group}</div>
                            <div className={index !== _columns.length - 1 ? styles.card_more_group_content : ""}>
                                {item.columns.map((item, index) => createItemRow(item, index, true))}
                            </div>
                        </div>
                    );
                }
            })}
        </div>
    );
};

const openDisplayDialog = ({title, width, columns, getData}) => {
    middleModal({
        title,
        width: width ?? "60%",
        okText: "OK",
        cancelText: " ",
        cancelButtonProps: {style: {display: "none"}},
        content: <DisplayInfo columns={columns} getData={getData} />
    });
};

export default openDisplayDialog;
