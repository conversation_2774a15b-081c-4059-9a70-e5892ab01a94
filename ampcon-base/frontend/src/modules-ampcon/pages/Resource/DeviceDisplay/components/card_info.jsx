import React from "react";
import {Card, Flex, Tag} from "antd";
import Icon, {CheckCircleFilled, CloseCircleFilled} from "@ant-design/icons";
import {normalizeNumber} from "@/modules-ampcon/utils/util";
import {editCommonIcon} from "@/utils/common/iconSvg";
import styles from "./card.module.scss";
import {getText, <PERSON><PERSON>unceButton, NULL_VALUE} from "../utils";
import openCustomEditForm from "./custom_edit_form";
import {modifyDCSConfig} from "@/modules-ampcon/apis/fmt";

const paddingType = ["POWER", "FAN"];

const CardInfo = ({data, refresh}) => {
    const {ne_id, type, name, neData, nmuData, slotIndex, deviceType} = data ?? {};
    const createColumns = cardType => {
        const columns = [
            {dataIndex: "card-type", getData: () => cardType},
            {dataIndex: "slot-num", getData: rs => rs?.slotIndex},
            {dataIndex: "hardware-version", getData: rs => rs?.["Hardware version"]},
            // 仅在 cardType 为 EDFA 或 DEDFA 或 TDCM 时添加 temperature 配置项
            ...(["EDFA", "DEDFA", "DCM", "TDCM"].includes(cardType)
                ? [
                      {
                          dataIndex: "temperature",
                          unit: "°C",
                          getData: rs => normalizeNumber(rs?.["Module temperature"])
                      }
                  ]
                : [{dataIndex: ""}]),
            {dataIndex: "serial-no", label: "SN", getData: rs => rs?.["Serial number"]?.slice(0, -4)},
            {dataIndex: "mfg-name", label: "manufacture", getData: () => "FS"},
            {dataIndex: "firmware-version", getData: rs => rs?.["Software version"]},
            {dataIndex: ""},
            {dataIndex: "board-model", label: "PN", getData: rs => rs?.["Board model"] || cardType},
            {dataIndex: "mfg-date", label: "production-date", getData: rs => rs?.["Production date"]},
            {dataIndex: "software-version", getData: rs => rs?.["Software version"]}
        ];

        return columns;
    };
    const SHOW_INFO_CONFIG = {
        chassis: {
            title: "Chassis Info",
            columns: [
                {dataIndex: "chassis-type", getData: () => "CHASSIS"},
                {dataIndex: "slot-num", getData: rs => rs?.["Slot number"]},
                {dataIndex: "hardware-version", getData: rs => rs?.["Hardware version"]},
                {dataIndex: ""},
                {dataIndex: "serial-no", label: "SN", getData: rs => rs?.SN?.slice(0, -4)},
                {dataIndex: "mfg-name", label: "manufacture", getData: () => "FS"},
                {dataIndex: "firmware-version", getData: rs => rs?.["Software version"]},
                {dataIndex: ""},
                {dataIndex: "part-no", label: "PN", getData: () => `${deviceType}-CH2U`},
                {dataIndex: "mfg-date", label: "production-date", getData: rs => rs?.["Production date"]},
                {dataIndex: "software-version", getData: rs => rs?.["Software version"]}
            ]
        },
        EDFA: {columns: createColumns("EDFA")},
        DEDFA: {columns: createColumns("DEDFA")},
        OLP: {columns: createColumns("OLP")},
        DCM: {columns: createColumns("DCM")},
        TDCM: {columns: createColumns("TDCM")},
        OEO: {columns: createColumns("OEO")},
        VOA: {columns: createColumns("VOA")},
        OPD: {columns: createColumns("OPD")},
        "4T4": {columns: createColumns("4T4")},
        "4M4": {columns: createColumns("4M4")},
        POWER: {
            title: "PSU Info",
            columns: [
                {dataIndex: "card-type", getData: () => "PSU"},
                {dataIndex: "input-current", unit: "A", getData: rs => rs?.businessInfo.query.input_current},
                {dataIndex: "hardware-version", getData: rs => rs?.["Hardware version"]},
                {
                    dataIndex: "power-switch-status",
                    getData: rs => (
                        <Tag
                            className={
                                rs?.businessInfo.config.power_switch === "1" ? styles.normalTag : styles.abnormalTag
                            }
                        >
                            {rs?.businessInfo.config.power_switch === "1" ? "Open" : "Off"}
                        </Tag>
                    )
                },
                {dataIndex: "slot-num", getData: rs => rs?.slotIndex},
                {dataIndex: "output-current", unit: "A", getData: rs => rs?.businessInfo.query.output_current},
                {dataIndex: "firmware-version", getData: rs => rs?.["Software version"]},
                {
                    dataIndex: "power-operation-status",
                    getData: rs => (
                        <Tag
                            className={
                                rs?.businessInfo.query.power_state === "1" ? styles.normalTag : styles.abnormalTag
                            }
                        >
                            {rs?.businessInfo.query.power_state === "1" ? "Normal" : "Abnormal"}
                        </Tag>
                    )
                },
                {dataIndex: "serial-no", label: "SN", getData: rs => rs?.["Serial number"]?.slice(0, -4)},
                {dataIndex: "input-voltage", unit: "V", getData: rs => rs?.businessInfo.query.input_voltage},
                {dataIndex: "software-version", getData: rs => rs?.["Software version"]},
                {dataIndex: "mfg-date", label: "production-date", getData: rs => rs?.["Production date"]},
                {dataIndex: "part-no", label: "PN", getData: () => `${deviceType}-PSU`},
                {dataIndex: "output-voltage", unit: "V", getData: rs => rs?.businessInfo.query.output_voltage},
                {dataIndex: "temperature", unit: "°C", getData: rs => normalizeNumber(rs?.["Module temperature"])},
                {dataIndex: ""}
            ]
        },
        FAN: {
            title: "FAN Info",
            columns: [
                {dataIndex: "card-type", getData: () => "FAN"},
                {dataIndex: "slot-num", getData: rs => rs?.slotIndex},
                {dataIndex: "hardware-version", getData: rs => rs?.["Hardware version"]},
                {
                    dataIndex: "fan-state",
                    getData: rs => (
                        <Tag className={rs?.businessInfo.query.rpm ? styles.normalTag : styles.abnormalTag}>
                            {rs?.businessInfo.query.rpm ? "Normal" : "Abnormal"}
                        </Tag>
                    )
                },
                {dataIndex: "serial-no", label: "SN", getData: rs => rs?.["Serial number"]?.slice(0, -4)},
                {dataIndex: "mfg-date", label: "production-date", getData: rs => rs?.["Production date"]},
                {dataIndex: "firmware-version", getData: rs => rs?.["Software version"]},
                {dataIndex: "fan-mode", getData: rs => (rs?.businessInfo.config.work_mode === "1" ? "AUTO" : "MANUAL")},
                {dataIndex: "part-no", label: "PN", getData: () => `${deviceType}-FAN`},
                {dataIndex: "temperature", unit: "°C", getData: rs => normalizeNumber(rs?.["Module temperature"])},
                {dataIndex: "software-version", getData: rs => rs?.["Software version"]},
                {dataIndex: "fan-speed", getData: rs => rs?.businessInfo.config.work_speed}
            ],
            editConfig: {
                width: 600,
                columns: [
                    [
                        {
                            dataIndex: "fan-mode",
                            inputType: "select",
                            data: {options: ["AUTO", "MANUAL"]}
                        }
                    ],
                    [
                        {
                            dataIndex: "fan-speed",
                            inputType: "select",
                            data: {options: ["HIGH", "MIDDLE", "LOW"]}
                        }
                    ]
                ],
                getData: async () => {
                    return {
                        "fan-mode": neData?.businessInfo?.config?.["work-mode"] ?? NULL_VALUE
                    };
                },
                setDataAPI: () => {
                    const ip = ne_id.split(":")[0];
                    return {
                        APIName: type === "DCS" ? modifyDCSConfig : () => {},
                        APIParameter: () => {
                            return {
                                ip,
                                slotIndex
                            };
                        }
                    };
                }
            }
        },
        NMU: {columns: createColumns("NMU")}
    };
    const showInfoConfig = SHOW_INFO_CONFIG[type] ?? {columns: []};

    let state = "Active";
    if (!neData) {
        state = "Absent";
    } else {
        state = "Present";
    }

    return (
        <Card
            style={{
                height: paddingType.includes(type) ? "100%" : "224px",
                borderRadius: "8px"
            }}
            loading={!type}
            title={showInfoConfig.title ?? "Card Info"}
            className={styles.card}
            extra={
                <Tag
                    className={["Absent", "Mismatch"].includes(state) ? styles.card_tagColor : styles.card_tag}
                    icon={["Absent", "Mismatch"].includes(state) ? <CloseCircleFilled /> : <CheckCircleFilled />}
                >
                    {type === "chassis" && neData ? "Active" : state}
                </Tag>
            }
        >
            <div style={{display: "flex"}}>
                <Flex wrap="wrap" style={{flex: 1}}>
                    {showInfoConfig.columns.map(i => {
                        const _v =
                            i?.getData?.(type === "chassis" ? nmuData : neData) ||
                            (type === "chassis" ? nmuData : neData)?.[i.dataIndex] ||
                            NULL_VALUE;
                        return i.dataIndex ? (
                            <div key={i.dataIndex} className={styles.item}>
                                <div className={styles.dot} />
                                <div
                                    className={styles.item_label}
                                >{`${getText(i.label ?? i.dataIndex) + (i.unit ? ` (${i.unit})` : "")}`}</div>
                                <span style={{padding: "0 8px 0 2px"}}>:</span>
                                <div className={styles.item_value} title={typeof _v === "string" ? _v : ""}>
                                    {_v}
                                </div>
                            </div>
                        ) : (
                            <div className={styles.item} />
                        );
                    })}
                </Flex>
                {showInfoConfig.editConfig && (
                    <DebounceButton
                        containerType="Icon"
                        title="Edit"
                        component={editCommonIcon}
                        style={{cursor: "pointer", width: 28, alignSelf: "flex-start", paddingTop: 10}}
                        onClick={() => {
                            const columnNum = type === "fan" ? 1 : 2;
                            openCustomEditForm({
                                title: "Modify",
                                width: showInfoConfig.editConfig?.width ?? 900,
                                columns: showInfoConfig.editConfig?.columns ?? [],
                                getData: showInfoConfig.editConfig?.getData ?? {},
                                setDataAPI: showInfoConfig.editConfig?.setDataAPI,
                                columnNum
                            });
                        }}
                    />
                )}
            </div>
        </Card>
    );
};

export default CardInfo;
