export const SIDES = ["front", "rear"];

const CARDWIDTH = 393;
const INIT = 180;

export const LED_STATUS = Object.freeze({
    GREEN: 1,
    FLASHING: 2,
    RED: 3
});

const DCS_CHASSIS_CONFIG = {
    1: {
        side: SIDES[0],
        left: INIT,
        top: 0
    },
    2: {
        side: SIDES[0],
        left: INIT + CARDWIDTH,
        top: 0
    },
    3: {
        side: SIDES[0],
        left: INIT,
        top: 0
    },
    4: {
        side: SIDES[0],
        left: INIT + CARDWIDTH,
        top: 0
    },
    5: {
        side: SIDES[1],
        left: 759,
        top: 5
    },
    6: {
        side: SIDES[1],
        left: 925,
        top: 5
    },
    7: {
        side: SIDES[1],
        left: 1093,
        top: 5
    },
    8: {
        side: SIDES[1],
        left: 1260,
        top: 5
    },
    9: {
        side: SIDES[1],
        left: 78,
        top: 5
    },
    10: {
        side: SIDES[1],
        left: 420,
        top: 5
    },
    16: {
        side: SIDES[1],
        left: 1428,
        top: 5
    }
};

const DCS_NMU_CONFIG = {
    "LED-PWR1": {
        top: 71,
        left: 40,
        status: {
            key: "businessInfo.query.power_state1",
            judge: data => (data === "1" ? LED_STATUS.GREEN : null)
        }
    },
    "LED-PWR2": {
        top: 90,
        left: 40,
        status: {
            key: "businessInfo.query.power_state2",
            judge: data => (data === "1" ? LED_STATUS.GREEN : null)
        }
    }
};

const DCS_4M4_CONFIG = {
    "LED-SYS": {
        top: 81,
        left: 11,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-ALARM": {
        top: 122,
        left: 12
    },
    "LED-L1": {
        top: 38,
        left: 208,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-C1": {
        top: 152,
        left: 73,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-C2": {
        top: 152,
        left: 158,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-C3": {
        top: 152,
        left: 243,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-C4": {
        top: 152,
        left: 327,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "PORT-L1": {
        top: 49,
        left: 174,
        status: {
            key: "businessInfo.query",
            judge: data => data
        }
    },
    "PORT-C1": {
        top: 103,
        left: 39,
        status: {
            key: "businessInfo.query",
            judge: data => data
        }
    },
    "PORT-C2": {
        top: 103,
        left: 124,
        status: {
            key: "businessInfo.query",
            judge: data => data
        }
    },
    "PORT-C3": {
        top: 103,
        left: 208,
        status: {
            key: "businessInfo.query",
            judge: data => data
        }
    },
    "PORT-C4": {
        top: 103,
        left: 292,
        status: {
            key: "businessInfo.query",
            judge: data => data
        }
    }
};

const DCS_4T4_CONFIG = {
    "LED-SYS": {
        top: 81,
        left: 11,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-ALARM": {
        top: 123,
        left: 11
    },
    "LED-L1": {
        top: 38,
        left: 74,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-L2": {
        top: 38,
        left: 158,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-L3": {
        top: 38,
        left: 241,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-L4": {
        top: 38,
        left: 324,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-C1": {
        top: 152,
        left: 74,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-C2": {
        top: 152,
        left: 158,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-C3": {
        top: 152,
        left: 241,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-C4": {
        top: 152,
        left: 324,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "PORT-L1": {
        top: 49,
        left: 40,
        status: {
            key: "businessInfo.query",
            judge: data => data
        }
    },
    "PORT-L2": {
        top: 49,
        left: 123,
        status: {
            key: "businessInfo.query",
            judge: data => data
        }
    },
    "PORT-L3": {
        top: 49,
        left: 207,
        status: {
            key: "businessInfo.query",
            judge: data => data
        }
    },
    "PORT-L4": {
        top: 49,
        left: 290,
        status: {
            key: "businessInfo.query",
            judge: data => data
        }
    },
    "PORT-C1": {
        top: 103,
        left: 40,
        status: {
            key: "businessInfo.query",
            judge: data => data
        }
    },
    "PORT-C2": {
        top: 103,
        left: 123,
        status: {
            key: "businessInfo.query",
            judge: data => data
        }
    },
    "PORT-C3": {
        top: 103,
        left: 207,
        status: {
            key: "businessInfo.query",
            judge: data => data
        }
    },
    "PORT-C4": {
        top: 103,
        left: 290,
        status: {
            key: "businessInfo.query",
            judge: data => data
        }
    }
};

const DCS_POWER_CONFIG = {
    "LED-SYS": {
        top: 17,
        left: 294,
        status: {
            key: "businessInfo.query.power_state",
            judge: data => (data === "1" ? LED_STATUS.GREEN : null)
        }
    },
    "LED-ALARM": {
        top: 124,
        left: 11
    }
};

const DCS_FAN_CONFIG = {
    "LED-SYS": {
        top: 26,
        left: 99,
        status: {
            key: "businessInfo.query",
            judge: data => (data ? LED_STATUS.GREEN : null)
        }
    },
    "LED-ALARM": {
        top: 124,
        left: 11
    }
};

export const chassisConfig = {
    DCS: DCS_CHASSIS_CONFIG,
    NMU: DCS_NMU_CONFIG,
    "4M4": DCS_4M4_CONFIG,
    "4T4": DCS_4T4_CONFIG,
    POWER: DCS_POWER_CONFIG,
    FAN: DCS_FAN_CONFIG
};
