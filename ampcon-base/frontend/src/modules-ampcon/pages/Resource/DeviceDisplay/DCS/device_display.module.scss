.wrap {
    width: 100%;
    height: 100%;
    position: relative;
}

.loading {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.deviceDisplay {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f0f2f5;
  overflow: auto;
  gap: 18px 24px;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: repeat(3, 1fr);
  > div {
        &:nth-child(1) {
            grid-column: 1 / 13;
        }
        &:nth-child(2) {
            grid-column: 1 / 13;
        }
        &:nth-child(3) {
            grid-column: 1 / 13;
        }
    }
}

.viewDisplay {
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 0 0 8px 8px;
  padding-top: 10px;
  padding-bottom: 24px;
}

.customRadioGroup {
  :global {
      .ant-radio-button-wrapper{
          border-radius: 0px;
      }
      .ant-radio-button-wrapper-checked {
          color: var(--primary-color) !important;
          background: #E7F9F8;
          border: 1px solid var(--primary-color) !important;
      }

      .ant-radio-button-wrapper {
          width: 100px;
          font-weight: 400;
          font-size: 14px;
          text-align: center;
          color: #B3BBC8;
          border: 1px solid #DCDCDC;
      }
      //.ant-radio-button-wrapper:first-child{
      //    border-radius: 2px 0px 0px 2px;
      //}
      //.ant-radio-button-wrapper:nth-child(2) {
      //    margin-left:-1px;
      //}
      //.ant-radio-button-wrapper:last-child{
      //    margin-left:-1px;
      //    border-radius: 0px 2px 2px 0px;
      //}

      .ant-radio-button-wrapper:not(.ant-radio-button-wrapper-checked):hover {
          color: var(--primary-color) !important;
          //background: #E7F9F8;
          //border: 1px solid var(--primary-color) !important;
      }
  }
}

.chassisFront {
  width: 1850px;
  height: 200px;
  background-image: url("./img/front_view.svg");
  background-size: 100% 100%;
  position: relative;
}

.chassisRear {
  width: 1850px;
  height: 200px;
  background-image: url("./img/rear_view.svg");
  background-size: 100% 100%;
  position: relative;
}

.CARD_common {
  background-size: 100% 100%;
  position: absolute;
}

@keyframes led-flashing {
    10% {
        opacity: 0;
    }
    90% {
        opacity: 1;
    }
}

.LED_COMMON {
    box-shadow: none;
    width: 8px;
    height: 8px;
    border-radius: 4px;
    position: absolute;
    //background-color: #2bbf2b;
}

.LED_ACTIVE {
    background-color: #2bbf2b;
}

.LED_CRITICAL {
    background-color: red;
}

.LED_FLASHING {
    background-color: #2bbf2b;
    animation: led-flashing 1s infinite;
}

.CARD_4M4 {
  width: 390px;
  height: 199px;
  background-image: url("./img/4M4.svg");
}

.CARD_4T4 {
  width: 390px;
  height: 199px;
  background-image: url("./img/4T4.svg");
}

.CARD_NMU {
  width: 342px;
  height: 186px;
  background-image: url("./img/NMU.svg");
}

.CARD_POWER {
  width: 320px;
  height: 186px;
  background-image: url("./img/power.svg");
}

.CARD_FAN {
  width: 147px;
  height: 186px;
  background-image: url("./img/fan.svg");
}

.PORT2 {
  width: 77px;
  height: 45px;
  background-image: url("./img/port_2.png");
  background-size: 100% 100%;
  position: absolute;
  //transform: rotate(-90deg);
}
