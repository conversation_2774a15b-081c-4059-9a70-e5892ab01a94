import {<PERSON><PERSON>, <PERSON>, Flex, message, Radio, Spin, Typography} from "antd";
import React, {forwardRef, useDeferredValue, useEffect, useRef, useState} from "react";
import {debounce} from "lodash";
import {useDispatch, useSelector} from "react-redux";
import {useRequest} from "ahooks";
import {chassisConfig, SIDES, LED_STATUS} from "./device_display_config";
import styles from "./device_display.module.scss";
import {normalizeNumber} from "@/modules-ampcon/utils/util";
import CardInfo from "../components/card_info";
import PortList from "../components/port_list";
import {getDCSInfo, queryDCSInfo} from "@/modules-ampcon/apis/fmt";
import {setSelectedItem, setTableFilter} from "@/store/modules/otn/mapSlice";
import {NULL_VALUE} from "../utils";

const {Text} = Typography;

const LED_CLASS_MAP = {
    [LED_STATUS.GREEN]: styles.LED_ACTIVE,
    [LED_STATUS.RED]: styles.LED_CRITICAL,
    [LED_STATUS.FLASHING]: styles.LED_FLASHING
};
const portListHide = ["POWER", "FAN"];

const DeviceDisplay = () => {
    const {runAsync} = useRequest(getDCSInfo, {manual: true});
    const {tableFilter} = useSelector(state => state.map);
    // const deferredTableFilter = useDeferredValue(tableFilter);
    const {selectedItem, onSelectItem} = useSelector(state => state.map);
    const selectedItemRef = useRef(selectedItem);
    // 0:面板 1: 背板 2: mux
    const [showView, setShowView] = useState(0);
    const [data, setData] = useState();
    const [cardData, setCardData] = useState();
    const [portData, setPortData] = useState();
    const [loading, setLoading] = useState(false);
    const chassisRef = useRef();
    const chassisWrapRef = useRef();
    const containerRef = useRef();

    const syncData = async ip => {
        if (ip) {
            setLoading(true);
            try {
                await queryDCSInfo(ip).then(res => {
                    if (res.errorCode === 0) {
                        message.success(`${ip} synchronize succeeded`);
                        const _data = transformData(clonePowerObject(res.data));
                        const _cardData = transformCardData(clonePowerObject(res.data));
                        const _portData = transformPortData(clonePowerObject(res.data));
                        let slotIndex = "0";
                        let type = "chassis";
                        if (tableFilter.resource) {
                            slotIndex = tableFilter.resource.value;
                            type = tableFilter.resource.type;
                        }
                        setData(_data);
                        setCardData({
                            ne_id: tableFilter.id,
                            type,
                            neData: _cardData[slotIndex],
                            nmuData: _cardData[0],
                            slotIndex,
                            deviceType: "DCS"
                        });
                        setPortData({
                            ne_id: tableFilter.id,
                            type,
                            neData: _portData[slotIndex],
                            slotIndex,
                            deviceType: "DCS"
                        });
                    } else {
                        message.success(`${ip} synchronize failed`);
                    }
                });
            } catch (error) {
                message.success(`${ip} synchronize failed`);
                console.log(error);
            } finally {
                setLoading(false);
            }
        }
    };

    const sync = () => {
        syncData(selectedItem?.value?.host).then();
    };

    useEffect(() => {
        updateData().then();
        if (selectedItemRef.current.id !== selectedItem.id) {
            setShowView(0);
        }
    }, [selectedItem]);

    useEffect(() => {
        selectedItemRef.current = selectedItem;
    }, [selectedItem]);

    const transformCardData = data => {
        const result = [];
        data?.boardInfos?.forEach(board => {
            const {slotIndex} = board;
            result[slotIndex] = board;
        });
        if (result[0]) {
            result[0] = {
                ...result[0],
                "Slot number": data["Slot number"],
                SN: data.SN,
                Model: data.Model,
                "Production date": data["Production date"],
                "Software version": data["Software version"],
                "Hardware version": data["Hardware version"],
                IP: data.IP
            };
        }
        return result;
    };

    const transformPortData = data => {
        const result = {
            1: [],
            2: [],
            3: [],
            4: [],
            0: []
        };

        data?.boardInfos?.forEach(board => {
            const {slotIndex} = board;
            let portData = [];
            portData = Object.values(board.ports_data || {}).map(item => {
                const newItem = {};
                Object.keys(item).forEach(key => {
                    const newKey = key.toLowerCase().replace(/[ _]/g, "-");
                    if (newKey === "slot-no" || newKey === "no") {
                        newItem[newKey] = item[key];
                    } else {
                        newItem[newKey] = normalizeNumber(item[key]) || NULL_VALUE;
                    }
                });
                return newItem;
            });
            result[slotIndex] = portData;
        });
        return result;
    };

    const clonePowerObject = originalData => {
        const boardInfos = JSON.parse(JSON.stringify(originalData.boardInfos));
        const nmuObject = boardInfos.find(item => item.slotIndex === 16);
        if (!nmuObject) return originalData;

        const power1 = {
            ...nmuObject,
            slotIndex: 9,
            boardType: "POWER",
            businessInfo: {
                query: {
                    power_state: nmuObject.businessInfo.query.power_state1,
                    supply_mode: nmuObject.businessInfo.query.supply_mode1,
                    max_power: nmuObject.businessInfo.query.max_power1,
                    input_current: nmuObject.businessInfo.query.input_current1,
                    output_current: nmuObject.businessInfo.query.output_current1,
                    input_voltage: nmuObject.businessInfo.query.input_voltage1,
                    output_voltage: nmuObject.businessInfo.query.output_voltage1,
                    power_temperature: nmuObject.businessInfo.query.power_temperature1
                },
                config: {
                    power_switch: nmuObject.businessInfo.config.power_switch1,
                    fan_switch: nmuObject.businessInfo.config.fan_switch,
                    fan_speed: nmuObject.businessInfo.config.fan_speed
                }
            }
        };

        const power2 = {
            ...nmuObject,
            slotIndex: 10,
            boardType: "POWER",
            businessInfo: {
                query: {
                    power_state: nmuObject.businessInfo.query.power_state2,
                    supply_mode: nmuObject.businessInfo.query.supply_mode2,
                    max_power: nmuObject.businessInfo.query.max_power2,
                    input_current: nmuObject.businessInfo.query.input_current2,
                    output_current: nmuObject.businessInfo.query.output_current2,
                    input_voltage: nmuObject.businessInfo.query.input_voltage2,
                    output_voltage: nmuObject.businessInfo.query.output_voltage2,
                    power_temperature: nmuObject.businessInfo.query.power_temperature2
                },
                config: {
                    power_switch: nmuObject.businessInfo.config.power_switch2,
                    fan_switch: nmuObject.businessInfo.config.fan_switch,
                    fan_speed: nmuObject.businessInfo.config.fan_speed
                }
            }
        };

        boardInfos.push(power1);
        boardInfos.push(power2);

        return {
            ...originalData,
            boardInfos
        };
    };

    const transformData = data => {
        return (
            SIDES.map(side => {
                const children = data?.boardInfos
                    ?.filter(card => side === chassisConfig.DCS[card.slotIndex]?.side)
                    .map(card => {
                        const type = card.boardType;
                        const {side, ...style} = chassisConfig.DCS[card.slotIndex];
                        const className = `${styles.CARD_common}  ${styles[`CARD_${card.boardType}`]}`;
                        const title = `Name: ${card.boardType}-${card.slotIndex}`;
                        const getValueByPath = path => {
                            return path
                                ? path.split(".").reduce((acc, key) => {
                                      // 如果 key 包含数组索引
                                      const match = key.match(/^(\w+)\[(\d+)]$/);
                                      if (match) {
                                          const [, prop, index] = match;
                                          return acc?.[prop]?.[parseInt(index)];
                                      }
                                      return acc?.[key];
                                  }, card)
                                : null;
                        };
                        const cardItems = Object.entries(chassisConfig[card.boardType] || {}).map(
                            ([key, {top, left, transform, status}]) => {
                                let className = key.startsWith("LED") ? styles.LED_COMMON : "";
                                if (key.startsWith("LED") && status?.key) {
                                    const keyValue = getValueByPath(status.key);

                                    if (status?.judge) {
                                        if (status.threshold) {
                                            const threshold = getValueByPath(status.threshold);
                                            if (status.threshold2) {
                                                const threshold2 = getValueByPath(status.threshold2);
                                                className += ` ${LED_CLASS_MAP[status.judge(keyValue, threshold, threshold2)] || ""}`;
                                            } else {
                                                className += ` ${LED_CLASS_MAP[status.judge(keyValue, threshold)] || ""}`;
                                            }
                                        } else {
                                            className += ` ${LED_CLASS_MAP[status.judge(keyValue)] || ""}`;
                                        }
                                    }
                                } else if (key.startsWith("PORT")) {
                                    if (status?.key) {
                                        const keyValue = getValueByPath(status.key);
                                        if (status?.judge(keyValue)) {
                                            className = styles.PORT2;
                                        }
                                    }
                                }
                                const type = key.startsWith("LED") ? "led" : "port";
                                const style = {top, left, transform};
                                const title = `Name: ${key}`;
                                return {
                                    title,
                                    type,
                                    className,
                                    style
                                };
                            }
                        );
                        return {
                            slotIndex: card?.slotIndex,
                            title,
                            type,
                            className,
                            style,
                            cardItems
                        };
                    });
                return {
                    className: `${styles[`chassis${side.charAt(0).toUpperCase() + side.slice(1)}`]}`,
                    children,
                    nodeType: "chassis",
                    type: "chassis"
                };
            }) ?? []
        );
    };

    const updateData = async () => {
        const neIP = selectedItem?.value?.host;
        if (!neIP) return;
        const neData = (await runAsync(neIP)).data;
        const _data = transformData(clonePowerObject(neData));
        const _cardData = transformCardData(clonePowerObject(neData));
        const _portData = transformPortData(clonePowerObject(neData));
        let slotIndex = "0";
        let type = "chassis";
        if (tableFilter.resource) {
            slotIndex = tableFilter.resource.value;
            type = tableFilter.resource.type;
        }
        setData(_data);
        setCardData({
            ne_id: tableFilter.id,
            type,
            neData: _cardData[slotIndex],
            nmuData: _cardData[0],
            slotIndex,
            deviceType: "DCS"
        });
        setPortData({
            ne_id: tableFilter.id,
            type,
            neData: _portData[slotIndex],
            slotIndex,
            deviceType: "DCS"
        });
    };

    useEffect(() => {
        if (chassisWrapRef.current && chassisRef.current) {
            chassisRef.current.style = `zoom: ${(chassisWrapRef?.current.clientWidth || 0) / 1856}`;
        }

        const observer = new ResizeObserver(
            debounce(() => {
                if (chassisRef.current)
                    chassisRef.current.style = `zoom: ${(chassisWrapRef?.current.clientWidth || 0) / 1856}`;
            }, 5)
        );
        observer.observe(containerRef.current);

        return () => {
            observer.disconnect();
        };
    }, []);
    const bt = ["Front", "Rear"];

    return (
        <div className={styles.wrap}>
            {loading && (
                <div className={styles.loading}>
                    <Spin />
                </div>
            )}
            <div className={styles.deviceDisplay} style={{flex: 1}}>
                <div className={styles.viewDisplay}>
                    <Card style={{margin: "0 24px"}} ref={containerRef}>
                        <Flex vertical gap="middle" ref={chassisWrapRef}>
                            <Flex justify="space-between" style={{marginTop: "12px"}}>
                                <Radio.Group
                                    value={showView}
                                    onChange={e => {
                                        setShowView(e.target.value);
                                    }}
                                    className={styles.customRadioGroup}
                                >
                                    {bt.map((item, i) => {
                                        return <Radio.Button value={i}>{item}</Radio.Button>;
                                    })}
                                </Radio.Group>
                                <div>
                                    <Button onClick={sync} style={{marginLeft: "10px"}}>
                                        Synchronize
                                    </Button>
                                </div>
                            </Flex>
                            <ViewCard showView={showView} selectedItem={selectedItem} data={data} ref={chassisRef} />
                        </Flex>
                    </Card>
                </div>
                {portListHide.includes(cardData?.type) ? (
                    <>
                        <CardInfo
                            data={{
                                ...cardData,
                                type: "chassis"
                            }}
                        />
                        <CardInfo data={cardData} />
                    </>
                ) : (
                    <>
                        <CardInfo data={cardData} />
                        {!portListHide.includes(portData?.type) && <PortList data={portData} />}
                    </>
                )}
            </div>
        </div>
    );
};

const ViewCard = forwardRef(({showView, selectedItem, data}, ref) => {
    const dispatch = useDispatch();
    const {name, className, children, nodeType, type} = data?.[showView] ?? {};
    // 指向选中的模块元素
    const selectedPortRef = useRef({style: {}});
    let isDBClick;

    const setSelectPort = selectPort => {
        if (selectPort) {
            dispatch(
                setSelectedItem({
                    ...selectedItem,
                    selectPort: {type: selectPort.type, value: selectPort.value},
                    resource: {type: selectPort.type, value: selectPort.value}
                })
            );
            dispatch(
                setTableFilter({
                    type: "NODE_NE",
                    id: selectedItem.value.ne_id,
                    resource: {type: selectPort.type, value: selectPort.value}
                })
            );
        } else {
            const newSelect = {...selectedItem};
            delete newSelect.selectPort;
            dispatch(setSelectedItem(newSelect));
            dispatch(setTableFilter({type: "NODE_NE", id: selectedItem.value.ne_id}));
        }
    };

    // 机框图模块MouseMove
    const handleModuleMouseMove = e => {
        e.stopPropagation();
        e.target.style.boxShadow = "inset 0px 0px 80px 4px rgb(0 255 0 / 30%)";
    };
    // 机框图模块MouseOut
    const handleModuleMouseOut = e => {
        e.stopPropagation();

        if (e.target.title === selectedPortRef.current?.title) {
            return;
        }
        e.target.style.boxShadow = "";
    };

    // 机框图模块Click
    const handleModuleClick = e => {
        isDBClick = false;
        const {dataset} = e.currentTarget;
        e.stopPropagation();
        if (e.button === 0) {
            // console.log("Left button clicked");
            // setOpenDropdown(false);
        }
        if (!dataset.type) {
            return;
        }

        setTimeout(() => {
            if (!isDBClick) {
                if (e.target.title === selectedPortRef.current?.title) {
                    selectedPortRef.current.style.boxShadow = "";
                    selectedPortRef.current = {style: {}};
                    return;
                }
                e.target.style.boxShadow = "inset 0px 0px 80px 4px rgb(0 255 0 / 30%)";
                selectedPortRef.current.style.boxShadow = "";
                selectedPortRef.current = e.target;
                setSelectPort(dataset);
            }
        }, 300);
    };

    return (
        <Flex vertical align="center" justify="center" gap="24px">
            <div
                ref={ref}
                className={className}
                onMouseMove={handleModuleMouseMove}
                onMouseOut={handleModuleMouseOut}
                onClick={handleModuleClick}
                onBlur={() => {}}
                data-type="chassis"
                data-value="0"
                title="CHASSIS"
            >
                {children?.map(({title, type, style, className, cardItems, slotIndex, nmuItems}) => {
                    if (slotIndex !== 0) {
                        return (
                            <div
                                key={slotIndex}
                                style={style}
                                className={className}
                                onClick={handleModuleClick}
                                data-type={type}
                                data-value={slotIndex}
                                title={title}
                            >
                                {cardItems?.map(({title, type, className, style}, index) => (
                                    <div
                                        key={index}
                                        className={className}
                                        style={style}
                                        data-type={type}
                                        data-value={slotIndex}
                                        title={title}
                                    />
                                ))}
                            </div>
                        );
                    }
                    // return nmuItems?.map(({title, style, className}, index) => (
                    //     <div key={index} style={style} className={className} />
                    // ));
                })}
            </div>
            <Text style={{paddingBottom: "20px"}}>{data?.length > 0 ? `DCS (${selectedItem?.value?.host})` : ""}</Text>
        </Flex>
    );
});

export default DeviceDisplay;
