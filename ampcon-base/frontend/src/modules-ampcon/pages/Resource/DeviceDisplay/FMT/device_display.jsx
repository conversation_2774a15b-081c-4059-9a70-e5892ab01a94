import {<PERSON><PERSON>, <PERSON>, Flex, message, Radio, Spin, Typography} from "antd";
import React, {forwardRef, useDeferredValue, useEffect, useRef, useState} from "react";
import {debounce} from "lodash";
import {useDispatch, useSelector} from "react-redux";
import {useRequest} from "ahooks";
import {chassisConfig, LED_STATUS} from "./device_display_config";
import styles from "./device_display.module.scss";
import {normalizeNumber} from "@/modules-ampcon/utils/util";
import CardInfo from "../components/card_info";
import PortList from "../components/port_list";
import {getFMTInfo, queryFMTInfo} from "@/modules-ampcon/apis/fmt";
import {setSelectedItem, setTableFilter} from "@/store/modules/otn/mapSlice";
import {NULL_VALUE} from "../utils";

const {Text} = Typography;

const LED_CLASS_MAP = {
    [LED_STATUS.GREEN]: styles.LED_ACTIVE,
    [LED_STATUS.RED]: styles.LED_CRITICAL,
    [LED_STATUS.FLASHING]: styles.LED_FLASHING
};

const DeviceDisplay = () => {
    const {runAsync} = useRequest(getFMTInfo, {manual: true});
    const {tableFilter} = useSelector(state => state.map);
    // const deferredTableFilter = useDeferredValue(tableFilter);
    const {selectedItem, onSelectItem} = useSelector(state => state.map);
    const selectedItemRef = useRef(selectedItem);
    const [viewSelect, setViewSelect] = useState("Front");
    const [data, setData] = useState();
    const [cardData, setCardData] = useState();
    const [portData, setPortData] = useState();
    const [loading, setLoading] = useState(false);
    const chassisRef = useRef();
    const chassisWrapRef = useRef();
    const containerRef = useRef();

    const viewRadioOptions = [
        {label: "Front", value: "Front"},
        {label: "Rear", value: "Rear"}
    ];
    const onChangeViewSelect = ({target: {value}}) => {
        setViewSelect(value);
    };

    const syncData = async ip => {
        if (ip) {
            setLoading(true);
            try {
                await queryFMTInfo(ip).then(res => {
                    if (res.errorCode === 0) {
                        message.success(`${ip} synchronize succeeded`);
                        const _data = transformData(res.data);
                        const _cardData = transformCardData(res.data);
                        const _portData = transformPortData(res.data);
                        let slotIndex = "0";
                        let type = "chassis";
                        if (tableFilter.resource) {
                            slotIndex = tableFilter.resource.value;
                            type = tableFilter.resource.type;
                        }
                        setData(_data);
                        setCardData({
                            ne_id: tableFilter.id,
                            type,
                            neData: _cardData[slotIndex],
                            slotIndex,
                            deviceType: "FMT"
                        });
                        setPortData({
                            ne_id: tableFilter.id,
                            type,
                            neData: _portData[slotIndex],
                            slotIndex,
                            deviceType: "FMT"
                        });
                    } else {
                        message.success(`${ip} synchronize failed`);
                    }
                });
            } catch (error) {
                message.success(`${ip} synchronize failed`);
                console.log(error);
            } finally {
                setLoading(false);
            }
        }
    };

    const sync = () => {
        syncData(selectedItem?.value?.host).then();
    };

    useEffect(() => {
        updateData().then();
        if (selectedItemRef.current.id !== selectedItem.id) {
            setViewSelect("Front");
        }
    }, [selectedItem]);

    useEffect(() => {
        selectedItemRef.current = selectedItem;
    }, [selectedItem]);

    const transformCardData = data => {
        const result = [];
        data?.boardInfos?.forEach(board => {
            const {slotIndex} = board;
            result[slotIndex] = board;
        });
        if (result[0]) {
            result[0] = {
                ...result[0],
                "Slot number": data["Slot number"],
                SN: data.SN,
                Model: data.Model,
                "Production date": data["Production date"],
                "Software version": data["Software version"],
                "Hardware version": data["Hardware version"],
                IP: data.IP
            };
        }
        return result;
    };

    const transformPortData = data => {
        const result = {
            1: [],
            2: [],
            3: [],
            4: [],
            0: []
        };

        data?.boardInfos?.forEach(board => {
            const {slotIndex} = board;
            let portData = [];
            if (["DCM", "TDCM"].includes(board.boardType)) {
                portData.push({
                    card: board.boardType,
                    "module-state": Number(board?.businessInfo?.config?.module_state),
                    "module-temperature": board?.["Module temperature"],
                    "tdc-setting-value": normalizeNumber(board?.businessInfo?.config?.tdc_setting_value),
                    "tdc-value": normalizeNumber(board?.businessInfo?.config?.tdc_value),
                    "frequency-interval": board?.businessInfo?.config?.frequency_inter
                }); // DCM没有端口信息
            } else {
                portData = Object.values(board.ports_data || {}).map(item => {
                    const newItem = {};
                    Object.keys(item).forEach(key => {
                        const newKey = key.toLowerCase().replace(/ /g, "-");
                        if (newKey === "slot-no" || newKey === "no") {
                            newItem[newKey] = item[key];
                        } else {
                            newItem[newKey] = normalizeNumber(item[key]) || NULL_VALUE;
                        }
                    });
                    return newItem;
                });
            }
            result[slotIndex] = portData;
        });
        return result;
    };

    const transformData = data => {
        return (
            data?.boardInfos?.map(card => {
                if (card.slotIndex === 0) {
                    const nmuItems = Object.entries(chassisConfig.NMU).map(([key, {top, left, status}]) => {
                        let className = key.startsWith("LED") ? styles.LED_COMMON : styles.PORT;
                        className +=
                            status?.key && status?.judge
                                ? ` ${LED_CLASS_MAP[status.judge(data[status.key])] || ""}`
                                : "";
                        const style = {top, left};
                        return {
                            title: `Name: ${key}`,
                            className,
                            style
                        };
                    });
                    return {
                        slotIndex: 0,
                        nmuItems
                    };
                }
                const type = card.boardType;
                const {side, ...style} = chassisConfig.FMT[card.slotIndex];
                const className = `${styles.CARD_common}  ${styles[`CARD_${card.boardType}`]}`;
                const title = `Name: ${card.boardType}-${card.slotIndex}`;
                const getValueByPath = path => {
                    return path
                        ? path.split(".").reduce((acc, key) => {
                              // 如果 key 包含数组索引
                              const match = key.match(/^(\w+)\[(\d+)]$/);
                              if (match) {
                                  const [, prop, index] = match;
                                  return acc?.[prop]?.[parseInt(index)];
                              }
                              return acc?.[key];
                          }, card)
                        : null;
                };
                const cardItems = Object.entries(chassisConfig[card.boardType] || {}).map(
                    ([key, {top, left, transform, status}]) => {
                        let className = key.startsWith("LED") ? styles.LED_COMMON : "";
                        if (key.startsWith("LED") && status?.key) {
                            const keyValue = getValueByPath(status.key);

                            if (status?.judge) {
                                if (status.threshold) {
                                    const threshold = getValueByPath(status.threshold);
                                    if (status.threshold2) {
                                        const threshold2 = getValueByPath(status.threshold2);
                                        className += ` ${LED_CLASS_MAP[status.judge(keyValue, threshold, threshold2)] || ""}`;
                                    } else {
                                        className += ` ${LED_CLASS_MAP[status.judge(keyValue, threshold)] || ""}`;
                                    }
                                } else {
                                    className += ` ${LED_CLASS_MAP[status.judge(keyValue)] || ""}`;
                                }
                            }
                        } else if (key.startsWith("PORT")) {
                            if (status?.key) {
                                const keyValue = getValueByPath(status.key);
                                if (card.boardType === "OEO" && status?.judge(keyValue)) {
                                    className = styles.PORT2;
                                }
                            } else {
                                className = styles.PORT1;
                            }
                        }
                        const type = key.startsWith("LED") ? "led" : "port";
                        const style = {top, left, transform};
                        const title = `Name: ${key}`;
                        return {
                            title,
                            type,
                            className,
                            style
                        };
                    }
                );
                return {
                    slotIndex: card?.slotIndex,
                    title,
                    type,
                    className,
                    style,
                    cardItems
                };
            }) ?? []
        );
    };

    const updateData = async () => {
        const neIP = selectedItem?.value?.host;
        if (!neIP) return;
        const neData = (await runAsync(neIP)).data;
        const _data = transformData(neData);
        const _cardData = transformCardData(neData);
        const _portData = transformPortData(neData);
        let slotIndex = "0";
        let type = "chassis";
        if (tableFilter.resource) {
            slotIndex = tableFilter.resource.value;
            type = tableFilter.resource.type;
        }
        setData(_data);
        setCardData({
            ne_id: tableFilter.id,
            type,
            neData: _cardData[slotIndex],
            slotIndex,
            deviceType: "FMT"
        });
        setPortData({
            ne_id: tableFilter.id,
            type,
            neData: _portData[slotIndex],
            slotIndex,
            deviceType: "FMT"
        });
    };

    useEffect(() => {
        if (chassisWrapRef.current && chassisRef.current) {
            chassisRef.current.style = `zoom: ${(chassisWrapRef?.current.clientWidth || 0) / 1856}`;
        }

        const observer = new ResizeObserver(
            debounce(() => {
                if (chassisRef.current)
                    chassisRef.current.style = `zoom: ${(chassisWrapRef?.current.clientWidth || 0) / 1856}`;
            }, 5)
        );
        observer.observe(containerRef.current);

        return () => {
            observer.disconnect();
        };
    }, []);

    return (
        <div className={styles.wrap}>
            {loading && (
                <div className={styles.loading}>
                    <Spin />
                </div>
            )}
            <div className={styles.deviceDisplay} style={{flex: 1}}>
                <div className={styles.viewDisplay}>
                    <Card style={{margin: "0 24px"}} ref={containerRef}>
                        <Flex vertical gap="middle" ref={chassisWrapRef}>
                            <Flex justify="space-between" style={{marginTop: "12px"}}>
                                <Radio.Group
                                    options={viewRadioOptions}
                                    optionType="button"
                                    value={viewSelect}
                                    onChange={onChangeViewSelect}
                                    className={styles.customRadioGroup}
                                />
                                <div>
                                    <Button onClick={sync} style={{marginLeft: "10px"}}>
                                        Synchronize
                                    </Button>
                                </div>
                            </Flex>
                            <ViewCard
                                viewSelect={viewSelect}
                                selectedItem={selectedItem}
                                data={data}
                                ref={chassisRef}
                            />
                        </Flex>
                    </Card>
                </div>
                <CardInfo data={cardData} />
                <PortList data={portData} />
            </div>
        </div>
    );
};

const ViewCard = forwardRef(({viewSelect, selectedItem, data}, ref) => {
    const dispatch = useDispatch();
    // 指向选中的模块元素
    const selectedPortRef = useRef({style: {}});
    let isDBClick;

    const setSelectPort = selectPort => {
        if (selectPort) {
            dispatch(
                setSelectedItem({
                    ...selectedItem,
                    selectPort: {type: selectPort.type, value: selectPort.value},
                    resource: {type: selectPort.type, value: selectPort.value}
                })
            );
            dispatch(
                setTableFilter({
                    type: "NODE_NE",
                    id: selectedItem.value.ne_id,
                    resource: {type: selectPort.type, value: selectPort.value}
                })
            );
        } else {
            const newSelect = {...selectedItem};
            delete newSelect.selectPort;
            dispatch(setSelectedItem(newSelect));
            dispatch(setTableFilter({type: "NODE_NE", id: selectedItem.value.ne_id}));
        }
    };

    // 机框图模块MouseMove
    const handleModuleMouseMove = e => {
        e.stopPropagation();
        e.target.style.boxShadow = "inset 0px 0px 80px 4px rgb(0 255 0 / 30%)";
    };
    // 机框图模块MouseOut
    const handleModuleMouseOut = e => {
        e.stopPropagation();

        if (e.target.title === selectedPortRef.current?.title) {
            return;
        }
        e.target.style.boxShadow = "";
    };

    // 机框图模块Click
    const handleModuleClick = e => {
        isDBClick = false;
        const {dataset} = e.currentTarget;
        e.stopPropagation();
        if (e.button === 0) {
            // console.log("Left button clicked");
            // setOpenDropdown(false);
        }
        if (!dataset.type) {
            return;
        }

        setTimeout(() => {
            if (!isDBClick) {
                if (e.target.title === selectedPortRef.current?.title) {
                    selectedPortRef.current.style.boxShadow = "";
                    selectedPortRef.current = {style: {}};
                    return;
                }
                e.target.style.boxShadow = "inset 0px 0px 80px 4px rgb(0 255 0 / 30%)";
                selectedPortRef.current.style.boxShadow = "";
                selectedPortRef.current = e.target;
                setSelectPort(dataset);
            }
        }, 300);
    };

    return (
        <Flex vertical align="center" justify="center" gap="24px">
            {viewSelect === "Front" ? (
                <div
                    ref={ref}
                    className={styles.chassisFront}
                    onMouseMove={handleModuleMouseMove}
                    onMouseOut={handleModuleMouseOut}
                    onClick={handleModuleClick}
                    onBlur={() => {}}
                    data-type="chassis"
                    data-value="0"
                    title="CHASSIS"
                >
                    {data?.map(({title, type, style, className, cardItems, slotIndex, nmuItems}) => {
                        if (slotIndex !== 0) {
                            return (
                                <div
                                    key={slotIndex}
                                    style={style}
                                    className={className}
                                    onClick={handleModuleClick}
                                    data-type={type}
                                    data-value={slotIndex}
                                    title={title}
                                >
                                    {cardItems?.map(({title, type, className, style}, index) => (
                                        <div
                                            key={index}
                                            className={className}
                                            style={style}
                                            data-type={type}
                                            data-value={slotIndex}
                                            title={title}
                                        />
                                    ))}
                                </div>
                            );
                        }
                        return nmuItems?.map(({title, style, className}, index) => (
                            <div key={index} style={style} className={className} />
                        ));
                    })}
                </div>
            ) : (
                <div
                    ref={ref}
                    className={styles.chassisRear}
                    onMouseMove={handleModuleMouseMove}
                    onMouseOut={handleModuleMouseOut}
                    onClick={handleModuleClick}
                    onBlur={() => {}}
                    data-type="chassis"
                    data-value="0"
                />
            )}
            <Text style={{paddingBottom: "20px"}}>{data?.length > 0 ? `FMT (${selectedItem?.value?.host})` : ""}</Text>
        </Flex>
    );
});

export default DeviceDisplay;
