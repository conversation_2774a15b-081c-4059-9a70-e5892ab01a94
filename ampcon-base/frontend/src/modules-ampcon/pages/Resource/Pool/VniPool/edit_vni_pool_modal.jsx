import React, {useState, forwardRef, useImperativeHandle, useEffect} from "react";
import {Button, Modal, Form, Input, Divider, Row, Col, message, Flex, Select} from "antd";
import {PlusOutlined, MinusOutlined} from "@ant-design/icons";
import {editResourcePoolVniPool, fetchVniFabricList} from "@/modules-ampcon/apis/resource_pool_api";
import {FormItemRangeInput} from "@/modules-ampcon/components/form_item_range_input";
import {isRangeListConflict, rangeInputValidatorWithDefaultValueCheck} from "@/utils/pool_utils";

const EditPoolModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showEditPoolModal: record => {
            const {ranges, name, id, use, fabric} = record;
            const formattedRanges = (ranges || []).map(range => ({
                startASN: range.start_value,
                endASN: range.end_value,
                rangeId: range.id,
                status: range.is_in_use
            }));
            setFormData(formattedRanges);
            setFields(formattedRanges); // 更新本地状态
            editPoolForm.setFieldsValue({pn: name, fields: formattedRanges, use, fabric: fabric.map(f => f.name)}); // 设置表单初始值
            // 监听 fields 的变化
            setOriginalRanges(formattedRanges); // 记录修改前的数据
            setPoolId(id); // 记录当前编辑的 poolId
            setIsShowModal(true);
        }
    }));
    const [formData, setFormData] = useState("");
    const [poolId, setPoolId] = useState(""); // 记录当前编辑的 poolId
    const [originalRanges, setOriginalRanges] = useState({}); // 记录修改前的数据
    const [fields, setFields] = useState([]); // 初始字段值
    const [isShowModal, setIsShowModal] = useState(false);
    const [editPoolForm] = Form.useForm();
    const [fabricList, setFabricList] = useState([]);

    useEffect(() => {
        const fetchFabricList = async () => {
            try {
                const response = await fetchVniFabricList(poolId);
                if (response.status === 200) {
                    setFabricList(response.data);
                } else {
                    message.error(response.info);
                }
            } catch (error) {
                message.error("Failed to fetch fabric list.");
            }
        };
        if (isShowModal) {
            fetchFabricList();
        }
    }, [isShowModal]);

    const handleSave = async () => {
        try {
            const inputs = document.querySelectorAll(".range-list input");
            inputs.forEach(input => {
                const event = new Event("focusout", {bubbles: true});
                input.dispatchEvent(event);
            });
            const values = await editPoolForm.validateFields();
            if (isRangeListConflict(values.fields)) {
                return message.error("Validation failed: Range is invalid.");
            }
        } catch (errorInfo) {
            // console.error("Validation failed:", errorInfo);
            return;
        }
        try {
            const value = editPoolForm.getFieldsValue();
            const {fields} = value; // 获取修改后表单中的 ranges
            // 计算新增、修改、删除的数据
            const rangesData = {
                delete: [],
                modify: [],
                add: []
            };
            // Delete
            const currentRangeIds = fields.filter(range => range && range.rangeId).map(range => range?.rangeId);
            const originalRangeIds = originalRanges.map(range => range.rangeId);
            rangesData.delete = originalRanges
                .filter(range => !currentRangeIds.includes(range.rangeId))
                .map(range => ({rangeId: range.rangeId}));
            // Modify
            rangesData.modify = fields
                .map(range => {
                    const originalRange = originalRanges.find(item => item && item.rangeId === range?.rangeId);
                    if (
                        originalRange &&
                        (originalRange.startASN !== range.startASN || originalRange.endASN !== range.endASN)
                    ) {
                        return {
                            rangeId: range.rangeId,
                            start: range.startASN,
                            end: range.endASN
                        };
                    }
                    return null;
                })
                .filter(item => item !== null); // Remove null values

            // Add
            rangesData.add = fields
                .filter(range => !originalRangeIds.includes(range?.rangeId))
                .map(range => ({
                    start: range.startASN,
                    end: range.endASN
                }));

            const editPoolDate = {
                poolId,
                poolName: value.pn,
                ranges: rangesData,
                rawRanges: fields.map(range => ({
                    start: range.startASN,
                    end: range.endASN,
                    rangeId: range.rangeId
                })),
                use: value.use,
                fabric: value.fabric
            };
            const res = await editResourcePoolVniPool(editPoolDate);
            if (res.status === 200) {
                message.success(res.info);
                setIsShowModal(false);
                editPoolForm.resetFields();
            } else {
                message.error(res.info);
            }
        } catch (error) {
            // console.error("Edit Pool Failed", error);
        }

        if (props.saveCallback) {
            props.saveCallback();
        }
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    Edit VNI Pool
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
                editPoolForm.resetFields();
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                editPoolForm.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleSave}>
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form form={editPoolForm} style={{minHeight: "267.23px"}}>
                <Form.Item
                    name="pn"
                    label="Pool Name"
                    rules={[
                        {required: true, message: "Please input Pool Name!"},
                        {
                            max: 64,
                            message: "Max length: 64 characters!" // 超过64字符时的提示信息
                        },
                        {
                            pattern: /^[\w:-]+$/,
                            message: (
                                <span>
                                    Name can only contain letters, numbers, underscores,
                                    <br />
                                    hyphens and colons.
                                </span>
                            )
                        }
                    ]}
                    initialValue={editPoolForm.getFieldValue("pn") !== "" ? editPoolForm.getFieldValue("pn") : ""}
                    labelCol={{style: {paddingRight: "32px"}}}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="use"
                    label="Use"
                    initialValue={editPoolForm.getFieldValue("use") !== "" ? editPoolForm.getFieldValue("use") : ""}
                    labelCol={{style: {paddingRight: "74.98px"}}}
                    rules={[
                        {
                            required: true,
                            message: "Please select Use"
                        }
                    ]}
                >
                    <Select
                        style={{width: "280px"}}
                        options={[
                            {label: "Default", value: "default"}
                            // {label: "Reserve", value: "reserve"}
                        ]}
                        placeholder="Select Use"
                        allowClear
                        disabled
                    />
                </Form.Item>
                <Form.Item
                    name="fabric"
                    label="Fabric"
                    initialValue={
                        editPoolForm.getFieldValue("fabric") !== "" ? editPoolForm.getFieldValue("fabric") : ""
                    }
                    labelCol={{style: {paddingRight: "60px"}}}
                    rules={[
                        {
                            required: true,
                            message: "Please select Fabric"
                        }
                    ]}
                >
                    <Select
                        style={{width: "280px"}}
                        options={fabricList.map(fabric => ({
                            label: fabric.name,
                            value: fabric.name,
                            disabled: !!props.currentRecord?.fabric.some(f => f.name === fabric.name)
                        }))}
                        placeholder="Select Fabric"
                        mode="multiple"
                    />
                </Form.Item>
                <Form.List
                    name="fields"
                    onChange={setFields} // 设置 fields 数据
                >
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map(({key, name}, index) => {
                                return (
                                    <Row key={key}>
                                        <Col>
                                            <FormItemRangeInput
                                                startOnBlurValidator={() => {
                                                    return rangeInputValidatorWithDefaultValueCheck(
                                                        editPoolForm,
                                                        formData,
                                                        index,
                                                        true,
                                                        16777215
                                                    );
                                                }}
                                                endOnBlurValidator={() => {
                                                    return rangeInputValidatorWithDefaultValueCheck(
                                                        editPoolForm,
                                                        formData,
                                                        index,
                                                        false,
                                                        16777215
                                                    );
                                                }}
                                                startDefaultValue={formData[index] ? formData[index].startASN : ""}
                                                endDefaultValue={formData[index] ? formData[index].endASN : ""}
                                                index={index}
                                            />
                                        </Col>
                                        <Col>
                                            <Button
                                                onClick={() => {
                                                    add();
                                                    setFormData([
                                                        ...formData,
                                                        {
                                                            startASN: null,
                                                            endASN: null,
                                                            status: false,
                                                            rangeId: null
                                                        }
                                                    ]);
                                                }}
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF",
                                                    marginBottom: "24px"
                                                }}
                                                type="link"
                                                icon={<PlusOutlined />}
                                            />
                                            {(fields.length > 1 &&
                                                formData[index] &&
                                                formData[index].status === true) ||
                                            fields.length === 1 ? null : (
                                                <Button
                                                    style={{
                                                        backgroundColor: "transparent",
                                                        color: "#BFBFBF",
                                                        marginBottom: "24px"
                                                    }}
                                                    type="link"
                                                    icon={<MinusOutlined />}
                                                    onClick={() => {
                                                        remove(name);
                                                        const updatedFormData = [...formData];
                                                        updatedFormData.splice(index, 1); // 删除对应的formData条目
                                                        setFormData(updatedFormData); // 更新formData
                                                    }}
                                                />
                                            )}
                                        </Col>
                                    </Row>
                                );
                            })}
                        </>
                    )}
                </Form.List>
            </Form>
        </Modal>
    ) : null;
});

export default EditPoolModal;
