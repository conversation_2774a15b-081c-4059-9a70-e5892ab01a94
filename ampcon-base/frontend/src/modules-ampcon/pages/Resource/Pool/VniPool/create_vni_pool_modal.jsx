import React, {useState, forwardRef, useImperative<PERSON><PERSON><PERSON>, useEffect} from "react";
import {Button, Modal, Form, Input, Divider, Row, Col, message, Flex, Select} from "antd";
import {PlusOutlined, MinusOutlined} from "@ant-design/icons";
import {createResourcePoolVniPool, fetchVniFabricList} from "@/modules-ampcon/apis/resource_pool_api";
import {FormItemRangeInput} from "@/modules-ampcon/components/form_item_range_input";
import {isRangeListConflict, rangeInputValidator} from "@/utils/pool_utils";

const CreatePoolModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showCreatePoolModal: () => {
            createPoolForm.resetFields();
            setIsShowModal(true);
        }
    }));
    const [fields, setFields] = useState([{startASN: null, endASN: null}]); // 初始字段值
    const [isShowModal, setIsShowModal] = useState(false);
    const [createPoolForm] = Form.useForm();
    const [, setLoading] = useState(true);
    const [fabricList, setFabricList] = useState([]);

    useEffect(() => {
        const fetchFabricList = async () => {
            try {
                const response = await fetchVniFabricList(0);
                if (response.status === 200) {
                    setFabricList(response.data);
                } else {
                    message.error(response.info);
                }
            } catch (error) {
                message.error("Failed to fetch fabric list.");
            }
        };
        if (isShowModal) {
            fetchFabricList();
        }
    }, [isShowModal]);

    const handleSave = async () => {
        try {
            const inputs = document.querySelectorAll(".range-list input");
            inputs.forEach(input => {
                const event = new Event("focusout", {bubbles: true});
                input.dispatchEvent(event);
            });
            const values = await createPoolForm.validateFields();
            if (isRangeListConflict(values.fields, 16777215)) {
                return message.error("Validation failed: Range is invalid.");
            }
        } catch (errorInfo) {
            // console.error("Validation failed:", errorInfo);
            return;
        }
        try {
            const values = createPoolForm.getFieldsValue();
            const createPoolDate = {
                poolName: values.pn,
                use: values.use,
                fabric: values.fabric,
                ranges: (values.fields || []).map(field => ({
                    start: field.startASN,
                    end: field.endASN
                }))
            };
            setLoading(true);
            const response = await createResourcePoolVniPool(createPoolDate);
            if (response.status === 200) {
                setIsShowModal(false);
                message.success(response.info);
                createPoolForm.resetFields();
            } else {
                message.error(response.info);
            }
        } catch (error) {
            // console.error("Create Pool Failed", error);
        }
        if (props.saveCallback) {
            props.saveCallback();
        }
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    Create VNI Pool
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
                createPoolForm.resetFields();
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                createPoolForm.resetFields();
                                setFields([{startASN: "", endASN: ""}]);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleSave}>
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form form={createPoolForm} style={{minHeight: "267.23px"}} labelAlign="left" layout="horizontal">
                <Form.Item
                    name="pn"
                    label="Pool Name"
                    validateFirst
                    rules={[
                        {
                            required: true,
                            message: "Please input Pool Name"
                        },
                        {
                            max: 64,
                            message: "Max length: 64 characters!"
                        },
                        {
                            pattern: /^[\w:-]+$/,
                            message: (
                                <span>
                                    Name can only contain letters, numbers, underscores,
                                    <br />
                                    hyphens and colons.
                                </span>
                            )
                        }
                    ]}
                    initialValue={createPoolForm.getFieldValue("pn") !== "" ? createPoolForm.getFieldValue("pn") : ""}
                    labelCol={{style: {paddingRight: "32px"}}}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="use"
                    label="Use"
                    initialValue={createPoolForm.getFieldValue("use") !== "" ? createPoolForm.getFieldValue("use") : ""}
                    labelCol={{style: {paddingRight: "74.98px"}}}
                    rules={[
                        {
                            required: true,
                            message: "Please select Use"
                        }
                    ]}
                >
                    <Select
                        style={{width: "280px"}}
                        options={[
                            {label: "Default", value: "default"}
                            // {label: "Reserve", value: "reserve"}
                        ]}
                        placeholder="Select Use"
                        allowClear
                    />
                </Form.Item>
                <Form.Item
                    name="fabric"
                    label="Fabric"
                    initialValue={
                        createPoolForm.getFieldValue("fabric") !== "" ? createPoolForm.getFieldValue("fabric") : ""
                    }
                    labelCol={{style: {paddingRight: "60px"}}}
                    rules={[
                        {
                            required: true,
                            message: "Please select Fabric"
                        }
                    ]}
                >
                    <Select
                        style={{width: "280px"}}
                        options={fabricList.map(fabric => ({
                            label: fabric.name,
                            value: fabric.name
                        }))}
                        placeholder="Select Fabric"
                        mode="multiple"
                        allowClear
                    />
                </Form.Item>
                <Form.List
                    name="fields"
                    initialValue={fields}
                    onChange={setFields} // 设置 fields 数据
                >
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map(({key, name, fieldNames, ...resetFields}, index) => {
                                return (
                                    <Row key={key}>
                                        <Col>
                                            <FormItemRangeInput
                                                startOnBlurValidator={() => {
                                                    return rangeInputValidator(createPoolForm, index, true, 16777215);
                                                }}
                                                endOnBlurValidator={() => {
                                                    return rangeInputValidator(createPoolForm, index, false, 16777215);
                                                }}
                                                startDefaultValue=""
                                                endDefaultValue=""
                                                index={index}
                                            />
                                        </Col>
                                        <Col>
                                            {index === 0 ? (
                                                <Button
                                                    onClick={() => add()}
                                                    style={{
                                                        backgroundColor: "transparent",
                                                        color: "#BFBFBF",
                                                        marginBottom: "24px"
                                                    }}
                                                    type="link"
                                                    icon={<PlusOutlined />}
                                                />
                                            ) : (
                                                // 其他 Range 后面是 - 号
                                                <Button
                                                    style={{
                                                        backgroundColor: "transparent",
                                                        color: "#BFBFBF",
                                                        marginBottom: "24px"
                                                    }}
                                                    type="link"
                                                    icon={<MinusOutlined />}
                                                    onClick={() => {
                                                        remove(name);
                                                    }}
                                                />
                                            )}
                                        </Col>
                                    </Row>
                                );
                            })}
                        </>
                    )}
                </Form.List>
            </Form>
        </Modal>
    ) : null;
});

export default CreatePoolModal;
