.cardHeight {
  display: flex;
  flex-grow: 1;
}

.titleSize1 {
  font-weight: bold;
  font-size: 18px;
  width: 193px;
  height: 22px;
  white-space: nowrap;
  margin-bottom: 24px;
}

.titleSize2 {
  font-size: 18px;
  font-weight: bold;
  margin: 0px;
}

.cardStyle1 {
  margin-right: 24px;
}

.cardStyle2 {
  flex: 1;
}

.buttonWidth {
  width: 80px;
  height: 32px;
}

.container {
  display: flex;
  height: 36px;
  align-items: center;
  justify-content: space-between; /* 使内容和按钮分别位于最左边和最右边 */
}

.expiredTag {
  background-color: rgba(245, 63, 63, 0.1);
  color: rgb(245, 63, 63);
  border-color: rgb(245, 63, 63);
  font-size: 14px;
  text-align: center;
}

.avaliableTag {
  background-color: rgba(84, 196, 28, 0.1);
  color: #2bc174;
  border-color: #2bc174;
  font-size: 14px;
  text-align: center;
}

.invalidTag {
  background-color: rgba(253, 186, 0, 0.1);
  color: rgb(253, 186, 0);
  border-color: rgb(253, 186, 0);
  font-size: 14px;
  text-align: center;
}

.rightButton {
  /* 按钮样式 */
  justify-content: end;
}

.divider1 {
  margin: 8px 0 24px 0;
}

.divider2 {
  margin: 12px 0 24px 0;
}
