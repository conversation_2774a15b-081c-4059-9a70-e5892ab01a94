import {forwardRef, useImperativeHandle, useState} from "react";
import {Button, Divider, Form, Input, Modal} from "antd";

const SendTestEmailModal = forwardRef((props, ref) => {
    const testEmailAddressLabel = "Test Email Address";

    const {sendTestEmailCallback} = props;

    const [sendTestEmailForm] = Form.useForm();
    const [isShowModal, setIsShowModal] = useState(false);

    useImperativeHandle(ref, () => ({
        showSendTestEmailModal: () => {
            setIsShowModal(true);
        },
        hideEmailUserSettingModal: () => {}
    }));

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    Send Test Email
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
                sendTestEmailForm.resetFields();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            sendTestEmailForm.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={async () => {
                            try {
                                await sendTestEmailForm.validateFields();
                            } catch (e) {
                                return;
                            }
                            sendTestEmailCallback(sendTestEmailForm.getFieldValue("testEmailAddress"));
                        }}
                    >
                        Send
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 5}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={sendTestEmailForm}
                style={{minHeight: "267.23px"}}
            >
                <Form.Item
                    name="testEmailAddress"
                    rules={[
                        {
                            validator: (_, value) => {
                                if (
                                    !/^[\w%+.-]+@[\d.A-Za-z-]+\.[A-Za-z]{2,}(?:,[\w%+.-]+@[\d.A-Za-z-]+\.[A-Za-z]{2,})*$/.test(
                                        value
                                    )
                                ) {
                                    return Promise.reject(
                                        new Error("Please enter valid email addresses separated by commas(,)!")
                                    );
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    label={testEmailAddressLabel}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});

export default SendTestEmailModal;
