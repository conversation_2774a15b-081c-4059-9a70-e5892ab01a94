import {useEffect, useRef, useState} from "react";
import {Button, Card, Checkbox, Radio, Form, Input, message, Spin, Tooltip, Flex} from "antd";
import CustomPasswordInput from "@/modules-ampcon/components/custom_input_password";
import {
    getEmailServerSetting,
    sendTestEmail,
    updateEmailServerSetting,
    verifyEmailServerConnection
} from "@/modules-ampcon/apis/email_api";
import {ApiFilled} from "@ant-design/icons";
import SendTestEmailModal from "@/modules-ampcon/pages/System/EmailSetting/send_test_email_modal";

const EmailSetting = () => {
    const smtpServerLabel = "SMTP Server Address";
    const smtpPortLabel = "SMTP Server Port";
    const senderEmailLabel = "Sender Email";
    const emailUserLabel = "Username";
    const authenticationLabel = "Use Authentication";
    const senderEmailPasswordLabel = "Password";
    const enableSSLOrTLSLabel = "Secure Connection";

    const [updateEmailServerSettingForm] = Form.useForm();
    const sendTestEmailModalRef = useRef();

    const [isShowSpin, setIsShowSpin] = useState(false);
    // 修改 isAuthentication 为字符串类型，用于存储单选按钮的值
    const [isAuthentication, setIsAuthentication] = useState("enabled");

    useEffect(() => {
        setIsShowSpin(true);
        getEmailServerSettingCallback();
    }, []);

    const getEmailServerSettingCallback = async () => {
        try {
            await getEmailServerSetting().then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else if (response.data.emailServer !== undefined) {
                    let enableSSLOrTLS = "none";
                    if (response.data.emailSSL) {
                        enableSSLOrTLS = "ssl";
                    } else if (response.data.emailTLS) {
                        enableSSLOrTLS = "tls";
                    }
                    // 根据接口返回的 isAuthentication 状态设置单选按钮的值
                    const authValue = response.data.isAuthentication ? "enabled" : "disabled";
                    setIsAuthentication(authValue);
                    if (response.data.isAuthentication === true) {
                        updateEmailServerSettingForm.setFieldsValue({
                            emailServer: response.data.emailServer,
                            emailPort: response.data.emailPort,
                            enableSSLOrTLS,
                            senderEmail: response.data.senderEmail,
                            isAuthentication: authValue,
                            emailUser: response.data.emailUser,
                            emailPassword: response.data.emailPassword
                        });
                    } else {
                        updateEmailServerSettingForm.setFieldsValue({
                            emailServer: response.data.emailServer,
                            emailPort: response.data.emailPort,
                            enableSSLOrTLS,
                            senderEmail: response.data.senderEmail,
                            isAuthentication: authValue
                        });
                    }
                } else {
                    setIsAuthentication("enabled");
                    updateEmailServerSettingForm.setFieldsValue({
                        emailServer: "",
                        emailPort: "",
                        enableSSLOrTLS: "none",
                        senderEmail: "",
                        isAuthentication: "enabled",
                        emailUser: "",
                        emailPassword: ""
                    });
                }
            });
        } catch (e) {
            message.error("Failed to get email server settings");
            setIsShowSpin(false);
        } finally {
            setIsShowSpin(false);
        }
    };

    const checkEmailServerConnection = async () => {
        try {
            try {
                await updateEmailServerSettingForm.validateFields();
            } catch (e) {
                return;
            }
            if (isAuthentication !== "enabled") {
                message.error("Please enable authentication first!");
                return;
            }
            setIsShowSpin(true);
            const emailSSL = updateEmailServerSettingForm.getFieldValue("enableSSLOrTLS") === "ssl";
            const emailTLS = updateEmailServerSettingForm.getFieldValue("enableSSLOrTLS") === "tls";
            await verifyEmailServerConnection({
                emailServer: updateEmailServerSettingForm.getFieldValue("emailServer"),
                emailPort: updateEmailServerSettingForm.getFieldValue("emailPort"),
                emailUser: updateEmailServerSettingForm.getFieldValue("emailUser"),
                emailPassword: updateEmailServerSettingForm.getFieldValue("emailPassword"),
                isAuthentication: isAuthentication === "enabled",
                emailSSL,
                emailTLS
            }).then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                }
            });
        } finally {
            setIsShowSpin(false);
        }
    };

    const emailServerSettingSaveCallback = async () => {
        try {
            try {
                await updateEmailServerSettingForm.validateFields();
            } catch (e) {
                return;
            }
            setIsShowSpin(true);
            const emailSSL = updateEmailServerSettingForm.getFieldValue("enableSSLOrTLS") === "ssl";
            const emailTLS = updateEmailServerSettingForm.getFieldValue("enableSSLOrTLS") === "tls";
            let data;
            if (isAuthentication === "enabled") {
                data = {
                    emailServer: updateEmailServerSettingForm.getFieldValue("emailServer"),
                    emailPort: updateEmailServerSettingForm.getFieldValue("emailPort"),
                    senderEmail: updateEmailServerSettingForm.getFieldValue("senderEmail"),
                    emailSSL,
                    emailTLS,
                    isAuthentication: true,
                    emailUser: updateEmailServerSettingForm.getFieldValue("emailUser"),
                    emailPassword: updateEmailServerSettingForm.getFieldValue("emailPassword")
                };
            } else {
                data = {
                    emailServer: updateEmailServerSettingForm.getFieldValue("emailServer"),
                    emailPort: updateEmailServerSettingForm.getFieldValue("emailPort"),
                    senderEmail: updateEmailServerSettingForm.getFieldValue("senderEmail"),
                    emailSSL,
                    emailTLS,
                    isAuthentication: false
                };
            }
            await updateEmailServerSetting(data).then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                }
            });
        } finally {
            setIsShowSpin(false);
        }
    };

    const sendTestEmailButtonClickCallback = async () => {
        try {
            await updateEmailServerSettingForm.validateFields();
        } catch (e) {
            return;
        }
        sendTestEmailModalRef.current.showSendTestEmailModal();
    };

    const sendTestEmailCallback = async targetEmailAddress => {
        try {
            setIsShowSpin(true);
            await sendTestEmail({
                emailServer: updateEmailServerSettingForm.getFieldValue("emailServer"),
                emailPort: updateEmailServerSettingForm.getFieldValue("emailPort"),
                senderEmail: updateEmailServerSettingForm.getFieldValue("senderEmail"),
                emailSSL: updateEmailServerSettingForm.getFieldValue("enableSSLOrTLS") === "ssl",
                emailTLS: updateEmailServerSettingForm.getFieldValue("enableSSLOrTLS") === "tls",
                isAuthentication: isAuthentication === "enabled",
                emailUser: updateEmailServerSettingForm.getFieldValue("emailUser"),
                emailPassword: updateEmailServerSettingForm.getFieldValue("emailPassword"),
                receivers: targetEmailAddress
            }).then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                }
            });
        } finally {
            setIsShowSpin(false);
        }
    };

    const resetForm = () => {
        // 重置表单到默认状态
        updateEmailServerSettingForm.resetFields();
        // 重置 isAuthentication 至默认状态
        setIsAuthentication("enabled");
        updateEmailServerSettingForm.setFieldsValue({
            isAuthentication: "enabled"
        });
    };

    return (
        <Card style={{display: "flex", flex: 1}}>
            <SendTestEmailModal ref={sendTestEmailModalRef} sendTestEmailCallback={sendTestEmailCallback} />
            <h2 style={{margin: "8px 0 20px"}}>Email Settings</h2>
            <Spin spinning={isShowSpin} fullscreen />
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={updateEmailServerSettingForm}
                style={{minHeight: "267.23px", width: "700px", minWidth: "600px"}}
                initialValues={{
                    enableSSLOrTLS: "none"
                }}
            >
                <Form.Item
                    name="emailServer"
                    required="true"
                    label={smtpServerLabel}
                    rules={[
                        {
                            validator: (_, value) => {
                                if (value === "") {
                                    return Promise.reject(new Error("Please input a valid domain or IP address!"));
                                }
                                const DOMAIN_REGEX = /^(?:[\dA-Za-z-]+\.){2}[A-Za-z]{2,}$/;
                                const IPV4_REGEX =
                                    /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/;
                                if (DOMAIN_REGEX.test(value) || IPV4_REGEX.test(value)) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error("Please input a valid smtp server!"));
                            }
                        }
                    ]}
                    initialValue=""
                >
                    <Input placeholder="Please input a valid domain or IP address" style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="emailPort"
                    required="true"
                    rules={[
                        {
                            validator: (_, value) => {
                                if (value === "" || isNaN(value)) {
                                    return Promise.reject(new Error("Please input a valid SMTP Server Port!"));
                                }
                                if (value < 1 || value > 65535) {
                                    return Promise.reject(new Error("Port number must be between 1 and 65535!"));
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    label={smtpPortLabel}
                    initialValue=""
                >
                    <Input placeholder="25, 465, 587" style={{width: "280px"}} />
                </Form.Item>
                <Form.Item name="enableSSLOrTLS" label={enableSSLOrTLSLabel}>
                    <Radio.Group
                        options={[
                            {value: "ssl", label: "SSL"},
                            {value: "tls", label: "TLS"},
                            {value: "none", label: "None"}
                        ]}
                    />
                </Form.Item>
                <Form.Item
                    name="senderEmail"
                    required="true"
                    rules={[
                        {
                            validator: (_, value) => {
                                if (!/^[\w%+.-]+@[\d.A-Za-z-]+\.[A-Za-z]{2,}$/.test(value)) {
                                    return Promise.reject(new Error("Please input a valid email address!"));
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    label={senderEmailLabel}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item name="isAuthentication" label={authenticationLabel} initialValue={isAuthentication}>
                    <Radio.Group
                        options={[
                            {value: "enabled", label: "Enabled"},
                            {value: "disabled", label: "Disabled"}
                        ]}
                        onChange={e => setIsAuthentication(e.target.value)}
                    />
                </Form.Item>
                {isAuthentication === "enabled" ? (
                    <>
                        <Form.Item
                            name="emailUser"
                            required="true"
                            rules={[
                                {
                                    required: true
                                }
                            ]}
                            label={emailUserLabel}
                        >
                            <Input
                                suffix={
                                    <ApiFilled
                                        style={{color: "rgba(0,0,0,.45)"}}
                                        onClick={checkEmailServerConnection}
                                    />
                                }
                                style={{width: "280px"}}
                            />
                        </Form.Item>
                        <Form.Item
                            name="emailPassword"
                            required="true"
                            label={senderEmailPasswordLabel}
                            rules={[{required: true, message: "Please input a valid Password!"}]}
                        >
                            <CustomPasswordInput style={{width: "280px"}} />
                        </Form.Item>
                    </>
                ) : null}
                <Form.Item wrapperCol={{offset: 6}}>
                    <Button
                        type="primary"
                        htmlType="button"
                        style={{width: 216}}
                        onClick={() => {
                            sendTestEmailButtonClickCallback();
                        }}
                    >
                        Send Test Email
                    </Button>
                </Form.Item>
                <Form.Item wrapperCol={{offset: 6}}>
                    <Flex gap="small">
                        <Button
                            type="primary"
                            htmlType="button"
                            style={{marginRight: "8px", width: 100}}
                            onClick={() => {
                                emailServerSettingSaveCallback();
                            }}
                        >
                            Apply
                        </Button>
                        <Button htmlType="button" style={{width: 100}} onClick={resetForm}>
                            Reset
                        </Button>
                    </Flex>
                </Form.Item>
            </Form>
        </Card>
    );
};

export default EmailSetting;
