import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Form, Row, Col, Card, message, Select, TreeSelect, Tooltip, Empty, Tabs, Radio} from "antd";
import {useEffect, useState, useRef, useMemo, forwardRef} from "react";
import {useForm} from "antd/es/form/Form";
import styles from "./Switch.module.scss";
import {CustomLineChart} from "@/modules-ampcon/components/echarts_common";
import EmptyPic from "@/assets/images/App/empty.png";
import {getDeviceNameInfo, getTopkInfo, getAiInfo, fetchRole} from "@/modules-ampcon/apis/monitor_api";
import {getFabric} from "@/modules-ampcon/apis/lifecycle_api";
import {AmpConCustomModalForm, createColumnConfig, AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import filterGreenSvg from "./resource/filterGreenSvg.svg?react";
import filterGreySvg from "./resource/filterGreySvg.svg?react";
import shrinkSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink.svg?react";
import unfoldSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold.svg?react";
import shrinkHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink_hover.svg?react";
import unfoldHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold_hover.svg?react";
import filterSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/filter.svg?react";
import Icon from "@ant-design/icons/lib/components/Icon";
import ProtectedRoute from "@/modules-ampcon/utils/util";
import RoceNicsMonitor from "@/modules-ampcon/pages/Monitor/RoCE_Counts/Nics/roce_monitor";
import {TelemetryDateRangePicker} from "@/modules-ampcon/components/telemetry_date_range_picker";

const {Option} = Select;
const interfaceCheckboxOptions = [
    {value: "ecn-marked-packets", label: "ECN-Marked-Packets"},
    {value: "send-pfc-pause-frames", label: "Send-PFC-Pause-Frames"},
    {value: "receive-pfc-pause-frames", label: "Receive-PFC-Pause-Frames"},
    {value: "pfc-deadlock-monitor-count", label: "PFC-Deadlock-Monitor-Count"},
    {value: "pfc-deadlock-recovery-count", label: "PFC-Deadlock-Recovery-Count"}
];

const pfcKind = [
    "send-pfc-pause-frames",
    "receive-pfc-pause-frames",
    "pfc-deadlock-monitor-count",
    "pfc-deadlock-recovery-count"
];

const SwitchPage = () => {
    const [form] = useForm();
    const [timeRange, setTimeRange] = useState(["", ""]);
    const counters = {
        interface: interfaceCheckboxOptions.map(item => item.value)
    };
    const [isSelectCountersModalOpen, setSelectCountersModalOpen] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const [selectedHost, setSelectedHost] = useState([]);
    const [selectedTopK, setSelectedTopK] = useState(5);
    const [activeTab, setActiveTab] = useState("trends");
    const [filters, setFilters] = useState([]);
    const [deviceNameList, setDeviceNameList] = useState([]);
    const selectedDevice = form.getFieldsValue().device || [];

    const monitoringCardRefs = useRef([]);
    const monitoringSettingRef = useRef();
    const filterResult = {};
    const filtersResult = [];

    const handleDeviceListChange = (list, isInitialLoad) => {
        setDeviceNameList(list);

        if (isInitialLoad) {
            const allDevicesObj = {}; // 用于 setSelectedHost
            const allDevicesArr = []; // 用于 setFilters

            list.forEach(item => {
                const key = item.value;
                const values = item.children?.map(child => child.title) || [];

                // 处理 selectedHost（对象格式）
                allDevicesObj[key] = values;

                // 处理 filters（数组格式）
                values.forEach(value => {
                    const existingEntry = allDevicesArr.find(entry => entry.sn === key);
                    if (existingEntry) {
                        existingEntry.interfaceNames.push(value);
                    } else {
                        allDevicesArr.push({
                            sn: key,
                            interfaceNames: [value]
                        });
                    }
                });
            });

            setSelectedHost(allDevicesObj); // 确保是对象格式
            setFilters(allDevicesArr); // 确保是数组格式
        }
    };

    const onChangeCounters = () => {
        // selected device  topK  setselectedhost settopk
        // refeshCheckedNodes();

        const selectedTopK = parseInt(form.getFieldsValue().topk.split(" ")[1], 10);

        if (selectedDevice.length > 0) {
            selectedDevice.forEach(item => {
                const match = item.match(/(.*?)-(.*)/); // 用正则表达式匹配 key 和 value
                if (match) {
                    const key = match[1]; // 第一组为 key
                    const value = match[2]; // 第二组为 value

                    // 处理 filterResult
                    if (!filterResult[key]) {
                        filterResult[key] = []; // 初始化数组
                    }
                    filterResult[key].push(value); // 添加到对应的数组中

                    // 处理 filters,新增设备编号和接口名称
                    const existingEntry = filtersResult.find(entry => entry.sn === key);
                    if (existingEntry) {
                        existingEntry.interfaceNames.push(value);
                    } else {
                        filtersResult.push({
                            sn: key,
                            interfaceNames: [value]
                        });
                    }
                }
            });
        } else {
            deviceNameList.forEach(item => {
                const key = item.value;
                const values = item.children.map(child => child.title);

                if (!filterResult[key]) {
                    filterResult[key] = [];
                }
                filterResult[key].push(...values);

                const existingEntry = filtersResult.find(entry => entry.sn === key);
                if (existingEntry) {
                    existingEntry.interfaceNames.push(...values);
                } else {
                    filtersResult.push({
                        sn: key,
                        interfaceNames: [...values]
                    });
                }
            });
        }

        setSelectedHost(filterResult);
        setSelectedTopK(selectedTopK);
        setFilters(filtersResult);
        handleDeviceListChange(deviceNameList);

        setSelectCountersModalOpen(false);
    };

    useEffect(() => {
        // 在组件挂载时设置默认值
        form.setFieldsValue(counters);
    }, []);

    const tabItems = [
        {
            key: "trends",
            label: "Trends",
            children: (
                <div style={{display: "flex", flexDirection: "column", marginTop: "20px"}}>
                    <Row gutter={[24, 24]}>
                        {counters.interface.length > 0 &&
                            counters.interface.map((interfaceItem, index) => (
                                <Col
                                    key={interfaceItem}
                                    span={24}
                                    xxl={12}
                                    style={{display: "flex", justifyContent: "center"}}
                                >
                                    <MonitoringCard
                                        ref={el => {
                                            monitoringCardRefs.current[index] = el;
                                        }}
                                        name={interfaceItem}
                                        type="interface"
                                        timeRange={timeRange}
                                        selectedHost={selectedHost}
                                        value={selectedTopK}
                                    />
                                </Col>
                            ))}
                    </Row>
                </div>
            )
        },
        {
            key: "statistics",
            label: "Statistics",
            children: <StatisticsTable filters={filters} topK={selectedTopK} />
        }
    ];
    const [deviceType, setDeviceType] = useState("switch");
    const renderTabsContent = () => {
        return (
            <>
                <div className={styles.dlbTab}>
                    <Tabs
                        tabBarStyle={{marginBottom: "4px"}}
                        defaultActiveKey="trends"
                        activeKey={activeTab}
                        onChange={setActiveTab}
                        tabBarExtraContent={
                            activeTab === "trends" ? (
                                <div
                                    style={{
                                        display: "flex",
                                        flexWrap: "wrap",
                                        alignItems: "center",
                                        justifyContent: "flex-end",
                                        maxWidth: "100%"
                                    }}
                                >
                                    Time (UTC-0)
                                    <TelemetryDateRangePicker timeRange={timeRange} setTimeRange={setTimeRange} />
                                    <Divider
                                        type="vertical"
                                        style={{height: "30px", marginLeft: "16px", marginRight: "16px"}}
                                    />
                                    <Tooltip title="Filter" placement="right">
                                        <Button
                                            style={{borderColor: isHovered ? "#34DCCF" : "#d9d9d9"}}
                                            icon={<Icon component={isHovered ? filterGreenSvg : filterGreySvg} />}
                                            onClick={() => {
                                                setSelectCountersModalOpen(true);
                                            }}
                                            onMouseEnter={() => {
                                                setIsHovered(true);
                                            }}
                                            onMouseLeave={() => {
                                                setIsHovered(false);
                                            }}
                                        />
                                    </Tooltip>
                                </div>
                            ) : (
                                <div style={{display: "flex", justifyContent: "right"}}>
                                    <Tooltip title="Filter" placement="right">
                                        <Button
                                            style={{borderColor: isHovered ? "#34DCCF" : "#d9d9d9"}}
                                            icon={<Icon component={isHovered ? filterGreenSvg : filterGreySvg} />}
                                            onClick={() => {
                                                setSelectCountersModalOpen(true);
                                            }}
                                            onMouseEnter={() => {
                                                setIsHovered(true);
                                            }}
                                            onMouseLeave={() => {
                                                setIsHovered(false);
                                            }}
                                        />
                                    </Tooltip>
                                </div>
                            )
                        }
                        items={tabItems.map(item => ({
                            ...item,
                            label: <span className={styles.dlbTab}>{item.label}</span>
                        }))}
                        style={{marginBottom: "32px"}}
                    />
                </div>
                <MonitoringSetting
                    form={form}
                    isModalOpen={isSelectCountersModalOpen}
                    onCancel={() => {
                        setSelectCountersModalOpen(false);
                        monitoringSettingRef.current?.resetCheckedKeys();
                    }}
                    onChange={onChangeCounters}
                    timeRange={timeRange}
                    onDeviceListChange={handleDeviceListChange}
                />
            </>
        );
    };

    return (
        <div style={{display: "flex", flex: 1, flexDirection: "column", height: "100vh"}}>
            <Form
                layout="inline"
                style={{flexWrap: "nowrap", marginBottom: "30px"}}
                initialValues={{devicetype: "switch"}}
            >
                <Form.Item name="devicetype" label="Device Type" wrapperCol={{style: {marginLeft: 20, width: 280}}}>
                    <Radio.Group value={deviceType} onChange={e => setDeviceType(e.target.value)}>
                        <Radio value="switch">Switch</Radio>
                        <Radio value="nic">NIC</Radio>
                    </Radio.Group>
                </Form.Item>
            </Form>
            {deviceType === "switch" ? renderTabsContent() : <ProtectedRoute component={RoceNicsMonitor} />}
        </div>
    );
};

export const MonitoringCard = forwardRef(({name, type, timeRange, selectedHost, value, cardstyle}, ref) => {
    const [chartData, setChartData] = useState([]);
    const [xAxisData, setXAxisData] = useState([]);
    const [xAxisInterval, setXAxisInterval] = useState(1);

    // todo get_interface_topK
    const topK = value;
    const fetchData = async () => {
        const filter = selectedHost || {};

        try {
            // todo 增加filter参数
            const params = {
                metricName: name,
                topK,
                startTime: timeRange[0],
                endTime: timeRange[1],
                filter
            };

            if (!filter || Object.keys(filter).length === 0) {
                setChartData([]);
                setXAxisData([]);
                return;
            }
            const response = await getTopkInfo(params);
            // console.log("request data:", {metricName: name, topK, timeRange, filter});

            if (response.status !== 200) {
                message.error(response.info);
                setChartData([]);
                setXAxisData([]);
            } else if (response.data.length > 0) {
                const series = response.data.map(item =>
                    pfcKind.includes(name)
                        ? {
                              name: `${item.target}: ${item.interface_name}--${item.queue_name}`,
                              data: item.values.map(([x, y]) => [x, y])
                          }
                        : {
                              name: `${item.target}: ${item.interface_name}`,
                              data: item.values.map(([x, y]) => [x, y])
                          }
                );
                setChartData(series);
                const xAxisData = Array.from(
                    new Set(response.data.flatMap(item => item.values.map(([x]) => x)))
                ).sort();
                if (timeRange[0] && timeRange[1]) {
                    const totalPoints = xAxisData.length;
                    const interval = Math.floor(totalPoints / 5);
                    setXAxisInterval(interval);
                } else {
                    setXAxisInterval(1);
                }
                setXAxisData(xAxisData);
            } else {
                setChartData([]);
                setXAxisData([]);
            }
        } catch (error) {
            message.error("Request data failed.");
        }
    };

    useEffect(() => {
        fetchData();
    }, [name, timeRange, topK, selectedHost]);

    const option = useMemo(
        () => ({
            tooltip: {
                trigger: "axis",
                formatter: params => {
                    const sortedParams = params.sort((a, b) => b.value[1] - a.value[1]);

                    let content = `
                <div style="width: 100%; margin: 0; padding: 0;">
                    <div style="background-color: #F8FAFB; width: calc(100% + 22px); padding: 5px;padding-left:14px; margin: -11px -12px 10px -11px;border-bottom: 1px solid #F2F2F2;">
                        <div style="font-size: 16px;front-weight: 600 ; color: #212519">${params[0].name}</div>
                    </div>
            `;
                    sortedParams.forEach(item => {
                        content += `
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div style="display: flex; align-items: center;">
                            <span style="display:inline-block;margin-right:5px;border-radius:1px;width:12px;height:12px;background-color:${item.color};"></span>
                            <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">${item.seriesName}</span>
                          </div>
                          <span style="margin-left: 20px;front-weight: 400; font-size: 14px; color: #212519">${item.value[1]}</span>
                        </div>
                    `;
                    });

                    content += `</div>`;
                    return content;
                },
                position(pos, params, el, elRect, size) {
                    const obj = {};
                    const [x, y] = pos;
                    const tooltipWidth = el.getBoundingClientRect().width;
                    const parentRect = el.parentElement.getBoundingClientRect();
                    const rightSpace = parentRect.width - x;
                    if (y > window.innerHeight / 2) {
                        obj.bottom = "30px";
                        delete obj.top;
                    }
                    if (rightSpace < x - 10 - tooltipWidth) {
                        obj.left = `${x - tooltipWidth - 10}px`;
                    } else {
                        obj.left = `${x + 10}px`;
                    }

                    return obj;
                }
            },
            legend: {
                data: chartData.map(item => item.name),
                orient: "horizontal", // 设置图例的方向为水平
                top: "90%", // 设置图例的垂直位置
                left: "center", // 设置图例的水平位置
                right: "5%",
                textStyle: {
                    // 图例文字样式
                    fontSize: 15
                },
                itemWidth: 10, // 图例图形的宽度
                itemHeight: 10, // 图例图形的高度
                type: "scroll",
                itemGap: 30,
                pageIconColor: "#A2ACB2", // 默认可点击色值
                pageIconInactiveColor: "#E3E5EB", // 不可点击色值
                width: "95%",
                icon: "rect"
            },
            grid: {
                left: "3%",
                right: "3%",
                top: "5%",
                bottom: "10%",
                containLabel: true,
                width: "95%",
                height: "75%"
            },
            xAxis: {
                type: "category",
                data: xAxisData,
                axisLabel: {
                    interval: xAxisInterval,
                    formatter(value) {
                        const date = new Date(value);
                        const startDate = new Date(timeRange[0] || Date.now() - 5 * 60 * 1000);
                        const endDate = new Date(timeRange[1] || Date.now());
                        const hour = date.getHours().toString().padStart(2, "0");
                        const minute = date.getMinutes().toString().padStart(2, "0");
                        const second = date.getSeconds().toString().padStart(2, "0");
                        if (startDate.getMonth() !== endDate.getMonth() || startDate.getDate() !== endDate.getDate()) {
                            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${hour}:${minute}`;
                        }
                        return `${hour}:${minute}:${second}`;
                    }
                },
                splitLine: {
                    show: true
                }
            },
            yAxis: {
                type: "value",
                axisLabel: {
                    formatter(value) {
                        if (value > 1e9) {
                            return `${value.toExponential(2)}`;
                        }
                        if (value >= 1000000) {
                            return `${value / 1000000}M`;
                        }
                        if (value >= 1000) {
                            return `${value / 1000}k`;
                        }
                        return value;
                    }
                }
            },
            series: chartData.map(item => ({
                name: item.name,
                type: "line",
                data: item.data,
                symbol: "none"
            })),
            width: "100%",
            height: "180px"
        }),
        [chartData]
    );

    let label;

    if (type === "interface") {
        label = interfaceCheckboxOptions.find(item => item.value === name).label;
    }

    return (
        <Card
            title={
                <div
                    className={styles.monitoring}
                    style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}
                >
                    {label}
                </div>
            }
            style={{
                height: "350px",
                width: "100%",
                borderColor: "#E7E7E7",
                ...(cardstyle ?? {})
            }}
            className="linechart"
        >
            {option.series.length === 0 ? (
                <div style={{display: "flex", justifyContent: "center", alignItems: "center"}}>
                    <Empty image={EmptyPic} description="No Data" imageStyle={{display: "block", margin: 0}} />
                </div>
            ) : (
                <CustomLineChart chartOption={option} />
            )}
        </Card>
    );
});

const MonitoringSetting = ({form, isModalOpen, onCancel, onChange, onDeviceListChange}) => {
    const [hoveredIcons, setHoveredIcons] = useState({});

    const handleMouseEnter = id => {
        setHoveredIcons(prev => ({...prev, [id]: true}));
    };

    const handleMouseLeave = id => {
        setHoveredIcons(prev => ({...prev, [id]: false}));
    };

    const getIconComponent = (expanded, isHovered) => {
        if (expanded) {
            return isHovered ? shrinkHoverSvg : shrinkSvg;
        }
        return isHovered ? unfoldHoverSvg : unfoldSvg;
    };

    const switcherIcon = ({expanded, id}) => {
        const IconComponent = getIconComponent(expanded, hoveredIcons[id]);

        return (
            <IconComponent
                style={{width: "16px", height: "16px", marginTop: "4px", marginLeft: "8px"}}
                alt={expanded ? "shrink" : "unfold"}
                onMouseEnter={() => handleMouseEnter(id)}
                onMouseLeave={() => handleMouseLeave(id)}
            />
        );
    };

    const [fabricLists, setFabricLists] = useState([]);
    const [selectedFabric, setSelectedFabric] = useState([]);
    const [roleLists, setRoleLists] = useState([]);
    const [selectedRole, setSelectedRole] = useState([]);
    const [deviceNameList, setDeviceNameList] = useState([]);
    const [selectedDevice, setSelectedDevice] = useState([]);
    const [isInitialLoad, setIsInitialLoad] = useState(true); // 标识是否是初次加载

    const fetchFabricList = async () => {
        try {
            const response = await getFabric();
            if (response.status === 200) {
                setFabricLists(response.data);
            } else {
                message.error(response.info);
            }
        } catch (error) {
            message.error("Failed to get Fabric.");
        }
    };
    const fetchRoles = async fabric => {
        if (!fabric.length) {
            setRoleLists([]);
            setSelectedRole([]);
            return;
        }
        try {
            const response = await fetchRole(fabric);
            if (response.status === 200) {
                setRoleLists(response.data); // 动态更新role列表
            } else {
                message.error(response.info);
            }
        } catch (error) {
            message.error("Failed to get role.");
        }
    };
    useEffect(() => {
        fetchFabricList();
    }, []);
    useEffect(() => {
        fetchRoles(selectedFabric);
        setSelectedRole([]);
        setSelectedDevice([]);
        form.setFieldsValue({role: [], device: []});
    }, [selectedFabric]);

    const fetchDeviceNames = async () => {
        const extractParts = str => {
            const match = str.match(/^([A-Za-z-]+)-(\d+)\/(\d+)\/(\d+)$/);
            if (!match) {
                return {prefix: str, numbers: []};
            }
            const prefix = match[1];
            const numbers = match.slice(2).map(num => parseInt(num, 10));
            return {prefix, numbers};
        };

        const compareNumbers = (a, b) => {
            const aParts = extractParts(a);
            const bParts = extractParts(b);
            if (aParts.prefix !== bParts.prefix) {
                return aParts.prefix.localeCompare(bParts.prefix);
            }
            for (let i = 0; i < Math.min(aParts.numbers.length, bParts.numbers.length); i++) {
                if (aParts.numbers[i] !== bParts.numbers[i]) {
                    return aParts.numbers[i] - bParts.numbers[i];
                }
            }
            return aParts.numbers.length - bParts.numbers.length;
        };

        const fabricParam = selectedFabric.length === 0 ? [] : selectedFabric;
        const roleParam = selectedRole.length === 0 ? [] : selectedRole;
        try {
            const response = await getDeviceNameInfo(fabricParam, roleParam);
            if (response.status === 200) {
                const treeData = response.data.map(item => {
                    const children = (item.children || []).map(child => {
                        return {
                            title: child,
                            value: `${item.sn}-${child}`,
                            key: `${item.sn}-${child}`
                        };
                    });
                    children.sort((a, b) => compareNumbers(a.title, b.title));
                    return {
                        title: `SN: ${item.sn}`,
                        value: item.sn,
                        key: item.sn,
                        children
                    };
                });

                setDeviceNameList(treeData);
                onDeviceListChange(treeData, isInitialLoad);
                setIsInitialLoad(false); // 之后不再是初次加载
            } else {
                message.error(response.info);
            }
        } catch (error) {
            message.error("Failed to obtain device information.");
        }
    };
    useEffect(() => {
        fetchDeviceNames();
    }, [selectedFabric, selectedRole]);

    // Top K 展示
    const [topK, setTopK] = useState(5);
    const [useTopN, setUseTopN] = useState(false);

    useEffect(() => {
        if (selectedDevice.length === 0) {
            setUseTopN(false);
            setTopK(5);
            return form.setFieldsValue({topk: `Top ${topK}`});
        }
        if (useTopN) {
            return form.setFieldsValue({topk: `Total ${selectedDevice.length}`});
        }
    }, [selectedDevice]);

    const formItems = () => {
        return (
            <>
                <Form.Item name="fabric" label="Fabric">
                    <Select
                        mode="multiple"
                        style={{width: 280}}
                        placeholder="Select Fabric"
                        value={selectedFabric}
                        onChange={value => {
                            setSelectedFabric(value);
                        }}
                        allowClear
                        showSearch={false}
                    >
                        {fabricLists.map(fabric => (
                            <Option key={fabric} value={fabric}>
                                {fabric}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>
                <Form.Item name="role" label="Device Role">
                    <Select
                        mode="multiple"
                        style={{width: 280}}
                        placeholder="Select Role"
                        value={selectedRole}
                        onChange={value => {
                            setSelectedRole(value);
                            setSelectedDevice([]);
                            form.setFieldsValue({device: []});
                        }}
                        allowClear
                        showSearch={false}
                    >
                        {roleLists.map(role => (
                            <Option key={role} value={role}>
                                {role}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>
                <Form.Item name="device" label="Sysname">
                    <TreeSelect
                        maxTagCount={2}
                        maxTagTextLength={6}
                        treeData={deviceNameList}
                        value={selectedDevice}
                        onChange={value => {
                            setSelectedDevice(value);
                        }}
                        treeCheckable
                        switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
                        placeholder="Select Device"
                        style={{width: 280}}
                        allowClear
                        virtual={false}
                        showSearch={false}
                        // suffixIcon={<Icon component={filterSvg} />}
                    />
                </Form.Item>
                <Form.Item name="topk" label="TopN">
                    <Select
                        style={{width: 280}}
                        onChange={value => {
                            const isTopN = value === `Total ${selectedDevice.length}`;
                            setUseTopN(isTopN);
                            if (!isTopN) {
                                const num = parseInt(value.split(" ")[1], 10);
                                setTopK(num);
                            }
                        }}
                        defaultValue="Top 5"
                    >
                        <Option value="Top 5">Top 5</Option>
                        <Option value="Top 10">Top 10</Option>
                        <Option value="Top 25">Top 25</Option>
                        {selectedDevice.length > 0 && (
                            <Option value={`Total ${selectedDevice.length}`}>{`Total ${selectedDevice.length}`}</Option>
                        )}
                    </Select>
                </Form.Item>
            </>
        );
    };
    return (
        <AmpConCustomModalForm
            title="Filter"
            isModalOpen={isModalOpen}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 5
                }
            }}
            CustomFormItems={formItems}
            onCancel={onCancel}
            onSubmit={onChange}
            modalClass="ampcon-middle-modal"
        />
    );
};

const StatisticsTable = ({filters, topK}) => {
    const [taskData, setTaskData] = useState([]); // State to store table data
    const [loading, setLoading] = useState(false); // Loading state

    const taskColumns = [
        createColumnConfig("Fabric", "fabric"),
        createColumnConfig("Sysname", "hostname"),
        createColumnConfig("Device Role", "role"),
        createColumnConfig("Port Name", "interface_name"),
        createColumnConfig("Queue Number", "queue_name"),
        createColumnConfig("Send-PFC-Pause-Frames", "send_pfc_pause_frames"),
        createColumnConfig("Receive-PFC-Pause-Frames", "receive_pfc_pause_frames"),
        createColumnConfig("PFC-Deadlock-Monitor-Count", "pfc_deadlock_monitor_count"),
        createColumnConfig("PFC-Deadlock-Recovery-Count", "pfc_deadlock_recovery_count"),
        createColumnConfig("Send-PFC-Pause-Frames Rate", "send_pfc_pause_frames_rate"),
        createColumnConfig("Receive-PFC-Pause-Frames Rate", "receive_pfc_pause_frames_rate"),
        createColumnConfig("ECN Marking Number", "ecn_marked_packets"),
        createColumnConfig("The Rate Of ECN Marked Packets", "ecn_marked_packets_rate")
    ];

    const fetchAiInfo = async () => {
        setLoading(true); // Set loading to true

        // 如果 filters 为空数组，直接返回空数据
        if (Array.isArray(filters) && filters.length === 0) {
            console.log("filters is empty, setting taskData to empty array");
            setTaskData([]);
            setLoading(false);
            return;
        }

        try {
            const params = {filters, topK};
            const response = await getAiInfo(params);
            console.log("request data:", {filters, topK});

            if (response.status === 200) {
                if (response.data && response.data.length > 0) {
                    const sanitizedData = response.data.map(item => {
                        return Object.fromEntries(
                            Object.entries(item).map(([key, value]) => [
                                key,
                                value === null || value === undefined || value === "" ? "-" : value
                            ])
                        );
                    });

                    setTaskData(sanitizedData); // 更新表格数据
                } else {
                    setTaskData([]); // 如果返回数据为空，确保清空表格数据
                    console.log("no AI info data");
                }
            } else {
                console.warn("API response status is not 200:", response.status);
                setTaskData([]);
            }
        } catch (error) {
            console.error("Error fetching AI info:", error);
            message.error("Error fetching data");
        } finally {
            setLoading(false); // 设置加载状态为 false
        }
    };

    useEffect(() => {
        fetchAiInfo();
    }, [filters, topK]);

    return (
        <AmpConCustomTable
            columns={taskColumns}
            dataSource={taskData}
            loading={loading}
            tableLayout="fixed"
            isShowPagination
        />
    );
};

export default SwitchPage;
