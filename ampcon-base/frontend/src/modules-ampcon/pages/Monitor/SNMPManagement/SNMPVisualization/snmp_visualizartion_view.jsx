import {useEffect, useState, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON>, useMemo} from "react";
import {useForm} from "antd/es/form/Form";
import {DatePicker, Row, Col, Card, message, Select, Empty} from "antd";
import EmptyPic from "@/assets/images/App/empty.png";
import {
    fetchSnmpDeviceTemperTopk,
    fetchSnmpVisualTopk,
    fetchSnmpVisualUsageTopk
} from "@/modules-ampcon/apis/monitor_api";
import {CustomLineChart} from "@/modules-ampcon/components/echarts_common";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import {TelemetryDateRangePicker} from "@/modules-ampcon/components/telemetry_date_range_picker";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault("UTC");

const temperatureOptions = {
    temperature: "Device Temperature (°C)"
};
const usageOptions = {
    cpu: "CPU (%)",
    memory: "Memory (%)"
};

const interfaceOptions = {
    "out-packets-with-errors": "Out Packets with Errors (Count)",
    "out-packets-discarded": "Out Packets Discarded (Count)",
    "in-packets-with-errors": "In Packets with Errors (Count)",
    "in-packets-discarded": "In Packets Discarded (Count)",
    "bits-sent": "Bits Sent (Bytes)",
    "bits-received": "Bits Received (Bytes)"
};

const SnmpVisualizationView = () => {
    const [form] = useForm();
    const [timeRange, setTimeRange] = useState(["", ""]);
    const [counters, setCounters] = useState({
        temperature: Object.keys(temperatureOptions),
        usage: Object.keys(usageOptions),
        interface: Object.keys(interfaceOptions)
    });
    const [quickRange, setQuickRange] = useState(null);

    useEffect(() => {
        document.querySelector(".ant-layout .ant-layout-content")?.scrollTo(0, 0);
        form.setFieldsValue(counters);
    }, []);
    const commonCardStyle = {
        borderColor: "#E7E7E7",
        borderWidth: "1px",
        borderStyle: "solid",
        boxShadow: "none"
    };
    const allCards = useMemo(
        () => [
            ...counters.temperature.map(item => (
                <SnmpCard
                    key={item}
                    name={item}
                    type="temperature"
                    label={temperatureOptions[item]}
                    timeRange={timeRange}
                    cardstyle={commonCardStyle}
                />
            )),
            ...counters.usage.map(item => (
                <SnmpCard
                    key={item}
                    name={item}
                    type="usage"
                    label={usageOptions[item]}
                    timeRange={timeRange}
                    cardstyle={commonCardStyle}
                />
            )),
            ...counters.interface.map(item => (
                <SnmpCard
                    key={item}
                    name={item}
                    type="interface"
                    label={interfaceOptions[item]}
                    timeRange={timeRange}
                    cardstyle={commonCardStyle}
                />
            ))
        ],
        [counters, timeRange]
    );
    return (
        <Card style={{display: "flex", flexDirection: "column", flex: 1, border: "none"}}>
            <div>
                <div style={{display: "flex", justifyContent: "space-between", marginTop: "-12px"}}>
                    <h2 style={{margin: "20px 0 20px"}}>SNMP Visualization</h2>
                    <div
                        style={{
                            display: "flex",
                            alignItems: "center",
                            marginTop: "20px",
                            marginBottom: "20px"
                        }}
                    >
                        <div style={{fontSize: "14px", marginRight: "32px"}}>Time</div>
                        <Select
                            style={{width: 160}}
                            value={quickRange}
                            allowClear
                            placeholder="Quick Select"
                            onChange={value => {
                                if (!value) {
                                    setQuickRange(null);
                                    setTimeRange(["", ""]);
                                    return;
                                }

                                setQuickRange(value);

                                const now = dayjs.utc();
                                const ranges = {
                                    last1h: [now.subtract(1, "hour"), now],
                                    last6h: [now.subtract(6, "hour"), now],
                                    last12h: [now.subtract(12, "hour"), now],
                                    today: [now.startOf("day"), now],
                                    yesterday: [
                                        now.subtract(1, "day").startOf("day"),
                                        now.subtract(1, "day").endOf("day")
                                    ],
                                    last7d: [now.subtract(7, "day"), now],
                                    last30d: [now.subtract(30, "day"), now]
                                };

                                const newRange = ranges[value] || [now.subtract(1, "hour"), now];

                                setTimeRange([
                                    newRange[0].format("YYYY-MM-DD HH:mm"),
                                    newRange[1].format("YYYY-MM-DD HH:mm")
                                ]);
                            }}
                        >
                            <Option value="last1h">Last 1 Hour</Option>
                            <Option value="last6h">Last 6 Hours</Option>
                            <Option value="last12h">Last 12 Hours</Option>
                            <Option value="today">Today</Option>
                            <Option value="yesterday">Yesterday</Option>
                            <Option value="last7d">Last 7 Days</Option>
                            <Option value="last30d">Last 30 Days</Option>
                        </Select>

                        <div style={{fontSize: "14px", marginLeft: "8px", marginRight: "-20px"}}>Or</div>
                        <TelemetryDateRangePicker
                            timeRange={timeRange}
                            setTimeRange={setTimeRange}
                            placeholder={["Start Time", "End Time"]}
                        />
                    </div>
                </div>
            </div>
            <div style={{width: "100%", marginBottom: "18px"}}>
                {/* <Row gutter={[24, 24]}>
                    {allCards.flat().map((card, index) =>
                        card ? (
                            <Col key={index} span={24} xxl={12} style={{display: "flex", justifyContent: "center"}}>
                                {card}
                            </Col>
                        ) : null
                    )}
                </Row> */}
                <Row gutter={[24, 24]}>
                    {allCards.map((card, index) => {
                        if (card.props.name === "temperature") {
                            return (
                                <Col key={index} span={24} style={{display: "flex", justifyContent: "center"}}>
                                    {card}
                                </Col>
                            );
                        }
                        return (
                            <Col key={index} span={24} xxl={12} style={{display: "flex", justifyContent: "center"}}>
                                {card}
                            </Col>
                        );
                    })}
                </Row>
            </div>
        </Card>
    );
};

export const SnmpCard = forwardRef(({name, type, timeRange, cardstyle, target = ""}, ref) => {
    const [chartData, setChartData] = useState([]);
    const [xAxisData, setXAxisData] = useState([]);
    const [topK, setTopK] = useState(5);
    const [xAxisInterval, setXAxisInterval] = useState(1);

    const fetchDataTrue = async () => {
        let response;
        if (type === "interface") {
            response = await fetchSnmpVisualTopk(name, topK, target, timeRange[0], timeRange[1]);
        } else if (type === "usage") {
            response = await fetchSnmpVisualUsageTopk(name, topK, target, timeRange[0], timeRange[1]);
        } else if (type === "temperature") {
            response = await fetchSnmpDeviceTemperTopk(name, topK, target, timeRange[0], timeRange[1]);
        }

        if (response.status !== 200) {
            message.error(response.info);
            setChartData([]);
            setXAxisData([]);
        } else if (response.data.length > 0) {
            const series = response.data.map(item => ({
                name: `${item.target}: ${item.interface_name}${item.queue_name ? `--${item.queue_name}` : ""}`,
                data: item.values.map(([x, y]) => ({
                    value: [x, y],
                    index_value: item.index_value
                })),
                index_value: item.index_value
            }));

            setChartData(series);
            const xAxisData = Array.from(new Set(response.data.flatMap(item => item.values.map(([x]) => x)))).sort();
            if (timeRange[0] && timeRange[1]) {
                const totalPoints = xAxisData.length;
                const interval = Math.floor(totalPoints / 5);
                setXAxisInterval(interval);
            } else {
                setXAxisInterval(1);
            }
            setXAxisData(xAxisData);
        } else {
            setChartData([]);
            setXAxisData([]);
        }
    };
    const getNiceAxis = (rawMax, rawMin, targetTicks = 5) => {
        if (!isFinite(rawMax) || !isFinite(rawMin)) return {max: 1, min: 0, interval: 0.2};
        let min, max;
        const isAllPositive = rawMin >= 0;
        const isAllNegative = rawMax <= 0;
        if (rawMax === 0 && rawMin === 0) {
            return {max: 1, min: 0, interval: 0.2};
        }
        if (isAllPositive) {
            min = 0;
            max = rawMax;
        } else if (isAllNegative) {
            // 全负数特殊处理：从最小值到0
            min = rawMin;
            max = 0;
        } else {
            min = rawMin;
            max = rawMax;
        }
        // 计算初始间隔
        const calculateInterval = span => {
            const rough = span / targetTicks;
            const mag = 10 ** Math.floor(Math.log10(Math.abs(rough)));
            const r = rough / mag;
            if (r <= 1) return 1 * mag;
            if (r <= 2) return 2 * mag;
            if (r <= 2.5) return 2.5 * mag;
            if (r <= 5) return 5 * mag;
            return 10 * mag;
        };
        let interval = calculateInterval(max - min);
        // 对全负数情况的特殊处理
        if (isAllNegative) {
            // 计算绝对值范围的nice间隔
            const absInterval = calculateInterval(Math.abs(rawMin));
            // 确保间隔为正值，并向下取整
            interval = Math.abs(absInterval);
            min = Math.floor(rawMin / interval) * interval;
            // 确保0总是包含在内
            max = 0;
        }
        // 对全正数情况的特殊处理
        if (isAllPositive) {
            const targetRatio = 4 / 5;
            const candidateMax1 = Math.ceil(rawMax / interval) * interval;
            const candidateMax2 = Math.ceil(rawMax / targetRatio / interval) * interval;

            max = Math.min(candidateMax2, rawMax * 2);
            interval = calculateInterval(max);
        }
        // 最终调整
        if (isAllPositive) {
            max = Math.ceil(max / interval) * interval;
        } else if (isAllNegative) {
            // 保持min的调整，max固定为0
        } else {
            max = Math.ceil(max / interval) * interval;
            min = Math.floor(min / interval) * interval;
        }
        return {max, min, interval};
    };
    const allValues = chartData.flatMap(item => item.data.map(point => point.value[1]));
    const dataMax = allValues.length > 0 ? allValues.reduce((max, v) => Math.max(max, v), -Infinity) : 0;
    const dataMin = allValues.length > 0 ? allValues.reduce((min, v) => Math.min(min, v), Infinity) : 0;
    const {max: yMax, interval: yInterval, min: yMin} = getNiceAxis(dataMax, dataMin, 5);
    const option = {
        tooltip: {
            trigger: "axis",
            formatter: params => {
                const sortedParams = params.sort((a, b) => {
                    if (b.value[1] !== a.value[1]) {
                        return b.value[1] - a.value[1];
                    }
                    return (a.data.index_value ?? 0) - (b.data.index_value ?? 0);
                });
                let content = `
                <div style="width: 100%; margin: 0; padding: 0;">
                    <div style="background-color: #F8FAFB; width: calc(100% + 22px); padding: 5px;padding-left:14px; margin: -11px -12px 10px -11px;border-bottom: 1px solid #F2F2F2;">
                        <div style="font-size: 16px;front-weight: 600 ; color: #212519">${params[0].name}</div>
                    </div>
            `;
                sortedParams.forEach(item => {
                    content += `
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div style="display: flex; align-items: center;">
                            <span style="display:inline-block;margin-right:5px;border-radius:1px;width:12px;height:12px;background-color:${item.color};"></span>
                            <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">${item.seriesName}</span>
                          </div>
                          <span style="margin-left: 20px;front-weight: 400; font-size: 14px; color: #212519">${item.value[1]}</span>
                        </div>
                    `;
                });
                content += `</div>`;

                return content;
            },
            position(pos, params, el, elRect, size) {
                const obj = {};
                const [x, y] = pos;
                const tooltipWidth = el.getBoundingClientRect().width;
                const parentRect = el.parentElement.getBoundingClientRect();
                const rightSpace = parentRect.width - x;
                if (y > window.innerHeight / 2) {
                    obj.bottom = "30px";
                    delete obj.top;
                }
                if (rightSpace < x - 10 - tooltipWidth) {
                    obj.left = `${x - tooltipWidth - 10}px`;
                } else {
                    obj.left = `${x + 10}px`;
                }

                return obj;
            }
        },
        legend: {
            data: chartData.map(item => item.name),
            orient: "horizontal", // 设置图例的方向为水平
            top: "88%", // 设置图例的垂直位置
            left: "center", // 设置图例的水平位置
            right: "5%",
            textStyle: {
                // 图例文字样式
                fontSize: 15
            },
            itemWidth: 10, // 图例图形的宽度
            itemHeight: 10, // 图例图形的高度
            type: "scroll",
            pageIconColor: "#A2ACB2", // 默认可点击色值
            pageIconInactiveColor: "#E3E5EB", // 不可点击色值
            width: "95%",
            icon: "rect"
        },
        grid: {
            left: "3%",
            right: "3%",
            top: "5%",
            bottom: "10%",
            containLabel: true,
            width: "95%",
            height: "75%"
        },
        xAxis: {
            type: "category",
            data: xAxisData,
            axisLabel: {
                interval: xAxisInterval,
                formatter(value) {
                    const date = new Date(value);
                    const startDate = new Date(timeRange[0] || Date.now() - 5 * 60 * 1000);
                    const endDate = new Date(timeRange[1] || Date.now());
                    const hour = date.getHours().toString().padStart(2, "0");
                    const minute = date.getMinutes().toString().padStart(2, "0");
                    const second = date.getSeconds().toString().padStart(2, "0");
                    if (startDate.getMonth() !== endDate.getMonth() || startDate.getDate() !== endDate.getDate()) {
                        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${hour}:${minute}`;
                    }
                    return `${hour}:${minute}:${second}`;
                }
            },
            splitLine: {
                show: true
            }
        },
        yAxis: {
            type: "value",
            max: yMax,
            min: yMin,
            interval: yInterval,
            axisLabel: {
                formatter(value) {
                    if (value > 1e9) {
                        return `${value.toExponential(2)}`;
                    }
                    if (value >= 1000000) {
                        return `${value / 1000000}M`;
                    }
                    if (value >= 1000) {
                        return `${value / 1000}k`;
                    }
                    return value;
                }
            }
        },
        series: chartData.map(item => ({
            name: item.name,
            type: "line",
            data: item.data,
            symbol: "none"
        })),
        width: "100%",
        height: "180px"
    };

    useImperativeHandle(ref, () => ({
        refreshTelemetry: () => {
            // fetchData();
            fetchDataTrue();
        }
    }));

    useEffect(() => {
        // fetchData();
        fetchDataTrue();
    }, [name, timeRange, topK]);
    let label;
    if (type === "temperature") {
        label = <span>{temperatureOptions[name]}</span>;
    } else if (type === "usage") {
        label = <span>{usageOptions[name]}</span>;
    } else if (type === "interface") {
        label = <span>{interfaceOptions[name]}</span>;
    } else {
        label = <span>{name}</span>;
    }
    return (
        <Card
            title={
                <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                    {label}
                    <Select
                        style={{width: 120}}
                        onChange={value => {
                            setTopK(parseInt(value));
                        }}
                        defaultValue="5"
                    >
                        <Option value="5">Top 5</Option>
                        <Option value="10">Top 10</Option>
                        <Option value="25">Top 25</Option>
                    </Select>
                </div>
            }
            bordered={false}
            style={{
                height: "350px",
                width: "100%",
                ...(cardstyle ?? {})
            }}
            className="linechart"
        >
            {option.series.length === 0 ? (
                <div style={{display: "flex", justifyContent: "center", alignItems: "center"}}>
                    <Empty image={EmptyPic} description="No Data" imageStyle={{display: "block", margin: 0}} />
                </div>
            ) : (
                <CustomLineChart chartOption={option} />
            )}
        </Card>
    );
});

export default SnmpVisualizationView;
