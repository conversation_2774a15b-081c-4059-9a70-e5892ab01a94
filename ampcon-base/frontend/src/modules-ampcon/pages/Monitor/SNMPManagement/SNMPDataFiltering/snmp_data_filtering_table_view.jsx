import {Button, Card, Checkbox, Divider, Flex, Modal, Select, Space} from "antd";
import {AmpConCustomTable, createColumnConfig} from "@/modules-ampcon/components/custom_table";
import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from "react";
import styles from "./snmp_data_filtering_table_view.module.scss";
import ExpandIcon from "@/modules-ampcon/components/expand_icon";
import {getSnmpViewData} from "@/modules-ampcon/apis/dashboard_api";
import Icon from "@ant-design/icons";
import {exclamationSvg, offlineSvg, onlineSvg} from "@/utils/common/iconSvg";
import {getSnmpDataFilteringData} from "@/modules-ampcon/apis/monitor_api";

/**
 * 格式化字节单位 (B, KB, MB, GB)
 * @param {number} bytes - 字节数
 * @param {number} [decimals=2] - 保留的小数位数
 * @returns {string} 格式化后的字符串（如 "2.56MB"）
 */
function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return "0 B";

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ["B", "KB", "MB", "GB"];

    // 计算最合适的单位指数
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    // 格式化数值并拼接单位
    return `${parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`;
}

/**
 * 格式化比特率单位 (bps, Kbps, Mbps, Gbps)
 * @param {number} bps - 比特率（比特每秒）
 * @param {number} [decimals=2] - 保留的小数位数
 * @returns {string} 格式化后的字符串（如 "1.25Gbps"）
 */
function formatBps(bps, decimals = 2) {
    if (bps === 0) return "0bps";
    const k = 1000;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ["bps", "Kbps", "Mbps", "Gbps"];
    // 计算最合适的单位指数
    const i = Math.floor(Math.log(bps) / Math.log(k));
    // 格式化数值并拼接单位
    return `${parseFloat((bps / k ** i).toFixed(dm))} ${sizes[i]}`;
}

function formatTime(seconds) {
    const d = Math.floor(seconds / (24 * 60 * 60));
    const h = Math.floor((seconds % (24 * 60 * 60)) / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    const s = seconds % 60;

    let result = [];
    if (d > 0) result.push(`${d}d`);
    if (h > 0) result.push(`${h}h`);
    if (m > 0) result.push(`${m}m`);
    if (s > 0 || result.length === 0) result.push(`${s}s`);

    return result.join(" ");
}

const WORD_DICT = ["Power", "But", "Abnormal", "Normal", "On", "Off", "Error", "Warning", "Active", "Inactive"];
function formatStatus(str) {
    if (!str) return "";

    // 先处理连字符，用空格替代
    str = str.replace(/-/g, " ");

    let result = [];
    let temp = "";
    for (let char of str) {
        if (char === " ") {
            if (temp) result.push(temp);
            temp = "";
        } else {
            temp += char;
            // 检查 temp 是否是字典中的单词
            const match = WORD_DICT.find(word => word.toLowerCase() === temp.toLowerCase());
            if (match) {
                result.push(match);
                temp = "";
            }
        }
    }

    if (temp) result.push(temp);

    // 对最终所有单词首字母大写
    return result.map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(" ");
}

/**
 * itemName数据格式化map
 */
const processItemNameUnit = {
    system_uptime: v => formatTime(v),
    cpu_utilization_5sec: v => `${v} %`,
    device_temperature: v => `${v} °C`,
    memory_used: v => `${v} %`,
    memory_free: v => formatBytes(v),
    memory_size: v => formatBytes(v),
    flash_used: v => formatBytes(v),
    flash_free: v => formatBytes(v),
    flash_size: v => formatBytes(v),
    fan_speed: (v, unit) => {
        if (unit === "rpm") unit = "RPM";
        return `${v} ${unit}`;
    },
    optical_module_temperature: v => `${v} °C`,
    rx_power: v => `${v} dBm`,
    tx_power: v => `${v} dBm`,
    bias_current: v => `${v} mA`,
    bias_voltage: v => `${v} V`,
    poe_power: v => `${v} W`,
    port_speed: v => formatBps(v),
    bits_sent: v => formatBytes(v),
    bits_received: v => formatBytes(v),
    fan_status: v => formatStatus(v),
    supply_status: v => formatStatus(v),
    operational_status: v => formatStatus(v)
};

/**
 * 处理筛选和排序的工具函数
 */
const processTableRawData = (rawData, sortFields, searchFields) => {
    // 1. 筛选逻辑：根据searchFields过滤数据
    let filteredData = [...rawData];
    const {fields: searchFieldsList, value: searchValue} = searchFields;
    if (searchValue && searchFieldsList?.length) {
        const lowerSearchValue = String(searchValue).toLowerCase(); // 统一转为小写，忽略大小写
        filteredData = filteredData.filter(item => {
            // 检查item是否有任一搜索字段包含搜索值
            return searchFieldsList.some(field => {
                if (!Object.prototype.hasOwnProperty.call(item, field)) return false; // 字段不存在则不匹配
                const itemValue = String(item[field]).toLowerCase(); // 字段值转为字符串并小写
                return itemValue.includes(lowerSearchValue);
            });
        });
    }

    // 2. 排序逻辑：根据sortFields对筛选后的数据排序
    const sortedData = [...filteredData];
    if (sortFields.length) {
        sortedData.sort((a, b) => {
            // 按sortFields顺序依次比较，前序字段相等时才比较后序字段
            for (const sort of sortFields) {
                const {field, order} = sort;
                const aVal = a[field];
                const bVal = b[field];

                // 处理值为null/undefined的情况（统一排在后面）
                if (aVal === null || aVal === undefined) return 1;
                if (bVal === null || bVal === undefined) return -1;

                // 比较逻辑（区分数字和字符串类型）
                let compareResult;
                if (typeof aVal === "number" && typeof bVal === "number") {
                    compareResult = aVal - bVal; // 数字直接相减
                } else {
                    // 字符串按 locale 规则比较（支持多语言，忽略大小写）
                    compareResult = String(aVal).localeCompare(String(bVal), undefined, {
                        sensitivity: "base" // 忽略大小写和重音符号
                    });
                }

                // 根据排序方向调整结果
                if (compareResult !== 0) {
                    return order === "asc" ? compareResult : -compareResult;
                }
            }
            return 0; // 所有排序字段都相等时保持原顺序
        });
    }

    return sortedData;
};

const SelectSwitchModal = forwardRef(({selectedRowKeys, setSelectedRowKeys}, ref) => {
    const [isModalOpen, setIsModalOpen] = useState(false);

    const tableRef = useRef(null);

    const columns = [
        createColumnConfig("Sysname", "sysname", null),
        createColumnConfig("SN", "sn", null),
        createColumnConfig("Model", "model", null),
        {
            ...createColumnConfig("Version", "version", null),
            sorter: null
        },
        {
            ...createColumnConfig("Mgmt IP", "mgt_ip", null),
            sorter: null,
            render: (ip, record) => {
                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }
                return (
                    <Space style={{display: "flex"}} align="middle">
                        <div style={{height: "100%", display: "flex", alignItems: "center"}}>
                            <Icon component={iconComponent} style={{width: "16px"}} />
                        </div>
                        <div style={{height: "100%", display: "flex", alignItems: "center"}}>{ip}</div>
                    </Space>
                );
            }
        }
    ];

    useImperativeHandle(ref, () => ({
        open: () => {
            setIsModalOpen(true);
            tableRef.current?.refreshTable(true);
            tableRef.current?.setSelectedRows(selectedRowKeys, []);
        },
        getTableData: () => {
            return tableRef.current?.getTableData();
        }
    }));

    const handleApplySelectSwitch = () => {
        const {tableSelectedRowKey} = tableRef.current.getSelectedRow();
        setSelectedRowKeys(tableSelectedRowKey);
        setIsModalOpen(false);
    };

    const handleCancelModal = () => {
        setIsModalOpen(false);
    };

    return (
        <Modal
            className="ampcon-max-modal"
            onCancel={() => {
                handleCancelModal();
            }}
            open={isModalOpen}
            title={
                <>
                    <div>Select Switch</div>
                    <Divider style={{marginBottom: 0}} />
                </>
            }
            footer={
                <Flex vertical>
                    <Divider />
                    <Flex justify="flex-end">
                        <Button
                            htmlType="button"
                            onClick={() => {
                                handleCancelModal();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={() => handleApplySelectSwitch()}>
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <AmpConCustomTable
                ref={tableRef}
                columns={columns}
                matchFieldsList={[{name: "mgt_ip", matchMode: "fuzzy"}]}
                rowSelection={{
                    selectedRowKeys
                }}
                preSavedSelectedRowKeys
                searchFieldsList={["sysname", "sn", "model", "version", "mgt_ip"]}
                fetchAPIInfo={getSnmpViewData}
            />
        </Modal>
    );
});

const ExpandedRowTable = ({record, refreshCountdown}) => {
    const expandedRowDataFetch = async (_, page, pageSize, filterFields, sortFields = [], searchFields = {}) => {
        const processedData = processTableRawData(record.children, sortFields, searchFields);
        const newData = processedData.map(row => {
            let newValue = row.value;

            if (row.value && row.value !== "--" && processItemNameUnit[record.item_name]) {
                newValue = processItemNameUnit[record.item_name](newValue, row.unit);
            }
            return {
                ...row,
                value: newValue
            };
        });
        return {
            status: 200,
            data: newData
        };
    };
    const expandColumns = [
        {
            title: "",
            width: "30vw"
        },
        {...createColumnConfig("Item Name", "item_name", null), width: "15vw"},
        {
            ...createColumnConfig("Refresh Countdown (s)", "refresh_countdown", null),
            width: "15vw",
            sorter: null,
            render: () => {
                return <div>{refreshCountdown}</div>;
            }
        },
        {
            ...createColumnConfig("Current Value", "value", null),
            render: value => {
                return <div>{value ?? "--"}</div>;
            }
        }
    ];

    return (
        <AmpConCustomTable
            style={{
                paddingLeft: 33,
                paddingRight: 30,
                paddingBottom: 20,
                backgroundColor: "#f7f7f7"
            }}
            fetchAPIInfo={expandedRowDataFetch}
            fetchAPIParams={[refreshCountdown]}
            columns={expandColumns}
            isShowPagination={false}
        />
    );
};

const itemNameOptions = [
    {label: "All Items", value: "all"},
    {label: "Device Location", value: "device_location"},
    {label: "Sysname", value: "sysname"},
    {label: "System Uptime", value: "system_uptime"},
    {label: "Device MAC", value: "device_mac"},
    {label: "Device SW Version", value: "device_sw_version"},
    {label: "Device HW Version", value: "device_hw_version"},
    {label: "Serial Number", value: "serial_number"},
    {label: "CPU Utilization 5Sec", value: "cpu_utilization_5sec"},
    {label: "Device Temperature", value: "device_temperature"},
    {label: "Memory Used", value: "memory_used"},
    {label: "Memory Free", value: "memory_free"},
    {label: "Memory Size", value: "memory_size"},
    {label: "Flash Used", value: "flash_used"},
    {label: "Flash Free", value: "flash_free"},
    {label: "Flash Size", value: "flash_size"},
    {label: "Fan Speed", value: "fan_speed"},
    {label: "Fan Status", value: "fan_status"},
    {label: "Supply Status", value: "supply_status"},
    {label: "Optical Module Temperature", value: "optical_module_temperature"},
    {label: "Rx Power", value: "rx_power"},
    {label: "Tx Power", value: "tx_power"},
    {label: "Bias Current", value: "bias_current"},
    {label: "Bias Voltage", value: "bias_voltage"},
    {label: "PoE Power", value: "poe_power"},
    {label: "Interface Type", value: "interface_type"},
    {label: "Operational Status", value: "operational_status"},
    {label: "Port Speed", value: "port_speed"},
    {label: "In Packets with Errors", value: "in_packets_with_errors"},
    {label: "In Packets Discarded", value: "in_packets_discarded"},
    {label: "Out Packets with Errors", value: "out_packets_with_errors"},
    {label: "Out Packets Discarded", value: "out_packets_discarded"},
    {label: "Bits Sent", value: "bits_sent"},
    {label: "Bits Received", value: "bits_received"}
];
const itemNameMap = itemNameOptions.reduce((acc, option) => {
    acc[option.value] = option.label;
    return acc;
}, {});

const expandIcon = props => <ExpandIcon isTable={false} {...props} />;

const defaultCountDown = 60;

const SnmpDataFilteringPage = () => {
    const selectSwitchModalRef = useRef(null);
    const [selectSwitchItems, setSelectSwitchItems] = useState([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [itemNameSelectedItems, setItemNameSelectedItems] = useState([]);
    const tableDataRef = useRef([]);
    const [countdown, setCountdown] = useState(null);
    const timerRef = useRef(null);
    // 记录哪些行已经显示过，用于定时器运行时保留行
    const displayedRowsRef = useRef(new Set());
    const tableRef = useRef(null);

    const tableColumns = [
        {...createColumnConfig("Sysname", "sysname", null), width: "15vw"},
        {...createColumnConfig("SN", "sn", null), width: "15vw"},
        {
            ...createColumnConfig("Item Name", "item_name", null),
            width: "15vw",
            render: value => {
                return <div>{itemNameMap[value]}</div>;
            }
        },
        {
            ...createColumnConfig("Refresh Countdown (s)", "refresh_countdown", null),
            width: "15vw",
            sorter: null,
            render: () => {
                return <div>{countdown || "--"}</div>;
            }
        },
        {
            ...createColumnConfig("Current Value", "value", null),
            render: value => {
                return <div>{value ?? "--"}</div>;
            }
        }
    ];

    const itemNameOptionRender = oriOption => {
        const isAll = oriOption.value === "all";
        const allCount = itemNameOptions.length - 1;
        const selectedCount = itemNameSelectedItems.length;

        // 复选框状态
        const checked = isAll ? selectedCount === allCount : itemNameSelectedItems.includes(oriOption.value);

        // 部分选中状态
        const indeterminate = isAll && selectedCount > 0 && selectedCount < allCount;

        return (
            <Space>
                <Checkbox checked={checked} indeterminate={indeterminate} />
                <div className={styles.optionLabel}>{oriOption.label}</div>
            </Space>
        );
    };

    const handleDeselectSwitch = v => {
        const newSelectedRowKeys = selectedRowKeys.filter(key => key !== v);
        const newSelectSwitchItems = selectSwitchItems.filter(key => key.id !== v);
        setSelectedRowKeys(newSelectedRowKeys);
        setSelectSwitchItems(newSelectSwitchItems);
    };

    /**
     * 处理itemName变化
     */
    const handleItemNameChange = (value, option) => {
        // 筛选出新的selectItems
        const allClicked = option.find(opt => opt.value === "all");
        let newItemNames;
        if (allClicked) {
            if (itemNameSelectedItems.length === itemNameOptions.length - 1) {
                // 取消 All，清空选择
                newItemNames = [];
            } else {
                // 选中 All，选中所有其他选项
                newItemNames = itemNameOptions.filter(opt => opt.value !== "all").map(opt => opt.value);
            }
        } else {
            newItemNames = value;
        }
        setItemNameSelectedItems(newItemNames);
    };

    const selectSwitchesValues = selectSwitchItems.map(row => ({label: row.sysname, value: row.id}));

    /**
     * 监听RowKeys筛选交换机
     */
    useEffect(() => {
        const switchTableData = selectSwitchModalRef.current?.getTableData();
        if (!switchTableData) {
            return;
        }
        const items = switchTableData.filter(row => selectedRowKeys.includes(row.id));
        setSelectSwitchItems(items);
    }, [selectedRowKeys]);

    /**
     * 初始化默认选择第一个交换机，全选item name
     * 组件卸载时，清理所有定时器, displayedRows
     */
    useEffect(() => {
        const initSetSwitch = async () => {
            const res = await getSnmpViewData(1, 1);
            if (res.status === 200 && res.data && res.data.length) {
                // 初始selectSwitchModalRef中table未加载没数据，需手动设置data
                setSelectedRowKeys([res.data[0].id]);
                setSelectSwitchItems([res.data[0]]);
            }
        };
        initSetSwitch();
        handleItemNameChange("all", [{value: "all"}]);

        // 组件卸载时清除所有定时器
        return () => {
            clearInterval(timerRef.current);
            timerRef.current = null;
            displayedRowsRef.current = new Set();
            tableDataRef.current = [];
        };
    }, []);

    /**
     * 监听设备和itemName，更新定时器，同时清理tableData
     */
    useEffect(() => {
        // 重置displayedRowsRef
        displayedRowsRef.current = new Set();
        // 立即获取一次数据（首次加载会过滤掉value为--的行）
        const deviceIds = selectSwitchItems.map(row => row.id);
        fetchDeviceItemData(deviceIds, itemNameSelectedItems).then(() => {
            // 已存在则清除定时器
            if (timerRef.current) {
                clearInterval(timerRef.current);
                timerRef.current = null;
            }
            // 创建定时器
            timerRef.current = setInterval(async () => {
                // 更新倒计时
                setCountdown(prev => {
                    const current = prev || defaultCountDown;
                    // 当倒计时结束（current <= 1）时，触发请求
                    if (current <= 1) {
                        const deviceIds = selectSwitchItems.map(row => row.id);
                        fetchDeviceItemData(deviceIds, itemNameSelectedItems).then(() => {
                            // 请求完成后重置倒计时
                            setCountdown(defaultCountDown);
                        });
                    }
                    return current - 1;
                });
            }, 1000);
        });
    }, [selectSwitchItems, itemNameSelectedItems]);

    /**
     * 获取指device的itemName的数据, 同时更新tableData数据
     */
    const fetchDeviceItemData = async (deviceIds, itemNames) => {
        tableRef.current?.setTableLoading(true);
        const res = await getSnmpDataFilteringData(deviceIds, itemNames);
        if (res.status === 200) {
            const rows = res.data;
            const newData = [];
            for (const row of rows) {
                if (row.value !== "--" || displayedRowsRef.current.has(row.id) || row.children.length) {
                    displayedRowsRef.current.add(row.id);
                    newData.push(row);
                }
            }
            tableDataRef.current = newData;
        }
        tableRef.current?.setTableLoading(false);
    };

    const processTableData = async (_, page, pageSize, filterFields, sortFields = [], searchFields = {}) => {
        const processedData = processTableRawData(tableDataRef.current, sortFields, searchFields);
        const newData = processedData.map(row => {
            let newValue = row.value;
            if (row.value && row.value !== "--" && processItemNameUnit[row.item_name]) {
                newValue = processItemNameUnit[row.item_name](newValue, row.unit);
            }
            return {
                ...row,
                value: newValue
            };
        });
        return {
            status: 200,
            data: newData
        };
    };

    const expandedRowRender = record => {
        return <ExpandedRowTable record={record} refreshCountdown={countdown} />;
    };

    return (
        <Card style={{display: "flex", flexDirection: "column", flex: 1, border: "none"}}>
            <SelectSwitchModal
                selectedRowKeys={selectedRowKeys}
                setSelectedRowKeys={setSelectedRowKeys}
                ref={selectSwitchModalRef}
            />
            <div>
                <div style={{display: "flex", justifyContent: "space-between", marginTop: "-12px"}}>
                    <h2 style={{margin: "20px 0 20px"}}>SNMP Data Filtering</h2>
                </div>

                <AmpConCustomTable
                    ref={tableRef}
                    extraButton={
                        <Flex>
                            <Space style={{marginRight: "80px"}} size={20}>
                                <span>Switches</span>
                                <Select
                                    suffixIcon={null}
                                    style={{width: "280px"}}
                                    mode="multiple"
                                    open={false}
                                    showSearch={false}
                                    value={selectSwitchesValues}
                                    maxTagCount="responsive"
                                    onDeselect={handleDeselectSwitch}
                                />
                                <Button onClick={() => selectSwitchModalRef.current.open()}>Select</Button>
                            </Space>
                            <Space size={20}>
                                <span>Item Name</span>
                                <Select
                                    mode="multiple"
                                    style={{width: 280}}
                                    options={itemNameOptions}
                                    value={itemNameSelectedItems}
                                    optionRender={itemNameOptionRender}
                                    onChange={handleItemNameChange}
                                    maxTagCount="responsive"
                                    popupClassName={styles.mySelect}
                                />
                            </Space>
                        </Flex>
                    }
                    fetchAPIInfo={processTableData}
                    fetchAPIParams={[tableDataRef.current]}
                    columns={tableColumns}
                    searchFieldsList={["sn", "value", "item_name"]}
                    pagination={false}
                    expandable={{
                        expandedRowRender,
                        expandIcon,
                        childrenColumnName: "dontShowDefaultRow"
                    }}
                />
            </div>
        </Card>
    );
};

export default SnmpDataFilteringPage;
