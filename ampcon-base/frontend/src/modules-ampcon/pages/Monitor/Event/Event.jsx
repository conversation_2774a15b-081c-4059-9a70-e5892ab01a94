import React, {useState, useEffect} from "react";
import {<PERSON>, Modal, But<PERSON>, Divider, Space, Tag, Card, Tabs} from "antd";
import {getRoceTaskTable, getResultInfo} from "@/modules-ampcon/apis/roce_api";
import {
    createColumnConfig,
    createColumnWithoutFilter,
    createColumnConfigMultipleParams,
    TableFilterDropdown,
    AmpConCustomTable,
    TableSelectFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import styles from "./Event.module.scss";

const AIEventPage = () => {
    const tableSearchFieldsList = ["create_time"];
    const matchFieldsList = [
        {name: "create_time", matchMode: "fuzzy"}
        // {name: "device_time", matchMode: "fuzzy"}
        // {name: "device_ip", matchMode: "fuzzy"},
        // {name: "device_user", matchMode: "fuzzy"},
        // {name: "create_time", matchMode: "fuzzy"}
    ];

    const [modalVisible, setModalVisible] = useState(false);
    const [modalContent, setModalContent] = useState([]);
    const [loading, setLoading] = useState(false);

    // 获取 Host 详情信息
    const handleDetailClick = async record => {
        setLoading(true);
        setModalVisible(true);
        try {
            const response = await getResultInfo(record.id);

            if (response.status === 200) {
                const errorPattern = /error|failed|reboot/i;
                const processedData = response.data.split("\n").map(line => {
                    const isProgramFailed = line.includes("Failed to execute scripts");
                    return {
                        content: line,
                        isError: !isProgramFailed && errorPattern.test(line.trim()) // 检查是否包含关键词
                    };
                });

                setModalContent(processedData);
            } else {
                setModalContent([{content: "Failed to load data.", isError: true}]);
            }
        } catch (error) {
            setModalContent([{content: "Failed to load data.", isError: true}]);
            console.error("Error fetching host info:", error);
        }
        setLoading(false);
    };
    const filterOptions = [
        {
            label: "Success",
            value: "success"
        },
        {
            label: "Failed",
            value: "failed"
        },
        {
            label: "Running",
            value: "running"
        }
    ];

    const columns = [
        createColumnConfig("Sysname", "device_name", TableFilterDropdown),
        createColumnConfig("Device IP", "device_ip", TableFilterDropdown),
        createColumnConfig("Device User", "device_user", TableFilterDropdown),
        createColumnConfig("Create Time", "create_time", TableFilterDropdown),
        {
            title: "Type",
            dataIndex: "type"
        },
        {
            title: "Status",
            dataIndex: "status",
            filterDropdown: props => TableSelectFilterDropdown({...props, filterOptions}),
            onFilter: () => true,
            sorter: (a, b) => a.status.localeCompare(b.status),
            render: (_, record) => {
                const status = String(record.status || "").toLowerCase();
                return (
                    <div>
                        <Space size="small">
                            {status === "success" && <Tag className={styles.successTag}>Success</Tag>}
                            {status === "failed" && <Tag className={styles.failedTag}>Failed</Tag>}
                            {status === "running" && <Tag className={styles.runningTag}>Running</Tag>}
                        </Space>
                    </div>
                );
            }
        },
        {
            title: "Operation",
            dataIndex: "operation",
            render: (_, record) => (
                <Button type="link" onClick={() => handleDetailClick(record)} style={{marginLeft: "-16px"}}>
                    Detail
                </Button>
            )
        }
    ];

    return (
        <div style={{minHeight: "100%"}}>
            <AmpConCustomTable
                fetchAPIInfo={getRoceTaskTable}
                columns={columns}
                searchFieldsList={tableSearchFieldsList}
                matchFieldsList={matchFieldsList}
            />
            <Modal
                title={
                    <div>
                        <span>Result Detail</span>
                        <Divider
                            style={{
                                margin: "8px 0",
                                width: "calc(100% + 48px)",
                                marginLeft: "-24px"
                            }}
                        />
                    </div>
                }
                open={modalVisible}
                onCancel={() => setModalVisible(false)}
                width={900}
                footer={[
                    <div style={{width: "100%"}}>
                        {/* <Divider
                            style={{
                                marginTop: "0px",
                                marginBottom: 20,
                                width: "calc(100% + 48px)",
                                marginLeft: "-24px"
                            }}
                        /> */}
                        {/* <Space>
                            <Button key="cancel" onClick={() => setModalVisible(false)}>
                                Cancel
                            </Button>
                        </Space> */}
                    </div>
                ]}
            >
                <div className={styles.detail_body}>
                    <pre style={{whiteSpace: "pre-wrap", wordBreak: "break-word"}}>
                        {loading
                            ? "Loading..."
                            : modalContent.map((line, index) => (
                                  <div key={index} style={{color: line.isError ? "red" : "inherit"}}>
                                      {line.content}
                                  </div>
                              ))}
                    </pre>
                </div>
            </Modal>
        </div>
    );
};

export default AIEventPage;
