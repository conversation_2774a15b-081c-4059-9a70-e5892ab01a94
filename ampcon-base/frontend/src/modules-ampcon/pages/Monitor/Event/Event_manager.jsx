import React, {useEffect, useState, useMemo} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import {useSelector} from "react-redux";
import ProtectedRoute from "@/modules-ampcon/utils/util";
import AIEventPage from "./Event";

let items = [
    {
        key: "ai_event",
        label: "AI Event",
        children: <ProtectedRoute component={AIEventPage} />
    }
];
const EventManager = () => {
    const currentUser = useSelector(state => state.user.userInfo);

    // items = currentUser.type === "readonly" ? items.filter(item => item.key !== "ai_event") : items;

    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();

    useEffect(() => {
        const currentPath = location.pathname;
        if (/(ai_event)$/.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(/(ai_event)$/)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (/(ai_event)$/.test(currentPath)) {
            const matchLength = currentPath.match(/(ai_event)$/)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        <div style={{display: "flex", flex: 1}}>
            <Tabs
                style={{flex: 1}}
                activeKey={currentActiveKey}
                items={items}
                onChange={onChange}
                destroyInactiveTabPane
            />
        </div>
    );
};

export default EventManager;
