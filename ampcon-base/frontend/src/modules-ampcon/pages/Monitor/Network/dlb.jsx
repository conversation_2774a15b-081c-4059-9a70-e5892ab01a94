import React, {useState, useEffect, useRef} from "react";
import {Form, Tabs, Row, Col, message} from "antd";
import {CustomSelect} from "@/modules-ampcon/components/custom_form";
import {fetchAllSwitch} from "@/modules-ampcon/apis/dashboard_api";
import {CustomTelemetryCard} from "@/modules-ampcon/components/echarts_common";
import {fetchDLBTopK, fetchInterfaceList, getDlbTableAPI} from "@/modules-ampcon/apis/monitor_api";
import {AmpConCustomTelemteryTable} from "@/modules-ampcon/components/custom_table";
import {TelemetryDateRangePicker} from "@/modules-ampcon/components/telemetry_date_range_picker";
import styles from "./dlb.module.scss";

const dlbCardOptions = {
    "Bandwidth Utilization (Output)": "out-bindwidth-utilization",
    "Bandwidth Utilization (Input)": "in-bindwidth-utilization",
    "Packet Loss Rate (Output)": "out-packet-loss-rate",
    "Packet Loss Rate (Input)": "in-packet-loss-rate",
    "Throughput (Output)": "out-throughput",
    "Throughput (Input)": "in-throughput"
};

const DLB = () => {
    const [timeRange, setTimeRange] = useState([null, null]);
    const [switchSN, setSwitchSN] = useState("");
    const [switchSNList, setSwitchSNList] = useState([]);
    const [activeKey, setActiveKey] = useState("Performance Trend");

    const handleSelectChange = value => {
        setSwitchSN(value);
    };
    const items = [
        {
            key: "Performance Trend",
            label: "Performance Trend",
            children: (
                <PerformanceTrend
                    active={activeKey === "Performance Trend"}
                    sn={switchSN}
                    timeRange={timeRange}
                    setTimeRange={setTimeRange}
                />
            )
        },
        {
            key: "Real-time Statistics",
            label: "Real-time Statistics",
            children: <RealTimeStatistics active={activeKey === "Real-time Statistics"} sn={switchSN} />
        }
    ];

    const fetchSwitchList = async () => {
        let response = await fetchAllSwitch(1, 10, [], [], {});
        if (response.status === 200) {
            let allData = response.data;
            if (response.total > 10) {
                response = await fetchAllSwitch(1, response.total, [], [], {});
                if (response.status === 200) {
                    allData = response.data;
                }
            }
            const snList = allData.map(item => item.sn);
            setSwitchSNList(snList);
        }
    };

    useEffect(() => {
        fetchSwitchList().then();
    }, []);

    return (
        <div>
            <Form layout="inline" style={{flexWrap: "nowrap", marginBottom: "30px"}}>
                <Form.Item
                    name="sn"
                    label="SN/Service Tag"
                    required="true"
                    wrapperCol={{style: {marginLeft: 20, width: 280}}}
                >
                    <CustomSelect onChange={handleSelectChange} options={switchSNList} />
                </Form.Item>
            </Form>
            <div className={styles.dlbTab}>
                {/* <Tabs onChange={setActiveKey} items={items} /> */}
                <Tabs
                    defaultActiveKey="Performance Trend"
                    onChange={setActiveKey}
                    activeKey={activeKey}
                    tabBarExtraContent={
                        activeKey === "Performance Trend" ? (
                            <div style={{display: "flex", alignItems: "center", gap: 16}}>
                                <span>Time (UTC-0)</span>
                                <TelemetryDateRangePicker timeRange={timeRange} setTimeRange={setTimeRange} />
                            </div>
                        ) : null
                    }
                    items={items}
                />
            </div>
        </div>
    );
};

const PerformanceTrend = ({active, sn, timeRange}) => {
    // const [timeRange, setTimeRange] = useState(["", ""]);
    const [interfaceList, setInterfaceList] = useState([]);
    const cardRefs = useRef({});

    const addToRefs = (el, name) => {
        if (el) {
            cardRefs.current[name] = el;
        }
    };

    useEffect(() => {
        fetchInterfaceList(sn).then(rs => {
            if (rs.status === 200) {
                if (rs.data.length > 0) {
                    const interfaceList = {
                        title: "All",
                        value: "all",
                        children: rs.data.map(item => ({
                            title: item,
                            value: item
                        }))
                    };
                    setInterfaceList([interfaceList]);
                }
            }
        });
    }, [sn]);

    useEffect(() => {
        if (active) {
            Object.keys(cardRefs.current).forEach(key => {
                const ref = cardRefs.current[key];
                if (ref && ref.refreshTelemetry) {
                    ref.refreshTelemetry();
                }
            });
        }
    }, [active]);

    const fetchApi = async (label, finalTopK, target, timeRange, selectedInterface) => {
        let chartData = [];
        let xAxisData = [];
        if (target) {
            const response = await fetchDLBTopK(
                dlbCardOptions[label],
                finalTopK,
                target,
                timeRange[0],
                timeRange[1],
                selectedInterface
            );
            if (response.status !== 200) {
                message.error(response.info);
            } else if (response.data.length > 0) {
                chartData = response.data.map(item => {
                    const name = `${item.interface_name}`;
                    return {
                        name,
                        data: item.values.map(([x, y]) => [x, y])
                    };
                });
                xAxisData = Array.from(new Set(response.data.flatMap(item => item.values.map(([x]) => x)))).sort();
            }
        }
        return {
            chartData,
            xAxisData
        };
    };

    return (
        <div>
            <div
                style={{
                    width: "100%",
                    margin: "0px 0px 24px 0px"
                }}
            >
                <Row gutter={[24, 24]}>
                    {Object.keys(dlbCardOptions).map((item, index) => (
                        <Col key={index} span={24} xxl={12} style={{display: "flex", justifyContent: "center"}}>
                            <CustomTelemetryCard
                                ref={el => addToRefs(el, item)}
                                label={item}
                                timeRange={timeRange}
                                fetchApi={fetchApi}
                                target={sn}
                                filterList={interfaceList}
                                cardstyle={{
                                    borderColor: "#E7E7E7",
                                    borderWidth: "1px",
                                    borderStyle: "solid",
                                    boxShadow: "none"
                                }}
                            />
                        </Col>
                    ))}
                </Row>
            </div>
        </div>
    );
};

const getSorter = (dataIndex, isString) => {
    return isString ? (a, b) => a[dataIndex].localeCompare(b[dataIndex]) : (a, b) => a[dataIndex] - b[dataIndex];
};
const RealTimeStatistics = ({active, sn}) => {
    // "abc-123/456/789" 的字符串中提取前缀 "abc-" 和数字 [123, 456, 789]
    const extractParts = str => {
        const match = str.match(/^([A-Za-z-]+)-(\d+)\/(\d+)\/(\d+)$/);
        if (!match) {
            return {prefix: str, numbers: []};
        }
        const prefix = match[1];
        const numbers = match.slice(2).map(num => parseInt(num, 10));
        return {prefix, numbers};
    };

    // 比较前缀以及对应位数字
    const compareNumbers = (a, b) => {
        const aParts = extractParts(a);
        const bParts = extractParts(b);
        if (aParts.prefix !== bParts.prefix) {
            return aParts.prefix.localeCompare(bParts.prefix);
        }
        for (let i = 0; i < Math.min(aParts.numbers.length, bParts.numbers.length); i++) {
            if (aParts.numbers[i] !== bParts.numbers[i]) {
                return aParts.numbers[i] - bParts.numbers[i];
            }
        }
        return aParts.numbers.length - bParts.numbers.length;
    };

    const [formattedData, setFormattedData] = useState([]);
    const formatNumber = value => (value > 1e8 ? value.toExponential(3) : value || 0);

    const portColumns = [
        {title: "Port Name", dataIndex: "name", isString: true, sorter: false},
        {title: "5sec input rate", dataIndex: "in_pkts_rate", sorter: true},
        {title: "5sec output rate", dataIndex: "out_pkts_rate", sorter: true},
        {title: "input Total Packets Without Errors", dataIndex: "in_pkts", sorter: true},
        {title: "output Total Packets Without Errors", dataIndex: "out_pkts", sorter: true},
        {title: "intput Total Packets With Errors", dataIndex: "in_errors", sorter: true},
        {title: "output Total Packets With Errors", dataIndex: "out_errors", sorter: true}
    ];
    const columns = portColumns.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const fetchData = async () => {
        const response = await getDlbTableAPI(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const formattedData = Object.keys(response.data).map(key => {
                const port = response.data[key];
                return {
                    name: key,
                    in_pkts_rate: formatNumber(port?.in_pkts_rate),
                    out_pkts_rate: formatNumber(port?.out_pkts_rate),
                    in_pkts: formatNumber(port ? port.in_pkts - port.in_errors : "-"),
                    out_pkts: formatNumber(port ? port.out_pkts - port.out_errors : "-"),
                    in_errors: formatNumber(port?.in_pkts),
                    out_errors: formatNumber(port?.out_pkts)
                };
            });
            formattedData.sort((a, b) => compareNumbers(a.name, b.name));
            setFormattedData(formattedData);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return (
        <AmpConCustomTelemteryTable
            columnsConfig={columns}
            data={formattedData}
            // tableWidth={tableWidth}
            showSetting={false}
        />
    );
};
export default DLB;
