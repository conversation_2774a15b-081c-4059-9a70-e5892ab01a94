import {forwardRef, useImperativeHandle, useState} from "react";
import {Button, Divider, Form, Input, message, Modal} from "antd";
import {viewEmailDetail} from "@/modules-ampcon/apis/email_api";

const EmailDetailModal = forwardRef((props, ref) => {
    const title = "Email Detail";

    useImperativeHandle(ref, () => ({
        showEmailDetailModal: emailLogId => {
            viewEmailDetail({emailLogId}).then(response => {
                if (response.status === 200) {
                    setHtmlContent(response.data);
                    setIsShowModal(true);
                } else {
                    message.error("Failed to get email detail");
                }
            });
        },
        hideEmailDetailModal: () => {
            setIsShowModal(false);
        }
    }));

    const [isShowModal, setIsShowModal] = useState(false);
    const [htmlContent, setHtmlContent] = useState("");

    return isShowModal ? (
        <Modal
            className="ampcon-email-viewer-modal"
            title={
                <div>
                    {title}
                    <Divider style={{margin: "8px 0px -7px -24px", width: "calc(100% + 48px)"}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
            }}
            footer={null}
        >
            {/* eslint-disable-next-line react/no-danger */}
            <div dangerouslySetInnerHTML={{__html: htmlContent}} />
        </Modal>
    ) : null;
});

export default EmailDetailModal;
