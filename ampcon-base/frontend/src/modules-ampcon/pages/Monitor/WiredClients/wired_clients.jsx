import {But<PERSON>, <PERSON>, message, Space, Tag} from "antd";
import {
    AmpConCustomTable,
    createColumnConfigMultipleParams,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import Icon from "@ant-design/icons";
import {addSvg} from "@/utils/common/iconSvg";
import {useRef, useState, useEffect} from "react";
import {deleteClient, fetchClientsInfo, getClientsSelectInfo} from "@/modules-ampcon/apis/monitor_api";
import CreateClientModal from "@/modules-ampcon/pages/Monitor/WiredClients/create_client_modal";
import EditClientModal from "@/modules-ampcon/pages/Monitor/WiredClients/edit_client_modal";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import styles from "@/modules-ampcon/pages/Topo/Topology/topo.module.scss";
import {useNavigate} from "react-router-dom";

const tagStyle = state => {
    if (state === "online") {
        return <Tag className={styles.UpStyle}>Online</Tag>;
    }
    return <Tag className={styles.DownStyle}>Offline</Tag>;
};

const WiredClients = () => {
    const [editingRecord, setEditingRecord] = useState(null);
    const [terminalTypes, setTerminalTypes] = useState([]);
    const [manufacturers, setManufacturers] = useState([]);

    const columns = [
        createColumnConfigMultipleParams({
            title: "Client Name",
            dataIndex: "client_name",
            filterDropdownComponent: TableFilterDropdown,
            width: "10%"
        }),
        createColumnConfigMultipleParams({
            title: "Status",
            dataIndex: "state",
            filterDropdownComponent: TableFilterDropdown,
            width: "7%",
            render: state => tagStyle(state)
        }),
        createColumnConfigMultipleParams({
            title: "MAC Address",
            dataIndex: "mac_address",
            filterDropdownComponent: TableFilterDropdown,
            width: "10%"
        }),
        createColumnConfigMultipleParams({
            title: "IP Address",
            dataIndex: "ip_address",
            filterDropdownComponent: TableFilterDropdown,
            width: "10%"
        }),
        createColumnConfigMultipleParams({
            title: "Switch SN",
            dataIndex: "switch_sn",
            filterDropdownComponent: TableFilterDropdown,
            width: "10%",
            render: (_, record) => {
                if (record.is_switch_exist) {
                    return (
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    navigate(`/device/switches/${record.switch_sn}`);
                                }}
                            >
                                {record.switch_sn}
                            </a>
                        </Space>
                    );
                }
                return record.switch_sn;
            }
        }),
        createColumnConfigMultipleParams({
            title: "Port",
            dataIndex: "port",
            filterDropdownComponent: TableFilterDropdown,
            width: "10%"
        }),
        createColumnConfigMultipleParams({
            title: "Manufacturer",
            dataIndex: "manufacturer",
            filterDropdownComponent: TableFilterDropdown,
            width: "13%"
        }),
        createColumnConfigMultipleParams({
            title: "Terminal Type",
            dataIndex: "terminal_type",
            filterDropdownComponent: TableFilterDropdown,
            width: "10%"
        }),
        createColumnConfigMultipleParams({
            title: "Up/Down Time",
            dataIndex: "update_time",
            enableFilter: false,
            enableSorter: true,
            filterDropdownComponent: TableFilterDropdown,
            width: "10%"
        }),
        {
            title: "Operation",
            width: "10%",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    editClientModalRef.current.showEditClientModal(record);
                                    setEditingRecord(record);
                                }}
                            >
                                Edit
                            </a>
                            {record.state !== "online" ? (
                                <a
                                    onClick={() => {
                                        confirmModalAction("Are you sure you want to delete the client?", () => {
                                            deleteClient({clientId: record.id}).then(res => {
                                                if (res.status === 200) {
                                                    message.success(res.info);
                                                    tableRef.current.refreshTable();
                                                } else {
                                                    message.error("Failed to delete record");
                                                }
                                            });
                                        });
                                    }}
                                >
                                    Delete
                                </a>
                            ) : null}
                        </Space>
                    </div>
                );
            }
        }
    ];

    const searchFieldsList = [
        "client_name",
        "mac_address",
        "manufacturer",
        "ip_address",
        "switch_sn",
        "state",
        "terminal_type"
    ];
    const matchFieldsList = [
        {name: "client_name", matchMode: "fuzzy"},
        {name: "mac_address", matchMode: "fuzzy"},
        {name: "manufacturer", matchMode: "fuzzy"},
        {name: "ip_address", matchMode: "fuzzy"},
        {name: "switch_sn", matchMode: "fuzzy"},
        {name: "state", matchMode: "fuzzy"},
        {name: "terminal_type", matchMode: "fuzzy"}
    ];

    const tableRef = useRef();
    const createClientModalRef = useRef();
    const editClientModalRef = useRef();
    const navigate = useNavigate();

    const fetchClientSelectData = async () => {
        try {
            const response = await getClientsSelectInfo();
            if (response.status === 200) {
                setTerminalTypes(response.terminalType || []);
                setManufacturers(response.manufacturers || []);
            }
        } catch (error) {
            message.error("An error occurred while fetching client info data.");
        }
    };

    useEffect(() => {
        fetchClientSelectData();
    }, []);

    return (
        <Card style={{display: "flex", flex: 1}}>
            <CreateClientModal
                ref={createClientModalRef}
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
                terminalTypes={terminalTypes}
                setTerminalTypes={setTerminalTypes}
            />
            <EditClientModal
                ref={editClientModalRef}
                isOnline={
                    editingRecord?.state === "online" &&
                    Boolean(editingRecord?.ip_address && editingRecord?.ip_source === "dhcp")
                }
                saveCallback={() => {
                    tableRef.current.refreshTable();
                }}
                terminalTypes={terminalTypes}
                setTerminalTypes={setTerminalTypes}
                manufacturers={manufacturers}
                setManufacturers={setManufacturers}
            />
            <h2 style={{margin: "8px 0 20px"}}>Wired Clients</h2>
            <AmpConCustomTable
                ref={tableRef}
                columns={columns}
                fetchAPIInfo={fetchClientsInfo}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={
                    <Button
                        type="primary"
                        onClick={() => {
                            createClientModalRef.current.showCreateClientModal();
                        }}
                    >
                        <Icon component={addSvg} />
                        Wired Client
                    </Button>
                }
            />
        </Card>
    );
};
export default WiredClients;
