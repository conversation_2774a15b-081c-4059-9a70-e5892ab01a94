import {request} from "@/utils/common/request";

const baseURL = "/ampcon";
const fmtUrl = `${baseURL}/fmt`;
const dcsUrl = `${baseURL}/dcs`;

// bypass database in backend
export function queryFMTInfo(ip) {
    return request({
        url: `${fmtUrl}/info/query`,
        method: "GET",
        params: {
            ip
        }
    });
}

// query database in backend
export function getFMTInfo(ip) {
    return request({
        url: `${fmtUrl}/info/get`,
        method: "GET",
        params: {
            ip
        }
    });
}

export function modifyFMTConfig(data) {
    return request({
        url: `${fmtUrl}/config/modify`,
        method: "PUT",
        data
    });
}

export function modifyFMTPortNote(data) {
    return request({
        url: `${fmtUrl}/config/set_note`,
        method: "POST",
        data
    });
}

export function queryFMTConfig(ip, slotIndex = null, cardId = null) {
    return request({
        url: `${fmtUrl}/config/query`,
        method: "GET",
        params: {
            ip,
            slotIndex,
            cardId
        }
    });
}

export function getFMTDeviceCard(filterCard, {ip}) {
    return request({
        url: `${fmtUrl}/get_fmt_device_card`,
        method: "POST",
        data: {
            ip,
            filterCard
        }
    });
}

export function getFMTDevicePort(id, type = "") {
    return request({
        url: `${fmtUrl}/get_fmt_device_port`,
        method: "POST",
        data: {
            id,
            type
        }
    });
}

export function getFMTDeviceSinglePort(cardID, portName) {
    return request({
        url: `${fmtUrl}/get_fmt_device_single_port`,
        method: "POST",
        data: {
            cardID,
            portName
        }
    });
}

export function getFilterFMTEventAPI(NeIp, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${fmtUrl}/get_filter_fmt_event`,
        method: "POST",
        data: {
            NeIp,
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function getAmpconOTNCardList() {
    return request({
        url: `${fmtUrl}/get_card_list`,
        method: "GET"
    });
}

// bypass database in backend
export function queryDCSInfo(ip) {
    return request({
        url: `${dcsUrl}/info/query`,
        method: "GET",
        params: {
            ip
        }
    });
}

// query database in backend
export function getDCSInfo(ip) {
    return request({
        url: `${dcsUrl}/info/get`,
        method: "GET",
        params: {
            ip
        }
    });
}

export function modifyDCSConfig(data) {
    return request({
        url: `${dcsUrl}/config/modify`,
        method: "PUT",
        data
    });
}

export function modifyDCSPortNote(data) {
    return request({
        url: `${dcsUrl}/config/set_note`,
        method: "POST",
        data
    });
}

export function queryDCSConfig(ip, slotIndex = null, cardId = null) {
    return request({
        url: `${dcsUrl}/config/query`,
        method: "GET",
        params: {
            ip,
            slotIndex,
            cardId
        }
    });
}

export function getDCSDeviceCard(filterCard, {ip}) {
    return request({
        url: `${dcsUrl}/get_dcs_device_card`,
        method: "POST",
        data: {
            ip,
            filterCard
        }
    });
}

export function getDCSDevicePort(id, type = "") {
    return request({
        url: `${dcsUrl}/get_dcs_device_port`,
        method: "POST",
        data: {
            id,
            type
        }
    });
}

export function getDCSDeviceSinglePort(cardID, portName) {
    return request({
        url: `${dcsUrl}/get_dcs_device_single_port`,
        method: "POST",
        data: {
            cardID,
            portName
        }
    });
}

export function getFilterDCSEventAPI(NeIp, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${dcsUrl}/get_filter_dcs_event`,
        method: "POST",
        data: {
            NeIp,
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}