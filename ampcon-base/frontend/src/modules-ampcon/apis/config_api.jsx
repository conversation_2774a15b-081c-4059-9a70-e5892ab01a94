import {request} from "@/utils/common/request";

const baseUrl = "/ampcon/config";
export function queryAllPlatforms() {
    return request({
        url: `${baseUrl}/switch_model`,
        method: "GET"
    });
}

export function queryDeployConfigsTree(type, page, pageSize, searchArgs) {
    return request({
        url: `${baseUrl}/deploy_configs_tree`,
        method: "POST",
        data: {
            type,
            page,
            pageSize,
            searchArgs
        }
    });
}

export function queryConfigFileContent(configFileName) {
    return request({
        url: `${baseUrl}/${configFileName}/config_name`,
        method: "GET"
    });
}

export function saveGenerateConfig(name, config, modelName, modelType) {
    return request({
        url: `${baseUrl}/generate_config/save_config`,
        method: "POST",
        data: {
            name,
            config,
            modelName,
            modelType
        }
    });
}

export function checkGlobalFile(model, glob_file) {
    return request({
        url: `${baseUrl}/check_glob_file`,
        method: "POST",
        data: {
            model,
            glob_file
        }
    });
}

export function generateGlobalConfig(model_name, name, generic_global, security_global) {
    const formData = new FormData();
    formData.append("model_name", model_name);
    formData.append("name", name);
    formData.append("generic_global", generic_global);
    formData.append("security_global", security_global);
    return request({
        url: `${baseUrl}/generate/global`,
        method: "POST",
        data: formData,
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
}

export function getAllSystemConfigBrief() {
    return request({
        url: `${baseUrl}/system_config/brief_all/info`,
        method: "GET"
    });
}

export function getModelDefaultConfig(model) {
    return request({
        url: `${baseUrl}/default_config/${model}`,
        method: "GET"
    });
}

export function editConfigFile(configName, configContent) {
    return request({
        url: `${baseUrl}/${configName}/edit`,
        method: "POST",
        data: {
            configContent
        }
    });
}

export function getSystemConfigInfo(name) {
    return request({
        url: `${baseUrl}/system_config/info/${name}`,
        method: "GET"
    });
}

export function getFileContent(path, model) {
    return request({
        url: `${baseUrl}/file`,
        method: "POST",
        data: {
            path,
            model
        }
    });
}

export function saveGlobalSystemConfig(
    action,
    selectedSystemConfig,
    deviceDefaultLoginUser,
    deviceDefaultPassword,
    licensePortalURL,
    licensePortalUser,
    licensePortalPassword,
    configBackupNumber,
    dbBackupNumber,
    securityConfigFile,
    parkingSecurityConfigFile,
    allowSwitchSourceIP
) {
    const formData = new FormData();
    formData.append("action", action);
    formData.append("configuration_name", selectedSystemConfig);
    formData.append("switch_op_user", deviceDefaultLoginUser);
    formData.append("switch_op_password", deviceDefaultPassword);
    formData.append("license_portal_url", licensePortalURL);
    formData.append("license_portal_user", licensePortalUser);
    formData.append("license_portal_password", licensePortalPassword);
    formData.append("retrieve_config_num", configBackupNumber);
    formData.append("db_config_num", dbBackupNumber);
    formData.append("security_config", securityConfigFile);
    formData.append("parking_security_config", parkingSecurityConfigFile);
    formData.append("allowed_source_ip", allowSwitchSourceIP);
    return request({
        url: `${baseUrl}/save/system`,
        method: "POST",
        data: formData,
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
}

export function saveGroupSystemConfig(
    action,
    selectedSystemConfig,
    deviceDefaultLoginUser,
    deviceDefaultPassword,
    licensePortalURL,
    licensePortalUser,
    licensePortalPassword,
    securityConfigFile
) {
    const formData = new FormData();
    formData.append("action", action);
    formData.append("configuration_name", selectedSystemConfig);
    formData.append("switch_op_user", deviceDefaultLoginUser);
    formData.append("switch_op_password", deviceDefaultPassword);
    formData.append("license_portal_url", licensePortalURL);
    formData.append("license_portal_user", licensePortalUser);
    formData.append("license_portal_password", licensePortalPassword);
    formData.append("security_config", securityConfigFile);
    return request({
        url: `${baseUrl}/save/system`,
        method: "POST",
        data: formData,
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
}

export function addGroupSystemConfig(
    selectedSystemConfig,
    deviceDefaultLoginUser,
    deviceDefaultPassword,
    licensePortalURL,
    licensePortalUser,
    licensePortalPassword,
    securityConfigFile
) {
    const formData = new FormData();
    formData.append("action", "add");
    formData.append("configuration_name", selectedSystemConfig);
    formData.append("switch_op_user", deviceDefaultLoginUser);
    formData.append("switch_op_password", deviceDefaultPassword);
    formData.append("license_portal_url", licensePortalURL);
    formData.append("license_portal_user", licensePortalUser);
    formData.append("license_portal_password", licensePortalPassword);
    formData.append("security_config", securityConfigFile);
    return request({
        url: `${baseUrl}/save/system`,
        method: "POST",
        data: formData,
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
}

export function getLicenseConnectState(url, user, password, configurationName) {
    return request({
        url: `${baseUrl}/license_connect`,
        method: "POST",
        data: {
            url,
            user,
            password,
            configurationName
        }
    });
}

export function updateEncryptKey(originKey, newKey) {
    return request({
        url: `${baseUrl}/update/encrypt_key`,
        method: "POST",
        data: {
            originKey,
            newKey
        }
    });
}

export function removeSystemConfigByName(systemConfigName) {
    return request({
        url: `${baseUrl}/system_config/remove`,
        method: "POST",
        data: {
            systemConfigName
        }
    });
}

export function querySyslogConfigInfo(page, pageSize, filterFields, sortFields, searchFields) {
    return request({
        url: `${baseUrl}/syslog_config_info`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function updateSyslogConfigInfo(syslogIP, syslogPort, syslogProtocol, syslogLevel) {
    return request({
        url: `${baseUrl}/update/syslog_config`,
        method: "POST",
        data: {
            syslogIP,
            syslogPort,
            syslogProtocol,
            syslogLevel
        }
    });
}

export function deleteSyslogConfigInfo(syslogIP) {
    return request({
        url: `${baseUrl}/delete/syslog`,
        method: "POST",
        data: {
            syslogIP
        }
    });
}

export function querySwitchFilterBySystemConfigName(
    systemConfigName,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseUrl}/switch_table/filter_by_system_config_name/data`,
        method: "POST",
        data: {
            systemConfigName,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function querySwitchPinBySNList(snList, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/switch_table/pin_by_sn_list/data`,
        method: "POST",
        data: {
            snList,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function querySwitchPinBySystemConfigName(
    systemConfigName,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseUrl}/switch_table/pin_by_system_config_name/data`,
        method: "POST",
        data: {
            systemConfigName,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function updateSystemConfigAndSwitchMapping(systemConfigName, operations) {
    return request({
        url: `${baseUrl}/system_config/switch_management/update`,
        method: "POST",
        data: {
            systemConfigName,
            operations
        }
    });
}

export function queryImages(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/view_image`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function deleteImage(imageId) {
    return request({
        url: `${baseUrl}/delete_image`,
        method: "POST",
        data: {
            imageId
        }
    });
}

export function uploadImageMd5FileByLink(imageId, md5Link) {
    return request({
        url: `${baseUrl}/upload_md5_img_by_link`,
        method: "POST",
        data: {
            imageId,
            md5Link
        }
    });
}

export function uploadImageMd5FileByFile(imageId, md5File) {
    const formData = new FormData();
    formData.append("imageId", imageId);
    formData.append("file", md5File);
    return request({
        url: `${baseUrl}/upload_md5_img_by_file`,
        method: "POST",
        data: formData,
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
}

export function uploadImageFileByFile(imageFile, version, reversion, platform, md5File) {
    const formData = new FormData();
    formData.append("file", imageFile);
    formData.append("version", version);
    formData.append("revision", reversion);
    formData.append("platform", platform);
    formData.append("md5File", md5File || null);
    return request({
        url: `${baseUrl}/upload_image/1`,
        method: "POST",
        data: formData,
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
}

export function uploadImageFileByLink(imageLink, version, revision, platform, md5Link) {
    return request({
        url: `${baseUrl}/upload_image/2`,
        method: "POST",
        data: {
            imageLink,
            version,
            revision,
            platform,
            md5Link
        }
    });
}

export function uploadImageFileByLatestLink(latestImageNameList) {
    return request({
        url: `${baseUrl}/upload_image/3`,
        method: "POST",
        data: {
            latest_image_name: latestImageNameList
        }
    });
}

export function getConfigsTree() {
    return request({
        url: `${baseUrl}/configs_tree`,
        method: "POST"
    });
}

export function addConfig(name, desc, pid, level) {
    return request({
        url: `${baseUrl}/general/add`,
        method: "POST",
        data: {
            name,
            desc,
            pid,
            level
        }
    });
}

export function delGeneralConfig(name) {
    return request({
        url: `${baseUrl}/general/${name}/delete`,
        method: "GET"
    });
}

export function getGeneralConfig(configId) {
    return request({
        url: `${baseUrl}/general/${configId}/config`,
        method: "GET"
    });
}

export function updateConfig(name, config) {
    return request({
        url: `${baseUrl}/general/update`,
        method: "POST",
        data: {
            name,
            config
        }
    });
}

export function saveAsConfig(name, desc, pid, level, config) {
    return request({
        url: `${baseUrl}/general/save_as`,
        method: "POST",
        data: {
            name,
            desc,
            pid,
            level,
            config
        }
    });
}

export function moveConfig(configName, parentId) {
    return request({
        url: `${baseUrl}/general/move`,
        method: "POST",
        data: {
            configName,
            parentId
        }
    });
}

export function queryDeployConfig(sn) {
    return request({
        url: `${baseUrl}/switch/${sn}/config/show`,
        method: "GET"
    });
}

export function fetchSwitchConfig(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/config/data`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function getConfig(configId) {
    return request({
        url: `${baseUrl}/${configId}/config`,
        method: "GET"
    });
}

export function getConfigAttach(configId) {
    return request({
        url: `${baseUrl}/${configId}/attach`,
        method: "GET"
    });
}

export function delConfig(configName) {
    return request({
        url: `${baseUrl}/${configName}/delete`,
        method: "GET"
    });
}

export function getModelConfigInfo(modelName) {
    return request({
        url: `${baseUrl}/model/${modelName}`,
        method: "GET"
    });
}

export function getImageInfo(modelName) {
    return request({
        url: `${baseUrl}/get_image_info/${modelName}`,
        method: "GET"
    });
}

export function createModel(modelParams) {
    return request({
        url: `${baseUrl}/model/create`,
        method: "POST",
        data: {
            ...modelParams
        }
    });
}

export function fetchParkingSwitch(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/parking/data`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function delParkingSwitch(sn) {
    return request({
        url: `${baseUrl}/del_switch_from_parking`,
        method: "POST",
        data: {
            sn
        }
    });
}

export function investigateSwitch(sn) {
    return request({
        url: `${baseUrl}/investigate/${sn}`,
        method: "GET"
    });
}

export function editSysname(data) {
    return request({
        url: `${baseUrl}/edit_sysname`,
        method: "POST",
        data,
        timeout: 60000
    });
}

export function getBackupDB(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/db_backup_management/get_backup_db_record`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function addBackupDB(data) {
    return request({
        url: `${baseUrl}/db_backup_management/add_backup_db_record`,
        method: "POST",
        data
    });
}

export function updateBackupDB(data) {
    return request({
        url: `${baseUrl}/db_backup_management/update_backup_db_record`,
        method: "POST",
        data
    });
}

export function delBackupDB(name) {
    return request({
        url: `${baseUrl}/db_backup_management/delete_backup_db_record`,
        method: "POST",
        data: {
            name
        }
    });
}

export function restoreBackupDB(data) {
    return request({
        url: `${baseUrl}/db_backup_management/restore_db_backup`,
        method: "POST",
        data
    });
}
export function getLatestImageForCampus() {
    return request({
        url: `${baseUrl}/get_latest_image_for_campus`,
        method: "GET"
    });
}
export function getLatestImage() {
    return request({
        url: `${baseUrl}/get_latest_image`,
        method: "GET"
    });
}

export function updateSwitchModel() {
    return request({
        url: `${baseUrl}/update_switch_model`,
        method: "POST"
    });
}

export function getPortList(data) {
    return request({
        url: `${baseUrl}/get_port_list`,
        method: "POST",
        data
    });
}

export function getAllModelPhysicPortInfo() {
    return request({
        url: `${baseUrl}/all_model_physic_port_info`,
        method: "GET"
    });
}

export function refreshSystemConfigLicenseInfo() {
    return request({
        url: `${baseUrl}/refresh_system_config_account_info`,
        method: "POST"
    });
}

export function exportHwidExcel(data) {
    return request({
        url: `${baseUrl}/export_not_activated_hwid_excel`,
        method: "POST",
        data,
        responseType: "arraybuffer"
    });
}

export function getLicenseConvertSwitch(page, pageSize, filterFields, sortFields, searchFields) {
    return request({
        url: `${baseUrl}/get_license_convert_switch`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function checkCurrentLicenseType() {
    return request({
        url: `${baseUrl}/check_license_type`,
        method: "GET"
    });
}

export function convertTrialLicense(data) {
    return request({
        url: `${baseUrl}/convert_trial_license`,
        method: "POST",
        data
    });
}

export function htmlToTar(data) {
    return request({
        url: `${baseUrl}/html_to_tar`,
        method: "POST",
        data
    });
}

export function tarToHtml(data) {
    return request({
        url: `${baseUrl}/tar_to_html`,
        method: "POST",
        data
    });
}

export function getQuickActivationTableData(page, pageSize, filterFields, sortFields, searchFields) {
    return request({
        url: `${baseUrl}/get_quick_activation_table_data`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function activateLicense(data) {
    return request({
        url: `${baseUrl}/activate_ampcon_license_by_hwid`,
        method: "POST",
        data
    });
}

export function getConfigAccountInfo() {
    return request({
        url: `${baseUrl}/get_system_config_account_info`,
        method: "GET"
    });
}
