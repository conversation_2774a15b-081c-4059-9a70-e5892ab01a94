import {request} from "@/utils/common/request";

const baseUrl = "/ampcon/inventory";

export function queryModelPhysicPorts(platform_name) {
    return request({
        url: `${baseUrl}/platform/${platform_name}/ports`,
        method: "GET"
    });
}

export function batchApplyConfig(config, configName, switchChecked, groupChecked) {
    return request({
        url: `${baseUrl}/batch_apply_config`,
        method: "POST",
        data: {
            config,
            configName,
            switchChecked,
            groupChecked
        }
    });
}

export function getSwitchTreeData() {
    return request({
        url: `${baseUrl}/get_switch_tree_info`,
        method: "GET"
    });
}

export function getAvaliableSwitch() {
    return request({
        url: `${baseUrl}/get_avaliable_switch_tree`,
        method: "GET"
    });
}

export function addSwitchTreeInfo(host, group) {
    return request({
        url: `${baseUrl}/switch_list/add`,
        method: "POST",
        data: {
            host,
            group
        }
    });
}

export function editSwitchTreeInfo(oldHost, newHost) {
    return request({
        url: `${baseUrl}/switch_list/edit`,
        method: "POST",
        data: {
            oldHost,
            newHost
        }
    });
}

export function delSwitchTreeInfo(host) {
    return request({
        url: `${baseUrl}/switch_list/del`,
        method: "POST",
        data: {
            host
        }
    });
}

export function setSwitchTreeLocation(data) {
    return request({
        url: `${baseUrl}/map/set_switch_location`,
        method: "POST",
        data
    });
}

export function addSwitchTreeGroup(name, parentKey) {
    return request({
        url: `${baseUrl}/add_tree_switch_group`,
        method: "POST",
        data: {
            name,
            parentKey
        }
    });
}

export function delSwitchTreeGroup(key) {
    return request({
        url: `${baseUrl}/del_tree_switch_group`,
        method: "POST",
        data: {
            key
        }
    });
}

export function editSwitchTreeGroup(key, name) {
    return request({
        url: `${baseUrl}/edit_tree_switch_group`,
        method: "POST",
        data: {
            key,
            name
        }
    });
}

export function updateSwitchTreeGroup(data) {
    return request({
        url: `${baseUrl}/update_tree_switch_group`,
        method: "POST",
        data
    });
}

export function getSwitchDetails(sn) {
    return request({
        url: `${baseUrl}/get_switch_details`,
        method: "POST",
        data: {
            sn
        }
    });
}

export function getUserGroup() {
    return request({
        url: `${baseUrl}/get_user_group`,
        method: "GET"
    });
}
