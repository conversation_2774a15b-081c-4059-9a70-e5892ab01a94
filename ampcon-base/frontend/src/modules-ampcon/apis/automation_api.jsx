import {request} from "@/utils/common/request";

const baseUrl = "/ampcon/automation";

export function fetchDeviceInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/get_devices`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function addDeviceInfo(data) {
    return request({
        url: `${baseUrl}/create_device`,
        method: "POST",
        data
    });
}

export function batchAddDevice(data) {
    return request({
        url: `${baseUrl}/batch_create_device`,
        method: "POST",
        data
    });
}

export function delDeviceInfo(username) {
    return request({
        url: `${baseUrl}/del_device/${username}`,
        method: "GET"
    });
}

export function enableDeviceMonitor(deviceName, deviceSudoPass) {
    return request({
        url: `${baseUrl}/enable_device_monitor`,
        method: "POST",
        data: {
            deviceName,
            deviceSudoPass
        }
    });
}

export function fetchJobInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/get_job`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function fetchJobInfoBySwitch(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/get_job_by_switch`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function fetchTaskResultInfo(job_name, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/get_task_result/${job_name}`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function fetchTaskResultInfoBySn(sn, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/get_task_result_by_sn/${sn}`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function fetchTaskResultInfoById(job_id) {
    return request({
        url: `${baseUrl}/get_task_result_by_id/${job_id}`,
        method: "GET"
    });
}

export function fetchTaskOutputInfo(data) {
    return request({
        url: `${baseUrl}/get_task_result_output`,
        method: "POST",
        data
    });
}

export function fetchJobDetailInfo() {
    return request({
        url: `${baseUrl}/get_job_details`,
        method: "GET"
    });
}

export function terminateJob(job_name) {
    return request({
        url: `${baseUrl}/terminate_job/${job_name}`,
        method: "GET"
    });
}

export function fetchPlaybookListInfo(
    showPreBuiltTag,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseUrl}/get_playbook_list`,
        method: "POST",
        data: {
            showPreBuiltTag,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function fetchInternalPlaybookInfo() {
    return request({
        url: `${baseUrl}/update_internal_playbook`,
        method: "POST"
    });
}

export function fetchPlaybookGroupInfo(
    groupType,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseUrl}/get_playbook_groups`,
        method: "POST",
        data: {
            groupType,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function checkPlaybookName(playbookName) {
    return request({
        url: `${baseUrl}/pre_create_playbook`,
        method: "POST",
        data: {
            name: playbookName
        }
    });
}

export function addPlaybook(data) {
    return request({
        url: `${baseUrl}/add_playbook`,
        method: "POST",
        data
    });
}

export function runPlaybook(data) {
    return request({
        url: `${baseUrl}/run_playbook`,
        method: "POST",
        data
    });
}

export function editPlaybook(data) {
    return request({
        url: `${baseUrl}/edit_playbook`,
        method: "POST",
        data
    });
}

export function fetchPlaybookFilelist(playbookName) {
    return request({
        url: `${baseUrl}/get_playbook_filelist/${playbookName}`,
        method: "GET"
    });
}

export function fetchPlaybookFileContent(data) {
    return request({
        url: `${baseUrl}/get_playbook_file_content`,
        method: "POST",
        data
    });
}

export function saveAsPlaybookAPI(data) {
    return request({
        url: `${baseUrl}/save_as_playbook`,
        method: "POST",
        data
    });
}

export function exportPlaybookAPI(playbookName) {
    window.open(`${baseUrl}/export_playbook/${playbookName}`, "_self");
}

export function importPlaybookAPI(data) {
    const formData = new FormData();
    Object.keys(data).forEach(key => {
        if (key === "playbookFile") {
            formData.append(key, data[key].target.files[0]);
        } else {
            formData.append(key, data[key]);
        }
    });

    return request({
        headers: {
            "Content-Type": "multipart/form-data"
        },
        data: formData,
        url: `${baseUrl}/import_playbook`,
        method: "POST"
    });
}

export function removePlaybookAPI(playbookName) {
    return request({
        url: `${baseUrl}/delete_playbook/${playbookName}`,
        method: "POST"
    });
}

export function updatePlaybookTag(data) {
    return request({
        url: `${baseUrl}/update_tag`,
        method: "POST",
        data
    });
}

export function updateTemplateTag(recordId, tagContent) {
    return request({
        url: `${baseUrl}/update_tag`,
        method: "POST",
        data: {
            recordId,
            tagContent,
            recordType: "template"
        }
    });
}

export function checkPlaybookAPI(playbookName) {
    return request({
        url: `${baseUrl}/check_playbook/${playbookName}`,
        method: "POST"
    });
}

export function checkPingDevicesAPI(deviceList) {
    return request({
        url: `${baseUrl}/check_ping_devices`,
        method: "POST",
        data: {
            deviceList
        }
    });
}

export function deviceReloadAPI(deviceName) {
    return request({
        url: `${baseUrl}/reload_device/${deviceName}`,
        method: "POST"
    });
}

export function devicePowerUpAPI(data) {
    return request({
        url: `${baseUrl}/power_up_device`,
        method: "POST",
        data
    });
}

export function devicePowerDownAPI(deviceName) {
    return request({
        url: `${baseUrl}/power_down_device/${deviceName}`,
        method: "POST"
    });
}
