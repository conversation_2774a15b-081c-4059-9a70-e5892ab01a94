import {request} from "@/utils/common/request";

const baseUrl = "/ampcon/dc_virtual_resource";
const baseUrl2 = "/ampcon/dc_blueprint";

// Bare Metal Node
export function fetchBareMetalNodeInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/node_group/list`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

// links info
export function fabricLinkInfo(data) {
    return request({
        url: `${baseUrl}/node_group/list_link_info`,
        method: "POST",
        data
    });
}

export function NodeLogInfo(data, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/node_addition/list_log`,
        method: "POST",
        data: {
            id: data.id,
            type: data.type,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}
export function deleteBareMetalNodeInfo(data) {
    return request({
        url: `${baseUrl}/node_group/delete`,
        method: "POST",
        data
    });
}
// Fabric List
export function fetchFabricInfo() {
    return request({
        url: `${baseUrl}/resource_interconnection/list_fabric`,
        method: "POST"
    });
}

export function getNodeTemplateList() {
    return request({
        url: `${baseUrl}/node_template/list`,
        method: "post"
    });
}

export function saveNodeTemplate(data) {
    return request({
        url: `${baseUrl}/node_group/save`,
        method: "POST",
        data
    });
}

// POD
export function fetchPODInfo(data) {
    return request({
        url: `${baseUrl}/get_az_by_fabric`,
        method: "POST",
        data
    });
}

// port List
export function node_group_list_device_port_info(data) {
    return request({
        url: `${baseUrl}/node_group/list_device_port_info`,
        method: "POST",
        data
    });
}

// -------------------------------

export function save_node_group(data) {
    return request({
        url: `${baseUrl}/node_group/save`,
        method: "POST",
        data
    });
}
export function list_vd_dropdown_data(data) {
    return request({
        url: `${baseUrl2}/fabric_topo/list_vd_dropdown_data`,
        method: "POST",
        data
    });
}
// campus

// Cloud
export function fetchCloudInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/virtual_resource_host/list`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

// Cloud links info
export function cloudLinkInfo(data) {
    return request({
        url: `${baseUrl}/virtual_resource_host/list_link_info`,
        method: "POST",
        data
    });
}

export function saveCloudNode(data) {
    return request({
        url: `${baseUrl}/virtual_resource_host/save`,
        method: "POST",
        data
    });
}

export function delete_virtual_resource_host(data) {
    return request({
        url: `${baseUrl}/virtual_resource_host/delete`,
        method: "POST",
        data
    });
}

// vlan domain
export function fetchVlanDomainInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/vlan_domain_group/list`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function viewFabric(data) {
    return request({
        url: `${baseUrl2}/fabric_topo/view`,
        method: "POST",
        data
    });
}

// Custom Node Template
export function delete_custom_node(data) {
    return request({
        url: `${baseUrl}/node_template/delete`,
        method: "POST",
        data
    });
}

// VLAN Domain
export function list_vlan_domain(data) {
    return request({
        url: `${baseUrl2}/fabric_topo/list_vlan_domain`,
        method: "POST",
        data
    });
}
export function edit_vlan_domain(data) {
    return request({
        url: `${baseUrl2}/fabric_topo/edit_vlan_domain`,
        method: "POST",
        data
    });
}
export function add_vlan_domain(data) {
    return request({
        url: `${baseUrl2}/fabric_topo/add_vlan_domain`,
        method: "POST",
        data
    });
}

export function load_fabric_switch(data) {
    return request({
        url: `${baseUrl2}/lifecycle/list_fabric_switch`,
        method: "POST",
        data
    });
}

export function save_custom_node(data) {
    return request({
        url: `${baseUrl}/node_template/save`,
        method: "POST",
        data
    });
}

export function delete_vlan_domain(data) {
    return request({
        url: `${baseUrl}/vlan_domain_group/delete`,
        method: "POST",
        data
    });
}
