import {request} from "@/utils/common/request";

const baseURL = "/ampcon/resource_pool";

// vni start
export function queryResourcePoolVniTableData(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/resource_pool_vni_table_data`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function deleteResourcePoolVniPool(data) {
    return request({
        url: `${baseURL}/delete_vni_pool_record`,
        method: "POST",
        data
    });
}

export function createResourcePoolVniPool(data) {
    return request({
        url: `${baseURL}/add_vni_pool`,
        method: "POST",
        data
    });
}

export function editResourcePoolVniPool(data) {
    return request({
        url: `${baseURL}/edit_vni_pool`,
        method: "POST",
        data
    });
}

export function copyResourcePoolVniPool(data) {
    return request({
        url: `${baseURL}/clone_vni_pool`,
        method: "POST",
        data
    });
}

export function generateResourceVniRecord(data) {
    return request({
        url: `${baseURL}/generate_vni_pool_record`,
        method: "POST",
        data
    });
}

export function deleteVniResourceRecord(data) {
    return request({
        url: `${baseURL}/delete_first_ten_record_in_vni`,
        method: "POST",
        data
    });
}

export function queryResourcePoolAnsTableData(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/resource_pool_asn_table_data`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function deleteResourcePoolAsnPool(data) {
    return request({
        url: `${baseURL}/delete_asn_pool_record`,
        method: "POST",
        data
    });
}

export function createResourcePoolAsnPool(data) {
    return request({
        url: `${baseURL}/add_asn_pool`,
        method: "POST",
        data
    });
}

export function editResourcePoolAsnPool(data) {
    return request({
        url: `${baseURL}/edit_asn_pool`,
        method: "POST",
        data
    });
}

export function queryResourcePoolIpTableData(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/resource_pool_ip_table_data`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function deleteResourcePoolIpPool(data) {
    return request({
        url: `${baseURL}/delete_ip_pool_record`,
        method: "POST",
        data
    });
}

export function createResourcePoolIpPool(data) {
    return request({
        url: `${baseURL}/add_ip_pool`,
        method: "POST",
        data
    });
}

export function editResourcePoolIpPool(data) {
    return request({
        url: `${baseURL}/edit_ip_pool`,
        method: "POST",
        data
    });
}

export function generateResourceRecord(data) {
    return request({
        url: `${baseURL}/generate_pool_record`,
        method: "POST",
        data
    });
}

export function deleteResourceRecord(data) {
    return request({
        url: `${baseURL}/delete_first_ten_record`,
        method: "POST",
        data
    });
}

export function getResourcePoolDropdownList(data) {
    return request({
        url: `${baseURL}/get_resource_pool_dropdown_list`,
        method: "POST",
        data
    });
}

export function copyResourcePoolAsnPool(data) {
    return request({
        url: `${baseURL}/clone_asn_pool`,
        method: "POST",
        data
    });
}

// area start
export function queryResourcePoolAreaTableData(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/resource_pool_area_table_data`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function deleteResourcePoolAreaPool(data) {
    return request({
        url: `${baseURL}/delete_area_pool`,
        method: "POST",
        data
    });
}

export function deleteResourcePoolAreaRecord(data) {
    return request({
        url: `${baseURL}/delete_area_pool_record`,
        method: "POST",
        data
    });
}

export function createResourcePoolAreaPool(data) {
    return request({
        url: `${baseURL}/add_area_pool`,
        method: "POST",
        data
    });
}

export function editResourcePoolAreaPool(data) {
    return request({
        url: `${baseURL}/edit_area_pool`,
        method: "POST",
        data
    });
}

export function generateAreaResourceRecord(data) {
    return request({
        url: `${baseURL}/generate_area_pool_record`,
        method: "POST",
        data
    });
}

export function generateIpResourceRecord(data) {
    return request({
        url: `${baseURL}/generate_ip_pool_record`,
        method: "POST",
        data
    });
}

export function copyResourcePoolAreaPool(data) {
    return request({
        url: `${baseURL}/clone_area_pool`,
        method: "POST",
        data
    });
}

// area end

export function copyResourcePoolIpPool(data) {
    return request({
        url: `${baseURL}/clone_ip_pool`,
        method: "POST",
        data
    });
}

export function deleteIpResourceRecord(data) {
    return request({
        url: `${baseURL}/delete_first_ten_record_in_ip`,
        method: "POST",
        data
    });
}

export function fetchVniFabricList(vniID) {
    return request({
        url: `${baseURL}/get_vni_fabric_list/${vniID}`,
        method: "POST"
    });
}
