import {request} from "@/utils/common/request";

const baseURL = "/ampcon/config/system";

export function updateEmailServerSetting(data) {
    return request({
        url: `${baseURL}/update_email_server_setting`,
        method: "POST",
        data
    });
}

export function getEmailServerSetting() {
    return request({
        url: `${baseURL}/get_email_server_setting`,
        method: "GET"
    });
}

export function getEmailRuleSettingsTableData(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    const defaultSortFields = sortFields.length === 0 ? [{field: "create_time", order: "desc"}] : sortFields;

    return request({
        url: `${baseURL}/get_email_rule_settings_table_data`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields: defaultSortFields,
            searchFields
        }
    });
}

export function addEmailRuleSetting(data) {
    return request({
        url: `${baseURL}/add_email_rule_setting`,
        method: "POST",
        data
    });
}

export function updateEmailRuleSetting(data) {
    return request({
        url: `${baseURL}/edit_email_rule_setting`,
        method: "POST",
        data
    });
}

export function deleteEmailRoleSetting(data) {
    return request({
        url: `${baseURL}/delete_email_rule_setting`,
        method: "POST",
        data
    });
}

export function sendTestEmail(data) {
    return request({
        url: `${baseURL}/send_test_email`,
        method: "POST",
        data
    });
}

export function verifyEmailServerConnection(data) {
    return request({
        url: `${baseURL}/verify_email_server_connection`,
        method: "POST",
        data
    });
}

export function alarmEmailLogsTableData(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    // 如果 sortFields 为空，添加默认排序
    const defaultSortFields = sortFields.length === 0 ? [{field: "create_time", order: "desc"}] : sortFields;

    return request({
        url: `${baseURL}/get_alarm_email_logs_table_data`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields: defaultSortFields,
            searchFields
        }
    });
}

export function deleteAlarmEmailLogs(data) {
    return request({
        url: `${baseURL}/delete_alarm_email_logs`,
        method: "POST",
        data
    });
}

export function viewEmailDetail(data) {
    return request({
        url: `${baseURL}/view_email_detail`,
        method: "POST",
        data
    });
}
