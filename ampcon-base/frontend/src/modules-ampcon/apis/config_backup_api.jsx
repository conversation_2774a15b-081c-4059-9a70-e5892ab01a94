import {request} from "@/utils/common/request";

const baseURL = "/ampcon";
const configUrl = `${baseURL}/config`;
const templateUrl = `${baseURL}/template`;

export function queryRetrieveConfig() {
    return request({
        url: `${configUrl}/retrieve_config`,
        method: "GET"
    });
}

export function collectBackupConfig(days, hour) {
    return request({
        url: `${configUrl}/collect_backup_config`,
        method: "POST",
        data: {
            trigger: "cron",
            day: `*/${days}`,
            hour,
            minute: "0",
            second: "0"
        }
    });
}

export function backupGroupConfig(groupName) {
    return request({
        url: `${configUrl}/lifecycle_retrieve/group/${groupName}`,
        method: "GET"
    });
}

export function backupSNConfig(sn, ip) {
    return request({
        url: `${configUrl}/lifecycle_retrieve/sn`,
        method: "GET",
        params: {
            sn,
            ip
        },
        timeout: 60000
    });
}

export function queryLog(sn) {
    return request({
        url: `${baseURL}/display_log/${sn}`,
        method: "GET"
    });
}

export function getSnapshotList(sn, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    if (sn === undefined) return;
    return request({
        url: `${templateUrl}/get_snapshot_list/${sn}/data`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}
