import {request} from "@/utils/common/request";

const baseURL = "/ampcon/monitor";

export function fetchPushConfigTask(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    if (sortFields.length === 0) {
        sortFields.push({field: "create_time", order: "desc"});
    }
    return request({
        url: `${baseURL}/push_config_tasks/data_all`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function readPushConfig(taskId) {
    return request({
        url: `${baseURL}/read_config/${taskId}`,
        method: "Get"
    });
}

export function delPushConfig(taskName) {
    return request({
        url: `${baseURL}/del_push_config/${taskName}`,
        method: "Get"
    });
}

export function getConfigContent(taskName) {
    return request({
        url: `${baseURL}/get_config_content/${taskName}`,
        method: "Get"
    });
}

export function fetchTaskList(taskName) {
    return request({
        url: `${baseURL}/get_push_config_task_items/${taskName}`,
        method: "Get"
    });
}

export function getSwitchPushLog(taskName, sn) {
    return request({
        url: `${baseURL}/show_switch_ret/${taskName}/${sn}`,
        method: "Get"
    });
}

export function getSwitchAlarm(startTime, endTime) {
    return request({
        url: `${baseURL}/get_switch_alarm`,
        method: "POST",
        data: {
            startTime,
            endTime
        }
    });
}

export function getHistorySwitchAlarm(startTime, endTime) {
    return request({
        url: `${baseURL}/get_history_switch_alarm`,
        method: "POST",
        data: {
            startTime,
            endTime
        }
    });
}

export function ackSwitchAlarm(data) {
    return request({
        url: `${baseURL}/ack_switch_alarm`,
        method: "POST",
        data
    });
}
export function clearSwitchHistoryAlarm(data) {
    return request({
        url: `${baseURL}/clear_switch_history_alarm`,
        method: "POST",
        data
    });
}

function processTypeFilter(filterFields) {
    return filterFields.map(filter => {
        if (filter.field === "type") {
            const processedFilters = filter.filters.map(f => {
                const lowerValue = (f.value || "").toLowerCase();
                if (lowerValue.startsWith("warn")) {
                    return {...f, value: "warn"};
                }
                return f;
            });
            return {...filter, filters: processedFilters};
        }
        return filter;
    });
}

function processTypeSearch(searchFields) {
    if (searchFields?.fields?.includes("type")) {
        const lowerValue = (searchFields.value || "").toLowerCase();
        if (lowerValue.startsWith("warn")) {
            return {...searchFields, value: "warn"};
        }
    }
    return searchFields;
}

export function getAllAlarmList(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/get_all_alarm`,
        method: "POST",
        data: {
            filterFields: processTypeFilter(filterFields),
            sortFields,
            searchFields: processTypeSearch(searchFields),
            page,
            pageSize
        }
    });
}
export function getUnreadAlarmList(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/get_unread_alarm`,
        method: "POST",
        data: {
            filterFields: processTypeFilter(filterFields),
            sortFields,
            searchFields: processTypeSearch(searchFields),
            page,
            pageSize
        }
    });
}

export function updateAlarmStatus(id) {
    return request({
        url: `${baseURL}/update_alarm_status`,
        method: "POST",
        data: {id}
    });
}

export function removeAlarmsAPI(id) {
    return request({
        url: `${baseURL}/delete_alarm/${id}`,
        method: "POST"
    });
}

export function getDlbTableAPI(target, data) {
    return request({
        url: `${baseURL}/get_dlb_table`,
        method: "POST",
        data: {
            target,
            data
        }
    });
}

export function getTopologyList() {
    return request({
        url: `${baseURL}/get_topology_list`,
        method: "Get"
    });
}

export function setDefaultTopology(data) {
    return request({
        url: `${baseURL}/set_default_topology`,
        method: "POST",
        data
    });
}

export function addTopology(data) {
    return request({
        url: `${baseURL}/add_topology`,
        method: "POST",
        data
    });
}

export function editTopology(data) {
    return request({
        url: `${baseURL}/edit_topology`,
        method: "POST",
        data
    });
}

export function delTopology(id) {
    return request({
        url: `${baseURL}/del_topology`,
        method: "POST",
        data: {id}
    });
}

export function getTopology(data) {
    return request({
        url: `${baseURL}/get_topology`,
        method: "POST",
        data
    });
}

export function saveTopologyDetail(data) {
    return request({
        url: `${baseURL}/save_topology`,
        method: "POST",
        data
    });
}

export function fetchAddDeviceModalTableData(
    snList,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseURL}/get_add_device_modal_table_data`,
        method: "POST",
        data: {
            snList,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function autoDiscoverTopology(monitorTargetIds) {
    return request({
        url: `${baseURL}/refresh_lldp_info`,
        method: "POST",
        data: {monitorTargetIds}
    });
}

export function fetchTopologyToBeAddedSwitch(switchIdList, otnIpList) {
    return request({
        url: `${baseURL}/get_topology_to_be_added_switch`,
        method: "POST",
        data: {switchIdList, otnIpList}
    });
}

export function fetchInterfaceInfo(target, date) {
    return request({
        url: `${baseURL}/get_interface_info`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function fetchDeviceInfo(target, date) {
    return request({
        url: `${baseURL}/get_device_table`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function fetchAIInfo(target, date) {
    return request({
        url: `${baseURL}/get_ai_info`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function fetchModulesInfo(target, date) {
    return request({
        url: `${baseURL}/get_modules_info`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function fetchUsageTopK(metricName, topK, target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_usage_topk`,
        method: "POST",
        data: {
            metricName,
            topK,
            target,
            startTime,
            endTime
        }
    });
}

export function fetchInterfaceTopK(metricName, topK, target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_interfaces_topk`,
        method: "POST",
        data: {
            metricName,
            topK,
            target,
            startTime,
            endTime
        }
    });
}

export function fetchModulesTopK(metricName, topK, target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_modules_topk`,
        method: "POST",
        data: {
            metricName,
            topK,
            target,
            startTime,
            endTime
        }
    });
}

export function fetchModulesPortStatus(metric, target, port, startTime, endTime, channel_index = null) {
    return request({
        url: `${baseURL}/get_modules_port_status`,
        method: "POST",
        data: {
            metric,
            target,
            port,
            startTime,
            endTime,
            channel_index
        }
    });
}

export function fetchFsosSwtichAllPorts(target) {
    return request({
        url: `${baseURL}/get_fsos_switch_all_ports`,
        method: "POST",
        data: {
            target
        }
    });
}

export function getFsosDeviceTemperature(target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_fsos_switch_device_temperature`,
        method: "POST",
        data: {
            target,
            startTime,
            endTime
        }
    });
}

export function getFsosDeviceFanSpeed(target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_fsos_switch_device_fan_speed`,
        method: "POST",
        data: {
            target,
            startTime,
            endTime
        }
    });
}

export function getFsosDeviceSupplyStatus(target) {
    return request({
        url: `${baseURL}/get_fsos_switch_device_supply_status`,
        method: "POST",
        data: {
            target
        }
    });
}

export function getFsosDeviceFanStatus(target) {
    return request({
        url: `${baseURL}/get_fsos_switch_device_fan_status`,
        method: "POST",
        data: {
            target
        }
    });
}

export function getFsosDevicePortOverview(target) {
    return request({
        url: `${baseURL}/get_fsos_switch_device_interface_overview`,
        method: "POST",
        data: {
            target
        }
    });
}

export function fetchFsosSwtichInterface(interface_name, metricName, target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_fsos_switch_interface_filter`,
        method: "POST",
        data: {
            interface_name,
            metricName,
            target,
            startTime,
            endTime
        }
    });
}

export function fetchFsosSwtichUsage(metricName, target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_fsos_switch_usage`,
        method: "POST",
        data: {
            metricName,
            target,
            startTime,
            endTime
        }
    });
}

export function fetchFsosSwitchDeviceMac(target) {
    return request({
        url: `${baseURL}/get_fsos_switch_device_mac_address`,
        method: "POST",
        data: {
            target
        }
    });
}

export function fetchSnmpDeviceTemperTopk(metricName, topK, target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_snmp_device_temperature_topk`,
        method: "POST",
        data: {
            metricName,
            topK,
            target,
            startTime,
            endTime
        }
    });
}

export function fetchSnmpVisualTopk(metricName, topK, target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_snmp_visual_topk`,
        method: "POST",
        data: {
            metricName,
            topK,
            target,
            startTime,
            endTime
        }
    });
}

export function fetchSnmpVisualUsageTopk(metricName, topK, target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_snmp_visual_usage_topk`,
        method: "POST",
        data: {
            metricName,
            topK,
            target,
            startTime,
            endTime
        }
    });
}

export function getSnmpDataFilteringData(snmpDeviceIds, itemNames) {
    return request({
        url: `${baseURL}/get_snmp_data_filtering_data`,
        method: "POST",
        data: {
            snmpDeviceIds,
            itemNames
        }
    });
}

export function fetchModulesLinkStatus(startTime, endTime, port_info) {
    return request({
        url: `${baseURL}/get_light_attenuation_info`,
        method: "POST",
        data: {
            startTime,
            endTime,
            port_info
        }
    });
}

export function fetchLinkInfo(sn) {
    return request({
        url: `${baseURL}/get_optical_fiber_link`,
        method: "POST",
        data: {sn}
    });
}

export function updateLinksThreshold(apply_type, sn, threshold, links_selected) {
    return request({
        url: `${baseURL}/update_optical_links_threshold`,
        method: "POST",
        data: {
            apply_type,
            sn,
            threshold,
            links_selected
        }
    });
}

export function fetchOTNPortInfo(otnIp) {
    return request({
        url: `${baseURL}/get_otn_port_info`,
        method: "POST",
        data: {
            otnIp
        }
    });
}

export function fetchNICsInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/get_nic_info`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function fetchNICsTopK(metricName, topK, instance, startTime, endTime, filter) {
    return request({
        url: `${baseURL}/get_nic_topk`,
        method: "POST",
        data: {
            metricName,
            topK,
            instance,
            startTime,
            endTime,
            filter
        }
    });
}

export function getNICsHistoryInfo(startTime, endTime) {
    return request({
        url: `${baseURL}/get_nic_history_info`,
        method: "POST",
        data: {
            startTime,
            endTime
        }
    });
}

export function fetchRoCENICPort(startTime, endTime, type) {
    return request({
        url: `${baseURL}/get_roce_nic_port`,
        method: "POST",
        data: {
            startTime,
            endTime,
            type
        }
    });
}

export function fetchRoCENICTopK(metricName, prioX, topK, instance, startTime, endTime, filter, type) {
    return request({
        url: `${baseURL}/get_roce_topk`,
        method: "POST",
        data: {
            metricName,
            prioX,
            topK,
            instance,
            startTime,
            endTime,
            filter,
            type
        }
    });
}

export function fetchMacTable(target, date) {
    return request({
        url: `${baseURL}/get_mac_table`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function fetchArpTable(target, date) {
    return request({
        url: `${baseURL}/get_arp_table`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function fetchOSPFTable(target, date) {
    return request({
        url: `${baseURL}/get_ospf_table`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function fetchBGPTable(target, date) {
    return request({
        url: `${baseURL}/get_bgp_table`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function fetchDLBTopK(metricName, topK, target, startTime, endTime, filter) {
    return request({
        url: `${baseURL}/get_dlb_topk`,
        method: "POST",
        data: {
            metricName,
            topK,
            target,
            startTime,
            endTime,
            filter
        }
    });
}

export function fetchInterfaceList(target, date) {
    return request({
        url: `${baseURL}/get_target_interface`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function fetchFanData(target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_device_fan_data`,
        method: "POST",
        data: {
            target,
            startTime,
            endTime
        }
    });
}

export function fetchInterfaceNicInfo(target, date) {
    return request({
        url: `${baseURL}/get_interface_nic_auto_discovery`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function getSwitchLldp(snList) {
    return request({
        url: `${baseURL}/get_switch_lldp_links`,
        method: "POST",
        data: {snList}
    });
}

export function fetchHostInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/get_host_info`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function fetchRoceNicPort(nicVendor) {
    return request({
        url: `${baseURL}/get_roce_task_port`,
        method: "POST",
        data: {
            type: nicVendor
        }
    });
}

export function fetchClientsInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/fetch_client_table_data`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function getClientsSelectInfo(data) {
    return request({
        url: `${baseURL}/get_client_select_info`,
        method: "POST",
        data
    });
}

export function createClient(data) {
    return request({
        url: `${baseURL}/create_client`,
        method: "POST",
        data
    });
}

export function editClient(data) {
    return request({
        url: `${baseURL}/edit_client`,
        method: "POST",
        data
    });
}

export function deleteClient(data) {
    return request({
        url: `${baseURL}/delete_client`,
        method: "POST",
        data
    });
}

export function fetchSwitchClientInfo(target, date) {
    return request({
        url: `${baseURL}/fetch_switch_client_table_data`,
        method: "POST",
        data: {
            switchSN: target,
            date
        }
    });
}

export function getRoceModulesInfo(
    filterResult,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseURL}/get_roce_modules_info`,
        method: "POST",
        data: {
            filterResult,
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function getRoceNicPort() {
    return request({
        url: `${baseURL}/get_roce_nic_port`,
        method: "POST",
        data: {
            startTime: null,
            endTime: null
        }
    });
}

export function getDeviceNameInfo(fabric, role) {
    return request({
        url: `${baseURL}/get_switch_interface`,
        method: "POST",
        data: {fabric, role}
    });
}
export function getTopkInfo({metricName, topK, startTime, endTime, filter}) {
    return request({
        url: `${baseURL}/get_interfaces_topk`,
        method: "POST",
        data: {
            metricName,
            topK,
            startTime,
            endTime,
            filter
        }
    });
}
export function fetchRole(fabric) {
    return request({
        url: `${baseURL}/get_role_dropdown`,
        method: "POST",
        data: {
            fabric
        }
    });
}
export function getAiInfo({filters, topK}) {
    return request({
        url: `${baseURL}/get_ai_info_by_filter`,
        method: "POST",
        data: {
            filters,
            topK
        }
    });
}

export function fetchRouteTable(target, date) {
    return request({
        url: `${baseURL}/get_route_info`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function fetchMlagTable(target, date) {
    return request({
        url: `${baseURL}/get_mlag_info`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function fetchPoeTable(target, date) {
    return request({
        url: `${baseURL}/get_poe_info`,
        method: "POST",
        data: {
            target,
            date
        }
    });
}

export function getModulesCount(target) {
    return request({
        url: `${baseURL}/get_modules_count`,
        method: "POST",
        data: {
            target
        }
    });
}

export function getModulesHistoryInfo(target, startTime, endTime) {
    return request({
        url: `${baseURL}/get_modules_history_info`,
        method: "POST",
        data: {
            target,
            startTime,
            endTime
        }
    });
}

export function fetchDeviceDDMInfo(sn, level, startTime, endTime, isEcharts) {
    return request({
        url: `${baseURL}/get_device_ddm_info`,
        method: "POST",
        data: {
            sn,
            level,
            startTime,
            endTime,
            isEcharts
        }
    });
}

export function fetchInterfaceDDMInfo(sn, interfaceName) {
    return request({
        url: `${baseURL}/get_interface_ddm_info`,
        method: "POST",
        data: {
            sn,
            interfaceName
        }
    });
}

export function fetchModuleTableAPI(
    sn,
    alertLevel,
    startTime,
    endTime,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseURL}/get_device_ddm_table`,
        method: "POST",
        data: {
            sn,
            alertLevel,
            startTime,
            endTime,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}
