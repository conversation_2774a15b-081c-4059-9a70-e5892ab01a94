import {request} from "@/utils/common/request";

const baseURL = "/ampcon";
const otnUrl = `${baseURL}/otn`;

export function getOTNDeviceList() {
    return request({
        url: `${otnUrl}/action/query`,
        method: "GET"
    });
}

export function addOTNDevice(group, name, model, ip) {
    return request({
        url: `${otnUrl}/action/add`,
        method: "POST",
        data: {
            group,
            name,
            model,
            ip
        }
    });
}

export function delOTNDevice(ip) {
    return request({
        url: `${otnUrl}/action/delete`,
        method: "DELETE",
        params: {
            ip
        }
    });
}

export function editOTNInfo(ip, name) {
    return request({
        url: `${otnUrl}/action/modify`,
        method: "PUT",
        params: {
            ip,
            name
        }
    });
}

export function setOTNTreeLocation(data) {
    return request({
        url: `${otnUrl}/map/set_otn_location`,
        method: "POST",
        data
    });
}

export function setOTNTreeGroup(data) {
    return request({
        url: `${otnUrl}/map/set_otn_tree_group_id`,
        method: "POST",
        data
    });
}

// Tag: All OEO EDFA
export function getOTNDeviceIP(filterCard, {layer}) {
    return request({
        url: `${otnUrl}/get_otn_device_ips`,
        method: "GET",
        params: {
            filterCard,
            layer
        }
    });
}
