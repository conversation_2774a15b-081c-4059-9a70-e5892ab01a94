import {request} from "@/utils/common/request";
import {axiosProv} from "@/modules-smb/utils/axiosInstances";
import {head} from "lodash";

const baseURL = "/ampcon/lifecycle";

export function queryLicenseAudit() {
    return request({
        url: `${baseURL}/license_audit`,
        method: "GET"
    });
}

export function queryLicenseAction() {
    return request({
        url: `${baseURL}/license_action`,
        method: "GET"
    });
}

export function viewReport(time) {
    return request({
        url: `${baseURL}/get_report_by_time/${time}`,
        method: "GET"
    });
}

export function upgradeGroupLicense(action, group) {
    return request({
        url: `${baseURL}/lifecycle_renewal/${action}/${group}`,
        method: "GET"
    });
}

export function upgradeLicense(action, sn) {
    return request({
        url: `${baseURL}/lifecycle_license/${sn}/${action}`,
        method: "GET"
    });
}

export function fetchLocalLicense(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/get_local_license`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function editLocalLicense(sn, license) {
    return request({
        url: `${baseURL}/local_license_save`,
        method: "POST",
        data: {
            sn,
            license
        }
    });
}

export function delLocalLicense(sn) {
    return request({
        url: `${baseURL}/local_license_delete`,
        method: "POST",
        data: {
            sn
        }
    });
}

export function fetchGroups() {
    return request({
        url: `${baseURL}/groups`,
        method: "Get"
    });
}

export function fetchGroupInfo(groupName) {
    return request({
        url: `${baseURL}/load_group/${groupName}`,
        method: "Get"
    });
}

export function loadGroup(groupName, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    if (groupName !== "All" && groupName) {
        return request({
            url: `${baseURL}/license_table_data/${groupName}`,
            method: "POST",
            data: {
                page,
                pageSize,
                filterFields,
                sortFields,
                searchFields
            }
        });
    }
    return request({
        url: `${baseURL}/group_management/data`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function groupManagementData(groupName, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/group_management/data`,
        method: "POST",
        data: {groupName, page, pageSize, filterFields, sortFields, searchFields}
    });
}

export function fetchUpgradeSwitchTableData(
    selectedPlatform,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    if (selectedPlatform === null) {
        return request({
            url: `${baseURL}/group_management/data`,
            method: "POST",
            data: {
                page,
                pageSize,
                filterFields,
                sortFields,
                searchFields
            }
        });
    }
    return request({
        url: `${baseURL}/upgrade_table/data`,
        method: "POST",
        data: {
            selectedPlatform,
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function fetchSwitchWithPicosV(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/group_management/data_with_picos_v`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function createGroup(actionList, groupName, description, groupType) {
    return request({
        url: `${baseURL}/create_group`,
        method: "POST",
        data: {
            actionList,
            groupName,
            description,
            groupType
        }
    });
}

export function deleteGroup(groupName) {
    return request({
        url: `${baseURL}/delete_group/${groupName}`,
        method: "Get"
    });
}

export function saveGroup(actionList, groupName, delSwitches, addSwitches) {
    return request({
        url: `${baseURL}/save_group`,
        method: "POST",
        data: {
            actionList,
            groupName,
            delSwitches,
            addSwitches
        }
    });
}

export function editHostGroup(groupName, delHosts, addHosts) {
    return request({
        url: `${baseURL}/edit_host_group`,
        method: "POST",
        data: {
            groupName,
            delHosts,
            addHosts
        }
    });
}

export function deleteHostFromGroup(groupName, deviceName) {
    return request({
        url: `${baseURL}/delete_host_from_group`,
        method: "POST",
        data: {
            groupName,
            deviceName
        }
    });
}

export function updateLinkIp() {
    return request({
        url: `${baseURL}/update_link_ip_addr`,
        method: "POST"
    });
}

export function updateHostname(data) {
    return request({
        url: `${baseURL}/update_hostname`,
        method: "POST",
        data,
        timeout: 600000
    });
}

export function getFabric() {
    return request({
        url: `${baseURL}/fabric`,
        method: "Get"
    });
}

export function getSite() {
    return request({
        url: `${baseURL}/site`,
        method: "Get"
    });
}

export function createFabric(fabricName, description) {
    return request({
        url: `${baseURL}/create_fabric`,
        method: "POST",
        data: {
            fabricName,
            description
        }
    });
}

export function createSite(siteName, description) {
    const token = axiosProv.defaults.headers.common.Authorization;
    console.log(token);
    return request({
        url: `${baseURL}/create_site`,
        headers: {
            Authorization: token
        },
        method: "POST",
        data: {
            siteName,
            description
        }
    });
}

export function deleteFabric(fabricName) {
    return request({
        url: `${baseURL}/delete_fabric`,
        method: "POST",
        data: {
            fabricName
        }
    });
}

export function deleteSite(siteName) {
    const token = axiosProv.defaults.headers.common.Authorization;
    console.log(token);
    return request({
        url: `${baseURL}/delete_site`,
        headers: {
            Authorization: token
        },
        method: "POST",
        data: {
            siteName
        }
    });
}

export function loadFabric(fabricName) {
    return request({
        url: `${baseURL}/load_fabric/${fabricName}`,
        method: "Get"
    });
}

export function loadFabricSwitch(fabricName, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/load_fabric_switch`,
        method: "POST",
        data: {
            fabricName,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function loadSite(siteName) {
    return request({
        url: `${baseURL}/load_site/${siteName}`,
        method: "Get"
    });
}

export function loadSiteSwitch(siteName, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/load_site_switch`,
        method: "POST",
        data: {
            siteName,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function loadGroupSwitch(groupName, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/load_group_switch`,
        method: "POST",
        data: {
            groupName,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function editGroupTableData(groupName, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/edit_group_table_data`,
        method: "POST",
        data: {
            groupName,
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function fabricManagementData(
    fabricName,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseURL}/fabric_management/data`,
        method: "POST",
        data: {fabricName, page, pageSize, filterFields, sortFields, searchFields}
    });
}

export function siteManagementData(siteName, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/site_management/data`,
        method: "POST",
        data: {siteName, page, pageSize, filterFields, sortFields, searchFields}
    });
}

export function editFabricTableData(fabricName, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/edit_fabric_table_data`,
        method: "POST",
        data: {
            fabricName,
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function saveFabric(fabricName, delSwitches, addSwitches) {
    return request({
        url: `${baseURL}/save_fabric`,
        method: "POST",
        data: {
            fabricName,
            delSwitches,
            addSwitches
        }
    });
}
export function editSiteTableData(siteName, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/edit_site_table_data`,
        method: "POST",
        data: {
            siteName,
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function fetchSiteTableData(
    selectedSwitchIdList,
    siteName,
    layerType,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseURL}/fetch_site_table_data`,
        method: "POST",
        data: {
            selectedSwitchIdList,
            siteName,
            layerType,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function saveSite(siteName, delSwitches, addSwitches) {
    return request({
        url: `${baseURL}/save_site`,
        method: "POST",
        data: {
            siteName,
            delSwitches,
            addSwitches
        }
    });
}

export function fetchFabricInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/list_fabric`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function fetchIPclosSiteTableData(
    selectedSwitchIdList,
    siteName,
    layerType,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseURL}/fetch_site_table_data_in_ipclos`,
        method: "POST",
        data: {
            selectedSwitchIdList,
            siteName,
            layerType,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function getAllFabricSwitch() {
    return request({
        url: `${baseURL}/get_all_fabric_switch`,
        method: "Get"
    });
}

export function fetchHostInfo(groupName, page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/fetch_host_info`,
        method: "POST",
        data: {
            groupName,
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function fetchGroupDevices(groupName) {
    return request({
        url: `${baseURL}/fetch_group_devices/${groupName}`,
        method: "Get"
    });
}

export function loadSwitchGroupTableAPI(
    groupName,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseURL}/load_switch_group_table/${groupName}`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function editGroup(actionList, groupName, delSwitches, addSwitches) {
    return request({
        url: `${baseURL}/save_group`,
        method: "POST",
        data: {
            actionList,
            groupName,
            delSwitches,
            addSwitches
        }
    });
}
