import {request} from "@/utils/common/request";

const baseUrl = "/ampcon/dc_blueprint";
const baseUrl2 = "/ampcon";

// dc
export function fetchLogicalNetworksTableData(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/logical_networks_table_data`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function saveLogicalNetwork(data) {
    return request({
        url: `${baseUrl}/logical_network/save`,
        method: "POST",
        data
    });
}

export function deleteLogicalNetwork(ln_id) {
    return request({
        url: `${baseUrl}/logical_network/delete/${ln_id}`,
        method: "POST"
    });
}

export function fetchLogicalNetworkDetail(data) {
    return request({
        url: `${baseUrl}/logical_network/detail`,
        method: "POST",
        data
    });
}

export function createLogicalRouter(data) {
    return request({
        url: `${baseUrl}/logical_router/save`,
        method: "POST",
        data
    });
}

export function createLogicalSwitch(data) {
    return request({
        url: `${baseUrl}/logical_switch/save`,
        method: "POST",
        data
    });
}

export function fetchLogicalNetworkHistoryConfig(
    ln_id,
    startTime,
    endTime,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseUrl}/logical_network/history_configuration_list`,
        method: "POST",
        data: {
            ln_id,
            startTime,
            endTime,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function fetchLogicalNetworkCurrentConfig(data) {
    return request({
        url: `${baseUrl}/logical_network/current_configuration`,
        method: "POST",
        data
    });
}

export function deleteLogicalNetworkConfiguration(config_id) {
    return request({
        url: `${baseUrl}/logical_network/delete_configuration/${config_id}`,
        method: "POST"
    });
}

export function saveLogicalNetworkConfiguration(data) {
    return request({
        url: `${baseUrl}/logical_network/save_configuration`,
        method: "POST",
        data
    });
}

export function fetchNetworkAccessTableData(
    fabricId,
    azId,
    vpcName,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseUrl}/virtual_network/list_network_access`,
        method: "POST",
        data: {
            fabricId,
            azId,
            vpcName,
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function saveVirtualNetwork(data) {
    return request({
        url: `${baseUrl}/virtual_network/save`,
        method: "POST",
        data
    });
}

export function deleteLogicalRouter(data) {
    return request({
        url: `${baseUrl}/logical_router/delete`,
        method: "POST",
        data
    });
}

export function deleteLogicalSwitch(data) {
    return request({
        url: `${baseUrl}/logical_switch/delete`,
        method: "POST",
        data
    });
}

export function deleteVirtualNetwork(data) {
    return request({
        url: `${baseUrl}/virtual_network/delete`,
        method: "POST",
        data
    });
}

export function saveLogicalInterface(data) {
    return request({
        url: `${baseUrl}/logical_interface/save`,
        method: "POST",
        data
    });
}

export function deleteLogicalInterface(data) {
    return request({
        url: `${baseUrl}/logical_interface/delete`,
        method: "POST",
        data
    });
}

export function saveLogicalPort(data) {
    return request({
        url: `${baseUrl}/logical_port/save`,
        method: "POST",
        data
    });
}

export function deleteLogicalPort(data) {
    return request({
        url: `${baseUrl}/logical_port/delete`,
        method: "POST",
        data
    });
}

export function updateTopoPosition(data) {
    return request({
        url: `${baseUrl}/logical_network/update_position`,
        method: "POST",
        data
    });
}

export function generateVirtualIPrange(data) {
    return request({
        url: `${baseUrl}/logical_interface/generate_virtual_ip_range`,
        method: "POST",
        data
    });
}

export function fetchConnectableVirtualNetworks(data) {
    return request({
        url: `${baseUrl}/logical_port/list_virtual_network`,
        method: "POST",
        data
    });
}

export function fetchLogicalNewtworkList(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/logical_network/list`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function fetchLogicalRouterList(
    network_id,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseUrl}/logical_router/list`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize,
            network_id
        }
    });
}

export function fetchLogicalRouterDetail(id) {
    return request({
        url: `${baseUrl}/logical_router/detail`,
        method: "POST",
        data: {
            id
        }
    });
}

export function fetchLogicalRouterPortDetail(
    id,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseUrl}/logical_router/port_detail`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize,
            id
        }
    });
}

export function fetchLogicalRouterStatusMonitor(id) {
    return request({
        url: `${baseUrl}/logical_router/status_monitor`,
        method: "POST",
        data: {
            id
        }
    });
}

export function fetchLogicalSwitchList(
    network_id,
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseUrl}/logical_switch/list`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize,
            network_id
        }
    });
}

export function fetchLogicalSwitchPortDetail(id) {
    return request({
        url: `${baseUrl}/logical_switch/port_detail`,
        method: "POST",
        data: {
            id
        }
    });
}

export function fetchLogicalSwitchPodDetail(id) {
    return request({
        url: `${baseUrl}/logical_switch/pod_detail`,
        method: "POST",
        data: {
            id
        }
    });
}

export function fetchLogicalSwitchStatusMonitor(id) {
    return request({
        url: `${baseUrl}/logical_switch/status_monitor`,
        method: "POST",
        data: {
            id
        }
    });
}

// DC Template
export function fetchDCTemplateInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/fabric_template/list`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function cloneDCTemplateInfo(data) {
    return request({
        url: `${baseUrl}/fabric_template/clone`,
        method: "POST",
        data
    });
}

export function deleteDCTemplateInfo(data) {
    return request({
        url: `${baseUrl}/fabric_template/delete`,
        method: "POST",
        data
    });
}

export function addDCTemplateInfo(data) {
    return request({
        url: `${baseUrl}/fabric_template/save`,
        method: "POST",
        data
    });
}
export function viewDCTemplateInfo(data) {
    return request({
        url: `${baseUrl}/fabric_template/view`,
        method: "POST",
        data
    });
}
// Fabric
export function fetchFabricInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseUrl}/fabric_topo/list`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function createFabricTopo(data) {
    return request({
        url: `${baseUrl}/fabric_topo/create`,
        method: "POST",
        data
    });
}
export function editFabric(data) {
    return request({
        url: `${baseUrl}/fabric_topo/edit`,
        method: "POST",
        data
    });
}

export function viewFabric(data) {
    return request({
        url: `${baseUrl}/fabric_topo/view`,
        method: "POST",
        data
    });
}

export function deleteFabric(data) {
    return request({
        url: `${baseUrl}/fabric_topo/delete`,
        method: "POST",
        data
    });
}

export function generate_routed_interface_address(data) {
    return request({
        url: `${baseUrl2}/resource_pool/generate_routed_interface_address`,
        method: "POST",
        data
    });
}

export function load_fabric_switch(data) {
    return request({
        url: `${baseUrl2}/lifecycle/list_fabric_switch`,
        method: "POST",
        data
    });
}

export function allocate_resourced(data) {
    return request({
        url: `${baseUrl}/fabric_topo/allocate_resource`,
        method: "POST",
        data
    });
}
export function fabric_topo_auto_link(data) {
    return request({
        url: `${baseUrl}/fabric_topo/auto_link`,
        method: "POST",
        data
    });
}

// Topo Check
export function fabric_topo_check(data) {
    return request({
        url: `${baseUrl}/fabric_topo/check_link`,
        method: "POST",
        data
    });
}
// AZ
export function fetchAZInfo(data) {
    return request({
        url: `${baseUrl2}/dc_virtual_resource/get_az_by_fabric`,
        method: "POST",
        data
    });
}

// port List
export function list_port_dropdown_data(data) {
    return request({
        url: `${baseUrl}/fabric_topo/list_port_dropdown_data`,
        method: "POST",
        data
    });
}

// deployment
export function deployment_status(data) {
    return request({
        url: `${baseUrl}/fabric_topo/deployment_status`,
        method: "POST",
        data
    });
}

export function fabric_deployment(data) {
    return request({
        url: `${baseUrl}/fabric_topo/deployment`,
        method: "POST",
        data
    });
}

// campus
