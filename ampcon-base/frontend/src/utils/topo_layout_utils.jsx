import Hierarchy from "@antv/hierarchy";
import {CircularLayout, GridLayout} from "@antv/layout";
import {Point} from "@antv/x6";
import cloneDeep from "lodash/cloneDeep";

function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

function findCycleEdges(nodes, edges) {
    const fakeRootId = -1000;
    const nodeMap = new Map(nodes.map(node => [node.id, node]));
    const visited = new Set();
    const recStack = new Set();
    const cycleEdges = [];

    function dfs(nodeId, parentId) {
        if (!nodeMap.has(nodeId)) return false;
        if (recStack.has(nodeId)) return true;
        if (visited.has(nodeId)) return false;

        visited.add(nodeId);
        recStack.add(nodeId);

        const neighbors = edges
            .filter(edge => edge.source === nodeId || edge.target === nodeId)
            .map(edge => (edge.source === nodeId ? edge.target : edge.source));

        for (const neighbor of neighbors) {
            if (neighbor !== parentId && dfs(neighbor, nodeId)) {
                cycleEdges.push({source: nodeId, target: neighbor});
                return true;
            }
        }

        recStack.delete(nodeId);
        return false;
    }

    for (const node of nodes) {
        if (!visited.has(node.id) && dfs(node.id, null)) {
            const cycleEdgesWithoutFakeRoot = cycleEdges.filter(
                edge => edge.source !== fakeRootId && edge.target !== fakeRootId
            );
            if (cycleEdgesWithoutFakeRoot.length === 0) {
                return cycleEdges;
            }
            return cycleEdgesWithoutFakeRoot;
        }
    }

    return [];
}

function treeDataFormat(nodes, edges) {
    const nodesTemp = cloneDeep(nodes);
    let edgesTemp = cloneDeep(edges);
    const removedEdges = [];
    while (true) {
        const cycleEdges = findCycleEdges(nodesTemp, edgesTemp);
        if (cycleEdges.length === 0) {
            break;
        }
        const toBeRemovedCycleEdge = cycleEdges[getRandomInt(1, 100) % cycleEdges.length];
        edgesTemp = edgesTemp.filter(
            edge =>
                !(
                    (edge.source === toBeRemovedCycleEdge.source && edge.target === toBeRemovedCycleEdge.target) ||
                    (edge.source === toBeRemovedCycleEdge.target && edge.target === toBeRemovedCycleEdge.source)
                )
        );
        removedEdges.push(toBeRemovedCycleEdge);
    }
    return [nodesTemp, edgesTemp, removedEdges];
}

// Function to build tree structure from nodes and edges
function buildTree(nodes, edges, rootIdList) {
    const fakeRootId = -1000;
    nodes.push({id: fakeRootId, label: "fake root"});
    rootIdList.forEach(rootId => {
        const node = nodes.find(node => node.id === rootId);
        if (node) {
            edges.push({source: -1000, target: node.id});
        }
    });
    edges.every(edge => {
        if (edge.source > edge.target) {
            const temp = edge.source;
            edge.source = edge.target;
            edge.target = temp;
        }
    });
    const [nodesTemp, edgesTemp, removedEdges] = treeDataFormat(nodes, edges);
    // Create a map for easy lookup of nodes by their id
    const nodeMap = new Map(nodesTemp.map(node => [node.id, node]));

    // Create a map to store children for each node
    const childrenMap = new Map();

    // Initialize children maps for undirected edges
    for (const edge of edgesTemp) {
        if (!childrenMap.has(edge.source)) {
            childrenMap.set(edge.source, []);
        }
        if (!childrenMap.has(edge.target)) {
            childrenMap.set(edge.target, []);
        }
        childrenMap.get(edge.source).push(edge.target);
        childrenMap.get(edge.target).push(edge.source);
    }

    // Recursive function to build the tree and track depth
    function buildSubTree(nodeId, parentId = null, layer = 0, visited = new Set()) {
        if (visited.has(nodeId)) return null;
        visited.add(nodeId);
        const node = nodeMap.get(nodeId);
        if (!node) return null;

        const children = (childrenMap.get(nodeId) || []).filter(childId => childId !== parentId);
        const childNodes = children
            .map(childId => buildSubTree(childId, nodeId, layer + 1, visited))
            .filter(child => child !== null);

        return {
            id: node.id,
            label: node.label,
            layer,
            children: childNodes
        };
    }

    // Start building the tree from the rootId
    return [buildSubTree(fakeRootId), removedEdges];
}

export const hierarchyLayoutDataTraverse = (nodes, edges, rootId) => {
    const [data, removedEdges] = buildTree(nodes, edges, rootId);
    const result = Hierarchy.compactBox(data, {
        direction: "TB",
        getHeight: () => 80,
        getWidth: () => 120,
        getVGap: () => 80,
        getHGap: () => 1,
        getSide: () => {
            return "bottom";
        }
    });
    const model = {nodes: [], edges: []};
    const traverse = data => {
        if (data) {
            model.nodes?.push({
                id: `${data.id}`,
                label: `${data.data.label}`,
                x: data.x + 1050,
                y: data.y + 350,
                shape: "custom-node",
                layer: data.data.layer,
                attrs: {
                    body: {
                        fill: "#5F95FF",
                        stroke: "transparent"
                    }
                }
            });
        }
        if (data.children) {
            data.children.forEach(item => {
                model.edges?.push({
                    source: `${data.id}`,
                    target: `${item.id}`,
                    attrs: {
                        line: {
                            stroke: "#A2B1C3",
                            strokeWidth: 1,
                            targetMarker: null
                        }
                    }
                });
                traverse(item);
            });
        }
    };
    traverse(result);
    return model;
};

export const girdLayoutDataTraverse = (nodes, edges, width, height) => {
    const gridLayout = new GridLayout({
        type: "grid",
        width: width - 160,
        height,
        sortBy: "label",
        rows: Math.ceil(Math.sqrt(nodes.length)),
        cols: Math.max(2, Math.ceil(Math.sqrt(nodes.length)))
    });
    return gridLayout.layout({nodes, edges});
};

export const circularLayoutDataTraverse = (nodes, edges, width, height) => {
    if (nodes.length === 1) {
        return false;
    }
    const circularLayout = new CircularLayout({
        type: "circular",
        center: [(width - 160) / 2, height / 2],
        radius: height / 2
    });

    return circularLayout.layout({nodes, edges});
};

export const ellipticalLayoutDataTraverse = (nodes, edges, width, height) => {
    const cx = width / 2;
    const cy = height / 2;

    const rx = (width / 2 - 80) * 0.8;
    const ry = (height / 2 - 80) * 0.8;
    const ratio = rx / ry;
    const center = new Point(cx, cy);
    const start = new Point(cx, cy - ry);
    const stepAngle = 360 / nodes.length;
    nodes.forEach((node, index) => {
        const angle = stepAngle * index;
        const p = start.clone().rotate(-angle, center).scale(ratio, 1, center).round();
        node.x = p.x;
        node.y = p.y;
    });

    return {nodes, edges};
};

export const formatDate = date => {
    if (!date) {
        return null;
    }
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, "0");
    const dd = String(date.getDate()).padStart(2, "0");
    const hh = String(date.getHours()).padStart(2, "0");
    const min = String(date.getMinutes()).padStart(2, "0");
    return `${yyyy}-${mm}-${dd} ${hh}:${min}`;
};

export const convertToUTCString = dateString => {
    if (!dateString) {
        return null;
    }
    const date = new Date(dateString);
    const utcYear = date.getUTCFullYear();
    const utcMonth = String(date.getUTCMonth() + 1).padStart(2, "0");
    const utcDay = String(date.getUTCDate()).padStart(2, "0");
    const utcHours = String(date.getUTCHours()).padStart(2, "0");
    const utcMinutes = String(date.getUTCMinutes()).padStart(2, "0");
    const utcSeconds = String(date.getUTCSeconds()).padStart(2, "0");
    return `${utcYear}-${utcMonth}-${utcDay} ${utcHours}:${utcMinutes}:${utcSeconds}`;
};

export const calculateVertices = (sourceNode, targetNode) => {
    let sourceNodeBBox, targetNodeBBox;
    if (sourceNode === undefined || targetNode === undefined) {
        return [];
    }
    if (sourceNode.getBBox === undefined) {
        sourceNodeBBox = {
            x: sourceNode.x === undefined ? sourceNode.position.x : sourceNode.x,
            y: sourceNode.y === undefined ? sourceNode.position.y : sourceNode.y,
            width: sourceNode.size === undefined ? 36 : sourceNode.size.width,
            height: sourceNode.size === undefined ? 34 : sourceNode.size.height
        };
    } else {
        sourceNodeBBox = sourceNode.getBBox();
    }
    if (targetNode.getBBox === undefined) {
        targetNodeBBox = {
            x: targetNode.x === undefined ? targetNode.position.x : targetNode.x,
            y: targetNode.y === undefined ? targetNode.position.y : targetNode.y,
            width: targetNode.size === undefined ? 36 : targetNode.size.width,
            height: targetNode.size === undefined ? 34 : targetNode.size.height
        };
    } else {
        targetNodeBBox = targetNode.getBBox();
    }
    const sourcePoint = {
        x: sourceNodeBBox.x + sourceNodeBBox.width / 2 - 20,
        y: sourceNodeBBox.y + sourceNodeBBox.height / 2
    };
    const targetPoint = {
        x: targetNodeBBox.x + targetNodeBBox.width / 2 - 20,
        y: targetNodeBBox.y + targetNodeBBox.height / 2
    };
    let offsetMax = 30;
    if (Math.abs(sourcePoint.y - targetPoint.y) <= 50) {
        return [];
    }
    if (Math.abs(sourcePoint.y - targetPoint.y) <= 150) {
        offsetMax = (30 * Math.abs(sourcePoint.y - targetPoint.y)) / 150;
    }

    const midNode = {
        x: (targetPoint.x + sourcePoint.x) / 2,
        y: (targetPoint.y + sourcePoint.y) / 2
    };

    let offset = (targetPoint.y - sourcePoint.y) * 0.45;
    if (offset > offsetMax) {
        offset = offsetMax;
    } else if (offset < -1 * offsetMax) {
        offset = -1 * offsetMax;
    }

    if (Math.abs(sourcePoint.y - targetPoint.y) >= 300) {
        offset = (offset * Math.abs(sourcePoint.y - targetPoint.y)) / 300;
    }

    const vertices = [
        {x: targetPoint.x + (sourcePoint.x - targetPoint.x) * 0.2 + 20, y: midNode.y + offset},
        {x: sourcePoint.x - (sourcePoint.x - targetPoint.x) * 0.2 + 20, y: midNode.y - offset}
    ];

    const sourceDistance = Math.sqrt((vertices[0].x - sourcePoint.x) ** 2 + (vertices[0].y - sourcePoint.y) ** 2);
    const targetDistance = Math.sqrt((vertices[0].x - targetPoint.x) ** 2 + (vertices[0].y - targetPoint.y) ** 2);
    if (sourceDistance < targetDistance) {
        return [
            vertices[0],
            {x: midNode.x + 20, y: midNode.y}, // 1/3平缓
            vertices[1]
        ];
    }
    return [
        vertices[1],
        {x: midNode.x + 20, y: midNode.y}, // 1/3平缓
        vertices[0]
    ];
};

export const calculateSwitchViewTopoVertices = (sourcePoint, targetPoint) => {
    const midNode = {
        x: (targetPoint.x + sourcePoint.x) / 2,
        y: (targetPoint.y + sourcePoint.y) / 2
    };
    let offset = (targetPoint.x - sourcePoint.x) * 0.45;
    offset = (offset * Math.abs(sourcePoint.x - targetPoint.x)) / 300;

    const vertices = [
        {x: midNode.x + offset, y: targetPoint.y - (sourcePoint.x - targetPoint.x) * 0.2},
        {x: midNode.x - offset, y: sourcePoint.y + (sourcePoint.x - targetPoint.x) * 0.2}
    ];

    const sourceDistance = Math.sqrt((vertices[0].x - sourcePoint.x) ** 2 + (vertices[0].y - sourcePoint.y) ** 2);
    const targetDistance = Math.sqrt((vertices[0].x - targetPoint.x) ** 2 + (vertices[0].y - targetPoint.y) ** 2);

    if (sourceDistance < targetDistance) {
        return [
            vertices[0],
            {x: midNode.x, y: midNode.y}, // 1/3平缓
            vertices[1]
        ];
    }
    return [
        vertices[1],
        {x: midNode.x, y: midNode.y}, // 1/3平缓
        vertices[0]
    ];
};

export const unitTopoLayout = (leafNodeID, point, unitInfo, dottedFrameWidth) => {
    const {leafNodes, accessNodes} = unitInfo;

    const leafNodes_1 = [];

    const noneNodeWidth = 225;
    const mlagNodeWidth = 100;
    const nodeInterval = 25;
    const nodeHeight = 30;

    let nodeX = point.x;
    let nodeY = point.y;
    let rowNodeMaxCount = 8;
    let nodeID = leafNodeID;

    // 计算节点标签
    const calcNodeLabel = (nodeName, maxTextLength) => {
        const textUnitWidth = 8;
        const fullTextWidth = nodeName.length * textUnitWidth;
        if (maxTextLength > fullTextWidth) {
            return {textLength: fullTextWidth, nodeLabel: nodeName};
        }
        const allowedChars = Math.floor(maxTextLength / textUnitWidth) - 3;
        return {textLength: maxTextLength, nodeLabel: `${nodeName.substring(0, allowedChars)}...`};
    };

    const calcNewWidths = (leafNodes, dottedFrameWidth) => {
        let newNoneNodeWidth, newMLAGNodeWidth;
        let isUseNewWidth = false;

        if (leafNodes && leafNodes.length >= 4) {
            if (dottedFrameWidth > noneNodeWidth * 4 + nodeInterval * 5) {
                newNoneNodeWidth = (dottedFrameWidth - 5 * nodeInterval) / 4;
                newMLAGNodeWidth = (newNoneNodeWidth - nodeInterval) / 2;
                isUseNewWidth = true;
            }
        } else if (leafNodes) {
            if (dottedFrameWidth > noneNodeWidth * leafNodes.length + nodeInterval * (leafNodes.length + 1)) {
                newNoneNodeWidth = (dottedFrameWidth - (leafNodes.length + 1) * nodeInterval) / leafNodes.length;
                newMLAGNodeWidth = (newNoneNodeWidth - nodeInterval) / 2;
                isUseNewWidth = true;
            }
        }

        return {newNoneNodeWidth, newMLAGNodeWidth, isUseNewWidth};
    };

    // 预计算新宽度
    const {newNoneNodeWidth, newMLAGNodeWidth, isUseNewWidth} = calcNewWidths(leafNodes, dottedFrameWidth);

    if (leafNodes !== undefined) {
        leafNodes?.forEach(node => {
            // 判断当前节点的迭代次数，MLAG需要生成两个节点
            const iterations = node.strategy === "MLAG" ? 2 : 1;
            for (let i = 1; i <= iterations; i++) {
                const currentStrategy = node.strategy;
                // 选择使用新宽度还是默认宽度
                let maxTextLength, nodeWidth;
                if (isUseNewWidth) {
                    if (currentStrategy === "MLAG") {
                        maxTextLength = newMLAGNodeWidth - 10;
                        nodeWidth = newMLAGNodeWidth;
                    } else {
                        maxTextLength = newNoneNodeWidth - 10;
                        nodeWidth = newNoneNodeWidth;
                    }
                } else if (currentStrategy === "MLAG") {
                    maxTextLength = mlagNodeWidth - 10;
                    nodeWidth = mlagNodeWidth;
                } else {
                    maxTextLength = noneNodeWidth - 10;
                    nodeWidth = noneNodeWidth;
                }

                // 生成节点名称
                const baseName = node.name === "" ? "no name" : node.name;
                const nodeName = currentStrategy === "MLAG" && i === 2 ? `${baseName}_2` : `${baseName}_1`;

                // 计算节点标签
                const {textLength, nodeLabel} = calcNodeLabel(nodeName, maxTextLength);

                const nodeInfo = {
                    ...node,
                    id: `node_${nodeID++}`,
                    label: nodeLabel,
                    name: nodeName,
                    width: nodeWidth,
                    height: nodeHeight,
                    x: nodeX,
                    y: nodeY,
                    shape: "leafNode",
                    type: "leaf",
                    attrs: {
                        labels: {
                            textLength
                        }
                    }
                };

                leafNodes_1.push(nodeInfo);

                if (isUseNewWidth) {
                    if (currentStrategy === "MLAG") {
                        rowNodeMaxCount -= 1;
                        nodeX = rowNodeMaxCount !== 0 ? nodeX + newMLAGNodeWidth + nodeInterval : point.x;
                    } else {
                        rowNodeMaxCount -= 2;
                        nodeX = rowNodeMaxCount !== 0 ? nodeX + newNoneNodeWidth + nodeInterval : point.x;
                    }
                } else if (currentStrategy === "MLAG") {
                    rowNodeMaxCount -= 1;
                    nodeX = rowNodeMaxCount !== 0 ? nodeX + mlagNodeWidth + nodeInterval : point.x;
                } else {
                    rowNodeMaxCount -= 2;
                    nodeX = rowNodeMaxCount !== 0 ? nodeX + noneNodeWidth + nodeInterval : point.x;
                }

                if (rowNodeMaxCount === 0) {
                    nodeX = point.x;
                    nodeY += nodeHeight + nodeInterval;
                    rowNodeMaxCount = 8;
                }
            }
        });
    }

    if (accessNodes !== undefined) {
        // NodeX = point.x - mlagNodeWidth - nodeInterval;
        // const accessNodes_1 = accessNodes?.map((node, index) => {
        //     const uniqueId = `id_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        //     NodeX += mlagNodeWidth + nodeInterval;
        //     const nodeName = node.name === "" ? "no name" : node.name;
        //     let textLength;
        //     let nodeLabel;
        //     if (mlagNodeWidth > nodeName.length * 8) {
        //         textLength = nodeName.length * 8;
        //         nodeLabel = nodeName;
        //     } else {
        //         textLength = mlagNodeWidth;
        //         nodeLabel = `${nodeName.substring(0, Math.floor(mlagNodeWidth / 8) - 3)}...`;
        //     }
        //     const autoNodeInterval = (maxWidth - accessNodes.length * mlagNodeWidth) / (accessNodes.length + 1);
        //     return {
        //         ...node,
        //         id: uniqueId,
        //         label: nodeLabel,
        //         name: nodeName,
        //         width: mlagNodeWidth,
        //         height: 30,
        //         type: "access",
        //         x:
        //             accessNodes.length === maxNodesCount
        //                 ? NodeX
        //                 : point.x + (autoNodeInterval + mlagNodeWidth) * index + autoNodeInterval,
        //         y: point.y + 280,
        //         ports: {
        //             groups: {
        //                 in: {
        //                     position: "top",
        //                     attrs: {
        //                         circle: {r: 1, magnet: false, stroke: "none", strokeWidth: 1, fill: "none"}
        //                     }
        //                 }
        //             },
        //             items: [{group: "in", id: "accessPort"}]
        //         },
        //         attrs: {
        //             body: {
        //                 fill: "#9B43FF",
        //                 fillOpacity: 0.1,
        //                 stroke: "transparent",
        //                 rx: 4,
        //                 ry: 4
        //             },
        //             label: {
        //                 textLength,
        //                 fill: "#9B43FF",
        //                 fontSize: 14,
        //                 fontWeight: 500,
        //                 textAnchor: "middle"
        //             }
        //         }
        //     };
        // });
        // const addEdge = (edges, source, target) => {
        //     edges.push({
        //         source,
        //         target,
        //         sourcePort: "leafBottomPort",
        //         targetPort: "accessPort",
        //         attrs: {
        //             line: {
        //                 stroke: "#D8D8D8",
        //                 strokeWidth: 1.5,
        //                 targetMarker: {
        //                     name: "block",
        //                     size: 8,
        //                     height: 12,
        //                     fill: "none",
        //                     stroke: " #D8D8D8",
        //                     open: true
        //                 }
        //             }
        //         }
        //     });
        // };
        // const edges = [];
        // accessNodes?.forEach(node => {
        //     if (node.name !== "" && node.leaf !== "") {
        //         if (node.peer_leaf !== "") {
        //             const sourceNode = leafNodes_1.find(item => item?.name === `${node.peer_leaf}`)?.id;
        //             if (sourceNode === undefined) {
        //                 return;
        //             }
        //             const targetNode = accessNodes_1.find(item => item?.name === node.name)?.id;
        //             addEdge(edges, sourceNode, targetNode);
        //             return;
        //         }
        //         if (node.access_method === "Dual-Homed") {
        //             const sourceNode_1 = leafNodes_1.find(item => item?.name === `${node.leaf}_1`)?.id;
        //             if (sourceNode_1 === undefined) {
        //                 return;
        //             }
        //             const sourceNode_2 = leafNodes_1.find(item => item?.name === `${node.leaf}_2`)?.id;
        //             const targetNode = accessNodes_1.find(item => item?.name === node.name)?.id;
        //             addEdge(edges, sourceNode_1, targetNode);
        //             addEdge(edges, sourceNode_2, targetNode);
        //         } else {
        //             const sourceNode = leafNodes_1.find(item => item?.name === `${node.leaf}_1`)?.id;
        //             if (sourceNode === undefined) {
        //                 return;
        //             }
        //             const targetNode = accessNodes_1.find(item => item?.name === node.name)?.id;
        //             addEdge(edges, sourceNode, targetNode);
        //         }
        //     }
        // });
    }
    leafNodes_1.push(nodeID);
    return leafNodes_1;
};
