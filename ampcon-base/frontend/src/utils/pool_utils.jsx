export const isRangeConflict = (ranges, i) => {
    const range = ranges[i];
    for (let j = 0; j < ranges.length; j++) {
        if (i === j) continue;
        const otherRange = ranges[j];
        if (range.startASN <= otherRange.endASN && range.endASN >= otherRange.startASN) {
            return true;
        }
    }
    return false;
};

export const isRangeListConflict = (ranges, maxValue = 4294967295) => {
    for (let i = 0; i < ranges.length; i++) {
        const range = ranges[i];
        if (!range?.startASN || !range?.endASN) {
            return true;
        }
        if (range.startASN < 1 || range.startASN > maxValue || range.endASN < 1 || range.endASN > maxValue) {
            return true;
        }
        if (range.startASN >= range.endASN) {
            return true;
        }
        if (isRangeConflict(ranges, i)) {
            return true;
        }
    }
    return false;
};

export const rangeInputValidator = (formRef, index, isStart = true, maxValue = 4294967295) => {
    const startInputValue = formRef.getFieldsValue().fields[index]?.startASN;
    const endInputValue = formRef.getFieldsValue().fields[index]?.endASN;
    if (
        (isStart && (!startInputValue || startInputValue < 1 || startInputValue > maxValue)) ||
        (!isStart && (!endInputValue || endInputValue < 1 || endInputValue > maxValue))
    ) {
        return Promise.reject(new Error(`Value must be between 1 and ${maxValue}`));
    }
    if (
        (!startInputValue && startInputValue !== 0) ||
        (!endInputValue && endInputValue !== 0) ||
        startInputValue >= endInputValue
    ) {
        return Promise.reject(new Error("Start value must be less than end value"));
    }
    const filterFields = formRef.getFieldsValue().fields.filter(item => item !== undefined);
    if (isRangeConflict(filterFields, index)) {
        return Promise.reject(new Error("Conflict with other ranges"));
    }
    return Promise.resolve();
};

export const rangeInputValidatorWithDefaultValueCheck = (
    formRef,
    formData,
    index,
    isStart = true,
    maxValue = 4294967295
) => {
    const startDefaultValue = formData[index] ? formData[index].startASN : "";
    const endDefaultValue = formData[index] ? formData[index].endASN : "";
    const startInputValue = formRef.getFieldsValue().fields[index]?.startASN;
    const endInputValue = formRef.getFieldsValue().fields[index]?.endASN;
    if (
        (isStart && (!startInputValue || startInputValue < 1 || startInputValue > maxValue)) ||
        (!isStart && (!endInputValue || endInputValue < 1 || endInputValue > maxValue))
    ) {
        return Promise.reject(new Error(`Value must be between 1 and ${maxValue}`));
    }
    if (
        (!startInputValue && startInputValue !== 0) ||
        (!endInputValue && endInputValue !== 0) ||
        startInputValue >= endInputValue
    ) {
        return Promise.reject(new Error("Start value must be less than end value"));
    }
    if (
        (formData[index].status && startInputValue > startDefaultValue && startInputValue < endDefaultValue) ||
        (formData[index].status && endInputValue < endDefaultValue && endInputValue > startDefaultValue)
    ) {
        return Promise.reject(
            new Error(`Existing range cannot be reduced (${startDefaultValue} - ${endDefaultValue})`)
        );
    }
    const filterFields = formRef.getFieldsValue().fields.filter(item => item !== undefined);
    if (isRangeConflict(filterFields, index)) {
        return Promise.reject(new Error("Conflict with other ranges"));
    }
    return Promise.resolve();
};
