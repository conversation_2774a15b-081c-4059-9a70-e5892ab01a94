export const rangeConflictCheck = (ranges, extraRanges = []) => {
    const allRanges = [...ranges, ...extraRanges];
    allRanges.sort((a, b) => a.startVLAN - b.startVLAN);

    for (let i = 1; i < allRanges.length; i++) {
        if (allRanges[i].startVLAN <= allRanges[i - 1].endVLAN) {
            return true;
        }
    }
    return false;
};

export const rangeSubmitValidator = ranges => {
    if (rangeConflictCheck(ranges)) return true;

    return ranges.some(({startVLAN, endVLAN}) => {
        if ((startVLAN == null) !== (endVLAN == null)) return true;

        if (startVLAN == null && endVLAN == null) return false;

        return startVLAN < 2 || startVLAN > 3965 || endVLAN < 2 || endVLAN > 3965 || startVLAN > endVLAN;
    });
};

export const rangeSubmitValidatorWithDefaultValueCheck = (ranges, formData) => {
    if (rangeConflictCheck(ranges)) return true;

    return ranges.some(({startVLAN, endVLAN}, index) => {
        if ((startVLAN == null) !== (endVLAN == null)) return true;

        if (startVLAN == null && endVLAN == null) return false;

        if (startVLAN < 2 || startVLAN > 3965 || endVLAN < 2 || endVLAN > 3965 || startVLAN > endVLAN) {
            return true;
        }

        const defaultStartVLAN = formData?.[index]?.startVLAN;
        const defaultEndVLAN = formData?.[index]?.endVLAN;

        if (defaultStartVLAN !== undefined && startVLAN > defaultStartVLAN) {
            return true;
        }
        if (defaultEndVLAN !== undefined && endVLAN < defaultEndVLAN) {
            return true;
        }
        return false;
    });
};

export const rangeInputValidator = (name, formRef, index, isStart = true) => {
    const fieldName = name === "globalVlanFields" ? "globalVlanFields" : "globalVniVlanFields";
    const startInputValue = formRef.getFieldsValue()[fieldName][index]?.startVLAN;
    const endInputValue = formRef.getFieldsValue()[fieldName][index]?.endVLAN;

    if (startInputValue !== 0 && !startInputValue && endInputValue !== 0 && !endInputValue) {
        return Promise.resolve();
    }
    if (
        (isStart && (!startInputValue || startInputValue < 2 || startInputValue > 3965)) ||
        (!isStart && (!endInputValue || endInputValue < 2 || endInputValue > 3965))
    ) {
        return Promise.reject(new Error("Value must be between 2 and 3965"));
    }
    if (
        (!startInputValue && startInputValue !== 0) ||
        (!endInputValue && endInputValue !== 0) ||
        startInputValue > endInputValue
    ) {
        return Promise.reject(new Error("Start value must be less than end value"));
    }
    const filterFields = formRef.getFieldsValue()[fieldName].filter(item => item !== undefined);
    if (rangeConflictCheck(filterFields)) {
        return Promise.reject(new Error("Conflict with other ranges"));
    }
    return Promise.resolve();
};

export const rangeInputValidatorWithDefaultValueCheck = (name, formRef, formData, index, isStart = true) => {
    const fieldName = name === "globalVlanFields" ? "globalVlanFields" : "globalVniVlanFields";
    const startInputValue = formRef.getFieldsValue()[fieldName][index]?.startVLAN;
    const endInputValue = formRef.getFieldsValue()[fieldName][index]?.endVLAN;

    if (startInputValue !== 0 && !startInputValue && endInputValue !== 0 && !endInputValue) {
        return Promise.resolve();
    }
    if (
        (isStart && (!startInputValue || startInputValue < 2 || startInputValue > 3965)) ||
        (!isStart && (!endInputValue || endInputValue < 2 || endInputValue > 3965))
    ) {
        return Promise.reject(new Error("Value must be between 2 and 3965"));
    }
    if (
        (!startInputValue && startInputValue !== 0) ||
        (!endInputValue && endInputValue !== 0) ||
        startInputValue > endInputValue
    ) {
        return Promise.reject(new Error("Start value must be less than end value"));
    }

    const defaultStartVLAN = formData?.[fieldName]?.[index]?.startVLAN;
    const defaultEndVLAN = formData?.[fieldName]?.[index]?.endVLAN;
    if (defaultStartVLAN !== undefined && startInputValue > defaultStartVLAN) {
        return Promise.reject(new Error(`Start VLAN can only be reduced (must be ≤ ${defaultStartVLAN})`));
    }
    if (defaultEndVLAN !== undefined && endInputValue < defaultEndVLAN) {
        return Promise.reject(new Error(`End VLAN can only be increased (must be ≥ ${defaultEndVLAN})`));
    }
    const filterFields = formRef.getFieldsValue()[fieldName].filter(item => item !== undefined);
    if (rangeConflictCheck(filterFields)) {
        return Promise.reject(new Error("Conflict with other ranges"));
    }
    return Promise.resolve();
};
