import {fetchVirtualResourceVMInfo} from "@/modules-ampcon/apis/dc_virtual_resource_api";
import {AmpConCustomTable, createColumnConfig} from "@/modules-ampcon/components/custom_table";
import {Card, Space} from "antd";
import React, {useRef} from "react";

const AZVM = ({az_id}) => {
    const tableRef = useRef(null);
    const configColumns = [
        {...createColumnConfig("Name", "vm_name"), width: "15%"},
        {
            ...createColumnConfig("VM IP Address", "vm_ip_address"),
            width: "15%",
            render: (_, record) => {
                return record.vm_ip_address.length > 0 ? record.vm_ip_address.join("; ") : "-";
            }
        },
        {
            ...createColumnConfig("Host IP Address", "host_ip_address"),
            width: "15%",
            render: (_, record) => {
                return record.host_ip_address ? record.host_ip_address : "-";
            }
        },
        {
            ...createColumnConfig("Network", "network_name"),
            width: "15%",
            render: (_, record) => {
                return record.network_name.length > 0 ? record.network_name.join("; ") : "-";
            }
        },
        {...createColumnConfig("Fabric", "fabric_name"), width: "15%", sorter: false},
        {...createColumnConfig("PoD", "az_name"), width: "15%", sorter: false},
        {...createColumnConfig("VPC", "vpc_name"), width: "15%"},
        {...createColumnConfig("Status", "power_status"), width: "15%"}
    ];
    const configSearchFieldsList = ["vm_name", "vm_ip_address", "host_ip_address", "network_name"];

    const configMatchFieldsList = [
        {name: "vm_name", matchMode: "fuzzy"},
        {name: "vm_ip_address", matchMode: "fuzzy"},
        {name: "host_ip_address", matchMode: "fuzzy"},
        {name: "network_name", matchMode: "fuzzy"}
    ];

    return (
        <AmpConCustomTable
            columns={configColumns}
            searchFieldsList={configSearchFieldsList}
            matchFieldsList={configMatchFieldsList}
            fetchAPIInfo={fetchVirtualResourceVMInfo}
            fetchAPIParams={[az_id]}
            ref={tableRef}
        />
    );
};
export default AZVM;
