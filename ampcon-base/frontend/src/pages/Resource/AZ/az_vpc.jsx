import {fetchVirtualResourceVPCInfo} from "@/modules-ampcon/apis/dc_virtual_resource_api";
import {
    AmpConCustomTable,
    createColumnConfig,
    createColumnConfigMultipleParams,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {Card, Space} from "antd";
import React, {useRef} from "react";
import dayjs from "dayjs";

const AZVPC = ({az_id}) => {
    const tableRef = useRef(null);
    const configColumns = [
        {...createColumnConfig("VPC ID", "vpc_id"), width: "15%"},
        {...createColumnConfig("VPC Name", "vpc_name"), width: "15%"},
        createColumnConfigMultipleParams({
            title: "User",
            dataIndex: "tenant",
            filterDropdownComponent: TableFilterDropdown,
            enableFilter: false,
            enableSorter: false,
            width: "15%"
        }),
        {...createColumnConfig("Fabric", "fabric_name"), width: "15%", sorter: false},
        {...createColumnConfig("PoD", "az_name"), width: "15%", sorter: false},
        {
            ...createColumnConfig("Resource Create Time", "resource_create_time"),
            width: "15%",
            render: text => (text ? dayjs(text).format("YYYY-MM-DD HH:mm:ss") : "--")
        }
    ];
    const configSearchFieldsList = ["vpc_name", "vpc_id", "tenant", "resource_create_time"];

    const configMatchFieldsList = [
        {name: "vpc_name", matchMode: "fuzzy"},
        {name: "vpc_id", matchMode: "fuzzy"},
        {name: "tenant", matchMode: "fuzzy"},
        {name: "resource_create_time", matchMode: "fuzzy"}
    ];

    return (
        <AmpConCustomTable
            columns={configColumns}
            searchFieldsList={configSearchFieldsList}
            matchFieldsList={configMatchFieldsList}
            fetchAPIInfo={fetchVirtualResourceVPCInfo}
            fetchAPIParams={[az_id]}
            ref={tableRef}
        />
    );
};
export default AZVPC;
