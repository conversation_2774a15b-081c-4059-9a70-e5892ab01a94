import {
    deleteVirtualResourcePoolAZ,
    fetchVirtualResourcePoolAZInfo,
    addVirtualResourcePoolAZ,
    editVirtualResourcePoolAZ
} from "@/modules-ampcon/apis/dc_virtual_resource_api";
import Icon, {
    EyeOutlined,
    EyeInvisibleOutlined,
    PlusOutlined,
    MinusOutlined,
    QuestionCircleOutlined
} from "@ant-design/icons";

import {AmpConCustomTable, createColumnConfig, AmpConCustomModalForm} from "@/modules-ampcon/components/custom_table";
import {confirmModalAction, tipModalAction} from "@/modules-ampcon/components/custom_modal";
import {getFabric} from "@/modules-ampcon/apis/lifecycle_api";
import {addSvg} from "@/utils/common/iconSvg";
import {Button, Card, Form, Input, Select, Space, message, Row, Col, Tooltip} from "antd";
import React, {useEffect, useRef, useState} from "react";
import {useNavigate} from "react-router-dom";
import {AzPoolVlanInput} from "@/modules-ampcon/components/az_pool_vlan_input";
import {
    rangeConflictCheck,
    rangeSubmitValidator,
    rangeSubmitValidatorWithDefaultValueCheck,
    rangeInputValidator,
    rangeInputValidatorWithDefaultValueCheck
} from "@/utils/az_utils";
import dayjs from "dayjs";
import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";

const {Option} = Select;

const CreateAZPoolModal = ({
    form,
    formData,
    isModalOpen,
    onCancel,
    setIsEdit,
    isEdit,
    oldGlobalVlanLength,
    oldGlobalVniVlanLength
}) => {
    const [fabricOptions, setFabricOptions] = useState([]);
    const resourceType = Form.useWatch("resource_type", form);
    const [globalVlanFields, setGlobalVlanFields] = useState([{startVLAN: null, endVLAN: null}]);
    const [globalVniVlanFields, setGlobalVniVlanFields] = useState([{startVLAN: null, endVLAN: null}]);

    useEffect(() => {
        getFabric().then(response => {
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                setFabricOptions(response.data);
            }
        });
    }, []);

    useEffect(() => {
        if (isEdit && formData) {
            const defaultVlanField = [{startVLAN: null, endVLAN: null}];

            const newGlobalVlanFields = formData.globalVlanFields?.length
                ? formData.globalVlanFields
                : defaultVlanField;
            const newGlobalVniVlanFields = formData.globalVniVlanFields?.length
                ? formData.globalVniVlanFields
                : defaultVlanField;

            form.setFieldsValue({
                globalVlanFields: newGlobalVlanFields,
                globalVniVlanFields: newGlobalVniVlanFields
            });

            setGlobalVlanFields(newGlobalVlanFields);
            setGlobalVniVlanFields(newGlobalVniVlanFields);
        } else {
            form.resetFields(["globalVlanFields", "globalVniVlanFields"]);
            const defaultVlanField = [{startVLAN: null, endVLAN: null}];
            setGlobalVlanFields(defaultVlanField);
            setGlobalVniVlanFields(defaultVlanField);
        }
    }, [isEdit, formData, form]);

    const formItems = isEdit => {
        return (
            <>
                <Form.Item
                    label="Name"
                    name="name"
                    rules={[
                        {required: true, message: "Please input the name!"},
                        {max: 256, message: "Enter a maximum of 256 characters."},
                        {
                            validator: (_, value) => {
                                if (value === undefined || value === null || value === "") {
                                    return Promise.resolve();
                                }
                                if (value.includes(" ")) {
                                    return Promise.reject(new Error("Name cannot contain spaces!"));
                                }
                                const validRegex = /^[\w-]+$/;
                                if (!validRegex.test(value)) {
                                    return Promise.reject(
                                        new Error("Name can only contain letters, numbers, underscores, and hyphens.")
                                    );
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    label="Resource Type"
                    name="resource_type"
                    rules={[{required: true, message: "Please select the resource type!"}]}
                >
                    <Select style={{width: "280px"}} disabled={isEdit}>
                        <Option value="BareMetal">BareMetal</Option>
                        <Option value="vSphere">vSphere</Option>
                        <Option value="OpenStack">OpenStack</Option>
                    </Select>
                </Form.Item>
                <Form.Item
                    label="Fabric"
                    name="fabric"
                    rules={[{required: true, message: "Please select the fabric!"}]}
                >
                    <Select style={{width: "280px"}} disabled={isEdit}>
                        {fabricOptions.map(fabric => (
                            <Option
                                key={fabric}
                                value={fabric}
                                disabled={fabric === "default"}
                                style={{
                                    color: fabric === "default" ? "#B3BBC8" : "inherit"
                                }}
                            >
                                {fabric}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>
                <Form.List
                    name="globalVlanFields"
                    initialValue={globalVlanFields.length > 0 ? globalVlanFields : []}
                    onChange={setGlobalVlanFields}
                >
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map(({key, name}, index) => {
                                return (
                                    <Row key={key}>
                                        <Col>
                                            <AzPoolVlanInput
                                                startOnBlurValidator={() =>
                                                    isEdit
                                                        ? rangeInputValidatorWithDefaultValueCheck(
                                                              "globalVlanFields",
                                                              form,
                                                              formData,
                                                              index,
                                                              true
                                                          )
                                                        : rangeInputValidator("globalVlanFields", form, index, true)
                                                }
                                                endOnBlurValidator={() =>
                                                    isEdit
                                                        ? rangeInputValidatorWithDefaultValueCheck(
                                                              "globalVlanFields",
                                                              form,
                                                              formData,
                                                              index,
                                                              false
                                                          )
                                                        : rangeInputValidator("globalVlanFields", form, index, false)
                                                }
                                                label={
                                                    index === 0 ? (
                                                        <>
                                                            Global VLAN
                                                            <Tooltip title="The VLAN range that can be assigned in the availability zone. The supported input range is 2-3965.">
                                                                <QuestionCircleOutlined
                                                                    className="questioncircle-color"
                                                                    style={{marginLeft: "5px"}}
                                                                />
                                                            </Tooltip>
                                                        </>
                                                    ) : (
                                                        " "
                                                    )
                                                }
                                                startDefaultValue={globalVlanFields[index]?.startVLAN || ""}
                                                endDefaultValue={globalVlanFields[index]?.endVLAN || ""}
                                                index={index}
                                            />
                                        </Col>
                                        <Col>
                                            {index === 0 && (
                                                <Button
                                                    onClick={() => add()}
                                                    style={{
                                                        backgroundColor: "transparent",
                                                        color: "#BFBFBF",
                                                        marginBottom: "24px"
                                                    }}
                                                    type="link"
                                                    icon={<PlusOutlined />}
                                                />
                                            )}
                                            {index > 0 && index > oldGlobalVlanLength - 1 && (
                                                <Button
                                                    style={{
                                                        backgroundColor: "transparent",
                                                        color: "#BFBFBF",
                                                        marginBottom: "24px"
                                                    }}
                                                    type="link"
                                                    icon={<MinusOutlined />}
                                                    onClick={() => {
                                                        remove(name);
                                                    }}
                                                />
                                            )}
                                        </Col>
                                    </Row>
                                );
                            })}
                        </>
                    )}
                </Form.List>
                {resourceType === "OpenStack" && (
                    <>
                        <Form.List
                            name="globalVniVlanFields"
                            initialValue={globalVniVlanFields.length > 0 ? globalVniVlanFields : []}
                            onChange={setGlobalVniVlanFields}
                        >
                            {(fields, {add, remove}) => (
                                <>
                                    {fields.map(({key, name}, index) => {
                                        return (
                                            <Row key={key}>
                                                <Col>
                                                    <AzPoolVlanInput
                                                        startOnBlurValidator={() =>
                                                            isEdit
                                                                ? rangeInputValidatorWithDefaultValueCheck(
                                                                      "globalVniVlanFields",
                                                                      form,
                                                                      formData,
                                                                      index,
                                                                      true
                                                                  )
                                                                : rangeInputValidator(
                                                                      "globalVniVlanFields",
                                                                      form,
                                                                      index,
                                                                      true
                                                                  )
                                                        }
                                                        endOnBlurValidator={() =>
                                                            isEdit
                                                                ? rangeInputValidatorWithDefaultValueCheck(
                                                                      "globalVniVlanFields",
                                                                      form,
                                                                      formData,
                                                                      index,
                                                                      false
                                                                  )
                                                                : rangeInputValidator(
                                                                      "globalVniVlanFields",
                                                                      form,
                                                                      index,
                                                                      false
                                                                  )
                                                        }
                                                        label={
                                                            index === 0 ? (
                                                                <>
                                                                    Global VNI VLAN
                                                                    <Tooltip title="The VLAN used within the VXLAN network of the cloud platform needs to be assigned from AmpCon-DC.The supported input range is 2-3965.">
                                                                        <QuestionCircleOutlined
                                                                            className="questioncircle-color"
                                                                            style={{marginLeft: "5px"}}
                                                                        />
                                                                    </Tooltip>
                                                                </>
                                                            ) : (
                                                                " "
                                                            )
                                                        }
                                                        startDefaultValue={globalVniVlanFields[index]?.startVLAN || ""}
                                                        endDefaultValue={globalVniVlanFields[index]?.endVLAN || ""}
                                                        index={index}
                                                    />
                                                </Col>
                                                <Col>
                                                    {index === 0 && (
                                                        <Button
                                                            onClick={() => add()}
                                                            style={{
                                                                backgroundColor: "transparent",
                                                                color: "#BFBFBF",
                                                                marginBottom: "24px"
                                                            }}
                                                            type="link"
                                                            icon={<PlusOutlined />}
                                                        />
                                                    )}
                                                    {index > 0 && index > oldGlobalVniVlanLength - 1 && (
                                                        <Button
                                                            style={{
                                                                backgroundColor: "transparent",
                                                                color: "#BFBFBF",
                                                                marginBottom: "24px"
                                                            }}
                                                            type="link"
                                                            icon={<MinusOutlined />}
                                                            onClick={() => {
                                                                remove(name);
                                                            }}
                                                        />
                                                    )}
                                                </Col>
                                            </Row>
                                        );
                                    })}
                                </>
                            )}
                        </Form.List>
                        <div
                            style={{
                                fontSize: "18px",
                                fontWeight: "bold",
                                marginBottom: "26px",
                                borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
                                paddingBottom: "10px",
                                marginLeft: "-32px",
                                marginRight: "-32px",
                                paddingLeft: "32px",
                                paddingRight: "32px"
                            }}
                        >
                            OpenStack Info
                        </div>
                        <Form.Item
                            label="Auth URL"
                            name="auth_url"
                            rules={[
                                {required: true, message: "Please input the auth URL!"},
                                {
                                    pattern: /^(https?:\/\/)?([\w-]+(\.[\w-]+)+)(:\d+)?(\/.*)?$/,
                                    message: "Invalid URL format!"
                                }
                            ]}
                        >
                            <Input style={{width: "280px"}} />
                        </Form.Item>
                        <Form.Item
                            label="Username"
                            name="username"
                            rules={[{required: true, message: "Please input the username!"}]}
                        >
                            <Input style={{width: "280px"}} />
                        </Form.Item>
                        <Form.Item
                            label="Password"
                            name="password"
                            rules={[{required: true, message: "Please input the password!"}]}
                        >
                            <Input.Password
                                iconRender={visible => {
                                    return visible ? (
                                        <EyeOutlined style={{color: "#c5c5c5"}} />
                                    ) : (
                                        <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                    );
                                }}
                                style={{width: "280px"}}
                            />
                        </Form.Item>
                        <Form.Item
                            label="User Domain Name"
                            name="user_domain_name"
                            rules={[{required: true, message: "Please input the user domain name!"}]}
                        >
                            <Input style={{width: "280px"}} />
                        </Form.Item>
                        <Form.Item
                            label="Project Name"
                            name="project_name"
                            rules={[{required: true, message: "Please input the project name!"}]}
                        >
                            <Input style={{width: "280px"}} />
                        </Form.Item>
                        <Form.Item
                            label="Project Domain Name"
                            name="project_domain_name"
                            rules={[{required: true, message: "Please input the project domain name!"}]}
                        >
                            <Input style={{width: "280px"}} />
                        </Form.Item>
                    </>
                )}
                {resourceType === "vSphere" && (
                    <>
                        <div
                            style={{
                                fontSize: "18px",
                                fontWeight: "bold",
                                marginBottom: "26px",
                                paddingBottom: "10px",
                                marginLeft: "-32px",
                                marginRight: "-32px",
                                paddingLeft: "32px",
                                paddingRight: "32px",
                                position: "relative"
                            }}
                        >
                            <div
                                style={{
                                    position: "absolute",
                                    bottom: 0,
                                    left: "24px",
                                    right: "24px",
                                    height: "1px",
                                    backgroundColor: "rgba(5, 5, 5, 0.06)"
                                }}
                            />
                            vSphere Info
                        </div>
                        <Form.Item
                            label="Version"
                            name="version"
                            rules={[{required: true, message: "Please select the version!"}]}
                        >
                            <Select style={{width: "280px"}}>
                                <Select.Option value="8.0">8.0</Select.Option>
                            </Select>
                        </Form.Item>
                        <Form.Item
                            label="IP Address"
                            name="ip_address"
                            rules={[
                                {required: true, message: "Please input the IP address!"},
                                {
                                    validator: (_, value) => {
                                        if (!value) {
                                            return Promise.resolve();
                                        }
                                        const ipv4Pattern =
                                            /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/;
                                        const ipv6Pattern =
                                            /^(([\dA-Fa-f]{1,4}:){7}[\dA-Fa-f]{1,4}|([\dA-Fa-f]{1,4}:){1,7}:|([\dA-Fa-f]{1,4}:){1,6}:[\dA-Fa-f]{1,4}|([\dA-Fa-f]{1,4}:){1,5}(:[\dA-Fa-f]{1,4}){1,2}|([\dA-Fa-f]{1,4}:){1,4}(:[\dA-Fa-f]{1,4}){1,3}|([\dA-Fa-f]{1,4}:){1,3}(:[\dA-Fa-f]{1,4}){1,4}|([\dA-Fa-f]{1,4}:){1,2}(:[\dA-Fa-f]{1,4}){1,5}|[\dA-Fa-f]{1,4}:((:[\dA-Fa-f]{1,4}){1,6})|:((:[\dA-Fa-f]{1,4}){1,7}|:)|fe80:(:[\dA-Fa-f]{0,4}){0,4}%[\dA-Za-z]+|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}\d){0,1}\d)\.){3}(25[0-5]|(2[0-4]|1{0,1}\d){0,1}\d)|([\dA-Fa-f]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}\d){0,1}\d)\.){3}(25[0-5]|(2[0-4]|1{0,1}\d){0,1}\d))$/;

                                        if (!ipv4Pattern.test(value) && !ipv6Pattern.test(value)) {
                                            return Promise.reject(new Error("Invalid IP address format!"));
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        >
                            <Input style={{width: "280px"}} />
                        </Form.Item>
                        <Form.Item
                            label="Username"
                            name="username"
                            rules={[{required: true, message: "Please input the username!"}]}
                        >
                            <Input style={{width: "280px"}} />
                        </Form.Item>
                        <Form.Item
                            label="Port"
                            name="port"
                            rules={[
                                {required: true, message: "Please input the port!"},
                                {
                                    validator: (_, value) => {
                                        if (value < 1 || value > 65535) {
                                            return Promise.reject(new Error("Port must be between 1 and 65535."));
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        >
                            <Input type="number" style={{width: "280px"}} />
                        </Form.Item>
                        <Form.Item
                            label="Password"
                            name="password"
                            rules={[{required: true, message: "Please input the password!"}]}
                        >
                            <Input.Password
                                iconRender={visible => {
                                    return visible ? (
                                        <EyeOutlined style={{color: "#c5c5c5"}} />
                                    ) : (
                                        <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                    );
                                }}
                                style={{width: "280px"}}
                            />
                        </Form.Item>
                    </>
                )}
            </>
        );
    };

    const filterVlanFields = fields =>
        fields
            ?.map(field =>
                field.startVLAN == null && field.endVLAN == null
                    ? null
                    : {
                          startVLAN: field.startVLAN,
                          endVLAN: field.endVLAN
                      }
            )
            .filter(field => field !== null) || [];

    const onSubmit = async values => {
        const id = form.getFieldValue("id");
        const globalVlanFields = filterVlanFields(values.globalVlanFields);
        const globalVniVlanFields = filterVlanFields(values.globalVniVlanFields);
        if (values.resource_type === "OpenStack" && globalVlanFields.length === 0 && globalVniVlanFields.length === 0) {
            message.error("Validation failed: At least one of Global Vlan and Global Vni Vlan must be filled in");
            return;
        }
        if (values.resource_type === "OpenStack" && rangeConflictCheck(globalVlanFields, globalVniVlanFields)) {
            message.error("Validation failed: Global VLAN and Global VNI VLAN ranges cannot overlap.");
            return;
        }
        const veriMethod = isEdit ? rangeSubmitValidatorWithDefaultValueCheck : rangeSubmitValidator;

        if (veriMethod(globalVlanFields, formData.globalVlanFields)) {
            message.error("Validation failed: Global VLAN range is invalid.");
            return;
        }
        if (veriMethod(globalVniVlanFields, formData.globalVniVlanFields)) {
            message.error("Validation failed: Global VNI VLAN range is invalid.");
            return;
        }
        const data = {};
        data.az_name = values.name;
        data.resource_type = values.resource_type;
        data.fabric_name = values.fabric;
        data.global_vlan = globalVlanFields;
        switch (values.resource_type) {
            case "vSphere":
                data.auth_info = {
                    server_ip: values.ip_address,
                    username: values.username,
                    password: values.password,
                    server_port_num: values.port,
                    version: values.version
                };
                break;
            // eslint-disable-next-line no-fallthrough
            case "OpenStack":
                data.global_vni_vlan = globalVniVlanFields;
                data.auth_info = {
                    auth_url: values.auth_url,
                    username: values.username,
                    password: values.password,
                    user_domain_name: values.user_domain_name,
                    project_name: values.project_name,
                    project_domain_name: values.project_domain_name
                };
                break;
            // eslint-disable-next-line no-fallthrough
            case "BareMetal":
                break;
            default:
                break;
        }
        if (isEdit) {
            data.id = id;
            const response = await editVirtualResourcePoolAZ(data);
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                message.success(response.info);
                onCancelModal();
            }
        } else {
            const response = await addVirtualResourcePoolAZ(data);
            if (response.status !== 200) {
                message.error(response.info);
            } else {
                message.success(response.info);
                onCancelModal();
            }
        }
    };

    const onCancelModal = () => {
        form.resetFields();
        setIsEdit(false);
        onCancel();
    };

    return (
        <AmpConCustomModalForm
            title={isEdit ? "Edit PoD" : "Create PoD"}
            isModalOpen={isModalOpen}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 6
                }
            }}
            CustomFormItems={() => formItems(isEdit)}
            onCancel={onCancelModal}
            onSubmit={onSubmit}
            modalClass="ampcon-middle-modal"
        />
    );
};

const AZPool = () => {
    const tableRef = useRef(null);
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const navigate = useNavigate();
    const [isEdit, setIsEdit] = useState(false);
    const [form] = Form.useForm();
    const [oldGlobalVlanLength, setOldGlobalVlanLength] = useState(0);
    const [oldGlobalVniVlanLength, setOldGlobalVniVlanLength] = useState(0);
    const [formData, setFormData] = useState({});

    const edit_az_pool = record => {
        setIsEdit(true);
        setIsCreateModalOpen(true);
        const globalVlanFields =
            typeof record.global_vlan === "string"
                ? JSON.parse(record.global_vlan.replace(/'/g, '"'))
                : record.global_vlan || [];

        const globalVniVlanFields =
            typeof record.global_vni_vlan === "string"
                ? JSON.parse(record.global_vni_vlan.replace(/'/g, '"'))
                : record.global_vni_vlan || [];

        setFormData({globalVlanFields, globalVniVlanFields});

        setOldGlobalVlanLength(globalVlanFields.length);
        setOldGlobalVniVlanLength(globalVniVlanFields.length);
        form.setFieldsValue({
            id: record.id,
            name: record.az_name,
            resource_type: record.resource_type,
            fabric: record.fabric_name,
            globalVlanFields,
            globalVniVlanFields,
            auth_url: record.auth_info?.auth_url || "",
            username: record.auth_info?.username || "",
            password: record.auth_info?.password || "",
            user_domain_name: record.auth_info?.user_domain_name || "",
            project_name: record.auth_info?.project_name || "",
            project_domain_name: record.auth_info?.project_domain_name || "",
            version: record.auth_info?.version || "",
            ip_address: record.auth_info?.server_ip || "",
            port: record.auth_info?.server_port_num || ""
        });
    };

    const formatVlanRanges = vlanData => {
        if (!vlanData || vlanData === "[]" || !vlanData.includes("startVLAN")) {
            return "--";
        }

        if (typeof vlanData === "string") {
            try {
                const vlanArray = JSON.parse(
                    vlanData.replace(/'startVLAN':/g, '"startVLAN":').replace(/'endVLAN':/g, '"endVLAN":')
                );
                return vlanArray.map(({startVLAN, endVLAN}) => `${startVLAN ?? ""}-${endVLAN ?? ""}`).join(",");
            } catch {
                return vlanData;
            }
        }

        return vlanData;
    };

    const configColumns = [
        {
            ...createColumnConfig("Name", "az_name"),
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                navigate(`/resource/resource_interconnection/PoD/${record.az_name}`, {
                                    state: {id: record.id, resource_type: record.resource_type}
                                });
                            }}
                        >
                            {record.az_name}
                        </a>
                    </Space>
                );
            }
        },
        {...createColumnConfig("Resource Type", "resource_type")},
        {
            title: "IP Address",
            render: (_, record) => {
                switch (record.resource_type) {
                    case "vSphere":
                        return <p>{record.auth_info.server_ip}</p>;
                    case "OpenStack":
                        const urlMatch = record.auth_info.auth_url.match(
                            /(?:http:\/\/|https:\/\/)?([\d.A-Za-z-]+|\d{1,3}(?:\.\d{1,3}){3})/
                        );

                        if (urlMatch) {
                            const address = urlMatch[1];
                            return <p>{address}</p>;
                        }
                        return <p>{record.auth_info.auth_url}</p>;

                    case "BareMetal":
                        return <p>--</p>;
                    default:
                        return <p>--</p>;
                }
            }
        },
        {...createColumnConfig("Fabric", "fabric_name")},
        {
            title: "Global VLAN",
            render: (_, record) => {
                return <p>{formatVlanRanges(record.global_vlan)}</p>;
            }
        },
        {
            title: "Global VNI VLAN",
            render: (_, record) => {
                return <p>{formatVlanRanges(record.global_vni_vlan)}</p>;
            }
        },
        {
            ...createColumnConfig("Connectivity Status", "connect_status"),
            render: (_, record) => {
                return record.connect_status ? "Active" : "Inactive";
            }
        },
        {
            ...createColumnConfig("Last Active", "connect_active_time"),
            render: (_, record) => {
                return record.connect_active_time
                    ? dayjs(record.connect_active_time).format("YYYY-MM-DD HH:mm:ss")
                    : "--";
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="middle" className={styles.actionLink}>
                            <a onClick={() => edit_az_pool(record)}>Edit</a>
                            <a
                                onClick={() => {
                                    if (record.usage_state) {
                                        return tipModalAction(
                                            "The PoD can't be deleted because node uplink is added to this PoD. Please delete node uplink first and try again.",
                                            () => {}
                                        );
                                    }
                                    confirmModalAction("Are you sure want to delete?", () => {
                                        deleteVirtualResourcePoolAZ({az_id: record.id}).then(res => {
                                            if (res.status === 200) {
                                                message.success(res.info);
                                                tableRef.current.refreshTable();
                                            } else {
                                                message.error("Failed to delete PoD Pool");
                                            }
                                        });
                                    });
                                }}
                            >
                                Delete
                            </a>
                        </Space>
                    </div>
                );
            },
            width: "25%"
        }
    ];

    const configSearchFieldsList = ["az_name", "resource_type"];

    const configMatchFieldsList = [
        {name: "az_name", matchMode: "fuzzy"},
        {name: "resource_type", matchMode: "fuzzy"},
        {name: "global_vlan", matchMode: "fuzzy"},
        {name: "global_vni_vlan", matchMode: "fuzzy"}
    ];

    return (
        <>
            <AmpConCustomTable
                columns={configColumns}
                searchFieldsList={configSearchFieldsList}
                matchFieldsList={configMatchFieldsList}
                fetchAPIInfo={fetchVirtualResourcePoolAZInfo}
                ref={tableRef}
                extraButton={
                    <Button
                        type="primary"
                        onClick={() => {
                            setFormData({});
                            setIsCreateModalOpen(true);
                            setOldGlobalVlanLength(0);
                            setOldGlobalVniVlanLength(0);
                        }}
                    >
                        <Icon component={addSvg} />
                        PoD
                    </Button>
                }
            />
            <CreateAZPoolModal
                form={form}
                isModalOpen={isCreateModalOpen}
                onCancel={() => {
                    setIsCreateModalOpen(false);
                    setIsEdit(false);
                    tableRef.current.refreshTable();
                }}
                setIsEdit={setIsEdit}
                isEdit={isEdit}
                oldGlobalVlanLength={oldGlobalVlanLength}
                oldGlobalVniVlanLength={oldGlobalVniVlanLength}
                formData={formData}
            />
        </>
    );
};

export default AZPool;
