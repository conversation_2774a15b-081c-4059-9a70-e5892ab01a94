import React, {useState} from "react";
import {useLocation} from "react-router-dom";
import {Tabs} from "antd";
import AZVPC from "./az_vpc";
import AZNetwork from "./az_network";
import AZVM from "./az_vm";

const AZDetail = () => {
    const [currentActiveKey, setCurrentActiveKey] = useState();
    const location = useLocation();
    const az_id = location.state?.id || "default";
    const resource_type = location.state?.resource_type || "default";

    const items = [
        {
            key: "vpc",
            label: "VPC",
            children: <AZVPC az_id={az_id} />
        },
        {
            key: "network",
            label: "Network",
            children: <AZNetwork az_id={az_id} />
        },
        {
            key: "vm",
            label: "VM",
            children: <AZVM az_id={az_id} />
        }
    ];
    const filteredItems = resource_type === "BareMetal" ? items.filter(item => item.key !== "vm") : items;

    // const onChange = key => {
    //     // const currentPath = location.pathname;
    //     // if (pathReg.test(currentPath)) {
    //     //     const matchLength = currentPath.match(pathReg)[0].length;
    //     //     const parentPath = currentPath.slice(0, -matchLength);
    //     //     navigate(parentPath + key);
    //     // }
    // };

    return (
        <div style={{display: "flex", flex: 1}}>
            <Tabs
                activeKey={currentActiveKey}
                items={filteredItems}
                // onChange={onChange}
                style={{flex: 1}}
                destroyInactiveTabPane
            />
        </div>
    );
};
export default AZDetail;
