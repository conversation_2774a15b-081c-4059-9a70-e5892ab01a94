import {fetchVirtualResourceNetworkInfo} from "@/modules-ampcon/apis/dc_virtual_resource_api";
import {AmpConCustomTable, createColumnConfig} from "@/modules-ampcon/components/custom_table";
import {Card, Space} from "antd";
import React, {useRef} from "react";
import dayjs from "dayjs";

const AZNetwork = ({az_id}) => {
    const tableRef = useRef(null);
    const configColumns = [
        {...createColumnConfig("Name", "network_name"), width: "15%"},
        {...createColumnConfig("VPC", "vpc_name"), width: "15%"},
        {...createColumnConfig("Fabric", "fabric_name"), width: "15%", sorter: false},
        {...createColumnConfig("PoD", "az_name"), width: "15%", sorter: false},
        {...createColumnConfig("VM Count", "vm_count"), width: "15%"},
        {...createColumnConfig("Host Count", "host_count"), width: "15%"},
        {...createColumnConfig("Vlan ID", "vlan_id"), width: "15%"},
        {
            ...createColumnConfig("Resource Create Time", "resource_create_time"),
            width: "15%",
            render: text => (text ? dayjs(text).format("YYYY-MM-DD HH:mm:ss") : "--")
        }
    ];
    const configSearchFieldsList = ["network_name", "resource_create_time"];
    const configMatchFieldsList = [{name: "network_name", matchMode: "fuzzy"}];

    return (
        <AmpConCustomTable
            columns={configColumns}
            searchFieldsList={configSearchFieldsList}
            matchFieldsList={configMatchFieldsList}
            fetchAPIInfo={fetchVirtualResourceNetworkInfo}
            fetchAPIParams={[az_id]}
            ref={tableRef}
        />
    );
};
export default AZNetwork;
