import React, {useEffect, useState} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import {useSelector} from "react-redux";
// import Upgrade from "@/modules-otn/pages/otn/device/upgrade";
// import serviceLayer0Styles from "@/modules-otn/pages/otn/service/service_layer0.module.scss";
import SwitchUpgrade from "@/modules-ampcon/pages/Resource/UpgradeManagementSwitch/Switch/switch_upgrade";
import UpgradeManagementSwitch from "@/modules-ampcon/pages/Resource/UpgradeManagementSwitch/upgrade_management_switch";

let items = [
    // {
    //     key: "otn",
    //     label: "OTN",
    //     children: <Upgrade />
    // },
    {
        key: "switch",
        label: "Switch",
        children: <UpgradeManagementSwitch />
    }
];

const UpgradeManagement = () => {
    const currentUser = useSelector(state => state.user.userInfo);
    items = currentUser.type === "readonly" ? items.filter(item => item.key !== "otn" && item.key !== "switch") : items;

    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();
    const pathReg = /(otn|switch)$/;

    useEffect(() => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(pathReg)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            const matchLength = currentPath.match(pathReg)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    const renderContent = () => {
        switch (import.meta.env.VITE_APP_EXPORT_MODULE) {
            // case "AmpCon-T":
            //     return <Upgrade />;
            // case "AmpCon-SUPER":
            //     return (
            //         <Tabs
            //             activeKey={currentActiveKey}
            //             items={items}
            //             onChange={onChange}
            //             style={{flex: 1}}
            //             className={serviceLayer0Styles.tabs}
            //             destroyInactiveTabPane
            //         />
            //     );
            case "AmpCon-CAMPUS":
                return <UpgradeManagementSwitch />;
            default:
                return <SwitchUpgrade />;
        }
    };

    return <div style={{display: "flex", flex: 1}}>{renderContent()}</div>;
};

export default UpgradeManagement;
