import {But<PERSON>, Card, Checkbox, Form, Input, message} from "antd";
import {useNavigate} from "react-router-dom";
import {useDispatch} from "react-redux";
import React, {useDeferredValue, useEffect, useState} from "react";
import Icon, {EyeInvisibleOutlined, EyeOutlined, LockOutlined, UserOutlined} from "@ant-design/icons";
import {fetchLogin} from "@/store/modules/common/user_slice";
import {fsSvg} from "@/utils/common/iconSvg";
import {checkLicense} from "@/apis/common/user";
import {bgRightSvg, customTitle} from "@/custom_modules";
import {ImportLicenseModal} from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseManagement/license_management";
import {ObtainModal} from "@/modules-ampcon/pages/System/SoftwareLicense/software_license";
import LoginPageDCStyles from "./login.dc.module.scss";
import LoginPageCampusStyles from "./login.campus.module.scss";
import LoginPageTStyles from "./login.t.module.scss";

export const styleModules = {
    "AmpCon-DC": LoginPageDCStyles,
    "AmpCon-CAMPUS": LoginPageCampusStyles,
    "AmpCon-T": LoginPageTStyles,
    "AmpCon-SUPER": LoginPageTStyles,
    default: LoginPageDCStyles
};

const Login = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [register, setRegister] = useState(true);
    const deferredRegister = useDeferredValue(register);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isObtainModalOpen, setIsObtainModalOpen] = useState(false);
    const [isFirstShowCheckLicenseMessage, setIsFirstShowCheckLicenseMessage] = useState(true);
    const onFinish = async values => {
        const checkResult = await checkLicenseStatus();
        if (checkResult === true) {
            await dispatch(fetchLogin(values, navigate));
        }
    };

    const styles = styleModules[import.meta.env.VITE_APP_EXPORT_MODULE] || styleModules.default;

    const currentYear = new Date().getFullYear();

    const standardLicensePartialExpireAlertMessage = (idsStr, type) => {
        let msg =
            "Some of the licenses are about to expire. If you wish to continue using this device, please renew the license.";
        if (type === "all") {
            msg =
                "Part of the license has expired. If you wish to continue using this device, please renew the license.";
        }
        return (
            <span>
                License ID: {idsStr} {msg} You can view the relevant information through the &#34;<a>License View</a>
                &#34; function.
            </span>
        );
    };

    const checkLicenseStatus = async () => {
        let response = {};
        let result;

        try {
            response = await checkLicense();
        } catch (error) {
            // error
        } finally {
            if (response.status === 200) {
                result = true;
            } else if (response.status === 201) {
                message.warning(response.msg, 10).then();
                result = true;
            } else if (response.status === 202) {
                const sortedIds = [...response.data].sort((a, b) => a - b);
                if (isFirstShowCheckLicenseMessage) {
                    message
                        .warning(
                            standardLicensePartialExpireAlertMessage(sortedIds.join(", "), response.expire_type),
                            0
                        )
                        .then();
                    setIsFirstShowCheckLicenseMessage(false);
                }

                localStorage.setItem(
                    "standardExpireMessage",
                    JSON.stringify({
                        type: response.expire_type,
                        ids: sortedIds
                    })
                );
                result = true;
            } else {
                setRegister(false);
                if (response.msg) {
                    message.error(response.msg, 10).then();
                } else {
                    message.error("License check failed", 10).then();
                }
                result = false;
            }
            if (response.status !== 202) {
                localStorage.removeItem("standardExpireMessage");
            }
        }
        return result;
    };

    useEffect(() => {
        if (!deferredRegister) {
            if (["AmpCon-SUPER", "AmpCon-T"].includes(import.meta.env.VITE_APP_EXPORT_MODULE)) {
                setIsObtainModalOpen(true);
            } else {
                setIsModalOpen(true);
            }
        }
    }, [deferredRegister]);

    useEffect(() => {
        checkLicenseStatus().then();
    }, []);

    return (
        <div className={styles.page_container}>
            <header className={styles.layout_header}>
                <Icon component={fsSvg} className={styles.logo_icon} />
            </header>
            <div className={styles.main_content}>
                <div className={styles.form_container}>
                    <Card className={styles.ampcon_login_card}>
                        <h1 className={styles.login_title}>Welcome to {customTitle}</h1>
                        <Form validateTrigger="onBlur" onFinish={onFinish}>
                            <h2 className={styles.login_input_label}>Username</h2>
                            <Form.Item name="username" rules={[{required: true, message: "Username is required"}]}>
                                <Input className={styles.login_input} prefix={<UserOutlined />} />
                            </Form.Item>
                            <h2 className={styles.login_input_label}>Password</h2>
                            <Form.Item
                                name="password"
                                rules={[{required: true, message: "Password is required"}]}
                                style={{marginBottom: "0px"}}
                            >
                                <Input.Password
                                    iconRender={visible => {
                                        return visible ? (
                                            <EyeOutlined style={{color: "#c5c5c5"}} />
                                        ) : (
                                            <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                        );
                                    }}
                                    className={styles.login_input}
                                    prefix={<LockOutlined />}
                                />
                            </Form.Item>
                            <div>
                                <Form.Item
                                    name="remember"
                                    valuePropName="checked"
                                    initialValue={false}
                                    style={{marginTop: "10px"}}
                                >
                                    <Checkbox style={{margin: 0}}>Keep me signed in</Checkbox>
                                </Form.Item>
                            </div>
                            <Button type="primary" htmlType="submit" className={styles.login_button}>
                                LOGIN
                            </Button>
                        </Form>
                    </Card>
                </div>
                {["AmpCon-SUPER", "AmpCon-T"].includes(import.meta.env.VITE_APP_EXPORT_MODULE) && (
                    <div className={styles.icon_container}>
                        <Icon component={bgRightSvg} />
                    </div>
                )}
                <footer className={styles.layout_footer}>
                    <a
                        style={{color: "#929A9E"}}
                        href="https://www.fs.com"
                        target="_blank"
                        rel="noreferrer"
                    >{`Copyright © 2009-${currentYear} FS.COM Inc. All Rights Reserved.`}</a>
                </footer>
                <ImportLicenseModal
                    isModalOpen={isModalOpen}
                    onCancelFunc={() => {
                        setIsModalOpen(false);
                        setRegister(true);
                        checkLicenseStatus().then();
                    }}
                />
                {(import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-T" ||
                    import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-SUPER") && (
                    <ObtainModal
                        isModalOpen={isObtainModalOpen}
                        onCancelFunc={() => {
                            setIsObtainModalOpen(false);
                            setIsModalOpen(true);
                        }}
                    />
                )}
            </div>
        </div>
    );
};

export default Login;
