import * as React from 'react';
import {
  <PERSON>con<PERSON>utton,
  <PERSON>over,
  <PERSON>over<PERSON>rrow,
  <PERSON>overBody,
  Popover<PERSON>lose<PERSON>utton,
  <PERSON>over<PERSON>ontent,
  <PERSON><PERSON>Header,
  PopoverTrigger,
} from '@chakra-ui/react';
import { Question } from '@phosphor-icons/react';

export type InfoPopoverProps = {
  title: string;
  popoverContentProps?: React.ComponentProps<typeof PopoverContent>;
  buttonProps?: React.ComponentProps<typeof IconButton>;
  children: React.ReactNode;
};

export const InfoPopover = ({ title, popoverContentProps, buttonProps, children }: InfoPopoverProps) => (
  <Popover isLazy trigger="hover" placement="auto">
    <PopoverTrigger>
      <IconButton
        aria-label="Information Hover"
        icon={<Question size={24} />}
        size="sm"
        colorScheme="blue"
        {...buttonProps}
      />
    </PopoverTrigger>
    <PopoverContent {...popoverContentProps}>
      <PopoverArrow />
      <PopoverCloseButton alignContent="center" mt={1} />
      <PopoverHeader display="flex">{title}</PopoverHeader>
      <PopoverBody>{children}</PopoverBody>
    </PopoverContent>
  </Popover>
);
