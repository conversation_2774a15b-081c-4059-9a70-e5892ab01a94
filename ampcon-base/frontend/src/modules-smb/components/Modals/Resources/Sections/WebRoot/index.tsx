import React, { useEffect, useState } from 'react';
import { Heading, Select, SimpleGrid, Text, Tab, TabList, TabPanel, TabPanels, Tabs, useToast, FormControl, FormLabel, Flex } from '@chakra-ui/react';
import { AxiosError } from 'axios';
import { Formik, FormikProps } from 'formik';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import NotesTable from '@/modules-smb/components/CustomFields/NotesTable';
import StringField from '@/modules-smb/components/FormFields/StringField';
import { useCreateResource, useUpdateResource } from '@/modules-smb/hooks/Network/Resources';
import { Note } from '@/modules-smb/models/Note';
import { Resource } from '@/modules-smb/models/Resource';
import WebRootSelector from '@/modules-smb/pages/ConfigurationPage/ConfigurationCard/ConfigurationSectionsCard/InterfaceSection/SingleInterface/SsidList/Captive/CaptiveWebRoot';


interface Props {
  isOpen: boolean;
  onClose: () => void;
  refresh: () => void;
  formRef: React.Ref<FormikProps<Record<string, unknown>>> | undefined;
  resource?: Resource;
  isDisabled?: boolean;
  parent?: {
    entity?: string;
    venue?: string;
    subscriber?: string;
  };
}

const InterfaceCaptiveWebRootResource = ({
  isOpen,
  onClose,
  refresh,
  formRef,
  resource,
  isDisabled = false,
  parent,
}: Props) => {
  const { t } = useTranslation();
  const toast = useToast();
  const [formKey, setFormKey] = useState(uuid());

  const create = useCreateResource();
  const update = useUpdateResource(resource?.id ?? '');

  useEffect(() => {
    setFormKey(uuid());
  }, [isOpen]);

  return (
    <Formik
      innerRef={formRef}
      key={formKey}
      initialValues={
        resource !== undefined && resource.variables[0]
          ? {
              editing: { 
                webroot: {
                  'web-root': resource.variables[0].value
                }
              },
              _unused_name: resource.name,
              _unused_description: resource.description,
              entity: resource.entity !== '' ? `ent:${resource.entity}` : `ven:${resource.venue}`,
              _unused_notes: resource.notes,
            }
          : {
            editing: { 
              webroot: {
                'web-root': ''
              }
            },
              _unused_name: 'WebRoot',
              _unused_description: 'click-to-continue',
              _unused_notes: [],
            }
      }
      onSubmit={async (formData, { setSubmitting, resetForm }) => {
        // 校验 web-root 是否为空
        // if (!formData.editing?.webroot?.['web-root']) {
        //   toast({
        //     id: 'web-root-required',
        //     title: t('common.error'),
        //     description: 'Web Root cannot be default',
        //     status: 'error',
        //     duration: 5000,
        //     isClosable: true,
        //     position: 'top-right',
        //   });
        //   setSubmitting(false);
        //   return;
        // }

        const after = (success: boolean) => {
          if (success) {
            setSubmitting(false);
            resetForm();
            refresh();
            onClose();
          } else {
            setSubmitting(false);
          }
        };
        
        return resource
          ? update.mutateAsync(
              {
                variables: [
                  {
                    type: 'string',
                    weight: 0,
                    prefix: 'captive-webroot',
                    value: formData.editing.webroot['web-root'],
                  },
                ],
                name: formData._unused_name,
                description: formData._unused_description,
                // @ts-ignore
                notes: formData._unused_notes.filter((note: Note) => note.isNew),
              },
              {
                onSuccess: async () => {
                  toast({
                    id: 'resource-update-success',
                    title: t('common.success'),
                    description: t('crud.success_update_obj', {
                      obj: t('resources.configuration_resource'),
                    }),
                    status: 'success',
                    duration: 5000,
                    isClosable: true,
                    position: 'top-right',
                  });
                  after(true);
                },
                // @ts-ignore
                onError: (e: AxiosError) => {
                  toast({
                    id: uuid(),
                    title: t('common.error'),
                    description: t('crud.error_update_obj', {
                      obj: t('resources.configuration_resource'),
                      e: e?.response?.data?.ErrorDescription,
                    }),
                    status: 'error',
                    duration: 5000,
                    isClosable: true,
                    position: 'top-right',
                  });
                  after(false);
                },
              },
            )
          : create.mutateAsync(
              {
                variables: [
                  {
                    type: 'string',
                    weight: 0,
                    prefix: 'captive-webroot',
                    value: formData.editing.webroot['web-root'],
                  },
                ],
                ...parent,
                name: formData._unused_name,
                description: formData._unused_description,
                // @ts-ignore
                notes: formData._unused_notes.filter((note: Note) => note.isNew),
              },
              {
                onSuccess: async () => {
                  toast({
                    id: 'user-creation-success',
                    title: t('common.success'),
                    description: t('crud.success_create_obj', {
                      obj: t('resources.configuration_resource'),
                    }),
                    status: 'success',
                    duration: 5000,
                    isClosable: true,
                    position: 'top-right',
                  });
                  after(true);
                },
                // @ts-ignore
                onError: (e: AxiosError) => {
                  toast({
                    id: uuid(),
                    title: t('common.error'),
                    description: t('crud.error_create_obj', {
                      obj: t('resources.configuration_resource'),
                      e: e?.response?.data?.ErrorDescription,
                    }),
                    status: 'error',
                    duration: 5000,
                    isClosable: true,
                    position: 'top-right',
                  });
                  after(false);
                },
              },
            );
      }}
    >
      {(formikProps: FormikProps<any>) => ( 
      <Tabs variant="enclosed">
        <TabList>
          <Tab>{t('common.main')}</Tab>
          <Tab>{t('common.notes')}</Tab>
        </TabList>
        <TabPanels>
          <TabPanel>
            <SimpleGrid minChildWidth="300px" spacing="20px" mt={4}>
              <StringField name="_unused_name" label={t('common.name')} isRequired isDisabled={isDisabled} />
              {/* <StringField name="_unused_description" label={t('common.description')} isDisabled={isDisabled} /> */}
            </SimpleGrid>
            <Heading size="md" display="flex" alignItems="center" mt={2} mb={5}>
                <Text pt={1}>Web Root</Text>
            </Heading>
            <Flex>
              <FormControl isRequired>
                <FormLabel>authMode</FormLabel>
                {/* 由于后端variables格式限制，原auto-mode使用description字段 */}
                <Select 
                  name="_unused_description"
                  w="max-content" 
                  ml={2}
                  value={formikProps.values._unused_description}
                  onChange={(e) => formikProps.setFieldValue('_unused_description', e.target.value)} 
                  isDisabled={isDisabled}
                >
                  <option value="click-to-continue">Click</option>
                  <option value="radius">Radius</option>
                  <option value="credentials">Credentials</option>
                  {/* <option value="uam">UAM</option> */}
                </Select>
              </FormControl>
              <WebRootSelector
                namePrefix="editing.webroot"
                authMode={formikProps.values._unused_description}
                isDisabled={isDisabled}
                parent={parent}
              />
            </Flex>
          </TabPanel>
          <TabPanel>
            <NotesTable name="_unused_notes" isDisabled={isDisabled} />
          </TabPanel>
        </TabPanels>
      </Tabs>
      )}
    </Formik>
  );
};

export default InterfaceCaptiveWebRootResource;
