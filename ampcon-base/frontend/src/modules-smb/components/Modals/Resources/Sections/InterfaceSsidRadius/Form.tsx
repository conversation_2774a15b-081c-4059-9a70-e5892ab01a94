import React, { useMemo } from 'react';
import { FormControl, FormLabel, Heading, SimpleGrid, Switch } from '@chakra-ui/react';
import { useFormikContext, getIn } from 'formik';
import { useTranslation } from 'react-i18next';
import { INTERFACE_SSID_RADIUS_LOCAL_SCHEMA, INTERFACE_SSID_RADIUS_LOCAL_USER_SCHEMA } from './schemas';
import NumberField from '@/modules-smb/components/FormFields/NumberField';
import ObjectArrayFieldModal from '@/modules-smb/components/FormFields/ObjectArrayFieldModal';
import StringField from '@/modules-smb/components/FormFields/StringField';
import ToggleField from '@/modules-smb/components/FormFields/ToggleField';

const InterfaceSsidRadiusResourceForm = ({ isDisabled }: { isDisabled: boolean }) => {
  const { t } = useTranslation();
  const { values, setFieldValue } = useFormikContext();

  const onEnabledAccountingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setFieldValue('accounting', {
        host: '***************',
        port: 1813,
        secret: 'YOUR_SECRET',
      });
    } else {
      setFieldValue('accounting', undefined);
    }
  };

  const isAccountingEnabled = useMemo(() => getIn(values, 'accounting') !== undefined, [getIn(values, 'accounting')]);

  const onEnableDynamicChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setFieldValue('dynamic-authorization', {
        host: '***************',
        port: 1814,
        secret: 'YOUR_SECRET',
      });
    } else {
      setFieldValue('dynamic-authorization', undefined);
    }
  };

  const isDynamicEnabled = useMemo(
    () => getIn(values, 'dynamic-authorization') !== undefined,
    [getIn(values, 'dynamic-authorization')],
  );

  const onLocalEnabled = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setFieldValue('local', INTERFACE_SSID_RADIUS_LOCAL_SCHEMA(t, true).cast());
      setFieldValue('authentication.mac-filter', false);
      setFieldValue('accounting', undefined);
      setFieldValue('dynamic-authorization', undefined);
      setFieldValue('chargeable-user-id', undefined);
    } else {
      setFieldValue('local', undefined);
    }
  };

  const isLocalEnabled = useMemo(() => getIn(values, 'local') !== undefined, [getIn(values, 'local')]);
  const isFieldDisabled = isLocalEnabled || isDisabled;

  return (
    <>
      <Heading size="md" mt={6} mb={2} textDecoration="underline">
        interface.ssid.radius
      </Heading>
      <SimpleGrid minChildWidth="300px" spacing="20px">
        <StringField name="authentication.host" label="authentication.host" isRequired isDisabled={isFieldDisabled} />
        <NumberField
          name="authentication.port"
          label="authentication.port"
          isRequired
          hideArrows
          w={24}
          isDisabled={isFieldDisabled}
        />
        <StringField
          name="authentication.secret"
          label="authentication.secret"
          isRequired
          hideButton
          isDisabled={isFieldDisabled}
        />
        <ToggleField
          name="authentication.mac-filter"
          label="authentication.mac-filter"
          falseIsUndefined
          isDisabled={isFieldDisabled}
        />
      </SimpleGrid>
      <FormControl>
        <FormLabel ms="4px" fontSize="md" fontWeight="normal">
          Enable Accounting
        </FormLabel>
        <Switch
          onChange={onEnabledAccountingChange}
          isChecked={isAccountingEnabled}
          borderRadius="15px"
          size="lg"
          _disabled={{ opacity: 0.8, cursor: 'not-allowed' }}
          isDisabled={isFieldDisabled}
        />
      </FormControl>
      {isAccountingEnabled && (
        <SimpleGrid minChildWidth="300px" spacing="20px">
          <StringField name="accounting.host" label="accounting.host" isRequired isDisabled={isFieldDisabled} />
          <NumberField
            name="accounting.port"
            label="accounting.port"
            isRequired
            hideArrows
            w={24}
            isDisabled={isFieldDisabled}
          />
          <StringField
            name="accounting.secret"
            label="accounting.secret"
            isRequired
            hideButton
            isDisabled={isFieldDisabled}
          />
        </SimpleGrid>
      )}
      <FormControl>
        <FormLabel ms="4px" fontSize="md" fontWeight="normal">
          Enable Dynamic Authorization
        </FormLabel>
        <Switch
          onChange={onEnableDynamicChange}
          isChecked={isDynamicEnabled}
          borderRadius="15px"
          size="lg"
          _disabled={{ opacity: 0.8, cursor: 'not-allowed' }}
          isDisabled={isFieldDisabled}
        />
      </FormControl>
      {isDynamicEnabled && (
        <SimpleGrid minChildWidth="300px" spacing="20px" mb={4}>
          <StringField
            name="dynamic-authorization.host"
            label="dynamic-authorization.host"
            isRequired
            isDisabled={isFieldDisabled}
          />
          <NumberField
            name="dynamic-authorization.port"
            label="dynamic-authorization.port"
            isRequired
            isDisabled={isFieldDisabled}
          />
          <StringField
            name="dynamic-authorization.secret"
            label="dynamic-authorization.secret"
            isRequired
            hideButton
            isDisabled={isFieldDisabled}
          />
        </SimpleGrid>
      )}
      <SimpleGrid minChildWidth="300px" spacing="20px">
        <StringField name="nas-identifier" label="nas-identifier" isDisabled={isFieldDisabled} />
        <ToggleField name="chargeable-user-id" label="chargeable-user-id" isDisabled={isFieldDisabled} />
      </SimpleGrid>
      <FormControl>
        <FormLabel ms="4px" fontSize="md" fontWeight="normal">
          Enable Local
        </FormLabel>
        <Switch
          onChange={onLocalEnabled}
          isChecked={isLocalEnabled}
          borderRadius="15px"
          size="lg"
          _disabled={{ opacity: 0.8, cursor: 'not-allowed' }}
          isDisabled={isDisabled}
        />
      </FormControl>
      {isLocalEnabled && (
        <SimpleGrid minChildWidth="300px" spacing="20px">
          <StringField name="local.server-identity" label="server-identity" isRequired isDisabled={isDisabled} />
          <ObjectArrayFieldModal
            name="local.users"
            label="users"
            isDisabled={isDisabled}
            fields={
              <SimpleGrid minChildWidth="300px" gap={4}>
                <StringField name="mac" label="mac" isRequired />
                <StringField name="user-name" label="user-name" isRequired />
                <StringField name="password" label="password" isRequired hideButton />
                <NumberField name="vlan-id" label="vlan-id" isRequired w={24} />
              </SimpleGrid>
            }
            columns={[
              {
                id: 'user-name',
                Header: 'user-name',
                Footer: '',
                accessor: 'user-name',
              },
              {
                id: 'mac',
                Header: 'mac',
                Footer: '',
                accessor: 'mac',
                customWidth: '150px',
              },
              {
                id: 'vlan-id',
                Header: 'vlan-id',
                Footer: '',
                accessor: 'vlan-id',
                customWidth: '100px',
              },
            ]}
            schema={INTERFACE_SSID_RADIUS_LOCAL_USER_SCHEMA}
            isRequired
          />
        </SimpleGrid>
      )}
    </>
  );
};

export default React.memo(InterfaceSsidRadiusResourceForm);
