import * as React from 'react';
import useFastField from '@/modules-smb/hooks/useFastField';
import TunnelForm from '@/modules-smb/pages/ConfigurationPage/ConfigurationCard/ConfigurationSectionsCard/InterfaceSection/SingleInterface/Tunnel/Tunnel';

interface Props {
  isDisabled?: boolean;
  parent?: {
    entity?: string;
    venue?: string;
    subscriber?: string;
  };
}

const TunnelResourceForm = ({ isDisabled, parent }: Props) => {
  const { value, onChange } = useFastField({ name: `editing` });

  const onProtoChange = React.useCallback(
    (e: React.ChangeEvent<HTMLSelectElement>) => {
      if (e.target.value === 'mesh') onChange({ proto: 'mesh' });
      else if (e.target.value === 'vxlan') {
        onChange({
          proto: 'vxlan',
          'peer-address': '***********',
          'peer-port': 4700,
        });
      } else if (e.target.value === 'l2tp') {
        onChange({
          proto: 'l2tp',
          server: '***********',
          password: 'YOUR_PASSWORD',
        });
      } else {
        onChange({
          proto: 'gre',
          'peer-address': '***********',
          'dhcp-healthcheck': true,
        });
      }
    },
    [onChange],
  );

  return (
    <TunnelForm
      isDisabled={isDisabled}
      namePrefix="editing"
      value={value}
      onProtoChange={onProtoChange}
      protoValue={value?.proto}
      parent={parent}
    />
  );
};

export default TunnelResourceForm;
