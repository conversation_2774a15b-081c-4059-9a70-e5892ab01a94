import React, { useEffect } from 'react';
import { Modal, ModalOverlay, ModalContent, ModalBody, useBoolean, Center, Spinner } from '@chakra-ui/react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import EditServiceClassForm from './Form';
import CloseButton from '@/modules-smb/components/Buttons/CloseButton';
import EditButton from '@/modules-smb/components/Buttons/EditButton';
import SaveButton from '@/modules-smb/components/Buttons/SaveButton';
import ConfirmCloseAlert from '@/modules-smb/components/Modals/Actions/ConfirmCloseAlert';
import ModalHeader from '@/modules-smb/components/Modals/ModalHeader';
import { useGetServiceClass } from '@/modules-smb/hooks/Network/ServiceClasses';
import useFormModal from '@/modules-smb/hooks/useFormModal';
import useFormRef from '@/modules-smb/hooks/useFormRef';

const propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  serviceClass: PropTypes.instanceOf(Object),
  refresh: PropTypes.func.isRequired,
};

const defaultProps = {
  serviceClass: null,
};

const EditServiceClassModal = ({ isOpen, onClose, serviceClass, refresh }) => {
  const { t } = useTranslation();
  const { form, formRef } = useFormRef();
  const [editing, setEditing] = useBoolean();
  const { isConfirmOpen, closeConfirm, closeModal, closeCancelAndForm } = useFormModal({
    isDirty: form?.dirty,
    onModalClose: onClose,
  });

  const { data: serviceData, isLoading } = useGetServiceClass({
    id: serviceClass?.id,
    enabled: serviceClass?.id !== '' && isOpen,
  });

  useEffect(() => {
    if (isOpen) {
      setEditing.off();
    }
  }, [isOpen]);

  return (
    <Modal onClose={closeModal} isOpen={isOpen} size="xl">
      <ModalOverlay />
      <ModalContent maxWidth={{ sm: '600px', md: '700px', lg: '800px', xl: '50%' }}>
        <ModalHeader
          title={t('crud.edit_obj', { obj: t('service.one', { count: 1 }) })}
          right={
            <>
              <SaveButton
                onClick={form.submitForm}
                isLoading={form.isSubmitting}
                isDisabled={!form.isValid || !form.dirty}
              />
              <EditButton ml={2} isDisabled={editing} onClick={setEditing.toggle} isCompact />
              <CloseButton ml={2} onClick={closeModal} />
            </>
          }
        />
        <ModalBody>
          {!isLoading && serviceData ? (
            <EditServiceClassForm
              editing={editing}
              serviceClass={serviceData}
              isOpen={isOpen}
              onClose={closeCancelAndForm}
              refresh={refresh}
              formRef={formRef}
            />
          ) : (
            <Center>
              <Spinner />
            </Center>
          )}
        </ModalBody>
      </ModalContent>
      <ConfirmCloseAlert isOpen={isConfirmOpen} confirm={closeCancelAndForm} cancel={closeConfirm} />
    </Modal>
  );
};

EditServiceClassModal.propTypes = propTypes;
EditServiceClassModal.defaultProps = defaultProps;

export default EditServiceClassModal;
