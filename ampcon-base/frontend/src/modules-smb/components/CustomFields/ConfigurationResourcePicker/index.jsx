import React, { useCallback, useMemo } from 'react';
import { useToast } from '@chakra-ui/react';
import { useField } from 'formik';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import ResourcePicker from './ResourcePicker';
// import { useGetAllResources } from '@/modules-smb/hooks/Network/Resources';
import { useGetResourceTemplates } from '@/modules-smb/hooks/Network/Resources'

const propTypes = {
  name: PropTypes.string.isRequired,
  prefix: PropTypes.string.isRequired,
  defaultValue: PropTypes.func.isRequired,
  isDisabled: PropTypes.bool.isRequired,
  blockedIds: PropTypes.arrayOf(PropTypes.string),
  parent: PropTypes.shape({
    entity: PropTypes.string,
    venue: PropTypes.string,
    subscriber: PropTypes.string,
  })
};

const ConfigurationResourcePicker = ({ name, prefix, defaultValue, isDisabled, blockedIds, parent }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const [{ value }, , { setValue }] = useField(name);
  // const { data: resources, isFetching } = useGetAllResources({ t, toast });

  const venueID = parent?.venue || ''
  const isFetching = React.useMemo(() => {
    return !!venueID;
  }, [prefix, venueID]);
  const { data: resources = [] } = useGetResourceTemplates(venueID, prefix, {
    enabled: isFetching,
  });

  const availableResources = useMemo(() => {
    if (resources)
      return resources
        .filter((resource) => resource.variables[0]?.prefix === prefix && !blockedIds?.includes(resource.id))
        .map((resource) => ({ value: resource.id, label: resource.name }));
    return [];
  }, [resources, blockedIds]);

  const getValue = () => {
    // tunnel特殊处理
    // if (prefix === 'interface.tunnel') {
    //   if (value && value['resource-tunnel'] && value['resource-tunnel'].__variableBlock) {
    //     return value['resource-tunnel'].__variableBlock[0];
    //   } else {
    //     return '';
    //   }
    // }
    if (!value || !value.__variableBlock) return '';
    return value.__variableBlock[0];
  };

  const onChange = useCallback((e) => {
    if (e.target.value === '') setValue(defaultValue(t, true).cast());
    // tunnel特殊处理
    // else if (prefix === 'interface.tunnel') {
    //   const selectedResource = resources.find((resource) => resource.id === e.target.value);
    //   const newObj = selectedResource.variables[0].value;
    //   newObj['resource-tunnel'] = { __variableBlock: [e.target.value] };
    //   setValue(newObj);
    // }
    else {
      const newObj = {};
      newObj.__variableBlock = [e.target.value];
      setValue(newObj);
    }
  }, []);

  return (
    <ResourcePicker
      value={getValue()}
      onChange={onChange}
      resources={availableResources}
      isDisabled={isDisabled || !resources}
    />
  );
};

ConfigurationResourcePicker.propTypes = propTypes;
export default ConfigurationResourcePicker;
