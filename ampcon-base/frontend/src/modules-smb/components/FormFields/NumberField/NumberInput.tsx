import React from 'react';
import {
  FormControl,
  FormErrorMessage,
  FormLabel,
  NumberInput as Input,
  NumberDecrementStepper,
  NumberIncrementStepper,
  NumberInputField,
  NumberInputStepper,
  InputGroup,
  InputRightAddon,
} from '@chakra-ui/react';
import ConfigurationFieldExplanation from '../ConfigurationFieldExplanation';
import { FieldInputProps } from '@/modules-smb/models/Form';

interface Props extends FieldInputProps<string | undefined | string[]> {
  onChange: (v: string) => void;
  isError: boolean;
  hideArrows: boolean;
  unit?: string;
  w?: string | number;
}

const NumberInput = (
  {
    label,
    value,
    unit,
    onChange,
    onBlur,
    error,
    isError,
    isRequired,
    hideArrows,
    element,
    isDisabled,
    w,
    min,
    max,
    definitionKey
  }: Props
) => {
  if (element)
    return (
      <FormControl isInvalid={isError} isRequired={isRequired} isDisabled={isDisabled}>
        <FormLabel ms="4px" fontSize="md" fontWeight="normal" _disabled={{ opacity: 0.8 }}>
          {label}
        </FormLabel>
        {element}
        <FormErrorMessage>{error}</FormErrorMessage>
      </FormControl>
    );

  return (
    <FormControl isInvalid={isError} isRequired={isRequired} isDisabled={isDisabled}>
      <FormLabel ms="4px" fontSize="md" fontWeight="normal" _disabled={{ opacity: 0.8 }}>
        {label}
        <ConfigurationFieldExplanation definitionKey={definitionKey} />
      </FormLabel>
      {unit ? (
        <InputGroup>
          <Input
            allowMouseWheel
            value={value as string | number}
            onChange={onChange}
            onBlur={onBlur}
            borderRadius="15px"
            fontSize="sm"
            w={w}
            min={min}
            max={max}
            _disabled={{ opacity: 0.8, cursor: 'not-allowed' }}
          >
            <NumberInputField border="2px solid" style={{boxSizing: 'border-box'}}/>
            <NumberInputStepper hidden={hideArrows}>
              <NumberIncrementStepper />
              <NumberDecrementStepper />
            </NumberInputStepper>
          </Input>
          <InputRightAddon>{unit}</InputRightAddon>
        </InputGroup>
      ) : (
        <Input
          allowMouseWheel
          value={value as string | number}
          onChange={onChange}
          onBlur={onBlur}
          borderRadius="15px"
          fontSize="sm"
          w={w}
          min={min}
          max={max}
        >
          <NumberInputField border="2px solid" style={{boxSizing: 'border-box'}}/>
          <NumberInputStepper hidden={hideArrows}>
            <NumberIncrementStepper />
            <NumberDecrementStepper />
          </NumberInputStepper>
        </Input>
      )}
      <FormErrorMessage>{error}</FormErrorMessage>
    </FormControl>
  );
};

export default React.memo(NumberInput);
