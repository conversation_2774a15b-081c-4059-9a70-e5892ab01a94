import React, { useCallback } from 'react';
import { LayoutProps } from '@chakra-ui/react';
import StringInput from './StringInput';
import useFastField from '@/modules-smb/hooks/useFastField';
import { FieldProps } from '@/modules-smb/models/Form';

interface StringFieldProps extends FieldProps, LayoutProps {
  hideButton?: boolean;
  explanation?: string;
  placeholder?: string;
  autoComplete?: string;
}

const StringField: React.FC<StringFieldProps> = ({
  name,
  isDisabled = false,
  label,
  hideButton = false,
  isRequired = false,
  element,
  isArea = false,
  emptyIsUndefined = false,
  definitionKey,
  explanation,
  placeholder,
  autoComplete,
  ...props
}) => {
  const { value, error, isError, onChange, onBlur } = useFastField<string | undefined>({ name });

  const onFieldChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
      if (emptyIsUndefined && e.target.value.length === 0) onChange(undefined);
      else onChange(e.target.value);
    },
    [onChange],
  );

  return (
    <StringInput
      label={label ?? name}
      value={value}
      onChange={onFieldChange}
      onBlur={onBlur}
      isError={isError}
      error={error}
      hideButton={hideButton}
      isRequired={isRequired}
      element={element}
      isArea={isArea}
      isDisabled={isDisabled}
      definitionKey={definitionKey}
      explanation={explanation}
      placeholder={placeholder}
      autoComplete={autoComplete || 'off'}
      {...props}
    />
  );
};

export default React.memo(StringField);
