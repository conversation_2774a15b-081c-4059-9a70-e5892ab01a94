import React, { useCallback, useEffect, useState, onTempValueChange } from 'react';
import {
  FormControl,
  FormErrorMessage,
  FormLabel,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody,
  Text,
  Button,
  Box,
  Tooltip,
  IconButton,
} from '@chakra-ui/react';
import { Trash } from '@phosphor-icons/react';
import { Formik } from 'formik';
import { useTranslation } from 'react-i18next';
import CloseButton from '@/modules-smb/components/Buttons/CloseButton';
import SaveButton from '@/modules-smb/components/Buttons/SaveButton';
import DataTable from '@/modules-smb/components/DataTable';
import ModalHeader from '@/modules-smb/components/Modals/ModalHeader';
import { FieldInputProps } from '@/modules-smb/models/Form';
import { Column } from '@/modules-smb/models/Table';

interface ObjectArrayFieldModalOptions {
  buttonLabel?: string;
  modalTitle?: string;
  onFormSubmit?: (value: any) => object;
  onRemove?: (updatedArray: object[]) => void;  // 新增这个
}

interface Props extends FieldInputProps<object[]> {
  name: string;
  isError: boolean;
  onChange: (e: unknown[]) => void;
  isHidden: boolean;
  hideLabel: boolean;
  fields: React.ReactNode;
  columns: Column<object[]>[];
  options: ObjectArrayFieldModalOptions;
  //leases?: any[];  // 在接口里声明可选字段
  onTempValueChange?: (temp: object[]) => void; // <-- 新增
}

const ObjectArrayFieldInput: React.FC<Props> = ({
  name,
  label,
  value,
  onChange,
  isError,
  error,
  fields,
  isRequired,
  isHidden,
  schema,
  columns,
  isDisabled,
  hideLabel,
  options,
  onTempValueChange,
}) => {
  const { t } = useTranslation();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [tempValue, setTempValue] = useState<object[]>([]);
  const variableName = name.split('.')[name.split('.').length - 1];

  // const removeObj = (index: number) => {
  //   const newArr = [...tempValue];
  //   newArr.splice(index, 1);
  //   setTempValue(newArr);
  // };
  const removeObj = (index: number) => {
  const item = tempValue[index];
  if (item?.__locked) return; // locked阻止删除
  const newArr = [...tempValue];
  newArr.splice(index, 1);
  setTempValue(newArr);
};

  // const onSave = () => {
  //   onChange(tempValue);
  //   onClose();
  // };
  const onSave = () => {
    const filteredValue = tempValue.map(({ __locked, ...rest }) => rest);
    onChange(filteredValue);
    onClose();
  };

  const removeAction = useCallback(
  (cell: { row: { index: number } }) => {
    const item = tempValue[cell.row.index];
    const isLocked = item?.__locked;

    return (
      <Tooltip hasArrow label={isLocked ? t('Default options cannot be removed') : t('common.remove')} placement="top">
        <IconButton
          aria-label="Remove Object"
          ml={2}
          colorScheme={isLocked ? 'gray' : 'red'}
          icon={<Trash size={20} />}
          size="sm"
          isDisabled={isLocked}
          onClick={() => {
            if (!isLocked) removeObj(cell.row.index);
          }}
        />
      </Tooltip>
    );
  },
  [tempValue],
);

  const computedButtonLabel = () => {
    if (options?.buttonLabel) return options.buttonLabel;

    return `${t('common.manage')} ${variableName} (${value?.length ?? 0}
            ${t('common.entries', { count: value?.length ?? 0 }).toLowerCase()})`;
  };

  useEffect(() => {
    if (!isOpen) {
      setTempValue(value ?? []);
    }
  }, [value, isOpen]);

  useEffect(() => {
    if (onTempValueChange) {
      onTempValueChange(tempValue);
    }
  }, [tempValue]); // 当 tempValue 改变时通知外部

  return (
    <>
      <FormControl isInvalid={isError} isRequired={isRequired} isDisabled={isDisabled} hidden={isHidden}>
        <FormLabel hidden={hideLabel} ms="4px" fontSize="md" fontWeight="normal">
          {label}
        </FormLabel>
        <Text ml={1} fontSize="sm">
          <Button colorScheme="blue" onClick={onOpen}>
            {computedButtonLabel()}
          </Button>
        </Text>
        <FormErrorMessage>{error}</FormErrorMessage>
      </FormControl>
      <Modal onClose={onClose} isOpen={isOpen} size="xl" scrollBehavior="inside">
        <ModalOverlay />
        <ModalContent maxWidth={{ sm: '600px', md: '700px', lg: '800px', xl: '50%' }}>
          <ModalHeader
            title={options.modalTitle ?? name}
            right={
              <>
                <SaveButton onClick={onSave} hidden={isDisabled} />
                <CloseButton ml={2} onClick={onClose} />
              </>
            }
          />
          <ModalBody>
            {!isDisabled && (
              <Formik
                // @ts-ignore
                initialValues={schema(t, true).cast()}
                validationSchema={schema(t)}
                validateOnMount
                onSubmit={(data, { setSubmitting, resetForm,setErrors , setFieldValue  }) => {
                  setSubmitting(true);
                  if(name.includes('.multi-psk')){
                    if(data.mac===""){
                      data.mac=undefined;
                    }
                  const isKeyExists = tempValue.some(item => item.key === data.key);
                  if (isKeyExists) {
                    setErrors({ key: t('configurations.multi_psk_key_exsist') });
                    setSubmitting(false);
                    return;
                  }
                  }                 
                  if (!options.onFormSubmit) {
                    setTempValue([...tempValue, data]);
                  } else {
                    setTempValue([...tempValue, options.onFormSubmit(data)]);
                  }
                  resetForm();
                  const hasMacaddrField = schema(t).fields?.hasOwnProperty('macaddr');
                  if (hasMacaddrField) {
                    setFieldValue('macaddr', '');
                  }
                  setSubmitting(false);
                }}
              >
                {({ resetForm, isValid, dirty, submitForm,setFieldValue }) => (
                  <>
                    {fields}
                    <Box textAlign="right" my={4}>
                      <Button colorScheme="blue" isDisabled={!isValid} onClick={submitForm}>
                        {t('crud.add')}
                      </Button>
                      <Button colorScheme="gray" isDisabled={!dirty} ml={2} onClick={() => {resetForm();if(schema(t).fields?.hasOwnProperty('macaddr')){ setFieldValue('macaddr', '');}}}>
                        {t('common.reset')}
                      </Button>
                    </Box>
                  </>
                )}
              </Formik>
            )}
            <DataTable
              columns={
                !isDisabled
                  ? [
                    ...columns,
                    {
                      id: 'actions',
                      Header: t('common.actions'),
                      Footer: '',
                      accessor: 'Id',
                      customWidth: '80px',
                      Cell: ({ cell }) => removeAction(cell),
                      disableSortBy: true,
                      alwaysShow: true,
                    },
                  ]
                  : columns
              }
              data={tempValue}
              minHeight="200px"
            />
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default React.memo(ObjectArrayFieldInput);
