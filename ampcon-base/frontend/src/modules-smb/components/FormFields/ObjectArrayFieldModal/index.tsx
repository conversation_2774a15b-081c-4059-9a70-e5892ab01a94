import React, { useCallback } from 'react';
import ObjectArrayFieldInput from './Input';
import useFastField from '@/modules-smb/hooks/useFastField';
import { FieldProps } from '@/modules-smb/models/Form';
import { Column } from '@/modules-smb/models/Table';

interface ObjectArrayFieldModalOptions {
  buttonLabel?: string;
  modalTitle?: string;
}

interface Props extends FieldProps {
  fields: React.ReactNode;
  columns: Column<unknown>[];
  options?: ObjectArrayFieldModalOptions;
  schema: (t: (e: string) => string, useDefault?: boolean) => object;
  onTempValueChange?: (value: any[]) => void; 
}

const ObjectArrayFieldModal = ({
  name,
  label,
  fields,
  schema,
  columns,
  hideLabel = false,
  isDisabled = false,
  isHidden = false,
  isRequired = false,
  emptyIsUndefined = false,
  definitionKey,
  onTempValueChange,
  options = {},
}: Props) => {
  const { value, error, isError, onChange, onBlur } = useFastField<unknown[] | undefined>({ name });

  const onFieldChange = useCallback(
    (v: unknown[]) => {
      if (emptyIsUndefined && v.length === 0) onChange(undefined);
      else onChange(v);
    },
    [onChange],
  );

  return (
    <ObjectArrayFieldInput
      name={name}
      label={label ?? name}
      value={value}
      fields={fields}
      schema={schema}
      columns={columns}
      hideLabel={hideLabel}
      onChange={onFieldChange}
      isHidden={isHidden}
      onBlur={onBlur}
      isError={isError}
      error={error}
      isRequired={isRequired}
      isDisabled={isDisabled}
      definitionKey={definitionKey}
      options={options}
       onTempValueChange={onTempValueChange} 
    />
  );
};

export default React.memo(ObjectArrayFieldModal);
