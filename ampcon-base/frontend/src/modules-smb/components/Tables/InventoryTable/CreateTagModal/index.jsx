import React, { useCallback, useState,useEffect } from 'react';
import { useDisclosure, Modal, ModalOverlay, ModalContent, ModalBody } from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import CreateTagForm from './Form';
import CloseButton from '@/modules-smb/components/Buttons/CloseButton';
import CreateButton from '@/modules-smb/components/Buttons/CreateButton';
import SaveButton from '@/modules-smb/components/Buttons/SaveButton';
import ConfirmCloseAlert from '@/modules-smb/components/Modals/Actions/ConfirmCloseAlert';
import ModalHeader from '@/modules-smb/components/Modals/ModalHeader';
// import useGetDeviceTypes from '@/modules-smb/hooks/Network/DeviceTypes';
import useFormRef from '@/modules-smb/hooks/useFormRef';
import { axiosProv } from '@/modules-smb/utils/axiosInstances';
import { useGetDeviceTypes } from "@/modules-ampcon/apis/upgrade_api";
import ConfirmWebRootErrorsModal from '@/modules-smb/pages/ConfigurationPage/ConfigurationCard/ConfirmWebRootErrorsModal';
import { Button } from 'antd';
import Icon from "@ant-design/icons";
import { addSvg } from "@/utils/common/iconSvg";

const propTypes = {
  refresh: PropTypes.func.isRequired,
  entityId: PropTypes.string,
  subId: PropTypes.string,
  deviceClass: PropTypes.string,
  venueId: PropTypes.string,
};

const defaultProps = {
  entityId: '',
  subId: '',
  deviceClass: '',
  venueId: '',
};

const CreateTagModal = ({ refresh, entityId,venueId, subId, deviceClass }) => {
  const { t } = useTranslation();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: showConfirm, onOpen: openConfirm, onClose: closeConfirm } = useDisclosure();
  const { form, formRef } = useFormRef();
  const { data: deviceTypes } = useGetDeviceTypes();
  const [configuration, setConfiguration] = useState(null);
  // 新增：跟踪当前选中的venueId，用于传递给子组件
  const [currentVenueId, setCurrentVenueId] = useState(venueId);

  const onConfigurationChange = useCallback((conf) =>{
    setConfiguration(conf)
    setWebRootErrors(conf.webRootErrors || {}); // 新增错误状态更新
  }, []);

  const create = useMutation((newObj) =>
    axiosProv.post(
      `inventory/${newObj.serialNumber}${
        newObj.__newConfig
          ? `?createObjects=${JSON.stringify({
              objects: [{ configuration: newObj.__newConfig }],
            })}`
          : ''
      }`,
      newObj,
    ),
  );

  const openModal = () => {
    setConfiguration(null);
    onOpen();
  };
  const closeModal = () => (form.dirty || configuration?.__form?.isDirty ? openConfirm() : onClose());
  const closeCancelAndForm = () => {
    closeConfirm();
    onClose();
  };
   const [webRootErrors, setWebRootErrors] = useState({});
   const { isOpen: showWebRootErrors, onOpen: openWebRootErrors, onClose: closeWebRootErrors } = useDisclosure();
  // 统一错误处理逻辑
  const handleSubmit = () => {
    const hasErrors = 
      Object.keys(webRootErrors).length > 0 || 
      !form.isValid ||
      (configuration !== null &&configuration?.__form?.isValid !==undefined && !configuration?.__form?.isValid);

      console.log('hasErrors-----modal',hasErrors);
    if (hasErrors) {
      openWebRootErrors();
    } else {
      form.submitForm();
    }
  };
  // 新增：当venueId变化时更新currentVenueId
  useEffect(() => {
    setCurrentVenueId(venueId);
  }, [venueId]);
   return (
    <>
       <Button
        type="primary"
        onClick={openModal}
        style={{ display: "flex", alignItems: "center" }}
      >
        <Icon component={addSvg} style={{ marginRight: 8 }} />
        {t('common.create')}
      </Button>
      <Modal onClose={closeModal} isOpen={isOpen} size="xl">
        <ModalOverlay />
        <ModalContent maxWidth={{ sm: '90%', md: '900px', lg: '1000px', xl: '80%' }}>
          <ModalHeader
            title={t('crud.create_object', { obj: t('inventory.tag', { count: 1 }) })}
            right={
              <>
                <SaveButton
                  onClick={handleSubmit}
                  isLoading={form.isSubmitting}
                  isDisabled={!form.isValid || !form.dirty || (configuration !== null &&configuration?.__form?.isValid !==undefined&& !configuration?.__form?.isValid)}
                />
                <CloseButton ml={2} onClick={closeModal} />
              </>
            }
          />
          <ModalBody>
            <CreateTagForm
              deviceTypesList={deviceTypes ?? []}
              create={create}
              isOpen={isOpen}
              onClose={onClose}
              refresh={refresh}
              formRef={formRef}
              entityId={entityId}
              subId={subId}
              configuration={configuration}
              onConfigurationChange={onConfigurationChange}
              deviceClass={deviceClass}
              venueId={currentVenueId} // 新增：传递venueId到子组件
            />
          </ModalBody>
        </ModalContent>
        <ConfirmCloseAlert isOpen={showConfirm} confirm={closeCancelAndForm} cancel={closeConfirm} />
        <ConfirmWebRootErrorsModal isOpen={showWebRootErrors} onClose={closeWebRootErrors} errors={webRootErrors} />
      </Modal>
    </>
  );
};

CreateTagModal.propTypes = propTypes;
CreateTagModal.defaultProps = defaultProps;

export default CreateTagModal;
