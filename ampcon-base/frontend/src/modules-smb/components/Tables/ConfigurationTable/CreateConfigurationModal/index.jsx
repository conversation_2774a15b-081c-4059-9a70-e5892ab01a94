import React, { useCallback, useState,useEffect } from 'react';
import { useDisclosure, Modal, ModalOverlay, ModalContent, ModalBody } from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import CreateConfigurationForm from './Form';
import CloseButton from '@/modules-smb/components/Buttons/CloseButton';
import CreateButton from '@/modules-smb/components/Buttons/CreateButton';
import SaveButton from '@/modules-smb/components/Buttons/SaveButton';
import ConfirmCloseAlert from '@/modules-smb/components/Modals/Actions/ConfirmCloseAlert';
import ModalHeader from '@/modules-smb/components/Modals/ModalHeader';
// import useGetDeviceTypes from '@/modules-smb/hooks/Network/DeviceTypes';
import useFormRef from '@/modules-smb/hooks/useFormRef';
import { axiosProv } from '@/modules-smb/utils/axiosInstances';
import { useGetDeviceTypes } from "@/modules-ampcon/apis/upgrade_api";
import ConfirmWebRootErrorsModal from '@/modules-smb/pages/ConfigurationPage/ConfigurationCard/ConfirmWebRootErrorsModal';
import VlanErrorModal  from '@/modules-smb/pages/ConfigurationPage/ConfigurationCard/VlanErrorModal';
import { updateInterfaces } from '@/modules-smb/utils/configHelpers';


const propTypes = {
  refresh: PropTypes.func,
  entityId: PropTypes.string,
};

const defaultProps = {
  refresh: () => {},
  entityId: null,
};

const CreateConfigurationModal = ({ refresh, entityId }) => {
  const { t } = useTranslation();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: showConfirm, onOpen: openConfirm, onClose: closeConfirm } = useDisclosure();
  const { form, formRef } = useFormRef();
  const { data: deviceTypes } = useGetDeviceTypes();
  const [configuration, setConfiguration] = useState(null);
  const [webRootErrors, setWebRootErrors] = useState({});
  const [vlanErrors, setVlanErrors] = useState([]);
  const { isOpen: showWebRootErrors, onOpen: openWebRootErrors, onClose: closeWebRootErrors } = useDisclosure();
  const { isOpen: showVlanErrors, onOpen: openVlanErrorModal, onClose: closeVlanErrorModal } = useDisclosure();
  const create = useMutation((newObj) =>
    axiosProv.post('configuration/1', {
      ...newObj,
      configuration: configuration?.data.configuration ?? null,
    }),
  );

  const onConfigurationChange = useCallback((conf) =>  {
    setConfiguration(conf);
    setWebRootErrors(conf.webRootErrors || {}); // 新增错误状态更新
  }, []);

  const openModal = () => {
    setConfiguration(null);
    onOpen();
  };
  const closeModal = () => (form.dirty || configuration?.__form?.isDirty ? openConfirm() : onClose());
  const closeCancelAndForm = () => {
    closeConfirm();
    onClose();
  };
  const checkVlanConflicts = () => {
    const vlanGroups = {};
    const errors = [];

    // 1. 找到包含interfaces数组的配置项
    const configWithInterfaces = configuration?.data?.configuration?.find(
      config => config.configuration?.interfaces
    );

    if (!configWithInterfaces) return errors;

    // 2. 获取interfaces数组
    const interfaces = configWithInterfaces.configuration.interfaces;
    
    // 3. 检查每个接口的VLAN ID
    interfaces.forEach((iface, index) => {
      const vlanId = iface.vlan?.id ?? 'No Set';
      const ifaceName = iface.name || `Interface ${index + 1}`;

      if (!vlanGroups[vlanId]) {
        vlanGroups[vlanId] = [];
      }
      vlanGroups[vlanId].push(ifaceName);
    });

    // 4. 生成错误信息
    Object.entries(vlanGroups).forEach(([vlanId, ifaceNames]) => {
      if (ifaceNames.length > 1) {
        const displayId = vlanId === 'No Set' ? 'Empty' : vlanId;
        errors.push(
          `[${ifaceNames.join('], [')}] have the same VLAN ID [${displayId}]`
        );
      }
    });

    return errors;
  };
   // 统一错误处理逻辑
   const handleSubmit = () => {
    console.log('handleSubmit-----modal',webRootErrors);
    const vlanErrors = checkVlanConflicts();
    if (vlanErrors.length > 0) {
      setVlanErrors(vlanErrors);
      openVlanErrorModal();
      return;
    }
    const hasErrors = 
      Object.keys(webRootErrors).length > 0 || 
      !form.isValid ||
      (configuration !== null && !configuration?.__form?.isValid);
    if (hasErrors) {
      openWebRootErrors();
    } else {
      // 增加保存SSID排序逻辑
      if (configuration.data?.configuration?.length && configuration.data.configuration[0].configuration && configuration.data.configuration[0].configuration.interfaces) {  
        configuration.data.configuration[0].configuration.interfaces = updateInterfaces(configuration.data.configuration[0].configuration.interfaces);
      }
      form.submitForm();
    }
  };
  useEffect(() => {
    if (Object.keys(webRootErrors).length > 0) {
      console.log('检测到web-root错误:', webRootErrors);
    }
  }, [webRootErrors]);
  return (
    <>
      <CreateButton onClick={openModal} ml={2} />
      <Modal onClose={closeModal} isOpen={isOpen} size="xl" scrollBehavior="inside">
        <ModalOverlay />
        <ModalContent maxWidth={{ sm: '90%', md: '900px', lg: '1000px', xl: '80%' }}>
          <ModalHeader
            title={t('crud.create_object', { obj: t('configurations.one') })}
            right={
              <>
                <SaveButton
                  onClick={handleSubmit}
                  isLoading={form.isSubmitting}
                  isDisabled={!form.isValid || !form.dirty || (configuration !== null && !configuration?.__form?.isValid)}
                />
                <CloseButton ml={2} onClick={closeModal} />
              </>
            }
          />
          <ModalBody>
            <CreateConfigurationForm
              deviceTypesList={deviceTypes ?? []}
              create={create}
              isOpen={isOpen}
              onClose={onClose}
              refresh={refresh}
              formRef={formRef}
              entityId={entityId}
              onConfigurationChange={onConfigurationChange}
            />
          </ModalBody>
        </ModalContent>
        <ConfirmCloseAlert isOpen={showConfirm} confirm={closeCancelAndForm} cancel={closeConfirm} />
      </Modal>
      <ConfirmWebRootErrorsModal isOpen={showWebRootErrors} onClose={closeWebRootErrors} errors={webRootErrors} />
      <VlanErrorModal isOpen={showVlanErrors} onClose={closeVlanErrorModal} errors={vlanErrors} />
    </>
  );
};

CreateConfigurationModal.propTypes = propTypes;
CreateConfigurationModal.defaultProps = defaultProps;

export default CreateConfigurationModal;
