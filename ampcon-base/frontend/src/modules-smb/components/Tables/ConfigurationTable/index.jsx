import React, { useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { v4 as uuid } from 'uuid';
import DataTable from '@/modules-smb/components/DataTable';
import FormattedDate from '@/modules-smb/components/FormattedDate';
import { useGetSelectConfigurations } from '@/modules-smb/hooks/Network/Configurations';

const propTypes = {
  select: PropTypes.arrayOf(PropTypes.string).isRequired,
  actions: PropTypes.func.isRequired,
};

const ConfigurationsTable = ({ select, actions }) => {
  const { t } = useTranslation();
  const { data: configurations, isFetching } = useGetSelectConfigurations({ select });
  const navigate = useNavigate();

  const handleGoToPage = (configuration) => navigate(`/wireless/configuration/${configuration.id}`);

  const dateCell = useCallback((cell, key) => <FormattedDate date={cell.row.values[key]} key={uuid()} />, []);
  const typesCell = useCallback((cell) => cell.row.values.deviceTypes.join(', '), []);

  // Columns array. This array contains your table headings and accessors which maps keys from data array
  const columns = useMemo(() => {
    const baseColumns = [
      {
        id: 'name',
        Header: t('common.name'),
        Footer: '',
        accessor: 'name',
        customMaxWidth: '200px',
        customWidth: 'calc(15vh)',
        customMinWidth: '150px',
        alwaysShow: true,
      },
      {
        id: 'deviceTypes',
        Header: t('configurations.device_types'),
        Footer: '',
        accessor: 'deviceTypes',
        Cell: ({ cell }) => typesCell(cell),
        disableSortBy: true,
        customMaxWidth: '150px',
      },
      {
        id: 'modified',
        Header: t('common.modified'),
        Footer: '',
        accessor: 'modified',
        Cell: ({ cell }) => dateCell(cell, 'modified'),
        customMinWidth: '150px',
        customWidth: '150px',
      },
      {
        id: 'description',
        Header: t('common.description'),
        Footer: '',
        accessor: 'description',
        disableSortBy: true,
      },
      {
        id: 'actions',
        Header: '',
        Footer: '',
        accessor: 'Id',
        customWidth: '80px',
        Cell: ({ cell }) => actions(cell),
        disableSortBy: true,
        alwaysShow: true,
      },
    ];

    return baseColumns;
  }, [t]);

  return (
    <DataTable
      h="100%"
      columns={columns}
      data={configurations ?? []}
      isLoading={isFetching}
      sortBy={[
        {
          id: 'name',
          desc: false,
        },
      ]}
      obj={t('configurations.title')}
      minHeight="200px"
      hideControls
      showAllRows
      onRowClick={handleGoToPage}
      isRowClickable={() => true}
    />
  );
};

ConfigurationsTable.propTypes = propTypes;

export default ConfigurationsTable;
