import React, { useEffect } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody,
  useToast,
  Spinner,
  Center,
  useDisclosure,
  useBoolean,
} from '@chakra-ui/react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import EditContactForm from './Form';
import CloseButton from '@/modules-smb/components/Buttons/CloseButton';
import EditButton from '@/modules-smb/components/Buttons/EditButton';
import SaveButton from '@/modules-smb/components/Buttons/SaveButton';
import ConfirmCloseAlert from '@/modules-smb/components/Modals/Actions/ConfirmCloseAlert';
import ModalHeader from '@/modules-smb/components/Modals/ModalHeader';
import { ContactShape } from '@/modules-smb/constants/propShapes';
import { useGetContact } from '@/modules-smb/hooks/Network/Contacts';
import useFormRef from '@/modules-smb/hooks/useFormRef';

const propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  contact: PropTypes.shape(ContactShape),
  refresh: PropTypes.func.isRequired,
};

const defaultProps = {
  contact: null,
};

const EditContactModal = ({ isOpen, onClose, contact, refresh }) => {
  const { t } = useTranslation();
  const [editing, setEditing] = useBoolean();
  const { isOpen: showConfirm, onOpen: openConfirm, onClose: closeConfirm } = useDisclosure();
  const toast = useToast();
  const { form, formRef } = useFormRef();
  const { data: contactData, isLoading } = useGetContact({
    t,
    toast,
    id: contact?.id,
    enabled: contact?.id !== '' && isOpen,
  });
  const closeModal = () => (form.dirty ? openConfirm() : onClose());

  const closeCancelAndForm = () => {
    closeConfirm();
    onClose();
  };

  useEffect(() => {
    if (isOpen) setEditing.off();
  }, [isOpen]);

  return (
    <Modal onClose={closeModal} isOpen={isOpen} size="xl" scrollBehavior="inside">
      <ModalOverlay />
      <ModalContent maxWidth={{ sm: '600px', md: '700px', lg: '800px', xl: '50%' }}>
        <ModalHeader
          title={t('crud.edit_obj', { obj: t('contacts.one') })}
          right={
            <>
              <SaveButton
                onClick={form.submitForm}
                isLoading={form.isSubmitting}
                isDisabled={!editing || !form.isValid || !form.dirty}
              />
              <EditButton ml={2} isDisabled={editing} onClick={setEditing.toggle} isCompact />
              <CloseButton ml={2} onClick={closeModal} />
            </>
          }
        />
        <ModalBody>
          {!isLoading && contactData !== undefined ? (
            <EditContactForm
              editing={editing}
              contact={contactData}
              isOpen={isOpen}
              onClose={onClose}
              refresh={refresh}
              formRef={formRef}
            />
          ) : (
            <Center>
              <Spinner />
            </Center>
          )}
        </ModalBody>
      </ModalContent>
      <ConfirmCloseAlert isOpen={showConfirm} confirm={closeCancelAndForm} cancel={closeConfirm} />
    </Modal>
  );
};

EditContactModal.propTypes = propTypes;
EditContactModal.defaultProps = defaultProps;

export default EditContactModal;
