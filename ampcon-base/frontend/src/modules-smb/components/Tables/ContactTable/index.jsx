import React, { useCallback, useEffect } from 'react';
import { useToast } from '@chakra-ui/react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import DataTable from '@/modules-smb/components/DataTable';
import FormattedDate from '@/modules-smb/components/FormattedDate';
import { useGetSelectContacts } from '@/modules-smb/hooks/Network/Contacts';

const propTypes = {
  actions: PropTypes.func.isRequired,
  select: PropTypes.arrayOf(PropTypes.string).isRequired,
  ignoredColumns: PropTypes.arrayOf(PropTypes.string),
  refreshId: PropTypes.number,
  disabledIds: PropTypes.arrayOf(PropTypes.string),
  openDetailsModal: PropTypes.func.isRequired,
};

const defaultProps = {
  ignoredColumns: [],
  refreshId: 0,
  disabledIds: [],
};

const ContactTable = ({ actions, select, ignoredColumns, refreshId, disabledIds, openDetailsModal }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { data: venues, isFetching, refetch } = useGetSelectContacts({ t, toast, select });

  const memoizedDate = useCallback((cell, key) => <FormattedDate date={cell.row.values[key]} key={uuid()} />, []);

  // Columns array. This array contains your table headings and accessors which maps keys from data array
  const columns = React.useMemo(() => {
    const baseColumns = [
      {
        id: 'name',
        Header: t('common.name'),
        Footer: '',
        accessor: 'name',
        customMaxWidth: '200px',
        customWidth: 'calc(15vh)',
        customMinWidth: '150px',
      },
      {
        id: 'email',
        Header: t('common.email'),
        Footer: '',
        accessor: 'primaryEmail',
        customMaxWidth: '200px',
        customWidth: 'calc(15vh)',
        customMinWidth: '150px',
      },
      {
        id: 'type',
        Header: t('common.type'),
        Footer: '',
        accessor: 'type',
        customMaxWidth: '200px',
        customWidth: 'calc(15vh)',
        customMinWidth: '150px',
      },
      {
        id: 'modified',
        Header: t('common.modified'),
        Footer: '',
        accessor: 'modified',
        Cell: ({ cell }) => memoizedDate(cell, 'modified'),
        customMinWidth: '150px',
        customWidth: '150px',
      },
      {
        id: 'description',
        Header: t('common.description'),
        Footer: '',
        accessor: 'description',
        disableSortBy: true,
      },
      {
        id: 'actions',
        Header: '',
        Footer: '',
        accessor: 'Id',
        customWidth: '80px',
        Cell: ({ cell }) => actions(cell, disabledIds),
        disableSortBy: true,
        alwaysShow: true,
      },
    ];

    return baseColumns;
  }, [t, disabledIds]);

  useEffect(() => {
    if (refreshId > 0) refetch();
  }, [refreshId]);

  return (
    <DataTable
      columns={columns.filter((col) => !ignoredColumns.find((ignored) => ignored === col.id))}
      data={venues ?? []}
      isLoading={isFetching}
      obj={t('contacts.other')}
      sortBy={[
        {
          id: 'name',
          desc: false,
        },
      ]}
      minHeight="200px"
      showAllRows
      hideControls
      onRowClick={openDetailsModal}
      isRowClickable={() => true}
    />
  );
};

ContactTable.propTypes = propTypes;
ContactTable.defaultProps = defaultProps;

export default ContactTable;
