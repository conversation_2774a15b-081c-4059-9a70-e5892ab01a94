import React from 'react';
import { Button, ButtonProps } from '@chakra-ui/react';

interface CustomButtonProps extends ButtonProps {
  children: React.ReactNode;
}

const AmpconButton: React.FC<CustomButtonProps> = ({ children, ...props }) => {
  return (
    <Button
      color="#14C9BB"
      backgroundColor="#FFFFFF"
      borderColor="#14C9BB"
      borderRadius="2px"
      h="40px"
      lineHeight="40px"
      size="sm"
      cursor="pointer"
      fontSize="14px"
      _hover={{
        backgroundColor: "#FFFFFF",
      }}
      {...props} // 允许传递额外的 Button 属性
    >
      {children}
    </Button>
  );
};

export default AmpconButton;
