import React from 'react';
import { Button, IconButton, Tooltip, useBreakpoint, LayoutProps, SpaceProps } from '@chakra-ui/react';
import { Plus } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';

interface Props extends LayoutProps, SpaceProps {
  onClick?: () => void;
  isDisabled?: boolean;
  isLoading?: boolean;
  isCompact?: boolean;
  label?: string;
  hidden?: boolean;
}

const defaultProps = {
  onClick: () => {},
  isDisabled: false,
  isLoading: false,
  isCompact: true,
  label: undefined,
  hidden: false,
};

const CreateButton = ({ onClick, isDisabled, isLoading, isCompact, label,hidden, ...props }: Props) => {
  const { t } = useTranslation();
  const breakpoint =  useBreakpoint();

  if (hidden) return null; // 如果 hidden 为 true，直接返回 null

  if (!isCompact && breakpoint !== 'base' && breakpoint !== 'sm') {
    return (
      <Button
        colorScheme="blue"
        type="button"
        onClick={onClick}
        rightIcon={<Plus size={20} />}
        isLoading={isLoading}
        isDisabled={isDisabled}
        {...props}
      >
        {label ?? t('common.create')}
      </Button>
    );
  }
  return (
    <Tooltip label={label ?? t('common.create')}>
      <IconButton
        aria-label="Create"
        colorScheme="blue"
        type="button"
        onClick={onClick}
        icon={<Plus size={20} />}
        isLoading={isLoading}
        isDisabled={isDisabled}
        {...props}
      />
    </Tooltip>
  );
};

CreateButton.defaultProps = defaultProps;

export default React.memo(CreateButton);
