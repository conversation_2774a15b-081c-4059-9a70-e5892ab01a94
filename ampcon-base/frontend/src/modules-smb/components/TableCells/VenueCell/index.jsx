import React from 'react';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';

const propTypes = {
  venueName: PropTypes.string,
  venueId: PropTypes.string,
};
const defaultProps = {
  venueName: '',
  venueId: '',
};

const VenueCell = ({ venueName, venueId }) => {
  const navigate = useNavigate();
  const goToVenuePage = () => {
    navigate(`/wireless/entities/Monitor#${venueId}`); 
  };

  if (venueName !== '' && venueId !== '') {
    return (
      <a
        href="#" 
        onClick={(e) => {
          e.preventDefault(); 
          goToVenuePage();
        }}
        style={{ 
          textDecoration: 'underline', 
          color: '#14C9BB',
          cursor: 'pointer' 
        }}
      >
        {venueName}
      </a>
    );
  }

  return null;
};

VenueCell.propTypes = propTypes;
VenueCell.defaultProps = defaultProps;
export default React.memo(VenueCell);