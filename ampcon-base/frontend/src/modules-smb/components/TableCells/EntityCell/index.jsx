import React from 'react';
import { Button } from '@chakra-ui/react';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';

const propTypes = {
  entityName: PropTypes.string,
  entityId: PropTypes.string,
};
const defaultProps = {
  entityName: '',
  entityId: '',
};

const EntityCell = ({ entityName, entityId }) => {
  const navigate = useNavigate();

  const goTo = () => navigate(`/wireless/entity/${entityId}`);

  if (entityName !== '' && entityId !== '') {
    return (
      <Button size="sm" variant="link" onClick={goTo}>
        {entityName}
      </Button>
    );
  }

  return null;
};

EntityCell.propTypes = propTypes;
EntityCell.defaultProps = defaultProps;
export default React.memo(EntityCell);
