<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>Captive Portal</title>
  <style type="text/css">
    html,body{width:100%;height:100%;margin:0;padding:0}.container{width:100%;height:100%;min-height: 600px;display:-ms-flexbox;display:-webkit-box;display:flex;flex-direction:column;-ms-flex-align:center;-ms-flex-pack:center;-webkit-box-align:center;align-items:center;-webkit-box-pack:center;justify-content:center;background-color:#F0F8F9;background-repeat:no-repeat;background-size:100%100%;position:relative;font-family:Lato,Lato}.content{background:#FFFFFF;box-shadow:0px 0px 20px 0px#F5F5F5;border-radius:16px 16px 16px 16px;padding:20px 40px;max-width:80%;position:relative}.text-center{text-align:center}.lead{font-weight:700;font-size:32px;color:#212519;line-height:38px;text-align:left;overflow: hidden;word-wrap: break-word;}.btn{display:inline-block;font-weight:400;color:#212529;text-align:center;text-decoration:none;vertical-align:middle;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;background-color:transparent;border:1px solid transparent;padding:10px 16px;font-size:18px;line-height:1.5;border-radius:4px 4px 4px 4px}.btn-primary{color:#fff;background-color:#14C9BB;border-color:#14C9BB}.btn-block{width:100%}.form-signin{width:100%;max-width:360px;margin:20px auto;margin-bottom:50px;text-align:center;font-size:14px}.form-signin.form-control{position:relative;box-sizing:border-box;height:auto;padding:10px;font-size:16px;width:100%;resize:none;background:none;border:none}.form-signin.form-control:focus{z-index:2}.logo_p{margin:25px;margin-left:0}#logo{max-width:200px}#corporate-info{font-weight:400;font-size:14px;color:#929A9E;text-align:center;max-width:250px;position:absolute;bottom:20px;overflow: hidden;word-wrap: break-word;}#readme{background:#fff;border-radius:8px 8px 8px 8px;position:absolute;top:0;left:0;right:0;bottom:0;z-index:11}.terms-head{font-weight:700;font-size:20px;color:#323333;line-height:60px;text-indent:1em;border-bottom:1px solid#E7E7E7;position:relative}#readmeTxt{position:absolute;top:80px;left:20px;right:20px;bottom:20px;background:#F8FAFB;border-radius:4px 4px 4px 4px;padding:10px;overflow-y:auto;word-wrap: break-word;}#terms-btn{position:absolute;right:15px;font-size:16px;display:inline-block;cursor:pointer}#submit{margin-top:20px}#checkError{color:#F53F3F;font-size:12px}@media(max-width:800px){.lead{font-size:16px}#corporate-info{font-size:11px}}
  </style>
</head>
<body>
  <div class="container">
    <div class="content">
      <div id="auth_form">
        <p class="logo_p">
          <img id="logo" src="data:image/png;base64,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" alt="logo">
        </p>
        <div class="text-center"><p class="lead" id="title">Welcome to use Wi-Fi</p></div>
        <form class="form-signin" id="loginForm" method="post" name="loginForm" action="/hotspot" onsubmit="return login()">
          <div id="errormsg" class="lead text-center" style="color:#F56C6C"></div>
          <div id="divReadme" class="form-signin" style="max-width:680px">
            <div style="text-align: left; margin-bottom: 10px;">
              <input type="checkbox" id="agreeCheckbox">
              <label for="agreeCheckbox">
                I agree to <a href="javascript:void(0);" id="termsLink" style="color: #14C9BB; text-decoration: none;">Terms of Service</a>
              </label>
            </div>
            <div id="checkError" style="display: none;">You must agree to the Terms of Service to proceed.</div>
          </div>
          <input type="hidden" name="action" value="click">
          <input type="hidden" name="accept_terms" value="clicked">
          <button class="btn btn-primary btn-block" id="submit">Click</button>
        </form>
      </div>
      <div id="readme">
        <div class="terms-head">Terms of Service<div id="terms-btn">×</div></div>
        <div id="readmeTxt"></div>
      </div>
    </div>
    <div id="corporate-info">© 2025 FS.COM INC. All rights reserved</div>
  </div>
  <script>
    const termsLink = document.getElementById('termsLink');
    const readmeTextarea = document.getElementById('readme');
    const readmeTxt = document.getElementById('readmeTxt');
    const divReadme = document.getElementById('divReadme');
    const termsBtn = document.getElementById('terms-btn');
    const checkError = document.getElementById('checkError');
    const agreeCheckbox = document.getElementById('agreeCheckbox'); 

    readmeTextarea.style.display = 'none';
    if (readmeTxt.textContent.trim()) {
      divReadme.style.display = 'block';
    } else {
      divReadme.style.display = 'none';
    }
    function login() {
      if (validate()) {
        document.getElementById('loginForm').submit();
      }
      return false;
    }
    termsLink.addEventListener('click', () => {
      if (readmeTextarea.style.display === 'none') {
        readmeTextarea.style.display = 'block';
      } else {
        readmeTextarea.style.display = 'none';
      }
    });
    agreeCheckbox.addEventListener('change', () => {
      if (agreeCheckbox.checked) {
        checkError.style.display = 'none';
      } else {
        checkError.style.display = 'block';
      }
    });
    termsBtn.addEventListener('click', () => {
      readmeTextarea.style.display = 'none';
    });
    function validate() {
      if (readmeTxt.textContent.trim() && !agreeCheckbox.checked) {
        checkError.style.display = 'block';
        return false;
      }
      return true;
    }
  </script>
</body>

</html>