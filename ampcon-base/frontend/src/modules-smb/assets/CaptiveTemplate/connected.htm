<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>connected</title>
    <style type="text/css">
        html,
        body {
            width: 100%;
            height: 100%;
        }

        * {
            margin: 0;
            padding: 0;
        }

        .container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column; 
            align-items: center;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            background-color: #f0f8f9;
        }

        .welcome_box {
            box-sizing: border-box;
            max-width: 80%;
            border-radius: 16px;
            background: #fff;
            padding: 30px;
            box-shadow: 0px 0px 20px 0px #F5F5F5;
            display: flex;
            justify-content: space-around;
            flex-direction: column;
            margin: auto;
        }

        #title, #content {
            color: #212519;
        }

        #title {
            font-weight: 700;
            font-size: 16px;
        }

        #content {
			margin: 20px auto 0 0;
            font-weight: 400;
            font-size: 14px;
        }

        #corporate-info {
            font-size: 14px;
            font-weight: 400;
            color: #929A9E;
            margin-bottom: 32px;
        }

        .text-center {
            text-align: center;
        }
		
		/* 小屏幕设备 */
		@media (max-width: 480px) {
            #title {
                font-size: 16px;
            }
            
            #content {
                font-size: 14px;
            }
		}
		
		/* 平板设备 */
		@media (min-width: 481px) and (max-width: 768px) {
            #title {
                font-size: 24px;
            }
            
            #content {
                font-size: 20px;
            }
		}
		
		/* 桌面设备 */
		@media (min-width: 769px) {
            #title {
                font-size: 32px;
            }
            
            #content {
                font-size: 26px;
            }
            
            .welcome_box {
                justify-content: space-evenly;
                min-height: 30vh;
            }
		}
    </style>
</head>
<body>
    <div class="container">
        <div class="welcome_box">
            <p id="title">Welcome to Connect WiFi</p>
            <p id="content">You are already connected</p>
        </div>
        <div id="corporate-info">© 2025 FS.COM INC. All rights reserved</div>
    </div>

    <script type="text/javascript">

    </script>
</body>
</html>