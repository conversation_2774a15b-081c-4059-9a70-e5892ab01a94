import React from 'react';
import { Modal, Checkbox, Button, Space, Input } from 'antd';
import { SettingOutlined, RedoOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { SearchOutlined } from '@ant-design/icons';

interface BlacklistSettingsModalProps {
    isOpen: boolean;
    onClose: () => void;
    controller: {
        columnVisibility: Record<string, boolean>;
        columnOrder: string[];
        setColumnVisibility: (vis: Record<string, boolean>) => void;
        setColumnOrder: (order: string[]) => void;
        resetPreferences: () => void;
    };
    columns: Array<{
        key: string;
        title: string;
        dataIndex?: string;
    }>;
}

const BlacklistSettingsModal = ({ isOpen, onClose, controller, columns }: BlacklistSettingsModalProps) => {
    const { t } = useTranslation();
    const [searchText, setSearchText] = React.useState('');

    const modalStyle: React.CSSProperties = {
        position: 'fixed',
        top: '255px',
        right: '55px',
        margin: 0,
        boxShadow: 'none'
    };

    const handleReset = () => {
        controller.setColumnVisibility({});
        controller.setColumnOrder(columns.map(col => col.key));
        controller.resetPreferences();
        message.success(t('table.reset_success'));
    };

    const filteredColumns = columns.filter(column =>
        column.title.toLowerCase().includes(searchText.toLowerCase())
    );

    return (
        <Modal
            open={isOpen}
            onCancel={onClose}
            footer={
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Button
                        type="default"
                        onClick={handleReset}
                        style={{ color: '#14C9BB ' }}
                    >
                        Reset
                    </Button>
                    <Button
                        type="primary"
                        onClick={onClose}
                        style={{ background: '#14C9BB ', borderColor: '#14C9BB ' }}
                    >
                        Ok
                    </Button>
                </div>
            }
            width={280}
            style={modalStyle}
            //bodyStyle={{ padding: '0px 15px'}}
            mask={false}
        >
            <Input
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
                prefix={
                    <SearchOutlined
                        style={{
                            color: 'rgba(0, 0, 0, 0.45)',
                            marginRight: 8
                        }}
                    />
                }
            />

            <div style={{ maxHeight: 400, overflowY: 'auto' }}>
                {filteredColumns.map(column => (
                    <div
                        key={column.key}
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            padding: '8px 0',
                            borderBottom: '1px solid #f0f0f0'
                        }}
                    >
                        <Checkbox
                            checked={controller.columnVisibility[column.key] !== false}
                            onChange={e => {
                                controller.setColumnVisibility({
                                    ...controller.columnVisibility,
                                    [column.key]: e.target.checked
                                });
                            }}
                            style={{ marginRight: 8 }}
                        />
                        <span>{column.title}</span>
                    </div>
                ))}
            </div>
        </Modal>
    );
};

export default BlacklistSettingsModal;