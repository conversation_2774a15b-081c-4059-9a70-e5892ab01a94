import React, { useEffect, useRef } from 'react';
import { Modal, Form, Input, Button, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { useCreateBlacklist } from '@/modules-smb/hooks/Network/Blacklist';
import { AxiosError } from '@/modules-smb/models/Axios';

interface CreateBlacklistModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CreateBlacklistModal: React.FC<CreateBlacklistModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const createDevice = useCreateBlacklist();
  const inputRef = useRef<Input | null>(null);

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 200);
    } else {
      form.resetFields();
    }
  }, [isOpen]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      const serialNumber = values.serialNumber.toLowerCase();
      const reason = values.reason;

      createDevice.mutate(
        { serialNumber, reason },
        {
          onSuccess: () => {
            message.success(t('controller.devices.added_blacklist'));
            onClose();
          },
          onError: (e) => {
            message.error((e as AxiosError)?.response?.data?.ErrorDescription || t('common.error'));
          },
        },
      );
    } catch (err) {
      // validation error; do nothing
    }
  };

  const validateSerial = (_: any, value: string) => {
    const isValid = value?.length === 12 && /^[a-fA-F0-9]+$/.test(value);
    return isValid ? Promise.resolve() : Promise.reject(t('Invalid serial number. A serial number should be 12 HEX chars (A-F, 0-9)'));
  };

  return (
    <Modal
      title={null}
      open={isOpen}
      onCancel={onClose}
      onOk={handleOk}
      confirmLoading={createDevice.isLoading}
      width={680}
      style={{ height: 450 }}
      destroyOnClose
      footer={[
        <Button key="cancel" onClick={onClose}>
          {t('Cancel')}
        </Button>,
        <Button key="submit" type="primary" onClick={handleOk} loading={createDevice.isLoading}>
          {t('Apply')}
        </Button>,
      ]}
    >
      {/* Header */}
      <div style={{ padding: ' 0 0 0' }}>
        <div
          style={{
            width: '65px',
            height: '24px',
            fontFamily: 'Lato, sans-serif',
            fontWeight: 700,
            fontSize: '20px',
            lineHeight: '24px',
            textAlign: 'left',
            fontStyle: 'normal',
            textTransform: 'none'
          }}
        >
          {t('Create')}
        </div>
        <div
          style={{
            width: '680px',
            height: '1px',
            borderTop: '1px solid #E7E7E7',
            marginBottom: 24,
          }}
        />
      </div>

      {/* Form */}
      <Form
        form={form}
        layout="horizontal"
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 19 }}
        style={{ padding: '0 24px' }}
      >
        <Form.Item
          label={t('Serial Number')}
          name="serialNumber"
          rules={[
            { required: true, message: t('Please enter Serial Number') },
            { validator: validateSerial },
          ]}
        >
          <Input ref={inputRef} />
        </Form.Item>

        <Form.Item
          label={t('Reason')}
          name="reason"
          rules={[{ required: false }]}
        >
          <Input.TextArea rows={4} />
        </Form.Item>

        {/* Footer top border */}
        <div
          style={{
            borderTop: '1px solid #f0f0f0',
            marginTop: 24,
            marginBottom: 0,
          }}
        />
      </Form>
    </Modal >
  );
};

export default CreateBlacklistModal;
