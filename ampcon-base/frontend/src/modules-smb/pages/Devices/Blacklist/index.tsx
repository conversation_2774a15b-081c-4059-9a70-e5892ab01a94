import React, { useMemo, useState, useCallback, useEffect } from 'react';
import { Box } from '@chakra-ui/react';
import { Button, Space, message, Typography, Card, Tooltip } from 'antd';
import { PlusOutlined, ReloadOutlined, SettingOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
import { useGetBlacklistDevices, useGetBlacklistCount, BlacklistDevice } from '@/modules-smb/hooks/Network/Blacklist';
import { useDataGrid } from '@/modules-smb/components/DataTables/DataGrid/useDataGrid';
import FormattedDate from '@/modules-smb/components/InformationDisplays/FormattedDate';
import Actions from './Actions';
import CreateBlacklistModal from './CreateModal';
import EditBlacklistModal from './EditModal';
import BlacklistSettingsModal from './BlacklistSettingsModal';

const BlacklistDeviceList = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [device, setDevice] = useState<BlacklistDevice>();
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  const tableController = useDataGrid({
    tableSettingsId: 'gateway.blacklist.table',
    defaultOrder: ['serialNumber'],
    pageSize: 10,
  });

  const pageInfo = {
    limit: tableController.pageInfo.pageSize,
    index: tableController.pageInfo.pageIndex,
  };

  const getDevices = useGetBlacklistDevices({
    pageInfo,
    enabled: true,
  });

  const getCount = useGetBlacklistCount({ enabled: true });

  const handleEdit = useCallback((dev: BlacklistDevice) => {
    setDevice(dev);
    setIsEditOpen(true);
  }, []);

  const goToSerial = useCallback((serial: string) => {
    navigate(`/wireless/devices/${serial}`);
  }, [navigate]);

  const handleBack = () => navigate(-1);

  useEffect(() => {
    getDevices.refetch();
  }, [tableController.pageInfo.pageIndex, tableController.pageInfo.pageSize]);

  const handleTableChange = useCallback((pagination) => {
    tableController.onPaginationChange({
      pageIndex: pagination.current! - 1,
      pageSize: pagination.pageSize!,
    });
  }, [tableController.setPageInfo, getDevices.refetch]);



  // 完整列定义（title 支持 ReactNode）
  const fullColumns = useMemo(() => [
    {
      key: 'serialNumber',
      title: t('inventory.serial_number'),
      label: t('inventory.serial_number'),
      dataIndex: 'serialNumber',
      sorter: true,
      render: (text: string, record: BlacklistDevice) => (
        <Typography.Link onClick={() => goToSerial(record.serialNumber)}>
          {text}
        </Typography.Link>
      ),
    },
    {
      key: 'created',
      title: 'Added',
      label: 'Added',
      dataIndex: 'created',
      sorter: true,
      render: (text: string) => <FormattedDate date={text} />,
    },
    {
      key: 'author',
      title: 'By',
      label: 'By',
      dataIndex: 'author',
      sorter: true,
    },
    {
      key: 'reason',
      title: 'Reason',
      label: 'Reason',
      dataIndex: 'reason',
      sorter: true,
    },
    {
      key: 'actions',
      title: 'Operation',
      label: 'Operation',
      render: (_: unknown, record: BlacklistDevice) => (
        <Actions device={record} refreshTable={getDevices.refetch} onOpenEdit={handleEdit} />
      ),
    },
    {
      key: 'settings',
      title: (
        <Tooltip title="Table Settings">
          <Button
            type="text"
            icon={<SettingOutlined style={{ color: '#000000' }} />}
            onClick={() => setIsSettingsOpen(true)}
            style={{ padding: 0, border: 'none', boxShadow: 'none' }}
          />
        </Tooltip>
      ),
      width: 50,
      align: 'center',
      render: () => null,
    }
  ], [t, goToSerial, handleEdit, getDevices.refetch]);

  // 简化版列（用于 SettingsModal，要求 title 为 string）
  const simpleColumns = useMemo(() => {
    return fullColumns
      .filter(col => typeof col.title === 'string')
      .map(col => ({
        key: col.key,
        title: col.title as string,
        dataIndex: col.dataIndex,
      }));
  }, [fullColumns]);

  return (
    <>
      <Box style={{ marginTop: 1, marginBottom: 16 }}>
        <Box
          as="button"
          onClick={handleBack}
          display="flex"
          fontFamily="Lato, sans-serif"
          fontWeight={500}
          fontSize="14px"
          color="#929A9E;"
          lineHeight="16px"
          cursor="pointer"
          bg="transparent"
          border="none"
          p={0}
          _hover={{ opacity: 0.8 }}
          _active={{ opacity: 0.6 }}
          _focus={{ outline: 'none' }}
        >
          <Box as="span" mr={1}>←</Box>
          <Box as="span">Back</Box>
        </Box>
      </Box>

      <Card style={{ width: '100%', minHeight: '100%', borderRadius: 0, boxShadow: 'none' }} bodyStyle={{ padding: 24 }}>
        <Space style={{ marginTop: 16, marginBottom: 8 }}>
          <Button type="primary" icon={<PlusOutlined />} onClick={() => setIsCreateOpen(true)}>Create</Button>
          <Button icon={<ReloadOutlined />} onClick={() => {
            getDevices.refetch();
            message.success('Blacklist refreshed');
          }}>Refresh</Button>
        </Space>

        <WirelessCustomTable
          columns={fullColumns}
          dataSource={getDevices.data?.devices || []}
          loading={getDevices.isFetching || getCount.isFetching}
          pagination={{
            current: tableController.pageInfo.pageIndex + 1,
            pageSize: tableController.pageInfo.pageSize,
            total: getCount.data?.count,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} items`,
          }}
          onChange={handleTableChange}
        />
      </Card>

      <BlacklistSettingsModal
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        controller={{
          ...tableController,
          columnVisibility: tableController.columnVisibility ?? {},
          columnOrder: simpleColumns.map(col => col.key),
          setColumnVisibility: tableController.setColumnVisibility,
          setColumnOrder: tableController.setColumnOrder,
          resetPreferences: () => {
            message.success(t('table.reset_success'));
          }
        }}
        columns={simpleColumns}
      />

      <CreateBlacklistModal isOpen={isCreateOpen} onClose={() => setIsCreateOpen(false)} />
      <EditBlacklistModal device={device} modalProps={{ isOpen: isEditOpen, onClose: () => setIsEditOpen(false) }} />
    </>
  );
};

export default React.memo(BlacklistDeviceList);
