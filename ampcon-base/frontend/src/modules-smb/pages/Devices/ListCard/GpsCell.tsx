import * as React from 'react';
import { Center } from '@chakra-ui/react';
import { DeviceWithStatus } from '@/modules-smb/hooks/Network/Devices';
import LocationDisplayButton from '@/modules-smb/pages/Device/LocationDisplayButton';

type Props = {
  device: DeviceWithStatus;
};
const DeviceTableGpsCell = ({ device }: Props) => {
  if (!device.hasGPS) return <Center>-</Center>;

  return (
    <Center>
      <LocationDisplayButton serialNumber={device.serialNumber} isCompact />
    </Center>
  );
};

export default DeviceTableGpsCell;
