import * as React from 'react';
import { Button } from '@chakra-ui/react';
import { DeviceWithStatus } from '@/modules-smb/hooks/Network/Devices';
import { useGetTag } from '@/modules-smb/hooks/Network/Inventory';
import { useNavigate } from 'react-router-dom';

type Props = {
  device: DeviceWithStatus;
};

const ProvisioningStatusCell = ({ device }: Props) => {
  const getTag = useGetTag({ serialNumber: device.serialNumber });
  const navigate = useNavigate();

  const handleClick = (path: string) => () => {
    navigate(`/wireless/${path}`);
  };

  if (getTag.data?.extendedInfo?.entity?.name) {
    return (
      <Button variant="link" size="sm" onClick={handleClick(`entity/${getTag.data?.entity}`)}>
        {getTag.data?.extendedInfo?.entity?.name}
      </Button>
    );
  }
  if (getTag.data?.extendedInfo?.venue?.name) {
    return (
      <Button variant="link" size="sm" onClick={handleClick(`venue/${getTag.data?.venue}`)}>
        {getTag.data?.extendedInfo?.venue?.name}
      </Button>
    );
  }
  if (getTag.data?.extendedInfo?.subscriber?.name) {
    return (
      <Button variant="link" size="sm" onClick={handleClick(`venue/${getTag.data?.subscriber}`)}>
        {getTag.data?.extendedInfo?.subscriber?.name}
      </Button>
    );
  }
  return <span>-</span>;
};

export default ProvisioningStatusCell;
