import React, {Suspense, useState, useEffect} from "react";
import {
    Alert,
    AlertDescription,
    AlertIcon,
    AlertTitle,
    Box,
    Center,
    Flex,
    Heading,
    Spacer,
    Spinner,
    Text,
    Tooltip
} from "@chakra-ui/react";
import {Info, WifiHigh} from "@phosphor-icons/react";
import {useTranslation} from "react-i18next";
import Masonry from "react-masonry-css";
import AssociationsPieChart from "./AssociationsPieChart";
import CertificatesPie<PERSON>hart from "./CertificatesPieChart";
import CommandsBar<PERSON>hart from "./CommandsBarChart";
import ConnectedPie<PERSON>hart from "./ConnectedPieChart";
import DeviceTypes<PERSON>ie<PERSON>hart from "./DeviceTypesPieChart";
import MemoryBar<PERSON>hart from "./MemoryBarChart";
import MemorySimpleChart from "./MemorySimpleChart";
import OverallHealthSimple from "./OverallHealth";
import OverallHealthPieChart from "./OverallHealthPieChart";
import UptimesBar<PERSON><PERSON> from "./UptimesBar<PERSON>hart";
import VendorBar<PERSON>hart from "./VendorBarChart";
import RefreshButton from "@/modules-smb/components/Buttons/RefreshButton";
import {Card} from "@/modules-smb/components/Containers/Card";
import {CardHeader} from "@/modules-smb/components/Containers/Card/CardHeader";
import SimpleIconStatDisplay from "@/modules-smb/components/Containers/SimpleIconStatDisplay";
import FormattedDate from "@/modules-smb/components/InformationDisplays/FormattedDate";
import {useGetControllerDashboard} from "@/modules-smb/hooks/Network/Controller";

const DevicesDashboard = () => {
    const {t} = useTranslation();
    const getDashboard = useGetControllerDashboard();
    // const [getDashboard, setgetDashboard] = useState(null);
    // useEffect(() => {
    //     setgetDashboard(useGetControllerDashboard());
    // }, []);
    const chartOptions = {
        animation: {
          duration: 0 // 禁用动画
        },
        responsive: true,
        maintainAspectRatio: false
      };
    return (
        <>
        {getDashboard ? (<Suspense fallback={<div>Loading...</div>}>
                <Card mb="20px">
                    <CardHeader variant="unstyled" px={4} py={2}>
                        <Flex alignItems="center">
                            <Heading size="md">{t("analytics.last_ping")}</Heading>
                            <Text ml={1} pt={0.5}>
                                <FormattedDate
                                    date={getDashboard.data?.snapshot ?? 0}
                                    key={getDashboard.dataUpdatedAt}
                                />
                            </Text>
                            <Tooltip label={t("controller.dashboard.last_ping_explanation")} hasArrow>
                                <Info style={{marginLeft: "4px", marginTop: "2px"}} />
                            </Tooltip>
                        </Flex>
                        <Spacer />
                        <RefreshButton isCompact onClick={getDashboard.refetch} isFetching={getDashboard.isFetching} />
                    </CardHeader>
                </Card>
                <Box display="block" w="100%">
                    <>
                        {getDashboard.isLoading && (
                            <Center my="100px">
                                <Spinner size="xl" />
                            </Center>
                        )}
                        {getDashboard.error && (
                            <Center my="100px">
                                <Alert status="error" mb={4} w="unset">
                                    <AlertIcon />
                                    <Box>
                                        <AlertTitle>{t("controller.dashboard.error_fetching")}</AlertTitle>
                                        {
                                            // @ts-ignore
                                            <AlertDescription>
                                                {getDashboard.error?.response?.data?.ErrorDescription}
                                            </AlertDescription>
                                        }
                                    </Box>
                                </Alert>
                            </Center>
                        )}
                        {getDashboard.data && (
                            <Masonry
                                breakpointCols={{
                                    default: 3,
                                    1800: 3,
                                    1400: 2,
                                    800: 1
                                }}
                                className="my-masonry-grid"
                                columnClassName="my-masonry-grid_column"
                            >
                                <SimpleIconStatDisplay
                                    title={t("devices.title")}
                                    value={getDashboard.data?.numberOfDevices ?? 0}
                                    description={t("controller.dashboard.devices_explanation")}
                                    icon={WifiHigh}
                                    color={["blue.300", "blue.300"]}
                                />
                                <OverallHealthSimple data={getDashboard.data.healths}/>
                                <MemorySimpleChart data={getDashboard.data.memoryUsed} />
                                <ConnectedPieChart data={getDashboard.data}  options={chartOptions}/>
                                <OverallHealthPieChart data={getDashboard.data.healths} options={chartOptions} />
                                <UptimesBarChart data={getDashboard.data.upTimes}  options={chartOptions}/>
                                {/*<VendorBarChart data={getDashboard.data.vendors} />*/}
                                <AssociationsPieChart data={getDashboard.data.associations} options={chartOptions} />
                                <MemoryBarChart data={getDashboard.data.memoryUsed} options={chartOptions}/>
                                <DeviceTypesPieChart data={getDashboard.data.deviceType} options={chartOptions}/>
                                <CommandsBarChart data={getDashboard.data.commands} options={chartOptions}/>
                                {/*<CertificatesPieChart data={getDashboard.data.certificates} />*/}
                            </Masonry>
                        )}
                    </>
                </Box>
            </Suspense>) :("error")}
        </>
    );
};

export default DevicesDashboard;
