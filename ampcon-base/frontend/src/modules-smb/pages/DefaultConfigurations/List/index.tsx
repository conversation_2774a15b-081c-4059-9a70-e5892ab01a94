import * as React from 'react';
import { Box, Heading, Spacer, useDisclosure } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import Actions from './Actions';
import CreateDefaultConfigurationModal from './CreateModal';
import EditDefaultConfiguration from './EditModal';
import  RefreshButton  from '@/modules-smb/components/Buttons/RefreshButton';
import { Card } from '@/modules-smb/components/Containers/Card';
import { CardBody } from '@/modules-smb/components/Containers/Card/CardBody';
import { CardHeader } from '@/modules-smb/components/Containers/Card/CardHeader';
import { DataTable } from '@/modules-smb/components/DataTables/DataTable';
import FormattedDate from '@/modules-smb/components/InformationDisplays/FormattedDate';
import  LoadingOverlay  from '@/modules-smb/components/LoadingOverlay';
import { DefaultConfigurationResponse, useGetDefaultConfigurations } from '@/modules-smb/hooks/Network/DefaultConfigurations';
import { Column } from '@/modules-smb/models/Table';

const DefaultConfigurationsList = () => {
  const { t } = useTranslation();
  const getConfigs = useGetDefaultConfigurations({});
  const modalProps = useDisclosure();
  const [selectedConfig, setSelectedConfig] = React.useState<DefaultConfigurationResponse | undefined>();

  const onViewDetails = (config: DefaultConfigurationResponse) => {
    setSelectedConfig(config);
    modalProps.onOpen();
  };

  const dateCell = React.useCallback((v: number) => <FormattedDate date={v} />, []);
  const deviceTypesCell = React.useCallback((v: string[]) => {
    if (v.length === 0) {
      return t('common.none');
    }
    if (v.length <= 3) {
      return v.join(', ');
    }
    return `${v.join(', ')}...`;
  }, []);
  const actionCell = React.useCallback(
    (config: DefaultConfigurationResponse) => <Actions config={config} handleViewDetails={onViewDetails} />,
    [],
  );

  const columns: Column<DefaultConfigurationResponse>[] = React.useMemo(
    () => [
      {
        id: 'name',
        Header: t('common.name'),
        Footer: '',
        accessor: 'name',
        customWidth: '150px',
        alwaysShow: true,
      },
      {
        id: 'modified',
        Header: t('common.modified'),
        Footer: '',
        accessor: 'modified',
        Cell: ({ cell }) => dateCell(cell.row.original.lastModified),
        customWidth: '50px',
      },
      {
        id: 'platform',
        Header: 'Platform',
        Footer: '',
        accessor: 'platform',
        Cell: ({ cell }) => cell.row.original.platform.toUpperCase(),
        customWidth: '50px',
      },
      {
        id: 'modelIds',
        Header: t('controller.dashboard.device_types'),
        Footer: '',
        accessor: 'modelIds',
        Cell: ({ cell }) => deviceTypesCell(cell.row.original.modelIds),
        customWidth: '50px',
      },
      {
        id: 'description',
        Header: t('common.description'),
        Footer: '',
        accessor: 'description',
      },
      {
        id: 'actions',
        Header: t('common.actions'),
        Footer: '',
        accessor: 'actions',
        Cell: (v) => actionCell(v.cell.row.original),
        customWidth: '50px',
        alwaysShow: true,
        disableSortBy: true,
      },
    ],
    [dateCell],
  );

  return (
    <Card>
      <CardHeader>
        <Heading size="md">
          {t('controller.configurations.title')} {getConfigs.data ? `(${getConfigs.data.length})` : ''}
        </Heading>
        <Spacer />
        <CreateDefaultConfigurationModal />
        <RefreshButton onClick={getConfigs.refetch} isCompact isFetching={getConfigs.isFetching} />
      </CardHeader>
      <CardBody>
        <Box overflowX="auto" w="100%">
          <LoadingOverlay isLoading={getConfigs.isFetching}>
            <DataTable<DefaultConfigurationResponse>
              columns={columns}
              saveSettingsId="firmware.table"
              data={getConfigs.data ?? []}
              obj={t('controller.configurations.title')}
              minHeight="200px"
              sortBy={[{ id: 'name', desc: true }]}
              onRowClick={onViewDetails}
            />
          </LoadingOverlay>
        </Box>
      </CardBody>
      <EditDefaultConfiguration modalProps={modalProps} config={selectedConfig} />
    </Card>
  );
};

export default DefaultConfigurationsList;
