import React, { useCallback, useState } from 'react';
import { Box, useDisclosure } from '@chakra-ui/react';
import { v4 as uuid } from 'uuid';
import Actions from './Actions';
import EditSubscriberDeviceModal from '@/modules-smb/components/Modals/SubscriberDevice/EditModal';
import FactoryResetModal from '@/modules-smb/components/Modals/SubscriberDevice/FactoryResetModal';
// import FirmwareUpgradeModal from '@/modules-smb/components/Modals/SubscriberDevice/FirmwareUpgradeModal';
import WifiScanModal from '@/modules-smb/components/Modals/SubscriberDevice/WifiScanModal';
import SubscriberDeviceSearch from '@/modules-smb/components/SearchBars/SubscriberDeviceSearch';
import SubscriberDeviceTable from '@/modules-smb/components/Tables/SubscriberDeviceTable';
import useObjectModal from '@/modules-smb/hooks/useObjectModal';
import useRefreshId from '@/modules-smb/hooks/useRefreshId';

interface Props {
  operatorId: string;
}

const OperatorDevicesTab: React.FC<Props> = ({ operatorId }) => {
  const { refreshId, refresh } = useRefreshId();
  const { obj: subscriberDevice, openModal, isOpen, onClose } = useObjectModal();
  const [serialNumber, setSerialNumber] = useState<string>('');
  const scanModalProps = useDisclosure();
  const resetModalProps = useDisclosure();
  const upgradeModalProps = useDisclosure();
  const onOpenScan = (serial: string) => {
    setSerialNumber(serial);
    scanModalProps.onOpen();
  };
  const onOpenFactoryReset = (serial: string) => {
    setSerialNumber(serial);
    resetModalProps.onOpen();
  };
  const onOpenUpgradeModal = (serial: string) => {
    setSerialNumber(serial);
    upgradeModalProps.onOpen();
  };
  const actions = useCallback(
    (cell) => (
      <Actions
        key={uuid()}
        cell={cell.row}
        refreshTable={refresh}
        openEdit={openModal}
        onOpenScan={onOpenScan}
        onOpenFactoryReset={onOpenFactoryReset}
        onOpenUpgradeModal={onOpenUpgradeModal}
      />
    ),
    [openModal, refreshId],
  );

  return (
    <>
      <Box w="250px">
        <SubscriberDeviceSearch operatorId={operatorId} onClick={openModal} />
      </Box>
      <SubscriberDeviceTable
        operatorId={operatorId}
        onOpenDetails={openModal}
        actions={actions}
        refreshId={refreshId}
        minHeight="270px"
      />
      <EditSubscriberDeviceModal
        isOpen={isOpen}
        onClose={onClose}
        subscriberDevice={subscriberDevice ?? undefined}
        refresh={refresh}
        operatorId={operatorId}
        onOpenScan={onOpenScan}
        onOpenFactoryReset={onOpenFactoryReset}
        onOpenUpgradeModal={onOpenUpgradeModal}
      />
      <WifiScanModal modalProps={scanModalProps} serialNumber={serialNumber} />
      {/* <FirmwareUpgradeModal modalProps={upgradeModalProps} serialNumber={serialNumber} /> */}
      <FactoryResetModal modalProps={resetModalProps} serialNumber={serialNumber} />
    </>
  );
};
export default OperatorDevicesTab;
