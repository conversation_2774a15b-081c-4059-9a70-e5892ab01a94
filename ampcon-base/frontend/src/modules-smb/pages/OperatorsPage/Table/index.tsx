import React, { useCallback, useMemo, useState } from 'react';
import { Box, Flex, Heading, Spacer } from '@chakra-ui/react';
import { UseQueryResult } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { v4 as uuid } from 'uuid';
import Actions from './Actions';
import RefreshButton from '@/modules-smb/components/Buttons/RefreshButton';
import Card from '@/modules-smb/components/Card';
import CardBody from '@/modules-smb/components/Card/CardBody';
import CardHeader from '@/modules-smb/components/Card/CardHeader';
import ColumnPicker from '@/modules-smb/components/ColumnPicker';
import DataTable from '@/modules-smb/components/DataTable';
import FormattedDate from '@/modules-smb/components/FormattedDate';
import CreateOperatorModal from '@/modules-smb/components/Modals/Operator/CreateOperatorModal';
import { OperatorApiResponse, useGetOperatorCount, useGetOperators } from '@/modules-smb/hooks/Network/Operators';
import useControlledTable from '@/modules-smb/hooks/useControlledTable';
import { Column } from '@/modules-smb/models/Table';

const OperatorsTable = () => {
  const { t } = useTranslation();
  const {
    count,
    data: operators,
    isFetching,
    setPageInfo,
    refetchCount,
  } = useControlledTable({
    useCount: useGetOperatorCount as (props: unknown) => UseQueryResult,
    useGet: useGetOperators as (props: unknown) => UseQueryResult,
  });
  const [hiddenColumns, setHiddenColumns] = useState<string[]>([]);
  const navigate = useNavigate();

  const handleGoToClick = (operator: { id: string }) => navigate(`/wireless/operators/${operator.id}`);

  const memoizedActions = useCallback(
    (cell) => <Actions cell={cell.row} refreshTable={refetchCount} key={uuid()} />,
    [],
  );
  const memoizedDate = useCallback((cell, key) => <FormattedDate date={cell.row.values[key]} key={uuid()} />, []);

  const columns: Column<OperatorApiResponse>[] = useMemo(
    (): Column<OperatorApiResponse>[] => [
      {
        id: 'name',
        Header: t('common.name'),
        Footer: '',
        accessor: 'name',
        customMaxWidth: '200px',
        customWidth: 'calc(15vh)',
        customMinWidth: '150px',
      },
      {
        id: 'description',
        Header: t('common.description'),
        Footer: '',
        accessor: 'description',
        disableSortBy: true,
      },
      {
        id: 'modified',
        Header: t('common.modified'),
        Footer: '',
        accessor: 'modified',
        Cell: ({ cell }) => memoizedDate(cell, 'modified'),
        customMinWidth: '150px',
        customWidth: '150px',
      },
      {
        id: 'actions',
        Header: t('common.actions'),
        Footer: '',
        accessor: 'Id',
        customWidth: '80px',
        Cell: ({ cell }) => memoizedActions(cell),
        disableSortBy: true,
        alwaysShow: true,
      },
    ],
    [t],
  );

  return (
    <Card>
      <CardHeader>
        <Heading size="md" mt={1}>{`${count ?? 0} ${t('operator.operator', { count: count ?? 0 })}`}</Heading>
        <Spacer />
        <Flex flexDirection="row" alignItems="center">
          <Box ms="auto" display="flex">
            <ColumnPicker
              columns={columns}
              hiddenColumns={hiddenColumns}
              setHiddenColumns={setHiddenColumns}
              preference="provisioning.operatorTable.hiddenColumns"
            />
            <CreateOperatorModal refresh={refetchCount} />
            <RefreshButton onClick={refetchCount} isFetching={isFetching} ml={2} />
          </Box>
        </Flex>
      </CardHeader>
      <CardBody>
        <Box overflowX="auto" w="100%">
          <DataTable<OperatorApiResponse>
            columns={
              columns as {
                id: string;
                Header: string;
                Footer: string;
                accessor: string;
              }[]
            }
            data={(operators as unknown as OperatorApiResponse[]) ?? []}
            sortBy={[
              {
                id: 'name',
                desc: false,
              },
            ]}
            isLoading={isFetching}
            isManual
            hiddenColumns={hiddenColumns}
            obj={t('operator.other')}
            count={count || 0}
            // @ts-ignore
            setPageInfo={setPageInfo}
            fullScreen
            saveSettingsId="operators.table"
            onRowClick={handleGoToClick}
            isRowClickable={() => true}
          />
        </Box>
      </CardBody>
    </Card>
  );
};

export default OperatorsTable;
