import React from 'react';
import { <PERSON><PERSON>, ModalOverlay, Modal<PERSON>ontent, Modal<PERSON>eader, Modal<PERSON>ooter, ModalBody, Button, Text } from '@chakra-ui/react';

const VlanErrorModal = ({ isOpen, onClose, errors }) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>VLAN Configuration Errors</ModalHeader>
        <ModalBody>
          <Text mb={4}>Please resolve the following VLAN conflicts before saving:</Text>
          {errors.map((error, index) => (
            <Text key={index} color="red.500" fontSize="sm" mb={4}>
              {error}
            </Text>
          ))}
        </ModalBody>
        <ModalFooter>
          <Button colorScheme="red" mr={3} onClick={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
export default VlanErrorModal;