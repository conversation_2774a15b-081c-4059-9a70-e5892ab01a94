/* eslint-disable react/no-array-index-key */
import React, { useCallback, useState,useEffect, useRef } from 'react';
import { Box, Center, TabList, TabPanel, TabPanels, Tabs } from '@chakra-ui/react';
import PropTypes from 'prop-types';
import CreateInterfaceButton from './CreateInterfaceButton';
import InterfaceTab from './InterfaceTab';
import SingleInterface from './SingleInterface';
import Card from '@/modules-smb/components/Card';
import CardBody from '@/modules-smb/components/Card/CardBody';
import { SINGLE_INTERFACE_SCHEMA } from './interfacesConstants';
import { useTranslation } from 'react-i18next';

const propTypes = {
  editing: PropTypes.bool.isRequired,
  arrayHelpers: PropTypes.shape({
    push: PropTypes.func.isRequired,
    remove: PropTypes.func.isRequired,
  }).isRequired,
  interfacesLength: PropTypes.number.isRequired,
  parent: PropTypes.shape({
    entity: PropTypes.string,
    venue: PropTypes.string,
    subscriber: PropTypes.string,
  })
};
const deepOmitLocked = (obj) => {
  if (Array.isArray(obj)) {
    return obj.map(deepOmitLocked);
  } else if (typeof obj === 'object' && obj !== null) {
    return Object.entries(obj).reduce((acc, [key, value]) => {
      if (key === '__locked') return acc;
      acc[key] = deepOmitLocked(value);
      return acc;
    }, {});
  }
  return obj;
};

const Interfaces = ({ editing, arrayHelpers, interfacesLength, parent }) => {
  const [tabIndex, setTabIndex] = useState(0);
  const { t } = useTranslation();
  const hasAutoAddedDefault = useRef(false);

  useEffect(() => {
  if (!hasAutoAddedDefault.current && interfacesLength === 0 && editing) {
    const rawDefaultInterface = SINGLE_INTERFACE_SCHEMA(t, true, 'upstream', 'Default', true).cast();
    const cleanedDefault = deepOmitLocked(rawDefaultInterface);
    arrayHelpers.push(cleanedDefault);
    setTabIndex(0);
    hasAutoAddedDefault.current = true;
  }
}, [interfacesLength, editing, arrayHelpers, setTabIndex, t]);

  const handleRemove = (index) => {
    arrayHelpers.remove(index);
    if (index > 0) setTabIndex(0);
  };

  const handleTabsChange = useCallback((index) => {
    setTabIndex(index);
  }, []);

  if (interfacesLength === 0) {
    return (
      <Center>
        <CreateInterfaceButton
          editing={editing}
          arrayHelpers={arrayHelpers}
          setTabIndex={setTabIndex}
          arrLength={interfacesLength}
        />
      </Center>
    );
  }
  return (
    <Card variant="widget">
      <CardBody display="block">
        <Box display="unset" position="unset" w="100%">
          <Tabs index={tabIndex} onChange={handleTabsChange} variant="enclosed" isLazy w="100%">
            <Box overflowX="auto" overflowY="auto" pt={1} h="56px">
              <TabList mt={0}>
                {Array(interfacesLength)
                  .fill(1)
                  .map((el, i) => (
                    <InterfaceTab key={i} index={i} />
                  ))}
                <CreateInterfaceButton
                  editing={editing}
                  arrayHelpers={arrayHelpers}
                  setTabIndex={setTabIndex}
                  arrLength={interfacesLength}
                />
              </TabList>
            </Box>
            <TabPanels w="100%">
              {Array(interfacesLength)
                .fill(1)
                .map((el, i) => (
                  <TabPanel key={i}>
                    <SingleInterface index={i} remove={handleRemove} editing={editing} parent={parent} />
                  </TabPanel>
                ))}
            </TabPanels>
          </Tabs>
        </Box>
      </CardBody>
    </Card>
  );
};

Interfaces.propTypes = propTypes;
export default React.memo(Interfaces);
