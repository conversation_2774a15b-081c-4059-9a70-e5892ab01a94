import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalCloseButton,
  ModalBody,
  Modal<PERSON>ooter,
  ModalHeader,
  Button,
  Box,
  Input,
  FormControl,
  FormLabel,
  Textarea,
  Flex,
  useToast,
} from '@chakra-ui/react';
import clickTemplate from '@/modules-smb/assets/CaptiveTemplate/click.htm';
import credentialsTemplate from '@/modules-smb/assets/CaptiveTemplate/credentials.htm';
import radiusTemplate from '@/modules-smb/assets/CaptiveTemplate/radius.htm';
import AmpconButton from '@/modules-smb/components/Buttons/AmpconButton';
import debounce from 'lodash.debounce';
import { convertHtmlToBase64 } from '@/modules-smb/utils/configHelpers';


interface WebRootCustomizationModalProps {
  isOpen: boolean;
  authMode: string;
  onClose: () => void;
  onSave: (updatedHtmlBase64: string) => void; // 保存 Base64 转换后的 HTML
}

const Customization: React.FC<WebRootCustomizationModalProps> = ({
  isOpen,
  authMode,
  onClose,
  onSave
}) => {
  const toast = useToast();
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [backgroundColor, setBackgroundColor] = useState<string>('#F0F8F9');
  // const [backgroundImageBase64, setBackgroundImageBase64] = useState<string | null>(null);
  const [logoBase64, setLogoBase64] = useState<string | null>(null);
  const [welcomeMessage, setWelcomeMessage] = useState<string>('Welcome to use Wi-Fi');
  const [termsOfService, setTermsOfService] = useState<string>('');
  const [corporateInfo, setCorporateInfo] = useState<string>('© 2025 FS.COM INC. All rights reserved');

  const getDefaultTemplate = () => {
    switch (authMode) {
      case 'click-to-continue':
        return clickTemplate;
      case 'radius':
        return radiusTemplate;
      case 'credentials':
        return credentialsTemplate;
      default:
        return clickTemplate;
    }
  }

  const debouncedSetHtmlContent = useCallback(
    debounce((updatedHtml: string) => {
      setHtmlContent(updatedHtml);
    }, 300),
    []
  );

  // 动态加载 HTML 文件内容
  useEffect(() => {
    const templatePath = getDefaultTemplate();
    
    fetch(templatePath)
      .then((response) => response.text())
      .then((html) => setHtmlContent(html))
      .catch((error) => console.error('Failed to load defaultHtml:', error));
  }, [authMode]);

  // 更新 HTML 内容
  useEffect(() => {
    if (!htmlContent) return;

    let updatedHtml = htmlContent;

    // 修改背景颜色
    if (backgroundColor) {
      updatedHtml = updatedHtml.replace(
        /background-color:\s*(?:unset|#[a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)\s*;?/i,
        `background-color: ${backgroundColor};`
      );
    }

    // 替换背景图片
    // if (backgroundImageBase64) {
    //   updatedHtml = updatedHtml.replace(
    //     /background-image:\s*url\(([^)]*)\);?/,
    //     `background-image: url(${backgroundImageBase64});`
    //   );
    // }

    // 替换 Logo 图片
    if (logoBase64) {
      updatedHtml = updatedHtml.replace(/<img id="logo"[^>]*src="[^"]*"[^>]*>/, `<img id="logo" src="${logoBase64}" alt="logo" />`);
    }

    // 替换 Welcome Message
    updatedHtml = updatedHtml.replace(/<p class="lead" id="title">.*?<\/p>/, `<p class="lead" id="title">${welcomeMessage}</p>`);

    // 替换 Terms of Service
    updatedHtml = updatedHtml.replace(
      /<div[^>]*id="readmeTxt"[^>]*>([\s\S]*?)<\/div>/,
      `<div id="readmeTxt">${termsOfService}</div>`
    );

    // 替换 Corporate Info
    updatedHtml = updatedHtml.replace(
      /<div[^>]*id="corporate-info"[^>]*>([\s\S]*?)<\/div>/,
      `<div id="corporate-info">${corporateInfo}</div>`
    );

    debouncedSetHtmlContent(updatedHtml);
  }, [backgroundColor, logoBase64, welcomeMessage, termsOfService, corporateInfo]);
  
  // 手动绑定 Terms of Servic交互事件
  useEffect(() => {
    const termsLink = document.getElementById('termsLink');
    const readmeTextarea = document.getElementById('readme') as HTMLTextAreaElement;
    const readmeTxt = document.getElementById('readmeTxt');
    const divReadme = document.getElementById('divReadme');
    const termsBtn = document.getElementById('terms-btn');
    const submitButton = document.getElementById('submit');
    
    if (readmeTextarea && divReadme && readmeTxt) {
        if ((readmeTxt.textContent || '').trim()) {
            divReadme.style.display = 'block';
        } else {
            divReadme.style.display = 'none';
        }
    }

    if (termsBtn) {
      termsBtn.addEventListener('click', () => {
        if (readmeTextarea) {
          readmeTextarea.style.display = 'none';
        }
      });
    }

    // 禁用提交按钮
    if (submitButton) {
      const disableSubmit = (event: Event) => {
        event.preventDefault();
      };
      submitButton.addEventListener('click', disableSubmit);
    }
    
    if (termsLink && readmeTextarea) {
      readmeTextarea.style.display = 'none';
  
      const toggleReadme = () => {
        if (readmeTextarea.style.display === 'none') {
          readmeTextarea.style.display = 'block';
        } else {
          readmeTextarea.style.display = 'none';
        }
      };
  
      termsLink.addEventListener('click', toggleReadme);
  
      // 清理事件绑定
      return () => {
        termsLink.removeEventListener('click', toggleReadme);
      };
    }
  }, [htmlContent]); 

  // 图片上传校验
  const validateImage = (file: File, maxSize: number): boolean => {
    const validTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];

    if (!validTypes.includes(file.type)) {
      toast({
        title: 'Invalid File Type',
        description: 'Only PNG, JPG, JPEG, or GIF file types are supported.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return false;
    }

    if (file.size > maxSize) {
      toast({
        title: 'File Too Large',
        description: `Choose a picture that is no more than ${maxSize / 1024 }KB.`,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return false;
    }

    return true;
  };

  // 处理图片上传
  const handleImageUpload = (
    event: React.ChangeEvent<HTMLInputElement>,
    setImageBase64: React.Dispatch<React.SetStateAction<string | null>>,
    maxSize: number = 200 * 1024 // 200kB
  ) => {
    const file = event.target.files?.[0];
    if (file && validateImage(file, maxSize)) {
      const reader = new FileReader();
      reader.onload = () => {
        setImageBase64(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const debouncedToast = useCallback(
    debounce((errorMessage: string) => {
      toast({
        title: 'Validation Error',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }, 200),
    []
  );
  // 校验输入内容的长度
  const validateInputLength = (value: string, maxLength: number, errorMessage: string): boolean => {
    if (value.length > maxLength) {
      debouncedToast(errorMessage); 
      return false;
    }
    return true;
  };

  const handleWelcomeMessageChange = (value: string) => {
    if (validateInputLength(value, 31, 'This length should be no more than 31.')) {
        setWelcomeMessage(value);
    } 
  };
  const handleTermsOfServiceChange = (value: string) => {
    if (validateInputLength(value, 200, 'This length should be no more than 200.')) {
      setTermsOfService(value);
    }
  };
  const handleCorporateInfoChange = (value: string) => {
    if (validateInputLength(value, 50, 'This length should be no more than 50.')) {
        setCorporateInfo(value);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="6xl" isCentered>
      <ModalOverlay />
      <ModalContent border="none">
        <ModalHeader borderColor="#E7E7E7">
          <Box textAlign="left" w="100%">
            Customization
          </Box>
        </ModalHeader>
        <ModalCloseButton color="#A2ACB2" border="none"/>
        <ModalBody>
          <Flex>
            {/* 左侧配置项 */}
            <Box flex="1" pr={4}>
              <Box fontWeight="bold" marginBottom="20px" >Configuration</Box>
              <FormControl mb={4} width="100%" overflow="hidden">
                <FormLabel w="125px" float="left" h="40px" lineHeight="40px" fontSize="14px">Background</FormLabel>
                <Flex alignItems="center" mb={2}>
                  <Input
                    id="color-picker-text"
                    type="text"
                    value={backgroundColor}
                    isReadOnly
                    mr={5}
                    background= "#F4F5F7"
                    borderRadius="2px 2px 2px 2px"
                    border="1px solid #DADCE1"
                    placeholder="Selected Color"
                    w={260}
                    fontSize="14px"
                  />
                  <AmpconButton
                    w="120px"
                    onClick={() => {
                      const colorInput = document.getElementById('color-picker') as HTMLInputElement;
                      if (colorInput) {
                        colorInput.click();
                      }
                    }}
                  >
                    Color
                  </AmpconButton>
                  <Input
                    id="color-picker"
                    type="color"
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    zIndex={-1}
                    position={"absolute"}
                    opacity={0}
                  />
                </Flex>

                {/* <Flex alignItems="center" float="right">
                  <Input
                    id="background-upload-text"
                    type="text"
                    value={backgroundImageBase64 || ""}
                    isReadOnly
                    mr={4}
                    placeholder="PNG, JPG, JPEG and GIF, Must be less than 200kB"
                    background= "#F4F5F7"
                    borderRadius="2px 2px 2px 2px"
                    border="1px solid #DADCE1"
                    w="260px"
                    fontSize="14px"
                  />
                  <AmpconButton
                    as="label"
                    htmlFor="background-upload"
                    w="120px"
                  >
                    Upload Pic
                  </AmpconButton>
                  <Input
                    id="background-upload"
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleImageUpload(e, setBackgroundImageBase64, 200 * 1024)}
                    display="none"
                  />
                </Flex> */}
              </FormControl>
              <FormControl mb={4} width="100%" overflow="hidden">
                <FormLabel w="125px" float="left" h="40px" lineHeight="40px" fontSize="14px">Upload Logo</FormLabel>
                <Flex alignItems="center">
                  <Input
                    id="logo-upload-text"
                    type="text"
                    value={logoBase64 || ""}
                    isReadOnly
                    mr={5}
                    placeholder="PNG, JPG, JPEG and GIF, Must be less than 10kB"
                    title="PNG, JPG, JPEG and GIF, Must be less than 10kB"
                    background= "#F4F5F7"
                    borderRadius="2px 2px 2px 2px"
                    border="1px solid #DADCE1"
                    w={260}
                    fontSize="14px"
                  />
                  <AmpconButton
                    as="label"
                    htmlFor="logo-upload"
                    w="95px"
                  >
                    Upload Logo
                  </AmpconButton>
                  <Input
                    id="logo-upload"
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleImageUpload(e, setLogoBase64, 10 * 1024)}
                    display="none"
                  />
                </Flex>
                
              </FormControl>
              <FormControl mb={4} width="100%" overflow="hidden">
                <FormLabel w="125px" float="left" h="40px" lineHeight="40px" fontSize="14px">Welcome Message</FormLabel>
                <Flex alignItems="center"mr={135}>
                  <Input
                    id="welcome-message-text"
                    type="text"
                    w="260px"
                    fontSize="14px"
                    borderRadius="2px 2px 2px 2px"
                    _focus={{borderColor: "#14C9BB"}}
                    value={welcomeMessage}
                    onChange={(e) => handleWelcomeMessageChange(e.target.value)}
                  />
                </Flex>
              </FormControl>
              <FormControl mb={4} width="100%" overflow="hidden">
                <FormLabel w="125px" float="left" h="40px" lineHeight="40px" fontSize="14px">Terms of Service</FormLabel>
                <Flex alignItems="center" mr={135}>
                  <Textarea
                    id="terms-text"
                    value={termsOfService}
                    w="260px"
                    fontSize="14px"
                    borderRadius="2px 2px 2px 2px"
                    resize="none"
                    onChange={(e) => handleTermsOfServiceChange(e.target.value)}
                    rows={6}
                  />
                </Flex>
              </FormControl>
              <FormControl mb={4} width="100%" overflow="hidden">
                <FormLabel w="125px" float="left" h="40px" lineHeight="40px" fontSize="14px">Copyright</FormLabel>
                <Flex alignItems="center"  mr={135}>
                  <Textarea
                    id="corporate-text"
                    value={corporateInfo}
                    w="260px"
                    fontSize="14px"
                    borderRadius="2px 2px 2px 2px"
                    resize="none"
                    onChange={(e) => handleCorporateInfoChange(e.target.value)}
                    rows={5}
                  />
                </Flex>
              </FormControl>
            </Box>

            {/* 右侧实时预览 */}
            <Box flex="1" pl={4} borderLeft="1px solid #E7E7E7">
              <Box fontWeight="bold" marginBottom="20px">Preview</Box>
              <Box height="90%" overflow="auto">
                <div style={{ height: "100%" }} dangerouslySetInnerHTML={{ __html: htmlContent }} />
              </Box>
            </Box>
          </Flex>
        </ModalBody>
        <ModalFooter>
          <AmpconButton onClick={onClose} mr={3} w="100px">Cancel</AmpconButton>
          <Button
            color="#FFFFFF"
            backgroundColor="#14C9BB"
            borderColor="#14C9BB"
            borderRadius="2px"
            h="40px"
            w="100px"
            lineHeight="40px"
            fontSize="14px"
            _hover={{
              backgroundColor: "#14C9BB",
            }}
            onClick={async () => {
              try {
                const base64Html = await convertHtmlToBase64(htmlContent, authMode);
                onSave(base64Html);
              } catch (error) {
                toast({
                  title: 'Error',
                  description: 'Failed to save the HTML content.',
                  status: 'error',
                  duration: 5000,
                  isClosable: true,
                });
              }
            }}
          >
            Apply
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default Customization;