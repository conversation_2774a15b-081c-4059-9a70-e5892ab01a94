import React, { useMemo } from 'react';
import { Tab, useColorMode, useMultiStyleConfig, useTab } from '@chakra-ui/react';
import useFastField from '@/modules-smb/hooks/useFastField';

// eslint-disable-next-line react/prop-types
const InterfaceTab: React.FC<{ index: number }> = React.forwardRef(({ index, ...props }, ref) => {
  const { value } = useFastField({ name: `configuration[${index}]` });
  const { colorMode } = useColorMode();
  const isLight = colorMode === 'light';
  // @ts-ignore
  const tabProps = useTab({ ...props, ref });

  const styles = useMultiStyleConfig('Tabs', tabProps);

  const name = useMemo(() => {
    if (value?.name) {
      return value.name.length <= 15 ? value.name : `${value.name.substring(0, 12)}...`;
    }
    return '';
  }, [value]);

   const roleText = useMemo(() => {
    if (value?.role === 'upstream') {
      return 'Bridged Mode (Layer 2 bridging)';
    } else if (value?.role === 'downstream') {
      return 'Routing Mode (NAT)';
    }
    return value?.role;
  }, [value]);

  return (
    <Tab
      _selected={{
        // @ts-ignore
        ...styles.tab?._selected,
        borderBottomColor: isLight ? 'gray.100' : 'gray.800',
      }}
    >
      {name} ({roleText})
    </Tab>
  );
});

export default React.memo(InterfaceTab);
