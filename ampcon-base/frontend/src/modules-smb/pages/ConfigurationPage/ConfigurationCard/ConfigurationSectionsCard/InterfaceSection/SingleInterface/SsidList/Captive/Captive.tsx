import React, { useState, useEffect } from 'react';
import { Heading, Select, SimpleGrid, Text, Button } from '@chakra-ui/react';
import { object, string } from 'yup';
import { getIn, useFormikContext } from 'formik';
import CreatableSelectField from '@/modules-smb/components/FormFields/CreatableSelectField';
import NumberField from '@/modules-smb/components/FormFields/NumberField';
import ObjectArrayFieldModal from '@/modules-smb/components/FormFields/ObjectArrayFieldModal';
import SelectField from '@/modules-smb/components/FormFields/SelectField';
import StringField from '@/modules-smb/components/FormFields/StringField';
import CaptiveResourcePicker from './CaptiveResourcePicker';
import WebRootSelector from './CaptiveWebRoot';


const CREDENTIALS_SCHEMA = (t: (str: string) => string, useDefault = false) => {
  const shape = object().shape({
    username: string().required(t('form.required')).default(''),
    password: string().required(t('form.required')).default(''),
  });

  return useDefault ? shape : shape.nullable().default(undefined);
};

interface Props {
  isDisabled?: boolean;
  namePrefix: string;
  onAuthModeChange?: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  authMode?: string;
  parent?: {
    entity?: string;
    venue?: string;
    subscriber?: string;
  };
  kind?: string;
}

const CaptiveForm = ({ isDisabled, namePrefix, onAuthModeChange, authMode, parent, kind}: Props) => {
  const { values, setFieldValue } = useFormikContext<any>();
  const [templateValues, setTemplateValues] = useState<Record<string, any>>({});
  const isInitializedRef = React.useRef(false); // 用于标记是否已初始化
  
  const fieldProps = (suffix: string) => ({
    name: `${namePrefix}.${suffix}`,
    label: suffix,
    definitionKey: `interface.ssid.pass-point.${suffix}`,
    isDisabled,
  });

  const credFields = React.useMemo(
    () => (
      <SimpleGrid minChildWidth="300px" gap={4}>
        <StringField name="username" label="username" isRequired />
        <StringField name="password" label="password" isRequired />
      </SimpleGrid>
    ),
    [],
  );
  const credCols = React.useMemo(
    () => [
      {
        id: 'username',
        Header: 'username',
        Footer: '',
        accessor: 'username',
      },
      {
        id: 'password',
        Header: 'password',
        Footer: '',
        accessor: 'password',
      },
    ],
    [],
  );

  // 处理Captive模板变化
  const onTemplateChange = (template: Record<string, any>) => {
    // 先清空之前的再更新表单字段值
    Object.keys(templateValues).forEach((key) => {
      setFieldValue(`${namePrefix}.${key}`, undefined);
    });
    Object.keys(template).forEach((key) => {
      setFieldValue(`${namePrefix}.${key}`, template[key]);
    });
    setTemplateValues(template);
    // console.log(authMode)
    // if (onAuthModeChange && !template['auth-mode']) {
    //   // authMode = undefined;
    //   const event = { target: { value: authMode } } as React.ChangeEvent<HTMLSelectElement>;
    //   onAuthModeChange(event);
    //   console.log(event)
    // }
    // console.log(authMode)
  };

  const isTemplateDisabled = React.useMemo(() => {
    if (isDisabled) {
      return true;
    }
    // 如果有资源且已选择模板，则禁用
    const selectedValue = getIn(values, `${namePrefix}.captive-resource.__variableBlock[0]`);
    if (Object.keys(templateValues).length !== 0 && selectedValue !== undefined) {
      return true;
    }
    return false;
  }, [isDisabled, templateValues]);

  // 编辑初始化Captive模板
  useEffect(() => {
    if (!isInitializedRef.current) {
      const initialTemplate = getIn(values, `${namePrefix}`);
      if (initialTemplate) {
        onTemplateChange(initialTemplate);
      }
      isInitializedRef.current = true;
      // console.log('Initialized Captive template');
    }
  }, [namePrefix, values]);

  return (
    <>
      <Heading size="md" display="flex" alignItems="center" mt={2}>
        <Text pt={1}>Captive Portal</Text>

        <CaptiveResourcePicker
          name={`${namePrefix}.captive-resource`}
          isDisabled={isDisabled}
          onTemplateChange={onTemplateChange}
          parent={parent}
          kind={kind}
        />

        <Select w="max-content" ml={2} value={authMode} onChange={onAuthModeChange} isDisabled={isTemplateDisabled}>
          <option value="none">None</option>
          <option value="click-to-continue">Click</option>
          <option value="radius">Radius</option>
          <option value="credentials">Credentials</option>
          <option value="uam">UAM</option>
        </Select>
      </Heading>
      {authMode !== undefined && (
        <SimpleGrid minChildWidth="300px" spacing="20px" mb={8} mt={2} w="100%">
          <CreatableSelectField
            {...fieldProps('walled-garden-fqdn')}
            placeholder="Example: *.google.com"
            emptyIsUndefined={authMode !== 'uam'}
            isRequired={authMode === 'uam'}
            isDisabled={isTemplateDisabled}
          />
          <CreatableSelectField
            {...fieldProps('walled-garden-ipaddr')}
            placeholder="Example: ***********"
            emptyIsUndefined
            isDisabled={isTemplateDisabled}
          />
          {/* <FileInputFieldModal
            {...fieldProps('web-root')}
            fileName="configuration.captive.web-root-filename"
            definitionKey="service.captive.web-root"
            explanation={t('form.captive_web_root_explanation')}
            test={() => true}
            acceptedFileTypes=".tar"
            isDisabled={isDisabled}
            canDelete
            isRequired
            wantBase64
          /> */}
          <WebRootSelector
            namePrefix={`${namePrefix}`}
            authMode={authMode}
            isDisabled={isDisabled}
            parent={parent}
          />
          <NumberField
            {...fieldProps('idle-timeout')}
            isRequired
            w="100px"
            isDisabled={isTemplateDisabled}
          />
          <NumberField
            {...fieldProps('session-timeout')}
            emptyIsUndefined
            acceptEmptyValue
            w="100px"
            isDisabled={isTemplateDisabled}
          />
          {authMode === 'credentials' && (
            <ObjectArrayFieldModal
              {...fieldProps('credentials')}
              fields={credFields}
              columns={credCols}
              schema={CREDENTIALS_SCHEMA}
              isDisabled={isTemplateDisabled}
              isRequired
            />
          )}
          {authMode === 'uam' && (
            <>
              <StringField
                {...fieldProps('uam-server')}
                isRequired
                isDisabled={isTemplateDisabled}
              />
              <StringField
                {...fieldProps('uam-secret')}
                isRequired
                hideButton
                isDisabled={isTemplateDisabled}
              />
              <NumberField
                {...fieldProps('uam-port')}
                isRequired
                isDisabled={isTemplateDisabled}
              />
              <StringField
                {...fieldProps('nasid')}
                isRequired
                isDisabled={isTemplateDisabled}
              />
              <StringField
                {...fieldProps('nasmac')}
                emptyIsUndefined
                isDisabled={isTemplateDisabled}
              />
              <SelectField
                {...fieldProps('mac-format')}
                options={[
                  { value: 'aabbccddeeff', label: 'aabbccddeeff' },
                  { value: 'aa-bb-cc-dd-ee-ff', label: 'aa-bb-cc-dd-ee-ff' },
                  { value: 'aa:bb:cc:dd:ee:ff', label: 'aa:bb:cc:dd:ee:ff' },
                  { value: 'AABBCCDDEEFF', label: 'AABBCCDDEEFF' },
                  { value: 'AA:BB:CC:DD:EE:FF', label: 'AA:BB:CC:DD:EE:FF' },
                  { value: 'AA-BB-CC-DD-EE-FF', label: 'AA-BB-CC-DD-EE-FF' },
                ]}
                isRequired
                isDisabled={isTemplateDisabled}
              />
              <StringField
                {...fieldProps('ssid')}
                emptyIsUndefined
                isDisabled={isTemplateDisabled}
              />
            </>
          )}
          {(authMode === 'radius' || authMode === 'uam') && (
            <>
              <StringField
                {...fieldProps('auth-server')}
                isRequired
                isDisabled={isTemplateDisabled}
              />
              <StringField
                {...fieldProps('auth-secret')}
                isRequired
                hideButton
                isDisabled={isTemplateDisabled}
              />
              <NumberField
                {...fieldProps('auth-port')}
                isRequired
                isDisabled={isTemplateDisabled}
              />
              <StringField
                {...fieldProps('acct-server')}
                emptyIsUndefined
                isDisabled={isTemplateDisabled}
              />
              <StringField
                {...fieldProps('acct-secret')}
                emptyIsUndefined
                hideButton
                isDisabled={isTemplateDisabled}
              />
              <NumberField
                {...fieldProps('acct-port')}
                emptyIsUndefined
                acceptEmptyValue
                isDisabled={isTemplateDisabled}
              />
              <NumberField
                {...fieldProps('acct-interval')}
                emptyIsUndefined
                acceptEmptyValue
                isDisabled={isTemplateDisabled}
              />
            </>
          )}
        </SimpleGrid>
      )}
    </>
  );
};

export default React.memo(CaptiveForm);
