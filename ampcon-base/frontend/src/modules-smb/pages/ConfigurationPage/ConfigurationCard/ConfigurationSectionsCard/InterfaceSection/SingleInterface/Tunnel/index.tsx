import React, { useCallback } from 'react';
import TunnelForm from './Tunnel';
import useFastField from '@/modules-smb/hooks/useFastField';

interface Props {
  editing: boolean;
  index: number;
  parent?: {
    entity?: string;
    venue?: string;
    subscriber?: string;
  };
}

const Tunnel = ({ editing, index, parent }: Props) => {
  const { value, onChange } = useFastField({ name: `configuration[${index}].tunnel` });
  const { value: protoValue } = useFastField({ name: `configuration[${index}].tunnel.proto` });

  const onToggle = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (!e.target.checked) {
        onChange(undefined);
      } else {
        onChange({ proto: 'mesh' });
      }
    },
    [onChange],
  );

  const onProtoChange = useCallback(
    (e: React.ChangeEvent<HTMLSelectElement>) => {
      if (e.target.value === 'mesh') onChange({ proto: 'mesh' });
      else if (e.target.value === 'vxlan') {
        onChange({
          proto: 'vxlan',
          'peer-address': '***********',
          'peer-port': 4700,
        });
      } else if (e.target.value === 'l2tp') {
        onChange({
          proto: 'l2tp',
          server: '***********',
          password: 'YOUR_PASSWORD',
        });
      } else {
        onChange({
          proto: 'gre',
          'peer-address': '***********',
          'dhcp-healthcheck': true,
        });
      }
    },
    [onChange],
  );

  return (
    <TunnelForm
      isDisabled={!editing}
      namePrefix={`configuration[${index}].tunnel`}
      value={value}
      onToggle={onToggle}
      onProtoChange={onProtoChange}
      protoValue={protoValue}
      variableBlockId={value?.__variableBlock?.[0] as string | undefined}
      // variableBlockId={value?.["resource-tunnel"]?.__variableBlock?.[0] as string | undefined}
      parent={parent}
    />
  );
};
export default React.memo(Tunnel);
