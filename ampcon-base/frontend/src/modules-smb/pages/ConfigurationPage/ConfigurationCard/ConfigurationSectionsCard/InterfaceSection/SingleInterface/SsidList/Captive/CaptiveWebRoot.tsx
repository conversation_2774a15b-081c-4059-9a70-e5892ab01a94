import React, { useState, useEffect } from 'react';
import { FormControl, FormLabel, Select,Text } from '@chakra-ui/react';
import { useFormikContext, getIn } from 'formik';
import Customization from '@/modules-smb/pages/ConfigurationPage/ConfigurationCard/ConfigurationSectionsCard/InterfaceSection/SingleInterface/SsidList/Captive/Customization';
import { useGetResourceTemplates } from '@/modules-smb/hooks/Network/Resources'
// 新增认证模式名称映射函数
const getAuthMethodDisplayName = (modeValue: string): string => {
  const modeMapping: Record<string, string> = {
    'click-to-continue': 'Click',
    'credentials': 'Credentials',
    'radius': 'Radius'
  };
    return modeMapping[modeValue]||modeValue;
};

interface WebRootSelectorProps {
  namePrefix: string;
  authMode?: string;
  isDisabled?: boolean;
  parent?: {
    entity?: string;
    venue?: string;
    subscriber?: string;
  };
}

const WebRootSelector: React.FC<WebRootSelectorProps> = ({
  namePrefix,
  authMode,
  isDisabled = false,
  parent,
}) => {
  if (authMode === 'uam') {
    return null;
  }
  
  const { values, setFieldValue,errors} = useFormikContext<any>();
  const [webRootSelectValue, setWebRootSelectValue] = useState<'default' | 'customization' | ''>('');
  const [isCustomizationOpen, setIsCustomizationOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [webRootError, setWebRootError] = useState<string | null>(null);
  const [templates, setTemplates] = useState<any[]>([]);
  const isInitializedRef = React.useRef(false); // 用于标记是否已初始化
  const closeCustomization = () => setIsCustomizationOpen(false);

  const venueID = parent?.venue || ''

  // web-root模板方法
  const shouldFetchTemplates = React.useMemo(() => {
    return namePrefix !== 'editing.webroot' && !!venueID;
  }, [namePrefix, venueID]);
  const { data: fetchedTemplates = [] } = useGetResourceTemplates(venueID, 'captive-webroot', {
    enabled: shouldFetchTemplates,
  });
  // console.log('shouldFetchTemplates:', shouldFetchTemplates, 'namePrefix:', namePrefix, 'venueID:', venueID, 'authMode:', authMode)
  const setFilterTemplates = () => {
    const filteredTemplates = fetchedTemplates.filter((template: any) => {
        return authMode === template.description;
    });
    setTemplates(filteredTemplates);
  }
  const onTemplateChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedTemplateValue = e.target.value;
    setSelectedTemplate(selectedTemplateValue);

    if (selectedTemplateValue) {
      // 如果选择了模板，则禁用web-root框并设置为customization
      const selectedObj = templates.find((tpl: any) => String(tpl.id) === String(selectedTemplateValue));
      const variableValue = selectedObj?.variables?.[0]?.value;
      // 模板为default时特殊处理
      if (!variableValue) {
        setFieldValue(`${namePrefix}.web-root`, `default_remove__variableBlock__${selectedTemplateValue}`);
      } else {
        setFieldValue(`${namePrefix}.web-root`, `__variableBlock__${selectedTemplateValue}`);
      }
      setWebRootSelectValue('customization');
    } else {
      // 如果未选择模板，则恢复默认状态
      setWebRootSelectValue('default');
      setFieldValue(`${namePrefix}.web-root`, undefined);
    }
  };
  
  // 判断当前是否是captive模板表单
  const isCaptiveTemplateForm = React.useMemo(() => {
    if (namePrefix == 'editing.captive' && getIn(values, 'editing.auto-mode')) {
      return true;
    } else {
      return false;
    }
  }, [namePrefix, values]);

  const onWebrootModeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = e.target.value as 'default' | 'customization';
    setWebRootSelectValue(selectedValue);

    if (selectedValue === 'default') {
      setFieldValue(`${namePrefix}.web-root`, undefined);
    } else if (selectedValue === 'customization') {
      // 打开 Customization 弹窗
      setIsCustomizationOpen(true);
    }
  };
  // 查找与当前 authMode 相关的错误信息
  useEffect(() => {
    setWebRootError(null); // 重置错误信息
    if (errors.ssids && Array.isArray(errors.ssids)) {
      errors.ssids.forEach((ssidError: any) => {
        if (
          ssidError.captivePortal && 
          ssidError.captivePortal['web-root'] &&
          ssidError.captivePortal['auth-mode'] === getAuthMethodDisplayName(authMode!)
        ) {
          setWebRootError(ssidError.captivePortal['web-root']);
        }
      });
    }
  }, [errors, authMode]);

  // 动态获取当前条件web-root模板数据
  useEffect(() => {
    if (fetchedTemplates.length > 0) {
      setFilterTemplates();
    }
  }, [namePrefix, fetchedTemplates, authMode]);

  // 编辑初始化web-root及是否自定义判断
  useEffect(() => {
    // captive模板表单去掉web-root
    if (isCaptiveTemplateForm) {
      return;
    } 
    if (
        !isInitializedRef.current &&
        getIn(values, `${namePrefix}`)
      ) {
      //   console.log('Initializing template values...');
      const template = getIn(values, `${namePrefix}`);

      // 检查是否有web-root模板数据
      if (template['web-root'] && template['web-root'].includes('__variableBlock__')) {
        const blockId = template['web-root'].split('__variableBlock__')[1] || '';
        setSelectedTemplate(blockId);
        setWebRootSelectValue('customization');
        setFieldValue(`${namePrefix}.web-root`, template['web-root']);
      // 自定义web-root
      } else if (template['web-root']) {
        setWebRootSelectValue('customization');
        setFieldValue(`${namePrefix}.web-root`, template['web-root']);
      }
      isInitializedRef.current = true; // 标记为已初始化
    }
  }, [values, namePrefix]);

  // authMode切换初始化web-root
  useEffect(() => {
    // captive模板表单去掉web-root
    if (isCaptiveTemplateForm) {
    return;
    }
    if (authMode !== 'none' && authMode !== undefined && !getIn(values, `${namePrefix}.web-root`)) {
      // console.log('Setting default web-root content...');
      setFieldValue(`${namePrefix}.web-root`, undefined);
      setWebRootSelectValue('default');
      setSelectedTemplate(null);
    }
  }, [authMode]);

  return (
    <>
      {isCaptiveTemplateForm === false && (
        <FormControl isRequired>
            <FormLabel>web-root</FormLabel>
            {shouldFetchTemplates && (
                <Select w="45%" mr={4} float="left"
                  placeholder="Select a template" 
                  value={selectedTemplate || ''} 
                  onChange={onTemplateChange}
                  isDisabled={isDisabled}
                >
                {templates.map((template: any) => (
                    <option key={template.id} value={template.id}>
                    {template.name}
                    </option>
                ))}
                </Select>
            )}
            <Select isDisabled={isDisabled || !!selectedTemplate}  w="50%" float="left" value={webRootSelectValue} onChange={onWebrootModeChange}>
              <option value="default">Default</option>
              <option value="customization">Customization</option>
            </Select>
            {webRootError && (
              <Text color="red.500" fontSize="sm"  width="50%" marginLeft="0" wordBreak="break-word" marginTop="2">
                {webRootError}
              </Text>
            )}
        </FormControl>
        )}
      {isCustomizationOpen && (
        <Customization
          isOpen={isCustomizationOpen}
          authMode={authMode || ''}
          onClose={closeCustomization}
          onSave={(updatedHtmlBase64: string) => {
            setFieldValue(`${namePrefix}.web-root`, updatedHtmlBase64);
            closeCustomization();
          }}
        />
      )}
    </>
  );
};

export default WebRootSelector;