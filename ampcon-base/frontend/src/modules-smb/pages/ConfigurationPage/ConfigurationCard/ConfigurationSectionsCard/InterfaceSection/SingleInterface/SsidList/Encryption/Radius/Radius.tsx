import React from 'react';
import { Flex, FormControl, FormLabel, Heading, SimpleGrid, Switch } from '@chakra-ui/react';
import { INTERFACE_SSID_RADIUS_SCHEMA } from '../../../../interfacesConstants';
import Local from '../../Local';
import LockedRadius from './LockedRadius';
import ConfigurationResourcePicker from '@/modules-smb/components/CustomFields/ConfigurationResourcePicker';
import NumberField from '@/modules-smb/components/FormFields/NumberField';
import StringField from '@/modules-smb/components/FormFields/StringField';
import ToggleField from '@/modules-smb/components/FormFields/ToggleField';
import { useFormikContext, getIn } from "formik";

type Props = {
  editing: boolean;
  namePrefix: string;
  isUsingCustom: boolean;
  onAccountingChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  isAccountingEnabled: boolean;
  onDynamicChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  isDynamicEnabled: boolean;
  variableBlock?: string;
  // eslint-disable-next-line react/no-unused-prop-types
  isPasspoint?: boolean;
  isEnabled: boolean;
  onEnableToggle: () => void;
  isNotRequired: boolean;
  parent?: {
    entity?: string;
    venue?: string;
    subscriber?: string;
  };
};
const RadiusForm: React.FC<Props> = ({
  editing,
  namePrefix,
  isUsingCustom,
  onAccountingChange,
  isAccountingEnabled,
  onDynamicChange,
  isDynamicEnabled,
  variableBlock,
  isEnabled,
  onEnableToggle,
  isNotRequired,
  parent,
}) => {
  const { values } = useFormikContext();
  // use ${namePrefix}.local to determine whether local is enabled
  const local = getIn(values, `${namePrefix}.local`); 
  const isLocalEnabled = local !== undefined;
  const isFieldDisabled = isLocalEnabled || !editing;
  return (
    <>
      <Flex mt={6}>
        <div>
          <Heading size="md" display="flex" mt={2} mr={2} borderBottom="1px solid">
            Radius
          </Heading>
        </div>
        {isNotRequired && (
          <Switch isChecked={isEnabled} onChange={onEnableToggle} size="lg" mt={2} mr={2} />
        )}
        {isEnabled && (
          <ConfigurationResourcePicker
            name={namePrefix}
            prefix="interface.ssid.radius"
            isDisabled={!editing}
            defaultValue={INTERFACE_SSID_RADIUS_SCHEMA}
            parent={parent}
          />
        )}
      </Flex>

      {isEnabled &&
        (isUsingCustom || !variableBlock ? (
          <>
            <SimpleGrid minChildWidth="300px" spacing="20px">
              <StringField
                name={`${namePrefix}.authentication.host`}
                label="authentication.host"
                isDisabled={isFieldDisabled}
                isRequired
              />
              <NumberField
                name={`${namePrefix}.authentication.port`}
                label="authentication.port"
                isDisabled={isFieldDisabled}
                isRequired
                hideArrows
                w={24}
              />
              <StringField
                name={`${namePrefix}.authentication.secret`}
                label="authentication.secret"
                isDisabled={isFieldDisabled}
                isRequired
                hideButton
              />
              <ToggleField
                name={`${namePrefix}.authentication.mac-filter`}
                label="authentication.mac-filter"
                isDisabled={isFieldDisabled}
                falseIsUndefined
              />
            </SimpleGrid>

            <FormControl>
              <FormLabel ms="4px" fontSize="md" fontWeight="normal">
                Enable Accounting
              </FormLabel>
              <Switch
                onChange={onAccountingChange}
                isChecked={isAccountingEnabled}
                borderRadius="15px"
                size="lg"
                isDisabled={isFieldDisabled}
                _disabled={{ opacity: 0.8, cursor: "not-allowed" }}
              />
            </FormControl>

            {isAccountingEnabled && (
              <SimpleGrid minChildWidth="300px" spacing="20px">
                <StringField
                  name={`${namePrefix}.accounting.host`}
                  label="accounting.host"
                  isDisabled={isFieldDisabled}
                  isRequired
                />
                <NumberField
                  name={`${namePrefix}.accounting.port`}
                  label="accounting.port"
                  isDisabled={isFieldDisabled}
                  isRequired
                  hideArrows
                  w={24}
                />
                <StringField
                  name={`${namePrefix}.accounting.secret`}
                  label="accounting.secret"
                  isDisabled={isFieldDisabled}
                  isRequired
                  hideButton
                />
              </SimpleGrid>
            )}

            <FormControl >
              <FormLabel ms="4px" fontSize="md" fontWeight="normal">
                Enable Dynamic Authorization
              </FormLabel>
              <Switch
                onChange={onDynamicChange}
                isChecked={isDynamicEnabled}
                borderRadius="15px"
                size="lg"
                isDisabled={isFieldDisabled}
                _disabled={{ opacity: 0.8, cursor: "not-allowed" }}
              />
            </FormControl>

            {isDynamicEnabled && (
              <SimpleGrid minChildWidth="300px" spacing="20px" mb={4}>
                <StringField
                  name={`${namePrefix}.dynamic-authorization.host`}
                  label="dynamic-authorization.host"
                  isDisabled={isFieldDisabled}
                  isRequired
                />
                <NumberField
                  name={`${namePrefix}.dynamic-authorization.port`}
                  label="dynamic-authorization.port"
                  isDisabled={isFieldDisabled}
                  isRequired
                />
                <StringField
                  name={`${namePrefix}.dynamic-authorization.secret`}
                  label="dynamic-authorization.secret"
                  isDisabled={isFieldDisabled}
                  isRequired
                  hideButton
                />
              </SimpleGrid>
            )}

            <SimpleGrid minChildWidth="300px" spacing="20px">
              <StringField
                name={`${namePrefix}.nas-identifier`}
                label="nas-identifier"
                isDisabled={isFieldDisabled}
                emptyIsUndefined
              />
              <ToggleField
                name={`${namePrefix}.chargeable-user-id`}
                label="chargeable-user-id"
                isDisabled={isFieldDisabled}
                falseIsUndefined
              />
            </SimpleGrid>

            <Local editing={editing} namePrefix={`${namePrefix}.local`} />
          </>
        ) : (
          <LockedRadius variableBlockId={variableBlock} />
        ))}
    </>
  );
};

export default React.memo(RadiusForm);
