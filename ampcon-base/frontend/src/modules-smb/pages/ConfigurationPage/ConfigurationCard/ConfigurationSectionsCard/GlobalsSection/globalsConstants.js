import {object, number, string} from "yup";
import {testIpv4, testIpv6} from "@/modules-smb/constants/formTests";

export const DEFAULT_GLOBALS = {
    name: "Globals",
    description: "",
    weight: 0,
    configuration: {
        "ipv4-network": "",
        // "ipv6-network": ""
    }
};

export const GLOBALS_SCHEMA = t =>
    object().shape({
        name: string().required(t("form.required")).default("Globals"),
        description: string().default(""),
        weight: number().required(t("form.required")).moreThan(0).integer().default(1),
        configuration: object().shape({
            "ipv4-network": string()
                .required(t("form.required"))
                .test("test-ipv4-network", t("form.invalid_ipv4"), testIpv4)
                .default(""),
            "ipv6-network": string()
                // .required(t("form.required"))
                .test("test-ipv6-network", t("form.invalid_ipv6"), testIpv6)
                // .default("")
        })
    });
