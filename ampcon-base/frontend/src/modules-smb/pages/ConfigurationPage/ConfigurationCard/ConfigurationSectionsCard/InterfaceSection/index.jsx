import React, { useCallback, useState, useEffect } from 'react';
import { VStack } from '@chakra-ui/react';
import { FieldArray, Formik } from 'formik';
import PropTypes from 'prop-types';
import isEqual from 'react-fast-compare';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import InternalFormAccess from '../common/InternalFormAccess';
import SectionGeneralCard from '../common/SectionGeneralCard';
import Interfaces from './Interfaces';
import { INTERFACES_SCHEMA } from './interfacesConstants';
import DeleteButton from '@/modules-smb/components/Buttons/DeleteButton';
import { ConfigurationSectionShape } from '@/modules-smb/constants/propShapes';
import _ from 'lodash';

// 新增认证模式名称映射函数
const getAuthMethodDisplayName = (modeValue) => {
  const modeMapping = {
    'click-to-continue' : 'Click',
    radius: 'Radius',
    credentials: 'Credentials'
  };
  return modeMapping[modeValue] || modeValue;
};
const propTypes = {
  editing: PropTypes.bool.isRequired,
  setSection: PropTypes.func.isRequired,
  sectionInformation: ConfigurationSectionShape.isRequired,
  removeSub: PropTypes.func.isRequired,
  parent: PropTypes.shape({
    entity: PropTypes.string,
    venue: PropTypes.string,
    subscriber: PropTypes.string,
  })
};

const warningTests = (values) => {
  const warnings = [];

  // Test for missing WAN port
  let foundWan = false;
  for (const config of values.configuration) {
    if (config.ethernet?.length > 0) {
      for (let i = 0; i < config.ethernet[0]['select-ports'].length; i += 1) {
        if (config.ethernet[0]['select-ports'][i] === 'WAN*' || config.ethernet[0]['select-ports'][i] === '*') {
          foundWan = true;
          break;
        }
      }
    }
  }
  if (!foundWan)
    warnings.push({
      warning: 'Missing interface with WAN select-port',
      explanation: "There needs to be at least one interface with a WAN port within it's select-ports",
    });

  return warnings;
};

const InterfaceSection = ({ editing, setSection, sectionInformation, removeSub, parent,  onWebRootError=() => {} }) => {
  const { t } = useTranslation();
  const [formKey, setFormKey] = useState(uuid());
  const sectionRef = useCallback(
    (node) => {
      if (node !== null) {
        const invalidValues = [];
        for (const [k, error] of Object.entries(node.errors)) {
          //排除ssids的web-root不同的错误
          if (k.includes('ssids')) {
            continue;
          }
          invalidValues.push({ key: `network.${k}`, error });
        }
        const warnings = warningTests(node.values);
        const newSection = {
          data: node.values,
          isDirty: node.dirty,
          invalidValues,
          warnings,
        };
        
        if (!isEqual(sectionInformation, newSection)) {
          setSection(newSection);
        }
      }
    },
    [sectionInformation],
  );

  const removeUnit = () => removeSub('interfaces');

  const validate= (values) => {
    const errors = {};
    const portalGroups = {
      'click-to-continue': [],
      'radius': [],
      'credentials': []
  };

    // 遍历所有 interface 的 SSID
    values.configuration?.forEach((interfaceConf, interfaceIndex) => {
      interfaceConf.ssids?.forEach((ssid, ssidIndex) => {
        const captive = ssid?.captive;
        if (!captive) return;
        const authMode = captive['auth-mode'];
        // console.log('authMode', authMode);
        const webRoot = captive['web-root'];
        const ssidName = ssid.name || `SSID ${interfaceIndex + 1}-${ssidIndex + 1}`;
        if (authMode&& portalGroups[authMode]) {
          portalGroups[authMode].push({
            ssidName,
            webRoot,
            interfaceIndex,
            ssidIndex,
          
          });
        }
      });
    });
    // 检查每个认证模式分组
    Object.entries(portalGroups).forEach(([mode, entries]) => {
      if (entries.length > 1) {
        const firstWebRoot = entries[0].webRoot;
        const conflictEntries = entries.filter(e => {
          return (
            (e.webRoot || '').replace(/^(need_append_two_html|need_remove|default_remove)/, '') !==
            (firstWebRoot || '').replace(/^(need_append_two_html|need_remove|default_remove)/, '')
          );
        });
        // console.log('conflictEntries',conflictEntries)
        if (conflictEntries.length > 0 ) {
          const allSSIDNames = entries.map(e => e.ssidName);
          const uniqueNames = [...new Set(allSSIDNames)];
          const authMethodName = getAuthMethodDisplayName(mode);
          const errorMessage = 
            `[${uniqueNames.join('] & [')}] have selected the same Captive Portal method ` +
            `[${authMethodName}] but different Webroot templates. ` +
            `Only the same Webroot template can be used.`;
            const entry = entries[0];
            const errorPath = `ssids[${entry.ssidIndex}].captivePortal.web-root`;
            _.set(errors, errorPath, errorMessage);
            _.set(errors, `ssids[${entry.ssidIndex}].captivePortal.auth-mode`, authMethodName);
         
        }
        // console.log('errors',errors)
      }
    });
    // 过滤掉 null 值
const filteredErrors = {};
Object.entries(errors).forEach(([key, value]) => {
if (Array.isArray(value)) {
  filteredErrors[key] = value.filter(item => item !== null && item !== undefined);
} else if (value !== null && value !== undefined) {
  filteredErrors[key] = value;
}
})


  // console.log('filteredErrors----------webeeee',filteredErrors);
      onWebRootError(filteredErrors);


    return filteredErrors;
  };

  useEffect(() => {
    if (!editing) {
      setFormKey(uuid());
    }
  }, [editing]);

  return (
    <Formik
      key={formKey}
      innerRef={sectionRef}
      initialValues={sectionInformation.data}
      validationSchema={INTERFACES_SCHEMA(t)}
      validate={validate}
    >
      {({ values }) => (
        <>
          <InternalFormAccess shouldValidate={sectionInformation?.shouldValidate} />
          <VStack spacing={4}>
            <SectionGeneralCard
              editing={editing}
              buttons={<DeleteButton ml={2} onClick={removeUnit} isDisabled={!editing} />}
            />
            <FieldArray name="configuration">
              {(arrayHelpers) => (
                <Interfaces
                  editing={editing}
                  arrayHelpers={arrayHelpers}
                  interfacesLength={values.configuration.length}
                  parent={parent}
                />
              )}
            </FieldArray>
          </VStack>
        </>
      )}
    </Formik>
  );
};

InterfaceSection.propTypes = propTypes;
export default React.memo(InterfaceSection, isEqual);
