import React from 'react';
import { getIn, useFormikContext } from 'formik';
import CaptiveForm from './Captive';
import useFastField from '@/modules-smb/hooks/useFastField';

type Props = {
  editing: boolean;
  namePrefix: string;
  parent?: {
    entity?: string;
    venue?: string;
    subscriber?: string;
  };
  kind?: string;
};

const Captive = ({ editing, namePrefix, parent, kind }: Props) => {
  
  const { value, onChange } = useFastField({ name: namePrefix });
  const { values, setFieldValue } = useFormikContext<any>();
  const parentName = namePrefix.replace('.captive', '');
  const parentValue = getIn(values, parentName);

  // 根据条件在 services 中添加或移除 'captive'
  function updateServices(services: string[] = [], add: boolean) {
    const set = new Set(services);
    if (add) {
      set.add('captive');
    } else {
      set.delete('captive');
    }
    return Array.from(set);
  }

  const handleAuthModeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const addCaptive = e.target.value !== 'none';
    const newParentValue = { ...parentValue, services: updateServices(parentValue?.services, addCaptive) };
    setFieldValue(parentName, newParentValue);

    if (e.target.value === 'radius') {
      onChange({
        'idle-timeout': 600,
        'auth-mode': e.target.value,
        'auth-server': '************',
        'auth-secret': 'secret',
        'auth-port': 1812,
      });
    } else if (e.target.value === 'uam') {
      onChange({
        'walled-garden-fqdn': [],
        'idle-timeout': 600,
        'auth-mode': e.target.value,
        'auth-server': '************',
        'auth-secret': 'secret',
        'auth-port': 1812,
        'uam-port': 3990,
        'uam-secret': 'secret',
        'uam-server': 'https://YOUR-LOGIN-ADDRESS.YOURS',
        nasid: 'TestLab',
      });
    } else if (e.target.value === 'none') {
      onChange(undefined);
    } else {
      onChange({ 'idle-timeout': 600, 'auth-mode': e.target.value });
    }
  };

  const mode = value?.['auth-mode'] as string | undefined;

  return (
    <CaptiveForm
      isDisabled={!editing}
      namePrefix={namePrefix}
      authMode={mode}
      onAuthModeChange={handleAuthModeChange}
      parent={parent}
      kind={kind}
    />
  );
};

export default React.memo(Captive);
