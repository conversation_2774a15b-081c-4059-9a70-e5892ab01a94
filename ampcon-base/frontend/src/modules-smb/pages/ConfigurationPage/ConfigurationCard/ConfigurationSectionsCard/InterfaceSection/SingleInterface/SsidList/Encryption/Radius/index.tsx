import React, { useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { INTERFACE_SSID_RADIUS_SCHEMA } from '../../../../interfacesConstants';
import RadiusForm from './Radius';
import useFastField from '@/modules-smb/hooks/useFastField';

type Props = { 
  editing: boolean; 
  namePrefix: string; 
  isPasspoint?: boolean; 
  isNotRequired: boolean;
  parent?: {
    entity?: string;
    venue?: string;
    subscriber?: string;
  };
};

const Radius = ({ editing, namePrefix, isPasspoint, isNotRequired, parent }: Props) => {
  const { t } = useTranslation();
  const { value: customRadius } = useFastField({ name: `${namePrefix}.__variableBlock` });
  const { value: radius, onChange: onRadiusChange } = useFastField({ name: `${namePrefix}` });
  const { value: accounting, onChange: setAccounting } = useFastField({ name: `${namePrefix}.accounting` });
  const { value: dynamicAuth, onChange: setDynamicAuth } = useFastField({
    name: `${namePrefix}.dynamic-authorization`,
  });

  const isUsingCustomRadius = useMemo(() => customRadius === undefined, [customRadius]);

  useEffect(() => {
    if (!radius) return;
    // 只在local存在，且其他字段也存在时，才清理
    const keys = Object.keys(radius);
    const hasLocal = 'local' in radius;
    const onlyLocal = keys.length === 1 && hasLocal;

    if (hasLocal && !onlyLocal) {
      // console.log('原始 radius 数据:', radius);
      const cleanedRadius = {
        local: radius.local,
      };
      // 只在数据不一致时，触发更新
      const cleanedJson = JSON.stringify(cleanedRadius);
      const currentJson = JSON.stringify(radius);

      if (cleanedJson !== currentJson) {
        // console.log('清理后的 radius 数据:', cleanedRadius);
        onRadiusChange(cleanedRadius);
      }
    }
  }, [radius]);

  const onEnabledAccountingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setAccounting({
        host: '***************',
        port: 1813,
        secret: 'YOUR_SECRET',
      });
    } else {
      setAccounting(undefined);
    }
  };

  const isAccountingEnabled = useMemo(() => accounting !== undefined, [accounting !== undefined]);

  const isEnabled = React.useMemo(() => radius !== undefined, [radius !== undefined]);
  const onEnableToggle = React.useCallback(() => {
    if (isEnabled) onRadiusChange(undefined);
    else onRadiusChange(INTERFACE_SSID_RADIUS_SCHEMA(t, true).cast());
  }, [isEnabled]);
  const onEnableDynamicChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setDynamicAuth({
        host: '***************',
        port: 1813,
        secret: 'YOUR_SECRET',
      });
    } else {
      setDynamicAuth(undefined);
    }
  };

  const isDynamicEnabled = useMemo(() => dynamicAuth !== undefined, [dynamicAuth !== undefined]);

  return (
    <RadiusForm
      editing={editing}
      isUsingCustom={isUsingCustomRadius}
      onAccountingChange={onEnabledAccountingChange}
      isAccountingEnabled={isAccountingEnabled}
      onDynamicChange={onEnableDynamicChange}
      isDynamicEnabled={isDynamicEnabled}
      variableBlock={customRadius}
      namePrefix={namePrefix}
      isEnabled={isEnabled}
      onEnableToggle={onEnableToggle}
      isPasspoint={isPasspoint}
      isNotRequired={isNotRequired}
      parent={parent}
    />
  );
};

export default React.memo(Radius);
