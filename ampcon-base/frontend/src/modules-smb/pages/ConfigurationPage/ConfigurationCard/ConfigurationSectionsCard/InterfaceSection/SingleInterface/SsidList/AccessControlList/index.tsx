import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { INTERFACE_SSID_ACCESS_CONTROL_LIST_SCHEMA } from '../../../interfacesConstants';
import AccessControlListForm from './AccessControlListForm';
import useFastField from '@/modules-smb/hooks/useFastField';

const AccessControlList: React.FC<{ editing: boolean; namePrefix: string }> = ({ editing, namePrefix }) => {
  const { t } = useTranslation();
  const { value, onChange } = useFastField({ name: namePrefix });

  const { isEnabled } = useMemo(
    () => ({
      isEnabled: value !== undefined,
    }),
    [value],
  );

  const onToggle = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (!e.target.checked) {
        onChange(undefined);
      } else {
        onChange(INTERFACE_SSID_ACCESS_CONTROL_LIST_SCHEMA(t, true).cast());
      }
    },
    [onChange],
  );

  return <AccessControlListForm editing={editing} namePrefix={namePrefix} isEnabled={isEnabled} onToggle={onToggle} />;
};

export default React.memo(AccessControlList);
