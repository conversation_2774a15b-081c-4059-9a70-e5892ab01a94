import React, { useCallback, useMemo } from 'react';
import * as Yup from 'yup';
import VlanForm from './Vlan';
import useFastField from '@/modules-smb/hooks/useFastField';

interface Props {
  editing: boolean;
  index: number
  parent?: {
    entity?: string;
    venue?: string;
    subscriber?: string;
  };
}

const Vlan = ({ editing, index, parent }: Props) => {
  const { value, onChange } = useFastField({ name: `configuration[${index}].vlan` });

  const { isActive, isUsingCustom, variableBlock } = useMemo(
    () => ({
      isActive: value !== undefined,
      isUsingCustom: value !== undefined && value.__variableBlock === undefined,
      variableBlock: value?.__variableBlock,
    }),
    [value],
  );

  const onToggle = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (!e.target.checked) {
        onChange(undefined);
      } else {
        onChange({ id: 1080 });
      }
    },
    [onChange],
  );

  const basicSchema = useCallback(
    (t: (s: string) => string) =>
      Yup.object()
        .shape({
          id: Yup.number().required(t('form.required')).moreThan(0).lessThan(4095).default(1080),
        })
        .default({
          id: 1080,
        }),
    [],
  );

  return (
    <VlanForm
      editing={editing}
      index={index}
      isActive={isActive}
      onToggle={onToggle}
      isUsingCustom={isUsingCustom}
      variableBlock={variableBlock}
      basicSchema={basicSchema}
      parent={parent}
    />
  );
};

export default React.memo(Vlan);
