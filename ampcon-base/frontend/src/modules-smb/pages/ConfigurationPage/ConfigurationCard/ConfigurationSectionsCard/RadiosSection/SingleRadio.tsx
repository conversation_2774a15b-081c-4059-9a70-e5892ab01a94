import React, { useState, useEffect } from'react';
import { Flex, Heading, SimpleGrid, Spacer } from '@chakra-ui/react';
import { useTranslation } from'react-i18next';
import AdvancedSettings from './AdvancedSettings';
import ChannelPicker from './ChannelPicker';
import LockedRadio from './LockedRadio';
import { SINGLE_RADIO_SCHEMA } from './radiosConstants';
import Rates from './Rates';
import DeleteButton from '@/modules-smb/components/Buttons/DeleteButton';
import ConfigurationResourcePicker from '@/modules-smb/components/CustomFields/ConfigurationResourcePicker';
import NumberField from '@/modules-smb/components/FormFields/NumberField';
import SelectField from '@/modules-smb/components/FormFields/SelectField';
import ToggleField from '@/modules-smb/components/FormFields/ToggleField';
import COUNTRY_LIST from '@/modules-smb/constants/countryList';
import useFastField from '@/modules-smb/hooks/useFastField';

type Props = {
  namePrefix: string;
  remove?: () => void;
  isDisabled?: boolean;
  canEditBand?: boolean;
  parent?: {
    entity?: string;
    venue?: string;
    subscriber?: string;
  };
};

const SingleRadio = ({ isDisabled, namePrefix, remove, canEditBand, parent }: Props) => {
  const { t } = useTranslation();
  const { value, setFieldValue } = useFastField({ name: namePrefix });
  const [txPowerOption, setTxPowerOption] = useState('default');
  // channel width动态选项
  const channelWidthList = [
    { value: 20, label: '20 MHz' },
    { value: 40, label: '40 MHz' },
    { value: 80, label: '80 MHz' },
    { value: 160, label: '160 MHz' },
  ]
  const [channelWidthOptions, setChannelWidthOptions] = useState(channelWidthList);

  // 根据tx-power值确定是否默认展示default
  useEffect(() => {
    if (value && value[`tx-power`] >= 0) {
      setTxPowerOption('set power');
    } else {
      setTxPowerOption('default');
    }
  }, [value, namePrefix]);

  // 根据band的值动态设置channel-width的选项
useEffect(() => {
    if (!value?.band) {
      // 如果value.band未定义，直接设置为默认选项，避免不必要的计算
      setChannelWidthOptions(channelWidthList);
      return;
    }
    const bandOptionsMap = {
      '2G': [
        { value: 20, label: '20 MHz' },
        { value: 40, label: '40 MHz' },
      ],
      '6G': [
        { value: 20, label: '20 MHz' },
        { value: 40, label: '40 MHz' },
        { value: 80, label: '80 MHz' },
        { value: 160, label: '160 MHz' },
        { value: 320, label: '320 MHz' },
      ],
    };
    const selectedOptions = bandOptionsMap[value.band] || channelWidthList;
    setChannelWidthOptions(selectedOptions);
  }, [value?.band]);

  const handleTxPowerOptionChange = (event) => {
    setTxPowerOption(event.target.value);
    if (event.target.value === 'default') {
      // 当切换到default时，清空tx-power的值
      setFieldValue(`${namePrefix}.tx-power`, undefined);
    } else {
      setFieldValue(`${namePrefix}.tx-power`, 1);
    }
  };

  // 当band不是2G时，清空legacy-rates的值
  useEffect(() => {
    if (value && value.band !== '2G' && value['legacy-rates'] !== undefined) {
      setFieldValue(`${namePrefix}.legacy-rates`, undefined);
    }
  }, [value?.band]);

  return (
    <>
      <Flex>
        <Heading mt={2} mr={2} size="md" textDecor="underline">
          General
        </Heading>
        {/* {remove!== undefined && (
          <ConfigurationResourcePicker
            name={namePrefix}
            prefix="radio"
            isDisabled={isDisabled?? false}
            defaultValue={SINGLE_RADIO_SCHEMA}
            parent={parent}
          />
        )} */}
        <Spacer />
        {remove!== undefined && (
          <DeleteButton isDisabled={isDisabled} onClick={() => remove()} label={t('configurations.delete_radio')} />
        )}
      </Flex>
      {value!== undefined && value.__variableBlock === undefined? (
        <>
          <SimpleGrid minChildWidth="300px" spacing="20px" mb={8} mt={2} w="100%">
            <SelectField
              name={`${namePrefix}.band`}
              label="band"
              definitionKey="radio.band"
              isDisabled={!canEditBand || isDisabled}
              isRequired
              options={[
                { value: '2G', label: '2G' },
                { value: '5G', label: '5G' },
                // { value: '5G-lower', label: '5G-lower' },
                // { value: '5G-upper', label: '5G-upper' },
                { value: '6G', label: '6G' },
              ]}
            />
            {/*        <SelectField
              name={`${namePrefix}.bandwidth`}
              label="bandwidth"
              definitionKey="radio.bandwidth"
              isDisabled={isDisabled}
              isInt
              isRequired
              options={[
                { value: 5, label: '5' },
                { value: 10, label: '10' },
                { value: 20, label: '20' },
              ]}
            />*/}
            <SelectField
              name={`${namePrefix}.country`}
              label="country"
              definitionKey="radio.country"
              isDisabled={isDisabled}
              isRequired
              options={COUNTRY_LIST}
            />
            <SelectField
              name={`${namePrefix}.channel-mode`}
              label="channel-mode"
              definitionKey="radio.channel-mode"
              isDisabled={isDisabled}
              isRequired
              options={[
                { value: 'EHT', label: 'EHT (WiFi 7,A,B,G,N,AC,AX,BE)' },
                { value: 'HE', label: 'HE (WiFi 6,A,B,G,N,AC,AX)' },
                { value: 'VHT', label: 'VHT (A,B,G,N,AC)' },
                { value: 'HT', label: 'HT (A,B,G,N)' },
              ]}
            />
            <SelectField
              name={`${namePrefix}.channel-width`}
              label="channel-width"
              definitionKey="radio.channel-width"
              isRequired
              isDisabled={isDisabled}
              isInt
              options={channelWidthOptions}
            />
            <ChannelPicker namePrefix={namePrefix} isDisabled={isDisabled} />
          {/*  <SelectField
              name={`${namePrefix}.mimo`}
              label="mimo"
              definitionKey="radio.mimo"
              isDisabled={isDisabled}
              options={[
                { value: '', label: 'None' },
                { value: '1x1', label: '1x1' },
                { value: '2x2', label: '2x2' },
                { value: '3x3', label: '3x3' },
                { value: '4x4', label: '4x4' },
                { value: '5x5', label: '5x5' },
                { value: '6x6', label: '6x6' },
                { value: '7x7', label: '7x7' },
                { value: '8x8', label: '8x8' },
              ]}
              emptyIsUndefined
            />*/}
            <div>
              <label>tx-power</label>
              <div>
                <div style={{ display: 'inline-block', marginRight: '20px' }}>
                  <input
                    type="radio"
                    id={`${namePrefix}.tx-power-default`}
                    name={`${namePrefix}.tx-power-option`}
                    value="default"
                    checked={txPowerOption === 'default'}
                    onChange={handleTxPowerOptionChange}
                    disabled={isDisabled}
                  />
                  <label htmlFor={`${namePrefix}.tx-power-default`} style={{ marginLeft: '5px' }}>default</label>
                </div>
                <div style={{ display: 'inline-block' }}>
                  <input
                    type="radio"
                    id={`${namePrefix}.tx-power-set`}
                    name={`${namePrefix}.tx-power-option`}
                    value="set power"
                    checked={txPowerOption ==='set power'}
                    onChange={handleTxPowerOptionChange}
                    disabled={isDisabled}
                  />
                  <label htmlFor={`${namePrefix}.tx-power-set`} style={{ marginLeft: '5px' }}>set power</label>
                  <div
                    style={{
                      float: 'right',
                      display: txPowerOption === 'set power' ? 'block' : 'none'
                    }}
                  >
                    <NumberField
                      name={`${namePrefix}.tx-power`}
                      label=" "
                      definitionKey="radio.tx-power"
                      isDisabled={isDisabled}
                      w={24}
                      min={1}
                      max={30}
                      emptyIsUndefined
                    />
                  </div>
                </div>
              </div>
            </div>
            {value?.band === '2G' && (
              <ToggleField
                name={`${namePrefix}.legacy-rates`}
                label="legacy-rates"
                definitionKey="radio.legacy-rates"
                isDisabled={isDisabled}
                falseIsUndefined
                onChangeCallback={(e) => {
                  // legacy-rates变化清空multicast与beacon值
                  setFieldValue(`${namePrefix}.rates.multicast`, undefined);
                  setFieldValue(`${namePrefix}.rates.beacon`, undefined);
      
                }}
              />
            )}
            <NumberField
              name={`${namePrefix}.maximum-clients`}
              label="maximum-clients"
              definitionKey="radio.maximum-clients"
              isDisabled={isDisabled}
              acceptEmptyValue
              w={24}
            />
            <ToggleField
              name={`${namePrefix}.he.multiple-bssid`}
              label="multiple-bssid"
              definitionKey="radio.he.multiple-bssid"
              isDisabled={isDisabled}
              falseIsUndefined
            />
            <Rates namePrefix={namePrefix} />
          </SimpleGrid>
          <AdvancedSettings namePrefix={namePrefix} isDisabled={isDisabled} />
        </>
      ) : (
        <LockedRadio variableBlockId={value?.__variableBlock?.[0]} />
      )}
    </>
  );
};

export default React.memo(SingleRadio);