import * as React from 'react';
import { FormControl, FormErrorMessage, FormLabel, Input } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import NumberField from '@/modules-smb/components/FormFields/NumberField';
import { testIpv4 } from '@/modules-smb/constants/formTests';
import useFastField from '@/modules-smb/hooks/useFastField';
import ipaddr from 'ipaddr.js';

type Props = {
  subnet?: string;
  isDisabled?: boolean;
  tempValue?: object[];
};

const StaticLeaseOffsetField = ({ subnet, isDisabled, tempValue = [] }: Props) => {
  const { t } = useTranslation();
  const { onChange: onOffsetChange } = useFastField<number>({ name: 'static-lease-offset' });
  const { value, onChange, onBlur } = useFastField<string>({ name: '__temp_ip' });

  const isSubnetValid = testIpv4(subnet);
  const onIpChange = (v: string) => {
    onChange(v);

    if (testIpv4(v)) {
      const ipEnding = v.split('.').pop()?.split('/')[0];
      const subnetEnding = subnet?.split('.').pop()?.split('/')[0] ?? '0';
      const newOffset = Number(ipEnding) - Number(subnetEnding);

      if (newOffset > 0) {
        onOffsetChange(newOffset);
      } else {
        onOffsetChange(-newOffset);
      }
    }
  };

  React.useEffect(() => {
    if (!value && subnet && isSubnetValid) {
      const subnetEnding = Number(subnet.split('.').pop()?.split('/')[0]);
      onIpChange(`${subnet.split('.').slice(0, 3).join('.')}.${subnetEnding + 1}`);
    }
  }, [value, subnet]);

  if (!subnet || !isSubnetValid) {
    return (
      <NumberField
        name="static-lease-offset"
        label="dhcp-lease.static-lease-offset"
        definitionKey="interface.ipv4.dhcp-lease.static-lease-offset"
        isDisabled={isDisabled}
        isRequired
        w="200px"
      />
    );
  }

  const currOffset = value
    ? Number(value.split('.').pop()?.split('/')[0]) - Number(subnet.split('.').pop()?.split('/')[0])
    : NaN;
  const isIpValid = testIpv4(value);

  // get offest
  const getOffset = (ip: string, subnet: string): number | null => {
    if (!ip || !subnet) return null;
    const ipEnding = ip.split('.').pop()?.split('/')[0];
    const subnetEnding = subnet.split('.').pop()?.split('/')[0];

    if (ipEnding && subnetEnding) {
      const offset = Number(ipEnding) - Number(subnetEnding);
      return offset > 0 ? offset : -offset;
    }
    return null;
  };
  const valOffset = getOffset(value, subnet);
  const isDuplicate = tempValue.some(item => {
    const offset = item['static-lease-offset'];
    if (offset === undefined || offset === null) return false;
    return Number(offset) === valOffset;
  });

  //check if the IP is in the subnet
  const ipv4ToInt = (ip: string): number =>
    ip.split('.').reduce((acc, octet) => (acc << 8) + Number(octet), 0) >>> 0;

  // get available IP range excluding network and broadcast addresses
  const getValidIpRange = (cidr: string): string | null => {
    try {
      const [network, prefixLength] = ipaddr.parseCIDR(cidr);
      if (network.kind() !== 'ipv4') return null;
      const mask = ipaddr.IPv4.subnetMaskFromPrefixLength(prefixLength);
      const networkInt = ipv4ToInt(network.toString()) & ipv4ToInt(mask.toString());
      const broadcastInt = networkInt | (~ipv4ToInt(mask.toString()) >>> 0);
      const firstIpInt = networkInt + 1;
      const lastIpInt = broadcastInt - 1;

      if (firstIpInt > lastIpInt) return null;

      return `${intToIpv4(firstIpInt)} - ${intToIpv4(lastIpInt)}`;
    } catch {
      return null;
    }
  };

  const intToIpv4 = (int: number): string =>
    [(int >>> 24) & 0xff, (int >>> 16) & 0xff, (int >>> 8) & 0xff, int & 0xff].join('.');

  // check if the IP is in the subnet valid range
  const isIpInRange = (ip: string, range: string): boolean => {
    if (!range) return false;
    const [start, end] = range.split(' - ');
    const ipInt = ipv4ToInt(ip);
    return ipv4ToInt(start) <= ipInt && ipInt <= ipv4ToInt(end);
  };
  const validRange = subnet ? getValidIpRange(subnet) : null;
  const isInSubnet = value && validRange ? isIpInRange(value, validRange) : false;

  return (
    <FormControl isRequired isDisabled={isDisabled} isInvalid={!isIpValid || currOffset <= 0 || isDuplicate || !isInSubnet}>
      <FormLabel ms="4px" fontSize="md" fontWeight="normal" _disabled={{ opacity: 0.8 }}>
        IP Address
      </FormLabel>
      <Input
        value={value}
        onChange={(e) => onIpChange(e.target.value)}
        onBlur={onBlur}
        borderRadius="15px"
        fontSize="sm"
        autoComplete="off"
        border="2px solid"
        _disabled={{ opacity: 0.8, cursor: 'not-allowed' }}
        w="160px"
      />
      <FormErrorMessage>
        {!isIpValid
          ? t('form.invalid_ipv4')
          : !isInSubnet
            ? `IP address is not in the subnet${validRange ? `, valid range is ${validRange}` : ''}`
            : currOffset <= 0
              ? `Offset${Number.isNaN(currOffset) ? '' : ` (${currOffset})`} with subnet needs to be bigger than 0`
              : isDuplicate
                ? t('form.duplicate_ip')
                : null}
      </FormErrorMessage>
    </FormControl>
  );
};

export default React.memo(StaticLeaseOffsetField);
