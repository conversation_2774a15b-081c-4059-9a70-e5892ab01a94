import * as React from 'react';
import { Select } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { useConfigurationContext } from '@/modules-smb/contexts/ConfigurationProvider';
import useFastField from '@/modules-smb/hooks/useFastField';
import { useGetResourceTemplates } from '@/modules-smb/hooks/Network/Resources'


type Props = {
  name: string;
  isDisabled: boolean;
  onTemplateChange: (template: Record<string, any>) => void;
  parent?: {
    entity?: string;
    venue?: string;
    subscriber?: string;
  };
  kind?: string;
};

const CaptiveResourcePicker = ({ name, isDisabled, onTemplateChange, parent, kind }: Props) => {
  const { t } = useTranslation();
  const context = useConfigurationContext();
  const field = useFastField<{ __variableBlock?: string[] } | undefined>({ name });
  const [templates, setTemplates] = React.useState<any[]>([]);

  const venueID = parent?.venue || ''

  // 获取可用资源
  const availableResources = React.useMemo(() => {
    if (context.availableResources)
      return context.availableResources
        .filter((resource) => resource.variables[0]?.prefix === 'interface.captive')
        .map((resource) => ({
          value: resource.id,
          label: resource.name,
          template: JSON.parse(resource.variables[0]?.value || '{}'),
        }));
    return [];
  }, [context.availableResources?.length]);


  // 手动请求captive模板方法
  const shouldFetchTemplates = React.useMemo(() => {
    return availableResources.length === 0 && !!venueID && kind !== 'resource-captive';
  }, [availableResources, venueID, kind]);
  const { data: fetchedTemplates = [] } = useGetResourceTemplates(venueID, 'interface.captive', {
    enabled: shouldFetchTemplates,
  });

  // 合并 availableResources 和 fetchedTemplates
  const mergedResources = React.useMemo(() => {
    if (availableResources.length > 0) {
      return availableResources;
    }

    // 格式化 fetchedTemplates
    return templates.map((template: any) => ({
      value: template.id,
      label: template.name,
      template: template.variables?.[0]?.value || {},
    }));
  }, [availableResources, templates]);

  // 当前选中的值
  const selectValue = React.useMemo(() => {
    if (!field.value || !field.value.__variableBlock) return '';
    return field.value.__variableBlock[0];
  }, [field.value?.__variableBlock]);

  // 处理下拉框变化
  const onChange = React.useCallback(
    (e: React.ChangeEvent<HTMLSelectElement>) => {
      const selectedResource = mergedResources.find((res) => res.value === e.target.value);
      if (selectedResource) {
        onTemplateChange(selectedResource.template.captive || {});
        const newObj = { __variableBlock: [e.target.value] };
        field.onChange(newObj);
      } else {
        onTemplateChange({});
        field.onChange(undefined);
      }
    },
    [mergedResources, field, onTemplateChange]
  );

  React.useEffect(() => {
    if (!shouldFetchTemplates) {
      setTemplates([]); // 重置 fetchedTemplates
    } else {
      setTemplates(fetchedTemplates)
    }
  }, [shouldFetchTemplates]);

  if (mergedResources.length === 0) {
    return null;
  }

  return (
    <Select value={selectValue} isDisabled={isDisabled} ml={2} maxW={72} onChange={onChange}>
      <option value="">{t('configurations.no_resource_selected')}</option>
      {mergedResources.map((res) => (
        <option key={res.value} value={res.value}>
          {res.label}
        </option>
      ))}
      {selectValue !== '' && !mergedResources.find(({ value: resource }) => resource === selectValue) && (
        <option value={selectValue}>{t('configurations.invalid_resource')}</option>
      )}
    </Select>
  );
};

export default CaptiveResourcePicker;