import React, { useMemo } from 'react';
import { Tab, useColorMode, useMultiStyleConfig, useTab } from '@chakra-ui/react';
import { useGetResource } from '@/modules-smb/hooks/Network/Resources';
import useFastField from '@/modules-smb/hooks/useFastField';

// eslint-disable-next-line react/prop-types
const RadioTab: React.FC<{ index: number }> = React.forwardRef(({ index, ...props }, ref) => {
  const { value } = useFastField({ name: `configuration[${index}]` });
  const { data: resource } = useGetResource({
    id: value?.__variableBlock,
    enabled: value?.__variableBlock !== undefined,
  });
  const { colorMode } = useColorMode();
  const isLight = colorMode === 'light';

  // @ts-ignore
  const tabProps = useTab({ ...props, ref });

  // 2. Hook into the Tabs `size`, `variant`, props
  const styles = useMultiStyleConfig('Tabs', tabProps);

  const name = useMemo(() => {
    if (value?.band) {
      return value.band;
    }
    if (resource?.variables && resource?.variables[0]?.value) {
      try {
        const json = JSON.parse(resource?.variables[0]?.value);
        return json.band;
      } catch (e) {
        return '';
      }
    }

    return '';
  }, [value, resource]);
  return (
    <Tab
      _selected={{
        // @ts-ignore
        ...styles.tab?._selected,
        borderBottomColor: isLight ? 'gray.100' : 'gray.800',
      }}
    >
      {name} Radio
    </Tab>
  );
});

export default React.memo(RadioTab);
