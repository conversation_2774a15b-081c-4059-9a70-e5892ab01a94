import React, {useCallback, useState, useEffect, useMemo} from "react";
import {Flex, Spacer, VStack} from "@chakra-ui/react";
import {FieldArray, Formik} from "formik";
import PropTypes from "prop-types";
import isEqual from "react-fast-compare";
import {useTranslation} from "react-i18next";
import {v4 as uuid} from "uuid";
import InternalFormAccess from "../common/InternalFormAccess";
import SectionGeneralCard from "../common/SectionGeneralCard";
import Radios from "./Radios";
import {RADIOS_SCHEMA} from "./radiosConstants";
import DeleteButton from "@/modules-smb/components/Buttons/DeleteButton";
import {ConfigurationSectionShape} from "@/modules-smb/constants/propShapes";

const propTypes = {
    editing: PropTypes.bool.isRequired,
    setSection: PropTypes.func.isRequired,
    sectionInformation: ConfigurationSectionShape.isRequired,
    removeSub: PropTypes.func.isRequired,
    parent: PropTypes.shape({
        entity: PropTypes.string,
        venue: PropTypes.string,
        subscriber: PropTypes.string
    })
};

const RadiosSection = ({editing, setSection, sectionInformation, removeSub, parent}) => {
    const {t} = useTranslation();
    const [formKey, setFormKey] = useState(uuid());
    const sectionRef = useCallback(
        node => {
            if (node !== null) {
                const invalidValues = [];
                for (const [k, error] of Object.entries(node.errors)) {
                    invalidValues.push({key: `radios.${k}`, error});
                }

                const newSection = {
                    data: node.values,
                    isDirty: node.dirty,
                    invalidValues
                };

                if (!isEqual(sectionInformation, newSection)) {
                    setSection(newSection);
                }
            }
        },
        [sectionInformation]
    );

    const removeUnit = () => removeSub("radios");

    useEffect(() => {
        if (!editing) {
            setFormKey(uuid());
        }
    }, [editing]);

    return (
        <>
            <Flex>
                <Spacer />
            </Flex>
            <Formik
                key={formKey}
                innerRef={sectionRef}
                initialValues={sectionInformation.data}
                validationSchema={RADIOS_SCHEMA(t)}
            >
                {({values}) => (
                    <>
                        <InternalFormAccess shouldValidate={sectionInformation?.shouldValidate} />
                        <VStack spacing={4}>
                            <SectionGeneralCard
                                // editing={editing}
                                buttons={<DeleteButton onClick={removeUnit} isDisabled={!editing} />}
                            />
                            <FieldArray name="configuration">
                                {arrayHelpers => (
                                    <Radios
                                        editing={editing}
                                        arrayHelpers={arrayHelpers}
                                        radioBands={values.configuration.map(radio => radio.band)}
                                        radioBandsLength={values.configuration.length}
                                        parent={parent}
                                    />
                                )}
                            </FieldArray>
                        </VStack>
                    </>
                )}
            </Formik>
        </>
    );
};

RadiosSection.propTypes = propTypes;
export default React.memo(RadiosSection, isEqual);
