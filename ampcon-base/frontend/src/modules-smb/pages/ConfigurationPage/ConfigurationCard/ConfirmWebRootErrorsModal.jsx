import React from 'react';
import { <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>lay, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>dal<PERSON>ooter, ModalBody, Button, Text } from '@chakra-ui/react';

const ConfirmWebRootErrorsModal = ({ isOpen, onClose, errors }) => {
    // console.log('modal',errors.ssids);
    const errorResult=[];
    if (errors.ssids && Array.isArray(errors.ssids)) {
        errors.ssids.forEach((ssidError) => {
          if (
            ssidError.captivePortal && 
            ssidError.captivePortal['web-root']
          ) {
            errorResult.push(ssidError.captivePortal['web-root']);
          }
        });
      }
      // console.log('errorResult',errorResult);
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Web-Root Errors</ModalHeader>
        <ModalBody>
          <Text mb={4}>Please resolve the following web-root errors before saving:</Text>
          {errorResult.map((error, index) => (
                        <Text key={index} color="red.500" fontSize="sm" mb={4}>
                            {error}
                        </Text>
                    ))}
          
        </ModalBody>
        <ModalFooter>
          <Button colorScheme="red" mr={3} onClick={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ConfirmWebRootErrorsModal;