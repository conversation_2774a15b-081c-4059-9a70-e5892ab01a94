import React, { useEffect, useState } from 'react';
import {
  useToast,
  Ta<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>bPanels,
  TabPanel,
  Tab,
  SimpleGrid,
  FormControl,
  FormLabel,
  Button,
  useDisclosure,
} from '@chakra-ui/react';
import { Formik, Form } from 'formik';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import DeviceRulesField from '@/modules-smb/components/CustomFields/DeviceRulesField';
import NotesTable from '@/modules-smb/components/CustomFields/NotesTable';
import MultiSelectField from '@/modules-smb/components/FormFields/MultiSelectField';
import SelectWithSearchField from '@/modules-smb/components/FormFields/SelectWithSearchField';
import StringField from '@/modules-smb/components/FormFields/StringField';
import ConfigurationInUseModal from '@/modules-smb/components/Modals/Configuration/ConfigurationInUseModal';
import { EntitySchema } from '@/modules-smb/constants/formSchemas';
import { EntityShape } from '@/modules-smb/constants/propShapes';
import { useGetConfigurationInUse } from '@/modules-smb/hooks/Network/Configurations';
// import useGetDeviceTypes from '@/modules-smb/hooks/Network/DeviceTypes';
import { useGetEntities } from '@/modules-smb/hooks/Network/Entity';
import { useGetVenues } from '@/modules-smb/hooks/Network/Venues';
import { useGetDeviceTypes } from "@/modules-ampcon/apis/upgrade_api";

const propTypes = {
  editing: PropTypes.bool.isRequired,
  configuration: PropTypes.shape(EntityShape).isRequired,
  formRef: PropTypes.instanceOf(Object).isRequired,
};

const EditConfigurationForm = ({ editing, configuration, formRef }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const [formKey, setFormKey] = useState(uuid());
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { data: entities } = useGetEntities({ t, toast });
  const { data: venues } = useGetVenues({ t, toast });
  const { data: deviceTypesList } = useGetDeviceTypes();
  const { data: inUse } = useGetConfigurationInUse({
    id: configuration?.id,
    enabled: true,
  });

  const getEntity = () => {
    if (configuration?.entity !== '') return `ent:${configuration?.entity}`;
    if (configuration?.venue !== '') return `ven:${configuration?.venue}`;
    return `ent:0000-0000-0000`;
  };

  useEffect(() => {
    setFormKey(uuid());
  }, [editing]);

  return (
    <>
      <Formik
        innerRef={formRef}
        enableReinitialize
        key={formKey}
        initialValues={{
          ...configuration,
          entity: getEntity(),
        }}
        validationSchema={EntitySchema(t)}
      >
        <Tabs variant="enclosed" w="100%">
          <TabList>
            <Tab>{t('common.main')}</Tab>
            <Tab>{t('common.notes')}</Tab>
          </TabList>
          <TabPanels>
            <TabPanel>
              <Form>
                <SimpleGrid minChildWidth="300px" spacing="20px">
                  <StringField name="name" label={t('common.name')} isRequired isDisabled={!editing} />
                  <MultiSelectField
                    name="deviceTypes"
                    label={t('configurations.device_types')}
                    options={
                      deviceTypesList
                        ? deviceTypesList.map((deviceType) => ({
                            value: deviceType,
                            label: deviceType,
                          }))
                        : []
                    }
                    isRequired
                    canSelectAll
                    isDisabled={!editing}
                  />
                  {/* <DeviceRulesField isDisabled={!editing} /> */}
                  <StringField name="description" label={t('common.description')} isDisabled={!editing} />
                  <SelectWithSearchField
                    name="entity"
                    label={t('inventory.parent')}
                    isRequired
                    isDisabled={!editing}
                    options={
                          venues?.map((ven) => ({
                            value: `ven:${ven.id}`,
                            label: `${ven.name}${ven.description ? `: ${ven.description}` : ''}`,
                          })) ?? []
                    }
                    // options={[
                    //   {
                    //     label: t('entities.title'),
                    //     options:
                    //       entities?.map((ent) => ({
                    //         value: `ent:${ent.id}`,
                    //         label: `${ent.name}${ent.description ? `: ${ent.description}` : ''}`,
                    //       })) ?? [],
                    //   },
                    //   {
                    //     label: t('venues.title'),
                    //     options:
                    //       venues?.map((ven) => ({
                    //         value: `ven:${ven.id}`,
                    //         label: `${ven.name}${ven.description ? `: ${ven.description}` : ''}`,
                    //       })) ?? [],
                    //   },
                    // ]}
                  />
                  {/*<FormControl>*/}
                  {/*  <FormLabel ms="4px" fontSize="md" fontWeight="normal">*/}
                  {/*    In Use By*/}
                  {/*  </FormLabel>*/}
                  {/*  <Button variant="link" mt={2} onClick={onOpen}>*/}
                  {/*    {`${inUse?.ent?.length ?? 0} ${t('entities.one')}, ${inUse?.ven?.length ?? 0} ${t(*/}
                  {/*      'venues.one',*/}
                  {/*    )}, ${inUse?.inv?.length ?? 0} ${t('devices.title')}`}*/}
                  {/*  </Button>*/}
                  {/*</FormControl>*/}
                </SimpleGrid>
              </Form>
            </TabPanel>
            <TabPanel>
              <NotesTable isDisabled={!editing} />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </Formik>
      <ConfigurationInUseModal isOpen={isOpen} onClose={onClose} config={configuration} />
    </>
  );
};

EditConfigurationForm.propTypes = propTypes;

export default React.memo(EditConfigurationForm);
