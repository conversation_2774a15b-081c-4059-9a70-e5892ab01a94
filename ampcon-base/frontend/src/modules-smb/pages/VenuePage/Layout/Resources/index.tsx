import * as React from 'react';
import { Box, Heading, Spacer, useDisclosure } from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import EntityResourceActions from './ResourceActions';
import Card from '@/modules-smb/components/Card';
import CardBody from '@/modules-smb/components/Card/CardBody';
import CardHeader from '@/modules-smb/components/Card/CardHeader';
import CreateResourceModal from '@/modules-smb/components/Modals/Resources/CreateModal';
import EditResourceModal from '@/modules-smb/components/Modals/Resources/EditModal';
import ResourcesTable from '@/modules-smb/components/Tables/ResourceTable';
import { useGetVenue } from '@/modules-smb/hooks/Network/Venues';
import { Resource } from '@/modules-smb/models/Resource';

type Props = {
  id: string;
};

const VenueResourcesCard = ({ id }: Props) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [resource, setResource] = React.useState<Resource>();
  const { isOpen: isEditOpen, onOpen: openEdit, onClose: closeEdit } = useDisclosure();
  const getVenue = useGetVenue({ id });

  const refreshTable = () => {
    queryClient.invalidateQueries(['get-resources-with-select']);
  };

  const openEditModal = (openedResource: Resource) => () => {
    setResource(openedResource);
    openEdit();
  };
  const openDetailsModalFromTable = (openedResource: Resource) => {
    setResource(openedResource);
    openEdit();
  };

  const actions = React.useCallback(
    (cell: { row: { original: Resource } }) => (
      <EntityResourceActions
        resource={cell.row.original}
        refreshTable={getVenue.refetch}
        openEditModal={openEditModal(cell.row.original)}
      />
    ),
    [],
  );

  return (
    <Card>
      <CardHeader>
        <Heading size="md">{t('resources.title')}</Heading>
        <Spacer />
        <CreateResourceModal refresh={getVenue.refetch} entityId={id} isVenue />
      </CardHeader>
      <CardBody>
        <Box w="100%" overflowX="auto">
          <ResourcesTable
            select={getVenue.data?.variables ?? []}
            actions={actions}
            openDetailsModal={openDetailsModalFromTable}
          />
        </Box>
      </CardBody>
      <EditResourceModal isOpen={isEditOpen} onClose={closeEdit} resource={resource} refresh={refreshTable} />
    </Card>
  );
};

export default VenueResourcesCard;
