import * as React from 'react';
import { Button, Popover, Space, Typography, Grid, Tooltip } from 'antd';
import { ClockCircleOutlined, CloseOutlined, SaveOutlined, StopOutlined } from '@ant-design/icons';
import 'react-datepicker/dist/react-datepicker.css';
import ReactDatePicker from 'react-datepicker';
import { useTranslation } from 'react-i18next';
import type { PopoverProps } from 'antd';

const { useBreakpoint } = Grid;
const { Text } = Typography;

const CustomInputButton = React.forwardRef(
  ({ value, onClick }: { value: string; onClick: () => void }, ref: React.LegacyRef<HTMLButtonElement>) => (
    <Button type="default" size="small" onClick={onClick} ref={ref} style={{ marginTop: 4 }}>
      {value}
    </Button>
  ),
);

const getStart = () => {
  const date = new Date();
  date.setHours(date.getHours() - 1);
  return date;
};

type Props = {
  defaults?: { start: Date; end: Date };
  setTime: (start: Date, end: Date) => void;
  onClear: () => void;
};

const AnalyticsDatePickers = ({ defaults, setTime, onClear }: Props) => {
  const { t } = useTranslation();
  const [start, setStart] = React.useState<Date>(defaults?.start ?? getStart());
  const [end, setEnd] = React.useState<Date>(defaults?.end ?? new Date());
  const screens = useBreakpoint();

  const onStartChange = (newDate: Date) => {
    setStart(newDate);
  };
  const onEndChange = (newDate: Date) => {
    setEnd(newDate);
  };
  const clear = (onClose: PopoverProps['onOpenChange']) => () => {
    onClear();
    if (onClose) onClose(false);
  };
  const onSave = (onClose: PopoverProps['onOpenChange']) => () => {
    if (onClose) onClose(false);
    setTime(start, end);
  };

  const content = (onClose: PopoverProps['onOpenChange']) => (
    <Space direction="vertical" style={{ width: screens.xs ? 360 : 460 }}>
      <Space style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
        <Text strong>{t('controller.crud.choose_time')}</Text>
        <Space>
          <Tooltip title={t('controller.crud.clear_time')}>
            <Button
              type="text"
              danger
              icon={<StopOutlined />}
              onClick={clear(onClose)}
              size="small"
            />
          </Tooltip>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={onSave(onClose)}
            size="small"
          />
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={() => onClose && onClose(false)}
            size="small"
          />
        </Space>
      </Space>
      {screens.xs ? (
        <Space direction="vertical">
          <Space>
            <Text>{t('system.start')}:</Text>
            <ReactDatePicker
              selected={start}
              onChange={onStartChange}
              timeInputLabel={`${t('common.time')}: `}
              dateFormat="dd/MM/yyyy hh:mm aa"
              timeFormat="p"
              showTimeSelect
              // @ts-ignore
              customInput={<CustomInputButton />}
            />
          </Space>
          <Space>
            <Text>{t('common.end')}:</Text>
            <ReactDatePicker
              selected={end}
              onChange={onEndChange}
              timeInputLabel={`${t('common.time')}: `}
              dateFormat="dd/MM/yyyy hh:mm aa"
              timeFormat="p"
              showTimeSelect
              // @ts-ignore
              customInput={<CustomInputButton />}
            />
          </Space>
        </Space>
      ) : (
        <Space>
          <Text>{t('system.start')}:</Text>
          <ReactDatePicker
            selected={start}
            onChange={onStartChange}
            timeInputLabel={`${t('common.time')}: `}
            dateFormat="dd/MM/yyyy hh:mm aa"
            timeFormat="p"
            showTimeSelect
            // @ts-ignore
            customInput={<CustomInputButton />}
          />
          <Text>{t('common.end')}:</Text>
          <ReactDatePicker
            selected={end}
            onChange={onEndChange}
            timeInputLabel={`${t('common.time')}: `}
            dateFormat="dd/MM/yyyy hh:mm aa"
            timeFormat="p"
            showTimeSelect
            // @ts-ignore
            customInput={<CustomInputButton />}
          />
        </Space>
      )}
    </Space>
  );

  React.useEffect(() => {
    setStart(defaults?.start ?? getStart());
    setEnd(defaults?.end ?? new Date());
  }, [defaults]);

  return (
    <Popover
      placement="bottomRight"
      trigger="click"
      content={(_, onClose) => content(onClose)}
    >
      <Tooltip title={t('controller.crud.choose_time')}>
        <Button
          type="primary"
          icon={<ClockCircleOutlined />}
          size="middle"
        />
      </Tooltip>
    </Popover>
  );
};

export default AnalyticsDatePickers;