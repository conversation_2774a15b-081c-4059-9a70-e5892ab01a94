import React from "react";
import {useParams} from "react-router-dom";
import VenuePageLayout from "./Layout";
import VenuePageHeader from "./VenueHeader";
import {useGetAnalyticsBoardDevices} from "@/modules-smb/hooks/Network/Analytics";
import {useGetVenue} from "@/modules-smb/hooks/Network/Venues";
import {axiosAnalytics, axiosSec} from "@/modules-smb/utils/axiosInstances";

import {useBreakpoint, useDisclosure, Box} from "@chakra-ui/react";
import {useNavigate} from "react-router-dom";
import EntityNavigationTree from "@/modules-smb/layout/Sidebar/EntityNavigationButton/Tree";
const VenuePage = ({idToUse}: {idToUse?: string}) => {
    const breakpoint = useBreakpoint();
    const navigate = useNavigate();
    const modalProps = useDisclosure();

    const navigateTo = React.useCallback(
        (id: string, type: "venue" | "entity") => {
            console.log("navigateTo", type, id);
            navigate(`/wireless/${type}/${id}`);
        },
        [breakpoint]
    );

    const {id} = useParams();

    const entityIdToUse = React.useMemo(() => {
        if (id !== undefined && id !== "") {
            return id;
        }
        if (idToUse !== undefined && idToUse !== "") {
            return idToUse;
        }

        return undefined;
    }, [idToUse, id]);

    const getVenue = useGetVenue({id});
    const isAnalyticsAvailable = axiosSec.defaults.baseURL !== axiosAnalytics.defaults.baseURL;
    const boardId = getVenue.data?.boards[0];
    const getDashboard = useGetAnalyticsBoardDevices({id: isAnalyticsAvailable ? boardId : undefined});

    const hasAnalyticsVal = !getDashboard.isLoading && !getDashboard.error;

    return (
        <>
            <Box>
                <EntityNavigationTree isModalOpen={modalProps.isOpen} treeRoot={undefined} navigateTo={navigateTo} />
            </Box>
            {entityIdToUse && (
                <>
                    <VenuePageHeader
                        id={entityIdToUse}
                        boardId={!getDashboard.isLoading && !getDashboard.error ? boardId : undefined}
                    />
                    <VenuePageLayout id={entityIdToUse} hasAnalytics={!getDashboard.isLoading && !getDashboard.error} />
                </>
            )}
        </>
    );
};

export default VenuePage;
