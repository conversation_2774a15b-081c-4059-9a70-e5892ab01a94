import React from 'react';
import { <PERSON>, Tab, <PERSON>b<PERSON><PERSON>, TabPanel, TabPane<PERSON>, Tabs } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import FmsLogsCard from './FmsLogs';
import GeneralLogsCard from './GeneralLogs';
import LogsCard from './Notifications';
import SecLogsCard from './SecLogs';
import Card from '@/modules-smb/components/Card';
import CardHeader from '@/modules-smb/components/Card/CardHeader';

const INDEX_PARAM = 'notifications-tab-index';

const getDefaultTabIndex = () => {
  const index = localStorage.getItem(INDEX_PARAM) || '0';
  try {
    return parseInt(index, 10);
  } catch {
    return 0;
  }
};

const NotificationsPage = () => {
  const { t } = useTranslation();
  const [tabIndex, setTabIndex] = React.useState(getDefaultTabIndex());

  const handleTabChange = (index: number) => {
    setTabIndex(index);
    localStorage.setItem(INDEX_PARAM, index.toString());
  };

  return (
    <Card p={0}>
      <Tabs index={tabIndex} onChange={handleTabChange} variant="enclosed" isLazy>
        <TabList>
          <CardHeader>
            <Tab>
              {t('venues.one')} {t('notification.other')}
            </Tab>
            <Tab>Provisioning</Tab>
            <Tab>{t('logs.security')}</Tab>
            <Tab>{t('logs.firmware')}</Tab>
          </CardHeader>
        </TabList>
        <TabPanels>
          <TabPanel p={0}>
            <Box
              borderLeft="1px solid"
              borderRight="1px solid"
              borderBottom="1px solid"
              borderColor="var(--chakra-colors-chakra-border-color)"
              borderBottomLeftRadius="15px"
              borderBottomRightRadius="15px"
            >
              <LogsCard />
            </Box>
          </TabPanel>
          <TabPanel p={0}>
            <Box
              borderLeft="1px solid"
              borderRight="1px solid"
              borderBottom="1px solid"
              borderColor="var(--chakra-colors-chakra-border-color)"
              borderBottomLeftRadius="15px"
              borderBottomRightRadius="15px"
            >
              <GeneralLogsCard />
            </Box>
          </TabPanel>
          <TabPanel p={0}>
            <Box
              borderLeft="1px solid"
              borderRight="1px solid"
              borderBottom="1px solid"
              borderColor="var(--chakra-colors-chakra-border-color)"
              borderBottomLeftRadius="15px"
              borderBottomRightRadius="15px"
            >
              <SecLogsCard />
            </Box>
          </TabPanel>
          <TabPanel p={0}>
            <Box
              borderLeft="1px solid"
              borderRight="1px solid"
              borderBottom="1px solid"
              borderColor="var(--chakra-colors-chakra-border-color)"
              borderBottomLeftRadius="15px"
              borderBottomRightRadius="15px"
            >
              <FmsLogsCard />
            </Box>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Card>
  );
};

export default NotificationsPage;
