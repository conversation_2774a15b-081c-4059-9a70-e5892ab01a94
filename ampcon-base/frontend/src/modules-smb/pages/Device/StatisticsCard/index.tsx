import * as React from 'react';
import { Box, Center, Flex, Heading, HStack, Select, Spacer, Spinner } from '@chakra-ui/react';
import { ChartLine } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import StatisticsCardDatePickers from './DatePickers';
import InterfaceChart from './InterfaceChart';
import DeviceMemoryChart from './MemoryChart';
import { useStatisticsCard } from './useStatisticsCard';
import ViewLastStatsModal from './ViewLastStatsModal';
import Vlan<PERSON>hart from './VlanChart';
import  RefreshButton  from '@/modules-smb/components/Buttons/RefreshButton';
import { Card } from '@/modules-smb/components/Containers/Card';
import { CardBody } from '@/modules-smb/components/Containers/Card/CardBody';
import { CardHeader } from '@/modules-smb/components/Containers/Card/CardHeader';
import  LoadingOverlay  from '@/modules-smb/components/LoadingOverlay';

const interfaceNameLabel = (v?: string) => {
  if (!v) return '';

  if (v.startsWith('up')) {
    const split = v.split('v');
    const vlanId = split[split.length - 1];

    return vlanId === '0' ? 'Upstream' : `Upstream - Vlan ${vlanId}`;
  }
  if (v.startsWith('down')) {
    const split = v.split('v');
    const vlanId = split[split.length - 1];

    return vlanId === '0' ? 'Downstream' : `Downstream - Vlan ${vlanId}`;
  }

  return v;
};
const EmptyStateMessage = () => {
  const { t } = useTranslation();
  return (
    <Center py={8}>
      <Box textAlign="center">
        <Heading size="md" mb={2}>
          {t('common.no_statistics_found')}
        </Heading>
      </Box>
    </Center>
  );
};
type Props = {
  serialNumber: string;
};

const DeviceStatisticsCard = ({ serialNumber }: Props) => {
  const { t } = useTranslation();
  const { time, setTime, parsedData, isLoading, selected, onSelectInterface, refresh } = useStatisticsCard({
    serialNumber,
  });
  const [formatChosen, setFormatChosen] = React.useState<'bytes' | 'packets'>('bytes');

  const setNewTime = (start: Date, end: Date) => {
    setTime({ start, end });
  };
  const onClear = () => {
    setTime(undefined);
  };
  const onFormatChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormatChosen(e.target.value as 'bytes' | 'packets');
  };

  const interfaces = React.useMemo(() => {
    if (!parsedData || !parsedData.interfaces) return null;
    return parsedData.interfaces[selected] ? (
      <InterfaceChart data={parsedData.interfaces[selected]} format={formatChosen} />
    ) : null;
  }, [parsedData, selected, formatChosen]);
  const vlans = React.useMemo(() => {
    if (!parsedData || !parsedData.vlans) return null;
    const vlanName = selected.startsWith('VLAN-') ? selected.replace('VLAN-', '') : null;
    return vlanName && parsedData.vlans[vlanName] ? (
      <VlanChart data={parsedData.vlans[vlanName]} format={formatChosen} />
    ) : null;
  }, [parsedData, selected, formatChosen]);

  const memory = React.useMemo(() => {
    console.log("parsedData",parsedData)
    console.log("memory",parsedData?.memory)
    if (!parsedData || !parsedData.memory) return null;
    return <DeviceMemoryChart data={parsedData.memory} />;
  }, [parsedData]);
   const hasNoData = () => {
      if (!parsedData) return true;

      if (selected === 'memory') {
        return !memory;
      }

      return !interfaces && !vlans;
    };
  return (
    <Card mb={4}>
      <CardHeader
        icon={<ChartLine weight="bold" size={20} />}
        headerStyle={{
          color: 'green',
        }}
      >
        <Heading size="md">{t('configurations.statistics')}</Heading>
        <Spacer />
        <HStack>
          {selected === 'memory' ? null : (
            <Select value={formatChosen} onChange={onFormatChange} w="112px">
              <option value="bytes">Data</option>
              <option value="packets">Packets</option>
            </Select>
          )}
          <Select value={selected} onChange={onSelectInterface} w="unset">
            {parsedData?.interfaces
              ? Object.keys(parsedData.interfaces).map((v) => (
                  <option value={v} key={uuid()}>
                    {interfaceNameLabel(v)}
                  </option>
                ))
              : null}
            {parsedData?.vlans
              ? Object.keys(parsedData.vlans).map((v) => (
                  <option value={`VLAN-${v}`} key={uuid()}>
                    VLAN - {v}
                  </option>
                ))
              : null}
            <option value="memory">{t('statistics.memory')}</option>
          </Select>
          <StatisticsCardDatePickers defaults={time} setTime={setNewTime} onClear={onClear} />
          <ViewLastStatsModal serialNumber={serialNumber} />
          <RefreshButton
            size="sm"
            onClick={refresh}
            isCompact
            isFetching={isLoading.isLoading}
            // @ts-ignore
            colorScheme="blue"
          />
        </HStack>
      </CardHeader>
      <CardBody display="block" mb={2} minH="230px">
        {time && (
          <Flex>
            <Heading size="sm">
              {t('controller.devices.from_to', {
                from: `${time.start.toLocaleDateString()} ${time.start.toLocaleTimeString()}`,
                to: `${time.end.toLocaleDateString()} ${time.end.toLocaleTimeString()}`,
              })}
            </Heading>
          </Flex>
        )}
        {(!parsedData && isLoading.isLoading) || (isLoading.isLoading && isLoading.progress !== undefined) ? (
          <Center my="auto" mt="100px">
            {isLoading.progress !== undefined && (
              <Heading size="md" mr={2}>
                {isLoading.progress.toFixed(2)}%
              </Heading>
            )}
            <Spinner size="xl" />
          </Center>
        ) : (
          <LoadingOverlay isLoading={isLoading.isLoading}>
            <Box>
              {selected === 'memory' ? memory : interfaces || vlans}
              {hasNoData() && !isLoading.isLoading && <EmptyStateMessage />}
            </Box>
          </LoadingOverlay>
        )}
      </CardBody>
    </Card>
  );
};

export default DeviceStatisticsCard;
