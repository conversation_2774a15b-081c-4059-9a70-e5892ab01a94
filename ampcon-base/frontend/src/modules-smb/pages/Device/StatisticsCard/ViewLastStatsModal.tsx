import * as React from 'react';
import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Button,
  Center,
  IconButton,
  Spinner,
  Tooltip,
  Heading,
  useClipboard,
  useColorMode,
  useDisclosure,
} from '@chakra-ui/react';
import { JsonViewer } from '@textea/json-viewer';
import { ClockCounterClockwise } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';
import  RefreshButton  from '@/modules-smb/components/Buttons/RefreshButton';
import  LoadingOverlay  from '@/modules-smb/components/LoadingOverlay';
import { Modal } from '@/modules-smb/components/Modals/Modal';
import { useGetDeviceLastStats } from '@/modules-smb/hooks/Network/Statistics';

type Props = {
  serialNumber: string;
};
const EmptyStateMessage = () => {
  const { t } = useTranslation();
  return (
    <Center py={8}>
      <Box textAlign="center">
        <Heading size="md" mb={2}>
          {t('common.no_last_statistics_found')}
        </Heading>
      </Box>
    </Center>
  );
};

const ViewLastStatsModal = ({ serialNumber }: Props) => {
  const { t } = useTranslation();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const getLastStats = useGetDeviceLastStats({ serialNumber });
  const { hasCopied, onCopy, setValue } = useClipboard(JSON.stringify(getLastStats.data ?? {}, null, 2));
  const { colorMode } = useColorMode();

  React.useEffect(() => {
    if (getLastStats.data) {
      setValue(JSON.stringify(getLastStats.data, null, 2));
    }
  }, [getLastStats.data, isOpen]);
  return (
    <>
      <Tooltip label={t('statistics.last_stats')}>
        <IconButton
          aria-label={t('statistics.last_stats')}
          onClick={onOpen}
          colorScheme="purple"
          icon={<ClockCounterClockwise size={20} />}
        />
      </Tooltip>
      <Modal
        isOpen={isOpen}
        title={t('statistics.last_stats')}
        topRightButtons={
          <>
            <Button onClick={onCopy} size="md" colorScheme="teal">
              {hasCopied ? `${t('common.copied')}!` : t('common.copy')}
            </Button>
            <RefreshButton
              isCompact
              onClick={getLastStats.refetch}
              isFetching={getLastStats.isFetching}
              colorScheme="blue"
            />
          </>
        }
        onClose={onClose}
      >
        <Box display="inline-block" w="100%">
          {getLastStats.isFetching && (
            <Center my="100px">
              <Spinner />
            </Center>
          )}
          {!getLastStats.data && !getLastStats.isFetching && (
            <EmptyStateMessage />
          )}
          {getLastStats.data && (
            <LoadingOverlay isLoading={getLastStats.isFetching}>
              <Box maxH="calc(100vh - 250px)" minH="300px" overflowY="auto">
                <Accordion defaultIndex={0} allowToggle>
                  <AccordionItem>
                    <h2>
                      <AccordionButton>
                        <Box flex="1" textAlign="left">
                          {t('common.preview')}
                        </Box>
                        <AccordionIcon />
                      </AccordionButton>
                    </h2>
                    <AccordionPanel pb={4}>
                      <JsonViewer
                        rootName={false}
                        displayDataTypes={false}
                        enableClipboard={false}
                        theme={colorMode === 'light' ? undefined : 'dark'}
                        defaultInspectDepth={1}
                        value={getLastStats.data as object}
                        style={{ background: 'unset', display: 'unset' }}
                      />
                    </AccordionPanel>
                  </AccordionItem>
                  <AccordionItem>
                    <h2>
                      <AccordionButton>
                        <Box flex="1" textAlign="left">
                          {t('analytics.raw_data')}
                        </Box>
                        <AccordionIcon />
                      </AccordionButton>
                    </h2>
                    <AccordionPanel pb={4} overflowX="auto">
                      <pre>{JSON.stringify(getLastStats.data, null, 2)}</pre>
                    </AccordionPanel>
                  </AccordionItem>
                </Accordion>
              </Box>
            </LoadingOverlay>
          )}
        </Box>
      </Modal>
    </>
  );
};

export default ViewLastStatsModal;
