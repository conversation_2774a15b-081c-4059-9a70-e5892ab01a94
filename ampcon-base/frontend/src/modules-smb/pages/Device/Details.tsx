import * as React from 'react';
import { Button, Grid, GridItem, Heading, Link, Spacer, useClipboard, useDisclosure, Box } from '@chakra-ui/react';
import { Eye, EyeSlash, ListBullets } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';
import ViewCapabilitiesModal from './ViewCapabilitiesModal';
import ViewConfigurationModal from './ViewConfigurationModal';
import { Card } from '@/modules-smb/components/Containers/Card';
import { CardBody } from '@/modules-smb/components/Containers/Card/CardBody';
import { CardHeader } from '@/modules-smb/components/Containers/Card/CardHeader';
import { compactDate } from '@/modules-smb/helpers/dateFormatting';
import { useGetDevice } from '@/modules-smb/hooks/Network/Devices';
import { useGetProvUi } from '@/modules-smb/hooks/Network/Endpoints';
import { useGetTag } from '@/modules-smb/hooks/Network/Inventory';
import { useNavigate } from 'react-router-dom';

type Props = {
  serialNumber: string;
};

const DeviceDetails = ({ serialNumber }: Props) => {
  const { t } = useTranslation();
  const getProvUi = useGetProvUi();
  const getDevice = useGetDevice({ serialNumber });
  const getTag = useGetTag({ serialNumber });
  const { isOpen: isShowingPassword, onToggle: onTogglePassword } = useDisclosure();
  const { hasCopied, onCopy, setValue } = useClipboard(
    getDevice.data?.devicePassword !== undefined && getDevice.data?.devicePassword !== ''
      ? getDevice.data?.devicePassword
      : 'adminwifi',
  );
  const getPassword = () => {
    if (!getDevice.data) return 'adminwifi';
    if (isShowingPassword) {
      return getDevice.data?.devicePassword === '' ? 'adminwifi' : getDevice.data?.devicePassword;
    }
    return '••••••••••••';
  };

  // const goToProvUi = (dir: string) => `${getProvUi.data}/#/${dir}`;

  //handle Provisioning->venue click, click to jump to the page
  const navigate = useNavigate();
  const handleClick = (path: string) => () => {
    navigate(`/wireless/${path}`);
  };

  React.useEffect(() => {
    if (getDevice.data && getDevice.data.devicePassword) {
      setValue(
        getDevice.data?.devicePassword !== undefined && getDevice.data?.devicePassword !== ''
          ? getDevice.data?.devicePassword
          : 'adminwifi',
      );
    }
  }, [getDevice.data]);

  return (
    <Card mb={4}>
      <CardHeader
        mb={2}
        headerStyle={{
          color: 'purple',
        }}
        icon={<ListBullets weight="bold" size={20} />}
      >
        <Heading size="md">{t('common.details')}</Heading>
        <Spacer />
        {/* <ViewCapabilitiesModal serialNumber={serialNumber} />
        <ViewConfigurationModal serialNumber={serialNumber} /> */}
      </CardHeader>
      <CardBody display="block">
        <Grid templateColumns="repeat(2, 1fr)" gap={0} w="100%">
          <GridItem colSpan={1} alignContent="center" alignItems="center">
            <Heading size="sm">MAC:</Heading>
          </GridItem>
          <GridItem colSpan={1}>{getDevice.data?.macAddress}</GridItem>
          <GridItem colSpan={1} alignContent="center" alignItems="center">
            <Heading size="sm">{t('common.password')}</Heading>
          </GridItem>
          <GridItem colSpan={1}>
            <Button
              onClick={onTogglePassword}
              size="sm"
              rightIcon={isShowingPassword ? <EyeSlash /> : <Eye />}
              height={6}
            >
              {getPassword()}
            </Button>
            <Button onClick={onCopy} size="sm" ml={2} colorScheme="teal" height={6}>
              {hasCopied ? t('common.copied') : t('common.copy')}
            </Button>
          </GridItem>
          <GridItem colSpan={1} alignContent="center" alignItems="center">
            <Heading size="sm">{t('common.manufacturer')}:</Heading>
          </GridItem>
          <GridItem colSpan={1}>fs.com</GridItem>
          <GridItem colSpan={1} alignContent="center" alignItems="center">
            <Heading size="sm">{t('common.type')}:</Heading>
          </GridItem>
          <GridItem colSpan={1}>{getDevice.data?.deviceType}</GridItem>
          <GridItem colSpan={1} alignContent="center" alignItems="center">
            <Heading size="sm">{t('common.created')}:</Heading>
          </GridItem>
          <GridItem colSpan={1}>
            {getDevice.data?.createdTimestamp ? compactDate(getDevice.data.createdTimestamp) : '-'}
          </GridItem>
          <GridItem colSpan={1} alignContent="center" alignItems="center">
            <Heading size="sm">{t('common.modified')}:</Heading>
          </GridItem>
          <GridItem colSpan={1}>{getDevice.data?.modified ? compactDate(getDevice.data.modified) : '-'}</GridItem>
        </Grid>
        {/* <Heading my={2} size="md">
          {t('controller.provisioning.title')}
        </Heading> */}
        <Grid templateColumns="repeat(2, 1fr)" gap={0} w="100%">
          {/*<GridItem colSpan={1} alignContent="center" alignItems="center">*/}
          {/*  <Heading size="sm">{t('entities.one')}:</Heading>*/}
          {/*</GridItem>*/}
          {/*<GridItem colSpan={1}>*/}
          {/*  {getTag.data?.extendedInfo?.entity?.name ? (*/}
          {/*    <Link isExternal href={goToProvUi(`entity/${getTag.data?.entity}`)}>*/}
          {/*      {getTag.data?.extendedInfo?.entity?.name}*/}
          {/*    </Link>*/}
          {/*  ) : (*/}
          {/*    '-'*/}
          {/*  )}*/}
          {/*</GridItem>*/}
          <GridItem colSpan={1} alignContent="center" alignItems="center">
            <Heading size="sm">{t('venues.one')}:</Heading>
          </GridItem>
          <GridItem colSpan={1}>
            {getTag.data?.extendedInfo?.venue?.name ? (
              <Box
                as="span"
                onClick={handleClick(`venue/${getTag.data?.venue}`)}
                cursor="pointer"
                color="gray.800"
                _hover={{
                  color: 'blue.500',
                  textDecoration: 'underline',
                }}
                transition="all 0.2s"
              >
                {getTag.data.extendedInfo.venue.name}
              </Box>
            ) : (
              '-'
            )}
          </GridItem>
          {/* <GridItem colSpan={1}>
            {getTag.data?.extendedInfo?.venue?.name ? (
              <Link isExternal href={goToProvUi(`venue/${getTag.data?.venue}`)}>
                {getTag.data?.extendedInfo?.venue?.name}
              </Link>
            ) : (
              '-'
            )}
          </GridItem> */}
          {/*<GridItem colSpan={1} alignContent="center" alignItems="center">*/}
          {/*  <Heading size="sm">{t('subscribers.one')}:</Heading>*/}
          {/*</GridItem>*/}
          {/*<GridItem colSpan={1}>*/}
          {/*  {getTag.data?.subscriber !== undefined && getTag.data?.subscriber !== '' ? (*/}
          {/*    <Link isExternal href={goToProvUi(`subscriber/${getTag.data?.subscriber}`)}>*/}
          {/*      {getTag.data?.extendedInfo?.subscriber?.name ?? getTag.data.subscriber}*/}
          {/*    </Link>*/}
          {/*  ) : (*/}
          {/*    '-'*/}
          {/*  )}*/}
          {/*</GridItem>*/}
        </Grid>
      </CardBody>
    </Card>
  );
};

export default React.memo(DeviceDetails);
