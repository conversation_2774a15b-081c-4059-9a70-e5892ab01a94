import * as React from 'react';
import { Box, Center, HStack, Heading, Spacer, Spinner, useBoolean, useToast } from '@chakra-ui/react';
import { Formik } from 'formik';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import EntityDetailsForm from './Form';
import SaveButton from '@/modules-smb/components/Buttons/SaveButton';
import ToggleEditButton from '@/modules-smb/components/Buttons/ToggleEditButton';
import Card from '@/modules-smb/components/Card';
import CardBody from '@/modules-smb/components/Card/CardBody';
import CardHeader from '@/modules-smb/components/Card/CardHeader';
import { EntitySchema } from '@/modules-smb/constants/formSchemas';
import { useGetEntity, useUpdateEntity } from '@/modules-smb/hooks/Network/Entity';
import useFormModal from '@/modules-smb/hooks/useFormModal';
import useFormRef from '@/modules-smb/hooks/useFormRef';
import { AxiosError } from '@/modules-smb/models/Axios';
import { Entity } from '@/modules-smb/models/Entity';
import { VenueApiResponse } from '@/modules-smb/models/Venue';

type Props = {
  id: string;
};

const EntityBehaviorsCard = ({ id }: Props) => {
  const { t } = useTranslation();
  const toast = useToast();
  const [formKey, setFormKey] = React.useState(uuid());
  const { form, formRef } = useFormRef<VenueApiResponse & { __createLocation?: unknown }>();
  const updateEntity = useUpdateEntity({ id });
  const [isEditing, setEditing] = useBoolean();
  const modalInfo = useFormModal({
    isDirty: form.dirty,
  });
  const getEntity = useGetEntity({ id });

  React.useEffect(() => {
    setFormKey(uuid());
  }, [isEditing]);

  React.useEffect(() => {
    if (!modalInfo.isOpen) {
      setEditing.off();
    }
  }, [modalInfo.isOpen]);

  return (
    <Card>
      <CardHeader>
        <Heading size="md">Behaviors</Heading>
        <Spacer />
        <HStack>
          <SaveButton
            onClick={form.submitForm}
            isLoading={form.isSubmitting}
            isCompact
            isDisabled={!isEditing || !form.isValid || !form.dirty}
            hidden={!isEditing}
          />
          <ToggleEditButton
            isEditing={isEditing}
            toggleEdit={setEditing.toggle}
            isDisabled={getEntity.isFetching}
            isDirty={form.dirty}
          />
        </HStack>
      </CardHeader>
      <CardBody>
        <Box w="100%">
          {getEntity.data ? (
            <Formik
              innerRef={formRef}
              enableReinitialize
              key={formKey}
              initialValues={getEntity.data as Entity}
              validationSchema={EntitySchema(t)}
              onSubmit={({ deviceRules, sourceIP }, { setSubmitting, resetForm }) =>
                updateEntity.mutateAsync(
                  {
                    deviceRules,
                    sourceIP,
                  },
                  {
                    onSuccess: () => {
                      setSubmitting(false);
                      toast({
                        id: 'entity-update-success',
                        title: t('common.success'),
                        description: t('crud.success_update_obj', {
                          obj: t('entities.one'),
                        }),
                        status: 'success',
                        duration: 5000,
                        isClosable: true,
                        position: 'top-right',
                      });
                      resetForm();
                      setEditing.off();
                    },
                    onError: (e) => {
                      toast({
                        id: uuid(),
                        title: t('common.error'),
                        description: t('crud.error_update_obj', {
                          obj: t('entities.one'),
                          e: (e as AxiosError)?.response?.data?.ErrorDescription,
                        }),
                        status: 'error',
                        duration: 5000,
                        isClosable: true,
                        position: 'top-right',
                      });
                      setSubmitting(false);
                    },
                  },
                )
              }
            >
              <EntityDetailsForm isDisabled={!isEditing || getEntity.isFetching || updateEntity.isLoading} />
            </Formik>
          ) : (
            <Center my={12}>
              <Spinner size="xl" />
            </Center>
          )}
        </Box>
      </CardBody>
    </Card>
  );
};

export default EntityBehaviorsCard;
