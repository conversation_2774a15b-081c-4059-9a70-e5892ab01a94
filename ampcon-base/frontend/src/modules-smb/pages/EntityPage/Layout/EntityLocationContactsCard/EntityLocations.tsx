import * as React from 'react';
import { Box, Flex, Spacer, useDisclosure } from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import LocationActions from './LocationActions';
import CardBody from '@/modules-smb/components/Card/CardBody';
import LocationTable from '@/modules-smb/components/Tables/LocationTable';
import CreateLocationModal from '@/modules-smb/components/Tables/LocationTable/CreateLocationModal';
import EditLocationModal from '@/modules-smb/components/Tables/LocationTable/EditLocationModal';
import { useGetEntity } from '@/modules-smb/hooks/Network/Entity';
import { Location } from '@/modules-smb/models/Location';

type Props = {
  id: string;
};

const EntityLocations = ({ id }: Props) => {
  const queryClient = useQueryClient();
  const getEntity = useGetEntity({ id });
  const [location, setLocation] = React.useState<Location>();
  const { isOpen: isEditOpen, onOpen: openEdit, onClose: closeEdit } = useDisclosure();

  const openEditModal = (newLoc: Location) => {
    setLocation(newLoc);
    openEdit();
  };

  const refetchLocations = () => {
    queryClient.invalidateQueries(['get-locations-select']);
  };

  const actions = React.useCallback(
    (cell: { row: { original: Location } }) => (
      <LocationActions location={cell.row.original} refreshEntity={getEntity.refetch} openEditModal={openEditModal} />
    ),
    [],
  );

  return (
    <>
      <Flex px={2} pt={2}>
        <Spacer />
        <CreateLocationModal refresh={getEntity.refetch} entityId={getEntity.data?.id ?? ''} />
      </Flex>
      <CardBody p={4}>
        <Box w="100%">
          <LocationTable
            select={getEntity.data?.locations ?? []}
            actions={actions}
            ignoredColumns={['entity']}
            openDetailsModal={openEditModal}
          />
        </Box>
      </CardBody>
      <EditLocationModal isOpen={isEditOpen} onClose={closeEdit} location={location} refresh={refetchLocations} />
    </>
  );
};

export default EntityLocations;
