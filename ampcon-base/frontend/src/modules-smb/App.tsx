import React, { Suspense } from 'react';
import { Spinner } from '@chakra-ui/react';
import { QueryClientProvider, QueryClient } from '@tanstack/react-query';
import { HashRouter } from 'react-router-dom';
import { AuthProvider } from '@/modules-smb/contexts/AuthProvider';
import { ControllerSocketProvider } from '@/modules-smb/contexts/ControllerSocketProvider';
import { FirmwareSocketProvider } from '@/modules-smb/contexts/FirmwareSocketProvider';
import { ProvisioningSocketProvider } from '@/modules-smb/contexts/ProvisioningSocketProvider';
import { SecuritySocketProvider } from '@/modules-smb/contexts/SecuritySocketProvider';
import Router from 'router';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 0,
      refetchOnWindowFocus: false,
    },
  },
});

const App = () => {
  const storageToken = localStorage.getItem('access_token') ?? sessionStorage.getItem('access_token');

  return (
    <QueryClientProvider client={queryClient}>
      <HashRouter>
        <Suspense fallback={<Spinner />}>
          <AuthProvider token={storageToken !== null ? storageToken : undefined}>
            <SecuritySocketProvider>
              <FirmwareSocketProvider>
                <ProvisioningSocketProvider>
                  <ControllerSocketProvider>
                    <Router />
                  </ControllerSocketProvider>
                </ProvisioningSocketProvider>
              </FirmwareSocketProvider>
            </SecuritySocketProvider>
          </AuthProvider>
        </Suspense>
      </HashRouter>
    </QueryClientProvider>
  );
};

export default App;
