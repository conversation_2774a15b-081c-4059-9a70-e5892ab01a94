import * as React from "react";
import {Box, useBreakpoint, useDisclosure} from "@chakra-ui/react";
import {useNavigate} from "react-router-dom";
import {Route} from "@/modules-smb/models/Routes";
import EntityNavigationTree from "./Tree";
import {useGetVenues} from "@/modules-smb/hooks/Network/Venues";
import {useEntityFavorite} from "@/modules-smb/hooks/useEntityFavorite";

type Props = {
    route: Route;
    toggleSidebar: () => void;
};
const EntityNavigationButton = ({route, toggleSidebar}: Props) => {
    const breakpoint = useBreakpoint();
    const navigate = useNavigate();
    const modalProps = useDisclosure();

    const {isFavorite, onFavoriteClick, isLoading, getFirstVenueFavoriteId} = useEntityFavorite({
        id: "",
        type: "venue"
    });

    const getVenues = useGetVenues();

    let firstId = getFirstVenueFavoriteId();
    if (getVenues.data) {
        // 取第一个收藏的venue，没有选择默认第一个
        firstId = firstId == null ? getVenues.data[0].id : firstId;
        navigate(`/wireless/venue/${firstId}`);
    }

    const navigateTo = React.useCallback(
        (id: string, type: "venue" | "entity") => {
            console.log("navigateTo", type, id);
            navigate(`/wireless/${type}/${id}`);
            if (breakpoint === "base" || breakpoint === "sm" || breakpoint === "md") toggleSidebar();
        },
        [breakpoint]
    );

    return (
        <>
            <Box>
                <EntityNavigationTree isModalOpen={modalProps.isOpen} treeRoot={undefined} navigateTo={navigateTo} />
            </Box>
        </>
    );
};

export default EntityNavigationButton;
