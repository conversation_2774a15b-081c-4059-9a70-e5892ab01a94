import { useCallback, useMemo } from 'react';
import { message } from 'antd';
import { useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useTranslation } from 'react-i18next';

interface Props {
  objName: string;
  operationType: 'update' | 'delete' | 'create' | 'blink' | 'reboot';
  refresh?: () => void;
  onClose?: () => void;
  queryToInvalidate?: string[];
}

const defaultProps = {
  refresh: () => {},
  onClose: () => {},
  queryToInvalidate: null,
};

const useMutationResult = ({ objName, operationType, refresh, onClose, queryToInvalidate }: Props) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const successDescription = () => {
    switch (operationType) {
      case 'update':
        return t('crud.success_update_obj', { obj: objName });
      case 'delete':
        return t('crud.success_delete_obj', { obj: objName });
      case 'blink':
        return t('commands.blink_success', { obj: objName });
      case 'reboot':
        return t('commands.reboot_success', { obj: objName });
      default:
        return t('crud.success_create_obj', { obj: objName });
    }
  };

  const errorDescription = (e: AxiosError) => {
    const errorMsg = e?.response?.data?.ErrorDescription || t('common.unknown_error');
    switch (operationType) {
      case 'update':
        return t('crud.error_update_obj', { obj: objName, e: errorMsg });
      case 'delete':
        return t('crud.error_delete_obj', { obj: objName, e: errorMsg });
      case 'blink':
        return t('commands.blink_error', { obj: objName, e: errorMsg });
      case 'reboot':
        return t('commands.reboot_error', { obj: objName, e: errorMsg });
      default:
        return t('crud.error_create_obj', { obj: objName, e: errorMsg });
    }
  };

  const onSuccess = useCallback(
    ({ setSubmitting, resetForm } = { setSubmitting: null, resetForm: null }) => {
      if (refresh) refresh();
      if (setSubmitting) setSubmitting(false);
      if (resetForm) resetForm();
      message.success(successDescription());
      if (onClose) onClose();
      if (queryToInvalidate) queryClient.invalidateQueries(queryToInvalidate);
    },
    [queryToInvalidate],
  );

  const onError = useCallback((e, { setSubmitting } = { setSubmitting: null }) => {
    message.error(errorDescription(e));
    if (setSubmitting) setSubmitting(false);
  }, []);

  return useMemo(
    () => ({
      onSuccess,
      onError,
    }),
    [],
  );
};

useMutationResult.defaultProps = defaultProps;
export default useMutationResult;
