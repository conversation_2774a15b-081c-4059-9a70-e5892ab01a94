import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { axiosGw } from '@/modules-smb/utils/axiosInstances';
import { AxiosError } from '@/modules-smb/models/Axios';
import { PageInfo } from '@/modules-smb/models/Table';

export type BlacklistDevice = {
  author: string;
  created: number;
  reason: string;
  serialNumber: string;
  siteId?: string;
};

const getBlacklistCount = async (siteId?: string) => {
  try {
    const url = siteId ? `blacklist?countOnly=true&siteId=${siteId}` : 'blacklist?countOnly=true';
    const response = await axiosGw.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching getBlacklistCount:", error);
    throw error;
  }
};

export const useGetBlacklistCount = ({ enabled, siteId }: { enabled: boolean; siteId?: string }) =>
  useQuery(['blacklist', 'count', siteId], () => getBlacklistCount(siteId), {
    enabled,
  });

const getBlacklistDevices = async (limit: number, offset: number, siteId?: string) => {
  try {
    const baseUrl = 'blacklist';
    const params = new URLSearchParams();
    
    if (limit) params.append('limit', limit.toString());
    if (offset) params.append('offset', offset.toString());
    if (siteId) params.append('siteId', siteId);
    
    const url = `${baseUrl}?${params.toString()}`;
    const response = await axiosGw.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching getBlacklistDevices:", error);
    throw error;
  }
};

export const useGetBlacklistDevices = ({
  pageInfo,
  enabled,
  siteId,
  onError,
}: {
  pageInfo?: PageInfo;
  enabled: boolean;
  siteId?: string;
  onError?: (e: AxiosError) => void;
}) => {
  const offset = pageInfo?.limit !== undefined ? pageInfo.limit * pageInfo.index : 0;

  return useQuery(
    ['blacklist', 'all', { limit: pageInfo?.limit, offset, siteId }],
    () => getBlacklistDevices(pageInfo?.limit || 0, offset, siteId),
    {
      keepPreviousData: true,
      enabled: enabled && pageInfo !== undefined,
      staleTime: 30000,
      onError,
    },
  );
};

const deleteBlacklist = async (serialNumber: string, siteId?: string) => {
  const url = siteId ? `blacklist/${serialNumber}?siteId=${siteId}` : `blacklist/${serialNumber}`;
  return axiosGw.delete(url);
};

export const useDeleteBlacklistDevice = () => {
  const queryClient = useQueryClient();

  return useMutation(
    (data: { serialNumber: string; siteId?: string }) => 
      deleteBlacklist(data.serialNumber, data.siteId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['blacklist']);
      },
    }
  );
};

const updateBlacklist = async (data: { serialNumber: string; reason: string; siteId?: string }) => {
  const url = data.siteId ? `blacklist/${data.serialNumber}?siteId=${data.siteId}` : `blacklist/${data.serialNumber}`;
  return axiosGw.put(url, data).then((response: { data: BlacklistDevice }) => response.data);
};

export const useUpdateBlacklist = () => {
  const queryClient = useQueryClient();

  return useMutation(updateBlacklist, {
    onSuccess: (data) => {
      const queries = queryClient.getQueriesData(['blacklist', 'all']);

      for (const [key, devices] of queries) {
        const val = devices as undefined | { devices: BlacklistDevice[] };
        const indexToUpdate = val?.devices?.findIndex((device) => device.serialNumber === data.serialNumber);
        if (indexToUpdate !== undefined && indexToUpdate >= 0) {
          const newArray = [...(val?.devices ?? [])];
          newArray[indexToUpdate] = data;
          queryClient.setQueryData(key, { devices: newArray });
        }
      }
    },
  });
};

const addBlacklist = async (data: { serialNumber: string; reason: string; siteId?: string }) => {
  const url = data.siteId ? `blacklist/${data.serialNumber}?siteId=${data.siteId}` : `blacklist/${data.serialNumber}`;
  return axiosGw.post(url, data).then((response: { data: BlacklistDevice }) => response.data);
};

export const useCreateBlacklist = () => {
  const queryClient = useQueryClient();

  return useMutation(addBlacklist, {
    onSuccess: () => {
      queryClient.invalidateQueries(['blacklist', 'count']);
      queryClient.invalidateQueries(['blacklist', 'all']);
    },
  });
};