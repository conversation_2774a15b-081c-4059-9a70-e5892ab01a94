import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { message } from 'antd';
import { AxiosError } from '@/modules-smb/models/Axios';
import { axiosAnalytics } from '@/modules-smb/utils/axiosInstances';

export const useGetAnalyticsBoards = ({ venueId }: { venueId: string | number }) => {
    const { t } = useTranslation();

    return useQuery(
        ['get-analytics-boards', venueId],
        async () => {
            const { data } = await axiosAnalytics.get(
                `boards?forVenue=${venueId}`
            );
            return data;
        },
        {
            enabled: venueId !== undefined && venueId !== null,
            onError: (e: AxiosError) => {
                message.error(
                    t('crud.error_fetching_obj', {
                        obj: t('analytics.board'),
                        e: e.response?.data?.ErrorDescription || t('common.error'),
                    }),
                    5
                );
            },
        }
    );
};