import { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useSiteStore } from "@/modules-smb/Wireless/components/SiteContext";
import { useEntityFavorite } from "@/modules-smb/Wireless/hooks/useEntityFavorite";

export const useSiteSelection = (hasAllSitesOption = false) => {
  const {
    selectedSiteId,
    setSelectedSiteId,
    isAllSitesSelected,
    setAllSites,
    defaultSiteId,
    resetFromOtherPage
  } = useSiteStore();

  const { getFirstVenueFavoriteId } = useEntityFavorite();
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const initializeSiteSelection = () => {
      const hash = window.location.hash.substring(1);
      const pathname = location.pathname;

      // 处理从其他页面切换过来的情况(Devicesh和Inventory的all sites相互切换)
      if (isAllSitesSelected && hash !== "all") {
        navigate(`${pathname}#all`, { replace: true });
        return;
      }

      if (!isAllSitesSelected && selectedSiteId && hash !== selectedSiteId) {
        navigate(`${pathname}#${selectedSiteId}`, { replace: true });
        return;
      }

      // 逻辑1：有当前选择（包括all）
      if (hash || selectedSiteId) {
        if (hasAllSitesOption && hash === "all") {
          setAllSites();
          return;
        }

        if (/^\d+$/.test(hash || selectedSiteId)) {
          setSelectedSiteId(hash || selectedSiteId);
          return;
        }
      }

      // 逻辑2：没有当前选择
      resetFromOtherPage();

      // 逻辑2.1：有收藏站点
      const favId = getFirstVenueFavoriteId();
      if (favId) {
        setSelectedSiteId(favId);
        navigate(`${location.pathname}#${favId}`, { replace: true });
        return;
      }

      // 逻辑2.2：无收藏站点，使用default
      if (defaultSiteId) {
        setSelectedSiteId(defaultSiteId);
        navigate(`${location.pathname}#${defaultSiteId}`, { replace: true });
      }
    };

    initializeSiteSelection();
  }, [hasAllSitesOption]);

  const handleSiteChange = (value: string | string[]) => {
    const id = Array.isArray(value) ? value[0] : value;

    if (hasAllSitesOption && id === "all") {
      setAllSites();
      navigate(`${location.pathname}#all`);
    } else {
      setSelectedSiteId(id);
      navigate(`${location.pathname}#${id}`);
    }
  };

  return {
    selectedSiteId,
    isAllSitesSelected,
    handleSiteChange,
    displaySiteId: isAllSitesSelected ? null : selectedSiteId
  };
};