import { useEntityFavorite } from "@/modules-smb/Wireless/hooks/useEntityFavorite";
import React, { useState } from "react";
import { useGetVenues } from "@/modules-smb/hooks/Network/Venues";
type Option = { value: string; label: string; type: "venue" | "entity" };
const useGetEntityFavorite = () => {
    const { getFirstVenueFavoriteId } = useEntityFavorite();
    const getVenues = useGetVenues();

    const getHashId = () => {
        const hash = window.location.hash;
        if (hash) {
            return hash.substring(1);
        }
        return null;
    };

    let myFavoriteId: string = getFirstVenueFavoriteId();
    let defaultValue: string = myFavoriteId;
    if (getVenues.data) {
        defaultValue = defaultValue == null ? getVenues.data[0].id : defaultValue;
        defaultValue = getHashId() || defaultValue;
    }
    return {
        defaultValue
    }
}
export default useGetEntityFavorite;
