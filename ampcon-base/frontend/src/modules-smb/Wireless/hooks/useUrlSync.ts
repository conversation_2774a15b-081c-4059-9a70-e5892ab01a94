import { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useSiteStore } from "@/modules-smb/Wireless/components/SiteContext";

export const useUrlSync = () => {
  const { selectedSiteId, isAllSitesSelected } = useSiteStore();
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const syncUrlWithSiteState = () => {
      const hash = window.location.hash.substring(1);
      
      if (isAllSitesSelected && hash !== "all") {
        navigate(`${location.pathname}#all`, { replace: true });
      } else if (!isAllSitesSelected && selectedSiteId && hash !== selectedSiteId) {
        navigate(`${location.pathname}#${selectedSiteId}`, { replace: true });
      }
    };

    syncUrlWithSiteState();
  }, [ selectedSiteId, isAllSitesSelected]);
};