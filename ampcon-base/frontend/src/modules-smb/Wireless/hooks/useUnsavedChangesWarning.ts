import { useEffect, useRef } from "react";
import { useBlocker } from "react-router-dom";
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";

export function useBlockNavigation(when: boolean) {
    const blocker = useBlocker(when);
    const isPromptOpen = useRef(false); // 防止多次弹窗

    useEffect(() => {
        if (blocker.state === "blocked" && !isPromptOpen.current) {
            isPromptOpen.current = true;

            confirmModalAction(
                "You have unsaved changes. If you leave or switch to another site, your changes will be lost. Do you want to continue?",
                () => {
                    blocker.proceed();
                    isPromptOpen.current = false;
                },
                () => {
                    blocker.reset();
                    isPromptOpen.current = false;
                }
            );
        }
    }, [blocker]);
}



