import { request } from "@/utils/common/request";

const baseUrl = "/ampcon/wireless/configure";

export function createEthernetPort({
  site_id,
  port,
  mac,
  network_type,  
  vlan_or_dhcp_name = null,
  vlan_tag = 1
}) {
  return request({
    url: `${baseUrl}/ethernet_ports`,
    method: "POST",
    data: {
      site_id,
      port,
      mac,
      network_type,  
      vlan_or_dhcp_name,
      vlan_tag
    }
  });
}


export function getEthernetPortList({
  siteId,
  pageNum,
  pageSize,
  sortBy,
  sortType
}) {
  return request({
    url: `${baseUrl}/ethernet_ports`,
    method: "GET",
    params: {
      siteId,
      pageNum,
      pageSize,
      sortBy,
      sortType
    }
  }).then(response => {
    return response;
  });;
}


export function deleteEthernetPort({
  id
}) {
  return request({
    url: `${baseUrl}/ethernet_ports`,
    method: "DELETE",
    data: {
      id
    }
  });
}
