import { request } from "@/utils/common/request";
import axios from "axios";
// import { OptimizeHistoryItem, RRMConfig, FailedDevice, SchedulerConfig, RRMApiRequest, RRMApiResponse } from '../pages/Entities/RRMOptimize/types';

const baseUrl = "/ampcon/wireless";

// 查询RRM执行记录接口
export async function fetchRRMTaskRecords(params) {
  return request({
    url: `${baseUrl}/rrm/task/record`,
    method: "GET",
    params: {
      siteId: params.siteId,
      sortBy: params.sortBy || 'create_time',
      sortType: params.sortType || 'desc',
      pageNum: params.pageNum,
      pageSize: params.pageSize,
    },
  });
}

// 查询RRM执行结果接口
export async function fetchRRMTaskResult(params) {
  const res = await request({
    url: `${baseUrl}/rrm/task/result`,
    method: "GET",
    params: {
      siteId: params.siteId,
      taskId: params.taskId,
      sortBy: params.sortBy,
      sortType: params.sortType,
      pageNum: params.pageNum,
      pageSize: params.pageSize,
    },
  });

  // 字段兼容处理，确保device_name和device_mode存在
  if (res && Array.isArray(res.info)) {
    res.info = res.info.map(item => ({
      ...item,
      device_name: typeof item.device_name === 'string' ? item.device_name : '',
      device_mode: typeof item.device_mode === 'string' ? item.device_mode : '',
    }));
  }
  return res;
}


/**
 * 执行立即优化
 */
export async function runOptimizeNow(config) {

  return request({
    url: `/smb/owrrm/api/v1/startRRM`,
    method: "POST",
    data: config,
  });

}

// mock模拟数据 临时ip
// const instance = axios.create({
//   baseURL: 'http://localhost:3210',
// });

// 获取优化历史（分页）
// export async function fetchOptimizeHistory(params: { page: number; pageSize: number; }) {
//   const res = await instance.get('/history', {
//     params: {
//       _page: params.page,
//       _limit: params.pageSize,
//       _sort: 'triggerTime',
//       _order: 'desc',
//     },
//   });
//   return {
//     list: res.data,
//     total: Number(res.headers['x-total-count']) || 0,
//   };
// }

// 获取优化历史（分页，兼容无x-total-count头）
// export async function fetchOptimizeHistory(params: { page: number; pageSize: number; _sort?: string; _order?: string }) {
//   // 1. 获取总数
//   const totalRes = await instance.get('/history');
//   const total = Array.isArray(totalRes.data) ? totalRes.data.length : 0;

//   // 2. 获取当前页数据
//   const res = await instance.get('/history', {
//     params: {
//       _page: params.page,
//       _limit: params.pageSize,
//       _sort: params._sort || 'triggerTime',
//       _order: params._order || 'desc',
//     },
//   });

//   return {
//     list: res.data,
//     total,
//   };
// }

// 执行立即优化（mock无实际效果）
// export async function runOptimizeNow(config: Partial<RRMConfig>) {
//   return instance.post('/optimize', config);
// }

// 获取算法配置
// export async function fetchRRMConfig() {
//   return instance.get<RRMConfig>('/config');
// }

// 保存算法配置
// export async function saveRRMConfig(config: RRMConfig) {
//   return instance.put('/config', config);
// }

// 获取失败设备列表
// export async function fetchFailedDevices(historyId: string) {
//   return instance.get<FailedDevice[]>(`/failedDevices`, { params: { historyId } });
// }

// 获取定时任务配置
// export async function fetchSchedulerConfig() {
//   return instance.get<SchedulerConfig>('/scheduler');
// }

// // 保存定时任务配置
// export async function saveSchedulerConfig(config: SchedulerConfig) {
//   return instance.put('/scheduler', config);
// }

