.text-title {
    width: 105px;
    height: 29px;
    font-family: Lato;
    font-size: 24px;
    font-weight: bold;
    line-height: normal;
    letter-spacing: normal;
    /* 一级文字颜色 */
    /* 样式描述：#212529 */
    color: #212519;
}

.site-select-container {
    display: flex;
    align-items: center;
    gap: 32px;
    margin-top: 32px;
    margin-bottom: 32px;
}

.site-title {
    width: 24px;
    height: 17px;
    font-family: Lato;
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    /* 一级文字颜色 */
    /* 样式描述：#212529 */
    color: #212519;
}

.site-select {
    width: 280px;
    height: 36px;
    border-radius: 2px;
    background: #FFFFFF;
    box-sizing: border-box;
    border: 1px #DADCE1;
}

// 折叠面板标题样式
.collapse-title {
    font-weight: 600;
    font-size: 16px;
}

/* 折叠面板样式限定在当前组件内 */
.collapse-modal {

    /* 统计信息折叠面板样式 */
    .ant-collapse-item {
        border: none !important;
        /* 创建面板之间的间距 */
        margin-top: 24px !important;
    }

    /* 移除第一个面板的顶部间距 */
    .ant-collapse-item:first-child {
        margin-top: 0 !important;
    }

    .ant-collapse-header {
        padding-top: 10px !important;
        padding-bottom: 10px !important;
        height: 40px !important;
        /* 标题栏背景色 */
        background: #F8FAFB;
    }

    .ant-collapse-content-box {
        padding: 24px 0 0 16px !important;
    }
}

.input-search-hover{
    .ant-list-item:hover {
      background-color: #f5f5f5;
    }
}