.wirelessForm {
  padding: 0 6px;
  .ant-form-item-label > label {
    width: 200px;
    text-align: left;
    // word-wrap: break-word;
    // white-space: break-spaces;
    overflow: hidden;
  }
  .ant-form-item-control {
    .ant-input-affix-wrapper,
    .ant-input,
    .ant-select,
    .ant-picker,
    .ant-select-selector {
      width: 280px;
    }
    .ant-select-show-search {
      width: auto !important;

    }
  }
  .ant-form-item-control {
    .ant-input-number {
      width: 140px;
    }
  }
  .header1 {
    margin-top: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
  }
  .header2 {
    margin-top: 8px;
    padding-top: 32px;
    margin-bottom: 20px;
    border-top: 1px solid #eee;
    font-size: 16px;
  }
  .ant-collapse-header {
    margin-top: 32px;
    margin-bottom: 24px;
    height: 56px;
    padding: 0 24px;
    background: #F8FAFB;
    align-items: center !important;
  }
  .anticon-right {
    color: #B8BFBF !important;
  }
}
.wirelessModal {
  .ant-modal-body {
    margin-right: 6px;
    padding-left: 18px !important;
  }
}

.input-item-error-number .ant-form-item-explain-error {
  max-width: 140px;   /* 跟随 input 宽度 */
  white-space: normal;
  word-break: break-word;
}
.input-item-error-other .ant-form-item-explain-error {
  max-width: 280px;   /* 跟随 input 宽度 */
  white-space: normal;
  word-break: break-word;
}

//时间组件Now按钮
.custom-range-picker-dropdown {
  .ant-picker-footer {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 4px 12px !important;
    width: 100% !important;
    height: auto !important;
  }

  .ant-picker-footer-extra {
    border: none !important;
  }

  .ant-picker-now-btn {
    padding: 0 !important;
    height: auto !important;
  }

  .ant-picker-time-panel-btn {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }

  .ant-picker-time-panel-btn:hover {
    background: rgba(0, 0, 0, 0.04) !important;
  }
}