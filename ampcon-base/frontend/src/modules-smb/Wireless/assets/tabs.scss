.div-main {
  width: 100%;
  height: 100%;
  flex-direction: column;
  padding: 0;
  margin: 0;
}

.div-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 16px 8px 0;
  background-color: transparent;
  border-bottom: none;
  height: 50px;
  line-height: 50px;
}

.div-tabs {
  flex: 1;
  background: #fff;
  margin-top: 10px;
  display: flex;
  width: 100%;
  
  .ant-tabs {
    width: 100%;
  }

  .ant-tabs-content {
    overflow: auto !important;
  }
  
  .ant-table-cell {
    white-space: normal;
    word-break: break-word;
    overflow-wrap: break-word;
  }

  .chakra-tabs__tablist {
    border-bottom: none !important;
    padding: 0;
    margin-bottom: 20px;
  }

  .chakra-tabs__tab {
    color: #929A9EFF !important;
    cursor: pointer;
    padding: 5px;
    margin-right: 20px;
  }

  .css-ph9xv1[aria-selected=true],
  .css-ph9xv1[data-selected] {
    color: #14C9BBFF !important;
    border-bottom-width: 3px;
  }
  .css-ph9xv1:hover {
    color: #14C9BB !important;
  }
}

.div-header> :first-child {
  margin-right: auto;
}