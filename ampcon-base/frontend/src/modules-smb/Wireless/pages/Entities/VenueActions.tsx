import React, { useState } from "react";
import { <PERSON>u, Dropdown, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, message } from "antd";
import Icon from "@ant-design/icons";
import { CheckCircleFilled, CloseCircleFilled } from "@ant-design/icons";
import VenueFirmwareUpgradeModal from "./VenueFirmwareUpgradeModal";
import { useRebootVenueDevices, useUpdateVenueDevices } from "@/modules-smb/Wireless/hooks/Venues";
import setttingSvg from "../../assets/Entities/venue_setting.svg?react";
import { useGetVenue } from "@/modules-smb/Wireless/hooks/Venues";
import "./index.scss";

const VenueActions = ({ venueId, isDisabled }: { venueId: string; isDisabled?: boolean }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const rebootDevices = useRebootVenueDevices({ id: venueId });
  const updateDevices = useUpdateVenueDevices({ id: venueId });
  const getVenue = useGetVenue({ id: venueId });
  const deviceCount = getVenue.data?.boards?.length || 0;

  const showActionMessage = (
    type: "success" | "error",
    action: "Reboot" | "Update",
    count: number
  ) => {
    const icon = type === "success" ? (
      <CheckCircleFilled style={{ color: "#2BC174 ", marginRight: 8 }} />
    ) : (
      <CloseCircleFilled style={{ color: "#FF5145", marginRight: 8 }} />
    );

    const text =
      type === "success"
        ? `Successfully ${action} ${count} Devices`
        : `Failed ${action} ${count} Devices`;

    message.open({
      content: (
        <span style={{ color: type === "success" ? "#2BC174 " : "#FF5145" }}>
          {icon}
          {text}
        </span>
      ),
      duration: 3,
    });
  };

  const handleClick = async ({ key }: { key: string }) => {
    try {
      if (key === "reboot") {
        const response = await rebootDevices.mutateAsync();
        const count = response.data.serialNumbers?.length || 0;
        showActionMessage("success", "Reboot", count);
      } else if (key === "update") {
        const response = await updateDevices.mutateAsync();
        const count = response.data.serialNumbers?.length || 0;
        showActionMessage("success", "Update", count);
      } else if (key === "upgrade") {
        setModalVisible(true);
      }
    } catch (error: any) {
      const failedCount = error.response?.data?.failedCount || 0;
      if (key === "reboot") {
        showActionMessage("error", "Reboot", failedCount);
      } else if (key === "update") {
        showActionMessage("error", "Update", failedCount);
      }
    }
  };

  const menu = (
    <Menu onClick={handleClick}>
      <Menu.Item key="reboot">Reboot All Devices</Menu.Item>
      <Menu.Item key="update">Update All Device Configurations</Menu.Item>
      {/* <Menu.Item key="upgrade">Upgrade All Devices</Menu.Item> */}
    </Menu>
  );

  return (
    <>
      <Dropdown overlay={menu} trigger={["click"]} placement="bottomRight" disabled={!venueId}>
        <Tooltip title="Actions" placement="bottom">
          <Button
            icon={<Icon component={setttingSvg} />}
            type="text"
            style={{ width: 40, height: 40, borderRadius: 4 }}
            className="actions-button"
          />
        </Tooltip>
      </Dropdown>
      <VenueFirmwareUpgradeModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        venueId={venueId}
      />
    </>
  );
};

export default VenueActions;