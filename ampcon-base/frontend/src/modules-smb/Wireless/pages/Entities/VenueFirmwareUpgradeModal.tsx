import React, { useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON>, Button, Modal, Spin, Tabs, Typography } from "antd";
import { useTranslation } from "react-i18next";
import FormattedDate from "@/modules-smb/components/FormattedDate";
import { useGetVenueUpgradeAvailableFirmware, useUpgradeVenueDevices } from "@/modules-smb/Wireless/hooks/Venues";

const { TabPane } = Tabs;

const VenueFirmwareUpgradeModal = ({ visible, onClose, venueId }: {
  visible: boolean;
  onClose: () => void;
  venueId: string;
}) => {
  const { t } = useTranslation();
  const getAvailableFirmware = useGetVenueUpgradeAvailableFirmware({ id: venueId, enabled: visible });
  const upgrade = useUpgradeVenueDevices();
  const [selectedRevision, setSelectedRevision] = useState<string>();

  useEffect(() => {
    if (visible) {
      setSelectedRevision(undefined);
      getAvailableFirmware.refetch();
    }
  }, [visible]);

  const handleUpgrade = () => {
    if (selectedRevision) {
      upgrade.mutateAsync(
        { revision: selectedRevision, id: venueId },
        { onSuccess: onClose }
      );
    }
  };

  const renderList = (list: any[]) => (
    <ul style={{ listStyle: "none", padding: 0 }}>
      {list
        .sort((a, b) => b.date - a.date)
        .map(item => (
          <li
            key={item.revision}
            onClick={() => setSelectedRevision(item.revision)}
            style={{
              padding: 8,
              border: "1px solid #eee",
              borderRadius: 4,
              marginBottom: 6,
              background: selectedRevision === item.revision ? "#f0f0f0" : "#fff",
              cursor: "pointer",
            }}
          >
            <FormattedDate date={item.date} />
            <div>{item.revision}</div>
          </li>
        ))}
    </ul>
  );

  const content = useMemo(() => {
    if (getAvailableFirmware.isFetching) {
      return <Spin />;
    }

    if (getAvailableFirmware.isError) {
      return <Alert message="Error" description="Failed to load firmware list" type="error" />;
    }

    const data = getAvailableFirmware.data;

    return (
      <>
        <Typography.Text strong>{t("venues.upgrade_options_available")}</Typography.Text>
        <Tabs>
          <TabPane tab="Official Releases" key="official">
            {renderList(data?.releases || [])}
          </TabPane>
          <TabPane tab="Release Candidates" key="rc">
            {renderList(data?.releasesCandidates || [])}
          </TabPane>
          <TabPane tab="Dev Releases" key="dev">
            {renderList(data?.developmentReleases || [])}
          </TabPane>
        </Tabs>
      </>
    );
  }, [getAvailableFirmware, selectedRevision]);

  return (
    <Modal
      title={t("venues.upgrade_all_devices")}
      visible={visible}
      onCancel={onClose}
      onOk={handleUpgrade}
      okButtonProps={{ disabled: !selectedRevision, loading: upgrade.isLoading }}
    >
      {content}
    </Modal>
  );
};

export default VenueFirmwareUpgradeModal;
