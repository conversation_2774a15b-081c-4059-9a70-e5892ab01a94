import React ,{useEffect}from 'react';
import { Row, Col } from 'antd';
import StringField from '@/modules-smb/Wireless/components/FormFields/StringField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import useFastField from '@/modules-smb/hooks/useFastField';
import { useTranslation } from 'react-i18next';
import { useFormikContext } from 'formik';
import {getSubSectionDefaults} from './servicesConstants';
const Lldp = () => {
  const { value: lldpEnabled } = useFastField({ name: 'configuration.services.lldp.enabled' });
  const { value: lldp } = useFastField({ name: 'configuration.services.lldp' });
  const {t}=useTranslation();
    
  const { values, setFieldValue, errors } = useFormikContext<any>();
  useEffect(() => {
     if (lldpEnabled) {
      if(!lldp||(lldp&&Object.keys(lldp).length<=1&&lldp.enabled===true)){
      const defaultLldpConfig = getSubSectionDefaults(t,'lldp');
       setFieldValue('configuration.services.lldp', {
     ...defaultLldpConfig,
     describe: "auto",
     location: "auto",
     enabled: true,
     });
     }
     }else {
   // 可选：关闭时清除 lldp 字段
   setFieldValue('configuration.services.lldp', undefined);
     }
   }, [lldpEnabled]);
  return(
    <>
    <Row gutter={[20, 0]} style={{ marginBottom: 0, width: '100%' }}>
      <Col>
        <ToggleField
          name="configuration.services.lldp.enabled"
          label="LLDP"
        />
        {lldpEnabled && (
          <>
           <StringField
          name="configuration.services.lldp.describe"
          label="Describe"
          definitionKey="service.lldp.describe"
          // isDisabled={!editing}
          isRequired
          w={280}
        />
        <StringField
          name="configuration.services.lldp.location"
          label="Location"
          definitionKey="service.lldp.location"
          // isDisabled={!editing}
          isRequired
          w={280}
        />
          </>
        )}
      </Col>
    </Row>
    </>
  );
};

export default React.memo(Lldp);
