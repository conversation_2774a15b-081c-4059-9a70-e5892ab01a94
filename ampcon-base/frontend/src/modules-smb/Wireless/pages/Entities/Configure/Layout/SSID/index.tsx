import React, { useState, useEffect } from "react";
import { Table, Button, Space, message, Switch } from "antd";
import { addSvg } from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";
import type { ColumnsType } from 'antd/es/table';
import SSIDForm from './SSIDForm/Form';
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";
import { getWirelessConfigureList, deleteWirelessConfigure, getWirelessConfigureDetail, enableConfigure, updateConfigureGroup } from "@/modules-smb/Wireless/apis/configure_api";
// import { fetchLables } from '@/modules-smb/Wireless/apis/lable';
// import GroupSelect from '@/modules-smb/Wireless/components/GroupSelect';


interface Props {
  siteId?: number;
}

const SSID: React.FC<Props> = ({ siteId = 0 }) => {
  // url中获取siteId
  if (window.location.hash) {
    const hash = window.location.hash.replace('#', '');
    if (/^\d+$/.test(hash)) {
      siteId = parseInt(hash, 10);
    }
  }
  const [data, setData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [editingResource, setEditingResource] = useState<any>(null);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
  const [sorter, setSorter] = useState<{ field?: string, order?: string }>({});
  // group列表
  // const [groupOptions, setGroupOptions] = useState<{ label: string; value: string }[]>([]);

  // List
  const fetchList = (page = 1, pageSize = 10, sorterParam = sorter) => {
    setIsLoading(true);
    const sortFields = sorterParam.field
      ? [{ field: sorterParam.field, order: sorterParam.order }]
      : [];
    getWirelessConfigureList(siteId, page, pageSize, [], sortFields)
      .then(res => {
        if (res?.status !== 200) {
          message.error(res?.info);
          return;
        }
        setData(res?.info || []);
        setPagination({
          current: page,
          pageSize,
          total: res?.total || 0,
        });
      })
      .catch(() => message.error('Failed to fetch list'))
      .finally(() => setIsLoading(false));
  };

  useEffect(() => {
    fetchList();

    // fetchLables({ siteId })
    //   .then(res => {
    //     if (res?.status === 200 && Array.isArray(res.info)) {
    //       setGroupOptions(res.info.map((g: any) => ({ label: g.name, value: g.name })));
    //     } else {
    //       setGroupOptions([]);
    //     }
    //   })
    //   .catch(() => setGroupOptions([]))
  }, [siteId]);

  // Delete
  const handleDelete = (record: any) => {
    confirmModalAction(
      `Are you sure you want to delete?`,
      () => {
        deleteWirelessConfigure({ id: record.key })
          .then(res => {
            if (res?.status !== 200) {
              message.error(res?.info);
              return;
            }
            message.success('Deleted successfully');
            fetchList(pagination.current, pagination.pageSize);
          })
          .catch(() => message.error('Delete failed'));
      }
    );
  };

  // Edit
  const handleEdit = (record: any) => {
    getWirelessConfigureDetail(record.key)
      .then(res => {
        if (res?.status !== 200) {
          message.error(res?.info);
          return;
        }
        // Parse ssid_configure in detail
        let detail = res?.info || null;
        if (detail && typeof detail.ssid_configure === 'string') {
          try {
            detail.ssid_configure = JSON.parse(detail.ssid_configure);
          } catch (e) {
            detail.ssid_configure = {};
          }
        }
        setEditingResource(detail);
        setModalOpen(true);
      })
      .catch(() => message.error('Failed to fetch detail'));
  };

  const handleCreate = () => {
    setEditingResource(null);
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setEditingResource(null);
  };

  // WLAN Status开关切换
  const handleStatusChange = (checked: boolean, record: any) => {
    enableConfigure({ id: record.key, is_enable: checked ? 1 : 0 })
      .then(res => {
        if (res?.status !== 200) {
          message.error(res?.info);
          return;
        }
        message.success('Status updated');
        setData(prevData => prevData.map(item =>
          item.id === record.key ? { ...item, is_enable: checked ? 1 : 0 } : item
        ));
      })
      .catch(() => message.error('Failed to update status'));
  };

  const handleCopy = (record: any) => {
    getWirelessConfigureDetail(record.key)
      .then(res => {
        if (res?.status !== 200) {
          message.error(res?.info);
          return;
        }
        let detail = res?.info || null;
        if (detail && typeof detail.ssid_configure === 'string') {
          detail.name = detail.name + '_Copy';
          try {
            detail.ssid_configure = JSON.parse(detail.ssid_configure);
            detail.ssid_configure.name = detail.ssid_configure.name + '_Copy';
            delete detail.id;
          } catch (e) {
            detail.ssid_configure = {};
          }
        }
        setEditingResource(detail);
        setModalOpen(true);
      })
      .catch(() => message.error('Failed to fetch detail'));
  };

  // AP Label确认方法
  // const handleGroupSelectOk = (record: any, labels_name: string[]) => {
  //   updateConfigureGroup({ id: record.key, labels_name: labels_name })
  //     .then(res => {
  //       if (res?.status !== 200) {
  //         message.error(res?.info);
  //         return false;
  //       }
  //       message.success('Label updated');
  //       setData(list => list.map(item => item.id === record.key ? { ...item, labels_name } : item));
  //     })
  //     .catch(() => message.error('Failed to update group'));
  //   return true
  // };

  const tableData = (data || []).map((item: any) => {
    return {
      key: item.id,
      name: item.name,
      security: item.security,
      radio: item.radio,
      vlan_or_dhcp_name: item.network_type === 2 ? item.vlan_or_dhcp_name : '-',
      labels_name: item.labels_name,
      is_enable: item.is_enable,
      originResource: item,
    };
  });

  const columns: ColumnsType<any> = [
    { title: 'SSID Name', dataIndex: 'name', key: 'name', sorter: true },
    { title: 'Security', dataIndex: 'security', key: 'security', sorter: true },
    { title: 'Radio', dataIndex: 'radio', key: 'radio', sorter: true },
    { title: 'VLAN', dataIndex: 'vlan_or_dhcp_name', key: 'vlan_or_dhcp_name', sorter: false },
    {
      title: 'AP Label',
      dataIndex: 'labels_name',
      key: 'labels_name',
      sorter: true,
      // render: (labels_name: string[], record: any) => (
      //   <GroupSelect
      //     value={labels_name}
      //     groupOptions={groupOptions}
      //     handleOk={(val: string[]) => handleGroupSelectOk(record, val)}
      //   />
      // ),
      render: (labels_name: string[] | string) => {
        if (Array.isArray(labels_name)) {
          return labels_name.join(', ');
        }
        return labels_name || '';
      },
    },
    {
      title: 'WLAN Status',
      dataIndex: 'is_enable',
      key: 'is_enable',
      sorter: true,
      render: (is_enable: number, record: any) => (
        <Switch
          checked={is_enable === 1}
          onChange={checked => handleStatusChange(checked, record)}
        />
      ),
    },
    {
      title: 'Operation',
      key: 'operation',
      render: (_: any, record: any) => (
        <Space size={24}>
          <Button style={{ padding: 0 }} type="link" onClick={() => handleEdit(record)}>
            Edit
          </Button>
          <Button style={{ padding: 0 }} type="link" onClick={() => handleCopy(record)}>
            Copy
          </Button>
          <Button style={{ padding: 0 }} type="link" onClick={() => handleDelete(record)}>
            Delete
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" onClick={handleCreate}>
          <Icon component={addSvg} />
          Create New SSID
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={tableData}
        loading={isLoading}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          showSizeChanger: true,
          showQuickJumper: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          onChange: (page, pageSize) => fetchList(page, pageSize),
        }}
        rowKey="key"
        bordered
        onChange={(pagination, filters, sorterObj) => {
          let order = '';
          let field = '';
          if (!Array.isArray(sorterObj)) {
            if (sorterObj.order === 'ascend') order = 'asc';
            else if (sorterObj.order === 'descend') order = 'desc';
            if (typeof sorterObj.field === 'string') field = sorterObj.field;
            else field = '';
          }
          setSorter({ field, order });
          fetchList(pagination.current, pagination.pageSize, { field, order });
        }}
      />
      <SSIDForm
        key={editingResource?.id || String(modalOpen)}
        modalOpen={modalOpen}
        onClose={handleCloseModal}
        refresh={() => fetchList(pagination.current, pagination.pageSize)}
        resource={editingResource}
        isDisabled={false}
        siteId={siteId}
      />
    </div>
  );
};

export default SSID;