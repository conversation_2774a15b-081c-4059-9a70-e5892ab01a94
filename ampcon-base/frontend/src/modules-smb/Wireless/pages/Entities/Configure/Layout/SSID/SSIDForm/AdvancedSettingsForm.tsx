import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, Input, InputNumber, Select, Switch, Row, Col, Checkbox } from 'antd';
import CaptivePortalForm from './CaptivePortalForm/Form';
import { LabelTip } from '@/modules-smb/Wireless/components/LabelTip';
import { validateMac } from '@/modules-smb/Wireless/utils/util';

interface Props {
  resource?: any;
  siteId?: number;
}

const AdvancedSettingsForm: React.FC<Props> = ({ resource, siteId }) => {
  const { t } = useTranslation();
  const initial = resource || {};
  const [rateLimitEnabled, setRateLimitEnabled] = useState(!!initial['rate-limit']);
  // const [rrmEnabled, setRrmEnabled] = useState(!!initial.rrm);
  const [aclEnabled, setAclEnabled] = useState(!!initial['access-control-list']);
  const [roamingState, setRoamingState] = useState(
    initial.roaming && initial.roaming['message-exchange'] ? 'custom' : (initial['roaming'] === true ? 'on' : 'off')
  );
  const form = Form.useFormInstance();
  const protoWatch = Form.useWatch(['encryption', 'proto'], form);
  const personProtos = ['none', 'owe', 'owe-transition'];

  // 当 proto 属于 Open 类型时，自动清除 roaming 并关闭 FT Roaming UI
  React.useEffect(() => {
    const isOpen = protoWatch && personProtos.includes(protoWatch);
    if (isOpen) {
      const roamingVal = form.getFieldValue('roaming');
      if (roamingVal !== undefined && roamingVal !== null && roamingVal !== false) {
        form.setFieldsValue({ roaming: undefined });
      }
      if (roamingState !== 'off') setRoamingState('off');
    }
  }, [protoWatch]);
  
  // wifi-steering联动
  const handleWifiSteeringChange = (checked: boolean, form: any) => {
    const services = form.getFieldValue('services') || [];
    if (checked) {
      if (!services.includes('wifi-steering')) {
        form.setFieldValue('services', [...services, 'wifi-steering']);
      }
    } else {
      form.setFieldValue('services', services.filter((s: string) => s !== 'wifi-steering'));
    }
  };
  const renderWifiSteeringSwitch = (form: any) => {
    const services = form.getFieldValue('services') || [];
    return (
      <Switch
        checked={services.includes('wifi-steering')}
        onChange={checked => handleWifiSteeringChange(checked, form)}
      />
    );
  };

  return (
    <Form.Item noStyle shouldUpdate>
      {form => {
        // authMode变化时联动services
        const handleAuthModeChange = (authMode: string) => {
          const services = form.getFieldValue('services') || [];
          if (authMode !== 'off') {
            if (!services.includes('captive')) {
              form.setFieldValue('services', [...services, 'captive']);
            }
          } else {
            if (services.includes('captive')) {
              form.setFieldValue('services', services.filter((s: string) => s !== 'captive'));
            }
          }
        };
        return (
          <>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name={['hidden-ssid']}
                  label="Hidden SSID"
                  tooltip={LabelTip('interface.ssid.hidden-ssid')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Wi-Fi Steering" valuePropName="checked">
                  {renderWifiSteeringSwitch(form)}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name={['services']}
                  label="Services"
                  style={{ display: 'none' }}
                >
                  <Select mode="multiple" options={[]} />
                </Form.Item>
              </Col>
              <Col span={12}></Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name={['maximum-clients']}
                  label="Maximum Clients"
                  tooltip={LabelTip('interface.ssid.maximum-clients')}
                  rules={[
                    { required: true, message: t('form.required') },
                    { type: 'number', min: 1, max: 65535, message: 'maximum-clients must be 1 ~ 65535' }
                  ]}
                >
                  <InputNumber />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['fils-discovery-interval']}
                  label="Fils-Discovery-Interval"
                  tooltip={LabelTip('interface.ssid.fils-discovery-interval')}
                  rules={[
                    { required: true, message: t('form.required') },
                    { type: 'number', min: 1, max: 20, message: 'fils-discovery-interval must be 1 ~ 20' }
                  ]}
                >
                  <InputNumber />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name={['dtim-period']}
                  label="Dtim-Period"
                  rules={[
                    { required: true, message: t('form.required') },
                    { type: 'number', min: 1, max: 255, message: 'dtim-period must be 1 ~ 255' }
                  ]}
                >
                  <InputNumber/>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['isolate-clients']}
                  label="Isolate Clients"
                  tooltip={LabelTip('interface.ssid.isolate-clients')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name={['power-save']}
                  label="Power Save"
                  tooltip={LabelTip('interface.ssid.power-save')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['broadcast-time']}
                  label="Broadcast Time"
                  tooltip={LabelTip('interface.ssid.broadcast-time')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name={['unicast-conversion']}
                  label="Unicast Conversion"
                  tooltip={LabelTip('interface.ssid.unicast-conversion')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['proxy-arp']}
                  label="Proxy ARP"
                  tooltip={LabelTip('interface.ssid.proxy-arp')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name={['disassoc-low-ack']}
                  label="Disassoc Low Ack"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
            <h3 className='header2'>Roaming</h3>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name={['encryption', 'key-caching']}
                  label="Key-Caching"
                  tooltip={LabelTip('interface.ssid.encryption.key-caching')}
                  valuePropName="checked"
                >
                  <Switch defaultChecked={true}/>
                </Form.Item>
              </Col>
            </Row>
            {!protoWatch || (protoWatch && !personProtos.includes(protoWatch)) ? (
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item label="FT Roaming">
                    <Select
                      value={roamingState}
                      onChange={val => {
                        setRoamingState(val);
                        // // Roaming与RRM联动
                        // if (val === 'custom' || val === 'on') {
                        //   setRrmEnabled(true);
                        //   const rrm = form.getFieldValue('rrm') || {};
                        //   if (!rrm['neighbor-reporting']) {
                        //     form.setFieldValue(['rrm', 'neighbor-reporting'], true);
                        //   }
                        //   if (!rrm['stationary-ap']) {
                        //     form.setFieldValue(['rrm', 'stationary-ap'], true);
                        //   }
                        // } else {
                        //   setRrmEnabled(false);
                        // }
                        if (val === 'custom') {
                          const roaming = form.getFieldValue('roaming') || {};
                          if (!roaming['message-exchange']) {
                            form.setFieldValue(['roaming', 'message-exchange'], 'ds');
                          }
                        } else if (val === 'on') {
                          form.setFieldValue('roaming', true);
                        }
                      }}
                    >
                      <Select.Option value="on">Auto</Select.Option>
                      <Select.Option value="custom">Custom</Select.Option>
                      <Select.Option value="off">Off</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}></Col>
              </Row>
            ) : null}
            {roamingState === 'on' && (
              <Form.Item
                name={['roaming']}
                style={{ display: 'none' }}
              >
                <Switch />
              </Form.Item>
            )}
            {roamingState === 'custom' && (
              <>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      name={['roaming', 'message-exchange']}
                      label="Message Exchange"
                      tooltip={LabelTip('interface.ssid.roaming.message-exchange')}
                      rules={[
                        { required: true, message: t('form.required') }
                      ]}
                    >
                      <Select placeholder="message-exchange">
                        <Select.Option value="air">air</Select.Option>
                        <Select.Option value="ds">ds</Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={['roaming', 'generate-psk']}
                      label="Generate PSK"
                      tooltip={LabelTip('interface.ssid.roaming.generate-psk')}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      name={['roaming', 'domain-identifier']}
                      label="Domain Identifier"
                      tooltip={LabelTip('interface.ssid.roaming.domain-identifier')}
                      rules={[
                        {
                          validator: (_: any, value: any) => {
                            if (value === undefined || value === null || value === '') return Promise.resolve();
                            const hexPattern = /^[0-9A-Fa-f]{4}$/;
                            return hexPattern.test(String(value))
                              ? Promise.resolve()
                              : Promise.reject('domain-identifier must be exactly 4 hexadecimal characters');
                          }
                        }
                      ]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                  {/* <Col span={12}>
                    <Form.Item name={['roaming', 'pmk-r0-key-holder']} label="pmk-r0-key-holder" tooltip={LabelTip('interface.ssid.roaming.pmk-r0-key-holder')}>
                      <Input placeholder="pmk-r0-key-holder" />
                    </Form.Item>
                  </Col> */}
                </Row>
                {/* <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item name={['roaming', 'pmk-r1-key-holder']} label="pmk-r1-key-holder" tooltip={LabelTip('interface.ssid.roaming.pmk-r1-key-holder')}>
                      <Input placeholder="pmk-r1-key-holder" />
                    </Form.Item>
                  </Col>
                  <Col span={12}></Col>
                </Row> */}
              </>
            )}
            {/* <h3 className='header2'>RRM</h3>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item label="rrm">
                  <Switch
                    checked={rrmEnabled}
                    onChange={setRrmEnabled}
                  />
                </Form.Item>
              </Col>
              <Col span={12}></Col>
            </Row>
            {rrmEnabled && (
              <>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item name={['rrm', 'neighbor-reporting']} label="neighbor-reporting" tooltip={LabelTip('interface.ssid.rrm.neighbor-reporting')} valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item name={['rrm', 'lci']} label="lci" tooltip={LabelTip('interface.ssid.rrm.lci')}>
                      <Input placeholder="lci" />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item name={['rrm', 'civic-location']} label="civic-location" tooltip={LabelTip('interface.ssid.rrm.civic-location')}>
                      <Input placeholder="civic-location" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item name={['rrm', 'ftm-responder']} label="ftm-responder" tooltip={LabelTip('interface.ssid.rrm.ftm-responder')} valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item name={['rrm', 'stationary-ap']} label="stationary-ap" tooltip={LabelTip('interface.ssid.rrm.stationary-ap')} valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={12}></Col>
                </Row>
              </>
            )} */}
            
            <div style={{ display: 'none' }}>
              <Form.Item name={['rrm', 'neighbor-reporting']} label="Neighbor Reporting" tooltip={LabelTip('interface.ssid.rrm.neighbor-reporting')} valuePropName="checked">
                <Switch />
              </Form.Item>
              <Form.Item name={['rrm', 'ftm-responder']} label="FTM Responder" tooltip={LabelTip('interface.ssid.rrm.ftm-responder')} valuePropName="checked">
                <Switch />
              </Form.Item>
              <Form.Item name={['rrm', 'stationary-ap']} label="Stationary AP" tooltip={LabelTip('interface.ssid.rrm.stationary-ap')} valuePropName="checked">
                <Switch />
              </Form.Item>
            </div>

            <h3 className='header2'>Rate Limit</h3>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item label="Rate Limit">
                  <Switch
                    checked={rateLimitEnabled}
                    onChange={setRateLimitEnabled}
                  />
                </Form.Item>
              </Col>
              <Col span={12}></Col>
            </Row>
            {rateLimitEnabled && (
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name={['rate-limit', 'ingress-rate']}
                    label="Ingress Rate"
                    tooltip={LabelTip('interface.ssid.rate-limit.ingress-rate')}
                    rules={[
                      { required: true, message: t('form.required') },
                      { type: 'number', min: 0, max: 65535, message: 'ingress-rate must be less than 65535' },
                    ]}
                  >
                    <InputNumber min={0} addonAfter="Mb/s" style={{ width: 280 }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['rate-limit', 'egress-rate']}
                    label="Egress Rate"
                    tooltip={LabelTip('interface.ssid.rate-limit.egress-rate')}
                    rules={[
                      { required: true, message: t('form.required') },
                      { type: 'number', min: 0, max: 65535, message: 'egress-rate must be less than 65535' },
                    ]}
                  >
                    <InputNumber min={0} addonAfter="Mb/s" style={{ width: 280 }} />
                  </Form.Item>
                </Col>
              </Row>
            )}
            <h3 className='header2'>Access Control List</h3>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item label="Access-Control-List">
                  <Switch
                    checked={aclEnabled}
                    onChange={setAclEnabled}
                  />
                </Form.Item>
              </Col>
              <Col span={12}></Col>
            </Row>
            {aclEnabled && (
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name={['access-control-list', 'mode']}
                    label="Mode"
                    rules={[
                      { required: true, message: t('form.required') }
                    ]}
                  >
                    <Select>
                      <Select.Option value="allow">Allow</Select.Option>
                      <Select.Option value="deny">Deny</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['access-control-list', 'mac-address']}
                    label="MAC Address"
                    validateTrigger={['onChange', 'onBlur']}
                    rules={[
                      { required: true, message: t('form.required') },
                      { validator: (_, value) => validateMac(value) }
                    ]}
                  >
                    <Select
                      mode="tags"
                      tokenSeparators={[',']}
                    />
                  </Form.Item>
                </Col>
              </Row>
            )}
            <CaptivePortalForm resource={resource} siteId={siteId} onAuthModeChange={handleAuthModeChange} />
          </>
        );
      }}
    </Form.Item>
  );
};

export default AdvancedSettingsForm;
