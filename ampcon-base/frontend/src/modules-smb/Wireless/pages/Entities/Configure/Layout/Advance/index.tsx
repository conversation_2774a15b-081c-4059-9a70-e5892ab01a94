import React, { useEffect, useMemo, useState,useImperativeHandle,forwardRef,useRef } from 'react';
import { Collapse, Button, message,Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';

import SystemSettingsFields from './System/SystemSettingsFields';
import DhcpService from './DhcpService';
import Ethernet from './Ethernet';
import Services from './Services';
import Metrics from './Metrics';
import IpAddress from './IpAddress';

import { SERVICES_SCHEMA } from './Services/servicesConstants';
import { METRICS_SCHEMA } from './Metrics/metricsConstants';
// import { ADVANCE_SCHEMA } from './advanceConstants';
import { UNIT_SCHEMA} from './System/unitConstants';
import {INTERFACE_ETHERNET_SCHEMA} from './ethernetConstants';
import {INTERFACE_IP_ADDRESS_SCHEMA} from './IpAddress/interfacesConstants'
import {useGetVenue} from '@/modules-smb/hooks/Network/Venues.ts'
import { useGetConfiguration } from "@/modules-smb/hooks/Network/Configurations";
import { updateConfigureGeneral } from "@/modules-smb/Wireless/apis/configure_api";
import ConfigurationFieldExplanation from "@/modules-smb/Wireless/components/FormFields/ConfigurationFieldExplanation" 
import { useBlockNavigation } from "@/modules-smb/Wireless/hooks/useUnsavedChangesWarning.ts";
import './collapse.css';
import { isEqual } from 'lodash';
import "@/modules-smb/Wireless/assets/wireless.scss";
interface AdvanceProps {
  onDirtyChange?: (isDirty: boolean) => void;
}

const Advance= forwardRef(({ onDirtyChange }: AdvanceProps, ref) => {
  const { t } = useTranslation();
  const [siteId, setSiteId] = useState<number | null>(null);
  const formikRef = useRef<any>(null);
  const [hasUserModified, setHasUserModified] = useState(false);//手动追踪是否修改过
  useEffect(() => {
    const hash = location.hash.replace("#", "");
    const parsed = Number(hash);
    // 只有当站点ID真正改变时才执行重置操作
  if (parsed !== siteId) {
    setSiteId(parsed);
    // 重置所有状态
    setHasUserModified(false);
    setChildDirtyStates({});
    // 如果formikRef存在，重置表单
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
  }
  }, [location.hash]);

  // 控制哪些子模块被启用（仿 ConfigurationSectionsCard）
  const defaultModules = ['system', 'ethernet', 'services', 'metrics','DHCP Service','IP address'];
  const [activeModules, setActiveModules] = useState<string[]>([]);
  const [initialValues, setInitialValues] = useState<any | null>(null);
  const configId = siteId != null ? `${siteId}-advance` : undefined;
  const { data: configurations = [], refetch, isLoading } = useGetConfiguration({ id: configId });//获取配置
  // 脏状态管理
  const [childDirtyStates, setChildDirtyStates] = useState<Record<string, boolean>>({});
  // const [parentDirty, setParentDirty] = useState(false);
  // 计算总脏状态
  // 修改判断是否为脏状态的逻辑
const isModified = useMemo(() => {
  // 只有当initialValues存在时才判断
  if (!initialValues || !formikRef.current) return false;
  
  return hasUserModified || Object.values(childDirtyStates).some(dirty => dirty);
}, [hasUserModified, childDirtyStates, initialValues]);
  // 通知父组件脏状态变化
  useEffect(() => {
    if (onDirtyChange) {
      onDirtyChange(isModified);
    }
  }, [isModified, onDirtyChange]);
  // useBlockNavigation(isModified);
  // // 更新子组件脏状态的函数
  // const updateChildDirtyState = (key: string, isDirty: boolean) => {
  //   setChildDirtyStates(prev => ({ ...prev, [key]: isDirty }));
  // };

   useEffect(() => {
    if (siteId) refetch();
  }, [siteId, refetch]);
  useEffect(() => {
    setHasUserModified(false); // 切换站点时重置修改状态
    setChildDirtyStates({});
  }, [siteId]);
  // 初始化激活模块
  useEffect(() => {
    setActiveModules(defaultModules);
  }, []);

  useEffect(() => {
    const raw=configurations?.configuration;
    if (!raw || !Array.isArray(raw)) return;

    const merged = { configuration: {} };

    for (const item of raw) {
      const parsed = JSON.parse(item.configuration || '{}');
      if (item.name === 'System' && parsed.unit) merged.configuration.unit = parsed.unit;
        if (item.name === 'Services' && parsed.services) {
          merged.configuration.services = parsed.services;
          const servicesPart=['ssh','http','lldp','ntp','log','rtty','wifi-steering','online-check'];
        // 循环处理所有服务项
      servicesPart.forEach(service => {
        if (merged.configuration.services?.[service] !== undefined) {
          merged.configuration.services[service] = {
            ...merged.configuration.services[service],
            enabled: true
          };
        }
      });
    }
      if (item.name === 'Metrics' && parsed.metrics) merged.configuration.metrics = parsed.metrics;
      if (item.name === 'IP address' && parsed['IP address']) {
        merged.configuration['IP address'] = parsed['IP address']
        const ipAddressPart=['vlan','ipv6'];
        // 循环处理所有服务项
        ipAddressPart.forEach(service => {
        if (merged.configuration['IP address']?.[service] !== undefined) {
          merged.configuration['IP address'][service] = {
            ...merged.configuration['IP address'][service],
            enabled: true
          };
        }
      });
        
      };
    }

    setInitialValues(merged);
    // 当配置数据加载完成后，强制重置表单
  if (formikRef.current) {
    formikRef.current.resetForm({ values: merged });
  }
  // 重置修改状态
  setHasUserModified(false);
  setChildDirtyStates({});
  }, [configurations?.configuration]);

  // 拼接校验 schema
  const combinedSchema = useMemo(() => {
    let schema = UNIT_SCHEMA(t);
    schema=schema.concat(SERVICES_SCHEMA(t)).concat(METRICS_SCHEMA(t)).concat(INTERFACE_IP_ADDRESS_SCHEMA(t));
    return schema;
  }, [t]);
  // console.log('initialValues',initialValues);
   if (!initialValues) return null; // 或 loading spinner
  
  

  const systemItems = [
    {
      key: '1',
      label: <span className="collapse-title">System</span>,
      children: <SystemSettingsFields editing={true} />,
    },
     {
      key: '2',
      label: <div>
        <span className="collapse-title">IP Address</span>
        <ConfigurationFieldExplanation definitionKey="interface.ipAddress" />
        </div>,
      children: <IpAddress/>,
    },
     {
      key: '3',
      label: <div>
        <span className="collapse-title">DHCP Service</span>
         <ConfigurationFieldExplanation definitionKey="interface.DhcpService" />
        </div>,
      children: <DhcpService />,
    },
    {
      key: '4',
      label: <span className="collapse-title">Manage Ethernet Ports</span>,
      children: <Ethernet />,
    },
    {
      key: '5',
      label: <span className="collapse-title">Services</span>,
      children: <Services />,
    },
    // {
    //   key: '6',
    //   label: <span style={{ fontWeight: 600, fontSize: 16 }}>Metrics</span>,
    //   children: <Metrics />,
    // },
  ]
  .filter((item) => {
    if (item.key === '1') return activeModules.includes('system');
    if (item.key === '2') return activeModules.includes('IP address');
    if (item.key === '3') return activeModules.includes('DHCP Service');
    if (item.key === '4') return activeModules.includes('ethernet');
    if (item.key === '5') return activeModules.includes('services');
    if (item.key === '6') return activeModules.includes('metrics');
    return true;
  });

   const handleApply = async (values: any) => {
    const result: any[] = [];
          // 工具函数：递归去除 enabled 字段
          const removeEnabledField = (obj: any): any => {
            if (Array.isArray(obj)) {
              return obj.map(removeEnabledField);
            } else if (typeof obj === 'object' && obj !== null) {
              //去除service中的enable=false时候的字段
              if (
                Object.keys(obj).length === 1 &&
                obj.hasOwnProperty('enable') &&
                (obj.enable === false || obj.enable === 'false')
              ) {
                return undefined; // 整个对象丢掉
              }
              const newObj: any = {};
              for (const key in obj) {
                if (key === 'enabled') continue; // 过滤掉 enabled
                const value = removeEnabledField(obj[key]); // 递归处理
                if(value !== undefined){
                  newObj[key] = value;
                }
              }
              return newObj;
            }
            return obj; // 原始值
          };
  const cleanedServices = removeEnabledField(values.configuration.services);
  const cleanedMetrics = removeEnabledField(values.configuration.metrics);
  const cleanIpAddress = removeEnabledField(values.configuration['IP address']);
  if (activeModules.includes('services') && values.configuration.services) {
    result.push({
      configuration: JSON.stringify({ services: cleanedServices }),
      name: 'Services',
    });
  }

  if (activeModules.includes('metrics') && values.configuration.metrics) {
    result.push({
      configuration: JSON.stringify({ metrics: cleanedMetrics }),
      name: 'Metrics',
    });
  }

  if (activeModules.includes('system') && values.configuration.unit) {
    result.push({
      configuration: JSON.stringify({ unit: values.configuration.unit }),
      name: 'System',
    });
  }

  if (activeModules.includes('IP address') && values.configuration['IP address']) {
    result.push({
      configuration: JSON.stringify({ 'IP address': cleanIpAddress }),
      name: 'IP address',
    });
  }

   try {
      await updateConfigureGeneral(configId!, siteId, JSON.stringify(result));
      message.success("Successfully applied advance configuration");
       // 提交成功后重置修改状态
      setHasUserModified(false);
      setChildDirtyStates({});
    } catch (err) {
      console.error(err);
      message.error("Failed to apply settings.");
      return;
    }

    const res = await refetch();
    if (res.error) {
      message.warning("Settings saved, but failed to refresh config.");
    }
   };

  return (
    <div style={{ width: '100%', overflowX: 'auto' }}>
      <Formik innerRef={formikRef} initialValues={initialValues} enableReinitialize onSubmit={()=>{}} validationSchema={combinedSchema}
        >
        {({ resetForm,values,validateForm, setTouched,dirty  }) => {
          useEffect(() => {
            // 初始渲染时不触发（此时values与initialValues一致）
            if (formikRef.current && initialValues) {
              // 对比当前值与初始值是否不同
              const isDifferent = !isEqual(values, initialValues);
              // 只要有过一次不同，就永久设为true
              if (isDifferent) {
                setHasUserModified(true);
              }
            }
          }, [values, initialValues]); // 监听值变化
          const markAllTouched = (obj: any) => {
          const touched: any = {};
            for (const key in obj) {
              if (typeof obj[key] === "object" && obj[key] !== null) {
                touched[key] = markAllTouched(obj[key]);
              } else {
                touched[key] = true;
              }
            }
            return touched;
          };
           // 暴露重置方法给父组件
  useImperativeHandle(ref, () => ({
    reset: () => {
        resetForm();
        // 重置所有修改状态
        setHasUserModified(false);
        // 重置所有子组件的脏状态
        setChildDirtyStates({});
        message.info('Changes have been reset.');
      },
    apply:() => {
        validateForm().then(errors => {
        if (Object.keys(errors).length === 0) {
            handleApply(values);
            // 应用成功后重置脏状态
            setChildDirtyStates({});
        } else {
          // console.log('Validation errors:', errors);
          // 关键：标记所有字段为 touched，让错误立刻显示
        setTouched(markAllTouched(values));
        message.error(`Please correct the errors before saving`);
        }
      });
      }
    }));
          
          return(
          <>
            <Collapse
              size="large"
              items={systemItems}
              defaultActiveKey={[]}
              expandIconPosition="right"
              className="no-collapse-border"
              style={{ marginTop: 12, marginBottom: 0, border: 'none' ,background: '#ffffff'}}
            />
          </>
          );
        }}
      </Formik>
    </div>
  );
});

export default Advance;
