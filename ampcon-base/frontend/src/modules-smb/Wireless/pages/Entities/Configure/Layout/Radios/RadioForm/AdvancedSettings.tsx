import React, { useMemo, useEffect } from "react";
import { Form, Row, Col, Switch, Select, InputNumber } from "antd";
import { useTranslation } from "react-i18next";
import _ from "lodash";
import { LabelTip } from '@/modules-smb/Wireless/components/LabelTip';

const { Option } = Select;

type Props = {
  namePrefix: string;
  values: any;
  setFieldValue: (path: string[], val: any) => void;
  errors?: any;
  touched?: any;
};

const itemLayout = { labelCol: { style: { width: 150 } }, wrapperCol: { style: { width: 300 } } };

const AdvancedSettings = ({ namePrefix, values, setFieldValue }: Props) => {
  const { t } = useTranslation();

  const band = _.get(values, [namePrefix, "band"]);
  const legacyRates = _.get(values, [namePrefix, "legacy-rates"]);

  const beaconRateAndMulticastOptions = useMemo(() => {
    // const noneValue = "__none__";
    const commonOptions = [
      // { value: noneValue, label: t("common.none") },
      { value: 1000, label: "1000" },
      { value: 2000, label: "2000" },
      { value: 5500, label: "5500" },
      { value: 6000, label: "6000" },
      { value: 9000, label: "9000" },
      { value: 11000, label: "11000" },
      { value: 12000, label: "12000" },
      { value: 18000, label: "18000" },
      { value: 24000, label: "24000" },
      { value: 36000, label: "36000" },
      { value: 48000, label: "48000" },
      { value: 54000, label: "54000" }
    ];
    const highBandOptions = [
      // { value: noneValue, label: t("common.none") },
      { value: 6000, label: "6000" },
      { value: 9000, label: "9000" },
      { value: 12000, label: "12000" },
      { value: 18000, label: "18000" },
      { value: 24000, label: "24000" },
      { value: 36000, label: "36000" },
      { value: 48000, label: "48000" },
      { value: 54000, label: "54000" }
    ];

    return band === "2G" && legacyRates ? commonOptions : highBandOptions;
  }, [band, legacyRates, t]);

  // he和rates，空对象，设置为undifined
  useEffect(() => {
    const he = _.get(values, [namePrefix, "he"]);
    if (he && typeof he === "object" && Object.keys(he).length === 0) {
      setFieldValue([namePrefix, "he"], undefined);
    }

    const rates = _.get(values, [namePrefix, "rates"]);
    if (rates && typeof rates === "object" && Object.keys(rates).length === 0) {
      setFieldValue([namePrefix, "rates"], undefined);
    }
  }, [values, namePrefix, setFieldValue]);

  return (
    <>
      <Row gutter={64}>
        <Col span={10}>
          <Form.Item
            label="Beacon-Rate"
            tooltip={LabelTip('radio.rates.beacon')}
            {...itemLayout}
          >
            <Select
              style={{ width: 280 }}
              value={_.get(values, [namePrefix, "rates", "beacon"], 6000)}
              onChange={(val) => {
                setFieldValue([namePrefix, "rates", "beacon"], val);
              }}
              placeholder={t("common.none")}
            >
              {beaconRateAndMulticastOptions.map((opt) => (
                <Select.Option key={opt.value} value={opt.value}>
                  {opt.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={10}>
          <Form.Item
            label="Beacon-Interval"
            required
            {...itemLayout}
            validateStatus={
              _.get(values, [namePrefix, "beacon-interval"], 100) <= 14
                ? "error"
                : _.get(values, [namePrefix, "beacon-interval"], 100) >= 65535
                  ? "error"
                  : ""
            }
            help={
              _.get(values, [namePrefix, "beacon-interval"], 100) <= 14
                ? 'Beacon-Interval must be greater than 14'
                : _.get(values, [namePrefix, "beacon-interval"], 100) >= 65535
                  ? 'Beacon-Interval must be less than 65535'
                  : ''
            }
          >
            <InputNumber
              value={_.get(values, [namePrefix, "beacon-interval"], 100)}
              onChange={(val) => setFieldValue([namePrefix, "beacon-interval"], val ?? 100)}
              style={{ width: 140 }}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={64}>
        <Col span={10}>
          <Form.Item
            label="Multicast"
            tooltip={LabelTip('radio.rates.multicast')}
            {...itemLayout}
            style={{ marginBottom: 4 }}
          >
            <Select
              style={{ width: 280 }}
              value={_.get(values, [namePrefix, "rates", "multicast"], 24000)}
              onChange={(val) => {
                setFieldValue([namePrefix, "rates", "multicast"], val);
              }}
              placeholder="Select multicast rate"
            >
              {beaconRateAndMulticastOptions.map((opt) => (
                <Option key={opt.value} value={opt.value}>
                  {opt.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={10}>
          <Form.Item
            label="BSS-Color"
            tooltip={LabelTip('radio.he.bss-color')}
            {...itemLayout}
            style={{ marginBottom: 4 }}
            className="inline-error-fixed"
            required
            validateStatus={
              typeof _.get(values, [namePrefix, "he", "bss-color"]) === "number" &&
                _.get(values, [namePrefix, "he", "bss-color"]) < 0
                ? "error"
                : ""
            }
            help={
              typeof _.get(values, [namePrefix, "he", "bss-color"]) === "number" &&
                _.get(values, [namePrefix, "he", "bss-color"]) < 0
                ? "BSS-Color cannot be negative"
                : ""
            }
          >
            <InputNumber
              max={63}
              style={{ width: 140 }}
              value={_.get(values, [namePrefix, "he", "bss-color"], 0)}
              onChange={(val) => {
                setFieldValue([namePrefix, "he", "bss-color"], val ?? 0);
              }}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={64}>
        {/* 2g不显示 Allow-DFS选项*/}
        {(band !== "2G") && (
          <Col span={10}>
            <Form.Item
              label="Allow-DFS"
              tooltip={LabelTip('radio.allow-dfs')}
              {...itemLayout}
              style={{ marginTop: 20, marginBottom: 4 }}>
              <Switch
                checked={_.get(values, [namePrefix, "allow-dfs"])}
                onChange={(val) => setFieldValue([namePrefix, "allow-dfs"], val)}
              />
            </Form.Item>
          </Col>
        )}
      </Row>
    </>
  );
};

export default AdvancedSettings;