import React from 'react';
import Http from './Http';
import Ssh from './Ssh';
import Mdns from './Mdns';
import Igmp from './Igmp';
import Lldp from './Lldp';
import Ntp from './Ntp';
import Log from './Log';
import Rtty from './Rtty';
import WifiSteering from './WifiSteering';
import OnlineCheck from './OnlineCheck';
import { Divider } from 'antd';

const Services= () => {

  return (
    // <div style={{ display: 'grid' }}>
    //   <Row left={<Ssh />} right={<Http />} />
    //   <Row left={<Mdns />} right={<Igmp />} />
    //   <Row left={<Lldp />} right={<Ntp />} />
    //   <Row left={<Log />} right={<Rtty />} />
    //   <Row left={<WifiSteering />} right={<OnlineCheck />} />
    //   <Divider style={{ marginBottom: '16px' }} />
    // </div>
   <>
   <div style={{display:"flex"}}>
    <Ssh/>
    <Divider type="vertical" style={{ height: "auto", margin: "0 90px" }} />
    <Http/>
   </div>
   {/* <Divider style={{marginTop: 0}}/> */}
   <div style={{display:"flex"}}>
    <Mdns/>
    <Divider type="vertical" style={{ height: "auto", margin: "0 90px" }} />
    <Igmp/>
   </div>
   {/* <Divider style={{marginTop: 0}}/> */}
   <div style={{display:"flex"}}>
    <Lldp/>
    <Divider type="vertical" style={{ height: "auto", margin: "0 90px" }} />
    <Ntp/>
   </div>
   {/* <Divider style={{marginTop: 0}}/> */}
   <div style={{display:"flex"}}>
    <Log/>
    <Divider type="vertical" style={{ height: "auto", margin: "0 90px" }} />
    <Rtty/>
   </div>
   {/* <Divider style={{marginTop: 0}}/> */}
   <div style={{display:"flex"}}>
    <WifiSteering/>
    <Divider type="vertical" style={{ height: "auto", margin: "0 90px" }} />
    <OnlineCheck/>
   </div>
   </>
  );
};

export default Services;
