import React,{useEffect} from 'react';
import {Col,Row} from 'antd';
import NumberField from '@/modules-smb/Wireless/components/FormFields/NumberField';
import StringField from '@/modules-smb/Wireless/components/FormFields/StringField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import useFastField from '@/modules-smb/hooks/useFastField';
import { useTranslation } from 'react-i18next';
import { useFormikContext } from 'formik';
import {getSubSectionDefaults} from './servicesConstants';

const Rtty = () => {
  const { value: rttyEnabled } = useFastField({ name: 'configuration.services.rtty.enabled' });
  const { value: rtty } = useFastField({ name: 'configuration.services.rtty' });
  const {t}=useTranslation();
  
  const { values, setFieldValue, errors } = useFormikContext<any>();
  console.log('rtty values',values);
  useEffect(() => {
     if (rttyEnabled) {
      if(!rtty||(rtty&&Object.keys(rtty).length<=1&&rtty.enabled===true)){
      const defaultRttyConfig = getSubSectionDefaults(t,'rtty');
       setFieldValue('configuration.services.rtty', {
     ...defaultRttyConfig,
     enabled: true,
     });
    }
     }else {
   // 可选：关闭时清除 rtty 字段
   setFieldValue('configuration.services.rtty', undefined);
     }
   }, [rttyEnabled]);
  return(
  <Row gutter={[20, 0]} style={{ marginBottom: 0, width: '100%' }}>
      <Col>
        <ToggleField
          name="configuration.services.rtty.enabled"
          label="RTTY"
        />
        {rttyEnabled&&(
          <>
          <StringField
          name="configuration.services.rtty.host"
          label="Host"
          definitionKey="service.rtty.host"
          // isDisabled={!editing}
          isRequired
          w={280}
        />
        <NumberField
          name="configuration.services.rtty.port"
          label="Port"
          definitionKey="service.rtty.port"
          // isDisabled={!editing}
          isRequired
          w={140}
        />
        <StringField
          name="configuration.services.rtty.token"
          label="Token"
          definitionKey="service.rtty.token"
          // isDisabled={!editing}
          isRequired
          w={280}
        />
          </>
        )}
      </Col>
    </Row>
  );

};

export default React.memo(Rtty);
