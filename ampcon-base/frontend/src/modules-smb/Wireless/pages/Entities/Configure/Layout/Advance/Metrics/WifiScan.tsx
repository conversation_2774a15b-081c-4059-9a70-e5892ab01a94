import React,{useEffect} from 'react';
import {Col,Row} from 'antd';
import NumberField from '@/modules-smb/Wireless/components/FormFields/NumberField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import useFastField from '@/modules-smb/hooks/useFastField';
import {useFormikContext} from 'formik';
import { getSubSectionDefaults } from './metricsConstants';
import { useTranslation } from 'react-i18next';
const WifiScan = () => {
  const { value: wifiScanEnabled } = useFastField({ name: 'configuration.metrics.wifi-scan.enabled' });
  const { values, setFieldValue, errors } = useFormikContext<any>();
    const { t } = useTranslation();
    useEffect(() => {
       if (wifiScanEnabled) {
        const defaultWifiScanConfig = getSubSectionDefaults(t,'wifi-scan');
         setFieldValue('configuration.metrics.wifi-scan', {
       ...defaultWifiScanConfig,
       enabled: true,
       });
       }else {
     // 可选：关闭时清除 wifi-scan 字段
     setFieldValue('configuration.metrics.wifi-scan', undefined);
       }
     }, [wifiScanEnabled]);
  return(
      <Row gutter={[20, 20]} style={{ marginBottom: 0, marginTop: 8, width: '100%' }}>
      <Col>
        <ToggleField
          name="configuration.metrics.wifi-scan.enabled"
          label="Wifi Scan"
        />
        {wifiScanEnabled && (
        <NumberField
          name="configuration.metrics.wifi-scan.interval"
          label="Interval"
          definitionKey="metrics.wifi-scan.interval"
          unit="s"
          // isDisabled={!editing}
          isRequired
          w={140}
        />
        )}    
      </Col>
    </Row>
  );

};

export default React.memo(WifiScan);
