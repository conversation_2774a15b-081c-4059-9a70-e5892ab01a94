import React, { useState, useEffect } from "react";
import { Form, Select, Input } from "antd";
import Icon from "@ant-design/icons/lib/components/Icon";
import { searchSvg } from "@/utils/common/iconSvg";
import COUNTRY_LIST from "@/modules-smb/constants/countryList";

const { Option } = Select;

const CountryListSelector = ({ value, onChange, formItemProps, style }: any) => {
    const [searchValue, setSearchValue] = useState("");

    const filteredOptions = COUNTRY_LIST.filter((option) =>
        option.label.toLowerCase().includes(searchValue.toLowerCase())
    );

    const dropdownRender = (menu) => (
        <div>
            <Input
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                placeholder="Search"
                prefix={<Icon component={searchSvg} />}
                allowClear
                style={{ width: "100%", height: "32px", marginBottom: "3px" }}
            />
            {menu}
        </div>
    );

    return (
        <Select
            value={value}
            onChange={onChange}
            dropdownRender={dropdownRender}
            style={style}
            onDropdownVisibleChange={(open) => {
                if (open) {
                    setSearchValue("");
                }
            }}
        >
            {filteredOptions.map((option: any) => (
                <Option key={option.value} value={option.value}>
                    {option.label}
                </Option>
            ))}
        </Select>
    );
};
export default CountryListSelector;
