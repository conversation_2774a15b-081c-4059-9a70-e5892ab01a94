import React,{useEffect} from 'react';
import {Col,Row} from 'antd';
import NumberField from '@/modules-smb/Wireless/components/FormFields/NumberField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import useFastField from '@/modules-smb/hooks/useFastField';
import {useFormikContext} from 'formik';
import { getSubSectionDefaults } from './metricsConstants';
import { useTranslation } from 'react-i18next';

const Health = () => {
  const { t } = useTranslation();
  const { value: healthEnabled } = useFastField({ name: 'configuration.metrics.health.enabled' });
  const { values, setFieldValue, errors } = useFormikContext<any>();
      useEffect(() => {
         if (healthEnabled) {
          const defaultTelemetryConfig = getSubSectionDefaults(t,'health');
           setFieldValue('configuration.metrics.health', {
         ...defaultTelemetryConfig,
         enabled: true,
         });
         }else {
       // 可选：关闭时清除 health 字段
       setFieldValue('configuration.metrics.health', undefined);
         }
       }, [healthEnabled]);
  return (
    <Row gutter={[20, 20]} style={{ marginBottom: 0, marginTop: 8, width: '100%' }}>
    <Col>
      <ToggleField
        name="configuration.metrics.health.enabled"
        label="Health"
      />
      {healthEnabled && (
        <>
        <NumberField
        name="configuration.metrics.health.interval"
        label="Interval"
        definitionKey="metrics.health.interval"
        // isDisabled={!editing}
        isRequired
        w={140}
      />
      <ToggleField
        name="configuration.metrics.health.dhcp-local"
        label="DHCP-Local"
        isRequired
        defaultValue
        // isDisabled={!editing}
      />
      <ToggleField 
      name="configuration.metrics.health.dhcp-remote" 
      label="DHCP-Remote" 
      isRequired 
      // isDisabled={!editing} 
      />
      <ToggleField
        name="configuration.metrics.health.dns-local"
        label="DNS-Local"
        isRequired
        defaultValue
        // isDisabled={!editing}
      />
      <ToggleField
        name="configuration.metrics.health.dns-remote"
        label="DNS-Remote"
        isRequired
        defaultValue
        // isDisabled={!editing}
      />
        </>
      )}
      
    </Col>
  </Row>
  );
};

export default React.memo(Health);
