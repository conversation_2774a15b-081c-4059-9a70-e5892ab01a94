import React,{useEffect} from 'react';
import {Col,Row} from 'antd';
import { useTranslation } from 'react-i18next';
import MultiSelectField from '@/modules-smb/Wireless/components/FormFields/MultiSelectField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import useFastField from '@/modules-smb/hooks/useFastField';
import {useFormikContext} from 'formik';
import { getSubSectionDefaults } from './metricsConstants';

const DhcpSnooping = () => {
  const { t } = useTranslation();
  const { value: dhcpSnoopingEnabled } = useFastField({ name: 'configuration.metrics.dhcp-snooping.enabled' });
  const { values, setFieldValue, errors } = useFormikContext<any>();
  useEffect(() => {
     if (dhcpSnoopingEnabled) {
      const defaultDhcpSnoopingConfig = getSubSectionDefaults(t,'dhcp-snooping');
       setFieldValue('configuration.metrics.dhcp-snooping', {
     ...defaultDhcpSnoopingConfig,
     enabled: true,
     });
     }else {
   // 可选：关闭时清除 dhcp-snooping 字段
   setFieldValue('configuration.metrics.dhcp-snooping', undefined);
     }
   }, [dhcpSnoopingEnabled]);
  return (
    <Row gutter={[20, 20]} style={{ marginBottom: 0, marginTop: 8, width: '100%' }}>
        <Col>
          <ToggleField
            name="configuration.metrics.dhcp-snooping.enabled"
            label="DHCP Snooping"
          />
          {dhcpSnoopingEnabled&&(
             <MultiSelectField
            name="configuration.metrics.dhcp-snooping.filters"
            label="Filters"
            definitionKey="metrics.dhcp-snooping.filters"
            hasVirtualAll
            canSelectAll
            w={280}
            options={[
              { value: 'ack', label: 'ack' },
              { value: 'discover', label: 'discover' },
              { value: 'offer', label: 'offer' },
              { value: 'request', label: 'request' },
              { value: 'solicit', label: 'solicit' },
              { value: 'reply', label: 'reply' },
              { value: 'renew', label: 'renew' },
            ]}
            isRequired
            // isDisabled={!editing}
          />
          )}        
        </Col>
      </Row>
  );
};

export default React.memo(DhcpSnooping);
