import React ,{useEffect, useState}from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  message,
  Select,
} from "antd";
import { PlusOutlined } from '@ant-design/icons';
import { useTranslation } from "react-i18next";
import { Formik, Form } from "formik";
import { v4 as uuid } from 'uuid';
import SelectField from "@/modules-smb/Wireless/components/FormFields/SelectField";
import MultiSelectFieldWithCheckBox from "@/modules-smb/Wireless/components/FormFields/MultiSelectFieldWithCheckBox";
import StringField from "@/modules-smb/Wireless/components/FormFields/StringField";
import ToggleField from "@/modules-smb/Wireless/components/FormFields/ToggleField";
import {createEthernetPort} from "@/modules-smb/Wireless/apis/wireless_ethernet_ports";
import NumberField from '@/modules-smb/Wireless/components/FormFields/NumberField';
import { useFormikContext } from 'formik';
import { getDhcpServiceList } from '@/modules-smb/Wireless/apis/dhcp_service_api';
import { AmpConCustomModal } from '@/modules-ampcon/components/custom_table';
import NetworkForm from '@/modules-smb/Wireless/pages/Entities/Configure/Layout/Advance/DhcpService/DhcpForm/Form';

import { INTERFACE_ETHERNET_SCHEMA } from "../ethernetConstants";

// DHCP Service列表获取方法
const fetchDhcpOptions = async (siteId: number): Promise<{ label: string; value: string }[]> => {
  try {
    const res = await getDhcpServiceList({ siteId, pageNum: 1, pageSize: 100 });
    if (res?.status === 200 && Array.isArray(res.info)) {
      return res.info.map((item: any) => ({ label: item.name, value: item.name }));
    }
    return [];
  } catch {
    return [];
  }
};
const EthernetCreateModal = ({
  title,
  okText,
  isModalOpen,
  onCancel,
  role = "",
  modalClass = "",
  siteId,
  onSuccess
}) => {
  const { t } = useTranslation();
  const { values, setFieldValue, errors } = useFormikContext<any>();
  const vlanEnabled = values.vlan || false;
  const [networkType,setNetworkType]=useState(1);

  // DHCP 相关状态
  const [dhcpOptions, setDhcpOptions] = useState<{ label: string; value: string }[]>([]);
  const [dhcpLoading, setDhcpLoading] = useState(false);
  const [dhcpModalOpen, setDhcpModalOpen] = useState(false);
  const [dhcpOptionsLoaded, setDhcpOptionsLoaded] = useState(false);// 新增状态，标记是否已加载过选项

  const [formKey, setFormKey] = useState(uuid());
  const validationSchema = React.useMemo(
    () => INTERFACE_ETHERNET_SCHEMA(t, false, role),
    [t, role]
  );
  const initialValues = validationSchema.getDefault();

  const allSelectPortsOptions = [
    { value: "LAN*", label: "LAN*" },
    { value: "LAN1", label: "LAN1" },
    { value: "LAN2", label: "LAN2" },
    { value: "LAN3", label: "LAN3" },
    { value: "LAN4", label: "LAN4" },
  ];

  // 处理DHCP模态框关闭
  const handleDhcpModalClose = async () => {
    setDhcpModalOpen(false);
    setDhcpLoading(true);
    const options = await fetchDhcpOptions(siteId);
    setDhcpOptions(options);
    setDhcpLoading(false);
    setDhcpOptionsLoaded(true); // 标记为已加载
  };

  // 获取DHCP服务列表
  const loadDhcpOptions = async () => {
    if (siteId && !dhcpOptionsLoaded) { // 只在未加载过时获取
      setDhcpLoading(true);
      const options = await fetchDhcpOptions(siteId);
      setDhcpOptions(options);
      setDhcpLoading(false);
      setDhcpOptionsLoaded(true); // 标记为已加载
    }
  };
  // 下拉框展开时的处理函数
  const handleDropdownVisibleChange = (open: boolean) => {
    if (open) {
      loadDhcpOptions();
    }
  };
   // VLAN标签映射关系
  const vlanTagMapping = {
    "auto": 1,
    "tagged": 2,
    "un-tagged": 3
  };
  const handleSubmit = async (values) => {
    if (!values['select-ports'] || values['select-ports'].length === 0) {
    // message.error(t("wireless.please_select_at_least_one_port"));
    message.error("ports can not be empty");
    return;
    }
    const ports=JSON.stringify(values['select-ports']);//转成json字符串
    // 准备请求数据
    const requestData = {
      site_id: siteId, // 根据实际情况可能需要动态获取
      port: ports, 
      mac: values.macaddr || "",
      network_type: networkType,
      // multicast: values.multicast ? 1 : 0,
      // learning: values.learning ? 1 : 0,
      // reverse_path: values['reverse-path'] ? 1 : 0,
      vlan_tag: vlanTagMapping[values['vlan-tag']] || 1,
      vlan_or_dhcp_name: values['ap-assign-ip'] ? values['dhcp-service-name'] : (values.vlan ? values.vlan_id : null),
    };
    // 调用 API 函数
    const success = await createEthernetPort(requestData);
    if (success.status === 200) {
      message.success("Added successfully");
      if (onSuccess) {
        onSuccess(); // 调用成功回调，通知父组件刷新表格
      }
      onCancel();
      setFormKey(uuid()); // 重置表单
    }else{
      const errorMessage = success.info ? `Failed to add: ${success.info}` : "Failed to add.";
      message.error(errorMessage);
    }
  };

  return (
    <Formik
      key={formKey}
      initialValues={initialValues}
      validationSchema={validationSchema}
     onSubmit={async (values, { setSubmitting }) => {
    try {
      const success = true;
      if (success) {
        message.success("Added successfully");
        onCancel();             // 关闭弹窗
        setFormKey(prev => prev + 1); // 重建 form
      } else {
        message.error("Failed to add.");
      }
    } catch {
      message.error("Network error.");
    } finally {
      setSubmitting(false);
    }
  }}
    >
      {({ resetForm,values,setFieldValue,validateForm,setTouched}) => {
        useEffect(() => {
          if (values.vlan && !values.vlan_id) {
            setFieldValue('vlan_id', 1080);
          } else if (!values.vlan) {
            setFieldValue('vlan_id', null);
          }
        }, [values.vlan]);

        useEffect(() => {
         if(values.vlan){
           setNetworkType(2);
         }
         else if(values['ap-assign-ip']){
           setNetworkType(3);
         }else{
           setNetworkType(1);
         }
        },[values.vlan,values['ap-assign-ip']]);

        const markAllTouched = (obj: any) => {
          const touched: any = {};
            for (const key in obj) {
              if (typeof obj[key] === "object" && obj[key] !== null) {
                touched[key] = markAllTouched(obj[key]);
              } else {
                touched[key] = true;
              }
            }
            return touched;
          };
        return(<>
        <Modal
          className={modalClass || ""}
          title={
            <div>
              {title}
              <Divider style={{ marginTop: 8, marginBottom: 0 }} />
            </div>
          }
          open={isModalOpen}
          footer={[
            <Divider style={{ marginTop: 0, marginBottom: 20 }} key="divider" />,
            <Button key="cancel" onClick={() => { resetForm(); onCancel(); }}>
              Cancel
            </Button>,
            <Button key="ok" type="primary" onClick={() => {
                                validateForm().then(errors => {
                                if (Object.keys(errors).length === 0) {
                                    handleSubmit(values);
                                } else {
                                  // 关键：标记所有字段为 touched，让错误立刻显示
                                setTouched(markAllTouched(values));
                                message.error(`Please correct the errors before saving`);
                                }
                              });
                              }}>
              Apply
            </Button>,
          ]}
          onCancel={() => {
            resetForm();
            onCancel();
          }}
          destroyOnClose
        >
          <Form>
            <MultiSelectFieldWithCheckBox
              name="select-ports"
              label="Ports"
              options={allSelectPortsOptions}
              isRequired
              w={280}
            />
            {!values['ap-assign-ip']&&(
              <>
              <ToggleField name="vlan" label="VLAN" />
            {values.vlan&&(<NumberField
              name="vlan_id"
              label=" "
              // isDisabled={!editing}
              w={280}
            />)}
            </>
            )}
            {!values.vlan&&(
              <>
              <ToggleField name="ap-assign-ip" label="AP Assign IP" />
              {values['ap-assign-ip']&&(
                             <SelectField
    name="dhcp-service-name"
    label="DHCP Service Name"
    options={dhcpOptions}
    isRequired
    w={280}
    dropdownRender={menu => (
      <>
        {menu}
        <Button
          type="link"
          icon={<PlusOutlined />}
          style={{ width: '100%', borderTop: '1px solid #E7E7E7' }}
          onClick={() => setDhcpModalOpen(true)}
        >
          Create New DHCP Service
        </Button>
      </>
    )}
    onDropdownVisibleChange={handleDropdownVisibleChange}
  />

                // <div style={{ marginBottom: 16 }}>
                //       <label style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}>
                //         DHCP Service Name
                //       </label>
                //       <Select
                //         value={values['dhcp-service-name']}
                //         onChange={(value) => setFieldValue('dhcp-service-name', value)}
                //         options={dhcpOptions}
                //         loading={dhcpLoading}
                //         style={{ width: 280 }}
                //         dropdownRender={menu => (
                //           <>
                //             {menu}
                //             <Button
                //               type="link"
                //               icon={<PlusOutlined />}
                //               style={{ width: '100%', borderTop: '1px solid #E7E7E7' }}
                //               onClick={() => setDhcpModalOpen(true)}
                //             >
                //               Create New DHCP Service
                //             </Button>
                //           </>
                //         )}
                //       />
                //     </div>
                )}
              </>)}
            <StringField
              name="macaddr"
              label="Mac Address"
              w={280}
              emptyIsUndefined
            />
            <SelectField
              name="vlan-tag"
              label="Vlan Tag"
              options={[
                { label: "Auto", value: "auto" },
                { label: "Tagged", value: "tagged" },
                { label: "Un-tagged", value: "un-tagged" },
              ]}
              w={280}
            />
            {/* <ToggleField name="multicast" label="Multicast" />
            <ToggleField name="learning" label="Learning" />
            <ToggleField name="reverse-path" label="Reverse Path" /> */}
          </Form>

          {/* 新建 DHCP Service 弹窗 */}
            {dhcpModalOpen && (
              <AmpConCustomModal
                isModalOpen={dhcpModalOpen}
                title={'Create DHCP Service'}
                onCancel={() => setDhcpModalOpen(false)}
                modalClass="ampcon-max-modal"
                childItems={
                  <NetworkForm
                    resource={undefined}
                    onClose={handleDhcpModalClose}
                    siteId={siteId}
                  />
                }
              />
            )}
        </Modal>
        </>);
        
}}
    </Formik>
  );
};

export default EthernetCreateModal;
