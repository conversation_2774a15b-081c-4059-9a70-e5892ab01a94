import React, { useCallback,useEffect } from 'react';
import { Row, Col } from 'antd';
import CreatableSelectField from '@/modules-smb/Wireless/components/FormFields/CreatableSelectField';
import NumberField from '@/modules-smb/Wireless/components/FormFields/NumberField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import useFastField from '@/modules-smb/hooks/useFastField';
import { useFormikContext } from 'formik';
import {getSubSectionDefaults} from './servicesConstants';
import { useTranslation } from 'react-i18next';

const Ssh = () => {
  const { value: sshEnabled } = useFastField({ name: 'configuration.services.ssh.enabled' });
  const { value: ssh } = useFastField({ name: 'configuration.services.ssh' });
  const { value: isUsingPasswordAuth } = useFastField({ name: 'configuration.services.ssh.password-authentication' });
  const { onChange: setAuthKeys, onBlur } = useFastField({ name: 'configuration.services.ssh.authorized-keys' });

  const {t}=useTranslation();
  
  const { values, setFieldValue, errors } = useFormikContext<any>();
  useEffect(() => {
     if (sshEnabled) {
      if(!ssh||(ssh&&Object.keys(ssh).length<=1&&ssh.enabled===true)){
      const defaultSshConfig = getSubSectionDefaults(t,'ssh');
       setFieldValue('configuration.services.ssh', {
     ...defaultSshConfig,
     enabled: true,
     });
      }
     }else {
   // 可选：关闭时清除 ssh 字段
   setFieldValue('configuration.services.ssh', undefined);
     }
   }, [sshEnabled]);

  const onPasswordAuthenticationChange = useCallback((isChecked) => {
    if (isChecked) {
      setAuthKeys(undefined);
      setTimeout(() => {
        onBlur();
      }, 200);
    } else {
      setAuthKeys([]);
      setTimeout(() => {
        onBlur();
      }, 200);
    }
  }, []);

  return (
        <Row gutter={[20, 0]} style={{ marginBottom: 0, width: '100%' }}>
          <Col>
          <ToggleField
            name="configuration.services.ssh.enabled"
            label="SSH"
          /> 
          {sshEnabled && (
            <>
            <NumberField
            name="configuration.services.ssh.port"
            label="Port"
            definitionKey="service.ssh.port"
            // isDisabled={!editing}
            isRequired
            w={140}
          />
          <ToggleField
            name="configuration.services.ssh.password-authentication"
            label="Password-Authentication"
            definitionKey="service.ssh.password-authentication"
            // isDisabled={!editing}
            onChangeCallback={onPasswordAuthenticationChange}
            isRequired
            defaultValue
          />
          {isUsingPasswordAuth !== undefined && !isUsingPasswordAuth && (
            <CreatableSelectField
              name="configuration.services.ssh.authorized-keys"
              label="authorized-keys"
              definitionKey="service.ssh.authorized-keys"
              // isDisabled={!editing}
              w={280}
              isRequired
            />
          )}
            </>
          )}
          
          </Col>
        </Row>
  );
};

export default React.memo(Ssh);
