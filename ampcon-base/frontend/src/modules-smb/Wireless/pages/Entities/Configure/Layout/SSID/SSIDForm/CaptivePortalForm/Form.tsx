import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, Input, Select, InputNumber, Row, Col, Button } from 'antd';
import ProfileSwitchSelect from '@/modules-smb/Wireless/components/ProfileSwitchSelect';
import CredentialsUsersForm from './CredentialsUsersForm';
import { validateUrlOrIp, validateIp } from '@/modules-smb/Wireless/utils/util';

interface Props {
  resource?: any;
  siteId?: number;
  onAuthModeChange?: (authMode: string) => void;
}

const UAM_MAC_FORMAT_OPTIONS = [
  { value: 'aabbccddeeff', label: 'aabbccddeeff' },
  { value: 'aa-bb-cc-dd-ee-ff', label: 'aa-bb-cc-dd-ee-ff' },
  { value: 'aa:bb:cc:dd:ee:ff', label: 'aa:bb:cc:dd:ee:ff' },
  { value: 'AABBCCDDEEFF', label: 'AABBCCDDEEFF' },
  { value: 'AA:BB:CC:DD:EE:FF', label: 'AA:BB:CC:DD:EE:FF' },
  { value: 'AA-BB-CC-DD-EE-FF', label: 'AA-BB-CC-DD-EE-FF' },
];

const DEFAULT_CAPTIVE_PORTAL_VALUES = {
  "walled-garden-fqdn": undefined,
  "walled-garden-ipaddr": undefined,
  "web-root": undefined,
  "idle-timeout": 600,
  "session-timeout": undefined,
  "credentials": undefined,
  "auth-server": "************",
  "auth-secret": "secret",
  "auth-port": 1812,
  "acct-server": undefined,
  "acct-secret": undefined,
  "acct-port": 1813,
  "acct-interval": 60,
  "uam-server": "https://YOUR-LOGIN-ADDRESS.YOURS",
  "uam-secret": "secret",
  "uam-port": 3990,
  "ssid": undefined,
  "mac-format": "aabbccddeeff",
  "nasid": "TestLab",
  "nasmac": undefined,
}

const CaptivePortalForm: React.FC<Props> = ({ resource, siteId, onAuthModeChange }) => {
  const { t } = useTranslation();
  const form = Form.useFormInstance();
  const isFirstMountRef = useRef(true);
  const [authMode, setAuthMode] = useState(resource?.captive?.['auth-mode'] || 'off');
  const [webRootEnabled, setWebRootEnabled] = useState(!!resource?.captive?.['web-root']);
  const [webRoot, setWebRoot] = useState(resource?.captive?.['web-root'] || undefined);

  useEffect(() => {
    if (onAuthModeChange) onAuthModeChange(authMode);
    // 如果是首次编辑不触发
    if (isFirstMountRef.current) {
      isFirstMountRef.current = false;
      if (resource) return;
    }
    // authMode 切换后重新赋默认值
    form.setFieldsValue({ captive: DEFAULT_CAPTIVE_PORTAL_VALUES });
    setWebRoot(undefined);
    setWebRootEnabled(false);
  }, [authMode]);

  return (
    <>
      <h3 className='header2'>Captive Portal</h3>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name={['captive', 'auth-mode']}
            label="Auth Mode"
            rules={[{ required: true, message: t('form.required') }]}
          >
            <Select
              value={authMode}
              onChange={val => setAuthMode(val)}
            >
              <Select.Option value="off">Off</Select.Option>
              <Select.Option value="click-to-continue">Click</Select.Option>
              <Select.Option value="radius">Radius</Select.Option>
              <Select.Option value="credentials">Credentials</Select.Option>
              <Select.Option value="uam">UAM</Select.Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      {authMode !== 'off' && <>
        {/* 通用字段 */}
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name={['captive', 'walled-garden-fqdn']}
              label="Walled Garden FQDN"
            >
              <Select
                mode="tags"
                tokenSeparators={[',']}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['captive', 'walled-garden-ipaddr']}
              label="Walled Garden IP Address"
              rules={[
                { validator: (_, value) => validateIp(value) }
              ]}
              
            >
              <Select
                mode="tags"
                tokenSeparators={[',']}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name={['captive', 'idle-timeout']}
              label="Idle Timeout"
              rules={[
                { required: true, message: t('form.required') },
                { type: 'number', max: 65534, message: 'idle-timeout must be less than 65535' },
                { type: 'number', min: 1, message: 'idle-timeout must be greater than 0' }
              ]}
            >
              <InputNumber min={0}/>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['captive', 'session-timeout']}
              label="Session Timeout"
              rules={[
                { type: 'number', max: 65534, message: 'session-timeout must be less than 65535' },
                { type: 'number', min: 1, message: 'session-timeout must be greater than 0' }
              ]}
            >
              <InputNumber min={0} />
            </Form.Item>
          </Col>
        </Row>
        {/* credentials 模式 */}
        {authMode === 'credentials' && (
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item
                label="Credentials"
                name={['captive', 'credentials']}
                rules={[{ required: true, message: t('form.required') }]}
              >
                <CredentialsUsersForm />
              </Form.Item>
            </Col>
          </Row>
        )}
        {/* uam 模式 */}
        {authMode === 'uam' && (
          <>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'uam-server']} 
                  label="UAM Server" 
                  rules={[
                    { required: true, message: t('form.required') }
                  ]}
                > 
                  <Input /> 
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'uam-secret']} 
                  label="UAM Secret" 
                  rules={[{ required: true, message: t('form.required') }]}
                > 
                  <Input.Password />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'uam-port']} 
                  label="UAM Port" 
                  rules={[
                    { required: true, message: t('form.required') },
                    { type: 'number', min: 1024, message: 'captive.uam-port must be greater than 1023' },
                    { type: 'number', max: 65534, message: 'captive.uam-port must be less than 65535' }
                  ]}
                > 
                  <InputNumber /> 
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'nasid']} 
                  label="NAS ID" 
                  rules={[{ required: true, message: t('form.required') }]}
                > 
                  <Input /> 
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'nasmac']} 
                  label="NAS MAC" 
                > 
                  <Input /> 
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'mac-format']} 
                  label="MAC Format" 
                  rules={[{ required: true, message: t('form.required') }]}
                > 
                  <Select options={UAM_MAC_FORMAT_OPTIONS} />
                </Form.Item>
              </Col>
            </Row>
            {/* <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'ssid']} 
                  label="SSID" 
                > 
                  <Input /> 
                </Form.Item>
              </Col>
            </Row> */}
          </>
        )}
        {/* radius/uam 模式 */}
        {(authMode === 'radius' || authMode === 'uam') && (
          <>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'auth-server']} 
                  label="Auth Server" 
                  rules={[
                    { required: true, message: t('form.required') },
                    { validator: (_, value) => validateUrlOrIp(value) }
                  ]}
                > 
                  <Input /> 
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'auth-secret']} 
                  label="Auth Secret" 
                  rules={[{ required: true, message: t('form.required') }]}
                > 
                  <Input.Password /> 
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'auth-port']} 
                  label="Auth Port" 
                  rules={[
                    { required: true, message: t('form.required') },
                    { type: 'number', min: 1024, message: 'captive.auth-port must be greater than 1023' },
                    { type: 'number', max: 65534, message: 'captive.auth-port must be less than 65535' }
                  ]}
                > 
                  <InputNumber /> 
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'acct-server']} 
                  label="Acct Server" 
                  rules={[
                    { validator: (_, value) => validateUrlOrIp(value) }
                  ]}
                > 
                  <Input /> 
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'acct-secret']} 
                  label="Acct Secret" 
                > 
                  <Input.Password /> 
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'acct-port']} 
                  label="Acct Port" 
                  rules={[
                    { type: 'number', min: 1024, message: 'captive.acct-port must be greater than 1023' },
                    { type: 'number', max: 65534, message: 'captive.acct-port must be less than 65535' }
                  ]}
                > 
                  <InputNumber /> 
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'acct-interval']} 
                  label="Acct Interval" 
                  rules={[
                    { type: 'number', min: 1, message: 'acct-interval must be a positive number' },
                    { type: 'number', max: 65534, message: 'acct-interval must be less than 65535' }
                  ]}
                > 
                  <InputNumber /> 
                </Form.Item>
              </Col>
            </Row>
          </>
        )}
        {/* webroot 配置部分 */}
        { authMode !== 'uam' && (
          <Row gutter={24}>
            <Col span={24}>
              <ProfileSwitchSelect
                label="Web Root"
                formName={['captive', 'web-root']}
                switchEnabled={webRootEnabled}
                onSwitchChange={setWebRootEnabled}
                type={3}
                siteId={siteId}
                mode={authMode}
                edit={webRoot}
              />
            </Col>
          </Row>
        )}
      </>}
    </>
  );
};

export default CaptivePortalForm;