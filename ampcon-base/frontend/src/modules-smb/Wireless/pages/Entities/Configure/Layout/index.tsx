import * as React from 'react';
import { Tab, <PERSON>b<PERSON><PERSON>, TabPanel, TabPanels, Tabs, useColorMode, Box, Flex } from '@chakra-ui/react';
import { Button } from 'antd';
import Radios from "./Radios";
import SSID from "./SSID";
import Advance from "./Advance";
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";
import { useBlockNavigation } from "@/modules-smb/Wireless/hooks/useUnsavedChangesWarning.ts";

const SETTING = 'configurepage.tabIndex';

const getDefaultTabIndex = () => {
  const tabIndex = localStorage.getItem(SETTING);

  try {
    if (tabIndex) {
      const parsedTabIndex = parseInt(tabIndex, 10);
      if (parsedTabIndex >= 0 && parsedTabIndex <= 2) {
        return parsedTabIndex;
      }
    }

    return 0;
  } catch (e) {
    return 0;
  }
};

type Props = {
  id: string;
};

const ConfigureLayout = ({ id }: Props) => {
  const [tabIndex, setTabIndex] = React.useState(getDefaultTabIndex());
  const { colorMode } = useColorMode();
  const isLight = colorMode === 'light';
  const tabStyle = {
    textColor: isLight ? 'var(--chakra-colors-blue-600)' : 'var(--chakra-colors-blue-300)',
    fontWeight: 'semibold',
    borderWidth: '0px',
    marginBottom: '-1px',
    borderBottom: '2px solid',
  };

  const [hasUnsavedChanges, setHasUnsavedChanges] = React.useState(false);
  const [hasAdvanceChanges, setHasAdvanceChanges] = React.useState(false);
  const radiosRef = React.useRef<any>(null);
  const advanceRef = React.useRef(null);
  const pendingTabIndex = React.useRef<number | null>(null);
  // 统一 dirty 状态
  const anyDirty = hasUnsavedChanges || hasAdvanceChanges;
  // 统一拦截路由/菜单跳转
  useBlockNavigation(anyDirty);

  // 切换tab时检查是否有未保存修改
  const handleTabClick = (index: number) => {
    if (index === tabIndex) return;

    if (hasUnsavedChanges) {
      pendingTabIndex.current = index;
      confirmModalAction(
        "You have unsaved changes. If you leave or switch to another site, your changes will be lost. Do you want to continue?",
        () => {
          if (pendingTabIndex.current !== null) {
            radiosRef.current?.reset();
            console.log("radios reset");
            setTabIndex(pendingTabIndex.current);
            localStorage.setItem(SETTING, pendingTabIndex.current.toString());
            pendingTabIndex.current = null;
          }
        },
        () => {
          pendingTabIndex.current = null;
        }
      );
    } else if (hasAdvanceChanges) {
      pendingTabIndex.current = index;
      confirmModalAction(
        "You have unsaved changes. If you leave or switch to another site, your changes will be lost. Do you want to continue?",
        () => {
          if (pendingTabIndex.current !== null) {
            advanceRef.current?.reset();
            console.log("advance reset");
            setTabIndex(pendingTabIndex.current);
            localStorage.setItem(SETTING, pendingTabIndex.current.toString());
            pendingTabIndex.current = null;
          }
        },
        () => {
          pendingTabIndex.current = null;
        }
      );
    } else {
      setTabIndex(index);
      localStorage.setItem(SETTING, index.toString());
    }
  };

  React.useEffect(() => {
    setTabIndex(getDefaultTabIndex());
  }, [id]);
  const handleDirtyChange = (dirty: boolean) => {
    setHasAdvanceChanges(dirty);
  };

  const isCurrentTabRadioOrAdvance = tabIndex === 1 || tabIndex === 2;
  const isDisabled = (tabIndex === 1 && !hasUnsavedChanges) || (tabIndex === 2 && !hasAdvanceChanges);
  return (
    <Flex direction="column" minH="100%">
      <Box flex="1">
        <Tabs index={tabIndex} isManual>
          <TabList>
            <Tab _selected={tabStyle} onClick={() => handleTabClick(0)}>SSID Configuration</Tab>
            <Tab _selected={tabStyle} onClick={() => handleTabClick(1)}>Radio Configuration</Tab>
            <Tab _selected={tabStyle} onClick={() => handleTabClick(2)}>Advanced Configuration</Tab>
          </TabList>
          <TabPanels>
            <TabPanel px={0}>
              <SSID />
            </TabPanel>
            <TabPanel px={0}>
              <Radios ref={radiosRef} onDirtyChange={setHasUnsavedChanges} />
            </TabPanel>
            <TabPanel px={0}>
              <Advance ref={advanceRef} onDirtyChange={setHasAdvanceChanges} />
            </TabPanel>
            
          </TabPanels>
        </Tabs>
      </Box>

      {/* 底部固定按钮，始终显示，未修改禁用 */}
      {isCurrentTabRadioOrAdvance && (
        <Box
          position="sticky"
          bottom={0}
          bg="white"
          py={4}
          display="flex"
          justifyContent="flex-end"
          zIndex={1}
          sx={{
            _before: {
              content: '""',
              position: 'absolute',
              top: 0,
              left: '50%',
              transform: 'translateX(-50%)',
              width: 'calc(100% + 48px)',
              height: '1px',
              backgroundColor: '#E2E8F0',
            },
          }}
        >
          <Button
            onClick={() => {
              if (tabIndex === 1) radiosRef.current?.reset();
              if (tabIndex === 2) advanceRef.current?.reset();
            }}
            style={{ marginRight: 16, width: 100 }}
            disabled={isDisabled}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              if (tabIndex === 1) radiosRef.current?.apply();
              if (tabIndex === 2) advanceRef.current?.apply();
            }}
            type="primary"
            style={{ width: 100 }}
            disabled={isDisabled}>
            Apply
          </Button>
        </Box>
      )}
    </Flex>
  );
};

export default ConfigureLayout;
