import React from 'react';
import { Table, Tooltip, Pagination } from 'antd';
import type { SortOrder } from 'antd/es/table/interface';
import { OptimizeHistoryItem } from '@/modules-smb/Wireless/pages/Entities/RRMOptimize/types';
import '@/modules-smb/Wireless/pages/Entities/RRMOptimize/styles/HistoryTable.scss';

interface SortParams {
  field: string;
  order: 'ascend' | 'descend' | null;
}

interface Props {
  data: OptimizeHistoryItem[];
  total: number;
  page: number;
  pageSize: number;
  onPageChange: (page: number, pageSize: number) => void;
  onSortChange: (sorter: SortParams) => void;
  onShowFailed: (historyId: string) => void;
}

const columns = (onShowFailed: (historyId: string) => void) => [
  {
    title: (
      <Tooltip>Trigger Time</Tooltip>
    ),
    dataIndex: 'trigger_time',
    key: 'trigger_time',
    sorter: true,
    multiple: 3,
    width: '25%',
    defaultSortOrder: 'descend' as SortOrder,
  },
  {
    title: (
      <Tooltip>Update Time</Tooltip>
    ),
    dataIndex: 'modified_time',
    key: 'modified_time',
    sorter: true,
    multiple: 2,
    width: '25%',
  },
  {
    title: (
      <Tooltip>Schedule Task</Tooltip>
    ),
    dataIndex: 'is_schedule_task',
    key: 'is_schedule_task',
    sorter: true,
    multiple: 1,
    width: '25%',
    render: (v: number) => v === 2 ? 'Yes' : 'No',
  },
  {
    title: (
      <Tooltip>Result</Tooltip>
    ),
    key: 'result',
    width: '25%',
    render: (_: any, record: OptimizeHistoryItem) => {
      const { online_num, success_num, failed_num, id } = record;
      if (online_num === null || success_num === null || failed_num === null) {
        return <span>task running</span>;
      }
      return (
        <span>
          {/* 
          online_num | 0：确保显示整数，如果是小数会被截断
          success_num || 0：如果值为假值则显示 0，保持原始数据类型
          */}
          Online Device:{online_num || 0},Succeed:{success_num || 0},
          {failed_num > 0 ? (
            <a className="history-failed-link" onClick={() => onShowFailed(id)}>
              Failed:{failed_num}
            </a>
          ) : (
            <span>Failed:{failed_num || 0}</span>
          )}
        </span>
      );
    },
  },
];

const HistoryTable: React.FC<Props> = ({
  data,
  total,
  page,
  pageSize,
  onPageChange,
  onSortChange,
  onShowFailed
}) => {
  // 处理表格排序变化
  const handleTableChange = (
    _pagination: any,
    _filters: any,
    sorter: any
  ) => {
    // 调用父组件传递的排序处理函数
    if (onSortChange) {
      onSortChange({
        field: sorter.field,
        order: sorter.order
      });
    }
  };

  return (
    <div >
      <Table
        // scroll={{ x: 'max-content' }}
        bordered={true}
        // loading={loading}
        rowKey="id"
        columns={columns(onShowFailed)}
        dataSource={data}
        pagination={false}
        size="middle"
        onChange={handleTableChange}
        showSorterTooltip={false}
      />
      {/* 仅当 total > 0 时显示分页 */}
      {total > 0 && (
        <Pagination
          total={total}
          current={page}
          pageSize={pageSize}
          onChange={onPageChange}
          showSizeChanger
          showQuickJumper
          showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} items`}
        />
      )}
    </div>
  );
};

export default HistoryTable;

