import React, { useEffect, useState, useRef } from 'react';
import { Modal, Table, Pagination } from 'antd';
import { fetchRRMTaskResult } from '@/modules-smb/Wireless/apis/wireless_rrm_api';
import { FailedDevice } from '@/modules-smb/Wireless/pages/Entities/RRMOptimize/types';
import '@/modules-smb/Wireless/pages/Entities/RRMOptimize/styles/FailedDeviceModal.scss';
import type { TablePaginationConfig, TableProps } from 'antd/es/table';
import { PAGE_SIZE } from '@/modules-smb/Wireless/pages/Entities/RRMOptimize/index.tsx';


interface Props {
  visible: boolean;
  historyId: string;
  onClose: () => void;
}

// 提取 columns
export const FAILED_DEVICE_COLUMNS = [
  {
    title: 'Device Name',
    dataIndex: 'device_name',
    key: 'device_name',
    width: 200
  },
  {
    title: 'Device SN',
    dataIndex: 'sn',
    key: 'sn',
    width: 220
  },
  {
    title: 'Device Mode',
    dataIndex: 'device_mode',
    key: 'device_mode',
    width: 180
  },
];

const FailedDeviceModal: React.FC<Props> = ({ visible, historyId, onClose }) => {
  const [data, setData] = useState<FailedDevice[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const tableRef = useRef<HTMLDivElement>(null);
  const [hasScrollbar, setHasScrollbar] = useState(false);
  // 分页
  const [sorter, setSorter] = useState<{ field?: string; order?: string }>({});
  // 需要siteId
  const siteId = window.location.hash ? window.location.hash.replace('#', '') : '';

  const fetchData = (page = 1, pageSize = 10, sortField?: string, sortOrder?: string) => {
    setLoading(true);
    fetchRRMTaskResult({
      siteId,
      taskId: historyId,
      sortBy: sortField,
      sortType: sortOrder === 'ascend' ? 'asc' : 'desc',
      pageNum: page,
      pageSize: pageSize,
    })
      .then((res: any) => {
        setData(res.info || []);
        setPagination(prev => ({ ...prev, current: page, pageSize, total: res.total || 0 }));
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    if (visible && historyId) {
      fetchData(1, pagination.pageSize);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, historyId]);


  //
  const handleTableChange: TableProps<FailedDevice>["onChange"] = (paginationObj, filters, sorterObj) => {
    let sortField = (sorterObj as any).field;
    let sortOrder = (sorterObj as any).order;
    setSorter({ field: sortField, order: sortOrder });
    fetchData((paginationObj as TablePaginationConfig).current || 1, (paginationObj as TablePaginationConfig).pageSize || 10, sortField, sortOrder);
  };

  return (
    <Modal
      title="Failed Device List"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1360}
      style={{
        minHeight: '500px',
        maxHeight: 'calc(100vh - 100px)' // 优化最大高度计算，确保在各种缩放比例下都能完整显示
      }}
      destroyOnClose
      className="failed-device-modal"
      wrapClassName="failed-device-modal-wrapper" // 添加包装类名以便进一步定制样式
    >
      <div className="failed-device-modal-content">
        <div className="table-container">
          <Table
            rowKey="id"
            loading={loading}
            dataSource={data}
            pagination={false} // 禁用表格内置分页
            columns={FAILED_DEVICE_COLUMNS}
            size="middle"
            bordered={true}
            showSorterTooltip={false}
            onChange={handleTableChange}
            // 设置切换分页 是否跳到首行
            scroll={{ y: 300, scrollToFirstRowOnChange: false }} // 设置固定高度以确保滚动条显示
          />
        </div>
        {pagination.total > PAGE_SIZE && (
          <div className="pagination-container">
            <Pagination
              current={pagination.current}
              pageSize={pagination.pageSize}
              total={pagination.total}
              showSizeChanger={true}
              showQuickJumper={true}
              showTotal={(total, range) => `${range[0]}-${range[1]} of ${total} items`}
              onChange={(page, pageSize) => fetchData(page, pageSize, sorter.field, sorter.order)}
              onShowSizeChange={(_current, size) => fetchData(1, size, sorter.field, sorter.order)}
              style={{ marginTop: '16px', textAlign: 'right' }}
              pageSizeOptions={['10', '20', '50', '100']} // 添加分页大小选项
            />
          </div>
        )}
      </div>
    </Modal>
  );
};

export default FailedDeviceModal;