.rrm-optimize-root {
    // background: #fff;
    // min-height: 70vh;

    .rrm-optimize-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        // margin-bottom: 16px;
        margin-bottom: 5px;
        margin-top: -5px;

        h2 {
            width: auto;
            height: 22px;
            font-family: Lato;
            font-size: 18px;
            font-weight: 900;
            line-height: normal;
            letter-spacing: normal;
            /* 一级文字颜色 */
            /* 样式描述：#212529 */
            color: #212519;
        }

        a {
            color: #13c2b3;
            font-size: 14px;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: color 0.2s;
            cursor: pointer;
            margin-top: 4px;

            &:hover {
                color: #1ddacc;
            }

            span {
                font-size: 18px;
                margin-right: 4px;
            }
        }
    }

    .rrm-optimize-desc {
        margin-bottom: 40px;
        max-width: 1439px;
        height: 19px;
        font-family: Lato;
        font-size: 16px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #212529;
    }

    .rrm-optimize-alert {
        // width: 1564px;
        max-width: 100%;
        width: 100%;
        // height: 40px;
        min-height: 40px; // 使用最小高度替代固定高度
        // margin-bottom: 16px;
        // margin-top: 16px;
        background: #F3F8FF;
        border: 0px solid;
        color: #367EFF;
        border-radius: 6px;
        // padding: 8px 16px;
    }

    .rrm-optimize-alert .ant-alert-message {
        color: #367EFF !important;
        font-size: 13px;
        line-height: 20px;
    }

    .rrm-optimize-actions {
        margin-top: 24px;
        display: flex;

        gap: 12px;
        margin-bottom: 32px;

        .ant-btn-primary {
            background: #1ddacc;
            border: none;
            font-weight: 500;
            font-size: 14px;
            min-width: 140px;
            height: 36px;
            border-radius: 2px;
            transition: background 0.2s;

            &:hover,
            &:active,
            &:focus {
                background: #13c2b3;
            }
        }

        .ant-btn-default {
            background: #fff;
            border: 1px solid #1ddacc;
            color: #1ddacc;
            font-weight: 500;
            font-size: 14px;
            min-width: 160px;
            width: 179px;
            height: 36px;
            border-radius: 2px;
            transition: background 0.2s, color 0.2s, border 0.2s;

            &:hover,
            &:active,
            &:focus {
                background: #e6fcfa;
                color: #13c2b3;
                border-color: #13c2b3;
            }
        }
    }

}