// 覆盖confirmModalAction中的按钮样式
// .ampcon-mini-modal {
//   .ant-btn-primary,
//   .ant-btn-default {
//     width: 100px !important;
//     font-weight: normal !important;  
//   }
// }
/*
.confirm-modal {
  .ant-modal-content {
    border-radius: 8px !important;
    box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.10) !important;
    padding: 0 !important;
    background: #fff !important;
  }

  .ant-modal-header {
    border-radius: 8px 8px 0 0 !important;
    font-size: 20px !important;
    font-weight: bold !important;
    color: #222 !important;
    padding: 15px 20px 15px 20px !important;
    border-bottom: 1px solid #e5e6eb !important;
    display: flex !important;
    align-items: center !important;
  }

  .ant-modal-title {
    font-size: 20px !important;
    font-weight: bold !important;
    color: #222 !important;
    padding: 0 !important;
    margin-top: 0px !important;
    height: 27px !important;
    display: flex !important;
    align-items: center !important;
  }

  .ant-modal-close {
    top: 18px !important;
    right: 18px !important;
    font-size: 20px !important;
  }

  .ant-modal-body {
    padding: 10px 0px 10px 5px !important;
    border-bottom: 1px solid #e5e6eb !important;
  }

  .confirm-content-row {
    display: flex;
    align-items: flex-start;
    min-height: 50px;
    background: none !important;

    .confirm-icon {
      color: #ff9800 !important;
      font-size: 20px !important;
      margin-right: 10px !important;
      margin-left: 20px !important;
      margin-top: 3px !important;
      display: flex !important;
      align-items: center !important;
      flex-shrink: 0 !important;
    }

    .confirm-content-text {
      font-size: 15px !important;
      color: #212529 !important;
      line-height: 1.7 !important;
      font-family: Lato, Arial, sans-serif !important;
      margin-top: 1px !important;
    }
  }

  .ant-modal-footer {
    display: flex !important;
    justify-content: flex-end !important;
    gap: 0px !important;
    padding: 6px 20px 16px 32px !important;
    background: #fff !important;
    border-radius: 0 0 8px 8px !important;
    border-top: none !important;
    height: 55px !important;

    .ant-btn {
      min-width: 73.94px !important;
    }
    
    .ant-btn-primary,           // Yes button
    .ant-btn-default {          // No button
      width: 100px !important;
      font-weight: normal !important;  // 调细按钮字体
    }
  }

}
*/

