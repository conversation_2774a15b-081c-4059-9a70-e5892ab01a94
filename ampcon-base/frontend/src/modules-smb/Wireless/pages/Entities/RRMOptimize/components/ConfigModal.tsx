import React, { useEffect } from 'react';
import { Modal, Form, InputNumber, Radio, Switch, Divider, message } from 'antd';
import { FormModal } from '@/modules-smb/Wireless/components/Modals/FormModal';
import { RRMConfig } from '@/modules-smb/Wireless/pages/Entities/RRMOptimize/types';
// import '@/modules-smb/Wireless/pages/Entities/RRMOptimize/styles/ConfigModal.scss';

interface Props {
  visible: boolean;
  onClose: () => void;
  config: RRMConfig;
  onApply: (form: RRMConfig) => Promise<void>;
  loading?: boolean;
}


const ConfigModal: React.FC<Props> = ({ visible, onClose, config, onApply, loading = false }) => {
  const [form] = Form.useForm<RRMConfig>();

  useEffect(() => {
    if (visible) {
      config.enableChannelOptimization = config.channelMode === 'unmanaged_aware'
      config.enableTxPowerOptimization = config.txPowerMode === 'measure_ap_ap'
      // 为coverageThreshold设置默认值
      if (config.coverageThreshold === undefined || config.coverageThreshold === null) {
        config.coverageThreshold = -70;
      }
      form.setFieldsValue(config);
    }
  }, [visible, config, form]);

  const handleOk = async (values: RRMConfig) => {
    // 根据开关状态设置mode值
    const updatedValues = { ...values };

    // 如果enableChannelOptimization开关打开，则设置channelMode为unmanaged_aware
    if (updatedValues.enableChannelOptimization) {
      updatedValues.channelMode = 'unmanaged_aware';
    }

    // 如果enableTxPowerOptimization开关打开，则设置txPowerMode为measure_ap_ap，
    // 并设置nthSmallestRssi为0
    if (updatedValues.enableTxPowerOptimization) {
      updatedValues.txPowerMode = 'measure_ap_ap';
      updatedValues.nthSmallestRssi = 0;
    }

    // 至少选择一种算法
    if (!updatedValues.channelMode && !updatedValues.txPowerMode) {
      return message.error('Please enable at least one optimization method')
    }

    await onApply(updatedValues);
  };

  const handleClose = () => {
    // 重置表单，清除未保存的配置
    form.resetFields();
    onClose();
  };

  return (
    <FormModal
      title="Optimization Config"
      open={visible}
      onCancel={handleClose}
      onFinish={(values: any) => handleOk(values)}
      form={form}
      initialValues={config}
      modalClass="ampcon-middle-modal"
    >
      <style>
        {`
          .coverage-threshold-form-item .ant-form-item-explain {
            margin-left: 50px;
          }
        `}
      </style>
      {/* Optimize channel configuration */}
      <div style={{ display: 'flex', alignItems: 'center', marginTop: '10px' }}>
        <span>Automatic Channel Optimization</span>
        <Form.Item
          style={{ marginBottom: 0, marginLeft: '50px' }}
          name="enableChannelOptimization"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
      </div>

      {/*Optimize tx power configuration  */}
      <div>
        <div style={{ display: 'flex', alignItems: 'center', marginTop: '16px' }}>
          <span>Optimize TX Power Configuration</span>
          <Form.Item
            style={{ marginBottom: 0, marginLeft: '44.5px' }}
            name="enableTxPowerOptimization"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </div>
        <Form.Item noStyle shouldUpdate={(prev, curr) => prev.txPowerMode !== curr.txPowerMode || prev.enableTxPowerOptimization !== curr.enableTxPowerOptimization}>
          {() => (
            <>
              {form.getFieldValue('enableTxPowerOptimization') && (
                <div>
                  <div style={{ display: 'flex', alignItems: 'center', marginTop: '20px' }}>
                    <span style={{ position: 'relative' }}>
                      {/* <span style={{ 
                        color: 'red', 
                        position: 'absolute', 
                        top: '60%', 
                        right: '-10px',
                        transform: 'translateY(-50%)'
                      }}>*</span> */}
                    </span>
                    <Form.Item
                      label='Coverage Threshold'
                      name="coverageThreshold"
                      rules={[
                        { required: true, message: ('') },
                        {
                          validator: (_, value) => {
                            if (value === undefined || value === null || value === '') {
                              return Promise.reject(new Error('Coverage Threshold Rssi is required'));
                            }
                            if (value < -80 || value > -60) {
                              return Promise.reject(new Error('values:  -80 ～ -60'));
                            }
                            return Promise.resolve();
                          }
                        }
                      ]}
                      style={{ marginBottom: 0, marginLeft: '0px', minHeight: '35px' }}
                      className="coverage-threshold-form-item"
                    >
                      <InputNumber
                        style={{ width: '280px', marginLeft: '50px' }}
                      />
                    </Form.Item>
                  </div>
                </div>
              )}
            </>
          )}
        </Form.Item>
      </div>
    </FormModal>
  );
};



// const ConfigModalOld: React.FC<Props> = ({ visible, onClose, config, onApply, loading = false }) => {
//   const [form] = Form.useForm<RRMConfig>();

//   useEffect(() => {
//     if (visible) {
//       form.setFieldsValue(config);
//     }
//   }, [visible, config, form]);

//   const handleOk = async () => {
//     // try {
//     const values = await form.validateFields();
//     await onApply(values);
//     // onClose() 现在由 handleConfigApply 在成功后调用
//     // } catch (e) {
//     //   message.error('Failed to save the configuration');
//     // }
//   };

//   return (
//     <Modal
//       title={<div>Optimization Config</div>}
//       open={visible}
//       onCancel={onClose}
//       onOk={handleOk}
//       okText="Apply"
//       cancelText="Cancel"
//       width={1360}
//       destroyOnClose
//       className="config-modal"
//       confirmLoading={loading}
//     >
//     <Form form={form} layout="vertical" initialValues={config}>
//       <div className="config-section">
//         <div className="config-section-title">Optimize channel configuration</div>
//         <div className="config-row config-row-center">
//           <div className="config-mode-row">
//             <span className="config-label">Mode:</span>
//             <Form.Item name="channelMode" className="form-item-margin150">
//               <Radio.Group>
//                 <Radio value="random">Random</Radio>
//                 <Radio value="least_used">Least_used</Radio>
//                 <Radio value="unmanaged_aware">Unmanaged_aware</Radio>
//               </Radio.Group>
//             </Form.Item>
//           </div>
//         </div>
//         <Form.Item noStyle shouldUpdate={(prev, curr) => prev.channelMode !== curr.channelMode}>
//           {() =>
//             form.getFieldValue('channelMode') === 'random' && (
//               <div className="config-row config-row-mt24">
//                 <div className="config-inline-item config-mode-row config-inline-item-margin">
//                   <span className="config-label">Set Different Channel Per Ap</span>
//                   <Form.Item
//                     name="setDifferentChannelPerAp"
//                     valuePropName="checked"
//                     className="form-item-no-margin"
//                   >
//                     <Switch />
//                   </Form.Item>
//                 </div>
//               </div>
//             )
//           }
//         </Form.Item>

//       </div>
//       <div className="config-divider-section" />

//       <Divider
//         style={{
//           display: 'block',
//           clear: 'both',
//           width: '50%',
//           minWidth: '97%',
//           margin: '12px 20px',
//         }}
//       />
//       <div className="config-section">
//         <div className="config-section-title">Optimize tx power configuration</div>
//         <div className="config-row config-row-center">
//           <div className="config-mode-row">
//             <span className="config-label">Mode:</span>
//             <Form.Item name="txPowerMode" className="form-item-margin150">
//               <Radio.Group>
//                 <Radio value="random">Random</Radio>
//                 <Radio value="measure_ap_client">Measure_ap_client</Radio>
//                 <Radio value="measure_ap_ap">Measure_ap_ap</Radio>
//                 {/* <Radio value="location_optimal">Location_optimal</Radio> */}
//               </Radio.Group>
//             </Form.Item>
//           </div>
//         </div>
//         <Form.Item noStyle shouldUpdate={(prev, curr) => prev.txPowerMode !== curr.txPowerMode}>
//           {() => (
//             <>
//               {form.getFieldValue('txPowerMode') === 'random' && (
//                 <div className="config-row config-row-mt24">
//                   <div className="config-inline-item config-mode-row config-inline-item-margin">
//                     <span className="config-label">Set Different Tx Power Per Ap</span>
//                     <Form.Item
//                       name="setDifferentTxPowerPerAp"
//                       valuePropName="checked"
//                       className="form-item-no-margin tx-power-switch"
//                     >
//                       <Switch />
//                     </Form.Item>
//                   </div>
//                 </div>
//               )}
//               {form.getFieldValue('txPowerMode') === 'measure_ap_ap' && (
//                 <div className="config-row config-row-gap0">
//                   <div className="config-mode-row config-inline-item-margin-right32">
//                     <span className="config-label">Coverage Threshold</span>
//                     <Form.Item
//                       name="coverageThreshold"
//                       className="form-item-flex1 form-item-no-margin coverage-threshold-item"
//                       rules={[
//                           // { required: true, message: 'Coverage Threshold Rssi is required' },
//                           {
//                             // required: true, message: '',
//                             validator: (_, value) => {
//                               if (value === undefined || value === null || value === '') {
//                                 return Promise.reject(new Error('Coverage Threshold Rssi is required'));
//                               }
//                               if (value == 30 || value > 30) {
//                                 return Promise.reject(new Error('values: int < 30'));
//                               }
//                               return Promise.resolve();
//                             }
//                           }
//                         ]}
//                       >
//                         <InputNumber className="input-number-ml50-mr100" />
//                       </Form.Item>
//                     </div>
//                     <div className="config-inline-item config-mode-row config-inline-item-flex1">
//                       <span className="config-label config-label-min160-mr-20">Nth Smallest Rssi</span>
//                       <Form.Item
//                         name="nthSmallestRssi"
//                         className="form-item-flex1 form-item-no-margin nth-smallest-rssi-item"
//                         rules={[
//                           // { required: true, message: 'Nth Smallest Rssi is required' },
//                           {
//                             // required: true, message: '',
//                             validator: (_, value) => {
//                               if (value === undefined || value === null || value === '') {
//                                 return Promise.reject(new Error('Nth Smallest Rssi is required'));
//                               }
//                               if (value < 0) {
//                                 return Promise.reject(new Error('values: int >= 0'));
//                               }
//                               return Promise.resolve();
//                             }
//                           }
//                         ]}
//                       >
//                         <InputNumber className="input-number-280" />
//                       </Form.Item>
//                     </div>
//                   </div>
//                 )}
//                 {form.getFieldValue('txPowerMode') === 'measure_ap_client' && (
//                   <div className="config-row">
//                     <div className="config-mode-row">
//                       <span className="config-label">Target Mcs</span>
//                       <div className="target-mcs-container">
//                         <Form.Item
//                           name="targetMcs"
//                           className="form-item-no-margin"
//                           rules={[
//                             {
//                               // required: true, message: '',
//                               validator: (_, value) => {
//                                 if (value === undefined || value === null || value === '') {
//                                   return Promise.reject(new Error('Target Mcs is required'));
//                                 }
//                                 if (value < 0 || value > 9) {
//                                   return Promise.reject(new Error('Value: 0-9'));
//                                 }
//                                 return Promise.resolve();
//                               }
//                             }
//                           ]}
//                         >
//                           <InputNumber className="input-number-ml140" />
//                         </Form.Item>
//                       </div>
//                     </div>
//                   </div>
//                 )}
//               </>
//             )}
//           </Form.Item>
//         </div>
//         {/* <div className="config-divider-section" /> */}
//       </Form>
//       <div className="config-divider-footer" />
//     </Modal>
//   );
// };

export default ConfigModal;