.config-modal {
  .ant-modal-content {
    border-radius: 8px !important;
    box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.10) !important;
    padding: 0 !important;
    background: #fff !important;
  }

  .ant-modal-header {
    border-radius: 8px 8px 0 0 !important;
    font-size: 20px !important;
    font-weight: bold !important;
    color: #222 !important;
    padding: 15px 20px 15px 20px !important;
    border-bottom: 1px solid #e5e6eb !important;
    display: flex !important;
    align-items: center !important;
  }

  .ant-modal-title {
    font-size: 20px !important;
    font-weight: bold !important;
    color: #222 !important;
    padding: 0 !important;
    margin-top: 0px !important;
    height: 27px !important;
    display: flex !important;
    align-items: center !important;
  }

  .ant-modal-close {
    // top: auto !important;
    right: 24px !important;
    font-size: 20px !important;
  }

  .ant-modal-body {
    margin: 0 0 0 0;
    padding: 0 !important;
  }

  .config-section {
    padding: 32px 40px 0 20px;
    background: #fff;
    margin-bottom: 32px;
  }

  .config-section-title {
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 16px;
    color: #222;
  }



  // .config-divider-section {
  //   width: calc(100% - 80px);
  //   margin-left: 40px;
  //   margin-right: 40px;
  //   background: #e5e6eb;
  //   height: 1px;
  //   margin-top: 32px;
  //   margin-bottom: 0;
  // }

  .config-divider-footer {
    width: 100%;
    height: 1px;
    background: #e5e6eb;
    margin: 0 0 0 0;
  }

  .config-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0 32px;
    margin-bottom: 16px;
  }

  .config-inline-item {
    margin-left: 0 !important;
  }

  .config-label {
    min-width: 160px;
    font-size: 14px !important;
    color: #222 !important;
    font-weight: 400 !important;
    margin-right: 12px !important;
    display: flex;
    align-items: center;
    height: 38px;
  }

  .ant-radio-group {
    font-size: 15px !important;
    display: flex !important;
    align-items: center !important;
    gap: 24px !important;
    margin-left: 0;

    .ant-radio-wrapper {
      margin-right: 0 !important;
    }
  }

  .ant-switch {
    margin-top: 2px;
    margin-left: 16px;
  }

  /* Specific styles for the channel and tx power switches */
  .form-item-no-margin .ant-switch {
    position: relative;
    left: 16px;
    /* Adjust this value to align with 'Random' radio button */
  }

  /* Specific style for tx power switch - move it left a bit */
  .tx-power-switch .ant-switch {
    position: relative;
    left: 9px;
    /* Move tx power switch left while keeping channel switch at 14px */
  }

  .ant-input-number {
    width: 280px !important;
    height: 36px !important;
    border-radius: 2px !important;
    font-size: 15px !important;
  }

  .ant-form-item-label>label {
    font-size: 14px !important;
    color: #222 !important;
    font-weight: 400 !important;
  }

  .ant-form-item-explain-error {
    color: #ff4d4f !important;
    font-size: 13px !important;
    margin-top: 2px !important;
  }

  .ant-modal-footer {
    border-top: none !important;
    padding: 5px 20px 20px 20px !important;
    background: #fff !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 0 !important;
    border-radius: 0 0 8px 8px !important;
    
    .ant-btn-primary,           // Apply button
    .ant-btn-default {          // Cancel button
      width: 100px !important;
    }
  }

}

.config-mode-row {
  display: flex;
  align-items: center;
  margin-left: 0; // 确保没有左边距

  .config-label {
    font-size: 14px;
    color: #222;
    font-weight: 400;
    margin-right: 16px;
    min-width: 60px;
  }
}

// 新增：用于替换内联style的class
.config-row-center {
  align-items: center;
  display: flex;
}

.config-inline-item-margin {
  display: flex;
  align-items: center;
  margin-bottom: 0;
  margin-right: 24px;
  margin-left: 0;
}

// 通用标签样式

.config-label-min160-mr-20 {
  min-width: 160px;
  margin-right: -200px;
  height: 38px;
  display: flex;
  align-items: center;
}

// 通用表单项样式
.form-item-margin150 {
  margin-bottom: 0;
  margin-left: 150px;
}

// 通用间距样式
.config-row-mt24 {
  margin-top: 24px;
}

// 通用输入框样式 - 基础宽度280px
.input-number-ml50-mr100,
.input-number-280,
.input-number-ml140 {
  width: 280px;
}

.input-number-ml50-mr100 {
  margin-left: 85px;
  margin-right: 100px;
}

.input-number-280 {
  margin-left: 100px;
}

.input-number-ml140 {
  margin-left: 140px;
}

.config-row-gap0 {
  gap: 0 !important;
}

// 通用表单项样式
.form-item-no-margin {
  margin-bottom: 0 !important;
}

.form-item-flex1 {
  flex: 1;
}

// 通用内联项样式
.config-inline-item-margin-right32 {
  margin-right: 32px;
  margin-left: 0;
}

.config-inline-item-flex1 {
  flex: 1;
}

// Target Mcs 容器和提示样式
.target-mcs-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 140px;

  .input-number-ml140 {
    margin-left: 0 !important;
  }
}

.target-mcs-hint {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
  margin-left: 0;
}

// Coverage Threshold 
.coverage-threshold-item {
  .ant-form-item-explain-error {
    margin-left: 85px !important; // 与输入框的 margin-left 保持一致
  }
}

// Nth Smallest Rssi 
.nth-smallest-rssi-item {
  .ant-form-item-explain-error {
    margin-left: 100px !important; // 与输入框的 margin-left 保持一致
  }
}