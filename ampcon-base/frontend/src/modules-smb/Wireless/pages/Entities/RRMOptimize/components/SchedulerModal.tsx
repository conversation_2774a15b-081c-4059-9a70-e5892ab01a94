import React, { useEffect } from 'react';
import { Modal, Switch, TimePicker, Checkbox, Form, message, Divider } from 'antd';
import dayjs from 'dayjs';
import type { SchedulerConfig } from '@/modules-smb/Wireless/pages/Entities/RRMOptimize/types';
import '@/modules-smb/Wireless/pages/Entities/RRMOptimize/styles/SchedulerModal.scss';

interface Props {
  visible: boolean;
  onClose: () => void;
  config: SchedulerConfig;
  // onChange: (form: SchedulerConfig) => void;
  onApply: (form: SchedulerConfig) => boolean | void | Promise<boolean | void>;
}


const weekOptions = [
  { label: 'Monday', value: '2' },
  { label: 'Tuesday', value: '3' },
  { label: 'Wednesday', value: '4' },
  { label: 'Thursday', value: '5' },
  { label: 'Friday', value: '6' },
  { label: 'Saturday', value: '7' },
  { label: 'Sunday', value: '1' },
];

const SchedulerModal: React.FC<Props> = ({ visible, onClose, config, onApply }) => {
  const [form] = Form.useForm<SchedulerConfig>();

  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        ...config,
        executeTime: config.executeTime ? dayjs(config.executeTime, 'HH:mm') : null,
      });
    }
  }, [visible, config, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      // 等待 onApply 返回结果
      const result = await onApply({
        ...values,
        executeTime: values.executeTime ? values.executeTime.format('HH:mm') : '',
      }) as boolean | void;
      // 只有 onApply 返回非 false/非 rejected 才关闭弹窗
      if (result !== false) {
        onClose();
      }
    } catch (e) {
      message.error('Failed to save the scheduled task');
    }
  };

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      onOk={handleOk}
      okText="Apply"
      cancelText="Cancel"
      width={680}
      destroyOnClose
      className="scheduler-modal"
      title={<span className="scheduler-title">Optimization Scheduler</span>}
    >
      <Divider
        style={{

          margin: '-10px 0px 0px 0px',
        }}
      />

      <Form form={form} layout="vertical" initialValues={{
        ...config,
        executeTime: config.executeTime ? dayjs(config.executeTime, 'HH:mm') : null,
      }}>
        <div className="scheduler-modal-body-row">
          <div className="scheduler-row scheduler-row-switch">
            <span className="scheduler-label">Scheduled Optimization</span>
            <Form.Item name="enabled" valuePropName="checked" noStyle>
              <Switch />
            </Form.Item>
          </div>
          <Form.Item noStyle shouldUpdate={(prev, curr) => prev.enabled !== curr.enabled}>
            {() => form.getFieldValue('enabled') && (
              <>
                <div className="scheduler-row scheduler-row-time">
                  <span className="scheduler-label">Network Segment Execution Time</span>
                  <Form.Item name="executeTime" noStyle>
                    <TimePicker
                      format="HH:mm"
                      allowClear={false}
                      getPopupContainer={trigger => trigger.parentElement as HTMLElement}
                      minuteStep={1}
                    />
                  </Form.Item>
                </div>
                <Form.Item name="days" noStyle>
                  <Checkbox.Group options={weekOptions} className="scheduler-week-group" />
                </Form.Item>
              </>
            )}
          </Form.Item>
        </div>
      </Form>
      <Divider
        style={{
          margin: '0px 0px 0px 0px',
        }}
      />
      {/* <div className="scheduler-divider" /> */}
    </Modal>
  );
};

export default SchedulerModal; 