.scheduler-modal {
  .ant-modal-content {
    border-radius: 8px !important;
    box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.10) !important;
    padding: 0 !important;
    background: #fff !important;
    // height: 538px !important;
  }

  .ant-modal-header {
    border-radius: 8px 8px 0 0 !important;
    // background: #f7fafd !important;
    font-size: 20px !important;
    font-weight: bold !important;
    color: #222 !important;
    padding: 15px 20px 15px 20px !important;
    // border-bottom: 1px solid #e5e6eb !important;
    display: flex !important;
    align-items: center !important;
  }

  .ant-modal-title {
    font-size: 20px !important;
    font-weight: bold !important;
    color: #222 !important;
    padding: 0 !important;
    margin-top: 0px !important;
    height: 27px !important;
    display: flex !important;
    align-items: center !important;
  }

  .ant-modal-close {
    position: absolute !important;
    right: 24px !important;
    font-size: 20px !important;
  }

  .scheduler-divider {
    width: 100%;
    height: 1px;
    background: #e5e6eb;
    margin-top: 10px;
    margin-bottom: 1px;
  }


  .scheduler-modal-body-row {
    padding: 20px 20px 20px 20px !important;
    min-height: 120px;
    background: #fff;
  }

  .scheduler-row {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    &.scheduler-row-switch {
      margin-bottom: 24px;
    }

    &.scheduler-row-time {
      margin-bottom: 24px;
    }

    .scheduler-label {
      width: 210px;
      font-family: Lato;
      font-size: 14px;
      font-weight: normal;
      color: #212519;
      margin-right: 16px;
    }

    >*:not(.scheduler-label) {
      margin-left: 32px;
    }
  }

  // .scheduler-datepicker {
  //   width: 140px !important;
  //   height: 36px !important;

  //   .ant-picker-input>input {
  //     font-size: 15px;
  //     height: 36px !important;
  //     border-radius: 2px !important;

  //   }
  // }

  .ant-picker-time-panel-column {
    width: 60px !important;
    min-width: 60px !important;
    max-width: 60px !important;
  }

  .scheduler-week-group {
    display: flex !important;
    flex-wrap: wrap;
    width: 360px;
    margin: 0 0 24px 258px;
    column-gap: 32px;

    .ant-checkbox-wrapper {
      width: 90px;
      margin-bottom: 24px !important;
      font-size: 14px !important;
      color: #212519 !important;
      display: flex !important;
      align-items: center !important;

      .ant-checkbox-inner {
        border-radius: 2px !important;
        width: 18px !important;
        height: 18px !important;
      }

      .ant-checkbox-checked .ant-checkbox-inner {
        background: #14C9BB !important;
        border-color: #14C9BB !important;
      }

      .ant-checkbox-checked .ant-checkbox-inner:after {
        border-color: #fff !important;
      }

      .ant-checkbox+span {
        margin-left: 0px !important;
        vertical-align: middle !important;
      }
    }
  }



  .ant-picker-time-panel .ant-picker-ok button {
    background: #fff !important;
    color: #14C9BB !important;
    border: 1.5px solid #14C9BB !important;
    border-radius: 2px !important;
    font-weight: 500 !important;
    box-shadow: none !important;
    transition: all 0.2s !important;
  }

  .ant-picker-time-panel .ant-picker-ok button:hover {
    background: #f7fafd !important;
    color: #14C9BB !important;
    border-color: #14C9BB !important;
  }

  .ant-modal-footer {
    display: flex;
    justify-content: flex-end;
    padding-right: 20px !important;
    padding-bottom: 20px !important;
    padding-top: 10px !important;
    padding-left: 0 !important;
    background: #fff;
    border-radius: 0 0 8px 8px;
    
    // .ant-btn-primary,           // Apply button
    // .ant-btn-default {          // Cancel button
    //   width: 100px !important;
    // }
  }
}