// 算法配置
export interface RRMConfig {
  channelMode: 'random' | 'least_used' | 'unmanaged_aware';
  setDifferentChannelPerAp: boolean;
  /** Random模式下每AP不同TxPower */
  
  txPowerMode: 'random' | 'measure_ap_client' | 'measure_ap_ap' | 'location_optimal';
  setDifferentTxPowerPerAp?: boolean;
  targetMcs?: number | null;
  coverageThreshold?: number | null;
  nthSmallestRssi?: number | null;

  // steerMode: 'band';
  // minRssi2G: number | null;
  // maxRssi2G: number | null;
  // minRssiNon2G: number | null;
  // backoffTimeSec: number | null;
}


// 定时任务
export interface SchedulerConfig {
  enabled: boolean;
  executeTime: string;
  days: string[];
} 

// 优化历史记录
export interface OptimizeHistoryItem {
  id: string;
  create_time: string;
  modified_time: string;
  site_id: number;
  trigger_time: string;
  is_schedule_task: number;
  online_num: number;
  success_num: number;
  failed_num: number;
}


// 失败设备
export interface FailedDevice {
  task_id: string;
  sn: string;
  create_time: string;
  modified_time: string;
  result_type: number;
  device_name: string;
  device_mode: string;
}

// RRM API请求参数
export interface RRMApiParameter {
  algorithm: string;
  args: string;
}

export interface RRMApiRequest {
  parameter: RRMApiParameter[];
  siteId: string;
}

// RRM API响应
export interface RRMApiResponse {
  info: string;
  status: number;
}




