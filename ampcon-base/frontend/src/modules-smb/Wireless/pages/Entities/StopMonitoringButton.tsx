import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, message, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { Stop } from '@phosphor-icons/react';
import { useDeleteAnalyticsBoard } from '@/modules-smb/hooks/Network/Analytics';
import { useUpdateVenue } from '@/modules-smb/hooks/Network/Venues';
import { AxiosError } from '@/modules-smb/models/Axios';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";
import "./index.scss"

interface Props {
    boardId: string;
    venueId: string;
    onSuccess?: () => void;
}

const StopMonitoringButton = ({ boardId, venueId, onSuccess }: Props) => {
    const { t } = useTranslation();
    const deleteAnalytics = useDeleteAnalyticsBoard();
    const updateVenue = useUpdateVenue({ id: venueId });


    const handleStopConfirm = () => {
        confirmModalAction(
            t('Are you sure? This will erase all recorded monitoring data for this venue.'),
            async () => {
                try {
                    await updateVenue.mutateAsync({ params: { boards: [] } });
                    await deleteAnalytics.mutateAsync(boardId);
                    message.success(t('analytics.stop_monitoring_success'));
                    onSuccess?.();
                } catch (e) {
                    message.error(
                        t('analytics.stop_monitoring_error', {
                            e: (e as AxiosError)?.response?.data?.ErrorDescription,
                        }),
                    );
                }
            },
            () => { }, 
            true
        );
    };

    return (
        <Button
            className="stop-button"
            onClick={handleStopConfirm}
            style={{ width: '133px', height: '36px' }}
            loading={deleteAnalytics.isLoading || updateVenue.isLoading}
        >
            {t('Stop Monitoring')}
        </Button>
    );
};

export default StopMonitoringButton;