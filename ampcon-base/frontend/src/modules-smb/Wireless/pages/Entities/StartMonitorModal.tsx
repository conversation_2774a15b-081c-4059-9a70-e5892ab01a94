import React, { useEffect, useState } from 'react';
import { Modal, Form, InputNumber, Button, message, Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import * as Yup from 'yup';
import { useFormik } from 'formik';
import { useCreateAnalyticsBoard } from '@/modules-smb/hooks/Network/Analytics';
import { useGetVenue, useUpdateVenue } from '@/modules-smb/hooks/Network/Venues';
import { AxiosError } from '@/modules-smb/models/Axios';

interface Props {
  id: string;
  visible: boolean;
  onClose: () => void;
  onApplySuccess?: (value: boolean) => void; 
}

const StartMonitorModal = ({ id, visible, onClose, onApplySuccess }: Props) => {
  const { t } = useTranslation();
  const [formKey, setFormKey] = useState(uuid());
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const getVenue = useGetVenue({ id });
  const createAnalytics = useCreateAnalyticsBoard();
  const updateVenue = useUpdateVenue({ id });

  useEffect(() => {
    if (visible) {
      setFormKey(uuid());
    }
  }, [visible]);

  const validationSchema = Yup.object().shape({
    interval: Yup.number().required(t('form.required')).moreThan(0).integer(),
    retention: Yup.number().required(t('form.required')).moreThan(0).integer(),
  });

  const formik = useFormik({
    enableReinitialize: true,
    validationSchema,
    validateOnMount: true,
    initialValues: {
      name: getVenue.data?.name ?? '',
      interval: 60,
      retention: 7,
      monitorSubVenues: true,
    },
    onSubmit: async (values, { setSubmitting, resetForm }) => {
      try {
        const { name, interval, retention, monitorSubVenues } = values;
        const retentionInSeconds = retention * 24 * 60 * 60;
        const res = await createAnalytics.mutateAsync({
          name,
          venueList: [{
            id,
            name,
            interval,
            retention: retentionInSeconds,
            monitorSubVenues
          }],
        });
        const boardId = res?.data?.id;

        await updateVenue.mutateAsync({
          params: { boards: [boardId] },
        });

        message.success(
          t('crud.success_update_obj', { obj: t('venues.one') }) as string
        );
        resetForm();
        onApplySuccess?.(false);
        onClose();
      } catch (e) {
        message.error(
          t('crud.error_create_obj', {
            obj: t('analytics.board'),
            e: (e as AxiosError)?.response?.data?.ErrorDescription,
          }) as string
        );
      } finally {
        setSubmitting(false);
      }
    },
  });

  const handleClose = () => {
    if (formik.dirty) {
      setIsConfirmOpen(true);
    } else {
      formik.resetForm();
      onClose();
    }
  };

  const handleConfirmClose = () => {
    formik.resetForm();
    setIsConfirmOpen(false);
    onClose();
  };

  return (
    <>
      <Modal
        key={formKey}
        title={t('analytics.create_board')}
        open={visible}
        onCancel={handleClose}
        footer={[
          <Button key="cancel" onClick={handleClose} style={{ width: 100, height: 36 }}>
            {t('common.cancel')}
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => formik.submitForm()}
            disabled={formik.isSubmitting}
            loading={formik.isSubmitting}
            style={{
              width: 100,
              height: 36,
              backgroundColor: '#14C9BB',
              borderColor: '#14C9BB',
            }}
          >
            {t('Apply')}
          </Button>,
        ]}
        width={680}
        bodyStyle={{
          height: 330
        }}
      >
        <Divider style={{ margin: '0px 0px 16px -24px', width: 'calc(100% + 48px)' }} />
        <Form layout="horizontal" labelAlign="left" style={{ marginBottom: 100 }}>
          <Form.Item
            label={
              <span style={{ width: 100, display: 'inline-block' }}>
                {t('analytics.interval')}
                <span style={{ color: 'red', marginLeft: 4 }}>*</span>
              </span>
            }
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 18 }}
            style={{ marginBottom: 24 }}
            validateStatus={formik.errors.interval && formik.touched.interval ? 'error' : ''}
            help={formik.errors.interval && formik.touched.interval && formik.errors.interval}
          >
            <div className="custom-input-wrapper" style={{ position: 'relative', width: 200 }}>
              <InputNumber
                name="interval"
                value={formik.values.interval}
                onChange={(value) => formik.setFieldValue('interval', value ?? 0)}
                onBlur={() => formik.setFieldTouched('interval', true)}
                //min={1}
                style={{ width: '100%' }}
                controls={true}
                className="always-show-controls"
              />
              <span
                className="unit-label"
                style={{
                  position: 'absolute',
                  right: 30,
                  top: '50%',
                  transform: 'translateY(-50%)',
                  color: '#929A9E',
                  fontSize: 14,
                }}
              >
                {t('common.seconds')}
              </span>
            </div>
          </Form.Item>
          <Form.Item
            label={
              <span style={{ width: 100, display: 'inline-block' }}>
                {t('analytics.retention')}
                <span style={{ color: 'red', marginLeft: 4 }}>*</span>
              </span>
            }
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 18 }}
            style={{ marginBottom: 24 }}
            validateStatus={formik.errors.retention && formik.touched.retention ? 'error' : ''}
            help={formik.errors.retention && formik.touched.retention && formik.errors.retention}
          >
            <div className="custom-input-wrapper" style={{ position: 'relative', width: 200 }}>
              <InputNumber
                name="retention"
                value={formik.values.retention}
                onChange={(value) => formik.setFieldValue('retention', value ?? 0)}
                onBlur={() => formik.setFieldTouched('retention', true)}
                //min={1}
                style={{ width: '100%' }}
                controls={true}
                className="always-show-controls"
              />
              <span
                className="unit-label"
                style={{
                  position: 'absolute',
                  right: 30,
                  top: '50%',
                  transform: 'translateY(-50%)',
                  color: '#929A9E',
                  fontSize: 14,
                }}
              >
                {t('common.days')}
              </span>
            </div>
          </Form.Item>
        </Form>
        <Divider style={{ margin: '220px 0px 16px -24px', width: 'calc(100% + 48px)' }} />
      </Modal>

      <Modal
        title={t('Confirm Discard')}
        open={isConfirmOpen}
        onOk={handleConfirmClose}
        onCancel={() => setIsConfirmOpen(false)}
        okText={t('common.yes')}
        cancelText={t('common.cancel')}
        zIndex={2000}
        getContainer={false}
      >
        <p>{t('Are you sure you want to discard the changes?')}</p>
      </Modal>
    </>
  );
};

export default StartMonitorModal;