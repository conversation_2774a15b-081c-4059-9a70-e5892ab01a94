import React from "react";
import { But<PERSON> } from "antd";
import monitorLogo from "@/modules-smb/Wireless/assets/Monitor/Logo_NOMoitor.png"; 

const NoMonitoringPrompt = ({ onTurnOn }) => {
  return (
    <div
      style={{
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -70%)",
        textAlign: "center",
        backgroundColor: "#fff",
        padding: "40px",
        zIndex: 999,
      }}
    >
      <img
        src={monitorLogo}
        alt="No Data"
        style={{ 
          marginBottom: "0px",
          width: "96px", 
          height: "96px",
        }}
      />
      <div style={{  
        fontSize: "12px", 
        color: "#9DA6B3",
        height: "18px",
        marginBottom: "22px", 
      }}>
        No Data
      </div>
      <p
        style={{
          marginBottom: "40px",
          width: "739px", 
          height: "auto", 
          fontFamily: "Lato, Lato",
          fontWeight: 600,
          fontSize: "16px",
          color: "#212519",
        }}
      >
        Monitoring not activated on this site.<br />
        Please activate it by using the "Monitor" button at the top right of the
        screen or the button at the bottom
      </p>
       <Button 
        type="primary" 
        onClick={onTurnOn}
        style={{ 
          width: "175px", 
          height: "36px",
        }}
      >
        Turn on the monitoring
      </Button>
    </div>
  );
};

export default NoMonitoringPrompt;