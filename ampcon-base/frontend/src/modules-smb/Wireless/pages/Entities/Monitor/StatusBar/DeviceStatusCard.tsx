import React from 'react';
import { Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { useVenueMonitoring } from '../VenueMonitoringContext';
import CommonMetricCard from '@/modules-smb/Wireless/components/Card/CommonMetricCard';

const { Title, Text } = Typography;

const DeviceStatusCard = () => {
  const { t } = useTranslation();
  const { dashboard, handleDashboardModalOpen } = useVenueMonitoring();
  const cardContent = (
    <div style={{
      display: 'flex',
      justifyContent: 'space-around',
      alignItems: 'center',
      padding: '24px 8px',
      height: '100%'
    }}>
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginRight: 32 }}>
        <Text style={{ fontSize: '24px', fontWeight: 'bold' }}>{dashboard?.connectedDevices ?? 0}</Text>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ width: 10, height: 10, borderRadius: '50%', background: '#52c41a', marginRight: 4 }} />
          <Text style={{ fontSize: '16px' ,whiteSpace: 'nowrap'}}>{t('analytics.connected')}</Text>
        </div>
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Text style={{ fontSize: '24px', fontWeight: 'bold' }}>{dashboard?.disconnectedDevices ?? 0}</Text>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ width: 10, height: 10, borderRadius: '50%', background: '#ccc', marginRight: 4 }} />
          <Text style={{ fontSize: '16px', color: '#999',whiteSpace: 'nowrap' }}>{t('analytics.disconnected')}</Text>
        </div>
      </div>
    </div>
  );

  return (
    <CommonMetricCard
      title="common.status"
      explanation={{
        key: "analytics.total_devices_explanation",
        params: {
          connectedCount: dashboard?.connectedDevices ?? 0,
          disconnectedCount: dashboard?.disconnectedDevices ?? 0,
        }
      }}
      bgGradient="linear-gradient(180deg, #F6F7FF 0%, #ECECFF 100%)"
      content={cardContent}
       buttonColor="#DCE0FD" // 添加按钮hover颜色
      onDetailClick={() =>
        handleDashboardModalOpen({
          prioritizedColumns: ['connected'],
          sortBy: [{ id: 'connected', desc: true }],
        })
      }
    />
  );
};

export default DeviceStatusCard;
