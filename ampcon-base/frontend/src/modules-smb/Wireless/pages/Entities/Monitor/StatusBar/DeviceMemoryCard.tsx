import React from 'react';
import { Typography } from 'antd';
import { useTranslation } from "react-i18next";
import { useVenueMonitoring } from '../VenueMonitoringContext';
import CommonMetricCard from '../../../../components/Card/CommonMetricCard';
import { CircularProgressEcharts } from '@/modules-smb/Wireless/components/Echarts/CircularEcharts';

const { Text } = Typography;

const DeviceMemoryCard = () => {
  const { t } = useTranslation();
  const { dashboard, handleDashboardModalOpen } = useVenueMonitoring();

  const avgMemoryUsed = `${dashboard?.avgMemoryUsed ?? 0}%`;

  return (
    <CommonMetricCard
      title="analytics.average_memory"
      explanation="analytics.average_memory_explanation"
      bgGradient="linear-gradient(180deg, #F5FEF2 0%, #E6FEEE 100%)"
       buttonColor="#DDF6D9 " // 添加按钮hover颜色
      content={
        <div style={{
          display: 'flex',
          alignItems: 'center', 
          height: '100%',       
          padding: 0,
        }}>
          <Text
            style={{
              fontSize: '24px',
              fontWeight: 'bold',
              marginLeft: '32px', 
              whiteSpace: 'nowrap',
            }}
          >
            {avgMemoryUsed}
          </Text>

          <div style={{ flex: 1 }} />

          <CircularProgressEcharts
            value={dashboard?.avgMemoryUsed ?? 0}
            style={{
              marginRight: '32px', 
            }}
          />
        </div>
      }
      onDetailClick={() =>
        handleDashboardModalOpen({
          prioritizedColumns: ['lastPing', 'memory'],
          sortBy: [{ id: 'memory', desc: true }],
        })
      }
    />
  );
};

export default DeviceMemoryCard;
