import React from 'react';
import { Center, Spinner, useDisclosure } from '@chakra-ui/react';
import VenueDashboardTableModal from '../StatusBar/TableModal';
import { message } from 'antd';
import {
  ParsedDashboardData,
  parseDashboardData,
  parseTimepointsData,
  ParsedTimepointsData,
  parseTimepointsToMonitoringData,
  ApMonitoringData,
  SsidMonitoringData,
  UeMonitoringData,
  RadioMonitoringData,
} from '../utils';
import { useGetAnalyticsBoardDevices, useGetAnalyticsBoardTimepoints } from '@/modules-smb/hooks/Network/Analytics';
import { useQuery } from '@tanstack/react-query';
import { useGetVenue } from '@/modules-smb/hooks/Network/Venues';
import { getAnalyticsBoardTimepoints } from '@/modules-smb/Wireless/apis/wireless_client_api';

const tenMinutesAgo = () => {
  const newDate = new Date();
  newDate.setMinutes(newDate.getMinutes() - 10);
  return newDate;
};

type SelectedItem =
  | {
      type: 'AP';
      data: ApMonitoringData;
    }
  | {
      type: 'SSID';
      data: SsidMonitoringData;
    }
  | {
      type: 'UE';
      data: UeMonitoringData;
    }
  | {
      type: 'RADIO';
      serialNumber: string;
      data: RadioMonitoringData;
    };

type DashboardModalOpenOptions = {
  prioritizedColumns?: string[];
  sortBy?: { id: string; desc: boolean }[];
};

type UseVenueMonitoringReturn = {
  venueId: string;
  dashboard: ParsedDashboardData;
  timepoints: ParsedTimepointsData;
  monitoring: ApMonitoringData[];
  handleDashboardModalOpen: (options: DashboardModalOpenOptions) => void;
  selectedItem: SelectedItem | undefined;
  onSelectItem: (item: SelectedItem) => void;
  onUnselectItem: () => void;
};

const VenueMonitoringContext = React.createContext<UseVenueMonitoringReturn>({
  venueId: '',
} as UseVenueMonitoringReturn);

export const VenueMonitoringProvider = ({ venueId, venueData, children }: { venueId: string; venueData?: any; children: React.ReactElement }) => {
  const dashboardModalProps = useDisclosure();
  const [dashboardTableOptions, setDashboardTableOptions] = React.useState<DashboardModalOpenOptions>();
   const boardId = venueData?.boards?.[0]; 
  const getDashboard = useGetAnalyticsBoardDevices({ id: boardId });
  const [startTimepointTime] = React.useState(tenMinutesAgo());

  // 替换ampcon接口获取timepoints
  const getTimepoints = useQuery(
    ['get-venue-timepoints', boardId, startTimepointTime.toString()],
    async () => {
      if (!boardId) return [];
      try {
        const resp = await getAnalyticsBoardTimepoints({ id: boardId, startTime: startTimepointTime });
        if (!resp) return [];
        if (resp.status !== 200) {
          message.error(resp.info);
          return [];
        }
        return resp.info || [];
      } catch (e) {
        message.error('Failed to fetch timepoints');
        return [];
      }
    },
    {
      enabled: !!boardId,
      keepPreviousData: true,
      staleTime: Infinity,
    }
  );
  const [selectedItem, setSelectedItem] = React.useState<SelectedItem>();

  const onSelectItem = React.useCallback((item: SelectedItem) => {
    setSelectedItem(item);
  }, []);

  const onUnselectItem = React.useCallback(() => {
    setSelectedItem(undefined);
  }, []);

  const handleDashboardModalOpen = React.useCallback((tableOptions: DashboardModalOpenOptions) => {
    setDashboardTableOptions(tableOptions);
    dashboardModalProps.onOpen();
  }, []);

  const parsedDashboardData = React.useMemo(() => {
    if (!getDashboard.data) return undefined;

    return parseDashboardData(getDashboard.data);
  }, [getDashboard.data]);

  // const parsedClientTimepoints = React.useMemo(() => {
  //   if (!getTimepoints.data) return undefined;

  //   return parseTimepointsData(getTimepoints.data);
  // }, [getTimepoints.data]);

  const monitoringData = React.useMemo(() => {
    if (!getTimepoints.data || !parsedDashboardData) return undefined;

    return parseTimepointsToMonitoringData(getTimepoints.data, parsedDashboardData.devices);
  }, [getTimepoints.data, parsedDashboardData]);

  const value = React.useMemo(
    () => ({
      venueId,
      dashboard: parsedDashboardData as ParsedDashboardData,
      timepoints: getTimepoints.data as ParsedTimepointsData,
      monitoring: monitoringData as ApMonitoringData[],
      handleDashboardModalOpen,
      selectedItem,
      onSelectItem,
      onUnselectItem,
    }),
    [venueId, parsedDashboardData, getTimepoints.data, monitoringData, handleDashboardModalOpen, selectedItem],
  );

  // 监听 venueId 变化时清空选中项
  React.useEffect(() => {
    setSelectedItem(undefined);
  }, [venueId]);
  
  return (
    <VenueMonitoringContext.Provider value={value}>
    
        <>
          {children}
          <VenueDashboardTableModal
            data={value.dashboard}
            tableOptions={dashboardTableOptions}
            {...dashboardModalProps}
          />
        </>
    
    </VenueMonitoringContext.Provider>
  );
};

export const useVenueMonitoring = () => React.useContext(VenueMonitoringContext);
