import React, { useCallback, useMemo, useState, useEffect } from 'react';
import { Space, Typography, Divider, Card } from 'antd'; // 导入Ant Design的Card
import { useTranslation } from 'react-i18next';
import FormattedDate from '@/modules-smb/components/FormattedDate';
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
import BooleanCell from '@/modules-smb/components/TableCells/BooleanCell';
import DataCell from '@/modules-smb/components/TableCells/DataCell';
import DecibelCell from '@/modules-smb/components/TableCells/DecibelCell';
import DurationCell from '@/modules-smb/components/TableCells/DurationCell';
import MillisecondsDuration from '@/modules-smb/Wireless/components/TableCells/MillisecondsDuration';
import NumberCell from '@/modules-smb/components/TableCells/NumberCell';
import {
  useGetClientLifecycle,
  useGetClientLifecycleCount,
} from '@/modules-smb/hooks/Network/Analytics';
import useControlledTable from '@/modules-smb/hooks/useControlledTable';
import { Column, SortInfo } from '@/modules-smb/models/Table';
import { UseQueryResult } from '@tanstack/react-query';

const { Title } = Typography;

interface PageInfo {
  index: number;
  limit: number;
}

const ClientLifecyleTable: React.FC<{
  venueId: string;
  mac?: string;
  fromDate: number;
  endDate: number;
  timePickers: React.ReactNode;
  searchBar: React.ReactNode;
}> = ({ venueId, mac, fromDate, endDate, timePickers, searchBar }) => {
  const { t } = useTranslation();
  const [sortInfo, setSortInfo] = useState<SortInfo>([{ id: 'timestamp', sort: 'dsc' }]);
  const [pageInfo, setPageInfo] = useState<PageInfo>({ index: 0, limit: 10 });
  useEffect(() => {
    if (mac) {
      setPageInfo({ index: 0, limit: 10 });
      setSortInfo([{ id: 'timestamp', sort: 'dsc' }]);
    }
  }, [mac]);
  const { count, data: lifecycles, isFetching } = useControlledTable({
    useCount: useGetClientLifecycleCount as (props: unknown) => UseQueryResult,
    useGet: useGetClientLifecycle as (props: unknown) => UseQueryResult,
    countParams: { venueId, mac, sortInfo, fromDate, endDate },
    getParams: { venueId, mac, sortInfo, fromDate, endDate, pageInfo },
  });

  // 正确接收参数：pagination（分页）、filters（筛选）、sorter（排序）
  const handleTableChange = (pagination, filters, sorter) => {
    setPageInfo({
      index: pagination.current - 1,
      limit: pagination.pageSize,
    });


    if (sorter.field) {
      setSortInfo([{
        id: sorter.field,
        sort: sorter.order === 'ascend' ? 'asc' : 'desc'
      }]);
    }
  };

  const columns = useMemo((): Column[] => [
    {
      key: 'timestamp',
      title: t('common.timestamp'),
      dataIndex: 'timestamp',
      render: (value) => <FormattedDate date={value} />,
      fixed: 'left',
      sorter: true,
      isMonospace: true,
      columnsFix:true
    },
    {
      key: 'bssid',
      title: 'BSSID',
      dataIndex: 'bssid',
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'ssid',
      title: 'SSID',
      dataIndex: 'ssid',
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'rssi',
      title: `RSSI(db)`,
      dataIndex: 'rssi',
      render: (value) => <DecibelCell db={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'noise',
      title: `${t('analytics.noise')}(db)`,
      dataIndex: 'noise',
      render: (value) => <DecibelCell db={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'channel',
      title: t('analytics.channel'),
      dataIndex: 'channel',
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'tx_power',
      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          TX Power
        </span>
      ),
      dataIndex: 'tx_power',
      sorter: true,
      isMonospace: true,
    },
    {
      key: 'tx_retries',
      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          TX {t('analytics.retries')}
        </span>
      ),
      dataIndex: 'tx_retries',
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'connected',
      title: t('analytics.connected'),
      dataIndex: 'connected',
      render: (value) => <DurationCell seconds={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'inactive',
      title: t('analytics.inactive'),
      dataIndex: 'inactive',
      render: (value) => <DurationCell seconds={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'ack_signal',
      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          {t('analytics.ack_signal')}(db)
        </span>
      ),
      dataIndex: 'ack_signal',
      render: (value) => <DecibelCell db={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'ack_signal_avg',
      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          {t('analytics.ack_signal')}{t('common.avg')}(db)
        </span>
      ),
      dataIndex: 'ack_signal_avg',
      render: (value) => <DecibelCell db={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'rx_bytes',
      title: 'RX',
      dataIndex: 'rx_bytes',
      render: (value) => <DataCell bytes={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'rx_mcs',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          RX MCS
        </span>
      ),
      dataIndex: 'rx_mcs',
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'rx_nss',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          RX NSS
        </span>
      ),
      dataIndex: 'rx_nss',
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'tx_bytes',
      title: 'TX',
      dataIndex: 'tx_bytes',
      render: (value) => <DataCell bytes={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'tx_mcs',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          TX MCS
        </span>
      ),
      dataIndex: 'tx_mcs',
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'tx_nss',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          TX NSS
        </span>
      ),
      dataIndex: 'tx_nss',
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'rx_bitrate',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          RX Bitrate
        </span>
      ),
      dataIndex: 'rx_bitrate',
      render: (value) => <NumberCell value={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'rx_chwidth',
      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          RX Ch Width
        </span>
      ),

      dataIndex: 'rx_chwidth',
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'rx_duration',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          RX Duration
        </span>
      ),
      dataIndex: 'rx_duration',
      render: (value) => <MillisecondsDuration milliseconds={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'rx_packets',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          RX Packets
        </span>
      ),
      dataIndex: 'rx_packets',
      render: (value) => <NumberCell value={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'rx_vht',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          RX VHT
        </span>
      ),
      dataIndex: 'rx_vht',
      render: (value) => <BooleanCell isTrue={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'tx_bitrate',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          TX Bitrate
        </span>
      ),
      dataIndex: 'tx_bitrate',
      render: (value) => <NumberCell value={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'tx_chwidth',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          TX Ch Width
        </span>
      ),
      dataIndex: 'tx_chwidth',
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'tx_vht',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          TX VHT
        </span>
      ),
      dataIndex: 'tx_vht',
      render: (value) => <BooleanCell isTrue={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'tx_duration',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          TX Duration
        </span>
      ),
      dataIndex: 'tx_duration',
      render: (value) => <MillisecondsDuration milliseconds={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'tx_packets',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          TX Packets
        </span>
      ),
      dataIndex: 'tx_packets',
      render: (value) => <NumberCell value={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'ipv4',
      title: 'IPv4',
      dataIndex: 'ipv4',
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'ipv6',
      title: 'IPv6',
      dataIndex: 'ipv6',
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'channel_width',
      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          Ch Width
        </span>
      ),

      dataIndex: 'channel_width',
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'active_ms',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          Active MS
        </span>
      ),
      dataIndex: 'active_ms',
      render: (value) => <MillisecondsDuration milliseconds={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'busy_ms',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          Busy MS
        </span>
      ),
      dataIndex: 'busy_ms',
      render: (value) => <MillisecondsDuration milliseconds={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'receive_ms',

      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          Receive MS
        </span>
      ),
      dataIndex: 'receive_ms',

      render: (value) => <MillisecondsDuration milliseconds={value} />,
      sorter: true,
      isMonospace: true,

    },
    {
      key: 'mode',
      title: t('analytics.mode'),
      dataIndex: 'mode',
      sorter: true,
      isMonospace: true,

    },
  ], [t]);

  return (
    <Card
      title={
        <Title level={4} style={{ margin: 0,fontSize: '18px' }}>
          {t('analytics.client_lifecycle')}
        </Title>
      }
      style={{
        height: '100%',
        border: 'none', // 移除卡片边框线
      }}
      bodyStyle={{
        padding: '16px',
        minWidth: '100%',
        overflow: 'auto',
        maxWidth: '70vw',
        marginBottom: '6px',
      }}
    >

      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: '20px',
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ marginLeft: '12px', marginRight: '32px', fontSize: '14px' }}>Time</span>
          <div>{timePickers}</div>
        </div>
        <div style={{ marginRight: '2px' }}>{searchBar}</div>
      </div>
      {
        <WirelessCustomTable
          columns={columns}
          dataSource={lifecycles || []}
          loading={isFetching}
          onChange={handleTableChange}
          showColumnSelector={true}
          fetchAPIInfo={useGetClientLifecycle}
          pagination={{
            current: pageInfo.index + 1,
            pageSize: pageInfo.limit,
            total: count,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} items`,
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
         //    scroll={{ x: 'max-content' }}
        />


      }
    </Card>
  );
};

export default ClientLifecyleTable;