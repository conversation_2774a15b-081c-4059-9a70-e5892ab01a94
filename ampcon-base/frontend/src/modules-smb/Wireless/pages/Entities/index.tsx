import React, { useEffect, useState, useMemo } from "react";
import { <PERSON>bs, Button, Space, Tooltip, Flex, message } from "antd";
import Icon from "@ant-design/icons";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

import StartMonitorModal from "./StartMonitorModal";
import ViewMonitoringModal from "./ViewMonitoringModal";
import { useEntityFavorite } from "@/modules-smb/hooks/useEntityFavorite";
import ProtectedRoute from "@/modules-ampcon/utils/util";
import VenueSelect from "@/modules-smb/Wireless/components/VenueSelect";
import MonitorPage from "./Monitor";
import Configure from "./Configure";
import RRMOptimize from "./RRMOptimize";
import { useGetVenue } from "@/modules-smb/hooks/Network/Venues";
import { useGetAnalyticsBoards } from "@/modules-smb/Wireless/hooks/Network/Analytics";
import VenueActions from "./VenueActions";

import monitorSvg from "../../assets/Entities/venue_monitor.svg?react";
import setttingSvg from "../../assets/Entities/venue_setting.svg?react";
import refreshSvg from "../../assets/Entities/venue_refresh.svg?react";
import NoMonitoringPrompt from "@/modules-smb/Wireless/pages/Entities/Monitor/NoMonitoringPrompt";
import { useSiteStore } from "@/modules-smb/Wireless/components/SiteContext";
import { useSiteSelection } from "@/modules-smb/Wireless/hooks/useSelectedSite";
import { useUrlSync } from "@/modules-smb/Wireless/hooks/useUrlSync";
import '@/modules-smb/Wireless/assets/tabs.scss';
import "./index.scss";

const VenuePage = () => {
    const currentUser = useSelector((state: any) => state.user.userInfo);
    const location = useLocation();
    const navigate = useNavigate();
    const {
        selectedSiteId,
        handleSiteChange,
        displaySiteId
    } = useSiteSelection(false);
    //const { selectedSiteId, setSelectedSiteId } = useSiteStore();
    const [currentActiveKey, setCurrentActiveKey] = useState("");
    const [refreshKey, setRefreshKey] = useState(0);
    const [monitorModalVisible, setMonitorModalVisible] = useState(false);
    const [settingsModalVisible, setSettingsModalVisible] = useState(false);
    const [showMonitorPrompt, setShowMonitorPrompt] = useState(false);

    const { getFirstVenueFavoriteId } = useEntityFavorite({
        id: "",
        type: "venue"
    });

    const selectedVenueId = useMemo(() => {
        const hash = window.location.hash.substring(1);
        const venueId = selectedSiteId || hash || getFirstVenueFavoriteId?.() || "0";

        return venueId;
    }, [selectedSiteId, location.hash]);


    const getVenue = useGetVenue({ id: selectedVenueId || "0" });

    const getAnalyticsBoards = useGetAnalyticsBoards({ venueId: selectedVenueId || "0" });
    const hasMonitoring = getVenue.data?.boards?.length > 0;
    const boardId = getVenue.data?.boards?.[0] || "";
    const { resetFromOtherPage } = useSiteStore();

    useUrlSync();

    const handleTurnOnMonitoring = () => {
        setMonitorModalVisible(true);
    };

    useEffect(() => {
        if (getAnalyticsBoards.data) {
            setShowMonitorPrompt(!(getAnalyticsBoards.data.boards?.length > 0));
        }
    }, [getAnalyticsBoards.data]);


    const handleMonitor = () => {
        if (!selectedVenueId) {
            message.warning("Please select a venue first");
            return;
        }
        getVenue.refetch();
        const hasMon = getVenue.data?.boards?.length > 0;
        if (hasMon) {
            setSettingsModalVisible(true);
        } else {
            setMonitorModalVisible(true);
        }
    };

    const handleSetting = (action: string) => {
    };

    const handleRefresh = () => {
        setRefreshKey(prev => prev + 1);
        getVenue.refetch();
        message.success("Successfully refreshed");
    };

    const MonitorPageWithVenue = React.useMemo(() => {
        return () => <MonitorPage venueId={selectedVenueId} venueData={getVenue.data} />;
    }, [selectedVenueId, getVenue.data]);

    // useEffect(() => {
    //     getAnalyticsBoards.refetch();
    // });

    const allItems = [
        {
            key: "Monitor",
            label: "Monitor",
            children: showMonitorPrompt ? (
                <NoMonitoringPrompt onTurnOn={handleTurnOnMonitoring} />
            ) : (
                <ProtectedRoute component={MonitorPageWithVenue} />
            ),
        },
        {
            key: "Configure",
            label: "Configure",
            children: <ProtectedRoute component={Configure} />,
        },
        {
            key: "RRM-Optimize",
            label: "RRM Optimize",
            children: <ProtectedRoute component={RRMOptimize} />,
        },
    ];

    const items: any[] = currentUser.type === "readonly" ? [] : allItems;


    useEffect(() => {
        const match = location.pathname.match(/(Monitor|Configure|RRM-Optimize)$/);
        if (match) {
            setCurrentActiveKey(match[0]);
        } else if (items.length > 0) {
            setCurrentActiveKey(items[0].key);
            let path = `${location.pathname.replace(/\/$/, "")}/${items[0].key}`;
            if (selectedSiteId) {
                path += `#${selectedSiteId}`;
            }
            navigate(path, { replace: true });
        }
    }, [location.pathname, items, selectedSiteId]);

    const onChange = (key: string) => {
        let path = location.pathname.replace(/(Monitor|Configure|RRM-Optimize)$/, "");
        path = `${path.replace(/\/$/, "")}/${key}`;
        if (selectedVenueId) {
            path += `#${selectedVenueId}`;
        }
        navigate(path);
    };

    return (
        <Flex className="div-main">
            <div className="div-header">
                <VenueSelect onChange={handleSiteChange} />
                <Space className="action-buttons" style={{ paddingTop: 10 }}>
                    <Tooltip title="Monitoring" placement="bottom">
                        <Button
                            icon={<Icon component={monitorSvg} />}
                            type="text"
                            onClick={handleMonitor}
                            style={{ width: 40, height: 40, borderRadius: 4 }}
                            className="monitor-button"
                        />
                    </Tooltip>
                    <VenueActions
                        venueId={selectedVenueId}
                        isDisabled={!getVenue.data}
                    />
                    <Tooltip title="Refresh" placement="bottom">
                        <Button
                            icon={<Icon component={refreshSvg} />}
                            type="text"
                            onClick={handleRefresh}
                            style={{ width: 40, height: 40, borderRadius: 4 }}
                            className="refresh-button"
                        />
                    </Tooltip>
                </Space>
            </div>

            <div className="div-tabs">
                <Tabs
                    key={refreshKey}
                    activeKey={currentActiveKey}
                    onChange={onChange}
                    destroyInactiveTabPane
                    items={items}
                />
                {selectedVenueId && (
                    <>
                        <StartMonitorModal
                            id={selectedVenueId}
                            visible={monitorModalVisible}
                            onClose={() => {
                                setMonitorModalVisible(false);
                            }}
                            onApplySuccess={setShowMonitorPrompt}
                        />
                        <ViewMonitoringModal
                            boardId={boardId}
                            venueId={selectedVenueId}
                            visible={settingsModalVisible}
                            onClose={() => setSettingsModalVisible(false)}
                            onApplySuccess={setShowMonitorPrompt}
                        />
                    </>
                )}
            </div>

        </Flex>
    );
};

export default VenuePage;
