import { useDisclosure } from '@chakra-ui/react';
import React from 'react';
import { Button, Typography, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import DeviceActionDropdown from '@/modules-smb/Wireless/components/TableCells/DeviceActionDropdown';
import ConfigurationPushModal from '@/modules-smb/Wireless/components/Tables/InventoryTable/ConfigurationPushModal';
import { useGetGatewayUi } from '@/modules-smb/hooks/Network/Endpoints';
import { useDeleteTag, usePushConfig } from '@/modules-smb/hooks/Network/Inventory';
import { Device } from '@/modules-smb/models/Device';
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";

interface Props {
  cell: { original: Device };
  refreshTable: () => void;
  openEditModal: (dev: Device) => void;
  // onOpenScan: (serialNumber: string) => void;
  onOpenFactoryReset: (serialNumber: string) => void;
  onOpenUpgradeModal: (serialNumber: string) => void;
}

const Actions: React.FC<Props> = ({
  cell: { original: tag },
  refreshTable,
  openEditModal,
  // onOpenScan,
  onOpenFactoryReset,
  onOpenUpgradeModal,
}) => {
  const { t } = useTranslation();
  const { onOpen, onClose } = useDisclosure();
  const { isOpen: isPushOpen, onOpen: openPush, onClose: closePush } = useDisclosure();
  const [isConfirmed, setIsConfirmed] = React.useState(false);
  const { data: gwUi } = useGetGatewayUi();
  const { mutateAsync: deleteConfig } = useDeleteTag({
    name: tag.name,
    refreshTable,
    onClose,
  });

  const pushConfiguration = usePushConfig({
    onSuccess: () => {
      if (isConfirmed) { // 只有用户确认后才弹
        openPush();
        setIsConfirmed(false); // 重置标志位
      }
    },
  });
  
  const isVenueEmpty = tag.venue === undefined || tag.venue === null || tag.venue === '';
  const handleDeleteClick = () => deleteConfig(tag.serialNumber);
  const handleOpenEdit = () => openEditModal(tag);
  const target = window.location.origin;
  const handleOpenInGateway = () => window.open(`${target}/wireless/devices/${tag.serialNumber}#/devices/${tag.serialNumber}`, '_blank');
  
  const handleSyncConfig = () => {
    confirmModalAction(
      <Typography.Paragraph style={{ display: 'flex', alignItems: 'center' }}>
        <span>
          Are you sure you want to push the configuration to device <br />
          <b>#{tag.serialNumber}</b>?<br />
          <br />
          You cannot undo this action afterwards.
        </span>
      </Typography.Paragraph>,
      () => {
        setIsConfirmed(true); // 用户点击 Yes
        return pushConfiguration.mutateAsync(tag.serialNumber);
      },
      () => {
        setIsConfirmed(false); // 用户点击 No / 取消
      },
      { confirmLoading: pushConfiguration.isLoading }
    );
  };
  
  const handleDelete = () => {
    confirmModalAction(
      `Are you sure you want to delete this Device?`,
      handleDeleteClick
    );
  };
  
  return (
    <Space size={24}>
      {/* View 按钮 */}
      <Button 
        type="link" 
        onClick={handleOpenEdit}
        style={{ padding: 0 }}
      >
        {t('common.edit')}
      </Button>

      {/* View In Controller 按钮 */}
      {/* <Button 
        type="link" 
        onClick={handleOpenInGateway}
        style={{ padding: 0 }}
      >
        {t('common.view_in_gateway')}
      </Button> */}

      {/* Push Configuration 按钮 */}
      <Button
        type="link"
        onClick={handleSyncConfig}
        style={{
          margin: 0,
          padding: '0 ',
          color: isVenueEmpty ? '#B3BBC8' : undefined, // 为空时改变颜色
          //pointerEvents: isVenueEmpty ? 'none' : 'auto' // 为空时禁用点击事件
        }}
        disabled={isVenueEmpty} // 禁用按钮交互
      >
        {t('configurations.push_configuration')}
      </Button>

      {/* Action */}
      <DeviceActionDropdown
        device={tag}
        refresh={refreshTable}
        onOpenFactoryReset={onOpenFactoryReset}
        onOpenUpgradeModal={onOpenUpgradeModal}
      />

      {/* Delete 按钮 */}
      <Button
        type="link"
        onClick={handleDelete}
        style={{ padding: 0 }}
      >
        {t('crud.delete')}
      </Button>

      <ConfigurationPushModal
        isOpen={isPushOpen}
        onClose={closePush}
        pushResult={pushConfiguration.data}
      />
    </Space>
  );
};

export default Actions;