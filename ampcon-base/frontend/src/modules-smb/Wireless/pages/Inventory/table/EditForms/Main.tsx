import React, { useEffect, useState } from 'react';
import { Form, Input, Select, Switch, Row, Col } from 'antd';
import { useGetDeviceTypes } from "@/modules-ampcon/apis/upgrade_api";
import { useGetVenues } from '@/modules-smb/hooks/Network/Venues';
import { useTranslation } from 'react-i18next';
import { fetchLables } from '@/modules-smb/Wireless/apis/lable';
import '@/modules-smb/Wireless/assets/form.scss';

interface MainProps {
  value: any;
  onChange: (v: any) => void;
}

const Main: React.FC<MainProps> = ({ value, onChange }) => {
  const { t } = useTranslation();
  const { data: deviceTypesList } = useGetDeviceTypes();
  const { data: venues } = useGetVenues();
  const [labelsOptions, setLabelsOptions] = useState<any[]>([]);
 
  useEffect(() => {
    const fetch = async () => {
      if (!value?.venue) {
        setLabelsOptions([]);
        return;
      }
      try {
        const res = await fetchLables({ siteId: parseInt(value.venue) });
        setLabelsOptions((res?.info || []).map((item: any) => ({ label: item.name, value: item.name })));
      } catch {
        setLabelsOptions([]);
      }
    };
    fetch();
  }, [value?.venue]);

  // 处理表单初始值
  const initialLabels = value?.labelsName ? value.labelsName.split(',').filter(Boolean) : [];

  const handleValuesChange = (_: any, allValues: any) => {
    if (Array.isArray(allValues.labelsName)) {
      allValues.labelsName = allValues.labelsName.join(',');
    }
    onChange(allValues);
  };

  return (
    <Form
      initialValues={{ ...value, labelsName: initialLabels }}
      onValuesChange={handleValuesChange}
      className="wirelessForm InventoryMainForm"
    >
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item label={t('inventory.serial_number')} name="serialNumber" rules={[{ required: true, message: t('form.required') }]}> 
            <Input disabled/>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label={t('common.name')} name="name" rules={[
            { required: true, message: t('form.required') },
            { type: 'string', max: 50, message: t('form.max_length', { max: 50 }) },
          ]}> 
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item label={t('common.description')} name="description" rules={[{ max: 128, message: t('form.max_length', { max: 128 }) }]}> 
            <Input />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label={t('inventory.device_type')} name="deviceType" rules={[{ required: true, message: t('form.required') }]}> 
            <Select options={deviceTypesList.map((d: any) => ({ label: d, value: d }))} showSearch />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item label='Site' name="venue">
            <Select
              options={Array.isArray(venues) ? venues.map((v: any) => ({ label: v.name, value: String(v.id), key: v.id })) : []}
              showSearch
              optionFilterProp="label"
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label='Label' name="labelsName">
            <Select
              mode="multiple"
              options={labelsOptions}
              showSearch
              optionFilterProp="label"
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24} style={{ display: 'none' }}>
        <Col span={12}>
          <Form.Item label={t('overrides.ignore_overrides')} name="doNotAllowOverrides" valuePropName="checked"  rules={[{ required: true, message: t('form.required') }]}>
            <Switch />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default Main;

