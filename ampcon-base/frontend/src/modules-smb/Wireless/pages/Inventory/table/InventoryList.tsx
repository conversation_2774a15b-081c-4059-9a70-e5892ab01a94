import React, { useState, useEffect, useMemo, useCallback, useRef, forwardRef, useImperativeHandle } from 'react';
import { useDisclosure } from '@chakra-ui/react';
import { Card, Form, Switch, Space, message, Button, Dropdown, Menu, Select, Alert } from 'antd';
import { CellContext } from '@tanstack/react-table';
import { useTranslation } from 'react-i18next';
import { SortInfo } from '@/modules-smb/models/Table';
import { v4 as uuid } from 'uuid';
import Actions from './Actions';
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
// import ExportDevicesTableButton from '@/modules-smb/components/ExportInventoryButton';
import ExportDevicesTableButtonAntd from '@/modules-smb/Wireless/components/Button/ExportInventoryButton/indexAntd';
import FormattedDate from '@/modules-smb/components/FormattedDate';
import FactoryResetModal from '@/modules-smb/Wireless/components/Modals/SubscriberDevice/FactoryResetModal';
// import WifiScanModal from '@/modules-smb/Wireless/components/Modals/SubscriberDevice/WifiScanModal';
import DeviceSearchBar from '@/modules-smb/Wireless/components/SearchBar/DeviceSearch';
import VenueCell from '@/modules-smb/Wireless/components/TableCells/VenueCell';
import ConfigurationPushModal from '@/modules-smb/components/Tables/InventoryTable/ConfigurationPushModal';
import CreateConfigurationModal from '@/modules-smb/Wireless/components/Tables/InventoryTable/CreateTagModal';
// import EditTagModal from '@/modules-smb/components/Tables/InveWireless/ntoryTable/EditTagModal';
import EditForms from './EditForms';
import {
  useGetInventoryCount,
  useGetInventoryTags,
  usePushConfig,
} from '@/modules-smb/hooks/Network/Inventory';
import { Device } from '@/modules-smb/models/Device';
import { InventoryTagApiResponse } from '@/modules-smb/models/Inventory';
import ImportDeviceCsvModalAntd from '@/modules-smb/Wireless/components/Button/ImportDeviceCsvModal/indexAntd';
import { refreshSvg, deleteSvg } from "@/utils/common/iconSvg";
import { MenuOutlined } from '@ant-design/icons';
import Icon from "@ant-design/icons";
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";
import useGetEntityFavorite from "@/modules-smb/Wireless/hooks/useGetEntityFavorite";
import { batchDeleteInventory, batchSwitchSite } from "@/modules-smb/Wireless/apis/inventory_api.jsx";
import { FormModal } from '@/modules-smb/Wireless/components/Modals/FormModal';
import { useGetVenues } from '@/modules-smb/Wireless/hooks/Venues';

type InventoryTableProps = {
  venueId?: string;
  onlyUnassigned: boolean;
};
interface PageInfo {
  index: number;
  limit: number;
}
const InventoryTable = forwardRef(({ venueId, onlyUnassigned }: InventoryTableProps, ref) => {

  const { defaultValue } = useGetEntityFavorite();
  let hashVenueId: string | null = null;
  const hash = window.location.hash.replace('#', '');
  if (hash && /^\d+$/.test(hash)) {
    hashVenueId = hash;
  }

  let validDefaultValue = null;
  if (defaultValue && /^\d+$/.test(defaultValue)) {
    validDefaultValue = defaultValue;
  }

  venueId = venueId || hashVenueId || validDefaultValue;
  const { t } = useTranslation();
  const [serialNumber, setSerialNumber] = useState<string>('');
  const [tag, setTag] = useState<Device | { serialNumber: string } | undefined>(undefined);
  const { isOpen: isEditOpen, onOpen: openEdit, onClose: closeEdit } = useDisclosure();
  const { isOpen: isPushOpen, onOpen: openPush, onClose: closePush } = useDisclosure();
  const [isDeleting, setIsDeleting] = useState(false);
  const [sortInfo, setSortInfo] = useState<SortInfo>([{ id: 'serialNumber', sort: 'asc' }]);
  const [pageInfo, setPageInfo] = useState<PageInfo>({ index: 0, limit: 10 });
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const scanModalProps = useDisclosure();
  const resetModalProps = useDisclosure();
  const upgradeModalProps = useDisclosure();
  const pushConfiguration = usePushConfig({ onSuccess: () => openPush() });
  const {
    data: count,
    isFetching: isFetchingCount,
    refetch: refetchCount,
  } = useGetInventoryCount({
    enabled: true,
    onlyUnassigned,
    venueId: venueId
  });

  const {
    data: tags,
    isFetching: isFetchingTags,
    refetch: refetchTags,
  } = useGetInventoryTags({
    pageInfo: pageInfo,
    sortInfo,
    enabled: true,
    count,
    onlyUnassigned,
    venueId: venueId
  });
  const rowSelection = {
    selectedRowKeys,
    selectedRows,
    onChange: (keys, rows) => {
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
    },
  };


  const deleteSelected = async (selectedRowKeys) => {
    if (!selectedRowKeys.length) return;

    setIsDeleting(true);
    try {
      const idList = selectedRowKeys;
      await batchDeleteInventory(idList);
      message.success('Batch delete inventory success.');
      refetchCount();
      refetchTags();
      setSelectedRowKeys([]);
    } catch (error) {
      message.error(`Failed to delete devices: ${error?.response?.data?.ErrorDescription || 'Unknown error'}`);
    } finally {
      setIsDeleting(false);
    }
  };

  const serialCell = React.useCallback(
    (device) => {
      return (<a href={`/wireless/devices/${device.serialNumber}#/devices/${device.serialNumber}`} style={{ color: '#14C9BB ', textDecoration: 'underline' }} >
        <pre>{device.serialNumber}</pre>
      </a>
      );
    },
    [],);


  // 新增：删除确认逻辑（使用 confirmModalAction）
  const handleDeleteSelected = () => {
    // 检查是否有选中的行
    if (selectedRowKeys.length === 0) {
      message.warning('Please select at least one piece of data');
      return;
    }

    confirmModalAction(
      `Are you sure you need to delete the selected device?`,
      () => {
        deleteSelected(selectedRowKeys); // 执行删除
      }
    );
  };

  const handleTableChange = (pagination, filters, sorter) => {
    setPageInfo({
      index: pagination.current - 1,
      limit: pagination.pageSize,
    });
    if (sorter.field) {
      setSortInfo([{
        id: sorter.field,
        sort: sorter.order === 'ascend' ? 'asc' : 'desc'
      }]);
    }
  };

  const onOpenScan = (serial: string) => {
    setSerialNumber(serial);
    scanModalProps.onOpen();
  };

  const onOpenFactoryReset = (serial: string) => {
    setSerialNumber(serial);
    resetModalProps.onOpen();
  };

  const onOpenUpgradeModal = (serial: string) => {
    setSerialNumber(serial);
    upgradeModalProps.onOpen();
  };

  const openEditModal = (newTag: Device | { serialNumber: string }) => {
    setTag(newTag);
    openEdit();
  };

  const memoizedActions = useCallback(
    (cell: CellContext<InventoryTagApiResponse, unknown>) => (
      <Actions
        cell={cell.row as unknown as { original: Device }}
        refreshTable={refetchCount}
        key={uuid()}
        openEditModal={openEditModal}
        // onOpenScan={onOpenScan}
        onOpenFactoryReset={onOpenFactoryReset}
        onOpenUpgradeModal={onOpenUpgradeModal}
      />
    ),
    [],
  );

  const memoizedDate = useCallback(
    (cell: CellContext<InventoryTagApiResponse, unknown>, key: 'modified') => (
      <FormattedDate date={cell.row.original[key]} key={uuid()} />
    ),
    [],
  );

  const venueCell = useCallback(
    (cell: CellContext<InventoryTagApiResponse, unknown>) => (
      <VenueCell venueName={cell.row.original.extendedInfo?.venue?.name ?? ''} venueId={cell.row.original.venue} />
    ),
    [],
  );

  const onSearchClick = useCallback((serial: string) => {
    openEditModal({ serialNumber: serial });
  }, []);

  const columns = React.useMemo(() => {
    const baseColumns = [
      {
        key: 'serialNumber',
        title: (
          <span style={{ whiteSpace: 'nowrap' }}>
            {t('inventory.serial_number')}
          </span>
        ),
        dataIndex: 'serialNumber',
        render: (_, record) => serialCell(record),
        sorter: true,
        columnsFix: true,
      },
      {
        key: 'name',
        title: t('common.name'),
        dataIndex: 'name',
        sorter: true,
      },
      {
        key: 'venue',
        title: t('inventory.site'),
        dataIndex: ['venue'],
        render: (_, record) => venueCell({ row: { original: record } }),
        sorter: true,
      },
      {
        key: 'description',
        title: t('common.description'),
        dataIndex: 'description',
        sorter: true,
      },
      {
        key: 'label',
        title: t('inventory.label'),
        dataIndex: 'labelsName',
        sorter: true,
      },
      {
        key: 'modified',
        title: t('common.modified'),
        dataIndex: 'modified',
        render: (_, record) => memoizedDate({ row: { original: record } }, 'modified'),
        sorter: true,
      },
      {
        key: 'operation',
        title: 'Operation',
        dataIndex: 'operation',
        render: (_, record) => memoizedActions({ row: { original: record } }, 'operation'),
        columnsFix: true,
        fixed: 'right',
      },
    ];

    return baseColumns;
  }, [t]);

  const handleRefresh = () => {
    refetchCount();
    refetchTags();
  };

  useImperativeHandle(ref, () => ({
    refreshTable: () => {
      refetchCount();
      refetchTags();
    },
  }));

  useEffect(() => {
    setPageInfo(prevPageInfo => ({
      ...prevPageInfo,
      index: 0
    }));
    setSelectedRowKeys([]);
    setSelectedRows([]);
  }, [venueId, onlyUnassigned]);

  // 新增：Move 弹窗相关状态和方法
  const [isMoveModalOpen, setIsMoveModalOpen] = useState(false);
  const [selectedSite, setSelectedSite] = useState('');
  const { data: venues = [] } = useGetVenues();
  const [moveSiteForm] = Form.useForm(); // 创建表单实例

  // 创建防抖函数和移动状态
  const debounceRef = useRef(null);
  const [isMoving, setIsMoving] = useState(false); // 添加移动状态

  const handleOpenMoveModal = () => {

    // 检查是否有选中的行
    if (selectedRowKeys.length === 0) {
      message.warning('Please select at least one piece of data');
      return;
    }

    setIsMoveModalOpen(true); // 确保状态更新
  };

  // 新增：处理移动设备的函数
  const handleApplyMove = async (values) => {
    // 如果正在移动中，则阻止新的请求
    if (isMoving) {
      message.warning('Moving in progress, please wait...');
      return;
    }

    // 防抖逻辑：如果已经有定时器，则取消之前的请求
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    // 设置新的定时器
    debounceRef.current = setTimeout(async () => {
      const siteId = values.site;

      if (!siteId) {
        message.error('Please select a site');
        return;
      }

      try {
        // 设置移动状态为true
        setIsMoving(true);

        // 调用API进行批量切换站点
        const requestData = {
          idList: selectedRowKeys,
          siteId: siteId === 'none' ? null : siteId
        };

        const response = await batchSwitchSite(requestData);

        if (response.status === 200 || response.status === 201) {
          message.success(`Successfully moved ${selectedRowKeys.length} devices`);
          handleCloseMoveModal();
          setSelectedRowKeys([]);
          refetchTags();
          refetchCount();
        } else {
          message.error(response.data?.info || 'Failed to move devices');
        }
      } catch (error) {
        console.error('Error moving devices:', error);
        if (error.response?.data?.info) {
          message.error(`Failed to move devices: ${error.response.data.info}`);
        } else if (error.response?.status) {
          message.error(`Failed to move devices: Server returned status ${error.response.status}`);
        } else {
          message.error(`Failed to move devices: ${error.message}`);
        }
      } finally {
        // 无论成功还是失败，都要重置移动状态
        setIsMoving(false);
      }
    }, 300); // 300ms 防抖延迟
  };

  const handleCloseMoveModal = () => {
    setIsMoveModalOpen(false); // 确保状态更新
    setSelectedSite('');
    // 清除防抖定时器
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
      debounceRef.current = null;
    }
    // 重置移动状态
    setIsMoving(false);
  };

  // 更新 Dropdown 菜单，将 Move 选项的点击事件绑定到 handleOpenMoveModal
  return (
    <>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',  // 左右两端对齐
        alignItems: 'center',
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Space size={16} align="middle">
            <CreateConfigurationModal refresh={refetchCount} venueId={venueId} />
            <ExportDevicesTableButtonAntd />
            <ImportDeviceCsvModalAntd refresh={refetchCount} handleRefresh={handleRefresh} deviceClass="venue" />
            <Button
              htmlType="button"
              style={{ display: "flex", alignItems: "center" }}
              onClick={() => {
                handleRefresh();
                message.success("Inventory table refresh success.");
              }}
              icon={<Icon component={refreshSvg} />}
            >
              Refresh
            </Button>
            <Dropdown
              trigger={['click']}
              overlayStyle={{ width: '150px' }}
              menu={{
                items: [
                  {
                    key: 'move',
                    label: 'Move',
                    onClick: handleOpenMoveModal // 绑定点击事件
                  },
                  {
                    key: 'delete',
                    label: 'Delete',
                    onClick: handleDeleteSelected
                  }
                ]
              }}
            >
              <Button
                htmlType="button"
                icon={<MenuOutlined style={{ marginTop: '5px' }} />}
                // icon={<MenuOutlined />}

                disabled={selectedRowKeys.length === 0 || isDeleting}
                loading={isDeleting}
              >
                Actions
              </Button>
            </Dropdown>
          </Space>
        </div>

        <div style={{ minWidth: '280px' }}>
          <DeviceSearchBar onClick={onSearchClick} />
        </div>
      </div>
      {/* <Card bordered={false}> */}
      <WirelessCustomTable
        // 开启自定义列顺序
        columnsOrder={true}
        // 开启自定义列宽度
        resizableColumns={true}
        // 表格唯一标识符(列顺序数据持久化)
        tableId='inventory-table'
        ref={ref}
        columns={onlyUnassigned ? columns.filter(col => col.key !== 'entity' && col.key !== 'venue') : columns}
        dataSource={tags || []}
        loading={isFetchingCount || isFetchingTags}
        onChange={handleTableChange}
        showColumnSelector='true'
        rowSelection={rowSelection}
        disableInternalRowSelection
        pagination={{
          current: pageInfo.index + 1,
          pageSize: pageInfo.limit,
          total: count,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Total ${total} items`,
          pageSizeOptions: ['10', '20', '50', '100'],
        }}
      />
      {/* <EditTagModal
        isOpen={isEditOpen}
        onClose={closeEdit}
        tag={tag}
        refresh={refetchTags}
        pushConfig={pushConfiguration}
        onOpenScan={onOpenScan}
        onOpenFactoryReset={onOpenFactoryReset}
        onOpenUpgradeModal={onOpenUpgradeModal}
      /> */}
      <EditForms
        tag={tag}
        key={tag?.serialNumber}
        refresh={refetchTags}
        onClose={closeEdit}
        open={isEditOpen}
      />
      <ConfigurationPushModal isOpen={isPushOpen} onClose={closePush} pushResult={pushConfiguration.data} />
      {/* <WifiScanModal modalProps={scanModalProps} serialNumber={serialNumber} />  */}
      <FactoryResetModal modalProps={resetModalProps} serialNumber={serialNumber} />
      {/* </Card> */}

      {/* 新增：Move 设备弹窗 */}
      <FormModal
        title="Move"
        open={isMoveModalOpen}
        onCancel={handleCloseMoveModal}
        onFinish={(values) => {
          handleApplyMove(values);
        }}
        form={moveSiteForm}
        modalClass="ampcon-middle-modal"
      >
        {/* <div style={{ marginBottom: '20px' }}>
          <p style={{ color: '#14C9BB', fontSize: '14px', lineHeight: '1.5' }}>
            After the device is moved, the target site configuration will be delivered to the device.
          </p>
        </div> */}

        <style>
          {`
            .ant-form-item-explain {
              margin-left: -90px;
            }
          `}
        </style>

        {/* <Alert
          message={('Note: After the device is moved, the target site configuration will be delivered to the device.')}
          type="info"
          showIcon
          closable
          className="custom-alert"
          style={{
            marginTop: '10px',
            marginBottom: '20px',
            background: '#F3F8FF',
            color: '#367EFF',
            border: 'none',
          }}
        /> */}
        <Alert
          className="custom-trace-alert"
          // message={t('commands.factory_reset_warning')}
          message={t('Note: After the device is moved, the target site configuration will be delivered to the device.')}
          type="info"
          showIcon
          closable
          style={{ marginTop: 10, marginBottom: 20 }}
        />

        <Form.Item
          // label={
          //   <span>
          //     Select Site<span style={{ color: 'red', position: 'relative', top: '10px', left: '5px', fontSize: '25px' }}>*</span>
          //   </span>
          // }
          label='Select Site'
          name="site"
          rules={[{ required: true, message: ('Please select site') }]}
          className="site-selection-form-item"
        >
          <Select
            value={selectedSite}
            onChange={(value) => setSelectedSite(value)}
            placeholder="Please select site"
            allowClear
            style={{ width: '280px', height: '36px', marginLeft: '-90px' }} // 设置宽度为280px，高度为36px
          >
            <Select.Option value="none">All Sites</Select.Option>
            {venues.map((venue) => (
              <Select.Option key={venue.id} value={venue.id}>
                {venue.name}
                {/* {venue.description ? `: ${venue.description}` : ''} */}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </FormModal>
    </>
  );
});

export default InventoryTable;