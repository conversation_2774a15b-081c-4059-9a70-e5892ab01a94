import React from "react";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import SiteSelect from "@/modules-smb/Wireless/components/SiteSelect";
import InventoryList from "@/modules-smb/Wireless/pages/Inventory/table/InventoryList";
import { Form, Switch, Space, Card } from "antd";
import { useTranslation } from "react-i18next";
import { useSiteStore } from "@/modules-smb/Wireless/components/SiteContext";
import { useEntityFavorite } from "@/modules-smb/Wireless/hooks/useEntityFavorite";
import { useSiteSelection } from "@/modules-smb/Wireless/hooks/useSelectedSite";
import { useUrlSync } from "@/modules-smb/Wireless/hooks/useUrlSync";

const InventoryPage = () => {
  const { t } = useTranslation();
  const {
    selectedSiteId,
    isAllSitesSelected,
    handleSiteChange,
    displaySiteId
  } = useSiteSelection(true);
  //const [venueId, setVenueId] = useState<number | null>(null);
  const [onlyUnassigned, setOnlyUnassigned] = useState(false);
  const navigate = useNavigate();
  //const displaySiteId = isAllSitesSelected ? null : selectedSiteId;
  const { getFirstVenueFavoriteId } = useEntityFavorite();
  useUrlSync();

  const handleUnassignedToggle = (checked: boolean) => {
    setOnlyUnassigned(checked);
  };

  return (

    <Card
      style={{
        width: '100%',
        minHeight: '100%',
        borderRadius: '8px',
        boxShadow: 'none',
        padding: '20px 24px',
        overflowX: 'auto', // 内容超出时显示横向滚动条
      }}
      bodyStyle={{ padding: 0 }}
    >
      <span className="text-title">Inventory</span>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: 16,
      }}>
        <SiteSelect onChange={handleSiteChange} value={isAllSitesSelected ? "all" : selectedSiteId || "all"} />
        {isAllSitesSelected && (
          <Form.Item
            label={t('devices.unassigned_only')}
            style={{ marginBottom: 0, marginLeft: '80px' }}
          >
            <Switch
              checked={onlyUnassigned}
              onChange={handleUnassignedToggle}
            // size="small"
            />
          </Form.Item>
        )}
      </div>

      {/* 将onlyUnassigned状态传递给InventoryList（子组件） */}
      <InventoryList venueId={displaySiteId} onlyUnassigned={onlyUnassigned} />

    </Card>
  );
};

export default InventoryPage;