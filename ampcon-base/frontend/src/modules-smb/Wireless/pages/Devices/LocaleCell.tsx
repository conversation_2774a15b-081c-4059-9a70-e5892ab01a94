import React, { useState } from 'react';
import { Tooltip, Button, Typography, message } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import ReactCountryFlag from 'react-country-flag';
import { useTranslation } from 'react-i18next';
import { DeviceWithStatus } from '@/modules-smb/hooks/Network/Devices';

const { Text } = Typography;

const ICON_STYLE: React.CSSProperties = { width: '24px', height: '24px', borderRadius: '20px' };

type Props = {
  device: DeviceWithStatus;
};

const DeviceLocaleCell: React.FC<Props> = ({ device }) => {
  const { t } = useTranslation();
  const [copied, setCopied] = useState(false);

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (device.ipAddress) {
      await navigator.clipboard.writeText(device.ipAddress);
      setCopied(true);
      message.success(`${t('common.copied')}!`);
      setTimeout(() => setCopied(false), 1500);
    }
  };

  return (
    <Tooltip title={device.ipAddress} placement="top">
      <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
        {/* 如果需要显示国家旗帜，取消注释 */}
        {/* {device.locale && device.ipAddress && (
          <ReactCountryFlag style={ICON_STYLE} countryCode={device.locale} svg />
        )} */}

        {device.ipAddress && (
          <Tooltip title={copied ? `${t('common.copied')}!` : t('common.copy')}>
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              onClick={handleCopy}
              style={{ marginRight: 4 }}
            />
          </Tooltip>
        )}

        <Text ellipsis={{ tooltip: false }} style={{ flex: 1 }}>
          {device.ipAddress || '-'}
        </Text>
      </div>
    </Tooltip>
  );
};

export default DeviceLocaleCell;
