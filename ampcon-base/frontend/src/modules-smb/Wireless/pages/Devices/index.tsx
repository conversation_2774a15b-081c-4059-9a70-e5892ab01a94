import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import SiteSelect from "@/modules-smb/Wireless/components/SiteSelect";
import "../../assets/wireless.scss";
import DevicesList from "./DevicesList";
import { deleteWirelessDevice } from '@/modules-smb/Wireless/apis/wireless_device_api';
import { Card } from 'antd';
import { useSiteStore } from "@/modules-smb/Wireless/components/SiteContext";
import { useEntityFavorite } from "@/modules-smb/Wireless/hooks/useEntityFavorite";
import { useSiteSelection } from "@/modules-smb/Wireless/hooks/useSelectedSite";
import { useUrlSync } from "@/modules-smb/Wireless/hooks/useUrlSync";

const DevicesPage = () => {
  const {
    selectedSiteId,
    isAllSitesSelected,
    handleSiteChange,
    displaySiteId
  } = useSiteSelection(true);
  const location = useLocation();
  const navigate = useNavigate();
  const [siteId, setSiteId] = useState<string>();
  //const displaySiteId = isAllSitesSelected ? null : selectedSiteId;
  const { getFirstVenueFavoriteId } = useEntityFavorite();
  useUrlSync();
  
  return (
    <>
      <Card
        style={{
          width: '100%',
          minHeight: '100%',
          borderRadius: '8px',
          boxShadow: 'none',
          padding: '20px 24px',
          overflowX: 'auto', // 核心：内容超出时显示横向滚动条
        }}
        bodyStyle={{ padding: 0 }}
      >
        <span className="text-title">Devices</span>
        <SiteSelect onChange={handleSiteChange}  value={isAllSitesSelected ? "all" : selectedSiteId || "all"}  />
        <DevicesList siteId={displaySiteId} />
        {/* <Button onClick={handleDeleteDevices}>delete</Button> */}
      </Card>
    </>
  );
};

export default DevicesPage;
