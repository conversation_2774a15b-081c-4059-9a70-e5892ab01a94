import * as React from 'react';
import { CSVLink } from 'react-csv';
import { useTranslation } from 'react-i18next';
import { ExportedDeviceInfo, getAllExportedDevicesInfo, getSelectExportedDevicesInfo } from './utils';
import { dateForFilename } from '@/modules-smb/helpers/dateFormatting';
import { Button, Modal, Tooltip, Progress, Typography, Space, Divider } from 'antd';
import { UploadOutlined, DownloadOutlined } from '@ant-design/icons';
// import exportSvg from "@/modules-smb/Wireless/assets/Devices/export.svg?react";
import Icon from "@ant-design/icons";
import {exportWhiteSvg} from "@/utils/common/iconSvg";

const HEADER_MAPPING: { key: keyof ExportedDeviceInfo; label: string }[] = [
  { key: 'connected', label: 'Status' },
  { key: 'serialNumber', label: 'Serial number' },
  { key: 'name', label: 'Name' },
  { key: 'sanity', label: 'Sanity' },
  { key: 'memory', label: 'Memory' },
  { key: 'load', label: 'Load' },
  { key: 'temperature', label: 'Temp(°c)' },
  { key: 'revision', label: 'Revision' },
  { key: 'deviceType', label: 'Type' },
  { key: 'ip', label: 'Ip' },
  { key: 'provisioning', label: 'Venue' },
  // { key: 'radiusSessions', label: 'Rad' },
  { key: 'hasGPS', label: 'GPS' },
  { key: 'uptime', label: 'Uptime' },
  { key: 'lastRecordedContact', label: 'Last Connected' },
  { key: 'lastContact', label: 'Last Contact' },
  { key: 'lastUpgrade', label: 'Last Upgrade' },
  { key: 'rx', label: 'Rx' },
  { key: 'tx', label: 'Tx' },
  { key: 'twoG', label: '2G' },
  { key: 'fiveG', label: '5G' },
  { key: 'sixG', label: '6G' },
  { key: 'connectReason', label: 'Connect Reason' },
  // { key: 'certificateExpiry', label: 'Exp' },
];

// const HEADER_MAPPING: { key: keyof ExportedDeviceInfo; label: string }[] = [
//   { key: 'serialNumber', label: 'Serial Number' },
//   { key: 'connected', label: 'Connected' },
//   { key: 'firmware', label: 'Firmware' },
//   { key: 'memory', label: 'Memory (%)' },
//   { key: 'load', label: 'Load (%)' },
//   { key: 'temperature', label: 'Temperature (°C)' },
//   { key: 'sanity', label: 'Sanity (%)' },
//   { key: 'revision', label: 'Revision' },
//   { key: 'ip', label: 'IP Address' },
//   { key: 'provisioning', label: 'Provisioning' },
//   { key: 'radiusSessions', label: 'Radius Sessions' },
//   { key: 'uptime', label: 'Uptime (s)' },
//   { key: 'lastContact', label: 'Last Contact (UTC)' },
//   { key: 'lastUpgrade', label: 'Last Upgrade (UTC)' },
//   { key: 'rx', label: 'RX (MB)' },
//   { key: 'tx', label: 'TX (MB)' },
//   { key: 'twoG', label: '2G Associations' },
//   { key: 'fiveG', label: '5G Associations' },
//   { key: 'sixG', label: '6G Associations' },
//   { key: 'certificateExpiry', label: 'Certificate Expiry (UTC)' },
// ];

type Status = {
  progress: number;
  status: 'loading-all' | 'loading-select' | 'success' | 'error' | 'idle';
  error?: string;
  lastResults?: ExportedDeviceInfo[];
};

type Props = {
  currentPageSerialNumbers: string[];
};

// 根据url获取当前页面的 venueId
const getVenueIdFromHash = () => {
  if (window.location.hash) {
    const id = window.location.hash.replace('#', '');
    return id === 'all' ? '' : id;
  }
  return '';
};

const ExportDevicesTableButtonAntd = ({ currentPageSerialNumbers }: Props) => {
  const { t } = useTranslation();
  const [modalOpen, setModalOpen] = React.useState(false);
  const [status, setStatus] = React.useState<Status>({
    progress: 0,
    status: 'idle',
  });

  const setProgress = (progress: number) => {
    setStatus((prev) => ({ ...prev, progress }));
  };

  const handleAllClick = () => {
    setStatus((prev) => ({ ...prev, error: undefined, lastResults: undefined, status: 'loading-all', progress: 0 }));
    const venueId = getVenueIdFromHash();
    getAllExportedDevicesInfo(setProgress, venueId)
      .then((result) => {
        setStatus((prev) => ({ ...prev, status: 'success', lastResults: result }));
      })
      .catch((error) => {
        setStatus((prev) => ({ ...prev, status: 'error', error }));
      });
  };

  const handleCurrentPageClick = () => {
    setStatus((prev) => ({
      ...prev,
      error: undefined,
      lastResults: undefined,
      status: 'loading-select',
      progress: 0,
    }));
    const venueId = getVenueIdFromHash();
    getSelectExportedDevicesInfo(currentPageSerialNumbers, setProgress, venueId)
      .then((result) => {
        setStatus((prev) => ({ ...prev, status: 'success', lastResults: result }));
      })
      .catch((error) => {
        setStatus((prev) => ({ ...prev, status: 'error', error }));
      });
  };

  const onOpen = () => {
    setStatus({ progress: 0, status: 'idle' });
    setModalOpen(true);
  };

  const handleCancel = () => {
    setModalOpen(false);
  };

  return (
    <>
      <Tooltip title={t('common.export')}>
        <Button
          type="primary"
          icon={<Icon component={exportWhiteSvg} />}
          onClick={onOpen}
        >
          {t('common.export')}
        </Button>
      </Tooltip>

      <Modal
        title={t('common.export')}
        open={modalOpen}
        onCancel={handleCancel}
        footer={null}
        width={680}
        style={{
          height: 450,
          // minHeight: 450,
          // maxHeight: 'calc(100vh - 350px)'
        }}
      // bodyStyle={{ 
      //   height: 'calc(100% - 40px)',
      //   // overflowY: 'auto',
      //   padding: '0 24px'
      // }}
      >
        <Divider
          style={{
            margin: '16px 0 0 0',
            width: 'calc(100% + 48px)',
            marginLeft: '-24px',
            marginBottom: 16,
          }}
        />
        <div style={{ padding: 0 }}>


          {/* <div style={{ textAlign: 'left', marginBottom: (status.status.includes('loading') || status.status === 'success') ? 16 : 111 }}> */}
          <div style={{ textAlign: 'left', marginBottom: 16 }}>

            <Space>
              <Button
                onClick={handleAllClick}
                disabled={status.status.includes('loading')}
                loading={status.status === 'loading-all'}
              >
                {t('devices.all')} {t('devices.title')}
              </Button>
              <Button
                onClick={handleCurrentPageClick}
                disabled={currentPageSerialNumbers.length === 0 || status.status.includes('loading')}
                loading={status.status === 'loading-select'}
              >
                {t('table.export_current_page')} ({currentPageSerialNumbers.length})
              </Button>
            </Space>
          </div>

          {(status.status.includes('loading') || status.status === 'success') && (
            <>

              <div style={{ margin: '113px 0px 113px 0px' }}>
                <div style={{ display: 'flex', alignItems: 'center', margin: '-33px 0px -25px 0px' }}>
                  <Progress
                    style={{ flex: 1, marginBottom: 0 }}
                    // style={{ marginBottom: 0 }}
                    percent={Math.round(status.progress)}
                    status={status.progress !== 100 ? 'active' : 'success'}
                    strokeColor={status.progress === 100 ? '#14C9BB' : undefined}
                    strokeWidth={12}
                    showInfo={false}

                  />
                  <Typography.Title level={5} style={{ textAlign: 'center', marginBottom: 25, width: 50, fontWeight: 'bold' }}>
                    {Math.round(status.progress)}%
                  </Typography.Title>

                </div>
                {/* 进度提示信息 */}
                <Typography.Text style={{ textAlign: 'center', marginBottom: 25, fontWeight: 'bold' }}>
                  {`Exporting to ${status.progress}%, Please Wait...`}
                </Typography.Text>
              </div>


              {status.lastResults && (
                <Divider
                  style={{
                    margin: '16px 0 0 0',
                    width: 'calc(100% + 48px)',
                    marginLeft: '-24px',
                  }}
                />
              )}

              {status.lastResults && (
                <div style={{ textAlign: 'right', margin: '20px 0px 0px 0px' }}>
                  <CSVLink
                    filename={`devices_export_${dateForFilename(new Date().getTime() / 1000)}.csv`}
                    data={status.lastResults ?? []}
                    headers={HEADER_MAPPING}
                  >
                    <Button
                      type="primary"
                    >
                      {t('common.download')}
                    </Button>
                  </CSVLink>
                </div>
              )}
            </>
          )}
          {status.status.includes('error') && (
            <Typography.Text type="danger" style={{ display: 'block', textAlign: 'center', marginTop: 32 }}>
              {JSON.stringify(status.error, null, 2)}
            </Typography.Text>
          )}
        </div>
      </Modal >
    </>
  );
};

export default ExportDevicesTableButtonAntd;
