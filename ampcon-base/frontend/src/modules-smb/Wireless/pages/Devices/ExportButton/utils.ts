import { axiosGw, axiosProv } from '@/modules-smb/utils/axiosInstances';
import { DeviceWithStatus } from '@/modules-smb/hooks/Network/Devices';
import { InventoryTag } from '@/modules-smb/hooks/Network/Inventory';
import { getRevision, bytesString } from '@/modules-smb/helpers/stringHelper';


// 数据处理函数，与 DevicesList.tsx 保持一致
const fourDigitNumber = (v?: number) => {
  if (v === undefined || typeof v !== 'number') return '-';
  if (v === 0) {
    return '0.00';
  }
  const str = v.toString();
  const fourthChar = str.charAt(3);
  if (fourthChar === '.') return `${str.slice(0, 3)}`;
  return `${str.slice(0, 4)}`;
};

const processTemperature = (temperature: number, connected: boolean) => {
  if (!connected || temperature === 0) return '-';
  const processedTemp = temperature > 1000 ? temperature / 1000 : temperature;
  return `${fourDigitNumber(processedTemp)}°C`;
};

const processConnectedStatus = (device: DeviceWithStatus) => {
  let connected = device.connected ? "Connected" : "Disconnected";
  if (device.blackListed) {
    connected = "Denied";
  }
  return connected;
};

const processDataBytes = (bytes: number) => {
  // 使用 bytesString 函数，与 DevicesList.tsx 中的 DataCell 逻辑一致
  if (!bytes || bytes === 0) return '-';
  return bytesString(bytes);
};

const processNumberValue = (v?: number) => {
  if (v === undefined) return 0;
  return v;
};

export type ExportedDeviceInfo = {
  connected: string;
  serialNumber: string;
  name: string;
  sanity: string;
  memory: string;
  load: string;
  temperature: string;
  revision: string;
  deviceType: string;
  ip: string;
  provisioning: string;
  radiusSessions: string;
  hasGPS: string;
  uptime: string;
  lastContact: string;
  lastRecordedContact: string;
  lastUpgrade: string;
  rx: string;
  tx: string;
  twoG: number;
  fiveG: number;
  sixG: number;
  connectReason: string;
  certificateExpiry: string;
};

// export type ExportedDeviceInfo = {
//   serialNumber: string;
//   connected: 'true' | 'false';
//   firmware: string;
//   memory: number;
//   load: number;
//   temperature: number;
//   sanity: number;
//   revision: string;
//   ip: string;
//   /** Venue, Entity or subscriber name */
//   provisioning: string;
//   radiusSessions: number;
//   /** Uptime in seconds */
//   uptime: number;
//   /** Last Contact as date */
//   lastContact: string;
//   /** Last Upgrade as date */
//   lastUpgrade: string;
//   /** Rx MBs  */
//   rx: number;
//   /** Tx MBs  */
//   tx: number;
//   twoG: number;
//   fiveG: number;
//   sixG: number;
//   /** Expiry as date */
//   certificateExpiry: string;
// };

const getDevicesProvisioningStatus = async (serialNumbers: string[], venueId?: string) =>
  serialNumbers.length === 0
    ? []
    : axiosProv
      .get(`inventory?withExtendedInfo=true&select=${serialNumbers}${venueId ? `&siteId=${venueId}` : ''}`)
      .then(({ data }: { data: { taglist: InventoryTag[] } }) =>
        serialNumbers.map((serialNumber) => {
          const found = data.taglist.find((tag) => tag.serialNumber === serialNumber);

          let provisioning = 'Not Found';

          if (found) {
            if (found.entity.length > 0) provisioning = found.extendedInfo?.entity?.name ?? found.entity;
            else if (found.venue.length > 0) provisioning = found.extendedInfo?.venue?.name ?? found.venue;
            else if (found.subscriber.length > 0)
              provisioning = found.extendedInfo?.subscriber?.name ?? found.subscriber;
          }

          return {
            serialNumber,
            provisioning,
          };
        }),
      )
      .catch(() => []);

const getDeviceGatewayInfo = (limit: number, offset: number, venueId?: string) =>
  axiosGw
    .get(`devices?deviceWithStatus=true&limit=${limit}&offset=${offset}${venueId ? `&siteId=${venueId}` : ''}`)
    .then((response) => response.data) as Promise<{ devicesWithStatus: DeviceWithStatus[] }>;

const getAllGatewayDeviceInfo = async (
  count: number,
  initialProgress: number,
  setProgress: (progress: number) => void,
  venueId?: string,
) => {
  const progressStep = (90 - initialProgress) / Math.ceil(count / 100);
  let newProgress = initialProgress;
  let offset = 0;
  let devices: DeviceWithStatus[] = [];
  let devicesResponse: { devicesWithStatus: DeviceWithStatus[] };
  do {
    // eslint-disable-next-line no-await-in-loop
    devicesResponse = await getDeviceGatewayInfo(100, offset, venueId);
    devices = devices.concat(devicesResponse.devicesWithStatus);
    setProgress((newProgress += progressStep));
    offset += 100;
  } while (devicesResponse.devicesWithStatus.length === 100);

  return devices;
};

export const getAllExportedDevicesInfo = async (setProgress: (progress: number) => void, venueId?: string) => {
  // Base Setup
  setProgress(0);
  const devicesCount = await axiosGw.get(`devices?countOnly=true${venueId ? `&siteId=${venueId}` : ''}`).then((response) => response.data.count as number);
  setProgress(10);

  if (devicesCount === 0) {
    setProgress(100);
    return [];
  }

  // Get Devices Info
  const devices = await getAllGatewayDeviceInfo(devicesCount, 10, setProgress, venueId);

  const serialNumbers = devices
    .filter((device) => device.entity.length > 0 || device.venue.length > 0 || device.subscriber.length > 0)
    .map((device) => device.serialNumber);
  const provisioningStatus = await getDevicesProvisioningStatus(serialNumbers, venueId);

  setProgress(95);

  const unixToStr = (unixValue: number) => {
    try {
      return new Date(unixValue * 1000).toISOString();
    } catch (e) {
      return '';
    }
  };
  const exportedDevicesInfo: ExportedDeviceInfo[] = devices.map((device) => {
    const provisioning = provisioningStatus.find((status) => status.serialNumber === device.serialNumber)?.provisioning;
    return {
      connected: processConnectedStatus(device),
      serialNumber: device.serialNumber,
      name: device.name,
      sanity: device.connected ? `${device.sanity}%` : '-',
      memory: device.connected ? `${fourDigitNumber(device.memoryUsed)}%` : '-',
      load: device.connected ? `${fourDigitNumber(device.load)}%` : '-',
      temperature: processTemperature(device.temperature, device.connected),
      revision: getRevision(device.firmware),
      deviceType: device.compatible,
      ip: device.ipAddress || '-',
      provisioning: provisioning ?? '-',
      radiusSessions: (typeof device.hasRADIUSSessions === 'number' ? device.hasRADIUSSessions : 0).toString(),
      hasGPS: device.hasGPS ? 'true' : '-',
      uptime: !device.connected || device.started === 0 ? '-' : Math.floor(Date.now() / 1000 - device.started).toString(),
      // uptime: !device.connected || device.started === 0 ? '-' : device.started.toString(),
      lastContact: typeof device.lastContact === 'string' ? device.lastContact : unixToStr(device.lastContact),
      lastRecordedContact: typeof device.lastRecordedContact === 'string' ? device.lastRecordedContact : unixToStr(device.lastRecordedContact),
      lastUpgrade: typeof device.lastFWUpdate === 'string' ? device.lastFWUpdate : unixToStr(device.lastFWUpdate),
      rx: processDataBytes(device.rxBytes),
      tx: processDataBytes(device.txBytes),
      twoG: processNumberValue(device.associations_2G),
      fiveG: processNumberValue(device.associations_5G),
      sixG: processNumberValue(device.associations_6G),
      connectReason: device.connectReason || '-',
      certificateExpiry: device.certificateExpiryDate ? unixToStr(device.certificateExpiryDate) : '-',
    };

    // return {
    //   serialNumber: device.serialNumber,
    //   connected: device.connected ? 'true' : 'false',
    //   firmware: device.firmware,
    //   memory: device.memoryUsed,
    //   load: device.load,
    //   temperature: device.temperature,
    //   sanity: device.sanity,
    //   revision: device.compatible,
    //   ip: device.ipAddress.length > 0 ? device.ipAddress : '',
    //   provisioning: provisioning ?? '',
    //   radiusSessions: typeof device.hasRADIUSSessions === 'number' ? device.hasRADIUSSessions : 0,
    //   uptime: !device.connected || device.started === 0 ? 0 : Math.floor(Date.now() / 1000 - device.started),
    //   lastContact: typeof device.lastContact === 'string' ? '' : unixToStr(device.lastContact),
    //   lastUpgrade: typeof device.lastFWUpdate === 'string' ? '' : unixToStr(device.lastFWUpdate),
    //   rx: device.rxBytes / 1024 / 1024,
    //   tx: device.txBytes / 1024 / 1024,
    //   twoG: device.associations_2G,
    //   fiveG: device.associations_5G,
    //   sixG: device.associations_6G,
    //   certificateExpiry: device.certificateExpiryDate ? unixToStr(device.certificateExpiryDate) : '',
    // };
  });

  setProgress(100);
  return exportedDevicesInfo;
};

const getSelectDevicesGatewayInfo = (serialNumbers: string[], venueId?: string) =>
  axiosGw
    .get(`devices?deviceWithStatus=true&select=${serialNumbers.join(',')}${venueId ? `&siteId=${venueId}` : ''}`)
    .then((response) => response.data) as Promise<{ devicesWithStatus: DeviceWithStatus[] }>;

export const getSelectExportedDevicesInfo = async (
  serialNumbers: string[],
  setProgress: (progress: number) => void,
  venueId?: string,
) => {
  // Base Setup
  setProgress(0);
  const devicesCount = serialNumbers.length;
  setProgress(10);

  if (devicesCount === 0) {
    setProgress(100);
    return [];
  }

  // Get Devices Info
  const devices = (await getSelectDevicesGatewayInfo(serialNumbers, venueId)).devicesWithStatus;
  setProgress(90);

  const provSerialNumbers = devices
    .filter((device) => device.entity.length > 0 || device.venue.length > 0 || device.subscriber.length > 0)
    .map((device) => device.serialNumber);
  const provisioningStatus = await getDevicesProvisioningStatus(provSerialNumbers, venueId);

  setProgress(95);

  const unixToStr = (unixValue: number) => {
    try {
      return new Date(unixValue * 1000).toISOString();
    } catch (e) {
      return '';
    }
  };
  const exportedDevicesInfo: ExportedDeviceInfo[] = devices.map((device) => {
    const provisioning = provisioningStatus.find((status) => status.serialNumber === device.serialNumber)?.provisioning;
    return {
      connected: processConnectedStatus(device),
      serialNumber: device.serialNumber,
      name: device?.configuration?.unit?.name || '-',
      sanity: device.connected ? `${device.sanity}%` : '-',
      memory: device.connected ? `${fourDigitNumber(device.memoryUsed)}%` : '-',
      load: device.connected ? `${fourDigitNumber(device.load)}%` : '-',
      temperature: processTemperature(device.temperature, device.connected),
      revision: getRevision(device.firmware),
      deviceType: device.compatible,
      ip: device.ipAddress || '-',
      provisioning: provisioning ?? '-',
      radiusSessions: (typeof device.hasRADIUSSessions === 'number' ? device.hasRADIUSSessions : 0).toString(),
      hasGPS: device.hasGPS ? 'true' : '-',
      uptime: !device.connected || device.started === 0 ? '-' : Math.floor(Date.now() / 1000 - device.started).toString(),
      // uptime: !device.connected || device.started === 0 ? '-' : device.started.toString(),
      lastContact: typeof device.lastContact === 'string' ? device.lastContact : unixToStr(device.lastContact),
      lastRecordedContact: typeof device.lastRecordedContact === 'string' ? device.lastRecordedContact : unixToStr(device.lastRecordedContact),
      lastUpgrade: typeof device.lastFWUpdate === 'string' ? device.lastFWUpdate : unixToStr(device.lastFWUpdate),
      rx: processDataBytes(device.rxBytes),
      tx: processDataBytes(device.txBytes),
      twoG: processNumberValue(device.associations_2G),
      fiveG: processNumberValue(device.associations_5G),
      sixG: processNumberValue(device.associations_6G),
      connectReason: device.connectReason || '-',
      certificateExpiry: device.certificateExpiryDate ? unixToStr(device.certificateExpiryDate) : '-',
    };
    // return {
    //   serialNumber: device.serialNumber,
    //   connected: device.connected ? 'true' : 'false',
    //   firmware: device.firmware,
    //   memory: device.memoryUsed,
    //   load: device.load,
    //   temperature: device.temperature,
    //   sanity: device.sanity,
    //   revision: device.compatible,
    //   ip: device.ipAddress.length > 0 ? device.ipAddress : '',
    //   provisioning: provisioning ?? '',
    //   radiusSessions: typeof device.hasRADIUSSessions === 'number' ? device.hasRADIUSSessions : 0,
    //   uptime: !device.connected || device.started === 0 ? 0 : Math.floor(Date.now() / 1000 - device.started),
    //   lastContact: typeof device.lastContact === 'string' ? '' : unixToStr(device.lastContact),
    //   lastUpgrade: typeof device.lastFWUpdate === 'string' ? '' : unixToStr(device.lastFWUpdate),
    //   rx: device.rxBytes / 1024,
    //   tx: device.txBytes / 1024,
    //   twoG: device.associations_2G,
    //   fiveG: device.associations_5G,
    //   sixG: device.associations_6G,
    //   certificateExpiry: device.certificateExpiryDate ? unixToStr(device.certificateExpiryDate) : '',
    // };
  });

  setProgress(100);
  return exportedDevicesInfo;
};