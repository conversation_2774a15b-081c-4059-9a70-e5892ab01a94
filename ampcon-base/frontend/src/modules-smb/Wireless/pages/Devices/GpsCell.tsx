import * as React from 'react';
import { DeviceWithStatus } from '@/modules-smb/hooks/Network/Devices';
import LocationDisplayButton from '@/modules-smb/Wireless/pages/Device/LocationDisplayButton';

type Props = {
  device: DeviceWithStatus;
};
const DeviceTableGpsCell = ({ device }: Props) => {
  if (!device.hasGPS) return <span>-</span>;

  return (
    <>
      <LocationDisplayButton serialNumber={device.serialNumber} isCompact />
    </>
  );
};

export default DeviceTableGpsCell;
