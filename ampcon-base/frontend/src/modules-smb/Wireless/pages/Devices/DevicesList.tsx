import React, { useCallback, useMemo, useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Space, Typography, Tag, Button, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
import useControlledTable from '@/modules-smb/hooks/useControlledTable';
import { Column, SortInfo } from '@/modules-smb/models/Table';
import { UseQueryResult } from '@tanstack/react-query';
import { DevicePlatform, DeviceWithStatus, useGetDeviceCount, useGetDevices, useGetDeviceRtty } from '@/modules-smb/Wireless/hooks/Network/Devices';
import { getRevision } from '@/modules-smb/helpers/stringHelper';
import { DataGridColumn, useDataGrid } from '@/modules-smb/components/DataTables/DataGrid/useDataGrid';
import { useNavigate } from 'react-router-dom';
import Actions from './Actions';
import DeviceTableGpsCell from './GpsCell';
import DeviceUptimeCell from './Uptime';
import FormattedDate from '@/modules-smb/Wireless/components/InformationDisplays/FormattedDate';

import { WifiScanModal } from '@/modules-smb/components/Modals/WifiScanModal';
import { ConfigureModal } from './Action/ConfigureModal';
import { RebootModal } from '@/modules-smb/components/Modals/RebootModal';
import { EventQueueModal } from '@/modules-smb/components/Modals/EventQueueModal';
import { TraceModal } from './Action/TraceModal';
import FactoryResetModal from './Action/FactoryResetModal';
import { TelemetryModal } from '@/modules-smb/components/Modals/TelemetryModal';
import ProvisioningStatusCell from './ProvisioningStatusCell';

import Icon from "@ant-design/icons";
import denyListSvg from "../../assets/Devices/deny_list.svg?react";
import "../../assets/wireless.scss";
import SearchInput from "./SearchInput";
import ExportDevicesTableButton from "./ExportButton";
import DeviceLocaleCell from './LocaleCell';
import ExportDevicesTableButtonAntd from "./ExportButton/indexAntd";
import { refreshSvg } from "@/utils/common/iconSvg";
import NumberCell from '@/modules-smb/Wireless/components/TableCells/NumberCell';
import DataCell from '@/modules-smb/Wireless/components/TableCells/DataCell';
type DevicesListProps = {
  siteId?: string;
};
const DevicesList = forwardRef(({ siteId }: DevicesListProps, ref) => {
  const { t } = useTranslation();
  let hashSiteId: string | null = null;
  const hash = window.location.hash.replace('#', '');
  if (hash && hash !== '') {
    hashSiteId = hash;
  }
  if (siteId != '') {
    siteId = siteId;
  } else if (hashSiteId) {
    siteId = hashSiteId;
  }
  const [sortInfo, setSortInfo] = useState<SortInfo>([{ id: 'serialNumber', sort: 'asc' }]);
  const [pageInfo, setPageInfo] = useState<PageInfo>({ index: 0, limit: 10 });
  const [platform, setPlatform] = React.useState<DevicePlatform>('ALL');
  const [serialNumber, setSerialNumber] = React.useState<string>('');
  const [isScanModalOpen, setIsScanModalOpen] = React.useState(false);
  const [isConfigureModalOpen, setIsConfigureModalOpen] = React.useState(false);
  const [isRebootModalOpen, setIsRebootModalOpen] = React.useState(false);
  const [isEventQueueModalOpen, setIsEventQueueModalOpen] = React.useState(false);
  const [isFactoryResetModalOpen, setIsFactoryResetModalOpen] = React.useState(false);
  const [isTelemetryModalOpen, setIsTelemetryModalOpen] = React.useState(false);
  const [isTraceModalOpen, setIsTraceModalOpen] = React.useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const getCount = useGetDeviceCount({ enabled: true, platform, siteId });
  const navigate = useNavigate();
  const tableController = useDataGrid({
    tableSettingsId: 'gateway.devices.table',
    defaultOrder: [
      'serialNumber',
      'sanity',
      'memory',
      'load',
      'temperature',
      'firmware',
      'compatible',
      'connected',
      'actions',
    ],
  });
  const getDevices = useGetDevices({
    pageInfo: {
      limit: pageInfo.limit,
      index: pageInfo.index,
    },
    sortInfo,
    enabled: true,
    platform,
    siteId
  });

  useEffect(() => {
    if (siteId) {
      getDevices.refetch();
    }
    setPageInfo({ index: 0, limit: pageInfo.limit });
  }, [siteId]);
  const fourDigitNumber = (v?: number) => {
    if (v === undefined || typeof v !== 'number') return '-';
    if (v === 0) {
      return '0.00';
    }
    const str = v.toString();
    const fourthChar = str.charAt(3);
    if (fourthChar === '.') return `${str.slice(0, 3)}`;
    return `${str.slice(0, 4)}`;
  };


  const hexToRgba = (hex: string, alpha: number) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  };

  const serialCell = React.useCallback(
    (device: DeviceWithStatus) => {
      return (<a href={`/wireless/devices/${device.serialNumber}#/devices/${device.serialNumber}`} style={{ color: '#14C9BB ', textDecoration: 'underline' }} >
        <pre>{device.serialNumber}</pre>
      </a>
      );
    },
    [],);
  const sanityCell = React.useCallback((device: DeviceWithStatus) => {
    if (!device.connected) return <span>-</span>;
    let colorScheme = '#F53F3F';
    if (device.sanity >= 80) colorScheme = '#FFBB00';
    if (device.sanity === 100) colorScheme = '#2BC174';

    return (
      <Tag style={{
        border: `1px solid ${colorScheme}`,
        color: colorScheme,
        background: hexToRgba(colorScheme, 0.1),
      }}
      >
        {device.sanity}%
      </Tag>
    );
  }, []);
  const memoryCell = React.useCallback((device: DeviceWithStatus) => {
    if (!device.connected) return <span>-</span>;

    let colorScheme = '#F53F3F';
    if (device.memoryUsed <= 85) colorScheme = '#FFBB00';
    if (device.memoryUsed <= 60) colorScheme = '#2BC174';

    return (
      <Tag style={{
        border: `1px solid ${colorScheme}`,
        color: colorScheme,
        background: hexToRgba(colorScheme, 0.1),
      }}
      >
        {fourDigitNumber(device.memoryUsed)}%
      </Tag>
    );
  }, []);

  const loadCell = React.useCallback((device: DeviceWithStatus) => {
    if (!device.connected) return <span>-</span>;

    let colorScheme = '#F53F3F';
    if (device.load <= 20) colorScheme = '#FFBB00';
    if (device.load <= 5) colorScheme = '#2BC174';

    return (
      <Tag style={{
        border: `1px solid ${colorScheme}`,
        color: colorScheme,
        background: hexToRgba(colorScheme, 0.1),
      }}
      >
        {fourDigitNumber(device.load)}%
        {/* <TagRightIcon marginStart="0.1rem" as={colorScheme === 'green' ? CheckCircle : WarningCircle} /> */}
      </Tag>
    );
  }, []);

  const statusCell = React.useCallback((device: DeviceWithStatus) => {
    let colorScheme = '#F53F3F';
    let connected = device.connected ? "Connected" : "Disconnected";
    if (device.blackListed) {
      connected = "Denied";
    }

    if (connected == "Denied") { colorScheme = '#F53F3F' }
    else if (connected == "Connected") colorScheme = '#2BC174';
    else colorScheme = '#B3BBC8';

    return (
      <Tag style={{
        border: `1px solid ${colorScheme}`,
        color: colorScheme,
        background: hexToRgba(colorScheme, 0.1),
      }}
      >
        {connected}
      </Tag>);
  }, []);

  const firmwareCell = React.useCallback(
    (device: DeviceWithStatus) => {
      if (!device.firmware) return <span>-</span>;
      return (<span>{getRevision(device.firmware)}</span>);
    },
    [],
  );

  const temperatureCell = React.useCallback((device: DeviceWithStatus) => {
    if (!device.connected || device.temperature === 0) return <p>-</p>;

    const temperature = device.temperature > 1000 ? device.temperature / 1000 : device.temperature;

    return (
      <>
        <span>{fourDigitNumber(temperature)}°C</span>
      </>
    );
  }, []);
  const provCell = React.useCallback((device: DeviceWithStatus) => {
    // 添加错误边界处理
    if (!device.entity && !device.venue) {
      return '-';
    }
    return <ProvisioningStatusCell device={device} />;
  }, []);

  const localeCell = React.useCallback((device: DeviceWithStatus) => <DeviceLocaleCell device={device} />, []);
  const gpsCell = React.useCallback((device: DeviceWithStatus) => <DeviceTableGpsCell device={device} />, []);
  const uptimeCell = React.useCallback((device: DeviceWithStatus) => <DeviceUptimeCell device={device} />, []);
  const dateCell = React.useCallback(
    (v?: number | string, hidePrefix?: boolean) =>
      v !== undefined && typeof v === 'number' && v !== 0 ? (
        <FormattedDate date={v as number} hidePrefix={hidePrefix} />
      ) : (
        '-'
      ),
    [],
  );
  const dataCell = React.useCallback(
    (v: number) => (
      <div>
        <DataCell bytes={v} showZerosAs="-" />
      </div>
    ),
    [],
  );
  const compactDateCell = React.useCallback(
    (v?: number | string, hidePrefix?: boolean) =>
      v !== undefined && typeof v === 'number' && v !== 0 ? (
        <FormattedDate date={v as number} hidePrefix={hidePrefix} isCompact />
      ) : (
        '-'
      ),
    [],
  );
  const onOpenScan = (serial: string) => {
    setSerialNumber(serial);
    setIsScanModalOpen(true);
  };

  const onCloseScan = () => {
    setIsScanModalOpen(false);
  };

  const onOpenConfigureModal = (serial: string) => {
    setSerialNumber(serial);
    setIsConfigureModalOpen(true);
  };

  const onCloseConfigure = () => {
    setIsConfigureModalOpen(false);
  };

  const onOpenRebootModal = (serial: string) => {
    setSerialNumber(serial);
    setIsRebootModalOpen(true);
  };

  const onCloseReboot = () => {
    setIsRebootModalOpen(false);
  };

  const onOpenEventQueue = (serial: string) => {
    setSerialNumber(serial);
    setIsEventQueueModalOpen(true);
  };

  const onCloseEventQueue = () => {
    setIsEventQueueModalOpen(false);
  };

  const onOpenFactoryReset = (serial: string) => {
    setSerialNumber(serial);
    setIsFactoryResetModalOpen(true);
  };

  const onCloseFactoryReset = () => {
    setIsFactoryResetModalOpen(false);
  };

  const onOpenTelemetry = (serial: string) => {
    setSerialNumber(serial);
    setIsTelemetryModalOpen(true);
  };

  const onCloseTelemetry = () => {
    setIsTelemetryModalOpen(false);
  };

  const onOpenTrace = (serial: string) => {
    setSerialNumber(serial);
    setIsTraceModalOpen(true);
  };

  const onCloseTrace = () => {
    setIsTraceModalOpen(false);
  };



  const actionsCell = React.useCallback((device: DeviceWithStatus) => {
    return (
      <Actions device={device}
        refreshTable={getDevices.refetch}
        onOpenScan={onOpenScan}
        onOpenConfigureModal={onOpenConfigureModal}
        onOpenRebootModal={onOpenRebootModal}
        onOpenEventQueue={onOpenEventQueue}
        onOpenFactoryReset={onOpenFactoryReset}
        onOpenTelemetryModal={onOpenTelemetry}
        onOpenTrace={onOpenTrace}
      />
    );
  }, []);

  const numberCell = React.useCallback(
    (v?: number) => (
      <NumberCell
        value={v !== undefined ? v : 0}
        showZerosAs="-"
        boxProps={{
          textAlign: 'right',
        }}
      />
    ),
    [],
  );
  // 处理表格变化（分页、排序）
  // const handleTableChange = (pagination, sorter) => {
  //   // 更新分页信息
  //   setPageInfo({
  //     index: pagination.current - 1,
  //     limit: pagination.pageSize,
  //   });
  //   // 更新排序信息
  //   if (sorter&&sorter.field) {
  //     setSortInfo([{
  //       id: sorter.field,
  //       sort: sorter.order === 'ascend' ? 'asc' : 'desc'
  //     }]);
  //   }
  // };
  const handleTableChange = (pagination, filters, sorter) => {
    // 处理多列排序的情况
    let newSortInfo: SortInfo = [];

    if (sorter && sorter.field) {
      // 单列排序
      newSortInfo = [{
        id: sorter.field as string,
        sort: sorter.order === 'ascend' ? 'asc' : 'desc'
      }];
    } else if (Array.isArray(sorter)) {
      // 多列排序
      newSortInfo = sorter
        .filter(s => s.order)
        .map(s => ({
          id: s.field as string,
          sort: s.order === 'ascend' ? 'asc' : 'desc'
        }));
    }

    // 更新分页和排序信息
    setPageInfo({
      index: pagination.current - 1,
      limit: pagination.pageSize,
    });

    // 只有当排序信息确实发生变化时才更新
    if (JSON.stringify(newSortInfo) !== JSON.stringify(sortInfo)) {
      setSortInfo(newSortInfo);
    }
  };

  const handleDenyListClick = () => {
    // console.log(`handleDenyListClick`);
    navigate(`/wireless/devices/denyList#${siteId || 'all'}`);
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    getDevices.refetch().finally(() => setIsRefreshing(false));
    getCount.refetch();

  };

  useImperativeHandle(ref, () => ({
    refreshTable: () => {
      handleRefresh();
    },
  }));
  const data = getDevices.data?.devicesWithStatus ?? [];
  const columns = useMemo((): Column[] => [
    {
      key: 'connected',
      title: 'Status',
      footer: '',
      dataIndex: 'connected',
      render: (_, record: DeviceWithStatus) => statusCell(record),
      fixed: 'left',
      sorter: false,
      isMonospace: true,
    },
    {
      key: 'serialNumber',
      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          {t('inventory.serial_number')}
        </span>
      ),
      dataIndex: 'serialNumber',
      render: (_, record: DeviceWithStatus) => serialCell(record),
      sorter: true,
      columnsFix: true,
    },
    {
      key: 'name',
      title: 'Name',
      dataIndex: 'name',
      enableSorting: false,
      sorter: false,
      isMonospace: true,
    },
    {
      key: 'sanity',
      title: t('devices.sanity'),
      dataIndex: 'sanity',
      footer: '',
      render: (_, record: DeviceWithStatus) => sanityCell(record),
      sorter: false,
      isMonospace: true,
    },
    {
      key: 'memory',
      title: t('analytics.memory'),
      dataIndex: 'memoryUsed',
      footer: '',
      render: (_, record: DeviceWithStatus) => memoryCell(record),
      sorter: false,
      isMonospace: true,
    },
    {
      key: 'load',
      title: 'Load',
      footer: '',
      dataIndex: 'load',
      render: (_, record: DeviceWithStatus) => loadCell(record),
      sorter: false,
      isMonospace: true,
    },
    {
      key: 'temperature',
      title: 'Temp(°C)',
      dataIndex: 'temperature',
      footer: '',
      render: (_, record: DeviceWithStatus) => temperatureCell(record),
      sorter: false,
      isMonospace: true,
    },
    {
      key: 'firmware',
      title: t('commands.revision'),
      footer: '',
      dataIndex: 'firmware',
      render: (_, record: DeviceWithStatus) => firmwareCell(record),
      sorter: false,
      isMonospace: true,
    },
    {
      key: 'compatible',
      title: t('common.type'),
      footer: '',
      dataIndex: 'compatible',
      sorter: true,
      isMonospace: true,
    },
    {
      key: 'IP',
      title: 'IP',
      footer: '',
      dataIndex: 'IP',
      render: (_, record: DeviceWithStatus) => localeCell(record),
      sorter: false,
      isMonospace: true,
    },
    {
      key: 'Venue',
      title: 'Site',
      footer: '',
      dataIndex: 'Venue',
      render: (_, record: DeviceWithStatus) => provCell(record),
      sorter: false,
      isMonospace: true,
    },
    // {
    //   key: 'radius',
    //   title: 'Rad',
    //   footer: '',
    //   dataIndex: 'hasRADIUSSessions',
    //   render: (_, record: DeviceWithStatus) => numberCell(typeof record.hasRADIUSSessions === 'number' ? record.hasRADIUSSessions : 0,),
    //   sorter: false,
    //   isMonospace: true,
    // },
    {
      key: 'GPS',
      title: 'GPS',
      footer: '',
      dataIndex: 'GPS',
      render: (_, record: DeviceWithStatus) => gpsCell(record),
      sorter: false,
      isMonospace: true,
    },
    {
      key: 'uptime',
      title: t('system.uptime'),
      footer: '',
      dataIndex: 'uptime',
      render: (_, record: DeviceWithStatus) => uptimeCell(record),
      sorter: false,
      isMonospace: true,
    },
    {
      key: 'lastRecordedContact',
      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          {t('analytics.last_connected')}
        </span>
      ),
      footer: '',
      dataIndex: 'lastRecordedContact',
      render: (_, record: DeviceWithStatus) => dateCell(record.lastRecordedContact),
      sorter: false,
      isMonospace: true,
    },
    {
      key: 'lastContact',
      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          {t('analytics.last_contact')}
        </span>
      ),
      footer: '',
      dataIndex: 'lastContact',
      render: (_, record: DeviceWithStatus) => dateCell(record.lastContact),
      sorter: false,
      isMonospace: true,
    },
    {
      key: 'lastFWUpdate',
      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          {t('controller.devices.last_upgrade')}
        </span>
      ),
      footer: '',
      dataIndex: 'lastFWUpdate',
      render: (_, record: DeviceWithStatus) => dateCell(record.lastFWUpdate),
      sorter: false,
      isMonospace: true,
    },
    {
      key: 'rxBytes',
      title: 'Rx',
      footer: '',
      dataIndex: 'rxBytes',
      render: (_, record: DeviceWithStatus) => dataCell(record.rxBytes),
      sorter: false,
      isMonospace: true,
    },
    {
      key: 'txBytes',
      title: 'Tx',
      footer: '',
      dataIndex: 'txBytes',
      render: (_, record: DeviceWithStatus) => dataCell(record.txBytes),
      sorter: false,
      isMonospace: true,
    },
    {
      key: '2G',
      title: '2G',
      footer: '',
      dataIndex: 'associations_2G',
      render: (_, record: DeviceWithStatus) => numberCell(record.associations_2G),
      sorter: false,
      isMonospace: true,
    },
    {
      key: '5G',
      title: '5G',
      footer: '',
      dataIndex: 'associations_5G',
      render: (_, record: DeviceWithStatus) => numberCell(record.associations_5G),
      sorter: false,
      isMonospace: true,
    },
    {
      key: '6G',
      title: '6G',
      footer: '',
      dataIndex: 'associations_6G',
      render: (_, record: DeviceWithStatus) => numberCell(record.associations_6G),
      sorter: false,
      isMonospace: true,
    },
    {
      key: 'connectReason',
      title: (
        <span style={{ whiteSpace: 'nowrap' }}>
          {'Connect Reason'}
        </span>
      ),
      footer: '',
      dataIndex: 'connectReason',
      render: (_, record: DeviceWithStatus) => (record.connectReason ? record.connectReason : '-'),
      sorter: false,
      isMonospace: true,
    },
    // {
    //   key: 'certificateExpiryDate',
    //   title: 'Exp',
    //   footer: '',
    //   dataIndex: 'certificateExpiryDate',
    //   render: (_, record: DeviceWithStatus) => compactDateCell(record, true),
    //   sorter: false,
    //   isMonospace: true,
    // },
    {
      key: 'actions',
      title: 'Operation',
      footer: '',
      dataIndex: 'actions',
      render: (_, record: DeviceWithStatus) => (
        <div data-col-key="actions">
          {actionsCell(record)}
        </div>),
      columnsFix: true,
      fixed: 'right',
    },
  ], [t]);

  // 计算加载状态
  const loading = getDevices.isFetching || getCount.isFetching || isRefreshing;
  return (
    <>
      <div style={{ display: 'flex', gap: 10 }}>
        {/* <ExportDevicesTableButton currentPageSerialNumbers={data.map((device) => device.serialNumber)} /> */}
        <ExportDevicesTableButtonAntd currentPageSerialNumbers={data.map((device) => device.serialNumber)} />
        <Button
          htmlType="button"
          onClick={() => {
            handleRefresh();
            message.success("Device refresh success.");
          }}
          loading={isRefreshing}
          icon={<Icon component={refreshSvg} />}
        >
          Refresh
        </Button>
        <Button icon={<Icon component={denyListSvg} />} onClick={handleDenyListClick}>
          Denylist
        </Button>
        <div style={{ display: "flex", justifyContent: "flex-end", width: "100%" }}>
          <SearchInput />
        </div>
      </div>
      <WirelessCustomTable
        columnsOrder={true}
        resizableColumns={true}
        tableId='devices-table'
        ref={ref}
        columns={columns}
        dataSource={data}
        loading={loading}
        onChange={handleTableChange}
        showColumnSelector='true'
        isShowPagination={true}
        // showColumnSelector={true}
        fetchAPIInfo={useGetDevices}
        disableInternalRowSelection
        pagination={{
          current: pageInfo.index + 1,
          pageSize: pageInfo.limit,
          total: getCount.data?.count || 0,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Total ${total} items`,
          pageSizeOptions: ['10', '20', '50', '100'],
        }}
      //   onRow={(record: DeviceWithStatus) => ({
      //   onClick: () => {
      //     navigate(`/devices/${record.serialNumber}`);
      //   },
      //   style: { cursor: 'pointer' }, // 鼠标变手势
      // })}
      // onRow={(record: DeviceWithStatus) => ({
      //   onClick: (e) => {
      //     // 防止点击 actions 列中的元素触发跳转
      //     const isAction = (e.target as HTMLElement).closest('[data-col-key="actions"]');
      //     if (!isAction) {
      //       navigate(`/wireless/devices/${record.serialNumber}#/devices/${record.serialNumber}`);
      //     }
      //   },
      //   style: { cursor: 'pointer' }, // 鼠标变手势
      // })}
      />
      <WifiScanModal modalProps={{ isOpen: isScanModalOpen, onClose: onCloseScan }} serialNumber={serialNumber} />
      <ConfigureModal modalProps={{ isOpen: isConfigureModalOpen, onClose: onCloseConfigure }} serialNumber={serialNumber} />
      <RebootModal modalProps={{ isOpen: isRebootModalOpen, onClose: onCloseReboot }} serialNumber={serialNumber} />
      <FactoryResetModal modalProps={{ isOpen: isFactoryResetModalOpen, onClose: onCloseFactoryReset }} serialNumber={serialNumber} />
      <TelemetryModal modalProps={{ isOpen: isTelemetryModalOpen, onClose: onCloseTelemetry }} serialNumber={serialNumber} />
      <TraceModal modalProps={{ isOpen: isTraceModalOpen, onClose: onCloseTrace }} serialNumber={serialNumber} />
      <EventQueueModal modalProps={{ isOpen: isEventQueueModalOpen, onClose: onCloseEventQueue }} serialNumber={serialNumber} />
    </>
  );
});

export default DevicesList;
