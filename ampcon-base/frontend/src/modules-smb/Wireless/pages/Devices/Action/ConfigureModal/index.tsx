import React from 'react';
import {
  <PERSON><PERSON>,
  Button,
  Form,
  Input,
  message,
  Space,
} from 'antd';
import { useTranslation } from 'react-i18next';
import FileInputButton from './FileInputButton';
import { useConfigureDevice } from '@/modules-smb/hooks/Network/Commands';
import { useGetDevice } from '@/modules-smb/Wireless/hooks/Network/Devices';
import { AxiosError } from '@/modules-smb/models/Axios';
import { FormModal } from '@/modules-smb/Wireless/components/Modals/FormModal';

export type ConfigureModalProps = {
  serialNumber: string;
  modalProps: {
    isOpen: boolean;
    onClose: () => void;
  };
};

const _ConfigureModal = ({ serialNumber, modalProps }: ConfigureModalProps) => {
  const { t } = useTranslation();
  const configure = useConfigureDevice({ serialNumber });
  const getDevice = useGetDevice({ serialNumber });
  const [form] = Form.useForm();

  const [newConfig, setNewConfig] = React.useState('');

  const onChange = React.useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNewConfig(e.target.value);
    form.setFieldsValue({ configuration: e.target.value });
  }, [form]);

  const onImportConfiguration = () => {
    const configValue = getDevice.data?.configuration
      ? JSON.stringify(getDevice.data.configuration, null, 4)
      : '';
    setNewConfig(configValue);
    form.setFieldsValue({ configuration: configValue });
  };

  const isValid = React.useMemo(() => {
    try {
      if (!newConfig.trim()) return false;
      JSON.parse(newConfig);
      return true;
    } catch (error) {
      return false;
    }
  }, [newConfig]);

  const onFinish = () => {
    try {
      if (getDevice.data?.blackListed) {
        message.error({
          content: `${serialNumber} on the blacklist cannot be configured`,
          duration: 5,
        });
        return;
      }
      const config = JSON.parse(newConfig);
      configure.mutate(config, {
        onSuccess: (data) => {
          if (data.errorCode === 0) {
            message.success({
              content: data.status === 'pending'
                ? 'Command is pending! It will execute once the device connects'
                : t('controller.configure.success'),
              duration: 5,
            });
            modalProps.onClose();
          } else if (data.errorCode === 1) {
            message.warning({
              content: `${data?.errorText ?? 'Unknown Warning'}`,
              duration: 5,
            });
            modalProps.onClose();
          } else {
            message.error({
              content: `${data?.errorText ?? 'Unknown Error'} (Code ${data.errorCode})`,
              duration: 5,
            });
          }
          modalProps.onClose();
        },
      });
    } catch (e) {
    }
  };

  React.useEffect(() => {
    if (modalProps.isOpen) {
      getDevice.refetch();
    } else {
      setNewConfig('');
      form.resetFields();
    }
  }, [modalProps.isOpen, form]);

  return (
    <FormModal
      open={modalProps.isOpen}
      title={t('controller.configure.title')}
      onCancel={modalProps.onClose}
      onFinish={onFinish}
      form={form}
      modalClass="ampcon-max-modal"
      bodyStyle={{
        overflowY: 'auto', // 仅纵向滚动，横向禁止（避免布局错乱）
      }}
    >
      {configure.error && (
        <Alert
          type="error"
          message={
            <div style={{ color: '#ff4d4f' }}>
              <div>{t('common.error')}</div>
              <div>{(configure.error as AxiosError)?.response?.data?.ErrorDescription}</div>
            </div>
          }
          showIcon
          closable
          style={{ marginTop: 8, marginBottom: 32 }}
        />
      )}
      <Alert
        className="custom-trace-alert"
        message={t('controller.configure.warning')}
        type="info"
        showIcon
        closable

        style={{
          marginBottom: 32,
          ...(configure.error ? {} : { marginTop: 8 })
        }}
      />

      <Form.Item
        name="configuration"
        validateStatus={!isValid && newConfig.length > 0 ? "error" : ""}
        help={!isValid && newConfig.length > 0 ? t('controller.configure.invalid') : ""}
      >
        <Space direction="vertical" style={{ width: '100%', gap: 0 }}>
          <FileInputButton
            value={newConfig}
            setValue={(v) => {
              setNewConfig(v);
              form.setFieldsValue({ configuration: v });
            }}
            refreshId="1"
            accept=".json"
            isStringFile
          />

          <Button
            type='primary'
            onClick={onImportConfiguration}
            disabled={!getDevice.data}
            style={{ marginTop: 32 }}
          >
            Current Configuration
          </Button>

          <Input.TextArea
            value={newConfig}
            onChange={onChange}
            rows={20}
            style={{ width: '100%', marginTop: 32 }}
          />
        </Space>
      </Form.Item>
    </FormModal>
  );
};

export const ConfigureModal = React.memo(_ConfigureModal);
