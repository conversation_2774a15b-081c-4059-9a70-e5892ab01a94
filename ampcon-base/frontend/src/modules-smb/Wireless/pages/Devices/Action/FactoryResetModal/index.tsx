import React, { useState, useEffect } from 'react';
import { useBoolean } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { Spin, Alert, Form, Switch, Button, Divider } from 'antd';
import { ModalProps } from '@/modules-smb/models/Modal';
import { useFactoryReset } from '@/modules-smb/Wireless/hooks/Network/Devices';
import { AmpConCustomModal } from "@/modules-ampcon/components/custom_table";

interface FactoryResetModalProps {
  modalProps: ModalProps;
  serialNumber: string;
}

const FactoryResetModal: React.FC<FactoryResetModalProps> = ({
  modalProps: { isOpen, onClose },
  serialNumber
}) => {
  const { t } = useTranslation();
  const [isRedirector, setIsRedirector] = useState<boolean>(false);
  const { mutateAsync: handleFactoryReset, isLoading } = useFactoryReset({
    serialNumber,
    onClose
  });

  // 提交工厂重置
  const handleSubmit = () => {
    handleFactoryReset({ keepRedirector: isRedirector });
  };

  // 当模态框打开时重置状态
  useEffect(() => {
    if (isOpen) {
      setIsRedirector(false);
    }
  }, [isOpen]);

  // 构建模态框内容
  const renderContent = () => {
    if (isLoading) {
      return (
        <div style={{ textAlign: 'center', padding: '30px 0' }}>
          <Spin size="large" />
        </div>
      );
    }

    return (
      <>
        <Alert
          className="custom-trace-alert"
          message={t('commands.factory_reset_warning')}
          type="info"
          showIcon
          closable
          style={{ marginTop: 14, marginBottom: 24 }}
        />

        <Form initialValues={{ keepRedirector: false }}>
          <Form.Item
            name="keepRedirector"
            label={t('commands.keep_redirector')}
            valuePropName="checked"
          >
            <Switch
              checked={isRedirector}
              onChange={setIsRedirector}
            />
          </Form.Item>
        </Form>

        <div style={{ margin: '20px 0 10px' }}>
          <Button
            size="large"
            type="primary"
            onClick={handleSubmit}
            loading={isLoading}
          >
            {t('commands.confirm_reset', { serialNumber })}
          </Button>
        </div>
      </>
    );
  };

  return (
    <AmpConCustomModal
      title={t('commands.factory_reset')}
      childItems={renderContent()}
      isModalOpen={isOpen}
      onCancel={onClose}
      footer={null}
      modalClass="ampcon-middle-modal"
    />
  );
};

export default FactoryResetModal;
