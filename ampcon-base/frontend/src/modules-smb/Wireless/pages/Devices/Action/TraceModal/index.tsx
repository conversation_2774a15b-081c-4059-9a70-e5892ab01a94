import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, Button, Form, Select, Switch, Spin, Row, Col } from 'antd';
import { useDownloadTrace, useTrace } from '@/modules-smb/Wireless/hooks/Network/Trace';
import { AmpConCustomModal } from "@/modules-ampcon/components/custom_table";
import '../Form.scss';
export type TraceModalProps = {
  serialNumber: string;
  modalProps: {
    isOpen: boolean;
    onClose: () => void;
  };
};

type FormValues = {
  type: 'duration' | 'packets';
  network: 'up' | 'down';
  waitForResponse: boolean;
  duration: string;
  packets: string;
};

export const TraceModal = ({ serialNumber, modalProps }: TraceModalProps) => {
  const { t } = useTranslation();
  const [formState, setFormState] = React.useState<FormValues>({
    type: 'duration',
    network: 'up',
    waitForResponse: true,
    duration: '20',
    packets: '100',
  });
  
  const traceDevice = useTrace({
    serialNumber,
    alertOnCompletion: !formState.waitForResponse
  });
  
  const download = useDownloadTrace({
    serialNumber,
    commandId: traceDevice.data?.data.UUID ?? ''
  });

  const onFormChange = (name: string, value: string) => {
    setFormState({
      ...formState,
      [name]: value,
    });
  };

  const onToggleChange = (name: string, checked: boolean) => {
    setFormState({
      ...formState,
      [name]: checked,
    });
  };

  const onStart = () => {
    traceDevice.mutate({
      serialNumber,
      type: formState.type,
      network: formState.network,
      waitForResponse: formState.waitForResponse,
      duration: formState.type === 'duration' ? parseInt(formState.duration, 10) : undefined,
      packets: formState.type === 'packets' ? parseInt(formState.packets, 10) : undefined,
    });
    if (!formState.waitForResponse) {
      modalProps.onClose();
    }
  };

  const onDownload = () => {
    download.refetch();
  };

  // 定义模态框内容
  const modalContent = (
    <div className='TraceModalCreate'>
      {traceDevice.isLoading && (
        <div style={{ textAlign: 'center', padding: '100px 0' }}>
          <Spin size="large" />
        </div>
      )}

      {!traceDevice.isLoading && !traceDevice.data && !traceDevice.error && (
        <>
          <div >
             <Alert
                message={t('controller.devices.trace_description')}
                type="info"
                showIcon
                closable
                className="custom-trace-alert" 
                style={{ 
                  marginTop: 14,
                  marginBottom: 32,
                  whiteSpace: 'normal' 
                }}
              />
          </div>

          <Row  >
            <Col xs={24}>
              <Form.Item label={t('common.type')}>
                <Select
                  value={formState.type}
                  onChange={(value) => onFormChange('type', value as string)}
                  style={{ width: '100%' }}
                >
                  <Select.Option value="duration">{t('controller.trace.duration')}</Select.Option>
                  <Select.Option value="packets">{t('controller.trace.packets')}</Select.Option>
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24}>
              {formState.type === 'duration' ? (
                <Form.Item label={t('controller.trace.duration')}>
                  <Select
                    value={formState.duration}
                    onChange={(value) => onFormChange('duration', value as string)}
                    style={{ width: '100%' }}
                  >
                    <Select.Option value="20">20s</Select.Option>
                    <Select.Option value="40">40s</Select.Option>
                    <Select.Option value="60">60s</Select.Option>
                    <Select.Option value="120">120s</Select.Option>
                  </Select>
                </Form.Item>
              ) : (
                <Form.Item label={t('controller.trace.packets')}>
                  <Select
                    value={formState.packets}
                    onChange={(value) => onFormChange('packets', value as string)}
                    style={{ width: '100%' }}
                  >
                    <Select.Option value="100">100</Select.Option>
                    <Select.Option value="250">250</Select.Option>
                    <Select.Option value="500">500</Select.Option>
                    <Select.Option value="1000">1000</Select.Option>
                  </Select>
                </Form.Item>
              )}
            </Col>

            <Col xs={24}>
              <Form.Item label={t('controller.trace.network')}>
                <Select
                  value={formState.network}
                  onChange={(value) => onFormChange('network', value as string)}
                  style={{ width: '100%' }}
                >
                  <Select.Option value="up">{t('controller.trace.up')}</Select.Option>
                  {/* <Select.Option value="down">{t('controller.trace.down')}</Select.Option> */}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24}>
              <Form.Item label={t('controller.trace.wait')}>
                <Switch
                  size="large"
                  checked={formState.waitForResponse}
                  onChange={(checked) => onToggleChange('waitForResponse', checked)}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      )}

      <div style={{ marginTop: 16, marginBottom: 16 }}>
        {traceDevice.data ? (
          <>
            <Button
              type="primary"
              onClick={onDownload}
              loading={download.isFetching}
              style={{ marginRight: 8 }}
            >
              {t('controller.trace.download')}
            </Button>
            <Button
              type="primary"
              onClick={traceDevice.reset}
            >
              {t('common.go_back')}
            </Button>
          </>
        ) : (
          <Button
            type="primary"
            onClick={onStart}
            loading={traceDevice.isLoading}
            style={{ width: '100px' }}
          >
            {t('common.start')}
          </Button>
        )}
      </div>
    </div>
  );


  return (
    <AmpConCustomModal
      title={t('controller.devices.trace')}
      childItems={modalContent}
      isModalOpen={modalProps.isOpen}
      onCancel={modalProps.onClose}
      footer={null}
      modalClass="ampcon-middle-modal"
    />
  );
};
