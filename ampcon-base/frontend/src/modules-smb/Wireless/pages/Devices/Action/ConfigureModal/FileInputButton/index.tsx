import React, { useEffect, useState } from 'react';
import { Input } from 'antd';
import { v4 as uuid } from 'uuid';

interface FileInputButtonProps {
  value: string;
  setValue: (v: string, file?: File) => void;
  setFileName?: (v: string) => void;
  refreshId: string;
  accept: string;
  isHidden?: boolean;
  isStringFile?: boolean;
  sizeLimit?: number;
}

const FileInputButton: React.FC<FileInputButtonProps> = ({
  value,
  setValue,
  setFileName,
  refreshId,
  accept,
  isHidden,
  isStringFile,
  sizeLimit,
}) => {
  const [fileKey, setFileKey] = useState(uuid());
  let fileReader: FileReader | undefined;

  const handleStringFileRead = (file: File) => () => {
    if (fileReader) {
      const content = fileReader.result;
      if (content) {
        setValue(content as string, file);
      }
    }
  };

  const changeFile = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files ? e.target.files[0] : undefined;
    if (file) {
      if (sizeLimit && file.size > sizeLimit) {
        setFileKey(uuid());
      } else {
        const newVal = URL.createObjectURL(file);
        if (!isStringFile) {
          setValue(newVal, file);
          if (setFileName) setFileName(file.name ?? '');
        } else {
          fileReader = new FileReader();
          if (setFileName) setFileName(file.name);
          fileReader.onloadend = handleStringFileRead(file);
          fileReader.readAsText(file);
        }
      }
    }
  };

  useEffect(() => {
    if (value === '') setFileKey(uuid());
  }, [refreshId, value]);

  return (
    <div style={{ display: isHidden ? 'none' : 'block'  }}>
      <Input
        type="file"
        onChange={changeFile}
        key={fileKey}
        accept={accept}
        style={{
          width: '280px',
          height: '36px',
        }}
      />
    </div>
  );
};

export default React.memo(FileInputButton);