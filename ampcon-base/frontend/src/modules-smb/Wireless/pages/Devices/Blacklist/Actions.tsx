import React, { useState } from 'react';
import { But<PERSON>, <PERSON>, Popconfirm, message, Modal, Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { BlacklistDevice } from '@/modules-smb/Wireless/hooks/Network/Blacklist';
import { useDeleteBlacklistDevice } from '@/modules-smb/Wireless/hooks/Network/Blacklist';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";

interface Props {
  device: BlacklistDevice;
  refreshTable: () => void;
  onOpenEdit: (device: BlacklistDevice) => void;
  siteId?: string;
}

const Actions: React.FC<Props> = ({ device, refreshTable, onOpenEdit, siteId }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { mutateAsync: deleteDevice, isLoading: isDeleting } = useDeleteBlacklistDevice();
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);

  const handleDelete = () => {
    confirmModalAction(
      t('Are you sure you want to delete this Device?'),
      async () => {
        try {
          await deleteDevice({ serialNumber: device.serialNumber, siteId });
          refreshTable();
          message.success(t('Successfully Deleted'));
        } catch {
          message.error(t('Delete failed'));
        }
      }
    );
  };

  const handleEdit = () => {
    onOpenEdit({ ...device, siteId });
  };

  return (
    <Space size="middle">
      <Button
        type="link"
        style={{ color: '#14C9BB', padding: 0 }}
        onClick={handleEdit}
      >
        {t('common.edit')}
      </Button>

      <Button
        type="link"
        style={{ color: '#14C9BB', padding: 0 }}
        onClick={() => navigate(`/wireless/devices/${device.serialNumber}`)}
      >
        {t('common.view_details')}
      </Button>

      <Button
        type="link"
        style={{ color: '#14C9BB', padding: 0 }}
        onClick={handleDelete}
      >
        {t('crud.delete')}
      </Button>
    </Space >
  );
};

export default Actions;