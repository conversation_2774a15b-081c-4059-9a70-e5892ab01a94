import React, { useEffect, useRef, useState } from 'react';
import { Modal, Form, Input, Button, message, Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import { useCreateBlacklist } from '@/modules-smb/Wireless/hooks/Network/Blacklist';
import { AxiosError } from '@/modules-smb/models/Axios';

interface CreateBlacklistModalProps {
  isOpen: boolean;
  onClose: () => void;
  tableController: any;
  totalCount: number;
  refetchCount: () => Promise<any>;
  siteId?: string;
}

const CreateBlacklistModal: React.FC<CreateBlacklistModalProps> = ({ 
  isOpen, onClose, tableController, totalCount, refetchCount, siteId 
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const createDevice = useCreateBlacklist();
  const inputRef = useRef<Input | null>(null);
  const [serialNumberError, setSerialNumberError] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 200);
    } else {
      form.resetFields();
      setSerialNumberError('');
      setIsSubmitting(false);
    }
  }, [isOpen, form]);

  const handleOk = async () => {
    setIsSubmitting(true);
    
    try {
      const values = await form.validateFields();
      const { serialNumber, reason } = values;

      await createDevice.mutateAsync(
        { serialNumber, reason, siteId },
        {
          onSuccess: async () => {
            message.success(t('Successfully Added'));
            await refetchCount();
            const newTotalCount = totalCount + 1;
            const totalPages = Math.ceil(newTotalCount / tableController.pageInfo.pageSize);
            tableController.onPaginationChange({
              pageIndex: totalPages - 1,
              pageSize: tableController.pageInfo.pageSize,
            });
            onClose();
          },
          onError: (e) => {
            message.error((e as AxiosError)?.response?.data?.ErrorDescription || t('common.error'));
          },
        },
      );
    } catch (error) {
      
    } finally {
      setIsSubmitting(false);
    }
  };

  const validateSerial = (_: any, value: string) => {
    if (!value) {
      setSerialNumberError(t('Invalid serial number. A serial number should only be 12 HEX chars (A-F, 0-9)'));
      return Promise.reject(new Error(t('Invalid serial number. A serial number should only be 12 HEX chars (A-F, 0-9)')));
    }
    const isValid = value?.length === 12 && /^[a-fA-F0-9]+$/.test(value);
    if (!isValid) {
      setSerialNumberError(t('Invalid serial number. A serial number should be 12 HEX chars (A-F, 0-9)'));
      return Promise.reject(new Error(t('Invalid serial number. A serial number should be 12 HEX chars (A-F, 0-9)')));
    }

    setSerialNumberError('');
    return Promise.resolve();
  };

  return (
    <Modal
      title={null}
      open={isOpen}
      onCancel={onClose}
      onOk={handleOk}
      width={680}
      bodyStyle={{
        height: 365
      }}
      style={{
        height: '450px',
        borderRadius: '8px',
        overflow: 'hidden'
      }}
      footer={[
        <Button key="cancel" onClick={onClose}>
          {t('Cancel')}
        </Button>,
        <Button 
          key="submit" 
          type="primary" 
          onClick={handleOk} 
          loading={isSubmitting}
          style={{
            color: '#FFFFFF',
            background: '#14C9BB',
            borderColor: '#14C9BB',
          }}
        >
          {t('Apply')}
        </Button>,
      ]}
    >
      <div style={{ padding: ' 0 0 0' }}>
        <div
          style={{
            width: '65px',
            height: '24px',
            fontFamily: 'Lato, sans-serif',
            fontWeight: 700,
            fontSize: '20px',
            lineHeight: '24px',
            textAlign: 'left',
            fontStyle: 'normal',
            textTransform: 'none',
          }}
        >
          {t('Create')}
        </div>
        <Divider style={{ margin: '10px 0px 16px -24px', width: 'calc(100% + 48px)' }} />
        
      </div>
      <Form
        form={form}
        layout="horizontal"
        labelAlign="left"
        labelCol={{ flex: '100px' }}
        wrapperCol={{ flex: 'auto' }}
        style={{ paddingBottom: '60px' }}
      >
        <Form.Item
          label={t('Serial Number')}
          name="serialNumber"
          validateTrigger="onChange"
          rules={[
            { message: t('Invalid serial number. A serial number should only be 12 HEX chars (A-F, 0-9)') },
            { validator: validateSerial },
          ]}
          validateStatus={serialNumberError ? 'error' : ''}
          help={serialNumberError}
        >
          <Input 
            ref={inputRef} 
            style={{ 
              width: '280px', 
              height: '36px', 
              borderRadius: '2px', 
              border: serialNumberError ? '1px solid #F53F3F' : '1px solid #B2B2B2' 
            }} 
          />
        </Form.Item>

        <Form.Item
          label={t('Reason')}
          name="reason"
          rules={[{ required: false }]}
        >
          <Input.TextArea 
            rows={2} 
            style={{ 
              width: '280px', 
              height: '56px', 
              borderRadius: '2px', 
              border: '1px solid #B2B2B2' 
            }} 
          />
        </Form.Item>
      </Form>
      <Divider style={{ margin: '110px 0px 16px -24px', width: 'calc(100% + 48px)' }} />
    </Modal>
  );
};

export default CreateBlacklistModal;