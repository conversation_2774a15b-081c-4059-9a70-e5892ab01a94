import React from 'react';
import { Modal, Checkbox, Button, Space, Input, Divider, message } from 'antd';
import { SettingOutlined, RedoOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { SearchOutlined } from '@ant-design/icons';

interface BlacklistSettingsModalProps {
    isOpen: boolean;
    onClose: () => void;
    controller: {
        columnVisibility: Record<string, boolean>;
        columnOrder: string[];
        setColumnVisibility: (vis: Record<string, boolean>) => void;
        setColumnOrder: (order: string[]) => void;
        resetPreferences: () => void;
    };
    columns: Array<{
        key: string;
        title: string;
        dataIndex?: string;
        columnsFix?: boolean;
    }>;
}

const BlacklistSettingsModal = ({ isOpen, onClose, controller, columns }: BlacklistSettingsModalProps) => {
    const { t } = useTranslation();
    const [searchText, setSearchText] = React.useState('');

    const modalStyle: React.CSSProperties = {
        position: 'fixed',
        top: '265px',
        right: '54px',
        background: '#FFFFFF',
        boxShadow: '0px 4px 10px 0px rgba(0,0,0,0.15)',
        borderRadius: '2px',
        overflow: 'hidden',
        padding: 0
    };

    const handleReset = () => {
        controller.setColumnVisibility({});
        controller.setColumnOrder(columns.map(col => col.key));
        controller.resetPreferences();
        message.success(t('Successfully reseted !'));
    };

    const filteredColumns = columns.filter(column =>
        column.title.toLowerCase().includes(searchText.toLowerCase())
    );

    const handleSelectAll = (checked: boolean) => {
        const newVisibility: Record<string, boolean> = {};
        columns.forEach(col => {
            newVisibility[col.key] = checked;
        });
        controller.setColumnVisibility(newVisibility);
    };
    const allChecked = columns.every(col => controller.columnVisibility[col.key] !== false);
    const indeterminate = !allChecked && columns.some(col => controller.columnVisibility[col.key] !== false);

    return (
        <Modal
            open={isOpen}
            onCancel={onClose}
            footer={null}
            width={245}
            style={modalStyle}
            closable={false}
            mask={false}
            bodyStyle={{ padding: 0, overflow: 'visible' }}
        >
            <div>
                <Input
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    allowClear
                    prefix={<SearchOutlined style={{ width: 16, height: 16, borderRadius: '0 0 0 0', color: '#B2B2B2' }} />}
                    style={{
                        marginLeft: -6,
                        marginTop: -16,
                        width: '210px',
                        height: '32px',
                        borderRadius: '2px',
                        border: '1px solid #B2B2B2'
                    }}
                />
            </div>

            <div style={{
                display: 'flex',
                alignItems: 'center',
                height: '36px',
                paddingTop: '4px',
                marginLeft: -16,
                background: '#FFFFFF',
            }}>
                <Checkbox
                    checked={allChecked}
                    indeterminate={indeterminate}
                    onChange={e => handleSelectAll(e.target.checked)}
                    style={{
                        marginRight: '8px',
                        color: '#14C9BB',
                    }}
                />
                <span style={{
                    fontFamily: 'Lato, sans-serif',
                    fontSize: '14px',
                    color: '#212519',
                    fontWeight: 500
                }}>
                    ALL
                </span>
            </div>

            <div style={{
                overflowY: 'auto',
                marginLeft: -32,
                //marginBottom: 114
            }}>
                {filteredColumns.map(column => (
                    <div
                        key={column.key}
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            height: '36px',
                            padding: '0 16px',
                            background: '#FFFFFF'
                        }}
                    >
                        <Checkbox
                            checked={controller.columnVisibility[column.key] !== false}
                            disabled={column.columnsFix}
                            onChange={e => {
                                if (column.columnsFix) return;
                                controller.setColumnVisibility({
                                    ...controller.columnVisibility,
                                    [column.key]: e.target.checked
                                });
                            }}
                            style={{
                                marginRight: '8px',
                                color: '#14C9BB',
                            }}
                        />
                        <span style={{
                            fontFamily: 'Lato, sans-serif',
                            fontSize: '14px',
                            color: '#212519'
                        }}>
                            {column.title}
                        </span>
                    </div>
                ))}
            </div>

        </Modal >
    );
};

export default BlacklistSettingsModal;