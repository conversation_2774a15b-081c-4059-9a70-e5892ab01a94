import React, { useMemo, useState, useCallback, useEffect } from 'react';
import { Box } from '@chakra-ui/react';
import { Button, Space, message, Typography, Card, Tooltip } from 'antd';
import { PlusOutlined, ReloadOutlined, SettingOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
import { useGetBlacklistDevices, useGetBlacklistCount, BlacklistDevice } from '@/modules-smb/Wireless/hooks/Network/Blacklist';
import { useDataGrid } from '@/modules-smb/components/DataTables/DataGrid/useDataGrid';
import FormattedDate from '@/modules-smb/components/InformationDisplays/FormattedDate';
import { addSvg } from "@/utils/common/iconSvg";
import refreshSvg from "@/modules-smb/Wireless/assets/Devices/icon_Refresh.svg?react";
import Icon from "@ant-design/icons";
import Actions from './Actions';
import CreateBlacklistModal from './CreateModal';
import EditBlacklistModal from './EditModal';
import BlacklistSettingsModal from './BlacklistSettingsModal';

type BlacklistDeviceListProps = {
  siteId?: string;
};

const BlacklistDeviceList = ({ siteId: propSiteId }: BlacklistDeviceListProps) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const [device, setDevice] = useState<BlacklistDevice>();
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  // 从URL参数获取siteId，如果没有则使用props中的siteId，最后默认为'all'
  const hashSiteId = location.hash.replace('#', '') || undefined;
  const siteId = hashSiteId || propSiteId || 'all';

  const tableController = useDataGrid({
    tableSettingsId: 'gateway.blacklist.table',
    defaultOrder: ['serialNumber'],
    pageSize: 10,
  });

  const pageInfo = {
    limit: tableController.pageInfo.pageSize,
    index: tableController.pageInfo.pageIndex,
  };

  const getDevices = useGetBlacklistDevices({
    pageInfo,
    enabled: true,
    siteId,
  });

  const getCount = useGetBlacklistCount({
    enabled: true,
    siteId
  });

  const handleEdit = useCallback((dev: BlacklistDevice) => {
    setDevice(dev);
    setIsEditOpen(true);
  }, []);

  const goToSerial = useCallback((serial: string) => {
    navigate(`/wireless/devices/${serial}`);
  }, [navigate]);

  const handleBack = () => navigate(-1);

  useEffect(() => {
    if (!getCount.data?.count) return;

    const totalPages = Math.ceil(getCount.data.count / tableController.pageInfo.pageSize);
    if (tableController.pageInfo.pageIndex + 1 > totalPages && totalPages > 0) {
      tableController.onPaginationChange({
        pageIndex: totalPages - 1,
        pageSize: tableController.pageInfo.pageSize,
      });
    } else {
      getDevices.refetch();
    }
  }, [getCount.data?.count, tableController.pageInfo.pageIndex, tableController.pageInfo.pageSize]);

  const safeRefetch = useCallback(() => {
    const total = getCount.data?.count ?? 0;
    const totalPages = Math.ceil(total / tableController.pageInfo.pageSize);
    if (tableController.pageInfo.pageIndex + 1 > totalPages && totalPages > 0) {
      tableController.onPaginationChange({
        pageIndex: totalPages - 1,
        pageSize: tableController.pageInfo.pageSize,
      });
    } else {
      getDevices.refetch();
    }
  }, [getCount.data?.count, tableController.pageInfo.pageIndex, tableController.pageInfo.pageSize, getDevices.refetch]);

  const handleTableChange = useCallback((pagination) => {
    tableController.onPaginationChange({
      pageIndex: pagination.current! - 1,
      pageSize: pagination.pageSize!,
    });
  }, [tableController]);

  const fullColumns = useMemo(() => [
    {
      key: 'serialNumber',
      title: t('Serial Number'),
      dataIndex: 'serialNumber',
      sorter: (a: BlacklistDevice, b: BlacklistDevice) => a.serialNumber.localeCompare(b.serialNumber),
      columnsFix: true,
      render: (text: string, record: BlacklistDevice) => (
        <Typography.Link
          onClick={() => goToSerial(record.serialNumber)}
          style={{
            height: "17px",
            fontFamily: "Lato, sans-serif",
            fontWeight: 400,
            fontSize: "14px",
            color: "#14C9BB",
            lineHeight: "17px",
            textAlign: "left",
            fontStyle: "normal",
            textDecoration: "underline",
            textDecorationColor: "#14C9BB",
            textTransform: "none",
            display: "inline-block",
            cursor: "pointer"
          }}
        >
          {text}
        </Typography.Link>
      ),
    },
    {
      key: 'created',
      title: 'Added',
      dataIndex: 'created',
      sorter: (a: BlacklistDevice, b: BlacklistDevice) =>
        new Date(a.created).getTime() - new Date(b.created).getTime(),
      render: (text: string) => <FormattedDate date={text} />,
    },
    {
      key: 'author',
      title: 'By',
      dataIndex: 'author',
      sorter: (a: BlacklistDevice, b: BlacklistDevice) => a.author.localeCompare(b.author),
    },
    {
      key: 'reason',
      title: 'Reason',
      dataIndex: 'reason',
      sorter: (a: BlacklistDevice, b: BlacklistDevice) => (a.reason || '').localeCompare(b.reason || ''),
    },
    {
      key: 'actions',
      title: 'Operation',
      columnsFix: true,
      render: (_: unknown, record: BlacklistDevice) => (
        <Actions
          device={record}
          refreshTable={safeRefetch}
          onOpenEdit={handleEdit}
          siteId={siteId}
        />
      ),
    }
  ], [t, goToSerial, handleEdit, safeRefetch, isSettingsOpen, siteId]);

  const simpleColumns = useMemo(() => {
    return fullColumns
      .filter(col => typeof col.title === 'string')
      .map(col => ({
        key: col.key,
        title: col.title as string,
        dataIndex: col.dataIndex,
        columnsFix: col.columnsFix || false,
        fixed: col.key === 'serialNumber' || col.key === 'actions',
      }));
  }, [fullColumns]);

  useEffect(() => {
    if (Object.keys(tableController.columnVisibility || {}).length === 0) {
      const allVisible: Record<string, boolean> = {};
      simpleColumns.forEach(col => {
        allVisible[col.key] = col.columnsFix ? true : true;
      });
      tableController.setColumnVisibility(allVisible);
    }
  }, [simpleColumns, tableController]);

  const visibleColumns = useMemo(() => {
    return fullColumns.filter(col => {
      if (col.columnsFix) return true;
      if (col.key === 'settings') return true;
      return tableController.columnVisibility[col.key] !== false;
    });
  }, [fullColumns, tableController.columnVisibility]);

  return (
    <>
      <Box style={{ marginTop: 1, marginBottom: 16 }}>
        <Box
          as="button"
          onClick={handleBack}
          display="flex"
          fontFamily="Lato, sans-serif"
          fontWeight={500}
          fontSize="14px"
          color="#929A9E"
          lineHeight="16px"
          cursor="pointer"
          bg="transparent"
          border="none"
          p={0}
          _hover={{ opacity: 0.8 }}
          _active={{ opacity: 0.6 }}
          _focus={{ outline: 'none' }}
        >
          <ArrowLeftOutlined style={{ marginRight: "8px", fontSize: "14px" }} />
          <Box as="span">Back</Box>
        </Box>
      </Box>

      <Card style={{ width: '100%', minHeight: '100%', borderRadius: 0, boxShadow: 'none' }} bodyStyle={{ padding: 24 }}>
        <Space style={{ marginTop: 16, marginBottom: 8 }}>
          <Button type="primary" onClick={() => setIsCreateOpen(true)}>
            <Icon component={addSvg} />
            Create
          </Button>
          <Button
            onClick={() => {
              safeRefetch();
              message.success('Blacklist refreshed');
            }}
            icon={<Icon component={refreshSvg} style={{ fontSize: 15.6 }}/>}
          >
            {/* <Icon component={refreshSvg} /> */}
            
            Refresh
          </Button>
        </Space>

        <WirelessCustomTable
          columns={visibleColumns}
          dataSource={getDevices.data?.devices || []}
          showColumnSelector='true'
          loading={getDevices.isFetching || getCount.isFetching}
          pagination={{
            current: tableController.pageInfo.pageIndex + 1,
            pageSize: tableController.pageInfo.pageSize,
            total: getCount.data?.count,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} items`,
          }}
          onChange={handleTableChange}
        />
      </Card>


      <CreateBlacklistModal
        isOpen={isCreateOpen}
        onClose={() => setIsCreateOpen(false)}
        tableController={tableController}
        totalCount={getCount.data?.count || 0}
        refetchCount={getCount.refetch}
        siteId={siteId}
      />
      <EditBlacklistModal
        device={device}
        modalProps={{ isOpen: isEditOpen, onClose: () => setIsEditOpen(false) }}
      />
    </>
  );
};

export default React.memo(BlacklistDeviceList);