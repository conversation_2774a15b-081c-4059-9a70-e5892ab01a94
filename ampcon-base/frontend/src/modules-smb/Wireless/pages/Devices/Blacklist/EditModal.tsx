import React, { useEffect, useState } from "react";
import { Modal, Form, Input, Alert, message, Divider } from "antd";
import { useTranslation } from "react-i18next";
import { BlacklistDevice, useUpdateBlacklist } from "@/modules-smb/Wireless/hooks/Network/Blacklist";

type Props = {
  modalProps: {
    isOpen: boolean;
    onClose: () => void;
  };
  device?: BlacklistDevice;
};

const EditBlacklistModal: React.FC<Props> = ({ modalProps, device }) => {
  const { t } = useTranslation();
  const updateDevice = useUpdateBlacklist();
  const [form] = Form.useForm();
  const [initialValues, setInitialValues] = useState({ reason: "" });

  const onSave = () => {
    form
      .validateFields()
      .then((values) => {
        updateDevice.mutate(
          { serialNumber: device?.serialNumber ?? "", reason: values.reason, siteId: device?.siteId  },
          {
            onSuccess: () => {
              updateDevice.reset();
              message.success(t("controller.devices.updated_blacklist"));
              modalProps.onClose();
            },
          }
        );
      })
      .catch(() => { });
  };
  const handleCancel = () => {
    form.setFieldsValue(initialValues);
    modalProps.onClose();
  };

  useEffect(() => {
    if (device) {
      const newInitialValues = { reason: device?.reason ?? "" };
      setInitialValues(newInitialValues);
      form.setFieldsValue(newInitialValues);
    }
  }, [device, form, modalProps.isOpen]);

  return (
    <Modal
      open={modalProps.isOpen}
      width={680}
      bodyStyle={{
        height: 365
      }}
      style={{
        height: '450px',
        borderRadius: '8px',
        overflow: 'hidden'
      }}

      onCancel={handleCancel}
      onOk={onSave}
      confirmLoading={updateDevice.isLoading}
      okText={t("Apply")}
      cancelText={t("Cancel")}
      destroyOnClose
    >
      <div style={{ padding: ' 0 0 0' }}>
        <div
          style={{
            width: '65px',
            height: '24px',
            fontFamily: 'Lato, sans-serif',
            fontWeight: 700,
            fontSize: '20px',
            lineHeight: '24px',
            textAlign: 'left',
            fontStyle: 'normal',
            textTransform: 'none',
            //marginTop: -9
          }}
        >
          {t('Edit')}
        </div>
        <Divider style={{ margin: '10px 0px 16px -24px', width: 'calc(100% + 48px)' }} />
      </div>
      {updateDevice.error && (
        <Alert
          message={t("common.error")}
          description={
            updateDevice.error?.response?.data?.ErrorDescription
          }
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Form form={form} layout="horizontal" initialValues={initialValues}>
        <Form.Item
          label={t("controller.devices.reason")}
          name="reason"
          rules={[{ required: false, message: t("common.required") }]}
          style={{ paddingTop: '5px', paddingBottom: '85px' }}
        >
          <Input.TextArea rows={2} style={{ width: '280px', height: '56px', borderRadius: '2px 2px 2px 2px', border: '1px solid #B2B2B2' }} />
        </Form.Item>
      </Form>
      <Divider style={{ margin: '165px 0px 16px -24px', width: 'calc(100% + 48px)' }} />
    </Modal>
  );
};

export default EditBlacklistModal;
