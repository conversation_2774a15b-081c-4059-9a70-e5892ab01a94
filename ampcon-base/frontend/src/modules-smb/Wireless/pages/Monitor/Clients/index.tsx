import { But<PERSON>, Card, Tag, Input, message, Typography } from "antd";
import React, { useState, useMemo, useRef, useEffect } from 'react';
import { SearchOutlined } from '@ant-design/icons';
import {
  AmpConCustomTable,
  createColumnConfigMultipleParams,
  TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
import EditClientModal from "@/modules-ampcon/pages/Monitor/WiredClients/edit_client_modal";
import styles from "@/modules-ampcon/pages/Topo/Topology/topo.module.scss";
import { useNavigate } from "react-router-dom";
import { getWirelessClientList } from "@/modules-smb/Wireless/apis/wireless_client_api";
import { useSiteStore } from "@/modules-smb/Wireless/components/SiteContext";
import "./index.scss";


const WirelessClients = () => {

  const navigate = useNavigate();
  const editClientModalRef = useRef();
  const tableRef = useRef();

  const [loading, setLoading] = useState(false);
  const [allData, setAllData] = useState<any[]>([]);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [searchText, setSearchText] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [clientCounts, setClientCounts] = useState({ all: 0, online: 0, offline: 0 });
  const fetchCounts = async () => {
    const allRes = await getWirelessClientList({ status: 0, pageNum: 1, pageSize: 1 });
    const onlineRes = await getWirelessClientList({ status: 1, pageNum: 1, pageSize: 1 });
    const offlineRes = await getWirelessClientList({ status: 2, pageNum: 1, pageSize: 1 });

    setClientCounts({
      all: allRes.total || 0,
      online: onlineRes.total || 0,
      offline: offlineRes.total || 0,
    });
  };

  const fetchData = async ({
    pageNum = pagination.current,
    pageSize = pagination.pageSize,
    sortBy,
    sortType,
    searchValue = searchText,
    status = activeTab,
  }: {
    pageNum?: number;
    pageSize?: number;
    sortBy?: string;
    sortType?: string;
    searchValue?: string;
    status?: string | string[];
  } = {}) => {
    setLoading(true);
    try {
      const statusParam =
        status === "all" ? 0 : status === "online" ? 1 : status === "offline" ? 2 : 0;
      const res = await getWirelessClientList({
        status: statusParam,
        pageNum,
        pageSize,
        searchValue,
        sortBy,
        sortType,
      });
      const list = res.info || [];
      const processedData = list.map(item => ({
        ...item,
        mac: item.mac || "",
        ip: item.ip || "",
        sn: item.sn || "",
        ssid: item.ssid || "",
        vendor: item.vendor || "",
        venue: item.venue || "",
      }));

      setDataSource(processedData);
      setPagination(prev => ({
        ...prev,
        current: pageNum,
        pageSize,
        total: res.total || list.length,
      }));
    } catch (err) {
      setDataSource([]);
      setPagination(prev => ({ ...prev, total: 0 }));
    } finally {
      setLoading(false);
    }
  };





  useEffect(() => {
    fetchCounts();
    fetchData();
  }, []);

  const handleTabChange = (key) => {
    setActiveTab(key);
    fetchData({ status: key, pageNum: 1 });
  };

  const handleSearch = (value) => {
    setSearchText(value);
    fetchData({ searchValue: value, pageNum: 1 });
  };


  const handleStatusChange = (status) => {
    setActiveTab(status);
    setPagination({
      ...pagination,
      current: 1,
    });
  };



  const { setSelectedSiteId } = useSiteStore();

  const columns = React.useMemo(() => {
    const baseColumns = [
      {
        key: 'status',
        title: 'Status',
        dataIndex: 'status',
        columnsFix: true,
        filters: activeTab === 'all'
          ? [
            { text: 'Online', value: 1 },
            { text: 'Offline', value: 2 },
          ]
          : undefined,
        filterIcon: activeTab === 'all' ? undefined : null,
        render: (status) => {
          const isOnline = status === 1;
          return (
            <Tag
              color={isOnline ? "green" : "default"}
              style={{
                margin: 0,
                borderRadius: "2px",
                fontFamily: "Lato, sans-serif",
                fontWeight: 400,
                fontSize: "14px",
                lineHeight: "17px",
                textAlign: "left",
                fontStyle: "normal",
                textTransform: "none",
                minWidth: "59px",
                height: "24px",
                padding: 0,
                display: "inline-flex",
                alignItems: "center",
                justifyContent: "center",
                ...(isOnline
                  ? {
                    color: "#2BC174",
                    backgroundColor: "rgba(43, 193, 116, 0.1)",
                    border: "1px solid #2BC174",
                  }
                  : {
                    backgroundColor: "#F4F5F7",
                    color: "#B3BBC8",
                    border: "1px solid #DADCE1",
                  }),
              }}
            >
              {isOnline ? "Online" : "Offline"}
            </Tag>
          );
        },
      },
      {
        key: 'host_name',
        title: 'Hostname',
        dataIndex: 'host_name',
        sorter: true,
        render: (text, record) => (
          <div
            style={{
              textAlign: 'left',
              width: "100%",
              height: "17px",
              lineHeight: "17px",
              fontFamily: "Lato, sans-serif",
            }}
          >
            {text || record.mac || "-"}
          </div>
        ),
      },
      {
        key: 'mac',
        title: 'MAC Address',
        dataIndex: 'mac',
        sorter: true,
        render: (mac, record) => {
          return (
            <div style={{ width: "100%" }}>
              {mac ? (
                <a
                  onClick={() => {
                    const cleanMac = mac.replace(/:/g, '');
                    setSelectedSiteId(record.siteId);
                    navigate(`/wireless/manage/Monitor#${record.siteId}`, {
                      state: { siteId: record.siteId, scrollToClientLifecycle: true, targetMac: cleanMac },
                    });
                  }}
                  style={{
                    height: "17px",
                    fontFamily: "Lato, sans-serif",
                    fontWeight: 400,
                    fontSize: "14px",
                    color: "#14C9BB",
                    lineHeight: "17px",
                    textAlign: "left",
                    fontStyle: "normal",
                    textDecoration: "underline",
                    textDecorationColor: "#14C9BB",
                    textTransform: "none",
                    display: "inline-block",
                    cursor: "pointer",
                  }}
                >
                  {mac}
                </a>
              ) : (
                <span
                  style={{
                    display: "inline-block",
                    height: "17px",
                    lineHeight: "17px",
                    color: "#B3BBC8",
                  }}
                >
                  -
                </span>
              )}
            </div>
          );

        },
      },
      {
        key: 'vendor',
        title: 'Vendor',
        dataIndex: 'vendor',
        sorter: true,
        filterDropdown: null,
        filterIcon: false,
        render: (vendor: string) => (
          <div
            style={{
              textAlign: 'left',
              width: "100%",
            }}
          >
            {vendor || "-"}
          </div>
        ),
      },
      {
        key: 'sn',
        title: 'Connect AP',
        dataIndex: 'sn',
        sorter: true,
        filterDropdown: null,
        filterIcon: false,
        render: (sn: string, record: any) => (
          <div style={{ width: "100%" }}>
            {sn ? (
              <Typography.Link
                onClick={() => navigate(`/wireless/devices/${sn}`)}
                className={styles["ap-link"]}
                style={{
                  height: "17px",
                  fontFamily: "Lato, sans-serif",
                  fontWeight: 400,
                  fontSize: "14px",
                  color: "#14C9BB",
                  lineHeight: "17px",
                  textAlign: "left",
                  fontStyle: "normal",
                  textDecoration: "underline",
                  textDecorationColor: "#14C9BB",
                  textTransform: "none",
                  display: "inline-block",
                  cursor: "pointer"
                }}
              >
                {sn}
              </Typography.Link>
            ) : (
              <span
                style={{
                  display: "inline-block",
                  height: "17px",
                  lineHeight: "17px",
                  color: "#B3BBC8"
                }}
              >
                -
              </span>
            )}
          </div>
        ),
      },
      {
        key: 'venue',
        title: 'Site',
        dataIndex: 'venue',
        sorter: true,
        filterDropdown: null,
        filterIcon: false,
        // sorter: (a, b) => (a.venue || "").localeCompare(b.venue || ""),
        render: (venue: string, record: any) => {
          if (!venue && venue !== 0) {
            return (
              <div
                style={{
                  textAlign: 'left',
                  width: "100%",
                  height: "17px",
                  lineHeight: "17px",
                  fontFamily: "Lato, sans-serif"
                }}
              >
                -
              </div>
            );
          }
          return (
            <div
              style={{
                height: "17px",
                fontFamily: "Lato, sans-serif",
                fontWeight: 400,
                fontSize: "14px",
                color: "#14C9BB",
                lineHeight: "17px",
                textAlign: "left",
                fontStyle: "normal",
                textDecoration: "underline",
                textDecorationColor: "#14C9BB",
                textTransform: "none",
                display: "inline-block",
                cursor: "pointer"
              }}
            >
              {venue ? (
                <a
                  onClick={() => {
                    setSelectedSiteId(record.siteId);
                    navigate(`/wireless/manage/Monitor#${record.siteId}`)
                  }}
                  className={styles["venue-link"]}
                  style={{
                    display: "inline-block",
                    color: "inherit !important",
                    textDecoration: "inherit !important"
                  }}
                >
                  {venue}
                </a>
              ) : (
                <span style={{ display: "inline-block" }}>-</span>
              )}
            </div>
          );
        },
      },
      {
        key: 'ssid',
        title: 'SSID',
        dataIndex: 'ssid',
        sorter: true,
        filterDropdown: null,
        filterIcon: false,
        render: (ssid: string) => (
          <div style={{ textAlign: 'left' }}>
            {ssid || '-'}
          </div>
        ),
      },
      {
        key: 'rssi',
        title: 'RSSI',
        dataIndex: 'rssi',
        sorter: true,
        filterDropdown: null,
        filterIcon: false,
        render: (rssi: number) => {
          if (!rssi && rssi !== 0) {
            return <div style={{ textAlign: 'left', width: '100%' }}>-</div>;
          }
          return (
            <div style={{ width: '100%' }}>
              <span className={styles['rssi-value']}>{rssi} dBm</span>
            </div>
          );
        },
      },
      {
        key: 'band',
        title: 'Band',
        dataIndex: 'band',
        sorter: true,
        filterDropdown: null,
        filterIcon: false,
        render: (band: string) => <div>{band || '-'}</div>,
      },
      {
        key: 'channel',
        title: 'Channel',
        dataIndex: 'channel',
        sorter: true,
        filterDropdown: null,
        filterIcon: false,
        render: (channel: number | string) => <div>{channel || '-'}</div>,
      },
      {
        key: 'channel_width',
        title: 'Channel Width',
        dataIndex: 'channel_width',
        sorter: true,
        filterDropdown: null,
        filterIcon: false,
        render: (text: string) => (
          <div
            style={{
              textAlign: 'left',
              width: '100%',
              height: '17px',
              lineHeight: '17px',
              fontFamily: 'Lato, sans-serif',
            }}
          >
            {text || '-'}
          </div>
        ),
      },
      {
        key: 'ip',
        title: 'IP',
        dataIndex: 'ip',
        sorter: true,
        filterDropdown: null,
        filterIcon: false,
        render: (text: string) => (
          <div
            style={{
              textAlign: 'left',
              width: '100%',
              height: '17px',
              lineHeight: '17px',
              fontFamily: 'Lato, sans-serif',
            }}
          >
            {text || '-'}
          </div>
        ),
      },
      {
        key: 'vlan',
        title: 'VLAN',
        dataIndex: 'vlan',
        sorter: true,
        filterDropdown: null,
        filterIcon: false,
        render: (text: string) => (
          <div
            style={{
              textAlign: 'left',
              width: '100%',
              height: '17px',
              lineHeight: '17px',
              fontFamily: 'Lato, sans-serif',
            }}
          >
            {text || '-'}
          </div>
        ),
      },
      {
        title: "Rx",
        dataIndex: "rx",
        key: "rx",
        sorter: true,
        render: (value: string | undefined | null) => (
          <div
            style={{
              textAlign: 'left',
              width: "100%",
              height: 17,
              lineHeight: '17px',
              fontFamily: 'Lato, sans-serif',
            }}
          >
            {value ?? "-"}
          </div>
        ),
      },
      {
        title: "Tx",
        dataIndex: "tx",
        key: "tx",
        sorter: true,
        render: (value: string | undefined | null) => (
          <div
            style={{
              textAlign: 'left',
              width: "100%",
              height: 17,
              lineHeight: '17px',
              fontFamily: 'Lato, sans-serif',
            }}
          >
            {value ?? "-"}
          </div>
        ),
      },
      {
        title: "Tx Packets",
        dataIndex: "tx_packets",
        key: "tx_packets",
        sorter: true,
        render: (value: number | undefined | null) => (
          <div
            style={{
              textAlign: 'left',
              width: "100%",
              height: 17,
              lineHeight: '17px',
              fontFamily: 'Lato, sans-serif',
            }}
          >
            {value ?? "-"}
          </div>
        ),
      },
      {
        title: "Rx Packets",
        dataIndex: "rx_packets",
        key: "rx_packets",
        sorter: true,
        render: (value: number | undefined | null) => (
          <div
            style={{
              textAlign: 'left',
              width: "100%",
              height: 17,
              lineHeight: '17px',
              fontFamily: 'Lato, sans-serif',
            }}
          >
            {value ?? "-"}
          </div>
        ),
      },
    ];
    return baseColumns;
  }, [navigate, activeTab]);


  return (
    <Card style={{ display: "flex", flex: 1 }}>
      <EditClientModal
        ref={editClientModalRef}
        saveCallback={() => {
          fetchData();
        }}
      />
      <h2 style={{ margin: "8px 0 20px" }}>Wireless Clients</h2>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16
      }}>

        <div style={{ marginBottom: -10 }}>
          {['all', 'online', 'offline'].map((key, index, array) => (
            <Button
              key={key}
              type="default"
              className={`tab-button ${activeTab === key ? 'active' : ''}`}
              onClick={() => handleTabChange(key)}
            >
              {key === 'all' && `All (${clientCounts.all})`}
              {key === 'online' && `Online (${clientCounts.online})`}
              {key === 'offline' && `Offline (${clientCounts.offline})`}
            </Button>
          ))}
        </div>
        <Input
          placeholder="Search by STA, IP, AP, SSID or Vendor"
          allowClear
          enterButton={false}
          size="large"
          prefix={<SearchOutlined style={{ color: '#B8BFBF', width: 16, height: 16 }} />}
          style={{
            width: 270,
            height: 32,
            borderRadius: 2,
            fontSize: 13,
            fontWeight: 400,
            marginBottom: -14
          }}
          value={searchText}
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>

      <WirelessCustomTable
        ref={tableRef}
        loading={loading}
        columns={columns}
        dataSource={dataSource}
        rowKey="id"
        showColumnSelector='true'
        pagination={{
          ...pagination,
          showTotal: total => `Total ${total} items`,
          showSizeChanger: true,
          showQuickJumper: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          showLessItems: false
        }}
        onChange={(pagination, filters, sorter) => {
          let status: string | string[] = activeTab;

          if (activeTab === 'all' && filters?.status && filters.status.length > 0) {
            const mapped = filters.status.map((val: number) => {
              if (val === 1) return "online";
              if (val === 2) return "offline";
              return "all";
            });
            status = mapped.length === 1 ? mapped[0] : mapped;
          }
          fetchData({
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            sortBy: sorter.field,
            sortType: sorter.order === 'ascend'
              ? 'asc'
              : sorter.order === 'descend'
                ? 'desc'
                : undefined,
            status,
          });
        }}
      />

    </Card>
  );
};
export default WirelessClients;