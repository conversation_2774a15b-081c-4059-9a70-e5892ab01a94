import React from "react";
import { Typography, Tooltip } from "antd";

const { Text } = Typography;

interface InfoRowProps {
  label: string;
  value?: React.ReactNode;
  width?: number;
  disableTooltip?: boolean;
}

const InfoRow: React.FC<InfoRowProps> = ({ label, value, width = 315, disableTooltip }) => {
  const stringValue =
    typeof value === "string" || typeof value === "number" ? String(value) : "";

  const content = (
    <Text
      ellipsis
      style={{
        fontWeight: 600,
        fontSize: 14,
        color: "#212519",
        flex: 1,
      }}
    >
      {value || "----"}
    </Text>
  );
  return (
    <div
      style={{
        width,
        display: "flex",
        alignItems: "center",
        gap: 10,
      }}
    >
      <Text
        type="secondary"
        style={{
          fontWeight: 400,
          fontSize: 14,
          color: "#929A9E",
          minWidth: 120,
        }}
      >
        {label}
      </Text>

      {disableTooltip ? (
        content
      ) : (
        <Tooltip title={stringValue || value || "----"} placement="top">
          {content}
        </Tooltip>
      )}
    </div>
  );
};

export default InfoRow;
