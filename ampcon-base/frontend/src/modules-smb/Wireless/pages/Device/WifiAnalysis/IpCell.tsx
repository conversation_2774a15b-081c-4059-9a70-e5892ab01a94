import React from "react";
import { But<PERSON>, Popover, Typography, List, message, Tooltip, Divider } from "antd";
import { CopyOutlined } from "@ant-design/icons";

const { Text } = Typography;

const CopyString = ({ str }: { str: string }) => {
  const [copied, setCopied] = React.useState(false);
  const handleCopy = () => {
    navigator.clipboard.writeText(str);
    setCopied(true);
    message.success("Copied!");
    setTimeout(() => setCopied(false), 1500);
  };

  return (
    <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
      <span>{str}</span>
      <Tooltip title={copied ? "Copied!" : "Copy"}>
        <Button
          size="small"
          type="link"
          icon={<CopyOutlined style={{ color: "#14C9BB" }} />}
          onClick={handleCopy}
        />
      </Tooltip>
    </div>
  );
};

type Props = {
  ipv4: string[];
  ipv6: string[];
};

const IpCell = ({ ipv4, ipv6 }: Props) => {
  const length = ipv4.length + ipv6.length;

  return (
    <Popover
      title={<div style={{ marginBottom: 16 }}>{`${length} ${length === 1 ? "IP" : "IPs"}`}</div>}
      trigger="click"
      placement="top"
      content={
        <div>
          <div>
            <Text strong style={{ fontSize: 14, color: '#474747' }}>
              IPv4 ({ipv4.length})
            </Text>
            {ipv4.length > 0 && (
              <List
                size="small"
                dataSource={ipv4}
                split={false}
                renderItem={(ip) => (
                  <List.Item style={{ padding: '8px 0 0 0', fontSize: 14, color: '#212519' }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span style={{ marginRight: 6, color: '#474747' }}>•</span>
                      <CopyString str={ip} />
                    </div>
                  </List.Item>
                )}
              />
            )}
          </div>
          <Divider style={{ margin: '8px 0 8px 0', borderTop: '1px solid #E7E7E7' }} />
          <div>
            <Text strong style={{ fontSize: 14, color: '#474747' }}>
              IPv6 ({ipv6.length})
            </Text>
            {ipv6.length > 0 && (
              <List
                size="small"
                dataSource={ipv6}
                split={false}
                renderItem={(ip) => (
                  <List.Item style={{ padding: '8px 0 0 0', fontSize: 14, color: '#212519' }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span style={{ marginRight: 6, color: '#474747' }}>•</span>
                      <CopyString str={ip} />
                    </div>
                  </List.Item>
                )}
              />
            )}
          </div>
        </div>
      }
    >
      <Button size="small" type="primary">
        {length}
      </Button>
    </Popover>
  );
};

export default IpCell;
