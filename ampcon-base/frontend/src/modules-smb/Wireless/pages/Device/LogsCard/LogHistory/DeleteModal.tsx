import * as React from 'react';
import { useTranslation } from 'react-i18next';
import 'react-datepicker/dist/react-datepicker.css';
import DeleteButton from '@/modules-smb/Wireless/components/Button/DeleteButton';
import { useDeleteLogs } from '@/modules-smb/hooks/Network/DeviceLogs';
import { AxiosError } from '@/modules-smb/models/Axios';
import { AmpConCustomModal } from "@/modules-ampcon/components/custom_table";
import {Alert,DatePicker,message} from 'antd';
import dayjs,{ Dayjs } from 'dayjs';
import "../LogsCard.css";

type Props = { serialNumber: string; logType: 0 | 1 | 2 };
const DeleteLogModal = ({ serialNumber, logType }: Props) => {
  const { t } = useTranslation();
  const deleteLogs = useDeleteLogs();
  const [date, setDate] = React.useState<Dayjs>(dayjs());
  const [isOpen, setIsOpen] = React.useState(false);
  const onDeleteClick = () => {
    deleteLogs.mutate(
      { endDate: date.unix(), serialNumber, logType },
      {
        onSuccess: () => {
          setIsOpen(false);
          message.success(
            t('controller.crud.delete_success_obj', {
              obj: t('controller.devices.logs'),
            })
          );
        },
        onError: (e) => {
          const error = e as AxiosError;
          message.error(error?.response?.data?.ErrorDescription || t('common.error'));
        },
      },
    );
  };
  const onChange = (newDate: Date) => {
    setDate(newDate);
  };
  // 限制不能选未来日期
  const disabledDate = (current: Dayjs) => {
    return current && current > dayjs().endOf('day');
  };

   const modalContent = (
    <div>
      <Alert
        type="warning"
        showIcon
        message={t('controller.devices.delete_logs_explanation')}
        style={{ borderRadius: 8, marginBottom: 16 }}
      />
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center',gap: 10 }}>
           <DatePicker
             popupClassName="picker-split-footer"
             value={date}
             onChange={onChange}
             format="YYYY-MM-DD HH:mm:ss"
             disabledDate={disabledDate}
             showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
             disabled={deleteLogs.isLoading}
           />
         <DeleteButton onClick={onDeleteClick} isLoading={deleteLogs.isLoading} />
      </div>
        </div>
  );

  return (
    <>
      <DeleteButton onClick={() => setIsOpen(true)} isCompact />
      <AmpConCustomModal  
        title={`${t('crud.delete')} ${t('controller.devices.logs')}`}
        childItems={modalContent}
        isModalOpen={isOpen}
        onCancel={() => setIsOpen(false)}
        footer={null}
        modalClass="ampcon-middle-modal"
      />
    </>
  );
};

export default DeleteLogModal;
