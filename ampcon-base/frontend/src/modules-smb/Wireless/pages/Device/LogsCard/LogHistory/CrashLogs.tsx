import * as React from 'react';
import { Center} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import DeleteLogModal from './DeleteModal';
import useDeviceLogsTable from './useDeviceLogsTable';
import { Button} from "antd";
import dayjs from 'dayjs';
import DateTimeRangePicker from "../DateTimeRangePicker";
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
import Icon from "@ant-design/icons";
import { refreshSvg } from "@/utils/common/iconSvg";
type Props = {
  serialNumber: string;
};
const CrashLogs = ({ serialNumber }: Props) => {
  const { t } = useTranslation();
  const [limit, setLimit] = React.useState(25);
  const { setTime, getCustomLogs, getLogs, columns, modal } = useDeviceLogsTable({
    serialNumber,
    limit,
    logType: 1,
  });
  const [pickerTime, setPickerTime] = React.useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [isPanelOpen, setIsPanelOpen] = React.useState(false);
  const raiseLimit = () => {
    setLimit(limit + 25);
  };

  const noMoreAvailable = getLogs.data !== undefined && getLogs.data.values.length < limit;

  // 获取表格数据
  const getTableData = () => {
    if (getCustomLogs.data) return getCustomLogs.data.values.sort((a, b) => b.recorded - a.recorded);
    if (getLogs.data) return getLogs.data.values;
    return [];
  };
  

  // 处理时间范围变化
  const handleRangeChange = (dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
    if (dates) {
      setPickerTime(dates);
    } else {
      // 清除时恢复默认时间
      setPickerTime(null);
      setTime(undefined);
    }
  };
  // 处理面板打开/关闭
  const handleOpenChange = (open: boolean) => {
    setIsPanelOpen(open);
    if (!open && pickerTime) {
      // 面板关闭时确认选择的时间
      setTime({
        start: pickerTime[0].toDate(),
        end: pickerTime[1].toDate()
      });
    }
  };

  return (
    <>
         <DateTimeRangePicker
          value={pickerTime}
          onChange={handleRangeChange}
          placeholder={[t('common.start_time'), t('common.end_time')]}
          style={{ width: 380 }}
          onOpenChange={handleOpenChange}
          open={isPanelOpen}
          t={t}
        />
      <div style={{display: "flex",gap: 10,marginTop: 32,marginBottom:4}}>
      <DeleteLogModal serialNumber={serialNumber} logType={1} />
      <Button icon={<Icon component={refreshSvg} />}
          onClick={getLogs.refetch}
          loading={getLogs.isFetching}
        >{t('common.refresh')}</Button>
      </div>            
        <WirelessCustomTable
          columnsOrder={true}
          columns={columns}
          dataSource={getTableData()}
          loading={getLogs.isFetching || getCustomLogs.isFetching}
          showColumnSelector={true}
          pagination={false}
          disableInternalRowSelection
          scroll={{ y: getTableData().length > 6 ? 300 : undefined }}
        />
        {getLogs.data !== undefined && (
          <Center mt={1} hidden={getCustomLogs.data !== undefined}>
            {!noMoreAvailable || getLogs.isFetching ? (
              <Button 
                onClick={raiseLimit} 
                loading={getLogs.isFetching}
                type="primary"
              >
                {t('controller.devices.show_more')}
              </Button>
            ) : (
              <span></span>
            )}
          </Center>
        )}
      {modal}
    </>
  );
};

export default CrashLogs;
