import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Button, 
  Space, 
  Typography, 
} from 'antd';
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
import CommandResultModal from './ResultModal';
import useCommandHistoryTable from './useCommandHistoryTable';
import dayjs from 'dayjs';
import DateTimeRangePicker from "../DateTimeRangePicker";
import Icon from "@ant-design/icons";
import { refreshSvg } from "@/utils/common/iconSvg";


type Props = {
  serialNumber: string;
};

const CommandHistory = ({ serialNumber }: Props) => {
  const { t } = useTranslation();
  const [limit, setLimit] = React.useState(25);
  const [effectiveData, setEffectiveData] = React.useState([]); // 存储有效数据
  const [isLoadingMore, setIsLoadingMore] = React.useState(false); // 加载更多状态
  const { time, setTime, getCustomCommands, getCommands, columns, selectedCommand, detailsModalProps } =
    useCommandHistoryTable({ serialNumber, limit });
  // RangePicker相关状态
  const [pickerTime, setPickerTime] = React.useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [isPanelOpen, setIsPanelOpen] = React.useState(false);

  // 过滤函数
  const filterWifiScan = (commands) => {
    return commands.filter(command => command.command !== 'wifiscan');
  };

  // 获取有效数据
  const getEffectiveData = React.useCallback(() => {
    if (getCustomCommands.data) {
      return filterWifiScan(getCustomCommands.data.commands.sort((a, b) => b.submitted - a.submitted));
    }
    if (getCommands.data) {
      return filterWifiScan(getCommands.data.commands);
    }
    return [];
  }, [getCustomCommands.data, getCommands.data]);

  // 当数据变化时更新有效数据
  React.useEffect(() => {
    const newEffectiveData = getEffectiveData();
    setEffectiveData(newEffectiveData);
    
    // 如果有效数据不足且还有更多数据可加载，则自动加载更多
    if (newEffectiveData.length < limit && 
        !noMoreAvailable && 
        !isLoadingMore &&
        (getCommands.data?.commands.length >= limit || getCustomCommands.data?.commands.length >= limit)) {
      setIsLoadingMore(true);
      setTimeout(() => {
        setLimit(limit + 25);
        setIsLoadingMore(false);
      }, 300);
    }
  }, [getCommands.data, getCustomCommands.data, limit]);

  const raiseLimit = () => {
    setLimit(limit + 25);
  };

  // 处理时间范围变化
  const handleRangeChange = (dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
    if (dates) {
      setPickerTime(dates);
    } else {
      // 清除时恢复默认时间
      setPickerTime(null);
      setTime(undefined);
    }
  };
  // 处理面板打开/关闭
  const handleOpenChange = (open: boolean) => {
    setIsPanelOpen(open);
    if (!open && pickerTime) {
      // 面板关闭时确认选择的时间
      setTime({
        start: pickerTime[0].toDate(),
        end: pickerTime[1].toDate()
      });
    }
  };
  // 判断是否还有更多数据可加载
  const noMoreAvailable =
    getCustomCommands.data || (getCommands.data !== undefined && 
    (getCommands.data.commands.length < limit || 
     (getCommands.data.totalCount !== undefined && getCommands.data.commands.length >= getCommands.data.totalCount)));

  return (
    <>
      <Space>
        <DateTimeRangePicker
          value={pickerTime}
          onChange={handleRangeChange}
          placeholder={[t('common.start_time'), t('common.end_time')]}
          style={{ width: 380 }}
          onOpenChange={handleOpenChange}
          open={isPanelOpen}
          t={t}
        />
      </Space>
      <div style={{display: 'flex', gap: 10,marginTop: 32,marginBottom:4}}>
        <Button 
          icon={<Icon component={refreshSvg} />}
          onClick={getCommands.refetch}
          loading={getCommands.isFetching}
        >
          {t('common.refresh')}
        </Button>
      </div>
      <WirelessCustomTable
        columns={columns}
        dataSource={effectiveData}
        loading={getCommands.isFetching || getCustomCommands.isFetching || isLoadingMore}
        showColumnSelector='true'
        pagination={false}
        disableInternalRowSelection
        scroll={{x: 'max-content', y: effectiveData.length > 6 ? 300 : undefined }}
      />
      {effectiveData.length > 0 && (
        <div style={{ textAlign: 'center', padding: '16px' }}>
          {!noMoreAvailable || getCommands.isFetching || isLoadingMore ? (
            <Button 
              onClick={raiseLimit} 
              loading={getCommands.isFetching || isLoadingMore}
              type="primary"
            >
              {t('controller.devices.show_more')}
            </Button>
          ) : (
            <span></span>
          )}
        </div>
      )}
      <CommandResultModal command={selectedCommand} modalProps={detailsModalProps} />
    </>
  );
};

export default CommandHistory;