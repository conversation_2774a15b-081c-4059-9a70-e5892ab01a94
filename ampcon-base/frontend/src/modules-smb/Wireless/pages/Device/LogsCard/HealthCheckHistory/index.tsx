import * as React from 'react';
import {  Center} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import DeleteHealthChecksModal from './DeleteModal';
import useHealthCheckTable from './useHealthCheckTable';
import DateTimeRangePicker from "../DateTimeRangePicker";
import dayjs from 'dayjs';
import {Space, Button} from "antd";
import { WirelessCustomTable } from '@/modules-smb/Wireless/components/CustomTable';
import Icon from "@ant-design/icons";
import { refreshSvg } from "@/utils/common/iconSvg";
type Props = {
  serialNumber: string;
};
const HealthCheckHistory = ({ serialNumber }: Props) => {
  const { t } = useTranslation();
  const [limit, setLimit] = React.useState(25);
  const { setTime, getCustomHealthChecks, getHealthChecks, columns } = useHealthCheckTable({
    serialNumber,
    limit,
  });
  const [pickerTime, setPickerTime] = React.useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [isPanelOpen, setIsPanelOpen] = React.useState(false);
  // 处理时间范围变化
    const handleRangeChange = (dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
      if (dates) {
        setPickerTime(dates);
      } else {
        // 清除时恢复默认时间
        setPickerTime(null);
        setTime(undefined);
      }
    };
  // 处理面板打开/关闭
  const handleOpenChange = (open: boolean) => {
    setIsPanelOpen(open);
    if (!open && pickerTime) {
      // 面板关闭时确认选择的时间
      setTime({
        start: pickerTime[0].toDate(),
        end: pickerTime[1].toDate()
      });
    }
  };
  
  const raiseLimit = () => {
    setLimit(limit + 25);
  };

  const noMoreAvailable = getHealthChecks.data !== undefined && getHealthChecks.data.values.length < limit;
// 获取表格数据
  const getTableData = () => {
    if (getCustomHealthChecks.data) return getCustomHealthChecks.data.values;
    if (getHealthChecks.data) return getHealthChecks.data.values;
    return [];
  };
  return (
    <>
        <Space>
          <DateTimeRangePicker
            value={pickerTime}
            onChange={handleRangeChange}
            placeholder={[t('common.start_time'), t('common.end_time')]}
            style={{ width: 380 }}
            onOpenChange={handleOpenChange}
            open={isPanelOpen}
            t={t}
          />
        </Space>
        <div style={{display: "flex",gap: 10,marginTop: 32,marginBottom:4}}>
        <DeleteHealthChecksModal serialNumber={serialNumber} />
          <Button icon={<Icon component={refreshSvg} />}
            onClick={getHealthChecks.refetch}
            loading={getHealthChecks.isFetching || getCustomHealthChecks.isFetching}
          >{t('common.refresh')}</Button>
      </div>
        <WirelessCustomTable
          columns={columns}
          dataSource={getTableData()}
          loading={getHealthChecks.isFetching || getCustomHealthChecks.isFetching}
          showColumnSelector={true}
          pagination={false}
          disableInternalRowSelection
          style={{ width: '100%' }} 
          scroll={{x: 'max-content', y: getTableData().length > 6 ? 300 : undefined }}
        />
        {getHealthChecks.data !== undefined && (
          <Center mt={2} hidden={getCustomHealthChecks.data !== undefined}>
            {!noMoreAvailable || getHealthChecks.isFetching ? (
              <Button 
                onClick={raiseLimit} 
                loading={getHealthChecks.isFetching}
                type="primary"
              >
                {t('controller.devices.show_more')}
              </Button>
            ) : (
              <span></span>
            )}
          </Center>
        )}
    </>
  );
};

export default HealthCheckHistory;
