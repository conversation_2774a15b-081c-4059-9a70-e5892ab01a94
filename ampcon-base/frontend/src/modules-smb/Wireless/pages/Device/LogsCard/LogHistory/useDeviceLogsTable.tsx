import * as React from 'react';
import { Box, IconButton, Text, useDisclosure } from '@chakra-ui/react';
import { MagnifyingGlass } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';
import DetailedLogViewModal from './DetailedLogViewModal';
import FormattedDate from '@/modules-smb/components/InformationDisplays/FormattedDate';
import { DeviceLog, useGetDeviceLogs, useGetDeviceLogsWithTimestamps } from '@/modules-smb/hooks/Network/DeviceLogs';
import { Column } from '@/modules-smb/models/Table';

type Props = {
  serialNumber: string;
  limit: number;
  logType: 0 | 1 | 2;
};

const useDeviceLogsTable = ({ serialNumber, limit, logType }: Props) => {
  const { t } = useTranslation();
  const getLogs = useGetDeviceLogs({ serialNumber, limit, logType });
  const modalProps = useDisclosure();
  const [log, setLog] = React.useState<DeviceLog | undefined>();
  const [time, setTime] = React.useState<{ start: Date; end: Date } | undefined>();
  const getCustomLogs = useGetDeviceLogsWithTimestamps({
    serialNumber,
    start: time ? Math.floor(time.start.getTime() / 1000) : undefined,
    end: time ? Math.floor(time.end.getTime() / 1000) : undefined,
    logType,
  });

  const onOpen = React.useCallback((v: DeviceLog) => {
    setLog(v);
    modalProps.onOpen();
  }, []);

  const logCell = React.useCallback(
    (v: DeviceLog) =>
      logType === 1 ? (
        <div style={{display:"flex"}}>
          <IconButton
            aria-label="Open Log Details"
            onClick={() => onOpen(v)}
            colorScheme="blue"
            icon={<MagnifyingGlass size={16} />}
            size="xs"
            mr={2}
          />
          <Text my="auto" maxW="calc(20vw)" textOverflow="ellipsis" overflow="hidden" whiteSpace="nowrap">
            {v.log}
          </Text>
        </div>
      ) : (
        v.log
      ),
    [onOpen],
  );

  const detailsCell = React.useCallback((v: DeviceLog) => {
    if (logType === 2) {
      return (
        <Box display="flex">
          <IconButton
            aria-label="Open Log Details"
            onClick={() => onOpen(v)}
            colorScheme="blue"
            icon={<MagnifyingGlass size={16} />}
            size="xs"
            mr={2}
          />
          <Text my="auto" textOverflow="ellipsis" overflow="hidden" whiteSpace="nowrap">
            {JSON.stringify(v.data, null, 0)}
          </Text>
        </Box>
      );
    }

    return <pre>{JSON.stringify(v.data, null, 0)}</pre>;
  }, []);

  const dateCell = React.useCallback(
    (v: number) => (
      <Box>
        <FormattedDate date={v} />
      </Box>
    ),
    [],
  );

  const columns: Column<DeviceLog>[] = React.useMemo(
    (): Column<DeviceLog>[] => [
      {
        key: 'submitted',
        title: t('common.submitted'),
        dataIndex: 'submitted',
        render: (_,record) =>  dateCell(record.recorded),
        fixed: 'left',
        sorter: false,
        // customWidth: '35px',
        // disableSortBy: true,
      },
      {
        key: 'UUID',
        title: t('controller.devices.config_id'),
        dataIndex: 'UUID',
        sorter: false,
        columnsFix:true,
        // customWidth: '35px',
        // alwaysShow: true,
        // disableSortBy: true,
      },
      {
        key: 'severity',
        title: t('controller.devices.severity'),
        dataIndex: 'severity',
        sorter: false,
        // customWidth: '35px',
        // disableSortBy: true,
      },
      {
        key: 'log',
        title: 'Log',
        dataIndex: 'log',
        // customWidth: '35px',
        render: (_,record) =>  logCell(record),
        sorter: false,
        // disableSortBy: true,
      },
      {
        key: 'data',
        title: t('common.details'),
        dataIndex: 'data',
        render: (_,record) =>  detailsCell(record),
        sorter: false,
        // disableSortBy: true,
      },
    ],
    [t],
  );

  return {
    columns:
      logType === 2
        ? columns
            .filter((c) => c.id !== 'severity')
            .map((col) =>
              col.id === 'log'
                ? {
                    ...col,
                    Header: 'Type',
                  }
                : col,
            )
        : columns,
    getLogs,
    getCustomLogs,
    time,
    setTime,
    modal: <DetailedLogViewModal modalProps={modalProps} log={log} />,
  };
};

export default useDeviceLogsTable;
