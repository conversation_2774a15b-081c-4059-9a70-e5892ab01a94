import * as React from 'react';
import { useColorMode } from '@chakra-ui/react';
import * as echarts from 'echarts';
import {
  formatDateTime,
  formatFullDateTime,
  getVisibleLabelIndices,
  getDivisionFactor,
  getDivisionFactorPackets,
} from './chartUtils';


type Props = {
  data: {
    tx: number[];
    rx: number[];
    packetsRx: number[];
    packetsTx: number[];
    recorded: number[];
    maxRx: number;
    maxTx: number;
    maxPacketsRx: number;
    maxPacketsTx: number;
    removed?: boolean;
  };
  format: 'bytes' | 'packets';
};

const InterfaceChart: React.FC<Props> = ({ data, format }) => {
  const { colorMode } = useColorMode();
  const isDarkMode = colorMode === 'dark';
  const chartRef = React.useRef<HTMLDivElement>(null);
  let chartInstance: echarts.ECharts | null = null;

  const RX_COLOR = '#14C9BB';
  const TX_COLOR = '#21CCFF';

  const { factor, unit } = getDivisionFactor(data.maxTx);
  const packetsFactor = getDivisionFactorPackets(
    data.maxPacketsTx > data.maxPacketsRx ? data.maxPacketsTx : data.maxPacketsRx,
  );
  // 计算可见的X轴标签索引
  const visibleIndices = getVisibleLabelIndices(data.recorded.length);
  // 处理数据系列
  const processDataSeries = () => {
    const rxAreaColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: 'rgba(20, 201, 187, 0.5)' }, 
      { offset: 1, color: 'rgba(20, 201, 187, 0)' }    
    ]);
    const txAreaColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: 'rgba(33, 204, 255, 0.5)' }, 
      { offset: 1, color: 'rgba(33, 204, 255, 0)' }    
    ]);

    if (format === 'bytes') {
      return [
        {
          name: 'RX',
          data: data.tx.map(rx => Math.floor((rx / factor) * 100) / 100),
          type: 'line',
          smooth: true,
          symbol: 'none',
          lineStyle: { color: RX_COLOR },
          areaStyle: { color: rxAreaColor },
          itemStyle: { color: RX_COLOR }
        },
        {
          name: 'TX',
          data: data.rx.map(tx => Math.floor((tx / factor) * 100) / 100),
          type: 'line',
          smooth: true,
          symbol: 'none',
          lineStyle: { color: TX_COLOR },
          areaStyle: { color: txAreaColor }, 
          itemStyle: { color: TX_COLOR }
        },
      ];
    } else {
      return [
        {
          name: 'RX',
          data: data.packetsTx,
          type: 'line',
          smooth: true,
          symbol: 'none',
          lineStyle: { color: RX_COLOR },
          areaStyle: { color: rxAreaColor },
          itemStyle: { color: RX_COLOR }
        },
        {
          name: 'TX',
          data: data.packetsRx,
          type: 'line',
          smooth: true,
          symbol: 'none',
          lineStyle: { color: TX_COLOR },
          areaStyle: { color: txAreaColor },
          itemStyle: { color: TX_COLOR }
        },
      ];
    }
  };

  // 生成ECharts配置项
  const getOption = (): echarts.EChartsOption => {
    const textColor = isDarkMode ? 'white' : '#333';
    const gridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

    return {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: isDarkMode ? '#333' : '#fff',
            color: textColor
          }
        },
        formatter: (params: any[]) => {
          const timestamp = data.recorded[params[0].dataIndex];
          let result = formatFullDateTime(timestamp) + '<br/>';
          params.forEach(param => {
            const color = param.seriesItemStyle?.color || param.color;
            const value = format === 'bytes'
              ? `${param.value.toFixed(2)} ${unit}`
              : `${packetsFactor.factor === 1
                ? param.value.toLocaleString()
                : (param.value / packetsFactor.factor).toFixed(1) + packetsFactor.unit
              }`;
            result += `<span style="display:inline-block;width:10px;height:10px;background-color:${color};margin-right:5px;"></span>${param.seriesName}: ${value}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: [
          {
            name: 'RX',
            itemStyle: { color: RX_COLOR }
          },
          {
            name: 'TX',
            itemStyle: { color: TX_COLOR }
          }
        ],
        bottom: 0,
        left: 'center',
        textStyle: { color: textColor },
        icon: 'roundRect',
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 15
      },
      grid: {
        left: '1%',
        right: '2%',
        top: '32px',
        bottom: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: data.recorded.map((timestamp, index) =>
          // 只对可见索引的标签进行格式化，其他留空
          visibleIndices.includes(index) ? formatDateTime(timestamp) : ''
        ),
        axisLine: { lineStyle: { color: gridColor } },
        axisTick: {
          show: true,
          alignWithLabel: true,
          interval: (index: number) => visibleIndices.includes(index)
        },
        axisLabel: {
          color: textColor,
          rotate: 0,
          padding: [0, 0, 0, 15],
          // 控制标签显示间隔
          interval: (index: number) => visibleIndices.includes(index)
        },
        splitLine: { lineStyle: { color: gridColor } }
      },
      yAxis: {
        type: 'value',
        min: 0,
        axisLine: { lineStyle: { color: gridColor } },
        axisLabel: {
          color: textColor,
          margin: 32,
          formatter: (value: number) => {
            if (format === 'bytes') {
              const temp = String(value);
              const formattedValue = temp.includes('.')
                ? Number(temp).toFixed(1)
                : temp;
              return `${formattedValue} ${unit}`;
            } else {
              return packetsFactor.factor === 1
                ? value.toLocaleString()
                : `${(value / packetsFactor.factor).toFixed(1)}${packetsFactor.unit}`;
            }
          }
        },
        splitLine: { lineStyle: { color: gridColor } }
      },
      series: processDataSeries()
    };
  };

  // 图表初始化与销毁
  React.useEffect(() => {
    if (chartRef.current) {
      if (chartInstance) {
        echarts.dispose(chartInstance);
      }
      chartInstance = echarts.init(chartRef.current);
      chartInstance.setOption(getOption());
    }

    const handleResize = () => {
      chartInstance?.resize();
    };
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance) {
        echarts.dispose(chartInstance);
        chartInstance = null;
      }
    };
  }, [data, format, isDarkMode]);

  // 数据更新时刷新图表
  React.useEffect(() => {
    if (chartInstance) {
      chartInstance.setOption(getOption());
    }
  }, [data, format, isDarkMode]);

  return (
    <div
      ref={chartRef}
      style={{ width: '100%', height: '100%', minHeight: '300px' }}
    />
  );
};

export default React.memo(InterfaceChart);
