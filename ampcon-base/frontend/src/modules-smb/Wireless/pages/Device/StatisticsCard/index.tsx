import * as React from 'react';
import { Select, Spin, Typography, Row, Space, Flex, Col, Button, Empty } from 'antd';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import CustomRangePicker from '@/modules-smb/Wireless/components/CustomWirelessRangePicker';
import InterfaceChart from './InterfaceChart';
import DeviceMemory<PERSON>hart from './MemoryChart';
import { useStatisticsCard } from './useStatisticsCard';
import ViewLastStatsModal from './ViewLastStatsModal';
import VlanChart from './VlanChart';
import LoadingOverlay from '@/modules-smb/components/LoadingOverlay';
import Icon from '@ant-design/icons';
const { Text } = Typography;
import { refreshSvg } from "@/utils/common/iconSvg";
import dayjs, { Dayjs } from 'dayjs';
import EmptyPic from "@/assets/images/App/empty.png";

const interfaceNameLabel = (v?: string) => {
  if (!v) return '';

  if (v.startsWith('up')) {
    const split = v.split('v');
    const vlanId = split[split.length - 1];
    return vlanId === '0' ? 'Upstream' : `Upstream - Vlan ${vlanId}`;
  }
  if (v.startsWith('down')) {
    const split = v.split('v');
    const vlanId = split[split.length - 1];
    return vlanId === '0' ? 'Downstream' : `Downstream - Vlan ${vlanId}`;
  }
  return v;
};

const EmptyState = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%'
  }}>
    <Empty image={EmptyPic} description="No Data" imageStyle={{ marginTop: 16, marginBottom: 0 }} />
  </div>
);

type Props = {
  serialNumber: string;
};

const DeviceStatistics = ({ serialNumber }: Props) => {
  const { t } = useTranslation();
  const { time, setTime, parsedData, isLoading, selected, onSelectInterface, refresh } = useStatisticsCard({
    serialNumber,
  });
  const [formatChosen, setFormatChosen] = React.useState<'bytes' | 'packets'>('bytes');
  
  const defaultTimeRange: [Dayjs, Dayjs] = [
    dayjs().subtract(1, 'hour'),
    dayjs()
  ];

  // 计算真实数据时间范围（用于显示）
  const getRealDataRange = React.useMemo(() => {
    const allTimestamps: number[] = [];
    if (!parsedData) return null;

    if (selected === 'memory') {
      const memoryTimestamps = parsedData.memory?.recorded || [];
      allTimestamps.push(...memoryTimestamps);
    } else if (selected.startsWith('VLAN-')) {
      const vlanName = selected.replace('VLAN-', '');
      const vlanTimestamps = parsedData.vlans?.[vlanName]?.recorded || [];
      allTimestamps.push(...vlanTimestamps);
    } else {
      const interfaceTimestamps = parsedData.interfaces?.[selected]?.recorded || [];
      allTimestamps.push(...interfaceTimestamps);
    }

    const validTimestamps = [...new Set(allTimestamps.filter(ts => ts > 0))];
    if (validTimestamps.length === 0) return null;

    const realStartTs = Math.min(...validTimestamps);
    const realEndTs = Math.max(...validTimestamps);
    const realStart = new Date(realStartTs * 1000);
    const realEnd = new Date(realEndTs * 1000);

    return { realStart, realEnd };
  }, [parsedData, selected]); 

  const handleTimeChange = (dates) => {
    const fromDate = dates ? dates[0] ? dates[0].toDate() : 0 : 0;
    const endDate = dates ? dates[1]? dates[1].toDate() : 0 : 0;
    const newTime = {
      start: fromDate,
      end: endDate,
    };
    setTime(newTime); 
  };

  const onFormatChange = (value: 'bytes' | 'packets') => {
    setFormatChosen(value);
  };

  const interfaces = React.useMemo(() => {
    if (!parsedData || !parsedData.interfaces || !parsedData.interfaces[selected]) return null;
    
    const interfaceData = parsedData.interfaces[selected];
    if (!interfaceData.recorded || interfaceData.recorded.length === 0) return null;
    
    return <InterfaceChart data={interfaceData} format={formatChosen} />;
  }, [parsedData, selected, formatChosen]);
  
  const vlans = React.useMemo(() => {
    if (!parsedData || !parsedData.vlans) return null;
    
    const vlanName = selected.startsWith('VLAN-') ? selected.replace('VLAN-', '') : null;
    if (!vlanName || !parsedData.vlans[vlanName]) return null;
    
    const vlanData = parsedData.vlans[vlanName];
    if (!vlanData.recorded || vlanData.recorded.length === 0) return null;
    
    return <VlanChart data={vlanData} format={formatChosen} />;
  }, [parsedData, selected, formatChosen]);

  const memory = React.useMemo(() => {
    if (!parsedData || !parsedData.memory) return null;
    if (!parsedData.memory.recorded || parsedData.memory.recorded.length === 0) return null;
    
    return <DeviceMemoryChart data={parsedData.memory} />;
  }, [parsedData]);

  const hasNoData = React.useMemo(() => {
    if (!parsedData) return true;
    if (selected === 'memory') {
      return !memory || !parsedData.memory || !parsedData.memory.recorded || parsedData.memory.recorded.length === 0;
    }
    
    if (selected.startsWith('VLAN-')) {
      const vlanName = selected.replace('VLAN-', '');
      return !vlans || !parsedData.vlans || !parsedData.vlans[vlanName] || !parsedData.vlans[vlanName].recorded || parsedData.vlans[vlanName].recorded.length === 0;
    }
    
    return !interfaces || !parsedData.interfaces || !parsedData.interfaces[selected] || !parsedData.interfaces[selected].recorded || parsedData.interfaces[selected].recorded.length === 0;
  }, [parsedData, selected, interfaces, vlans, memory]);

  const getTimeAsDayjs = React.useMemo(() => {
    if (time) {
      return [
        time.start ? dayjs(time.start) : undefined,
        time.end ? dayjs(time.end) : undefined
      ];
    }
    return defaultTimeRange;
  }, [time]); 
  return (
    <>
      <div style={{ padding: '8px 0' }}>
        <Row gutter={[16, 16]}>
    
          <Col span={24}>
            <Flex >
              <div style={{ display: 'flex', alignItems: 'center', fontSize: '14px', fontWeight: 400 }}>
                Time
              </div>
              <div style={{ marginLeft: '32px' }}>
                <CustomRangePicker
                  value={getTimeAsDayjs}
                  onChange={handleTimeChange}
                  tooltipText={t('controller.crud.choose_time')}
                />
              </div>
            </Flex>
            <Flex style={{ gap: '16px 8px', marginTop: '24px', alignItems: 'center'}}>
              {selected !== 'memory' && (
                <div  style={{ marginLeft: '62.679px' }}>
                  <Select
                    value={formatChosen}
                    onChange={onFormatChange}
                    style={{ width: 280 }}
                    options={[
                      { value: 'bytes', label: 'Data' },
                      { value: 'packets', label: 'Packets' }
                    ]}
                  />
                </div>
              )}
              
              <div >
                <Select
                  value={selected}
                  onChange={onSelectInterface}
                  style={{ width: 280 }}
                >
                  {parsedData?.interfaces && Object.keys(parsedData.interfaces).map((v) => (
                    <Select.Option value={v} key={uuid()}>
                      {interfaceNameLabel(v)}
                    </Select.Option>
                  ))}
                  {parsedData?.vlans && Object.keys(parsedData.vlans).map((v) => (
                    <Select.Option value={`VLAN-${v}`} key={uuid()}>
                      VLAN - {v}
                    </Select.Option>
                  ))}
                  <Select.Option value="memory">{t('statistics.memory')}</Select.Option>
                </Select>
              </div>
            </Flex>

            <Flex style={{ marginTop: '32px',alignItems: 'center'}}>
              <div>
                <Button
                  htmlType="button"
                  style={{ display: "flex", alignItems: "center" }}
                  onClick={refresh}
                  icon={<Icon component={refreshSvg} />}
                >
                  Refresh
                </Button>
              </div>
            </Flex>
          </Col>
        </Row>
      </div>

      {getRealDataRange && !isLoading.isLoading && (
        <Row style={{ padding: '16px 0px 0px 0px' }}>
          <Text>
            {t('controller.devices.from_to', {
              from: `${getRealDataRange.realStart.toLocaleDateString()} ${getRealDataRange.realStart.toLocaleTimeString()}`,
              to: `${getRealDataRange.realEnd.toLocaleDateString()} ${getRealDataRange.realEnd.toLocaleTimeString()}`,
            })}
          </Text>
        </Row>
      )}

      <div style={{ marginLeft: '-16px'}}>
        {(!parsedData && isLoading.isLoading) || (isLoading.isLoading && isLoading.progress !== undefined) ? (
          <Flex justify="center" align="center" style={{  padding: 0 }}>
            {isLoading.progress !== undefined && (
              <Typography.Title level={4} style={{ marginRight: 8, margin: 0 }}>
                {isLoading.progress.toFixed(2)}%
              </Typography.Title>
            )}
            <Spin size="large" />
          </Flex>
        ) : (
          <LoadingOverlay isLoading={isLoading.isLoading}>
            <div style={{  padding: 0 }}>
              {selected === 'memory' ? memory : interfaces || vlans}
              {hasNoData && !isLoading.isLoading && (
                <EmptyState />
              )}
            </div>
          </LoadingOverlay>
        )}
      </div>


    </>
  );
};

export default DeviceStatistics;
    