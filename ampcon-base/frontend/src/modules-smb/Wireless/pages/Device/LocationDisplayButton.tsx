import React, { useMemo, useState } from 'react';
import { Button, Tooltip, Modal, Form, Typography } from 'antd';
import { Globe } from '@phosphor-icons/react';
import { Wrapper } from '@googlemaps/react-wrapper';
import { useTranslation } from 'react-i18next';

import { GoogleMap } from '@/modules-smb/components/Maps/GoogleMap';
import { GoogleMapMarker } from '@/modules-smb/components/Maps/GoogleMap/Marker';
import { useGetSystemSecret } from '@/modules-smb/hooks/Network/Secrets';
import { useGetDeviceLastStats } from '@/modules-smb/hooks/Network/Statistics';

type Props = {
  serialNumber: string;
  isCompact?: boolean;
};

const LocationDisplayButton: React.FC<Props> = ({ serialNumber, isCompact }) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);

  const getGoogleApiKey = useGetSystemSecret({ secret: 'google.maps.apikey' });
  const getLastStats = useGetDeviceLastStats({ serialNumber });

  const location: google.maps.LatLngLiteral | undefined = useMemo(() => {
    if (!getLastStats.data?.gps) return undefined;

    try {
      return {
        lat: parseFloat(getLastStats.data.gps.latitude),
        lng: parseFloat(getLastStats.data.gps.longitude),
      };
    } catch {
      return undefined;
    }
  }, [getLastStats.data?.gps]);

  if (!location) {
    return null;
  }

  return (
    <>
      {isCompact ? (
        <Tooltip title={t('locations.view_gps')}>
          <Globe
            size={24}
            style={{ color: '#1677ff', cursor: 'pointer' }}
            onClick={() => setOpen(true)}
          />
        </Tooltip>
      ) : (
        <Button type="link" onClick={() => setOpen(true)} icon={<Globe size={18} />}>
          {t('locations.view_gps')}
        </Button>
      )}

      <Modal
        open={open}
        title={t('locations.one')}
        onCancel={() => setOpen(false)}
        footer={null}
        width={800}
      >
        <div style={{ display: 'flex', marginBottom: 16 }}>
          <Form layout="inline">
            <Form.Item label={t('locations.lat')}>
              <Typography.Text>{location.lat}</Typography.Text>
            </Form.Item>
            <Form.Item label={t('locations.longitude')} style={{ marginLeft: 16 }}>
              <Typography.Text>{location.lng}</Typography.Text>
            </Form.Item>
            <Form.Item label={t('locations.elevation')} style={{ marginLeft: 16 }}>
              <Typography.Text>{getLastStats.data?.gps?.elevation}</Typography.Text>
            </Form.Item>
          </Form>
        </div>

        {getGoogleApiKey.data ? (
          <div style={{ height: 500 }}>
            <Wrapper apiKey={getGoogleApiKey.data.value}>
              <GoogleMap center={location} style={{ flexGrow: 1, height: '100%' }} zoom={10}>
                <GoogleMapMarker position={location} />
              </GoogleMap>
            </Wrapper>
          </div>
        ) : null}
      </Modal>
    </>
  );
};

export default LocationDisplayButton;
