import * as React from 'react';
import { Heart, Warning } from '@phosphor-icons/react';
import { useTranslation } from 'react-i18next';
import SimpleIconStatDisplay from '@/modules-smb/Wireless/components/Containers/SimpleIconStatDisplay';
import { ControllerDashboardHealth } from '@/modules-smb/hooks/Network/Controller';
import iconOverallHealth from "@/modules-smb/Wireless/assets/Dashboard/icon_Overall_Health.png";

type Props = {
  data: ControllerDashboardHealth[];
};
const OverallHealthSimple = ({ data }: Props) => {
  const { t } = useTranslation();

  const parsedData = React.useMemo(() => {
    const totalDevices = data.reduce(
      (acc, curr) => {
        let newHealth = 0;
        if (curr.tag === '100%') newHealth = curr.value * 100;
        else if (curr.tag === '>90%') newHealth = curr.value * 95;
        else if (curr.tag === '>60%') newHealth = curr.value * 75;
        else if (curr.tag === '<60%') newHealth = curr.value * 30;

        return {
          totalDevices: acc.totalDevices + curr.value,
          totalHealth: acc.totalHealth + newHealth,
        };
      },
      {
        totalHealth: 0,
        totalDevices: 0,
      },
    );

    const avg = totalDevices.totalDevices === 0 ? -1 : Math.floor(totalDevices.totalHealth / totalDevices.totalDevices);
    let color: [string, string] = ['green.300', 'green.300'];
    let icon = Heart;
    const text = avg === -1 ? '-' : `${avg}%`;

    if (avg === -1) {
      icon = Heart;
      color = ['gray.300', 'gray.300'];
    } else if (avg >= 80 && avg < 100) {
      icon = Warning;
      color = ['yellow.300', 'yellow.300'];
    } else if (avg < 80) {
      icon = Warning;
      color = ['red.300', 'red.300'];
    }

    return { title: text, color, icon };
  }, [data]);

  return (
    <SimpleIconStatDisplay
      title={t('controller.dashboard.overall_health')}
      value={parsedData.title}
      description={t('controller.dashboard.overall_health_explanation')}
      icon={<img src={iconOverallHealth} style={{ width: 30, height: 30 }}/>}
      color={parsedData.color}
    />
  );
};

export default OverallHealthSimple;
