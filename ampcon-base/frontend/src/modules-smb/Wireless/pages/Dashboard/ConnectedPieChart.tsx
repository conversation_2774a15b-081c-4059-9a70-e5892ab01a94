import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import PieEcharts from '@/modules-smb/Wireless/components/Echarts/PieEcharts';
import GraphStatDisplay from '@/modules-smb/Wireless/components/Containers/GraphStatDisplay';
import { ControllerDashboardResponse } from '@/modules-smb/hooks/Network/Controller';
import { COLORS } from '@/modules-smb/Wireless/constants/colors';
type Props = {
  data: ControllerDashboardResponse;
};

const ConnectedPieChart: React.FC<Props> = ({ data }) => {
  const { t } = useTranslation();

  const seriesData = useMemo(() => {
    let connected = 0;
    let notConnected = 0;

    data.status.forEach((item) => {
      if (item.tag === 'connected') {
        connected = item.value;
      } else if (item.tag === 'not connected') {
        notConnected = item.value;
      }
    });

    return [
      { name: t('common.connected'), value: connected },
      { name: t('controller.dashboard.not_connected'), value: notConnected },
    ];
  }, [data, t]);

  return (
    <GraphStatDisplay
      title={t('controller.dashboard.status')}
      explanation={t('controller.dashboard.status_explanation')}
      chart={(isModal) => (
        <PieEcharts
          seriesData={seriesData}
          name={t('controller.dashboard.status')}
          chartType="ring"
          height={isModal ? "300px" : "31vh"}
          maxWidth={"100%"}
          showPercent
          colorList={[COLORS[8], COLORS[11]]}
          legendDirection="horizontal"
        />)
      }
    />
  );
};

export default ConnectedPieChart;
