import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import GraphStatDisplay from '@/modules-smb/Wireless/components/Containers/GraphStatDisplay';
import PieEcharts from '@/modules-smb/Wireless/components/Echarts/PieEcharts';
import type { ControllerDashboardAssociations } from '@/modules-smb/hooks/Network/Controller';
import { COLORS } from '@/modules-smb/Wireless/constants/colors';
type Props = {
  data: ControllerDashboardAssociations[];
};
const AssociationsPieChart = ({ data }: Props) => {
  const { t } = useTranslation();
  const parsedData = useMemo(() => {
    const obj = {
      '2G': 0,
      '5G': 0,
      '6G': 0,
    };

    data.forEach(({ tag, value }) => {
      if (tag === '2G' || tag === '5G' || tag === '6G') {
    obj[tag] += value; 
  }
    });

    return [
      { name: '2G', value: obj['2G'] },
      { name: '5G', value: obj['5G'] },
      { name: '6G', value: obj['6G'] },
    ];
  }, [data]);  
  return (
    <GraphStatDisplay
      title={t('controller.dashboard.associations')}
      explanation={t('controller.dashboard.associations_explanation')}
      chart={(isModal) => (
        <PieEcharts
        seriesData={parsedData}
        name={t("controller.dashboard.associations")}
        chartType="ring"
        height={isModal ? "300px" : "31vh"}
        maxWidth={"100%"}
        showPercent={true}
        colorList={[COLORS[0], COLORS[11], COLORS[7]]}
        legendDirection="horizontal"
      />)
      }
    />
  );
};

export default AssociationsPieChart;
