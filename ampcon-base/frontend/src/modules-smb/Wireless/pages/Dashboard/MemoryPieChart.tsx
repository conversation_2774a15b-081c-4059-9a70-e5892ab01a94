import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import GraphStatDisplay from '@/modules-smb/Wireless/components/Containers/GraphStatDisplay';
import PieEcharts from '@/modules-smb/Wireless/components/Echarts/PieEcharts';
import { ControllerDashboardMemoryUsed } from '@/modules-smb/hooks/Network/Controller';
import { COLORS } from '@/modules-smb/Wireless/constants/colors';
type Props = {
  data: ControllerDashboardMemoryUsed[];
};

const MemoryPieChart = ({ data }: Props) => {
  const { t } = useTranslation();

  const seriesData = useMemo(() => {
    return data.map(({ tag, value }) => ({
      name: tag,
      value,
    }));
  }, [data]);

  return (
    <GraphStatDisplay
      title={t('controller.dashboard.memory')}
      explanation={t('controller.dashboard.memory_explanation')}
      chart={(isModal) => (
        <PieEcharts
          seriesData={seriesData}
          name={t('controller.dashboard.memory')}
          chartType="ring"
          height={isModal ? "300px" : "31vh"}
          maxWidth={"100%"}
          showPercent={true}
          colorList={[COLORS[8], COLORS[11]]}
          legendDirection="horizontal"
        />)
      }
    />
  );
};

export default MemoryPieChart;
