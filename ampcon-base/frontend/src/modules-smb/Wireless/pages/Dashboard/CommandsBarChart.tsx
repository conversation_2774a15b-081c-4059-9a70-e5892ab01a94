import * as React from "react";
import {useMemo}from "react";
import { useTranslation } from "react-i18next";
import GraphStatDisplay from "@/modules-smb/Wireless/components/Containers/GraphStatDisplay";
import { COLORS } from "@/modules-smb/Wireless/constants/colors";
import { uppercaseFirstLetter } from "@/modules-smb/helpers/stringHelper";
import { ControllerDashboardCommands } from "@/modules-smb/hooks/Network/Controller";
import BarEcharts  from "@/modules-smb/Wireless/components/Echarts/BarEcharts";
import PaginatedBarChart from "@/modules-smb/Wireless/components/Echarts/PaginatedBarChart";

type Props = {
  data: ControllerDashboardCommands[];
};

const CommandsBarChart = ({ data }: Props) => {
  const { t } = useTranslation();
   const filteredData = (data || []).filter(item => item.tag !== "wifiscan");

  const { xAxis, yAxisData } = React.useMemo(() => {
    const x: string[] = [];
    const y: { value: number; itemStyle: { color: string } }[] = [];

    const sortedData = filteredData.sort((a, b) => a.tag.localeCompare(b.tag));

    for (let i = 0; i < sortedData.length; i++) {
      const { tag, value } = sortedData[i];
      const label = tag === "rtty" ? "RTTY" : uppercaseFirstLetter(tag);
      x.push(label);
      y.push({
        value,
        itemStyle: {
          color: COLORS[i % COLORS.length], // 循环使用颜色
        },
      });
    }

    return { xAxis: x, yAxisData: y };
  }, [filteredData]);

  // 处理数据并排序
  const processedData = useMemo(() => {
    if (!filteredData || !Array.isArray(filteredData)) return [];
    
    return filteredData.map((item, index) => ({
      tag: item.tag,
      value: item.value,
      color: COLORS[index % COLORS.length], // 给每个柱子固定颜色
    })).sort((a, b) => (a.tag || "").localeCompare(b.tag || ""));
  }, [filteredData]);

  return (
    <GraphStatDisplay
      title={t("controller.dashboard.commands")}
      explanation={t("controller.dashboard.commands_explanation")}
      chart={(isModal)=>(
        <PaginatedBarChart data={processedData} width="40%" height={isModal ? "300px" : "31vh"} />
      )
      }
    />
  );
};

export default CommandsBarChart;
