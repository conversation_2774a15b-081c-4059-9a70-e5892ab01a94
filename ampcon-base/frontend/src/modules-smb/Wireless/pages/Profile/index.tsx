import React, { useEffect, useState } from "react";
import { Tabs, <PERSON>readcrumb, Select, Flex } from "antd";
import { useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import ProtectedRoute from "@/modules-ampcon/utils/util";
import VenueSelect from "@/modules-smb/Wireless/components/VenueSelect";
// import Network from "./Network";
import SSIDRadius from "./SSIDRadius";
import MPSKUser from "./MPSKUser";
import CaptivePortal from "./CaptivePortal";
import TimeRange from "./TimeRange";
import '@/modules-smb/Wireless/assets/tabs.scss';
import { useSiteStore } from "@/modules-smb/Wireless/components/SiteContext";
import { useSiteSelection } from "@/modules-smb/Wireless/hooks/useSelectedSite";
import { useUrlSync } from "@/modules-smb/Wireless/hooks/useUrlSync";

const ProfilePage = () => {
    const currentUser = useSelector((state: any) => state.user.userInfo);
    const location = useLocation();
    const navigate = useNavigate();
    const { handleSiteChange } = useSiteSelection(false);

    const { selectedSiteId } = useSiteStore();
    const [currentActiveKey, setCurrentActiveKey] = useState("");
    const handleChange = (value: string | string[]) => {
        const id = Array.isArray(value) ? value[0] : value;
        const pathWithoutTab = location.pathname;

        navigate(`${pathWithoutTab}#${id}`);
    };

    useUrlSync();


    const allItems = [
        // {
        //     key: "Network",
        //     label: "Network",
        //     children: <ProtectedRoute component={Network} />
        // },
        {
            key: "SSIDRadius",
            label: "SSID Radius",
            children: <ProtectedRoute component={SSIDRadius} />
        },
        {
            key: "MPSKUser",
            label: "MPSK",
            children: <ProtectedRoute component={MPSKUser} />
        },
        {
            key: "CaptivePortal",
            label: "Portal",
            children: <ProtectedRoute component={CaptivePortal} />
        },
        {
            key: "TimeRange",
            label: "Time Range",
            children: <ProtectedRoute component={TimeRange} />
        }
    ];

    const items: any[] =
        currentUser.type === "readonly"
            ? [] // 只读用户不显示任何 tab
            : allItems;

    useEffect(() => {

        // 处理tab激活状态
        const match = location.pathname.match(/(SSIDRadius|MPSKUser|CaptivePortal|TimeRange)$/);
        if (match) {
            setCurrentActiveKey(match[0]);
        } else if (items.length > 0) {
            setCurrentActiveKey(items[0].key);
            let pathWithoutTab = `${location.pathname.replace(/\/$/, "")}/${items[0].key}`;
            if (selectedSiteId) {
                pathWithoutTab += `#${selectedSiteId}`;
            }
            navigate(pathWithoutTab);
        }
    }, [location.pathname, items]);

    // 点击 tab 更新 URL
    const onChange = (key: string) => {
        let pathWithoutTab = location.pathname.replace(/(SSIDRadius|MPSKUser|CaptivePortal|TimeRange)$/, "");
        pathWithoutTab = `${pathWithoutTab.replace(/\/$/, "")}/${key}`;
        if (selectedSiteId) {
            pathWithoutTab += `#${selectedSiteId}`;
        }
        navigate(pathWithoutTab);
    };

    return (
        <Flex className="div-main">
            <div className="div-header">
                <VenueSelect onChange={handleSiteChange} />
            </div>
            <div className="div-tabs">
                <Tabs
                    activeKey={currentActiveKey}
                    onChange={onChange}
                    destroyInactiveTabPane
                    items={items}
                />
            </div>
        </Flex>
    );
};
export default ProfilePage;
