import { Card, Button, Form, Modal, message, Space, Table, Divider } from "antd";
import React, { useState, useEffect } from "react";
import { addSvg } from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";
import FullPageMPSKForm from '@/modules-smb/Wireless/pages/Profile/MPSKUser/MPSKUserForm';
import { getWirelessProfileList, deleteWirelessProfile } from "@/modules-smb/Wireless/apis/wireless_profile_api";
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";
import { utcToLocalString } from "../../../utils/util";

interface MPSKProfile {
    id: number;
    name: string;
    description?: string;
    modified_time?: string;
    parameter: any;
}

const MPSKUser: React.FC = () => {
    const [data, setData] = useState<MPSKProfile[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
    const [sorter, setSorter] = useState<{ field?: string; order?: string }>({});
    const [editingProfile, setEditingProfile] = useState<MPSKProfile | null>(null);
    const [isModalVisible, setIsModalVisible] = useState(false);

    let hashSiteId: number | null = null;
    const hash = window.location.hash.replace('#', '');
    if (hash && /^\d+$/.test(hash)) {
        hashSiteId = parseInt(hash, 10);
    }
    const finalSiteId = hashSiteId || 0;


    const fetchList = async (
        page = pagination.current,
        pageSize = pagination.pageSize,
        sorterParam = sorter
    ) => {
        setIsLoading(true);
        try {
            const sortFields = sorterParam.field
                ? [{ field: sorterParam.field, order: sorterParam.order }]
                : [];

            const res = await getWirelessProfileList(2, finalSiteId, page, pageSize, [], sortFields);
            if (res.status === 200) {
                setData(
                    (res.info || []).map((item: any) => ({
                        ...item,
                        parameter:
                            typeof item.parameter === "string"
                                ? JSON.parse(item.parameter)
                                : item.parameter || {},
                        modified_time: item.modified_time ? utcToLocalString(item.modified_time) : "",
                    }))
                );
                setPagination((prev) => ({
                    ...prev,
                    current: page,
                    pageSize,
                    total: res?.total || 0,
                }));
            } else {
                message.error(res?.info || "Failed to fetch profile list");
            }
        } catch (error) {
            message.error("Failed to fetch profile list");
        } finally {
            setIsLoading(false);
        }
    };

    const handleTableChange = (paginationChange: any, _filters: any, sorterChange: any) => {
        setPagination({
            ...pagination,
            current: paginationChange.current,
            pageSize: paginationChange.pageSize,
        });

        let order = "";
        let field = "";
        if (!Array.isArray(sorterChange) && sorterChange?.order) {
            order = sorterChange.order === "ascend" ? "asc" : "desc";
            field = sorterChange.field || "";
        }
        setSorter({ field, order });
        fetchList(paginationChange.current, paginationChange.pageSize, { field, order });
    };

    useEffect(() => {
        if (finalSiteId !== null && finalSiteId !== undefined) {
            fetchList(pagination.current, pagination.pageSize, sorter);
        }
    }, [finalSiteId, pagination.current, pagination.pageSize, sorter]);


    const handleCreate = () => {
        setEditingProfile(null);
        setIsModalVisible(true);
    };

    const handleEdit = (record: MPSKProfile) => {
        setEditingProfile(record);
        setIsModalVisible(true);
    };

    const handleDelete = (record: MPSKProfile) => {
        confirmModalAction(
            'Are you sure you want to delete?',
            async () => {
                try {
                    const res = await deleteWirelessProfile({ id: record.id });
                    if (res?.status !== 200) {
                        message.error(res?.info || 'Delete failed');
                        return;
                    }
                    message.success('Successfully Deleted');
                    const newTotal = pagination.total - 1;
                    const totalPages = Math.ceil(newTotal / pagination.pageSize);
                    const newPage = totalPages === 0 ? 1 : Math.min(pagination.current, totalPages);
                    setPagination((prev) => ({
                        ...prev,
                        current: newPage,
                        total: newTotal,
                    }));
                    fetchList(newPage, pagination.pageSize);
                } catch {
                    message.error('Delete failed');
                }
            }
        );
    };

    const handleModalClose = (success?: boolean) => {
        setIsModalVisible(false);
        if (success) {
            setPagination((prev) => {
                const newTotal = prev.total + 1;
                const totalPages = Math.ceil(newTotal / prev.pageSize);

                const isCurrentPageFull = prev.total !== 0 && (prev.total % prev.pageSize === 0);

                const newPage = isCurrentPageFull ? prev.current + 1 : prev.current;

                fetchList(newPage, prev.pageSize, sorter);
                return { ...prev, current: newPage, total: newTotal };
            });
        }
    };

    const columns = [
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name',
            sorter: true,
            width: '25%',
        },
        {
            title: 'Modified',
            dataIndex: 'modified_time',
            key: 'modified',
            sorter: true,
            width: '25%',
        },
        {
            title: 'Description',
            dataIndex: 'description',
            key: 'description',
            sorter: true,
            width: '30%',
        },
        {
            title: 'Operation',
            key: 'operate',
            width: '20%',
            render: (_: any, record: MPSKProfile) => (
                <Space size={16}>
                    <Button type="link" onClick={() => handleEdit(record)} style={{ marginLeft: -10, marginRight: -20 }}>Edit</Button>
                    <Button type="link" onClick={() => handleDelete(record)}>Delete</Button>
                </Space>
            ),
        },
    ];


    return (
        <div style={{ flex: 1 }}>
            <div style={{ marginBottom: 16 }}>
                <Button type="primary" onClick={handleCreate}>
                    <Icon component={addSvg} />
                    Create New MPSK Profile
                </Button>
            </div>
            <Table
                columns={columns}
                dataSource={data}
                rowKey="id"
                loading={isLoading}
                onChange={handleTableChange}
                pagination={{
                    current: pagination.current,
                    pageSize: pagination.pageSize,
                    total: pagination.total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    pageSizeOptions: ["10", "20", "50", "100"],
                    showTotal: total => `Total ${total} items`
                }}
                scroll={{ x: 1000 }}
                bordered
            />
            <Modal
                title={editingProfile ? "Edit MPSK Profile" : "Create MPSK Profile"}
                visible={isModalVisible}
                onCancel={() => handleModalClose(false)}
                footer={null}
                width={1360}
                destroyOnClose
            >
                <div
                    onKeyDown={(e) => {
                        if (e.key === "Enter") {
                            e.preventDefault();
                        }
                    }}
                >
                    <Divider style={{ margin: '0px 0px 16px -24px', width: 'calc(100% + 48px)' }} />

                    <FullPageMPSKForm
                        editingProfile={editingProfile}
                        onClose={handleModalClose}
                        siteId={finalSiteId}
                    />
                </div>

            </Modal>
        </div>
    );
};

export default MPSKUser;
