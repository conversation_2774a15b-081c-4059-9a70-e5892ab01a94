.webroot-form {
  display: flex;
  flex-direction: column;

  .ant-form-item-label > label {
    width: 152px;
  }

  .ant-form-item-control {
    .ant-input-affix-wrapper,
    .ant-input,
    .ant-select,
    .ant-input-number,
    .ant-picker,
    .ant-select-selector {
      width: 280px;
    }
  }
  // textarea {
  //   resize: none;
  // }
  .ant-form-item-control-input {
    width: 280px;
  }
  .upload-button {
    width: 100px;
  }
}
.previewBox {
  border-left: 1px solid #d9d9d9;
  padding-left: 16px;
  min-height: 550px;
  height: 100%;
  box-sizing: border-box;
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;;
}
.previewContent {
  width: 100%;
  height: 100%;
  border: none;
  background: #fff;
}
.webroot-row {
  .ant-form-item-label > label {
    width: 180px;
  }
}