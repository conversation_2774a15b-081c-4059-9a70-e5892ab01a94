import React, { useState } from 'react';
import { addSvg } from "@/utils/common/iconSvg";
import { validateUrlOrIp } from '@/modules-smb/Wireless/utils/util';
import Icon from "@ant-design/icons";
import { Form, Input, Select, Button, Switch, Row, Col, message, InputNumber, Table } from 'antd';
import { FormModal } from '@/modules-smb/Wireless/components/Modals/FormModal';
import UsersForm from './UsersForm';
import { useTranslation } from 'react-i18next';
import { createWirelessProfile, updateWirelessProfile } from '@/modules-smb/Wireless/apis/wireless_profile_api';
import { filterNullValues } from '@/modules-smb/Wireless/utils/util';

interface Props {
  isDisabled?: boolean;
  resource?: any;
  onClose: (success?: boolean) => void;
  refresh?: () => void;
  siteId?: number;
  open?: boolean;
  disableMode?: boolean;
}

const defaultValues = {
  name: '',
  description: '',
  mode: 'External',
  authentication: {
    host: '***************',
    port: 1812,
    secret: 'YOUR_SECRET',
    'mac-filter': false,
  },
  accounting: undefined,
  'dynamic-authorization': undefined,
  'nas-identifier': '',
  'chargeable-user-id': false,
  local: {
    // 'server-identity': 'uCentral',
    users: [],
  },
};

const getInitialValues = (resource: any) => {
  if (!resource) return { ...defaultValues };

  let parsedVars = {};
  let modeVal = 'External';

  try {
    if (resource.config_variables) {
      // const arr = JSON.parse(resource.config_variables);
      // if (Array.isArray(arr) && arr[0]?.value) {
      //   parsedVars = typeof arr[0].value === 'string'
      //     ? JSON.parse(arr[0].value)
      //     : arr[0].value;
      // }
      parsedVars = JSON.parse(resource.config_variables);
    }
    if (resource.parameter) {
      let paramObj = resource.parameter;
      if (typeof paramObj === 'string') {
        try { paramObj = JSON.parse(paramObj); } catch {}
      }
      if (paramObj && paramObj.type) {
        modeVal = paramObj.type;
      }
    }
  } catch {}

  return {
    ...parsedVars,
    name: resource.name,
    description: resource.description,
    mode: modeVal,
  };
};

const RadiusForm: React.FC<Props> = ({ isDisabled = false, resource, onClose, refresh, siteId, open = false, disableMode = false }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const initialVals = getInitialValues(resource);
  const [mode, setMode] = useState(initialVals.mode);
  const [submitting, setSubmitting] = useState(false);
  const [accountingEnabled, setAccountingEnabled] = useState(!!(initialVals && (initialVals as any).accounting));
  const [dynamicEnabled, setDynamicEnabled] = useState(!!(initialVals && (initialVals as any)['dynamic-authorization']));

  const handleFinish = async (values: any) => {
    setSubmitting(true);
    const filteredValues = filterNullValues(values);
    try {
      // 生成 parameter
      const parameter = {
        type: values.mode || 'External',
        auth_server_host: values.authentication?.host || '-',
        port: values.authentication?.port || '-',
      };
      // 生成 config_variables
      const { name, mode, description, ...pureValue } = filteredValues;
      // const config_variables = JSON.stringify([
      //   {
      //     type: 'json',
      //     weight: 0,
      //     prefix: 'interface.ssid.radius',
      //     value: JSON.stringify(pureValue),
      //   },
      // ]);
      const config_variables = JSON.stringify(pureValue);
      let res;
      if (resource && resource.id) {
        res = await updateWirelessProfile({
          id: resource.id,
          site_id: siteId,
          type: 1,
          name: values.name,
          parameter,
          description: values.description,
          config_variables,
        });
        if (res?.status !== 200) {
          message.error(res?.info || t('crud.error_update_obj', { obj: t('resources.configuration_resource') }));
          setSubmitting(false);
          return;
        }
        message.success(t('crud.success_update_obj', { obj: t('resources.configuration_resource') }));
      } else {
        res = await createWirelessProfile({
          site_id: siteId,
          type: 1,
          name: values.name,
          parameter,
          description: values.description,
          config_variables,
        });
        if (res?.status !== 200) {
          message.error(res?.info || t('crud.error_create_obj', { obj: t('resources.configuration_resource') }));
          setSubmitting(false);
          return;
        }
        message.success(t('crud.success_create_obj', { obj: t('resources.configuration_resource') }));
      }
      refresh && refresh();
      onClose && onClose(true);
    } catch (e) {
      message.error(t('crud.error_create_obj', { obj: t('resources.configuration_resource') }));
    } finally {
      setSubmitting(false);
    }
  };

  // 动态表单项条件
  const isLocal = mode === 'Local';
  const isFieldDisabled = isLocal || isDisabled;

  // 动态表单项: Local
  const [userPage, setUserPage] = useState(1);
  const [userPageSize, setUserPageSize] = useState(10);
  const users = Form.useWatch(['local', 'users'], form) || [];
  const [sortedInfo, setSortedInfo] = useState<any>({});
  const [userModalOpen, setUserModalOpen] = useState(false);
  const handleAddUser = () => setUserModalOpen(true);
  const handleSaveUser = (values: any) => {
    const user = { ...values };
    // 将 MAC 地址转换为小写
    if (user.mac) {
      user.mac = user.mac.toLowerCase();
    }
    form.setFieldValue(['local', 'users'], [user, ...users]);
    setUserModalOpen(false);
    setUserPage(1);
  };
  const handleDeleteUser = (userToRemove: any) => {
    // find first index that matches on MAC, User Name, Password and VLAN ID
    const idx = users.findIndex((u: any) => {
      const macMatch = String(u.mac || '') === String(userToRemove.mac || '');
      const nameMatch = String(u['user-name'] || '') === String(userToRemove['user-name'] || '');
      const passMatch = String(u.password || '') === String(userToRemove.password || '');
      const vlanMatch = String(u['vlan-id'] ?? '') === String(userToRemove['vlan-id'] ?? '');
      return macMatch && nameMatch && passMatch && vlanMatch;
    });
    if (idx === -1) return;
    const newUsers = [...users];
    newUsers.splice(idx, 1);
    form.setFieldValue(['local', 'users'], newUsers);
    if ((userPage - 1) * userPageSize >= newUsers.length && userPage > 1) {
      setUserPage(userPage - 1);
    }
  };
  const userColumns = [
    { title: 'MAC', dataIndex: 'mac', key: 'mac', sorter: (a: any, b: any) => String(a.mac || '').localeCompare(String(b.mac || '')) },
    { title: 'User Name', dataIndex: 'user-name', key: 'user-name', sorter: (a: any, b: any) => String(a['user-name'] || '').localeCompare(String(b['user-name'] || '')) },
    { title: 'Password', dataIndex: 'password', key: 'password', sorter: (a: any, b: any) => String(a.password || '').localeCompare(String(b.password || '')) },
    { title: 'VLAN ID', dataIndex: 'vlan-id', key: 'vlan-id', sorter: (a: any, b: any) => {
      const va = a['vlan-id'] === undefined || a['vlan-id'] === null || a['vlan-id']==='' ? Number.NEGATIVE_INFINITY : Number(a['vlan-id']);
      const vb = b['vlan-id'] === undefined || b['vlan-id'] === null || b['vlan-id']==='' ? Number.NEGATIVE_INFINITY : Number(b['vlan-id']);
      return va - vb;
    } },
    {
      title: 'Operation',
      key: 'action',
      render: (_: any, record: any) => (
        <Button type="text" onClick={() => handleDeleteUser(record)}>
          Delete
        </Button>
      ),
    },
  ];

  // 动态表单项：LocalUsers
  const renderLocalUsers = () => (
    <>
      <Row gutter={24}>
        <Col span={8} style={{ display: 'none' }}>
          <Form.Item
            name={['local', 'server-identity']}
            label="Server Identity"
            initialValue="uCentral"
          >
            <Input disabled={isDisabled} />
          </Form.Item>
        </Col>
        <Col span={24} style={{ display: 'none' }}>
          <Form.List name={['local', 'users']}>
            {(fields) => (
              <>
                {fields.map(() => null)}
              </>
            )}
          </Form.List>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={24}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
              <Button type="primary" icon={<Icon component={addSvg} />} onClick={handleAddUser} disabled={isDisabled}>
                User
              </Button>
            </div>
            <Table
              columns={userColumns}
              dataSource={users}
              pagination={{
                current: userPage,
                pageSize: userPageSize,
                total: users.length,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
                showSizeChanger: true,
                showQuickJumper: true,
                pageSizeOptions: ['5', '10', '20', '50'],
              }}
              style={{ marginBottom: 24 }}
              onChange={(pagination, _filters, sorter: any) => {
                setUserPage(pagination.current || 1);
                setUserPageSize(pagination.pageSize || 10);
                setSortedInfo(sorter);
              }}
              rowKey={(_record, idx) => (typeof idx === 'number' ? idx.toString() : '')}
              size="middle"
              bordered
            />
            <UsersForm
              open={userModalOpen}
              onCancel={() => setUserModalOpen(false)}
              onOk={handleSaveUser}
              isDisabled={isDisabled}
              existingUsers={users}
            />
          </Col>
      </Row>
    </>
  );

  // 动态表单项：External
  const renderExternalFields = () => (
    <>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name={['authentication', 'host']}
            label="Authentication Host"
            rules={[
              { required: true, message: t('form.required') },
              { validator: (_, value) => validateUrlOrIp(value) }
            ]}
          >
            <Input disabled={isFieldDisabled} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name={['authentication', 'port']}
            label="Authentication Port"
            rules={[
              { required: true, message: t('form.required') },
              { type: 'number', min: 1, message: 'radius.authentication.port must be a positive number' },
              { type: 'number', max: 65534, message: 'radius.authentication.port must be less than 65535' },
            ]}
          >
            <InputNumber disabled={isFieldDisabled} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name={['authentication', 'secret']}
            label="Authentication Secret"
            rules={[{ required: true, message: t('form.required') }]}
          >
            <Input.Password disabled={isFieldDisabled} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name={['authentication', 'mac-filter']}
            label="MAC Filter"
            valuePropName="checked"
          >
            <Switch disabled={isFieldDisabled} />
          </Form.Item>
        </Col>
      </Row>
      <h3 className='header2' style={{ marginBottom: 0 }}></h3>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            label="Enable Accounting"
          >
            <Switch
              checked={accountingEnabled}
              disabled={isFieldDisabled}
              onChange={checked => {
                setAccountingEnabled(checked);
                if (checked) {
                  form.setFieldsValue({
                    accounting: { host: '***************', port: 1813, secret: 'YOUR_SECRET' },
                  });
                } else {
                  form.setFieldsValue({ accounting: undefined });
                }
              }}
            />
          </Form.Item>
        </Col>
      </Row>
      {accountingEnabled && (
        <>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name={['accounting', 'host']}
                label="Accounting Host"
                rules={[
                  { required: true, message: t('form.required') },
                  { validator: (_, value) => validateUrlOrIp(value) }
                ]}
              >
                <Input disabled={isFieldDisabled} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['accounting', 'port']}
                label="Accounting Port"
                rules={[
                  { required: true, message: t('form.required') },
                  { type: 'number', min: 1, message: 'accounting.port must be a positive number' },
                  { type: 'number', max: 65534, message: 'accounting.port must be less than 65535' },
                ]}
              >
                <InputNumber disabled={isFieldDisabled} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name={['accounting', 'secret']}
                label="Accounting Secret"
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Input.Password disabled={isFieldDisabled} />
              </Form.Item>
            </Col>
          </Row>
        </>
        )}
      {/* Enable Dynamic Authorization */}
      <h3 className='header2' style={{ marginBottom: 0 }}></h3>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item label="Enable Dynamic Auth">
            <Switch
              checked={dynamicEnabled}
              disabled={isFieldDisabled}
              onChange={checked => {
                setDynamicEnabled(checked);
                if (checked) {
                  form.setFieldsValue({
                    'dynamic-authorization': { host: '***************', port: 1814, secret: 'YOUR_SECRET' },
                  });
                } else {
                  form.setFieldsValue({ 'dynamic-authorization': undefined });
                }
              }}
            />
          </Form.Item>
        </Col>
      </Row>
      {dynamicEnabled && (
        <>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name={['dynamic-authorization', 'host']}
                label="Dynamic Auth Host"
                rules={[
                  { required: true, message: t('form.required') },
                  { validator: (_, value) => validateUrlOrIp(value) }
                ]}
              >
                <Input disabled={isFieldDisabled} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['dynamic-authorization', 'port']}
                label="Dynamic Auth Port"
                rules={[
                  { required: true, message: t('form.required') },
                  { type: 'number', min: 1, message: 'dynamic-authorization.port must be a positive number' },
                  { type: 'number', max: 65534, message: 'dynamic-authorization.port must be less than 65535' },
                ]}
              >
                <InputNumber disabled={isFieldDisabled} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name={['dynamic-authorization', 'secret']}
                label="Dynamic Auth Secret"
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Input.Password disabled={isFieldDisabled} />
              </Form.Item>
            </Col>
          </Row>
        </>
      )}
      <h3 className='header2' style={{ marginBottom: 0 }}></h3>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="nas-identifier"
            label="NAS Identifier"
          >
            <Input disabled={isFieldDisabled} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="chargeable-user-id"
            label="Chargeable User ID"
            valuePropName="checked"
          >
            <Switch disabled={isFieldDisabled} />
          </Form.Item>
        </Col>
      </Row>
    </>
  );

  return (
    <FormModal
      open={open}
      title={resource ? 'Edit SSID Radius Profile' : 'Create SSID Radius Profile'}
      onCancel={() => onClose(false)}
      onFinish={handleFinish}
      initialValues={initialVals}
      form={form}
      onValuesChange={(_: any, allValues: { mode: React.SetStateAction<string>; }) => { if (allValues.mode !== mode) setMode(allValues.mode);}}
    >
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="name"
            label={t('common.name')}
            rules={[
              { required: true, message: t('form.required') },
              { type: 'string', max: 32, message: t('form.max_length', { max: 32 }) },
            ]}
          >
            <Input disabled={isDisabled} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="mode"
            label="Mode"
            rules={[{ required: true, message: t('form.required') }]}
          >
            <Select
              disabled={isDisabled || disableMode}
              options={[
                { label: 'External', value: 'External' },
                { label: 'Local', value: 'Local' },
              ]}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="description"
            label={t('common.description')}
            rules={[{ max: 128, message: t('form.max_length', { max: 128 }) }]}
          >
            <Input.TextArea disabled={isDisabled} rows={2} />
          </Form.Item>
        </Col>
      </Row>
      {isLocal ? renderLocalUsers() : renderExternalFields()}
    </FormModal>
  );
};

export default RadiusForm;
