import React from 'react';
import { Box, ListItem, UnorderedList } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import { ProvisioningVenueNotificationMessage } from '@/modules-smb/contexts/ProvisioningSocketProvider/utils';

interface Props {
  notification: ProvisioningVenueNotificationMessage['notification'];
}

const ConfigurationPushesNotificationContent = ({ notification }: Props) => {
  const { t } = useTranslation();

  return (
    <>
      <div>
        {t('configurations.successful_pushes', { count: notification?.content?.success?.length ?? 0 })}
      </div>
      {notification?.content?.success && (
        <Box maxH="200px" overflowY="auto">
          <UnorderedList>
            {notification?.content?.success.map((serialNumber: string) => (
              <ListItem key={uuid()}>{serialNumber}</ListItem>
            ))}
          </UnorderedList>
        </Box>
      )}
      <div>
        {t('configurations.warning_pushes', { count: notification?.content?.warning?.length ?? 0 })}
      </div>
      {notification?.content?.warning && (
        <Box maxH="200px" overflowY="auto">
          <UnorderedList maxH="200px" overflowY="auto">
            {notification?.content?.warning.map((serialNumber: string) => (
              <ListItem key={uuid()}>{serialNumber}</ListItem>
            ))}
          </UnorderedList>
        </Box>
      )}
      <div>
        {t('configurations.error_pushes', { count: notification?.content?.error?.length ?? 0 })}
      </div>
      {notification?.content?.error && (
        <Box maxH="200px" overflowY="auto">
          <UnorderedList maxH="200px" overflowY="auto">
            {notification?.content?.error.map((serialNumber: string) => (
              <ListItem key={uuid()}>{serialNumber}</ListItem>
            ))}
          </UnorderedList>
        </Box>
      )}
    </>
  );
};

export default ConfigurationPushesNotificationContent;
