import React from 'react';
import { Box, ListItem, UnorderedList } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import { ProvisioningVenueNotificationMessage } from '@/modules-smb/contexts/ProvisioningSocketProvider/utils';

interface Props {
  notification: ProvisioningVenueNotificationMessage['notification'];
}

const DeviceRebootNotificationContent = ({ notification }: Props) => {
  const { t } = useTranslation();

  return (
    <>
      <div>
        {t('inventory.successful_reboots', {
          count: notification?.content?.success?.length ?? 0,
        })}
      </div>
      {notification?.content?.success && (
        <Box maxH="200px" overflowY="auto">
          <UnorderedList>
            {notification?.content?.success.map((serialNumber) => (
              <ListItem key={uuid()}>{serialNumber}</ListItem>
            ))}
          </UnorderedList>
        </Box>
      )}
      <div>
        {t('inventory.warning_reboots', { count: notification?.content?.warning?.length ?? 0 })}
      </div>
      {notification?.content?.warning && (
        <Box maxH="200px" overflowY="auto">
          <UnorderedList maxH="200px" overflowY="auto">
            {notification?.content?.warning.map((serialNumber) => (
              <ListItem key={uuid()}>{serialNumber}</ListItem>
            ))}
          </UnorderedList>
        </Box>
      )}
      <div>
        {t('inventory.error_reboots', { count: notification?.content?.error?.length ?? 0 })}
      </div>
      {notification?.content?.error && (
        <Box maxH="200px" overflowY="auto">
          <UnorderedList maxH="200px" overflowY="auto">
            {notification?.content?.error.map((serialNumber) => (
              <ListItem key={uuid()}>{serialNumber}</ListItem>
            ))}
          </UnorderedList>
        </Box>
      )}
    </>
  );
};

export default DeviceRebootNotificationContent;
