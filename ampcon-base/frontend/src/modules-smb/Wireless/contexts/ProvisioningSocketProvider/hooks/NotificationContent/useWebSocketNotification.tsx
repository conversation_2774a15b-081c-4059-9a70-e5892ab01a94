import React, { useCallback, useMemo, useState } from 'react';
import { Modal, notification as antdNotification, But<PERSON>, Divider } from 'antd';
import { TOptions } from 'i18next';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import { ProvisioningVenueNotificationMessage } from '../../utils';
import NotificationContent from '.';

const getStatusFromNotification = (notification: ProvisioningVenueNotificationMessage['notification']) => {
  let status: 'success' | 'warning' | 'error' = 'success';
  if (notification.content.warning?.length > 0) status = 'warning';
  if (notification.content.error?.length > 0) status = 'error';

  return status;
};

const getNotificationDescription = (
  t: (key: string, options?: string | TOptions<Record<string, number>> | undefined) => string,
  notification: ProvisioningVenueNotificationMessage['notification'],
) => {
  if (notification.type_id === 1000 || notification.type_id === 2000 || notification.type_id === 3000) {
    return t('configurations.notification_details', {
      success: notification.content.success?.length ?? 0,
      warning: notification.content.warning?.length ?? 0,
      error: notification.content.error?.length ?? 0,
    });
  }
  return notification.content.details;
};

const useWebSocketNotification = () => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [notif, setNotif] = useState<ProvisioningVenueNotificationMessage['notification'] | undefined>(undefined);
  const [api, contextHolder] = antdNotification.useNotification();

  const openDetails = useCallback(
    (newObj: ProvisioningVenueNotificationMessage['notification'], closeNotif?: () => void) => {
      setNotif(newObj);
      if (closeNotif) closeNotif();
      setIsOpen(true);
    },
    [],
  );

  const pushNotification = useCallback(
    (notification: ProvisioningVenueNotificationMessage['notification']) => {
      const status = getStatusFromNotification(notification);
      const key = uuid();
      const desc = getNotificationDescription(t, notification);
      const btn = (
        <div style={{ marginTop: 10 }}>
          <Button
            type="primary"
            onClick={() =>
              openDetails(notification, () => {
                // @ts-ignore
                if (typeof api.close === 'function') api.close(key);
                // @ts-ignore
                else if (typeof api.destroy === 'function') api.destroy();
              })
            }
          >
            {t('common.view_details')}
          </Button>
        </div>
      );

      const messageNode = <div style={{ fontWeight: 600, fontSize: 14 }}>{notification.content.title}</div>;
      const descriptionNode = (
        <div>
          <div style={{ whiteSpace: 'pre-wrap' }}>{desc}</div>
          <div style={{ marginTop: 10, textAlign: 'left' }}>{btn}</div>
        </div>
      );

      if (status === 'success') {
        api.success({ key, message: messageNode, description: descriptionNode, duration: 5000 });
      } else if (status === 'warning') {
        api.warning({ key, message: messageNode, description: descriptionNode, duration: 5000 });
      } else if (status === 'error') {
        api.error({ key, message: messageNode, description: descriptionNode, duration: 5000 });
      } else {
        api.open({ key, message: messageNode, description: descriptionNode, duration: 5000 });
      }
    },
    [t, openDetails, api],
  );

  const modal = useMemo(() => {
    return (
      <>
        {contextHolder}
        <Modal
          title={
            <div style={{ fontFamily: 'Lato, Lato' }}>
                View Details
                <Divider style={{ marginTop: 8, marginBottom: 0 }} />
            </div>
          }
          open={isOpen}
          onCancel={() => setIsOpen(false)}
          footer={null}
          className="ampcon-middle-modal"
        >
          <h3 style={{ fontSize: '16px' }}>{notif?.content?.title ?? ''}</h3>
          <NotificationContent notification={notif} />
        </Modal>
      </>
    );
  }, [notif, isOpen, contextHolder]);

  const toReturn = useMemo(
    () => ({
      modal,
      pushNotification,
    }),
    [modal, pushNotification],
  );

  return toReturn;
};

export default useWebSocketNotification;
