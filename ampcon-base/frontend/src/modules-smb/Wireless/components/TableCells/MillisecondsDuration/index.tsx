import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

// 毫秒级时间显示组件
const MillisecondsDuration = ({ milliseconds }: { milliseconds?: number }) => {
  const { t } = useTranslation();

  const formattedDuration = useMemo(() => {
    if (milliseconds === undefined) {
      return '-';
    }
    if (!milliseconds || milliseconds === 0)
      return `0 ${t('common.seconds')}`;
    const ms = milliseconds % 1000;

    let seconds = Math.floor(milliseconds / 1000);

    const days = Math.floor(seconds / (3600 * 24));
    seconds -= days * (3600 * 24);

    const hours = Math.floor(seconds / 3600);
    seconds -= hours * 3600;
    
    const minutes = Math.floor(seconds / 60);
    seconds -= minutes * 60;

    let finalString = '';
    finalString += `${twoDigitNumber(days)}d`;
    finalString += `${twoDigitNumber(hours)}h`;
    finalString += `${twoDigitNumber(minutes)}m`;
    finalString += `${twoDigitNumber(seconds)}.`;
    finalString += `${threeDigitNumber(ms)}s`;

    return finalString;
  }, [milliseconds, t]);

  return <div>{formattedDuration}</div>;
};

const twoDigitNumber = (number: number) => {
  return number >= 10 ? number.toString() : `0${number}`;
};

// 数字为三位数（用于毫秒）
const threeDigitNumber = (number: number) => {
  if (number >= 100) return number.toString();
  if (number >= 10) return `0${number}`;
  return `00${number}`;
};

export default React.memo(MillisecondsDuration);
    