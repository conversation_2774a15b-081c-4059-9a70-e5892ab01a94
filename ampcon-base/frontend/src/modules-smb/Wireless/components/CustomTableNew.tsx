import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useState, useCallback, useRef } from "react";
import { Flex, Table, Input } from "antd";
import { useDispatch } from "react-redux";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

import { useTableInitialElement } from "@/modules-ampcon/hooks/useModalTable";
import { updateAlarmSearch, updateAlarmSearchStatus } from "@/store/modules/common/alarm_slice";
import { handleTableChange } from "@/modules-ampcon/components/custom_table";
import ColumnSelector from '@/modules-smb/Wireless/components/Modals/ColumnSelectorWithOrderProps';
import ResizableTable from '@/modules-smb/Wireless/components/Tables/ResizableTable';
import { useAuth } from '@/modules-smb/contexts/AuthProvider';
import { useGetPreferences } from '@/modules-smb/hooks/Network/Account';

dayjs.extend(utc);
dayjs.extend(timezone);

// ==================== 工具函数 ====================

/**
 * 创建匹配模式映射
 * @param fields 字段配置数组
 * @returns 匹配模式映射对象
 */
export const createMatchMode = (fields: any[]) => {
    const matchModes: Record<string, string> = {};
    fields.forEach(field => {
        matchModes[field.name] = field.matchMode;
    });
    return matchModes;
};

/**
 * 创建过滤字段配置
 * @param filters 过滤器对象
 * @param matchModes 匹配模式映射
 * @returns 过滤字段配置数组
 */
const createFilterFields = (filters: Record<string, any[]>, matchModes: Record<string, string>) => {
    return Object.keys(filters).map(field => {
        const matchMode = matchModes[field] || "exact";
        const fieldFilters = filters[field] || [];
        return {
            field,
            filters: fieldFilters.map(value => ({ value, matchMode }))
        };
    });
};

/**
 * 全局搜索输入组件
 */
const GlobalSearchInput: React.FC<{ onChange: (e: React.ChangeEvent<HTMLInputElement>) => void }> = ({ onChange }) => (
    <Input
        placeholder="Search"
        allowClear
        onChange={onChange}
        style={{ width: 280, height: "32px", float: "right", borderRadius: "2px" }}
    />
);

// ==================== 类型定义 ====================

interface WirelessCustomTableProps {
    quantity?: number;
    columns: any[];
    rowSelection?: any;
    matchFieldsList?: any[];
    searchFieldsList?: any[];
    extraButton?: React.ReactNode;
    helpDraw?: React.ReactNode;
    fetchAPIInfo: (...args: any[]) => Promise<any>;
    fetchAPIParams?: any[];
    isShowPagination?: boolean;
    disableInternalRowSelection?: boolean;
    showColumnSelector?: boolean;
    columnsOrder?: boolean;
    resizableColumns?: boolean;
    tableId?: string;
    [key: string]: any;
}

interface WirelessCustomTableRef {
    refreshTable: () => void;
    setTableLoading: (value: boolean) => void;
    getSelectedRow: () => { tableSelectedRowKey: any[]; tableSelectedRows: any[] };
    getRemovedRow: () => { tableRemovedRowKey: any[]; tableRemovedRows: any[] };
    clearSelectedRow: () => void;
    getOperations: () => Record<string, string>;
    getOperationRowsMappings: () => Record<string, any>;
    getTableData: () => any[];
    clearAndRefresh: () => void;
    refreshAndSaveSelectedRow: () => void;
}

// ==================== 主组件 ====================

export const WirelessCustomTable = forwardRef<WirelessCustomTableRef, WirelessCustomTableProps>(
    (
        {
            quantity,
            columns,
            rowSelection,
            matchFieldsList,
            searchFieldsList,
            extraButton,
            helpDraw,
            fetchAPIInfo,
            fetchAPIParams,
            isShowPagination,
            disableInternalRowSelection,
            showColumnSelector = false,
            columnsOrder = false,
            resizableColumns = false,
            tableId,
            ...props
        },
        ref
    ) => {
        // ==================== Preferences 相关状态和逻辑 ====================

        const { setPref } = useAuth();
        const [visibleColumns, setVisibleColumns] = useState<string[]>([]);
        const [isInitialized, setIsInitialized] = useState(false);

        // 判断是否需要启用 preferences 功能
        const shouldEnablePreferences = !!(tableId && (columnsOrder || showColumnSelector));

        const { data: preferences, isLoading: preferencesLoading } = useGetPreferences({
            enabled: shouldEnablePreferences,
        });

        // 稳定化 columns 引用
        const stableColumns = useMemo(() => columns, [
            JSON.stringify(columns?.map((col: any) => ({ key: col.key, dataIndex: col.dataIndex })))
        ]);

        // Preferences 工具函数
        const getPreference = useCallback((preference: string) => {
            if (!preferences) return null;
            for (const pref of preferences) {
                if (pref.tag === preference) return pref.value;
            }
            return null;
        }, [preferences]);

        // 获取默认可见列
        const getDefaultVisibleColumns = useCallback(() => {
            return stableColumns.map((col: any) => col.key || col.dataIndex);
        }, [stableColumns]);

        // 初始化列配置
        useEffect(() => {
            if (!shouldEnablePreferences) {
                // 不需要 preferences 功能时，直接使用默认值
                if (!isInitialized && stableColumns) {
                    setVisibleColumns(getDefaultVisibleColumns());
                    setIsInitialized(true);
                }
                return;
            }

            // 需要 preferences 功能时，等待数据加载完成
            if (!tableId || !stableColumns || isInitialized || preferencesLoading) return;

            try {
                const savedVisibleColumns = getPreference(`table_${tableId}_visible_columns`);
                const savedColumnOrder = getPreference(`table_${tableId}_column_order`);

                let newVisibleColumns: string[] = [];

                if (savedVisibleColumns) {
                    newVisibleColumns = JSON.parse(savedVisibleColumns);
                } else if (savedColumnOrder) {
                    newVisibleColumns = JSON.parse(savedColumnOrder);
                } else {
                    newVisibleColumns = getDefaultVisibleColumns();
                }

                // 如果有保存的列顺序，按照该顺序重新排列可见列
                if (savedColumnOrder) {
                    const columnOrder = JSON.parse(savedColumnOrder);
                    const orderedVisibleColumns: string[] = [];

                    // 按保存的顺序添加列
                    columnOrder.forEach((key: string) => {
                        if (newVisibleColumns.includes(key)) {
                            orderedVisibleColumns.push(key);
                        }
                    });

                    // 添加不在顺序中但可见的列
                    newVisibleColumns.forEach(key => {
                        if (!orderedVisibleColumns.includes(key)) {
                            orderedVisibleColumns.push(key);
                        }
                    });

                    newVisibleColumns = orderedVisibleColumns;
                }

                setVisibleColumns(newVisibleColumns);
            } catch (e) {
                // 出错时使用默认值
                setVisibleColumns(getDefaultVisibleColumns());
            }

            setIsInitialized(true);
        }, [
            tableId,
            stableColumns,
            preferences,
            preferencesLoading,
            isInitialized,
            shouldEnablePreferences,
            getPreference,
            getDefaultVisibleColumns
        ]);

        // ==================== 列配置处理逻辑 ====================

        // 根据可见列配置过滤和排序列（规范化 fixed 列在左右两侧的位置）
        const filteredColumns = useMemo(() => {
            if (!stableColumns) return [];

            // 规范化顺序方法：left-fixed -> normal -> right-fixed，并保持各自组内与 ColumnSelector 一致的顺序
            const normalizeOrder = (keys: string[]) => {
                const leftSet = new Set(
                    stableColumns
                        .filter((c: any) => c.fixed === 'left')
                        .map((c: any) => c.key || c.dataIndex)
                );
                const rightSet = new Set(
                    stableColumns
                        .filter((c: any) => c.fixed === 'right')
                        .map((c: any) => c.key || c.dataIndex)
                );

                const left: string[] = [];
                const middle: string[] = [];
                const right: string[] = [];

                keys.forEach((k) => {
                    if (leftSet.has(k)) left.push(k);
                    else if (rightSet.has(k)) right.push(k);
                    else middle.push(k);
                });

                return [...left, ...middle, ...right];
            };

            const rawVisibleKeys = visibleColumns.length > 0
                ? visibleColumns
                : stableColumns.map((col: any) => col.key || col.dataIndex);

            const visibleKeys = normalizeOrder(rawVisibleKeys);

            // 过滤出可见的列
            const visibleColumnsData = stableColumns.filter((col: any) => {
                const key = col.key || col.dataIndex;
                return visibleKeys.includes(key);
            });

            // 按规范化后的顺序排序
            return visibleColumnsData.sort((a: any, b: any) => {
                const keyA = a.key || a.dataIndex;
                const keyB = b.key || b.dataIndex;
                const indexA = visibleKeys.indexOf(keyA);
                const indexB = visibleKeys.indexOf(keyB);
                return indexA - indexB;
            });
        }, [stableColumns, visibleColumns]);

        // 保存列可见性配置
        const saveVisibleColumns = useCallback(async (newVisibleColumns: string[]) => {
            // 始终更新本地状态，保证在未传 tableId（不持久化）的情况下也能正常生效
            setVisibleColumns(newVisibleColumns);

            // 仅当提供了 tableId 时才持久化到偏好设置
            if (!tableId) return;

            try {
                await setPref({
                    preference: `table_${tableId}_visible_columns`,
                    value: JSON.stringify(newVisibleColumns)
                });
            } catch (e) {
                // console.error('Error saving visible columns:', e);
            }
        }, [tableId, setPref]);

        // 保存列顺序配置
        const saveColumnOrder = useCallback(async (newColumnOrder: string[]) => {
            // 未提供 tableId 时不做持久化，但也不应阻断功能
            if (!tableId) return;

            try {
                await setPref({
                    preference: `table_${tableId}_column_order`,
                    value: JSON.stringify(newColumnOrder)
                });
            } catch (e) {
                // console.error('Error saving column order:', e);
            }
        }, [tableId, setPref]);

        // 处理列可见性变化
        const handleVisibleColumnsChange = useCallback((newVisibleColumns: string[]) => {
            const fixedKeepVisibleKeys = stableColumns
                .filter((col: any) => col.columnsFix)
                .map((col: any) => col.key || col.dataIndex);

            // 合并并去重，确保 columnsFix 的列始终可见
            const merged = Array.from(new Set([...newVisibleColumns, ...fixedKeepVisibleKeys]));

            // 规范化顺序：保证 fixed:'left' 在最左、fixed:'right' 在最右
            const leftSet = new Set(
                stableColumns
                    .filter((c: any) => c.fixed === 'left')
                    .map((c: any) => c.key || c.dataIndex)
            );
            const rightSet = new Set(
                stableColumns
                    .filter((c: any) => c.fixed === 'right')
                    .map((c: any) => c.key || c.dataIndex)
            );

            const left: string[] = [];
            const middle: string[] = [];
            const right: string[] = [];
            merged.forEach((k) => {
                if (leftSet.has(k)) left.push(k);
                else if (rightSet.has(k)) right.push(k);
                else middle.push(k);
            });
            const normalized = [...left, ...middle, ...right];

            // 保存可见列配置（顺序已规范化）
            saveVisibleColumns(normalized);
        }, [stableColumns, saveVisibleColumns]);

        // 处理列顺序变化
        const handleColumnOrderChange = useCallback((newColumnOrder: string[]) => {
            // 找出所有fixed: 'left'和fixed: 'right'的列
            const leftFixedColumns = stableColumns
                .filter((col: any) => col.fixed === 'left')
                .map((col: any) => col.key || col.dataIndex);
                
            const rightFixedColumns = stableColumns
                .filter((col: any) => col.fixed === 'right')
                .map((col: any) => col.key || col.dataIndex);
            
            // 从newColumnOrder中移除所有fixed列
            let filteredOrder = newColumnOrder.filter(key => 
                !leftFixedColumns.includes(key) && !rightFixedColumns.includes(key)
            );
            
            // 将fixed: 'left'的列放在最前面，fixed: 'right'的列放在最后面
            const finalOrder = [...leftFixedColumns, ...filteredOrder, ...rightFixedColumns];
            
            // 保存最终的列顺序
            saveColumnOrder(finalOrder);
        }, [saveColumnOrder, stableColumns]);

        // 处理可调整大小的列
        const processedColumns = useMemo(() => {
            if (!resizableColumns) {
                return filteredColumns;
            }

            return filteredColumns.map((column: any) => {
                const identity = column.key || column.dataIndex;
                // 查找原始列配置，确保保留所有属性
                const originalColumn = stableColumns.find((col: any) => 
                    (col.key || col.dataIndex) === identity
                ) || column;
                
                // 确保resizable属性被正确保留，即使列被取消勾选再勾选
                const isResizable = originalColumn.resizable !== false;
                
                // 创建一个新的列对象，确保保留所有原始属性
                const processedColumn: any = {
                    // 以原始列为基准，保留原有属性
                    ...originalColumn,
                    // 可见列上的变更覆盖
                    ...column,
                };

                // 补充稳定的 key，避免只存在 dataIndex 导致匹配异常
                processedColumn.key = originalColumn.key || originalColumn.dataIndex || identity;

                // 明确设置resizable属性和基础尺寸，防止被丢失
                if (isResizable) {
                    processedColumn.resizable = true;
                    processedColumn.minWidth = originalColumn.minWidth || processedColumn.minWidth || 1;
                    processedColumn.width = processedColumn.width || originalColumn.width || 1;
                } else {
                    processedColumn.resizable = false;
                }
                
                return processedColumn;
            });
        }, [filteredColumns, resizableColumns, stableColumns]);

        // 构建最终的列配置（去掉内置列选择器列）
        const finalColumns = useMemo(() => {
            return processedColumns;
        }, [processedColumns]);


        // ==================== 表格状态管理 ====================

        const [_, __, searchFields, setSearchFields, data, setData, loading, setLoading, pagination, setPagination] =
            useTableInitialElement(searchFieldsList, false);

        const matchModes = createMatchMode(matchFieldsList || []);
        const dispatch = useDispatch();

        // 行选择相关状态
        const [tableSelectedRowKey, setTableSelectedRowKey] = useState<any[]>(
            rowSelection ? rowSelection.selectedRowKeys || [] : []
        );
        const [tableSelectedRows, setTableSelectedRows] = useState<any[]>(
            rowSelection ? rowSelection.selectedRows || [] : []
        );
        const [tableRemovedRowKey, setTableRemovedRowKey] = useState<any[]>([]);
        const [tableRemovedRows, setTableRemovedRows] = useState<any[]>([]);
        const [operations, setOperations] = useState<Record<string, string>>({});
        const [operationRowsMappings, setOperationRowsMappings] = useState<Record<string, any>>({});

        // 排序和过滤状态
        const [sorter, setSorter] = useState<any>({});
        const [filters, setFilters] = useState<Record<string, any>>({});

        // 检查默认排序列
        const checkSortedColumn = useCallback((columns: any[]) => {
            for (const columnKey in columns) {
                if (Object.prototype.hasOwnProperty.call(columns, columnKey)) {
                    const columnConfig = columns[columnKey];
                    if (columnConfig.defaultSortOrder !== null) {
                        return [columnConfig.dataIndex, columnConfig.defaultSortOrder];
                    }
                }
            }
            return [undefined, undefined];
        }, []);

        // ==================== 行选择处理逻辑 ====================

        // 处理单行选择
        const handleSelect = useCallback((record: any, selected: boolean) => {
            const keys = selected
                ? tableSelectedRowKey.concat([record.id])
                : tableSelectedRowKey.filter((item: any) => item !== record.id);

            // 更新移除的行记录
            if (!selected) {
                setTableRemovedRowKey([...tableRemovedRowKey, record.id]);
                setTableRemovedRows([...tableRemovedRows, record]);
            } else {
                setTableRemovedRowKey(tableRemovedRowKey.filter((item: any) => item !== record.id));
                setTableRemovedRows(tableRemovedRows.filter((item: any) => item.id !== record.id));
            }

            const rows = selected
                ? [...tableSelectedRows, record]
                : tableSelectedRows.filter((item: any) => item.id !== record.id);

            // 处理操作状态
            if (Object.prototype.hasOwnProperty.call(record, "selected")) {
                const operationsTemp = { ...operations };
                if (record.selected === false && selected) {
                    operationsTemp[record.id] = "add";
                } else if (record.selected === false && !selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && selected) {
                    delete operationsTemp[record.id];
                } else if (record.selected === true && !selected) {
                    operationsTemp[record.id] = "remove";
                }
                setOperations(operationsTemp);
            }

            // 处理父子关系 - 子项选择时移除父项
            rows.forEach((row: any) => {
                if (row.children) {
                    const isInChildren = row.children.some((child: any) => child.id === record.id);
                    if (isInChildren) {
                        const rowIndex = rows.findIndex((r: any) => r.id === row.id);
                        if (rowIndex > -1) {
                            rows.splice(rowIndex, 1);
                        }

                        const keyIndex = keys.findIndex((k: any) => k === row.id);
                        if (keyIndex > -1) {
                            keys.splice(keyIndex, 1);
                        }
                    }
                }
            });

            // 处理父子关系 - 父项选择时处理子项
            if (Object.prototype.hasOwnProperty.call(record, "children")) {
                if (selected) {
                    record.children.forEach((child: any) => {
                        if (!keys.includes(child.id)) {
                            keys.push(child.id);
                        }
                        if (!rows.some((row: any) => row.id === child.id)) {
                            rows.push(child);
                        }
                    });
                } else {
                    const needRemoveRows = record.children.map((child: any) => child.id);
                    needRemoveRows.forEach((id: any) => {
                        const rowIndex = rows.findIndex((r: any) => r.id === id);
                        if (rowIndex > -1) {
                            rows.splice(rowIndex, 1);
                        }
                        const keyIndex = keys.findIndex((k: any) => k === id);
                        if (keyIndex > -1) {
                            keys.splice(keyIndex, 1);
                        }
                    });
                }
            }

            setTableSelectedRowKey(keys);
            setTableSelectedRows(rows);

            if (rowSelection && rowSelection.onChange) {
                rowSelection.onChange(keys, rows);
            }
        }, [
            tableSelectedRowKey,
            tableRemovedRowKey,
            tableRemovedRows,
            tableSelectedRows,
            operations,
            rowSelection
        ]);

        // 处理全选/取消全选
        const handleSelectAll = useCallback((selected: boolean, selectedRows: any[], changeRows: any[]) => {
            if (quantity) {
                // 有数量限制的情况
                const currentCount = tableSelectedRowKey.length;
                const remaining = quantity - currentCount;

                if (selected && remaining <= 0) {
                    return; // 已满，不允许再选
                }

                const limitedRows = selected ? changeRows.slice(0, remaining) : changeRows;
                const limitedIds = limitedRows.map((item: any) => item.id);

                const keys = selected
                    ? Array.from(new Set([...tableSelectedRowKey, ...limitedIds]))
                    : tableSelectedRowKey.filter((item: any) => !limitedIds.includes(item));
                setTableSelectedRowKey(keys);

                const rows = selected
                    ? Array.from(new Set([...tableSelectedRows, ...limitedRows]))
                    : tableSelectedRows.filter((item: any) => !limitedIds.includes(item.id));
                setTableSelectedRows(rows);

                // 更新移除记录
                if (!selected) {
                    setTableRemovedRowKey([...tableRemovedRowKey, ...limitedIds]);
                    setTableRemovedRows([...tableRemovedRows, ...limitedRows]);
                } else {
                    setTableRemovedRowKey(tableRemovedRowKey.filter((item: any) => !limitedIds.includes(item)));
                    setTableRemovedRows(tableRemovedRows.filter((item: any) => !limitedIds.includes(item.id)));
                }

                // 处理操作状态
                if (changeRows.length > 0 && Object.prototype.hasOwnProperty.call(changeRows[0], "selected")) {
                    const operationsTemp = { ...operations };
                    limitedRows.forEach((record: any) => {
                        if (record.selected === false && selected) {
                            operationsTemp[record.id] = "add";
                        } else if (record.selected === false && !selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && !selected) {
                            operationsTemp[record.id] = "remove";
                        }
                    });
                    setOperations(operationsTemp);
                }

                if (rowSelection && rowSelection.onChange) {
                    rowSelection.onChange(keys, rows);
                }
            } else {
                // 无数量限制的情况
                const ids = changeRows.map((item: any) => item.id);
                const keys = selected
                    ? tableSelectedRowKey.concat(ids)
                    : tableSelectedRowKey.filter((item: any) => !ids.includes(item));
                setTableSelectedRowKey(keys);

                // 更新移除记录
                if (!selected) {
                    setTableRemovedRowKey([...tableRemovedRowKey, ...ids]);
                    setTableRemovedRows([...tableRemovedRows, ...changeRows]);
                } else {
                    setTableRemovedRowKey(tableRemovedRowKey.filter((item: any) => !ids.includes(item)));
                    setTableRemovedRows(tableRemovedRows.filter((item: any) => !ids.includes(item.id)));
                }

                const rows = selected
                    ? [...tableSelectedRows, ...changeRows]
                    : tableSelectedRows.filter((item: any) => !ids.includes(item.id));
                setTableSelectedRows(rows);

                // 处理默认选中行的操作状态
                if (changeRows.length > 0 && Object.prototype.hasOwnProperty.call(changeRows[0], "selected")) {
                    const operationsTemp = { ...operations };
                    changeRows.forEach((record: any) => {
                        if (record.selected === false && selected) {
                            operationsTemp[record.id] = "add";
                        } else if (record.selected === false && !selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && selected) {
                            delete operationsTemp[record.id];
                        } else if (record.selected === true && !selected) {
                            operationsTemp[record.id] = "remove";
                        }

                        setOperationRowsMappings(prev => ({
                            ...prev,
                            [record.id]: record
                        }));
                    });
                    setOperations(operationsTemp);
                }

                if (rowSelection && rowSelection.onChange) {
                    rowSelection.onChange(keys, rows);
                }
            }
        }, [
            quantity,
            tableSelectedRowKey,
            tableSelectedRows,
            tableRemovedRowKey,
            tableRemovedRows,
            operations,
            rowSelection
        ]);

        // 构建表格行选择配置
        const tableRowSelection = useMemo(() => {
            if (rowSelection?.type === "radio") {
                return rowSelection;
            }

            if (disableInternalRowSelection && rowSelection) {
                return rowSelection;
            }

            return {
                selectedRowKeys: tableSelectedRowKey,
                onSelect: handleSelect,
                onSelectAll: handleSelectAll,
                getCheckboxProps: rowSelection?.getCheckboxProps,
                fixed: rowSelection?.fixed,
                checkStrictly: rowSelection?.checkStrictly
            };
        }, [
            disableInternalRowSelection,
            rowSelection?.type,
            rowSelection?.getCheckboxProps,
            rowSelection?.fixed,
            rowSelection?.checkStrictly,
            tableSelectedRowKey,
            handleSelect,
            handleSelectAll
        ]);

        // ==================== 组件引用方法 ====================

        useImperativeHandle(ref, () => ({
            refreshTable() {
                fetchData();
            },
            setTableLoading(value: boolean) {
                setLoading(value);
            },
            getSelectedRow: () => ({
                tableSelectedRowKey,
                tableSelectedRows
            }),
            getRemovedRow: () => ({
                tableRemovedRowKey,
                tableRemovedRows
            }),
            clearSelectedRow: () => {
                setTableSelectedRowKey([]);
                setTableSelectedRows([]);
            },
            getOperations: () => operations,
            getOperationRowsMappings: () => operationRowsMappings,
            getTableData: () => data,
            clearAndRefresh: () => {
                setTableSelectedRowKey([]);
                setTableSelectedRows([]);
                fetchData(true);
            },
            refreshAndSaveSelectedRow: () => {
                fetchData(true);
            }
        }), [
            tableSelectedRowKey,
            tableSelectedRows,
            tableRemovedRowKey,
            tableRemovedRows,
            operations,
            operationRowsMappings,
            data,
            setLoading,
            setTableSelectedRowKey,
            setTableSelectedRows
        ]);

        // ==================== 数据获取逻辑 ====================

        const fetchData = useCallback(async (ignoreSelection = false) => {
            setLoading(true);

            try {
                // 构建过滤和排序参数
                const filterFields = filters ? createFilterFields(filters, matchModes) : [];
                const sortFields: any[] = [];

                if (sorter.field && sorter.order) {
                    sortFields.push({
                        field: sorter.field,
                        order: sorter.order === "ascend" ? "asc" : "desc"
                    });
                }

                // 调用API获取数据
                let response = await fetchAPIInfo(
                    ...(fetchAPIParams
                        ? [
                            ...fetchAPIParams,
                            pagination.current,
                            pagination.pageSize,
                            filterFields,
                            sortFields,
                            searchFields
                        ]
                        : [pagination.current, pagination.pageSize, filterFields, sortFields, searchFields])
                );

                // 如果当前页没有数据但总数不为0，跳转到最后一页
                if (response.data.length === 0 && response.total !== 0) {
                    const lastPage = Math.ceil(response.total / response.pageSize);
                    response = await fetchAPIInfo(
                        ...(fetchAPIParams
                            ? [...fetchAPIParams, lastPage, pagination.pageSize, [], [], searchFields]
                            : [lastPage, pagination.pageSize, [], [], searchFields])
                    );
                }

                const responseDataTemp = JSON.parse(JSON.stringify(response.data));

                // 处理选择状态
                if (!ignoreSelection) {
                    responseDataTemp.forEach((item: any) => {
                        item.selected = operations[item.id] === "add" ? true : item.selected;
                    });

                    if (responseDataTemp.every((item: any) => "selected" in item)) {
                        const backendSelectedRowKeys = responseDataTemp
                            .filter((item: any) => item.selected)
                            .map((item: any) => item.id);

                        const frontendSelectedRowKeys = tableSelectedRowKey
                            ? responseDataTemp
                                .filter((item: any) => tableSelectedRowKey.indexOf(item.id) > -1)
                                .map((item: any) => item.id)
                            : [];

                        const removedRowKeys = tableRemovedRowKey
                            ? responseDataTemp
                                .filter((item: any) => tableRemovedRowKey.indexOf(item.id) > -1)
                                .map((item: any) => item.id)
                            : [];

                        const selectedRowKeys = Array.from(
                            new Set([
                                ...(tableSelectedRowKey || []),
                                ...backendSelectedRowKeys,
                                ...frontendSelectedRowKeys
                            ])
                        ).filter((itemId: any) => removedRowKeys.indexOf(itemId) === -1);

                        setTableSelectedRowKey(selectedRowKeys);
                        setTableSelectedRows(responseDataTemp.filter((item: any) => item.selected));
                    }
                }

                setData(responseDataTemp);
                setPagination((prev: any) => ({
                    ...prev,
                    total: response.total,
                    current: response.page,
                    pageSize: response.pageSize
                }));
            } catch (error) {
                // console.error("Error fetching data:", error);
            } finally {
                setLoading(false);
            }
        }, [
            filters,
            matchModes,
            sorter,
            fetchAPIInfo,
            fetchAPIParams,
            pagination.current,
            pagination.pageSize,
            searchFields,
            operations,
            tableSelectedRowKey,
            tableRemovedRowKey
        ]);

        // ==================== 副作用和事件处理 ====================

        // 初始化默认排序
        useEffect(() => {
            const [sortedColumn, sortedOrder] = checkSortedColumn(columns);
            if (sortedColumn) {
                setSorter({
                    field: sortedColumn,
                    order: sortedOrder
                });
            }
        }, [columns, checkSortedColumn]);

        // 数据获取副作用
        useEffect(() => {
            fetchData();
        }, [fetchData]);

        // 搜索变化处理
        const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
            dispatch(updateAlarmSearchStatus(false));
            dispatch(updateAlarmSearch(""));

            // 清空选择状态
            if (tableRowSelection && tableRowSelection.selectedRowKeys) {
                tableRowSelection.selectedRowKeys = [];
            }
            setTableSelectedRows([]);
            setTableSelectedRowKey([]);

            setSearchFields({
                fields: searchFieldsList,
                value: e.target.value
            });
        }, [dispatch, tableRowSelection, searchFieldsList, setSearchFields]);

        // 表格变化处理
        const tableChange = useCallback(async (pagination: any, filters: any, sorter: any) => {
            // 添加延迟以确保状态更新
            await new Promise(resolve => setTimeout(resolve, 100));

            setSorter(sorter);
            setFilters(filters);

            await handleTableChange(
                pagination,
                filters,
                sorter,
                setPagination,
                searchFields,
                fetchAPIInfo,
                fetchAPIParams,
                setData,
                matchModes,
                setLoading,
                tableSelectedRowKey,
                tableSelectedRows,
                setTableSelectedRowKey,
                setTableSelectedRows,
                tableRemovedRowKey
            );
        }, [
            searchFields,
            fetchAPIInfo,
            fetchAPIParams,
            matchModes,
            tableSelectedRowKey,
            tableSelectedRows,
            tableRemovedRowKey
        ]);



        // ==================== 组件渲染 ====================

        // 右侧列选择器独立表格：与主表严格拼接，并同步行高与滚动
        const mainTableWrapRef = useRef<HTMLDivElement>(null);
        const sideTableWrapRef = useRef<HTMLDivElement>(null);
        const [sideRowCount, setSideRowCount] = useState<number>(0);
        const sideTableClass = useMemo(() => `side-selector-table-${tableId || 'default'}`, [tableId]);
        const mainTableClass = useMemo(() => `main-data-table-${tableId || 'default'}`, [tableId]);

        useEffect(() => {
            // 同步滚动
            const mainWrap = mainTableWrapRef.current;
            const sideWrap = sideTableWrapRef.current;
            if (!mainWrap || !sideWrap) return;

            const mainBody = mainWrap.querySelector('.ant-table-body') as HTMLElement | null;
            const sideBody = sideWrap.querySelector('.ant-table-body') as HTMLElement | null;
            if (!mainBody || !sideBody) return;

            const onMainScroll = () => {
                if (sideBody && sideBody.scrollTop !== mainBody.scrollTop) {
                    sideBody.scrollTop = mainBody.scrollTop;
                }
            };
            mainBody.addEventListener('scroll', onMainScroll, { passive: true });
            return () => {
                mainBody.removeEventListener('scroll', onMainScroll as any);
            };
        }, [data, loading]);

        // 监听主表 tbody 的 DOM 变化，实时同步行数与行高（应对分页/过滤等导致的异步渲染）
        useEffect(() => {
            const mainWrap = mainTableWrapRef.current;
            const sideWrap = sideTableWrapRef.current;
            if (!mainWrap || !sideWrap) return;

            const tbody = mainWrap.querySelector('.ant-table-tbody');
            if (!tbody) return;

            const applySync = () => {
                const mainRows = mainWrap.querySelectorAll('.ant-table-tbody > tr.ant-table-row');
                const sideRows = sideWrap.querySelectorAll('.ant-table-tbody > tr.ant-table-row');
                setSideRowCount(mainRows.length);
                if (mainRows.length === sideRows.length) {
                    sideRows.forEach((row, idx) => {
                        const mainRow = mainRows[idx] as HTMLElement;
                        const h = mainRow.getBoundingClientRect().height;
                        (row as HTMLElement).style.height = `${h}px`;
                    });
                }
            };

            const observer = new MutationObserver(() => {
                requestAnimationFrame(applySync);
            });

            observer.observe(tbody, { childList: true, subtree: true });
            // 初始同步一次
            requestAnimationFrame(applySync);
            return () => observer.disconnect();
        }, [data, loading]);

        useEffect(() => {
            // 同步行高（在下一帧执行，确保主表已经完成渲染）
            const mainWrap = mainTableWrapRef.current;
            const sideWrap = sideTableWrapRef.current;
            if (!mainWrap || !sideWrap) return;

            const mainBody = mainWrap.querySelector('.ant-table-body') as HTMLElement | null;
            const sideBody = sideWrap.querySelector('.ant-table-body') as HTMLElement | null;
            if (!mainBody || !sideBody) return;

            const measure = () => {
                // 仅统计真实数据行，避免占位或测量行导致 +1
                const mainRows = mainBody.querySelectorAll('tbody > tr.ant-table-row');
                const sideRows = sideBody.querySelectorAll('tbody > tr.ant-table-row');
                // 记录主表当前可视行数，用于占位渲染
                setSideRowCount(mainRows.length);

                if (mainRows.length !== sideRows.length) return; // data 已同步，长度不一致则略过本次
                sideRows.forEach((row, idx) => {
                    const mainRow = mainRows[idx] as HTMLElement;
                    const h = mainRow.getBoundingClientRect().height;
                    (row as HTMLElement).style.height = `${h}px`;
                });
            };

            const raf1 = requestAnimationFrame(() => {
                const raf2 = requestAnimationFrame(measure);
                // 存一个引用避免 lint 报错
                (window as any).__raf2 = raf2;
            });

            return () => {
                cancelAnimationFrame(raf1);
                if ((window as any).__raf2) cancelAnimationFrame((window as any).__raf2);
            };
        }, [data, loading]);

        // 添加窗口大小变化监听器，确保在浏览器缩放时也能同步行高
        useEffect(() => {
            const handleResize = () => {
                const mainWrap = mainTableWrapRef.current;
                const sideWrap = sideTableWrapRef.current;
                if (!mainWrap || !sideWrap) return;

                const mainRows = mainWrap.querySelectorAll('.ant-table-tbody > tr.ant-table-row');
                const sideRows = sideWrap.querySelectorAll('.ant-table-tbody > tr.ant-table-row');
                
                if (mainRows.length === sideRows.length) {
                    requestAnimationFrame(() => {
                        mainRows.forEach((row, idx) => {
                            const mainRow = row as HTMLElement;
                            const sideRow = sideRows[idx] as HTMLElement;
                            const h = mainRow.getBoundingClientRect().height;
                            sideRow.style.height = `${h}px`;
                        });
                    });
                }
            };

            window.addEventListener('resize', handleResize);
            // 组件挂载后也执行一次同步
            handleResize();
            
            return () => {
                window.removeEventListener('resize', handleResize);
            };
        }, [data, loading]);

        // 根据主表可视行数构造右侧数据：
        // 始终以主表当前可视行数 sideRowCount 生成占位数据，保证左右行数一致
        const sideData = useMemo(() => {
            if (sideRowCount > 0) return Array.from({ length: sideRowCount }, (_, i) => ({ id: `__blank_${i}` }));
            return [] as any[];
        }, [sideRowCount]);

        return (
            <div>
                <Flex vertical>
                    <Flex gap="middle" style={{ marginBottom: "20px" } as React.CSSProperties}>
                        {extraButton}
                        <div style={{ flexGrow: 1 }} />
                        {searchFieldsList ? <GlobalSearchInput onChange={handleSearchChange} /> : null}
                        {helpDraw}
                    </Flex>

                    {(() => {
                        const TableComponent = resizableColumns ? ResizableTable : Table;

                        const tableProps = {
                            rowSelection: rowSelection ? tableRowSelection : null,
                            columns: finalColumns,
                            bordered: true,
                            rowKey: (record: any) => record.id,
                            loading,
                            dataSource: data,
                            pagination: searchFieldsList || isShowPagination ? pagination : false,
                            onChange: tableChange,
                            scroll: { x: 'max-content' },
                            ...(resizableColumns ? { resizableColumns: true } : {}),
                            ...props
                        } as any;

                        if (!showColumnSelector) {
                            return <TableComponent {...tableProps} />;
                        }

                        // 带右侧拼接表格的布局
                        const sideColumns = [
                            {
                                title: (
                                    <div style={{ width: '100%', height: '54px', display: 'flex', alignItems: 'center', justifyContent: 'center', padding: 0 }}>
                                        <ColumnSelector
                                            columns={columns}
                                            visibleColumns={visibleColumns}
                                            onChange={handleVisibleColumnsChange}
                                            draggable={columnsOrder}
                                            onDragEnd={handleColumnOrderChange}
                                        />
                                    </div>
                                ),
                                dataIndex: '__column_selector__',
                                key: 'columnSelector',
                                width: 52,
                                align: 'center' as const,
                                render: () => null,
                                onHeaderCell: () => ({
                                    style: {
                                        width: '50px',
                                        padding: 0,
                                        height: '54px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    }
                                })
                            }
                        ];

                        return (
                            <div style={{ display: 'flex' }}>
                                <div 
                                  ref={mainTableWrapRef} 
                                  className={mainTableClass} 
                                  style={{ 
                                    flex: 1, 
                                    minWidth: 0, 
                                    display: 'flex', 
                                    flexDirection: 'column'
                                  }}
                                >
                                    <TableComponent
                                        {...tableProps}
                                        bordered={data.length > 0}
                                        style={{ flex: 1 }}
                                    />
                                </div>

                                {/* 隐藏右侧表滚动条（尤其是底部横向滚动条） */}
                                <style>{`
                                  /* 主表：去掉右上角圆角，并移除右边框以与右侧表无缝拼接 */
                                  .${mainTableClass} .ant-table-container {
                                    border-top-right-radius: 0 !important; /* 旧表格右上角直角 */
                                  }
                                  .${mainTableClass} .ant-table-header {
                                    border-top-right-radius: 0 !important;
                                  }
                                  .${mainTableClass} .ant-table-content {
                                    border-top-right-radius: 0 !important;
                                  }

                                  /* 右表：隐藏横向滚动、移除左边框，保留右边框可见 */
                                  .${sideTableClass} { 
                                    position: relative; 
                                    z-index: 1; 
                                    /* 添加这一行确保即使在无数据时容器也有最小高度 */
                                    min-height: 54px;
                                  }
                                  .${mainTableClass} { position: relative; z-index: 0; }
                                  .${sideTableClass} .ant-table-container,
                                  .${sideTableClass} .ant-table-content {
                                    overflow-x: hidden !important; /* 彻底隐藏横向滚动 */
                                  }
                                  .${sideTableClass} .ant-table-container {
                                    /* 仅移除左边框，其他边框全部使用 AntD 原生，保持样式与旧表一致 */
                                    border-left: none !important;
                                    border-top-left-radius: 0 !important; /* 左上角直角 */
                                    border-bottom-left-radius: 0 !important; /* 左下角直角 */
                                    background: #fff; /* 与主表一致，避免边界色被背景干扰 */
                                  }
                                  .${sideTableClass} .ant-table-header,
                                  .${sideTableClass} .ant-table-content,
                                  .${sideTableClass} .ant-table-body {
                                    border-top-left-radius: 0 !important;
                                  }
                                  /* 侧表最外层容器：确保边框以内布局与背景一致，边界可见 */
                                  .${sideTableClass} {
                                    box-sizing: border-box;
                                    background: #fff;
                                    /* 确保即使在没有数据的情况下，容器也有边框 */
                                    border-right: 1px solid var(--ant-color-border) !important;
                                    border-bottom: 1px solid var(--ant-color-border) !important;
                                  }
                                  /* 进一步确保侧表所有层级左侧圆角归零 */
                                  .${sideTableClass} .ant-table,
                                  .${sideTableClass} .ant-table-container,
                                  .${sideTableClass} .ant-table-header,
                                  .${sideTableClass} .ant-table-content {
                                    border-top-left-radius: 0 !important;
                                    border-bottom-left-radius: 0 !important;
                                  }
                                  .${sideTableClass} .ant-table-thead > tr > th:first-child {
                                    border-top-left-radius: 0 !important; /* 表头第一列左上角直角 */
                                  }
                                  .${sideTableClass} .ant-table-tbody > tr > td:first-child {
                                    border-bottom-left-radius: 0 !important; /* 表体第一列左下角直角（防主题圆角） */
                                  }
                                  /* 主表右上角直角，确保与侧表左上角贴合 */
                                  .${mainTableClass} .ant-table-container {
                                    border-top-right-radius: 0 !important;
                                    border-bottom-right-radius: 0 !important;
                                  }
                                  .${mainTableClass} .ant-table-header,
                                  .${mainTableClass} .ant-table-content {
                                    border-top-right-radius: 0 !important;
                                  }
                                  .${mainTableClass} .ant-table-thead > tr > th:last-child {
                                    border-top-right-radius: 0 !important;
                                  }
                                  .${mainTableClass} .ant-table-tbody > tr > td:last-child {
                                    border-bottom-right-radius: 0 !important;
                                  }
                                  .${mainTableClass} .ant-table-scroll-horizontal {
                                    border-top-right-radius: 0 !important;
                                  }
                                  .${mainTableClass} .ant-table-body {
                                    border-top-right-radius: 0 !important;
                                  }
                                  /* 去除右侧表头在无数据时出现的垂直滚动条 */
                                  /* 侧表容器最小高度与主表表头等高（常见为 54px），无数据时保证底边可见 */
                                  .${sideTableClass} .ant-table-container { overflow: hidden !important; min-height: 54px; background: #fff; }
                                  .${sideTableClass} .ant-table-header { overflow: hidden !important; }
                                  .${sideTableClass} .ant-table-content { overflow: hidden !important; }
                                  .${sideTableClass} .ant-table-header .ant-table-sticky-holder { overflow: hidden !important; }
                                  .${sideTableClass} .ant-table-sticky-scroll,
                                  .${sideTableClass} .ant-table-sticky-scroll-bar { display: none !important; }
                                  .${sideTableClass} .ant-table-header::-webkit-scrollbar { width: 0; height: 0; }
                                  .${sideTableClass} .ant-table-content::-webkit-scrollbar { width: 0; height: 0; }
                                  /*
                                  /* 显式补齐侧表的右、下边框，避免无数据时边界缺失（与 AntD 原生边框色一致） */
                                  .${sideTableClass} .ant-table-container {
                                    border-right: 1px solid var(--ant-color-border) !important;
                                    border-bottom: 1px solid var(--ant-color-border) !important;
                                  }
                                  .${sideTableClass} .ant-table {
                                    border-right: 1px solid var(--ant-color-border) !important;
                                    border-bottom: 1px solid var(--ant-color-border) !important;
                                  }
                                  .${sideTableClass} {
                                    border-right: 1px solid var(--ant-color-border) !important;
                                    border-bottom: 1px solid var(--ant-color-border) !important;
                                  }
                                `}</style>

                                <div
                                  ref={sideTableWrapRef}
                                  className={sideTableClass}
                                  style={{
                                    width: 50,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    borderLeft: 'none',
                                    borderColor: data.length === 0 ? 'transparent' : undefined
                                  }}
                                >
                                    <Table
                                        rowKey={(record: any) => record.id}
                                        columns={sideColumns as any}
                                        dataSource={sideData}
                                        loading={loading}
                                        pagination={false}
                                        locale={{ 
                                            emptyText:  
                                                <div style={{ 
                                                    opacity: 0,
                                                    height: '153.5px',
                                                    width: '100%',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center'
                                                }} />
                                        }}
                                        bordered={false}
                                        style={{ 
                                            flex: 1
                                        }}
                                        scroll={{ 
                                            x: 'max-content',
                                            y: undefined
                                        }}
                                    />
                                </div>
                            </div>
                        );
                    })()}
                </Flex>
            </div>
        );
    }
);
