import React, { ReactNode } from 'react';
import { Form, Button, Modal, Divider } from 'antd';
import '@/modules-smb/Wireless/assets/form.scss';

export interface FormModalProps {
  open: boolean;
  title?: string;
  onCancel?: () => void;
  onFinish?: (values: any) => void;
  initialValues?: any;
  modalClass?: string;
  form?: any;
  onValuesChange?: (changedValues: any, allValues: any) => void;
  children?: ReactNode;
}

export const FormModal: React.FC<FormModalProps> = ({
  open,
  title,
  onCancel,
  onFinish,
  initialValues,
  modalClass = "ampcon-max-modal",
  form,
  onValuesChange,
  children
}) => {
  if (!open) return null;
  if (!form) {
    [form] = Form.useForm();
  }

  return (
    <Modal
      open={open}
      title={
        <div>
            {title}
            <Divider style={{ marginTop: 8, marginBottom: 0 }} />
        </div>
      }
      onCancel={onCancel}
      footer={
        <>
          <Divider style={{ marginTop: 0, marginBottom: 20 }} />
          <Button key="cancel" onClick={onCancel}>
            Cancel
          </Button>
          <Button key="ok" type="primary" onClick={() => form?.submit()}>
            Apply
          </Button>
        </>
      }
      destroyOnClose
      className={`wirelessModal${modalClass ? ' ' + modalClass : ''}`}
    >
      <Form
        form={form}
        initialValues={initialValues}
        onFinish={onFinish}
        onValuesChange={onValuesChange}
        className="wirelessForm"
      >
        {children}
      </Form>
    </Modal>
  );
};
