import React ,{useEffect}from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, Button, Modal, Typography,Divider } from "antd";
import { useTranslation } from "react-i18next";
import iconDetails from "@/modules-smb/Wireless/assets/Dashboard/icon_details.png";
import hoverIconDetails from "@/modules-smb/Wireless/assets/Dashboard/hover_icon_details.png";
import iconEnlarge from "@/modules-smb/Wireless/assets/Dashboard/icon_enlarge.png";
import hoverIconEnlarge from "@/modules-smb/Wireless/assets/Dashboard/hover_icon_enlarge.png";
const { Title, Paragraph } = Typography;

type Props = {
  chart: React.ReactNode;
  explanation: string;
  title: string;
};

const GraphStatDisplay: React.FC<Props> = ({ chart, title, explanation }) => {
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [hovered, setHovered] = React.useState(false);
  const [detailHovered,setDetailHovered] = React.useState(false);

  const handleOpenModal = () => setIsModalOpen(true);
  const handleCloseModal = () => setIsModalOpen(false);
  const renderChart = (isModal: boolean) => {
    // 如果 chart 是个函数，就传递 isModal，否则原样返回
    if (typeof chart === 'function') {
      return chart(isModal);
    }
    return chart;
  };

  //在 Modal 打开后手动触发 resize
  useEffect(() => {
    if (isModalOpen) {
      setTimeout(() => {
        window.dispatchEvent(new Event("resize"));
      }, 100); // 延迟一点，确保 DOM 渲染完成
    }
  }, [isModalOpen]);
  return (
    <>
      <Card
        title={
          <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
            <Title level={5} style={{ margin: 0 }}>
              {title}
            </Title>
            <Tooltip title={explanation}>
              {/* <InfoCircleOutlined /> */}
              <img src={detailHovered ? hoverIconDetails:iconDetails} style={{marginLeft: -4,width: 10, height: 10}} 
              onMouseEnter={() => setDetailHovered(true)}
              onMouseLeave={() => setDetailHovered(false)}
              />
            </Tooltip>
          </div>
        }
        extra={
          <Tooltip>
            <Button
              type="text"
               icon={<img src={hovered ? hoverIconEnlarge : iconEnlarge} style={{ width: 20, height: 20 }} />}
               onClick={handleOpenModal}
               style={{ backgroundColor: "transparent" }}
               onMouseEnter={(e) => {
                 setHovered(true);
                 e.currentTarget.style.backgroundColor = "transparent";
               }}
               onMouseLeave={(e) => {
                 setHovered(false);
                 e.currentTarget.style.backgroundColor = "transparent";
               }}
            />
          </Tooltip>
        }
        style={{ width: "100%", height: "100%" }} 
        bordered={false}
      >
    {renderChart(false)}
      </Card>

      <Modal
        title={typeof title === "string" ? (
                        <div>
                            {title}
                            <Divider style={{ margin: '8px -24px 24px -24px',width: '680px' }} />
                        </div>
                    ) : (
                        title
                    )}
        open={isModalOpen}
        onCancel={handleCloseModal}
        footer={null}
        width={680}
        styles={{
        content: {
          height: 450,         // 整体最小高度 = 450（包含头/体/尾）
        },
        body: {
          display: "flex",
          flexDirection: "column",
        },
  }}
      
      >
        <Paragraph type="secondary" style={{ marginBottom: 16 }}>
          {explanation}
        </Paragraph>
        <div>{renderChart(true)}</div>
      </Modal>
    </>
  );
};

export default GraphStatDisplay;
