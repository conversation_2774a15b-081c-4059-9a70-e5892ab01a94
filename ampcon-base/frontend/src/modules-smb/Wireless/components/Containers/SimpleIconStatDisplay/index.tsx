import React from "react";
import { Card, Tooltip,Typography } from "antd";
import styles from "./SimpleIconStatDisplay.module.scss";
import iconDetails from "@/modules-smb/Wireless/assets/Dashboard/icon_details.png";
import hoverIconDetails from "@/modules-smb/Wireless/assets/Dashboard/hover_icon_details.png";
const { Title} = Typography;
interface SimpleStatDisplayProps {
  title: string;
  description: string;
  value: string | number | React.ReactNode;
  icon: React.ReactNode;
  color?: string; // optional: icon background color
}

const SimpleIconStatDisplay: React.FC<SimpleStatDisplayProps> = ({
  title,
  description,
  value,
  icon,
  // color = "#1677ff", // 默认蓝色
}) => {
   const [detailHovered,setDetailHovered] = React.useState(false);
  return (
    <Card className={styles.statCard} bordered={false} 
          title={
          <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
            <Title level={5} style={{ margin: 0 }}>
              {title}
            </Title>
            <Tooltip title={description}>
              <img src={detailHovered ? hoverIconDetails:iconDetails} style={{marginLeft: -4,width: 10, height: 10}} 
              onMouseEnter={() => setDetailHovered(true)}
              onMouseLeave={() => setDetailHovered(false)}
              />
            </Tooltip>
          </div>
          }
    >
      <div className={styles.statCardContent}>
        <div className={styles.value}>{value}</div>
        <div>
          {icon}
        </div>
      </div>
    </Card>
  );
};

export default SimpleIconStatDisplay;

