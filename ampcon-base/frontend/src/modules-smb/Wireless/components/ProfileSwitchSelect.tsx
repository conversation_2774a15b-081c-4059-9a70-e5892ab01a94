import React, { useState, useEffect } from 'react';
import { Form, Select, Input, Button, message, Modal, Switch, Flex, Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import { PlusOutlined } from '@ant-design/icons';
import { useNavigate } from "react-router-dom";
import { getWirelessProfileList } from '@/modules-smb/Wireless/apis/wireless_profile_api';
import RadiusForm from '@/modules-smb/Wireless/pages/Profile/SSIDRadius/RadiusForm/Form';
import WebrootForm from '@/modules-smb/Wireless/pages/Profile/CaptivePortal/WebrootForm/Form';
import FullPageMPSKForm from '@/modules-smb/Wireless/pages/Profile/MPSKUser/MPSKUserForm';
import TimeRangeModal from '@/modules-smb/Wireless/pages/Profile/TimeRange/TimeRangeModal';

interface ProfileSwitchSelectProps {
  label: string;
  formName: string | string[];
  switchEnabled: boolean;
  onSwitchChange: (checked: boolean) => void;
  type: number;
  siteId: number;
  edit?: string;
  mode?: string;
  title?: string;
  onProfileChange?: (profile: any) => void;
  enableName?: string | string[];
  selectName?: string | string[];
}

// profile初始化配置
const PROFILE_CONFIG = {
  1: { // Radius
    modalComponent: RadiusForm,
    navigateUrl: "/wireless/profile/SSIDRadius",
  },
  2: { // Multi Psk
    modalComponent: FullPageMPSKForm,
    navigateUrl: "/wireless/profile/MPSKUser",
  },
  3: { // Captive Portal
    modalComponent: WebrootForm,
    navigateUrl: "/wireless/profile/CaptivePortal",
  },
  4: { // Schedule
    modalComponent: TimeRangeModal,
    navigateUrl: "/wireless/profile/TimeRange",
  },
};

const ProfileSwitchSelect: React.FC<ProfileSwitchSelectProps> = ({
  label,
  formName,
  switchEnabled,
  onSwitchChange,
  type,
  siteId,
  edit,
  mode,
  title,
  onProfileChange,
  enableName,
  selectName,
}) => {
  const { t } = useTranslation();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [profiles, setProfiles] = useState<any[]>([]);
  const form = Form.useFormInstance();
  const navigate = useNavigate();
  const [parameterMode, setParameterMode] = useState<string | undefined>(undefined);
  const [isNewAdd, setIsNewAdd] = useState(false);

  // 从配置字典获取配置
  const config = PROFILE_CONFIG[type as keyof typeof PROFILE_CONFIG];
  const ModalComponent = config.modalComponent;

  const loadProfiles = async (selectLast: boolean = false) => {
    try {
      let res;
      let nextParameterMode: string | undefined = undefined;
      // werboot过滤条件
      if (mode) {
        if (mode === 'radius') {
          nextParameterMode = 'Radius';
        } else if (mode === 'credentials') {
          nextParameterMode = 'Credentials';
        } else {
          nextParameterMode = 'Click';
        }
        setParameterMode(nextParameterMode);
        const parameterFilter = [{ field: 'mode', value: nextParameterMode }];
        res = await getWirelessProfileList(type, siteId, 1, 1000, [], [{ field: "id", order: "asc" }], parameterFilter);
      } else {
        setParameterMode(undefined);
        res = await getWirelessProfileList(type, siteId, 1, 1000, [], [{ field: "id", order: "asc" }]);
      }
      if (res?.status === 200) {
        setProfiles(res.info || []);
        // 新增成功选中最后一个
        if (selectLast) {
          if (edit) {
            setIsNewAdd(true);
          } else {
            const lastProfile = res.info[res.info.length - 1];
            if (lastProfile) {
              handleSelectChange(lastProfile.variable_id);
            }
          }
        }
      } else {
        message.error(res?.info || 'fetch profile list fail');
      }
    } catch (error) {
      if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error('An unknown error occurred');
      }
    }
  };

  useEffect(() => {
    if (switchEnabled) {
      loadProfiles();
      // 切换清空已选择值
      form.setFieldValue(formName, undefined);
      if (onProfileChange) onProfileChange(undefined);
    }
  }, [switchEnabled, mode, siteId]);

  // edit有值时自动选中
  useEffect(() => {
    if (!edit) return;
    if (profiles.length === 0) return;
    if (!switchEnabled) {
      onSwitchChange(true);
    }
    if (isNewAdd) {
      const lastProfile = profiles[profiles.length - 1];
      handleSelectChange(lastProfile.variable_id);
      setIsNewAdd(false);
    } else {
      const selected = profiles.find(p => p.variable_id === edit);
      if (selected) {
        form.setFieldValue(formName, edit);
      }
      if (onProfileChange) {
        onProfileChange(selected);
      }
    }
    return;
  }, [edit, profiles, type]);

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
  };

  const handleModalOk = (success?: boolean) => {
    setIsModalVisible(false);
    loadProfiles(success);
  };

  const handleSwitchChange = (checked: boolean) => {
    onSwitchChange(checked);
    if (!checked && form) {
      form.setFieldValue(formName, undefined);
      if (selectName) {
        form.setFieldValue(selectName, undefined);
      }
    }
  };

  const handleSelectChange = (value: any) => {
    form.setFieldValue(formName, value);
    const selected = profiles.find(p => p.variable_id === value);
    if (selectName) {
      form.setFieldValue(selectName, selected ? selected.name : value);
    }
    if (onProfileChange) {
      onProfileChange(selected);
    }
  };

  return (
    <>
      <Form.Item
        label={label}
        style={{ marginBottom: 10 }}
        name={enableName ? enableName : undefined}
        valuePropName={enableName ? undefined : "checked"}
        getValueFromEvent={enableName ? (v) => v ? 1 : 0 : undefined}
      >
        <Switch
          checked={switchEnabled}
          onChange={handleSwitchChange}
        />
      </Form.Item>
      {selectName && (
        <Form.Item name={selectName} style={{ display: 'none' }}><Input type="hidden" /></Form.Item>
      )}
      {switchEnabled && (
        <Flex>
          <Form.Item
            name={formName}
            label=" "
            required={false}
            rules={[{ required: true, message: t('form.required') }]}
          >
            <Select
              dropdownRender={menu => (
                <>
                  {menu}
                  <Button type="link" icon={<PlusOutlined />} onClick={showModal} style={{ margin: '4px 0px 0px -24px', width: 'calc(100% + 48px)', borderTop: '1px solid #E7E7E7' }}>
                    Create {title ? title : label} Profile
                  </Button>
                </>
              )}
              onChange={handleSelectChange}
            >
              {profiles.map(profile => (
                <Select.Option
                  key={profile.variable_id}
                  value={profile.variable_id}
                >
                  {profile.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Button
            type="text"
            style={{ backgroundColor: '#fff' }}
            onClick={() => navigate(config.navigateUrl + '#' + siteId)}
          >
            Manage {title ? title : label} Profile
          </Button>
        </Flex>
      )}
      {/* 新增模态框 */}
      {type !== 2 ? (
        <ModalComponent onClose={handleModalOk} siteId={siteId}
          key={Date.now()}
          {...(type !== 4 ? { open: isModalVisible } : { visible: isModalVisible })}
          {...(parameterMode ? { parameterMode: parameterMode } : { })}
        />
      ) : (
        <Modal
          title={
            <div>
                {`Create ${title ? title : label} Profile`}
                <Divider style={{ marginTop: 8, marginBottom: 0 }} />
            </div>
          }
          open={isModalVisible}
          onCancel={handleModalCancel}
          footer={null}
          destroyOnClose
          className='ampcon-max-modal'
        >
          {ModalComponent && <ModalComponent key={Date.now()} onClose={handleModalOk} siteId={siteId} 
            {...(parameterMode ? { parameterMode: parameterMode } : { })}
          />}
        </Modal>
      )}
    </>
  );
};

export default ProfileSwitchSelect;