import React, { useState, useEffect } from 'react';
import { Form, Select, Button, message, Flex } from 'antd';
import { useTranslation } from 'react-i18next';
import { PlusOutlined } from '@ant-design/icons';
import { useNavigate } from "react-router-dom";
import { getWirelessProfileList } from '@/modules-smb/Wireless/apis/wireless_profile_api';
import RadiusForm from '@/modules-smb/Wireless/pages/Profile/SSIDRadius/RadiusForm/Form';

interface ProfileSelectProps {
  label: string;
  formName: string | string[];
  type: number;
  siteId: number;
  edit?: string;
  title?: string;
  onProfileChange?: (profile: any) => void;
  proto?: string;
}

// profile初始化配置
const PROFILE_CONFIG = {
  1: { // Radius
    modalComponent: RadiusForm,
    navigateUrl: "/wireless/profile/SSIDRadius",
  }
};

const ProfileSelect: React.FC<ProfileSelectProps> = ({
  label,
  formName,
  type,
  siteId,
  edit,
  title,
  onProfileChange,
  proto,
}) => {
  const { t } = useTranslation();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [profiles, setProfiles] = useState<any[]>([]);
  const form = Form.useFormInstance();
  const navigate = useNavigate();
  const [isNewAdd, setIsNewAdd] = useState(false);

  // 从配置字典获取配置
  const config = PROFILE_CONFIG[type as keyof typeof PROFILE_CONFIG];
  const ModalComponent = config.modalComponent;

  const loadProfiles = async (selectLast: boolean = false) => {
    try {
      const parameterFilter = [];
      // radius特殊处理：只有当 proto 为 'psk2-radius' 时才过滤 External
      if (proto === 'psk2-radius') {
        parameterFilter.push({ field: "type", value: "External" });
      }
      const res = await getWirelessProfileList(type, siteId, 1, 1000, [], [{ field: "id", order: "asc" }], parameterFilter);

      if (res?.status === 200) {
        setProfiles(res.info || []);
        // 新增成功选中最后一个
        if (selectLast) {
          if (edit) {
            setIsNewAdd(true);
          } else {
            const lastProfile = res.info[res.info.length - 1];
            if (lastProfile) {
              handleSelectChange(lastProfile.variable_id);
            }
          }
        }
      } else {
        message.error(res?.info || 'fetch profile list fail');
      }
    } catch (error) {
      if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error('An unknown error occurred');
      }
    }
  };
  // edit有值时自动选中
  useEffect(() => {
    if (!edit) return;
    if (profiles.length === 0) return;
    
    if (isNewAdd) {
      const lastProfile = profiles[profiles.length - 1];
      handleSelectChange(lastProfile.variable_id);
      setIsNewAdd(false);
    } else {
      const selected = profiles.find(p => p.variable_id === edit);
      if (selected) {
        form.setFieldValue(formName, edit);
      }
      if (onProfileChange) {
        onProfileChange(selected);
      }
    }
    return;
  }, [edit, profiles, type]);

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleModalOk = (success?: boolean) => {
    setIsModalVisible(false);
    loadProfiles(success);
  };

  const handleSelectChange = (value: any) => {
    form.setFieldValue(formName, value);
    const selected = profiles.find(p => p.variable_id === value);
    if (onProfileChange) {
      onProfileChange(selected);
    }
  };

  useEffect(() => {
    loadProfiles();
    // 切换清空已选择值
    form.setFieldValue(formName, undefined);
    if (onProfileChange) onProfileChange(undefined);
  }, [siteId, proto]);

  return (
    <> 
      <Flex>
        <Form.Item
          name={formName}
          label={label}
          rules={[{ required: true, message: t('form.required') }]}
        >
          <Select
            dropdownRender={menu => (
              <>
                {menu}
                <Button type="link" icon={<PlusOutlined />} onClick={showModal} style={{ width: '100%', borderTop: '1px solid #E7E7E7'}}>
                  Create {title ? title : label} Profile
                </Button>
              </>
            )}
            onChange={handleSelectChange}
          >
            {profiles.map(profile => (
              <Select.Option
                key={profile.variable_id}
                value={profile.variable_id}
              >
                {profile.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Button
          type="text"
          style={{ backgroundColor: '#fff' }}
          onClick={() => navigate(config.navigateUrl + '#' + siteId)}
        >
          Manage {title ? title : label} Profile
        </Button>
      </Flex>

      {/* 新增模态框 */}
      <ModalComponent onClose={handleModalOk} siteId={siteId}
        key={Date.now()}
        {...(proto === 'psk2-radius' ? { disableMode: true } : {})}
        {...(type !== 4 ? { open: isModalVisible } : { visible: isModalVisible })}
      />

    </>
  );
};

export default ProfileSelect;