import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useProviderDeviceSearch } from '@/modules-smb/contexts/ProvisioningSocketProvider/hooks/Commands/useDeviceSearch';
import { SearchOutlined } from '@ant-design/icons';
import { Input, Dropdown, Menu, Typography } from "antd";

const { Title } = Typography; 

interface Props {
  onClick: (id: string) => void;
  isDisabled?: boolean;
}

const DeviceSearchBar = ({ onClick, isDisabled }: Props) => {
  const { t } = useTranslation();
  const { inputValue, results, onInputChange, isOpen, resetSearch } = useProviderDeviceSearch({
    minLength: 2,
  });
 
  const [searchValue, setSearchValue] = useState(inputValue);

  // 无搜索结果时的提示组件
  const NoOptionsMessage = useCallback(
    () => (
      <Title 
        level={5} 
        style={{ 
          textAlign: 'center',
          padding: '8px 0',
          margin: 0,   
        }}
      >
        {t('common.no_devices_found')}
      </Title>
    ),
    [t]
  );

  const changeDevice = (value: string) => {
    resetSearch();
    onClick(value);
  };

  const onChange = (value: string) => {
    setSearchValue(value);
    if (value.length === 0 || value.match('^[a-fA-F0-9-*]+$')) {
      onInputChange(value);
    }
  };

  return (
    <Dropdown
      trigger={['click']}
      overlay={
        <Menu>
          {results.length > 0 ? (
            results.map((v) => (
              <Menu.Item key={v} onClick={() => changeDevice(v)}>
                {v}
              </Menu.Item>
            ))
          ) : (
            <Menu.Item disabled style={{ border: 'none', padding: 0 }}>
              <NoOptionsMessage />
            </Menu.Item>
          )}
        </Menu>
      }
    >
      <Input
        placeholder={t("common.search")}
        value={searchValue}
        onChange={(e) => onChange(e.target.value)}
        prefix={<SearchOutlined style={{ color: "rgba(0,0,0,.25)" }} />}
      />
    </Dropdown>
  );
};

export default DeviceSearchBar;