import React from 'react';
import { Select, Form } from 'antd';
import PropTypes from 'prop-types';
import isEqual from 'react-fast-compare';
import { useTranslation } from 'react-i18next';
import ConfigurationFieldExplanation from '../ConfigurationFieldExplanation';

const propTypes = {
  value: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])),
  label: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    }),
  ).isRequired,
  onBlur: PropTypes.func.isRequired,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  touched: PropTypes.bool,
  isDisabled: PropTypes.bool,
  canSelectAll: PropTypes.bool,
  isRequired: PropTypes.bool,
  isHidden: PropTypes.bool,
  isPortal: PropTypes.bool.isRequired,
  definitionKey: PropTypes.string,
  w: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  placeholder: PropTypes.string,
};

// const defaultProps = {
//   value: [],
//   error: false,
//   touched: false,
//   isRequired: false,
//   canSelectAll: false,
//   isDisabled: false,
//   isHidden: false,
//   definitionKey: null,
// };

const FastMultiSelectInput = ({
  options,
  label,
  value=[],
  onChange,
  onBlur,
  error=false,
  touched=false,
  canSelectAll=false,
  isRequired=false,
  isDisabled=false,
  isHidden=false,
  isPortal=false,
  definitionKey=null,
  w,
  placeholder,
}) => {
  const { t } = useTranslation();

  const fullOptions = canSelectAll
    ? [{ value: '*', label: t('common.all') }, ...options]
    : options;

  return (
    <Form.Item
      label={
        !isHidden && (
          <div style={{ display: 'flex', alignItems: 'center', gap: 2 ,width:'180px'}}>
           <span>{label}</span>
           <ConfigurationFieldExplanation definitionKey={definitionKey} />
           {isRequired && <span 
                  style={{ color: "#ff4d4f",
                  fontFamily: "SimSun, sans-serif",
                  marginLeft: "2px",
                  marginRight: "4px",
                  display: "inline-block"}}
                  >*</span>}
          </div>
        )
      }
      validateStatus={error && touched ? 'error' : ''}
      help={error && touched ? error : null}
      // required={isRequired}
      hidden={isHidden}
    >
      <Select
        mode="multiple"
        allowClear
        placeholder={placeholder} 
        value={value ?? []}
        options={fullOptions}
        onChange={onChange}
        onBlur={onBlur}
        disabled={isDisabled}
        getPopupContainer={(trigger) => trigger.parentElement || document.body}
        style={{ width: typeof w === 'number' ? `${w}px` : w || '100%' }}
      />
    </Form.Item>
  );
};

FastMultiSelectInput.propTypes = propTypes;
// FastMultiSelectInput.defaultProps = defaultProps;

export default React.memo(FastMultiSelectInput, isEqual);
