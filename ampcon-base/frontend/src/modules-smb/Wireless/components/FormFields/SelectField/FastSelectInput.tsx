import React from 'react';
import { Form, Select } from 'antd';
import isEqual from 'react-fast-compare';
import { v4 as uuid } from 'uuid';
import ConfigurationFieldExplanation from '../ConfigurationFieldExplanation';
import { defaultFormInput, FormInputProps } from '@/modules-smb/models/FormField';

const { Option } = Select;

interface Props extends FormInputProps {
  value?: string;
  options: { label: string; value: string | number }[];
  dropdownRender?: (menu: React.ReactNode) => React.ReactNode;
  onDropdownVisibleChange?: (open: boolean) => void;
}

// const defaultProps = {
//   value: '',
//   ...defaultFormInput,
// };

const FastSelectInput: React.FC<Props> = ({
  options,
  label,
  value ='',
  onChange,
  onBlur,
  error = undefined,
  touched = false,
  isRequired = false,
  isDisabled = false,
  isHidden = false,
  isLabelHidden = false,
  w = undefined,
  definitionKey = undefined,
  dropdownRender,
  onDropdownVisibleChange,
}) => {
  if (isHidden) return null;

  return (
    <Form.Item
      label={
        !isLabelHidden && (
          <div style={{ display: 'flex', alignItems: 'center', gap: 2 ,width:'180px'}}>
             <span>{label}</span>
             <ConfigurationFieldExplanation definitionKey={definitionKey} />
             {isRequired && <span 
                  style={{ color: "#ff4d4f",
                  fontFamily: "SimSun, sans-serif",
                  marginLeft: "2px",
                  marginRight: "4px",
                  display: "inline-block"}}
                  >*</span>}
          </div>
        )
      }
      validateStatus={error && touched ? 'error' : ''}
      help={error && touched ? error : null}
      // required={isRequired}
      // style={w ? { width: w } : {}}
    >
      <Select
        value={value}
        onChange={(val) => onChange?.({ target: { value: val } })}
        onBlur={onBlur}
        disabled={isDisabled}
        style={{ borderRadius: 8 ,width: typeof w === 'number' ? `${w}px` : w || '100%'}}
        // dropdownMatchSelectWidth
        popupMatchSelectWidth
        dropdownRender={dropdownRender}
        onDropdownVisibleChange={onDropdownVisibleChange}
      >
        {options.map((option) => (
          <Option value={option.value} key={uuid()}>
            {option.label}
          </Option>
        ))}
      </Select>
    </Form.Item>
  );
};

// FastSelectInput.defaultProps = defaultProps;

export default React.memo(FastSelectInput, isEqual);
