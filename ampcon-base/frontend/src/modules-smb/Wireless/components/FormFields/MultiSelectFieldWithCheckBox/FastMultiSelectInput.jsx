import React ,{useState,useEffect} from 'react';
import { Select, Form,Checkbox,Divider,Button, Input } from 'antd';
import PropTypes from 'prop-types';
import isEqual from 'react-fast-compare';
import { useTranslation } from 'react-i18next';
import ConfigurationFieldExplanation from '../ConfigurationFieldExplanation';
import { AudioOutlined,SearchOutlined } from '@ant-design/icons';

const propTypes = {
  value: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])),
  label: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    }),
  ).isRequired,
  onBlur: PropTypes.func.isRequired,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  touched: PropTypes.bool,
  isDisabled: PropTypes.bool,
  canSelectAll: PropTypes.bool,
  isRequired: PropTypes.bool,
  isHidden: PropTypes.bool,
  isPortal: PropTypes.bool.isRequired,
  definitionKey: PropTypes.string,
  w: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

const defaultProps = {
  value: [],
  error: false,
  touched: false,
  isRequired: false,
  canSelectAll: false,
  isDisabled: false,
  isHidden: false,
  definitionKey: null,
};

const FastMultiSelectInput = ({
  options,
  label,
  value,
  onChange,
  onBlur,
  error,
  touched,
  canSelectAll,
  isRequired,
  isDisabled,
  isHidden,
  isPortal,
  definitionKey,
  w,
}) => {
  const { t } = useTranslation();
 


  const [open, setOpen] = useState(false);
  const [tempSelected, setTempSelected] = useState(value ?? []);
  const [searchValue, setSearchValue] = useState('');
  const isAllSelected = tempSelected.includes('LAN*');
  const fullOptions = canSelectAll
    ? [{ value: '*', label: t('common.all') }, ...options]
    : options;

  const filteredOptions = fullOptions.filter(opt => 
  opt.label.toLowerCase().includes(searchValue.toLowerCase()) ||
  opt.value.toString().toLowerCase().includes(searchValue.toLowerCase())
  );

    // 同步外部传进来的值
  useEffect(() => {
    setTempSelected(value ?? []);
  }, [value]);

  const handleOk = () => {
    onChange(tempSelected);
    setOpen(false);
    setSearchValue(''); // 清空搜索值
  };

  const handleReset = () => {
    setTempSelected([]);
    onChange([]);
    setOpen(false);
    setSearchValue(''); // 清空搜索值
  };
   // 添加搜索处理函数
  const handleSearchChange = (e) => {
    setSearchValue(e.target.value);
  };
  return (
    <Form.Item
      label={
        !isHidden && (
          <div style={{ display: 'flex', alignItems: 'center', gap: 2 ,width:'180px'}}>
           <span>{label}</span>
           <ConfigurationFieldExplanation definitionKey={definitionKey} />
           {isRequired && <span 
                  style={{ color: "#ff4d4f",
                  fontFamily: "SimSun, sans-serif",
                  marginLeft: "2px",
                  marginRight: "4px",
                  display: "inline-block"}}
                  >*</span>}
          </div>
        )
      }
      validateStatus={error && touched ? 'error' : ''}
      help={error && touched ? error : null}
      // required={isRequired}
      hidden={isHidden}
    >
      <Select
       placeholder={t('common.select')} 
        mode="multiple"
        open={open}
        showSearch={false}
        onDropdownVisibleChange={setOpen}
        allowClear
        value={tempSelected}
        options={fullOptions}
        // onChange={(vals) => setTempSelected(vals)}
        onChange={(vals) => {
          setTempSelected(vals);
          onChange(vals); 
        }}
        onBlur={onBlur}
        disabled={isDisabled}
        getPopupContainer={(trigger) => trigger.parentElement || document.body}
        style={{ width: typeof w === 'number' ? `${w}px` : w || '100%' }}
        dropdownRender={(menu) => (
          <>
            <div style={{ padding: 4, maxHeight: 200, overflowY: 'auto' }}>
              <Input prefix={<SearchOutlined style={{ color: 'rgba(0,0,0,.25)' }}/>}
              value={searchValue}
              onChange={handleSearchChange}/>
              {filteredOptions.map((opt) => (
                <div key={opt.value}>
                  <Checkbox
                  style={{marginTop: "18px"}}
                   disabled={
                      (isAllSelected && opt.value !== 'LAN*') || // 如果选中全部，其他禁用
                      (!isAllSelected && opt.value === 'LAN*' && tempSelected.length > 0) // 选中其他，禁用 *
                    }
                    checked={tempSelected.includes(opt.value)}
                    onChange={(e) => {
                      let newSelected;
                      if (e.target.checked) {
                        newSelected = [...tempSelected, opt.value];
                      } else {
                        newSelected = tempSelected.filter((v) => v !== opt.value);
                      }
                      setTempSelected(newSelected);
                      onChange(newSelected);
                    }}
                  >
                    {opt.label}
                  </Checkbox>
                </div>
              ))}
            </div>
            <Divider style={{ margin: '8px -4px' }} />
            <div style={{ display: 'flex', justifyContent: 'space-between', padding: '0 0px 4px',marginLeft: "-4px" }}>
              <Button type="link" size="small" onClick={handleReset}>
                Reset
              </Button>
              <Button type="link" size="small" onClick={handleOk}>
                Ok
              </Button>
            </div>
          </>
        )}
      />
    </Form.Item>
  );
};

FastMultiSelectInput.propTypes = propTypes;
FastMultiSelectInput.defaultProps = defaultProps;

export default React.memo(FastMultiSelectInput, isEqual);
