.wirelessFormCreateLabel {
  .ant-form-item-label > label {
    width: 150px;
    text-align: left;
    word-wrap: break-word;
    overflow: hidden;
  }
  .ant-form-item-control {
    .ant-input-affix-wrapper,
    .ant-input,
    .ant-select,
    .ant-input-number,
    .ant-picker,
    .ant-select-selector {
      width: 280px;
    }
  }

  .foot-btns {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    margin-right: 30px;
    padding-bottom: 8px;
  }
}
.mySelect .ant-select-item-option-selected {
  background: none !important;
  font-weight: normal !important;

}

.mySelect .ant-select-item-option-state {
  display: none !important;
}

.optionLabel {
  color: #000000 !important;
}