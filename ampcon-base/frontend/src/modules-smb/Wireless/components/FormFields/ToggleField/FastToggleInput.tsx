import React from 'react';
import { Form, Switch } from 'antd';
import ConfigurationFieldExplanation from '../ConfigurationFieldExplanation';
import { FieldInputProps } from '@/modules-smb/models/Form';

interface Props extends FieldInputProps<boolean> {
  element?: React.ReactNode;
  isError: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement> | boolean) => void;
}

const FastToggleInput: React.FC<Props> = ({
  element,
  label,
  value,
  onChange,
  onBlur,
  error,
  isError,
  isRequired,
  isDisabled,
  definitionKey,
}) => {
  console.log('value',value)
  return (
    <Form.Item
      label={
        <div style={{ display: 'flex', alignItems: 'center', gap: 2,width:'180px' }}>
         <span>{label}</span>
         <ConfigurationFieldExplanation definitionKey={definitionKey} />
         {isRequired && <span 
         style={{ color: "#ff4d4f",
                  fontFamily: "SimSun, sans-serif",
                  marginLeft: "2px",
                  marginRight: "4px",
                  display: "inline-block"}}
                  >*</span>}
        </div>
      }
      // required={isRequired}
      help={isError ? error : null}
      validateStatus={isError ? 'error' : ''}
      labelCol={{ style: { display: 'flex', alignItems: 'center' } }}
      style={{ marginBottom: label==='VLAN'&&value ? 8 : 24 }}
    >
      {element ?? (
        <Switch
          checked={value}
          onChange={onChange}
          onBlur={onBlur} // 模拟 onBlur
          disabled={isDisabled}
        />
      )}
    </Form.Item>
  );
};

export default React.memo(FastToggleInput);
