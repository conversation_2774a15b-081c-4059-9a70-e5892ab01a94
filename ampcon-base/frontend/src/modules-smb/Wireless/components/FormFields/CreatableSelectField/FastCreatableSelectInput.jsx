import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Select, Form ,Tooltip} from 'antd';
import { useTranslation } from 'react-i18next';
import ConfigurationFieldExplanation from '../ConfigurationFieldExplanation';
import '@/modules-smb/Wireless/assets/form.scss';
import { CloseOutlined } from '@ant-design/icons';
const propTypes = {
  value: PropTypes.arrayOf(PropTypes.string),
  label: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  onBlur: PropTypes.func.isRequired,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  touched: PropTypes.bool,
  isDisabled: PropTypes.bool,
  isRequired: PropTypes.bool,
  isHidden: PropTypes.bool,
  definitionKey: PropTypes.string,
  placeholder: PropTypes.string,
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

const defaultProps = {
  value: [],
  error: false,
  touched: false,
  isRequired: false,
  isDisabled: false,
  isHidden: false,
  definitionKey: null,
  placeholder: '',
};

const FastCreatableSelectInput = ({
  label,
  value,
  onChange,
  onBlur,
  error,
  touched,
  isRequired,
  isDisabled,
  isHidden,
  definitionKey,
  placeholder,
  w,
}) => {
  const { t } = useTranslation();
  const [inputValue, setInputValue] = useState('');

  const safeValue = value || [];

  const handleSearch = (val) => {
    setInputValue(val);
  };

  const handleChange = (newValue) => {
    onChange(newValue);
    setInputValue('');
  };

  const options = [
    ...safeValue.map((v) => ({ label: v, value: v })),
    ...(inputValue && !safeValue.includes(inputValue)
      ? [{ label: `Create "${inputValue}"`, value: inputValue }]
      : []),
  ];

  return (
    <Form.Item
      label={
        <div style={{ display: 'flex', alignItems: 'center', gap: 2, width: '180px' }}>
          <span>{label}</span>
          <ConfigurationFieldExplanation definitionKey={definitionKey} />
          {isRequired && <span 
                  style={{ color: "#ff4d4f",
                  fontFamily: "SimSun, sans-serif",
                  marginLeft: "2px",
                  marginRight: "4px",
                  display: "inline-block"}}
                  >*</span>}
        </div>
      }
      hidden={isHidden}
      validateStatus={error && touched ? 'error' : ''}
      help={touched && error ? error : null}
      className="input-item-error-other"
    >
      <Select
        mode="tags"
        style={{ width: typeof w === 'number' ? `${w}px` : w || '100%' }}
        placeholder={placeholder}
        value={safeValue}
        onChange={handleChange}
        onBlur={onBlur}
        disabled={isDisabled}
        options={options}
        onSearch={handleSearch}
        showSearch
        notFoundContent={!inputValue ? t('common.type_for_options') : null}
        // 自定义 tag 渲染，控制省略和 Tooltip
        tagRender={(props) => {
  const { label, closable, onClose } = props;
  const displayLabel = String(label);

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        maxWidth: 250,
        background: '#f0f0f0',
        borderRadius: 4,
        padding: '0 6px',
        marginRight: 4,
      }}
    >
      <Tooltip title={displayLabel} overlayStyle={{ maxWidth: 400 }}>
        <span
          style={{
            flex: 1,
            minWidth: 0,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
        >
          {displayLabel}
        </span>
      </Tooltip>

      {/* 删除按钮 */}
      {closable && (
        <CloseOutlined
          onClick={onClose}
          style={{
            marginLeft: 6,
            fontSize: 12,
            color: 'rgba(0,0,0,.45)',
            cursor: 'pointer',
            flexShrink: 0,
          }}
        />
      )}
    </span>
  );
}}
      />
    </Form.Item>
  );
};

FastCreatableSelectInput.propTypes = propTypes;
FastCreatableSelectInput.defaultProps = defaultProps;

export default React.memo(FastCreatableSelectInput);
