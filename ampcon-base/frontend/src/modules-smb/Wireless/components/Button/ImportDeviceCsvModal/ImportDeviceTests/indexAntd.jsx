import axios from 'axios';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/modules-smb/contexts/AuthProvider';
import { axiosProv } from '@/modules-smb/utils/axiosInstances';
import { useGetDeviceTypes } from "@/modules-ampcon/apis/upgrade_api";
import { useState, useEffect, useMemo } from 'react';
import { Table, Typography, Spin, Progress, Switch, Button } from 'antd';

const propTypes = {
  setPhase: PropTypes.func.isRequired,
  setDevicesToImport: PropTypes.func.isRequired,
  devicesToTest: PropTypes.arrayOf(PropTypes.instanceOf(Object)).isRequired,
  venueId: PropTypes.string.isRequired,
};

// 创建可复用的分页Table组件
const PaginatedTable = ({ dataSource, columns, showPagination = true, rowKey = "SerialNumber" }) => {
  const { t } = useTranslation();

  const paginationConfig = showPagination ? {
    total: dataSource.length,
    // pageSize: 10,
    pageSizeOptions: ['10', '20', '30', '40', '50'],
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
    // locale: { items_per_page: t('common.items_per_page') || 'items per page' }
    // size: 'small'
  } : false;

  return (
    <Table
      dataSource={dataSource}
      columns={columns}
      pagination={paginationConfig}
      rowKey={rowKey}
      style={{ maxHeight: '200px', overflow: 'auto' }}
      siaze="small"
    // bordered={true}
    />
  );
};

const ImportDeviceTestsAntd = ({ devicesToTest, setPhase, setDevicesToImport, venueId }) => {
  const { t } = useTranslation();
  const { token } = useAuth();
  const { data: deviceTypes } = useGetDeviceTypes();
  const [reassign, setReassign] = useState(false);
  const [assignUnassigned, setAssignUnassigned] = useState(false);
  const [testResult, setTestResult] = useState({ isLoading: false });
  console.log(testResult);


  // Define columns for the tables
  const columns = useMemo(() => [
    {
      title: t('inventory.serial_number'),
      dataIndex: 'SerialNumber',
      key: 'SerialNumber',
      sorter: (a, b) => a.SerialNumber.localeCompare(b.SerialNumber),
    },
    {
      title: t('inventory.device_type'),
      dataIndex: 'DeviceType',
      key: 'DeviceType',
      sorter: (a, b) => a.DeviceType.localeCompare(b.DeviceType),
    },
    {
      title: t('Name') || 'Name',
      dataIndex: 'Name',
      key: 'Name',
      sorter: (a, b) => a.Name.localeCompare(b.Name),
    },
    {
      title: t('inventory.label'),
      dataIndex: 'Label',
      key: 'Label',
      sorter: (a, b) => a.Label.localeCompare(b.Label),
    },
    {
      title: t('Description'),
      dataIndex: 'Description',
      key: 'Description',
      sorter: (a, b) => a.Description.localeCompare(b.Description),
    },
    {
      title: t('Note'),
      dataIndex: 'Note',
      key: 'Note',
      sorter: (a, b) => a.Note.localeCompare(b.Note),
    },
  ], [t]);

  // Define columns with error for the error table
  const columnsWithError = useMemo(() => [
    // ...columns,
    {
      title: t('inventory.serial_number'),
      dataIndex: 'SerialNumber',
      key: 'SerialNumber',
      sorter: (a, b) => a.SerialNumber.localeCompare(b.SerialNumber),
    },
    {
      title: t('inventory.device_type'),
      dataIndex: 'DeviceType',
      key: 'DeviceType',
      sorter: (a, b) => a.DeviceType.localeCompare(b.DeviceType),
    },
    {
      title: t('Name'),
      dataIndex: 'Name',
      key: 'Name',
      sorter: (a, b) => a.Name.localeCompare(b.Name),
    },
    {
      title: t('Label'),
      dataIndex: 'Label',
      key: 'Label',
      sorter: (a, b) => a.Label.localeCompare(b.Label),
    },
    {
      title: t('common.error'),
      dataIndex: 'error',
      key: 'error',
      sorter: (a, b) => a.error.localeCompare(b.error),
    },
    // ], [columns, t]);
  ], [t]);

  const getDevice = (device, axiosSource) => {
    const deviceResult = {
      found: false,
      alreadyAssigned: false,
    };

    const options = {
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${token}`,
      },
      cancelToken: axiosSource.token,
    };

    return axios
      // .get(`${axiosProv.defaults.baseURL}/inventory/${device.SerialNumber}${venueId !== 'all' ? `?venueId=${venueId}` : ''}`, options)
      .get(`${axiosProv.defaults.baseURL}/inventory/${device.SerialNumber}`, options)
      .then((response) => {
        if (response.data.venue !== '' || response.data.entity !== '' || response.data.subscriber !== '')
          deviceResult.alreadyAssigned = true;
        else deviceResult.foundUnassigned = true;
        return deviceResult;
      })
      .catch((e) => {
        if (axios.isCancel(e)) return { stop: true };
        return deviceResult;
      });
  };

  const testDevice = (device, alreadyTested) => {
    // 1.
    if (device.SerialNumber === '' || device.SerialNumber.length !== 12 || !device.SerialNumber.match('^[a-fA-F0-9]+$'))
      return t('devices.invalid_serial_number');
    // 2.
    if (!deviceTypes.find((devType) => devType === device.DeviceType))
      return t('devices.device_type_not_found');
    // 3.
    if (alreadyTested.find((testedDevice) => device.SerialNumber === testedDevice))
      return t('devices.duplicate_serial');
    //新增校验：2025.08.22
    // 4. Name不能为空（去除首尾空格后仍为空则判定为无效） 
    if (!device.Name || device.Name.trim() === '')
      // return t('devices.name_cannot_be_empty'); 
      return t('Name not found');
    //5.label仅支持数字和字母
    // 先去除所有逗号(前置步骤中将$符号替换为了逗号)，再判断是否仅包含数字和字母
    if (device.Label) {
      const labelWithoutComma = device.Label.replace(/,/g, '');
      if (!/^[a-zA-Z0-9]+$/.test(labelWithoutComma)) {
        return t('Invalid Label (only numbers and letters are allowed)');
      }
    }
    return null;
  };

  const testImport = async (source) => {
    setTestResult({ isLoading: true });

    // 检查导入文件模版
    if (devicesToTest.length > 0) {
      const firstDevice = devicesToTest[0];
      const requiredHeaders = ['Serial Number', 'Device Type', 'Name', 'Label', 'Description', 'Note'];
      // const requiredHeaders = ['Serial Number', 'Device Type', 'Name'];
      const hasRequiredHeaders = requiredHeaders.every(header =>
        Object.prototype.hasOwnProperty.call(firstDevice, header));
      if (!hasRequiredHeaders) {
        setTestResult({
          isLoading: false,
          isFinished: true,
          newDevices: [],
          foundNotAssigned: [],
          foundAssigned: [],
          fileErrors: [{
            'Serial Number': '',
            'Device Type': '',
            'Name': '',
            error: t('Error importing template')
          }],
        });
        return;
      }

    }


    const treatedSerialNumbers = [];

    const newDevices = [];
    const foundNotAssigned = [];
    const foundAssigned = [];
    const fileErrors = [];

    for (let i = 0; i < devicesToTest.length; i += 1) {
      const device = devicesToTest[i];
      // 格式化excel数据，默认导入模板是带空格的，不带空格的不允许导入
      if (!device.SerialNumber) {
        device.SerialNumber = device['Serial Number']
      }
      if (!device.DeviceType) {
        device.DeviceType = device['Device Type']
      }
      // 保留Label, Description, Note字段
      if (!device.Name) {
        device.Name = device['Name'];
      }
      if (!device.Label) {

        device.Label = device['Label'] || '';
      }
      if (device.Label) {
        // $ 替换为,
        device.Label = device['Label']?.replace(/\$/g, ',');
      }
      if (!device.Description) {
        device.Description = device['Description'] || '';
      }
      if (!device.Note) {
        device.Note = device['Note'] || '';
      }

      setTestResult({
        isLoading: true,
        treating: device.SerialNumber,
        percentTreated: Math.floor((Math.max(i - 1, 0) / devicesToTest.length) * 100),
      });
      const testDeviceInfo = testDevice(device, treatedSerialNumbers);

      if (!testDeviceInfo) {
        // eslint-disable-next-line no-await-in-loop
        const result = await getDevice(device, source);
        if (result.stop) break;
        else if (result.alreadyAssigned) foundAssigned.push(device);
        else if (result.foundUnassigned) foundNotAssigned.push(device);
        else newDevices.push(device);
      } else {
        fileErrors.push({ ...device, error: testDeviceInfo });
      }
      treatedSerialNumbers.push(device.SerialNumber);
    }

    setTestResult({
      isLoading: false,
      isFinished: true,
      newDevices,
      foundNotAssigned,
      foundAssigned,
      fileErrors,
    });
  };

  const canImport = () => {
    const newLength = testResult?.newDevices?.length ?? -1;
    const unassignedLength = testResult?.foundNotAssigned?.length ?? -1;
    const assignedLength = testResult?.foundAssigned?.length ?? -1;
    return newLength > 0 || (unassignedLength > 0 && assignUnassigned) || (assignedLength > 0 && reassign);
  };

  const startImport = () => {
    const assigned = reassign ? testResult.foundAssigned : [];
    const notAssigned = assignUnassigned ? testResult.foundNotAssigned : [];

    setDevicesToImport({
      newDevices: testResult.newDevices,
      devicesToUpdate: [...assigned, ...notAssigned],
    });
    setPhase(2);
  };

  useEffect(() => {
    const cancelToken = axios.CancelToken;
    const source = cancelToken.source();
    if (devicesToTest.length > 0) {
      testImport(source);
    }
    return () => {
      source.cancel('axios request cancelled');
    };
  }, [devicesToTest]);

  if (testResult?.isLoading) {
    return (
      <div style={{ padding: '24px' }}>
        {/* <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '16px' }}>
          <Spin size="large" />
        </div> */}
        <div style={{ display: 'flex', alignItems: 'center', marginTop: '130px' }}>
          <Progress
            percent={testResult?.percentTreated ?? 0}
            showInfo={false}
            style={{ flex: 1, marginBottom: 0 }}
            status={testResult?.percentTreated !== 100 ? 'active' : 'success'}
            strokeColor={testResult?.percentTreated === 100 ? '#14C9BB' : undefined}
            strokeWidth={12}
          />
          <Typography.Title
            level={5}
            style={{ margin: '0 0 0px 0', width: 50, fontWeight: 'bold', marginLeft: 10 }}>
            {Math.round(testResult.percentTreated || 0)}%
          </Typography.Title>
        </div>
        <Typography.Title
          level={5}
          // style={{ textAlign: 'center', marginBottom: 25, fontWeight: 'bold' }}
          style={{ marginTop: '16px', marginBottom: '0', fontWeight: 'bold' }}
        >
          {t('devices.treating')} {testResult?.treating}
        </Typography.Title>
      </div>



    );
  }

  if (testResult?.isFinished) {
    return (
      <div style={{ padding: '0' }}>
        <Typography.Title level={5} style={{ margin: '0 0 16px 0' }}>{t('devices.test_results')}</Typography.Title>

        {testResult.newDevices.length > 0 && (
          <div style={{ border: '2px solid #36d399', borderRadius: '5px', padding: '8px', marginBottom: '16px' }}>
            <Typography.Text strong style={{ display: 'block', marginBottom: '8px' }}>
              {testResult.newDevices.length} {t('devices.new_devices')}
            </Typography.Text>
            <PaginatedTable
              dataSource={testResult.newDevices}
              columns={columns}
              showPagination={true}
            />
          </div>
        )}

        {testResult.foundNotAssigned.length > 0 && (
          <div style={{ border: '2px solid #fbbf24', borderRadius: '5px', padding: '8px', marginBottom: '16px' }}>
            <Typography.Text strong style={{ display: 'block', marginBottom: '8px' }}>
              {testResult.foundNotAssigned.length} {t('devices.found_not_assigned')}
            </Typography.Text>
            <PaginatedTable
              dataSource={testResult.foundNotAssigned}
              columns={columns}
              showPagination={true}
            />
          </div>
        )}

        {testResult.foundAssigned.length > 0 && (
          <div style={{ border: '2px solid #fbbf24', borderRadius: '5px', padding: '8px', marginBottom: '16px' }}>
            <Typography.Text strong style={{ display: 'block', marginBottom: '8px' }}>
              {testResult.foundAssigned.length} {t('devices.found_assigned')}
            </Typography.Text>
            <PaginatedTable
              dataSource={testResult.foundAssigned}
              columns={columns}
              showPagination={true}
            />
          </div>
        )}

        {testResult.fileErrors.length > 0 && (
          <div style={{ border: '2px solid #f87171', borderRadius: '5px', padding: '8px', marginBottom: '16px' }}>
            <Typography.Text strong style={{ display: 'block', marginBottom: '8px' }}>
              {testResult.fileErrors.length} {t('devices.file_errors')}
            </Typography.Text>
            <PaginatedTable
              dataSource={testResult.fileErrors}
              columns={columnsWithError}
              showPagination={true}
            />
          </div>
        )}

        {/* 已经创建但未分配站点的设备 */}
        {testResult.foundNotAssigned.length > 0 && (
          <div style={{ marginTop: '0px' }}>
            <div>
              <Typography.Text style={{ color: 'red' }}>{'Reassign devices which already exist and are owned by another site?'}</Typography.Text>
            </div>
            <div style={{ marginTop: '5px', marginBottom: '67px' }}>
              <Switch checked={assignUnassigned} onChange={setAssignUnassigned} />
            </div>
          </div>
        )}

        {/* 已经创建并且被分配站点的设备 */}
        {testResult.foundAssigned.length > 0 && (
          <div style={{ marginTop: '16px' }}>
            <div>
              {/* Reassign devices which already exist and are owned by another entity/venue/subscriber? */}
              <Typography.Text style={{ color: 'red' }}>{t('devices.reassign_already_owned')}</Typography.Text>
            </div>
            <div style={{ marginTop: '5px', marginBottom: '67px' }}>
              <Switch checked={reassign} onChange={setReassign} />
            </div>
          </div>
        )}
        <div style={{ display: 'flex', justifyContent: 'left', gap: '8px', marginTop: '40px',marginBottom: '46px' }}>
          <Button
            type="primary"
            onClick={startImport}
            disabled={!canImport()}
            style={{ minWidth: '160px' }}
          >
            {t('devices.start_import')}
          </Button>
        </div>
      </div>
    );
  }

  return null;
};

ImportDeviceTestsAntd.propTypes = propTypes;

export default ImportDeviceTestsAntd;
