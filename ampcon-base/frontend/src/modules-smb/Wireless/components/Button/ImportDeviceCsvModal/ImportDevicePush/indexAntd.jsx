import React, { useEffect, useState, useMemo } from 'react';
import { Table, Typography, Spin, Progress, Divider } from 'antd';
import axios from 'axios';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/modules-smb/contexts/AuthProvider';
import { axiosProv } from '@/modules-smb/utils/axiosInstances';
import { some } from 'lodash';

const propTypes = {
  devices: PropTypes.shape({
    newDevices: PropTypes.instanceOf(Array).isRequired,
    devicesToUpdate: PropTypes.instanceOf(Array).isRequired,
  }).isRequired,
  refresh: PropTypes.func.isRequired,
  deviceClass: PropTypes.string.isRequired,
  parent: PropTypes.instanceOf(Object).isRequired,
  setIsCloseable: PropTypes.func.isRequired,
  venueId: PropTypes.string.isRequired,
  handleRefresh: PropTypes.func.isRequired,
};

// 创建可复用的分页Table组件
const PaginatedTable = ({ dataSource, columns, showPagination = true, rowKey = "SerialNumber" }) => {
  const { t } = useTranslation();

  const paginationConfig = showPagination ? {
    total: dataSource.length,
    // pageSize: 10,
    pageSizeOptions: ['10', '20', '30', '40', '50'],
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
    // locale: { items_per_page: t('common.items_per_page') || 'items per page' }
    // size: 'small'
  } : false;

  return (
    <Table
      style={{ maxHeight: '200px', overflow: 'auto' }}
      dataSource={dataSource}
      columns={columns}
      pagination={paginationConfig}
      rowKey={rowKey}
      siaze="small"
    // bordered={true}
    />
  );
};

const ImportDevicePushAntd = ({ devices, refresh, deviceClass, parent, setIsCloseable, venueId ,handleRefresh}) => {
  const { t } = useTranslation();
  const { token } = useAuth();
  const [results, setResults] = useState({ isLoading: false });

  const createDevice = (device, source) => {
    const options = {
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${token}`,
      },
      cancelToken: source.token,
    };

    return axios
      .post(`${axiosProv.defaults.baseURL}/inventory/${device.serialNumber}`, device, options)
      .then(() => ({ success: true }))
      .catch((e) => {
        if (axios.isCancel(e)) return { success: false, stop: true };
        return {
          success: false,
          error: e.response?.data?.ErrorDescription ?? 'Unknown Error',
        };
      });
  };

  const updateDevice = (device, source) => {
    const options = {
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${token}`,
      },
      cancelToken: source.token,
    };

    return axios
      .put(`${axiosProv.defaults.baseURL}/inventory/${device.serialNumber}`, device, options)
      .then(() => ({ success: true }))
      .catch((e) => {
        if (axios.isCancel(e)) return { success: false, stop: true };
        return {
          success: false,
          error: e.response?.data?.ErrorDescription ?? 'Unknown Error',
        };
      });
  };

  const createLabels = async (siteId, labelNames) => {
    const options = {
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${token}`,
      },
    };

    const requestData = {
      site_id: siteId,
      name: labelNames
    };

    try {

      // const response = await axios.post(`site/labels`, requestData, options);
      const response = await axios.post('/ampcon/wireless/site/labels', requestData, options);
      return response.data;
    } catch (error) {
      console.error('Failed to create labels:', error);
      return { status: 500, info: 'Failed to create labels' };
    }
  };


  const importDevices = async (source) => {
    setResults({ isLoading: true });

    const commonInfo = {
      entity: '',
      venue: venueId == 'all' ? '' : venueId,
      subscriber: '',
      devClass: deviceClass,
      ...parent,
    };

    const totalLength = devices.newDevices.length + devices.devicesToUpdate.length;
    const successPost = [];
    const errorPost = [];
    const successPut = [];
    const errorPut = [];

    // 获取所有的lable,在新增或者修改devices前,将lable传给后端添加到站点中
    const allLabels = new Set();

    // 封装遍历方法
    const collectLabels = (device) => {
      // 再把,转换成$
      const newLabel = device.Label?.replace(/\,/g, '$');
      allLabels.add(newLabel);
    }

    // 新增或修改前 调用 遍历lable函数
    devices.newDevices.forEach(collectLabels);
    devices.devicesToUpdate.forEach(collectLabels);

    // 如果有标签需要创建，且当前不是'all'站点，则批量创建标签
    if (allLabels.size > 0 && venueId !== 'all') {
      const labelNames = Array.from(allLabels);
      await createLabels(venueId, labelNames);
    }

    for (let i = 0; i < devices.newDevices.length; i += 1) {
      const device = devices.newDevices[i];

      setResults({
        isLoading: true,
        treating: device.SerialNumber,
        percentTreated: Math.floor((Math.max(i - 1, 0) / totalLength) * 100),
      });

      const deviceToPush = {
        ...commonInfo,
        serialNumber: device.SerialNumber,
        deviceType: device.DeviceType,
        name: device.Name.length > 0 ? device.Name : device.SerialNumber,
        description: device.Description,
        notes: device.Note !== '' ? [{ note: device.Note }] : undefined,
        labelsName: device.Label,
      };

      // eslint-disable-next-line no-await-in-loop
      const result = await createDevice(deviceToPush, source);
      if (result.stop) break;
      if (result.success) successPost.push(device);
      else if (!result.success) errorPost.push({ ...device, error: result.error });
    }

    for (let i = 0; i < devices.devicesToUpdate.length; i += 1) {
      const device = devices.devicesToUpdate[i];

      setResults({
        isLoading: true,
        treating: device.SerialNumber,
        percentTreated: Math.floor((Math.max(i - 1, 0) / totalLength) * 100),
      });

      const deviceToPush = {
        ...commonInfo,
        serialNumber: device.SerialNumber,
        name: device.Name.length > 0 ? device.Name : device.SerialNumber,
        labelsName: device.Label,
        description: device.Description,
        notes: device.Note !== '' ? [{ note: device.Note }] : undefined,
      };

      // eslint-disable-next-line no-await-in-loop
      const result = await updateDevice(deviceToPush, source);
      if (result.stop) break;
      if (result.success) successPut.push(device);
      else if (!result.success) errorPut.push({ ...device, error: result.error });
    }

    setResults({
      isLoading: false,
      isFinished: true,
      successPost,
      errorPost,
      successPut,
      errorPut,
    });
    setIsCloseable(true);
    refresh();
    handleRefresh(); // 刷新数据
  };

  useEffect(() => {
    const cancelToken = axios.CancelToken;
    const source = cancelToken.source();
    if ((devices?.newDevices?.length > 0) || (devices?.devicesToUpdate?.length > 0)) {
      importDevices(source);
    }
    return () => {
      source.cancel('axios request cancelled');
    };
  }, [devices]);

  // antd Table columns
  const columns = useMemo(() => [
    {
      title: t('inventory.serial_number') || 'Serial Number',
      dataIndex: 'SerialNumber',
      key: 'SerialNumber',
      sorter: (a, b) => a.SerialNumber.localeCompare(b.SerialNumber),

    },

    // {
    //   title: t('devices.device_type') || 'Device Type',
    //   dataIndex: 'DeviceType',
    //   key: 'DeviceType'
    // },
    // {
    //   title: t('devices.name') || 'Name',
    //   dataIndex: 'Name',
    //   key: 'Name'
    // },
    // {
    //   title: t('devices.label') || 'Label',
    //   dataIndex: 'Label',
    //   key: 'Label'
    // },
    // {
    //   title: t('devices.description') || 'Description',
    //   dataIndex: 'Description',
    //   key: 'Description'
    // },
    // {
    //   title: t('devices.note') || 'Note',
    //   dataIndex: 'Note',
    //   key: 'Note'
    // },

  ], [t]);

  const columnsWithError = useMemo(() => [
    ...columns,
    {
      title: t('common.error') || 'Error',
      dataIndex: 'error',
      key: 'error',
      sorter: (a, b) => a.error.localeCompare(b.error),
      render: (text) => <Typography.Text type="danger">{text}</Typography.Text>
    },
  ], [columns, t]);

  if (results?.isLoading) {
    return (
      // <div style={{ padding: 24 }}>
      //   <div style={{ display: 'flex', justifyContent: 'center', marginBottom: 16 }}>
      //     <Spin size="large" />
      //   </div>
      //   <Progress percent={results?.percentTreated ?? 0} showInfo={false} />
      //   <Typography.Title level={5} style={{ marginTop: 16, marginBottom: 0 }}>
      //     {t('devices.treating')}: {results?.treating}
      //   </Typography.Title>
      // </div>
      <div style={{ padding: '24px', marginTop: '130px' }}>
        {/* <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '16px' }}>
                <Spin size="large" />
              </div> */}
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Progress
            percent={results?.percentTreated ?? 0}
            showInfo={false}
            style={{ flex: 1, marginBottom: 0 }}
            status={results?.percentTreated !== 100 ? 'active' : 'success'}
            strokeColor={results?.percentTreated === 100 ? '#14C9BB' : undefined}
            strokeWidth={12}
          />
          <Typography.Title
            level={5}
            style={{ margin: '0 0 0px 0', width: 50, fontWeight: 'bold', marginLeft: 10 }}>
            {Math.round(results.percentTreated || 0)}%
          </Typography.Title>
        </div>
        <Typography.Title
          level={5}
          // style={{ textAlign: 'center', marginBottom: 25, fontWeight: 'bold' }}
          style={{ marginTop: '16px', marginBottom: '0', fontWeight: 'bold' }}
        >
          {t('devices.treating')} {results?.treating}
        </Typography.Title>
      </div>
    );
  }

  if (results?.isFinished) {
    return (
      <div style={{ padding: 0 }}>
        {results.successPost.length > 0 && (
          <div style={{ border: '2px solid #36d399', borderRadius: 5, padding: 8, marginBottom: 16 }}>
            <Typography.Text strong style={{ display: 'block', marginBottom: 12, }}>
              {results.successPost.length} {t('devices.create_success')}
            </Typography.Text>
            <PaginatedTable
              dataSource={results.successPost}
              columns={columns}
              showPagination={true}
            />
          </div>
        )}
        {results.errorPost.length > 0 && (
          <div style={{ border: '2px solid #f87171', borderRadius: 5, padding: 8, marginBottom: 16 }}>
            <Typography.Text strong style={{ display: 'block', marginBottom: 12 }}>
              {results.errorPost.length} {t('devices.create_errors')}
            </Typography.Text>
            <PaginatedTable
              dataSource={results.errorPost}
              columns={columnsWithError}
              showPagination={true}
            />
          </div>
        )}
        {results.successPut.length > 0 && (
          <div style={{ border: '2px solid #36d399', borderRadius: 5, padding: 8, marginBottom: 16 }}>
            <Typography.Text strong style={{ display: 'block', marginBottom: 12 }}>
              {results.successPut.length} {t('devices.update_success')}
            </Typography.Text>
            <PaginatedTable
              dataSource={results.successPut}
              columns={columns}
              showPagination={true}
            />
          </div>
        )}
        {results.errorPut.length > 0 && (
          <div style={{ border: '2px solid #f87171', borderRadius: 5, padding: 8, marginBottom: 16 }}>
            <Typography.Text strong style={{ display: 'block', marginBottom: 12 }}>
              {results.errorPut.length} {t('devices.update_error')}
            </Typography.Text>
            <PaginatedTable
              dataSource={results.errorPut}
              columns={columnsWithError}
              showPagination={true}
            />
          </div>
        )}
      </div>
    );
  }

  return null;
};

ImportDevicePushAntd.propTypes = propTypes;
export default ImportDevicePushAntd;
