import React, { useState } from 'react';
import { UploadSimple } from '@phosphor-icons/react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import ImportDeviceFileAntd from './ImportDeviceFile/indexAntd';
import ImportDevicePushAntd from './ImportDevicePush/indexAntd';
import ImportDeviceTestsAntd from './ImportDeviceTests/indexAntd';
import { Button, Modal, Tooltip, Alert, Divider } from 'antd';
import { UploadOutlined, CloseOutlined } from '@ant-design/icons';
import { blacken } from '@chakra-ui/theme-tools';
import LogoUpload from '@/modules-smb/Wireless/assets/Inventory/Logo_Upload.png';

const propTypes = {
  refresh: PropTypes.func.isRequired,
  handleRefresh: PropTypes.func.isRequired,
  deviceClass: PropTypes.string.isRequired,
  parent: PropTypes.shape({
    entity: PropTypes.string,
    venue: PropTypes.string,
  }),
};

const defaultProps = {
  parent: {},
};

// 根据url获取当前页面的 venueId
const getVenueIdFromHash = () => {
  if (window.location.hash) {
    return window.location.hash.replace('#', '');
  }
  return '';
};

const ImportDeviceCsvModalAntd = ({ refresh, handleRefresh, deviceClass, parent }) => {
  const { t } = useTranslation();
  const [refreshId, setRefreshId] = useState(uuid());
  const [phase, setPhase] = useState(0);
  const [isCloseable, setIsCloseable] = useState(true);
  const [isOpen, setIsOpen] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const [devicesToTest, setDevicesToTest] = useState([]);
  const [devicesToImport, setDevicesToImport] = useState({});

  const getPhase = () => {
    switch (phase) {
      case 0:
        return (
          <ImportDeviceFileAntd
            setPhase={setPhase}
            setDevices={setDevicesToTest}
            setIsCloseable={setIsCloseable}
            refreshId={refreshId}
          />
        );
      case 1:
        return (
          <ImportDeviceTestsAntd
            setPhase={setPhase}
            devicesToTest={devicesToTest}
            setDevicesToImport={setDevicesToImport}
            setIsCloseable={setIsCloseable}
            venueId={getVenueIdFromHash()}
          />
        );
      case 2:
        return (
          <ImportDevicePushAntd
            devices={devicesToImport}
            refresh={refresh}
            deviceClass={deviceClass}
            parent={parent}
            setIsCloseable={setIsCloseable}
            venueId={getVenueIdFromHash()}
            handleRefresh={handleRefresh}
          />
        );
      default:
        return null;
    }
  };

  const openModal = () => {
    setPhase(0);
    setRefreshId(uuid());
    setIsCloseable(true);
    setDevicesToTest([]);
    setDevicesToImport([]);
    setIsOpen(true);
  };

  const closeModal = () => (isCloseable ? setIsOpen(false) : setShowConfirm(true));

  const handleConfirmClose = () => {
    setShowConfirm(false);
    setIsOpen(false);
  };

  const handleCancelClose = () => {
    setShowConfirm(false);
  };

  return (
    <>
      <Tooltip title={t('devices.import_batch_tags') || 'Import Batch Tags'} placement="top" mouseEnterDelay={0.3}>
        <Button
          htmlType="button"
          onClick={openModal}
          style={{
            display: "flex",
            alignItems: "center", 
          }}
          icon={<img src={LogoUpload} alt="upload" style={{ width: 16, height: 16 }} />}
        >
          Upload
        </Button>
      </Tooltip>

      <Modal
        style={{
          borderRadius: '8px',
          minHeight: '500px',
          padding: '15px 20px'
        }}
        title={
          <div style={{

            fontSize: '20px',
            fontWeight: 'bold',
            color: '#222',
            marginTop: '0px !important',
            height: '27px',
            display: 'flex',
            alignItems: 'center',
            margin: '0px 0px 15px 0',
            // justifyContent: 'space-between',
            // padding: '0px 10px',
            // borderBottom: '1px solid #ccc',
            // marginBottom: '10px'

            // display: 'flex', alignItems: 'center', justifyContent: 'space-between', display: 'flex',

          }}>
            <span>{t('devices.import_batch_tags') || 'Import Batch Tags'}</span>
          </div >
        }
        open={isOpen}
        onCancel={closeModal}
        width={1360}
        footer={null}
      // closable={false}
      >
        <Divider
          style={{
            // 上右下右
            margin: '16 0 16px 0',
            width: 'calc(100% + 48px)',
            marginLeft: '-24px',

          }}
        />

        <div style={{
          padding: '0px',
          minHeight: '420px',
          marginTop: '30px',
          // marginBottom: '5px',
          maxHeight: 'calc(100vh - 350px)',
          overflowY: 'auto'
        }}>{getPhase()}</div>
      </Modal >

      <Modal
        open={showConfirm}
        onOk={handleConfirmClose}
        onCancel={handleCancelClose}
        zIndex={1001}
        title={
          <div style={{

            fontSize: '20px',
            fontWeight: 'bold',
            color: '#222',
            marginTop: '0px !important',
            height: '27px',
            display: 'flex',
            alignItems: 'center',
            margin: '0px 0px 15px 0',
          }}>
            <span>{t('common.discard_changes')}</span>
          </div >
        }

        cancelText={t('common.cancel')}
        okText={t('common.confirm')}
        width={480}
        footer={[
          <Button key="cancel" onClick={handleCancelClose}>{t('common.cancel')}</Button>,
          <Button key="ok" type="primary" danger onClick={handleConfirmClose}>{t('common.confirm')}</Button>
        ]}
      >
        <Divider
          style={{
            margin: '0 0 32px 0',
            width: 'calc(100% + 50px)',
            marginLeft: '-25px',
          }}
        />

        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Alert
            type="warning"
            showIcon
            style={{
              color: 'rgba(0, 0, 0, 0.88)',
              backgroundColor: 'transparent',
              border: 'none',
              padding: 0
            }}
          />
          {t('crud.confirm_cancel') || 'Are you sure you want to discard the changes you have made?'}
        </div>

        <Divider
          style={{
            margin: '64px 0px 16px 0px',
            width: 'calc(100% + 50px)',
            marginLeft: '-25px',
          }}
        />
      </Modal >
    </>
  );
};

ImportDeviceCsvModalAntd.propTypes = propTypes;
ImportDeviceCsvModalAntd.defaultProps = defaultProps;
export default ImportDeviceCsvModalAntd;
