import React from "react";
import { Button, Tooltip, Grid } from "antd";
import { useTranslation } from "react-i18next";
import deleteSvg from "@/modules-smb/Wireless/assets/Devices/delete.svg?react";
import Icon from "@ant-design/icons";

interface Props {
  onClick: () => void;
  isDisabled?: boolean;
  isLoading?: boolean;
  isCompact?: boolean;
  label?: string;
  ml?: string | number;
}

const defaultProps = {
  isDisabled: false,
  isLoading: false,
  isCompact: true,
  label: undefined,
  ml: undefined,
};

const DeleteButton: React.FC<Props> = ({
  onClick,
  isDisabled,
  isLoading,
  isCompact,
  label,
  ml,
  ...props
}) => {
  const { t } = useTranslation();
  const screens = Grid.useBreakpoint();

  // 判断屏幕尺寸（小屏只显示图标按钮）
  const isSmallScreen = !screens.md; // 小于 md 的时候 true

  if (!isCompact && !isSmallScreen) {
    return (
      <Button
        type="primary"
        danger
        onClick={onClick}
        icon={<Icon component={deleteSvg} />}
        loading={isLoading}
        disabled={isDisabled}
        style={{ marginLeft: ml }}
        {...props}
      >
        {label ?? t("crud.delete")}
      </Button>
    );
  }

  return (
    <Tooltip title={label ?? t("crud.delete")}>
      <Button
        // type="primary"
        // danger
        // shape="circle"
        onClick={onClick}
        icon={<Icon component={deleteSvg} />}
        loading={isLoading}
        disabled={isDisabled}
        style={{ marginLeft: ml }}
        {...props}
      >{label ?? t("crud.delete")}</Button>
    </Tooltip>
  );
};

DeleteButton.defaultProps = defaultProps;
export default React.memo(DeleteButton);
