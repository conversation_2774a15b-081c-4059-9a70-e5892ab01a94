import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { ExportedDeviceInfo, getAllExportedDevicesInfo, getSelectExportedDevicesInfo } from './utils';
import { CSVLink } from 'react-csv';
import { dateForFilename } from '@/modules-smb/utils/dateFormatting';
import { Button, Modal, Tooltip, Progress, Typography, Divider } from 'antd';
import Icon from "@ant-design/icons";
import { whiteExportSvg } from "@/utils/common/iconSvg";
import LogoExport from "@/modules-smb/Wireless/assets/Inventory/Logo_Export.png";
const HEADER_MAPPING: { key: keyof ExportedDeviceInfo; label: string }[] = [
  { key: 'serialNumber', label: 'Serial Number' },
  { key: 'name', label: 'Name' },
  { key: 'site', label: 'Site' },
  { key: 'description', label: 'Description' },
  { key: 'label', label: 'Label' },
  { key: 'modified', label: 'Modified' },
];
// const HEADER_MAPPING: { key: keyof ExportedDeviceInfo; label: string }[] = [
//   { key: 'serialNumber', label: 'Serial Number' },
//   { key: 'deviceType', label: 'Device Type' },
//   { key: 'name', label: 'Name' },
//   { key: 'entity', label: 'Entity' },
//   { key: 'venue', label: 'Venue' },
//   { key: 'created', label: 'Created' },
//   { key: 'modified', label: 'Modified' },
//   { key: 'description', label: 'Description' },
//   { key: 'devClass', label: 'Device Class' },
//   { key: 'firmwareUpgrade', label: 'Firmware Upgrade' },
//   { key: 'rcOnly', label: 'Release Candidates Only' },
//   { key: 'rrm', label: 'RRM' },
//   { key: 'id', label: 'ID' },
//   { key: 'locale', label: 'Locale' },
// ];
type Status = {
  progress: number;
  status: 'loading-all' | 'loading-select' | 'success' | 'error' | 'idle';
  error?: string;
  lastResults?: ExportedDeviceInfo[];
};

type Props = {
  serialNumbers?: string[];
};

// 根据url获取当前页面的 venueId
const getVenueIdFromHash = () => {
  if (window.location.hash) {
    const id = window.location.hash.replace('#', '');
    return id === 'all' ? '' : id;
  }
  return '';
};

const ExportDevicesTableButtonAntd = ({ serialNumbers }: Props) => {
  const { t } = useTranslation();
  const [modalOpen, setModalOpen] = React.useState(false);
  const [status, setStatus] = React.useState<Status>({
    progress: 0,
    status: 'idle',
  });

  const setProgress = (progress: number) => {
    setStatus((prev) => ({ ...prev, progress }));
  };

  const onOpen = () => {
    const venueId = getVenueIdFromHash();
    if (!serialNumbers) {
      setStatus((prev) => ({ ...prev, error: undefined, lastResults: undefined, status: 'loading-all', progress: 0 }));
      getAllExportedDevicesInfo(setProgress, venueId)
        .then((result) => {
          setStatus((prev) => ({ ...prev, status: 'success', lastResults: result }));
        })
        .catch((error) => {
          setStatus((prev) => ({ ...prev, status: 'error', error }));
        });
    } else {
      setStatus((prev) => ({
        ...prev,
        error: undefined,
        lastResults: undefined,
        status: 'loading-select',
        progress: 0,
      }));
      getSelectExportedDevicesInfo(serialNumbers, setProgress, venueId)
        .then((result) => {
          setStatus((prev) => ({ ...prev, status: 'success', lastResults: result }));
        })
        .catch((error) => {
          setStatus((prev) => ({ ...prev, status: 'error', error }));
        });
    }
    setModalOpen(true);
  };

  const handleCancel = () => {
    setModalOpen(false);
  };

  return (
    <>
      <Tooltip title={t('common.export')}>
        <Button
          htmlType="button"
          onClick={onOpen}
          style={{ display: "flex", alignItems: "center" }}
          icon={<img src={LogoExport} alt="export" style={{ width: 16, height: 16 }} />}
        >
          Export
        </Button>
      </Tooltip>

      <Modal
        title={t('common.export')}
        open={modalOpen}
        onCancel={handleCancel}
        footer={null}
        width={680}
        style={{
          height: 450,
          // minHeight: 450,
          // maxHeight: 'calc(100vh - 350px)'
        }}
      >

        <div style={{ padding: 0 }}>
          {(status.status.includes('loading') || status.status === 'success') && (
            <>
              <Divider
                style={{
                  margin: '15px 0 45px 0',
                  width: 'calc(100% + 48px)',
                  marginLeft: '-24px',
                }}
              />

              <div style={{ margin: '139px 0 139px 0' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Progress
                    style={{ flex: 1, marginBottom: 0 }}
                    percent={Math.round(status.progress)}
                    status={status.progress !== 100 ? 'active' : 'success'}
                    showInfo={false}
                    strokeColor={status.progress === 100 ? '#14C9BB' : undefined}
                    strokeWidth={12}
                  />
                  <Typography.Title level={5} style={{ margin: '0 0 0px 0', width: 50, fontWeight: 'bold' }}>
                    {Math.round(status.progress)}%
                  </Typography.Title>
                </div>
                {/* 进度提示信息 */}
                <Typography.Text style={{ textAlign: 'center', marginBottom: 25, fontWeight: 'bold' }}>
                  {`Exporting to ${status.progress}%, Please Wait...`}
                </Typography.Text>
              </div>

              {status.lastResults && (
                <Divider
                  style={{
                    margin: '45px 0 0 0',
                    width: 'calc(100% + 48px)',
                    marginLeft: '-24px',
                  }}
                />
              )}

              {status.lastResults && (
                <div style={{ textAlign: 'right', margin: '20px 0 0 0' }}>
                  <CSVLink
                    filename={`devices_export_${dateForFilename(new Date().getTime() / 1000)}.csv`}
                    data={status.lastResults ?? []}
                    headers={HEADER_MAPPING}
                  >
                    <Button type="primary" >
                      {t('common.download')}
                    </Button>
                  </CSVLink>
                </div>
              )}
            </>
          )}
          {status.status.includes('error') && (
            <Typography.Text type="danger" style={{ display: 'block', textAlign: 'center', marginTop: 32 }}>
              {JSON.stringify(status.error, null, 2)}
            </Typography.Text>
          )}
        </div>
      </Modal>
    </>
  );
};

export default ExportDevicesTableButtonAntd;
