import React from 'react';
import { <PERSON><PERSON>, Dropdown, <PERSON>u, <PERSON><PERSON><PERSON>, message } from 'antd';
import {
  Barcode,
  Power,
  TerminalWindow,
  WifiHigh,
  Wrench,
} from '@phosphor-icons/react';
import axios from 'axios';
import { useTranslation } from 'react-i18next';
import { useControllerStore } from '@/modules-smb/contexts/ControllerSocketProvider/useStore';
import { useBlinkDevice, useGetDeviceRtty } from '@/modules-smb/hooks/Network/Devices';
import { useUpdateDeviceToLatest } from '@/modules-smb/hooks/Network/Firmware';
import useMutationResult from '@/modules-smb/hooks/useMutationResult';
import { GatewayDevice } from '@/modules-smb/models/Device';
import Icon from "@ant-design/icons";
import icon_Actions from "@/modules-smb/Wireless/assets/Devices/icon_Actions.svg?react";
import icon_Reboot from "@/modules-smb/Wireless/assets/Devices/icon_Reboot.svg?react";
import icon_Rtty from "@/modules-smb/Wireless/assets/Devices/icon_Rtty.svg?react";
import {DownOutlined}  from '@ant-design/icons';

interface Props {
  device: GatewayDevice;
  refresh: () => void;
  isDisabled?: boolean;
  onOpenScan: (serialNumber: string) => void;
  onOpenFactoryReset: (serialNumber: string) => void;
  onOpenUpgradeModal: (serialNumber: string) => void;
  onOpenTrace: (serialNumber: string) => void;
  onOpenEventQueue: (serialNumber: string) => void;
  onOpenConfigureModal: (serialNumber: string) => void;
  onOpenTelemetryModal: (serialNumber: string) => void;
  onOpenScriptModal: (device: GatewayDevice) => void;
  onOpenRebootModal: (serialNumber: string) => void;
  size?: 'small' | 'middle' | 'large';
  isCompact?: boolean;
}

const DeviceActionDropdown = ({
  device,
  refresh,
  isDisabled,
  onOpenScan,
  onOpenFactoryReset,
  onOpenTrace,
  onOpenUpgradeModal,
  onOpenEventQueue,
  onOpenTelemetryModal,
  onOpenConfigureModal,
  onOpenScriptModal,
  onOpenRebootModal,
  isCompact,
}: Props) => {
  const { t } = useTranslation();
  const deviceType = device?.deviceType ?? 'ap';
  const addEventListeners = useControllerStore((state) => state.addEventListeners);

  const { refetch: getRtty, isFetching: isRtty } = useGetDeviceRtty({
    serialNumber: device.serialNumber,
    extraId: 'inventory-modal',
  });
  const { mutateAsync: blink } = useBlinkDevice({ serialNumber: device.serialNumber });
  const { onSuccess: onBlinkSuccess, onError: onBlinkError } = useMutationResult({
    objName: t('devices.one'),
    operationType: 'blink',
    refresh,
  });
  const updateToLatest = useUpdateDeviceToLatest({ serialNumber: device.serialNumber, compatible: device.compatible });

  /** handlers */
  // const handleBlinkClick = () => {
  //   blink(undefined, {
  //     onError: (e) => {
  //       if (axios.isAxiosError(e)) onBlinkError(e);
  //     },
  //   });
  //   onBlinkSuccess();
  // };
  const handleBlinkClick = () => {
  blink(undefined, {
    onSuccess: () => {
      message.success(
        t('commands.blink_success')
      );
      refresh?.();
    },
    onError: (e) => {
      message.error(
        axios.isAxiosError(e)
          ? t('commands.blink_error', { e: e.response?.data?.ErrorDescription }) || t('common.error')
          : t('common.error')
      );
    },
  });
};
  const handleOpenScan = () => onOpenScan(device.serialNumber);
  const handleOpenFactoryReset = () => onOpenFactoryReset(device.serialNumber);
  const handleOpenUpgrade = () => onOpenUpgradeModal(device.serialNumber);
  const handleOpenTrace = () => onOpenTrace(device.serialNumber);
  const handleOpenQueue = () => onOpenEventQueue(device.serialNumber);
  const handleOpenConfigure = () => onOpenConfigureModal(device.serialNumber);
  const handleOpenTelemetry = () => onOpenTelemetryModal(device.serialNumber);
  const handleOpenScript = () => onOpenScriptModal(device);
  const handleRebootClick = () => onOpenRebootModal(device.serialNumber);

  const handleUpdateToLatest = () => {
    updateToLatest.mutate(
      { keepRedirector: true },
      {
        onSuccess: () => {
          message.success(t('controller.devices.sent_upgrade_to_latest'));
          addEventListeners([
            {
              id: `device-connection-upgrade-${device.serialNumber}`,
              type: 'DEVICE_CONNECTION',
              serialNumber: device.serialNumber,
              callback: () =>
                message.success(
                  t('controller.devices.finished_upgrade', { serialNumber: device.serialNumber }),
                ),
            },
            {
              id: `device-disconnected-upgrade-${device.serialNumber}`,
              type: 'DEVICE_DISCONNECTION',
              serialNumber: device.serialNumber,
              callback: () =>
                message.success(
                  t('controller.devices.started_upgrade', { serialNumber: device.serialNumber }),
                ),
            },
          ]);
        },
        onError: (e) => {
          if (axios.isAxiosError(e)) {
            message.error(e?.response?.data?.ErrorDescription || t('common.error'));
          }
        },
      },
    );
  };

  /** dropdown menu items */
  const menu = (
    <Menu
      items={[
        { key: 'blink', label: t('commands.blink'), onClick: handleBlinkClick },
        {
          key: 'configure',
          label: t('controller.configure.title'),
          onClick: handleOpenConfigure,
          hidden: !isCompact || deviceType !== 'ap',
        },
        { key: 'rtty', label: t('commands.rtty'), onClick: getRtty, hidden: !isCompact },
        { key: 'factory', label: t('commands.factory_reset'), onClick: handleOpenFactoryReset },
        { key: 'reboot', label: t('commands.reboot'), onClick: handleRebootClick, hidden: !isCompact },
        { key: 'trace', label: t('controller.devices.trace'), onClick: handleOpenTrace },
        // { key: 'upgrade', label: t('premium.toolbox.upgrade_to_latest'), onClick: handleUpdateToLatest },
      ].filter((i) => !i.hidden)}
    />
  );

  return (
    <>
      <Tooltip title={t('commands.rtty')}>
        <Button
          type="primary" 
          icon={<Icon component={icon_Rtty}/> }
          disabled={isDisabled}
          loading={isRtty}
          onClick={getRtty}
          // hidden={isCompact}
        >RTTY</Button>
      </Tooltip>

      <Tooltip title={t('commands.reboot')}>
        <Button
          icon={<Icon component={icon_Reboot} />}
          disabled={isDisabled}
          onClick={handleRebootClick}
          hidden={isCompact}
        >Reboot</Button>
      </Tooltip>

      <Dropdown overlay={menu} trigger={['click']} placement="bottomRight">
        <Tooltip title={t('common.actions')}>
          <Button
            icon={<Icon component={icon_Actions} />}
            disabled={isDisabled}
          >Actions<DownOutlined/></Button>
        </Tooltip>
      </Dropdown>
    </>
  );
};

export default React.memo(DeviceActionDropdown);
