import React from 'react';
import { QuestionCircleFilled } from '@ant-design/icons';
import { globalConfigurationDescriptions } from '@/modules-smb/contexts/AuthProvider';

export function LabelTip(definitionKey?: string) {
  let title = definitionKey;
  const configurationDescriptions = globalConfigurationDescriptions;
  if (definitionKey && configurationDescriptions) {
    const split = definitionKey.split('.');
    const { length } = split;
    if (length >= 2) {
      const start = split.slice(0, length - 1);
      const end = split[length - 1];
      title = configurationDescriptions[start.slice(0, length - 1).join('.')]
        ?.properties[end ?? '']?.description;
    }
  }
  return {
    title,
    icon: <QuestionCircleFilled style={{ color: '#B3BBC8', cursor: 'pointer', paddingLeft: 0 , marginLeft: 2, width: 10, height: 10 }} />,
  };
}
