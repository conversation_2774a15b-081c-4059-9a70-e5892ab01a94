import React, { useState, useEffect, useMemo, useRef } from "react";
import { Select, Input } from "antd";
import { useGetVenues } from "@/modules-smb/hooks/Network/Venues";
import { useEntityFavorite } from "@/modules-smb/Wireless/hooks/useEntityFavorite";
import "../assets/wireless.scss";
import { SearchOutlined } from '@ant-design/icons';
import { useSiteStore } from "@/modules-smb/Wireless/components/SiteContext";
import { useSiteSelection } from "@/modules-smb/Wireless/hooks/useSelectedSite";

type Option = { value: string; label: string; type: "venue" | "entity" };

const SiteSelect = ({ onChange }) => {
    const { getFirstVenueFavoriteId } = useEntityFavorite();
    const getVenues = useGetVenues();
    const [searchValue, setSearchValue] = useState('');
    const {
        selectedSiteId,
        isAllSitesSelected,
        handleSiteChange,
        displaySiteId
    } = useSiteSelection(true);
    
    // 初始化数据获取
    useEffect(() => {
        if (typeof getVenues.refetch === 'function') {
            getVenues.refetch();
        }
    }, []);

    // 处理选项数据
    const allOptions = useMemo(() => {
        if (!getVenues.data) return [];

        const options = getVenues.data
            .sort((a, b) => {
                if (String(a.id) === "0") return -1;
                if (String(b.id) === "0") return 1;
                return a.name.localeCompare(b.name);
            })
            .map(venue => ({
                label: venue.name,
                value: String(venue.id),
                type: "venue"
            }));

        // 在options头部添加一个默认选项
        options.unshift({ label: "All Sites", value: "all", type: "venue" });

        return options;
    }, [getVenues.data]);

    // 根据搜索值过滤选项
    const filteredOptions = useMemo(() => {
        if (!searchValue) return allOptions;
        return allOptions.filter(option =>
            option.label.toLowerCase().includes(searchValue.toLowerCase())
        );
    }, [allOptions, searchValue]);

    

    const handleSearchChange = (e) => {
        setSearchValue(e.target.value);
    };

    // 当下拉框关闭时清空搜索值
    const handleDropdownVisibleChange = (open) => {
        if (!open) {
            setSearchValue('');
        }
    };

    return (
        <div className="site-select-container">
            <span className="site-title">Site</span>
            <Select
                className="site-select"
                //placeholder={defaultLabel}
                options={filteredOptions}
                value={isAllSitesSelected ? "all" : selectedSiteId || "all"}
                onChange={(value) => {
                    handleSiteChange(value); // 使用封装好的处理方法
                    onChange?.(value); // 保留原有的onChange回调
                }}
                style={{ width: 260 }}
                onDropdownVisibleChange={handleDropdownVisibleChange}
                dropdownRender={(menu) => (
                    <>
                        <div style={{ padding: 8 }}>
                            <Input
                                prefix={<SearchOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                                placeholder="Search sites..."
                                value={searchValue}
                                onChange={handleSearchChange}
                                allowClear
                            />
                        </div>
                        <div >
                            {filteredOptions.length > 0 ? menu : <div style={{ padding: 8, textAlign: 'center' }}>No results found</div>}
                        </div>
                    </>
                )}
            />
        </div>
    );
};

export default SiteSelect;