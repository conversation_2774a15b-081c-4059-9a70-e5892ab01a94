import React, { useState, useRef } from 'react';
import { DatePicker, Button, Tooltip, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import dayjs, { Dayjs } from 'dayjs';
import '@/modules-smb/Wireless/assets/form.scss'; 

const { RangePicker } = DatePicker;



interface CustomRangePickerProps {
  value: [Dayjs, Dayjs];
  onChange: (dates: [Dayjs, Dayjs]) => void;
  width?: number;
  tooltipText?: string;
  defaultTime: [Dayjs, Dayjs];
}

const CustomRangePicker: React.FC<CustomRangePickerProps> = ({
  value,
  onChange,
  width = 380,
  tooltipText = '',
  defaultTime // 接收外部传递的默认时间
}) => {
  const { t } = useTranslation();
  const [pendingTime, setPendingTime] = useState<[Dayjs, Dayjs] | null>(null);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [activeInputIndex, setActiveInputIndex] = useState<number | null>(null);
  const rangePickerRef = useRef<any>(null);
  // 处理范围变更
  const handleRangeChange = (dates: [Dayjs, Dayjs] | null) => {
    if (dates) {
      setPendingTime(dates);
    } else {
      // 当点击×时，使用外部传递的默认时间
      setPendingTime(defaultTime);
      onChange(defaultTime);
    }
  };

  // 处理面板打开/关闭状态变更
  const handleOpenChange = (open: boolean) => {
    setIsPanelOpen(open);
    if (!open) {
      // 面板关闭时，确认使用pendingTime
      if (pendingTime) {
        onChange(pendingTime);
      }
      setPendingTime(null);
    } else {
      // 面板打开时，初始化pendingTime为当前值
      setPendingTime(value);
    }
  };

  // 处理输入框聚焦
  const handleRangeFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    const index = e.target.placeholder === t('common.start_time') ? 0 : 1;
    setActiveInputIndex(index);

    if (!pendingTime) {
      setPendingTime(value);
    }
  };

  // 处理"Now"按钮点击
  const handleNow = () => {
    if (activeInputIndex === null) return;
    const now = dayjs();
    // 使用pendingTime或当前值作为基础
    const baseTime = pendingTime || value;
    const newTime = [baseTime[0], baseTime[1]] as [Dayjs, Dayjs];
    newTime[activeInputIndex] = now;
    if (newTime[0] && newTime[1] && newTime[0].isAfter(newTime[1]) && activeInputIndex === 0) {
      newTime[1] = now;
    }
    setPendingTime(newTime);
  };

  const handleCalendarChange = (dates: [Dayjs, Dayjs] | null) => {
    if (dates) {
      setPendingTime(dates);
    }
  };

  return (
    <>
      <Space align="center">
        <Tooltip title={tooltipText || t('controller.crud.choose_time')}>
          <RangePicker
            ref={rangePickerRef}
            value={pendingTime || value}
            onChange={handleRangeChange}
            onCalendarChange={handleCalendarChange}
            showTime={{ format: 'HH:mm:ss' }}
            format="YYYY-MM-DD HH:mm:ss"
            placeholder={[t('common.start_time'), t('common.end_time')]}
            style={{ width }}
            onFocus={handleRangeFocus}
            onOpenChange={handleOpenChange}
            open={isPanelOpen}
            dropdownClassName="custom-range-picker-dropdown"
            renderExtraFooter={() => (
              <div className="ant-picker-footer">
                <Button
                  type="text"
                  className="ant-picker-now-btn"
                  onClick={handleNow}
                >
                  Now
                </Button>
              </div>
            )}
          />
        </Tooltip>
      </Space>
    </>
  );
};

export default CustomRangePicker;