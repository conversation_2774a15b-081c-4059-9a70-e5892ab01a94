import React, { useEffect, useRef } from "react";
import * as echarts from "echarts";

type TooltipFormatter = (params: any) => string;

type BaseBarEchartsProps = {
  xAxis: string[];
  yAxisData: number[] | { value: number; name?: string }[];
  colorList?: string[];
  width?: string;
  onClicked?: (params: any) => void;
  tooltipStyle?: {
    trigger?: string;
    formatter?: TooltipFormatter;
  };
  title?: string;
  height?: string;
};

const BaseBarEcharts: React.FC<BaseBarEchartsProps> = ({
  xAxis,
  yAxisData,
  colorList = [],
  width = "",
  onClicked,
  tooltipStyle = {},
  title = "",
  height="300px",
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const myChart = echarts.init(chartRef.current);

    const defaultTooltip = {
      trigger: "axis",
      backgroundColor: 'rgba(0, 0, 0, 0.6)',  // 添加黑色半透明背景
      textStyle: { color: '#fff' },  
      formatter: (params: any[]) => {
        const { name, value,color } = params[0];
        return `<div style="line-height:1.5">
                  <div>${name}</div>
                  <div>
                    <span style="
                      display: inline-block;
                      width: 10px;
                      height: 10px;
                      background: ${color};
                      margin-right: 5px;
                      border-radius: 2px;
                    "></span>
                    ${name}: ${value}
                  </div>
                </div>`;
      }
    };

    const option: echarts.EChartsOption = {
      title: title
        ? {
            text: title,
            left: "left",
            top: 24,
            textStyle: {
              fontSize: 12,
              fontWeight: 400,
              color: "#212519"
            }
          }
        : undefined,
      tooltip: tooltipStyle.trigger ? tooltipStyle : defaultTooltip,
      xAxis: {
        type: "category",
        data: xAxis,
        axisLabel: {
          interval: 0,
          fontSize: 12
        }
      },
      yAxis: {
        type: "value"
      },
      grid: {
        left: "10%",
        right: "3%",
        top: title ? "14%" : "8%",
        bottom: "10%"
      },
      series: [
        {
          type: "bar",
          barMaxWidth: 30,   // 最大像素宽
          barMinWidth: 6, 
          data: yAxisData,
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          }
        }
      ],
      color: colorList.length ? colorList : undefined
    };

    myChart.setOption(option);

    if (onClicked) {
      myChart.on("click", onClicked);
    }

    const handleResize = () => {
      myChart.resize();
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
      myChart.dispose();
    };
  }, [xAxis, yAxisData, colorList, width, onClicked, tooltipStyle, title]);

  return (
    <div style={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100%" }}>
      <div ref={chartRef} style={{ height: height, width: "100%" }} />
    </div>
  );
};

export default BaseBarEcharts;
