import React, { useState, useEffect, useRef } from "react";
import * as echarts from "echarts";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";

interface ArrowPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const ArrowPagination: React.FC<ArrowPaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
}) => {
  return (
    <div
      style={{
        position: "absolute",
        top: 8,
        right: 12,
        display: "flex",
        alignItems: "center",
        gap: 8,
        background: "rgba(255, 255, 255, 0.75)",
        padding: "2px 6px",
        borderRadius: 6,
        boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
      }}
    >
      <LeftOutlined
        style={{
          fontSize: 16,
          color: currentPage === 1 ? "#ccc" : "#1890ff",
          cursor: currentPage === 1 ? "not-allowed" : "pointer",
        }}
        onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
      />

      <span style={{ fontSize: 13 }}>
        {currentPage} / {totalPages}
      </span>

      <RightOutlined
        style={{
          fontSize: 16,
          color: currentPage === totalPages ? "#ccc" : "#1890ff",
          cursor: currentPage === totalPages ? "not-allowed" : "pointer",
        }}
        onClick={() => currentPage < totalPages && onPageChange(currentPage + 1)}
      />
    </div>
  );
};

const ChartWithPagination = () => {
  const totalPages = 5;
  const [currentPage, setCurrentPage] = useState(1);
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<echarts.ECharts>();

  const getDataForPage = (page: number) => {
    return Array.from({ length: 6 }, (_, i) => ({
      name: `分类${(page - 1) * 6 + i + 1}`,
      value: Math.round(Math.random() * 100),
    }));
  };

  useEffect(() => {
    if (chartRef.current) {
      if (!chartInstanceRef.current) {
        chartInstanceRef.current = echarts.init(chartRef.current);
      }
      const option: echarts.EChartsOption = {
        tooltip: { trigger: "item" },
        legend: { show: false },
        series: [
          {
            type: "pie",
            radius: ["40%", "70%"],
            data: getDataForPage(currentPage),
          },
        ],
      };
      chartInstanceRef.current.setOption(option);
    }
  }, [currentPage]);

  return (
    <div style={{ position: "relative", width: 500, height: 400 }}>
      <div ref={chartRef} style={{ width: "100%", height: "100%" }} />
      <ArrowPagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={setCurrentPage}
      />
    </div>
  );
};

export default ChartWithPagination;
