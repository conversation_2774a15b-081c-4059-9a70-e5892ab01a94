import React, { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Card, Pagination, Tooltip, Button, Modal, Typography, Divider } from "antd";
import { LeftOutlined, RightOutlined ,CaretRightOutlined,CaretLeftOutlined} from "@ant-design/icons";
import GraphStatDisplay from "@/modules-smb/Wireless/components/Containers/GraphStatDisplay";
import { COLORS } from "@/modules-smb/Wireless/constants/colors";
import { uppercaseFirstLetter } from "@/modules-smb/helpers/stringHelper";
import { ControllerDashboardCommands } from "@/modules-smb/hooks/Network/Controller";
import BarEcharts from "@/modules-smb/Wireless/components/Echarts/BarEcharts";
import ArrowPagination from "@/modules-smb/Wireless/components/Echarts/PaginatedBarChart/ArrowPagination";
const { Title, Paragraph } = Typography;

// 分页柱状图组件
const PaginatedBarChart = ({ data, itemsPerPage = 5,height="300px" }) => {
  const [currentPage, setCurrentPage] = useState(1);
  
  // 计算总页数
  const totalPages = Math.ceil(data.length / itemsPerPage);
  
  // 获取当前页的数据
  const currentData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return data.slice(startIndex, endIndex);
  }, [data, currentPage, itemsPerPage]);

  // 处理分页变化
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // 生成当前页的x轴和y轴数据
  const { xAxis, yAxisData } = useMemo(() => {
    const x = [];
     const y: { value: number; itemStyle: { color: string } }[] = [];
     const startIndex = (currentPage - 1) * itemsPerPage;

    for (let i = 0; i < currentData.length; i++) {
      const { tag, value,color } = currentData[i];
      const label = tag === "rtty" ? "RTTY" : uppercaseFirstLetter(tag);
      const globalIndex = startIndex + i; // 计算全局下标
      x.push(label);
      y.push({
        value,
        itemStyle: {
          color: color || COLORS[globalIndex % COLORS.length], // 优先用数据自带颜色
        },
      });
    }

    return { xAxis: x, yAxisData: y };
  }, [currentData]);

  return (
    <div style={{ 
      width: "100%", 
      position: "relative",
      // paddingBottom: totalPages > 1 ? 15 : 0 // 为分页控件预留空间
    }}>
      <BarEcharts
        title=""
        xAxis={xAxis}
        yAxisData={yAxisData}
        width="100%"
        height={height}
        grid={{ bottom: 50, containLabel: true }}
        tooltipStyle={{
          trigger: "axis",
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: { color: '#fff' },
          borderWidth: 1,
          borderColor: (params) => params[0]?.color || '#ccc',
          formatter: (params) => {
            const { name, value, color } = params[0];
            const total = data.reduce((acc, curr) => acc + curr.value, 0);
            const percent = total > 0 ? ((value / total) * 100).toFixed(2) : "0";
            
            return `<div style="line-height:1.5">
                      <div>${name}</div>
                      <div>
                        <span style="
                          display: inline-block;
                          width: 10px;
                          height: 10px;
                          background: ${color};
                          margin-right: 5px;
                          border-radius: 2px;
                        "></span>
                        ${name}: ${value} (${percent}%)
                      </div>
                    </div>`;
          },
        }}
      />
      
      {/* 分页控件 */}
      {totalPages > 1 && (
        <div style={{
          // marginTop: 0,       //和图表保持距离
          display: "flex",
          justifyContent: "flex-end", //右对齐
          alignItems: "center",
          gap: 8,
        }}  >
          <CaretLeftOutlined
                  style={{
                    fontSize: 10,
                    color: currentPage === 1 ? "#ccc" : "black",
                    cursor: currentPage === 1 ? "not-allowed" : "pointer",
                  }}
                  disabled={currentPage === 1}
                  onClick={() => handlePageChange(currentPage - 1)}
                />
          
          <span style={{fontSize: 10}}>
            {currentPage} / {totalPages}
          </span>
          <CaretRightOutlined
                  style={{
                    fontSize: 10,
                    marginRight: 5,
                    color: currentPage === totalPages ? "#ccc" : "black",
                    cursor: currentPage === totalPages ? "not-allowed" : "pointer",
                  }}
                  disabled={currentPage === totalPages}
                  onClick={() => handlePageChange(currentPage + 1)}
                />
        </div>
      )}
    </div>
  );
};

export default PaginatedBarChart;