export const cardBaseStyle = {
  width: '100%',
  height: '180px', 
  borderRadius: '8px',
  boxSizing: 'border-box',
  border: 'none',
  padding: 0,
  margin: 0,
  overflow: 'hidden',
};

// 新增：内部容器样式，确保100%填充
export const cardInnerContainerStyle = {
  height: '100%',
  width: '100%',
  padding: 0,
  margin: 0,
  boxSizing: 'border-box',
};



export const headerStyle = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: '0 16px', 
  boxSizing: 'border-box',
  height: '60px', 
  margin: 0,
};

export const contentBaseHeight = '120px'; 

export const cardBodyStyle = {
  padding: 0,
  margin: 0,
  height: '100%',
  display: 'flex',
  flexDirection: 'column'

};

export const dividerStyle = {
  margin: 0, 
  borderColor: '#DEEAF3',
  padding: 0,
};

export const detailButtonStyle = {
  border: 'none',
  outline: 'none',
  background: 'transparent',
  padding: 0,
};

export const tooltipIconStyle = {
  marginLeft: 2,
  width: 12,
  height: 12,
};

// 添加全局样式覆盖Ant Design默认样式
const style = document.createElement('style');
style.textContent = `
  .custom-metric-card .ant-card-body {
    padding: 0 !important;
    margin: 0 !important;
  }
  .custom-metric-card .ant-card-head {
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: none !important;
  }
  .custom-metric-card {
    padding: 0 !important;
    margin: 0 !important;
  }
`;
document.head.appendChild(style);

