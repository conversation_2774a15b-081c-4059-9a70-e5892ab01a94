import React, { useState } from 'react';
import { Card, Typography, Tooltip, Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import threeLineIcon from '@/modules-smb/Wireless/assets/Monitor/Logo_Three_Line.png';
import iconDetails from '@/modules-smb/Wireless/assets/Monitor/Logo_Detail.png';
import {
  cardBaseStyle,
  cardBodyStyle,
  headerStyle,
  dividerStyle,
  detailButtonStyle,
  tooltipIconStyle,
  contentBaseHeight,
  cardInnerContainerStyle
} from './style';

const { Title } = Typography;

const CommonMetricCard = ({
  title,
  explanation,
  bgGradient,
  content,
  onDetailClick,
  buttonColor
}) => {
  const { t } = useTranslation();
  const [isHovered, setIsHovered] = useState(false);
  const explanationKey = typeof explanation === 'string'
    ? explanation
    : explanation.key;

  const explanationParams = typeof explanation === 'object'
    ? explanation.params || {}
    : {};


  const buttonStyles = {
    ...detailButtonStyle,
    backgroundColor: isHovered ? buttonColor : 'transparent',
    border: 'none',
    padding: '6px 6px',
    display: 'flex',
    borderRadius:'2px' 
  };

  return (
    <Card
      style={{ ...cardBaseStyle, background: bgGradient,boxShadow: 'none' }}
      bodyStyle={{ padding: 0, margin: 0, border: 0,boxShadow: 'none' }}
      bordered={false}
      className="custom-metric-card"
    >
      {/* 内部容器，确保完全填充 */}
      <div style={cardInnerContainerStyle}>
        <div style={headerStyle}>
          <Title level={4} style={{ margin: 0, paddingLeft: 0,whiteSpace: 'nowrap' }}>
            {t(title)}
            <Tooltip title={t(explanationKey, explanationParams)}>
              <img src={iconDetails} style={tooltipIconStyle} />
            </Tooltip>
          </Title>
          <Tooltip title={t('common.view_details')}>
            <div
              aria-label={t('common.view_details')}
              onClick={onDetailClick}
              style={buttonStyles}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              <img src={threeLineIcon} style={{ height: 20, width: 20 }} />
            </div>
          </Tooltip>
        </div>

        <Divider style={dividerStyle} />

        <div style={{ 
          height: contentBaseHeight, 
          padding: 0, 
          boxSizing: 'border-box',
          overflow: 'hidden'
        }}>
          {content}
        </div>
      </div>
    </Card>
  );
};

export default CommonMetricCard;