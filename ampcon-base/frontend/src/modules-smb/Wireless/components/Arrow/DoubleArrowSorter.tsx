import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import React from'react';
interface DoubleArrowSorterProps {
  sortOrder: string; 
}
const DoubleArrowSorter: React.FC<DoubleArrowSorterProps> = ({ sortOrder }) => {
  return (
    <span style={{ 
       display: 'inline-flex',
      flexDirection: 'row',
      alignItems: 'center',
      marginLeft: "10px", 
      gap: 0 
    }}>
      <ArrowUpOutlined 
        style={{
          fontSize: 10,
          color: sortOrder === 'ascend'? '#14C9BB' : '#bfbfbf',
          transform: 'scale(1, 1.6)' // 水平缩放1倍，垂直缩放1.6倍
        }} 
      />
      <ArrowDownOutlined 
        style={{
          fontSize: 10,
          color: sortOrder === 'descend'? '#14C9BB' : '#bfbfbf',
          transform: 'scale(1, 1.6)' // 水平缩放1倍，垂直缩放1.6倍
        }}
      />
    </span>
  );
};
export default DoubleArrowSorter;