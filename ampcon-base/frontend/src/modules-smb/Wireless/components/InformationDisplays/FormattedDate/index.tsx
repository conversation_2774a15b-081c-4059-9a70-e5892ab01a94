import React from 'react';
import { Tooltip } from 'antd';
import { compactDate, formatDaysAgo, formatDaysAgoCompact } from '@/modules-smb/helpers/dateFormatting';

type Props = { date?: number; hidePrefix?: boolean; isCompact?: boolean };

const getDaysAgo = ({ date, hidePrefix, isCompact }: { date?: number; hidePrefix?: boolean; isCompact?: boolean }) => {
  if (!date || date === 0) return '-';
  if (isCompact)
    return hidePrefix ? formatDaysAgoCompact(date).split(' ').slice(1).join(' ') : formatDaysAgoCompact(date);
  return hidePrefix ? formatDaysAgo(date).split(' ').slice(1).join(' ') : formatDaysAgo(date);
};

const FormattedDate: React.FC<Props> = ({ date, hidePrefix, isCompact }) => (
  <Tooltip placement="top" title={compactDate(date ?? 0)}>
    <span>{getDaysAgo({ date, hidePrefix, isCompact })}</span>
  </Tooltip>
);

export default React.memo(FormattedDate);
