import { create } from 'zustand';

interface SiteState {
  selectedSiteId: string;
  isAllSitesSelected: boolean;
  setSelectedSiteId: (id: string) => void;
  setAllSites: () => void;
  resetToDefault: () => void;
  resetFromOtherPage: () => void;
}

export const useSiteStore = create<SiteState>((set) => ({
  selectedSiteId: '',
  isAllSitesSelected: false,
  defaultSiteId: '0',
  setSelectedSiteId: (id) => set({
    selectedSiteId: id,
    isAllSitesSelected: false
  }),
  setAllSites: () => set({
    isAllSitesSelected: true,
    selectedSiteId: ''
  }),
  resetToDefault: () => set({
    selectedSiteId: '',
    isAllSitesSelected: false
  }),
  resetFromOtherPage: () => set({
    isAllSitesSelected: false
  }),
}));