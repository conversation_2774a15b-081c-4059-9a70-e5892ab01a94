const domainRegex = /^(?=.{1,254}$)((?=[a-z0-9-]{1,63}\.)(xn--+)?[a-z0-9]+(-[a-z0-9]+)*\.)+[a-z]{2,63}$/i;
const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
const ipv6Regex = /^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:))$/;
const macRegex = /^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$/;

export const setProfileVariable = (value: string) => {
    return `__variableBlock__<<${value}>>`
}

export function getProfileVariable(str: string) {
    const match = str.match(/__variableBlock__<<(.+?)>>/);
    if (match) {
        return match[1];
    }
    return str;
}

// 校验 URL/IP4/IP6
export function validateUrlOrIp(input: string | string[]): Promise<void> {
    if (!input) {
        return Promise.resolve();
    }
    
    if (Array.isArray(input)) {
        return new Promise<void>((resolve, reject) => {
            for (const item of input) {
                const trimmedItem = item.trim();
                if (!trimmedItem) {
                    continue;
                }
                if (!(domainRegex.test(trimmedItem) || ipv4Regex.test(trimmedItem) || ipv6Regex.test(trimmedItem))) {
                    reject(new Error(`Invalid URL or IP format: ${trimmedItem}`));
                    return;
                }
            }
            resolve();
        });
    } else {
        if (domainRegex.test(input) || ipv4Regex.test(input) || ipv6Regex.test(input)) {
            return Promise.resolve();
        } else {
            return Promise.reject(new Error('Invalid URL or IP format'));
        }
    }
}

// 校验 IP4/IP6
export function validateIp(input: string | string[]): Promise<void> {
    if (!input) {
        return Promise.resolve();
    }
    
    if (Array.isArray(input)) {
        return new Promise<void>((resolve, reject) => {
            for (const item of input) {
                const trimmedItem = item.trim();
                if (!trimmedItem) {
                    continue;
                }
                if (!(ipv4Regex.test(trimmedItem) || ipv6Regex.test(trimmedItem))) {
                    reject(new Error(`Invalid IP format: ${trimmedItem}`));
                    return;
                }
            }
            resolve();
        });
    } else {
        if (ipv4Regex.test(input) || ipv6Regex.test(input)) {
            return Promise.resolve();
        } else {
            return Promise.reject(new Error('Invalid IP format'));
        }
    }
}

// 校验 MAC
export function validateMac(input: string | string[]): Promise<void> {
    if (!input) {
        return Promise.resolve();
    }
    
    if (Array.isArray(input)) {
        return new Promise<void>((resolve, reject) => {
            for (const item of input) {
                const trimmedItem = item.trim();
                if (!trimmedItem) {
                    continue;
                }
                if (!macRegex.test(trimmedItem)) {
                    reject(new Error(`${trimmedItem} Invalid MAC value, for example: 00:00:5e:00:53:af`));
                    return;
                }
            }
            resolve();
        });
    } else {
        if (macRegex.test(input)) {
            return Promise.resolve();
        } else {
            return Promise.reject(new Error('Invalid MAC value, for example: 00:00:5e:00:53:af'));
        }
    }
}

/**
 * 将 UTC 时间（时间戳或字符串）转换为浏览器本地时区的格式化字符串。
 * 入参必须表示 UTC 时间：
 *  - 数字：支持秒或毫秒时间戳（小于 1e12 会按秒处理）。
 *  - 字符串：YYYY-MM-DD HH:mm:ss。
 * @param input UTC 时间（number | string）
 * @returns 客户端时区格式化时间字符串，输入无效时抛出错误
 */
export function utcToLocalString(input: number | string): string {
    if (input === null || input === undefined || input === "") return "";
    let utcMs: number;
    if (typeof input === 'number') {
        utcMs = input < 1e12 ? input * 1000 : input;
    } else {
        const s = input.trim();
        const strictRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
        if (!strictRegex.test(s)) {
            throw new Error('Input must be a timestamp or a string in format "YYYY-MM-DD HH:mm:ss"');
        }
        const datePart = s.substring(0, 10); // YYYY-MM-DD
        const timePart = s.substring(11); // HH:mm:ss
        const dateParts = datePart.split('-');
        if (dateParts.length !== 3) throw new Error(`Invalid UTC time string: ${input}`);
        const y = Number(dateParts[0]);
        const mo = Number(dateParts[1]);
        const day = Number(dateParts[2]);
        if (Number.isNaN(y) || Number.isNaN(mo) || Number.isNaN(day)) {
            throw new Error(`Invalid UTC time string: ${input}`);
        }

        const timeParts = timePart.split(':');
        const hh = Number(timeParts[0] || 0);
        const mm = Number(timeParts[1] || 0);
        const ss = Number(timeParts[2] || 0);
        if (Number.isNaN(hh) || Number.isNaN(mm) || Number.isNaN(ss)) {
            throw new Error(`Invalid UTC time string: ${input}`);
        }
        utcMs = Date.UTC(y, mo - 1, day, hh, mm, ss);
    }

    // 直接用 Date(utcMs) 得到本地时间（浏览器会根据时区和 DST 自动转换）
    const local = new Date(utcMs);
    const Y = local.getFullYear();
    const M = String(local.getMonth() + 1).padStart(2, '0');
    const D = String(local.getDate()).padStart(2, '0');
    const h = String(local.getHours()).padStart(2, '0');
    const m = String(local.getMinutes()).padStart(2, '0');
    const s = String(local.getSeconds()).padStart(2, '0');

    return `${Y}-${M}-${D} ${h}:${m}:${s}`;
}


// 递归过滤掉对象中所有空值的key
export const filterNullValues = (obj: any): any => {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    if (Array.isArray(obj)) {
        return obj.map(item => filterNullValues(item));
    }
    const result: any = {};
    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const value = filterNullValues(obj[key]);
            if (value !== null && value !== undefined && value !== '') {
                result[key] = value;
            }
        }
    }
    return result;
};

