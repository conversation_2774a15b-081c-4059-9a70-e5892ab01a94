import { htmlToTar, tarToHtml } from '@/modules-ampcon/apis/config_api';
import allowTemplate from '@/modules-smb/assets/CaptiveTemplate/allow.htm';
import connectedTemplate from '@/modules-smb/assets/CaptiveTemplate/connected.htm';


export async function convertHtmlToBase64(
  html: string,
  authMode: string,
  appendExtra: boolean = false
): Promise<string> {
  let filename = 'click.html';
  authMode = authMode?.toLowerCase();
  if (authMode === 'credentials') {
    filename = 'credentials.html';
  } else if (authMode === 'radius') {
    filename = 'radius.html';
  }

  try {
    const data = [
      { html: html, filename: filename },
    ];
    if (appendExtra) {
      const [allowHtml, connectedHtml] = await Promise.all([
        fetch(allowTemplate).then(res => res.text()),
        fetch(connectedTemplate).then(res => res.text()),
      ]);
      data.push(
        { html: allowHtml, filename: 'allow.html' },
        { html: connectedHtml, filename: 'connected.html' }
      );
    }
    const response = await htmlToTar({ data });
    if (response.status === 200) {
      return response.data;
    } else {
      throw new Error(response.info || 'Failed to convert HTML to Base64');
    }
  } catch (error) {
    console.error(error);
    throw error;
  }
}

// ssids优先级排序函数
function sortSsids(ssids: any[] = []) {
  return [...ssids].sort((a, b) => {
    const getPriority = (ssid: any) => {
      if (!ssid.captive) return 0; // 没有captive，最高优先级
      if (ssid.captive && !ssid.captive['web-root']) return 1; // 有captive但没有web-root(默认default)
      if (ssid.captive && ssid.captive['web-root'].includes('default_remove')) return 2; // 选了default模板
      return 3; // 有captive且有web-root，且是自定义（选了自定义模板或直接自定义配置）
    };
    return getPriority(a) - getPriority(b);
  });
}

// 动态更新配置保存interfaces内容方法
export function updateInterfaces(interfaces: { ssids?: any[] }[] = []) {
  // 先内部ssid排序
  interfaces.forEach((item) => {
    if (Array.isArray(item.ssids)) {
      item.ssids = sortSsids(item.ssids);
    }
  });

  // 全局处理模板ID去重和最后一个web-root特殊处理
  // 收集所有ssids位置信息
  const allSsidRefs: { ssid: any; ifaceIdx: number; ssidIdx: number }[] = [];
  // 收集所有非default Block模板信息
  const blockIdMap: Record<string, number[]> = {};
  interfaces.forEach((iface, ifaceIdx) => {
    if (Array.isArray(iface.ssids)) {
      iface.ssids.forEach((ssid, ssidIdx) => {
        const ref = { ssid, ifaceIdx, ssidIdx };
        allSsidRefs.push(ref);

        const webRoot = ssid?.captive?.['web-root'];
        if (typeof webRoot === 'string') {
          // 编辑的时候要重置need_append_two_html
          if (webRoot.startsWith('need_append_two_html')) {
            ssid.captive['web-root'] = webRoot.replace(/^need_append_two_html/, '');
          }
          if (webRoot.includes('__variableBlock__') && !webRoot.startsWith('default_remove')) {
            const blockId = webRoot.split('__variableBlock__')[1] || '';
            if (!blockIdMap[blockId]) blockIdMap[blockId] = [];
            blockIdMap[blockId].push(allSsidRefs.length - 1);
          }
          
        }
      });
    }
  });

  // 对重复模板ID处理，最后一个保留，前面的加need_remove
  Object.values(blockIdMap).forEach((indexes) => {
    if (indexes.length > 0) {
      for (let i = 0; i < indexes.length; i++) {
        const idx = indexes[i];
        const ref = allSsidRefs[idx];
        if (ref.ssid?.captive?.['web-root']) {
          // 最后一个对象，编辑的时候如果改变顺序了要去掉need_remove前缀
          if (i === indexes.length - 1) {
            if (ref.ssid.captive['web-root'].startsWith('need_remove')) {
              ref.ssid.captive['web-root'] = ref.ssid.captive['web-root'].replace(/^need_remove/, '');
            }
          } else {
            if (!ref.ssid.captive['web-root'].startsWith('need_remove')) {
              ref.ssid.captive['web-root'] = 'need_remove' + ref.ssid.captive['web-root'];
            }
          }
        }
      }
    }
  });
  // let asyncTask: Promise<any> | null = null;
  // 最后一个web-root特殊处理
  if (allSsidRefs.length > 0) {
    // 找到最后一个有web-root的ssid
    const lastRef = [...allSsidRefs].reverse().find(ref => ref.ssid?.captive?.['web-root']);
    const lastWebRoot = lastRef?.ssid?.captive?.['web-root'];
    if (lastRef && typeof lastWebRoot === 'string') {
      // 模板加上need_append_two_html
      if (lastWebRoot.includes('__variableBlock__')) {
        if (lastWebRoot.startsWith('__variableBlock__')) {
          lastRef.ssid.captive['web-root'] = 'need_append_two_html' + lastWebRoot;
        }
      // 非模板也加need_append_two_html
      } else if (lastWebRoot) {
        lastRef.ssid.captive['web-root'] = 'need_append_two_html' + lastWebRoot;

      // 非模板请求接口解压tar重新追加打包
      //   console.log('last web-root:', lastRef.ssid.captive['auth-mode']);
      //   asyncTask = tarToHtml({ data: lastWebRoot }).then((tarRes: { data?: { content: string } }) => {
      //     const htmlContent = tarRes.data?.content || '';
      //     return convertHtmlToBase64(htmlContent, lastRef.ssid.captive['auth-mode'], true).then(newBase64 => {
      //       lastRef.ssid.captive['web-root'] = newBase64;
      //     });
      //   });
      }
    }
  }
  // console.log('interfaces:', interfaces);
  return interfaces;

  // if (asyncTask) {
  //   return asyncTask.then(() => {
  //     console.log('async interfaces:', interfaces);
  //     return interfaces;
  //   });
  // } else {
  //   console.log('interfaces:', interfaces);
  //   return interfaces;
  // }
}
