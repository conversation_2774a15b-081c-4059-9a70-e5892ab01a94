import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { Preference } from '@/modules-smb/models/Preference';
import { User } from '@/modules-smb/models/User';
import { axiosProv } from '@/modules-smb/utils/axiosInstances';

const getConfigDescriptions = async (baseUrl: string) =>
  axios.get(`${baseUrl.split('/api')[0]}/wwwassets/ucentral.schema.pretty.json`).then(({ data }) => data.$defs);

export const useGetConfigurationDescriptions = ({ enabled }: { enabled: boolean }) =>
  useQuery(['get-configuration-descriptions'], () => getConfigDescriptions(axiosProv.defaults.baseURL ?? ''), {
    staleTime: Infinity,
    enabled,
  });
export interface AuthProviderProps {
  token?: string;
  children: React.ReactNode;
}

export interface AuthProviderReturn {
  avatar: string;
  refetchUser: () => void;
  refetchAvatar: () => void;
  user?: User;
  token?: string;
  setToken: (token: string) => void;
  logout: () => void;
  getPref: (preference: string) => string | null;
  setPref: ({ preference, value }: { preference: string; value: string }) => void;
  setPrefs: (preferencesToUpdate: Preference[]) => void;
  deletePref: (preference: string | string[]) => void;
  ref: React.MutableRefObject<undefined>;
  endpoints: { [key: string]: string } | null;
  configurationDescriptions: Record<string, unknown>;
  isUserLoaded: boolean;
}
