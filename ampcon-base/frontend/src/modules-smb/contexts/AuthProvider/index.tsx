import React, {useState, useMemo, useEffect, useRef} from "react";
import {AuthProviderProps, AuthProviderReturn, useGetConfigurationDescriptions} from "./utils";
import {
    useDeleteAccountToken,
    useGetAvatar,
    useGetPreferences,
    useGetProfile,
    useUpdatePreferences
} from "@/modules-smb/hooks/Network/Account";
import {useGetEndpoints} from "@/modules-smb/hooks/Network/Endpoints";
import {Endpoint} from "@/modules-smb/models/Endpoint";
import {Preference} from "@/modules-smb/models/Preference";
import {
    axiosAnalytics,
    axiosFms,
    axiosGw,
    axiosInstaller,
    axiosOwls,
    axiosProv,
    axiosRrm,
    axiosSec,
    axiosSub
} from "@/modules-smb/utils/axiosInstances";

export let globalConfigurationDescriptions: any = undefined;

const AuthContext = React.createContext({} as AuthProviderReturn);

function getHost() {
    return window.location.protocol + "//" + window.location.host;
}
function getContentAfterLastColon(url: string) {
    const host = getHost();
    // 找到最后一个冒号的索引
    const lastColonIndex = url.lastIndexOf(":");
    // 如果没有找到冒号，返回空字符串
    if (lastColonIndex === -1) {
        return host;
    }
    // 截取最后一个冒号后的内容
    return host + url.substring(lastColonIndex);
}
export const AuthProvider = ({token = "", children = Object.create(null) as React.ReactElement}: AuthProviderProps) => {
    const ref = useRef();
    const [loadedEndpoints, setLoadedEndpoints] = useState(false);
    console.log("AuthProvider token:", token);
    const [currentToken, setCurrentToken] = useState(token);
    const [endpoints, setEndpoints] = useState<{[key: string]: string} | null>(null);
    const {data: configurationDescriptions} = useGetConfigurationDescriptions({enabled: loadedEndpoints});

    // 修改scheme中的描述
    if (configurationDescriptions && configurationDescriptions.unit) {
        console.log("configurationDescriptions:", configurationDescriptions);
        Object.keys(configurationDescriptions).forEach(key => {
            const value = configurationDescriptions[key];
            if (key === "unit" && value.properties && value.properties.hostname) {
                value.properties.hostname.description = "The Device name configured in Wireless > Inventory will be synchronized to the AP.";
                // console.log("hostname:", configurationDescriptions?.unit?.properties?.hostname?.description);
            }
            if(key==="radio.rates"&&value.properties){
                if(value.properties.beacon){
                    value.properties.beacon.description = "The beacon rate that shall be used by the BSS. Values are in Kbps.";
                }
                if(value.properties.multicast){
                    value.properties.multicast.description ="The multicast rate that shall be used by the BSS. Values are in Kbps."
                }
            }
            if(key==="service.lldp"&&value.properties){
                if(value.properties.describe){
                    value.properties.describe.description="The LLDP description field.";
                }
                if(value.properties.location){
                    value.properties.location.description="The LLDP location field.";
                }
            }
            if(key==="interface.ipv4"&&value.properties){
                if(value.properties.subnet){
                    value.properties.subnet.description="This option defines the static IPv4 of the logical interface in CIDR notation.";
                }
            }
            if(key==="interface.ipv6"&&value.properties){
                if(value.properties.subnet){
                    value.properties.subnet.description="This option defines a static IPv6 prefix in CIDR notation to set on the logical interface.";
                }

            }
            if(key==="interface"&&value.properties){
                if(value.properties.role){
                    value.properties.role.description="The role defines if the interface is Bridged Mode (Layer 2 bridging) or Routing Mode (NAT) facing.";
                }
                value.properties.ipAddress={
                                type: "string",
                                description: "config AP IP address & vlan"
                };
                value.properties.DhcpService={
                                type: "string",
                                description: "The AP acts as a DHCp server to assign IP addresses to clients"
                };
            }
        });
    }
    if (configurationDescriptions) {
        globalConfigurationDescriptions = configurationDescriptions;
        Object.keys(configurationDescriptions).forEach(key => {
            const value = configurationDescriptions[key];
             if(key==="interface.ssid.encryption" && value.properties && value.properties.proto) {
             value.properties.proto.description = "The wireless encryption protocol that shall be used for this BSS,When the wifi-band is 6G, only wpa3 and OWE take effect"; // Your new description here
            }
        });
    }
    
    const {data: user, refetch: refetchUser} = useGetProfile();
    const {refetch: refetchEndpoints} = useGetEndpoints({
        onSuccess: (newEndpoints: Endpoint[]) => {
            const foundEndpoints: {[key: string]: string} = {};
            let host = getHost();
            for (const endpoint of newEndpoints) {
                console.log(endpoint.type + "---" + endpoint.uri);
                let url = getContentAfterLastColon(endpoint.uri);
                console.log("AuthProvider url:" + url);
                foundEndpoints[endpoint.type] = url;
                switch (endpoint.type) {
                    case "owprov":
                        axiosProv.defaults.baseURL = `${host}/smb/owprov/api/v1`;
                        break;
                    case "owfms":
                        axiosFms.defaults.baseURL = `${host}/smb/owfms/api/v1`;
                        break;
                    case "owgw":
                        axiosGw.defaults.baseURL = `${host}/smb/owgw/api/v1`;
                        break;
                    case "owsub":
                        axiosSub.defaults.baseURL = `${host}/smb/owsub/api/v1`;
                        break;
                    case "owls":
                        axiosOwls.defaults.baseURL = `${host}/smb/owls/api/v1`;
                        break;
                    case "owanalytics":
                        axiosAnalytics.defaults.baseURL = `${host}/smb/owanalytics/api/v1`;
                        break;
                    case "owinstaller":
                        axiosInstaller.defaults.baseURL = `${host}/smb/owinstaller/api/v1`;
                        break;
                    case "owrrm":
                        axiosRrm.defaults.baseURL = `${host}/smb/owrrm/api/v1`;
                        break;
                    default:
                        break;
                }
                console.log("axiosOwls.defaults.baseURL url:" + axiosOwls.defaults.baseURL);
            }
            setEndpoints(foundEndpoints);
            setLoadedEndpoints(true);
        }
    });
    const userId = user?.id ?? "";
    const userAvatar = user?.avatar ?? "";
    const {data: preferences} = useGetPreferences({enabled: !!userId});
    const {data: avatar, refetch: refetchAvatar} = useGetAvatar({
        id: userId,
        enabled: !!userId && userAvatar !== "0" && userAvatar !== "",
        cache: userAvatar
    });
    const updatePreferences = useUpdatePreferences();

    const logout = useDeleteAccountToken({setCurrentToken});
    const logoutUser = () => logout.mutateAsync(currentToken ?? "");

    const getPref = (preference: string) => {
        for (const pref of preferences ?? []) {
            if (pref.tag === preference) return pref.value;
        }
        return null;
    };

    const setPref = async ({preference, value}: {preference: string; value: string}) => {
        let updated = false;
        if (preferences) {
            const newPreferences: Preference[] = preferences.map((pref: Preference) => {
                if (pref.tag === preference) {
                    updated = true;
                    return {tag: pref.tag, value};
                }
                return pref;
            });

            if (!updated) newPreferences.push({tag: preference, value});

            await updatePreferences.mutateAsync(newPreferences);
        }
    };

    const setPrefs = async (preferencesToUpdate: Preference[]) => {
        if (preferences) {
            const updatedPreferences: string[] = [];
            const newPreferences = preferences.map((pref: Preference) => {
                const preferenceToUpdate = preferencesToUpdate.find(
                    (prefToUpdate: Preference) => prefToUpdate.tag === pref.tag
                );
                if (preferenceToUpdate) {
                    updatedPreferences.push(pref.tag);
                    return {tag: pref.tag, value: preferenceToUpdate.value};
                }
                return pref;
            });

            for (const preferenceToUpdate of preferencesToUpdate) {
                if (!updatedPreferences.includes(preferenceToUpdate.tag)) {
                    newPreferences.push(preferenceToUpdate);
                }
            }

            await updatePreferences.mutateAsync(newPreferences);
        }
    };

    const deletePref = async (preference: string | string[]) => {
        if (preferences) {
            const newPreferences: Preference[] = preferences.filter((pref: Preference) =>
                typeof preference === "string" ? pref.tag !== preference : !preference.includes(pref.tag)
            );

            await updatePreferences.mutateAsync(newPreferences);
        }
    };

    useEffect(() => {
        if (currentToken) {
            console.log("AuthProvider setting token:", currentToken);
            axiosSec.defaults.headers.common.Authorization = `Bearer ${currentToken}`;
            axiosGw.defaults.headers.common.Authorization = `Bearer ${currentToken}`;
            axiosProv.defaults.headers.common.Authorization = `Bearer ${currentToken}`;
            axiosFms.defaults.headers.common.Authorization = `Bearer ${currentToken}`;
            axiosSub.defaults.headers.common.Authorization = `Bearer ${currentToken}`;
            axiosOwls.defaults.headers.common.Authorization = `Bearer ${currentToken}`;
            axiosAnalytics.defaults.headers.common.Authorization = `Bearer ${currentToken}`;
            axiosInstaller.defaults.headers.common.Authorization = `Bearer ${currentToken}`;
            axiosRrm.defaults.headers.common.Authorization = `Bearer ${currentToken}`;
            refetchUser();
            refetchEndpoints();
        }
    }, [currentToken]);

    const value: AuthProviderReturn = useMemo(
        () => ({
            avatar: avatar?.data
                ? `data:;base64,${btoa(
                      new Uint8Array(avatar.data).reduce((data, byte) => data + String.fromCharCode(byte), "")
                  )}`
                : "",
            refetchUser,
            refetchAvatar,
            user,
            token: currentToken,
            setToken: setCurrentToken,
            logout: logoutUser,
            ref,
            getPref,
            setPref,
            setPrefs,
            deletePref,
            endpoints,
            configurationDescriptions,
            isUserLoaded: preferences !== undefined && user !== undefined && loadedEndpoints
        }),
        [currentToken, user, avatar, preferences, loadedEndpoints, configurationDescriptions, endpoints, ref]
    );

    return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
// AuthProvider.defaultProps = {
//   token: '',
// };

export const useAuth: () => AuthProviderReturn = () => React.useContext(AuthContext);
