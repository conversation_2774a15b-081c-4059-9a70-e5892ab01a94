import React, { useCallback, useEffect, useMemo } from 'react';
// import useWebSocketNotification from './hooks/NotificationContent/useWebSocketNotification';
import useWebSocketNotification from '@/modules-smb/Wireless/contexts/ProvisioningSocketProvider/hooks/NotificationContent/useWebSocketNotification';
import { useProvisioningStore } from './useStore';
import { ProvisioningSocketRawMessage } from './utils';
import { useAuth } from '@/modules-smb/contexts/AuthProvider';
import { axiosProv, axiosSec } from '@/modules-smb/utils/axiosInstances';

export type ProvisioningSocketContextReturn = Record<string, unknown>;

const ProvisioningSocketContext = React.createContext<ProvisioningSocketContextReturn>({
  webSocket: undefined,
  isOpen: false,
});

// Object.create(null) as React.ReactElement
export const ProvisioningSocketProvider = ({ children = Object.create(null) as React.ReactElement }: { children: React.ReactElement }) => {
  const { token, isUserLoaded } = useAuth();
  const { pushNotification, modal } = useWebSocketNotification();
  const { addMessage, isOpen, webSocket, onStartWebSocket } = useProvisioningStore((state) => ({
    addMessage: state.addMessage,
    isOpen: state.isWebSocketOpen,
    webSocket: state.webSocket,
    onStartWebSocket: state.startWebSocket,
  }));

  const onMessage = useCallback((message: MessageEvent<string>) => {
    // console.log('WebSocket message received:', message.data);
    try {
      const data = JSON.parse(message.data) as ProvisioningSocketRawMessage | undefined;
      if (data) {
        addMessage(data, pushNotification);
      }
      return undefined;
    } catch {
      return undefined;
    }
  }, []);

  // useEffect for created the WebSocket and 'storing' it in useRef
  useEffect(() => {
    if (isUserLoaded && axiosProv?.defaults?.baseURL !== axiosSec?.defaults?.baseURL) {
      onStartWebSocket(token ?? '');
    }

    const wsCurrent = webSocket;
    return () => wsCurrent?.close();
  }, [isUserLoaded]);

  // useEffect for generating global notifications
  useEffect(() => {
    if (webSocket) {
      webSocket.addEventListener('message', onMessage);
      // console.log('WebSocket changed:', webSocket);
    }

    return () => {
      if (webSocket) webSocket.removeEventListener('message', onMessage);
    };
  }, [webSocket]);

  useEffect(() => {
    const handleVisibilityChange = () => {
      let timeoutId;

      if (webSocket) {
        if (document.visibilityState === 'hidden') {
          /* timeoutId = setTimeout(() => {
            if (webSocket) webSocket.onclose = () => {};
            webSocket?.close();
            setIsOpen(false);
          }, 5000); */
        } else {
          // If tab is active again, verify if browser killed the WS
          clearTimeout(timeoutId);

          if (!isOpen && isUserLoaded && axiosProv?.defaults?.baseURL !== axiosSec?.defaults?.baseURL) {
            onStartWebSocket(token ?? '');
          }
        }
      }
    };
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [webSocket, isOpen]);

  const values: ProvisioningSocketContextReturn = useMemo(() => ({}), []);

  // 假数据测试websocket弹窗
  // useEffect(() => {
  //   const fakeNotification: Parameters<typeof pushNotification>[0] = {
  //     notification_id: -1,
  //     type: 'venue_config_update',
  //     // type: 'venue_rebooter',
  //     // type: 'venue_fw_upgrade',
  //     type_id: 1000,
  //     content: {
  //       title: 'Updating Test Connfigurations',
  //       // details: '这是一个用于开发测试的假通知。请忽略。',
  //       success: ['Device A', 'Device B'],
  //       noFirmware: [],
  //       pending: [],
  //       notConnected: [],
  //       skipped: [],
  //       // warning: ['警告：部分设备未响应'],
  //       error: [],
  //       timeStamp: Date.now(),
  //     },
  //   };
  //   pushNotification(fakeNotification);
  // }, [pushNotification]);

  return (
    <ProvisioningSocketContext.Provider value={values}>
      <>
        {children}
        {modal}
      </>
    </ProvisioningSocketContext.Provider>
  );
};

export const useGlobalProvisioningSocket: () => ProvisioningSocketContextReturn = () =>
  React.useContext(ProvisioningSocketContext);
