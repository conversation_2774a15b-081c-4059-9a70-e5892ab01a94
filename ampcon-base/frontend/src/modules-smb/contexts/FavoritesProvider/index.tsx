import React from "react";
import {useAuth} from "@/modules-smb/contexts/AuthProvider";

const SETTING_NAME = "global.favorites";

export type EntityFavorite = {
    id: string;
    type: "venue" | "entity";
};

export type SettingValue = {
    entityFavorites: EntityFavorite[];
};

export interface FavoritesProviderReturn {
    entityFavorites: {
        favorites: EntityFavorite[];
        add: (entityFavorite: EntityFavorite) => Promise<void>;
        remove: (entityFavorite: EntityFavorite) => Promise<void>;
    };
    isLoading: boolean; // 添加加载状态
}

const FavoritesContext = React.createContext<FavoritesProviderReturn>({
    entityFavorites: {
        favorites: [],
        add: async () => {},
        remove: async () => {}
    },
    isLoading: false // 添加 isLoading 属性并指定初始值
});

export const FavoritesProvider = ({
    children = Object.create(null) as React.ReactElement
}: {
    children: React.ReactElement;
}) => {
    const authContext = useAuth();
    const [favorites, setFavorites] = React.useState<SettingValue>({
        entityFavorites: []
    });
     const [isLoading, setIsLoading] = React.useState(true); // 添加加载状态
    const fetchSetting = () => {
        setIsLoading(true); // 开始加载
        const newFavorites = authContext.getPref(SETTING_NAME);
        if (newFavorites) {
            try {
                setFavorites(JSON.parse(newFavorites));
            } catch (e) {
                authContext.deletePref(SETTING_NAME);
                setFavorites({entityFavorites: []});
            }
        }
         setIsLoading(false); // 加载完成
    };

    const addEntityFavorite = async (entityFavorite: EntityFavorite) => {
        // const newEntityFavorites = [...favorites.entityFavorites, entityFavorite];

        // setFavorites({ entityFavorites: newEntityFavorites });
        // await authContext.setPref({
        //   preference: SETTING_NAME,
        //   value: JSON.stringify({ entityFavorites: newEntityFavorites }),
        // });
        let newEntityFavorites: EntityFavorite[];

        if (entityFavorite.type === "venue") {
            // 如果添加的是 venue 类型，先过滤掉所有 venue 类型的收藏
            newEntityFavorites = favorites.entityFavorites.filter(favorite => favorite.type !== "venue");
        } else {
            // 否则，直接复制现有的收藏列表
            newEntityFavorites = [...favorites.entityFavorites];
        }

        // 添加新的收藏
        newEntityFavorites.push(entityFavorite);

        setFavorites({entityFavorites: newEntityFavorites});
        await authContext.setPref({
            preference: SETTING_NAME,
            value: JSON.stringify({entityFavorites: newEntityFavorites})
        });
    };

    const removeEntityFavorite = async (entityFavorite: EntityFavorite) => {
        const newEntityFavorites = favorites.entityFavorites.filter(
            favorite => favorite.id !== entityFavorite.id || favorite.type !== entityFavorite.type
        );

        setFavorites({entityFavorites: newEntityFavorites});
        await authContext.setPref({
            preference: SETTING_NAME,
            value: JSON.stringify({entityFavorites: newEntityFavorites})
        });
    };

    const value = React.useMemo(
        () => ({
            entityFavorites: {
                favorites: favorites.entityFavorites,
                add: addEntityFavorite,
                remove: removeEntityFavorite
            },
             isLoading // 提供加载状态
        }),
        [favorites.entityFavorites, isLoading ]
    );

    React.useEffect(() => {
        if (authContext.isUserLoaded) {
            fetchSetting();
        }
    }, [authContext.isUserLoaded]);

    return <FavoritesContext.Provider value={value}>{children}</FavoritesContext.Provider>;
};

export const useFavorites: () => FavoritesProviderReturn = () => React.useContext(FavoritesContext);
