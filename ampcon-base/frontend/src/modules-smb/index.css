.my-masonry-grid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-left: -20px;
  margin-bottom: -20px;
  width: auto;
}
.my-masonry-grid_column {
  padding-left: 20px;
  background-clip: padding-box;
}
.my-masonry-grid_column > div {
  margin-bottom: 20px;
}

.map-tooltip {
  width: 300px;
  background-color: white !important;
  border-radius: 15px !important;
  color: black !important;
  opacity: 1 !important;
  z-index: 1040 !important;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

/* When using a portal with chakra-react-select, we make sure it shows even within a modal */
.chakra-react-select__menu-portal {
  z-index: 9999 !important;
}

.tile-shadow-animate {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  -webkit-transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.tile-shadow-animate::after {
  top: 0;
  left: 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  opacity: 0;
  -webkit-transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.tile-shadow-animate:hover {
  -webkit-transform: scale(1.05, 1.05);
  transform: scale(1.05, 1.05);
}

.tile-shadow-animate:hover::after {
  opacity: 1;
}
