import React from 'react';
import { Route, Routes } from 'react-router-dom';
import darkLogo from '@/modules-smb/assets/Logo_Dark_Mode.svg';
import lightLogo from '@/modules-smb/assets/Logo_Light_Mode.svg';
import { useAuth } from '@/modules-smb/contexts/AuthProvider';

const Layout = React.lazy(() => import('@/modules-smb/layout'));
const Login = React.lazy(() => import('@/modules-smb/pages/LoginPage'));

const Router: React.FC = () => {
  const { token } = useAuth();

  return (
    <Routes>
      {token !== '' ? (
        <Route path="/*" element={<Layout />} />
      ) : (
        <Route path="/*" element={<Login lightLogo={lightLogo} darkLogo={darkLogo} />} />
      )}
    </Routes>
  );
};

export default Router;
