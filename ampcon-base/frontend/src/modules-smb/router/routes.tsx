import React from 'react';
import { Barcode, FloppyDisk, Info, ListBullets, TerminalWindow, UsersThree, WifiHigh, Storefront, Tag, TreeStructure } from '@phosphor-icons/react';
import EntityNavigationButton from '@/modules-smb/layout/Sidebar/EntityNavigationButton';
import { Route } from '@/modules-smb/models/Routes';

const AdvancedSystemPage = React.lazy(() => import('@/modules-smb/pages/AdvancedSystemPage'));
const DefaultConfigurationsPage = React.lazy(() => import('@/modules-smb/pages/DefaultConfigurations'));
const DefaultFirmwarePage = React.lazy(() => import('@/modules-smb/pages/DefaultFirmware'));
const DevicePage = React.lazy(() => import('@/modules-smb/pages/Device'));
const DashboardPage = React.lazy(() => import('@/modules-smb/pages/Devices/Dashboard'));
const AllDevicesPage = React.lazy(() => import('@/modules-smb/pages/Devices/ListCard'));
const BlacklistPage = React.lazy(() => import('@/modules-smb/pages/Devices/Blacklist'));
const ControllerLogsPage = React.lazy(() => import('@/modules-smb/pages/Notifications/GeneralLogs'));
const DeviceLogsPage = React.lazy(() => import('@/modules-smb/pages/Notifications/DeviceLogs'));
const ConfigurationPage = React.lazy(() => import('@/modules-smb/pages/ConfigurationPage'));
const EntityPage = React.lazy(() => import('@/modules-smb/pages/EntityPage'));
const InventoryPage = React.lazy(() => import('@/modules-smb/pages/InventoryPage'));
const OpenRoamingPage = React.lazy(() => import('@/modules-smb/pages/OpenRoamingPage'));
const ProvLogsPage = React.lazy(() => import('@/modules-smb/pages/Notifications/GeneralLogs'));
const VenueNotificationsPage = React.lazy(() => import('@/modules-smb/pages/Notifications/Notifications'));
const FmsLogsPage = React.lazy(() => import('@/modules-smb/pages/Notifications/FmsLogs'));
const SecLogsPage = React.lazy(() => import('@/modules-smb/pages/Notifications/SecLogs'));
const FirmwarePage = React.lazy(() => import('@/modules-smb/pages/Firmware/List'));
const FirmwareDashboard = React.lazy(() => import('@/modules-smb/pages/Firmware/Dashboard'));
const MapPage = React.lazy(() => import('@/modules-smb/pages/MapPage'));
const ProfilePage = React.lazy(() => import('@/modules-smb/pages/Profile'));
const ScriptsPage = React.lazy(() => import('@/modules-smb/pages/Scripts'));
const UsersPage = React.lazy(() => import('@/modules-smb/pages/UsersPage'));
const MonitoringPage = React.lazy(() => import('@/modules-smb/pages/MonitoringPage'));
const OperatorPage = React.lazy(() => import('@/modules-smb/pages/OperatorPage'));
const OperatorsPage = React.lazy(() => import('@/modules-smb/pages/OperatorsPage'));
const SubscriberPage = React.lazy(() => import('@/modules-smb/pages/SubscriberPage'));
const EndpointsPage = React.lazy(() => import('@/modules-smb/pages/EndpointsPage'));
//const MonitoringPage = React.lazy(() => import('@/modules-smb/pages/MonitoringPage'));
const SystemConfigurationPage = React.lazy(() => import('@/modules-smb/pages/SystemConfigurationPage'));
//const UsersPage = React.lazy(() => import('@/modules-smb/pages/UsersPage'));
const VenuePage = React.lazy(() => import('@/modules-smb/pages/VenuePage'));

const routes: Route[] = [
  {
    id: 'devices-group',
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    name: 'devices.title',
    icon: () => <WifiHigh size={28} weight="bold" />,
    children: [
      {
        id: 'devices-table',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/devices',
        name: 'devices.all',
        navName: 'devices.title',
        component: AllDevicesPage,
      },
      {
        id: 'devices-dashboard',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/devices_dashboard',
        name: 'analytics.dashboard',
        component: DashboardPage,
      },
      {
        id: 'devices-blacklist',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/devices_blacklist',
        name: 'controller.devices.blacklist',
        component: BlacklistPage,
      },
    ],
  },
  {
    id: 'firmware-group',
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    name: 'analytics.firmware',
    icon: () => <FloppyDisk size={28} weight="bold" />,
    children: [
      {
        id: 'firmware-table',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/firmware',
        name: 'devices.all',
        navName: 'analytics.firmware',
        component: FirmwarePage,
      },
      {
        id: 'firmware-dashboard',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/firmware/dashboard',
        name: 'analytics.dashboard',
        component: FirmwareDashboard,
      },
    ],
  },
  {
    id: 'scripts',
    authorized: ['root'],
    path: '/scripts/:id',
    name: 'script.other',
    icon: () => <TerminalWindow size={28} weight="bold" />,
    component: ScriptsPage,
  },
  {
    id: 'defaults-group',
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    name: 'common.defaults',
    icon: () => <Barcode size={28} weight="bold" />,
    children: [
      {
        id: 'configurations',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/configurations',
        name: 'configurations.title',
        component: DefaultConfigurationsPage,
      },
      {
        id: 'default_firmware',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/firmware/defaults',
        name: 'firmware.one',
        component: DefaultFirmwarePage,
      },
    ],
  },
  {
    id: 'entity-page',
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    path: '/entity/:id',
    name: 'entities.title',
    navName: '',
    icon: () => <TreeStructure size={28} weight="bold" />,
    navButton: (_, toggleSidebar: () => void, route: Route) => (
      <EntityNavigationButton toggleSidebar={toggleSidebar} route={route} />
    ),
    isEntity: true,
    component: EntityPage,
  },
  {
    id: 'venue-page',
    hidden: true,
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    path: '/venue/:id',
    name: 'venues.title',
    navName: '',
    icon: () => <TreeStructure size={28} weight="bold" />,
    isEntity: true,
    component: VenuePage,
  },
  {
    id: 'inventory-page',
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    path: '/',
    name: 'inventory.title',
    icon: () => <Tag size={28} weight="bold" />,
    component: InventoryPage,
  },
  {
    id: 'operators-page',
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    path: '/operators',
    name: 'operator.other',
    icon: () => <Storefront size={28} weight="bold" />,
    component: OperatorsPage,
  },
  {
    id: 'logs-group',
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    name: 'controller.devices.logs',
    icon: () => <ListBullets size={28} weight="bold" />,
    children: [
      {
        id: 'logs-devices',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/logs/devices',
        name: 'devices.title',
        navName: (t) => `${t('devices.one')} ${t('controller.devices.logs')}`,
        component: DeviceLogsPage,
      },
      {
        id: 'logs-controller',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/logs/controller',
        name: 'simulation.controller',
        navName: (t) => `${t('simulation.controller')} ${t('controller.devices.logs')}`,
        component: ControllerLogsPage,
      },
      {
        id: 'logs-devices',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/logs/notifications',
        name: 'venues.title',
        navName: (t) => `${t('venues.one')} ${t('notification.other')}`,
        component: VenueNotificationsPage,
      },
      {
        id: 'logs-prov',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/logs/provisioning',
        name: 'controller.provisioning.title',
        navName: (t) => `${t('controller.provisioning.title')} ${t('controller.devices.logs')}`,
        component: ProvLogsPage,
      },
      {
        id: 'logs-security',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/logs/security',
        name: 'logs.security',
        navName: (t) => `${t('logs.security')} ${t('controller.devices.logs')}`,
        component: SecLogsPage,
      },
      {
        id: 'logs-firmware',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/logs/firmware',
        name: 'logs.firmware',
        navName: (t) => `${t('logs.firmware')} ${t('controller.devices.logs')}`,
        component: FmsLogsPage,
      },
    ],
  },
  {
    id: 'device-page',
    hidden: true,
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    path: '/devices/:id',
    name: 'devices.one',
    navName: 'PATH',
    icon: () => <WifiHigh size={28} weight="bold" />,
    component: DevicePage,
  },
  {
    id: 'account-page',
    hidden: true,
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    path: '/account',
    name: 'account.title',
    icon: () => <UsersThree size={28} weight="bold" />,
    component: ProfilePage,
  },
  {
    id: 'users-page',
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    path: '/users',
    name: 'users.title',
    icon: () => <UsersThree size={28} weight="bold" />,
    component: UsersPage,
  },
  {
    id: 'system-group',
    authorized: ['root', 'partner', 'admin'],
    name: 'system.title',
    icon: () => <Info size={28} weight="bold" />,
    children: [
      {
        id: 'system-advanced',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/systemAdvanced',
        name: 'system.advanced',
        component: AdvancedSystemPage,
      },
      {
        id: 'system-configuration',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/systemConfiguration',
        name: 'system.configuration',
        component: SystemConfigurationPage,
      },
      {
        id: 'system-globalroaming',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/openRoaming',
        name: 'RAW-OpenRoaming',
        label: 'OpenRoaming',
        component: OpenRoamingPage,
      },
      {
        id: 'system-monitoring',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/systemMonitoring',
        name: 'analytics.monitoring',
        component: MonitoringPage,
      },
      {
        id: 'system-services',
        authorized: ['root', 'partner', 'admin', 'csr', 'system'],
        path: '/services',
        name: 'system.services',
        component: EndpointsPage,
      },
    ],
  },
  {
    id: 'account-page',
    hidden: true,
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    path: '/account',
    name: 'account.title',
    icon: () => <UsersThree size={28} weight="bold" />,
    component: ProfilePage,
  },
  {
    id: 'configuration-page',
    hidden: true,
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    path: '/configuration/:id',
    name: 'configurations.one',
    icon: () => <UsersThree size={28} weight="bold" />,
    component: ConfigurationPage,
  },
  {
    id: 'operator-page',
    hidden: true,
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    path: '/operators/:id',
    name: 'operator.one',
    icon: () => <UsersThree size={28} weight="bold" />,
    component: OperatorPage,
  },
  {
    id: 'subscriber-page',
    hidden: true,
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    path: '/subscriber/:id',
    name: 'subscribers.one',
    icon: () => <UsersThree size={28} weight="bold" />,
    component: SubscriberPage,
  },
  {
    id: 'map-page',
    hidden: true,
    authorized: ['root', 'partner', 'admin', 'csr', 'system'],
    path: '/map',
    name: 'common.map',
    icon: () => <UsersThree size={28} weight="bold" />,
    component: MapPage,
  },
];

export default routes;
