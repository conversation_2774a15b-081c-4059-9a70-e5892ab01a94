import React from 'react';
import { Barcode, FloppyDisk, Info, ListBullets, TerminalWindow, UsersThree, WifiHigh, Storefront, Tag, TreeStructure } from '@phosphor-icons/react';
import EntityNavigationButton from '@/modules-smb/layout/Sidebar/EntityNavigationButton';
import { Route } from '@/modules-smb/models/Routes';
import ProtectedRoute from "@/modules-ampcon/utils/util";
import darkLogo from '@/modules-smb/assets/Logo_Dark_Mode.svg';
import lightLogo from '@/modules-smb/assets/Logo_Light_Mode.svg';
// import AllDevicesPage from "@/modules-smb/pages/Devices/ListCard"
// import DashboardPage from "@/modules-smb/pages/Devices/Dashboard"
// import BlacklistPage from "@/modules-smb/pages/Devices/Blacklist"
import LoginPage from "@/modules-smb/pages/LoginPage";
const LogoutPage = React.lazy(() => import('@/modules-smb/pages/LogoutPage'));
const AdvancedSystemPage = React.lazy(() => import('@/modules-smb/pages/AdvancedSystemPage'));
const DefaultConfigurationsPage = React.lazy(() => import('@/modules-smb/pages/DefaultConfigurations'));
const DefaultFirmwarePage = React.lazy(() => import('@/modules-smb/pages/DefaultFirmware'));
const DevicePage = React.lazy(() => import('@/modules-smb/pages/Device'));
const DashboardPage = React.lazy(() => import('@/modules-smb/pages/Devices/Dashboard'));
const AllDevicesPage = React.lazy(() => import('@/modules-smb/pages/Devices/ListCard'));
const BlacklistPage = React.lazy(() => import('@/modules-smb/pages/Devices/Blacklist'));
const ControllerLogsPage = React.lazy(() => import('@/modules-smb/pages/Notifications/GeneralLogs'));
const DeviceLogsPage = React.lazy(() => import('@/modules-smb/pages/Notifications/DeviceLogs'));
const ConfigurationPage = React.lazy(() => import('@/modules-smb/pages/ConfigurationPage'));
const EntityPage = React.lazy(() => import('@/modules-smb/pages/EntityPage'));
const InventoryPage = React.lazy(() => import('@/modules-smb/pages/InventoryPage'));
const OpenRoamingPage = React.lazy(() => import('@/modules-smb/pages/OpenRoamingPage'));
const ProvLogsPage = React.lazy(() => import('@/modules-smb/pages/Notifications/GeneralLogs'));
const VenueNotificationsPage = React.lazy(() => import('@/modules-smb/pages/Notifications/Notifications'));
const FmsLogsPage = React.lazy(() => import('@/modules-smb/pages/Notifications/FmsLogs'));
const SecLogsPage = React.lazy(() => import('@/modules-smb/pages/Notifications/SecLogs'));
const FirmwarePage = React.lazy(() => import('@/modules-smb/pages/Firmware/List'));
const FirmwareDashboard = React.lazy(() => import('@/modules-smb/pages/Firmware/Dashboard'));
const MapPage = React.lazy(() => import('@/modules-smb/pages/MapPage'));
const ProfilePage = React.lazy(() => import('@/modules-smb/pages/Profile'));
const ScriptsPage = React.lazy(() => import('@/modules-smb/pages/Scripts'));
const UsersPage = React.lazy(() => import('@/modules-smb/pages/UsersPage'));
const MonitoringPage = React.lazy(() => import('@/modules-smb/pages/MonitoringPage'));
const OperatorPage = React.lazy(() => import('@/modules-smb/pages/OperatorPage'));
const OperatorsPage = React.lazy(() => import('@/modules-smb/pages/OperatorsPage'));
const SubscriberPage = React.lazy(() => import('@/modules-smb/pages/SubscriberPage'));
const EndpointsPage = React.lazy(() => import('@/modules-smb/pages/EndpointsPage'));
const SystemConfigurationPage = React.lazy(() => import('@/modules-smb/pages/SystemConfigurationPage'));
const VenuePage = React.lazy(() => import('@/modules-smb/pages/VenuePage'));

const Layout = React.lazy(() => import('@/modules-smb/layout'));

const defaultRoute: Route = {
    // 根据 Route 接口定义添加必要的属性
    title: 'Entities',
    icon: <TreeStructure />,
    path: 'wireless/entities',
    // 其他必要的属性
};
const getModules = () => {
    let smbRoute= [
        {
            path: "wireless/login",
            element: <ProtectedRoute component={LoginPage} />
        },
        {
            path: "wireless/logout",
            element: <ProtectedRoute component={LogoutPage} />
        },
        {
            path: "wireless/devices",
            element: <AllDevicesPage/>
        },
        {
            path: "wireless/devices/:id",
            element: <DevicePage/>
        },
        {
            path: "wireless/devices/dashboard",
            element: <DashboardPage/>
        },
        {
            path: "wireless/devices/blacklist",
            element: <BlacklistPage/>
        },
        {
            path: "wireless/firmware/list",
            element: <FirmwarePage/>
        },
        {
            path: "wireless/firmware/dashboard",
            element: <FirmwareDashboard/>
        },
        {
            path: "wireless/scripts",
            element: <ScriptsPage/>
        },
        {
            path: "wireless/defaults/configuration",
            element: <DefaultConfigurationsPage/>
        },
        {
            path: "wireless/defaults/firmware",
            element: <DefaultFirmwarePage/>
        },
        {
            path: "wireless/entities",
            element: <EntityNavigationButton toggleSidebar={() => {}} route={defaultRoute}/>
            // (_, toggleSidebar: () => void, route: Route) => (
            //     <EntityNavigationButton toggleSidebar={toggleSidebar} route={route} />
            //   )
              // <EntityNavigationButton toggleSidebar={() => {}} route={defaultRoute}/>
        },
        {
            path: "wireless/maps",
            element: <MapPage/>
        },
        {
            path: "wireless/entity/:id",
            element: <EntityPage/>
        },
        {
            path: "wireless/venue/:id",
            element: <VenuePage/>
        },
        {
            path: "wireless/inventory",
            element: <InventoryPage/>
        },
        {
            path: "wireless/operators",
            element: <OperatorsPage/>
        },
        {
            path: "wireless/logs/devices",
            element: <DeviceLogsPage/>
        },
        {
            path: "wireless/logs/controller",
            element: <ControllerLogsPage/>
        },
        {
            path: "wireless/logs/venues",
            element: <VenueNotificationsPage/>
        },
        {
            path: "wireless/logs/provisioning",
            element: <ProvLogsPage/>
        },
        {
            path: "wireless/logs/security",
            element: <SecLogsPage/>
        },
        {
            path: "wireless/logs/firmware",
            element: <FmsLogsPage/>
        },
        {
            path: "wireless/users",
            element: <UsersPage/>
        },
        {
            path: "wireless/system/advanced",
            element: <AdvancedSystemPage/>
        },
        {
            path: "wireless/system/configuration",
            element: <SystemConfigurationPage/>
        },
        {
            path: "wireless/system/openroaming",
            element: <OpenRoamingPage/>
        },
        {
            path: "wireless/system/monitoring",
            element: <MonitoringPage/>
        },
        {
            path: "wireless/system/services",
            element: <EndpointsPage />
        },
        {
            path: "wireless/configuration/:id",
            element: <ConfigurationPage />
        },
        {
            path: "wireless/account",
            element: <ProfilePage />
        },
        {
            path: "wireless/operators/:id",
            element: <OperatorPage />
        },
        {
            path: "wireless/subscriber/:id",
            element: <SubscriberPage />
        },
        // {
        //     path: "wireless/layout",
        //     element: <Layout />
        // },
    ];

return {
    smbRoute
    };
};

export const {smbRoute} = getModules();
