import {
    axiosAnalytics,
    axiosFms,
    axiosGw,
    axiosInstaller,
    axiosOwls,
    axiosProv,
    axiosRrm,
    axiosSec,
    axiosSub
} from "@/modules-smb/utils/axiosInstances";

function getHost() {
    return window.location.protocol + "//" + window.location.host;
}

function getContentAfterLastColon(url: string) {
    const host = getHost();
    // 找到最后一个冒号的索引
    const lastColonIndex = url.lastIndexOf(":");
    // 如果没有找到冒号，返回空字符串
    if (lastColonIndex === -1) {
        return host;
    }
    // 截取最后一个冒号后的内容
    return host + url.substring(lastColonIndex);
}
export const login_smb = async (username: string, password: string, remember: boolean) => {
    try {
        const response = await axiosSec.post<{access_token: string}>("oauth2", {userId: username, password: password, rememberMe: remember});
        const token = response.data.access_token;
        localStorage.setItem("access_token", token);
        sessionStorage.setItem("access_token", token);
        
        // 设置token
        console.log("login_smb setting token:", token);
        axiosSec.defaults.headers.common.Authorization = `Bearer ${token}`;
        axiosGw.defaults.headers.common.Authorization = `Bearer ${token}`;
        axiosProv.defaults.headers.common.Authorization = `Bearer ${token}`;
        axiosFms.defaults.headers.common.Authorization = `Bearer ${token}`;
        axiosSub.defaults.headers.common.Authorization = `Bearer ${token}`;
        axiosOwls.defaults.headers.common.Authorization = `Bearer ${token}`;
        axiosAnalytics.defaults.headers.common.Authorization = `Bearer ${token}`;
        axiosInstaller.defaults.headers.common.Authorization = `Bearer ${token}`;
        axiosRrm.defaults.headers.common.Authorization = `Bearer ${token}`;
        // 获取Endpoints
        const rsp = await axiosSec.get("systemEndpoints");
        let host = getHost();
        for (const endpoint of rsp.data.endpoints) {
            let url = getContentAfterLastColon(endpoint.uri);
            console.log("login_smb url:" + url);
            switch (endpoint.type) {
                case "owprov":
                    axiosProv.defaults.baseURL = `${host}/smb/owprov/api/v1`;
                    break;
                case "owfms":
                    axiosFms.defaults.baseURL = `${host}/smb/owfms/api/v1`;
                    break;
                case "owgw":
                    axiosGw.defaults.baseURL = `${host}/smb/owgw/api/v1`;
                    break;
                case "owsub":
                    axiosSub.defaults.baseURL = `${host}/smb/owsub/api/v1`;
                    break;
                case "owls":
                    axiosOwls.defaults.baseURL = `${host}/smb/owls/api/v1`;
                    break;
                case "owanalytics":
                    axiosAnalytics.defaults.baseURL = `${host}/smb/owanalytics/api/v1`;
                    break;
                case "owinstaller":
                    axiosInstaller.defaults.baseURL = `${host}/smb/owinstaller/api/v1`;
                    break;
                case "owrrm":
                    axiosRrm.defaults.baseURL = `${host}/smb/owrrm/api/v1`;
                    break;
                default:
                    break;
            }
        }
    } catch (error) {
        console.error("login smb fail!", error);
    }
};

export const logout_smb = async () => {
    const storageToken = localStorage.getItem("access_token") ?? sessionStorage.getItem("access_token");
    console.log("logout smb success start", storageToken);
    try {
        if (storageToken) {
            await axiosSec
                .delete(`/oauth2/${storageToken}`)
                .then(() => true)
                .catch(() => false);

            localStorage.removeItem("access_token");
            sessionStorage.removeItem("access_token");
            console.log("logout smb success");
        } else {
            console.log("No token found to logout");
        }
    } catch (error) {
        console.error("logout smb fail!", error);
    }
};
