import { Ref, useCallback, useMemo, useState } from 'react';
import { FormikProps } from 'formik';
import { FormType } from '@/modules-smb/models/Form';

const useFormRef = <Type = Record<string, unknown>>() => {
  const [form, setForm] = useState<FormType>({
    submitForm: () => {},
    isSubmitting: false,
    isValid: true,
    dirty: false,
  });
  const formRef = useCallback(
    (node: FormikProps<Type>) => {
      if (
        node &&
        (form.submitForm !== node.submitForm ||
          form.isSubmitting !== node.isSubmitting ||
          form.isValid !== node.isValid ||
          form.dirty !== node.dirty)
      ) {
        setForm(node);
      }
    },
    [form],
  ) as Ref<FormikProps<Type>>;

  const toReturn = useMemo(() => ({ form, formRef }), [form]);

  return toReturn;
};

export default useFormRef;
