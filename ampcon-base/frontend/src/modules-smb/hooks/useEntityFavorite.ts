import * as React from 'react';
import { useFavorites } from '@/modules-smb/contexts/FavoritesProvider';

export type UseEntityFavoriteProps = {
  id: string;
  type: 'venue' | 'entity';
};

export const useEntityFavorite = ({ id, type }: UseEntityFavoriteProps) => {
  const favoriteContext = useFavorites();
  const [isLoading, setIsLoading] = React.useState(false);
  const isReady = !favoriteContext.isLoading;
  // const isFavorite = favoriteContext.entityFavorites.favorites.some(({ id: entityId }) => entityId === id);

  const isFavorite = React.useMemo(() => {
    // 只有当数据加载完成后才计算 isFavorite
    return isReady
      ? favoriteContext.entityFavorites.favorites.some(({ id: entityId }) => entityId === id)
      : false;
  }, [id, isReady, favoriteContext.entityFavorites.favorites]);

  const onFavoriteClick = async () => {
    setIsLoading(true);
    if (isFavorite) {
      await favoriteContext.entityFavorites.remove({ id, type });
    } else {
      await favoriteContext.entityFavorites.add({ id, type });
    }
    setIsLoading(false);
  };

  const getFirstVenueFavoriteId = () => {
    const venueFavorite = favoriteContext.entityFavorites.favorites.find(favorite => favorite.type === 'venue');
    return venueFavorite ? venueFavorite.id : null;
  };

  return {
    isFavorite,
    onFavoriteClick,
    isLoading,
    getFirstVenueFavoriteId,
    isReady
  };
};
