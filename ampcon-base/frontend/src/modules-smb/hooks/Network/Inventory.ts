import { useToast } from '@chakra-ui/react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import { AxiosError } from '@/modules-smb/models/Axios';
import { PageInfo, SortInfo } from '@/modules-smb/models/Table';
import { axiosProv } from '@/modules-smb/utils/axiosInstances';
import { useGetBlacklistDevices } from '@/modules-smb/hooks/Network/Blacklist';
import { message } from 'antd'; 
export const useGetSelectInventoryPaginated = ({
  serialNumbers,
  pageInfo,
  sortInfo,
}: {
  serialNumbers: string[];
  pageInfo?: PageInfo;
  sortInfo?: SortInfo;
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  const paginatedSerials = pageInfo
    ? serialNumbers.slice(pageInfo.limit * pageInfo.index, pageInfo.limit * (pageInfo.index + 1))
    : [];
  let sortString = '';
  if (sortInfo && sortInfo.length > 0) {
    sortString = `&orderBy=${sortInfo.map((info) => `${info.id}:${info.sort.charAt(0)}`).join(',')}`;
  }

  return useQuery(
    ['get-inventory-with-select', serialNumbers, pageInfo],
    () =>
      paginatedSerials.length > 0
        ? axiosProv
            .get(`inventory?withExtendedInfo=true&select=${paginatedSerials}${sortString}`)
            .then(({ data }) => data.taglist)
        : [],
    {
      staleTime: 30000,
      keepPreviousData: true,
      onError: (e: AxiosError) => {
        if (!toast.isActive('get-inventory-tags-fetching-error'))
          toast({
            id: 'get-inventory-tags-fetching-error',
            title: t('common.error'),
            description: t('crud.error_fetching_obj', {
              obj: t('inventory.tags'),
              e: e?.response?.data?.ErrorDescription,
            }),
            status: 'error',
            duration: 5000,
            isClosable: true,
            position: 'top-right',
          });
      },
    },
  );
};

export const useGetInventoryTableSpecs = () =>
  useQuery(
    ['get-inventory-table-spec'],
    () => axiosProv.get(`inventory?orderSpec=true`).then(({ data }) => data.list),
    {
      staleTime: Infinity,
    },
  );

export const useGetInventoryCount = ({
  enabled,
  onlyUnassigned = false,
  isSubscribersOnly = false,
  venueId, // 添加venueId参数
}: {
  enabled: boolean;
  onlyUnassigned?: boolean;
  isSubscribersOnly?: boolean;
  venueId?: string; // 站点ID，可选参数
}) => {
  const { t } = useTranslation();
  const toast = useToast();

  return useQuery(
    ['get-inventory-count', onlyUnassigned, venueId], // 更新查询键，包含venueId
    () =>
      axiosProv
        .get(
          `inventory?countOnly=true${onlyUnassigned ? '&unassigned=true' : ''}${
            isSubscribersOnly ? '&subscribersOnly=true' : ''
          }${venueId !== undefined && venueId !== null ? `&venue=${venueId}` : ''}` // 动态添加venue参数
        )
        .then(({ data }) => data.count),
    {
      enabled,
      onError: (e: AxiosError) => {
        if (!toast.isActive('inventory-fetching-error'))
          toast({
            id: 'inventory-fetching-error',
            title: t('common.error'),
            description: t('crud.error_fetching_obj', {
              obj: t('inventory.one'),
              e: e?.response?.data?.ErrorDescription,
            }),
            status: 'error',
            duration: 5000,
            isClosable: true,
            position: 'top-right',
          });
      },
    },
  );
};

export const useGetInventoryTags = ({
  pageInfo,
  sortInfo,
  owner,
  tagSelect,
  enabled,
  count,
  onlyUnassigned = false,
  isSubscribersOnly = false,
  venueId, // 添加venueId参数
}: {
  pageInfo?: PageInfo;
  sortInfo?: SortInfo;
  owner?: string;
  tagSelect?: string[];
  enabled: boolean;
  count?: number;
  onlyUnassigned?: boolean;
  isSubscribersOnly?: boolean;
  venueId?: string; // 站点ID，可选参数
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  let sortString = '';
  if (sortInfo && sortInfo.length > 0) {
    sortString = `&orderBy=${sortInfo.map((info) => `${info.id}:${info.sort.charAt(0)}`).join(',')}`;
  }

  if (tagSelect !== undefined && tagSelect !== null) {
    return useQuery(
      ['get-inventory-with-select', tagSelect, venueId], // 更新查询键，包含venueId
      () =>
        tagSelect.length > 0
          ? axiosProv
              .get(
                `inventory?withExtendedInfo=true&select=${tagSelect}${
                  isSubscribersOnly ? '&subscribersOnly=true' : ''
                }${sortString}${venueId !== undefined && venueId !== null ? `&venue=${venueId}` : ''}` // 动态添加venue参数
              )
              .then(({ data }) => data.taglist)
          : [],
      {
        enabled,
        staleTime: 30000,
        keepPreviousData: true,
        onError: (e: AxiosError) => {
          if (!toast.isActive('get-inventory-tags-fetching-error'))
            toast({
              id: 'get-inventory-tags-fetching-error',
              title: t('common.error'),
              description: t('crud.error_fetching_obj', {
                obj: t('inventory.tags'),
                e: e?.response?.data?.ErrorDescription,
              }),
              status: 'error',
              duration: 5000,
              isClosable: true,
              position: 'top-right',
            });
        },
      },
    );
  }
  if (owner !== undefined && owner !== null) {
    return useQuery(
      ['get-inventory-with-owner', owner, venueId], // 更新查询键，包含venueId
      () =>
        axiosProv
          .get(`inventory?serialOnly=true&subscriber=${owner}${sortString}${venueId !== undefined && venueId !== null ? `&venue=${venueId}` : ''}`) // 动态添加venue参数
          .then(({ data }) => data.serialNumbers),
      {
        enabled,
        onError: (e: AxiosError) => {
          if (!toast.isActive('get-inventory-tags-fetching-error'))
            toast({
              id: 'get-inventory-tags-fetching-error',
              title: t('common.error'),
              description: t('crud.error_fetching_obj', {
                obj: t('inventory.tags'),
                e: e?.response?.data?.ErrorDescription,
              }),
              status: 'error',
              duration: 5000,
              isClosable: true,
              position: 'top-right',
            });
        },
      },
    );
  }

  return useQuery(
    ['get-inventory-with-pagination', pageInfo, count, onlyUnassigned, sortInfo, venueId], // 更新查询键，包含venueId
    () =>
      axiosProv
        .get(
          `inventory?withExtendedInfo=true&limit=${pageInfo?.limit ?? 10}&offset=${
            (pageInfo?.limit ?? 10) * (pageInfo?.index ?? 1)
          }${onlyUnassigned ? '&unassigned=true' : ''}${isSubscribersOnly ? '&subscribersOnly=true' : ''}${sortString}${venueId !== undefined && venueId !== null ? `&venue=${venueId}` : ''}` // 动态添加venue参数
        )
        .then(({ data }) => data.taglist),
    {
      keepPreviousData: true,
      enabled,
      staleTime: 30000,
      onError: (e: AxiosError) => {
        if (!toast.isActive('get-inventory-tags-fetching-error'))
          toast({
            id: 'get-inventory-tags-fetching-error',
            title: t('common.error'),
            description: t('crud.error_fetching_obj', {
              obj: t('inventory.tags'),
              e: e?.response?.data?.ErrorDescription,
            }),
            status: 'error',
            duration: 5000,
            isClosable: true,
            position: 'top-right',
          });
      },
    },
  );
};
export const useGetTag = ({ enabled, serialNumber }: { enabled: boolean; serialNumber: string }) => {
  const { t } = useTranslation();
  const toast = useToast();

  return useQuery(
    ['get-inventory-tag', serialNumber],
    () => axiosProv.get(`inventory/${serialNumber}?withExtendedInfo=true`).then(({ data }) => data),
    {
      enabled,
      onError: (e: AxiosError) => {
        if (!toast.isActive('tag-fetching-error'))
          toast({
            id: 'tag-fetching-error',
            title: t('common.error'),
            description: t('crud.error_fetching_obj', {
              obj: t('inventory.tag_one'),
              e: e?.response?.data?.ErrorDescription,
            }),
            status: 'error',
            duration: 5000,
            isClosable: true,
            position: 'top-right',
          });
      },
    },
  );
};

export type ComputedConfigurationExplanation =
  | {
      action: 'added';
      element: Record<string, unknown>;
      'from-name': string;
      'from-uuid': string;
      reason?: undefined;
    }
  | {
      action: 'ignored';
      'from-name': string;
      'from-uuid': string;
      reason: string;
    };
export type ComputedConfigurationResponse = {
  config: Record<string, unknown> | 'none';
  explanation: Partial<ComputedConfigurationExplanation>[];
};
export const useGetComputedConfiguration = ({ enabled, serialNumber }: { enabled: boolean; serialNumber: string }) => {
  const { t } = useTranslation();
  const toast = useToast();

  return useQuery(
    ['get-tag-computed-configuration', serialNumber],
    () =>
      axiosProv
        .get(`inventory/${serialNumber}?config=true&explain=true`)
        .then(({ data }) => data as ComputedConfigurationResponse),
    {
      enabled,
      staleTime: 30000,
      onError: (e: AxiosError) => {
        if (!toast.isActive('tag-computed-configuration-fetching-error'))
          toast({
            id: 'tag-fetching-error',
            title: t('common.error'),
            description: t('crud.error_fetching_obj', {
              obj: t('inventory.computed_configuration'),
              e: e?.response?.data?.ErrorDescription,
            }),
            status: 'error',
            duration: 5000,
            isClosable: true,
            position: 'top-right',
          });
      },
    },
  );
};

export const usePushConfig = ({
  onSuccess,
}: {
  onSuccess: (data?: unknown, variables?: void, context?: unknown) => void;
}) => {
  const { t } = useTranslation();
  const toast = useToast();

  // 获取黑名单设备列表
  const { data: blacklistDevices = [] } = useGetBlacklistDevices({
    pageInfo: { limit: 1000, index: 0 },
    enabled: true,
  });

  return useMutation(
    ['apply-tag-configuration'],
    (serialNumber: string) => {
      // 检查 serialNumber 是否在黑名单中
      const blacklistedSerialNumbers = blacklistDevices.devices.map((d) => d.serialNumber);
      const isBlacklisted = blacklistedSerialNumbers.includes(serialNumber);

      if (isBlacklisted) {
        return {errorCode: -1, msg: `${serialNumber} on the blacklist cannot be configured`};
      }

      // 如果不在黑名单中，调用原始接口
      return axiosProv.get(`inventory/${serialNumber}?applyConfiguration=true`).then(({ data }) => data);
    },
    {
      onSuccess,
      onError: (e: AxiosError) => {
        if (!toast.isActive('apply-tag-configuration'))
          toast({
            id: 'apply-tag-configuration-error',
            title: t('common.error'),
            description: t('configurations.push_configuration_error', {
              e: e?.response?.data?.ErrorDescription,
            }),
            status: 'error',
            duration: 5000,
            isClosable: true,
            position: 'top-right',
          });
      },
    },
  );
};

export const useDeleteTag = ({
  name,
  onClose,
  refreshTable,
}: {
  name: string;
  onClose?: () => void;
  refreshTable?: () => void;
}) => {
  const { t } = useTranslation();

  return useMutation((id: string) => axiosProv.delete(`/inventory/${id}`), {
    onSuccess: () => {
      if (onClose) onClose();
      if (refreshTable) refreshTable();
      // 成功提示：使用message替代toast
      message.success('Deleted successfully');
    },
    onError: (e: AxiosError) => {
      // 错误提示：使用message替代toast
      const errorMsg = e?.response?.data?.ErrorDescription || 'Delete failed';
      message.error(errorMsg);
    },
  });
};
