import {configureStore} from "@reduxjs/toolkit";
import userReducer from "@/store/modules/common/user_slice";
import versionReducer from "@/store/modules/common/version_slice";
import alarmReducer from "@/store/modules/common/alarm_slice";

import languageOTNSlice from "@/store/modules/otn/languageOTNSlice";
import notificationSlice from "@/store/modules/otn/notificationSlice";
import mapSlice from "@/store/modules/otn/mapSlice";
import neNameSlice from "@/store/modules/otn/neNameSlice";

const reducers = {
    user: userReducer,
    version: versionReducer,
    alarm: alarmReducer,
    languageOTN: languageOTNSlice,
    notification: notificationSlice,
    map: mapSlice,
    neName: neNameSlice
};

export default configureStore({
    reducer: reducers
});
