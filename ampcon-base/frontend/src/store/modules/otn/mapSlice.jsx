import {createSlice} from "@reduxjs/toolkit";

const mapSlice = createSlice({
    name: "map",
    initialState: {
        // 网元key和数据的map，为了绕过循环查找
        neInfoListMap: {},
        // 当前选中的元素，树选中或图上节点选中，其实就是那个网元的数据
        selectedItem: {},
        // Tree 选中时触发
        onSelectItem: {},
        // fiber 处理过的连接信息
        connections: [],
        // 表格过滤条件
        tableFilter: {},
        // 树节点变更标志,map要刷新节点渲染
        treeItemChangedTag: false,
        // 选中的连接信息
        selectedFiberConnectionInfo: {},
        // 选中的网元或者选中的组下所有网元
        selectedNes: [],
        updateServiceView: false,
        // 设置mock屏罩chassic6
        wrapMock: false,
        spinLoading: false,
        // 根节点类型： switchRoot | otnRoot
        rootKey: undefined
    },
    reducers: {
        setNeInfoListMap: (state, action) => {
            state.neInfoListMap = action.payload;
        },
        setSelectedItem: (state, action) => {
            state.selectedItem = action.payload;
        },
        setOnSelectItem: (state, action) => {
            state.onSelectItem = action.payload;
        },
        setConnections: (state, action) => {
            state.connections = action.payload;
        },
        setTableFilter: (state, action) => {
            state.tableFilter = action.payload;
        },
        setTreeItemChangedTag: (state, action) => {
            state.treeItemChangedTag = action.payload;
        },
        setSelectedFiberConnectionInfo: (state, action) => {
            state.selectedFiberConnectionInfo = action.payload;
        },
        setWrapMock: (state, action) => {
            state.wrapMock = action.payload;
        },
        setSpinLoading: (state, action) => {
            state.spinLoading = action.payload;
        },
        setUpdateServiceView: state => {
            state.updateServiceView = !state.updateServiceView;
        },
        setRootKey: (state, action) => {
            state.rootKey = action.payload;
        }
    }
});

export const {
    setNeInfoListMap,
    setSelectedItem,
    setOnSelectItem,
    setConnections,
    setTableFilter,
    setTreeItemChangedTag,
    setSelectedFiberConnectionInfo,
    setUpdateServiceView,
    setWrapMock,
    setSpinLoading,
    setRootKey
} = mapSlice.actions;
export default mapSlice.reducer;
