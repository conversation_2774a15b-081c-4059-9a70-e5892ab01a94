import {createSlice} from "@reduxjs/toolkit";

const textMapping = {
    copyRight: {en: "Copyright © 2009-", cn: "2009-"},
    FSCompany: {
        en: "FS.COM Inc. All Rights Reserved.",
        cn: "飞速"
    },
    // login.js
    fs_login: {en: "LOGIN", cn: "登录"},
    login: {en: "Login", cn: "登录"},
    cant_empty: {en: "User and password can't be empty!", cn: "用户名密码不能为空！"},
    login_success: {en: "Login succeeded", cn: "登录成功！"},
    login_fail: {en: "Login Failed!", cn: "登录失败！"},
    user: {en: "Username", cn: "用户名"},
    password: {en: "Password", cn: "密码"},
    welcome_to_AmpConT: {en: "Welcome to AmpCon-T", cn: "欢迎来到AmpCon-T"},
    login_dashboard: {en: "Please Login to your Dashboard", cn: "请登录"},
    front: {en: "Front", cn: "面板"},
    rear: {en: "Rear", cn: "背板"},

    // common
    assess_timeout: {en: "Request timeout.", cn: "请求连接超时."},
    table_empty_text: {en: "No Data", cn: "没有数据"},
    required: {en: "It is required", cn: "必填项"},
    please_select: {en: "Please select", cn: "请选择"},
    delete: {en: "Delete", cn: "删除"},
    pls_input_pwd: {en: "Please input password", cn: "请输入密码"},
    name_patter: {
        en: "Only contain letters, numbers, Chinese and -_:|.+, and must be between 1 and 20 characters long",
        cn: "只支持字母,数字,中文和-_:|.+等20个字符"
    },
    ip_patter: {
        en: "Please input IP address",
        cn: "请输入正确的IP地址"
    },
    ip_port_patter: {
        en: "Please input IP address and Port",
        cn: "请输入正确的IP地址和端口"
    },
    number_patter: {
        en: "Only support numbers",
        cn: "只支持数字"
    },
    delete_fail: {
        en: "Delete Failed",
        cn: "删除失败"
    },
    signed: {en: "Keep me signed in", cn: "记住登录信息"},
    remember: {en: "Remember Login Information", cn: "记住登录信息"},
    "reboot-type": {en: "Reboot Tpye", cn: "重启类型"},

    // main.js - sidebar
    name: {en: "Name", cn: "名称"},
    host: {en: "IP", cn: "IP"},
    port: {en: "Port", cn: "端口"},
    failed: {en: "Failed", cn: "失败"},
    property: {en: "Property", cn: "属性"},

    software: {en: "Software", cn: "软件版本"},
    state: {en: "State", cn: "状态"},
    error: {en: "Error", cn: "错误"},
    connected: {en: "Connected", cn: "连接正常"},
    lost: {en: "Lost", cn: "断开"},
    download_sw: {en: "Download Software", cn: "下载网元版本"},
    download_sw_ing: {en: "Downloading Software", cn: "下载网元版本中"},
    activate_sw: {en: "Activate Software", cn: "激活网元版本"},
    activate_sw_ing: {en: "Activating Software", cn: "激活网元版本中"},
    activate_suc: {en: "Activated, Wait reboot.", cn: "激活完成，等待重启"},
    activate_suc_ing: {en: "Activated, Waiting reboot.", cn: "激活完成，等待重启中"},
    reboot: {en: "Reboot", cn: "重启"},
    reboot_ing: {en: "Rebooting", cn: "重启中"},
    data_base: {en: "Database", cn: "数据文件"},
    ne_id: {en: "NE ID", cn: "网元名称"},
    ne_id_input_require_message: {en: "Please select NE", cn: "请选择网元"},
    ip: {en: "IP", cn: "IP地址"},

    download_data: {en: "Download File", cn: "下载数据文件"},
    download_data_ing: {en: "Downloading File", cn: "下载数据文件中"},
    activate_data: {en: "Activate File", cn: "激活数据文件"},
    activate_data_ing: {en: "Activating File", cn: "激活数据文件中"},

    sw_version: {en: "Software", cn: "网元版本"},

    "admin-state": {en: "Admin State", cn: "管理状态"},
    "oper-status": {en: "Oper Status", cn: "运行状态"},
    upgrade: {en: "Upgrade", cn: "升级"},
    sure_upgrade: {en: "Are you sure to upgrade?", cn: "你确定要升级？"},
    upload_success: {en: "Upload succeeded", cn: "上传成功"},
    upload_failed: {en: "Upload failed", cn: "上传失败"},
    upload: {en: "Upload", cn: "上传"},

    device: {en: "Device", cn: "设备"},
    device_management: {en: "Device Management", cn: "设备管理"},
    machine_block_diagram: {en: "Control Panel", cn: "设备控制面板"},
    ne_upGrade: {en: "NE Upgrade", cn: "网元升级"},
    dataBase: {en: "Database Management", cn: "数据库管理"},
    not_allowed_pop: {en: "Websites are not allowed to display pop-ups", cn: "不允许网站显示弹出式窗口"},

    alarm: {en: "Alarm & Event", cn: "告警"},
    ne_alarm: {en: "NE Alarm", cn: "网元告警"},
    tnms_alarm: {en: "AmpCon-T Alarm", cn: "AmpCon-T告警"},
    alarm_mask: {en: "Alarm Mask", cn: "告警屏蔽"},
    event: {en: "Event", cn: "事件查看"},
    event_title: {en: "Event", cn: "事件"},
    alarm_title: {en: "Current NE Alarm", cn: "网元当前告警"},
    service_view: {en: "Service View", cn: "业务视图"},
    index: {en: "Index", cn: "序号"},
    "object-range": {en: "Object Range", cn: "对象类别"},
    "object-name": {en: "Object Name", cn: "对象名称"},
    "object-type": {en: "Object Type", cn: "对象类型"},
    "mask-state": {en: "Mask State", cn: "状态"},
    "alarm-code": {en: "Alarm Code", cn: "告警码"},
    create_alarm_mask: {en: "Create Alarm Mask", cn: "创建告警屏蔽"},
    alarm_mask_exist: {en: "This Alarm Mask already exists", cn: "存在同样的告警屏蔽"},
    alarm_mask_index_pattern: {en: "Only supports 1~15 characters, numbers or _", cn: "只支持1~15位字母数字或_"},

    inventory: {en: "Inventory", cn: "设备资源"},
    location: {en: "Location", cn: "位置"},
    parent: {en: "parent", cn: "父对象"},
    "serial-no": {en: "Serial No", cn: "序列号"},
    "part-no": {en: "Part No", cn: "部件号"},
    description: {en: "Description", cn: "备注"},

    NE_A: {en: "NE A", cn: "A端网元"},
    Port_A: {en: "Port A", cn: "A端端口"},
    NE_B: {en: "NE B", cn: "B端网元"},
    Port_B: {en: "Port B", cn: "B端端口"},

    performance: {en: "Performance", cn: "性能"},
    pmp_mgmt: {en: "PMP", cn: "监控点管理"},
    tca_alert: {en: "TCA", cn: "阈值门限管理"},
    pm: {en: "PM", cn: "性能数据"},
    pmp: {en: "PMP", cn: "监控点管理"},
    "pm-point": {en: "PM Point", cn: "性能监控点"},
    "pm-point-enable": {en: "PM Point Enable", cn: "性能点使能"},
    "tca-enable": {en: "TCA Enable", cn: "TCA使能"},
    "pm-parameter": {en: "PM Parameter", cn: "性能参数"},
    "pm-point-type": {en: "PM Point Type", cn: "性能监控点类型"},
    pm_point_type_input_require_message: {en: "Please select PM Point Type", cn: "请选择性能监控点类型"},
    "pm-granularity": {en: "PM Granularity", cn: "性能监控点粒度"},
    "threshold-value-high": {en: "Threshold Value High", cn: "门限最高阈值"},
    "threshold-value-low": {en: "Threshold Value Low", cn: "门限最低阈值"},
    "monitoring-date-time": {en: "Monitoring Date Time", cn: "监控时间"},
    "max-value": {en: "Max Value", cn: "最大值"},
    "min-value": {en: "Min Value", cn: "最小值"},
    "average-value": {en: "Average Value", cn: "平均值"},
    "current-value": {en: "Current Value", cn: "当前值"},

    service: {en: "Service", cn: "业务"},
    service_mgmt: {en: "Service Management", cn: "业务管理"},

    maintenance: {en: "Maintenance", cn: "维护"},

    system: {en: "System", cn: "系统"},
    user_management: {en: "User Management", cn: "用户管理"},
    authorization: {en: "Authorization", cn: "权限管理"},
    log_management: {en: "Log Management", cn: "日志管理"},
    aboutUs: {en: "About", cn: "关于"},

    username: {en: "User", cn: "用户名"},
    changePassword: {en: "Change Password", cn: "修改密码"},
    history_information: {en: "Historical information about the message channel", cn: "消息通道历史信息"},
    message_channel_disconnected: {en: "Message channel disconnected", cn: "消息通道断开连接"},
    message_channel_reconnection: {en: "Message channel connected", cn: "消息通道连接成功"},
    oldPassword: {en: "Old Password", cn: "旧密码"},
    newPassword: {en: "New Password", cn: "新密码"},
    confirmPassword: {en: "Confirm Password", cn: "确认密码"},
    oldPasswordText: {en: "Please enter the old password", cn: "请输入旧密码"},
    newPasswordText: {en: "Please enter the new password", cn: "请输入新密码"},
    confirmPasswordText: {en: "Please enter again", cn: "请再次输入密码"},
    successMessage: {en: "Updated succeeded", cn: "修改成功"},
    emptyMessage: {en: "The password cannot be empty!", cn: "密码不可为空!"},
    confirmMessage: {en: "The confirm password cannot be empty", cn: "确认密码不可为空!"},
    differMessage: {en: "Entered passwords differ", cn: "密码不一致"},
    typeEmptyMessage: {en: "The type cannot be empty!", cn: "类型不可为空!"},
    passwordFormatError: {
        en: "Password must be 8-32 characters long and include at least one uppercase letter, one lowercase letter, one digit, and one special character",
        cn: "密码要求8-32个字符，包含至少一个大写字母、一个小写字母、一个数字和一个特殊字符"
    },

    exit: {en: "Exit", cn: "退出"},
    exitMessage: {en: "Confirm to exit?", cn: "是否退出?"},

    connection_lost: {
        en: "Disconnected from the server, will return to the login page",
        cn: "和服务器已断连，将返回登录界面"
    },
    // main.js - newMessage
    alarm_new: {en: "alarm_new", cn: "告警产生"},
    alarm_delete: {en: "alarm_delete", cn: "告警删除"},
    event_msg: {en: "Event", cn: "事件"},
    message: {en: "Message", cn: "消息"},
    message_alarm: {en: "Alarm", cn: "告警"},
    message_event: {en: "Event", cn: "事件"},
    allRead: {en: "Set Read All", cn: "全部已读"},

    ok: {en: "OK", cn: "确定"},
    cancel: {en: "Cancel", cn: "取消"},
    overwrite: {en: "Overwrite", cn: "覆盖"},
    bin_files: {en: "Only bin files can be uploaded", cn: "只能上传bin文件"},
    tar_gz: {en: "Only tar.gz files can be uploaded", cn: "只能上传tar.gz文件"},
    // edit-rpc.js
    operation: {en: "Operation", cn: "操作"},
    operation_success: {en: "Operation Succeeded", cn: "操作成功."},
    operation_fail: {en: "Operation Failed.", cn: "操作失败."},
    ring: {en: "Ring", cn: "环"},

    // dataBase.js
    active: {en: "Activating...", cn: "激活中..."},
    activating: {en: "Activating...", cn: "激活中..."},
    delete_success: {en: "Successfully Deleted", cn: "删除成功"},
    delete_failed: {en: "Delete Failed", cn: "删除失败"},
    backup: {en: "Backup", cn: "备份"},
    backup_desc: {en: "Backup the data file to SFTP server", cn: "备份数据文件到STFP服务器."},
    backup_confirm: {en: "Are you sure to backup the data file?", cn: "你确定要备份这个文件吗?"},
    backup_success: {en: "Successfully Backuped", cn: "备份成功."},
    backup_failed: {en: "Backup Failed.", cn: "备份失败."},
    activate: {en: "Activate", cn: "激活"},
    activate_confirm: {en: "Are you sure to activate the data file?", cn: "你确定要激活这个数据文件吗?"},
    history: {en: "History", cn: "历史"},

    // upgrate.js
    update: {en: "Upgrading...", cn: "升级中"},
    upgrading: {en: "Upgrading...", cn: "升级中"},

    // user.js
    id: {en: "ID", cn: "序号"},
    type: {en: "Type", cn: "类型"},
    add: {en: "Add", cn: "添加"},
    edit: {en: "Edit", cn: "编辑"},
    del: {en: "Delete", cn: "删除"},
    addUser: {en: "Add User", cn: "添加用户"},
    userEmptyMessage: {en: "The username cannot be empty!", cn: "用户名不可为空!"},
    placeholder_user: {en: "Please enter the username", cn: "请输入用户名"},
    user_add_success: {en: "User added successfully", cn: "用户添加成功!"},
    delete_confirm: {en: "Are you sure you want to delete it?", cn: "你确定要删除?"},
    delete_confirm2: {en: "Are you sure you want to delete {0}?", cn: "你确定要删除{0}?"},
    delete_confirm_message: {en: "Confirm to delete the user?", cn: "确认要删除该用户吗?"},
    user_delete_success: {en: "User deleted successfully", cn: "用户删除成功!"},
    placeholder_pwd: {en: "Please enter the password", cn: "请输入密码"},
    placeholder_search: {en: "Please enter the username you want to search", cn: "请输入要查询的用户名"},
    edit_password: {en: "Edit Password", cn: "修改密码"},
    edit_right: {en: "Edit Right", cn: "编辑权限"},

    // logs.js
    download: {en: "Download", cn: "下载"},
    download_logfile: {en: "Log file download completed!", cn: "下载日志文件完成!"},
    download_logfile_failed: {en: "Download log file failed", cn: "下载日志文件失败."},

    // device.js
    critical_ne: {en: "Critical NE", cn: "严重告警网元"},
    normal_ne: {en: "Normal NE", cn: "正常网元"},
    offline_ne: {en: "Offline NE", cn: "离线网元"},
    test_result: {en: "Test Result", cn: "测试结果"},
    create_success: {en: "Successfully Saved", cn: "创建成功"},
    create_failed: {en: "Save Failed", cn: "创建失败"},
    save_success: {en: "Successfully Saved", cn: "保存成功"},
    save_failed: {en: "Save Failed", cn: "保存失败"},
    timeslot_pass_fail: {en: "The selected time slot verification failed.", cn: "所选时隙验证失败!"},
    complete: {en: "Complete", cn: "完整"},
    partial: {en: "Partial", cn: "不完整"},
    ne_list: {en: "NE List", cn: "网元列表"},
    map: {en: "Map", cn: "地图"},
    device_view: {en: "Device Display", cn: "设备显示"},
    common_view: {en: "Common View", cn: "Common View"},
    fiber_connection: {en: "Fiber Connection", cn: "光纤连接"},
    same_group: {
        en: "The same group already exists!",
        cn: "已存在相同名的组."
    },
    delete_ne_confirm: {en: "This NE has services, cannot be delete", cn: "这网元存在业务，不能删除!"},
    delete_node_confirm: {
        en: "This Node has children nodes, cannot be delete.",
        cn: "有子节点不能删除，请先删除或者移出子节点!"
    },
    move_group_to_ne: {en: "Cannot move the group node to NE node.", cn: "组不能移动到网元节点下."},
    warning: {
        en: "Warning",
        cn: "警告"
    },
    sync_confirm: {
        en: "This NE is in {0} state. If continue to synchronize, the state will be reset. Are you sure to continue?",
        cn: "此网元在{0}状态，如果同步将会重置网元状态，你确定要同步吗?"
    },
    activate_data_state: {
        en: "active data",
        cn: "数据激活"
    },
    // device_map.js
    up_one_level: {en: "Up One Level", cn: "上一级"},
    up_to_top_level: {en: "Up to Top Level", cn: "回到顶层"},
    save_view: {en: "Save View", cn: "保存视图"},
    edit_location: {en: "Edit Location", cn: "编辑位置"},

    resource: {en: "Resource", cn: "资源"},
    severity: {en: "Severity", cn: "级别"},
    text: {en: "Text", cn: "内容"},
    "time-created": {en: "Created Time", cn: "产生时间"},
    "type-id": {en: "Type", cn: "类型"},
    "alarm-abbreviate": {en: "Abbreviate", cn: "缩写"},
    hostname: {en: "Host Name", cn: "主机名"},
    "vendor-type": {en: "Vendor", cn: "厂商"},
    vendor_info: {en: "Vendor Type", cn: "型号"},
    "event-abbreviate": {en: "Abbreviate", cn: "缩写"},
    xpath: {en: "Path", cn: "路径"},
    ne_name: {en: "NE Name", cn: "网元名称"},
    current_event: {en: "Current Event", cn: "当前事件"},
    history_event: {en: "History Event", cn: "历史事件"},
    critical: {en: "Critical", cn: "严重"},
    major: {en: "Major", cn: "重要"},
    minor: {en: "Minor", cn: "轻微"},

    ungrouped_ne: {en: "Ungrouped NE", cn: "未分组网元"},
    ne_removed_confirm: {
        en: "Are you sure to remove this NE from this group?",
        cn: "你确定要从该组移除该网元?"
    },
    ne_removed_msg: {en: "The NE is successfully removed from this group!", cn: "网元从该组移除成功!"},
    ne_notfound: {en: "No NE found", cn: "没有找到网元"},
    cannot_move: {en: "Default group cannot be moved", cn: "不能移动默认组"},
    no_permission: {en: "No Permission", cn: "没有权限!"},
    ne_notadd: {en: "The NE cannot add child nodes", cn: "网元不能添加子节点"},
    actionMap_1: {en: "Please enter to search.", cn: "请输入查找的字符"},
    actionMap_2: {en: "Please enter the group name.", cn: "请输入组名"},
    actionMap_3: {en: "Please enter the group name.", cn: "请输入组名"},
    actionMap_4: {en: "Please enter the IP address.", cn: "请输入IP"},
    toggle_btn: {en: "Toggle Display IP/Name", cn: "切换显示IP/名称"},
    sync_ne: {en: "Sync NE", cn: "同步网元数据"},
    sync: {en: "Synchronize", cn: "同步"},
    expand_btn: {en: "Expand/shrink", cn: "展开/收缩"},
    search_title: {en: "Search", cn: "查找"},
    addGroup_btn: {en: "Add Group", cn: "添加组"},
    delete_confirm_msg: {en: "Are you sure to delete?", cn: "你确定要删除?"},
    modify_group: {en: "Modify Group/NE", cn: "修改组/网元"},
    add_ne: {en: "Add NE", cn: "添加网元"},

    // device.js
    group: {en: "Group", cn: "组"},
    hide: {en: "Hide", cn: "隐藏"},
    expand: {en: "Expand", cn: "展开"},
    floatBtn_title: {en: "List of NE examples/chart", cn: "网元例表/图表"},
    floatBtn_title_return: {en: "Back to List of NE examples/chart", cn: "返回网元例表/图表"},
    oduk_avg: {en: "AVG", cn: "平均值"},
    oduk_max: {en: "MAX", cn: "最大值"},
    oduk_min: {en: "MIN", cn: "最小值"},

    // connect_map
    save: {en: "Save", cn: "保存"},
    un_located: {en: "Unlocated", cn: "未定位"},
    top_topologies: {en: "Top Level Topologies", cn: "顶层拓扑图"},
    // connection
    create: {en: "Create", cn: "创建"},
    refresh: {en: "Refresh", cn: "刷新"},
    operate: {en: "Operate", cn: "操作"},
    Operation_success: {en: "Operation succeeded", cn: "操作成功"},

    // pmp.js,tca.js
    disabled: {en: "Disabled", cn: "未启用"},
    enabled: {en: "Enabled", cn: "已启用"},
    ne: {en: "NE", cn: "网元"},
    message_empty: {en: "The modified value cannot be empty", cn: "修改值不能为空"},

    // pm_pm
    trend_chart: {en: "Trend Chart", cn: "趋势图"},
    pm_type: {en: "PM Type", cn: "性能类型"},
    pm_point_type: {en: "PM Point Type", cn: "性能点类型"},
    pm_point: {en: "PM Point", cn: "性能点"},
    pm_point_input_require_message: {en: "Please select PM Point", cn: "请选择性能点"},
    pm_granularity: {en: "PM Granularity", cn: "性能区间"},
    pm_granularity_input_require_message: {en: "Please select PM Granularity", cn: "请选择性能区间"},
    pm_parameter: {en: "PM Parameter", cn: "性能参数"},
    pm_parameter_input_require_message: {en: "Please select PM Parameter", cn: "请选择性能参数"},
    value_scope: {en: "Value Scope", cn: "值域"},
    value_scope_input_require_message: {en: "Please select Value Scope", cn: "请选择值域"},
    history_data_type: {en: "History Data Type", cn: "历史数据类型"},
    history_data_type_input_require_message: {en: "Please select History Data Type", cn: "请选择历史数据类型"},
    number_of_records: {en: "Number Of Records", cn: "记录数"},
    number_of_records_input_require_message: {en: "Please select Number Of Records", cn: "请选择记录数"},
    start_time: {en: "Start Time", cn: "开始时间"},
    start_time_input_require_message: {en: "Please select Start Time", cn: "请选择开始时间"},
    end_time: {en: "End Time", cn: "结束时间"},
    end_time_input_require_message: {en: "Please select End Time", cn: "请选择结束时间"},
    reset: {en: "Reset", cn: "重置"},
    search: {en: "Search", cn: "查询"},
    error_records_empty: {en: "Please input number(>1)!", cn: "请输入数字(>1)!"},
    warn_reload: {
        en: "AmpCon-T has been upgraded, click here to reload!",
        cn: "AmpCon-T系统已升级，点击此处重新加载！"
    },

    // provision
    create_service: {en: "Create Service", cn: "创建业务"},
    mux_not_fit_tff: {
        en: "The selected MUX card cannot match in the OMS. Please select again.",
        cn: "所选MUX的PORT类型在OMS里没有找到相匹配的TFF卡，请重新选择."
    },
    tff_not_fit_tff: {
        en: "The selected TFF card cannot match in the OMS. Please select again.",
        cn: "所选TFF卡类型在OMS里没有找到相匹配的TFF卡，请重新选择！"
    },
    mult_ttf: {
        en: "The TTF card type of '{0}' has duplicates",
        cn: "TTF卡类型'{0}'有重复！"
    },
    signal_type_diff: {
        en: "All Signal Type must be the same!",
        cn: "所有的信号类型必须一致!"
    },
    data_has_change: {
        en: " has been changed, please reconfigure it.",
        cn: "数据已发生变化, 请重新配置!"
    },
    tff_not_fit: {
        en: "TFF card types do not match!",
        cn: "两端TFF类型不匹配!"
    },
    max_not_fit_max: {
        en: "MUX card types do not match!",
        cn: "两端MUX类型不匹配!"
    },
    max96_not_fit_ttf: {
        en: "MUX96 can not match TTF card",
        cn: "MUX96不能与TFF匹配"
    },
    service_title: {
        en: " Service",
        cn: "业务"
    },
    same_service: {
        en: "The same service already exists!",
        cn: "已存在相同业务!"
    },
    create_channel_fail: {
        en: "Create the frequency channel of NE {0}-{1} failed, reason:{2}",
        cn: "创建网元{0}-{1}频率通道失败, 原因:{2}"
    },
    add_fail: {
        en: "Create failed",
        cn: "添加失败"
    },
    add_suc: {
        en: "Create succeeded",
        cn: "创建成功"
    },
    up_route: {
        en: "Up Route",
        cn: "上路由"
    },
    down_route: {
        en: "Down Route",
        cn: "下路由"
    },
    switch_a_z: {
        en: "Exchange the location of A and Z.",
        cn: "交换AZ位置"
    },
    no_protection: {
        en: "No Protection",
        cn: "不带保护"
    },
    protection: {
        en: "Protection",
        cn: "带保护"
    },
    exists: {
        en: " already exists!",
        cn: "已存在!"
    },
    delete_ots_confirm: {
        en: "This OTS has been used by OMS, if you want to delete it, please delete the OMS at first.",
        cn: "此OTS已被OMS使用，请先删OMS!"
    },
    delete_oms_confirm: {
        en: "This OMS has been used by OCH, if you want to delete it, please delete the OCH at first.",
        cn: "此OMS已被OCH使用，请先删OCH!"
    },
    delete_och_confirm: {
        en: "This OC has been used by Client Service, if you want to delete it, please delete the Client Service at first.",
        cn: "此OCH已被Client业务使用，请先删Client业务!"
    },

    // otdr
    test_config: {en: "Test Config", cn: "测试设置"},
    start_test: {en: "Start Test", cn: "开始测试"},
    otdr_start: {en: "Testing, please wait.", cn: "测试开始,请等待."},
    otdr_fail: {en: "Test Failed.", cn: "OTDR测试失败"},
    otdr_stop_suc: {en: "Stop OTDR succeeded.", cn: "停止OTDR成功."},
    otdr_stop_fail: {en: "Stop OTDR failed.", cn: "停止OTDR失败."},
    stop: {en: "Stop", cn: "停止"},
    card: {en: "Card", cn: "卡"},
    "active-local-port": {en: "Active Local Port", cn: "端口"},
    "fiber-refractive-index": {en: "Fiber Refractive Index", cn: "光纤折射的序号"},
    "measuring-state": {en: "Measuring State", cn: "测量状态"},
    "otdr-wavelength": {en: "OTDR Wavelength", cn: "OTDR波长"},
    "measure-mode": {en: "Measure Mode", cn: "测量模式"},
    "pulse-width": {en: "Pulse Width", cn: "脉冲宽度"},
    "measuring-range": {en: "Measuring Range", cn: "测量范围"},
    "measuring-time": {en: "Measuring Time", cn: "测量时间"},
    result: {en: "Result", cn: "结果"},
    "detect-time": {en: "Detect Time", cn: "测试时间"},
    otdr_result: {en: "OTDR Result", cn: "OTDR结果"},
    otdr_cfg_desc: {en: "OTDR Config Description", cn: "OTDR配置明细"},
    otdr_data_ing: {en: " Generating data.", cn: "测试完成,生成数据中."},
    otdr_done: {en: "Test completed", cn: "OTDR测试结束."},
    test_done: {en: "Test completed", cn: "测试结束."},
    test_failed: {en: "Test Failed, {0}", cn: "测试失败, {0}"},
    nanoseconds: {en: "nanoseconds", cn: "纳秒"},
    kilometers: {en: "kilometers", cn: "千米"},
    seconds: {en: "seconds", cn: "秒"},
    distance: {en: "Distance", cn: "距离"},
    "splice-loss": {en: "Splice Loss", cn: "接头损耗"},
    "return-loss": {en: "Return Loss", cn: "回波损耗"},
    "cumulate-loss": {en: "Cumulate Loss", cn: "累积损耗"},
    "event-type": {en: "Event Type", cn: "事件类型"},
    attenuation: {en: "Attenuation", cn: "衰减"},
    loss: {en: "Loss", cn: "衰减"},
    parameter: {en: "Parameter", cn: "参数"},
    value: {en: "Value", cn: "值"},
    mark: {en: "Mark", cn: "标记位"},
    position: {en: "Position", cn: "位置"},
    // ocm
    select_card_before: {en: "Please select the Test Card at first!", cn: "请先选择要测试的OCM卡！"},
    select_group: {en: "Please select group", cn: "请选择组"},
    test_ocm: {en: "Config OCM", cn: "OCM配置"},
    ocm_testing: {en: "Configuration completed, scanning", cn: "配置完成,扫描中."},
    auto_refresh: {en: "Auto Refresh", cn: "自动刷新"},
    spectrogram: {en: "Spectrogram ", cn: "光谱图"},
    data_table: {en: "Data Table", cn: "数据表格"},
    "lower-frequency": {en: "Lower Frequency", cn: "低频"},
    "upper-frequency": {en: "Upper Frequency", cn: "高频"},
    "channel-interval": {en: "Channel Interval", cn: "信道间隔"},
    power: {en: "Power", cn: "功率"},
    osnr: {en: "OSNR", cn: "OSNR"},

    // logs
    log: {en: "Log", cn: "日志"},
    upload_start: {en: "Start Upload", cn: "开始上传"},
    upload_done: {en: "Upload Completed", cn: "上传完成"},
    upload_logs: {en: "Backup", cn: "备份"},
    upload_logs_desc: {en: "Backup the log files to SFTP server.", cn: "备份日志文件到SFTP服务器."},
    upload_logs_confirm: {
        en: "Are you sure to back it up?",
        cn: "你确定要备份？"
    },
    tnms_log: {en: "AmpCon-T Log", cn: "AmpCon-T日志"},

    // api
    sync_fail: {en: "{0} synchronize failed", cn: "{0}同步失败"},
    sync_suc: {en: "{0} synchronize succeeded", cn: "{0}同步成功"},
    get_data_fail: {en: "Request data failed", cn: "数据请求失败"},
    optics_service_list: {en: "OTN Service", cn: "OTN业务"},
    service_topo: {en: "Service Topo", cn: "业务拓扑"},

    // telemetry
    subscribe_exist: {
        en: "Sensor Group is {0} and Destination Group is {0} has been subscribed, please don't repeat subscribe.",
        cn: "监测组为{0}和接收组为{1}已被订阅, 请务重复订阅."
    },
    telemetry: {en: "Telemetry", cn: "性能订阅"},
    sensor_group: {en: "Sensor Group", cn: "监测组"},
    destination_group: {en: "Destination Group", cn: "接收组"},
    subscription: {en: "Subscription", cn: "订阅"},
    reset_counter: {en: "Reset Counter", cn: "重置计数器"},
    "sensor-group-id": {en: "Sensor Group ID", cn: "监测组ID"},
    "sensor-paths": {en: "Sensor Paths", cn: "监测路径"},
    includes: {en: "Includes", cn: "包括"},
    "exclude-filter": {en: "Exclude Filter", cn: "排除过滤"},
    create_sensor_group: {en: "Create Sensor Group", cn: "创建监测组"},
    edit_sensor_group: {en: "Edit Sensor Group", cn: "编辑监测组"},
    "group-id": {en: "Group ID", cn: "组ID"},
    destinations: {en: "Destination", cn: "地址"},
    "destination-address": {en: "Destination Address", cn: "IP地址"},
    "destination-port": {en: "Destination Port", cn: "端口"},
    create_destination_group: {en: "Create Destination Group", cn: "创建接收组"},
    entry_ipaddress: {en: "IP Address, i.g. ***********", cn: "请输入IP地址,如***********"},
    entry_ip_port: {en: "IP Address and Port, i.g. ***********:1000", cn: "请输入IP和端口,如***********:1000"},
    entry_port: {en: "Port, i.g. 1000", cn: "请输入端口,如1000"},
    "local-source-address": {en: "Local Source Address", cn: "本地源地址"},
    "local-source-port": {en: "Local Source Port", cn: "本地源端口"},
    "originated-qos-marking": {en: "Originated QOS Marking", cn: "起源QOS标记"},
    protocol: {en: "Protocol", cn: "协议"},
    encoding: {en: "Encoding", cn: "编码"},
    "sensor-profiles": {en: "Sensor Profiles", cn: "监测组"},
    "destination-groups": {en: "Destination Groups", cn: "接收组"},
    "create-persistent-subscription": {en: "Create Subscription", cn: "创建订阅"},
    "sample-interval": {en: "Sample Interval", cn: "采样间隔"},
    "heartbeat-interval": {en: "Heartbeat Interval", cn: "心跳间隔"},
    "suppress-redundant": {en: "Suppress Redundant", cn: "抑制冗余"},
    "reset-counter": {en: "Reset Counter", cn: "重置计数器"},
    reset_success: {en: "Reset succeeded", cn: "重置成功"},
    reset_fail: {en: "Reset failed", cn: "重置失败，原因: {0}"},
    reset_fail_warning: {en: "Reset failed", cn: "重置失败"},
    sensor_del_warning: {en: "This Sensor Group has been used", cn: "这个监测组已被使用"},
    destination_del_warning: {en: "This Destination Group has been used", cn: "这个接收组已被使用"},
    default_tnms_server: {en: "Default AmpCon-T Server", cn: "默认AmpCon-T服务器"},
    same_group_id: {
        en: "The same Group ID already exists!",
        cn: "已存在相同名的组ID."
    },
    same_name: {
        en: "The same Name already exists!",
        cn: "已存在相同名的名."
    },
    single_same_file: {
        en: "There is 1 file with same name",
        cn: "有 1 个同名文件"
    },
    multiple_same_file: {
        en: "There are files with same name",
        cn: "有同名的文件"
    },
    will_be_overwritten: {en: "Will be overwritten?", cn: "是否覆盖?"},
    right_destinations: {
        en: "Please input a correct Destinations, e.g. ***********:1000",
        cn: "请输入正确的目地地址, 如：***********:1000"
    },
    data_view: {
        en: "Data View",
        cn: "数据视图"
    },
    no_data: {
        en: "No Data",
        cn: "没有数据"
    },

    remain_day: {en: "Remaining days: {0}", cn: "剩余天数: {0}"},
    licence: {en: "Licence", cn: "许可"},
    register: {en: "Register", cn: "注册"},
    secret_key: {en: "Secret Key", cn: "密钥"},
    register_code: {en: "Code", cn: "注册码"},
    register_suc: {en: "Register succeeded", cn: "注册成功"},
    register_fail: {en: "Register failed, reason: {0}", cn: "注册失败, 原因: {0}"},
    register_expired: {en: "Licence has expired", cn: "许可证已失效"},
    pls_register: {en: "Please register", cn: "请先注册"},
    registered_by: {en: "Registered by {0}", cn: "注册用户: {0}"},
    update_license: {en: "Update License", cn: "更新许可证书"},

    submit: {en: "Submit", cn: "提交"},
    switch_confirm: {
        en: "This operation will interrupt the connection to the NE. Do you want to continue?",
        cn: "此操作会造成与网元连接的中断，是否要继续?"
    },
    switch_success: {en: "Switch MCU successfully!", cn: "成功激活MCU！"},
    switch_fail: {en: "Switch MCU failed!", cn: "激活MCU失败！"},
    switch_lost: {
        en: "The connection to the NE is about to be disconnected, please login again later!",
        cn: "与网元连接即将断开连接，请稍后重新登录！"
    },
    preconf: {en: "Preconf", cn: "预配"},
    actual: {en: "Actual", cn: "实际"},
    layer: {en: "Layer rate", cn: "层速率"},
    switchover: {en: "Active/standby switchover", cn: "主备切换"},
    rebootConfirm: {
        en: "Reboot will make traffic break, are you sure to continue?",
        cn: "重启将中断业务，确定要继续吗？"
    },
    config_service: {en: "Config Service", cn: "配置业务"},
    config_electrical_service: {en: "Config Electrical Service", cn: "配置电卡业务"},
    config_amplifier: {en: "Config Amplifier", cn: "配置放大器"},
    config_frequency_channel: {en: "Batch Frequency Channel", cn: "批量频率通道"},
    frequency_channel: {en: "Frequency Channel", cn: "频率通道"},

    system_information: {en: "System Information", cn: "系统信息"},
    device_resource: {en: "Device Resource", cn: "设备资源"},
    upload_add: {en: "Add", cn: "添加"},
    download_local: {en: "Download", cn: "保存到本地"},

    ne_is_lost: {en: "NE {0} is disconnected, unable to configurate fiber.", cn: "网元{0}断开状态，不能配置业务."},
    ne_is_upgrade: {en: "NE {0} is upgrading, unable to configurate fiber.", cn: "网元{0}升级状态，不能配置业务."},
    och_tff_link_card: {en: "{0} and {1} must be link by TFF Card.", cn: "{0}和{1}的TFF卡级联必须都是TFF卡。"},
    och_tff_link_type: {en: "The TFF card type of {0} and {1} must be same.", cn: "{0}和{1}的TFF类型必须一样。"},
    och_mux_link_type: {en: "The MUX card type of {0} and {1} must be same.", cn: "{0}和{1}的MUX类型必须一样。"},
    "time-cleared": {en: "Time Cleared", cn: "清除时间"},
    current: {en: "Current", cn: "当前"},
    current_tnms_alarm: {en: "Current AmpCon-T Alarm", cn: "当前AmpCon-T告警"},
    history_tnms_alarm: {en: "History AmpCon-T Alarm", cn: "历史AmpCon-T告警"},
    del_device_with_service: {
        en: "This NE has services, it will affect the service feature, Are you sure to delete it?",
        cn: "这网元存在业务,它将会影响业务功能,你确定要删除它?"
    },
    no_same_ch_port: {
        en: "Can ont find port of the same frequency channel in {0} {1}",
        cn: "网元{0}的{1}找不到合适的频率的端口"
    },
    edit_card: {en: "Edit Card", cn: "修改卡"},
    create_card: {en: "Create Card", cn: "创建卡"},
    protection_group: {en: "Protection", cn: "保护组"},
    left_port: {en: "Left Port", cn: "左端口"},
    right_port: {en: "Right Port", cn: "右端口"},
    a_port: {en: "Primary", cn: "主"},
    b_port: {en: "Secondary", cn: "备"},
    reboot_warning: {en: "Are you sure to reset it?", cn: "你确定要重启?"},
    // telemetry view
    select_ne: {en: "Please select ne", cn: "请选择网元"},
    select_type: {en: "Please select type", cn: "请选择类型"},
    select_value: {en: "Please select value", cn: "请选择值"},
    select_start_time: {en: "Please select start time", cn: "请选择开始时间"},
    select_end_time: {en: "Please select end time", cn: "请选择结束时间"},
    // connections
    "layer-protocol-name": {en: "Layer Protocol Name", cn: "层协议"},
    "service-type": {en: "Service Type", cn: "业务类型"},
    information: {en: "Information", cn: "详细信息"},
    label: {en: "Label", cn: "标签"},
    same_label_in_connection: {
        en: "The same name already exists.",
        cn: "已存在相同名称的业务"
    },
    connection_label_patter: {
        en: "Only support letters, Chinese, numbers and . : _ ~, and must be between 1 and 128 characters long",
        cn: "只支持字母,中文,数字,.:_等128个字符"
    },
    connection: {en: "Connection", cn: "连接"},
    otn_connection: {en: "Electric Connection", cn: "电层连接"},
    switch_connection: {en: "Switch", cn: "业务倒换"},
    switch_device: {en: "Switch", cn: "设备倒换"},
    port_manager: {en: "Port Management", cn: "端口管理"},
    service_resource_manager: {en: "Resource Management", cn: "资源管理"},
    resource_detail: {en: "Resource Details", cn: "资源详情"},
    "form-check": {en: "Please check if there are any incorrect values.", cn: "请检查是否有值不正确?"},
    optics_service: {en: "OTN Service", cn: "OTN业务"},
    client_service: {en: "Client Service", cn: "客户侧业务"},
    tca: {en: "TCA", cn: "TCA"},
    free: {en: "Free", cn: "空闲"},
    request: {en: "Request", cn: "请求"},
    occupied: {en: "Occupied", cn: "已占用"},
    selected: {en: "Selected", cn: "已选择"},
    timeslot: {en: "Time Slot", cn: "时隙"},
    eh: {en: "EH", cn: "EH"},
    eq: {en: "EQ", cn: "EQ"},
    delete_card: {en: "Delete Card", cn: "删除卡"},
    delete_ptp: {en: "Delete PTP", cn: "删除PTP"},
    device_protection: {en: "Device Protection", cn: "设备保护组"},
    update_success: {en: "Successfully Saved", cn: "保存成功"},
    update_fail: {en: "Save Failed", cn: "保存失败"},
    fiber: {en: "Fiber", cn: "光纤连接"},
    "loop-back": {en: "Loopback", cn: "Loopback"},
    value_between: {en: "The value must be between {0} and {1}"},
    "client-side-card": {en: "Client Side Card", cn: "客户侧板卡"},
    "line-side-ptp": {en: "Line Side PTP", cn: "线路侧PTP"},
    "sm-tim-mode": {en: "SM Tim Mode", cn: "SM TIM检测模式"},
    "sm-tim-action": {en: "SM Tim Action", cn: "SM TIM后续使能"},
    "smtrail-trace-actual-tx-sapi": {en: "Actual TX SAPI SM Trail Trace", cn: "SM SAPI发送"},
    "smtrail-trace-actual-tx-dapi": {en: "Actual TX DAPI SM Trail Trace", cn: "SM DAPI发送"},
    "smtrail-trace-expected-rx-sapi": {en: "Expected TX SAPI SM Trail Trace", cn: "SM SAPI期望接收"},
    "smtrail-trace-expected-rx-dapi": {en: "Expected TX DAPI SM Trail Trace", cn: "SM DAPI期望接收"},
    "smtrail-trace-actual-rx-sapi": {en: "Actual RX SAPI SM Trail Trace", cn: "SM SAPI接收"},
    "smtrail-trace-actual-rx-dapi": {en: "Actual RX DAPI SM Trail Trace", cn: "SM DAPI接收"},
    "pmtrail-trace-actual-rx-sapi": {en: "Actual RX SAPI PM Trail Trace", cn: "PM SAPI接收"},
    "pmtrail-trace-actual-rx-dapi": {en: "Actual RX DAPI PM Trail Trace", cn: "PM DAPI接收"},
    collect_data: {en: "Collecting data.", cn: "收集数据中."},
    create_uni_port: {en: "Creating UNI port.", cn: "创建UNI端口中."},
    config_uni_port: {en: "Configuring UNI port.", cn: "配置UNI端口中."},
    create_connection: {en: "Creating connection.", cn: "创建业务中."},
    services: {en: "Services", cn: "业务"},
    "available-time-slot": {en: "Available Time Slot", cn: "可用时隙"},
    "eq-type": {en: "EQ Type", cn: "卡类型"},
    "actual-eq-type": {en: "Actual EQ Type", cn: "卡实际类型"},
    "support-eq-type": {en: "Support EQ Type", cn: "卡支持类型"},
    "ptp-type": {en: "PTP Type", cn: "PTP类型"},
    "actual-ptp-type": {en: "Actual PTP Type", cn: "PTP实际类型"},
    "interface-type": {en: "Interface Type", cn: "接口类型"},
    "laser-status": {en: "Laser Status", cn: "激光器状态"},
    loopback: {en: "Loopback", cn: "环回"},
    "laser-auto-enable": {en: "Laser Auto Enable", cn: "激光器自动使能"},
    "laser-manual-enable": {en: "Laser Manual Enable", cn: "激光器手动使能"},
    "laser-enable": {en: "Laser Enable", cn: "激光器使能"},
    "current-working-mode": {en: "Current Working Mode", cn: "当前工作模式"},
    "current-mtu": {en: "Current MTU", cn: "当前MTU"},
    "pause-control": {en: "Pause Control", cn: "暂停控制"},
    "mac-address": {en: "MAC Address", cn: "MAC地址"},
    "port-type": {en: "Port Type", cn: "端口类型"},
    "lldp-enable": {en: "LLDP Enable", cn: "LLDP功能"},
    "current-signal-type": {en: "Current Signal Type", cn: "当前信号类型"},
    "e1-phy-type": {en: "E1 Phy Type", cn: "E1物理类型"},
    "e1-opcode": {en: "E1 Opcode", cn: "E1指令"},
    "e1-frame-type": {en: "E1 Frame Type", cn: "E1架构类型"},
    "client-ctp": {en: "Client CTP", cn: "客户侧CTP"},
    "server-ctp": {en: "Server CTP", cn: "线路侧CTP"},
    "tributary-port-num": {en: "Tributary Port Num", cn: "支路端口数据"},
    "oduk-level": {en: "Oduk Level", cn: "oduk级别"},
    tsNum: {en: "TS Number", cn: "时隙数量"},
    "ts-occupied-tx": {en: "TS Occupied TX", cn: "发送使用时隙"},
    "ts-occupied-rx": {en: "TS Occupied RX", cn: "接收使用时隙"},
    auto: {en: "Auto", cn: "自动"},
    "j0-actual-tx": {en: "J0 Actual TX", cn: "J0实际发送"},
    "j0-expected-rx": {en: "J0 Expected RX", cn: "J0预期接收"},
    "j0-actual-rx": {en: "J0 Actual RX", cn: "J0实际接收"},
    cli_login_success: {en: "CLI login", cn: "CLI登录"},
    start_upgrade: {en: "Upgrade card", cn: "升级卡"},
    start_upgrade_ing: {en: "Upgrading", cn: "升级中..."},
    upgrade_success: {en: "Upgrade succeeded", cn: "升级成功"},
    upgrade_completed: {en: "Upgrade completed", cn: "升级完成"},
    upgrade_completed_reboot: {en: "Upgrade completed, waiting reboot", cn: "升级完成,等待重启"},
    download_success: {en: "Download Success", cn: "下载完成"},
    "port-management": {en: "Port Management", cn: "端口管理"},
    och: {en: "OCH", cn: "OCH"},
    "signal-type": {en: "Signal Type", cn: "信号类型"},
    "module-type": {en: "Module Type", cn: "模块类型"},
    "reverse-mode": {en: "Reverse Mode", cn: "反转模式"},
    "otn-tti": {en: "OTN TTI", cn: "OTN TTI"},
    lldp: {en: "LLDP", cn: "LLDP"},
    "operational-mode": {en: "Operational Mode", cn: "模式"},
    frequency: {en: "Frequency", cn: "频率"},
    "target-output-power": {en: "Target Output Power", cn: "发送功率"},
    "output-frequency": {en: "Output Frequency", cn: "发送频率"},
    "pre-fec": {en: "PRE-FEC", cn: "PRE-FEC"},
    "post-fec": {en: "POST-FEC", cn: "POST-FEC"},
    "sm-tti-msg-transmit": {en: "SM Transmit", cn: "SM发送"},
    "sm-tti-msg-transmit-sapi": {en: "SM SAPI Transmit", cn: "SM SAPI发送"},
    "sm-tti-msg-transmit-dapi": {en: "SM DAPI Transmit", cn: "SM DAPI发送"},
    "sm-tti-msg-transmit-oper": {en: "SM Oper Transmit", cn: "SM Oper发送"},
    "sm-tti-msg-recv": {en: "SM Receive", cn: "SM接收"},
    "sm-tti-msg-recv-sapi": {en: "SM SAPI Receive", cn: "SM SAPI接收"},
    "sm-tti-msg-recv-dapi": {en: "SM DAPI Receive", cn: "SM DAPI接收"},
    "sm-tti-msg-recv-oper": {en: "SM Oper Receive", cn: "SM Oper接收"},
    "sm-tti-msg-expected": {en: "SM Expected", cn: "SM期望接收"},
    "sm-tti-msg-expected-sapi": {en: "SM SAPI Expected", cn: "SM SAPI期望接收"},
    "sm-tti-msg-expected-dapi": {en: "SM DAPI Expected", cn: "SM DAPI期望接收"},
    "sm-tti-msg-expected-oper": {en: "SM Oper Expected", cn: "SM Oper期望接收"},
    "pm-tti-msg-transmit": {en: "PM Transmit", cn: "PM 发送"},
    "pm-tti-msg-transmit-sapi": {en: "PM SAPI Transmit", cn: "PM SAPI发送"},
    "pm-tti-msg-transmit-dapi": {en: "PM DAPI Transmit", cn: "PM DAPI发送"},
    "pm-tti-msg-transmit-oper": {en: "PM Oper Transmit", cn: "PM Oper发送"},
    "pm-tti-msg-recv-sapi": {en: "PM SAPI Receive", cn: "PM SAPI接收"},
    "pm-tti-msg-recv-dapi": {en: "PM DAPI Receive", cn: "PM DAPI接收"},
    "pm-tti-msg-recv-oper": {en: "PM Oper Receive", cn: "PM Oper接收"},
    "pm-tti-msg-expected-sapi": {en: "PM SAPI Expected", cn: "PM SAPI期望接收"},
    "pm-tti-msg-expected-dapi": {en: "PM DAPI Expected", cn: "PM DAPI期望接收"},
    "pm-tti-msg-expected-oper": {en: "PM Oper Expected", cn: "PM Oper期望接收"},

    "target-gain": {en: "Target Gain", cn: "增益"},
    "target-gain-tilt": {en: "Target Gain Tilt", cn: "增益斜率"},
    "target-voa-attenuation": {en: "Target VOA Attenuation", cn: "VOA衰减"},
    "apr-enable": {en: "APR Enable", cn: "APR使能"},
    "input-power": {en: "Input Power", cn: "输入功率"},
    "output-power": {en: "Output Power", cn: "输出功率"},
    "actual-gain": {en: "Actual Gain", cn: "实际增益"},
    "ad-port": {en: "AD Port", cn: "AD端口"},
    "line-port": {en: "Line Port", cn: "线路侧端口"},
    "target-add-voa-attenuation": {en: "Add VOA Attenuation", cn: "Add VOA衰减"},
    "target-drop-voa-attenuation": {en: "Drop VOA Attenuation", cn: "Drop VOA衰减"},
    "min-edge-freq": {en: "Min Edge Frequency", cn: "频率下限"},
    "max-edge-freq": {en: "Max Edge Frequency", cn: "频率上限"},
    "actual-add-voa-attenuation": {en: "Actual Add VOA Attenuation", cn: "实际Add VOA衰减"},
    "actual-drop-voa-attenuation": {en: "Actual Drop VOA Attenuation", cn: "实际Drop VOA衰减"},
    "frequency-channel": {en: "Frequency Channel", cn: "频率通道"},
    "hello-timer": {en: "Hello Timer", cn: "握手周期"},
    "system-name": {en: "System Name", cn: "系统名称"},
    "system-description": {en: "System Description", cn: "系统描述"},
    "chassis-id": {en: "Chassis ID", cn: "机框ID"},
    "chassis-id-type": {en: "Chassis ID Type", cn: "机框ID类型"},
    "last-update": {en: "Last Update", cn: "最后更新"},
    "port-id": {en: "Port ID", cn: "端口ID"},
    "management-address": {en: "Management Address", cn: "管理地址"},
    "actual-voa-attenuation": {en: "Actual VOA Attenuation", cn: "实际VOA衰减"},
    "wait-to-restore-time": {en: "WTR Time", cn: "等待恢复时间"},
    "used-service-port-type-preconf": {en: "Speed", cn: "速率"},

    "lldp-state": {en: "LLDP", cn: "LLDP"},
    interfaces: {en: "Interfaces", cn: "Interfaces"},
    hex: {en: "HEX", cn: "转换为16进制字符"},
    dec: {en: "DEC", cn: "转换为可见字符"},
    // "collect-data": {en: "Collect Data", cn: "收集数据"},
    "active-path": {en: "Active Path", cn: "当前工作端口"},
    "aps-status": {en: "APS Status", cn: "APS状态"},
    revertive: {en: "Revertive", cn: "返回方式"},
    "wtr-time": {en: "Restore Time", cn: "等待恢复时间"},
    "hold-off-time": {en: "Hold Off Time", cn: "延迟时间"},
    "primary-switch-threshold": {en: "Primary Switch Threshold", cn: "主开关阈值"},
    "primary-switch-hysteresis": {en: "Primary Switch Hysteresis", cn: "主开关迟滞"},
    "secondary-switch-threshold": {en: "Secondary Switch Threshold", cn: "备开关阈值"},
    "relative-switch-threshold": {en: "Relative Switch Threshold", cn: "相对开关阈值"},
    "relative-switch-threshold-offset": {en: "Relative Switch Threshold Offset", cn: "相对开关阈值补偿"},
    "force-to-port": {en: "Force To Port", cn: "强制倒换"},
    card_type_match: {en: "Card Type Match", cn: "卡类型匹配"},
    clear_history_alarm: {en: "Clear", cn: "清除历史告警"},
    fileter_need_clear_history_alarm_warning: {
        en: "Please filter the NE that you want to clear history alarm at first.",
        cn: "请先选择过滤你需要清除历史告警的网元."
    },
    clear_history_alarm_warning: {
        en: "Are you sure you want to clear?",
        cn: "你确定要清除这历史告警吗?"
    },
    // device time manage
    time: {en: "Time", cn: "时间"},
    timezone: {en: "Timezone", cn: "时区"},
    ntp_template_v4_error: {en: "OTN device does not support V4 NTP template", cn: "OTN设备不支持V4版本的NTP模板"},
    time_manage: {en: "Time Management", cn: "时间管理"},
    use_current_time_and_timezone: {en: "Use the machine time and time zone", cn: "使用本机时间和时区"},
    check_selected_text_preffix: {en: "Selected", cn: "已选择"},
    check_selected_text_suffix: {en: "items", cn: "项"},
    history_alarm: {en: "History NE Alarm", cn: "网元历史告警"},
    current_config: {en: "Current Config", cn: "当前配置"},
    custom_config: {en: "Custom Config", cn: "自定义配置"},
    template: {en: "Template", cn: "模板"},
    ntp_enable: {en: "NTP Enable", cn: "开启NTP"},
    ntp_server1: {en: "NTP Server 1", cn: "NTP服务器1"},
    ntp_server2: {en: "NTP Server 2", cn: "NTP服务器2"},
    edit_ntp: {en: "Edit NTP", cn: "编辑NTP"},
    size_kb: {en: "Size", cn: "大小"},
    ne_1: {en: "NE 1", cn: "网元1"},
    ne_2: {en: "NE 2", cn: "网元2"},
    ptp_1: {en: "Port 1", cn: "端口1"},
    ptp_2: {en: "Port 2", cn: "端口2"},
    vcg: {en: "VCG", cn: "VCG"},
    "sdh-ftp-p": {en: "Protection SDH FTP", cn: "SDH FTP保护"},
    "mapping-path-p": {en: "Protection Mapping Path", cn: "保护时隙"},
    "mapping-path-number": {en: "Mapping Path Number", cn: "时隙数量"},

    clear_tca: {en: "Clear TCA", cn: "清除TCA"},
    clear_tca_warning: {
        en: "Are you sure to clear the PM center and TCA alarm?",
        cn: "你确定要清除PM计数器和TCA告警吗？"
    },
    clear_success: {en: "Clear succeeded", cn: "清除成功"},
    // clock_manage
    clock_manage: {en: "Clock Management", cn: "时钟管理"},
    global_clock_manage: {en: "Global Clock Manage", cn: "全局时钟管理"},
    clock_source_manage: {en: "Clock Source Manage", cn: "时钟源管理"},
    ex_clock_manage: {en: "EX Clock Manage", cn: "外时钟管理"},
    current_ql: {en: "Current Ql", cn: "当前质量等级"},
    pll_state: {en: "PLL State", cn: "锁定状态"},
    ssm_enable: {en: "SSM Enable", cn: "SMM使能状态"},
    minutes: {en: "Minutes", cn: "分钟"},
    use_current_time: {en: "Use Current Time", cn: "使用当前时间"},
    create_clock_source: {en: "Create Clock Source", cn: "创建时钟源"},
    edit_clock_source: {en: "Edit Sync Clock Source", cn: "编辑时钟源"},
    clock_source_port: {en: "Clock Source Port", cn: "时钟源端口"},
    priority: {en: "Priority", cn: "优先级"},
    "ssm-mode": {en: "SSM Mode", cn: "SSM模式"},
    "ssm-force-ql": {en: "SSM Force QL", cn: "时钟源强制质量等级"},
    "rx-ql": {en: "RX QL", cn: "时钟源接收质量等级"},
    "clk-2m-type": {en: "Clk 2M Type", cn: "2M时钟源类型"},
    "sa-tx-bit": {en: "Sa Tx Bit", cn: "发送sa-bit"},
    "sa-rx-bit": {en: "Sa Rx Bit", cn: "接收sa-bit"},
    "ql-limit": {en: "QL Limit", cn: "闭塞门限"},

    // ntp-template
    address: {en: "Address", cn: "地址"},
    version: {en: "Version", cn: "版本"},
    "association-type": {en: "Association Type", cn: "关联类型"},
    iburst: {en: "Iburst", cn: "Iburst"},
    ntp_template: {en: "NTP Template", cn: "NTP模板"},
    system_clock: {en: "System Clock", cn: "系统时钟查询"},
    "ssm-enable": {en: "SSM Enable", cn: "SSM使能状态"},
    "current-ql": {en: "Current QL", cn: "当前质量等级"},
    "pll-state": {en: "PLL State", cn: "锁定状态"},
    wtrt: {en: "WTRT", cn: "等待恢复时间"},
    "unit16-limit": {en: "Please enter an integer between 0 and 65535", cn: "请输入0-65535之间的整数"},
    // alarm_notice_settings
    alarm_notice_settings: {en: "Alarm Notice Settings", cn: "告警通知配置"},
    separated_semicolons: {en: "Multiple addresses separated by semicolons.", cn: "多个地址以分号分开."},
    "mail-service-host": {en: "Mail Service Host", cn: "邮箱服务地址"},
    "mail-service-port": {en: "Mail Service Port", cn: "邮箱服务端口"},
    "user-name": {en: "User Name", cn: "用户名"},
    subject: {en: "Subject", cn: "邮件主题"},
    "send-to": {en: "Receive", cn: "接收用户列表"},
    enable: {en: "Enable", cn: "启用"},
    disable: {en: "Disable", cn: "禁用"},
    mail_service_patter: {
        en: "The address of email service, e.g. smtp.microsoft.com",
        cn: "邮箱服务器地址,如smtp.microsoft.com"
    },
    send_to_patter: {
        en: "Email address, e.g. <EMAIL>;<EMAIL>",
        cn: "邮箱地址,如*****************;<EMAIL>"
    },
    control_card: {
        en: "Control Card",
        cn: "主控卡"
    },
    line_card: {
        en: "Line Card",
        cn: "线卡"
    },
    slot: {
        en: "Slot",
        cn: "槽位"
    },
    version_card: {
        en: "Version Card",
        cn: "卡版本"
    },
    version_file: {
        en: "Version File",
        cn: "升级版本"
    },
    file: {
        en: "File",
        cn: "文件"
    },
    ctl_state: {
        en: "CTL State",
        cn: "主备状态"
    },
    create_ntp_title: {
        en: "Create NTP Template",
        cn: "新建NTP模板"
    },
    empty_name: {
        en: "Please input your name",
        cn: "请输入名称"
    },
    empty_version: {
        en: "Please select version",
        cn: "请选择版本"
    },
    select_map: {
        en: "Select Map",
        cn: "切换地图"
    },
    gaode_map: {
        en: "Gaode Map",
        cn: "高德地图"
    },
    mapbox_map: {
        en: "Mapbox Map",
        cn: "Mapbox地图"
    },
    offline_map: {
        en: "Offline Map",
        cn: "离线地图"
    },
    not_found: {en: "Not Found", cn: "未找到"},
    using: {en: "using", cn: "使用中"},
    tokenless: {en: "tokenless", cn: "无token"},
    get: {en: "Get", cn: "获取"},
    map_token_config_warning: {en: "Please configure at least one map Token!", cn: "请至少配置一种地图Token！"},
    // custom_table
    export: {en: "Export", cn: "导出"},
    export_to_excel: {en: "Export to Excel", cn: "导出为Excel"},
    // alarm current
    alarm_name: {en: "Alarm Name", cn: "告警名称"},
    alarm_meaning: {en: "Alarm Meaning", cn: "告警含义"},
    report_object: {en: "Report Object", cn: "上报对象"},
    error_description: {en: "Error Description", cn: "故障描述"},
    treatment_suggestion: {en: "Treatment Suggestion", cn: "处理建议"},
    "operator-name": {en: "Operator Name", cn: "确认人员"},
    "operator-text": {en: "Operator Text", cn: "确认内容"},
    "time-acknowledged": {en: "Time Acknowledged", cn: "确认时间"},
    "time-deleted": {en: "Time Deleted", cn: "清除时间"},
    ack_alarm: {en: "ACK", cn: "告警确认"},
    alarm_ack_pattern: {
        en: "Only support 32 characters and not support the symbols < > & \" '",
        cn: "只支持32位字符,并且不支持符号 < > & \" '"
    },
    items: {en: "items", cn: "项"},
    of: {en: "of", cn: "共"},
    edfa_manager: {en: "EDFA", cn: "EDFA管理"},
    osc_manager: {en: "OSC", cn: "OSC管理"},
    wss_manager: {en: "Frequency Channels", cn: "WSS频率通道"},
    tff_manager: {en: "TFF Management", cn: "TFF管理"},
    aps_manager: {en: "APS Management", cn: "APS管理"},
    ocm_manager: {en: "OCM Management", cn: "OCM管理"},
    more: {en: "More", cn: "更多"},
    "optical-power-management": {en: "Optical Power Management", cn: "光功率管理"},
    two_linecard_must_same_port_type: {
        en: "Two Linecard must be the same type",
        cn: "两个电卡必须是相同类型"
    },
    read_community: {en: "Read Community", cn: "读团体字"},
    write_community: {en: "Write Community", cn: "写团体字"},
    trap_port: {en: "Trap Port", cn: "Trap端口"},
    applyText: {en: "Apply", cn: "应用"},
    clear: {en: "Clear", cn: "清除"},
    configure: {en: "Configure", cn: "配置"},
    drop_type_not_match_error: {en: "The node type does not match", cn: "节点类型不匹配"},
    ports: {en: "Ports", cn: "端口"},
    "otn-tti_manager": {en: "OTN TTI", cn: "OTN TTI"},
    lldp_manager: {en: "LLDP", cn: "LLDP"},
    info: {en: "Info", cn: "Info"},
    start_update_fc32: {
        en: "After modify the service type of FC32G , the card need to be hard restart",
        cn: "修改了FC32G信号类型后, 必须冷重启板卡"
    },
    effect_service_update: {
        en: "It will affect the service of port {0} , are you sure to continue to update?",
        cn: "本次修改将影响端口{0}的业务, 是否继续修改?"
    },
    reset_to_factory: {
        en: "Are you sure to Reset To Factory?",
        cn: "你确定要恢复出厂设置吗?"
    },
    reboot_warning_text: {
        en: "Are you sure to Reboot?",
        cn: "你确定要重启吗?"
    },
    one_decimal_warning: {
        en: "The modified value is a number and only supports one decimal place",
        cn: "修改的值为数字并只支持一位小数"
    },
    edit_range_warning: {
        en: "The modification range is {0} to {1}",
        cn: "修改范围是{0}到{1}"
    },

    // raman
    "pre-raman": {en: "Pre-Raman", cn: "前置拉曼"},
    "post-raman": {en: "Post-Raman", cn: "后置拉曼"},
    "left-pre-raman": {en: "Left Pre-Raman", cn: "左前置拉曼"},
    "left-post-raman": {en: "Left Post-Raman", cn: "左后置拉曼"},
    "right-pre-raman": {en: "Right Pre-Raman", cn: "右前置拉曼"},
    "right-post-raman": {en: "Right Post-Raman", cn: "右后置拉曼"},
    channel: {en: "Channel ", cn: "通道"},
    power_title_one: {en: "Power One ", cn: "电源 1"},
    power_title_two: {en: "Power Two ", cn: "电源 2"},
    power_table: {en: "Power ", cn: "电源"},
    pump_title_one: {en: "Pump One ", cn: "泵浦 1"},
    pump_title_two: {en: "Pump Two ", cn: "泵浦 2"},
    Pump: {en: "Pump ", cn: "泵浦"},
    sure_reset_factory: {en: "Are you sure to Reset To Factory?", cn: "确定要恢复出厂设置"},
    commonNetworkAddress: {en: "IP Address ", cn: "IP地址"},
    commonNESerialNumber: {en: "Serial Number ", cn: "序列号"},
    commonNEModelNumber: {en: "Model Number", cn: "型号"},
    commonDeviceFW: {en: "Device FW ", cn: "版本"},
    commonInternalTemperature: {en: "Temperature (℃)", cn: "温度 (℃)"},
    sysName: {en: "sysName", cn: "设备名称"},
    sysLocation: {en: "sysLocation", cn: "设备位置"},
    commonNEVendor: {en: "NE Vendor", cn: "设备供应商"},
    ramanChannelIndex: {en: "Index ", cn: "序号"},
    ramanChannelSignalPower: {en: "Signal Power (dBm) ", cn: "信号功率 (dBm)"},
    ramanChannelConfigGain: {en: "Config Gain (dB)", cn: "配置增益 (dB)"},
    ramanChannelOutputGain: {en: "Actual Gain (dB)", cn: "实际增益 (dB)"},
    ramanChannelPumpCtrl: {en: "Pump Ctrl", cn: "泵浦状态"},
    ramanChannelEyeProtect: {en: "Eye Protect", cn: "眼保护"},
    ramanChannelEyeProHigh: {en: "Eye ProHigh (dBm)", cn: "眼保护高阈值 (dBm)"},
    ramanChannelEyeProLow: {en: "Eye ProLow (dBm)", cn: "眼保护低阈值 (dBm)"},
    ramanPumpIndex: {en: "Index", cn: "序号"},
    ramanPumpBIAS: {en: "BIAS (mA)", cn: "BIAS (mA)"},
    ramanPumpTEC: {en: "TEC (mA)", cn: "制冷电流 (mA)"},
    ramanPumpTemp: {en: "Temperature (℃)", cn: "温度 (℃)"},
    ramanPumpPower: {en: "Power (dBm)", cn: "功率 (dBm)"},
    ramanDCPowerIndex: {en: "Index", cn: "序号"},
    ramanDCPowerVoltage: {en: "Voltage (V)", cn: "电压 (V)"},
    ramanDCPowerCurrent: {en: "Current (A)", cn: "电流 (A)"},
    commonDeviceAlarmDetectionControl: {en: "Alarm Status", cn: "告警状态"},
    fail_message: {
        en: "The modified value is a number and only supports one decimal place",
        cn: "修改的值为数字并只支持一位小数"
    },
    eyePro_message: {
        en: "The high threshold for eye protection is greater than the low threshold for eye protection",
        cn: "眼保护高阈值要大于眼保护低阈值"
    },
    modify_range: {
        en: "The modification range is 0.0 to {0}.0",
        cn: "修改范围是0.0到{0}.0"
    },
    reset_factory: {
        en: "Reset To Factory",
        cn: "恢复出厂设置"
    },
    range_38_length: {en: "Only supports 0~38 byte characters", cn: "只支持0~38位字节的字符串"},
    "forward-raman": {en: "Co-RFA", cn: "正向拉曼"},
    "backward-raman": {en: "RFA", cn: "反向拉曼"},
    "left-forward-raman": {en: "Left Co-RFA", cn: "左侧正向拉曼"},
    "left-backward-raman": {en: "Left RFA", cn: "左侧反向拉曼"},
    "right-forward-raman": {en: "Right Co-RFA", cn: "右侧正向拉曼"},
    "right-backward-raman": {en: "Right RFA", cn: "右侧反向拉曼"}
};

const getLabelList = lang => Object.fromEntries(Object.entries(textMapping).map(([k, v]) => [k, v[lang]]));

// eslint-disable-next-line import/no-mutable-exports
export let gLabelList = getLabelList("en");
// eslint-disable-next-line import/no-mutable-exports
export let gLanguage = "en";

const languageOTNSlice = createSlice({
    name: "languageOTN",
    initialState: {
        language: "en",
        labelList: getLabelList("en"),
        ready: false
    },
    reducers: {
        setOTNLanguage: (state, action) => {
            if (state.language !== action.payload) {
                state.language = action.payload;
                gLanguage = state.language;
                gLabelList = getLabelList(state.language);
                state.labelList = getLabelList(state.language);
            }
            state.ready = true;
        }
    }
});

export const {setOTNLanguage} = languageOTNSlice.actions;
export default languageOTNSlice.reducer;
