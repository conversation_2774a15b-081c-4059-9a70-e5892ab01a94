import {createSlice} from "@reduxjs/toolkit";

const neNameSlice = createSlice({
    name: "neName",
    initialState: {
        neNameMap: {},
        neTypeMap: {},
        ready: false
    },
    reducers: {
        setNeNameMap: (state, action) => {
            state.neNameMap = Object.fromEntries(action.payload.map(item => [item.value.ne_id, item.value.name]));
            state.neTypeMap = Object.fromEntries(action.payload.map(item => [item.value.ne_id, item.value.type]));
            state.ready = true;
        }
    }
});

export const {setNeNameMap} = neNameSlice.actions;
export default neNameSlice.reducer;
