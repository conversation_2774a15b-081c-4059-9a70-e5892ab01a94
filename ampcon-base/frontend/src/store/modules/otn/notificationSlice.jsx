import {createSlice} from "@reduxjs/toolkit";
import {removeNSForObj} from "@/modules-otn/utils/util";

const notificationSlice = createSlice({
    name: "notification",
    initialState: {
        alarms: [],
        tnmsAlarms: [],
        events: [],
        upgrade: {},
        ready: 0,
        dataChanged: {},
        switchAlarms: []
    },
    reducers: {
        updateEvent: (state, action) => {
            state.events = action.payload;
            // eslint-disable-next-line no-bitwise
            state.ready |= 1;
        },
        newEvent: (state, action) => {
            state.events.unshift(action.payload.data);
        },
        updateAlarm: (state, action) => {
            state.alarms = action.payload.map(item => ({
                ne_id: item.value.ne_id,
                ...removeNSForObj(item.value.data.state)
            }));
            // eslint-disable-next-line no-bitwise
            state.ready |= 2;
        },
        updateNEAlarms: (state, action) => {
            const {data, source} = action.payload;
            state.alarms = state?.alarms?.filter(alarm => alarm.ne_id !== source).concat(data);
        },
        newAlarm: (state, action) => {
            const idx = state?.alarms?.findIndex?.(
                ({ne_id, id}) => ne_id === action.payload.data.ne_id && id === action.payload.data.item.id
            );
            if (idx > -1) {
                // eslint-disable-next-line no-console
                console.warn("New alarm but already exist", action.payload.data);
                return;
            }
            state.alarms.unshift({
                ...action.payload.data,
                ne_id: action.payload.source.ne_id
            });
        },
        deleteAlarm: (state, action) => {
            const idx = state?.alarms?.findIndex?.(
                ({ne_id, id}) => ne_id === action.payload.source.ne_id && id === action.payload.data.id
            );
            if (idx < 0) {
                // eslint-disable-next-line no-console
                console.error("No alarm found to delete", action.payload);
                return;
            }
            state.alarms.splice(idx, 1);
        },
        newTNMSAlarm: (state, action) => {
            const idx = state.tnmsAlarms?.findIndex?.(
                ({ne_id, id}) => ne_id === action.payload.data.ne_id && id === action.payload.data.key
            );
            if (idx > -1) {
                return;
            }
            state.tnmsAlarms?.unshift?.({
                ...removeNSForObj(action.payload.data.item.state),
                ne_id: action.payload.data.ne_id
            });
        },
        deleteTNMSAlarm: (state, action) => {
            const idx = state.tnmsAlarms?.findIndex?.(
                ({ne_id, id}) => ne_id === action.payload.data.ne_id && id === action.payload.data.key
            );
            if (idx < 0) {
                // eslint-disable-next-line no-console
                console.warn("没有找到", action.payload.data);
                return;
            }
            state.tnmsAlarms?.splice?.(idx, 1);
        },
        updateTNMSAlarm: (state, action) => {
            state.tnmsAlarms = action.payload;
        },
        updateUpgrade: (state, action) => {
            state.upgrade = action.payload;
        },
        updateDataChanged: (state, action) => {
            state.dataChanged = action.payload;
        },
        updateSwitchAlarm: (state, action) => {
            if (
                state.switchAlarms.length !== action.payload.length ||
                state.switchAlarms.findIndex(
                    ({id, "time-cleared": timeCleared}, index) =>
                        id !== action.payload[index].data.id ||
                        timeCleared !== action.payload[index].data.state['"time-cleared"']
                ) > -1
            ) {
                state.switchAlarms = action.payload.map(i => ({
                    ...i.data.state,
                    id: i.data.id,
                    ne_id: i.ne_id,
                    severity: i.data.state.severity.toUpperCase()
                }));
            }
        }
    }
});

export const {
    updateAlarm,
    updateEvent,
    newAlarm,
    deleteAlarm,
    newTNMSAlarm,
    deleteTNMSAlarm,
    updateUpgrade,
    newEvent,
    updateTNMSAlarm,
    updateNEAlarms,
    updateDataChanged,
    updateSwitchAlarm
} = notificationSlice.actions;
export default notificationSlice.reducer;
