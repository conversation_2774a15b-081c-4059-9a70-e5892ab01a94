import {createSlice} from "@reduxjs/toolkit";
import {loginAPI, logoutAPI, checkStatusAPI} from "@/apis/common/user";
import {login_smb, logout_smb} from "@/modules-smb/router/account_smb";
import {message} from "antd";

const userStore = createSlice({
    name: "user",
    initialState: {
        userInfo: {}
    },
    reducers: {
        setUserInfo(state, action) {
            state.userInfo = action.payload;
        },
        clearUserInfo(state) {
            state.userInfo = {};
        }
    }
});

const {setUserInfo, clearUserInfo} = userStore.actions;

const fetchLogin = (loginForm, navigate) => {
    return async dispatch => {
        console.log("loginForm:", loginForm);
        const res = await loginAPI(loginForm);
        if (res.status === 200) {
            message.success("Login success");
            // 登录ampcon成功后，登录smb，生成smb token
            await login_smb(loginForm.username, loginForm.password, loginForm.remember);
            navigate("/");
            location.reload();
        } else {
            dispatch(clearUserInfo());
            navigate("/login");
            message.error(`Login failed:${res.msg}`);
        }
    };
};

const fetchLogout = () => {
    return async dispatch => {
        // 注销ampcon成功后，注销smb，清理smb token
        await logout_smb();
        const res = await logoutAPI();
        if (res.status === 200) {
            dispatch(clearUserInfo());
        }
    };
};

const checkStatus = () => {
    return async dispatch => {
        const res = await checkStatusAPI();
        if (res.status === 200) {
            dispatch(setUserInfo(res.data));
        } else {
            dispatch(clearUserInfo());
        }
    };
};

const userReducer = userStore.reducer;

export {fetchLogin, fetchLogout, checkStatus};

export default userReducer;
