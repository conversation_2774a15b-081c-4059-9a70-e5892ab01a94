import {createSlice} from "@reduxjs/toolkit";
import {getUnreadAlarmList} from "@/modules-ampcon/apis/monitor_api";
import {message} from "antd";

const alarmStore = createSlice({
    name: "alarm",
    initialState: {
        alarmInfo: [0, 0, 0],
        alarmSearch: "",
        alarmStatus: false,
        alarmClickCount: 0
    },
    reducers: {
        updateAlarm: (state, action) => {
            state.alarmInfo = action.payload;
        },
        updateAlarmType: (state, action) => {
            state.alarmSearch = action.payload;
            state.alarmClickCount += 1;
        },
        updateAlarmStatus: (state, action) => {
            state.alarmStatus = action.payload;
        }
    }
});

const {updateAlarm, updateAlarmType, updateAlarmStatus} = alarmStore.actions;

const getAlarmCount = () => {
    return async dispatch => {
        const res = await getUnreadAlarmList();
        if (res.status === 200) {
            dispatch(updateAlarm([res.criticalCount, res.infoCount, res.warningCount]));
        } else {
            dispatch(updateAlarm([0, 0, 0]));
            message.error("Get alarm message failed, please check network");
        }
    };
};

const updateAlarmSearch = type => {
    return async dispatch => {
        dispatch(updateAlarmType(type));
    };
};

const updateAlarmSearchStatus = type => {
    return async dispatch => {
        dispatch(updateAlarmStatus(type));
    };
};

const alarmReducer = alarmStore.reducer;

export {getAlarmCount, updateAlarmSearch, updateAlarmSearchStatus};

export default alarmReducer;
