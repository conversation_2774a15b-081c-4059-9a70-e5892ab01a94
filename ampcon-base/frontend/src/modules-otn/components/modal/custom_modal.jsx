import Icon, {CloseOutlined} from "@ant-design/icons";
import {classNames, rootModal} from "@/modules-otn/utils/util";
import {confirmIcon} from "@/modules-otn/pages/otn/device/device_icons";

export function ConfirmModalHeader({cancelEnable, title, onDestoryModal}) {
    return (
        <div
            style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                padding: "20px !important",
                borderBottom: "1px solid #E7E7E7",
                color: "rgba(0,0,0,0.88)",
                lineHeight: 1
            }}
        >
            <span style={{fontWeight: 600, fontSize: "20px"}}>{title}</span>
            <div style={{fontSize: 16}}>{cancelEnable && <CloseOutlined onClick={onDestoryModal} />}</div>
        </div>
    );
}

export const bigModal = config => {
    const modal = rootModal.confirm({
        ...config,
        icon: null,
        width: config?.width || 1360,
        title: (
            <ConfirmModalHeader
                title={config.title}
                onDestoryModal={() => {
                    modal?.destroy();
                }}
                cancelEnable
            />
        ),
        className: classNames([config?.className ? config?.className : "", "big-modal"])
        // centered: true
    });
    return modal;
};

export const middleModal = config => {
    const modal = rootModal.confirm({
        ...config,
        icon: null,
        width: 680,
        title: (
            <ConfirmModalHeader
                title={config.title}
                onDestoryModal={() => {
                    modal?.destroy();
                }}
                cancelEnable={config.closable ?? true}
            />
        ),
        className: classNames([config?.className ? config?.className : "", "middle-modal"])
        // centered: true
    });
    return modal;
};

export const smallModal = config => {
    const modal = rootModal.confirm({
        ...config,
        icon: null,
        width: 480,
        title: (
            <ConfirmModalHeader
                title="Note"
                onDestoryModal={() => {
                    modal?.destroy();
                }}
                cancelEnable
            />
        ),
        content: (
            <div style={{display: "flex", alignContent: "center"}}>
                <Icon component={confirmIcon} style={{marginRight: 8}} />
                <span style={{fontWeight: 500, fontSize: "14px", color: "#212519"}}>{config?.content}</span>
            </div>
        ),
        className: classNames([config?.className ? config?.className : "", "small-modal"])
        // centered: true
    });
    return modal;
};
