.customTable_ctn {
    padding: 5px;

    &_header {
        padding: 8px 15px;
        border-bottom: 1px solid #e22020;

        &_title {
            font-size: 16px;
            font-weight: 600;
        }
    }

    &_content {
        display: flex;
        flex-direction: column;
        overflow: auto;
        margin: 5px 10px;

        &_row {
            // font-size: 12px;
            // line-height: 1;
        }

        &_cell {
            padding: 1px 16px !important;
            justify-content: center;
            height: 48px !important;
        }

        &_headerCell {
            padding: 9px 16px !important;
            height: 40px !important;
        }
    }
}

.table_header {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #f0f0f0;

    &_title {
        margin-bottom: 32px;

        font-weight: 700;
        font-size: 24px;
        color: #212519;
        line-height: 29px;
        text-align: left;
        font-style: normal;
        text-transform: none;

    }

    &_operation {
        display: flex;

    }
}

:global{
    .ant-input-affix-wrapper:focus-within{
        border-color: #14c9bb !important;
        box-shadow: transparent;
    }

    .ant-table-tbody .ant-btn-link {
        padding: 0;
        border: none;
    }

    .ant-tag {
        font-size: 14px;
    }
}

.fixFixedTableLeftBorder {
    :global {
        .ant-table-container {
            border-inline-start: inherit !important;

            .ant-table-expanded-row-fixed,
            tr > .ant-table-cell-fix-left:first-child {
                border-left: 1px solid #f0f0f0;
            }
        }
    }
}

.fixExpandableWidth {
    :global {
        td.ant-table-cell.ant-table-row-expand-icon-cell,
        th.ant-table-cell.ant-table-selection-column {
            width: 50px;
            padding-top: 9px;
            padding-bottom: 9px;
        }

        .ant-table-pagination .ant-pagination-options {
            .ant-pagination-options-size-changer, .ant-pagination-options-quick-jumper {
                width: auto;
            }
        }
    }
}


.fixTableYScroll {
    :global {
        td.ant-table-cell.ant-table-cell-scrollbar {
            //display: table-cell;
            width: 5px;
            height: 48px;
            background: #FFF;
            border: none !important;
            border-radius: 0 !important;
        }
        .ant-table-container {
            border-top: none !important;

            thead.ant-table-thead > tr > th:last-of-type {
                border-top-right-radius: 5px;
                border-right: 1px solid #f0f0f0;
                z-index: 3;
            }
        }
        .ant-table-thead > tr > th {
            border-top: 1px solid #f0f0f0
        }
    }
}