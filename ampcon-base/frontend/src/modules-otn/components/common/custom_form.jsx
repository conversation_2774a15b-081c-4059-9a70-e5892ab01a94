import {useEffect, useState} from "react";
import {useSelector} from "react-redux";
import {Button} from "antd";
import {SaveOutlined, SyncOutlined} from "@ant-design/icons";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {TableConfig} from "@/modules-otn/config/table_config";
import {CreateDBForm} from "@/modules-otn/components/form/create_form_db";
import tableStyle from "@/modules-otn/components/common/edit_table.module.scss";
import {objectGet} from "@/modules-otn/apis/api";

const CustomForm = ({type, msg = true, success, fail, submit, initDataAuto}) => {
    const {labelList} = useSelector(state => state.languageOTN);
    const userRight = useUserRight();
    const [initData, setInitData] = useState({});
    const [loading, setLoading] = useState(false);
    const config = TableConfig[type];

    let form;
    const handle = fun => {
        form = fun;
    };

    let saved = false;

    const afterFinish = () => {
        setLoading(false);
    };

    const processFail = () => {
        setLoading(false);
        setTimeout(() => {
            saved = false;
        }, 1000);
    };

    const commitForm = () => {
        form.validateFields().then(() => {
            if (saved) return;
            saved = true;
            setLoading(true);
            form.submit();
        });
    };

    const loadData = data => {
        const initValues = {};
        Object.entries(data).map(([k, v]) => {
            if (config?.columns?.filter(i => i.dataIndex === k)?.[0]?.init === false) return;
            initValues[k] = {value: v};
        });
        setInitData(initValues);
    };

    useEffect(() => {
        if (initDataAuto) {
            objectGet("", {DBKey: initDataAuto.key}, null, false).then(rs => {
                if (rs?.documents?.length > 0) {
                    loadData(rs.documents[0].value);
                }
            });
        }
    }, []);

    return (
        <div style={{display: "flex", flexDirection: "column"}}>
            <div className={tableStyle.edit_table_header}>
                <span className={tableStyle.edit_table_header_title}>
                    {config.title ? labelList[config.title] : labelList[type] ?? type}
                </span>
            </div>
            <div style={{width: 500, padding: "12px 0"}}>
                <CreateDBForm
                    type={type}
                    processFail={processFail}
                    onCancel={afterFinish}
                    setForm={handle}
                    success={success}
                    fail={fail}
                    msg={msg}
                    submit={submit}
                    initData={initData}
                />
            </div>
            <div style={{paddingLeft: 20}}>
                <Button
                    icon={loading ? <SyncOutlined spin style={{color: "#087bee"}} /> : <SaveOutlined />}
                    disabled={loading || userRight.disabled}
                    type="primary"
                    onClick={commitForm}
                >
                    {labelList.save}
                </Button>
            </div>
        </div>
    );
};

export default CustomForm;
