.chassisView {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    margin: 0;
    border: 1px solid #95b8e7;
    border-radius: 3px;
    overflow: auto;
}

.wrap {
    display: flex;
    flex-direction: column;
}

.header {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    font-size: 12px;
    border-bottom: 1px solid #ddd;
    flex-direction: row-reverse;
}

.diagram {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow-x: auto;
}

.button {
    margin-left: 50px;
}

.select-entity-view {
    padding: 5px;
}

.loading {
    font-size: 18px;
    font-weight: 800;
}

$ONE_SLOT_WIDTH: 304px;
$TWO_SLOT_WIDTH: 609px;
$ONE_U_HEIGHT: 131.5px;

$CHASSIS_HEIGHT: 272.5px;
$CHASSIS_WIDTH: 1308.5px;


/*
Equipment
*/
.entity_common {
    top: 3px;
    position: absolute;
    background-size: 100% 100%;
}

.entity_selected {
    position: absolute;
    z-index: 100;
}

.chassis_common {
    position: relative;
    width: $CHASSIS_WIDTH;
    height: $CHASSIS_HEIGHT;
}

.chassis_common_half {
    position: relative;
    width: $CHASSIS_WIDTH;
    height: 136px;
    margin: 10px;
}

.chassis_front {
    background-image: url("img_fs/card/chassis_front.png");
}

.chassis_rear {
    background-image: url("img_fs/card/chassis_rear.png");
}

.chassis_zMUX48,
.chassis_OMD48ECM {
    height: $ONE_U_HEIGHT;
}

.chassis_zMUX96 {
    height: $CHASSIS_HEIGHT;
}


.SLOT_common {
    height: $ONE_U_HEIGHT;
    width: $ONE_SLOT_WIDTH;
    background-image: url("./img_fs/card/SLOT.png");
}

.CARD_OMD48ECM {
    background-image: url("./img_fs/card/OMD48ECM.png");
    width: $CHASSIS_WIDTH;
    height: $ONE_U_HEIGHT;
}

/**
*   Card settings
 */

.CARD_ROADM-09T {
    width: $TWO_SLOT_WIDTH;
    background-image: url("img_fs/card/ROADM-09T.png");
    height: $ONE_U_HEIGHT;
}

.CARD_D7000-EMU {
    width: 141px;
    height: $ONE_U_HEIGHT;
    background-image: url("img_fs/card/EMU.png");
}

.CARD_D7000-AUX {
    width: 86px;
    height: 266px;
    background-image: url("img_fs/card/AUX_FS.png");
}

.CARD_OPB2 {
    width: $ONE_SLOT_WIDTH;
    height: $ONE_U_HEIGHT;
    background-image: url("img_fs/card/OPB2.png");
}

.CARD_OCM08 {
    width: $ONE_SLOT_WIDTH;
    height: $ONE_U_HEIGHT;
    background-image: url("img_fs/card/OCM08.png");
}

.CARD_OPB2-I {
    width: $ONE_SLOT_WIDTH;
    height: $ONE_U_HEIGHT;
    background-image: url("img_fs/card/OPB2-I.png");
}

.CARD_TFF1619 {
    width: $ONE_SLOT_WIDTH;
    background-image: url("img_fs/card/TFF04 (1619).png");
    height: $ONE_U_HEIGHT;
}

.CARD_TFF2023 {
    width: $ONE_SLOT_WIDTH;
    background-image: url("img_fs/card/TFF04 (2023).png");
    height: $ONE_U_HEIGHT;
}

.CARD_TFF2528 {
    width: $ONE_SLOT_WIDTH;
    background-image: url("img_fs/card/TFF04 (2528).png");
    height: $ONE_U_HEIGHT;
}

.CARD_TFF2932 {
    width: $ONE_SLOT_WIDTH;
    background-image: url("img_fs/card/TFF04 (2932).png");
    height: $ONE_U_HEIGHT;
}

.CARD_TFF3437 {
    width: $ONE_SLOT_WIDTH;
    background-image: url("img_fs/card/TFF04 (3437).png");
    height: $ONE_U_HEIGHT;
}

.CARD_TFF3841 {
    width: $ONE_SLOT_WIDTH;
    background-image: url("img_fs/card/TFF04 (3841).png");
    height: $ONE_U_HEIGHT;
}

.CARD_TFF4346 {
    width: $ONE_SLOT_WIDTH;
    background-image: url("img_fs/card/TFF04 (4346).png");
    height: $ONE_U_HEIGHT;
}

.CARD_TFF4750 {
    width: $ONE_SLOT_WIDTH;
    background-image: url("img_fs/card/TFF04 (4750).png");
    height: $ONE_U_HEIGHT;
}

.CARD_TFF5255 {
    width: $ONE_SLOT_WIDTH;
    background-image: url("img_fs/card/TFF04 (5255).png");
    height: $ONE_U_HEIGHT;
}

.CARD_TFF5659 {
    width: $ONE_SLOT_WIDTH;
    background-image: url("img_fs/card/TFF04 (5659).png");
    height: $ONE_U_HEIGHT;
}

.CARD_OTDR08 {
    width: $ONE_SLOT_WIDTH;
    height: $ONE_U_HEIGHT;
    background-image: url("img_fs/card/OTDR08.png");
}

.CARD_2MC2 {
    width: $ONE_SLOT_WIDTH;
    height: $ONE_U_HEIGHT;
    background-image: url("img_fs/card/2MC2.png");
}

.CARD_4MC4 {
    width: $ONE_SLOT_WIDTH;
    height: $ONE_U_HEIGHT;
    background-image: url("img_fs/card/4MC4.png");
}


.CARD_11MC2 {
    width: $TWO_SLOT_WIDTH;
    height: $ONE_U_HEIGHT;
    background-image: url("img_fs/card/11MC2.png");
}

.CARD_20MC2 {
    width: $TWO_SLOT_WIDTH;
    height: $ONE_U_HEIGHT;
    background-image: url("img_fs/card/20MC2.png");
}

.CARD_D7000-PSU-AC {
    width: 227px;
    height: 132px;
    background-image: url("img_fs/card/PSU_AC.png");
}

.CARD_D7000-PSU-DC {
    width: 227px;
    height: 132px;
    background-image: url("img_fs/card/PSU_DC.png");
}

.CARD_OA1825 {
    width: $TWO_SLOT_WIDTH;
    height: $ONE_U_HEIGHT;
    background-image: url("img_fs/card/OA1825.png");
}

.CARD_OA1835 {
    width: $TWO_SLOT_WIDTH;
    height: $ONE_U_HEIGHT;
    background-image: url("img_fs/card/OA1835.png");
}

.CARD_OLA2525 {
    width: $TWO_SLOT_WIDTH;
    height: $ONE_U_HEIGHT;
    background-image: url("img_fs/card/OLA2525.png");
}

.CARD_FAKE {
    filter: contrast(0.85);
}

/**
*   Port settings
 */
.Port-D7000-AUX-NM1, .Port-D7000-AUX-NM2, .Port-D7000-AUX-ETH, .Port-D7000-AUX-CON {
    width: 41px;
    height: 51px;
    background-image: url("img_fs/port/port_e.png");
}

.Port-TFF-CH1, .Port-TFF-CH2, .Port-TFF-CH3, .Port-TFF-CH4, .Port-TFF-PT1,
.Port-WSS-AD1, .Port-WSS-AD2, .Port-WSS-AD3, .Port-WSS-AD4, .Port-WSS-AD5, .Port-WSS-AD6, .Port-WSS-AD7, .Port-WSS-AD8, .Port-WSS-AD9,
.Port-OLP-1-APSP, .Port-OLP-1-APSS, .Port-OLP-2-APSP, .Port-OLP-2-APSS,
.Port-PTMW-SIG, .Port-PTMW-C60,
.Port-PTME-SIG, .Port-PTME-C61,
.Port-MUX-MON, .Port-MUX-MUX,
.Port-MUX-CH1, .Port-MUX-CH2, .Port-MUX-CH3, .Port-MUX-CH4, .Port-MUX-CH5, .Port-MUX-CH6, .Port-MUX-CH7, .Port-MUX-CH8, .Port-MUX-CH9, .Port-MUX-CH10,
.Port-MUX-CH11, .Port-MUX-CH12, .Port-MUX-CH13, .Port-MUX-CH14, .Port-MUX-CH15, .Port-MUX-CH16, .Port-MUX-CH17, .Port-MUX-CH18, .Port-MUX-CH19, .Port-MUX-CH20,
.Port-MUX-CH21, .Port-MUX-CH22, .Port-MUX-CH23, .Port-MUX-CH24, .Port-MUX-CH49, .Port-MUX-CH50, .Port-MUX-CH51, .Port-MUX-CH52, .Port-MUX-CH53, .Port-MUX-CH54,
.Port-MUX-CH55, .Port-MUX-CH56, .Port-MUX-CH57, .Port-MUX-CH58, .Port-MUX-CH59, .Port-MUX-CH60, .Port-MUX-CH61, .Port-MUX-CH62, .Port-MUX-CH63, .Port-MUX-CH64,
.Port-MUX-CH65, .Port-MUX-CH66, .Port-MUX-CH67, .Port-MUX-CH68, .Port-MUX-CH69, .Port-MUX-CH70, .Port-MUX-CH71, .Port-MUX-CH72, .Port-MUX-CH73, .Port-MUX-CH74,
.Port-MUX-CH75, .Port-MUX-CH76, .Port-MUX-CH77, .Port-MUX-CH78, .Port-MUX-CH79, .Port-MUX-CH80, .Port-MUX-CH81, .Port-MUX-CH82, .Port-MUX-CH83, .Port-MUX-CH84,
.Port-MUX-CH85, .Port-MUX-CH86, .Port-MUX-CH87, .Port-MUX-CH88, .Port-MUX-CH89, .Port-MUX-CH90, .Port-MUX-CH91, .Port-MUX-CH92, .Port-MUX-CH93, .Port-MUX-CH94,
.Port-MUX-CH95, .Port-MUX-CH96, .CARD_MUX96 > .Port-MUX-CH25, .CARD_MUX96 > .Port-MUX-CH26, .CARD_MUX96 > .Port-MUX-CH27, .CARD_MUX96 > .Port-MUX-CH28, .CARD_MUX96 > .Port-MUX-CH29,
.CARD_MUX96 > .Port-MUX-CH30, .CARD_MUX96 > .Port-MUX-CH31, .CARD_MUX96 > .Port-MUX-CH32, .CARD_MUX96 > .Port-MUX-CH33, .CARD_MUX96 > .Port-MUX-CH34, .CARD_MUX96 > .Port-MUX-CH35,
.CARD_MUX96 > .Port-MUX-CH36, .CARD_MUX96 > .Port-MUX-CH37, .CARD_MUX96 > .Port-MUX-CH38, .CARD_MUX96 > .Port-MUX-CH39, .CARD_MUX96 > .Port-MUX-CH40, .CARD_MUX96 > .Port-MUX-CH41,
.CARD_MUX96 > .Port-MUX-CH42, .CARD_MUX96 > .Port-MUX-CH43, .CARD_MUX96 > .Port-MUX-CH44, .CARD_MUX96 > .Port-MUX-CH45, .CARD_MUX96 > .Port-MUX-CH46, .CARD_MUX96 > .Port-MUX-CH47,
.CARD_MUX96 > .Port-MUX-CH48 {
    width: 43px;
    height: 31px;
    background-image: url("img_fs/port/port_o_2.png");
}

.Port-MUX-CH25, .Port-MUX-CH26, .Port-MUX-CH27, .Port-MUX-CH28, .Port-MUX-CH29, .Port-MUX-CH30,
.Port-MUX-CH31, .Port-MUX-CH32, .Port-MUX-CH33, .Port-MUX-CH34, .Port-MUX-CH35, .Port-MUX-CH36, .Port-MUX-CH37, .Port-MUX-CH38, .Port-MUX-CH39, .Port-MUX-CH40,
.Port-MUX-CH41, .Port-MUX-CH42, .Port-MUX-CH43, .Port-MUX-CH44, .Port-MUX-CH45, .Port-MUX-CH46, .Port-MUX-CH47, .Port-MUX-CH48 {
    width: 43px;
    height: 31px;
    background-image: url("img_fs/port/port_o_2.png");
}

.Port-TFF-MUX,
.Port-OLP-1-APSC, .Port-OLP-2-APSC,
.Port-PTMW-LINE,
.Port-PTME-LINE, .CARD_MUX96 > .Port-MUX-MUX {
    width: 43px;
    height: 31px;
    background-image: url("img_fs/port/port_o_2.png");
}

.Port-MUX-MUX {
    width: 43px;
    height: 31px;
    background-image: url("img_fs/port/port_o_2.png");
}

.Port-WSS-PAIN, .Port-WSS-BAOUT,
.Port-WSS-OTDR1, .Port-WSS-OTDR2,
.Port-WSS-MON1, .Port-WSS-MON2,
.Port-OTDR_CARD-OTDR1, .Port-OTDR_CARD-OTDR2, .Port-OTDR_CARD-OTDR3, .Port-OTDR_CARD-OTDR4, .Port-OTDR_CARD-OTDR5, .Port-OTDR_CARD-OTDR6, .Port-OTDR_CARD-OTDR7, .Port-OTDR_CARD-OTDR8,
.Port-OA-PAIN, .Port-OA-PAOUT,
.Port-OA-BAIN, .Port-OA-BAOUT,
.Port-OA-OTDR1, .Port-OA-OTDR2,
.Port-OA-MON1, .Port-OA-MON2,
.Port-OCM_CARD-OCM1, .Port-OCM_CARD-OCM2, .Port-OCM_CARD-OCM3, .Port-OCM_CARD-OCM4, .Port-OCM_CARD-OCM5, .Port-OCM_CARD-OCM6, .Port-OCM_CARD-OCM7, .Port-OCM_CARD-OCM8,
.Port-OLA-OTDR1, .Port-OLA-OTDR2, .Port-OLA-OTDR3, .Port-OLA-OTDR4, .Port-OLA-LA1IN, .Port-OLA-LA1OUT, .Port-OLA-LA2IN, .Port-OLA-LA2OUT,
.Port-OLA-MON1, .Port-OLA-MON2, {
    width: 19px;
    height: 27px;
    background-image: url("img_fs/port/port_o_1.png");
}

.Port-LINECARD-L1 {
    width: 109px;
    height: 38px;
    //background-color: #333333;
}

.Port-LINECARD-C1, .Port-LINECARD-C2, .Port-LINECARD-C3, .Port-LINECARD-C4, .Port-LINECARD-C5, .Port-LINECARD-C6, .Port-LINECARD-C7, .Port-LINECARD-C8, .Port-LINECARD-C9, .Port-LINECARD-C10,
.Port-LINECARD-C11, .Port-LINECARD-C12, .Port-LINECARD-C13, .Port-LINECARD-C14, .Port-LINECARD-C15, .Port-LINECARD-C16, .Port-LINECARD-C17, .Port-LINECARD-C18, .Port-LINECARD-C19, .Port-LINECARD-C20,
.Port-LINECARD-C21,
.Port-PTMW-S60, .Port-PTMW-S61,
.Port-PTME-S60, .Port-PTME-S61 {
    width: 46px;
    height: 32px;
    //background-color: #333333;
}

/**
* FAN settings
 */
.CARD_D7000-FAN {
    background-image: url("img_fs/card/FAN.png");
    display: flex;
    align-items: center;
    justify-content: center;
    width: 227px;
    height: 266px;
}

.icon_fan_common {
    font-size: 68px;
    opacity: 0.5;
}

@keyframes loading_circle {
    100% {
        transform: rotate(360deg);
    }
}

.CARD_D7000-FAN .fanSpeed_LOW {
    animation: loading_circle 1s infinite linear;
    color: rgb(104 104 126 / 84%);
}

.CARD_D7000-FAN .fanSpeed_MIDDLE {
    animation: loading_circle 0.5s infinite linear;
    color: rgb(159 154 122 / 84%);
}

.CARD_D7000-FAN .fanSpeed_HIGH {
    opacity: 0.7;
    animation: loading_circle 0.2s infinite linear;
    color: rgba(192, 131, 131, 0.68);
}

/**
* Transceiver settings
 */
.TRANSCEIVER_common {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.TRANSCEIVER_QSFP28-LR4,
.TRANSCEIVER_QSFP28-LR4-100G,
.TRANSCEIVER_QSFP28-LR4-D,
.TRANSCEIVER_QSFP28-SR4-M,
.TRANSCEIVER_QSFP28-CWDM4,
.TRANSCEIVER_QSFPDD,
.TRANSCEIVER_OP100GQ-CWDM4-02,
.TRANSCEIVER_OP100GQ-LR4-10,
.TRANSCEIVER_OP100UQ-LR4-10,
.TRANSCEIVER_QSFP28-CWDM4-100G,
.TRANSCEIVER_QSFP28-100G {
    height: 100%;
    width: 100%;
    background-image: url("img_fs/port/port_o_2.png");
}

.TRANSCEIVER_ACCELINK-OLST-CFP2-DCO,
.TRANSCEIVER_CFP2-DCO-L-400G,
.TRANSCEIVER_CFP2-DCO-L-200G,
.TRANSCEIVER_OP400UC-D-CO,
.TRANSCEIVER_OP200UC-D-CO,
.TRANSCEIVER_CFP2-DCO-400G-D,
.TRANSCEIVER_CFP2-DCO-400G,
.TRANSCEIVER_CFP2-DCO-200G-D,
.TRANSCEIVER_CFP2-DCO-200G {
    height: 100%;
    width: 100%;
    background-image: url("img_fs/port/cfp.png");
}

.TRANSCEIVER_SFP\+10G,
.TRANSCEIVER_SFP,
.TRANSCEIVER_XGXP-1396-10D,
.TRANSCEIVER_SFPP-S,
.TRANSCEIVER_SFPP-D,
.TRANSCEIVER_SFP28,
.TRANSCEIVER_OPGS-1310-10 {
    background-image: url("img_fs/port/port_o_2.png");
}


/**
alarm
 */
.alarm_CRITICAL {
    box-shadow: inset 10px 10px 1000px 1000px rgba(255, 0, 0, 0.4);
}

.alarm_MAJOR {
    box-shadow: inset 10px 10px 1000px 1000px rgba(255, 165, 0, 0.4);
}

.alarm_MINOR {
    box-shadow: inset 10px 10px 1000px 1000px rgba(255, 255, 0, 0.4);
}

.alarm_WARNING {
    box-shadow: inset 10px 10px 1000px 1000px rgba(0, 0, 255, .3);
}

.led_COMMON {
    box-shadow: none !important;
    width: 7px;
    height: 7px;
    border-radius: 4px;
    position: absolute;
    background-color: #2bbf2b;
}

.led_CRITICAL {
    background-color: red;
}

.led_MAJOR {
    background-color: red;
    animation: led-flashing 3s infinite;
}

.led_MINOR {
    background-color: orange;
}

.led_WARNING {
    background-color: orange;
    animation: led-flashing 3s infinite;
}

.led_DISABLE {
    background-color: gray;
    animation: led-flashing 2s infinite;
}

.led_ACTIVE {
    background-color: #2bbf2b;
    animation: led-flashing 2s infinite;
}

@keyframes led-flashing {
    10% {
        opacity: 0;
    }
    90% {
        opacity: 1;
    }
}

.viewTypeTools {
  :global {
      .ant-radio-button-wrapper{
          border-radius: 0px;
      }
      .ant-radio-button-wrapper-checked {
          color: var(--primary-color) !important;
          background: #E7F9F8;
          border: 1px solid var(--primary-color) !important;
      }

      .ant-radio-button-wrapper {
          width: 100px;
          font-weight: 400;
          font-size: 14px;
          color: #B3BBC8;
          border: 1px solid #DCDCDC;
      }
      .ant-radio-button-wrapper:first-child{
          border-radius: 2px 0px 0px 2px;
      }
      .ant-radio-button-wrapper:nth-child(2) {
          margin-left:-1px;
      }
      .ant-radio-button-wrapper:last-child{
          margin-left:-1px;
          border-radius: 0px 2px 2px 0px;
      }

      .ant-radio-button-wrapper:not(.ant-radio-button-wrapper-checked):hover {
          color: var(--primary-color) !important;
          //background: #E7F9F8;
          //border: 1px solid var(--primary-color) !important;
      }
  }
}