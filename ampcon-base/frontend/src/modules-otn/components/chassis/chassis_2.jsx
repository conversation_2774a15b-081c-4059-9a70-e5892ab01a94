import {useEffect, useRef, useState} from "react";
import {useSelector} from "react-redux";
import {debounce} from "lodash";
import {Button, Dropdown, Radio} from "antd";
import {NEGet, NESet, objectGet, syncNE} from "@/modules-otn/apis/api";
import Icon from "@ant-design/icons";
import {offLineSvg} from "@/modules-otn/utils/iconSvg";
import styles_common from "@/modules-otn/components/chassis/chassis.module.scss";
import styles_fs from "@/modules-otn/components/chassis/chassis_5_fs.module.scss";
import {smallModal} from "@/modules-otn/components/modal/custom_modal";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {rebootIcon, resetIcon} from "@/modules-otn/components/chassis/chassis_icons";
import styles from "./chassis_2.module.scss";

const Chassis2 = ({data}) => {
    const {neNameMap} = useSelector(state => state.neName);
    const {selectedItem} = useSelector(state => state.map);
    const containerRef = useRef();
    const chassisWrapRef = useRef();
    const chassisRef = useRef();
    const {labelList} = useSelector(state => state.languageOTN);
    const [syncState, setSyncState] = useState({});
    const {alarms, upgrade} = useSelector(state => state.notification);
    const [alarmsCount, setAlarmsCount] = useState({});
    const [offLine, setOffLine] = useState(false);
    const [showType, setShowType] = useState(0);
    const [neLabel, setNeLabel] = useState("PDRA5014");
    const [menuItems, setMenuItems] = useState([]);
    const readyOnlyRight = useUserRight();
    const [openDropdown, setOpenDropdown] = useState(false);

    const ledConfig = {
        DC: 37,
        PUMP: 53,
        ALARM: 68,
        Channel: 83
    };

    const getMenu = (key, label, icon, onClick, disabled) => ({key, label, icon, onClick, disabled});

    const handleMenu = () => {
        const ne_id = selectedItem?.value?.ne_id;
        const items = [
            getMenu(
                "commonReset",
                "Reboot",
                <Icon component={rebootIcon} />,
                () => {
                    setOpenDropdown(false);
                    const modal = smallModal({
                        title: labelList.warning,
                        content: labelList.reboot_warning_text,
                        // eslint-disable-next-line no-unused-vars
                        onOk: _ => {
                            NESet({
                                ne_id,
                                parameter: {
                                    commonAdminGroup: {
                                        commonReset: 1
                                    }
                                },
                                success: () => {
                                    modal.destroy();
                                }
                            }).then();
                        }
                    });
                },
                readyOnlyRight?.disabled
            ),
            getMenu(
                "reset",
                "Reset To Factory",
                <div>
                    <Icon component={resetIcon} />
                </div>,
                () => {
                    setOpenDropdown(false);
                    const modal = smallModal({
                        title: labelList.warning,
                        content: labelList.reset_to_factory,
                        // eslint-disable-next-line no-unused-vars
                        onOk: _ => {
                            NESet({
                                ne_id,
                                parameter: {
                                    ramanModuleStatusGroup: {
                                        ramanModuleResetToFactory: 1
                                    }
                                },
                                success: () => {
                                    modal.destroy();
                                }
                            }).then();
                        }
                    });
                },
                readyOnlyRight?.disabled
            )
        ];
        setMenuItems(items);
    };

    const handleModuleClick = e => {
        e.stopPropagation();
        if (e.button === 0) {
            setOpenDropdown(false);
        }
    };

    const callSyncNE = async () => {
        const _id = selectedItem?.value?.ne_id;
        const newState = {...syncState};
        newState[_id] = true;
        setSyncState(newState);
        await syncNE({
            ne_id: _id,
            success: () => {
                const newState = {...syncState};
                delete newState[_id];
                setSyncState(newState);
            },
            fail: () => {
                const newState = {...syncState};
                delete newState[_id];
                setSyncState(newState);
            }
        }).then();
    };

    useEffect(() => {
        const observer = new ResizeObserver(
            debounce(() => {
                if (chassisWrapRef?.current?.clientWidth && chassisRef.current)
                    chassisRef.current.style = `zoom: ${chassisWrapRef.current.clientWidth / 1264}`;
            }, 5)
        );
        observer.observe(containerRef.current);

        return () => {
            observer.disconnect();
        };
    }, []);

    useEffect(() => {
        objectGet("config:ne", {ne_id: selectedItem?.value?.ne_id}).then(rs => {
            setOffLine(rs.documents?.[0]?.value?.runState === 0);
        });
        NEGet({
            ne_id: selectedItem?.value?.ne_id,
            parameter: {commonAdminGroup: {}},
            msg: false
        }).then(rs => {
            setNeLabel(rs?.commonAdminGroup?.commonNEModelNumber ?? "");
        });
    }, [selectedItem]);

    useEffect(() => {
        const alarmCount = alarms
            .filter(i => i.ne_id === selectedItem?.value?.ne_id)
            .reduce((pre, cur) => {
                pre[cur["type-id"]] = pre[cur["type-id"]] ? pre[cur["type-id"]]++ : 1;
                return pre;
            }, {});
        alarmCount.ALARM = alarms.filter(
            i => i.ne_id === selectedItem?.value?.ne_id && ["Module", "PUMP"].includes(i["type-id"])
        ).length;
        setAlarmsCount(alarmCount);
    }, [selectedItem, alarms]);

    useEffect(() => {
        if (upgrade.source === selectedItem?.value?.ne_id) {
            setOffLine(upgrade.data === 0);
        }
    }, [upgrade]);

    const buttonsConfig = [
        {
            label: labelList.front,
            value: 0
        },
        {
            label: labelList.rear,
            value: 1
        }
    ];

    return (
        <div
            style={{
                border: "1px solid #ddd",
                padding: 10,
                marginRight: 24,
                marginLeft: 24,
                borderRadius: 5
            }}
        >
            <div ref={containerRef} style={{padding: "24px 23px", overflow: "hidden"}}>
                <div className={styles_common.head_div} style={{width: "100%"}}>
                    <div>
                        <Radio.Group
                            value={showType}
                            className={styles_fs.viewTypeTools}
                            onChange={e => {
                                setShowType(e.target.value);
                            }}
                        >
                            {buttonsConfig.map(item => (
                                <Radio.Button value={item.value}>{item.label}</Radio.Button>
                            ))}
                        </Radio.Group>
                    </div>
                    <Button
                        disabled={syncState[selectedItem?.value?.ne_id]}
                        loading={syncState[selectedItem?.value?.ne_id]}
                        onClick={callSyncNE}
                    >
                        {labelList.sync}
                    </Button>
                </div>
                <div
                    onMouseLeave={() => {
                        setOpenDropdown(false);
                    }}
                >
                    <Dropdown
                        menu={{items: menuItems}}
                        trigger={["contextMenu"]}
                        open={openDropdown}
                        overlayStyle={{}}
                        onContextMenu={e => {
                            e.preventDefault();
                            if (e.button === 2) setOpenDropdown(true);
                        }}
                    >
                        <div
                            ref={chassisWrapRef}
                            className={styles_common.wrap}
                            onContextMenu={e => handleMenu(e)}
                            onClick={handleModuleClick}
                        >
                            <div
                                ref={chassisRef}
                                className={`${styles.device} ${showType === 0 ? styles.front : styles.rear}`}
                            >
                                {showType === 0 && <div className={styles.border_label}>{neLabel}</div>}
                                {showType === 0 && !offLine && (
                                    <div className={`${styles.led_label} ${styles.led_label_1}`}>{neLabel}</div>
                                )}
                                {showType === 0 && !offLine && (
                                    <div className={`${styles.led_label} ${styles.led_label_2}`}>RAMAN</div>
                                )}
                                {showType === 0 &&
                                    Object.entries(ledConfig).map(([key, top]) => {
                                        let ledColor;
                                        if (offLine) {
                                            ledColor = styles.led_OFFLINE;
                                        } else {
                                            ledColor = alarmsCount[key] ? styles.led_CRITICAL : styles.led_NORMAL;
                                        }
                                        return <div className={`${styles.led_COMMON} ${ledColor}`} style={{top}} />;
                                    })}
                                {showType !== 0 &&
                                    Array.isArray(data?.neData?.state?.ramanDCPowerEntry) &&
                                    data.neData.state.ramanDCPowerEntry.map(ramanDCPowerEntryItem => {
                                        const {instance, ramanDCPowerType, ramanDCPowerVoltage} = ramanDCPowerEntryItem;
                                        const powerCardClassName = [];
                                        powerCardClassName.push(
                                            instance === "1" ? styles.power_left : styles.power_right
                                        );
                                        if (ramanDCPowerType === "1") powerCardClassName.push(styles.power_ac220v);
                                        if (ramanDCPowerType === "2") powerCardClassName.push(styles.power_dc48v);
                                        const powerClassName = [
                                            styles.power,
                                            parseInt(ramanDCPowerVoltage) ? styles.power_on : styles.power_off
                                        ];

                                        return (
                                            <div className={powerCardClassName.join(" ")}>
                                                <div className={powerClassName.join(" ")} />
                                            </div>
                                        );
                                    })}
                            </div>
                        </div>
                    </Dropdown>
                </div>
                <div
                    style={{
                        marginTop: 24,
                        width: "100%",
                        textAlign: "center",
                        fontSize: 14
                    }}
                >
                    {neNameMap[selectedItem?.value?.ne_id]} ({selectedItem?.value?.ne_id}){" "}
                    {offLine && <Icon component={offLineSvg} style={{marginRight: 4}} />}
                </div>
            </div>
        </div>
    );
};

export default Chassis2;
