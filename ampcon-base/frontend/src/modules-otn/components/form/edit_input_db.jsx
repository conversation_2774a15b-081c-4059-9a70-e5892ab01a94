import {Cascader, Checkbox, Input, InputNumber, Radio, Select, Switch} from "antd";
import {useState} from "react";
import TextArea from "antd/es/input/TextArea";
import {Option} from "antd/es/mentions";
import {objectGet} from "@/modules-otn/apis/api";
import {convertToArray} from "@/modules-otn/utils/util";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import CustomSearchSelect from "@/modules-otn/components/select/custom_search_select";

const CheckboxGroup = Checkbox.Group;

const EditInputDB = ({config, formType, allData, setAllData, resetDefaultValue, initData, form, formKey}) => {
    const [data, setData] = useState({});
    const [selectStatus, setSelectStatus] = useState();
    const {
        unit,
        inputType,
        edit,
        dataIndex,
        rows,
        pattern,
        selectLimit,
        resetAffect,
        showCheckedStrategy,
        range,
        multiple,
        step,
        parserFun
    } = config;
    let {placeholder, mode} = config;
    let regExpTool;
    if (selectLimit) {
        const numberLimit = selectLimit(allData, form?.getFieldsValue());
        if (numberLimit !== 1) {
            mode = "multiple";
            placeholder = gLabelList.select_limit_items.format(convertToArray(numberLimit).join("~"));
        }
    }
    if (mode === "tags" && pattern) {
        regExpTool = new RegExp(pattern);
    }

    const processResetAffect = _data => {
        if (resetAffect) {
            if (resetAffect instanceof Array) {
                form.resetFields(resetAffect);
                resetAffect.forEach(i => {
                    delete _data[i];
                });
            } else {
                Object.entries(_data).map(([k]) => {
                    if (!resetAffect?.exclude?.includes(k)) {
                        form.resetFields([k]);
                        delete _data[k];
                    }
                });
            }
        }
    };

    const onClear = () => {
        if (form) {
            form.resetFields([formKey ?? dataIndex]);
        }
        const _data = {...allData};
        // processResetAffect(_data);
        if (setAllData) {
            delete _data[formKey ?? dataIndex];
            setAllData(_data);
        }
    };

    const onChange = v => {
        if (regExpTool) {
            let regExpPass = true;
            convertToArray(v).map(item => {
                if (!regExpTool.test(item)) {
                    regExpPass = false;
                }
            });
            if (!regExpPass) {
                setSelectStatus("error");
            } else {
                setSelectStatus(null);
            }
        }
        let newValue = {...allData};
        processResetAffect(newValue);
        newValue[formKey ?? dataIndex] = v;
        if (config?.data?.effect) {
            if (resetDefaultValue) {
                resetDefaultValue(
                    config.data.effect.reduce((p, c) => {
                        p[c] = null;
                        return p;
                    }, {})
                );
            }
        }
        if (config?.covertDefault) {
            config.covertDefault(newValue, form?.getFieldsValue(), data).then(rs => {
                if (!rs) {
                    return;
                }
                if (resetDefaultValue) {
                    resetDefaultValue(rs);
                }
                if (setAllData) {
                    newValue = {...newValue, ...rs};
                    setAllData(newValue);
                }
                if (form) {
                    Object.entries(rs).map(([_k, _v]) => {
                        form.setFieldValue(_k, _v);
                    });
                }
            });
        } else if (setAllData) {
            setAllData(newValue);
        }
    };

    const getValue = (itemValue, keys, titleKeys, inputKeys, labelKey) => {
        const _keys = keys.split(".");
        let _value = itemValue.value;
        _keys.forEach((item, index) => {
            if (_value instanceof Array) {
                const temp = [];
                _value.forEach(i => {
                    if (titleKeys && index === _keys.length - 1) {
                        const tt = [];
                        titleKeys.forEach(t => {
                            tt.push(i[t]);
                        });
                        temp.push({
                            label: labelKey ? _value[labelKey] : i[item],
                            value: inputKeys === "dbKey" ? itemValue.id : i[inputKeys],
                            title: tt.join("  ")
                        });
                    } else {
                        temp.push(i[item]);
                    }
                });
                _value = temp;
            } else if (titleKeys && index === _keys.length - 1) {
                const tt = [];
                titleKeys.forEach(t => {
                    tt.push(_value[t]);
                });
                _value = {
                    // eslint-disable-next-line no-nested-ternary
                    label: labelKey ? _value[labelKey] : _value[item] ? _value[item] : _value.name,
                    value: inputKeys === "dbKey" ? itemValue.id : _value[inputKeys],
                    title: tt.join("  ")
                };
            } else if (labelKey) {
                _value = {label: _value[labelKey], value: _value[item], title: _value[item]};
            } else {
                _value = _value[item];
            }
        });
        return _value;
    };

    const checkValues = (value, keys, filterValues) => {
        const _keys = keys.split(".");
        let _value = value;
        for (let i = 0; i < _keys.length; i++) {
            if (!_value?.[_keys[i]]) {
                return false;
            }
            _value = _value[_keys[i]];
        }
        if (filterValues instanceof Array) {
            return filterValues.includes(_value);
        }
        if (filterValues.startsWith("$.")) {
            return _value === allData[filterValues.substring(2)];
        }
        return _value === filterValues;
    };

    const getOptionsCascader = (data, dataIndex) => {
        if (!data[formKey ?? dataIndex] && initData) {
            if (config.initOptions) {
                return config.initOptions(initData);
            }
            return initData.options.map(i => {
                return typeof i === "string"
                    ? {
                          value: i,
                          label: i,
                          title: i
                      }
                    : i;
            });
        }
        return data?.[formKey ?? dataIndex];
    };

    const getOptions = (data, dataIndex) => {
        if (!data[formKey ?? dataIndex] && initData?.options) {
            return initData.options.map(i => {
                return typeof i === "string"
                    ? {
                          value: i,
                          label: i,
                          title: i
                      }
                    : i;
            });
        }
        return (
            data?.[formKey ?? dataIndex]?.map(enumKey => ({
                value: enumKey.value || enumKey,
                label: enumKey.label || enumKey,
                title: enumKey.title || enumKey.value || enumKey
            })) ?? [{value: "", label: gLabelList.no_data, disabled: true}]
        );
    };

    let _disabled = false;
    const disabledFun = config?.[formType]?.disabled ?? config?.disabled;
    if (disabledFun) {
        if (typeof disabledFun === "function") {
            _disabled = disabledFun(allData, form?.getFieldsValue());
        } else {
            _disabled = disabledFun;
        }
    }

    if (inputType === "boolean") {
        const options = [
            <Option key="true" value="true">
                true
            </Option>,
            <Option key="false" value="false">
                false
            </Option>
        ];
        return (
            <Select
                key={formKey ?? dataIndex}
                style={{width: 280}}
                onChange={v => {
                    onChange(v);
                }}
            >
                {options}
            </Select>
        );
    }

    if (inputType === "switch") {
        return (
            <Switch
                key={formKey ?? dataIndex}
                checked={allData?.[formKey ?? dataIndex] ?? initData?.value}
                onChange={v => {
                    onChange(v);
                }}
            />
        );
    }

    if (inputType === "number") {
        const rangeArray = range?.split("..");
        return (
            <InputNumber
                style={{width: 280}}
                key={formKey ?? dataIndex}
                min={rangeArray?.[0]}
                max={rangeArray?.[1]}
                step={step}
                addonAfter={unit}
                parser={v => {
                    if (parserFun) {
                        return parserFun(v);
                    }
                    return v;
                }}
                onChange={v => {
                    onChange(v);
                }}
            />
        );
    }

    if (inputType === "cascader") {
        return (
            <Cascader
                style={{width: 280}}
                key={formKey ?? dataIndex}
                multiple={multiple}
                showCheckedStrategy={showCheckedStrategy}
                options={getOptionsCascader(data, formKey ?? dataIndex)}
                onClear={onClear}
                onChange={v => {
                    let newValue = {...allData};
                    processResetAffect(newValue);
                    if (!v) {
                        if (setAllData) {
                            setAllData(newValue);
                        }
                        return;
                    }
                    if (!multiple && showCheckedStrategy === "SHOW_CHILD") {
                        if (v.length > 1) {
                            v = v[v.length - 1];
                        }
                    }
                    newValue = {...newValue, [formKey ?? dataIndex]: v};
                    if (form) {
                        form.setFieldValue(formKey ?? dataIndex, v);
                    }
                    if (config?.covertDefault) {
                        config.covertDefault(newValue, form?.getFieldsValue(), data).then(rs => {
                            if (!rs) {
                                return;
                            }
                            if (resetDefaultValue) {
                                resetDefaultValue(rs);
                            }
                            newValue = {...newValue, ...rs};
                            if (form) {
                                Object.entries(rs).map(([_k, _v]) => {
                                    form.setFieldValue(_k, _v);
                                });
                            }
                            if (setAllData) {
                                setAllData(newValue);
                            }
                        });
                    } else if (setAllData) {
                        setAllData(newValue);
                    }
                }}
                onDropdownVisibleChange={async open => {
                    if (open) {
                        if (typeof config.data === "function") {
                            const rs = await config.data(allData, form?.getFieldsValue());
                            setData({...data, [formKey ?? dataIndex]: rs});
                        }
                    }
                }}
            />
        );
    }

    if (inputType === "password") {
        return (
            <Input.Password
                style={{width: 280}}
                placeholder={gLabelList.pls_input_pwd}
                disabled={formType === "edit" ? edit?.disabled : _disabled}
            />
        );
    }
    if (inputType === "select") {
        return (
            <CustomSearchSelect
                style={{width: 280}}
                mode={mode}
                status={selectStatus}
                allowClear
                placeholder={gLabelList[placeholder] ?? placeholder}
                onChange={onChange}
                onClear={onClear}
                disabled={_disabled}
                onDropdownVisibleChange={async open => {
                    if (!config.data) {
                        return;
                    }
                    if (open) {
                        if (typeof config.data === "function") {
                            const r = await config.data(allData, form?.getFieldsValue());
                            if (!r) {
                                return;
                            }
                            const newData = {...data};
                            newData[formKey ?? dataIndex] = r.map(item => {
                                if (typeof item === "string") {
                                    return {label: item, value: item, title: item};
                                }
                                return item;
                            });
                            setData(newData);
                            return;
                        }
                        if (config.data.options) {
                            const newData = {...data};
                            newData[formKey ?? dataIndex] = config.data.options.map(item => {
                                if (typeof item === "string") {
                                    return {
                                        label: item,
                                        value: item,
                                        title: item
                                    };
                                }
                                return item;
                            });
                            setData(newData);
                            return;
                        }
                        const filter = {};
                        if (config.data.filter) {
                            for (let j = 0; j < config.data.filter.length; j++) {
                                const i = config.data.filter[j];
                                if (
                                    i.filterIndex &&
                                    allData[i.filterIndex] &&
                                    allData[i.filterIndex].toString().trim() !== ""
                                ) {
                                    filter[i.dataIndex] = allData[i.filterIndex];
                                } else if (i.key) {
                                    filter[i.key] = i.value;
                                } else if (i.required === false) {
                                    // 非必要条件，全出来。
                                } else {
                                    return; // 如果过滤条件不选，不出值
                                }
                            }
                        }
                        await objectGet(config.data.type, filter).then(rs => {
                            let r = [];
                            rs.documents.forEach(item => {
                                let exclude = false;
                                if (config.data.exclude) {
                                    for (let i = 0; i < config.data.exclude.length; i++) {
                                        const temp = config.data.exclude[i];
                                        if (checkValues(item.value, temp.key, temp.value)) {
                                            exclude = true;
                                            break;
                                        }
                                    }
                                }
                                if (!exclude) {
                                    const t = getValue(
                                        item,
                                        config.data.valueKey,
                                        config.data.titleKeys,
                                        config.data.inputKey,
                                        config.data.labelKey
                                    );
                                    r = r.concat(convertToArray(t));
                                }
                            });
                            if (config.data.additionValue) {
                                r = r.concat(config.data.additionValue);
                            }
                            const newData = {...data};
                            newData[formKey ?? dataIndex] = r;
                            setData(newData);
                        });
                    }
                }}
                key={formKey ?? dataIndex}
                options={getOptions(data, formKey ?? dataIndex)}
            />
        );
    }
    if (inputType === "textarea") {
        return (
            <TextArea
                style={{width: 280}}
                rows={rows}
                disabled={_disabled}
                placeholder={gLabelList[placeholder] ?? placeholder}
                onChange={v => {
                    onChange(v.target.value);
                }}
            />
        );
    }
    if (inputType === "checkbox") {
        return (
            <Checkbox.Group>
                <Checkbox
                    disabled={_disabled}
                    value
                    onChange={v => {
                        setData(v.target.checked);
                        onChange(v.target.checked);
                    }}
                />
            </Checkbox.Group>
        );
    }
    if (inputType === "checkboxgroup") {
        return (
            <CheckboxGroup
                disabled={_disabled}
                options={config?.options}
                value
                onChange={v => {
                    setData(v);
                    onChange(v);
                }}
            />
        );
    }
    if (inputType === "radio") {
        return (
            <Radio.Group onChange={onChange} value>
                {config.data.options.map(i => (
                    <Radio value={i.value}>{i.title}</Radio>
                ))}
            </Radio.Group>
        );
    }
    return (
        <Input
            style={{width: 280}}
            placeholder={gLabelList[placeholder] ?? placeholder}
            addonAfter={gLabelList[unit] ?? unit}
            disabled={_disabled}
            onChange={v => {
                onChange(v.target.value);
            }}
        />
    );
};

export default EditInputDB;
