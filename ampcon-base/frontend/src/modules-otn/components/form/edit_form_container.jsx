import {Form} from "antd";
import {useEffect, useState} from "react";
import {LoadingOutlined} from "@ant-design/icons";
import {apiEditRpc, apiGetYang, netconfGetByXMLWithXPath} from "@/modules-otn/apis/api";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {getText, removeNSForObj} from "@/modules-otn/utils/util";
import styles from "@/modules-otn/components/form/edit_form.module.scss";
import DisableInput from "./disable_input";
import EditInput from "./edit_input";
import {middleModal} from "../modal/custom_modal";

const EditForm = ({
    setForm,
    rootPath,
    afterFinish,
    yangSystem,
    labelCol,
    wrapperCol,
    title,
    refresh,
    ne_id,
    containerName,
    type,
    disabledKeys = []
}) => {
    const [originData, setOriginData] = useState({});
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm();
    setForm?.(form);

    function fetchData() {
        setLoading(true);
        const _path = [containerName, ...rootPath];
        const _data = {};
        for (let i = 0; i < _path.length; i++) {
            let subObj = _data;
            for (let j = 0; j <= i; j++) {
                if (!subObj[_path[j]]) {
                    subObj[_path[j]] = {};
                }
                subObj = subObj[_path[j]];
            }
        }
        netconfGetByXMLWithXPath({
            ne_id,
            msg: true,
            xml: _data,
            type
        })
            .then(rs => {
                let _data = removeNSForObj(rs);
                _path.map(k => {
                    _data = _data[k];
                });
                if (type === "5") {
                    _data = _data.state;
                }
                setOriginData(_data);
                form.setFieldsValue(_data);
                setLoading(false);
            })
            .catch(e => {
                // eslint-disable-next-line no-console
                console.log(e.message);
                afterFinish?.(true);
            });
    }

    useEffect(() => {
        fetchData();
    }, []);
    return !loading ? (
        <>
            <div className={styles.edit_form_header}>
                <span className={styles.edit_form_header_title}>{title}</span>
                <div className={styles.edit_form_header_operation}>
                    {refresh ? (
                        <a key="refresh" onClick={fetchData}>
                            {gLabelList.refresh}
                        </a>
                    ) : (
                        ""
                    )}
                </div>
            </div>
            <div className={styles.edit_form_content} style={{overflow: "auto"}}>
                <Form
                    labelCol={{span: labelCol ?? 10}}
                    wrapperCol={{span: wrapperCol ?? 13}}
                    form={form}
                    className={styles.edit_form}
                    labelAlign="left"
                    onFinish={values => {
                        const configData = {};
                        // eslint-disable-next-line no-restricted-syntax,guard-for-in
                        for (const i in values) {
                            if (type === "5" && yangSystem?.config?.[i] && originData[i] !== values[i]) {
                                configData[i] = values[i];
                            }
                        }
                        const _path = [containerName, ...rootPath];
                        if (type === "5") {
                            _path.push("config");
                        }
                        const _data = {};
                        for (let i = 0; i < _path.length; i++) {
                            let subObj = _data;
                            for (let j = 0; j <= i; j++) {
                                if (!subObj[_path[j]]) {
                                    subObj[_path[j]] = j === _path.length - 1 ? configData : {};
                                }
                                subObj = subObj[_path[j]];
                            }
                        }
                        apiEditRpc({
                            ne_id,
                            params: _data,
                            success: () => {
                                afterFinish?.(true);
                            },
                            fail: () => {
                                afterFinish?.(false);
                            }
                        }).then();
                    }}
                >
                    {type === "5" &&
                        Object.entries(yangSystem.state).map(v => {
                            if (v[0] !== "definition" && v[1].type !== "container" && v[1].type !== "list") {
                                return (
                                    <Form.Item key={v[0]} label={getText(v[0])} name={v[0]}>
                                        {yangSystem?.config?.[v[0]] &&
                                        yangSystem[v[0]]?.config === undefined &&
                                        !disabledKeys?.includes(v[0]) ? (
                                            EditInput({config: v[1]})
                                        ) : (
                                            <DisableInput config={v[1]} />
                                        )}
                                    </Form.Item>
                                );
                            }
                        })}
                </Form>
            </div>
        </>
    ) : (
        <div style={{width: "100%", textAlign: "center"}}>
            <a>
                <LoadingOutlined style={{fontSize: 32, fill: "#14C9BB", color: "#14C9BB"}} />
            </a>
        </div>
    );
};

const openContainerEdit = (containerName, rootPath, type, db_key, title, ne_id, initData, onUpdate, disabledKeys) => {
    apiGetYang(type).then(yang => {
        let form;
        // eslint-disable-next-line no-return-assign
        const handle = f => (form = f);
        let modal;
        const afterFinish = success => {
            if (success) {
                modal.destroy();
                onUpdate?.();
            } else {
                modal.update({okButtonProps: {loading: false}});
            }
        };
        modal = middleModal({
            title,
            okText: gLabelList.ok,
            cancelText: gLabelList.cancel,
            // eslint-disable-next-line no-unused-vars
            onOk: _ => {
                modal.update({okButtonProps: {loading: true}});
                form.submit();
            },
            content: (
                <EditForm
                    setForm={handle}
                    afterFinish={afterFinish}
                    yangSystem={yang?.[containerName]}
                    db_key={db_key}
                    ne_id={ne_id}
                    initData={initData}
                    rootPath={rootPath ?? []}
                    containerName={containerName}
                    type={type}
                    disabledKeys={disabledKeys}
                />
            )
        });
    });
};

export {openContainerEdit};
