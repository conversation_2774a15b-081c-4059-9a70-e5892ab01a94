import {forwardRef} from "react";
import {Form, Col, Row} from "antd";
import chunk from "lodash/chunk";
import styles from "@/modules-otn/components/form/edit_form.module.scss";

/**
 * 表单组件简化渲染，并支持了布局显示
 * @param {Array} items 表单项，格式为[{render: <Input />, name: "username", label: "用户名"}, ...], render定义了表单项的渲染，其他属性会传递给Form.Item
 */
const ModalForm = forwardRef((props, ref) => {
    // console.log("props =", props);
    const {items, layout, ...formProps} = props;
    const {isRow, rowSize = 4, rowProps, colProps} = layout ?? {};
    const [form] = Form.useForm();

    if (isRow) {
        return (
            <Form {...formProps} ref={ref} form={form} labelAlign="left" className={styles.edit_form}>
                {chunk(items, rowSize).map((rowItems, rowIndex) => {
                    return (
                        // eslint-disable-next-line react/no-array-index-key
                        <Row key={rowIndex} {...rowProps}>
                            {rowItems.map((item, itemIndex) => {
                                const {render, ...otherProps} = item;
                                return (
                                    // eslint-disable-next-line react/no-array-index-key
                                    <Col key={itemIndex} span={24 / rowSize} {...colProps}>
                                        <Form.Item {...otherProps}>{render}</Form.Item>
                                    </Col>
                                );
                            })}
                        </Row>
                    );
                })}
            </Form>
        );
    }

    return (
        <Form {...formProps} ref={ref} form={form}>
            {items.map((item, index) => {
                const {render, ...otherProps} = item;
                return (
                    // eslint-disable-next-line react/no-array-index-key
                    <Form.Item key={index} {...otherProps}>
                        {render}
                    </Form.Item>
                );
            })}
        </Form>
    );
});

export default ModalForm;
