import {Divider} from "antd";
import {useEffect, useState} from "react";
import {apiGetCategory, apiGetYang, netconfGetByXML} from "@/modules-otn/apis/api";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {useSize} from "ahooks";
import {addNS, convertToArray, getText, getValueByJPath, removeNS1, removeNSForObj} from "@/modules-otn/utils/util";
import {CustomTable} from "@/modules-otn/pages/otn/common/custom_table";
import {openModalEdit} from "./edit_form";
import {openModalCreate} from "./create_form";
import styles from "./edit_table.module.scss";
import {bigModal} from "../modal/custom_modal";

/**
 *
 * @param categoryName defined in "src/config/category.js"
 * @param keys string | string[] Object keys
 * @param operation string[] array includes: "create", "refresh"
 * @param rowOperation string[] array includes: "delete", "edit", etc...
 * @returns {JSX.Element}
 * @constructor
 */
const EditCustomTable = ({
    categoryName,
    category,
    Yang,
    createEnable = false,
    refresh,
    title,
    ne_id,
    tags = [],
    keys = [],
    type,
    readonly,
    onCreate,
    language,
    initColumns,
    formatData,
    loadDataFun
}) => {
    const {rootPath, tabs} = structuredClone(category);
    const [tableConfig, setTableConfig] = useState({});
    const [tableColumns, setTableColumns] = useState(null);
    const [loading, setLoading] = useState(false);
    const size = useSize(document.querySelector("body"));

    const updateTableConfig = () => {
        setLoading(true);
        if (loadDataFun) {
            loadDataFun().then(rs => {
                setTableConfig({data: rs, keys: null});
                setLoading(false);
            });
            return;
        }
        const _keys = [...keys];
        const req = {};
        let obj;
        rootPath.reduce(
            // eslint-disable-next-line no-return-assign
            (p, c) => [
                (p[0][c] = Object.fromEntries(
                    p[1][c].definition.key && _keys.length > 0
                        ? p[1][c].definition.key.split(" ").map(k => [k, _keys.shift()])
                        : []
                )),
                (obj = p[1][c])
            ],
            [req, Yang]
        );
        netconfGetByXML({
            ne_id,
            msg: true,
            xml: addNS(req, Yang)
        })
            .then(rs => {
                let dataSource = convertToArray(getValueByJPath(removeNSForObj(rs), rootPath));
                dataSource =
                    dataSource === ""
                        ? []
                        : dataSource.map(item => {
                              const d = removeNS1(
                                  Object.values(tabs)[0].mode === 0 ? (item.state ?? item.config) : item
                              );
                              tags.forEach(tag => {
                                  if (Array.isArray(tag[tag.length - 1])) {
                                      const newTag = [...tag];
                                      const tagKeys = newTag.pop();
                                      const tmp = convertToArray(getValueByJPath(item, newTag));
                                      d[tag[0]] = tmp.map(t => tagKeys.map(k => t[k]).join(":"));
                                  } else {
                                      d[tag[0]] = convertToArray(getValueByJPath(item, tag));
                                  }
                              });
                              return d;
                          });
                if (formatData) {
                    dataSource = formatData(dataSource);
                }
                setTableConfig({data: dataSource, keys: obj.definition.key});
            })
            .catch(e => {
                // eslint-disable-next-line no-console
                console.log(e.message);
            })
            .finally(() => {
                setLoading(false);
            });
    };

    useEffect(() => {
        updateTableConfig();
        const columns = initColumns.map(f => {
            const {dataIndex, title, unit} = f;
            return {
                ...f,
                title:
                    (gLabelList[title] ?? getText(title) ?? gLabelList[dataIndex] ?? getText(dataIndex)) +
                    (unit ? ` (${unit})` : "")
            };
        });
        setTableColumns(columns);
    }, [ne_id, language]);

    return (
        <>
            <div className={styles.edit_table_header}>
                <span className={styles.edit_table_header_title}>{title}</span>
                <div className={styles.edit_table_header_operation}>
                    {createEnable ? (
                        <a
                            key="create"
                            onClick={() => {
                                if (onCreate) {
                                    onCreate(updateTableConfig);
                                    return;
                                }
                                openModalCreate({
                                    categoryName,
                                    type,
                                    title: `${gLabelList.create} ${getText(rootPath[rootPath.length - 1])}`,
                                    keys,
                                    ne_id,
                                    callback: () => {
                                        setTimeout(() => {
                                            updateTableConfig();
                                        }, 2000);
                                    }
                                });
                            }}
                        >
                            {gLabelList.create}
                        </a>
                    ) : null}
                    {createEnable && refresh ? <Divider type="vertical" /> : null}
                    {refresh ? (
                        <a
                            key="refresh"
                            style={{color: loading ? "#b9b9b9" : "", cursor: loading ? "not-allowed" : "pointer"}}
                            onClick={() => {
                                if (!loading) {
                                    updateTableConfig();
                                }
                            }}
                        >
                            {gLabelList.refresh}
                        </a>
                    ) : null}
                </div>
            </div>
            <div className={styles.edit_table_content}>
                {CustomTable({
                    rowKey: tableConfig.keys ?? null,
                    columns: tableColumns,
                    dataSource: tableConfig.data,
                    pagination: false,
                    scroll: {x: 1, y: isNaN(size?.height) ? 0 : size.height - 450},
                    loading,
                    onRow: r => ({
                        onDoubleClick: () => {
                            if (tableConfig.keys) {
                                openModalEdit(
                                    categoryName,
                                    [...keys, ...tableConfig.keys.split(" ").map(k => r[k])],
                                    type,
                                    null,
                                    ne_id,
                                    null,
                                    null,
                                    readonly
                                );
                            }
                        }
                    })
                })}
            </div>
        </>
    );
};

/**
 *
 * @param categoryName
 * @param keys
 * @param type
 * @param title
 * @param config
 * @param ne_id
 * @param db_key  如果有dbKey,下面两个参数就不要
 * @param requestType  没有dbkey，要从索引找值就要设置类型，和redis的字段同名
 * @param filter
 * @param requestCategoryName
 */
const openCustomTable = (
    categoryName,
    keys,
    type,
    title,
    config,
    ne_id,
    db_key,
    requestType,
    filter,
    requestCategoryName,
    readonly
) => {
    if (!categoryName) return;
    apiGetCategory(categoryName, type).then(categoryRs => {
        apiGetYang(type).then(yang => {
            bigModal({
                title,
                okText: gLabelList.ok,
                onOk: () => {
                    config?.callback?.();
                },
                onCancel: () => {
                    config?.callback?.();
                },
                cancelText: " ",
                cancelButtonProps: {style: {display: "none"}},
                content: (
                    <EditCustomTable
                        categoryName={categoryName}
                        rowOperation={config?.rowOperation}
                        createEnable={config?.createEnable}
                        refresh={config?.refresh}
                        initColumns={config?.initColumns}
                        formatData={config?.formatData}
                        loadDataFun={config?.loadData}
                        category={categoryRs}
                        Yang={yang}
                        db_key={db_key}
                        ne_id={ne_id}
                        requestType={requestType}
                        filter={filter}
                        requestCategoryName={requestCategoryName}
                        keys={typeof keys === "string" ? [keys] : keys}
                        type={type}
                        readonly={readonly}
                        showType="Modal"
                    />
                )
            });
        });
    });
};

export {openCustomTable};
