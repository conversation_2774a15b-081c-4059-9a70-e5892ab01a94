import {Col, Form, Row} from "antd";
import React, {useEffect, useState} from "react";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {isEmpty} from "lodash";
import {getText} from "@/modules-otn/utils/util";
import EditInputDB from "@/modules-otn/components/form/edit_input_db";
import styles from "./edit_form.module.scss";
import {bigModal, middleModal} from "../modal/custom_modal";

const EditForm = ({setForm, afterFinish, getData, columns, saveFun, onCancel, columnNum, labelCol, wrapperCol}) => {
    const [form] = Form.useForm();
    setForm?.(form);
    const [originalData, setOriginalData] = useState([]);
    const [datas, setDatas] = useState({});

    const loadData = () => {
        getData().then(rs => {
            setOriginalData(rs);
            setDatas(rs);
            form.setFieldsValue(rs);
        });
    };

    const submit = () => {
        const value = form.getFieldsValue();
        const diffValue = structuredClone(value);
        Object.keys(diffValue).map(k => {
            if (originalData[k] === value[k] || (originalData[k] === undefined && value[k] === "")) {
                delete diffValue[k];
            }
        });
        if (isEmpty(diffValue)) {
            onCancel();
            return;
        }
        saveFun?.(diffValue, value).then(rs => {
            if (rs === true || rs.message === "SUCCESS" || rs.apiResult === 0) {
                afterFinish(true);
            } else {
                afterFinish(false);
            }
        });
    };

    useEffect(loadData, []);

    return (
        <>
            {/* <div style={{paddingBottom: 8, borderBottom: "1px solid #f0f0f0"}} /> */}
            <Form
                style={{overflow: "auto"}}
                labelCol={{span: labelCol ?? 10}}
                wrapperCol={{span: wrapperCol ?? 13}}
                form={form}
                onFinish={submit}
                className={styles.edit_form}
                labelAlign="left"
            >
                {columns.map(i => {
                    return (
                        <Row>
                            {i.map(item => {
                                if (item.type === "split") {
                                    return (
                                        <div style={{width: "100%", paddingTop: 20, paddingBottom: 20}}>
                                            <div
                                                style={{
                                                    width: "100%",
                                                    borderTop: "1px solid #f0f0f0"
                                                }}
                                            />
                                        </div>
                                    );
                                }
                                const rules = [];
                                if (item.required) {
                                    if (typeof item.required === "function") {
                                        if (item.required(datas, form.getFieldsValue())) {
                                            rules.push({required: true, message: gLabelList.required});
                                        }
                                    } else {
                                        rules.push({required: true, message: gLabelList.required});
                                    }
                                }
                                if (item.pattern) {
                                    const p = {pattern: item.pattern};
                                    if (item.message) {
                                        p.message = gLabelList[item.message] ?? item.message;
                                    }
                                    rules.push(p);
                                }
                                return (
                                    <Col
                                        key={`${item.dataIndex}_col`}
                                        style={{
                                            display: "flex",
                                            alignItems: "center"
                                        }}
                                        span={24 / (columnNum ?? columns?.[0]?.length ?? 1)}
                                    >
                                        <Form.Item
                                            style={{width: "100%"}}
                                            key={item.dataIndex}
                                            label={getText(item.title ?? item.dataIndex)}
                                            name={item.dataIndex}
                                            initialValue={datas[item.dataIndex]}
                                            rules={rules}
                                        >
                                            {EditInputDB({
                                                config: item,
                                                allData: datas,
                                                setAllData: setDatas
                                            })}
                                        </Form.Item>
                                    </Col>
                                );
                            })}
                        </Row>
                    );
                })}
            </Form>
        </>
    );
};

const openCustomEditForm = ({
    title,
    columns,
    getData,
    saveFun,
    afterUpdate,
    columnNum,
    labelCol,
    wrapperCol,
    readOnly
}) => {
    let form;
    // eslint-disable-next-line no-return-assign
    const handle = f => (form = f);
    let modal;
    const afterFinish = success => {
        if (success) {
            modal.destroy();
            afterUpdate?.();
        } else {
            modal.update({okButtonProps: {loading: false}});
        }
    };
    const onCancel = () => {
        modal.destroy();
    };
    modal = (columnNum === 1 ? middleModal : bigModal)({
        title,
        okText: gLabelList.ok,
        cancelText: gLabelList.cancel,
        // eslint-disable-next-line no-unused-vars
        onOk: _ => {
            form.validateFields().then(() => {
                modal.update({okButtonProps: {loading: true}});
                form.submit();
            });
        },
        okButtonProps: {
            disabled: readOnly
        },
        content: (
            <EditForm
                setForm={handle}
                afterFinish={afterFinish}
                getData={getData}
                columns={columns}
                saveFun={saveFun}
                onCancel={onCancel}
                columnNum={columnNum}
                labelCol={labelCol}
                wrapperCol={wrapperCol}
            />
        )
    });
};

export default openCustomEditForm;
