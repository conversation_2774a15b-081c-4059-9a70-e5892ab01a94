import {useRef} from "react";
import {But<PERSON>, Space} from "antd";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {rootModal} from "@/modules-otn/utils/util";
import ModalForm from "@/modules-otn/components/form/modal_form";

const useCustomModal = () => {
    const formRef = useRef();
    let editModal;
    return {
        openCustomModal: ({
            items,
            onFinish,
            title = "",
            okText = gLabelList.ok,
            cancelText = gLabelList.cancel,
            initialValues = {},
            closable = true,
            autoDistroyModal = true,
            width = "60%",
            buttons = []
        }) => {
            editModal = rootModal.confirm({
                title,
                icon: null,
                width,
                centered: true,
                okText,
                cancelText,
                footer: (
                    <div style={{display: "flex", justifyContent: "flex-end"}}>
                        <Space>
                            {buttons}
                            <Button
                                onClick={() => {
                                    editModal.destroy();
                                }}
                            >
                                {cancelText}
                            </Button>
                            <Button
                                type="primary"
                                onClick={() => {
                                    formRef.current.validateFields().then(() => {
                                        formRef.current.submit();
                                    });
                                }}
                            >
                                {okText}
                            </Button>
                        </Space>
                    </div>
                ),
                closable,
                // eslint-disable-next-line no-unused-vars
                // onOk: _ => {
                //     formRef.current.validateFields().then(() => {
                //         formRef.current.submit();
                //     });
                // },
                content: (
                    <ModalForm
                        ref={formRef}
                        labelCol={{span: 5}}
                        wrapperCol={{span: 16}}
                        initialValues={initialValues}
                        items={items}
                        onFinish={values => {
                            if (autoDistroyModal) editModal.destroy();
                            onFinish?.(values);
                        }}
                    />
                )
            });
        },
        formRef,
        destroy: () => {
            editModal?.destroy?.();
        }
    };
};

export default useCustomModal;
