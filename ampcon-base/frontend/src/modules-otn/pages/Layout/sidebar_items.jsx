import React from "react";
import {Tooltip} from "antd";
import Icon from "@ant-design/icons";
import {dashBoardSvg, maintainSvg, monitorSvg, resourceSvg, serviceSvg, settingSvg} from "@/utils/common/iconSvg";

const all_items = [
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Dashboard</div>} placement="right">
                Dashboard
            </Tooltip>
        ),
        key: "/dashboard",
        icon: <Icon component={dashBoardSvg} />,
        children: [
            {
                key: "/dashboard/global_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Global View</div>} placement="right">
                        Global View
                    </Tooltip>
                )
            },
            {
                key: "/dashboard/otn_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">OTN View</div>} placement="right">
                        OTN View
                    </Tooltip>
                )
            },
            {
                key: "/dashboard/switch_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch View</div>} placement="right">
                        Switch View
                    </Tooltip>
                )
            },
            {
                key: "/dashboard/telemetry_dashboard",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Telemetry Dashboard</div>} placement="right">
                        Telemetry Dashboard
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Resource</div>} placement="right">
                Resource
            </Tooltip>
        ),
        key: "/resource",
        icon: <Icon component={resourceSvg} />,
        children: [
            {
                key: "/resource/device_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Device View</div>} placement="right">
                        Device View
                    </Tooltip>
                )
            },
            {
                key: "/resource/upgrade_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Upgrade Management</div>} placement="right">
                        Upgrade Management
                    </Tooltip>
                )
            },
            {
                key: "/resource/auth_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Authority Management</div>} placement="right">
                        Authority Management
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/resource/auth_management/device_license_management",
                        label: (
                            <Tooltip
                                title={<div className="fixed-tooltip">Device License Management</div>}
                                placement="right"
                            >
                                Device License Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/resource/auth_management/group_management",
                        label: (
                            <Tooltip title={<div className="fixed-tooltip">Group Management</div>} placement="right">
                                Group Management
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Service</div>} placement="right">
                Service
            </Tooltip>
        ),
        key: "/service",
        icon: <Icon component={serviceSvg} />,
        children: [
            {
                key: "/service/otn",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">OTN</div>} placement="right">
                        OTN
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/service/otn/l0_config",
                        label: (
                            <Tooltip title={<div className="fixed-tooltip">L0 Config</div>} placement="right">
                                L0 Config
                            </Tooltip>
                        )
                    },
                    {
                        key: "/service/otn/l1_config",
                        label: (
                            <Tooltip title={<div className="fixed-tooltip">L1 Config</div>} placement="right">
                                L1 Config
                            </Tooltip>
                        )
                    },
                    {
                        key: "/service/otn/e2e_service_config",
                        label: (
                            <Tooltip title={<div className="fixed-tooltip">E2E Service Config</div>} placement="right">
                                E2E Service Config
                            </Tooltip>
                        )
                    }
                ]
            },
            {
                key: "/service/switch",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch</div>} placement="right">
                        Switch
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/service/switch/switch",
                        label: (
                            <Tooltip title={<div className="fixed-tooltip">Switch</div>} placement="right">
                                Switch
                            </Tooltip>
                        )
                    },
                    {
                        key: "/service/switch/global_configuration",
                        label: (
                            <Tooltip
                                title={<div className="fixed-tooltip">Global Configuration</div>}
                                placement="right"
                            >
                                Global Configuration
                            </Tooltip>
                        )
                    },
                    {
                        key: "/service/switch/switch_configuration",
                        label: (
                            <Tooltip
                                title={<div className="fixed-tooltip">Switch Configuration</div>}
                                placement="right"
                            >
                                Switch Configuration
                            </Tooltip>
                        )
                    },
                    {
                        key: "/service/switch/config_files_view",
                        label: (
                            <Tooltip title={<div className="fixed-tooltip">Config Files View</div>} placement="right">
                                Config Files View
                            </Tooltip>
                        )
                    },
                    {
                        key: "/service/switch/switch_model",
                        label: (
                            <Tooltip title={<div className="fixed-tooltip">Switch Model</div>} placement="right">
                                Switch Model
                            </Tooltip>
                        )
                    },
                    {
                        key: "/service/switch/system_management",
                        label: (
                            <Tooltip title={<div className="fixed-tooltip">System Management</div>} placement="right">
                                System Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/service/switch/config_template",
                        label: (
                            <Tooltip title={<div className="fixed-tooltip">Config Template</div>} placement="right">
                                Config Template
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Monitor</div>} placement="right">
                Monitor
            </Tooltip>
        ),
        key: "/monitor",
        icon: <Icon component={monitorSvg} />,
        children: [
            {
                key: "/monitor/alarm",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Alarm</div>} placement="right">
                        Alarm
                    </Tooltip>
                )
            },
            {
                key: "/monitor/performance",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Performance</div>} placement="right">
                        Performance
                    </Tooltip>
                )
            },
            {
                key: "/monitor/performance_subscription",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Performance Subscription</div>} placement="right">
                        Performance Subscription
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Maintain</div>} placement="right">
                Maintain
            </Tooltip>
        ),
        key: "/maintain",
        icon: <Icon component={maintainSvg} />,
        children: [
            {
                key: "/maintain/transport_config",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Transport Configuration</div>} placement="right">
                        Transport Configuration
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/maintain/transport_config/link_measure",
                        label: (
                            <Tooltip title={<div className="fixed-tooltip">Link Measurement</div>} placement="right">
                                Link Measurement
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/transport_config/database_manager",
                        label: (
                            <Tooltip title={<div className="fixed-tooltip">Database Management</div>} placement="right">
                                Database Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/transport_config/log_manager",
                        label: (
                            <Tooltip title={<div className="fixed-tooltip">Log Management</div>} placement="right">
                                Log Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/transport_config/openconfig_model",
                        label: (
                            <Tooltip title={<div className="fixed-tooltip">OpenConfig Model</div>} placement="right">
                                OpenConfig Model
                            </Tooltip>
                        )
                    }
                ]
            },
            {
                key: "/maintain/network_config",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Network Configuration</div>} placement="right">
                        Network Configuration
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/maintain/network_config/automation",
                        label: (
                            <Tooltip title={<div className="fixed-tooltip">Automation</div>} placement="right">
                                Automation
                            </Tooltip>
                        ),
                        children: [
                            {
                                key: "/maintain/network_config/automation/playbooks",
                                label: (
                                    <Tooltip title={<div className="fixed-tooltip">Playbooks</div>} placement="right">
                                        Playbooks
                                    </Tooltip>
                                )
                            },
                            {
                                key: "/maintain/network_config/automation/other_devices",
                                label: (
                                    <Tooltip
                                        title={<div className="fixed-tooltip">Other Devices</div>}
                                        placement="right"
                                    >
                                        Other Devices
                                    </Tooltip>
                                )
                            },
                            {
                                key: "/maintain/network_config/automation/ansible_jobs_list",
                                label: (
                                    <Tooltip
                                        title={<div className="fixed-tooltip">Ansible Jobs List</div>}
                                        placement="right"
                                    >
                                        Ansible Jobs List
                                    </Tooltip>
                                )
                            },
                            {
                                key: "/maintain/network_config/automation/schedule",
                                label: (
                                    <Tooltip title={<div className="fixed-tooltip">Schedule</div>} placement="right">
                                        Schedule
                                    </Tooltip>
                                )
                            }
                        ]
                    },
                    {
                        key: "/maintain/network_config/system_backup",
                        label: (
                            <Tooltip title={<div className="fixed-tooltip">System Backup</div>} placement="right">
                                System Backup
                            </Tooltip>
                        )
                    }
                ]
            },
            {
                key: "/maintain/cli_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">CLI Configuration</div>} placement="right">
                        CLI Configuration
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">System</div>} placement="right">
                System
            </Tooltip>
        ),
        key: "/system",
        icon: <Icon component={settingSvg} />,
        children: [
            {
                key: "/system/user_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">User Management</div>} placement="right">
                        User Management
                    </Tooltip>
                )
            },
            {
                key: "/system/software_license",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Software License</div>} placement="right">
                        Software License
                    </Tooltip>
                )
            },
            {
                key: "/system/time_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Time Management</div>} placement="right">
                        Time Management
                    </Tooltip>
                )
            }
        ]
    }
];

const excludeItemsByKey = (items, excludeKeys) => {
    const excludeRecursive = list => {
        return list.reduce((acc, item) => {
            if (!excludeKeys.includes(item.key)) {
                if (item.children) {
                    const filteredChildren = excludeRecursive(item.children);
                    acc.push({...item, children: filteredChildren});
                } else {
                    acc.push(item);
                }
            }
            return acc;
        }, []);
    };

    return excludeRecursive(items);
};

const adminExcludeKeys = [
    "/resource/auth_management/group_management",
    "/service/switch/system_config",
    "/system/user_management"
];
const operatorExcludeKeys = [
    "/resource/auth_management/group_management",
    "/service/switch/system_config",
    "/system",
    "/service/switch/system_management"
];
const readonlyExcludeKeys = [
    "/resource/upgrade_management",
    "/resource/auth_management",
    "/maintain/network_config",
    "/service/switch/system_config",
    "/service/switch/global_configuration",
    "/service/switch/switch_configuration",
    "/service/switch/switch_model",
    "/service/switch/system_management",
    "/system"
];

const sidebar_items = {
    superuser: all_items,
    superadmin: excludeItemsByKey(all_items, adminExcludeKeys),
    operator: excludeItemsByKey(all_items, operatorExcludeKeys),
    readonly: excludeItemsByKey(all_items, readonlyExcludeKeys)
};

export default sidebar_items;
