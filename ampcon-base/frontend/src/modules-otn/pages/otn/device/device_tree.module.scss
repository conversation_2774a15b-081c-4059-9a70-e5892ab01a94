.container {
    flex: 1;
    display: flex;
    height: 100%;
    flex-direction: column;
    padding-left: 24px;
    padding-right: 24px;

    :global {
        .ant-tree {
            .ant-tree-node-content-wrapper {
                line-height: 21px;
            }

            .ant-tree-node-selected {
                background-color: rgba(20, 201, 187, 0.1) !important;
                border-radius: 2px;
                -webkit-border-radius: 2px;
                -moz-border-radius: 2px;
                -ms-border-radius: 2px;
                -o-border-radius: 2px;
            }

            .ant-tree-node-selected .ant-tree-title > span {
                color: var(--primary-color) !important;
            }
            .ant-tree-node-content-wrapper .ant-tree-title {
                height: 24px;
                display: inline-flex;
                align-items: center;
            }

            .ant-tree-node-content-wrapper .ant-tree-iconEle {
                display: inline-flex;
                align-items: center;
            }
            .ant-tree-iconEle.ant-tree-icon__docu:empty,
            .ant-tree-iconEle.ant-tree-icon__open:empty,
            .ant-tree-iconEle.ant-tree-icon__close:empty{
                display: none !important;
            }
        }
    }
}

