import {useEffect, useRef} from "react";
import {useDispatch, useSelector} from "react-redux";
import DockLayout, {DividerBox} from "rc-dock";
import {Tag} from "antd";
import {useRequest} from "ahooks";
import {apiConnectionByGroup} from "@/modules-otn/apis/api";
import {
    setConnections,
    setNeInfoListMap,
    setSelectedItem,
    setTableFilter,
    setTreeItemChangedTag,
    setWrapMock
} from "@/store/modules/otn/mapSlice";
import CommonView from "@/modules-otn/pages/otn/device/common_view";
import ChassisContainer from "@/modules-otn/components/chassis/chassis_container";
import SwitchDetail from "@/modules-ampcon/components/switch_detail";
import DCP920 from "@/modules-ampcon/pages/Resource/DeviceDisplay/DCP920/device_display";
import FMT from "@/modules-ampcon/pages/Resource/DeviceDisplay/FMT/device_display";
import DeviceMap from "./device_map";
import DeviceTree from "./device_tree";
import "rc-dock/dist/rc-dock.css";
import styles from "./device.module.scss";

const portConfig = {
    BAIN: "PAOUT",
    BAOUT: "PAIN",
    PAOUT: "BAIN",
    PAIN: "BAOUT",
    LA1IN: "LA2OUT",
    LA2OUT: "LA1IN",
    LA1OUT: "LA2IN",
    LA2IN: "LA1OUT"
};

const Device = () => {
    const onlyOTN = import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-T";
    const {labelList} = useSelector(state => state.languageOTN);
    const {neNameMap} = useSelector(state => state.neName);
    const {selectedItem, treeItemChangedTag} = useSelector(state => state.map);
    const dispatch = useDispatch();

    const loadData = () => {
        runAsync("ne:5:connection", {}).then(res => {
            const {configNeDocuments, connectionInfo} = res;
            try {
                // 网元节点数据map配置
                const cloneConfigNeDocuments = structuredClone(configNeDocuments);
                dispatch(
                    setNeInfoListMap(
                        cloneConfigNeDocuments?.reduce((res, item) => {
                            const {id, value} = item;
                            res[id] = value;
                            return res;
                        }, {})
                    )
                );

                const connectionData = connectionInfo?.map(item => ({
                    ne_id: item.value.ne_id,
                    ...item.value.data.config
                }));
                const flagArr = [];
                const countArr = [];
                let res = [];
                connectionData?.forEach(item => {
                    if (item.source && item.dest) {
                        let sourcePort = "";
                        let destPort = "";
                        const sourceIP =
                            item.source.split("|")[1] === undefined ? item.ne_id : item.source.split("|")[0];
                        const destIP = item.dest.split("|")[1] === undefined ? item.ne_id : item.dest.split("|")[0];
                        const sportname =
                            item.source.split("-", 3).join("-").split("|")[1] ?? item.source.split("-", 3).join("-");
                        const dportname =
                            item.dest.split("-", 3).join("-").split("|")[1] ?? item.dest.split("-", 3).join("-");
                        const sp = item.source.split("-").pop();
                        const dp = item.dest.split("-").pop();
                        const sport_1 = `${sportname}-${sp}`;
                        const sport_2 = `${sportname}-${portConfig[sp] ?? sp}`;
                        const dport_1 = `${dportname}-${dp}`;
                        const dport_2 = `${dportname}-${portConfig[dp] ?? dp}`;
                        const source = `${sourceIP}|${sport_1}`;
                        const dest = `${destIP}|${dport_1}`;
                        countArr.push({...item, connection: `${source}&${dest}`});
                        if (flagArr.includes(source) && flagArr.includes(dest)) {
                            return true;
                        }
                        if (sport_1 === sport_2) {
                            flagArr.push(`${sourceIP}|${sport_1}`);
                            sourcePort = sport_1;
                        } else {
                            flagArr.push(`${sourceIP}|${sport_1}`);
                            flagArr.push(`${sourceIP}|${sport_2}`);
                            sourcePort = `${sport_1}/${sport_2}`;
                        }
                        if (dport_1 === dport_2) {
                            flagArr.push(`${destIP}|${dport_1}`);
                            destPort = dport_1;
                        } else {
                            flagArr.push(`${destIP}|${dport_1}`);
                            flagArr.push(`${destIP}|${dport_2}`);
                            destPort = `${dport_1}/${dport_2}`;
                        }
                        res.push({
                            sourceIP,
                            sourcePort,
                            destIP,
                            destPort
                        });
                    }
                });
                res = res.map(item => {
                    let refArr = [];
                    const resArr = [];
                    let state;
                    if (item.sourceIP === item.destIP) {
                        refArr = [
                            `${item.sourceIP}|${item.sourcePort.split("/")[0]}&${item.destIP}|${
                                item.destPort.split("/")[0]
                            }`,
                            `${item.destIP}|${item.destPort.split("/")[1] ?? item.destPort.split("/")[0]}&${
                                item.sourceIP
                            }|${item.sourcePort.split("/")[1] ?? item.sourcePort.split("/")[0]}`
                        ];
                    } else {
                        refArr = [
                            `${item.sourceIP}|${item.sourcePort.split("/")[0]}&${item.destIP}|${
                                item.destPort.split("/")[0]
                            }`,
                            `${item.sourceIP}|${item.sourcePort.split("/")[0]}&${item.destIP}|${
                                item.destPort.split("/")[0]
                            }`,
                            `${item.destIP}|${item.destPort.split("/")[1] ?? item.destPort.split("/")[0]}&${
                                item.sourceIP
                            }|${item.sourcePort.split("/")[1] ?? item.sourcePort.split("/")[0]}`,
                            `${item.destIP}|${item.destPort.split("/")[1] ?? item.destPort.split("/")[0]}&${
                                item.sourceIP
                            }|${item.sourcePort.split("/")[1] ?? item.sourcePort.split("/")[0]}`
                        ];
                    }
                    countArr.forEach(element => {
                        if (refArr.includes(element.connection)) {
                            resArr.push(element);
                        }
                    });
                    if (resArr.length !== refArr.length) {
                        state = labelList.partial;
                    } else {
                        state = labelList.complete;
                    }
                    return {
                        ...item,
                        state,
                        originValue: resArr,
                        description: resArr.map(item => {
                            let s = item.source;
                            if (s.indexOf("|") > -1) {
                                const temp = s.split("|");
                                s = `${neNameMap[temp[0]]}|${temp[1]}`;
                            }
                            let d = item.dest;
                            if (d.indexOf("|") > -1) {
                                const temp = d.split("|");
                                d = `${neNameMap[temp[0]]}|${temp[1]}`;
                            }
                            return (
                                <Tag key={`${neNameMap[item.ne_id]} : ${s} -> ${d}`} color="green">{`${
                                    neNameMap[item.ne_id]
                                } : ${s} -> ${d}`}</Tag>
                            );
                        })
                    };
                });
                // 处理后的fiber conncetion信息
                dispatch(
                    setConnections(
                        res?.map(item => {
                            const {description, ...others} = item;
                            return others;
                        })
                    )
                );
                // 树状态变更标志关闭
                dispatch(setTreeItemChangedTag(false));
            } catch (e) {
                // eslint-disable-next-line no-console
                console.log(e);
            }
        });
    };

    const refLayout = useRef();

    const tabList = {
        tree: {
            id: "tabTree",
            title: labelList.ne_list,
            content: <DeviceTree />,
            minWidth: 200,
            group: "card custom"
        },
        common: {
            id: "tabCommon",
            title: labelList.common_view,
            content: <CommonView />
        },
        map: {
            id: "tabMap",
            title: labelList.map,
            content: <DeviceMap />,
            group: "card",
            minHeight: 100
        },
        chassis: {
            id: "tabChassis",
            title: labelList.device_view,
            content: <ChassisContainer />
        },
        switchDetail: {
            id: "tabSwitchDetail",
            title: "Switch",
            content: <SwitchDetail />
        },
        DCP920Detail: {
            id: "tabDCP920Chassis",
            title: labelList.device_view,
            content: <DCP920 />
        },
        FMTDetail: {
            id: "tabFMTDetail",
            title: labelList.device_view,
            content: <FMT />
        }
    };
    const layoutList = {
        tree: {
            dockbox: {
                mode: "horizontal",
                children: [
                    {
                        id: "rightPanel",
                        tabs: [tabList.tree],
                        group: "default"
                    }
                ]
            }
        },
        default: {
            dockbox: {
                mode: "horizontal",
                children: [
                    {
                        id: "panel",
                        tabs: onlyOTN
                            ? [tabList.map, tabList.chassis, tabList.DCP920Detail, tabList.FMTDetail]
                            : [
                                  tabList.common,
                                  tabList.map,
                                  tabList.chassis,
                                  tabList.switchDetail,
                                  tabList.DCP920Detail,
                                  tabList.FMTDetail
                              ],
                        group: "default"
                    }
                ]
            }
        },
        common: {
            dockbox: {
                mode: "horizontal",
                children: [
                    {
                        id: "panel",
                        tabs: [{id: "tabCommon"}],
                        group: "default"
                    }
                ]
            }
        },
        map: {
            dockbox: {
                mode: "horizontal",
                children: [
                    {
                        id: "panel",
                        tabs: [{id: "tabMap"}],
                        group: "default"
                    }
                ]
            }
        },
        mapChassis: {
            dockbox: {
                mode: "horizontal",
                children: [
                    {
                        id: "panel",
                        tabs: [{id: "tabMap"}, {id: "tabChassis"}],
                        group: "default"
                    }
                ]
            }
        },
        mapDCP920Chassis: {
            dockbox: {
                mode: "horizontal",
                children: [
                    {
                        id: "panel",
                        tabs: [{id: "tabMap"}, {id: "tabDCP920Chassis"}],
                        group: "default"
                    }
                ]
            }
        },
        mapFMTChassis: {
            dockbox: {
                mode: "horizontal",
                children: [
                    {
                        id: "panel",
                        tabs: [{id: "tabMap"}, {id: "tabFMTDetail"}],
                        group: "default"
                    }
                ]
            }
        },
        switch: {
            dockbox: {
                mode: "horizontal",
                children: [
                    {
                        id: "panel",
                        tabs: [{id: "tabMap"}, {id: "tabSwitchDetail"}],
                        group: "default"
                    }
                ]
            }
        }
    };

    const groups = {
        card: {floatable: false, tabLocked: true, disableDock: true, maximizable: true, animated: false},
        "card custom": {floatable: false, tabLocked: true, disableDock: true, maximizable: true, animated: false},
        default: {floatable: false, tabLocked: true, disableDock: true, maximizable: true}
    };

    useEffect(() => {
        refLayout.current.loadLayout(layoutList.default);
    }, [labelList]);

    const {runAsync} = useRequest(apiConnectionByGroup, {manual: true});

    const mockDividerDown = e => {
        if (e.target.className === "dock-divider drag-initiator") {
            dispatch(setWrapMock(true));
        }
    };
    const mockDividerUp = e => {
        if (e.target.className === "dock-divider drag-initiator") {
            dispatch(setWrapMock(false));
        }
    };

    useEffect(() => {
        loadData();
        window.addEventListener("mousedown", mockDividerDown);
        window.addEventListener("mouseup", mockDividerUp);
        refLayout.current.loadLayout(onlyOTN ? layoutList.map : layoutList.common);
        return () => {
            dispatch(setSelectedItem({}));
            dispatch(setTableFilter({}));
            window.removeEventListener("mousedown", mockDividerDown);
            window.removeEventListener("mouseup", mockDividerUp);
        };
    }, []);

    useEffect(() => {
        if (selectedItem.resource) return;
        const nodeType = selectedItem?.value?.nodeType;
        if (!nodeType) {
            if (refLayout?.current?.getLayout()?.dockbox?.children?.[0]?.tabs?.length === 2) {
                refLayout.current.loadLayout(layoutList.map);
            }
            return;
        }
        if (nodeType === "common_group") {
            refLayout.current.loadLayout(layoutList.common);
            return;
        }
        if (nodeType === "otn_ne") {
            if (selectedItem.value?.type === "6") {
                refLayout.current.loadLayout(layoutList.mapDCP920Chassis);
            } else if (selectedItem.value?.type === "7") {
                refLayout.current.loadLayout(layoutList.mapFMTChassis);
            } else {
                refLayout.current.loadLayout(layoutList.mapChassis);
            }
            if (selectedItem.action === "selectChassis") {
                refLayout.current.dockMove(tabList.chassis, "panel", "middle");
            }
            if (selectedItem.action === "selectChassisDCP920") {
                refLayout.current.dockMove(tabList.DCP920Detail, "panel", "middle");
            }
            if (selectedItem.action === "selectChassisFMT") {
                refLayout.current.dockMove(tabList.FMTDetail, "panel", "middle");
            }
            return;
        }
        if (nodeType === "switch_ne") {
            refLayout.current.loadLayout(layoutList.switch);
            refLayout.current.dockMove(tabList.switchDetail, "panel", "middle");
            return;
        }
        refLayout.current.loadLayout(layoutList.map);
    }, [selectedItem]);

    useEffect(() => {
        if (treeItemChangedTag) {
            loadData();
        }
    }, [treeItemChangedTag]);

    // const onLayoutChange = (newLayout, tabName, operation) => {
    //     dispatch(setWrapMock(false));
    // };

    return (
        <DividerBox className={styles.dockLayout}>
            <DividerBox mode="vertical" style={{width: 329, backgroundColor: "#fff", borderRadius: 5}}>
                <DeviceTree />
            </DividerBox>
            <DockLayout ref={refLayout} defaultLayout={layoutList.default} groups={groups} style={{width: "85%"}} />
        </DividerBox>
    );
};

export default Device;
