import {useEffect, useRef, useState} from "react";
import {DataSet, Network} from "vis-network/standalone/esm/vis-network";
import {useSelector} from "react-redux";
import {useRequest} from "ahooks";
import {getSwitchTreeData} from "@/modules-ampcon/apis/inventory_api";
import {message} from "antd";
import "vis-network/styles/vis-network.css";
import SwitchPic from "@/assets/images/common_view/switch.png";
import OtnPic from "@/assets/images/common_view/otn.png";
import OtnSelectedPic from "@/assets/images/common_view/otn_selected.png";
import OtnOfflinePic from "@/assets/images/common_view/otn_offline.png";
import OtnOfflineSelectedPic from "@/assets/images/common_view/otn_offline_selected.png";
import OtnWarningPic from "@/assets/images/common_view/otn_warning.png";
import OtnWarningSelectedPic from "@/assets/images/common_view/otn_warning_selected.png";
import {apiConnectionByGroup} from "@/modules-otn/apis/api";
import {getOTNDeviceList} from "@/modules-ampcon/apis/otn";
import {formatGroupAndNeInfo} from "@/modules-otn/pages/otn/device/device_map_helper";
import styles from "./common_view.module.scss";

const requestMap = {
    switch: getSwitchTreeData,
    otn: apiConnectionByGroup
};

const iconMap = {
    otn: {
        selected: OtnSelectedPic,
        unselected: OtnPic
    },
    otn_offline: {
        selected: OtnOfflineSelectedPic,
        unselected: OtnOfflinePic
    },
    otn_warning: {
        selected: OtnWarningSelectedPic,
        unselected: OtnWarningPic
    },
    switch: {
        selected: SwitchPic,
        unselected: SwitchPic
    }
};

const ViewItem = ({type, setNodeInfo}) => {
    const [dataSource, setDataSouce] = useState([]);
    const {alarms} = useSelector(state => state.notification);
    const container = useRef(null);
    const networkRef = useRef();

    const getPic = (type, item) => {
        if (type === "otn") {
            if (
                Array.isArray(alarms) &&
                alarms.find(alarm => alarm?.ne_id === item?.value?.ne_id && alarm?.severity === "CRITICAL")
            )
                return "otn_warning";
            if (item?.value?.runState) return "otn";
            if (!item?.value?.runState) return "otn_offline";
        }

        return "switch";
    };

    useRequest(
        () => {
            if (type === "otn") {
                return Promise.all([apiConnectionByGroup(), getOTNDeviceList()]).then(
                    ([otnApiRes, ampconOTNApiRes]) => {
                        if (otnApiRes.apiResult === "fail") throw new Error(otnApiRes.apiMessage);
                        if (ampconOTNApiRes.apiResult === "fail") throw new Error(ampconOTNApiRes.apiMessage);
                        return {neInfo: [...otnApiRes.neInfo, ...ampconOTNApiRes.neInfo]};
                    }
                );
            }
            return requestMap[type]();
        },
        {
            onSuccess(response) {
                const {apiResult, apiMessage, neInfo} = response;
                if (apiResult === "fail") {
                    message.error(apiMessage).then();
                    return;
                }
                setDataSouce(neInfo);
            }
        }
    );

    useEffect(() => {
        if (!container.current) return;
        const nodesData = dataSource.map(item => {
            return {
                id: item.id,
                label: item.value.name,
                image: {
                    selected: iconMap[getPic(type, item)].selected, // 选中状态下的图片路径
                    unselected: iconMap[getPic(type, item)].unselected, // 未选中状态下的图片路径
                    width: 50, // 图片宽度
                    height: 50 // 图片高度
                }
            };
        });
        const nodes = new DataSet(nodesData);
        const edges = new DataSet([]);

        const data = {
            nodes,
            edges
        };
        const options = {
            nodes: {
                shape: "image"
                // fixed: true
            },
            interaction: {
                dragNodes: false,
                dragView: false,
                zoomView: false
            },
            physics: {
                enabled: false
            },
            layout: {
                hierarchical: {
                    direction: "UD", // 设置方向为从上到下
                    sortMethod: "directed", // 根据边的方向排序
                    levelSeparation: 200, // 节点之间的垂直间距
                    nodeSpacing: 100, // 节点之间的水平间距
                    treeSpacing: 200 // 树之间的间距
                }
            }
        };
        networkRef.current = new Network(container.current, data, options);
    }, [dataSource, alarms]);

    useEffect(() => {
        return () => {
            networkRef.current.destroy();
            networkRef.current = null;
        };
    }, []);
    useEffect(() => {
        if (!networkRef.current) return;
        networkRef.current.on(
            "click",
            params => {
                const nodeId = params.nodes[0];
                if (nodeId !== undefined) {
                    const nodeData = dataSource.find(item => item?.id === nodeId);
                    setNodeInfo({...nodeData.value, ...{nodeType: `${type}`}});
                }
            },
            [dataSource, alarms]
        );
        return () => {
            networkRef?.current?.off("click");
        };
    });
    return <div ref={container} className={styles.item} style={{display: "flex"}} />;
};

export default ViewItem;
