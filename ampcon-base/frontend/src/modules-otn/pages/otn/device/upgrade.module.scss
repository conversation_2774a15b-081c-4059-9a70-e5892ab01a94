.common {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    flex: 1;
}

.upgrade {
    :global {
        .ant-collapse {
            border: 0px !important;
            background-color: transparent;          
        }

        .ant-collapse-content {
            border-top: 0px !important;
        }

        .ant-timeline {
            .ant-timeline-item-label {
                text-align: start;
                inset-inline-start: 8px !important;
            }

            .ant-timeline-item-head {
                inset-inline-start: 185px !important;
            }

            .ant-timeline-item-tail {
                inset-inline-start: 185px !important;
            }

            .ant-timeline-item-content {
                inset-inline-start: 237px !important;
                margin-inline-start: 0px;
            }
        }
    }
}

.container {
    :global {
        .ant-table-selection-column {
            padding-top: 9px !important;
            padding-bottom: 9px !important;
        }
    }
}
