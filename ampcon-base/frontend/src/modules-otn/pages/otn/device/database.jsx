import React, {useEffect, useState} from "react";
import {useSelector} from "react-redux";
import {Drawer, message, Collapse, Timeline, Space, Button} from "antd";
import Icon, {LoadingOutlined} from "@ant-design/icons";
import {NEStateConfig} from "@/modules-otn/config/state_config";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {apiNEUpgrade, objectGet, uploadFile} from "@/modules-otn/apis/api";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {NE_TYPE_CONFIG} from "@/modules-otn/utils/util";
import {
    activateEnabledDisabledIcon,
    activateEnabledIcon,
    backupLogDisabledIcon,
    backupLogEnabledIcon
} from "@/modules-otn/pages/otn/device/device_icons";
import {middleModal} from "@/modules-otn/components/modal/custom_modal";
import CustomEmpty from "@/modules-otn/components/common/custom_empty";
import logStyles from "./logs.module.scss";
import upgradeStyles from "./upgrade.module.scss";

const {Panel} = Collapse;

const Database = () => {
    const [commonData, setCommonData] = useState({allData: {}});
    const [upgradeInfo, setUpgradeInfo] = useState();
    const [reloadDataList, setReloadDataList] = useState(false);
    const [backupLoading, setBackupLoading] = useState(false);
    const {upgrade} = useSelector(state => state.notification);
    const userRight = useUserRight();

    const backupDisabled =
        !commonData?.upgrade ||
        backupLoading ||
        commonData?.allData?.upgrade?.filter(a => a.state !== 0).length === 0 ||
        userRight.disabled;
    const activateDisabled =
        !(commonData?.database?.selectedRowKeys?.length > 0) ||
        !(commonData?.upgrade?.selectedRowKeys?.length > 0) ||
        commonData?.allData?.upgrade?.filter(a => a.state === 1 || a.state < 0).length === 0 ||
        userRight.disabled;

    return (
        <div className={logStyles.container} style={commonData?.allData?.upgrade?.length ? {paddingBottom: 16} : {}}>
            <CustomTable
                type="database"
                rootStyle={!commonData?.allData?.database?.length ? {marginBottom: 24} : {}}
                commonData={commonData}
                execRefresh={reloadDataList}
                tableID="database_file"
                scroll={false}
                search
                refreshParent={() => {
                    setReloadDataList(!reloadDataList);
                }}
                setCommonData={setCommonData}
                buttons={[
                    {
                        label: "upload",
                        upload: {
                            name: "data",
                            accept: [".tar.gz", ".txt"],
                            api: "../../otn/api/file/upload?type=data"
                        }
                    }
                ]}
                initRowOperation={[
                    {
                        label: "download_local",
                        disabled: () => {
                            return userRight.disabled;
                        },
                        async onClick() {
                            const {name} = this;
                            window.open(`../../otn/api/file/download?type=data&filename=${name}`, "_self");
                        }
                    }
                ]}
                clearData={(selectedRows, oldData) => {
                    const filterNEType = selectedRows?.[0]?.type;
                    if (filterNEType) {
                        if (oldData.upgrade?.selectedRows?.find(i => i.type !== filterNEType)) {
                            return true;
                        }
                    }
                    return false;
                }}
                columnFormat={{
                    type: state => {
                        return NE_TYPE_CONFIG[parseInt(state)];
                    }
                }}
            />
            <CustomTable
                type="upgrade"
                execRefresh={upgrade}
                search
                scroll={false}
                buttons={[
                    {
                        label: "backup",
                        title: "backup_desc",
                        confirm: {
                            title: "backup_confirm"
                        },
                        icon: <Icon component={backupDisabled ? backupLogDisabledIcon : backupLogEnabledIcon} />,
                        loading: backupLoading,
                        disabled: backupDisabled,
                        type: "primary",
                        onClick() {
                            setBackupLoading(true);
                            const neList = [];
                            commonData.upgrade.selectedRows.forEach(item => {
                                neList.push(item.ne_id);
                            });
                            uploadFile({
                                neList,
                                type: "data"
                            }).then(rs => {
                                if (rs.length > 0 && rs[0]?.apiResult === "SUCCESS") {
                                    setTimeout(() => {
                                        message.success(gLabelList.backup_success).then();
                                        setBackupLoading(false);
                                        setReloadDataList(!reloadDataList);
                                    }, 1000);
                                } else {
                                    message.error(gLabelList.backup_failed).then();
                                    setBackupLoading(false);
                                }
                            });
                        }
                    },
                    {
                        label: "activate",
                        confirm: {
                            title: "activate_confirm"
                        },
                        icon: <Icon component={activateDisabled ? activateEnabledDisabledIcon : activateEnabledIcon} />,
                        disabled: activateDisabled,
                        onClick: () => {
                            const neList = [];
                            commonData.upgrade.selectedRows.forEach(item => {
                                neList.push(item.ne_id);
                            });
                            commonData.upgrade.selectedRows = [];
                            apiNEUpgrade(neList, commonData.database.selectedRows[0].name, "data").then();
                        }
                    }
                ]}
                checkboxProps={record => {
                    try {
                        const {type: neType, state: neState} = record ?? {};
                        const [selectedImage] = commonData?.database?.selectedRows ?? [];
                        const {type: imageType} = selectedImage ?? {};

                        const disableState = neState === 0 || neState >= 2;
                        if (disableState) {
                            return {disabled: true};
                        }
                        if (imageType === "5" && neType !== "5") {
                            return {disabled: true};
                        }

                        return {disabled: false};
                    } catch (e) {
                        return {disabled: false};
                    }
                }}
                columnFormat={{
                    type: state => {
                        return NE_TYPE_CONFIG[parseInt(state)];
                    },
                    state: (state, data) => {
                        try {
                            const {upgradeType} = data;
                            let _state = state < 0 ? "error" : state;
                            if (upgradeType !== "data" && state < 0) {
                                _state = 1;
                            }
                            return (
                                <span>
                                    {NEStateConfig[upgradeType][_state].icon}{" "}
                                    {gLabelList[NEStateConfig[upgradeType][_state].title2]}
                                </span>
                            );
                        } catch (e) {
                            return state;
                        }
                    }
                }}
                initRowOperation={[
                    {
                        label: "history",
                        async onClick() {
                            // eslint-disable-next-line react/no-this-in-sfc
                            openActiveHistory(this.ne_id);
                        }
                    }
                ]}
                setCommonData={setCommonData}
                commonData={commonData}
            />
            <Drawer
                width={400}
                title={upgradeInfo?.value.ne_id || ""}
                open={!!upgradeInfo}
                size="large"
                destroyOnClose
                onClose={() => setUpgradeInfo(null)}
            />
        </div>
    );
};

const ActiveHistory = ({ne_id}) => {
    const {upgrade} = useSelector(state => state.notification);
    const [upgradeInfo, setUpgradeInfo] = useState();

    useEffect(() => {
        if (upgrade && upgradeInfo && upgrade?.source === upgradeInfo?.value.ne_id) {
            updateUpgradeInfo(upgrade.source);
        }
    }, [upgrade]);

    const updateUpgradeInfo = () => {
        objectGet("config:ne", {ne_id}).then(rs => {
            setUpgradeInfo(rs.documents[0]);
        });
    };

    useEffect(() => {
        updateUpgradeInfo();
    }, []);

    const getNEState = rs => {
        return rs.value?.upgrade_type === "database" && rs.value?.state > 1;
    };

    return upgradeInfo ? (
        <div className={upgradeStyles.upgrade}>
            <Collapse accordion expandIconPosition="end" defaultActiveKey={[upgradeInfo?.value.data?.[0]?.startTime]}>
                {Array.isArray(upgradeInfo?.value?.data) && upgradeInfo.value.data.length ? (
                    upgradeInfo.value.data.map((item, index) => {
                        try {
                            return (
                                <Panel header={item.startTime} key={item.startTime}>
                                    <Timeline
                                        mode="left"
                                        pending={
                                            index === 0 && getNEState(upgradeInfo) ? (
                                                <a>{gLabelList.activating}</a>
                                            ) : (
                                                false
                                            )
                                        }
                                    >
                                        {item.step.map(item2 => {
                                            return (
                                                <Timeline.Item
                                                    key={item2.time}
                                                    color={item2.state < 0 ? "red" : "blue"}
                                                    label={item2.time}
                                                >
                                                    {gLabelList[NEStateConfig.data[Math.abs(item2.state)]?.title]}
                                                    {item2.state < 0 ? ` (${gLabelList.failed})` : null}
                                                    {item2.desc ? ` ${item2.desc}` : null}
                                                </Timeline.Item>
                                            );
                                        })}
                                    </Timeline>
                                </Panel>
                            );
                        } catch (e) {
                            return <div>{e.toString()}</div>;
                        }
                    })
                ) : (
                    <CustomEmpty />
                )}
            </Collapse>
        </div>
    ) : (
        <div style={{width: "100%", textAlign: "center"}}>
            <a>
                <LoadingOutlined style={{fontSize: 32, fill: "#14C9BB", color: "#14C9BB"}} />
            </a>
        </div>
    );
};

const openActiveHistory = ne_id => {
    middleModal({
        title: ne_id,
        footer: <div style={{height: 32, borderTop: "none"}} className="ant-modal-confirm-btns" />,
        content: <ActiveHistory ne_id={ne_id} />
    });
};

export default Database;
