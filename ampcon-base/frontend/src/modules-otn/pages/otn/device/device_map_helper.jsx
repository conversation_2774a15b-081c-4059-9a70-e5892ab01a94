import {sortArr} from "@/modules-otn/utils/util";

// 根据组ID返回所有下层组
export const getDownGroupsByGroupId = (id, groupList) => {
    const groupDetail = groupList.find(group => group.id === id);
    if (!groupDetail) return [];

    const result = [groupDetail];
    let needFindList = groupList.filter(item => item?.id !== id);
    let isExistFamilyItem = needFindList.some(item =>
        result.find(resultItem => resultItem.id === item?.value?.parentKey)
    );
    while (isExistFamilyItem) {
        const innerNeedFindList = [];
        needFindList.forEach(item => {
            if (result.find(resultItem => resultItem.id === item?.value?.parentKey)) {
                result.push(item);
            } else {
                innerNeedFindList.push(item);
            }
        });
        needFindList = innerNeedFindList;
        isExistFamilyItem = innerNeedFindList.some(item =>
            result.find(resultItem => resultItem.id === item?.value?.parentKey)
        );
    }
    return result;
};

// 根据组ID返回所有上层组
export const getUpGroupsByGroupId = (id, groupList) => {
    groupList = groupList ?? [];
    if (isRootNode(id)) return [id];
    let groupDetail = groupList.find(group => String(group.id) === String(id));
    const resData = [];
    while (groupDetail && !isRootNode(groupDetail.value.parentKey)) {
        resData.push(groupDetail);
        // eslint-disable-next-line no-loop-func
        groupDetail = groupList.find(group => String(group.id) === String(groupDetail.value.parentKey));
    }
    if (isRootNode(groupDetail.value.parentKey)) resData.push(groupDetail);
    return resData;
};

// 返回指定组的所有网元ID
export const getNEsByGroupID = (groupID, groupList, neList) => {
    const groups = getDownGroupsByGroupId(groupID, groupList);
    return neList.filter(item => groups.find(group => group.id === item?.value?.group));
};

export const getCategoryByType = type => {
    if (type === "5") return "ne";
    return "group";
};

export const getCategoryByID = id => {
    if (!id || typeof id !== "string") return;
    if (id.startsWith("config:ne")) return "ne";
    return "group";
};

/*  原始json数据转换为geoJson数据
    properties:
        id: 唯一标识
        label: 名称
        type: 5|group
*/
export const getGeojsonData = originData => {
    // console.log("originData =", originData);
    const {id, value = {}} = originData;
    if (isNeNode(value.nodeType)) {
        const {ne_id, group, name, lng, lat, state, isOnline, nodeType, isCritical} = value;
        return {
            type: "Feature",
            properties: {
                id,
                label: name,
                ne_id,
                type: "ne",
                isCritical, // isCritical: 是否存在严重告警
                isOnline, // isOnline: 是否在线
                group,
                state,
                nodeType,
                originData
            },
            geometry: {
                type: "Point",
                coordinates: [parseFloat(lng), parseFloat(lat)] // 坐标
            }
        };
    }

    if (isGroupNode(value.nodeType)) {
        const {name, parentKey, lng, lat, nodeType} = value;

        return {
            type: "Feature",
            properties: {
                id,
                label: name,
                type: "group",
                nodeType,
                parentKey, // 父组ID,
                originData
            },
            geometry: {
                type: "Point",
                coordinates: [parseFloat(lng), parseFloat(lat)]
            }
        };
    }
};

export const getNeListByGroupId = (groupMap, groupId) => {
    return Object.entries(groupMap).reduce((res, item) => {
        const [id, groupName] = item;
        if (groupName === groupId) return res.concat(id);
        return res;
    }, []);
};

export const getGroupType = (type, location) => {
    if (type === undefined && location === undefined) return "group";
    const groupLevelFlag = location?.slice(-1);
    if (type === 0 && groupLevelFlag === "p") return "primary";
    if (type === 0 && groupLevelFlag === "s") return "standby";
    if (type === 1) return "first";
    if (type === 2) return "second";
};

// 定位状态更新组获取
export const getNeedUpdateGroupKeys = ({id, neAndGroupData}) => {
    const rootKey = ["otnRoot", "switchRoot"];
    const result = [];

    const getNeedUpdateGroupItem = id => {
        const {groupInfo: groupList, neInfo: neList} = neAndGroupData;
        const detail = getNeOrGroupDetailById(id, groupList, neList);
        const group = detail.value?.group ?? detail.value?.parentKey;

        // 顶层网元
        if (rootKey.includes(group)) return;

        const brotherNe = neList.filter(item => item.value.group === group).map(item => ({...item.value, id: item.id}));
        const brotherGroup = groupList
            .filter(item => item.value.parentKey === group)
            .map(item => ({...item.value, id: item.id}));
        const queryList = sortArr([...brotherNe, ...brotherGroup], ["name"]);
        const queryItemIndex = queryList.findIndex(item => String(item.id) === String(id));
        const isFirstNode = queryItemIndex === 0;
        const isFirstLocationedNode = queryList.slice(0, queryItemIndex).some(item => item.lng && item.lat);
        if (isFirstNode || !isFirstLocationedNode) {
            result.push(group);
            const {parentKey} = groupList.find(item => String(item.id) === String(group)).value;
            if (!rootKey.includes(parentKey)) getNeedUpdateGroupItem(group);
        }
    };
    getNeedUpdateGroupItem(id);
    return result;
};

export const getNeedUpdateGroupKeysInLeave = ({id, groupInfo, neInfo}) => {
    const result = {};
    const test = ({id, groupInfo, neInfo}) => {
        if (isRootNode(id)) return;
        const detail = getNeOrGroupDetailById(id, groupInfo);
        const baseLocationNode = getBaseLocationNode({id, groupInfo, neInfo});
        const newLng = baseLocationNode?.value?.lng;
        const newLat = baseLocationNode?.value?.lat;
        if (detail.value.lng !== newLng || detail.value.lat !== newLat) {
            const key = `${String(newLng)},${String(newLat)}`;
            if (result[key]) {
                result[key].push(id);
            } else {
                result[key] = [id];
            }
            groupInfo = groupInfo.map(item => {
                if (item.id === id) {
                    return {
                        id: item.id,
                        value: {
                            ...item.value,
                            lng: newLng,
                            lat: newLat
                        }
                    };
                }
                return item;
            });
        }
        test({id: detail.value.parentKey, groupInfo, neInfo});
    };
    test({id, groupInfo, neInfo});
    return result;
};

export const getBaseLocationNode = ({id, neInfo, groupInfo}) => {
    if (isRootNode(id)) return undefined;
    const QueryList = sortArr(
        [...neInfo, ...groupInfo].filter(item => item.value.group === id || item.value.parentKey === id),
        ["name"]
    );
    return QueryList.find(item => item.value.lng && item.value.lat);
};

export const getNeOrGroupDetailById = (id, groupInfo = [], neInfo = []) => {
    return [...groupInfo, ...neInfo].find(item => String(item.id) === String(id));
};

export const splitOtnOrSwitchGroupKey = groupKeys => {
    return groupKeys.reduce(
        (res, key) => {
            if (key.includes("nms:group")) {
                res.otn.push(key);
            } else {
                res.switch.push(key);
            }
            return res;
        },
        {otn: [], switch: []}
    );
};

export const isOtnNode = type => ["otn_group", "otn_ne"].includes(type);
export const isSwitchNode = type => ["switch_group", "switch_ne"].includes(type);
export const isCommonViewNode = type => type === "common_group";
export const isGroupNode = type => ["switch_group", "otn_group"].includes(type);
export const isNeNode = type => ["switch_ne", "otn_ne"].includes(type);
export const isRootNode = id => ["otnRoot", "switchRoot", "commonViewRoot"].includes(id);
export const isOtnGroup = type => type === "otn_group";
export const isSwitchGroup = type => type === "switch_group";
export const isOtnNe = type => type === "otn_ne";
export const isSwitchNe = type => type === "switch_ne";
export const getRootNode = id => {
    const rootNode = [
        {
            id: "commonViewRoot",
            value: {
                name: "Common View",
                nodeType: "common_group"
            }
        },
        {
            id: "otnRoot",
            value: {
                name: "OTN",
                nodeType: "otn_group"
            }
        },
        {
            id: "switchRoot",
            value: {
                name: "Switch",
                nodeType: "switch_group"
            }
        }
    ];
    return rootNode.find(item => item.id === id);
};

export const formatGroupAndNeInfo = (data, type) => {
    const {groupInfo = [], neInfo = []} = data;
    if (type === "otn") {
        const formatGroups = groupInfo.map(item => ({
            ...item,
            value: {...item.value, nodeType: "otn_group"}
        }));
        const formatNes = neInfo.map(item => ({
            ...item,
            value: {...item.value, nodeType: "otn_ne"}
        }));

        return {formatGroups, formatNes};
    }
    if (type === "switch") {
        const formatGroups = groupInfo.map(item => ({
            ...item,
            value: {...item.value, nodeType: "switch_group"}
        }));
        const formatNes = neInfo.map(item => ({
            ...item,
            value: {...item.value, nodeType: "switch_ne"}
        }));
        return {formatGroups, formatNes};
    }
};
