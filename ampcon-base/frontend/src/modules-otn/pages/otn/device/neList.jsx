import {useEffect, useState, useRef} from "react";
import {useSelector} from "react-redux";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {apiGetNESummary, objectGet} from "@/modules-otn/apis/api";
import {includes} from "lodash/collection";

const NeList = ({serviceType}) => {
    const {tableFilter} = useSelector(state => state.map);
    const {neNameMap} = useSelector(state => state.neName);
    const [group, setGroup] = useState({});
    const [dataList, setDataList] = useState([]);
    const tableFilterRef = useRef(tableFilter);

    tableFilterRef.current = tableFilter;

    const loadData = async gp => {
        try {
            const _group = gp ?? group;
            let rs = await apiGetNESummary();
            if (tableFilterRef.current.type === "NODE_NE") {
                rs = rs.filter(item => tableFilterRef.current.id === item.ne_id);
            } else if (tableFilterRef.current.type === "NODE_GROUP") {
                rs = rs.filter(item => tableFilterRef.current.idList.includes(item.ne_id));
            } else if (tableFilterRef.current.type === "CELL_FILTER") {
                rs = rs.filter(
                    item =>
                        tableFilterRef.current.sourceList.includes(item.ne_id) ||
                        tableFilterRef.current.targetList.includes(item.ne_id)
                );
            } else if (tableFilterRef.current.servicePortList) {
                const ne1 = tableFilterRef.current.servicePortList.map(item => item.ne_id);
                const raman = tableFilterRef.current.provision?.ots_ne ?? [];
                const ramanSet = new Set();
                raman.forEach(neInfo => {
                    Object.entries(neInfo).forEach(([k, v]) => {
                        if (k.indexOf("raman") > -1) {
                            ramanSet.add(v);
                        }
                    });
                });
                const nes = new Set([...ne1, ...Array.from(ramanSet)]);
                rs = rs.filter(item => nes.has(item.ne_id));
            } else if (serviceType) {
                rs = rs.filter(item => ["2", "5"].includes(item.type));
            }
            setDataList(
                rs.map(item => {
                    return {
                        ...item,
                        key: item.ne_id,
                        name: neNameMap[item.ne_id],
                        group: _group[item.group]
                    };
                })
            );
        } catch (e) {
            // console.log(e);
        }
    };

    useEffect(() => {
        loadData().then();
    }, [neNameMap, tableFilter]);

    useEffect(() => {
        objectGet("nms:group").then(rs => {
            const gp = {};
            rs.documents.map(item => {
                gp[item.id] = item.value.name;
            });
            setGroup(gp);
            loadData(gp).then();
        });
    }, []);

    return <CustomTable type="neList" initDataSource={dataList} initHead={false} />;
};

export default NeList;
