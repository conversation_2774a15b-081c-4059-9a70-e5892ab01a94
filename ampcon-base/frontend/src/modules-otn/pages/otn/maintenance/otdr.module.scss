.btn {
    height: 50px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-top: 16px;
    padding-bottom: 32px
}

.stopBtn {
    background: #FFF;
    color: #14c9bb;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0);
    border: 1px solid #14c9bb;
}

.content {
    display: flex;
    flex-direction: column;
    flex: 1;
    // overflow: hidden;
    :global {
        .ant-card-body {
            padding-top: 24px !important;
            padding-bottom: 24px !important;
        }
    }
}

.headerRow {
    flex: 1.1;
    margin-bottom: 24px;
}

.footerRow {
    flex: 1.3;
    margin-bottom: 24px;
    overflow: hidden;
}

.cardBorder {
    height: 100%;
    border-top: 1px solid #F2F2F2;
    border-bottom: 1px solid #F2F2F2;
}
