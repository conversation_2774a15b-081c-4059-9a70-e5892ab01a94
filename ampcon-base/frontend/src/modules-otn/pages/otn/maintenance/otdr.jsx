import React, {useEffect, useLayoutEffect, useRef, useState} from "react";
import * as echarts from "echarts";
import {message, Table, Col, Row, Card, Space, Form, Input} from "antd";
import Icon, {SyncOutlined} from "@ant-design/icons";
import {useSelector} from "react-redux";
import {apiOtdrGet, apiRpc, netconfChange} from "@/modules-otn/apis/api";
import {openDBModalCreate} from "@/modules-otn/components/form/create_form_db";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {playSvg, pauseSvg, resultSvg, pauseDisabledSvg, resultDisabledSvg} from "@/modules-otn/utils/iconSvg";
import ProcessTool from "@/modules-otn/components/common/process_tool";
import {convertToArray, DebounceButton, getText} from "@/modules-otn/utils/util";
import {useSize} from "ahooks";
import {middleModal} from "@/modules-otn/components/modal/custom_modal";
import {debounce} from "lodash";
import styles from "./otdr.module.scss";

const OTDR = () => {
    const {labelList} = useSelector(state => state.languageOTN);
    const chartRef = useRef();
    const {events} = useSelector(state => state.notification);
    const {neNameMap} = useSelector(state => state.neName);
    const [otdrConfig, setOtdrConfig] = useState();
    const [resultConfig, setResultConfig] = useState();
    const [otdrRun, setOtdrRun] = useState(false);
    const [stopRun, setStopRun] = useState(false);
    const [curDataSource, setCurDataSource] = useState([]);
    const [ABDataSource, setABDataSource] = useState([]);
    const [eventData, setEventData] = useState([]);
    const [process, setProcess] = useState({percent: -1});
    const userRight = useUserRight();
    const topRef = useRef();
    const bottomRef = useRef();
    const size = useSize(bottomRef);

    const eventColumns = [
        {dataIndex: "event-index", key: "event-index", title: labelList.index, width: 100, fixed: "left"},
        {dataIndex: "distance", key: "distance", title: labelList.distance, width: 150},
        {dataIndex: "splice-loss", key: "splice-loss", title: labelList["splice-loss"], width: 150},
        {dataIndex: "return-loss", key: "return-loss", title: labelList["return-loss"], width: 150},
        {dataIndex: "cumulate-loss", key: "cumulate-loss", title: labelList["cumulate-loss"], width: 150},
        {dataIndex: "event-type", key: "event-type", title: labelList["event-type"], width: 200, fixed: "right"}
    ];

    const curColumns = [
        {
            title: labelList.parameter,
            dataIndex: "parameter",
            key: "parameter"
        },
        {
            title: labelList.value,
            dataIndex: "value",
            key: "value"
        }
    ];

    const ABColumns = [
        {
            title: labelList.mark,
            dataIndex: "mark",
            key: "mark"
        },
        {
            title: `${labelList.position}(m)`,
            dataIndex: "position",
            key: "position"
        },
        {
            title: `${labelList.loss}(dB)`,
            dataIndex: "loss",
            key: "loss"
        }
    ];
    const pointSpace = 1.5;
    useEffect(() => {
        if (otdrRun) {
            if (events[0].ne_id === otdrConfig?.ne && events[0]["event-abbreviate"] === "OTDR-TEST-COMPLETE") {
                setTimeout(() => {
                    setResultConfig({
                        ne_id: otdrConfig?.ne,
                        name: otdrConfig?.name,
                        index: 0
                    });
                    otdrConfig?.callback({});
                }, 6000);
            }
        }
    }, [events]);

    const ABLine = {
        itemStyle: {
            borderWidth: 1
        },
        markAOnDrag: false,
        markBOnDrag: false,
        data: [
            [
                {
                    name: "A",
                    xAxis: 500,
                    yAxis: 0,
                    lineStyle: {
                        type: "solid",
                        color: "#9a9117",
                        width: 3
                    }
                },
                {
                    name: "",
                    xAxis: 500,
                    yAxis: 30
                }
            ],
            [
                {
                    name: "B",
                    xAxis: 1000,
                    yAxis: 0,
                    lineStyle: {
                        type: "solid",
                        color: "#32ee08",
                        width: 3
                    }
                },
                {
                    name: "",
                    xAxis: 1000,
                    yAxis: 30
                }
            ]
        ]
    };

    const option = {
        title: {
            left: "left",
            text: `${resultConfig ? `(${neNameMap[resultConfig?.ne_id]} / ${resultConfig?.name})` : ""}`,
            textStyle: {
                // color: "#fff"
            }
        },
        tooltip: {
            trigger: "axis",
            axisPointer: {
                type: "cross"
            }
        },
        color: ["#087bee", "#c4ccd3", "#749f83"],
        toolbox: {
            emphasis: {
                iconStyle: {
                    borderColor: "#14C9BB" // 图标hover颜色
                }
            },
            feature: {
                // myTool1: {
                //     show: true,
                //     title: "Start OTDR",
                //     icon: "path://M823.8 603.5l-501.2 336c-50.7 34-119.3 20.4-153.2-30.2-12.2-18.2-18.7-39.6-18.7-61.5v-672c0-61 49.5-110.4 110.4-110.4 21.9 0 43.3 6.5 61.5 18.7l501.1 336c50.7 34 64.2 102.6 30.2 153.2-7.8 11.9-18.1 22.2-30.1 30.2z m0 0",
                //     iconStyle: {color: "#eedb08", borderWidth: 1, borderColor: "#eedb08", borderStyle: "solid"},
                //     onclick: () => {
                //         alert("myToolHandler");
                //     }
                // },
                myTool2: {
                    show: resultConfig !== undefined,
                    title: labelList.otdr_cfg_desc,
                    icon: "path://M256 213.333333h512c25.6 0 42.666667 17.066667 42.666667 42.666667v512c0 25.6-17.066667 42.666667-42.666667 42.666667H256c-25.6 0-42.666667-17.066667-42.666667-42.666667V256c0-25.6 17.066667-42.666667 42.666667-42.666667z",
                    iconStyle: {color: "#32ee08", borderWidth: 1, borderColor: "#087bee", borderStyle: "solid"},
                    onclick: () => {
                        if (option.relateData) {
                            middleModal({
                                title: labelList.otdr_cfg_desc,
                                content: (
                                    <Form labelCol={{span: 8}} wrapperCol={{span: 14}} labelAlign="left">
                                        {Object.keys(option.relateData.config).map(item => (
                                            <Form.Item key={item} label={getText(item)}>
                                                <Input
                                                    disabled
                                                    value={option.relateData.config[item]}
                                                    style={{width: 280}}
                                                />
                                            </Form.Item>
                                        ))}
                                    </Form>
                                )
                            });
                        }
                    }
                },
                dataZoom: {
                    yAxisIndex: "none"
                },
                restore: {},
                saveAsImage: {}
            }
        },
        grid: [
            {
                left: 50,
                right: 50,
                bottom: 65,
                top: "15%"
            },
            {
                left: 50,
                right: 50,
                top: "88%",
                height: 1
            }
        ],
        xAxis: [
            {
                min: 0,
                type: "value",
                name: `${labelList.distance}(m)`,
                minInterval: 1,
                scale: true,
                nameLocation: "middle",
                nameGap: "18",
                axisLine: {
                    lineStyle: {
                        color: "#090909"
                    }
                },
                splitLine: {
                    show: false
                },
                splitArea: {
                    show: true,
                    areaStyle: {
                        color: ["rgba(250,250,250,0.0)", "rgba(250,250,250,0.05)"]
                    }
                },
                axisLabel: {
                    color: "#090909",
                    fontSize: 12
                }
            }
            // ,
            // {
            //     min: 0,
            //     gridIndex: 1,
            //     type: "value",
            //     minInterval: 1,
            //     scale: true,
            //     nameLocation: "middle",
            //     nameGap: "18",
            //     boundaryGap: false,
            //     axisLine: {
            //         onZero: true,
            //         lineStyle: {
            //             color: "#090909"
            //         }
            //     },
            //     position: "top",
            //     splitLine: {
            //         show: false
            //     },
            //     splitArea: {
            //         show: true,
            //         areaStyle: {
            //             color: ["rgba(250,250,250,0.0)", "rgba(250,250,250,0.05)"]
            //         }
            //     },
            //     axisLabel: {
            //         color: "#00c7ff",
            //         fontSize: 12,
            //         show: false
            //     },
            //     show: true
            // }
        ],
        yAxis: [
            {
                min: 0,
                type: "value",
                name: `${labelList.attenuation}(dB)`,
                nameLocation: "middle",
                nameGap: "30",
                axisLine: {
                    lineStyle: {
                        color: "#090909"
                    }
                },
                splitLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                splitArea: {
                    show: true,
                    areaStyle: {
                        color: ["rgba(250,250,250,0.0)", "rgba(250,250,250,0.05)"]
                    }
                },
                axisLabel: {
                    color: "#090909",
                    fontSize: 12
                }
            }
            // ,
            // {
            //     gridIndex: 1,
            //     name: "link",
            //     type: "value",
            //     inverse: true,
            //     show: false
            // }
        ],
        dataZoom: [
            {
                type: "inside",
                start: 0,
                end: 1000,
                xAxisIndex: [0, 1]
            },
            {
                start: 0,
                end: 1000,
                bottom: 5,
                handleIcon:
                    "M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",
                handleSize: "80%",
                handleStyle: {
                    color: "#fff",
                    shadowBlur: 3,
                    shadowColor: "rgba(0, 0, 0, 0.6)",
                    shadowOffsetX: 2,
                    shadowOffsetY: 2
                },
                xAxisIndex: [0, 1]
            }
        ],
        series: [
            {
                z: 1,
                data: [],
                type: "line",
                smooth: true,
                showSymbol: false,
                markLine: ABLine,
                itemStyle: {
                    color: "#14C9BB"
                }
            }
            // {
            //     name: "Reference",
            //     z: 0,
            //     data: [],
            //     type: "line",
            //     smooth: true,
            //     showSymbol: false
            // }
        ],
        animation: true
    };

    const createTableTr = (key, parameter, value) => {
        return {
            key,
            parameter,
            value
        };
    };

    const fillTableData1 = result => {
        let index = 1;
        const tableData = [];
        Object.keys(result).map(item => {
            tableData.push(createTableTr(index++, item, result[item]));
        });
        setCurDataSource(tableData);
    };

    // const fillTableData = result => {
    //     let index = 1;
    //     const tableData = [];
    //     tableData.push(createTableTr(index++, "fiber length", `${result.summary["loss end"]} km`));
    //     tableData.push(createTableTr(index++, "Total Loss", `${result.summary["total loss"]} dB`));
    //     tableData.push(
    //         createTableTr(
    //             index++,
    //             "Attenuation",
    //             `${(result.summary["total loss"] / result.summary["loss end"]).toFixed(3)} dB/km`
    //         )
    //     );
    //     tableData.push(createTableTr(index++, "ORL", `${result.summary.ORL} dB`));
    //     Object.keys(result.params.FxdParams).forEach(k => {
    //         if (k === "date/time") {
    //             tableData.push(
    //                 createTableTr(
    //                     index++,
    //                     k,
    //                     new Date(result.params.FxdParams[k] * 1000).toLocaleString("lt-LT", {timeZone: "UTC"})
    //                 )
    //             );
    //         } else {
    //             tableData.push(createTableTr(index++, k, result.params.FxdParams[k]));
    //         }
    //     });
    //     setCurDataSource(tableData);
    // };

    const getPointIndex = (pointData, pointSpace, curDistance) => {
        if (pointData == null || pointData.length < 1 || curDistance < 0) {
            return -1;
        }
        for (let i = 0; i < pointData.length; i++) {
            if (Math.abs(pointData[i][0] - curDistance) <= pointSpace * 0.5) {
                return i;
            }
        }
        return -1;
    };

    useEffect(() => {
        try {
            const chart = echarts.init(chartRef.current);
            chart.on("mousedown", params => {
                if (params.componentType === "markLine") {
                    // eslint-disable-next-line eqeqeq
                    if (params.dataIndex === 0) {
                        ABLine.markAOnDrag = true;
                    } else {
                        ABLine.markBOnDrag = true;
                    }
                }
            });
            chart.getZr().on("mouseup", params => {
                const pointInPixel = [params.offsetX, params.offsetY];
                if (chart.containPixel("grid", pointInPixel)) {
                    if (ABLine.markAOnDrag) {
                        ABLine.markAOnDrag = false;
                    }
                    if (ABLine.markBOnDrag) {
                        ABLine.markBOnDrag = false;
                    }
                }
            });
            chart.getZr().on("mousemove", params => {
                if (ABLine.markAOnDrag) {
                    const pointInPixel = [params.offsetX, params.offsetY];
                    if (chart.containPixel("grid", pointInPixel)) {
                        const pointIndex = chart.convertFromPixel(
                            {
                                seriesIndex: 0
                            },
                            [params.offsetX, params.offsetY]
                        )[0];
                        const xIndex = getPointIndex(option.series[0].data, pointSpace, pointIndex);
                        if (xIndex < 0) {
                            return;
                        }
                        // eslint-disable-next-line prefer-destructuring
                        ABLine.data[0][0].xAxis = option.series[0].data[xIndex][0];
                        // eslint-disable-next-line prefer-destructuring
                        ABLine.data[0][1].xAxis = option.series[0].data[xIndex][0];
                        chart.setOption({
                            series: [
                                {
                                    markLine: ABLine
                                }
                            ]
                        });
                        const data = [];
                        const PosA = ABLine.data[0][0].xAxis.toFixed(2);
                        const PosB = ABLine.data[1][0].xAxis.toFixed(2);
                        const PosAB = Math.abs(ABLine.data[0][0].xAxis - ABLine.data[1][0].xAxis).toFixed(2);
                        const xIndexB = getPointIndex(option.series[0].data, pointSpace, ABLine.data[1][0].xAxis);
                        const LossA = Math.round(option.series[0].data[xIndex][1] * 100) / 100;
                        const LossB = Math.round(option.series[0].data[xIndexB][1] * 100) / 100;
                        const LossAB =
                            Math.round((option.series[0].data[xIndex][1] - option.series[0].data[xIndexB][1]) * 100) /
                            100;
                        data.push({
                            key: 1,
                            mark: "Mark A",
                            position: PosA,
                            loss: LossA
                        });
                        data.push({
                            key: 2,
                            mark: "Mark B",
                            position: PosB,
                            loss: LossB
                        });
                        data.push({
                            key: 3,
                            mark: "A-B",
                            position: PosAB,
                            loss: LossAB
                        });
                        setABDataSource(data);
                    }
                }
                if (ABLine.markBOnDrag) {
                    const pointInPixel = [params.offsetX, params.offsetY];
                    if (chart.containPixel("grid", pointInPixel)) {
                        const pointIndex = chart.convertFromPixel(
                            {
                                seriesIndex: 0
                            },
                            [params.offsetX, params.offsetY]
                        )[0];
                        const xIndex = getPointIndex(option.series[0].data, pointSpace, pointIndex);
                        if (xIndex < 0) {
                            return;
                        }
                        // eslint-disable-next-line prefer-destructuring
                        ABLine.data[1][0].xAxis = option.series[0].data[xIndex][0];
                        // eslint-disable-next-line prefer-destructuring
                        ABLine.data[1][1].xAxis = option.series[0].data[xIndex][0];
                        chart.setOption({
                            series: [
                                {
                                    markLine: ABLine
                                }
                            ]
                        });
                        const data = [];
                        const PosA = ABLine.data[0][0].xAxis.toFixed(2);
                        const PosB = ABLine.data[1][0].xAxis.toFixed(2);
                        const PosAB = Math.abs(ABLine.data[0][0].xAxis - ABLine.data[1][0].xAxis).toFixed(2);
                        const xIndexA = getPointIndex(option.series[0].data, pointSpace, ABLine.data[0][0].xAxis);
                        const LossA = Math.round(option.series[0].data[xIndexA][1] * 100) / 100;
                        const LossB = Math.round(option.series[0].data[xIndex][1] * 100) / 100;
                        const LossAB =
                            Math.round((option.series[0].data[xIndexA][1] - option.series[0].data[xIndex][1]) * 100) /
                            100;
                        data.push({
                            key: 1,
                            mark: "Mark A",
                            position: PosA,
                            loss: LossA
                        });
                        data.push({
                            key: 2,
                            mark: "Mark B",
                            position: PosB,
                            loss: LossB
                        });
                        data.push({
                            key: 3,
                            mark: "A-B",
                            position: PosAB,
                            loss: LossAB
                        });
                        setABDataSource(data);
                    }
                }
            });

            if (resultConfig) {
                if (otdrRun) {
                    setProcess({percent: 70, nextPercent: 99, title: labelList.otdr_data_ing});
                }
                apiOtdrGet({
                    ne_id: resultConfig?.ne_id,
                    card: resultConfig?.name,
                    index: resultConfig?.index
                }).then(rs => {
                    if (rs.apiResult === "fail") {
                        message.error(labelList.test_failed.format(rs.apiMessage), 30).then();
                        setOtdrRun(false);
                        setProcess({percent: -1});
                        return;
                    }
                    if (otdrRun) {
                        setOtdrRun(false);
                        setProcess({percent: 100, nextPercent: 100, title: labelList.test_done});
                    }
                    const {result, data, event, resultData} = rs;
                    const pointSpace = result.params.FxdParams.dx * 1000;
                    const dataSize = result.params.DataPts["number of Points"];
                    // let maxY = 0;
                    option.series[0].data = result.points.points.map(item => {
                        item[0] *= 1000;
                        // if (item[1] > maxY) {
                        //     // eslint-disable-next-line prefer-destructuring
                        //     maxY = item[1];
                        // }
                        return item;
                    });
                    option.xAxis[0].max = Math.ceil(pointSpace * dataSize);
                    // ABLine.data[0][1].yAxis = maxY + 2;
                    option.title.text = `OTDR(${neNameMap[resultConfig?.ne_id]} / ${
                        data.config?.["active-local-port"]
                    })`;
                    option.relateData = data;
                    // console.log(option);
                    chart.setOption(option);
                    // 填充表格数据
                    // fillTableData(result);
                    fillTableData1(resultData);

                    setEventData(convertToArray(event));
                });
            } else {
                chart.setOption(option);
            }

            window.onresize = () => {
                chart.resize();
            };

            return () => {
                chart.dispose();
            };
        } catch (e) {
            // console.log(e);
        }
    }, [resultConfig]);

    useLayoutEffect(() => {
        const observer = new ResizeObserver(
            debounce(() => {
                try {
                    [topRef.current, bottomRef.current].forEach(containerElement => {
                        const containerHeight = containerElement.querySelector(".ant-card-body").offsetHeight;
                        const tableBodyHeight = containerHeight - 48 - 48 - 64;

                        Array.from(containerElement.querySelectorAll(".ant-table-tbody")).forEach(bodyElement => {
                            bodyElement.style.height = `${tableBodyHeight}px`;
                        });
                    });
                    if (!curDataSource.length || !eventData.length) {
                        const containerHeight = bottomRef.current.querySelector(".ant-card-body").offsetHeight;
                        const tableContainerHeight = containerHeight - 48 - 48 - 64;
                        const emptyHeight = 123;
                        const translateYValue =
                            (tableContainerHeight - emptyHeight) * -1 + (tableContainerHeight - emptyHeight) * 0.5;
                        if (translateYValue < 0) {
                            Array.from(bottomRef.current.querySelectorAll(".ant-empty")).forEach(emptyElement => {
                                emptyElement.style.transform = `translateY(${translateYValue}px)`;
                            });
                        }
                    }
                } catch (e) {
                    //
                }
            }, 30)
        );
        observer.observe(document.body);
        return () => {
            observer.disconnect();
        };
    }, []);

    return (
        <>
            <div className={styles.btn}>
                <Space size={16}>
                    <DebounceButton
                        type="primary"
                        disabled={otdrRun || userRight.disabled}
                        onClick={() => {
                            openDBModalCreate({
                                type: "startOTDR",
                                okText: labelList.start_test,
                                submit: async (values, defaultValue, diffValue, cancel, fail) => {
                                    if (diffValue["active-local-port"] || diffValue["fiber-refractive-index"]) {
                                        const change = {};
                                        if (diffValue["active-local-port"]) {
                                            change["active-local-port"] = diffValue["active-local-port"];
                                        }
                                        if (diffValue["fiber-refractive-index"]) {
                                            change["fiber-refractive-index"] = diffValue["fiber-refractive-index"];
                                        }
                                        await netconfChange({
                                            ne_id: values.ne,
                                            operation: "edit",
                                            entity: "otdr",
                                            keys: [values.name],
                                            values: {config: change},
                                            msg: false
                                        });
                                    }
                                    apiRpc({
                                        ne_id: values.ne,
                                        rpcName: "start-otdr",
                                        rpcConfig: {
                                            name: values.name,
                                            "measure-mode": values["measure-mode"],
                                            "measuring-range": values["measuring-range"],
                                            "measuring-time": values["measuring-time"],
                                            "pulse-width": values["pulse-width"]
                                        }
                                    }).then(rs => {
                                        if (rs.apiResult === "fail") {
                                            message.error(labelList.test_failed.format(rs.apiMessage)).then();
                                            fail(rs);
                                        } else {
                                            setOtdrConfig({...values, index: 0, callback: cancel});
                                            setOtdrRun(true);
                                            cancel(rs);
                                            setProcess({percent: 5, nextPercent: 70, title: labelList.otdr_start});
                                        }
                                    });
                                }
                            });
                        }}
                    >
                        {otdrRun ? <SyncOutlined spin /> : <Icon component={playSvg} />}
                        {labelList.test_config}
                    </DebounceButton>
                    <DebounceButton
                        disabled={!otdrRun || stopRun}
                        onClick={() => {
                            if (otdrRun) {
                                setStopRun(true);
                                apiRpc({
                                    ne_id: otdrConfig?.ne,
                                    rpcName: "stop-otdr",
                                    rpcConfig: {
                                        name: otdrConfig?.name
                                    }
                                }).then(rs => {
                                    if (rs.message === "SUCCESS") {
                                        setStopRun(false);
                                        setOtdrRun(false);
                                        setProcess({percent: -1});
                                        message.success(labelList.otdr_stop_suc).then();
                                    } else {
                                        message.error(labelList.otdr_stop_fail).then();
                                        setStopRun(false);
                                    }
                                });
                            }
                        }}
                    >
                        {stopRun ? (
                            <SyncOutlined spin />
                        ) : (
                            <Icon component={!otdrRun || stopRun ? pauseDisabledSvg : pauseSvg} />
                        )}
                        {labelList.stop}
                    </DebounceButton>
                    <DebounceButton
                        className={styles.stopBtn}
                        disabled={otdrRun}
                        onClick={() => {
                            openDBModalCreate({
                                type: "resultOTDR",
                                submit: async (values, defaultValue, diffValue, cancel) => {
                                    setResultConfig({
                                        ne_id: values.ne,
                                        name: values.name,
                                        index: values.index
                                    });
                                    cancel?.({});
                                }
                            });
                        }}
                    >
                        <Icon component={otdrRun || stopRun ? resultDisabledSvg : resultSvg} />
                        {labelList.result}
                    </DebounceButton>
                </Space>
            </div>
            <div className={styles.content}>
                <Row gutter={24} className={styles.headerRow} ref={topRef}>
                    <Col span={14}>
                        <Card title={<div>OTDR Test Results</div>} style={{height: "100%"}}>
                            <div style={{height: "100%"}} ref={chartRef} />
                        </Card>
                    </Col>
                    <Col span={10}>
                        <Card title={<div>Data Comparison</div>} className={styles.cardBorder}>
                            <Table
                                style={{height: "100%"}}
                                dataSource={ABDataSource}
                                columns={ABColumns}
                                pagination={false}
                            />
                        </Card>
                    </Col>
                </Row>
                <Row gutter={24} className={styles.footerRow} ref={bottomRef}>
                    <Col span={14} style={{height: "100%"}}>
                        <Card title={<div>Event Data Table</div>} className={styles.cardBorder}>
                            <div style={{height: "100%"}}>
                                <Table
                                    dataSource={eventData}
                                    columns={eventColumns}
                                    scroll={{
                                        // eslint-disable-next-line no-unsafe-optional-chaining
                                        y: eventData.length > 0 && size?.height <= 811 ? size?.height - 206 : undefined
                                    }}
                                />
                            </div>
                        </Card>
                    </Col>
                    <Col span={10} style={{height: "100%"}}>
                        <Card title={<div>Parameters of this Test</div>} className={styles.cardBorder}>
                            <Table
                                // eslint-disable-next-line no-unsafe-optional-chaining
                                scroll={{y: !curDataSource.length || size?.height > 811 ? false : size?.height - 158}}
                                dataSource={curDataSource}
                                columns={curColumns}
                                pagination={false}
                            />
                        </Card>
                    </Col>
                </Row>
            </div>
            <ProcessTool processState={process} />
        </>
    );
};

export default OTDR;
