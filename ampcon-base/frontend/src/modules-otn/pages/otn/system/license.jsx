import {useEffect, useState} from "react";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import dayjs from "dayjs";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {openDBModalCreate} from "@/modules-otn/components/form/create_form_db";
import {message} from "antd";
import {editTableWriteIcon} from "@/modules-otn/pages/otn/device/device_icons";
import Icon from "@ant-design/icons";
import styles from "./license.module.scss";

const License = () => {
    const userRight = useUserRight();
    const [data, setData] = useState([]);

    const loadData = () => {
        // todo
        // getLicense().then(rs => {
        //     const data = rs?.data?.reverse() ?? [];
        //     if (data?.[0]?.state === false) {
        //         message.error("License expires").then();
        //     }
        //     setData(data);
        // });
    };

    useEffect(loadData, []);

    return (
        <div className={styles.container}>
            <CustomTable
                type="license"
                initTitle={null}
                scroll={false}
                initDataSource={data}
                refreshParent={loadData}
                buttons={[
                    {
                        label: "Update",
                        type: "primary",
                        icon: <Icon component={editTableWriteIcon} />,
                        disabled: userRight.disabled,
                        // eslint-disable-next-line no-unused-vars
                        onClick: _ => {
                            openDBModalCreate({
                                type: "register",
                                submit: async (values, defaultValue, diffValue, cancel, fail) => {
                                    // todo
                                    // const rs = await apiRegister(values);
                                    // if (rs.apiResult === "fail") {
                                    //     message.error(rs.apiMessage);
                                    //     fail?.(rs);
                                    // } else {
                                    //     message.success("Register succeeded");
                                    //     cancel?.(rs);
                                    //     loadData();
                                    // }
                                }
                            });
                        }
                    }
                ]}
                columnFormat={{
                    type: (state, data) => {
                        return state === 0 ? `Trial (${data.days} days)` : "Permanent";
                    },
                    "register-time": time => {
                        return dayjs(parseInt(time)).format("YYYY/MM/DD HH:mm:ss");
                    },
                    state: state => {
                        return state ? "Validity" : "Invalidity";
                    }
                }}
            />
        </div>
    );
};

export default License;
