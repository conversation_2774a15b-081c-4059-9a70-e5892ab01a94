import {useEffect, useMemo, useState} from "react";
import {Descriptions, Input, Select, Switch} from "antd";
import {sortArr, isEmptyObject} from "@/modules-otn/utils/util";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";

export default function NtpTemplateItem(props) {
    const {formRef, id, prefer, setPrefer, templates, usedTemplate, setUsedTemplate, record} = props;
    const oldConfig = useMemo(() => {
        let servers = record?.ntp?.servers?.server;
        if (servers && !Array.isArray(servers)) servers = [servers];
        const configInfo = servers?.at(id === "ntp-server1" ? 0 : 1);
        if (!servers || !configInfo) return {};
        return configInfo.config;
    }, [record]);
    const [serverInfo, setServerInfo] = useState(oldConfig);
    const disabledPrefer = useMemo(() => {
        if (id === "ntp-server2") return !serverInfo?.address || !serverInfo?.port;
    }, [serverInfo]);

    const templatesOptions = useMemo(() => {
        const options = [];
        const usedTemplateArr = Object.values(usedTemplate).filter(key => !["custom", "current"].includes(key));
        templates?.forEach(({name}) => {
            options.push({
                label: name,
                value: name,
                disabled: usedTemplateArr.includes(name)
            });
        });
        sortArr(options, ["label"]);
        if (!isEmptyObject(oldConfig)) options.unshift({label: gLabelList.current_config, value: "current"});
        options.unshift({label: gLabelList.custom_config, value: "custom"});

        return options;
    }, [templates, usedTemplate]);

    const defaultSelectTemplate = useMemo(() => {
        if (!isEmptyObject(oldConfig)) return "current";
        return "custom";
    }, [record]);

    const disabled = useMemo(
        () => serverInfo.name && !["custom", "current"].includes(serverInfo.name),
        [serverInfo, id]
    );

    useEffect(() => {
        if (formRef?.setFieldValue) {
            const {address, port} = serverInfo;
            const {name, ...restServerInfo} = serverInfo;
            formRef.setFieldValue(id, address && port ? restServerInfo : undefined);
        }
    }, [serverInfo]);

    return (
        <Descriptions bordered column={2}>
            <Descriptions.Item label={gLabelList.template} span={2}>
                <Select
                    options={templatesOptions}
                    style={{width: 200}}
                    placeholder="请选择模板或自定义类型"
                    bordered={false}
                    defaultValue={defaultSelectTemplate}
                    onChange={value => {
                        setUsedTemplate({...usedTemplate, [id]: value});
                        if (value === "custom") {
                            setServerInfo({name: value});
                            return;
                        }
                        if (value === "current") {
                            const _serverInfo = oldConfig;
                            _serverInfo.name = value;
                            setServerInfo(_serverInfo);
                            return;
                        }
                        // 模板数据
                        const templateData = templates.find(({name}) => name === value) ?? {};
                        setServerInfo(templateData);
                    }}
                />
            </Descriptions.Item>
            <Descriptions.Item label="IP Address">
                <Input
                    bordered={false}
                    disabled={disabled}
                    value={serverInfo.address}
                    placeholder="please input"
                    onChange={e => {
                        setServerInfo({...serverInfo, address: e.target.value});
                    }}
                />
            </Descriptions.Item>
            <Descriptions.Item label="Port">
                <Input
                    disabled={disabled}
                    placeholder="please input"
                    bordered={false}
                    value={serverInfo.port}
                    onChange={e => {
                        setServerInfo({...serverInfo, port: e.target.value});
                    }}
                />
            </Descriptions.Item>
            <Descriptions.Item label="Version">
                <Select
                    disabled={disabled}
                    placeholder="please select"
                    bordered={false}
                    options={["1", "2", "3", "4"].map(version => ({
                        value: version,
                        label: version
                    }))}
                    value={serverInfo.version}
                    onChange={value => {
                        setServerInfo({...serverInfo, version: value});
                    }}
                    style={{width: 200}}
                />
            </Descriptions.Item>
            <Descriptions.Item label="Prefer">
                <Switch
                    checked={prefer === id}
                    disabled={disabledPrefer}
                    onChange={value => {
                        // 切换server优先级
                        const otherServerKey = id === "ntp-server1" ? "ntp-server2" : "ntp-server1";
                        setPrefer(value ? id : otherServerKey);
                    }}
                />
            </Descriptions.Item>
        </Descriptions>
    );
}
