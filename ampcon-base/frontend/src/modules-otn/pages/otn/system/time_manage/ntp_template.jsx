import React, {useState} from "react";
import {useSelector} from "react-redux";
import {Input, InputNumber, Select, message, Form} from "antd";
import {useRequest} from "ahooks";
import Icon from "@ant-design/icons";

import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {middleModal} from "@/modules-otn/components/modal/custom_modal";
import {addSvg} from "@/utils/common/iconSvg";
import {apiEditNtpTemplate, apiAddNTPTemplate, apiGetNTPTemplates, apiDelNTPTemplate} from "./api";

export default function NTPTemplate() {
    const [dataSource, setDataSource] = useState([]);

    const {labelList} = useSelector(state => state.languageOTN);
    const userRight = useUserRight();

    const templateItems = [
        {
            label: gLabelList.name,
            name: "name",
            render: <Input style={{width: 280}} />,
            rules: [{required: true, message: gLabelList.empty_name}]
        },
        {
            label: gLabelList.address,
            name: "address",
            render: <Input style={{width: 280}} />,
            rules: [
                {required: true, message: gLabelList.entry_ipaddress},
                {
                    pattern: /^((2(5[0-5]|[0-4]\d))|[01]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[01]?\d{1,2})){3}$/,
                    message: gLabelList.ip_patter
                }
            ]
        },
        {
            label: gLabelList.port,
            name: "port",
            render: <InputNumber max={65535} min={0} step={1} style={{width: 280}} />,
            rules: [{required: true, message: gLabelList.entry_port}]
        },
        {
            label: gLabelList.version,
            name: "version",
            render: (
                <Select
                    options={["1", "2", "3", "4"].map(option => ({
                        value: option,
                        label: option
                    }))}
                    style={{width: 280}}
                />
            ),
            rules: [{required: true, message: gLabelList.empty_version}]
        }
    ];

    const {refresh, loading} = useRequest(apiGetNTPTemplates, {
        onSuccess(rs) {
            const {apiResult, data} = rs;
            if (apiResult === "fail") return;
            setDataSource(data);
        }
    });

    return (
        <CustomTable
            type="ntp_template"
            initTitle=""
            initDataSource={dataSource}
            loading={loading}
            refreshParent={refresh}
            scroll={false}
            buttons={[
                {
                    label: gLabelList.create,
                    disabled: userRight.disabled,
                    icon: <Icon component={addSvg} />,
                    type: "primary",
                    onClick: () => {
                        let formRef;
                        const bindForm = f => {
                            formRef = f;
                        };

                        const onFinish = values => {
                            modal.update({okButtonProps: {loading: true}});
                            apiAddNTPTemplate(values).then(rs => {
                                modal.update({okButtonProps: {loading: false}});
                                const {apiResult, apiMessage} = rs;
                                if (apiResult === "fail") {
                                    message.error(apiMessage).then();
                                    return;
                                }
                                message.success(gLabelList.operation_success).then();
                                modal.destroy();
                                refresh();
                            });
                        };

                        const modal = middleModal({
                            title: gLabelList.create_ntp_title,
                            content: (
                                <EditNtpTemplateForm items={templateItems} bindForm={bindForm} onFinish={onFinish} />
                            ),
                            // eslint-disable-next-line no-unused-vars
                            onOk: _ => {
                                formRef
                                    .validateFields()
                                    .then(() => {
                                        formRef.submit();
                                    })
                                    .catch(() => {});
                            }
                        });
                    }
                }
            ]}
            initRowOperation={[
                {
                    label: labelList.del,
                    confirm: {
                        title: labelList.delete_confirm
                    },
                    disabled: () => userRight.disabled,
                    async onClick() {
                        const {name} = {...this};
                        apiDelNTPTemplate({name}).then(rs => {
                            const {apiResult, apiMessage} = rs;
                            if (apiResult === "fail") {
                                message.error(apiMessage);
                                return;
                            }
                            message.success(gLabelList.delete_success);
                            refresh();
                        });
                    }
                },
                {
                    label: labelList.edit,
                    disabled: () => userRight.disabled,
                    async onClick() {
                        const record = {...this};
                        const onFinish = values => {
                            modal.update({okButtonProps: {loading: true}});
                            apiEditNtpTemplate(values).then(rs => {
                                modal.update({okButtonProps: {loading: false}});
                                const {apiResult, apiMessage} = rs;
                                if (apiResult === "fail") {
                                    message.error(apiMessage);
                                    return;
                                }
                                message.success(gLabelList.operation_success);
                                modal.destroy();
                                refresh();
                            });
                        };

                        let formRef;
                        const bindForm = f => {
                            formRef = f;
                        };

                        const modal = middleModal({
                            title: `${gLabelList.edit}`,
                            content: (
                                <EditNtpTemplateForm
                                    initialValues={record}
                                    onFinish={onFinish}
                                    bindForm={bindForm}
                                    items={templateItems.map(item => ({
                                        ...item,
                                        render: React.cloneElement(item.render, {disabled: item.name === "name"})
                                    }))}
                                />
                            ),
                            onOk: _ => {
                                formRef
                                    .validateFields()
                                    .then(() => {
                                        formRef.submit();
                                    })
                                    .catch(() => {});
                            }
                        });
                    }
                }
            ]}
        />
    );
}

const EditNtpTemplateForm = ({bindForm, items, initialValues, onFinish}) => {
    const [form] = Form.useForm();
    bindForm(form);

    return (
        <Form
            form={form}
            onFinish={onFinish}
            initialValues={initialValues}
            labelCol={{span: 8}}
            wrapperCol={{span: 14}}
            labelAlign="left"
            layout="horizontal"
            LabelWrap
            className="label-wrap"
        >
            {items.map((item, index) => {
                const {render, ...otherProps} = item;
                return (
                    // eslint-disable-next-line react/no-array-index-key
                    <Form.Item key={index} {...otherProps}>
                        {render}
                    </Form.Item>
                );
            })}
        </Form>
    );
};
