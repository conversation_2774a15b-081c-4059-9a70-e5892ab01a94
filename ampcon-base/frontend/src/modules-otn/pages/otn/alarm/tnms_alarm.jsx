import {useEffect, useState} from "react";
import {useSelector} from "react-redux";
import dayjs from "dayjs";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {objectGet} from "@/modules-otn/apis/api";
import styles from "./alarm.module.scss";

const TMNSAlarm = ({head}) => {
    const [data, setData] = useState([]);
    const {tnmsAlarms} = useSelector(state => state.notification);
    const {neNameMap} = useSelector(state => state.neName);
    const [showType, setShowType] = useState(0);

    const loadData = type => {
        const _type = type ?? showType;
        const alarmData = [];
        objectGet("nms:alarm", {}).then(rs => {
            rs.documents.map(item => {
                try {
                    let neName = neNameMap[item.value.ne_id];
                    if (!neName || neName === "") {
                        neName = item.value.ne_id;
                    }
                    if (
                        (_type === 0 && item.value.data["time-cleared"] === "0") ||
                        (_type !== 0 && item.value.data["time-cleared"] !== "0")
                    ) {
                        const v = item.value.data;
                        alarmData.push({
                            ...v,
                            key: item.id,
                            name: neName
                        });
                    }
                } catch (e) {
                    // console.log(e);
                }
            });
            setData(alarmData);
        });
    };

    useEffect(() => {
        loadData();
    }, [tnmsAlarms, neNameMap]);

    return (
        <CustomTable
            type={showType === 0 ? "tnmsAlarm" : "tnmsHistoryAlarm"}
            initTitle={showType === 0 ? "current_tnms_alarm" : "history_tnms_alarm"}
            initHead={head}
            initDataSource={data}
            refreshParent={loadData}
            buttons={[
                {
                    label: showType === 0 ? "history" : "current",
                    onClick() {
                        const _type = showType === 0 ? 1 : 0;
                        setShowType(_type);
                        loadData(_type);
                    }
                }
            ]}
            columnFormat={{
                // eslint-disable-next-line react/no-unstable-nested-components
                severity: severity => {
                    try {
                        if (severity) {
                            const _severity = severity.replace(/\S+:/g, "");
                            return (
                                <div className={styles["cell-severity"]}>
                                    <div
                                        className={`${styles["circle-common"]} ${
                                            styles[`alarm-${_severity.toLowerCase()}`]
                                        }`}
                                    />
                                    <span>{_severity}</span>
                                </div>
                            );
                        }
                    } catch (e) {
                        return severity;
                    }
                },
                "time-created": time => {
                    try {
                        return dayjs(time / 1000000).format("YYYY/MM/DD HH:mm:ss");
                    } catch (e) {
                        return time;
                    }
                },
                "time-cleared": time => {
                    try {
                        return dayjs(time / 1000000).format("YYYY/MM/DD HH:mm:ss");
                    } catch (e) {
                        return time;
                    }
                }
            }}
        />
    );
};
export default TMNSAlarm;
