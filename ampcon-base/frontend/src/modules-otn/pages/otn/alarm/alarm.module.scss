.alarm_ctn {
    margin: 10px 30px;

    &_header {
        display: flex;
        justify-content: space-between;
        margin: 10px 10px;
        border-bottom: 1px solid #f0f0f0;

        &_title {
            font-size: 16px;
            font-weight: 600;
        }
    }

    &_content {
        margin: 5px 10px;
    }
}

.alarm_tabs {
    margin: 0;
}

$alarm-critical-color: red;
$alarm-major-color: #ff9900;
$alarm-minor-color: #f3f300;
$alarm-warning-color: lightblue;

.alarm-critical {
    background-color: $alarm-critical-color;
}

.alarm-major {
    background-color: $alarm-major-color;
}

.alarm-minor {
    background-color: $alarm-minor-color;
}

.alarm-warning {
    background-color: $alarm-warning-color;
}

.cell-severity {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.circle-common {
    width: 1vw;
    height: 1vw;
    margin-right: 5px;
    border-radius: 50%;
}

.abbreviate-popover-item-label {
    font-weight: 600;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.antd_container {
    display: flex;
    padding-bottom: 32px;

    :global {
        .ant-form-item {
            margin-bottom: 0;
        }
        .ant-tag-cyan {
            color: #FFBB00;
            background: rgba(255,187,0,0.1);
            border: 1px solid #FFBB00;
        }
    }
}
