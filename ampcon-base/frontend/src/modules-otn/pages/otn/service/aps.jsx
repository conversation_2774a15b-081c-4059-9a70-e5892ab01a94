import {useSelector} from "react-redux";
import {useEffect, useState} from "react";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {objectGet} from "@/modules-otn/apis/api";
import {openModalEdit} from "@/modules-otn/components/form/edit_form";
import {openModalRpc} from "@/modules-otn/components/form/edit_rpc";
import useUserRight from "@/modules-otn/hooks/useUserRight";

const Aps = () => {
    const {labelList} = useSelector(state => state.languageOTN);
    const {tableFilter} = useSelector(state => state.map);
    const {dataChanged} = useSelector(state => state.notification);
    const {neNameMap, neTypeMap} = useSelector(state => state.neName);
    const [pgData, setPgData] = useState([]);
    const readyOnlyRight = useUserRight();

    const loadPgData = () => {
        try {
            let filter = {};
            if (tableFilter?.type === "NODE_NE") {
                filter = {ne_id: tableFilter.id};
            }

            objectGet("ne:5:aps-module", filter).then(rs => {
                const _data = [];
                rs.documents.forEach(async i => {
                    let found = true;
                    if (tableFilter?.type === "client") {
                        found = false;
                        if (tableFilter.provision.nodeList.length === 2) {
                            return false;
                        }
                        const endPointInfo = [
                            tableFilter.provision.nodeList[0]?.[0]?.param,
                            tableFilter.provision.nodeList.slice(-1)[0]?.[0]?.param
                        ];
                        endPointInfo.forEach(port => {
                            const keyStr = `${i.value.data.name.replace("APS", "PORT")}-`;
                            if (
                                port &&
                                port.ne_id === i.value.ne_id &&
                                port.card.startsWith("OLP") &&
                                (port.port?.left?.[0].indexOf?.(keyStr) > -1 ||
                                    port.port?.right?.[0].indexOf?.(keyStr)) > -1
                            ) {
                                found = true;
                                return false;
                            }
                        });
                    } else if (["ots", "oms", "och"].includes(tableFilter?.type) && tableFilter.servicePortList) {
                        found = false;
                        tableFilter.servicePortList.forEach(port => {
                            if (
                                port.ne_id === i.value.ne_id &&
                                port.card.startsWith("OLP") &&
                                port.ports[0].startsWith(i.value.data.name.replace("APS", "PORT"))
                            ) {
                                found = true;
                                return false;
                            }
                        });
                    }
                    if (found) {
                        _data.push({
                            ne_id: i.value.ne_id,
                            ne_name: neNameMap[i.value.ne_id],
                            name: i.value.data.name,
                            ...i.value.data.state
                        });
                    }
                });
                _data.sort((a, b) => a.ne_name.localeCompare(b.ne_name, "ZH-CN", {numeric: true}));
                setPgData(_data);
            });
        } catch (e) {
            // console.log(e);
        }
    };

    const loadData = () => {
        loadPgData();
    };

    useEffect(() => {
        loadData(tableFilter);
    }, [tableFilter]);

    useEffect(() => {
        if (dataChanged.data?.type === "aps") {
            loadData(tableFilter);
        }
    }, [dataChanged]);

    return (
        <CustomTable
            type="ne:5:aps-module"
            initHead={false}
            initDataSource={pgData}
            initRowOperation={[
                {
                    label: labelList.edit,
                    disabled: () => {
                        return readyOnlyRight.disabled;
                    },
                    async onClick() {
                        const that = this;
                        openModalEdit(
                            "aps",
                            that.name,
                            neTypeMap[that.ne_id],
                            that.name,
                            that.ne_id,
                            null,
                            null,
                            readyOnlyRight.disabled
                        );
                    }
                },
                {
                    label: labelList.switch_connection,
                    disabled: () => {
                        return readyOnlyRight.disabled;
                    },
                    async onClick() {
                        const that = this;
                        openModalRpc(
                            that.ne_id,
                            "switch-olp",
                            neTypeMap[that.ne_id],
                            `Switch OLP (${that.name})`,
                            {name: that.name},
                            ["name"],
                            null,
                            null,
                            null,
                            readyOnlyRight.disabled
                        );
                    }
                }
            ]}
        />
    );
};

export default Aps;
