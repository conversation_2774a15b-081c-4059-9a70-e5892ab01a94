.container {
    flex: 1;
    display: flex;
    flex-direction: column;
    // overflow: hidden;
    background-color: #ffffff;

    &_tag_green {
    font-size: 14px;
    background: rgba(43, 193, 116, 0.1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #2BC174;
    color: #2BC174
  }

  &_tag_red {
    font-size: 14px;
    color: #F53F3F;
    background: #fff0f6;
    border-color: #ffadd2;
  }

    .up {
        display: flex;
        height: 350px;

        .tree {
            width: 350px;
            border: 1px solid #ddd;
            border-radius: 2px;
            overflow: auto;
            padding: 24px 24px 0;
        }

        .view {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 2px;
            margin-left: 20px;
            text-align: center;
            background: #F8FAFB;
        }
    }

    .down {
        flex: 1;
        display: flex;
        margin-top: 20px;
        padding: 24px;
        padding-top: 0;
        //margin-bottom: 20px;

        border-radius: 2px;
        border: 1px solid #E7E7E7;
        overflow: hidden;

        :global {
            .ant-tabs-tabpane {
                padding: 0;
            }

            .ant-tabs-nav-list .ant-tabs-tab:first-child {
                padding-left: 0px !important;
            }
        }

        .lldp {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
    }

    .service_optics_down {
        border: none;
        padding: 0;
    }

    :global {
        .ant-tabs-content, .ant-tabs-tabpane {
            overflow: hidden;
        }
    }
}

:global {
    .ant-tabs-content  {
        .ant-tabs-nav {
            background: none;
            margin-bottom: 24px;
            // margin-left: -4px;
        }

        .ant-tabs-ink-bar {
            display: none;
        }

        .ant-tabs-tab {
            background: none !important;
            border: none !important;
            padding: 24px 16px 0 16px !important;

            .ant-tabs-tab-btn {
                padding-bottom: 8px;
                border-bottom: 3px solid #FFF;
            }
        }

        .ant-tabs-tab-active {
            .ant-tabs-tab-btn {
                border-bottom: 3px solid var(--primary-color);
            }
        }

        .ant-tabs-nav::before {
            display: none;
        }
    }

    .service_layer1 {
        .ant-tabs-nav::before {
            display: none;
        }

        .ant-tabs-nav-wrap {
            background: #FFF;
        }

        .ant-tabs-nav-list {
            margin-bottom: 8px;

            .ant-tabs-tab-btn {
                padding-bottom: 8px;
                border-bottom: 3px solid #FFF;
            }

            .ant-tabs-tab-active {
                .ant-tabs-tab-btn {
                    border-bottom: 3px solid var(--primary-color);
                }
            }

            .ant-tabs-ink-bar {
                display: none;
            }

            .ant-tabs-tab {
                background: none !important;
                border: none !important;
                padding: 24px 16px 0 !important;
            }
        }
    }
}

.ant-form .ant-form-horizontal {
    margin-bottom: 8px !important;
    margin-top: 8px !important;
}