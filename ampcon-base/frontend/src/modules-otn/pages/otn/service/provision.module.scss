.container {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    height: 100%;
    width: 100%;
    //min-height: 600px;

    :global {
        .ant-form-item-label {
            flex: 1;
            text-align: start;
        }

        .ant-form-item-control {
            flex: none;
            width: 260px;
        }
    }
}

.chart {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.discover_container {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.discover {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.node_div {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.input_div {
    //display: inline-block;
}

.input_div a {
    display: inline-block;
    width: 30px;
    color: #2bbf2b;
}

.node_dup_div {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.add_node {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0 30px 30px 10px;
}

.combineDiv {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: flex-start;
    padding: 0 30px 30px 10px;
}

.combineNode {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding-left: 1px;
}

.add_bt {
    background: #cddaee;
    opacity: 0.2;
}

.add_bt div {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 40px;
}

.add_bt div:hover {
    background: #2bbf2b;
}

.add_bt:hover {
    opacity: 1;
}

.node {
    //border: solid 1px #087bee;
    display: flex;
    flex-direction: column;
    //align-items: center;
    background: #F8FAFB;
    width: 399px;
    //height: 100px;

    &_content {
        padding: 24px;

        &_type {
            font-weight: 700;
        }

        &_elm {
            padding-top: 24px;
            padding-bottom: 24px;
        }
    }
}

.select {
    width: 200px;
}

.buttons {
    height: 30px;
    padding: 0 6px;
    display: flex;
    margin-bottom: 20px;
    //justify-content: space-between;
}

.form_item {
    margin: 24px 0px;
    width: 100%
}