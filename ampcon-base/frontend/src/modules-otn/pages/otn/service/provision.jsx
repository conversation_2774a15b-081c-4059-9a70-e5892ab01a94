import {useEffect, useRef, useState, forwardRef, useImperativeHandle} from "react";
import {Button, Select, message, Form, Input, Cascader} from "antd";
import {MinusOutlined, PlusOutlined, RetweetOutlined} from "@ant-design/icons";
import {useSelector} from "react-redux";
import {
    arrayAdd,
    createFiber,
    getPortUsed,
    netconfByXML,
    netconfChange,
    objectAdd,
    objectGet,
    objectEdit
} from "@/modules-otn/apis/api";
import {combinePort} from "@/modules-otn/config/table_config_5";
import {convertToArray} from "@/modules-otn/utils/util";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";
import {
    getDefaultSignalType,
    getSignalType,
    updatePortSignalType
} from "@/modules-otn/pages/otn/service/resource_manager";
import styles from "./provision.module.scss";

const Provision = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        onOk: async () => {
            return await saveFiber();
        },
        onReset: () => {
            const newValue = {...values};
            clearFormData(dataList, newValue);
            setValues(newValue);
            for (let i = 0; i < menuConfig.length; i++) {
                const f = menuConfig[i].child.filter(s => s.key === modelActive);
                if (f.length > 0) {
                    setDataList(f[0].model);
                    break;
                }
            }
        }
    }));

    const {serviceType, saveSuccess, setAddProvisionWating} = props;

    const {labelList} = useSelector(state => state.languageOTN);
    const {neNameMap} = useSelector(state => state.neName);
    const [form] = Form.useForm();
    const [groupData, setGroupData] = useState([]);
    const [selectData, setSelectData] = useState({});
    const [values, setValues] = useState({});
    const valuesRef = useRef(values);
    const [loading, setLoading] = useState("");
    const [channel, setChannel] = useState(191400000);
    const [channelDisabled, setChannelDisabled] = useState(true);
    const saveValues = v => {
        setValues(v);
        valuesRef.current = v;
    };
    const Mux48CardType = ["OMD48ECM", "MUX48"];
    // const editType = {
    //     MUX: ["MUX", "TFF"],
    //     TFF: ["MUX", "TFF"],
    //     OA: ["OA", "WSS"],
    //     WSS: ["OA", "WSS"]
    // };

    const editBT = {
        OA: [
            {
                type: "add"
            }
        ],
        WSS: [
            {
                type: "add",
                enabled: index => {
                    return index === 0;
                },
                target: () => {
                    return createNewNode({type: "OLA"});
                }
            }
        ],
        OLA: [
            {
                type: "add"
            },
            {type: "del"}
        ],
        LINECARD: [{type: "del"}],
        OTS_B_FIRST: [
            {
                type: "add"
            },
            {
                type: "switch"
            }
        ],
        OTS_B: [
            {
                type: "add"
            },
            {
                type: "del"
            },
            {
                type: "switch"
            }
        ],
        OMS: [
            {
                type: "add",
                enabled: v => {
                    return v?.card?.startsWith("TFF") || v?.card?.startsWith("MUX");
                }
            },
            {
                type: "switch"
            }
        ],
        OMS_B_FIRST: [
            {
                type: "add",
                enabled: v => {
                    return v?.card?.startsWith("TFF") || v?.card?.startsWith("MUX");
                }
            },
            {type: "del"},
            {
                type: "switch"
            }
        ],
        OCH: [
            {
                type: "switch"
            }
        ]
    };

    const model = {
        model1: [
            {
                type: ["WSS", "OA"],
                data: {
                    port: {enabled: false}
                },
                key: "model1_wss1",
                listenerEffect: ["model1_wss2"],
                buttons: [
                    {
                        type: "add",
                        target: () => {
                            return createNewNode({
                                type: "OLA"
                            });
                        }
                    }
                ]
            },
            {
                type: ["WSS", "OA"],
                data: {
                    port: {enabled: false}
                },
                dire: "r",
                key: "model1_wss2"
            }
        ],
        model2: [
            {
                type: ["MUX", "TFF"],
                data: {
                    port: {enabled: false}
                },
                key: "model2_mux1",
                leftButtons: [
                    {
                        type: "add",
                        enabled: v => {
                            return v?.card?.startsWith("TFF");
                        },
                        target: () => {
                            return createNewNode({
                                type: "TFF",
                                buttons: {add: false},
                                leftButtons: {del: false},
                                data: {
                                    port: {enabled: false}
                                }
                            });
                        }
                    }
                ]
            },
            {
                type: "OTS",
                title: "OTS A",
                key: "model2_ots1_a"
            },
            {
                type: "linkSpan",
                key: "model2_ots1_link"
            },
            {
                type: "OTS",
                title: "OTS Z",
                buttons: editBT.OTS_B,
                key: "model2_ots1_b",
                dire: "r"
            },
            {
                type: ["MUX", "TFF"],
                key: "model2_mux2",
                data: {
                    port: {enabled: false}
                },
                buttons: [
                    {
                        type: "add",
                        enabled: v => {
                            return v?.card?.startsWith("TFF");
                        },
                        target: () => {
                            return createNewNode({
                                type: "TFF",
                                data: {
                                    port: {enabled: false}
                                },
                                dire: "r"
                            });
                        }
                    }
                ],
                dire: "r"
            }
        ],
        model3: [
            {
                type: ["MUX", "TFF"],
                key: "model3_mux1",
                data: {
                    port: {enabled: false}
                },
                leftButtons: [
                    {
                        type: "add",
                        enabled: v => {
                            return v?.card?.startsWith("TFF");
                        },
                        target: () => {
                            return createNewNode({
                                type: "TFF",
                                buttons: {add: false},
                                leftButtons: {del: false},
                                data: {
                                    port: {enabled: false}
                                }
                            });
                        }
                    }
                ]
            },
            {
                type: "OLP",
                key: "model3_olp1"
            },
            [
                [
                    {
                        type: "OTS",
                        title: "OTS A",
                        key: "model3_ots1_a"
                    },
                    {
                        type: "linkSpan",
                        key: "model3_ots1_link"
                    },
                    {
                        type: "OTS",
                        title: "OTS Z",
                        buttons: editBT.OTS_B,
                        key: "model3_ots1_b",
                        dire: "r"
                    }
                ],
                [
                    {
                        type: "OTS",
                        title: "OTS A",
                        key: "model3_ots2_a"
                    },
                    {
                        type: "linkSpan",
                        key: "model3_ots2_link"
                    },
                    {
                        type: "OTS",
                        title: "OTS Z",
                        buttons: editBT.OTS_B,
                        key: "model3_ots2_b",
                        dire: "r"
                    }
                ]
            ],
            {
                type: "OLP",
                key: "model3_olp2",
                dire: "r"
            },
            {
                type: ["MUX", "TFF"],
                key: "model3_mux2",
                data: {
                    port: {enabled: false}
                },
                buttons: [
                    {
                        type: "add",
                        enabled: v => {
                            return v?.card?.startsWith("TFF");
                        },
                        target: () => {
                            return createNewNode({
                                type: "TFF",
                                data: {
                                    port: {enabled: false}
                                },
                                dire: "r"
                            });
                        }
                    }
                ],
                dire: "r"
            }
        ],
        model4: [
            {
                type: "LINECARD",
                key: "model4_linecard1",
                addListener: ["group"],
                listenerEffect: ["model4_mux_a"]
            },
            {
                type: "OMS",
                title: "OMS A",
                subType: ["MUX", "TFF"],
                key: "model4_mux_a",
                addListener: ["oms", "ne", "card", "port"]
            },
            {
                type: "linkSpan",
                key: "model4_mux_link"
            },
            {
                type: "OMS",
                title: "OMS Z",
                subType: ["MUX", "TFF"],
                key: "model4_mux_b",
                buttons: editBT.OMS_B_FIRST,
                addListener: ["oms", "ne", "card", "port"],
                dire: "r"
            },
            {
                type: "LINECARD",
                key: "model4_linecard2",
                addListener: ["group"],
                listenerEffect: ["model4_mux_b"]
            }
        ],
        model5: [
            {
                type: "LINECARD",
                key: "model5_linecard1",
                addListener: ["group"],
                listenerEffect: ["model5_olp1"]
            },
            {
                type: "OLP",
                key: "model5_olp1"
            },
            [
                [
                    {
                        type: "OMS",
                        title: "OMS A",
                        subType: ["MUX", "TFF"],
                        key: "model5_mux1_a",
                        addListener: ["oms", "ne", "card", "port"]
                    },
                    {
                        type: "linkSpan",
                        key: "model5_mux1_link"
                    },
                    {
                        type: "OMS",
                        title: "OMS Z",
                        subType: ["MUX", "TFF"],
                        key: "model5_mux1_b",
                        buttons: editBT.OMS_B_FIRST,
                        addListener: ["oms", "ne", "card", "port"],
                        dire: "r"
                    }
                ],
                [
                    {
                        type: "OMS",
                        title: "OMS A",
                        subType: ["MUX", "TFF"],
                        key: "model5_mux2_a",
                        addListener: ["oms", "ne", "card", "port"]
                    },
                    {
                        type: "linkSpan",
                        key: "model5_mux2_link"
                    },
                    {
                        type: "OMS",
                        title: "OMS Z",
                        subType: ["MUX", "TFF"],
                        key: "model5_mux2_b",
                        buttons: editBT.OMS_B_FIRST,
                        addListener: ["oms", "ne", "card", "port"],
                        dire: "r"
                    }
                ]
            ],
            {
                type: "OLP",
                key: "model5_olp2",
                dire: "r"
            },
            {
                type: "LINECARD",
                key: "model5_linecard2",
                addListener: ["group"],
                listenerEffect: ["model5_olp2"]
            }
        ],
        model6: [
            {
                type: "OMS",
                title: "OMS A",
                subType: ["MUX", "TFF"],
                key: "model6_mux1_a",
                addListener: ["oms", "ne", "card", "port"]
            },
            {
                type: "linkSpan",
                key: "model6_mux1_link"
            },
            {
                type: "OMS",
                title: "OMS Z",
                subType: ["MUX", "TFF"],
                key: "model6_mux1_b",
                buttons: [
                    {
                        type: "add"
                    },
                    {
                        type: "switch"
                    }
                ],
                addListener: ["oms", "ne", "card", "port"],
                dire: "r"
            },
            {
                type: "OMS",
                title: "OMS A",
                subType: ["MUX", "TFF"],
                key: "model6_mux2_a",
                addListener: ["oms", "ne", "card", "port"]
            },
            {
                type: "linkSpan",
                key: "model6_mux2_link"
            },
            {
                type: "OMS",
                title: "OMS Z",
                subType: ["MUX", "TFF"],
                key: "model6_mux2_b",
                buttons: editBT.OMS,
                addListener: ["oms", "ne", "card", "port"],
                dire: "r"
            }
        ],
        model7: [
            {
                type: "OLP",
                key: "model7_olp1"
            },
            [
                [
                    {
                        type: "OCH",
                        title: "OCH A",
                        key: "model7_och1_a"
                    },
                    {
                        type: "linkSpan",
                        key: "model7_och1_link"
                    },
                    {
                        type: "OCH",
                        title: "OCH Z",
                        key: "model7_och1_b",
                        buttons: editBT.OCH,
                        dire: "r"
                    }
                ],
                [
                    {
                        type: "OCH",
                        title: "OCH A",
                        key: "model7_och2_a"
                    },
                    {
                        type: "linkSpan",
                        key: "model7_och2_link"
                    },
                    {
                        type: "OCH",
                        title: "OCH Z",
                        key: "model7_och2_b",
                        buttons: editBT.OCH,
                        dire: "r"
                    }
                ]
            ],
            {
                type: "OLP",
                key: "model7_olp2",
                dire: "r"
            }
        ],
        model11: [
            {
                type: ["WSS", "OA"],
                data: {
                    port: {enabled: false}
                },
                key: "model11_wss1",
                listenerEffect: ["model11_wss2"]
            },
            {
                type: "OLP",
                key: "model11_olp1"
            },
            {
                type: "OLP",
                key: "model11_olp2",
                dire: "r"
            },
            {
                type: ["WSS", "OA"],
                data: {
                    port: {enabled: false}
                },
                dire: "r",
                key: "model11_wss2"
            }
        ],
        model12: [
            {
                type: "OCH",
                title: "OCH A",
                key: "model12_och1_a",
                split: true
            },
            {
                type: "linkSpan",
                key: "model12_och1_link"
            },
            {
                type: "OCH",
                title: "OCH Z",
                key: "model12_och1_b",
                dire: "r",
                split: true
            }
        ]
    };

    const menuConfig = [
        {
            name: "ots",
            title: `OTS${labelList.service_title}`,
            child: [
                {
                    model: model.model1,
                    key: "ots_ots_no_protection",
                    title: labelList.no_protection
                },
                {
                    model: model.model11,
                    key: "ots_ots_protection",
                    title: labelList.protection
                }
            ]
        },
        {
            name: "oms",
            title: `OMS${labelList.service_title}`,
            child: [
                {
                    model: model.model2,
                    key: "oms_no_protection",
                    title: labelList.no_protection
                },
                {
                    model: model.model3,
                    key: "oms_protection",
                    title: labelList.protection
                }
                // ,
                // {
                //     model: model.model6,
                //     key: "oms_ring",
                //     title: labelList.ring
                // }
            ]
        },
        {
            name: "och",
            title: `OCH${labelList.service_title}`,
            child: [
                {
                    model: model.model4,
                    key: "och_no_protection",
                    title: labelList.no_protection
                },
                {
                    model: model.model5,
                    key: "och_protection",
                    title: labelList.protection
                }
            ]
        },
        {
            name: "client",
            title: labelList.client_service,
            child: [
                {
                    model: model.model12,
                    key: "client_no_protection",
                    title: labelList.no_protection
                },
                {
                    model: model.model7,
                    key: "client_protection",
                    title: labelList.protection
                }
            ]
        }
    ];

    const defaultModel = "ots";

    const [dataList, setDataList] = useState(model.model1);
    const [modelType, setModelType] = useState(defaultModel);
    const defaultModelAction = menuConfig.filter(item => item.name === defaultModel)[0].child[0].key;
    const [modelActive, setModelAction] = useState(defaultModelAction);
    const [configCache, setConfigCache] = useState({});
    const createNewNode = cfg => {
        const {
            type,
            addListener,
            listenerEffect,
            dire,
            nextConfig,
            buttons,
            disabled,
            title,
            addByKey,
            defineKey,
            keyCondition,
            defineButtons,
            leftButtons,
            data
        } = cfg;
        const obj = {
            type,
            title,
            key:
                defineKey ||
                `${convertToArray(type).join("_").toLowerCase()}_${Math.ceil(Math.random() * 100000)}${
                    keyCondition ? `_${keyCondition}` : ""
                }`,
            disabled,
            addByKey,
            addListener,
            listenerEffect,
            dire,
            data
        };
        if (defineButtons) {
            obj.buttons = defineButtons;
        } else if (type !== "linkSpan" && (buttons || buttons === undefined)) {
            setBt(obj, buttons, nextConfig, cfg, "buttons");
        }
        if (leftButtons) {
            setBt(obj, leftButtons, nextConfig, cfg, "leftButtons");
        }
        return obj;
    };

    const setBt = (obj, buttons, nextConfig, cfg, btType) => {
        obj[btType] = [
            {
                type: "add",
                enabled: buttons === undefined || buttons.add !== false,
                target: () => {
                    return createNewNode(nextConfig || cfg);
                }
            },
            {type: "del", enabled: buttons === undefined || buttons.del !== false}
        ];
    };

    const cardConfig = {
        linkSpan: {},
        OTS: {
            output: {
                in: {},
                out: {}
            },
            data: {
                ots: {},
                port: {
                    data: data => {
                        return data.filter(item => item.value.indexOf("AD") > -1);
                    },
                    enabled: card => {
                        return card.startsWith("WSS");
                    }
                }
            },
            listenerAll: {
                ots: async (name, cfg, v) => {
                    await resetNodeGroup(cfg, name, "ots", v);
                }
            }
        },
        NE: {
            data: {
                port: {}
            }
        },
        LINECARD: {
            output: {
                in: neInfo => {
                    // line
                    return `${neInfo.card.replace("LINECARD", "PORT")}-L1`;
                },
                out: neInfo => {
                    // line
                    return `${neInfo.card.replace("LINECARD", "PORT")}-L1`;
                }
            }
        },
        OMS: {
            data: {
                oms: {},
                port: {
                    data: (data, cfg) => {
                        if (cfg.key === "ne")
                            return data.filter(item => item.value.indexOf("CH") > -1 || item.value.indexOf("MUX") > -1);
                        return data.filter(item => item.value.indexOf("CH") > -1);
                    }
                }
            },
            output: {
                in: {}, // CH
                out: neInfo => {
                    // MUX
                    return `${neInfo.card.replace("MUX", "PORT")}-MUX`;
                }
            },
            listenerAll: {
                oms: async (value, cfg, v) => {
                    await resetNodeGroup(cfg, value, "oms", v);
                },
                ne: () => {},
                card: () => {},
                port: async (value, cfg, v) => {
                    const tffLinkList = [];
                    await checkOCHInvalid(dataList, null, false, tffLinkList);
                    if (modelActive === "och_protection") {
                        const bSideKey =
                            cfg.key.indexOf("mux1") > -1
                                ? cfg.key.replace("mux1", "mux2")
                                : cfg.key.replace("mux2", "mux1");
                        const currentOppoSideKey = getOppoSideKey(cfg.key);
                        const newV = {...values};
                        let updated = false;
                        if (values[currentOppoSideKey]?.port) {
                            updated = true;
                            await updateRelatePort(value.split("CH").pop(), cfg.key, currentOppoSideKey, newV);
                        }
                        if (values[bSideKey]?.port) {
                            updated = true;
                            const oppoSideKey = getOppoSideKey(bSideKey);
                            await updateRelatePort(value.split("CH").pop(), cfg.key, bSideKey, newV);
                            await updateRelatePort(value.split("CH").pop(), cfg.key, oppoSideKey, newV);
                        }
                        if (updated) {
                            saveValues(newV);
                        }
                    }

                    const muxType = await getCardVendorType(v[cfg.key].ne, v[cfg.key].card);
                    const chanelIndex = value.split("CH")[1];
                    if (muxType && chanelIndex) {
                        let frequency;
                        if (Mux48CardType.includes(muxType)) {
                            frequency = 191400 + (chanelIndex - 1) * 100;
                        } else if (muxType === "MUX96") {
                            frequency = 191350 + (chanelIndex - 1) * 50;
                        } else if (muxType.startsWith("TFF")) {
                            frequency = parseInt(`19${parseInt(muxType.substring(3, 5)) + (chanelIndex - 1)}00`);
                        }
                        const channel = frequency * 1000;
                        setChannel(channel);
                        dataList.forEach(i => {
                            if (i?.type === "LINECARD") {
                                form.setFieldValue(`${i.key}_channel`, (channel / 1000000).toFixed(2));
                            }
                        });
                    }

                    await setMUXCard(cfg, v, tffLinkList);
                }
            }
        },
        OCH: {
            data: {
                och: {},
                port: {}
            },
            output: {
                in: {}, // CH
                out: neInfo => {
                    // MUX
                    return `${neInfo.card.replace("MUX", "PORT")}-MUX`;
                }
            },
            listenerAll: {
                och: async (value, cfg, v) => {
                    await resetNodeGroup(cfg, value, "och", v);
                },
                ne: () => {},
                card: () => {},
                port: async (value, cfg, v) => {
                    const oppoKey = getOppoSideKey(cfg.key);
                    const newPorts = await combinePort(values?.[oppoKey]?.ne, values?.[oppoKey]?.card, "C");
                    const portKey = value.split("-").pop();
                    const f = newPorts.find(i => i.value.endsWith(portKey));
                    const newValue = {...v};
                    const defaultSignalType_A = await getDefaultSignalType(v[cfg.key].ne, v[cfg.key].port);
                    form.setFieldValue(`${cfg.key}_signal-type`, defaultSignalType_A);
                    newValue[cfg.key]["signal-type"] = defaultSignalType_A;
                    newValue[cfg.key]["default-signal-type"] = defaultSignalType_A;
                    if (f) {
                        const oppoPort = f.value;
                        form.setFieldValue(`${oppoKey}_port`, oppoPort);
                        newValue[oppoKey].port = oppoPort;
                        const newDataList = [...dataList];
                        editNode(newDataList, oppoKey, {disabled: {och: true, ne: true, card: true, port: true}});
                        const defaultSignalType_Z = await getDefaultSignalType(v[oppoKey].ne, v[oppoKey].port);
                        form.setFieldValue(`${oppoKey}_signal-type`, defaultSignalType_Z);
                        newValue[oppoKey]["signal-type"] = defaultSignalType_Z;
                        newValue[oppoKey]["default-signal-type"] = defaultSignalType_Z;
                    } else {
                        message.error("Unable to find a suitable port on the opposite");
                    }
                    setValues(newValue);
                },
                "signal-type": async () => {
                    // console.log(value, cfg, v);
                    // const oppoKey = getOppoSideKey(cfg.key);
                    // const r = (await getSignalType(ne, port)).map(s => ({...s, title: s.label}));
                }
            }
        },
        MUX: {
            data: {
                port: {
                    data: (data, cfg) => {
                        if (cfg.key === "ne")
                            return data.filter(item => item.value.indexOf("CH") > -1 || item.value.indexOf("MUX") > -1);
                        return data.filter(item => item.value.indexOf("CH") > -1);
                    }
                }
            },
            output: {
                in: {}, // CH
                out: neInfo => {
                    // MUX
                    return `${neInfo.card.replace("MUX", "PORT")}-MUX`;
                }
            }
        },
        TFF: {
            data: {
                port: {
                    data: (data, cfg) => {
                        if (cfg.key === "ne") {
                            return data.filter(item => item.value.indexOf("CH") > -1 || item.value.indexOf("MUX") > -1);
                        }
                        return data.filter(item => item.value.indexOf("CH") > -1);
                    }
                }
            },
            output: {
                in: neInfo => {
                    return `${neInfo.card.replace("TFF", "PORT")}-PT1`;
                },
                out: neInfo => {
                    return `${neInfo.card.replace("TFF", "PORT")}-MUX`;
                }
            }
        },
        OLP: {
            data: {
                port: {
                    data: data => {
                        return data
                            .filter(item => item.value.indexOf("APSC") > -1)
                            .map(item => {
                                const l = item.value.replace("-APSC", "");
                                return {label: l, value: item.value, title: l, used: item.used};
                            });
                    }
                },
                apsc: {}
            },
            output: {
                in: {}, // APSC
                out: (neInfo, lineIndex) => {
                    return neInfo.port.replace("APSC", lineIndex + 1 === neInfo.apsp ? "APSP" : "APSS");
                } // APSP  APSS
            },
            listenerAll: {
                apsp: (value, cfg, v) => {
                    const _ov = value === 1 ? 2 : 1;
                    form.setFieldValue(`${cfg.key}_apss`, _ov);
                    v[cfg.key].apss = _ov;
                },
                apss: (value, cfg, v) => {
                    const _ov = value === 1 ? 2 : 1;
                    form.setFieldValue(`${cfg.key}_apsp`, _ov);
                    v[cfg.key].apsp = _ov;
                }
            }
        },
        OA: {
            output: {
                in: (neInfo, lineIndex, dire) => {
                    return `${neInfo.card.replace("OA", "PORT")}-${dire ? "PAOUT" : "BAIN"}`;
                }, // BAIN PAOUT
                out: (neInfo, lineIndex, dire) => {
                    return `${neInfo.card.replace("OA", "PORT")}-${dire ? "PAIN" : "BAOUT"}`;
                } // PAIN BAOUT
            }
        },
        OLA: {
            data: {
                port: {}
            },
            output: {
                in: neInfo => {
                    return `${neInfo.port.replace("OUT", "IN")}`;
                },
                out: neInfo => {
                    return `${neInfo.port.replace("IN", "OUT")}`;
                }
            }
        },
        WSS: {
            data: {
                port: {
                    data: (data, cfg) => {
                        if (cfg.key === "ne") return data;
                        return data.filter(item => item.value.indexOf("IN") < 0);
                    }
                }
            },
            output: {
                in: {}, // AD
                out: (neInfo, lineIndex, dire) => {
                    return `${neInfo.card.replace("WSS", "PORT")}-${dire ? "PAIN" : "BAOUT"}`;
                } // BAOUT PAIN
            }
        }
    };

    // ------------------------以上为配置-------------------------------------------------

    const changeType = v => {
        const defaultValue = menuConfig.filter(item => item.name === v)[0].child[0];
        setModelType(v);
        setModelAction(defaultValue.key);
        setDataList(configCache[defaultValue.key] || defaultValue.model);
    };

    useEffect(() => {
        objectGet("nms:group", {}).then(rs => {
            setGroupData(rs.documents);
        });
        if (serviceType) {
            changeType(serviceType);
        }
    }, []);

    // const parseEditFiber = (fiber, newValues, index) => {
    //     if (fiber instanceof Array) {
    //         fiber.map((item, i) => {
    //             parseEditFiber(item, newValues, i);
    //         });
    //     } else {
    //         const type = fiber.card.split("-")[0];
    //         fiber.type = editType[type] || [type];
    //         fiber.key = `${fiber.id}`;
    //         // ne
    //         form.setFieldValue(`${fiber.id}_ne`, fiber.ne_id);
    //         if (!newValues[fiber.id]) {
    //             newValues[fiber.id] = {};
    //         }
    //         newValues[fiber.id].ne = fiber.ne_id;
    //         // card
    //         form.setFieldValue(`${fiber.id}_card`, fiber.card);
    //         newValues[fiber.id].card = fiber.card;
    //         // port
    //         if (supportNode(fiber, fiber.id, "port")) {
    //             form.setFieldValue(`${fiber.id}_port`, fiber.portPeerBI);
    //             newValues[fiber.id].port = fiber.portPeerBI;
    //         }
    //         const bts = editBT[type];
    //         if (bts) {
    //             const newBts = [];
    //             bts.map(item => {
    //                 if (item.enabled) {
    //                     if (item.enabled(index)) {
    //                         newBts.push(item);
    //                     }
    //                 } else {
    //                     newBts.push(item);
    //                 }
    //             });
    //             if (newBts.length > 0) {
    //                 fiber.buttons = newBts;
    //             }
    //         }
    //     }
    // };

    const setGroup = (value, cfg, newValues) => {
        cfg.listenerEffect.map(item => {
            if (!newValues[item]) {
                newValues[item] = {};
            }
            if (!newValues[item].group) {
                newValues[item].group = value;
                form.setFieldValue(`${item}_group`, value);
            }
        });
    };

    const setElementValue = (data, key, cfgKey, newValue) => {
        if (data[key] !== undefined && data[key] !== null) {
            newValue[key] = data[key];
            form.setFieldValue(`${cfgKey}_${key}`, data[key]);
        }
    };

    const setNodeData = (key, data, values, nodeName, type, savePort) => {
        const newData = {...data, ne: data.ne_id, [type]: nodeName};
        if (!values[key]) {
            values[key] = {};
        }
        const newValue = {...values[key]};
        const editType = ["group", "oms", "ots", "och", "ne", "card"];
        if (savePort) {
            editType.push("port");
        }
        editType.map(item => {
            setElementValue(newData, item, key, newValue);
        });
        values[key] = newValue;
    };

    const getFit_MUX_TFF = (currentNeInfo, ttfSide) => {
        const muxChanel = currentNeInfo.port.split("CH")[1];
        const frequencyType = 14 + (muxChanel - 1);
        for (let i = 0; i < ttfSide.length; i++) {
            if (
                frequencyType >= ttfSide[i].tffType.substring(3, 5) &&
                frequencyType <= ttfSide[i].tffType.substring(5)
            ) {
                return {
                    ...ttfSide[i],
                    port: `${ttfSide[i].card.replace("TFF", "PORT")}-CH${
                        frequencyType - ttfSide[i].tffType.substring(3, 5) + 1
                    }`
                };
            }
        }
        message.warning(labelList.mux_not_fit_tff);
        // frequency = parseInt(`19${parseInt(muxType.substring(3, 5)) + (chanelIndex - 1)}00`);
    };

    const getFit_TFF_MUX = (currentNeInfo, ttfSide, muxSide) => {
        const currentTTFType = ttfSide.filter(
            item => item.ne === currentNeInfo.ne && item.card === currentNeInfo.card
        )[0].tffType;
        const ttfChanelIndex = currentNeInfo.port.split("CH")[1];
        const ttfFrequency = parseInt(`${parseInt(currentTTFType.substring(3, 5)) + (ttfChanelIndex - 1)}`);
        const muxChannel = ttfFrequency - 14 + 1;
        return `${muxSide.card.replace("MUX", "PORT")}-CH${muxChannel}`;
    };

    const getFit_TFF_TFF = (currentNeInfo, currentSideTFF, otherSideTFF) => {
        const currentTTFType = currentSideTFF.filter(
            item => item.ne === currentNeInfo.ne && item.card === currentNeInfo.card
        )[0].tffType;
        const otherSideInfo = otherSideTFF.filter(item => item.tffType === currentTTFType);
        if (otherSideInfo.length === 0) {
            message.warning(labelList.tff_not_fit_tff);
        }
        return otherSideInfo;
    };

    const getNEGroup = async ne_id => {
        const groupID = (await objectGet("config:ne", {ne_id, type: "5"})).documents[0].value.group;
        if (groupID) {
            const gs = groupData.filter(item => item.id === groupID);
            if (gs.length > 0) {
                return gs[0].value.name;
            }
        }
        return "";
    };

    // const getRelateNodeKey = (cfgList, currentKey, preNode, nextNode) => {
    //     cfgList.map((item, index) => {
    //         if (item instanceof Array) {
    //             getRelateNodeKey(item, currentKey, preNode, nextNode);
    //         }
    //         if (item.key === currentKey) {
    //             nextNode = cfgList?.[index + 1];
    //             console.log(preNode, nextNode);
    //         } else {
    //             preNode = item;
    //         }
    //     });
    // };

    const resetNodeGroup = async (cfg, nodeName, type, v) => {
        const currentKey = cfg.key;
        // getRelateNodeKey(dataList, currentKey);
        const name = nodeName || values[currentKey]?.[type];
        if (!name) return;
        const _data = (await objectGet("nms:provision", {name})).documents[0];
        const rs = _data.value;
        const aSide = rs.a;
        const zSide = rs.z;
        aSide.group = await getNEGroup(aSide.ne_id);
        zSide.group = await getNEGroup(zSide.ne_id);
        const newDataList = [...dataList];
        const isTFF = (currentKey.endsWith("_a") && rs.tffA) || (currentKey.endsWith("_b") && rs.tffZ);
        const key = getOppoSideKey(currentKey);
        editNode(newDataList, currentKey, {disabled: {ne: !isTFF, card: !isTFF}});
        if (currentKey.endsWith("_b")) {
            editNode(newDataList, currentKey, {side: "b"});
            editNode(newDataList, key, {side: "b"});
        }
        setOptions(cfg, "ne", [aSide.ne_id]);
        setNodeData(cfg.key, aSide, v, name, type);
        setOptions({key}, "ne", [zSide.ne_id]);
        setNodeData(key, zSide, v, name, type);
        if (isTFF) {
            editNode(newDataList, key, {disabled: {group: true, [type]: true}, dbKey: _data.id});
        } else {
            editNode(newDataList, key, {disabled: {group: true, [type]: true, ne: true, card: true}, dbKey: _data.id});
        }
        // pocm-1116 start
        if (v[cfg.key]?.port) {
            form.resetFields([`${cfg.key}_port`]);
            v[cfg.key].port = "";
        }
        if (v[key]?.port) {
            form.resetFields([`${key}_port`]);
            v[key].port = "";
        }
        // pocm-1116 end
        saveValues(v);
        setDataList(newDataList);
    };

    const updateRelatePort = async (currentPortIndex, currentKey, otherKey, newV) => {
        let otherSidePortIndex = 1;
        if (
            (values[currentKey].card.startsWith("MUX") && values[otherKey].card.startsWith("MUX")) ||
            (values[currentKey].card.startsWith("TFF") && values[otherKey].card.startsWith("TFF"))
        ) {
            otherSidePortIndex = currentPortIndex;
        } else if (values[currentKey].card.startsWith("MUX")) {
            const tffType = await getCardVendorType(values[otherKey].ne, values[otherKey].card);
            const channelPort = 14 + (currentPortIndex - 1);
            otherSidePortIndex = channelPort - tffType.substring(3, 5) + 1;
        } else {
            const tffType = await getCardVendorType(values[currentKey].ne, values[currentKey].card);
            const ttfChanelIndex = currentPortIndex;
            const ttfFrequency = parseInt(`${parseInt(tffType.substring(3, 5)) + (ttfChanelIndex - 1)}`);
            otherSidePortIndex = ttfFrequency - 14 + 1;
        }
        const otherSidePort = `${values[otherKey].port.substring(
            0,
            values[otherKey].port.lastIndexOf("-")
        )}-CH${otherSidePortIndex}`;
        form.setFieldValue(`${otherKey}_port`, otherSidePort);
        newV[otherKey].port = otherSidePort;
    };

    const setMUXCard = async (cfg, v, _l) => {
        const currentKey = cfg.key;
        const {oms, port} = valuesRef.current[currentKey];
        const newCfg = [...dataList];
        const key = getOppoSideKey(currentKey);

        if (_l.length > 0) {
            await resetPTPort(_l, newCfg, valuesRef.current, key, true, currentKey);
            return;
        }
        let rs;

        if (values[currentKey].card.startsWith("MUX") && values[key].card.startsWith("TFF")) {
            const _oms = (await objectGet("nms:provision", {type: "oms", name: oms})).documents[0].value;
            const ttfSide = key.endsWith("_a") ? _oms.tffA : _oms.tffZ;
            const ttf = getFit_MUX_TFF(values[currentKey], ttfSide);
            if (ttf) {
                rs = {
                    apiResult: true,
                    oms,
                    ne_id: ttf.ne,
                    card: ttf.card,
                    port: ttf.port
                };
            } else {
                rs = {
                    apiResult: true,
                    port: ""
                };
            }
        } else if (values[currentKey].card.startsWith("TFF") && values[key].card.startsWith("MUX")) {
            const _oms = (await objectGet("nms:provision", {type: "oms", name: oms})).documents[0].value;
            const currentSideTFF = currentKey.endsWith("_a") ? _oms.tffA : _oms.tffZ;
            const muxPort = getFit_TFF_MUX(values[currentKey], currentSideTFF, values[key]);
            rs = {
                apiResult: true,
                port: muxPort
            };
        } else if (values[currentKey].card.startsWith("TFF") && values[key].card.startsWith("TFF")) {
            const _oms = (await objectGet("nms:provision", {type: "oms", name: oms})).documents[0].value;
            const currentSideTFF = currentKey.endsWith("_a") ? _oms.tffA : _oms.tffZ;
            const otherSideTFF = key.endsWith("_a") ? _oms.tffA : _oms.tffZ;
            // eslint-disable-next-line prefer-destructuring
            const otherSideInfo = getFit_TFF_TFF(values[currentKey], currentSideTFF, otherSideTFF);
            if (otherSideInfo.length > 0) {
                rs = {
                    apiResult: true,
                    oms,
                    ne_id: otherSideInfo[0].ne,
                    card: otherSideInfo[0].card,
                    port: `${otherSideInfo[0].card.replace("TFF", "PORT")}-${port.split("-").pop()}`
                };
            } else {
                rs = {
                    apiResult: true,
                    port: ""
                };
            }
        } else {
            rs = {
                apiResult: true,
                port: `${values[key].card.replace("MUX", "PORT")}-${port.split("-").pop()}`
            };
        }
        if (rs.apiResult === "fail") {
            message.error(rs.apiMessage());
            return;
        }

        if (_l.includes(key)) {
            rs.port = rs.port.replace(/-CH\d+/, "-PT1");
        }
        setNodeData(key, rs, v, oms, "oms", true);
        saveValues(v);
        await resetPTPort(_l, newCfg, valuesRef.current, key);
    };

    const resetPTLinkPort = async (cfgList, ptList, newCfg, currentKey, v, channel) => {
        for (let i = 0; i < cfgList.length; i++) {
            const item = cfgList[i];
            if (item instanceof Array) {
                // eslint-disable-next-line no-unreachable-loop
                for (let j = 0; j < item.length; j++) {
                    await resetPTLinkPort(item[j], ptList, newCfg, currentKey, v, channel);
                }
                return;
            }
            if (item.type === "OMS") {
                const _key = item.key;
                if (!ptList.includes(_key)) {
                    editNode(newCfg, getOppoSideKey(_key), {
                        disabled: {group: true, oms: true, ne: true, card: false, port: false}
                    });
                    const p = await getPortByFrequency(v, _key, channel);
                    if (!p) {
                        message.error(labelList.no_same_ch_port.format(neNameMap[v[_key].ne], v[_key].card));
                        v[_key].port = "";
                        form.resetFields([`${_key}_port`]);
                    } else {
                        v[_key].port = p;
                        form.setFieldValue(`${_key}_port`, p);
                    }
                } else {
                    editNode(newCfg, _key, {disabled: {group: true, oms: true, ne: true, card: false, port: false}});
                    editNode(newCfg, currentKey, {
                        disabled: {group: true, oms: true, ne: true, card: false, port: false}
                    });
                    if (!v[_key]?.port?.endsWith("-PT1")) {
                        v[_key].port = `${v[_key].card.replace("TFF", "PORT")}-PT1`;
                        form.setFieldValue(`${_key}_port`, v[_key].port);
                    }
                }
            }
        }
    };

    const getFrequency = async (v, key) => {
        const muxType = await getCardVendorType(v[key].ne, v[key].card);
        const chanelIndex = v[key].port.split("CH")[1];
        if (Mux48CardType.includes(muxType)) {
            return 191400 + (chanelIndex - 1) * 100;
        }
        if (muxType === "MUX96") {
            return 191350 + (chanelIndex - 1) * 50;
        }
        if (muxType.startsWith("TFF")) {
            return parseInt(`19${parseInt(muxType.substring(3, 5)) + (chanelIndex - 1)}00`);
        }
    };

    const getPortByFrequency = async (v, key, frequency) => {
        const muxType = await getCardVendorType(v[key].ne, v[key].card);
        let ch;
        if (Mux48CardType.includes(muxType)) {
            ch = (frequency - 191400) / 100 + 1;
            if (ch < 1 || ch > 48) {
                return false;
            }
        } else if (muxType === "MUX96") {
            return false;
        } else if (muxType.startsWith("TFF")) {
            const fc = frequency.toString();
            ch = parseInt(fc.substring(2, fc.length - 2)) - parseInt(muxType.substring(3, 5)) + 1;
            if (ch < 1 || ch > 4 || parseInt(`19${parseInt(muxType.substring(3, 5)) + (ch - 1)}00`) !== frequency) {
                return false;
            }
        }
        const _temp = v[key].card.split("-");
        return `PORT-${_temp[1]}-${_temp[2]}-CH${ch}`;
    };

    const resetPTPort = async (_l, newCfg, v, key, isPTPort, currentKey) => {
        if (isPTPort) {
            editNode(newCfg, key, {disabled: {group: true, oms: true, ne: true, card: false, port: false}});
        } else {
            editNode(newCfg, key, {disabled: true});
        }
        if (isPTPort) {
            if (v[currentKey]?.port?.endsWith("-PT1")) {
                if (_l.length > 0) {
                    _l.map(i => {
                        editNode(newCfg, i, {disabled: {group: true, oms: true, ne: true, card: false, port: false}});
                        editNode(newCfg, currentKey, {
                            disabled: {group: true, oms: true, ne: true, card: false, port: false}
                        });
                        if (!v[i]?.port?.endsWith("-PT1")) {
                            v[i].port = `${v[i].card.replace("TFF", "PORT")}-PT1`;
                            form.setFieldValue(`${i}_port`, v[i].port);
                        }
                    });
                }
            } else {
                const channel = await getFrequency(v, currentKey);
                await resetPTLinkPort(dataList, _l, newCfg, currentKey, v, channel);
            }
        }
        setDataList(newCfg);
        saveValues(v);
    };

    const getPortInfo = (neCfg, type, lineIndex) => {
        let _dire = neCfg?.dire;
        if (_dire && typeof _dire === "function") {
            _dire = _dire(values, neCfg);
        }
        let side = type === "A" ? "out" : "in";
        if (_dire === "r") {
            side = side === "in" ? "out" : "in";
        }
        const cardType = getCardType(neCfg);
        if (typeof cardConfig[cardType].output[side] === "function") {
            return {port: cardConfig[cardType].output[side](values[neCfg.key], lineIndex, _dire, type)};
        }
    };

    const createConnection = (ne1Cfg, ne2Cfg, lineIndex, twoWay = true) => {
        if (ne1Cfg.type === "linkSpan" || ne2Cfg.type === "linkSpan") {
            return;
        }
        const neA = {...values[ne1Cfg.key], ...getPortInfo(ne1Cfg, "A", lineIndex)};
        const neB = {...values[ne2Cfg.key], ...getPortInfo(ne2Cfg, "B", lineIndex)};
        if (
            neA.card.startsWith("TFF") &&
            neA.port.indexOf("CH") > -1 &&
            neB.card.startsWith("TFF") &&
            neB.port.indexOf("CH") > -1
        ) {
            neA.port = `${neA.card.replace("TFF", "PORT")}-PT1`;
            neB.port = `${neB.card.replace("TFF", "PORT")}-PT1`;
        }
        const conn = {
            source_ne: neA.ne,
            source_card: neA.card,
            source_port: neA.port,
            dest_ne: neB.ne,
            dest_card: neB.card,
            dest_port: neB.port
        };
        if (
            (conn.source_port.endsWith("APSS") && conn.dest_port.endsWith("APSS")) ||
            (conn.source_port.endsWith("APSP") && conn.dest_port.endsWith("APSP"))
        ) {
            if (twoWay) {
                return [
                    conn,
                    {
                        ...conn,
                        source_port: conn.source_port.endsWith("APSS")
                            ? conn.source_port.replace("APSS", "APSP")
                            : conn.source_port.replace("APSP", "APSS"),
                        dest_port: conn.dest_port.endsWith("APSS")
                            ? conn.dest_port.replace("APSS", "APSP")
                            : conn.dest_port.replace("APSP", "APSS")
                    }
                ];
            }
        }
        return [conn];
    };

    const addNewConnections = (fiberList, newConnections) => {
        newConnections?.map?.(i => {
            fiberList.push(i);
        });
    };

    const createFiberInfo = (fiberList, dataList, listNextNE, lineIndex) => {
        dataList.forEach((currentNE, index) => {
            if (index < dataList.length - 1) {
                const nextNE = dataList[index + 1];
                if (currentNE instanceof Array) {
                    currentNE.forEach((item, index2) => {
                        if (item.length > 0) {
                            createFiberInfo(fiberList, item, nextNE, index2);
                        }
                    });
                } else if (nextNE instanceof Array) {
                    nextNE.forEach((item, index3) => {
                        if (item.length > 0) {
                            addNewConnections(fiberList, createConnection(currentNE, item[0], index3));
                        } else {
                            addNewConnections(
                                fiberList,
                                createConnection(currentNE, dataList[index + 2], index3, false)
                            );
                        }
                    });
                } else {
                    addNewConnections(fiberList, createConnection(currentNE, nextNE, lineIndex));
                }
            } else if (listNextNE) {
                // 分组最后一个，如果父组有后一个，要做连接
                addNewConnections(fiberList, createConnection(currentNE, listNextNE, lineIndex));
            }
        });
    };

    const execSaveFiber = async () => {
        const fiberList = [];
        let successFiber = [];
        createFiberInfo(fiberList, dataList);
        const copyFiberList = [...fiberList];
        let createSuccess = true;
        while (createSuccess && fiberList.length > 0) {
            const item = fiberList.shift();
            if (item) {
                try {
                    const s = successFiber.reduce((p, c) => {
                        if (!c?.exist && c.ne === item.source_ne && c.index > p) {
                            return c.index;
                        }
                        return 0;
                    }, 0);
                    if (s) {
                        item.source_index = s + 1;
                    }
                    if (item.source_ne !== item.dest_ne) {
                        const d = successFiber.reduce((p, c) => {
                            if (!c?.exist && c.ne === item.dest_ne && c.index > p) {
                                return c.index;
                            }
                            return 0;
                        }, 0);
                        if (d) {
                            item.desc_index = d + 1;
                        }
                    }
                    // eslint-disable-next-line no-loop-func
                    await createFiber(item).then(rs => {
                        if (rs.apiResult !== "complete") {
                            createSuccess = false;
                            message.error(labelList.save_failed).then();
                        }
                        successFiber = successFiber.concat(rs.fiberList);
                    });
                } catch (e) {
                    createSuccess = false;
                    // eslint-disable-next-line no-console
                    console.log(e);
                }
            }
        }
        return {result: createSuccess, fiberList: copyFiberList, successFiber};
    };

    const getAllOCHDatas = async (dataList, ochDatas) => {
        for (let i = 0; i < dataList.length; i++) {
            const item = dataList[i];
            if (item instanceof Array) {
                for (let j = 0; j < item.length; j++) {
                    await getAllOCHDatas(item[j], ochDatas);
                }
            } else if (values[item.key]) {
                if (item.type === "OCH") {
                    ochDatas.push(values[item.key]);
                }
            }
        }
    };

    const getAllNodes = async (dataList, nes, otss, ochs, ochDatas) => {
        const subOtss = [];
        for (let i = 0; i < dataList.length; i++) {
            const item = dataList[i];
            if (item instanceof Array) {
                for (let j = 0; j < item.length; j++) {
                    await getAllNodes(item[j], nes, otss, ochs, ochDatas);
                }
            } else if (values[item.key]) {
                if (
                    item.type === "OTS" &&
                    subOtss.filter(subItem => subItem.ots === values[item.key].ots).length === 0
                ) {
                    const otsA = (await objectGet("nms:provision", {name: values[item.key].ots})).documents[0];
                    subOtss.push({
                        ots: otsA.id,
                        dire:
                            values[item.key].ne === otsA.value.a.ne_id && values[item.key].card === otsA.value.a.card
                                ? 1
                                : -1
                    });
                }
                if (item.type === "OCH") {
                    const ochA = (await objectGet("nms:provision", {name: values[item.key].och})).documents[0];
                    ochs.push(ochA.id);
                    ochDatas.push(values[item.key]);
                }
                // const {ne, card, port} = values[item.key];
                // nes.push({ne, card, port});
                nes.push(values[item.key]);
            }
        }
        if (subOtss.length > 0) {
            otss.push(subOtss);
        }
    };

    const saveTFFInfo = async (key, tffList) => {
        const neInfo = values[key];
        if (neInfo.card.startsWith("TFF")) {
            const tffType = await getCardVendorType(neInfo.ne, neInfo.card);
            if (checkDup(tffList, tffType)) {
                return false;
            }
            delete neInfo.group;
            tffList.push({...neInfo, tffType});
            return true;
        }
        return false;
    };

    const findTFF = async () => {
        const startTFF = [];
        for (let i = 1; i < dataList.length; i++) {
            if (!(await saveTFFInfo(dataList[i].key, startTFF))) {
                break;
            }
        }
        const endTFF = [];
        for (let i = dataList.length - 2; i >= 0; i--) {
            if (!(await saveTFFInfo(dataList[i].key, endTFF))) {
                break;
            }
        }
        return {start: startTFF, end: endTFF};
    };

    const updateCardWithTTF = async (cfg, cards) => {
        const bSideKey = getOppoSideKey(cfg.key);
        if (cfg.type === "OMS" && values[cfg.key].card.startsWith("TFF") && values[bSideKey].card.startsWith("TFF")) {
            const oms = (await objectGet("nms:provision", {name: values[cfg.key].oms})).documents[0].value;
            let tff = cfg.key.endsWith("_a") ? oms.tffZ : oms.tffA;
            let currentTFF = cfg.key.endsWith("_a") ? oms.tffA : oms.tffZ;
            if (cfg.side) {
                const temp = [...tff];
                tff = [...currentTFF];
                currentTFF = temp;
            }
            cards.map(card => {
                const currentTTFType = currentTFF.filter(
                    item => item.ne === values[cfg.key].ne && item.card === card.value
                )[0].tffType;
                if (tff.filter(item => item.tffType === currentTTFType).length > 0) {
                    delete card.disabled;
                } else {
                    card.disabled = true;
                }
            });
        }
    };
    const getOppoSideKey = key => {
        return key.replace(key.endsWith("_a") ? "_a" : "_b", key.endsWith("_a") ? "_b" : "_a");
    };

    // const checkPortsInvalid = async (currentKey, bSideKey) => {
    //     const {ne: bSideNE, card: bSideCard, port: bSidePort} = values[bSideKey] ?? {};

    //     if (!bSideCard || !bSidePort) {
    //         return true;
    //     }
    //     const {ne: aSideNE, card: aSideCard, port: aSidePort} = values[currentKey] ?? {};
    //     const aSideType = await getCardVendorType(aSideNE, aSideCard);
    //     const bSideType = await getCardVendorType(bSideNE, bSideCard);
    //     if (
    //         ((bSideCard.startsWith("MUX") && aSideCard.startsWith("MUX")) ||
    //             (bSideCard.startsWith("TFF") && aSideCard.startsWith("TFF"))) &&
    //         aSideType !== bSideType
    //     ) {
    //         return false;
    //     }

    //     if (
    //         (bSideType === "MUX96" && aSideType.startsWith("TFF")) ||
    //         (aSideType === "MUX96" && bSideType.startsWith("TFF"))
    //     ) {
    //         return false;
    //     }

    //     if (
    //         ((bSideCard.startsWith("MUX") && aSideCard.startsWith("MUX")) ||
    //             (bSideCard.startsWith("TFF") && aSideCard.startsWith("TFF"))) &&
    //         bSideType === aSideType
    //     ) {
    //         return aSidePort === bSidePort;
    //     }

    //     if (bSideType === "OMD48ECM" && aSideType.startsWith("TFF")) {
    //         const bSideChannel = parseInt(bSidePort.split("CH")[1]);
    //         const ttfChanelIndex = aSidePort.split("CH")[1];
    //         const ttfFrequency = parseInt(`${parseInt(aSideType.substring(3, 5)) + (ttfChanelIndex - 1)}`);
    //         const muxChannel = ttfFrequency - 14 + 1;
    //         return muxChannel === bSideChannel;
    //     }
    //     if (aSideType === "OMD48ECM" && bSideType.startsWith("TFF")) {
    //         const ttfChanelIndex = parseInt(bSidePort.split("CH")[1]);
    //         const ttfFrequency = parseInt(`${parseInt(bSideType.substring(3, 5)) + (ttfChanelIndex - 1)}`);
    //         const muxChannel = ttfFrequency - 14 + 1;
    //         return muxChannel === parseInt(aSidePort.split("CH")[1]);
    //     }
    //     return false;
    // };

    const all_OMS_is_MUX = (list, notMux) => {
        list.forEach(i => {
            if (i instanceof Array) {
                all_OMS_is_MUX(i, notMux);
            } else if (i.type === "OMS") {
                if (values[i.key]?.card && !values[i.key].card.startsWith("MUX")) {
                    notMux.push(values[i.key].card);
                }
            }
        });
    };

    const updatePortForFrequency = async (cfg, ports, tffLink, tffLinkList) => {
        if (cfg.type === "OMS") {
            let checkSameFrequencyAfterSelected = true;
            if (values[cfg.key]?.port) {
                checkSameFrequencyAfterSelected = false;
            }
            const notMUXCard = [];
            all_OMS_is_MUX(dataList, notMUXCard);
            if (checkSameFrequencyAfterSelected && notMUXCard.length === 0) {
                const selectPorts = Object.values(values).filter(i => i.oms && i.port);
                if (selectPorts.length > 0) {
                    const selectPort = selectPorts[0].port.split("-").pop();
                    ports.map(item => {
                        if (item.value.endsWith(selectPort)) {
                            delete item.disabled;
                        } else {
                            item.disabled = true;
                        }
                    });
                    return;
                }
            }
            // 查找TTF合适的port
            const bSideKey = getOppoSideKey(cfg.key);
            if (values[cfg.key].card.startsWith("MUX") && values[bSideKey].card.startsWith("TFF")) {
                const oms = (await objectGet("nms:provision", {name: values[cfg.key].oms})).documents[0].value;
                const ttf = oms.tffA.length > 0 ? oms.tffA : oms.tffZ;
                const channelList = [];
                ttf.map(item => {
                    const start = item.tffType.substring(3, 5) - 14 + 1;
                    for (let i = 0; i < 4; i++) {
                        channelList.push(start + i);
                    }
                });
                ports.map(item => {
                    if (!channelList.includes(parseInt(item.value.split("CH")[1]))) {
                        if (!tffLink) {
                            item.disabled = true;
                        } else {
                            delete item.disabled;
                        }
                    } else if (!tffLink) {
                        delete item.disabled;
                    } else {
                        item.disabled = true;
                    }
                });
            } else if (values[cfg.key].card.startsWith("TFF") && values[bSideKey].card.startsWith("TFF")) {
                const oms = (await objectGet("nms:provision", {name: values[cfg.key].oms})).documents[0].value;
                let tff = cfg.key.endsWith("_a") ? oms.tffZ : oms.tffA;
                let currentTFF = cfg.key.endsWith("_a") ? oms.tffA : oms.tffZ;
                if (cfg.side) {
                    const temp = [...tff];
                    tff = [...currentTFF];
                    currentTFF = temp;
                }
                const currentTTFType = currentTFF?.filter(
                    item => item.ne === values[cfg.key].ne && item.card === values[cfg.key].card
                )[0].tffType;
                if (tff.filter(item => item.tffType === currentTTFType).length > 0) {
                    if (!tffLink) {
                        ports.map(port => {
                            delete port.disabled;
                        });
                    } else {
                        ports.map(port => {
                            port.disabled = true;
                        });
                    }
                } else if (!tffLink) {
                    ports.map(port => {
                        port.disabled = true;
                    });
                } else {
                    ports.map(port => {
                        delete port.disabled;
                    });
                }
            }
            // 带保护的OCH上下OMS PORT频率一样
            if (modelActive === "och_protection") {
                let bSideKey =
                    cfg.key.indexOf("mux1") > -1 ? cfg.key.replace("mux1", "mux2") : cfg.key.replace("mux2", "mux1");

                if (tffLink && cfg.key.startsWith("oms_")) {
                    if (
                        valuesRef.current?.model5_mux1_a?.card?.startsWith("TFF") &&
                        !tffLinkList.includes("model5_mux1_a")
                    ) {
                        bSideKey = "model5_mux1_a";
                    } else if (
                        valuesRef.current?.model5_mux2_a?.card?.startsWith("TFF") &&
                        !tffLinkList.includes("model5_mux2_a")
                    ) {
                        bSideKey = "model5_mux2_a";
                    }
                }
                if (tffLinkList && tffLinkList.includes(bSideKey)) {
                    bSideKey = getOppoSideKey(bSideKey);
                }
                const {ne: bSideNE, card: bSideCard, port: bSidePort} = valuesRef.current[bSideKey] ?? {};

                if (!bSideCard) {
                    return;
                }

                const {ne: aSideNE, card: aSideCard, port: aSidePort} = values[cfg.key] ?? {};

                const aSideType = await getCardVendorType(aSideNE, aSideCard);
                const bSideType = await getCardVendorType(bSideNE, bSideCard);
                if (
                    ((bSideCard.startsWith("MUX") && aSideCard.startsWith("MUX")) ||
                        (bSideCard.startsWith("OMD") && aSideCard.startsWith("OMD")) ||
                        (bSideCard.startsWith("TFF") && aSideCard.startsWith("TFF"))) &&
                    aSideType !== bSideType
                ) {
                    ports.map(item => {
                        item.disabled = true;
                    });
                    return;
                }

                if (
                    (bSideType === "MUX96" && aSideType.startsWith("TFF")) ||
                    (aSideType === "MUX96" && bSideType.startsWith("TFF"))
                ) {
                    ports.map(item => {
                        item.disabled = true;
                    });
                    return;
                }
                if (!bSidePort || aSidePort) {
                    if ((aSideCard.startsWith("MUX") || aSideCard.startsWith("OMD")) && bSideCard.startsWith("TFF")) {
                        const channelList = [];
                        const start = bSideType.substring(3, 5) - 14 + 1;
                        for (let i = 0; i < 4; i++) {
                            channelList.push(start + i);
                        }
                        ports.map(item => {
                            if (!channelList.includes(parseInt(item.value.split("CH")[1]))) {
                                item.disabled = true;
                            }
                        });
                    }
                    return;
                }
                if (
                    ((bSideCard.startsWith("MUX") && aSideCard.startsWith("MUX")) ||
                        (bSideCard.startsWith("OMD") && aSideCard.startsWith("OMD")) ||
                        (bSideCard.startsWith("TFF") && aSideCard.startsWith("TFF"))) &&
                    bSideType === aSideType
                ) {
                    const bSideChannel = parseInt(bSidePort.split("CH")[1]);
                    ports.map(item => {
                        if (parseInt(item.value.split("CH")[1]) !== bSideChannel) {
                            item.disabled = true;
                        }
                    });
                    return;
                }

                if (Mux48CardType.includes(bSideType) && aSideType.startsWith("TFF")) {
                    const bSideChannel = parseInt(bSidePort.split("CH")[1]);
                    ports.map(item => {
                        const ttfChanelIndex = item.value.split("CH")[1];
                        const ttfFrequency = parseInt(`${parseInt(aSideType.substring(3, 5)) + (ttfChanelIndex - 1)}`);
                        const muxChannel = ttfFrequency - 14 + 1;
                        if (muxChannel !== bSideChannel) {
                            item.disabled = true;
                        }
                    });
                    return;
                }
                if (Mux48CardType.includes(aSideType) && bSideType.startsWith("TFF")) {
                    const ttfChanelIndex = bSidePort.split("CH")[1];
                    const ttfFrequency = parseInt(`${parseInt(bSideType.substring(3, 5)) + (ttfChanelIndex - 1)}`);
                    const muxChannel = ttfFrequency - 14 + 1;
                    ports.map(item => {
                        if (muxChannel !== parseInt(item.value.split("CH")[1])) {
                            item.disabled = true;
                        }
                    });
                }
            }
        }
    };

    const checkDup = (list, type) => {
        if (list.filter(item => item.tffType === type).length > 0) {
            message.error(labelList.mult_ttf.format(type)).then();
            return true;
        }
        return false;
    };

    const checkDirtyData = async dataConfig => {
        for (let j = 0; j < dataConfig.length; j++) {
            const item = dataConfig[j];
            if (item instanceof Array) {
                const r = await checkDirtyData(item);
                if (r === false) {
                    return r;
                }
            }
            if (values[dataConfig[j].key]?.ne) {
                const _ne = await objectGet("", {DBKey: `config:ne:${values[dataConfig[j].key].ne}`});
                if (_ne.documents[0].runState === 0) {
                    message.error(labelList.ne_is_lost.format(_ne.documents[0].value.name));
                    return false;
                }
            }
            if (dataConfig[j].dbKey) {
                const rs = await objectGet("nms:provision", {DBKey: dataConfig[j].dbKey}, null, false);
                if (rs?.apiMessage) {
                    message
                        .error(
                            `${dataConfig[j].type}${values[dataConfig[j].key][dataConfig[j].type.toLowerCase()]}${
                                labelList.data_has_change
                            }`
                        )
                        .then();
                    return false;
                }
            }
        }
    };

    const getCardVendorType = async (ne, card) => {
        const type = (
            await objectGet("ne:5:component", {
                ne_id: ne,
                name: card
            })
        ).documents[0].value.data.state["vendor-type-preconf"];
        return type;
    };

    // const checkOCHInvalid = async (cfgList, preNode, msg, list) => {
    //     // bug fix: need check the channel for all oms.
    //     for (let i = 0; i < cfgList.length; i++) {
    //         const item = cfgList[i];
    //         if (item instanceof Array) {
    //             // eslint-disable-next-line no-unreachable-loop
    //             for (let j = 0; j < item.length; j++) {
    //                 preNode = null;
    //                 await checkOCHInvalid(item[j], preNode, msg, list);
    //             }
    //         }
    //         if (item.type === "OMS" && preNode?.type === "OMS") {
    //             try {
    //                 const preValue = valuesRef.current[preNode.key];
    //                 const currentValue = valuesRef.current[item.key];
    //                 if (preValue.card.startsWith("TFF") && currentValue.card.startsWith("TFF")) {
    //                     const preType = await getCardVendorType(preValue.ne, preValue.card);
    //
    //                     const currentType = await getCardVendorType(currentValue.ne, currentValue.card);
    //                     if (preType !== currentType) {
    //                         if (msg) {
    //                             message.error(labelList.och_tff_link_type.format(preValue.oms, currentValue.oms));
    //                             return false;
    //                         }
    //                     } else {
    //                         list.push(item.key);
    //                         list.push(preNode.key);
    //                         if (msg) {
    //                             return true;
    //                         }
    //                     }
    //                 }
    //                 if (preValue.card.startsWith("MUX") && currentValue.card.startsWith("MUX")) {
    //                     const preType = await getCardVendorType(preValue.ne, preValue.card);
    //                     const currentType = await getCardVendorType(currentValue.ne, currentValue.card);
    //                     if (preType !== currentType) {
    //                         message.error(labelList.och_mux_link_type.format(preValue.oms, currentValue.oms));
    //                         return false;
    //                     }
    //                     return true;
    //                 }
    //                 if (msg) {
    //                     message.error(labelList.och_tff_link_card.format(preValue.oms, currentValue.oms));
    //                     return false;
    //                 }
    //             } catch (e) {
    //                 //
    //             }
    //         }
    //         preNode = item;
    //     }
    // };

    const getAllOMS = (cfg, OMSList) => {
        cfg.forEach(i => {
            if (i instanceof Array) {
                getAllOMS(i, OMSList);
            } else if (i.type === "OMS" && values[i.key]) {
                OMSList.push(values[i.key]);
            }
        });
    };

    const checkOCHInvalid = async cfgList => {
        const OMSList = [];
        getAllOMS(cfgList, OMSList);
        const selectedNEs = [];
        const unSelectedNES = [];
        const neTypeObj = {};
        OMSList.forEach(i => {
            if (i.port) {
                selectedNEs.push(i);
            } else {
                unSelectedNES.push(i);
            }
        });
        if (selectedNEs.length > 0) {
            for (let i = 0; i < selectedNEs.length; i++) {
                const selectedNE = selectedNEs[i];
                const currentCardType =
                    neTypeObj[`${selectedNE.ne}_${selectedNE.card}`] ??
                    (await getCardVendorType(selectedNE.ne, selectedNE.card));
                neTypeObj[`${selectedNE.ne}_${selectedNE.card}`] = currentCardType;
                if (selectedNE.card.startsWith("TFF")) {
                    // tff
                    for (let j = 0; j < unSelectedNES.length; j++) {
                        const compareCardType =
                            neTypeObj[`${unSelectedNES[j].ne}_${unSelectedNES[j].card}`] ??
                            (await getCardVendorType(unSelectedNES[j].ne, unSelectedNES[j].card));
                        neTypeObj[`${unSelectedNES[j].ne}_${unSelectedNES[j].card}`] = compareCardType;
                        if (unSelectedNES[j].card.startsWith("TFF")) {
                            if (compareCardType !== currentCardType) {
                                message.error(labelList.och_tff_match);
                                return false;
                            }
                        } else if (!Mux48CardType.includes(compareCardType)) {
                            message.error(labelList.och_tff_mux_match);
                            return false;
                        }
                    }
                } else {
                    // mux
                    for (let j = 0; j < unSelectedNES.length; j++) {
                        const compareCardType =
                            neTypeObj[`${unSelectedNES[j].ne}_${unSelectedNES[j].card}`] ??
                            (await getCardVendorType(unSelectedNES[j].ne, unSelectedNES[j].card));
                        neTypeObj[`${unSelectedNES[j].ne}_${unSelectedNES[j].card}`] = compareCardType;
                        if (unSelectedNES[j].card.startsWith("TFF")) {
                            if (!Mux48CardType.includes(currentCardType)) {
                                message.error(labelList.och_tff_mux_match);
                                return false;
                            }
                        } else if (compareCardType !== currentCardType) {
                            message.error(labelList.och_mux_match);
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    };

    const checkInvalid = async cfg => {
        // 脏数据检查
        const r = await checkDirtyData(dataList);
        if (r === false) {
            return {result: false};
        }
        if (cfg.name === "och") {
            // check two client card has same port type
            const startLineCard = valuesRef.current?.[dataList?.[0]?.key];
            const endLineCard = valuesRef.current?.[dataList?.[dataList.length - 1]?.key];
            if (startLineCard && endLineCard) {
                const startLineCardType = await getCardVendorType(startLineCard.ne, startLineCard.card);
                const endLineCardType = await getCardVendorType(endLineCard.ne, endLineCard.card);
                if (
                    startLineCardType !== endLineCardType
                    // &&!intersection(CLIENT_PORT_TYPE_MAP[startLineCardType], CLIENT_PORT_TYPE_MAP[endLineCardType])
                ) {
                    message.error(labelList.two_linecard_must_same_port_type);
                    return {result: false};
                }
            }
            const checkRs = await checkOCHInvalid(dataList, null, true, []);
            if (checkRs === false) {
                return {result: false};
            }
        } else if (cfg.name === "oms") {
            const startMUX = values[dataList[0].key];
            const startMUXType = await getCardVendorType(startMUX.ne, startMUX.card);
            const endMUX = values[dataList[dataList.length - 1].key];
            const endMUXType = await getCardVendorType(endMUX.ne, endMUX.card);
            if (
                ((startMUXType.startsWith("MUX") && endMUXType.startsWith("MUX")) ||
                    (startMUXType.startsWith("OMD") && endMUXType.startsWith("OMD"))) &&
                startMUXType !== endMUXType
            ) {
                message.error(labelList.max_not_fit_max);
                return {result: false};
            }
            if (
                (startMUXType === "MUX96" && endMUXType.startsWith("TFF")) ||
                (endMUXType === "MUX96" && startMUXType.startsWith("TFF"))
            ) {
                message.error(labelList.max96_not_fit_ttf);
                return {result: false};
            }
            if (
                (startMUXType.startsWith("TFF") && (endMUXType.startsWith("MUX") || endMUXType.startsWith("OMD"))) ||
                ((startMUXType.startsWith("MUX") || startMUXType.startsWith("OMD")) && endMUXType.startsWith("TFF"))
            ) {
                const {start, end} = await findTFF();
                if (startMUXType.startsWith("TFF")) {
                    if (checkDup(start, startMUXType)) {
                        return {result: false};
                    }
                    start.push({...startMUX, tffType: startMUXType});
                } else {
                    if (checkDup(end, endMUXType)) {
                        return {result: false};
                    }
                    end.push({...endMUX, tffType: endMUXType});
                }
                return {start, end, result: true};
            }
            if (startMUXType.startsWith("TFF") && endMUXType.startsWith("TFF")) {
                const {start, end} = await findTFF();

                // rollback the code for bug TNMS-770
                // if (checkDup(start, startMUXType)) {
                //     return {result: false};
                // }
                // --------------end

                start.push({...startMUX, tffType: startMUXType});

                // rollback the code for bug TNMS-770
                // if (checkDup(end, endMUXType)) {
                //     return {result: false};
                // }
                // --------------end

                end.push({...endMUX, tffType: endMUXType});

                // rollback the code for bug TNMS-770
                return {start, end, result: true};
                // --------------end

                // rollback the code for bug TNMS-770
                // for (let i = 0; i < start.length; i++) {
                //     for (let j = 0; j < end.length; j++) {
                //         if (start[i].tffType === end[j].tffType) {
                //             return {start, end, result: true};
                //         }
                //     }
                // }
                // message.error(labelList.tff_not_fit);
                // return {result: false};
                // --------------end
            }
        } else if (cfg.name === "client") {
            const ochDatas = [];
            await getAllOCHDatas(dataList, ochDatas);
            if (
                Array.from(
                    new Set(
                        ochDatas.map(i => {
                            if (i["signal-type"].startsWith("PROT")) {
                                return i["signal-type"]?.substring?.(i["signal-type"].indexOf("_") + 1);
                            }
                            return i["signal-type"];
                        })
                    )
                ).length > 1
            ) {
                message.error(labelList.signal_type_diff);
                return {result: false};
            }
        }
        return {result: true};
    };

    const getEndPointData = (side, fiberList, type) => {
        const i = side === "a" ? 0 : dataList.length - 1;
        const {ne, card, port} = values[dataList[i].key];
        const {key} = dataList[i];
        let _port;
        if (type === "client" && fiberList.length === 0) {
            // client service for no protection
            _port = port;
        } else {
            _port = fiberList[side === "a" ? 0 : fiberList.length - 1][side === "a" ? "source_port" : "dest_port"];
        }
        return {
            ne_id: ne,
            card,
            port: type === "client" ? _port : values[key].port || _port
        };
    };

    const getOMSKey = (dataList, nameList) => {
        for (let i = 0; i < dataList.length; i++) {
            const item = dataList[i];
            if (item instanceof Array) {
                getOMSKey(item, nameList);
            } else if (item.type === "OMS") {
                const _key = item.key.substring(0, item.key.length - 2);
                if (!nameList.includes(_key)) {
                    nameList.push(_key);
                }
            }
        }
    };

    const saveFiber = async () => {
        // eslint-disable-next-line no-unreachable-loop
        for (let i = 0; i < menuConfig.length; i++) {
            setAddProvisionWating(true);
            const item = menuConfig[i];
            if (item.child.filter(model => model.key === modelActive).length > 0) {
                const checkRs = await checkInvalid(item);
                if (!checkRs.result) {
                    setAddProvisionWating(false);
                    return false;
                }

                const serviceName = form.getFieldValue("name");
                if (!serviceName) {
                    message.error("Please input name");
                    setAddProvisionWating(false);
                    return false;
                }
                if ((await objectGet("nms:provision", {name: serviceName})).documents.length > 0) {
                    message.error(serviceName + gLabelList.exists);
                    setAddProvisionWating(false);
                    return false;
                }
                const successChannel = [];
                const {result, fiberList, successFiber} = await execSaveFiber();
                try {
                    if (!result) {
                        await rollback(successFiber);
                        setAddProvisionWating(false);
                        return false;
                    }
                    if (fiberList.length > 0 && successFiber.filter(item => !item?.exist).length === 0) {
                        message.error(labelList.same_service);
                        setAddProvisionWating(false);
                        return false;
                    }

                    const a = getEndPointData("a", fiberList, item.name);
                    const b = getEndPointData("z", fiberList, item.name);
                    const saveValue = {
                        name: serviceName,
                        type: item.name,
                        a,
                        z: b,
                        fiber: successFiber
                    };
                    if (checkRs.start) {
                        saveValue.tffA = checkRs.start;
                    }
                    if (checkRs.end) {
                        saveValue.tffZ = checkRs.end;
                    }
                    const otss = [];
                    const ochs = [];
                    const nes = [];
                    const ochDatas = [];
                    await getAllNodes(dataList, nes, otss, ochs, ochDatas);
                    if (item.name === "client") {
                        const rs_a = (
                            await objectGet("ne:5:component", {
                                ne_id: a.ne_id,
                                name: a.card
                            })
                        ).documents;
                        a.type = rs_a[0].value.data.state["vendor-type-preconf"];
                        nes[0] = a;
                        const rs_b = (
                            await objectGet("ne:5:component", {
                                ne_id: b.ne_id,
                                name: b.card
                            })
                        ).documents;
                        b.type = rs_b[0].value.data.state["vendor-type-preconf"];
                        nes[nes.length - 1] = b;
                    }
                    saveValue.ne = nes;
                    if (item.name === "ots") {
                        // ots
                    } else if (item.name === "och") {
                        const omsKeyList = [];
                        const omsIDList = [];
                        const frequencyIndexList = {};
                        getOMSKey(dataList, omsKeyList);
                        let frequencyIndex;
                        let minFrequency;
                        let maxFrequency;
                        if (omsKeyList.length > 0) {
                            for (let k = 0; k < omsKeyList.length; k++) {
                                const omsKey = `${omsKeyList[k]}_a`;
                                const omsName = values[omsKey].oms;
                                const omsID = (await objectGet("nms:provision", {name: omsName})).documents[0].id;
                                omsIDList.push(omsID);
                                // save Frequency Channel
                                const wss = (
                                    await objectGet("nms:provision", {DBKey: omsID})
                                ).documents[0].value.ne.filter(item => item.card.startsWith("WSS"));
                                let {ne, card, port} = values[omsKey];
                                if (port.indexOf("CH") < 0) {
                                    const _t = values[omsKey.replace("_a", "_b")];
                                    ne = _t.ne;
                                    card = _t.card;
                                    port = _t.port;
                                }
                                const muxType = await getCardVendorType(ne, card);
                                const chanelIndex = port.split("CH")[1];
                                if (chanelIndex) {
                                    let frequency;
                                    if (Mux48CardType.includes(muxType)) {
                                        frequency = 191400 + (chanelIndex - 1) * 100;
                                        minFrequency = frequency - 50;
                                        maxFrequency = frequency + 50;
                                    } else if (muxType === "MUX96") {
                                        frequency = 191350 + (chanelIndex - 1) * 50;
                                        minFrequency = frequency - 25;
                                        maxFrequency = frequency + 25;
                                    } else if (muxType.startsWith("TFF")) {
                                        frequency = parseInt(
                                            `19${parseInt(muxType.substring(3, 5)) + (chanelIndex - 1)}00`
                                        );
                                        minFrequency = frequency - 50;
                                        maxFrequency = frequency + 50;
                                    }
                                    frequencyIndex = (frequency * 1000).toString();
                                }
                                frequencyIndexList[omsID] = frequencyIndex;
                                for (let i = 0; i < wss.length; i++) {
                                    const item = wss[i];
                                    const value = {
                                        index: frequencyIndex,
                                        "ad-port": item.port,
                                        "line-port": `${item.card.replace("WSS", "PORT")}-MUX`,
                                        "min-edge-freq": (minFrequency * 1000).toString(),
                                        "max-edge-freq": (maxFrequency * 1000).toString()
                                    };
                                    const rs = await netconfByXML({
                                        ne_id: item.ne,
                                        msg: false,
                                        xml: {
                                            components: {
                                                $: {
                                                    xmlns: "http://openconfig.net/yang/platform"
                                                },
                                                component: {
                                                    name: item.card,
                                                    wss: {
                                                        config: {
                                                            "frequency-channel": {
                                                                $: {
                                                                    xmlns: "http://openconfig.net/yang/platform/wss",
                                                                    "nc:operation": "create"
                                                                },
                                                                ...value
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    });
                                    if (rs.message !== "SUCCESS") {
                                        if (rs.apiMessage.indexOf("already exists") > -1) {
                                            // eslint-disable-next-line no-console
                                            console.error(rs.apiMessage);
                                        } else {
                                            message.error(
                                                labelList.create_channel_fail.format(item.ne, item.port, rs.apiMessage)
                                            );
                                            await rollback(successFiber, successChannel);
                                            setAddProvisionWating(false);
                                            return false;
                                        }
                                    }
                                    const ch = (
                                        await objectGet("ne:5:component", {
                                            ne_id: item.ne,
                                            name: item.card
                                        })
                                    ).documents[0];
                                    let r;
                                    if (!ch.value.data.wss) {
                                        const v = {...ch.value};
                                        v.data.wss = {config: {"frequency-channel": [value]}};
                                        r = await objectEdit({
                                            key: ch.id,
                                            data: v,
                                            msg: false
                                        });
                                    } else if (!ch.value.data.wss.config) {
                                        const v = {...ch.value};
                                        v.data.wss.config = {"frequency-channel": [value]};
                                        r = await objectEdit({
                                            key: ch.id,
                                            data: v,
                                            msg: false
                                        });
                                    } else if (
                                        ch.value.data.wss.config === "" ||
                                        !ch.value.data.wss.config["frequency-channel"]
                                    ) {
                                        const v = {...ch.value};
                                        v.data.wss.config["frequency-channel"] = [value];
                                        r = await objectEdit({
                                            key: ch.id,
                                            data: v,
                                            msg: false
                                        });
                                    } else if (ch.value.data.wss.config["frequency-channel"] instanceof Array) {
                                        r = await arrayAdd({
                                            entity: "ne:5:component",
                                            key: {ne_id: item.ne, name: item.card},
                                            path: "data.wss.config.frequency-channel",
                                            data: value,
                                            msg: false
                                        });
                                    } else {
                                        const v = {...ch.value};
                                        v.data.wss.config["frequency-channel"] = [
                                            {...v.data.wss.config["frequency-channel"]},
                                            value
                                        ];
                                        r = await objectEdit({
                                            key: ch.id,
                                            data: v,
                                            msg: false
                                        });
                                    }

                                    if (r.apiResult === "fail") {
                                        // eslint-disable-next-line no-console
                                        console.log("save wss channel fail!");
                                    }

                                    successChannel.push({
                                        ne_id: item.ne,
                                        name: item.card,
                                        index: frequencyIndex
                                    });
                                }
                            }
                        } else {
                            frequencyIndexList.defaultFrequency = channel;
                        }
                        saveValue.oms = omsIDList;
                        saveValue.frequencyIndex = frequencyIndexList;
                        // edit lineCard channel
                        const lineCard = [];
                        lineCard.push({ne: fiberList[0].source_ne, port: fiberList[0].source_port});
                        lineCard.push({
                            ne: fiberList[fiberList.length - 1].dest_ne,
                            port: fiberList[fiberList.length - 1].dest_port
                        });
                        let setLine = true;
                        for (let i = 0; i < lineCard.length; i++) {
                            const _r = await netconfChange({
                                ne_id: lineCard[i].ne,
                                operation: "edit",
                                entity: "component",
                                keys: [lineCard[i].port.replace("PORT", "OCH")],
                                values: {
                                    "optical-channel": {
                                        config: {
                                            frequency: Object.values(frequencyIndexList)[0]
                                        }
                                    }
                                },
                                msg: false
                            });
                            if (_r?.message !== "SUCCESS") {
                                message.error(labelList.save_failed);
                                setLine = false;
                                break;
                            }
                        }
                        if (!setLine) {
                            await rollback(successFiber, successChannel);
                            setAddProvisionWating(false);
                            return false;
                        }
                    } else if (item.name === "oms") {
                        saveValue.ots = otss;
                    } else if (item.name === "client") {
                        saveValue.och = ochs;
                        for (let k = 0; k < ochDatas.length; k++) {
                            const ochData = ochDatas[k];
                            let newSignalType = ochData["signal-type"];
                            if (newSignalType.startsWith("PROT")) {
                                newSignalType = newSignalType.substring(newSignalType.indexOf("_") + 1);
                            }
                            if (newSignalType === ochData["default-signal-type"]) {
                                continue;
                            }
                            const rz = await updatePortSignalType({
                                selectNe: ochData.ne,
                                data: {
                                    port: ochData.port
                                },
                                portType: `PROT_${newSignalType}`,
                                lineCard: ochData.card,
                                lineCardType: await getCardVendorType(ochData.ne, ochData.card)
                            });
                            if (!rz) {
                                await rollback(successFiber, successChannel);
                                setAddProvisionWating(false);
                                return false;
                            }
                        }
                    }
                    const rs = await objectAdd({
                        DBKey: `nms:provision:${saveValue.name}`,
                        entity: "nms:provision",
                        data: saveValue,
                        msg: false,
                        fail: () => {
                            message.error(labelList.save_failed);
                            setAddProvisionWating(false);
                            return false;
                        }
                    });
                    message.success(labelList.save_success).then();
                    saveSuccess({type: item.name, name: saveValue.name, dbKey: rs.db_key});
                    return true;
                } catch (e) {
                    // eslint-disable-next-line no-console
                    console.log(e);
                    await rollback(successFiber, successChannel);
                    setAddProvisionWating(false);
                    return false;
                }
            }
        }
    };

    const rollback = async (fibers, channels) => {
        for (let i = 0; i < fibers.length; i++) {
            const fiber = fibers[i];
            if (!fiber || fiber?.exist) {
                continue;
            }
            try {
                await netconfChange({
                    ne_id: fiber.ne,
                    operation: "delete",
                    entity: "connection",
                    keys: [fiber.index.toString()],
                    values: {},
                    msg: false
                }).then();
            } catch (e) {
                // eslint-disable-next-line no-console
                console.log(e);
            }
        }
        if (channels) {
            for (let i = 0; i < channels.length; i++) {
                const item = channels[i];
                try {
                    await netconfByXML({
                        ne_id: item.ne_id,
                        msg: false,
                        xml: {
                            components: {
                                $: {
                                    xmlns: "http://openconfig.net/yang/platform"
                                },
                                component: {
                                    name: item.name,
                                    wss: {
                                        config: {
                                            "frequency-channel": {
                                                $: {
                                                    xmlns: "http://openconfig.net/yang/platform/wss",
                                                    "nc:operation": "delete"
                                                },
                                                index: item.index
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    });
                } catch (e) {
                    // eslint-disable-next-line no-console
                    console.log(e);
                }
            }
        }
    };

    const resetRelateValue = (value, vkey, key, cfg) => {
        if (key === "group") {
            form.resetFields([`${vkey}_ots`, `${vkey}_oms`, `${vkey}_ne`, `${vkey}_card`, `${vkey}_port`]);
            if (["OMS", "OTS"].includes(cfg.type)) {
                const bSideKey = getOppoSideKey(cfg.key);
                form.resetFields([
                    `${bSideKey}_group`,
                    `${bSideKey}_ots`,
                    `${bSideKey}_oms`,
                    `${bSideKey}_ne`,
                    `${bSideKey}_card`,
                    `${bSideKey}_port`
                ]);
            }
        }
        if (key === "ne") {
            form.resetFields([`${vkey}_card`, `${vkey}_port`]);
        } else if (key === "card") {
            form.resetFields([`${vkey}_port`]);
        }
    };

    const onChangeValue = (v, vkey, key, cfg) => {
        const n = {...values};
        if (v && v.toString().trim() === "") {
            if (n[vkey]) {
                delete n[vkey][key];
            }
            return;
        }
        if (n[vkey]) {
            n[vkey][key] = v;
            resetRelateValue(n, vkey, key, cfg);
        } else {
            n[vkey] = {[key]: v};
        }
        if (cfg?.addListener?.includes(key)) {
            let cardType = getCardType(cfg);
            if (!cardType) {
                // eslint-disable-next-line prefer-destructuring
                cardType = cfg.type[0];
            }
            if (key === "group") {
                setGroup(v, cfg, n);
            } else {
                cardConfig[cardType]?.listener?.[key](v, cfg, n);
            }
        }
        if (typeof cfg.type === "string" && cardConfig?.[cfg.type]?.listenerAll?.[key]) {
            cardConfig?.[cfg.type]?.listenerAll?.[key](v, cfg, n);
        }
        saveValues(n);
    };

    const getOptions = (cfg, type) => {
        const rs = selectData?.[cfg.key]?.[`${type}Options`];
        if (!rs || rs.length === 0) {
            return [{value: "", label: "No Data", disabled: true}];
        }
        const options = [];
        for (let i = 0; i < rs.length; i++) {
            if (typeof rs[i] === "string") {
                options.push({
                    value: rs[i],
                    label: type === "ne" ? neNameMap[rs[i]] : rs[i]
                });
                continue;
            } else if (type === "ne") {
                rs[i].label = neNameMap[rs[i].value];
            }
            const item = {...rs[i]};
            if (item.used || item.selected) {
                if (
                    cfg.key !== "ne" &&
                    !((!item.type || item.type.startsWith("TFF")) && item.value.startsWith("MUX-")) && // fix bug TNMS-1314
                    !(cfg.type === "OTS" && item.value.startsWith("PORT-"))
                ) {
                    item.disabled = true;
                }
                item.label += ` (${item.used ? "used" : "selected"})`;
            }
            if (item.type) {
                item.title += ` (${item.type})`;
            }
            options.push(item);
        }
        return options;
    };

    const setOptions = (cfg, type, value) => {
        const n = {...selectData};
        if (n[cfg.key]) {
            n[cfg.key][`${type}Options`] = value;
        } else {
            n[cfg.key] = {[`${type}Options`]: value};
        }
        setSelectData(n);
    };

    const getCardType = cfg => {
        if (cfg.type instanceof Array || cfg.type === "OTS") {
            const card = values?.[cfg.key]?.card;
            if (!card) {
                return false;
            }
            return card.split("-")[0].toUpperCase();
        }
        return cfg.type;
    };

    const supportNode = (cfg, key, type, card) => {
        const _card = card || values?.[cfg.key]?.card;
        if (_card && typeof cfg.type === "string" && cardConfig[cfg.type]) {
            if (cardConfig[cfg.type]?.data?.[type]?.enabled) {
                return cardConfig[cfg.type]?.data?.[type]?.enabled(_card);
            }
        }
        if (cfg.data && cfg.data[type] && Object.hasOwn(cfg.data[type], "enabled")) {
            if (typeof cfg.data[type].enabled === "boolean") {
                return cfg.data[type].enabled;
            }
            return cfg.data[type].enabled(_card);
        }

        if (cfg.type instanceof Array) {
            if (_card) {
                return cardConfig[_card.split("-")[0].toUpperCase()]?.data?.[type];
            }
            for (let i = 0; i < cfg.type.length; i++) {
                if (cardConfig[cfg.type[i]]?.data?.[type]) {
                    return true;
                }
            }
        } else {
            return cardConfig[cfg.type]?.data?.[type];
        }
    };

    const createSelectByData = (cfg, rKey, label, disabled, datas, require) => {
        const {key} = cfg;
        return (
            <Form.Item
                className={styles.form_item}
                key={`${key}_${rKey}`}
                label={label}
                name={`${key}_${rKey}`}
                rules={
                    (require === undefined || require === true) && [
                        {
                            required: !(cfg.data && cfg.data[rKey] && cfg.data[rKey].require === false),
                            message: labelList.required
                        }
                    ]
                }
            >
                <Select
                    onChange={v => {
                        onChangeValue(v, key, rKey, cfg);
                    }}
                    filterSort={sort()}
                    disabled={typeof disabled === "boolean" ? disabled : disabled?.[rKey]}
                    key={`${key}${rKey}`}
                    allowClear={rKey === "group"}
                    options={datas}
                />
            </Form.Item>
        );
    };

    const checkSelected = (currentKey, dataConfig, selectObject) => {
        for (let j = 0; j < dataConfig.length; j++) {
            const item = dataConfig[j];
            if (item.key !== currentKey) {
                if (item instanceof Array) {
                    const r = checkSelected(currentKey, item, selectObject);
                    if (r) {
                        return r;
                    }
                }
                if (values[item.key]?.ne || values[item.key]?.ots) {
                    const keys = Object.keys(selectObject);
                    let dup = true;

                    for (let i = 0; i < keys.length; i++) {
                        const _key = keys[i];
                        if (selectObject[_key] !== values[item.key][_key]) {
                            dup = false;
                            break;
                        }
                    }
                    if (dup) {
                        return true;
                    }
                }
            }
        }
    };

    const createInput = (cfg, rKey, label) => {
        const {key} = cfg;
        const frequencyOptions = [
            {value: "50GHz", label: "50GHz", children: []},
            {value: "75GHz", label: "75GHz", children: []},
            {value: "100GHz", label: "100GHz", children: []}
        ];
        for (let i = 0; i < 96; i++) {
            const value = 191.35 + i * 0.05;
            const frequencyNm = (
                Math.round(((((2.99792458 * 10 ** 8) / value) * 1000000) / 1000000000) * 100) / 100
            ).toFixed(2);
            frequencyOptions[0].children.push({
                label:
                    i < 48
                        ? `C${i + 13}-${value.toFixed(2)}THz-${frequencyNm}nm`
                        : `H${i - 35}-${value.toFixed(2)}THz-${frequencyNm}nm`,
                value:
                    i < 48
                        ? `C${i + 13}-${value.toFixed(2)}THz-${frequencyNm}nm`
                        : `H${i - 35}-${value.toFixed(2)}THz-${frequencyNm}nm`
            });
        }
        for (let i = 0; i < 48; i++) {
            const value = 191.4 + i * 0.1;
            const frequencyTHz = value.toFixed(2);
            const frequencyNm = (
                Math.round(((((2.99792458 * 10 ** 8) / value) * 1000000) / 1000000000) * 100) / 100
            ).toFixed(2);
            const label = `C${i + 14}-${frequencyTHz}THz-${frequencyNm}nm`;
            const optionValue = `C${i + 14}-${frequencyTHz}THz-${frequencyNm}nm`;
            frequencyOptions[2].children.push({
                label,
                value: optionValue
            });
        }
        for (let i = 0; i < 64; i++) {
            const value = 196.0375 - i * 0.075;
            const frequencyTHz = value.toFixed(4);
            const frequencyNm = (
                Math.round(((((2.99792458 * 10 ** 8) / value) * 1000000) / 1000000000) * 100) / 100
            ).toFixed(2);
            const label = `CM${i + 1}-${frequencyTHz}THz-${frequencyNm}nm`;
            const optionValue = `CM${i + 1}-${frequencyTHz}THz-${frequencyNm}nm`;
            frequencyOptions[1].children.push({
                label,
                value: optionValue
            });
        }
        return (
            <Form.Item
                className={styles.form_item}
                key={`${key}_${rKey}`}
                label={labelList[label] ?? label}
                name={`${key}_${rKey}`}
                // rules={[
                //     {
                //         required: true,
                //         message: labelList.required
                //     }
                // ]}
            >
                <Cascader
                    allowClear={false}
                    disabled={channelDisabled}
                    defaultValue={(channel / 1000000).toFixed(2)}
                    options={frequencyOptions}
                    onChange={v => {
                        setChannel(v[1].match(/(\d+(\.\d+)?)/g)[1] * 1000000);
                        dataList.forEach(i => {
                            if (i?.type === "LINECARD" && i.key !== key) {
                                form.setFieldValue(`${i.key}_${rKey}`, (channel / 1000000).toFixed(2));
                            }
                        });
                    }}
                />
            </Form.Item>
        );
    };

    const createSelect = (cfg, rKey, label, disabled, required = true, allowClear = false) => {
        const {key} = cfg;
        return (
            <Form.Item
                className={styles.form_item}
                key={`${key}_${rKey}`}
                label={labelList[label] ?? label}
                name={`${key}_${rKey}`}
                rules={
                    required
                        ? [
                              {
                                  required: true,
                                  message: labelList.required
                              }
                          ]
                        : []
                }
            >
                <Select
                    onChange={v => {
                        onChangeValue(v, key, rKey, cfg);
                    }}
                    filterSort={sort()}
                    allowClear={allowClear}
                    onDropdownVisibleChange={async open => {
                        if (open) {
                            if (rKey.startsWith("raman")) {
                                const otsService = (await objectGet("nms:provision", {type: "ots"})).documents;
                                const ramanDevices = (await objectGet("ne:2:ramanChannelEntry", {})).documents
                                    .filter(
                                        i =>
                                            i.value.data?.ramanChannelWorkMode ===
                                            (rKey.startsWith("raman-pre") ? "0" : "1")
                                    )
                                    .map(i => {
                                        const _option = {
                                            value: i.value.ne_id,
                                            label: neNameMap[i.value.ne_id],
                                            title: neNameMap[i.value.ne_id]
                                        };
                                        for (let i = 0; i < dataList.length; i++) {
                                            const configKey = dataList[i].key;
                                            const _v = values[configKey];
                                            if (_v) {
                                                _option.selected =
                                                    Object.entries(_v).find(
                                                        ([k, v]) => k.indexOf("raman") > -1 && v === _option.value
                                                    ) !== undefined;
                                                if (_option.selected) {
                                                    break;
                                                }
                                            }
                                        }
                                        return _option;
                                    });
                                ramanDevices.map(i => {
                                    if (!i.selected) {
                                        i.used =
                                            otsService.find(
                                                service =>
                                                    service.value.ne.find(ne =>
                                                        Object.entries(ne).find(
                                                            ([k, v]) => k.indexOf("raman") > -1 && v === i.value
                                                        )
                                                    ) !== undefined
                                            ) !== undefined;
                                    }
                                });
                                setOptions(cfg, rKey, ramanDevices);
                            } else if (rKey === "signal-type") {
                                const {ne, port} = values?.[cfg.key] ?? {};
                                if (ne && port) {
                                    const r = (await getSignalType(ne, port)).map(s => ({...s, title: s.label}));
                                    setOptions(cfg, rKey, r);
                                }
                            } else {
                                const omss = (
                                    await objectGet("nms:provision", {
                                        type: rKey
                                    })
                                ).documents.map(item => item.value.name);
                                if (rKey === "ots" || rKey === "oms") {
                                    const omsList = omss.map(item => {
                                        const oms = {
                                            value: item,
                                            label: item,
                                            title: item
                                        };
                                        if (
                                            checkSelected(cfg.key, dataList, {
                                                [rKey]: item
                                            })
                                        ) {
                                            oms.selected = true;
                                        }
                                        return oms;
                                    });
                                    setOptions(cfg, rKey, omsList);
                                } else {
                                    setOptions(cfg, rKey, omss);
                                }
                            }
                        }
                    }}
                    disabled={typeof disabled === "boolean" ? disabled : disabled?.[rKey]}
                    options={getOptions(cfg, rKey)}
                />
            </Form.Item>
        );
    };

    const sort = () => {
        return (optionA, optionB) =>
            (optionA?.label ?? "")
                .toString()
                .toLowerCase()
                .localeCompare((optionB?.label ?? "").toString().toLowerCase(), "ZH-CN", {numeric: true});
    };

    const createNodeContent = cfg => {
        const {type, key, disabled} = cfg;
        const types = convertToArray(type).join("/");
        if (type === "linkSpan") {
            return <div />;
        }
        return (
            <div className={styles.node}>
                <div className={styles.node_content}>
                    <div className={styles.node_content_type}>{cfg.title || types}</div>
                    {createSelectByData(
                        cfg,
                        "group",
                        labelList.group,
                        ["OTS", "OMS", "OCH"].includes(cfg.type) ? true : disabled,
                        groupData.map(item => ({
                            label: item.value.name,
                            value: item.id
                        })),
                        false
                    )}
                    {cfg.type === "OTS" && createSelect(cfg, "ots", "OTS", disabled)}
                    {cfg.type === "OMS" && createSelect(cfg, "oms", "OMS", disabled)}
                    {cfg.type === "OCH" && createSelect(cfg, "och", "OCH", disabled)}
                    <Form.Item
                        className={styles.form_item}
                        key={`${key}_ne`}
                        label={labelList.ne}
                        name={`${key}_ne`}
                        rules={[{required: true, message: labelList.required}]}
                    >
                        <Select
                            onChange={v => {
                                onChangeValue(v, key, "ne", cfg);
                            }}
                            filterSort={sort()}
                            onDropdownVisibleChange={async open => {
                                if (open) {
                                    const filter = {type: "5"};
                                    if (cfg.type === "OMS" && values?.[key]?.oms) {
                                        await objectGet("nms:provision", {name: values[key].oms}).then(rs => {
                                            const neSet = new Set();
                                            let ttfName = key.endsWith("_a") ? "tffA" : "tffZ";
                                            if (cfg.side) {
                                                ttfName = ttfName === "tffA" ? "tffZ" : "tffA";
                                            }
                                            rs.documents[0].value[ttfName]?.map(item => {
                                                neSet.add(item.ne);
                                            });
                                            setOptions(cfg, "ne", [...neSet]);
                                        });
                                    } else {
                                        if (values?.[key]?.group && values?.[key]?.group.trim() !== "") {
                                            filter.group = values?.[key]?.group;
                                        }
                                        await objectGet("config:ne", filter).then(rs => {
                                            setOptions(
                                                cfg,
                                                "ne",
                                                rs.documents.map(i => {
                                                    return {
                                                        label: i.value.ne_id,
                                                        value: i.value.ne_id,
                                                        disabled: i.value.runState === 0
                                                    };
                                                })
                                            );
                                        });
                                    }
                                }
                            }}
                            disabled={typeof disabled === "boolean" ? disabled : disabled?.ne}
                            options={getOptions(cfg, "ne")}
                        />
                    </Form.Item>
                    <Form.Item
                        className={styles.form_item}
                        key={`${key}_card`}
                        label={labelList.card}
                        name={`${key}_card`}
                        rules={[{required: true, message: labelList.required}]}
                    >
                        <Select
                            loading={loading === `${cfg.key}_card`}
                            onChange={v => {
                                onChangeValue(v, key, "card", cfg);
                            }}
                            disabled={typeof disabled === "boolean" ? disabled : disabled?.card}
                            filterSort={sort()}
                            onDropdownVisibleChange={async open => {
                                if (open) {
                                    if (values?.[key]?.ne) {
                                        setLoading(`${cfg.key}_card`);
                                        let cards = [];
                                        if (cfg?.type?.[0] === "*") {
                                            cards = cards.concat(
                                                (
                                                    await objectGet("ne:5:component", {
                                                        ne_id: values?.[key]?.ne,
                                                        parent: "CHASSIS-1"
                                                    })
                                                ).documents
                                                    .filter(
                                                        item =>
                                                            ![
                                                                "OTDR_CARD",
                                                                "SLOT",
                                                                "POWER_SUPPLY",
                                                                "FAN",
                                                                "CONTROLLER_CARD",
                                                                "PTM",
                                                                "PTMW",
                                                                "PTME",
                                                                "OCM_CARD"
                                                            ].includes(item.value.data.state.type)
                                                    )
                                                    .map(item => item.value.data)
                                            );
                                        } else if (cfg.type === "OMS" && values?.[key]?.ne) {
                                            // oms
                                            const oms = await objectGet("nms:provision", {name: values[key].oms});
                                            let ttfName = key.endsWith("_a") ? "tffA" : "tffZ";
                                            if (cfg.side) {
                                                ttfName = ttfName === "tffA" ? "tffZ" : "tffA";
                                            }
                                            const ttfs =
                                                oms.documents[0].value[ttfName]?.filter(i => i.ne === values[key].ne) ??
                                                [];
                                            for (let i = 0; i < ttfs.length; i++) {
                                                cards.push({
                                                    name: ttfs[i].card,
                                                    config: {"vendor-type-preconf": ttfs[i].tffType}
                                                });
                                            }
                                        } else if (cfg?.type === "OLA") {
                                            const rs = (
                                                await objectGet("ne:5:component", {
                                                    ne_id: values?.[key]?.ne,
                                                    type: "OLA"
                                                })
                                            ).documents;
                                            const cardsInfo = [];
                                            for (let i = 0; i < rs.length; i++) {
                                                const cardName = rs[i].value.data.name;
                                                const rs1 = (
                                                    await objectGet("ne:5:component", {
                                                        ne_id: values?.[key]?.ne,
                                                        parent: cardName,
                                                        type: "PORT"
                                                    })
                                                ).documents;
                                                const fs = rs1.filter(it => it.value.data.name.endsWith("LA1IN"));
                                                if (fs.length > 0) {
                                                    const _rs = await getPortUsed({
                                                        ne_id: values?.[key]?.ne,
                                                        portName: [fs[0].value.data.name]
                                                    });
                                                    let selected = false;
                                                    if (
                                                        checkSelected(cfg.key, dataList, {
                                                            ne: values[cfg.key].ne,
                                                            card: cardName
                                                        })
                                                    ) {
                                                        selected = true;
                                                    }
                                                    cardsInfo.push({
                                                        title: cardName,
                                                        label: cardName,
                                                        type: rs[i].value.data.config["vendor-type-preconf"],
                                                        used: _rs.used[0],
                                                        value: cardName,
                                                        selected
                                                    });
                                                }
                                            }
                                            setOptions(cfg, "card", cardsInfo);
                                            setLoading("");
                                            return;
                                        } else if (values?.[key]?.ne) {
                                            await Promise.all(
                                                convertToArray(values?.[key]?.type || cfg.subType || cfg.type).map(
                                                    async item => {
                                                        await objectGet("ne:5:component", {
                                                            ne_id: values?.[key]?.ne,
                                                            type: item
                                                        }).then(rs => {
                                                            cards = cards.concat(rs.documents.map(i => i.value.data));
                                                        });
                                                    }
                                                )
                                            );
                                        }
                                        const newCards = [];
                                        await Promise.all(
                                            cards.map(async cardObj => {
                                                const _card = cardObj.name;
                                                let used = false;
                                                let selected = false;
                                                if (!supportNode(cfg, key, "port", _card)) {
                                                    const ports = new Set();
                                                    ["in", "out"].map(async dire => {
                                                        const _fun =
                                                            cardConfig[_card.split("-")[0].toUpperCase()].output[dire];
                                                        if (typeof _fun === "function") {
                                                            const _port = _fun({card: _card}, null, cfg?.dire);
                                                            ports.add(_port);
                                                        }
                                                    });
                                                    const _rs = await getPortUsed({
                                                        ne_id: values[cfg.key].ne,
                                                        portName: Array.from(ports)
                                                    });
                                                    used = _rs.used.filter(item => item === true).length > 0;
                                                    if (
                                                        checkSelected(cfg.key, dataList, {
                                                            ne: values[cfg.key].ne,
                                                            card: _card
                                                        })
                                                    ) {
                                                        selected = true;
                                                    }
                                                }
                                                const cardLabel = `${
                                                    cardObj.config["vendor-type-preconf"]
                                                }${_card.substring(_card.indexOf("-"))}`;
                                                newCards.push({
                                                    title: cardLabel,
                                                    label: cardLabel,
                                                    type: cardObj.config["vendor-type-preconf"],
                                                    used,
                                                    value: _card,
                                                    selected
                                                });
                                            })
                                        );
                                        const l = [];
                                        await checkOCHInvalid(dataList, null, false, l);
                                        if (l.length === 0) {
                                            await updateCardWithTTF(cfg, newCards);
                                        }
                                        setOptions(cfg, "card", newCards);
                                        setLoading("");
                                    }
                                }
                            }}
                            key={`${key}card`}
                            options={getOptions(cfg, "card")}
                        />
                    </Form.Item>
                    {supportNode(cfg, key, "port") && (
                        <Form.Item
                            className={styles.form_item}
                            key={`${key}_port`}
                            label={
                                // eslint-disable-next-line no-nested-ternary
                                supportNode(cfg, key, "apsc")
                                    ? labelList.protection_group
                                    : type === "OLA"
                                      ? labelList.left_port
                                      : labelList.port
                            }
                            name={`${key}_port`}
                            rules={[
                                {
                                    required: !(cfg.data && cfg.data.port && cfg.data.port.require === false),
                                    message: labelList.required
                                }
                            ]}
                        >
                            <Select
                                loading={loading === `${cfg.key}_port`}
                                onChange={v => {
                                    onChangeValue(v, key, "port", cfg);
                                    if (type === "OLA") {
                                        const _ops = getOptions(cfg, "port");
                                        const {label} = _ops.filter(i1 => i1.value !== v)[0];
                                        form.setFieldValue(`${key}_right_port`, label);
                                    }
                                }}
                                filterSort={sort()}
                                disabled={typeof disabled === "boolean" ? disabled : disabled?.port}
                                onDropdownVisibleChange={async open => {
                                    if (open) {
                                        if (values?.[key]?.ne && values?.[key]?.card) {
                                            setLoading(`${cfg.key}_port`);
                                            let newPorts = await combinePort(
                                                values?.[key]?.ne,
                                                values?.[key]?.card,
                                                type === "OCH" ? "C" : null
                                            );
                                            if (cardConfig[getCardType(cfg)]?.data?.port?.data) {
                                                newPorts = cardConfig[getCardType(cfg)].data.port.data(newPorts, cfg);
                                            }

                                            const l = [];
                                            const invalid = await checkOCHInvalid(dataList, null, false, l);
                                            if (l.includes(key)) {
                                                const ptPort = `${values[key].card.replace("TFF", "PORT")}-PT1`;
                                                newPorts.push({
                                                    label: ptPort,
                                                    value: ptPort,
                                                    title: ptPort
                                                });
                                            } else {
                                                await updatePortForFrequency(
                                                    cfg,
                                                    newPorts,
                                                    l.includes(getOppoSideKey(key)),
                                                    l
                                                );
                                            }
                                            // if (type === "OCH") {
                                            //     //todo same card wiath a,z
                                            // }
                                            newPorts.map(item => {
                                                if (
                                                    checkSelected(cfg.key, dataList, {
                                                        ne: values[cfg.key].ne,
                                                        card: values?.[key]?.card,
                                                        port: item.value
                                                    })
                                                ) {
                                                    item.selected = true;
                                                }
                                                if (!invalid) {
                                                    item.disabled = true;
                                                }
                                            });
                                            setOptions(cfg, "port", newPorts);
                                            setLoading("");
                                        }
                                    }
                                }}
                                key={`${key}port`}
                                options={getOptions(cfg, "port")}
                            />
                        </Form.Item>
                    )}
                    {type === "OLA" && (
                        <Form.Item
                            className={styles.form_item}
                            key={`${key}_right_port`}
                            label={labelList.right_port}
                            name={`${key}_right_port`}
                        >
                            <Input disabled key={`${key}_right_port_input`} />
                        </Form.Item>
                    )}
                    {supportNode(cfg, key, "apsc") &&
                        createSelectByData(cfg, "apsp", labelList.a_port, disabled, [
                            {label: labelList.up_route, value: 1},
                            {label: labelList.down_route, value: 2}
                        ])}
                    {supportNode(cfg, key, "apsc") &&
                        createSelectByData(cfg, "apss", labelList.b_port, disabled, [
                            {label: labelList.up_route, value: 1},
                            {label: labelList.down_route, value: 2}
                        ])}
                    {type === "OCH" && createSelect(cfg, "signal-type", "signal-type", disabled)}
                    {type === "LINECARD" && createInput(cfg, "channel", "Channel", true)}
                    {types === "WSS/OA" &&
                        createSelect(
                            cfg,
                            cfg.dire === "r" ? "raman-pre-left" : "raman-pre-right",
                            "forward-raman",
                            disabled,
                            false,
                            true
                        )}
                    {types === "WSS/OA" &&
                        createSelect(
                            cfg,
                            cfg.dire === "r" ? "raman-post-left" : "raman-post-right",
                            "backward-raman",
                            disabled,
                            false,
                            true
                        )}
                    {types === "OLA" &&
                        createSelect(cfg, "raman-pre-left", "left-forward-raman", disabled, false, true)}
                    {types === "OLA" &&
                        createSelect(cfg, "raman-post-left", "left-backward-raman", disabled, false, true)}
                    {types === "OLA" &&
                        createSelect(cfg, "raman-pre-right", "right-forward-raman", disabled, false, true)}
                    {types === "OLA" &&
                        createSelect(cfg, "raman-post-right", "right-backward-raman", disabled, false, true)}
                </div>
            </div>
        );
    };

    const addNode = (data, cfg, newNode, newObject, btType) => {
        for (let index = 0; index < data.length; index++) {
            const item = data[index];
            if (item instanceof Array) {
                item.map(sub => {
                    if (addNode(sub, cfg, newNode, newObject, btType)) {
                        return;
                    }
                });
            } else if (item.key === cfg.key) {
                const _node = newNode();
                if (newObject) {
                    newObject.push(_node);
                }
                data.splice(btType === "leftButtons" ? index : index + 1, 0, _node);
                if (_node.type === "TFF" && values?.[cfg.key]?.group) {
                    values[_node.key] = {group: values[cfg.key].group};
                    form.setFieldValue(`${_node.key}_group`, form.getFieldValue(`${cfg.key}_group`));
                }
                return true;
            }
        }
    };

    const delNode = (data, cfg) => {
        data.map((item, index) => {
            if (item instanceof Array) {
                item.map(sub => {
                    delNode(sub, cfg);
                });
            } else if (item.key === cfg.key) {
                data.splice(index, 1);
            }
        });
    };

    const editNode = (data, key, value) => {
        data.map((item, index) => {
            if (item instanceof Array) {
                item.map(sub => {
                    editNode(sub, key, value);
                });
            } else if (item.key === key) {
                data[index] = {...item, ...value};
            }
        });
    };

    const createButtons = (cfg, btType) => {
        if (!cfg[btType]) {
            return;
        }
        return (
            <div className={styles.add_bt}>
                {cfg[btType].map(item => {
                    const key = `bt_${cfg.key}`;
                    if (item.type === "add" && (item.enabled || item.enabled === undefined)) {
                        if (item.enabled && typeof item.enabled === "function" && !item.enabled(values[cfg.key])) {
                            return;
                        }
                        return (
                            <div
                                key={`${key}add`}
                                title={labelList.add}
                                onClick={() => {
                                    const newDataList = [...dataList];
                                    if (cfg.type === "OTS") {
                                        const newObj = [];
                                        addNode(
                                            newDataList,
                                            cfg,
                                            () => {
                                                return createNewNode({
                                                    type: "OTS",
                                                    title: "OTS A",
                                                    keyCondition: "a",
                                                    defineButtons: []
                                                });
                                            },
                                            newObj
                                        );
                                        const newObj1 = [];
                                        addNode(
                                            newDataList,
                                            newObj[0],
                                            () => {
                                                return createNewNode({
                                                    type: "linkSpan",
                                                    defineKey: `${newObj[0].key.replace("_a", "_link")}`
                                                });
                                            },
                                            newObj1
                                        );
                                        addNode(newDataList, newObj1[0], () => {
                                            return createNewNode({
                                                type: "OTS",
                                                title: "OTS Z",
                                                defineKey: `${newObj[0].key.replace("_a", "_b")}`,
                                                defineButtons: editBT.OTS_B,
                                                dire: "r"
                                            });
                                        });
                                    } else if (cfg.type === "OMS") {
                                        const newObj = [];
                                        addNode(
                                            newDataList,
                                            cfg,
                                            () => {
                                                return createNewNode({
                                                    type: "OMS",
                                                    title: "OMS A",
                                                    keyCondition: "a",
                                                    defineButtons: []
                                                });
                                            },
                                            newObj
                                        );
                                        const newObj1 = [];
                                        addNode(
                                            newDataList,
                                            newObj[0],
                                            () => {
                                                return createNewNode({
                                                    type: "linkSpan",
                                                    defineKey: `${newObj[0].key.replace("_a", "_link")}`
                                                });
                                            },
                                            newObj1
                                        );
                                        addNode(newDataList, newObj1[0], () => {
                                            return createNewNode({
                                                type: "OMS",
                                                title: "OMS Z",
                                                defineKey: `${newObj[0].key.replace("_a", "_b")}`,
                                                defineButtons: editBT.OTS_B,
                                                dire: "r"
                                            });
                                        });
                                    } else {
                                        addNode(newDataList, cfg, item.target, null, btType);
                                    }
                                    setDataList(newDataList);
                                }}
                            >
                                <PlusOutlined />
                            </div>
                        );
                    }
                    if (item.type === "del" && (item.enabled || item.enabled === undefined)) {
                        if (item.enabled && typeof item.enabled === "function" && !item.enabled(values[cfg.key])) {
                            return;
                        }
                        return (
                            <div
                                key={`${key}del`}
                                title={labelList.delete}
                                onClick={() => {
                                    const newDataList = [...dataList];
                                    delNode(newDataList, cfg);
                                    if (cfg.type === "OTS" || cfg.type === "OMS") {
                                        delNode(newDataList, {
                                            key: cfg.key.replace(cfg.key.endsWith("_a") ? "_a" : "_b", "_link")
                                        });
                                        delNode(newDataList, {
                                            key: getOppoSideKey(cfg.key)
                                        });
                                        if (cfg.type === "OMS") {
                                            setChannelDisabled(false);
                                        }
                                    }
                                    setDataList(newDataList);
                                }}
                            >
                                <MinusOutlined />
                            </div>
                        );
                    }
                    if (
                        (item.type === "switch" || item.type === "oms") &&
                        (item.enabled || item.enabled === undefined)
                    ) {
                        return (
                            <div
                                key={`${key}switch`}
                                title={labelList.switch_a_z}
                                onClick={() => {
                                    const newDataList = [...dataList];
                                    const keyA = cfg.key;
                                    const keyB = getOppoSideKey(keyA);
                                    editNode(newDataList, keyA, {key: "tempKey"});
                                    editNode(newDataList, keyB, {key: keyA});
                                    editNode(newDataList, "tempKey", {key: keyB});
                                    setDataList(newDataList);
                                }}
                            >
                                <RetweetOutlined />
                            </div>
                        );
                    }
                })}
            </div>
        );
    };

    const createNode = cfg => {
        const contentDiv = createNodeContent(cfg);
        if (!contentDiv) {
            return;
        }
        return (
            <div
                key={`node_div_${cfg.key}`}
                className={["linkSpan", "OTS", "OMS", "OCH"].includes(cfg.type) ? styles.combineNode : styles.add_node}
            >
                {createButtons(cfg, "leftButtons")}
                {contentDiv}
                {createButtons(cfg, "buttons")}
            </div>
        );
    };

    let mainDivIndex = 0;
    let nodeGroup = [];
    const createNodes = data => {
        mainDivIndex++;
        return (
            <div key={`nodeMain${mainDivIndex}`} className={styles.chart}>
                {data.map((item, index) => {
                    if (item instanceof Array) {
                        return (
                            // eslint-disable-next-line react/no-array-index-key
                            <div key={`sublist_${mainDivIndex}${index}`} className={styles.node_dup_div}>
                                {item.map(sub => {
                                    if (sub instanceof Array) {
                                        return createNodes(sub);
                                    }
                                    return createNode(sub);
                                })}
                            </div>
                        );
                    }
                    if (["OTS", "OMS", "OCH"].includes(item.type) && !item.split) {
                        nodeGroup.push(item);
                        if (nodeGroup.length === 2) {
                            const nodeA = nodeGroup[0];
                            const nodeB = nodeGroup[1];
                            nodeGroup = [];
                            return (
                                <div key={nodeA.key + nodeB.key} className={styles.combineDiv}>
                                    {createNode(nodeA)}
                                    {createNode(nodeB)}
                                </div>
                            );
                        }
                    } else {
                        return createNode(item);
                    }
                })}
            </div>
        );
    };

    const updateConfigCache = () => {
        const cache = {...configCache};
        cache[modelActive] = dataList;
        setConfigCache(cache);
    };

    const clearFormData = (config, v) => {
        config.map(item => {
            if (item instanceof Array) {
                item.map(subItem => {
                    clearFormData(subItem, v);
                });
            } else {
                form.resetFields([
                    `${item.key}_group`,
                    `${item.key}_ots`,
                    `${item.key}_oms`,
                    `${item.key}_ne`,
                    `${item.key}_card`,
                    `${item.key}_port`,
                    `${item.key}_och`,
                    `${item.key}_apss`,
                    `${item.key}_apsp``${item.key}_raman-pre`,
                    `${item.key}_raman-post`,
                    `${item.key}_raman-pre-left`,
                    `${item.key}_raman-post-left`,
                    `${item.key}_raman-pre-right`,
                    `${item.key}_raman-post-right`
                ]);
                delete v[item.key];
            }
        });
        form.resetFields(["name"]);
    };

    return (
        <Form style={{marginTop: 15, overflowX: "auto"}} form={form} onFinish={saveFiber}>
            <div className={styles.container}>
                <div className={styles.buttons}>
                    <Form.Item
                        label={gLabelList.name}
                        labelCol={{style: {width: 87}}}
                        name="name"
                        rules={[
                            {required: true, message: gLabelList.required},
                            {
                                pattern: /^[\w+./:|~\u4e00-\u9fa5-]{1,128}$/,
                                message: gLabelList.connection_label_patter
                            }
                        ]}
                    >
                        <Input style={{width: 260}} />
                    </Form.Item>
                    <Form.Item label={gLabelList.service} labelCol={{style: {width: 87, marginLeft: 80}}}>
                        <Select
                            value={modelType}
                            style={{
                                width: 260
                            }}
                            onChange={v => {
                                updateConfigCache();
                                changeType(v);
                            }}
                            options={menuConfig.map(item => ({label: item.title, value: item.name}))}
                        />
                    </Form.Item>
                    {menuConfig
                        .filter(item => item.name === modelType)
                        .map(item =>
                            item.child.map(tag => {
                                return (
                                    <Button
                                        key={tag.key}
                                        style={{marginLeft: 40}}
                                        type={modelActive === tag.key ? "primary" : undefined}
                                        onClick={() => {
                                            updateConfigCache();
                                            setModelAction(tag.key);
                                            setDataList(configCache[tag.key] || tag.model);
                                        }}
                                    >
                                        {tag.title}
                                    </Button>
                                );
                            })
                        )}
                </div>
                {createNodes(dataList)}
            </div>
        </Form>
    );
});

export default Provision;
