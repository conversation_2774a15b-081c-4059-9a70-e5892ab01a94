import {useState, useEffect} from "react";
import {useSelector} from "react-redux";
import {message, Switch, Form} from "antd";
import {netconfGetByXML, objectGet, netconfByXML} from "@/modules-otn/apis/api";
import {sortArr} from "@/modules-otn/utils/util";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {useRequest} from "ahooks";
import SelectLoading from "@/modules-otn/components/common/select_loading";
import styles from "./pmp.module.scss";
import MutilColumnForm from "../common/mutil_column_form";

const Pmp = () => {
    const {labelList} = useSelector(state => state.languageOTN);
    const [selectNe, setSelectNe] = useState();
    const [commonData, setCommonData] = useState({allData: {}});
    const [dataSource, setDataSource] = useState([]);

    const [form] = Form.useForm();
    const [filterNeData, setFilterNeData] = useState([]);
    const [filterCardData, setFilterCardData] = useState([]);
    const [filterPortData, setFilterPortData] = useState([]);

    const userRight = useUserRight();

    const tableType = "pmp";

    const {run, refresh, loading} = useRequest(
        ne_id => {
            return netconfGetByXML({
                ne_id,
                msg: true,
                xml: {
                    performance: {
                        $: {
                            xmlns: "http://openconfig.net/yang/performance"
                        },
                        pmps: {}
                    }
                }
            });
        },
        {
            manual: true,
            onSuccess: rs => {
                setFilterNeData(rs?.performance?.pmps?.pmp);
            }
        }
    );

    useEffect(() => {
        const {card, port} = form.getFieldsValue();
        if (card && port) {
            const _cardData = [];
            const portData = filterNeData.reduce((prev, item) => {
                _cardData.push(item);
                const [, , R2, R3] = item["pm-point"].split("-");
                const [, , P2, P3] = port.split("-");
                if (R3 === P3 && R2 === P2) prev.push(item);
                return prev;
            }, []);
            const cardData = filterCard(_cardData, card);
            setFilterCardData(_cardData);
            setFilterPortData(cardData);
            setDataSource(portData);
        } else if (card) {
            const cardData = filterCard(filterCardData, card);
            setDataSource(cardData);
            setFilterCardData(filterNeData);
        } else {
            setDataSource(filterNeData);
            setFilterCardData(filterNeData);
        }
    }, [filterNeData]);

    const filterCard = (data, card) =>
        data.filter(item => {
            const [, chassis, slot] = item["pm-point"].split("-");
            const [, cardChassis, cardSlot] = card.split("-");
            return (!slot && item["pm-point"] === card) || (chassis === cardChassis && slot === cardSlot);
        });

    const onSelectNeChange = ne_id => {
        setSelectNe(ne_id);
        run(ne_id);
    };

    const PmpFormat = {
        // eslint-disable-next-line react/no-unstable-nested-components
        "pm-point-enable": (val, record) => {
            return (
                <LoadingSwitch
                    key={`${record["pm-point"]}pmp`}
                    type={tableType}
                    value={val}
                    record={record}
                    refresh={refresh}
                    commonData={commonData}
                    setCommonData={setCommonData}
                    disabled={
                        (commonData?.[tableType]?.selectedRows?.length > 0 &&
                            !commonData?.[tableType]?.selectedRows?.some(
                                item => item?.["pm-point"] === record["pm-point"]
                            )) ||
                        userRight.disabled
                    }
                    api={newVal => {
                        const pmps = [];
                        if (commonData?.[tableType]?.selectedRows?.length > 0) {
                            commonData?.[tableType]?.selectedRows?.forEach(selectedRow => {
                                pmps.push({
                                    pmp: {
                                        "pm-point": selectedRow["pm-point"],
                                        "pm-point-enable": `${newVal}`
                                    }
                                });
                            });
                        } else {
                            pmps.push({
                                pmp: {
                                    "pm-point": record["pm-point"],
                                    "pm-point-enable": `${newVal}`
                                }
                            });
                        }

                        return netconfByXML({
                            ne_id: selectNe,
                            xml: {
                                performance: {
                                    $: {
                                        xmlns: "http://openconfig.net/yang/performance"
                                    },
                                    pmps
                                }
                            }
                        });
                    }}
                />
            );
        },
        // eslint-disable-next-line react/no-unstable-nested-components
        "tca-enable": (val, record) => {
            return (
                <LoadingSwitch
                    key={`${record["pm-point"]}tca`}
                    type={tableType}
                    value={val}
                    record={record}
                    refresh={refresh}
                    commonData={commonData}
                    setCommonData={setCommonData}
                    disabled={
                        (commonData?.[tableType]?.selectedRows?.length > 0 &&
                            !commonData?.[tableType]?.selectedRows?.some(
                                item => item?.["pm-point"] === record["pm-point"]
                            )) ||
                        userRight.disabled
                    }
                    api={newVal => {
                        const pmps = [];
                        if (commonData?.[tableType]?.selectedRows?.length > 0) {
                            commonData?.[tableType]?.selectedRows?.forEach(selectedRow => {
                                pmps.push({
                                    pmp: {
                                        "pm-point": selectedRow["pm-point"],
                                        "tca-enable": `${newVal}`
                                    }
                                });
                            });
                        } else {
                            pmps.push({
                                pmp: {
                                    "pm-point": record["pm-point"],
                                    "tca-enable": `${newVal}`
                                }
                            });
                        }

                        return netconfByXML({
                            ne_id: selectNe,
                            xml: {
                                performance: {
                                    $: {
                                        xmlns: "http://openconfig.net/yang/performance"
                                    },
                                    pmps
                                }
                            }
                        });
                    }}
                />
            );
        }
    };

    return (
        <div style={{width: "100%"}} className={styles.container}>
            <div style={{width: "100%", marginBottom: 8}}>
                <MutilColumnForm
                    fields={[
                        {
                            name: "ne",
                            label: labelList.ne,
                            rules: [{required: true}],
                            render: (
                                <SelectLoading
                                    placeholder={labelList.select_ne}
                                    style={{width: 280}}
                                    fetchData={() => {
                                        return objectGet("config:ne", {}).then(rs => {
                                            const {apiResult, documents, apiMessage} = rs;
                                            if (apiResult === "fail") {
                                                throw new Error(apiMessage);
                                            }
                                            const options = sortArr(documents, ["value", "name"]).map(item => ({
                                                label: item.value.name,
                                                value: item.value.ne_id,
                                                disabled: !(item?.value.state === 1 || item?.value.state < 0)
                                            }));
                                            return options;
                                        });
                                    }}
                                    onChange={onSelectNeChange}
                                />
                            )
                        },
                        {
                            name: "card",
                            label: labelList.slot,
                            render: (
                                <SelectLoading
                                    allowClear
                                    placeholder={labelList.please_select}
                                    dependence={filterCardData}
                                    style={{width: 280}}
                                    fetchData={() => {
                                        const options = filterCardData
                                            .reduce((pre, item) => {
                                                const [, chassis, slot] = item["pm-point"].split("-");
                                                const card = slot ? `SLOT-${chassis}-${slot}` : null;
                                                if (typeof card === "string" && !pre.find(f => f.value === card)) {
                                                    pre.push({label: card, value: card});
                                                }
                                                return pre;
                                            }, [])
                                            .sort((a, b) => a.label.localeCompare(b.label, "ZH-CN", {numeric: true}));
                                        return options;
                                    }}
                                    onChange={card => {
                                        if (form.getFieldsValue()?.port) {
                                            form.setFieldsValue({...form.getFieldsValue(), port: undefined});
                                        }
                                        if (card) {
                                            const cardData = filterCard(filterCardData, card);
                                            setDataSource(cardData);
                                            setFilterPortData(cardData);
                                        } else {
                                            setDataSource(filterCardData);
                                            setFilterPortData([]);
                                        }
                                    }}
                                />
                            )
                        },
                        {
                            name: "port",
                            label: labelList.port,
                            render: (
                                <SelectLoading
                                    allowClear
                                    style={{width: 280}}
                                    placeholder={labelList.please_select}
                                    dependence={filterPortData}
                                    fetchData={() => {
                                        const options = filterPortData
                                            .reduce((pre, item) => {
                                                const ports = item["pm-point"]?.split("-");
                                                ports[0] = "PORT";
                                                const port = ports.join("-");
                                                if (ports?.[3] && !ports?.[4] && !pre.find(f => f.value === port)) {
                                                    pre.push({label: port, value: port});
                                                }
                                                return pre;
                                            }, [])
                                            .sort((a, b) => a.label.localeCompare(b.label, "ZH-CN", {numeric: true}));
                                        return options;
                                    }}
                                    onChange={port => {
                                        if (port) {
                                            const portData = filterPortData.filter(
                                                item => item["pm-point"]?.split("-")?.[3] === port.split("-")[3]
                                            );
                                            setDataSource(portData);
                                        } else {
                                            setDataSource(filterPortData);
                                        }
                                    }}
                                />
                            )
                        }
                    ]}
                />
            </div>
            <CustomTable
                initHead
                type={tableType}
                scroll={false}
                initDataSource={dataSource}
                loading={loading}
                commonData={commonData}
                setCommonData={setCommonData}
                columnFormat={PmpFormat}
                refreshParent={refresh}
                refreshDisabled={!selectNe}
            />
        </div>
    );
};

export default Pmp;

const LoadingSwitch = ({api, value, record, refresh, commonData, setCommonData, type, ...rest}) => {
    const {labelList} = useSelector(state => state.languageOTN);
    const [val, setVal] = useState(value === "true");
    const {run, loading} = useRequest(api, {
        manual: true,
        onSuccess(response, params) {
            if (response.result) {
                message.success(labelList.save_success);
                if (commonData?.[type]?.selectedRows?.length > 0) {
                    refresh();
                } else {
                    setVal(params[0]);
                }
            } else {
                message
                    .error({
                        content: response.apiMessage,
                        key: "error2",
                        duration: 0,
                        onClick: () => message.destroy("error2")
                    })
                    .then();
                refresh();
            }
            setCommonData({});
        }
    });

    const onChange = newVal => {
        run(newVal);
    };

    useEffect(() => {
        setVal(value === "true");
    }, [value, record]);

    return <Switch loading={loading} checked={val} onChange={onChange} {...rest} />;
};
