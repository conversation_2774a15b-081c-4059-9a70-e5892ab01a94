import {useEffect, useRef} from "react";
import * as echarts from "echarts";
import dayjs from "dayjs";

import {upperFirst} from "lodash";
import {sortArr} from "@/modules-otn/utils/util";
import {gLabelList} from "@/store/modules/otn/languageOTNSlice";

export default function DataCharts({tableSources, checkedChartKey}) {
    const chartContainerRef = useRef(null);

    useEffect(() => {
        const _data = sortArr(
            tableSources
                .filter(item => item["pm-parameter"] === checkedChartKey)
                .map(item => ({
                    ...item,
                    "monitoring-date-time": dayjs(item["monitoring-date-time"]).format("YYYY-MM-DD HH:mm:ss")
                })),
            ["monitoring-date-time"]
        );
        const chartData = [];
        let max = 0;
        _data.forEach(item => {
            const {
                "monitoring-date-time": time,
                "pm-point": pmPoint,
                "current-value": currentValue,
                "max-value": maxValue,
                "min-value": minValue,
                "average-value": averageValue
            } = item;
            max = Math.max(max, currentValue ?? 0, maxValue ?? 0, minValue ?? 0, averageValue ?? 0);

            const findItem = chartData.find(item => item.product === time);
            if (findItem) {
                if (Object.hasOwn(item, "current-value")) findItem[`${pmPoint}/instant`] = currentValue;
                if (Object.hasOwn(item, "max-value")) findItem[`${pmPoint}/max`] = currentValue;
                if (Object.hasOwn(item, "min-value")) findItem[`${pmPoint}/min`] = currentValue;
                if (Object.hasOwn(item, "average-value")) findItem[`${pmPoint}/average`] = currentValue;
            } else {
                chartData.push({
                    product: time,
                    ...(Object.hasOwn(item, "current-value") && {[`${pmPoint}/instant`]: currentValue}),
                    ...(Object.hasOwn(item, "max-value") && {[`${pmPoint}/max`]: maxValue}),
                    ...(Object.hasOwn(item, "min-value") && {[`${pmPoint}/min`]: minValue}),
                    ...(Object.hasOwn(item, "average-value") && {[`${pmPoint}/average`]: averageValue})
                });
            }
        });
        const products = chartData.reduce((keys, item) => {
            Object.keys(item).forEach(key => {
                if (key !== "product" && !keys.includes(key)) keys.push(key);
            });
            return keys;
        }, []);

        const myChart = echarts.init(chartContainerRef.current, null, {});
        myChart.setOption({
            legend: {
                orient: "horizontal",
                type: "scroll",
                let: "center", // title的位置
                icon: "roundRect",
                itemWidth: 10,
                itemHeight: 10,
                formatter(name) {
                    return formatLegendText(name);
                },
                textStyle: {
                    color: "#212519",
                    fontWeight: 400,
                    fontSize: 14,
                    fontFamily: "Lato, Lato"
                },
                width: "90%"
            },
            grid: {top: "20%", left: "8%", right: "5%", bottom: "10%"},
            tooltip: {
                trigger: "axis",
                enterable: true,
                hideDelay: 200, // 浮层隐藏的延迟
                confine: true,
                formatter(params) {
                    let astr = "";
                    let _axisValue = null;
                    params.forEach(ele => {
                        const {data, seriesName, axisValue} = ele;
                        if (_axisValue === null) _axisValue = axisValue;
                        if (data[seriesName] !== 0 && !data[seriesName]) return;
                        astr += `
                            <div style="display: block;height:20px;width: 100%;float:left;">
                                <i style="width: 10px;height: 10px;display: inline-block;background: ${ele.color};border-radius: 2px;"></i>
                                <span>${formatLegendText(ele.seriesName)}: ${data[seriesName]}</span>
                            </div>
                        `;
                    });
                    if (astr === "") {
                        astr = `<div style="display: block;height:20px;width: 100%;float:left;">No Data</div>`;
                    }
                    const b = `
                        <div style="width: 220px; max-height: 240px;overflow-y: ${
                            params.length > 10 ? "auto" : "hidden"
                        }; overflow-x: auto;">
                            <div style="display: block;height:20px;width: 100%;float:left;margin-bottom: 10px;color:#000000;">${_axisValue}</div>${astr}
                        </div>
                    `;
                    return b;
                }
            },
            dataset: {
                source: chartData
            },
            xAxis: {type: "category"},
            yAxis: {},
            series: new Array(products.length).fill({
                symbol: "none", // 折线的点
                type: "line",
                smooth: true
            })
        });

        const resizeObserver = new ResizeObserver(() => {
            myChart.resize();
        });

        if (chartContainerRef.current) {
            resizeObserver.observe(chartContainerRef.current);
        }

        return () => {
            if (chartContainerRef.current) {
                resizeObserver.unobserve(chartContainerRef.current);
            }
        };
    }, []);

    return <div ref={chartContainerRef} style={{width: "100%", height: "100%"}} />;
}

function formatLegendText(text) {
    const splitText = text.split("/");
    return [...splitText.slice(0, -1), upperFirst(splitText.at(-1))].join("/");
}
