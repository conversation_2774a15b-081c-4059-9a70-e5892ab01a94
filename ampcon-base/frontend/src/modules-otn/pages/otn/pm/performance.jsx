import {Tabs} from "antd";
import {useSelector} from "react-redux";
import {useLocation, useNavigate} from "react-router-dom";
import {useEffect, useState} from "react";
import serviceLayer0Styles from "@/modules-otn/pages/otn/service/service_layer0.module.scss";
import PM from "./pm";
import Pmp from "./pmp";

export default function Performance() {
    const {labelList} = useSelector(state => state.languageOTN);
    const items = [
        {
            key: "current",
            label: labelList.current,
            children: <PM pmType="CURRENT" />
        },
        {
            key: "history",
            label: labelList.history,
            children: <PM pmType="HISTORY" />
        },
        {
            key: "pmp",
            label: labelList.pmp,
            children: <Pmp />
        }
    ];

    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();
    const pathReg = /(current|history|pmp)$/;
    useEffect(() => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(pathReg)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            const matchLength = currentPath.match(pathReg)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        <Tabs
            items={items}
            destroyInactiveTabPane
            style={{flex: 1, width: "100%"}}
            className={serviceLayer0Styles.tabs}
            tabBarStyle={{paddingLeft: 16}}
            activeKey={currentActiveKey}
            onChange={onChange}
        />
    );
}
