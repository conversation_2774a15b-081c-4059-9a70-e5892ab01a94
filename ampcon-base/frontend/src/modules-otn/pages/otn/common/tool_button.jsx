import React, {useState} from "react";
import Icon from "@ant-design/icons";

const ToolButton = ({enabledIcon, disabledIcon, onFocusIcon, title, disabled, onClick, confirm, style}) => {
    const [buttonOnFocus, setButtonOnFocus] = useState(false);
    let buttonIcon = disabledIcon;
    if (!disabled) {
        buttonIcon = buttonOnFocus ? onFocusIcon : enabledIcon;
    }
    const iconButton = (
        <span title={title} style={style}>
            <Icon
                onClick={() => {
                    if (!confirm && !disabled) {
                        onClick();
                    }
                }}
                component={buttonIcon}
                onMouseEnter={() => {
                    if (buttonOnFocus) {
                        return;
                    }
                    setButtonOnFocus(true);
                }}
                onMouseLeave={() => {
                    if (!buttonOnFocus) {
                        return;
                    }
                    setButtonOnFocus(false);
                }}
                style={{cursor: disabled ? "not-allowed" : "pointer"}}
            />
        </span>
    );
    // if (confirm) {
    //     return (
    //         <Popconfirm
    //             title={confirm}
    //             disabled={disabled}
    //             onConfirm={event => {
    //                 if (!disabled) {
    //                     onClick();
    //                 }
    //             }}
    //         >
    //             {iconButton}
    //         </Popconfirm>
    //     );
    // }
    return iconButton;
};

export default ToolButton;
