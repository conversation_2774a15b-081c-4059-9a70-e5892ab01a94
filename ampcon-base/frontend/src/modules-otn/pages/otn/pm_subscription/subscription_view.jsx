import {Tabs} from "antd";
import {useSelector} from "react-redux";
import {useLocation, useNavigate} from "react-router-dom";
import {useEffect, useState} from "react";
import ServiceLayer0Styles from "@/modules-otn/pages/otn/service/service_layer0.module.scss";
import Subscription from "./subscription";
import Sensor from "./sensor";
import Destination from "./destination";
import DataView from "./view";
import ResetCounter from "./reset_counter";

export default function Performance() {
    const {labelList} = useSelector(state => state.languageOTN);
    const items = [
        {
            key: "sensor_group",
            label: labelList.sensor_group,
            children: <Sensor />
        },
        {
            key: "destination-groups",
            label: labelList["destination-groups"],
            children: <Destination />
        },
        {
            key: "subscription",
            label: labelList.subscription,
            children: <Subscription />
        },
        {
            key: "reset_counter",
            label: labelList.reset_counter,
            children: <ResetCounter />
        },
        {
            key: "data_view",
            label: "Data View",
            children: <DataView />
        }
    ];

    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();
    const pathReg = /(sensor_group|destination-groups|subscription|reset_counter|data_view)$/;
    useEffect(() => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(pathReg)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (pathReg.test(currentPath)) {
            const matchLength = currentPath.match(pathReg)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        <Tabs
            items={items}
            destroyInactiveTabPane
            activeKey={currentActiveKey}
            onChange={onChange}
            className={ServiceLayer0Styles.tabs}
        />
    );
}
