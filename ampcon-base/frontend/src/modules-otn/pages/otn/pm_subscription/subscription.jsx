import {useSelector} from "react-redux";
import {useEffect, useState} from "react";
import {message, Tag} from "antd";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {openDBModalCreate} from "@/modules-otn/components/form/create_form_db";
import {convertToArray} from "@/modules-otn/utils/util";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import Icon from "@ant-design/icons";
import {plusDisableIcon, plusIcon} from "@/modules-otn/pages/otn/device/device_icons";
import {clearTelemetryData, netconfByXML, netconfChange, objectGet} from "@/modules-otn/apis/api";

const Subscription = () => {
    const {labelList} = useSelector(state => state.languageOTN);
    const {neNameMap} = useSelector(state => state.neName);
    const userRight = useUserRight();
    const [data, setData] = useState([]);
    const {dataChanged} = useSelector(state => state.notification);

    const loadData = () => {
        try {
            objectGet("ne:5:persistent-subscription").then(rs => {
                setData(
                    rs.documents.map(item => {
                        return {
                            ...item.value.data.state,
                            name: item.value.data.name,
                            "sensor-profiles": item.value.data["sensor-profiles"],
                            "destination-groups": item.value.data["destination-groups"],
                            ne_id: item.value.ne_id,
                            ne_name: neNameMap[item.value.ne_id],
                            ...convertToArray(item.value.data["sensor-profiles"]?.["sensor-profile"])?.[0]?.config
                        };
                    })
                );
            });
        } catch (e) {
            // console.log(e);
        }
    };

    useEffect(() => {
        loadData();
    }, [neNameMap]);

    useEffect(() => {
        if (dataChanged.data?.type === "telemetry-system") {
            loadData();
        }
    }, [dataChanged]);

    return (
        <CustomTable
            type="persistent-subscription"
            initTitle=""
            initDataSource={data}
            refreshParent={loadData}
            scroll={false}
            buttons={[
                {
                    label: "create",
                    icon: <Icon component={userRight.disabled ? plusDisableIcon : plusIcon} />,
                    type: "primary",
                    disabled: userRight.disabled,
                    onClick() {
                        openDBModalCreate({
                            type: "create-persistent-subscription",
                            submit: async (values, defaultValue, diffValue, cancel, fail) => {
                                const rs = (
                                    await objectGet("ne:5:persistent-subscription", {
                                        ne_id: values.ne_id,
                                        name: values.name
                                    })
                                ).documents;
                                if (rs.length > 0) {
                                    message.error(labelList.same_name);
                                    fail({});
                                    return;
                                }

                                const allsbc = (
                                    await objectGet("ne:5:persistent-subscription", {
                                        ne_id: values.ne_id
                                    })
                                ).documents;

                                let dgFound = false;
                                let dgFoundName;
                                for (let i = 0; i < allsbc.length; i++) {
                                    const f = convertToArray(
                                        allsbc[i].value.data["destination-groups"]["destination-group"]
                                    ).filter(s => values["destination-groups"].includes(s["group-id"]));
                                    if (f.length > 0) {
                                        dgFoundName = f[0]["group-id"];
                                        dgFound = true;
                                        break;
                                    }
                                }

                                let spFound = false;
                                let spFoundName = false;
                                for (let i = 0; i < allsbc.length; i++) {
                                    const f = convertToArray(
                                        allsbc[i].value.data["sensor-profiles"]["sensor-profile"]
                                    ).filter(s => values["sensor-profiles"].includes(s["sensor-group"]));
                                    if (f.length > 0) {
                                        spFound = true;
                                        spFoundName = f[0]["sensor-group"];
                                        break;
                                    }
                                }

                                if (spFound && dgFound) {
                                    message.error(labelList.subscribe_exist.format(spFoundName, dgFoundName));
                                    fail({});
                                    return;
                                }

                                await netconfByXML({
                                    ne_id: values.ne_id,
                                    msg: true,
                                    action: "create",
                                    timeout: 60 * 1000,
                                    sync: {type: "telemetry-system"},
                                    xml: {
                                        "telemetry-system": {
                                            $: {
                                                xmlns: "http://openconfig.net/yang/telemetry"
                                            },
                                            subscriptions: {
                                                "persistent-subscriptions": {
                                                    "persistent-subscription": {
                                                        name: values.name,
                                                        config: {
                                                            name: values.name,
                                                            "local-source-address": values?.["local-source-address"],
                                                            "local-source-port": values?.["local-source-port"],
                                                            "originated-qos-marking":
                                                                values?.["originated-qos-marking"],
                                                            protocol: values?.protocol,
                                                            encoding: values?.encoding
                                                        },
                                                        "sensor-profiles": {
                                                            "sensor-profile": values?.["sensor-profiles"]?.map(item => {
                                                                return {
                                                                    "sensor-group": item,
                                                                    config: {
                                                                        "sensor-group": item,
                                                                        "sample-interval": values?.["sample-interval"],
                                                                        "heartbeat-interval":
                                                                            values?.["heartbeat-interval"],
                                                                        "suppress-redundant":
                                                                            values?.["suppress-redundant"]
                                                                    }
                                                                };
                                                            })
                                                        },
                                                        "destination-groups": {
                                                            "destination-group": values?.["destination-groups"]?.map(
                                                                item => {
                                                                    return {
                                                                        "group-id": item,
                                                                        config: {
                                                                            "group-id": item
                                                                        }
                                                                    };
                                                                }
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    },
                                    success: async () => {
                                        await clearTelemetryData({
                                            ne_id: values.ne_id,
                                            sensors: values["sensor-profiles"],
                                            destinations: values["destination-groups"]
                                        });
                                        cancel(true);
                                        loadData();
                                    },
                                    fail: () => {
                                        fail({});
                                    }
                                });
                            }
                        });
                    }
                }
            ]}
            initRowOperation={[
                {
                    label: labelList.del,
                    confirm: {
                        title: labelList.delete_confirm
                    },
                    onClick() {
                        const originValue = this;
                        netconfChange({
                            ne_id: originValue.ne_id,
                            operation: "delete",
                            entity: "persistent-subscription",
                            sync: {type: "telemetry-system"},
                            keys: [originValue.name],
                            values: {
                                config: {}
                            },
                            msg: false,
                            success: rs => {
                                if (rs.result) {
                                    message.success(labelList.delete_success);
                                } else {
                                    message.error(labelList.delete_fail);
                                }
                                setTimeout(() => {
                                    loadData();
                                }, 1000);
                            }
                        }).then();
                    },
                    disabled: userRight?.disabled ? () => true : undefined
                }
            ]}
            columnFormat={{
                "sensor-profiles": data => {
                    try {
                        const tagList = [];
                        if (data && data["sensor-profile"]) {
                            convertToArray(data["sensor-profile"]).map(item => {
                                // const tagText = encodeSensorPath(item.path);
                                tagList.push(
                                    <Tag
                                        key={item["sensor-group"]}
                                        style={{
                                            margin: "0 8px 0 0",
                                            padding: "0 7px",
                                            background: "#14C9BB1A",
                                            color: "#14C9BB",
                                            borderColor: "#14C9BB"
                                        }}
                                    >
                                        {item["sensor-group"]}
                                    </Tag>
                                );
                            });
                        }
                        return tagList;
                    } catch (e) {
                        // console.log(e);
                    }
                },
                "destination-groups": data => {
                    try {
                        const tagList = [];
                        if (data && data["destination-group"]) {
                            convertToArray(data["destination-group"]).map(item => {
                                tagList.push(
                                    <Tag
                                        key={item["group-id"]}
                                        style={{
                                            margin: "0 8px 0 0",
                                            padding: "0 7px",
                                            background: "#14C9BB1A",
                                            color: "#14C9BB",
                                            borderColor: "#14C9BB"
                                        }}
                                    >
                                        {item["group-id"]}
                                    </Tag>
                                );
                            });
                        }
                        return tagList;
                    } catch (e) {
                        // console.log(e);
                    }
                }
            }}
        />
    );
};

export default Subscription;
