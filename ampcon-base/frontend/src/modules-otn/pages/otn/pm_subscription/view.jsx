import {useState, useRef, useLayoutEffect} from "react";
import {Button, Form, DatePicker, message, Space, Row, Col} from "antd";
import {useRequest} from "ahooks";
import {useSelector} from "react-redux";
import dayjs from "dayjs";
import * as echarts from "echarts";
import {getTelemetryComponents, getTelemetry, apiGetSafeNeList} from "@/modules-otn/apis/api";
import {sortArr, sensorPathList} from "@/modules-otn/utils/util";
import {chunk, max, upperFirst} from "lodash";
import SelectLoading from "@/modules-otn/components/common/select_loading";
import CustomSearchSelect from "@/modules-otn/components/select/custom_search_select";
import styles from "./view.module.scss";

const typeOptions = sortArr(Object.keys(sensorPathList)).map(item => ({value: item, label: item.toUpperCase()}));

export default function TelemeryView() {
    const [chart, setChart] = useState(null);

    const containerRef = useRef(null);

    const {runAsync: getTelemetryRun, refresh} = useRequest(
        values => {
            return getTelemetry(values);
        },
        {
            manual: true,
            onSuccess(res) {
                const {apiResult, data, sampleInterval} = res;
                if (apiResult === "success") {
                    chart?.dispose();
                    if (data.length === 0) {
                        return;
                    }
                    const _data = [];
                    const insertArr = [];
                    data.forEach((item, index) => {
                        const {id: time, message} = item;
                        const formatTime = dayjs(Number(time.slice(0, -2))).format("YYYY-MM-DD HH:mm:ss");
                        _data.push({product: formatTime, ...message});
                        if (sampleInterval && index !== data.length - 1) {
                            const currentTime = Number(time.slice(0, -2));
                            const nextTime = Number(data[index + 1].id.slice(0, -2));
                            const timeInterval = Math.abs(currentTime - nextTime);
                            if (Math.abs(timeInterval - sampleInterval) >= sampleInterval) {
                                insertArr.push({
                                    index,
                                    date: {
                                        product: dayjs(currentTime + sampleInterval).format("YYYY-MM-DD HH:mm:ss")
                                    }
                                });
                            }
                        }
                    });

                    insertArr.forEach((item, index) => {
                        _data.splice(index + item.index + 1, 0, item.date);
                    });

                    const myChart = echarts.init(containerRef.current, null, {
                        width: "auto",
                        height: "auto"
                    });

                    myChart.setOption({
                        legend: {
                            orient: "horizontal", // title行的排列方式
                            type: "scroll", // title行滚动的显示方式
                            let: "center",
                            icon: "roundRect",
                            itemWidth: 10,
                            itemHeight: 10,
                            formatter(name) {
                                return upperFirst(name);
                            },
                            textStyle: {
                                color: "#212519",
                                fontWeight: 400,
                                fontSize: 14,
                                fontFamily: "Lato, Lato"
                            },
                            width: "90%"
                        },
                        color: ["#14C9BB", "#FFBB00", "#00B7FF"],
                        tooltip: {
                            trigger: "axis",
                            enterable: true,
                            hideDelay: 200, // 浮层隐藏的延迟
                            confine: true,
                            formatter(params) {
                                let astr = "";
                                let _axisValue = null;
                                params.forEach(ele => {
                                    const {data, seriesName, axisValue} = ele;
                                    if (_axisValue === null) _axisValue = axisValue;
                                    astr += `
                                                        <div style="display: block;height:20px;width: 100%;float:left;">
                                                            <i style="width: 10px;height: 10px;display: inline-block;background: ${
                                                                ele.color
                                                            };border-radius: 2px;"></i>
                                                            <span>${upperFirst(ele.seriesName)}: ${data[seriesName] ?? ""}</span>
                                                        </div>
                                                    `;
                                });
                                const b = `
                                                    <div style="width: 220px; max-height: 240px;overflow-y: ${
                                                        params.length > 10 ? "auto" : "hidden"
                                                    }; overflow-x: hidden;">
                                                        <div style="display: block;height:20px;width: 100%;float:left;">${_axisValue}</div>${astr}
                                                    </div>
                                                `;
                                return b;
                            }
                        },
                        dataZoom: [
                            {
                                type: "inside",
                                start: 0,
                                end: 100
                            },
                            {
                                start: 0,
                                end: 100
                            }
                        ],
                        dataset: {
                            source: _data
                        },
                        xAxis: {
                            type: "category",
                            axisLabel: {
                                align: "left"
                            }
                        },
                        yAxis: {},
                        series: new Array(Object.keys(_data.at(0)).length - 1).fill({
                            symbol: "none", // 折线的点
                            type: "line",
                            smooth: true
                        })
                    });
                    setChart(myChart);
                }
                if (apiResult === "fail") {
                    message.error(apiResult);
                }
            },
            onError(error) {
                message.error(error?.message);
            }
        }
    );

    const onSearch = values => {
        const {startTime, endTime, ne_id} = values;
        const query = {
            ...values,
            ne_id: ne_id.split(":").at(0),
            startTime: startTime ? startTime.valueOf() : startTime,
            endTime: endTime ? endTime.valueOf() : endTime
        };
        getTelemetryRun(query);
    };

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <SearchForm onSearch={onSearch} refresh={refresh} />
            </div>
            <div style={{flex: 1}} ref={containerRef} />
        </div>
    );
}

const SearchForm = ({refresh, onSearch}) => {
    const {labelList} = useSelector(state => state.languageOTN);
    const [form] = Form.useForm();
    const [labelMaxWidth, setLabelMaxWidth] = useState(0);
    const type = Form.useWatch("type", form);

    const formItem = [
        {
            name: "ne_id",
            label: labelList.ne,
            rules: [{required: true, message: labelList.required}],
            render: (
                <SelectLoading
                    style={{width: 280}}
                    fetchData={() => {
                        return apiGetSafeNeList().then(rs => {
                            const {apiResult, apiMessage, data} = rs;
                            if (apiResult === "fail") throw new Error(apiMessage);
                            return sortArr(
                                data.map(item => ({
                                    label: item.name,
                                    value: item.ne_id,
                                    key: item.ne_id
                                })),
                                ["label"]
                            );
                        });
                    }}
                    onChange={() => {
                        form.setFieldsValue({type: undefined, component_id: undefined});
                    }}
                    noCache
                    placeholder={labelList.select_ne}
                />
            )
        },
        {
            name: "type",
            label: labelList.type,
            rules: [{required: true, message: labelList.required}],
            render: (
                <CustomSearchSelect
                    style={{width: 280}}
                    placeholder={labelList.select_type}
                    options={typeOptions}
                    popupMatchSelectWidth={false}
                    onChange={() => {
                        form.setFieldValue("component_id", undefined);
                    }}
                />
            )
        },
        {
            name: "component_id",
            label: labelList.value,
            rules: [{required: true, message: labelList.required}],
            render: (
                <SelectLoading
                    style={{width: 280}}
                    placeholder={labelList.select_value}
                    dependence={type}
                    popupMatchSelectWidth={false}
                    fetchData={async () => {
                        try {
                            const values = await form.validateFields(["ne_id", "type"]);
                            const {ne_id, type} = values;

                            return getTelemetryComponents({ne_id: ne_id.split(":").at(0), type}).then(res => {
                                const {apiResult, apiMessage, data} = res;
                                if (apiResult === "fail") throw new Error(apiMessage);
                                return sortArr(
                                    data.map(item => ({label: item, value: item, key: item})),
                                    ["label"]
                                );
                            });
                        } catch (e) {
                            return [];
                        }
                    }}
                />
            )
        },
        {
            name: "startTime",
            label: labelList.start_time,
            render: (
                <DatePicker
                    style={{width: 280}}
                    format={time => dayjs(time.$d).format("YYYY-MM-DD HH:mm:ss")}
                    placeholder={labelList.select_start_time}
                    showTime
                />
            )
        },
        {
            name: "endTime",
            label: labelList.end_time,
            render: (
                <DatePicker
                    style={{width: 280}}
                    format={time => dayjs(time.$d).format("YYYY-MM-DD HH:mm:ss")}
                    placeholder={labelList.select_end_time}
                    showTime
                />
            )
        }
    ];

    const handleSearch = () => {
        form.validateFields()
            .then(values => {
                onSearch(values);
            })
            .catch(() => {});
    };

    const buttons = {
        label: "action",
        render: (
            <Space size={16} style={{width: 280}}>
                <Button onClick={handleSearch} type="primary">
                    {labelList.search_title}
                </Button>
                <Button onClick={refresh}>{labelList.refresh}</Button>
            </Space>
        )
    };

    useLayoutEffect(() => {
        const maxWidth =
            max(
                Array.from(document.querySelectorAll(".ant-form-item-label > label")).map(item => {
                    return parseInt(item.offsetWidth);
                })
            ) + 32;
        setLabelMaxWidth(maxWidth);
    }, []);
    return (
        <Form
            name="searchform"
            form={form}
            labelAlign="left"
            style={{display: "flex", flexWrap: "wrap", columnGap: 80}}
        >
            {formItem.map(rowItems => {
                const {render, ...rest} = rowItems;
                return (
                    <Form.Item
                        {...rest}
                        labelCol={{style: {...(labelMaxWidth ? {width: labelMaxWidth} : {})}}}
                        wrapperCol={{flex: "0 0 280px"}}
                        style={{width: "auto"}}
                    >
                        {render}
                    </Form.Item>
                );
            })}
            {buttons && (
                <Form.Item
                    label={buttons.label}
                    labelCol={{style: {...(labelMaxWidth ? {width: labelMaxWidth, visibility: "hidden"} : {})}}}
                    wrapperCol={{flex: "0 0 280px"}}
                    style={{width: "100%"}}
                >
                    {buttons.render}
                </Form.Item>
            )}
        </Form>
    );
};
