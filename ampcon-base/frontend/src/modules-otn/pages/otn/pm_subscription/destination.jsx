import {useSelector} from "react-redux";
import {message, Tag, theme} from "antd";
import {useEffect, useState} from "react";
import useUserRight from "@/modules-otn/hooks/useUserRight";
import {createDestinationGroup, netconfByXML, netconfChange, objectGet} from "@/modules-otn/apis/api";
import {CustomTable} from "@/modules-otn/components/common/custom_table";
import {openDBModalCreate} from "@/modules-otn/components/form/create_form_db";
import {convertToArray} from "@/modules-otn/utils/util";
import Icon from "@ant-design/icons";
import {plusDisableIcon, plusIcon} from "@/modules-otn/pages/otn/device/device_icons";

const defaultPort = "1000";

const Destination = () => {
    const {labelList} = useSelector(state => state.languageOTN);
    const {neNameMap} = useSelector(state => state.neName);
    const userRight = useUserRight();
    const [data, setData] = useState([]);
    const {dataChanged} = useSelector(state => state.notification);

    const {
        token: {colorPrimary}
    } = theme.useToken();

    const loadData = () => {
        try {
            objectGet("ne:5:destination-group").then(rs => {
                setData(
                    rs.documents.map(item => {
                        return {
                            ...item.value.data,
                            ne_id: item.value.ne_id,
                            ne_name: neNameMap[item.value.ne_id]
                        };
                    })
                );
            });
        } catch (e) {
            // console.log(e);
        }
    };

    useEffect(() => {
        loadData();
    }, [neNameMap]);

    useEffect(() => {
        if (dataChanged.data?.type === "telemetry-system") {
            loadData();
        }
    }, [dataChanged]);

    return (
        <CustomTable
            type="destination-groups"
            initTitle=""
            scroll={false}
            initDataSource={data}
            refreshParent={loadData}
            buttons={[
                {
                    label: "create",
                    icon: <Icon component={userRight.disabled ? plusDisableIcon : plusIcon} />,
                    type: "primary",
                    disabled: userRight.disabled,
                    onClick() {
                        openDBModalCreate({
                            type: "create-destination-groups",
                            submit: async (values, defaultValue, diffValue, cancel, fail) => {
                                const rs = (
                                    await objectGet("ne:5:destination-group", {
                                        ne_id: values.ne_id,
                                        group_id: values["group-id"]
                                    })
                                ).documents;
                                if (rs.length > 0) {
                                    message.error(labelList.same_group_id);
                                    fail({});
                                    return;
                                }

                                if (values?.default_tnms_server?.length > 0) {
                                    await createDestinationGroup({
                                        ne_id: values.ne_id,
                                        group_id: values["group-id"],
                                        msg: true,
                                        timeout: 60 * 1000,
                                        success: () => {
                                            cancel(true);
                                            loadData();
                                        },
                                        fail: () => {
                                            fail({});
                                        }
                                    });
                                    return;
                                }
                                if (!values.destinations) {
                                    fail({});
                                    message.error(labelList.right_destinations);
                                    return;
                                }
                                const ds = convertToArray(values.destinations);
                                for (let i = 0; i < ds.length; i++) {
                                    if (
                                        !/((2(5[0-5]|[0-4]\d))|1?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|1?\d{1,2})){3}:\d{1,5}/g.test(
                                            ds[i]
                                        )
                                    ) {
                                        fail({});
                                        message.error(labelList.right_destinations);
                                        return;
                                    }
                                }
                                await netconfByXML({
                                    ne_id: values.ne_id,
                                    msg: true,
                                    action: "create",
                                    timeout: 60 * 1000,
                                    sync: {type: "telemetry-system"},
                                    xml: {
                                        "telemetry-system": {
                                            $: {
                                                xmlns: "http://openconfig.net/yang/telemetry"
                                            },
                                            "destination-groups": {
                                                "destination-group": {
                                                    "group-id": values["group-id"],
                                                    config: {
                                                        "group-id": values["group-id"]
                                                    },
                                                    destinations: {
                                                        destination: [values?.destinations]?.map(item => {
                                                            const _temp = item.split(":");
                                                            return {
                                                                "destination-address": _temp[0],
                                                                "destination-port": _temp[1] ?? defaultPort,
                                                                config: {
                                                                    "destination-address": _temp[0],
                                                                    "destination-port": _temp[1] ?? defaultPort
                                                                }
                                                            };
                                                        })
                                                    }
                                                }
                                            }
                                        }
                                    },
                                    success: () => {
                                        cancel(true);
                                        loadData();
                                    },
                                    fail: () => {
                                        fail({});
                                    }
                                });
                            }
                        });
                    }
                }
            ]}
            initRowOperation={[
                {
                    label: labelList.del,
                    confirm: {
                        title: labelList.delete_confirm
                    },
                    onClick() {
                        const originValue = this;
                        objectGet("ne:5:persistent-subscription", {ne_id: originValue.ne_id}).then(rs => {
                            let found = false;
                            for (let i = 0; i < rs.documents.length; i++) {
                                const subscriptionDestinations = convertToArray(
                                    rs.documents[i].value.data?.["destination-groups"]?.["destination-group"]
                                );
                                if (
                                    subscriptionDestinations.filter(
                                        item => item["group-id"] === originValue["group-id"]
                                    ).length > 0
                                ) {
                                    found = true;
                                    break;
                                }
                            }
                            if (found) {
                                message.error(labelList.destination_del_warning).then();
                                return;
                            }
                            netconfChange({
                                ne_id: originValue.ne_id,
                                operation: "delete",
                                entity: "destination-group",
                                keys: [originValue["group-id"]],
                                sync: {type: "telemetry-system"},
                                values: {
                                    config: {}
                                },
                                msg: false,
                                success: rs => {
                                    if (rs.result) {
                                        message.success(labelList.delete_success);
                                    } else {
                                        message.error(labelList.delete_fail);
                                    }
                                    setTimeout(() => {
                                        loadData();
                                    }, 1000);
                                }
                            }).then();
                        });
                    },
                    disabled: userRight?.disabled ? () => true : undefined
                }
            ]}
            columnFormat={{
                destinations: data => {
                    try {
                        const tagList = [];
                        if (data && data.destination) {
                            convertToArray(data.destination).map(item => {
                                const tagText = `${item["destination-address"]}:${item["destination-port"]}`;
                                tagList.push(
                                    <Tag
                                        key={tagText}
                                        style={{
                                            margin: "0 8px 0 0",
                                            padding: "0 7px",
                                            background: "#14C9BB1A",
                                            color: "#14C9BB",
                                            borderColor: "#14C9BB"
                                        }}
                                    >
                                        {tagText}
                                    </Tag>
                                );
                            });
                        }
                        return tagList;
                    } catch (e) {
                        // console.log(e);
                    }
                }
            }}
        />
    );
};

export default Destination;
