import React, {useEffect, useState} from "react";
import {Card, Col, message, Row} from "antd";
import {useSelector} from "react-redux";
import {criticalSvg, majorSvg, minorSvg, warningSvg} from "@/modules-otn/utils/iconSvg";
import Icon from "@ant-design/icons";
import dayjs from "dayjs";
import {ALARM_COLOR} from "@/modules-otn/utils/util";
import {objectGet} from "@/modules-otn/apis/api";
import {fetchStaticHistory} from "@/modules-ampcon/apis/dashboard_api";
import {getSwitchTreeData} from "@/modules-ampcon/apis/inventory_api";
import {HollowPiechart, Linechart} from "./echarts_common";
import styles from "./global_view.module.scss";

const DeviceStatusCard = ({neId, neName, runState, time}) => {
    const _state = runState === 0 ? "Offline" : "Online";
    const _time = dayjs(time / 1000000).format("YYYY/MM/DD HH:mm:ss");

    return (
        <div className={styles.otnView_alarmCard_alarmBody_body}>
            <div className={styles.otnView_alarmCard_alarmBody_td} style={{width: 500, borderTop: 0}} title={neId}>
                {neName}
            </div>
            <div
                className={styles.otnView_alarmCard_alarmBody_td}
                style={{width: 500, borderLeft: 0, borderTop: 0}}
                title={_state}
            >
                {_state}
            </div>
            <div
                className={styles.otnView_alarmCard_alarmBody_td}
                style={{flex: 1, borderLeft: 0, borderTop: 0}}
                title={_time}
            >
                {_time}
            </div>
        </div>
    );
};

const GlobalView = () => {
    const [statisticsData, setStatisticsData] = useState({
        serverCpuData: [],
        serverMemData: [],
        serverDiskData: [],
        loading: true
    });

    const [statisticsHistoryData, setStatisticsHistoryData] = useState({
        cpuUsageList: [],
        memUsageList: [],
        timeList: [],
        loading: true
    });

    useEffect(() => {
        const fetchHistoryData = async () => {
            await fetchStaticHistory().then(res => {
                if (res.status === 200) {
                    setStatisticsHistoryData({
                        timeList: res.data.map(i => dayjs(i.create_time).format("HH:mm:ss")).reverse(),
                        cpuUsageList: res.data.map(i => parseFloat(i.cpu)).reverse(),
                        memUsageList: res.data.map(i => parseFloat(i.mem)).reverse()
                    });

                    const cpu_count = parseFloat(parseFloat(res.data[0].cpu).toFixed(2));
                    const cpu_count_free = parseFloat(parseFloat(100 - cpu_count).toFixed(2));
                    const mem_count = parseFloat(parseFloat(res.data[0].mem).toFixed(2));
                    const mem_count_free = parseFloat(parseFloat(100 - mem_count).toFixed(2));
                    const disk_count = parseFloat(parseFloat(res.data[0].disk).toFixed(2));
                    const disk_count_free = parseFloat(parseFloat(100 - disk_count).toFixed(2));

                    setStatisticsData({
                        serverCpuData: [
                            {value: cpu_count, name: `Usage ${cpu_count}`},
                            {value: cpu_count_free, name: `Free ${cpu_count_free}`}
                        ],
                        serverMemData: [
                            {value: mem_count, name: `Usage ${mem_count}`},
                            {value: mem_count_free, name: `Free ${mem_count_free}`}
                        ],
                        serverDiskData: [
                            {value: disk_count, name: `Usage ${disk_count}`},
                            {value: disk_count_free, name: `Free ${disk_count_free}`}
                        ],
                        loading: false
                    });
                }
            });
        };
        fetchHistoryData();

        const fetchSwitchData = async () =>
            await getSwitchTreeData().then(res => {
                const {apiResult, apiMessage} = res;
                if (apiResult === "fail") {
                    message.error(apiMessage).then();
                    return;
                }
                setDeviceSwitchData(
                    res.neInfo.map(item => ({
                        ne_id: item.id,
                        runState: item.value.runState,
                        time: dayjs().valueOf() * 1e6
                    }))
                );
            });
        fetchSwitchData();

        const timer = setInterval(() => {
            fetchHistoryData().then();
            fetchSwitchData().then();
        }, 60000);

        return () => clearInterval(timer);
    }, []);

    // const {labelList} = useSelector(state => state.languageOTN);
    const {alarms, switchAlarms, upgrade} = useSelector(state => state.notification);
    const {neNameMap} = useSelector(state => state.neName);
    // const currentYear = new Date().getFullYear();
    const alarmRightImg = [criticalSvg, majorSvg, minorSvg, warningSvg];
    const alarmRightText = ["Critical", "Major", "Minor", "Warning"];
    const [alarmRightData, setAlarmRightData] = useState();
    const [deviceData, setDeviceData] = useState([]);
    const [deviceSwitchData, setDeviceSwitchData] = useState([]);

    useEffect(() => {
        setAlarmRightData(
            Object.values(
                [...alarms, ...switchAlarms].reduce(
                    (prev, cur) => {
                        prev[cur.severity]++;
                        return prev;
                    },
                    {CRITICAL: 0, MAJOR: 0, MINOR: 0, WARNING: 0}
                )
            )
        );
    }, [alarms, switchAlarms]);

    useEffect(() => {
        objectGet("config:ne", {}).then(rs => {
            setDeviceData(
                rs.documents.map(i => ({
                    ...i.value
                }))
            );
        });
    }, [upgrade]);
    return (
        <div className={styles.otnView}>
            <Card
                title={<div className={styles.otnView_custom_title}>CPU</div>}
                style={{height: "100%"}}
                loading={statisticsData.loading}
            >
                <HollowPiechart chartData={statisticsData.serverCpuData} Echartsname="CPU" color="#14C9BB" />
            </Card>

            <Card
                title={<div className={styles.otnView_custom_title}>MEM</div>}
                style={{height: "100%"}}
                loading={statisticsData.loading}
            >
                <HollowPiechart chartData={statisticsData.serverMemData} Echartsname="Mem" color="#FFBB00" />
            </Card>

            <Card
                title={<div className={styles.otnView_custom_title}>DISK</div>}
                style={{
                    width: "100%",
                    height: "100%"
                }}
                loading={statisticsData.loading}
            >
                <HollowPiechart chartData={statisticsData.serverDiskData} Echartsname="Disk" color="#14C9BB" />
            </Card>

            <Card
                title={<div className={styles.otnView_custom_title}>Alarms</div>}
                bordered={false}
                style={{height: "100%"}}
            >
                <div className={styles.otnView_header_cardBody}>
                    {alarmRightData?.map((item, index) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <div key={index}>
                            <div className={styles.otnView_alarms_value}>
                                <Icon component={alarmRightImg[index]} />
                                <span className={styles.otnView_alarms_number}>{item}</span>
                            </div>
                            <div className={styles.otnView_header_title}>{alarmRightText[index]}</div>
                        </div>
                    ))}
                </div>
            </Card>

            <Card
                title={<div className={styles.otnView_custom_title}>Devices</div>}
                bordered={false}
                style={{
                    height: "100%",
                    width: "100%"
                }}
            >
                <div className={styles.otnView_alarmCard_alarmBody}>
                    <div className={styles.otnView_alarmCard_alarmBody_head}>
                        <div
                            className={styles.otnView_alarmCard_alarmBody_td}
                            style={{width: 500, borderRight: "1px solid #E7E7E7"}}
                        >
                            NE Name
                        </div>
                        <div
                            className={styles.otnView_alarmCard_alarmBody_td}
                            style={{width: 500, borderRight: "1px solid #E7E7E7"}}
                        >
                            State
                        </div>
                        <div className={styles.otnView_alarmCard_alarmBody_td} style={{flex: 1}}>
                            Time
                        </div>
                    </div>
                    {[...deviceData, ...deviceSwitchData].map(i => {
                        const neName = neNameMap[i.ne_id] || i.ne_id;
                        return (
                            <DeviceStatusCard
                                key={i.ne_id + i.time}
                                neId={i.ne_id}
                                neName={neName}
                                runState={i.runState}
                                time={i.time}
                            />
                        );
                    })}
                </div>
            </Card>

            <Card
                title={<div className={styles.otnView_custom_title}>CPU Utilization</div>}
                bordered={false}
                style={{
                    height: "32vh"
                }}
                loading={statisticsHistoryData.loading}
            >
                <Linechart
                    title="CPU"
                    chartData={statisticsHistoryData.timeList}
                    chartXAxis={statisticsHistoryData.cpuUsageList}
                />
            </Card>
            <Card
                title={<div className={styles.otnView_custom_title}>Memory Utilization</div>}
                bordered={false}
                style={{
                    flex: 1,
                    height: "32vh"
                }}
                loading={statisticsHistoryData.loading}
            >
                <Linechart
                    title="MEM"
                    chartData={statisticsHistoryData.timeList}
                    chartXAxis={statisticsHistoryData.memUsageList}
                />
            </Card>

            <Card
                className={styles.otnView_recentAlarms}
                title={<div className={styles.otnView_custom_title}>Recent Alarms</div>}
                bordered={false}
                style={{
                    height: "32vh"
                }}
            >
                <div className={styles.otnView_alarmCard_alarmBody}>
                    {[...alarms, ...switchAlarms]
                        .sort((a, b) => (a["time-created"] < b["time-created"] ? 1 : -1))
                        .map((i, index) => (
                            // eslint-disable-next-line react/no-array-index-key
                            <div className={styles.otnView_alarmCard_alarmBody_alarm} key={index}>
                                <div
                                    className={styles.otnView_alarmCard_alarmBody_alarm_dot}
                                    style={{backgroundColor: ALARM_COLOR[i.severity].color}}
                                />
                                <div className={styles.otnView_alarmCard_alarmBody_alarm_text}>
                                    NE={neNameMap[i.ne_id]} Source={i.resource} Content=
                                    {i["alarm-abbreviate"] ?? i.text} Time=
                                    {dayjs(i["time-created"] / 1000000).format("YYYY/MM/DD HH:mm:ss")}
                                </div>
                            </div>
                        ))}
                </div>
            </Card>
            {/*       <footer className={styles.otnView_footer}>
                <a
                    style={{color: "#929A9E"}}
                    href="https://www.fs.com"
                    target="_blank"
                    rel="noreferrer"
                >{`${labelList.copyRight}${currentYear} ${labelList.FSCompany}`}</a>
            </footer> */}
        </div>
    );
};

export default GlobalView;
