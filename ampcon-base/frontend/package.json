{"name": "ampcon", "version": "1.0.0", "private": true, "scripts": {"start:ampcon-super": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-SUPER vite", "start:ampcon-t": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-T vite", "start:ampcon-dc": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-DC vite", "start:ampcon-campus": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-CAMPUS vite", "start:ampcon-smb": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-SMB vite", "build:ampcon-super": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-SUPER NODE_OPTIONS=--max-old-space-size=4096 vite build", "build:ampcon-t": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-T NODE_OPTIONS=--max-old-space-size=4096 vite build", "build:ampcon-dc": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-DC NODE_OPTIONS=--max-old-space-size=4096 vite build", "build:ampcon-campus": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-CAMPUS NODE_OPTIONS=--max-old-space-size=4096 vite build", "build:ampcon-smb": "cross-env VITE_APP_EXPORT_MODULE=AmpCon-SMB NODE_OPTIONS=--max-old-space-size=4096 vite build", "serve": "vite preview"}, "devDependencies": {"@types/google.maps": "^3.52.5", "@types/lodash.debounce": "^4.0.7", "@types/node": "^18.15.11", "@types/react": "^18.0.37", "@types/react-csv": "^1.1.3", "@types/react-datepicker": "4.8.0", "@types/react-dom": "^18.0.11", "@types/react-table": "^7.7.14", "@types/react-virtualized-auto-sizer": "^1.0.1", "@types/react-window": "^1.8.5", "@types/uuid": "^9.0.1", "@originjs/vite-plugin-commonjs": "^1.0.3", "@vitejs/plugin-react": "^4.3.1", "@vitejs/plugin-react-swc": "^3.7.1", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-airbnb-typescript-prettier": "^5.0.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-react": "^7.33.2", "eslint-plugin-no-inline-styles": "^1.0.5", "eslint-plugin-react-hooks": "^4.6.0", "lint-staged": "^13.2.1", "prettier": "^3.1.1", "vite-plugin-pwa": "^0.14.7", "eslint-plugin-unicorn": "^50.0.1", "http-proxy-middleware": "^2.0.6", "sass": "^1.69.7", "sass-loader": "^13.3.3", "vite": "^5.4.14", "vite-plugin-babel": "^1.2.0", "vite-plugin-commonjs": "^0.10.3", "vite-plugin-require": "^1.2.14", "vite-plugin-sass": "^0.1.0", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.2.0"}, "dependencies": {"@chakra-ui/anatomy": "^2.1.1", "@chakra-ui/icons": "^2.0.18", "@chakra-ui/react": "^2.3.6", "@chakra-ui/styled-system": "^2.9.0", "@chakra-ui/theme-tools": "^2.0.12", "@chakra-ui/utils": "^2.0.14", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@fontsource/inter": "^4.5.15", "@googlemaps/react-wrapper": "^1.1.35", "@googlemaps/typescript-guards": "^2.0.3", "@hello-pangea/dnd": "^16.2.0", "@nivo/circle-packing": "^0.80.0", "@nivo/core": "^0.80.0", "@phosphor-icons/react": "^2.0.8", "@react-spring/web": "^9.5.5", "@tanstack/react-query": "^4.29.3", "@tanstack/react-table": "^8.8.5", "@textea/json-viewer": "^2.16.2", "buffer": "^6.0.3", "chakra-react-select": "^4.6.0", "chart.js": "^4.4.0", "country-state-city": "^3.2.0", "cronstrue": "2.26.0", "currency-codes": "^2.1.0", "dagre": "^0.8.5", "dotenv": "^16.0.3", "fast-equals": "^5.0.1", "formik": "^2.2.9", "framer-motion": "^10.12.3", "i18next": "^22.4.14", "i18next-browser-languagedetector": "^7.0.1", "i18next-http-backend": "^2.2.0", "libphonenumber-js": "^1.10.28", "lodash.debounce": "^4.0.8", "papaparse": "^5.4.1", "prop-types": "^15.8.1", "rc-tree": "^5.7.9", "react-app-polyfill": "^3.0.0", "react-chartjs-2": "^5.2.0", "react-country-flag": "^3.1.0", "react-csv": "^2.2.2", "react-datepicker": "^4.11.0", "react-fast-compare": "^3.2.1", "react-flow-renderer": "^10.3.17", "react-full-screen": "^1.1.1", "react-i18next": "^12.2.0", "react-masonry-css": "^1.0.16", "react-papaparse": "^4.1.0", "react-table": "^7.8.0", "react-virtualized-auto-sizer": "^1.0.15", "react-window": "^1.8.9", "source-map-explorer": "^2.5.3", "typescript": "^5.0.4", "uuid": "^9.0.0", "vite": "^5.4.14", "yup": "^0.32.11", "zustand": "^4.3.7", "@ant-design/icons": "^5.2.6", "@antv/hierarchy": "^0.6.12", "@antv/l7": "2.20.5", "@antv/l7-draw": "^3.1.5", "@antv/layout": "^0.3.25", "@antv/x6": "^2.16.1", "@antv/x6-plugin-dnd": "^2.1.1", "@antv/x6-plugin-export": "^2.1.6", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-minimap": "^2.0.7", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-react-components": "^2.0.8", "@antv/x6-react-shape": "^2.2.3", "@babel/eslint-parser": "^7.25.9", "@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.11", "@fullcalendar/interaction": "^6.1.11", "@fullcalendar/list": "^6.1.11", "@fullcalendar/react": "^6.1.11", "@fullcalendar/timegrid": "^6.1.11", "@popperjs/core": "^2.11.8", "@reduxjs/toolkit": "^2.0.1", "ahooks": "^3.7.8", "antd": "5.12.8", "axios": "^1.6.5", "dayjs": "^1.11.10", "diff": "^5.2.0", "echarts": "^5.4.3", "ip": "^2.0.1", "lodash": "^4.17.21", "monaco-editor": "^0.46.0", "normalize.css": "^8.0.1", "rc-dock": "^3.2.18", "react": "^18.2.0", "react-dom": "^18.2.0", "react-popper": "^2.3.0", "react-redux": "^9.0.4", "react-resizable": "^3.0.5", "react-router-dom": "^6.21.1", "react-scripts": "^5.0.1", "sass-embedded": "^1.80.4", "swc-loader": "^0.2.6", "vis-network": "^9.1.9", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-node-polyfills": "^0.22.0", "vite-plugin-replace": "^0.1.1", "vite-plugin-svgr": "^4.2.0", "xlsx": "^0.18.5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 50 chrome version", "last 50 firefox version", "last 50 safari version"]}, "resolutions": {"@swc/core": "1.10.11"}}