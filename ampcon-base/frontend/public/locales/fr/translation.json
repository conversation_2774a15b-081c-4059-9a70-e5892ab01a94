{"account": {"account": "<PERSON><PERSON><PERSON>", "activating_google_authenticator": "Activation de Google Authenticator", "activating_sms_mfa": "Validation du numéro de téléphone", "avatar": "Avatar", "error_fetching_qr": "Erreur lors de la récupération du code QR : {{e}}", "error_phone_verif": "Erreur avec votre code de validation, veuillez réessayer.", "google_authenticator": "Google Authenticator", "google_authenticator_intro": "Pour utiliser Google Authenticator comme méthode de double authentification de votre compte, vous devez d'abord installer l'application sur votre appareil iOS ou Android", "google_authenticator_ready": "Une fois que vous avez l'application prête à l'emploi, vous pouvez continuer", "google_authenticator_scan_qr_code_explanation": "Scannez le code QR suivant à l'aide de \"Scanner un code QR\" dans l'application Google Authenticator", "google_authenticator_scanned_qr_code": "Une fois le code QR scanné avec succès sur votre téléphone, vous pouvez passer à l'étape suivante", "google_authenticator_success_explanation": "Vous avez maintenant configuré avec succès Google Authenticator avec votre compte. N'oubliez pas d'enregistrer vos modifications pour confirmer !", "google_authenticator_type_code": "Veuillez saisir ci-dessous le code à 6 chiffres de votre application Google Authenticator", "google_authenticator_wait_for_code": "Attendez le code suivant (pas {{old}})", "google_authenticator_wrong_code": "Code invalide! Veuillez réessayer ou attendre que le prochain code soit généré dans l'application Google Authenticator", "mfa": "Authentification multifacteur", "phone": "Téléphone", "phone_number": "Numéro de téléphone", "phone_number_add_introduction": "Veuillez entrer le numéro de téléphone que vous souhaitez utiliser pour sécuriser votre compte lors de la connexion", "phone_required": "Pour activer la vérification par SMS, vous devez entrer un numéro de téléphone", "phone_validation_success_explanation": "Numéro de téléphone vérifié avec succès ! Cliquez sur \"Enregistrer\" pour ajouter ce numéro de téléphone à votre compte", "proceed_to_activation": "<PERSON><PERSON><PERSON>rer le processus d'activation", "resend": "<PERSON><PERSON><PERSON>", "sms": "SMS", "success_phone_verif": "Numéro de téléphone vérifié avec succès ! Vous pouvez maintenant enregistrer votre profil", "title": "Mon compte", "verify_phone_instructions": "Vous devriez recevoir un code sur votre numéro de téléphone dans les prochaines secondes. Veuillez le saisir ci-dessous pour vérifier votre numéro de téléphone", "verify_phone_number": "Vérifiez votre numéro de téléphone"}, "analytics": {"ack_signal": "Signal ACK", "active": "actif", "airtime": "Temps d'antenne", "analyze_sub_venues": "Surveiller les sous-salles", "associations": "Les associations", "associations_explanation": "Associations totales", "average_health": "Santé globale", "average_health_explanation": "Bon état d'esprit moyen de tous les appareils connectés fournissant les informations de vérification de l'état", "average_memory": "Mémoire utilisée", "average_memory_explanation": "Pourcentage moyen de mémoire utilisée", "average_uptime": "Disponibilité moyenne", "average_uptime_explanation": "Disponibilité moyenne de l'appareil (JJ:HH:MM:SS)", "band": "B: et", "bandwidth": "Bande passante", "board": "Collecte d'analyses", "busy": "<PERSON><PERSON><PERSON><PERSON>", "channel": "Canal", "client_lifecycle": "Cycle de vie des clients", "client_lifecycle_one": "{{count}} Cycle de vie des clients", "client_lifecycle_other": "{{count}} Cycles de vie des clients", "connected": "Connecté", "connection_explanation": "{{connectedCount}} connecté, {{disconnectedCount}} non connecté", "connection_percentage": "{{count}} % connectés", "connection_percentage_explanation": "Pourcentage de tous les appareils sous ce lieu qui sont connectés ({{connectedCount}} connectés, {{disconnectedCount}} non connectés)", "create_board": "Démarrer la surveillance", "dashboard": "Tableau de bord", "delta": "Delta", "device_types": "Les types", "device_types_explanation": "Types d'appareils de tous les appareils disponibles", "disconnected": "Débranché", "firmware": "Micrologiciel", "health": "sant<PERSON>", "inactive": "Inactif", "interval": "Intervalle", "last_connected": "Dernière connexion", "last_connected_tooltip": "La dernière fois que cet appareil a été connecté au contrôleur. Cela peut être utilisé pour estimer quand un appareil s'est déconnecté", "last_connection": "Dernière connexion", "last_contact": "<PERSON><PERSON> contact", "last_disconnection": "Dernière déconnexion", "last_firmware_explanation": "Micrologiciel le plus courant exécuté sur les appareils analysés", "last_health": "<PERSON><PERSON><PERSON> santé", "last_ping": "<PERSON><PERSON> ping", "live_view": "Vue en direct", "live_view_explanation_five": "Vous pouvez également cliquer sur l'un des cercles pour zoomer", "live_view_explanation_four": "Vous pouvez survoler n'importe quel objet avec votre souris pour voir des informations détaillées", "live_view_explanation_one": "Le graphique 'Live View' est une représentation visuelle de votre site.", "live_view_explanation_three": "Lieu -> AP -> Radio -> SSID -> UE", "live_view_explanation_two": "De l'extérieur vers l'intérieur :", "live_view_help": "Aide sur l'affichage en direct", "memory": "m<PERSON><PERSON><PERSON>", "memory_used": "Mémoire utilisée", "missing_board": "La surveillance analytique sur ce site n'est plus active. Cliquez ici pour redémarrer la surveillance", "mode": "Mode", "monitoring": "surveillance", "no_board": "Aucune surveillance", "no_board_description": "Vous ne surveillez pas ce lieu pour le moment, cliquez ici pour commencer", "noise": "Bruit", "packets": "Paquets", "radio": "Radio", "raw_analytics_data": "Données analytiques brutes", "raw_data": "Données brutes", "receive": "Recevoir", "retention": "Rétention", "retries": "Tentatives", "search_serials": "Rechercher des publications en série", "stop_monitoring": "<PERSON><PERSON><PERSON><PERSON>", "stop_monitoring_success": "Lieu de surveillance arrêté !", "stop_monitoring_warning": "Êtes-vous sûr? <PERSON><PERSON> effacera toutes les données de surveillance enregistrées pour ce lieu", "temperature": "Température", "title": "ANALYTIQUE", "total_data": "Données totales", "total_devices_explanation": "Tous les appareils sous ce lieu ({{connectedCount}} connectés, {{disconnectedCount}} non connectés)", "total_devices_one": "{{count}} Appareil", "total_devices_other": " {{count}} appareils", "uptime": "La disponibilité"}, "batch": {"batches": "Des lots", "cannot_edit_macs": "Étant donné que des tâches ont déjà été exécutées sur ce lot, vous ne pouvez pas modifier ses adresses MAC", "change_warning": "ATTENTION : vous avez mis à jour soit le modèle soit le fabricant. Nous vous suggérons fortement de mettre à jour vos certificats pour qu'ils restent cohérents avec ce lot en choisissant l'option \"Enregistrer et mettre à jour les certificats\"", "create": "Créer des certificats", "create_certificates": "Créer des certificats", "create_certificates_explanation": "Voulez-vous vraiment créer les certificats {{nbCerts}} de ce lot ?", "create_certificates_title": "<PERSON><PERSON><PERSON> les certificats de {{name}}", "delete_explanation": "Voulez-vous vraiment supprimer ce lot ? Cela r<PERSON> tous ses certificats {{nbCerts}} et les supprimera. Cette opération n'est pas réversible", "delete_title": "Supprimer le lot {{name}}", "duplicate_in_file": "MAC en double trouvé dans la ligne {{firstRow}} et {{secondRow}} : {{mac}}", "emails_to_notify": "E-mails pour notifier lorsque cette tâche est terminée", "error_push": "Erreur lors du démarrage de la tâche de modifications push : {{e}}", "general_error_treating_file": "Erreur générale lors du traitement du fichier : veuil<PERSON><PERSON> vous assurer qu'il est au format .CSV, qu'il ne contient qu'une seule colonne sans en-tête.", "invalid_mac": "MAC invalide sur la ligne {{row}} : {{mac}}", "mac_count_title": "{{nb}}  MAC font actuellement partie de ce lot", "nb_macs": "{{nb}} MAC", "need_devices": "Vous devez avoir au moins un certificat pour créer !", "parsing_error": "Erreur d'analyse sur la ligne {{row}} : {{e}}", "phones_to_notify": "Numéros de téléphone à notifier lorsque la tâche est terminée", "push_changes": "<PERSON><PERSON><PERSON> les changements", "push_changes_explanation": "Voulez-vous vraiment envoyer les informations de lot à tous les certificats de ce lot ?", "revoke_explanation": "Voulez-vous vraiment révoquer ce lot ? Cela révoquera tous ses {{nbCerts}}  certificats. Cette opération n'est pas réversible", "revoke_title": "Révoquer le lot {{name}}", "save_and_change": "Enregistrer et mettre à jour les certificats", "success_push": "Tâche Push Changes démarrée avec succès ! Numéro de tâche : {{job}}", "title": "Lot"}, "certificates": {"certificate": "Certificat", "common_names_explanation": "Besoin d'un fichier .CSV d'une seule colonne sans nom contenant des MAC de périphérique à 12 chiffres HEX.", "device": "Dispositif", "device_macs": "MAC de l'appareil", "domain_name": "Nom de domaine", "error_fetching": "Erreur lors de la récupération des certificats : {{e}}", "error_revoke": "Erreur lors de la tentative de révocation du certificat : {{e}}", "expires_on": "Expire le", "filename": "Nom de fi<PERSON>er", "invalid_domain": "Les formats acceptés sont : domain.top_level_domain ou sub_domain.domain._top_level_domain", "invalid_mac": "Doit contenir 12 caractères HEX", "invalid_redirector": "Les formats acceptés sont : example.com, example.com:16000", "mac_address": "ADRESSE MAC", "macs": "MAC", "manufacturer": "fabricant", "model": "<PERSON><PERSON><PERSON><PERSON>", "redirector": "redirecteur", "revoke": "Révoquer", "revoke_count": "Révoquer le compte", "revoke_warning": "Voulez-vous vraiment révoquer ce certificat ?", "server": "Ser<PERSON><PERSON>", "successful_revoke": "Certificat révoqué avec succès !", "title": "Certificats"}, "commands": {"abort_command_explanation": "Voulez-vous vraiment arrêter d'attendre le résultat de cette commande ?", "abort_command_title": "Abandonner la commande", "active_scan": "Analyse active", "blink": "<PERSON><PERSON><PERSON><PERSON>", "blink_error": "Erreur lors de l'envoi de la commande de clignotement : {{e}}", "blink_success": "La commande Blink a été envoyée avec succès !", "channel": "Canal", "confirm_reset": "Démarrer la réinitialisation de l'appareil n° {{serialNumber}}", "connect": "Re<PERSON>", "rtty": "RTTY", "execution_time": "Temps d'exécution", "factory_reset": "Dispositif de réinitialisation d'usine", "factory_reset_error": "Erreur lors de la tentative de réinitialisation d'usine de l'appareil : {{e}}", "factory_reset_success": "La réinitialisation d'usine de l'appareil a démarré avec succès !", "factory_reset_warning": "Remarque: Voulez-vous vraiment rétablir la configuration d'usine de cet appareil ? Cette action n'est pas réversible", "firmware_upgrade": "Mise à jour du firmware", "firmware_upgrade_error": "Erreur lors de la tentative de mise à niveau du micrologiciel de l'appareil : {{e}}", "firmware_upgrade_success": "La mise à niveau de l'appareil a démarré avec succès !", "image_date": "Date de l'image", "keep_redirector": "Conserver le redirecteur ?", "other": "Les commandes", "override_dfs": "Remplacer DFS", "reboot": "<PERSON><PERSON><PERSON><PERSON>", "reboot_description": "Vou<PERSON><PERSON>-vous redémarrer cet appareil ?", "reboot_error": "Erreur lors de l'envoi de la commande de redémarrage : {{e}}", "reboot_success": "Commande de redémarrage envoyée avec succès !", "revision": "Révision", "scan": "Balayage", "signal": "Signal", "upgrade": "<PERSON><PERSON><PERSON><PERSON>", "wifiscan": "Balayage Wi-Fi", "wifiscan_error": "Erreur lors de la tentative d'analyse de l'appareil : {{e}}", "wifiscan_error_1": "Votre radio 5G est sur un canal radar, vous devez activer \"Override DFS\" pour permettre le balayage de tous les canaux 5G"}, "common": {"actions": "actes", "address_search_autofill": "Rechercher des emplacements pour remplir automatiquement les champs ci-dessous", "alert": "<PERSON><PERSON><PERSON>", "all": "<PERSON>ut", "assign": "Attribuer", "avg": "<PERSON><PERSON>.", "back": "Retour", "base_information": "Informations de base", "by": "Par", "cancel": "annuler", "claim": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "columns": "Les colonnes", "command": "Commander", "completed": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmer", "connected": "Connecté", "copied": "copie", "copy": "copie", "create": "<PERSON><PERSON><PERSON>", "create_new": "Créer un nouveau", "created": "<PERSON><PERSON><PERSON>", "creator": "<PERSON><PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON>", "daily": "du quotidien", "date": "<PERSON><PERSON><PERSON>vous amoureux", "day": "journ<PERSON>", "days": "Journées", "default": "Défaut", "defaults": "Valeurs par défaut", "description": "La description", "details": "Détails", "device_details": "<PERSON>é<PERSON> de l'appareil", "discard_changes": "Annuler les modifications?", "disconnected": "Débranché", "display_name": "Afficher un nom", "download": "Télécharger", "download_instructions": "Téléchargement réussi ! Si vous ne trouvez pas le fichier, ve<PERSON><PERSON><PERSON> confirmer que vous autorisez les téléchargements à partir de ce site Web", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "edit": "modifier", "email": "Email", "empty_list": "Liste vide", "end": "Fin", "entries_one": "Entrée", "entries_other": "Les entrées", "error": "erreur", "error_claiming_obj": "Erreur lors de la revendication de {{obj}}", "error_download": "Erreur lors de la tentative de téléchargement : {{e}}", "errors": "les erreurs", "exit_fullscreen": "<PERSON><PERSON><PERSON>", "export": "Exportation", "finished": "fini", "fullscreen": "Plein écran", "general_error": "Erreur de connexion au serveur. Veuillez consulter votre administrateur.", "general_info": "Informations générales", "go_back": "<PERSON><PERSON><PERSON>", "go_to_map": "Aller à la carte", "hide": "<PERSON><PERSON>", "hourly": "Toutes les heures", "identification": "Identification", "inherit": "Hériter", "language": "La langue", "last_use": "Dernière utilisation", "lifetime": "durée de vie", "locale": "lieu", "logout": "Connectez - Out", "main": "<PERSON><PERSON>", "make_higher_priority": "Faire une priorité plus élevée", "make_lower_priority": "Faire une priorité inférieure", "manage": "<PERSON><PERSON><PERSON>", "manual": "manuel", "manufacturer": "fabricant", "map": "<PERSON><PERSON>", "max": "Max", "min": "Min", "miscellaneous": "Divers", "mode": "Mode", "model": "<PERSON><PERSON><PERSON><PERSON>", "modified": "<PERSON><PERSON><PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "months": "mois", "my_account": "Mon compte", "name": "Prénom", "name_error": "Le nom doit comporter moins de 50 caractères", "next": "Suivant", "no": "Non", "no_addresses_found": "<PERSON><PERSON><PERSON> adresse trouvée", "no_clients_found": "Aucun client trouvé", "no_devices_found": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> trouvé", "no_items_yet": "Aucun article pour le moment", "no_obj_found": "Aucun {{obj}} trouvé", "no_records_found": "Aucun Enregistrement Trouvé", "no_statistics_found": "Aucune statistique trouvée", "no_last_statistics_found": "Aucune dernière statistique trouvée", "none": "Aucun", "not_found": "404 - Introuvable", "note": "<PERSON><PERSON><PERSON>", "notes": "<PERSON><PERSON><PERSON>", "of": "De", "password": "Mot de passe", "preview": "<PERSON><PERSON><PERSON><PERSON>", "quarterly": "TRIMESTRIEL", "redirector": "redirecteur", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON>", "remove_claim": "Supprimer la réclamation", "reset": "Réinitialiser", "revoked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "save": "sauve<PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "seconds": "Secondes", "select_all": "<PERSON><PERSON> tout", "select_value": "Sélectionnez une valeur", "sending": "Envoi", "sent_code": "Code envoyé !", "show": "Spectacle", "size": "<PERSON><PERSON>", "start": "D<PERSON>but", "start_time": "<PERSON><PERSON> d<PERSON>", "end_time": "Heure de fin", "started": "commencé", "state": "Etat", "status": "Statut", "stop_editing": "<PERSON><PERSON><PERSON><PERSON>", "submitted": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "successfully_claimed_obj": "<PERSON><PERSON><PERSON><PERSON><PERSON> avec succès {{count}} {{obj}}", "sync": "Sync", "test": "tester", "theme": "Thème", "time": "Temps", "timestamp": "horodatage", "type": "Type", "type_for_options": "Ta<PERSON>z la valeur que vous devez créer...", "select": "Sélectionner...", "unknown": "Inconnu", "use_file": "Utiliser le fichier", "value": "<PERSON><PERSON>", "variable": "Variable", "view": "<PERSON><PERSON>", "view_details": "Voir les détails", "view_in_gateway": "Afficher dans le contrôleur", "view_json": "Afficher JSON", "warning": "Attention", "warnings": "Avertissements", "yearly": "<PERSON><PERSON>", "yes": "O<PERSON>", "your_new_note": "Votre nouvelle note"}, "configurations": {"add_interface": "Ajouter une interface", "add_radio": "Ajouter une radio", "add_ssid": "Ajouter SSID", "add_subsection": "Ajouter une sous-section", "advanced_settings": "R<PERSON>g<PERSON>s a<PERSON>", "affected_explanation_one": "Il y a {{count}}  appareils concernés par cette configuration", "affected_explanation_other": "Il y a {{count}}  appareils concernés par cette configuration", "applied_configuration": "Configuration appliquée", "cant_delete_explanation": "Impossible de supprimer cette configuration car elle est utilisée par au moins un appareil, un lieu ou une entité. Vous pouvez voir ce qu'ils sont en cliquant sur le bouton à côté de \"En cours d'utilisation\" sur le formulaire de cette configuration", "cant_delete_explanation_simple": "Impossible de supprimer cette configuration car elle est utilisée par au moins un appareil, un lieu ou une entité. Vous pouvez voir ce qu'ils sont en allant sur la page de configuration", "configuration_json": "JSON de configuration", "configuration_push_result": "Résultat de l'envoi de la configuration", "configuration_sections": "Rubriques de configuration", "delete_interface": "Supprimer l'interface", "delete_radio": "Supprimer la radio", "delete_ssid": "Supprimer le SSID", "device_configurations": "Configurations de l'appareil", "device_types": "Types d'appareils", "dhcp_snooping": "Espionnage DHCP", "error_pushes_one": "Erreur (peut être due à une mauvaise configuration) : {{count}}", "error_pushes_other": "Erreurs (peut-être dues à une mauvaise configuration) : {{count}}", "expert_name": "Mode expert", "expert_name_explanation": "Vous pouvez utiliser le mode expert pour modifier directement votre configuration, notamment en ajoutant des valeurs qui ne sont pas actuellement prises en charge par l'interface utilisateur. Veuillez utiliser ce format : { \"interfaces\": [ ... ], \"radios\": { ... }, ...etc }", "explanation": "Explication", "firewall": "Pare-feu", "firmware_upgrade": "Mise à jour du firmware", "globals": "Globals", "health": "sant<PERSON>", "hostapd_warning": "Paramètre d'URL, ex. : test=valeur", "import_file": "Importer la configuration à partir d'un fichier", "import_file_explanation": "Vous pouvez utiliser l'option ci-dessous pour importer un fichier JSON de configuration, avec un contenu de ce format :\n{\n     \"interfaces\": [ ... ],\n     \"radios\": { ... },\n     ...etc\n}", "import_warning": "AVERTISSEMENT : cette opération écrasera toutes les sections de configuration que vous avez peut-être déjà créées.", "imported_configuration": "Configuration importée", "in_use_title": "{{name}} En cours d'utilisation", "interfaces": "Des interfaces", "network": "<PERSON><PERSON><PERSON>", "interfaces_instruction": "Veuillez utiliser une chaîne JSON valide sous la forme suivante : { \"interfaces\": [] }.", "invalid_resource": "Ressource invalide ou supprimée", "metrics": "mé<PERSON><PERSON>", "no_resource_selected": "Aucune ressource sélectionnée", "notification_details": "Mis à jour : {{success}}, En attente de connexion : {{warning}}, Erreurs : {{error}}", "one": "Configuration", "push_configuration": "<PERSON><PERSON><PERSON> la <PERSON>", "push_configuration_error": "Erreur lors de la tentative d'envoi de la configuration sur l'appareil : {{e}}", "push_configuration_explanation": "Configuration non poussée, code d'erreur {{code}}", "push_success": "La configuration a été vérifiée et une commande \"Configurer\" a maintenant été lancée par le contrôleur !", "radio_limit": "Vous avez atteint le nombre maximum de radios (5). Vous devez supprimer une des bandes activées pour en ajouter une nouvelle", "radios": "Radios", "rc_only": "Libérer les candidats uniquement", "save_warnings": "Voulez-vous vraiment enregistrer votre configuration ?", "services": "Prestations de service", "special_configuration": "Configuration spécifique à l'appareil", "start_special_creation": "<PERSON><PERSON>er une configuration pour cet appareil", "statistics": "statistiques", "successful_pushes_one": "<PERSON><PERSON><PERSON> : {{count}}", "successful_pushes_other": "Transmissions réussies : {{count}}", "third_party": "<PERSON><PERSON> personne", "third_party_instructions": "Veuillez utiliser une chaîne JSON valide sous la forme suivante : { \"value_name\": \"value\" }.", "title": "Les configurations", "unit": "Unité", "system": "Système", "view_affected_devices": "Afficher les appareils concernés", "view_in_use": "Afficher en cours d'utilisation", "warning_pushes_one": "En attente de connexion des appareils : {{count}}", "warning_pushes_other": "En attente de connexion des appareils : {{count}}", "weight": "Poids", "wifi_bands_max": "Il ne peut y avoir plus de 16 SSID utilisant cette bande wifi", "wifi_frames": "Cadres Wi-Fi", "multi_psk_key_exsist": "La clé existe déjà"}, "contacts": {"access_pin": "NIP d'accès", "claim_explanation": "Pour réc<PERSON>er des contacts, vous pouvez utiliser le tableau ci-dessous", "first_name": "Prénom", "identifier": "Identifiant", "initials": "Initiales", "last_name": "Nom de famille", "mobiles": "MOBILES", "one": "Contact", "other": "Contacts", "phones": "Téléphones", "primary_email": "Email primaire", "salutation": "salutation", "secondary_email": "Email secondaire", "title": "Titre", "to_claim": "Contacts pour réclamer", "visual": "<PERSON><PERSON><PERSON>"}, "controller": {"configurations": {"create_success": "Configuration créée !", "delete_success": "La configuration est maintenant supprimée !", "title": "Configurations par défaut", "update_success": "Configuration mise à jour !"}, "configure": {"invalid": "Votre nouvelle configuration doit être valide JSON", "success": "La nouvelle configuration est en cours de déploiement sur l'appareil", "title": "Configure via CLI", "warning": "Remarque: So<PERSON>z averti : il n'y aura que des tests minimaux effectués sur cette configuration."}, "crud": {"choose_time": "<PERSON><PERSON><PERSON>", "clear_time": "<PERSON><PERSON><PERSON><PERSON> le temps", "delete_success_obj": "Supprimé {{obj}}"}, "dashboard": {"associations": "Les associations", "associations_explanation": "Toutes les associations (ou UE) actuellement connectées", "certificates": "Certificats", "certificates_explanation": "État des certificats des appareils actuellement connectés", "commands": "Les commandes", "commands_explanation": "Toutes les commandes exécutées", "device_dashboard_refresh": "Nouvelles statistiques de connexion", "device_types": "Types d'appareils", "device_types_explanation": "Types d'appareils de tous les appareils pointant vers ce contrôleur", "devices_explanation": "Tous les appareils pointent vers ce point de terminaison du contrôleur", "error_fetching": "Erreur lors de la récupération du tableau de bord", "expand": "Développer", "last_ping_explanation": "Lorsque ces données ont été recueillies", "memory": "Utilisation de la mémoire", "memory_explanation": "Appareils actuellement connectés avec la quantité de mémoire utilisée correspondante", "no_certificate": "Aucun certificat", "not_connected": "Pas connecté", "others": "autres", "overall_health": "Santé globale", "overall_health_explanation": "Santé moyenne de tous les appareils actuellement connectés à partir desquels nous recevons des données de santé. Le calcul exact est : (Appareils=100 % * 100 + Appareils>=90 * 95 + Appareils>=60 * 75 + Appareils<60 * 30) / Appareils connectés", "overall_health_explanation_pie": "Le nombre d'appareils avec un pourcentage de santé dans ces catégories", "serial_mismatch": "Incompatibilité de série", "status": "Statut", "status_explanation": "État de tous les appareils pointant vers ce point de terminaison de contrôleur", "unknown_status": "Statut non reconnu", "unrecognized": "<PERSON><PERSON><PERSON><PERSON>", "uptimes": "", "uptimes_explanation": "Appareils actuellement connectés avec les temps de disponibilité correspondants", "vendors": "Vendeurs", "vendors_explanation": "Fournisseurs des appareils pointant vers ce contrôleur", "verified": "Vérifié"}, "devices": {"add_blacklist": "Ajouter un numéro de série", "added": "<PERSON><PERSON><PERSON><PERSON>", "added_blacklist": "Numéro de série ajouté à la liste noire !", "average_uptime": "Disponibilité moyenne", "blacklist": "Liste noire", "blacklist_update": "Mettre à jour {{serialNumber}} enregistrement", "by": "Par", "capabilities": "Capacités", "command_one": "Commander", "commands": "Les commandes", "complete_data": "<PERSON><PERSON><PERSON> complètes", "config_id": "Identifiant de configuration", "connecting": "De liaison", "connection_changes": "États de connexion", "delete_blacklist": "Numéro de série supprimé de la liste noire !", "delete_health_explanation": "<PERSON>la supprimera définitivement tous les bilans de santé avant la date que vous avez choisie", "delete_logs_explanation": "<PERSON>la supprimera définitivement tous les journaux avant la date que vous choisissez", "error_code": "Code d'erreur", "executed": "<PERSON><PERSON><PERSON><PERSON>", "finished_reboot": "{{serialNumber}} vient de terminer le redémarrage !", "finished_upgrade": "{{serialNumber}} a terminé la mise à jour !", "from_to": "de {{from}} à {{to}}", "healthchecks": "Bilans de santé", "last_modified": "Dernière modification:", "last_upgrade": "Dernière mise à jour", "localtime": "heure locale", "logs": "LOGS", "new_statistics": "Nouvelles statistiques", "no_more_available": "<PERSON><PERSON> r<PERSON>", "reason": "raison", "results": "Résultats", "sent_upgrade_to_latest": "Commande \"Mettre à niveau vers la dernière version\" envoy<PERSON> à l'appareil", "severity": "Gravité", "show_more": "Montre plus", "started_reboot": "{{serialNumber}} s'éteint pour redémarrer !", "started_upgrade": "{{serialNumber}} vient de s'arrêter pour commencer la mise à jour !", "trace": "Trace", "trace_description": "Remarque: Lancer une trace à distance de cet appareil pour une durée spécifique ou un nombre de paquets", "update_success": "Appareil mis à jour !", "updated_blacklist": "Liste noire mise à jour !"}, "firmware": {"devices_explanation": "Périphériques qui ont pointé vers ce serveur de firmware. <PERSON><PERSON> pourrait expliquer les écarts entre ce nombre et le tableau de bord de l'appareil", "endpoints": "Points de terminaison", "endpoints_explanation": "Tous les terminaux pointant vers ce serveur de firmware", "firmware_age": "Âge du micrologiciel", "firmware_age_explanation": "Âge moyen du micrologiciel des appareils pour lesquels nous disposons de ces données", "latest": "Dernier micrologiciel installé", "old_firmware": "Ancien micrologiciel", "ouis_explanation": "OUI des appareils qui se sont connectés à ce serveur de firmware", "outdated_one": "Micrologiciel vieux de {{count}}  jours", "outdated_other": "Micrologiciel vieux de {{count}}  jours", "outdated_unknown": "Firmware d'âge inconnu", "release": "libération", "show_dev_releases": "Versions de développement", "status_explanation": "État de connexion des appareils qui se sont connectés à ce serveur de micrologiciel", "unrecognized": "Micrologiciel non reconnu", "unrecognized_firmware": "Micrologiciel non reconnu", "unrecognized_firmware_explanation": "Firmware actuellement utilisé par les appareils et non reconnu par ce serveur de firmware", "up_to_date": "Appareils à jour", "up_to_date_explanation": "Appareils utilisant les derniers logiciels disponibles à leur disposition"}, "provisioning": {"title": "Provisioning"}, "queue": {"title": "File d'attente d'événements"}, "radius": {"calling_station_id": "Station", "disconnect": "déconnecter", "disconnect_success": "Session Radius déconnectée !", "input_octets": "Contribution", "output_octets": "<PERSON><PERSON><PERSON>", "radius_clients": "Clients rayon", "session_time": "Temps de session", "username": "Nom d'utilisateur"}, "stats": {"load": "Charge (1 | 5 | 15 m.)", "seconds_ago": " Il y a {{s}} secondes", "used": "<PERSON><PERSON><PERSON><PERSON>"}, "telemetry": {"duration": "<PERSON><PERSON><PERSON>", "interval": "Intervalle", "kafka": "Kafka", "kafka_success": "La télémétrie Kafka est maintenant lancée !", "last_update": "Dernière mise à jour", "minutes": "Minutes", "need_types": "<PERSON><PERSON> devez sélectionner au moins un type", "output": "Mode de sortie", "seconds_ago": " Il y a{{seconds}} secondes", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "types": "Les types", "websocket": "Websocket"}, "trace": {"down": "VERS LE BAS", "download": "Télécharger Trace", "duration": "<PERSON><PERSON><PERSON>", "network": "<PERSON><PERSON><PERSON>", "packets": "Paquets", "success": "Suivi terminé sur l'appareil n° {{serialNumber}}. V<PERSON> pouvez maintenant télécharger le résultat", "up": "UP", "wait": "Attendre les résultats ?"}, "wifi": {"active_ms": "actif", "busy_ms": "<PERSON><PERSON><PERSON><PERSON>", "channel_width": "<PERSON><PERSON> Ch", "mode": "Mode", "noise": "Bruit", "receive_ms": "Recevoir", "rx_rate": "<PERSON><PERSON>", "station": "Station", "tx_rate": "Taux d'émission", "vendor": "vendeur", "wifi_analysis": "Ana<PERSON><PERSON>"}}, "crud": {"add": "Ajouter", "confirm_cancel": "Voulez-vous vraiment annuler les modifications que vous avez apportées ?", "confirm_delete_obj": "Êtes-vous sûr de vouloir supprimer ce {{obj}}?", "create": "<PERSON><PERSON><PERSON>", "create_object": "<PERSON><PERSON><PERSON> {{obj}}", "delete": "<PERSON><PERSON><PERSON><PERSON>", "delete_confirm": "Êtes-vous sûr de vouloir supprimer ce {{obj}}?", "delete_obj": "Supprimer {{obj}}", "edit": "modifier", "edit_obj": "Modifier {{obj}}", "error_create_obj": "Erreur lors de la création de {{obj}} : {{e}}", "error_delete_obj": "E<PERSON>ur lors de la <PERSON> de {{obj}} : {{e}}", "error_fetching_obj": "Erreur lors de la récupération de {{obj}} : {{e}}", "error_revoke_obj": "Erreur lors de la révocation de {{obj}} : {{e}}", "error_update_obj": "Erreur lors de la mise à jour de {{obj}} : {{e}}", "success_create_obj": "<PERSON><PERSON><PERSON> avec succès {{obj}} !", "success_delete_obj": " {{obj}}a bien été supprimé !", "success_revoke_obj": "Ré<PERSON> ré<PERSON>ie de {{obj}} !", "success_update_obj": " {{obj}}mis à jour avec succès !"}, "devices": {"all": "<PERSON>ut", "associations": "Les associations", "certificate_expires_in": "Le certificat expire dans", "certificate_expiry": "Cert. Expire dans", "connected": "Connecté", "crash_logs": "Journaux des plantages", "create_errors": "erreurs lors de la tentative de création d'appareils", "create_success": " appareils créés avec succès", "current_firmware": "Firmware actuel", "device_type_not_found": "Type d'appareil non reconnu", "duplicate_serial": "Numéro de série en double dans le fichier", "error_rtty": "Erreur lors de la tentative de connexion à l'appareil : {{e}}", "file_errors": "appareils problématiques", "found_assigned": "appareils déjà attribués", "found_not_assigned": "appareils déjà existants mais maintenant détenus", "import_batch_tags": "Importer des appareils", "import_device_warning": "Veuillez vous assurer qu'il n'y a pas d'espaces supplémentaires au début ou à la fin des valeurs, sauf si cela fait partie de la valeur souhaitée", "import_explanation": "Pour importer en masse des appareils, vous devez utiliser un fichier CSV avec les colonnes suivantes : SerialNumber, DeviceType, Name, Description, Note", "invalid_serial_number": "Numéro de série non valide (doit être composé de 12 caractères HEX)", "logs_one": "Bûche", "new_devices": "nouveaux appareils", "no_model_image": "Aucune image de modèle trouvée", "not_connected": "Pas connecté", "not_found_gateway": "Erreur : l'appareil n'est pas encore connecté à la passerelle", "notifications": "notifications de l'appareil", "one": "Dispositif", "reassign_already_owned": "Réattribuer des appareils qui existent déjà et qui appartiennent à une autre entité/salle/abonné ?", "reboot_logs": "Journaux de redémarrage", "restricted": "Limité", "restricted_overriden": "Il s'agit d'un appareil restreint, mais il est en mode développement. Toutes les restrictions sont actuellement ignorées", "restrictions_overriden_title": "Mode développement", "sanity": "Santé <PERSON>e", "start_import": "Démarrer l'importation de l'appareil", "test_batch": "Tester les données d'importation", "test_results": "Résultats de test", "title": "Dispositifs", "treating": "Essai:", "unassigned_only": "Non attribué uniquement", "update_error": "erreurs lors de la tentative de mise à jour des appareils", "update_success": "appareils mis à jour avec succès"}, "entities": {"active": "actif", "add_configurations": "Ajouter des configurations", "add_ips": "Ajouter des adresses IP", "add_ips_explanation": "Vous pouvez ajouter des adresses IPv4 ou IPv6 dans les formats suivants", "api_key": "Clé API", "cant_delete_explanation": "Impossible de supprimer cette entité car elle a des entités enfants et/ou des lieux. Vous devez supprimer tous les enfants de cette entité avant de la supprimer", "claim_device_explanation": "Pour réclamer des appareils, vous pouvez utiliser le tableau ci-dessous. Si un appareil a déjà été revendiqué par une autre entité ou un autre lieu, nous les désattribuerons également avant de les attribuer à cette entité", "client_enrollment_profile": "Profil d'inscription du client", "create_child_entity": "<PERSON><PERSON><PERSON> une entité enfant", "create_root": "<PERSON><PERSON>er une entité racine", "create_root_explanation": "Veuillez entrer les informations nécessaires pour créer l'entité racine de votre service d'approvisionnement. Ces informations peuvent être modifiées après la création", "current_state": "État actuel", "default_redirector": "Redirecteur par défaut", "devices_to_claim": "Nouveaux appareils à réclamer", "devices_under_root": "Les appareils ne peuvent pas être créés directement sous l'entité racine. Veuillez créer de nouvelles entités ou lieux et créer des appareils sous eux.", "enter_ips": "Entrez les IP que vous souhaitez ajouter ici", "entity": "Entité", "error_sync": "Erreur lors de la tentative de démarrage de la synchronisation de {{name}} : {{e}}", "failed_test": "Échec des tests avec les informations d'identification DigiCert, veuillez vérifier les informations de votre entité", "initial_state": "État initial", "ip_cidr": "IP/numéro (exemple : 10.0.0.0/8)", "ip_detection": "Détection IP", "ip_list": "Liste : IP, IP IP", "ip_range": "Gamme : IP-IP", "ip_single_address": "Adresse unique : IP", "one": "Entité", "organization": "Organisation", "server_enrollment_profile": "Profil d'inscription du serveur", "status": "Statut", "sub_one": "Sous-entité", "sub_other": "Sous-entités", "success_sync": "La synchronisation de {{name}}a démarré avec succès !", "success_test": "Le test des informations d'identification DigiCert de cette entité a réussi !", "suspended": "Suspendu", "sync_explanation": "Souhaitez-vous synchroniser cette entité ? Cela peut prendre un certain temps en fonction du nombre de certificats appartenant à cette entité.", "sync_title": "Synchroniser {{name}}", "test_digicert_creds": "Tester les informations d'identification", "title": "Entités", "tree": "Arborescence des entités", "update_success": "Entité mise à jour !", "venues_under_root": "Les lieux ne peuvent pas être créés directement sous l'entité racine"}, "firmware": {"confirm_default_data": "Veuillez confirmer les informations ci-dessous et cliquez sur \"Confirmer\" une fois que vous êtes prêt à démarrer le processus", "create_success": "Création de nouveaux paramètres de firmware par défaut !", "db_update_warning": "Cette opération se fait automatiquement quotidiennement sans avoir besoin d'utiliser cette mise à jour manuelle. La mise à jour de cette base de données peut prendre jusqu'à 25 minutes", "default_created_error_one": "{{count}} erreur lors de la tentative de création d'un nouveau paramètre", "default_created_error_other": "{{count}}  erreurs lors de la tentative de création d'un nouveau paramètre", "default_created_one": "{{count}} paramètre de micrologiciel par défaut créé", "default_created_other": "{{count}}  paramètres de micrologiciel par défaut créés", "default_found_one": "Révision valide trouvée pour le type d'appareil {{count}} ", "default_found_other": "Révisions valides trouvées pour {{count}}  types d'appareils", "default_mass_delete_success_one": "Paramètre de micrologiciel par défaut {{count}} supprimé !", "default_mass_delete_success_other": " {{count}}  paramètres de micrologiciel par défaut supprimés !", "default_not_found_one": "Aucune version de micrologiciel valide pour le type d'appareil {{count}} ", "default_not_found_other": "Aucune version de micrologiciel valide pour {{count}}  types d'appareils", "default_title": "", "default_update_success": "Firmware par défaut mis à jour pour {{deviceType}} !", "delete_success": "Paramètre de micrologiciel par défaut supprimé !", "edit_default_title": "Il s'agit du micrologiciel actuel utilisé comme version minimale pour les nouveaux points d'accès de type {{deviceType}}. Si un nouveau point d'accès {{deviceType}} se connecte à la passerelle, il sera automatiquement mis à niveau vers cette version.", "fetching_defaults": "Récupération de tous les micrologiciels disponibles pour les types d'appareils sélectionnés...", "last_db_update_modal": "Base de données du micrologiciel", "last_db_update_title": "Base de données", "one": "Micrologiciel", "select_default_device_types": "Veuillez sélectionner tous les types d'appareils que vous souhaitez cibler avec cette nouvelle règle de micrologiciel par défaut. Si vous ne trouvez pas le type d'appareil souhaité, cela signifie qu'une règle est déjà appliquée.", "select_default_revision": "Vous pouvez maintenant sélectionner la révision minimale que vous souhaitez que vos types d'appareils ciblent", "start_db_update": "<PERSON><PERSON><PERSON><PERSON> la mise à jour de la base de données", "started_db_update": "Mise à jour de la base de données d<PERSON>, cette opération devrait prendre jusqu'à 25 minutes", "update_success": "Informations sur le micrologiciel par défaut enregistrées !"}, "footer": {"powered_by": "Alimenté par", "version": "Version"}, "form": {"captive_web_root_explanation": "Veuillez utiliser uniquement des fichiers .tar (pas de fichiers compressés comme .targz, par exemple)", "certificate_file_explanation": "Veuillez utiliser un fichier .pem qui commence par \"-----BEGIN CERTIFICATE-----\" et se termine par \"-----END CERTIFICATE-----\"", "invalid_alphanumeric_with_dash": "Caractères acceptés. sont uniquement alphanumériques (lettres et chiffres)", "invalid_cidr": "Adresse IPv4 CIDR non valide. Exemple : ***********/12", "invalid_email": "Email In<PERSON>ide", "invalid_file_content": "Contenu de fichier non valide, veuil<PERSON>z confirmer qu'il est au format valide", "invalid_fqdn_host": "Nom d'hôte FQDN non valide", "invalid_hostname": "Nom d'hôte non valide : il doit être composé uniquement de caractères alphanumériques et de tirets", "invalid_icon_lang": "Langue non valide, elle doit être dans un format à 3 lettres (eng, fre, ger, ita, etc.)", "invalid_ieee": "Pour ce protocole de cryptage, ieee80211w doit être soit 'facultatif' soit 'obligatoire'", "invalid_ieee_required": "ieee80211w doit être \"obligatoire\" pour ce protocole de cryptage", "invalid_interfaces": "Chaîne JSON d'interfaces non valide. Veuillez confirmer que votre valeur est : JSON valide et a des interfaces comme seule clé et que la valeur des interfaces est un tableau. Exemple : {\"interfaces\": []}", "invalid_ipv4": "Adresse IPv4 invalide (ex. : ***********/16)", "invalid_ipv6": "Adresse IPv6 invalide (ex. : fd00::/64)", "invalid_json": "Chaîne JSON non valide", "invalid_lease_time": "Valeur de durée de bail non valide ! Ils doivent être au format digitUnit. Par exemple : 6d2h5m, ce qui signifie 6 jours, 2 heures et 5 minutes. Voici les unités acceptées : m, h, d. Si vous ne voulez pas utiliser une unité, omettez-la complètement. Donc au lieu de dire 0d2h0m, utilisez 2h", "invalid_mac_uc": "Valeur MAC non valide, par exemple : 00:00:5e:00:53:af", "duplicate_mac": "Cette adresse MAC existe déjà dans la liste", "duplicate_ip": "Cette adresse IP existe déjà dans la liste", "invalid_password": "<PERSON>t de passe invalide, veuil<PERSON>z consulter la politique de mot de passe", "invalid_phone_number": "Numéro de téléphone invalide", "invalid_phone_numbers": "Un ou plusieurs des numéros de téléphone sont invalides. Veuillez les fournir sans symboles ni espaces, ou dans ce format : +1(123)123-1234", "invalid_port_range": "Valeur de port non valide. Il doit être supérieur à 0 et inférieur à 65 535. Si vous utilisez une plage de ports, assurez-vous que le deuxième port est un nombre supérieur au premier.", "invalid_port_ranges": "Combinaison de plages de ports non valide ! Assurez-vous que les deux valeurs de port sont du même type (simple ou plage). S'il s'agit de plages, assurez-vous qu'elles couvrent toutes les deux le même nombre de ports", "invalid_proto_6g": "Ce protocole de cryptage ne peut pas être utilisé sur un SSID qui utilise la 6G", "invalid_proto_passpoint": "Ce protocole de cryptage ne peut pas être utilisé avec un SSID de point de passe. Veuillez sélectionner un protocole qui peut utiliser Radius", "invalid_select_ports": "Valeurs incompatibles entre les interfaces ! Veuillez vous assurer qu'il n'y a pas de combinaison PORT/VLAN ID en double entre vos interfaces", "invalid_static_ipv4_d": "<PERSON><PERSON><PERSON> invalide, cette plage est réservée à la multidiffusion (classe D). Le premier octet doit être 223 ou moins", "invalid_static_ipv4_e": "<PERSON><PERSON><PERSON> invalide, cette plage est réservée aux expérimentations (classe E). Le premier octet doit être 223 ou moins", "invalid_third_party": "Chaîne JSON tierce non valide. Veuillez confirmer que votre valeur est un JSON valide", "key_file_explanation": "Veuillez utiliser un fichier .pem qui commence par \"-----BEGIN PRIVATE KEY-----\" et se termine par \"-----END PRIVATE KEY-----\"", "max_length": "Longueur maximale de {{max}}  caractères.", "max_value": "Valeur maximale de  {{max}}", "min_length": "Longueur minimale de {{min}}  caractères.", "min_max_string": "La valeur doit être d'une longueur comprise entre {{min}} (inclus) et {{max}} (inclus)", "must_string": "la longueur de la valeur doit être {{max}}", "min_value": "Valeur minimale de  {{min}}", "missing_interface_upstream": "<PERSON><PERSON> <PERSON><PERSON> disposer d'au moins une interface en mode ponté (pontage de couche 2). Actuellement, toutes vos interfaces sont en mode routage (NAT)", "new_email_to_notify": "Nouvel e-mail à notifier", "new_phone_to_notify": "Nouveau téléphone à notifier", "not_selected": "Non séléctionné", "not_uploaded_yet": "pas encore téléchargé", "pem_file_explanation": "Veuillez utiliser un fichier .pem", "required": "Champs obligatoires", "using_file": "(en utilisant le fichier : {{filename}})", "value_recorded_no_filename": "<PERSON>ur enregistrée, pas de nom de fichier", "invalid_mac_format": "Le format légal Mac ne peut contenir que des lettres et des chiffres minuscules, avec un délimiteur de ':'. Veuillez utiliser le format: 00:00:5e:00:53:af", "must_be_integer": "Doit être un nombre entier", "range_min": "La valeur doit être d'au moins {{min}}", "range_max": "La valeur ne doit pas dépasser {{max}}", "range_both": "La valeur doit être comprise entre {{min}} et {{max}}", "invalid_label_name": "Le nom de l'étiquette ne peut contenir que des lettres et des chiffres", "invalid_static_ipv4_loopback": "<PERSON><PERSON><PERSON> invalid<PERSON>, la plage d'adresses loopback ne peut pas être utilisée", "invalid_domain_or_ip": "Il ne peut s'agir qu'd'une adresse IP ou d'un nom de domaine, par exemple: www.fs.com"}, "inventory": {"computed_configuration": "Configuration calculée", "dev_class": "classe de périphérique", "device_type": "Type d'appareil", "error_reboots": "Erreur lors de l'envoi de la commande : {{count}}", "error_remove_claim": "Erreur lors de la suppression de la revendication : {{e}}", "error_upgrades": "Erreur lors de l'envoi de la commande de mise à niveau : {{count}}", "invalid_serial_number": "Numéro de série invalide. Un numéro de série ne doit comporter que 12 caractères HEX (A-F, 0-9)", "invalid_device_name": "Nom de périphérique invalide. Doit contenir 1-2 caractères alphanumériques, ou 3-63 caractères alphanumériques avec des tirets (ne peut pas commencer ou se terminer par un tiret)", "no_computed": "Aucune configuration calculée : vous devrez attribuer une configuration valide pour la voir", "no_firmware": "Aucun micrologiciel disponible pour le type d'appareil : {{count}}", "not_connected": "Appareil non connecté : {{count}}", "parent": "Parent", "serial_number": "Numéro de série", "skipped_upgrades": "Mises à niveau ignorées : {{count}}", "success_remove_claim": "Revendication supprimée avec succès sur : {{serial}}", "successful_reboots": "Redémarrage commencé : {{count}}", "successful_upgrades": "Mises à jour réussies : {{count}}", "tag_one": "Étiquette", "tags": "Balises d'inventaire", "title": "Inventaire", "warning_reboots": "Appareils non connectés : {{count}}", "warning_upgrades": "Appareils non connectés : {{count}}", "label": "Étiquette", "site": "Site", "create_label": "<PERSON><PERSON>er une étiquette"}, "jobs": {"error_macs": "MAC d'erreur", "job": "Emploi", "job_details": "<PERSON>é<PERSON> du poste", "notify_emails": "Notifier les e-mails", "notify_sms": "Notifier SMS", "successful_macs": "MAC réussis", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "keys": {"description_error": "La description doit comporter moins de 64 caractères", "expire_error": "L'expiration ne peut pas être supérieure à un an dans le futur", "expires": "EXPIRÉ", "max_keys": "<PERSON> de clés at<PERSON> (10)", "name_error": "Le nom doit être unique et comporter entre 6 et 20 caractères alphanumériques", "one": "Clé API", "other": "Clés API"}, "locations": {"address_line_one": "Adresse Ligne 1", "address_line_two": "Adresse ligne deux", "building_name": "Nom du bâtiment", "city": "Ville", "claim_explanation": "Pour revendiquer des emplacements, vous pouvez utiliser le tableau ci-dessous", "country": "Pays", "elevation": "Élévation", "geocode": "Geo code", "lat": "Latitude", "longitude": "Longitude", "one": "Emplacement", "other": "Emplacements", "postal": "Zip / code postal", "state": "Etat / Province", "title": "Emplacements", "to_claim": "Emplacements à réclamer", "view_gps": ""}, "login": {"access_policy": "Politique d'accès", "change_password_error": "Mot de passe rejeté, il s'agit peut-être d'un ancien mot de passe", "change_password_explanation": "Saisissez et confirmez votre nouveau mot de passe", "change_your_password": "Changer le mot de passe", "confirm_new_password": "Confirmer le nouveau mot de passe", "email_instructions": "Vous devriez bientôt recevoir un code à 6 chiffres sur votre adresse e-mail. Si vous ne le trouvez pas, veuillez vérifier votre dossier spam.", "error_sending_code": "Erreur lors de la tentative d'envoi du code : {{e}}", "forgot_password": "Mot de passe oublié?", "forgot_password_instructions": "Entrez votre adresse e-mail pour recevoir un e-mail contenant les instructions pour réinitialiser votre mot de passe", "forgot_password_successful": "Vous devriez bientôt recevoir un e-mail contenant les instructions pour réinitialiser votre mot de passe. S'il vous plaît assurez-vous de vérifier vos spams si vous ne trouvez pas l'e-mail", "forgot_password_title": "Mot de passe oublié", "google_instructions": "Veuillez saisir le code à 6 chiffres de votre application Google Authenticator. S'il est sur le point d'expirer, vous pouvez en attendre un nouveau", "invalid_credentials": "Informations d'identification non valides, veuillez confirmer que vous utilisez le bon e-mail et le bon mot de passe.", "invalid_mfa": "Code invalide! Veuillez réessayer", "login_explanation": "Entrez votre email et votre mot de passe pour vous connecter", "new_password": "Nouveau mot de passe", "password_policy": "Politique de mot de passe", "remember_me": "Souviens-toi de moi", "resend": "<PERSON><PERSON><PERSON>", "resent_code": "Code renvoyé avec succès !", "reset_password": "Réinitialiser le mot de passe", "sign_in": "se connecter", "sms_instructions": "Vous devriez bientôt recevoir un code à 6 chiffres sur votre téléphone. Veuillez le saisir ci-dessous pour vous connecter", "suspended_error": "<PERSON><PERSON><PERSON> susp<PERSON>, ve<PERSON><PERSON>z contacter votre administrateur", "verification": "Vérifiez votre connexion", "waiting_for_email_verification": "Compte pas encore e-mail validé. Veuillez consulter votre boîte de réception ou demander à votre administrateur de renvoyer une validation", "welcome_back": "Nous saluons le retour!", "your_email": "Votre adresse email", "your_new_password": "Votre nouveau mot de passe", "your_password": "Votre mot de passe"}, "logs": {"configuration_upgrade": "Mise à jour de la configuration", "device_firmware_upgrade": "Mise à jour du firmware", "device_statistics": "Statistiques de l'appareil", "export": "Exportation", "filter": "Filtre", "firmware": "Micrologiciel", "global_connections": "Connexions mondiales", "level": "Niveau", "message": "Message", "one": "Bûche", "receiving_types": "Filtre de notification", "security": "SÉCURITÉ", "source": "La source", "thread": "Fil de discussion", "venue_config": "Configuration", "venue_reboot": "<PERSON><PERSON><PERSON><PERSON>", "venue_upgrade": "<PERSON><PERSON><PERSON><PERSON>"}, "map": {"auto_align": "Alignement automatique", "auto_map": "Carte automatique", "by_others": "Cartes par d'autres", "cumulative_devices": "Appareils cumulatifs", "default_map": "Carte par défaut", "delete_warning": "Voulez-vous vraiment supprimer cette carte ? Cette opération n'est pas réversible", "duplicating": "Dup<PERSON>r la carte", "my_maps": "mes cartes", "other": "Plans", "root": "Ra<PERSON>", "root_node": "Noeud principal", "set_as_default": "Définir par défaut", "title": "<PERSON><PERSON>", "visibility": "Visibilité"}, "notification": {"one": "Notification", "other": "Les notifications"}, "openroaming": {"pool_strategy": "Stratégie de pool", "radius_endpoint_one": "Point final de rayon", "radius_endpoint_other": "Points de terminaison du rayon"}, "operator": {"delete_explanation": "Voulez-vous vraiment supprimer cet opérateur ? Cette opération n'est pas réversible", "delete_operator": "Supprimer l'opérateur", "import_location_from_device": "Importer depuis un autre appareil", "one": "Opérateur", "operator_one": "Opérateur", "operator_other": "Les opérateurs", "other": "Les opérateurs", "registration_id": "ID d'enregistrement"}, "organization": {"my_organization": "Mon organisation", "title": "Organisation"}, "overrides": {"delete_source": "Supprimer tous les remplacements de {{source}}", "ignore_overrides": "Ignorer les remplacements de configuration", "name_error": "Le paramètre est déjà défini par votre source", "one": "Remplacement de la configuration", "other": "Remplacements de configuration", "param_name": "<PERSON><PERSON><PERSON><PERSON>", "param_value": "<PERSON><PERSON>", "parameter": "<PERSON><PERSON><PERSON><PERSON>", "reason": "raison", "reason_error": "Votre raison doit être inférieure à 64 caractères. long", "source": "La source", "tx_power_error": "La puissance de transmission doit être comprise entre 1 et 32", "update_success": "Remplacements de configuration mis à jour !", "value": "<PERSON><PERSON>"}, "profile": {"about_me": "À propos de moi", "activate": "", "add_new_note": "Ajouter une note", "deactivate": "Désactiver", "delete_account": "Supprimer mon profil", "delete_account_confirm": "Supprimer toutes mes informations", "delete_warning": "Cette action est irréversible. Toutes les informations de votre profil et vos clés API seront supprimées", "deleted_success": "Votre profil est maintenant supprimé, nous allons maintenant vous déconnecter...", "disabled": "Désactivé", "enabled": "Activée", "manage_avatar": "<PERSON><PERSON><PERSON> l'avatar", "new_password": "Nouveau mot de passe", "new_password_confirmation": "Confirmer le nouveau mot de passe", "your_profile": "Votre profil"}, "resources": {"configuration_resource": "ressource", "title": "Ressources", "variable": "Variable"}, "restrictions": {"algo": "Algorithme de signature", "allowed": "<PERSON><PERSON>", "countries": "Pays autorisés", "developer": "Mode développeur", "dfs": "Remplacement DFS", "gw_commands": "Commandes de passerelle", "identifier": "Identifiant", "key_verification": "Signature des informations clés", "restricted": "Limité", "signed_upgrade": "Mise à niveau signée uniquement", "title": "Restrictions", "tty": "Accès ATS"}, "roaming": {"account_created": "Nouveau compte créé !", "account_deleted": "Compte supprimé !", "account_one": "<PERSON><PERSON><PERSON>", "account_other": "<PERSON><PERSON><PERSON>", "certificate_deleted": "Certificat supprimé !", "certificate_one": "Certificat", "certificate_other": "Certificats", "city": "Ville", "common_name": "Nom commun", "country": "Pays", "global_reach": "Portée mondiale", "global_reach_account_id": "ID de compte ", "invalid_certificate": "certificat invalide", "invalid_key": "Clé privée invalide", "location_details_title": "Emplacement", "organization": "Organisation", "private_key": "Clé privée", "province": "province", "state": "Etat"}, "rrm": {"algorithm": "Algorithme", "algorithm_other": "Algorithmes", "cant_save_custom": "Impossible de créer ou de modifier des configurations RRM personnalisées tant que le serveur RRM n'est pas accessible. Veuillez consulter votre administrateur", "cron_error": "Erreur lors de l'analyse de l'expression CRON : ve<PERSON><PERSON><PERSON> confirmer qu'elle est valide", "cron_scheduler": "Planificateur CRON", "cron_templates": "<PERSON><PERSON><PERSON><PERSON>", "no_algos": "Nous ne sommes pas en mesure de récupérer les algorithmes RRM pour le moment", "no_providers": "Nous ne sommes pas en mesure de récupérer les fournisseurs RRM pour le moment", "param_error": "Vos paramètres ne respectent pas les règles de cet algorithme. Veuillez regarder les exemples d'algorithmes et les détails", "parameters": "Paramètres", "vendor": "vendeur", "version": "Version"}, "script": {"author": "<PERSON><PERSON><PERSON><PERSON>", "automatic": "Automatique", "create_success": "Le script est maintenant créé et prêt à être utilisé !", "custom_domain": "Domaine <PERSON>", "deferred": "<PERSON><PERSON><PERSON><PERSON>", "device_title": "Script de lancement", "diagnostics": "Diagnostics", "explanation": "Exécutez un script personnalisé sur cet appareil et téléchargez ses résultats", "file_not_ready": "Le résultat n'est pas encore téléchargé, veuillez revenir plus tard", "file_too_large": "Veuillez sélectionner un fichier de moins de 500 Ko", "helper": "Documentation", "no_script_available": "Aucun script disponible pour votre rôle d'utilisateur", "now": "À présent", "one": "<PERSON><PERSON><PERSON>", "other": "scripts", "restricted": "Utilisateurs autorisés à exécuter ce script", "schedule_success": "Exécution du script planifié !", "signature": "signature", "started_execution": "Lancement de l'exécution du script, venez plus tard pour les résultats !", "timeout": "Temps libre", "update_success": "Scénario mis à jour !", "upload_destination": "Destination de téléchargement des résultats", "upload_file": "<PERSON><PERSON>léverser un fichier", "visit_external_website": "Afficher la documentation", "when": "Planifier l'exécution"}, "service": {"billing_code": "Code de facturation", "billing_frequency": "Fréquence de facturation", "class_one": "Classe de service", "class_other": "Catégories de services", "cost": "Coût", "one": "Classe de service", "other": "Catégories de services"}, "simulation": {"cancel": "Annuler la simulation", "cancel_explanation": "<PERSON><PERSON><PERSON><PERSON><PERSON> la <PERSON> et effacez son enregistrement", "cancel_success": "Simulation arrêtée et efface son enregistrement !", "client_interval": "Intervalle client", "concurrent_devices": "Périphériques simultanés", "controller": "<PERSON><PERSON>", "current_live_devices": "Appareils en direct actuels", "currently_running_one": "Il y a actuellement {{count}} simulation en cours", "currently_running_other": "Il y a actuellement {{count}}  simulations en cours d'exécution", "delete_devices_confirm": "Voulez-vous vraiment supprimer tous les appareils et leurs statistiques de la passerelle ? Cette action n'est pas réversible", "delete_devices_loading": "Ce processus peut prendre jusqu'à 5 minutes", "delete_simulation_devices": "Supprimer des appareils", "delete_success": "Simulation supprimée !", "duration": "<PERSON><PERSON><PERSON>", "error_devices": "Périphériques <PERSON>'erreur", "healthcheck_interval": "Intervalle de vérification de l'état", "infinite": "Infini", "keep_alive": "Rester en vie", "mac_prefix": "Préfixe MAC", "mac_prefix_length": "Votre préfixe MAC doit être valide à 6 chiffres HEX (ex. : 00112233)", "max_associations": "Max. Les associations", "max_clients": "Max. Clients", "min_associations": "Min. Les associations", "min_clients": "Min. <PERSON>", "no_sim_running": "Aucune simulation en cours", "one": "simulation", "other": "Simulations", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "realtime_data": "Données en temps réel", "realtime_messages": "Messages en temps réel", "reconnect_interval": "Intervalle de reconnexion", "result_delete_success": "Résultat supprimé !", "rx": "<PERSON><PERSON><PERSON>", "rx_messages": "Messages reçus", "sim_currently_running": "Simulation \"{{sim}}\" en cours", "sim_history": "{{sim}} courses précédentes", "simulated": "<PERSON><PERSON><PERSON>", "start": "<PERSON><PERSON><PERSON><PERSON>", "start_success": "Lancement de la simulation !", "state_interval": "Intervalle d'état", "stop": "<PERSON><PERSON><PERSON><PERSON>", "stop_success": "Simulation arrêtée !", "threads": "Fils", "time_to_full": "Temps pour les appareils pleins", "tx": "Transmis", "tx_messages": "Messages de transmission", "view_previous_runs": "Voir les courses précédentes"}, "statistics": {"last_stats": "Dernières statistiques", "latest": "Dernières statistiques", "memory": "m<PERSON><PERSON><PERSON>"}, "subscribers": {"billing_contact_info": "Facturation et coordonnées", "claim_device_explanation": "Pour réclamer des appareils, vous pouvez utiliser le tableau ci-dessous. Si un appareil a déjà été réclamé par un utilisateur, vous devrez accéder à ses détails et le désattribuer avant de le réclamer.", "devices_claimed_one": "{{count}}  <PERSON><PERSON><PERSON><PERSON>", "devices_claimed_other": "{{count}} appareils revendiqués", "devices_to_claim_one": "{{count}} <PERSON><PERSON><PERSON><PERSON> ré<PERSON>er", "devices_to_claim_other": "{{count}} appareils <PERSON> r<PERSON><PERSON>", "error_claiming": "Erreur lors de la revendication : {{serials}}", "error_removing_claim": "Erreur lors de la suppression des revendications sur : {{serials}}", "no_subscribers_found": "<PERSON><PERSON><PERSON> a<PERSON> trouvé", "one": "<PERSON><PERSON><PERSON><PERSON>", "other": "Les abonnés", "reactivate_explanation": "Voulez-vous vraiment ré<PERSON>r cet abonné ?", "reactivate_title": "Ré<PERSON>r l'abonné", "title": "Les abonnés"}, "system": {"advanced": "<PERSON><PERSON><PERSON>", "backend_logs": "<PERSON>urn<PERSON> principaux", "configuration": "Configuration", "could_not_retrieve": "Erreur : impossible de récupérer les informations système {{name}} ", "endpoint": "Point final", "hostname": "nom d'hôte", "info": "Information système", "level": "niveau de journal", "logging": "Enregistrement", "no_log_levels": "Aucun niveau de journal signalé", "os": "Système opérateur", "processors": "Processeurs", "reload_chosen_subsystems": "Recharger les sous-systèmes choisis", "secrets": "Secrets", "secrets_create": "<PERSON><PERSON><PERSON> un secret", "secrets_one": "Secret", "services": "Prestations de service", "start": "D<PERSON>but", "subsystems": "Sous-systèmes", "success_reload": "Commande de rechargement envoyée avec succès !", "systems_to_reload": "Choisissez les systèmes à recharger", "title": "Système", "update_level_success": "Niveaux de journal mis à jour !", "update_levels": "Mettre à jour", "uptime": "La disponibilité", "version": "Version"}, "table": {"columns": "Les colonnes", "columns_hidden_one": "{{count}} <PERSON><PERSON><PERSON>", "columns_hidden_other": "{{count}}  colonnes masquées", "display_column": "<PERSON><PERSON><PERSON><PERSON>", "drag_always_show": "Vous ne pouvez pas masquer cette colonne, mais vous pouvez modifier sa position", "drag_explanation": "Glisser-déposer pour réorganiser et modifier la visibilité des colonnes", "drag_locked": "Cette colonne est verrouillée dans sa position", "export_current_page": "Page actuelle uniquement", "first_page": "Première page", "go_to_page": "<PERSON>er à la page", "hide_column": "<PERSON><PERSON>", "last_page": "Dernière page", "next_page": "<PERSON> suivante", "page": "Page", "preferences": "Préférences de tableau", "previous_page": "<PERSON> p<PERSON>", "reset": "Remettre à zéro les préférences", "settings": "Réglages"}, "user": {"email_not_validated": "Mail non valide", "error_fetching": "Erreur lors de la récupération des informations utilisateur : {{e}}", "password": "Mot de passe", "role": "R<PERSON><PERSON>", "suspended": "Suspendu", "title": "Utilisa<PERSON>ur"}, "users": {"change_password": "Forcer le changement de mot de passe", "email_validation": "Validation des e-mails", "error_fetching": "Erreur lors de la récupération des utilisateurs : {{e}}", "error_sending_validation": "Erreur lors de l'envoi de l'e-mail de validation : {{e}}", "last_login": "Dernière connexion", "login_id": "Identifiant de connexion", "one": "Utilisa<PERSON>ur", "re_validate_email": "Re-valider l'e-mail", "reactivate_user": "Réactiver l'utilisateur", "reset_mfa": "Réinitialiser l'authentification multifacteur", "reset_mfa_success": "L'utilisateur MFA a bien été réinitialisé !", "reset_password": "Réinitialiser le mot de passe", "reset_password_error": "Erreur lors de la tentative de réinitialisation du mot de passe utilisateur : {{e}}", "reset_password_success": "L'e-mail de réinitialisation du mot de passe a bien été envoyé à l'adresse e-mail de l'utilisateur", "role": "R<PERSON><PERSON>", "send_validation": "Envoyer un e-mail de validation", "send_validation_explanation": "Voulez-vous renvoyer le lien de vérification par e-mail ?", "stop_suspension": "<PERSON><PERSON><PERSON><PERSON>", "success_sending_validation": "E-mail de validation envoyé !", "suspend": "<PERSON><PERSON><PERSON><PERSON>", "suspend_success": "L'utilisateur est maintenant suspendu", "suspended": "Suspendu", "title": "utilisateurs", "waitiing_for_email_verification": "Email non vérifié"}, "venues": {"confirm_remove_contact": "Voulez-vous supprimer ce contact de ce lieu ?", "create_child": "<PERSON><PERSON><PERSON> un lieu enfant", "error_remove_contact": "E<PERSON>ur lors de la tentative de suppression du contact : {{e}}", "error_update_devices": "Erreur lors du démarrage de la mise à jour de l'appareil : {{e}}", "go_to_page": "<PERSON>er à la page", "one": "lieu", "reboot_all_devices": "<PERSON><PERSON><PERSON><PERSON> tous les appareils", "sub_one": "Sous-lieu", "sub_other": "Sous-lieux", "subvenues": "Sous-lieux", "successfully_reboot_devices": "Redémarrage de {{num}}  appareils !", "successfully_removed_contact": "Contact supprimé avec succès !", "successfully_update_devices": "Mise à jour de {{num}}  appareils !", "title": "<PERSON>", "update_all_devices": "Mettre à jour toutes les configurations de périphérique", "update_success": "Lieu mis à jour !", "upgrade_all_devices": "Mettre à niveau le micrologiciel de tous les appareils", "upgrade_all_devices_error": "Erreur lors de la mise à jour des appareils : {{e}}", "upgrade_all_devices_success": "La mise à niveau des appareils a démarré avec succès !", "upgrade_options_available": "Voici toutes les révisions disponibles, ve<PERSON><PERSON><PERSON> sélectionner celle vers laquelle vous souhaitez que TOUS les appareils de ce lieu soient mis à niveau", "use_existing": "Utiliser l'existant", "use_existing_contacts": "Utiliser les contacts existants", "use_this_contact": "Utilisez ce contact"}}