#volumes:
#  web-dist:
#    driver: local
#    driver_opts:
#      o: bind
#      type: none
#      device: ./data/dist


services:
  ampcon-t-webpack:
    image: harbor.ampcon.com/ampcon-t-base/node:alpine
    build:
      context: ./frontend
      dockerfile: ./Dockerfile
      args:
        MODULE_TYPE: ${MODULE_TYPE:-test}
        IMG_CENTER_PROXY_DOMAIN: ${IMG_CENTER_PROXY_DOMAIN:-test}
    container_name: ampcon-t-webpack
    working_dir: /home/<USER>/ampcon-otn
    volumes:
      - ./data/dist:/home/<USER>/ampcon-otn/code/dist
      - ./frontend:/home/<USER>/ampcon-otn/code
    environment:
      - MODULE_TYPE=${MODULE_TYPE:-test}
    command: sh ./code/build_web.sh