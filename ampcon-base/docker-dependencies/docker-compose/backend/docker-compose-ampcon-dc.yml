services:
  ######################################################### server-ampcon start #########################################################
  nginx-service:
    image: ampcon-nginx:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-NGINX
    depends_on:
      flask-main-service:
        condition: service_healthy
    container_name: nginx-service
    restart: always
    tty: true
    environment:
      TZ: "UTC"
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "nginx-service"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./data/nginx/sites-enabled:/etc/nginx/sites-enabled
      - ./data/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ampcon_data/nginx/logs:/var/log/nginx
      - ./data/dist:/etc/nginx/dist
    working_dir: /etc/nginx
    privileged: true
    healthcheck:
      test: ["CMD-SHELL", "netstat -ltn | grep -E ':80 |:443 '"]
      interval: 5s
      timeout: 5s
      retries: 10
    ports:
      - "80:80"
      - "443:443"
    networks:
      - custom_net

  flask-main-service:
    image: ampcon-main:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-MAIN
    container_name: flask-main-service
    privileged: true
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "flask-main-service"
    depends_on:
      ssh-service:
        condition: service_healthy
      celery-worker-service:
        condition: service_healthy
      celery-beat-service:
        condition: service_healthy
      redis-service:
        condition: service_healthy
    restart: on-failure
    environment:
      TZ: "UTC"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./ampcon_data/openvpn_log:/var/log/openvpn
      - ./data/pre-built:/usr/share/automation/pre-built
      - ./data/agent/keys/ca.key:/etc/openvpn/server/ca.key
      - ./data/agent/keys/ca.crt:/etc/openvpn/server/ca.crt
      - ./data/agent/keys/ca.crt:/etc/openvpn/ca.crt
      - ./data/agent/keys/automation.crt:/etc/openvpn/automation.crt
      - ./data/agent/keys/automation.key:/etc/openvpn/automation.key
      - ./data/agent/keys/dh2048.pem:/etc/openvpn/dh2048.pem
      - ./data/agent/vpn_config/server.conf:/etc/openvpn/server.conf
      - ./data/agent:/usr/share/automation/server/agent
      - ./data/ansible_playbook:/usr/share/automation/server/ansible_playbook
      - ./data/celery_app/celeryconfig.py:/usr/share/automation/server/celery_app/celeryconfig.py
      - ./data/config_gen:/usr/share/automation/server/config_gen
      - ./data/img:/usr/share/automation/server/img
      - ./data/server_keys:/usr/share/automation/server/server_keys
      - ./data/vpn:/usr/share/automation/server/vpn
      - ./data/settings/inner/alembic-mysql.ini:/usr/share/automation/server/alembic-mysql.ini
      - ./data/settings/inner/alembic-postgresql.ini:/usr/share/automation/server/alembic-postgresql.ini
      - ./data/settings/inner/automation.ini:/usr/share/automation/server/automation.ini
      - ./data/settings/inner/upgrade_automation.ini:/usr/share/automation/server/upgrade_automation.ini
      - ./data/settings/inner/default_imgs_for_dc.json:/usr/share/automation/server/default_imgs.json
      - ./data/settings/inner/model_platform_mapping.json:/usr/share/automation/server/model_platform_mapping.json
      - ./data/settings/inner/request_level_role.json:/usr/share/automation/server/request_level_role.json
      - ./data/settings/outer/server.cnf:/usr/share/automation/server.cnf
      - ./data/rsyslog_conf/rsyslog.d:/etc/rsyslog.d
      - ./.env:/usr/share/automation/server/.env
      - ./data/settings/inner/ampcon-private.key:/usr/share/automation/server/ampcon-private.key
      - ./data/settings/inner/ampcon-public.key:/usr/share/automation/server/ampcon-public.key
      - ./data/settings/inner/pica-public.key:/usr/share/automation/server/pica-public.key
      - ./data/monitor/rules:/usr/share/automation/server/monitor/rules:rw
      - ./data/monitor/email_template:/usr/share/automation/server/monitor/email_template:rw
      - ./data/monitor/roce:/usr/share/automation/server/monitor/roce:rw
      - ./data/monitor/node_exporter:/usr/share/automation/server/monitor/node_exporter:rw
      - ./data/monitor/prometheus_default_values.yml:/usr/share/automation/server/monitor/prometheus_default_values.yml:rw
      - ./data/settings/inner/mac_address:/usr/share/automation/server/mac_address:ro
      - ./data/ansible_device_ssh_key:/usr/share/automation/server/ansible_device_ssh_key
      - ./data/license:/usr/share/automation/server/license
    working_dir: /usr/share/automation/server
    healthcheck:
      test:
        [
          "CMD",
          "python",
          "-c",
          "import requests; response = requests.post('http://localhost:443/check_status')",
        ]
      interval: 5s
      timeout: 5s
      start_period: 20s
      retries: 20
    command: sh start.sh
    networks:
      - custom_net

  celery-worker-service:
    image: ampcon-main:${REACT_APP_VERSION:-latest}
    container_name: celery-worker-service
    depends_on:
      mysql-service:
        condition: service_healthy
      rabbitmq-service:
        condition: service_healthy
      openvpn-service:
        condition: service_healthy
    restart: always
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "celery-worker-service"
    privileged: true
    environment:
      TZ: "UTC"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./ampcon_data/openvpn_log:/var/log/openvpn
      - ./data/agent/keys/ca.key:/etc/openvpn/server/ca.key
      - ./data/agent/keys/ca.crt:/etc/openvpn/server/ca.crt
      - ./data/agent/keys/ca.crt:/etc/openvpn/ca.crt
      - ./data/agent/keys/automation.crt:/etc/openvpn/automation.crt
      - ./data/agent/keys/automation.key:/etc/openvpn/automation.key
      - ./data/agent/keys/dh2048.pem:/etc/openvpn/dh2048.pem
      - ./data/agent/vpn_config/server.conf:/etc/openvpn/server.conf
      - ./data/agent:/usr/share/automation/server/agent
      - ./data/ansible_playbook:/usr/share/automation/server/ansible_playbook
      - ./data/celery_app/celeryconfig.py:/usr/share/automation/server/celery_app/celeryconfig.py
      - ./data/config_gen:/usr/share/automation/server/config_gen
      - ./data/img:/usr/share/automation/server/img
      - ./data/server_keys:/usr/share/automation/server/server_keys
      - ./data/vpn:/usr/share/automation/server/vpn
      - ./data/settings/inner/alembic-mysql.ini:/usr/share/automation/server/alembic-mysql.ini
      - ./data/settings/inner/alembic-postgresql.ini:/usr/share/automation/server/alembic-postgresql.ini
      - ./data/settings/inner/automation.ini:/usr/share/automation/server/automation.ini
      - ./.env:/usr/share/automation/server/.env
      - ./data/celery_app/celery_worker_health_check.sh:/usr/local/bin/celery_worker_health_check.sh
      - ./data/settings/inner/ampcon-private.key:/usr/share/automation/server/ampcon-private.key
      - ./data/settings/inner/ampcon-public.key:/usr/share/automation/server/ampcon-public.key
      - ./data/settings/inner/pica-public.key:/usr/share/automation/server/pica-public.key
      - ./data/settings/inner/mac_address:/usr/share/automation/server/mac_address:ro
      - ./data/ansible_device_ssh_key:/usr/share/automation/server/ansible_device_ssh_key
      - ./data/monitor/node_exporter:/usr/share/automation/server/monitor/node_exporter:rw
      - ./data/monitor/settings:/usr/share/automation/server/monitor/settings
      - ./data/license:/usr/share/automation/server/license
      - ./data/monitor/roce:/usr/share/automation/server/monitor/roce:rw
    working_dir: /usr/share/automation/server
    healthcheck:
      test: ["CMD", "/usr/local/bin/celery_worker_health_check.sh"]
      interval: 5s
      timeout: 10s
      retries: 20
    command: >
      sh -c "ip route add ********/20 via $(getent hosts openvpn-service | awk '{ print $1 }') &&
            python -O /usr/local/bin/celery -A celery_app.my_celery_app worker -Q pica8_beat_task,pica8_normal_task,pica8_deploy_upgrade_task,snmp_trap_alarm -l INFO"
    networks:
      - custom_net

  celery-worker-config-distribute-service:
    image: ampcon-main:${REACT_APP_VERSION:-latest}
    container_name: celery-worker-config-distribute-service
    depends_on:
      mysql-service:
        condition: service_healthy
      rabbitmq-service:
        condition: service_healthy
      openvpn-service:
        condition: service_healthy
    restart: always
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "celery-worker-config-distribute-service"
    privileged: true
    environment:
      TZ: "UTC"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./ampcon_data/openvpn_log:/var/log/openvpn
      - ./data/agent/keys/ca.key:/etc/openvpn/server/ca.key
      - ./data/agent/keys/ca.crt:/etc/openvpn/server/ca.crt
      - ./data/agent/keys/ca.crt:/etc/openvpn/ca.crt
      - ./data/agent/keys/automation.crt:/etc/openvpn/automation.crt
      - ./data/agent/keys/automation.key:/etc/openvpn/automation.key
      - ./data/agent/keys/dh2048.pem:/etc/openvpn/dh2048.pem
      - ./data/agent/vpn_config/server.conf:/etc/openvpn/server.conf
      - ./data/agent:/usr/share/automation/server/agent
      - ./data/ansible_playbook:/usr/share/automation/server/ansible_playbook
      - ./data/celery_app/celeryconfig.py:/usr/share/automation/server/celery_app/celeryconfig.py
      - ./data/config_gen:/usr/share/automation/server/config_gen
      - ./data/img:/usr/share/automation/server/img
      - ./data/server_keys:/usr/share/automation/server/server_keys
      - ./data/vpn:/usr/share/automation/server/vpn
      - ./data/settings/inner/alembic-mysql.ini:/usr/share/automation/server/alembic-mysql.ini
      - ./data/settings/inner/alembic-postgresql.ini:/usr/share/automation/server/alembic-postgresql.ini
      - ./data/settings/inner/automation.ini:/usr/share/automation/server/automation.ini
      - ./.env:/usr/share/automation/server/.env
      - ./data/celery_app/celery_worker_health_check.sh:/usr/local/bin/celery_worker_health_check.sh
      - ./data/settings/inner/ampcon-private.key:/usr/share/automation/server/ampcon-private.key
      - ./data/settings/inner/ampcon-public.key:/usr/share/automation/server/ampcon-public.key
      - ./data/settings/inner/pica-public.key:/usr/share/automation/server/pica-public.key
      - ./data/settings/inner/mac_address:/usr/share/automation/server/mac_address:ro
      - ./data/ansible_device_ssh_key:/usr/share/automation/server/ansible_device_ssh_key
      - ./data/monitor/node_exporter:/usr/share/automation/server/monitor/node_exporter:rw
      - ./data/monitor/settings:/usr/share/automation/server/monitor/settings
      - ./data/license:/usr/share/automation/server/license
    working_dir: /usr/share/automation/server
    healthcheck:
      test: ["CMD", "/usr/local/bin/celery_worker_health_check.sh"]
      interval: 5s
      timeout: 10s
      retries: 20
    command: >
      sh -c "ip route add ********/20 via $(getent hosts openvpn-service | awk '{ print $1 }') &&
             python -O /usr/local/bin/celery -A celery_app.my_celery_app worker -Q config_distribution_task -l INFO"
    networks:
      - custom_net

  celery-beat-service:
    image: ampcon-main:${REACT_APP_VERSION:-latest}
    container_name: celery-beat-service
    depends_on:
      mysql-service:
        condition: service_healthy
      rabbitmq-service:
        condition: service_healthy
    restart: on-failure
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "celery-beat-service"
    environment:
      TZ: "UTC"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./ampcon_data/openvpn_log:/var/log/openvpn
      - ./data/agent/keys/ca.key:/etc/openvpn/server/ca.key
      - ./data/agent/keys/ca.crt:/etc/openvpn/server/ca.crt
      - ./data/agent/keys/ca.crt:/etc/openvpn/ca.crt
      - ./data/agent/keys/automation.crt:/etc/openvpn/automation.crt
      - ./data/agent/keys/automation.key:/etc/openvpn/automation.key
      - ./data/agent/keys/dh2048.pem:/etc/openvpn/dh2048.pem
      - ./data/agent/vpn_config/server.conf:/etc/openvpn/server.conf
      - ./data/ansible_playbook:/usr/share/automation/server/ansible_playbook
      - ./data/celery_app/celeryconfig.py:/usr/share/automation/server/celery_app/celeryconfig.py
      - ./data/config_gen:/usr/share/automation/server/config_gen
      - ./data/img:/usr/share/automation/server/img
      - ./data/server_keys:/usr/share/automation/server/server_keys
      - ./data/vpn:/usr/share/automation/server/vpn
      - ./data/settings/inner/alembic-mysql.ini:/usr/share/automation/server/alembic-mysql.ini
      - ./data/settings/inner/alembic-postgresql.ini:/usr/share/automation/server/alembic-postgresql.ini
      - ./data/settings/inner/automation.ini:/usr/share/automation/server/automation.ini
      - ./.env:/usr/share/automation/server/.env
      - ./data/settings/inner/ampcon-private.key:/usr/share/automation/server/ampcon-private.key
      - ./data/settings/inner/ampcon-public.key:/usr/share/automation/server/ampcon-public.key
      - ./data/settings/inner/pica-public.key:/usr/share/automation/server/pica-public.key
      - ./data/settings/inner/mac_address:/usr/share/automation/server/mac_address:ro
      - ./data/ansible_device_ssh_key:/usr/share/automation/server/ansible_device_ssh_key
    working_dir: /usr/share/automation/server
    command: sh -c "python -O /usr/local/bin/celery -A celery_app.my_celery_app beat -S celery_app.utils:DatabaseScheduler -l INFO"
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "if [ $(ps -ef | grep '[c]elery' | wc -l) -gt 1 ]; then exit 0; else exit 1; fi",
        ]
      interval: 5s
      timeout: 5s
      retries: 20
    networks:
      - custom_net

  ssh-service:
    image: ampcon-ssh:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-SSH
    depends_on:
      mysql-service:
        condition: service_healthy
      rabbitmq-service:
        condition: service_healthy
      openvpn-service:
        condition: service_healthy
    container_name: ssh-service
    privileged: true
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "ssh-service"
    restart: on-failure
    environment:
      TZ: "UTC"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./data/settings/inner/automation.ini:/usr/share/automation/server/automation.ini
      - ./data/onie_install:/usr/share/automation/server/onie_install
      - ./data/agent/auto-deploy.py:/usr/share/automation/server/agent/auto-deploy.py
      - ./data/config_gen:/usr/share/automation/server/config_gen
      - ./data/img:/usr/share/automation/server/img
      - ./data/settings/inner/model_platform_mapping.json:/usr/share/automation/server/model_platform_mapping.json
      - ./.env:/usr/share/automation/server/.env
    working_dir: /usr/share/automation/server
    command: sh -c "ip route add ********/20 via $(getent hosts openvpn-service | awk '{ print $1 }') && python collect/ssh_service.pyc"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 5s
      timeout: 10s
      retries: 20
    networks:
      - custom_net

  openvpn-service:
    image: ampcon-openvpn:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-OPENVPN
    container_name: openvpn-service
    depends_on:
      rsyslog-service:
        condition: service_healthy
    restart: always
    tty: true
    environment:
      TZ: "UTC"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./data/openvpn:/etc/openvpn
      - ./ampcon_data/openvpn_log:/var/log/openvpn
    command: sh -c "sh add_iptables.sh >> /var/log/openvpn/iptables.log 2>&1 & exec openvpn --config /etc/openvpn/server.conf"
    working_dir: /etc/openvpn
    privileged: true
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "openvpn-service"
    healthcheck:
      test: ["CMD-SHELL", "ps -ef | grep '[o]penvpn'"]
      interval: 5s
      timeout: 5s
      retries: 5
    ports:
      - "80:80/udp"
    networks:
      - custom_net

  mysql-service:
    image: ampcon-db:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-DB
    container_name: mysql-service
    depends_on:
      rsyslog-service:
        condition: service_healthy
    restart: unless-stopped
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "mysql-service"
    environment:
      TZ: "UTC"
      MYSQL_ROOT_PASSWORD: root
    healthcheck:
      #      test: out=$$(mysqladmin ping -h localhost -P 3306 -u root --password=root 2>&1); echo $$out | grep 'mysqld is alive' || { echo $$out; exit 1; }
      # test: ["CMD", "test", "-f", "/var/lib/mysql/load_all_data_done"]
      test:
        [
          "CMD-SHELL",
          "mysqladmin ping -h localhost -P 3306 -u root --password=root 2>&1 | grep 'mysqld is alive' && test -f /var/lib/mysql/load_all_data_done",
        ]
      interval: 10s
      timeout: 10s
      retries: 60
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./ampcon_data/mysql:/var/lib/mysql
      - ./data/init_sql:/docker-entrypoint-initdb.d
      - ./data/mysql/restart_init:/docker-entrypoint-every-time
      - ./data/mysql/start_sh/docker-entrypoint.sh:/usr/local/bin/docker-entrypoint.sh
      - ./data/custom_sql:/opt/sql
      - ./data/settings/outer/server.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - custom_net

  rabbitmq-service:
    image: rabbitmq:${REACT_APP_VERSION:-latest}
    container_name: rabbitmq-service
    restart: unless-stopped
    depends_on:
      rsyslog-service:
        condition: service_healthy
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "rabbitmq-service"
    environment:
      TZ: "UTC"
      RABBITMQ_DEFAULT_VHOST: "/"
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin
    healthcheck:
      test: ["CMD", "/usr/local/bin/rabbitmq_health_check.sh"]
      interval: 5s
      timeout: 5s
      retries: 10
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./data/rabbitmq/rabbitmq_health_check.sh:/usr/local/bin/rabbitmq_health_check.sh
    command: ["rabbitmq-server"]
    networks:
      - custom_net

  rsyslog-service:
    image: ampcon-rsyslog:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-RSYSLOG
    container_name: rsyslog-service
    privileged: true
    restart: unless-stopped
    ports:
      - "8514:514"
    working_dir: /usr/local/bin
    healthcheck:
      test: ["CMD", "/usr/local/bin/rsyslog_healthcheck.sh"]
      interval: 5s
      timeout: 5s
      retries: 5
    volumes:
      - ./data/rsyslog_conf/rsyslog_healthcheck.sh:/usr/local/bin/rsyslog_healthcheck.sh
      - ./data/rsyslog_conf/start_rsyslog.sh:/usr/local/bin/start_rsyslog.sh
      - ./data/rsyslog_conf/watch_config.sh:/usr/local/bin/watch_config.sh
      - ./data/rsyslog_conf/rsyslog.conf:/etc/rsyslog.conf
      - ./data/rsyslog_conf/rsyslog.d:/etc/rsyslog.d
      - ./data/logrotate_conf:/etc/logrotate.d
      - ./ampcon_data/rsyslog:/var/log/automation
    networks:
      - custom_net

  redis-service:
    image: ampcon-redis:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-REDIS
    container_name: redis-service
    privileged: true
    restart: always
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "redis-service"
    depends_on:
      rsyslog-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 10
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./data/redis/redis.conf:/usr/local/etc/redis/redis.conf
      - ./data/redis/redis/users.acl:/usr/local/etc/redis/users.acl
      - ./ampcon_data/redis:/data
    networks:
      - custom_net

  tftp-service:
    image: stoxygen/tftpd-hpa:${REACT_APP_VERSION:-latest}
    container_name: tftp-service
    privileged: true
    restart: unless-stopped
    ports:
      - "69:69/udp"
    healthcheck:
      test: ["CMD", "test", "-f", "/etc/default/tftpd-hpa"]
      interval: 5s
      timeout: 5s
      retries: 5
    volumes:
      - ./data/onie_install/system_ztp_start.sh:/data/system_ztp_start.sh
    networks:
      - custom_net

  ######################################################### server-ampcon end  #########################################################

  ######################################################### Prometheus monitor start  ##################################################
  prometheus:
    image: prometheus:${REACT_APP_VERSION:-latest}
    container_name: prometheus
    hostname: prometheus
    restart: always
    environment:
      TZ: "UTC"
    volumes:
      - ./ampcon_data/prometheus:/prometheus/data
      - ./data/monitor/settings:/etc/prometheus/settings # prometheus监控配置文件
      - ./data/monitor/rules:/etc/prometheus/rules # 告警规则配置文件
    command:
      - "--config.file=/etc/prometheus/settings/prometheus.yml"
      - "--web.enable-lifecycle" # 用于配置文件热更新，调用curl -X POST http://localhost:9090/-/reload更新配置文件
      - "--storage.tsdb.retention.time=30d" # 数据过期时间
      - "--query.lookback-delta=30s" # 查询追溯时长，最多追溯30s前的数据
    ports:
      - "9090:9090"
    networks:
      - custom_net

  alertmanager:
    image: alertmanager:${REACT_APP_VERSION:-latest}
    container_name: alertmanager
    hostname: alertmanager
    restart: always
    environment:
      TZ: "UTC"
    ports:
      - 9093:9093
    networks:
      - custom_net
    volumes:
      - ./data/monitor/settings/alertmanager.yml:/etc/alertmanager/alertmanager.yml
    command:
      - "--config.file=/etc/alertmanager/alertmanager.yml"

  gnmi-exporter:
    image: gnmi-exporter:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-GNMI-EXPORTER
    depends_on:
      nginx-service:
        condition: service_healthy
      openvpn-service:
        condition: service_healthy
    container_name: gnmi-exporter
    privileged: true
    environment:
      TZ: "UTC"
    hostname: gnmi-exporter
    restart: always
    volumes:
      - ./data/monitor/settings/gnmi.yaml:/app/gnmi.yaml:rw
    ports:
      - "5000:5000"
      - "9999:9999"
    command:
      [
        "sh",
        "-c",
        "ip route add ********/20 via $(getent hosts openvpn-service | awk '{ print $1 }') && ./gnmi_exporter",
      ]
    networks:
      - custom_net
  ######################################################### Prometheus monitor end  ####################################################

networks:
  custom_net:
    driver: bridge
#    ipam:
#      config:
#        - subnet: ********/24
