FROM ampcon-ssh:base

RUN mkdir -p /usr/share/automation/server
RUN mkdir -p /usr/share/automation/origin

COPY ../../backend/server-ampcon/server/collect /usr/share/automation/server/collect
COPY ../../backend/server-ampcon/server/db /usr/share/automation/server/db
COPY ../../data/onie_install /usr/share/automation/server/onie_install
COPY ../../backend/server-ampcon/server/util /usr/share/automation/server/util
COPY ../../data/settings/inner/automation.ini /usr/share/automation/server
COPY ../../backend/server-ampcon/server/cfg.py /usr/share/automation/server
COPY ../../backend/server-ampcon/server/constants.py /usr/share/automation/server

COPY ../../data/settings/outer/requirements_ssh.txt /usr/share/automation
COPY ../../data/settings/encrypt_code.py /usr/share/automation/encrypt_code.py

WORKDIR /usr/share/automation

RUN mv server origin
RUN python encrypt_code.py
RUN mv dest/server server
RUN rm -rf origin
RUN rm -rf dest

WORKDIR /usr/share/automation/server/collect
CMD echo "build ssh app image successfully."
CMD ["python", "ssh_service.py"]

