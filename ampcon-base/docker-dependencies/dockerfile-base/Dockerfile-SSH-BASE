FROM python:3.11.3-slim-bullseye
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list
RUN apt-get update && \
    apt-get install -y iproute2 && \
    apt-get install -y curl && \
    rm -rf /var/lib/apt/lists/*

RUN mkdir -p /usr/share/automation/server/pip_libs
COPY ../backend/server-ampcon/pip_libs /usr/share/automation/pip_libs
COPY ../data/settings/outer/requirements_ssh.txt /usr/share/automation/requirements_ssh.txt

WORKDIR /usr/share/automation
RUN pip install --no-index --find-links=pip_libs/ -r requirements_ssh.txt

RUN rm -rf /usr/share/automation/pip_libs
RUN rm -rf /usr/share/automation/requirements_ssh.txt
