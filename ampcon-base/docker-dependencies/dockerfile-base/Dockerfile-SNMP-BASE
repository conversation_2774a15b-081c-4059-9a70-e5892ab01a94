FROM python:3.11.3-slim-bullseye
R<PERSON> sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list
RUN apt-get update && \
    apt-get install -y net-tools && \
    rm -rf /var/lib/apt/lists/*

RUN mkdir -p /usr/share/automation/server

COPY ../../backend/server-ampcon/pip_libs /usr/share/automation/pip_libs
COPY ../../backend/server-ampcon/server/cfg.py /usr/share/automation/server/cfg.py
COPY ../../data/settings/inner/automation.ini /usr/share/automation/server/automation.ini
COPY ../../data/settings/outer/requirements_snmp.txt /usr/share/automation/requirements.txt

WORKDIR /usr/share/automation

RUN pip install --no-index --find-links=pip_libs/ -r requirements.txt

RUN rm -rf /usr/share/automation/pip_libs
RUN rm -rf /usr/share/automation/requirements.txt

WORKDIR /usr/share/automation/server