#!/bin/bash
set -e

source .env

MODULE_TYPE=$1
FULL_PACKAGE=$2

# docker compose pull not work

#pull_common_images(){
#  docker pull ${IMG_CENTER_PROXY_DOMAIN}/ampcon-nginx:base
#  docker pull ${IMG_CENTER_PROXY_DOMAIN}/ampcon-rsyslog:base
#  docker tag ${IMG_CENTER_PROXY_DOMAIN}/ampcon-nginx:base ampcon-nginx:base
#  docker tag ${IMG_CENTER_PROXY_DOMAIN}/ampcon-rsyslog:base ampcon-rsyslog:base
#}
#
#clear_common_images(){
#  docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/ampcon-nginx:base
#  docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/ampcon-rsyslog:base ampcon-rsyslog:base
#}

pull_ampcon_images(){
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/ampcon-nginx:base
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/ampcon-rsyslog:base

  docker pull ${IMG_CENTER_PROXY_DOMAIN}/rabbitmq:management-alpine
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/stoxygen/tftpd-hpa:v2.0.0
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/ampcon-db:base
  if [ "$MODULE_TYPE" == "ampcon-smb" ] ; then
    docker pull ${IMG_CENTER_PROXY_DOMAIN}/ampcon-main:base
  else
    docker pull ${IMG_CENTER_PROXY_DOMAIN}/ampcon-main-async:wireless
    docker tag ${IMG_CENTER_PROXY_DOMAIN}/ampcon-main-async:wireless ${IMG_CENTER_PROXY_DOMAIN}/ampcon-main:base
  fi
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/ampcon-openvpn:base
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/ampcon-ssh:base

  docker tag ${IMG_CENTER_PROXY_DOMAIN}/ampcon-nginx:base ampcon-nginx:base
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/ampcon-rsyslog:base ampcon-rsyslog:base

  docker tag ${IMG_CENTER_PROXY_DOMAIN}/rabbitmq:management-alpine rabbitmq:${REACT_APP_VERSION}
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/stoxygen/tftpd-hpa:v2.0.0 stoxygen/tftpd-hpa:${REACT_APP_VERSION}
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/ampcon-db:base ampcon-db:base
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/ampcon-main:base ampcon-main:base
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/ampcon-openvpn:base ampcon-openvpn:base
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/ampcon-ssh:base ampcon-ssh:base

  #Prometheus相关
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/prometheus:base
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/alertmanager:base
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/golang:1.21-alpine
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/alpine:3.19.1
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/prometheus:base prometheus:${REACT_APP_VERSION}
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/alertmanager:base alertmanager:${REACT_APP_VERSION}
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/golang:1.21-alpine golang:1.21-alpine
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/alpine:3.19.1 alpine:3.19.1

  #redis && python base for snmp
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/ampcon-redis:base
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/ampcon-snmp:base
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/snmp-exporter:base
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/ampcon-redis:base ampcon-redis:base
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/ampcon-snmp:base ampcon-snmp:base
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/snmp-exporter:base snmp-exporter:base
}

clear_ampcon_images(){
  if [ "$FULL_PACKAGE" == "clear" ] ; then
    echo ">>>>>>>>>>>>>>>>>>>  Clear all ampcon docker images start ...    <<<<<<<<<<<<<<<<<<<<<<<"
    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/ampcon-nginx:base
    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/ampcon-rsyslog:base ampcon-rsyslog:base

    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/rabbitmq:management-alpine
    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/stoxygen/tftpd-hpa:v2.0.0
    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/ampcon-db:base ampcon-db:base
    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/ampcon-main:base ampcon-main:base
    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/ampcon-openvpn:base ampcon-openvpn:base
    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/ampcon-ssh:base ampcon-ssh:base
    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/snmp-exporter:base snmp-exporter:base
    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/golang:1.21-alpine golang:1.21-alpine
    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/alpine:3.19.1 alpine:3.19.1

    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/ampcon-redis:base
    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/ampcon-snmp:base
    echo ">>>>>>>>>>>>>>>>>>>  Clear all ampcon docker images successfully <<<<<<<<<<<<<<<<<<<<<<"
  fi
}


pull_otn_images(){
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/jmcombs/sftp:latest
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/node:alpine
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/redis/redis-stack-server:latest
  docker pull ${IMG_CENTER_PROXY_DOMAIN}/mysql:8.0-debian

  docker tag ${IMG_CENTER_PROXY_DOMAIN}/jmcombs/sftp:latest jmcombs/sftp:${REACT_APP_VERSION}
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/node:alpine node:${REACT_APP_VERSION}
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/redis/redis-stack-server:latest redis/redis-stack-server:${REACT_APP_VERSION}
  docker tag ${IMG_CENTER_PROXY_DOMAIN}/mysql:8.0-debian mysql:${REACT_APP_VERSION}
}

clear_otn_images(){
  if [ "$FULL_PACKAGE" == "clear" ] ; then
    echo ">>>>>>>>>>>>>>>>>>>  Clear all otn docker images start ...    <<<<<<<<<<<<<<<<<<<<<<<"
    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/jmcombs/sftp:latest
    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/node:alpine
    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/redis/redis-stack-server:latest
    docker rmi -f ${IMG_CENTER_PROXY_DOMAIN}/mysql:8.0-debian
    echo ">>>>>>>>>>>>>>>>>>>  Clear all otn docker images successfully <<<<<<<<<<<<<<<<<<<<<<"
  fi
}

check_license_file(){
  license_file_path="./backend/server-ampcon/server/license_check"
  case "$MODULE_TYPE" in 
    ampcon-dc) 
      keep_files=("ampcon_dc_license_check.py" "license_check.py") 
      ;;
    ampcon-campus)
      keep_files=("ampcon_campus_license_check.py" "license_check.py") 
      ;;
    ampcon-t)
      keep_files=("ampcon_t_license_check.py" "license_check.py") 
      ;;
    ampcon-super)
      keep_files=("ampcon_t_license_check.py" "license_check.py")
      ;;
    ampcon-smb)
      keep_files=("ampcon_campus_license_check.py" "license_check.py") 
      ;;
    *)
      echo "Invalid parameter: $MODULE_TYPE" 
      exit 1 
      ;;
    esac
    for file in "$license_file_path"/*.py; do
        if [[ ! " ${keep_files[*]} " =~ " $(basename "$file") " ]]; then
            rm "$file"
        fi
    done
}

build_image_module_type() {
  case "${MODULE_TYPE}" in
    ampcon-super)
        pull_ampcon_images
        pull_otn_images
        cp ./docker-dependencies/docker-compose/backend/docker-compose-ampcon-super.yml ./docker-compose.yml
        docker compose build
        clear_ampcon_images
        clear_otn_images
        ;;
    ampcon-t)
        pull_ampcon_images
        pull_otn_images
        cp ./docker-dependencies/docker-compose/backend/docker-compose-ampcon-t.yml ./docker-compose.yml
        docker compose build
        clear_ampcon_images
        clear_otn_images
        ;;
    ampcon-dc)
        pull_ampcon_images
        cp ./docker-dependencies/docker-compose/backend/docker-compose-ampcon-dc.yml ./docker-compose.yml
        docker compose build
        clear_ampcon_images
        ;;
    ampcon-campus)
        pull_ampcon_images
        cp ./docker-dependencies/docker-compose/backend/docker-compose-ampcon-campus.yml ./docker-compose.yml
        docker compose build
        clear_ampcon_images
        ;;
    ampcon-smb)
        pull_ampcon_images
        cp ./docker-dependencies/docker-compose/backend/docker-compose-ampcon-smb.yml ./docker-compose.yml
        docker compose build
        clear_ampcon_images
        ;;
    *)
        echo "error module type ${MODULE_TYPE},  please check ..."
        exit 1
        ;;
  esac
}

#sudo ./docker-dependencies-scripts/achive_images.sh

check_docker_images_file(){
  output_path="./docker_images"
  if [ ! -d "${output_path}" ]; then
    mkdir -p "${output_path}"
    echo ">>>>>>>>>>>>>>>>>>>  Create new docker_images path successfully <<<<<<<<<<<<<<<<<<<<"
  else
    rm -rf "${output_path}"/*
    echo "$output_path"
    echo ">>>>>>>>>>>>>>>>>>>  Clear all docker_images file  successfully <<<<<<<<<<<<<<<<<<<<<"

  fi
}

export_images(){
  rm -f ./docker-compose.yml

  images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "${REACT_APP_VERSION}")
  for image in $images; do
      echo "Exporting image: $image"
      docker save "$image" | pigz --fast -c > "$output_path/$(echo "$image" | tr '/' '_').tar.gz"
  done
  if [ "$MODULE_TYPE" == "ampcon-smb" ] || [ "$MODULE_TYPE" == "ampcon-campus" ] && [ "$FULL_PACKAGE" == "full" ]; then
    echo ">>>>>>>>>>>>>>>>>>>  Start exporting wireless images <<<<<<<<<<<<<<<<<<<<<"
    wireless_images_label=(
        "harbor.ampcon.com/openwifi/ampcon-smb-owgw-dev:latest"
        "harbor.ampcon.com/openwifi/ampcon-smb-owsec-dev:latest"
        "harbor.ampcon.com/openwifi/ampcon-smb-owprov-dev:latest"
        "harbor.ampcon.com/openwifi/ampcon-smb-owrrm-dev:latest"
        "harbor.ampcon.com/openwifi/mysql:latest"
        "harbor.ampcon.com/openwifi/ampcon-smb-owanalytics-dev:latest"
        "harbor.ampcon.com/openwifi/kafka:3.7-debian-12"
        "harbor.ampcon.com/openwifi/postgres:15.0"
        "harbor.ampcon.com/openwifi/traefik:latest"
    )
    for item in "${wireless_images_label[@]}"; do
        docker pull "$item"
        docker save "$item" | pigz --fast -c > "$output_path/$(echo "$item" | tr '/' '_').tar.gz"
    done
    echo ">>>>>>>>>>>>>>>>>>>  Exporting wireless images successfully <<<<<<<<<<<<<<<<<<<<<"
fi

  echo ">>>>>>>>>>>>>>>>>>>  All images *.tar file export successfully       <<<<<<<<<<<<<<<<"
  echo ">>>>>>>>>>>>>>>>>>>  Cleaning up release images     <<<<<<<<<<<<<<<<"
  for image in $images; do
      echo "Delete image: $image"
      docker rmi -f "$image"
  done
  echo ">>>>>>>>>>>>>>>>>>>  Cleaning up release images successfully    <<<<<<<<<<<<<<<<"
}

main(){
  check_license_file
  build_image_module_type
  check_docker_images_file
  export_images
}

main



