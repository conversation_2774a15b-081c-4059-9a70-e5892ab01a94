#!/bin/bash

set -e

PRO_PATH=/usr/share/automation/server
RUNNING_VERSION_PATH=/usr/share/automation/server/.env

pre_version=$(grep -oP '^REACT_APP_PRE_VERSION=\K.*' "$RUNNING_VERSION_PATH")
current_version=$(grep -oP '^REACT_APP_VERSION=\K.*' "$RUNNING_VERSION_PATH")

check_version(){
  if [ -z "$pre_version" ]; then
    echo ">>>>>>>>>>>>>>>>>>>   AmpCon project pre version is None, not allowed restore     <<<<<<<<<<<<<<<<<<<<<<"
    exit;
  fi
  if [ ! -e "${PRO_PATH}_${pre_version}_bak" ]; then
    echo ">>>>>>>>>>>>>>>>>>>   AmpCon project need rollback file not found !!!             <<<<<<<<<<<<<<<<<<<<<<"
    exit;
  fi
}

prepare_env_start_server(){
  cd ${PRO_PATH} && ./stop.sh
  echo ">>>>>>>>>>>>>>>>>>>   AmpCon project backup project data start          <<<<<<<<<<<<<<<<<<<<<<"
  mv ${PRO_PATH} "${PRO_PATH}_${current_version}_bak"
  echo ">>>>>>>>>>>>>>>>>>>   AmpCon project backup project data successfully   <<<<<<<<<<<<<<<<<<<<<<"

  echo ">>>>>>>>>>>>>>>>>>>   AmpCon project restore project data start         <<<<<<<<<<<<<<<<<<<<<<"
  mv "${PRO_PATH}_${pre_version}_bak" ${PRO_PATH}
  chown -R 999:999 ${PRO_PATH}/*_data/mysql
  if [ -d "$PRO_PATH/ampcon_data/prometheus" ]; then
    chown -R 65534:65534 ${PRO_PATH}/ampcon_data/prometheus 
  fi
  cd ${PRO_PATH} && ./start.sh
  echo ">>>>>>>>>>>>>>>>>>>   AmpCon project restore project data successfully  <<<<<<<<<<<<<<<<<<<<<<"
}

rollback_server(){
  echo ">>>>>>>>>>>>>>>>>>>   AmpCon project rollback  data start          <<<<<<<<<<<<<<<<<<<<<<"
  cd ${PRO_PATH} && ./stop.sh
  mv "${PRO_PATH}_${current_version}_bak" ${PRO_PATH}
  chown -R 999:999 ${PRO_PATH}/*_data/mysql
  if [ -d "$PRO_PATH/ampcon_data/prometheus" ]; then
    chown -R 65534:65534 ${PRO_PATH}/ampcon_data/prometheus 
  fi
  echo ">>>>>>>>>>>>>>>>>>>   AmpCon project rollback  data successfully   <<<<<<<<<<<<<<<<<<<<<<"
  cd ${PRO_PATH} && ./start.sh
}


clean_docker_images(){
  echo ">>>>>>>>>>>>>>>>>>>    Clean old docker images Started   <<<<<<<<<<<<<<<<<<<<<<"
  sudo docker images --format "{{.Repository}}:{{.Tag}}" | while read -r image; do
    tag=$(echo "${image}" | awk -F ':' '{print $2}')
    if [[ -n "$pre_version" && "$tag" != "$pre_version" ]]; then
      sudo docker rmi -f $image
    fi
  done
  echo ">>>>>>>>>>>>>>>>>>>   Clean old docker images successfully               <<<<<<<<<<<<<<<<<<<<<<"
}



check_server_health(){
  sleep 6
  nginx_container_id=$(sudo docker ps -qf name=nginx-service)
  if [ -z "$nginx_container_id" ]; then
      echo ">>>>>>>>>>>>>>>>>>> Sorry! container nginx-service is not running, current upgrade failed...check error   <<<<<<<<<<<<<<<<<<<<<<"
  else
    health_status=$(sudo docker inspect --format '{{.State.Health.Status}}' nginx-service)
    if [ "$health_status" = "healthy" ]; then
        sed -i "s/^REACT_APP_VERSION=.*/REACT_APP_VERSION=${pre_version}/g" ${RUNNING_VERSION_PATH}
        sed -i "s/^REACT_APP_PRE_VERSION=.*/REACT_APP_PRE_VERSION=/g" ${RUNNING_VERSION_PATH}
        echo ">>>>>>>>>>>>>>>>>>> Congratulations! container nginx-service is healthy, current upgrade successfully!!!  <<<<<<<<<<<<<<<<<<<<<<"
    else
        rollback_server
        echo ">>>>>>>>>>>>>>>>>>> Sorry! container nginx-service is unhealthy, current upgrade failed...check error     <<<<<<<<<<<<<<<<<<<<<<"
    fi
  fi
}

main(){
  check_version
  prepare_env_start_server
  check_server_health
  clean_docker_images
}

main




