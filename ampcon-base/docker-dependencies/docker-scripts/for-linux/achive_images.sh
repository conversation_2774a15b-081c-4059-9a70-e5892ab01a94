#!/bin/bash
set -e

source .env

echo ">>>>>>>>>>>>>>>>>>>  Archvie docker images to harbor start     <<<<<<<<<<<<<<<<"

curl -k -u 'admin:pica8' -X POST -H "Content-Type: application/json" -d '{"project_name":"'"${VERSION}"'","public": true}' "https://harbor.ampcon.com/api/v2.0/projects"

version_registry=$(echo "$IMG_CENTER_PROXY_DOMAIN" | cut -d'/' -f1)"/${VERSION}"

docker tag jmcombs/sftp:${VERSION} ${version_registry}/jmcombs/sftp:${VERSION}
docker tag node:${VERSION} ${version_registry}/node:${VERSION}
docker tag redis/redis-stack-server:${VERSION} ${version_registry}/redis/redis-stack-server:${VERSION}
docker tag mysql:${VERSION} ${version_registry}/mysql:${VERSION}
docker tag rabbitmq:${VERSION} ${version_registry}/rabbitmq:${VERSION}
docker tag alpine:${VERSION} ${version_registry}/alpine:${VERSION}
docker tag nginx:${VERSION} ${version_registry}/nginx:${VERSION}
docker tag portainer/portainer:${VERSION} ${version_registry}/portainer/portainer:${VERSION}

docker tag ampcon-db:${VERSION} ${version_registry}/ampcon-db:${VERSION}
docker tag ampcon-main:${VERSION} ${version_registry}/ampcon-main:${VERSION}
docker tag ampcon-nginx:${VERSION} ${version_registry}/ampcon-nginx:${VERSION}
docker tag ampcon-openvpn:${VERSION} ${version_registry}/ampcon-openvpn:${VERSION}
docker tag ampcon-rsyslog:${VERSION} ${version_registry}/ampcon-rsyslog:${VERSION}
docker tag ampcon-ssh:${VERSION} ${version_registry}/ampcon-ssh:${VERSION}
docker tag ampcon-t:${VERSION} ${version_registry}/ampcon-t:${VERSION}


images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "${version_registry}")

for image in $images; do
    docker push $image
    docker rmi -f $image
done

echo ">>>>>>>>>>>>>>>>>>>  Archvie docker images to harbor successfully     <<<<<<<<<<<<<<<<"