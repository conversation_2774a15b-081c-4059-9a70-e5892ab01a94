#!/bin/bash
set -e
source .env

#if [ "$(docker ps -q)" ]; then
#    sudo docker rm -f $(sudo docker ps -aq)
#fi
#yes | sudo docker system prune -a

# 老的镜像管理太过于简陋，启用harbor来管理docker镜像仓
#sudo docker run -d -p 5000:5000 --restart=always --name registry registry:latest
#sudo docker run --name registry-browser -p 8080:8080 --restart=always --link registry -e DOCKER_REGISTRY_URL=http://registry:5000/v2 -e SECRET_KEY_BASE=$(openssl rand -hex 64) -d klausmeyer/docker-registry-browser

# openssl genrsa -out ca.key 4096
# openssl req -x509 -new -nodes -sha512 -days 3650 -subj "/C=CN/ST=Beijing/L=Beijing/O=example/OU=Personal/CN=harbor.ampcon.com" -key ca.key -out ca.crt
# openssl genrsa -out harbor.ampcon.com.key 4096
# openssl req -sha512 -new -subj "/C=CN/ST=Beijing/L=Beijing/O=example/OU=Personal/CN=harbor.ampcon.com" -key harbor.ampcon.com.key -out harbor.ampcon.com.csr
# cat > v3.ext <<-EOF
#authorityKeyIdentifier=keyid,issuer
#basicConstraints=CA:FALSE
#keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
#extendedKeyUsage = serverAuth
#subjectAltName = @alt_names
#
#[alt_names]
#DNS.1=harbor.ampcon.com
#EOF
# openssl x509 -req -sha512 -days 3650  -extfile v3.ext -CA ca.crt -CAkey ca.key -CAcreateserial -in harbor.ampcon.com.csr -out harbor.ampcon.com.crt
# openssl x509 -inform PEM -in harbor.ampcon.com.crt -out harbor.ampcon.com.cert
#sudo mkdir -p /etc/docker/certs.d/harbor.ampcon.com
#sudo cp harbor.ampcon.com.cert harbor.ampcon.com.crt ca.crt /etc/docker/certs.d/harbor.ampcon.com/

echo ">>>>>>>>>>>>>>>>>>>  Build base docker images started...    <<<<<<<<<<<<<<<<<<<<<"

sudo docker pull jmcombs/sftp:latest
sudo docker pull node:alpine
sudo docker pull ubuntu:24.04
sudo docker pull redis/redis-stack-server:latest
sudo docker pull mysql:8.0-debian
sudo docker pull rabbitmq:management-alpine
sudo docker pull alpine:3.19.1
sudo docker pull python:3.11.3-slim-bullseye
sudo docker pull nginx:stable-alpine
sudo docker pull portainer/portainer:latest

sudo docker build -t ampcon-db:base -f ./dockerfile-base/Dockerfile-DB-BASE .
sudo docker build -t ampcon-main:base -f ./dockerfile-base/Dockerfile-MAIN-BASE .
sudo docker build -t ampcon-nginx:base -f ./dockerfile-base/Dockerfile-NGINX-BASE .
sudo docker build -t ampcon-openvpn:base -f ./dockerfile-base/Dockerfile-OPENVPN-BASE .
sudo docker build -t ampcon-rsyslog:base -f ./dockerfile-base/Dockerfile-RSYSLOG-BASE .
sudo docker build -t ampcon-ssh:base -f ./dockerfile-base/Dockerfile-SSH-BASE .
sudo docker build -t ampcon-snmp:base -f ./dockerfile-base/Dockerfile-SNMP-BASE .

# 出现WARNING: current commit information was not captured by the build: failed to read current commit information with git rev-parse --is-inside-work-tree
sudo docker tag jmcombs/sftp:latest ${IMG_CENTER_PROXY_DOMAIN}/jmcombs/sftp:latest
sudo docker tag node:alpine ${IMG_CENTER_PROXY_DOMAIN}/node:alpine
sudo docker tag redis/redis-stack-server:latest ${IMG_CENTER_PROXY_DOMAIN}/redis/redis-stack-server:latest
sudo docker tag mysql:8.0-debian ${IMG_CENTER_PROXY_DOMAIN}/mysql:8.0-debian
sudo docker tag rabbitmq:management-alpine ${IMG_CENTER_PROXY_DOMAIN}/rabbitmq:management-alpine
sudo docker tag alpine:3.19.1 ${IMG_CENTER_PROXY_DOMAIN}/alpine:3.19.1
sudo docker tag nginx:stable-alpine ${IMG_CENTER_PROXY_DOMAIN}/nginx:stable-alpine
sudo docker tag portainer/portainer:latest ${IMG_CENTER_PROXY_DOMAIN}/portainer/portainer:latest
sudo docker tag ampcon-db:base ${IMG_CENTER_PROXY_DOMAIN}/ampcon-db:base
sudo docker tag ampcon-main:base ${IMG_CENTER_PROXY_DOMAIN}/ampcon-main:base
sudo docker tag ampcon-nginx:base ${IMG_CENTER_PROXY_DOMAIN}/ampcon-nginx:base
sudo docker tag ampcon-openvpn:base ${IMG_CENTER_PROXY_DOMAIN}/ampcon-openvpn:base
sudo docker tag ampcon-rsyslog:base ${IMG_CENTER_PROXY_DOMAIN}/ampcon-rsyslog:base
sudo docker tag ampcon-ssh:base ${IMG_CENTER_PROXY_DOMAIN}/ampcon-ssh:base
sudo docker tag ampcon-snmp:base ${IMG_CENTER_PROXY_DOMAIN}/ampcon-snmp:base

sudo docker push ${IMG_CENTER_PROXY_DOMAIN}/jmcombs/sftp:latest
sudo docker push ${IMG_CENTER_PROXY_DOMAIN}/node:alpine
sudo docker push ${IMG_CENTER_PROXY_DOMAIN}/redis/redis-stack-server:latest
sudo docker push ${IMG_CENTER_PROXY_DOMAIN}/mysql:8.0-debian
sudo docker push ${IMG_CENTER_PROXY_DOMAIN}/rabbitmq:management-alpine
sudo docker push ${IMG_CENTER_PROXY_DOMAIN}/alpine:3.19.1
sudo docker push ${IMG_CENTER_PROXY_DOMAIN}/nginx:stable-alpine
sudo docker push ${IMG_CENTER_PROXY_DOMAIN}/portainer/portainer:latest
sudo docker push ${IMG_CENTER_PROXY_DOMAIN}/ampcon-db:base
sudo docker push ${IMG_CENTER_PROXY_DOMAIN}/ampcon-main:base
sudo docker push ${IMG_CENTER_PROXY_DOMAIN}/ampcon-nginx:base
sudo docker push ${IMG_CENTER_PROXY_DOMAIN}/ampcon-openvpn:base
sudo docker push ${IMG_CENTER_PROXY_DOMAIN}/ampcon-rsyslog:base
sudo docker push ${IMG_CENTER_PROXY_DOMAIN}/ampcon-ssh:base
sudo docker push ${IMG_CENTER_PROXY_DOMAIN}/ampcon-snmp:base


echo ">>>>>>>>>>>>>>>>>>>  Build base docker images  completed      <<<<<<<<<<<<<<<<<<<<<"


