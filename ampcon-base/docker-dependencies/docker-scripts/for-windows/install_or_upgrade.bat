@echo off
setlocal enabledelayedexpansion

title AmpCon-Project

set currentDrive=%CD:~0,1%
set "currentDir=%~dp0"
set "PRO_PATH=C:\usr\share\automation\server"
set "INSTALL_WITHOUT_START=false"
set "RUNNING_VERSION_PATH=C:\usr\share\automation\server\.env"
set "CURRENT_VERSION_PATH=.env"
set "current_version="
set "pre_version="
set "ready_version="

if "%1"=="--without-start" (
    set "INSTALL_WITHOUT_START=true"
)

rem Initialize counter and array
set count=0
set ips=

rem Find all matching IPv4 addresses and store them in an array
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr "IPv4"') do (
    for /f "tokens=1 delims= " %%j in ("%%i") do (
        set /a count+=1
        set ips[!count!]=%%j
    )
)

rem Exit if no IPv4 addresses are found
if %count%==0 (
    echo No IPv4 addresses found.
    goto :EOF
)

rem Display all found IPv4 addresses
echo Found the following IPv4 addresses:
for /l %%k in (1,1,%count%) do (
    echo %%k: !ips[%%k]!
)

rem Prompt user to select an IPv4 address
set /p choice=Enter the number of the desired IP address:

rem Check if the user input is valid
if %choice% lss 1 if %choice% gtr %count% (
    echo Invalid choice.
    goto :EOF
)

rem Set and save the selected IPv4 address
set LOCAL_ADDRESS=!ips[%choice%]!
setx LOCAL_ADDRESS %LOCAL_ADDRESS%
echo You selected: %LOCAL_ADDRESS%

"%currentDir%nettool.exe" %LOCAL_ADDRESS%

:main
if not exist "%PRO_PATH%" (
  mkdir "%PRO_PATH%"
  echo AmpCon project %PRO_PATH% dir not exists, ready to install
  call :prepare_env_for_install
  if "!INSTALL_WITHOUT_START!"=="false" (
    call "%PRO_PATH%\start.exe"
    call :do_install_post_action
  )

) else (
  echo AmpCon project %PRO_PATH% dir already exists, ready to upgrade
  call :prepare_env_for_upgrade
  if "!INSTALL_WITHOUT_START!"=="false" (
    call "%PRO_PATH%\start.exe"
    call :do_upgrade_post_action
  )
)
pause
exit /b


:prepare_env_for_install
for /f %%i in ('docker ps -q') do (
  docker rm -f %%i
)
docker system prune -a -f
xcopy /e /i /h /y * "%PRO_PATH%"
call powershell -Command "Move-Item -Path 'mac_address' -Destination '%PRO_PATH%\data\settings\inner\mac_address' -Force"
del /q "%PRO_PATH%\mac_address"
del /q "uuid"
del /q "%PRO_PATH%\uuid"
attrib +h start.exe
attrib +h stop.exe
attrib +h import_images.exe
if /i "%currentDrive%"=="C" (
  cd "%PRO_PATH%"
) else (
  cd /d "%PRO_PATH%"
)
del /q install_or_upgrade.exe
del /q restore.exe
mkdir "%PRO_PATH%\ampcon_data\mysql"
mkdir "%PRO_PATH%\otn_data\mysql"
echo AmpCon project install successfully
exit /b


:prepare_env_for_upgrade
call :old_env_support
call :compare_version
call :clean_docker_images
call :prepare_env_backup_start_server
exit /b

:old_env_support
set "ENV_FILE=C:\usr\share\automation\server\.env"
findstr /r "^REACT_APP_" "%ENV_FILE%" >nul
if %errorlevel% equ 0 (
  echo "current file is new"
) else (
  set "COUNT=0"
  > "temp.env" (
    for /f "delims=" %%A in ('type "%ENV_FILE%"') do (
        set /a COUNT+=1
        if !COUNT! LEQ 3 (
            echo REACT_APP_%%A
        ) else (
            echo %%A
        )
    )
  )
  call powershell -Command "Move-Item -Path 'temp.env' -Destination '%ENV_FILE%' -Force"
  echo "current env file update success"
)
exit /b

:compare_version
if exist "%RUNNING_VERSION_PATH%" (
    for /f "tokens=2 delims==" %%i in ('findstr /r "^REACT_APP_PRE_VERSION=" "%RUNNING_VERSION_PATH%"') do set pre_version=%%i
    for /f "tokens=2 delims==" %%i in ('findstr /r "^REACT_APP_VERSION=" "%RUNNING_VERSION_PATH%"') do set current_version=%%i
)
if exist "%CURRENT_VERSION_PATH%" (
    for /f "tokens=2 delims==" %%i in ('findstr /r "^REACT_APP_VERSION=" "%CURRENT_VERSION_PATH%"') do set ready_version=%%i
)
if "!pre_version!"=="!ready_version!" (
  echo AmpCon project not allowed downgrade !!!
  pause
) else if "!current_version!"=="!ready_version!" (
  echo AmpCon project version is same !!!
  pause
)
exit /b


:clean_docker_images
for /f %%i in ('docker ps -aq') do (
  docker rm -f %%i
)
for /f "tokens=*" %%i in ('docker images --format "{{.Repository}}:{{.Tag}}"') do (
  for /f "tokens=2 delims=:" %%j in ("%%i") do set tag=%%j
  if defined pre_version if "!tag!" neq "!pre_version!" (
    docker rmi -f %%i
  ) else if defined current_version if "!tag!" neq "!current_version!" (
    docker rmi -f %%i
  )
)
exit /b


:prepare_env_backup_start_server
echo AmpCon project backup project data start
xcopy /e /i /h /y "%PRO_PATH%" "%PRO_PATH%_%current_version%_bak"
del /q "%PRO_PATH%\docker_images\*"
echo AmpCon project backup project data successfully
xcopy /e /i /h /y * "%PRO_PATH%"
call powershell -Command "Move-Item -Path 'mac_address' -Destination '%PRO_PATH%\data\settings\inner\mac_address' -Force"
del /q "%PRO_PATH%\mac_address"
del /q "uuid"
del /q "%PRO_PATH%\uuid"
if /i "%currentDrive%"=="C" (
  cd "%PRO_PATH%"
) else (
  cd /d "%PRO_PATH%"
)
exit /b


:do_upgrade_post_action
echo AmpCon project upgrade post action start
call powershell -Command "(gc %RUNNING_VERSION_PATH%) -replace '^REACT_APP_VERSION=.*', 'REACT_APP_VERSION=%ready_version%' | Out-File -encoding ASCII %RUNNING_VERSION_PATH%"
call powershell -Command "(gc %RUNNING_VERSION_PATH%) -replace '^REACT_APP_PRE_VERSION=.*', 'REACT_APP_PRE_VERSION=%current_version%' | Out-File -encoding ASCII %RUNNING_VERSION_PATH%"
for /f "tokens=*" %%i in ('docker ps -qf "name=flask-main-service"') do (
    docker exec -i %%i bash -c "cd /usr/share/automation && python sub_upgrade.py"
)
echo AmpCon project upgrade post action successfully
exit /b

:do_install_post_action
echo AmpCon project install post action start
for /f "tokens=*" %%i in ('docker ps -qf "name=flask-main-service"') do (
    docker exec -i %%i bash -c "cd /usr/share/automation && python sub_upgrade.py --install"
)
echo AmpCon project install post action successfully
exit /b