@echo off
setlocal enabledelayedexpansion

title AmpCon-Project

:main
call import_images.exe
call :configure_env
docker compose down
docker compose up -d
call :check_server_health
exit /b


:configure_env
set PRO_PATH=.\data
set default_amp_cfg=%PRO_PATH%\settings\inner\automation.ini
set agent_cnf=%PRO_PATH%\agent\auto-deploy.conf
set agent_cnf_dir=%PRO_PATH%\config_gen
set vpn_client_cnf=%PRO_PATH%\vpn\client.conf
set onie_install_sh=%PRO_PATH%\onie_install\start.sh
set system_ztp_sh=%PRO_PATH%/onie_install/system_ztp_start.sh

call powershell -Command "(gc %default_amp_cfg%) -replace '^global_ip : .*', 'global_ip = %LOCAL_ADDRESS%' | Out-File -encoding ASCII %default_amp_cfg%"
call powershell -Command "(gc %agent_cnf%) -replace '^server_vpn_host = .*', 'server_vpn_host = %LOCAL_ADDRESS%' | Out-File -encoding ASCII %agent_cnf%"
for /r "%agent_cnf_dir%" %%f in (*.conf) do (
  set "file=%%f"
  findstr /r "^server_vpn_host" "!file!" >nul
  if %errorlevel% equ 0 (
    REM Replace the line with the new address
    powershell -command "(gc '!file!') -replace '^server_vpn_host = .*', 'server_vpn_host = %LOCAL_ADDRESS%' | Out-File -encoding ASCII '!file!'"
  )
)
call powershell -Command "(gc %onie_install_sh%) -replace '^server_host=.*', 'server_host=%LOCAL_ADDRESS%' | Out-File -encoding ASCII %onie_install_sh%"
call powershell -Command "(gc %system_ztp_sh%) -replace '^server_host=.*', 'server_host=%LOCAL_ADDRESS%' | Out-File -encoding ASCII %system_ztp_sh%"
call powershell -Command "(gc %vpn_client_cnf%) -replace '^remote .*', 'remote %LOCAL_ADDRESS% 80' | Out-File -encoding ASCII %vpn_client_cnf%"

exit /b


:check_server_health
set end=%time:~0,2%
set /a end+=5

:loop
for /f "tokens=*" %%i in ('docker ps -qf "name=nginx-service"') do set nginx_container_id=%%i

if "%nginx_container_id%"=="" (
    echo Sorry! container nginx-service is not running, please check error
    exit /b
) else (
    for /f "tokens=*" %%i in ('docker inspect --format "{{.State.Health.Status}}" nginx-service') do set health_status=%%i
    if "%health_status%"=="healthy" (
        "%currentDir%nettool.exe" %LOCAL_ADDRESS%
        docker cp -q uuid flask-main-service:/usr/share
        del uuid
        del mac_address
        echo Congratulations! container nginx-service is healthy
        exit /b
    )
)

set current_time=%time:~0,2%
if %current_time% lss %end% (
    timeout /t 3 /nobreak >nul
    goto loop
)

echo Sorry! container nginx-service did not become healthy within 5 minutes...
exit /b

