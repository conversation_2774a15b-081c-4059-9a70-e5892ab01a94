#!/usr/bin/env sh

# script to help run the binary with specific jvm and jvm options
# Defaults to openj9. Switch by setting JVM_IMPL environment variable

JAVA_BIN=$1
JAR=$2
shift 2

COMMON_PARAMETERS=" \
-XX:+CompactStrings \
-DCONSOLE_LOG_LEVEL=${CONSOLE_LOG_LEVEL:-DEBUG} \
-DFILE_LOG_LEVEL=${FILE_LOG_LEVEL:-INFO} \
-Dlog4j.configuration=file:/owrrm-data/log4j.properties \
"

JVM_IMPL="${JVM_IMPL:-openj9}"
EXTRA_JVM_FLAGS="${EXTRA_JVM_FLAGS:-}"

if [ "$JVM_IMPL" = "hotspot" ]; then
# for hotspot
PARAMETERS="\
$COMMON_PARAMETERS \
-XX:+UseG1GC \
-XX:+UseStringDeduplication \
$EXTRA_JVM_FLAGS \
"
elif [ "$JVM_IMPL" = "openj9" ]; then
# for openj9
PARAMETERS=" \
$COMMON_PARAMETERS \
$EXTRA_JVM_FLAGS \
"

#-XX:+IdleTuningGcOnIdle \
#-Xtune:virtualized

else
echo "Invalid JVM_IMPL option"
exit 1
fi

"$JAVA_BIN" \
	$PARAMETERS \
	-jar "$JAR" \
	$@
