log4j.rootLogger=ERROR, stdout, file
log4j.logger.com.facebook.openwifi=${CONSOLE_LOG_LEVEL}
log4j.logger.org.apache.kafka=INFO
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{yyyy-MM-dd'T'HH:mm:ss.SSS} %-5p [%c{1}:%L] - %m%n
log4j.appender.file=org.apache.log4j.RollingFileAppender
log4j.appender.file.Threshold=${FILE_LOG_LEVEL}
log4j.appender.file.File=./openwifi-rrm.log
log4j.appender.file.MaxFileSize=50MB
log4j.appender.file.MaxBackupIndex=10
log4j.appender.file.Append=true
log4j.appender.file.Encoding=UTF-8
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%d{yyyy-MM-dd'T'HH:mm:ss.SSS} %-5p [%c{1}:%L] - %m%n
