server {
    listen       80;
    listen  [::]:80;

    # Disable emitting nginx version
    server_tokens off;

    return 301 https://$host$request_uri;
}

server {
    listen       443 ssl;
    listen       [::]:443 ssl;

    # Disable emitting nginx version
    server_tokens off;

    ssl_certificate     /etc/nginx/restapi-cert.pem;
    ssl_certificate_key /etc/nginx/restapi-key.pem;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
    }

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
