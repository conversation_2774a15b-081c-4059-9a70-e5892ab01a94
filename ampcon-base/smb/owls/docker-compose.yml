volumes:
  kafka_data:
    driver: local

networks:
  owls:

services:
  owfms:
    image: "tip-tip-wlan-cloud-ucentral.jfrog.io/owfms:${OWFMS_TAG}"
    networks:
      owls:
        aliases:
          - ${INTERNAL_OWFMS_HOSTNAME}
    env_file:
      - owfms.env
    depends_on:
      init-kafka:
        condition: service_completed_successfully
    command: ["./wait-for-postgres.sh", "postgresql", "/openwifi/owfms"]
    restart: unless-stopped
    volumes:
      - "./owfms_data:${OWFMS_ROOT}"
      - "../certs:/${OWFMS_ROOT}/certs"
    ports:
      - "16004:16004"
      - "16104:16104"
  owsec:
    image: "tip-tip-wlan-cloud-ucentral.jfrog.io/owsec:${OWSEC_TAG}"
    networks:
      owls:
        aliases:
          - ${INTERNAL_OWSEC_HOSTNAME}
    env_file:
      - owsec.env
    depends_on:
      init-kafka:
        condition: service_completed_successfully
    restart: unless-stopped
    volumes:
      - "./owsec_data:${OWSEC_ROOT}"
      - "../certs:/${OWSEC_ROOT}/certs"
    ports:
      - "16001:16001"
      - "16101:16101"

  owls:
    image: "tip-tip-wlan-cloud-ucentral.jfrog.io/owls:${OWLS_TAG}"
    networks:
      owls:
        aliases:
          - ${INTERNAL_OWLS_HOSTNAME}
    env_file:
      - owls.env
    depends_on:
      owsec:
        condition: service_started
      init-kafka:
        condition: service_completed_successfully
    restart: unless-stopped
    volumes:
      - "./owls_data:${OWLS_ROOT}"
      - "../certs:/${OWLS_ROOT}/certs"
    ports:
      - "16007:16007"
      - "16107:16107"

  owls-ui:
    image: "tip-tip-wlan-cloud-ucentral.jfrog.io/owls-ui:${OWLSUI_TAG}"
    networks:
      owls:
    env_file:
      - owls-ui.env
    depends_on:
      - owsec
      - owls
    restart: unless-stopped
    volumes:
      - "./owls-ui/default.conf:/etc/nginx/conf.d/default.conf"
      - "../certs/restapi-cert.pem:/etc/nginx/restapi-cert.pem"
      - "../certs/restapi-key.pem:/etc/nginx/restapi-key.pem"
    ports:
      - "80:80"
      - "443:443"

  kafka:
    image: "docker.io/bitnami/kafka:${KAFKA_TAG}"
    networks:
      owls:
    env_file:
      - kafka.env
    restart: unless-stopped
    volumes:
      - kafka_data:/bitnami/kafka

  init-kafka:
    image: "docker.io/bitnami/kafka:${KAFKA_TAG}"
    networks:
      owls:
    depends_on:
      - kafka
    env_file:
      - kafka.env
    entrypoint:
      - /bin/sh
      - -c
      - |
        echo "Sleeping to allow kafka to start up..."
        sleep 10
        echo "Creating all required Kafka topics..."
        for topic in $$TOPICS; do
          /opt/bitnami/kafka/bin/kafka-topics.sh \
          --create --if-not-exists --topic $$topic --replication-factor 1 \
          --partitions 1 --bootstrap-server kafka:9092
        done && echo "Successfully created Kafka topics, exiting." && exit 0

  postgresql:
    image: "postgres:${POSTGRESQL_TAG}"
    networks:
      owls:
    command:
      - "postgres"
      - "-c"
      - "max_connections=400"
      - "-c"
      - "shared_buffers=20MB"
    env_file:
      - postgresql.env
    restart: unless-stopped
    volumes:
      - ./postgresql_data:/var/lib/postgresql/data
      - ../postgresql/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
    healthcheck:
      # owsub is the last DB created in init-db.sh
      test: ["CMD-SHELL", "pg_isready -U postgres -d owsub"]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s        
