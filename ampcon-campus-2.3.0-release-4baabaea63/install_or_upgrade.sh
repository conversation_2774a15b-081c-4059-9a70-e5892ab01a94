#!/bin/bash

set -e

PRO_PATH=/usr/share/automation/server
INSTALL_WITHOUT_START=false
RUNNING_VERSION_PATH=/usr/share/automation/server/.env
CURRENT_VERSION_PATH=.env

current_version=""
pre_version=""
ready_version=""

current_pro_type=""
ready_pro_type=""

service_lockfile="/tmp/service.lock"


check_install_without_start() {
  while (( "$#" )); do
    case "$1" in
      --without-start)
        INSTALL_WITHOUT_START=true
        shift
        ;;
      *)
        usage
        ;;
    esac
  done
}

old_env_support(){
  ENV_FILE="/usr/share/automation/server/.env"
  if [[ -f "$ENV_FILE" ]]; then
    if grep -q "^REACT_APP_" "$ENV_FILE"; then
        echo "current file is new"
    else
        awk 'NR<=3 {print "REACT_APP_" $0} NR>3 {print $0}' $ENV_FILE > temp.env
        mv temp.env $ENV_FILE
        echo "current env file update success"
    fi
  fi
}

compare_version(){
  pre_version=$(grep -oP '^REACT_APP_PRE_VERSION=\K.*' "$RUNNING_VERSION_PATH")
  current_version=$(grep -oP '^REACT_APP_VERSION=\K.*' "$RUNNING_VERSION_PATH")
  ready_version=$(grep -oP '^REACT_APP_VERSION=\K.*' "$CURRENT_VERSION_PATH")

  current_pro_type=$(grep -oP '^PRO_TYPE=\K.*' "$RUNNING_VERSION_PATH" || true)
  ready_pro_type=$(grep -oP '^PRO_TYPE=\K.*' "$CURRENT_VERSION_PATH" || true)

  if [ "$pre_version" = "$ready_version" ]; then
    echo ">>>>>>>>>>>>>>>>>>>   AmpCon project not allowed downgrade !!!         <<<<<<<<<<<<<<<<<<<<<<"
    exit;
  elif [ "$current_version" = "$ready_version" ]; then
    echo ">>>>>>>>>>>>>>>>>>>   AmpCon project version is same  !!!              <<<<<<<<<<<<<<<<<<<<<<"
    exit;
  fi

  if [ -n "$current_pro_type" ] && [ "$current_pro_type" != "$ready_pro_type" ]; then
    echo ">>>>>>>>>>>>>>>>>>>   The currently running project type and upgrade package type do not match   !!!    <<<<<<<<<<<<<<<<<<<<<<"
    exit;
  fi
}

clean_docker_images(){
  if [ "$(sudo docker ps -aq)" ]; then
    sudo docker rm -f $(sudo docker ps -aq)
  fi

  sudo docker images --format "{{.Repository}}:{{.Tag}}" | while read -r image; do
    tag=$(echo "$image" | awk -F ':' '{print $2}')
    if [[ (-n "$pre_version" && "$tag" != "$pre_version") || (-n "$current_version" && "$tag" != "$current_version") ]]; then
      sudo docker rmi -f $image
    fi
  done
}

prepare_env_backup_start_server(){
  echo ">>>>>>>>>>>>>>>>>>>   AmpCon project backup project data start          <<<<<<<<<<<<<<<<<<<<<<"
  cp -r ${PRO_PATH} "${PRO_PATH}_${current_version}_bak"
  rm -f ${PRO_PATH}/docker_images/*
  echo ">>>>>>>>>>>>>>>>>>>   AmpCon project backup project data successfully   <<<<<<<<<<<<<<<<<<<<<<"
  cp -r * ${PRO_PATH}
  cp ${PRO_PATH}/data/settings/inner/automation.ini ${PRO_PATH}/data/settings/inner/upgrade_automation.ini
  cp ${PRO_PATH}_${current_version}_bak/data/settings/inner/automation.ini ${PRO_PATH}/data/settings/inner/automation.ini
  cp .env ${PRO_PATH}
  cp .import_images.sh ${PRO_PATH}/import_images.sh
  cp .start.sh ${PRO_PATH}/start.sh
  cp .stop.sh ${PRO_PATH}/stop.sh
  cp .update_openvpn_subnet.sh ${PRO_PATH}/update_openvpn_subnet.sh
  cp .fix_openvpn_subnet_mask.sh ${PRO_PATH}/fix_openvpn_subnet_mask.sh
  chmod 555 ${PRO_PATH}/data/mysql/start_sh/*.sh
  [ -f ${PRO_PATH}/smb/owrrm_data/runner.sh ] && chmod 555 ${PRO_PATH}/smb/owrrm_data/runner.sh
  cd ${PRO_PATH}
  MAC_ADDRESS=$(
    for iface in /sys/class/net/en* /sys/class/net/eth*; do
      if [ -f "$iface/address" ]; then
        echo "$(cat $iface/address)"
        break
      fi
    done | head -n 1
  )
  echo "$MAC_ADDRESS" > ${PRO_PATH}/data/settings/inner/mac_address
  if [ ! -d "$PRO_PATH/ampcon_data/prometheus" ]; then
    mkdir -p ${PRO_PATH}/ampcon_data/prometheus  
    chown -R 65534:65534 ${PRO_PATH}/ampcon_data/prometheus 
  fi
}


do_upgrade_post_action(){
  echo ">>>>>>>>>>>>>>>>>>>   AmpCon project upgrade post action start          <<<<<<<<<<<<<<<<<<<<<<"
  sed -i "s/^REACT_APP_VERSION=.*/REACT_APP_VERSION=${ready_version}/g" ${RUNNING_VERSION_PATH}
  sed -i "s/^REACT_APP_PRE_VERSION=.*/REACT_APP_PRE_VERSION=${current_version}/g" ${RUNNING_VERSION_PATH}
  sudo docker exec -i $(sudo docker ps -qf name=flask-main-service) bash -c "cd /usr/share/automation && python sub_upgrade.py"
  cd /usr/share/automation/server && sudo docker compose restart flask-main-service
  echo ">>>>>>>>>>>>>>>>>>>   AmpCon project upgrade post action successfully   <<<<<<<<<<<<<<<<<<<<<<"
}

do_install_post_action(){
  echo ">>>>>>>>>>>>>>>>>>>   AmpCon project install post action start          <<<<<<<<<<<<<<<<<<<<<<"
  sudo docker exec -i $(sudo docker ps -qf name=flask-main-service) bash -c "cd /usr/share/automation && python sub_upgrade.py --install"
  cd /usr/share/automation/server && sudo docker compose restart flask-main-service
  echo ">>>>>>>>>>>>>>>>>>>   AmpCon project install post action successfully   <<<<<<<<<<<<<<<<<<<<<<"
}

configure_system_service(){
  service_file="/usr/lib/systemd/system/ampcon.service"
  if [ ! -f $service_file ]; then
    echo "[Unit]
    Description=AmpCon APP Server Daemon
    After=network.target
    [Service]
    Type=oneshot
    RemainAfterExit=yes
    ExecStart=/usr/bin/sudo /usr/share/automation/server/start.sh
    ExecStop=/usr/bin/sudo /usr/share/automation/server/stop.sh
    ExecReload=/usr/bin/sudo /usr/share/automation/server/stop.sh && /usr/share/automation/server/start.sh
    StandardOutput=journal
    TimeoutSec=300
    Restart=on-failure
    KillMode=process
    WorkingDirectory=/usr/share/automation/server
    [Install]
    WantedBy=multi-user.target" > /usr/lib/systemd/system/ampcon.service
    systemctl daemon-reload
    systemctl enable ampcon
  fi
}


check_system_service(){
  service_file="/usr/lib/systemd/system/ampcon.service"
  if [ -f $service_file ]; then
    systemctl stop ampcon
    sleep 8
    rm -f ${service_file}
    systemctl daemon-reload
  fi
}


configure_ampcon_upstart(){
cat << 'EOF' > /usr/share/automation/server/auto_start.sh
#!/bin/bash

while true; do
    if systemctl is-active --quiet docker; then
        echo "Linux machine power on, Ampcon server starting..." > /usr/share/automation.log
        /usr/share/automation/server/start.sh true
        echo "Linux machine power on, Ampcon server started..." > /usr/share/automation.log
        exit 0
    fi
    sleep 2
done
EOF

chmod +x /usr/share/automation/server/auto_start.sh

cat << 'EOF' > /etc/rc.local
#!/bin/bash

DIR="/usr/share/automation/server"
SCRIPT="./auto_start.sh"

nohup sh -c "cd $DIR && $SCRIPT" > /dev/null 2>&1 &

EOF

chmod +x /etc/rc.local

touch /usr/share/automation.log
chmod 666 /usr/share/automation.log
}


prepare_env_for_install(){
  systemctl enable docker
  check_system_service

  if [ "$(docker ps -q)" ]; then
    sudo docker rm -f $(sudo docker ps -aq)
  fi
  yes | sudo docker system prune -a

  cp -r * ${PRO_PATH}
  cp .env ${PRO_PATH}
  cp .import_images.sh ${PRO_PATH}/import_images.sh
  cp .start.sh ${PRO_PATH}/start.sh
  cp .stop.sh ${PRO_PATH}/stop.sh
  cd ${PRO_PATH}
  rm -f ./install_or_upgrade.sh
  mkdir -p ./ampcon_data/mysql ./otn_data/mysql ./ampcon_data/prometheus ./ampcon_data/redis
  chown 999:999 ./ampcon_data/mysql ./otn_data/mysql
  chown -R 65534:65534 ./ampcon_data/prometheus
  chmod 555 ${PRO_PATH}/data/mysql/start_sh/*.sh
  [ -f ${PRO_PATH}/smb/owrrm_data/runner.sh ] && chmod 555 ${PRO_PATH}/smb/owrrm_data/runner.sh
  test -d ${PRO_PATH}/smb/kafka_data || mkdir -p ${PRO_PATH}/smb/kafka_data && chmod -R 777 ${PRO_PATH}/smb/kafka_data
  test -d ${PRO_PATH}/smb/postgresql/logs || mkdir -p ${PRO_PATH}/smb/postgresql/logs && chmod -R 777 ${PRO_PATH}/smb/postgresql/logs
  MAC_ADDRESS=$(
    for iface in /sys/class/net/en* /sys/class/net/eth*; do
      if [ -f "$iface/address" ]; then
        echo "$(cat $iface/address)"
        break
      fi
    done | head -n 1
  )
  echo "$MAC_ADDRESS" > ${PRO_PATH}/data/settings/inner/mac_address
  configure_ampcon_upstart
#  configure_system_service
}


prepare_env_for_upgrade(){
  old_env_support
  compare_version
  ${PRO_PATH}/stop.sh
  clean_docker_images
  prepare_env_backup_start_server
  configure_ampcon_upstart
#  configure_system_service
}

#cleanup() {
#  echo "Releasing lock and cleaning up..."
#  flock -u 200
#  rm -f "$service_lockfile"
#}




main(){
  exec 200>"$service_lockfile"

#  trap cleanup EXIT
#  trap "echo 'Script interrupted, exiting...'; exit 1" SIGINT

  flock -n 200 || {
    echo "install && upgrade script already running, please wait seconds..."
    exit 1
  }

  check_install_without_start "$@"
  if [ ! -d "${PRO_PATH}" ] || [ -z "$(ls -A "${PRO_PATH}")" ]; then
    mkdir -p /usr/share/automation/server
    echo ">>>>>>>>>>>>>>>>>>> AmpCon project ${PRO_PATH} dir not exists, ready to install         <<<<<<<<<<<<<<<<<<<<<<"
    prepare_env_for_install
    if [ "${INSTALL_WITHOUT_START}" = false ]; then
      ${PRO_PATH}/start.sh
      do_install_post_action
    fi
  else
    echo ">>>>>>>>>>>>>>>>>>> AmpCon project ${PRO_PATH} dir already exists, ready to upgrade     <<<<<<<<<<<<<<<<<<<<<<"
    prepare_env_for_upgrade
    if [ "${INSTALL_WITHOUT_START}" = false ]; then
      ${PRO_PATH}/start.sh
      do_upgrade_post_action
    fi
  fi
}

main "$@"