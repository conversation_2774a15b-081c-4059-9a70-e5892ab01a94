upstream main_app {
  ip_hash;
  server flask-main-service:443;
}

upstream otn_app {
  ip_hash;
  server tnms_web:8888;
}

upstream ssh_app {
  server ssh-service:80;
}

server {
    listen 443 ssl;
    listen [::]:443 default_server ipv6only=on;

    server_name localhost;

    ssl_certificate           /etc/nginx/cert.crt;
    ssl_certificate_key       /etc/nginx/cert.key;

    ssl_session_cache  builtin:1000  shared:SSL:10m;
    ssl_ciphers HIGH:!aNULL:!eNULL:!EXPORT:!CAMELLIA:!DES:!MD5:!PSK:!RC4;
    ssl_prefer_server_ciphers on;

    location / {
        root /etc/nginx/dist;
        try_files $uri $uri/ /index.html =404;
    }

    location /ampcon/ {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://main_app/;
        proxy_http_version 1.1;
        proxy_connect_timeout 4s;
        proxy_read_timeout 8760m;
        proxy_send_timeout 12s;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location ~ ^/ssh(/.*|$) {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_redirect off;
        proxy_pass http://ssh_app$1;
    }


    location /ws {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://ssh_app/ws;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }


    location = /auth {
        internal;
        proxy_pass http://main_app/auth;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Original-URI $request_uri;
        proxy_set_body $request_body;
    }

    location /otn/ {
        rewrite ^/otn(.*)$ $1 break;
        auth_request /auth;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_redirect off;
        proxy_pass   http://otn_app;
    }

    location /otn/api/info {
        rewrite ^/otn(.*)$ $1 break;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_redirect off;
        proxy_pass   http://otn_app;
    }

    location /otn/api/register {
        rewrite ^/otn(.*)$ $1 break;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_redirect off;
        proxy_pass   http://otn_app;
    }

    location /otn/api/ws {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://otn_app/api/ws;
        proxy_http_version 1.1;
        proxy_connect_timeout 4s;
        proxy_read_timeout 8760m;
        proxy_send_timeout 12s;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location /otn/api {
        auth_request /auth;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://otn_app/api;
    }

    error_page 403 /403.html;
    error_page 500 502 503 504 /50x.html;

    location = /403.html {
        root /usr/share/nginx/html;
        internal;
    }

    location = /50x.html {
        root /usr/share/nginx/html;
        internal;
    }
}

server {
    listen 80;
    server_name local;

    location / {
        return 302 https://$host$request_uri;
    }
}

