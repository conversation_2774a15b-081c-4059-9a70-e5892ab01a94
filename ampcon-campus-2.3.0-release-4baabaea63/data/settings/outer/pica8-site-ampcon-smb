upstream main_app {
  ip_hash;
  server flask-main-service:443;
}

upstream ssh_app {
  server ssh-service:80;
}


server {
    listen 443 ssl;
    listen [::]:443 default_server ipv6only=on;

    server_name localhost;

    ssl_certificate           /etc/nginx/cert.crt;
    ssl_certificate_key       /etc/nginx/cert.key;

    ssl_session_cache  builtin:1000  shared:SSL:10m;
    ssl_ciphers HIGH:!aNULL:!eNULL:!EXPORT:!CAMELLIA:!DES:!MD5:!PSK:!RC4;
    ssl_prefer_server_ciphers on;

    location / {
        root /etc/nginx/dist;
        try_files $uri $uri/ /index.html =404;
    }

    location ~ ^/(management|reg|deploy|rma)/ {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://main_app;
        proxy_http_version 1.1;
        proxy_connect_timeout 4s;
        proxy_read_timeout 8760m;
        proxy_send_timeout 12s;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location /ampcon/ {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://main_app/;
        proxy_http_version 1.1;
        proxy_connect_timeout 4s;
        proxy_read_timeout 8760m;
        proxy_send_timeout 12s;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location /smb/owsec/ {
        #proxy_set_header Host $host;
        #proxy_set_header X-Real-IP $remote_addr;
        #proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #proxy_set_header X-Forwarded-Proto $scheme;
        #proxy_pass https://owsec:16001/;
        #proxy_http_version 1.1;
        #proxy_connect_timeout 4s;
        #proxy_read_timeout 8760m;
        #proxy_send_timeout 12s;
        #proxy_set_header Upgrade $http_upgrade;
        #proxy_set_header Connection "upgrade";
		
		# 代理到 HTTPS 后端服务器
        proxy_pass https://owsec:16001/;
		
		# 忽略 SSL 验证
		proxy_ssl_verify off;


        # 设置请求头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 允许代理连接升级
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
    }
	
	location /smb/owprov/ {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass https://owprov:16005/;
        proxy_http_version 1.1;
        proxy_connect_timeout 4s;
        proxy_read_timeout 8760m;
        proxy_send_timeout 12s;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
	
	location /smb/owgw/ {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass https://owgw:16002/;
        proxy_http_version 1.1;
        proxy_connect_timeout 4s;
        proxy_read_timeout 8760m;
        proxy_send_timeout 12s;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
	
#	location /smb/owfms/ {
#        proxy_set_header Host $host;
#        proxy_set_header X-Real-IP $remote_addr;
#        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#        proxy_set_header X-Forwarded-Proto $scheme;
#        proxy_pass https://owfms:16004/;
#        proxy_http_version 1.1;
#        proxy_connect_timeout 4s;
#        proxy_read_timeout 8760m;
#        proxy_send_timeout 12s;
#        proxy_set_header Upgrade $http_upgrade;
#        proxy_set_header Connection "upgrade";
#    }
	
#	location /smb/owsub/ {
#        proxy_set_header Host $host;
#        proxy_set_header X-Real-IP $remote_addr;
#        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#        proxy_set_header X-Forwarded-Proto $scheme;
#        proxy_pass https://owsub:16006/;
#        proxy_http_version 1.1;
#        proxy_connect_timeout 4s;
#        proxy_read_timeout 8760m;
#        proxy_send_timeout 12s;
#        proxy_set_header Upgrade $http_upgrade;
#        proxy_set_header Connection "upgrade";
#    }
	
	location /smb/owanalytics/ {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass https://owanalytics:16009/;
        proxy_http_version 1.1;
        proxy_connect_timeout 4s;
        proxy_read_timeout 8760m;
        proxy_send_timeout 12s;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
	
	location /smb/owrrm/ {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://owrrm:16789/;
        proxy_http_version 1.1;
        proxy_connect_timeout 4s;
        proxy_read_timeout 8760m;
        proxy_send_timeout 12s;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
	
#	location /smb/owls/ {
#        proxy_set_header Host $host;
#        proxy_set_header X-Real-IP $remote_addr;
#        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#        proxy_set_header X-Forwarded-Proto $scheme;
#        proxy_pass https://owls:16001/;
#        proxy_http_version 1.1;
#        proxy_connect_timeout 4s;
#        proxy_read_timeout 8760m;
#        proxy_send_timeout 12s;
#        proxy_set_header Upgrade $http_upgrade;
#        proxy_set_header Connection "upgrade";
#    }
#	
#	location /smb/owinstaller/ {
#        proxy_set_header Host $host;
#        proxy_set_header X-Real-IP $remote_addr;
#        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#        proxy_set_header X-Forwarded-Proto $scheme;
#        proxy_pass https://owinstaller:16001/;
#        proxy_http_version 1.1;
#        proxy_connect_timeout 4s;
#        proxy_read_timeout 8760m;
#        proxy_send_timeout 12s;
#        proxy_set_header Upgrade $http_upgrade;
#        proxy_set_header Connection "upgrade";
#    }

    location = /auth {
        internal;
        proxy_pass http://main_app/auth;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Original-URI $request_uri;
        proxy_set_body $request_body;
    }

     location ~ ^/ssh(/.*|$) {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_redirect off;
        proxy_pass http://ssh_app$1;
    }


    location /ws {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://ssh_app/ws;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    error_page 403 /403.html;
    error_page 500 502 503 504 /50x.html;

    location = /403.html {
        root /usr/share/nginx/html;
        internal;
    }

    location = /50x.html {
        root /usr/share/nginx/html;
        internal;
    }
}

server {
    listen 80;
    server_name ssh_app.local;

    location /wireless {
		#root /etc/nginx/wireless_image;
        #try_files $uri $uri/ /index.html =404;
		#
        #types {
        #    application/octet-stream bin;
        #    application/octet-stream exe;
        #    application/pdf pdf;
        #}
        #root /wireless_image;
        #try_files $uri $uri/ /index.html =404;
		
		
		proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://main_app/;
        proxy_http_version 1.1;
        proxy_connect_timeout 4s;
        proxy_read_timeout 8760m;
        proxy_send_timeout 12s;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location ~*/(css|img|js) {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_redirect off;
        proxy_pass  http://ssh_app;
    }

    location /onie {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_redirect off;
        proxy_pass   http://ssh_app/onie;
    }

    location / {
        return 302 https://$host$request_uri;
    }

    location /ws {
        proxy_set_header  Host $host;
        proxy_set_header  X-Real-IP  $remote_addr;
        proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header  X-Forwarded-Proto   $scheme;
        proxy_pass        http://ssh_app/ws;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

}

