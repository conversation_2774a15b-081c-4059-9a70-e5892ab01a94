INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S5800-48T4S', 'ifIndex', '*******.*******.1.1', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S5800-48T4S', 'fanIndex', '*******.4.1.52642.********.*******.1', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S5800-48T4S', 'supplyIndex', '*******.4.1.52642.********.1.3', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S5850C-24XMG2C', 'ifIndex', '*******.*******.1.1', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S5850C-24XMG2C', 'fanIndex', '*******.4.1.52642.********.*******.1', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S5850C-24XMG2C', 'supplyIndex', '*******.4.1.52642.********.1.3', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S3410-48TS-P', 'ifIndex', '*******.*******.1.1', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S3410-48TS-P', 'fanIndex', '*******.4.1.52642.********.********.1', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S3410-48TS-P', 'supplyIndex', '*******.4.1.52642.********.********.1', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S5500-48T6SP-R', 'ifIndex', '*******.*******.1.1', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S5500-48T6SP-R', 'fanIndex', '', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S5500-48T6SP-R', 'supplyIndex', '', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S5850-48S6Q-R', 'ifIndex', '*******.*******.1.1', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S5850-48S6Q-R', 'fanIndex', '*******.4.1.52642.********.*******.1', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S5850-48S6Q-R', 'supplyIndex', '*******.4.1.52642.********.1.3', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S5860-24MG-U', 'ifIndex', '*******.*******.1.1', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S5860-24MG-U', 'fanIndex', '*******.4.1.52642.********.********.1', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
INSERT INTO snmp_metric_index (model, index_name, base_oid, description, is_internal)
        VALUES ('S5860-24MG-U', 'supplyIndex', '*******.4.1.52642.********.********.1', '', 1) AS new
        ON DUPLICATE KEY UPDATE
            base_oid=new.base_oid,
            description=new.description,
            is_internal=new.is_internal;
