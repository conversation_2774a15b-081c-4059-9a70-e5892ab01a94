DROP PROCEDURE IF EXISTS ampcon_clear_table_data;

DE<PERSON><PERSON>ITER $$

CREATE PROCEDURE `ampcon_clear_table_data`()
BEGIN
    DECLARE table_exists INT DEFAULT 0;

    -- 检查表是否存在
    SELECT COUNT(*) INTO table_exists FROM information_schema.tables WHERE table_schema = 'automation' AND table_name = 'distributed_lock';

    -- 如果表存在，则删除其数据
    IF table_exists = 1 THEN
        TRUNCATE TABLE automation.distributed_lock;
		commit;
    END IF;
END$$

DELIMITER ;

CALL ampcon_clear_table_data();