#!/bin/bash

log_warn() {
    echo "  $1"
    logger -t " [Auto-deploy]" -p user.warning "$1"
}

sudo rm -rf /backup/pica/config/pica*
sudo rm -rf /pica/config/pica*
sudo rm -f /ovs/*.conf.db
sudo rm -rf /home/<USER>/
sudo rm -f /cftmp/auto/post-xorplus
sudo rm -f /var/lib/dhcp/*
sudo rm -f /etc/openvpn/client*
sudo rm -f /etc/openvpn/ca.crt
sudo rm -f /etc/openvpn/automation.up
sudo rm -f /etc/openvpn/update_vpn_ip.sh
sudo license -r

echo "admin:pica8" | sudo chpasswd

type save_config &> /dev/null
ret=$?
if [[ ${ret} -eq 0 ]]; then
    sudo save_config
    log_warn "Do save_config"
fi

log_warn "Succeed in restoring pica8 configuration"
log_warn "Please reboot this switch"
