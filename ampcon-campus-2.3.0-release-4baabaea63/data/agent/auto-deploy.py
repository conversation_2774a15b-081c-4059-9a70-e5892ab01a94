#!/usr/bin/python3

import threading
import subprocess
import re, time, os
import urllib.request, urllib.parse, urllib.error, urllib.request, urllib.error, urllib.parse
import fcntl, sys, socket
import configparser
import signal
import json
import ssl
import logging, logging.handlers


# Constants
BUSY_BOX_MODEL_LIST = ['S3410L-24TF', 'S3410L-24TF-P', 'S3410-24TS', 'S3410-24TS-P', 'S3410C-16TF',
                       'S3410C-16TF-P', 'S3410C-16TMS-P', 'S3410C-8TMS-P', 'S3270-10TM', 'S3270-24TM', 'S3270-48TM',
                       'S3270-10TM-P', 'S3270-24TM-P']


try:
    ssl._create_default_https_context = ssl._create_unverified_context
except:
    pass


def create_vrf_connection(address, timeout=socket._GLOBAL_DEFAULT_TIMEOUT,
                          source_address=None):
    from _socket import error, getaddrinfo, SOCK_STREAM

    host, port = address
    err = None
    for res in getaddrinfo(host, port, 0, SOCK_STREAM):
        af, socktype, proto, canonname, sa = res
        sock = None
        try:
            sock = socket.socket(af, socktype, proto)
            if socket.VRF:
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_BINDTODEVICE, (socket.VRF + '\0').encode())
            if timeout is not socket._GLOBAL_DEFAULT_TIMEOUT:
                sock.settimeout(timeout)
            if source_address:
                sock.bind(source_address)
            sock.connect(sa)
            return sock

        except error as _:
            err = _
            if sock is not None:
                sock.close()

    if err is not None:
        raise err
    else:
        raise error("getaddrinfo returns an empty list")


# Replace socket.create_connection to support vrf
socket.create_connection = create_vrf_connection
socket.VRF = None
if not hasattr(socket, 'SO_BINDTODEVICE'):
    socket.SO_BINDTODEVICE = 25

_auto_dir = '/home/<USER>'
_deploy_dir = '/opt/auto-deploy/'
_agent_deployed_flag = '%s/auto_flag' % _auto_dir
_push_config_file = '%s/auto.config' % _auto_dir
_backup_config_file = '%s/backup.config' % _auto_dir
_domain_name_file = '%s/domain' % _auto_dir
_ansible_tmp = '%s/.ansible' % _auto_dir
_agent_conf_filename = '/opt/auto-deploy/auto-deploy.conf'
_bashrc_file = '/etc/bash.bashrc'
_bashrc_bak_file = '/etc/bash.bashrc.bak'
_upgrade2_log_file = '/cftmp/upgrade2.log'
_pid_file = '/var/run/auto-deploy.pid'
_rollback_file = '/mnt/open/picos/rollback'

ALARM_CONNECTED = 0
ALARM_EXIT_SUCCESS = -1
ALARM_EXIT_FAIL = -2
ALARM_CONNECTING = -3
ALARM_NO_VLAN = 1
ALARM_NO_IP = 2
ALARM_SERVER_UNREACHABLE = 3

STATUS_DEPLOYED = -1  # switch has been deployed
STATUS_NO_FLAG = 0  # no flag file and agent is first started
STATUS_DEPLOYING = 1  # agent has connected to server and switch is deploying
STATUS_LOAD_CONFIG = 2  # agent needs to load full config
STATUS_LOAD_RMA_CONFIG = 3  # agent needs to load rma config
STATUS_UPGRADING = 4  # switch is doing upgrade2

STATE_FIRST_REGISTER = 0  # first time to register
STATE_NOT_CONFIGURED = 1  # registered but not configured
STATE_CONFIGURED = 2  # user config pushed
STATE_UPGRADING = 3  # doing upgrade2
STATE_DEPLOYED = 4  # switch has been deployed or upgraded
STATE_MAX_FAILS = -1  # max tries reached

_wait_time = 30
_alarm_type = ALARM_CONNECTING
_lock_fd = 0
_event = threading.Event()
_config = configparser.ConfigParser()
_ntp_server = None
_ntp_synced = False
_has_mgmt_vrf = False
MGMT_VRF = 'mgmt-vrf'


def sigint_handler(signum, frame):
    log_warn("Auto-deploy Agent Stop")
    unlock_agent()
    if signum == signal.SIGINT:
        print('catched interrupt signal!')
    elif signum == signal.SIGTERM:
        print('catched termination signal!')
    sys.exit(1)


def check_enable():
    if not os.path.isfile(_agent_conf_filename):
        sys.exit(1)

    global _config
    _config.read(_agent_conf_filename)
    try:
        enable = _config.getboolean("DEFAULT", "enable")
    except:
        enable = False
    if not enable:
        sys.exit(1)
    try:
        log_to_console = _config.getboolean("DEFAULT", "log_to_console")
    except:
        log_to_console = False
    if log_to_console:
        add_log_to_console()


def check_already_running():
    global _lock_fd
    _lock_fd = open(_pid_file, "a+")
    try:
        fcntl.flock(_lock_fd, fcntl.LOCK_EX | fcntl.LOCK_NB)
        pid = str(os.getpid())
        _lock_fd.truncate()
        _lock_fd.write(pid)
        _lock_fd.flush()
    except:
        pid = _lock_fd.read()
        print("Auto-deploy is already running as pid %s, aborting" % pid)
        log_warn("already running as pid %s, aborting" % pid)
        _lock_fd.close()
        sys.exit(1)


def unlock_agent():
    global _lock_fd
    if _lock_fd:
        fcntl.flock(_lock_fd, fcntl.LOCK_UN)
        _lock_fd.close()
        _lock_fd = 0
        os.unlink(_pid_file)


def check_work_path(path):
    if not os.path.exists(path):
        os.system("sudo mkdir -m777 %s" % path)
    else:
        os.system("sudo chmod -R 777 %s" % path)


def check_rollback(need_save_config):
    if need_save_config:
        if os.path.isfile(_rollback_file):
            log_warn("Rollback has been done")
            os.system("sudo rm -f %s" % _rollback_file)
            return True
        return False
    if os.path.isfile(_upgrade2_log_file):
        cmd = "tail -1 %s | grep 'Rollback.*successful'" % _upgrade2_log_file
        result, rt = subprocess.getstatusoutput(cmd)
        if rt:
            log_warn(rt)
            return True
    return False


def check_mgmt_vrf():
    global _has_mgmt_vrf
    res, rt = subprocess.getstatusoutput('ip link show')
    if res == 0 and MGMT_VRF in rt:
        _has_mgmt_vrf = True
    else:
        _has_mgmt_vrf = False


def sync_time_ntp():
    global _config
    global _ntp_server
    global _has_mgmt_vrf
    global _ntp_synced

    if _ntp_synced:
        return

    if not _ntp_server:
        ntp_server = 'pool.ntp.org'
        try:
            ntp_server = _config.get("DEFAULT", "ntp_server")
        except:
            pass
        _ntp_server = ntp_server

    ntp_running = False
    res, rt = subprocess.getstatusoutput('sudo systemctl is-active ntp')
    if rt == 'active':
        ntp_running = True

    if ntp_running:
        os.system('sudo service ntp stop')

    cmd = 'ntpdate -p 1 ' + _ntp_server
    res, rt = subprocess.getstatusoutput(cmd)
    if not res:
        log_warn('Sync time with ntp server %s' % _ntp_server)
        _ntp_synced = True
    elif _has_mgmt_vrf:
        cmd = 'sudo /sbin/ip vrf exec ' + MGMT_VRF + ' ' + cmd
        res, rt = subprocess.getstatusoutput(cmd)
        if not res:
            log_warn('Sync time with ntp server %s by mgmt-vrf.' % _ntp_server)
            _ntp_synced = True

    if ntp_running:
        os.system('sudo service ntp start')
    if _ntp_synced:
        return

    try:
        import ntplib
        client = ntplib.NTPClient()
        response = client.request(_ntp_server)
        log_warn('Sync time with ntp server %s' % _ntp_server)
        os.system('date ' + time.strftime('%m%d%H%M%Y.%S', time.localtime(response.tx_time)))
        _ntp_synced = True
    except:
        log_warn('Could not sync time with ntp server %s' % _ntp_server)
        _ntp_synced = False


def logger_init():
    try:
        logger = logging.getLogger('Auto-deploy')
        logger.setLevel(logging.INFO)
        sh = logging.handlers.SysLogHandler('/dev/log')
        sh.setFormatter(logging.Formatter(" [%(name)s]%(message)s"))
        logger.addHandler(sh)
        return logger
    except Exception as e:
        print(e)
        sys.exit(1)


def add_log_to_console():
    try:
        logger = logging.getLogger('Auto-deploy')
        ch = logging.FileHandler('/dev/console')
        ch.setFormatter(logging.Formatter("%(asctime)s %(name)s %(levelname)s : %(message)s"))
        logger.addHandler(ch)
    except Exception as e:
        log_warn("Cannot log to console: %s" % str(e))
        print(e)


def send_vpn_reg(server, server_port, sn, ip, model, service_tag):
    while True:
        # get server IP
        try:
            server_ip = socket.getaddrinfo(server, server_port, 2, 0, socket.SOL_TCP)[0][4][0]
        except:
            log_warn("Resolve VPN server hostname [%s] failed" % server)
            log_warn("Agent will wait 30s")
            time.sleep(30)
            continue

        url = 'https://' + server_ip + ':' + server_port + '/management/vpn_reg/' \
              + sn + ';' + ip + ';' + model
        if service_tag:
            url = url + ';' + service_tag
        log_warn("Sending request url %s" % url)
        try:
            req = urllib.request.Request(url)
            f = urllib.request.urlopen(req, timeout=30)
            res = f.read()
            if type(res) == bytes:
                res = res.decode('utf-8')
            f.close()
            log_warn("Received reply message: %s" % res)
            if 'Found the VPN config' in res:
                break
        except urllib.error.HTTPError as e:
            log_warn("Failed to register in VPN server: %s, %s" % (e, e.read()))
            e.close()
        except urllib.error.URLError as e:
            log_warn("Failed to register in VPN server: %s" % e)
        log_warn("Agent will wait 30s")
        time.sleep(30)


def restart_openvpn():
    log_warn("\nStart the openvpn service.")
    ovpn_service = '/lib/systemd/system/openvpn@.service'
    restart_shell = '/opt/auto-deploy/restart_ovpn.sh'
    busybox_service = '/etc/init.d/openvpn'
    if os.path.isfile(ovpn_service):
        if not os.path.isfile(restart_shell):
            subprocess.getstatusoutput('service openvpn@client restart')
        else:
            vrf = 'default' if not socket.VRF else socket.VRF
            subprocess.getstatusoutput(restart_shell + ' ' + vrf)
    elif os.path.isfile(busybox_service):
        subprocess.getstatusoutput('/etc/init.d/openvpn restart')
    else:
        subprocess.getstatusoutput('service openvpn restart')

    time.sleep(10)


def sync_time_without_ntp(vpn_server_ip, vpn_server_port):
    # request server time if ntp sync failed
    if not _ntp_synced:
        log_warn("Request AmpCon Server time")
        url = 'https://' + vpn_server_ip + ':' + vpn_server_port + '/reg/vpn/time'
        try:
            req = urllib.request.Request(url)
            f = urllib.request.urlopen(req, timeout=30)
            res = f.read()
            if type(res) == bytes:
                res = res.decode('utf-8')
            f.close()
            log_warn("Received reply message: %s" % res)
            os.system('sudo date -us "%s"' % res)
        except urllib.error.HTTPError as e:
            log_warn("Failed to request AmpCon Server time: %s, %s" % (e, e.read()))
            e.close()
        except urllib.error.URLError as e:
            log_warn("Failed to request AmpCon Server time: %s" % e)


def enable_vpn(vpn_server, vpn_server_port, sn, server_host, server_ip):
    log_warn("Try to enable VPN")
    # resolve VPN server IP
    try:
        vpn_server_ip = socket.getaddrinfo(vpn_server, vpn_server_port, 2, 0, socket.SOL_TCP)[0][4][0]
    except:
        log_warn("Resolve VPN server hostname [%s] failed" % vpn_server)
        return False

    sync_time_without_ntp(vpn_server_ip, vpn_server_port)

    url = 'https://' + vpn_server_ip + ':' + vpn_server_port
    # get the VPN config from server
    file_dir = '/etc/openvpn/'
    config_url = url + '/management/vpn/' + sn + '/'
    static_config_url = url + '/management/vpn/'
    if not os.path.exists(file_dir):
        log_warn("\nNot installed OpenVPN, please check the switch version")
        return False

    def download(_save_path, _url):
        try:
            MyURLopener().retrieve(_url, _save_path)
            return True
        except urllib.error.HTTPError as e:
            log_warn("Download failed: %s, %s" % (e, e.read()))
            e.close()
            return False
        except Exception as e:
            log_warn("Download failed: %s" % e)
            return False

    log_warn("\nDownload URL: %sclient.conf" % config_url)
    ret = download(file_dir + 'client.conf', static_config_url + 'client.conf')
    if ret == False:
        return False
    log_warn("\nDownload URL: %sautomation.up" % config_url)
    ret = download(file_dir + 'automation.up', static_config_url + 'automation.up')
    if ret == False:
        return False
    log_warn("\nDownload URL: %supdate_vpn_ip.sh" % config_url)
    ret = download(file_dir + 'update_vpn_ip.sh', static_config_url + 'update_vpn_ip.sh')
    if ret == False:
        return False
    log_warn("\nDownload URL: %sclient.key" % config_url)
    ret = download(file_dir + 'client.key', config_url + 'client.key')
    if ret == False:
        return False
    log_warn("\nDownload URL: %sclient.crt" % config_url)
    ret = download(file_dir + 'client.crt', config_url + 'client.crt')
    if ret == False:
        return False
    log_warn("\nDownload URL: %sca.crt" % config_url)
    ret = download(file_dir + 'ca.crt', config_url + 'ca.crt')
    if ret == False:
        return False
    time.sleep(5)

    log_warn("\nadd execute permission in automation.up")
    cmd = "sed -i -e 's/\r$//' /etc/openvpn/automation.up"
    subprocess.getstatusoutput(cmd)
    cmd = "sed -i -e 's/\r$//' /etc/openvpn/update_vpn_ip.sh"
    subprocess.getstatusoutput(cmd)
    cmd = "chmod +x /etc/openvpn/automation.up /etc/openvpn/update_vpn_ip.sh"
    subprocess.getstatusoutput(cmd)

    sync_time_ntp()
    restart_openvpn()

    # remove the old VPN server hosts name
    cmd = 'sed -i "/\s%s/d" /etc/hosts' % server_host
    subprocess.getstatusoutput(cmd)
    # add the new VPN server host name
    cmd = 'sed -i "1i%s %s" /etc/hosts' % (server_ip, server_host)
    subprocess.getstatusoutput(cmd)
    cmd = 'route del -net default dev tun0'
    subprocess.getstatusoutput(cmd)
    time.sleep(5)
    return True


def log_warn(msg):
    _logger.warning(msg)


def turn_on_led_yellow():
    cmd = "echo 1 > /sys/class/leds/diag_led/brightness"
    subprocess.getstatusoutput(cmd)


def turn_on_led_green():
    cmd = "echo 2 > /sys/class/leds/diag_led/brightness"
    subprocess.getstatusoutput(cmd)


def turn_off_led():
    cmd = "echo 0 > /sys/class/leds/diag_led/brightness"
    subprocess.getstatusoutput(cmd)


def blink_led(num):
    for n in range(0, num):
        turn_off_led()
        time.sleep(0.5)
        turn_on_led_yellow()
        time.sleep(0.5)
    time.sleep(2)


def alarm_led():
    while True:
        _event.clear()
        if _alarm_type == ALARM_CONNECTED:
            turn_on_led_yellow()
            _event.wait()
        elif _alarm_type == ALARM_EXIT_SUCCESS:
            turn_on_led_green()
            os._exit(0)
        elif _alarm_type == ALARM_EXIT_FAIL:
            turn_on_led_yellow()
            os._exit(0)
        elif _alarm_type == ALARM_CONNECTING:
            turn_off_led()
            time.sleep(0.3)
            turn_on_led_yellow()
            time.sleep(0.3)
        elif _alarm_type > 0:
            blink_led(_alarm_type)
        else:
            _event.wait()


class MyURLopener(urllib.request.FancyURLopener):
    def http_error_500(self, url, fp, errcode, errmsg, headers, data=None):
        raise urllib.error.HTTPError(url, errcode, errmsg, headers, fp)

    def http_error_default(self, url, fp, errcode, errmsg, headers):
        """Default error handler: close the connection and raise IOError."""
        fp.close()
        raise IOError('http error', errcode, errmsg, headers)


class DeployAgent(threading.Thread):

    def __init__(self):
        threading.Thread.__init__(self)
        self.server_hostname_prefix = 'ac'
        self.server_domain = 'pica8.com'
        self.server_port = '80'
        self.uplink = []
        self.uplink_speed = '1000'
        self.inband_vlan = []
        self.inband_vlan_config = []
        self.inband_native_vlan = []
        self.inband_native_vlan_config = []
        self.inband_lacp = False
        self.probe_interval = 300
        self.max_retry = 3
        self.backoff = 9999
        self.stage = 'Init'
        self.state = STATE_FIRST_REGISTER
        self.vpn_enable = False
        self.server_vpn_ip = '********'
        self.server_vpn_host = 'vpn.pica8.com'
        self.connection_type = ''
        self.connected = False
        self.fail = 0
        self.wait_register_time = 0
        self.n_try = 0
        self.need_save_config = self.check_need_save_config()

        self.model = self.get_platform_name()
        self.version = self.get_version()
        self.read_conf_file()
        self.server_hostname = '%s.%s' % (self.server_hostname_prefix, self.server_domain)
        self.sn = self.get_serial_num()
        self.hw_id = self.get_hardware_id()
        self.service_tag = self.get_service_tag()
        self.default_config = self.get_default_config_file_name()

    def read_conf_file(self):
        if not os.path.isfile(_agent_conf_filename):
            log_warn("Cannot open conf file: %s, no such file" % _agent_conf_filename)
            return

        global _config
        try:
            self.server_hostname_prefix = _config.get("DEFAULT", "server_hostname_prefix")
        except:
            log_warn("Cannot read 'server_hostname_prefix' in conf file")
        try:
            server_domain = self.get_domain_name()
            if not server_domain:
                server_domain = _config.get("DEFAULT", "server_domain")
            self.server_domain = server_domain
        except:
            log_warn("Cannot read 'server_domain' in conf file")
        try:
            self.server_port = _config.get("DEFAULT", "server_port")
        except:
            log_warn("Cannot read 'server_port' in conf file")
        try:
            self.uplink = _config.get(self.model, "uplink").split(',')
        except:
            log_warn("Cannot read 'uplink' in conf file")
        try:
            uplink_speed = _config.get(self.model, "uplink_speed")
            if uplink_speed:
                self.uplink_speed = uplink_speed
        except:
            log_warn("Cannot read 'uplink_speed' in conf file")
        try:
            inband_vlans = _config.get("DEFAULT", "inband_vlan")
            if inband_vlans:
                self.inband_vlan_config = inband_vlans.split(',')
        except:
            log_warn("Cannot read 'inband_vlan' in conf file")
        try:
            inband_native_vlans = _config.get("DEFAULT", "inband_native_vlan")
            if inband_native_vlans:
                self.inband_native_vlan_config = inband_native_vlans.split(',')
        except:
            log_warn("Cannot read 'inband_native_vlan' in conf file")
        try:
            self.inband_lacp = _config.getboolean("DEFAULT", "inband_lacp")
        except:
            log_warn("Cannot read 'inband_lacp' in conf file")
        try:
            self.probe_interval = _config.getint("DEFAULT", "probe_interval")
        except:
            log_warn("Cannot read 'probe_interval' in conf file")
        try:
            self.max_retry = _config.getint("DEFAULT", "max_retry")
        except:
            log_warn("Cannot read 'max_retry' in conf file")
        try:
            self.backoff = _config.getint("DEFAULT", "backoff")
        except:
            log_warn("Cannot read 'backoff' in conf file")
        try:
            self.vpn_enable = _config.getboolean("DEFAULT", "vpn_enable")
        except:
            log_warn("Cannot read 'vpn_enable' in conf file")
        try:
            self.server_vpn_ip = _config.get("DEFAULT", "server_vpn_ip")
        except:
            log_warn("Cannot read 'server_vpn_ip' in conf file")
        try:
            self.server_vpn_host = _config.get("DEFAULT", "server_vpn_host")
        except:
            log_warn("Cannot read 'server_vpn_host' in conf file")

    def get_default_config_file_name(self):
        config1 = '/pica/bin/pica_default.boot'
        if os.path.exists(config1):
            return config1

        PICA_LC_OPTIONAL_MODULE_DIS = (1 << 0)  # power off
        PICA_LC_OPTIONAL_MODULE_NPS = (1 << 1)  # not presented
        PICA_LC_OPTIONAL_MODULE_PG = (1 << 2)  # power status good
        PICA_LC_OPTIONAL_MODULE_SFP = (1 << 3)  # type is sfp+
        PICA_LC_OPTIONAL_MODULE_10GBT = (1 << 4)  # type is 10GB-T
        PICA_LC_OPTIONAL_MODULE_QSFP = (1 << 5)  # type is QSFP
        PICA_LC_OPTIONAL_MODULE_SMP = (1 << 6)  # type is stacking module port

        name = self.model
        if name in ['N3024EP-ON', 'N3024ET-ON', 'N3048EP-ON', 'N3048ET-ON']:
            with open('/sys/class/swmon/ctrl/slot_info') as fslot:
                value = int(fslot.read().strip(), 16)
                if value & PICA_LC_OPTIONAL_MODULE_NPS:
                    if name in ['N3024EP-ON', 'N3024ET-ON']:
                        name = name + '/28P'
                    else:
                        name = name + '/52P'
                else:
                    if value & PICA_LC_OPTIONAL_MODULE_10GBT:
                        if name in ['N3024EP-ON', 'N3024ET-ON']:
                            name = name + '/30PT'
                        else:
                            name = name + '/54PT'
                    else:
                        if name in ['N3024EP-ON', 'N3024ET-ON']:
                            name = name + '/30PX'
                        else:
                            name = name + '/54PX'
        elif name == 'N3132PX-ON':
            with open('/sys/class/swmon/ctrl/slot_info') as fslot:
                value = int(fslot.read().strip(), 16)
                if value & PICA_LC_OPTIONAL_MODULE_NPS:
                    name = name + '/36P'
                else:
                    name = name + '/38P'
        elif name in ['S4148T-ON', 'S4148F-ON']:
            with open('/etc/picos/picos_start.conf') as cf:
                info = cf.read().strip()
                if '4x100G_QSFP' in info:
                    name = name + '/100G'
                else:
                    name = name + '/40G'
        elif name in ['as7726_32x', 'as7326_56x']:
            with open('/etc/picos/picos_start.conf') as cf:
                info = cf.read().strip()
                if 'FRONT_PANEL' in info:
                    name = name + '/FRONT_PANEL'
                else:
                    name = name + '/HOST_CPU'

        config2 = '/pica/etc/' + name + '/pica_default.boot'
        if os.path.exists(config2):
            return config2
        return ""

    def get_domain_name(self):
        domain_name = ''
        if os.path.isfile(_domain_name_file):
            with open(_domain_name_file, "r") as f:
                domain_name = f.readline().strip()
        return domain_name

    def save_domain_name(self):
        os.system("sudo rm -f %s" % _domain_name_file)
        os.system('sudo echo "%s" > %s' % (self.server_domain, _domain_name_file))
        log_warn("Store '%s' in %s" % (self.server_domain, _domain_name_file))

    def change_bashrc(self):
        # enable non-interactive
        file_data = ""
        interactive_str = '[ -z "$PS1" ] && exit'
        non_interactive_str = '[ -z "$PS1" ] && return'
        change = False
        if os.path.isfile(_bashrc_file):
            with open(_bashrc_file, "r") as f:
                for line in f:
                    if interactive_str in line:
                        line = line.replace(interactive_str, non_interactive_str)
                        change = True
                    file_data += line

        if change:
            if not os.path.isfile(_bashrc_bak_file):
                os.rename(_bashrc_file, _bashrc_bak_file)
            with open(_bashrc_file, "w") as f:
                f.write(file_data)
            os.chown(_bashrc_file, 0, 1)
            log_warn("Modify /etc/bash.bashrc")

    def restore_bashrc(self):
        # restore interactive mode
        if os.path.isfile(_bashrc_bak_file):
            log_warn("Restore /etc/bash.bashrc")
            os.remove(_bashrc_file)
            os.rename(_bashrc_bak_file, _bashrc_file)

    def get_serial_num(self):
        sys_sn = '/sys/class/swmon/hwinfo/serial_number'
        if os.path.isfile(sys_sn):
            with open(sys_sn) as f:
                sn = f.read().strip()
        else:
            cmd = "/pica/bin/system/fan_status -s | grep MotherBoard"
            result, rt = subprocess.getstatusoutput(cmd)
            sn = re.findall("MotherBoard Serial Number : (.*)", rt)[0]
        log_warn("Got serial num %s" % sn)
        return sn

    def get_hardware_id(self):
        cmd = "license -s | grep 'Hardware ID'"
        result, rt = subprocess.getstatusoutput(cmd)
        hw_id = re.findall("(([0-9A-Z]+-)+[0-9A-Z]+)", rt)[0][0]
        log_warn("Got hardware id %s" % hw_id)
        return hw_id

    def get_service_tag(self):
        sys_eeprom = '/sys/class/swmon/hwinfo/onie_syseeprom'
        key = 'Service Tag: '
        st = ''
        if os.path.isfile(sys_eeprom):
            f = open(sys_eeprom)
            info = f.read()
            f.close()
            index = info.find(key)
            if index != -1:
                start = index + len(key)
                end = start + info[start:].find('\n')
                st = info[start:end].strip()
                log_warn("Got Service Tag: %s" % st)
        return st

    def get_platform_name(self):
        product = '/sys/class/swmon/ctrl/product_id'
        if os.path.isfile(product):
            f = open(product)
            model = f.read().strip()
            f.close()
            if model in ('as5812_54x', 'as7326_56x', 'as7726_32x', 'S3910-48TS','S3910-24TS','S3100-16TMS-P','S3100-16TF-P','S3100-16TF','S3100-8TMS-P','S3910-48TF','S3410-24TF-P','S3910-24TF','AS4630-54NPE', "S5870-48T6BC-U"):
                cmd = "/usr/bin/version | grep Model"
                result, rt = subprocess.getstatusoutput(cmd)
                model = re.findall(": (.*)", rt)[0]
        else:
            cmd = "/usr/bin/version | grep Model"
            result, rt = subprocess.getstatusoutput(cmd)
            model = re.findall(": (.*)", rt)[0]
        log_warn("Got platform name %s" % model)
        return model

    def get_version(self):
        try:
            cmd = "/usr/bin/version | grep -E 'L2/L3 Version/Revision|PICOS Release/Commit|Software Version'"
            _, rt = subprocess.getstatusoutput(cmd)
            version = re.findall(": (.*)", rt)[0].split('/')[0]
            log_warn("Got version number %s" % version)
        except Exception as _:
            return '9.8.7'
        return version

    def is_version_4(self, version):
        try:
            main_version_num = int(version.split('.')[0])
            return True if main_version_num >= 4 else False
        except Exception as _:
            return True

    def agent_status(self):
        if os.path.isfile(_agent_deployed_flag):
            f = open(_agent_deployed_flag)
            status = f.read()
            f.close()
            if status.find("deployed") == 0:
                return STATUS_DEPLOYED
            if status.find("push_config") == 0:
                return STATUS_LOAD_CONFIG
            if status.find("rma_push_config") == 0:
                return STATUS_LOAD_RMA_CONFIG
            if status.find("upgrading") == 0:
                return STATUS_UPGRADING
            return STATUS_DEPLOYING
        return STATUS_NO_FLAG

    def check_need_save_config(self):
        result, rt = subprocess.getstatusoutput("type save_config")
        return False if result else True

    def do_save_config(self):
        if self.need_save_config:
            log_warn("Do save_config")
            os.system("sudo save_config")

    def rollback(self):
        if self.model in BUSY_BOX_MODEL_LIST:
            log_warn("Upgrade failed, agent will not rollback, due to busybox model")
            self.set_deployed_flag("deployed")
            return
        log_warn("Agent will do rollback")
        if self.need_save_config:
            os.system("sudo touch %s" % _rollback_file)
        os.system("sudo nos-rollback -y")

    def clean_deployed_flag(self):
        log_warn("Clean deploy flag")
        os.system("sudo rm -f %s" % _agent_deployed_flag)

    def set_deployed_flag(self, flag):
        log_warn("Set deploy flag: %s" % flag)
        os.system("sudo echo '%s' > %s" % (flag, _agent_deployed_flag))

    def set_socket_vrf(self):
        check_mgmt_vrf()
        if not _has_mgmt_vrf:
            socket.VRF = None
            return

        if not self.connection_type:
            self.connection_type = 'OUTOFBAND'
            local_ip = self.get_local_ip()
            if not local_ip:
                self.connection_type = 'INBAND'
                local_ip = self.get_local_ip()
                if not local_ip:
                    self.connection_type = ''

        if self.connection_type == 'OUTOFBAND':
            socket.VRF = MGMT_VRF
        else:
            socket.VRF = None

    def add_vpn_route_table252(self):
        if not self.vpn_enable:
            return True

        time.sleep(3)
        # find the tun0 is exist or not
        failed_num = 0
        while True:
            result, rt = subprocess.getstatusoutput("ifconfig tun0")
            if result != 0:
                log_warn("Can not find tunnel interface tun0")
                failed_num += 1
                time.sleep(5)
                if failed_num > 30:
                    log_warn("Can not find tunnel interface tun0 after try 30 times")
                    return False
            else:
                cmd = "sudo ip route add table 252 {0} dev tun0".format(self.server_vpn_ip)
                result, rt = subprocess.getstatusoutput(cmd)
                return True

    def check_vpn_route(self):
        time.sleep(1)

        # find the tun0 is exist or not
        result, rt = subprocess.getstatusoutput("ifconfig tun0")
        if result == 0:
            # check tun0 in iptables
            result, rt = subprocess.getstatusoutput("sudo iptables -nvL | grep tun0")
            if 'tun0' not in rt:
                log_warn("There is no policy in iptables, adding it")
                cmd = 'sudo iptables -I INPUT 4 -i tun0 -p tcp -m multiport --dport 22,23,8080 -j ACCEPT'
                result, rt = subprocess.getstatusoutput(cmd)

            result, rt = subprocess.getstatusoutput("ip route show table 252")
            if self.server_vpn_ip not in rt:
                log_warn("There is no VPN route, adding it")
                result, rt = subprocess.getstatusoutput(
                    "sudo ip route add table 252 {0} dev tun0".format(self.server_vpn_ip))

    def save_backup_config(self):
        log_warn("Backup current config to '%s'" % _backup_config_file)
        os.system("sudo cp -f /pica/config/pica_startup.boot %s" % _backup_config_file)

    def load_backup_config(self):
        log_warn("Load backup config '%s'" % _backup_config_file)
        cmd = "configure;load override " + _backup_config_file + ";"
        cmd += "commit"
        cmd = '/pica/bin/pica_sh -c \"' + cmd + '\"'
        result, rt = subprocess.getstatusoutput(cmd)
        if rt.find("Commit OK") == -1:
            log_warn("Load backup config failed: %s" % rt)
            return False
        return True

    def load_default_config(self):
        if self.version and self.version < '3.0':
            cmd = "configure;load override " + self.default_config + ";"
        else:
            cmd = "configure;rollback default;"
        cmd += "commit"
        cmd = '/pica/bin/pica_sh -c \"' + cmd + '\"'
        log_warn("Loading default config in switch")
        result, rt = subprocess.getstatusoutput(cmd)
        if rt.find("Commit OK") == -1:
            log_warn("Load default config failed: %s" % rt)
            return False
        return True

    def load_user_config(self):
        self.save_backup_config()
        if not self.load_default_config():
            return False
        cmd = "configure;execute " + _push_config_file + ";"
        cmd += "commit"
        cmd = '/pica/bin/pica_sh -c \"' + cmd + '\"'
        log_warn("Loading user config in switch")
        result, rt = subprocess.getstatusoutput(cmd)
        if rt.find("Commit OK") == -1:
            log_warn("Load user config failed: %s" % rt)
            self.load_backup_config()
            if self.vpn_enable:
                restart_openvpn()
                self.add_vpn_route_table252()
            time.sleep(10)
            self.notify_fail_to_server("push full config", rt)
            return False
        if self.vpn_enable:
            # restart_openvpn()
            self.add_vpn_route_table252()
        return True

    def load_rma_config(self):
        self.save_backup_config()
        if not self.load_default_config():
            return False
        cmd = "configure;load merge " + _push_config_file + ";"
        cmd += "commit"
        cmd = '/pica/bin/pica_sh -c \"' + cmd + '\"'
        log_warn("Loading rma config in switch")
        result, rt = subprocess.getstatusoutput(cmd)
        if rt.find("Commit OK") == -1:
            log_warn("Load rma config failed: %s" % rt)
            self.load_backup_config()
            self.add_vpn_route_table252()
            time.sleep(10)
            self.notify_fail_to_server("push rma config", rt)
            return False
        self.add_vpn_route_table252()
        return True

    def enable_lldp_inband(self):
        cmd = "configure;set protocols lldp enable true;"
        if not self.is_version_4(self.version):
            cmd += "set system inband enable true;"
        cmd += "set ip routing enable true;commit"
        cmd = '/pica/bin/pica_sh -c \"' + cmd + '\"'
        log_warn("Enable lldp/inband in switch")
        result, rt = subprocess.getstatusoutput(cmd)
        if rt.find("Commit OK") == -1:
            log_warn("Enable lldp/inband failed: %s" % rt)
            return False
        return True

    def set_uplink_trunk(self):
        if not self.uplink:
            log_warn("No uplink ports")
            return False

        cmd = "configure;"
        for port in self.uplink:
            cmd += "set interface gigabit-ethernet " + port + " family ethernet-switching port-mode trunk;"
            cmd += "set interface gigabit-ethernet " + port + " speed " + self.uplink_speed + ";"
        cmd += "commit"
        cmd = '/pica/bin/pica_sh -c \"' + cmd + '\"'
        log_warn("Set uplink port trunk in switch")
        result, rt = subprocess.getstatusoutput(cmd)
        if rt.find("Commit OK") == -1:
            log_warn("Set uplink port trunk failed: %s" % rt)
            return False
        return True

    def get_inband_vlan_by_lldp(self):
        global _alarm_type
        num = 0
        inband_vlan = []
        # get uplink ports' LLDP neighbor
        while True:
            log_warn("Getting inband vlan from LLDP neighbor")
            for port in self.uplink:
                cmd = '/pica/bin/lldp/tools/print_lldp -m LLDP_NEIGHBOR -d detail -i ' + port
                result, rt = subprocess.getstatusoutput(cmd)
                if result == 0:
                    vlan_list = re.findall("/[0-9]+\.([0-9]+)", rt)
                    for vlan in vlan_list:
                        if vlan != '0' and vlan not in inband_vlan:
                            inband_vlan.append(vlan)
            if inband_vlan:
                _alarm_type = ALARM_NO_IP
                _event.set()
                return inband_vlan
            if num > 0:
                _alarm_type = ALARM_NO_VLAN
                _event.set()
            num += 1
            log_warn("Agent will wait %ds" % _wait_time)
            time.sleep(_wait_time)

    def set_vlan_interface(self, inband_vlan, inband_native_vlan):
        cmd = "configure;"
        for vlan in inband_vlan:
            cmd += "set vlans vlan-id " + vlan + " l3-interface vlan" + vlan + ";"
            if self.is_version_4(self.version):
                cmd += "set system inband vlan-interface vlan" + vlan + ";"
                cmd += "set l3-interface vlan-interface vlan" + vlan + " dhcp true;"
            else:
                cmd += "set vlan-interface interface vlan" + vlan + " dhcp true;"
            log_warn("Set vlan interface vlan%s" % vlan)
        for vlan in inband_native_vlan:
            cmd += "set vlans vlan-id " + vlan + " l3-interface vlan" + vlan + ";"
            if self.is_version_4(self.version):
                cmd += "set system inband vlan-interface vlan" + vlan + ";"
                cmd += "set l3-interface vlan-interface vlan" + vlan + " dhcp true;"
            else:
                cmd += "set vlan-interface interface vlan" + vlan + " dhcp true;"
            log_warn("Set vlan interface vlan%s" % vlan)
        cmd += "commit"
        cmd = '/pica/bin/pica_sh -c \"' + cmd + '\"'
        result, rt = subprocess.getstatusoutput(cmd)
        if rt.find("Commit OK") == -1:
            log_warn("Set vlan interface failed: %s" % rt)
            return False

        log_warn("Set vlan members")
        cmd = "configure;"
        for port in self.uplink:
            for vlan in inband_vlan:
                cmd += "set interface gigabit-ethernet " + port + " family ethernet-switching vlan members " + vlan + ";"
            for vlan in inband_native_vlan:
                cmd += "set interface gigabit-ethernet " + port + " family ethernet-switching native-vlan-id " + vlan + ";"
                cmd += "set interface gigabit-ethernet " + port + " family ethernet-switching vlan members " + vlan + " untagged" + ";"
        cmd += "commit"
        cmd = '/pica/bin/pica_sh -c \"' + cmd + '\"'
        result, rt = subprocess.getstatusoutput(cmd)
        if rt.find("Commit OK") == -1:
            log_warn("Set vlan members failed: %s" % rt)
            return False

        if self.inband_lacp:
            log_warn("Enable LACP")
            cmd = "configure;"
            cmd += "set interface aggregate-ethernet ae1 aggregated-ether-options lacp enable true;"
            cmd += "set interface aggregate-ethernet ae1 family ethernet-switching port-mode trunk;"
            for vlan in inband_vlan:
                cmd += "set interface aggregate-ethernet ae1 family ethernet-switching vlan members " + vlan + ";"
            for vlan in inband_native_vlan:
                cmd += "set interface aggregate-ethernet ae1 family ethernet-switching native-vlan-id " + vlan + ";"
                cmd += "set interface aggregate-ethernet ae1 family ethernet-switching vlan members " + vlan + " untagged;"
            for port in self.uplink:
                cmd += "set interface gigabit-ethernet " + port + " ether-options 802.3ad ae1;"
            cmd += "commit"
            cmd = '/pica/bin/pica_sh -c \"' + cmd + '\"'
            result, rt = subprocess.getstatusoutput(cmd)
            if rt.find("Commit OK") == -1:
                log_warn("Enable LACP failed: %s" % rt)
                return False
        return True

    def get_local_ip(self):
        local_ip = ''
        if self.connection_type == 'INBAND':
            result, rt = subprocess.getstatusoutput('ip addr | grep "inet.*vlan"')
        elif self.connection_type == 'OUTOFBAND':
            result, rt = subprocess.getstatusoutput('ip addr | grep "inet.*eth"')
            if not rt:
                result, rt = subprocess.getstatusoutput('ip addr | grep "inet.*inband-mgmt"')
        else:
            return local_ip
        if rt:
            ips = re.findall(r"inet ((?:[0-9]{1,3}\.){3}[0-9]{1,3})", rt)

            if ips:
                local_ip = ips[0]
        return local_ip

    def get_vpn_ip(self):
        vpn_ip = ''
        result, rt = subprocess.getstatusoutput('ip addr | grep "inet.*tun"')
        if rt:
            ips = re.findall(r"inet ((?:[0-9]{1,3}\.){3}[0-9]{1,3})", rt)
            if ips:
                vpn_ip = ips[0]
        return vpn_ip

    def build_outofband_network(self):
        log_warn("Try to use out-of-band network")
        local_gw = ''
        num = 0
        while True:
            log_warn("Getting MGMT IP address")
            local_ip = self.get_local_ip()
            if local_ip:
                log_warn("Got MGMT IP address %s" % local_ip)

                local_gw = ''
                if os.path.exists("/var/lib/dhcp/"):
                    if os.path.exists("/var/lib/dhcp/udhcpc.inband-mgmt.leases"):
                        result, rt = subprocess.getstatusoutput('cat /var/lib/dhcp/udhcpc.inband-mgmt.leases')
                    else:
                        result, rt = subprocess.getstatusoutput('cat /var/lib/dhcp/dhclient.eth*')
                    if rt:
                        all_local_gw = re.findall("option routers ((?:[0-9]{1,3}\.){3}[0-9]{1,3})", rt)
                        if all_local_gw:
                            local_gw = all_local_gw[0]
                            log_warn("Got MGMT local gateway %s" % local_gw)

                    # get domain name
                    local_domain = re.findall('option domain-name "(.+)"', rt)
                    if local_domain:
                        self.server_domain = local_domain[0]
                        self.server_hostname = '%s.%s' % (self.server_hostname_prefix, local_domain[0])
                        self.save_domain_name()
                        log_warn("Got server hostname %s" % self.server_hostname)
                else:
                    result, rt = subprocess.getstatusoutput('ip route | grep default')
                    local_gw = re.search(r'default via ([\d\.]+) dev', rt)[1]

                if local_gw:
                    # get the VPN config
                    if self.vpn_enable:
                        self.server_hostname = '%s.%s' % (self.server_hostname_prefix, self.server_domain)
                        self.save_domain_name()
                        log_warn("Use server hostname %s" % self.server_hostname)
                        self.set_socket_vrf()
                        send_vpn_reg(self.server_vpn_host, self.server_port, self.sn,
                                     local_ip, self.model, self.service_tag)
                        enable_vpn(self.server_vpn_host, self.server_port, self.sn,
                                   self.server_hostname, self.server_vpn_ip)
                        break

                    break

            if num >= 3:
                if not local_ip:
                    log_warn("Cannot get MGMT ip address, change to use inband")
                else:
                    log_warn("Cannot get MGMT gateway, change to use inband")
                return False
            num += 1
            log_warn("Agent will wait %ds" % _wait_time)
            time.sleep(_wait_time)

        # start to install a default gw
        cmd = "sudo route add -net default gw " + local_gw
        result, rt = subprocess.getstatusoutput(cmd)
        log_warn("Add default route | %s | %s" % (cmd, rt))

        # get the DNS
        result, rt = subprocess.getstatusoutput('cat /etc/resolv.conf')
        log_warn("Got the DNS server %s" % rt)

        return True

    def build_inband_config(self):
        if (not self.inband_vlan_config) and (not self.inband_native_vlan_config):
            inband_vlan = self.get_inband_vlan_by_lldp()
            inband_native_vlan = []
        else:
            inband_vlan = self.inband_vlan_config
            inband_native_vlan = self.inband_native_vlan_config
        if inband_vlan:
            log_warn("Got inband vlan %s" % repr(inband_vlan))
        if inband_native_vlan:
            log_warn("Got inband native vlan %s" % repr(inband_native_vlan))

        vlans = []
        native_vlans = []
        for vlan in inband_vlan:
            if vlan not in self.inband_vlan:
                vlans.append(vlan)
        for vlan in inband_native_vlan:
            if vlan not in self.inband_native_vlan:
                native_vlans.append(vlan)
        if (not vlans) and (not native_vlans):
            return True
        if not self.set_vlan_interface(vlans, native_vlans):
            return False

        self.inband_vlan = self.inband_vlan + vlans
        self.inband_native_vlan = self.inband_native_vlan + native_vlans
        return True

    def build_inband_network(self):
        # get the DHCP ip address
        local_gw = ''
        num = 0
        global _alarm_type
        while True:
            log_warn("Getting inband ip")
            local_ip = self.get_local_ip()
            if local_ip:
                log_warn("Got inband IP address %s" % local_ip)
                result, rt = subprocess.getstatusoutput('cat /var/lib/dhcp/dhclient.vlan*')
                if rt:
                    all_local_gw = re.findall("option routers ((?:[0-9]{1,3}\.){3}[0-9]{1,3})", rt)
                    if all_local_gw:
                        local_gw = all_local_gw[0]
                        log_warn("Got inband local gateway %s" % local_gw)
                    if local_gw:
                        # get the VPN config
                        if self.vpn_enable:
                            self.server_hostname = '%s.%s' % (self.server_hostname_prefix, self.server_domain)
                            self.save_domain_name()
                            log_warn("Use server hostname %s" % self.server_hostname)
                            send_vpn_reg(self.server_vpn_host, self.server_port, self.sn, local_ip, self.model,
                                         self.service_tag)
                            enable_vpn(self.server_vpn_host, self.server_port, self.sn, self.server_hostname,
                                       self.server_vpn_ip)
                            break

                        # get domain name
                        local_domain = re.findall('option domain-name "(.+)"', rt)
                        if local_domain:
                            self.server_domain = local_domain[0]
                            self.server_hostname = '%s.%s' % (self.server_hostname_prefix, local_domain[0])
                            self.save_domain_name()
                            log_warn("Got server hostname %s" % self.server_hostname)
                        break

            # blink led
            if num > 0:
                _alarm_type = ALARM_NO_IP
                _event.set()
            if num > 1:
                log_warn("Failed to get inband ip")
                return False
            num += 1
            log_warn("Agent will wait %ds" % _wait_time)
            time.sleep(_wait_time)

        _alarm_type = ALARM_CONNECTING
        _event.set()
        # start to install a default gw in Linux
        cmd = "sudo route add -net default gw " + local_gw
        result, rt = subprocess.getstatusoutput(cmd)
        log_warn("Add default route | %s | %s" % (cmd, rt))

        # get the DNS
        result, rt = subprocess.getstatusoutput('cat /etc/resolv.conf')
        log_warn("Got the DNS server %s" % rt)
        return True

    def notify_fail_to_server(self, task, reason):
        log_warn("Notify deploy fail to server")

        if self.vpn_enable:
            self.check_vpn_route()

        # get server IP
        try:
            if self.vpn_enable:
                server_ip = self.server_vpn_host
            else:
                server_ip = socket.getaddrinfo(self.server_hostname, self.server_port, 2, 0, socket.SOL_TCP)[0][4][0]
        except:
            log_warn("Resolve server hostname [%s] failed" % self.server_hostname)
            return False

        url = "https://" + server_ip + ':' + self.server_port + '/deploy/failed'
        data = json.dumps({"sn": self.sn, "task_name": task, "reason": reason}).encode('utf-8')
        try:
            self.set_socket_vrf()
            req = urllib.request.Request(url, data, {'Content-Type': 'application/json'})
            f = urllib.request.urlopen(req, timeout=5)
            res = f.read()
            if type(res) == bytes:
                res = res.decode('utf-8')
            f.close()
            log_warn("Received reply message: %s" % res[:15])
        except urllib.error.URLError as e:
            log_warn("Failed to notify server: %s" % e)
            return False

        return True

    def register_in_server(self):
        log_warn("Registering in server")
        res = ''

        if self.vpn_enable:
            self.check_vpn_route()

        self.wait_register_time = self.probe_interval
        # get server IP
        try:
            if self.vpn_enable:
                server_ip = self.server_vpn_host
                sync_time_without_ntp(server_ip, self.server_port)
            else:
                server_ip = socket.getaddrinfo(self.server_hostname, self.server_port, 2, 0, socket.SOL_TCP)[0][4][0]

        except:
            log_warn("Resolve server hostname [%s] failed" % self.server_hostname)
            return False, res

        # get current local IP
        if not self.connection_type:
            self.connection_type = 'OUTOFBAND'
            local_ip = self.get_local_ip()
            if not local_ip:
                self.connection_type = 'INBAND'
                local_ip = self.get_local_ip()
                if not local_ip:
                    self.connection_type = ''
                    log_warn("Got local ip failed")
                    return False, res
        else:
            local_ip = self.get_local_ip()
            if not local_ip:
                log_warn("Got local ip failed")
                return False, res

        mgmt_flag = '0' if self.connection_type == 'INBAND' else '1'
        url = "https://" + server_ip + ':' + self.server_port + '/reg/' + \
              self.sn + ';' + local_ip + ';' + self.model + ';' + \
              self.hw_id + ';' + str(self.state) + ';' + mgmt_flag
        if self.vpn_enable:
            vpn_ip = self.get_vpn_ip()
            mgmt_flag = '2'
            url = "https://" + server_ip + ':' + self.server_port + '/reg/' + \
                  self.sn + ';' + vpn_ip + ';' + self.model + ';' + \
                  self.hw_id + ';' + str(self.state) + ';' + mgmt_flag
        if self.service_tag:
            url = url + ';' + self.service_tag

        check_work_path(_auto_dir)
        log_warn("Sending request url %s" % url)
        try:
            self.set_socket_vrf()
            req = urllib.request.Request(url)
            f = urllib.request.urlopen(req, timeout=30)
            res = f.read()
            if type(res) == bytes:
                res = res.decode('utf-8')
            f.close()
            log_warn("Received reply message: %s" % res)
        except urllib.error.HTTPError as e:
            res = e.read()
            log_warn("Failed to register in server: %s, %s" % (e, res))
            e.close()
            return False, res
        except urllib.error.URLError as e:
            log_warn("Failed to register in server: %s" % e)
            return False, res

        return True, res

    def process_stage_init(self):
        log_warn("Agent Initialized")
        global _alarm_type
        _alarm_type = ALARM_CONNECTING
        _event.set()

        self.n_try += 1
        self.change_bashrc()
        if self.n_try > self.backoff:
            # Max try times reached, exit with error log
            self.state = STATE_MAX_FAILS
            self.stage = 'Exit'
            return

        do_enable_vpn = True
        sleep_time = 0
        status = self.agent_status()
        if status == STATUS_DEPLOYED:
            # This agent should only be executed one time
            self.stage = 'Exit'
            if self.n_try != 1:
                do_enable_vpn = False
        elif status == STATUS_NO_FLAG:
            self.stage = 'Connecting'
            self.state = STATE_FIRST_REGISTER
            do_enable_vpn = False
        elif status == STATUS_DEPLOYING:
            self.stage = 'Connected'
            self.state = STATE_CONFIGURED
            self.clean_deployed_flag()
            sleep_time = _wait_time
        elif status == STATUS_LOAD_CONFIG:
            self.stage = 'Connected'
            self.state = STATE_NOT_CONFIGURED
        elif status == STATUS_LOAD_RMA_CONFIG:
            self.stage = 'Connected'
            self.state = STATE_NOT_CONFIGURED
        elif status == STATUS_UPGRADING:
            self.stage = 'Connected'
            self.state = STATE_UPGRADING
            if check_rollback(self.need_save_config):
                self.set_deployed_flag("deployed")
                self.stage = 'Exit'

        if do_enable_vpn and self.vpn_enable:
            count = 0
            self.set_socket_vrf()
            while not enable_vpn(self.server_vpn_host, self.server_port, self.sn, self.server_hostname,
                                 self.server_vpn_ip):
                count += 1
                if count > 9:
                    # fix the issue when deployed while power-circle without internet access
                    if self.stage == 'Exit':
                        log_warn("Failed to enable vpn, agent will quit with deployed flag")
                        sync_time_ntp()
                        restart_openvpn()
                        time.sleep(5)
                        break
                    elif status == STATUS_UPGRADING:
                        log_warn("Agent is upgrading, but failed to enable vpn")
                        self.stage = 'Exit'
                        self.state = STATE_UPGRADING
                        # self.clean_deployed_flag()
                        self.set_deployed_flag("deployed")
                        self.rollback()
                        break
                    else:
                        self.stage = 'Connecting'
                        self.state = STATE_FIRST_REGISTER
                        log_warn("Failed to enable vpn, agent will reinitialize")
                        break
                time.sleep(10)
        if sleep_time:
            time.sleep(sleep_time)

    def process_stage_connecting(self):
        log_warn("This switch is not deployed, agent will start to connect server")

        global _alarm_type
        while True:
            if not self.load_default_config():
                break

            os.system('echo "admin:pica8" | sudo chpasswd')
            os.system("sudo rm -rf %s" % _ansible_tmp)

            self.connection_type = 'OUTOFBAND'
            self.set_socket_vrf()
            if not self.build_outofband_network():
                self.connection_type = 'INBAND'
                self.set_socket_vrf()
                self.inband_vlan = []
                self.inband_native_vlan = []
                if not self.enable_lldp_inband():
                    break
                if not self.set_uplink_trunk():
                    break
                time.sleep(_wait_time)  # wait 30s for LLDP to receive packets
                config_fail = False
                while True:
                    if not self.build_inband_config():
                        config_fail = True
                        break
                    if self.build_inband_network():
                        break
                if config_fail:
                    break

            num = 0
            while True:
                ret, res = self.register_in_server()
                if ret:
                    log_warn("Agent has already connected to server")
                    log_warn("Agent will wait %ds" % self.probe_interval)
                    self.stage = 'Connected'
                    self.state = STATE_NOT_CONFIGURED
                    self.connected = True
                    self.fail = 0
                    _alarm_type = ALARM_CONNECTED
                    _event.set()
                    return

                # blink led
                if num > 0:
                    _alarm_type = ALARM_SERVER_UNREACHABLE
                    _event.set()
                num += 1
                log_warn("Agent will wait %ds" % _wait_time)
                time.sleep(_wait_time)
                if num > 10:
                    log_warn("Agent has tried 10 times connecting server, load default and try again")
                    break

        log_warn("Agent will wait %ds" % _wait_time)
        time.sleep(_wait_time)

    def process_stage_connected(self):
        global _alarm_type
        _alarm_type = ALARM_CONNECTED
        _event.set()

        status = self.agent_status()
        if status == STATUS_DEPLOYED:
            self.stage = 'Exit'
            self.state = STATE_DEPLOYED
            return
        elif status == STATUS_LOAD_CONFIG:
            self.clean_deployed_flag()
            if not self.load_user_config():
                log_warn("Failed to load user config, agent will restart")
                self.stage = 'Init'
                return

            self.wait_register_time = 30
            self.state = STATE_CONFIGURED
        elif status == STATUS_LOAD_RMA_CONFIG:
            self.clean_deployed_flag()
            if not self.load_rma_config():
                log_warn("Failed to load rma config, agent will restart")
                self.stage = 'Init'
                return
            self.wait_register_time = 30
            self.state = STATE_CONFIGURED
        else:
            if self.wait_register_time > 0:
                time.sleep(10)
                self.wait_register_time -= 10
                return

            ret, res = self.register_in_server()
            if ret:
                log_warn("Agent has already connected to server")
                self.connected = True
                self.fail = 0

                if status == STATUS_UPGRADING and 'upgrade failed' in res:
                    log_warn("Upgrade failed in server")
                    self.stage = 'Exit'
                    self.state = STATE_UPGRADING
                    # self.clean_deployed_flag()
                    self.set_deployed_flag("deployed")
                    self.rollback()
                    return
            else:
                log_warn("Agent has been disconnected from server")
                self.connected = False
                self.fail += 1
                if self.fail >= self.max_retry:
                    if status == STATUS_UPGRADING:
                        log_warn("Agent is upgrading, but cannot connect to server")
                        self.stage = 'Exit'
                        self.state = STATE_UPGRADING
                        # self.clean_deployed_flag()
                        self.set_deployed_flag("deployed")
                        self.rollback()
                        return

                    self.stage = 'Init'
                    self.clean_deployed_flag()
                    return

            log_warn("Agent will wait %ds" % self.probe_interval)

    def process_stage_exit(self):
        self.restore_bashrc()
        self.do_save_config()
        global _alarm_type
        if self.state == STATE_MAX_FAILS:
            log_warn("Max try times reached")
            log_warn("This switch has not been deployed, agent will quit")
            _alarm_type = ALARM_EXIT_FAIL
        elif self.state == STATE_UPGRADING:
            log_warn("Agent will quit")
            _alarm_type = ALARM_EXIT_SUCCESS
        else:
            log_warn("This switch has been already deployed, agent will quit")
            _alarm_type = ALARM_EXIT_SUCCESS
        _event.set()
        unlock_agent()

    def run(self):
        try:
            while True:
                if self.stage == 'Init':
                    self.process_stage_init()
                elif self.stage == 'Connecting':
                    self.process_stage_connecting()
                elif self.stage == 'Connected':
                    self.process_stage_connected()
                elif self.stage == 'Exit':
                    self.process_stage_exit()
                    break
        except Exception as exce:
            log_warn("Run with exception %s" % str(exce))
            unlock_agent()
            os._exit(0)


if __name__ == '__main__':
    # Agent should be called by root
    _logger = logger_init()
    check_enable()
    log_warn("Auto-deploy Agent Start")
    signal.signal(signal.SIGINT, sigint_handler)
    signal.signal(signal.SIGTERM, sigint_handler)
    check_already_running()
    time.sleep(10)
    check_work_path(_auto_dir)
    check_work_path(_deploy_dir)
    check_mgmt_vrf()
    sync_time_ntp()

    try:
        agent_alarm = threading.Thread(target=alarm_led)
        agent_ins = DeployAgent()
        agent_alarm.setDaemon(True)
        agent_ins.setDaemon(True)
        agent_alarm.start()
        agent_ins.start()
    except Exception as e:
        log_warn("Exit with exception %s" % str(e))
        unlock_agent()
        print(e)
        sys.exit(1)

    while True:
        time.sleep(100)
