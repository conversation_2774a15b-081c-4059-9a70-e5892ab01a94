import logging
import os
import socket
from OpenSSL import crypto, SSL
from server.db.models.inventory import Switch, VpnConfig
from server.db.models.inventory import inven_db


LOG = logging.getLogger(__name__)

ca_crt_path = "/etc/openvpn/server/ca.crt"
ca_key = "/etc/openvpn/server/ca.key"
key_info = """V       280914185304Z           01      unknown /C=US/ST=CA/L=SanFrancisco/O=Pica8 Inc/OU=automation/CN={name}/name=EasyRSA/emailAddress=<EMAIL>"""


def create_vpn_client(name):
    vpn_config = inven_db.get_model(VpnConfig, filters={'sn': [name]})
    if vpn_config:
        # already create vpn keys, check keys exists
        if vpn_config.client_key and vpn_config.client_key != '' \
           and vpn_config.client_crt and vpn_config.client_crt != '' \
           and vpn_config.ca_crt and vpn_config.ca_crt != '':
            return

    common = key_info.format(name=name)
    client_crt, client_key, ca_crt = create_openvpn_client(ca_crt_path, ca_key, name, 0x0C, common)
    # save to db
    client_vpn = VpnConfig()
    client_vpn.sn = name
    client_vpn.client_key = client_key
    client_vpn.client_crt = client_crt
    client_vpn.ca_crt = ca_crt
    inven_db.insert_or_update(client_vpn, primary_key='sn')


# Create a new keypair of specified algorithm and number of bits.
def make_keypair(algorithm=crypto.TYPE_RSA, numbits=2048):
    pkey = crypto.PKey()
    pkey.generate_key(algorithm, numbits)
    return pkey


# Creates a certificate signing request (CSR) given the specified subject attributes.
def make_csr(pkey, CN, C=None, ST=None, L=None, O=None, OU=None, emailAddress=None, hashalgorithm='sha256WithRSAEncryption'):
    req = crypto.X509Req()
    req.get_subject()
    subj = req.get_subject()

    if C:
        subj.C = C
    if ST:
        subj.ST = ST
    if L:
        subj.L = L
    if O:
        subj.O = O
    if OU:
        subj.OU = OU
    if CN:
        subj.CN = CN
    if emailAddress:
        subj.emailAddress = emailAddress

    req.set_pubkey(pkey)
    req.sign(pkey, hashalgorithm)
    return req


# Create a certificate authority (if we need one)
def create_ca(CN, C="", ST="", L="", O="", OU="", emailAddress="", hashalgorithm='sha256WithRSAEncryption'):
    cakey = make_keypair()
    careq = make_csr(cakey, cn=CN)
    cacert = crypto.X509()
    cacert.set_serial_number(0)
    cacert.gmtime_adj_notBefore(0)
    cacert.gmtime_adj_notAfter(60*60*24*365*10) # 10 yrs - hard to beat this kind of cert!
    cacert.set_issuer(careq.get_subject())
    cacert.set_subject(careq.get_subject())
    cacert.set_pubkey(careq.get_pubkey())
    cacert.set_version(2)

    # Set the extensions in two passes
    cacert.add_extensions([
        crypto.X509Extension('basicConstraints', True,'CA:TRUE'),
        crypto.X509Extension('subjectKeyIdentifier' , True , 'hash', subject=cacert)
    ])

    # ... now we can set the authority key since it depends on the subject key
    cacert.add_extensions([
        crypto.X509Extension('authorityKeyIdentifier' , False, 'issuer:always, keyid:always', issuer=cacert, subject=cacert)
    ])

    cacert.sign(cakey, hashalgorithm)
    return (cacert, cakey)


# Create a new slave cert.
def create_slave_certificate(csr, cakey, cacert, serial):
    cert = crypto.X509()
    cert.set_serial_number(serial)
    cert.gmtime_adj_notBefore(0)
    cert.gmtime_adj_notAfter(60*60*24*365*10) # 10 yrs - hard to beat this kind of cert!
    cert.set_issuer(cacert.get_subject())
    cert.set_subject(csr.get_subject())
    cert.set_pubkey(csr.get_pubkey())
    cert.set_version(2)

    extensions = []
    extensions.append(crypto.X509Extension(b'basicConstraints', False, b'CA:FALSE'))

    extensions.append(crypto.X509Extension(b'subjectKeyIdentifier', False, b'hash', subject=cert))
    extensions.append(crypto.X509Extension(b'authorityKeyIdentifier', False, b'keyid:always,issuer:always', subject=cacert, issuer=cacert))

    cert.add_extensions(extensions)
    cert.sign(cakey, 'sha256WithRSAEncryption')

    return cert


# Dumps content to a string
def dump_file_in_mem(material, format=crypto.FILETYPE_PEM):
    dump_func = None
    if isinstance(material, crypto.X509):
        dump_func = crypto.dump_certificate
    elif isinstance(material, crypto.PKey):
        dump_func = crypto.dump_privatekey
    elif isinstance(material, crypto.X509Req):
        dump_func = crypto.dump_certificate_request
    else:
        raise Exception("Don't know how to dump content type to file: %s (%r)" % (type(material), material))

    return dump_func(format, material)


# Loads the file into the appropriate openssl object type.
def load_from_file(materialfile, objtype, format=crypto.FILETYPE_PEM):
    if objtype is crypto.X509:
        load_func = crypto.load_certificate
    elif objtype is crypto.X509Req:
        load_func = crypto.load_certificate_request
    elif objtype is crypto.PKey:
        load_func = crypto.load_privatekey
    else:
        raise Exception("Unsupported material type: %s" % (objtype,))

    with open(materialfile, 'r') as fp:
        buf = fp.read()

    material = load_func(format, buf)
    return material


def retrieve_key_from_file(keyfile):
    return load_from_file(keyfile, crypto.PKey)


def retrieve_csr_from_file(csrfile):
    return load_from_file(csrfile, crypto.X509Req)


def retrieve_cert_from_file(certfile):
    return load_from_file(certfile, crypto.X509)


def create_openvpn_client(ca_cert, ca_key, clientname, serial, common):
    # common should be "V       280914185304Z           01      unknown /C=US/ST=CA/L=SanFrancisco/O=Pica8 Inc/OU=server/CN=server/name=EasyRSA/emailAddress=<EMAIL>"
    cacert = retrieve_cert_from_file(ca_cert)
    cakey  = retrieve_key_from_file(ca_key)

    # Generate a new private key pair for a new certificate.
    key = make_keypair()
    # Generate a certificate request
    csr = make_csr(key, clientname)
    # Sign the certificate with the new csr
    crt = create_slave_certificate(csr, cakey, cacert, serial)

    # Now we have a successfully signed certificate.
    clientkey  = dump_file_in_mem(key)
    clientcert = dump_file_in_mem(crt)
    cacertdump = dump_file_in_mem(cacert)
    return clientcert, clientkey, cacertdump
