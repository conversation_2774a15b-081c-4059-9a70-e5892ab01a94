#!/bin/sh
sleep 10
vpn_ip="********"
config_file_path="/opt/auto-deploy/auto-deploy.conf"
# get vpn_ip from /opt/auto-deploy/auto-deploy.conf
server_vpn_output=`grep -o -P '(?<=server_vpn_ip =).*\b' ${config_file_path} | sed 's/[ =]//g'`
if [ ! "$server_vpn_output" = "" ]; then
   vpn_ip=$server_vpn_output
fi

#send reg
sn=`cat /sys/class/swmon/hwinfo/serial_number`
if [ $? -ne 0 ]; then
  sn=`hostname`
fi
tun_ip=`ip addr show tun0 | grep 'POINTOPOINT' -A2 | tail -n1 | awk '{print $2}' | cut -f1  -d'/'`
url="https://${vpn_ip}:443/vpn_update_ip/"$sn";"$tun_ip
if [ -n "$1" ]; then
    sudo ip vrf exec $1 curl -k $url
else
    curl -k $url
fi
