{"account": {"account": "Ko<PERSON>", "activating_google_authenticator": "Aktivierung von Google Authenticator", "activating_sms_mfa": "Validierung der Telefonnummer", "avatar": "Benutzerbild", "error_fetching_qr": "Fehler beim Abrufen des QR-Codes: {{e}}", "error_phone_verif": "Fehler bei Ihrem <PERSON>scode, bitte versuchen Sie es erneut.", "google_authenticator": "Google Authenticator", "google_authenticator_intro": "Um Google Authenticator als doppelte Authentifizierungsmethode für Ihr Konto zu verwenden, müssen Si<PERSON> zu<PERSON>t die App auf Ihrem iOS- oder Android-Gerät installieren", "google_authenticator_ready": "<PERSON>bald Sie die App einsatzbereit haben, können Si<PERSON> fortfahren", "google_authenticator_scan_qr_code_explanation": "Scannen Sie den folgenden QR-Code mit „Scannen Sie einen QR-Code“ in der Google Authenticator-App", "google_authenticator_scanned_qr_code": "Sobald der QR-Code erfolgreich auf Ihrem Telefon gescannt wurde, können Si<PERSON> mit dem nächsten Schritt fortfahren", "google_authenticator_success_explanation": "Sie haben Google Authenticator jetzt erfolgreich mit Ihrem Konto eingerichtet. Vergessen <PERSON> nicht, Ihre Änderungen zu speichern, um sie zu bestätigen!", "google_authenticator_type_code": "<PERSON>te geben Sie unten den 6-stelligen Code aus Ihrer Google Authenticator-App ein", "google_authenticator_wait_for_code": "Auf den nächsten Code warten (nicht {{old}})", "google_authenticator_wrong_code": "Ungültiger Code! Bitte versuchen Sie es erneut oder warten <PERSON>, bis der nächste Code in der Google Authenticator-App generiert wird", "mfa": "Multi-Faktor-Authentifizierung", "phone": "Telefon", "phone_number": "Telefonnummer", "phone_number_add_introduction": "Bitte geben Sie die Telefonnummer ein, die Si<PERSON> verwenden möchten, um Ihr Konto bei der Anmeldung zu sichern", "phone_required": "Um die SMS-Verifizierung zu aktivieren, müssen Sie eine Telefonnummer eingeben", "phone_validation_success_explanation": "Telefonnummer erfolgreich verifiziert! Klicken Sie auf „Speichern“, um diese Telefonnummer zu Ihrem Konto hinzuzufügen", "proceed_to_activation": "Aktivierungsprozess starten", "resend": "<PERSON><PERSON><PERSON> senden", "sms": "SMS", "success_phone_verif": "Telefonnummer erfolgreich verifiziert! Sie können Ihr Profil jetzt speichern", "title": "<PERSON><PERSON>", "verify_phone_instructions": "<PERSON>e sollten in den nächsten Sekunden einen Code für Ihre Telefonnummer erhalten. Bitte geben Sie es unten ein, um Ihre Telefonnummer zu bestätigen", "verify_phone_number": "Bestätige deine Telefonnummer"}, "analytics": {"ack_signal": "ACK-Signal", "active": "Aktiv", "airtime": "Sendezeit", "analyze_sub_venues": "Überwachen Sie untergeordnete Veranstaltungsorte", "associations": "Verbände", "associations_explanation": "Assoziationen insgesamt", "average_health": "Allgemeine Gesundheit", "average_health_explanation": "Durchschnittliche Unversehrtheit aller angeschlossenen Geräte, die die Zustandsprüfungsinformationen bereitstellen", "average_memory": "Verwendeter Speicher", "average_memory_explanation": "Durchschnittlicher Prozentsatz des verwendeten Speichers", "average_uptime": "Durchschnittliche Betriebszeit", "average_uptime_explanation": "Durchschnittliche Gerätebetriebszeit (DD:HH:MM:SS)", "band": "Band", "bandwidth": "Bandbreite", "board": "Analytics-Sammlung", "busy": "Beschäftigt", "channel": "<PERSON><PERSON>", "client_lifecycle": "Client-Lebenszyklus", "client_lifecycle_one": "{{count}} Clientlebenszyklus", "client_lifecycle_other": "{{count}} Clientlebenszyklen", "connected": "In Verbindung gebracht", "connection_explanation": "{{connectedCount}} verbunden, {{disconnectedCount}} nicht verbunden", "connection_percentage": "{{count}} % verbunden", "connection_percentage_explanation": "Prozentsatz aller Geräte unter diesem Veranstaltungsort, die verbunden sind ({{connectedCount}} verbunden, {{disconnectedCount}} nicht verbunden)", "create_board": "Überwachung starten", "dashboard": "Instrumententafel", "delta": "Delta", "device_types": "Typen", "device_types_explanation": "Gerätetypen aller verfügbaren Geräte", "disconnected": "Getrennt", "firmware": "Firmware", "health": "Gesundheit", "inactive": "Inaktiv", "interval": "Intervall", "last_connected": "Zuletzt verbunden", "last_connected_tooltip": "Das letzte Mal, wann dieses Gerät mit dem Controller verbunden war. Dies kann verwendet werden, um abzuschätzen, wann ein Gerät getrennt wurde", "last_connection": "Letzte Verbindung", "last_contact": "Letzter Kontakt", "last_disconnection": "Letzte Trennung", "last_firmware_explanation": "Die gängigste Firmware, die auf den analysierten Geräten ausgeführt wird", "last_health": "Letzte Gesundheit", "last_ping": "<PERSON><PERSON><PERSON>", "live_view": "Liveübertragung", "live_view_explanation_five": "<PERSON><PERSON> können auch auf einen der Kreise klicken, um hineinzuzoomen", "live_view_explanation_four": "<PERSON><PERSON> können mit der Maus über jedes der Objekte fahren, um detaillierte Informationen anzuzeigen", "live_view_explanation_one": "Das 'Live View'-Diagramm ist eine visuelle Darstellung Ihres Veranstaltungsortes.", "live_view_explanation_three": "Veranstaltungsort -> AP -> Radio -> SSID -> UEs", "live_view_explanation_two": "Von außen nach innen:", "live_view_help": "<PERSON><PERSON><PERSON> zur Live-Ansicht", "memory": "Erinnerung", "memory_used": "Verwendeter Speicher", "missing_board": "Analytics-Überwachung an diesem Ort ist nicht mehr aktiv. Klicken <PERSON> hier, um die Überwachung neu zu starten", "mode": "Modus", "monitoring": "Überwachung", "no_board": "<PERSON><PERSON>", "no_board_description": "Sie überwachen diesen Veranstaltungsort derzeit nicht, klicken <PERSON><PERSON> hier, um zu beginnen", "noise": "L<PERSON>rm", "packets": "<PERSON><PERSON>", "radio": "RADIO", "raw_analytics_data": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raw_data": "<PERSON><PERSON><PERSON><PERSON>", "receive": "<PERSON><PERSON><PERSON><PERSON>", "retention": "Zurückbehaltung", "retries": "Wiederholungen", "search_serials": "Zeitschriften suchen", "stop_monitoring": "Beenden Sie die Überwachung", "stop_monitoring_success": "Überwachungsort gestoppt!", "stop_monitoring_warning": "Bist du sicher? Dad<PERSON>ch werden alle aufgezeichneten Überwachungsdaten für diesen Veranstaltungsort gelöscht", "temperature": "Temperatur", "title": "ANALYTICS", "total_data": "Gesamtdaten", "total_devices_explanation": "Alle Geräte unter diesem Veranstaltungsort ({{connectedCount}} verbunden, {{disconnectedCount}} nicht verbunden)", "total_devices_one": "{{count}} <PERSON><PERSON><PERSON>", "total_devices_other": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "uptime": "Betriebszeit"}, "batch": {"batches": "Chargen", "cannot_edit_macs": "Da für diesen Stapel bereits Jobs ausgeführt wurden, können Sie seine MAC-Adressen nicht bearbeiten", "change_warning": "WARNUNG: <PERSON><PERSON> haben entweder das Modell oder den Hersteller aktualisiert. Wir empfehlen dringend, Ihre Zertifikate zu aktualisieren, damit sie mit diesem Batch konsistent bleiben, indem Sie die Option „Zertifikate speichern und aktualisieren“ auswählen", "create": "Zertifikate erstellen", "create_certificates": "Zertifikate erstellen", "create_certificates_explanation": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> die {{nbCerts}} -<PERSON>ertifika<PERSON> dieses Stapels wirklich erstellen?", "create_certificates_title": "<PERSON><PERSON><PERSON><PERSON> Sie die Zertifikate von {{name}}", "delete_explanation": "<PERSON><PERSON>cht<PERSON> Sie diesen Batch wirklich löschen? Dad<PERSON>ch werden alle seine {{nbCerts}} -Zertifikate widerrufen und gelöscht. Dieser Vorgang kann nicht rückgängig gemacht werden", "delete_title": "Stapel {{name}}löschen", "duplicate_in_file": "Doppelter MAC in Zeile {{firstRow}} und {{secondRow}}gefunden: {{mac}}", "emails_to_notify": "E-Mails zur Benachrichtigung, wenn diese Aufgabe abgeschlossen ist", "error_push": "Fehler beim Starten des Push-Änderungsjobs: {{e}}", "general_error_treating_file": "Allgemeiner Fehler beim Behandeln der Datei: <PERSON>te stellen Si<PERSON> sicher, dass sie im .CSV-Format vorliegt und nur eine Spalte ohne Kopfzeile enthält.", "invalid_mac": "Ungültiger MAC in Zeile {{row}}: {{mac}}", "mac_count_title": "{{nb}} MACs sind derzeit Teil dieses Stapels", "nb_macs": "{{nb}} MACs", "need_devices": "Sie müssen mindestens ein Zertifikat erstellen!", "parsing_error": "Parsing-<PERSON>hler in Zeile {{row}}: {{e}}", "phones_to_notify": "Telefonnummern, die benachrichtigt werden sollen, wenn die Aufgabe abgeschlossen ist", "push_changes": "Push-Änderungen", "push_changes_explanation": "Möchten Sie die Stapelinformationen wirklich auf alle Zertifikate dieses Stapels übertragen?", "revoke_explanation": "M<PERSON>cht<PERSON> Sie diesen Batch wirklich widerrufen? Dadurch werden alle seine {{nbCerts}} -Zertifikate widerrufen. Dieser Vorgang kann nicht rückgängig gemacht werden", "revoke_title": "Charge {{name}}widerrufen", "save_and_change": "Zertifikate speichern und aktualisieren", "success_push": "Push-Änderungsjob erfolgreich gestartet! Auftragsnummer: {{job}}", "title": "<PERSON><PERSON><PERSON>"}, "certificates": {"certificate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "common_names_explanation": "Benötigen Sie eine .CSV-Datei mit nur einer unbenannten Spalte mit 12 HEX-Ziffern für Geräte-MACs.", "device": "G<PERSON><PERSON>", "device_macs": "Geräte-MACs", "domain_name": "Domänenname", "error_fetching": "Fe<PERSON> beim Abrufen der Zertifikate: {{e}}", "error_revoke": "<PERSON><PERSON> beim <PERSON>, das Zertifikat zu widerrufen: {{e}}", "expires_on": "Läuft aus am", "filename": "Dateiname", "invalid_domain": "Akzeptierte Formate sind: domain.domain_der obersten_ebene oder sub_domain.domain._domain_der_obersten_ebene", "invalid_mac": "Muss aus 12 HEX-Zeichen bestehen", "invalid_redirector": "Akzeptierte Formate sind: example.com, example.com:16000", "mac_address": "MAC-Adresse", "macs": "MACs", "manufacturer": "<PERSON><PERSON><PERSON>", "model": "<PERSON><PERSON>", "redirector": "Umleitung", "revoke": "Widerrufen", "revoke_count": "Zählung widerrufen", "revoke_warning": "Möchten Sie dieses Zertifikat wirklich widerrufen?", "server": "Server", "successful_revoke": "Zertifikat erfolgreich widerrufen!", "title": "Zertifikate"}, "commands": {"abort_command_explanation": "<PERSON><PERSON>cht<PERSON> Sie wirklich nicht mehr auf das Ergebnis dieses Befehl<PERSON> warten?", "abort_command_title": "<PERSON><PERSON><PERSON> abbrechen", "active_scan": "Aktiver Scan", "blink": "Blinken", "blink_error": "<PERSON><PERSON> beim Senden des Blinkbefehls: {{e}}", "blink_success": "Blinkbefehl erfolgreich gesendet!", "channel": "<PERSON><PERSON>", "confirm_reset": "<PERSON><PERSON><PERSON><PERSON><PERSON> von Gerät Nr.{{serialNumber}}starten", "connect": "Verbinden", "rtty": "RTTY", "execution_time": "Ausführungszeit", "factory_reset": "Gerät auf Werkseinstellungen zurücksetzen", "factory_reset_error": "<PERSON><PERSON> beim V<PERSON>uch, das Gerät auf die Werkseinstellungen zurückzusetzen: {{e}}", "factory_reset_success": "Werksreset des Geräts erfolgreich gestartet!", "factory_reset_warning": "Hin<PERSON>s: Möcht<PERSON> Sie dieses Gerät wirklich auf die Werkseinstellungen zurücksetzen? Diese Aktion ist nicht umkehrbar", "firmware_upgrade": "Firmware-Aktualisierung", "firmware_upgrade_error": "<PERSON><PERSON> beim V<PERSON>uch, die Gerätefirmware zu aktualisieren: {{e}}", "firmware_upgrade_success": "Geräte-Upgrade erfolgreich gestartet!", "image_date": "Bilddatum", "keep_redirector": "Redirector beibe<PERSON>en?", "other": "<PERSON><PERSON><PERSON><PERSON>", "override_dfs": "DFS überschreiben", "reboot": "Starten Sie neu", "reboot_description": "<PERSON><PERSON>chten Sie dieses Gerät neu starten?", "reboot_error": "Fehler beim Senden des Neustartbefehls: {{e}}", "reboot_success": "Neustartbefehl erfolgreich gesendet!", "revision": "Revision", "scan": "<PERSON><PERSON>", "signal": "Signal", "upgrade": "Aktualisierung", "wifiscan": "WLAN-Scan", "wifiscan_error": "<PERSON><PERSON> beim <PERSON>, das Gerät zu scannen: {{e}}", "wifiscan_error_1": "Ihr 5G-Funkgerät befindet sich auf einem Radarkanal, <PERSON><PERSON> <PERSON>ü<PERSON> „Override DFS“ aktivieren, um das Scannen aller 5G-Kan<PERSON>le zu ermöglichen"}, "common": {"actions": "Aktionen", "address_search_autofill": "<PERSON>en Sie Standorte, um die Felder unten automatisch auszufüllen", "alert": "<PERSON><PERSON>", "all": "Alles", "assign": "<PERSON><PERSON><PERSON><PERSON>", "avg": "Durchschn", "back": "Zurück", "base_information": "Basisinformationen", "by": "Durch", "cancel": "Stornieren", "claim": "<PERSON><PERSON><PERSON><PERSON>", "close": "Schließen", "columns": "S<PERSON>ulen", "command": "<PERSON><PERSON><PERSON>", "completed": "Abgeschlossen", "confirm": "Bestätigen", "connected": "In Verbindung gebracht", "copied": "<PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON>", "create_new": "<PERSON><PERSON><PERSON> neu", "created": "<PERSON><PERSON><PERSON><PERSON>", "creator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON>", "daily": "Tä<PERSON><PERSON>", "date": "Datum", "day": "Tag", "days": "Tage", "default": "Standard", "defaults": "Standardeinstellungen", "description": "Beschreibung", "details": "Einzelheiten", "device_details": "G<PERSON><PERSON><PERSON><PERSON>", "discard_changes": "Änderungen verwerfen?", "disconnected": "Getrennt", "display_name": "Anzeigename", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "download_instructions": "Herunterladen erfolgreich! Wenn Sie die Datei nicht finden können, bestätigen <PERSON>, dass Sie Downloads von dieser Website zulassen", "duplicate": "Duplikat", "edit": "<PERSON><PERSON><PERSON>", "email": "Email", "empty_list": "<PERSON><PERSON>", "end": "<PERSON><PERSON>", "entries_one": "Eintrag", "entries_other": "Einträge", "error": "Error", "error_claiming_obj": "<PERSON><PERSON> beim <PERSON> {{obj}}", "error_download": "<PERSON><PERSON> beim <PERSON>: {{e}}", "errors": "<PERSON><PERSON>", "exit_fullscreen": "Ausgang", "export": "Export", "finished": "<PERSON><PERSON><PERSON>", "fullscreen": "Vollbildschirm", "general_error": "Fehler beim Verbinden mit dem Server. Bitte wenden Si<PERSON> sich an Ihren Administrator.", "general_info": "Allgemeine Information", "go_back": "Geh zurück", "go_to_map": "<PERSON><PERSON><PERSON> Si<PERSON>", "hide": "verbergen", "hourly": "<PERSON><PERSON><PERSON><PERSON>", "identification": "Identifizierung", "inherit": "<PERSON><PERSON><PERSON>", "language": "<PERSON><PERSON><PERSON>", "last_use": "Zuletzt verwendeten", "lifetime": "Lebenszeit", "locale": "Gebietsschema", "logout": "Ausloggen", "main": "Main", "make_higher_priority": "<PERSON><PERSON><PERSON> eine höhere Priorität ein", "make_lower_priority": "Niedrigere Priorität setzen", "manage": "<PERSON><PERSON><PERSON><PERSON>", "manual": "Handbuch", "manufacturer": "<PERSON><PERSON><PERSON>", "map": "<PERSON><PERSON>", "max": "Max", "min": "MINDEST", "miscellaneous": "Verschiedenes", "mode": "Modus", "model": "<PERSON><PERSON>", "modified": "G<PERSON><PERSON>ndert", "monthly": "<PERSON><PERSON><PERSON>", "months": "Monate", "my_account": "<PERSON><PERSON>", "name": "Name", "name_error": "Der Name muss weniger als 50 Zeichen lang sein", "next": "Nächster", "no": "<PERSON><PERSON>", "no_addresses_found": "<PERSON><PERSON> gefunden", "no_clients_found": "<PERSON><PERSON> gefunden", "no_devices_found": "<PERSON><PERSON> G<PERSON> gefunden", "no_items_yet": "<PERSON>ch keine Artikel", "no_obj_found": "Keine {{obj}} gefunden", "no_records_found": "<PERSON><PERSON>nungen gefunden", "no_statistics_found": "<PERSON><PERSON> Statistiken gefunden", "no_last_statistics_found": "<PERSON><PERSON> letzten Statistiken gefunden", "none": "<PERSON><PERSON>", "not_found": "404 Nicht gefunden", "note": "<PERSON><PERSON><PERSON><PERSON>", "notes": "Anmerkungen", "of": "<PERSON>", "password": "Passwort", "preview": "Vorschau", "quarterly": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "redirector": "Umleitung", "refresh": "Aktualisierung", "remove": "Löschen", "remove_claim": "Anspru<PERSON> entfernen", "reset": "Z<PERSON>ücksetzen", "revoked": "Widerrufen", "save": "sparen", "search": "<PERSON><PERSON>", "seconds": "Sekunden", "select_all": "<PERSON><PERSON><PERSON> alles", "select_value": "Wähle Wert", "sending": "Senden", "sent_code": "Code gesendet!", "show": "Show", "size": "Größe", "start": "Start", "start_time": "Startzeit", "end_time": "Endzeit", "started": "gestartet", "state": "Zustand", "status": "Status", "stop_editing": "Stoppen Sie die Bearbeitung", "submitted": "Eingereicht", "success": "Erfolg", "successfully_claimed_obj": " {{count}} {{obj}}erfolg<PERSON>ich beansprucht", "sync": "Sync", "test": "PRÜFUNG", "theme": "<PERSON>a", "time": "Zeit", "timestamp": "Zeitstempel", "type": "Art", "type_for_options": "<PERSON><PERSON><PERSON> den <PERSON>rt ein, den <PERSON> erstellen müssen...", "select": "Auswahl...", "unknown": "unbekannte", "use_file": "<PERSON><PERSON> verwenden", "value": "Wert", "variable": "Variable", "view": "Au<PERSON>cht", "view_details": "Details anzeigen", "view_in_gateway": "Im Controller anzeigen", "view_json": "JSON anzeigen", "warning": "<PERSON><PERSON><PERSON>", "warnings": "Warnungen", "yearly": "<PERSON><PERSON><PERSON><PERSON>", "yes": "<PERSON>a", "your_new_note": "<PERSON><PERSON>e neue Notiz"}, "configurations": {"add_interface": "Schnittstelle hinzufügen", "add_radio": "Radio hinzufügen", "add_ssid": "SSID hinzufügen", "add_subsection": "Unterabschnitt hinzufügen", "advanced_settings": "Erweiterte Einstellungen", "affected_explanation_one": "<PERSON> dieser Konfiguration sind {{count}} Ger<PERSON><PERSON> betroffen", "affected_explanation_other": "<PERSON> dieser Konfiguration sind {{count}} Ger<PERSON><PERSON> betroffen", "applied_configuration": "Angewandte Konfiguration", "cant_delete_explanation": "Diese Konfiguration kann nicht gel<PERSON> werden, da sie von mindestens einem Gerät, einem Ort oder einer Entität verwendet wird. Sie können sehen, was sie sind, indem Sie im Formular dieser Konfiguration auf die Schaltfläche neben \"In Verwendung\" klicken", "cant_delete_explanation_simple": "Diese Konfiguration kann nicht gel<PERSON> werden, da sie von mindestens einem Gerät, einem Ort oder einer Entität verwendet wird. Sie können sehen, was sie sind, indem Sie auf die Konfigurationsseite gehen", "configuration_json": "Konfiguration JSON", "configuration_push_result": "Konfigurations-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "configuration_sections": "Konfigurationsabschnitte", "delete_interface": "Schnittstelle löschen", "delete_radio": "Radio löschen", "delete_ssid": "SSID löschen", "device_configurations": "Gerätekonfigurationen", "device_types": "Gerätetypen", "dhcp_snooping": "DHCP-Snooping", "error_pushes_one": "Fehler (könnte an einer fehlerhaften Konfiguration liegen): {{count}}", "error_pushes_other": "<PERSON><PERSON> (können auf eine fehlerhafte Konfiguration zurückzuführen sein): {{count}}", "expert_name": "Expertenmodus", "expert_name_explanation": "<PERSON>e können den Expertenmodus verwenden, um Ihre Konfiguration direkt zu ändern, einschließlich des Hinzufügens von Werten, die derzeit nicht von der Benutzeroberfläche unterstützt werden. Bitte verwenden Sie dieses Format: { \"interfaces\": [ ... ], \"radios\": { ... }, ...etc }", "explanation": "Erläuterung", "firewall": "Firewall", "firmware_upgrade": "Firmware-Aktualisierung", "globals": "Globals", "health": "Gesundheit", "hostapd_warning": "URL-Parameter, Bsp.: test=value", "import_file": "Konfiguration aus Datei importieren", "import_file_explanation": "Sie können die folgende Option verwenden, um eine JSON-Konfigurationsdatei mit einem Inhalt in diesem Format zu importieren:\n{\n     \"interfaces\": [ ... ],\n     \"radios\": { ... },\n     ...etc\n}", "import_warning": "WARNUNG: Dieser Vorgang überschreibt alle Konfigurationsabschnitte, die Sie möglicherweise bereits erstellt haben.", "imported_configuration": "Importierte Konfiguration", "in_use_title": "{{name}} In Gebrauch", "interfaces": "Schnittstellen", "network": "Netzwerk", "interfaces_instruction": "Bitte verwenden Sie eine gültige JSON-Zeichenfolge im folgenden Format: { \"interfaces\": [] }.", "invalid_resource": "Ungültige oder Ressource löschen", "metrics": "Metriken", "no_resource_selected": "<PERSON><PERSON> ausgewählt", "notification_details": "Aktualisiert: {{success}}, <PERSON><PERSON> auf Verbindung: {{warning}}, <PERSON><PERSON>: {{error}}", "one": "Aufbau", "push_configuration": "Push-Konfiguration", "push_configuration_error": "<PERSON><PERSON> beim V<PERSON>uch, die Konfiguration auf das Gerät zu übertragen: {{e}}", "push_configuration_explanation": "Konfiguration nicht übertragen, Fehlercode {{code}}", "push_success": "Die Konfiguration wurde verifiziert und ein \"Konfigurieren\"-Befehl wurde jetzt von der Steuerung initiiert!", "radio_limit": "Sie haben die maximale Anzahl an Funkgeräten (5) erreicht. Sie müssen eines der aktivierten Bänder löschen, um ein neues hinzuzufügen", "radios": "Radios", "rc_only": "Nur für Release-Kandidaten", "save_warnings": "Möchten Sie Ihre Konfiguration wirklich speichern?", "services": "dienstleistungen", "special_configuration": "Gerätespezifische Konfiguration", "start_special_creation": "Konfiguration für dieses Gerät erstellen", "statistics": "Statistiken", "successful_pushes_one": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ush: {{count}}", "successful_pushes_other": "Erfolgreiche Pushs: {{count}}", "third_party": "Dritte Seite", "third_party_instructions": "Bitte verwenden Sie eine gültige JSON-Zeichenfolge im folgenden Format: { \"value_name\": \"value\" }.", "title": "Konfigurationen", "unit": "Einheit", "system": "System", "view_affected_devices": "Betroffene Geräte anzeigen", "view_in_use": "In Verwendung anzeigen", "warning_pushes_one": "Warten auf Geräteverbindung: {{count}}", "warning_pushes_other": "Warten auf Geräteverbindung: {{count}}", "weight": "Gewicht", "wifi_bands_max": "<PERSON><PERSON> können nicht mehr als 16 SSIDs dieses WLAN-Band verwenden", "wifi_frames": "WiFi-Frames", "multi_psk_key_exsist": "Der Schlüssel existiert bereits"}, "contacts": {"access_pin": "Zugangs-PIN", "claim_explanation": "Um Kontakte zu beanspruchen, können Sie die folgende Tabelle verwenden", "first_name": "<PERSON><PERSON><PERSON>", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initials": "<PERSON>en", "last_name": "Nachname", "mobiles": "MOBILES", "one": "Kontakt", "other": "Kontakte", "phones": "Telefone", "primary_email": "<PERSON><PERSON><PERSON>", "salutation": "<PERSON><PERSON><PERSON>", "secondary_email": "Alternative Email", "title": "Titel", "to_claim": "Kontakte zu beanspruchen", "visual": "Visual"}, "controller": {"configurations": {"create_success": "Konfiguration erstellt!", "delete_success": "Konfiguration ist jetzt gelöscht!", "title": "Standardkonfigurationen", "update_success": "Aktualisierte Konfiguration!"}, "configure": {"invalid": "Ihre neue Konfiguration muss gültiges JSON sein", "success": "Die neue Konfiguration wird jetzt auf dem Gerät bereitgestellt", "title": "Konfigurieren Sie über die CLI", "warning": "<PERSON><PERSON><PERSON><PERSON>: <PERSON><PERSON> gew<PERSON>: Es werden nur minimale Tests mit dieser Konfiguration durchgeführt"}, "crud": {"choose_time": "Benutzerdefinierter Zeitrahmen", "clear_time": "<PERSON><PERSON><PERSON>", "delete_success_obj": " {{obj}}<PERSON><PERSON><PERSON><PERSON>"}, "dashboard": {"associations": "Verbände", "associations_explanation": "Alle aktuell verbundenen Assoziationen (oder UEs)", "certificates": "Zertifikate", "certificates_explanation": "Status der Zertifikate aktuell verbundener Geräte", "commands": "<PERSON><PERSON><PERSON><PERSON>", "commands_explanation": "Alle ausgeführten Befehle", "device_dashboard_refresh": "Neue Verbindungsstatistik", "device_types": "Gerätetypen", "device_types_explanation": "Gerätetypen aller Geräte, die auf diesen Controller verweisen", "devices_explanation": "Alle Geräte zeigen auf diesen Controller-Endpunkt", "error_fetching": "Fehler beim Abrufen des Dashboards", "expand": "<PERSON><PERSON><PERSON><PERSON>", "last_ping_explanation": "<PERSON><PERSON> diese Daten erhoben wurden", "memory": "Speichernutzung", "memory_explanation": "Derzeit verbundene Geräte mit der entsprechenden Menge an belegtem Speicher", "no_certificate": "<PERSON><PERSON>", "not_connected": "Nicht verbunden", "others": "andere", "overall_health": "Allgemeine Gesundheit", "overall_health_explanation": "Durchschnittlicher Gesundheitszustand aller derzeit verbundenen Geräte, von denen wir Gesundheitsdaten erhalten. Die genaue Berechnung lautet: (Geräte = 100 % * 100 + <PERSON><PERSON><PERSON><PERSON> >= 90 * 95 + <PERSON><PERSON><PERSON><PERSON> >= 60 * 75 + <PERSON><PERSON><PERSON><PERSON> < 60 * 30) / Verbundene Geräte", "overall_health_explanation_pie": "<PERSON> Anzahl der Geräte mit einem Integritätsprozentsatz innerhalb dieser Kategorien", "serial_mismatch": "Serielle Diskrepanz", "status": "Status", "status_explanation": "Status aller G<PERSON>, die auf diesen Controller-Endpunkt verweisen", "unknown_status": "Unbekannter Status", "unrecognized": "<PERSON><PERSON><PERSON><PERSON>", "uptimes": "", "uptimes_explanation": "Aktuell verbundene Geräte mit den entsprechenden Uptimes", "vendors": "<PERSON><PERSON><PERSON>", "vendors_explanation": "<PERSON><PERSON><PERSON> der Geräte, die auf diesen Controller verweisen", "verified": "Verifiziert"}, "devices": {"add_blacklist": "Seriennummer hinzufügen", "added": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "added_blacklist": "Seriennummer zur Blacklist hinzugefügt!", "average_uptime": "Durchschnittliche Betriebszeit", "blacklist": "Schwarze Liste", "blacklist_update": " {{serialNumber}} Datensatz aktualisieren", "by": "Durch", "capabilities": "Fähigkeiten", "command_one": "<PERSON><PERSON><PERSON>", "commands": "<PERSON><PERSON><PERSON><PERSON>", "complete_data": "Vollständige Daten", "config_id": "Konfigurations-ID", "connecting": "Anschließen", "connection_changes": "Verbindungss<PERSON>us", "delete_blacklist": "<PERSON><PERSON><PERSON><PERSON> von <PERSON> entfernt!", "delete_health_explanation": "Dadurch werden alle Zustandsprüfungen vor dem von Ihnen gewählten Datum dauerhaft gelöscht", "delete_logs_explanation": "Dadurch werden alle Protokolle vor dem von Ihnen gewählten Datum dauerhaft gelöscht", "error_code": "Fehlercode", "executed": "Hingerichtet", "finished_reboot": "{{serialNumber}} hat gerade den Neustart beendet!", "finished_upgrade": "{{serialNumber}} hat das Upgrade abgeschlossen!", "from_to": "Von {{from}} bis {{to}}", "healthchecks": "Gesundheitschecks", "last_modified": "Zuletzt bearbeitet:", "last_upgrade": "Letzte Aktualisierung", "localtime": "Ortszeit", "logs": "LOGS", "new_statistics": "Neue Statistiken", "no_more_available": "Alle abgerufen", "reason": "<PERSON><PERSON><PERSON>", "results": "Ergebnisse", "sent_upgrade_to_latest": "<PERSON><PERSON><PERSON> „Auf neueste Version aktualisieren“ an das Gerät gesendet", "severity": "Schwere", "show_more": "<PERSON><PERSON><PERSON> mehr", "started_reboot": "{{serialNumber}} zum Neustart abschalten!", "started_upgrade": "{{serialNumber}} einfach herunter<PERSON>, um das Upgrade zu starten!", "trace": "Spur", "trace_description": "Hinweis: Starten Sie eine Fernverfolgung dieses Geräts für eine bestimmte Dauer oder eine Anzahl von <PERSON>n", "update_success": "Gerät aktualisiert!", "updated_blacklist": "Schwarze Liste aktualisiert!"}, "firmware": {"devices_explanation": "<PERSON><PERSON><PERSON><PERSON>, die auf diesen Firmware-Server verwiesen haben. Dies könnte Diskrepanzen zwischen dieser Nummer und der des Geräte-Dashboards erklären", "endpoints": "Endpunkte", "endpoints_explanation": "Alle Endpunkte, die auf diesen Firmware-Server verweisen", "firmware_age": "Firmware-Alter", "firmware_age_explanation": "Durchschnittliches Firmware-Alter für die Geräte, für die uns diese Daten vorliegen", "latest": "Neueste Firmware installiert", "old_firmware": "Alte Firmware", "ouis_explanation": "<PERSON><PERSON><PERSON>, die sich mit diesem Firmware-Server verbunden haben", "outdated_one": "Firmware {{count}} Tag alt", "outdated_other": "Firmware {{count}} Tage alt", "outdated_unknown": "Firmware unbekannten Alters", "release": "Veröffentlichung", "show_dev_releases": "Entwicklerversionen", "status_explanation": "Verbindungss<PERSON><PERSON> von Geräten, die sich mit diesem Firmware-Server verbunden haben", "unrecognized": "Unbekannte Firmware", "unrecognized_firmware": "Unbekannte Firmware", "unrecognized_firmware_explanation": "Firmware, die derzeit von Geräten verwendet wird und von diesem Firmware-Server nicht erkannt wird", "up_to_date": "Aktuelle Geräte", "up_to_date_explanation": "<PERSON><PERSON><PERSON><PERSON>, die die neueste verfügbare Software verwenden, die ihnen zur Verfügung steht"}, "provisioning": {"title": "Bereitstellung"}, "queue": {"title": "Ereigniswarteschlange"}, "radius": {"calling_station_id": "Stations", "disconnect": "<PERSON><PERSON><PERSON>", "disconnect_success": "Radius-Sitzung getrennt!", "input_octets": "Eingang", "output_octets": "Ausgabe", "radius_clients": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "session_time": "Sitzungszeit", "username": "<PERSON><PERSON><PERSON><PERSON>"}, "stats": {"load": "Belastung (1 | 5 | 15 m.)", "seconds_ago": " Vor {{s}} <PERSON><PERSON><PERSON>", "used": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "telemetry": {"duration": "<PERSON><PERSON>", "interval": "Intervall", "kafka": "Kafka", "kafka_success": "Kafka-Telemetrie ist jetzt gestartet!", "last_update": "Letztes Update", "minutes": "Protokoll", "need_types": "<PERSON>e müssen mindestens einen Typ au<PERSON>wählen", "output": "Ausgabemodus", "seconds_ago": " Vor{{seconds}} <PERSON><PERSON><PERSON>", "title": "Telemetrie", "types": "Typen", "websocket": "WebSocket"}, "trace": {"down": "<PERSON><PERSON><PERSON>", "download": "<PERSON> herunt<PERSON>n", "duration": "<PERSON><PERSON>", "network": "Netzwerk", "packets": "<PERSON><PERSON>", "success": "Trace auf Gerät Nr.{{serialNumber}}abgeschlossen. Das Ergebnis können Sie jetzt herunterladen", "up": "<PERSON><PERSON>", "wait": "Auf die Ergebnisse warten?"}, "wifi": {"active_ms": "Aktiv", "busy_ms": "Beschäftigt", "channel_width": "Ch-Breite", "mode": "Modus", "noise": "L<PERSON>rm", "receive_ms": "<PERSON><PERSON><PERSON><PERSON>", "rx_rate": "Rx-Rate", "station": "Bahnhof", "tx_rate": "Tx-Rate", "vendor": "Verkäufer", "wifi_analysis": "WLAN-Analyse"}}, "crud": {"add": "Hinzufügen", "confirm_cancel": "Möchten Sie die vorgenommenen Änderungen wirklich verwerfen?", "confirm_delete_obj": "<PERSON><PERSON><PERSON><PERSON> Sie diese {{obj}}wirklich löschen?", "create": "<PERSON><PERSON><PERSON><PERSON>", "create_object": " {{obj}}erste<PERSON>", "delete": "Löschen", "delete_confirm": "<PERSON><PERSON><PERSON><PERSON> Sie diese {{obj}}wirklich löschen?", "delete_obj": " {{obj}}löschen", "edit": "<PERSON><PERSON><PERSON>", "edit_obj": " {{obj}}bearbeiten", "error_create_obj": "<PERSON><PERSON> beim <PERSON><PERSON> von {{obj}}: {{e}}", "error_delete_obj": "<PERSON><PERSON> beim Lö<PERSON> von {{obj}}: {{e}}", "error_fetching_obj": "<PERSON><PERSON> beim <PERSON> von {{obj}}: {{e}}", "error_revoke_obj": "<PERSON><PERSON> beim <PERSON> von {{obj}}: {{e}}", "error_update_obj": "<PERSON><PERSON> beim Aktualisieren von {{obj}}: {{e}}", "success_create_obj": " {{obj}}erfolgreich erstellt!", "success_delete_obj": " {{obj}}erfolgreich gelöscht!", "success_revoke_obj": " {{obj}}erfolgreich widerrufen!", "success_update_obj": " {{obj}}erfolgreich aktualisiert!"}, "devices": {"all": "Alles", "associations": "Verbände", "certificate_expires_in": "Zertifikat läuft ab in", "certificate_expiry": "Zert. Läuft ab in", "connected": "In Verbindung gebracht", "crash_logs": "Absturzprotokolle", "create_errors": "<PERSON><PERSON> beim <PERSON>, <PERSON><PERSON><PERSON><PERSON> zu erstellen", "create_success": " Geräte erfolgreich erstellt", "current_firmware": "Aktuelle Firmware", "device_type_not_found": "Unbekannter Gerätetyp", "duplicate_serial": "Doppelte Seriennummer in der Datei", "error_rtty": "<PERSON><PERSON> beim <PERSON>, eine Verbindung zum Gerät herzustellen: {{e}}", "file_errors": "problematische Geräte", "found_assigned": "bereits zugewiesene Geräte", "found_not_assigned": "bereits vorhandene, aber jetzt eigene Geräte", "import_batch_tags": "Geräte importieren", "import_device_warning": "<PERSON>te stellen Si<PERSON> sicher, dass am Anfang oder Ende von Werten keine zusätzlichen Leerzeichen stehen, es sei denn, es handelt sich um einen Teil des gewünschten Werts", "import_explanation": "<PERSON><PERSON><PERSON> den Massenimport von Geräten müssen Sie eine CSV-Datei mit den folgenden Spalten verwenden: SerialNumber, DeviceType, Name, Description, Note", "invalid_serial_number": "Ungültige Seriennummer (muss 12 HEX-Zeichen lang sein)", "logs_one": "Log", "new_devices": "Neue Geräte", "no_model_image": "<PERSON><PERSON>d gefunden", "not_connected": "Nicht verbunden", "not_found_gateway": "<PERSON>hler: Ger<PERSON> hat sich noch nicht mit dem <PERSON> verbunden", "notifications": "Gerätebenachrichtigungen", "one": "G<PERSON><PERSON>", "reassign_already_owned": "Geräte neu zuweisen, die bereits vorhanden sind und einem anderen Unternehmen/Veranstaltungsort/Abonnenten gehören?", "reboot_logs": "Neustartprotokolle", "restricted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "restricted_overriden": "Dies ist ein eingeschränktes Gerät, aber es befindet sich im Entwicklungsmodus. Alle Einschränkungen werden derzeit ignoriert", "restrictions_overriden_title": "<PERSON><PERSON><PERSON><PERSON>", "sanity": "Gesundheit", "start_import": "Geräteimport starten", "test_batch": "Testen Sie Importdaten", "test_results": "Testergebnisse", "title": "<PERSON><PERSON><PERSON><PERSON>", "treating": "Testen:", "unassigned_only": "<PERSON>ur nicht zugewiesen", "update_error": "Fehler beim V<PERSON>, Geräte zu aktualisieren", "update_success": "Geräte erfolgreich aktualisiert"}, "entities": {"active": "Aktiv", "add_configurations": "Konfigurationen hinzufügen", "add_ips": "IPs hinzufügen", "add_ips_explanation": "Sie können IPv4- oder IPv6-Adressen in den folgenden Formaten hinzufügen", "api_key": "API-Schlüssel", "cant_delete_explanation": "Diese Entität kann nicht gelö<PERSON>t werden, da sie untergeordnete Entitäten und/oder Veranstaltungsorte hat. Sie müssen alle untergeordneten Elemente dieser Entität löschen, bevor Sie sie löschen", "claim_device_explanation": "Um Geräte zu beanspruchen, können Sie die folgende Tabelle verwenden. Wenn ein Gerät bereits von einer anderen Entität oder einem anderen Ort beansprucht wurde, werden wir die Zuordnung auch aufheben, bevor wir es dieser Entität zuweisen", "client_enrollment_profile": "Kundenregistrierungsprofil", "create_child_entity": "Untergeordnete Entität erstellen", "create_root": "Root-Entität erstellen", "create_root_explanation": "Bitte geben Sie die erforderlichen Informationen ein, um die Root-Entität Ihres Bereitstellungsdienstes zu erstellen. Diese Informationen können nach der Erstellung geändert werden", "current_state": "Aktuellen Zustand", "default_redirector": "Standard-Redirector", "devices_to_claim": "Neue Geräte zum Beantragen", "devices_under_root": "Geräte können nicht direkt unter der Root-Entität erstellt werden. Bitte erstellen Sie neue Entitäten oder Veranstaltungsorte und erstellen Sie Geräte darunter.", "enter_ips": "<PERSON><PERSON><PERSON> Sie hier die IP(s) ein, die Sie hinzufügen möchten", "entity": "Entität", "error_sync": "<PERSON><PERSON> beim <PERSON>, die Synchronisierung von {{name}}zu starten: {{e}}", "failed_test": "Fehlgeschlagene Tests mit DigiCert-Anmeldeinformationen, bitte überprüfen Sie Ihre Entitätsinformationen", "initial_state": "Ausgangszustand", "ip_cidr": "IP/Nummer (Beispiel: 10.0.0.0/8)", "ip_detection": "IP-Erkennung", "ip_list": "Liste: IP, IP IP", "ip_range": "Bereich: IP-IP", "ip_single_address": "Einzelne Adresse: IP", "one": "Entität", "organization": "Organisation", "server_enrollment_profile": "Serverregistrierungsprofil", "status": "Status", "sub_one": "Untereinheit", "sub_other": "Untereinheiten", "success_sync": "Synchronisierung von {{name}}erfolgreich gestartet!", "success_test": "Der Test der DigiCert-Anmeldeinformationen dieser Entität war erfolgreich!", "suspended": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sync_explanation": "Möcht<PERSON> Sie diese Entität synchronisieren? Dies kann je nach Menge der Zertifikate dieser Entität eine Weile dauern.", "sync_title": " {{name}}synchronisieren", "test_digicert_creds": "Anmeldeinformationen testen", "title": "Entitäten", "tree": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "update_success": "Entität aktualisiert!", "venues_under_root": "Veranstaltungsorte können nicht direkt unter der Root-Entität erstellt werden"}, "firmware": {"confirm_default_data": "Bitte bestätigen Sie die untenstehenden Informationen und klicken Sie auf „Bestätigen“, sobald <PERSON> bereit sind, den Vorgang zu starten", "create_success": "Neue Standard-Firmware-Einstellungen erstellt!", "db_update_warning": "Dieser Vorgang wird täglich automatisch durchgeführt, ohne dass dieses manuelle Update verwendet werden muss. Die Aktualisierung dieser Datenbank kann bis zu 25 Minuten dauern", "default_created_error_one": "{{count}} <PERSON><PERSON> be<PERSON>, eine neue Einstellung zu erstellen", "default_created_error_other": "{{count}} <PERSON><PERSON> be<PERSON>, eine neue Einstellung zu erstellen", "default_created_one": "{{count}} Standard-Firmware-Einstellung erstellt", "default_created_other": "{{count}} Standard-Firmware-Einstellungen erstellt", "default_found_one": "<PERSON><PERSON><PERSON> den Gerätetyp {{count}} wurde eine gültige Revision gefunden", "default_found_other": "Gültige Revisionen für {{count}} Gerätetypen gefunden", "default_mass_delete_success_one": " {{count}} Standard-Firmware-Einstellung gelöscht!", "default_mass_delete_success_other": " {{count}} Standard-Firmware-Einstellungen gelöscht!", "default_not_found_one": "Keine gültigen Firmware-Versionen für den Gerätetyp {{count}} ", "default_not_found_other": "<PERSON>ine gültigen Firmware-Versionen für {{count}} Gerätetypen", "default_title": "", "default_update_success": "Standard-Firmware für {{deviceType}}aktualisiert!", "delete_success": "Standard-Firmware-Einstellung gelöscht!", "edit_default_title": "Dies ist die aktuelle Firmware, die als Mindestversion für neue APs vom Typ {{deviceType}}verwendet wird. Wenn ein neuer {{deviceType}} AP eine Verbindung zum Gateway herstellt, wird er automatisch auf diese Version aktualisiert.", "fetching_defaults": "Alle verfügbaren Firmware für ausgewählte Gerätetypen werden abgerufen...", "last_db_update_modal": "Firmware-Datenbank", "last_db_update_title": "Datenbank", "one": "Firmware", "select_default_device_types": "Bitte wählen Sie alle Gerätetypen aus, auf die Sie diese neue Standard-Firmware-Regel anwenden möchten. Wenn Sie den gewünschten Gerätetyp nicht finden können, bedeu<PERSON><PERSON> dies, dass bereits eine Regel angewendet wurde.", "select_default_revision": "<PERSON>e können jetzt die Mindestversion auswählen, auf die Ihre Gerätetypen abzielen sollen", "start_db_update": "Datenbankaktualisierung starten", "started_db_update": "Datenbankaktualisierung gestartet, dieser Vorgang sollte bis zu 25 Minuten dauern", "update_success": "Standard-Firmware-Informationen gespeichert!"}, "footer": {"powered_by": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "version": "Ausführung"}, "form": {"captive_web_root_explanation": "Bitte verwenden Sie nur .tar-Dateien (keine komprimierten Dateien wie z. B. .targz)", "certificate_file_explanation": "Bitte verwenden Sie eine .pem-Datei, die mit „-----BEGIN CERTIFICATE-----“ beginnt und mit „-----END CERTIFICATE-----“ endet.", "invalid_alphanumeric_with_dash": "Akzeptierte Zeichen. sind nur alphanumerisch (Buchstaben & Zahlen)", "invalid_cidr": "Ungültige CIDR-IPv4-Adresse. Beispiel: ***********/12", "invalid_email": "Ungültige E-Mail", "invalid_file_content": "Ungült<PERSON>, bitte bestätigen Sie, dass es sich um ein gültiges Format handelt", "invalid_fqdn_host": "Ungültiger FQDN-Hostname", "invalid_hostname": "Ungültiger Hostname: Er darf nur aus alphanumerischen Zeichen und Bindestrichen bestehen", "invalid_icon_lang": "Ungültige Sprache, sollte aus 3 Buchstaben bestehen (eng, fre, ger, ita usw.)", "invalid_ieee": "<PERSON><PERSON><PERSON> dieses Verschlüsselungsprotokoll muss ieee80211w entweder „optional“ oder „erforderlich“ sein.", "invalid_ieee_required": "ieee80211w muss für dieses Verschlüsselungsprotokoll „erforderlich“ sein", "invalid_interfaces": "Ungültige Schnittstellen-JSON-Zeichenfolge. Bitte bestätigen Sie, dass Ihr Wert gültiges JSON ist und Schnittstellen als einzigen Schlüssel hat und dass der Schnittstellenwert ein Array ist. Beispiel: {\"interfaces\": []}", "invalid_ipv4": "Ungültige IPv4-<PERSON><PERSON><PERSON> (Bsp.: ***********/16)", "invalid_ipv6": "Ungültige IPv6-Adresse (Bsp.: fd00::/64)", "invalid_json": "Ungültige JSON-Zeichenfolge", "invalid_lease_time": "Ungültiger Lease-Time-Wert! Sie müssen im digitUnit-Format vorliegen. Zum Beispiel: 6d2h5m, was 6 Tage, 2 Stunden und 5 Minuten bedeutet. Hier sind die akzeptierten Einheiten: m, h, d. Wenn Sie eine Einheit nicht verwenden möchten, lassen <PERSON>e sie vollständig weg. Anstatt also 0d2h0m zu sagen, verwenden Sie 2h", "invalid_mac_uc": "Ungültiger MAC-Wert, zum Beispiel: 00:00:5e:00:53:af", "duplicate_mac": "Diese MAC-Adresse ist bereits in der Liste vorhanden", "duplicate_ip": "Diese IP-Adresse ist bereits in der Liste vorhanden", "invalid_password": "Ungültiges Passwort, bitte sehen <PERSON>e sich die Passwortrichtlinie an", "invalid_phone_number": "Ungültige Telefonnummer", "invalid_phone_numbers": "Mindestens eine der Telefonnummern ist ungültig. Bitte geben Si<PERSON> sie ohne Symbole und Leerzeichen oder in diesem Format an: +1(123)123-1234", "invalid_port_range": "Ungültiger Portwert. <PERSON><PERSON> muss größer als 0 und kleiner als 65 535 sein. Wenn <PERSON><PERSON> einen Portbereich verwenden, stel<PERSON> bitte sicher, dass der zweite Port eine höhere Nummer als der erste hat.", "invalid_port_ranges": "Ungültige Portbereichskombination! Bitte stellen Si<PERSON> sicher, dass beide Portwerte vom gleichen Typ sind (einzeln oder Bereich). Wenn es sich um Bereiche handelt, stellen <PERSON> sicher, dass beide die gleiche Anzahl von Ports abdecken", "invalid_proto_6g": "Dieses Verschlüsselungsprotokoll kann nicht auf einer SSID verwendet werden, die 6G verwendet", "invalid_proto_passpoint": "<PERSON>ses V<PERSON>chlüsselungsprotokoll kann nicht mit einer Passpoint-SSID verwendet werden. Bitte wählen Sie ein Protokoll aus, das Radius verwenden kann", "invalid_select_ports": "Inkompatible Werte zwischen Schnittstellen! Bitte stellen Si<PERSON> sicher, dass es keine doppelte PORT/VLAN-ID-Kombination zwischen Ihren Schnittstellen gibt", "invalid_static_ipv4_d": "Ungültige Adresse, dieser Bereich ist für Multicasting reserviert (Klasse D). Das erste Oktett sollte 223 oder niedriger sein", "invalid_static_ipv4_e": "Ungültige Adresse, dieser Bereich ist für Experimente reserviert (Klasse E). Das erste Oktett sollte 223 oder niedriger sein", "invalid_third_party": "Ungültige Drittanbieter-JSON-Zeichenfolge. Bitte bestätigen Sie, dass Ihr Wert ein gültiges JSON ist", "key_file_explanation": "Bitte verwenden Sie eine .pem-Datei, die mit „-----BEGIN PRIVATE KEY-----“ beginnt und mit „-----END PRIVATE KEY-----“ endet.", "max_length": "Maximale Länge von {{max}} <PERSON><PERSON><PERSON>.", "max_value": "<PERSON><PERSON><PERSON><PERSON> von {{max}}", "min_length": "<PERSON><PERSON><PERSON><PERSON><PERSON> von {{min}} <PERSON><PERSON><PERSON>.", "min_max_string": "Der Wert muss eine Länge z<PERSON>schen {{min}} (e<PERSON><PERSON><PERSON><PERSON><PERSON>) und {{max}} (e<PERSON><PERSON><PERSON><PERSON><PERSON>) haben.", "must_string": "Die Länge des Wertes muss {{max}} betragen", "min_value": "<PERSON><PERSON><PERSON><PERSON> von {{min}}", "missing_interface_upstream": "Sie müssen mindestens eine Brückenmodus-Schnittstelle (Layer 2 Bridging) haben. Derzeit sind alle Schnittstellen im Routing Mode (NAT)", "new_email_to_notify": "Neue E-Mail zur Benachrichtigung", "new_phone_to_notify": "Neues Telefon zu benachrichtigen", "not_selected": "Nicht ausgewählt", "not_uploaded_yet": "Noch nicht hoch<PERSON>aden", "pem_file_explanation": "Bitte verwenden Sie eine .pem-Datei", "required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "using_file": "(mit Datei: {{filename}})", "value_recorded_no_filename": "Aufgezeichneter Wert, kein Dateiname", "invalid_mac_format": "Das legale Mac-Format kann nur kleine Buchstaben und Zahlen enthalten, mit einem Begrenzer von ':'. Bitte verwenden Sie das Format: 00:00:5e:00:53:af", "must_be_integer": "Der Wert muss eine ganze <PERSON> sein", "range_min": "Der Wert muss mindestens {{min}} betragen", "range_max": "Der Wert darf höchstens {{max}} betragen", "range_both": "Der Wert muss zwischen {{min}} und {{max}} liegen", "invalid_label_name": "Der Etikettname darf nur Buchstaben und Zahlen enthalten", "invalid_static_ipv4_loopback": "<PERSON>g<PERSON><PERSON><PERSON> adresse, Loopback-Adressbereich darf nicht verwendet werden", "invalid_domain_or_ip": "<PERSON>s kann nur eine IP oder Domainname sein, zum Beispiel: www.fs.com"}, "inventory": {"computed_configuration": "Berechnete Konfiguration", "dev_class": "Geräteklasse", "device_type": "Gerätetyp", "error_reboots": "<PERSON><PERSON> beim Senden des Befehls: {{count}}", "error_remove_claim": "<PERSON><PERSON> beim Entfernen des Anspruchs: {{e}}", "error_upgrades": "<PERSON><PERSON> beim Senden des Upgrade-Befehls: {{count}}", "invalid_serial_number": "Ungültige Seriennummer. Eine Seriennummer sollte nur 12 HEX-Zeichen lang sein (A-F, 0-9)", "invalid_device_name": "Ungültiger Gerätename. Muss 1-2 alphanumerische Zeichen oder 3-63 alphanumerische Zeichen mit Bindestrichen sein (kann nicht mit einem Bindestrich beginnen oder enden)", "no_computed": "<PERSON>ine berechnete Konfiguration: Sie müssen eine gültige Konfiguration zuweisen, um sie anzuzeigen", "no_firmware": "Keine Firmware verfügbar für Gerätetyp: {{count}}", "not_connected": "Gerät nicht verbunden: {{count}}", "parent": "Elternteil", "serial_number": "Ordnungsnummer", "skipped_upgrades": "Übersprungene Upgrades: {{count}}", "success_remove_claim": "Anspruch erfolgreich entfernt am: {{serial}}", "successful_reboots": "Neustart gestartet: {{count}}", "successful_upgrades": "Erfolgreiche Upgrades: {{count}}", "tag_one": "Etikett", "tags": "Inventar-Tags", "title": "Inventar", "warning_reboots": "Nicht verbunden: {{count}}", "warning_upgrades": "Nicht verbundene Geräte: {{count}}", "label": "Etikett", "site": "<PERSON><PERSON>", "create_label": "Neues Etikett erstellen"}, "jobs": {"error_macs": "Fehler-MACs", "job": "Job", "job_details": "Job Details", "notify_emails": "E-Mails benachrichtigen", "notify_sms": "SMS benachrichtigen", "successful_macs": "Erfolgreiche MACs", "title": "Arbeitsplätze"}, "keys": {"description_error": "Die Beschreibung muss weniger als 64 <PERSON>eichen lang sein", "expire_error": "Der Ablauf darf nicht mehr als ein Jahr in der Zukunft liegen", "expires": "Läuft ab", "max_keys": "<PERSON><PERSON> (10)", "name_error": "Der Name sollte eindeutig sein und aus 6 bis 20 alphanumerischen Zeichen bestehen", "one": "API-Schlüssel", "other": "API-Schlüssel"}, "locations": {"address_line_one": "<PERSON><PERSON><PERSON><PERSON> eins", "address_line_two": "Adresszeile zwei", "building_name": "Gebäudename", "city": "Stadt", "claim_explanation": "Um Standorte zu beanspruchen, können Sie die folgende Tabelle verwenden", "country": "Land", "elevation": "Elevation", "geocode": "Geo-Code", "lat": "Breite", "longitude": "Längengrad", "one": "Ort", "other": "<PERSON><PERSON><PERSON>", "postal": "<PERSON><PERSON><PERSON><PERSON>", "state": "Bundesstaat / Provinz", "title": "<PERSON><PERSON><PERSON>", "to_claim": "<PERSON><PERSON><PERSON> zu beanspru<PERSON>", "view_gps": ""}, "login": {"access_policy": "Zugangsrichtlinien", "change_password_error": "Abgelehntes Passwort, möglicherweise ein altes Passwort", "change_password_explanation": "Geben Sie Ihr neues Passwort ein und bestätigen Sie es", "change_your_password": "Ändere das Passwort", "confirm_new_password": "Bestätige neues Passwort", "email_instructions": "<PERSON>e sollten bald einen 6-stelligen Code an Ihre E-Mail-Adresse erhalten. Wenn Sie es nicht finden können, überprüfen Sie bitte Ihren Spam-Ordner.", "error_sending_code": "<PERSON><PERSON> be<PERSON>, Code zu senden: {{e}}", "forgot_password": "Passwort vergessen?", "forgot_password_instructions": "Geben Sie Ihre E-Mail-Adresse ein, um eine E-Mail mit Anweisungen zum Zurücksetzen Ihres Passworts zu erhalten", "forgot_password_successful": "<PERSON>e sollten in Kürze eine E-Mail mit Anweisungen zum Zurücksetzen Ihres Passworts erhalten. Bitte überprüfen Sie Ihren Spam, wenn Sie die E-Mail nicht finden können", "forgot_password_title": "Passwort vergessen", "google_instructions": "Bitte geben Sie den 6-stelligen Code aus Ihrer Google Authenticator-App ein. Wenn es kurz vor dem Ablaufen ist, können Sie auf ein neues warten", "invalid_credentials": "Ungültige Zugangsdaten, bitte bestätigen Sie, dass Sie die richtige E-Mail-Adresse und das richtige Passwort verwenden.", "invalid_mfa": "Ungültiger Code! Bitte versuche es erneut", "login_explanation": "Geben Sie Ihre E-Mail-Adresse und Ihr Passwort ein, um sich anzumelden", "new_password": "Neues Kennwort", "password_policy": "Kennwortrichtlinie", "remember_me": "<PERSON><PERSON><PERSON> dich an mich", "resend": "<PERSON><PERSON><PERSON> senden", "resent_code": "Code erfolgreich erneut gesendet!", "reset_password": "Passwort zurücksetzen", "sign_in": "Einloggen", "sms_instructions": "<PERSON>e sollten bald einen 6-stelligen Code auf Ihrem Telefon erhalten. Bitte geben Sie es unten ein, um sich anzumelden", "suspended_error": "<PERSON><PERSON> ges<PERSON>rt, wenden <PERSON> sich bitte an Ihren Administrator", "verification": "Bestätigen Sie Ihre Anmeldung", "waiting_for_email_verification": "Konto noch nicht per E-Mail validiert. <PERSON><PERSON> se<PERSON> in Ihrem Posteingang nach oder bitten Sie Ihren Administrator, eine Bestätigung erneut zu senden", "welcome_back": "Willkommen zurück!", "your_email": "<PERSON><PERSON>", "your_new_password": "<PERSON><PERSON> neues Passwort", "your_password": "Ihr Passwort"}, "logs": {"configuration_upgrade": "Konfigurationsaktualisierung", "device_firmware_upgrade": "Firmware-Aktualisierung", "device_statistics": "Gerätestatistik", "export": "Export", "filter": "Filter", "firmware": "Firmware", "global_connections": "Globale Verbindungen", "level": "Niveau", "message": "Botschaft", "one": "Log", "receiving_types": "Benachrichtigungsfilter", "security": "Sicherheit", "source": "<PERSON><PERSON>", "thread": "Faden", "venue_config": "Aufbau", "venue_reboot": "Starten Sie neu", "venue_upgrade": "Aktualisierung"}, "map": {"auto_align": "Automatisch ausrichten", "auto_map": "Automatische Karte", "by_others": "<PERSON><PERSON>", "cumulative_devices": "<PERSON><PERSON><PERSON>", "default_map": "Standardkarte", "delete_warning": "Möchten Sie diese Karte wirklich löschen? Dieser Vorgang ist nicht umkehrbar", "duplicating": "<PERSON><PERSON> dupli<PERSON>", "my_maps": "<PERSON><PERSON>", "other": "<PERSON><PERSON>", "root": "<PERSON><PERSON><PERSON>", "root_node": "Wu<PERSON>elk<PERSON>n", "set_as_default": "Als Standard einstellen", "title": "<PERSON><PERSON>", "visibility": "Sichtweite"}, "notification": {"one": "Benachrichtigung", "other": "Benachrichtigungen"}, "openroaming": {"pool_strategy": "Pool-Strategie", "radius_endpoint_one": "Radiusendpunkt", "radius_endpoint_other": "Radiusendpunkte"}, "operator": {"delete_explanation": "Möchten Sie diesen Operator wirklich löschen? Dieser Vorgang ist nicht umkehrbar", "delete_operator": "Betreiber löschen", "import_location_from_device": "Von anderem Gerät importieren", "one": "Operator", "operator_one": "Operator", "operator_other": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>", "registration_id": "Registrierungs-ID"}, "organization": {"my_organization": "Meine Organisation", "title": "Organisation"}, "overrides": {"delete_source": "Alle Überschreibungen von {{source}}löschen", "ignore_overrides": "Konfigurationsüberschreibungen ignorieren", "name_error": "Der Parameter ist bereits von Ihrer Quelle definiert", "one": "Konfigurationsüberschreibung", "other": "Konfigurationsüberschreibungen", "param_name": "Parameter", "param_value": "Wert", "parameter": "Parameter", "reason": "<PERSON><PERSON><PERSON>", "reason_error": "<PERSON><PERSON> Grund muss weniger als 64 <PERSON><PERSON><PERSON> lang sein. lang", "source": "<PERSON><PERSON>", "tx_power_error": "Die Sendeleistung muss zwischen 1 und 32 liegen", "update_success": "Aktualisierte Konfigurationsüberschreibungen!", "value": "Wert"}, "profile": {"about_me": "<PERSON>ber mich", "activate": "", "add_new_note": "<PERSON><PERSON>", "deactivate": "Deaktivieren", "delete_account": "<PERSON>n Profil <PERSON>", "delete_account_confirm": "Löschen Sie alle meine Informationen", "delete_warning": "Diese Aktion ist nicht umkehrbar. Alle Ihre Profilinformationen und Ihre API-Schlüssel werden entfernt", "deleted_success": "Ihr Profil ist jetzt gelöscht, wir melden Si<PERSON> jetzt ab...", "disabled": "Deaktiviert", "enabled": "aktiviert", "manage_avatar": "<PERSON><PERSON> verwalten", "new_password": "Neues Kennwort", "new_password_confirmation": "Bestätige neues Passwort", "your_profile": "<PERSON><PERSON>"}, "resources": {"configuration_resource": "Ressource", "title": "Ressourcen", "variable": "Variable"}, "restrictions": {"algo": "Signaturalgorithmus", "allowed": "<PERSON><PERSON><PERSON><PERSON>", "countries": "<PERSON><PERSON><PERSON><PERSON>", "developer": "Entwicklermodus", "dfs": "DFS-Überschreibung", "gw_commands": "Gateway-Befehle", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key_verification": "<PERSON><PERSON><PERSON> von Schlüsselinformationen", "restricted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signed_upgrade": "<PERSON><PERSON> signiertes Upgrade", "title": "Beschränkungen", "tty": "TTY-Zugriff"}, "roaming": {"account_created": "Neues Konto erstellt!", "account_deleted": "Konto gelöscht!", "account_one": "Ko<PERSON>", "account_other": "Konten", "certificate_deleted": "Zertifikat gelöscht!", "certificate_one": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "certificate_other": "Zertifikate", "city": "Stadt", "common_name": "Gemeinsamen Namen", "country": "Land", "global_reach": "Globale Reichweite", "global_reach_account_id": "Konto-ID", "invalid_certificate": "Ungültiges Zertifikat", "invalid_key": "Ungültiger privater <PERSON><PERSON><PERSON><PERSON>", "location_details_title": "Ort", "organization": "Organisation", "private_key": "Privat Schlüssel", "province": "<PERSON><PERSON><PERSON>", "state": "Zustand"}, "rrm": {"algorithm": "<PERSON><PERSON><PERSON><PERSON>", "algorithm_other": "Algorithmen", "cant_save_custom": "Benutzerdefinierte RRM-Konfigurationen können erst erstellt oder bearbeitet werden, wenn der RRM-Server erreichbar ist. Bitte wenden Si<PERSON> sich an Ihren Administrator", "cron_error": "Fehler beim Analysieren des CRON-Ausdrucks: Bitte bestätigen Sie, dass er gültig ist", "cron_scheduler": "CRON-Scheduler", "cron_templates": "Vorlagen", "no_algos": "Wir können derzeit keine RRM-Algorithmen abrufen", "no_providers": "Wir können derzeit keine RRM-Anbieter abrufen", "param_error": "Ihre Parameter respektieren die Regeln dieses Algorithmus nicht. Bitte schauen Si<PERSON> sich die Algorithmusbeispiele und Details an", "parameters": "Parameter", "vendor": "Verkäufer", "version": "Ausführung"}, "script": {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "automatic": "Automatik", "create_success": "Das Skript ist jetzt erstellt und einsatzbereit!", "custom_domain": "Benutzerdefinierten Domain", "deferred": "Aufgeschoben", "device_title": "Skript ausführen", "diagnostics": "Diagnose", "explanation": "Führen Sie ein benutzerdefiniertes Skript auf diesem Gerät aus und laden Sie die Ergebnisse herunter", "file_not_ready": "Das Ergebnis wurde noch nicht hoch<PERSON>n, bitte kommen Si<PERSON> später wieder", "file_too_large": "Bitte wählen Sie eine Datei aus, die kleiner als 500 KB ist", "helper": "Dokumentation", "no_script_available": "<PERSON><PERSON> Skript für Ihre Benutzerrolle verfügbar", "now": "Jetzt", "one": "S<PERSON><PERSON><PERSON>", "other": "Skripte", "restricted": "<PERSON><PERSON><PERSON>, die dieses Skript ausführen dürfen", "schedule_success": "Geplante Skriptausführung!", "signature": "Unterschrift", "started_execution": "Ausführung des Skripts gestartet, kommen Sie später für die Ergebnisse!", "timeout": "Auszeit", "update_success": "Skript aktualisiert!", "upload_destination": "Ergebnis-Upload-Ziel", "upload_file": "<PERSON><PERSON> ho<PERSON>n", "visit_external_website": "<PERSON><PERSON><PERSON> an<PERSON>", "when": "Ausführung planen"}, "service": {"billing_code": "Abrechnungscode", "billing_frequency": "Abrechnungshäufigkeit", "class_one": "Serviceklasse", "class_other": "Serviceklassen", "cost": "<PERSON><PERSON>", "one": "Serviceklasse", "other": "Serviceklassen"}, "simulation": {"cancel": "Simulation abbrechen", "cancel_explanation": "Stoppen Sie die Simulation und löschen Sie ihren Datensatz", "cancel_success": "Simulation beendet und Aufzeichnungen gelöscht!", "client_interval": "Kundenintervall", "concurrent_devices": "Gleichzeitige Geräte", "controller": "<PERSON><PERSON>", "current_live_devices": "Aktuelle Live-Geräte", "currently_running_one": "Derzeit wird {{count}} Simulation ausgeführt", "currently_running_other": "Derzeit laufen {{count}} Simulationen", "delete_devices_confirm": "Sind <PERSON> sic<PERSON>, dass Sie alle Geräte und deren Statistiken vom Gateway entfernen möchten? Diese Aktion ist nicht rückgängig zu machen", "delete_devices_loading": "Dieser Vorgang kann bis zu 5 Minuten dauern", "delete_simulation_devices": "Geräte löschen", "delete_success": "Gelöschte Simulation!", "duration": "<PERSON><PERSON>", "error_devices": "<PERSON>hler Geräte", "healthcheck_interval": "Healthcheck-Intervall", "infinite": "<PERSON><PERSON><PERSON>", "keep_alive": "Bleib am Leben", "mac_prefix": "MAC-Präfix", "mac_prefix_length": "Ihr MAC-Präfix muss gültige 6 HEX-Ziff<PERSON> haben (z. B.: 00112233)", "max_associations": "max. <PERSON><PERSON>b<PERSON><PERSON>", "max_clients": "<PERSON><PERSON>", "min_associations": "Mindest. Verbände", "min_clients": "Mindest. <PERSON>", "no_sim_running": "Derzeit läuft keine Simulation", "one": "Simulation", "other": "Simulationen", "owner": "<PERSON><PERSON><PERSON>", "realtime_data": "Echtzeitdaten", "realtime_messages": "Echtzeit-Nachrichten", "reconnect_interval": "Wiederverbindungsintervall", "result_delete_success": "Gelöschtes Ergebnis!", "rx": "empfangen", "rx_messages": "Rx-Meldungen", "sim_currently_running": "Simulation \"{{sim}}\" l<PERSON>uft", "sim_history": "{{sim}} Vorherige Läufe", "simulated": "<PERSON><PERSON><PERSON><PERSON>", "start": "Simulation starten", "start_success": "Simulationslauf gestartet!", "state_interval": "Zustandsintervall", "stop": "Simulation stoppen", "stop_success": "Simulation gestoppt!", "threads": "Themen", "time_to_full": "Zeit für volle Geräte", "tx": "Übertragen", "tx_messages": "Tx-Nachrichten", "view_previous_runs": "Vorherige Läufe anzeigen"}, "statistics": {"last_stats": "Letzte Statistik", "latest": "Neueste Statistiken", "memory": "Erinnerung"}, "subscribers": {"billing_contact_info": "Rechnungs- und Kontaktdaten", "claim_device_explanation": "Um Geräte zu beanspruchen, können Sie die folgende Tabelle verwenden. Wenn ein Gerät bereits von einem Benutzer beansprucht wurde, müssen Si<PERSON> zu dessen Details gehen und die Zuweisung aufheben, bevor Sie es beanspruchen können.", "devices_claimed_one": "{{count}} <PERSON><PERSON><PERSON>", "devices_claimed_other": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "devices_to_claim_one": "{{count}} <PERSON><PERSON> beanspruchendes Gerät", "devices_to_claim_other": "{{count}} <PERSON><PERSON> beanspruchende Geräte", "error_claiming": "<PERSON><PERSON> beim <PERSON>: {{serials}}", "error_removing_claim": "<PERSON><PERSON> beim En<PERSON>fer<PERSON> von Anspruch(en) auf: {{serials}}", "no_subscribers_found": "<PERSON><PERSON> gefunden", "one": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "other": "Abonnenten", "reactivate_explanation": "Möchten Sie diesen Abonnenten wirklich reaktivieren?", "reactivate_title": "Abonnent reaktivieren", "title": "Abonnenten"}, "system": {"advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backend_logs": "Back-End-<PERSON><PERSON><PERSON>", "configuration": "Aufbau", "could_not_retrieve": "<PERSON><PERSON>: {{name}} Systeminformationen konnten nicht abgerufen werden", "endpoint": "Endpunkt", "hostname": "Hostname", "info": "Systeminformationen", "level": "Protokollstufe", "logging": "Protokollierung", "no_log_levels": "<PERSON>ine gemeldeten Protokollebenen", "os": "Betriebssystem", "processors": "Prozessoren", "reload_chosen_subsystems": "Ausgewählte Subsysteme neu laden", "secrets": "Geheimnisse", "secrets_create": "Geheimnis erstellen", "secrets_one": "Geheimnis", "services": "dienstleistungen", "start": "Start", "subsystems": "Subsysteme", "success_reload": "Reload-Befehl erfolgreich gesendet!", "systems_to_reload": "Wählen Sie Systeme zum Neuladen aus", "title": "System", "update_level_success": "Loglevel aktualisiert!", "update_levels": "Aktualisieren", "uptime": "Betriebszeit", "version": "Ausführung"}, "table": {"columns": "S<PERSON>ulen", "columns_hidden_one": "{{count}} Spalte ausgeblendet", "columns_hidden_other": "{{count}} Spalten ausgeblendet", "display_column": "Anzeige", "drag_always_show": "<PERSON>e können diese Spalte nicht ausblenden, aber ihre Position ändern", "drag_explanation": "Ziehen und Ablegen zum Neuordnen und Ändern der Spaltensichtbarkeit", "drag_locked": "Diese Säule ist in ihrer Position arretiert", "export_current_page": "Nur aktuelle Seite", "first_page": "Erste Seite", "go_to_page": "Zur Seite gehen", "hide_column": "verbergen", "last_page": "Letzte Seite", "next_page": "Nächste Seite", "page": "Seite", "preferences": "Tabelleneinstellungen", "previous_page": "Vorherige Seite", "reset": "Einstellungen zurücksetzen", "settings": "die Einstellungen"}, "user": {"email_not_validated": "E-Mail nicht validiert", "error_fetching": "Fehler beim Abrufen der Benutzerinformationen: {{e}}", "password": "Passwort", "role": "<PERSON><PERSON>", "suspended": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>"}, "users": {"change_password": "Kennwort ändern", "email_validation": "E-MAIL-VALIDIERUNG", "error_fetching": "<PERSON><PERSON> beim Abrufen der Nutzer: {{e}}", "error_sending_validation": "<PERSON><PERSON> beim Senden der E-Mail-Validierung: {{e}}", "last_login": "Letzte Anmeldung", "login_id": "Anmelde-ID", "one": "<PERSON><PERSON><PERSON>", "re_validate_email": "E-Mail erneut validieren", "reactivate_user": "Benutzer reaktivieren", "reset_mfa": "MFA zurücksetzen", "reset_mfa_success": "Benutzer-MFA erfolgreich zurückgesetzt!", "reset_password": "Passwort zurücksetzen", "reset_password_error": "<PERSON><PERSON> beim <PERSON>, das Benutzerkennwort zurückzusetzen: {{e}}", "reset_password_success": "Die E-Mail zum Zurücksetzen des Passworts wurde erfolgreich an die E-Mail-Adresse des Benutzers gesendet", "role": "<PERSON><PERSON>", "send_validation": "E-Mail-Validierung senden", "send_validation_explanation": "Möchten Sie den E-Mail-Bestätigungslink erneut senden?", "stop_suspension": "Reaktivieren", "success_sending_validation": "Bestätigungs-E-Mail gesendet!", "suspend": "<PERSON><PERSON>tz<PERSON>", "suspend_success": "Der Benutzer ist jetzt gesperrt", "suspended": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "waitiing_for_email_verification": "E-Mail nicht verifiziert"}, "venues": {"confirm_remove_contact": "<PERSON><PERSON>chten Sie diesen Kontakt von diesem Veranstaltungsort entfernen?", "create_child": "Untergeordneten Veranstaltungsort erstellen", "error_remove_contact": "<PERSON><PERSON> beim Versuch, den Kontakt zu entfernen: {{e}}", "error_update_devices": "<PERSON>hler beim Starten des Geräteupdates: {{e}}", "go_to_page": "Zur Seite gehen", "one": "Tagungsort", "reboot_all_devices": "Alle Geräte neu starten", "sub_one": "Nebenschauplatz", "sub_other": "Unterorte", "subvenues": "Unterorte", "successfully_reboot_devices": "<PERSON><PERSON><PERSON><PERSON> von {{num}} Geräten!", "successfully_removed_contact": "Kontakt erfolgreich entfernt!", "successfully_update_devices": " {{num}} Geräte werden aktualisiert!", "title": "Veranstaltungsorte", "update_all_devices": "Alle Gerätekonfigurationen aktualisieren", "update_success": "Veranstaltungsort aktualisiert!", "upgrade_all_devices": "Aktualisieren Sie die Firmware aller Geräte", "upgrade_all_devices_error": "<PERSON><PERSON> beim Aktualisieren von Geräten: {{e}}", "upgrade_all_devices_success": "Upgrade von Geräten erfolgreich gestartet!", "upgrade_options_available": "Hier sind alle verfügbaren Revisionen, bitte wählen Sie diejenige aus, auf die ALLE Geräte dieses Veranstaltungsortes aktualisiert werden sollen", "use_existing": "<PERSON><PERSON><PERSON>", "use_existing_contacts": "Verwenden Sie vorhandene Kontakte", "use_this_contact": "Verwenden Sie diesen Kontakt"}}