import{l as he,r as d,n as Se,m as hs,q as H,s as yt,t as Ct,j as s,v as N,w as B,aG as gs,x as xs,aH as ys,aI as Cs,aJ as Ve,aK as oe,aL as bs,aM as js,aN as De,aO as Ss,aP as bt,aQ as vs,p as jt,R as j,b as T,aR as As,aA as K,B as F,aB as ks,aS as ws,Q as Es,aT as Is,aU as Ms,u as Os,aV as Rs,T as ee,M as St,O as U,c as G,aW as Ds,aX as Ue,aY as We,J as Y,aZ as _s,a_ as Ts,a$ as Fs,b0 as Ns,b1 as Ls,b2 as _e,b3 as $s,b4 as Gs,b5 as Ps,b6 as vt,b7 as qs,b8 as Hs,W as At,A as ze,V as kt,H as W,z as wt,b9 as Bs,ba as Zs,bb as Te,ax as L,bc as de,bd as me,be as fe,bf as ve,bg as Ae,y as Qs,ao as Vs,a as ge,bh as Us,bi as Ws,bj as zs,ai as Fe,e as Js,ag as Ks,bk as Ne,bl as Xs,X as ke,bm as Ys,D as Je,bn as en,$ as ae,bo as tn,a4 as sn,C as nn}from"./index-CCDcquaz.js";import{u as rn,S as on}from"./useMutationResult-BK5EZibL.js";import{b as an}from"./Form-CDHrBU_a.js";import{W as ln}from"./CustomTable-B4Am8LRY.js";import{u as cn,a as un,b as dn,c as mn,T as Ke,d as fn,C as te,W as pn,R as ie,s as Xe,e as hn,f as gn,g as xn,h as yn,i as Cn,j as bn,k as Et,l as jn,F as le,S as Ye,m as et,n as tt,o as st,p as nt,q as Sn,r as vn,t as An,v as rt,w as kn,x as wn,y as En}from"./SearchInput-Ct0cxdxv.js";import{u as In}from"./useDataGrid-C8ZjC9Fo.js";import{s as Mn}from"./dateFormatting-yHFQ8l7H.js";import{u as On,C as It,H as Rn}from"./useCommandModal-B9ChXmyw.js";import{F as Dn,a as _n}from"./index-B9-ToZA1.js";import{F as Tn,u as Fn,b as Nn,a as Ln}from"./useUrlSync-DjGOfs83.js";import{c as $n}from"./use-descendant-DTjabNrV.js";import{u as Gn}from"./useFastField-C4vjyPS0.js";import"./SiteContext-D-2Xr-_P.js";/* empty css             */const ne=e=>{const{condition:t,message:n}=e},[Pn,xe]=he({name:"AccordionStylesContext",hookName:"useAccordionStyles",providerName:"<Accordion />"}),[qn,Le]=he({name:"AccordionItemContext",hookName:"useAccordionItemContext",providerName:"<AccordionItem />"}),[Hn,pa,Bn,Zn]=$n();function Qn(e){const{onChange:t,defaultIndex:n,index:r,allowMultiple:o,allowToggle:l,...a}=e;Wn(e),zn(e);const u=Bn(),[i,c]=d.useState(-1);d.useEffect(()=>()=>{c(-1)},[]);const[m,h]=Gn({value:r,defaultValue(){return o?n??[]:n??-1},onChange:t});return{index:m,setIndex:h,htmlProps:a,getAccordionItemProps:g=>{let p=!1;return g!==null&&(p=Array.isArray(m)?m.includes(g):m===g),{isOpen:p,onChange:b=>{if(g!==null)if(o&&Array.isArray(m)){const S=b?m.concat(g):m.filter(A=>A!==g);h(S)}else b?h(g):l&&h(-1)}}},focusedIndex:i,setFocusedIndex:c,descendants:u}}const[Vn,$e]=he({name:"AccordionContext",hookName:"useAccordionContext",providerName:"Accordion"});function Un(e){const{isDisabled:t,isFocusable:n,id:r,...o}=e,{getAccordionItemProps:l,setFocusedIndex:a}=$e(),u=d.useRef(null),i=d.useId(),c=r??i,m=`accordion-button-${c}`,h=`accordion-panel-${c}`;Jn(e);const{register:C,index:g,descendants:p}=Zn({disabled:t&&!n}),{isOpen:y,onChange:b}=l(g===-1?null:g);Kn({isOpen:y,isDisabled:t});const S=()=>{b==null||b(!0)},A=()=>{b==null||b(!1)},v=d.useCallback(()=>{b==null||b(!y),a(g)},[g,a,y,b]),E=d.useCallback(O=>{const _={ArrowDown:()=>{const k=p.nextEnabled(g);k==null||k.node.focus()},ArrowUp:()=>{const k=p.prevEnabled(g);k==null||k.node.focus()},Home:()=>{const k=p.firstEnabled();k==null||k.node.focus()},End:()=>{const k=p.lastEnabled();k==null||k.node.focus()}}[O.key];_&&(O.preventDefault(),_(O))},[p,g]),M=d.useCallback(()=>{a(g)},[a,g]),D=d.useCallback(function(I={},_=null){return{...I,type:"button",ref:hs(C,u,_),id:m,disabled:!!t,"aria-expanded":!!y,"aria-controls":h,onClick:Se(I.onClick,v),onFocus:Se(I.onFocus,M),onKeyDown:Se(I.onKeyDown,E)}},[m,t,y,v,M,E,h,C]),w=d.useCallback(function(I={},_=null){return{...I,ref:_,role:"region",id:h,"aria-labelledby":m,hidden:!y}},[m,y,h]);return{isOpen:y,isDisabled:t,isFocusable:n,onOpen:S,onClose:A,getButtonProps:D,getPanelProps:w,htmlProps:o}}function Wn(e){const t=e.index||e.defaultIndex,n=t!=null&&!Array.isArray(t)&&e.allowMultiple;ne({condition:!!n,message:`If 'allowMultiple' is passed, then 'index' or 'defaultIndex' must be an array. You passed: ${typeof t},`})}function zn(e){ne({condition:!!(e.allowMultiple&&e.allowToggle),message:"If 'allowMultiple' is passed, 'allowToggle' will be ignored. Either remove 'allowToggle' or 'allowMultiple' depending on whether you want multiple accordions visible or not"})}function Jn(e){ne({condition:!!(e.isFocusable&&!e.isDisabled),message:`Using only 'isFocusable', this prop is reserved for situations where you pass 'isDisabled' but you still want the element to receive focus (A11y). Either remove it or pass 'isDisabled' as well.
    `})}function Kn(e){ne({condition:e.isOpen&&!!e.isDisabled,message:"Cannot open a disabled accordion item"})}const Mt=H(function({children:t,reduceMotion:n,...r},o){const l=yt("Accordion",r),a=Ct(r),{htmlProps:u,descendants:i,...c}=Qn(a),m=d.useMemo(()=>({...c,reduceMotion:!!n}),[c,n]);return s.jsx(Hn,{value:i,children:s.jsx(Vn,{value:m,children:s.jsx(Pn,{value:l,children:s.jsx(N.div,{ref:o,...u,className:B("chakra-accordion",r.className),__css:l.root,children:t})})})})});Mt.displayName="Accordion";const we=H(function(t,n){const{getButtonProps:r}=Le(),o=r(t,n),a={display:"flex",alignItems:"center",width:"100%",outline:0,...xe().button};return s.jsx(N.button,{...o,className:B("chakra-accordion__button",t.className),__css:a})});we.displayName="AccordionButton";function Ee(e){const{isOpen:t,isDisabled:n}=Le(),{reduceMotion:r}=$e(),o=B("chakra-accordion__icon",e.className),l=xe(),a={opacity:n?.4:1,transform:t?"rotate(-180deg)":void 0,transition:r?void 0:"transform 0.2s",transformOrigin:"center",...l.icon};return s.jsx(gs,{viewBox:"0 0 24 24","aria-hidden":!0,className:o,__css:a,...e,children:s.jsx("path",{fill:"currentColor",d:"M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"})})}Ee.displayName="AccordionIcon";const Ie=H(function(t,n){const{children:r,className:o}=t,{htmlProps:l,...a}=Un(t),u=xe(),i=xs({...u.container,overflowAnchor:"none"}),c=d.useMemo(()=>a,[a]);return s.jsx(qn,{value:c,children:s.jsx(N.div,{ref:n,...l,className:B("chakra-accordion__item",o),__css:i,children:typeof r=="function"?r({isExpanded:!!a.isOpen,isDisabled:!!a.isDisabled}):r})})});Ie.displayName="AccordionItem";const Xn=e=>e!=null&&parseInt(e.toString(),10)>0,ot={exit:{height:{duration:.2,ease:oe.ease},opacity:{duration:.3,ease:oe.ease}},enter:{height:{duration:.3,ease:oe.ease},opacity:{duration:.4,ease:oe.ease}}},Yn={exit:({animateOpacity:e,startingHeight:t,transition:n,transitionEnd:r,delay:o})=>({...e&&{opacity:Xn(t)?1:0},height:t,transitionEnd:r==null?void 0:r.exit,transition:(n==null?void 0:n.exit)??Ve.exit(ot.exit,o)}),enter:({animateOpacity:e,endingHeight:t,transition:n,transitionEnd:r,delay:o})=>({...e&&{opacity:1},height:t,transitionEnd:r==null?void 0:r.enter,transition:(n==null?void 0:n.enter)??Ve.enter(ot.enter,o)})},Ot=d.forwardRef((e,t)=>{const{in:n,unmountOnExit:r,animateOpacity:o=!0,startingHeight:l=0,endingHeight:a="auto",style:u,className:i,transition:c,transitionEnd:m,animatePresenceProps:h,...C}=e,[g,p]=d.useState(!1);d.useEffect(()=>{const v=setTimeout(()=>{p(!0)});return()=>clearTimeout(v)},[]),ne({condition:Number(l)>0&&!!r,message:"startingHeight and unmountOnExit are mutually exclusive. You can't use them together"});const y=parseFloat(l.toString())>0,b={startingHeight:l,endingHeight:a,animateOpacity:o,transition:g?c:{enter:{duration:0}},transitionEnd:{enter:m==null?void 0:m.enter,exit:r?m==null?void 0:m.exit:{...m==null?void 0:m.exit,display:y?"block":"none"}}},S=r?n:!0,A=n||r?"enter":"exit";return s.jsx(ys,{...h,initial:!1,custom:b,children:S&&s.jsx(Cs.div,{ref:t,...C,className:B("chakra-collapse",i),style:{overflow:"hidden",display:"block",...u},custom:b,variants:Yn,initial:r?"exit":!1,animate:A,exit:"exit"})})});Ot.displayName="Collapse";const Me=H(function(t,n){const{className:r,motionProps:o,...l}=t,{reduceMotion:a}=$e(),{getPanelProps:u,isOpen:i}=Le(),c=u(l,n),m=B("chakra-accordion__panel",r),h=xe();a||delete c.hidden;const C=s.jsx(N.div,{...c,__css:h.panel,className:m});return a?C:s.jsx(Ot,{in:i,...o,children:C})});Me.displayName="AccordionPanel";const[ha,er]=he({name:"CheckboxGroupContext",strict:!1});function tr(e){return s.jsx(N.svg,{width:"1.2em",viewBox:"0 0 12 10",style:{fill:"none",strokeWidth:2,stroke:"currentColor",strokeDasharray:16},...e,children:s.jsx("polyline",{points:"1.5 6 4.5 9 10.5 1"})})}function sr(e){return s.jsx(N.svg,{width:"1.2em",viewBox:"0 0 24 24",style:{stroke:"currentColor",strokeWidth:4},...e,children:s.jsx("line",{x1:"21",x2:"3",y1:"12",y2:"12"})})}function nr(e){const{isIndeterminate:t,isChecked:n,...r}=e,o=t?sr:tr;return n||t?s.jsx(N.div,{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"},children:s.jsx(o,{...r})}):null}function rr(e){const[t,n]=d.useState(e),[r,o]=d.useState(!1);return e!==t&&(o(!0),n(e)),r}const or={display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"top",userSelect:"none",flexShrink:0},ar={cursor:"pointer",display:"inline-flex",alignItems:"center",verticalAlign:"top",position:"relative"},ir=De({from:{opacity:0,strokeDashoffset:16,transform:"scale(0.95)"},to:{opacity:1,strokeDashoffset:0,transform:"scale(1)"}}),lr=De({from:{opacity:0},to:{opacity:1}}),cr=De({from:{transform:"scaleX(0.65)"},to:{transform:"scaleX(1)"}}),ue=H(function(t,n){const r=er(),o={...r,...t},l=yt("Checkbox",o),a=Ct(t),{spacing:u="0.5rem",className:i,children:c,iconColor:m,iconSize:h,icon:C=s.jsx(nr,{}),isChecked:g,isDisabled:p=r==null?void 0:r.isDisabled,onChange:y,inputProps:b,...S}=a;let A=g;r!=null&&r.value&&a.value&&(A=r.value.includes(a.value));let v=y;r!=null&&r.onChange&&a.value&&(v=bs(r.onChange,y));const{state:E,getInputProps:M,getCheckboxProps:D,getLabelProps:w,getRootProps:O}=cn({...S,isDisabled:p,isChecked:A,onChange:v}),I=rr(E.isChecked),_=d.useMemo(()=>({animation:I?E.isIndeterminate?`${lr} 20ms linear, ${cr} 200ms linear`:`${ir} 200ms linear`:void 0,...l.icon,...js({fontSize:h,color:m})}),[m,h,I,E.isIndeterminate,l.icon]),k=d.cloneElement(C,{__css:_,isIndeterminate:E.isIndeterminate,isChecked:E.isChecked});return s.jsxs(N.label,{__css:{...ar,...l.container},className:B("chakra-checkbox",i),...O(),children:[s.jsx("input",{className:"chakra-checkbox__input",...M(b,n)}),s.jsx(N.span,{__css:{...or,...l.control},className:"chakra-checkbox__control",...D(),children:k}),c&&s.jsx(N.span,{className:"chakra-checkbox__label",...w(),__css:{marginStart:u,...l.label},children:c})]})});ue.displayName="Checkbox";const Rt=H(function(t,n){const{templateAreas:r,gap:o,rowGap:l,columnGap:a,column:u,row:i,autoFlow:c,autoRows:m,templateRows:h,autoColumns:C,templateColumns:g,...p}=t,y={display:"grid",gridTemplateAreas:r,gridGap:o,gridRowGap:l,gridColumnGap:a,gridAutoColumns:C,gridColumn:u,gridRow:i,gridAutoFlow:c,gridAutoRows:m,gridTemplateRows:h,gridTemplateColumns:g};return s.jsx(N.div,{ref:n,__css:y,...p})});Rt.displayName="Grid";const Ge=H(function(t,n){const{columns:r,spacingX:o,spacingY:l,spacing:a,minChildWidth:u,...i}=t,c=Ss(),m=u?dr(u,c):mr(r);return s.jsx(Rt,{ref:n,gap:a,columnGap:o,rowGap:l,templateColumns:m,...i})});Ge.displayName="SimpleGrid";function ur(e){return typeof e=="number"?`${e}px`:e}function dr(e,t){return bt(e,n=>{const r=vs("sizes",n,ur(n))(t);return n===null?null:`repeat(auto-fit, minmax(${r}, 1fr))`})}function mr(e){return bt(e,t=>t===null?null:`repeat(${t}, minmax(0, 1fr))`)}const fr=new Map([["bold",d.createElement(d.Fragment,null,d.createElement("path",{d:"M209.88,69.83A115.19,115.19,0,0,0,128,36h-.41C63.85,36.22,12,88.76,12,153.13V176a20,20,0,0,0,20,20H224a20,20,0,0,0,20-20V152A115.25,115.25,0,0,0,209.88,69.83ZM220,172H127.32l46.44-65A12,12,0,1,0,154.24,93L97.82,172H36V153.13c0-1.72,0-3.43.14-5.13H56a12,12,0,0,0,0-24H40.62c10.91-33.39,40-58.52,75.38-63.21V80a12,12,0,0,0,24,0V60.8A92,92,0,0,1,215.66,124H200a12,12,0,0,0,0,24h19.9c.06,1.33.1,2.66.1,4Z"}))],["duotone",d.createElement(d.Fragment,null,d.createElement("path",{d:"M232,152v24a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V153.13C24,95.65,70.15,48.2,127.63,48A104,104,0,0,1,232,152Z",opacity:"0.2"}),d.createElement("path",{d:"M207.06,72.67A111.24,111.24,0,0,0,128,40h-.4C66.07,40.21,16,91,16,153.13V176a16,16,0,0,0,16,16H224a16,16,0,0,0,16-16V152A111.25,111.25,0,0,0,207.06,72.67ZM224,176H119.71l54.76-75.3a8,8,0,0,0-12.94-9.42L99.92,176H32V153.13c0-3.08.15-6.12.43-9.13H56a8,8,0,0,0,0-16H35.27c10.32-38.86,44-68.24,84.73-71.66V80a8,8,0,0,0,16,0V56.33A96.14,96.14,0,0,1,221,128H200a8,8,0,0,0,0,16h23.67c.21,2.65.33,5.31.33,8Z"}))],["fill",d.createElement(d.Fragment,null,d.createElement("path",{d:"M240,152v24a16,16,0,0,1-16,16H115.93a4,4,0,0,1-3.24-6.35L174.27,101a8.21,8.21,0,0,0-1.37-11.3,8,8,0,0,0-11.37,1.61l-72,99.06A4,4,0,0,1,86.25,192H32a16,16,0,0,1-16-16V153.13c0-1.79,0-3.57.13-5.33a4,4,0,0,1,4-3.8H48a8,8,0,0,0,8-8.53A8.17,8.17,0,0,0,47.73,128H23.92a4,4,0,0,1-3.87-5c12-43.84,49.66-77.13,95.52-82.28a4,4,0,0,1,4.43,4V72a8,8,0,0,0,8.53,8A8.17,8.17,0,0,0,136,71.73V44.67a4,4,0,0,1,4.43-4A112.18,112.18,0,0,1,236.23,123a4,4,0,0,1-3.88,5H208.27a8.17,8.17,0,0,0-8.25,7.47,8,8,0,0,0,8,8.53h27.92a4,4,0,0,1,4,3.86C240,149.23,240,150.61,240,152Z"}))],["light",d.createElement(d.Fragment,null,d.createElement("path",{d:"M205.65,74.08A109.26,109.26,0,0,0,128,42h-.39C67.17,42.21,18,92.06,18,153.13V176a14,14,0,0,0,14,14H224a14,14,0,0,0,14-14V152A109.3,109.3,0,0,0,205.65,74.08ZM226,176a2,2,0,0,1-2,2H115.78l57.07-78.47a6,6,0,0,0-9.7-7.06L100.94,178H32a2,2,0,0,1-2-2V153.13A102.36,102.36,0,0,1,30.62,142H56a6,6,0,0,0,0-12H32.71C42.6,88.4,78.53,56.86,122,54.19V80a6,6,0,0,0,12,0V54.19A98.05,98.05,0,0,1,223.53,130H200a6,6,0,0,0,0,12h25.5c.33,3.3.5,6.64.5,10Z"}))],["regular",d.createElement(d.Fragment,null,d.createElement("path",{d:"M207.06,72.67A111.24,111.24,0,0,0,128,40h-.4C66.07,40.21,16,91,16,153.13V176a16,16,0,0,0,16,16H224a16,16,0,0,0,16-16V152A111.25,111.25,0,0,0,207.06,72.67ZM224,176H119.71l54.76-75.3a8,8,0,0,0-12.94-9.42L99.92,176H32V153.13c0-3.08.15-6.12.43-9.13H56a8,8,0,0,0,0-16H35.27c10.32-38.86,44-68.24,84.73-71.66V80a8,8,0,0,0,16,0V56.33A96.14,96.14,0,0,1,221,128H200a8,8,0,0,0,0,16h23.67c.21,2.65.33,5.31.33,8Z"}))],["thin",d.createElement(d.Fragment,null,d.createElement("path",{d:"M204.23,75.5A107.37,107.37,0,0,0,127.62,44C68.28,44.21,20,93.16,20,153.13V176a12,12,0,0,0,12,12H224a12,12,0,0,0,12-12V152A107.25,107.25,0,0,0,204.23,75.5ZM228,176a4,4,0,0,1-4,4H111.85l59.38-81.65a4,4,0,1,0-6.46-4.7L102,180H32a4,4,0,0,1-4-4V153.13A103.42,103.42,0,0,1,28.84,140H56a4,4,0,0,0,0-8H30.21C39.59,87.66,77.84,53.93,124,52.09V80a4,4,0,0,0,8,0V52.08A100.08,100.08,0,0,1,226,132H200a4,4,0,0,0,0,8h27.29a101.6,101.6,0,0,1,.71,12Z"}))]]),pr=new Map([["bold",d.createElement(d.Fragment,null,d.createElement("path",{d:"M128,20A108,108,0,1,0,236,128,108.12,108.12,0,0,0,128,20Zm0,187a113.4,113.4,0,0,1-20.39-35h40.82a116.94,116.94,0,0,1-10,20.77A108.61,108.61,0,0,1,128,207Zm-26.49-59a135.42,135.42,0,0,1,0-40h53a135.42,135.42,0,0,1,0,40ZM44,128a83.49,83.49,0,0,1,2.43-20H77.25a160.63,160.63,0,0,0,0,40H46.43A83.49,83.49,0,0,1,44,128Zm84-79a113.4,113.4,0,0,1,20.39,35H107.59a116.94,116.94,0,0,1,10-20.77A108.61,108.61,0,0,1,128,49Zm50.73,59h30.82a83.52,83.52,0,0,1,0,40H178.75a160.63,160.63,0,0,0,0-40Zm20.77-24H173.71a140.82,140.82,0,0,0-15.5-34.36A84.51,84.51,0,0,1,199.52,84ZM97.79,49.64A140.82,140.82,0,0,0,82.29,84H56.48A84.51,84.51,0,0,1,97.79,49.64ZM56.48,172H82.29a140.82,140.82,0,0,0,15.5,34.36A84.51,84.51,0,0,1,56.48,172Zm101.73,34.36A140.82,140.82,0,0,0,173.71,172h25.81A84.51,84.51,0,0,1,158.21,206.36Z"}))],["duotone",d.createElement(d.Fragment,null,d.createElement("path",{d:"M224,128a96,96,0,1,1-96-96A96,96,0,0,1,224,128Z",opacity:"0.2"}),d.createElement("path",{d:"M128,24h0A104,104,0,1,0,232,128,104.12,104.12,0,0,0,128,24Zm88,104a87.61,87.61,0,0,1-3.33,24H174.16a157.44,157.44,0,0,0,0-48h38.51A87.61,87.61,0,0,1,216,128ZM102,168H154a115.11,115.11,0,0,1-26,45A115.27,115.27,0,0,1,102,168Zm-3.9-16a140.84,140.84,0,0,1,0-48h59.88a140.84,140.84,0,0,1,0,48ZM40,128a87.61,87.61,0,0,1,3.33-24H81.84a157.44,157.44,0,0,0,0,48H43.33A87.61,87.61,0,0,1,40,128ZM154,88H102a115.11,115.11,0,0,1,26-45A115.27,115.27,0,0,1,154,88Zm52.33,0H170.71a135.28,135.28,0,0,0-22.3-45.6A88.29,88.29,0,0,1,206.37,88ZM107.59,42.4A135.28,135.28,0,0,0,85.29,88H49.63A88.29,88.29,0,0,1,107.59,42.4ZM49.63,168H85.29a135.28,135.28,0,0,0,22.3,45.6A88.29,88.29,0,0,1,49.63,168Zm98.78,45.6a135.28,135.28,0,0,0,22.3-45.6h35.66A88.29,88.29,0,0,1,148.41,213.6Z"}))],["fill",d.createElement(d.Fragment,null,d.createElement("path",{d:"M128,24h0A104,104,0,1,0,232,128,104.12,104.12,0,0,0,128,24Zm78.36,64H170.71a135.28,135.28,0,0,0-22.3-45.6A88.29,88.29,0,0,1,206.37,88ZM216,128a87.61,87.61,0,0,1-3.33,24H174.16a157.44,157.44,0,0,0,0-48h38.51A87.61,87.61,0,0,1,216,128ZM128,43a115.27,115.27,0,0,1,26,45H102A115.11,115.11,0,0,1,128,43ZM102,168H154a115.11,115.11,0,0,1-26,45A115.27,115.27,0,0,1,102,168Zm-3.9-16a140.84,140.84,0,0,1,0-48h59.88a140.84,140.84,0,0,1,0,48Zm50.35,61.6a135.28,135.28,0,0,0,22.3-45.6h35.66A88.29,88.29,0,0,1,148.41,213.6Z"}))],["light",d.createElement(d.Fragment,null,d.createElement("path",{d:"M128,26A102,102,0,1,0,230,128,102.12,102.12,0,0,0,128,26Zm81.57,64H169.19a132.58,132.58,0,0,0-25.73-50.67A90.29,90.29,0,0,1,209.57,90ZM218,128a89.7,89.7,0,0,1-3.83,26H171.81a155.43,155.43,0,0,0,0-52h42.36A89.7,89.7,0,0,1,218,128Zm-90,87.83a110,110,0,0,1-15.19-19.45A124.24,124.24,0,0,1,99.35,166h57.3a124.24,124.24,0,0,1-13.46,30.38A110,110,0,0,1,128,215.83ZM96.45,154a139.18,139.18,0,0,1,0-52h63.1a139.18,139.18,0,0,1,0,52ZM38,128a89.7,89.7,0,0,1,3.83-26H84.19a155.43,155.43,0,0,0,0,52H41.83A89.7,89.7,0,0,1,38,128Zm90-87.83a110,110,0,0,1,15.19,19.45A124.24,124.24,0,0,1,156.65,90H99.35a124.24,124.24,0,0,1,13.46-30.38A110,110,0,0,1,128,40.17Zm-15.46-.84A132.58,132.58,0,0,0,86.81,90H46.43A90.29,90.29,0,0,1,112.54,39.33ZM46.43,166H86.81a132.58,132.58,0,0,0,25.73,50.67A90.29,90.29,0,0,1,46.43,166Zm97,50.67A132.58,132.58,0,0,0,169.19,166h40.38A90.29,90.29,0,0,1,143.46,216.67Z"}))],["regular",d.createElement(d.Fragment,null,d.createElement("path",{d:"M128,24h0A104,104,0,1,0,232,128,104.12,104.12,0,0,0,128,24Zm88,104a87.61,87.61,0,0,1-3.33,24H174.16a157.44,157.44,0,0,0,0-48h38.51A87.61,87.61,0,0,1,216,128ZM102,168H154a115.11,115.11,0,0,1-26,45A115.27,115.27,0,0,1,102,168Zm-3.9-16a140.84,140.84,0,0,1,0-48h59.88a140.84,140.84,0,0,1,0,48ZM40,128a87.61,87.61,0,0,1,3.33-24H81.84a157.44,157.44,0,0,0,0,48H43.33A87.61,87.61,0,0,1,40,128ZM154,88H102a115.11,115.11,0,0,1,26-45A115.27,115.27,0,0,1,154,88Zm52.33,0H170.71a135.28,135.28,0,0,0-22.3-45.6A88.29,88.29,0,0,1,206.37,88ZM107.59,42.4A135.28,135.28,0,0,0,85.29,88H49.63A88.29,88.29,0,0,1,107.59,42.4ZM49.63,168H85.29a135.28,135.28,0,0,0,22.3,45.6A88.29,88.29,0,0,1,49.63,168Zm98.78,45.6a135.28,135.28,0,0,0,22.3-45.6h35.66A88.29,88.29,0,0,1,148.41,213.6Z"}))],["thin",d.createElement(d.Fragment,null,d.createElement("path",{d:"M128,28h0A100,100,0,1,0,228,128,100.11,100.11,0,0,0,128,28Zm0,190.61c-6.33-6.09-23-24.41-31.27-54.61h62.54C151,194.2,134.33,212.52,128,218.61ZM94.82,156a140.42,140.42,0,0,1,0-56h66.36a140.42,140.42,0,0,1,0,56ZM128,37.39c6.33,6.09,23,24.41,31.27,54.61H96.73C105,61.8,121.67,43.48,128,37.39ZM169.41,100h46.23a92.09,92.09,0,0,1,0,56H169.41a152.65,152.65,0,0,0,0-56Zm43.25-8h-45a129.39,129.39,0,0,0-29.19-55.4A92.25,92.25,0,0,1,212.66,92ZM117.54,36.6A129.39,129.39,0,0,0,88.35,92h-45A92.25,92.25,0,0,1,117.54,36.6ZM40.36,100H86.59a152.65,152.65,0,0,0,0,56H40.36a92.09,92.09,0,0,1,0-56Zm3,64h45a129.39,129.39,0,0,0,29.19,55.4A92.25,92.25,0,0,1,43.34,164Zm95.12,55.4A129.39,129.39,0,0,0,167.65,164h45A92.25,92.25,0,0,1,138.46,219.4Z"}))]]),Dt=d.forwardRef((e,t)=>d.createElement(jt,{ref:t,...e,weights:fr}));Dt.displayName="GaugeIcon";const hr=Dt,_t=d.forwardRef((e,t)=>d.createElement(jt,{ref:t,...e,weights:pr}));_t.displayName="GlobeIcon";const at=_t,gr=({device:e,refreshTable:t,onOpenScan:n,onOpenFactoryReset:r,onOpenTrace:o,onOpenEventQueue:l,onOpenConfigureModal:a,onOpenTelemetryModal:u,onOpenRebootModal:i})=>{const{t:c}=T(),[m,h]=d.useState(!1),{mutateAsync:C,isLoading:g}=As({serialNumber:e.serialNumber}),{mutateAsync:p}=un({serialNumber:e.serialNumber}),y=()=>C(e.serialNumber,{onSuccess:()=>{t(),h(!1)}}),{onSuccess:b,onError:S}=rn({objName:c("devices.one"),operationType:"blink"}),{refetch:A,isFetching:v}=dn({serialNumber:e.serialNumber,extraId:"inventory-modal"}),E=({key:k})=>{},M=()=>p(void 0,{onSuccess:()=>{b()},onError:k=>{S(k)}}),D=()=>a(e.serialNumber),w=()=>r(e.serialNumber),O=()=>o(e.serialNumber),I=s.jsxs(K,{onClick:E,children:[s.jsx(K.Item,{onClick:M,children:c("commands.blink")},"blink"),s.jsx(K.Item,{onClick:D,children:c("controller.configure.title")},"configuration"),s.jsx(K.Item,{onClick:w,children:c("commands.factory_reset")},"factoryReset"),s.jsx(K.Item,{onClick:O,children:c("controller.devices.trace")},"trace")]});c("crud.delete"),e.serialNumber,c("crud.delete_confirm",{obj:c("devices.one")}),F,c("common.cancel"),F,c("common.yes");const _=k=>{Es(`Are you sure you want to delete this ${k.serialNumber}?`,y)};return s.jsxs("div",{style:{display:"flex",gap:24,alignItems:"center"},children:[s.jsx(ks,{overlay:I,trigger:["click"],children:s.jsxs("a",{type:"link",style:{color:"#14C9BB",paddingLeft:0,display:"flex",alignItems:"center"},children:["Actions ",s.jsx(ws,{})]})}),s.jsx("a",{type:"link",style:{display:"flex",alignItems:"center",color:"#14C9BB"},onClick:()=>_(e),onFocus:k=>k.target.blur(),children:"Delete"})]})},xr=j.memo(gr);var Oe={exports:{}};function yr(e,t,n,r){function o(l){return l instanceof n?l:new n(function(a){a(l)})}return new(n||(n=Promise))(function(l,a){function u(m){try{c(r.next(m))}catch(h){a(h)}}function i(m){try{c(r.throw(m))}catch(h){a(h)}}function c(m){m.done?l(m.value):o(m.value).then(u,i)}c((r=r.apply(e,[])).next())})}function Cr(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var br=function e(t,n){if(t===n)return!0;if(t&&n&&typeof t=="object"&&typeof n=="object"){if(t.constructor!==n.constructor)return!1;var r,o,l;if(Array.isArray(t)){if(r=t.length,r!=n.length)return!1;for(o=r;o--!==0;)if(!e(t[o],n[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if(l=Object.keys(t),r=l.length,r!==Object.keys(n).length)return!1;for(o=r;o--!==0;)if(!Object.prototype.hasOwnProperty.call(n,l[o]))return!1;for(o=r;o--!==0;){var a=l[o];if(!e(t[a],n[a]))return!1}return!0}return t!==t&&n!==n},jr=Cr(br);const Re="__googleMapsScriptId";var q;(function(e){e[e.INITIALIZED=0]="INITIALIZED",e[e.LOADING=1]="LOADING",e[e.SUCCESS=2]="SUCCESS",e[e.FAILURE=3]="FAILURE"})(q||(q={}));class P{constructor({apiKey:t,authReferrerPolicy:n,channel:r,client:o,id:l=Re,language:a,libraries:u=[],mapIds:i,nonce:c,region:m,retries:h=3,url:C="https://maps.googleapis.com/maps/api/js",version:g}){if(this.callbacks=[],this.done=!1,this.loading=!1,this.errors=[],this.apiKey=t,this.authReferrerPolicy=n,this.channel=r,this.client=o,this.id=l||Re,this.language=a,this.libraries=u,this.mapIds=i,this.nonce=c,this.region=m,this.retries=h,this.url=C,this.version=g,P.instance){if(!jr(this.options,P.instance.options))throw new Error(`Loader must not be called again with different options. ${JSON.stringify(this.options)} !== ${JSON.stringify(P.instance.options)}`);return P.instance}P.instance=this}get options(){return{version:this.version,apiKey:this.apiKey,channel:this.channel,client:this.client,id:this.id,libraries:this.libraries,language:this.language,region:this.region,mapIds:this.mapIds,nonce:this.nonce,url:this.url,authReferrerPolicy:this.authReferrerPolicy}}get status(){return this.errors.length?q.FAILURE:this.done?q.SUCCESS:this.loading?q.LOADING:q.INITIALIZED}get failed(){return this.done&&!this.loading&&this.errors.length>=this.retries+1}createUrl(){let t=this.url;return t+="?callback=__googleMapsCallback&loading=async",this.apiKey&&(t+=`&key=${this.apiKey}`),this.channel&&(t+=`&channel=${this.channel}`),this.client&&(t+=`&client=${this.client}`),this.libraries.length>0&&(t+=`&libraries=${this.libraries.join(",")}`),this.language&&(t+=`&language=${this.language}`),this.region&&(t+=`&region=${this.region}`),this.version&&(t+=`&v=${this.version}`),this.mapIds&&(t+=`&map_ids=${this.mapIds.join(",")}`),this.authReferrerPolicy&&(t+=`&auth_referrer_policy=${this.authReferrerPolicy}`),t}deleteScript(){const t=document.getElementById(this.id);t&&t.remove()}load(){return this.loadPromise()}loadPromise(){return new Promise((t,n)=>{this.loadCallback(r=>{r?n(r.error):t(window.google)})})}importLibrary(t){return this.execute(),google.maps.importLibrary(t)}loadCallback(t){this.callbacks.push(t),this.execute()}setScript(){var t,n;if(document.getElementById(this.id)){this.callback();return}const r={key:this.apiKey,channel:this.channel,client:this.client,libraries:this.libraries.length&&this.libraries,v:this.version,mapIds:this.mapIds,language:this.language,region:this.region,authReferrerPolicy:this.authReferrerPolicy};Object.keys(r).forEach(l=>!r[l]&&delete r[l]),!((n=(t=window==null?void 0:window.google)===null||t===void 0?void 0:t.maps)===null||n===void 0)&&n.importLibrary||(l=>{let a,u,i,c="The Google Maps JavaScript API",m="google",h="importLibrary",C="__ib__",g=document,p=window;p=p[m]||(p[m]={});const y=p.maps||(p.maps={}),b=new Set,S=new URLSearchParams,A=()=>a||(a=new Promise((v,E)=>yr(this,void 0,void 0,function*(){var M;yield u=g.createElement("script"),u.id=this.id,S.set("libraries",[...b]+"");for(i in l)S.set(i.replace(/[A-Z]/g,D=>"_"+D[0].toLowerCase()),l[i]);S.set("callback",m+".maps."+C),u.src=this.url+"?"+S,y[C]=v,u.onerror=()=>a=E(Error(c+" could not load.")),u.nonce=this.nonce||((M=g.querySelector("script[nonce]"))===null||M===void 0?void 0:M.nonce)||"",g.head.append(u)})));y[h]?console.warn(c+" only loads once. Ignoring:",l):y[h]=(v,...E)=>b.add(v)&&A().then(()=>y[h](v,...E))})(r);const o=this.libraries.map(l=>this.importLibrary(l));o.length||o.push(this.importLibrary("core")),Promise.all(o).then(()=>this.callback(),l=>{const a=new ErrorEvent("error",{error:l});this.loadErrorCallback(a)})}reset(){this.deleteScript(),this.done=!1,this.loading=!1,this.errors=[],this.onerrorEvent=null}resetIfRetryingFailed(){this.failed&&this.reset()}loadErrorCallback(t){if(this.errors.push(t),this.errors.length<=this.retries){const n=this.errors.length*Math.pow(2,this.errors.length);console.error(`Failed to load Google Maps script, retrying in ${n} ms.`),setTimeout(()=>{this.deleteScript(),this.setScript()},n)}else this.onerrorEvent=t,this.callback()}callback(){this.done=!0,this.loading=!1,this.callbacks.forEach(t=>{t(this.onerrorEvent)}),this.callbacks=[]}execute(){if(this.resetIfRetryingFailed(),!this.loading)if(this.done)this.callback();else{if(window.google&&window.google.maps&&window.google.maps.version){console.warn("Google Maps already loaded outside @googlemaps/js-api-loader. This may result in undesirable behavior as options and script parameters may not match."),this.callback();return}this.loading=!0,this.setScript()}}}const Sr=Object.freeze(Object.defineProperty({__proto__:null,DEFAULT_ID:Re,Loader:P,get LoaderStatus(){return q}},Symbol.toStringTag,{value:"Module"})),vr=Is(Sr);(function(e,t){(function(n,r){r(t,vr,d)})(Ms,function(n,r,o){function l(i){return i&&typeof i=="object"&&"default"in i?i:{default:i}}var a=l(o);n.Status=void 0,function(i){i.LOADING="LOADING",i.FAILURE="FAILURE",i.SUCCESS="SUCCESS"}(n.Status||(n.Status={}));const u=({children:i,render:c,callback:m,...h})=>{const[C,g]=o.useState(n.Status.LOADING);return o.useEffect(()=>{const p=new r.Loader(h),y=b=>{m&&m(b,p),g(b)};y(n.Status.LOADING),p.load().then(()=>y(n.Status.SUCCESS),()=>y(n.Status.FAILURE))},[]),C===n.Status.SUCCESS&&i?a.default.createElement(a.default.Fragment,null,i):c?c(C):a.default.createElement(a.default.Fragment,null)};n.Wrapper=u,Object.defineProperty(n,"__esModule",{value:!0})})})(Oe,Oe.exports);var Ar=Oe.exports;const it=e=>e!=null&&typeof e=="object"&&Number.isFinite(e.lat)&&Number.isFinite(e.lng);var kr=Object.getOwnPropertyNames,wr=Object.getOwnPropertySymbols,Er=Object.prototype.hasOwnProperty;function lt(e,t){return function(r,o,l){return e(r,o,l)&&t(r,o,l)}}function ce(e){return function(n,r,o){if(!n||!r||typeof n!="object"||typeof r!="object")return e(n,r,o);var l=o.cache,a=l.get(n),u=l.get(r);if(a&&u)return a===r&&u===n;l.set(n,r),l.set(r,n);var i=e(n,r,o);return l.delete(n),l.delete(r),i}}function ct(e){return kr(e).concat(wr(e))}var Ir=Object.hasOwn||function(e,t){return Er.call(e,t)};function Z(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var Mr="__v",Or="__o",Rr="_owner",ut=Object.getOwnPropertyDescriptor,dt=Object.keys;function Dr(e,t,n){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(!n.equals(e[r],t[r],r,r,e,t,n))return!1;return!0}function _r(e,t){return Z(e.getTime(),t.getTime())}function Tr(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function Fr(e,t){return e===t}function mt(e,t,n){var r=e.size;if(r!==t.size)return!1;if(!r)return!0;for(var o=new Array(r),l=e.entries(),a,u,i=0;(a=l.next())&&!a.done;){for(var c=t.entries(),m=!1,h=0;(u=c.next())&&!u.done;){if(o[h]){h++;continue}var C=a.value,g=u.value;if(n.equals(C[0],g[0],i,h,e,t,n)&&n.equals(C[1],g[1],C[0],g[0],e,t,n)){m=o[h]=!0;break}h++}if(!m)return!1;i++}return!0}var Nr=Z;function Lr(e,t,n){var r=dt(e),o=r.length;if(dt(t).length!==o)return!1;for(;o-- >0;)if(!Tt(e,t,n,r[o]))return!1;return!0}function X(e,t,n){var r=ct(e),o=r.length;if(ct(t).length!==o)return!1;for(var l,a,u;o-- >0;)if(l=r[o],!Tt(e,t,n,l)||(a=ut(e,l),u=ut(t,l),(a||u)&&(!a||!u||a.configurable!==u.configurable||a.enumerable!==u.enumerable||a.writable!==u.writable)))return!1;return!0}function $r(e,t){return Z(e.valueOf(),t.valueOf())}function Gr(e,t){return e.source===t.source&&e.flags===t.flags}function ft(e,t,n){var r=e.size;if(r!==t.size)return!1;if(!r)return!0;for(var o=new Array(r),l=e.values(),a,u;(a=l.next())&&!a.done;){for(var i=t.values(),c=!1,m=0;(u=i.next())&&!u.done;){if(!o[m]&&n.equals(a.value,u.value,a.value,u.value,e,t,n)){c=o[m]=!0;break}m++}if(!c)return!1}return!0}function Pr(e,t){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(e[n]!==t[n])return!1;return!0}function qr(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function Tt(e,t,n,r){return(r===Rr||r===Or||r===Mr)&&(e.$$typeof||t.$$typeof)?!0:Ir(t,r)&&n.equals(e[r],t[r],r,r,e,t,n)}var Hr="[object Arguments]",Br="[object Boolean]",Zr="[object Date]",Qr="[object Error]",Vr="[object Map]",Ur="[object Number]",Wr="[object Object]",zr="[object RegExp]",Jr="[object Set]",Kr="[object String]",Xr="[object URL]",Yr=Array.isArray,pt=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,ht=Object.assign,eo=Object.prototype.toString.call.bind(Object.prototype.toString);function to(e){var t=e.areArraysEqual,n=e.areDatesEqual,r=e.areErrorsEqual,o=e.areFunctionsEqual,l=e.areMapsEqual,a=e.areNumbersEqual,u=e.areObjectsEqual,i=e.arePrimitiveWrappersEqual,c=e.areRegExpsEqual,m=e.areSetsEqual,h=e.areTypedArraysEqual,C=e.areUrlsEqual;return function(p,y,b){if(p===y)return!0;if(p==null||y==null)return!1;var S=typeof p;if(S!==typeof y)return!1;if(S!=="object")return S==="number"?a(p,y,b):S==="function"?o(p,y,b):!1;var A=p.constructor;if(A!==y.constructor)return!1;if(A===Object)return u(p,y,b);if(Yr(p))return t(p,y,b);if(pt!=null&&pt(p))return h(p,y,b);if(A===Date)return n(p,y,b);if(A===RegExp)return c(p,y,b);if(A===Map)return l(p,y,b);if(A===Set)return m(p,y,b);var v=eo(p);return v===Zr?n(p,y,b):v===zr?c(p,y,b):v===Vr?l(p,y,b):v===Jr?m(p,y,b):v===Wr?typeof p.then!="function"&&typeof y.then!="function"&&u(p,y,b):v===Xr?C(p,y,b):v===Qr?r(p,y,b):v===Hr?u(p,y,b):v===Br||v===Ur||v===Kr?i(p,y,b):!1}}function so(e){var t=e.circular,n=e.createCustomConfig,r=e.strict,o={areArraysEqual:r?X:Dr,areDatesEqual:_r,areErrorsEqual:Tr,areFunctionsEqual:Fr,areMapsEqual:r?lt(mt,X):mt,areNumbersEqual:Nr,areObjectsEqual:r?X:Lr,arePrimitiveWrappersEqual:$r,areRegExpsEqual:Gr,areSetsEqual:r?lt(ft,X):ft,areTypedArraysEqual:r?X:Pr,areUrlsEqual:qr};if(n&&(o=ht({},o,n(o))),t){var l=ce(o.areArraysEqual),a=ce(o.areMapsEqual),u=ce(o.areObjectsEqual),i=ce(o.areSetsEqual);o=ht({},o,{areArraysEqual:l,areMapsEqual:a,areObjectsEqual:u,areSetsEqual:i})}return o}function no(e){return function(t,n,r,o,l,a,u){return e(t,n,u)}}function ro(e){var t=e.circular,n=e.comparator,r=e.createState,o=e.equals,l=e.strict;if(r)return function(i,c){var m=r(),h=m.cache,C=h===void 0?t?new WeakMap:void 0:h,g=m.meta;return n(i,c,{cache:C,equals:o,meta:g,strict:l})};if(t)return function(i,c){return n(i,c,{cache:new WeakMap,equals:o,meta:void 0,strict:l})};var a={cache:void 0,equals:o,meta:void 0,strict:l};return function(i,c){return n(i,c,a)}}$();$({strict:!0});$({circular:!0});$({circular:!0,strict:!0});$({createInternalComparator:function(){return Z}});$({strict:!0,createInternalComparator:function(){return Z}});$({circular:!0,createInternalComparator:function(){return Z}});$({circular:!0,createInternalComparator:function(){return Z},strict:!0});function $(e){e===void 0&&(e={});var t=e.circular,n=t===void 0?!1:t,r=e.createInternalComparator,o=e.createState,l=e.strict,a=l===void 0?!1:l,u=so(e),i=to(u),c=r?r(i):no(i);return ro({circular:n,comparator:i,createState:o,equals:c,strict:a})}const oo=$(e=>(t,n)=>it(t)||t instanceof google.maps.LatLng||it(n)||n instanceof google.maps.LatLng?new google.maps.LatLng(t).equals(new google.maps.LatLng(n)):e(t,n)),ao=e=>{const t=d.useRef();return oo(e,t.current)||(t.current=e),t.current},io=(e,t)=>{d.useEffect(e,t.map(ao))},lo=({style:e,onClick:t,onIdle:n,children:r,...o})=>{const l=d.useRef(null),[a,u]=d.useState();return io(()=>{a&&a.setOptions(o)},[a,o]),d.useEffect(()=>{l.current&&!a&&u(new window.google.maps.Map(l.current,{}))},[l,a]),d.useEffect(()=>{a&&(["click","idle"].forEach(i=>google.maps.event.clearListeners(a,i)),t&&a.addListener("click",t),n&&a.addListener("idle",()=>n(a)))},[a,t,n]),s.jsxs(s.Fragment,{children:[s.jsx("div",{ref:l,style:e}),d.Children.map(r,i=>d.isValidElement(i)?d.cloneElement(i,{map:a}):null)]})},co=d.memo(lo),uo=e=>{const[t,n]=d.useState();return d.useEffect(()=>(t||n(new google.maps.Marker),()=>{t&&t.setMap(null)}),[t]),d.useEffect(()=>{t&&t.setOptions(e)},[t,e]),null},mo=d.memo(uo),fo=async e=>Rs.get(`/systemSecret/${e.queryKey[1]}`).then(({data:t})=>t),po=({secret:e})=>Os(["secrets",e],fo,{staleTime:1e3*60*10,refetchInterval:1e3*60*10}),ho=({serialNumber:e,isCompact:t})=>{var i,c,m;const{t:n}=T(),[r,o]=d.useState(!1),l=po({secret:"google.maps.apikey"}),a=mn({serialNumber:e}),u=d.useMemo(()=>{var h;if((h=a.data)!=null&&h.gps)try{return{lat:parseFloat(a.data.gps.latitude),lng:parseFloat(a.data.gps.longitude)}}catch{return}},[(i=a.data)==null?void 0:i.gps]);return u?s.jsxs(s.Fragment,{children:[t?s.jsx(ee,{title:n("locations.view_gps"),children:s.jsx(at,{size:24,style:{color:"#1677ff",cursor:"pointer"},onClick:()=>o(!0)})}):s.jsx(F,{type:"link",onClick:()=>o(!0),icon:s.jsx(at,{size:18}),children:n("locations.view_gps")}),s.jsxs(St,{open:r,title:n("locations.one"),onCancel:()=>o(!1),footer:null,width:800,children:[s.jsx("div",{style:{display:"flex",marginBottom:16},children:s.jsxs(U,{layout:"inline",children:[s.jsx(U.Item,{label:n("locations.lat"),children:s.jsx(G.Text,{children:u.lat})}),s.jsx(U.Item,{label:n("locations.longitude"),style:{marginLeft:16},children:s.jsx(G.Text,{children:u.lng})}),s.jsx(U.Item,{label:n("locations.elevation"),style:{marginLeft:16},children:s.jsx(G.Text,{children:(m=(c=a.data)==null?void 0:c.gps)==null?void 0:m.elevation})})]})}),l.data?s.jsx("div",{style:{height:500},children:s.jsx(Ar.Wrapper,{apiKey:l.data.value,children:s.jsx(co,{center:u,style:{flexGrow:1,height:"100%"},zoom:10,children:s.jsx(mo,{position:u})})})}):null]})]}):null},go=({device:e})=>e.hasGPS?s.jsx(s.Fragment,{children:s.jsx(ho,{serialNumber:e.serialNumber,isCompact:!0})}):s.jsx("span",{children:"-"}),xo=({seconds:e})=>{const{t}=T(),n=d.useMemo(()=>e===void 0?"-":Mn(e,t),[e]);return s.jsx("div",{children:n})},yo=j.memo(xo),Co=({device:e})=>{if(!e.connected||e.started===0)return s.jsx("span",{children:"-"});const t=Math.floor(Date.now()/1e3-e.started);return s.jsx(yo,{seconds:t})},bo=({date:e,hidePrefix:t,isCompact:n})=>!e||e===0?"-":n?t?Ue(e).split(" ").slice(1).join(" "):Ue(e):t?We(e).split(" ").slice(1).join(" "):We(e),jo=({date:e,hidePrefix:t,isCompact:n})=>s.jsx(ee,{placement:"top",title:Ds(e??0),children:s.jsx("span",{children:bo({date:e,hidePrefix:t,isCompact:n})})}),gt=j.memo(jo),So={activeScan:!1,dfs:!0,bandwidth:""},vo=({modalProps:{isOpen:e},submit:t,formRef:n})=>{const{t:r}=T(),[o,l]=d.useState(Y());return d.useEffect(()=>{l(Y())},[e]),s.jsx(Dn,{innerRef:n,enableReinitialize:!0,initialValues:So,onSubmit:a=>t(a),children:s.jsx(_n,{children:s.jsxs(Ge,{minChildWidth:"200px",spacing:"10px",mb:4,children:[s.jsx(Ke,{name:"dfs",label:r("commands.override_dfs"),isRequired:!0}),s.jsx(Ke,{name:"activeScan",label:r("commands.active_scan"),isRequired:!0})]})})},o)},Ao=({title:e,left:t,right:n})=>{const r=_s("blue.50","blue.700");return s.jsxs(Ts,{bg:r,children:[e,t?s.jsx(Fs,{spacing:2,ml:2,children:t}):null,s.jsx(Ns,{}),n]})},ko=j.memo(Ao),wo=({modalProps:{isOpen:e,onClose:t},serialNumber:n})=>{const{t:r}=T(),{form:o,formRef:l}=fn(),[a,u]=d.useState(void 0),{data:i,mutateAsync:c,isLoading:m,reset:h}=Ls({serialNumber:n}),{isConfirmOpen:C,closeConfirm:g,closeModal:p,closeCancelAndForm:y}=On({isLoading:m,onModalClose:t}),b=v=>{c(v)},S=d.useMemo(()=>m?s.jsx(te,{my:100,children:s.jsx(_e,{size:"lg"})}):i?s.jsx(pn,{results:i,setCsvData:u}):s.jsx(vo,{modalProps:{isOpen:e,onOpen:()=>{},onClose:t},submit:b,formRef:l}),[i,m,l]),A=()=>{h(),u(void 0)};return d.useEffect(()=>{e&&A()},[e]),s.jsxs($s,{onClose:p,isOpen:e,size:"xl",scrollBehavior:"inside",children:[s.jsx(Gs,{}),s.jsxs(Ps,{maxWidth:{sm:"600px",md:"700px",lg:"800px",xl:"50%"},children:[s.jsx(ko,{title:r("commands.wifiscan"),right:s.jsxs(s.Fragment,{children:[a?s.jsx(It,{filename:`wifi_scan_${n}_${vt(new Date().getTime()/1e3)}.csv`,data:a,children:s.jsx(ie,{color:"gray",icon:s.jsx(Xe,{size:20}),isCompact:!0,label:r("common.download"),onClick:()=>{}})}):s.jsx(ie,{color:"gray",isDisabled:!0,icon:s.jsx(Xe,{size:20}),isCompact:!0,label:r("common.download"),onClick:()=>{}}),i!==void 0?s.jsx(ie,{color:"blue",icon:s.jsx(hn,{size:20}),label:r("common.back"),onClick:A,isLoading:m,ml:2}):s.jsx(ie,{color:"blue",icon:s.jsx(hr,{size:20}),label:r("commands.scan"),onClick:o.submitForm,isLoading:m,ml:2}),s.jsx(qs,{ml:2,onClick:p})]})}),s.jsx(Hs,{children:S})]}),s.jsx(gn,{modalProps:{isOpen:C,onOpen:()=>{},onClose:g},confirm:y,cancel:g})]})},Eo=({value:e,setValue:t,setFileName:n,refreshId:r,accept:o,isHidden:l,isStringFile:a,sizeLimit:u})=>{const[i,c]=d.useState(Y());let m;const h=g=>()=>{if(m){const p=m.result;p&&t(p,g)}},C=g=>{const p=g.target.files?g.target.files[0]:void 0;if(p)if(u&&p.size>u)c(Y());else{const y=URL.createObjectURL(p);a?(m=new FileReader,n&&n(p.name),m.onloadend=h(p),m.readAsText(p)):(t(y,p),n&&n(p.name??""))}};return d.useEffect(()=>{e===""&&c(Y())},[r,e]),s.jsx("div",{style:{display:l?"none":"block"},children:s.jsx(At,{type:"file",onChange:C,accept:o,style:{width:"280px",height:"36px"}},i)})},Io=j.memo(Eo),Mo=({serialNumber:e,modalProps:t})=>{var C,g,p;const{t:n}=T(),r=xn({serialNumber:e}),o=yn({serialNumber:e}),[l]=U.useForm(),[a,u]=j.useState(""),i=j.useCallback(y=>{u(y.target.value),l.setFieldsValue({configuration:y.target.value})},[l]),c=()=>{var b;const y=(b=o.data)!=null&&b.configuration?JSON.stringify(o.data.configuration,null,4):"";u(y),l.setFieldsValue({configuration:y})},m=j.useMemo(()=>{try{return a.trim()?(JSON.parse(a),!0):!1}catch{return!1}},[a]),h=()=>{var y;try{if((y=o.data)!=null&&y.blackListed){W.error({content:`${e} on the blacklist cannot be configured`,duration:5});return}const b=JSON.parse(a);r.mutate(b,{onSuccess:S=>{S.errorCode===0?(W.success({content:S.status==="pending"?"Command is pending! It will execute once the device connects":n("controller.configure.success"),duration:5}),t.onClose()):S.errorCode===1?(W.warning({content:`${(S==null?void 0:S.errorText)??"Unknown Warning"}`,duration:5}),t.onClose()):W.error({content:`${(S==null?void 0:S.errorText)??"Unknown Error"} (Code ${S.errorCode})`,duration:5}),t.onClose()}})}catch{}};return j.useEffect(()=>{t.isOpen?o.refetch():(u(""),l.resetFields())},[t.isOpen,l]),s.jsxs(Tn,{open:t.isOpen,title:n("controller.configure.title"),onCancel:t.onClose,onFinish:h,form:l,modalClass:"ampcon-max-modal",bodyStyle:{overflowY:"auto"},children:[r.error&&s.jsx(ze,{type:"error",message:s.jsxs("div",{style:{color:"#ff4d4f"},children:[s.jsx("div",{children:n("common.error")}),s.jsx("div",{children:(p=(g=(C=r.error)==null?void 0:C.response)==null?void 0:g.data)==null?void 0:p.ErrorDescription})]}),showIcon:!0,closable:!0,style:{marginTop:8,marginBottom:32}}),s.jsx(ze,{className:"custom-trace-alert",message:n("controller.configure.warning"),type:"info",showIcon:!0,closable:!0,style:{marginBottom:32,...r.error?{}:{marginTop:8}}}),s.jsx(U.Item,{name:"configuration",validateStatus:!m&&a.length>0?"error":"",help:!m&&a.length>0?n("controller.configure.invalid"):"",children:s.jsxs(kt,{direction:"vertical",style:{width:"100%",gap:0},children:[s.jsx(Io,{value:a,setValue:y=>{u(y),l.setFieldsValue({configuration:y})},refreshId:"1",accept:".json",isStringFile:!0}),s.jsx(F,{type:"primary",onClick:c,disabled:!o.data,style:{marginTop:32},children:"Current Configuration"}),s.jsx(At.TextArea,{value:a,onChange:i,rows:20,style:{width:"100%",marginTop:32}})]})})]})},Oo=j.memo(Mo),Ro=({serialNumber:e,modalProps:t})=>{const{t:n}=T(),r=wt(),o=Bs(m=>m.addEventListeners),{mutateAsync:l,isLoading:a}=Zs({serialNumber:e}),{onSuccess:u,onError:i}=Cn({objName:n("devices.one"),operationType:"reboot",refresh:()=>{o([{id:`device-connection-${e}`,type:"DEVICE_CONNECTION",serialNumber:e,callback:()=>{const m=`device-connection-notification-${e}`;r.isActive(m)||r({id:m,title:n("common.success"),description:n("controller.devices.finished_reboot",{serialNumber:e}),status:"success",duration:5e3,isClosable:!0,position:"top-right"})}},{id:`device-disconnected-${e}`,type:"DEVICE_DISCONNECTION",serialNumber:e,callback:()=>{const m=`device-disconnection-notification-${e}`;r.isActive(m)||r({id:m,title:n("common.success"),description:n("controller.devices.started_reboot",{serialNumber:e}),status:"success",duration:5e3,isClosable:!0,position:"top-right"})}}])}}),c=()=>l(void 0,{onSuccess:()=>{u(),t.onClose()},onError:m=>{i(m)}});return s.jsx(Te,{...t,title:n("commands.reboot"),topRightButtons:s.jsx(fe,{colorScheme:"blue",onClick:c,isLoading:a,children:n("commands.reboot")}),options:{modalSize:"sm"},children:s.jsx(L,{children:s.jsx(te,{mb:2,children:s.jsxs(de,{status:"info",w:"unset",children:[s.jsx(me,{}),n("commands.reboot_description")]})})})})},Do=({serialNumber:e,modalProps:t})=>{var m,h,C,g;const{t:n}=T(),r=bn(),{hasCopied:o,onCopy:l,setValue:a}=Et(JSON.stringify(r.data??{},null,2)),[u,i]=d.useState(-1),c=()=>{r.mutate(e)};return d.useEffect(()=>{a(JSON.stringify(r.data??{},null,2)),r.data&&i(0)},[r.data]),d.useEffect(()=>{t.isOpen&&c()},[t.isOpen]),s.jsx(Te,{...t,title:n("controller.queue.title"),topRightButtons:s.jsxs(s.Fragment,{children:[s.jsx(fe,{onClick:l,size:"md",colorScheme:"teal",children:o?`${n("common.copied")}!`:n("common.copy")}),s.jsx(jn,{onClick:c,isCompact:!0,isFetching:r.isLoading})]}),children:s.jsxs(s.Fragment,{children:[r.isLoading&&s.jsx(te,{my:"100px",children:s.jsx(_e,{size:"xl"})}),r.error&&s.jsxs(de,{status:"error",my:"100px",children:[s.jsx(me,{}),s.jsxs(L,{children:[s.jsx(ve,{children:n("common.error")}),s.jsx(Ae,{children:(C=(h=(m=r.error)==null?void 0:m.response)==null?void 0:h.data)==null?void 0:C.ErrorDescription})]})]}),s.jsxs(Mt,{index:u,onChange:i,allowToggle:!0,children:[s.jsxs(Ie,{children:[s.jsx("h2",{children:s.jsxs(we,{children:[s.jsx(L,{flex:"1",textAlign:"left",children:n("controller.devices.results")}),s.jsx(Ee,{})]})}),s.jsx(Me,{pb:4,children:s.jsx("pre",{children:JSON.stringify((g=r.data)==null?void 0:g.results,null,2)})})]}),s.jsxs(Ie,{children:[s.jsx("h2",{children:s.jsxs(we,{children:[s.jsx(L,{flex:"1",textAlign:"left",children:n("controller.devices.complete_data")}),s.jsx(Ee,{})]})}),s.jsx(Me,{pb:4,children:s.jsx("pre",{children:JSON.stringify(r.data,null,2)})})]})]})]})})},_o=async e=>ge.post(`device/${e.serialNumber}/telemetry`,e),To=()=>Qs(_o),Fo=()=>{const{t:e}=T(),t=wt(),n=To(),{token:r}=Vs(),[o,l]=d.useState(!1),a=d.useRef(void 0),[u,i]=d.useState(),c=()=>{var g;a!==void 0&&((g=a==null?void 0:a.current)==null||g.close(),i(void 0),n.reset())},m=g=>{a.current=new WebSocket(g),a.current.onopen=()=>{var p;l(!0),(p=a.current)==null||p.send(`token:${r}`)},a.current.onclose=()=>{l(!1)}},h=async(g,p)=>{n.mutate(g,{onSuccess:y=>{g.kafka?(t({id:"telemetry-device-success",title:e("common.success"),description:e("controller.telemetry.kafka_success"),status:"success",duration:5e3,isClosable:!0,position:"top-right"}),p&&p()):m(y.data.uri)}})},C=g=>{try{i({msg:JSON.parse(g.data),timestamp:new Date})}catch{i({msg:{error:"Error Parsing Data"},timestamp:new Date})}};return j.useEffect(()=>{a!=null&&a.current&&a.current.addEventListener("message",C);const g=a==null?void 0:a.current;return()=>{g&&(g.removeEventListener("message",C),g.close())}},[a==null?void 0:a.current]),{startTelemetry:h,closeSocket:c,lastMessage:u,isOpen:o,startRequest:n}},No=({serialNumber:e,modalProps:t})=>{var y,b,S,A,v,E,M,D;const{t:n}=T(),[r,o]=d.useState({serialNumber:e,interval:3,lifetime:60*5,kafka:!1,types:["wifi-frames","dhcp-snooping","state"]}),[l,a]=d.useState(new Date),u=Fo(),{hasCopied:i,onCopy:c,setValue:m}=Et(JSON.stringify(((y=u.lastMessage)==null?void 0:y.msg)??{},null,2)),h=()=>{u.startTelemetry(r,t.onClose)},C=()=>{a(new Date)},g=d.useMemo(()=>{var w;return(w=u.lastMessage)!=null&&w.timestamp?Math.max(0,Math.floor((l.getTime()-u.lastMessage.timestamp.getTime())/1e3)):" X "},[l,(b=u.lastMessage)==null?void 0:b.timestamp]),p=w=>O=>{O.target.checked?o({...r,types:[...r.types,w]}):o({...r,types:r.types.filter(I=>I!==w)})};return d.useEffect(()=>{u.closeSocket()},[t.isOpen]),d.useEffect(()=>{var w;m(JSON.stringify(((w=u.lastMessage)==null?void 0:w.msg)??{},null,2))},[(S=u.lastMessage)==null?void 0:S.msg]),d.useEffect(()=>{const w=setTimeout(()=>C(),1e3);return()=>{clearTimeout(w)}},[l]),d.useEffect(()=>{o({...r,serialNumber:e})},[e]),s.jsx(Te,{...t,title:n("controller.telemetry.title"),topRightButtons:s.jsx(fe,{onClick:h,isDisabled:u.isOpen||r.types.length===0,isLoading:u.startRequest.isLoading,colorScheme:"blue",children:n("common.start")}),children:s.jsxs(s.Fragment,{children:[u.startRequest.isLoading&&s.jsx(te,{my:"100px",children:s.jsx(_e,{size:"xl"})}),u.startRequest.error&&s.jsxs(de,{status:"error",my:"100px",children:[s.jsx(me,{}),s.jsxs(L,{children:[s.jsx(ve,{children:n("common.error")}),s.jsx(Ae,{children:(E=(v=(A=u.error)==null?void 0:A.response)==null?void 0:v.data)==null?void 0:E.ErrorDescription})]})]}),u.startRequest.data?s.jsxs(s.Fragment,{children:[s.jsxs("p",{children:[n("controller.telemetry.interval"),": ",r.interval," ",Us(n("common.seconds"))]}),s.jsxs("p",{children:[n("controller.telemetry.duration"),": ",Ws(r.lifetime,n)]}),s.jsxs("p",{children:[n("controller.telemetry.types"),": ",r.types.join(", ")]}),s.jsxs("p",{children:[n("controller.telemetry.last_update"),": ",(M=u.lastMessage)==null?void 0:M.timestamp.toLocaleString()]}),s.jsx(Rn,{size:"sm",children:n("controller.telemetry.seconds_ago",{seconds:g})}),s.jsx(L,{textAlign:"right",mb:2,children:s.jsx(fe,{onClick:c,size:"md",colorScheme:"teal",children:i?`${n("common.copied")}!`:n("common.copy")})}),s.jsx(L,{maxH:"calc(100vh - 380px)",minH:"300px",overflowY:"auto",children:s.jsx("pre",{children:JSON.stringify(((D=u.lastMessage)==null?void 0:D.msg)??{},null,2)})})]}):s.jsxs(s.Fragment,{children:[s.jsx(le,{children:n("controller.telemetry.interval")}),s.jsxs(Ye,{step:1,value:r.interval,max:120,onChange:w=>o({...r,interval:w}),focusThumbOnChange:!1,children:[s.jsxs(et,{value:r.interval,textAlign:"center",bg:"blue.500",color:"white",ml:"6",mt:"-3",w:"12",zIndex:2,children:[r.interval,"s"]}),s.jsx(tt,{children:s.jsx(st,{})}),s.jsx(nt,{})]}),s.jsx(le,{mt:4,children:n("controller.telemetry.duration")}),s.jsxs(Ye,{step:1,value:r.lifetime/60,max:120,onChange:w=>o({...r,lifetime:w*60}),focusThumbOnChange:!1,children:[s.jsxs(et,{value:r.lifetime/60,textAlign:"center",bg:"blue.500",color:"white",ml:"6",mt:"-3",w:"12",zIndex:2,children:[r.lifetime/60,"m"]}),s.jsx(tt,{children:s.jsx(st,{})}),s.jsx(nt,{})]}),s.jsxs(Ge,{minChildWidth:"320px",spacing:2,mt:4,children:[s.jsxs(L,{children:[s.jsx(le,{children:n("controller.telemetry.output")}),s.jsxs(Sn,{value:r.kafka?"kafka":"",onChange:w=>o({...r,kafka:w.target.value==="kafka"}),w:"160px",children:[s.jsx("option",{value:"",children:n("controller.telemetry.websocket")}),s.jsx("option",{value:"kafka",children:n("controller.telemetry.kafka")})]})]}),s.jsxs(L,{children:[s.jsx(le,{children:n("controller.telemetry.types")}),s.jsxs(zs,{spacing:5,direction:"row",mt:4,children:[s.jsx(ue,{colorScheme:"blue",isChecked:r.types.includes("wifi-frames"),onChange:p("wifi-frames"),children:"WiFi Frames"}),s.jsx(ue,{colorScheme:"green",isChecked:r.types.includes("dhcp-snooping"),onChange:p("dhcp-snooping"),children:"DHCP Snooping"}),s.jsx(ue,{colorScheme:"purple",isChecked:r.types.includes("state"),onChange:p("state"),children:"State"})]})]})]}),r.types.length===0&&s.jsx(te,{children:s.jsxs(de,{status:"error",mt:4,w:"unset",children:[s.jsx(me,{}),s.jsxs(L,{children:[s.jsx(ve,{children:n("common.error")}),s.jsx(Ae,{children:n("controller.telemetry.need_types")})]})]})})]})]})})},Lo=d.memo(No),$o=({device:e})=>{var o,l,a,u,i,c,m;const t=vn({serialNumber:e.serialNumber}),n=Fe(),r=h=>()=>{n(`/wireless/manage/Monitor#${h}`)};return t.isError?s.jsx("span",{children:"-"}):(a=(l=(o=t.data)==null?void 0:o.extendedInfo)==null?void 0:l.venue)!=null&&a.name?s.jsx("a",{style:{color:"#14C9BB ",textDecoration:"underline"},onClick:r(`${(u=t.data)==null?void 0:u.venue}`),children:(m=(c=(i=t.data)==null?void 0:i.extendedInfo)==null?void 0:c.venue)==null?void 0:m.name}):s.jsx("span",{children:"-"})},Go=e=>d.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:16,height:16,viewBox:"0 0 16 16",...e},d.createElement("defs",null,d.createElement("clipPath",{id:"master_svg0_318_19504"},d.createElement("rect",{x:0,y:0,width:16,height:16,rx:0}))),d.createElement("g",{clipPath:"url(#master_svg0_318_19504)"},d.createElement("g",null,d.createElement("path",{d:"M14.323998,9.5806813L1.67600191,9.5806813C1.30251089,9.5806813,1,9.8831925,1,10.2566833C1,10.3986435,1.043940097,10.5304642,1.11830033,10.6386242C1.15041044,10.714674,1.19942062,10.7856541,1.26026076,10.8481846L4.2397392,13.825974C4.3715591,13.957793,4.5439398,14.023705,4.7180102,14.023705C4.8920805000000005,14.023705,5.0644612,13.957793,5.1962814,13.825974C5.4599218,13.562333,5.4599218,13.134761,5.1962814,12.869431L3.2561564,10.9326849L14.322308,10.9326849C14.695798,10.9326849,14.998309,10.6301737,14.998309,10.2566833C14.998309,9.8831925,14.697489,9.5806813,14.323998,9.5806813ZM1.67600191,6.4440327L14.323998,6.4440327C14.697489,6.4440327,15,6.1415219,15,5.768030899999999C15,5.6260705,14.95606,5.4942498,14.8817,5.386089800000001C14.858039,5.3303196,14.824239,5.2762394,14.78368,5.2255392L12.346692,2.24775064C12.110092,1.958759718,11.684211,1.916509598,11.39522,2.15311027C11.10623,2.38971093,11.063979,2.81559211,11.30058,3.104583L12.928055,5.0920284L1.67600191,5.0920284C1.30251089,5.0920284,1,5.394539099999999,1,5.7680304C1,6.1415215,1.30251089,6.4440322,1.67600191,6.4440327Z",fill:"#14C9BB",fillOpacity:1}),d.createElement("path",{d:"M1.1272311799999999,5.2192595Q0.89999999,5.446490799999999,0.89999999,5.7680304Q0.89999999,6.08957,1.12723114,6.3168011Q1.35446241,6.5440326,1.67600185,6.5440331L14.323998,6.5440331Q14.645537,6.5440331,14.872768,6.316802Q15.1,6.089571,15.1,5.7680321Q15.1,5.5321286,14.969716,5.3377044Q14.930436,5.2489069,14.86177,5.1630712L12.42408,2.18441728Q12.220385,1.935616836,11.900493,1.903823853Q11.580674,1.87203805,11.331872,2.075734667Q11.083088,2.27941844,11.051294,2.5993104000000002Q11.019508,2.91913074,11.223205,3.1679314L12.716917,4.9920285L1.67600197,4.9920285Q1.35446227,4.9920285,1.1272311799999999,5.2192595ZM1.26865256,6.1753798Q1.10000001,6.0067272,1.10000001,5.7680304Q1.10000001,5.5293336,1.26865256,5.3606811Q1.43730515,5.1920285,1.67600197,5.1920285L13.139194,5.1920285L11.377955,3.0412346Q11.226718,2.85650826,11.250314,2.61909026Q11.27391,2.38166842,11.458569,2.23048592Q11.643297,2.079247251,11.880713,2.10284334Q12.118135,2.12643991,12.269305,2.311084L14.705592,5.2880075Q14.760672,5.356859,14.789642,5.4251454L14.793582,5.4344313L14.799296,5.442743500000001Q14.900001,5.5892196,14.900001,5.7680321Q14.900001,6.0067286,14.731348,6.1753807Q14.562696,6.3440332,14.323998,6.3440332L1.6760020899999999,6.3440332Q1.4373052099999999,6.3440323,1.26865256,6.1753798ZM14.871078,10.8054543Q15.09831,10.5782223,15.09831,10.2566833Q15.09831,9.9350834,14.872046,9.708035500000001Q14.645478,9.4806819,14.323998,9.4806819L1.67600197,9.4806819Q1.35446241,9.4806819,1.12723117,9.7079129Q0.900000013,9.9351444,0.89999999,10.2566833Q0.89999999,10.492527,1.030221958,10.6869164Q1.084885135,10.8113365,1.18858799,10.9179201L4.1690488000000006,13.896705Q4.3960497,14.123705,4.7180104,14.123705Q5.0399723,14.123705,5.2669926,13.896686Q5.4940119,13.669665,5.4940119,13.348336Q5.4940119,13.027194,5.2672176,12.798947L3.4978759,11.0326862L14.322308,11.0326862Q14.643847,11.0326862,14.871078,10.8054543ZM14.73038,9.8492122Q14.89831,10.017725,14.89831,10.2566833Q14.89831,10.4953814,14.729658,10.6640339Q14.561006,10.8326855,14.322308,10.8326855L3.0144374,10.8326855L5.1253457,12.939917Q5.2940116,13.109662,5.2940116,13.348336Q5.2940116,13.586823,5.1255713,13.755263Q4.9571285,13.923705,4.7180104,13.923705Q4.4788909,13.923705,4.31043,13.755243L1.33193359,10.7784491Q1.2505840400000001,10.6948385,1.21042523,10.5997267L1.206468,10.5903549L1.20070443,10.5819721Q1.10000001,10.4354925,1.10000001,10.2566833Q1.10000002,10.0179873,1.26865256,9.8493347Q1.43730533,9.680681700000001,1.67600197,9.680681700000001L14.323998,9.680681700000001Q14.562431,9.680681700000001,14.73038,9.8492122Z",fillRule:"evenodd",fill:"#14C9BB",fillOpacity:1})))),{Text:Po}=G,qo=({device:e})=>{const{t}=T(),[n,r]=d.useState(!1),o=async l=>{l.stopPropagation(),e.ipAddress&&(await navigator.clipboard.writeText(e.ipAddress),r(!0),W.success(`${t("common.copied")}!`),setTimeout(()=>r(!1),1500))};return s.jsx(ee,{title:e.ipAddress,placement:"top",children:s.jsxs("div",{style:{display:"flex",alignItems:"center",width:"100%"},children:[e.ipAddress&&s.jsx(ee,{title:n?`${t("common.copied")}!`:t("common.copy"),children:s.jsx(F,{type:"text",size:"small",icon:s.jsx(Js,{}),onClick:o,style:{marginRight:4}})}),s.jsx(Po,{ellipsis:{tooltip:!1},style:{flex:1},children:e.ipAddress||"-"})]})})},se=e=>{if(e===void 0||typeof e!="number")return"-";if(e===0)return"0.00";const t=e.toString();return t.charAt(3)==="."?`${t.slice(0,3)}`:`${t.slice(0,4)}`},Ft=(e,t)=>{if(!t||e===0)return"-";const n=e>1e3?e/1e3:e;return`${se(n)}°C`},Nt=e=>{let t=e.connected?"Connected":"Disconnected";return e.blackListed&&(t="Denied"),t},pe=e=>!e||e===0?"-":Xs(e),z=e=>e===void 0?0:e,Lt=async(e,t)=>e.length===0?[]:Ks.get(`inventory?withExtendedInfo=true&select=${e}${t?`&siteId=${t}`:""}`).then(({data:n})=>e.map(r=>{var a,u,i,c,m,h;const o=n.taglist.find(C=>C.serialNumber===r);let l="Not Found";return o&&(o.entity.length>0?l=((u=(a=o.extendedInfo)==null?void 0:a.entity)==null?void 0:u.name)??o.entity:o.venue.length>0?l=((c=(i=o.extendedInfo)==null?void 0:i.venue)==null?void 0:c.name)??o.venue:o.subscriber.length>0&&(l=((h=(m=o.extendedInfo)==null?void 0:m.subscriber)==null?void 0:h.name)??o.subscriber)),{serialNumber:r,provisioning:l}})).catch(()=>[]),Ho=(e,t,n)=>ge.get(`devices?deviceWithStatus=true&limit=${e}&offset=${t}${n?`&siteId=${n}`:""}`).then(r=>r.data),Bo=async(e,t,n,r)=>{const o=(90-t)/Math.ceil(e/100);let l=t,a=0,u=[],i;do i=await Ho(100,a,r),u=u.concat(i.devicesWithStatus),n(l+=o),a+=100;while(i.devicesWithStatus.length===100);return u},Zo=async(e,t)=>{e(0);const n=await ge.get(`devices?countOnly=true${t?`&siteId=${t}`:""}`).then(i=>i.data.count);if(e(10),n===0)return e(100),[];const r=await Bo(n,10,e,t),o=r.filter(i=>i.entity.length>0||i.venue.length>0||i.subscriber.length>0).map(i=>i.serialNumber),l=await Lt(o,t);e(95);const a=i=>{try{return new Date(i*1e3).toISOString()}catch{return""}},u=r.map(i=>{var m;const c=(m=l.find(h=>h.serialNumber===i.serialNumber))==null?void 0:m.provisioning;return{connected:Nt(i),serialNumber:i.serialNumber,name:i.name,sanity:i.connected?`${i.sanity}%`:"-",memory:i.connected?`${se(i.memoryUsed)}%`:"-",load:i.connected?`${se(i.load)}%`:"-",temperature:Ft(i.temperature,i.connected),revision:Ne(i.firmware),deviceType:i.compatible,ip:i.ipAddress||"-",provisioning:c??"-",radiusSessions:(typeof i.hasRADIUSSessions=="number"?i.hasRADIUSSessions:0).toString(),hasGPS:i.hasGPS?"true":"-",uptime:!i.connected||i.started===0?"-":Math.floor(Date.now()/1e3-i.started).toString(),lastContact:typeof i.lastContact=="string"?i.lastContact:a(i.lastContact),lastRecordedContact:typeof i.lastRecordedContact=="string"?i.lastRecordedContact:a(i.lastRecordedContact),lastUpgrade:typeof i.lastFWUpdate=="string"?i.lastFWUpdate:a(i.lastFWUpdate),rx:pe(i.rxBytes),tx:pe(i.txBytes),twoG:z(i.associations_2G),fiveG:z(i.associations_5G),sixG:z(i.associations_6G),connectReason:i.connectReason||"-",certificateExpiry:i.certificateExpiryDate?a(i.certificateExpiryDate):"-"}});return e(100),u},Qo=(e,t)=>ge.get(`devices?deviceWithStatus=true&select=${e.join(",")}${t?`&siteId=${t}`:""}`).then(n=>n.data),Vo=async(e,t,n)=>{t(0);const r=e.length;if(t(10),r===0)return t(100),[];const o=(await Qo(e,n)).devicesWithStatus;t(90);const l=o.filter(c=>c.entity.length>0||c.venue.length>0||c.subscriber.length>0).map(c=>c.serialNumber),a=await Lt(l,n);t(95);const u=c=>{try{return new Date(c*1e3).toISOString()}catch{return""}},i=o.map(c=>{var h,C,g;const m=(h=a.find(p=>p.serialNumber===c.serialNumber))==null?void 0:h.provisioning;return{connected:Nt(c),serialNumber:c.serialNumber,name:((g=(C=c==null?void 0:c.configuration)==null?void 0:C.unit)==null?void 0:g.name)||"-",sanity:c.connected?`${c.sanity}%`:"-",memory:c.connected?`${se(c.memoryUsed)}%`:"-",load:c.connected?`${se(c.load)}%`:"-",temperature:Ft(c.temperature,c.connected),revision:Ne(c.firmware),deviceType:c.compatible,ip:c.ipAddress||"-",provisioning:m??"-",radiusSessions:(typeof c.hasRADIUSSessions=="number"?c.hasRADIUSSessions:0).toString(),hasGPS:c.hasGPS?"true":"-",uptime:!c.connected||c.started===0?"-":Math.floor(Date.now()/1e3-c.started).toString(),lastContact:typeof c.lastContact=="string"?c.lastContact:u(c.lastContact),lastRecordedContact:typeof c.lastRecordedContact=="string"?c.lastRecordedContact:u(c.lastRecordedContact),lastUpgrade:typeof c.lastFWUpdate=="string"?c.lastFWUpdate:u(c.lastFWUpdate),rx:pe(c.rxBytes),tx:pe(c.txBytes),twoG:z(c.associations_2G),fiveG:z(c.associations_5G),sixG:z(c.associations_6G),connectReason:c.connectReason||"-",certificateExpiry:c.certificateExpiryDate?u(c.certificateExpiryDate):"-"}});return t(100),i},Uo=[{key:"connected",label:"Status"},{key:"serialNumber",label:"Serial number"},{key:"name",label:"Name"},{key:"sanity",label:"Sanity"},{key:"memory",label:"Memory"},{key:"load",label:"Load"},{key:"temperature",label:"Temp(°c)"},{key:"revision",label:"Revision"},{key:"deviceType",label:"Type"},{key:"ip",label:"Ip"},{key:"provisioning",label:"Venue"},{key:"hasGPS",label:"GPS"},{key:"uptime",label:"Uptime"},{key:"lastRecordedContact",label:"Last Connected"},{key:"lastContact",label:"Last Contact"},{key:"lastUpgrade",label:"Last Upgrade"},{key:"rx",label:"Rx"},{key:"tx",label:"Tx"},{key:"twoG",label:"2G"},{key:"fiveG",label:"5G"},{key:"sixG",label:"6G"},{key:"connectReason",label:"Connect Reason"}],xt=()=>{if(window.location.hash){const e=window.location.hash.replace("#","");return e==="all"?"":e}return""},Wo=({currentPageSerialNumbers:e})=>{const{t}=T(),[n,r]=d.useState(!1),[o,l]=d.useState({progress:0,status:"idle"}),a=h=>{l(C=>({...C,progress:h}))},u=()=>{l(C=>({...C,error:void 0,lastResults:void 0,status:"loading-all",progress:0}));const h=xt();Zo(a,h).then(C=>{l(g=>({...g,status:"success",lastResults:C}))}).catch(C=>{l(g=>({...g,status:"error",error:C}))})},i=()=>{l(C=>({...C,error:void 0,lastResults:void 0,status:"loading-select",progress:0}));const h=xt();Vo(e,a,h).then(C=>{l(g=>({...g,status:"success",lastResults:C}))}).catch(C=>{l(g=>({...g,status:"error",error:C}))})},c=()=>{l({progress:0,status:"idle"}),r(!0)},m=()=>{r(!1)};return s.jsxs(s.Fragment,{children:[s.jsx(ee,{title:t("common.export"),children:s.jsx(F,{type:"primary",icon:s.jsx(ke,{component:Ys}),onClick:c,children:t("common.export")})}),s.jsxs(St,{title:t("common.export"),open:n,onCancel:m,footer:null,width:680,style:{height:450},children:[s.jsx(Je,{style:{margin:"16px 0 0 0",width:"calc(100% + 48px)",marginLeft:"-24px",marginBottom:16}}),s.jsxs("div",{style:{padding:0},children:[s.jsx("div",{style:{textAlign:"left",marginBottom:16},children:s.jsxs(kt,{children:[s.jsxs(F,{onClick:u,disabled:o.status.includes("loading"),loading:o.status==="loading-all",children:[t("devices.all")," ",t("devices.title")]}),s.jsxs(F,{onClick:i,disabled:e.length===0||o.status.includes("loading"),loading:o.status==="loading-select",children:[t("table.export_current_page")," (",e.length,")"]})]})}),(o.status.includes("loading")||o.status==="success")&&s.jsxs(s.Fragment,{children:[s.jsxs("div",{style:{margin:"113px 0px 113px 0px"},children:[s.jsxs("div",{style:{display:"flex",alignItems:"center",margin:"-33px 0px -25px 0px"},children:[s.jsx(en,{style:{flex:1,marginBottom:0},percent:Math.round(o.progress),status:o.progress!==100?"active":"success",strokeColor:o.progress===100?"#14C9BB":void 0,strokeWidth:12,showInfo:!1}),s.jsxs(G.Title,{level:5,style:{textAlign:"center",marginBottom:25,width:50,fontWeight:"bold"},children:[Math.round(o.progress),"%"]})]}),s.jsx(G.Text,{style:{textAlign:"center",marginBottom:25,fontWeight:"bold"},children:`Exporting to ${o.progress}%, Please Wait...`})]}),o.lastResults&&s.jsx(Je,{style:{margin:"16px 0 0 0",width:"calc(100% + 48px)",marginLeft:"-24px"}}),o.lastResults&&s.jsx("div",{style:{textAlign:"right",margin:"20px 0px 0px 0px"},children:s.jsx(It,{filename:`devices_export_${vt(new Date().getTime()/1e3)}.csv`,data:o.lastResults??[],headers:Uo,children:s.jsx(F,{type:"primary",children:t("common.download")})})})]}),o.status.includes("error")&&s.jsx(G.Text,{type:"danger",style:{display:"block",textAlign:"center",marginTop:32},children:JSON.stringify(o.error,null,2)})]})]})]})},zo=({value:e})=>{const t=d.useMemo(()=>e===void 0?"-":e.toLocaleString(),[e]);return s.jsx("div",{children:t})},Jo=j.memo(zo),Ko=({bytes:e,showZerosAs:t,divProps:n})=>{const r=d.useMemo(()=>e===void 0?"-":t&&e===0?t:an(e),[e,t]);return s.jsx("div",{...n,children:s.jsx(G.Text,{children:r})})},Xo=j.memo(Ko),Yo=d.forwardRef(({siteId:e},t)=>{var Ze,Qe;const{t:n}=T();let r=null;const o=window.location.hash.replace("#","");o&&o!==""&&(r=o),e!=""?e=e:r&&(e=r);const[l,a]=d.useState([{id:"serialNumber",sort:"asc"}]),[u,i]=d.useState({index:0,limit:10}),[c,m]=j.useState("ALL"),[h,C]=j.useState(""),[g,p]=j.useState(!1),[y,b]=j.useState(!1),[S,A]=j.useState(!1),[v,E]=j.useState(!1),[M,D]=j.useState(!1),[w,O]=j.useState(!1),[I,_]=j.useState(!1),[k,Pe]=d.useState(!1),ye=An({enabled:!0,platform:c,siteId:e}),$t=Fe();In({tableSettingsId:"gateway.devices.table",defaultOrder:["serialNumber","sanity","memory","load","temperature","firmware","compatible","connected","actions"]});const J=rt({pageInfo:{limit:u.limit,index:u.index},sortInfo:l,enabled:!0,platform:c,siteId:e});d.useEffect(()=>{e&&J.refetch(),i({index:0,limit:u.limit})},[e]);const Ce=f=>{if(f===void 0||typeof f!="number")return"-";if(f===0)return"0.00";const x=f.toString();return x.charAt(3)==="."?`${x.slice(0,3)}`:`${x.slice(0,4)}`},re=(f,x)=>{const R=parseInt(f.slice(1,3),16),Q=parseInt(f.slice(3,5),16),V=parseInt(f.slice(5,7),16);return`rgba(${R}, ${Q}, ${V}, ${x})`},Gt=j.useCallback(f=>s.jsx("a",{href:`/wireless/devices/${f.serialNumber}#/devices/${f.serialNumber}`,style:{color:"#14C9BB ",textDecoration:"underline"},children:s.jsx("pre",{children:f.serialNumber})}),[]),Pt=j.useCallback(f=>{if(!f.connected)return s.jsx("span",{children:"-"});let x="#F53F3F";return f.sanity>=80&&(x="#FFBB00"),f.sanity===100&&(x="#2BC174"),s.jsxs(ae,{style:{border:`1px solid ${x}`,color:x,background:re(x,.1)},children:[f.sanity,"%"]})},[]),qt=j.useCallback(f=>{if(!f.connected)return s.jsx("span",{children:"-"});let x="#F53F3F";return f.memoryUsed<=85&&(x="#FFBB00"),f.memoryUsed<=60&&(x="#2BC174"),s.jsxs(ae,{style:{border:`1px solid ${x}`,color:x,background:re(x,.1)},children:[Ce(f.memoryUsed),"%"]})},[]),Ht=j.useCallback(f=>{if(!f.connected)return s.jsx("span",{children:"-"});let x="#F53F3F";return f.load<=20&&(x="#FFBB00"),f.load<=5&&(x="#2BC174"),s.jsxs(ae,{style:{border:`1px solid ${x}`,color:x,background:re(x,.1)},children:[Ce(f.load),"%"]})},[]),Bt=j.useCallback(f=>{let x="#F53F3F",R=f.connected?"Connected":"Disconnected";return f.blackListed&&(R="Denied"),R=="Denied"?x="#F53F3F":R=="Connected"?x="#2BC174":x="#B3BBC8",s.jsx(ae,{style:{border:`1px solid ${x}`,color:x,background:re(x,.1)},children:R})},[]),Zt=j.useCallback(f=>f.firmware?s.jsx("span",{children:Ne(f.firmware)}):s.jsx("span",{children:"-"}),[]),Qt=j.useCallback(f=>{if(!f.connected||f.temperature===0)return s.jsx("p",{children:"-"});const x=f.temperature>1e3?f.temperature/1e3:f.temperature;return s.jsx(s.Fragment,{children:s.jsxs("span",{children:[Ce(x),"°C"]})})},[]),Vt=j.useCallback(f=>!f.entity&&!f.venue?"-":s.jsx($o,{device:f}),[]),Ut=j.useCallback(f=>s.jsx(qo,{device:f}),[]),Wt=j.useCallback(f=>s.jsx(go,{device:f}),[]),zt=j.useCallback(f=>s.jsx(Co,{device:f}),[]),be=j.useCallback((f,x)=>f!==void 0&&typeof f=="number"&&f!==0?s.jsx(gt,{date:f,hidePrefix:x}):"-",[]),qe=j.useCallback(f=>s.jsx("div",{children:s.jsx(Xo,{bytes:f,showZerosAs:"-"})}),[]);j.useCallback((f,x)=>f!==void 0&&typeof f=="number"&&f!==0?s.jsx(gt,{date:f,hidePrefix:x,isCompact:!0}):"-",[]);const Jt=f=>{C(f),p(!0)},Kt=()=>{p(!1)},Xt=f=>{C(f),b(!0)},Yt=()=>{b(!1)},es=f=>{C(f),A(!0)},ts=()=>{A(!1)},ss=f=>{C(f),E(!0)},ns=()=>{E(!1)},rs=f=>{C(f),D(!0)},os=()=>{D(!1)},as=f=>{C(f),O(!0)},is=()=>{O(!1)},ls=f=>{C(f),_(!0)},cs=()=>{_(!1)},us=j.useCallback(f=>s.jsx(xr,{device:f,refreshTable:J.refetch,onOpenScan:Jt,onOpenConfigureModal:Xt,onOpenRebootModal:es,onOpenEventQueue:ss,onOpenFactoryReset:rs,onOpenTelemetryModal:as,onOpenTrace:ls}),[]),je=j.useCallback(f=>s.jsx(Jo,{value:f!==void 0?f:0,showZerosAs:"-",boxProps:{textAlign:"right"}}),[]),ds=(f,x,R)=>{let Q=[];R&&R.field?Q=[{id:R.field,sort:R.order==="ascend"?"asc":"desc"}]:Array.isArray(R)&&(Q=R.filter(V=>V.order).map(V=>({id:V.field,sort:V.order==="ascend"?"asc":"desc"}))),i({index:f.current-1,limit:f.pageSize}),JSON.stringify(Q)!==JSON.stringify(l)&&a(Q)},ms=()=>{$t(`/wireless/devices/denyList#${e||"all"}`)},He=()=>{Pe(!0),J.refetch().finally(()=>Pe(!1)),ye.refetch()};d.useImperativeHandle(t,()=>({refreshTable:()=>{He()}}));const Be=((Ze=J.data)==null?void 0:Ze.devicesWithStatus)??[],fs=d.useMemo(()=>[{key:"connected",title:"Status",footer:"",dataIndex:"connected",render:(f,x)=>Bt(x),fixed:"left",sorter:!1,isMonospace:!0},{key:"serialNumber",title:s.jsx("span",{style:{whiteSpace:"nowrap"},children:n("inventory.serial_number")}),dataIndex:"serialNumber",render:(f,x)=>Gt(x),sorter:!0,columnsFix:!0},{key:"name",title:"Name",dataIndex:"name",enableSorting:!1,sorter:!1,isMonospace:!0},{key:"sanity",title:n("devices.sanity"),dataIndex:"sanity",footer:"",render:(f,x)=>Pt(x),sorter:!1,isMonospace:!0},{key:"memory",title:n("analytics.memory"),dataIndex:"memoryUsed",footer:"",render:(f,x)=>qt(x),sorter:!1,isMonospace:!0},{key:"load",title:"Load",footer:"",dataIndex:"load",render:(f,x)=>Ht(x),sorter:!1,isMonospace:!0},{key:"temperature",title:"Temp(°C)",dataIndex:"temperature",footer:"",render:(f,x)=>Qt(x),sorter:!1,isMonospace:!0},{key:"firmware",title:n("commands.revision"),footer:"",dataIndex:"firmware",render:(f,x)=>Zt(x),sorter:!1,isMonospace:!0},{key:"compatible",title:n("common.type"),footer:"",dataIndex:"compatible",sorter:!0,isMonospace:!0},{key:"IP",title:"IP",footer:"",dataIndex:"IP",render:(f,x)=>Ut(x),sorter:!1,isMonospace:!0},{key:"Venue",title:"Site",footer:"",dataIndex:"Venue",render:(f,x)=>Vt(x),sorter:!1,isMonospace:!0},{key:"GPS",title:"GPS",footer:"",dataIndex:"GPS",render:(f,x)=>Wt(x),sorter:!1,isMonospace:!0},{key:"uptime",title:n("system.uptime"),footer:"",dataIndex:"uptime",render:(f,x)=>zt(x),sorter:!1,isMonospace:!0},{key:"lastRecordedContact",title:s.jsx("span",{style:{whiteSpace:"nowrap"},children:n("analytics.last_connected")}),footer:"",dataIndex:"lastRecordedContact",render:(f,x)=>be(x.lastRecordedContact),sorter:!1,isMonospace:!0},{key:"lastContact",title:s.jsx("span",{style:{whiteSpace:"nowrap"},children:n("analytics.last_contact")}),footer:"",dataIndex:"lastContact",render:(f,x)=>be(x.lastContact),sorter:!1,isMonospace:!0},{key:"lastFWUpdate",title:s.jsx("span",{style:{whiteSpace:"nowrap"},children:n("controller.devices.last_upgrade")}),footer:"",dataIndex:"lastFWUpdate",render:(f,x)=>be(x.lastFWUpdate),sorter:!1,isMonospace:!0},{key:"rxBytes",title:"Rx",footer:"",dataIndex:"rxBytes",render:(f,x)=>qe(x.rxBytes),sorter:!1,isMonospace:!0},{key:"txBytes",title:"Tx",footer:"",dataIndex:"txBytes",render:(f,x)=>qe(x.txBytes),sorter:!1,isMonospace:!0},{key:"2G",title:"2G",footer:"",dataIndex:"associations_2G",render:(f,x)=>je(x.associations_2G),sorter:!1,isMonospace:!0},{key:"5G",title:"5G",footer:"",dataIndex:"associations_5G",render:(f,x)=>je(x.associations_5G),sorter:!1,isMonospace:!0},{key:"6G",title:"6G",footer:"",dataIndex:"associations_6G",render:(f,x)=>je(x.associations_6G),sorter:!1,isMonospace:!0},{key:"connectReason",title:s.jsx("span",{style:{whiteSpace:"nowrap"},children:"Connect Reason"}),footer:"",dataIndex:"connectReason",render:(f,x)=>x.connectReason?x.connectReason:"-",sorter:!1,isMonospace:!0},{key:"actions",title:"Operation",footer:"",dataIndex:"actions",render:(f,x)=>s.jsx("div",{"data-col-key":"actions",children:us(x)}),columnsFix:!0,fixed:"right"}],[n]),ps=J.isFetching||ye.isFetching||k;return s.jsxs(s.Fragment,{children:[s.jsxs("div",{style:{display:"flex",gap:10},children:[s.jsx(Wo,{currentPageSerialNumbers:Be.map(f=>f.serialNumber)}),s.jsx(F,{htmlType:"button",onClick:()=>{He(),W.success("Device refresh success.")},loading:k,icon:s.jsx(ke,{component:tn}),children:"Refresh"}),s.jsx(F,{icon:s.jsx(ke,{component:Go}),onClick:ms,children:"Denylist"}),s.jsx("div",{style:{display:"flex",justifyContent:"flex-end",width:"100%"},children:s.jsx(kn,{})})]}),s.jsx(ln,{columnsOrder:!0,resizableColumns:!0,tableId:"devices-table",ref:t,columns:fs,dataSource:Be,loading:ps,onChange:ds,showColumnSelector:"true",isShowPagination:!0,fetchAPIInfo:rt,disableInternalRowSelection:!0,pagination:{current:u.index+1,pageSize:u.limit,total:((Qe=ye.data)==null?void 0:Qe.count)||0,showSizeChanger:!0,showQuickJumper:!0,showTotal:f=>`Total ${f} items`,pageSizeOptions:["10","20","50","100"]}}),s.jsx(wo,{modalProps:{isOpen:g,onClose:Kt},serialNumber:h}),s.jsx(Oo,{modalProps:{isOpen:y,onClose:Yt},serialNumber:h}),s.jsx(Ro,{modalProps:{isOpen:S,onClose:ts},serialNumber:h}),s.jsx(wn,{modalProps:{isOpen:M,onClose:os},serialNumber:h}),s.jsx(Lo,{modalProps:{isOpen:w,onClose:is},serialNumber:h}),s.jsx(En,{modalProps:{isOpen:I,onClose:cs},serialNumber:h}),s.jsx(Do,{modalProps:{isOpen:v,onClose:ns},serialNumber:h})]})}),ga=()=>{const{selectedSiteId:e,isAllSitesSelected:t,handleSiteChange:n,displaySiteId:r}=Fn(!0);sn(),Fe();const[o,l]=d.useState();return Nn(),Ln(),s.jsx(s.Fragment,{children:s.jsxs(nn,{style:{width:"100%",minHeight:"100%",borderRadius:"8px",boxShadow:"none",padding:"20px 24px",overflowX:"auto"},bodyStyle:{padding:0},children:[s.jsx("span",{className:"text-title",children:"Devices"}),s.jsx(on,{onChange:n,value:t?"all":e||"all"}),s.jsx(Yo,{siteId:r})]})})};export{ga as default};
