import{R as b,j as h,bB as D,ac as u,ai as T,b as a,z as i,y as c,u as l,ag as n}from"./index-CCDcquaz.js";import{P as A}from"./CustomTable-B4Am8LRY.js";import{f as E,c as $}from"./dateFormatting-yHFQ8l7H.js";const w={date:A.number.isRequired},f=({date:e})=>h.jsx(D,{hasArrow:!0,placement:"top",label:$(e),children:e===0?"-":E(e)});f.propTypes=w;const U=b.memo(f),p="/ampcon/wireless/site/label";function C(e){return u({url:`${p}`,method:"GET",params:e})}function G(e){return u({url:`${p}`,method:"POST",data:e})}function L(e){return u({url:`${p}`,method:"DELETE",data:{id:e}})}const P=()=>{const e=T();return()=>e("/wireless/operators")},V=async(e,t)=>n.get(`venue?withExtendedInfo=true&offset=${t}&limit=${e}`).then(({data:r})=>r.venues),y=async()=>{let t=0,r=[],o=[];do o=await V(500,t),r=r.concat(o),t+=500;while(o.length===500);return r},_=()=>{const{t:e}=a(),t=i();return l(["get-venues"],()=>y(),{staleTime:3e4,onError:r=>{var o,s;t.isActive("venues-fetching-error")||t({id:"venues-fetching-error",title:e("common.error"),description:e("crud.error_fetching_obj",{obj:e("venues.title"),e:(s=(o=r==null?void 0:r.response)==null?void 0:o.data)==null?void 0:s.ErrorDescription}),status:"error",duration:5e3,isClosable:!0,position:"top-right"})}})},I=({id:e})=>{const{t}=a(),r=i(),o=P();return l(["get-venue",e],()=>n.get(`venue/${e}?withExtendedInfo=true`).then(({data:s})=>s),{enabled:e!==void 0&&e!=="",keepPreviousData:!0,staleTime:1e3*5,onError:s=>{var v,d,m,g;r.isActive("venue-fetching-error")||r({id:"venue-fetching-error",title:t("common.error"),description:t("crud.error_fetching_obj",{obj:t("venues.one"),e:(d=(v=s==null?void 0:s.response)==null?void 0:v.data)==null?void 0:d.ErrorDescription}),status:"error",duration:5e3,isClosable:!0,position:"top-right"}),((g=(m=s.response)==null?void 0:m.data)==null?void 0:g.ErrorCode)===404&&o()}})},q=({id:e})=>{const{t}=a();return i(),c(()=>n.put(`venue/${e}?updateAllDevices=true`,{}),{})},B=({id:e})=>{const{t}=a();return i(),c(()=>n.put(`venue/${e}?rebootAllDevices=true`,{}),{})},k=()=>{const{t:e}=a();return i(),c(t=>n.put(`venue/${t.id}?upgradeAllDevices=true&revision=${encodeURIComponent(t.revision)||""}`,{}),{})},j=e=>n.put(`venue/${e}?upgradeAllDevices=true&revisionsAvailable=true`,{}).then(t=>t.data),z=({id:e,enabled:t})=>l(["venue",e,"availableFirmware"],()=>j(e),{enabled:t});export{U as F,k as a,B as b,q as c,I as d,G as e,C as f,L as g,_ as h,z as u};
