var E=Object.defineProperty;var S=(s,e,t)=>e in s?E(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var p=(s,e,t)=>S(s,typeof e!="symbol"?e+"":e,t);import{q as m,w as C,dr as U,x as L,j as f,v as b,b3 as $,b5 as N,cd as _,t as D,R as k,a0 as g,r as I}from"./index-CCDcquaz.js";import{p as c}from"./CustomTable-B4Am8LRY.js";const H=m((s,e)=>{const{className:t,...n}=s,o=C("chakra-modal__footer",t),r=U(),a=L({display:"flex",alignItems:"center",justifyContent:"flex-end",...r.footer});return f.jsx(b.footer,{ref:e,...n,__css:a,className:o})});H.displayName="ModalFooter";function se(s){const{leastDestructiveRef:e,...t}=s;return f.jsx($,{...t,initialFocusRef:e})}const te=m((s,e)=>f.jsx(N,{ref:e,role:"alertdialog",...s})),M=m(function(e,t){const n=_("Heading",e),{className:o,...r}=D(e);return f.jsx(b.h2,{ref:t,className:C("chakra-heading",e.className),...r,__css:n})});M.displayName="Heading";const T=()=>/^((?!chrome|android).)*safari/i.test(navigator.userAgent),F=s=>Array.isArray(s)&&s.every(e=>typeof e=="object"&&!(e instanceof Array)),B=s=>Array.isArray(s)&&s.every(e=>Array.isArray(e)),P=s=>Array.from(s.map(e=>Object.keys(e)).reduce((e,t)=>new Set([...e,...t]),[])),V=(s,e)=>{e=e||P(s);let t=e,n=e;F(e)&&(t=e.map(r=>r.label),n=e.map(r=>r.key));const o=s.map(r=>n.map(a=>q(a,r)));return[t,...o]},q=(s,e)=>{const t=s.replace(/\[([^\]]+)]/g,".$1").split(".").reduce(function(n,o,r,a){const i=n[o];if(i==null)a.splice(1);else return i},e);return t===void 0?s in e?e[s]:"":t},J=s=>typeof s>"u"||s===null?"":s,R=(s,e=",",t='"')=>s.filter(n=>n).map(n=>n.map(o=>J(o)).map(o=>`${t}${o}${t}`).join(e)).join(`
`),K=(s,e,t,n)=>R(e?[e,...s]:s,t,n),W=(s,e,t,n)=>R(V(s,e),t,n),z=(s,e,t,n)=>e?`${e.join(t)}
${s}`:s.replace(/"/g,'""'),v=(s,e,t,n)=>{if(F(s))return W(s,e,t,n);if(B(s))return K(s,e,t,n);if(typeof s=="string")return z(s,e,t);throw new TypeError('Data should be a "String", "Array of arrays" OR "Array of objects" ')},w=(s,e,t,n,o)=>{const r=v(s,t,n,o),a=T()?"application/csv":"text/csv",i=new Blob([e?"\uFEFF":"",r],{type:a}),l=`data:${a};charset=utf-8,${e?"\uFEFF":""}${r}`,u=window.URL||window.webkitURL;return typeof u.createObjectURL>"u"?l:u.createObjectURL(i)},j={data:c.oneOfType([c.string,c.array,c.func]).isRequired,headers:c.array,target:c.string,separator:c.string,filename:c.string,uFEFF:c.bool,onClick:c.func,asyncOnClick:c.bool,enclosingCharacter:c.string},A={separator:",",filename:"generatedBy_react-csv.csv",uFEFF:!0,asyncOnClick:!1,enclosingCharacter:'"'},G={target:"_blank"};class h extends k.Component{constructor(e){super(e),this.state={}}buildURI(){return w(...arguments)}componentDidMount(){const{data:e,headers:t,separator:n,enclosingCharacter:o,uFEFF:r,target:a,specs:i,replace:l}=this.props;this.state.page=window.open(this.buildURI(e,r,t,n,o),a,i,l)}getWindow(){return this.state.page}render(){return null}}p(h,"defaultProps",Object.assign(A,G)),p(h,"propTypes",j);var y;let Q=(y=class extends k.Component{constructor(e){super(e),this.buildURI=this.buildURI.bind(this)}buildURI(){return w(...arguments)}handleLegacy(e,t=!1){if(window.navigator.msSaveOrOpenBlob){e.preventDefault();const{data:n,headers:o,separator:r,filename:a,enclosingCharacter:i,uFEFF:l}=this.props,u=t&&typeof n=="function"?n():n;let d=new Blob([l?"\uFEFF":"",v(u,o,r,i)]);return window.navigator.msSaveBlob(d,a),!1}}handleAsyncClick(e){const t=n=>{if(n===!1){e.preventDefault();return}this.handleLegacy(e,!0)};this.props.onClick(e,t)}handleSyncClick(e){if(this.props.onClick(e)===!1){e.preventDefault();return}this.handleLegacy(e)}handleClick(){return e=>{if(typeof this.props.onClick=="function")return this.props.asyncOnClick?this.handleAsyncClick(e):this.handleSyncClick(e);this.handleLegacy(e)}}render(){const{data:e,headers:t,separator:n,filename:o,uFEFF:r,children:a,onClick:i,asyncOnClick:l,enclosingCharacter:u,...d}=this.props,O=typeof window>"u"?"":this.buildURI(e,r,t,n,u);return f.jsx("a",{download:o,...d,ref:x=>this.link=x,target:"_self",href:O,onClick:this.handleClick(),children:a})}},p(y,"defaultProps",A),p(y,"propTypes",j),y);const re=Q,oe=({isLoading:s,onModalClose:e})=>{const{isOpen:t,onOpen:n,onClose:o}=g(),{isOpen:r,onOpen:a,onClose:i}=g(),l=()=>{s?a():e?e():o()},u=()=>{i(),e?e():o()};return I.useMemo(()=>({onOpen:n,isOpen:t,isConfirmOpen:r,closeModal:l,closeConfirm:i,closeCancelAndForm:u}),[t,r,s])};export{se as A,re as C,M as H,H as M,te as a,oe as u};
