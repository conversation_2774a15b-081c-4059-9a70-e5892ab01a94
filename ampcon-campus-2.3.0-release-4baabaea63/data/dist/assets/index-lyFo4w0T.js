import{r as d,m as wa,k as bs,l as Ut,n as Rt,o as vs,q as $e,s as ys,t as js,j as e,v as Ke,w as We,x as Ta,I as Aa,_ as ka,y as Dt,b as K,z as pt,u as De,E as we,G as Cs,H as D,J as ke,K as gt,N as Ot,M as Ie,D as de,O as A,P as ie,B as Z,Q as Ee,U as Ss,V as Je,F as Ue,W as ae,X as Pe,Y as Ia,Z as Xe,c as ve,T as ue,$ as Bt,a0 as ws,R as G,a1 as Ce,C as xt,a2 as Q,a3 as ta,a4 as bt,a5 as ut,g as _,h as F,a6 as Ts,a7 as As,a8 as ks,i as Is,a9 as Es,aa as X,ab as te,ac as oe,ad as Re,ae as Be,af as Ps,ag as Fs,ah as Ms,ai as vt,S as Ea,aj as Ze,ak as Rs,al as yt,am as Ye,an as _s,ao as Us,ap as Nt,aq as Ds,ar as Os,as as Bs,at as Ns,au as Ls,av as qs,aw as zs,ax as aa,ay as Pa,A as Fa,az as Lt,aA as kt,aB as Vs,aC as Qs,aD as Gs,aE as Hs,aF as It}from"./index-CCDcquaz.js";import{i as $s,c as H,a as $,D as sa,C as Ks,b as Y,d as q,e as N}from"./CustomWirelessRangePicker-Csazmig8.js";import{u as Ma,F as qt,a as Ra,b as jt,c as ye}from"./index-B9-ToZA1.js";import{f as zt,g as Ge,a as Ne,T as _a,s as He,R as Ua,W as Ws,F as Js,v as Xs,b as ht,c as Zs,d as Ys,V as en}from"./tabs-B30QcNxg.js";import{W as Vt,P as M}from"./CustomTable-B4Am8LRY.js";import{m as tn,s as an,g as sn}from"./dateFormatting-yHFQ8l7H.js";import{F as Ct,f as nn,u as rn,a as ln,b as on,c as dn,d as cn}from"./Venues-AQBKyfj9.js";import{p as Da,b as fe,f as na}from"./Form-CDHrBU_a.js";import{g as un}from"./wireless_client_api-DLGjGswK.js";import{u as Oa}from"./SiteContext-D-2Xr-_P.js";import{F as Qt,u as hn,a as fn}from"./useUrlSync-DjGOfs83.js";/* empty css             */import{u as mn,a as ne,F as pn}from"./useFastField-C4vjyPS0.js";import{i as gn}from"./icon_details-DTb3-MwP.js";import{c as xn}from"./use-descendant-DTjabNrV.js";function bn(t){const{wasSelected:a,enabled:s,isSelected:i,mode:n="unmount"}=t;return!!(!s||i||n==="keepMounted"&&a)}function vn(){const t=d.useRef(new Map),a=t.current,s=d.useCallback((n,r,l,o)=>{t.current.set(l,{type:r,el:n,options:o}),n.addEventListener(r,l,o)},[]),i=d.useCallback((n,r,l,o)=>{n.removeEventListener(r,l,o),t.current.delete(l)},[]);return d.useEffect(()=>()=>{a.forEach((n,r)=>{i(n.el,n.type,r,n.options)})},[i,a]),{add:s,remove:i}}function Et(t){var n,r;const a=((r=(n=t.composedPath)==null?void 0:n.call(t))==null?void 0:r[0])??t.target,{tagName:s,isContentEditable:i}=a;return s!=="INPUT"&&s!=="TEXTAREA"&&i!==!0}function yn(t={}){const{ref:a,isDisabled:s,isFocusable:i,clickOnEnter:n=!0,clickOnSpace:r=!0,onMouseDown:l,onMouseUp:o,onClick:u,onKeyDown:c,onKeyUp:b,tabIndex:x,onMouseOver:g,onMouseLeave:y,...T}=t,[f,p]=d.useState(!0),[m,v]=d.useState(!1),h=vn(),k=R=>{R&&R.tagName!=="BUTTON"&&p(!1)},P=f?x:x||0,S=s&&!i,j=d.useCallback(R=>{if(s){R.stopPropagation(),R.preventDefault();return}R.currentTarget.focus(),u==null||u(R)},[s,u]),C=d.useCallback(R=>{m&&Et(R)&&(R.preventDefault(),R.stopPropagation(),v(!1),h.remove(document,"keyup",C,!1))},[m,h]),E=d.useCallback(R=>{if(c==null||c(R),s||R.defaultPrevented||R.metaKey||!Et(R.nativeEvent)||f)return;const se=n&&R.key==="Enter";r&&R.key===" "&&(R.preventDefault(),v(!0)),se&&(R.preventDefault(),R.currentTarget.click()),h.add(document,"keyup",C,!1)},[s,f,c,n,r,h,C]),w=d.useCallback(R=>{if(b==null||b(R),s||R.defaultPrevented||R.metaKey||!Et(R.nativeEvent)||f)return;r&&R.key===" "&&(R.preventDefault(),v(!1),R.currentTarget.click())},[r,f,s,b]),L=d.useCallback(R=>{R.button===0&&(v(!1),h.remove(document,"mouseup",L,!1))},[h]),O=d.useCallback(R=>{if(R.button!==0)return;if(s){R.stopPropagation(),R.preventDefault();return}f||v(!0),R.currentTarget.focus({preventScroll:!0}),h.add(document,"mouseup",L,!1),l==null||l(R)},[s,f,l,h,L]),I=d.useCallback(R=>{R.button===0&&(f||v(!1),o==null||o(R))},[o,f]),U=d.useCallback(R=>{if(s){R.preventDefault();return}g==null||g(R)},[s,g]),z=d.useCallback(R=>{m&&(R.preventDefault(),v(!1)),y==null||y(R)},[m,y]),B=wa(a,k);return f?{...T,ref:B,type:"button","aria-disabled":S?void 0:s,disabled:S,onClick:j,onMouseDown:l,onMouseUp:o,onKeyUp:b,onKeyDown:c,onMouseOver:g,onMouseLeave:y}:{...T,ref:B,role:"button","data-active":bs(m),"aria-disabled":s?"true":void 0,tabIndex:S?void 0:P,onClick:j,onMouseDown:O,onMouseUp:I,onKeyUp:w,onKeyDown:E,onMouseOver:U,onMouseLeave:z}}const[jn,Cn,Sn,wn]=xn();function Tn(t){const{defaultIndex:a,onChange:s,index:i,isManual:n,isLazy:r,lazyBehavior:l="unmount",orientation:o="horizontal",direction:u="ltr",...c}=t,[b,x]=d.useState(a??0),[g,y]=mn({defaultValue:a??0,value:i,onChange:s});d.useEffect(()=>{i!=null&&x(i)},[i]);const T=Sn(),f=d.useId();return{id:`tabs-${t.id??f}`,selectedIndex:g,focusedIndex:b,setSelectedIndex:y,setFocusedIndex:x,isManual:n,isLazy:r,lazyBehavior:l,orientation:o,descendants:T,direction:u,htmlProps:c}}const[An,St]=Ut({name:"TabsContext",errorMessage:"useTabsContext: `context` is undefined. Seems you forgot to wrap all tabs components within <Tabs />"});function kn(t){const{focusedIndex:a,orientation:s,direction:i}=St(),n=Cn(),r=d.useCallback(l=>{const o=()=>{var h;const v=n.nextEnabled(a);v&&((h=v.node)==null||h.focus())},u=()=>{var h;const v=n.prevEnabled(a);v&&((h=v.node)==null||h.focus())},c=()=>{var h;const v=n.firstEnabled();v&&((h=v.node)==null||h.focus())},b=()=>{var h;const v=n.lastEnabled();v&&((h=v.node)==null||h.focus())},x=s==="horizontal",g=s==="vertical",y=l.key,T=i==="ltr"?"ArrowLeft":"ArrowRight",f=i==="ltr"?"ArrowRight":"ArrowLeft",m={[T]:()=>x&&u(),[f]:()=>x&&o(),ArrowDown:()=>g&&o(),ArrowUp:()=>g&&u(),Home:c,End:b}[y];m&&(l.preventDefault(),m(l))},[n,a,s,i]);return{...t,role:"tablist","aria-orientation":s,onKeyDown:Rt(t.onKeyDown,r)}}function In(t){const{isDisabled:a=!1,isFocusable:s=!1,...i}=t,{setSelectedIndex:n,isManual:r,id:l,setFocusedIndex:o,selectedIndex:u}=St(),{index:c,register:b}=wn({disabled:a&&!s}),x=c===u,g=()=>{n(c)},y=()=>{o(c),!r&&!(a&&s)&&n(c)};return{...yn({...i,ref:wa(b,t.ref),isDisabled:a,isFocusable:s,onClick:Rt(t.onClick,g)}),id:Ba(l,c),role:"tab",tabIndex:x?0:-1,type:"button","aria-selected":x,"aria-controls":Na(l,c),onFocus:a?void 0:Rt(t.onFocus,y)}}const[En,Pn]=Ut({});function Fn(t){const a=St(),{id:s,selectedIndex:i}=a,r=vs(t.children).map((l,o)=>d.createElement(En,{key:l.key??o,value:{isSelected:o===i,id:Na(s,o),tabId:Ba(s,o),selectedIndex:i}},l));return{...t,children:r}}function Mn(t){const{children:a,...s}=t,{isLazy:i,lazyBehavior:n}=St(),{isSelected:r,id:l,tabId:o}=Pn(),u=d.useRef(!1);r&&(u.current=!0);const c=bn({wasSelected:u.current,isSelected:r,enabled:i,mode:n});return{tabIndex:0,...s,children:c?a:null,role:"tabpanel","aria-labelledby":o,hidden:!r,id:l}}function Ba(t,a){return`${t}--tab-${a}`}function Na(t,a){return`${t}--tabpanel-${a}`}const[Rn,wt]=Ut({name:"TabsStylesContext",errorMessage:`useTabsStyles returned is 'undefined'. Seems you forgot to wrap the components in "<Tabs />" `}),La=$e(function(a,s){const i=ys("Tabs",a),{children:n,className:r,...l}=js(a),{htmlProps:o,descendants:u,...c}=Tn(l),b=d.useMemo(()=>c,[c]),{isFitted:x,...g}=o,y={position:"relative",...i.root};return e.jsx(jn,{value:u,children:e.jsx(An,{value:b,children:e.jsx(Rn,{value:i,children:e.jsx(Ke.div,{className:We("chakra-tabs",r),ref:s,...g,__css:y,children:n})})})})});La.displayName="Tabs";const ot=$e(function(a,s){const i=wt(),n=In({...a,ref:s}),r=Ta({outline:"0",display:"flex",alignItems:"center",justifyContent:"center",...i.tab});return e.jsx(Ke.button,{...n,className:We("chakra-tabs__tab",a.className),__css:r})});ot.displayName="Tab";const qa=$e(function(a,s){const i=kn({...a,ref:s}),n=wt(),r=Ta({display:"flex",...n.tablist});return e.jsx(Ke.div,{...i,className:We("chakra-tabs__tablist",a.className),__css:r})});qa.displayName="TabList";const dt=$e(function(a,s){const i=Mn({...a,ref:s}),n=wt();return e.jsx(Ke.div,{outline:"0",...i,className:We("chakra-tabs__tab-panel",a.className),__css:n.tabpanel})});dt.displayName="TabPanel";const za=$e(function(a,s){const i=Fn(a),n=wt();return e.jsx(Ke.div,{...i,width:"100%",ref:s,className:We("chakra-tabs__tab-panels",a.className),__css:n.tabpanels})});za.displayName="TabPanels";var _n={icon:function(a,s){return{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M81.8 537.8a60.3 60.3 0 010-51.5C176.6 286.5 319.8 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c-192.1 0-335.4-100.5-430.2-300.2z",fill:s}},{tag:"path",attrs:{d:"M512 258c-161.3 0-279.4 81.8-362.7 254C232.6 684.2 350.7 766 512 766c161.4 0 279.5-81.8 362.7-254C791.4 339.8 673.3 258 512 258zm-4 430c-97.2 0-176-78.8-176-176s78.8-176 176-176 176 78.8 176 176-78.8 176-176 176z",fill:s}},{tag:"path",attrs:{d:"M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258s279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766z",fill:a}},{tag:"path",attrs:{d:"M508 336c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z",fill:a}}]}},name:"eye",theme:"twotone"},Un=function(a,s){return d.createElement(Aa,ka({},a,{ref:s,icon:_n}))},Dn=d.forwardRef(Un),On={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 708c-22.1 0-40-17.9-40-40s17.9-40 40-40 40 17.9 40 40-17.9 40-40 40zm62.9-219.5a48.3 48.3 0 00-30.9 44.8V620c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8v-21.5c0-23.1 6.7-45.9 19.9-64.9 12.9-18.6 30.9-32.8 52.1-40.9 34-13.1 56-41.6 56-72.7 0-44.1-43.1-80-96-80s-96 35.9-96 80v7.6c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V420c0-39.3 17.2-76 48.4-103.3C430.4 290.4 470 276 512 276s81.6 14.5 111.6 40.7C654.8 344 672 380.7 672 420c0 57.8-38.1 109.8-97.1 132.5z"}}]},name:"question-circle",theme:"filled"},Bn=function(a,s){return d.createElement(Aa,ka({},a,{ref:s,icon:On}))},Nn=d.forwardRef(Bn);function Ln(t){return new qn(t)}class qn{constructor(a){this.type="lazy",this.__isYupSchema__=!0,this.__inputType=void 0,this.__outputType=void 0,this._resolve=(s,i={})=>{let n=this.builder(s,i);if(!$s(n))throw new TypeError("lazy() functions must return a valid schema");return n.resolve(i)},this.builder=a}resolve(a){return this._resolve(a.value,a)}cast(a,s){return this._resolve(a,s).cast(a,s)}validate(a,s,i){return this._resolve(a,s).validate(a,s,i)}validateSync(a,s){return this._resolve(a,s).validateSync(a,s)}validateAt(a,s,i){return this._resolve(s,i).validateAt(a,s,i)}validateSyncAt(a,s,i){return this._resolve(s,i).validateSyncAt(a,s,i)}describe(){return null}isValid(a,s){return this._resolve(a,s).isValid(a,s)}isValidSync(a,s){return this._resolve(a,s).isValidSync(a,s)}}const zn=({id:t})=>{const{t:a}=K(),s=pt();return De(["get-board",t],()=>we.get(`board/${t}`).then(({data:i})=>i),{enabled:t!=null&&t.length>0,onError:i=>{var n,r,l;((n=i.response)==null?void 0:n.status)!==404&&!s.isActive("board-fetching-error")&&s({id:"board-fetching-error",title:a("common.error"),description:a("crud.error_fetching_obj",{obj:a("analytics.board"),e:(l=(r=i==null?void 0:i.response)==null?void 0:r.data)==null?void 0:l.ErrorDescription}),status:"error",duration:5e3,isClosable:!0,position:"top-right"})}})},Vn=({id:t})=>{const{t:a}=K();return pt(),De(["get-board-devices",t],()=>we.get(`board/${t}/devices`).then(({data:s})=>s.devices),{enabled:t!=null&&t.length>0,onError:s=>{var i,n,r;((i=s.response)==null?void 0:i.status)!==404&&D.error(a("crud.error_fetching_obj",{obj:a("analytics.board"),e:(r=(n=s==null?void 0:s.response)==null?void 0:n.data)==null?void 0:r.ErrorDescription}))}})},Qn=({venueId:t,mac:a,fromDate:s,endDate:i,refreshId:n})=>{const{t:r}=K(),l=pt();return De(["get-lifecycles",t,a,s,i,n],()=>we.get(`wifiClientHistory/${a}?venue=${t}&countOnly=true&fromDate=${s}&endDate=${i}`).then(({data:o})=>o.count),{enabled:a!==void 0,onError:o=>{var u,c;l.isActive("lifecycle-count-fetching-error")||l({id:"lifecycle-count-fetching-error",title:r("common.error"),description:r("crud.error_fetching_obj",{obj:r("analytics.board"),e:(c=(u=o==null?void 0:o.response)==null?void 0:u.data)==null?void 0:c.ErrorDescription}),status:"error",duration:5e3,isClosable:!0,position:"top-right"})}})},ia=({pageInfo:t,venueId:a,mac:s,count:i,sortInfo:n,fromDate:r,endDate:l,refreshId:o})=>{let u="";return n&&n.length>0&&(u=`&orderBy=${n.map(c=>`${c.id}:${c.sort.charAt(0)}`).join(",")}`),De(["get-lifecycles-with-pagination",t,i,n,r,l,o],()=>we.get(`wifiClientHistory/${s}?venue=${a}&limit=${(t==null?void 0:t.limit)??10}&offset=${((t==null?void 0:t.limit)??10)*((t==null?void 0:t.index)??1)}${u}&fromDate=${r}&endDate=${l}`).then(({data:c})=>c.entries),{keepPreviousData:!0,enabled:i!==void 0&&t!==void 0,onError:()=>[]})},Gn=()=>Dt(t=>we.post("board/0",t)),Hn=({id:t})=>{const a=Cs();return Dt(s=>we.put(`board/${t}`,s),{onSuccess:()=>{a.invalidateQueries(["get-board",t])}})},$n=()=>Dt(t=>we.delete(`board/${t}`,{})),Kn=({id:t,visible:a,onClose:s,onApplySuccess:i})=>{var p;const{t:n}=K(),[r,l]=d.useState(ke()),[o,u]=d.useState(!1),c=gt({id:t}),b=Gn(),x=Ot({id:t});d.useEffect(()=>{a&&l(ke())},[a]);const g=H().shape({interval:$().required(n("form.required")).moreThan(0).integer(),retention:$().required(n("form.required")).moreThan(0).integer()}),y=Ma({enableReinitialize:!0,validationSchema:g,validateOnMount:!0,initialValues:{name:((p=c.data)==null?void 0:p.name)??"",interval:60,retention:7,monitorSubVenues:!0},onSubmit:async(m,{setSubmitting:v,resetForm:h})=>{var k,P,S;try{const{name:j,interval:C,retention:E,monitorSubVenues:w}=m,L=E*24*60*60,O=await b.mutateAsync({name:j,venueList:[{id:t,name:j,interval:C,retention:L,monitorSubVenues:w}]}),I=(k=O==null?void 0:O.data)==null?void 0:k.id;await x.mutateAsync({params:{boards:[I]}}),D.success(n("crud.success_update_obj",{obj:n("venues.one")})),h(),i==null||i(!1),s()}catch(j){D.error(n("crud.error_create_obj",{obj:n("analytics.board"),e:(S=(P=j==null?void 0:j.response)==null?void 0:P.data)==null?void 0:S.ErrorDescription}))}finally{v(!1)}}}),T=()=>{y.dirty?u(!0):(y.resetForm(),s())},f=()=>{y.resetForm(),u(!1),s()};return e.jsxs(e.Fragment,{children:[e.jsxs(Ie,{title:n("analytics.create_board"),open:a,onCancel:T,footer:[e.jsx(Z,{onClick:T,style:{width:100,height:36},children:n("common.cancel")},"cancel"),e.jsx(Z,{type:"primary",onClick:()=>y.submitForm(),disabled:y.isSubmitting,loading:y.isSubmitting,style:{width:100,height:36,backgroundColor:"#14C9BB",borderColor:"#14C9BB"},children:n("Apply")},"submit")],width:680,bodyStyle:{height:330},children:[e.jsx(de,{style:{margin:"0px 0px 16px -24px",width:"calc(100% + 48px)"}}),e.jsxs(A,{layout:"horizontal",labelAlign:"left",style:{marginBottom:100},children:[e.jsx(A.Item,{label:e.jsxs("span",{style:{width:100,display:"inline-block"},children:[n("analytics.interval"),e.jsx("span",{style:{color:"red",marginLeft:4},children:"*"})]}),labelCol:{span:4},wrapperCol:{span:18},style:{marginBottom:24},validateStatus:y.errors.interval&&y.touched.interval?"error":"",help:y.errors.interval&&y.touched.interval&&y.errors.interval,children:e.jsxs("div",{className:"custom-input-wrapper",style:{position:"relative",width:200},children:[e.jsx(ie,{name:"interval",value:y.values.interval,onChange:m=>y.setFieldValue("interval",m??0),onBlur:()=>y.setFieldTouched("interval",!0),style:{width:"100%"},controls:!0,className:"always-show-controls"}),e.jsx("span",{className:"unit-label",style:{position:"absolute",right:30,top:"50%",transform:"translateY(-50%)",color:"#929A9E",fontSize:14},children:n("common.seconds")})]})}),e.jsx(A.Item,{label:e.jsxs("span",{style:{width:100,display:"inline-block"},children:[n("analytics.retention"),e.jsx("span",{style:{color:"red",marginLeft:4},children:"*"})]}),labelCol:{span:4},wrapperCol:{span:18},style:{marginBottom:24},validateStatus:y.errors.retention&&y.touched.retention?"error":"",help:y.errors.retention&&y.touched.retention&&y.errors.retention,children:e.jsxs("div",{className:"custom-input-wrapper",style:{position:"relative",width:200},children:[e.jsx(ie,{name:"retention",value:y.values.retention,onChange:m=>y.setFieldValue("retention",m??0),onBlur:()=>y.setFieldTouched("retention",!0),style:{width:"100%"},controls:!0,className:"always-show-controls"}),e.jsx("span",{className:"unit-label",style:{position:"absolute",right:30,top:"50%",transform:"translateY(-50%)",color:"#929A9E",fontSize:14},children:n("common.days")})]})})]}),e.jsx(de,{style:{margin:"220px 0px 16px -24px",width:"calc(100% + 48px)"}})]},r),e.jsx(Ie,{title:n("Confirm Discard"),open:o,onOk:f,onCancel:()=>u(!1),okText:n("common.yes"),cancelText:n("common.cancel"),zIndex:2e3,getContainer:!1,children:e.jsx("p",{children:n("Are you sure you want to discard the changes?")})})]})},Wn=({boardId:t,venueId:a,onSuccess:s})=>{const{t:i}=K(),n=$n(),r=Ot({id:a}),l=()=>{Ee(i("Are you sure? This will erase all recorded monitoring data for this venue."),async()=>{var o,u;try{await r.mutateAsync({params:{boards:[]}}),await n.mutateAsync(t),D.success(i("analytics.stop_monitoring_success")),s==null||s()}catch(c){D.error(i("analytics.stop_monitoring_error",{e:(u=(o=c==null?void 0:c.response)==null?void 0:o.data)==null?void 0:u.ErrorDescription}))}},()=>{},!0)};return e.jsx(Z,{className:"stop-button",onClick:l,style:{width:"133px",height:"36px"},loading:n.isLoading||r.isLoading,children:i("Stop Monitoring")})},Jn=({boardId:t,venueId:a,visible:s,onClose:i,onApplySuccess:n})=>{var T,f,p,m,v,h,k;const{t:r}=K(),[l,o]=d.useState(ke()),u=zn({id:t}),c=Hn({id:t}),b=gt({id:a});d.useEffect(()=>{s&&o(ke())},[s]);const x=H().shape({interval:$().required(r("form.required")).moreThan(0).integer(),retention:$().required(r("form.required")).moreThan(0).integer()}),g=Ma({enableReinitialize:!0,validationSchema:x,validateOnMount:!0,initialValues:{name:((T=b.data)==null?void 0:T.name)??"",interval:((p=(f=u.data)==null?void 0:f.venueList[0])==null?void 0:p.interval)??60,retention:(((v=(m=u.data)==null?void 0:m.venueList[0])==null?void 0:v.retention)??604800)/(24*60*60),monitorSubVenues:((k=(h=u.data)==null?void 0:h.venueList[0])==null?void 0:k.monitorSubVenues)??!0},onSubmit:async(P,{setSubmitting:S})=>{var j,C;try{const{name:E,interval:w,retention:L,monitorSubVenues:O}=P;await c.mutateAsync({name:E,venueList:[{id:a,name:E,interval:w,retention:L*24*60*60,monitorSubVenues:O}]}),D.success(r("crud.success_update_obj",{obj:r("analytics.board")})),g.resetForm(),i()}catch(E){D.error(r("crud.error_update_obj",{obj:r("analytics.board"),e:(C=(j=E==null?void 0:E.response)==null?void 0:j.data)==null?void 0:C.ErrorDescription}))}finally{S(!1)}}}),y=()=>{g.resetForm(),i()};return e.jsxs(Ie,{title:r("Monitoring"),open:s,onCancel:y,footer:[e.jsx(Z,{onClick:y,style:{color:"#14C9BB",borderColor:"#14C9BB",height:"36px"},children:r("common.cancel")},"cancel"),e.jsx(Z,{type:"primary",onClick:()=>g.submitForm(),disabled:g.isSubmitting,loading:g.isSubmitting,style:{height:"36px",backgroundColor:"#14C9BB",borderColor:"#14C9BB"},children:r("Apply")},"apply")],width:680,bodyStyle:{height:330},children:[e.jsx("style",{children:`
                  .always-show-controls .ant-input-number-handler-wrap {
                    opacity: 1 !important;
                    display: flex !important;
                    pointer-events: auto !important;
                  }
                  .custom-input-wrapper {
                    position: relative;
                    width: 200px;
                  }
                  .unit-label {
                    position: absolute;
                    right: 30px; /* 放在调整按钮左侧 */
                    top: 50%;
                    transform: translateY(-50%);
                    color: #929A9E;
                    font-size: 14px;
                  }
                  .ant-input-number-input {
                    padding-right: 50px !important; 
                  }
                `}),e.jsx(de,{style:{margin:"0px 0px 16px -24px",width:"calc(100% + 48px)"}}),e.jsx("div",{style:{marginBottom:24},children:e.jsx(Wn,{boardId:t,venueId:a,onSuccess:()=>{i(),n==null||n(!0)}})}),e.jsxs(A,{layout:"horizontal",labelAlign:"left",style:{marginBottom:80},children:[e.jsx(A.Item,{label:e.jsxs("span",{style:{width:100,display:"inline-block"},children:[r("analytics.interval"),e.jsx("span",{style:{color:"red",marginLeft:4},children:"*"})]}),labelCol:{span:4},wrapperCol:{span:18},style:{marginBottom:24},validateStatus:g.errors.interval&&g.touched.interval?"error":"",help:g.errors.interval&&g.touched.interval?g.errors.interval:"",children:e.jsxs("div",{className:"custom-input-wrapper",children:[e.jsx(ie,{name:"interval",value:g.values.interval,onChange:P=>g.setFieldValue("interval",P??0),onBlur:()=>g.setFieldTouched("interval",!0),style:{width:"100%"},controls:!0,className:"always-show-controls"}),e.jsx("span",{className:"unit-label",children:r("common.seconds")})]})}),e.jsx(A.Item,{label:e.jsxs("span",{style:{width:100,display:"inline-block"},children:[r("analytics.retention"),e.jsx("span",{style:{color:"red",marginLeft:4},children:"*"})]}),labelCol:{span:4},wrapperCol:{span:18},style:{marginBottom:24},validateStatus:g.errors.retention&&g.touched.retention?"error":"",help:g.errors.retention&&g.touched.retention?g.errors.retention:"",children:e.jsxs("div",{className:"custom-input-wrapper",children:[e.jsx(ie,{name:"retention",value:g.values.retention,onChange:P=>g.setFieldValue("retention",P??0),onBlur:()=>g.setFieldTouched("retention",!0),style:{width:"100%"},controls:!0,className:"always-show-controls"}),e.jsx("span",{className:"unit-label",children:r("common.days")})]})})]}),e.jsx(de,{style:{margin:"165px 0px 16px -24px",width:"calc(100% + 48px)"}})]},l)},Xn=({id:t,type:a})=>{const s=Ss(),[i,n]=d.useState(!1),r=!s.isLoading,l=d.useMemo(()=>r?s.entityFavorites.favorites.some(({id:c})=>c===t):!1,[t,r,s.entityFavorites.favorites]);return{isFavorite:l,onFavoriteClick:async()=>{n(!0),l?await s.entityFavorites.remove({id:t,type:a}):await s.entityFavorites.add({id:t,type:a}),n(!1)},isLoading:i,getFirstVenueFavoriteId:()=>{const c=s.entityFavorites.favorites.find(b=>b.type==="venue");return c?c.id:null},isReady:r}},{Title:pd,Link:Zn}=ve,me=(t,a,s)=>{const i=t[s],n=a[s];return i==null?1:n==null?-1:i===n?0:typeof i=="number"&&typeof n=="number"?i-n:typeof i=="boolean"&&typeof n=="boolean"?(n?1:0)-(i?1:0):typeof i=="string"&&!isNaN(Date.parse(i))&&typeof n=="string"&&!isNaN(Date.parse(n))?new Date(i).getTime()-new Date(n).getTime():String(i).localeCompare(String(n),void 0,{sensitivity:"base"})},Yn={data:M.instanceOf(Object),isOpen:M.bool.isRequired,onClose:M.func.isRequired,tableOptions:M.shape({prioritizedColumns:M.arrayOf(M.string),sortBy:M.arrayOf(M.shape({id:M.string,desc:M.bool}))})},ei={data:null,tableOptions:null},Gt=({data:t,isOpen:a,onClose:s,tableOptions:i})=>{const{t:n}=K(),[r,l]=d.useState(""),o=window.location.origin,u=w=>window.open(`${o}/wireless/devices/${w}#/devices/${w}`,"_blank"),c=d.useCallback(w=>w?e.jsx(Ct,{date:w},ke()):"--",[]),b=d.useCallback(w=>`${w}%`,[]),x=d.useCallback(w=>n(w?"common.connected":"common.disconnected"),[n]),g=d.useCallback(w=>`${Math.floor(w*100)/100}%`,[]),y=d.useCallback(w=>w!==void 0?tn(w,n):"",[n]),T=d.useCallback(w=>l(w.target.value)),f=d.useMemo(()=>t?r.trim().length===0?[...t.devices,...t.ignoredDevices]:t.devices.filter(L=>L.serialNumber.includes(r)).concat(t.ignoredDevices.filter(L=>L.serialNumber.includes(r))):[],[t,r]),p=()=>{const w=[{key:"serialNumber",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:n("inventory.serial_number")}),dataIndex:"serialNumber",sorter:(I,U)=>me(I,U,"serialNumber"),render:(I,U)=>e.jsx(Zn,{onClick:()=>u(U.serialNumber),style:{color:"#14C9BB",textDecorationLine:"underline"},children:U.serialNumber}),columnsFix:!0,fixed:"left"}],L=[{key:"connected",title:n("common.status"),dataIndex:"connected",sorter:(I,U)=>me(I,U,"connected"),render:x},{key:"health",title:n("analytics.health"),dataIndex:"health",sorter:(I,U)=>me(I,U,"health"),render:b},{key:"lastHealth",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:n("analytics.last_health")}),dataIndex:"lastHealth",sorter:(I,U)=>me(I,U,"lastHealth"),render:c},{key:"lastPing",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:n("analytics.last_ping")}),dataIndex:"lastPing",sorter:(I,U)=>me(I,U,"lastPing"),render:c},{key:"memory",title:n("analytics.memory"),dataIndex:"memory",sorter:(I,U)=>me(I,U,"memory"),render:g},{key:"lastConnection",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:n("analytics.last_connection")}),dataIndex:"lastConnection",sorter:(I,U)=>me(I,U,"lastConnection"),render:c},{key:"lastContact",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:n("analytics.last_contact")}),dataIndex:"lastContact",sorter:(I,U)=>me(I,U,"lastContact"),render:c},{key:"2g",title:"2G",dataIndex:"associations_2g",sorter:(I,U)=>me(I,U,"associations_2g")},{key:"5g",title:"5G",dataIndex:"associations_5g",sorter:(I,U)=>me(I,U,"associations_5g")},{key:"6g",title:"6G",dataIndex:"associations_6g",sorter:(I,U)=>me(I,U,"associations_6g")},{key:"uptime",title:n("analytics.uptime"),dataIndex:"uptime",sorter:(I,U)=>me(I,U,"uptime"),render:y},{key:"deviceType",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:n("inventory.device_type")}),dataIndex:"deviceType",sorter:(I,U)=>me(I,U,"deviceType")},{key:"lastFirmware",title:n("analytics.firmware"),dataIndex:"lastFirmware",sorter:(I,U)=>me(I,U,"lastFirmware")}],O=(i==null?void 0:i.prioritizedColumns)??[];if(O.length>0){const I=L.filter(z=>!O.includes(z.key)),U=O.map(z=>{const B=L.find(R=>R.key===z);return B?{...B,columnsFix:!0}:null}).filter(Boolean);return[...w,...U,...I]}return[...w,...L]},m=d.useMemo(()=>p(),[i,n,c,b,x,g,y]),v=d.useCallback(()=>i&&i.sortBy&&i.sortBy.length>0?i.sortBy.map(w=>({field:w.id,order:w.desc?"descend":"ascend"})):[],[i]),[h,k]=d.useState({current:1,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],showTotal:w=>`Total ${w} items`}),[P,S]=d.useState(v());d.useEffect(()=>{a&&(l(""),k({current:1,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],showTotal:w=>`Total ${w} items`}),S(v()))},[a,i]);const j=d.useCallback((w,L,O)=>{k(I=>({...I,...w})),O.field?S([{field:O.field,order:O.order}]):S([])},[]),C=d.useMemo(()=>{let w=[...f];P&&P.length>0&&w.sort((I,U)=>{for(const z of P){const B=m.find(R=>R.dataIndex===z.field);if(B!=null&&B.sorter){const R=B.sorter(I,U);if(R!==0)return z.order==="ascend"?R:-R}}return 0});const L=(h.current-1)*h.pageSize,O=L+h.pageSize;return w.slice(L,O)},[f,h,P,m]),E=e.jsxs(Je,{direction:"vertical",style:{width:"100%"},children:[e.jsx(Ue,{justify:"flex-end",align:"center",children:e.jsx(ae,{value:r,onChange:T,allowClear:!0,prefix:e.jsx(Pe,{component:Ia}),placeholder:n("analytics.search_serials"),style:{width:280,height:"32px",float:"right",borderRadius:"2px"}})}),e.jsx("div",{style:{overflowX:"auto",width:"100%"},children:e.jsx(Vt,{columns:m,dataSource:C,rowKey:"serialNumber",pagination:{...h,total:f.length},onChange:j,showColumnSelector:!0},JSON.stringify([a,i]))})]});return e.jsx(Xe,{title:n("analytics.raw_analytics_data"),childItems:E,isModalOpen:a,onCancel:s,modalClass:"ampcon-max-modal",footer:null})};Gt.propTypes=Yn;Gt.defaultProps=ei;const ti="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAcpJREFUSEvtlj9oFEEUxr9v9s6gnprsXkAbURELG0HIJQoqISatBDWVhVgLonbGUhC7NIGUIqRO0ljsngQs5HZTWIpYpLLKRROJSQjrfLLrH864dy53Kiky9cz7ve99b94M8Y8XDy1UTxStnko6T9LpjKdPEiaXB0bGASiJxXIYvARwobPA206TN+qVy9MpwKv5cZL5lnTKbHxe6gTk7C3dJ/EQxES9Mnz3h4JUSry+1rMyOLrSCcCNqneMNLELaFrF3ujFRSs7R/L2T5PLYfDXPMgiJ226cwFe6D+geBWU+S17cY3GjLetwA2r5wz0qlVbS3jfNsCrBVdIzAqY/xLrXiOIsgecopNMiHRUtOVBA2B2uX94tBHQPT/TXdhX+vi/AP4qwIMCnpH48MdRIb2t949M5VbghcEjAsl4zb3spj3KLnP2uwetS5T44EbBdSMMgGBLinQrURvb+LjDwpm8gNyZe6G/SPDYzgd4kX8TFpe2SyN4DUTpFwVSCPBx415L7HeAby9aVn3KNf8dyJNNaqcNrR8psutwAc7rZjHSs8KbTIBbe36a3NOXBTDk4lJlKL2lbhSM0WIMRMZnQauwfNK6a3Lb33zjV9AVKPZnhGycAAAAAElFTkSuQmCC",ai=t=>t>90?"neutral":t>70?"warning":"error",_e=t=>t==="-"||t>-45?"neutral":t>-60?"warning":"error",Ht=t=>e.jsx(ue,{title:`${t} clients`,children:e.jsx("div",{style:{width:"41px",height:"24px",background:"rgba(20, 201, 187, 0.1)",borderRadius:"2px",border:"1px solid #14C9BB",display:"flex",alignItems:"center",justifyContent:"center",marginLeft:"12px"},children:e.jsxs(Bt,{color:"cyan",style:{borderRadius:"2px",border:"none",backgroundColor:"transparent",display:"flex",alignItems:"center",marginLeft:"10px",color:"#14C9BB"},children:[e.jsx("img",{src:ti,width:14,height:14,style:{marginRight:4}}),t]})})}),Va=t=>t==="-"?null:e.jsx(ue,{title:"Average level of noise",children:e.jsxs(Bt,{color:_e(t)==="neutral"?"success":_e(t)==="warning"?"warning":"error",style:{..._e(t)==="neutral"?{height:"24px",background:"rgba(43,193,116,0.1)",borderRadius:"2px",border:"1px solid #2BC174",color:"#2BC174",display:"inline-flex"}:{height:"24px",background:"rgba(245,63,63,0.1)",borderRadius:"2px",border:"1px solid #F53F3F",color:"#F53F3F"}},children:[t,"dB"]})}),si=t=>{const{t:a}=K(),s=ai(t),i={neutral:{bg:"rgba(43,193,116,0.1)",border:"1px solid #2BC174",color:"#2BC174"},warning:{bg:"rgba(250,173,20,0.1)",border:"1px solid #FABD14",color:"#FABD14"},error:{bg:"rgba(245,63,63,0.1)",border:"1px solid #F53F3F",color:"#F53F3F"}},{bg:n,border:r,color:l}=i[s];return e.jsx(ue,{title:a("Overall Health"),children:e.jsxs(Bt,{style:{width:"47px",height:"24px",background:n,borderRadius:"2px",border:r,color:l,display:"inline-flex",alignItems:"center",justifyContent:"center"},children:[t,"%"]})})},ni=t=>{const a={devices:[],ignoredDevices:[],totalDevices:0,connectedPercentage:0,connectedDevices:0,disconnectedDevices:0,avgMemoryUsed:0,avgHealth:0,avgUptime:0,twoGAssociations:0,fiveGAssociations:0,sixGAssociations:0,deviceTypeTotals:{Unknown:0},deviceFirmwareTotals:{Unknown:0}};try{const s=[],i=[];let n=0,r=0,l=0;for(const o of t)if(o.deviceType!==""){const u=o.lastFirmware.split(" / ");let c=u.length>1?u[1]:o.lastFirmware;(!c||o.lastFirmware.length===0)&&(c="Unknown"),a.deviceFirmwareTotals[c]?a.deviceFirmwareTotals[c]+=1:a.deviceFirmwareTotals[c]=1,a.deviceTypeTotals[o.deviceType]?a.deviceTypeTotals[o.deviceType]+=1:a.deviceTypeTotals[o.deviceType]=1,o.associations_2g>0&&(a.twoGAssociations+=o.associations_2g),o.associations_5g>0&&(a.fiveGAssociations+=o.associations_5g),o.associations_6g>0&&(a.sixGAssociations+=o.associations_6g),o.memory=Math.round(o.memory),o.connected?(a.connectedDevices+=1,n+=o.health,l+=o.memory,r+=o.uptime):a.disconnectedDevices+=1,s.push(o)}else i.push(o),o.connected?(a.connectedDevices+=1,l+=Math.round(o.memory),r+=o.uptime):a.disconnectedDevices+=1,a.deviceFirmwareTotals.Unknown>0?a.deviceFirmwareTotals.Unknown+=1:a.deviceFirmwareTotals.Unknown=1,a.deviceTypeTotals.Unknown>0?a.deviceTypeTotals.Unknown+=1:a.deviceTypeTotals.Unknown=1;return a.totalDevices=s.length+i.length,a.connectedPercentage=Math.round(a.connectedDevices/Math.max(1,a.totalDevices)*100),a.devices=s,a.avgHealth=Math.round(n/Math.max(1,a.connectedDevices)),a.avgUptime=Math.round(r/Math.max(1,a.connectedDevices)),a.avgMemoryUsed=Math.round(l/Math.max(1,a.connectedDevices)),a.devices=s,a.ignoredDevices=i,a}catch{return a}},ii=(t,a)=>{var i,n,r,l,o,u,c,b,x;const s=[];for(const[g,y]of Object.entries(t)){const T=a.find(v=>v.serialNumber===g);if(!T)continue;const f={serialNumber:g,dashboardData:T,timepoints:y,deltas:{rxBytes:0,txBytes:0,rxPackets:0,txPackets:0},ues:0,rssiStatus:"neutral",averageRssi:"-",radios:{}};for(const v of y)for(const h of v.radio_data)f.radios[h.band]?(i=f.radios[h.band])==null||i.timepoints.push(h):f.radios[h.band]={band:h.band,timepoints:[h],deltas:{rxBytes:0,txBytes:0,rxPackets:0,txPackets:0},amountOfUes:0,rssiStatus:"neutral",averageRssi:"-",ssids:{}};for(const v of y)for(const h of v.ssid_data)if(f.radios[h.band]){(n=f.radios[h.band])!=null&&n.ssids[h.bssid]?(l=(r=f.radios[h.band])==null?void 0:r.ssids[h.bssid])==null||l.timepoints.push(h):f.radios[h.band].ssids[h.bssid]={bssid:h.bssid,ssid:h.ssid,timepoints:[h],deltas:{rxBytes:0,txBytes:0,rxPackets:0,txPackets:0},amountOfUes:0,rssiStatus:"neutral",averageRssi:"-",ues:{},serialNumber:f.serialNumber,band:h.band};for(const k of h.associations)if(f.radios[h.band].ssids[h.bssid]){if((u=(o=f.radios[h.band])==null?void 0:o.ssids[h.bssid])!=null&&u.ues[k.station])(x=(b=(c=f.radios[h.band])==null?void 0:c.ssids[h.bssid])==null?void 0:b.ues[k.station])==null||x.timepoints.push(k);else{const P=Da(k.rssi);f.radios[h.band].ssids[h.bssid].ues[k.station]={station:k.station,timepoints:[k],deltas:{rxBytes:0,txBytes:0,rxPackets:0,txPackets:0},rssiStatus:_e(P),rssi:P,serialNumber:f.serialNumber,band:h.band,ssid:h.ssid}}f.radios[h.band].ssids[h.bssid].ues[k.station].deltas.rxBytes+=k.rx_bytes_delta,f.radios[h.band].ssids[h.bssid].ues[k.station].deltas.txBytes+=k.tx_bytes_delta,f.radios[h.band].ssids[h.bssid].ues[k.station].deltas.rxPackets+=k.rx_packets_delta,f.radios[h.band].ssids[h.bssid].ues[k.station].deltas.txPackets+=k.tx_packets_delta,f.radios[h.band].ssids[h.bssid].deltas.rxBytes+=k.rx_bytes_delta,f.radios[h.band].ssids[h.bssid].deltas.txBytes+=k.tx_bytes_delta,f.radios[h.band].ssids[h.bssid].deltas.rxPackets+=k.rx_packets_delta,f.radios[h.band].ssids[h.bssid].deltas.txPackets+=k.tx_packets_delta,f.radios[h.band].deltas.rxBytes+=k.rx_bytes_delta,f.radios[h.band].deltas.txBytes+=k.tx_bytes_delta,f.radios[h.band].deltas.rxPackets+=k.rx_packets_delta,f.radios[h.band].deltas.txPackets+=k.tx_packets_delta,f.deltas.rxBytes+=k.rx_bytes_delta,f.deltas.txBytes+=k.tx_bytes_delta,f.deltas.rxPackets+=k.rx_packets_delta,f.deltas.txPackets+=k.tx_packets_delta}}let p=0,m=0;for(const[v,h]of Object.entries(f.radios)){let k=0,P=0;for(const[S,j]of Object.entries(h.ssids)){let C=0,E=0;for(const w of Object.values(j.ues))typeof w.rssi=="number"&&(C+=w.rssi,k+=w.rssi,P+=1,E+=1);if(E>0){const w=Math.round(C/E);f.radios[v].ssids[S].averageRssi=w,f.radios[v].ssids[S].rssiStatus=_e(w),f.radios[v].ssids[S].amountOfUes=E}}if(P>0){p+=k,m+=P;const S=Math.round(k/P);f.radios[v].averageRssi=S,f.radios[v].rssiStatus=_e(S),f.radios[v].amountOfUes=P}}if(m>0){const v=Math.round(p/m);f.averageRssi=v,f.rssiStatus=_e(v),f.ues=m}s.push(f)}return s.sort((g,y)=>g.serialNumber.localeCompare(y.serialNumber))},ri=()=>{const t=new Date;return t.setMinutes(t.getMinutes()-10),t},Qa=G.createContext({venueId:""}),li=({venueId:t,venueData:a,children:s})=>{var v;const i=ws(),[n,r]=G.useState(),l=(v=a==null?void 0:a.boards)==null?void 0:v[0],o=Vn({id:l}),[u]=G.useState(ri()),c=De(["get-venue-timepoints",l,u.toString()],async()=>{if(!l)return[];try{const h=await un({id:l,startTime:u});return h?h.status!==200?(D.error(h.info),[]):h.info||[]:[]}catch{return D.error("Failed to fetch timepoints"),[]}},{enabled:!!l,keepPreviousData:!0,staleTime:1/0}),[b,x]=G.useState(),g=G.useCallback(h=>{x(h)},[]),y=G.useCallback(()=>{x(void 0)},[]),T=G.useCallback(h=>{r(h),i.onOpen()},[]),f=G.useMemo(()=>{if(o.data)return ni(o.data)},[o.data]),p=G.useMemo(()=>{if(!(!c.data||!f))return ii(c.data,f.devices)},[c.data,f]),m=G.useMemo(()=>({venueId:t,dashboard:f,timepoints:c.data,monitoring:p,handleDashboardModalOpen:T,selectedItem:b,onSelectItem:g,onUnselectItem:y}),[t,f,c.data,p,T,b]);return G.useEffect(()=>{x(void 0)},[t]),e.jsx(Qa.Provider,{value:m,children:e.jsxs(e.Fragment,{children:[s,e.jsx(Gt,{data:m.dashboard,tableOptions:n,...i})]})})},je=()=>G.useContext(Qa),oi=(t,a=2)=>{if(!t||t===0)return"0 MB";const s=1024,i=a<0?0:a,n=t/(s*s);return`${parseFloat(n.toFixed(i))} MB`},ra=({bytes:t,style:a})=>{const{Text:s}=ve,i=d.useMemo(()=>t===void 0?"-":oi(t),[t]);return e.jsx(s,{style:a,children:i})},di=()=>{const{t}=K(),[a,s]=G.useState("associations"),{monitoring:i}=je(),n=G.useMemo(()=>[{title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:t("inventory.serial_number")}),dataIndex:"serialNumber",key:"serialNumber",sorter:(o,u)=>o.serialNumber.localeCompare(u.serialNumber),columnsFix:!0},{title:"RX(MB)",dataIndex:"rxBytes",key:"rxBytes",render:o=>e.jsx(ra,{bytes:o}),sorter:(o,u)=>o.rxBytes-u.rxBytes},{title:"TX(MB)",dataIndex:"txBytes",key:"txBytes",render:o=>e.jsx(ra,{bytes:o}),sorter:(o,u)=>o.txBytes-u.txBytes},{title:"2G",dataIndex:"associations_2g",key:"associations_2g",sorter:(o,u)=>o.associations_2g-u.associations_2g},{title:"5G",dataIndex:"associations_5g",key:"associations_5g",sorter:(o,u)=>o.associations_5g-u.associations_5g},{title:"6G",dataIndex:"associations_6g",key:"associations_6g",sorter:(o,u)=>o.associations_6g-u.associations_6g},{title:t("analytics.health"),dataIndex:"health",key:"health",render:o=>`${Math.floor(o*100)/100}%`,sorter:(o,u)=>o.health-u.health},{title:t("analytics.memory"),dataIndex:"memory",key:"memory",render:o=>`${Math.floor(o*100)/100}%`,sorter:(o,u)=>o.memory-u.memory}],[t]),r=G.useMemo(()=>i==null?void 0:i.map(o=>({...o.dashboardData,totalAssociations:o.dashboardData.associations_2g+o.dashboardData.associations_5g+o.dashboardData.associations_6g,totalTraffic:o.deltas.rxBytes+o.deltas.txBytes,rxBytes:o.deltas.rxBytes,txBytes:o.deltas.txBytes})).sort((o,u)=>a==="associations"?u.totalAssociations-o.totalAssociations:u.totalTraffic-o.totalTraffic).slice(0,10),[i,a]),l=e.jsxs("div",{style:{display:"flex",alignItems:"center",position:"relative",marginTop:"20px"},children:[e.jsx("span",{style:{width:"45px",height:"17px",fontFamily:"Lato, sans-serif",fontWeight:500,fontSize:"14px",color:"#212519",lineHeight:"17px",marginRight:"56px"},children:"Sort by"}),e.jsxs(Ce.Group,{value:a,onChange:o=>s(o.target.value),style:{display:"flex",gap:"56px"},children:[e.jsx(Ce,{value:"associations",style:{width:"43px",height:"17px",fontWeight:400,lineHeight:"17px",margin:0},children:"Clients"}),e.jsx(Ce,{value:"traffic",style:{width:"41px",height:"17px",fontWeight:400,lineHeight:"17px",margin:0},children:"Traffic"})]})]});return e.jsxs(xt,{headStyle:{padding:"0 16px"},title:e.jsx("div",{style:{fontSize:"18px"},children:"Top 10 Busiest Devices"}),style:{height:"100%",border:"none"},bodyStyle:{padding:"16px",display:"flex",flexDirection:"column",height:"calc(100% - 57px)",marginBottom:"20px"},children:[l,e.jsx(Vt,{columns:n,dataSource:r,isShowPagination:!1,showColumnSelector:!0,style:{maxHeight:"245px",overflowY:"auto"},bordered:!0,scroll:{x:1200}})]})},{Option:ci}=Q,{Text:gd,Title:xd}=ve,ui=({macs:t,setMac:a,value:s})=>{const{t:i}=K(),[n,r]=d.useState(s),l=d.useCallback((x,g)=>x?g.value.includes(x.replace("*","")):!0,[t]),o=x=>{a(x),r(x)},u=x=>{r(x)},c=d.useCallback(()=>{r("")},[]);d.useEffect(()=>{r(s)},[s]);const b=x=>{x===void 0&&r("")};return e.jsx(Q,{showSearch:!0,style:{width:280,borderRadius:36},placeholder:i("common.search"),value:n||void 0,onSelect:o,onSearch:u,onFocus:c,onChange:b,filterOption:l,allowClear:!0,bordered:!0,dropdownMatchSelectWidth:!0,getPopupContainer:x=>x.parentElement,optionFilterProp:"value",children:t==null?void 0:t.map(x=>e.jsx(ci,{value:x,children:x},x))})},hi=({isTrue:t})=>{const a=d.useMemo(()=>t===void 0?"-":t?ta("common.yes"):ta("common.no"),[t]);return e.jsx("div",{children:a})},la=G.memo(hi),fi=({db:t})=>{const a=d.useMemo(()=>t===void 0?"-":Da(t),[t]);return e.jsx("div",{children:a})},at=G.memo(fi),mi=({seconds:t})=>{const{t:a}=K(),s=d.useMemo(()=>t===void 0?"-":an(t,a),[t]);return e.jsx("div",{children:s})},oa=G.memo(mi),pi=({milliseconds:t})=>{const{t:a}=K(),s=d.useMemo(()=>{if(t===void 0)return"-";if(!t||t===0)return`0 ${a("common.seconds")}`;const i=t%1e3;let n=Math.floor(t/1e3);const r=Math.floor(n/(3600*24));n-=r*(3600*24);const l=Math.floor(n/3600);n-=l*3600;const o=Math.floor(n/60);n-=o*60;let u="";return u+=`${st(r)}d`,u+=`${st(l)}h`,u+=`${st(o)}m`,u+=`${st(n)}.`,u+=`${gi(i)}s`,u},[t,a]);return e.jsx("div",{children:s})},st=t=>t>=10?t.toString():`0${t}`,gi=t=>t>=100?t.toString():t>=10?`0${t}`:`00${t}`,ze=G.memo(pi),xi=({value:t})=>{const a=d.useMemo(()=>t===void 0?"-":t.toLocaleString(),[t]);return e.jsx("div",{children:a})},nt=G.memo(xi),bi=({useCount:t,useGet:a,countParams:s={},getParams:i={}})=>{const[n,r]=d.useState(void 0),{data:l,isFetching:o,refetch:u}=t(s),{data:c,isFetching:b,refetch:x}=a({pageInfo:n,enabled:n!==null,count:l,...i});return d.useMemo(()=>({count:l,data:c,isFetching:o||b,refetchCount:u,refetchData:x,pageInfo:n,setPageInfo:r}),[l,c,o,b])},{Title:vi}=ve,yi=({venueId:t,mac:a,fromDate:s,endDate:i,timePickers:n,searchBar:r})=>{const{t:l}=K(),[o,u]=d.useState([{id:"timestamp",sort:"dsc"}]),[c,b]=d.useState({index:0,limit:10});d.useEffect(()=>{a&&(b({index:0,limit:10}),u([{id:"timestamp",sort:"dsc"}]))},[a]);const{count:x,data:g,isFetching:y}=bi({useCount:Qn,useGet:ia,countParams:{venueId:t,mac:a,sortInfo:o,fromDate:s,endDate:i},getParams:{venueId:t,mac:a,sortInfo:o,fromDate:s,endDate:i,pageInfo:c}}),T=(p,m,v)=>{b({index:p.current-1,limit:p.pageSize}),v.field&&u([{id:v.field,sort:v.order==="ascend"?"asc":"desc"}])},f=d.useMemo(()=>[{key:"timestamp",title:l("common.timestamp"),dataIndex:"timestamp",render:p=>e.jsx(Ct,{date:p}),fixed:"left",sorter:!0,isMonospace:!0,columnsFix:!0},{key:"bssid",title:"BSSID",dataIndex:"bssid",sorter:!0,isMonospace:!0},{key:"ssid",title:"SSID",dataIndex:"ssid",sorter:!0,isMonospace:!0},{key:"rssi",title:"RSSI(db)",dataIndex:"rssi",render:p=>e.jsx(at,{db:p}),sorter:!0,isMonospace:!0},{key:"noise",title:`${l("analytics.noise")}(db)`,dataIndex:"noise",render:p=>e.jsx(at,{db:p}),sorter:!0,isMonospace:!0},{key:"channel",title:l("analytics.channel"),dataIndex:"channel",sorter:!0,isMonospace:!0},{key:"tx_power",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX Power"}),dataIndex:"tx_power",sorter:!0,isMonospace:!0},{key:"tx_retries",title:e.jsxs("span",{style:{whiteSpace:"nowrap"},children:["TX ",l("analytics.retries")]}),dataIndex:"tx_retries",sorter:!0,isMonospace:!0},{key:"connected",title:l("analytics.connected"),dataIndex:"connected",render:p=>e.jsx(oa,{seconds:p}),sorter:!0,isMonospace:!0},{key:"inactive",title:l("analytics.inactive"),dataIndex:"inactive",render:p=>e.jsx(oa,{seconds:p}),sorter:!0,isMonospace:!0},{key:"ack_signal",title:e.jsxs("span",{style:{whiteSpace:"nowrap"},children:[l("analytics.ack_signal"),"(db)"]}),dataIndex:"ack_signal",render:p=>e.jsx(at,{db:p}),sorter:!0,isMonospace:!0},{key:"ack_signal_avg",title:e.jsxs("span",{style:{whiteSpace:"nowrap"},children:[l("analytics.ack_signal"),l("common.avg"),"(db)"]}),dataIndex:"ack_signal_avg",render:p=>e.jsx(at,{db:p}),sorter:!0,isMonospace:!0},{key:"rx_bytes",title:"RX",dataIndex:"rx_bytes",render:p=>e.jsx(sa,{bytes:p}),sorter:!0,isMonospace:!0},{key:"rx_mcs",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"RX MCS"}),dataIndex:"rx_mcs",sorter:!0,isMonospace:!0},{key:"rx_nss",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"RX NSS"}),dataIndex:"rx_nss",sorter:!0,isMonospace:!0},{key:"tx_bytes",title:"TX",dataIndex:"tx_bytes",render:p=>e.jsx(sa,{bytes:p}),sorter:!0,isMonospace:!0},{key:"tx_mcs",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX MCS"}),dataIndex:"tx_mcs",sorter:!0,isMonospace:!0},{key:"tx_nss",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX NSS"}),dataIndex:"tx_nss",sorter:!0,isMonospace:!0},{key:"rx_bitrate",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"RX Bitrate"}),dataIndex:"rx_bitrate",render:p=>e.jsx(nt,{value:p}),sorter:!0,isMonospace:!0},{key:"rx_chwidth",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"RX Ch Width"}),dataIndex:"rx_chwidth",sorter:!0,isMonospace:!0},{key:"rx_duration",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"RX Duration"}),dataIndex:"rx_duration",render:p=>e.jsx(ze,{milliseconds:p}),sorter:!0,isMonospace:!0},{key:"rx_packets",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"RX Packets"}),dataIndex:"rx_packets",render:p=>e.jsx(nt,{value:p}),sorter:!0,isMonospace:!0},{key:"rx_vht",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"RX VHT"}),dataIndex:"rx_vht",render:p=>e.jsx(la,{isTrue:p}),sorter:!0,isMonospace:!0},{key:"tx_bitrate",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX Bitrate"}),dataIndex:"tx_bitrate",render:p=>e.jsx(nt,{value:p}),sorter:!0,isMonospace:!0},{key:"tx_chwidth",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX Ch Width"}),dataIndex:"tx_chwidth",sorter:!0,isMonospace:!0},{key:"tx_vht",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX VHT"}),dataIndex:"tx_vht",render:p=>e.jsx(la,{isTrue:p}),sorter:!0,isMonospace:!0},{key:"tx_duration",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX Duration"}),dataIndex:"tx_duration",render:p=>e.jsx(ze,{milliseconds:p}),sorter:!0,isMonospace:!0},{key:"tx_packets",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX Packets"}),dataIndex:"tx_packets",render:p=>e.jsx(nt,{value:p}),sorter:!0,isMonospace:!0},{key:"ipv4",title:"IPv4",dataIndex:"ipv4",sorter:!0,isMonospace:!0},{key:"ipv6",title:"IPv6",dataIndex:"ipv6",sorter:!0,isMonospace:!0},{key:"channel_width",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"Ch Width"}),dataIndex:"channel_width",sorter:!0,isMonospace:!0},{key:"active_ms",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"Active MS"}),dataIndex:"active_ms",render:p=>e.jsx(ze,{milliseconds:p}),sorter:!0,isMonospace:!0},{key:"busy_ms",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"Busy MS"}),dataIndex:"busy_ms",render:p=>e.jsx(ze,{milliseconds:p}),sorter:!0,isMonospace:!0},{key:"receive_ms",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"Receive MS"}),dataIndex:"receive_ms",render:p=>e.jsx(ze,{milliseconds:p}),sorter:!0,isMonospace:!0},{key:"mode",title:l("analytics.mode"),dataIndex:"mode",sorter:!0,isMonospace:!0}],[l]);return e.jsxs(xt,{title:e.jsx(vi,{level:4,style:{margin:0,fontSize:"18px"},children:l("analytics.client_lifecycle")}),style:{height:"100%",border:"none"},bodyStyle:{padding:"16px",minWidth:"100%",overflow:"auto",maxWidth:"70vw",marginBottom:"6px"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginTop:"20px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx("span",{style:{marginLeft:"12px",marginRight:"32px",fontSize:"14px"},children:"Time"}),e.jsx("div",{children:n})]}),e.jsx("div",{style:{marginRight:"2px"},children:r})]}),e.jsx(Vt,{columns:f,dataSource:g||[],loading:y,onChange:T,showColumnSelector:!0,fetchAPIInfo:ia,pagination:{current:c.index+1,pageSize:c.limit,total:x,showSizeChanger:!0,showQuickJumper:!0,showTotal:p=>`Total ${p} items`,pageSizeOptions:["10","20","50","100"]}})]})},ji=async(t,a)=>we.get(`wifiClientHistory?macsOnly=true&venue=${t}&limit=500&offset=${a}`).then(({data:s})=>s.entries),Ci=async t=>{const a=[];let s=!0,i=0;for(;s;){const n=await ji(t,i);(!n||n.length<500||i>=5e4)&&(s=!1),a.push(...n),i+=500}return a},Si=({venueId:t})=>{var T,f,p;const{t:a}=K(),s=bt(),[i,n]=d.useState(),[r,l]=d.useState(),[o,u]=d.useState([ut(sn(3*24)),ut()]),c=d.useRef(null);d.useEffect(()=>{var m;if((m=s.state)!=null&&m.targetMac){const v=s.state.targetMac.replace(/[:\\-]/g,"");l(v)}},[s.state]),d.useEffect(()=>{var m;(m=s.state)!=null&&m.scrollToClientLifecycle&&c.current&&c.current.scrollIntoView({behavior:"smooth"})},[s.state]);const b=d.useCallback(async()=>{try{return await Ci(t)}catch{return}},[t]);d.useEffect(()=>{b().then(m=>{var v;if(n(m),(v=s.state)!=null&&v.targetMac){const h=s.state.targetMac.replace(/[:\-]/g,"");l(h)}else m&&m.length>0?l(m[0]):l(void 0)})},[b,(T=s.state)==null?void 0:T.targetMac]);const x=m=>{u(m)},g=o&&(f=o[0])!=null&&f.valueOf()?Math.floor(o[0].valueOf()/1e3):0,y=o&&(p=o[1])!=null&&p.valueOf()?Math.floor(o[1].valueOf()/1e3):0;return e.jsx("div",{ref:c,children:e.jsx(yi,{fromDate:g,endDate:y,venueId:t,mac:r,timePickers:e.jsx(Ks,{value:o,onChange:x,tooltipText:a("controller.crud.choose_time")}),searchBar:e.jsx(ui,{macs:i,setMac:l,value:r})})})},{Title:wi,Text:Pt}=ve,Ti=()=>{const{t}=K(),{selectedItem:a,onUnselectItem:s}=je(),i=()=>{s()};if(!a)return null;const n=(c,b,x)=>{const g=x%2===0;return e.jsx(F,{span:12,style:{padding:"0 4px",height:"48px",display:"flex",alignItems:"center"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",width:"100%",height:"100%",backgroundColor:g?"#F8FAFB":"#FFFFFF"},children:[e.jsx(Pt,{style:{fontSize:"14px",color:"#474747",whiteSpace:"nowrap",paddingLeft:"16px",width:"160px",flexShrink:0},children:c}),e.jsx("span",{style:{fontWeight:600,fontSize:"14px",color:"#212519",whiteSpace:"nowrap"},children:b})]})})},r=(c,b,x)=>e.jsx(F,{span:24,style:{padding:"0 4px",height:"48px",display:"flex",alignItems:"center"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",width:"100%",height:"100%",backgroundColor:"#F8FAFB"},children:[e.jsx(Pt,{style:{fontSize:"14px",color:"#474747",whiteSpace:"nowrap",paddingLeft:"16px",width:"160px",flexShrink:0},children:c}),e.jsx("span",{style:{fontWeight:600,fontSize:"14px",color:"#212519",whiteSpace:"nowrap"},children:b})]})}),l=()=>{var c;if(a.type==="AP")return e.jsxs("div",{style:{width:"100%"},children:[e.jsxs(_,{gutter:8,children:[n(t("analytics.health"),`${a.data.dashboardData.health}%`,0),n(t("analytics.memory_used"),`${Math.floor(a.data.dashboardData.memory)}%`,0)]}),e.jsxs(_,{gutter:8,children:[n(t("common.type"),a.data.dashboardData.deviceType,1),n(t("analytics.firmware"),((c=a.data.dashboardData.lastFirmware)==null?void 0:c.split("/")[1])??t("common.unknown"),1)]}),e.jsxs(_,{gutter:8,children:[n("2G Clients",a.data.dashboardData.associations_2g,2),n("5G Clients",a.data.dashboardData.associations_5g,2)]}),e.jsx(_,{gutter:8,children:n("6G Clients",a.data.dashboardData.associations_6g,3)}),e.jsx(_,{style:{marginTop:8},children:e.jsx(F,{span:24,children:e.jsx(wi,{level:5,style:{marginBottom:16},children:"Last 10 Minutes"})})}),e.jsxs(_,{gutter:8,children:[n("Tx",fe(a.data.deltas.txBytes),4),n("Tx Packets",a.data.deltas.txPackets.toLocaleString(),4)]}),e.jsxs(_,{gutter:8,children:[n("Rx",fe(a.data.deltas.rxBytes),5),n("Rx Packets",a.data.deltas.rxPackets.toLocaleString(),5)]})]});if(a.type==="RADIO"){const b=a.data.timepoints[a.data.timepoints.length-1];return e.jsxs("div",{style:{width:"100%"},children:[e.jsxs(_,{gutter:8,children:[n(t("analytics.noise"),`${a.data.averageRssi} dB`,0),n(t("analytics.channel"),b.channel,0)]}),e.jsxs(_,{gutter:8,children:[n(t("analytics.temperature"),`${b.temperature}°C`,1),n(t("analytics.airtime"),`${b.transmit_pct.toFixed(2)}%`,1)]}),e.jsxs(_,{gutter:8,children:[n(t("analytics.active"),`${b.active_pct.toFixed(2)}%`,2),n(t("analytics.busy"),`${b.busy_pct.toFixed(2)}%`,2)]}),e.jsx(_,{gutter:8,children:n(t("analytics.receive"),`${b.receive_pct.toFixed(2)}%`,3)})]})}if(a.type==="SSID"){const b=a.data.timepoints[a.data.timepoints.length-1];return e.jsxs("div",{style:{width:"100%"},children:[e.jsxs(_,{gutter:8,children:[n("Bssid",a.data.bssid,0),n("Noise",`${a.data.averageRssi} dB`,0)]}),e.jsxs(_,{gutter:8,children:[n("TX Bandwidth (avg)",fe(b.tx_bytes_bw.avg),1),n("TX Bandwidth (min)",fe(b.tx_bytes_bw.min),1)]}),e.jsxs(_,{gutter:8,children:[n("TX Bandwidth (max)",fe(b.tx_bytes_bw.max),2),n("TX Packets/s",`${b.tx_packets_bw.avg.toFixed(2)} / ${b.tx_packets_bw.min.toFixed(2)} / ${b.tx_packets_bw.max.toFixed(2)}`,2)]}),e.jsxs(_,{gutter:8,children:[n("RX Bandwidth (avg)",fe(b.rx_bytes_bw.avg),3),n("RX Bandwidth (min)",fe(b.rx_bytes_bw.min),3)]}),e.jsxs(_,{gutter:8,children:[n("RX Bandwidth (max)",fe(b.rx_bytes_bw.max),4),n("RX Packets/s",`${b.rx_packets_bw.avg.toFixed(2)} / ${b.rx_packets_bw.min.toFixed(2)} / ${b.rx_packets_bw.max.toFixed(2)}`,4)]})]})}if(a.type==="UE"){const b=a.data.timepoints[a.data.timepoints.length-1];return e.jsxs("div",{style:{width:"100%"},children:[e.jsxs(_,{gutter:8,children:[r("Connected to",`${a.data.band}G - ${a.data.ssid}`),e.jsx(F,{span:24,style:{padding:"12px 16px",backgroundColor:"#FFFFFF",height:"48px",display:"flex",alignItems:"center",marginLeft:-12},children:e.jsx(Pt,{strong:!0,children:"Data (TX / RX)"})})]}),e.jsxs(_,{gutter:8,children:[n(t("analytics.total_data"),`${fe(b.tx_bytes)} / ${fe(b.rx_bytes)}`,1),n(t("analytics.delta"),`${fe(a.data.deltas.txBytes)} / ${fe(a.data.deltas.rxBytes)}`,1)]}),e.jsxs(_,{gutter:8,children:[n(t("analytics.bandwidth"),`${fe(b.tx_bytes_bw)} / ${fe(b.rx_bytes_bw)}`,2),n(`${t("analytics.packets")}/s`,`${na(b.tx_packets_bw)} / ${na(b.rx_packets_bw)}`,2)]}),e.jsxs(_,{gutter:8,children:[n("MCS",`${b.tx_rate.mcs} / ${b.rx_rate.mcs}`,3),n("NSS",`${b.tx_rate.nss} / ${b.rx_rate.nss}`,3)]})]})}return e.jsx("pre",{children:JSON.stringify(a,null,2)})},o=()=>{const c={margin:0,display:"inline-block"};switch(a.type){case"AP":return e.jsx("span",{style:c,children:a.data.serialNumber});case"RADIO":return e.jsxs("span",{style:c,children:[a.serialNumber," - ",a.data.band,"G"]});case"SSID":return e.jsx("span",{style:c,children:a.data.ssid});case"UE":return e.jsx("span",{style:c,children:a.data.station});default:return e.jsx("span",{style:c,children:"Details"})}},u=e.jsx("div",{style:{width:"100%",height:"auto",overflowY:"auto",paddingRight:"8px"},children:l()});return e.jsx(Xe,{title:e.jsxs("div",{children:[o(),e.jsx(de,{style:{marginBottom:0,marginLeft:-24,marginRight:-24,width:"calc(100% + 48px)"}})]}),childItems:u,isModalOpen:!!a,onCancel:i,footer:null,modalClass:"ampcon-middle-modal"})},Ai="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAVRJREFUWEftl61OA0EUhc9tQ1J+EqhD4BDlASDBgKjfUZOVk6DwFTwEAo8iWdxmMTu+AgwJPAAIHALFT0KhSdNeMmS3tATBbCeZilk1m9w555sj7twheP7Isz+mAM4yvVUHHwNoA1h2DNcD0B2Cjg5kdFdqjwGMeQ2jawKtOjaekmPw2wi13RJiDJBkeQ4gMtXMeCLCo0sQZmwQYb3Q1EoKYdaTAO8mdmPeb9DmYRR9uAQ41Xqp0eeHAqKnpFj5DcCF4a2SYseleamVZPkNgG3zr6T4PvxkAgEgJBASmM8ETtJ0sYmFFgF1m97AwPAFg/tOHH/O1AeSTF8CvGdj/lNLV0pG+5UBmJnOL/QzgLVqAHhVUjQrA5iNSZq3mRATsdXswExMjFTFojsTQMWT/7kt3AUhgZDA3CfgfSj1O5Z7f5iY3un1aeay59toWd1wNsL/rf0CyIoyML8htJMAAAAASUVORK5CYII=",ki="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAY5JREFUWEftl9FNwzAQhs+xeC8blA3KBu0CgUgGiZe0mQAxAWIC2CBtX5AgUtouQEZgA7IBeY+OwEV25FSBppajCCl+TOy7z//Zv20GPTfWc36oAYTP8Zif8HsAuASAkWW4DAASzPEuuPFSFbsCkMnfAGBsOfF+uBRznCmICmAd7WI5cxpAtO+WQSaaqktfuAHF1wE+ZYcMOZ4FnkcQ1loYxyOO/EPl8IV7ug9QyGyJL9yZtcxaoHW0oxJP6ZMv3HLyugL/C0BbtKAvqr+Us6rAKtrOGbAlJSygWMzFxepQ2QaAQQFjBcLXuNy7enPAmTLG6NygzfyEX7itdSggC669mpsaAeiDDq3yhv81UzMFIPs0PaAyZbkEZwQQvsQT7vDbhtkRlCpNAgDVESvLklFZgiuP/pXNCOA32QcjGhToXYHacczxvM0NyuouMDAlu9uwCwB1KU2xpaTHQLS5lIY/braQQbu4lpNzKkvf+ML1apfS3h8mRCMhHqXHd/E022COD41Ps2PqabNv76/jbwuaeTBZWLHEAAAAAElFTkSuQmCC",Ii="data:image/png;base64,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",Ei="data:image/png;base64,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",Pi="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAopJREFUWEftlk9u2kAUxr/Hn6pZlS4qwSqDBOuSG5ATNDkBuUHhBIQTkJwg9AThCM4NnLWpmE0LUhf1qkEy4UUzxu50sMEGpKhSLCEhmHnzm+997z0TXvmhVz4fbwD/rwJCVAXKzxUERV/KudzXS5kUCA9DG4wOEQkA6mM/PgCXmR8AOHIyd7JAbQUQzeoVgb4CaGUJZq3xmfkWy8WNlL6CS3wSAUTzUwtcHBJRO+mmzOwC5IM4DMxUIdKQicowYyAns5skgg0A0aheE1HfXMzMDojGwPJBer/ctNsIUang3fsvWNEFES6sdQ4HT5e2GjGA2lwondzxvxslgwfSm4/ypmDtmz6Broy9klG4lN6P+BIxQL1Zm5oSMqOXJpsOXjLkXkICCz8p16JRbRPRnRHb54DPosrRAOr2VD75vSbdoNRrGtU2CB2C9kV6FYC/2YopYCprCO0pBrrSm92q739T0Kh1QfwZAQZmXadIuSsjialT/gLhFMGiF6m1vQwbtS4RhpvlBQfgx/h3wmmSMgQarYI/8WGZqiBalFANOw2pyrfApb5lZHfqzc5y9QG1uN6sKU9UdM6YB3Iyv96luwGvjHdv7D9P64ypKRCh/B1e0UB+/zk2D9e+KBZaKK4+hI3o+dHuD5HxGCzNnNuXyDQL4puFM8Gu7ehvySvq2bC7VMsMsK7nWNa0wHnTlQOgdm+0V8nAGMSungPQbTeeGxw8fdw2gEz4HADhjGDwyO4VYaPSnlEzRLn+fJf00f+ZAbIEVB01680PBohfUlB0zeGSBXSvFNiBRbM2JKALQE69WT3vwYcrEL4tqQHj5Mn5QX1gQ4U9cn5UgH1lP4oHjnG4inHUMtwH6g3gBYM/JDB6E3/cAAAAAElFTkSuQmCC",Fi="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAa5JREFUWEftl09OwlAQxr8xkuAOFybFjY9I99xAuAGeQDyBcgLkBOAJ8AjeQLwB+2L6VraJC7vXdPQ9IFDoH+E1EmPfsu915teZb95MCXtetGf/KAASIyDqVtM4PZ+QUvoyzc4GgDg/bdMBDwAIY4CZgTF/8HUSyAZAza6+A6jk5FybYWAoHa8bZzMOgPVLjC7AExMQIup9R6DJzGM59VtbAnBLTv2xCYCwrRGBOgXAPiPQIdBoRxGaa0DpR9gnDem8JYo5pQryAcgScW4AQlQqKJUHSvUJTgNm9OXUG67u5wdgV28IiBiPA3EdL+IzP4C6dTe/eAImvo04D1Gb7+E3AKTreLVVANXYiOhJPfs7AAAkM6e2Uv2VRBPVaMQyBcYRUKHaahbgkC5BYWOeZzMAXU6HZSWii9RBgki17IbunMQdLIVmBpB1cSz2hbAElcj9HwDCrg4IaMdER49taykImPk+cpZwtrghM8tw3YnSBJWO1JiWuJi5BYJQnS8jhYHreMepV3GcAT0hE64SjD9Lx3/QnW9WiknnAg6pL19eH7cG+KkwdzlX/BkVEfgCM+guMMvWbsIAAAAASUVORK5CYII=",Mi=({expanded:t,isLeaf:a})=>a?e.jsx("span",{style:{display:"inline-block",width:16}}):e.jsx("img",{src:t?Ai:ki,alt:"Expand/Collapse Icon",style:{width:"16px",height:"16px",marginRight:"4px",verticalAlign:"middle"}}),Ri=()=>e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e.jsx(As,{image:ks,description:"No Data",imageStyle:{marginTop:16,marginBottom:0}})}),_i=()=>{const t=je(),a=t.monitoring,s=a?a.map(r=>({key:`ap-${r.serialNumber}`,title:e.jsx(Ui,{data:r}),children:Object.values(r.radios).map(l=>({key:`radio-${r.serialNumber}-${l.band}`,title:e.jsx(Di,{data:l,serialNumber:r.serialNumber}),children:Object.values(l.ssids).map(o=>({key:`ssid-${o.bssid}`,title:e.jsx(Oi,{data:o}),children:Object.values(o.ues).map(u=>({key:`ue-${u.station}`,title:e.jsx(Bi,{data:u}),isLeaf:!0}))}))}))})):[],i=(r,{node:l})=>{const o=r[0];if(o){if(o.startsWith("ap-")){const u=o.replace("ap-",""),c=a.find(b=>b.serialNumber===u);c&&t.onSelectItem({type:"AP",data:c})}else if(o.startsWith("radio-")){const[u,c]=o.replace("radio-","").split("-"),b=a.find(x=>x.serialNumber===u);if(b){const x=Object.values(b.radios).find(g=>g.band==c);x&&t.onSelectItem({type:"RADIO",data:x,serialNumber:u})}}else if(o.startsWith("ssid-")){const u=o.replace("ssid-","");let c=null;a.forEach(b=>{Object.values(b.radios).forEach(x=>{Object.values(x.ssids).forEach(g=>{g.bssid===u&&(c=g)})})}),c&&t.onSelectItem({type:"SSID",data:c})}else if(o.startsWith("ue-")){const u=o.replace("ue-","");let c=null;a.forEach(b=>{Object.values(b.radios).forEach(x=>{Object.values(x.ssids).forEach(g=>{Object.values(g.ues).forEach(y=>{y.station===u&&(c=y)})})})}),c&&t.onSelectItem({type:"UE",data:c})}}},n=s.length===0;return e.jsx(xt,{title:e.jsx("div",{style:{fontSize:"18px"},children:"Live Data"}),style:{height:"100%",display:"flex",flexDirection:"column",border:"none"},bodyStyle:{flex:1,overflow:"hidden",padding:"16px"},children:e.jsx(_,{gutter:16,style:{height:"100%"},children:e.jsx(F,{span:24,style:{height:"100%"},children:e.jsxs("div",{style:{height:"100%",overflowY:n?"hidden":"auto"},children:[s.length>0?e.jsx(Ts,{multiple:!1,defaultExpandAll:!1,onSelect:i,treeData:s,showIcon:!1,switcherIcon:Mi,showLine:!0,expandAction:"click",style:{fontFamily:"Lato",lineHeight:"32px",minHeight:"100%"}}):e.jsx(Ri,{}),e.jsx(F,{span:8,children:e.jsx(Ti,{})})]})})})})},Ui=({data:t})=>{var r;const{t:a}=K(),s=je(),i=((r=s.selectedItem)==null?void 0:r.type)==="AP"&&s.selectedItem.data.dashboardData.serialNumber===t.dashboardData.serialNumber,n=window.location.origin;return e.jsxs("div",{style:{display:"inline-flex",alignItems:"center",fontWeight:i?"bold":"normal",padding:"2px 4px",verticalAlign:"middle"},children:[e.jsx("img",{src:Ii,alt:"MAC Address Logo",width:"20",height:"20"}),e.jsx("span",{style:{fontFamily:"Lato",marginRight:"16px",marginLeft:"4px"},children:t.serialNumber}),e.jsx(ue,{title:a("common.view_in_gateway"),placement:"top",children:e.jsx(Z,{style:{width:"48px",height:"24px"},type:"primary",onClick:()=>window.open(`${n}/wireless/devices/${t.serialNumber}#/devices/${t.serialNumber}`,"_blank"),children:"View"})}),e.jsxs("div",{style:{display:"flex",alignItems:"center",height:"24px",gap:"8px",marginLeft:"4px"},children:[Ht(t.ues),si(t.dashboardData.health)]}),Va(t.averageRssi)]})},Di=({data:t,serialNumber:a})=>{var n;const s=je(),i=((n=s.selectedItem)==null?void 0:n.type)==="RADIO"&&s.selectedItem.data.band===t.band&&s.selectedItem.serialNumber===a;return e.jsxs("div",{style:{display:"flex",alignItems:"center",fontWeight:i?"bold":"normal",padding:"2px 4px"},children:[e.jsx("img",{src:Pi,alt:"WiFi Signal",width:"20",height:"20",style:{marginRight:"4px"}}),e.jsxs("span",{style:{fontFamily:"Lato",marginRight:"4px"},children:[t.band,"G"]}),e.jsx("span",{style:{marginRight:"49px"},children:Ht(t.amountOfUes)})]})},Oi=({data:t})=>{var i;const a=je(),s=((i=a.selectedItem)==null?void 0:i.type)==="SSID"&&a.selectedItem.data.bssid===t.bssid;return e.jsxs("div",{style:{display:"flex",alignItems:"center",fontWeight:s?"bold":"normal",padding:"2px 4px"},children:[e.jsx("img",{src:Ei,alt:"Wireless Signal",width:"20",height:"20",style:{marginRight:"4px"}}),e.jsx("span",{style:{fontFamily:"Lato",marginRight:"6px",whiteSpace:"nowrap"},children:t.ssid}),Ht(t.amountOfUes)]})},Bi=({data:t})=>{var i;const a=je(),s=((i=a.selectedItem)==null?void 0:i.type)==="UE"&&a.selectedItem.data.station===t.station;return e.jsxs("div",{style:{display:"flex",alignItems:"center",fontWeight:s?"bold":"normal",padding:"2px 4px"},children:[e.jsx("img",{src:Fi,alt:"User Icon",width:"20",height:"20",style:{marginRight:"4px"}}),e.jsx("span",{style:{fontFamily:"Lato",marginRight:"4px"},children:t.station}),Va(t.rssi)]})},Ni="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAA1VJREFUWEftV0toE1EUvffNpB+1ChrNTJs2v0nB70IriCCiKEIXXYmiogu3IijiRtciKChSPwsXIi787VxqKS5UqJ+FKP5m4oytSSa21kVLNU3yriR04uQnrTPFFvKW99177plz77z7HsIcXzjH+UGdoNMKzT8FAwGvzBrEXiLsRqAxArhqaOYZAMg6VeNf4ssVZEFFeoqIm+xgRPyUoaXOWjav19uyaIlnHzBo/pektWI4hyeDseQr+34JwfaIb43I2JtyAALSDdUMW/agIt1FxD1ukpvCmkznfkUTn38MWtglBDsi8gaBwcsqBBOGarZZ9lBUfgQAO2aBIOSIVg9q5ruqBBUFGrMovUfAUElypKv6J/OIZeuI+sIM8CQANLlJEgn7dS15q2aJ8xsBxbuegec2IHQSAQHAg3Ehc3Dk48iYm2Smi1XrmBFDIW8kzfhEIjY6NF2w2fCbf+fgbKjgBLOuoBP18rHzUkEhEPXtZcC6ifNxDux6+fhxqspM4isUDCryBUQ4bgNJA2GPriUe2oH9/sVLPZ6FLTNJ9jffSSGdi2ujCQDgNQ9qf+eyNg95DAAU7U4ENGCoZvECEYpIJwjhPCK62iJE8MzQklvtN6eSBIHIis2MCU+rfOmIriaXF2exIr0AxC631LPjZHg2+jU2rFWdxa2drd4GTl8QYUFZ8j5dTe60bIFo635G/BogLnaPJGUJ8L6hJg8AFEZsYVWUKBTxHSXEi4goTPkMT2ZzO+P6t9fukZk+UtUeag/LXQxhFzI+Bhl+zzCGzelDuuvpapO7S61GiWcjiRPMuoJO1Ju3sxj8fn+zKGbWcRHHBzXzffn4carKTOIrz8GQvIVEuoOArXkgInqc+Snsjsfj32cC7JZvCcGCcs05FQGKT8wCSaAbhmoetpJ2KNIqhngaqGLiOOKFxPv1WKrXDlJCMByWNpKAzyuyEJm6ZsqWPahI/Yi4zRGbGsFZztcOxVJvq466QEBeyRqg+GguYhCoupbs/ENQvokIh9wmSAQTuXQmOjQ0kr92FVZ5D2JQkfoQcbs9OQE/ZqipS5bN74dmsdHXAwK6+nCHSTZgGIkPNUuc3yhcRJsWniOkbiQcI+RXDDV1+X/9yfVJ4rRP6wo6VfA381ATOPL5SfMAAAAASUVORK5CYII=",Li="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAdJJREFUOE+NVLtxwkAQ3T2JgAx3AB3gTJDYCgSRwFQA7sBUAFQAHUAHfBMDM5AhR6YDXAKRCRBas7KkEZIOsYlm7t4+7b59twiSmC13LUSsA0AeAIoMI4IfIWALgNuz8jtt6Poxmo7RAyYCwA6iS3QvjkTUrlVKozDohnD6uesKgZ0UoptrAhjUDK3tHwaEs5XVR4CPBDJuaw8AOa99/t6E41CvXi11+dAl9PQayoDjzSbHevFXsbOdpB8T0Tu3jwxS7ezBqyDgvGo4OiunduaS7V+H0XKHAk67ZpQHs6V1iGrMA6tVtAIu1l9NIroRlpNtujyrQnkBgkG4ctPQcLGy+pQgD1eJ85U1vmr0FtPOcRogRDN6Z6unp4yd7SQRXu01waTyZVP2xb+Ts+cK6RGbBGRyN7g0jxJOTENrLNbW0B+QrAgm/PaflgzkDogwD0Kw3tLgScuHEkqzbSqoKvYThxfCsdVQZupHdE1yhvdS4kYNgY82XXQVFX7jcXt5wMDY956eh92ahqanLw7STaO0DZZDSgIviNhS8LuILQf/Ir2KuLJhsmDbhGHz1e6VCIdpC5Y1A6De3QUbJUbEJhEUif7bRYQjIuzp4kzNanmS5IQ/6ZXqaIgQp5cAAAAASUVORK5CYII=",qi={width:"100%",height:"180px",borderRadius:"8px",boxSizing:"border-box",border:"none",padding:0,margin:0,overflow:"hidden"},zi={height:"100%",width:"100%",padding:0,margin:0,boxSizing:"border-box"},Vi={display:"flex",justifyContent:"space-between",alignItems:"center",padding:"0 16px",boxSizing:"border-box",height:"60px",margin:0},Qi="120px",Gi={margin:0,borderColor:"#DEEAF3",padding:0},Hi={border:"none",outline:"none",background:"transparent",padding:0},$i={marginLeft:2,width:12,height:12},Ga=document.createElement("style");Ga.textContent=`
  .custom-metric-card .ant-card-body {
    padding: 0 !important;
    margin: 0 !important;
  }
  .custom-metric-card .ant-card-head {
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: none !important;
  }
  .custom-metric-card {
    padding: 0 !important;
    margin: 0 !important;
  }
`;document.head.appendChild(Ga);const{Title:Ki}=ve,Tt=({title:t,explanation:a,bgGradient:s,content:i,onDetailClick:n,buttonColor:r})=>{const{t:l}=K(),[o,u]=d.useState(!1),c=typeof a=="string"?a:a.key,b=typeof a=="object"?a.params||{}:{},x={...Hi,backgroundColor:o?r:"transparent",border:"none",padding:"6px 6px",display:"flex",borderRadius:"2px"};return e.jsx(xt,{style:{...qi,background:s,boxShadow:"none"},bodyStyle:{padding:0,margin:0,border:0,boxShadow:"none"},bordered:!1,className:"custom-metric-card",children:e.jsxs("div",{style:zi,children:[e.jsxs("div",{style:Vi,children:[e.jsxs(Ki,{level:4,style:{margin:0,paddingLeft:0,whiteSpace:"nowrap"},children:[l(t),e.jsx(ue,{title:l(c,b),children:e.jsx("img",{src:Li,style:$i})})]}),e.jsx(ue,{title:l("common.view_details"),children:e.jsx("div",{"aria-label":l("common.view_details"),onClick:n,style:x,onMouseEnter:()=>u(!0),onMouseLeave:()=>u(!1),children:e.jsx("img",{src:Ni,style:{height:20,width:20}})})})]}),e.jsx(de,{style:Gi}),e.jsx("div",{style:{height:Qi,padding:0,boxSizing:"border-box",overflow:"hidden"},children:i})]})})},Wi="/assets/Logo_Health-CXJwZwbP.png",{Text:Ji}=ve,Xi=()=>{const{t}=K(),{dashboard:a,handleDashboardModalOpen:s}=je(),i=`${(a==null?void 0:a.avgHealth)??0}%`;return e.jsx(Tt,{title:"analytics.average_health",explanation:"analytics.average_health_explanation",bgGradient:"linear-gradient(180deg, #F2F9FE 0%, #E6F4FE 100%)",buttonColor:"#D9EFFC",content:e.jsxs("div",{style:{display:"flex",alignItems:"center",height:"100%",padding:0},children:[e.jsx(Ji,{style:{fontSize:"24px",fontWeight:"bold",marginLeft:"32px",whiteSpace:"nowrap"},children:i}),e.jsx("div",{style:{flex:1}}),e.jsx("img",{src:Wi,alt:"Health Curve Icon",style:{marginRight:"32px",width:"115px",height:"70px"}})]}),onDetailClick:()=>s({prioritizedColumns:["lastHealth","health"],sortBy:[{id:"health",desc:!1}]})})},Zi=d.memo(({value:t,color:a="#52c41a",bgColor:s="#ccc",height:i="120px",width:n="120px"})=>{const r=d.useRef();return d.useEffect(()=>{const l=Is(r.current),o={series:[{type:"pie",radius:["45%","65%"],center:["50%","50%"],data:[{value:t,name:"",itemStyle:{color:a}},{value:100-t,name:"",itemStyle:{color:s}}],label:{show:!1},emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};l.setOption(o);const u=()=>{l.resize()};return window.addEventListener("resize",u),()=>{window.removeEventListener("resize",u),l.dispose()}},[t,a,s]),e.jsx("div",{style:{height:i,width:n,marginLeft:"auto"},ref:r})}),{Text:Yi}=ve,er=()=>{const{t}=K(),{dashboard:a,handleDashboardModalOpen:s}=je(),i=`${(a==null?void 0:a.avgMemoryUsed)??0}%`;return e.jsx(Tt,{title:"analytics.average_memory",explanation:"analytics.average_memory_explanation",bgGradient:"linear-gradient(180deg, #F5FEF2 0%, #E6FEEE 100%)",buttonColor:"#DDF6D9 ",content:e.jsxs("div",{style:{display:"flex",alignItems:"center",height:"100%",padding:0},children:[e.jsx(Yi,{style:{fontSize:"24px",fontWeight:"bold",marginLeft:"32px",whiteSpace:"nowrap"},children:i}),e.jsx("div",{style:{flex:1}}),e.jsx(Zi,{value:(a==null?void 0:a.avgMemoryUsed)??0,style:{marginRight:"32px"}})]}),onDetailClick:()=>s({prioritizedColumns:["lastPing","memory"],sortBy:[{id:"memory",desc:!0}]})})},tr="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAYAAADhu0ooAAAAAXNSR0IArs4c6QAAAlFJREFUaEPtWz1PG0EQnY89USAcKQpKEZfpEBiliGiwcISVgjhABb8ALCpk/kCUUKGQJjRJHaTocBPZ0CTgiAJooMcY/wIEDkgpbIyju7OlFMbRrdCxvoztK1e65zdv5u3OLA5mEzWF7HzBe2h3byr/AkL2waFs4pqQWTWBEtLP/al8ImQ4wQMKzIo8RsMNVBgNTwCLRsPDpYdEGBVGu/QfkDraibiZ7/OTCvkpMYMF5BiNRqU/srYx8KZqKuFajM78SOcU0ituuimFXP/dU324PvLx0mygPp1R9wL16XW7F6gw2l5xwqjxyUg0GrbQFUaFUXFGZltA0aho1LdGlw9XR5DoJYECRc5DgDf1r4uxheMgwl3rzEjHAr47+rBEQO/dtkfrsPwGU4vDc/nggAagUTOABrB7MQOoMHp3WVcYDTQZiUbvN3Rt22Z+Hon1IKIFFjg/Zuti/PFo2W9JMrqOfjnZivRacOFWXmRwm9WkcuNPRl/rATU063pA8ZyBuAWUkPPJaDylB9RQjd490JAxWqyUPxHSM4XkhLozaaPXHw3K6+oyWqyUC4Q41pq0UazqgfVedAyDPtBSgZDbAA2ZRoVRv5NjotEOLQnRaAdTr5+MJOv6m+4UjYpGAYJNRqJR0WjbbZ/5yUi8rr+ZemHUmPLyn2fdmnu9p3nvBZF2D/5xwWd2O/2NkSZaa5Qz3Vm9erSR/PzrtkOrt0erGQa14u763W4aQaPRmM7E0rnb1tin9gOL+84YyX0/Zy0hbyaj8clOh2PHldKOIhX/+/3+ALGCM2mKKzofAAAAAElFTkSuQmCC",{Text:da}=ve,ar=()=>{const{t}=K(),{dashboard:a,handleDashboardModalOpen:s}=je(),i=[{label:"2G",value:(a==null?void 0:a.twoGAssociations)??0},{label:"5G",value:(a==null?void 0:a.fiveGAssociations)??0},{label:"6G",value:(a==null?void 0:a.sixGAssociations)??0}],n=e.jsx("div",{style:{display:"flex",justifyContent:"space-around",padding:"16px",height:"100%",marginTop:12},children:i.map(({label:r,value:l})=>e.jsxs("div",{style:{display:"flex",width:"33.33%",justifyContent:"center"},children:[e.jsx("img",{src:tr,alt:`${r} Associations`,style:{height:24,width:24,marginRight:8,marginTop:24}}),e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"flex-start"},children:[e.jsx(da,{style:{fontSize:"24px",fontWeight:"900"},children:l}),e.jsx(da,{style:{fontSize:"16px",color:"#777",marginTop:4},children:r})]})]},r))});return e.jsx(Tt,{title:"analytics.associations",explanation:"analytics.associations_explanation",bgGradient:"linear-gradient(180deg, #F2F9FE 0%, #E6F4FE 100%)",content:n,buttonColor:"#D9EFFC",onDetailClick:()=>s({prioritizedColumns:["2g","5g","6g"],sortBy:[{id:"2g",desc:!0},{id:"5g",desc:!0},{id:"6g",desc:!0}]})})},{Title:bd,Text:it}=ve,sr=()=>{const{t}=K(),{dashboard:a,handleDashboardModalOpen:s}=je(),i=e.jsxs("div",{style:{display:"flex",justifyContent:"space-around",alignItems:"center",padding:"24px 8px",height:"100%"},children:[e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",marginRight:32},children:[e.jsx(it,{style:{fontSize:"24px",fontWeight:"bold"},children:(a==null?void 0:a.connectedDevices)??0}),e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx("div",{style:{width:10,height:10,borderRadius:"50%",background:"#52c41a",marginRight:4}}),e.jsx(it,{style:{fontSize:"16px",whiteSpace:"nowrap"},children:t("analytics.connected")})]})]}),e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center"},children:[e.jsx(it,{style:{fontSize:"24px",fontWeight:"bold"},children:(a==null?void 0:a.disconnectedDevices)??0}),e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx("div",{style:{width:10,height:10,borderRadius:"50%",background:"#ccc",marginRight:4}}),e.jsx(it,{style:{fontSize:"16px",color:"#999",whiteSpace:"nowrap"},children:t("analytics.disconnected")})]})]})]});return e.jsx(Tt,{title:"common.status",explanation:{key:"analytics.total_devices_explanation",params:{connectedCount:(a==null?void 0:a.connectedDevices)??0,disconnectedCount:(a==null?void 0:a.disconnectedDevices)??0}},bgGradient:"linear-gradient(180deg, #F6F7FF 0%, #ECECFF 100%)",content:i,buttonColor:"#DCE0FD",onDetailClick:()=>s({prioritizedColumns:["connected"],sortBy:[{id:"connected",desc:!0}]})})},nr=()=>e.jsxs("div",{style:{width:"100%",margin:"0 auto ",display:"flex",gap:"32px"},children:[e.jsx("div",{style:{flex:1,minWidth:0},children:e.jsx(Xi,{})}),e.jsx("div",{style:{flex:1,minWidth:0},children:e.jsx(er,{})}),e.jsx("div",{style:{flex:1,minWidth:0},children:e.jsx(sr,{})}),e.jsx("div",{style:{flex:1.22,minWidth:0},children:e.jsx(ar,{})})]}),ir=({venueId:t="0",venueData:a})=>{const{selectedSiteId:s,setSelectedSiteId:i}=Oa(),[n,r]=d.useState(t),l=bt();return d.useEffect(()=>{const o=window.location.hash.substring(1);/^\d+$/.test(o)?(r(o),i(o)):s?(r(s),window.location.hash.includes(s)||window.history.replaceState({},"",`#${s}`)):r(t)},[l.hash,s]),d.useEffect(()=>{var o;if((o=l.state)!=null&&o.scrollToClientLifecycle){const u=document.getElementById("client-lifecycle-card");u&&u.scrollIntoView({behavior:"smooth"})}},[l]),e.jsx(li,{venueId:n,venueData:a,children:e.jsxs("div",{style:or,children:[e.jsx(nr,{}),e.jsxs("div",{style:dr,children:[e.jsx("div",{style:cr,children:e.jsx(di,{})}),e.jsx("div",{style:ur,children:e.jsx(_i,{})})]}),e.jsx("div",{id:"client-lifecycle-card",style:hr,children:e.jsx(Si,{venueId:n})})]})})},rr=32,lr=16,or={display:"flex",flexDirection:"column",gap:"24px",width:"100%",padding:`${lr}px`,boxSizing:"border-box"},dr={display:"grid",gridTemplateColumns:"1fr 1fr 1fr 1.22fr",gap:`${rr}px`,width:"100%",boxSizing:"border-box"},cr={gridColumn:"1 / span 3",minHeight:"360px",background:"#FFFFFF",border:"1px solid #E7E7E7",borderRadius:"8px",overflow:"hidden"},ur={gridColumn:"4",minHeight:"360px",background:"#FFFFFF",border:"1px solid #E7E7E7",borderRadius:"8px",overflow:"hidden"},hr={height:"auto",minHeight:"380px",background:"#FFFFFF",border:"1px solid #E7E7E7",borderRadius:"8px",overflow:"hidden",width:"100%",boxSizing:"border-box"};function J(t){var i,n;let a=t;const s=Es;if(t&&s){const r=t.split("."),{length:l}=r;if(l>=2){const o=r.slice(0,l-1),u=r[l-1];a=(n=(i=s[o.slice(0,l-1).join(".")])==null?void 0:i.properties[u??""])==null?void 0:n.description}}return{title:a,icon:e.jsx(Nn,{style:{color:"#B3BBC8",cursor:"pointer",paddingLeft:0,marginLeft:2,width:10,height:10}})}}const{Option:fr}=Q,Ve={labelCol:{style:{width:150}},wrapperCol:{style:{width:300}}},mr=({namePrefix:t,values:a,setFieldValue:s})=>{const{t:i}=K(),n=X.get(a,[t,"band"]),r=X.get(a,[t,"legacy-rates"]),l=d.useMemo(()=>n==="2G"&&r?[{value:1e3,label:"1000"},{value:2e3,label:"2000"},{value:5500,label:"5500"},{value:6e3,label:"6000"},{value:9e3,label:"9000"},{value:11e3,label:"11000"},{value:12e3,label:"12000"},{value:18e3,label:"18000"},{value:24e3,label:"24000"},{value:36e3,label:"36000"},{value:48e3,label:"48000"},{value:54e3,label:"54000"}]:[{value:6e3,label:"6000"},{value:9e3,label:"9000"},{value:12e3,label:"12000"},{value:18e3,label:"18000"},{value:24e3,label:"24000"},{value:36e3,label:"36000"},{value:48e3,label:"48000"},{value:54e3,label:"54000"}],[n,r,i]);return d.useEffect(()=>{const o=X.get(a,[t,"he"]);o&&typeof o=="object"&&Object.keys(o).length===0&&s([t,"he"],void 0);const u=X.get(a,[t,"rates"]);u&&typeof u=="object"&&Object.keys(u).length===0&&s([t,"rates"],void 0)},[a,t,s]),e.jsxs(e.Fragment,{children:[e.jsxs(_,{gutter:64,children:[e.jsx(F,{span:10,children:e.jsx(A.Item,{label:"Beacon-Rate",tooltip:J("radio.rates.beacon"),...Ve,children:e.jsx(Q,{style:{width:280},value:X.get(a,[t,"rates","beacon"],6e3),onChange:o=>{s([t,"rates","beacon"],o)},placeholder:i("common.none"),children:l.map(o=>e.jsx(Q.Option,{value:o.value,children:o.label},o.value))})})}),e.jsx(F,{span:10,children:e.jsx(A.Item,{label:"Beacon-Interval",required:!0,...Ve,validateStatus:X.get(a,[t,"beacon-interval"],100)<=14||X.get(a,[t,"beacon-interval"],100)>=65535?"error":"",help:X.get(a,[t,"beacon-interval"],100)<=14?"Beacon-Interval must be greater than 14":X.get(a,[t,"beacon-interval"],100)>=65535?"Beacon-Interval must be less than 65535":"",children:e.jsx(ie,{value:X.get(a,[t,"beacon-interval"],100),onChange:o=>s([t,"beacon-interval"],o??100),style:{width:140}})})})]}),e.jsxs(_,{gutter:64,children:[e.jsx(F,{span:10,children:e.jsx(A.Item,{label:"Multicast",tooltip:J("radio.rates.multicast"),...Ve,style:{marginBottom:4},children:e.jsx(Q,{style:{width:280},value:X.get(a,[t,"rates","multicast"],24e3),onChange:o=>{s([t,"rates","multicast"],o)},placeholder:"Select multicast rate",children:l.map(o=>e.jsx(fr,{value:o.value,children:o.label},o.value))})})}),e.jsx(F,{span:10,children:e.jsx(A.Item,{label:"BSS-Color",tooltip:J("radio.he.bss-color"),...Ve,style:{marginBottom:4},className:"inline-error-fixed",required:!0,validateStatus:typeof X.get(a,[t,"he","bss-color"])=="number"&&X.get(a,[t,"he","bss-color"])<0?"error":"",help:typeof X.get(a,[t,"he","bss-color"])=="number"&&X.get(a,[t,"he","bss-color"])<0?"BSS-Color cannot be negative":"",children:e.jsx(ie,{max:63,style:{width:140},value:X.get(a,[t,"he","bss-color"],0),onChange:o=>{s([t,"he","bss-color"],o??0)}})})})]}),e.jsx(_,{gutter:64,children:n!=="2G"&&e.jsx(F,{span:10,children:e.jsx(A.Item,{label:"Allow-DFS",tooltip:J("radio.allow-dfs"),...Ve,style:{marginTop:20,marginBottom:4},children:e.jsx(te,{checked:X.get(a,[t,"allow-dfs"]),onChange:o=>s([t,"allow-dfs"],o)})})})})]})},Fe="/ampcon/wireless/configure";function pr({site_id:t,name:a,security:s,radio:i,network_name:n,ssid_configure:r,labels_name:l,network_type:o,vlan_or_dhcp_name:u}){return oe({url:`${Fe}/ssid`,method:"POST",data:{site_id:t,name:a,security:s,radio:i,network_name:n,ssid_configure:r,labels_name:l,network_type:o,vlan_or_dhcp_name:u}})}function gr({id:t,name:a,security:s,radio:i,network_name:n,ssid_configure:r,labels_name:l,network_type:o,vlan_or_dhcp_name:u}){return oe({url:`${Fe}/ssid`,method:"PUT",data:{id:t,name:a,security:s,radio:i,network_name:n,ssid_configure:r,labels_name:l,network_type:o,vlan_or_dhcp_name:u}})}function xr({id:t,is_enable:a}){return oe({url:`${Fe}/ssid`,method:"PUT",data:{id:t,is_enable:a}})}function br({id:t}){return oe({url:`${Fe}/ssid`,method:"DELETE",data:{id:t}})}function vr(t,a,s,i=[],n=[],r={}){return oe({url:`${Fe}/ssid/list`,method:"POST",data:{site_id:t,filterFields:i,sortFields:n,searchFields:r,page:a,pageSize:s}})}function ca(t){return oe({url:`${Fe}/ssid`,method:"GET",params:{id:t}})}function yr(t){return oe({url:`${Fe}/channel`,method:"GET",params:{countryCode:t}})}function Ha(t,a,s){return oe({url:`${Fe}/general`,method:"PUT",data:{id:t,site_id:a,config:s}})}const{Option:jr}=Q,Qe={labelCol:{style:{width:150}},wrapperCol:{style:{width:300}}},Ft=(t,a,s,i)=>{var p;const n=`${t.toLowerCase()}_channel`,r=Object.keys((i==null?void 0:i[n])||{}).filter(m=>m!=="dfs"),l=String(X.get(a,[t,"channel-width"])),o=r.map(m=>({label:`${m} MHz`,value:String(m)})),u=l?((p=i==null?void 0:i[n])==null?void 0:p[l])||[]:[],c=X.get(a,[t,"maximum-clients"]),b=typeof c=="number"&&c<1,g=X.get(a,[t,"valid-channels"],[]).filter(m=>u.includes(m)),y=g.length>0&&g.length===u.length,T=m=>{s([t,"valid-channels"],m)},f=m=>{m.target.checked?s([t,"valid-channels"],u):s([t,"valid-channels"],[])};return e.jsx(A,{layout:"horizontal",labelAlign:"left",children:e.jsxs("div",{children:[e.jsxs(_,{gutter:64,children:[e.jsx(F,{span:10,children:e.jsx(A.Item,{label:"TX-Power",...Qe,children:e.jsxs(Ce.Group,{value:typeof X.get(a,[t,"tx-power"])=="number"?"Set Power":"Default",onChange:m=>{m.target.value==="Default"?s([t,"tx-power"],void 0):typeof X.get(a,[t,"tx-power"])!="number"&&s([t,"tx-power"],1)},children:[e.jsx(Ce,{value:"Default",children:"Default"}),e.jsx(Ce,{value:"Set Power",children:e.jsxs("span",{style:{display:"inline-flex",alignItems:"center"},children:["Set Power",typeof X.get(a,[t,"tx-power"])=="number"&&e.jsx(ie,{min:1,max:30,value:X.get(a,[t,"tx-power"]),onChange:m=>s([t,"tx-power"],m),style:{width:110,marginLeft:8},addonAfter:"dBm"})]})})]})})}),e.jsx(F,{span:10,children:e.jsx(A.Item,{label:"Channel-Width",tooltip:J("radio.channel-width"),required:!0,...Qe,children:e.jsx(Q,{style:{width:280},value:o.some(m=>m.value===l)?l:"None",onChange:m=>{var h;s([t,"channel-width"],m);const v=((h=i==null?void 0:i[n])==null?void 0:h[m])||[];s([t,"valid-channels"],v)},children:o.map(m=>e.jsx(jr,{value:m.value,children:m.label},m.value))})})})]}),e.jsx(A.Item,{hidden:!0,name:[t,"channel"],children:e.jsx(ae,{defaultValue:"auto"})}),e.jsx(_,{gutter:64,children:e.jsx(F,{span:24,children:e.jsxs(A.Item,{label:"Valid-Channels",required:!0,...Qe,validateStatus:g.length===0?"error":"",help:g.length===0?"Valid-Channels cannot be empty":"",children:[e.jsx(Be,{indeterminate:g.length>0&&!y,checked:y,onChange:f,style:{marginBottom:8,marginTop:5},disabled:u.length===0,children:"ALL"}),e.jsx(Be.Group,{value:g,onChange:T,style:{display:"flex",flexWrap:"wrap",gap:"8px 12px"},children:u.map(m=>e.jsx(Be,{value:m,style:{width:60},children:m},m))})]})})}),e.jsxs(_,{gutter:64,children:[e.jsx(F,{span:10,children:e.jsx(A.Item,{...Qe,label:"Maximum-Clients",tooltip:J("radio.maximum-clients"),validateStatus:b?"error":"",help:b?"Maximum-Clients must be a positive number":"",children:e.jsx(ie,{style:{width:140},value:c,onChange:m=>s([t,"maximum-clients"],m)})})}),t==="2G"&&e.jsx(F,{span:10,children:e.jsx(A.Item,{label:"Legacy-Rates",tooltip:J("radio.legacy-rates"),...Qe,children:e.jsx(te,{checked:X.get(a,[t,"legacy-rates"]),onChange:m=>{s([t,"legacy-rates"],m?!0:void 0),s([t,"rates","multicast"],24e3),s([t,"rates","beacon"],6e3)}})})})]}),e.jsx(mr,{namePrefix:t,values:a,setFieldValue:s})]})})},Cr=d.forwardRef(({initialValues:t,onApply:a,countryCode:s,onChange:i},n)=>{const r=d.useRef(null),[l,o]=d.useState({}),[u,c]=d.useState(t),[b,x]=d.useState([]);return d.useImperativeHandle(n,()=>({submit:()=>{const g=r.current;if(!g)return;const y=X.omit(g.values,b);for(const T in y)if(y[T]=X.omitBy(y[T],X.isNil),X.has(y[T],"channel-width")){const f=y[T]["channel-width"];f!=null&&f!=="auto"&&f!=="None"&&(y[T]["channel-width"]=Number(f))}a(y)},resetForm:()=>{const g=r.current;g&&g.resetForm({values:t})}})),d.useEffect(()=>{yr(s).then(g=>{const y=g||{};o(y);const T=[],f=X.cloneDeep(t);["2G","5G","6G"].forEach(p=>{const m=`${p.toLowerCase()}_channel`,v=y[m],h=Object.keys(v||{}),k=X.get(f,[p,"channel-width"]),P=h.length>0;P||T.push(p);const S=k&&h.includes(String(k)),j=p==="2G"?"20":"40",C=S?k:h.includes(j)?j:h[0]||"none";X.set(f,[p,"channel-width"],C),X.set(f,[p,"channel"],"auto");const E=(v==null?void 0:v[String(C)])||[],L=X.get(t,[p,"valid-channels"],[]).filter(I=>E.includes(I));X.set(f,[p,"valid-channels"],L.length>0?L:E),X.get(f,[p,"maximum-clients"])===void 0&&P&&X.set(f,[p,"maximum-clients"],64),["2G","5G","6G"].forEach(I=>{(T.includes(I)||!X.has(f,I))&&(X.set(f,[I,"rates","multicast"],24e3),X.set(f,[I,"rates","beacon"],6e3))})}),x(T),c(f)})},[s,t]),e.jsx("div",{children:e.jsx(qt,{innerRef:r,enableReinitialize:!0,initialValues:u,onSubmit:g=>{a(g)},children:({values:g,setFieldValue:y})=>{const T=(f,p)=>{f[f.length-1]==="channel-width"&&p!=="None"&&(p=Number(p));const m=f.join("."),v=X.set(X.cloneDeep(g),m,p),h=zt(X.omitBy(v,k=>typeof k["channel-width"]=="string"));i==null||i(h),y(m,p)};return e.jsx(Ra,{children:e.jsxs(Re,{className:"SetRadioForm",style:{marginBottom:0,border:"none"},expandIconPosition:"right",children:[!b.includes("2G")&&e.jsx(Re.Panel,{header:e.jsx("h3",{style:{fontSize:"16px",margin:0,border:"none"},children:"2G Radio"}),children:Ft("2G",g,T,l)},"2g"),e.jsx("div",{style:{height:20,backgroundColor:"#ffff"}}),!b.includes("5G")&&e.jsx(Re.Panel,{header:e.jsx("h3",{style:{fontSize:"16px",margin:0,border:"none"},children:"5G Radio"}),children:Ft("5G",g,T,l)},"5g"),e.jsx("div",{style:{height:20,backgroundColor:"#ffff"}}),!b.includes("6G")&&e.jsx(Re.Panel,{header:e.jsx("h3",{style:{fontSize:"16px",margin:0,border:"none"},children:"6G Radio"}),children:Ft("6G",g,T,l)},"6g")]})})}})})}),Sr=(t,a=!1,s="2G")=>{const i=H().shape({band:N().required(t("form.required")).default(s),channel:N().required(t("form.required")).default("auto"),"channel-width":$().required(t("form.required")).integer().default(s==="2G"?20:40),"legacy-rates":q().default(s==="2G"?!0:void 0),"allow-dfs":q().default(s!=="2G"?!0:void 0),"beacon-interval":$().required(t("form.required")).moreThan(14).lessThan(65535).integer().default(100),"maximum-clients":$().nullable().positive().integer().default(64),"hostadp-iface-raw":Y().of(N()).default(void 0),rates:H().shape({beacon:$().positive().integer().default(void 0),multicast:$().positive().integer().default(void 0)}).default(void 0),he:H().shape({"multiple-bssid":q().default(void 0),ema:q().default(void 0),"bss-color":$().min(0).integer().default(0)}).default(void 0),"valid-channels":Y().of($()).min(1,t("form.required")).required(t("form.required"))});return a?i:i.nullable().default(void 0)},$a=({id:t,onSuccess:a=()=>{}})=>{const{t:s}=K(),i=pt(),n=Ps();return De(["get-configuration",t],()=>Fs.get(`configuration/${t}?withExtendedInfo=true`).then(({data:r})=>r),{enabled:t!=null&&t!=="",keepPreviousData:!0,staleTime:10*1e3,onSuccess:a,onError:r=>{var l,o;i.isActive("configuration-fetching-error")||i({id:"configuration-fetching-error",title:s("common.error"),description:s("crud.error_fetching_obj",{obj:s("configurations.one"),e:(o=(l=r==null?void 0:r.response)==null?void 0:l.data)==null?void 0:o.ErrorDescription}),status:"error",duration:5e3,isClosable:!0,position:"top-right"}),r.code==="404"&&n()}})},wr=[{value:"AL",label:"Albania"},{value:"DZ",label:"Algeria"},{value:"AS",label:"American Samoa"},{value:"AD",label:"Andorra"},{value:"AO",label:"Angola"},{value:"AI",label:"Anguilla"},{value:"AG",label:"Antigua And Barbuda"},{value:"AR",label:"Argentina"},{value:"AM",label:"Armenia"},{value:"AN",label:"Netherlands Antilles"},{value:"AW",label:"Aruba"},{value:"AU",label:"Australia"},{value:"AT",label:"Austria"},{value:"AZ",label:"Azerbaijan"},{value:"BS",label:"Bahamas"},{value:"BH",label:"Bahrain"},{value:"BD",label:"Bangladesh"},{value:"BB",label:"Barbados"},{value:"BY",label:"Belarus"},{value:"BE",label:"Belgium"},{value:"BZ",label:"Belize"},{value:"BJ",label:"Benin"},{value:"BM",label:"Bermuda"},{value:"BO",label:"Bolivia"},{value:"BA",label:"Bosnia And Herzegovina"},{value:"BR",label:"Brazil"},{value:"BN",label:"Brunei Darussalam"},{value:"BG",label:"Bulgaria"},{value:"KH",label:"Cambodia"},{value:"CM",label:"Cameroon"},{value:"CA",label:"Canada"},{value:"CV",label:"Cape Verde"},{value:"KY",label:"Cayman Islands"},{value:"CL",label:"Chile"},{value:"CN",label:"China"},{value:"CO",label:"Colombia"},{value:"CR",label:"Costa Rica"},{value:"CI",label:"Cote D'Ivoire"},{value:"HR",label:"Croatia"},{value:"CY",label:"Cyprus"},{value:"CZ",label:"Czech Republic"},{value:"DK",label:"Denmark"},{value:"DO",label:"Dominican Republic"},{value:"EC",label:"Ecuador"},{value:"EG",label:"Egypt"},{value:"SV",label:"El Salvador"},{value:"EE",label:"Estonia"},{value:"ET",label:"Ethiopia"},{value:"FO",label:"Faroe Islands"},{value:"FJ",label:"Fiji"},{value:"FI",label:"Finland"},{value:"FR",label:"France"},{value:"PF",label:"French Polynesia"},{value:"TF",label:"French Southern Territories"},{value:"GA",label:"Gabon"},{value:"GE",label:"Georgia"},{value:"DE",label:"Germany"},{value:"GH",label:"Ghana"},{value:"GI",label:"Gibraltar"},{value:"GR",label:"Greece"},{value:"GL",label:"Greenland"},{value:"GD",label:"Grenada"},{value:"GU",label:"Guam"},{value:"GT",label:"Guatemala"},{value:"GG",label:"Guernsey"},{value:"GY",label:"Guyana"},{value:"HT",label:"Haiti"},{value:"VA",label:"Holy See (Vatican City State)"},{value:"HN",label:"Honduras"},{value:"HK",label:"Hong Kong"},{value:"HU",label:"Hungary"},{value:"IS",label:"Iceland"},{value:"IN",label:"India"},{value:"ID",label:"Indonesia"},{value:"IQ",label:"Iraq"},{value:"IE",label:"Ireland"},{value:"IL",label:"Israel"},{value:"IT",label:"Italy"},{value:"JM",label:"Jamaica"},{value:"JP",label:"Japan"},{value:"JO",label:"Jordan"},{value:"KZ",label:"Kazakhstan"},{value:"KE",label:"Kenya"},{value:"KR",label:"Korea"},{value:"KW",label:"Kuwait"},{value:"LA",label:"Lao People's Democratic Republic"},{value:"LV",label:"Latvia"},{value:"LB",label:"Lebanon"},{value:"LY",label:"Libyan Arab Jamahiriya"},{value:"LI",label:"Liechtenstein"},{value:"LT",label:"Lithuania"},{value:"LU",label:"Luxembourg"},{value:"MO",label:"Macao"},{value:"MK",label:"Macedonia"},{value:"MY",label:"Malaysia"},{value:"MV",label:"Maldives"},{value:"ML",label:"Mali"},{value:"MT",label:"Malta"},{value:"MU",label:"Mauritius"},{value:"MX",label:"Mexico"},{value:"FM",label:"Micronesia, Federated States Of"},{value:"MD",label:"Moldova"},{value:"MC",label:"Monaco"},{value:"ME",label:"Montenegro"},{value:"MA",label:"Morocco"},{value:"MZ",label:"Mozambique"},{value:"MM",label:"Myanmar"},{value:"NA",label:"Namibia"},{value:"NP",label:"Nepal"},{value:"NL",label:"Netherlands"},{value:"AN",label:"Netherlands Antilles"},{value:"NZ",label:"New Zealand"},{value:"NI",label:"Nicaragua"},{value:"NG",label:"Nigeria"},{value:"MP",label:"Northern Mariana Islands"},{value:"NO",label:"Norway"},{value:"OM",label:"Oman"},{value:"PK",label:"Pakistan"},{value:"PW",label:"Palau"},{value:"PA",label:"Panama"},{value:"PG",label:"Papua New Guinea"},{value:"PY",label:"Paraguay"},{value:"PE",label:"Peru"},{value:"PH",label:"Philippines"},{value:"PL",label:"Poland"},{value:"PT",label:"Portugal"},{value:"PR",label:"Puerto Rico"},{value:"QA",label:"Qatar"},{value:"RE",label:"Reunion"},{value:"RO",label:"Romania"},{value:"RU",label:"Russian Federation"},{value:"RW",label:"Rwanda"},{value:"KN",label:"Saint Kitts And Nevis"},{value:"LC",label:"Saint Lucia"},{value:"MF",label:"Saint Martin"},{value:"SM",label:"San Marino"},{value:"SA",label:"Saudi Arabia"},{value:"RS",label:"Serbia"},{value:"SG",label:"Singapore"},{value:"SK",label:"Slovakia"},{value:"SI",label:"Slovenia"},{value:"ZA",label:"South Africa"},{value:"ES",label:"Spain"},{value:"LK",label:"Sri Lanka"},{value:"SZ",label:"Swaziland"},{value:"SE",label:"Sweden"},{value:"CH",label:"Switzerland"},{value:"TW",label:"Taiwan"},{value:"TZ",label:"Tanzania"},{value:"TH",label:"Thailand"},{value:"TG",label:"Togo"},{value:"TT",label:"Trinidad And Tobago"},{value:"TN",label:"Tunisia"},{value:"TR",label:"Turkey"},{value:"TC",label:"Turks And Caicos Islands"},{value:"UG",label:"Uganda"},{value:"UA",label:"Ukraine"},{value:"AE",label:"United Arab Emirates"},{value:"GB",label:"United Kingdom"},{value:"US",label:"United States"},{value:"UY",label:"Uruguay"},{value:"UZ",label:"Uzbekistan"},{value:"VE",label:"Venezuela"},{value:"VN",label:"Viet Nam"},{value:"VI",label:"Virgin Islands, U.S."},{value:"WF",label:"Wallis And Futuna"},{value:"YE",label:"Yemen"},{value:"ZW",label:"Zimbabwe"}],{Option:Tr}=Q,Ar=({value:t,onChange:a,formItemProps:s,style:i})=>{const[n,r]=d.useState(""),l=wr.filter(u=>u.label.toLowerCase().includes(n.toLowerCase())),o=u=>e.jsxs("div",{children:[e.jsx(ae,{value:n,onChange:c=>r(c.target.value),placeholder:"Search",prefix:e.jsx(Ms,{component:Ia}),allowClear:!0,style:{width:"100%",height:"32px",marginBottom:"3px"}}),u]});return e.jsx(Q,{value:t,onChange:a,dropdownRender:o,style:i,onDropdownVisibleChange:u=>{u&&r("")},children:l.map(u=>e.jsx(Tr,{value:u.value,children:u.label},u.value))})},kr={labelCol:{style:{width:160}},wrapperCol:{style:{width:300}}};function Ir(t){const a={};return(t.radios||[]).forEach(s=>{const i=s.band;i&&(a[i]=s)}),a}function Er(t,a){const[s,i]=d.useState([]),[n,r]=d.useState(!0);return d.useEffect(()=>{t!=null&&(r(!0),Ne(a,t,1,1e4).then(l=>(l==null?void 0:l.status)===200&&i(l.info||[])).catch(()=>D.error("Failed to fetch time range profiles")).finally(()=>r(!1)))},[t,a]),{profiles:s,setProfiles:i,loading:n}}function Pr(t){var o,u;const a=t!=null?`${t}-radio`:void 0,{data:s=[],refetch:i,isLoading:n}=$a({id:a});d.useEffect(()=>{t&&i()},[t,i]);const r=(u=(o=s==null?void 0:s.configuration)==null?void 0:o[0])==null?void 0:u.configuration;return{radioValues:d.useMemo(()=>{if(!r)return null;try{return Ir(JSON.parse(r))}catch{return null}},[r]),configId:a,isLoading:n,refetch:i}}const Fr=d.forwardRef(({onDirtyChange:t},a)=>{const[s]=A.useForm(),[i,n]=d.useState(!1),r=d.useRef(null),l=vt(),{t:o}=K(),u=4,c=["2G","5G","6G"],[b,x]=d.useState(!1),[g,y]=d.useState(!1),T=b||g;d.useEffect(()=>{t==null||t(T)},[T,t]),d.useImperativeHandle(a,()=>({reset:()=>ce(),apply:()=>B()}));const[f,p]=d.useState(null);d.useEffect(()=>{const W=location.hash.replace("#",""),V=Number(W);p(V)},[location.hash]),d.useEffect(()=>{x(!1),y(!1)},[f]);const{profiles:m,setProfiles:v,loading:h}=Er(f,u),{radioValues:k,configId:P,isLoading:S,refetch:j}=Pr(f),C=f==null||h||S,E=d.useMemo(()=>{const W=(k==null?void 0:k["2G"])||{};return{country:W.country||"US","radio-schedule-enable":W["radio-schedule-enable"]===1,"radio-schedule-radio-mode":W["radio-schedule-radio-mode"]??0,"time-range-index":W["time-range-index"]?String(Ge(W["time-range-index"])):void 0,"time-range-name":W["time-range-name"]||void 0}},[k]),w=d.useMemo(()=>{if(!k)return null;const W=JSON.parse(JSON.stringify(k));return Object.entries(W).forEach(([V,re])=>{delete re.country,delete re["radio-schedule-enable"],delete re["radio-schedule-radio-mode"],delete re["time-range-name"],delete re["time-range-index"]}),W},[k]),L=d.useRef(E),O=d.useRef(w);d.useEffect(()=>{C||(s.setFieldsValue(E),L.current=E,x(!1),w&&(O.current=w,y(!1)))},[C,E,w]);const I=A.useWatch("country",s),[U,z]=d.useState(E.country);d.useEffect(()=>{I&&z(I)},[I]);const B=()=>{s.validateFields().then(()=>{var W;(W=r.current)==null||W.submit()}).catch(()=>{D.error("Please check your input")})},R=(W,V)=>{const re=V["radio-schedule-enable"],ge={"radio-schedule-enable":re?1:0,country:V.country};return re&&(V["time-range-index"]&&(ge["time-range-index"]=He(V["time-range-index"])),V["time-range-name"]&&(ge["time-range-name"]=V["time-range-name"]),typeof V["radio-schedule-radio-mode"]<"u"&&(ge["radio-schedule-radio-mode"]=V["radio-schedule-radio-mode"])),{radios:c.map(Te=>{const Ae=W[Te];if(!Ae||Ae.enabled===!1)return null;const Oe={...Ae};return re||(delete Oe["radio-schedule-radio-mode"],delete Oe["time-range-name"],delete Oe["time-range-index"]),{...Oe,band:Te,...ge}}).filter(Boolean)}},se=async W=>{try{for(const Te of Object.keys(W)){const Ae=W[Te];Ae&&await Sr(o).validate(Ae,{abortEarly:!1})}}catch{D.error("Please check your input");return}const V=s.getFieldsValue(),re=R(W,V),ge=[{name:"Radio",description:"Radio configuration",weight:0,configuration:JSON.stringify(re)}];try{await Ha(P,f,JSON.stringify(ge)),x(!1),y(!1),D.success("Successfully applied radio configuration")}catch{D.error("Failed to apply radio configuration");return}(await j()).error&&D.warning("Radio configuration saved, but refresh failed")},ce=async()=>{var V;(await j()).error?D.error("Failed to reset changes"):(s.resetFields(),(V=r.current)==null||V.resetForm(),x(!1),y(!1),D.info("Changes have been reset."))},pe=A.useWatch("radio-schedule-enable",s);return e.jsx(Ea,{spinning:C,children:e.jsxs("div",{style:{minWidth:1050},children:[e.jsxs(A,{form:s,layout:"horizontal",initialValues:E,labelAlign:"left",...kr,onValuesChange:()=>x(!0),children:[e.jsx(_,{gutter:16,align:"middle",children:e.jsx(F,{span:10,style:{marginLeft:12},children:e.jsx(A.Item,{label:"Country",tooltip:J("radio.country"),required:!0,name:"country",noStyle:!1,children:e.jsx(Ar,{value:s.getFieldValue("country"),onChange:W=>s.setFieldsValue({country:W}),style:{width:280}})})})}),e.jsx(_,{gutter:16,align:"middle",children:e.jsx(F,{span:10,style:{marginLeft:12},children:e.jsx(A.Item,{label:"Radio Scheduled",name:"radio-schedule-enable",valuePropName:"checked",noStyle:!1,children:e.jsx(te,{onChange:W=>{s.setFieldsValue({"radio-schedule-enable":W,"time-range-index":void 0,"time-range-name":void 0}),W&&f!=null&&Ne(u,f,1,1e4).then(V=>(V==null?void 0:V.status)===200&&v(V.info||[])).catch(()=>D.error("Failed to reload time range profiles"))}})})})}),pe&&e.jsxs(e.Fragment,{children:[e.jsx(_,{gutter:16,children:e.jsx(F,{span:10,style:{marginLeft:12},children:e.jsx(A.Item,{label:"Radio Mode",name:"radio-schedule-radio-mode",noStyle:!1,children:e.jsxs(Ce.Group,{children:[e.jsx(Ce,{value:1,children:"Radio On"}),e.jsx(Ce,{value:0,children:"Radio Off"})]})})})}),e.jsx(A.Item,{name:"time-range-name",noStyle:!0,children:e.jsx(ae,{type:"hidden"})}),e.jsx(_,{gutter:16,children:e.jsx(F,{span:10,style:{marginLeft:12},children:e.jsxs("div",{style:{display:"flex",alignItems:"flex-start",gap:162},children:[e.jsx("div",{style:{width:280},children:e.jsx(A.Item,{label:"Time Range",name:"time-range-index",rules:[{required:!0,message:"Required!"}],validateTrigger:"onBlur",noStyle:!1,children:e.jsx(Q,{placeholder:"Select a Time Range Profile",value:s.getFieldValue("time-range-index"),style:{width:280},onChange:W=>{if(W==="custom"){s.setFieldsValue({"time-range-index":null}),n(!0);return}const V=m.find(re=>String(re.variable_id)===String(W));V&&s.setFieldsValue({"time-range-index":String(V.variable_id),"time-range-name":V.name})},dropdownRender:W=>e.jsxs("div",{children:[W,e.jsx(Z,{type:"link",icon:e.jsx(Ze,{}),onClick:()=>n(!0),style:{margin:"4px 0px 0px -24px",width:"calc(100% + 48px)",borderTop:"1px solid #E7E7E7"},children:"Create Time Range Profile"})]}),children:m.map(W=>e.jsx(Q.Option,{value:W.variable_id,children:W.name},W.id))})})}),e.jsx(Z,{type:"link",style:{padding:4},onClick:()=>l("/wireless/profile/TimeRange"),children:"Manage Time Range Profile"})]})})})]}),!C&&w&&e.jsx(Cr,{ref:r,initialValues:w,onApply:se,countryCode:U,onChange:W=>{!Rs(W,O.current)&&y(!0)}})]}),e.jsx(_a,{siteId:f,visible:i,dataSource:null,onClose:()=>n(!1),onSuccess:()=>{n(!1),f!=null&&Ne(u,f,1,1e4).then(W=>{if((W==null?void 0:W.status)===200){const V=W.info||[];if(v(V),V.length>0){const re=V[V.length-1];s.setFieldsValue({"time-range-index":String(re.variable_id),"time-range-name":re.name})}}}).catch(()=>{D.error("Failed to load time range profiles")})}})]})})}),_t=t=>{const a=/^(([0-9])|([1-9][0-9])|(1([0-9]{2}))|(2[0-4][0-9])|(25[0-5]))((\.(([0-9])|([1-9][0-9])|(1([0-9]{2}))|(2[0-4][0-9])|(25[0-5]))){3})(\/(([0-9])|([12][0-9])|(3[0-2])))?$/gi;return t?a.test(t):!0},Mr=t=>{const a=/^(?!:\/\/)(?=.{1,255}$)((.{1,63}\.){1,127}(?![0-9]*$)[a-z0-9-]+\.?)$/;return t!==void 0?t.length===0?!0:a.test(t):!1},Rr=({val:t,min:a,max:s})=>{if(t){const{length:i}=t;return!(i<a||i>s)}return!1},Ka=t=>t?t.match("^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$"):!1,_r=["m","h","d"],Ur=t=>{let a=!0;try{const s=t.match(/\D+|\d+/g);if(s&&s.length>0&&s.length<=6&&s.length%2===0)for(let i=0;i<s.length;i+=1)if(i%2===0){const n=s[i]??"";if(n.length===0||n==="0"){a=!1;break}}else{const n=s[i]??"";if(!_r.includes(n)){a=!1;break}}else a=!1}catch{a=!1}return a},ua=t=>{if(typeof t!="string")return!1;if(!t)return!0;const a=t.split(".")[0];if(a)try{const s=Number(a);if(s>=1&&(s<=223||s>239))return!0}catch{return!1}return!1},Dr=t=>{if(typeof t!="string")return!1;if(!t)return!0;const a=t.split(".")[0];if(a)try{const s=Number(a);if(s>=1&&s<=223&&s<=239)return!0}catch{return!1}return!1},Or=["psk","psk2","psk-mixed","psk2-radius","sae","sae-mixed"],Wa=[{value:"none",label:"None"},{value:"psk",label:"WPA-PSK"},{value:"psk2",label:"WPA2-PSK"},{value:"psk2-radius",label:"PSK2-RADIUS"},{value:"psk-mixed",label:"WPA-PSK/WPA2-PSK Personal Mixed"},{value:"wpa",label:"WPA-Enterprise"},{value:"wpa2",label:"WPA2-Enterprise EAP-TLS"},{value:"wpa-mixed",label:"WPA-Enterprise-Mixed"},{value:"sae",label:"WPA3-SAE"},{value:"sae-mixed",label:"WPA2/WPA3 Transitional"},{value:"wpa3",label:"WPA3-Enterprise EAP-TLS"},{value:"wpa3-192",label:"WPA3-192-Enterprise EAP-TLS"},{value:"wpa3-mixed",label:"WPA3-Enterprise-Mixed"},{value:"owe",label:"OWE"},{value:"owe-transition",label:"OWE-Transition"}],Br=["owe-transition"],Nr=(t,a=!1)=>{const s=H().shape({"venue-name":Y().of(N()).default(void 0),"venue-url":Y().of(N()).default(void 0),"venue-group":$().moreThan(-1).lessThan(33).integer().default(1),"venue-type":$().moreThan(-1).lessThan(33).integer().default(1),"auth-type":H().shape({type:N().default("terms-and-condition"),url:N().default(void 0)}).default({type:"terms-and-condition",url:void 0}),"domain-name":Y().of(N()).default(void 0),"nai-realm":Y().of(N()).default(void 0),osen:q().default(void 0),"anqp-domain":$().moreThan(-1).lessThan(65535).integer().default(8888),"anqp-3gpp-cell-net":Y().of(N()).default(void 0),"friendly-name":Y().of(N()).default(void 0),"access-network-type":$().moreThan(-1).lessThan(15).integer().default(0),internet:q().default(!0),asra:q().default(void 0),esr:q().default(void 0),uesa:q().default(void 0),hessid:N().default(void 0),"roaming-consortium":Y().of(N()).default(void 0),"disable-dgaf":q().default(void 0),"ipaddr-type-available":$().moreThan(-1).lessThan(256).integer().default(void 0),"connection-capability":Y().of(N()).default(void 0),icons:Y().of(H()).default(void 0),"wan-metrics":H().shape({type:N().default("up"),downlink:$().moreThan(-1).integer().default(2e4),uplink:$().moreThan(-1).integer().default(2e4)}).default({type:"terms-and-condition",url:void 0})}).default({"venue-name":void 0,"venue-url":void 0,"venue-group":1,"venue-type":1,"auth-type":{type:"terms-and-condition",url:void 0},"domain-name":void 0,"nai-realm":void 0,osen:void 0,"anqp-domain":8888,"anqp-3gpp-cell-net":void 0,"friendly-name":void 0,"access-network-type":0,internet:!0,asra:void 0,esr:void 0,uesa:void 0,hessid:void 0,"roaming-consortium":void 0,"disable-dgaf":void 0,"ipaddr-type-available":void 0,"connection-capability":void 0,icons:void 0,"wan-metrics":{type:"up",downlink:2e4,uplink:2e4}});return a?s:s.nullable().default(void 0)},Lr=(t,a=!1)=>{const s=H().shape({"ingress-rate":$().required(t("form.required")).moreThan(-1).lessThan(65535).integer().default(0),"egress-rate":$().required(t("form.required")).moreThan(-1).lessThan(65535).integer().default(0)}).default({"ingress-rate":0,"egress-rate":0});return a?s:s.nullable().default(void 0)},qr=(t,a=!1)=>{const s=H().shape({proto:N().required(t("form.required")).oneOf(Wa.map(({value:i})=>i)).test("encryption-6g-test",t("form.invalid_proto_6g"),(i,{from:n})=>{const r=n[1].value["wifi-bands"];return!(r&&r.includes("6G")&&Br.includes(n[0].value.proto))}).test("encryption-passpoint-proto",t("form.invalid_proto_passpoint"),(i,{from:n})=>!0).default("psk"),ieee80211w:N().test("encryptionIeeeTest",t("form.invalid_ieee"),(i,{from:n})=>{const{proto:r}=n[0].value;return!((r==="owe"||r==="owe-transition")&&i==="disabled")}).test("encryptionRequiredIeee",t("form.invalid_ieee_required"),(i,{from:n})=>{const{proto:r}=n[0].value;return!((r==="wpa3"||r==="wpa3-192"||r==="wpa3-mixed"||r==="sae")&&i!=="required")}).default("disabled"),key:N().test("encryptionKeyTest",t("form.min_max_string",{min:8,max:63}),(i,{from:n})=>!Or.includes(n[0].value.proto)||n[1].value.radius!==void 0?!0:i.length>=8&&i.length<=63).default(""),"key-caching":q().default(!0)}).default({proto:"psk2",ieee80211w:"required",key:"YOUR_SECRET"});return a?s:s.nullable().default(void 0)},zr=t=>Ln(s=>typeof s=="object"?H().shape({"message-exchange":N().required(t("form.required")).default("ds"),"generate-psk":q().required(t("form.required")).default(!1),"domain-identifier":N().length(4).default(void 0),"pmk-r0-key-holder":N().default(void 0),"pmk-r1-key-holder":N().default(void 0)}).nullable().default(void 0):q().default(void 0)),Vr=(t,a=!1)=>{const s=H().shape({mode:N().required(t("form.required")).default("allow"),"mac-address":Y().of(N().test("ssid.access-control-list.mac",t("form.invalid_mac_uc"),Ka)).required(t("form.required")).min(1,t("form.required")).default([])}).default({mode:"allow","mac-address":[]});return a?s:s.nullable().default(void 0)},Qr=(t,a=!1)=>{const s=H().shape({"neighbor-reporting":q().default(!0),lci:N().default(""),"civic-location":N().default(""),"ftm-responder":q().default(!1),"stationary-ap":q().default(!0)}).default({"neighbor-reporting":!0,"ftm-responder":!1,"stationary-ap":!0});return a?s:s.nullable().default(void 0)},Gr=(t,a=!1)=>{const s=H().shape({"auth-mode":N().required(t("form.required")).default("off"),"walled-garden-fqdn":Y().default(void 0),"walled-garden-ipaddr":Y().of(N()).default(void 0),"web-root":N().default(void 0),"idle-timeout":$().required(t("form.required")).positive().lessThan(65535).integer().default(600),"session-timeout":$().positive().lessThan(65535).integer().default(void 0),credentials:Y().default(void 0),"auth-server":N().required(t("form.required")).default("************"),"auth-secret":N().required(t("form.required")).default("secret"),"auth-port":$().required(t("form.required")).moreThan(1023).lessThan(65535).integer().default(1812),"acct-server":N().default(void 0),"acct-secret":N().default(void 0),"acct-port":$().moreThan(1023).lessThan(65535).integer().default(void 0),"acct-interval":$().positive().lessThan(65535).integer().default(void 0),"uam-server":N().required(t("form.required")).default("https://YOUR-LOGIN-ADDRESS.YOURS"),"uam-secret":N().required(t("form.required")).default("secret"),"uam-port":$().required(t("form.required")).moreThan(1023).lessThan(65535).integer().default(3990),ssid:N().default(void 0),"mac-format":N().required(t("form.required")).default("aabbccddeeff"),nasid:N().required(t("form.required")).default("TestLab"),nasmac:N().default(void 0)}).default({});return a?s:s.nullable().default(void 0)},Hr=(t,a=!1)=>{const s=H().shape({name:N().test("ssid-name-test",t("form.min_max_string",{min:1,max:32}),(i,{from:n})=>{var r,l;return(l=(r=n[0])==null?void 0:r.value)!=null&&l.__variableBlock?!0:i.length>=1&&i.length<=32}).default(""),purpose:N().default(void 0),"ssid-schedule-enable":$().integer().default(0),"wifi-bands":Y().of(N()).required(t("form.required")).min(1,t("form.required")).test("ssid-bands-test",t("configurations.wifi_bands_max"),(i,{from:n})=>{var l,o;const r={};for(const u of((o=(l=n[2])==null?void 0:l.value)==null?void 0:o.configuration)??[])for(const c of u.ssids??[])for(const b of c["wifi-bands"]??[])if(r[b]||(r[b]=0),r[b]+=1,r[b]>16&&i.includes(b))return!1;return!0}).default(["2G","5G"]),"bss-mode":N().required(t("form.required")).default("ap"),"hidden-ssid":q().required(t("form.required")).default(!1),"isolate-clients":q().required(t("form.required")).default(!1),"power-save":q().default(void 0),"broadcast-time":q().default(void 0),"unicast-conversion":q().default(!1),services:Y().of(N()).default([]),"maximum-clients":$().required(t("form.required")).moreThan(0).lessThan(65535).integer().default(64),"proxy-arp":q().default(void 0),"disassoc-low-ack":q().default(void 0),"fils-discovery-interval":$().integer().moreThan(0).lessThan(21).default(20),"vendor-elements":N(),encryption:qr(t,a),"rate-limit":Lr(t,a),rrm:Qr(t,a),captive:Gr(t,a),"access-control-list":Vr(t,a),roaming:zr(t),"pass-point":Nr(),"dtim-period":$().moreThan(0).lessThan(256).integer().default(2),"tip-information-element":q().default(!0)});return a?s:s.nullable().default(void 0)},ha=["psk","psk2","psk2-radius","psk-mixed","wpa","wpa2","wpa-mixed","sae","sae-mixed","wpa3","wpa3-192","wpa3-mixed","owe","owe-transition"],fa=["psk2-radius","wpa","wpa2","wpa-mixed","wpa3","wpa3-192","wpa3-mixed"],$r={1:{modalComponent:Ua,navigateUrl:"/wireless/profile/SSIDRadius"}},Kr=({label:t,formName:a,type:s,siteId:i,edit:n,title:r,onProfileChange:l,proto:o})=>{const{t:u}=K(),[c,b]=d.useState(!1),[x,g]=d.useState([]),y=A.useFormInstance(),T=vt(),[f,p]=d.useState(!1),m=$r[s],v=m.modalComponent,h=async(j=!1)=>{try{const C=[];o==="psk2-radius"&&C.push({field:"type",value:"External"});const E=await Ne(s,i,1,1e3,[],[{field:"id",order:"asc"}],C);if((E==null?void 0:E.status)===200){if(g(E.info||[]),j)if(n)p(!0);else{const w=E.info[E.info.length-1];w&&S(w.variable_id)}}else D.error((E==null?void 0:E.info)||"fetch profile list fail")}catch(C){C instanceof Error?D.error(C.message):D.error("An unknown error occurred")}};d.useEffect(()=>{if(n&&x.length!==0)if(f){const j=x[x.length-1];S(j.variable_id),p(!1)}else{const j=x.find(C=>C.variable_id===n);j&&y.setFieldValue(a,n),l&&l(j)}},[n,x,s]);const k=()=>{b(!0)},P=j=>{b(!1),h(j)},S=j=>{y.setFieldValue(a,j);const C=x.find(E=>E.variable_id===j);l&&l(C)};return d.useEffect(()=>{h(),y.setFieldValue(a,void 0),l&&l(void 0)},[i,o]),e.jsxs(e.Fragment,{children:[e.jsxs(Ue,{children:[e.jsx(A.Item,{name:a,label:t,rules:[{required:!0,message:u("form.required")}],children:e.jsx(Q,{dropdownRender:j=>e.jsxs(e.Fragment,{children:[j,e.jsxs(Z,{type:"link",icon:e.jsx(Ze,{}),onClick:k,style:{width:"100%",borderTop:"1px solid #E7E7E7"},children:["Create ",r||t," Profile"]})]}),onChange:S,children:x.map(j=>e.jsx(Q.Option,{value:j.variable_id,children:j.name},j.variable_id))})}),e.jsxs(Z,{type:"text",style:{backgroundColor:"#fff"},onClick:()=>T(m.navigateUrl+"#"+i),children:["Manage ",r||t," Profile"]})]}),e.jsx(v,{onClose:P,siteId:i,...o==="psk2-radius"?{disableMode:!0}:{},...s!==4?{open:c}:{visible:c}},Date.now())]})},Wr={1:{modalComponent:Ua,navigateUrl:"/wireless/profile/SSIDRadius"},2:{modalComponent:Js,navigateUrl:"/wireless/profile/MPSKUser"},3:{modalComponent:Ws,navigateUrl:"/wireless/profile/CaptivePortal"},4:{modalComponent:_a,navigateUrl:"/wireless/profile/TimeRange"}},$t=({label:t,formName:a,switchEnabled:s,onSwitchChange:i,type:n,siteId:r,edit:l,mode:o,title:u,onProfileChange:c,enableName:b,selectName:x})=>{const{t:g}=K(),[y,T]=d.useState(!1),[f,p]=d.useState([]),m=A.useFormInstance(),v=vt(),[h,k]=d.useState(void 0),[P,S]=d.useState(!1),j=Wr[n],C=j.modalComponent,E=async(z=!1)=>{try{let B,R;if(o?(o==="radius"?R="Radius":o==="credentials"?R="Credentials":R="Click",k(R),B=await Ne(n,r,1,1e3,[],[{field:"id",order:"asc"}],[{field:"mode",value:R}])):(k(void 0),B=await Ne(n,r,1,1e3,[],[{field:"id",order:"asc"}])),(B==null?void 0:B.status)===200){if(p(B.info||[]),z)if(l)S(!0);else{const se=B.info[B.info.length-1];se&&U(se.variable_id)}}else D.error((B==null?void 0:B.info)||"fetch profile list fail")}catch(B){B instanceof Error?D.error(B.message):D.error("An unknown error occurred")}};d.useEffect(()=>{s&&(E(),m.setFieldValue(a,void 0),c&&c(void 0))},[s,o,r]),d.useEffect(()=>{if(l&&f.length!==0)if(s||i(!0),P){const z=f[f.length-1];U(z.variable_id),S(!1)}else{const z=f.find(B=>B.variable_id===l);z&&m.setFieldValue(a,l),c&&c(z)}},[l,f,n]);const w=()=>{T(!0)},L=()=>{T(!1)},O=z=>{T(!1),E(z)},I=z=>{i(z),!z&&m&&(m.setFieldValue(a,void 0),x&&m.setFieldValue(x,void 0))},U=z=>{m.setFieldValue(a,z);const B=f.find(R=>R.variable_id===z);x&&m.setFieldValue(x,B?B.name:z),c&&c(B)};return e.jsxs(e.Fragment,{children:[e.jsx(A.Item,{label:t,style:{marginBottom:10},name:b||void 0,valuePropName:b?void 0:"checked",getValueFromEvent:b?z=>z?1:0:void 0,children:e.jsx(te,{checked:s,onChange:I})}),x&&e.jsx(A.Item,{name:x,style:{display:"none"},children:e.jsx(ae,{type:"hidden"})}),s&&e.jsxs(Ue,{children:[e.jsx(A.Item,{name:a,label:" ",required:!1,rules:[{required:!0,message:g("form.required")}],children:e.jsx(Q,{dropdownRender:z=>e.jsxs(e.Fragment,{children:[z,e.jsxs(Z,{type:"link",icon:e.jsx(Ze,{}),onClick:w,style:{margin:"4px 0px 0px -24px",width:"calc(100% + 48px)",borderTop:"1px solid #E7E7E7"},children:["Create ",u||t," Profile"]})]}),onChange:U,children:f.map(z=>e.jsx(Q.Option,{value:z.variable_id,children:z.name},z.variable_id))})}),e.jsxs(Z,{type:"text",style:{backgroundColor:"#fff"},onClick:()=>v(j.navigateUrl+"#"+r),children:["Manage ",u||t," Profile"]})]}),n!==2?e.jsx(C,{onClose:O,siteId:r,...n!==4?{open:y}:{visible:y},...h?{parameterMode:h}:{}},Date.now()):e.jsx(Ie,{title:e.jsxs("div",{children:[`Create ${u||t} Profile`,e.jsx(de,{style:{marginTop:8,marginBottom:0}})]}),open:y,onCancel:L,footer:null,destroyOnClose:!0,className:"ampcon-max-modal",children:C&&e.jsx(C,{onClose:O,siteId:r,...h?{parameterMode:h}:{}},Date.now())})]})},rt={Enterprise:[{value:"wpa",label:"WPA-Enterprise"},{value:"wpa2",label:"WPA2-Enterprise EAP-TLS"},{value:"wpa-mixed",label:"WPA-Enterprise-Mixed"},{value:"wpa3",label:"WPA3-Enterprise EAP-TLS"},{value:"wpa3-192",label:"WPA3-192-Enterprise EAP-TLS"},{value:"wpa3-mixed",label:"WPA3-Enterprise-Mixed"}],Personal:[{value:"psk",label:"WPA-PSK"},{value:"psk2",label:"WPA2-PSK"},{value:"psk2-radius",label:"PSK2-RADIUS"},{value:"psk-mixed",label:"WPA-PSK/WPA2-PSK Personal Mixed"},{value:"sae",label:"WPA3-SAE"},{value:"sae-mixed",label:"WPA2/WPA3 Transitional"}],Open:[{value:"none",label:"None"},{value:"owe",label:"OWE"},{value:"owe-transition",label:"OWE-Transition"}]},ma={Enterprise:[{value:"wpa3",label:"WPA3-Enterprise EAP-TLS"},{value:"wpa3-192",label:"WPA3-192-Enterprise EAP-TLS"},{value:"wpa3-mixed",label:"WPA3-Enterprise-Mixed"}],Personal:[{value:"sae",label:"WPA3-SAE"},{value:"sae-mixed",label:"WPA2/WPA3 Transitional"}],Open:[{value:"owe",label:"OWE"}]},Jr=["psk","psk2","psk-mixed"],pa=["psk","psk2","psk-mixed","sae","sae-mixed"],Xr=({resource:t,siteId:a})=>{var P;const{t:s}=K(),[i,n]=d.useState(!!(t!=null&&t["multi-psk"])),r=A.useFormInstance(),l=A.useWatch("wifi-bands",r),o=((P=t==null?void 0:t.encryption)==null?void 0:P.proto)||"psk2";function u(S){for(const j of Object.keys(rt))if(rt[j].some(C=>C.value===S))return j;return"Personal"}const c=A.useWatch(["encryption","proto"],r)||o,[b,x]=d.useState(u(c)),[g,y]=d.useState(pa.includes(c)),[T,f]=d.useState(ha.includes(c)),[p,m]=d.useState(fa.includes(c)),v=G.useRef(!0),h=G.useRef(!1);G.useEffect(()=>{var w;if(v.current){v.current=!1;return}l&&r.validateFields([["encryption","proto"]]);const C=(Array.isArray(l)&&l.includes("6G")?ma:rt)[b],E=(w=C==null?void 0:C[0])==null?void 0:w.value;E&&(Array.isArray(C)&&C.some(O=>O.value===c)||(h.current=!0,r.setFieldsValue({encryption:{...r.getFieldValue("encryption"),proto:E}}),setTimeout(()=>{h.current=!1},0)))},[b,l]),G.useEffect(()=>{y(pa.includes(c)),f(ha.includes(c)),m(fa.includes(c));const S=u(c);S!==b&&x(S);const j=r.getFieldValue("encryption")||{};r.setFieldsValue({encryption:{...j,ieee80211w:"required"}})},[c]);function k(S,j){let C=S.replace("#","");C.length===8&&(C=C.slice(0,6)),C.length===3&&(C=C.split("").map(I=>I+I).join(""));const E=parseInt(C,16),w=E>>16&255,L=E>>8&255,O=E&255;return`rgba(${w},${L},${O},${j})`}return e.jsxs(e.Fragment,{children:[e.jsx(_,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(A.Item,{label:"Security Level",children:e.jsx("div",{style:{width:"100%",display:"flex"},children:e.jsx("div",{style:{display:"flex",gap:2},children:[{key:"Enterprise",color:"#2BC174FF"},{key:"Personal",color:"#F8961EFF"},{key:"Open",color:"#F53F3FFF"}].map((S,j,C)=>e.jsxs("div",{onClick:()=>x(S.key),style:{cursor:"pointer",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},children:[e.jsx("div",{style:{width:90,height:8,background:S.color,borderTopLeftRadius:j===0?24:0,borderBottomLeftRadius:j===0?24:0,borderTopRightRadius:j===C.length-1?24:0,borderBottomRightRadius:j===C.length-1?24:0,position:"relative",display:"flex",alignItems:"center",justifyContent:"center",marginBottom:4},children:b===S.key&&e.jsx("span",{style:{position:"absolute",right:"40%",top:"50%",transform:"translateY(-50%)",width:12,height:12,borderRadius:"50%",border:"3px solid #fff",background:S.color,boxShadow:`0px 0px 6px 0px ${k(S.color,.3)}`,display:"inline-block"}})}),e.jsx("div",{style:{color:"#333",fontSize:12,fontWeight:500,marginTop:2},children:S.key})]},S.key))})})})})}),e.jsx(_,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["encryption","proto"],label:"Protocol",tooltip:J("interface.ssid.encryption.proto"),rules:[{required:!0,message:s("form.required")},{validator:(S,j)=>{const C=r.getFieldValue("wifi-bands");return j==="owe-transition"&&C&&C.includes("6G")?Promise.reject(new Error(s("form.invalid_proto_6g"))):Promise.resolve()}}],children:e.jsx(Q,{children:((Array.isArray(l)&&l.includes("6G")?ma:rt)[b]||[]).map(S=>e.jsx(Q.Option,{value:S.value,children:S.label},S.value))})})})}),e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:T&&e.jsx(A.Item,{name:["encryption","ieee80211w"],label:"IEEE80211W",tooltip:J("interface.ssid.encryption.ieee80211w"),rules:[{required:!0,message:s("form.required")},{validator:(S,j)=>(c==="owe"||c==="owe-transition")&&j==="disabled"?Promise.reject(new Error(s("form.invalid_ieee"))):Promise.resolve()},{validator:(S,j)=>(c==="wpa3"||c==="wpa3-192"||c==="wpa3-mixed"||c==="sae")&&j!=="required"?Promise.reject(new Error(s("form.invalid_ieee_required"))):Promise.resolve()}],children:e.jsxs(Q,{placeholder:"ieee80211w",children:[e.jsx(Q.Option,{value:"disabled",children:"Disabled"}),e.jsx(Q.Option,{value:"optional",children:"Optional"}),e.jsx(Q.Option,{value:"required",children:"Required"})]})})}),e.jsx(F,{span:12,children:g&&e.jsx(A.Item,{name:["encryption","key"],label:"Key",tooltip:J("interface.ssid.encryption.key"),rules:[{required:!0,message:s("form.min_max_string",{min:8,max:63})},{min:8,max:63,message:s("form.min_max_string",{min:8,max:63})}],children:e.jsx(ae.Password,{placeholder:"key"})})})]}),p&&e.jsx(_,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx(Kr,{label:"Radius",formName:["radius"],type:1,siteId:a,proto:c,edit:t==null?void 0:t.radius})})}),Jr.includes(c)&&e.jsx(_,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx($t,{label:"MPSK",formName:["multi-psk"],switchEnabled:i,onSwitchChange:n,type:2,siteId:a,edit:t==null?void 0:t["multi-psk"]})})})]})},ga={username:"",password:""},Zr=({value:t=[],onChange:a})=>{const{t:s}=K(),[i,n]=d.useState(t),[r,l]=d.useState(!1),[o]=A.useForm(),[u,c]=d.useState(1),[b,x]=d.useState(10),g=d.useCallback(p=>{c(p.current),x(p.pageSize)},[]),y=()=>{l(!0),o.setFieldsValue(ga)},T=p=>{const m=i.filter(v=>v.username!==p.username||v.password!==p.password);n(m),a&&a(m)},f=[{title:"Username",dataIndex:"username",key:"username"},{title:"Password",dataIndex:"password",key:"password"},{title:"Operation",key:"action",render:(p,m)=>e.jsx(Z,{type:"text",onClick:()=>T(m),children:"Delete"})}];return e.jsxs("div",{children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:24},children:e.jsx(Z,{type:"primary",icon:e.jsx(Pe,{component:yt}),onClick:y,children:"Credentials"})}),e.jsx(Ye,{columns:f,dataSource:i,pagination:{current:u,pageSize:b,showTotal:p=>`Total ${p} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["5","10","20","50"],onChange:g},rowKey:(p,m)=>typeof m=="number"?m.toString():"",size:"middle",bordered:!0}),e.jsx(Qt,{open:r,title:"Add Credentials",onCancel:()=>l(!1),onFinish:p=>{const m=[p,...i];n(m),a&&a(m),l(!1),o.resetFields()},initialValues:ga,form:o,modalClass:"ampcon-middle-modal",children:e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:24,children:e.jsx(A.Item,{label:"Username",name:"username",rules:[{required:!0,message:"Username is required"},{validator:(p,m)=>m&&!/^[a-zA-Z][a-zA-Z0-9]*$/.test(m)?Promise.reject(new Error("Username must start with a letter and contain only letters and numbers")):Promise.resolve()},{validator:(p,m)=>m&&i.some(v=>v.username===m)?Promise.reject(new Error("Username already exists")):Promise.resolve()}],children:e.jsx(ae,{})})}),e.jsx(F,{span:24,children:e.jsx(A.Item,{label:"Password",name:"password",rules:[{required:!0,message:s("form.required")}],children:e.jsx(ae.Password,{})})})]})})]})},Yr=[{value:"aabbccddeeff",label:"aabbccddeeff"},{value:"aa-bb-cc-dd-ee-ff",label:"aa-bb-cc-dd-ee-ff"},{value:"aa:bb:cc:dd:ee:ff",label:"aa:bb:cc:dd:ee:ff"},{value:"AABBCCDDEEFF",label:"AABBCCDDEEFF"},{value:"AA:BB:CC:DD:EE:FF",label:"AA:BB:CC:DD:EE:FF"},{value:"AA-BB-CC-DD-EE-FF",label:"AA-BB-CC-DD-EE-FF"}],el={"walled-garden-fqdn":void 0,"walled-garden-ipaddr":void 0,"web-root":void 0,"idle-timeout":600,"session-timeout":void 0,credentials:void 0,"auth-server":"************","auth-secret":"secret","auth-port":1812,"acct-server":void 0,"acct-secret":void 0,"acct-port":1813,"acct-interval":60,"uam-server":"https://YOUR-LOGIN-ADDRESS.YOURS","uam-secret":"secret","uam-port":3990,ssid:void 0,"mac-format":"aabbccddeeff",nasid:"TestLab",nasmac:void 0},tl=({resource:t,siteId:a,onAuthModeChange:s})=>{var g,y,T;const{t:i}=K(),n=A.useFormInstance(),r=d.useRef(!0),[l,o]=d.useState(((g=t==null?void 0:t.captive)==null?void 0:g["auth-mode"])||"off"),[u,c]=d.useState(!!((y=t==null?void 0:t.captive)!=null&&y["web-root"])),[b,x]=d.useState(((T=t==null?void 0:t.captive)==null?void 0:T["web-root"])||void 0);return d.useEffect(()=>{s&&s(l),!(r.current&&(r.current=!1,t))&&(n.setFieldsValue({captive:el}),x(void 0),c(!1))},[l]),e.jsxs(e.Fragment,{children:[e.jsx("h3",{className:"header2",children:"Captive Portal"}),e.jsx(_,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","auth-mode"],label:"Auth Mode",rules:[{required:!0,message:i("form.required")}],children:e.jsxs(Q,{value:l,onChange:f=>o(f),children:[e.jsx(Q.Option,{value:"off",children:"Off"}),e.jsx(Q.Option,{value:"click-to-continue",children:"Click"}),e.jsx(Q.Option,{value:"radius",children:"Radius"}),e.jsx(Q.Option,{value:"credentials",children:"Credentials"}),e.jsx(Q.Option,{value:"uam",children:"UAM"})]})})})}),l!=="off"&&e.jsxs(e.Fragment,{children:[e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","walled-garden-fqdn"],label:"Walled Garden FQDN",children:e.jsx(Q,{mode:"tags",tokenSeparators:[","]})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","walled-garden-ipaddr"],label:"Walled Garden IP Address",rules:[{validator:(f,p)=>Xs(p)}],children:e.jsx(Q,{mode:"tags",tokenSeparators:[","]})})})]}),e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","idle-timeout"],label:"Idle Timeout",rules:[{required:!0,message:i("form.required")},{type:"number",max:65534,message:"idle-timeout must be less than 65535"},{type:"number",min:1,message:"idle-timeout must be greater than 0"}],children:e.jsx(ie,{min:0})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","session-timeout"],label:"Session Timeout",rules:[{type:"number",max:65534,message:"session-timeout must be less than 65535"},{type:"number",min:1,message:"session-timeout must be greater than 0"}],children:e.jsx(ie,{min:0})})})]}),l==="credentials"&&e.jsx(_,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx(A.Item,{label:"Credentials",name:["captive","credentials"],rules:[{required:!0,message:i("form.required")}],children:e.jsx(Zr,{})})})}),l==="uam"&&e.jsxs(e.Fragment,{children:[e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","uam-server"],label:"UAM Server",rules:[{required:!0,message:i("form.required")}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","uam-secret"],label:"UAM Secret",rules:[{required:!0,message:i("form.required")}],children:e.jsx(ae.Password,{})})})]}),e.jsx(_,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","uam-port"],label:"UAM Port",rules:[{required:!0,message:i("form.required")},{type:"number",min:1024,message:"captive.uam-port must be greater than 1023"},{type:"number",max:65534,message:"captive.uam-port must be less than 65535"}],children:e.jsx(ie,{})})})}),e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","nasid"],label:"NAS ID",rules:[{required:!0,message:i("form.required")}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","nasmac"],label:"NAS MAC",children:e.jsx(ae,{})})})]}),e.jsx(_,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","mac-format"],label:"MAC Format",rules:[{required:!0,message:i("form.required")}],children:e.jsx(Q,{options:Yr})})})})]}),(l==="radius"||l==="uam")&&e.jsxs(e.Fragment,{children:[e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","auth-server"],label:"Auth Server",rules:[{required:!0,message:i("form.required")},{validator:(f,p)=>ht(p)}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","auth-secret"],label:"Auth Secret",rules:[{required:!0,message:i("form.required")}],children:e.jsx(ae.Password,{})})})]}),e.jsx(_,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","auth-port"],label:"Auth Port",rules:[{required:!0,message:i("form.required")},{type:"number",min:1024,message:"captive.auth-port must be greater than 1023"},{type:"number",max:65534,message:"captive.auth-port must be less than 65535"}],children:e.jsx(ie,{})})})}),e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","acct-server"],label:"Acct Server",rules:[{validator:(f,p)=>ht(p)}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","acct-secret"],label:"Acct Secret",children:e.jsx(ae.Password,{})})})]}),e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","acct-port"],label:"Acct Port",rules:[{type:"number",min:1024,message:"captive.acct-port must be greater than 1023"},{type:"number",max:65534,message:"captive.acct-port must be less than 65535"}],children:e.jsx(ie,{})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["captive","acct-interval"],label:"Acct Interval",rules:[{type:"number",min:1,message:"acct-interval must be a positive number"},{type:"number",max:65534,message:"acct-interval must be less than 65535"}],children:e.jsx(ie,{})})})]})]}),l!=="uam"&&e.jsx(_,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx($t,{label:"Web Root",formName:["captive","web-root"],switchEnabled:u,onSwitchChange:c,type:3,siteId:a,mode:l,edit:b})})})]})]})},al=({resource:t,siteId:a})=>{const{t:s}=K(),i=t||{},[n,r]=d.useState(!!i["rate-limit"]),[l,o]=d.useState(!!i["access-control-list"]),[u,c]=d.useState(i.roaming&&i.roaming["message-exchange"]?"custom":i.roaming===!0?"on":"off"),b=A.useFormInstance(),x=A.useWatch(["encryption","proto"],b),g=["none","owe","owe-transition"];G.useEffect(()=>{if(x&&g.includes(x)){const p=b.getFieldValue("roaming");p!=null&&p!==!1&&b.setFieldsValue({roaming:void 0}),u!=="off"&&c("off")}},[x]);const y=(f,p)=>{const m=p.getFieldValue("services")||[];f?m.includes("wifi-steering")||p.setFieldValue("services",[...m,"wifi-steering"]):p.setFieldValue("services",m.filter(v=>v!=="wifi-steering"))},T=f=>{const p=f.getFieldValue("services")||[];return e.jsx(te,{checked:p.includes("wifi-steering"),onChange:m=>y(m,f)})};return e.jsx(A.Item,{noStyle:!0,shouldUpdate:!0,children:f=>{const p=m=>{const v=f.getFieldValue("services")||[];m!=="off"?v.includes("captive")||f.setFieldValue("services",[...v,"captive"]):v.includes("captive")&&f.setFieldValue("services",v.filter(h=>h!=="captive"))};return e.jsxs(e.Fragment,{children:[e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["hidden-ssid"],label:"Hidden SSID",tooltip:J("interface.ssid.hidden-ssid"),valuePropName:"checked",children:e.jsx(te,{})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{label:"Wi-Fi Steering",valuePropName:"checked",children:T(f)})})]}),e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["services"],label:"Services",style:{display:"none"},children:e.jsx(Q,{mode:"multiple",options:[]})})}),e.jsx(F,{span:12})]}),e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["maximum-clients"],label:"Maximum Clients",tooltip:J("interface.ssid.maximum-clients"),rules:[{required:!0,message:s("form.required")},{type:"number",min:1,max:65535,message:"maximum-clients must be 1 ~ 65535"}],children:e.jsx(ie,{})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["fils-discovery-interval"],label:"Fils-Discovery-Interval",tooltip:J("interface.ssid.fils-discovery-interval"),rules:[{required:!0,message:s("form.required")},{type:"number",min:1,max:20,message:"fils-discovery-interval must be 1 ~ 20"}],children:e.jsx(ie,{})})})]}),e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["dtim-period"],label:"Dtim-Period",rules:[{required:!0,message:s("form.required")},{type:"number",min:1,max:255,message:"dtim-period must be 1 ~ 255"}],children:e.jsx(ie,{})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["isolate-clients"],label:"Isolate Clients",tooltip:J("interface.ssid.isolate-clients"),valuePropName:"checked",children:e.jsx(te,{})})})]}),e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["power-save"],label:"Power Save",tooltip:J("interface.ssid.power-save"),valuePropName:"checked",children:e.jsx(te,{})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["broadcast-time"],label:"Broadcast Time",tooltip:J("interface.ssid.broadcast-time"),valuePropName:"checked",children:e.jsx(te,{})})})]}),e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["unicast-conversion"],label:"Unicast Conversion",tooltip:J("interface.ssid.unicast-conversion"),valuePropName:"checked",children:e.jsx(te,{})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["proxy-arp"],label:"Proxy ARP",tooltip:J("interface.ssid.proxy-arp"),valuePropName:"checked",children:e.jsx(te,{})})})]}),e.jsx(_,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["disassoc-low-ack"],label:"Disassoc Low Ack",valuePropName:"checked",children:e.jsx(te,{})})})}),e.jsx("h3",{className:"header2",children:"Roaming"}),e.jsx(_,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["encryption","key-caching"],label:"Key-Caching",tooltip:J("interface.ssid.encryption.key-caching"),valuePropName:"checked",children:e.jsx(te,{defaultChecked:!0})})})}),!x||x&&!g.includes(x)?e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{label:"FT Roaming",children:e.jsxs(Q,{value:u,onChange:m=>{c(m),m==="custom"?(f.getFieldValue("roaming")||{})["message-exchange"]||f.setFieldValue(["roaming","message-exchange"],"ds"):m==="on"&&f.setFieldValue("roaming",!0)},children:[e.jsx(Q.Option,{value:"on",children:"Auto"}),e.jsx(Q.Option,{value:"custom",children:"Custom"}),e.jsx(Q.Option,{value:"off",children:"Off"})]})})}),e.jsx(F,{span:12})]}):null,u==="on"&&e.jsx(A.Item,{name:["roaming"],style:{display:"none"},children:e.jsx(te,{})}),u==="custom"&&e.jsxs(e.Fragment,{children:[e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["roaming","message-exchange"],label:"Message Exchange",tooltip:J("interface.ssid.roaming.message-exchange"),rules:[{required:!0,message:s("form.required")}],children:e.jsxs(Q,{placeholder:"message-exchange",children:[e.jsx(Q.Option,{value:"air",children:"air"}),e.jsx(Q.Option,{value:"ds",children:"ds"})]})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["roaming","generate-psk"],label:"Generate PSK",tooltip:J("interface.ssid.roaming.generate-psk"),valuePropName:"checked",children:e.jsx(te,{})})})]}),e.jsx(_,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["roaming","domain-identifier"],label:"Domain Identifier",tooltip:J("interface.ssid.roaming.domain-identifier"),rules:[{validator:(m,v)=>v==null||v===""||/^[0-9A-Fa-f]{4}$/.test(String(v))?Promise.resolve():Promise.reject("domain-identifier must be exactly 4 hexadecimal characters")}],children:e.jsx(ae,{})})})})]}),e.jsxs("div",{style:{display:"none"},children:[e.jsx(A.Item,{name:["rrm","neighbor-reporting"],label:"Neighbor Reporting",tooltip:J("interface.ssid.rrm.neighbor-reporting"),valuePropName:"checked",children:e.jsx(te,{})}),e.jsx(A.Item,{name:["rrm","ftm-responder"],label:"FTM Responder",tooltip:J("interface.ssid.rrm.ftm-responder"),valuePropName:"checked",children:e.jsx(te,{})}),e.jsx(A.Item,{name:["rrm","stationary-ap"],label:"Stationary AP",tooltip:J("interface.ssid.rrm.stationary-ap"),valuePropName:"checked",children:e.jsx(te,{})})]}),e.jsx("h3",{className:"header2",children:"Rate Limit"}),e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{label:"Rate Limit",children:e.jsx(te,{checked:n,onChange:r})})}),e.jsx(F,{span:12})]}),n&&e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["rate-limit","ingress-rate"],label:"Ingress Rate",tooltip:J("interface.ssid.rate-limit.ingress-rate"),rules:[{required:!0,message:s("form.required")},{type:"number",min:0,max:65535,message:"ingress-rate must be less than 65535"}],children:e.jsx(ie,{min:0,addonAfter:"Mb/s",style:{width:280}})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["rate-limit","egress-rate"],label:"Egress Rate",tooltip:J("interface.ssid.rate-limit.egress-rate"),rules:[{required:!0,message:s("form.required")},{type:"number",min:0,max:65535,message:"egress-rate must be less than 65535"}],children:e.jsx(ie,{min:0,addonAfter:"Mb/s",style:{width:280}})})})]}),e.jsx("h3",{className:"header2",children:"Access Control List"}),e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{label:"Access-Control-List",children:e.jsx(te,{checked:l,onChange:o})})}),e.jsx(F,{span:12})]}),l&&e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["access-control-list","mode"],label:"Mode",rules:[{required:!0,message:s("form.required")}],children:e.jsxs(Q,{children:[e.jsx(Q.Option,{value:"allow",children:"Allow"}),e.jsx(Q.Option,{value:"deny",children:"Deny"})]})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:["access-control-list","mac-address"],label:"MAC Address",validateTrigger:["onChange","onBlur"],rules:[{required:!0,message:s("form.required")},{validator:(m,v)=>Zs(v)}],children:e.jsx(Q,{mode:"tags",tokenSeparators:[","]})})})]}),e.jsx(tl,{resource:t,siteId:a,onAuthModeChange:p})]})}})},et="/ampcon/wireless/configure";function Kt({siteId:t,pageNum:a,pageSize:s,sortBy:i,sortType:n}){return oe({url:`${et}/dhcp_service_list`,method:"GET",params:{siteId:t,pageNum:a,pageSize:s,sortBy:i,sortType:n}})}function sl({id:t}){return oe({url:`${et}/dhcp_service`,method:"GET",params:{id:t}})}function nl({site_id:t,name:a,subnet:s,vlan:i,dhcp_configure:n,description:r}){return oe({url:`${et}/dhcp_service`,method:"POST",data:{site_id:t,name:a,subnet:s,vlan:i,dhcp_configure:n,description:r}})}function il({id:t,name:a,subnet:s,vlan:i,dhcp_configure:n,description:r}){return oe({url:`${et}/dhcp_service`,method:"PUT",data:{id:t,name:a,subnet:s,vlan:i,dhcp_configure:n,description:r}})}function rl({id:t}){return oe({url:`${et}/dhcp_service`,method:"DELETE",data:{id:t}})}const lt=({value:t,onChange:a,...s})=>{const i=t==null||t===""||t==="-"?void 0:parseInt(String(t),10),n=r=>{r===null?a==null||a(""):Number.isInteger(r)&&(a==null||a(r.toString()))};return e.jsx(ie,{value:i,onChange:n,precision:0,...s})},Wt=({resource:t,onClose:a,refresh:s,siteId:i})=>{const{t:n}=K(),[r]=A.useForm(),[l,o]=d.useState(!1),[u,c]=d.useState(!1),[b,x]=d.useState(!1),g=(v,h,k)=>(P,S)=>{if(S==null||S===""||S==="-")return Promise.resolve();const j=Number(S);return Number.isInteger(j)?v===1&&j<v?Promise.reject(`configuration.${k} must be a positive number`):v!==void 0&&v!==1&&j<v?Promise.reject(`configuration.${k} must be greater than ${v}`):h!==void 0&&j>=h?Promise.reject(`configuration.${k} must be less than ${h}`):Promise.resolve():Promise.reject(`configuration.${k} must be an integer`)},y=d.useMemo(()=>({subnet:"***********/24",gateway:"***********","send-hostname":!0,"lease-first":1,"lease-count":128,"lease-time":"6h"}),[]),T=d.useMemo(()=>({"ipv6-subnet":"","ipv6-gateway":"","ipv6-prefix-size":64,"ipv6-dhcp-mode":"hybrid","ipv6-announce-dns":void 0,"ipv6-filter-prefix":"::/0"}),[]);d.useEffect(()=>{var v,h;if(t){const k={name:t.name||"",description:t.description||"",vlan_id:(h=(v=t.dhcp_configure)==null?void 0:v.vlan)==null?void 0:h.id,subnet:t.subnet||""};let P={};if(t.dhcp_configure&&typeof t.dhcp_configure=="string")try{P=JSON.parse(t.dhcp_configure)}catch{D.error("Failed to parse DHCP configure"),P={}}else P=t.dhcp_configure||{};const S=P.ipv4||{},j=S.dhcp||{},C=P.ipv6||{},E=C.dhcpv6||{},w=!!C.addressing;c(w),x(!!E.mode);const L={...k,ipv6:!!C.addressing,dhcp_v6:!!E.mode,gateway:S.gateway||"","send-hostname":S["send-hostname"]||!0,"lease-first":j["lease-first"],"lease-count":j["lease-count"],"lease-time":j["lease-time"],"ipv6-subnet":C.subnet||"","ipv6-gateway":C.gateway||"","ipv6-prefix-size":C["prefix-size"],"ipv6-dhcp-mode":E.mode||"hybrid","ipv6-announce-dns":E["announce-dns"]||void 0,"ipv6-filter-prefix":E["filter-prefix"]||""};r.setFieldsValue(L)}},[t,r]);const f=async v=>{o(!0);try{const{name:h,description:k,vlan_id:P,subnet:S,ipv6:j,...C}=v;let E;["-","",void 0,null].includes(P)?E=null:E=Number(P);const L={role:"downstream",ipv4:{addressing:"static",dhcp:{"lease-count":Number(C["lease-count"]),"lease-first":Number(C["lease-first"]),"lease-time":C["lease-time"]},gateway:C.gateway,"send-hostname":!0,subnet:S},...E!==null&&{vlan:{id:E}},name:h};if(u){const U=C["ipv6-prefix-size"],z=U===""||U===void 0||U===null?void 0:Number(U);L.ipv6={addressing:"static",subnet:C["ipv6-subnet"],gateway:C["ipv6-gateway"],...z!==void 0&&{"prefix-size":z}},b&&(L.ipv6.dhcpv6={mode:C["ipv6-dhcp-mode"],"announce-dns":C["ipv6-announce-dns"],"filter-prefix":C["ipv6-filter-prefix"]})}const O={site_id:i,name:h,subnet:S,dhcp_configure:zt(L),description:k||"",vlan:E??"-"};let I;if(t!=null&&t.id?I=await il({...O,id:t.id}):I=await nl(O),(I==null?void 0:I.status)!==200){D.error((I==null?void 0:I.info)||n("crud.error_create_obj",{obj:"DHCP Service"}));return}D.success(t!=null&&t.id?n("crud.success_update_obj",{obj:"DHCP Service"}):n("crud.success_create_obj",{obj:"DHCP Service"})),s&&s(),a&&a(!0)}catch{D.error(n("crud.error_create_obj",{obj:"DHCP Service"}))}finally{o(!1)}},p=v=>{c(v),v?(x(!0),r.setFieldsValue({ipv6:v,dhcp_v6:!0,"ipv6-subnet":T["ipv6-subnet"],"ipv6-gateway":T["ipv6-gateway"],"ipv6-prefix-size":T["ipv6-prefix-size"],"ipv6-dhcp-mode":T["ipv6-dhcp-mode"],"ipv6-filter-prefix":T["ipv6-filter-prefix"]})):(x(!1),r.setFieldsValue({ipv6:v,dhcp_v6:!1,"ipv6-subnet":void 0,"ipv6-gateway":void 0,"ipv6-prefix-size":void 0,"ipv6-dhcp-mode":void 0,"ipv6-announce-dns":void 0,"ipv6-filter-prefix":void 0}))},m=v=>{x(v),v?r.setFieldsValue({"ipv6-dhcp-mode":T["ipv6-dhcp-mode"],"ipv6-filter-prefix":T["ipv6-filter-prefix"]}):r.setFieldsValue({"ipv6-dhcp-mode":void 0,"ipv6-announce-dns":void 0,"ipv6-filter-prefix":void 0})};return e.jsxs(A,{form:r,name:"networkForm",onFinish:f,initialValues:{name:"",description:"",vlan_id:"",ipv6:!1,...y,...T},className:"wirelessFormCreate",children:[e.jsx(_,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"name",label:n("common.name"),rules:[{required:!0,message:n("form.required")},{validator:(v,h)=>h&&h&&new Blob([h]).size>64?Promise.reject("name must be less than 64 characters"):Promise.resolve()}],children:e.jsx(ae,{disabled:!!(t!=null&&t.id)})})})}),e.jsx(_,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"description",label:"Description",rules:[{validator:(v,h)=>h&&h&&new Blob([h]).size>128?Promise.reject("description must be less than 128 characters"):Promise.resolve()}],children:e.jsx(_s,{rows:2})})})}),e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:24,children:e.jsx("h3",{className:"header2",children:"IPv4"})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"subnet",label:"Subnet",tooltip:J("interface.ipv4.subnet"),rules:[{required:!0,message:n("form.required")},{validator:(v,h)=>h==="auto/24"?Promise.resolve():_t(h)?/^127\.0\.0\.0\/8$/.test(h)?Promise.reject(n("form.invalid_static_ipv4_loopback")):ua(h)?Dr(h)?Promise.resolve():Promise.reject(n("form.invalid_static_ipv4_e")):Promise.reject(n("form.invalid_static_ipv4_d")):Promise.reject(n("form.invalid_ipv4"))}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"gateway",label:"Gateway",tooltip:J("interface.ipv4.gateway"),rules:[{required:!0,message:n("form.required")},{validator:(v,h)=>/^127\.\d+\.\d+\.\d+(\/\d+)?$/.test(h)?Promise.reject(n("form.invalid_static_ipv4_loopback")):ua(h)?_t(h)?Promise.resolve():Promise.reject(n("form.invalid_ipv4")):Promise.reject(n("form.invalid_static_ipv4_d"))}],children:e.jsx(ae,{})})}),e.jsx(F,{span:24,children:e.jsx("h3",{className:"header2",children:"DHCPv4"})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"lease-first",label:"Lease-First",tooltip:J("interface.ipv4.dhcp.lease-first"),rules:[{required:!0,message:n("form.required")},{validator:g(1,void 0,"ipv4.dhcp.lease-first")}],children:e.jsx(lt,{})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"lease-count",label:"Lease-Count",tooltip:J("interface.ipv4.dhcp.lease-count"),rules:[{required:!0,message:n("form.required")},{validator:g(1,void 0,"ipv4.dhcp.lease-count")}],children:e.jsx(lt,{})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"lease-time",label:"Lease-Time",tooltip:J("interface.ipv4.dhcp.lease-time"),rules:[{validator:(v,h)=>Ur(h)?Promise.resolve():Promise.reject(n("form.invalid_lease_time"))}],children:e.jsx(ae,{})})})]}),e.jsx(_,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx(A.Item,{name:"ipv6",label:"IPv6",valuePropName:"checked",children:e.jsx(te,{onChange:p})})})}),u&&e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"ipv6-subnet",label:"Subnet",tooltip:J("interface.ipv6.subnet"),rules:[{required:!0,message:n("form.required")}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"ipv6-gateway",label:"Gateway",tooltip:J("interface.ipv6.gateway"),rules:[{required:!0,message:n("form.required")}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"ipv6-prefix-size",label:"Prefix-Size",tooltip:J("interface.ipv6.prefix-size"),rules:[{validator:g(0,65,"ipv6.prefix-size")}],children:e.jsx(lt,{})})}),e.jsx(F,{span:24,children:e.jsx(A.Item,{name:"dhcp_v6",label:"DHCPv6",valuePropName:"checked",children:e.jsx(te,{onChange:m,checked:b})})}),b&&e.jsxs(e.Fragment,{children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"ipv6-dhcp-mode",label:"Mode",tooltip:J("interface.ipv6.dhcpv6.mode"),children:e.jsx(Q,{options:[{value:"hybrid",label:"hybrid"},{value:"stateless",label:"stateless"},{value:"stateful",label:"stateful"}]})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"ipv6-announce-dns",label:"Announce-DNS",tooltip:J("interface.ipv6.dhcpv6.announce-dns"),children:e.jsx(Q,{mode:"tags",allowClear:!0,style:{width:"100%"},tokenSeparators:[","," "],notFoundContent:"Type the value you need to create..."})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"ipv6-filter-prefix",label:"Filter-Prefix",tooltip:J("interface.ipv6.dhcpv6.filter-prefix"),children:e.jsx(ae,{})})})]})]}),e.jsx(_,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx("h3",{className:"header2",children:"VLAN"})})}),e.jsx(_,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx(A.Item,{name:"vlan_id",label:"VLAN ID",rules:[{validator:g(1,4095,"vlan.id")}],children:e.jsx(lt,{})})})}),e.jsx(de,{}),e.jsx(_,{children:e.jsxs(F,{span:24,style:{textAlign:"right"},children:[e.jsx(Z,{onClick:()=>{r.resetFields(),a&&a()},disabled:l,style:{marginRight:8},children:"Cancel"}),e.jsx(Z,{type:"primary",htmlType:"submit",disabled:l,loading:l,children:"Apply"})]})})]})},xa=async t=>{try{const a=await Kt({siteId:t,pageNum:1,pageSize:100});return(a==null?void 0:a.status)===200&&Array.isArray(a.info)?a.info.map(s=>({label:s.name,value:s.name})):[]}catch{return[]}},ll=({isDisabled:t=!1,resource:a,onClose:s,refresh:i,siteId:n,modalOpen:r})=>{var L;const{t:l}=K(),[o]=A.useForm(),[u,c]=d.useState((a==null?void 0:a.network_type)===2),[b,x]=d.useState((a==null?void 0:a.network_type)===3),[g,y]=d.useState([]),[T,f]=d.useState(!1),[p,m]=d.useState(!1),v=async O=>{m(!1),f(!0);const I=await xa(n);if(y(I),f(!1),O&&I&&I.length>0){const U=I[I.length-1];o.setFieldValue(["dhcp_name"],U==null?void 0:U.value)}},h=Hr(l,!0).cast();let k,P={};a&&a.ssid_configure?(P={...a.ssid_configure},P.labels_name=a.labels_name,P.radius&&(P.radius=Ge(P.radius)),P["time-range-index"]&&(P["time-range-index"]=Ge(P["time-range-index"])),P["multi-psk"]&&(P["multi-psk"]=Ge(P["multi-psk"])),(L=P.captive)!=null&&L["web-root"]&&(P.captive["web-root"]=Ge(P.captive["web-root"])),a.network_type===2?P.vlan=parseInt(a.vlan_or_dhcp_name):a.network_type===3&&(P.dhcp_name=a.vlan_or_dhcp_name),k=P):k=h;const[S,j]=d.useState(!!(k!=null&&k["time-range-index"])),[C,E]=d.useState([]);G.useEffect(()=>{nn({siteId:n}).then(O=>{(O==null?void 0:O.status)===200&&Array.isArray(O.info)?E(O.info.map(I=>({label:I.name,value:I.name}))):E([])}).catch(()=>E([]))},[n]);const w=O=>{var pe,W;const I=zt(O);delete I.labels_name,delete I.network;const U=I.name,z=(pe=Wa.find(V=>V.value===I.encryption.proto))==null?void 0:pe.label;let B="",R=1;u&&(B=I.vlan,R=2,delete I.vlan),b&&(B=I.dhcp_name,R=3,delete I.dhcp_name),I.radius&&(I.radius=He(I.radius)),I["time-range-index"]&&(I["time-range-index"]=He(I["time-range-index"])),I["time-range-name"]||delete I["time-range-name"],I["multi-psk"]&&(I["multi-psk"]=He(I["multi-psk"])),(W=I.captive)!=null&&W["web-root"]&&(I.captive["web-root"]=He(I.captive["web-root"]));const se=(I["wifi-bands"]||[]).join(","),ce=JSON.stringify(I);a&&a.id?gr({id:a.id,name:U,security:z,radio:se,vlan_or_dhcp_name:B,network_type:R,ssid_configure:ce,labels_name:O.labels_name||[]}).then(V=>{if((V==null?void 0:V.status)!==200){D.error(V==null?void 0:V.info);return}D.success(V.info),i&&i(),s&&s()}).catch(()=>D.error("Failed to create Configure")):pr({site_id:n,name:U,security:z,radio:se,vlan_or_dhcp_name:B,network_type:R,ssid_configure:ce,labels_name:O.labels_name||[]}).then(V=>{if((V==null?void 0:V.status)!==200){D.error(V==null?void 0:V.info);return}D.success(V.info),i&&i(),s&&s()}).catch(()=>D.error("Failed to create Configure"))};return G.useEffect(()=>{b&&(f(!0),xa(n).then(O=>{y(O),f(!1)}))},[b,n]),e.jsxs(Qt,{open:r,title:a?"Edit SSID":"Create New SSID",onCancel:s,onFinish:w,initialValues:k,form:o,children:[e.jsxs(_,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"name",label:"SSID",tooltip:J("interface.ssid.name"),rules:[{required:!0,message:l("form.min_max_string",{min:1,max:32})},{min:1,max:32,message:l("form.min_max_string",{min:1,max:32})}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,style:{display:"none"},children:e.jsx(A.Item,{name:"bss-mode",label:"Bss Mode",tooltip:J("interface.ssid.bss-mode"),rules:[{required:!0,message:l("form.required")}],children:e.jsxs(Q,{placeholder:"bss-mode",children:[e.jsx(Q.Option,{value:"ap",children:"ap"}),e.jsx(Q.Option,{value:"sta",children:"sta"}),e.jsx(Q.Option,{value:"mesh",children:"mesh"}),e.jsx(Q.Option,{value:"wds-ap",children:"wds-ap"}),e.jsx(Q.Option,{value:"wds-sta",children:"wds-sta"})]})})}),e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"wifi-bands",label:"Wi-Fi Bands",tooltip:J("interface.ssid.wifi-bands"),rules:[{required:!0,message:l("form.required")}],children:e.jsx(Be.Group,{options:["2G","5G","6G"],value:["2G","5G","6G"]})})})]}),e.jsx(_,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx($t,{label:"Schedule Switch-on",title:"Time Range",formName:["time-range-index"],enableName:["ssid-schedule-enable"],selectName:["time-range-name"],switchEnabled:S,onSwitchChange:j,type:4,siteId:n,edit:P==null?void 0:P["time-range-index"]})})}),e.jsx(_,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx(A.Item,{name:"labels_name",label:"AP Label",children:e.jsx(Q,{mode:"multiple",options:C,allowClear:!0})})})}),!b&&e.jsx(_,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(A.Item,{label:"VLAN",children:e.jsx(te,{checked:u,onChange:O=>{c(O),O&&x(!1)},style:{marginRight:8}})})})}),u&&e.jsx(_,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"vlan",label:" ",required:!1,rules:[{required:!0,message:l("form.required")},{type:"number",max:4094,message:"vlan must be less than 4095"},{type:"number",min:1,message:"vlan must be greater than 0"}],children:e.jsx(ie,{})})})}),!u&&e.jsx(_,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(A.Item,{label:"AP Assign IP",tooltip:J("The AP acts as a DHCP server to assign IP addresses to clients"),children:e.jsx(te,{checked:b,onChange:O=>{x(O),O&&c(!1)},style:{marginRight:8}})})})}),b&&e.jsxs(e.Fragment,{children:[e.jsx(_,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(A.Item,{name:"dhcp_name",label:"DHCP Service Name",rules:[{required:!0,message:l("form.required")}],children:e.jsx(Q,{options:g,loading:T,dropdownRender:O=>e.jsxs(e.Fragment,{children:[O,e.jsx(Z,{type:"link",icon:e.jsx(Ze,{}),style:{width:"100%",borderTop:"1px solid #E7E7E7"},onClick:()=>m(!0),children:"Create New DHCP Service"})]})})})})}),p&&e.jsx(Xe,{isModalOpen:p,title:"Create DHCP Service",onCancel:()=>m(!1),modalClass:"ampcon-max-modal",childItems:e.jsx(Wt,{resource:void 0,onClose:v,siteId:n})})]}),e.jsx("h3",{className:"header2",children:"Authentication"}),e.jsx(Xr,{resource:P,siteId:n}),e.jsx("div",{style:{borderTop:"1px solid #eee",marginTop:22}}),e.jsx(Re,{bordered:!1,defaultActiveKey:[],expandIconPosition:"end",style:{background:"#FFFFFF",marginBottom:20},children:e.jsx(Re.Panel,{header:e.jsx("h3",{style:{fontSize:16,margin:0},children:"Advanced Settings"}),forceRender:!0,children:e.jsx(al,{resource:P,siteId:n})},"advanced")})]})},ol=({siteId:t=0})=>{if(window.location.hash){const S=window.location.hash.replace("#","");/^\d+$/.test(S)&&(t=parseInt(S,10))}const[a,s]=d.useState([]),[i,n]=d.useState(!1),[r,l]=d.useState(!1),[o,u]=d.useState(null),[c,b]=d.useState({current:1,pageSize:10,total:0}),[x,g]=d.useState({}),y=(S=1,j=10,C=x)=>{n(!0);const E=C.field?[{field:C.field,order:C.order}]:[];vr(t,S,j,[],E).then(w=>{if((w==null?void 0:w.status)!==200){D.error(w==null?void 0:w.info);return}s((w==null?void 0:w.info)||[]),b({current:S,pageSize:j,total:(w==null?void 0:w.total)||0})}).catch(()=>D.error("Failed to fetch list")).finally(()=>n(!1))};d.useEffect(()=>{y()},[t]);const T=S=>{Ee("Are you sure you want to delete?",()=>{br({id:S.key}).then(j=>{if((j==null?void 0:j.status)!==200){D.error(j==null?void 0:j.info);return}D.success("Deleted successfully"),y(c.current,c.pageSize)}).catch(()=>D.error("Delete failed"))})},f=S=>{ca(S.key).then(j=>{if((j==null?void 0:j.status)!==200){D.error(j==null?void 0:j.info);return}let C=(j==null?void 0:j.info)||null;if(C&&typeof C.ssid_configure=="string")try{C.ssid_configure=JSON.parse(C.ssid_configure)}catch{C.ssid_configure={}}u(C),l(!0)}).catch(()=>D.error("Failed to fetch detail"))},p=()=>{u(null),l(!0)},m=()=>{l(!1),u(null)},v=(S,j)=>{xr({id:j.key,is_enable:S?1:0}).then(C=>{if((C==null?void 0:C.status)!==200){D.error(C==null?void 0:C.info);return}D.success("Status updated"),s(E=>E.map(w=>w.id===j.key?{...w,is_enable:S?1:0}:w))}).catch(()=>D.error("Failed to update status"))},h=S=>{ca(S.key).then(j=>{if((j==null?void 0:j.status)!==200){D.error(j==null?void 0:j.info);return}let C=(j==null?void 0:j.info)||null;if(C&&typeof C.ssid_configure=="string"){C.name=C.name+"_Copy";try{C.ssid_configure=JSON.parse(C.ssid_configure),C.ssid_configure.name=C.ssid_configure.name+"_Copy",delete C.id}catch{C.ssid_configure={}}}u(C),l(!0)}).catch(()=>D.error("Failed to fetch detail"))},k=(a||[]).map(S=>({key:S.id,name:S.name,security:S.security,radio:S.radio,vlan_or_dhcp_name:S.network_type===2?S.vlan_or_dhcp_name:"-",labels_name:S.labels_name,is_enable:S.is_enable,originResource:S})),P=[{title:"SSID Name",dataIndex:"name",key:"name",sorter:!0},{title:"Security",dataIndex:"security",key:"security",sorter:!0},{title:"Radio",dataIndex:"radio",key:"radio",sorter:!0},{title:"VLAN",dataIndex:"vlan_or_dhcp_name",key:"vlan_or_dhcp_name",sorter:!1},{title:"AP Label",dataIndex:"labels_name",key:"labels_name",sorter:!0,render:S=>Array.isArray(S)?S.join(", "):S||""},{title:"WLAN Status",dataIndex:"is_enable",key:"is_enable",sorter:!0,render:(S,j)=>e.jsx(te,{checked:S===1,onChange:C=>v(C,j)})},{title:"Operation",key:"operation",render:(S,j)=>e.jsxs(Je,{size:24,children:[e.jsx(Z,{style:{padding:0},type:"link",onClick:()=>f(j),children:"Edit"}),e.jsx(Z,{style:{padding:0},type:"link",onClick:()=>h(j),children:"Copy"}),e.jsx(Z,{style:{padding:0},type:"link",onClick:()=>T(j),children:"Delete"})]})}];return e.jsxs("div",{children:[e.jsx("div",{style:{marginBottom:16},children:e.jsxs(Z,{type:"primary",onClick:p,children:[e.jsx(Pe,{component:yt}),"Create New SSID"]})}),e.jsx(Ye,{columns:P,dataSource:k,loading:i,pagination:{current:c.current,pageSize:c.pageSize,total:c.total,showTotal:(S,j)=>`${j[0]}-${j[1]} of ${S} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],onChange:(S,j)=>y(S,j)},rowKey:"key",bordered:!0,onChange:(S,j,C)=>{let E="",w="";Array.isArray(C)||(C.order==="ascend"?E="asc":C.order==="descend"&&(E="desc"),typeof C.field=="string"?w=C.field:w=""),g({field:w,order:E}),y(S.current,S.pageSize,{field:w,order:E})}}),e.jsx(ll,{modalOpen:r,onClose:m,refresh:()=>y(c.current,c.pageSize),resource:o,isDisabled:!1,siteId:t},(o==null?void 0:o.id)||String(r))]})},dl=(t,a)=>{var s,i,n;try{if(!t||!a)return null;const r=t.split("."),{length:l}=r;if(l<2)return null;const o=r.slice(0,l-1),u=r[l-1];return((n=(i=(s=a[o.slice(0,l-1).join(".")])==null?void 0:s.properties)==null?void 0:i[u??""])==null?void 0:n.description)??null}catch{return null}},cl=({definitionKey:t})=>{const{configurationDescriptions:a}=Us();if(!d.useMemo(()=>dl(t,a),[a]))return null;const{title:i,icon:n}=J(t);return e.jsx(ue,{title:i,children:n})},Se=G.memo(cl),{Option:ul}=Q,hl=({options:t,label:a,value:s="",onChange:i,onBlur:n,error:r=void 0,touched:l=!1,isRequired:o=!1,isDisabled:u=!1,isHidden:c=!1,isLabelHidden:b=!1,w:x=void 0,definitionKey:g=void 0,dropdownRender:y,onDropdownVisibleChange:T})=>c?null:e.jsx(A.Item,{label:!b&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:a}),e.jsx(Se,{definitionKey:g}),o&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]}),validateStatus:r&&l?"error":"",help:r&&l?r:null,children:e.jsx(Q,{value:s,onChange:f=>i==null?void 0:i({target:{value:f}}),onBlur:n,disabled:u,style:{borderRadius:8,width:typeof x=="number"?`${x}px`:x||"100%"},popupMatchSelectWidth:!0,dropdownRender:y,onDropdownVisibleChange:T,children:t.map(f=>e.jsx(ul,{value:f.value,children:f.label},ke()))})}),fl=G.memo(hl,Nt),ml=({options:t,name:a,isDisabled:s,label:i,isRequired:n,onChange:r,onChangeEffect:l,isHidden:o,isLabelHidden:u,emptyIsUndefined:c,isInt:b,w:x,definitionKey:g,dropdownRender:y,onDropdownVisibleChange:T})=>{const[{value:f},{touched:p,error:m},{setValue:v,setTouched:h}]=jt(a),k=d.useCallback(S=>{r?r(S):(c&&S.target.value===""?v(void 0):v(b?parseInt(S.target.value,10):S.target.value),l!==void 0&&l(S),setTimeout(()=>{h(!0)},200))},[r]),P=d.useCallback(()=>{h(!0)},[]);return e.jsx(fl,{label:i,value:f,onChange:k,onBlur:P,error:m,touched:p,options:t,isDisabled:s,isRequired:n,isHidden:o,isLabelHidden:u,w:x,definitionKey:g,dropdownRender:y,onDropdownVisibleChange:T})},ft=G.memo(ml),pl=({element:t,label:a,value:s,onChange:i,onBlur:n,error:r,isError:l,isRequired:o,isDisabled:u,definitionKey:c})=>(console.log("value",s),e.jsx(A.Item,{label:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:a}),e.jsx(Se,{definitionKey:c}),o&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]}),help:l?r:null,validateStatus:l?"error":"",labelCol:{style:{display:"flex",alignItems:"center"}},style:{marginBottom:a==="VLAN"&&s?8:24},children:t??e.jsx(te,{checked:s,onChange:i,onBlur:n,disabled:u})})),gl=G.memo(pl),xl=({name:t,isDisabled:a=!1,label:s,isRequired:i=!1,defaultValue:n,element:r,falseIsUndefined:l,definitionKey:o,onChangeCallback:u})=>{const{value:c,error:b,isError:x,onChange:g,onBlur:y}=ne({name:t}),T=d.useCallback(f=>{g(l&&!f?void 0:f),u&&u(f)},[l,u,g]);return e.jsx(gl,{label:s??t,value:c===void 0&&n!==void 0?n:c!==void 0&&c,onChange:T,error:b,onBlur:y,isError:x,isDisabled:a,isRequired:i,element:r,definitionKey:o})},le=G.memo(xl),bl=({editing:t})=>{const{t:a}=K(),{values:s,setFieldValue:i,errors:n}=ye(),r=[{value:"",label:a("common.none")},{value:"UTC-11:00",label:"Midway Islands Time (UTC-11:00)"},{value:"UTC-10:00",label:"Hawaii Standard Time (UTC-10:00)"},{value:"UTC-8:00",label:"Pacific Standard Time (UTC-8:00)"},{value:"UTC-7:00",label:"Mountain Standard Time (UTC-7:00)"},{value:"UTC-6:00",label:"Central Standard Time (UTC-6:00)"},{value:"UTC-5:00",label:"Eastern Standard Time (UTC-5:00)"},{value:"UTC-4:00",label:"Puerto Rico and US Virgin Islands Time (UTC-4:00)"},{value:"UTC-3:30",label:"Canada Newfoundland Time (UTC-3:30)"},{value:"UTC-3:00",label:"Brazil Eastern Time (UTC-3:00)"},{value:"UTC-1:00",label:"Central African Time (UTC-1:00)"},{value:"UTC",label:"Universal Coordinated Time (UTC)"},{value:"UTC+1:00",label:"European Central Time (UTC+1:00)"},{value:"UTC+2:00",label:"Eastern European Time (UTC+2:00)"},{value:"UTC+2:00",label:"(Arabic) Egypt Standard Time (UTC+2:00)"},{value:"UTC+3:00",label:"Eastern African Time (UTC+3:00)"},{value:"UTC+3:30",label:"Middle East Time (UTC+3:30)"},{value:"UTC+4:00",label:"Near East Time (UTC+4:00)"},{value:"UTC+5:00",label:"Pakistan Lahore Time (UTC+5:00)"},{value:"UTC+5:30",label:"India Standard Time (UTC+5:30)"},{value:"UTC+6:00",label:"Bangladesh Standard Time (UTC+6:00)"},{value:"UTC+7:00",label:"Vietnam Standard Time (UTC+7:00)"},{value:"UTC+8:00",label:"China Taiwan Time (UTC+8:00)"},{value:"UTC+9:00",label:"Japan Standard Time (UTC+9:00)"},{value:"UTC+9:30",label:"Australia Central Time (UTC+9:30)"},{value:"UTC+10:00",label:"Australia Eastern Time (UTC+10:00)"},{value:"UTC+11:00",label:"Solomon Standard Time (UTC+11:00)"},{value:"UTC+12:00",label:"New Zealand Standard Time (UTC+12:00)"},{value:"UTC",label:"UTC,Canary Islands (UTC)"},{value:"UTC",label:"Casablanca (UTC)"},{value:"UTC",label:"Monrovia, Reykjavik (UTC)"},{value:"UTC",label:"Dublin, Edinburgh, Lisbon, London (UTC)"},{value:"UTC+01:00",label:"Western Central Africa (UTC+01:00)"},{value:"UTC+01:00",label:"Brussels, Copenhagen, Madrid, Paris (UTC+01:00)"},{value:"UTC+01:00",label:"Windhoek (UTC+01:00)"},{value:"UTC+01:00",label:"Sarajevo, Skopje, Warsaw, Zagreb (UTC+01:00)"},{value:"UTC+01:00",label:"Belgrade, Bratislava, Budapest, Ljubljana, Prague (UTC+01:00)"},{value:"UTC+01:00",label:"Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna (UTC+01:00)"},{value:"UTC+02:00",label:"Harare, Pretoria (UTC+02:00)"},{value:"UTC+02:00",label:"Damascus (UTC+02:00)"},{value:"UTC+02:00",label:"Amman (UTC+02:00)"},{value:"UTC+02:00",label:"Cairo (UTC+02:00)"},{value:"UTC+02:00",label:"Minsk (UTC+02:00)"},{value:"UTC+02:00",label:"Jerusalem (UTC+02:00)"},{value:"UTC+02:00",label:"Beirut (UTC+02:00)"},{value:"UTC+02:00",label:"Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius (UTC+02:00)"},{value:"UTC+02:00",label:"Athens, Bucharest (UTC+02:00)"},{value:"UTC+03:00",label:"Istanbul (UTC+03:00)"},{value:"UTC+03:00",label:"Nairobi (UTC+03:00)"},{value:"UTC+03:00",label:"Kaliningrad (UTC+03:00)"},{value:"UTC+03:00",label:"Baghdad (UTC+03:00)"},{value:"UTC+03:00",label:"Kuwait, Riyadh (UTC+03:00)"},{value:"UTC+03:00",label:"Moscow, St. Petersburg (UTC+03:00)"},{value:"UTC+03:30",label:"Tehran (UTC+03:30)"},{value:"UTC+04:00",label:"Volgograd (UTC+04:00)"},{value:"UTC+04:00",label:"Yerevan (UTC+04:00)"},{value:"UTC+04:00",label:"Baku (UTC+04:00)"},{value:"UTC+04:00",label:"Tbilisi (UTC+04:00)"},{value:"UTC+04:00",label:"Port Louis (UTC+04:00)"},{value:"UTC+04:00",label:"Abu Dhabi, Muscat (UTC+04:00)"},{value:"UTC+04:30",label:"Kabul (UTC+04:30)"},{value:"UTC+05:00",label:"Islamabad, Karachi (UTC+05:00)"},{value:"UTC+05:00",label:"Tashkent (UTC+05:00)"},{value:"UTC+05:30",label:"Chennai, Kolkata, Mumbai, New Delhi (UTC+05:30)"},{value:"UTC+05:30",label:"Sri Lanka Standard Time (UTC+05:30)"},{value:"UTC+05:45",label:"Kathmandu (UTC+05:45)"},{value:"UTC+06:00",label:"Ekaterinburg (UTC+06:00)"},{value:"UTC+06:00",label:"Dhaka (UTC+06:00)"},{value:"UTC+06:00",label:"Astana (UTC+06:00)"},{value:"UTC+06:30",label:"Yangon (UTC+06:30)"},{value:"UTC+07:00",label:"Novosibirsk (UTC+07:00)"},{value:"UTC+07:00",label:"Bangkok, Hanoi, Jakarta (UTC+07:00)"},{value:"UTC+08:00",label:"Ulaanbaatar (UTC+08:00)"},{value:"UTC+08:00",label:"Irkutsk (UTC+08:00)"},{value:"UTC+08:00",label:"Krasnoyarsk (UTC+08:00)"},{value:"UTC+08:00",label:"Beijing, Chongqing, Hong Kong, Urumqi (UTC+08:00)"},{value:"UTC+08:00",label:"Taipei (UTC+08:00)"},{value:"UTC+08:00",label:"Kuala Lumpur, Singapore (UTC+08:00)"},{value:"UTC+08:00",label:"Perth (UTC+08:00)"},{value:"UTC+09:00",label:"Osaka, Sapporo, Tokyo (UTC+09:00)"},{value:"UTC+09:00",label:"Yakutsk (UTC+09:00)"},{value:"UTC+09:00",label:"Seoul (UTC+09:00)"},{value:"UTC+09:30",label:"Darwin (UTC+09:30)"},{value:"UTC+09:30",label:"Adelaide (UTC+09:30)"},{value:"UTC+10:00",label:"Guam, Port Moresby (UTC+10:00)"},{value:"UTC+10:00",label:"Canberra, Melbourne, Sydney (UTC+10:00)"},{value:"UTC+10:00",label:"Brisbane (UTC+10:00)"},{value:"UTC+10:00",label:"Hobart (UTC+10:00)"},{value:"UTC+11:00",label:"Solomon Islands, New Caledonia (UTC+11:00)"},{value:"UTC+11:00",label:"Vladivostok (UTC+11:00)"},{value:"UTC+12:00",label:"UTC+12 (UTC+12:00)"},{value:"UTC+12:00",label:"Auckland, Wellington (UTC+12:00)"},{value:"UTC+12:00",label:"Fiji (UTC+12:00)"},{value:"UTC+12:00",label:"Magadan (UTC+12:00)"},{value:"UTC+13:00",label:"Nukualofa (UTC+13:00)"},{value:"UTC+14:00",label:"Kiritimati (UTC+14:00)"},{value:"UTC-01:00",label:"Azores Islands (UTC-01:00)"},{value:"UTC-01:00",label:"Cape Verde Islands (UTC-01:00)"},{value:"UTC-02:00",label:"Mid-Atlantic (UTC-02:00)"},{value:"UTC-02:00",label:"UTC-02 (UTC-02:00)"},{value:"UTC-03:00",label:"Cayenne, Fortaleza (UTC-03:00)"},{value:"UTC-03:00",label:"Brasilia (UTC-03:00)"},{value:"UTC-03:00",label:"Buenos Aires (UTC-03:00)"},{value:"UTC-03:00",label:"Greenland (UTC-03:00)"},{value:"UTC-03:00",label:"Montevideo (UTC-03:00)"},{value:"UTC-03:30",label:"Newfoundland (UTC-03:30)"},{value:"UTC-04:00",label:"Georgetown, La Paz, Manaus, San Juan (UTC-04:00)"},{value:"UTC-04:00",label:"Asuncion (UTC-04:00)"},{value:"UTC-04:00",label:"Santiago (UTC-04:00)"},{value:"UTC-04:00",label:"Atlantic Time (Canada) (UTC-04:00)"},{value:"UTC-04:00",label:"Cuiaba (UTC-04:00)"},{value:"UTC-04:30",label:"Caracas (UTC-04:30)"},{value:"UTC-05:00",label:"Eastern Time (US and Canada) (UTC-05:00)"},{value:"UTC-05:00",label:"Indiana (East) (UTC-05:00)"},{value:"UTC-05:00",label:"Bogota, Lima, Quito (UTC-05:00)"},{value:"UTC-06:00",label:"Central America (UTC-06:00)"},{value:"UTC-06:00",label:"Central Time (US and Canada) (UTC-06:00)"},{value:"UTC-06:00",label:"Guadalajara, Mexico City, Monterrey (UTC-06:00)"},{value:"UTC-06:00",label:"Saskatchewan (UTC-06:00)"},{value:"UTC-07:00",label:"Arizona (UTC-07:00)"},{value:"UTC-07:00",label:"Chihuahua, La Paz, Mazatlan (UTC-07:00)"},{value:"UTC-07:00",label:"Mountain Time (US and Canada) (UTC-07:00)"},{value:"UTC-08:00",label:"Baja California (UTC-08:00)"},{value:"UTC-08:00",label:"Pacific Time (US and Canada) (UTC-08:00)"},{value:"UTC-09:00",label:"Alaska (UTC-09:00)"},{value:"UTC-10:00",label:"Hawaii (UTC-10:00)"},{value:"UTC-11:00",label:"Samoa (UTC-11:00)"},{value:"UTC-11:00",label:"UTC-11 (UTC-11:00)"},{value:"UTC-12:00",label:"Dateline Standard Time (UTC-12:00)"}].map((l,o)=>({value:l.value!==""?`${l.value}#${o}`:l.value,label:l.label}));return e.jsxs(_,{gutter:[16,0],children:[e.jsx(F,{span:12,children:e.jsx(le,{name:"configuration.unit.hostname_enable",label:"Hostname",definitionKey:"unit.hostname",isDisabled:!t})}),e.jsx(F,{span:12,children:e.jsx(ft,{w:280,name:"configuration.unit.timezone",label:"Timezone",definitionKey:"unit.timezone",emptyIsUndefined:!0,isDisabled:!t,options:r})}),e.jsx(F,{span:12,children:e.jsx(le,{name:"configuration.unit.leds-active",label:"Leds-Active",definitionKey:"unit.leds-active",isDisabled:!t,isRequired:!0})}),e.jsx(F,{span:12,children:e.jsx(le,{name:"configuration.unit.random-password",label:"Random-Password",definitionKey:"unit.random-password",isDisabled:!t,isRequired:!0})})]})},vl=({siteId:t})=>{if(window.location.hash){const P=window.location.hash.replace("#","");/^\d+$/.test(P)&&(t=parseInt(P,10))}const[a,s]=d.useState([]),[i,n]=d.useState(!1),[r,l]=d.useState(!1),[o,u]=d.useState(null),[c,b]=d.useState({current:1,pageSize:10,total:0}),[x,g]=d.useState([]),y=d.useCallback(P=>{const S=Math.floor(new Date(P).getTime()/1e3);return e.jsx(Ct,{date:S})},[]),T=(P,S)=>{if(n(!0),t==null||t==null){n(!1);return}const j=S.length>0?S[0].field:"",C=S.length>0?S[0].order:"",E={siteId:t,pageNum:P.current,pageSize:P.pageSize};j&&(E.sortBy=j,E.sortType=C),Kt(E).then(w=>{if((w==null?void 0:w.status)!==200){D.error(w==null?void 0:w.info);return}s((w==null?void 0:w.info)||[]),b({...P,total:(w==null?void 0:w.total)||0})}).catch(()=>D.error("Failed to fetch list")).finally(()=>n(!1))};d.useEffect(()=>{T(c,x)},[t]);const f=(P,S,j)=>{const C={current:P.current||1,pageSize:P.pageSize||10,total:P.total};let E=j.field;const w=j.field?[{field:E,order:j.order==="ascend"?"asc":"desc"}]:[];g(w),T(C,w)},p=P=>{Ee(`Are you sure you want to delete this ${P.name}?`,()=>{rl({id:P.id}).then(S=>{if((S==null?void 0:S.status)!==200){D.error(S==null?void 0:S.info);return}D.success("Deleted successfully"),T(c,x)}).catch(()=>D.error("Delete failed"))})},m=P=>{sl({id:P.id}).then(S=>{if((S==null?void 0:S.status)!==200){D.error(S==null?void 0:S.info);return}let j=(S==null?void 0:S.info)||null;u(j),l(!0)}).catch(()=>D.error("Failed to fetch detail"))},v=()=>{u(null),l(!0)},h=()=>{l(!1),u(null)},k=[{title:"Name",dataIndex:"name",key:"name",sorter:!0},{title:"IPv4 subnet",dataIndex:"subnet",key:"subnet",sorter:!0},{title:"VLAN",dataIndex:"vlan",key:"vlan",sorter:!0},{title:"Modified",dataIndex:"modified_time",key:"modified_time",render:P=>y(P),sorter:!0},{title:"Description",dataIndex:"description",key:"description",sorter:!0},{title:"Operation",key:"operation",render:(P,S)=>e.jsxs(Je,{size:24,children:[e.jsx(Z,{style:{padding:0},type:"link",onClick:()=>m(S),children:"Edit"}),e.jsx(Z,{style:{padding:0},type:"link",onClick:()=>p(S),children:"Delete"})]})}];return e.jsxs("div",{children:[e.jsx("div",{style:{marginBottom:24},children:e.jsxs(Z,{type:"primary",onClick:v,children:[e.jsx(Pe,{component:yt}),"Create"]})}),e.jsx(Ue,{vertical:!0,style:{position:"relative",width:"100%",marginBottom:a&&a.length>0?"8px":"24px"},children:e.jsx(Ye,{columns:k,dataSource:a,loading:i,onChange:f,pagination:{current:c.current,pageSize:c.pageSize,total:c.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:P=>`Total ${P} items`,pageSizeOptions:["10","20","50","100"]},rowKey:"key",bordered:!0})}),e.jsx(Xe,{isModalOpen:r,title:o?"Edit":"Create",onCancel:h,modalClass:"ampcon-max-modal",childItems:e.jsx(Wt,{onClose:h,refresh:()=>T(c,x),resource:o,siteId:t})})]})},yl={value:M.arrayOf(M.oneOfType([M.string,M.number])),label:M.string.isRequired,onChange:M.func.isRequired,options:M.arrayOf(M.shape({label:M.string.isRequired,value:M.oneOfType([M.string,M.number]).isRequired})).isRequired,onBlur:M.func.isRequired,error:M.oneOfType([M.string,M.bool]),touched:M.bool,isDisabled:M.bool,canSelectAll:M.bool,isRequired:M.bool,isHidden:M.bool,isPortal:M.bool.isRequired,definitionKey:M.string,w:M.oneOfType([M.string,M.number])},jl={value:[],error:!1,touched:!1,isRequired:!1,canSelectAll:!1,isDisabled:!1,isHidden:!1,definitionKey:null},Jt=({options:t,label:a,value:s,onChange:i,onBlur:n,error:r,touched:l,canSelectAll:o,isRequired:u,isDisabled:c,isHidden:b,isPortal:x,definitionKey:g,w:y})=>{const{t:T}=K(),[f,p]=d.useState(!1),[m,v]=d.useState(s??[]),[h,k]=d.useState(""),P=m.includes("LAN*"),S=o?[{value:"*",label:T("common.all")},...t]:t,j=S.filter(L=>L.label.toLowerCase().includes(h.toLowerCase())||L.value.toString().toLowerCase().includes(h.toLowerCase()));d.useEffect(()=>{v(s??[])},[s]);const C=()=>{i(m),p(!1),k("")},E=()=>{v([]),i([]),p(!1),k("")},w=L=>{k(L.target.value)};return e.jsx(A.Item,{label:!b&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:a}),e.jsx(Se,{definitionKey:g}),u&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]}),validateStatus:r&&l?"error":"",help:r&&l?r:null,hidden:b,children:e.jsx(Q,{placeholder:T("common.select"),mode:"multiple",open:f,showSearch:!1,onDropdownVisibleChange:p,allowClear:!0,value:m,options:S,onChange:L=>{v(L),i(L)},onBlur:n,disabled:c,getPopupContainer:L=>L.parentElement||document.body,style:{width:typeof y=="number"?`${y}px`:y||"100%"},dropdownRender:L=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{style:{padding:4,maxHeight:200,overflowY:"auto"},children:[e.jsx(ae,{prefix:e.jsx(Ds,{style:{color:"rgba(0,0,0,.25)"}}),value:h,onChange:w}),j.map(O=>e.jsx("div",{children:e.jsx(Be,{style:{marginTop:"18px"},disabled:P&&O.value!=="LAN*"||!P&&O.value==="LAN*"&&m.length>0,checked:m.includes(O.value),onChange:I=>{let U;I.target.checked?U=[...m,O.value]:U=m.filter(z=>z!==O.value),v(U),i(U)},children:O.label})},O.value))]}),e.jsx(de,{style:{margin:"8px -4px"}}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",padding:"0 0px 4px",marginLeft:"-4px"},children:[e.jsx(Z,{type:"link",size:"small",onClick:E,children:"Reset"}),e.jsx(Z,{type:"link",size:"small",onClick:C,children:"Ok"})]})]})})})};Jt.propTypes=yl;Jt.defaultProps=jl;const Cl=G.memo(Jt,Nt),Sl={name:M.string.isRequired,label:M.string.isRequired,options:M.arrayOf(M.shape({label:M.string.isRequired,value:M.string.isRequired})).isRequired,isDisabled:M.bool,isRequired:M.bool,isHidden:M.bool,emptyIsUndefined:M.bool,hasVirtualAll:M.bool,canSelectAll:M.bool,isPortal:M.bool,definitionKey:M.string},wl={isRequired:!1,isDisabled:!1,isHidden:!1,emptyIsUndefined:!1,hasVirtualAll:!1,canSelectAll:!1,isPortal:!1,definitionKey:null},Xt=({options:t,name:a,isDisabled:s,label:i,isRequired:n,isHidden:r,emptyIsUndefined:l,canSelectAll:o,hasVirtualAll:u,isPortal:c,definitionKey:b,w:x})=>{const[{value:g},{touched:y,error:T},{setValue:f,setTouched:p}]=jt(a),m=d.useCallback(h=>{h.length===0&&l?f(void 0):h.includes("*")?f(u?t.map(k=>k.value):["*"]):f(h),p(!0)},[l,u,t]),v=d.useCallback(()=>{p(!0)},[]);return e.jsx(Cl,{canSelectAll:o,label:i,value:g,onChange:m,onBlur:v,error:T,touched:y,options:t,isDisabled:s,isRequired:n,isHidden:r,isPortal:c,definitionKey:b,w:x})};Xt.propTypes=Sl;Xt.defaultProps=wl;const Tl=G.memo(Xt),Al=({label:t,value:a,onChange:s,onBlur:i,isError:n,error:r,hideButton:l,isRequired:o,element:u,isArea:c,isDisabled:b,definitionKey:x,explanation:g,placeholder:y,autoComplete:T,h:f,w:p})=>{const{t:m}=K(),[v,h]=d.useState(!1),k=e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:t}),g&&e.jsx(ue,{title:g,children:e.jsx("img",{src:gn,style:{marginLeft:3}})}),x&&e.jsx(Se,{definitionKey:x}),o&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]});if(u)return e.jsx(A.Item,{label:k,validateStatus:n?"error":"",help:n?r:"",className:"input-item-error-other",children:u});const P={value:a,onChange:s,onBlur:i,disabled:b,placeholder:y,autoComplete:T??"off"};return e.jsx(A.Item,{label:k,validateStatus:n?"error":"",help:n?r:"",className:"input-item-error-other",children:c?e.jsx(ae.TextArea,{...P,rows:4,style:{height:f}}):l?e.jsx(ae.Password,{...P,iconRender:S=>S?e.jsx(Dn,{}):e.jsx(Os,{})}):e.jsx(ae,{...P,style:{width:typeof p=="number"?`${p}px`:p||"100%"}})})},kl=G.memo(Al),Il=({name:t,isDisabled:a=!1,label:s,hideButton:i=!1,isRequired:n=!1,element:r,isArea:l=!1,emptyIsUndefined:o=!1,definitionKey:u,explanation:c,placeholder:b,autoComplete:x,...g})=>{const{value:y,error:T,isError:f,onChange:p,onBlur:m}=ne({name:t}),v=d.useCallback(h=>{o&&h.target.value.length===0?p(void 0):p(h.target.value)},[p]);return e.jsx(kl,{label:s??t,value:y,onChange:v,onBlur:m,isError:f,error:T,hideButton:i,isRequired:n,element:r,isArea:l,isDisabled:a,definitionKey:u,explanation:c,placeholder:b,autoComplete:x||"off",...g})},Le=G.memo(Il),Zt="/ampcon/wireless/configure";function El({site_id:t,port:a,mac:s,network_type:i,vlan_or_dhcp_name:n=null,vlan_tag:r=1}){return oe({url:`${Zt}/ethernet_ports`,method:"POST",data:{site_id:t,port:a,mac:s,network_type:i,vlan_or_dhcp_name:n,vlan_tag:r}})}function Pl({siteId:t,pageNum:a,pageSize:s,sortBy:i,sortType:n}){return oe({url:`${Zt}/ethernet_ports`,method:"GET",params:{siteId:t,pageNum:a,pageSize:s,sortBy:i,sortType:n}}).then(r=>r)}function Fl({id:t}){return oe({url:`${Zt}/ethernet_ports`,method:"DELETE",data:{id:t}})}const Ml=({label:t,value:a,unit:s,onChange:i,onBlur:n,error:r,isError:l,isRequired:o,hideArrows:u,element:c,isDisabled:b,w:x,min:g,max:y,definitionKey:T})=>{const f=typeof a=="string"?parseFloat(a):a,[p,m]=d.useState(!1);if(c)return e.jsx(A.Item,{label:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:t}),e.jsx(Se,{definitionKey:T}),o&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]}),required:o,validateStatus:l?"error":"",help:l?r:"",className:"input-item-error-number",children:c});const v=e.jsx(ie,{value:f,onChange:h=>{t==" "&&(h==null||h==="")?i("1"):i((h==null?void 0:h.toString())??"")},onBlur:n,disabled:b,style:{width:typeof x=="number"?`${x}px`:x||"100%"},min:g,max:y,controls:!u,onKeyPress:h=>{const k=String(f??"");if(/[0-9\-]/.test(h.key)||h.preventDefault(),h.key==="-"){const P=h.target.selectionStart??0;(k.includes("-")||P!==0)&&h.preventDefault()}},onCompositionStart:()=>m(!0),onCompositionEnd:h=>{m(!1);const k=h.currentTarget.value;/^-?\d*\.?\d*$/.test(k)||(h.currentTarget.value="",i("0"))},onInput:h=>{if(p)return;const k=h.target;/^-?\d*\.?\d*$/.test(k.value)||(k.value=k.value.replace(/[^0-9.-]/g,""))}});return e.jsx(A.Item,{label:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:t}),e.jsx(Se,{definitionKey:T}),o&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]}),validateStatus:l?"error":"",help:l?r:"",className:"input-item-error-number",children:s?e.jsx(ie,{addonAfter:s,value:f,onChange:h=>i((h==null?void 0:h.toString())??""),onBlur:n,disabled:b,style:{width:typeof x=="number"?`${x}px`:x||"100%"},min:g,max:y,controls:!u}):v})},Rl=G.memo(Ml),ba=(t,a)=>{if(a&&t==="")return;const s=parseInt(t,10);return Number.isNaN(s)?0:s},_l=({name:t,unit:a,isDisabled:s=!1,label:i,isRequired:n=!1,hideArrows:r=!1,w:l,acceptEmptyValue:o=!1,definitionKey:u,conversionFactor:c,min:b,max:x})=>{const{value:g,error:y,isError:T,onChange:f,onBlur:p}=ne({name:t}),m=d.useCallback(v=>{if(c){const h=ba(v,o),k=h!==void 0?h*c:void 0;f(k)}else f(ba(v,o))},[f]);return e.jsx(Rl,{label:i??t,value:c?Math.ceil(g/c):g,unit:a,isError:T,onChange:m,onBlur:p,error:y,hideArrows:r,isRequired:n,isDisabled:s,w:l,min:b,max:x,definitionKey:u})},be=G.memo(_l),Ul=(t,a=!1,s="")=>{const i=s==="upstream"?["WAN*"]:[],n=H().shape({"select-ports":Y().of(N()).min(1,t("form.required")).default(i),multicast:q().default(!0),learning:q().default(!0),isolate:q().default(!1),macaddr:N().test("interface.ethernet.mac.length",t("form.invalid_mac_uc"),r=>r===void 0?!0:Ka(r)).default(void 0),"reverse-path":q().default(!1),"vlan-tag":N().default("auto"),vlan:q().default(!1),vlan_id:$().nullable().when("vlan",{is:!0,then:r=>r.required(t("form.required")).moreThan(0).lessThan(4095).default(1080),otherwise:r=>r.notRequired().nullable()})});return a?n:n.nullable().default({"select-ports":i,multicast:!0,learning:!0,isolate:!1,macaddr:void 0,"reverse-path":!1,"vlan-tag":"auto","ap-assign-ip":!1,vlan:!1,vlan_id:null})},va=async t=>{try{const a=await Kt({siteId:t,pageNum:1,pageSize:100});return(a==null?void 0:a.status)===200&&Array.isArray(a.info)?a.info.map(s=>({label:s.name,value:s.name})):[]}catch{return[]}},Dl=({title:t,okText:a,isModalOpen:s,onCancel:i,role:n="",modalClass:r="",siteId:l,onSuccess:o})=>{const{t:u}=K(),{values:c,setFieldValue:b,errors:x}=ye();c.vlan;const[g,y]=d.useState(1),[T,f]=d.useState([]),[p,m]=d.useState(!1),[v,h]=d.useState(!1),[k,P]=d.useState(!1),[S,j]=d.useState(ke()),C=G.useMemo(()=>Ul(u,!1,n),[u,n]),E=C.getDefault(),w=[{value:"LAN*",label:"LAN*"},{value:"LAN1",label:"LAN1"},{value:"LAN2",label:"LAN2"},{value:"LAN3",label:"LAN3"},{value:"LAN4",label:"LAN4"}],L=async()=>{h(!1),m(!0);const B=await va(l);f(B),m(!1),P(!0)},O=async()=>{if(l&&!k){m(!0);const B=await va(l);f(B),m(!1),P(!0)}},I=B=>{B&&O()},U={auto:1,tagged:2,"un-tagged":3},z=async B=>{if(!B["select-ports"]||B["select-ports"].length===0){D.error("ports can not be empty");return}const R=JSON.stringify(B["select-ports"]),se={site_id:l,port:R,mac:B.macaddr||"",network_type:g,vlan_tag:U[B["vlan-tag"]]||1,vlan_or_dhcp_name:B["ap-assign-ip"]?B["dhcp-service-name"]:B.vlan?B.vlan_id:null},ce=await El(se);if(ce.status===200)D.success("Added successfully"),o&&o(),i(),j(ke());else{const pe=ce.info?`Failed to add: ${ce.info}`:"Failed to add.";D.error(pe)}};return e.jsx(qt,{initialValues:E,validationSchema:C,onSubmit:async(B,{setSubmitting:R})=>{try{D.success("Added successfully"),i(),j(ce=>ce+1)}catch{D.error("Network error.")}finally{R(!1)}},children:({resetForm:B,values:R,setFieldValue:se,validateForm:ce,setTouched:pe})=>{d.useEffect(()=>{R.vlan&&!R.vlan_id?se("vlan_id",1080):R.vlan||se("vlan_id",null)},[R.vlan]),d.useEffect(()=>{R.vlan?y(2):R["ap-assign-ip"]?y(3):y(1)},[R.vlan,R["ap-assign-ip"]]);const W=V=>{const re={};for(const ge in V)typeof V[ge]=="object"&&V[ge]!==null?re[ge]=W(V[ge]):re[ge]=!0;return re};return e.jsx(e.Fragment,{children:e.jsxs(Ie,{className:r||"",title:e.jsxs("div",{children:[t,e.jsx(de,{style:{marginTop:8,marginBottom:0}})]}),open:s,footer:[e.jsx(de,{style:{marginTop:0,marginBottom:20}},"divider"),e.jsx(Z,{onClick:()=>{B(),i()},children:"Cancel"},"cancel"),e.jsx(Z,{type:"primary",onClick:()=>{ce().then(V=>{Object.keys(V).length===0?z(R):(pe(W(R)),D.error("Please correct the errors before saving"))})},children:"Apply"},"ok")],onCancel:()=>{B(),i()},destroyOnClose:!0,children:[e.jsxs(Ra,{children:[e.jsx(Tl,{name:"select-ports",label:"Ports",options:w,isRequired:!0,w:280}),!R["ap-assign-ip"]&&e.jsxs(e.Fragment,{children:[e.jsx(le,{name:"vlan",label:"VLAN"}),R.vlan&&e.jsx(be,{name:"vlan_id",label:" ",w:280})]}),!R.vlan&&e.jsxs(e.Fragment,{children:[e.jsx(le,{name:"ap-assign-ip",label:"AP Assign IP"}),R["ap-assign-ip"]&&e.jsx(ft,{name:"dhcp-service-name",label:"DHCP Service Name",options:T,isRequired:!0,w:280,dropdownRender:V=>e.jsxs(e.Fragment,{children:[V,e.jsx(Z,{type:"link",icon:e.jsx(Ze,{}),style:{width:"100%",borderTop:"1px solid #E7E7E7"},onClick:()=>h(!0),children:"Create New DHCP Service"})]}),onDropdownVisibleChange:I})]}),e.jsx(Le,{name:"macaddr",label:"Mac Address",w:280,emptyIsUndefined:!0}),e.jsx(ft,{name:"vlan-tag",label:"Vlan Tag",options:[{label:"Auto",value:"auto"},{label:"Tagged",value:"tagged"},{label:"Un-tagged",value:"un-tagged"}],w:280})]}),v&&e.jsx(Xe,{isModalOpen:v,title:"Create DHCP Service",onCancel:()=>h(!1),modalClass:"ampcon-max-modal",childItems:e.jsx(Wt,{resource:void 0,onClose:L,siteId:l})})]})})}},S)},Ol=({siteId:t})=>{if(window.location.hash){const E=window.location.hash.replace("#","");/^\d+$/.test(E)&&(t=parseInt(E,10))}const{t:a}=K(),[s,i]=d.useState(!1),n=d.useRef(null),[r,l]=d.useState(!1),[o,u]=d.useState([]),[c,b]=d.useState([]),[x,g]=d.useState({index:0,limit:10}),[y,T]=d.useState([]),[f,p]=d.useState([]),[m,v]=d.useState(0),[h,k]=d.useState(!1);d.useEffect(()=>{g({index:0,limit:x.limit})},[t]),d.useEffect(()=>{t!=null&&P()},[t,x,y]);const P=async()=>{var E,w;k(!0);try{const L=(E=y==null?void 0:y[0])==null?void 0:E.id,O=(w=y==null?void 0:y[0])==null?void 0:w.sort,I=await Pl({siteId:t,pageNum:x.index+1,pageSize:x.limit,sortBy:L,sortType:O});I.status===200?(p(I.info),v(I.total)):(D.error(I.info||"Failed to fetch Ethernet port list"),p([]),v(0))}catch{D.error("Error fetching Ethernet port list"),p([]),v(0)}finally{k(!1)}},S=()=>{P()};d.useEffect(()=>{n.current&&(n.current.refreshTable=S)},[S]);const j=(E,w,L)=>{g({index:E.current-1,limit:E.pageSize}),L.field?T([{id:L.field,sort:L.order==="ascend"?"asc":"desc"}]):T([])},C=[{key:"port",title:"Ports",dataIndex:"port",sorter:!0,render:E=>JSON.parse(E).join(","),width:"15%"},{key:"mac",title:"Mac Address",dataIndex:"mac",render:E=>E==""?"--":E,width:"15%"},{key:"vlan_or_dhcp_name",title:"VLAN/DHCP Service",dataIndex:"vlan_or_dhcp_name",render:E=>E??"-",width:"15%"},{key:"vlan_tag",title:"Vlan Tag",dataIndex:"vlan_tag",render:E=>({1:"auto",2:"tagged",3:"un-tagged"})[E]||"unkown",width:"15%"},{key:"operation",title:"Operation",render:(E,w)=>e.jsx(Ue,{style:{flexWrap:"wrap",columnGap:"24px",rowGap:"5px"},className:"actionLink",children:e.jsx("a",{onClick:()=>{Ee(`This action will delete ethernet: ${w.port}, Do you want to continue?`,()=>{l(!0);try{Fl({id:w.id}).then(L=>{L.status!==200?D.error(L.info):(D.success(L.info),S())})}catch{D.error("An error occurred during the process of delete")}finally{l(!1)}})},children:"Delete"})}),width:"15%"}];return e.jsxs(e.Fragment,{children:[e.jsx(Dl,{title:"Create",onText:"Apply",isModalOpen:s,onCancel:()=>i(!1),modalClass:"ampcon-middle-modal",siteId:t,onSuccess:S}),e.jsx(Je,{size:16,style:{marginTop:"8px"},children:e.jsxs(Z,{type:"primary",block:!0,onClick:()=>i(!0),style:{marginBottom:"4px"},children:[e.jsx(Pe,{component:yt}),"Create"]})}),e.jsx(Ue,{vertical:!0,style:{position:"relative",width:"100%",marginBottom:f&&f.length>0?"8px":"24px"},children:e.jsx(Bs,{ref:n,columns:C,dataSource:f,loading:h,onChange:j,disableInternalRowSelection:!0,pagination:{current:x.index+1,pageSize:x.limit,total:m,showSizeChanger:!0,showQuickJumper:!0,showTotal:E=>`Total ${E} items`,pageSizeOptions:["10","20","50","100"]}})})]})},Ja=(t,a=!1)=>{const s=H().shape({enabled:q().default(!1),describe:N().required(t("form.required")).default(""),location:N().required(t("form.required")).default("")});return a?s:s.nullable().default(void 0)},Xa=(t,a=!1)=>{const s=H().shape({enabled:q().default(!0),port:$().required(t("form.required")).moreThan(-1).lessThan(65536).integer().default(22),"password-authentication":q().default(!0),"authorized-keys":Y().of(N()).when("password-authentication",{is:!1,then:Y().of(N()).required(t("form.required")).min(1,t("form.required")).default([]),otherwise:Y().of(N())}).default(void 0)});return a?s:s.nullable().default(void 0)},Za=(t,a=!1)=>{const s=H().shape({enabled:q().default(!1),servers:Y().of(N().required(t("form.required")).test("ntp-servers",t("form.invalid_domain_or_ip"),async i=>{if(!i)return!1;try{return await ht(i),!0}catch{return!1}})).required(t("form.required")).min(1,t("form.required")).default([]),"local-server":q().default(!1)});return a?s:s.nullable().default(void 0)},Ya=(t,a=!1)=>{const s=H().shape({enable:q().default(!1)});return a?s:s.nullable().default(void 0)},es=(t,a=!1)=>{const s=H().shape({enabled:q().default(!1),host:N().required(t("form.required")).test("rtty.host.value",t("form.invalid_fqdn_host"),Mr).default(""),port:$().required(t("form.required")).moreThan(-1).lessThan(65536).integer().default(5912),token:N().required(t("form.required")).test("rtty.token.length",t("form.must_string",{max:32}),i=>Rr({val:i,min:32,max:32})).default("")});return a?s:s.nullable().default(void 0)},ts=(t,a=!1)=>{const s=H().shape({enabled:q().default(!1),host:N().required(t("form.required")).test("log.host.value",t("form.invalid_cidr"),_t).default(""),port:$().required(t("form.required")).moreThan(-1).lessThan(65536).integer().default(5912),proto:N().required(t("form.required")).default("udp"),size:$().required(t("form.required")).moreThan(31).lessThan(65536).integer().default(1e3)});return a?s:s.nullable().default(void 0)},as=(t,a=!1)=>{const s=H().shape({enabled:q().default(!0),"http-port":$().required(t("form.required")).moreThan(0).lessThan(65536).integer().default(80)});return a?s:s.nullable().default(void 0)},ss=(t,a=!1)=>{const s=H().shape({enable:q().default(!1)});return a?s:s.nullable().default(void 0)},ns=(t,a=!1)=>{const s=H().shape({enabled:q().default(!1),"ping-hosts":Y().of(N().required(t("form.required")).test("online-check-servers",t("form.invalid_domain_or_ip"),async i=>{if(!i)return!1;try{return await ht(i),!0}catch{return!1}})).required(t("form.required")).min(1,t("form.required")).default([]),"download-hosts":Y().of(N()).required(t("form.required")).min(1,t("form.required")).default([]),"check-interval":$().required(t("form.required")).moreThan(-1).lessThan(65536).integer().default(60),"check-threshold":$().required(t("form.required")).moreThan(-1).lessThan(65536).integer().default(1),action:Y().of(N()).required(t("form.required")).min(1,t("form.required")).default([])});return a?s:s.nullable().default(void 0)},is=(t,a=!1)=>{const s=H().shape({mode:N().required(t("form.required")).default("local"),"assoc-steering":q().default(!1),"auto-channel":q().default(!1),"required-snr":$().required(t("form.required")).integer().default(0),"required-probe-snr":$().required(t("form.required")).moreThan(-1).lessThan(101).integer().default(0),"required-roam-snr":$().required(t("form.required")).moreThan(-1).lessThan(101).integer().default(0),"load-kick-threshold":$().required(t("form.required")).moreThan(-1).lessThan(101).integer().default(75)});return a?s:s.nullable().default(void 0)},Bl=(t,a=!1)=>H().shape({configuration:H().shape({services:H().shape({lldp:Ja(t,a),ssh:Xa(t,a),ntp:Za(t,a),http:as(t,a),mdns:Ya(t,a),rtty:es(t,a),log:ts(t,a),igmp:ss(t,a),"online-check":ns(t,a),"wifi-steering":is(t,a)})})}),Me=(t,a)=>{switch(a){case"lldp":return Ja(t,!0).cast();case"ssh":return Xa(t,!0).cast();case"ntp":return Za(t,!0).cast();case"mdns":return Ya(t,!0).cast();case"rtty":return es(t,!0).cast();case"log":return ts(t,!0).cast();case"http":return as(t,!0).cast();case"igmp":return ss(t,!0).cast();case"online-check":return ns(t,!0).cast();case"wifi-steering":return is(t,!0).cast();default:return null}},Nl=()=>{const{value:t}=ne({name:"configuration.services.http.enabled"}),{value:a}=ne({name:"configuration.services.http"}),{t:s}=K(),{values:i,setFieldValue:n,errors:r}=ye();return d.useEffect(()=>{if(t){if(!a||a&&Object.keys(a).length<=1&&a.enabled===!0){const l=Me(s,"http");n("configuration.services.http",{...l})}}else n("configuration.services.http",void 0)},[t]),e.jsx(e.Fragment,{children:e.jsx(_,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.http.enabled",label:"HTTP"}),t&&e.jsx(be,{name:"configuration.services.http.http-port",label:"HTTP-Port",definitionKey:"service.http.http-port",isRequired:!0,w:140})]})})})},Ll=G.memo(Nl),ql={value:M.arrayOf(M.string),label:M.string.isRequired,onChange:M.func.isRequired,onBlur:M.func.isRequired,error:M.oneOfType([M.string,M.bool]),touched:M.bool,isDisabled:M.bool,isRequired:M.bool,isHidden:M.bool,definitionKey:M.string,placeholder:M.string,width:M.oneOfType([M.string,M.number])},zl={value:[],error:!1,touched:!1,isRequired:!1,isDisabled:!1,isHidden:!1,definitionKey:null,placeholder:""},Yt=({label:t,value:a,onChange:s,onBlur:i,error:n,touched:r,isRequired:l,isDisabled:o,isHidden:u,definitionKey:c,placeholder:b,w:x})=>{const{t:g}=K(),[y,T]=d.useState(""),f=a||[],p=h=>{T(h)},m=h=>{s(h),T("")},v=[...f.map(h=>({label:h,value:h})),...y&&!f.includes(y)?[{label:`Create "${y}"`,value:y}]:[]];return e.jsx(A.Item,{label:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:t}),e.jsx(Se,{definitionKey:c}),l&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]}),hidden:u,validateStatus:n&&r?"error":"",help:r&&n?n:null,className:"input-item-error-other",children:e.jsx(Q,{mode:"tags",style:{width:typeof x=="number"?`${x}px`:x||"100%"},placeholder:b,value:f,onChange:m,onBlur:i,disabled:o,options:v,onSearch:p,showSearch:!0,notFoundContent:y?null:g("common.type_for_options"),tagRender:h=>{const{label:k,closable:P,onClose:S}=h,j=String(k);return e.jsxs("span",{style:{display:"inline-flex",alignItems:"center",maxWidth:250,background:"#f0f0f0",borderRadius:4,padding:"0 6px",marginRight:4},children:[e.jsx(ue,{title:j,overlayStyle:{maxWidth:400},children:e.jsx("span",{style:{flex:1,minWidth:0,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:j})}),P&&e.jsx(Ns,{onClick:S,style:{marginLeft:6,fontSize:12,color:"rgba(0,0,0,.45)",cursor:"pointer",flexShrink:0}})]})}})})};Yt.propTypes=ql;Yt.defaultProps=zl;const Vl=G.memo(Yt),Ql={name:M.string.isRequired,label:M.string.isRequired,isDisabled:M.bool,isRequired:M.bool,isHidden:M.bool,emptyIsUndefined:M.bool,placeholder:M.string,definitionKey:M.string},Gl={isRequired:!1,isDisabled:!1,isHidden:!1,emptyIsUndefined:!1,placeholder:"",definitionKey:null},ea=({name:t,isDisabled:a,label:s,isRequired:i,isHidden:n,emptyIsUndefined:r,placeholder:l,definitionKey:o,w:u})=>{const[{value:c},{touched:b,error:x},{setValue:g,setTouched:y}]=jt(t),T=d.useCallback(p=>{r&&p.length===0?g(void 0):g(p),y(!0)},[r]),f=d.useCallback(()=>{y(!0)},[]);return e.jsx(Vl,{label:s,value:c,onChange:T,onBlur:f,error:x,touched:b,placeholder:l,isDisabled:a,isRequired:i,isHidden:n,definitionKey:o,w:u})};ea.propTypes=Ql;ea.defaultProps=Gl;const mt=G.memo(ea),Hl=()=>{const{value:t}=ne({name:"configuration.services.ssh.enabled"}),{value:a}=ne({name:"configuration.services.ssh"}),{value:s}=ne({name:"configuration.services.ssh.password-authentication"}),{onChange:i,onBlur:n}=ne({name:"configuration.services.ssh.authorized-keys"}),{t:r}=K(),{values:l,setFieldValue:o,errors:u}=ye();d.useEffect(()=>{if(t){if(!a||a&&Object.keys(a).length<=1&&a.enabled===!0){const b=Me(r,"ssh");o("configuration.services.ssh",{...b,enabled:!0})}}else o("configuration.services.ssh",void 0)},[t]);const c=d.useCallback(b=>{b?(i(void 0),setTimeout(()=>{n()},200)):(i([]),setTimeout(()=>{n()},200))},[]);return e.jsx(_,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.ssh.enabled",label:"SSH"}),t&&e.jsxs(e.Fragment,{children:[e.jsx(be,{name:"configuration.services.ssh.port",label:"Port",definitionKey:"service.ssh.port",isRequired:!0,w:140}),e.jsx(le,{name:"configuration.services.ssh.password-authentication",label:"Password-Authentication",definitionKey:"service.ssh.password-authentication",onChangeCallback:c,isRequired:!0,defaultValue:!0}),s!==void 0&&!s&&e.jsx(mt,{name:"configuration.services.ssh.authorized-keys",label:"authorized-keys",definitionKey:"service.ssh.authorized-keys",w:280,isRequired:!0})]})]})})},$l=G.memo(Hl),Kl=()=>e.jsx(_,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsx(F,{children:e.jsx(le,{name:"configuration.services.mdns.enable",definitionKey:"service.mdns.enable",label:"MDNS"})})}),Wl=G.memo(Kl),Jl=()=>e.jsx(_,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsx(F,{children:e.jsx(le,{name:"configuration.services.igmp.enable",label:"IGMP",definitionKey:"service.igmp.enable"})})}),Xl=G.memo(Jl),Zl=()=>{const{value:t}=ne({name:"configuration.services.lldp.enabled"}),{value:a}=ne({name:"configuration.services.lldp"}),{t:s}=K(),{values:i,setFieldValue:n,errors:r}=ye();return d.useEffect(()=>{if(t){if(!a||a&&Object.keys(a).length<=1&&a.enabled===!0){const l=Me(s,"lldp");n("configuration.services.lldp",{...l,describe:"auto",location:"auto",enabled:!0})}}else n("configuration.services.lldp",void 0)},[t]),e.jsx(e.Fragment,{children:e.jsx(_,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.lldp.enabled",label:"LLDP"}),t&&e.jsxs(e.Fragment,{children:[e.jsx(Le,{name:"configuration.services.lldp.describe",label:"Describe",definitionKey:"service.lldp.describe",isRequired:!0,w:280}),e.jsx(Le,{name:"configuration.services.lldp.location",label:"Location",definitionKey:"service.lldp.location",isRequired:!0,w:280})]})]})})})},Yl=G.memo(Zl),eo=()=>{const{value:t}=ne({name:"configuration.services.ntp.enabled"}),{value:a}=ne({name:"configuration.services.ntp"}),{t:s}=K(),{values:i,setFieldValue:n,errors:r}=ye();return d.useEffect(()=>{if(t){if(!a||a&&Object.keys(a).length<=1&&a.enabled===!0){const l=Me(s,"ntp");n("configuration.services.ntp",{...l,enabled:!0})}}else n("configuration.services.ntp",void 0)},[t]),e.jsx(_,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.ntp.enabled",label:"NTP"}),t&&e.jsxs(e.Fragment,{children:[e.jsx(mt,{name:"configuration.services.ntp.servers",label:"Servers",definitionKey:"service.ntp.servers",isRequired:!0,w:280}),e.jsx(le,{name:"configuration.services.ntp.local-server",label:"Local-Server",definitionKey:"service.ntp.local-server",isRequired:!0})]})]})})},to=G.memo(eo),ao=()=>{const{value:t}=ne({name:"configuration.services.log.enabled"}),{value:a}=ne({name:"configuration.services.log"}),{t:s}=K(),{values:i,setFieldValue:n,errors:r}=ye();return d.useEffect(()=>{if(t){if(!a||a&&Object.keys(a).length<=1&&a.enabled===!0){const l=Me(s,"log");n("configuration.services.log",{...l,enabled:!0})}}else n("configuration.services.log",void 0)},[t]),e.jsx(_,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.log.enabled",label:"Log"}),t&&e.jsxs(e.Fragment,{children:[e.jsx(Le,{name:"configuration.services.log.host",label:"Host",definitionKey:"service.log.host",isRequired:!0,w:280}),e.jsx(be,{name:"configuration.services.log.port",label:"Port",definitionKey:"service.log.port",isRequired:!0,w:140}),e.jsx(ft,{name:"configuration.services.log.proto",label:"Proto",definitionKey:"service.log.proto",isRequired:!0,w:280,options:[{value:"udp",label:"udp"},{value:"tcp",label:"tcp"}]}),e.jsx(be,{name:"configuration.services.log.size",label:"Size",definitionKey:"service.log.size",isRequired:!0,w:140})]})]})})},so=G.memo(ao),no=()=>{const{value:t}=ne({name:"configuration.services.rtty.enabled"}),{value:a}=ne({name:"configuration.services.rtty"}),{t:s}=K(),{values:i,setFieldValue:n,errors:r}=ye();return console.log("rtty values",i),d.useEffect(()=>{if(t){if(!a||a&&Object.keys(a).length<=1&&a.enabled===!0){const l=Me(s,"rtty");n("configuration.services.rtty",{...l,enabled:!0})}}else n("configuration.services.rtty",void 0)},[t]),e.jsx(_,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.rtty.enabled",label:"RTTY"}),t&&e.jsxs(e.Fragment,{children:[e.jsx(Le,{name:"configuration.services.rtty.host",label:"Host",definitionKey:"service.rtty.host",isRequired:!0,w:280}),e.jsx(be,{name:"configuration.services.rtty.port",label:"Port",definitionKey:"service.rtty.port",isRequired:!0,w:140}),e.jsx(Le,{name:"configuration.services.rtty.token",label:"Token",definitionKey:"service.rtty.token",isRequired:!0,w:280})]})]})})},io=G.memo(no),ro=()=>{const{value:t}=ne({name:"configuration.services.wifi-steering.enabled"}),{value:a}=ne({name:"configuration.services.wifi-steering"}),{t:s}=K(),{values:i,setFieldValue:n,errors:r}=ye();return d.useEffect(()=>{if(t){if(!a||a&&Object.keys(a).length<=1&&a.enabled===!0){const l=Me(s,"wifi-steering");n("configuration.services.wifi-steering",{...l,enabled:!0})}}else n("configuration.services.wifi-steering",void 0)},[t]),e.jsx(_,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.wifi-steering.enabled",label:"Wi-Fi Steering"}),t&&e.jsxs(e.Fragment,{children:[e.jsx(le,{name:"configuration.services.wifi-steering.assoc-steering",label:"Assoc-Steering",definitionKey:"service.wifi-steering.assoc-steering",isRequired:!0}),e.jsx(le,{name:"configuration.services.wifi-steering.auto-channel",label:"Auto-Channel",definitionKey:"service.wifi-steering.auto-channel",isRequired:!0}),e.jsx(be,{name:"configuration.services.wifi-steering.required-probe-snr",label:"Required-Probe-Snr",definitionKey:"service.wifi-steering.required-probe-snr",isRequired:!0,w:140}),e.jsx(be,{name:"configuration.services.wifi-steering.required-roam-snr",label:"Required-Roam-Snr",definitionKey:"service.wifi-steering.required-roam-snr",isRequired:!0,w:140}),e.jsx(be,{name:"configuration.services.wifi-steering.load-kick-threshold",label:"load-kick-threshold",definitionKey:"service.wifi-steering.load-kick-threshold",isRequired:!0,w:140})]})]})})},lo=G.memo(ro),oo={value:M.arrayOf(M.oneOfType([M.string,M.number])),label:M.string.isRequired,onChange:M.func.isRequired,options:M.arrayOf(M.shape({label:M.string.isRequired,value:M.oneOfType([M.string,M.number]).isRequired})).isRequired,onBlur:M.func.isRequired,error:M.oneOfType([M.string,M.bool]),touched:M.bool,isDisabled:M.bool,canSelectAll:M.bool,isRequired:M.bool,isHidden:M.bool,isPortal:M.bool.isRequired,definitionKey:M.string,w:M.oneOfType([M.string,M.number]),placeholder:M.string},rs=({options:t,label:a,value:s=[],onChange:i,onBlur:n,error:r=!1,touched:l=!1,canSelectAll:o=!1,isRequired:u=!1,isDisabled:c=!1,isHidden:b=!1,isPortal:x=!1,definitionKey:g=null,w:y,placeholder:T})=>{const{t:f}=K(),p=o?[{value:"*",label:f("common.all")},...t]:t;return e.jsx(A.Item,{label:!b&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:a}),e.jsx(Se,{definitionKey:g}),u&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]}),validateStatus:r&&l?"error":"",help:r&&l?r:null,hidden:b,children:e.jsx(Q,{mode:"multiple",allowClear:!0,placeholder:T,value:s??[],options:p,onChange:i,onBlur:n,disabled:c,getPopupContainer:m=>m.parentElement||document.body,style:{width:typeof y=="number"?`${y}px`:y||"100%"}})})};rs.propTypes=oo;const co=G.memo(rs,Nt),uo={name:M.string.isRequired,label:M.string.isRequired,options:M.arrayOf(M.shape({label:M.string.isRequired,value:M.string.isRequired})).isRequired,isDisabled:M.bool,isRequired:M.bool,isHidden:M.bool,emptyIsUndefined:M.bool,hasVirtualAll:M.bool,canSelectAll:M.bool,isPortal:M.bool,definitionKey:M.string,placeholder:M.string},ls=({options:t,name:a,isDisabled:s=!1,label:i,isRequired:n=!1,isHidden:r=!1,emptyIsUndefined:l=!1,canSelectAll:o=!1,hasVirtualAll:u=!1,isPortal:c=!1,definitionKey:b=null,w:x,placeholder:g})=>{const[{value:y},{touched:T,error:f},{setValue:p,setTouched:m}]=jt(a),v=d.useCallback(k=>{k.length===0&&l?p(void 0):k.includes("*")?p(u?t.map(P=>P.value):["*"]):p(k),m(!0)},[l,u,t]),h=d.useCallback(()=>{m(!0)},[]);return e.jsx(co,{canSelectAll:o,label:i,value:y,onChange:v,onBlur:h,error:f,touched:T,options:t,isDisabled:s,isRequired:n,isHidden:r,isPortal:c,definitionKey:b,w:x,placeholder:g})};ls.propTypes=uo;const ho=G.memo(ls),fo=()=>{const{value:t}=ne({name:"configuration.services.online-check.enabled"}),{t:a}=K(),{value:s}=ne({name:"configuration.services.online-check"}),{values:i,setFieldValue:n,errors:r}=ye();return d.useEffect(()=>{if(t){if(!s||s&&Object.keys(s).length<=1&&s.enabled===!0){const l=Me(a,"online-check");n("configuration.services.online-check",{...l,enabled:!0})}}else n("configuration.services.online-check",void 0)},[t]),e.jsx(_,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.online-check.enabled",label:"Online Check"}),t&&e.jsxs(e.Fragment,{children:[e.jsx(ho,{name:"configuration.services.online-check.action",label:"Action",definitionKey:"service.online-check.action",w:280,isRequired:!0,placeholder:a("common.select"),options:[{value:"wifi",label:"wifi"},{value:"leds",label:"leds"}]}),e.jsx(mt,{name:"configuration.services.online-check.ping-hosts",label:"Ping-Hosts",definitionKey:"service.online-check.ping-hosts",isRequired:!0,w:280}),e.jsx(mt,{name:"configuration.services.online-check.download-hosts",label:"Download-Hosts",definitionKey:"service.online-check.download-hosts",isRequired:!0,w:280}),e.jsx(be,{name:"configuration.services.online-check.check-interval",label:"Check-Interval",definitionKey:"service.online-check.check-interval",isRequired:!0,w:140}),e.jsx(be,{name:"configuration.services.online-check.check-threshold",label:"Check-Threshold",definitionKey:"service.online-check.check-threshold",isRequired:!0,w:140})]})]})})},mo=G.memo(fo),po=()=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{style:{display:"flex"},children:[e.jsx($l,{}),e.jsx(de,{type:"vertical",style:{height:"auto",margin:"0 90px"}}),e.jsx(Ll,{})]}),e.jsxs("div",{style:{display:"flex"},children:[e.jsx(Wl,{}),e.jsx(de,{type:"vertical",style:{height:"auto",margin:"0 90px"}}),e.jsx(Xl,{})]}),e.jsxs("div",{style:{display:"flex"},children:[e.jsx(Yl,{}),e.jsx(de,{type:"vertical",style:{height:"auto",margin:"0 90px"}}),e.jsx(to,{})]}),e.jsxs("div",{style:{display:"flex"},children:[e.jsx(so,{}),e.jsx(de,{type:"vertical",style:{height:"auto",margin:"0 90px"}}),e.jsx(io,{})]}),e.jsxs("div",{style:{display:"flex"},children:[e.jsx(lo,{}),e.jsx(de,{type:"vertical",style:{height:"auto",margin:"0 90px"}}),e.jsx(mo,{})]})]}),go=()=>{const{value:t}=ne({name:"configuration['IP address'].vlan.enabled"}),{value:a}=ne({name:"configuration['IP address'].vlan"}),{value:s}=ne({name:"configuration['IP address'].ipv6.enabled"}),{value:i}=ne({name:"configuration['IP address'].ipv6"}),{values:n,setFieldValue:r,errors:l}=ye();return d.useEffect(()=>{t?((!a||a&&Object.keys(a).length<=1&&a.enabled===!0)&&r("configuration['IP address'].vlan.id",1080),r("configuration['IP address'].vlan.enabled",!0)):r("configuration['IP address'].vlan",void 0)},[t]),d.useEffect(()=>{s?((!i||i&&Object.keys(i).length<=1&&i.enabled===!0)&&r("configuration['IP address'].ipv6.addressing","dynamic"),r("configuration['IP address'].ipv6.enabled",!0)):r("configuration['IP address'].ipv6",void 0)},[s]),e.jsxs(e.Fragment,{children:[e.jsxs("div",{style:{display:"flex"},children:[e.jsx(_,{gutter:[20,0],style:{marginBottom:0,marginTop:0,width:"100%"},children:e.jsx(F,{children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:"IPv4"}),e.jsx(Ce,{style:{marginLeft:"160px"},name:"configuration['IP address'].ipv4.addressing",defaultChecked:!0,disabled:!1,children:"Dynamic"})]})})}),e.jsx(_,{gutter:[20,20],style:{marginBottom:0,marginTop:-6,width:"100%"},children:e.jsx(F,{children:e.jsx(le,{name:"configuration['IP address'].ipv6.enabled",label:"IPv6",definitionKey:"interface.ipv6.port-forward.protocol"})})})]}),e.jsx(le,{name:"configuration['IP address'].vlan.enabled",label:"VLAN",definitionKey:"interface.vlan"}),t&&e.jsx(be,{name:"configuration['IP address'].vlan.id",label:" ",w:140})]})},os=(t,a=!1)=>a?H().shape({enabled:q().default(!1),interval:$().required(t("form.required")).moreThan(59).lessThan(1e3).default(60)}):H().shape({enabled:q().default(!1),interval:$().required(t("form.required")).moreThan(59).lessThan(1e3).default(60)}).nullable().default(void 0),ds=(t,a=!1)=>a?H().shape({enabled:q().default(!1),types:Y().of(N()).required(t("form.required")).min(1,t("form.required")).default([])}):H().shape({enabled:q().default(!1),types:Y().of(N()).required(t("form.required")).min(1,t("form.required")).default([])}).nullable().default(void 0),cs=(t,a=!1)=>a?H().shape({enabled:q().default(!1),interval:$().required(t("form.required")).moreThan(59).lessThan(1e3).default(60),types:Y().of(N()).required(t("form.required")).min(1,t("form.required")).default([])}):H().shape({enabled:q().default(!1),interval:$().required(t("form.required")).moreThan(59).lessThan(1e3).default(60),types:Y().of(N()).required(t("form.required")).min(1,t("form.required")).default([])}).nullable().default(void 0),us=(t,a=!1)=>a?H().shape({enabled:q().default(!0),interval:$().required(t("form.required")).moreThan(59).lessThan(1e3).default(60),types:Y().of(N()).required(t("form.required")).min(1,t("form.required")).default(["ssids","lldp","clients"])}):H().shape({enabled:q().default(!0),interval:$().required(t("form.required")).moreThan(59).lessThan(1e3).default(60),types:Y().of(N()).required(t("form.required")).min(1,t("form.required")).default(["ssids","lldp","clients"])}).nullable().default(void 0),hs=(t,a=!1)=>a?H().shape({enabled:q().default(!1),interval:$().required(t("form.required")).moreThan(59).lessThan(1e3).default(60),"dhcp-local":q().default(!0),"dhcp-remote":q().default(!1),"dns-local":q().default(!0),"dns-remote":q().default(!0)}):H().shape({interval:$().required(t("form.required")).moreThan(59).lessThan(1e3).default(60),"dhcp-local":q().default(!0),"dhcp-remote":q().default(!1),"dns-local":q().default(!0),"dns-remote":q().default(!0)}).nullable().default(void 0),fs=(t,a=!1)=>a?H().shape({enabled:q().default(!1),filters:Y().of(N()).required(t("form.required")).min(1,t("form.required")).default([])}):H().shape({enabled:q().default(!1),filters:Y().of(N()).required(t("form.required")).min(1,t("form.required")).default([])}).nullable().default(void 0),ms=(t,a=!1)=>a?H().shape({enabled:q().default(!1),filters:Y().of(N()).required(t("form.required")).min(1,t("form.required")).default([])}):H().shape({enabled:q().default(!1),filters:Y().of(N()).required(t("form.required")).min(1,t("form.required")).default([])}).nullable().default(void 0),xo=(t,a=!1)=>H().shape({configuration:H().shape({metrics:H().shape({statistics:us(t,a),health:hs(t,a),"wifi-frames":fs(t,a),"dhcp-snooping":ms(t,a),realtime:ds(t,a),telemetry:cs(t,a),"wifi-scan":os(t,a)}).default(()=>({statistics:bo(t,"statistics")}))})}),bo=(t,a)=>{switch(a){case"statistics":return us(t,!0).cast();case"health":return hs(t,!0).cast();case"wifi-frames":return fs(t,!0).cast();case"dhcp-snooping":return ms(t,!0).cast();case"telemetry":return cs(t,!0).cast();case"realtime":return ds(t,!0).cast();case"wifi-scan":return os(t,!0).cast();default:return null}},vo=t=>H().shape({configuration:H().shape({unit:yo()})}),yo=t=>H().shape({hostname_enable:q().default(!1),timezone:N().default(void 0),"leds-active":q().default(!0),"random-password":q().default(!1)}),jo=(t,a=!1,s="")=>H().shape({configuration:H().shape({"IP address":Co(t,a)})}),Co=(t,a=!1,s="")=>{const i=H().shape({ipv4:H().shape({addressing:N()}).default({addressing:"dynamic"}),ipv6:H().shape({enabled:q().default(!1),addressing:N().default("dynamic")}),vlan:H().shape({enabled:q().default(!1),id:$().required(t("form.required")).moreThan(0).lessThan(4095).default(1080)}).nullable()});return a?i:i.nullable().default({vlan:!1})},So=d.forwardRef(({onDirtyChange:t},a)=>{const{t:s}=K(),[i,n]=d.useState(null),r=d.useRef(null),[l,o]=d.useState(!1);d.useEffect(()=>{const j=location.hash.replace("#",""),C=Number(j);C!==i&&(n(C),o(!1),v({}),r.current&&r.current.resetForm())},[location.hash]);const u=["system","ethernet","services","metrics","DHCP Service","IP address"],[c,b]=d.useState([]),[x,g]=d.useState(null),y=i!=null?`${i}-advance`:void 0,{data:T=[],refetch:f,isLoading:p}=$a({id:y}),[m,v]=d.useState({}),h=d.useMemo(()=>!x||!r.current?!1:l||Object.values(m).some(j=>j),[l,m,x]);d.useEffect(()=>{t&&t(h)},[h,t]),d.useEffect(()=>{i&&f()},[i,f]),d.useEffect(()=>{o(!1),v({})},[i]),d.useEffect(()=>{b(u)},[]),d.useEffect(()=>{const j=T==null?void 0:T.configuration;if(!j||!Array.isArray(j))return;const C={configuration:{}};for(const E of j){const w=JSON.parse(E.configuration||"{}");E.name==="System"&&w.unit&&(C.configuration.unit=w.unit),E.name==="Services"&&w.services&&(C.configuration.services=w.services,["ssh","http","lldp","ntp","log","rtty","wifi-steering","online-check"].forEach(O=>{var I;((I=C.configuration.services)==null?void 0:I[O])!==void 0&&(C.configuration.services[O]={...C.configuration.services[O],enabled:!0})})),E.name==="Metrics"&&w.metrics&&(C.configuration.metrics=w.metrics),E.name==="IP address"&&w["IP address"]&&(C.configuration["IP address"]=w["IP address"],["vlan","ipv6"].forEach(O=>{var I;((I=C.configuration["IP address"])==null?void 0:I[O])!==void 0&&(C.configuration["IP address"][O]={...C.configuration["IP address"][O],enabled:!0})}))}g(C),r.current&&r.current.resetForm({values:C}),o(!1),v({})},[T==null?void 0:T.configuration]);const k=d.useMemo(()=>{let j=vo();return j=j.concat(Bl(s)).concat(xo(s)).concat(jo(s)),j},[s]);if(!x)return null;const P=[{key:"1",label:e.jsx("span",{className:"collapse-title",children:"System"}),children:e.jsx(bl,{editing:!0})},{key:"2",label:e.jsxs("div",{children:[e.jsx("span",{className:"collapse-title",children:"IP Address"}),e.jsx(Se,{definitionKey:"interface.ipAddress"})]}),children:e.jsx(go,{})},{key:"3",label:e.jsxs("div",{children:[e.jsx("span",{className:"collapse-title",children:"DHCP Service"}),e.jsx(Se,{definitionKey:"interface.DhcpService"})]}),children:e.jsx(vl,{})},{key:"4",label:e.jsx("span",{className:"collapse-title",children:"Manage Ethernet Ports"}),children:e.jsx(Ol,{})},{key:"5",label:e.jsx("span",{className:"collapse-title",children:"Services"}),children:e.jsx(po,{})}].filter(j=>j.key==="1"?c.includes("system"):j.key==="2"?c.includes("IP address"):j.key==="3"?c.includes("DHCP Service"):j.key==="4"?c.includes("ethernet"):j.key==="5"?c.includes("services"):j.key==="6"?c.includes("metrics"):!0),S=async j=>{const C=[],E=U=>{if(Array.isArray(U))return U.map(E);if(typeof U=="object"&&U!==null){if(Object.keys(U).length===1&&U.hasOwnProperty("enable")&&(U.enable===!1||U.enable==="false"))return;const z={};for(const B in U){if(B==="enabled")continue;const R=E(U[B]);R!==void 0&&(z[B]=R)}return z}return U},w=E(j.configuration.services),L=E(j.configuration.metrics),O=E(j.configuration["IP address"]);c.includes("services")&&j.configuration.services&&C.push({configuration:JSON.stringify({services:w}),name:"Services"}),c.includes("metrics")&&j.configuration.metrics&&C.push({configuration:JSON.stringify({metrics:L}),name:"Metrics"}),c.includes("system")&&j.configuration.unit&&C.push({configuration:JSON.stringify({unit:j.configuration.unit}),name:"System"}),c.includes("IP address")&&j.configuration["IP address"]&&C.push({configuration:JSON.stringify({"IP address":O}),name:"IP address"});try{await Ha(y,i,JSON.stringify(C)),D.success("Successfully applied advance configuration"),o(!1),v({})}catch(U){console.error(U),D.error("Failed to apply settings.");return}(await f()).error&&D.warning("Settings saved, but failed to refresh config.")};return e.jsx("div",{style:{width:"100%",overflowX:"auto"},children:e.jsx(qt,{innerRef:r,initialValues:x,enableReinitialize:!0,onSubmit:()=>{},validationSchema:k,children:({resetForm:j,values:C,validateForm:E,setTouched:w,dirty:L})=>{d.useEffect(()=>{r.current&&x&&!Ls.isEqual(C,x)&&o(!0)},[C,x]);const O=I=>{const U={};for(const z in I)typeof I[z]=="object"&&I[z]!==null?U[z]=O(I[z]):U[z]=!0;return U};return d.useImperativeHandle(a,()=>({reset:()=>{j(),o(!1),v({}),D.info("Changes have been reset.")},apply:()=>{E().then(I=>{Object.keys(I).length===0?(S(C),v({})):(w(O(C)),D.error("Please correct the errors before saving"))})}})),e.jsx(e.Fragment,{children:e.jsx(Re,{size:"large",items:P,defaultActiveKey:[],expandIconPosition:"right",className:"no-collapse-border",style:{marginTop:12,marginBottom:0,border:"none",background:"#ffffff"}})})}})})});function wo(t){const a=qs(t),s=d.useRef(!1);d.useEffect(()=>{a.state==="blocked"&&!s.current&&(s.current=!0,Ee("You have unsaved changes. If you leave or switch to another site, your changes will be lost. Do you want to continue?",()=>{a.proceed(),s.current=!1},()=>{a.reset(),s.current=!1}))},[a])}const ct="configurepage.tabIndex",ya=()=>{const t=localStorage.getItem(ct);try{if(t){const a=parseInt(t,10);if(a>=0&&a<=2)return a}return 0}catch{return 0}},To=({id:t})=>{const[a,s]=d.useState(ya()),{colorMode:i}=zs(),r={textColor:i==="light"?"var(--chakra-colors-blue-600)":"var(--chakra-colors-blue-300)",fontWeight:"semibold",borderWidth:"0px",marginBottom:"-1px",borderBottom:"2px solid"},[l,o]=d.useState(!1),[u,c]=d.useState(!1),b=d.useRef(null),x=d.useRef(null),g=d.useRef(null);wo(l||u);const T=m=>{m!==a&&(l?(g.current=m,Ee("You have unsaved changes. If you leave or switch to another site, your changes will be lost. Do you want to continue?",()=>{var v;g.current!==null&&((v=b.current)==null||v.reset(),console.log("radios reset"),s(g.current),localStorage.setItem(ct,g.current.toString()),g.current=null)},()=>{g.current=null})):u?(g.current=m,Ee("You have unsaved changes. If you leave or switch to another site, your changes will be lost. Do you want to continue?",()=>{var v;g.current!==null&&((v=x.current)==null||v.reset(),console.log("advance reset"),s(g.current),localStorage.setItem(ct,g.current.toString()),g.current=null)},()=>{g.current=null})):(s(m),localStorage.setItem(ct,m.toString())))};d.useEffect(()=>{s(ya())},[t]);const f=a===1||a===2,p=a===1&&!l||a===2&&!u;return e.jsxs(pn,{direction:"column",minH:"100%",children:[e.jsx(aa,{flex:"1",children:e.jsxs(La,{index:a,isManual:!0,children:[e.jsxs(qa,{children:[e.jsx(ot,{_selected:r,onClick:()=>T(0),children:"SSID Configuration"}),e.jsx(ot,{_selected:r,onClick:()=>T(1),children:"Radio Configuration"}),e.jsx(ot,{_selected:r,onClick:()=>T(2),children:"Advanced Configuration"})]}),e.jsxs(za,{children:[e.jsx(dt,{px:0,children:e.jsx(ol,{})}),e.jsx(dt,{px:0,children:e.jsx(Fr,{ref:b,onDirtyChange:o})}),e.jsx(dt,{px:0,children:e.jsx(So,{ref:x,onDirtyChange:c})})]})]})}),f&&e.jsxs(aa,{position:"sticky",bottom:0,bg:"white",py:4,display:"flex",justifyContent:"flex-end",zIndex:1,sx:{_before:{content:'""',position:"absolute",top:0,left:"50%",transform:"translateX(-50%)",width:"calc(100% + 48px)",height:"1px",backgroundColor:"#E2E8F0"}},children:[e.jsx(Z,{onClick:()=>{var m,v;a===1&&((m=b.current)==null||m.reset()),a===2&&((v=x.current)==null||v.reset())},style:{marginRight:16,width:100},disabled:p,children:"Cancel"}),e.jsx(Z,{onClick:()=>{var m,v;a===1&&((m=b.current)==null||m.apply()),a===2&&((v=x.current)==null||v.apply())},type:"primary",style:{width:100},disabled:p,children:"Apply"})]})]})},Ao=()=>e.jsx(To,{id:"1"}),ko=({visible:t,onClose:a,config:s,onApply:i,loading:n=!1})=>{const[r]=A.useForm();d.useEffect(()=>{t&&(s.enableChannelOptimization=s.channelMode==="unmanaged_aware",s.enableTxPowerOptimization=s.txPowerMode==="measure_ap_ap",(s.coverageThreshold===void 0||s.coverageThreshold===null)&&(s.coverageThreshold=-70),r.setFieldsValue(s))},[t,s,r]);const l=async u=>{const c={...u};if(c.enableChannelOptimization&&(c.channelMode="unmanaged_aware"),c.enableTxPowerOptimization&&(c.txPowerMode="measure_ap_ap",c.nthSmallestRssi=0),!c.channelMode&&!c.txPowerMode)return D.error("Please enable at least one optimization method");await i(c)},o=()=>{r.resetFields(),a()};return e.jsxs(Qt,{title:"Optimization Config",open:t,onCancel:o,onFinish:u=>l(u),form:r,initialValues:s,modalClass:"ampcon-middle-modal",children:[e.jsx("style",{children:`
          .coverage-threshold-form-item .ant-form-item-explain {
            margin-left: 50px;
          }
        `}),e.jsxs("div",{style:{display:"flex",alignItems:"center",marginTop:"10px"},children:[e.jsx("span",{children:"Automatic Channel Optimization"}),e.jsx(A.Item,{style:{marginBottom:0,marginLeft:"50px"},name:"enableChannelOptimization",valuePropName:"checked",children:e.jsx(te,{})})]}),e.jsxs("div",{children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",marginTop:"16px"},children:[e.jsx("span",{children:"Optimize TX Power Configuration"}),e.jsx(A.Item,{style:{marginBottom:0,marginLeft:"44.5px"},name:"enableTxPowerOptimization",valuePropName:"checked",children:e.jsx(te,{})})]}),e.jsx(A.Item,{noStyle:!0,shouldUpdate:(u,c)=>u.txPowerMode!==c.txPowerMode||u.enableTxPowerOptimization!==c.enableTxPowerOptimization,children:()=>e.jsx(e.Fragment,{children:r.getFieldValue("enableTxPowerOptimization")&&e.jsx("div",{children:e.jsxs("div",{style:{display:"flex",alignItems:"center",marginTop:"20px"},children:[e.jsx("span",{style:{position:"relative"}}),e.jsx(A.Item,{label:"Coverage Threshold",name:"coverageThreshold",rules:[{required:!0,message:""},{validator:(u,c)=>c==null||c===""?Promise.reject(new Error("Coverage Threshold Rssi is required")):c<-80||c>-60?Promise.reject(new Error("values:  -80 ～ -60")):Promise.resolve()}],style:{marginBottom:0,marginLeft:"0px",minHeight:"35px"},className:"coverage-threshold-form-item",children:e.jsx(ie,{style:{width:"280px",marginLeft:"50px"}})})]})})})})]})]})},Io=({visible:t,onOk:a,onCancel:s,confirmLoading:i})=>(t&&Ee("Channel switching may occur during optimization, which may lead to user disconnection. The whole process is estimated to take 10 minutes. You are advised to avoid peak hours. Are you sure you want to start?",a,s),null),Eo=[{label:"Monday",value:"2"},{label:"Tuesday",value:"3"},{label:"Wednesday",value:"4"},{label:"Thursday",value:"5"},{label:"Friday",value:"6"},{label:"Saturday",value:"7"},{label:"Sunday",value:"1"}],Po=({visible:t,onClose:a,config:s,onApply:i})=>{const[n]=A.useForm();d.useEffect(()=>{t&&n.setFieldsValue({...s,executeTime:s.executeTime?ut(s.executeTime,"HH:mm"):null})},[t,s,n]);const r=async()=>{try{const l=await n.validateFields();await i({...l,executeTime:l.executeTime?l.executeTime.format("HH:mm"):""})!==!1&&a()}catch{D.error("Failed to save the scheduled task")}};return e.jsxs(Ie,{open:t,onCancel:a,onOk:r,okText:"Apply",cancelText:"Cancel",width:680,destroyOnClose:!0,className:"scheduler-modal",title:e.jsx("span",{className:"scheduler-title",children:"Optimization Scheduler"}),children:[e.jsx(de,{style:{margin:"-10px 0px 0px 0px"}}),e.jsx(A,{form:n,layout:"vertical",initialValues:{...s,executeTime:s.executeTime?ut(s.executeTime,"HH:mm"):null},children:e.jsxs("div",{className:"scheduler-modal-body-row",children:[e.jsxs("div",{className:"scheduler-row scheduler-row-switch",children:[e.jsx("span",{className:"scheduler-label",children:"Scheduled Optimization"}),e.jsx(A.Item,{name:"enabled",valuePropName:"checked",noStyle:!0,children:e.jsx(te,{})})]}),e.jsx(A.Item,{noStyle:!0,shouldUpdate:(l,o)=>l.enabled!==o.enabled,children:()=>n.getFieldValue("enabled")&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"scheduler-row scheduler-row-time",children:[e.jsx("span",{className:"scheduler-label",children:"Network Segment Execution Time"}),e.jsx(A.Item,{name:"executeTime",noStyle:!0,children:e.jsx(Ys,{format:"HH:mm",allowClear:!1,getPopupContainer:l=>l.parentElement,minuteStep:1})})]}),e.jsx(A.Item,{name:"days",noStyle:!0,children:e.jsx(Be.Group,{options:Eo,className:"scheduler-week-group"})})]})})]})}),e.jsx(de,{style:{margin:"0px 0px 0px 0px"}})]})},ps="/ampcon/wireless";async function Fo(t){return oe({url:`${ps}/rrm/task/record`,method:"GET",params:{siteId:t.siteId,sortBy:t.sortBy||"create_time",sortType:t.sortType||"desc",pageNum:t.pageNum,pageSize:t.pageSize}})}async function Mo(t){const a=await oe({url:`${ps}/rrm/task/result`,method:"GET",params:{siteId:t.siteId,taskId:t.taskId,sortBy:t.sortBy,sortType:t.sortType,pageNum:t.pageNum,pageSize:t.pageSize}});return a&&Array.isArray(a.info)&&(a.info=a.info.map(s=>({...s,device_name:typeof s.device_name=="string"?s.device_name:"",device_mode:typeof s.device_mode=="string"?s.device_mode:""}))),a}async function Ro(t){return oe({url:"/smb/owrrm/api/v1/startRRM",method:"POST",data:t})}const _o=[{title:"Device Name",dataIndex:"device_name",key:"device_name",width:200},{title:"Device SN",dataIndex:"sn",key:"sn",width:220},{title:"Device Mode",dataIndex:"device_mode",key:"device_mode",width:180}],Uo=({visible:t,historyId:a,onClose:s})=>{const[i,n]=d.useState([]),[r,l]=d.useState(!1),[o,u]=d.useState({current:1,pageSize:10,total:0});d.useRef(null);const[c,b]=d.useState(!1),[x,g]=d.useState({}),y=window.location.hash?window.location.hash.replace("#",""):"",T=(p=1,m=10,v,h)=>{l(!0),Mo({siteId:y,taskId:a,sortBy:v,sortType:h==="ascend"?"asc":"desc",pageNum:p,pageSize:m}).then(k=>{n(k.info||[]),u(P=>({...P,current:p,pageSize:m,total:k.total||0}))}).finally(()=>l(!1))};d.useEffect(()=>{t&&a&&T(1,o.pageSize)},[t,a]);const f=(p,m,v)=>{let h=v.field,k=v.order;g({field:h,order:k}),T(p.current||1,p.pageSize||10,h,k)};return e.jsx(Ie,{title:"Failed Device List",open:t,onCancel:s,footer:null,width:1360,style:{minHeight:"500px",maxHeight:"calc(100vh - 100px)"},destroyOnClose:!0,className:"failed-device-modal",wrapClassName:"failed-device-modal-wrapper",children:e.jsxs("div",{className:"failed-device-modal-content",children:[e.jsx("div",{className:"table-container",children:e.jsx(Ye,{rowKey:"id",loading:r,dataSource:i,pagination:!1,columns:_o,size:"middle",bordered:!0,showSorterTooltip:!1,onChange:f,scroll:{y:300,scrollToFirstRowOnChange:!1}})}),o.total>gs&&e.jsx("div",{className:"pagination-container",children:e.jsx(Pa,{current:o.current,pageSize:o.pageSize,total:o.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(p,m)=>`${m[0]}-${m[1]} of ${p} items`,onChange:(p,m)=>T(p,m,x.field,x.order),onShowSizeChange:(p,m)=>T(1,m,x.field,x.order),style:{marginTop:"16px",textAlign:"right"},pageSizeOptions:["10","20","50","100"]})})]})})},Do=t=>[{title:e.jsx(ue,{children:"Trigger Time"}),dataIndex:"trigger_time",key:"trigger_time",sorter:!0,multiple:3,width:"25%",defaultSortOrder:"descend"},{title:e.jsx(ue,{children:"Update Time"}),dataIndex:"modified_time",key:"modified_time",sorter:!0,multiple:2,width:"25%"},{title:e.jsx(ue,{children:"Schedule Task"}),dataIndex:"is_schedule_task",key:"is_schedule_task",sorter:!0,multiple:1,width:"25%",render:a=>a===2?"Yes":"No"},{title:e.jsx(ue,{children:"Result"}),key:"result",width:"25%",render:(a,s)=>{const{online_num:i,success_num:n,failed_num:r,id:l}=s;return i===null||n===null||r===null?e.jsx("span",{children:"task running"}):e.jsxs("span",{children:["Online Device:",i||0,",Succeed:",n||0,",",r>0?e.jsxs("a",{className:"history-failed-link",onClick:()=>t(l),children:["Failed:",r]}):e.jsxs("span",{children:["Failed:",r||0]})]})}}],Oo=({data:t,total:a,page:s,pageSize:i,onPageChange:n,onSortChange:r,onShowFailed:l})=>{const o=(u,c,b)=>{r&&r({field:b.field,order:b.order})};return e.jsxs("div",{children:[e.jsx(Ye,{bordered:!0,rowKey:"id",columns:Do(l),dataSource:t,pagination:!1,size:"middle",onChange:o,showSorterTooltip:!1}),a>0&&e.jsx(Pa,{total:a,current:s,pageSize:i,onChange:n,showSizeChanger:!0,showQuickJumper:!0,showTotal:(u,c)=>`${c[0]}-${c[1]} of ${u} items`})]})};function Bo(t){if(!t||t.trim()===""||t==="0 0 0 * * *")return t;const a=t.split(" ");let s=parseInt(a[1]||"0"),i=parseInt(a[2]||"0");const n=a[5]||"*";let r=i*60+s;const l=new Date().getTimezoneOffset();r+=l;let o=0;r<0?(r+=24*60,o=-1):r>=24*60&&(r-=24*60,o=1),i=Math.floor(r/60),s=r%60;let u=n;if(n!=="*"){const b=n.split(",").map(x=>parseInt(x)).map(x=>{let g=x+o;return g<1&&(g+=7),g>7&&(g-=7),g.toString()});u=[...new Set(b)].sort((x,g)=>parseInt(x)-parseInt(g)).join(",")}return`0 ${s} ${i} * * ${u}`}function No(t){if(!t||t.trim()===""||t==="0 0 0 * * *")return t;const a=t.split(" ");let s=parseInt(a[1]||"0"),i=parseInt(a[2]||"0");const n=a[5]||"*";let r=i*60+s;const l=new Date().getTimezoneOffset();r-=l;let o=0;r<0?(r+=24*60,o=-1):r>=24*60&&(r-=24*60,o=1),i=Math.floor(r/60),s=r%60;let u=n;if(n!=="*"){const b=n.split(",").map(x=>parseInt(x)).map(x=>{let g=x+o;return g<1&&(g+=7),g>7&&(g-=7),g.toString()});u=[...new Set(b)].sort((x,g)=>parseInt(x)-parseInt(g)).join(",")}return`0 ${s} ${i} * * ${u}`}function Lo(t,a=!1){if(!t||t.trim()===""||t==="0 0 0 * * *")return{enabled:!1,executeTime:"00:00",days:["2","3","4","5","6","7","1"]};const i=(a?No(t):t).split(" "),n=i[1]||"00",r=i[2]||"00",l=i[5]&&i[5]!=="*"?i[5].split(","):["2","3","4","5","6","7","1"];return{enabled:!0,executeTime:`${r.padStart(2,"0")}:${n.padStart(2,"0")}`,days:l}}function ja(t,a=!1){if(!t.enabled)return"";const[s,i]=t.executeTime.split(":"),n=t.days&&t.days.length===7?"*":t.days.join(","),r=`0 ${i||"0"} ${s||"0"} * * ${n}`;return a?Bo(r):r}const gs=10,Ca=()=>window.location.hash?window.location.hash.replace("#",""):"";function qo(t){try{return JSON.parse(t)}catch{return{algorithms:[],schedule:""}}}function zo(t){try{return JSON.parse(t)}catch{return[]}}function Vo(t){const a={};return t.forEach(s=>{var i,n;if(s.name==="OptimizeTxPower"){const r=Object.fromEntries(s.parameters.split(",").map(l=>l.split("=")));a.txPowerMode=((i=r.mode)==null?void 0:i.replace(/"/g,""))||"measure_ap_ap",a.setDifferentTxPowerPerAp=r.setDifferentTxPowerPerAp==="true",a.targetMcs=r.targetMcs?Number(r.targetMcs):"8",a.coverageThreshold=r.coverageThreshold?Number(r.coverageThreshold):void 0,a.nthSmallestRssi=r.nthSmallestRssi?Number(r.nthSmallestRssi):void 0}if(s.name==="OptimizeChannel"){const r=Object.fromEntries(s.parameters.split(",").map(l=>l.split("=")));a.channelMode=((n=r.mode)==null?void 0:n.replace(/"/g,""))||"least_used",a.setDifferentChannelPerAp=r.setDifferentChannelPerAp==="true"}}),a.channelMode&&!a.txPowerMode?{channelMode:a.channelMode||"least_used",setDifferentChannelPerAp:a.setDifferentChannelPerAp??!1}:!a.channelMode&&a.txPowerMode?{txPowerMode:a.txPowerMode||"measure_ap_ap",setDifferentTxPowerPerAp:a.setDifferentTxPowerPerAp??!1,coverageThreshold:a.coverageThreshold,nthSmallestRssi:a.nthSmallestRssi,targetMcs:a.targetMcs}:{channelMode:a.channelMode||"least_used",setDifferentChannelPerAp:a.setDifferentChannelPerAp??!1,txPowerMode:a.txPowerMode||"measure_ap_ap",setDifferentTxPowerPerAp:a.setDifferentTxPowerPerAp??!1,coverageThreshold:a.coverageThreshold,nthSmallestRssi:a.nthSmallestRssi,targetMcs:a.targetMcs}}function Sa(t){let a=`mode=${t.channelMode}`;t.channelMode==="random"&&(a+=`,setDifferentChannelPerAp=${t.setDifferentChannelPerAp}`);let s=`mode=${t.txPowerMode}`;return t.txPowerMode==="random"?s+=`,setDifferentTxPowerPerAp=${t.setDifferentTxPowerPerAp}`:t.txPowerMode==="measure_ap_client"?s+=`,targetMcs=${t.targetMcs}`:t.txPowerMode==="measure_ap_ap"?s+=`,coverageThreshold=${t.coverageThreshold},nthSmallestRssi=${t.nthSmallestRssi}`:t.txPowerMode,t.channelMode&&!t.txPowerMode?[{name:"OptimizeChannel",parameters:a}]:!t.channelMode&&t.txPowerMode?[{name:"OptimizeTxPower",parameters:s}]:[{name:"OptimizeChannel",parameters:a},{name:"OptimizeTxPower",parameters:s}]}function Qo(t){let a=`mode=${t.channelMode}`;t.channelMode==="random"&&(a+=`,setDifferentChannelPerAp=${t.setDifferentChannelPerAp}`);let s=`mode=${t.txPowerMode}`;return t.txPowerMode==="random"?s+=`,setDifferentTxPowerPerAp=${t.setDifferentTxPowerPerAp}`:t.txPowerMode==="measure_ap_client"?s+=`,targetMcs=${t.targetMcs}`:t.txPowerMode==="measure_ap_ap"?s+=`,coverageThreshold=${t.coverageThreshold},nthSmallestRssi=${t.nthSmallestRssi}`:t.txPowerMode,t.channelMode&&!t.txPowerMode?[{algorithm:"OptimizeChannel",args:a}]:!t.channelMode&&t.txPowerMode?[{algorithm:"OptimizeTxPower",args:s}]:[{algorithm:"OptimizeChannel",args:a},{algorithm:"OptimizeTxPower",args:s}]}const Go=()=>{const{t}=K(),a=bt(),[s,i]=d.useState(!1),[n,r]=d.useState(!1),[l,o]=d.useState(!1),[u,c]=d.useState({visible:!1,historyId:null}),[b,x]=d.useState([]),[g,y]=d.useState(0),[T,f]=d.useState(1),[p,m]=d.useState(gs),[v,h]=d.useState(!1),[k,P]=d.useState("create_time"),[S,j]=d.useState("descend"),[C,E]=d.useState(()=>Ca()),{data:w,refetch:L,isLoading:O}=gt({id:C}),I=Ot({id:C}),U=d.useCallback(async(ee=T,he=p,xe=k,qe=S)=>{h(!0);try{const tt=await Fo({siteId:C,sortBy:xe||"create_time",sortType:qe==="ascend"?"asc":"desc",pageNum:ee,pageSize:he});x(tt.info||[]),y(tt.total||0),f(ee),m(he)}catch{D.error("Failed to fetch optimization history")}finally{h(!1)}},[C,T,p,k,S]);d.useEffect(()=>{const ee=Ca();ee!==C&&(console.log(`VenueId changed from ${C} to ${ee}`),E(ee))},[a.hash,C]),d.useEffect(()=>{C&&(console.log(`Loading history data for venueId: ${C}`),U(1,p))},[C,p,L]);const z=G.useMemo(()=>{var ee,he,xe;return((ee=w==null?void 0:w.deviceRules)==null?void 0:ee.rrm)==="no"&&((he=w==null?void 0:w.deviceRules)!=null&&he.algorithms)?{algorithms:zo(w.deviceRules.algorithms),schedule:""}:!((xe=w==null?void 0:w.deviceRules)!=null&&xe.rrm)||w.deviceRules.rrm==="inherit"||w.deviceRules.rrm==="off"?{algorithms:[],schedule:""}:qo(w.deviceRules.rrm)},[w]),B=G.useMemo(()=>Vo(z.algorithms||[]),[z]),R=G.useMemo(()=>Lo(z.schedule||"",!0),[z]),[se,ce]=d.useState(B),[pe,W]=d.useState(R),[V,re]=d.useState(z.algorithms||[]),[ge,At]=d.useState(z.schedule||"");d.useEffect(()=>{re(z.algorithms||[]),At(z.schedule||""),ce(B),W(R)},[z,B,R]);const Te=async ee=>{const he=Sa(ee),xe=ja(pe,!0),qe=pe.enabled?{algorithms:he,schedule:xe}:"no";try{return await I.mutateAsync({params:{deviceRules:{...w.deviceRules,rrm:qe,algorithms:he}}}),L(),D.success("save successfully"),i(!1),!0}catch{return D.error("Failed to save the configuration"),!1}},Ae=async ee=>{const he=Sa(se),xe=ja(ee,!0),qe=ee.enabled?{algorithms:he,schedule:xe}:"no";try{return ee.enabled&&(!ee.days||ee.days.length===0)?(D.error("Please select at least one week of execution!"),!1):(await I.mutateAsync({params:{deviceRules:{...w.deviceRules,rrm:qe,algorithms:he}}}),L(),D.success("save successfully"),o(!1),!0)}catch{return D.error("Failed to save the configuration"),!1}},Oe=ee=>{P(ee.field),j(ee.order),U(1,p,ee.field,ee.order)},xs=async()=>{r(!1);try{const he={parameter:Qo(B),siteId:C},xe=await Ro(he);console.log("Optimization result:",xe),xe.status==300?D.error("Optimization in progress..."):xe.status==200?D.success("The task was submitted successfully"):D.error("Failed to submit the task")}catch(ee){console.error("Failed to start optimization:",ee)}finally{setTimeout(()=>{U(1,p)},2e3)}};return e.jsxs("div",{className:"rrm-optimize-root",children:[e.jsxs("div",{className:"rrm-optimize-header",children:[e.jsx("div",{children:e.jsx("h2",{children:e.jsx("strong",{children:"WLAN Optimization"})})}),e.jsxs("a",{href:"#",onClick:ee=>{ee.preventDefault(),i(!0)},children:[e.jsx("span",{children:"☰"})," Optimization Config"]})]}),e.jsx("div",{className:"rrm-optimize-desc",children:"With the WLAN optimization service, the organization will determine the optimum operation channels and power concluded from the scanning, considering the traffic, deployment size, and client factors."}),e.jsx(Fa,{className:"custom-trace-alert",message:"Note: The connection to internet will be lost for several minutes during the scanning and optimization. Please select a spare time of network to start scanning.",type:"info",showIcon:!0,closable:!0}),e.jsxs("div",{className:"rrm-optimize-actions",children:[e.jsx(Z,{type:"primary",onClick:()=>r(!0),children:"Optimization Now"}),e.jsx(Z,{type:"default",onClick:()=>o(!0),children:"Optimization Scheduler"})]}),e.jsx("hr",{}),e.jsxs("div",{className:"rrm-optimize-history",children:[e.jsx("h3",{children:"Optimization History"}),e.jsx(Oo,{data:b,total:g,page:T,pageSize:p,onPageChange:(ee,he)=>U(ee,he),onSortChange:Oe,onShowFailed:ee=>c({visible:!0,historyId:ee})})]}),e.jsx(ko,{visible:s,onClose:()=>i(!1),config:se,onApply:Te}),e.jsx(Po,{visible:l,onClose:()=>o(!1),config:pe,onApply:Ae}),e.jsx(Io,{visible:n,onOk:xs,onCancel:()=>r(!1)}),e.jsx(Uo,{visible:u.visible,historyId:u.historyId||"",onClose:()=>c({visible:!1,historyId:null})})]})},Ho=({venueId:t})=>{const{t:a}=K();return De(["get-analytics-boards",t],async()=>{const{data:s}=await we.get(`boards?forVenue=${t}`);return s},{enabled:t!=null,onError:s=>{var i,n;D.error(a("crud.error_fetching_obj",{obj:a("analytics.board"),e:((n=(i=s.response)==null?void 0:i.data)==null?void 0:n.ErrorDescription)||a("common.error")}),5)}})},{TabPane:Mt}=Lt,$o=({visible:t,onClose:a,venueId:s})=>{const{t:i}=K(),n=rn({id:s,enabled:t}),r=ln(),[l,o]=d.useState();d.useEffect(()=>{t&&(o(void 0),n.refetch())},[t]);const u=()=>{l&&r.mutateAsync({revision:l,id:s},{onSuccess:a})},c=x=>e.jsx("ul",{style:{listStyle:"none",padding:0},children:x.sort((g,y)=>y.date-g.date).map(g=>e.jsxs("li",{onClick:()=>o(g.revision),style:{padding:8,border:"1px solid #eee",borderRadius:4,marginBottom:6,background:l===g.revision?"#f0f0f0":"#fff",cursor:"pointer"},children:[e.jsx(Ct,{date:g.date}),e.jsx("div",{children:g.revision})]},g.revision))}),b=d.useMemo(()=>{if(n.isFetching)return e.jsx(Ea,{});if(n.isError)return e.jsx(Fa,{message:"Error",description:"Failed to load firmware list",type:"error"});const x=n.data;return e.jsxs(e.Fragment,{children:[e.jsx(ve.Text,{strong:!0,children:i("venues.upgrade_options_available")}),e.jsxs(Lt,{children:[e.jsx(Mt,{tab:"Official Releases",children:c((x==null?void 0:x.releases)||[])},"official"),e.jsx(Mt,{tab:"Release Candidates",children:c((x==null?void 0:x.releasesCandidates)||[])},"rc"),e.jsx(Mt,{tab:"Dev Releases",children:c((x==null?void 0:x.developmentReleases)||[])},"dev")]})]})},[n,l]);return e.jsx(Ie,{title:i("venues.upgrade_all_devices"),visible:t,onCancel:a,onOk:u,okButtonProps:{disabled:!l,loading:r.isLoading},children:b})},Ko=t=>d.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:32,height:32,viewBox:"0 0 32 32",...t},d.createElement("g",null,d.createElement("g",null,d.createElement("rect",{x:0,y:0,width:32,height:32,rx:4,fill:"#FFFFFF",fillOpacity:1})),d.createElement("g",null,d.createElement("path",{d:"M8.999086984558105,25.333366984558104C8.376126984558105,25.333366984558104,7.7904469845581055,25.090766984558105,7.349697984558105,24.650366984558104C6.909259984558106,24.209766984558104,6.6666669845581055,23.623866984558106,6.6666669845581055,23.000866984558105C6.6666669845581055,22.377966984558107,6.909260984558106,21.792266984558104,7.349697984558105,21.351866984558107L14.557616984558106,14.143896984558104C13.803976984558105,12.127816984558105,14.288846984558106,9.819476984558106,15.823506984558106,8.284976984558105C16.866966984558104,7.241331984558105,18.255266984558105,6.6666669845581055,19.732366984558105,6.6666669845581055C20.532966984558108,6.6666669845581055,21.339066984558105,6.843613984558106,22.063366984558105,7.178302984558106C22.236466984558106,7.258188984558106,22.359466984558104,7.4176009845581055,22.392766984558108,7.605379984558105C22.426366984558108,7.792996984558106,22.366066984558106,7.985206984558106,22.231066984558105,8.119876984558106L19.783966984558106,10.566846984558106L20.058966984558104,11.941196984558106L21.433066984558106,12.216216984558105L23.859166984558104,9.790146984558106C23.994566984558105,9.653886984558106,24.187966984558106,9.593806984558105,24.378666984558105,9.629266984558106C24.567766984558105,9.664696984558105,24.727166984558107,9.790996984558106,24.804966984558106,9.967086984558104C25.735366984558105,12.076566984558106,25.284966984558107,14.491516984558105,23.657566984558105,16.118996984558105C22.613366984558105,17.163066984558107,21.224566984558106,17.738166984558106,19.747266984558106,17.738166984558106C19.105466984558106,17.738166984558106,18.479666984558108,17.629966984558106,17.881966984558105,17.416466984558106L10.648196984558105,24.649966984558105C10.207696984558105,25.090766984558105,9.622066984558106,25.333366984558104,8.999086984558105,25.333366984558104ZM19.732366984558105,7.832856984558106C18.566766984558107,7.832856984558106,17.471466984558106,8.286246984558105,16.648026984558108,9.109526984558105C15.359986984558105,10.397576984558105,15.009176984558106,12.377386984558106,15.775076984558105,14.035986984558106C15.877596984558105,14.257496984558106,15.830866984558105,14.519996984558105,15.658016984558106,14.692566984558106L8.174266984558106,22.176366984558108C7.954176984558106,22.396766984558106,7.832896984558106,22.689466984558106,7.832896984558106,23.000866984558105C7.832896984558106,23.312366984558107,7.954176984558106,23.605366984558106,8.174266984558106,23.825466984558105C8.394656984558106,24.045766984558107,8.687636984558106,24.167166984558104,8.999086984558105,24.167166984558104C9.310576984558105,24.167166984558104,9.603266984558106,24.045766984558107,9.823636984558105,23.825466984558105L17.328466984558105,16.320856984558105C17.498466984558107,16.150876984558103,17.754766984558106,16.102206984558105,17.975866984558106,16.199516984558105C18.536266984558104,16.446686984558106,19.132166984558104,16.571976984558106,19.747166984558106,16.571976984558106C20.913066984558107,16.571976984558106,22.008966984558107,16.118136984558106,22.832966984558105,15.294466984558106C23.903766984558107,14.223336984558106,24.328866984558104,12.719746984558105,24.014466984558105,11.283906984558104L22.036866984558106,13.261446984558106C21.898766984558108,13.399376984558106,21.701666984558106,13.459446984558106,21.510166984558104,13.421016984558104L19.448766984558105,13.008446984558105C19.218166984558106,12.962316984558106,19.037566984558104,12.781986984558106,18.991466984558105,12.551066984558105L18.579266984558103,10.489586984558105C18.540666984558108,10.298376984558105,18.600866984558106,10.100776984558106,18.738666984558105,9.963006984558106L20.749066984558105,7.952436984558106C20.416266984558106,7.8735669845581056,20.073766984558105,7.832856984558106,19.732366984558105,7.832856984558106Z",fill:"#929A9E",fillOpacity:1}),d.createElement("path",{d:"M10.789616984558105,24.791466984558106L10.789666984558107,24.791366984558106L17.934966984558105,17.646266984558103Q18.812266984558107,17.938166984558105,19.747266984558106,17.938166984558105Q22.121166984558105,17.938166984558105,23.798966984558106,16.260416984558105Q25.063366984558105,14.995996984558106,25.387066984558107,13.260466984558105Q25.710766984558106,11.525096984558106,24.987866984558107,9.886296984558106Q24.821166984558104,9.508706984558106,24.415266984558105,9.432626984558105Q24.007866984558106,9.356916984558104,23.717666984558104,9.648716984558106L21.367266984558107,11.999096984558104L20.228866984558106,11.771246984558106L20.001066984558108,10.632576984558106L22.372366984558106,8.261446984558106Q22.661666984558103,7.972686984558106,22.589666984558107,7.570450984558105Q22.518266984558103,7.168018984558105,22.147266984558108,6.996745984558105Q21.000066984558103,6.466666984558105,19.732366984558105,6.466666984558105Q17.358666984558106,6.466666984558105,15.682086984558106,8.143556984558106Q14.492496984558105,9.333016984558105,14.136886984558107,10.991926984558106Q13.796666984558104,12.579066984558105,14.326296984558105,14.092386984558106L7.208276984558106,21.210466984558103Q6.466666984558105,21.952066984558105,6.466666984558105,23.000866984558105Q6.466666984558105,24.049966984558104,7.208323984558105,24.791766984558105Q7.9503569845581055,25.533366984558107,8.999086984558105,25.533366984558107Q10.048146984558105,25.533366984558107,10.789616984558105,24.791466984558106ZM17.830066984558105,17.185566984558108L10.506786984558106,24.508566984558104L10.506736984558106,24.508666984558104Q9.882416984558105,25.133366984558105,8.999086984558105,25.133366984558105Q8.115966984558106,25.133366984558105,7.491071984558106,24.508866984558104Q6.866666984558106,23.884366984558106,6.866666984558106,23.000866984558105Q6.866666984558106,22.117666984558106,7.491118984558105,21.493266984558105L14.789996984558105,14.194366984558105L14.744956984558106,14.073866984558105Q14.198616984558106,12.612356984558105,14.528006984558106,11.075766984558104Q14.858856984558106,9.532356984558106,15.964916984558105,8.426406984558106Q17.524366984558107,6.866664984558105,19.732366984558105,6.866666984558106Q20.912066984558106,6.866666984558106,21.979466984558105,7.359858984558105Q22.160966984558108,7.443620984558105,22.195866984558105,7.6403099845581055Q22.231066984558105,7.8372969845581055,22.089766984558104,7.978316984558106L19.566766984558107,10.501116984558106L19.888966984558106,12.111146984558106L21.498766984558106,12.433336984558107L24.000566984558105,9.931566984558106Q24.142466984558105,9.788796984558106,24.342166984558105,9.825896984558106Q24.540366984558105,9.863046984558105,24.621966984558107,10.047886984558104Q25.294866984558105,11.573346984558105,24.993866984558107,13.187126984558105Q24.692866984558105,14.800796984558106,23.516066984558105,15.977576984558105Q21.955466984558107,17.538166984558103,19.747266984558106,17.538166984558103Q18.817266984558106,17.538166984558103,17.949266984558108,17.228166984558108L17.830066984558105,17.185566984558108ZM18.880066984558106,10.104436984558106L21.143866984558105,7.840466984558105L20.795166984558108,7.757826984558106Q20.267866984558104,7.632852984558106,19.732366984558105,7.632852984558106Q17.842066984558105,7.632853984558105,16.506626984558103,8.968096984558105Q15.496056984558106,9.978656984558105,15.244396984558106,11.398906984558106Q14.992776984558105,12.818916984558106,15.593496984558106,14.119826984558106Q15.705726984558105,14.362316984558106,15.516716984558105,14.551036984558106L8.032846984558105,22.034966984558103Q7.632898984558105,22.435366984558108,7.632898984558105,23.000866984558105Q7.632898984558105,23.566866984558107,8.032826984558106,23.966866984558106Q8.433186984558105,24.367166984558107,8.999086984558105,24.367166984558107Q9.564756984558105,24.367166984558107,9.965036984558106,23.966966984558105L17.469966984558106,16.462276984558105Q17.655266984558104,16.276936984558105,17.895366984558105,16.382576984558106Q18.778166984558105,16.771966984558105,19.747166984558106,16.771966984558105Q21.637866984558105,16.771966984558105,22.974366984558106,15.435916984558105Q23.809166984558104,14.600866984558106,24.135166984558104,13.481896984558105Q24.457966984558105,12.374196984558106,24.209866984558104,11.241136984558105L24.131666984558105,10.883916984558105L21.895466984558105,13.120016984558106Q21.750066984558103,13.265166984558105,21.549466984558105,13.224926984558106L19.487966984558106,12.812336984558105Q19.237566984558107,12.762256984558105,19.187566984558103,12.511906984558106L18.775366984558104,10.450366984558105Q18.734866984558103,10.249566984558106,18.880066984558106,10.104436984558106ZM20.341166984558107,8.077536984558105Q20.038166984558103,8.032856984558105,19.732366984558105,8.032856984558105Q18.007766984558106,8.032856984558105,16.789466984558103,9.250966984558104Q15.867946984558106,10.172446984558105,15.638256984558106,11.468696984558106Q15.408526984558106,12.765146984558106,15.956656984558105,13.952136984558106Q16.186116984558105,14.447926984558105,15.799336984558105,14.834096984558105L8.315686984558106,22.317766984558105Q8.032896984558105,22.600966984558106,8.032896984558105,23.000866984558105Q8.032896984558105,23.401166984558106,8.315706984558105,23.684066984558104Q8.598836984558105,23.967166984558105,8.999086984558105,23.967166984558105Q9.399096984558106,23.967166984558105,9.682226984558106,23.684066984558104L17.187066984558108,16.179426984558106Q17.565866984558106,15.800616984558106,18.056466984558107,16.016446984558108Q18.862466984558104,16.371976984558106,19.747166984558106,16.371976984558106Q21.472166984558108,16.371976984558106,22.691566984558104,15.153026984558105Q24.167966984558106,13.676196984558105,23.885866984558106,11.695346984558105L22.178266984558107,13.402866984558106Q21.881266984558106,13.699456984558106,21.470766984558104,13.617106984558106L19.409466984558108,13.204556984558106Q18.897566984558104,13.102166984558107,18.795366984558108,12.590216984558104L18.383066984558106,10.528796984558106Q18.300466984558106,10.118336984558105,18.597266984558104,9.821576984558106L20.341166984558107,8.077536984558105Z",fillRule:"evenodd",fill:"#929A9E",fillOpacity:1})))),Wo=({venueId:t,isDisabled:a})=>{var b,x;const[s,i]=d.useState(!1),n=on({id:t}),r=dn({id:t});(x=(b=cn({id:t}).data)==null?void 0:b.boards)!=null&&x.length;const o=(g,y,T)=>{const f=g==="success"?e.jsx(Qs,{style:{color:"#2BC174 ",marginRight:8}}):e.jsx(Gs,{style:{color:"#FF5145",marginRight:8}}),p=g==="success"?`Successfully ${y} ${T} Devices`:`Failed ${y} ${T} Devices`;D.open({content:e.jsxs("span",{style:{color:g==="success"?"#2BC174 ":"#FF5145"},children:[f,p]}),duration:3})},u=async({key:g})=>{var y,T,f,p;try{if(g==="reboot"){const v=((y=(await n.mutateAsync()).data.serialNumbers)==null?void 0:y.length)||0;o("success","Reboot",v)}else if(g==="update"){const v=((T=(await r.mutateAsync()).data.serialNumbers)==null?void 0:T.length)||0;o("success","Update",v)}else g==="upgrade"&&i(!0)}catch(m){const v=((p=(f=m.response)==null?void 0:f.data)==null?void 0:p.failedCount)||0;g==="reboot"?o("error","Reboot",v):g==="update"&&o("error","Update",v)}},c=e.jsxs(kt,{onClick:u,children:[e.jsx(kt.Item,{children:"Reboot All Devices"},"reboot"),e.jsx(kt.Item,{children:"Update All Device Configurations"},"update")]});return e.jsxs(e.Fragment,{children:[e.jsx(Vs,{overlay:c,trigger:["click"],placement:"bottomRight",disabled:!t,children:e.jsx(ue,{title:"Actions",placement:"bottom",children:e.jsx(Z,{icon:e.jsx(Pe,{component:Ko}),type:"text",style:{width:40,height:40,borderRadius:4},className:"actions-button"})})}),e.jsx($o,{visible:s,onClose:()=>i(!1),venueId:t})]})},Jo=t=>d.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:32,height:32,viewBox:"0 0 32 32",...t},d.createElement("g",null,d.createElement("g",null,d.createElement("rect",{x:0,y:0,width:32,height:32,rx:4,fill:"#14C9BB",fillOpacity:1})),d.createElement("g",null,d.createElement("g",null,d.createElement("path",{d:"M15.999783277511597,11.000216960906982C14.263573277511597,11.000216960906982,12.631283277511596,11.676341960906983,11.403593277511597,12.904026960906982C10.175908277511597,14.131696960906982,9.499783277511597,15.763986960906983,9.499783277511597,17.500196960906983C9.499783277511597,17.86838696090698,9.798262277511597,18.166866960906983,10.166450277511597,18.166866960906983C10.534633277511597,18.166866960906983,10.833113277511597,17.86838696090698,10.833113277511597,17.500196960906983C10.833113277511597,16.120136960906983,11.370533277511596,14.822676960906982,12.346413277511598,13.846826960906983C13.322263277511597,12.870966960906982,14.619723277511596,12.333526960906983,15.999783277511597,12.333526960906983C17.379843277511597,12.333526960906983,18.677303277511598,12.870946960906982,19.653183277511594,13.846826960906983C20.628983277511598,14.822676960906982,21.166483277511595,16.120136960906983,21.166483277511595,17.500196960906983C21.166483277511595,17.86838696090698,21.464883277511596,18.166866960906983,21.833083277511598,18.166866960906983C22.201283277511596,18.166866960906983,22.499783277511597,17.86838696090698,22.499783277511597,17.500196960906983C22.499783277511597,15.763986960906983,21.8236832775116,14.131696960906982,20.595983277511596,12.904006960906983C19.368283277511594,11.676320960906983,17.735993277511596,11.000216960906982,15.999783277511597,11.000216960906982Z",fill:"#FFFFFF",fillOpacity:1,style:{mixBlendMode:"passthrough"}})),d.createElement("g",null,d.createElement("path",{d:"M15.999996984558106,8C10.845336984558106,8,6.6666669845581055,12.17867,6.6666669845581055,17.33333C6.6666669845581055,19.2083,7.219687984558106,20.9541,8.171336984558106,22.4167L23.828666984558105,22.4167C24.780266984558107,20.9541,25.333366984558104,19.2083,25.333366984558104,17.33333C25.333366984558104,12.17867,21.154666984558105,8,15.999996984558106,8ZM23.068466984558107,21.0833L8.931496984558105,21.0833C8.320266984558106,19.9336,7.999996984558106,18.6506,7.999996984558106,17.33333C7.999996984558106,16.25275,8.211336984558105,15.205210000000001,8.628126984558106,14.21977C9.030976984558105,13.26729,9.607996984558106,12.4116,10.343146984558105,11.67648C11.078266984558105,10.94133,11.933956984558105,10.364329999999999,12.886436984558106,9.96146C13.871876984558106,9.54467,14.919416984558106,9.33333,15.999996984558106,9.33333C17.080566984558104,9.33333,18.128166984558106,9.54467,19.113566984558105,9.96146C20.066066984558105,10.36431,20.921766984558104,10.94133,21.656866984558107,11.67648C22.391966984558106,12.4116,22.968966984558104,13.26729,23.371866984558107,14.21977C23.788666984558105,15.205210000000001,23.999966984558107,16.25275,23.999966984558107,17.33333C23.999966984558107,18.6506,23.679766984558107,19.9336,23.068466984558107,21.0833Z",fill:"#FFFFFF",fillOpacity:1,style:{mixBlendMode:"passthrough"}})),d.createElement("g",null,d.createElement("path",{d:"M16.42132332046509,16.92652784362793C16.213003320465088,16.92641784362793,16.00651332046509,16.96549784362793,15.812633320465087,17.04171784362793L14.373323320465088,15.60240084362793C14.112967320465089,15.34200544362793,13.690863320465088,15.342046743627929,13.430508320465087,15.60238084362793C13.170154420465089,15.86273484362793,13.170154420465089,16.28485984362793,13.430508320465087,16.54519784362793L14.869843320465089,17.98452784362793C14.793623320465088,18.17840784362793,14.754553320465089,18.38488784362793,14.754653320465088,18.59321784362793C14.754653320465088,19.51369784362793,15.500843320465087,20.25987784362793,16.42132332046509,20.25987784362793C17.34180332046509,20.25987784362793,18.08799332046509,19.51369784362793,18.08799332046509,18.59321784362793C18.08799332046509,17.672737843627928,17.341783320465087,16.92652784362793,16.42132332046509,16.92652784362793Z",fill:"#FFFFFF",fillOpacity:1,style:{mixBlendMode:"passthrough"}}))))),Xo=t=>d.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:32,height:32,viewBox:"0 0 32 32",...t},d.createElement("g",null,d.createElement("g",null,d.createElement("rect",{x:0,y:0,width:32,height:32,rx:4,fill:"#FFFFFF",fillOpacity:1})),d.createElement("g",null,d.createElement("g",null,d.createElement("path",{d:"M23.2494,13.75005L19.9997,13.75005C19.5854,13.75005,19.249499999999998,13.41419,19.249499999999998,12.99987C19.249499999999998,12.585560000000001,19.5854,12.249690000000001,19.9997,12.249690000000001L21.3049,12.249690000000001C20.089199999999998,10.52464,18.1102,9.49901,15.99979,9.50036C12.41066,9.50051,9.50117,12.41036,9.50117,15.9998C9.50131,19.589399999999998,12.411159999999999,22.499200000000002,16.0006,22.499200000000002C19.5902,22.4991,22.5,19.589199999999998,22.5,15.9998C22.4862,15.57609,22.8259,15.22508,23.2498,15.22508C23.6737,15.22508,24.0135,15.57609,23.9996,15.9998C23.9996,20.4174,20.4174,23.9996,15.99979,23.9996C11.58224,23.9996,8,20.4174,8,15.9998C8,11.58224,11.58224,8.000000919181,15.99979,8.000000919181C18.5805,7.99878595,21.002299999999998,9.24607,22.5,11.34771L22.5,9.99995C22.4862,9.57625,22.8259,9.22523,23.2498,9.22523C23.6737,9.22523,24.0135,9.57625,23.9996,9.99995L23.9996,12.99987C23.9996,13.41419,23.6637,13.75005,23.2494,13.75005Z",fill:"#929A9E",fillOpacity:1}),d.createElement("path",{d:"M23.8506,13.601040000000001Q24.0996,13.35203,24.0996,12.99987L24.0996,10.00155Q24.1107,9.642479999999999,23.8607,9.38416Q23.6101,9.12523,23.2498,9.12523Q22.889499999999998,9.12523,22.6389,9.38416Q22.3889,9.64247,22.4,10.00151L22.4,11.04452Q21.308,9.62357,19.7018,8.796382Q17.959519999999998,7.899078,15.9998,7.900001Q14.35221,7.900001,12.84721,8.536650999999999Q11.39367,9.15153,10.2726,10.2726Q9.15154,11.39367,8.536650999999999,12.84721Q7.9,14.3522,7.9000005,15.9998Q7.9000005,17.64741,8.536650999999999,19.1524Q9.15155,20.6059,10.2726,21.727Q11.39369,22.848100000000002,12.84721,23.462899999999998Q14.35221,24.0996,15.9998,24.0996Q17.64738,24.0996,19.1524,23.462899999999998Q20.6059,22.848100000000002,21.727,21.727Q22.848,20.6059,23.462899999999998,19.1524Q24.0993,17.64812,24.0996,16.0014Q24.1107,15.642330000000001,23.8607,15.384Q23.6101,15.12508,23.2498,15.12508Q22.889499999999998,15.12508,22.6389,15.384Q22.3889,15.64231,22.4,16.001350000000002Q22.3997,17.302509999999998,21.897199999999998,18.4906Q21.4114,19.639,20.5257,20.5248Q19.6399,21.4105,18.491500000000002,21.8963Q17.30264,22.3992,16.0006,22.3992Q14.698599999999999,22.3992,13.50974,21.8964Q12.36137,21.4107,11.47561,20.524900000000002Q10.58986,19.639200000000002,10.104099999999999,18.4907Q9.60122,17.30184,9.60116,15.9998Q9.60116,14.6978,10.10396,13.508939999999999Q10.58963,12.36056,11.47527,11.4748Q12.36091,10.58905,13.50918,10.1033Q14.69792,9.60042,15.99986,9.60036Q17.55822,9.59937,18.9418,10.31641Q20.238599999999998,10.98847,21.1086,12.14969L19.9997,12.14969Q19.6475,12.14969,19.3985,12.3987Q19.1495,12.64772,19.1495,12.99987Q19.1495,13.35203,19.3985,13.601040000000001Q19.6475,13.85005,19.9997,13.85006L23.2494,13.85006Q23.601599999999998,13.85006,23.8506,13.601040000000001ZM23.8996,9.99832L23.8996,12.99987Q23.8996,13.26919,23.7092,13.459620000000001Q23.518700000000003,13.65006,23.2494,13.65006L19.9997,13.65006Q19.7304,13.65006,19.5399,13.459620000000001Q19.3495,13.26919,19.3495,12.99987Q19.3495,12.73056,19.5399,12.540130000000001Q19.7304,12.349689999999999,19.9997,12.349689999999999L21.497700000000002,12.349689999999999L21.3866,12.19209Q20.4608,10.87834,19.0339,10.13884Q17.606920000000002,9.399329999999999,15.99973,9.40036Q14.657350000000001,9.40042,13.43126,9.9191Q12.247060000000001,10.42006,11.33384,11.33339Q10.42061,12.24672,9.91975,13.43104Q9.40116,14.65726,9.40116,15.9998Q9.40122,17.342399999999998,9.9199,18.5686Q10.42086,19.753,11.33419,20.6663Q12.24754,21.579700000000003,13.43183,22.0806Q14.65805,22.5992,16.0006,22.5992Q17.3432,22.5992,18.5694,22.0805Q19.7538,21.5795,20.667099999999998,20.6662Q21.5805,19.7529,22.081400000000002,18.5686Q22.6,17.34234,22.6,15.9998L22.6,15.99816L22.6,15.99653Q22.591,15.72111,22.782600000000002,15.52309Q22.9742,15.32508,23.2498,15.32508Q23.525399999999998,15.32508,23.717,15.52309Q23.9087,15.72111,23.8996,15.99653L23.8996,15.99816L23.8996,15.9998Q23.8996,17.606830000000002,23.2787,19.0745Q22.6791,20.4921,21.5856,21.5856Q20.4921,22.6791,19.0745,23.2787Q17.60684,23.8996,15.9998,23.8996Q14.392769999999999,23.8996,12.92513,23.2787Q11.50753,22.6791,10.41402,21.5856Q9.32055,20.4921,8.720847,19.0745Q8.1,17.60684,8.100001,15.9998Q8.1,14.392769999999999,8.720847,12.92513Q9.32054,11.50751,10.41402,10.41403Q11.50751,9.32054,12.92513,8.720848Q14.392769999999999,8.100001,15.9998,8.100001Q17.911099999999998,8.0991011,19.610300000000002,8.974187Q21.3094,9.84927,22.418599999999998,11.40574L22.6,11.66034L22.6,9.99832L22.6,9.99668Q22.591,9.721260000000001,22.782600000000002,9.52325Q22.9742,9.32523,23.2498,9.32523Q23.525399999999998,9.32523,23.717,9.52325Q23.9087,9.721260000000001,23.8996,9.99668L23.8996,9.99832Z",fillRule:"evenodd",fill:"#929A9E",fillOpacity:1}))))),Zo="/assets/Logo_NOMoitor-DT8EvDS5.png",Yo=({onTurnOn:t})=>e.jsxs("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -70%)",textAlign:"center",backgroundColor:"#fff",padding:"40px",zIndex:999},children:[e.jsx("img",{src:Zo,alt:"No Data",style:{marginBottom:"0px",width:"96px",height:"96px"}}),e.jsx("div",{style:{fontSize:"12px",color:"#9DA6B3",height:"18px",marginBottom:"22px"},children:"No Data"}),e.jsxs("p",{style:{marginBottom:"40px",width:"739px",height:"auto",fontFamily:"Lato, Lato",fontWeight:600,fontSize:"16px",color:"#212519"},children:["Monitoring not activated on this site.",e.jsx("br",{}),'Please activate it by using the "Monitor" button at the top right of the screen or the button at the bottom']}),e.jsx(Z,{type:"primary",onClick:t,style:{width:"175px",height:"36px"},children:"Turn on the monitoring"})]}),vd=()=>{var O,I,U,z;const t=Hs(B=>B.user.userInfo),a=bt(),s=vt(),{selectedSiteId:i,handleSiteChange:n}=hn(!1),[r,l]=d.useState(""),[o,u]=d.useState(0),[c,b]=d.useState(!1),[x,g]=d.useState(!1),[y,T]=d.useState(!1),{getFirstVenueFavoriteId:f}=Xn({id:"",type:"venue"}),p=d.useMemo(()=>{const B=window.location.hash.substring(1);return i||B||(f==null?void 0:f())||"0"},[i,a.hash]),m=gt({id:p||"0"}),v=Ho({venueId:p||"0"});((I=(O=m.data)==null?void 0:O.boards)==null?void 0:I.length)>0;const h=((z=(U=m.data)==null?void 0:U.boards)==null?void 0:z[0])||"",{resetFromOtherPage:k}=Oa();fn();const P=()=>{b(!0)};d.useEffect(()=>{var B;v.data&&T(!(((B=v.data.boards)==null?void 0:B.length)>0))},[v.data]);const S=()=>{var R,se;if(!p){D.warning("Please select a venue first");return}m.refetch(),((se=(R=m.data)==null?void 0:R.boards)==null?void 0:se.length)>0?g(!0):b(!0)},j=()=>{u(B=>B+1),m.refetch(),D.success("Successfully refreshed")},C=G.useMemo(()=>()=>e.jsx(ir,{venueId:p,venueData:m.data}),[p,m.data]),E=[{key:"Monitor",label:"Monitor",children:y?e.jsx(Yo,{onTurnOn:P}):e.jsx(It,{component:C})},{key:"Configure",label:"Configure",children:e.jsx(It,{component:Ao})},{key:"RRM-Optimize",label:"RRM Optimize",children:e.jsx(It,{component:Go})}],w=t.type==="readonly"?[]:E;d.useEffect(()=>{const B=a.pathname.match(/(Monitor|Configure|RRM-Optimize)$/);if(B)l(B[0]);else if(w.length>0){l(w[0].key);let R=`${a.pathname.replace(/\/$/,"")}/${w[0].key}`;i&&(R+=`#${i}`),s(R,{replace:!0})}},[a.pathname,w,i]);const L=B=>{let R=a.pathname.replace(/(Monitor|Configure|RRM-Optimize)$/,"");R=`${R.replace(/\/$/,"")}/${B}`,p&&(R+=`#${p}`),s(R)};return e.jsxs(Ue,{className:"div-main",children:[e.jsxs("div",{className:"div-header",children:[e.jsx(en,{onChange:n}),e.jsxs(Je,{className:"action-buttons",style:{paddingTop:10},children:[e.jsx(ue,{title:"Monitoring",placement:"bottom",children:e.jsx(Z,{icon:e.jsx(Pe,{component:Jo}),type:"text",onClick:S,style:{width:40,height:40,borderRadius:4},className:"monitor-button"})}),e.jsx(Wo,{venueId:p,isDisabled:!m.data}),e.jsx(ue,{title:"Refresh",placement:"bottom",children:e.jsx(Z,{icon:e.jsx(Pe,{component:Xo}),type:"text",onClick:j,style:{width:40,height:40,borderRadius:4},className:"refresh-button"})})]})]}),e.jsxs("div",{className:"div-tabs",children:[e.jsx(Lt,{activeKey:r,onChange:L,destroyInactiveTabPane:!0,items:w},o),p&&e.jsxs(e.Fragment,{children:[e.jsx(Kn,{id:p,visible:c,onClose:()=>{b(!1)},onApplySuccess:T}),e.jsx(Jn,{boardId:h,venueId:p,visible:x,onClose:()=>g(!1),onApplySuccess:T})]})]})]})};export{vd as default};
