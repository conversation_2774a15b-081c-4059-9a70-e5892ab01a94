import{bv as j,r as l,j as n,a2 as _,W as x,aq as g,b as v,G as w,H as m}from"./index-CCDcquaz.js";import{b as C,u as y}from"./useUrlSync-DjGOfs83.js";import"./Form-CDHrBU_a.js";const R=({onChange:t})=>{C();const o=j(),[c,a]=l.useState(""),{selectedSiteId:i,isAllSitesSelected:r,handleSiteChange:p}=y(!0);l.useEffect(()=>{typeof o.refetch=="function"&&o.refetch()},[]);const u=l.useMemo(()=>{if(!o.data)return[];const e=o.data.sort((s,d)=>String(s.id)==="0"?-1:String(d.id)==="0"?1:s.name.localeCompare(d.name)).map(s=>({label:s.name,value:String(s.id),type:"venue"}));return e.unshift({label:"All Sites",value:"all",type:"venue"}),e},[o.data]),f=l.useMemo(()=>c?u.filter(e=>e.label.toLowerCase().includes(c.toLowerCase())):u,[u,c]),b=e=>{a(e.target.value)},h=e=>{e||a("")};return n.jsxs("div",{className:"site-select-container",children:[n.jsx("span",{className:"site-title",children:"Site"}),n.jsx(_,{className:"site-select",options:f,value:r?"all":i||"all",onChange:e=>{p(e),t==null||t(e)},style:{width:260},onDropdownVisibleChange:h,dropdownRender:e=>n.jsxs(n.Fragment,{children:[n.jsx("div",{style:{padding:8},children:n.jsx(x,{prefix:n.jsx(g,{style:{color:"rgba(0,0,0,.25)"}}),placeholder:"Search sites...",value:c,onChange:b,allowClear:!0})}),n.jsx("div",{children:f.length>0?e:n.jsx("div",{style:{padding:8,textAlign:"center"},children:"No results found"})})]})})]})},k={refresh:()=>{},onClose:()=>{},queryToInvalidate:null},E=({objName:t,operationType:o,refresh:c,onClose:a,queryToInvalidate:i})=>{const{t:r}=v(),p=w(),u=()=>{switch(o){case"update":return r("crud.success_update_obj",{obj:t});case"delete":return r("crud.success_delete_obj",{obj:t});case"blink":return r("commands.blink_success",{obj:t});case"reboot":return r("commands.reboot_success",{obj:t});default:return r("crud.success_create_obj",{obj:t})}},f=e=>{var d,S;const s=((S=(d=e==null?void 0:e.response)==null?void 0:d.data)==null?void 0:S.ErrorDescription)||r("common.unknown_error");switch(o){case"update":return r("crud.error_update_obj",{obj:t,e:s});case"delete":return r("crud.error_delete_obj",{obj:t,e:s});case"blink":return r("commands.blink_error",{obj:t,e:s});case"reboot":return r("commands.reboot_error",{obj:t,e:s});default:return r("crud.error_create_obj",{obj:t,e:s})}},b=l.useCallback(({setSubmitting:e,resetForm:s}={setSubmitting:null,resetForm:null})=>{c&&c(),e&&e(!1),s&&s(),m.success(u()),a&&a(),i&&p.invalidateQueries(i)},[i]),h=l.useCallback((e,{setSubmitting:s}={setSubmitting:null})=>{m.error(f(e)),s&&s(!1)},[]);return l.useMemo(()=>({onSuccess:b,onError:h}),[])};E.defaultProps=k;export{R as S,E as u};
