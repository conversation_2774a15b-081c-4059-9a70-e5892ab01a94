import{U as x,r as v,a4 as F,ai as g,O as S,j as o,M as j,D as f,B as y}from"./index-CCDcquaz.js";import{u as $}from"./SiteContext-D-2Xr-_P.js";/* empty css             */const I=()=>{const e=x(),t=v.useCallback((a,s)=>{let i=n();s(i&&i===a)},[e.entityFavorites.favorites]),r=v.useCallback((a,s)=>{let i=n(),c=i&&i===a;c?e.entityFavorites.remove({id:a,type:"venue"}):e.entityFavorites.add({id:a,type:"venue"}),s(!c)},[e.entityFavorites.favorites]),n=()=>{const a=e.entityFavorites.favorites.find(s=>s.type==="venue");return a?a.id:null};return{isFavorited:t,toggleFavorite:r,getFirstVenueFavoriteId:n}},E=(e=!1)=>{const{selectedSiteId:t,setSelectedSiteId:r,isAllSitesSelected:n,setAllSites:a,defaultSiteId:s,resetFromOtherPage:i}=$(),{getFirstVenueFavoriteId:c}=I(),u=F(),d=g();return v.useEffect(()=>{(()=>{const l=window.location.hash.substring(1),m=u.pathname;if(n&&l!=="all"){d(`${m}#all`,{replace:!0});return}if(!n&&t&&l!==t){d(`${m}#${t}`,{replace:!0});return}if(l||t){if(e&&l==="all"){a();return}if(/^\d+$/.test(l||t)){r(l||t);return}}i();const h=c();if(h){r(h),d(`${u.pathname}#${h}`,{replace:!0});return}s&&(r(s),d(`${u.pathname}#${s}`,{replace:!0}))})()},[e]),{selectedSiteId:t,isAllSitesSelected:n,handleSiteChange:p=>{const l=Array.isArray(p)?p[0]:p;e&&l==="all"?(a(),d(`${u.pathname}#all`)):(r(l),d(`${u.pathname}#${l}`))},displaySiteId:n?null:t}},A=({open:e,title:t,onCancel:r,onFinish:n,initialValues:a,modalClass:s="ampcon-max-modal",form:i,onValuesChange:c,children:u})=>e?(i||([i]=S.useForm()),o.jsx(j,{open:e,title:o.jsxs("div",{children:[t,o.jsx(f,{style:{marginTop:8,marginBottom:0}})]}),onCancel:r,footer:o.jsxs(o.Fragment,{children:[o.jsx(f,{style:{marginTop:0,marginBottom:20}}),o.jsx(y,{onClick:r,children:"Cancel"},"cancel"),o.jsx(y,{type:"primary",onClick:()=>i==null?void 0:i.submit(),children:"Apply"},"ok")]}),destroyOnClose:!0,className:`wirelessModal${s?" "+s:""}`,children:o.jsx(S,{form:i,initialValues:a,onFinish:n,onValuesChange:c,className:"wirelessForm",children:u})})):null,B=()=>{const{selectedSiteId:e,isAllSitesSelected:t}=$(),r=F(),n=g();v.useEffect(()=>{(()=>{const s=window.location.hash.substring(1);t&&s!=="all"?n(`${r.pathname}#all`,{replace:!0}):!t&&e&&s!==e&&n(`${r.pathname}#${e}`,{replace:!0})})()},[e,t])};export{A as F,B as a,I as b,E as u};
