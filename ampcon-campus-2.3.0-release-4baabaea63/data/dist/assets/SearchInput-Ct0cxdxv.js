import{r as u,f as _r,d5 as Ie,v as K,q as Y,j as d,l as St,s as $t,t as ct,w as ye,m as Ye,k as H,cg as at,n as oe,ce as da,cc as Fn,d6 as Br,aG as ht,cd as Si,d7 as fa,aO as bi,cf as ga,d8 as ma,ch as pa,bj as ha,aM as va,p as dt,b as pe,be as rn,bB as qe,cK as st,u as Re,z as Wt,y as ft,G as bt,a as ae,H as on,b4 as ya,a_ as Sa,b8 as ba,R as Ae,ao as xa,d9 as xi,da as Ci,db as wi,dc as Ca,dd as wa,de as _a,df as Ra,dg as Gr,dh as Ea,aN as Ma,_ as A,di as $e,aT as xt,dj as _i,dk as Pa,c9 as $a,dl as Fa,dm as Ri,ax as lt,J as sn,aZ as Gt,a0 as ka,bb as ja,aw as Ei,b2 as Mi,cM as Pi,dn as $i,b0 as Rr,a$ as So,dp as Fi,cD as Aa,bc as Ia,by as ki,S as Zr,A as ji,g as Va,h as wn,O as Et,a2 as we,ab as Ai,B as kn,Z as Ii,ag as Ta,ai as Oa,dq as Da,T as La,aB as Ha,W as za,aq as Na,L as bo}from"./index-CCDcquaz.js";import{u as Ba,a as Ga,F as Mt}from"./useFastField-C4vjyPS0.js";import{A as Za,a as Wa,M as qa,H as Ue}from"./useCommandModal-B9ChXmyw.js";import{c as an,a as Ua,C as Ka,D as Ya}from"./CustomTable-B4Am8LRY.js";import{u as Xa}from"./useDataGrid-C8ZjC9Fo.js";import"./Form-CDHrBU_a.js";import{d as Ja}from"./index-B9-ToZA1.js";function Qa(e,t,n,r){return e.addEventListener(t,n,r),()=>{e.removeEventListener(t,n,r)}}function el(e){return e.view??window}function tl(e){const t=el(e);return typeof t.PointerEvent<"u"&&e instanceof t.PointerEvent?e.pointerType==="mouse":e instanceof t.MouseEvent}function Vi(e){return!!e.touches}function nl(e){return Vi(e)&&e.touches.length>1}function rl(e,t="page"){const n=e.touches[0]||e.changedTouches[0];return{x:n[`${t}X`],y:n[`${t}Y`]}}function ol(e,t="page"){return{x:e[`${t}X`],y:e[`${t}Y`]}}function Ti(e,t="page"){return Vi(e)?rl(e,t):ol(e,t)}function il(e){return t=>{const n=tl(t);(!n||n&&t.button===0)&&e(t)}}function sl(e,t=!1){function n(o){e(o,{point:Ti(o)})}return t?il(n):n}function jn(e,t,n,r){return Qa(e,t,sl(n,t==="pointerdown"),r)}function al(e){const t=parseFloat(e);return typeof t!="number"||Number.isNaN(t)?0:t}function Oi(e,t){let n=al(e);const r=10**(t??10);return n=Math.round(n*r)/r,t?n.toFixed(t):n.toString()}function xo(e){if(!Number.isFinite(e))return 0;let t=1,n=0;for(;Math.round(e*t)/t!==e;)t*=10,n+=1;return n}function ll(e,t,n){return e==null?e:(n<t&&console.warn("clamp: max cannot be less than min"),Math.min(Math.max(e,t),n))}function Ym(e,t={}){const[n,r]=u.useState(!1),[o,i]=u.useState(e);u.useEffect(()=>i(e),[e]);const{timeout:s=1500,...a}=typeof t=="number"?{timeout:t}:t,l=u.useCallback(c=>{const f=typeof c=="string"?c:o;"clipboard"in navigator?navigator.clipboard.writeText(f).then(()=>r(!0)).catch(()=>r(_r(f,a))):r(_r(f,a))},[o,a]);return u.useEffect(()=>{let c=null;return n&&(c=window.setTimeout(()=>{r(!1)},s)),()=>{c&&window.clearTimeout(c)}},[s,n]),{value:o,setValue:i,onCopy:l,hasCopied:n}}function ul(e={}){const{onChange:t,precision:n,defaultValue:r,value:o,step:i=1,min:s=Number.MIN_SAFE_INTEGER,max:a=Number.MAX_SAFE_INTEGER,keepWithinRange:l=!0}=e,c=Ie(t),[f,p]=u.useState(()=>r==null?"":cr(r,i,n)??""),g=typeof o<"u",m=g?o:f,h=Di(gt(m),i),v=n??h,y=u.useCallback(O=>{O!==m&&(g||p(O.toString()),c==null||c(O.toString(),gt(O)))},[c,g,m]),S=u.useCallback(O=>{let E=O;return l&&(E=ll(E,s,a)),Oi(E,v)},[v,l,a,s]),x=u.useCallback((O=i)=>{let E;m===""?E=gt(O):E=gt(m)+O,E=S(E),y(E)},[S,i,y,m]),C=u.useCallback((O=i)=>{let E;m===""?E=gt(-O):E=gt(m)-O,E=S(E),y(E)},[S,i,y,m]),_=u.useCallback(()=>{let O;r==null?O="":O=cr(r,i,n)??s,y(O)},[r,n,i,y,s]),P=u.useCallback(O=>{const E=cr(O,i,v)??s;y(E)},[v,i,y,s]),$=gt(m);return{isOutOfRange:$>a||$<s,isAtMax:$===a,isAtMin:$===s,precision:v,value:m,valueAsNumber:$,update:y,reset:_,increment:x,decrement:C,clamp:S,cast:P,setValue:p}}function gt(e){return parseFloat(e.toString().replace(/[^\w.-]+/g,""))}function Di(e,t){return Math.max(xo(t),xo(e))}function cr(e,t,n){const r=gt(e);if(Number.isNaN(r))return;const o=Di(r,t);return Oi(r,n??o)}function cl(e,t){const n=Ie(e);u.useEffect(()=>{let r=null;const o=()=>n();return t!==null&&(r=window.setInterval(o,t)),()=>{r&&window.clearInterval(r)}},[t,n])}function Li(e){const t=u.useRef(null);return t.current=e,t}const Hi=1/60*1e3,dl=typeof performance<"u"?()=>performance.now():()=>Date.now(),zi=typeof window<"u"?e=>window.requestAnimationFrame(e):e=>setTimeout(()=>e(dl()),Hi);function fl(e){let t=[],n=[],r=0,o=!1,i=!1;const s=new WeakSet,a={schedule:(l,c=!1,f=!1)=>{const p=f&&o,g=p?t:n;return c&&s.add(l),g.indexOf(l)===-1&&(g.push(l),p&&o&&(r=t.length)),l},cancel:l=>{const c=n.indexOf(l);c!==-1&&n.splice(c,1),s.delete(l)},process:l=>{if(o){i=!0;return}if(o=!0,[t,n]=[n,t],n.length=0,r=t.length,r)for(let c=0;c<r;c++){const f=t[c];f(l),s.has(f)&&(a.schedule(f),e())}o=!1,i&&(i=!1,a.process(l))}};return a}const gl=40;let Er=!0,ln=!1,Mr=!1;const Nt={delta:0,timestamp:0},fn=["read","update","preRender","render","postRender"],Dn=fn.reduce((e,t)=>(e[t]=fl(()=>ln=!0),e),{}),ml=fn.reduce((e,t)=>{const n=Dn[t];return e[t]=(r,o=!1,i=!1)=>(ln||vl(),n.schedule(r,o,i)),e},{}),pl=fn.reduce((e,t)=>(e[t]=Dn[t].cancel,e),{});fn.reduce((e,t)=>(e[t]=()=>Dn[t].process(Nt),e),{});const hl=e=>Dn[e].process(Nt),Ni=e=>{ln=!1,Nt.delta=Er?Hi:Math.max(Math.min(e-Nt.timestamp,gl),1),Nt.timestamp=e,Mr=!0,fn.forEach(hl),Mr=!1,ln&&(Er=!1,zi(Ni))},vl=()=>{ln=!0,Er=!0,Mr||zi(Ni)},Co=()=>Nt;var yl=Object.defineProperty,Sl=(e,t,n)=>t in e?yl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,We=(e,t,n)=>(Sl(e,typeof t!="symbol"?t+"":t,n),n);class bl{constructor(t,n,r){if(We(this,"history",[]),We(this,"startEvent",null),We(this,"lastEvent",null),We(this,"lastEventInfo",null),We(this,"handlers",{}),We(this,"removeListeners",()=>{}),We(this,"threshold",3),We(this,"win"),We(this,"updatePoint",()=>{if(!(this.lastEvent&&this.lastEventInfo))return;const a=dr(this.lastEventInfo,this.history),l=this.startEvent!==null,c=_l(a.offset,{x:0,y:0})>=this.threshold;if(!l&&!c)return;const{timestamp:f}=Co();this.history.push({...a.point,timestamp:f});const{onStart:p,onMove:g}=this.handlers;l||(p==null||p(this.lastEvent,a),this.startEvent=this.lastEvent),g==null||g(this.lastEvent,a)}),We(this,"onPointerMove",(a,l)=>{this.lastEvent=a,this.lastEventInfo=l,ml.update(this.updatePoint,!0)}),We(this,"onPointerUp",(a,l)=>{const c=dr(l,this.history),{onEnd:f,onSessionEnd:p}=this.handlers;p==null||p(a,c),this.end(),!(!f||!this.startEvent)&&(f==null||f(a,c))}),this.win=t.view??window,nl(t))return;this.handlers=n,r&&(this.threshold=r),t.stopPropagation(),t.preventDefault();const o={point:Ti(t)},{timestamp:i}=Co();this.history=[{...o.point,timestamp:i}];const{onSessionStart:s}=n;s==null||s(t,dr(o,this.history)),this.removeListeners=wl(jn(this.win,"pointermove",this.onPointerMove),jn(this.win,"pointerup",this.onPointerUp),jn(this.win,"pointercancel",this.onPointerUp))}updateHandlers(t){this.handlers=t}end(){var t;(t=this.removeListeners)==null||t.call(this),pl.update(this.updatePoint)}}function wo(e,t){return{x:e.x-t.x,y:e.y-t.y}}function dr(e,t){return{point:e.point,delta:wo(e.point,t[t.length-1]),offset:wo(e.point,t[0]),velocity:Cl(t,.1)}}const xl=e=>e*1e3;function Cl(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const o=e[e.length-1];for(;n>=0&&(r=e[n],!(o.timestamp-r.timestamp>xl(t)));)n--;if(!r)return{x:0,y:0};const i=(o.timestamp-r.timestamp)/1e3;if(i===0)return{x:0,y:0};const s={x:(o.x-r.x)/i,y:(o.y-r.y)/i};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function wl(...e){return t=>e.reduce((n,r)=>r(n),t)}function fr(e,t){return Math.abs(e-t)}function _o(e){return"x"in e&&"y"in e}function _l(e,t){if(typeof e=="number"&&typeof t=="number")return fr(e,t);if(_o(e)&&_o(t)){const n=fr(e.x,t.x),r=fr(e.y,t.y);return Math.sqrt(n**2+r**2)}return 0}function Rl(e,t){const{onPan:n,onPanStart:r,onPanEnd:o,onPanSessionStart:i,onPanSessionEnd:s,threshold:a}=t,l=!!(n||r||o||i||s),c=u.useRef(null),f=Li({onSessionStart:i,onSessionEnd:s,onStart:r,onMove:n,onEnd(p,g){c.current=null,o==null||o(p,g)}});u.useEffect(()=>{var p;(p=c.current)==null||p.updateHandlers(f.current)}),u.useEffect(()=>{const p=e.current;if(!p||!l)return;function g(m){c.current=new bl(m,f.current,a)}return jn(p,"pointerdown",g)},[e,l,f,a]),u.useEffect(()=>()=>{var p;(p=c.current)==null||p.end(),c.current=null},[])}function El(e){const t=parseFloat(e);return typeof t!="number"||Number.isNaN(t)?0:t}function Ml(e,t){let n=El(e);const r=10**(t??10);return n=Math.round(n*r)/r,t?n.toFixed(t):n.toString()}function Pl(e){if(!Number.isFinite(e))return 0;let t=1,n=0;for(;Math.round(e*t)/t!==e;)t*=10,n+=1;return n}function Ro(e,t,n){return(e-t)*100/(n-t)}function $l(e,t,n){return(n-t)*e+t}function Eo(e,t,n){const r=Math.round((e-t)/n)*n+t,o=Pl(n);return Ml(r,o)}function gr(e,t,n){return e==null?e:(n<t&&console.warn("clamp: max cannot be less than min"),Math.min(Math.max(e,t),n))}function Fl(e,t){const n={},r={};for(const[o,i]of Object.entries(e))t.includes(o)?n[o]=i:r[o]=i;return[n,r]}const en=K("div",{baseStyle:{display:"flex",alignItems:"center",justifyContent:"center"}});en.displayName="Center";const kl={horizontal:{insetStart:"50%",transform:"translateX(-50%)"},vertical:{top:"50%",transform:"translateY(-50%)"},both:{insetStart:"50%",top:"50%",transform:"translate(-50%, -50%)"}};Y(function(t,n){const{axis:r="both",...o}=t;return d.jsx(K.div,{ref:n,__css:kl[r],...o,position:"absolute"})});var jl=()=>typeof document<"u",Mo=!1,gn=null,Pt=!1,Pr=!1,$r=new Set;function Wr(e,t){$r.forEach(n=>n(e,t))}var Al=typeof window<"u"&&window.navigator!=null?/^Mac/.test(window.navigator.platform):!1;function Il(e){return!(e.metaKey||!Al&&e.altKey||e.ctrlKey||e.key==="Control"||e.key==="Shift"||e.key==="Meta")}function Po(e){Pt=!0,Il(e)&&(gn="keyboard",Wr("keyboard",e))}function It(e){if(gn="pointer",e.type==="mousedown"||e.type==="pointerdown"){Pt=!0;const t=e.composedPath?e.composedPath()[0]:e.target;let n=!1;try{n=t.matches(":focus-visible")}catch{}if(n)return;Wr("pointer",e)}}function Vl(e){return e.mozInputSource===0&&e.isTrusted?!0:e.detail===0&&!e.pointerType}function Tl(e){Vl(e)&&(Pt=!0,gn="virtual")}function Ol(e){e.target===window||e.target===document||e.target instanceof Element&&e.target.hasAttribute("tabindex")||(!Pt&&!Pr&&(gn="virtual",Wr("virtual",e)),Pt=!1,Pr=!1)}function Dl(){Pt=!1,Pr=!0}function $o(){return gn!=="pointer"}function Ll(){if(!jl()||Mo)return;const{focus:e}=HTMLElement.prototype;HTMLElement.prototype.focus=function(...n){Pt=!0,e.apply(this,n)},document.addEventListener("keydown",Po,!0),document.addEventListener("keyup",Po,!0),document.addEventListener("click",Tl,!0),window.addEventListener("focus",Ol,!0),window.addEventListener("blur",Dl,!1),typeof PointerEvent<"u"?(document.addEventListener("pointerdown",It,!0),document.addEventListener("pointermove",It,!0),document.addEventListener("pointerup",It,!0)):(document.addEventListener("mousedown",It,!0),document.addEventListener("mousemove",It,!0),document.addEventListener("mouseup",It,!0)),Mo=!0}function Hl(e){Ll(),e($o());const t=()=>e($o());return $r.add(t),()=>{$r.delete(t)}}const[zl,Bi]=St({name:"FormControlStylesContext",errorMessage:`useFormControlStyles returned is 'undefined'. Seems you forgot to wrap the components in "<FormControl />" `}),[Nl,qt]=St({strict:!1,name:"FormControlContext"});function Bl(e){const{id:t,isRequired:n,isInvalid:r,isDisabled:o,isReadOnly:i,...s}=e,a=u.useId(),l=t||`field-${a}`,c=`${l}-label`,f=`${l}-feedback`,p=`${l}-helptext`,[g,m]=u.useState(!1),[h,v]=u.useState(!1),[y,S]=u.useState(!1),x=u.useCallback((k={},T=null)=>({id:p,...k,ref:Ye(T,B=>{B&&v(!0)})}),[p]),C=u.useCallback((k={},T=null)=>({...k,ref:T,"data-focus":H(y),"data-disabled":H(o),"data-invalid":H(r),"data-readonly":H(i),id:k.id!==void 0?k.id:c,htmlFor:k.htmlFor!==void 0?k.htmlFor:l}),[l,o,y,r,i,c]),_=u.useCallback((k={},T=null)=>({id:f,...k,ref:Ye(T,B=>{B&&m(!0)}),"aria-live":"polite"}),[f]),P=u.useCallback((k={},T=null)=>({...k,...s,ref:T,role:"group","data-focus":H(y),"data-disabled":H(o),"data-invalid":H(r),"data-readonly":H(i)}),[s,o,y,r,i]),$=u.useCallback((k={},T=null)=>({...k,ref:T,role:"presentation","aria-hidden":!0,children:k.children||"*"}),[]);return{isRequired:!!n,isInvalid:!!r,isReadOnly:!!i,isDisabled:!!o,isFocused:!!y,onFocus:()=>S(!0),onBlur:()=>S(!1),hasFeedbackText:g,setHasFeedbackText:m,hasHelpText:h,setHasHelpText:v,id:l,labelId:c,feedbackId:f,helpTextId:p,htmlProps:s,getHelpTextProps:x,getErrorMessageProps:_,getRootProps:P,getLabelProps:C,getRequiredIndicatorProps:$}}const Gi=Y(function(t,n){const r=$t("Form",t),o=ct(t),{getRootProps:i,htmlProps:s,...a}=Bl(o),l=ye("chakra-form-control",t.className);return d.jsx(Nl,{value:a,children:d.jsx(zl,{value:r,children:d.jsx(K.div,{...i({},n),className:l,__css:r.container})})})});Gi.displayName="FormControl";const Gl=Y(function(t,n){const r=qt(),o=Bi(),i=ye("chakra-form__helper-text",t.className);return d.jsx(K.div,{...r==null?void 0:r.getHelpTextProps(t,n),__css:o.helperText,className:i})});Gl.displayName="FormHelperText";function Zl(e){const{isDisabled:t,isInvalid:n,isReadOnly:r,isRequired:o,...i}=qr(e);return{...i,disabled:t,readOnly:r,required:o,"aria-invalid":at(n),"aria-required":at(o),"aria-readonly":at(r)}}function qr(e){const t=qt(),{id:n,disabled:r,readOnly:o,required:i,isRequired:s,isInvalid:a,isReadOnly:l,isDisabled:c,onFocus:f,onBlur:p,...g}=e,m=e["aria-describedby"]?[e["aria-describedby"]]:[];return t!=null&&t.hasFeedbackText&&(t!=null&&t.isInvalid)&&m.push(t.feedbackId),t!=null&&t.hasHelpText&&m.push(t.helpTextId),{...g,"aria-describedby":m.join(" ")||void 0,id:n??(t==null?void 0:t.id),isDisabled:r??c??(t==null?void 0:t.isDisabled),isReadOnly:o??l??(t==null?void 0:t.isReadOnly),isRequired:i??s??(t==null?void 0:t.isRequired),isInvalid:a??(t==null?void 0:t.isInvalid),onFocus:oe(t==null?void 0:t.onFocus,f),onBlur:oe(t==null?void 0:t.onBlur,p)}}const Wl={border:"0",clip:"rect(0, 0, 0, 0)",height:"1px",width:"1px",margin:"-1px",padding:"0",overflow:"hidden",whiteSpace:"nowrap",position:"absolute"};function ql(e={}){const t=qr(e),{isDisabled:n,isReadOnly:r,isRequired:o,isInvalid:i,id:s,onBlur:a,onFocus:l,"aria-describedby":c}=t,{defaultChecked:f,isChecked:p,isFocusable:g,onChange:m,isIndeterminate:h,name:v,value:y,tabIndex:S=void 0,"aria-label":x,"aria-labelledby":C,"aria-invalid":_,...P}=e,$=da(P,["isDisabled","isReadOnly","isRequired","isInvalid","id","onBlur","onFocus","aria-describedby"]),k=Ie(m),T=Ie(a),B=Ie(l),[O,E]=u.useState(!1),[F,N]=u.useState(!1),[W,G]=u.useState(!1),Q=u.useRef(!1);u.useEffect(()=>Hl(R=>{Q.current=R}),[]);const ee=u.useRef(null),[I,q]=u.useState(!0),[Ee,Me]=u.useState(!!f),Fe=p!==void 0,X=Fe?p:Ee,U=u.useCallback(R=>{if(r||n){R.preventDefault();return}Fe||Me(X?R.currentTarget.checked:h?!0:R.currentTarget.checked),k==null||k(R)},[r,n,X,Fe,h,k]);Fn(()=>{ee.current&&(ee.current.indeterminate=!!h)},[h]),Br(()=>{n&&E(!1)},[n,E]),Fn(()=>{const R=ee.current;if(!(R!=null&&R.form))return;const ie=()=>{Me(!!f)};return R.form.addEventListener("reset",ie),()=>{var ve;return(ve=R.form)==null?void 0:ve.removeEventListener("reset",ie)}},[]);const le=n&&!g,me=u.useCallback(R=>{R.key===" "&&G(!0)},[G]),xe=u.useCallback(R=>{R.key===" "&&G(!1)},[G]);Fn(()=>{if(!ee.current)return;ee.current.checked!==X&&Me(ee.current.checked)},[ee.current]);const Oe=u.useCallback((R={},ie=null)=>{const ve=De=>{O&&De.preventDefault(),G(!0)};return{...R,ref:ie,"data-active":H(W),"data-hover":H(F),"data-checked":H(X),"data-focus":H(O),"data-focus-visible":H(O&&Q.current),"data-indeterminate":H(h),"data-disabled":H(n),"data-invalid":H(i),"data-readonly":H(r),"aria-hidden":!0,onMouseDown:oe(R.onMouseDown,ve),onMouseUp:oe(R.onMouseUp,()=>G(!1)),onMouseEnter:oe(R.onMouseEnter,()=>N(!0)),onMouseLeave:oe(R.onMouseLeave,()=>N(!1))}},[W,X,n,O,F,h,i,r]),Se=u.useCallback((R={},ie=null)=>({...R,ref:ie,"data-active":H(W),"data-hover":H(F),"data-checked":H(X),"data-focus":H(O),"data-focus-visible":H(O&&Q.current),"data-indeterminate":H(h),"data-disabled":H(n),"data-invalid":H(i),"data-readonly":H(r)}),[W,X,n,O,F,h,i,r]),he=u.useCallback((R={},ie=null)=>({...$,...R,ref:Ye(ie,ve=>{ve&&q(ve.tagName==="LABEL")}),onClick:oe(R.onClick,()=>{var ve;I||((ve=ee.current)==null||ve.click(),requestAnimationFrame(()=>{var De;(De=ee.current)==null||De.focus({preventScroll:!0})}))}),"data-disabled":H(n),"data-checked":H(X),"data-invalid":H(i)}),[$,n,X,i,I]),Z=u.useCallback((R={},ie=null)=>({...R,ref:Ye(ee,ie),type:"checkbox",name:v,value:y,id:s,tabIndex:S,onChange:oe(R.onChange,U),onBlur:oe(R.onBlur,T,()=>E(!1)),onFocus:oe(R.onFocus,B,()=>E(!0)),onKeyDown:oe(R.onKeyDown,me),onKeyUp:oe(R.onKeyUp,xe),required:o,checked:X,disabled:le,readOnly:r,"aria-label":x,"aria-labelledby":C,"aria-invalid":_?!!_:i,"aria-describedby":c,"aria-disabled":n,"aria-checked":h?"mixed":X,style:Wl}),[v,y,s,S,U,T,B,me,xe,o,X,le,r,x,C,_,i,c,n,h]),te=u.useCallback((R={},ie=null)=>({...R,ref:ie,onMouseDown:oe(R.onMouseDown,Ul),"data-disabled":H(n),"data-checked":H(X),"data-invalid":H(i)}),[X,n,i]);return{state:{isInvalid:i,isFocused:O,isChecked:X,isActive:W,isHovered:F,isIndeterminate:h,isDisabled:n,isReadOnly:r,isRequired:o},getRootProps:he,getCheckboxProps:Oe,getIndicatorProps:Se,getInputProps:Z,getLabelProps:te,htmlProps:$}}function Ul(e){e.preventDefault(),e.stopPropagation()}const[Kl,Yl]=St({name:"FormErrorStylesContext",errorMessage:`useFormErrorStyles returned is 'undefined'. Seems you forgot to wrap the components in "<FormError />" `}),Zi=Y((e,t)=>{const n=$t("FormError",e),r=ct(e),o=qt();return o!=null&&o.isInvalid?d.jsx(Kl,{value:n,children:d.jsx(K.div,{...o==null?void 0:o.getErrorMessageProps(r,t),className:ye("chakra-form__error-message",e.className),__css:{display:"flex",alignItems:"center",...n.text}})}):null});Zi.displayName="FormErrorMessage";const Xl=Y((e,t)=>{const n=Yl(),r=qt();if(!(r!=null&&r.isInvalid))return null;const o=ye("chakra-form__error-icon",e.className);return d.jsx(ht,{ref:t,"aria-hidden":!0,...e,__css:n.icon,className:o,children:d.jsx("path",{fill:"currentColor",d:"M11.983,0a12.206,12.206,0,0,0-8.51,3.653A11.8,11.8,0,0,0,0,12.207,11.779,11.779,0,0,0,11.8,24h.214A12.111,12.111,0,0,0,24,11.791h0A11.766,11.766,0,0,0,11.983,0ZM10.5,16.542a1.476,1.476,0,0,1,1.449-1.53h.027a1.527,1.527,0,0,1,1.523,1.47,1.475,1.475,0,0,1-1.449,1.53h-.027A1.529,1.529,0,0,1,10.5,16.542ZM11,12.5v-6a1,1,0,0,1,2,0v6a1,1,0,1,1-2,0Z"})})});Xl.displayName="FormErrorIcon";const Wi=Y(function(t,n){const r=Si("FormLabel",t),o=ct(t),{className:i,children:s,requiredIndicator:a=d.jsx(qi,{}),optionalIndicator:l=null,...c}=o,f=qt(),p=(f==null?void 0:f.getLabelProps(c,n))??{ref:n,...c};return d.jsxs(K.label,{...p,className:ye("chakra-form__label",o.className),__css:{display:"block",textAlign:"start",...r},children:[s,f!=null&&f.isRequired?a:l]})});Wi.displayName="FormLabel";const qi=Y(function(t,n){const r=qt(),o=Bi();if(!(r!=null&&r.isRequired))return null;const i=ye("chakra-form__required-indicator",t.className);return d.jsx(K.span,{...r==null?void 0:r.getRequiredIndicatorProps(t,n),__css:o.requiredIndicator,className:i})});qi.displayName="RequiredIndicator";function Ut(e){const{viewBox:t="0 0 24 24",d:n,displayName:r,defaultProps:o={}}=e,i=u.Children.toArray(e.path),s=Y((a,l)=>d.jsx(ht,{ref:l,viewBox:t,...o,...a,children:i.length?i:d.jsx("path",{fill:"currentColor",d:n})}));return s.displayName=r,s}function Jl(e,t={}){const{ssr:n=!0,fallback:r}=t,{getWindow:o}=fa(),i=Array.isArray(e)?e:[e];let s=Array.isArray(r)?r:[r];s=s.filter(c=>c!=null);const[a,l]=u.useState(()=>i.map((c,f)=>({media:c,matches:n?!!s[f]:o().matchMedia(c).matches})));return u.useEffect(()=>{const c=o();l(i.map(g=>({media:g,matches:c.matchMedia(g).matches})));const f=i.map(g=>c.matchMedia(g)),p=g=>{l(m=>m.slice().map(h=>h.media===g.media?{...h,matches:g.matches}:h))};return f.forEach(g=>{typeof g.addListener=="function"?g.addListener(p):g.addEventListener("change",p)}),()=>{f.forEach(g=>{typeof g.removeListener=="function"?g.removeListener(p):g.removeEventListener("change",p)})}},[o]),a.map(c=>c.matches)}function Ui(e){var a;const t=ga(e)?e:{fallback:"base"},r=bi().__breakpoints.details.map(({minMaxQuery:l,breakpoint:c})=>({breakpoint:c,query:l.replace("@media screen and ","")})),o=r.map(l=>l.breakpoint===t.fallback),s=Jl(r.map(l=>l.query),{fallback:o,ssr:t.ssr}).findIndex(l=>l==!0);return((a=r[s])==null?void 0:a.breakpoint)??t.fallback}const Ql=e=>d.jsx(ht,{viewBox:"0 0 24 24",...e,children:d.jsx("path",{fill:"currentColor",d:"M21,5H3C2.621,5,2.275,5.214,2.105,5.553C1.937,5.892,1.973,6.297,2.2,6.6l9,12 c0.188,0.252,0.485,0.4,0.8,0.4s0.611-0.148,0.8-0.4l9-12c0.228-0.303,0.264-0.708,0.095-1.047C21.725,5.214,21.379,5,21,5z"})}),eu=e=>d.jsx(ht,{viewBox:"0 0 24 24",...e,children:d.jsx("path",{fill:"currentColor",d:"M12.8,5.4c-0.377-0.504-1.223-0.504-1.6,0l-9,12c-0.228,0.303-0.264,0.708-0.095,1.047 C2.275,18.786,2.621,19,3,19h18c0.379,0,0.725-0.214,0.895-0.553c0.169-0.339,0.133-0.744-0.095-1.047L12.8,5.4z"})});function Fo(e,t,n,r){u.useEffect(()=>{if(!e.current||!r)return;const o=e.current.ownerDocument.defaultView??window,i=Array.isArray(t)?t:[t],s=new o.MutationObserver(a=>{for(const l of a)l.type==="attributes"&&l.attributeName&&i.includes(l.attributeName)&&n(l)});return s.observe(e.current,{attributes:!0,attributeFilter:i}),()=>s.disconnect()})}const tu=50,ko=300;function nu(e,t){const[n,r]=u.useState(!1),[o,i]=u.useState(null),[s,a]=u.useState(!0),l=u.useRef(null),c=()=>clearTimeout(l.current);cl(()=>{o==="increment"&&e(),o==="decrement"&&t()},n?tu:null);const f=u.useCallback(()=>{s&&e(),l.current=setTimeout(()=>{a(!1),r(!0),i("increment")},ko)},[e,s]),p=u.useCallback(()=>{s&&t(),l.current=setTimeout(()=>{a(!1),r(!0),i("decrement")},ko)},[t,s]),g=u.useCallback(()=>{a(!0),r(!1),c()},[]);return u.useEffect(()=>()=>c(),[]),{up:f,down:p,stop:g,isSpinning:n}}const ru=/^[Ee0-9+\-.]$/;function ou(e){return ru.test(e)}function iu(e,t){if(e.key==null)return!0;const n=e.ctrlKey||e.altKey||e.metaKey;return!(e.key.length===1)||n?!0:t(e.key)}function su(e={}){const{focusInputOnChange:t=!0,clampValueOnBlur:n=!0,keepWithinRange:r=!0,min:o=Number.MIN_SAFE_INTEGER,max:i=Number.MAX_SAFE_INTEGER,step:s=1,isReadOnly:a,isDisabled:l,isRequired:c,isInvalid:f,pattern:p="[0-9]*(.[0-9]+)?",inputMode:g="decimal",allowMouseWheel:m,id:h,onChange:v,precision:y,name:S,"aria-describedby":x,"aria-label":C,"aria-labelledby":_,onFocus:P,onBlur:$,onInvalid:k,getAriaValueText:T,isValidCharacter:B,format:O,parse:E,...F}=e,N=Ie(P),W=Ie($),G=Ie(k),Q=Ie(B??ou),ee=Ie(T),I=ul(e),{update:q,increment:Ee,decrement:Me}=I,[Fe,X]=u.useState(!1),U=!(a||l),le=u.useRef(null),me=u.useRef(null),xe=u.useRef(null),Oe=u.useRef(null),Se=u.useCallback(b=>b.split("").filter(Q).join(""),[Q]),he=u.useCallback(b=>(E==null?void 0:E(b))??b,[E]),Z=u.useCallback(b=>((O==null?void 0:O(b))??b).toString(),[O]);Br(()=>{(I.valueAsNumber>i||I.valueAsNumber<o)&&(G==null||G("rangeOverflow",Z(I.value),I.valueAsNumber))},[I.valueAsNumber,I.value,Z,G]),Fn(()=>{if(!le.current)return;if(le.current.value!=I.value){const ne=he(le.current.value);I.setValue(Se(ne))}},[he,Se]);const te=u.useCallback((b=s)=>{U&&Ee(b)},[Ee,U,s]),V=u.useCallback((b=s)=>{U&&Me(b)},[Me,U,s]),R=nu(te,V);Fo(xe,"disabled",R.stop,R.isSpinning),Fo(Oe,"disabled",R.stop,R.isSpinning);const ie=u.useCallback(b=>{if(b.nativeEvent.isComposing)return;const w=he(b.currentTarget.value);q(Se(w)),me.current={start:b.currentTarget.selectionStart,end:b.currentTarget.selectionEnd}},[q,Se,he]),ve=u.useCallback(b=>{var ne;N==null||N(b),me.current&&(b.currentTarget.selectionStart=me.current.start??((ne=b.currentTarget.value)==null?void 0:ne.length),b.currentTarget.selectionEnd=me.current.end??b.currentTarget.selectionStart)},[N]),De=u.useCallback(b=>{if(b.nativeEvent.isComposing)return;iu(b,Q)||b.preventDefault();const ne=Ct(b)*s,w=b.key,ke={ArrowUp:()=>te(ne),ArrowDown:()=>V(ne),Home:()=>q(o),End:()=>q(i)}[w];ke&&(b.preventDefault(),ke(b))},[Q,s,te,V,q,o,i]),Ct=b=>{let ne=1;return(b.metaKey||b.ctrlKey)&&(ne=.1),b.shiftKey&&(ne=10),ne},wt=u.useMemo(()=>{const b=ee==null?void 0:ee(I.value);if(b!=null)return b;const ne=I.value.toString();return ne||void 0},[I.value,ee]),et=u.useCallback(()=>{let b=I.value;if(I.value==="")return;/^[eE]/.test(I.value.toString())?I.setValue(""):(I.valueAsNumber<o&&(b=o),I.valueAsNumber>i&&(b=i),I.cast(b))},[I,i,o]),Ce=u.useCallback(()=>{X(!1),n&&et()},[n,X,et]),tt=u.useCallback(()=>{t&&requestAnimationFrame(()=>{var b;(b=le.current)==null||b.focus()})},[t]),nt=u.useCallback(b=>{b.preventDefault(),R.up(),tt()},[tt,R]),_t=u.useCallback(b=>{b.preventDefault(),R.down(),tt()},[tt,R]);ma(()=>le.current,"wheel",b=>{var rt;const w=(((rt=le.current)==null?void 0:rt.ownerDocument)??document).activeElement===le.current;if(!m||!w)return;b.preventDefault();const j=Ct(b)*s,ke=Math.sign(b.deltaY);ke===-1?te(j):ke===1&&V(j)},{passive:!1});const kt=u.useCallback((b={},ne=null)=>{const w=l||r&&I.isAtMax;return{...b,ref:Ye(ne,xe),role:"button",tabIndex:-1,onPointerDown:oe(b.onPointerDown,j=>{j.button!==0||w||nt(j)}),onPointerLeave:oe(b.onPointerLeave,R.stop),onPointerUp:oe(b.onPointerUp,R.stop),disabled:w,"aria-disabled":at(w)}},[I.isAtMax,r,nt,R.stop,l]),Rt=u.useCallback((b={},ne=null)=>{const w=l||r&&I.isAtMin;return{...b,ref:Ye(ne,Oe),role:"button",tabIndex:-1,onPointerDown:oe(b.onPointerDown,j=>{j.button!==0||w||_t(j)}),onPointerLeave:oe(b.onPointerLeave,R.stop),onPointerUp:oe(b.onPointerUp,R.stop),disabled:w,"aria-disabled":at(w)}},[I.isAtMin,r,_t,R.stop,l]),jt=u.useCallback((b={},ne=null)=>({name:S,inputMode:g,type:"text",pattern:p,"aria-labelledby":_,"aria-label":C,"aria-describedby":x,id:h,disabled:l,role:"spinbutton",...b,readOnly:b.readOnly??a,"aria-readonly":b.readOnly??a,"aria-required":b.required??c,required:b.required??c,ref:Ye(le,ne),value:Z(I.value),"aria-valuemin":o,"aria-valuemax":i,"aria-valuenow":Number.isNaN(I.valueAsNumber)?void 0:I.valueAsNumber,"aria-invalid":at(f??I.isOutOfRange),"aria-valuetext":wt,autoComplete:"off",autoCorrect:"off",onChange:oe(b.onChange,ie),onKeyDown:oe(b.onKeyDown,De),onFocus:oe(b.onFocus,ve,()=>X(!0)),onBlur:oe(b.onBlur,W,Ce)}),[S,g,p,_,C,Z,x,h,l,c,a,f,I.value,I.valueAsNumber,I.isOutOfRange,o,i,wt,ie,De,ve,W,Ce]);return{value:Z(I.value),valueAsNumber:I.valueAsNumber,isFocused:Fe,isDisabled:l,isReadOnly:a,getIncrementButtonProps:kt,getDecrementButtonProps:Rt,getInputProps:jt,htmlProps:F}}const[au,Ln]=St({name:"NumberInputStylesContext",errorMessage:`useNumberInputStyles returned is 'undefined'. Seems you forgot to wrap the components in "<NumberInput />" `}),[lu,Ur]=St({name:"NumberInputContext",errorMessage:"useNumberInputContext: `context` is undefined. Seems you forgot to wrap number-input's components within <NumberInput />"}),Ki=Y(function(t,n){const r=$t("NumberInput",t),o=ct(t),i=qr(o),{htmlProps:s,...a}=su(i),l=u.useMemo(()=>a,[a]);return d.jsx(lu,{value:l,children:d.jsx(au,{value:r,children:d.jsx(K.div,{...s,ref:n,className:ye("chakra-numberinput",t.className),__css:{position:"relative",zIndex:0,...r.root}})})})});Ki.displayName="NumberInput";const Yi=Y(function(t,n){const r=Ln();return d.jsx(K.div,{"aria-hidden":!0,ref:n,...t,__css:{display:"flex",flexDirection:"column",position:"absolute",top:"0",insetEnd:"0px",margin:"1px",height:"calc(100% - 2px)",zIndex:1,...r.stepperGroup}})});Yi.displayName="NumberInputStepper";const Xi=Y(function(t,n){const{getInputProps:r}=Ur(),o=r(t,n),i=Ln();return d.jsx(K.input,{...o,className:ye("chakra-numberinput__field",t.className),__css:{width:"100%",...i.field}})});Xi.displayName="NumberInputField";const Ji=K("div",{baseStyle:{display:"flex",justifyContent:"center",alignItems:"center",flex:1,transitionProperty:"common",transitionDuration:"normal",userSelect:"none",cursor:"pointer",lineHeight:"normal"}}),Qi=Y(function(t,n){const r=Ln(),{getDecrementButtonProps:o}=Ur(),i=o(t,n);return d.jsx(Ji,{...i,__css:r.stepper,children:t.children??d.jsx(Ql,{})})});Qi.displayName="NumberDecrementStepper";const es=Y(function(t,n){const{getIncrementButtonProps:r}=Ur(),o=r(t,n),i=Ln();return d.jsx(Ji,{...o,__css:i.stepper,children:t.children??d.jsx(eu,{})})});es.displayName="NumberIncrementStepper";const ts=Y(function(t,n){const{children:r,placeholder:o,className:i,...s}=t;return d.jsxs(K.select,{...s,ref:n,className:ye("chakra-select",i),children:[o&&d.jsx("option",{value:"",children:o}),r]})});ts.displayName="SelectField";const ns=Y((e,t)=>{var C;const n=$t("Select",e),{rootProps:r,placeholder:o,icon:i,color:s,height:a,h:l,minH:c,minHeight:f,iconColor:p,iconSize:g,...m}=ct(e),[h,v]=Fl(m,pa),y=Zl(v),S={width:"100%",height:"fit-content",position:"relative",color:s},x={paddingEnd:"2rem",...n.field,_focus:{zIndex:"unset",...(C=n.field)==null?void 0:C._focus}};return d.jsxs(K.div,{className:"chakra-select__wrapper",__css:S,...h,...r,children:[d.jsx(ts,{ref:t,height:l??a,minH:c??f,placeholder:o,...y,__css:x,children:e.children}),d.jsx(rs,{"data-disabled":H(y.disabled),...(p||s)&&{color:p||s},__css:n.icon,...g&&{fontSize:g},children:i})]})});ns.displayName="Select";const uu=e=>d.jsx("svg",{viewBox:"0 0 24 24",...e,children:d.jsx("path",{fill:"currentColor",d:"M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"})}),cu=K("div",{baseStyle:{position:"absolute",display:"inline-flex",alignItems:"center",justifyContent:"center",pointerEvents:"none",top:"50%",transform:"translateY(-50%)"}}),rs=e=>{const{children:t=d.jsx(uu,{}),...n}=e,r=u.cloneElement(t,{role:"presentation",className:"chakra-select__icon",focusable:!1,"aria-hidden":!0,style:{width:"1em",height:"1em",color:"currentColor"}});return d.jsx(cu,{...n,className:"chakra-select__icon-wrapper",children:u.isValidElement(t)?r:null})};rs.displayName="SelectIcon";function mr(e){const{orientation:t,vertical:n,horizontal:r}=e;return t==="vertical"?n:r}function du(e){const{orientation:t,thumbPercents:n,isReversed:r}=e,o=m=>({position:"absolute",userSelect:"none",WebkitUserSelect:"none",MozUserSelect:"none",msUserSelect:"none",touchAction:"none",...mr({orientation:t,vertical:{bottom:`${n[m]}%`,transform:"translate(-50%, 50%) scale(var(--slider-thumb-scale, 1))"},horizontal:{left:`${n[m]}%`,transform:"translate(-50%, -50%) scale(var(--slider-thumb-scale, 1))"}})}),i={position:"relative",touchAction:"none",WebkitTapHighlightColor:"rgba(0,0,0,0)",userSelect:"none",outline:0},s={position:"absolute",...mr({orientation:t,vertical:{left:"50%",transform:"translateX(-50%)",height:"100%"},horizontal:{top:"50%",transform:"translateY(-50%)",width:"100%"}})},a=n.length===1,l=[0,r?100-n[0]:n[0]],c=a?l:n;let f=c[0];!a&&r&&(f=100-f);const p=Math.abs(c[c.length-1]-c[0]),g={...s,...mr({orientation:t,vertical:r?{height:`${p}%`,top:`${f}%`}:{height:`${p}%`,bottom:`${f}%`},horizontal:r?{width:`${p}%`,right:`${f}%`}:{width:`${p}%`,left:`${f}%`}})};return{trackStyle:s,innerTrackStyle:g,rootStyle:i,getThumbStyle:o}}function fu(e){const{isReversed:t,direction:n,orientation:r}=e;return n==="ltr"||r==="vertical"?t:!t}function gu(e){const{min:t=0,max:n=100,onChange:r,value:o,defaultValue:i,isReversed:s,direction:a="ltr",orientation:l="horizontal",id:c,isDisabled:f,isReadOnly:p,onChangeStart:g,onChangeEnd:m,step:h=1,getAriaValueText:v,"aria-valuetext":y,"aria-label":S,"aria-labelledby":x,name:C,focusThumbOnChange:_=!0,...P}=e,$=Ie(g),k=Ie(m),T=Ie(v),B=fu({isReversed:s,direction:a,orientation:l}),[O,E]=Ba({value:o,defaultValue:i??pu(t,n),onChange:r}),[F,N]=u.useState(!1),[W,G]=u.useState(!1),Q=!(f||p),ee=(n-t)/10,I=h||(n-t)/100,q=gr(O,t,n),Ee=n-q+t,Fe=Ro(B?Ee:q,t,n),X=l==="vertical",U=Li({min:t,max:n,step:h,isDisabled:f,value:q,isInteractive:Q,isReversed:B,isVertical:X,eventSource:null,focusThumbOnChange:_,orientation:l}),le=u.useRef(null),me=u.useRef(null),xe=u.useRef(null),Oe=u.useId(),Se=c??Oe,[he,Z]=[`slider-thumb-${Se}`,`slider-track-${Se}`],te=u.useCallback(w=>{var yo;if(!le.current)return;const j=U.current;j.eventSource="pointer";const ke=le.current.getBoundingClientRect(),{clientX:rt,clientY:At}=((yo=w.touches)==null?void 0:yo[0])??w,lr=X?ke.bottom-At:rt-ke.left,ca=X?ke.height:ke.width;let ur=lr/ca;B&&(ur=1-ur);let Xt=$l(ur,j.min,j.max);return j.step&&(Xt=parseFloat(Eo(Xt,j.min,j.step))),Xt=gr(Xt,j.min,j.max),Xt},[X,B,U]),V=u.useCallback(w=>{const j=U.current;j.isInteractive&&(w=parseFloat(Eo(w,j.min,I)),w=gr(w,j.min,j.max),E(w))},[I,E,U]),R=u.useMemo(()=>({stepUp(w=I){const j=B?q-w:q+w;V(j)},stepDown(w=I){const j=B?q+w:q-w;V(j)},reset(){V(i||0)},stepTo(w){V(w)}}),[V,B,q,I,i]),ie=u.useCallback(w=>{const j=U.current,rt={ArrowRight:()=>R.stepUp(),ArrowUp:()=>R.stepUp(),ArrowLeft:()=>R.stepDown(),ArrowDown:()=>R.stepDown(),PageUp:()=>R.stepUp(ee),PageDown:()=>R.stepDown(ee),Home:()=>V(j.min),End:()=>V(j.max)}[w.key];rt&&(w.preventDefault(),w.stopPropagation(),rt(w),j.eventSource="keyboard")},[R,V,ee,U]),ve=(T==null?void 0:T(q))??y,{getThumbStyle:De,rootStyle:Ct,trackStyle:wt,innerTrackStyle:et}=u.useMemo(()=>{const w=U.current;return du({isReversed:B,orientation:w.orientation,thumbPercents:[Fe]})},[B,Fe,U]),Ce=u.useCallback(()=>{U.current.focusThumbOnChange&&setTimeout(()=>{var j;return(j=me.current)==null?void 0:j.focus()})},[U]);Br(()=>{const w=U.current;Ce(),w.eventSource==="keyboard"&&(k==null||k(w.value))},[q,k]);function tt(w){const j=te(w);j!=null&&j!==U.current.value&&E(j)}Rl(xe,{onPanSessionStart(w){const j=U.current;j.isInteractive&&(N(!0),Ce(),tt(w),$==null||$(j.value))},onPanSessionEnd(){const w=U.current;w.isInteractive&&(N(!1),k==null||k(w.value))},onPan(w){U.current.isInteractive&&tt(w)}});const nt=u.useCallback((w={},j=null)=>({...w,...P,ref:Ye(j,xe),tabIndex:-1,"aria-disabled":at(f),"data-focused":H(W),style:{...w.style,...Ct}}),[P,f,W,Ct]),_t=u.useCallback((w={},j=null)=>({...w,ref:Ye(j,le),id:Z,"data-disabled":H(f),style:{...w.style,...wt}}),[f,Z,wt]),kt=u.useCallback((w={},j=null)=>({...w,ref:j,style:{...w.style,...et}}),[et]),Rt=u.useCallback((w={},j=null)=>({...w,ref:Ye(j,me),role:"slider",tabIndex:Q?0:void 0,id:he,"data-active":H(F),"aria-valuetext":ve,"aria-valuemin":t,"aria-valuemax":n,"aria-valuenow":q,"aria-orientation":l,"aria-disabled":at(f),"aria-readonly":at(p),"aria-label":S,"aria-labelledby":S?void 0:x,style:{...w.style,...De(0)},onKeyDown:oe(w.onKeyDown,ie),onFocus:oe(w.onFocus,()=>G(!0)),onBlur:oe(w.onBlur,()=>G(!1))}),[Q,he,F,ve,t,n,q,l,f,p,S,x,De,ie]),jt=u.useCallback((w,j=null)=>{const ke=!(w.value<t||w.value>n),rt=q>=w.value,At=Ro(w.value,t,n),lr={position:"absolute",pointerEvents:"none",...mu({orientation:l,vertical:{bottom:B?`${100-At}%`:`${At}%`},horizontal:{left:B?`${100-At}%`:`${At}%`}})};return{...w,ref:j,role:"presentation","aria-hidden":!0,"data-disabled":H(f),"data-invalid":H(!ke),"data-highlighted":H(rt),style:{...w.style,...lr}}},[f,B,n,t,l,q]),b=u.useCallback((w={},j=null)=>({...w,ref:j,type:"hidden",value:q,name:C}),[C,q]);return{state:{value:q,isFocused:W,isDragging:F},actions:R,getRootProps:nt,getTrackProps:_t,getInnerTrackProps:kt,getThumbProps:Rt,getMarkerProps:jt,getInputProps:b}}function mu(e){const{orientation:t,vertical:n,horizontal:r}=e;return t==="vertical"?n:r}function pu(e,t){return t<e?e:e+(t-e)/2}const[hu,Hn]=St({name:"SliderContext",hookName:"useSliderContext",providerName:"<Slider />"}),[vu,zn]=St({name:"SliderStylesContext",hookName:"useSliderStyles",providerName:"<Slider />"}),yu=Y((e,t)=>{const n={...e,orientation:(e==null?void 0:e.orientation)??"horizontal"},r=$t("Slider",n),o=ct(n),{direction:i}=bi();o.direction=i;const{getInputProps:s,getRootProps:a,...l}=gu(o),c=a(),f=s({},t);return d.jsx(hu,{value:l,children:d.jsx(vu,{value:r,children:d.jsxs(K.div,{...c,className:ye("chakra-slider",n.className),__css:r.container,children:[n.children,d.jsx("input",{...f})]})})})});yu.displayName="Slider";const Su=Y((e,t)=>{const{getThumbProps:n}=Hn(),r=zn(),o=n(e,t);return d.jsx(K.div,{...o,className:ye("chakra-slider__thumb",e.className),__css:r.thumb})});Su.displayName="SliderThumb";const bu=Y((e,t)=>{const{getTrackProps:n}=Hn(),r=zn(),o=n(e,t);return d.jsx(K.div,{...o,className:ye("chakra-slider__track",e.className),__css:r.track})});bu.displayName="SliderTrack";const xu=Y((e,t)=>{const{getInnerTrackProps:n}=Hn(),r=zn(),o=n(e,t);return d.jsx(K.div,{...o,className:ye("chakra-slider__filled-track",e.className),__css:r.filledTrack})});xu.displayName="SliderFilledTrack";const Cu=Y((e,t)=>{const{getMarkerProps:n}=Hn(),r=zn(),o=n(e,t);return d.jsx(K.div,{...o,className:ye("chakra-slider__marker",e.className),__css:r.mark})});Cu.displayName="SliderMark";const os=Y((e,t)=>d.jsx(ha,{align:"center",...e,direction:"column",ref:t}));os.displayName="VStack";const is=Y(function(t,n){const r=$t("Switch",t),{spacing:o="0.5rem",children:i,...s}=ct(t),{getIndicatorProps:a,getInputProps:l,getCheckboxProps:c,getRootProps:f,getLabelProps:p}=ql(s),g=u.useMemo(()=>({display:"inline-block",position:"relative",verticalAlign:"middle",lineHeight:0,...r.container}),[r.container]),m=u.useMemo(()=>({display:"inline-flex",flexShrink:0,justifyContent:"flex-start",boxSizing:"content-box",cursor:"pointer",...r.track}),[r.track]),h=u.useMemo(()=>({userSelect:"none",marginStart:o,...r.label}),[o,r.label]);return d.jsxs(K.label,{...f(),className:ye("chakra-switch",t.className),__css:g,children:[d.jsx("input",{className:"chakra-switch__input",...l({},n)}),d.jsx(K.span,{...c(),className:"chakra-switch__track",__css:m,children:d.jsx(K.span,{__css:r.thumb,className:"chakra-switch__thumb",...a()})}),i&&d.jsx(K.span,{className:"chakra-switch__label",...p(),__css:h,children:i})]})});is.displayName="Switch";const[wu,mn]=St({name:"TableStylesContext",errorMessage:`useTableStyles returned is 'undefined'. Seems you forgot to wrap the components in "<Table />" `}),Fr=Y((e,t)=>{const n=$t("Table",e),{className:r,layout:o,...i}=ct(e);return d.jsx(wu,{value:n,children:d.jsx(K.table,{ref:t,__css:{tableLayout:o,...n.table},className:ye("chakra-table",r),...i})})});Fr.displayName="Table";const jo=Y((e,t)=>{const{overflow:n,overflowX:r,className:o,...i}=e;return d.jsx(K.div,{ref:t,className:ye("chakra-table__container",o),...i,__css:{display:"block",whiteSpace:"nowrap",WebkitOverflowScrolling:"touch",overflowX:n??r??"auto",overflowY:"hidden",maxWidth:"100%"}})}),Ao=Y((e,t)=>{const n=mn();return d.jsx(K.tbody,{...e,ref:t,__css:n.tbody})}),_u=Y(({isNumeric:e,...t},n)=>{const r=mn();return d.jsx(K.td,{...t,ref:n,__css:r.td,"data-is-numeric":e})}),Ru=Y(({isNumeric:e,...t},n)=>{const r=mn();return d.jsx(K.th,{...t,ref:n,__css:r.th,"data-is-numeric":e})}),Io=Y((e,t)=>{const n=mn();return d.jsx(K.thead,{...e,ref:t,__css:n.thead})}),ss=Y((e,t)=>{const n=mn();return d.jsx(K.tr,{...e,ref:t,__css:n.tr})}),zt=Y(function(t,n){const r=Si("Text",t),{className:o,align:i,decoration:s,casing:a,...l}=ct(t),c=va({textAlign:t.align,textDecoration:t.decoration,textTransform:t.casing});return d.jsx(K.p,{ref:n,className:ye("chakra-text",t.className),...c,...l,__css:r})});zt.displayName="Text";const Eu=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M208.49,152.49l-72,72a12,12,0,0,1-17,0l-72-72a12,12,0,0,1,17-17L116,187V40a12,12,0,0,1,24,0V187l51.51-51.52a12,12,0,0,1,17,17Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M200,144l-72,72L56,144Z",opacity:"0.2"}),u.createElement("path",{d:"M207.39,140.94A8,8,0,0,0,200,136H136V40a8,8,0,0,0-16,0v96H56a8,8,0,0,0-5.66,13.66l72,72a8,8,0,0,0,11.32,0l72-72A8,8,0,0,0,207.39,140.94ZM128,204.69,75.31,152H180.69Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M205.66,149.66l-72,72a8,8,0,0,1-11.32,0l-72-72A8,8,0,0,1,56,136h64V40a8,8,0,0,1,16,0v96h64a8,8,0,0,1,5.66,13.66Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M204.24,148.24l-72,72a6,6,0,0,1-8.48,0l-72-72a6,6,0,0,1,8.48-8.48L122,201.51V40a6,6,0,0,1,12,0V201.51l61.76-61.75a6,6,0,0,1,8.48,8.48Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M205.66,149.66l-72,72a8,8,0,0,1-11.32,0l-72-72a8,8,0,0,1,11.32-11.32L120,196.69V40a8,8,0,0,1,16,0V196.69l58.34-58.35a8,8,0,0,1,11.32,11.32Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M202.83,146.83l-72,72a4,4,0,0,1-5.66,0l-72-72a4,4,0,0,1,5.66-5.66L124,206.34V40a4,4,0,0,1,8,0V206.34l65.17-65.17a4,4,0,0,1,5.66,5.66Z"}))]]),Mu=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M228,128a12,12,0,0,1-12,12H69l51.52,51.51a12,12,0,0,1-17,17l-72-72a12,12,0,0,1,0-17l72-72a12,12,0,0,1,17,17L69,116H216A12,12,0,0,1,228,128Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M112,56V200L40,128Z",opacity:"0.2"}),u.createElement("path",{d:"M216,120H120V56a8,8,0,0,0-13.66-5.66l-72,72a8,8,0,0,0,0,11.32l72,72A8,8,0,0,0,120,200V136h96a8,8,0,0,0,0-16ZM104,180.69,51.31,128,104,75.31Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M224,128a8,8,0,0,1-8,8H120v64a8,8,0,0,1-13.66,5.66l-72-72a8,8,0,0,1,0-11.32l72-72A8,8,0,0,1,120,56v64h96A8,8,0,0,1,224,128Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M222,128a6,6,0,0,1-6,6H54.49l61.75,61.76a6,6,0,1,1-8.48,8.48l-72-72a6,6,0,0,1,0-8.48l72-72a6,6,0,0,1,8.48,8.48L54.49,122H216A6,6,0,0,1,222,128Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M220,128a4,4,0,0,1-4,4H49.66l65.17,65.17a4,4,0,0,1-5.66,5.66l-72-72a4,4,0,0,1,0-5.66l72-72a4,4,0,0,1,5.66,5.66L49.66,124H216A4,4,0,0,1,220,128Z"}))]]),Pu=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M208.49,120.49a12,12,0,0,1-17,0L140,69V216a12,12,0,0,1-24,0V69L64.49,120.49a12,12,0,0,1-17-17l72-72a12,12,0,0,1,17,0l72,72A12,12,0,0,1,208.49,120.49Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M200,112H56l72-72Z",opacity:"0.2"}),u.createElement("path",{d:"M205.66,106.34l-72-72a8,8,0,0,0-11.32,0l-72,72A8,8,0,0,0,56,120h64v96a8,8,0,0,0,16,0V120h64a8,8,0,0,0,5.66-13.66ZM75.31,104,128,51.31,180.69,104Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M207.39,115.06A8,8,0,0,1,200,120H136v96a8,8,0,0,1-16,0V120H56a8,8,0,0,1-5.66-13.66l72-72a8,8,0,0,1,11.32,0l72,72A8,8,0,0,1,207.39,115.06Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M204.24,116.24a6,6,0,0,1-8.48,0L134,54.49V216a6,6,0,0,1-12,0V54.49L60.24,116.24a6,6,0,0,1-8.48-8.48l72-72a6,6,0,0,1,8.48,0l72,72A6,6,0,0,1,204.24,116.24Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M205.66,117.66a8,8,0,0,1-11.32,0L136,59.31V216a8,8,0,0,1-16,0V59.31L61.66,117.66a8,8,0,0,1-11.32-11.32l72-72a8,8,0,0,1,11.32,0l72,72A8,8,0,0,1,205.66,117.66Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M202.83,114.83a4,4,0,0,1-5.66,0L132,49.66V216a4,4,0,0,1-8,0V49.66L58.83,114.83a4,4,0,0,1-5.66-5.66l72-72a4,4,0,0,1,5.66,0l72,72A4,4,0,0,1,202.83,114.83Z"}))]]),$u=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M228,48V96a12,12,0,0,1-12,12H168a12,12,0,0,1,0-24h19l-7.8-7.8a75.55,75.55,0,0,0-53.32-22.26h-.43A75.49,75.49,0,0,0,72.39,75.57,12,12,0,1,1,55.61,58.41a99.38,99.38,0,0,1,69.87-28.47H126A99.42,99.42,0,0,1,196.2,59.23L204,67V48a12,12,0,0,1,24,0ZM183.61,180.43a75.49,75.49,0,0,1-53.09,21.63h-.43A75.55,75.55,0,0,1,76.77,179.8L69,172H88a12,12,0,0,0,0-24H40a12,12,0,0,0-12,12v48a12,12,0,0,0,24,0V189l7.8,7.8A99.42,99.42,0,0,0,130,226.06h.56a99.38,99.38,0,0,0,69.87-28.47,12,12,0,0,0-16.78-17.16Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M216,128a88,88,0,1,1-88-88A88,88,0,0,1,216,128Z",opacity:"0.2"}),u.createElement("path",{d:"M224,48V96a8,8,0,0,1-8,8H168a8,8,0,0,1,0-16h28.69L182.06,73.37a79.56,79.56,0,0,0-56.13-23.43h-.45A79.52,79.52,0,0,0,69.59,72.71,8,8,0,0,1,58.41,61.27a96,96,0,0,1,135,.79L208,76.69V48a8,8,0,0,1,16,0ZM186.41,183.29a80,80,0,0,1-112.47-.66L59.31,168H88a8,8,0,0,0,0-16H40a8,8,0,0,0-8,8v48a8,8,0,0,0,16,0V179.31l14.63,14.63A95.43,95.43,0,0,0,130,222.06h.53a95.36,95.36,0,0,0,67.07-27.33,8,8,0,0,0-11.18-11.44Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M224,48V96a8,8,0,0,1-8,8H168a8,8,0,0,1-5.66-13.66L180.65,72a79.48,79.48,0,0,0-54.72-22.09h-.45A79.52,79.52,0,0,0,69.59,72.71,8,8,0,0,1,58.41,61.27,96,96,0,0,1,192,60.7l18.36-18.36A8,8,0,0,1,224,48ZM186.41,183.29A80,80,0,0,1,75.35,184l18.31-18.31A8,8,0,0,0,88,152H40a8,8,0,0,0-8,8v48a8,8,0,0,0,13.66,5.66L64,195.3a95.42,95.42,0,0,0,66,26.76h.53a95.36,95.36,0,0,0,67.07-27.33,8,8,0,0,0-11.18-11.44Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M222,48V96a6,6,0,0,1-6,6H168a6,6,0,0,1,0-12h33.52L183.47,72a81.51,81.51,0,0,0-57.53-24h-.46A81.5,81.5,0,0,0,68.19,71.28a6,6,0,1,1-8.38-8.58,93.38,93.38,0,0,1,65.67-26.76H126a93.45,93.45,0,0,1,66,27.53l18,18V48a6,6,0,0,1,12,0ZM187.81,184.72a81.5,81.5,0,0,1-57.29,23.34h-.46a81.51,81.51,0,0,1-57.53-24L54.48,166H88a6,6,0,0,0,0-12H40a6,6,0,0,0-6,6v48a6,6,0,0,0,12,0V174.48l18,18.05a93.45,93.45,0,0,0,66,27.53h.52a93.38,93.38,0,0,0,65.67-26.76,6,6,0,1,0-8.38-8.58Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M224,48V96a8,8,0,0,1-8,8H168a8,8,0,0,1,0-16h28.69L182.06,73.37a79.56,79.56,0,0,0-56.13-23.43h-.45A79.52,79.52,0,0,0,69.59,72.71,8,8,0,0,1,58.41,61.27a96,96,0,0,1,135,.79L208,76.69V48a8,8,0,0,1,16,0ZM186.41,183.29a80,80,0,0,1-112.47-.66L59.31,168H88a8,8,0,0,0,0-16H40a8,8,0,0,0-8,8v48a8,8,0,0,0,16,0V179.31l14.63,14.63A95.43,95.43,0,0,0,130,222.06h.53a95.36,95.36,0,0,0,67.07-27.33,8,8,0,0,0-11.18-11.44Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M220,48V96a4,4,0,0,1-4,4H168a4,4,0,0,1,0-8h38.34L184.89,70.54A84,84,0,0,0,66.8,69.85a4,4,0,1,1-5.6-5.72,92,92,0,0,1,129.34.76L212,86.34V48a4,4,0,0,1,8,0ZM189.2,186.15a83.44,83.44,0,0,1-58.68,23.91h-.47a83.52,83.52,0,0,1-58.94-24.6L49.66,164H88a4,4,0,0,0,0-8H40a4,4,0,0,0-4,4v48a4,4,0,0,0,8,0V169.66l21.46,21.45A91.43,91.43,0,0,0,130,218.06h.51a91.45,91.45,0,0,0,64.28-26.19,4,4,0,1,0-5.6-5.72Z"}))]]),Fu=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M120.49,167.51a12,12,0,0,1,0,17l-32,32a12,12,0,0,1-17,0l-32-32a12,12,0,1,1,17-17L68,179V48a12,12,0,0,1,24,0V179l11.51-11.52A12,12,0,0,1,120.49,167.51Zm96-96-32-32a12,12,0,0,0-17,0l-32,32a12,12,0,0,0,17,17L164,77V208a12,12,0,0,0,24,0V77l11.51,11.52a12,12,0,0,0,17-17Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M176,48V208H80V48Z",opacity:"0.2"}),u.createElement("path",{d:"M117.66,170.34a8,8,0,0,1,0,11.32l-32,32a8,8,0,0,1-11.32,0l-32-32a8,8,0,0,1,11.32-11.32L72,188.69V48a8,8,0,0,1,16,0V188.69l18.34-18.35A8,8,0,0,1,117.66,170.34Zm96-96-32-32a8,8,0,0,0-11.32,0l-32,32a8,8,0,0,0,11.32,11.32L168,67.31V208a8,8,0,0,0,16,0V67.31l18.34,18.35a8,8,0,0,0,11.32-11.32Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M119.39,172.94a8,8,0,0,1-1.73,8.72l-32,32a8,8,0,0,1-11.32,0l-32-32A8,8,0,0,1,48,168H72V48a8,8,0,0,1,16,0V168h24A8,8,0,0,1,119.39,172.94Zm94.27-98.6-32-32a8,8,0,0,0-11.32,0l-32,32A8,8,0,0,0,144,88h24V208a8,8,0,0,0,16,0V88h24a8,8,0,0,0,5.66-13.66Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M116.24,171.76a6,6,0,0,1,0,8.48l-32,32a6,6,0,0,1-8.48,0l-32-32a6,6,0,0,1,8.48-8.48L74,193.51V48a6,6,0,0,1,12,0V193.51l21.76-21.75A6,6,0,0,1,116.24,171.76Zm96-96-32-32a6,6,0,0,0-8.48,0l-32,32a6,6,0,0,0,8.48,8.48L170,62.49V208a6,6,0,0,0,12,0V62.49l21.76,21.75a6,6,0,0,0,8.48-8.48Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M117.66,170.34a8,8,0,0,1,0,11.32l-32,32a8,8,0,0,1-11.32,0l-32-32a8,8,0,0,1,11.32-11.32L72,188.69V48a8,8,0,0,1,16,0V188.69l18.34-18.35A8,8,0,0,1,117.66,170.34Zm96-96-32-32a8,8,0,0,0-11.32,0l-32,32a8,8,0,0,0,11.32,11.32L168,67.31V208a8,8,0,0,0,16,0V67.31l18.34,18.35a8,8,0,0,0,11.32-11.32Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M114.83,173.17a4,4,0,0,1,0,5.66l-32,32a4,4,0,0,1-5.66,0l-32-32a4,4,0,0,1,5.66-5.66L76,198.34V48a4,4,0,0,1,8,0V198.34l25.17-25.17A4,4,0,0,1,114.83,173.17Zm96-96-32-32a4,4,0,0,0-5.66,0l-32,32a4,4,0,0,0,5.66,5.66L172,57.66V208a4,4,0,0,0,8,0V57.66l25.17,25.17a4,4,0,1,0,5.66-5.66Z"}))]]),ku=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M128,20A108,108,0,1,0,236,128,108.12,108.12,0,0,0,128,20Zm0,192a84,84,0,1,1,84-84A84.09,84.09,0,0,1,128,212Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M224,128a96,96,0,1,1-96-96A96,96,0,0,1,224,128Z",opacity:"0.2"}),u.createElement("path",{d:"M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M232,128A104,104,0,1,1,128,24,104.13,104.13,0,0,1,232,128Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M128,26A102,102,0,1,0,230,128,102.12,102.12,0,0,0,128,26Zm0,192a90,90,0,1,1,90-90A90.1,90.1,0,0,1,128,218Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M128,28A100,100,0,1,0,228,128,100.11,100.11,0,0,0,128,28Zm0,192a92,92,0,1,1,92-92A92.1,92.1,0,0,1,128,220Z"}))]]),ju=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M140,80v41.21l34.17,20.5a12,12,0,1,1-12.34,20.58l-40-24A12,12,0,0,1,116,128V80a12,12,0,0,1,24,0ZM128,28A99.38,99.38,0,0,0,57.24,57.34c-4.69,4.74-9,9.37-13.24,14V64a12,12,0,0,0-24,0v40a12,12,0,0,0,12,12H72a12,12,0,0,0,0-24H57.77C63,86,68.37,80.22,74.26,74.26a76,76,0,1,1,1.58,109,12,12,0,0,0-16.48,17.46A100,100,0,1,0,128,28Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M216,128a88,88,0,1,1-88-88A88,88,0,0,1,216,128Z",opacity:"0.2"}),u.createElement("path",{d:"M136,80v43.47l36.12,21.67a8,8,0,0,1-8.24,13.72l-40-24A8,8,0,0,1,120,128V80a8,8,0,0,1,16,0Zm-8-48A95.44,95.44,0,0,0,60.08,60.15C52.81,67.51,46.35,74.59,40,82V64a8,8,0,0,0-16,0v40a8,8,0,0,0,8,8H72a8,8,0,0,0,0-16H49c7.15-8.42,14.27-16.35,22.39-24.57a80,80,0,1,1,1.66,114.75,8,8,0,1,0-11,11.64A96,96,0,1,0,128,32Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M224,128A96,96,0,0,1,62.11,197.82a8,8,0,1,1,11-11.64A80,80,0,1,0,71.43,71.43C67.9,75,64.58,78.51,61.35,82L77.66,98.34A8,8,0,0,1,72,112H32a8,8,0,0,1-8-8V64a8,8,0,0,1,13.66-5.66L50,70.7c3.22-3.49,6.54-7,10.06-10.55A96,96,0,0,1,224,128ZM128,72a8,8,0,0,0-8,8v48a8,8,0,0,0,3.88,6.86l40,24a8,8,0,1,0,8.24-13.72L136,123.47V80A8,8,0,0,0,128,72Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M134,80v44.6l37.09,22.25a6,6,0,0,1-6.18,10.3l-40-24A6,6,0,0,1,122,128V80a6,6,0,0,1,12,0Zm-6-46A93.4,93.4,0,0,0,61.51,61.56c-8.58,8.68-16,17-23.51,25.8V64a6,6,0,0,0-12,0v40a6,6,0,0,0,6,6H72a6,6,0,0,0,0-12H44.73C52.86,88.29,60.79,79.35,70,70a82,82,0,1,1,1.7,117.62,6,6,0,1,0-8.24,8.72A94,94,0,1,0,128,34Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M136,80v43.47l36.12,21.67a8,8,0,0,1-8.24,13.72l-40-24A8,8,0,0,1,120,128V80a8,8,0,0,1,16,0Zm-8-48A95.44,95.44,0,0,0,60.08,60.15C52.81,67.51,46.35,74.59,40,82V64a8,8,0,0,0-16,0v40a8,8,0,0,0,8,8H72a8,8,0,0,0,0-16H49c7.15-8.42,14.27-16.35,22.39-24.57a80,80,0,1,1,1.66,114.75,8,8,0,1,0-11,11.64A96,96,0,1,0,128,32Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M132,80v45.74l38.06,22.83a4,4,0,0,1-4.12,6.86l-40-24A4,4,0,0,1,124,128V80a4,4,0,0,1,8,0Zm-4-44A91.42,91.42,0,0,0,62.93,63C53.05,73,44.66,82.47,36,92.86V64a4,4,0,0,0-8,0v40a4,4,0,0,0,4,4H72a4,4,0,0,0,0-8H40.47C49.61,89,58.3,79,68.6,68.6a84,84,0,1,1,1.75,120.49,4,4,0,1,0-5.5,5.82A92,92,0,1,0,128,36Z"}))]]),Au=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M71.51,88.49a12,12,0,0,1,17-17L116,99V24a12,12,0,0,1,24,0V99l27.51-27.52a12,12,0,0,1,17,17l-48,48a12,12,0,0,1-17,0ZM224,116H188a12,12,0,0,0,0,24h32v56H36V140H68a12,12,0,0,0,0-24H32a20,20,0,0,0-20,20v64a20,20,0,0,0,20,20H224a20,20,0,0,0,20-20V136A20,20,0,0,0,224,116Zm-20,52a16,16,0,1,0-16,16A16,16,0,0,0,204,168Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M232,136v64a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V136a8,8,0,0,1,8-8H224A8,8,0,0,1,232,136Z",opacity:"0.2"}),u.createElement("path",{d:"M240,136v64a16,16,0,0,1-16,16H32a16,16,0,0,1-16-16V136a16,16,0,0,1,16-16H72a8,8,0,0,1,0,16H32v64H224V136H184a8,8,0,0,1,0-16h40A16,16,0,0,1,240,136Zm-117.66-2.34a8,8,0,0,0,11.32,0l48-48a8,8,0,0,0-11.32-11.32L136,108.69V24a8,8,0,0,0-16,0v84.69L85.66,74.34A8,8,0,0,0,74.34,85.66ZM200,168a12,12,0,1,0-12,12A12,12,0,0,0,200,168Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M74.34,85.66A8,8,0,0,1,85.66,74.34L120,108.69V24a8,8,0,0,1,16,0v84.69l34.34-34.35a8,8,0,0,1,11.32,11.32l-48,48a8,8,0,0,1-11.32,0ZM240,136v64a16,16,0,0,1-16,16H32a16,16,0,0,1-16-16V136a16,16,0,0,1,16-16H84.4a4,4,0,0,1,2.83,1.17L111,145A24,24,0,0,0,145,145l23.8-23.8A4,4,0,0,1,171.6,120H224A16,16,0,0,1,240,136Zm-40,32a12,12,0,1,0-12,12A12,12,0,0,0,200,168Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M238,136v64a14,14,0,0,1-14,14H32a14,14,0,0,1-14-14V136a14,14,0,0,1,14-14H72a6,6,0,0,1,0,12H32a2,2,0,0,0-2,2v64a2,2,0,0,0,2,2H224a2,2,0,0,0,2-2V136a2,2,0,0,0-2-2H184a6,6,0,0,1,0-12h40A14,14,0,0,1,238,136Zm-114.24-3.76a6,6,0,0,0,8.48,0l48-48a6,6,0,0,0-8.48-8.48L134,113.51V24a6,6,0,0,0-12,0v89.51L84.24,75.76a6,6,0,0,0-8.48,8.48ZM198,168a10,10,0,1,0-10,10A10,10,0,0,0,198,168Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M240,136v64a16,16,0,0,1-16,16H32a16,16,0,0,1-16-16V136a16,16,0,0,1,16-16H72a8,8,0,0,1,0,16H32v64H224V136H184a8,8,0,0,1,0-16h40A16,16,0,0,1,240,136Zm-117.66-2.34a8,8,0,0,0,11.32,0l48-48a8,8,0,0,0-11.32-11.32L136,108.69V24a8,8,0,0,0-16,0v84.69L85.66,74.34A8,8,0,0,0,74.34,85.66ZM200,168a12,12,0,1,0-12,12A12,12,0,0,0,200,168Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M236,136v64a12,12,0,0,1-12,12H32a12,12,0,0,1-12-12V136a12,12,0,0,1,12-12H72a4,4,0,0,1,0,8H32a4,4,0,0,0-4,4v64a4,4,0,0,0,4,4H224a4,4,0,0,0,4-4V136a4,4,0,0,0-4-4H184a4,4,0,0,1,0-8h40A12,12,0,0,1,236,136Zm-110.83-5.17a4,4,0,0,0,5.66,0l48-48a4,4,0,1,0-5.66-5.66L132,118.34V24a4,4,0,0,0-8,0v94.34L82.83,77.17a4,4,0,0,0-5.66,5.66ZM196,168a8,8,0,1,0-8,8A8,8,0,0,0,196,168Z"}))]]),Iu=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M208,76H180V56A52,52,0,0,0,76,56V76H48A20,20,0,0,0,28,96V208a20,20,0,0,0,20,20H208a20,20,0,0,0,20-20V96A20,20,0,0,0,208,76ZM100,56a28,28,0,0,1,56,0V76H100ZM204,204H52V100H204Zm-60-52a16,16,0,1,1-16-16A16,16,0,0,1,144,152Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M216,96V208a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V96a8,8,0,0,1,8-8H208A8,8,0,0,1,216,96Z",opacity:"0.2"}),u.createElement("path",{d:"M208,80H176V56a48,48,0,0,0-96,0V80H48A16,16,0,0,0,32,96V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V96A16,16,0,0,0,208,80ZM96,56a32,32,0,0,1,64,0V80H96ZM208,208H48V96H208V208Zm-68-56a12,12,0,1,1-12-12A12,12,0,0,1,140,152Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M208,80H176V56a48,48,0,0,0-96,0V80H48A16,16,0,0,0,32,96V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V96A16,16,0,0,0,208,80Zm-80,84a12,12,0,1,1,12-12A12,12,0,0,1,128,164Zm32-84H96V56a32,32,0,0,1,64,0Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M208,82H174V56a46,46,0,0,0-92,0V82H48A14,14,0,0,0,34,96V208a14,14,0,0,0,14,14H208a14,14,0,0,0,14-14V96A14,14,0,0,0,208,82ZM94,56a34,34,0,0,1,68,0V82H94ZM210,208a2,2,0,0,1-2,2H48a2,2,0,0,1-2-2V96a2,2,0,0,1,2-2H208a2,2,0,0,1,2,2Zm-72-56a10,10,0,1,1-10-10A10,10,0,0,1,138,152Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M208,80H176V56a48,48,0,0,0-96,0V80H48A16,16,0,0,0,32,96V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V96A16,16,0,0,0,208,80ZM96,56a32,32,0,0,1,64,0V80H96ZM208,208H48V96H208V208Zm-68-56a12,12,0,1,1-12-12A12,12,0,0,1,140,152Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M208,84H172V56a44,44,0,0,0-88,0V84H48A12,12,0,0,0,36,96V208a12,12,0,0,0,12,12H208a12,12,0,0,0,12-12V96A12,12,0,0,0,208,84ZM92,56a36,36,0,0,1,72,0V84H92ZM212,208a4,4,0,0,1-4,4H48a4,4,0,0,1-4-4V96a4,4,0,0,1,4-4H208a4,4,0,0,1,4,4Zm-76-56a8,8,0,1,1-8-8A8,8,0,0,1,136,152Z"}))]]),as=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:Eu}));as.displayName="ArrowDownIcon";const Vu=as,ls=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:Mu}));ls.displayName="ArrowLeftIcon";const Tu=ls,us=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:Pu}));us.displayName="ArrowUpIcon";const Ou=us,cs=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:$u}));cs.displayName="ArrowsClockwiseIcon";const Vo=cs,ds=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:Fu}));ds.displayName="ArrowsDownUpIcon";const Du=ds,fs=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:ku}));fs.displayName="CircleIcon";const Lu=fs,gs=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:ju}));gs.displayName="ClockCounterClockwiseIcon";const Hu=gs,ms=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:Au}));ms.displayName="DownloadIcon";const Xm=ms,ps=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:Iu}));ps.displayName="LockIcon";const zu=ps,Nu=Ut({viewBox:"0 0 14 14",d:"M14,7.77 L14,6.17 L12.06,5.53 L11.61,4.44 L12.49,2.6 L11.36,1.47 L9.55,2.38 L8.46,1.93 L7.77,0.01 L6.17,0.01 L5.54,1.95 L4.43,2.4 L2.59,1.52 L1.46,2.65 L2.37,4.46 L1.92,5.55 L0,6.23 L0,7.82 L1.94,8.46 L2.39,9.55 L1.51,11.39 L2.64,12.52 L4.45,11.61 L5.54,12.06 L6.23,13.98 L7.82,13.98 L8.45,12.04 L9.56,11.59 L11.4,12.47 L12.53,11.34 L11.61,9.53 L12.08,8.44 L14,7.75 L14,7.77 Z M7,10 C5.34,10 4,8.66 4,7 C4,5.34 5.34,4 7,4 C8.66,4 10,5.34 10,7 C10,8.66 8.66,10 7,10 Z",displayName:"SettingsIcon"}),Bu=Ut({d:"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z",displayName:"ChevronLeftIcon"}),Gu=Ut({d:"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z",displayName:"ChevronRightIcon"}),Zu=Ut({displayName:"ArrowRightIcon",path:d.jsxs("g",{fill:"currentColor",children:[d.jsx("path",{d:"M13.584,12a2.643,2.643,0,0,1-.775,1.875L3.268,23.416a1.768,1.768,0,0,1-2.5-2.5l8.739-8.739a.25.25,0,0,0,0-.354L.768,3.084a1.768,1.768,0,0,1,2.5-2.5l9.541,9.541A2.643,2.643,0,0,1,13.584,12Z"}),d.jsx("path",{d:"M23.75,12a2.643,2.643,0,0,1-.775,1.875l-9.541,9.541a1.768,1.768,0,0,1-2.5-2.5l8.739-8.739a.25.25,0,0,0,0-.354L10.934,3.084a1.768,1.768,0,0,1,2.5-2.5l9.541,9.541A2.643,2.643,0,0,1,23.75,12Z"})]})}),Wu=Ut({displayName:"ArrowLeftIcon",path:d.jsxs("g",{fill:"currentColor",children:[d.jsx("path",{d:"M10.416,12a2.643,2.643,0,0,1,.775-1.875L20.732.584a1.768,1.768,0,0,1,2.5,2.5l-8.739,8.739a.25.25,0,0,0,0,.354l8.739,8.739a1.768,1.768,0,0,1-2.5,2.5l-9.541-9.541A2.643,2.643,0,0,1,10.416,12Z"}),d.jsx("path",{d:"M.25,12a2.643,2.643,0,0,1,.775-1.875L10.566.584a1.768,1.768,0,0,1,2.5,2.5L4.327,11.823a.25.25,0,0,0,0,.354l8.739,8.739a1.768,1.768,0,0,1-2.5,2.5L1.025,13.875A2.643,2.643,0,0,1,.25,12Z"})]})}),qu=Ut({d:"M12,0A12,12,0,1,0,24,12,12.013,12.013,0,0,0,12,0Zm.25,5a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,12.25,5ZM14.5,18.5h-4a1,1,0,0,1,0-2h.75a.25.25,0,0,0,.25-.25v-4.5a.25.25,0,0,0-.25-.25H10.5a1,1,0,0,1,0-2h1a2,2,0,0,1,2,2v4.75a.25.25,0,0,0,.25.25h.75a1,1,0,1,1,0,2Z"}),To=({onClick:e=Object.create(null),isDisabled:t=!1,isFetching:n=!1,isCompact:r=!0,ml:o=void 0,...i})=>{const{t:s}=pe(),a=Ui();return!r&&a!=="base"&&a!=="sm"?d.jsx(rn,{minWidth:"112px",colorScheme:"gray",onClick:e,rightIcon:d.jsx(Vo,{size:20}),isDisabled:t,isLoading:n,ml:o,...i,children:s("common.refresh")}):d.jsx(qe,{label:s("common.refresh"),children:d.jsx(st,{"aria-label":"refresh",colorScheme:"gray",onClick:e,icon:d.jsx(Vo,{size:20}),isDisabled:t,isLoading:n,ml:o,...i})})},Uu=async(e,t)=>{try{const n={countOnly:!0,platform:e,siteId:t};return(await ae.get("devices",{params:n})).data}catch(n){throw n}},Jm=({enabled:e,platform:t="all",siteId:n})=>{const{t:r}=pe(),o=Wt();return Re(["devices","count",{platform:t,siteId:n}],()=>Uu(t,n),{enabled:e,onError:i=>{var s,a;o.isActive("inventory-fetching-error")||o({id:"inventory-fetching-error",title:r("common.error"),description:r("crud.error_fetching_obj",{obj:r("devices.one"),e:(a=(s=i==null?void 0:i.response)==null?void 0:s.data)==null?void 0:a.ErrorDescription}),status:"error",duration:3,isClosable:!0,position:"top-right"})}})},Ku=async(e,t,n,r,o,i)=>{let s="";i&&i.length>0&&(s=i.map(a=>`${a.id}:${a.sort.charAt(0)}`).join(","));try{const a={deviceWithStatus:!0,limit:e,offset:t,platform:n,...r?{model:r}:{},...o!=null?{siteId:o}:{},...s?{orderBy:s}:{}};return(await ae.get("devices",{params:a})).data}catch(a){throw a}},Qm=({pageInfo:e,sortInfo:t,enabled:n,onError:r,platform:o,model:i,siteId:s})=>{const a=(e==null?void 0:e.limit)!==void 0?e.limit*e.index:0;return Re(["devices","all",{limit:e==null?void 0:e.limit,offset:a,platform:o,model:i,siteId:s,sortInfo:t}],()=>Ku((e==null?void 0:e.limit)||0,a,o??"all",i,s,t),{keepPreviousData:!0,enabled:n&&e!==void 0,staleTime:3e4,onError:r})},Yu=e=>ae.get(`device/${e}/status`).then(t=>t.data),e1=({serialNumber:e,onError:t})=>Re(["device",e,"status"],()=>Yu(e),{enabled:e!==void 0&&e!=="",onError:t}),Xu=async(e,t)=>{try{return(await ae.get(`device/${e}/healthchecks?newest=true&limit=${t??50}`)).data}catch(n){throw n}},t1=({serialNumber:e,onError:t,limit:n})=>Re(["device",e,"healthchecks",{limit:n}],()=>Xu(e,n),{enabled:e!==void 0&&e!=="",keepPreviousData:!0,onError:t}),Ju=async e=>{try{return(await ae.get(`device/${e}`)).data}catch(t){throw t}},n1=({serialNumber:e,onClose:t,disableToast:n=!1})=>{const{t:r}=pe();return Re(["device",e],()=>Ju(e),{staleTime:60*1e3,enabled:e!==void 0&&e!=="",onError:o=>{var i,s,a;n||on.error({content:((i=o==null?void 0:o.response)==null?void 0:i.status)===404?r("devices.not_found_gateway"):r("crud.error_fetching_obj",{e:(a=(s=o==null?void 0:o.response)==null?void 0:s.data)==null?void 0:a.ErrorDescription,obj:r("devices.one")}),duration:3}),t&&t()}})},Qu=async e=>ae.delete(`device/${e}`),r1=({serialNumber:e})=>{const t=bt();return ft(Qu,{onSuccess:()=>{t.invalidateQueries(["devices"]),t.invalidateQueries(["device",e])}})},o1=({serialNumber:e})=>ft(()=>ae.post(`device/${e}/leds`,{serialNumber:e,when:0,pattern:"blink",duration:30})),ec=({serialNumber:e,onClose:t})=>{const{t:n}=pe();return ft(r=>ae.post(`device/${e}/factory`,{serialNumber:e,keepRedirector:r.keepRedirector}),{onSuccess:()=>{on.success(`${n("common.success")}: ${n("commands.factory_reset_success")}`,5),t()},onError:r=>{var o,i;on.error(`${n("common.error")}: ${n("commands.factory_reset_error",{e:((i=(o=r==null?void 0:r.response)==null?void 0:o.data)==null?void 0:i.ErrorDescription)||r.message})}`,5)}})},i1=({serialNumber:e,extraId:t})=>{const{t:n}=pe(),r=Wt();return Re(["get-gateway-device-rtty",e,t],()=>ae.get(`device/${e}/rtty`).then(({data:o})=>o),{enabled:!1,onSuccess:({server:o,viewport:i,connectionId:s})=>{var l;const a=`https://${o}:${i}/connect/${s}`;(l=window.open(a,"_blank"))==null||l.focus()},onError:o=>{var i,s,a;r.isActive("get-gateway-device-rtty-error")||r({id:"get-gateway-device-rtty",title:n("common.error"),description:((i=o==null?void 0:o.response)==null?void 0:i.status)===404?n("devices.not_found_gateway"):n("devices.error_rtty",{e:(a=(s=o==null?void 0:o.response)==null?void 0:s.data)==null?void 0:a.ErrorDescription}),status:"error",duration:3,isClosable:!0,position:"top-right"})}})},tc=async({serialNumber:e,notes:t})=>ae.put(`device/${e}`,{notes:t}),s1=({serialNumber:e})=>{const t=bt();return ft(tc,{onSuccess:()=>{t.invalidateQueries(["devices"]),t.invalidateQueries(["device",e])}})},nc=e=>ae.get(`device/${e}/statistics?lastOnly=true`).then(t=>t.data),a1=({serialNumber:e,onError:t})=>Re(["device",e,"last-statistics"],()=>nc(e),{enabled:e!==void 0&&e!=="",staleTime:1e3*60,refetchInterval:1e3*60,onError:t}),rc=(e,t)=>async()=>ae.get(`device/${t}/statistics?newest=true&limit=${e}`).then(n=>n.data),l1=({serialNumber:e,limit:t,onError:n})=>Re(["deviceStatistics",e,"newest",{limit:t}],rc(t,e),{enabled:e!==void 0&&e!=="",staleTime:1e3*60,onError:n}),oc=e=>async()=>ae.get(`/ouis?macList=${e==null?void 0:e.join(",")}`).then(t=>t.data),u1=({macs:e,onError:t})=>Re(["ouis",e],oc(e),{enabled:e!==void 0&&e.length>0,staleTime:1e3*60,onError:t}),c1=({modalProps:{isOpen:e},confirm:t,cancel:n})=>{const{t:r}=pe(),o=u.useRef(null);return d.jsx(Za,{isOpen:e,onClose:n,leastDestructiveRef:o,isCentered:!0,children:d.jsx(ya,{children:d.jsxs(Wa,{children:[d.jsx(Sa,{children:r("commands.abort_command_title")}),d.jsx(ba,{children:r("commands.abort_command_explanation")}),d.jsxs(qa,{children:[d.jsx(rn,{ref:o,onClick:n,mr:4,children:r("common.cancel")}),d.jsx(rn,{onClick:t,colorScheme:"red",children:r("common.confirm")})]})]})})})},ic=(e,t)=>{var s,a;if(!e||!t)return null;const n=e.split("."),{length:r}=n;if(r<2)return null;const o=n.slice(0,r-1),i=n[r-1];return((a=(s=t[o.slice(0,r-1).join(".")])==null?void 0:s.properties[i??""])==null?void 0:a.description)??null},sc=({definitionKey:e})=>{const{configurationDescriptions:t}=xa(),n=u.useMemo(()=>ic(e,t),[t]);return n?d.jsx(qe,{hasArrow:!0,label:n,children:d.jsx(qu,{ml:2,mb:"2px"})}):null},ac=Ae.memo(sc),lc=({element:e,label:t,value:n,onChange:r,onBlur:o,error:i,isError:s,isRequired:a,isDisabled:l,definitionKey:c})=>d.jsxs(Gi,{isInvalid:s,isRequired:a,isDisabled:l,_disabled:{opacity:.8},children:[d.jsxs(Wi,{ms:"4px",fontSize:"md",fontWeight:"normal",_disabled:{opacity:.8},children:[t," ",d.jsx(ac,{definitionKey:c})]}),e??d.jsx(is,{isChecked:n,onChange:r,onBlur:o,borderRadius:"15px",size:"lg",isDisabled:l,_disabled:{opacity:.8,cursor:"not-allowed"}}),d.jsx(Zi,{children:i})]}),uc=Ae.memo(lc),cc=({name:e,isDisabled:t=!1,label:n,isRequired:r=!1,defaultValue:o,element:i,falseIsUndefined:s,definitionKey:a,onChangeCallback:l})=>{const{value:c,error:f,isError:p,onChange:g,onBlur:m}=Ga({name:e}),h=u.useCallback(v=>{s&&!v.target.checked?g(void 0):g(v.target.checked),l&&l(v.target.checked)},[l]);return d.jsx(uc,{label:n??e,value:c===void 0&&o!==void 0?o:c!==void 0&&c,onChange:h,error:f,onBlur:m,isError:p,isDisabled:t,isRequired:r,element:i,definitionKey:a})},d1=Ae.memo(cc);var He=d.Fragment,M=function(t,n,r){return xi.call(n,"css")?d.jsx(Ci,wi(t,n),r):d.jsx(t,n,r)},_e=function(t,n,r){return xi.call(n,"css")?d.jsxs(Ci,wi(t,n),r):d.jsxs(t,n,r)};const un={black:"#000",white:"#fff"},Vt={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},Tt={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},Ot={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},Dt={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},Lt={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"},Jt={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},dc={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"};function Zt(e){let t="https://mui.com/production-error/?code="+e;for(let n=1;n<arguments.length;n+=1)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified MUI error #"+e+"; visit "+t+" for the full message."}const fc=Object.freeze(Object.defineProperty({__proto__:null,default:Zt},Symbol.toStringTag,{value:"Module"})),cn="$$material";let kr;typeof document=="object"&&(kr=wa({key:"css",prepend:!0}));function gc(e){const{injectFirst:t,children:n}=e;return t&&kr?d.jsx(Ca,{value:kr,children:n}):n}function mc(e){return e==null||Object.keys(e).length===0}function hs(e){const{styles:t,defaultTheme:n={}}=e,r=typeof t=="function"?o=>t(mc(o)?n:o):t;return d.jsx(_a,{styles:r})}function vs(e,t){return Ra(e,t)}const pc=(e,t)=>{Array.isArray(e.__emotion_styles)&&(e.__emotion_styles=t(e.__emotion_styles))},hc=Object.freeze(Object.defineProperty({__proto__:null,GlobalStyles:hs,StyledEngineProvider:gc,ThemeContext:Gr,css:Ea,default:vs,internal_processStyles:pc,keyframes:Ma},Symbol.toStringTag,{value:"Module"}));function mt(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function ys(e){if(u.isValidElement(e)||!mt(e))return e;const t={};return Object.keys(e).forEach(n=>{t[n]=ys(e[n])}),t}function Xe(e,t,n={clone:!0}){const r=n.clone?A({},e):e;return mt(e)&&mt(t)&&Object.keys(t).forEach(o=>{u.isValidElement(t[o])?r[o]=t[o]:mt(t[o])&&Object.prototype.hasOwnProperty.call(e,o)&&mt(e[o])?r[o]=Xe(e[o],t[o],n):n.clone?r[o]=mt(t[o])?ys(t[o]):t[o]:r[o]=t[o]}),r}const vc=Object.freeze(Object.defineProperty({__proto__:null,default:Xe,isPlainObject:mt},Symbol.toStringTag,{value:"Module"})),yc=["values","unit","step"],Sc=e=>{const t=Object.keys(e).map(n=>({key:n,val:e[n]}))||[];return t.sort((n,r)=>n.val-r.val),t.reduce((n,r)=>A({},n,{[r.key]:r.val}),{})};function Ss(e){const{values:t={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:n="px",step:r=5}=e,o=$e(e,yc),i=Sc(t),s=Object.keys(i);function a(g){return`@media (min-width:${typeof t[g]=="number"?t[g]:g}${n})`}function l(g){return`@media (max-width:${(typeof t[g]=="number"?t[g]:g)-r/100}${n})`}function c(g,m){const h=s.indexOf(m);return`@media (min-width:${typeof t[g]=="number"?t[g]:g}${n}) and (max-width:${(h!==-1&&typeof t[s[h]]=="number"?t[s[h]]:m)-r/100}${n})`}function f(g){return s.indexOf(g)+1<s.length?c(g,s[s.indexOf(g)+1]):a(g)}function p(g){const m=s.indexOf(g);return m===0?a(s[1]):m===s.length-1?l(s[m]):c(g,s[s.indexOf(g)+1]).replace("@media","@media not all and")}return A({keys:s,values:i,up:a,down:l,between:c,only:f,not:p,unit:n},o)}const bc={borderRadius:4};function tn(e,t){return t?Xe(e,t,{clone:!1}):e}const Kr={xs:0,sm:600,md:900,lg:1200,xl:1536},Oo={keys:["xs","sm","md","lg","xl"],up:e=>`@media (min-width:${Kr[e]}px)`};function ut(e,t,n){const r=e.theme||{};if(Array.isArray(t)){const i=r.breakpoints||Oo;return t.reduce((s,a,l)=>(s[i.up(i.keys[l])]=n(t[l]),s),{})}if(typeof t=="object"){const i=r.breakpoints||Oo;return Object.keys(t).reduce((s,a)=>{if(Object.keys(i.values||Kr).indexOf(a)!==-1){const l=i.up(a);s[l]=n(t[a],a)}else{const l=a;s[l]=t[l]}return s},{})}return n(t)}function xc(e={}){var t;return((t=e.keys)==null?void 0:t.reduce((r,o)=>{const i=e.up(o);return r[i]={},r},{}))||{}}function Cc(e,t){return e.reduce((n,r)=>{const o=n[r];return(!o||Object.keys(o).length===0)&&delete n[r],n},t)}function Qe(e){if(typeof e!="string")throw new Error(Zt(7));return e.charAt(0).toUpperCase()+e.slice(1)}const wc=Object.freeze(Object.defineProperty({__proto__:null,default:Qe},Symbol.toStringTag,{value:"Module"}));function Nn(e,t,n=!0){if(!t||typeof t!="string")return null;if(e&&e.vars&&n){const r=`vars.${t}`.split(".").reduce((o,i)=>o&&o[i]?o[i]:null,e);if(r!=null)return r}return t.split(".").reduce((r,o)=>r&&r[o]!=null?r[o]:null,e)}function Vn(e,t,n,r=n){let o;return typeof e=="function"?o=e(n):Array.isArray(e)?o=e[n]||r:o=Nn(e,n)||r,t&&(o=t(o,r,e)),o}function fe(e){const{prop:t,cssProperty:n=e.prop,themeKey:r,transform:o}=e,i=s=>{if(s[t]==null)return null;const a=s[t],l=s.theme,c=Nn(l,r)||{};return ut(s,a,p=>{let g=Vn(c,o,p);return p===g&&typeof p=="string"&&(g=Vn(c,o,`${t}${p==="default"?"":Qe(p)}`,p)),n===!1?g:{[n]:g}})};return i.propTypes={},i.filterProps=[t],i}function _c(e){const t={};return n=>(t[n]===void 0&&(t[n]=e(n)),t[n])}const Rc={m:"margin",p:"padding"},Ec={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},Do={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Mc=_c(e=>{if(e.length>2)if(Do[e])e=Do[e];else return[e];const[t,n]=e.split(""),r=Rc[t],o=Ec[n]||"";return Array.isArray(o)?o.map(i=>r+i):[r+o]}),Yr=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],Xr=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...Yr,...Xr];function pn(e,t,n,r){var o;const i=(o=Nn(e,t,!1))!=null?o:n;return typeof i=="number"?s=>typeof s=="string"?s:i*s:Array.isArray(i)?s=>typeof s=="string"?s:i[s]:typeof i=="function"?i:()=>{}}function bs(e){return pn(e,"spacing",8)}function hn(e,t){if(typeof t=="string"||t==null)return t;const n=Math.abs(t),r=e(n);return t>=0?r:typeof r=="number"?-r:`-${r}`}function Pc(e,t){return n=>e.reduce((r,o)=>(r[o]=hn(t,n),r),{})}function $c(e,t,n,r){if(t.indexOf(n)===-1)return null;const o=Mc(n),i=Pc(o,r),s=e[n];return ut(e,s,i)}function xs(e,t){const n=bs(e.theme);return Object.keys(e).map(r=>$c(e,t,r,n)).reduce(tn,{})}function ue(e){return xs(e,Yr)}ue.propTypes={};ue.filterProps=Yr;function ce(e){return xs(e,Xr)}ce.propTypes={};ce.filterProps=Xr;function Fc(e=8){if(e.mui)return e;const t=bs({spacing:e}),n=(...r)=>(r.length===0?[1]:r).map(i=>{const s=t(i);return typeof s=="number"?`${s}px`:s}).join(" ");return n.mui=!0,n}function Bn(...e){const t=e.reduce((r,o)=>(o.filterProps.forEach(i=>{r[i]=o}),r),{}),n=r=>Object.keys(r).reduce((o,i)=>t[i]?tn(o,t[i](r)):o,{});return n.propTypes={},n.filterProps=e.reduce((r,o)=>r.concat(o.filterProps),[]),n}function ze(e){return typeof e!="number"?e:`${e}px solid`}function Ge(e,t){return fe({prop:e,themeKey:"borders",transform:t})}const kc=Ge("border",ze),jc=Ge("borderTop",ze),Ac=Ge("borderRight",ze),Ic=Ge("borderBottom",ze),Vc=Ge("borderLeft",ze),Tc=Ge("borderColor"),Oc=Ge("borderTopColor"),Dc=Ge("borderRightColor"),Lc=Ge("borderBottomColor"),Hc=Ge("borderLeftColor"),zc=Ge("outline",ze),Nc=Ge("outlineColor"),Gn=e=>{if(e.borderRadius!==void 0&&e.borderRadius!==null){const t=pn(e.theme,"shape.borderRadius",4),n=r=>({borderRadius:hn(t,r)});return ut(e,e.borderRadius,n)}return null};Gn.propTypes={};Gn.filterProps=["borderRadius"];Bn(kc,jc,Ac,Ic,Vc,Tc,Oc,Dc,Lc,Hc,Gn,zc,Nc);const Zn=e=>{if(e.gap!==void 0&&e.gap!==null){const t=pn(e.theme,"spacing",8),n=r=>({gap:hn(t,r)});return ut(e,e.gap,n)}return null};Zn.propTypes={};Zn.filterProps=["gap"];const Wn=e=>{if(e.columnGap!==void 0&&e.columnGap!==null){const t=pn(e.theme,"spacing",8),n=r=>({columnGap:hn(t,r)});return ut(e,e.columnGap,n)}return null};Wn.propTypes={};Wn.filterProps=["columnGap"];const qn=e=>{if(e.rowGap!==void 0&&e.rowGap!==null){const t=pn(e.theme,"spacing",8),n=r=>({rowGap:hn(t,r)});return ut(e,e.rowGap,n)}return null};qn.propTypes={};qn.filterProps=["rowGap"];const Bc=fe({prop:"gridColumn"}),Gc=fe({prop:"gridRow"}),Zc=fe({prop:"gridAutoFlow"}),Wc=fe({prop:"gridAutoColumns"}),qc=fe({prop:"gridAutoRows"}),Uc=fe({prop:"gridTemplateColumns"}),Kc=fe({prop:"gridTemplateRows"}),Yc=fe({prop:"gridTemplateAreas"}),Xc=fe({prop:"gridArea"});Bn(Zn,Wn,qn,Bc,Gc,Zc,Wc,qc,Uc,Kc,Yc,Xc);function Bt(e,t){return t==="grey"?t:e}const Jc=fe({prop:"color",themeKey:"palette",transform:Bt}),Qc=fe({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:Bt}),ed=fe({prop:"backgroundColor",themeKey:"palette",transform:Bt});Bn(Jc,Qc,ed);function Ve(e){return e<=1&&e!==0?`${e*100}%`:e}const td=fe({prop:"width",transform:Ve}),Jr=e=>{if(e.maxWidth!==void 0&&e.maxWidth!==null){const t=n=>{var r,o;const i=((r=e.theme)==null||(r=r.breakpoints)==null||(r=r.values)==null?void 0:r[n])||Kr[n];return i?((o=e.theme)==null||(o=o.breakpoints)==null?void 0:o.unit)!=="px"?{maxWidth:`${i}${e.theme.breakpoints.unit}`}:{maxWidth:i}:{maxWidth:Ve(n)}};return ut(e,e.maxWidth,t)}return null};Jr.filterProps=["maxWidth"];const nd=fe({prop:"minWidth",transform:Ve}),rd=fe({prop:"height",transform:Ve}),od=fe({prop:"maxHeight",transform:Ve}),id=fe({prop:"minHeight",transform:Ve});fe({prop:"size",cssProperty:"width",transform:Ve});fe({prop:"size",cssProperty:"height",transform:Ve});const sd=fe({prop:"boxSizing"});Bn(td,Jr,nd,rd,od,id,sd);const vn={border:{themeKey:"borders",transform:ze},borderTop:{themeKey:"borders",transform:ze},borderRight:{themeKey:"borders",transform:ze},borderBottom:{themeKey:"borders",transform:ze},borderLeft:{themeKey:"borders",transform:ze},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:ze},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Gn},color:{themeKey:"palette",transform:Bt},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:Bt},backgroundColor:{themeKey:"palette",transform:Bt},p:{style:ce},pt:{style:ce},pr:{style:ce},pb:{style:ce},pl:{style:ce},px:{style:ce},py:{style:ce},padding:{style:ce},paddingTop:{style:ce},paddingRight:{style:ce},paddingBottom:{style:ce},paddingLeft:{style:ce},paddingX:{style:ce},paddingY:{style:ce},paddingInline:{style:ce},paddingInlineStart:{style:ce},paddingInlineEnd:{style:ce},paddingBlock:{style:ce},paddingBlockStart:{style:ce},paddingBlockEnd:{style:ce},m:{style:ue},mt:{style:ue},mr:{style:ue},mb:{style:ue},ml:{style:ue},mx:{style:ue},my:{style:ue},margin:{style:ue},marginTop:{style:ue},marginRight:{style:ue},marginBottom:{style:ue},marginLeft:{style:ue},marginX:{style:ue},marginY:{style:ue},marginInline:{style:ue},marginInlineStart:{style:ue},marginInlineEnd:{style:ue},marginBlock:{style:ue},marginBlockStart:{style:ue},marginBlockEnd:{style:ue},displayPrint:{cssProperty:!1,transform:e=>({"@media print":{display:e}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:Zn},rowGap:{style:qn},columnGap:{style:Wn},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:Ve},maxWidth:{style:Jr},minWidth:{transform:Ve},height:{transform:Ve},maxHeight:{transform:Ve},minHeight:{transform:Ve},boxSizing:{},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function ad(...e){const t=e.reduce((r,o)=>r.concat(Object.keys(o)),[]),n=new Set(t);return e.every(r=>n.size===Object.keys(r).length)}function ld(e,t){return typeof e=="function"?e(t):e}function Cs(){function e(n,r,o,i){const s={[n]:r,theme:o},a=i[n];if(!a)return{[n]:r};const{cssProperty:l=n,themeKey:c,transform:f,style:p}=a;if(r==null)return null;if(c==="typography"&&r==="inherit")return{[n]:r};const g=Nn(o,c)||{};return p?p(s):ut(s,r,h=>{let v=Vn(g,f,h);return h===v&&typeof h=="string"&&(v=Vn(g,f,`${n}${h==="default"?"":Qe(h)}`,h)),l===!1?v:{[l]:v}})}function t(n){var r;const{sx:o,theme:i={}}=n||{};if(!o)return null;const s=(r=i.unstable_sxConfig)!=null?r:vn;function a(l){let c=l;if(typeof l=="function")c=l(i);else if(typeof l!="object")return l;if(!c)return null;const f=xc(i.breakpoints),p=Object.keys(f);let g=f;return Object.keys(c).forEach(m=>{const h=ld(c[m],i);if(h!=null)if(typeof h=="object")if(s[m])g=tn(g,e(m,h,i,s));else{const v=ut({theme:i},h,y=>({[m]:y}));ad(v,h)?g[m]=t({sx:h,theme:i}):g=tn(g,v)}else g=tn(g,e(m,h,i,s))}),Cc(p,g)}return Array.isArray(o)?o.map(a):a(o)}return t}const yn=Cs();yn.filterProps=["sx"];function ws(e,t){const n=this;return n.vars&&typeof n.getColorSchemeSelector=="function"?{[n.getColorSchemeSelector(e).replace(/(\[[^\]]+\])/,"*:where($1)")]:t}:n.palette.mode===e?t:{}}const ud=["breakpoints","palette","spacing","shape"];function Qr(e={},...t){const{breakpoints:n={},palette:r={},spacing:o,shape:i={}}=e,s=$e(e,ud),a=Ss(n),l=Fc(o);let c=Xe({breakpoints:a,direction:"ltr",components:{},palette:A({mode:"light"},r),spacing:l,shape:A({},bc,i)},s);return c.applyStyles=ws,c=t.reduce((f,p)=>Xe(f,p),c),c.unstable_sxConfig=A({},vn,s==null?void 0:s.unstable_sxConfig),c.unstable_sx=function(p){return yn({sx:p,theme:this})},c}const cd=Object.freeze(Object.defineProperty({__proto__:null,default:Qr,private_createBreakpoints:Ss,unstable_applyStyles:ws},Symbol.toStringTag,{value:"Module"}));function dd(e){return Object.keys(e).length===0}function _s(e=null){const t=u.useContext(Gr);return!t||dd(t)?e:t}const fd=Qr();function Rs(e=fd){return _s(e)}function gd({styles:e,themeId:t,defaultTheme:n={}}){const r=Rs(n),o=typeof e=="function"?e(t&&r[t]||r):e;return d.jsx(hs,{styles:o})}const md=["sx"],pd=e=>{var t,n;const r={systemProps:{},otherProps:{}},o=(t=e==null||(n=e.theme)==null?void 0:n.unstable_sxConfig)!=null?t:vn;return Object.keys(e).forEach(i=>{o[i]?r.systemProps[i]=e[i]:r.otherProps[i]=e[i]}),r};function Es(e){const{sx:t}=e,n=$e(e,md),{systemProps:r,otherProps:o}=pd(n);let i;return Array.isArray(t)?i=[r,...t]:typeof t=="function"?i=(...s)=>{const a=t(...s);return mt(a)?A({},r,a):r}:i=A({},r,t),A({},o,{sx:i})}const hd=Object.freeze(Object.defineProperty({__proto__:null,default:yn,extendSxProp:Es,unstable_createStyleFunctionSx:Cs,unstable_defaultSxConfig:vn},Symbol.toStringTag,{value:"Module"})),Lo=e=>e,vd=()=>{let e=Lo;return{configure(t){e=t},generate(t){return e(t)},reset(){e=Lo}}},Ms=vd(),yd=["className","component"];function Sd(e={}){const{themeId:t,defaultTheme:n,defaultClassName:r="MuiBox-root",generateClassName:o}=e,i=vs("div",{shouldForwardProp:a=>a!=="theme"&&a!=="sx"&&a!=="as"})(yn);return u.forwardRef(function(l,c){const f=Rs(n),p=Es(l),{className:g,component:m="div"}=p,h=$e(p,yd);return d.jsx(i,A({as:m,ref:c,className:an(g,o?o(r):r),theme:t&&f[t]||f},h))})}const bd={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function Un(e,t,n="Mui"){const r=bd[t];return r?`${n}-${r}`:`${Ms.generate(e)}-${t}`}function Kn(e,t,n="Mui"){const r={};return t.forEach(o=>{r[o]=Un(e,o,n)}),r}var Ps={exports:{}},re={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var eo=Symbol.for("react.transitional.element"),to=Symbol.for("react.portal"),Yn=Symbol.for("react.fragment"),Xn=Symbol.for("react.strict_mode"),Jn=Symbol.for("react.profiler"),Qn=Symbol.for("react.consumer"),er=Symbol.for("react.context"),tr=Symbol.for("react.forward_ref"),nr=Symbol.for("react.suspense"),rr=Symbol.for("react.suspense_list"),or=Symbol.for("react.memo"),ir=Symbol.for("react.lazy"),xd=Symbol.for("react.view_transition"),Cd=Symbol.for("react.client.reference");function Ze(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case eo:switch(e=e.type,e){case Yn:case Jn:case Xn:case nr:case rr:case xd:return e;default:switch(e=e&&e.$$typeof,e){case er:case tr:case ir:case or:return e;case Qn:return e;default:return t}}case to:return t}}}re.ContextConsumer=Qn;re.ContextProvider=er;re.Element=eo;re.ForwardRef=tr;re.Fragment=Yn;re.Lazy=ir;re.Memo=or;re.Portal=to;re.Profiler=Jn;re.StrictMode=Xn;re.Suspense=nr;re.SuspenseList=rr;re.isContextConsumer=function(e){return Ze(e)===Qn};re.isContextProvider=function(e){return Ze(e)===er};re.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===eo};re.isForwardRef=function(e){return Ze(e)===tr};re.isFragment=function(e){return Ze(e)===Yn};re.isLazy=function(e){return Ze(e)===ir};re.isMemo=function(e){return Ze(e)===or};re.isPortal=function(e){return Ze(e)===to};re.isProfiler=function(e){return Ze(e)===Jn};re.isStrictMode=function(e){return Ze(e)===Xn};re.isSuspense=function(e){return Ze(e)===nr};re.isSuspenseList=function(e){return Ze(e)===rr};re.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===Yn||e===Jn||e===Xn||e===nr||e===rr||typeof e=="object"&&e!==null&&(e.$$typeof===ir||e.$$typeof===or||e.$$typeof===er||e.$$typeof===Qn||e.$$typeof===tr||e.$$typeof===Cd||e.getModuleId!==void 0)};re.typeOf=Ze;Ps.exports=re;var Ho=Ps.exports;const wd=/^\s*function(?:\s|\s*\/\*.*\*\/\s*)+([^(\s/]*)\s*/;function $s(e){const t=`${e}`.match(wd);return t&&t[1]||""}function Fs(e,t=""){return e.displayName||e.name||$s(e)||t}function zo(e,t,n){const r=Fs(t);return e.displayName||(r!==""?`${n}(${r})`:n)}function _d(e){if(e!=null){if(typeof e=="string")return e;if(typeof e=="function")return Fs(e,"Component");if(typeof e=="object")switch(e.$$typeof){case Ho.ForwardRef:return zo(e,e.render,"ForwardRef");case Ho.Memo:return zo(e,e.type,"memo");default:return}}}const Rd=Object.freeze(Object.defineProperty({__proto__:null,default:_d,getFunctionName:$s},Symbol.toStringTag,{value:"Module"}));function jr(e,t){const n=A({},t);return Object.keys(e).forEach(r=>{if(r.toString().match(/^(components|slots)$/))n[r]=A({},e[r],n[r]);else if(r.toString().match(/^(componentsProps|slotProps)$/)){const o=e[r]||{},i=t[r];n[r]={},!i||!Object.keys(i)?n[r]=o:!o||!Object.keys(o)?n[r]=i:(n[r]=A({},i),Object.keys(o).forEach(s=>{n[r][s]=jr(o[s],i[s])}))}else n[r]===void 0&&(n[r]=e[r])}),n}const dn=typeof window<"u"?u.useLayoutEffect:u.useEffect;function Ed(e,t=Number.MIN_SAFE_INTEGER,n=Number.MAX_SAFE_INTEGER){return Math.max(t,Math.min(e,n))}const Md=Object.freeze(Object.defineProperty({__proto__:null,default:Ed},Symbol.toStringTag,{value:"Module"}));function Pd(e,t=166){let n;function r(...o){const i=()=>{e.apply(this,o)};clearTimeout(n),n=setTimeout(i,t)}return r.clear=()=>{clearTimeout(n)},r}function $d(e){return e&&e.ownerDocument||document}function No(e){return $d(e).defaultView||window}function Fd(e,t){typeof e=="function"?e(t):e&&(e.current=t)}function kd(e){const t=u.useRef(e);return dn(()=>{t.current=e}),u.useRef((...n)=>(0,t.current)(...n)).current}function ks(...e){return u.useMemo(()=>e.every(t=>t==null)?null:t=>{e.forEach(n=>{Fd(n,t)})},e)}function no(e,t,n=void 0){const r={};return Object.keys(e).forEach(o=>{r[o]=e[o].reduce((i,s)=>{if(s){const a=t(s);a!==""&&i.push(a),n&&n[s]&&i.push(n[s])}return i},[]).join(" ")}),r}function Bo(e){return typeof e=="string"}const js=u.createContext(null);function As(){return u.useContext(js)}const jd=typeof Symbol=="function"&&Symbol.for,Ad=jd?Symbol.for("mui.nested"):"__THEME_NESTED__";function Id(e,t){return typeof t=="function"?t(e):A({},e,t)}function Vd(e){const{children:t,theme:n}=e,r=As(),o=u.useMemo(()=>{const i=r===null?n:Id(r,n);return i!=null&&(i[Ad]=r!==null),i},[n,r]);return d.jsx(js.Provider,{value:o,children:t})}const Td=["value"],Od=u.createContext();function Dd(e){let{value:t}=e,n=$e(e,Td);return d.jsx(Od.Provider,A({value:t??!0},n))}const Is=u.createContext(void 0);function Ld({value:e,children:t}){return d.jsx(Is.Provider,{value:e,children:t})}function Hd(e){const{theme:t,name:n,props:r}=e;if(!t||!t.components||!t.components[n])return r;const o=t.components[n];return o.defaultProps?jr(o.defaultProps,r):!o.styleOverrides&&!o.variants?jr(o,r):r}function zd({props:e,name:t}){const n=u.useContext(Is);return Hd({props:e,name:t,theme:{components:n}})}const Go={};function Zo(e,t,n,r=!1){return u.useMemo(()=>{const o=e&&t[e]||t;if(typeof n=="function"){const i=n(o),s=e?A({},t,{[e]:i}):i;return r?()=>s:s}return e?A({},t,{[e]:n}):A({},t,n)},[e,t,n,r])}function Nd(e){const{children:t,theme:n,themeId:r}=e,o=_s(Go),i=As()||Go,s=Zo(r,o,n),a=Zo(r,i,n,!0),l=s.direction==="rtl";return d.jsx(Vd,{theme:a,children:d.jsx(Gr.Provider,{value:s,children:d.jsx(Dd,{value:l,children:d.jsx(Ld,{value:s==null?void 0:s.components,children:t})})})})}function Bd(e,t){return A({toolbar:{minHeight:56,[e.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[e.up("sm")]:{minHeight:64}}},t)}var ge={};const Gd=xt(fc),Zd=xt(Md);var Vs=_i;Object.defineProperty(ge,"__esModule",{value:!0});var Wo=ge.alpha=Ls;ge.blend=o0;ge.colorChannel=void 0;var Wd=ge.darken=oo;ge.decomposeColor=Be;ge.emphasize=Hs;var qd=ge.getContrastRatio=Qd;ge.getLuminance=Tn;ge.hexToRgb=Ts;ge.hslToRgb=Ds;var Ud=ge.lighten=io;ge.private_safeAlpha=e0;ge.private_safeColorChannel=void 0;ge.private_safeDarken=t0;ge.private_safeEmphasize=r0;ge.private_safeLighten=n0;ge.recomposeColor=Kt;ge.rgbToHex=Jd;var qo=Vs(Gd),Kd=Vs(Zd);function ro(e,t=0,n=1){return(0,Kd.default)(e,t,n)}function Ts(e){e=e.slice(1);const t=new RegExp(`.{1,${e.length>=6?2:1}}`,"g");let n=e.match(t);return n&&n[0].length===1&&(n=n.map(r=>r+r)),n?`rgb${n.length===4?"a":""}(${n.map((r,o)=>o<3?parseInt(r,16):Math.round(parseInt(r,16)/255*1e3)/1e3).join(", ")})`:""}function Yd(e){const t=e.toString(16);return t.length===1?`0${t}`:t}function Be(e){if(e.type)return e;if(e.charAt(0)==="#")return Be(Ts(e));const t=e.indexOf("("),n=e.substring(0,t);if(["rgb","rgba","hsl","hsla","color"].indexOf(n)===-1)throw new Error((0,qo.default)(9,e));let r=e.substring(t+1,e.length-1),o;if(n==="color"){if(r=r.split(" "),o=r.shift(),r.length===4&&r[3].charAt(0)==="/"&&(r[3]=r[3].slice(1)),["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].indexOf(o)===-1)throw new Error((0,qo.default)(10,o))}else r=r.split(",");return r=r.map(i=>parseFloat(i)),{type:n,values:r,colorSpace:o}}const Os=e=>{const t=Be(e);return t.values.slice(0,3).map((n,r)=>t.type.indexOf("hsl")!==-1&&r!==0?`${n}%`:n).join(" ")};ge.colorChannel=Os;const Xd=(e,t)=>{try{return Os(e)}catch{return e}};ge.private_safeColorChannel=Xd;function Kt(e){const{type:t,colorSpace:n}=e;let{values:r}=e;return t.indexOf("rgb")!==-1?r=r.map((o,i)=>i<3?parseInt(o,10):o):t.indexOf("hsl")!==-1&&(r[1]=`${r[1]}%`,r[2]=`${r[2]}%`),t.indexOf("color")!==-1?r=`${n} ${r.join(" ")}`:r=`${r.join(", ")}`,`${t}(${r})`}function Jd(e){if(e.indexOf("#")===0)return e;const{values:t}=Be(e);return`#${t.map((n,r)=>Yd(r===3?Math.round(255*n):n)).join("")}`}function Ds(e){e=Be(e);const{values:t}=e,n=t[0],r=t[1]/100,o=t[2]/100,i=r*Math.min(o,1-o),s=(c,f=(c+n/30)%12)=>o-i*Math.max(Math.min(f-3,9-f,1),-1);let a="rgb";const l=[Math.round(s(0)*255),Math.round(s(8)*255),Math.round(s(4)*255)];return e.type==="hsla"&&(a+="a",l.push(t[3])),Kt({type:a,values:l})}function Tn(e){e=Be(e);let t=e.type==="hsl"||e.type==="hsla"?Be(Ds(e)).values:e.values;return t=t.map(n=>(e.type!=="color"&&(n/=255),n<=.03928?n/12.92:((n+.055)/1.055)**2.4)),Number((.2126*t[0]+.7152*t[1]+.0722*t[2]).toFixed(3))}function Qd(e,t){const n=Tn(e),r=Tn(t);return(Math.max(n,r)+.05)/(Math.min(n,r)+.05)}function Ls(e,t){return e=Be(e),t=ro(t),(e.type==="rgb"||e.type==="hsl")&&(e.type+="a"),e.type==="color"?e.values[3]=`/${t}`:e.values[3]=t,Kt(e)}function e0(e,t,n){try{return Ls(e,t)}catch{return e}}function oo(e,t){if(e=Be(e),t=ro(t),e.type.indexOf("hsl")!==-1)e.values[2]*=1-t;else if(e.type.indexOf("rgb")!==-1||e.type.indexOf("color")!==-1)for(let n=0;n<3;n+=1)e.values[n]*=1-t;return Kt(e)}function t0(e,t,n){try{return oo(e,t)}catch{return e}}function io(e,t){if(e=Be(e),t=ro(t),e.type.indexOf("hsl")!==-1)e.values[2]+=(100-e.values[2])*t;else if(e.type.indexOf("rgb")!==-1)for(let n=0;n<3;n+=1)e.values[n]+=(255-e.values[n])*t;else if(e.type.indexOf("color")!==-1)for(let n=0;n<3;n+=1)e.values[n]+=(1-e.values[n])*t;return Kt(e)}function n0(e,t,n){try{return io(e,t)}catch{return e}}function Hs(e,t=.15){return Tn(e)>.5?oo(e,t):io(e,t)}function r0(e,t,n){try{return Hs(e,t)}catch{return e}}function o0(e,t,n,r=1){const o=(l,c)=>Math.round((l**(1/r)*(1-n)+c**(1/r)*n)**r),i=Be(e),s=Be(t),a=[o(i.values[0],s.values[0]),o(i.values[1],s.values[1]),o(i.values[2],s.values[2])];return Kt({type:"rgb",values:a})}const i0=["mode","contrastThreshold","tonalOffset"],Uo={text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:un.white,default:un.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}},pr={text:{primary:un.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:un.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}};function Ko(e,t,n,r){const o=r.light||r,i=r.dark||r*1.5;e[t]||(e.hasOwnProperty(n)?e[t]=e[n]:t==="light"?e.light=Ud(e.main,o):t==="dark"&&(e.dark=Wd(e.main,i)))}function s0(e="light"){return e==="dark"?{main:Ot[200],light:Ot[50],dark:Ot[400]}:{main:Ot[700],light:Ot[400],dark:Ot[800]}}function a0(e="light"){return e==="dark"?{main:Tt[200],light:Tt[50],dark:Tt[400]}:{main:Tt[500],light:Tt[300],dark:Tt[700]}}function l0(e="light"){return e==="dark"?{main:Vt[500],light:Vt[300],dark:Vt[700]}:{main:Vt[700],light:Vt[400],dark:Vt[800]}}function u0(e="light"){return e==="dark"?{main:Dt[400],light:Dt[300],dark:Dt[700]}:{main:Dt[700],light:Dt[500],dark:Dt[900]}}function c0(e="light"){return e==="dark"?{main:Lt[400],light:Lt[300],dark:Lt[700]}:{main:Lt[800],light:Lt[500],dark:Lt[900]}}function d0(e="light"){return e==="dark"?{main:Jt[400],light:Jt[300],dark:Jt[700]}:{main:"#ed6c02",light:Jt[500],dark:Jt[900]}}function f0(e){const{mode:t="light",contrastThreshold:n=3,tonalOffset:r=.2}=e,o=$e(e,i0),i=e.primary||s0(t),s=e.secondary||a0(t),a=e.error||l0(t),l=e.info||u0(t),c=e.success||c0(t),f=e.warning||d0(t);function p(v){return qd(v,pr.text.primary)>=n?pr.text.primary:Uo.text.primary}const g=({color:v,name:y,mainShade:S=500,lightShade:x=300,darkShade:C=700})=>{if(v=A({},v),!v.main&&v[S]&&(v.main=v[S]),!v.hasOwnProperty("main"))throw new Error(Zt(11,y?` (${y})`:"",S));if(typeof v.main!="string")throw new Error(Zt(12,y?` (${y})`:"",JSON.stringify(v.main)));return Ko(v,"light",x,r),Ko(v,"dark",C,r),v.contrastText||(v.contrastText=p(v.main)),v},m={dark:pr,light:Uo};return Xe(A({common:A({},un),mode:t,primary:g({color:i,name:"primary"}),secondary:g({color:s,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:g({color:a,name:"error"}),warning:g({color:f,name:"warning"}),info:g({color:l,name:"info"}),success:g({color:c,name:"success"}),grey:dc,contrastThreshold:n,getContrastText:p,augmentColor:g,tonalOffset:r},m[t]),o)}const g0=["fontFamily","fontSize","fontWeightLight","fontWeightRegular","fontWeightMedium","fontWeightBold","htmlFontSize","allVariants","pxToRem"];function m0(e){return Math.round(e*1e5)/1e5}const Yo={textTransform:"uppercase"},Xo='"Roboto", "Helvetica", "Arial", sans-serif';function p0(e,t){const n=typeof t=="function"?t(e):t,{fontFamily:r=Xo,fontSize:o=14,fontWeightLight:i=300,fontWeightRegular:s=400,fontWeightMedium:a=500,fontWeightBold:l=700,htmlFontSize:c=16,allVariants:f,pxToRem:p}=n,g=$e(n,g0),m=o/14,h=p||(S=>`${S/c*m}rem`),v=(S,x,C,_,P)=>A({fontFamily:r,fontWeight:S,fontSize:h(x),lineHeight:C},r===Xo?{letterSpacing:`${m0(_/x)}em`}:{},P,f),y={h1:v(i,96,1.167,-1.5),h2:v(i,60,1.2,-.5),h3:v(s,48,1.167,0),h4:v(s,34,1.235,.25),h5:v(s,24,1.334,0),h6:v(a,20,1.6,.15),subtitle1:v(s,16,1.75,.15),subtitle2:v(a,14,1.57,.1),body1:v(s,16,1.5,.15),body2:v(s,14,1.43,.15),button:v(a,14,1.75,.4,Yo),caption:v(s,12,1.66,.4),overline:v(s,12,2.66,1,Yo),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return Xe(A({htmlFontSize:c,pxToRem:h,fontFamily:r,fontSize:o,fontWeightLight:i,fontWeightRegular:s,fontWeightMedium:a,fontWeightBold:l},y),g,{clone:!1})}const h0=.2,v0=.14,y0=.12;function se(...e){return[`${e[0]}px ${e[1]}px ${e[2]}px ${e[3]}px rgba(0,0,0,${h0})`,`${e[4]}px ${e[5]}px ${e[6]}px ${e[7]}px rgba(0,0,0,${v0})`,`${e[8]}px ${e[9]}px ${e[10]}px ${e[11]}px rgba(0,0,0,${y0})`].join(",")}const S0=["none",se(0,2,1,-1,0,1,1,0,0,1,3,0),se(0,3,1,-2,0,2,2,0,0,1,5,0),se(0,3,3,-2,0,3,4,0,0,1,8,0),se(0,2,4,-1,0,4,5,0,0,1,10,0),se(0,3,5,-1,0,5,8,0,0,1,14,0),se(0,3,5,-1,0,6,10,0,0,1,18,0),se(0,4,5,-2,0,7,10,1,0,2,16,1),se(0,5,5,-3,0,8,10,1,0,3,14,2),se(0,5,6,-3,0,9,12,1,0,3,16,2),se(0,6,6,-3,0,10,14,1,0,4,18,3),se(0,6,7,-4,0,11,15,1,0,4,20,3),se(0,7,8,-4,0,12,17,2,0,5,22,4),se(0,7,8,-4,0,13,19,2,0,5,24,4),se(0,7,9,-4,0,14,21,2,0,5,26,4),se(0,8,9,-5,0,15,22,2,0,6,28,5),se(0,8,10,-5,0,16,24,2,0,6,30,5),se(0,8,11,-5,0,17,26,2,0,6,32,5),se(0,9,11,-5,0,18,28,2,0,7,34,6),se(0,9,12,-6,0,19,29,2,0,7,36,6),se(0,10,13,-6,0,20,31,3,0,8,38,7),se(0,10,13,-6,0,21,33,3,0,8,40,7),se(0,10,14,-6,0,22,35,3,0,8,42,7),se(0,11,14,-7,0,23,36,3,0,9,44,8),se(0,11,15,-7,0,24,38,3,0,9,46,8)],b0=["duration","easing","delay"],x0={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},C0={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function Jo(e){return`${Math.round(e)}ms`}function w0(e){if(!e)return 0;const t=e/36;return Math.round((4+15*t**.25+t/5)*10)}function _0(e){const t=A({},x0,e.easing),n=A({},C0,e.duration);return A({getAutoHeightDuration:w0,create:(o=["all"],i={})=>{const{duration:s=n.standard,easing:a=t.easeInOut,delay:l=0}=i;return $e(i,b0),(Array.isArray(o)?o:[o]).map(c=>`${c} ${typeof s=="string"?s:Jo(s)} ${a} ${typeof l=="string"?l:Jo(l)}`).join(",")}},e,{easing:t,duration:n})}const R0={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500},E0=["breakpoints","mixins","spacing","palette","transitions","typography","shape"];function so(e={},...t){const{mixins:n={},palette:r={},transitions:o={},typography:i={}}=e,s=$e(e,E0);if(e.vars&&e.generateCssVars===void 0)throw new Error(Zt(18));const a=f0(r),l=Qr(e);let c=Xe(l,{mixins:Bd(l.breakpoints,n),palette:a,shadows:S0.slice(),typography:p0(a,i),transitions:_0(o),zIndex:A({},R0)});return c=Xe(c,s),c=t.reduce((f,p)=>Xe(f,p),c),c.unstable_sxConfig=A({},vn,s==null?void 0:s.unstable_sxConfig),c.unstable_sx=function(p){return yn({sx:p,theme:this})},c}const zs=so();var Sn={},hr={exports:{}},Qo;function M0(){return Qo||(Qo=1,function(e){function t(n,r){if(n==null)return{};var o={};for(var i in n)if({}.hasOwnProperty.call(n,i)){if(r.indexOf(i)!==-1)continue;o[i]=n[i]}return o}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(hr)),hr.exports}const P0=xt(hc),$0=xt(vc),F0=xt(wc),k0=xt(Rd),j0=xt(cd),A0=xt(hd);var Yt=_i;Object.defineProperty(Sn,"__esModule",{value:!0});var I0=Sn.default=q0;Sn.shouldForwardProp=An;Sn.systemDefaultTheme=void 0;var Le=Yt(Pa),Ar=Yt(M0()),ei=z0(P0),V0=$0;Yt(F0);Yt(k0);var T0=Yt(j0),O0=Yt(A0);const D0=["ownerState"],L0=["variants"],H0=["name","slot","skipVariantsResolver","skipSx","overridesResolver"];function Ns(e){if(typeof WeakMap!="function")return null;var t=new WeakMap,n=new WeakMap;return(Ns=function(r){return r?n:t})(e)}function z0(e,t){if(e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var n=Ns(t);if(n&&n.has(e))return n.get(e);var r={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(i!=="default"&&Object.prototype.hasOwnProperty.call(e,i)){var s=o?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(r,i,s):r[i]=e[i]}return r.default=e,n&&n.set(e,r),r}function N0(e){return Object.keys(e).length===0}function B0(e){return typeof e=="string"&&e.charCodeAt(0)>96}function An(e){return e!=="ownerState"&&e!=="theme"&&e!=="sx"&&e!=="as"}const G0=Sn.systemDefaultTheme=(0,T0.default)(),Z0=e=>e&&e.charAt(0).toLowerCase()+e.slice(1);function _n({defaultTheme:e,theme:t,themeId:n}){return N0(t)?e:t[n]||t}function W0(e){return e?(t,n)=>n[e]:null}function In(e,t){let{ownerState:n}=t,r=(0,Ar.default)(t,D0);const o=typeof e=="function"?e((0,Le.default)({ownerState:n},r)):e;if(Array.isArray(o))return o.flatMap(i=>In(i,(0,Le.default)({ownerState:n},r)));if(o&&typeof o=="object"&&Array.isArray(o.variants)){const{variants:i=[]}=o;let a=(0,Ar.default)(o,L0);return i.forEach(l=>{let c=!0;typeof l.props=="function"?c=l.props((0,Le.default)({ownerState:n},r,n)):Object.keys(l.props).forEach(f=>{(n==null?void 0:n[f])!==l.props[f]&&r[f]!==l.props[f]&&(c=!1)}),c&&(Array.isArray(a)||(a=[a]),a.push(typeof l.style=="function"?l.style((0,Le.default)({ownerState:n},r,n)):l.style))}),a}return o}function q0(e={}){const{themeId:t,defaultTheme:n=G0,rootShouldForwardProp:r=An,slotShouldForwardProp:o=An}=e,i=s=>(0,O0.default)((0,Le.default)({},s,{theme:_n((0,Le.default)({},s,{defaultTheme:n,themeId:t}))}));return i.__mui_systemSx=!0,(s,a={})=>{(0,ei.internal_processStyles)(s,P=>P.filter($=>!($!=null&&$.__mui_systemSx)));const{name:l,slot:c,skipVariantsResolver:f,skipSx:p,overridesResolver:g=W0(Z0(c))}=a,m=(0,Ar.default)(a,H0),h=f!==void 0?f:c&&c!=="Root"&&c!=="root"||!1,v=p||!1;let y,S=An;c==="Root"||c==="root"?S=r:c?S=o:B0(s)&&(S=void 0);const x=(0,ei.default)(s,(0,Le.default)({shouldForwardProp:S,label:y},m)),C=P=>typeof P=="function"&&P.__emotion_real!==P||(0,V0.isPlainObject)(P)?$=>In(P,(0,Le.default)({},$,{theme:_n({theme:$.theme,defaultTheme:n,themeId:t})})):P,_=(P,...$)=>{let k=C(P);const T=$?$.map(C):[];l&&g&&T.push(E=>{const F=_n((0,Le.default)({},E,{defaultTheme:n,themeId:t}));if(!F.components||!F.components[l]||!F.components[l].styleOverrides)return null;const N=F.components[l].styleOverrides,W={};return Object.entries(N).forEach(([G,Q])=>{W[G]=In(Q,(0,Le.default)({},E,{theme:F}))}),g(E,W)}),l&&!h&&T.push(E=>{var F;const N=_n((0,Le.default)({},E,{defaultTheme:n,themeId:t})),W=N==null||(F=N.components)==null||(F=F[l])==null?void 0:F.variants;return In({variants:W},(0,Le.default)({},E,{theme:N}))}),v||T.push(i);const B=T.length-$.length;if(Array.isArray(P)&&B>0){const E=new Array(B).fill("");k=[...P,...E],k.raw=[...P.raw,...E]}const O=x(k,...T);return s.muiName&&(O.muiName=s.muiName),O};return x.withConfig&&(_.withConfig=x.withConfig),_}}function U0(e){return e!=="ownerState"&&e!=="theme"&&e!=="sx"&&e!=="as"}const K0=e=>U0(e)&&e!=="classes",sr=I0({themeId:cn,defaultTheme:zs,rootShouldForwardProp:K0}),Y0=["theme"];function X0(e){let{theme:t}=e,n=$e(e,Y0);const r=t[cn];let o=r||t;return typeof t!="function"&&(r&&!r.vars?o=A({},r,{vars:null}):t&&!t.vars&&(o=A({},t,{vars:null}))),d.jsx(Nd,A({},n,{themeId:r?cn:void 0,theme:o}))}const ti=e=>{let t;return e<1?t=5.11916*e**2:t=4.5*Math.log(e+1)+2,(t/100).toFixed(2)};function ao(e){return zd(e)}function J0(e){return Un("MuiSvgIcon",e)}Kn("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const Q0=["children","className","color","component","fontSize","htmlColor","inheritViewBox","titleAccess","viewBox"],ef=e=>{const{color:t,fontSize:n,classes:r}=e,o={root:["root",t!=="inherit"&&`color${Qe(t)}`,`fontSize${Qe(n)}`]};return no(o,J0,r)},tf=sr("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.color!=="inherit"&&t[`color${Qe(n.color)}`],t[`fontSize${Qe(n.fontSize)}`]]}})(({theme:e,ownerState:t})=>{var n,r,o,i,s,a,l,c,f,p,g,m,h;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",fill:t.hasSvgAsChild?void 0:"currentColor",flexShrink:0,transition:(n=e.transitions)==null||(r=n.create)==null?void 0:r.call(n,"fill",{duration:(o=e.transitions)==null||(o=o.duration)==null?void 0:o.shorter}),fontSize:{inherit:"inherit",small:((i=e.typography)==null||(s=i.pxToRem)==null?void 0:s.call(i,20))||"1.25rem",medium:((a=e.typography)==null||(l=a.pxToRem)==null?void 0:l.call(a,24))||"1.5rem",large:((c=e.typography)==null||(f=c.pxToRem)==null?void 0:f.call(c,35))||"2.1875rem"}[t.fontSize],color:(p=(g=(e.vars||e).palette)==null||(g=g[t.color])==null?void 0:g.main)!=null?p:{action:(m=(e.vars||e).palette)==null||(m=m.action)==null?void 0:m.active,disabled:(h=(e.vars||e).palette)==null||(h=h.action)==null?void 0:h.disabled,inherit:void 0}[t.color]}}),Bs=u.forwardRef(function(t,n){const r=ao({props:t,name:"MuiSvgIcon"}),{children:o,className:i,color:s="inherit",component:a="svg",fontSize:l="medium",htmlColor:c,inheritViewBox:f=!1,titleAccess:p,viewBox:g="0 0 24 24"}=r,m=$e(r,Q0),h=u.isValidElement(o)&&o.type==="svg",v=A({},r,{color:s,component:a,fontSize:l,instanceFontSize:t.fontSize,inheritViewBox:f,viewBox:g,hasSvgAsChild:h}),y={};f||(y.viewBox=g);const S=ef(v);return d.jsxs(tf,A({as:a,className:an(S.root,i),focusable:"false",color:c,"aria-hidden":p?void 0:!0,role:p?"img":void 0,ref:n},y,m,h&&o.props,{ownerState:v,children:[h?o.props.children:o,p?d.jsx("title",{children:p}):null]}))});Bs.muiName="SvgIcon";function nf(e){return Un("MuiPaper",e)}Kn("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);const rf=["className","component","elevation","square","variant"],of=e=>{const{square:t,elevation:n,variant:r,classes:o}=e,i={root:["root",r,!t&&"rounded",r==="elevation"&&`elevation${n}`]};return no(i,nf,o)},sf=sr("div",{name:"MuiPaper",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[n.variant],!n.square&&t.rounded,n.variant==="elevation"&&t[`elevation${n.elevation}`]]}})(({theme:e,ownerState:t})=>{var n;return A({backgroundColor:(e.vars||e).palette.background.paper,color:(e.vars||e).palette.text.primary,transition:e.transitions.create("box-shadow")},!t.square&&{borderRadius:e.shape.borderRadius},t.variant==="outlined"&&{border:`1px solid ${(e.vars||e).palette.divider}`},t.variant==="elevation"&&A({boxShadow:(e.vars||e).shadows[t.elevation]},!e.vars&&e.palette.mode==="dark"&&{backgroundImage:`linear-gradient(${Wo("#fff",ti(t.elevation))}, ${Wo("#fff",ti(t.elevation))})`},e.vars&&{backgroundImage:(n=e.vars.overlays)==null?void 0:n[t.elevation]}))}),af=u.forwardRef(function(t,n){const r=ao({props:t,name:"MuiPaper"}),{className:o,component:i="div",elevation:s=1,square:a=!1,variant:l="elevation"}=r,c=$e(r,rf),f=A({},r,{component:i,elevation:s,square:a,variant:l}),p=of(f);return d.jsx(sf,A({as:i,ownerState:f,className:an(p.root,o),ref:n},c))}),lf=["onChange","maxRows","minRows","style","value"];function Rn(e){return parseInt(e,10)||0}const uf={shadow:{visibility:"hidden",position:"absolute",overflow:"hidden",height:0,top:0,left:0,transform:"translateZ(0)"}};function cf(e){for(const t in e)return!1;return!0}function ni(e){return cf(e)||e.outerHeightStyle===0&&!e.overflowing}const df=u.forwardRef(function(t,n){const{onChange:r,maxRows:o,minRows:i=1,style:s,value:a}=t,l=$e(t,lf),{current:c}=u.useRef(a!=null),f=u.useRef(null),p=ks(n,f),g=u.useRef(null),m=u.useRef(null),h=u.useCallback(()=>{const C=f.current,_=m.current;if(!C||!_)return;const $=No(C).getComputedStyle(C);if($.width==="0px")return{outerHeightStyle:0,overflowing:!1};_.style.width=$.width,_.value=C.value||t.placeholder||"x",_.value.slice(-1)===`
`&&(_.value+=" ");const k=$.boxSizing,T=Rn($.paddingBottom)+Rn($.paddingTop),B=Rn($.borderBottomWidth)+Rn($.borderTopWidth),O=_.scrollHeight;_.value="x";const E=_.scrollHeight;let F=O;i&&(F=Math.max(Number(i)*E,F)),o&&(F=Math.min(Number(o)*E,F)),F=Math.max(F,E);const N=F+(k==="border-box"?T+B:0),W=Math.abs(F-O)<=1;return{outerHeightStyle:N,overflowing:W}},[o,i,t.placeholder]),v=kd(()=>{const C=f.current,_=h();if(!C||!_||ni(_))return!1;const P=_.outerHeightStyle;return g.current!=null&&g.current!==P}),y=u.useCallback(()=>{const C=f.current,_=h();if(!C||!_||ni(_))return;const P=_.outerHeightStyle;g.current!==P&&(g.current=P,C.style.height=`${P}px`),C.style.overflow=_.overflowing?"hidden":""},[h]),S=u.useRef(-1);dn(()=>{const C=Pd(y),_=f==null?void 0:f.current;if(!_)return;const P=No(_);P.addEventListener("resize",C);let $;return typeof ResizeObserver<"u"&&($=new ResizeObserver(()=>{v()&&($.unobserve(_),cancelAnimationFrame(S.current),y(),S.current=requestAnimationFrame(()=>{$.observe(_)}))}),$.observe(_)),()=>{C.clear(),cancelAnimationFrame(S.current),P.removeEventListener("resize",C),$&&$.disconnect()}},[h,y,v]),dn(()=>{y()});const x=C=>{c||y(),r&&r(C)};return d.jsxs(u.Fragment,{children:[d.jsx("textarea",A({value:a,onChange:x,ref:p,rows:i,style:s},l)),d.jsx("textarea",{"aria-hidden":!0,className:t.className,readOnly:!0,ref:m,tabIndex:-1,style:A({},uf.shadow,s,{paddingTop:0,paddingBottom:0})})]})});function ff({props:e,states:t,muiFormControl:n}){return t.reduce((r,o)=>(r[o]=e[o],n&&typeof e[o]>"u"&&(r[o]=n[o]),r),{})}const Gs=u.createContext(void 0);function gf(){return u.useContext(Gs)}function mf(e){return d.jsx(gd,A({},e,{defaultTheme:zs,themeId:cn}))}function ri(e){return e!=null&&!(Array.isArray(e)&&e.length===0)}function pf(e,t=!1){return e&&(ri(e.value)&&e.value!==""||t&&ri(e.defaultValue)&&e.defaultValue!=="")}function hf(e){return Un("MuiInputBase",e)}const Ir=Kn("MuiInputBase",["root","formControl","focused","disabled","adornedStart","adornedEnd","error","sizeSmall","multiline","colorSecondary","fullWidth","hiddenLabel","readOnly","input","inputSizeSmall","inputMultiline","inputTypeSearch","inputAdornedStart","inputAdornedEnd","inputHiddenLabel"]),vf=["aria-describedby","autoComplete","autoFocus","className","color","components","componentsProps","defaultValue","disabled","disableInjectingGlobalStyles","endAdornment","error","fullWidth","id","inputComponent","inputProps","inputRef","margin","maxRows","minRows","multiline","name","onBlur","onChange","onClick","onFocus","onKeyDown","onKeyUp","placeholder","readOnly","renderSuffix","rows","size","slotProps","slots","startAdornment","type","value"],yf=(e,t)=>{const{ownerState:n}=e;return[t.root,n.formControl&&t.formControl,n.startAdornment&&t.adornedStart,n.endAdornment&&t.adornedEnd,n.error&&t.error,n.size==="small"&&t.sizeSmall,n.multiline&&t.multiline,n.color&&t[`color${Qe(n.color)}`],n.fullWidth&&t.fullWidth,n.hiddenLabel&&t.hiddenLabel]},Sf=(e,t)=>{const{ownerState:n}=e;return[t.input,n.size==="small"&&t.inputSizeSmall,n.multiline&&t.inputMultiline,n.type==="search"&&t.inputTypeSearch,n.startAdornment&&t.inputAdornedStart,n.endAdornment&&t.inputAdornedEnd,n.hiddenLabel&&t.inputHiddenLabel]},bf=e=>{const{classes:t,color:n,disabled:r,error:o,endAdornment:i,focused:s,formControl:a,fullWidth:l,hiddenLabel:c,multiline:f,readOnly:p,size:g,startAdornment:m,type:h}=e,v={root:["root",`color${Qe(n)}`,r&&"disabled",o&&"error",l&&"fullWidth",s&&"focused",a&&"formControl",g&&g!=="medium"&&`size${Qe(g)}`,f&&"multiline",m&&"adornedStart",i&&"adornedEnd",c&&"hiddenLabel",p&&"readOnly"],input:["input",r&&"disabled",h==="search"&&"inputTypeSearch",f&&"inputMultiline",g==="small"&&"inputSizeSmall",c&&"inputHiddenLabel",m&&"inputAdornedStart",i&&"inputAdornedEnd",p&&"readOnly"]};return no(v,hf,t)},xf=sr("div",{name:"MuiInputBase",slot:"Root",overridesResolver:yf})(({theme:e,ownerState:t})=>A({},e.typography.body1,{color:(e.vars||e).palette.text.primary,lineHeight:"1.4375em",boxSizing:"border-box",position:"relative",cursor:"text",display:"inline-flex",alignItems:"center",[`&.${Ir.disabled}`]:{color:(e.vars||e).palette.text.disabled,cursor:"default"}},t.multiline&&A({padding:"4px 0 5px"},t.size==="small"&&{paddingTop:1}),t.fullWidth&&{width:"100%"})),Cf=sr("input",{name:"MuiInputBase",slot:"Input",overridesResolver:Sf})(({theme:e,ownerState:t})=>{const n=e.palette.mode==="light",r=A({color:"currentColor"},e.vars?{opacity:e.vars.opacity.inputPlaceholder}:{opacity:n?.42:.5},{transition:e.transitions.create("opacity",{duration:e.transitions.duration.shorter})}),o={opacity:"0 !important"},i=e.vars?{opacity:e.vars.opacity.inputPlaceholder}:{opacity:n?.42:.5};return A({font:"inherit",letterSpacing:"inherit",color:"currentColor",padding:"4px 0 5px",border:0,boxSizing:"content-box",background:"none",height:"1.4375em",margin:0,WebkitTapHighlightColor:"transparent",display:"block",minWidth:0,width:"100%",animationName:"mui-auto-fill-cancel",animationDuration:"10ms","&::-webkit-input-placeholder":r,"&::-moz-placeholder":r,"&:-ms-input-placeholder":r,"&::-ms-input-placeholder":r,"&:focus":{outline:0},"&:invalid":{boxShadow:"none"},"&::-webkit-search-decoration":{WebkitAppearance:"none"},[`label[data-shrink=false] + .${Ir.formControl} &`]:{"&::-webkit-input-placeholder":o,"&::-moz-placeholder":o,"&:-ms-input-placeholder":o,"&::-ms-input-placeholder":o,"&:focus::-webkit-input-placeholder":i,"&:focus::-moz-placeholder":i,"&:focus:-ms-input-placeholder":i,"&:focus::-ms-input-placeholder":i},[`&.${Ir.disabled}`]:{opacity:1,WebkitTextFillColor:(e.vars||e).palette.text.disabled},"&:-webkit-autofill":{animationDuration:"5000s",animationName:"mui-auto-fill"}},t.size==="small"&&{paddingTop:1},t.multiline&&{height:"auto",resize:"none",padding:0,paddingTop:0},t.type==="search"&&{MozAppearance:"textfield"})}),wf=d.jsx(mf,{styles:{"@keyframes mui-auto-fill":{from:{display:"block"}},"@keyframes mui-auto-fill-cancel":{from:{display:"block"}}}}),_f=u.forwardRef(function(t,n){var r;const o=ao({props:t,name:"MuiInputBase"}),{"aria-describedby":i,autoComplete:s,autoFocus:a,className:l,components:c={},componentsProps:f={},defaultValue:p,disabled:g,disableInjectingGlobalStyles:m,endAdornment:h,fullWidth:v=!1,id:y,inputComponent:S="input",inputProps:x={},inputRef:C,maxRows:_,minRows:P,multiline:$=!1,name:k,onBlur:T,onChange:B,onClick:O,onFocus:E,onKeyDown:F,onKeyUp:N,placeholder:W,readOnly:G,renderSuffix:Q,rows:ee,slotProps:I={},slots:q={},startAdornment:Ee,type:Me="text",value:Fe}=o,X=$e(o,vf),U=x.value!=null?x.value:Fe,{current:le}=u.useRef(U!=null),me=u.useRef(),xe=u.useCallback(b=>{},[]),Oe=ks(me,C,x.ref,xe),[Se,he]=u.useState(!1),Z=gf(),te=ff({props:o,muiFormControl:Z,states:["color","disabled","error","hiddenLabel","size","required","filled"]});te.focused=Z?Z.focused:Se,u.useEffect(()=>{!Z&&g&&Se&&(he(!1),T&&T())},[Z,g,Se,T]);const V=Z&&Z.onFilled,R=Z&&Z.onEmpty,ie=u.useCallback(b=>{pf(b)?V&&V():R&&R()},[V,R]);dn(()=>{le&&ie({value:U})},[U,ie,le]);const ve=b=>{if(te.disabled){b.stopPropagation();return}E&&E(b),x.onFocus&&x.onFocus(b),Z&&Z.onFocus?Z.onFocus(b):he(!0)},De=b=>{T&&T(b),x.onBlur&&x.onBlur(b),Z&&Z.onBlur?Z.onBlur(b):he(!1)},Ct=(b,...ne)=>{if(!le){const w=b.target||me.current;if(w==null)throw new Error(Zt(1));ie({value:w.value})}x.onChange&&x.onChange(b,...ne),B&&B(b,...ne)};u.useEffect(()=>{ie(me.current)},[]);const wt=b=>{me.current&&b.currentTarget===b.target&&me.current.focus(),O&&O(b)};let et=S,Ce=x;$&&et==="input"&&(ee?Ce=A({type:void 0,minRows:ee,maxRows:ee},Ce):Ce=A({type:void 0,maxRows:_,minRows:P},Ce),et=df);const tt=b=>{ie(b.animationName==="mui-auto-fill-cancel"?me.current:{value:"x"})};u.useEffect(()=>{Z&&Z.setAdornedStart(!!Ee)},[Z,Ee]);const nt=A({},o,{color:te.color||"primary",disabled:te.disabled,endAdornment:h,error:te.error,focused:te.focused,formControl:Z,fullWidth:v,hiddenLabel:te.hiddenLabel,multiline:$,size:te.size,startAdornment:Ee,type:Me}),_t=bf(nt),kt=q.root||c.Root||xf,Rt=I.root||f.root||{},jt=q.input||c.Input||Cf;return Ce=A({},Ce,(r=I.input)!=null?r:f.input),d.jsxs(u.Fragment,{children:[!m&&wf,d.jsxs(kt,A({},Rt,!Bo(kt)&&{ownerState:A({},nt,Rt.ownerState)},{ref:n,onClick:wt},X,{className:an(_t.root,Rt.className,l,G&&"MuiInputBase-readOnly"),children:[Ee,d.jsx(Gs.Provider,{value:null,children:d.jsx(jt,A({ownerState:nt,"aria-invalid":te.error,"aria-describedby":i,autoComplete:s,autoFocus:a,defaultValue:p,disabled:te.disabled,id:y,onAnimationStart:tt,name:k,placeholder:W,readOnly:G,required:te.required,rows:ee,value:U,onKeyDown:F,onKeyUp:N,type:Me},Ce,!Bo(jt)&&{as:et,ownerState:A({},nt,Ce.ownerState)},{ref:Oe,className:an(_t.input,Ce.className,G&&"MuiInputBase-readOnly"),onBlur:De,onChange:Ct,onFocus:ve}))}),h,Q?Q(A({},te,{startAdornment:Ee})):null]}))]})}),Rf=Kn("MuiBox",["root"]),Ef=so(),de=Sd({themeId:cn,defaultTheme:Ef,defaultClassName:Rf.root,generateClassName:Ms.generate});function lo(e){const{children:t,defer:n=!1,fallback:r=null}=e,[o,i]=u.useState(!1);return dn(()=>{n||i(!0)},[n]),u.useEffect(()=>{n&&i(!0)},[n]),d.jsx(u.Fragment,{children:o?t:r})}function uo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function J(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),r.forEach(function(o){uo(e,o,n[o])})}return e}function Mf(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}function bn(e,t){return t=t??{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Mf(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function Vr(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Pf(e){if(Array.isArray(e))return Vr(e)}function $f(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Ff(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Zs(e,t){if(e){if(typeof e=="string")return Vr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(n);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Vr(e,t)}}function Ne(e){return Pf(e)||$f(e)||Zs(e)||Ff()}function Ws(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=Ws(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function kf(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=Ws(e))&&(r&&(r+=" "),r+=t);return r}function jf(e){if(Array.isArray(e))return e}function Af(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r=[],o=!0,i=!1,s,a;try{for(n=n.call(e);!(o=(s=n.next()).done)&&(r.push(s.value),!(t&&r.length===t));o=!0);}catch(l){i=!0,a=l}finally{try{!o&&n.return!=null&&n.return()}finally{if(i)throw a}}return r}}function If(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Pe(e,t){return jf(e)||Af(e,t)||Zs(e,t)||If()}function On(e){"@swc/helpers - typeof";return e&&typeof Symbol<"u"&&e.constructor===Symbol?"symbol":typeof e}var co={scheme:"Light Theme",author:"mac gainor (https://github.com/mac-s-g)",base00:"rgba(0, 0, 0, 0)",base01:"rgb(245, 245, 245)",base02:"rgb(235, 235, 235)",base03:"#93a1a1",base04:"rgba(0, 0, 0, 0.3)",base05:"#586e75",base06:"#073642",base07:"#002b36",base08:"#d33682",base09:"#cb4b16",base0A:"#ffd500",base0B:"#859900",base0C:"#6c71c4",base0D:"#586e75",base0E:"#2aa198",base0F:"#268bd2"},qs={scheme:"Dark Theme",author:"Chris Kempson (http://chriskempson.com)",base00:"#181818",base01:"#282828",base02:"#383838",base03:"#585858",base04:"#b8b8b8",base05:"#d8d8d8",base06:"#e8e8e8",base07:"#f8f8f8",base08:"#ab4642",base09:"#dc9656",base0A:"#f7ca88",base0B:"#a1b56c",base0C:"#86c1b9",base0D:"#7cafc2",base0E:"#ba8baf",base0F:"#a16946"},Us=function(){return null};Us.when=function(){return!1};var Vf=function(e){var t,n,r,o,i,s,a,l,c,f,p,g,m,h,v,y,S;return $a()(function(x,C){return{enableClipboard:(t=e.enableClipboard)!==null&&t!==void 0?t:!0,highlightUpdates:(n=e.highlightUpdates)!==null&&n!==void 0?n:!1,indentWidth:(r=e.indentWidth)!==null&&r!==void 0?r:3,groupArraysAfterLength:(o=e.groupArraysAfterLength)!==null&&o!==void 0?o:100,collapseStringsAfterLength:e.collapseStringsAfterLength===!1?Number.MAX_VALUE:(i=e.collapseStringsAfterLength)!==null&&i!==void 0?i:50,maxDisplayLength:(s=e.maxDisplayLength)!==null&&s!==void 0?s:30,rootName:(a=e.rootName)!==null&&a!==void 0?a:"root",onChange:(l=e.onChange)!==null&&l!==void 0?l:function(){},onCopy:(c=e.onCopy)!==null&&c!==void 0?c:void 0,onSelect:(f=e.onSelect)!==null&&f!==void 0?f:void 0,keyRenderer:(p=e.keyRenderer)!==null&&p!==void 0?p:Us,editable:(g=e.editable)!==null&&g!==void 0?g:!1,defaultInspectDepth:(m=e.defaultInspectDepth)!==null&&m!==void 0?m:5,objectSortKeys:(h=e.objectSortKeys)!==null&&h!==void 0?h:!1,quotesOnKeys:(v=e.quotesOnKeys)!==null&&v!==void 0?v:!0,displayDataTypes:(y=e.displayDataTypes)!==null&&y!==void 0?y:!0,inspectCache:{},hoverPath:null,colorspace:co,value:e.value,prevValue:void 0,displayObjectSize:(S=e.displayObjectSize)!==null&&S!==void 0?S:!0,getInspectCache:function(_,P){var $=P!==void 0?_.join(".")+"[".concat(P,"]nt"):_.join(".");return C().inspectCache[$]},setInspectCache:function(_,P,$){var k=$!==void 0?_.join(".")+"[".concat($,"]nt"):_.join(".");x(function(T){return{inspectCache:bn(J({},T.inspectCache),uo({},k,typeof P=="function"?P(T.inspectCache[k]):P))}})},setHover:function(_,P){x({hoverPath:_?{path:_,nestedIndex:P}:null})}}})},xn=u.createContext(void 0);xn.Provider;var z=function(e,t){var n=u.useContext(xn);return Ri(n,e,t)},fo=function(){return z(function(e){return e.colorspace.base07})};function oi(e,t,n,r,o,i,s){try{var a=e[i](s),l=a.value}catch(c){n(c);return}a.done?t(l):Promise.resolve(l).then(r,o)}function Ks(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function s(l){oi(i,r,o,s,a,"next",l)}function a(l){oi(i,r,o,s,a,"throw",l)}s(void 0)})}}function Ys(e,t){var n={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},r,o,i,s;return s={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function a(c){return function(f){return l([c,f])}}function l(c){if(r)throw new TypeError("Generator is already executing.");for(;s&&(s=0,c[0]&&(n=0)),n;)try{if(r=1,o&&(i=c[0]&2?o.return:c[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,c[1])).done)return i;switch(o=0,i&&(c=[c[0]&2,i.value]),c[0]){case 0:case 1:i=c;break;case 4:return n.label++,{value:c[1],done:!1};case 5:n.label++,o=c[1],c=[0];continue;case 7:c=n.ops.pop(),n.trys.pop();continue;default:if(i=n.trys,!(i=i.length>0&&i[i.length-1])&&(c[0]===6||c[0]===2)){n=0;continue}if(c[0]===3&&(!i||c[1]>i[0]&&c[1]<i[3])){n.label=c[1];break}if(c[0]===6&&n.label<i[1]){n.label=i[1],i=c;break}if(i&&n.label<i[2]){n.label=i[2],n.ops.push(c);break}i[2]&&n.ops.pop(),n.trys.pop();continue}c=t.call(e,n)}catch(f){c=[6,f],o=0}finally{r=i=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}function Je(e,t){return t!=null&&typeof Symbol<"u"&&t[Symbol.hasInstance]?!!t[Symbol.hasInstance](e):e instanceof t}Object.prototype.constructor.toString();var Tf=function(e,t,n){if(e===null||n===null||typeof e!="object"||typeof n!="object")return!1;if(Object.is(e,n)&&t.length!==0)return"";for(var r=[],o=Ne(t),i=e;i!==n||o.length!==0;){if(typeof i!="object"||i===null)return!1;if(Object.is(i,n))return r.reduce(function(a,l,c){return typeof l=="number"?a+"[".concat(l,"]"):a+"".concat(c===0?"":".").concat(l)},"");var s=o.shift();r.push(s),i=i[s]}return!1};function Cn(e){return e===null?0:Array.isArray(e)?e.length:Je(e,Map)||Je(e,Set)?e.size:Je(e,Date)?1:typeof e=="object"?Object.keys(e).length:typeof e=="string"?e.length:1}function ii(e,t){for(var n=[],r=0;r<e.length;)n.push(e.slice(r,r+t)),r+=t;return n}function Of(e,t){var n=function(i,s){if((typeof s>"u"?"undefined":On(s))==="bigint")return s.toString();if(Je(s,Map)){if("toJSON"in s&&typeof s.toJSON=="function")return s.toJSON();if(s.size===0)return{};if(r.includes(s))return"[Circular]";r.push(s);var a=Array.from(s.entries());return a.every(function(f){var p=Pe(f,1),g=p[0];return typeof g=="string"||typeof g=="number"})?Object.fromEntries(a):{}}if(Je(s,Set))return"toJSON"in s&&typeof s.toJSON=="function"?s.toJSON():r.includes(s)?"[Circular]":(r.push(s),Array.from(s.values()));if(typeof s=="object"&&s!==null&&Object.keys(s).length){var l=r.length;if(l){for(var c=l-1;c>=0&&r[c][i]!==s;--c)r.pop();if(r.includes(s))return"[Circular]"}r.push(s)}return s},r=[];return JSON.stringify(e,n,t)}function Tr(e){return Or.apply(this,arguments)}function Or(){return Or=Ks(function(e){return Ys(this,function(t){switch(t.label){case 0:if(!("clipboard"in navigator))return[3,4];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,navigator.clipboard.writeText(e)];case 2:return t.sent(),[3,4];case 3:return t.sent(),[3,4];case 4:return _r(e),[2]}})}),Or.apply(this,arguments)}function Df(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=e.timeout,n=t===void 0?2e3:t,r=Pe(u.useState(!1),2),o=r[0],i=r[1],s=u.useRef(null),a=u.useCallback(function(p){var g=s.current;g&&window.clearTimeout(g),s.current=window.setTimeout(function(){return i(!1)},n),i(p)},[n]),l=z(function(p){return p.onCopy}),c=u.useCallback(function(){var p=Ks(function(g,m){var h,v,y;return Ys(this,function(S){switch(S.label){case 0:if(typeof l!="function")return[3,5];S.label=1;case 1:return S.trys.push([1,3,,4]),[4,l(g,m,Tr)];case 2:return S.sent(),a(!0),[3,4];case 3:return h=S.sent(),console.error("error when copy ".concat(g.length===0?"src":"src[".concat(g.join(".")),"]"),h),[3,4];case 4:return[3,8];case 5:return S.trys.push([5,7,,8]),v=Of(typeof m=="function"?m.toString():m,"  "),[4,Tr(v)];case 6:return S.sent(),a(!0),[3,8];case 7:return y=S.sent(),console.error("error when copy ".concat(g.length===0?"src":"src[".concat(g.join(".")),"]"),y),[3,8];case 8:return[2]}})});return function(g,m){return p.apply(this,arguments)}}(),[a,l]),f=u.useCallback(function(){i(!1),s.current&&clearTimeout(s.current)},[]);return{copy:c,reset:f,copied:o}}function go(e,t){var n=z(function(r){return r.value});return u.useMemo(function(){return Tf(n,e,t)},[e,t,n])}function Lf(e,t,n){var r=e.length,o=go(e,t),i=z(function(g){return g.getInspectCache}),s=z(function(g){return g.setInspectCache}),a=z(function(g){return g.defaultInspectDepth});u.useEffect(function(){var g=i(e,n);if(g===void 0)if(n!==void 0)s(e,!1,n);else{var m=o?!1:r<a;s(e,m)}},[a,r,i,o,n,e,s]);var l=Pe(u.useState(function(){var g=i(e,n);return g!==void 0?g:n!==void 0||o?!1:r<a}),2),c=l[0],f=l[1],p=u.useCallback(function(g){f(function(m){var h=typeof g=="boolean"?g:g(m);return s(e,h,n),h})},[n,e,s]);return[c,p]}var vt=function(e){return M(de,bn(J({component:"div"},e),{sx:J({display:"inline-block"},e.sx)}))},Xs=function(e){var t=e.dataType,n=e.enable,r=n===void 0?!0:n;return r?M(vt,{className:"data-type-label",sx:{mx:.5,fontSize:"0.7rem",opacity:.8,userSelect:"none"},children:t}):null};function ot(e,t,n){var r=n.fromString,o=n.colorKey,i=n.displayTypeLabel,s=i===void 0?!0:i,a=u.memo(t),l=function(f){var p=z(function(h){return h.displayDataTypes}),g=z(function(h){return h.colorspace[o]}),m=z(function(h){return h.onSelect});return _e(vt,{onClick:function(){return m==null?void 0:m(f.path,f.value)},sx:{color:g},children:[s&&p&&M(Xs,{dataType:e}),M(vt,{className:"".concat(e,"-value"),children:M(a,{value:f.value})})]})};if(l.displayName="easy-".concat(e,"-type"),!r)return{Component:l};var c=function(f){var p=f.value,g=f.setValue,m=z(function(h){return h.colorspace[o]});return M(_f,{value:p,onChange:u.useCallback(function(h){var v=r(h.target.value);g(v)},[g]),size:"small",multiline:!0,sx:{color:m,padding:.5,borderStyle:"solid",borderColor:"black",borderWidth:1,fontSize:"0.8rem",fontFamily:"monospace",display:"inline-flex"}})};return c.displayName="easy-".concat(e,"-type-editor"),{Component:l,Editor:c}}var Hf=function(e){var t=e.toString(),n=!0,r=t.indexOf(")"),o=t.indexOf("=>");return o!==-1&&o>r&&(n=!1),n?t.substring(t.indexOf("{",r)+1,t.lastIndexOf("}")):t.substring(t.indexOf("=>")+2)},zf=function(e){var t=e.toString(),n=t.indexOf("function")!==-1;return n?t.substring(8,t.indexOf("{")).trim():t.substring(0,t.indexOf("=>")+2).trim()},Nf="{",Bf="}",Gf=function(e){return _e(lo,{children:[M(Xs,{dataType:"function"}),_e(de,{component:"span",className:"data-function-start",sx:{letterSpacing:.5},children:[zf(e.value)," ",Nf]})]})},Zf=function(){return M(lo,{children:M(de,{component:"span",className:"data-function-end",children:Bf})})},Wf=function(e){var t=z(function(n){return n.colorspace.base05});return M(lo,{children:M(de,{className:"data-function",sx:{display:e.inspect?"block":"inline-block",pl:e.inspect?2:0,color:t},children:e.inspect?Hf(e.value):M(de,{component:"span",className:"data-function-body",onClick:function(){return e.setInspect(!0)},sx:{"&:hover":{cursor:"pointer"},padding:.5},children:"…"})})})};function qf(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function Uf(e,t){if(e==null)return{};var n=qf(e,t),r,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}var Ft=function(e){var t=e.d,n=Uf(e,["d"]);return M(Bs,bn(J({},n),{children:M("path",{d:t})}))},Kf="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z",Yf="M10 6 8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z",Xf="M 12 2 C 10.615 1.998 9.214625 2.2867656 7.890625 2.8847656 L 8.9003906 4.6328125 C 9.9043906 4.2098125 10.957 3.998 12 4 C 15.080783 4 17.738521 5.7633175 19.074219 8.3222656 L 17.125 9 L 21.25 11 L 22.875 7 L 20.998047 7.6523438 C 19.377701 4.3110398 15.95585 2 12 2 z M 6.5097656 4.4882812 L 2.2324219 5.0820312 L 3.734375 6.3808594 C 1.6515335 9.4550558 1.3615962 13.574578 3.3398438 17 C 4.0308437 18.201 4.9801562 19.268234 6.1601562 20.115234 L 7.1699219 18.367188 C 6.3019219 17.710187 5.5922656 16.904 5.0722656 16 C 3.5320014 13.332354 3.729203 10.148679 5.2773438 7.7128906 L 6.8398438 9.0625 L 6.5097656 4.4882812 z M 19.929688 13 C 19.794687 14.08 19.450734 15.098 18.927734 16 C 17.386985 18.668487 14.531361 20.090637 11.646484 19.966797 L 12.035156 17.9375 L 8.2402344 20.511719 L 10.892578 23.917969 L 11.265625 21.966797 C 14.968963 22.233766 18.681899 20.426323 20.660156 17 C 21.355156 15.801 21.805219 14.445 21.949219 13 L 19.929688 13 z",Jf="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z",Qf="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z",eg="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.9959.9959 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z",tg="M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z",si=function(e){return M(Ft,J({d:Kf},e))},ng=function(e){return M(Ft,J({d:Yf},e))},rg=function(e){return M(Ft,J({d:Xf},e))},og=function(e){return M(Ft,J({d:Jf},e))},ig=function(e){return M(Ft,J({d:Qf},e))},sg=function(e){return M(Ft,J({d:eg},e))},ag=function(e){return M(Ft,J({d:tg},e))},lg="{",ug="[",cg="}",dg="]";function Js(e){var t=Cn(e),n="";return(Je(e,Map)||Je(e,Set))&&(n=e[Symbol.toStringTag]),Object.prototype.hasOwnProperty.call(e,Symbol.toStringTag)&&(n=e[Symbol.toStringTag]),"".concat(t," Items").concat(n?" (".concat(n,")"):"")}var fg=function(e){var t=z(function(l){return l.colorspace.base04}),n=fo(),r=u.useMemo(function(){return Array.isArray(e.value)},[e.value]),o=u.useMemo(function(){return Cn(e.value)===0},[e.value]),i=u.useMemo(function(){return Js(e.value)},[e.value]),s=z(function(l){return l.displayObjectSize}),a=go(e.path,e.value);return _e(de,{component:"span",className:"data-object-start",sx:{letterSpacing:.5},children:[r?ug:lg,s&&e.inspect&&!o&&M(de,{component:"span",sx:{pl:.5,fontStyle:"italic",color:t,userSelect:"none"},children:i}),a&&!e.inspect&&_e(He,{children:[M(rg,{sx:{fontSize:12,color:n,mx:.5}}),a]})]})},gg=function(e){var t=z(function(s){return s.colorspace.base04}),n=u.useMemo(function(){return Array.isArray(e.value)},[e.value]),r=z(function(s){return s.displayObjectSize}),o=u.useMemo(function(){return Cn(e.value)===0},[e.value]),i=u.useMemo(function(){return Js(e.value)},[e.value]);return _e(de,{component:"span",className:"data-object-end",children:[n?dg:cg,r&&(o||!e.inspect)?M(de,{component:"span",sx:{pl:.5,fontStyle:"italic",color:t,userSelect:"none"},children:i}):null]})};function mg(e){return typeof(e==null?void 0:e[Symbol.iterator])=="function"}var pg=function(e){var t=fo(),n=z(function(h){return h.colorspace.base02}),r=z(function(h){return h.groupArraysAfterLength}),o=go(e.path,e.value),i=Pe(u.useState(z(function(h){return h.maxDisplayLength})),2),s=i[0],a=i[1],l=z(function(h){return h.objectSortKeys}),c=u.useMemo(function(){if(!e.inspect)return null;var h=e.value,v=mg(h);if(v&&!Array.isArray(h)){var y=[];if(Je(h,Map))h.forEach(function(E,F){var N=F.toString(),W=Ne(e.path).concat([N]);y.push(M(Ht,{path:W,value:E,prevValue:Je(e.prevValue,Map)?e.prevValue.get(F):void 0,editable:!1},N))});else for(var S=h[Symbol.iterator](),x=S.next(),C=0;!x.done;)y.push(M(Ht,{path:Ne(e.path).concat(["iterator:".concat(C)]),value:x.value,nestedIndex:C,editable:!1},C)),C++,x=S.next();return y}if(Array.isArray(h)){if(h.length<=r){var _=h.slice(0,s).map(function(E,F){var N=Ne(e.path).concat([F]);return M(Ht,{path:N,value:E,prevValue:Array.isArray(e.prevValue)?e.prevValue[F]:void 0},F)});if(h.length>s){var P=h.length-s;_.push(_e(vt,{sx:{cursor:"pointer",lineHeight:1.5,color:t,letterSpacing:.5,opacity:.8,userSelect:"none"},onClick:function(){return a(function(E){return E*2})},children:["hidden ",P," items…"]},"last"))}return _}var $=ii(h,r),k=Array.isArray(e.prevValue)?ii(e.prevValue,r):void 0;return $.map(function(E,F){var N=Ne(e.path);return M(Ht,{path:N,value:E,nestedIndex:F,prevValue:k==null?void 0:k[F]},F)})}var T=Object.entries(h);l&&(T=l===!0?T.sort(function(E,F){var N=Pe(E,1),W=N[0],G=Pe(F,1),Q=G[0];return W.localeCompare(Q)}):T.sort(function(E,F){var N=Pe(E,1),W=N[0],G=Pe(F,1),Q=G[0];return l(W,Q)}));var B=T.slice(0,s).map(function(E){var F=Pe(E,2),N=F[0],W=F[1],G,Q=Ne(e.path).concat([N]);return M(Ht,{path:Q,value:W,prevValue:(G=e.prevValue)===null||G===void 0?void 0:G[N]},N)});if(T.length>s){var O=T.length-s;B.push(_e(vt,{sx:{cursor:"pointer",lineHeight:1.5,color:t,letterSpacing:.5,opacity:.8,userSelect:"none"},onClick:function(){return a(function(E){return E*2})},children:["hidden ",O," items…"]},"last"))}return B},[e.inspect,e.value,e.prevValue,e.path,r,s,t,l]),f=e.inspect?.6:0,p=z(function(h){return h.indentWidth}),g=e.inspect?p-f:p,m=u.useMemo(function(){return Cn(e.value)===0},[e.value]);return m?null:M(de,{className:"data-object",sx:{display:e.inspect?"block":"inline-block",pl:e.inspect?g-.6:0,marginLeft:f,color:t,borderLeft:e.inspect?"1px solid ".concat(n):"none"},children:e.inspect?c:!o&&M(de,{component:"span",className:"data-object-body",onClick:function(){return e.setInspect(!0)},sx:{"&:hover":{cursor:"pointer"},padding:.5,userSelect:"none"},children:"…"})})},hg=function(){return Fa()(function(e){return{registry:[],registerTypes:function(t){e(function(n){return{registry:typeof t=="function"?t(n.registry):t}})}}})},mo=u.createContext(void 0);mo.Provider;var Qs=function(e,t){var n=u.useContext(mo);return Ri(n,e,t)},vg={is:function(e){return typeof e=="object"},Component:pg,PreComponent:fg,PostComponent:gg};function yg(e,t,n){var r,o=!0,i=!1,s=void 0;try{for(var a=n[Symbol.iterator](),l;!(o=(l=a.next()).done);o=!0){var c=l.value;if(c.is(e,t)&&(r=c,typeof e=="object"))return c}}catch(f){i=!0,s=f}finally{try{!o&&a.return!=null&&a.return()}finally{if(i)throw s}}if(r===void 0){if(typeof e=="object")return vg;throw new Error("this is not possible")}return r}function Sg(e,t){var n=Qs(function(r){return r.registry});return u.useMemo(function(){return yg(e,t,n)},[e,t,n])}function bg(){var e=function(i){function s(a,l){var c,f;return Object.is(a.value,l.value)&&a.inspect&&l.inspect&&((c=a.path)===null||c===void 0?void 0:c.join("."))===((f=l.path)===null||f===void 0?void 0:f.join("."))}i.Component=u.memo(i.Component,s),i.Editor&&(i.Editor=u.memo(i.Editor,function(l,c){return Object.is(l.value,c.value)})),i.PreComponent&&(i.PreComponent=u.memo(i.PreComponent,s)),i.PostComponent&&(i.PostComponent=u.memo(i.PostComponent,s)),t.push(i)},t=[];e(J({is:function(o){return typeof o=="boolean"}},ot("bool",function(o){var i=o.value;return M(He,{children:i?"true":"false"})},{colorKey:"base0E",fromString:function(o){return!!o}})));var n={weekday:"short",year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"};e(J({is:function(o){return Je(o,Date)}},ot("date",function(o){var i=o.value;return M(He,{children:i.toLocaleTimeString("en-us",n)})},{colorKey:"base0D"}))),e(J({is:function(o){return o===null}},ot("null",function(){var o=z(function(i){return i.colorspace.base02});return M(de,{sx:{fontSize:"0.8rem",backgroundColor:o,fontWeight:"bold",borderRadius:"3px",padding:"0.5px 2px"},children:"NULL"})},{colorKey:"base08",displayTypeLabel:!1}))),e(J({is:function(o){return o===void 0}},ot("undefined",function(){var o=z(function(i){return i.colorspace.base02});return M(de,{sx:{fontSize:"0.7rem",backgroundColor:o,borderRadius:"3px",padding:"0.5px 2px"},children:"undefined"})},{colorKey:"base05",displayTypeLabel:!1}))),e(J({is:function(o){return typeof o=="string"}},ot("string",function(o){var i=Pe(u.useState(!1),2),s=i[0],a=i[1],l=z(function(p){return p.collapseStringsAfterLength}),c=s?o.value:o.value.slice(0,l),f=o.value.length>l;return _e(de,{component:"span",sx:{overflowWrap:"anywhere",cursor:f?"pointer":"inherit"},onClick:function(){f&&a(function(p){return!p})},children:['"',c,f&&!s&&M(de,{component:"span",sx:{padding:.5},children:"…"}),'"']})},{colorKey:"base09",fromString:function(o){return o}}))),e({is:function(o){return typeof o=="function"},Component:Wf,PreComponent:Gf,PostComponent:Zf});var r=function(o){return o%1===0};return e(J({is:function(o){return typeof o=="number"&&isNaN(o)}},ot("NaN",function(){var o=z(function(i){return i.colorspace.base02});return M(de,{sx:{backgroundColor:o,fontSize:"0.8rem",fontWeight:"bold",borderRadius:"3px"},children:"NaN"})},{colorKey:"base08",displayTypeLabel:!1}))),e(J({is:function(o){return typeof o=="number"&&!r(o)}},ot("float",function(o){var i=o.value;return M(He,{children:i})},{colorKey:"base0B",fromString:function(o){return parseFloat(o)}}))),e(J({is:function(o){return typeof o=="number"&&r(o)}},ot("int",function(o){var i=o.value;return M(He,{children:i})},{colorKey:"base0F",fromString:function(o){return parseInt(o)}}))),e(J({is:function(o){return(typeof o>"u"?"undefined":On(o))==="bigint"}},ot("bigint",function(o){var i=o.value;return M(He,{children:"".concat(i,"n")})},{colorKey:"base0F",fromString:function(o){return BigInt(o.replace(/\D/g,""))}}))),t}var En=function(e){return M(de,bn(J({component:"span"},e),{sx:J({cursor:"pointer",paddingLeft:"0.7rem"},e.sx)}))},Ht=function(e){var t=e.value,n=e.prevValue,r=e.path,o=e.nestedIndex,i,s=(i=e.editable)!==null&&i!==void 0?i:void 0,a=z(function(V){return V.editable}),l=u.useMemo(function(){return a===!1||s===!1?!1:typeof a=="function"?!!a(r,t):a},[r,s,a,t]),c=Pe(u.useState(typeof t=="function"?function(){return t}:t),2),f=c[0],p=c[1],g=r.length,m=r[g-1],h=z(function(V){return V.hoverPath}),v=u.useMemo(function(){return h&&r.every(function(V,R){return V===h.path[R]&&o===h.nestedIndex})},[h,r,o]),y=z(function(V){return V.setHover}),S=z(function(V){return V.value}),x=Pe(Lf(r,t,o),2),C=x[0],_=x[1],P=Pe(u.useState(!1),2),$=P[0],k=P[1],T=z(function(V){return V.onChange}),B=fo(),O=z(function(V){return V.colorspace.base0C}),E=z(function(V){return V.colorspace.base0A}),F=Sg(t,r),N=F.Component,W=F.PreComponent,G=F.PostComponent,Q=F.Editor,ee=z(function(V){return V.quotesOnKeys}),I=z(function(V){return V.rootName}),q=S===t,Ee=Number.isInteger(Number(m)),Me=z(function(V){return V.enableClipboard}),Fe=Df(),X=Fe.copy,U=Fe.copied,le=z(function(V){return V.highlightUpdates}),me=u.useMemo(function(){return!le||n===void 0?!1:(typeof t>"u"?"undefined":On(t))!==(typeof n>"u"?"undefined":On(n))?!0:typeof t=="number"?isNaN(t)&&isNaN(n)?!1:t!==n:Array.isArray(t)!==Array.isArray(n)?!0:typeof t=="object"||typeof t=="function"?!1:t!==n},[le,n,t]),xe=u.useRef();u.useEffect(function(){xe.current&&me&&"animate"in xe.current&&xe.current.animate([{backgroundColor:E},{backgroundColor:""}],{duration:1e3,easing:"ease-in"})},[E,me,n,t]);var Oe=u.useMemo(function(){return $?_e(He,{children:[M(En,{children:M(og,{sx:{fontSize:".8rem"},onClick:function(){k(!1),p(t)}})}),M(En,{children:M(si,{sx:{fontSize:".8rem"},onClick:function(){k(!1),T(r,t,f)}})})]}):_e(He,{children:[Me&&M(En,{onClick:function(V){V.preventDefault();try{X(r,t,Tr)}catch(R){console.error(R)}},children:U?M(si,{sx:{fontSize:".8rem"}}):M(ig,{sx:{fontSize:".8rem"}})}),Q&&l&&M(En,{onClick:function(V){V.preventDefault(),k(!0),p(t)},children:M(sg,{sx:{fontSize:".8rem"}})})]})},[Q,U,X,l,$,Me,T,r,f,t]),Se=u.useMemo(function(){return Cn(t)===0},[t]),he=!Se&&!!(W&&G),Z=z(function(V){return V.keyRenderer}),te=u.useMemo(function(){return{path:r,inspect:C,setInspect:_,value:t,prevValue:n}},[C,r,_,t,n]);return _e(de,{className:"data-key-pair","data-testid":"data-key-pair"+r.join("."),sx:{userSelect:"text"},onMouseEnter:u.useCallback(function(){return y(r,o)},[y,r,o]),children:[_e(vt,{component:"span",className:"data-key",sx:{lineHeight:1.5,color:B,letterSpacing:.5,opacity:.8},onClick:u.useCallback(function(V){V.isDefaultPrevented()||Se||_(function(R){return!R})},[Se,_]),children:[he?C?M(ag,{sx:{fontSize:".8rem","&:hover":{cursor:"pointer"}}}):M(ng,{sx:{fontSize:".8rem","&:hover":{cursor:"pointer"}}}):null,M(de,{ref:xe,component:"span",children:q?I!==!1?ee?_e(He,{children:['"',I,'"']}):M(He,{children:I}):null:Z.when(te)?M(Z,J({},te)):o===void 0&&(Ee?M(de,{component:"span",style:{color:O},children:m}):ee?_e(He,{children:['"',m,'"']}):M(He,{children:m}))}),q?I!==!1&&M(vt,{sx:{mr:.5},children:":"}):o===void 0&&M(vt,{sx:{mr:.5},children:":"}),W&&M(W,J({},te)),v&&he&&C&&Oe]}),$&&l?Q&&M(Q,{value:f,setValue:p}):N?M(N,J({},te)):M(de,{component:"span",className:"data-value-fallback",children:"fallback: ".concat(t)}),G&&M(G,J({},te)),v&&he&&!C&&Oe,v&&!he&&Oe]})},ai="(prefers-color-scheme: dark)";function xg(){var e=Pe(u.useState(!1),2),t=e[0],n=e[1];return u.useEffect(function(){var r=function(i){n(i.matches)};n(window.matchMedia(ai).matches);var o=window.matchMedia(ai);return o.addEventListener("change",r),function(){return o.removeEventListener("change",r)}},[]),t}function je(e,t){var n=u.useContext(xn).setState;u.useEffect(function(){t!==void 0&&n(uo({},e,t))},[e,t,n])}var Cg=function(e){var t=u.useContext(xn).setState;u.useEffect(function(){t(function(p){return{prevValue:p.value,value:e.value}})},[e.value,t]),je("editable",e.editable),je("indentWidth",e.indentWidth),je("onChange",e.onChange),je("groupArraysAfterLength",e.groupArraysAfterLength),je("keyRenderer",e.keyRenderer),je("maxDisplayLength",e.maxDisplayLength),je("enableClipboard",e.enableClipboard),je("highlightUpdates",e.highlightUpdates),je("rootName",e.rootName),je("displayDataTypes",e.displayDataTypes),je("displayObjectSize",e.displayObjectSize),je("onCopy",e.onCopy),je("onSelect",e.onSelect),u.useEffect(function(){e.theme==="light"?t({colorspace:co}):e.theme==="dark"?t({colorspace:qs}):typeof e.theme=="object"&&t({colorspace:e.theme})},[t,e.theme]);var n=u.useMemo(function(){return typeof e.theme=="object"?"json-viewer-theme-custom":e.theme==="dark"?"json-viewer-theme-dark":"json-viewer-theme-light"},[e.theme]),r=u.useRef(!0),o=u.useMemo(function(){return bg()},[]),i=Qs(function(p){return p.registerTypes});if(r.current){var s=e.valueTypes?Ne(o).concat(Ne(e.valueTypes)):Ne(o);i(s),r.current=!1}u.useEffect(function(){var p=e.valueTypes?Ne(o).concat(Ne(e.valueTypes)):Ne(o);i(p)},[e.valueTypes,o,i]);var a=z(function(p){return p.value}),l=z(function(p){return p.prevValue}),c=z(function(p){return p.setHover}),f=u.useCallback(function(){return c(null)},[c]);return M(af,{elevation:0,className:kf(n,e.className),style:e.style,sx:J({fontFamily:"monospace",userSelect:"none",contentVisibility:"auto"},e.sx),onMouseLeave:f,children:M(Ht,{value:a,prevValue:l,path:u.useMemo(function(){return[]},[])})})},wg=function(t){var n=xg(),r,o=u.useMemo(function(){return t.theme==="auto"?n?"light":"dark":(r=t.theme)!==null&&r!==void 0?r:"light"},[n,t.theme]),i=u.useMemo(function(){var c=typeof o=="object"?o.base00:o==="dark"?qs.base00:co.base00;return so({components:{MuiPaper:{styleOverrides:{root:{backgroundColor:c}}}},palette:{mode:o==="dark"?"dark":"light",background:{default:c}}})},[o]),s=bn(J({},t),{theme:o}),a=u.useMemo(function(){return Vf(t)},[]),l=u.useMemo(function(){return hg()},[]);return M(X0,{theme:i,children:M(mo.Provider,{value:l,children:M(xn.Provider,{value:a,children:M(Cg,J({},s))})})})};/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function pt(e,t){return typeof e=="function"?e(t):e}function Te(e,t){return n=>{t.setState(r=>({...r,[e]:pt(n,r[e])}))}}function ar(e){return e instanceof Function}function _g(e){return Array.isArray(e)&&e.every(t=>typeof t=="number")}function Rg(e,t){const n=[],r=o=>{o.forEach(i=>{n.push(i);const s=t(i);s!=null&&s.length&&r(s)})};return r(e),n}function D(e,t,n){let r=[],o;return i=>{let s;n.key&&n.debug&&(s=Date.now());const a=e(i);if(!(a.length!==r.length||a.some((f,p)=>r[p]!==f)))return o;r=a;let c;if(n.key&&n.debug&&(c=Date.now()),o=t(...a),n==null||n.onChange==null||n.onChange(o),n.key&&n.debug&&n!=null&&n.debug()){const f=Math.round((Date.now()-s)*100)/100,p=Math.round((Date.now()-c)*100)/100,g=p/16,m=(h,v)=>{for(h=String(h);h.length<v;)h=" "+h;return h};console.info(`%c⏱ ${m(p,5)} /${m(f,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*g,120))}deg 100% 31%);`,n==null?void 0:n.key)}return o}}function L(e,t,n,r){return{debug:()=>{var o;return(o=e==null?void 0:e.debugAll)!=null?o:e[t]},key:!1,onChange:r}}function Eg(e,t,n,r){const o=()=>{var s;return(s=i.getValue())!=null?s:e.options.renderFallbackValue},i={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(r),renderValue:o,getContext:D(()=>[e,n,t,i],(s,a,l,c)=>({table:s,column:a,row:l,cell:c,getValue:c.getValue,renderValue:c.renderValue}),L(e.options,"debugCells"))};return e._features.forEach(s=>{s.createCell==null||s.createCell(i,n,t,e)},{}),i}function Mg(e,t,n,r){var o,i;const a={...e._getDefaultColumnDef(),...t},l=a.accessorKey;let c=(o=(i=a.id)!=null?i:l?typeof String.prototype.replaceAll=="function"?l.replaceAll(".","_"):l.replace(/\./g,"_"):void 0)!=null?o:typeof a.header=="string"?a.header:void 0,f;if(a.accessorFn?f=a.accessorFn:l&&(l.includes(".")?f=g=>{let m=g;for(const v of l.split(".")){var h;m=(h=m)==null?void 0:h[v]}return m}:f=g=>g[a.accessorKey]),!c)throw new Error;let p={id:`${String(c)}`,accessorFn:f,parent:r,depth:n,columnDef:a,columns:[],getFlatColumns:D(()=>[!0],()=>{var g;return[p,...(g=p.columns)==null?void 0:g.flatMap(m=>m.getFlatColumns())]},L(e.options,"debugColumns")),getLeafColumns:D(()=>[e._getOrderColumnsFn()],g=>{var m;if((m=p.columns)!=null&&m.length){let h=p.columns.flatMap(v=>v.getLeafColumns());return g(h)}return[p]},L(e.options,"debugColumns"))};for(const g of e._features)g.createColumn==null||g.createColumn(p,e);return p}const be="debugHeaders";function li(e,t,n){var r;let i={id:(r=n.id)!=null?r:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const s=[],a=l=>{l.subHeaders&&l.subHeaders.length&&l.subHeaders.map(a),s.push(l)};return a(i),s},getContext:()=>({table:e,header:i,column:t})};return e._features.forEach(s=>{s.createHeader==null||s.createHeader(i,e)}),i}const Pg={createTable:e=>{e.getHeaderGroups=D(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,o)=>{var i,s;const a=(i=r==null?void 0:r.map(p=>n.find(g=>g.id===p)).filter(Boolean))!=null?i:[],l=(s=o==null?void 0:o.map(p=>n.find(g=>g.id===p)).filter(Boolean))!=null?s:[],c=n.filter(p=>!(r!=null&&r.includes(p.id))&&!(o!=null&&o.includes(p.id)));return Mn(t,[...a,...c,...l],e)},L(e.options,be)),e.getCenterHeaderGroups=D(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,o)=>(n=n.filter(i=>!(r!=null&&r.includes(i.id))&&!(o!=null&&o.includes(i.id))),Mn(t,n,e,"center")),L(e.options,be)),e.getLeftHeaderGroups=D(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,r)=>{var o;const i=(o=r==null?void 0:r.map(s=>n.find(a=>a.id===s)).filter(Boolean))!=null?o:[];return Mn(t,i,e,"left")},L(e.options,be)),e.getRightHeaderGroups=D(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,r)=>{var o;const i=(o=r==null?void 0:r.map(s=>n.find(a=>a.id===s)).filter(Boolean))!=null?o:[];return Mn(t,i,e,"right")},L(e.options,be)),e.getFooterGroups=D(()=>[e.getHeaderGroups()],t=>[...t].reverse(),L(e.options,be)),e.getLeftFooterGroups=D(()=>[e.getLeftHeaderGroups()],t=>[...t].reverse(),L(e.options,be)),e.getCenterFooterGroups=D(()=>[e.getCenterHeaderGroups()],t=>[...t].reverse(),L(e.options,be)),e.getRightFooterGroups=D(()=>[e.getRightHeaderGroups()],t=>[...t].reverse(),L(e.options,be)),e.getFlatHeaders=D(()=>[e.getHeaderGroups()],t=>t.map(n=>n.headers).flat(),L(e.options,be)),e.getLeftFlatHeaders=D(()=>[e.getLeftHeaderGroups()],t=>t.map(n=>n.headers).flat(),L(e.options,be)),e.getCenterFlatHeaders=D(()=>[e.getCenterHeaderGroups()],t=>t.map(n=>n.headers).flat(),L(e.options,be)),e.getRightFlatHeaders=D(()=>[e.getRightHeaderGroups()],t=>t.map(n=>n.headers).flat(),L(e.options,be)),e.getCenterLeafHeaders=D(()=>[e.getCenterFlatHeaders()],t=>t.filter(n=>{var r;return!((r=n.subHeaders)!=null&&r.length)}),L(e.options,be)),e.getLeftLeafHeaders=D(()=>[e.getLeftFlatHeaders()],t=>t.filter(n=>{var r;return!((r=n.subHeaders)!=null&&r.length)}),L(e.options,be)),e.getRightLeafHeaders=D(()=>[e.getRightFlatHeaders()],t=>t.filter(n=>{var r;return!((r=n.subHeaders)!=null&&r.length)}),L(e.options,be)),e.getLeafHeaders=D(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(t,n,r)=>{var o,i,s,a,l,c;return[...(o=(i=t[0])==null?void 0:i.headers)!=null?o:[],...(s=(a=n[0])==null?void 0:a.headers)!=null?s:[],...(l=(c=r[0])==null?void 0:c.headers)!=null?l:[]].map(f=>f.getLeafHeaders()).flat()},L(e.options,be))}};function Mn(e,t,n,r){var o,i;let s=0;const a=function(g,m){m===void 0&&(m=1),s=Math.max(s,m),g.filter(h=>h.getIsVisible()).forEach(h=>{var v;(v=h.columns)!=null&&v.length&&a(h.columns,m+1)},0)};a(e);let l=[];const c=(g,m)=>{const h={depth:m,id:[r,`${m}`].filter(Boolean).join("_"),headers:[]},v=[];g.forEach(y=>{const S=[...v].reverse()[0],x=y.column.depth===h.depth;let C,_=!1;if(x&&y.column.parent?C=y.column.parent:(C=y.column,_=!0),S&&(S==null?void 0:S.column)===C)S.subHeaders.push(y);else{const P=li(n,C,{id:[r,m,C.id,y==null?void 0:y.id].filter(Boolean).join("_"),isPlaceholder:_,placeholderId:_?`${v.filter($=>$.column===C).length}`:void 0,depth:m,index:v.length});P.subHeaders.push(y),v.push(P)}h.headers.push(y),y.headerGroup=h}),l.push(h),m>0&&c(v,m-1)},f=t.map((g,m)=>li(n,g,{depth:s,index:m}));c(f,s-1),l.reverse();const p=g=>g.filter(h=>h.column.getIsVisible()).map(h=>{let v=0,y=0,S=[0];h.subHeaders&&h.subHeaders.length?(S=[],p(h.subHeaders).forEach(C=>{let{colSpan:_,rowSpan:P}=C;v+=_,S.push(P)})):v=1;const x=Math.min(...S);return y=y+x,h.colSpan=v,h.rowSpan=y,{colSpan:v,rowSpan:y}});return p((o=(i=l[0])==null?void 0:i.headers)!=null?o:[]),l}const $g=(e,t,n,r,o,i,s)=>{let a={id:t,index:r,original:n,depth:o,parentId:s,_valuesCache:{},_uniqueValuesCache:{},getValue:l=>{if(a._valuesCache.hasOwnProperty(l))return a._valuesCache[l];const c=e.getColumn(l);if(c!=null&&c.accessorFn)return a._valuesCache[l]=c.accessorFn(a.original,r),a._valuesCache[l]},getUniqueValues:l=>{if(a._uniqueValuesCache.hasOwnProperty(l))return a._uniqueValuesCache[l];const c=e.getColumn(l);if(c!=null&&c.accessorFn)return c.columnDef.getUniqueValues?(a._uniqueValuesCache[l]=c.columnDef.getUniqueValues(a.original,r),a._uniqueValuesCache[l]):(a._uniqueValuesCache[l]=[a.getValue(l)],a._uniqueValuesCache[l])},renderValue:l=>{var c;return(c=a.getValue(l))!=null?c:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>Rg(a.subRows,l=>l.subRows),getParentRow:()=>a.parentId?e.getRow(a.parentId,!0):void 0,getParentRows:()=>{let l=[],c=a;for(;;){const f=c.getParentRow();if(!f)break;l.push(f),c=f}return l.reverse()},getAllCells:D(()=>[e.getAllLeafColumns()],l=>l.map(c=>Eg(e,a,c,c.id)),L(e.options,"debugRows")),_getAllCellsByColumnId:D(()=>[a.getAllCells()],l=>l.reduce((c,f)=>(c[f.column.id]=f,c),{}),L(e.options,"debugRows"))};for(let l=0;l<e._features.length;l++){const c=e._features[l];c==null||c.createRow==null||c.createRow(a,e)}return a},Fg={createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},ea=(e,t,n)=>{var r,o;const i=n==null||(r=n.toString())==null?void 0:r.toLowerCase();return!!(!((o=e.getValue(t))==null||(o=o.toString())==null||(o=o.toLowerCase())==null)&&o.includes(i))};ea.autoRemove=e=>Ke(e);const ta=(e,t,n)=>{var r;return!!(!((r=e.getValue(t))==null||(r=r.toString())==null)&&r.includes(n))};ta.autoRemove=e=>Ke(e);const na=(e,t,n)=>{var r;return((r=e.getValue(t))==null||(r=r.toString())==null?void 0:r.toLowerCase())===(n==null?void 0:n.toLowerCase())};na.autoRemove=e=>Ke(e);const ra=(e,t,n)=>{var r;return(r=e.getValue(t))==null?void 0:r.includes(n)};ra.autoRemove=e=>Ke(e);const oa=(e,t,n)=>!n.some(r=>{var o;return!((o=e.getValue(t))!=null&&o.includes(r))});oa.autoRemove=e=>Ke(e)||!(e!=null&&e.length);const ia=(e,t,n)=>n.some(r=>{var o;return(o=e.getValue(t))==null?void 0:o.includes(r)});ia.autoRemove=e=>Ke(e)||!(e!=null&&e.length);const sa=(e,t,n)=>e.getValue(t)===n;sa.autoRemove=e=>Ke(e);const aa=(e,t,n)=>e.getValue(t)==n;aa.autoRemove=e=>Ke(e);const po=(e,t,n)=>{let[r,o]=n;const i=e.getValue(t);return i>=r&&i<=o};po.resolveFilterValue=e=>{let[t,n]=e,r=typeof t!="number"?parseFloat(t):t,o=typeof n!="number"?parseFloat(n):n,i=t===null||Number.isNaN(r)?-1/0:r,s=n===null||Number.isNaN(o)?1/0:o;if(i>s){const a=i;i=s,s=a}return[i,s]};po.autoRemove=e=>Ke(e)||Ke(e[0])&&Ke(e[1]);const it={includesString:ea,includesStringSensitive:ta,equalsString:na,arrIncludes:ra,arrIncludesAll:oa,arrIncludesSome:ia,equals:sa,weakEquals:aa,inNumberRange:po};function Ke(e){return e==null||e===""}const kg={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:Te("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{const n=t.getCoreRowModel().flatRows[0],r=n==null?void 0:n.getValue(e.id);return typeof r=="string"?it.includesString:typeof r=="number"?it.inNumberRange:typeof r=="boolean"||r!==null&&typeof r=="object"?it.equals:Array.isArray(r)?it.arrIncludes:it.weakEquals},e.getFilterFn=()=>{var n,r;return ar(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(n=(r=t.options.filterFns)==null?void 0:r[e.columnDef.filterFn])!=null?n:it[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,r,o;return((n=e.columnDef.enableColumnFilter)!=null?n:!0)&&((r=t.options.enableColumnFilters)!=null?r:!0)&&((o=t.options.enableFilters)!=null?o:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return(n=t.getState().columnFilters)==null||(n=n.find(r=>r.id===e.id))==null?void 0:n.value},e.getFilterIndex=()=>{var n,r;return(n=(r=t.getState().columnFilters)==null?void 0:r.findIndex(o=>o.id===e.id))!=null?n:-1},e.setFilterValue=n=>{t.setColumnFilters(r=>{const o=e.getFilterFn(),i=r==null?void 0:r.find(f=>f.id===e.id),s=pt(n,i?i.value:void 0);if(ui(o,s,e)){var a;return(a=r==null?void 0:r.filter(f=>f.id!==e.id))!=null?a:[]}const l={id:e.id,value:s};if(i){var c;return(c=r==null?void 0:r.map(f=>f.id===e.id?l:f))!=null?c:[]}return r!=null&&r.length?[...r,l]:[l]})}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{const n=e.getAllLeafColumns(),r=o=>{var i;return(i=pt(t,o))==null?void 0:i.filter(s=>{const a=n.find(l=>l.id===s.id);if(a){const l=a.getFilterFn();if(ui(l,s.value,a))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(r)},e.resetColumnFilters=t=>{var n,r;e.setColumnFilters(t?[]:(n=(r=e.initialState)==null?void 0:r.columnFilters)!=null?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function ui(e,t,n){return(e&&e.autoRemove?e.autoRemove(t,n):!1)||typeof t>"u"||typeof t=="string"&&!t}const jg=(e,t,n)=>n.reduce((r,o)=>{const i=o.getValue(e);return r+(typeof i=="number"?i:0)},0),Ag=(e,t,n)=>{let r;return n.forEach(o=>{const i=o.getValue(e);i!=null&&(r>i||r===void 0&&i>=i)&&(r=i)}),r},Ig=(e,t,n)=>{let r;return n.forEach(o=>{const i=o.getValue(e);i!=null&&(r<i||r===void 0&&i>=i)&&(r=i)}),r},Vg=(e,t,n)=>{let r,o;return n.forEach(i=>{const s=i.getValue(e);s!=null&&(r===void 0?s>=s&&(r=o=s):(r>s&&(r=s),o<s&&(o=s)))}),[r,o]},Tg=(e,t)=>{let n=0,r=0;if(t.forEach(o=>{let i=o.getValue(e);i!=null&&(i=+i)>=i&&(++n,r+=i)}),n)return r/n},Og=(e,t)=>{if(!t.length)return;const n=t.map(i=>i.getValue(e));if(!_g(n))return;if(n.length===1)return n[0];const r=Math.floor(n.length/2),o=n.sort((i,s)=>i-s);return n.length%2!==0?o[r]:(o[r-1]+o[r])/2},Dg=(e,t)=>Array.from(new Set(t.map(n=>n.getValue(e))).values()),Lg=(e,t)=>new Set(t.map(n=>n.getValue(e))).size,Hg=(e,t)=>t.length,vr={sum:jg,min:Ag,max:Ig,extent:Vg,mean:Tg,median:Og,unique:Dg,uniqueCount:Lg,count:Hg},zg={getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return(t=(n=e.getValue())==null||n.toString==null?void 0:n.toString())!=null?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:Te("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(n=>n!=null&&n.includes(e.id)?n.filter(r=>r!==e.id):[...n??[],e.id])},e.getCanGroup=()=>{var n,r;return((n=e.columnDef.enableGrouping)!=null?n:!0)&&((r=t.options.enableGrouping)!=null?r:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var n;return(n=t.getState().grouping)==null?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return(n=t.getState().grouping)==null?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const n=e.getCanGroup();return()=>{n&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const n=t.getCoreRowModel().flatRows[0],r=n==null?void 0:n.getValue(e.id);if(typeof r=="number")return vr.sum;if(Object.prototype.toString.call(r)==="[object Date]")return vr.extent},e.getAggregationFn=()=>{var n,r;if(!e)throw new Error;return ar(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(n=(r=t.options.aggregationFns)==null?void 0:r[e.columnDef.aggregationFn])!=null?n:vr[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var n,r;e.setGrouping(t?[]:(n=(r=e.initialState)==null?void 0:r.grouping)!=null?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];const r=t.getColumn(n);return r!=null&&r.columnDef.getGroupingValue?(e._groupingValuesCache[n]=r.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,t,n,r)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var o;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((o=n.subRows)!=null&&o.length)}}};function Ng(e,t,n){if(!(t!=null&&t.length)||!n)return e;const r=e.filter(i=>!t.includes(i.id));return n==="remove"?r:[...t.map(i=>e.find(s=>s.id===i)).filter(Boolean),...r]}const Bg={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:Te("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=D(n=>[nn(t,n)],n=>n.findIndex(r=>r.id===e.id),L(t.options,"debugColumns")),e.getIsFirstColumn=n=>{var r;return((r=nn(t,n)[0])==null?void 0:r.id)===e.id},e.getIsLastColumn=n=>{var r;const o=nn(t,n);return((r=o[o.length-1])==null?void 0:r.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var n;e.setColumnOrder(t?[]:(n=e.initialState.columnOrder)!=null?n:[])},e._getOrderColumnsFn=D(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(t,n,r)=>o=>{let i=[];if(!(t!=null&&t.length))i=o;else{const s=[...t],a=[...o];for(;a.length&&s.length;){const l=s.shift(),c=a.findIndex(f=>f.id===l);c>-1&&i.push(a.splice(c,1)[0])}i=[...i,...a]}return Ng(i,n,r)},L(e.options,"debugTable"))}},yr=()=>({left:[],right:[]}),Gg={getInitialState:e=>({columnPinning:yr(),...e}),getDefaultOptions:e=>({onColumnPinningChange:Te("columnPinning",e)}),createColumn:(e,t)=>{e.pin=n=>{const r=e.getLeafColumns().map(o=>o.id).filter(Boolean);t.setColumnPinning(o=>{var i,s;if(n==="right"){var a,l;return{left:((a=o==null?void 0:o.left)!=null?a:[]).filter(p=>!(r!=null&&r.includes(p))),right:[...((l=o==null?void 0:o.right)!=null?l:[]).filter(p=>!(r!=null&&r.includes(p))),...r]}}if(n==="left"){var c,f;return{left:[...((c=o==null?void 0:o.left)!=null?c:[]).filter(p=>!(r!=null&&r.includes(p))),...r],right:((f=o==null?void 0:o.right)!=null?f:[]).filter(p=>!(r!=null&&r.includes(p)))}}return{left:((i=o==null?void 0:o.left)!=null?i:[]).filter(p=>!(r!=null&&r.includes(p))),right:((s=o==null?void 0:o.right)!=null?s:[]).filter(p=>!(r!=null&&r.includes(p)))}})},e.getCanPin=()=>e.getLeafColumns().some(r=>{var o,i,s;return((o=r.columnDef.enablePinning)!=null?o:!0)&&((i=(s=t.options.enableColumnPinning)!=null?s:t.options.enablePinning)!=null?i:!0)}),e.getIsPinned=()=>{const n=e.getLeafColumns().map(a=>a.id),{left:r,right:o}=t.getState().columnPinning,i=n.some(a=>r==null?void 0:r.includes(a)),s=n.some(a=>o==null?void 0:o.includes(a));return i?"left":s?"right":!1},e.getPinnedIndex=()=>{var n,r;const o=e.getIsPinned();return o?(n=(r=t.getState().columnPinning)==null||(r=r[o])==null?void 0:r.indexOf(e.id))!=null?n:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=D(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(n,r,o)=>{const i=[...r??[],...o??[]];return n.filter(s=>!i.includes(s.column.id))},L(t.options,"debugRows")),e.getLeftVisibleCells=D(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left],(n,r)=>(r??[]).map(i=>n.find(s=>s.column.id===i)).filter(Boolean).map(i=>({...i,position:"left"})),L(t.options,"debugRows")),e.getRightVisibleCells=D(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(n,r)=>(r??[]).map(i=>n.find(s=>s.column.id===i)).filter(Boolean).map(i=>({...i,position:"right"})),L(t.options,"debugRows"))},createTable:e=>{e.setColumnPinning=t=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var n,r;return e.setColumnPinning(t?yr():(n=(r=e.initialState)==null?void 0:r.columnPinning)!=null?n:yr())},e.getIsSomeColumnsPinned=t=>{var n;const r=e.getState().columnPinning;if(!t){var o,i;return!!((o=r.left)!=null&&o.length||(i=r.right)!=null&&i.length)}return!!((n=r[t])!=null&&n.length)},e.getLeftLeafColumns=D(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(t,n)=>(n??[]).map(r=>t.find(o=>o.id===r)).filter(Boolean),L(e.options,"debugColumns")),e.getRightLeafColumns=D(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(t,n)=>(n??[]).map(r=>t.find(o=>o.id===r)).filter(Boolean),L(e.options,"debugColumns")),e.getCenterLeafColumns=D(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r)=>{const o=[...n??[],...r??[]];return t.filter(i=>!o.includes(i.id))},L(e.options,"debugColumns"))}};function Zg(e){return e||(typeof document<"u"?document:null)}const Pn={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},Sr=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),Wg={getDefaultColumnDef:()=>Pn,getInitialState:e=>({columnSizing:{},columnSizingInfo:Sr(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:Te("columnSizing",e),onColumnSizingInfoChange:Te("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var n,r,o;const i=t.getState().columnSizing[e.id];return Math.min(Math.max((n=e.columnDef.minSize)!=null?n:Pn.minSize,(r=i??e.columnDef.size)!=null?r:Pn.size),(o=e.columnDef.maxSize)!=null?o:Pn.maxSize)},e.getStart=D(n=>[n,nn(t,n),t.getState().columnSizing],(n,r)=>r.slice(0,e.getIndex(n)).reduce((o,i)=>o+i.getSize(),0),L(t.options,"debugColumns")),e.getAfter=D(n=>[n,nn(t,n),t.getState().columnSizing],(n,r)=>r.slice(e.getIndex(n)+1).reduce((o,i)=>o+i.getSize(),0),L(t.options,"debugColumns")),e.resetSize=()=>{t.setColumnSizing(n=>{let{[e.id]:r,...o}=n;return o})},e.getCanResize=()=>{var n,r;return((n=e.columnDef.enableResizing)!=null?n:!0)&&((r=t.options.enableColumnResizing)!=null?r:!0)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let n=0;const r=o=>{if(o.subHeaders.length)o.subHeaders.forEach(r);else{var i;n+=(i=o.column.getSize())!=null?i:0}};return r(e),n},e.getStart=()=>{if(e.index>0){const n=e.headerGroup.headers[e.index-1];return n.getStart()+n.getSize()}return 0},e.getResizeHandler=n=>{const r=t.getColumn(e.column.id),o=r==null?void 0:r.getCanResize();return i=>{if(!r||!o||(i.persist==null||i.persist(),br(i)&&i.touches&&i.touches.length>1))return;const s=e.getSize(),a=e?e.getLeafHeaders().map(S=>[S.column.id,S.column.getSize()]):[[r.id,r.getSize()]],l=br(i)?Math.round(i.touches[0].clientX):i.clientX,c={},f=(S,x)=>{typeof x=="number"&&(t.setColumnSizingInfo(C=>{var _,P;const $=t.options.columnResizeDirection==="rtl"?-1:1,k=(x-((_=C==null?void 0:C.startOffset)!=null?_:0))*$,T=Math.max(k/((P=C==null?void 0:C.startSize)!=null?P:0),-.999999);return C.columnSizingStart.forEach(B=>{let[O,E]=B;c[O]=Math.round(Math.max(E+E*T,0)*100)/100}),{...C,deltaOffset:k,deltaPercentage:T}}),(t.options.columnResizeMode==="onChange"||S==="end")&&t.setColumnSizing(C=>({...C,...c})))},p=S=>f("move",S),g=S=>{f("end",S),t.setColumnSizingInfo(x=>({...x,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},m=Zg(n),h={moveHandler:S=>p(S.clientX),upHandler:S=>{m==null||m.removeEventListener("mousemove",h.moveHandler),m==null||m.removeEventListener("mouseup",h.upHandler),g(S.clientX)}},v={moveHandler:S=>(S.cancelable&&(S.preventDefault(),S.stopPropagation()),p(S.touches[0].clientX),!1),upHandler:S=>{var x;m==null||m.removeEventListener("touchmove",v.moveHandler),m==null||m.removeEventListener("touchend",v.upHandler),S.cancelable&&(S.preventDefault(),S.stopPropagation()),g((x=S.touches[0])==null?void 0:x.clientX)}},y=qg()?{passive:!1}:!1;br(i)?(m==null||m.addEventListener("touchmove",v.moveHandler,y),m==null||m.addEventListener("touchend",v.upHandler,y)):(m==null||m.addEventListener("mousemove",h.moveHandler,y),m==null||m.addEventListener("mouseup",h.upHandler,y)),t.setColumnSizingInfo(S=>({...S,startOffset:l,startSize:s,deltaOffset:0,deltaPercentage:0,columnSizingStart:a,isResizingColumn:r.id}))}}},createTable:e=>{e.setColumnSizing=t=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var n;e.setColumnSizing(t?{}:(n=e.initialState.columnSizing)!=null?n:{})},e.resetHeaderSizeInfo=t=>{var n;e.setColumnSizingInfo(t?Sr():(n=e.initialState.columnSizingInfo)!=null?n:Sr())},e.getTotalSize=()=>{var t,n;return(t=(n=e.getHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0},e.getLeftTotalSize=()=>{var t,n;return(t=(n=e.getLeftHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0},e.getCenterTotalSize=()=>{var t,n;return(t=(n=e.getCenterHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0},e.getRightTotalSize=()=>{var t,n;return(t=(n=e.getRightHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0}}};let $n=null;function qg(){if(typeof $n=="boolean")return $n;let e=!1;try{const t={get passive(){return e=!0,!1}},n=()=>{};window.addEventListener("test",n,t),window.removeEventListener("test",n)}catch{e=!1}return $n=e,$n}function br(e){return e.type==="touchstart"}const Ug={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:Te("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=n=>{e.getCanHide()&&t.setColumnVisibility(r=>({...r,[e.id]:n??!e.getIsVisible()}))},e.getIsVisible=()=>{var n,r;const o=e.columns;return(n=o.length?o.some(i=>i.getIsVisible()):(r=t.getState().columnVisibility)==null?void 0:r[e.id])!=null?n:!0},e.getCanHide=()=>{var n,r;return((n=e.columnDef.enableHiding)!=null?n:!0)&&((r=t.options.enableHiding)!=null?r:!0)},e.getToggleVisibilityHandler=()=>n=>{e.toggleVisibility==null||e.toggleVisibility(n.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=D(()=>[e.getAllCells(),t.getState().columnVisibility],n=>n.filter(r=>r.column.getIsVisible()),L(t.options,"debugRows")),e.getVisibleCells=D(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(n,r,o)=>[...n,...r,...o],L(t.options,"debugRows"))},createTable:e=>{const t=(n,r)=>D(()=>[r(),r().filter(o=>o.getIsVisible()).map(o=>o.id).join("_")],o=>o.filter(i=>i.getIsVisible==null?void 0:i.getIsVisible()),L(e.options,"debugColumns"));e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=n=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(n),e.resetColumnVisibility=n=>{var r;e.setColumnVisibility(n?{}:(r=e.initialState.columnVisibility)!=null?r:{})},e.toggleAllColumnsVisible=n=>{var r;n=(r=n)!=null?r:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((o,i)=>({...o,[i.id]:n||!(i.getCanHide!=null&&i.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(n=>!(n.getIsVisible!=null&&n.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(n=>n.getIsVisible==null?void 0:n.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>n=>{var r;e.toggleAllColumnsVisible((r=n.target)==null?void 0:r.checked)}}};function nn(e,t){return t?t==="center"?e.getCenterVisibleLeafColumns():t==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const Kg={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},Yg={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:Te("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n;const r=(n=e.getCoreRowModel().flatRows[0])==null||(n=n._getAllCellsByColumnId()[t.id])==null?void 0:n.getValue();return typeof r=="string"||typeof r=="number"}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var n,r,o,i;return((n=e.columnDef.enableGlobalFilter)!=null?n:!0)&&((r=t.options.enableGlobalFilter)!=null?r:!0)&&((o=t.options.enableFilters)!=null?o:!0)&&((i=t.options.getColumnCanGlobalFilter==null?void 0:t.options.getColumnCanGlobalFilter(e))!=null?i:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>it.includesString,e.getGlobalFilterFn=()=>{var t,n;const{globalFilterFn:r}=e.options;return ar(r)?r:r==="auto"?e.getGlobalAutoFilterFn():(t=(n=e.options.filterFns)==null?void 0:n[r])!=null?t:it[r]},e.setGlobalFilter=t=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},Xg={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:Te("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;e._autoResetExpanded=()=>{var r,o;if(!t){e._queue(()=>{t=!0});return}if((r=(o=e.options.autoResetAll)!=null?o:e.options.autoResetExpanded)!=null?r:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=r=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(r),e.toggleAllRowsExpanded=r=>{r??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=r=>{var o,i;e.setExpanded(r?{}:(o=(i=e.initialState)==null?void 0:i.expanded)!=null?o:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(r=>r.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>r=>{r.persist==null||r.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const r=e.getState().expanded;return r===!0||Object.values(r).some(Boolean)},e.getIsAllRowsExpanded=()=>{const r=e.getState().expanded;return typeof r=="boolean"?r===!0:!(!Object.keys(r).length||e.getRowModel().flatRows.some(o=>!o.getIsExpanded()))},e.getExpandedDepth=()=>{let r=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(i=>{const s=i.split(".");r=Math.max(r,s.length)}),r},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,t)=>{e.toggleExpanded=n=>{t.setExpanded(r=>{var o;const i=r===!0?!0:!!(r!=null&&r[e.id]);let s={};if(r===!0?Object.keys(t.getRowModel().rowsById).forEach(a=>{s[a]=!0}):s=r,n=(o=n)!=null?o:!i,!i&&n)return{...s,[e.id]:!0};if(i&&!n){const{[e.id]:a,...l}=s;return l}return r})},e.getIsExpanded=()=>{var n;const r=t.getState().expanded;return!!((n=t.options.getIsRowExpanded==null?void 0:t.options.getIsRowExpanded(e))!=null?n:r===!0||r!=null&&r[e.id])},e.getCanExpand=()=>{var n,r,o;return(n=t.options.getRowCanExpand==null?void 0:t.options.getRowCanExpand(e))!=null?n:((r=t.options.enableExpanding)!=null?r:!0)&&!!((o=e.subRows)!=null&&o.length)},e.getIsAllParentsExpanded=()=>{let n=!0,r=e;for(;n&&r.parentId;)r=t.getRow(r.parentId,!0),n=r.getIsExpanded();return n},e.getToggleExpandedHandler=()=>{const n=e.getCanExpand();return()=>{n&&e.toggleExpanded()}}}},Dr=0,Lr=10,xr=()=>({pageIndex:Dr,pageSize:Lr}),Jg={getInitialState:e=>({...e,pagination:{...xr(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:Te("pagination",e)}),createTable:e=>{let t=!1,n=!1;e._autoResetPageIndex=()=>{var r,o;if(!t){e._queue(()=>{t=!0});return}if((r=(o=e.options.autoResetAll)!=null?o:e.options.autoResetPageIndex)!=null?r:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=r=>{const o=i=>pt(r,i);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(o)},e.resetPagination=r=>{var o;e.setPagination(r?xr():(o=e.initialState.pagination)!=null?o:xr())},e.setPageIndex=r=>{e.setPagination(o=>{let i=pt(r,o.pageIndex);const s=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return i=Math.max(0,Math.min(i,s)),{...o,pageIndex:i}})},e.resetPageIndex=r=>{var o,i;e.setPageIndex(r?Dr:(o=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageIndex)!=null?o:Dr)},e.resetPageSize=r=>{var o,i;e.setPageSize(r?Lr:(o=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageSize)!=null?o:Lr)},e.setPageSize=r=>{e.setPagination(o=>{const i=Math.max(1,pt(r,o.pageSize)),s=o.pageSize*o.pageIndex,a=Math.floor(s/i);return{...o,pageIndex:a,pageSize:i}})},e.setPageCount=r=>e.setPagination(o=>{var i;let s=pt(r,(i=e.options.pageCount)!=null?i:-1);return typeof s=="number"&&(s=Math.max(-1,s)),{...o,pageCount:s}}),e.getPageOptions=D(()=>[e.getPageCount()],r=>{let o=[];return r&&r>0&&(o=[...new Array(r)].fill(null).map((i,s)=>s)),o},L(e.options,"debugTable")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:r}=e.getState().pagination,o=e.getPageCount();return o===-1?!0:o===0?!1:r<o-1},e.previousPage=()=>e.setPageIndex(r=>r-1),e.nextPage=()=>e.setPageIndex(r=>r+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var r;return(r=e.options.pageCount)!=null?r:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var r;return(r=e.options.rowCount)!=null?r:e.getPrePaginationRowModel().rows.length}}},Cr=()=>({top:[],bottom:[]}),Qg={getInitialState:e=>({rowPinning:Cr(),...e}),getDefaultOptions:e=>({onRowPinningChange:Te("rowPinning",e)}),createRow:(e,t)=>{e.pin=(n,r,o)=>{const i=r?e.getLeafRows().map(l=>{let{id:c}=l;return c}):[],s=o?e.getParentRows().map(l=>{let{id:c}=l;return c}):[],a=new Set([...s,e.id,...i]);t.setRowPinning(l=>{var c,f;if(n==="bottom"){var p,g;return{top:((p=l==null?void 0:l.top)!=null?p:[]).filter(v=>!(a!=null&&a.has(v))),bottom:[...((g=l==null?void 0:l.bottom)!=null?g:[]).filter(v=>!(a!=null&&a.has(v))),...Array.from(a)]}}if(n==="top"){var m,h;return{top:[...((m=l==null?void 0:l.top)!=null?m:[]).filter(v=>!(a!=null&&a.has(v))),...Array.from(a)],bottom:((h=l==null?void 0:l.bottom)!=null?h:[]).filter(v=>!(a!=null&&a.has(v)))}}return{top:((c=l==null?void 0:l.top)!=null?c:[]).filter(v=>!(a!=null&&a.has(v))),bottom:((f=l==null?void 0:l.bottom)!=null?f:[]).filter(v=>!(a!=null&&a.has(v)))}})},e.getCanPin=()=>{var n;const{enableRowPinning:r,enablePinning:o}=t.options;return typeof r=="function"?r(e):(n=r??o)!=null?n:!0},e.getIsPinned=()=>{const n=[e.id],{top:r,bottom:o}=t.getState().rowPinning,i=n.some(a=>r==null?void 0:r.includes(a)),s=n.some(a=>o==null?void 0:o.includes(a));return i?"top":s?"bottom":!1},e.getPinnedIndex=()=>{var n,r;const o=e.getIsPinned();if(!o)return-1;const i=(n=o==="top"?t.getTopRows():t.getBottomRows())==null?void 0:n.map(s=>{let{id:a}=s;return a});return(r=i==null?void 0:i.indexOf(e.id))!=null?r:-1}},createTable:e=>{e.setRowPinning=t=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var n,r;return e.setRowPinning(t?Cr():(n=(r=e.initialState)==null?void 0:r.rowPinning)!=null?n:Cr())},e.getIsSomeRowsPinned=t=>{var n;const r=e.getState().rowPinning;if(!t){var o,i;return!!((o=r.top)!=null&&o.length||(i=r.bottom)!=null&&i.length)}return!!((n=r[t])!=null&&n.length)},e._getPinnedRows=(t,n,r)=>{var o;return((o=e.options.keepPinnedRows)==null||o?(n??[]).map(s=>{const a=e.getRow(s,!0);return a.getIsAllParentsExpanded()?a:null}):(n??[]).map(s=>t.find(a=>a.id===s))).filter(Boolean).map(s=>({...s,position:r}))},e.getTopRows=D(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(t,n)=>e._getPinnedRows(t,n,"top"),L(e.options,"debugRows")),e.getBottomRows=D(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(t,n)=>e._getPinnedRows(t,n,"bottom"),L(e.options,"debugRows")),e.getCenterRows=D(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(t,n,r)=>{const o=new Set([...n??[],...r??[]]);return t.filter(i=>!o.has(i.id))},L(e.options,"debugRows"))}},em={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:Te("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var n;return e.setRowSelection(t?{}:(n=e.initialState.rowSelection)!=null?n:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(n=>{t=typeof t<"u"?t:!e.getIsAllRowsSelected();const r={...n},o=e.getPreGroupedRowModel().flatRows;return t?o.forEach(i=>{i.getCanSelect()&&(r[i.id]=!0)}):o.forEach(i=>{delete r[i.id]}),r})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(n=>{const r=typeof t<"u"?t:!e.getIsAllPageRowsSelected(),o={...n};return e.getRowModel().rows.forEach(i=>{Hr(o,i.id,r,!0,e)}),o}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=D(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?wr(e,n):{rows:[],flatRows:[],rowsById:{}},L(e.options,"debugTable")),e.getFilteredSelectedRowModel=D(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?wr(e,n):{rows:[],flatRows:[],rowsById:{}},L(e.options,"debugTable")),e.getGroupedSelectedRowModel=D(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?wr(e,n):{rows:[],flatRows:[],rowsById:{}},L(e.options,"debugTable")),e.getIsAllRowsSelected=()=>{const t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState();let r=!!(t.length&&Object.keys(n).length);return r&&t.some(o=>o.getCanSelect()&&!n[o.id])&&(r=!1),r},e.getIsAllPageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows.filter(o=>o.getCanSelect()),{rowSelection:n}=e.getState();let r=!!t.length;return r&&t.some(o=>!n[o.id])&&(r=!1),r},e.getIsSomeRowsSelected=()=>{var t;const n=Object.keys((t=e.getState().rowSelection)!=null?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:t.filter(n=>n.getCanSelect()).some(n=>n.getIsSelected()||n.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(n,r)=>{const o=e.getIsSelected();t.setRowSelection(i=>{var s;if(n=typeof n<"u"?n:!o,e.getCanSelect()&&o===n)return i;const a={...i};return Hr(a,e.id,n,(s=r==null?void 0:r.selectChildren)!=null?s:!0,t),a})},e.getIsSelected=()=>{const{rowSelection:n}=t.getState();return ho(e,n)},e.getIsSomeSelected=()=>{const{rowSelection:n}=t.getState();return zr(e,n)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:n}=t.getState();return zr(e,n)==="all"},e.getCanSelect=()=>{var n;return typeof t.options.enableRowSelection=="function"?t.options.enableRowSelection(e):(n=t.options.enableRowSelection)!=null?n:!0},e.getCanSelectSubRows=()=>{var n;return typeof t.options.enableSubRowSelection=="function"?t.options.enableSubRowSelection(e):(n=t.options.enableSubRowSelection)!=null?n:!0},e.getCanMultiSelect=()=>{var n;return typeof t.options.enableMultiRowSelection=="function"?t.options.enableMultiRowSelection(e):(n=t.options.enableMultiRowSelection)!=null?n:!0},e.getToggleSelectedHandler=()=>{const n=e.getCanSelect();return r=>{var o;n&&e.toggleSelected((o=r.target)==null?void 0:o.checked)}}}},Hr=(e,t,n,r,o)=>{var i;const s=o.getRow(t,!0);n?(s.getCanMultiSelect()||Object.keys(e).forEach(a=>delete e[a]),s.getCanSelect()&&(e[t]=!0)):delete e[t],r&&(i=s.subRows)!=null&&i.length&&s.getCanSelectSubRows()&&s.subRows.forEach(a=>Hr(e,a.id,n,r,o))};function wr(e,t){const n=e.getState().rowSelection,r=[],o={},i=function(s,a){return s.map(l=>{var c;const f=ho(l,n);if(f&&(r.push(l),o[l.id]=l),(c=l.subRows)!=null&&c.length&&(l={...l,subRows:i(l.subRows)}),f)return l}).filter(Boolean)};return{rows:i(t.rows),flatRows:r,rowsById:o}}function ho(e,t){var n;return(n=t[e.id])!=null?n:!1}function zr(e,t,n){var r;if(!((r=e.subRows)!=null&&r.length))return!1;let o=!0,i=!1;return e.subRows.forEach(s=>{if(!(i&&!o)&&(s.getCanSelect()&&(ho(s,t)?i=!0:o=!1),s.subRows&&s.subRows.length)){const a=zr(s,t);a==="all"?i=!0:(a==="some"&&(i=!0),o=!1)}}),o?"all":i?"some":!1}const Nr=/([0-9]+)/gm,tm=(e,t,n)=>la(yt(e.getValue(n)).toLowerCase(),yt(t.getValue(n)).toLowerCase()),nm=(e,t,n)=>la(yt(e.getValue(n)),yt(t.getValue(n))),rm=(e,t,n)=>vo(yt(e.getValue(n)).toLowerCase(),yt(t.getValue(n)).toLowerCase()),om=(e,t,n)=>vo(yt(e.getValue(n)),yt(t.getValue(n))),im=(e,t,n)=>{const r=e.getValue(n),o=t.getValue(n);return r>o?1:r<o?-1:0},sm=(e,t,n)=>vo(e.getValue(n),t.getValue(n));function vo(e,t){return e===t?0:e>t?1:-1}function yt(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function la(e,t){const n=e.split(Nr).filter(Boolean),r=t.split(Nr).filter(Boolean);for(;n.length&&r.length;){const o=n.shift(),i=r.shift(),s=parseInt(o,10),a=parseInt(i,10),l=[s,a].sort();if(isNaN(l[0])){if(o>i)return 1;if(i>o)return-1;continue}if(isNaN(l[1]))return isNaN(s)?-1:1;if(s>a)return 1;if(a>s)return-1}return n.length-r.length}const Qt={alphanumeric:tm,alphanumericCaseSensitive:nm,text:rm,textCaseSensitive:om,datetime:im,basic:sm},am={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:Te("sorting",e),isMultiSortEvent:t=>t.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{const n=t.getFilteredRowModel().flatRows.slice(10);let r=!1;for(const o of n){const i=o==null?void 0:o.getValue(e.id);if(Object.prototype.toString.call(i)==="[object Date]")return Qt.datetime;if(typeof i=="string"&&(r=!0,i.split(Nr).length>1))return Qt.alphanumeric}return r?Qt.text:Qt.basic},e.getAutoSortDir=()=>{const n=t.getFilteredRowModel().flatRows[0];return typeof(n==null?void 0:n.getValue(e.id))=="string"?"asc":"desc"},e.getSortingFn=()=>{var n,r;if(!e)throw new Error;return ar(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(n=(r=t.options.sortingFns)==null?void 0:r[e.columnDef.sortingFn])!=null?n:Qt[e.columnDef.sortingFn]},e.toggleSorting=(n,r)=>{const o=e.getNextSortingOrder(),i=typeof n<"u"&&n!==null;t.setSorting(s=>{const a=s==null?void 0:s.find(m=>m.id===e.id),l=s==null?void 0:s.findIndex(m=>m.id===e.id);let c=[],f,p=i?n:o==="desc";if(s!=null&&s.length&&e.getCanMultiSort()&&r?a?f="toggle":f="add":s!=null&&s.length&&l!==s.length-1?f="replace":a?f="toggle":f="replace",f==="toggle"&&(i||o||(f="remove")),f==="add"){var g;c=[...s,{id:e.id,desc:p}],c.splice(0,c.length-((g=t.options.maxMultiSortColCount)!=null?g:Number.MAX_SAFE_INTEGER))}else f==="toggle"?c=s.map(m=>m.id===e.id?{...m,desc:p}:m):f==="remove"?c=s.filter(m=>m.id!==e.id):c=[{id:e.id,desc:p}];return c})},e.getFirstSortDir=()=>{var n,r;return((n=(r=e.columnDef.sortDescFirst)!=null?r:t.options.sortDescFirst)!=null?n:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=n=>{var r,o;const i=e.getFirstSortDir(),s=e.getIsSorted();return s?s!==i&&((r=t.options.enableSortingRemoval)==null||r)&&(!(n&&(o=t.options.enableMultiRemove)!=null)||o)?!1:s==="desc"?"asc":"desc":i},e.getCanSort=()=>{var n,r;return((n=e.columnDef.enableSorting)!=null?n:!0)&&((r=t.options.enableSorting)!=null?r:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,r;return(n=(r=e.columnDef.enableMultiSort)!=null?r:t.options.enableMultiSort)!=null?n:!!e.accessorFn},e.getIsSorted=()=>{var n;const r=(n=t.getState().sorting)==null?void 0:n.find(o=>o.id===e.id);return r?r.desc?"desc":"asc":!1},e.getSortIndex=()=>{var n,r;return(n=(r=t.getState().sorting)==null?void 0:r.findIndex(o=>o.id===e.id))!=null?n:-1},e.clearSorting=()=>{t.setSorting(n=>n!=null&&n.length?n.filter(r=>r.id!==e.id):[])},e.getToggleSortingHandler=()=>{const n=e.getCanSort();return r=>{n&&(r.persist==null||r.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?t.options.isMultiSortEvent==null?void 0:t.options.isMultiSortEvent(r):!1))}}},createTable:e=>{e.setSorting=t=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var n,r;e.setSorting(t?[]:(n=(r=e.initialState)==null?void 0:r.sorting)!=null?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},lm=[Pg,Ug,Bg,Gg,Fg,kg,Kg,Yg,am,zg,Xg,Jg,Qg,em,Wg];function um(e){var t,n;const r=[...lm,...(t=e._features)!=null?t:[]];let o={_features:r};const i=o._features.reduce((g,m)=>Object.assign(g,m.getDefaultOptions==null?void 0:m.getDefaultOptions(o)),{}),s=g=>o.options.mergeOptions?o.options.mergeOptions(i,g):{...i,...g};let l={...{},...(n=e.initialState)!=null?n:{}};o._features.forEach(g=>{var m;l=(m=g.getInitialState==null?void 0:g.getInitialState(l))!=null?m:l});const c=[];let f=!1;const p={_features:r,options:{...i,...e},initialState:l,_queue:g=>{c.push(g),f||(f=!0,Promise.resolve().then(()=>{for(;c.length;)c.shift()();f=!1}).catch(m=>setTimeout(()=>{throw m})))},reset:()=>{o.setState(o.initialState)},setOptions:g=>{const m=pt(g,o.options);o.options=s(m)},getState:()=>o.options.state,setState:g=>{o.options.onStateChange==null||o.options.onStateChange(g)},_getRowId:(g,m,h)=>{var v;return(v=o.options.getRowId==null?void 0:o.options.getRowId(g,m,h))!=null?v:`${h?[h.id,m].join("."):m}`},getCoreRowModel:()=>(o._getCoreRowModel||(o._getCoreRowModel=o.options.getCoreRowModel(o)),o._getCoreRowModel()),getRowModel:()=>o.getPaginationRowModel(),getRow:(g,m)=>{let h=(m?o.getPrePaginationRowModel():o.getRowModel()).rowsById[g];if(!h&&(h=o.getCoreRowModel().rowsById[g],!h))throw new Error;return h},_getDefaultColumnDef:D(()=>[o.options.defaultColumn],g=>{var m;return g=(m=g)!=null?m:{},{header:h=>{const v=h.header.column.columnDef;return v.accessorKey?v.accessorKey:v.accessorFn?v.id:null},cell:h=>{var v,y;return(v=(y=h.renderValue())==null||y.toString==null?void 0:y.toString())!=null?v:null},...o._features.reduce((h,v)=>Object.assign(h,v.getDefaultColumnDef==null?void 0:v.getDefaultColumnDef()),{}),...g}},L(e,"debugColumns")),_getColumnDefs:()=>o.options.columns,getAllColumns:D(()=>[o._getColumnDefs()],g=>{const m=function(h,v,y){return y===void 0&&(y=0),h.map(S=>{const x=Mg(o,S,y,v),C=S;return x.columns=C.columns?m(C.columns,x,y+1):[],x})};return m(g)},L(e,"debugColumns")),getAllFlatColumns:D(()=>[o.getAllColumns()],g=>g.flatMap(m=>m.getFlatColumns()),L(e,"debugColumns")),_getAllFlatColumnsById:D(()=>[o.getAllFlatColumns()],g=>g.reduce((m,h)=>(m[h.id]=h,m),{}),L(e,"debugColumns")),getAllLeafColumns:D(()=>[o.getAllColumns(),o._getOrderColumnsFn()],(g,m)=>{let h=g.flatMap(v=>v.getLeafColumns());return m(h)},L(e,"debugColumns")),getColumn:g=>o._getAllFlatColumnsById()[g]};Object.assign(o,p);for(let g=0;g<o._features.length;g++){const m=o._features[g];m==null||m.createTable==null||m.createTable(o)}return o}function cm(){return e=>D(()=>[e.options.data],t=>{const n={rows:[],flatRows:[],rowsById:{}},r=function(o,i,s){i===void 0&&(i=0);const a=[];for(let c=0;c<o.length;c++){const f=$g(e,e._getRowId(o[c],c,s),o[c],c,i,void 0,s==null?void 0:s.id);if(n.flatRows.push(f),n.rowsById[f.id]=f,a.push(f),e.options.getSubRows){var l;f.originalSubRows=e.options.getSubRows(o[c],c),(l=f.originalSubRows)!=null&&l.length&&(f.subRows=r(f.originalSubRows,i+1,f))}}return a};return n.rows=r(t),n},L(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function dm(e){const t=[],n=r=>{var o;t.push(r),(o=r.subRows)!=null&&o.length&&r.getIsExpanded()&&r.subRows.forEach(n)};return e.rows.forEach(n),{rows:t,flatRows:e.flatRows,rowsById:e.rowsById}}function fm(e){return t=>D(()=>[t.getState().pagination,t.getPrePaginationRowModel(),t.options.paginateExpandedRows?void 0:t.getState().expanded],(n,r)=>{if(!r.rows.length)return r;const{pageSize:o,pageIndex:i}=n;let{rows:s,flatRows:a,rowsById:l}=r;const c=o*i,f=c+o;s=s.slice(c,f);let p;t.options.paginateExpandedRows?p={rows:s,flatRows:a,rowsById:l}:p=dm({rows:s,flatRows:a,rowsById:l}),p.flatRows=[];const g=m=>{p.flatRows.push(m),m.subRows.length&&m.subRows.forEach(g)};return p.rows.forEach(g),p},L(t.options,"debugTable"))}function gm(){return e=>D(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,n)=>{if(!n.rows.length||!(t!=null&&t.length))return n;const r=e.getState().sorting,o=[],i=r.filter(l=>{var c;return(c=e.getColumn(l.id))==null?void 0:c.getCanSort()}),s={};i.forEach(l=>{const c=e.getColumn(l.id);c&&(s[l.id]={sortUndefined:c.columnDef.sortUndefined,invertSorting:c.columnDef.invertSorting,sortingFn:c.getSortingFn()})});const a=l=>{const c=l.map(f=>({...f}));return c.sort((f,p)=>{for(let m=0;m<i.length;m+=1){var g;const h=i[m],v=s[h.id],y=v.sortUndefined,S=(g=h==null?void 0:h.desc)!=null?g:!1;let x=0;if(y){const C=f.getValue(h.id),_=p.getValue(h.id),P=C===void 0,$=_===void 0;if(P||$){if(y==="first")return P?-1:1;if(y==="last")return P?1:-1;x=P&&$?0:P?y:-y}}if(x===0&&(x=v.sortingFn(f,p,h.id)),x!==0)return S&&(x*=-1),v.invertSorting&&(x*=-1),x}return f.index-p.index}),c.forEach(f=>{var p;o.push(f),(p=f.subRows)!=null&&p.length&&(f.subRows=a(f.subRows))}),c};return{rows:a(n.rows),flatRows:o,rowsById:n.rowsById}},L(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function ua(e,t){return e?mm(e)?u.createElement(e,t):e:null}function mm(e){return pm(e)||typeof e=="function"||hm(e)}function pm(e){return typeof e=="function"&&(()=>{const t=Object.getPrototypeOf(e);return t.prototype&&t.prototype.isReactComponent})()}function hm(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function vm(e){const t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=u.useState(()=>({current:um(t)})),[r,o]=u.useState(()=>n.current.initialState);return n.current.setOptions(i=>({...i,...e,state:{...r,...e.state},onStateChange:s=>{o(s),e.onStateChange==null||e.onStateChange(s)}})),n.current}const ci=({row:e,rowStyle:{hoveredRowBg:t},onRowClick:n})=>{const r=n?n(e.original):void 0;return d.jsx(ss,{_hover:{backgroundColor:t},onClick:r,children:e.getVisibleCells().map(o=>{var i,s,a,l,c,f,p,g,m;return d.jsx(_u,{px:1,textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",minWidth:((i=o.column.columnDef.meta)==null?void 0:i.customMinWidth)??void 0,maxWidth:((s=o.column.columnDef.meta)==null?void 0:s.customMaxWidth)??void 0,width:(a=o.column.columnDef.meta)==null?void 0:a.customWidth,textAlign:(l=o.column.columnDef.meta)!=null&&l.isCentered?"center":void 0,fontFamily:(c=o.column.columnDef.meta)!=null&&c.isMonospace?"Inter, SFMono-Regular, Menlo, Monaco, Consolas, monospace":void 0,onClick:(f=o.column.columnDef.meta)!=null&&f.stopPropagation||o.column.id==="actions"&&r?h=>{h.stopPropagation()}:void 0,cursor:!((p=o.column.columnDef.meta)!=null&&p.stopPropagation)&&o.column.id!=="actions"&&r?"pointer":void 0,border:"0.5px solid gray",style:(m=(g=o.column.columnDef.meta)==null?void 0:g.rowContentOptions)==null?void 0:m.style,children:ua(o.column.columnDef.cell,o.getContext())},o.id)})},e.id)},ym=({sortInfo:e,canSort:t})=>t?e?e==="desc"?d.jsx(ht,{ml:1,boxSize:3,as:Vu}):d.jsx(ht,{ml:1,boxSize:3,as:Ou}):d.jsx(ht,{ml:1,boxSize:3,as:Lu}):null,di=({headerGroup:e})=>d.jsx(ss,{p:0,children:e.headers.map(t=>{var n,r,o,i,s,a;return d.jsx(Ru,{color:"gray.400",colSpan:t.colSpan,minWidth:((n=t.column.columnDef.meta)==null?void 0:n.customMinWidth)??void 0,maxWidth:((r=t.column.columnDef.meta)==null?void 0:r.customMaxWidth)??void 0,width:(o=t.column.columnDef.meta)==null?void 0:o.customWidth,fontSize:"sm",onClick:t.column.getCanSort()?t.column.getToggleSortingHandler():void 0,cursor:t.column.getCanSort()?"pointer":void 0,border:"0.5px solid gray",px:1,children:d.jsxs(Mt,{display:"flex",alignItems:"center",children:[t.isPlaceholder?null:d.jsx(qe,{label:(s=(i=t.column.columnDef.meta)==null?void 0:i.headerOptions)==null?void 0:s.tooltip,children:d.jsx(lt,{overflow:"hidden",whiteSpace:"nowrap",alignContent:"center",width:"100%",...(a=t.column.columnDef.meta)==null?void 0:a.headerStyleProps,children:ua(t.column.columnDef.header,t.getContext())})}),d.jsx(ym,{sortInfo:t.column.getIsSorted(),canSort:t.column.getCanSort()})]})},t.id)})}),fi=(e,t)=>{const n=1/t;return Math.round(e*n)/n},Sm=({precision:e})=>{const t=u.useRef(null),[n,r]=u.useState({width:0,height:0});return u.useEffect(()=>{const o=()=>{var s,a;return{width:t&&((s=t.current)==null?void 0:s.offsetWidth)||0,height:t&&((a=t.current)==null?void 0:a.offsetHeight)||0}},i=()=>{const{width:s,height:a}=o();{const l={width:s,height:a};l.width=fi(l.width,e),l.height=fi(l.height,e),(l.width!==n.width||l.height!==n.height)&&r(l)}};return t.current&&i(),window.addEventListener("resize",i),()=>{window.removeEventListener("resize",i)}},[t,n]),u.useMemo(()=>({dimensions:n,ref:t}),[n.height,n.width])},gi=({table:e,isDisabled:t})=>{const{t:n}=pe(),{ref:r,dimensions:o}=Sm({precision:100}),i=o.width!==0&&o.width<=800;return d.jsxs(Mt,{ref:r,justifyContent:"space-between",m:4,alignItems:"center",children:[d.jsxs(Mt,{children:[d.jsx(qe,{label:n("table.first_page"),children:d.jsx(st,{"aria-label":"Go to first page",onClick:()=>e.setPageIndex(0),isDisabled:t||!e.getCanPreviousPage(),icon:d.jsx(Wu,{h:3,w:3}),mr:4})}),d.jsx(qe,{label:n("table.previous_page"),children:d.jsx(st,{"aria-label":"Previous page",onClick:()=>e.previousPage(),isDisabled:t||!e.getCanPreviousPage(),icon:d.jsx(Bu,{h:6,w:6})})})]}),d.jsxs(Mt,{alignItems:"center",children:[i?null:d.jsxs(d.Fragment,{children:[d.jsxs(zt,{flexShrink:0,mr:8,children:[n("table.page")," ",d.jsx(zt,{fontWeight:"bold",as:"span",children:e.getState().pagination.pageIndex+1})," ",n("common.of")," ",d.jsx(zt,{fontWeight:"bold",as:"span",children:e.getPageCount()})]}),d.jsx(zt,{flexShrink:0,children:n("table.go_to_page")})," ",d.jsxs(Ki,{ml:2,mr:8,w:28,min:1,max:e.getPageCount(),onChange:(s,a)=>{const l=a?a-1:0;e.setPageIndex(l)},value:e.getState().pagination.pageIndex+1,children:[d.jsx(Xi,{}),d.jsxs(Yi,{children:[d.jsx(es,{}),d.jsx(Qi,{})]})]})]}),d.jsx(ns,{w:32,value:e.getState().pagination.pageSize,onChange:s=>{e.setPageSize(Number(s.target.value))},children:[10,20,30,40,50].map(s=>d.jsxs("option",{value:s,children:[n("common.show")," ",s]},sn()))})]}),d.jsxs(Mt,{children:[d.jsx(qe,{label:n("table.next_page"),children:d.jsx(st,{"aria-label":"Go to next page",onClick:()=>e.nextPage(),isDisabled:t||!e.getCanNextPage(),icon:d.jsx(Gu,{h:6,w:6})})}),d.jsx(qe,{label:n("table.last_page"),children:d.jsx(st,{"aria-label":"Go to last page",onClick:()=>e.setPageIndex(e.getPageCount()-1),isDisabled:t||!e.getCanNextPage(),icon:d.jsx(Zu,{h:3,w:3}),ml:4})})]})]})},bm=({draggableId:e,index:t,column:n})=>{var l,c,f,p;const{t:r}=pe(),o=Gt("blue.100","blue.600"),i=Gt("gray.50","gray.700");let s=((l=n.header)==null?void 0:l.toString())??"Unrecognized column";(f=(c=n.meta)==null?void 0:c.columnSelectorOptions)!=null&&f.label&&(s=n.meta.columnSelectorOptions.label);const a=()=>{var g,m;return(g=n.meta)!=null&&g.anchored?r("table.drag_locked"):(m=n.meta)!=null&&m.alwaysShow?r("table.drag_always_show"):r("table.drag_explanation")};return d.jsx(Ua,{draggableId:e,index:t,isDragDisabled:(p=n.meta)==null?void 0:p.anchored,children:(g,m)=>{var h,v;return d.jsx(qe,{label:a(),children:d.jsxs(lt,{ref:g.innerRef,...g.draggableProps,...g.dragHandleProps,display:"flex",backgroundColor:m.isDragging?o:i,px:6,py:2,my:2,borderRadius:15,cursor:(h=n.meta)!=null&&h.anchored?"not-allowed":void 0,children:[d.jsx(ht,{as:(v=n.meta)!=null&&v.anchored?zu:Du,boxSize:5,ml:.5,mr:2,my:"auto"}),d.jsx(zt,{my:"auto",children:s})]})})}})},xm=({items:e,columns:t,droppableId:n,isDropDisabled:r})=>{const o=Gt("gray.200","gray.600"),i=Gt("blue.300","blue.500");return d.jsx(Ka,{droppableId:n,direction:"vertical",isCombineEnabled:!1,isDropDisabled:r,children:(s,a)=>d.jsxs(lt,{ref:s.innerRef,backgroundColor:a.isDraggingOver?i:o,padding:2,borderRadius:15,children:[e.map((l,c)=>{const f=t.find(p=>p.id===l);return f?d.jsx(bm,{draggableId:l,index:c,column:f},l):null}),s.placeholder]})})},mi=u.memo(xm),Cm=(e,t,n)=>{const r=Array.from(e),[o]=r.splice(t,1);return o&&r.splice(n,0,o),r},pi=(e,t)=>{const n=[...t];for(const r of e)n.includes(r.id)||n.push(r.id);return n},wm=({controller:e,shownColumns:t,hiddenColumns:n})=>{var m;const{t:r}=pe(),[o,i]=u.useState(pi(t,e.columnOrder)),[s,a]=u.useState(n.map(h=>h.id)),[l,c]=u.useState(),f=u.useCallback(h=>{const v=t.find(({id:y})=>y===h.draggableId)??n.find(({id:y})=>y===h.draggableId);c(v)},[t,n]),p=u.useMemo(()=>{var v;let h=0;for(const[y,S]of t.entries())(v=S.meta)!=null&&v.anchored&&(h=y);return h+1},[t]),g=u.useCallback(h=>{const{source:v,destination:y,draggableId:S}=h;if(y!==null){if(v.droppableId===y.droppableId){const x=Cm(o,v.index,Math.max(y.index,p));y.droppableId==="displayed-columns"?(e.setColumnOrder(x),i(x)):a(x)}else if(v.droppableId==="displayed-columns"){const x=e.hideColumn(S);x&&(a([...x.hiddenColumns]),i([...x.columnOrder]))}else if(v.droppableId==="hidden-columns"){const x=Array.from(o);x.splice(Math.max(y.index,p),0,S);const C=e.unhideColumn(S,x);C&&(a(C.hiddenColumns),i([...C.columnOrder]),a([...C.hiddenColumns]))}c(void 0)}},[t,n,e.hideColumn,e.unhideColumn,p]);return u.useEffect(()=>{console.log("Table settings ID changed, resetting TableDragDrop state:",e.tableSettingsId);const h=pi(t,e.columnOrder);i(h);const v=n.map(y=>y.id);a(v)},[e.tableSettingsId,t,n]),d.jsxs(d.Fragment,{children:[d.jsx(Ue,{size:"md",children:r("table.columns")}),d.jsx(Ya,{onDragStart:f,onDragEnd:g,children:d.jsxs(Mt,{mt:4,children:[d.jsxs(lt,{w:"50%",mr:2,children:[d.jsxs(Ue,{size:"sm",mb:4,children:["Visible (",o.length,")"]}),d.jsx(mi,{droppableId:"displayed-columns",items:o,columns:t})]}),d.jsxs(lt,{ml:2,w:"50%",children:[d.jsxs(Ue,{size:"sm",mb:4,children:["Hidden (",n.length,")"]}),d.jsx(mi,{droppableId:"hidden-columns",items:s,columns:n,isDropDisabled:(m=l==null?void 0:l.meta)==null?void 0:m.alwaysShow})]})]})})]})},_m=({controller:e,columns:t})=>{const{t:n}=pe(),r=ka();return d.jsxs(d.Fragment,{children:[d.jsx(qe,{label:n("table.preferences"),children:d.jsx(st,{"aria-label":n("table.preferences"),icon:d.jsx(Nu,{weight:"bold"}),onClick:r.onOpen})}),d.jsx(ja,{title:n("table.preferences"),topRightButtons:d.jsx(qe,{label:n("table.reset"),children:d.jsx(st,{"aria-label":n("table.reset"),icon:d.jsx(Hu,{size:20}),onClick:e.resetPreferences})}),options:{modalSize:"md",maxWidth:{sm:"600px",md:"600px",lg:"600px",xl:"600px"}},...r,children:d.jsx(lt,{w:"100%",children:d.jsx(wm,{shownColumns:t.filter(o=>e.columnVisibility[o.id]!==!1),hiddenColumns:t.filter(o=>e.columnVisibility[o.id]===!1),controller:e})})})]})},hi=u.memo(_m),vi=({isLoading:e,children:t})=>{const{colorMode:n}=Ei(),r={top:"0px",right:"0px",position:"absolute",width:"100%",height:"100%",backgroundColor:n==="light"?"var(--chakra-colors-gray-200)":"var(--chakra-colors-gray-900)",zIndex:1100,opacity:"0.4",filter:"alpha(opacity=40)",textAlign:"center",verticalAlign:"middle",borderRadius:"5px"};return d.jsxs(d.Fragment,{children:[e&&d.jsx("div",{style:r,children:d.jsx(Mi,{position:"absolute",top:"45%",size:"xl"})}),t]})},Rm=({innerTableKey:e,controller:t,columns:n,header:r,data:o=[],options:i={},isLoading:s=!1})=>{const{t:a}=pe(),l=Gt("gray.700","white"),c=Gt("gray.100","gray.600"),f=Ae.useMemo(()=>i.isFullScreen?{base:"calc(100vh - 360px)",md:"calc(100vh - 288px)"}:i.minimumHeight??"300px",[i.isFullScreen,i.minimumHeight]),p=Ae.useMemo(()=>i.onRowClick,[i.onRowClick]),g=Ae.useMemo(()=>({pageIndex:t.pageInfo.pageIndex,pageSize:t.pageInfo.pageSize}),[t.pageInfo.pageIndex,t.pageInfo.pageSize]),m=Ae.useMemo(()=>i.isManual&&i.count?Math.ceil(i.count/g.pageSize):Math.ceil(((o==null?void 0:o.length)??0)/g.pageSize),[i.count,i.isManual,o==null?void 0:o.length,g.pageSize]),h=Ae.useMemo(()=>({pageCount:m>0?m:1,initialState:{sorting:t.sortBy,pagination:g},manualPagination:i.isManual,manualSorting:i.isManual,autoResetPageIndex:!1}),[i.isManual,t.sortBy,m]),v=Ae.useMemo(()=>{const S=t.columnOrder.filter(x=>n.find(C=>C.id===x));if(S.length!==n.length)for(const x of n)S.includes(x.id)||S.push(x.id);return n.slice().sort((x,C)=>S.indexOf(x.id)-S.indexOf(C.id))},[n,t.columnOrder]),y=vm({getCoreRowModel:cm(),getPaginationRowModel:fm(),getSortedRowModel:gm(),data:o,columns:v,state:{sorting:t.sortBy,columnVisibility:t.columnVisibility,pagination:g},onSortingChange:t.setSortBy,onPaginationChange:t.onPaginationChange,...h});return Ae.useEffect(()=>{i.isManual&&!s&&o&&g.pageIndex>0&&i.count!==void 0&&Math.ceil(i.count/g.pageSize)-1<g.pageIndex&&t.onPaginationChange({pageIndex:0,pageSize:g.pageSize})},[i.count,s,g,o]),s&&!i.showAsCard&&o.length===0?d.jsx(en,{children:d.jsx(Mi,{size:"xl"})}):i.showAsCard?d.jsxs(Pi,{children:[d.jsxs($i,{children:[typeof r.title=="string"?d.jsx(Ue,{size:"md",my:"auto",mr:2,children:r.title}):r.title,r.leftContent,d.jsx(Rr,{}),d.jsxs(So,{spacing:2,children:[r.otherButtons,r.addButton,i.hideTablePreferences?null:d.jsx(hi,{controller:t,columns:n}),i.refetch?d.jsx(To,{onClick:i.refetch,isCompact:!0,isFetching:s}):null]})]}),d.jsxs(Fi,{display:"flex",flexDirection:"column",children:[d.jsx(vi,{isLoading:s,children:d.jsxs(jo,{minH:f,children:[d.jsxs(Fr,{size:"small",variant:"simple",textColor:l,w:"100%",fontSize:"14px",children:[d.jsx(Io,{children:y.getHeaderGroups().map(S=>d.jsx(di,{headerGroup:S},S.id))}),d.jsx(Ao,{children:y.getRowModel().rows.map(S=>d.jsx(ci,{row:S,onRowClick:p,rowStyle:{hoveredRowBg:c}},S.id))})]},e),(o==null?void 0:o.length)===0?d.jsx(en,{mt:8,children:d.jsx(Ue,{size:"md",children:r.objectListed?a("common.no_obj_found",{obj:r.objectListed}):a("common.empty_list")})}):null]})}),i.isHidingControls?null:d.jsx(gi,{table:y,isDisabled:s})]})]}):d.jsxs(lt,{w:"100%",children:[d.jsxs(Mt,{mb:2,hidden:i.hideTableTitleRow,children:[d.jsx(Ue,{size:"md",my:"auto",mr:2,children:r.title}),r.leftContent,d.jsx(Rr,{}),d.jsxs(So,{spacing:2,children:[r.otherButtons,r.addButton,i.hideTablePreferences?null:d.jsx(hi,{controller:t,columns:n}),i.refetch?d.jsx(To,{onClick:i.refetch,isCompact:!0,isFetching:s}):null]})]}),d.jsx(vi,{isLoading:s,children:d.jsxs(jo,{minH:f,children:[d.jsxs(Fr,{size:"small",variant:"simple",textColor:l,w:"100%",fontSize:"14px",children:[d.jsx(Io,{children:y.getHeaderGroups().map(S=>d.jsx(di,{headerGroup:S},S.id))}),d.jsx(Ao,{children:y.getRowModel().rows.map(S=>d.jsx(ci,{row:S,onRowClick:p,rowStyle:{hoveredRowBg:c}},S.id))})]},e),(o==null?void 0:o.length)===0?d.jsx(en,{mt:8,children:d.jsx(Ue,{size:"md",children:r.objectListed?a("common.no_obj_found",{obj:r.objectListed}):a("common.empty_list")})}):null]})}),i.isHidingControls?null:d.jsx(gi,{table:y,isDisabled:s})]})},Em=(e,t)=>d.jsx(rn,{size:"sm",colorScheme:"blue",onClick:()=>t(e),w:"100%",children:e.length}),yi=(e,t)=>e!==void 0?`${e}${t?`${t}`:""}`:d.jsx(en,{children:"-"}),Mm=({channelInfo:{channel:e,devices:t}})=>{const{t:n}=pe(),{colorMode:r}=Ei(),[o,i]=Ae.useState(),s=Xa({tableSettingsId:"wifiscan.devices.table",defaultOrder:["ssid","signal","actions"],defaultSortBy:[{desc:!1,id:"ssid"}]}),a=Ae.useMemo(()=>[{id:"ssid",header:"SSID",footer:"",accessorKey:"ssid",meta:{anchored:!0,alwaysShow:!0}},{id:"signal",header:"Signal",footer:"",accessorKey:"signal",cell:l=>`${l.cell.row.original.signal} db`,meta:{anchored:!0,customWidth:"80px",alwaysShow:!0,rowContentOptions:{style:{textAlign:"right"}}}},{id:"station",header:"UEs",accessorKey:"sta_count",cell:l=>yi(l.cell.row.original.sta_count),meta:{anchored:!0,customWidth:"40px",alwaysShow:!0,rowContentOptions:{style:{textAlign:"right"}}}},{id:"utilization",header:"Ch. Util.",accessorKey:"ch_util",cell:l=>yi(l.cell.row.original.ch_util,"%"),meta:{anchored:!0,customWidth:"60px",alwaysShow:!0,headerOptions:{tooltip:"Channel Utilization (%)"},rowContentOptions:{style:{textAlign:"right"}}}},{id:"ies",header:"Ies",footer:"",accessorKey:"actions",cell:l=>Em(l.cell.row.original.ies??[],i),meta:{customWidth:"50px",isCentered:!0,alwaysShow:!0}}],[n]);return d.jsxs(Pi,{children:[d.jsxs($i,{display:"flex",children:[d.jsxs(Ue,{size:"md",my:"auto",children:[n("commands.channel")," #",e," (",t.length," ",t.length===1?n("devices.one"):n("devices.title"),")"]}),d.jsx(Rr,{}),o&&d.jsx(st,{size:"sm",my:"auto","aria-label":n("common.back"),colorScheme:"blue",icon:d.jsx(Tu,{size:20}),onClick:()=>i(void 0)})]}),d.jsx(Fi,{children:o?d.jsx(lt,{w:"800px",children:o.map(({content:l,name:c,type:f})=>d.jsxs(lt,{my:2,children:[d.jsxs(Ue,{size:"sm",mb:2,textDecor:"underline",children:[c," (",f,")"]}),d.jsx(wg,{rootName:!1,displayDataTypes:!1,enableClipboard:!0,theme:r==="light"?void 0:"dark",value:l,style:{background:"unset",display:"unset"}})]},sn()))}):d.jsx(Rm,{controller:s,header:{title:"",objectListed:n("devices.title")},columns:a,data:t,options:{count:t.length,onRowClick:l=>()=>i(l.ies??[]),hideTablePreferences:!0,isHidingControls:!0,minimumHeight:"0px",hideTableTitleRow:!0}})})]})},f1=({results:e,setCsvData:t})=>{const{t:n}=pe(),r=u.useMemo(()=>{try{const o={},i=[];for(const s of e.results.status.scan)if(!o[s.channel]){const a={channel:s.channel,devices:[]};for(const l of e.results.status.scan)if(l.channel===s.channel){let c="";const f=Aa(l.signal);l.ssid&&l.ssid.length>0?c=l.ssid:c=l.meshid&&l.meshid.length>0?l.meshid:"N/A",a.devices.push({...l,ssid:c,signal:f}),i.push({...l,ssid:c,signal:f,ies:JSON.stringify(l.ies)})}o[s.channel]=a}return{scanList:Object.keys(o).map(s=>o[s]),listCsv:i}}catch{return}},[e]);return u.useEffect(()=>{r&&t(r.listCsv)},[r]),d.jsxs(d.Fragment,{children:[e.errorCode===1&&d.jsx(Ue,{size:"md",children:d.jsx(Ia,{colorScheme:"red",children:n("commands.wifiscan_error_1")})}),d.jsxs(Ue,{size:"md",mb:2,children:[n("commands.execution_time"),": ",Math.floor(e.executionTime/1e3),"s"]}),d.jsx(os,{spacing:4,align:"stretch",children:r==null?void 0:r.scanList.map(o=>d.jsx(Mm,{channelInfo:o},sn()))})]})},Pm=({onClick:e,isDisabled:t,isLoading:n,isCompact:r=!0,color:o,label:i,icon:s,...a})=>{const l=Ui();return!r&&l!=="base"&&l!=="sm"?d.jsx(rn,{colorScheme:o,type:"button",onClick:e,rightIcon:s,isLoading:n,isDisabled:t,...a,children:i}):d.jsx(qe,{label:i,children:d.jsx(st,{"aria-label":i,colorScheme:o,type:"button",onClick:e,icon:s,isLoading:n,isDisabled:t,...a})})},g1=Ae.memo(Pm),m1=()=>{const[e,t]=u.useState({submitForm:()=>{},isSubmitting:!1,isValid:!0,dirty:!1}),n=u.useCallback(o=>{o&&(e.submitForm!==o.submitForm||e.isSubmitting!==o.isSubmitting||e.isValid!==o.isValid||e.dirty!==o.dirty)&&t(o)},[e]);return u.useMemo(()=>({form:e,formRef:n}),[e])},$m=(e,t)=>async()=>ae.get(`commands?serialNumber=${t}&newest=true&limit=${e}`).then(n=>n.data),p1=({serialNumber:e,limit:t,onError:n})=>Re(["commands",e,{limit:t}],$m(t,e),{keepPreviousData:!0,enabled:e!==void 0&&e!=="",staleTime:3e4,onError:n}),Fm=(e,t,n,r,o)=>ae.get(`commands?serialNumber=${e}&startDate=${t}&endDate=${n}&limit=${r}&offset=${o}`).then(i=>i.data),km=(e,t,n)=>async()=>{let r=0;const o=100;let i=[],s;do s=await Fm(e,t,n,o,r),i=i.concat(s.commands),r+=o;while(s.commands.length===o);return{commands:i}},h1=({serialNumber:e,start:t,end:n,onError:r})=>Re(["commands",e,{start:t,end:n}],km(e,t,n),{enabled:e!==void 0&&e!==""&&t!==void 0&&n!==void 0,staleTime:1e3*60,onError:r}),jm=async e=>ae.delete(`command/${e}`),v1=()=>{const e=bt();return ft(jm,{onSuccess:()=>{e.invalidateQueries(["commands"])}})},y1=({serialNumber:e,commandId:t})=>Re(["commands",e,t],()=>ae.get(`command/${t}?serialNumber=${e}`).then(n=>n.data),{enabled:e!==void 0&&e!==""&&t!==void 0&&t!==""}),Am=async e=>ae.post(`device/${e}/eventqueue`,{types:["dhcp-snooping","wifi-frames"],serialNumber:e}).then(t=>t.data),S1=()=>{const e=bt();return ft(Am,{onSuccess:()=>{e.invalidateQueries(["commands"])}})},Im=e=>async t=>ae.post(`device/${e}/configure`,{when:0,UUID:1,ForceInitDeviceConfig:!0,serialNumber:e,configuration:t}).then(n=>n.data),b1=({serialNumber:e})=>{const t=bt();return ft(Im(e),{onSuccess:()=>{t.invalidateQueries(["commands",e]),t.invalidateQueries(["device",e]),t.invalidateQueries(["devices"])}})},Vm=e=>ae.post(`device/${e.serialNumber}/script`,e,{timeout:e.timeout?e.timeout*1e3+10:5*60*1e3}).then(t=>t.data),x1=({serialNumber:e})=>{const t=bt();return ft(Vm,{onSuccess:()=>{t.invalidateQueries(["commands",e])},onError:()=>{t.invalidateQueries(["commands",e])}})},Tm=(e,t)=>ae.get(`file/${t}?serialNumber=${e}`,{responseType:"arraybuffer"}),C1=({serialNumber:e,commandId:t})=>{const{t:n}=pe(),r=Wt();return Re(["download-script",e,t],()=>Tm(e,t),{enabled:!1,onSuccess:o=>{var c;const i=new Blob([o.data],{type:"application/octet-stream"}),s=document.createElement("a");s.href=window.URL.createObjectURL(i);const a=o.headers["content-disposition"]??o.headers["content-disposition"],l=((c=a==null?void 0:a.split("filename=")[1])==null?void 0:c.split(",")[0])??`Script_${t}.tar.gz`;s.download=l,s.click()},onError:o=>{var i;if(ki.isAxiosError(o)){const s=(i=o.response)==null?void 0:i.data;let a="";if(s instanceof ArrayBuffer){const l=new TextDecoder("utf-8");a=JSON.parse(l.decode(s)).ErrorDescription}r({id:`script-download-error-${e}`,title:n("common.error"),description:a,status:"error",duration:5e3,isClosable:!0,position:"top-right"})}}})},Om={refresh:()=>{},onClose:()=>{},queryToInvalidate:null},Dm=({objName:e,operationType:t,refresh:n,onClose:r,queryToInvalidate:o})=>{const{t:i}=pe(),s=Wt(),a=bt(),l=()=>t==="update"?i("crud.success_update_obj",{obj:e}):t==="delete"?i("crud.success_delete_obj",{obj:e}):t==="blink"?i("commands.blink_success",{obj:e}):t==="reboot"?i("commands.reboot_success",{obj:e}):i("crud.success_create_obj",{obj:e}),c=m=>{var h,v,y,S,x,C,_,P,$,k;return t==="update"?i("crud.error_update_obj",{obj:e,e:(v=(h=m==null?void 0:m.response)==null?void 0:h.data)==null?void 0:v.ErrorDescription}):(t==="delete"&&i("crud.error_delete_obj",{obj:e,e:(S=(y=m==null?void 0:m.response)==null?void 0:y.data)==null?void 0:S.ErrorDescription}),t==="blink"?i("commands.blink_error",{obj:e,e:(C=(x=m==null?void 0:m.response)==null?void 0:x.data)==null?void 0:C.ErrorDescription}):t==="reboot"?i("commands.reboot_error",{obj:e,e:(P=(_=m==null?void 0:m.response)==null?void 0:_.data)==null?void 0:P.ErrorDescription}):i("crud.error_create_obj",{obj:e,e:(k=($=m==null?void 0:m.response)==null?void 0:$.data)==null?void 0:k.ErrorDescription}))},f=u.useCallback(({setSubmitting:m,resetForm:h}={setSubmitting:null,resetForm:null})=>{n&&n(),m&&m(!1),h&&h(),s({id:`${e}-${t}-success-${sn()}`,title:i("common.success"),description:l(),status:"success",duration:5e3,isClosable:!0,position:"top-right"}),r&&r(),o&&a.invalidateQueries(o)},[o]),p=u.useCallback((m,{setSubmitting:h}={setSubmitting:null})=>{s({id:sn(),title:i("common.error"),description:c(m),status:"error",duration:5e3,isClosable:!0,position:"top-right"}),h&&h(!1)},[]);return u.useMemo(()=>({onSuccess:f,onError:p}),[])};Dm.defaultProps=Om;const Lm=async e=>ae.post(`device/${e.serialNumber}/trace`,{...e,when:e.when??0}),Hm=({serialNumber:e,alertOnCompletion:t})=>{const n=bt(),{t:r}=pe();return Wt(),ft(Lm,{onSuccess:()=>{n.invalidateQueries(["commands",e]),t&&on.success(`${r("common.success")}: ${r("controller.trace.success",{serialNumber:e})}`,5)}})},zm=(e,t)=>ae.get(`file/${t}?serialNumber=${e}`,{responseType:"arraybuffer"}),Nm=({serialNumber:e,commandId:t})=>{const{t:n}=pe();return Wt(),Re(["download-trace",e,t],()=>zm(e,t),{enabled:!1,onSuccess:r=>{var l;const o=new Blob([r.data],{type:"application/octet-stream"}),i=document.createElement("a");i.href=window.URL.createObjectURL(o);const s=r.headers["content-disposition"]??r.headers["content-disposition"],a=((l=s==null?void 0:s.split("filename=")[1])==null?void 0:l.split(",")[0])??`Trace_${t}.pcap`;i.download=a,i.click()},onError:r=>{var o;if(ki.isAxiosError(r)){const i=(o=r.response)==null?void 0:o.data;let s="";if(i instanceof ArrayBuffer){const a=new TextDecoder("utf-8");s=JSON.parse(a.decode(i)).ErrorDescription}on.error(`${n("common.error")}: ${s}`,5)}}})},w1=({serialNumber:e,modalProps:t})=>{var g;const{t:n}=pe(),[r,o]=u.useState({type:"duration",network:"up",waitForResponse:!0,duration:"20",packets:"100"}),i=Hm({serialNumber:e,alertOnCompletion:!r.waitForResponse}),s=Nm({serialNumber:e,commandId:((g=i.data)==null?void 0:g.data.UUID)??""}),a=(m,h)=>{o({...r,[m]:h})},l=(m,h)=>{o({...r,[m]:h})},c=()=>{i.mutate({serialNumber:e,type:r.type,network:r.network,waitForResponse:r.waitForResponse,duration:r.type==="duration"?parseInt(r.duration,10):void 0,packets:r.type==="packets"?parseInt(r.packets,10):void 0}),r.waitForResponse||t.onClose()},f=()=>{s.refetch()},p=d.jsxs("div",{className:"TraceModalCreate",children:[i.isLoading&&d.jsx("div",{style:{textAlign:"center",padding:"100px 0"},children:d.jsx(Zr,{size:"large"})}),!i.isLoading&&!i.data&&!i.error&&d.jsxs(d.Fragment,{children:[d.jsx("div",{children:d.jsx(ji,{message:n("controller.devices.trace_description"),type:"info",showIcon:!0,closable:!0,className:"custom-trace-alert",style:{marginTop:14,marginBottom:32,whiteSpace:"normal"}})}),d.jsxs(Va,{children:[d.jsx(wn,{xs:24,children:d.jsx(Et.Item,{label:n("common.type"),children:d.jsxs(we,{value:r.type,onChange:m=>a("type",m),style:{width:"100%"},children:[d.jsx(we.Option,{value:"duration",children:n("controller.trace.duration")}),d.jsx(we.Option,{value:"packets",children:n("controller.trace.packets")})]})})}),d.jsx(wn,{xs:24,children:r.type==="duration"?d.jsx(Et.Item,{label:n("controller.trace.duration"),children:d.jsxs(we,{value:r.duration,onChange:m=>a("duration",m),style:{width:"100%"},children:[d.jsx(we.Option,{value:"20",children:"20s"}),d.jsx(we.Option,{value:"40",children:"40s"}),d.jsx(we.Option,{value:"60",children:"60s"}),d.jsx(we.Option,{value:"120",children:"120s"})]})}):d.jsx(Et.Item,{label:n("controller.trace.packets"),children:d.jsxs(we,{value:r.packets,onChange:m=>a("packets",m),style:{width:"100%"},children:[d.jsx(we.Option,{value:"100",children:"100"}),d.jsx(we.Option,{value:"250",children:"250"}),d.jsx(we.Option,{value:"500",children:"500"}),d.jsx(we.Option,{value:"1000",children:"1000"})]})})}),d.jsx(wn,{xs:24,children:d.jsx(Et.Item,{label:n("controller.trace.network"),children:d.jsx(we,{value:r.network,onChange:m=>a("network",m),style:{width:"100%"},children:d.jsx(we.Option,{value:"up",children:n("controller.trace.up")})})})}),d.jsx(wn,{xs:24,children:d.jsx(Et.Item,{label:n("controller.trace.wait"),children:d.jsx(Ai,{size:"large",checked:r.waitForResponse,onChange:m=>l("waitForResponse",m)})})})]})]}),d.jsx("div",{style:{marginTop:16,marginBottom:16},children:i.data?d.jsxs(d.Fragment,{children:[d.jsx(kn,{type:"primary",onClick:f,loading:s.isFetching,style:{marginRight:8},children:n("controller.trace.download")}),d.jsx(kn,{type:"primary",onClick:i.reset,children:n("common.go_back")})]}):d.jsx(kn,{type:"primary",onClick:c,loading:i.isLoading,style:{width:"100px"},children:n("common.start")})})]});return d.jsx(Ii,{title:n("controller.devices.trace"),childItems:p,isModalOpen:t.isOpen,onCancel:t.onClose,footer:null,modalClass:"ampcon-middle-modal"})},_1=({modalProps:{isOpen:e,onClose:t},serialNumber:n})=>{const{t:r}=pe(),[o,i]=u.useState(!1),{mutateAsync:s,isLoading:a}=ec({serialNumber:n,onClose:t}),l=()=>{s({keepRedirector:o})};u.useEffect(()=>{e&&i(!1)},[e]);const c=()=>a?d.jsx("div",{style:{textAlign:"center",padding:"30px 0"},children:d.jsx(Zr,{size:"large"})}):d.jsxs(d.Fragment,{children:[d.jsx(ji,{className:"custom-trace-alert",message:r("commands.factory_reset_warning"),type:"info",showIcon:!0,closable:!0,style:{marginTop:14,marginBottom:24}}),d.jsx(Et,{initialValues:{keepRedirector:!1},children:d.jsx(Et.Item,{name:"keepRedirector",label:r("commands.keep_redirector"),valuePropName:"checked",children:d.jsx(Ai,{checked:o,onChange:i})})}),d.jsx("div",{style:{margin:"20px 0 10px"},children:d.jsx(kn,{size:"large",type:"primary",onClick:l,loading:a,children:r("commands.confirm_reset",{serialNumber:n})})})]});return d.jsx(Ii,{title:r("commands.factory_reset"),childItems:c(),isModalOpen:e,onCancel:t,footer:null,modalClass:"ampcon-middle-modal"})},R1=({enabled:e,serialNumber:t})=>Re(["get-inventory-tag",t],()=>Ta.get(`inventory/${t}?withExtendedInfo=true`).then(({data:n})=>n),{enabled:e,onError:n=>{console.error("get sn: "+t+" inventory info fail!")}}),E1=()=>{var h;const e=Oa(),[t,n]=u.useState(""),[r,o]=u.useState(!1),[i,s]=u.useState([]),[a,l]=u.useState(!1),{data:c}=Da({pageInfo:{index:0,limit:1e3},enabled:!0,platform:"ALL"}),f=((h=c==null?void 0:c.devicesWithStatus)==null?void 0:h.map(v=>v.serialNumber))||[],p=Ja(v=>{if(v.length<2){s([]),l(!1);return}o(!0);const y=f.filter(S=>S.toLowerCase().includes(v.toLowerCase()));s(y),l(y.length>0),o(!1)},300),g=v=>{const y=v.target.value;n(y),p(y)},m=v=>{n(""),s([]),l(!1),e(`/wireless/devices/${v}`)};return d.jsx(La,{title:`Search serial numbers and radius clients. For radius clients you can either use the client's username (rad:<EMAIL>)
       or use the client's station ID (rad:11:22:33:44:55:66)`,placement:"left",children:d.jsx(Ha,{open:a,dropdownRender:()=>r?d.jsx("div",{style:{background:"#fff",padding:10},children:d.jsx(Zr,{style:{padding:10}})}):d.jsx("div",{style:{background:"#fff"},children:d.jsx(bo,{className:"input-search-hover",dataSource:i,renderItem:v=>d.jsx(bo.Item,{style:{cursor:"pointer",padding:"4px 12px"},onClick:()=>m(v),children:v}),style:{maxHeight:200,overflowY:"auto",minWidth:250}})}),trigger:["click"],children:d.jsx(za,{style:{justifyContent:"flex-end",width:"250px"},value:t,onChange:g,placeholder:"Search Serial Numbers",prefix:d.jsx(Na,{style:{color:"rgba(0,0,0,.25)"}}),allowClear:!0})})})};export{s1 as $,qt as A,Hl as B,en as C,Wl as D,Fl as E,Wi as F,Ut as G,l1 as H,u1 as I,Gi as J,Zi as K,ac as L,Xi as M,Ki as N,Yi as O,es as P,Qi as Q,g1 as R,yu as S,d1 as T,zt as U,is as V,f1 as W,C1 as X,x1 as Y,e1 as Z,vi as _,o1 as a,y1 as a0,wg as a1,h1 as a2,p1 as a3,v1 as a4,r1 as a5,t1 as a6,i1 as b,a1 as c,m1 as d,Tu as e,c1 as f,b1 as g,n1 as h,Dm as i,S1 as j,Ym as k,To as l,Cu as m,bu as n,xu as o,Su as p,ns as q,R1 as r,Xm as s,Jm as t,ql as u,Qm as v,E1 as w,_1 as x,w1 as y,Zl as z};
