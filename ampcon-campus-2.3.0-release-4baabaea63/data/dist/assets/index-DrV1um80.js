import{r,j as o,B as j,X as T,al as $,am as A,H as d,V as E,Q as N,M as V,D as B,bC as _,bD as W,aE as U,a4 as H,ai as Q,F as X,az as q,aF as R}from"./index-CCDcquaz.js";import{u as K,R as G,a as L,e as O,h as J,F as Y,W as Z,T as ee,V as te}from"./tabs-B30QcNxg.js";/* empty css             */import{u as ae}from"./SiteContext-D-2Xr-_P.js";import{u as ie,a as ne}from"./useUrlSync-DjGOfs83.js";import"./index-B9-ToZA1.js";const oe=({siteId:h=0})=>{if(window.location.hash){const a=window.location.hash.replace("#","");/^\d+$/.test(a)&&(h=parseInt(a,10))}const[k,z]=r.useState([]),[b,l]=r.useState(!1),[w,c]=r.useState(!1),[g,f]=r.useState(null),[s,x]=r.useState({current:1,pageSize:10,total:0}),[y,P]=r.useState({}),m=(a=1,e=10,t=y)=>{l(!0);const n=t.field?[{field:t.field,order:t.order}]:[];L(1,h,a,e,[],n).then(i=>{if((i==null?void 0:i.status)!==200){d.error(i==null?void 0:i.info);return}z((i==null?void 0:i.info)||[]),x({current:a,pageSize:e,total:(i==null?void 0:i.total)||0})}).catch(()=>d.error("Failed to fetch list")).finally(()=>l(!1))};r.useEffect(()=>{m()},[h]);const D=a=>{N("Are you sure you want to delete?",()=>{J({id:a.key}).then(e=>{if((e==null?void 0:e.status)!==200){d.error(e==null?void 0:e.info);return}d.success("Deleted successfully"),m(s.current,s.pageSize)}).catch(()=>d.error("Delete failed"))})},C=a=>{O(a.key).then(e=>{if((e==null?void 0:e.status)!==200){d.error(e==null?void 0:e.info);return}let t=(e==null?void 0:e.info)||null;if(t&&typeof t.parameter=="string")try{t.parameter=JSON.parse(t.parameter)}catch{t.parameter={}}f(t),c(!0)}).catch(()=>d.error("Failed to fetch detail"))},F=()=>{f(null),c(!0)},I=()=>{c(!1),f(null)},M=(k||[]).map(a=>{let e={};if(typeof a.parameter=="string")try{e=JSON.parse(a.parameter)}catch{e={}}else typeof a.parameter=="object"&&a.parameter!==null&&(e=a.parameter);return{key:a.id,name:a.name,type:e.type||"",authHost:e.auth_server_host||"",port:e.port||"",modified_time:a.modified_time?K(a.modified_time):"",description:a.description||"",originResource:a}}),v=[{title:"Name",dataIndex:"name",key:"name",sorter:!0,width:"10%"},{title:"Type",dataIndex:"type",key:"type",sorter:!0,width:"10%"},{title:"Auth server host",dataIndex:"authHost",key:"authHost",sorter:!0,width:"15%"},{title:"Port",dataIndex:"port",key:"port",sorter:!0,width:"10%"},{title:"Modified",dataIndex:"modified_time",key:"modified_time",sorter:!0},{title:"Description",dataIndex:"description",key:"description",sorter:!0},{title:"Operation",key:"operation",render:(a,e)=>o.jsxs(E,{size:24,children:[o.jsx(j,{style:{padding:0},type:"link",onClick:()=>C(e),children:"Edit"}),o.jsx(j,{style:{padding:0},type:"link",onClick:()=>D(e),children:"Delete"})]})}];return o.jsxs("div",{children:[o.jsx("div",{style:{marginBottom:16},children:o.jsxs(j,{type:"primary",onClick:F,children:[o.jsx(T,{component:$}),"Create New SSID Radius Profile"]})}),o.jsx(A,{columns:v,dataSource:M,loading:b,pagination:{current:s.current,pageSize:s.pageSize,total:s.total,showTotal:(a,e)=>`${e[0]}-${e[1]} of ${a} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],onChange:(a,e)=>m(a,e)},rowKey:"key",bordered:!0,onChange:(a,e,t)=>{let n="",i="";if(!Array.isArray(t))if(t.order==="ascend"?n="asc":t.order==="descend"&&(n="desc"),typeof t.field=="string")switch(t.field){case"type":i="parameter.type";break;case"authHost":i="parameter.auth_server_host";break;case"port":i="parameter.port";break;default:i=t.field}else i="";P({field:i,order:n}),m(a.current,a.pageSize,{field:i,order:n})}}),o.jsx(G,{open:w,onClose:I,refresh:()=>m(s.current,s.pageSize),resource:g,isDisabled:!1,siteId:h},(g==null?void 0:g.id)||String(w))]})},re=()=>{const[h,k]=r.useState([]),[z,b]=r.useState(!1),[l,w]=r.useState({current:1,pageSize:10,total:0}),[c,g]=r.useState({}),[f,s]=r.useState(null),[x,y]=r.useState(!1);let P=null;const m=window.location.hash.replace("#","");m&&/^\d+$/.test(m)&&(P=parseInt(m,10));const D=P||0,C=async(t=l.current,n=l.pageSize,i=c)=>{b(!0);try{const p=i.field?[{field:i.field,order:i.order}]:[],u=await L(2,D,t,n,[],p);u.status===200?(k((u.info||[]).map(S=>({...S,parameter:typeof S.parameter=="string"?JSON.parse(S.parameter):S.parameter||{},modified_time:S.modified_time?K(S.modified_time):""}))),w(S=>({...S,current:t,pageSize:n,total:(u==null?void 0:u.total)||0}))):d.error((u==null?void 0:u.info)||"Failed to fetch profile list")}catch{d.error("Failed to fetch profile list")}finally{b(!1)}},F=(t,n,i)=>{w({...l,current:t.current,pageSize:t.pageSize});let p="",u="";!Array.isArray(i)&&(i!=null&&i.order)&&(p=i.order==="ascend"?"asc":"desc",u=i.field||""),g({field:u,order:p}),C(t.current,t.pageSize,{field:u,order:p})};r.useEffect(()=>{D!=null&&C(l.current,l.pageSize,c)},[D,l.current,l.pageSize,c]);const I=()=>{s(null),y(!0)},M=t=>{s(t),y(!0)},v=t=>{N("Are you sure you want to delete?",async()=>{try{const n=await J({id:t.id});if((n==null?void 0:n.status)!==200){d.error((n==null?void 0:n.info)||"Delete failed");return}d.success("Successfully Deleted");const i=l.total-1,p=Math.ceil(i/l.pageSize),u=p===0?1:Math.min(l.current,p);w(S=>({...S,current:u,total:i})),C(u,l.pageSize)}catch{d.error("Delete failed")}})},a=t=>{y(!1),t&&w(n=>{const i=n.total+1;Math.ceil(i/n.pageSize);const u=n.total!==0&&n.total%n.pageSize===0?n.current+1:n.current;return C(u,n.pageSize,c),{...n,current:u,total:i}})},e=[{title:"Name",dataIndex:"name",key:"name",sorter:!0,width:"25%"},{title:"Modified",dataIndex:"modified_time",key:"modified",sorter:!0,width:"25%"},{title:"Description",dataIndex:"description",key:"description",sorter:!0,width:"30%"},{title:"Operation",key:"operate",width:"20%",render:(t,n)=>o.jsxs(E,{size:16,children:[o.jsx(j,{type:"link",onClick:()=>M(n),style:{marginLeft:-10,marginRight:-20},children:"Edit"}),o.jsx(j,{type:"link",onClick:()=>v(n),children:"Delete"})]})}];return o.jsxs("div",{style:{flex:1},children:[o.jsx("div",{style:{marginBottom:16},children:o.jsxs(j,{type:"primary",onClick:I,children:[o.jsx(T,{component:$}),"Create New MPSK Profile"]})}),o.jsx(A,{columns:e,dataSource:h,rowKey:"id",loading:z,onChange:F,pagination:{current:l.current,pageSize:l.pageSize,total:l.total,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],showTotal:t=>`Total ${t} items`},scroll:{x:1e3},bordered:!0}),o.jsx(V,{title:f?"Edit MPSK Profile":"Create MPSK Profile",visible:x,onCancel:()=>a(!1),footer:null,width:1360,destroyOnClose:!0,children:o.jsxs("div",{onKeyDown:t=>{t.key==="Enter"&&t.preventDefault()},children:[o.jsx(B,{style:{margin:"0px 0px 16px -24px",width:"calc(100% + 48px)"}}),o.jsx(Y,{editingProfile:f,onClose:a,siteId:D})]})})]})},le=({siteId:h=0})=>{if(window.location.hash){const a=window.location.hash.replace("#","");/^\d+$/.test(a)&&(h=parseInt(a,10))}const[k,z]=r.useState([]),[b,l]=r.useState(!1),[w,c]=r.useState(!1),[g,f]=r.useState(null),[s,x]=r.useState({current:1,pageSize:10,total:0}),[y,P]=r.useState({}),m=(a=1,e=10,t=y)=>{l(!0);const n=t.field?[{field:t.field,order:t.order}]:[];L(3,h,a,e,[],n).then(i=>{if((i==null?void 0:i.status)!==200){d.error(i==null?void 0:i.info);return}z((i==null?void 0:i.info)||[]),x({current:a,pageSize:e,total:(i==null?void 0:i.total)||0})}).catch(()=>d.error("Failed to fetch list")).finally(()=>l(!1))};r.useEffect(()=>{m()},[h]);const D=a=>{N("Are you sure you want to delete?",()=>{J({id:a.key}).then(e=>{if((e==null?void 0:e.status)!==200){d.error(e==null?void 0:e.info);return}d.success("Deleted successfully"),m(s.current,s.pageSize)}).catch(()=>d.error("Delete failed"))})},C=a=>{O(a.key).then(e=>{if((e==null?void 0:e.status)!==200){d.error(e==null?void 0:e.info);return}let t=(e==null?void 0:e.info)||null;if(t&&typeof t.parameter=="string")try{t.parameter=JSON.parse(t.parameter)}catch{t.parameter={}}f(t),c(!0)}).catch(()=>d.error("Failed to fetch detail"))},F=()=>{f(null),c(!0)},I=()=>{c(!1),f(null)},M=(k||[]).map(a=>{let e={};if(typeof a.parameter=="string")try{e=JSON.parse(a.parameter)}catch{e={}}else typeof a.parameter=="object"&&a.parameter!==null&&(e=a.parameter);return{key:a.id,name:a.name,mode:e.mode,modified_time:a.modified_time?K(a.modified_time):"",description:a.description||"",originResource:a}}),v=[{title:"Name",dataIndex:"name",key:"name",sorter:!0,width:"12%"},{title:"Mode",dataIndex:"mode",key:"mode",sorter:!0,width:"12%"},{title:"Modified",dataIndex:"modified_time",key:"modified_time",sorter:!0},{title:"Description",dataIndex:"description",key:"description",sorter:!0},{title:"Operation",key:"operation",render:(a,e)=>o.jsxs(E,{size:24,children:[o.jsx(j,{style:{padding:0},type:"link",onClick:()=>C(e),children:"Edit"}),o.jsx(j,{style:{padding:0},type:"link",onClick:()=>D(e),children:"Delete"})]})}];return o.jsxs("div",{children:[o.jsx("div",{style:{marginBottom:16},children:o.jsxs(j,{type:"primary",onClick:F,children:[o.jsx(T,{component:$}),"Create New Portal Webroot Profile"]})}),o.jsx(A,{columns:v,dataSource:M,loading:b,pagination:{current:s.current,pageSize:s.pageSize,total:s.total,showTotal:(a,e)=>`${e[0]}-${e[1]} of ${a} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],onChange:(a,e)=>m(a,e)},rowKey:"key",bordered:!0,onChange:(a,e,t)=>{let n="",i="";if(!Array.isArray(t))if(t.order==="ascend"?n="asc":t.order==="descend"&&(n="desc"),typeof t.field=="string")switch(t.field){case"mode":i="parameter.mode";break;default:i=t.field}else i="";P({field:i,order:n}),m(a.current,a.pageSize,{field:i,order:n})}}),o.jsx(Z,{open:w,onClose:I,refresh:()=>m(s.current,s.pageSize),resource:g,isDisabled:!1,siteId:h},(g==null?void 0:g.id)||String(w))]})},se=({siteId:h})=>{const[k,z]=r.useState([]),[b,l]=r.useState(!1),[w,c]=r.useState(!1),[g,f]=r.useState(null),[s,x]=r.useState({current:1,pageSize:10,total:0}),[y,P]=r.useState(""),[m,D]=r.useState({}),C=4;if(window.location.hash){const e=window.location.hash.replace("#","");/^\d+$/.test(e)&&(h=parseInt(e,10))}const F=e=>{try{const t=typeof e=="string"?JSON.parse(e):e,n=t==null?void 0:t.time_range;return Array.isArray(n)?n.join(`
`):"No time range"}catch{return"Invalid time range"}},I=async(e=1,t=10,n=m)=>{l(!0);try{const i=n.field?[{field:n.field,order:n.order}]:[],p=await L(C,h,e,t,[],i);if(p.status===200&&Array.isArray(p.info)){let u=p.info.map(S=>({...S,key:S.id,timeRange:F(S.parameter),modified_time:S.modified_time?K(S.modified_time):""}));z(u),x({current:e,pageSize:t,total:p.total||0})}else d.error("Failed to fetch profile list")}catch{d.error("Failed to fetch profile list")}finally{l(!1)}};r.useEffect(()=>{I()},[h]);const M=e=>{N("Are you sure you want to delete?",()=>{J({id:e.key}).then(t=>{if((t==null?void 0:t.status)!==200){d.error(t==null?void 0:t.info);return}d.success("Successfully Deleted"),I(s.current,s.pageSize)}).catch(()=>d.error("Delete Failed"))})},v=(e,t,n)=>{x({...s,current:e.current,pageSize:e.pageSize});let i="",p="";!Array.isArray(n)&&(n!=null&&n.order)&&(i=n.order==="ascend"?"asc":"desc",p=n.field||""),D({field:p,order:i}),I(e.current,e.pageSize,{field:p,order:i})},a=[_({title:"Name",dataIndex:"name",enableSorter:!0,enableFilter:!1,filterDropdownComponent:W,defaultValue:"",width:"15%"}),_({title:"Time Range",dataIndex:"timeRange",enableSorter:!1,enableFilter:!1,render:e=>o.jsx("div",{style:{padding:"13px 0"},children:e.split(`
`).filter(Boolean).map(t=>{const[n,i]=t.split(" ");return o.jsxs("div",{children:[o.jsx("span",{style:{display:"inline-block",width:"100px",textAlign:"left"},children:n}),o.jsx("span",{children:i})]})})}),width:"15%"}),_({title:"Modify",dataIndex:"modified_time",enableSorter:!0,enableFilter:!1,width:"15%"}),_({title:"Description",dataIndex:"description",enableSorter:!0,enableFilter:!1,width:"30%",render:e=>o.jsx("div",{style:{padding:"13px 0",whiteSpace:"normal",wordBreak:"break-word"},children:e})}),{title:"Operation",width:"15%",render:(e,t)=>o.jsxs(E,{size:24,children:[o.jsx(j,{style:{padding:0},type:"link",onClick:()=>{f(t),c(!0)},children:"Edit"}),o.jsx(j,{type:"link",style:{padding:0},onClick:()=>M(t),children:"Delete"})]})}];return o.jsxs("div",{children:[o.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:16},children:o.jsxs(j,{type:"primary",onClick:()=>{f(null),c(!0)},children:[o.jsx(T,{component:$}),"Create New Time Range Profile"]})}),o.jsx(A,{columns:a,dataSource:k,loading:b,onChange:v,pagination:{current:s.current,pageSize:s.pageSize,total:s.total,showTotal:(e,t)=>`${t[0]}-${t[1]} of ${e} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"]},scroll:{x:1e3},rowKey:"key",bordered:!0}),o.jsx(ee,{visible:w,resource:g,siteId:h,onClose:()=>c(!1),onSuccess:()=>{c(!1),I()}})]})},me=()=>{const h=U(x=>x.user.userInfo),k=H(),z=Q(),{handleSiteChange:b}=ie(!1),{selectedSiteId:l}=ae(),[w,c]=r.useState("");ne();const g=[{key:"SSIDRadius",label:"SSID Radius",children:o.jsx(R,{component:oe})},{key:"MPSKUser",label:"MPSK",children:o.jsx(R,{component:re})},{key:"CaptivePortal",label:"Portal",children:o.jsx(R,{component:le})},{key:"TimeRange",label:"Time Range",children:o.jsx(R,{component:se})}],f=h.type==="readonly"?[]:g;r.useEffect(()=>{const x=k.pathname.match(/(SSIDRadius|MPSKUser|CaptivePortal|TimeRange)$/);if(x)c(x[0]);else if(f.length>0){c(f[0].key);let y=`${k.pathname.replace(/\/$/,"")}/${f[0].key}`;l&&(y+=`#${l}`),z(y)}},[k.pathname,f]);const s=x=>{let y=k.pathname.replace(/(SSIDRadius|MPSKUser|CaptivePortal|TimeRange)$/,"");y=`${y.replace(/\/$/,"")}/${x}`,l&&(y+=`#${l}`),z(y)};return o.jsxs(X,{className:"div-main",children:[o.jsx("div",{className:"div-header",children:o.jsx(te,{onChange:b})}),o.jsx("div",{className:"div-tabs",children:o.jsx(q,{activeKey:w,onChange:s,destroyInactiveTabPane:!0,items:f})})]})};export{me as default};
