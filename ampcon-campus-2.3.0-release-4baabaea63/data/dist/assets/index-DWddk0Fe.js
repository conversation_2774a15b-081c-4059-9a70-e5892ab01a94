import{u as W,G as N,y as T,a as v,r,b as D,ai as X,j as n,V as _,B as S,Q as J,H as I,O as Q,M as G,D as E,W as z,A as K,R as Y,a4 as ee,c as te,ca as se,ax as A,cb as ne,C as ae,X as q,al as ie}from"./index-CCDcquaz.js";import{W as re}from"./CustomTable-B4Am8LRY.js";import{u as le}from"./useDataGrid-C8ZjC9Fo.js";const oe=async e=>{try{const t=e?`blacklist?countOnly=true&siteId=${e}`:"blacklist?countOnly=true";return(await v.get(t)).data}catch(t){throw console.error("Error fetching getBlacklistCount:",t),t}},ce=({enabled:e,siteId:t})=>W(["blacklist","count",t],()=>oe(t),{enabled:e}),de=async(e,t,a)=>{try{const c="blacklist",l=new URLSearchParams;e&&l.append("limit",e.toString()),t&&l.append("offset",t.toString()),a&&l.append("siteId",a);const m=`${c}?${l.toString()}`;return(await v.get(m)).data}catch(c){throw console.error("Error fetching getBlacklistDevices:",c),c}},ue=({pageInfo:e,enabled:t,siteId:a,onError:c})=>{const l=(e==null?void 0:e.limit)!==void 0?e.limit*e.index:0;return W(["blacklist","all",{limit:e==null?void 0:e.limit,offset:l,siteId:a}],()=>de((e==null?void 0:e.limit)||0,l,a),{keepPreviousData:!0,enabled:e!==void 0,staleTime:3e4,onError:c})},pe=async(e,t)=>{const a=t?`blacklist/${e}?siteId=${t}`:`blacklist/${e}`;return v.delete(a)},me=()=>{const e=N();return T(t=>pe(t.serialNumber,t.siteId),{onSuccess:()=>{e.invalidateQueries(["blacklist"])}})},he=async e=>{const t=e.siteId?`blacklist/${e.serialNumber}?siteId=${e.siteId}`:`blacklist/${e.serialNumber}`;return v.put(t,e).then(a=>a.data)},xe=()=>{const e=N();return T(he,{onSuccess:t=>{var c;const a=e.getQueriesData(["blacklist","all"]);for(const[l,m]of a){const o=m,p=(c=o==null?void 0:o.devices)==null?void 0:c.findIndex(x=>x.serialNumber===t.serialNumber);if(p!==void 0&&p>=0){const x=[...(o==null?void 0:o.devices)??[]];x[p]=t,e.setQueryData(l,{devices:x})}}}})},ge=async e=>{const t=e.siteId?`blacklist/${e.serialNumber}?siteId=${e.siteId}`:`blacklist/${e.serialNumber}`;return v.post(t,e).then(a=>a.data)},fe=()=>{const e=N();return T(ge,{onSuccess:()=>{e.invalidateQueries(["blacklist","count"]),e.invalidateQueries(["blacklist","all"])}})},ye=e=>r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:16,height:16,viewBox:"0 0 16 16",...e},r.createElement("g",null,r.createElement("g",null),r.createElement("g",null,r.createElement("g",null,r.createElement("path",{d:"M13.390143,6.6719193L10.7497492,6.6719193C10.4131203,6.6719193,10.1402273,6.3990269,10.1402273,6.062397C10.1402273,5.7257671000000006,10.4131203,5.4528744,10.7497492,5.4528744L11.810215,5.4528744C10.8224821,4.0512688,9.214509,3.2179475,7.4998331,3.2190462C4.5836589,3.2191658,2.2196968,5.5834198,2.2196968,8.499834100000001C2.2198162,11.4163685,4.5840702,13.780622,7.5004849,13.780622C10.4170189,13.780503,12.781273,11.4162483,12.781274,8.499834100000001C12.770009,8.155575299999999,13.046027,7.8703766,13.390471,7.8703766C13.734914,7.8703766,14.010932,8.155575299999999,13.999666,8.499834100000001C13.999666,12.0891,11.089098,14.999667,7.4998331,14.999667C3.910568,14.999667,1,12.089099,1,8.499833599999999C1,4.9105687,3.910568,2.00000074683419,7.4998331,2.00000074683419C9.5966444,1.99901358143,11.564398,3.0124317,12.781274,4.7200115L12.781274,3.6249599C12.770009,3.2807006999999997,13.046027,2.9955023499999998,13.390471,2.9955023499999998C13.734914,2.9955023499999998,14.010932,3.2807013,13.999666,3.6249599L13.999666,6.062397C13.999666,6.3990269,13.726773,6.6719193,13.390143,6.6719193Z",fill:"#14C9BB",fillOpacity:1}),r.createElement("path",{d:"M13.927207,6.5994606Q14.149666,6.3770013,14.149666,6.062397L14.149666,3.627274Q14.159211,3.3071988,13.936246,3.0768172Q13.712378,2.84550238,13.390471,2.84550238Q13.068564,2.84550238,12.844695,3.0768168Q12.621716,3.3072106999999997,12.631274,3.6273055999999997L12.631274,4.2761319Q11.768703,3.2191457000000003,10.5392084,2.58593982Q9.1087656,1.84924325,7.4998331,1.85000075Q4.7457521,1.85000114,2.7978762,3.7978769999999997Q0.84999999,5.7457528,0.85000075,8.499833599999999Q0.85000075,11.2539158,2.7978766,13.201792Q4.7457521,15.149668,7.4998322,15.149668Q10.2539139,15.149668,12.20179,13.201792Q14.148844,11.2547379,14.149666,8.5021591Q14.159215,8.1820769,13.936246,7.9516912Q13.712378,7.7203765,13.390471,7.7203765Q13.068564,7.7203765,12.844695,7.9516912Q12.621722,8.1820793,12.631274,8.5021639Q12.63045,10.6258373,11.128513,12.127773Q9.6257505,13.630536,7.5004854,13.630623Q5.3753066,13.630623,3.8725462,12.127863Q2.3697838,10.6251001,2.3696976000000003,8.499833599999999Q2.3696975,6.37465,3.8722725,4.871889100000001Q5.3748426,3.3691334,7.499929,3.3690461000000003Q8.749326199999999,3.3682455,9.8586092,3.9431241999999997Q10.8360796,4.4496918,11.51106,5.3028743L10.7497492,5.3028743Q10.4351454,5.3028743,10.2126865,5.5253334Q9.9902277,5.7477925,9.9902277,6.062397Q9.9902277,6.3770022,10.2126865,6.5994606Q10.4351454,6.8219194,10.7497492,6.8219194L13.390144,6.8219194Q13.704748,6.8219194,13.927207,6.5994606ZM13.849666,3.6225064L13.849666,6.062397Q13.849666,6.2527375,13.715075,6.3873286Q13.580484,6.5219193,13.390144,6.5219193L10.7497492,6.5219193Q10.5594091,6.5219193,10.424819,6.3873286Q10.2902279,6.2527375,10.2902279,6.062397Q10.2902279,5.8720567,10.424819,5.7374656Q10.5594091,5.6028745,10.7497492,5.6028745L12.099427,5.6028745L11.932828,5.366467200000001Q11.170939,4.2853379,9.9966459,3.6767678999999998Q8.8223543,3.0681986,7.4997368,3.069046Q5.2505622,3.0691384,3.6601274,4.6597703Q2.0696974,6.2503967,2.0696976,8.499833599999999Q2.0697889,10.7493696,3.6604145,12.339995Q5.2510424,13.930623,7.5004854,13.930623Q9.75002,13.930531,11.340646,12.339906Q12.931273,10.7492781,12.931273,8.499836L12.931273,8.4973803L12.931193,8.4949279Q12.924824,8.3002744,13.060268,8.160325499999999Q13.195712,8.0203767,13.390471,8.020376200000001Q13.585229,8.0203767,13.720673,8.160325499999999Q13.856117,8.3002744,13.849747,8.4949279L13.849666,8.4973803L13.849666,8.499833599999999Q13.849666,11.1296511,11.989657,12.989659Q10.1296492,14.849668,7.4998322,14.849668Q4.8700161,14.849668,3.0100086,12.989659Q1.1500007700000001,11.1296511,1.1500007700000001,8.499833599999999Q1.15000001,5.8700168,3.0100081,4.0100088Q4.8700159,2.15000115,7.4998331,2.15000077Q9.0361185,2.1492774900000002,10.4018507,2.85264724Q11.767583,3.5560172999999997,12.659118,4.8070638L12.931273,5.1889655999999995L12.931273,3.6225065L12.931193,3.6200542Q12.924824,3.4254,13.060268,3.2854512Q13.195712,3.1455024,13.390471,3.1455024Q13.585229,3.1455024,13.720673,3.2854512Q13.856117,3.4254001,13.849747,3.6200539000000003L13.849666,3.6225064Z",fillRule:"evenodd",fill:"#14C9BB",fillOpacity:1}))))),be=({device:e,refreshTable:t,onOpenEdit:a,siteId:c})=>{const{t:l}=D(),m=X(),{mutateAsync:o,isLoading:p}=me(),[x,y]=r.useState(!1),g=()=>{J(l("Are you sure you want to delete this Device?"),async()=>{try{await o({serialNumber:e.serialNumber,siteId:c}),t(),I.success(l("Successfully Deleted"))}catch{I.error(l("Delete failed"))}})},f=()=>{a({...e,siteId:c})};return n.jsxs(_,{size:"middle",children:[n.jsx(S,{type:"link",style:{color:"#14C9BB",padding:0},onClick:f,children:l("common.edit")}),n.jsx(S,{type:"link",style:{color:"#14C9BB",padding:0},onClick:()=>m(`/wireless/devices/${e.serialNumber}`),children:l("common.view_details")}),n.jsx(S,{type:"link",style:{color:"#14C9BB",padding:0},onClick:g,children:l("crud.delete")})]})},Ce=({isOpen:e,onClose:t,tableController:a,totalCount:c,refetchCount:l,siteId:m})=>{const{t:o}=D(),[p]=Q.useForm(),x=fe(),y=r.useRef(null),[g,f]=r.useState(""),[k,b]=r.useState(!1);r.useEffect(()=>{e?setTimeout(()=>{var h;(h=y.current)==null||h.focus()},200):(p.resetFields(),f(""),b(!1))},[e,p]);const i=async()=>{b(!0);try{const h=await p.validateFields(),{serialNumber:u,reason:j}=h;await x.mutateAsync({serialNumber:u,reason:j,siteId:m},{onSuccess:async()=>{I.success(o("Successfully Added")),await l();const C=c+1,w=Math.ceil(C/a.pageInfo.pageSize);a.onPaginationChange({pageIndex:w-1,pageSize:a.pageInfo.pageSize}),t()},onError:C=>{var w,B;I.error(((B=(w=C==null?void 0:C.response)==null?void 0:w.data)==null?void 0:B.ErrorDescription)||o("common.error"))}})}catch{}finally{b(!1)}},L=(h,u)=>u?(u==null?void 0:u.length)===12&&/^[a-fA-F0-9]+$/.test(u)?(f(""),Promise.resolve()):(f(o("Invalid serial number. A serial number should be 12 HEX chars (A-F, 0-9)")),Promise.reject(new Error(o("Invalid serial number. A serial number should be 12 HEX chars (A-F, 0-9)")))):(f(o("Invalid serial number. A serial number should only be 12 HEX chars (A-F, 0-9)")),Promise.reject(new Error(o("Invalid serial number. A serial number should only be 12 HEX chars (A-F, 0-9)"))));return n.jsxs(G,{title:null,open:e,onCancel:t,onOk:i,width:680,bodyStyle:{height:365},style:{height:"450px",borderRadius:"8px",overflow:"hidden"},footer:[n.jsx(S,{onClick:t,children:o("Cancel")},"cancel"),n.jsx(S,{type:"primary",onClick:i,loading:k,style:{color:"#FFFFFF",background:"#14C9BB",borderColor:"#14C9BB"},children:o("Apply")},"submit")],children:[n.jsxs("div",{style:{padding:" 0 0 0"},children:[n.jsx("div",{style:{width:"65px",height:"24px",fontFamily:"Lato, sans-serif",fontWeight:700,fontSize:"20px",lineHeight:"24px",textAlign:"left",fontStyle:"normal",textTransform:"none"},children:o("Create")}),n.jsx(E,{style:{margin:"10px 0px 16px -24px",width:"calc(100% + 48px)"}})]}),n.jsxs(Q,{form:p,layout:"horizontal",labelAlign:"left",labelCol:{flex:"100px"},wrapperCol:{flex:"auto"},style:{paddingBottom:"60px"},children:[n.jsx(Q.Item,{label:o("Serial Number"),name:"serialNumber",validateTrigger:"onChange",rules:[{message:o("Invalid serial number. A serial number should only be 12 HEX chars (A-F, 0-9)")},{validator:L}],validateStatus:g?"error":"",help:g,children:n.jsx(z,{ref:y,style:{width:"280px",height:"36px",borderRadius:"2px",border:g?"1px solid #F53F3F":"1px solid #B2B2B2"}})}),n.jsx(Q.Item,{label:o("Reason"),name:"reason",rules:[{required:!1}],children:n.jsx(z.TextArea,{rows:2,style:{width:"280px",height:"56px",borderRadius:"2px",border:"1px solid #B2B2B2"}})})]}),n.jsx(E,{style:{margin:"110px 0px 16px -24px",width:"calc(100% + 48px)"}})]})},ke=({modalProps:e,device:t})=>{var y,g,f;const{t:a}=D(),c=xe(),[l]=Q.useForm(),[m,o]=r.useState({reason:""}),p=()=>{l.validateFields().then(k=>{c.mutate({serialNumber:(t==null?void 0:t.serialNumber)??"",reason:k.reason,siteId:t==null?void 0:t.siteId},{onSuccess:()=>{c.reset(),I.success(a("controller.devices.updated_blacklist")),e.onClose()}})}).catch(()=>{})},x=()=>{l.setFieldsValue(m),e.onClose()};return r.useEffect(()=>{if(t){const k={reason:(t==null?void 0:t.reason)??""};o(k),l.setFieldsValue(k)}},[t,l,e.isOpen]),n.jsxs(G,{open:e.isOpen,width:680,bodyStyle:{height:365},style:{height:"450px",borderRadius:"8px",overflow:"hidden"},onCancel:x,onOk:p,confirmLoading:c.isLoading,okText:a("Apply"),cancelText:a("Cancel"),destroyOnClose:!0,children:[n.jsxs("div",{style:{padding:" 0 0 0"},children:[n.jsx("div",{style:{width:"65px",height:"24px",fontFamily:"Lato, sans-serif",fontWeight:700,fontSize:"20px",lineHeight:"24px",textAlign:"left",fontStyle:"normal",textTransform:"none"},children:a("Edit")}),n.jsx(E,{style:{margin:"10px 0px 16px -24px",width:"calc(100% + 48px)"}})]}),c.error&&n.jsx(K,{message:a("common.error"),description:(f=(g=(y=c.error)==null?void 0:y.response)==null?void 0:g.data)==null?void 0:f.ErrorDescription,type:"error",showIcon:!0,style:{marginBottom:16}}),n.jsx(Q,{form:l,layout:"horizontal",initialValues:m,children:n.jsx(Q.Item,{label:a("controller.devices.reason"),name:"reason",rules:[{required:!1,message:a("common.required")}],style:{paddingTop:"5px",paddingBottom:"85px"},children:n.jsx(z.TextArea,{rows:2,style:{width:"280px",height:"56px",borderRadius:"2px 2px 2px 2px",border:"1px solid #B2B2B2"}})})}),n.jsx(E,{style:{margin:"165px 0px 16px -24px",width:"calc(100% + 48px)"}})]})},Qe=({siteId:e})=>{var R,M,$,V,H;const{t}=D(),a=X(),c=ee(),[l,m]=r.useState(),[o,p]=r.useState(!1),[x,y]=r.useState(!1),[g,f]=r.useState(!1),b=c.hash.replace("#","")||void 0||e||"all",i=le({tableSettingsId:"gateway.blacklist.table",defaultOrder:["serialNumber"]}),L={limit:i.pageInfo.pageSize,index:i.pageInfo.pageIndex},h=ue({pageInfo:L,enabled:!0,siteId:b}),u=ce({enabled:!0,siteId:b}),j=r.useCallback(s=>{m(s),y(!0)},[]),C=r.useCallback(s=>{a(`/wireless/devices/${s}`)},[a]),w=()=>a(-1);r.useEffect(()=>{var d;if(!((d=u.data)!=null&&d.count))return;const s=Math.ceil(u.data.count/i.pageInfo.pageSize);i.pageInfo.pageIndex+1>s&&s>0?i.onPaginationChange({pageIndex:s-1,pageSize:i.pageInfo.pageSize}):h.refetch()},[(R=u.data)==null?void 0:R.count,i.pageInfo.pageIndex,i.pageInfo.pageSize]);const B=r.useCallback(()=>{var P;const s=((P=u.data)==null?void 0:P.count)??0,d=Math.ceil(s/i.pageInfo.pageSize);i.pageInfo.pageIndex+1>d&&d>0?i.onPaginationChange({pageIndex:d-1,pageSize:i.pageInfo.pageSize}):h.refetch()},[(M=u.data)==null?void 0:M.count,i.pageInfo.pageIndex,i.pageInfo.pageSize,h.refetch]),U=r.useCallback(s=>{i.onPaginationChange({pageIndex:s.current-1,pageSize:s.pageSize})},[i]),F=r.useMemo(()=>[{key:"serialNumber",title:t("Serial Number"),dataIndex:"serialNumber",sorter:(s,d)=>s.serialNumber.localeCompare(d.serialNumber),columnsFix:!0,render:(s,d)=>n.jsx(te.Link,{onClick:()=>C(d.serialNumber),style:{height:"17px",fontFamily:"Lato, sans-serif",fontWeight:400,fontSize:"14px",color:"#14C9BB",lineHeight:"17px",textAlign:"left",fontStyle:"normal",textDecoration:"underline",textDecorationColor:"#14C9BB",textTransform:"none",display:"inline-block",cursor:"pointer"},children:s})},{key:"created",title:"Added",dataIndex:"created",sorter:(s,d)=>new Date(s.created).getTime()-new Date(d.created).getTime(),render:s=>n.jsx(se,{date:s})},{key:"author",title:"By",dataIndex:"author",sorter:(s,d)=>s.author.localeCompare(d.author)},{key:"reason",title:"Reason",dataIndex:"reason",sorter:(s,d)=>(s.reason||"").localeCompare(d.reason||"")},{key:"actions",title:"Operation",columnsFix:!0,render:(s,d)=>n.jsx(be,{device:d,refreshTable:B,onOpenEdit:j,siteId:b})}],[t,C,j,B,g,b]),O=r.useMemo(()=>F.filter(s=>typeof s.title=="string").map(s=>({key:s.key,title:s.title,dataIndex:s.dataIndex,columnsFix:s.columnsFix||!1,fixed:s.key==="serialNumber"||s.key==="actions"})),[F]);r.useEffect(()=>{if(Object.keys(i.columnVisibility||{}).length===0){const s={};O.forEach(d=>{s[d.key]=(d.columnsFix,!0)}),i.setColumnVisibility(s)}},[O,i]);const Z=r.useMemo(()=>F.filter(s=>s.columnsFix||s.key==="settings"?!0:i.columnVisibility[s.key]!==!1),[F,i.columnVisibility]);return n.jsxs(n.Fragment,{children:[n.jsx(A,{style:{marginTop:1,marginBottom:16},children:n.jsxs(A,{as:"button",onClick:w,display:"flex",fontFamily:"Lato, sans-serif",fontWeight:500,fontSize:"14px",color:"#929A9E",lineHeight:"16px",cursor:"pointer",bg:"transparent",border:"none",p:0,_hover:{opacity:.8},_active:{opacity:.6},_focus:{outline:"none"},children:[n.jsx(ne,{style:{marginRight:"8px",fontSize:"14px"}}),n.jsx(A,{as:"span",children:"Back"})]})}),n.jsxs(ae,{style:{width:"100%",minHeight:"100%",borderRadius:0,boxShadow:"none"},bodyStyle:{padding:24},children:[n.jsxs(_,{style:{marginTop:16,marginBottom:8},children:[n.jsxs(S,{type:"primary",onClick:()=>p(!0),children:[n.jsx(q,{component:ie}),"Create"]}),n.jsx(S,{onClick:()=>{B(),I.success("Blacklist refreshed")},icon:n.jsx(q,{component:ye,style:{fontSize:15.6}}),children:"Refresh"})]}),n.jsx(re,{columns:Z,dataSource:(($=h.data)==null?void 0:$.devices)||[],showColumnSelector:"true",loading:h.isFetching||u.isFetching,pagination:{current:i.pageInfo.pageIndex+1,pageSize:i.pageInfo.pageSize,total:(V=u.data)==null?void 0:V.count,showSizeChanger:!0,showQuickJumper:!0,showTotal:s=>`Total ${s} items`},onChange:U})]}),n.jsx(Ce,{isOpen:o,onClose:()=>p(!1),tableController:i,totalCount:((H=u.data)==null?void 0:H.count)||0,refetchCount:u.refetch,siteId:b}),n.jsx(ke,{device:l,modalProps:{isOpen:x,onClose:()=>y(!1)}})]})},Ie=Y.memo(Qe);export{Ie as default};
