<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>Captive Portal</title>
  <style type="text/css">
    html,body {width: 100%;height: 100%;margin: 0;padding: 0;}.container {width: 100%;height: 100%;min-height: 700px;display: -ms-flexbox;display: -webkit-box;display: flex;flex-direction: column;-ms-flex-align: center;-ms-flex-pack: center;-webkit-box-align: center;align-items: center;-webkit-box-pack: center;justify-content: center;background-color: #F0F8F9;background-repeat: no-repeat;background-size: 100% 100%;position: relative;font-family: Lato, Lato;}.content {background: #FFFFFF;box-shadow: 0px 0px 20px 0px #F5F5F5;border-radius: 16px 16px 16px 16px;padding: 20px 40px;max-width: 80%;position: relative;}.text-center {text-align: center;}.lead {font-weight: 700;font-size: 32px;color: #212519;line-height: 38px;text-align: left;overflow: hidden;word-wrap: break-word;}.btn {display: inline-block;font-weight: 400;color: #212529;text-align: center;text-decoration: none;vertical-align: middle;cursor: pointer;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;background-color: transparent;border: 1px solid transparent;padding: 10px 16px;font-size: 18px;line-height: 1.5;border-radius: 4px 4px 4px 4px;}.btn-primary {color: #fff;background-color: #14C9BB;border-color: #14C9BB;}.btn-block {width: 100%;}.form-signin {width: 100%;max-width: 360px;margin: 20px auto;margin-bottom: 50px;text-align: center;font-size: 14px;}.form-signin .form-control {position: relative;box-sizing: border-box;height: auto;padding: 10px;font-size: 16px;width: 100%;resize: none;background: none;border: none;}.form-signin .form-control:focus {z-index: 2;}input:focus {border-color: #14C9BB !important;}.padtop {padding-top: 15px;position: relative;}.logo_p {margin: 25px;margin-left: 0;}#logo {max-width: 200px;}#corporate-info {font-weight: 400;font-size: 14px;color: #929A9E;text-align: center;max-width: 250px;position: absolute;bottom: 20px;overflow: hidden;word-wrap: break-word;}#readme {background: #fff;border-radius: 8px 8px 8px 8px;position: absolute;top: 0;left: 0;right: 0;bottom: 0;z-index: 11;}.terms-head {font-weight: 700;font-size: 20px;color: #323333;line-height: 60px;text-indent: 1em;border-bottom: 1px solid #E7E7E7;position: relative;}#readmeTxt {position: absolute;top: 80px;left: 20px;right: 20px;bottom: 20px;background: #F8FAFB;border-radius: 4px 4px 4px 4px;padding: 10px;overflow-y: auto;word-wrap: break-word;}#terms-btn {position: absolute;right: 15px;font-size: 16px;display: inline-block;cursor: pointer;}#submit {margin-top: 20px;}#checkError {color: #F53F3F;font-size: 12px;}@media (max-width:800px){.lead {font-size: 16px;}#corporate-info {font-size: 11px;}}#username,#password {border: 1px solid #F7F7F8;background: #F7F7F8;border-radius: 4px 4px 4px 4px;outline: none;background-repeat: no-repeat;background-size: auto 60%;background-position-y: center;background-position-x: 5px;padding-left: 40px;}.errormsg {color:#F53F3F;font-size: 14px;text-align: left;display: none;}.errorinput {border: 1px solid #F53F3F !important;}input[type="password"]::-ms-reveal,input[type="password"]::-ms-clear {display: none !important;}
    #username {margin-bottom: -1px;background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABGZJREFUaEPtWb1vW1UU/53rJg6iEu5QFL9UcO3Ebpql6QabI1WCAaRUAgkGRLuBhET6F6SZGNOIgW4JA6ISlciA1AGkmgkkkJIJRX6J/RhiZ6iEF1Bsx/eg+2Kn/nz32e/lo5Lf6HvPPb/fOeeej2vCS/7RS44fIwLn7cGRB0YeCGiBUQi1GnA6bX3EjHcAvs3ANQL9AuI/1aXomvOXcxDQ2D3FQ/GAnJOTVKs+BXi+H0gBcW/P3t8Im0RgAhq8qFX+0BY3gSOIz/P2/iPTvkHWAxNIpKytF5anMjGvqKPohuM45ZnZaxml6l8yY7EJilm87ezu/z4ISK+9gQhMp61Fxfyjq4DgsOAFZ+fA6VSYTMVXGVhq/P6kYJc+vBAEEmnrazB/ocF4xbiUMkZj1QLAMb2Xa9FXHMc5DINEIA8kU9bPDL7tgoK45dj72/1AJVLxZwAyen2M6EYuV9w5dwKJlPUbwG+5BCKc6BU+TZAXkkAyFf+Ggc9MIaTXE+l4AQzZ8MDVXK74/Nw9oAuXYv7+5BJXo7d09um6xGnrATMvN37fKdilG2GAP84dAT4p5QSNVbYAzPbKRPryirHKcksGgiD6eC9XfBxAbZtoIAL6JHk9/i4pPG0HRM3LLJuZ53idfirYxffDAh/YAycXdGbqA5D6wQsYgR6/MZX+JJvNHl04Aq4n5mVM/Fv5CsB7HW3FE0H03V6uuBkm8OZZgUOoF6i5uauX/8Orl+Xr8nnYFu/UdyoETsPS/c4MlYDOOpHxaoaBeTC/BoJkRoxAx/0R4W+lVBZHE9u90u0wxEMhMJ2auqugPm22CmYgVCZgU0XUilf1Np8TsA64wEktNyusH4VdMUy0ks8VHwwjO3QalbOTkhQ96wM8S0S/QnGZBDkkRJmVioEpxlA33fBqNHVN0AW7NHQkDCwo09YigVfbwBMcwWKlHqln/YSEnJnMEJFuLTIEPMzbpftn4oHkTHyJCasvlFFZgO6fxqzrl5BvDzSspnv6RlfQfwLzqzyMfb4IuDFfF3r2dScqANsc4Tt+wiUMkF5n+CLQOox4zb5+wLod6nh1CYw3zfu5rCK85mUoI4FGjl9vxE2ZQQteo6MJVMeAb9quq1+5YBevDFWJ3WF8vLLVzDgUMGdrEMn24cZMgOAUcqXEUAS6rF8bT4TRAujnGF0XzOgBU2r2DKFkylpn8F234oVg/SZg7VlMHHoS8JsgPAkkUtY/J285zAvO7kHWj9W89rR71fO0bMEuLZj09SUgU1PzBKXnXeNFMilpXW/1qkmOa9ErppDtS6D12ZAIm/lc6Y5JoZ/1Y8PwOuikpvQUI4W1/G7poenM/gSOW2Q3fRJoI28X75kOO4/1vgRa012oF3h2Uoq6WGaw+8jV+gmIbwftq3yFEId0gd060P5S3eU00xNlp4BnFtLxiki97Del+Qmh7o62RUo/0fd53RuqkPkBNMwe/cdHL7mjw0sDz8rGXmgYgGcpMyJwltbupet/KUPeQJ4LtZkAAAAASUVORK5CYII=");}
    #password {background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAv5JREFUaEPtmE1S20AQhftJibfx0jZZyGC5WMIN7H0WduUAwA3ICYAT2D4B5ASQE8Aiezs7Cjm2UhVsltqSlKYT2bERoJ+RJZhyStqqZ+Z93dM90wNa8w9rrp9yANURzCPwX0XAMIyi/vZ3y4NisEGCHWiwXU0M7Os7+yVgM9lCRq3UAHBERI1wkRiA+fNoOO1mCZIKwNguGXBxTkQ70qJAtsbayXfr9kx6TIThygBGvdIC0ykRF5/MfwWCTaAfxPyOQAYzGk/tAJyMbibHaSFWAjDMjR0QXz6IggNQT/wqdG3bdoJEbZkb+wLiiJiMxX9mbtrDu6s0ECsBVOvl8VIIyGaNmzJJ6iW5Vrg/ZaZZouu63hxe/3xdgM1a+ZBBnZnXEoj3e3mrXmm5QjhpvT+XkPDze18j7SCrZEwoY2meCKC2/b7huu7lwvvjm2l11YWzGpcIwAu9YPbKJgF0MbqZtrMSsuo8UgBevdddvSGIPxLxh/liGILo66oLPxvH/E284QuZYuAfGwswK5kQ5/7yl5noZxPBAfNJktM6EsAreyjc919H/AMN61yVjUQkwOzwIXG6SFoI6gFa4EGVJirMosigvcWVBETdkTX9JDNnJMBmvXLMzN4ljbI6+sNE+QvE30vh1diaNtcK4FGJzgH+xU52C/ntIsPuXT1Ya9vW7eCpndIIVM2ydzpHNDMPcsMSVCmAYZb2Zx2Z76ocHAkMWBftoBKpFECmWsTZKAfwrhtRIuMOJqUAMkkcd44oBiifL7qs8ChgMLYmu2H/lQLMe2TR8Rr4QIEMh0n0bOsu9CVCKUBcgsr8zwGCvCR7Ege/EYXX/KC1lEagalb6RPzsdS6u8vhBlAI8empZqoLDutiNq/8L85cB8L0BvXQT73cCCGcja3Igk/zRLeW8PPZlJsrSJsl7U2xTv2mWO0x0mKXAmLmku7FZpygjbB5e7AUlqsx4ORsMAPqS9MVaCkBOgBqrHECN3x91eKolpFs/30Lp/Jd+dB6B9D5MN8MfIKAcT+tP1UMAAAAASUVORK5CYII=");}
    .pwdBtn {position: absolute;right: 10px;top: 25px;cursor: pointer;background-color: unset;background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAArxJREFUWEftlr9rE2EYx7/PXQIODv0DHOIgODhkcFDo0AxuCnYQBBV6iblYNRcziItiBTu3uSq1F5MrirNCBcdkEHTokM2lQ4aOghkcCib3yNvk0mt6P3sRity73vN8n8/7fZ/3fY5wwhedcD4kgHFPKHEwcTCuA3Hzkx5MHIzrQNx81x7UjcaCENbUwmbcAlHy3eoeARwGkSmEmbBcKeafRily3FjdaL4BUBrms2KbcwSwZjTuEWjdLiQB1x6q+c/HLRwmT6+bl8D8zY4l8GJZLQhg958F3Wj+AHB+lNCTQbkHqtIJUyxqzGvDzA7ALQAzI6Cdspo/dwDrojhKEjs69S8hR859seEA7Mmgy04zPB/q1Y3GrAS0QJQatgX3IeGxViysRnXJLb5Wb74ki5849QnSjXJJ+eSM950kr4zmVQvYmijQI6L5clFpHwe0Zpg3Cfzc0UJCZs9ivvKoVPg6qRk46iZ7xCHwE8TLMkvtoP6svX13gaz+dQC3JsCEnG+PBwIKhRXTnJH/sHBy1tU1cfxEuyBpWyL+LWIsptMEK8MWZ8fHeMQe2gLS97Xi7V2v0wgFqBvmAsD7b+P0FymaqngOhEDA2kZjiYhEz9irazHfkSRcBFMFQCYQeujwDoAPFnNbInrvzGPmF5VSYclNxxfQDW4woFx1Uek6xcRlYvAZdjzwAJ6B0YNE21pR+e6MX1k3M7K8//aNN+cF6QnoHHlCnIBOP025qqL0vBzTjSbb3zQ177t5AZmS+SMDWTuH2Jovl+6Ge2YOA9LmII2qH5woEgXw4PLBBFjc8EMzeAzt1z9R/2qiAtq1/eoEXpLAC+AI0I3mL3tsBR1xWN2pAq7VzRYzzwHoamr+bFgIv7ipAopCa3Vzrp9CJ6hfw8JPHTBs4bBxCWBYp7ziEgcTB+M6EDc/6cH/3sG/PwoHOBQggXMAAAAASUVORK5CYII=");background-repeat: no-repeat;background-size: 90%;background-position: center;height: 20px;width: 20px;border: none;z-index: 10;}.pwdBtn.acitve {background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAA2RJREFUWEftV01u2kAYfZ9tWCMVpC6TE5QsCs0qIIVsm54g4QRNTkA5QegJyA3KOo4EXaXQRegJwrISVPI62P6aGbBrhgEPIYuoYjZI2DPzvvfe92PCK1/0yvFhB3BbhXYM7hjcloFt9//fHszfuRUQimTRO3BYEWwxaE/8EnjEIA+gEcC/YKE3eV/rbcroxgxKUDY+gnEOILfJhSTAEnp2YDd/H1ZHJnuNAb696+4FdtBgZgFs60VE1yZAjQDmf7gXT5o1NIx5IFyTkBAY2kHGi5iZBTQVDBcB6wiMCoOl/MpqTsq1L6siXgtQXOLbflscvnAAoYcAzcnhZp7K/3QrxHSmUWHohM4nnewrAUoGrKC7EPUzgans6M4W/rRDu6qC1ALUggPWSvEcU+b7rpBWWEcuHUgtwEL/9iHJHIVcHx+eXKeBEBIiEJ4DyOLeuHQyTNtTuLs5Z4vaifeGftapegdVbwZaWfmBewXGRfx3iGqa13L33VxmGlxpvNXxs049umwVWFm6LHTj54TWpFS7XAL4pn97SuBv8YuMy8mHWiuNhXzfFXtOde+JcjIuHddTz5hViiuVmAUGk9KaHlwY3BSZ6T72EZG0wgKbFqomXSQZqPDjuHy8HwMsDG7OmWdeWJVROhbmNVJGngyqMLhtxyANlRBWcR79h6jeEnE9BqjI6/lZZz/NOwLUSwJUq8cCQHlZ3xVSySx8+u1MyrVPqd4RmRv+M7hgUZWYiA+MMjrB+pLEEqCaTYa1b0HO5YjMAlVr4jyo1DJjUgPn3hH+XchkweY0Y1+mWUVTC+OmoC3U+YHbVfqvUReRGR3SrG/bGBplrlpegOGkXDuIq4LOY7LwPgb3Sh9u+RmnmcZGmmej55L1qd9INgXjVicOkZOM5YsCHCWNLD+gsDkupbe9dUCF18mitjJ+aSeatePWqhYWAZ1mMh1TRmdnTU8ZdLY0vgErW6LRwCqKONhqaAdOMYIxvjNoSBZ7xOyJwdV3/D0OKUfER1IFlkqonwgeg+p/ysedVYwbAYwkf8GRX0wqX/2s00pTwBhgFOF8lK8w0+ekP42SY862CbC1WWx02TyRplZQjGWULQQ5AueYaAQmD8Qj8c1iB5me6Zdc8v6NGTQF/1Lv7QBuy+SOwR2D2zKw7f6/USXlKmhOL6IAAAAASUVORK5CYII=");}
  </style>
</head>
<body>
  <div class="container">
    <div class="content">
      <div id="auth_form">
        <p class="logo_p"><img id="logo" src="data:image/png;base64,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" alt="logo"></p>
        <div class="text-center"><p class="lead" id="title">Welcome to use Wi-Fi</p></div>
        <form class="form-signin" id="loginForm" method="post" name="loginForm" action="/hotspot"
        onsubmit="return validate();">
          <div id="hideforoneclick">
            <div>
              <input type="text" id="username" name="username" class="form-control" autocomplete="off">
              <div id="username_errormsg" class="errormsg">Please enter the correct username.</div>
            </div>
            <div class="padtop" style="margin-bottom: 20px;">
              <input type="password" id="password" name="password" class="form-control" autocomplete="off">
              <button type="button" id="togglePassword" class="pwdBtn"></button>
              <div id="password_errormsg" class="errormsg">Please enter the correct Password.</div>
            </div>
          </div>
          <div id="divReadme" class="form-signin" style="max-width:680px">
            <div style="text-align: left;">
              <input type="checkbox" id="agreeCheckbox">
              <label for="agreeCheckbox">
                I agree to <a href="javascript:void(0);" id="termsLink" style="color: #14C9BB; text-decoration: none;">Terms of Service</a>
              </label>
            </div>
            <div id="checkError" style="display: none;">You must agree to the Terms of Service to proceed.</div>
          </div>
          <input type="hidden" name="action" value="credentials">
          <button class="btn btn-primary btn-block" id="submit">Login</button>
        </form>
      </div>
      <div id="readme"><div class="terms-head">Terms of Service<div id="terms-btn">×</div></div><div id="readmeTxt"></div></div>
    </div>
    <div id="corporate-info">© 2025 FS.COM INC. All rights reserved</div>
  </div>
  <script>
    const $ = id => document.getElementById(id);
    $('readme').style.display = 'none';
    $('divReadme').style.display = $('readmeTxt').textContent.trim() ? 'block' : 'none';
    function login() { if (validate()) $('loginForm').submit(); return false; }
    $('termsLink').addEventListener('click', () => {
      $('readme').style.display = $('readme').style.display === 'none' ? 'block' : 'none';
    });
    $('terms-btn').addEventListener('click', () => {$('readme').style.display = 'none';});
    $('agreeCheckbox').addEventListener('change', () => {
      $('checkError').style.display = $('agreeCheckbox').checked ? 'none' : 'block';
    });
    function validate() {
      const username = $('username'), password = $('password');
      if ($('readmeTxt').textContent.trim() && !$('agreeCheckbox').checked) {
        $('checkError').style.display = 'block';
        return false;
      }
      if (!username.value.trim()) { username.focus(); return false; }
      if (!password.value.trim()) { password.focus(); return false; }
      return true;
    }
    $('togglePassword').addEventListener('click', () => {
      if ($('password').type === 'password') {
        $('password').type = 'text';
        $('togglePassword').classList.add('acitve');
      } else {
        $('password').type = 'password';
        $('togglePassword').classList.remove('acitve');
      }
    });
    function getUrlParam(name) {
      const match = window.location.search.match(new RegExp('[?&]' + name + '=([^&]*)'));
      return match ? decodeURIComponent(match[1]) : null;
    }
    if (getUrlParam('code') === '2') {
      $('username_errormsg').style.display = 'block';
      $('password_errormsg').style.display = 'block';
      $('username').classList.add('errorinput');
      $('password').classList.add('errorinput');
    }
  </script>
</body>
</html>