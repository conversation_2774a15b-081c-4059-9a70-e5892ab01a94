import{r as i,I as It,_ as kt,y as xe,b as _,H as M,a as Ne,u as Dt,R as oe,j as e,aB as qe,B as q,S as Ae,aS as Et,A as me,c as B,D as te,Z as lt,a0 as de,bp as Ot,bq as Ft,br as ct,V as re,Q as ye,ag as X,T as dt,M as Se,bn as Re,b4 as mt,a_ as Lt,b8 as ut,be as rt,O as D,ab as pe,bs as Pt,W as J,aq as Bt,aA as Le,ai as pt,b3 as Mt,b5 as _t,bt as $t,b7 as qt,bc as Rt,bd as Ut,ax as Pe,a2 as K,ae as zt,aj as Vt,bu as Ue,bv as ze,X as ve,al as Ve,g as Q,h as R,bw as Wt,F as Gt,bo as ht,ao as Te,P as ot,am as Ie,J as be,a1 as fe,bx as se,ac as We,by as ee,bz as Kt,bA as Jt,C as Ht}from"./index-CCDcquaz.js";import{u as at,S as Qt}from"./useMutationResult-BK5EZibL.js";import{P as E,W as Zt}from"./CustomTable-B4Am8LRY.js";import{r as Yt}from"./Form-CDHrBU_a.js";import{C as Xt,A as es,a as ts,M as ss,u as ns,H as Be}from"./useCommandModal-B9ChXmyw.js";import{d as rs}from"./dateFormatting-yHFQ8l7H.js";import{f as xt,e as os,g as as,F as is,h as ls}from"./Venues-AQBKyfj9.js";import{F as he,b as gt,u as cs,a as ds}from"./useUrlSync-DjGOfs83.js";/* empty css             */import"./SiteContext-D-2Xr-_P.js";var ms={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"},us=function(o,s){return i.createElement(It,kt({},o,{ref:s,icon:ms}))},ps=i.forwardRef(us);const hs=({serialNumber:t})=>xe(()=>Ne.post(`device/${t}/reboot`,{serialNumber:t,when:0})),xs=({serialNumber:t})=>xe(()=>Ne.post(`device/${t}/leds`,{serialNumber:t,when:0,pattern:"blink",duration:30})),gs=({serialNumber:t,keepRedirector:o,onClose:s})=>{const{t:r}=_();return xe(()=>Ne.post(`device/${t}/factory`,{serialNumber:t,keepRedirector:o}),{onSuccess:()=>{M.success(r("commands.factory_reset_success"),5),s()},onError:n=>{var a,m;M.error(r("commands.factory_reset_error",{e:((m=(a=n==null?void 0:n.response)==null?void 0:a.data)==null?void 0:m.ErrorDescription)||"unknown error"}),5)}})},fs=({serialNumber:t,extraId:o})=>{const{t:s}=_();return Dt(["get-gateway-device-rtty",t,o],()=>Ne.get(`device/${t}/rtty`).then(({data:r})=>r),{enabled:!1,onSuccess:({server:r,viewport:n,connectionId:a})=>{var c;const m=`https://${r}:${n}/connect/${a}`;(c=window.open(m,"_blank"))==null||c.focus()},onError:r=>{var a,m,c;const n=((a=r==null?void 0:r.response)==null?void 0:a.status)===404;M.error(n?s("devices.not_found_gateway"):s("devices.error_rtty",{e:(c=(m=r==null?void 0:r.response)==null?void 0:m.data)==null?void 0:c.ErrorDescription}),5)}})},ys=({device:t,refresh:o,isDisabled:s,onOpenScan:r,onOpenFactoryReset:n,onOpenUpgradeModal:a})=>{const{t:m}=_(),{refetch:c,isInitialLoading:g}=fs({serialNumber:t.serialNumber,extraId:"inventory-modal"}),{mutateAsync:d,isLoading:h}=hs({serialNumber:t.serialNumber}),{mutateAsync:l}=xs({serialNumber:t.serialNumber}),{onSuccess:f,onError:j}=at({objName:m("devices.one"),operationType:"reboot",refresh:o}),{onSuccess:C,onError:O}=at({objName:m("devices.one"),operationType:"blink",refresh:o}),F=()=>d(void 0,{onSuccess:f,onError:j}),b=()=>l(void 0,{onSuccess:()=>{C()},onError:y=>{O(y)}}),v=()=>n(t.serialNumber),P=()=>c(),p=[{key:"reboot",label:m("commands.reboot"),onClick:F},{key:"blink",label:m("commands.blink"),onClick:b},{key:"rtty",label:"RTTY",onClick:P},{key:"factory_reset",label:m("commands.factory_reset"),onClick:v}];return e.jsx(qe,{menu:{items:p},disabled:s,trigger:["click"],children:e.jsxs(q,{type:"link",style:{margin:0,padding:"0 4px"},children:[h||g?e.jsx(Ae,{size:"small"}):m("common.actions"),e.jsx(Et,{style:{marginLeft:"8px"}})]})})},bs=oe.memo(ys),vs="/assets/error-D_ymsMcw.svg",js="/assets/success-Cweqjn-W.svg",{Title:Me}=B,Cs={isOpen:E.bool.isRequired,onClose:E.func.isRequired,pushResult:E.instanceOf(Object)},ws={pushResult:null},Ge=({isOpen:t,onClose:o,pushResult:s})=>{const{t:r}=_(),n=(s==null?void 0:s.errorCode)===0,a=(s==null?void 0:s.errorCode)!==0,m=()=>{if(!(s!=null&&s.errors)||s.errors.length===0)return null;const d=s.errors[0],[h,...l]=d.split(":"),f=l.join(":").trim();return`Configuration not pushed, error code: ${h.trim()}, detail: ${f}`},c=()=>(s==null?void 0:s.errorCode)===-1?s==null?void 0:s.msg:a?m()||r("configurations.push_configuration_explanation",{code:(s==null?void 0:s.errorCode)??0}):r("configurations.push_success"),g=e.jsxs(e.Fragment,{children:[e.jsx(me,{style:{marginTop:8,marginBottom:24,backgroundColor:n?"rgba(43, 193, 116, 0.1)":"rgba(245, 63, 63, 0.1)",border:`1px solid ${n?"#2BC174":"#F53F3F"}`},type:n?"success":"error",showIcon:!0,icon:e.jsx("img",{src:n?js:vs,alt:n?"success":"error",style:{width:20,height:20}}),message:e.jsx("span",{style:{color:a?"#F53F3F":"#2BC174",fontWeight:500},children:c()})}),n&&e.jsxs("div",{style:{marginTop:24},children:[e.jsx(Me,{level:5,children:r("configurations.applied_configuration")}),e.jsx("div",{style:_e.scrollBox,children:e.jsx("pre",{children:JSON.stringify(JSON.parse((s==null?void 0:s.appliedConfiguration)||"{}"),null,2)})}),e.jsx(te,{style:{marginLeft:"0px",width:"616px"}}),e.jsx(Me,{level:5,style:{marginTop:32},children:r("common.errors")}),e.jsx("div",{style:_e.scrollBox,children:e.jsx("pre",{children:JSON.stringify(s==null?void 0:s.errors,null,2)})}),e.jsx(te,{style:{marginLeft:"0px",width:"616px"}}),e.jsx(Me,{level:5,style:{marginTop:32},children:r("common.warnings")}),e.jsx("div",{style:_e.scrollBox,children:e.jsx("pre",{children:JSON.stringify(s==null?void 0:s.warnings,null,2)})})]})]});return e.jsx(lt,{title:r("configurations.configuration_push_result"),isModalOpen:t,onCancel:o,modalClass:"ampcon-max-modal",footer:null,childItems:g})},_e={scrollBox:{border:"1px solid #d9d9d9",borderRadius:4,padding:"0px 16px",height:"25vh",overflowY:"auto",backgroundColor:"#fafafa",fontSize:12,margin:"24px 0 32px 0"}};Ge.propTypes=Cs;Ge.defaultProps=ws;const Ss=({cell:{original:t},refreshTable:o,openEditModal:s,onOpenFactoryReset:r,onOpenUpgradeModal:n})=>{const{t:a}=_(),{onOpen:m,onClose:c}=de(),{isOpen:g,onOpen:d,onClose:h}=de(),[l,f]=oe.useState(!1),{data:j}=Ot(),{mutateAsync:C}=Ft({name:t.name,refreshTable:o,onClose:c}),O=ct({onSuccess:()=>{l&&(d(),f(!1))}}),F=t.venue===void 0||t.venue===null||t.venue==="",b=()=>C(t.serialNumber),v=()=>s(t),P=()=>{ye(e.jsx(B.Paragraph,{style:{display:"flex",alignItems:"center"},children:e.jsxs("span",{children:["Are you sure you want to push the configuration to device ",e.jsx("br",{}),e.jsxs("b",{children:["#",t.serialNumber]}),"?",e.jsx("br",{}),e.jsx("br",{}),"You cannot undo this action afterwards."]})}),()=>(f(!0),O.mutateAsync(t.serialNumber)),()=>{f(!1)},{confirmLoading:O.isLoading})},p=()=>{ye("Are you sure you want to delete this Device?",b)};return e.jsxs(re,{size:24,children:[e.jsx(q,{type:"link",onClick:v,style:{padding:0},children:a("common.edit")}),e.jsx(q,{type:"link",onClick:P,style:{margin:0,padding:"0 ",color:F?"#B3BBC8":void 0},disabled:F,children:a("configurations.push_configuration")}),e.jsx(bs,{device:t,refresh:o,onOpenFactoryReset:r,onOpenUpgradeModal:n}),e.jsx(q,{type:"link",onClick:p,style:{padding:0},children:a("crud.delete")}),e.jsx(Ge,{isOpen:g,onClose:h,pushResult:O.data})]})},Ns=(t,o,s)=>X.get(`inventory?withExtendedInfo=true&limit=${t}&offset=${o}${s?`&venue=${s}`:""}`).then(r=>r.data),As=async(t,o,s,r)=>{const n=(90-o)/Math.ceil(t/100);let a=o,m=0,c=[],g;do g=await Ns(100,m,r),c=c.concat(g.taglist),s(a+=n),m+=100;while(g.taglist.length===100);return c},Ts=async(t,o)=>{t(0);const s=await X.get(`inventory?countOnly=true${o?`&venue=${o}`:""}`).then(m=>m.data.count);if(t(10),s===0)return t(100),[];const r=await As(s,10,t,o);t(95);const n=m=>{try{return new Date(m*1e3).toISOString()}catch{return""}},a=r.map(m=>{var c,g;return{serialNumber:m.serialNumber,name:m.name,site:(g=(c=m==null?void 0:m.extendedInfo)==null?void 0:c.venue)==null?void 0:g.name,description:m.description,label:m.labelsName,modified:n(m.modified)}});return t(100),a},Is=(t,o)=>X.get(`inventory?withExtendedInfo=true&select=${t.join(",")}${o?`&venue=${o}`:""}`).then(s=>s.data),ks=async(t,o,s)=>{o(0);const r=t.length;if(o(10),r===0)return o(100),[];const n=(await Is(t,s)).taglist;o(95);const a=c=>{try{return new Date(c*1e3).toISOString()}catch{return""}},m=n.map(c=>{var g,d;return{serialNumber:c.serialNumber,name:c.name,site:(d=(g=c==null?void 0:c.extendedInfo)==null?void 0:g.venue)==null?void 0:d.name,description:c.description,label:c.labelsName,modified:a(c.modified)}});return o(100),m},Ds="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAe9JREFUWEftlkFOwkAUht8rxRBWTahr4Qa4AdnRRCXsPALeAE8gnkA8gUfQHREWZWMKK7gB7C1JV0igMDpFsNSh02khRsPs2k7f/8289/4ZhF8eGFVfMRppmCQsS9OsMLEiAajGSw0k6dYRXizuzEKpJgoRGmBDfKUaAiIUAFM8JIQwgK94CAghgEDighCBAbziBMgQiNRGJBVHE6EOBIoAkF0XYoCaCATAEp8v5poMUsXdBfbkqC4nbV0EggugdlpVQLhfrYqunIpbhfKQ1YaKrisiEL4AX8EGAKBQALc4fd7mAwwIyx7LGZZZcXcg1W0OEDDtFfcDoN/cEPTfUf4ywzIpLgANFEtOi6N86dkbIIgTHr82s7NpfLjNqrkAftYaBIBnzQcA5g7QvAc5XsOkwBt7A2BZcLMeAioEFteswnPnVBQgZTQrKOEjILTN3IW2NFDXoBVLZOw5rwi0zbPlpG1DFEDttp4A4Gp5f7A1s1BubwCoRqMIkkytdD8AnZb+uXp6XvwTgHVOaS4Rq2+58wfflO16B1Z2TDB2Mn+P3fA6R90HAM/pNrrmbwEAWECgL7JC7lx0bkvO0c5vQ260iBNYPuBywnTE8L6/O3eLcfyUFu2Ps8C5SCQm3xfLPZDYk0R/1TGRjuNdsB0APgDlroowDeDQuAAAAABJRU5ErkJggg==",Es=[{key:"serialNumber",label:"Serial Number"},{key:"name",label:"Name"},{key:"site",label:"Site"},{key:"description",label:"Description"},{key:"label",label:"Label"},{key:"modified",label:"Modified"}],Os=()=>{if(window.location.hash){const t=window.location.hash.replace("#","");return t==="all"?"":t}return""},Fs=({serialNumbers:t})=>{const{t:o}=_(),[s,r]=i.useState(!1),[n,a]=i.useState({progress:0,status:"idle"}),m=d=>{a(h=>({...h,progress:d}))},c=()=>{const d=Os();t?(a(h=>({...h,error:void 0,lastResults:void 0,status:"loading-select",progress:0})),ks(t,m,d).then(h=>{a(l=>({...l,status:"success",lastResults:h}))}).catch(h=>{a(l=>({...l,status:"error",error:h}))})):(a(h=>({...h,error:void 0,lastResults:void 0,status:"loading-all",progress:0})),Ts(m,d).then(h=>{a(l=>({...l,status:"success",lastResults:h}))}).catch(h=>{a(l=>({...l,status:"error",error:h}))})),r(!0)},g=()=>{r(!1)};return e.jsxs(e.Fragment,{children:[e.jsx(dt,{title:o("common.export"),children:e.jsx(q,{htmlType:"button",onClick:c,style:{display:"flex",alignItems:"center"},icon:e.jsx("img",{src:Ds,alt:"export",style:{width:16,height:16}}),children:"Export"})}),e.jsx(Se,{title:o("common.export"),open:s,onCancel:g,footer:null,width:680,style:{height:450},children:e.jsxs("div",{style:{padding:0},children:[(n.status.includes("loading")||n.status==="success")&&e.jsxs(e.Fragment,{children:[e.jsx(te,{style:{margin:"15px 0 45px 0",width:"calc(100% + 48px)",marginLeft:"-24px"}}),e.jsxs("div",{style:{margin:"139px 0 139px 0"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx(Re,{style:{flex:1,marginBottom:0},percent:Math.round(n.progress),status:n.progress!==100?"active":"success",showInfo:!1,strokeColor:n.progress===100?"#14C9BB":void 0,strokeWidth:12}),e.jsxs(B.Title,{level:5,style:{margin:"0 0 0px 0",width:50,fontWeight:"bold"},children:[Math.round(n.progress),"%"]})]}),e.jsx(B.Text,{style:{textAlign:"center",marginBottom:25,fontWeight:"bold"},children:`Exporting to ${n.progress}%, Please Wait...`})]}),n.lastResults&&e.jsx(te,{style:{margin:"45px 0 0 0",width:"calc(100% + 48px)",marginLeft:"-24px"}}),n.lastResults&&e.jsx("div",{style:{textAlign:"right",margin:"20px 0 0 0"},children:e.jsx(Xt,{filename:`devices_export_${rs(new Date().getTime()/1e3)}.csv`,data:n.lastResults??[],headers:Es,children:e.jsx(q,{type:"primary",children:o("common.download")})})})]}),n.status.includes("error")&&e.jsx(B.Text,{type:"danger",style:{display:"block",textAlign:"center",marginTop:32},children:JSON.stringify(n.error,null,2)})]})})]})},Ls=({modalProps:{isOpen:t},confirm:o,cancel:s})=>{const{t:r}=_(),n=i.useRef(null);return e.jsx(es,{isOpen:t,onClose:s,leastDestructiveRef:n,isCentered:!0,children:e.jsx(mt,{children:e.jsxs(ts,{children:[e.jsx(Lt,{children:r("commands.abort_command_title")}),e.jsx(ut,{children:r("commands.abort_command_explanation")}),e.jsxs(ss,{children:[e.jsx(rt,{ref:n,onClick:s,mr:4,children:r("common.cancel")}),e.jsx(rt,{onClick:o,colorScheme:"red",children:r("common.confirm")})]})]})})})},{Text:Mn}=B,Ps=({modalProps:{isOpen:t,onClose:o},serialNumber:s})=>{const{t:r}=_(),[n,a]=oe.useState(!1),{mutateAsync:m,isLoading:c}=gs({serialNumber:s,keepRedirector:n,onClose:o}),{isConfirmOpen:g,closeConfirm:d,closeModal:h,closeCancelAndForm:l}=ns({isLoading:c,onModalClose:o}),f=()=>{m()};return e.jsxs(e.Fragment,{children:[e.jsx(lt,{title:r("commands.factory_reset"),isModalOpen:t,onCancel:h,modalClass:"ampcon-middle-modal",footer:null,childItems:c?e.jsx("div",{style:{textAlign:"center",padding:"40px 0"},children:e.jsx(Ae,{size:"large"})}):e.jsxs(e.Fragment,{children:[e.jsx(me,{message:e.jsxs(e.Fragment,{children:[e.jsx("strong",{children:"Note:"})," Are you sure you want to factory reset this device? This action is not reversible"]}),type:"info",showIcon:!0,className:"custom-trace-alert",closable:!0,style:{marginTop:8,marginBottom:24}}),e.jsxs(D,{style:{display:"flex",flexDirection:"column"},children:[e.jsx(D.Item,{label:r("commands.keep_redirector"),children:e.jsx(pe,{checked:n,onChange:a})}),e.jsx(D.Item,{children:e.jsx(q,{size:"large",type:"primary",onClick:f,loading:c,children:r("commands.confirm_reset",{serialNumber:s})})})]})]})}),e.jsx(Ls,{modalProps:{isOpen:g,onOpen:()=>{},onClose:d},confirm:l,cancel:d})]})},Bs=(t,o=1e3)=>{let s;return(...r)=>{clearTimeout(s),s=setTimeout(()=>{t.apply(void 0,r)},o)}},Ms=({callback:t})=>{const{isOpen:o,webSocket:s,lastMessage:r}=Pt(g=>({isOpen:g.isWebSocketOpen,webSocket:g.webSocket,lastMessage:g.lastMessage})),[n,a]=i.useState([]),m=i.useCallback(g=>{if(o&&s){const d=Yt();a([...n,d]);const h={...g,id:d};s.send(JSON.stringify(h))}},[o,s,n]);return i.useEffect(()=>{var g;r&&r.type==="COMMAND"&&n.includes((g=r.data)==null?void 0:g.command_response_id)&&t(r.data)},[r,n]),i.useMemo(()=>({isOpen:o,send:m}),[m])},_s=({minLength:t=4,operatorId:o})=>{const[s,r]=i.useState(""),[n,a]=i.useState(void 0),[m,c]=i.useState([]),g=F=>{F.response.serialNumbers&&c(F.response.serialNumbers)},{isOpen:d,send:h}=Ms({callback:g}),l=i.useCallback(F=>{F.length>=t&&a({command:"serial_number_search",serial_prefix:F,operatorId:o})},[a]),f=i.useCallback(Bs(F=>{l(F)},300),[a]),j=i.useCallback(F=>{F!==s&&(r(F),f(F))},[s,f,r,a]),C=()=>{c([]),r("")};return i.useEffect(()=>{n&&h(n)},[n,d]),i.useMemo(()=>({inputValue:s,results:m,onInputChange:j,isOpen:d,resetSearch:C}),[s,m,j,d])},{Title:$s}=B,qs=({onClick:t,isDisabled:o})=>{const{t:s}=_(),{inputValue:r,results:n,onInputChange:a,isOpen:m,resetSearch:c}=_s({minLength:2}),[g,d]=i.useState(r),h=i.useCallback(()=>e.jsx($s,{level:5,style:{textAlign:"center",padding:"8px 0",margin:0},children:s("common.no_devices_found")}),[s]),l=j=>{c(),t(j)},f=j=>{d(j),(j.length===0||j.match("^[a-fA-F0-9-*]+$"))&&a(j)};return e.jsx(qe,{trigger:["click"],overlay:e.jsx(Le,{children:n.length>0?n.map(j=>e.jsx(Le.Item,{onClick:()=>l(j),children:j},j)):e.jsx(Le.Item,{disabled:!0,style:{border:"none",padding:0},children:e.jsx(h,{})})}),children:e.jsx(J,{placeholder:s("common.search"),value:g,onChange:j=>f(j.target.value),prefix:e.jsx(Bt,{style:{color:"rgba(0,0,0,.25)"}})})})},Rs={venueName:E.string,venueId:E.string},Us={venueName:"",venueId:""},Ke=({venueName:t,venueId:o})=>{const s=pt(),r=()=>{s(`/wireless/manage/Monitor#${o}`)};return t!==""&&o!==""?e.jsx("a",{href:"#",onClick:n=>{n.preventDefault(),r()},style:{textDecoration:"underline",color:"#14C9BB",cursor:"pointer"},children:t}):null};Ke.propTypes=Rs;Ke.defaultProps=Us;const zs=oe.memo(Ke),Vs={isOpen:E.bool.isRequired,onClose:E.func.isRequired,pushResult:E.instanceOf(Object)},Ws={pushResult:null},Je=({isOpen:t,onClose:o,pushResult:s})=>{const{t:r}=_();return e.jsxs(Mt,{onClose:o,isOpen:t,size:"xl",children:[e.jsx(mt,{}),e.jsxs(_t,{maxWidth:{sm:"600px",md:"700px",lg:"800px",xl:"50%"},children:[e.jsx($t,{title:r("configurations.configuration_push_result"),right:e.jsx(qt,{ml:2,onClick:o})}),e.jsxs(ut,{children:[e.jsxs(Rt,{status:(s==null?void 0:s.errorCode)!==0?"error":"success",maxWidth:"96%",children:[e.jsx(Ut,{}),(s==null?void 0:s.errorCode)===-1?s==null?void 0:s.msg:(s==null?void 0:s.errorCode)!==0?r("configurations.push_configuration_explanation",{code:(s==null?void 0:s.errorCode)??0}):r("configurations.push_success")]}),(s==null?void 0:s.errorCode)===0&&e.jsxs(e.Fragment,{children:[e.jsx(Be,{size:"md",mt:4,children:r("configurations.applied_configuration")}),e.jsx(Pe,{border:"1px",borderRadius:"5px",h:"calc(20vh)",overflowY:"auto",children:e.jsx("pre",{children:JSON.stringify(s==null?void 0:s.appliedConfiguration,null,2)})}),e.jsx(Be,{size:"md",mt:4,children:r("common.errors")}),e.jsx(Pe,{border:"1px",borderRadius:"5px",h:"calc(20vh)",overflowY:"auto",children:e.jsx("pre",{children:JSON.stringify(s==null?void 0:s.errors,null,2)})}),e.jsx(Be,{size:"md",mt:4,children:r("common.warnings")}),e.jsx(Pe,{border:"1px",borderRadius:"5px",h:"calc(20vh)",overflowY:"auto",children:e.jsx("pre",{children:JSON.stringify(s==null?void 0:s.warnings,null,2)})})]})]})]})]})};Je.propTypes=Vs;Je.defaultProps=Ws;const Gs="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAaFJREFUWEftVk1OwkAU/p5C4pINSSubITL7cgM5gXgDuIGcADgB3gBvIJ4AbiD7Vjsr28RNd5qAfWbAYqKlHX+JprObzJvvfe+bN+89wo4X7dg/CgJ/SwEhLIESRGbeLB/nSkWRaW4ZKyCk1SHQ2AA44pi66uZuYmBrnoR1afsABDPPtgETUQWAA0D5blD/bgKsAX03yFStLm0ju4RcKpgQlQrKByPw63sT0bG+lKWAPk+1I5orN+ilKZJOQFYdQunaREJTG8ayqdz7+Vv7rXIKWXXA+/pNv77oKUpzvlIsD13ImkOIxxzTMMls0bCmAF0pLzjX90XDPiPCie8GrdVe1hxw3MeSe0qFKstHPoGGNSCiPoMvlBt2dS2gMukfscn0zQ9ZcF07FNIaE6jDzEPlhYOCQKFAocD/UkBHo/89g2e6LqwLjz0ioM2Lh6aeBX60DuRVzjWhHyxEv0/g6LBNe3wJIGLmd90stcUS6bFNDy8t5YVbBxijZvTy5lMAq3nAdBFjcusFp3n2uc0oAfhoe86LPME1JpAXyWfPCwKFAs8Li4swnaqJqQAAAABJRU5ErkJggg==",Ks=({siteId:t,value:o,onChange:s})=>{const{t:r}=_(),[n,a]=i.useState([]),[m,c]=i.useState(!1),[g,d]=i.useState(""),[h,l]=i.useState(!1),[f]=D.useForm(),[j,C]=i.useState(!1);i.useEffect(()=>{t!=null&&O(g)},[t,g]);const O=async(N="")=>{try{c(!0);const u={siteId:t};N.trim()&&(u.key=N);const T=await xt(u);a(T.info||[])}catch{M.error("Failed to load labels")}finally{c(!1)}},F=async N=>{try{if(C(!0),n.some(T=>T.name===N.name)){M.error("Label name already exists");return}const u=await os({site_id:t,name:N.name});(u==null?void 0:u.status)===200?(M.success("Create label success"),O(),l(!1),f.resetFields()):M.error((u==null?void 0:u.info)||"Create label failed")}catch{M.error("Failed to create label")}finally{C(!1)}},b=(N,u)=>{u.stopPropagation();const T=n.find(L=>L.name===N);T&&ye(`Are you sure to delete this label ${N}?`,async()=>{try{const L=await as(T.id);if((L==null?void 0:L.status)===200){M.success("Delete label success");const S=(o||[]).filter(w=>w!==N);s(S),O()}}catch{M.error("Failed to delete label")}})},v=N=>{d(N)},P=N=>{s(N),g.trim()&&(d(""),O(""))},p=N=>{const u=(o||[]).includes(N.value);return e.jsxs(re,{style:{width:"100%",justifyContent:"space-between",alignItems:"center"},children:[e.jsxs(re,{children:[e.jsx(zt,{checked:u}),e.jsx("span",{className:"optionLabel",children:N.label})]}),e.jsx("span",{onClick:T=>b(N.value,T),style:{cursor:"pointer"},children:e.jsx("img",{src:Gs,alt:"Delete",style:{width:16,height:16,verticalAlign:"middle"}})})]})},y=N=>e.jsxs("div",{children:[N,e.jsx(q,{type:"link",icon:e.jsx(Vt,{}),style:{margin:"4px 0px 0px -8px",width:"calc(100% + 16px)",borderTop:"1px solid #E7E7E7"},onClick:()=>l(!0),children:r("inventory.create_label")})]}),k=n.map(N=>({key:N.id,value:N.name,label:N.name}));return e.jsxs(e.Fragment,{children:[e.jsx(K,{mode:"multiple",style:{width:"100%"},value:o||[],options:k,onChange:P,onSearch:v,loading:m,dropdownRender:y,optionRender:p,disabled:t==null,filterOption:!1,showSearch:!0,popupClassName:"mySelect"}),e.jsx(he,{open:h,title:r("inventory.create_label"),onCancel:()=>{l(!1),f.resetFields()},onFinish:F,form:f,modalClass:"ampcon-middle-modal",children:e.jsx(D.Item,{name:"name",label:r("common.name"),rules:[{required:!0,message:r("form.required")},{validator:(N,u)=>u?/^[a-zA-Z0-9]+$/.test(u)?Promise.resolve():Promise.reject(r("form.invalid_label_name")):Promise.resolve()}],children:e.jsx(J,{})})})]})},{Option:$e}=K,Js={refresh:E.func.isRequired,entityId:E.string,subId:E.string,deviceClass:E.string,venueId:E.string},Hs={entityId:"",subId:"",deviceClass:"",venueId:""},He=({refresh:t,entityId:o,venueId:s,subId:r,deviceClass:n})=>{const{t:a}=_(),[m]=D.useForm(),[c,g]=i.useState(!1),[d,h]=i.useState(!1),[l,f]=i.useState([]),[j,C]=i.useState([]),[O,F]=i.useState(!!s),{data:b}=Ue(),{data:v}=ze();i.useEffect(()=>{b&&f(b)},[b]),i.useEffect(()=>{v&&C(v)},[v]),i.useEffect(()=>{const u=s!=null&&s!=="";F(u),m.setFieldsValue({entity:u?`ven:${s}`:""})},[s,m]);const P=xe(u=>X.post(`inventory/${u.serialNumber}`,u)),p=u=>{var T;return{serialNumber:u.serialNumber.toLowerCase(),name:u.name,deviceRules:u.deviceRules,deviceType:u.deviceType,devClass:n!==""?n:u.devClass,description:u.description||void 0,notes:u.note?[{note:u.note}]:void 0,entity:u.entity===""||u.entity.split(":")[0]!=="ent"?"":u.entity.split(":")[1],venue:u.entity===""||u.entity.split(":")[0]!=="ven"?"":u.entity.split(":")[1],doNotAllowOverrides:u.doNotAllowOverrides,subscriber:r!==""?r:"",labelsName:(T=u.labelsName)!=null&&T.length?u.labelsName.join(","):""}},y=async u=>{try{h(!0);const T={...u,deviceRules:{rrm:"inherit",rcOnly:"inherit",firmwareUpgrade:"inherit"},devClass:n||"any"},L=p(T);await P.mutateAsync(L,{onSuccess:()=>{M.success(a("crud.success_create_obj",{obj:a("certificates.device")})),t(),g(!1),m.resetFields()},onError:S=>{var w,$;M.error(a("crud.error_create_obj",{obj:a("certificates.device"),e:($=(w=S==null?void 0:S.response)==null?void 0:w.data)==null?void 0:$.ErrorDescription}))}})}finally{h(!1)}},k=()=>{m.resetFields(),g(!1)},N={serialNumber:"",name:"",description:"",deviceType:l[0]||"",deviceRules:{rrm:"inherit",rcOnly:"inherit",firmwareUpgrade:"inherit"},devClass:n||"any",note:"",entity:s!==""&&s!==void 0&&s!==null?`ven:${s}`:"",doNotAllowOverrides:!1,labelsName:""};return e.jsxs(e.Fragment,{children:[e.jsxs(q,{type:"primary",onClick:()=>g(!0),children:[e.jsx(ve,{component:Ve}),a("common.create")]}),e.jsx(he,{open:c,title:a("common.create"),onCancel:k,onFinish:y,initialValues:N,form:m,modalClass:"ampcon-middle-modal",children:e.jsxs(Q,{className:"CreateTagModal",children:[e.jsx(R,{xs:24,children:e.jsx(D.Item,{name:"serialNumber",label:a("inventory.serial_number"),rules:[{required:!0,message:a("form.required")},{validator:(u,T)=>T&&(T.length!==12||!/^[a-fA-F0-9]+$/.test(T))?Promise.reject(new Error(a("inventory.invalid_serial_number"))):Promise.resolve()}],children:e.jsx(J,{style:{width:"100%"}})})}),e.jsx(R,{xs:24,children:e.jsx(D.Item,{name:"name",label:a("common.name"),rules:[{required:!0,message:a("form.required")},{validator:(u,T)=>T?/^([a-zA-Z0-9]{1,2}|[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])$/.test(T)?Promise.resolve():Promise.reject(new Error(a("inventory.invalid_device_name"))):Promise.resolve()}],children:e.jsx(J,{style:{width:"100%"}})})}),e.jsx(R,{xs:24,children:e.jsx(D.Item,{name:"deviceType",label:a("inventory.device_type"),rules:[{required:!0,message:a("form.required")}],children:e.jsx(K,{style:{width:"100%"},children:l.map(u=>e.jsx($e,{value:u,children:u},u))})})}),e.jsx(R,{xs:24,children:e.jsx(D.Item,{name:"entity",label:a("inventory.site"),children:e.jsxs(K,{disabled:O,style:{width:"100%"},children:[e.jsx($e,{value:"",children:a("common.none")}),j.map(u=>e.jsx($e,{value:`ven:${u.id}`,children:`${u.name}${u.description?`: ${u.description}`:""}`},`ven:${u.id}`))]})})}),e.jsx(R,{xs:24,children:e.jsx(D.Item,{noStyle:!0,shouldUpdate:(u,T)=>u.entity!==T.entity,children:({getFieldValue:u})=>{const T=u("entity"),L=T!=null&&T.startsWith("ven:")?parseInt(T.split(":")[1]):null;return e.jsx(D.Item,{name:"labelsName",label:a("inventory.label"),children:e.jsx(Ks,{siteId:L,value:m.getFieldValue("labelsName")||[],onChange:S=>m.setFieldsValue({labelsName:S}),className:"custom-label-select",style:{width:"100%"}})})}})}),e.jsx(R,{xs:24,children:e.jsx(D.Item,{name:"doNotAllowOverrides",label:a("overrides.ignore_overrides"),valuePropName:"checked",style:{display:"none"},rules:[{required:!0,message:a("form.required")}],children:e.jsx(pe,{})})}),e.jsx(R,{xs:24,children:e.jsx(D.Item,{name:"description",label:a("common.description"),children:e.jsx(J,{style:{width:"100%"}})})}),e.jsx(R,{xs:24,children:e.jsx(D.Item,{name:"note",label:a("common.note"),children:e.jsx(J,{style:{width:"100%"}})})})]})})]})};He.propTypes=Js;He.defaultProps=Hs;const Qs=({value:t,onChange:o})=>{const{t:s}=_(),{data:r}=Ue(),{data:n}=ze(),[a,m]=i.useState([]);i.useEffect(()=>{(async()=>{if(!(t!=null&&t.venue)){m([]);return}try{const h=await xt({siteId:parseInt(t.venue)});m(((h==null?void 0:h.info)||[]).map(l=>({label:l.name,value:l.name})))}catch{m([])}})()},[t==null?void 0:t.venue]);const c=t!=null&&t.labelsName?t.labelsName.split(",").filter(Boolean):[],g=(d,h)=>{Array.isArray(h.labelsName)&&(h.labelsName=h.labelsName.join(",")),o(h)};return e.jsxs(D,{initialValues:{...t,labelsName:c},onValuesChange:g,className:"wirelessForm InventoryMainForm",children:[e.jsxs(Q,{gutter:24,children:[e.jsx(R,{span:12,children:e.jsx(D.Item,{label:s("inventory.serial_number"),name:"serialNumber",rules:[{required:!0,message:s("form.required")}],children:e.jsx(J,{disabled:!0})})}),e.jsx(R,{span:12,children:e.jsx(D.Item,{label:s("common.name"),name:"name",rules:[{required:!0,message:s("form.required")},{type:"string",max:50,message:s("form.max_length",{max:50})}],children:e.jsx(J,{})})})]}),e.jsxs(Q,{gutter:24,children:[e.jsx(R,{span:12,children:e.jsx(D.Item,{label:s("common.description"),name:"description",rules:[{max:128,message:s("form.max_length",{max:128})}],children:e.jsx(J,{})})}),e.jsx(R,{span:12,children:e.jsx(D.Item,{label:s("inventory.device_type"),name:"deviceType",rules:[{required:!0,message:s("form.required")}],children:e.jsx(K,{options:r.map(d=>({label:d,value:d})),showSearch:!0})})})]}),e.jsxs(Q,{gutter:24,children:[e.jsx(R,{span:12,children:e.jsx(D.Item,{label:"Site",name:"venue",children:e.jsx(K,{options:Array.isArray(n)?n.map(d=>({label:d.name,value:String(d.id),key:d.id})):[],showSearch:!0,optionFilterProp:"label"})})}),e.jsx(R,{span:12,children:e.jsx(D.Item,{label:"Label",name:"labelsName",children:e.jsx(K,{mode:"multiple",options:a,showSearch:!0,optionFilterProp:"label"})})})]}),e.jsx(Q,{gutter:24,style:{display:"none"},children:e.jsx(R,{span:12,children:e.jsx(D.Item,{label:s("overrides.ignore_overrides"),name:"doNotAllowOverrides",valuePropName:"checked",rules:[{required:!0,message:s("form.required")}],children:e.jsx(pe,{})})})})]})},{Title:Zs}=B,Ys=({serialNumber:t})=>{const{t:o}=_(),{data:s,isFetching:r,refetch:n}=Wt({serialNumber:t,enabled:!0});return r?e.jsx(Ae,{}):!s||s.config==="none"?e.jsx(me,{message:o("inventory.no_computed"),type:"info",showIcon:!0,style:{color:"#1677ff",borderColor:"#1677ff"}}):e.jsxs("div",{children:[e.jsxs(Gt,{style:{flexDirection:"column",marginBottom:8},children:[e.jsx(Zs,{level:5,style:{margin:0},children:"Configuration"}),e.jsx(q,{icon:e.jsx(ve,{component:ht}),size:"small",loading:r,onClick:()=>n(),style:{width:100,height:32,marginTop:20,marginBottom:10},children:"Refresh"})]}),e.jsx("div",{style:{border:"1px solid #eee",borderRadius:16,height:"30vh",overflowY:"auto",marginBottom:24},children:e.jsx("pre",{style:{margin:0,padding:8},children:JSON.stringify(s.config,null,2)})})]})},Xs={"2G":[1,6,11],"5G":{40:[36,44,52,60,100,108,116,124,132,149,157,165,173,184,192],80:[36,52,100,116,132,149]},"5G-lower":{20:[36,40,44,48,52,56,60,64,100,104,108,112,116,120,124,128,132,136,140,144],40:[38,46,54,63,102,110,118,126,134,142],80:[42,58,106,122,138],160:[50,114]},"5G-upper":{20:[149,153,157,161,165],40:[151,159],80:[155]},"6G":{20:[1,5,9,13,17,21,25,29,33,37,41,45,49,53,57,61,65,69,73,77,81,85,89,93,97,101,105,109,113,117,121,125,129,133,137,141,145,149,153,157,161,165,169,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,233],40:[3,11,19,27,35,43,51,59,67,75,83,91,99,107,115,123,131,139,147,155,163,171,17,187,195,203,211,219,227],80:[7,23,39,55,71,87,103,119,135,151,167,183,199,215],160:[15,47,79,143]}},en=()=>{const t=[];for(const[,o]of Object.entries(Xs))if(Array.isArray(o))for(const s of o){const r=s.toString();t.find(n=>n.value===r)||t.push({value:r,label:r})}else for(const[,s]of Object.entries(o))for(const r of s){const n=r.toString();t.find(a=>a.value===n)||t.push({value:n,label:n})}return t.sort((o,s)=>parseInt(o.value,10)-parseInt(s.value,10))},Y={radios:{channel:{name:"channel",label:"overrides.channel",type:"select",defaultValue:"auto",options:[{value:"auto",label:"Auto"},...en()],test:()=>{}},"tx-power":{name:"tx-power",label:"overrides.tx_power",type:"integer",defaultValue:10,test:t=>t<1||t>32?"overrides.tx_power_error":void 0}}},tn=async t=>X.get(`configurationOverrides/${t}`).then(({data:o})=>o).catch(o=>{var s;if(((s=o.response)==null?void 0:s.status)===404)return{serialNumber:t,managementPolicy:"",overrides:[]};throw o}),sn=async({data:t,source:o})=>X.put(`configurationOverrides/${t.serialNumber}?source=${o}`,t),nn=()=>xe(sn),rn=({serialNumber:t,overrides:o,setOverrides:s})=>{const{t:r}=_(),[n,a]=i.useState(null),[m,c]=i.useState(!1),[g,d]=i.useState(!1),[h,l]=i.useState(!1),[f,j]=i.useState(null),[C,O]=i.useState(""),[F,b]=i.useState(""),[v,P]=i.useState({value:"",reason:""}),{user:p}=Te(),[y,k]=i.useState(1),[N,u]=i.useState(10),T=i.useCallback(x=>{k(x.current),u(x.pageSize)},[]),L=[{label:"radios",value:"radios"}],S=[{label:"2G",value:"2G"},{label:"5G",value:"5G"},{label:"6G",value:"6G"}],w=[{label:"channel",value:"channel"},{label:"tx-power",value:"tx-power"}],$={startName:"radios",nameIndex:"2G",endName:"channel",value:Y.radios.channel.defaultValue,reason:""},[z,ue]=i.useState({...$}),[Z]=D.useForm(),H=i.useCallback(x=>`${x.startName}.${x.nameIndex}.${x.endName}`,[]),ae=i.useCallback(()=>{const x=o.map(I=>I.parameterName);return{startName:[{required:!0,message:r("form.required")},{validator:async()=>{const I=H(z);if(x.includes(I))throw new Error("")}}],nameIndex:[{required:!0,message:r("form.required")},{validator:async()=>{const I=H(z);if(x.includes(I))throw new Error("")}}],endName:[{required:!0,message:r("form.required")},{validator:async()=>{const I=H(z);if(x.includes(I))throw new Error(r("overrides.name_error"))}}],value:[{required:!0,message:r("form.required")},{validator:async(I,W)=>{var V;if(z.endName&&W!==void 0){const je=(V=Y.radios[z.endName])==null?void 0:V.test;if(je){const ge=je(W);if(ge)throw new Error(r(ge)||ge)}}}}],reason:[{max:64,message:r("overrides.reason_error")}]}},[z,o,r,H]),ne=i.useCallback((x,I)=>{ue(W=>{const V={...W,[I]:x};return I==="endName"&&(x==="channel"?(V.value=Y.radios.channel.defaultValue,Z.setFieldValue("value",V.value)):x==="tx-power"&&(V.value=Y.radios["tx-power"].defaultValue,Z.setFieldValue("value",V.value))),V})},[Z]);oe.useEffect(()=>{Z.validateFields(["startName","nameIndex","endName"])},[z.startName,z.nameIndex,z.endName,Z]);const ie=i.useCallback(async x=>{const V=[{parameterName:`${x.startName}.${x.nameIndex}.${x.endName}`,parameterValue:x.value,source:p==null?void 0:p.userRole,reason:x.reason,modified:Math.floor(new Date().getTime()/1e3),parameterType:x.endName==="tx-power"?"integer":"string"},...o];s(V),d(!1),ue({...$}),Z.resetFields()},[o,p==null?void 0:p.userRole,Z,s]),ke=i.useCallback(async x=>{if(!f)return;const I=o.map(W=>W.parameterName===f.parameterName&&W.source===f.source?{...W,parameterValue:C,reason:F}:W);s(I),l(!1)},[f,C,F,o,s,r]),De=i.useCallback(x=>{ye("Are you sure you want to delete this Configuration Override?",()=>{const I=o.filter(W=>W.parameterName!==x.parameterName||W.source!==x.source);s(I)})},[o,s,r]);i.useEffect(()=>{let x=!0;return t&&(c(!0),tn(t).then(I=>{x&&(a(I),I!=null&&I.overrides&&s(I.overrides))}).finally(()=>{x&&c(!1)})),()=>{x=!1}},[t,s]);const Ee=[{title:r("overrides.source"),dataIndex:"source",key:"source",sorter:(x,I)=>(x.source||"").localeCompare(I.source||"")},{title:r("common.name"),dataIndex:"parameterName",key:"parameterName",sorter:(x,I)=>(x.parameterName||"").localeCompare(I.parameterName||"")},{title:r("overrides.value"),dataIndex:"parameterValue",key:"parameterValue",sorter:(x,I)=>(x.parameterValue||"").toString().localeCompare((I.parameterValue||"").toString())},{title:r("overrides.reason"),dataIndex:"reason",key:"reason",sorter:(x,I)=>(x.reason||"").localeCompare(I.reason||"")},{title:"Operation",key:"action",render:(x,I)=>e.jsxs(re,{size:24,children:[e.jsx(q,{type:"text",style:{padding:0},onClick:()=>{j(I),O(I.parameterValue),b(I.reason),P({value:"",reason:""}),l(!0)},children:r("crud.edit")}),e.jsx(q,{type:"text",style:{padding:0},onClick:()=>De(I),children:r("crud.delete")})]})}],Oe=i.useCallback((x,I,W)=>W==="channel"?e.jsx(K,{value:x,style:{minWidth:100},onChange:I,children:Y.radios.channel.options.map(V=>e.jsx(K.Option,{value:V.value,children:V.label},V.value))}):W==="tx-power"?e.jsx(ot,{value:x,min:Y.radios["tx-power"].min,max:Y.radios["tx-power"].max,style:{minWidth:100},onChange:I}):e.jsx(J,{value:x,style:{minWidth:100},onChange:V=>I(V.target.value)}),[]);return e.jsxs("div",{children:[e.jsx(re,{style:{marginBottom:20},children:e.jsx(q,{type:"primary",icon:e.jsx(ve,{component:Ve}),onClick:()=>d(!0),children:"Create"})}),m?e.jsx(Ae,{}):e.jsx(Ie,{columns:Ee,dataSource:o,rowKey:x=>x.parameterName+x.source+(x.modified||""),pagination:{current:y,pageSize:N,showTotal:x=>`Total ${x} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["5","10","20","50"],onChange:T},size:"small",scroll:{y:300}}),e.jsx(he,{open:h,title:"Edit",onCancel:()=>l(!1),onFinish:ke,modalClass:"ampcon-middle-modal configOverridesForm",children:f&&e.jsxs(e.Fragment,{children:[e.jsx(Q,{gutter:24,children:e.jsx(R,{span:18,children:e.jsx(D.Item,{label:r("overrides.parameter"),children:e.jsx(J,{value:f.parameterName,type:"hidden"})})})}),e.jsx(Q,{gutter:24,style:{marginBottom:16,marginTop:-20},children:e.jsx(R,{span:18,children:f.parameterName})}),e.jsx(Q,{gutter:24,children:e.jsx(R,{span:18,children:e.jsxs(D.Item,{label:r("overrides.value"),required:!0,children:[Oe(C,O,f.parameterName.split(".").pop()||""),v.value&&e.jsx("div",{style:{color:"red",fontSize:12},children:v.value})]})})}),e.jsx(Q,{gutter:24,children:e.jsx(R,{span:18,children:e.jsxs(D.Item,{label:r("overrides.reason"),children:[e.jsx(J.TextArea,{value:F,rows:2,onChange:x=>b(x.target.value)}),v.reason&&e.jsx("div",{style:{color:"red",fontSize:12},children:v.reason})]})})})]})}),e.jsxs(he,{open:g,title:"Create",onCancel:()=>{d(!1),ue({...$}),Z.resetFields()},onFinish:ie,initialValues:$,form:Z,modalClass:"ampcon-middle-modal configOverridesForm",children:[e.jsx(Q,{gutter:24,children:e.jsx(R,{span:18,children:e.jsx(D.Item,{label:r("overrides.parameter"),required:!0,children:e.jsxs(re.Compact,{block:!0,children:[e.jsx(D.Item,{noStyle:!0,name:"startName",rules:ae().startName,children:e.jsx(K,{options:L,style:{width:88,marginRight:8},onChange:x=>ne(x,"startName")})}),e.jsx(D.Item,{noStyle:!0,name:"nameIndex",rules:ae().nameIndex,children:e.jsx(K,{options:S,style:{width:78,marginRight:8},onChange:x=>ne(x,"nameIndex")})}),e.jsx(D.Item,{noStyle:!0,name:"endName",rules:ae().endName,children:e.jsx(K,{options:w,style:{width:98},onChange:x=>ne(x,"endName")})})]})})})}),e.jsx(Q,{gutter:24,children:e.jsx(R,{span:18,children:e.jsx(D.Item,{label:r("overrides.value"),name:"value",rules:ae().value,required:!0,children:z.endName==="channel"?e.jsx(K,{onChange:x=>ne(x,"value"),children:Y.radios.channel.options.map(x=>e.jsx(K.Option,{value:x.value,children:x.label},x.value))}):z.endName==="tx-power"?e.jsx(ot,{min:Y.radios["tx-power"].min,max:Y.radios["tx-power"].max,onChange:x=>ne(x,"value")}):null})})}),e.jsx(Q,{gutter:24,children:e.jsx(R,{span:18,children:e.jsx(D.Item,{label:r("overrides.reason"),name:"reason",rules:ae().reason,children:e.jsx(J.TextArea,{rows:2,onChange:x=>ne(x.target.value,"reason")})})})})]})]})},on=({oldNotes:t,setNotes:o})=>{const{t:s}=_(),{user:r}=Te(),[n,a]=i.useState(""),[m,c]=i.useState(!1),[g,d]=i.useState([...t||[]]),[h,l]=i.useState([]),f=()=>{if(!n)return;const b={note:n,isNew:!0,createdBy:r.email,created:Math.floor(new Date().getTime()/1e3)};l(v=>{const P=[...v,b];return o(P),P}),d(v=>[...v,b]),a(""),c(!1)},j=[{title:"Date",dataIndex:"created",key:"created",render:b=>b?new Date(b*1e3).toLocaleString():"",sorter:(b,v)=>b.created-v.created},{title:"Note",dataIndex:"note",key:"note",sorter:(b,v)=>(b.note||"").localeCompare(v.note||"")},{title:"By",dataIndex:"createdBy",key:"createdBy",sorter:(b,v)=>(b.createdBy||"").localeCompare(v.createdBy||"")}],[C,O]=i.useState({current:1,pageSize:10}),F=g.sort((b,v)=>v.created-b.created).slice((C.current-1)*C.pageSize,C.current*C.pageSize);return e.jsxs("div",{children:[e.jsx(re,{style:{marginBottom:20},children:e.jsx(q,{type:"primary",icon:e.jsx(ve,{component:Ve}),onClick:()=>c(!0),children:"Create Note"})}),e.jsx(Ie,{columns:j,dataSource:F,rowKey:()=>be(),pagination:{current:C.current,pageSize:C.pageSize,total:g.length,onChange:(b,v)=>O({current:b,pageSize:v}),showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:[5,10,20,50],showTotal:b=>`Total ${b} items`},size:"small",scroll:{y:200},className:"NoteTable"}),e.jsx(he,{open:m,title:"Create Note ",onCancel:()=>{c(!1),a("")},onFinish:f,modalClass:"ampcon-middle-modal",children:e.jsx(D.Item,{label:"Note",name:"note",rules:[{required:!0,message:s("form.required")}],children:e.jsx(J.TextArea,{value:n,onChange:b=>a(b.target.value),style:{width:220}})})})]})},an=({tag:t,refresh:o,onClose:s,open:r=!0})=>{const[n,a]=i.useState("main"),[m,c]=i.useState({...t}),[g,d]=i.useState([]),[h,l]=i.useState([]);oe.useEffect(()=>{a("main"),c({...t}),d([]),l([])},[t]);const f=xe(O=>X.put(`inventory/${t==null?void 0:t.serialNumber}`,O)),j=nn(),C=async()=>{var O;await f.mutateAsync({...m,devClass:"any",notes:g}),h&&h.length>0&&await j.mutateAsync({data:{serialNumber:t==null?void 0:t.serialNumber,overrides:h,managementPolicy:(t==null?void 0:t.managementPolicy)||""},source:((O=h[0])==null?void 0:O.source)||"User"}),M.success("Update successfully"),o(),s()};return e.jsxs(Se,{open:r,onCancel:s,destroyOnClose:!0,title:e.jsxs("div",{children:[`Edit ${t==null?void 0:t.serialNumber}`," ",e.jsx(te,{style:{marginTop:8,marginBottom:0}})]}),footer:e.jsxs(e.Fragment,{children:[e.jsx(te,{}),e.jsxs("div",{className:"foot-btns",children:[e.jsx(q,{onClick:s,children:"Cancel"}),e.jsx(q,{type:"primary",onClick:C,children:"Apply"})]})]}),className:"ampcon-max-modal",children:[e.jsxs(fe.Group,{value:n,onChange:O=>a(O.target.value),className:"inventoryEditFormTabs",style:{marginTop:8},children:[e.jsx(fe.Button,{value:"main",children:"Main"}),e.jsx(fe.Button,{value:"computed",children:"Computed Config"}),e.jsx(fe.Button,{value:"overrides",children:"Config Overrides"}),e.jsx(fe.Button,{value:"notes",children:"Notes"})]}),e.jsxs("div",{style:{marginTop:24},children:[e.jsx("div",{style:{display:n==="main"?"block":"none"},children:e.jsx(Qs,{value:m,onChange:c},(t==null?void 0:t.serialNumber)+"-main")}),e.jsx("div",{style:{display:n==="computed"?"block":"none"},children:e.jsx(Ys,{serialNumber:t==null?void 0:t.serialNumber},(t==null?void 0:t.serialNumber)+"-computed")}),e.jsx("div",{style:{display:n==="overrides"?"block":"none"},children:e.jsx(rn,{serialNumber:t==null?void 0:t.serialNumber,overrides:h,setOverrides:l},(t==null?void 0:t.serialNumber)+"-overrides")}),e.jsx("div",{style:{display:n==="notes"?"block":"none"},children:e.jsx(on,{oldNotes:t==null?void 0:t.notes,setNotes:d},(t==null?void 0:t.serialNumber)+"-notes")})]})]})};function ln(t,o){return se.parse(t,o)}function cn(t,o){se.parse(t,Object.assign({},{download:!0},o))}function dn(t,o){return o===void 0&&(o={}),se.unparse(t,o)}function mn(){return{readString:ln,readRemoteFile:cn,jsonToCSV:dn}}se.BAD_DELIMITERS;se.RECORD_SEP;se.UNIT_SEP;se.WORKERS_SUPPORTED;se.LocalChunkSize;se.DefaultDelimiter;const un="/ampcon/wireless";function pn(){return We({url:`${un}/inventory/import_template`,method:"GET",responseType:"blob"})}const hn=t=>t.replace(/"/g,"").trim(),xn=async t=>new Promise(o=>{const s=new FileReader;s.readAsText(t),s.onload=({target:{result:r=null}})=>o(r),s.onerror=()=>o(null)}),gn={setPhase:E.func.isRequired,setDevices:E.func.isRequired,setIsCloseable:E.func.isRequired,refreshId:E.string.isRequired},ft=({setPhase:t,setDevices:o,setIsCloseable:s,refreshId:r})=>{const{t:n}=_(),[a,m]=i.useState(null),{readString:c}=mn(),g=async l=>{m({isLoading:!0});const f=await xn(l);if(f===null)m({error:n("generalErrorParsingFile")||"General error while parsing file"});else{const C=c(f,{header:!0,transformHeader:hn,skipEmptyLines:!0,quoteChar:'"'});C.errors.length>0?m({error:`Error on row ${C.errors[0].row}: ${C.errors[0].message}`}):m({deviceList:C.data})}},d=l=>{var f;s(!1),((f=l.target.files)==null?void 0:f.length)>0&&g(l.target.files[0])},h=l=>{l.preventDefault(),pn().then(f=>{const j=window.URL.createObjectURL(f),C=document.createElement("a");C.href=j,C.download="inventory_import_template.csv",document.body.appendChild(C),C.click(),C.remove(),window.URL.revokeObjectURL(j)}).catch(f=>{if(f.response&&f.response.data){const j=new FileReader;j.onload=function(){try{const C=JSON.parse(j.result);M.error(C.info||j.result)}catch{M.error(j.result)}},j.readAsText(f.response.data)}else M.error(f.message||"Network Error")})};return e.jsxs("div",{style:{padding:"0 0 0 0"},children:[e.jsx("style",{jsx:!0,global:!0,children:`
        .custom-alert .ant-alert-icon {
          color: #367EFF !important;
        }
      `}),e.jsx(me,{className:"custom-trace-alert",message:"Note: "+n("devices.import_device_warning"),type:"info",showIcon:!0,closable:!0,style:{marginBottom:30}}),e.jsxs("div",{style:{marginBottom:"30px",fontSize:"14px",textAlign:"left",paddingLeft:0},children:[e.jsx(B.Text,{children:"To bulk import devices, you need to use a CSV file with the following columns: "}),e.jsx(B.Text,{strong:!0,children:"SerialNumber, DeviceType, Name, Label, Description, Note. "}),e.jsx(B.Text,{children:"Download device import template "}),e.jsx(B.Text,{}),e.jsxs("a",{href:"#",onClick:h,style:{color:"#14C9BB",textDecoration:"underline"},children:["(","template.csv",")"]}),e.jsx(B.Text,{})]}),e.jsx(J,{style:{width:"25%",marginBottom:"20px",textAlign:"left",paddingLeft:5},type:"file",accept:".csv",onChange:d,placeholder:n("noFileSelected")||"No file selected"},r),(a==null?void 0:a.error)&&e.jsx(me,{message:a.error,type:"error",showIcon:!0,style:{marginTop:"20px"}}),e.jsx("br",{}),e.jsx(q,{type:"primary",style:{marginTop:"20px",textAlign:"left",width:"136px"},onClick:()=>{o(a.deviceList),t(1)},disabled:!(a!=null&&a.deviceList),children:n("devices.test_batch")||"Test Import Data"})]})};ft.propTypes=gn;const fn={devices:E.shape({newDevices:E.instanceOf(Array).isRequired,devicesToUpdate:E.instanceOf(Array).isRequired}).isRequired,refresh:E.func.isRequired,deviceClass:E.string.isRequired,parent:E.instanceOf(Object).isRequired,setIsCloseable:E.func.isRequired,venueId:E.string.isRequired,handleRefresh:E.func.isRequired},Ce=({dataSource:t,columns:o,showPagination:s=!0,rowKey:r="SerialNumber"})=>{const{t:n}=_(),a=s?{total:t.length,pageSizeOptions:["10","20","30","40","50"],showSizeChanger:!0,showQuickJumper:!0,showTotal:(m,c)=>`${c[0]}-${c[1]} of ${m} items`}:!1;return e.jsx(Ie,{style:{maxHeight:"200px",overflow:"auto"},dataSource:t,columns:o,pagination:a,rowKey:r,siaze:"small"})},yt=({devices:t,refresh:o,deviceClass:s,parent:r,setIsCloseable:n,venueId:a,handleRefresh:m})=>{const{t:c}=_(),{token:g}=Te(),[d,h]=i.useState({isLoading:!1}),l=(b,v)=>{const P={headers:{Accept:"application/json",Authorization:`Bearer ${g}`},cancelToken:v.token};return ee.post(`${X.defaults.baseURL}/inventory/${b.serialNumber}`,b,P).then(()=>({success:!0})).catch(p=>{var y,k;return ee.isCancel(p)?{success:!1,stop:!0}:{success:!1,error:((k=(y=p.response)==null?void 0:y.data)==null?void 0:k.ErrorDescription)??"Unknown Error"}})},f=(b,v)=>{const P={headers:{Accept:"application/json",Authorization:`Bearer ${g}`},cancelToken:v.token};return ee.put(`${X.defaults.baseURL}/inventory/${b.serialNumber}`,b,P).then(()=>({success:!0})).catch(p=>{var y,k;return ee.isCancel(p)?{success:!1,stop:!0}:{success:!1,error:((k=(y=p.response)==null?void 0:y.data)==null?void 0:k.ErrorDescription)??"Unknown Error"}})},j=async(b,v)=>{const P={headers:{Accept:"application/json",Authorization:`Bearer ${g}`}},p={site_id:b,name:v};try{return(await ee.post("/ampcon/wireless/site/labels",p,P)).data}catch(y){return console.error("Failed to create labels:",y),{status:500,info:"Failed to create labels"}}},C=async b=>{h({isLoading:!0});const v={entity:"",venue:a=="all"?"":a,subscriber:"",devClass:s,...r},P=t.newDevices.length+t.devicesToUpdate.length,p=[],y=[],k=[],N=[],u=new Set,T=L=>{var w;const S=(w=L.Label)==null?void 0:w.replace(/\,/g,"$");u.add(S)};if(t.newDevices.forEach(T),t.devicesToUpdate.forEach(T),u.size>0&&a!=="all"){const L=Array.from(u);await j(a,L)}for(let L=0;L<t.newDevices.length;L+=1){const S=t.newDevices[L];h({isLoading:!0,treating:S.SerialNumber,percentTreated:Math.floor(Math.max(L-1,0)/P*100)});const w={...v,serialNumber:S.SerialNumber,deviceType:S.DeviceType,name:S.Name.length>0?S.Name:S.SerialNumber,description:S.Description,notes:S.Note!==""?[{note:S.Note}]:void 0,labelsName:S.Label},$=await l(w,b);if($.stop)break;$.success?p.push(S):$.success||y.push({...S,error:$.error})}for(let L=0;L<t.devicesToUpdate.length;L+=1){const S=t.devicesToUpdate[L];h({isLoading:!0,treating:S.SerialNumber,percentTreated:Math.floor(Math.max(L-1,0)/P*100)});const w={...v,serialNumber:S.SerialNumber,name:S.Name.length>0?S.Name:S.SerialNumber,labelsName:S.Label,description:S.Description,notes:S.Note!==""?[{note:S.Note}]:void 0},$=await f(w,b);if($.stop)break;$.success?k.push(S):$.success||N.push({...S,error:$.error})}h({isLoading:!1,isFinished:!0,successPost:p,errorPost:y,successPut:k,errorPut:N}),n(!0),o(),m()};i.useEffect(()=>{var P,p;const v=ee.CancelToken.source();return(((P=t==null?void 0:t.newDevices)==null?void 0:P.length)>0||((p=t==null?void 0:t.devicesToUpdate)==null?void 0:p.length)>0)&&C(v),()=>{v.cancel("axios request cancelled")}},[t]);const O=i.useMemo(()=>[{title:c("inventory.serial_number")||"Serial Number",dataIndex:"SerialNumber",key:"SerialNumber",sorter:(b,v)=>b.SerialNumber.localeCompare(v.SerialNumber)}],[c]),F=i.useMemo(()=>[...O,{title:c("common.error")||"Error",dataIndex:"error",key:"error",sorter:(b,v)=>b.error.localeCompare(v.error),render:b=>e.jsx(B.Text,{type:"danger",children:b})}],[O,c]);return d!=null&&d.isLoading?e.jsxs("div",{style:{padding:"24px",marginTop:"130px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx(Re,{percent:(d==null?void 0:d.percentTreated)??0,showInfo:!1,style:{flex:1,marginBottom:0},status:(d==null?void 0:d.percentTreated)!==100?"active":"success",strokeColor:(d==null?void 0:d.percentTreated)===100?"#14C9BB":void 0,strokeWidth:12}),e.jsxs(B.Title,{level:5,style:{margin:"0 0 0px 0",width:50,fontWeight:"bold",marginLeft:10},children:[Math.round(d.percentTreated||0),"%"]})]}),e.jsxs(B.Title,{level:5,style:{marginTop:"16px",marginBottom:"0",fontWeight:"bold"},children:[c("devices.treating")," ",d==null?void 0:d.treating]})]}):d!=null&&d.isFinished?e.jsxs("div",{style:{padding:0},children:[d.successPost.length>0&&e.jsxs("div",{style:{border:"2px solid #36d399",borderRadius:5,padding:8,marginBottom:16},children:[e.jsxs(B.Text,{strong:!0,style:{display:"block",marginBottom:12},children:[d.successPost.length," ",c("devices.create_success")]}),e.jsx(Ce,{dataSource:d.successPost,columns:O,showPagination:!0})]}),d.errorPost.length>0&&e.jsxs("div",{style:{border:"2px solid #f87171",borderRadius:5,padding:8,marginBottom:16},children:[e.jsxs(B.Text,{strong:!0,style:{display:"block",marginBottom:12},children:[d.errorPost.length," ",c("devices.create_errors")]}),e.jsx(Ce,{dataSource:d.errorPost,columns:F,showPagination:!0})]}),d.successPut.length>0&&e.jsxs("div",{style:{border:"2px solid #36d399",borderRadius:5,padding:8,marginBottom:16},children:[e.jsxs(B.Text,{strong:!0,style:{display:"block",marginBottom:12},children:[d.successPut.length," ",c("devices.update_success")]}),e.jsx(Ce,{dataSource:d.successPut,columns:O,showPagination:!0})]}),d.errorPut.length>0&&e.jsxs("div",{style:{border:"2px solid #f87171",borderRadius:5,padding:8,marginBottom:16},children:[e.jsxs(B.Text,{strong:!0,style:{display:"block",marginBottom:12},children:[d.errorPut.length," ",c("devices.update_error")]}),e.jsx(Ce,{dataSource:d.errorPut,columns:F,showPagination:!0})]})]}):null};yt.propTypes=fn;const yn={setPhase:E.func.isRequired,setDevicesToImport:E.func.isRequired,devicesToTest:E.arrayOf(E.instanceOf(Object)).isRequired,venueId:E.string.isRequired},we=({dataSource:t,columns:o,showPagination:s=!0,rowKey:r="SerialNumber"})=>{const{t:n}=_(),a=s?{total:t.length,pageSizeOptions:["10","20","30","40","50"],showSizeChanger:!0,showQuickJumper:!0,showTotal:(m,c)=>`${c[0]}-${c[1]} of ${m} items`}:!1;return e.jsx(Ie,{dataSource:t,columns:o,pagination:a,rowKey:r,style:{maxHeight:"200px",overflow:"auto"},siaze:"small"})},bt=({devicesToTest:t,setPhase:o,setDevicesToImport:s,venueId:r})=>{const{t:n}=_(),{token:a}=Te(),{data:m}=Ue(),[c,g]=i.useState(!1),[d,h]=i.useState(!1),[l,f]=i.useState({isLoading:!1});console.log(l);const j=i.useMemo(()=>[{title:n("inventory.serial_number"),dataIndex:"SerialNumber",key:"SerialNumber",sorter:(p,y)=>p.SerialNumber.localeCompare(y.SerialNumber)},{title:n("inventory.device_type"),dataIndex:"DeviceType",key:"DeviceType",sorter:(p,y)=>p.DeviceType.localeCompare(y.DeviceType)},{title:n("Name")||"Name",dataIndex:"Name",key:"Name",sorter:(p,y)=>p.Name.localeCompare(y.Name)},{title:n("inventory.label"),dataIndex:"Label",key:"Label",sorter:(p,y)=>p.Label.localeCompare(y.Label)},{title:n("Description"),dataIndex:"Description",key:"Description",sorter:(p,y)=>p.Description.localeCompare(y.Description)},{title:n("Note"),dataIndex:"Note",key:"Note",sorter:(p,y)=>p.Note.localeCompare(y.Note)}],[n]),C=i.useMemo(()=>[{title:n("inventory.serial_number"),dataIndex:"SerialNumber",key:"SerialNumber",sorter:(p,y)=>p.SerialNumber.localeCompare(y.SerialNumber)},{title:n("inventory.device_type"),dataIndex:"DeviceType",key:"DeviceType",sorter:(p,y)=>p.DeviceType.localeCompare(y.DeviceType)},{title:n("Name"),dataIndex:"Name",key:"Name",sorter:(p,y)=>p.Name.localeCompare(y.Name)},{title:n("Label"),dataIndex:"Label",key:"Label",sorter:(p,y)=>p.Label.localeCompare(y.Label)},{title:n("common.error"),dataIndex:"error",key:"error",sorter:(p,y)=>p.error.localeCompare(y.error)}],[n]),O=(p,y)=>{const k={found:!1,alreadyAssigned:!1},N={headers:{Accept:"application/json",Authorization:`Bearer ${a}`},cancelToken:y.token};return ee.get(`${X.defaults.baseURL}/inventory/${p.SerialNumber}`,N).then(u=>(u.data.venue!==""||u.data.entity!==""||u.data.subscriber!==""?k.alreadyAssigned=!0:k.foundUnassigned=!0,k)).catch(u=>ee.isCancel(u)?{stop:!0}:k)},F=(p,y)=>{if(p.SerialNumber===""||p.SerialNumber.length!==12||!p.SerialNumber.match("^[a-fA-F0-9]+$"))return n("devices.invalid_serial_number");if(!m.find(k=>k===p.DeviceType))return n("devices.device_type_not_found");if(y.find(k=>p.SerialNumber===k))return n("devices.duplicate_serial");if(!p.Name||p.Name.trim()==="")return n("Name not found");if(p.Label){const k=p.Label.replace(/,/g,"");if(!/^[a-zA-Z0-9]+$/.test(k))return n("Invalid Label (only numbers and letters are allowed)")}return null},b=async p=>{var L;if(f({isLoading:!0}),t.length>0){const S=t[0];if(!["Serial Number","Device Type","Name","Label","Description","Note"].every(z=>Object.prototype.hasOwnProperty.call(S,z))){f({isLoading:!1,isFinished:!0,newDevices:[],foundNotAssigned:[],foundAssigned:[],fileErrors:[{"Serial Number":"","Device Type":"",Name:"",error:n("Error importing template")}]});return}}const y=[],k=[],N=[],u=[],T=[];for(let S=0;S<t.length;S+=1){const w=t[S];w.SerialNumber||(w.SerialNumber=w["Serial Number"]),w.DeviceType||(w.DeviceType=w["Device Type"]),w.Name||(w.Name=w.Name),w.Label||(w.Label=w.Label||""),w.Label&&(w.Label=(L=w.Label)==null?void 0:L.replace(/\$/g,",")),w.Description||(w.Description=w.Description||""),w.Note||(w.Note=w.Note||""),f({isLoading:!0,treating:w.SerialNumber,percentTreated:Math.floor(Math.max(S-1,0)/t.length*100)});const $=F(w,y);if($)T.push({...w,error:$});else{const z=await O(w,p);if(z.stop)break;z.alreadyAssigned?u.push(w):z.foundUnassigned?N.push(w):k.push(w)}y.push(w.SerialNumber)}f({isLoading:!1,isFinished:!0,newDevices:k,foundNotAssigned:N,foundAssigned:u,fileErrors:T})},v=()=>{var N,u,T;const p=((N=l==null?void 0:l.newDevices)==null?void 0:N.length)??-1,y=((u=l==null?void 0:l.foundNotAssigned)==null?void 0:u.length)??-1,k=((T=l==null?void 0:l.foundAssigned)==null?void 0:T.length)??-1;return p>0||y>0&&d||k>0&&c},P=()=>{const p=c?l.foundAssigned:[],y=d?l.foundNotAssigned:[];s({newDevices:l.newDevices,devicesToUpdate:[...p,...y]}),o(2)};return i.useEffect(()=>{const y=ee.CancelToken.source();return t.length>0&&b(y),()=>{y.cancel("axios request cancelled")}},[t]),l!=null&&l.isLoading?e.jsxs("div",{style:{padding:"24px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",marginTop:"130px"},children:[e.jsx(Re,{percent:(l==null?void 0:l.percentTreated)??0,showInfo:!1,style:{flex:1,marginBottom:0},status:(l==null?void 0:l.percentTreated)!==100?"active":"success",strokeColor:(l==null?void 0:l.percentTreated)===100?"#14C9BB":void 0,strokeWidth:12}),e.jsxs(B.Title,{level:5,style:{margin:"0 0 0px 0",width:50,fontWeight:"bold",marginLeft:10},children:[Math.round(l.percentTreated||0),"%"]})]}),e.jsxs(B.Title,{level:5,style:{marginTop:"16px",marginBottom:"0",fontWeight:"bold"},children:[n("devices.treating")," ",l==null?void 0:l.treating]})]}):l!=null&&l.isFinished?e.jsxs("div",{style:{padding:"0"},children:[e.jsx(B.Title,{level:5,style:{margin:"0 0 16px 0"},children:n("devices.test_results")}),l.newDevices.length>0&&e.jsxs("div",{style:{border:"2px solid #36d399",borderRadius:"5px",padding:"8px",marginBottom:"16px"},children:[e.jsxs(B.Text,{strong:!0,style:{display:"block",marginBottom:"8px"},children:[l.newDevices.length," ",n("devices.new_devices")]}),e.jsx(we,{dataSource:l.newDevices,columns:j,showPagination:!0})]}),l.foundNotAssigned.length>0&&e.jsxs("div",{style:{border:"2px solid #fbbf24",borderRadius:"5px",padding:"8px",marginBottom:"16px"},children:[e.jsxs(B.Text,{strong:!0,style:{display:"block",marginBottom:"8px"},children:[l.foundNotAssigned.length," ",n("devices.found_not_assigned")]}),e.jsx(we,{dataSource:l.foundNotAssigned,columns:j,showPagination:!0})]}),l.foundAssigned.length>0&&e.jsxs("div",{style:{border:"2px solid #fbbf24",borderRadius:"5px",padding:"8px",marginBottom:"16px"},children:[e.jsxs(B.Text,{strong:!0,style:{display:"block",marginBottom:"8px"},children:[l.foundAssigned.length," ",n("devices.found_assigned")]}),e.jsx(we,{dataSource:l.foundAssigned,columns:j,showPagination:!0})]}),l.fileErrors.length>0&&e.jsxs("div",{style:{border:"2px solid #f87171",borderRadius:"5px",padding:"8px",marginBottom:"16px"},children:[e.jsxs(B.Text,{strong:!0,style:{display:"block",marginBottom:"8px"},children:[l.fileErrors.length," ",n("devices.file_errors")]}),e.jsx(we,{dataSource:l.fileErrors,columns:C,showPagination:!0})]}),l.foundNotAssigned.length>0&&e.jsxs("div",{style:{marginTop:"0px"},children:[e.jsx("div",{children:e.jsx(B.Text,{style:{color:"red"},children:"Reassign devices which already exist and are owned by another site?"})}),e.jsx("div",{style:{marginTop:"5px",marginBottom:"67px"},children:e.jsx(pe,{checked:d,onChange:h})})]}),l.foundAssigned.length>0&&e.jsxs("div",{style:{marginTop:"16px"},children:[e.jsx("div",{children:e.jsx(B.Text,{style:{color:"red"},children:n("devices.reassign_already_owned")})}),e.jsx("div",{style:{marginTop:"5px",marginBottom:"67px"},children:e.jsx(pe,{checked:c,onChange:g})})]}),e.jsx("div",{style:{display:"flex",justifyContent:"left",gap:"8px",marginTop:"40px",marginBottom:"46px"},children:e.jsx(q,{type:"primary",onClick:P,disabled:!v(),style:{minWidth:"160px"},children:n("devices.start_import")})})]}):null};bt.propTypes=yn;const bn="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACEAAAAgCAYAAACcuBHKAAAEDUlEQVR4AexWvW4TQRCeWR8F0BhsJAok8Bs4TYy7RHL4qSBPACkQDRKxRIsgHQ0KEQ8QkOgDEuInRnIKJMc0AfEACRVInCU3IKScb5lv73b9d3eOk4aC1d3Nzs/OfLc7O7uK/oH2H4RdhEPNRLH1dq7Yev+wsP1hvdhubBgqfLHVmMs3m3nr/KB0KhDF7cZyob25S8prklIPmPVNCXTdUOFJUdM7EewC4DRgDgQi33p7wQRnWmXiCxI468kDYO7E/g7GZRla3UQQZz5tlj3l7fBw8K44eElhuKKJbzLzstb8TGSQCyFiscc4jKcJLRME/iTUtCE++ussgYMwmPErC4t+9fLDTqX2/Odsba1zsbYU/PZKACb29sljPPxYQRLNBOHlvHX8UTywSyHNI3C3enUvlg2R7vx8F3rYicLMCsbnVK4pfOqTCgKZTprmKG461HW/urAVs5kEdprCJWsEIIXWJpLYioZoKgjJ9LvOkmmrU72ENXeiSZ1O5fLLOE+MqeTNDdNJ+CSCiLeXmwXq0UrC2Iki1vvPnRFTag0ZA4Ek8o4HmDqXjMEf77NzNkXHr14dWj7ZtrfjHxzyMgSi+KmxKttql5hWB6x+IOEG+Gm7vh0gufEoqZgZEEBXbDd2SNMyjTV+PSaaTvBxxNwUMwHTRFzoDAjvZIBaUIYArya9Z5JK9+p+pXYLssO+UjuWiPme8Udkti1FrZw73jMzrka3IopNp3KphOLjX7zyJLJP/xba7+6c3n5zLs0CS+nP1h7DnwAqEZPziTMHW1dRjh6QbUxbpthYPoMigWUJvzPlnio+9hU8TWgRoIU6SRxryoqvKa21O5DiKbP6VIqAcRU8GxvlwUMe85lE63DNGmjSZSUZ60CoIPxilWkUgRBwcBxswUMOPfisVwW8Z/XM6jwS0yVL6PVnxRoNUgRAIJYTclBu+5BDDzsrS6KDcWRWvgFEv6Do3LWkQVY2cqBZ8RAFEI+99SHhCCN/744EKed7Sg6mV9YG2YqCZflRqkN20yjjlgb1HNJ9y2vq21mZpbh1kSZ3JMhMrKn4YOqXZSlYkvU7AGO2L/UbtpmmcFHuE6V4nFPuU/BCtuAp6GHnFNIptN9fhz9zO8M1UGTmYdrqyEGH5SBxuqilQBlF9CkL2mU5SZu4xEai6ItBWfcJ6CPL6IvgTGoD/ngglxAv+OUtkjQDAk57v4/NJG1RWSJ3kIn91I8s4dh4xEE81A04NCDQgQDTKLNSkmSJ7oxS3YSvQ3/Yt6f3V0j8IDD8ir8S4iCe9elAWAFmxd4Z/dmFOnirOwzFePhBYPgFP+pnDMSoQRaPdY313STnsW4iORKIXtibj6d4ZmKkDIMjgcDfp03xWMwMwZFAZPidSvUXAAD//1AyYvkAAAAGSURBVAMAwUvEUMChw5IAAAAASUVORK5CYII=",vn={refresh:E.func.isRequired,handleRefresh:E.func.isRequired,deviceClass:E.string.isRequired,parent:E.shape({entity:E.string,venue:E.string})},jn={parent:{}},it=()=>window.location.hash?window.location.hash.replace("#",""):"",Qe=({refresh:t,handleRefresh:o,deviceClass:s,parent:r})=>{const{t:n}=_(),[a,m]=i.useState(be()),[c,g]=i.useState(0),[d,h]=i.useState(!0),[l,f]=i.useState(!1),[j,C]=i.useState(!1),[O,F]=i.useState([]),[b,v]=i.useState({}),P=()=>{switch(c){case 0:return e.jsx(ft,{setPhase:g,setDevices:F,setIsCloseable:h,refreshId:a});case 1:return e.jsx(bt,{setPhase:g,devicesToTest:O,setDevicesToImport:v,setIsCloseable:h,venueId:it()});case 2:return e.jsx(yt,{devices:b,refresh:t,deviceClass:s,parent:r,setIsCloseable:h,venueId:it(),handleRefresh:o});default:return null}},p=()=>{g(0),m(be()),h(!0),F([]),v([]),f(!0)},y=()=>d?f(!1):C(!0),k=()=>{C(!1),f(!1)},N=()=>{C(!1)};return e.jsxs(e.Fragment,{children:[e.jsx(dt,{title:n("devices.import_batch_tags")||"Import Batch Tags",placement:"top",mouseEnterDelay:.3,children:e.jsx(q,{htmlType:"button",onClick:p,style:{display:"flex",alignItems:"center"},icon:e.jsx("img",{src:bn,alt:"upload",style:{width:16,height:16}}),children:"Upload"})}),e.jsxs(Se,{style:{borderRadius:"8px",minHeight:"500px",padding:"15px 20px"},title:e.jsx("div",{style:{fontSize:"20px",fontWeight:"bold",color:"#222",marginTop:"0px !important",height:"27px",display:"flex",alignItems:"center",margin:"0px 0px 15px 0"},children:e.jsx("span",{children:n("devices.import_batch_tags")||"Import Batch Tags"})}),open:l,onCancel:y,width:1360,footer:null,children:[e.jsx(te,{style:{margin:"16 0 16px 0",width:"calc(100% + 48px)",marginLeft:"-24px"}}),e.jsx("div",{style:{padding:"0px",minHeight:"420px",marginTop:"30px",maxHeight:"calc(100vh - 350px)",overflowY:"auto"},children:P()})]}),e.jsxs(Se,{open:j,onOk:k,onCancel:N,zIndex:1001,title:e.jsx("div",{style:{fontSize:"20px",fontWeight:"bold",color:"#222",marginTop:"0px !important",height:"27px",display:"flex",alignItems:"center",margin:"0px 0px 15px 0"},children:e.jsx("span",{children:n("common.discard_changes")})}),cancelText:n("common.cancel"),okText:n("common.confirm"),width:480,footer:[e.jsx(q,{onClick:N,children:n("common.cancel")},"cancel"),e.jsx(q,{type:"primary",danger:!0,onClick:k,children:n("common.confirm")},"ok")],children:[e.jsx(te,{style:{margin:"0 0 32px 0",width:"calc(100% + 50px)",marginLeft:"-25px"}}),e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx(me,{type:"warning",showIcon:!0,style:{color:"rgba(0, 0, 0, 0.88)",backgroundColor:"transparent",border:"none",padding:0}}),n("crud.confirm_cancel")||"Are you sure you want to discard the changes you have made?"]}),e.jsx(te,{style:{margin:"64px 0px 16px 0px",width:"calc(100% + 50px)",marginLeft:"-25px"}})]})]})};Qe.propTypes=vn;Qe.defaultProps=jn;const Cn=()=>{const{getFirstVenueFavoriteId:t}=gt(),o=ze(),s=()=>{const a=window.location.hash;return a?a.substring(1):null};let n=t();return o.data&&(n=n??o.data[0].id,n=s()||n),{defaultValue:n}},wn="/ampcon/wireless/inventory/batch_delete";function Sn(t){return We({url:`${wn}`,method:"DELETE",data:{idList:t}})}function Nn({idList:t,siteId:o}){return We({url:"/ampcon/wireless/inventory/batch_switch_site",method:"PUT",data:{idList:t,siteId:o}})}const An=i.forwardRef(({venueId:t,onlyUnassigned:o},s)=>{const{defaultValue:r}=Cn();let n=null;const a=window.location.hash.replace("#","");a&&/^\d+$/.test(a)&&(n=a);let m=null;r&&/^\d+$/.test(r)&&(m=r),t=t||n||m;const{t:c}=_(),[g,d]=i.useState(""),[h,l]=i.useState(void 0),{isOpen:f,onOpen:j,onClose:C}=de(),{isOpen:O,onOpen:F,onClose:b}=de(),[v,P]=i.useState(!1),[p,y]=i.useState([{id:"serialNumber",sort:"asc"}]),[k,N]=i.useState({index:0,limit:10}),[u,T]=i.useState([]),[L,S]=i.useState([]);de();const w=de(),$=de(),z=ct({onSuccess:()=>F()}),{data:ue,isFetching:Z,refetch:H}=Kt({enabled:!0,onlyUnassigned:o,venueId:t}),{data:ae,isFetching:ne,refetch:ie}=Jt({pageInfo:k,sortInfo:p,enabled:!0,count:ue,onlyUnassigned:o,venueId:t}),ke={selectedRowKeys:u,selectedRows:L,onChange:(A,G)=>{T(A),S(G)}},De=async A=>{var G,U;if(A.length){P(!0);try{await Sn(A),M.success("Batch delete inventory success."),H(),ie(),T([])}catch(le){M.error(`Failed to delete devices: ${((U=(G=le==null?void 0:le.response)==null?void 0:G.data)==null?void 0:U.ErrorDescription)||"Unknown error"}`)}finally{P(!1)}}},Ee=oe.useCallback(A=>e.jsx("a",{href:`/wireless/devices/${A.serialNumber}#/devices/${A.serialNumber}`,style:{color:"#14C9BB ",textDecoration:"underline"},children:e.jsx("pre",{children:A.serialNumber})}),[]),Oe=()=>{if(u.length===0){M.warning("Please select at least one piece of data");return}ye("Are you sure you need to delete the selected device?",()=>{De(u)})},x=(A,G,U)=>{N({index:A.current-1,limit:A.pageSize}),U.field&&y([{id:U.field,sort:U.order==="ascend"?"asc":"desc"}])},I=A=>{d(A),w.onOpen()},W=A=>{d(A),$.onOpen()},V=A=>{l(A),j()},je=i.useCallback(A=>e.jsx(Ss,{cell:A.row,refreshTable:H,openEditModal:V,onOpenFactoryReset:I,onOpenUpgradeModal:W},be()),[]),ge=i.useCallback((A,G)=>e.jsx(is,{date:A.row.original[G]},be()),[]),vt=i.useCallback(A=>{var G,U;return e.jsx(zs,{venueName:((U=(G=A.row.original.extendedInfo)==null?void 0:G.venue)==null?void 0:U.name)??"",venueId:A.row.original.venue})},[]),jt=i.useCallback(A=>{V({serialNumber:A})},[]),Ze=oe.useMemo(()=>[{key:"serialNumber",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:c("inventory.serial_number")}),dataIndex:"serialNumber",render:(G,U)=>Ee(U),sorter:!0,columnsFix:!0},{key:"name",title:c("common.name"),dataIndex:"name",sorter:!0},{key:"venue",title:c("inventory.site"),dataIndex:["venue"],render:(G,U)=>vt({row:{original:U}}),sorter:!0},{key:"description",title:c("common.description"),dataIndex:"description",sorter:!0},{key:"label",title:c("inventory.label"),dataIndex:"labelsName",sorter:!0},{key:"modified",title:c("common.modified"),dataIndex:"modified",render:(G,U)=>ge({row:{original:U}},"modified"),sorter:!0},{key:"operation",title:"Operation",dataIndex:"operation",render:(G,U)=>je({row:{original:U}},"operation"),columnsFix:!0,fixed:"right"}],[c]),Ye=()=>{H(),ie()};i.useImperativeHandle(s,()=>({refreshTable:()=>{H(),ie()}})),i.useEffect(()=>{N(A=>({...A,index:0})),T([]),S([])},[t,o]);const[Ct,Xe]=i.useState(!1),[wt,et]=i.useState(""),{data:St=[]}=ls(),[Nt]=D.useForm(),At=()=>{if(u.length===0){M.warning("Please select at least one piece of data");return}Xe(!0)},Tt=async A=>{var U,le,st,nt;const G=A.site;if(!G){M.error("Please select a site");return}try{const Fe=await Nn({idList:u,siteId:G==="none"?null:G});Fe.status===200||Fe.status===201?(M.success(`Successfully moved ${u.length} devices`),tt(),T([]),ie(),H()):M.error(((U=Fe.data)==null?void 0:U.info)||"Failed to move devices")}catch(ce){console.error("Error moving devices:",ce),(st=(le=ce.response)==null?void 0:le.data)!=null&&st.info?M.error(`Failed to move devices: ${ce.response.data.info}`):(nt=ce.response)!=null&&nt.status?M.error(`Failed to move devices: Server returned status ${ce.response.status}`):M.error(`Failed to move devices: ${ce.message}`)}},tt=()=>{Xe(!1),et("")};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("div",{style:{display:"flex",alignItems:"center"},children:e.jsxs(re,{size:16,align:"middle",children:[e.jsx(He,{refresh:H,venueId:t}),e.jsx(Fs,{}),e.jsx(Qe,{refresh:H,handleRefresh:Ye,deviceClass:"venue"}),e.jsx(q,{htmlType:"button",style:{display:"flex",alignItems:"center"},onClick:()=>{Ye(),M.success("Inventory table refresh success.")},icon:e.jsx(ve,{component:ht}),children:"Refresh"}),e.jsx(qe,{trigger:["click"],overlayStyle:{width:"150px"},menu:{items:[{key:"move",label:"Move",onClick:At},{key:"delete",label:"Delete",onClick:Oe}]},children:e.jsx(q,{htmlType:"button",icon:e.jsx(ps,{style:{marginTop:"5px"}}),disabled:u.length===0||v,loading:v,children:"Actions"})})]})}),e.jsx("div",{style:{minWidth:"280px"},children:e.jsx(qs,{onClick:jt})})]}),e.jsx(Zt,{columnsOrder:!0,resizableColumns:!0,tableId:"inventory-table",ref:s,columns:o?Ze.filter(A=>A.key!=="entity"&&A.key!=="venue"):Ze,dataSource:ae||[],loading:Z||ne,onChange:x,showColumnSelector:"true",rowSelection:ke,disableInternalRowSelection:!0,pagination:{current:k.index+1,pageSize:k.limit,total:ue,showSizeChanger:!0,showQuickJumper:!0,showTotal:A=>`Total ${A} items`,pageSizeOptions:["10","20","50","100"]}}),e.jsx(an,{tag:h,refresh:ie,onClose:C,open:f},h==null?void 0:h.serialNumber),e.jsx(Je,{isOpen:O,onClose:b,pushResult:z.data}),e.jsx(Ps,{modalProps:w,serialNumber:g}),e.jsxs(he,{title:"Move",open:Ct,onCancel:tt,onFinish:A=>{Tt(A)},form:Nt,modalClass:"ampcon-middle-modal",children:[e.jsx("style",{children:`
            .ant-form-item-explain {
              margin-left: -90px;
            }
          `}),e.jsx(me,{className:"custom-trace-alert",message:c("Note: After the device is moved, the target site configuration will be delivered to the device."),type:"info",showIcon:!0,closable:!0,style:{marginTop:10,marginBottom:20}}),e.jsx(D.Item,{label:"Select Site",name:"site",rules:[{required:!0,message:"Please select site"}],className:"site-selection-form-item",children:e.jsxs(K,{value:wt,onChange:A=>et(A),placeholder:"Please select site",allowClear:!0,style:{width:"280px",height:"36px",marginLeft:"-90px"},children:[e.jsx(K.Option,{value:"none",children:"All Sites"}),St.map(A=>e.jsx(K.Option,{value:A.id,children:A.name},A.id))]})})]})]})}),_n=()=>{const{t}=_(),{selectedSiteId:o,isAllSitesSelected:s,handleSiteChange:r,displaySiteId:n}=cs(!0),[a,m]=i.useState(!1);pt(),gt(),ds();const c=g=>{m(g)};return e.jsxs(Ht,{style:{width:"100%",minHeight:"100%",borderRadius:"8px",boxShadow:"none",padding:"20px 24px",overflowX:"auto"},bodyStyle:{padding:0},children:[e.jsx("span",{className:"text-title",children:"Inventory"}),e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:16},children:[e.jsx(Qt,{onChange:r,value:s?"all":o||"all"}),s&&e.jsx(D.Item,{label:t("devices.unassigned_only"),style:{marginBottom:0,marginLeft:"80px"},children:e.jsx(pe,{checked:a,onChange:c})})]}),e.jsx(An,{venueId:n,onlyUnassigned:a})]})};export{_n as default};
