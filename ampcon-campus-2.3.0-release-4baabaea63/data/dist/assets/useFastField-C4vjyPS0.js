import{d5 as h,r as f,q as C,j as F,v}from"./index-CCDcquaz.js";import{c as k,b}from"./index-B9-ToZA1.js";function R(d){const{value:a,defaultValue:n,onChange:t,shouldUpdate:s=(i,m)=>i!==m}=d,r=h(t),o=h(s),[l,c]=f.useState(n),u=a!==void 0,e=u?a:l,p=h(i=>{const x=typeof i=="function"?i(e):i;o(e,x)&&(u||c(x),r(x))},[u,r,e,o]);return[e,p]}const g=C(function(a,n){const{direction:t,align:s,justify:r,wrap:o,basis:l,grow:c,shrink:u,...e}=a,p={display:"flex",flexDirection:t,alignItems:s,justifyContent:r,flexWrap:o,flexBasis:l,flexGrow:c,flexShrink:u};return F.jsx(v.div,{ref:n,__css:p,...e})});g.displayName="Flex";const S=({name:d})=>{const{setFieldValue:a}=k(),[{value:n},{touched:t,error:s},{setValue:r,setTouched:o}]=b(d),l=f.useCallback(e=>{r(e,!0),setTimeout(()=>{o(!0,!1)},200)},[]),c=f.useCallback(()=>{o(!0)},[]);return f.useMemo(()=>({value:n,touched:t,error:s,isError:s!==void 0&&t,setFieldValue:a,onChange:l,onBlur:c}),[n,t,s,l])};export{g as F,S as a,R as u};
