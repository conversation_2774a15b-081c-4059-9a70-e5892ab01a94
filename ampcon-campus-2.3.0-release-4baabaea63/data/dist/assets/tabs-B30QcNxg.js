import{bE as zt,bF as Kt,bG as ft,bH as pt,bI as It,bJ as At,bK as te,bL as Ut,R as b,bM as J,r as i,bN as pe,bO as Ve,bP as bt,_ as Te,P as me,W as G,bQ as Le,a2 as de,bR as Jt,bS as vt,ad as qt,D as Ce,bT as Y,bU as er,bV as tr,bW as rr,bX as ar,bY as nr,bZ as or,b_ as lr,b$ as sr,c0 as ir,c1 as cr,c2 as xt,c3 as gr,bv as dr,ai as ur,j as a,aA as we,X as ie,aj as mr,B as K,al as Xe,aB as Cr,ac as Ie,O as P,a5 as ue,M as yt,b as Ae,g as V,h as T,ae as hr,c4 as fr,H as M,am as wt,ab as Se,c5 as pr,T as Ir,c6 as We,V as Ar,Q as br}from"./index-CCDcquaz.js";import{b as vr,u as xr,F as Ye}from"./useUrlSync-DjGOfs83.js";import{d as Me,F as yr}from"./index-B9-ToZA1.js";var wr=["b"],Sr=["v"],De=function(t){return Math.round(Number(t||0))},jr=function(t){if(t&&It(t)==="object"&&"h"in t&&"b"in t){var n=t,r=n.b,o=At(n,wr);return te(te({},o),{},{v:r})}return typeof t=="string"&&/hsb/.test(t)?t.replace(/hsb/,"hsv"):t},Ne=function(e){zt(n,e);var t=Kt(n);function n(r){return ft(this,n),t.call(this,jr(r))}return pt(n,[{key:"toHsbString",value:function(){var o=this.toHsb(),l=De(o.s*100),c=De(o.b*100),s=De(o.h),g=o.a,u="hsb(".concat(s,", ").concat(l,"%, ").concat(c,"%)"),C="hsba(".concat(s,", ").concat(l,"%, ").concat(c,"%, ").concat(g.toFixed(g===0?0:2),")");return g===1?u:C}},{key:"toHsb",value:function(){var o=this.toHsv();It(this.originalInput)==="object"&&this.originalInput&&"h"in this.originalInput&&(o=this.originalInput);var l=o;l.v;var c=At(l,Sr);return te(te({},c),{},{b:o.v,a:this.a})}}]),n}(Ut),Er="rc-color-picker",oe=function(t){return t instanceof Ne?t:new Ne(t)},Pr=oe("#1677ff"),St=function(t){var n=t.offset,r=t.targetRef,o=t.containerRef,l=t.color,c=t.type,s=o.current.getBoundingClientRect(),g=s.width,u=s.height,C=r.current.getBoundingClientRect(),m=C.width,A=C.height,d=m/2,I=A/2,f=(n.x+d)/g,j=1-(n.y+I)/u,E=l.toHsb(),L=f,N=(n.x+d)/g*360;if(c)switch(c){case"hue":return oe(te(te({},E),{},{h:N<=0?0:N}));case"alpha":return oe(te(te({},E),{},{a:L<=0?0:L}))}return oe({h:E.h,s:f<=0?0:f,b:j>=1?1:j,a:E.a})},jt=function(t,n,r,o){var l=t.current.getBoundingClientRect(),c=l.width,s=l.height,g=n.current.getBoundingClientRect(),u=g.width,C=g.height,m=u/2,A=C/2,d=r.toHsb();if(!(u===0&&C===0||u!==C)){if(o)switch(o){case"hue":return{x:d.h/360*c-m,y:-A/3};case"alpha":return{x:d.a/1*c-m,y:-A/3}}return{x:d.s*c-m,y:(1-d.b)*s-A}}},ze=function(t){var n=t.color,r=t.prefixCls,o=t.className,l=t.style,c=t.onClick,s="".concat(r,"-color-block");return b.createElement("div",{className:J(s,o),style:l,onClick:c},b.createElement("div",{className:"".concat(s,"-inner"),style:{background:n}}))};function Lr(e){var t="touches"in e?e.touches[0]:e,n=document.documentElement.scrollLeft||document.body.scrollLeft||window.pageXOffset,r=document.documentElement.scrollTop||document.body.scrollTop||window.pageYOffset;return{pageX:t.pageX-n,pageY:t.pageY-r}}function Et(e){var t=e.offset,n=e.targetRef,r=e.containerRef,o=e.direction,l=e.onDragChange,c=e.onDragChangeComplete,s=e.calculate,g=e.color,u=e.disabledDrag,C=i.useState(t||{x:0,y:0}),m=pe(C,2),A=m[0],d=m[1],I=i.useRef(null),f=i.useRef(null),j=i.useRef({flag:!1});i.useEffect(function(){if(j.current.flag===!1){var B=s==null?void 0:s(r);B&&d(B)}},[g,r]),i.useEffect(function(){return function(){document.removeEventListener("mousemove",I.current),document.removeEventListener("mouseup",f.current),document.removeEventListener("touchmove",I.current),document.removeEventListener("touchend",f.current),I.current=null,f.current=null}},[]);var E=function(R){var S=Lr(R),W=S.pageX,_=S.pageY,D=r.current.getBoundingClientRect(),Q=D.x,q=D.y,x=D.width,y=D.height,$=n.current.getBoundingClientRect(),F=$.width,k=$.height,H=F/2,p=k/2,h=Math.max(0,Math.min(W-Q,x))-H,v=Math.max(0,Math.min(_-q,y))-p,O={x:h,y:o==="x"?A.y:v};if(F===0&&k===0||F!==k)return!1;d(O),l==null||l(O)},L=function(R){R.preventDefault(),E(R)},N=function(R){R.preventDefault(),j.current.flag=!1,document.removeEventListener("mousemove",I.current),document.removeEventListener("mouseup",f.current),document.removeEventListener("touchmove",I.current),document.removeEventListener("touchend",f.current),I.current=null,f.current=null,c==null||c()},w=function(R){document.removeEventListener("mousemove",I.current),document.removeEventListener("mouseup",f.current),!u&&(E(R),j.current.flag=!0,document.addEventListener("mousemove",L),document.addEventListener("mouseup",N),document.addEventListener("touchmove",L),document.addEventListener("touchend",N),I.current=L,f.current=N)};return[A,w]}var Pt=function(t){var n=t.size,r=n===void 0?"default":n,o=t.color,l=t.prefixCls;return b.createElement("div",{className:J("".concat(l,"-handler"),Ve({},"".concat(l,"-handler-sm"),r==="small")),style:{backgroundColor:o}})},Lt=function(t){var n=t.children,r=t.style,o=t.prefixCls;return b.createElement("div",{className:"".concat(o,"-palette"),style:te({position:"relative"},r)},n)},Nt=i.forwardRef(function(e,t){var n=e.children,r=e.offset;return b.createElement("div",{ref:t,style:{position:"absolute",left:r.x,top:r.y,zIndex:1}},n)}),Nr=function(t){var n=t.color,r=t.onChange,o=t.prefixCls,l=t.onChangeComplete,c=t.disabled,s=i.useRef(),g=i.useRef(),u=i.useRef(n),C=bt(function(f){var j=St({offset:f,targetRef:g,containerRef:s,color:n});u.current=j,r(j)}),m=Et({color:n,containerRef:s,targetRef:g,calculate:function(j){return jt(j,g,n)},onDragChange:C,onDragChangeComplete:function(){return l==null?void 0:l(u.current)},disabledDrag:c}),A=pe(m,2),d=A[0],I=A[1];return b.createElement("div",{ref:s,className:"".concat(o,"-select"),onMouseDown:I,onTouchStart:I},b.createElement(Lt,{prefixCls:o},b.createElement(Nt,{offset:d,ref:g},b.createElement(Pt,{color:n.toRgbString(),prefixCls:o})),b.createElement("div",{className:"".concat(o,"-saturation"),style:{backgroundColor:"hsl(".concat(n.toHsb().h,",100%, 50%)"),backgroundImage:"linear-gradient(0deg, #000, transparent),linear-gradient(90deg, #fff, hsla(0, 0%, 100%, 0))"}})))},Rr=function(t){var n=t.colors,r=t.children,o=t.direction,l=o===void 0?"to right":o,c=t.type,s=t.prefixCls,g=i.useMemo(function(){return n.map(function(u,C){var m=oe(u);return c==="alpha"&&C===n.length-1&&m.setAlpha(1),m.toRgbString()}).join(",")},[n,c]);return b.createElement("div",{className:"".concat(s,"-gradient"),style:{position:"absolute",inset:0,background:"linear-gradient(".concat(l,", ").concat(g,")")}},r)},tt=function(t){var n=t.gradientColors,r=t.direction,o=t.type,l=o===void 0?"hue":o,c=t.color,s=t.value,g=t.onChange,u=t.onChangeComplete,C=t.disabled,m=t.prefixCls,A=i.useRef(),d=i.useRef(),I=i.useRef(c),f=bt(function(w){var B=St({offset:w,targetRef:d,containerRef:A,color:c,type:l});I.current=B,g(B)}),j=Et({color:c,targetRef:d,containerRef:A,calculate:function(B){return jt(B,d,c,l)},onDragChange:f,onDragChangeComplete:function(){u==null||u(I.current,l)},direction:"x",disabledDrag:C}),E=pe(j,2),L=E[0],N=E[1];return b.createElement("div",{ref:A,className:J("".concat(m,"-slider"),"".concat(m,"-slider-").concat(l)),onMouseDown:N,onTouchStart:N},b.createElement(Lt,{prefixCls:m},b.createElement(Nt,{offset:L,ref:d},b.createElement(Pt,{size:"small",color:s,prefixCls:m})),b.createElement(Rr,{colors:n,direction:r,type:l,prefixCls:m})))};function rt(e){return e!==void 0}var kr=function(t,n){var r=n.defaultValue,o=n.value,l=i.useState(function(){var u;return rt(o)?u=o:rt(r)?u=r:u=t,oe(u)}),c=pe(l,2),s=c[0],g=c[1];return i.useEffect(function(){o&&g(oe(o))},[o]),[s,g]},Or=["rgb(255, 0, 0) 0%","rgb(255, 255, 0) 17%","rgb(0, 255, 0) 33%","rgb(0, 255, 255) 50%","rgb(0, 0, 255) 67%","rgb(255, 0, 255) 83%","rgb(255, 0, 0) 100%"];const Hr=i.forwardRef(function(e,t){var n=e.value,r=e.defaultValue,o=e.prefixCls,l=o===void 0?Er:o,c=e.onChange,s=e.onChangeComplete,g=e.className,u=e.style,C=e.panelRender,m=e.disabledAlpha,A=m===void 0?!1:m,d=e.disabled,I=d===void 0?!1:d,f=kr(Pr,{value:n,defaultValue:r}),j=pe(f,2),E=j[0],L=j[1],N=i.useMemo(function(){var W=oe(E.toRgbString());return W.setAlpha(1),W.toRgbString()},[E]),w=J("".concat(l,"-panel"),g,Ve({},"".concat(l,"-panel-disabled"),I)),B={prefixCls:l,onChangeComplete:s,disabled:I},R=function(_,D){n||L(_),c==null||c(_,D)},S=b.createElement(b.Fragment,null,b.createElement(Nr,Te({color:E,onChange:R},B)),b.createElement("div",{className:"".concat(l,"-slider-container")},b.createElement("div",{className:J("".concat(l,"-slider-group"),Ve({},"".concat(l,"-slider-group-disabled-alpha"),A))},b.createElement(tt,Te({gradientColors:Or,color:E,value:"hsl(".concat(E.toHsb().h,",100%, 50%)"),onChange:function(_){return R(_,"hue")}},B)),!A&&b.createElement(tt,Te({type:"alpha",gradientColors:["rgba(255, 0, 4, 0) 0%",N],color:E,value:E.toRgbString(),onChange:function(_){return R(_,"alpha")}},B))),b.createElement(ze,{color:E.toRgbString(),prefixCls:l})));return b.createElement("div",{className:w,style:u,ref:t},typeof C=="function"?C(S):S)}),Rt=b.createContext({}),kt=b.createContext({}),{Provider:Br}=Rt,{Provider:$r}=kt,Ee=(e,t)=>(e==null?void 0:e.replace(/[^\w/]/gi,"").slice(0,t?8:6))||"",Tr=(e,t)=>e?Ee(e,t):"";let at=function(){function e(t){ft(this,e),this.metaColor=new Ne(t),t||this.metaColor.setAlpha(0)}return pt(e,[{key:"toHsb",value:function(){return this.metaColor.toHsb()}},{key:"toHsbString",value:function(){return this.metaColor.toHsbString()}},{key:"toHex",value:function(){return Tr(this.toHexString(),this.metaColor.getAlpha()<1)}},{key:"toHexString",value:function(){return this.metaColor.getAlpha()===1?this.metaColor.toHexString():this.metaColor.toHex8String()}},{key:"toRgb",value:function(){return this.metaColor.toRgb()}},{key:"toRgbString",value:function(){return this.metaColor.toRgbString()}}]),e}();const X=e=>e instanceof at?e:new at(e),Pe=e=>Math.round(Number(e||0)),Re=e=>Pe(e.toHsb().a*100),Ge=(e,t)=>{const n=e.toHsb();return n.a=1,X(n)},Ot=e=>{let{prefixCls:t,value:n,colorCleared:r,onChange:o}=e;const l=()=>{if(n&&!r){const c=n.toHsb();c.a=0;const s=X(c);o==null||o(s)}};return b.createElement("div",{className:`${t}-clear`,onClick:l})};var re;(function(e){e.hex="hex",e.rgb="rgb",e.hsb="hsb"})(re||(re={}));const ce=e=>{let{prefixCls:t,min:n=0,max:r=100,value:o,onChange:l,className:c,formatter:s}=e;const g=`${t}-steppers`,[u,C]=i.useState(o);return i.useEffect(()=>{Number.isNaN(o)||C(o)},[o]),b.createElement(me,{className:J(g,c),min:n,max:r,value:u,formatter:s,size:"small",onChange:m=>{o||C(m||0),l==null||l(m)}})},Wr=e=>{let{prefixCls:t,value:n,onChange:r}=e;const o=`${t}-alpha-input`,[l,c]=i.useState(X(n||"#000"));i.useEffect(()=>{n&&c(n)},[n]);const s=g=>{const u=l.toHsb();u.a=(g||0)/100;const C=X(u);n||c(C),r==null||r(C)};return b.createElement(ce,{value:Re(l),prefixCls:t,formatter:g=>`${g}%`,className:o,onChange:s})},Mr=/(^#[\da-f]{6}$)|(^#[\da-f]{8}$)/i,nt=e=>Mr.test(`#${e}`),Dr=e=>{let{prefixCls:t,value:n,onChange:r}=e;const o=`${t}-hex-input`,[l,c]=i.useState(n==null?void 0:n.toHex());i.useEffect(()=>{const g=n==null?void 0:n.toHex();nt(g)&&n&&c(Ee(g))},[n]);const s=g=>{const u=g.target.value;c(Ee(u)),nt(Ee(u,!0))&&(r==null||r(X(u)))};return b.createElement(G,{className:o,value:l,prefix:"#",onChange:s,size:"small"})},Gr=e=>{let{prefixCls:t,value:n,onChange:r}=e;const o=`${t}-hsb-input`,[l,c]=i.useState(X(n||"#000"));i.useEffect(()=>{n&&c(n)},[n]);const s=(g,u)=>{const C=l.toHsb();C[u]=u==="h"?g:(g||0)/100;const m=X(C);n||c(m),r==null||r(m)};return b.createElement("div",{className:o},b.createElement(ce,{max:360,min:0,value:Number(l.toHsb().h),prefixCls:t,className:o,formatter:g=>Pe(g||0).toString(),onChange:g=>s(Number(g),"h")}),b.createElement(ce,{max:100,min:0,value:Number(l.toHsb().s)*100,prefixCls:t,className:o,formatter:g=>`${Pe(g||0)}%`,onChange:g=>s(Number(g),"s")}),b.createElement(ce,{max:100,min:0,value:Number(l.toHsb().b)*100,prefixCls:t,className:o,formatter:g=>`${Pe(g||0)}%`,onChange:g=>s(Number(g),"b")}))},_r=e=>{let{prefixCls:t,value:n,onChange:r}=e;const o=`${t}-rgb-input`,[l,c]=i.useState(X(n||"#000"));i.useEffect(()=>{n&&c(n)},[n]);const s=(g,u)=>{const C=l.toRgb();C[u]=g||0;const m=X(C);n||c(m),r==null||r(m)};return b.createElement("div",{className:o},b.createElement(ce,{max:255,min:0,value:Number(l.toRgb().r),prefixCls:t,className:o,onChange:g=>s(Number(g),"r")}),b.createElement(ce,{max:255,min:0,value:Number(l.toRgb().g),prefixCls:t,className:o,onChange:g=>s(Number(g),"g")}),b.createElement(ce,{max:255,min:0,value:Number(l.toRgb().b),prefixCls:t,className:o,onChange:g=>s(Number(g),"b")}))},Fr=[re.hex,re.hsb,re.rgb].map(e=>({value:e,label:e.toLocaleUpperCase()})),Vr=e=>{const{prefixCls:t,format:n,value:r,disabledAlpha:o,onFormatChange:l,onChange:c}=e,[s,g]=Le(re.hex,{value:n,onChange:l}),u=`${t}-input`,C=A=>{g(A)},m=i.useMemo(()=>{const A={value:r,prefixCls:t,onChange:c};switch(s){case re.hsb:return b.createElement(Gr,Object.assign({},A));case re.rgb:return b.createElement(_r,Object.assign({},A));case re.hex:default:return b.createElement(Dr,Object.assign({},A))}},[s,t,r,c]);return b.createElement("div",{className:`${u}-container`},b.createElement(de,{value:s,bordered:!1,getPopupContainer:A=>A,popupMatchSelectWidth:68,placement:"bottomRight",onChange:C,className:`${t}-format-select`,size:"small",options:Fr}),b.createElement("div",{className:u},m),!o&&b.createElement(Wr,{prefixCls:t,value:r,onChange:c}))};var Zr=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const ot=()=>{const e=i.useContext(Rt),{prefixCls:t,colorCleared:n,allowClear:r,value:o,disabledAlpha:l,onChange:c,onClear:s,onChangeComplete:g}=e,u=Zr(e,["prefixCls","colorCleared","allowClear","value","disabledAlpha","onChange","onClear","onChangeComplete"]);return b.createElement(b.Fragment,null,r&&b.createElement(Ot,Object.assign({prefixCls:t,value:o,colorCleared:n,onChange:C=>{c==null||c(C),s==null||s()}},u)),b.createElement(Hr,{prefixCls:t,value:o==null?void 0:o.toHsb(),disabledAlpha:l,onChange:(C,m)=>c==null?void 0:c(C,m,!0),onChangeComplete:g}),b.createElement(Vr,Object.assign({value:o,onChange:c,prefixCls:t,disabledAlpha:l},u)))},_e=e=>e.map(t=>(t.colors=t.colors.map(X),t)),Qr=(e,t)=>{const{r:n,g:r,b:o,a:l}=e.toRgb(),c=new Ne(e.toRgbString()).onBackground(t).toHsv();return l<=.5?c.v>.5:n*.299+r*.587+o*.114>192},lt=e=>{let{label:t}=e;return`panel-${t}`},Xr=e=>{let{prefixCls:t,presets:n,value:r,onChange:o}=e;const[l]=Jt("ColorPicker"),[,c]=vt(),[s]=Le(_e(n),{value:_e(n),postState:_e}),g=`${t}-presets`,u=i.useMemo(()=>s.reduce((A,d)=>{const{defaultOpen:I=!0}=d;return I&&A.push(lt(d)),A},[]),[s]),C=A=>{o==null||o(A)},m=s.map(A=>{var d;return{key:lt(A),label:b.createElement("div",{className:`${g}-label`},A==null?void 0:A.label),children:b.createElement("div",{className:`${g}-items`},Array.isArray(A==null?void 0:A.colors)&&((d=A.colors)===null||d===void 0?void 0:d.length)>0?A.colors.map((I,f)=>b.createElement(ze,{key:`preset-${f}-${I.toHexString()}`,color:X(I).toRgbString(),prefixCls:t,className:J(`${g}-color`,{[`${g}-color-checked`]:I.toHexString()===(r==null?void 0:r.toHexString()),[`${g}-color-bright`]:Qr(I,c.colorBgElevated)}),onClick:()=>C(I)})):b.createElement("span",{className:`${g}-empty`},l.presetEmpty))}});return b.createElement("div",{className:g},b.createElement(qt,{defaultActiveKey:u,ghost:!0,items:m}))},st=()=>{const{prefixCls:e,value:t,presets:n,onChange:r}=i.useContext(kt);return Array.isArray(n)?b.createElement(Xr,{value:t,presets:n,prefixCls:e,onChange:r}):null};var Yr=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const zr=e=>{const{prefixCls:t,presets:n,panelRender:r,color:o,onChange:l,onClear:c}=e,s=Yr(e,["prefixCls","presets","panelRender","color","onChange","onClear"]),g=`${t}-inner-content`,u=Object.assign({prefixCls:t,value:o,onChange:l,onClear:c},s),C=b.useMemo(()=>({prefixCls:t,value:o,presets:n,onChange:l}),[t,o,n,l]),m=b.createElement(b.Fragment,null,b.createElement(ot,null),Array.isArray(n)&&b.createElement(Ce,{className:`${g}-divider`}),b.createElement(st,null));return b.createElement(Br,{value:u},b.createElement($r,{value:C},b.createElement("div",{className:g},typeof r=="function"?r(m,{components:{Picker:ot,Presets:st}}):m)))};var Kr=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Ur=i.forwardRef((e,t)=>{const{color:n,prefixCls:r,open:o,colorCleared:l,disabled:c,format:s,className:g,showText:u}=e,C=Kr(e,["color","prefixCls","open","colorCleared","disabled","format","className","showText"]),m=`${r}-trigger`,A=i.useMemo(()=>l?b.createElement(Ot,{prefixCls:r}):b.createElement(ze,{prefixCls:r,color:n.toRgbString()}),[n,l,r]),d=()=>{const f=n.toHexString().toUpperCase(),j=Re(n);switch(s){case"rgb":return n.toRgbString();case"hsb":return n.toHsbString();case"hex":default:return j<100?`${f.slice(0,7)},${j}%`:f}},I=()=>{if(typeof u=="function")return u(n);if(u)return d()};return b.createElement("div",Object.assign({ref:t,className:J(m,g,{[`${m}-active`]:o,[`${m}-disabled`]:c})},C),A,u&&b.createElement("div",{className:`${m}-text`},I()))});function it(e){return e!==void 0}const Jr=(e,t)=>{const{defaultValue:n,value:r}=t,[o,l]=i.useState(()=>{let c;return it(r)?c=r:it(n)?c=n:c=e,X(c||"")});return i.useEffect(()=>{r&&l(X(r))},[r]),[o,l]},Ht=(e,t)=>({backgroundImage:`conic-gradient(${t} 0 25%, transparent 0 50%, ${t} 0 75%, transparent 0)`,backgroundSize:`${e} ${e}`}),ct=(e,t)=>{const{componentCls:n,borderRadiusSM:r,colorPickerInsetShadow:o,lineWidth:l,colorFillSecondary:c}=e;return{[`${n}-color-block`]:Object.assign(Object.assign({position:"relative",borderRadius:r,width:t,height:t,boxShadow:o},Ht("50%",e.colorFillSecondary)),{[`${n}-color-block-inner`]:{width:"100%",height:"100%",border:`${Y(l)} solid ${c}`,borderRadius:"inherit"}})}},qr=e=>{const{componentCls:t,antCls:n,fontSizeSM:r,lineHeightSM:o,colorPickerAlphaInputWidth:l,marginXXS:c,paddingXXS:s,controlHeightSM:g,marginXS:u,fontSizeIcon:C,paddingXS:m,colorTextPlaceholder:A,colorPickerInputNumberHandleWidth:d,lineWidth:I}=e;return{[`${t}-input-container`]:{display:"flex",[`${t}-steppers${n}-input-number`]:{fontSize:r,lineHeight:o,[`${n}-input-number-input`]:{paddingInlineStart:s,paddingInlineEnd:0},[`${n}-input-number-handler-wrap`]:{width:d}},[`${t}-steppers${t}-alpha-input`]:{flex:`0 0 ${Y(l)}`,marginInlineStart:c},[`${t}-format-select${n}-select`]:{marginInlineEnd:u,width:"auto","&-single":{[`${n}-select-selector`]:{padding:0,border:0},[`${n}-select-arrow`]:{insetInlineEnd:0},[`${n}-select-selection-item`]:{paddingInlineEnd:e.calc(C).add(c).equal(),fontSize:r,lineHeight:`${Y(g)}`},[`${n}-select-item-option-content`]:{fontSize:r,lineHeight:o},[`${n}-select-dropdown`]:{[`${n}-select-item`]:{minHeight:"auto"}}}},[`${t}-input`]:{gap:c,alignItems:"center",flex:1,width:0,[`${t}-hsb-input,${t}-rgb-input`]:{display:"flex",gap:c,alignItems:"center"},[`${t}-steppers`]:{flex:1},[`${t}-hex-input${n}-input-affix-wrapper`]:{flex:1,padding:`0 ${Y(m)}`,[`${n}-input`]:{fontSize:r,textTransform:"uppercase",lineHeight:Y(e.calc(g).sub(e.calc(I).mul(2)).equal())},[`${n}-input-prefix`]:{color:A}}}}}},ea=e=>{const{componentCls:t,controlHeightLG:n,borderRadiusSM:r,colorPickerInsetShadow:o,marginSM:l,colorBgElevated:c,colorFillSecondary:s,lineWidthBold:g,colorPickerHandlerSize:u,colorPickerHandlerSizeSM:C,colorPickerSliderHeight:m}=e;return{[`${t}-select`]:{[`${t}-palette`]:{minHeight:e.calc(n).mul(4).equal(),overflow:"hidden",borderRadius:r},[`${t}-saturation`]:{position:"absolute",borderRadius:"inherit",boxShadow:o,inset:0},marginBottom:l},[`${t}-handler`]:{width:u,height:u,border:`${Y(g)} solid ${c}`,position:"relative",borderRadius:"50%",cursor:"pointer",boxShadow:`${o}, 0 0 0 1px ${s}`,"&-sm":{width:C,height:C}},[`${t}-slider`]:{borderRadius:e.calc(m).div(2).equal(),[`${t}-palette`]:{height:m},[`${t}-gradient`]:{borderRadius:e.calc(m).div(2).equal(),boxShadow:o},"&-alpha":Ht(`${Y(m)}`,e.colorFillSecondary),"&-hue":{marginBottom:l}},[`${t}-slider-container`]:{display:"flex",gap:l,marginBottom:l,[`${t}-slider-group`]:{flex:1,"&-disabled-alpha":{display:"flex",alignItems:"center",[`${t}-slider`]:{flex:1,marginBottom:0}}}}}},ta=e=>{const{componentCls:t,antCls:n,colorTextQuaternary:r,paddingXXS:o,colorPickerPresetColorSize:l,fontSizeSM:c,colorText:s,lineHeightSM:g,lineWidth:u,borderRadius:C,colorFill:m,colorWhite:A,marginXXS:d,paddingXS:I,fontHeightSM:f}=e;return{[`${t}-presets`]:{[`${n}-collapse-item > ${n}-collapse-header`]:{padding:0,[`${n}-collapse-expand-icon`]:{height:f,color:r,paddingInlineEnd:o}},[`${n}-collapse`]:{display:"flex",flexDirection:"column",gap:d},[`${n}-collapse-item > ${n}-collapse-content > ${n}-collapse-content-box`]:{padding:`${Y(I)} 0`},"&-label":{fontSize:c,color:s,lineHeight:g},"&-items":{display:"flex",flexWrap:"wrap",gap:e.calc(d).mul(1.5).equal(),[`${t}-presets-color`]:{position:"relative",cursor:"pointer",width:l,height:l,"&::before":{content:'""',pointerEvents:"none",width:e.calc(l).add(e.calc(u).mul(4)).equal(),height:e.calc(l).add(e.calc(u).mul(4)).equal(),position:"absolute",top:e.calc(u).mul(-2).equal(),insetInlineStart:e.calc(u).mul(-2).equal(),borderRadius:C,border:`${Y(u)} solid transparent`,transition:`border-color ${e.motionDurationMid} ${e.motionEaseInBack}`},"&:hover::before":{borderColor:m},"&::after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"21.5%",display:"table",width:e.calc(l).div(13).mul(5).equal(),height:e.calc(l).div(13).mul(8).equal(),border:`${Y(e.lineWidthBold)} solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`},[`&${t}-presets-color-checked`]:{"&::after":{opacity:1,borderColor:A,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`transform ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`},[`&${t}-presets-color-bright`]:{"&::after":{borderColor:"rgba(0, 0, 0, 0.45)"}}}}},"&-empty":{fontSize:c,color:r}}}},Ze=(e,t,n)=>({borderInlineEndWidth:e.lineWidth,borderColor:t,boxShadow:`0 0 0 ${Y(e.controlOutlineWidth)} ${n}`,outline:0}),ra=e=>{const{componentCls:t}=e;return{"&-rtl":{[`${t}-presets-color`]:{"&::after":{direction:"ltr"}},[`${t}-clear`]:{"&::after":{direction:"ltr"}}}}},gt=(e,t,n)=>{const{componentCls:r,borderRadiusSM:o,lineWidth:l,colorSplit:c,red6:s}=e;return{[`${r}-clear`]:Object.assign(Object.assign({width:t,height:t,borderRadius:o,border:`${Y(l)} solid ${c}`,position:"relative",cursor:"pointer",overflow:"hidden"},n),{"&::after":{content:'""',position:"absolute",insetInlineEnd:l,top:0,display:"block",width:40,height:2,transformOrigin:"right",transform:"rotate(-45deg)",backgroundColor:s}})}},aa=e=>{const{componentCls:t,colorError:n,colorWarning:r,colorErrorHover:o,colorWarningHover:l,colorErrorOutline:c,colorWarningOutline:s}=e;return{[`&${t}-status-error`]:{borderColor:n,"&:hover":{borderColor:o},[`&${t}-trigger-active`]:Object.assign({},Ze(e,n,c))},[`&${t}-status-warning`]:{borderColor:r,"&:hover":{borderColor:l},[`&${t}-trigger-active`]:Object.assign({},Ze(e,r,s))}}},na=e=>{const{componentCls:t,controlHeightLG:n,controlHeightSM:r,controlHeight:o,controlHeightXS:l,borderRadius:c,borderRadiusSM:s,borderRadiusXS:g,borderRadiusLG:u,fontSizeLG:C}=e;return{[`&${t}-lg`]:{minWidth:n,height:n,borderRadius:u,[`${t}-color-block, ${t}-clear`]:{width:o,height:o,borderRadius:c},[`${t}-trigger-text`]:{fontSize:C}},[`&${t}-sm`]:{minWidth:r,height:r,borderRadius:s,[`${t}-color-block, ${t}-clear`]:{width:l,height:l,borderRadius:g}}}},oa=e=>{const{componentCls:t,colorPickerWidth:n,colorPrimary:r,motionDurationMid:o,colorBgElevated:l,colorTextDisabled:c,colorText:s,colorBgContainerDisabled:g,borderRadius:u,marginXS:C,marginSM:m,controlHeight:A,controlHeightSM:d,colorBgTextActive:I,colorPickerPresetColorSize:f,colorPickerPreviewSize:j,lineWidth:E,colorBorder:L,paddingXXS:N,fontSize:w,colorPrimaryHover:B,controlOutline:R}=e;return[{[t]:Object.assign({[`${t}-inner-content`]:Object.assign(Object.assign(Object.assign(Object.assign({display:"flex",flexDirection:"column",width:n,"&-divider":{margin:`${Y(m)} 0 ${Y(C)}`},[`${t}-panel`]:Object.assign({},ea(e))},ct(e,j)),qr(e)),ta(e)),gt(e,f,{marginInlineStart:"auto",marginBottom:C})),"&-trigger":Object.assign(Object.assign(Object.assign(Object.assign({minWidth:A,height:A,borderRadius:u,border:`${Y(E)} solid ${L}`,cursor:"pointer",display:"inline-flex",alignItems:"center",justifyContent:"center",transition:`all ${o}`,background:l,padding:e.calc(N).sub(E).equal(),[`${t}-trigger-text`]:{marginInlineStart:C,marginInlineEnd:e.calc(C).sub(e.calc(N).sub(E)).equal(),fontSize:w,color:s},"&:hover":{borderColor:B},[`&${t}-trigger-active`]:Object.assign({},Ze(e,r,R)),"&-disabled":{color:c,background:g,cursor:"not-allowed","&:hover":{borderColor:I},[`${t}-trigger-text`]:{color:c}}},gt(e,d)),ct(e,d)),aa(e)),na(e))},ra(e))}]},la=er("ColorPicker",e=>{const{colorTextQuaternary:t,marginSM:n}=e,r=8,o=tr(e,{colorPickerWidth:234,colorPickerHandlerSize:16,colorPickerHandlerSizeSM:12,colorPickerAlphaInputWidth:44,colorPickerInputNumberHandleWidth:16,colorPickerPresetColorSize:18,colorPickerInsetShadow:`inset 0 0 1px 0 ${t}`,colorPickerSliderHeight:r,colorPickerPreviewSize:e.calc(r).mul(2).add(n).equal()});return[oa(o)]});var sa=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Ke=e=>{const{value:t,defaultValue:n,format:r,defaultFormat:o,allowClear:l=!1,presets:c,children:s,trigger:g="click",open:u,disabled:C,placement:m="bottomLeft",arrow:A=!0,panelRender:d,showText:I,style:f,className:j,size:E,rootClassName:L,prefixCls:N,styles:w,disabledAlpha:B=!1,onFormatChange:R,onChange:S,onClear:W,onOpenChange:_,onChangeComplete:D,getPopupContainer:Q,autoAdjustOverflow:q=!0,destroyTooltipOnHide:x}=e,y=sa(e,["value","defaultValue","format","defaultFormat","allowClear","presets","children","trigger","open","disabled","placement","arrow","panelRender","showText","style","className","size","rootClassName","prefixCls","styles","disabledAlpha","onFormatChange","onChange","onClear","onOpenChange","onChangeComplete","getPopupContainer","autoAdjustOverflow","destroyTooltipOnHide"]),{getPrefixCls:$,direction:F,colorPicker:k}=i.useContext(rr),H=i.useContext(ar),p=C??H,[,h]=vt(),[v,O]=Jr(h.colorPrimary,{value:t,defaultValue:n}),[z,le]=Le(!1,{value:u,postState:ae=>!p&&ae,onChange:_}),[fe,ve]=Le(r,{value:r,defaultValue:o,onChange:R}),[ge,xe]=i.useState(!1),U=$("color-picker",N),Z=i.useMemo(()=>Re(v)<100,[v]),{status:$t}=b.useContext(nr),Ue=or(E),Je=lr(U),[Tt,Wt,Mt]=la(U,Je),Dt={[`${U}-rtl`]:F},qe=J(L,Mt,Je,Dt),Gt=J(sr(U,$t),{[`${U}-sm`]:Ue==="small",[`${U}-lg`]:Ue==="large"},k==null?void 0:k.className,qe,j,Wt),_t=J(U,qe),$e=i.useRef(!0),Ft=(ae,ye,Yt)=>{let ne=X(ae);(ge||(t===null||!t&&n===null))&&(xe(!1),Re(v)===0&&ye!=="alpha"&&(ne=Ge(ne))),B&&Z&&(ne=Ge(ne)),Yt?$e.current=!1:D==null||D(ne),O(ne),S==null||S(ne,ne.toHexString())},Vt=()=>{xe(!0),W==null||W()},et=ae=>{$e.current=!0;let ye=X(ae);B&&Z&&(ye=Ge(ae)),D==null||D(ye)},Zt={open:z,trigger:g,placement:m,arrow:A,rootClassName:L,getPopupContainer:Q,autoAdjustOverflow:q,destroyTooltipOnHide:x},Qt={prefixCls:U,color:v,allowClear:l,colorCleared:ge,disabled:p,disabledAlpha:B,presets:c,panelRender:d,format:fe,onFormatChange:ve,onChangeComplete:et},Xt=Object.assign(Object.assign({},k==null?void 0:k.style),f);return Tt(b.createElement(ir,Object.assign({style:w==null?void 0:w.popup,overlayInnerStyle:w==null?void 0:w.popupOverlayInner,onOpenChange:ae=>{$e.current&&!p&&le(ae)},content:b.createElement(cr,{override:!0,status:!0},b.createElement(zr,Object.assign({},Qt,{onChange:Ft,onChangeComplete:et,onClear:Vt}))),overlayClassName:_t},Zt),s||b.createElement(Ur,Object.assign({open:z,className:Gt,style:Xt,color:t?X(t):v,prefixCls:U,disabled:p,colorCleared:ge,showText:I,format:fe},y))))},ia=xt(Ke,"color-picker",e=>e,e=>Object.assign(Object.assign({},e),{placement:"bottom",autoAdjustOverflow:!1}));Ke._InternalPanelDoNotUseOrYouWillBeFired=ia;var ca=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const{TimePicker:ga,RangePicker:da}=gr,ua=i.forwardRef((e,t)=>i.createElement(da,Object.assign({},e,{picker:"time",mode:void 0,ref:t}))),he=i.forwardRef((e,t)=>{var{addon:n,renderExtraFooter:r}=e,o=ca(e,["addon","renderExtraFooter"]);const l=i.useMemo(()=>{if(r)return r;if(n)return n},[n,r]);return i.createElement(ga,Object.assign({},o,{mode:void 0,ref:t,renderExtraFooter:l}))}),Bt=xt(he,"picker");he._InternalPanelDoNotUseOrYouWillBeFired=Bt;he.RangePicker=ua;he._InternalPanelDoNotUseOrYouWillBeFired=Bt;const ma=e=>i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:20,height:20,viewBox:"0 0 20 20",...e},i.createElement("defs",null,i.createElement("clipPath",{id:"master_svg0_195_66473"},i.createElement("rect",{x:0,y:0,width:20,height:20,rx:0}))),i.createElement("g",{clipPath:"url(#master_svg0_195_66473)"},i.createElement("g",null,i.createElement("path",{d:"M3.2309200000000002,7.81229C2.843441,7.81231,2.682339,8.30813,2.995804,8.53589L3.5694,8.95266L3.65144,9.01228L6.70891,11.23384C6.84909,11.33569,6.90775,11.51623,6.85421,11.68103L5.68647,15.2754L5.65513,15.3719L5.4360599999999994,16.0462C5.31634,16.4147,5.73811,16.7212,6.05159,16.4934L6.62521,16.076700000000002L6.70726,16.0171L9.7649,13.7958C9.905090000000001,13.6939,10.09491,13.6939,10.2351,13.7958L13.2927,16.0171L13.3748,16.076700000000002L13.9484,16.4934C14.2619,16.7212,14.6837,16.4147,14.5639,16.0462L14.3449,15.3719L14.3135,15.2754L13.1458,11.68103C13.0922,11.51623,13.1509,11.33569,13.2911,11.23384L16.348599999999998,9.01228L16.4306,8.95266L17.0042,8.53589C17.317700000000002,8.30813,17.156599999999997,7.81231,16.7691,7.81229L16.0601,7.81227L15.9587,7.81226L12.1793,7.81212C12.006,7.81211,11.85246,7.70054,11.7989,7.53574L10.63089,3.9414100000000003L10.59954,3.84496L10.38042,3.17066C10.26067,2.80215,9.739329999999999,2.80215,9.61958,3.17066L9.400459999999999,3.84496L9.369119999999999,3.9414100000000003L8.2011,7.53574C8.14754,7.70054,7.99397,7.81211,7.82069,7.81212L4.0413499999999996,7.81226L3.93993,7.81227L3.2309200000000002,7.81229ZM7.41429,10.26304L5.6927900000000005,9.0122L7.82074,9.01212Q8.98312,9.012080000000001,9.34235,7.90661L10,5.88283L10.65765,7.9066Q11.01688,9.012080000000001,12.1793,9.01212L14.3072,9.0122L12.5857,10.26304Q11.64535,10.9463,12.0045,12.0518L12.662,14.0756L10.9404,12.8249Q10,12.1417,9.05959,12.8249L7.338,14.0756L7.9955,12.0518Q8.35465,10.9463,7.41429,10.26304Z",fillRule:"evenodd",fill:"#000000",fillOpacity:1})))),dt=e=>i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:20,height:20,viewBox:"0 0 20 20",...e},i.createElement("defs",null,i.createElement("clipPath",{id:"master_svg0_101_30935"},i.createElement("rect",{x:0,y:0,width:20,height:20,rx:0}))),i.createElement("g",{clipPath:"url(#master_svg0_101_30935)"},i.createElement("g",null,i.createElement("path",{d:"M3.2309200000000002,7.81229C2.843441,7.81231,2.682339,8.30813,2.995804,8.53589L6.70891,11.23384C6.84909,11.33569,6.90775,11.51623,6.85421,11.68103L5.4360599999999994,16.0462C5.31634,16.4147,5.73811,16.7212,6.05159,16.4934L9.7649,13.7958C9.905090000000001,13.6939,10.09491,13.6939,10.2351,13.7958L13.9484,16.4934C14.2619,16.7212,14.6837,16.4147,14.5639,16.0462L13.1458,11.68103C13.0922,11.51623,13.1509,11.33569,13.2911,11.23384L17.0042,8.53589C17.317700000000002,8.30813,17.156599999999997,7.81231,16.7691,7.81229L12.1793,7.81212C12.006,7.81211,11.85246,7.70054,11.7989,7.53574L10.38042,3.17066C10.26067,2.80215,9.739329999999999,2.80215,9.61958,3.17066L8.2011,7.53574C8.14754,7.70054,7.99397,7.81211,7.82069,7.81212L3.2309200000000002,7.81229Z",fill:"#14C9BB",fillOpacity:1})))),Ca=e=>i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:20,height:20,viewBox:"0 0 20 20",...e},i.createElement("defs",null,i.createElement("clipPath",{id:"master_svg0_195_65048"},i.createElement("rect",{x:0,y:0,width:20,height:20,rx:0}))),i.createElement("g",{clipPath:"url(#master_svg0_195_65048)"},i.createElement("g",null,i.createElement("path",{d:"M3.2309200000000002,7.81229C2.843441,7.81231,2.682339,8.30813,2.995804,8.53589L3.5694,8.95266L3.65144,9.01228L6.70891,11.23384C6.84909,11.33569,6.90775,11.51623,6.85421,11.68103L5.68647,15.2754L5.65513,15.3719L5.4360599999999994,16.0462C5.31634,16.4147,5.73811,16.7212,6.05159,16.4934L6.62521,16.076700000000002L6.70726,16.0171L9.7649,13.7958C9.905090000000001,13.6939,10.09491,13.6939,10.2351,13.7958L13.2927,16.0171L13.3748,16.076700000000002L13.9484,16.4934C14.2619,16.7212,14.6837,16.4147,14.5639,16.0462L14.3449,15.3719L14.3135,15.2754L13.1458,11.68103C13.0922,11.51623,13.1509,11.33569,13.2911,11.23384L16.348599999999998,9.01228L16.4306,8.95266L17.0042,8.53589C17.317700000000002,8.30813,17.156599999999997,7.81231,16.7691,7.81229L16.0601,7.81227L15.9587,7.81226L12.1793,7.81212C12.006,7.81211,11.85246,7.70054,11.7989,7.53574L10.63089,3.9414100000000003L10.59954,3.84496L10.38042,3.17066C10.26067,2.80215,9.739329999999999,2.80215,9.61958,3.17066L9.400459999999999,3.84496L9.369119999999999,3.9414100000000003L8.2011,7.53574C8.14754,7.70054,7.99397,7.81211,7.82069,7.81212L4.0413499999999996,7.81226L3.93993,7.81227L3.2309200000000002,7.81229ZM7.41429,10.26304L5.6927900000000005,9.0122L7.82074,9.01212Q8.98312,9.012080000000001,9.34235,7.90661L10,5.88283L10.65765,7.9066Q11.01688,9.012080000000001,12.1793,9.01212L14.3072,9.0122L12.5857,10.26304Q11.64535,10.9463,12.0045,12.0518L12.662,14.0756L10.9404,12.8249Q10,12.1417,9.05959,12.8249L7.338,14.0756L7.9955,12.0518Q8.35465,10.9463,7.41429,10.26304Z",fillRule:"evenodd",fill:"#14C9BB",fillOpacity:1})))),ha=e=>i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:16,height:16,viewBox:"0 0 16 16",...e},i.createElement("defs",null,i.createElement("clipPath",{id:"master_svg0_101_30924"},i.createElement("rect",{x:0,y:0,width:16,height:16,rx:0}))),i.createElement("g",null,i.createElement("g",{clipPath:"url(#master_svg0_101_30924)"},i.createElement("g",null,i.createElement("path",{d:"M8.79152,10.846689999999999L12.79579,5.6000499999999995C13.0256,5.29887,13.0647,4.893348,12.89652,4.553846C12.72835,4.214342,12.38211,3.999667786,12.00324,4.000000197864L3.995932,4.000000197864C3.617212,3.9999837752,3.271259,4.214779,3.1033,4.554218C2.9353417,4.893657,2.9744491,5.29898,3.204206,5.6000499999999995L7.20848,10.846689999999999C7.60707,11.36869,8.39293,11.36869,8.79152,10.846689999999999Z",fill:"#212519",fillOpacity:1}))))),fa=e=>i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:24,height:24,viewBox:"0 0 24 24",...e},i.createElement("defs",null,i.createElement("clipPath",{id:"master_svg0_195_65388"},i.createElement("rect",{x:0,y:0,width:24,height:24,rx:0}))),i.createElement("g",{clipPath:"url(#master_svg0_195_65388)"},i.createElement("g",null,i.createElement("path",{d:"M20.81583984375,7.087728515625L14.93983984375,2.615729515625C14.22023984375,2.067530515625,13.34043984375,1.770986437625,12.43583984375,1.771729946135C11.55183984375,1.771729946135,10.67183984375,2.051730515625,9.93183984375,2.615729515625L4.05183984375,7.087728515625C3.53583984375,7.479728515625,3.23583984375,8.087728515624999,3.23583984375,8.735728515625L3.23583984375,17.351728515625C3.23583984375,19.447728515625,4.93583984375,21.143728515625,7.02783984375,21.143728515625L17.83983984375,21.143728515625C19.93583984375,21.143728515625,21.63183984375,19.443728515625,21.63183984375,17.351728515625L21.63183984375,8.735728515625C21.63583984375,8.087728515624999,21.33183984375,7.479728515625,20.81583984375,7.087728515625ZM20.03583984375,17.347728515625C20.03583984375,18.555728515625,19.051839843750003,19.539728515625,17.84383984375,19.539728515625L7.02783984375,19.539728515625C5.81983984375,19.539728515625,4.83583984375,18.555728515625,4.83583984375,17.347728515625L4.83583984375,8.735728515625C4.83583984375,8.591728515625,4.90383984375,8.451728515625,5.01983984375,8.363728515624999L10.89983984375,3.891728515625C11.34383984375,3.5517285156250002,11.87583984375,3.371728515625,12.43583984375,3.371728515625C12.99583984375,3.371728515625,13.52783984375,3.5517285156250002,13.97183984375,3.891728515625L19.85183984375,8.363728515624999C19.96783984375,8.451728515625,20.03583984375,8.591728515625,20.03583984375,8.735728515625L20.03583984375,17.347728515625Z",fill:"#212519",fillOpacity:1,style:{mixBlendMode:"passthrough"}}),i.createElement("path",{d:"M21.83183984375,17.351728515625L21.83183984375,8.736338515625L21.83183984375,8.735728515625Q21.83823984375,7.613298515625,20.93683984375,6.928478515625L15.06093984375,2.456579515625Q13.89793984375,1.570527515625,12.43567984375,1.571729515625Q10.97168984375,1.571729515625,9.810609843750001,2.456663515625L3.93076884375,6.928538515625Q3.03583984375,7.608408515625,3.03583984375,8.735728515625L3.03583984375,17.351728515625Q3.03583984375,19.005928515625,4.20596984375,20.175228515625Q5.37534984375,21.343728515625,7.02783984375,21.343728515625L17.83983984375,21.343728515625Q19.49403984375,21.343728515625,20.66333984375,20.173628515625Q21.83183984375,19.004228515625,21.83183984375,17.351728515625ZM20.69483984375,7.246988515625Q21.43753984375,7.811198515625,21.43183984375,8.734498515624999L21.43183984375,8.735728515625L21.43183984375,17.351728515625Q21.43183984375,18.838628515625,20.38033984375,19.890828515625Q19.32823984375,20.943728515625,17.83983984375,20.943728515625L7.02783984375,20.943728515625Q5.54094984375,20.943728515625,4.48870984375,19.892228515625Q3.43583984375,18.840128515625,3.43583984375,17.351728515625L3.43583984375,8.735728515625Q3.43583984375,7.806868515625,4.17291184375,7.246918515625L10.05307984375,2.774798515625Q11.10674984375,1.971730515625,12.43600984375,1.971729515625Q13.76303984375,1.970639515625,14.81873984375,2.7748785156249998L20.69483984375,7.246988515625ZM20.23583984375,17.347728515625L20.23583984375,8.735728515625Q20.23583984375,8.403998515625,19.97293984375,8.204538515625L14.09343984375,3.732938515625Q13.36053984375,3.171728515625,12.43583984375,3.171728515625Q11.51111984375,3.171728515625,10.77876984375,3.732538515625L4.89895984375,8.204388515625Q4.63583984375,8.403998515625,4.63583984375,8.735728515625L4.63583984375,17.347728515625Q4.63583984375,18.336628515625,5.33741984375,19.038128515625Q6.03899984375,19.739728515625,7.02783984375,19.739728515625L17.84383984375,19.739728515625Q18.83263984375,19.739728515625,19.53423984375,19.038128515625Q20.23583984375,18.336528515625,20.23583984375,17.347728515625ZM19.73073984375,8.522918515625001Q19.83583984375,8.602628515625,19.83583984375,8.735728515625L19.83583984375,17.347728515625Q19.83583984375,18.170928515625,19.25143984375,18.755328515625Q18.66703984375,19.339728515625,17.84383984375,19.339728515625L7.02783984375,19.339728515625Q6.20467984375,19.339728515625,5.62025984375,18.755328515625Q5.03583984375,18.170928515625,5.03583984375,17.347728515625L5.03583984375,8.735728515625Q5.03583984375,8.602628515625,5.14071984375,8.523068515624999L11.020909843750001,4.050918515625Q11.64667984375,3.571728515625,12.43583984375,3.571728515625Q13.22499984375,3.571728515625,13.85023984375,4.050518515625L19.73073984375,8.522918515625001Z",fillRule:"evenodd",fill:"#212519",fillOpacity:1})),i.createElement("g",null,i.createElement("path",{d:"M12.432265625,10.688232421875C10.304265625,10.688232421875,8.572265625,12.420232421875,8.572265625,14.548232421875L8.572265625,17.232232421875C8.572265625,17.672232421875,8.932265625,18.032232421875,9.372265625,18.032232421875C9.812265625,18.032232421875,10.172265625,17.672232421875,10.172265625,17.232232421875L10.172265625,14.548232421875C10.172265625,13.304232421875,11.184265625,12.288232421875,12.432265625,12.288232421875C13.680265625,12.288232421875,14.692265625000001,13.300232421875,14.692265625000001,14.548232421875L14.692265625000001,17.232232421875C14.692265625000001,17.672232421875,15.052265625,18.032232421875,15.492265625,18.032232421875C15.932265625,18.032232421875,16.292265625,17.672232421875,16.292265625,17.232232421875L16.292265625,14.548232421875C16.292265625,12.420232421875,14.564265625,10.688233852385,12.432265625,10.688232421875Z",fill:"#212519",fillOpacity:1,style:{mixBlendMode:"passthrough"}}),i.createElement("path",{d:"M16.492265625,17.232232421875L16.492265625,14.548232421875Q16.492265625,12.868742421875,15.303235625,11.678862421875Q14.113455625,10.488233421875,12.432265625,10.488232421875Q10.753425625,10.488232421875,9.562844625,11.678811421875Q8.372265625,12.869392421875,8.372265625,14.548232421875L8.372265625,17.232232421875Q8.372265625,17.645072421875,8.665843925,17.938652421875Q8.959422625,18.232232421875,9.372265625,18.232232421875Q9.785105625,18.232232421875,10.078685625,17.938652421875Q10.372265625,17.645072421875,10.372265625,17.232232421875L10.372265625,14.548232421875Q10.372265625,13.697312421875,10.975775625,13.093072421875Q11.579865625,12.488232421875,12.432265625,12.488232421875Q13.285425625,12.488232421875,13.888845625,13.091652421875Q14.492265625,13.695072421875,14.492265625,14.548232421875L14.492265625,17.232232421875Q14.492265625,17.645072421875,14.785845625,17.938652421875Q15.079425624999999,18.232232421875,15.492265625,18.232232421875Q15.905105625000001,18.232232421875,16.198685625,17.938652421875Q16.492265625,17.645072421875,16.492265625,17.232232421875ZM15.020295625,11.961602421875Q16.092265625,13.034342421875,16.092265625,14.548232421875L16.092265625,17.232232421875Q16.092265625,17.479392421874998,15.915845625,17.655812421874998Q15.739415625,17.832232421875,15.492265625,17.832232421875Q15.245105625,17.832232421875,15.068685625,17.655812421874998Q14.892265625,17.479392421874998,14.892265625,17.232232421875L14.892265625,14.548232421875Q14.892265625,13.529392421875,14.171685625,12.808812421875Q13.451105625,12.088232421875,12.432265625,12.088232421875Q11.414045625,12.088232421875,10.692755625,12.810402421875Q9.972265625,13.531772421875,9.972265625,14.548232421875L9.972265625,17.232232421875Q9.972265625,17.479392421874998,9.795845625,17.655812421874998Q9.619425625,17.832232421875,9.372265625,17.832232421875Q9.125107625,17.832232421875,8.948686625,17.655812421874998Q8.772265625,17.479392421874998,8.772265625,17.232232421875L8.772265625,14.548232421875Q8.772265625,13.035072421875,9.845685625,11.961652421875Q10.919105625,10.888232421875,12.432265625,10.888232421875Q13.947695625,10.888233421875,15.020295625,11.961602421875Z",fillRule:"evenodd",fill:"#212519",fillOpacity:1})))),Oa=({onChange:e})=>{var R;const{toggleFavorite:t,getFirstVenueFavoriteId:n}=vr(),r=dr(),[o,l]=i.useState(!1),c=ur(),[s,g]=i.useState(null),[u,C]=i.useState(!1),[m,A]=i.useState(!1),{selectedSiteId:d,handleSiteChange:I}=xr(!1),f=r.data?[...r.data].sort((S,W)=>String(S.id)==="0"?-1:String(W.id)==="0"?1:S.name.localeCompare(W.name)).map(S=>({label:S.name,value:S.id,type:"venue"})):[],j=f.reduce((S,W)=>Math.max(S,W.label.length),0),E=S=>n()===S,L=window.location.hash?window.location.hash.substring(1):null,N=L&&f.some(S=>S.value===L)?L:d,w=((R=f.find(S=>S.value===N))==null?void 0:R.label)||"";i.useEffect(()=>{A(E(N))},[N,n()]);const B=a.jsxs(we,{style:{minWidth:250,borderRadius:8,padding:"10px 0 10px 0"},children:[f.map(S=>a.jsxs(we.Item,{onClick:()=>{var _;(_=r.data)!=null&&_.some(D=>D.id===S.value)&&(I(S.value),e==null||e(S.value))},onMouseEnter:()=>g(S.value),onMouseLeave:()=>g(null),style:{height:30,lineHeight:"30px",fontSize:16,backgroundColor:N===S.value?"rgba(20, 201, 187, 0.1)":s===S.value?"#F4F5F7":"transparent",color:N===S.value?"#14C9BB":"#000",margin:"0 8px",borderRadius:4,transition:"background-color 0.3s",position:"relative",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[a.jsx("span",{style:{display:"inline-block",minWidth:`${j}ch`},children:S.label}),E(S.value)&&a.jsx(ie,{component:dt,style:{width:16,height:16,color:"#14C9BB",marginLeft:120}})]},S.value)),a.jsx(we.Divider,{style:{borderTop:"1px solid #E7E7E7",marginBottom:-6,marginTop:8}}),a.jsx(we.Item,{onClick:()=>c("/resource/site_management"),style:{display:"flex",justifyContent:"center",width:"100%",height:30,color:u?"#34DCCF":"#14C9BB",background:"transparent",margin:0,borderRadius:0},onMouseEnter:()=>C(!0),onMouseLeave:()=>C(!1),children:a.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",gap:8,whiteSpace:"nowrap",color:u?"#34DCCF":"#14C9BB"},children:[a.jsx(mr,{style:{width:16,height:16,color:u?"#34DCCF":"#14C9BB",marginBottom:-13}}),a.jsx("span",{style:{fontWeight:400,fontSize:15,marginBottom:-13},children:"Create New Site"})]})},"create")]});return!r.data||r.data.length===0?a.jsxs(K,{type:"primary",onClick:()=>c("/resource/site_management"),style:{backgroundColor:"#14C9BB",borderColor:"#14C9BB"},children:[a.jsx(ie,{component:Xe}),"Create New Site"]}):a.jsx(Cr,{overlay:B,trigger:["click"],overlayStyle:{minWidth:250},children:a.jsxs("div",{style:{display:"flex",alignItems:"center",position:"relative",cursor:"pointer"},children:[a.jsx(ie,{component:fa,style:{height:32,color:"#212519",marginRight:4,fontSize:26}}),a.jsx("span",{style:{marginRight:4,height:"24px",fontFamily:"Lato,Lato",fontWeight:700,fontSize:"20px",color:"#212529",textAlign:"left",fontStyle:"normal",lineHeight:"24px",textTransform:"none",whiteSpace:"nowrap"},children:w}),a.jsx("div",{style:{display:"flex",alignItems:"center",marginRight:8,cursor:"pointer",position:"relative",padding:"1px"},onMouseEnter:()=>l(!0),onMouseLeave:()=>l(!1),onClick:S=>{S.preventDefault(),S.stopPropagation();const W=!m;A(W),t(N,W)},children:a.jsx(ie,{component:m?dt:o?Ca:ma,style:{fontSize:380,transition:"all 0.3s",width:"22px",height:"22px",marginRight:-5}})}),a.jsx(ie,{component:ha,style:{fontSize:"18px",color:"#666",marginLeft:4,verticalAlign:"middle"}})]})})},ut=/^(?=.{1,254}$)((?=[a-z0-9-]{1,63}\.)(xn--+)?[a-z0-9]+(-[a-z0-9]+)*\.)+[a-z]{2,63}$/i,ke=/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,Oe=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:))$/,mt=/^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$/,Ha=e=>`__variableBlock__<<${e}>>`;function Ba(e){const t=e.match(/__variableBlock__<<(.+?)>>/);return t?t[1]:e}function Fe(e){return e?Array.isArray(e)?new Promise((t,n)=>{for(const r of e){const o=r.trim();if(o&&!(ut.test(o)||ke.test(o)||Oe.test(o))){n(new Error(`Invalid URL or IP format: ${o}`));return}}t()}):ut.test(e)||ke.test(e)||Oe.test(e)?Promise.resolve():Promise.reject(new Error("Invalid URL or IP format")):Promise.resolve()}function $a(e){return e?Array.isArray(e)?new Promise((t,n)=>{for(const r of e){const o=r.trim();if(o&&!(ke.test(o)||Oe.test(o))){n(new Error(`Invalid IP format: ${o}`));return}}t()}):ke.test(e)||Oe.test(e)?Promise.resolve():Promise.reject(new Error("Invalid IP format")):Promise.resolve()}function pa(e){return e?Array.isArray(e)?new Promise((t,n)=>{for(const r of e){const o=r.trim();if(o&&!mt.test(o)){n(new Error(`${o} Invalid MAC value, for example: 00:00:5e:00:53:af`));return}}t()}):mt.test(e)?Promise.resolve():Promise.reject(new Error("Invalid MAC value, for example: 00:00:5e:00:53:af")):Promise.resolve()}function Ta(e){if(e==null||e==="")return"";let t;if(typeof e=="number")t=e<1e12?e*1e3:e;else{const u=e.trim();if(!/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(u))throw new Error('Input must be a timestamp or a string in format "YYYY-MM-DD HH:mm:ss"');const m=u.substring(0,10),A=u.substring(11),d=m.split("-");if(d.length!==3)throw new Error(`Invalid UTC time string: ${e}`);const I=Number(d[0]),f=Number(d[1]),j=Number(d[2]);if(Number.isNaN(I)||Number.isNaN(f)||Number.isNaN(j))throw new Error(`Invalid UTC time string: ${e}`);const E=A.split(":"),L=Number(E[0]||0),N=Number(E[1]||0),w=Number(E[2]||0);if(Number.isNaN(L)||Number.isNaN(N)||Number.isNaN(w))throw new Error(`Invalid UTC time string: ${e}`);t=Date.UTC(I,f-1,j,L,N,w)}const n=new Date(t),r=n.getFullYear(),o=String(n.getMonth()+1).padStart(2,"0"),l=String(n.getDate()).padStart(2,"0"),c=String(n.getHours()).padStart(2,"0"),s=String(n.getMinutes()).padStart(2,"0"),g=String(n.getSeconds()).padStart(2,"0");return`${r}-${o}-${l} ${c}:${s}:${g}`}const Qe=e=>{if(e===null||typeof e!="object")return e;if(Array.isArray(e))return e.map(n=>Qe(n));const t={};for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)){const r=Qe(e[n]);r!=null&&r!==""&&(t[n]=r)}return t},be="/ampcon/wireless";function He({site_id:e,name:t,description:n,parameter:r,config_variables:o,type:l}){return Ie({url:`${be}/profile`,method:"POST",data:{site_id:e,type:l,name:t,parameter:r,description:n,config_variables:o}})}function Be({id:e,site_id:t,name:n,description:r,parameter:o,config_variables:l,type:c}){return Ie({url:`${be}/profile`,method:"PUT",data:{id:e,site_id:t,type:c,name:n,parameter:o,description:r,config_variables:l}})}function Wa({id:e}){return Ie({url:`${be}/profile`,method:"DELETE",data:{id:e}})}function Ma(e,t,n,r,o=[],l=[],c=[],s={}){return Ie({url:`${be}/profile/list`,method:"POST",data:{type:e,site_id:t,filterFields:o,parameterFilter:c,sortFields:l,searchFields:s,page:n,pageSize:r}})}function Da(e){return Ie({url:`${be}/profile/${e}`,method:"GET"})}const je=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Ia={Monday:1,Tuesday:2,Wednesday:3,Thursday:4,Friday:5,Saturday:6,Sunday:7};function Aa(e){const t=new Map;return e.forEach(({day:n,start:r,end:o})=>{const l=`${r}-${o}`;t.has(l)||t.set(l,{days:[],start:r,end:o}),t.get(l).days.push(n)}),Array.from(t.values()).map(n=>({days:n.days.sort((r,o)=>r-o),start:n.start,end:n.end}))}const Ga=({visible:e,resource:t,siteId:n,onClose:r,onSuccess:o})=>{const[l]=P.useForm(),[c,s]=i.useState(je.map(d=>({day:d,checked:!1,startTime:null,endTime:null})));i.useEffect(()=>{if(t&&e){l.setFieldsValue({name:t.name||"",description:t.description||""});let d={};if(t.parameter)try{d=typeof t.parameter=="string"?JSON.parse(t.parameter):t.parameter}catch{}if(Array.isArray(d.time_range)){const I=je.map(f=>{const j=d.time_range.find(E=>E.startsWith(f));if(j){const E=j.match(/^(\w+)\s+(\d{2}:\d{2})-(\d{2}:\d{2})$/);if(E){const[,,L,N]=E;return{day:f,checked:!0,startTime:ue(L,"HH:mm"),endTime:ue(N,"HH:mm")}}}return{day:f,checked:!1,startTime:null,endTime:null}});s(I);return}}else e||(l.resetFields(),s(je.map(d=>({day:d,checked:!1,startTime:null,endTime:null}))))},[t,l,e]);const g=(d,I)=>{const f=[...c];f[d].checked=I,I?(f[d].startTime=f[d].startTime||ue("08:00","HH:mm"),f[d].endTime=f[d].endTime||ue("18:00","HH:mm")):(f[d].startTime=null,f[d].endTime=null),s(f)},u=(d,I)=>{const f=[...c];f[d].startTime=I,I&&f[d].endTime&&I.isAfter(f[d].endTime)&&(f[d].endTime=I),s(f)},C=(d,I)=>{const f=[...c];f[d].endTime=I,I&&f[d].startTime&&I.isBefore(f[d].startTime)&&(f[d].startTime=I),s(f)},m=async()=>{if(!c.some(f=>f.checked)){M.error("Please select at least one day.");return}if(c.some(f=>f.checked&&(!f.startTime||!f.endTime))){M.error("Please select a valid time");return}try{const f=await l.validateFields(),j=c.filter(R=>R.checked).map(R=>`${R.day} ${R.startTime.format("HH:mm")}-${R.endTime.format("HH:mm")}`),E=j.map(R=>{const[S,W]=R.split(" "),[_,D]=W.split("-");return{day:Ia[S],start:_,end:D}}),L=Aa(E),N={site_id:n,type:4,name:f.name,description:f.description||"",parameter:{time_range:j},config_variables:JSON.stringify(L)};let w;t&&t.id?w=await Be({...N,id:t.id}):w=await He(N);const B=w.data||w;w.status===200?(M.success(t?"Updated successfully":"Created successfully"),o==null||o(),r(!0),l.resetFields(),s(je.map(R=>({day:R,checked:!1,startTime:null,endTime:null})))):M.error(t?`Update failed: ${B.info||"Unknown error"}`:`Create failed: ${B.info||"Unknown error"}`)}catch{}},A=()=>{l.resetFields(),r()};return a.jsx(yt,{title:a.jsxs("div",{children:[t?"Edit Time Range Profile":"Create Time Range Profile",a.jsx(Ce,{style:{marginTop:8,marginBottom:0}})]}),open:e,onCancel:A,className:"ampcon-max-modal",footer:[a.jsx(Ce,{style:{margin:"0px 0px 16px -24px",width:"calc(100% + 48px)"}}),a.jsx(K,{onClick:A,children:"Cancel"},"cancel"),a.jsx(K,{type:"primary",onClick:m,children:"Apply"},"apply")],zIndex:1060,destroyOnClose:!0,children:a.jsx(P,{form:l,layout:"horizontal",labelAlign:"left",labelCol:{span:4},wrapperCol:{span:10},children:a.jsx(ba,{form:l,timeRanges:c,onCheckChange:g,onStartTimeChange:u,onEndTimeChange:C})})})},ba=({form:e,timeRanges:t,onCheckChange:n,onStartTimeChange:r,onEndTimeChange:o})=>{const{t:s}=Ae(),g=m=>m?m.hour()*60+m.minute():0,u=m=>ue().hour(Math.floor(m/60)).minute(m%60),C=Array.from({length:13}).reduce((m,A,d)=>{const I=d*2;return m[I*60]={label:a.jsxs("div",{style:{display:"flex",flexDirection:"column",marginBottom:0,alignItems:"center"},children:[a.jsx("div",{style:{width:1,height:8,backgroundColor:"#DADCE1",marginBottom:0,marginTop:-8}}),a.jsx("span",{style:{fontSize:12,color:"#474747"},children:I})]})},m},{});return a.jsxs(a.Fragment,{children:[a.jsx(P.Item,{label:"Name",name:"name",rules:[{required:!0,message:"Required!"},{max:32,message:"Name cannot exceed 32 characters"}],labelCol:{span:2,style:{marginRight:24}},wrapperCol:{span:5},children:a.jsx(G,{placeholder:"Enter name"})}),a.jsx(P.Item,{label:"Description",name:"description",rules:[{max:128,message:s("form.max_length",{max:128})}],labelCol:{span:2,style:{marginRight:24}},wrapperCol:{span:5},children:a.jsx(G.TextArea,{rows:2,placeholder:"Enter description"})}),a.jsx("div",{style:{marginBottom:69},children:t.map((m,A)=>a.jsxs(V,{align:"middle",children:[a.jsx(T,{span:2,style:{marginRight:24},children:a.jsx(hr,{checked:m.checked,onChange:d=>n(A,d.target.checked),children:m.day})}),a.jsx(T,{span:2,children:a.jsx(he,{value:m.startTime,onChange:d=>{const I=m.endTime;d&&I&&!I.isAfter(d)&&o(A,d.add(1,"minute")),r(A,d)},disabled:!m.checked,format:"HH:mm",style:{width:"100%"},allowClear:!1,inputReadOnly:!0,placeholder:""})}),a.jsx(T,{span:1,style:{textAlign:"center"},children:a.jsx("div",{style:{width:"18px",borderBottom:"1px solid #B2B2B2 ",margin:"0 auto"}})}),a.jsx(T,{span:2,children:a.jsx(he,{value:m.endTime,onChange:d=>{const I=m.startTime;d&&g(d)>1439&&(d=ue().hour(23).minute(59)),d&&I&&!d.isAfter(I)&&(d=I.add(1,"minute")),o(A,d)},disabled:!m.checked,format:"HH:mm",style:{width:"100%"},allowClear:!1,inputReadOnly:!0,placeholder:""})}),a.jsx(T,{span:7,style:{paddingLeft:16},children:a.jsx("div",{style:{width:"100%",padding:"0 4px"},children:a.jsx(fr,{range:!0,min:0,max:1439,step:1,disabled:!m.checked,value:m.checked?[g(m.startTime),g(m.endTime)]:[0,0],onChange:([d,I])=>{if(!m.checked)return;I>1439&&(I=1439);let f=u(d),j=u(I);j.isAfter(f)||(j=f.add(1,"minute")),r(A,f),o(A,j)},marks:C,dotStyle:{display:"none"},activeDotStyle:{display:"none"},trackStyle:m.checked?[{backgroundColor:"#52c41a",height:8}]:[{backgroundColor:"transparent",height:8}],railStyle:{backgroundColor:"#eee",height:8},handleStyle:[{display:"none"},{display:"none"}]})})})]},m.day))})]})},Ct={mac:"","user-name":"",password:"","vlan-id":1},va=({open:e,onCancel:t,onOk:n,isDisabled:r,existingUsers:o=[]})=>{const{t:l}=Ae(),[c]=P.useForm();return i.useEffect(()=>{e&&c.setFieldsValue(Ct)},[e]),a.jsxs(Ye,{open:e,title:"Add User",onCancel:t,onFinish:async s=>{const{mac:g,"user-name":u,password:C}=s;if(o.some(A=>{var d;return((d=A.mac)==null?void 0:d.toLowerCase())===g.toLowerCase()&&A["user-name"]===u&&A.password===C})){M.error("User with the same MAC, username and password already exists.");return}n(s),c.resetFields()},initialValues:Ct,form:c,modalClass:"ampcon-middle-modal",children:[a.jsx(P.Item,{label:"MAC",name:"mac",rules:[{validator:(s,g)=>pa(g)}],children:a.jsx(G,{disabled:r})}),a.jsx(P.Item,{label:"User Name",name:"user-name",rules:[{required:!0,message:l("form.required")}],children:a.jsx(G,{disabled:r})}),a.jsx(P.Item,{label:"Password",name:"password",rules:[{required:!0,message:l("form.required")},{min:8,max:63,message:l("form.min_max_string",{min:8,max:63})}],children:a.jsx(G.Password,{disabled:r})}),a.jsx(P.Item,{label:"VLAN ID",name:"vlan-id",rules:[{required:!0,message:l("form.required")},{type:"number",max:4094,message:"vlan-id must be less than 4095"},{type:"number",min:1,message:"vlan-id must be greater than 0"}],children:a.jsx(me,{disabled:r})})]})},xa={name:"",description:"",mode:"External",authentication:{host:"***************",port:1812,secret:"YOUR_SECRET","mac-filter":!1},accounting:void 0,"dynamic-authorization":void 0,"nas-identifier":"","chargeable-user-id":!1,local:{users:[]}},ya=e=>{if(!e)return{...xa};let t={},n="External";try{if(e.config_variables&&(t=JSON.parse(e.config_variables)),e.parameter){let r=e.parameter;if(typeof r=="string")try{r=JSON.parse(r)}catch{}r&&r.type&&(n=r.type)}}catch{}return{...t,name:e.name,description:e.description,mode:n}},_a=({isDisabled:e=!1,resource:t,onClose:n,refresh:r,siteId:o,open:l=!1,disableMode:c=!1})=>{const{t:s}=Ae(),[g]=P.useForm(),u=ya(t),[C,m]=i.useState(u.mode),[A,d]=i.useState(!1),[I,f]=i.useState(!!(u&&u.accounting)),[j,E]=i.useState(!!(u&&u["dynamic-authorization"])),L=async h=>{var O,z;d(!0);const v=Qe(h);try{const le={type:h.mode||"External",auth_server_host:((O=h.authentication)==null?void 0:O.host)||"-",port:((z=h.authentication)==null?void 0:z.port)||"-"},{name:fe,mode:ve,description:ge,...xe}=v,U=JSON.stringify(xe);let Z;if(t&&t.id){if(Z=await Be({id:t.id,site_id:o,type:1,name:h.name,parameter:le,description:h.description,config_variables:U}),(Z==null?void 0:Z.status)!==200){M.error((Z==null?void 0:Z.info)||s("crud.error_update_obj",{obj:s("resources.configuration_resource")})),d(!1);return}M.success(s("crud.success_update_obj",{obj:s("resources.configuration_resource")}))}else{if(Z=await He({site_id:o,type:1,name:h.name,parameter:le,description:h.description,config_variables:U}),(Z==null?void 0:Z.status)!==200){M.error((Z==null?void 0:Z.info)||s("crud.error_create_obj",{obj:s("resources.configuration_resource")})),d(!1);return}M.success(s("crud.success_create_obj",{obj:s("resources.configuration_resource")}))}r&&r(),n&&n(!0)}catch{M.error(s("crud.error_create_obj",{obj:s("resources.configuration_resource")}))}finally{d(!1)}},N=C==="Local",w=N||e,[B,R]=i.useState(1),[S,W]=i.useState(10),_=P.useWatch(["local","users"],g)||[],[D,Q]=i.useState({}),[q,x]=i.useState(!1),y=()=>x(!0),$=h=>{const v={...h};v.mac&&(v.mac=v.mac.toLowerCase()),g.setFieldValue(["local","users"],[v,..._]),x(!1),R(1)},F=h=>{const v=_.findIndex(z=>{const le=String(z.mac||"")===String(h.mac||""),fe=String(z["user-name"]||"")===String(h["user-name"]||""),ve=String(z.password||"")===String(h.password||""),ge=String(z["vlan-id"]??"")===String(h["vlan-id"]??"");return le&&fe&&ve&&ge});if(v===-1)return;const O=[..._];O.splice(v,1),g.setFieldValue(["local","users"],O),(B-1)*S>=O.length&&B>1&&R(B-1)},k=[{title:"MAC",dataIndex:"mac",key:"mac",sorter:(h,v)=>String(h.mac||"").localeCompare(String(v.mac||""))},{title:"User Name",dataIndex:"user-name",key:"user-name",sorter:(h,v)=>String(h["user-name"]||"").localeCompare(String(v["user-name"]||""))},{title:"Password",dataIndex:"password",key:"password",sorter:(h,v)=>String(h.password||"").localeCompare(String(v.password||""))},{title:"VLAN ID",dataIndex:"vlan-id",key:"vlan-id",sorter:(h,v)=>{const O=h["vlan-id"]===void 0||h["vlan-id"]===null||h["vlan-id"]===""?Number.NEGATIVE_INFINITY:Number(h["vlan-id"]),z=v["vlan-id"]===void 0||v["vlan-id"]===null||v["vlan-id"]===""?Number.NEGATIVE_INFINITY:Number(v["vlan-id"]);return O-z}},{title:"Operation",key:"action",render:(h,v)=>a.jsx(K,{type:"text",onClick:()=>F(v),children:"Delete"})}],H=()=>a.jsxs(a.Fragment,{children:[a.jsxs(V,{gutter:24,children:[a.jsx(T,{span:8,style:{display:"none"},children:a.jsx(P.Item,{name:["local","server-identity"],label:"Server Identity",initialValue:"uCentral",children:a.jsx(G,{disabled:e})})}),a.jsx(T,{span:24,style:{display:"none"},children:a.jsx(P.List,{name:["local","users"],children:h=>a.jsx(a.Fragment,{children:h.map(()=>null)})})})]}),a.jsx(V,{gutter:24,children:a.jsxs(T,{span:24,children:[a.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:24},children:a.jsx(K,{type:"primary",icon:a.jsx(ie,{component:Xe}),onClick:y,disabled:e,children:"User"})}),a.jsx(wt,{columns:k,dataSource:_,pagination:{current:B,pageSize:S,total:_.length,showTotal:(h,v)=>`${v[0]}-${v[1]} of ${h} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["5","10","20","50"]},style:{marginBottom:24},onChange:(h,v,O)=>{R(h.current||1),W(h.pageSize||10),Q(O)},rowKey:(h,v)=>typeof v=="number"?v.toString():"",size:"middle",bordered:!0}),a.jsx(va,{open:q,onCancel:()=>x(!1),onOk:$,isDisabled:e,existingUsers:_})]})})]}),p=()=>a.jsxs(a.Fragment,{children:[a.jsxs(V,{gutter:24,children:[a.jsx(T,{span:12,children:a.jsx(P.Item,{name:["authentication","host"],label:"Authentication Host",rules:[{required:!0,message:s("form.required")},{validator:(h,v)=>Fe(v)}],children:a.jsx(G,{disabled:w})})}),a.jsx(T,{span:12,children:a.jsx(P.Item,{name:["authentication","port"],label:"Authentication Port",rules:[{required:!0,message:s("form.required")},{type:"number",min:1,message:"radius.authentication.port must be a positive number"},{type:"number",max:65534,message:"radius.authentication.port must be less than 65535"}],children:a.jsx(me,{disabled:w})})})]}),a.jsx(V,{gutter:24,children:a.jsx(T,{span:12,children:a.jsx(P.Item,{name:["authentication","secret"],label:"Authentication Secret",rules:[{required:!0,message:s("form.required")}],children:a.jsx(G.Password,{disabled:w})})})}),a.jsx(V,{gutter:24,children:a.jsx(T,{span:12,children:a.jsx(P.Item,{name:["authentication","mac-filter"],label:"MAC Filter",valuePropName:"checked",children:a.jsx(Se,{disabled:w})})})}),a.jsx("h3",{className:"header2",style:{marginBottom:0}}),a.jsx(V,{gutter:24,children:a.jsx(T,{span:12,children:a.jsx(P.Item,{label:"Enable Accounting",children:a.jsx(Se,{checked:I,disabled:w,onChange:h=>{f(h),h?g.setFieldsValue({accounting:{host:"***************",port:1813,secret:"YOUR_SECRET"}}):g.setFieldsValue({accounting:void 0})}})})})}),I&&a.jsxs(a.Fragment,{children:[a.jsxs(V,{gutter:24,children:[a.jsx(T,{span:12,children:a.jsx(P.Item,{name:["accounting","host"],label:"Accounting Host",rules:[{required:!0,message:s("form.required")},{validator:(h,v)=>Fe(v)}],children:a.jsx(G,{disabled:w})})}),a.jsx(T,{span:12,children:a.jsx(P.Item,{name:["accounting","port"],label:"Accounting Port",rules:[{required:!0,message:s("form.required")},{type:"number",min:1,message:"accounting.port must be a positive number"},{type:"number",max:65534,message:"accounting.port must be less than 65535"}],children:a.jsx(me,{disabled:w})})})]}),a.jsx(V,{gutter:24,children:a.jsx(T,{span:12,children:a.jsx(P.Item,{name:["accounting","secret"],label:"Accounting Secret",rules:[{required:!0,message:s("form.required")}],children:a.jsx(G.Password,{disabled:w})})})})]}),a.jsx("h3",{className:"header2",style:{marginBottom:0}}),a.jsx(V,{gutter:24,children:a.jsx(T,{span:12,children:a.jsx(P.Item,{label:"Enable Dynamic Auth",children:a.jsx(Se,{checked:j,disabled:w,onChange:h=>{E(h),h?g.setFieldsValue({"dynamic-authorization":{host:"***************",port:1814,secret:"YOUR_SECRET"}}):g.setFieldsValue({"dynamic-authorization":void 0})}})})})}),j&&a.jsxs(a.Fragment,{children:[a.jsxs(V,{gutter:24,children:[a.jsx(T,{span:12,children:a.jsx(P.Item,{name:["dynamic-authorization","host"],label:"Dynamic Auth Host",rules:[{required:!0,message:s("form.required")},{validator:(h,v)=>Fe(v)}],children:a.jsx(G,{disabled:w})})}),a.jsx(T,{span:12,children:a.jsx(P.Item,{name:["dynamic-authorization","port"],label:"Dynamic Auth Port",rules:[{required:!0,message:s("form.required")},{type:"number",min:1,message:"dynamic-authorization.port must be a positive number"},{type:"number",max:65534,message:"dynamic-authorization.port must be less than 65535"}],children:a.jsx(me,{disabled:w})})})]}),a.jsx(V,{gutter:24,children:a.jsx(T,{span:12,children:a.jsx(P.Item,{name:["dynamic-authorization","secret"],label:"Dynamic Auth Secret",rules:[{required:!0,message:s("form.required")}],children:a.jsx(G.Password,{disabled:w})})})})]}),a.jsx("h3",{className:"header2",style:{marginBottom:0}}),a.jsxs(V,{gutter:24,children:[a.jsx(T,{span:12,children:a.jsx(P.Item,{name:"nas-identifier",label:"NAS Identifier",children:a.jsx(G,{disabled:w})})}),a.jsx(T,{span:12,children:a.jsx(P.Item,{name:"chargeable-user-id",label:"Chargeable User ID",valuePropName:"checked",children:a.jsx(Se,{disabled:w})})})]})]});return a.jsxs(Ye,{open:l,title:t?"Edit SSID Radius Profile":"Create SSID Radius Profile",onCancel:()=>n(!1),onFinish:L,initialValues:u,form:g,onValuesChange:(h,v)=>{v.mode!==C&&m(v.mode)},children:[a.jsx(V,{gutter:24,children:a.jsx(T,{span:12,children:a.jsx(P.Item,{name:"name",label:s("common.name"),rules:[{required:!0,message:s("form.required")},{type:"string",max:32,message:s("form.max_length",{max:32})}],children:a.jsx(G,{disabled:e})})})}),a.jsx(V,{gutter:24,children:a.jsx(T,{span:12,children:a.jsx(P.Item,{name:"mode",label:"Mode",rules:[{required:!0,message:s("form.required")}],children:a.jsx(de,{disabled:e||c,options:[{label:"External",value:"External"},{label:"Local",value:"Local"}]})})})}),a.jsx(V,{gutter:24,children:a.jsx(T,{span:12,children:a.jsx(P.Item,{name:"description",label:s("common.description"),rules:[{max:128,message:s("form.max_length",{max:128})}],children:a.jsx(G.TextArea,{disabled:e,rows:2})})})}),N?H():p()]})},ht="/assets/click-B3Odzm4Q.htm",wa="/assets/radius-mIi39hed.htm",Sa="/assets/radius-mIi39hed.htm",ja="data:text/html;base64,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",Ea="data:text/html;base64,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";async function Pa(e,t,n=!1){let r="click.html";t=t==null?void 0:t.toLowerCase(),t==="credentials"?r="credentials.html":t==="radius"&&(r="radius.html");try{const o=[{html:e,filename:r}];if(n){const[c,s]=await Promise.all([fetch(ja).then(g=>g.text()),fetch(Ea).then(g=>g.text())]);o.push({html:c,filename:"allow.html"},{html:s,filename:"connected.html"})}const l=await pr({data:o});if(l.status===200)return l.data;throw new Error(l.info||"Failed to convert HTML to Base64")}catch(o){throw console.error(o),o}}const se={name:"",description:"",mode:"Click",backgroundColor:"#F0F8F9",logoBase64:"",welcomeMessage:"Welcome to use Wi-Fi",termsOfService:"",corporateInfo:"© 2025 FS.COM INC. All rights reserved"},ee={welcomeMessage:31,termsOfService:200,corporateInfo:50,logo:10},Fa=({isDisabled:e=!1,resource:t,onClose:n,refresh:r,siteId:o,parameterMode:l,open:c})=>{const{t:s}=Ae(),[g,u]=i.useState(""),[C,m]=i.useState(""),[A,d]=i.useState(se.mode),[I,f]=i.useState(se.welcomeMessage),[j,E]=i.useState(se.termsOfService),[L,N]=i.useState(se.corporateInfo),[w,B]=i.useState(se.backgroundColor),[R,S]=i.useState(se.logoBase64),W=i.useRef(null),[_,D]=i.useState(!1),[Q]=P.useForm();i.useEffect(()=>{l&&l!==A&&(d(l),Q.setFieldsValue({mode:l}))},[l]);const q=p=>{switch(p){case"Click":return ht;case"Radius":return Sa;case"Credentials":return wa;default:return ht}},x=(p,h)=>{let v=p;return h.backgroundColor&&(v=v.replace(/background-color:\s*(?:unset|#[a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)\s*;?/i,`background-color: ${h.backgroundColor};`)),h.logoBase64&&(v=v.replace(/<img id="logo"[^>]*src="[^"]*"[^>]*>/,`<img id="logo" src="${h.logoBase64}" alt="logo" />`)),v=v.replace(/<p class="lead" id="title">.*?<\/p>/,`<p class="lead" id="title">${h.welcomeMessage}</p>`),v=v.replace(/<div[^>]*id="readmeTxt"[^>]*>([\s\S]*?)<\/div>/,`<div id="readmeTxt">${h.termsOfService}</div>`),v=v.replace(/<div[^>]*id="corporate-info"[^>]*>([\s\S]*?)<\/div>/,`<div id="corporate-info">${h.corporateInfo}</div>`),v},y=i.useCallback(Me(p=>f(p),400),[]),$=i.useCallback(Me(p=>E(p),400),[]),F=i.useCallback(Me(p=>N(p),400),[]);i.useEffect(()=>{const p=q(A);fetch(p).then(h=>h.text()).then(h=>{m(h);const v=Q.getFieldsValue();u(x(h,{...v,backgroundColor:w,logoBase64:R,welcomeMessage:I,termsOfService:j,corporateInfo:L}))}).catch(h=>console.error("Failed to load defaultHtml:",h))},[A]),i.useEffect(()=>{if(!C)return;const p=Q.getFieldsValue();u(x(C,{...p,backgroundColor:w,logoBase64:R,welcomeMessage:I,termsOfService:j,corporateInfo:L}))},[I,j,L,C,w,R]),i.useEffect(()=>{if(t){const p=t.parameter||{};d(p.mode),B(p.background),S(p.logo),Q.setFieldsValue({name:t.name,description:t.description,mode:p.mode,backgroundColor:p.background,logoBase64:p.logo,welcomeMessage:p.welcome,termsOfService:p.terms_of_service,corporateInfo:p.copyright}),f(p.welcome),E(p.terms_of_service),N(p.copyright)}},[t]),i.useEffect(()=>{W.current&&g&&(W.current.srcdoc=g,setTimeout(()=>{var p;try{const h=(p=W.current)==null?void 0:p.contentDocument,v=h==null?void 0:h.querySelector("form");v&&(v.onsubmit=O=>(O.preventDefault(),!1))}catch{}},50))},[g]);const k=(p,h)=>["image/png","image/jpeg","image/jpg","image/gif"].includes(p.type)?p.size>h?(M.error(`Choose a picture that is no more than ${h/1024}KB.`),!1):!0:(M.error("Only PNG, JPG, JPEG, or GIF file types are supported."),!1),H=async p=>{D(!0);try{let h="";try{h=await Pa(g,p.mode)}catch{M.error("Failed to convert HTML"),D(!1);return}const v={mode:p.mode,background:p.backgroundColor,logo:p.logoBase64||"",welcome:p.welcomeMessage,terms_of_service:p.termsOfService,copyright:p.corporateInfo};let O;if(t){if(O=await Be({id:t.id,site_id:o,type:3,name:p.name,description:p.description,parameter:v,config_variables:h}),(O==null?void 0:O.status)!==200){M.error((O==null?void 0:O.info)||"Failed to update resource"),D(!1);return}M.success("Resource updated successfully")}else{if(O=await He({site_id:o,type:3,name:p.name,description:p.description,parameter:v,config_variables:h}),(O==null?void 0:O.status)!==200){M.error((O==null?void 0:O.info)||"Failed to create resource"),D(!1);return}M.success("Resource created successfully")}r&&r(),n&&n(!0)}catch{M.error("Failed to create resource")}finally{D(!1)}};return a.jsxs(Ye,{open:c,title:t?"Edit Portal Webroot Profile":"Create Portal Webroot Profile",onCancel:()=>n(!1),onFinish:H,initialValues:se,form:Q,children:[a.jsx(V,{gutter:24,className:"webroot-row",children:a.jsx(T,{span:12,children:a.jsx(P.Item,{name:"name",label:s("common.name"),rules:[{required:!0,message:s("form.required")},{type:"string",max:32,message:s("form.max_length",{max:32})}],children:a.jsx(G,{disabled:e})})})}),a.jsx(V,{gutter:24,className:"webroot-row",children:a.jsx(T,{span:12,children:a.jsx(P.Item,{name:"mode",label:"Mode",rules:[{required:!0,message:s("form.required")}],children:a.jsxs(de,{disabled:e||!!l||t&&t.usage_count>0,value:A,onChange:p=>{d(p),Q.setFieldsValue({mode:p})},children:[a.jsx(de.Option,{value:"Click",children:"Click"}),a.jsx(de.Option,{value:"Radius",children:"Radius"}),a.jsx(de.Option,{value:"Credentials",children:"Credentials"})]})})})}),a.jsx(V,{gutter:24,className:"webroot-row",children:a.jsx(T,{span:12,children:a.jsx(P.Item,{name:"description",label:s("common.description"),rules:[{max:128,message:s("form.max_length",{max:128})}],children:a.jsx(G.TextArea,{disabled:e,rows:2})})})}),a.jsxs(V,{gutter:24,style:{borderRadius:"16px",border:"1px solid #E7E7E7",padding:"16px",margin:0,marginBottom:68},children:[a.jsxs(T,{span:11,className:"webroot-form",children:[a.jsx("h4",{style:{marginTop:0,fontSize:"16px"},children:"Configuration"}),a.jsx(V,{gutter:24,children:a.jsxs(T,{span:24,style:{display:"flex"},children:[a.jsx(P.Item,{name:"backgroundColor",label:"Background",rules:[{required:!0,message:"Background is required."}],children:a.jsx(G,{type:"text",disabled:!0,value:w,readOnly:!0})}),a.jsx("div",{style:{marginLeft:16,position:"relative"},children:a.jsx(Ke,{value:w,disabled:e,onChange:p=>{const h=typeof p=="string"?p:p!=null&&p.toHexString?p.toHexString():String(p);B(h),Q.setFieldsValue({backgroundColor:h})},children:a.jsx(K,{className:"upload-button",disabled:e,children:"Color"})})})]})}),a.jsx(V,{gutter:24,children:a.jsxs(T,{span:24,style:{display:"flex"},children:[a.jsx(P.Item,{name:"logoBase64",label:"Upload Logo",children:a.jsx(Ir,{title:`PNG, JPG, JPEG and GIF, Must be less than ${ee.logo}kB`,children:a.jsx(G,{type:"text",disabled:!0,readOnly:!0,value:R,placeholder:`PNG, JPG, JPEG and GIF, Must be less than ${ee.logo}kB`})})}),a.jsx("div",{style:{marginLeft:16},children:a.jsx(We,{accept:"image/*",showUploadList:!1,disabled:e,style:{marginLeft:16},beforeUpload:p=>{if(!k(p,ee.logo*1024))return We.LIST_IGNORE;const h=new FileReader;return h.onload=()=>{S(h.result),Q.setFieldsValue({logoBase64:h.result})},h.readAsDataURL(p),We.LIST_IGNORE},children:a.jsx(K,{className:"upload-button",disabled:e,children:"Upload Logo"})})})]})}),a.jsx(V,{gutter:24,children:a.jsx(T,{span:18,children:a.jsx(P.Item,{name:"welcomeMessage",label:"Welcome Message",rules:[{max:ee.welcomeMessage,message:`This length should be no more than ${ee.welcomeMessage}.`}],children:a.jsx(G,{disabled:e,onChange:p=>y(p.target.value)})})})}),a.jsx(V,{gutter:24,children:a.jsx(T,{span:18,children:a.jsx(P.Item,{name:"termsOfService",label:"Terms of Service",rules:[{max:ee.termsOfService,message:`This length should be no more than ${ee.termsOfService}.`}],children:a.jsx(G.TextArea,{disabled:e,rows:5,onChange:p=>$(p.target.value)})})})}),a.jsx(V,{gutter:24,children:a.jsx(T,{span:18,children:a.jsx(P.Item,{name:"corporateInfo",label:"Copyright",rules:[{max:ee.corporateInfo,message:`This length should be no more than ${ee.corporateInfo}.`}],className:"form-item-uniform",children:a.jsx(G.TextArea,{disabled:e,rows:5,onChange:p=>F(p.target.value)})})})})]}),a.jsxs(T,{span:13,style:{display:"flex",flexDirection:"column"},children:[a.jsx("h4",{style:{marginTop:0,marginLeft:18,fontSize:"16px"},children:"Preview"}),a.jsx("div",{className:"previewBox",children:a.jsx("iframe",{ref:W,className:"previewContent"})})]})]})]})},Va=({editingProfile:e,onClose:t,siteId:n})=>{const{t:r}=Ae(),[o,l]=i.useState({name:"",description:""}),[c,s]=i.useState(!1),[g,u]=i.useState(!1),[C,m]=i.useState({mac:"",key:""}),[A,d]=i.useState(!1),[I,f]=i.useState(null),[j,E]=i.useState({field:"",order:""}),[L,N]=i.useState(()=>{if(!(e!=null&&e.parameter))return[];try{let x=e.parameter;if(typeof x=="string"){if(x.startsWith("(")&&x.endsWith("}")){const F=`{"${x.slice(1,-1).replace(/::/g,'":"').replace(/,/g,'","').replace(/:/g,'":"')}"}`,k=JSON.parse(F);return Object.keys(k).filter(H=>H.startsWith("entry_")).map(H=>({mac:k[H].mac||"",key:k[H].key||"",vlan_id:k[H].vlan_id||void 0}))}x=x.replace(/\\"/g,'"'),x.startsWith('"')&&x.endsWith('"')&&(x=x.slice(1,-1));const y=JSON.parse(x);return Object.keys(y).filter($=>$.startsWith("entry_")).map($=>({mac:y[$].mac||"",key:y[$].key||"",vlan_id:y[$].vlan_id||void 0}))}return[]}catch{return[]}}),w=()=>{m({mac:"",key:"",vlan_id:1813}),u(!0)},B=()=>{u(!1)},R=()=>{var k;if(!C.key){M.error(r("Key is required"));return}if(C.key.length<8||C.key.length>63){M.error(r("Value needs to be of a length between 8 (inclusive) and 63 (inclusive)"));return}if(C.vlan_id===void 0||C.vlan_id===null||C.vlan_id<1){M.error("VLAN-ID must be a positive number");return}if(C.vlan_id>=4095){M.error(r("vlan-id must be less than 4095"));return}let x="";C.mac&&(x=((k=C.mac.toLowerCase().replace(/[^a-f0-9]/g,"").match(/.{1,2}/g))==null?void 0:k.join(":"))||C.mac);const y={...C,mac:x};if(L.find(H=>H.key===y.key&&H.vlan_id!==y.vlan_id)){M.error(r("The key already exists in another VLAN"));return}if(L.find(H=>H.key===y.key&&H.vlan_id===y.vlan_id&&H.mac===y.mac)){M.error(r("The user already exists"));return}N(H=>[...H,y]),u(!1)},S=x=>{if(!x)return Promise.resolve();const y=x.toLowerCase();return/^([0-9a-f]{2}:){5}([0-9a-f]{2})$/.test(y)?Promise.resolve():Promise.reject(r('The legal Mac format can only contain lowercase letters and numbers, with a delimiter of ":". Please use the format: 00:00:5e:00:53:af'))},W=x=>{br(r("Are you sure you want to delete?"),async()=>{try{const y=[...L];y.splice(x,1),N(y),M.success(r("Successfully Deleted"))}catch{M.error(r("Delete failed"))}})},[_,D]=i.useState({current:1,pageSize:10,total:L.length});i.useEffect(()=>{D(x=>({...x,total:L.length}))},[L]);const Q=(x,y,$)=>{if(D(F=>({...F,current:x.current,pageSize:x.pageSize})),!Array.isArray($)&&($!=null&&$.order)){const{field:F,order:k}=$,H=[...L].sort((p,h)=>{const v=p[F],O=h[F];return v===void 0||O===void 0?0:typeof v=="number"&&typeof O=="number"?k==="ascend"?v-O:O-v:k==="ascend"?String(v).localeCompare(String(O)):String(O).localeCompare(String(v))});N(H)}};i.useEffect(()=>{if(e){l({name:e.name||"",description:e.description||""});try{const x=e.parameter||{},y=Object.keys(x).filter($=>$.startsWith("entry_")).map($=>{var F,k,H;return{mac:((F=x[$])==null?void 0:F.mac)||"",key:((k=x[$])==null?void 0:k.key)||"",vlan_id:((H=x[$])==null?void 0:H.vlan_id)||void 0}});N(y)}catch{N([])}}},[e]);const q=[{title:r("MAC"),dataIndex:"mac",key:"mac",sorter:!0,render:x=>a.jsx("div",{children:x})},{title:r("Key"),dataIndex:"key",key:"key",sorter:!0,render:x=>a.jsx("div",{children:x})},{title:r("VLAN-ID"),dataIndex:"vlan_id",key:"vlan_id",sorter:!0,render:x=>a.jsx("div",{children:x})},{title:"Operation",key:"actions",align:"left",render:(x,y,$)=>a.jsx(Ar,{style:{display:"flex",justifyContent:"flex-start",width:"100%",marginLeft:-12},children:a.jsx(K,{type:"link",onClick:()=>W($),children:"Delete"})})}];return a.jsxs(a.Fragment,{children:[a.jsx("h2",{style:{margin:"0 0 24px 0",color:"rgba(0, 0, 0, 0.85)",fontWeight:500,fontSize:20}}),a.jsx(yr,{initialValues:o,enableReinitialize:!0,validateOnChange:!0,validateOnBlur:!0,validate:x=>{const y={};return x.name?x.name.length>32&&(y.name=r("Profile name cannot exceed 32 characters")):y.name=r("Please enter profile name"),x.description&&x.description.length>128&&(y.description=r("form.max_length",{max:128})),y},onSubmit:async x=>{if(x.name.length>32){M.error(r("Profile name cannot exceed 32 characters"));return}s(!0);try{const y=L.reduce((H,p,h)=>(H[`entry_${h}`]={mac:p.mac,key:p.key,vlan_id:p.vlan_id},H),{}),$=L.map(H=>({...H.mac?{mac:H.mac}:{},"vlan-id":H.vlan_id,key:H.key})),F={site_id:n,type:2,name:x.name,parameter:y,description:x.description,config_variables:JSON.stringify($)};let k;if(e!=null&&e.id?k=await Be({id:e.id,...F}):k=await He(F),(k==null?void 0:k.status)!==200){M.error((k==null?void 0:k.info)||r("Operation failed"));return}M.success(r(e?"Profile updated successfully":"Profile created successfully")),t(!0)}catch(y){M.error(r("Operation failed: ")+y.message)}finally{s(!1)}},children:({values:x,errors:y,touched:$,handleSubmit:F,handleBlur:k,handleChange:H})=>a.jsxs(P,{onFinish:F,style:{flex:1,display:"flex",flexDirection:"column"},children:[a.jsxs("div",{style:{marginBottom:24,width:"100%",paddingLeft:0},children:[a.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:16,width:"100%"},children:[a.jsxs("span",{style:{fontWeight:500,minWidth:"140px",marginRight:-30},children:[r("Name")," ",a.jsx("span",{style:{color:"red"},children:"*"})]}),a.jsxs("div",{style:{width:"280px"},children:[a.jsx(G,{name:"name",value:x.name,onChange:H,onBlur:k,placeholder:r("Enter name"),style:{width:"100%"},status:$.name&&y.name?"error":""}),$.name&&y.name&&a.jsx("div",{style:{color:"#ff4d4f",marginTop:4},children:y.name})]})]}),a.jsxs("div",{style:{display:"flex",alignItems:"flex-start",width:"100%",marginBottom:16},children:[a.jsx("span",{style:{fontWeight:500,minWidth:"140px",marginRight:-30,paddingTop:8},children:r("Description")}),a.jsxs("div",{style:{width:"280px",marginBottom:-8},children:[a.jsx(G.TextArea,{name:"description",value:x.description,onChange:H,placeholder:r("Enter description"),rows:2}),y.description&&a.jsx("div",{style:{color:"#ff4d4f",marginTop:4},children:y.description})]})]})]}),a.jsx("div",{children:a.jsxs(K,{type:"primary",onClick:w,style:{marginBottom:24,marginTop:2},children:[a.jsx(ie,{component:Xe}),r("Create")]})}),a.jsx("div",{style:{flex:1,marginBottom:24},children:a.jsx(wt,{columns:q,dataSource:L,rowKey:(p,h)=>h.toString(),pagination:{..._,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"],showTotal:p=>`Total ${p} items`,position:["bottomRight"]},onChange:Q,scroll:{x:"max-content",y:300},bordered:!0})}),a.jsx(Ce,{style:{margin:"20px 0px 16px -24px",width:"calc(100% + 48px)"}}),a.jsxs("div",{style:{display:"flex",justifyContent:"flex-end",gap:16},children:[a.jsx(K,{onClick:()=>t(!1),disabled:c,children:r("Cancel")}),a.jsx(K,{type:"primary",htmlType:"submit",loading:c,children:r("Apply")})]})]})}),a.jsxs(yt,{title:a.jsx("div",{style:{fontFamily:"Lato, sans-serif",fontWeight:700,fontSize:"20px",color:"#212519",lineHeight:"24px",textAlign:"left"},children:r("Create")}),visible:g,onCancel:B,footer:[a.jsx(K,{onClick:B,children:r("Cancel")},"back"),a.jsx(K,{type:"primary",onClick:R,children:r("Apply")},"submit")],destroyOnClose:!0,width:680,bodyStyle:{height:340},style:{height:"450px",borderRadius:"8px"},children:[a.jsx(Ce,{style:{margin:"0px 0px 16px -24px",width:"calc(100% + 48px)"}}),a.jsxs(P,{layout:"horizontal",labelCol:{span:6},wrapperCol:{span:18},initialValues:C,onValuesChange:(x,y)=>m(y),validateTrigger:["onChange","onBlur"],onKeyDown:x=>{x.key==="Enter"&&x.preventDefault()},children:[a.jsx(P.Item,{name:"mac",label:a.jsx("span",{children:r("MAC")}),rules:[{validator:(x,y)=>S(y)}],validateTrigger:["onChange","onBlur"],labelAlign:"left",className:"left-error",children:a.jsx(G,{autoComplete:"off",style:{width:"280px",marginLeft:-70}})}),a.jsx(P.Item,{name:"key",label:a.jsx("span",{children:r("Key")}),rules:[{required:!0,message:r("Required")},{validator:(x,y)=>y&&(y.length<8||y.length>63)?Promise.reject(r("Value needs to be of a length between 8 (inclusive) and 63 (inclusive)")):Promise.resolve(),validateTrigger:["onChange","onBlur"]}],labelAlign:"left",validateTrigger:["onChange","onBlur"],className:"left-error",children:a.jsx(G,{autoComplete:"new-password",style:{width:"280px",marginLeft:-70}})}),a.jsx(P.Item,{name:"vlan_id",label:a.jsx("span",{children:r("VLAN-ID")}),rules:[{required:!0,message:r("VLAN-ID must be a positive number")},{validator:(x,y)=>y<1?Promise.reject(r("vlan-id must be greater than 0")):y>=4095?Promise.reject(r("VLAN-ID must be less than 4095")):Promise.resolve(),validateTrigger:["onChange","onBlur"]}],labelAlign:"left",validateTrigger:["onChange","onBlur"],className:"left-error",children:a.jsx(me,{value:C.vlan_id,style:{width:"140px",marginLeft:-70},parser:x=>{const y=parseInt(x||"0",10);return isNaN(y)?"":y},formatter:x=>x?x.toString():""})})]}),a.jsx(Ce,{style:{margin:"175px 0px 16px -24px",width:"calc(100% + 48px)"}})]})]})};export{Va as F,_a as R,Ga as T,Oa as V,Fa as W,Ma as a,Fe as b,pa as c,he as d,Da as e,Qe as f,Ba as g,Wa as h,Ha as s,Ta as u,$a as v};
