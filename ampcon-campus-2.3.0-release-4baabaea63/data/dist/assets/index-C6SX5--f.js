import{ai as W,r as i,R as N,j as t,C as P,c7 as $,B as M,W as O,aq as _,$ as E,c as V,c8 as g}from"./index-CCDcquaz.js";import{W as q}from"./CustomTable-B4Am8LRY.js";import{a as h}from"./wireless_client_api-DLGjGswK.js";import{u as J}from"./SiteContext-D-2Xr-_P.js";const Z=()=>{const f=W(),v=i.useRef(),j=i.useRef(),[k,y]=i.useState(!1),[K,Q]=i.useState([]),[B,m]=i.useState([]),[C,b]=i.useState(""),[a,A]=i.useState("all"),[u,I]=i.useState({current:1,pageSize:10,total:0}),[p,D]=i.useState({all:0,online:0,offline:0}),F=async()=>{const n=await h({status:0,pageNum:1,pageSize:1}),e=await h({status:1,pageNum:1,pageSize:1}),l=await h({status:2,pageNum:1,pageSize:1});D({all:n.total||0,online:e.total||0,offline:l.total||0})},d=async({pageNum:n=u.current,pageSize:e=u.pageSize,sortBy:l,sortType:r,searchValue:c=C,status:o=a}={})=>{y(!0);try{const x=await h({status:o==="all"?0:o==="online"?1:o==="offline"?2:0,pageNum:n,pageSize:e,searchValue:c,sortBy:l,sortType:r}),S=x.info||[],z=S.map(s=>({...s,mac:s.mac||"",ip:s.ip||"",sn:s.sn||"",ssid:s.ssid||"",vendor:s.vendor||"",venue:s.venue||""}));m(z),I(s=>({...s,current:n,pageSize:e,total:x.total||S.length}))}catch{m([]),I(x=>({...x,total:0}))}finally{y(!1)}};i.useEffect(()=>{F(),d()},[]);const L=n=>{A(n),d({status:n,pageNum:1})},T=n=>{b(n),d({searchValue:n,pageNum:1})},{setSelectedSiteId:w}=J(),H=N.useMemo(()=>[{key:"status",title:"Status",dataIndex:"status",columnsFix:!0,filters:a==="all"?[{text:"Online",value:1},{text:"Offline",value:2}]:void 0,filterIcon:a==="all"?void 0:null,render:e=>{const l=e===1;return t.jsx(E,{color:l?"green":"default",style:{margin:0,borderRadius:"2px",fontFamily:"Lato, sans-serif",fontWeight:400,fontSize:"14px",lineHeight:"17px",textAlign:"left",fontStyle:"normal",textTransform:"none",minWidth:"59px",height:"24px",padding:0,display:"inline-flex",alignItems:"center",justifyContent:"center",...l?{color:"#2BC174",backgroundColor:"rgba(43, 193, 116, 0.1)",border:"1px solid #2BC174"}:{backgroundColor:"#F4F5F7",color:"#B3BBC8",border:"1px solid #DADCE1"}},children:l?"Online":"Offline"})}},{key:"host_name",title:"Hostname",dataIndex:"host_name",sorter:!0,render:(e,l)=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:"17px",lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e||l.mac||"-"})},{key:"mac",title:"MAC Address",dataIndex:"mac",sorter:!0,render:(e,l)=>t.jsx("div",{style:{width:"100%"},children:e?t.jsx("a",{onClick:()=>{const r=e.replace(/:/g,"");w(l.siteId),f(`/wireless/manage/Monitor#${l.siteId}`,{state:{siteId:l.siteId,scrollToClientLifecycle:!0,targetMac:r}})},style:{height:"17px",fontFamily:"Lato, sans-serif",fontWeight:400,fontSize:"14px",color:"#14C9BB",lineHeight:"17px",textAlign:"left",fontStyle:"normal",textDecoration:"underline",textDecorationColor:"#14C9BB",textTransform:"none",display:"inline-block",cursor:"pointer"},children:e}):t.jsx("span",{style:{display:"inline-block",height:"17px",lineHeight:"17px",color:"#B3BBC8"},children:"-"})})},{key:"vendor",title:"Vendor",dataIndex:"vendor",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%"},children:e||"-"})},{key:"sn",title:"Connect AP",dataIndex:"sn",sorter:!0,filterDropdown:null,filterIcon:!1,render:(e,l)=>t.jsx("div",{style:{width:"100%"},children:e?t.jsx(V.Link,{onClick:()=>f(`/wireless/devices/${e}`),className:g["ap-link"],style:{height:"17px",fontFamily:"Lato, sans-serif",fontWeight:400,fontSize:"14px",color:"#14C9BB",lineHeight:"17px",textAlign:"left",fontStyle:"normal",textDecoration:"underline",textDecorationColor:"#14C9BB",textTransform:"none",display:"inline-block",cursor:"pointer"},children:e}):t.jsx("span",{style:{display:"inline-block",height:"17px",lineHeight:"17px",color:"#B3BBC8"},children:"-"})})},{key:"venue",title:"Site",dataIndex:"venue",sorter:!0,filterDropdown:null,filterIcon:!1,render:(e,l)=>!e&&e!==0?t.jsx("div",{style:{textAlign:"left",width:"100%",height:"17px",lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:"-"}):t.jsx("div",{style:{height:"17px",fontFamily:"Lato, sans-serif",fontWeight:400,fontSize:"14px",color:"#14C9BB",lineHeight:"17px",textAlign:"left",fontStyle:"normal",textDecoration:"underline",textDecorationColor:"#14C9BB",textTransform:"none",display:"inline-block",cursor:"pointer"},children:e?t.jsx("a",{onClick:()=>{w(l.siteId),f(`/wireless/manage/Monitor#${l.siteId}`)},className:g["venue-link"],style:{display:"inline-block",color:"inherit !important",textDecoration:"inherit !important"},children:e}):t.jsx("span",{style:{display:"inline-block"},children:"-"})})},{key:"ssid",title:"SSID",dataIndex:"ssid",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>t.jsx("div",{style:{textAlign:"left"},children:e||"-"})},{key:"rssi",title:"RSSI",dataIndex:"rssi",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>!e&&e!==0?t.jsx("div",{style:{textAlign:"left",width:"100%"},children:"-"}):t.jsx("div",{style:{width:"100%"},children:t.jsxs("span",{className:g["rssi-value"],children:[e," dBm"]})})},{key:"band",title:"Band",dataIndex:"band",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>t.jsx("div",{children:e||"-"})},{key:"channel",title:"Channel",dataIndex:"channel",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>t.jsx("div",{children:e||"-"})},{key:"channel_width",title:"Channel Width",dataIndex:"channel_width",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:"17px",lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e||"-"})},{key:"ip",title:"IP",dataIndex:"ip",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:"17px",lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e||"-"})},{key:"vlan",title:"VLAN",dataIndex:"vlan",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:"17px",lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e||"-"})},{title:"Rx",dataIndex:"rx",key:"rx",sorter:!0,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:17,lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e??"-"})},{title:"Tx",dataIndex:"tx",key:"tx",sorter:!0,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:17,lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e??"-"})},{title:"Tx Packets",dataIndex:"tx_packets",key:"tx_packets",sorter:!0,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:17,lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e??"-"})},{title:"Rx Packets",dataIndex:"rx_packets",key:"rx_packets",sorter:!0,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:17,lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e??"-"})}],[f,a]);return t.jsxs(P,{style:{display:"flex",flex:1},children:[t.jsx($,{ref:v,saveCallback:()=>{d()}}),t.jsx("h2",{style:{margin:"8px 0 20px"},children:"Wireless Clients"}),t.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:16},children:[t.jsx("div",{style:{marginBottom:-10},children:["all","online","offline"].map((n,e,l)=>t.jsxs(M,{type:"default",className:`tab-button ${a===n?"active":""}`,onClick:()=>L(n),children:[n==="all"&&`All (${p.all})`,n==="online"&&`Online (${p.online})`,n==="offline"&&`Offline (${p.offline})`]},n))}),t.jsx(O,{placeholder:"Search by STA, IP, AP, SSID or Vendor",allowClear:!0,enterButton:!1,size:"large",prefix:t.jsx(_,{style:{color:"#B8BFBF",width:16,height:16}}),style:{width:270,height:32,borderRadius:2,fontSize:13,fontWeight:400,marginBottom:-14},value:C,onChange:n=>T(n.target.value)})]}),t.jsx(q,{ref:j,loading:k,columns:H,dataSource:B,rowKey:"id",showColumnSelector:"true",pagination:{...u,showTotal:n=>`Total ${n} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],showLessItems:!1},onChange:(n,e,l)=>{let r=a;if(a==="all"&&(e!=null&&e.status)&&e.status.length>0){const c=e.status.map(o=>o===1?"online":o===2?"offline":"all");r=c.length===1?c[0]:c}d({pageNum:n.current,pageSize:n.pageSize,sortBy:l.field,sortType:l.order==="ascend"?"asc":l.order==="descend"?"desc":void 0,status:r})}})]})};export{Z as default};
