const s=(t,o=2)=>{const i=["B","KB","MB","GB","TB","PB","EB","ZB","YB"];if(!t||t===0)return"0 B";const n=1024,a=o<0?0:o,r=Math.floor(Math.log(t)/Math.log(n));return r<0?"1 B":`${parseFloat((t/n**r).toFixed(a).toLocaleString())} ${i[r]}`},B=t=>t==null?"-":t>-150&&t<100?t:(4294967295-t)*-1,c=(t,o=1e8)=>t&&(t<o?t.toLocaleString("en-US"):t.toExponential(2)),f=()=>Math.floor(Math.random()*2147483647);export{s as b,c as f,B as p,f as r};
