import{ac as a}from"./index-CCDcquaz.js";const n="/ampcon/wireless/monitor";function u({status:e,searchValue:r,sortBy:t,sortType:o,pageNum:i,pageSize:s}){return a({url:`${n}/client`,method:"GET",params:{status:e,searchValue:r,sortBy:t,sortType:o,pageNum:i,pageSize:s}}).then(m=>m)}function c({id:e,startTime:r,endTime:t}){return a({url:`${n}/timepoints`,method:"GET",params:{board_id:e,fromDate:Math.floor(r.getTime()/1e3),endDate:t?Math.floor(t.getTime()/1e3):void 0}}).then(o=>o)}export{u as a,c as g};
