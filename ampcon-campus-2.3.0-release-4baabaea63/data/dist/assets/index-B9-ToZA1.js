import{ds as Xe,dt as Je,du as Qe,dv as Ze,dw as er,dx as rr,dy as tr,d3 as Ve,r as f,dz as z,dA as Fe,dB as Ce,aU as Y}from"./index-CCDcquaz.js";function je(e){return Xe(e)?Je(e,tr):Ze(e)?[e]:Qe(er(rr(e)))}var nr=function(r){return ir(r)&&!ar(r)};function ir(e){return!!e&&typeof e=="object"}function ar(e){var r=Object.prototype.toString.call(e);return r==="[object RegExp]"||r==="[object Date]"||lr(e)}var ur=typeof Symbol=="function"&&Symbol.for,or=ur?Symbol.for("react.element"):60103;function lr(e){return e.$$typeof===or}function cr(e){return Array.isArray(e)?[]:{}}function X(e,r){return r.clone!==!1&&r.isMergeableObject(e)?W(cr(e),e,r):e}function fr(e,r,i){return e.concat(r).map(function(n){return X(n,i)})}function sr(e,r,i){var n={};return i.isMergeableObject(e)&&Object.keys(e).forEach(function(a){n[a]=X(e[a],i)}),Object.keys(r).forEach(function(a){!i.isMergeableObject(r[a])||!e[a]?n[a]=X(r[a],i):n[a]=W(e[a],r[a],i)}),n}function W(e,r,i){i=i||{},i.arrayMerge=i.arrayMerge||fr,i.isMergeableObject=i.isMergeableObject||nr;var n=Array.isArray(r),a=Array.isArray(e),o=n===a;return o?n?i.arrayMerge(e,r,i):sr(e,r,i):X(r,i)}W.all=function(r,i){if(!Array.isArray(r))throw new Error("first argument should be an array");return r.reduce(function(n,a){return W(n,a,i)},{})};var ce=W,Oe=Array.isArray,_e=Object.keys,dr=Object.prototype.hasOwnProperty,vr=typeof Element<"u";function fe(e,r){if(e===r)return!0;if(e&&r&&typeof e=="object"&&typeof r=="object"){var i=Oe(e),n=Oe(r),a,o,v;if(i&&n){if(o=e.length,o!=r.length)return!1;for(a=o;a--!==0;)if(!fe(e[a],r[a]))return!1;return!0}if(i!=n)return!1;var p=e instanceof Date,S=r instanceof Date;if(p!=S)return!1;if(p&&S)return e.getTime()==r.getTime();var h=e instanceof RegExp,O=r instanceof RegExp;if(h!=O)return!1;if(h&&O)return e.toString()==r.toString();var I=_e(e);if(o=I.length,o!==_e(r).length)return!1;for(a=o;a--!==0;)if(!dr.call(r,I[a]))return!1;if(vr&&e instanceof Element&&r instanceof Element)return e===r;for(a=o;a--!==0;)if(v=I[a],!(v==="_owner"&&e.$$typeof)&&!fe(e[v],r[v]))return!1;return!0}return e!==e&&r!==r}var yr=function(r,i){try{return fe(r,i)}catch(n){if(n.message&&n.message.match(/stack|recursion/i)||n.number===-2146828260)return console.warn("Warning: react-fast-compare does not handle circular references.",n.name,n.message),!1;throw n}};const N=Ve(yr);function T(){return T=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var i=arguments[r];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},T.apply(this,arguments)}function De(e,r){if(e==null)return{};var i={},n=Object.keys(e),a,o;for(o=0;o<n.length;o++)a=n[o],!(r.indexOf(a)>=0)&&(i[a]=e[a]);return i}var J=f.createContext(void 0);J.displayName="FormikContext";var Er=J.Provider;J.Consumer;function ke(){var e=f.useContext(J);return e}var C=function(r){return typeof r=="function"},G=function(r){return r!==null&&typeof r=="object"},pr=function(r){return String(Math.floor(Number(r)))===r},ue=function(r){return Object.prototype.toString.call(r)==="[object String]"},mr=function(r){return f.Children.count(r)===0},oe=function(r){return G(r)&&C(r.then)};function R(e,r,i,n){n===void 0&&(n=0);for(var a=je(r);e&&n<a.length;)e=e[a[n++]];return n!==a.length&&!e||e===void 0?i:e}function P(e,r,i){for(var n=Fe(e),a=n,o=0,v=je(r);o<v.length-1;o++){var p=v[o],S=R(e,v.slice(0,o+1));if(S&&(G(S)||Array.isArray(S)))a=a[p]=Fe(S);else{var h=v[o+1];a=a[p]=pr(h)&&Number(h)>=0?[]:{}}}return(o===0?e:a)[v[o]]===i?e:(i===void 0?delete a[v[o]]:a[v[o]]=i,o===0&&i===void 0&&delete n[v[o]],n)}function we(e,r,i,n){i===void 0&&(i=new WeakMap),n===void 0&&(n={});for(var a=0,o=Object.keys(e);a<o.length;a++){var v=o[a],p=e[v];G(p)?i.get(p)||(i.set(p,!0),n[v]=Array.isArray(p)?[]:{},we(p,r,i,n[v])):n[v]=r}return n}function hr(e,r){switch(r.type){case"SET_VALUES":return T({},e,{values:r.payload});case"SET_TOUCHED":return T({},e,{touched:r.payload});case"SET_ERRORS":return N(e.errors,r.payload)?e:T({},e,{errors:r.payload});case"SET_STATUS":return T({},e,{status:r.payload});case"SET_ISSUBMITTING":return T({},e,{isSubmitting:r.payload});case"SET_ISVALIDATING":return T({},e,{isValidating:r.payload});case"SET_FIELD_VALUE":return T({},e,{values:P(e.values,r.payload.field,r.payload.value)});case"SET_FIELD_TOUCHED":return T({},e,{touched:P(e.touched,r.payload.field,r.payload.value)});case"SET_FIELD_ERROR":return T({},e,{errors:P(e.errors,r.payload.field,r.payload.value)});case"RESET_FORM":return T({},e,r.payload);case"SET_FORMIK_STATE":return r.payload(e);case"SUBMIT_ATTEMPT":return T({},e,{touched:we(e.values,!0),isSubmitting:!0,submitCount:e.submitCount+1});case"SUBMIT_FAILURE":return T({},e,{isSubmitting:!1});case"SUBMIT_SUCCESS":return T({},e,{isSubmitting:!1});default:return e}}var U={},q={};function Sr(e){var r=e.validateOnChange,i=r===void 0?!0:r,n=e.validateOnBlur,a=n===void 0?!0:n,o=e.validateOnMount,v=o===void 0?!1:o,p=e.isInitialValid,S=e.enableReinitialize,h=S===void 0?!1:S,O=e.onSubmit,I=De(e,["validateOnChange","validateOnBlur","validateOnMount","isInitialValid","enableReinitialize","onSubmit"]),s=T({validateOnChange:i,validateOnBlur:a,validateOnMount:v,onSubmit:O},I),b=f.useRef(s.initialValues),L=f.useRef(s.initialErrors||U),V=f.useRef(s.initialTouched||q),j=f.useRef(s.initialStatus),g=f.useRef(!1),_=f.useRef({});f.useEffect(function(){return g.current=!0,function(){g.current=!1}},[]);var Q=f.useState(0),Z=Q[1],k=f.useRef({values:z(s.initialValues),errors:z(s.initialErrors)||U,touched:z(s.initialTouched)||q,status:z(s.initialStatus),isSubmitting:!1,isValidating:!1,submitCount:0}),d=k.current,E=f.useCallback(function(t){var u=k.current;k.current=hr(u,t),u!==k.current&&Z(function(l){return l+1})},[]),w=f.useCallback(function(t,u){return new Promise(function(l,c){var y=s.validate(t,u);y==null?l(U):oe(y)?y.then(function(m){l(m||U)},function(m){c(m)}):l(y)})},[s.validate]),B=f.useCallback(function(t,u){var l=s.validationSchema,c=C(l)?l(u):l,y=u&&c.validateAt?c.validateAt(u,t):gr(t,c);return new Promise(function(m,A){y.then(function(){m(U)},function(D){D.name==="ValidationError"?m(Tr(D)):A(D)})})},[s.validationSchema]),ve=f.useCallback(function(t,u){return new Promise(function(l){return l(_.current[t].validate(u))})},[]),ye=f.useCallback(function(t){var u=Object.keys(_.current).filter(function(c){return C(_.current[c].validate)}),l=u.length>0?u.map(function(c){return ve(c,R(t,c))}):[Promise.resolve("DO_NOT_DELETE_YOU_WILL_BE_FIRED")];return Promise.all(l).then(function(c){return c.reduce(function(y,m,A){return m==="DO_NOT_DELETE_YOU_WILL_BE_FIRED"||m&&(y=P(y,u[A],m)),y},{})})},[ve]),Ue=f.useCallback(function(t){return Promise.all([ye(t),s.validationSchema?B(t):{},s.validate?w(t):{}]).then(function(u){var l=u[0],c=u[1],y=u[2],m=ce.all([l,c,y],{arrayMerge:br});return m})},[s.validate,s.validationSchema,ye,w,B]),M=F(function(t){return t===void 0&&(t=d.values),E({type:"SET_ISVALIDATING",payload:!0}),Ue(t).then(function(u){return g.current&&(E({type:"SET_ISVALIDATING",payload:!1}),E({type:"SET_ERRORS",payload:u})),u})});f.useEffect(function(){v&&g.current===!0&&N(b.current,s.initialValues)&&M(b.current)},[v,M]);var x=f.useCallback(function(t){var u=t&&t.values?t.values:b.current,l=t&&t.errors?t.errors:L.current?L.current:s.initialErrors||{},c=t&&t.touched?t.touched:V.current?V.current:s.initialTouched||{},y=t&&t.status?t.status:j.current?j.current:s.initialStatus;b.current=u,L.current=l,V.current=c,j.current=y;var m=function(){E({type:"RESET_FORM",payload:{isSubmitting:!!t&&!!t.isSubmitting,errors:l,touched:c,status:y,values:u,isValidating:!!t&&!!t.isValidating,submitCount:t&&t.submitCount&&typeof t.submitCount=="number"?t.submitCount:0}})};if(s.onReset){var A=s.onReset(d.values,Ie);oe(A)?A.then(m):m()}else m()},[s.initialErrors,s.initialStatus,s.initialTouched,s.onReset]);f.useEffect(function(){g.current===!0&&!N(b.current,s.initialValues)&&h&&(b.current=s.initialValues,x(),v&&M(b.current))},[h,s.initialValues,x,v,M]),f.useEffect(function(){h&&g.current===!0&&!N(L.current,s.initialErrors)&&(L.current=s.initialErrors||U,E({type:"SET_ERRORS",payload:s.initialErrors||U}))},[h,s.initialErrors]),f.useEffect(function(){h&&g.current===!0&&!N(V.current,s.initialTouched)&&(V.current=s.initialTouched||q,E({type:"SET_TOUCHED",payload:s.initialTouched||q}))},[h,s.initialTouched]),f.useEffect(function(){h&&g.current===!0&&!N(j.current,s.initialStatus)&&(j.current=s.initialStatus,E({type:"SET_STATUS",payload:s.initialStatus}))},[h,s.initialStatus,s.initialTouched]);var Ee=F(function(t){if(_.current[t]&&C(_.current[t].validate)){var u=R(d.values,t),l=_.current[t].validate(u);return oe(l)?(E({type:"SET_ISVALIDATING",payload:!0}),l.then(function(c){return c}).then(function(c){E({type:"SET_FIELD_ERROR",payload:{field:t,value:c}}),E({type:"SET_ISVALIDATING",payload:!1})})):(E({type:"SET_FIELD_ERROR",payload:{field:t,value:l}}),Promise.resolve(l))}else if(s.validationSchema)return E({type:"SET_ISVALIDATING",payload:!0}),B(d.values,t).then(function(c){return c}).then(function(c){E({type:"SET_FIELD_ERROR",payload:{field:t,value:R(c,t)}}),E({type:"SET_ISVALIDATING",payload:!1})});return Promise.resolve()}),Ne=f.useCallback(function(t,u){var l=u.validate;_.current[t]={validate:l}},[]),Pe=f.useCallback(function(t){delete _.current[t]},[]),pe=F(function(t,u){E({type:"SET_TOUCHED",payload:t});var l=u===void 0?a:u;return l?M(d.values):Promise.resolve()}),me=f.useCallback(function(t){E({type:"SET_ERRORS",payload:t})},[]),he=F(function(t,u){var l=C(t)?t(d.values):t;E({type:"SET_VALUES",payload:l});var c=u===void 0?i:u;return c?M(l):Promise.resolve()}),K=f.useCallback(function(t,u){E({type:"SET_FIELD_ERROR",payload:{field:t,value:u}})},[]),$=F(function(t,u,l){E({type:"SET_FIELD_VALUE",payload:{field:t,value:u}});var c=l===void 0?i:l;return c?M(P(d.values,t,u)):Promise.resolve()}),Se=f.useCallback(function(t,u){var l=u,c=t,y;if(!ue(t)){t.persist&&t.persist();var m=t.target?t.target:t.currentTarget,A=m.type,D=m.name,ie=m.id,ae=m.value,Ye=m.checked;m.outerHTML;var Re=m.options,qe=m.multiple;l=u||D||ie,c=/number|range/.test(A)?(y=parseFloat(ae),isNaN(y)?"":y):/checkbox/.test(A)?Ir(R(d.values,l),Ye,ae):Re&&qe?Ar(Re):ae}l&&$(l,c)},[$,d.values]),ee=F(function(t){if(ue(t))return function(u){return Se(u,t)};Se(t)}),H=F(function(t,u,l){u===void 0&&(u=!0),E({type:"SET_FIELD_TOUCHED",payload:{field:t,value:u}});var c=l===void 0?a:l;return c?M(d.values):Promise.resolve()}),Te=f.useCallback(function(t,u){t.persist&&t.persist();var l=t.target,c=l.name,y=l.id;l.outerHTML;var m=u||c||y;H(m,!0)},[H]),re=F(function(t){if(ue(t))return function(u){return Te(u,t)};Te(t)}),ge=f.useCallback(function(t){C(t)?E({type:"SET_FORMIK_STATE",payload:t}):E({type:"SET_FORMIK_STATE",payload:function(){return t}})},[]),be=f.useCallback(function(t){E({type:"SET_STATUS",payload:t})},[]),Ae=f.useCallback(function(t){E({type:"SET_ISSUBMITTING",payload:t})},[]),te=F(function(){return E({type:"SUBMIT_ATTEMPT"}),M().then(function(t){var u=t instanceof Error,l=!u&&Object.keys(t).length===0;if(l){var c;try{if(c=$e(),c===void 0)return}catch(y){throw y}return Promise.resolve(c).then(function(y){return g.current&&E({type:"SUBMIT_SUCCESS"}),y}).catch(function(y){if(g.current)throw E({type:"SUBMIT_FAILURE"}),y})}else if(g.current&&(E({type:"SUBMIT_FAILURE"}),u))throw t})}),Be=F(function(t){t&&t.preventDefault&&C(t.preventDefault)&&t.preventDefault(),t&&t.stopPropagation&&C(t.stopPropagation)&&t.stopPropagation(),te().catch(function(u){console.warn("Warning: An unhandled error was caught from submitForm()",u)})}),Ie={resetForm:x,validateForm:M,validateField:Ee,setErrors:me,setFieldError:K,setFieldTouched:H,setFieldValue:$,setStatus:be,setSubmitting:Ae,setTouched:pe,setValues:he,setFormikState:ge,submitForm:te},$e=F(function(){return O(d.values,Ie)}),He=F(function(t){t&&t.preventDefault&&C(t.preventDefault)&&t.preventDefault(),t&&t.stopPropagation&&C(t.stopPropagation)&&t.stopPropagation(),x()}),xe=f.useCallback(function(t){return{value:R(d.values,t),error:R(d.errors,t),touched:!!R(d.touched,t),initialValue:R(b.current,t),initialTouched:!!R(V.current,t),initialError:R(L.current,t)}},[d.errors,d.touched,d.values]),We=f.useCallback(function(t){return{setValue:function(l,c){return $(t,l,c)},setTouched:function(l,c){return H(t,l,c)},setError:function(l){return K(t,l)}}},[$,H,K]),Ge=f.useCallback(function(t){var u=G(t),l=u?t.name:t,c=R(d.values,l),y={name:l,value:c,onChange:ee,onBlur:re};if(u){var m=t.type,A=t.value,D=t.as,ie=t.multiple;m==="checkbox"?A===void 0?y.checked=!!c:(y.checked=!!(Array.isArray(c)&&~c.indexOf(A)),y.value=A):m==="radio"?(y.checked=c===A,y.value=A):D==="select"&&ie&&(y.value=y.value||[],y.multiple=!0)}return y},[re,ee,d.values]),ne=f.useMemo(function(){return!N(b.current,d.values)},[b.current,d.values]),Ke=f.useMemo(function(){return typeof p<"u"?ne?d.errors&&Object.keys(d.errors).length===0:p!==!1&&C(p)?p(s):p:d.errors&&Object.keys(d.errors).length===0},[p,ne,d.errors,s]),ze=T({},d,{initialValues:b.current,initialErrors:L.current,initialTouched:V.current,initialStatus:j.current,handleBlur:re,handleChange:ee,handleReset:He,handleSubmit:Be,resetForm:x,setErrors:me,setFormikState:ge,setFieldTouched:H,setFieldValue:$,setFieldError:K,setStatus:be,setSubmitting:Ae,setTouched:pe,setValues:he,submitForm:te,validateForm:M,validateField:Ee,isValid:Ke,dirty:ne,unregisterField:Pe,registerField:Ne,getFieldProps:Ge,getFieldMeta:xe,getFieldHelpers:We,validateOnBlur:a,validateOnChange:i,validateOnMount:v});return ze}function Kr(e){var r=Sr(e),i=e.component,n=e.children,a=e.render,o=e.innerRef;return f.useImperativeHandle(o,function(){return r}),f.createElement(Er,{value:r},i?f.createElement(i,r):a?a(r):n?C(n)?n(r):mr(n)?null:f.Children.only(n):null)}function Tr(e){var r={};if(e.inner){if(e.inner.length===0)return P(r,e.path,e.message);for(var a=e.inner,i=Array.isArray(a),n=0,a=i?a:a[Symbol.iterator]();;){var o;if(i){if(n>=a.length)break;o=a[n++]}else{if(n=a.next(),n.done)break;o=n.value}var v=o;R(r,v.path)||(r=P(r,v.path,v.message))}}return r}function gr(e,r,i,n){i===void 0&&(i=!1);var a=se(e);return r[i?"validateSync":"validate"](a,{abortEarly:!1,context:a})}function se(e){var r=Array.isArray(e)?[]:{};for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var n=String(i);Array.isArray(e[n])===!0?r[n]=e[n].map(function(a){return Array.isArray(a)===!0||Ce(a)?se(a):a!==""?a:void 0}):Ce(e[n])?r[n]=se(e[n]):r[n]=e[n]!==""?e[n]:void 0}return r}function br(e,r,i){var n=e.slice();return r.forEach(function(o,v){if(typeof n[v]>"u"){var p=i.clone!==!1,S=p&&i.isMergeableObject(o);n[v]=S?ce(Array.isArray(o)?[]:{},o,i):o}else i.isMergeableObject(o)?n[v]=ce(e[v],o,i):e.indexOf(o)===-1&&n.push(o)}),n}function Ar(e){return Array.from(e).filter(function(r){return r.selected}).map(function(r){return r.value})}function Ir(e,r,i){if(typeof e=="boolean")return!!r;var n=[],a=!1,o=-1;if(Array.isArray(e))n=e,o=e.indexOf(i),a=o>=0;else if(!i||i=="true"||i=="false")return!!r;return r&&i&&!a?n.concat(i):a?n.slice(0,o).concat(n.slice(o+1)):n}var Rr=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?f.useLayoutEffect:f.useEffect;function F(e){var r=f.useRef(e);return Rr(function(){r.current=e}),f.useCallback(function(){for(var i=arguments.length,n=new Array(i),a=0;a<i;a++)n[a]=arguments[a];return r.current.apply(void 0,n)},[])}function zr(e){var r=ke(),i=r.getFieldProps,n=r.getFieldMeta,a=r.getFieldHelpers,o=r.registerField,v=r.unregisterField,p=G(e),S=p?e:{name:e},h=S.name,O=S.validate;f.useEffect(function(){return h&&o(h,{validate:O}),function(){h&&v(h)}},[o,v,h,O]);var I=f.useMemo(function(){return a(h)},[a,h]);return[i(S),n(h),I]}var Fr=f.forwardRef(function(e,r){var i=e.action,n=De(e,["action"]),a=i??"#",o=ke(),v=o.handleReset,p=o.handleSubmit;return f.createElement("form",T({onSubmit:p,ref:r,onReset:v,action:a},n))});Fr.displayName="Form";var Cr="Expected a function",Me=NaN,Or="[object Symbol]",_r=/^\s+|\s+$/g,Mr=/^[-+]0x[0-9a-f]+$/i,Lr=/^0b[01]+$/i,Vr=/^0o[0-7]+$/i,jr=parseInt,Dr=typeof Y=="object"&&Y&&Y.Object===Object&&Y,kr=typeof self=="object"&&self&&self.Object===Object&&self,wr=Dr||kr||Function("return this")(),Ur=Object.prototype,Nr=Ur.toString,Pr=Math.max,Br=Math.min,le=function(){return wr.Date.now()};function $r(e,r,i){var n,a,o,v,p,S,h=0,O=!1,I=!1,s=!0;if(typeof e!="function")throw new TypeError(Cr);r=Le(r)||0,de(i)&&(O=!!i.leading,I="maxWait"in i,o=I?Pr(Le(i.maxWait)||0,r):o,s="trailing"in i?!!i.trailing:s);function b(d){var E=n,w=a;return n=a=void 0,h=d,v=e.apply(w,E),v}function L(d){return h=d,p=setTimeout(g,r),O?b(d):v}function V(d){var E=d-S,w=d-h,B=r-E;return I?Br(B,o-w):B}function j(d){var E=d-S,w=d-h;return S===void 0||E>=r||E<0||I&&w>=o}function g(){var d=le();if(j(d))return _(d);p=setTimeout(g,V(d))}function _(d){return p=void 0,s&&n?b(d):(n=a=void 0,v)}function Q(){p!==void 0&&clearTimeout(p),h=0,n=S=a=p=void 0}function Z(){return p===void 0?v:_(le())}function k(){var d=le(),E=j(d);if(n=arguments,a=this,S=d,E){if(p===void 0)return L(S);if(I)return p=setTimeout(g,r),b(S)}return p===void 0&&(p=setTimeout(g,r)),v}return k.cancel=Q,k.flush=Z,k}function de(e){var r=typeof e;return!!e&&(r=="object"||r=="function")}function Hr(e){return!!e&&typeof e=="object"}function xr(e){return typeof e=="symbol"||Hr(e)&&Nr.call(e)==Or}function Le(e){if(typeof e=="number")return e;if(xr(e))return Me;if(de(e)){var r=typeof e.valueOf=="function"?e.valueOf():e;e=de(r)?r+"":r}if(typeof e!="string")return e===0?e:+e;e=e.replace(_r,"");var i=Lr.test(e);return i||Vr.test(e)?jr(e.slice(2),i?2:8):Mr.test(e)?Me:+e}var Wr=$r;const Yr=Ve(Wr);export{Kr as F,Fr as a,zr as b,ke as c,Yr as d,Sr as u};
