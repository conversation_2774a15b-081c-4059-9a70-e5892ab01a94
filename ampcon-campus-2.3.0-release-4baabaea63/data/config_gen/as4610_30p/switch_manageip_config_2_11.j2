
set vlans vlan-id {{ data_vlan.id }} l3-interface vlan{{ data_vlan.id }}
set vlans vlan-id {{ data_vlan.id }} description "{{ data_vlan.desc }}"
set vlan-interface interface vlan{{ data_vlan.id }} vif vlan{{ data_vlan.id }} address {{ data_vlan.ip_add }} prefix-length {{ data_vlan.prefix_length }}
set vlan-interface interface vlan{{ data_vlan.id }} vif vlan{{ data_vlan.id }} description "Data Vlan"
{# !IPV6 addressing, code as below #}
set vlan-interface interface vlan{{ data_vlan.id }} vif vlan{{ data_vlan.id }} address {{ data_vlan.ipv6_add }} prefix-length {{ data_vlan.prefix_length_v6 }}

set ip routing enable true
set system inband enable true