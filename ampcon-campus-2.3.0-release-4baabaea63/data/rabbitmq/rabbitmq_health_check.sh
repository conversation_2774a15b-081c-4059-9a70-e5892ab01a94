#!/usr/bin/env bash
set -e

HEALTHCHECK_FILE="/tmp/healthcheck"

if [ -f "$HEALTHCHECK_FILE" ]; then
  FIRST_HEALTHY_TIME=$(cat "$HEALTHCHECK_FILE")
  CURRENT_TIME=$(date +%s)

  if [ $(($CURRENT_TIME - $FIRST_HEALTHY_TIME)) -lt 180 ]; then
    exit 0
  fi
fi

if rabbitmq-diagnostics check_port_connectivity; then
  if [ -f "$HEALTHCHECK_FILE" ]; then
    rm -f "$HEALTHCHECK_FILE"
  fi
  echo $(date +%s) > "$HEALTHCHECK_FILE"
  exit 0
else
  rm -f "$HEALTHCHECK_FILE"
  exit 1
fi