---
- name: Drop packets from a specific Source IP
  hosts: all
  tasks:
  - name: Transfer the script that enables the Crossflow on Port 2 
    copy: src=enable_crossflow_port2.conf dest=/home/<USER>/enable_crossflow_port2.conf force=yes mode=0777
  - name: Activate the configuration to enable the Crossflow
    picos_config: mode='config_load' cmd='/home/<USER>/enable_crossflow_port2.conf'
    register: exec_result
  - name: Show execution result
    debug: var=exec_result.stdout_lines
    
  - name: Create a new bridge br0
    picos_config: mode='shell' cmd='/ovs/bin/ovs-vsctl --may-exist add-br br0'
    register: exec_result
  - name: Show execution result
    debug: var=exec_result.stdout_lines

  - name: Add a physical port 2 to the bridge
    picos_config: mode='shell' cmd='/ovs/bin/ovs-vsctl add-port br0 ge-1/1/2 -- set interface ge-1/1/2 type=crossflow'
    register: exec_result
  - name: Show execution result
    debug: var=exec_result.stdout_lines

  - name: Add a flow that will drop packets from specific source IP
    picos_config: mode='shell' cmd='/ovs/bin/ovs-ofctl add-flow br0 in_port=2,dl_type=0x0800,nw_src={{SrcIP}},actions=drop'
    register: exec_result
  - name: Show execution result
    debug: var=exec_result.stdout_lines
