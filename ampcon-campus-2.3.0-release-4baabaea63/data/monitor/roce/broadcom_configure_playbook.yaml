---
- name: Configure Broadcom NIC based on type variable
  hosts: all
  become: yes
  vars:

  tasks:
    - name: Get bus-info for each port
      shell: "ethtool -i {{ item }} | awk -F': ' '/bus-info/ {print $2}'"
      loop: "{{ target_ports }}"
      register: bus_info_results
      when: script_params.type == "Niccli"
      loop_control:
        label: "{{ item }}"

    - name: Build bus_info_map for port-to-bus mapping
      set_fact:
        bus_info_map: "{{ bus_info_map | default({}) | combine({ (target_ports[loop_index]): (item.stdout | default('')).upper() }) }}"
      loop: "{{ bus_info_results.results }}"
      when: script_params.type == "Niccli"
      loop_control:
        index_var: loop_index

    - name: Run niccli --list to get device index
      shell: niccli --list
      register: niccli_list_output
      when: script_params.type == "Niccli"

    - name: Match port index by bus-info
      set_fact:
        port_index_map: >-
          {{
            port_index_map | default({}) | combine({
              item.key: (niccli_list_output.stdout_lines | select('search', item.value) | list)[0].split(')')[0] | trim
            })
          }}
      loop: "{{ bus_info_map | dict2items }}"
      when: script_params.type == "Niccli"

    - name: Configure TC parameters (ETS/UP2TC/TCBW)
      shell: >
        niccli -i {{ port_index_map[item] }}
        ets -tsa 0:{{ script_params.tc_algorithm[0] }},1:{{ script_params.tc_algorithm[1] }},2:{{ script_params.tc_algorithm[2] }}
        -up2tc 0:{{ script_params.priority2TC[0] }},1:{{ script_params.priority2TC[1] }},2:{{ script_params.priority2TC[2] }},3:{{ script_params.priority2TC[3] }},4:{{ script_params.priority2TC[4] }},5:{{ script_params.priority2TC[5] }},6:{{ script_params.priority2TC[6] }},7:{{ script_params.priority2TC[7] }}
        -tcbw {{ script_params.tc_bw | join(',') }}
      loop: "{{ target_ports }}"
      register: tc_config_result
      when: script_params.type == "Niccli"
      failed_when: tc_config_result.rc != 0
      loop_control:
        label: "{{ item }}"

    - name: Configure PFC priorities (enable TCs) 
      shell: >
        niccli -i {{ port_index_map[item] }} pfc -enable {{ script_params.pfc_enabled_tc | join(',') }}
      loop: "{{ target_ports }}"
      register: pfc_config_result
      when:
        - script_params.type == "Niccli"
        - script_params.pfc_enabled_tc | length > 0
      failed_when: pfc_config_result.rc != 0
      loop_control:
        label: "{{ item }}"

    - name: Get RDMA link output
      shell: rdma link
      register: rdma_link_output
      when: script_params.type == "Bnxtsetupcc.sh"

    - name: Parse netdev to link name mapping
      set_fact:
        port_mappings: >-
          {{
            port_mappings | default({}) | combine({
              item.split("netdev ")[1].split()[0]: item.split()[1]
            })
          }}
      loop: "{{ rdma_link_output.stdout_lines }}"
      when: script_params.type == "Bnxtsetupcc.sh" and 'netdev' in item and item.startswith('link')

    - name: Run bnxt_setupcc.sh for each port
      vars:
        link_name: "{{ port_mappings[item] if port_mappings[item] is defined and port_mappings[item] != '' else item }}"
      shell: >
        bnxt_setupcc.sh
        -i {{ item }}
        -d {{ link_name }}
        -u {{ script_params.tool_type }}
        -r {{ script_params.roce_priority }}
        -s {{ script_params.roce_dscp }}
        -c {{ script_params.cnp_priority }}
        -p {{ script_params.cnp_dscp }}
        -m {{ script_params.select_mode }}
      loop: "{{ target_ports }}"
      register: bnxt_result
      when: script_params.type == "Bnxtsetupcc.sh"
      failed_when: bnxt_result.rc != 0
      loop_control:
        label: "{{ item }}"

