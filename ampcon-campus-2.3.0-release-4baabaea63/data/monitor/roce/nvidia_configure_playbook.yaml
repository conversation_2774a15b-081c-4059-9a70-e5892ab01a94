---
- name: Configure NVIDIA RoCEv2 NICs
  hosts: all
  become: yes
  vars:

  tasks:
    - name: Skip configuration if RoCEv2 is disabled
      debug:
        msg: "RoCEv2 is disabled, skipping all configuration steps."
      when: script_params.roce_v2 == "disable"

    - name: RoCEv2 Configuration Block - Per Port
      when: script_params.roce_v2 != "disable"
      loop: "{{ target_ports }}"
      loop_control:
        label: "Configuring port {{ item }}"
      include_tasks: nvidia_configure_one_port.yaml
      vars:
        port: "{{ item }}"
        script_params: "{{ script_params }}"