groups:
  - name: lag_records
    rules:
      - alert: lag_in_errors_count
        expr: changes(openconfig_interfaces:interfaces_interface_state_counters_in_errors[1m]) >= 2
        for: 5s
        labels:
          severity: error
        annotations:
          summary: interface in-errors count
          description: "Found interface: {{ $labels.interface_name }} on switch {{ $labels.target }} in-errors increased"
      - alert: lag_out_errors_count
        expr: changes(openconfig_interfaces:interfaces_interface_state_counters_out_errors[1m]) >= 2
        for: 5s
        labels:
          severity: error
        annotations:
          summary: interface out-errors count
          description: "Found interface: {{ $labels.interface_name }} on switch {{ $labels.target }} out-errors: increased"
      - alert: lag_in_discards_count
        expr: changes(openconfig_interfaces:interfaces_interface_state_counters_in_discards[1m]) >= 2
        for: 5s
        labels:
          severity: error
        annotations:
          summary: interface in-discards count
          description: "Found interface: {{ $labels.interface_name }} on switch {{ $labels.target }} in-discards increased"
      - alert: lag_out_discards_count
        expr: changes(openconfig_interfaces:interfaces_interface_state_counters_out_discards[1m]) >= 2
        for: 5s
        labels:
          severity: error
        annotations:
          summary: interface out-discards count
          description: "Found interface: {{ $labels.interface_name }} on switch {{ $labels.target }} out-discards increased"

  - name: usage_alerts
    rules:
      - alert: cpu_usage
        expr: avg by (target) (openconfig_system:system_cpus_cpu_state_total_avg) >= 85
        for: 0s
        labels:
          severity: warn
        annotations:
          summary: cpu usage
          description: "Found switch {{ $labels.target }} cpu usage over 85%"
      - alert: memory_usage
        expr: 100 * avg by (target) (openconfig_system:system_memory_state_used / openconfig_system:system_memory_state_physical) >= 85
        for: 0s
        labels:
          severity: warn
        annotations:
          summary: memory usage
          description: "Found switch {{ $labels.target }} memory usage over 85%"
      - alert: in_bindwidth_usage
        expr: (openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_in_bits_rate / on(interface_name, target) (openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_state_port_speed > 0)) * 100 >= 85
        for: 0s
        labels:
          severity: warn
        annotations:
          summary: in bindwidth usage
          description: "Found switch {{ $labels.target }} in bindwidth usage over 85%"
      - alert: out_bindwidth_usage
        expr: (openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_out_bits_rate / on(interface_name, target) (openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_state_port_speed > 0)) * 100 >= 85
        for: 0s
        labels:
          severity: warn
        annotations:
          summary: out bindwidth usage
          description: "Found switch {{ $labels.target }} out bindwidth usage over 85%"
      - alert: fan_pwm
        expr: openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:fan_pwm < 0.1 or openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:fan_pwm > 0.85
        for: 5s
        labels:
          severity: warn
        annotations:
          summary: fan_pwm_usage
          description: "Switch {{ $labels.target }} fan_index: {{ $labels.fan_index }} pwm is either below 0.1 or above 0.85."
      - alert: rear_fan_pwm
        expr: openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:rear_fan_pwm < 0.1 or openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:rear_fan_pwm > 0.85
        for: 5s
        labels:
          severity: warn
        annotations:
          summary: fan_pwm_usage
          description: "Switch {{ $labels.target }} rear_fan_index: {{ $labels.rear_fan_index }} pwm is either below 0.1 or above 0.85."

  - name: ai_extensions
    rules:
      - alert: ecn_marked_packets
        expr: openconfig_qos:qos_interfaces_interface_state_fsconfig_qos_ai_extensions:ecn_marked_packets > 1500
        for: 5s
        labels:
          severity: error
        annotations:
          summary: interface ecn_marked_packets overflow
          description: "Found interface: {{ $labels.interface_name }} on switch {{ $labels.target }} ecn_marked_packets overflow"
      - alert: send_pfc_pause_frames
        expr: changes(openconfig_qos:qos_interfaces_interface_output_queues_queue_state_fsconfig_qos_ai_extensions:send_pfc_pause_frames[1m]) > 0
        for: 5s
        labels:
          severity: error
        annotations:
          summary: interface send_pfc_pause_frames increased
          description: "Found interface: {{ $labels.interface_name }} on switch {{ $labels.target }} queue {{ $labels.queue_name }} send_pfc_pause_frames count increased"
      - alert: receive_pfc_pause_frames
        expr: changes(openconfig_qos:qos_interfaces_interface_output_queues_queue_state_fsconfig_qos_ai_extensions:receive_pfc_pause_frames[1m]) > 0
        for: 5s
        labels:
          severity: error
        annotations:
          summary: interface receive_pfc_pause_frames count increased
          description: "Found interface: {{ $labels.interface_name }} on switch {{ $labels.target }} queue {{ $labels.queue_name }} receive_pfc_pause_frames count increased"
      - alert: pfc_deadlock_monitor_count
        expr: changes(openconfig_qos:qos_interfaces_interface_output_queues_queue_state_fsconfig_qos_ai_extensions:pfc_deadlock_monitor_count[1m]) > 0
        for: 5s
        labels:
          severity: error
        annotations:
          summary: interface pfc_deadlock_monitor_count count increased
          description: "Found interface: {{ $labels.interface_name }} on switch {{ $labels.target }} queue {{ $labels.queue_name }} pfc_deadlock_monitor_count count increased"
      - alert: pfc_deadlock_recovery_count
        expr: changes(openconfig_qos:qos_interfaces_interface_output_queues_queue_state_fsconfig_qos_ai_extensions:pfc_deadlock_recovery_count[1m]) > 0
        for: 5s
        labels:
          severity: error
        annotations:
          summary: interface pfc_deadlock_recovery_count increased
          description: "Found interface: {{ $labels.interface_name }} on switch {{ $labels.target }} queue {{ $labels.queue_name }} pfc_deadlock_recovery_count count increased"
