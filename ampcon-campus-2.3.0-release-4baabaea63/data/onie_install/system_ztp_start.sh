#!/bin/sh

server_host="ac.ampcon.local"
tag_file="/home/<USER>/auto_flag"

if [ ! -f "$tag_file" ] || { [ "$(cat "$tag_file")" != "deployed" ] && [ "$(cat "$tag_file")" != "upgrading" ]; }; then
  curl -k -o /opt/auto-deploy/auto-deploy.py https://${server_host}/rma/file/agent/auto-deploy.py
  curl -k -o /opt/auto-deploy/auto-deploy.conf https://${server_host}/rma/file/agent/auto-deploy.conf
  chmod 777 /opt/auto-deploy/auto-deploy.py /opt/auto-deploy/auto-deploy.conf
  chown -R admin:xorp /opt/auto-deploy/
fi
