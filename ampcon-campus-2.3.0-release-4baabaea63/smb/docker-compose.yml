volumes:
  kafka_data:
    driver: local
  postgresql_data:
    driver: local

networks:
  openwifi:

services:
  owgw:
    image: "tip-tip-wlan-cloud-ucentral.jfrog.io/owgw:${OWGW_TAG}"
    networks:
      openwifi:
        aliases:
          - ${INTERNAL_OWGW_HOSTNAME}
    env_file:
      - owgw.env
    depends_on:
      init-kafka:
        condition: service_completed_successfully
      postgresql:
        condition: service_healthy
    command: ["./wait-for-postgres.sh", "postgresql", "/openwifi/owgw"]
    restart: unless-stopped
    volumes:
      - "./owgw_data:${OWGW_ROOT}"
      - "./certs:/${OWGW_ROOT}/certs"
    ports:
      - "15002:15002"
      - "16002:16002"
      - "16102:16102"
      - "16003:16003"
      - "5912:5912"
      - "5913:5913"
      - "1812:1812/udp"
      - "1813:1813/udp"
      - "3799:3799/udp"
    sysctls:
      - net.ipv4.tcp_keepalive_intvl=5
      - net.ipv4.tcp_keepalive_probes=2
      - net.ipv4.tcp_keepalive_time=45

  owgw-ui:
    image: "tip-tip-wlan-cloud-ucentral.jfrog.io/owgw-ui:${OWGWUI_TAG}"
    networks:
      openwifi:
    env_file:
      - owgw-ui.env
    depends_on:
      - owsec
      - owgw
      - owfms
      - owprov
      - owanalytics
      - owsub
    restart: unless-stopped
    volumes:
      - "./owgw-ui/default.conf:/etc/nginx/conf.d/default.conf"
      - "./certs/restapi-cert.pem:/etc/nginx/restapi-cert.pem"
      - "./certs/restapi-key.pem:/etc/nginx/restapi-key.pem"
    ports:
      - "80:80"
      - "443:443"

  owsec:
    image: "tip-tip-wlan-cloud-ucentral.jfrog.io/owsec:${OWSEC_TAG}"
    networks:
      openwifi:
        aliases:
          - ${INTERNAL_OWSEC_HOSTNAME}
    env_file:
      - owsec.env
    depends_on:
      init-kafka:
        condition: service_completed_successfully
      postgresql:
        condition: service_healthy
    command: ["./wait-for-postgres.sh", "postgresql", "/openwifi/owsec"]
    restart: unless-stopped
    volumes:
      - "./owsec_data:${OWSEC_ROOT}"
      - "./certs:/${OWSEC_ROOT}/certs"
    ports:
      - "16001:16001"
      - "16101:16101"

  owfms:
    image: "tip-tip-wlan-cloud-ucentral.jfrog.io/owfms:${OWFMS_TAG}"
    networks:
      openwifi:
        aliases:
          - ${INTERNAL_OWFMS_HOSTNAME}
    env_file:
      - owfms.env
    depends_on:
      init-kafka:
        condition: service_completed_successfully
      postgresql:
        condition: service_healthy
    command: ["./wait-for-postgres.sh", "postgresql", "/openwifi/owfms"]
    restart: unless-stopped
    volumes:
      - "./owfms_data:${OWFMS_ROOT}"
      - "./certs:/${OWFMS_ROOT}/certs"
    ports:
      - "16004:16004"
      - "16104:16104"

  owprov:
    image: "tip-tip-wlan-cloud-ucentral.jfrog.io/owprov:${OWPROV_TAG}"
    networks:
      openwifi:
        aliases:
          - ${INTERNAL_OWPROV_HOSTNAME}
    env_file:
      - owprov.env
    depends_on:
      init-kafka:
        condition: service_completed_successfully
      postgresql:
        condition: service_healthy
    command: ["./wait-for-postgres.sh", "postgresql", "/openwifi/owprov"]
    restart: unless-stopped
    volumes:
      - "./owprov_data:${OWPROV_ROOT}"
      - "./certs:/${OWPROV_ROOT}/certs"
    ports:
      - "16005:16005"
      - "16105:16105"

  owprov-ui:
    image: "tip-tip-wlan-cloud-ucentral.jfrog.io/owprov-ui:${OWPROVUI_TAG}"
    networks:
      openwifi:
    env_file:
      - owprov-ui.env
    depends_on:
      - owsec
      - owgw
      - owfms
      - owprov
      - owanalytics
      - owsub
    restart: unless-stopped
    volumes:
      - "./owprov-ui/default.conf:/etc/nginx/conf.d/default.conf"
      - "./certs/restapi-cert.pem:/etc/nginx/restapi-cert.pem"
      - "./certs/restapi-key.pem:/etc/nginx/restapi-key.pem"
    ports:
      - "8080:8080"
      - "8443:8443"

  owanalytics:
    image: "tip-tip-wlan-cloud-ucentral.jfrog.io/owanalytics:${OWANALYTICS_TAG}"
    networks:
      openwifi:
        aliases:
          - ${INTERNAL_OWANALYTICS_HOSTNAME}
    env_file:
      - owanalytics.env
    depends_on:
      init-kafka:
        condition: service_completed_successfully
      postgresql:
        condition: service_healthy
    command: ["./wait-for-postgres.sh", "postgresql", "/openwifi/owanalytics"]
    restart: unless-stopped
    volumes:
      - "./owanalytics_data:${OWANALYTICS_ROOT}"
      - "./certs:/${OWANALYTICS_ROOT}/certs"
    ports:
      - "16009:16009"
      - "16109:16109"

  owsub:
    image: "tip-tip-wlan-cloud-ucentral.jfrog.io/owsub:${OWSUB_TAG}"
    networks:
      openwifi:
        aliases:
          - ${INTERNAL_OWSUB_HOSTNAME}
    env_file:
      - owsub.env
    depends_on:
      init-kafka:
        condition: service_completed_successfully
      postgresql:
        condition: service_healthy
    command: ["./wait-for-postgres.sh", "postgresql", "/openwifi/owsub"]
    restart: unless-stopped
    volumes:
      - "./owsub_data:${OWSUB_ROOT}"
      - "./certs:/${OWSUB_ROOT}/certs"
    ports:
      - "16006:16006"
      - "16106:16106"

  kafka:
    image: "docker.io/bitnami/kafka:${KAFKA_TAG}"
    networks:
      openwifi:
    env_file:
      - kafka.env
    restart: unless-stopped
    volumes:
      - kafka_data:/bitnami/kafka

  init-kafka:
    image: "docker.io/bitnami/kafka:${KAFKA_TAG}"
    networks:
      openwifi:
    depends_on:
      - kafka
    env_file:
      - kafka.env
    entrypoint:
      - /bin/sh
      - -c
      - |
        echo "Sleeping to allow kafka to start up..."
        sleep 10
        echo "Creating all required Kafka topics..."
        for topic in $$TOPICS; do
          /opt/bitnami/kafka/bin/kafka-topics.sh \
          --create --if-not-exists --topic $$topic --replication-factor 1 \
          --partitions 1 --bootstrap-server kafka:9092
        done && echo "Successfully created Kafka topics, exiting." && exit 0

  postgresql:
    image: "postgres:${POSTGRESQL_TAG}"
    networks:
      openwifi:
    command:
      - "postgres"
      - "-c"
      - "max_connections=400"
      - "-c"
      - "shared_buffers=20MB"
    env_file:
      - postgresql.env
    restart: unless-stopped
    volumes:
      - postgresql_data:/var/lib/postgresql/data
      - ./postgresql/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
    healthcheck:
      # owsub is the last DB created in init-db.sh
      test: ["CMD-SHELL", "pg_isready -U postgres -d owsub"]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s
