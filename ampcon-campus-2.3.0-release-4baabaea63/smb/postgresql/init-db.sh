#!/bin/bash
set -e

# CREATE USER $OWFMS_DB_USER WITH ENCRYPTED PASSWORD '$OWFMS_DB_PASSWORD';
# CREATE DATABASE $OWFMS_DB OWNER $OWFMS_DB_USER;
# CREATE USER $OWSUB_DB_USER WITH ENCRYPTED PASSWORD '$OWSUB_DB_PASSWORD';
# CREATE DATABASE $OWSUB_DB OWNER $OWSUB_DB_USER;


# psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" <<-EOSQL
#     CREATE USER $OWGW_DB_USER WITH ENCRYPTED PASSWORD '$OWGW_DB_PASSWORD';
#     CREATE DATABASE $OWGW_DB OWNER $OWGW_DB_USER;
#     CREATE USER $OWSEC_DB_USER WITH ENCRYPTED PASSWORD '$OWSEC_DB_PASSWORD';
#     CREATE DATABASE $OWSEC_DB OWNER $OWSEC_DB_USER;
#     CREATE USER $OWPROV_DB_USER WITH ENCRYPTED PASSWORD '$OWPROV_DB_PASSWORD';
#     CREATE DATABASE $OWPROV_DB OWNER $OWPROV_DB_USER;
#     CREATE USER $OWANALYTICS_DB_USER WITH ENCRYPTED PASSWORD '$OWANALYTICS_DB_PASSWORD';
#     CREATE DATABASE $OWANALYTICS_DB OWNER $OWANALYTICS_DB_USER;
# EOSQL


psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" <<-EOSQL
    CREATE USER $SMB_DB_USER WITH ENCRYPTED PASSWORD '$SMB_DB_PASSWORD';
    CREATE DATABASE $SMB_DB OWNER $SMB_DB_USER;
EOSQL
