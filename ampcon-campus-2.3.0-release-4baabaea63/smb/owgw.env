RUN_CHOWN=true
TEMPLATE_CONFIG=true
SELFSIGNED_CERTS=true

OWGW_ROOT=/owgw-data
OWGW_CONFIG=/owgw-data

#WEBSOCKET_HOST_ROOTCA=$OWGW_ROOT/certs/root.pem
#WEBSOCKET_HOST_ISSUER=$OWGW_ROOT/certs/issuer.pem
#WEBSOCKET_HOST_CERT=$OWGW_ROOT/certs/websocket-cert.pem
#WEBSOCKET_HOST_KEY=$OWGW_ROOT/certs/websocket-key.pem
#WEBSOCKET_HOST_CLIENTCAS=$OWGW_ROOT/certs/clientcas.pem
#WEBSOCKET_HOST_CAS=$OWGW_ROOT/certs/cas
#WEBSOCKET_HOST_PORT=15002
#WEBSOCKET_HOST_KEY_PASSWORD=mypassword
#RESTAPI_HOST_ROOTCA=$OWGW_ROOT/certs/restapi-ca.pem
#RESTAPI_HOST_PORT=16002
#RESTAPI_HOST_CERT=$OWGW_ROOT/certs/restapi-cert.pem
#RESTAPI_HOST_KEY=$OWGW_ROOT/certs/restapi-key.pem
#RESTAPI_HOST_KEY_PASSWORD=mypassword
#INTERNAL_RESTAPI_HOST_ROOTCA=$OWGW_ROOT/certs/restapi-ca.pem
#INTERNAL_RESTAPI_HOST_PORT=17002
#INTERNAL_RESTAPI_HOST_CERT=$OWGW_ROOT/certs/restapi-cert.pem
#INTERNAL_RESTAPI_HOST_KEY=$OWGW_ROOT/certs/restapi-key.pem
#INTERNAL_RESTAPI_HOST_KEY_PASSWORD=mypassword
#FILEUPLOADER_HOST_ROOTCA=$OWGW_ROOT/certs/restapi-ca.pem
FILEUPLOADER_HOST_NAME=openwifi.wlan.local
#FILEUPLOADER_HOST_PORT=16003
#FILEUPLOADER_HOST_CERT=$OWGW_ROOT/certs/restapi-cert.pem
#FILEUPLOADER_HOST_KEY=$OWGW_ROOT/certs/restapi-key.pem
#FILEUPLOADER_HOST_KEY_PASSWORD=mypassword
FILEUPLOADER_PATH=$OWGW_ROOT/persist/uploads
FILEUPLOADER_URI=https://openwifi.wlan.local:16003
#SERVICE_KEY=$OWGW_ROOT/certs/restapi-key.pem
#SERVICE_KEY_PASSWORD=mypassword
SYSTEM_DATA=$OWGW_ROOT/persist
SYSTEM_URI_PRIVATE=https://owgw.wlan.local:17002
SYSTEM_URI_PUBLIC=https://openwifi.wlan.local:16002
SYSTEM_URI_UI=https://openwifi.wlan.local
#SECURITY_RESTAPI_DISABLE=false
SIMULATORID=53494d010101
#IPTOCOUNTRY_PROVIDER=ipinfo
#IPTOCOUNTRY_IPINFO_TOKEN=
#IPTOCOUNTRY_IPDATA_APIKEY=
#RTTY_INTERNAL=true
#RTTY_ENABLED=true
RTTY_SERVER=openwifi.wlan.local
#RTTY_PORT=5912
#RTTY_TOKEN=
#RTTY_TIMEOUT=60
#RTTY_VIEWPORT=5913
#RTTY_ASSETS=$OWGW_ROOT/rtty_ui
RADIUS_PROXY_ENABLE=true
#RADIUS_PROXY_ACCOUNTING_PORT=1813
#RADIUS_PROXY_AUTHENTICATION_PORT=1812
#RADIUS_PROXY_COA_PORT=3799
#KAFKA_ENABLE=true
KAFKA_BROKERLIST=kafka:9092
STORAGE_TYPE=postgresql
STORAGE_TYPE_POSTGRESQL_HOST=postgresql
STORAGE_TYPE_POSTGRESQL_USERNAME=smb
STORAGE_TYPE_POSTGRESQL_PASSWORD=smb
STORAGE_TYPE_POSTGRESQL_DATABASE=smbdb
STORAGE_TYPE_POSTGRESQL_PORT=5432
#STORAGE_TYPE_MYSQL_HOST=mysql-service
#STORAGE_TYPE_MYSQL_USERNAME=root
#STORAGE_TYPE_MYSQL_PASSWORD=root
#STORAGE_TYPE_MYSQL_DATABASE=automation
#STORAGE_TYPE_MYSQL_PORT=3306
#STORAGE_TYPE=sqlite
LOGGING_LEVEL=trace

#CERTIFICATES_ALLOWMISMATCH=false
