#!/bin/sh
set -e

input_path="./docker_images"

echo ">>>>>>>>>>>>>>>>>>>   Import docker custom images Start ...               <<<<<<<<<<<<<<<<<<<<<<"
for image_file in "$input_path"/*.tar.gz; do
    image_name_tag=$(basename "$image_file" .tar.gz)
    image_name_tag=$(echo "$image_name_tag" | tr '_' '/')

    # Check if the image exists in Docker
    if docker images --format '{{.Repository}}:{{.Tag}}' | grep -q "^$image_name_tag$"; then
        :
    else
        docker load -i "$image_file"
    fi
done
echo ">>>>>>>>>>>>>>>>>>>   Import docker custom images successfully            <<<<<<<<<<<<<<<<<<<<<<"
