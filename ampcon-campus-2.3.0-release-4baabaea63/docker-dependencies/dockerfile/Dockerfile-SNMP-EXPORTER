FROM snmp-exporter:base

RUN rm -rf /app/*

RUN mkdir -p /app/snmp_exporter

COPY ../../data/snmp_exporter/ /app/snmp_exporter/
COPY ../../data/settings/encrypt_snmp_exporter_code.py /app/encrypt_snmp_exporter_code.py

RUN python3 /app/encrypt_snmp_exporter_code.py

RUN rm -rf /app/*
RUN mkdir -p /app/snmp_exporter
RUN cp -r /tmp/* /app/snmp_exporter/

RUN rm -rf /tmp/*

WORKDIR /app/snmp_exporter

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
