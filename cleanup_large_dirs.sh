#!/bin/bash

# 脚本用于清理系统中占用大量空间的目录
# 执行前请确保了解每个目录的作用并确认可以安全删除

echo "开始清理大文件目录..."

# 清理Yarn缓存
echo "清理Yarn缓存..."
rm -rf /usr/local/share/.cache/yarn

# 清理旧的服务器备份（保留最近的一个作为示例）
# 注意：请根据实际情况修改要保留的备份
echo "清理旧的服务器备份..."
rm -rf /usr/share/automation/server_1c328baa17_bak
rm -rf /usr/share/automation/server_817fc403a7_bak
rm -rf /usr/share/automation/server_a716c5cb6f_bak
rm -rf /usr/share/automation/server_8eef4be027_bak

# 可选：清理部分自动化数据（谨慎操作）
# echo "清理部分自动化数据..."
# rm -rf /usr/share/automation/server/data/img/*  # 只删除img内容，不删除目录本身

echo "清理完成！"

# 显示清理后的磁盘使用情况
echo "清理后磁盘使用情况:"
du -sh /usr/share/automation /usr/local/share/.cache 2>/dev/null