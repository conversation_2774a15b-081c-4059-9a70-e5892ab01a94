"""add last_login to user

Revision ID: 53e7ea037a2d
Revises: 65bc3d997236
Create Date: 2025-09-26 03:58:44.258642

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '53e7ea037a2d'
down_revision: Union[str, None] = 'v4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade():
    op.add_column('user', sa.Column('last_login', sa.DateTime, nullable=True))


def downgrade():
    op.drop_table('profile')
