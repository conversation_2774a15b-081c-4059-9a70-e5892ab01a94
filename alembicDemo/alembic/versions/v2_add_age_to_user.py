"""add age to user

Revision ID: v2
Revises: v1
Create Date: 2025-09-26 03:48:01.339145

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'v2'
down_revision: Union[str, None] = 'v1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.add_column('user', sa.Column('age', sa.Integer, nullable=True))

def downgrade():
    op.drop_column('user', 'age')
    pass
