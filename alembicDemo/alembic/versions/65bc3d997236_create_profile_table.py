"""create profile table

Revision ID: 65bc3d997236
Revises: v4
Create Date: 2025-09-26 03:58:37.320155

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '65bc3d997236'
down_revision: Union[str, None] = 'v4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.create_table(
        'profile',
        sa.Column('id', sa.Integer, primary_key=True),
        sa.Column('bio', sa.Text, nullable=True),
        sa.Column('user_id', sa.Integer, sa.<PERSON>ey('user.id'))
    )

def downgrade():
    op.drop_column('user', 'last_login')
