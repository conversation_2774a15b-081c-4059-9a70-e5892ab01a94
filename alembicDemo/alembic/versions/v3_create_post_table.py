"""create post table

Revision ID: v3
Revises: v2
Create Date: 2025-09-26 03:52:57.725848

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'v3'
down_revision: Union[str, None] = 'v2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.create_table('post',
        sa.<PERSON>umn('id', sa.Integer, primary_key=True),
        sa.Column('title', sa.String(200), nullable=False),
        sa.Column('content', sa.Text, nullable=False),
        sa.Column('user_id', sa.Integer, sa.<PERSON>('user.id'))
    )

def downgrade():
    op.drop_table('post')
