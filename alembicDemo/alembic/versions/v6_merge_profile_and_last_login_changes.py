"""merge profile and last_login changes

Revision ID: v6
Revises: 65bc3d997236, 53e7ea037a2d
Create Date: 2025-09-26 04:21:41.587672

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'v6'
down_revision: Union[str, None] = ('65bc3d997236', '53e7ea037a2d')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
