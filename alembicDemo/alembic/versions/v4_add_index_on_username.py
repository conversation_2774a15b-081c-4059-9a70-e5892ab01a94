"""add index on username

Revision ID: v4
Revises: v3
Create Date: 2025-09-26 03:53:05.650913

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'v4'
down_revision: Union[str, None] = 'v3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.create_index('ix_user_username', 'user', ['username'])

def downgrade():
    op.drop_index('ix_user_username', table_name='user')