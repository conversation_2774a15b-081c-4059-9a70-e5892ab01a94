#!/bin/bash

set -e

cd /usr/share/automation/server || {
  echo "Failed to enter /usr/share/automation/server"
  exit 1
}

source .env

usage() {
  echo "Usage: $0 [<OLD_SUBNET_IP> <NEW_SUBNET_IP>]"
  echo "Example: $0 ******** ********"
  exit 1
}


is_valid_ip() {
  local ip=$1
  if [[ $ip =~ ^([0-9]{1,3}\.){3}[0-9]{1,3}$ ]]; then
    IFS='.' read -r -a octets <<< "$ip"
    for octet in "${octets[@]}"; do
      if ((octet < 0 || octet > 255)); then
        return 1
      fi
    done
    return 0
  else
    return 1
  fi
}

replace_ips_in_files() {
  local old_base=$1
  local new_base=$2

  IFS='.' read -r o1 o2 o3 o4 <<< "$old_base"
  IFS='.' read -r n1 n2 n3 n4 <<< "$new_base"

  OLD_IP_0="${o1}.${o2}.${o3}.0"
  OLD_IP_1="${o1}.${o2}.${o3}.1"
  NEW_IP_0="${n1}.${n2}.${n3}.0"
  NEW_IP_1="${n1}.${n2}.${n3}.1"

  FILES=(
    "docker-compose.yml"
    "data/vpn/automation.up"
    "data/openvpn/server.conf"
    "data/vpn/update_vpn_ip.sh"
    "data/agent/auto-deploy.conf"
    "data/agent/vpn_config/server.conf"
    "data/agent/auto-deploy.py"
    "data/agent/enable_switch_vpn.sh"
    "data/config_gen/auto-deploy.j2"
  )

  DIRS=("data/config_gen")

  echo "transfer openvpn ip..."
  for file in "${FILES[@]}"; do
    if [[ -f "$file" ]]; then
      sed -i.bak \
        -e "s|$OLD_IP_0|$NEW_IP_0|g" \
        -e "s|$OLD_IP_1|$NEW_IP_1|g" "$file"
    fi
  done

  oldIFS=$IFS
  IFS=$'\n'
  for dir in "${DIRS[@]}"; do
    if [[ -d "$dir" ]]; then
      files=$(grep -rl --include="auto-deploy.conf" -E "$OLD_IP_0|$OLD_IP_1" "$dir")
      for f in $files; do
        sed -i.bak \
          -e "s|$OLD_IP_0|$NEW_IP_0|g" \
          -e "s|$OLD_IP_1|$NEW_IP_1|g" "$f"
      done
    fi
  done
  IFS=$oldIFS

  echo "transfer openvpn ip successfully!"
}

if [[ $# -eq 2 ]]; then
  OLD_BASE="$1"
  NEW_BASE="$2"

  if ! is_valid_ip "$OLD_BASE"; then
    echo "error: not valid ip: $OLD_BASE"
    usage
  fi

  if ! is_valid_ip "$NEW_BASE"; then
    echo "error: not valid ip: $NEW_BASE"
    usage
  fi

  replace_ips_in_files "$OLD_BASE" "$NEW_BASE"

  TEMP_SCRIPT="./extra_customize_scripts.sh"
  cat <<EOF > "$TEMP_SCRIPT"
openvpn_ip=\$(sudo docker exec flask-main-service getent hosts openvpn-service | awk '{ print \$1 }')
sudo docker exec flask-main-service ip route delete "$OLD_BASE"/20 2>/dev/null
sudo docker exec flask-main-service ip route delete "$OLD_BASE"/24 2>/dev/null
sudo docker exec flask-main-service ip route delete ********/24 2>/dev/null
sudo docker exec flask-main-service ip route add "$NEW_BASE"/20 via "\$openvpn_ip"
echo "OpenVPN IP updated successfully from $OLD_BASE to $NEW_BASE"
EOF
  chmod +x "$TEMP_SCRIPT"
  echo "Temporary script created at $TEMP_SCRIPT"


  TARGET_SCRIPT="start.sh"

  if ! grep -q "extra_customize_scripts" "$TARGET_SCRIPT"; then
    cat <<'EOF' >> "$TARGET_SCRIPT"
if [[ -f "./extra_customize_scripts.sh" ]]; then
  ./extra_customize_scripts.sh
fi
EOF
  fi


  ./start.sh
else
  echo "error: wrong number of arguments"
  usage
fi