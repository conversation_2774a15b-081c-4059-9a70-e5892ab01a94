FROM ampcon-rsyslog:base

COPY ../../data/rsyslog_conf/rsyslog.conf /etc/rsyslog.conf
COPY ../../data/rsyslog_conf/rsyslog_healthcheck.sh /usr/local/bin/rsyslog_healthcheck.sh
COPY ../../data/rsyslog_conf/start_rsyslog.sh /usr/local/bin/start_rsyslog.sh
COPY ../../data/rsyslog_conf/watch_config.sh /usr/local/bin/watch_config.sh
COPY ../../data/logrotate_conf/* /etc/logrotate.d/


RUN mkdir -p /var/log/automation
RUN mkdir -p /var/lib/rsyslog
RUN chmod +x /usr/local/bin/rsyslog_healthcheck.sh /usr/local/bin/start_rsyslog.sh /usr/local/bin/watch_config.sh

EXPOSE 514

ENTRYPOINT ["/usr/local/bin/start_rsyslog.sh"]
