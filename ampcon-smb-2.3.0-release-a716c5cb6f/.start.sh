#!/bin/bash

set -e

cd /usr/share/automation/server || {
  echo "Failed to enter /usr/share/automation/server"
  exit 1
}

source .env

if [ -n "$PROD_IP" ]; then
  export LOCAL_ADDRESS=${PROD_IP}
else
  export LOCAL_ADDRESS=$(hostname -I|awk '{print $1}')
fi

configure_ip_forward(){
  grep -q "^#net.ipv4.ip_forward=1" /etc/sysctl.conf || true
  if [ $? -eq 0 ]; then
      sed -i 's/^#net.ipv4.ip_forward=1/net.ipv4.ip_forward=1/' /etc/sysctl.conf
      sysctl -p
#      /etc/init.d/procps restart
  else
      grep -q "^net.ipv4.ip_forward=1" /etc/sysctl.conf || true
      if [ $? -ne 0 ]; then
          echo "net.ipv4.ip_forward=1" >> /etc/sysctl.conf
          sysctl -p
#          /etc/init.d/procps restart
      fi
  fi
}


configure_env(){
  PRO_PATH="./data"
  oldIFS=$IFS
  IFS=$'\n'
  default_amp_cfg="${PRO_PATH}/settings/inner/automation.ini"
  sed -i "s/^.*global_ip.*$/global_ip = $LOCAL_ADDRESS/g" $default_amp_cfg
  # deal with server domain name
  agent_cnf="${PRO_PATH}/agent/auto-deploy.conf"
  agent_cnf_dir="${PRO_PATH}/config_gen/"
  vpn_client_cnf="${PRO_PATH}/vpn/client.conf"
  onie_install_sh="${PRO_PATH}/onie_install/start.sh"
  system_ztp_sh="${PRO_PATH}/onie_install/system_ztp_start.sh"
  owgw_env="./smb/owgw.env"
#  srv_domain=`grep -o -P '(?<=server_vpn_host).*\b' $agent_cnf | sed 's/[ =]//g'`
  sed -i "s/server_vpn_host *=.*/server_vpn_host = $LOCAL_ADDRESS/g" $agent_cnf
  sed -i "s/server_vpn_host *=.*/server_vpn_host = $LOCAL_ADDRESS/g" `grep -P "server_vpn_host *= .*" -rl $agent_cnf_dir`
  sed -i "s/server_host=\".*\"/server_host=\"$LOCAL_ADDRESS\"/g" $onie_install_sh
  sed -i "s/server_host=\".*\"/server_host=\"$LOCAL_ADDRESS\"/g" $system_ztp_sh
  sed -i "s/remote .*/remote $LOCAL_ADDRESS 80/g" $vpn_client_cnf
  [ -f $owgw_env ] && sed -i "s/RTTY_SERVER=.*/RTTY_SERVER=$LOCAL_ADDRESS/g" $owgw_env && sed -i "s|FILEUPLOADER_URI=.*|FILEUPLOADER_URI=https://$LOCAL_ADDRESS:16003|g" $owgw_env
  IFS=$oldIFS
}


check_server_health(){
  end=$((SECONDS+300))
  while [ $SECONDS -lt $end ]; do
    nginx_container_id=$(sudo docker ps -qf name=nginx-service)
    if [ -z "$nginx_container_id" ]; then
      echo ">>>>>>>>>>>>>>>>>>> Sorry! container nginx-service is not running, please check error   <<<<<<<<<<<<<<<<<<<<<<"
      return 1
    else
      health_status=$(sudo docker inspect --format '{{.State.Health.Status}}' nginx-service)
      if [ "$health_status" = "healthy" ]; then
        echo ">>>>>>>>>>>>>>>>>>> Congratulations! container nginx-service is healthy  <<<<<<<<<<<<<<<<<<<<<<"
        return 0
      fi
    fi
    sleep 3
  done
  echo ">>>>>>>>>>>>>>>>>>> Sorry! container nginx-service did not become healthy within 1 minute... <<<<<<<<<<<<<<<<<<<<<<"
}


main(){
  echo ">>>>>>>>>>>>>>>>>>>   All docker containers prepare started ...           <<<<<<<<<<<<<<<<<<<<<<"
  configure_ip_forward
  configure_env
  docker compose down
  docker compose up -d
  check_server_health
}


SKIP_MAIN_AND_IMPORT=${1:-false}

if [ "$SKIP_MAIN_AND_IMPORT" == "true" ] && ! docker images | grep -q "ampcon-main"; then
  echo "Skipping ./import_images.sh and main function as per input argument."
  exit 0
fi

SCRIPT_DIR=$(cd "$(dirname "$(readlink -f "$0")")" && pwd)
cd "$SCRIPT_DIR" || exit
./import_images.sh
main

if [[ -f "./extra_customize_scripts.sh" ]]; then
  ./extra_customize_scripts.sh
fi




