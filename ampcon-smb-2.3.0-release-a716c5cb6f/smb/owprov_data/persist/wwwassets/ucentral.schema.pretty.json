{"$id": "https://openwrt.org/ucentral.schema.json", "$schema": "http://json-schema.org/draft-07/schema#", "description": "OpenWrt uCentral schema", "type": "object", "properties": {"strict": {"description": "The device will reject any configuration that causes warnings if strict mode is enabled.", "type": "boolean", "default": false}, "uuid": {"description": "The unique ID of the configuration. This is the unix timestamp of when the config was created.", "type": "integer"}, "unit": {"$ref": "#/$defs/unit"}, "globals": {"$ref": "#/$defs/globals"}, "definitions": {"$ref": "#/$defs/definitions"}, "ethernet": {"type": "array", "items": {"$ref": "#/$defs/ethernet"}}, "switch": {"$ref": "#/$defs/switch"}, "radios": {"type": "array", "items": {"$ref": "#/$defs/radio"}}, "interfaces": {"type": "array", "items": {"$ref": "#/$defs/interface"}}, "services": {"$ref": "#/$defs/service"}, "metrics": {"$ref": "#/$defs/metrics"}, "config-raw": {"$ref": "#/$defs/config-raw"}, "timeouts": {"$ref": "#/$defs/timeouts"}, "third-party": {"type": "object", "additionalProperties": true}}, "$defs": {"unit": {"description": "A device has certain properties that describe its identity and location. These properties are described inside this object.", "type": "object", "properties": {"name": {"description": "This is a free text field, stating the administrative name of the device. It may contain spaces and special characters.", "type": "string"}, "hostname": {"description": "The hostname that shall be set on the device. If this field is not set, then the devices serial number is used.", "type": "string", "format": "hostname"}, "location": {"description": "This is a free text field, stating the location of the  device. It may contain spaces and special characters.", "type": "string"}, "timezone": {"description": "This allows you to change the TZ of the device.", "type": "string", "examples": ["UTC", "EST5", "CET-1CEST,M3.5.0,M10.5.0/3"]}, "leds-active": {"description": "This allows forcing all LEDs off.", "type": "boolean", "default": true}, "random-password": {"description": "The device shall create a random root password and tell the gateway about it.", "type": "boolean", "default": false}, "system-password": {"description": "System-config string that holds the password for main (root / admin) user to apply.", "type": "string"}, "beacon-advertisement": {"description": "The TIP vendor IEs that shall be added to beacons", "type": "object", "properties": {"device-name": {"description": "Add an IE containing the device's name to beacons.", "type": "boolean"}, "device-serial": {"description": "Add an IE containing the device's serial to beacons.", "type": "boolean"}, "network-id": {"description": "A provider specific ID for the network/venue that the device is part of.", "type": "integer"}}}}}, "globals.wireless-multimedia.class-selector": {"type": "array", "items": {"type": "string", "enum": ["CS0", "CS1", "CS2", "CS3", "CS4", "CS5", "CS6", "CS7", "AF11", "AF12", "AF13", "AF21", "AF22", "AF23", "AF31", "AF32", "AF33", "AF41", "AF42", "AF43", "DF", "EF", "VA", "LE"]}}, "globals.wireless-multimedia.table": {"description": "Define the default WMM behaviour of all SSIDs on the device. Each access category can be assigned a default class selector that gets used for packet matching.", "type": "object", "additionalProperties": false, "properties": {"UP0": {"$ref": "#/$defs/globals.wireless-multimedia.class-selector"}, "UP1": {"$ref": "#/$defs/globals.wireless-multimedia.class-selector"}, "UP2": {"$ref": "#/$defs/globals.wireless-multimedia.class-selector"}, "UP3": {"$ref": "#/$defs/globals.wireless-multimedia.class-selector"}, "UP4": {"$ref": "#/$defs/globals.wireless-multimedia.class-selector"}, "UP5": {"$ref": "#/$defs/globals.wireless-multimedia.class-selector"}, "UP6": {"$ref": "#/$defs/globals.wireless-multimedia.class-selector"}, "UP7": {"$ref": "#/$defs/globals.wireless-multimedia.class-selector"}}}, "globals.wireless-multimedia.profile": {"type": "object", "additionalProperties": false, "properties": {"profile": {"description": "Define a default profile that shall be used for the WMM behaviour of all SSIDs on the device.", "type": "string", "enum": ["enterprise", "rfc8325", "3gpp"]}}}, "globals": {"description": "A device has certain global properties that are used to derive parts of the final configuration that gets applied.", "type": "object", "properties": {"ipv4-network": {"description": "Define the IPv4 range that is delegatable to the downstream interfaces This is described as a CIDR block. (***********/16, 172.16.128/17)", "type": "string", "format": "uc-cidr4", "examples": ["***********/16"]}, "ipv6-network": {"description": "Define the IPv6 range that is delegatable to the downstream interfaces This is described as a CIDR block. (fdca:1234:4567::/48)", "type": "string", "format": "uc-cidr6", "examples": ["fdca:1234:4567::/48"]}, "wireless-multimedia": {"anyOf": [{"$ref": "#/$defs/globals.wireless-multimedia.table"}, {"$ref": "#/$defs/globals.wireless-multimedia.profile"}]}}}, "interface.ssid.encryption": {"description": "A device has certain properties that describe its identity and location. These properties are described inside this object.", "type": "object", "properties": {"proto": {"description": "The wireless encryption protocol that shall be used for this BSS", "type": "string", "enum": ["none", "owe", "owe-transition", "psk", "psk2", "psk-mixed", "psk2-radius", "wpa", "wpa2", "wpa-mixed", "sae", "sae-mixed", "wpa3", "wpa3-192", "wpa3-mixed", "mpsk-radius"], "examples": ["psk2"]}, "key": {"description": "The Pre Shared Key (PSK) that is used for encryption on the BSS when using any of the WPA-PSK modes.", "type": "string", "maxLength": 63, "minLength": 8}, "ieee80211w": {"description": "Enable 802.11w Management Frame Protection (MFP) for this BSS.", "type": "string", "enum": ["disabled", "optional", "required"], "default": "disabled"}, "key-caching": {"description": "PMKSA created through EAP authentication and RSN preauthentication can be cached.", "type": "boolean", "default": true}}}, "definitions": {"description": "This section is used to define templates that can be referenced by a configuration. This avoids duplication of data. A RADIUS server can be defined here for example and then referenced by several SSIDs.", "type": "object", "properties": {"wireless-encryption": {"type": "object", "description": "A dictionary of wireless encryption templates which can be referenced by the corresponding property name.", "patternProperties": {".+": {"$ref": "#/$defs/interface.ssid.encryption", "additionalProperties": false}}}}}, "ethernet": {"description": "This section defines the linkk speed and duplex mode of the physical copper/fiber ports of the device.", "type": "object", "properties": {"select-ports": {"description": "The list of physical network devices that shall be configured. The names are logical ones and wildcardable.", "type": "array", "items": {"type": "string", "examples": ["LAN1", "LAN2", "LAN3", "LAN4", "LAN*", "WAN*", "*"]}}, "speed": {"description": "The link speed that shall be forced.", "type": "integer", "enum": [10, 100, 1000, 2500, 5000, 10000]}, "enabled": {"description": "This allows forcing the port to down state by default.", "type": "boolean", "default": true}, "services": {"description": "The services that shall be offered on this L2 interface.", "type": "array", "items": {"type": "string", "examples": ["quality-of-service"]}}, "poe": {"description": "This section describes the ethernet poe-port configuration object.", "type": "object", "properties": {"admin-mode": {"description": "Option to force admin state over selected port. Setting to <false> immediately shuts down power. Setting to <true> starts PoE hanshake (Power sourcing equipment < - > Power Device) sequence and in case of success, power is being delivered to Powered Device.", "type": "boolean", "default": true}}}}}, "switch": {"description": "This section defines the switch fabric specific features of a physical switch.", "type": "object", "properties": {"port-mirror": {"description": "Enable mirror of traffic from multiple minotor ports to a single analysis port.", "type": "object", "properties": {"monitor-ports": {"description": "The list of ports that we want to mirror.", "type": "array", "items": {"type": "string"}}, "analysis-port": {"description": "The port that mirror'ed packets should be sent to.", "type": "string"}}}, "loop-detection": {"description": "Enable loop detection on the L2 switches/bridge.", "type": "object", "properties": {"protocol": {"description": "Define which protocol shall be used for loop detection.", "type": "string", "enum": ["rstp"], "default": "rstp"}, "roles": {"description": "Define on which logical switches/bridges we want to provide loop-detection.", "type": "array", "items": {"type": "string", "enum": ["upstream", "downstream"]}}}}}}, "radio.rates": {"description": "The rate configuration of this BSS.", "type": "object", "properties": {"beacon": {"description": "The beacon rate that shall be used by the BSS. Values are in Mbps.", "type": "integer", "default": 6000, "enum": [0, 1000, 2000, 5500, 6000, 9000, 11000, 12000, 18000, 24000, 36000, 48000, 54000]}, "multicast": {"description": "The multicast rate that shall be used by the BSS. Values are in Mbps.", "type": "integer", "default": 24000, "enum": [0, 1000, 2000, 5500, 6000, 9000, 11000, 12000, 18000, 24000, 36000, 48000, 54000]}}}, "radio.he": {"description": "This section describes the HE specific configuration options of the BSS.", "type": "object", "properties": {"multiple-bssid": {"description": "Enabling this option will make the PHY broadcast its BSSs using the multiple BSSID beacon IE.", "type": "boolean", "default": false}, "ema": {"description": "Enableing this option will make the PHY broadcast its multiple BSSID beacons using EMA.", "type": "boolean", "default": false}, "bss-color": {"description": "This enables BSS Coloring on the PHY. setting it to 0 disables the feature 1-63 sets the color and 64 will make hostapd pick a random color.", "type": "integer", "minimum": 0, "maximum": 64, "default": 0}}}, "radio.he-6ghz": {"type": "object", "properties": {"power-type": {"description": "This config is to set the 6 GHz Access Point type", "type": "string", "enum": ["indoor-power-indoor", "standard-power", "very-low-power"], "default": "very-low-power"}, "controller": {"description": "The URL of the AFC controller that the AP shall connect to.", "type": "string"}, "ca-certificate": {"description": "The CA of the server. This enables mTLS.", "type": "string", "format": "uc-base64"}, "serial-number": {"description": "The serial number that the AP shall send to the AFC controller.", "type": "string"}, "request-id": {"description": "The request-id that the AP shall send to the AFC controller.", "type": "string"}, "certificate-ids": {"description": "The certificate IDs that the AP shall send to the AFC controller.", "type": "string"}, "minimum-power": {"description": "The minimum power that the AP shall request from to the AFC controller.", "type": "number"}, "frequency-ranges": {"description": "The list of frequency ranges that the AP shall request from to the AFC controller.", "type": "array", "items": {"type": "string"}}, "operating-classes": {"description": "The list of frequency ranges that the AP shall request from to the AFC controller.", "type": "array", "items": {"type": "number"}}, "access-token": {"type": "string"}}}, "radio": {"description": "Describe a physical radio on the AP. A radio is be parent to several VAPs. They all share the same physical properties.", "type": "object", "properties": {"band": {"description": "Specifies the wireless band to configure the radio for. Available radio device phys on the target system are matched by the wireless band given here. If multiple radio phys support the same band, the settings specified here will be applied to all of them.", "type": "string", "enum": ["2G", "5G", "5G-lower", "5G-upper", "6G", "HaLow"]}, "bandwidth": {"description": "Specifies a narrow channel width in MHz, possible values are 5, 10, 20.", "type": "integer", "enum": [5, 10, 20]}, "channel": {"description": "Specifies the wireless channel to use. A value of 'auto' starts the ACS algorithm.", "oneOf": [{"type": "integer", "maximum": 233, "minimum": 1}, {"type": "string", "const": "auto"}]}, "valid-channels": {"description": "Pass a list of valid-channels that can be used during ACS.", "type": "array", "items": {"type": "integer", "maximum": 233, "minimum": 1}}, "acs-exclude-6ghz-non-psc": {"description": "Exclude non-psc when doing auto channel selection on 6GHz", "type": "boolean", "default": false}, "country": {"description": "Specifies the country code, affects the available channels and transmission powers.", "type": "string", "maxLength": 2, "minLength": 2, "examples": ["US"]}, "allow-dfs": {"description": "This property defines whether a radio may use DFS channels.", "type": "boolean", "default": true}, "channel-mode": {"description": "Define the ideal channel mode that the radio shall use. This can be 802.11n, 802.11ac or 802.11ax. This is just a hint for the AP. If the requested value is not supported then the AP will use the highest common denominator.", "type": "string", "enum": ["HT", "VHT", "HE", "EHT"], "default": "HE"}, "channel-width": {"description": "The channel width that the radio shall use. This is just a hint for the AP. If the requested value is not supported then the AP will use the highest common denominator.", "type": "integer", "enum": [1, 2, 4, 8, 20, 40, 80, 160, 320, 8080], "default": 80}, "enable": {"description": "Specifies radio is enabled/disabled.", "type": "boolean"}, "require-mode": {"description": "Stations that do no fulfill these HT modes will be rejected.", "type": "string", "enum": ["HT", "VHT", "HE"]}, "mimo": {"description": "This option allows configuring the antenna pairs that shall be used. This is just a hint for the AP. If the requested value is not supported then the AP will use the highest common denominator.", "type": "string", "enum": ["1x1", "2x2", "3x3", "4x4", "5x5", "6x6", "7x7", "8x8"]}, "tx-power": {"description": "This option specifies the transmission power in dBm", "type": "integer", "maximum": 30, "minimum": 0}, "legacy-rates": {"description": "Allow legacy 802.11b data rates.", "type": "boolean", "default": false}, "maximum-clients": {"description": "Set the maximum number of clients that may connect to this radio. This value is accumulative for all attached VAP interfaces.", "type": "integer", "example": 64}, "maximum-clients-ignore-probe": {"description": "Ignore probe requests if maximum-clients was reached.", "type": "boolean"}, "rates": {"$ref": "#/$defs/radio.rates"}, "he-settings": {"$ref": "#/$defs/radio.he"}, "he-6ghz-settings": {"$ref": "#/$defs/radio.he-6ghz"}, "hostapd-iface-raw": {"description": "This array allows passing raw hostapd.conf lines.", "type": "array", "items": {"type": "string", "examples": ["ap_table_expiration_time=3600", "device_type=6-0050F204-1", "ieee80211h=1", "rssi_ignore_probe_request=-75", "time_zone=EST5", "uuid=12345678-9abc-def0-1234-56789abcdef0", "venue_url=1:http://www.example.com/info-eng", "wpa_deny_ptk0_rekey=0"]}}}}, "interface.vlan": {"description": "This section describes the vlan behaviour of a logical network interface.", "type": "object", "properties": {"id": {"description": "This is the pvid of the vlan that shall be assigned to the interface. The individual physical network devices contained within the interface need to be told explicitly if egress traffic shall be tagged.", "type": "integer", "maximum": 4050}, "proto": {"decription": "The L2 vlan tag that shall be added (1q,1ad)", "type": "string", "enum": ["802.1ad", "802.1q"], "default": "802.1q"}}}, "interface.bridge": {"description": "This section describes the bridge behaviour of a logical network interface.", "type": "object", "properties": {"mtu": {"description": "The MTU that shall be used by the network interface.", "type": "integer", "maximum": 65535, "minimum": 256, "examples": [1500]}, "tx-queue-len": {"description": "The Transmit Queue Length is a TCP/IP stack network interface value that sets the number of packets allowed per kernel transmit queue of a network interface device.", "type": "integer", "examples": [5000]}, "isolate-ports": {"description": "Isolates the bridge ports from each other.", "type": "boolean", "default": false}}}, "interface.ethernet": {"description": "This section defines the physical copper/fiber ports that are members of the interface. Network devices are referenced by their logical names.", "type": "object", "properties": {"select-ports": {"description": "The list of physical network devices that shall be added to the interface. The names are logical ones and wildcardable. \"WAN\" will use whatever the hardwares default upstream facing port is. \"LANx\" will use the \"x'th\" downstream facing ethernet port. LAN* will use all downstream ports.", "type": "array", "items": {"type": "string", "examples": ["LAN1", "LAN2", "LAN3", "LAN4", "LAN*", "WAN*", "*"]}}, "multicast": {"description": "Enable multicast support.", "type": "boolean", "default": true}, "learning": {"description": "Controls whether a given port will learn MAC addresses from received traffic or not. If learning if off, the bridge will end up flooding any traffic for which it has no FDB entry. By default this flag is on.", "type": "boolean", "default": true}, "isolate": {"description": "Only allow communication with non-isolated bridge ports when enabled.", "type": "boolean", "default": false}, "macaddr": {"description": "Enforce a specific MAC to these ports.", "type": "string", "format": "uc-mac"}, "reverse-path-filter": {"description": "Reverse Path filtering is a method used by the Linux Kernel to help prevent attacks used by Spoofing IP Addresses.", "type": "boolean", "default": false}, "vlan-tag": {"description": "Shall the port have a vlan tag.", "type": "string", "enum": ["tagged", "un-tagged", "auto"], "default": "auto"}}}, "interface.ipv4.dhcp": {"description": "This section describes the DHCP server configuration", "type": "object", "properties": {"lease-first": {"description": "The last octet of the first IPv4 address in this DHCP pool.", "type": "integer", "examples": [10]}, "lease-count": {"description": "The number of IPv4 addresses inside the DHCP pool.", "type": "integer", "examples": [100]}, "lease-time": {"description": "How long the lease is valid before a RENEW must be issued.", "type": "string", "format": "uc-timeout", "default": "6h"}, "use-dns": {"description": "The DNS server sent to clients as DHCP option 6.", "anyOf": [{"type": "string", "format": "ipv4"}, {"type": "array", "items": {"type": "string", "format": "ipv4"}}]}}}, "interface.ipv4.dhcp-lease": {"description": "This section describes the static DHCP leases of this logical interface.", "type": "object", "properties": {"macaddr": {"description": "The MAC address of the host that this lease shall be used for.", "type": "string", "format": "uc-mac", "examples": ["00:11:22:33:44:55"]}, "static-lease-offset": {"description": "The offset of the IP that shall be used in relation to the first IP in the available range.", "type": "integer", "examples": [10]}, "lease-time": {"description": "How long the lease is valid before a RENEW muss ne issued.", "type": "string", "format": "uc-timeout", "default": "6h"}, "publish-hostname": {"description": "Shall the hosts hostname be made available locally via DNS.", "type": "boolean", "default": true}}}, "interface.ipv4.port-forward": {"description": "This section describes an IPv4 port forwarding.", "type": "object", "properties": {"protocol": {"description": "The layer 3 protocol to match.", "type": "string", "enum": ["tcp", "udp", "any"], "default": "any"}, "external-port": {"description": "The external port(s) to forward.", "type": ["integer", "string"], "minimum": 0, "maximum": 65535, "format": "uc-portrange"}, "internal-address": {"description": "The internal IP to forward to. The address will be masked and concatenated with the effective interface subnet.", "type": "string", "format": "ipv4", "example": "*********"}, "internal-port": {"description": "The internal port to forward to. Defaults to the external port if omitted.", "type": ["integer", "string"], "minimum": 0, "maximum": 65535, "format": "uc-portrange"}}, "required": ["external-port", "internal-address"]}, "interface.ipv4": {"description": "This section describes the IPv4 properties of a logical interface.", "type": "object", "properties": {"addressing": {"description": "This option defines the method by which the IPv4 address of the interface is chosen.", "type": "string", "enum": ["dynamic", "static", "none"], "examples": ["static"]}, "subnet": {"description": "This option defines the static IPv4 of the logical interface in CIDR notation. auto/24 can be used, causing the configuration layer to automatically use and address range from globals.ipv4-network.", "type": "string", "format": "uc-cidr4", "examples": ["auto/24"]}, "gateway": {"description": "This option defines the static IPv4 gateway of the logical interface.", "type": "string", "format": "ipv4", "examples": ["***********"]}, "send-hostname": {"description": "include the devices hostname inside DHCP requests", "type": "boolean", "default": true, "examples": [true]}, "vendor-class": {"description": "Include the provided vendor-class inside DHCP requests", "type": "string", "default": "OpenLAN", "examples": ["OpenLAN"]}, "request-options": {"description": "Define additional DHCP options to request inside DHCP requests", "type": "array", "default": [43, 60, 138, 224], "items": {"type": "integer", "minimum": 1, "maximum": 255, "examples": [43]}}, "use-dns": {"description": "Define which DNS servers shall be used. This can either be a list of static IPv4 addresse or dhcp (use the server provided by the DHCP lease)", "type": "array", "items": {"type": "string", "format": "ipv4", "examples": ["*******", "*******"]}}, "disallow-upstream-subnet": {"description": "This option only applies to \"downstream\" interfaces. The downstream interface will prevent traffic going out to the listed CIDR4s. This can be used to prevent a guest / captive interface being able to communicate with RFC1918 ranges.", "type": "array", "items": {"type": "string", "format": "uc-cidr4", "examples": ["***********/16", "10.0.0.0/8"]}}, "dhcp": {"$ref": "#/$defs/interface.ipv4.dhcp"}, "dhcp-leases": {"type": "array", "items": {"$ref": "#/$defs/interface.ipv4.dhcp-lease"}}, "port-forward": {"type": "array", "items": {"$ref": "#/$defs/interface.ipv4.port-forward"}}}}, "interface.ipv6.dhcpv6": {"description": "This section describes the DHCPv6 server configuration", "type": "object", "properties": {"mode": {"description": "Specifies the DHCPv6 server operation mode. When set to \"stateless\", the system will announce router advertisements only, without offering stateful DHCPv6 service. When set to \"stateful\", emitted router advertisements will instruct clients to obtain a DHCPv6 lease. When set to \"hybrid\", clients can freely chose whether to self-assign a random address through SLAAC, whether to request an address via DHCPv6, or both. For maximum compatibility with different clients, it is recommended to use the hybrid mode. The special mode \"relay\" will instruct the unit to act as DHCPv6 relay between this interface and any of the IPv6 interfaces in \"upstream\" mode.", "type": "string", "enum": ["hybrid", "stateless", "stateful", "relay"]}, "announce-dns": {"description": "Overrides the DNS server to announce in DHCPv6 and RA messages. By default, the device will announce its own local interface address as DNS server, essentially acting as proxy for downstream clients. By specifying a non-empty list of IPv6 addresses here, this default behaviour can be overridden.", "type": "array", "items": {"type": "string", "format": "ipv6"}}, "filter-prefix": {"description": "Selects a specific downstream prefix or a number of downstream prefix ranges to announce in DHCPv6 and RA messages. By default, all prefixes configured on a given downstream interface are advertised. By specifying an IPv6 prefix in CIDR notation here, only prefixes covered by this CIDR are selected.", "type": "string", "format": "uc-cidr6", "default": "::/0"}}}, "interface.ipv6.port-forward": {"description": "This section describes an IPv6 port forwarding.", "type": "object", "properties": {"protocol": {"description": "The layer 3 protocol to match.", "type": "string", "enum": ["tcp", "udp", "any"], "default": "any"}, "external-port": {"description": "The external port(s) to forward.", "type": ["integer", "string"], "minimum": 0, "maximum": 65535, "format": "uc-portrange"}, "internal-address": {"description": "The internal IP to forward to. The address will be masked and concatenated with the effective interface subnet.", "type": "string", "format": "ipv6", "example": "::1234:abcd"}, "internal-port": {"description": "The internal port to forward to. Defaults to the external port if omitted.", "type": ["integer", "string"], "minimum": 0, "maximum": 65535, "format": "uc-portrange"}}, "required": ["external-port", "internal-address"]}, "interface.ipv6.traffic-allow": {"description": "This section describes an IPv6 traffic accept rule.", "type": "object", "properties": {"protocol": {"description": "The layer 3 protocol to match.", "type": "string", "default": "any"}, "source-address": {"description": "The source IP to allow traffic from.", "type": "string", "format": "uc-cidr6", "example": "2001:db8:1234:abcd::/64", "default": "::/0"}, "source-ports": {"description": "The source port(s) to accept.", "type": "array", "minItems": 1, "items": {"type": ["integer", "string"], "minimum": 0, "maximum": 65535, "format": "uc-portrange"}}, "destination-address": {"description": "The destination IP to allow traffic to. The address will be masked and concatenated with the effective interface subnet.", "type": "string", "format": "ipv6", "example": "::1000"}, "destination-ports": {"description": "The destination ports to accept.", "type": "array", "minItems": 1, "items": {"type": ["integer", "string"], "minimum": 0, "maximum": 65535, "format": "uc-portrange"}}}, "required": ["destination-address"]}, "interface.ipv6": {"description": "This section describes the IPv6 properties of a logical interface.", "type": "object", "properties": {"addressing": {"description": "This option defines the method by which the IPv6 subnet of the interface is acquired. In static addressing mode, the specified subnet and gateway, if any, are configured on the interface in a fixed manner. Also - if a prefix size hint is specified - a prefix of the given size is allocated from each upstream received prefix delegation pool and assigned to the interface. In dynamic addressing mode, a DHCPv6 client will be launched to obtain IPv6 prefixes for the interface itself and for downstream delegation. Note that dynamic addressing usually only ever makes sense on upstream interfaces.", "type": "string", "enum": ["dynamic", "static"]}, "subnet": {"description": "This option defines a static IPv6 prefix in CIDR notation to set on the logical interface. A special notation \"auto/64\" can be used, causing the configuration agent to automatically allocate a suitable prefix from the IPv6 address pool specified in globals.ipv6-network. This property only applies to static addressing mode. Note that this is usually not needed due to DHCPv6-PD assisted prefix assignment.", "type": "string", "format": "uc-cidr6", "examples": ["auto/64"]}, "gateway": {"description": "This option defines the static IPv6 gateway of the logical interface. It only applies to static addressing mode. Note that this is usually not needed due to DHCPv6-PD assisted prefix assignment.", "type": "string", "format": "ipv6", "examples": ["2001:db8:123:456::1"]}, "prefix-size": {"description": "For dynamic addressing interfaces, this property specifies the prefix size to request from an upstream DHCPv6 server through prefix delegation. For static addressing interfaces, it specifies the size of the sub-prefix to allocate from the upstream-received delegation prefixes for assignment to the logical interface.", "type": "integer", "maximum": 64, "minimum": 0}, "dhcpv6": {"$ref": "#/$defs/interface.ipv6.dhcpv6"}, "port-forward": {"type": "array", "items": {"$ref": "#/$defs/interface.ipv6.port-forward"}}, "traffic-allow": {"type": "array", "items": {"$ref": "#/$defs/interface.ipv6.traffic-allow"}}}}, "interface.broad-band.wwan": {"description": "This Object defines the properties of a broad-band uplink.", "type": "object", "properties": {"protocol": {"description": "This uplink uses WWAN/LTE", "type": "string", "const": "wwan"}, "modem-type": {"description": "The local protocol that the modem supports.", "type": "string", "enum": ["qmi", "mbim", "wwan"]}, "access-point-name": {"description": "Commonly known as APN. The name of a gateway between a mobile network and the internet.", "type": "string"}, "authentication-type": {"description": "The authentication mode that shall be used.", "type": "string", "enum": ["none", "pap", "chap", "pap-chap"], "default": "none"}, "pin-code": {"description": "The PIN that shall be used to unlock the SIM card.", "type": "string"}, "user-name": {"description": "This option is only required if an authentication-type is defined.", "type": "string"}, "password": {"description": "This option is only required if an authentication-type is defined.", "type": "string"}, "packet-data-protocol": {"description": "Define what kind of IP stack shall be used.", "type": "string", "enum": ["ipv4", "ipv6", "dual-stack"], "default": "dual-stack"}}}, "interface.broad-band.pppoe": {"description": "This Object defines the properties of a PPPoE uplink.", "type": "object", "properties": {"protocol": {"description": "This uplink uses PPPoE", "type": "string", "const": "pppoe"}, "user-name": {"description": "The username used to authenticate.", "type": "string"}, "password": {"description": "The password used to authenticate.", "type": "string"}}}, "interface.broad-band": {"oneOf": [{"$ref": "#/$defs/interface.broad-band.wwan"}, {"$ref": "#/$defs/interface.broad-band.pppoe"}]}, "interface.ssid.multi-psk": {"type": "object", "description": "A SSID can have multiple PSK/VID mappings. Each one of them can be bound to a specific MAC or be a wildcard.", "properties": {"mac": {"type": "string", "format": "uc-mac"}, "key": {"description": "The Pre Shared Key (PSK) that is used for encryption on the BSS when using any of the WPA-PSK modes.", "type": "string", "maxLength": 63, "minLength": 8}, "vlan-id": {"type": "integer", "maximum": 4096, "examples": [3, 100, 200, 4094]}}}, "interface.ssid.rrm": {"description": "Enable 802.11k Radio Resource Management (RRM) for this BSS.", "type": "object", "properties": {"neighbor-reporting": {"description": "Enable neighbor report via radio measurements (802.11k).", "type": "boolean", "default": false}, "reduced-neighbor-reporting": {"description": "Enable reduced neighbor reports.", "type": "boolean", "default": false}, "lci": {"description": "The content of a LCI measurement subelement", "type": "string"}, "civic-location": {"description": "The content of a location civic measurement subelement", "type": "string"}, "ftm-responder": {"description": "Publish fine timing measurement (FTM) responder functionality on this BSS.", "type": "boolean", "default": false}, "stationary-ap": {"description": "Stationary AP config indicates that the AP doesn't move.", "type": "boolean", "default": false}}}, "interface.ssid.rate-limit": {"description": "The UE rate-limiting configuration of this BSS.", "type": "object", "properties": {"ingress-rate": {"description": "The ingress rate to which hosts will be shaped. Values are in Mbps", "type": "integer", "default": 0}, "egress-rate": {"description": "The egress rate to which hosts will be shaped. Values are in Mbps", "type": "integer", "default": 0}}}, "interface.ssid.roaming": {"description": "Enable 802.11r Fast Roaming for this BSS.", "type": "object", "properties": {"message-exchange": {"description": "Shall the pre authenticated message exchange happen over the air or distribution system.", "type": "string", "enum": ["air", "ds"], "default": "air"}, "generate-psk": {"description": "Whether to generate FT response locally for PSK networks. This avoids use of PMK-R1 push/pull from other APs with FT-PSK networks.", "type": "boolean", "default": false}, "domain-identifier": {"description": "Mobility Domain identifier (dot11FTMobilityDomainID, MDID).", "type": "string", "format": "uc-mobility", "examples": ["abcd"]}, "pmk-r0-key-holder": {"description": "The pairwise master key R0. This is unique to the mobility domain and is required for fast roaming over the air. If the field is left empty a deterministic key is generated.", "type": "string", "example": "14:DD:20:47:14:E4,14DD204714E4,00112233445566778899aabb<PERSON><PERSON><PERSON>ff"}, "pmk-r1-key-holder": {"description": "The pairwise master key R1. This is unique to the mobility domain and is required for fast roaming over the air. If the field is left empty a deterministic key is generated.", "type": "string", "example": "14:DD:20:47:14:E4,14DD204714E4,00112233445566778899aabb<PERSON><PERSON><PERSON>ff"}, "key-aes-256": {"description": "The AES-256 shared amongst a mobility domain. The R0/1K key pairs will be autogenerated based on this value.", "type": "string", "minimum": 64, "maximum": 64}}}, "interface.ssid.radius.local-user": {"type": "object", "description": "Describes a local EAP user/psk/vid triplet.", "properties": {"mac": {"type": "string", "format": "uc-mac"}, "user-name": {"type": "string", "minLength": 1}, "password": {"type": "string", "maxLength": 63, "minLength": 8}, "vlan-id": {"type": "integer", "maximum": 4096, "examples": [3, 100, 200, 4094]}}}, "interface.ssid.radius.local": {"description": "Describe the properties of the local Radius server inside hostapd.", "type": "object", "properties": {"server-identity": {"description": "EAP methods that provide mechanism for authenticated server identity delivery use this value.", "type": "string", "default": "uCentral"}, "users": {"description": "Specifies a collection of local EAP user/psk/vid triplets.", "type": "array", "items": {"$ref": "#/$defs/interface.ssid.radius.local-user"}}}}, "interface.ssid.radius.server": {"description": "Describe the properties of a Radius server.", "type": "object", "properties": {"host": {"description": "The URI of our Radius server.", "type": "string", "format": "uc-host", "examples": ["***********0"]}, "port": {"description": "The network port of our Radius server.", "type": "integer", "maximum": 65535, "minimum": 1024, "examples": [1812]}, "secret": {"description": "The shared Radius authentication secret.", "type": "string", "examples": ["secret"]}, "secondary": {"description": "Definition of the secondary/failsafe radius server.", "type": "object", "properties": {"host": {"description": "The URI of our Radius server.", "type": "string", "format": "uc-host", "examples": ["***********0"]}, "port": {"description": "The network port of our Radius server.", "type": "integer", "maximum": 65535, "minimum": 1024, "examples": [1812]}, "secret": {"description": "The shared Radius authentication secret.", "type": "string", "examples": ["secret"]}}}, "request-attribute": {"description": "The additional Access-Request attributes that gets sent to the server.", "type": "array", "items": {"anyOf": [{"type": "object", "properties": {"vendor-id": {"type": "integer", "description": "The ID of the vendor specific RADIUS attribute", "maximum": 65535, "minimum": 1}, "vendor-attributes": {"type": "array", "items": {"type": "object", "description": "The numeric RADIUS attribute value", "properties": {"id": {"type": "integer", "description": "The ID of the vendor specific RADIUS attribute", "maximum": 255, "minimum": 1}, "value": {"type": "string", "description": "The vendor specific RADIUS attribute value. This needs to be a hexadecimal string."}}}}}}, {"type": "object", "properties": {"id": {"type": "integer", "description": "The ID of the RADIUS attribute", "maximum": 255, "minimum": 1}, "value": {"type": "integer", "description": "The numeric RADIUS attribute value", "maximum": **********, "minimum": 0}}, "examples": [{"id": 27, "value": 900}, {"id": 56, "value": 1004}]}, {"type": "object", "properties": {"id": {"type": "integer", "description": "The ID of the RADIUS attribute", "maximum": 255, "minimum": 1}, "value": {"type": "string", "description": "The RADIUS attribute value string"}}, "examples": [{"id": 32, "value": "My NAS ID"}, {"id": 126, "value": "Example Operator"}]}, {"type": "object", "properties": {"id": {"type": "integer", "description": "The ID of the RADIUS attribute", "maximum": 255, "minimum": 1}, "hex-value": {"type": "string", "description": "The RADIUS attribute value string"}}, "examples": [{"id": 32, "value": "0a0b0c0d"}]}]}}}}, "interface.ssid.radius.health": {"description": "The credentials used when health check probes this radius server.", "type": "object", "properties": {"username": {"description": "The username that gets used when doing a healthcheck on this radius server.", "type": "string"}, "password": {"description": "The password that gets used when doing a healthcheck on this radius server.", "type": "string"}}}, "interface.ssid.radius": {"description": "When using EAP encryption we need to provide the required information allowing us to connect to the AAA servers.", "type": "object", "properties": {"nas-identifier": {"description": "NAS-Identifier string for RADIUS messages. When used, this should be unique to the NAS within the scope of the RADIUS server.", "type": "string"}, "chargeable-user-id": {"description": "This will enable support for Chargeable-User-Identity (RFC 4372).", "type": "boolean", "default": false}, "local": {"$ref": "#/$defs/interface.ssid.radius.local"}, "dynamic-authorization": {"description": "Dynamic Authorization Extensions (DAE) is an extension to Radius.", "type": "object", "properties": {"host": {"description": "The IP of the DAE client.", "type": "string", "format": "uc-ip", "examples": ["***********0"]}, "port": {"description": "The network port that the DAE client can connet on.", "type": "integer", "maximum": 65535, "minimum": 1024, "examples": [1812]}, "secret": {"description": "The shared DAE authentication secret.", "type": "string", "examples": ["secret"]}}}, "authentication": {"allOf": [{"$ref": "#/$defs/interface.ssid.radius.server"}, {"type": "object", "properties": {"mac-filter": {"description": "Should the radius server be used for MAC address ACL.", "type": "boolean", "default": false}}}]}, "accounting": {"allOf": [{"$ref": "#/$defs/interface.ssid.radius.server"}, {"type": "object", "properties": {"interval": {"description": "The interim accounting update interval. This value is defined in seconds.", "type": "integer", "maximum": 600, "minimum": 60, "default": 60}}}]}, "health": {"$ref": "#/$defs/interface.ssid.radius.health"}}}, "interface.ssid.certificates": {"description": "When running a local EAP server or using STA/MESH to connect to another BSS a set of certificates is required.", "type": "object", "properties": {"use-local-certificates": {"description": "The device will use its local certificate bundle for the TLS setup and ignores all other certificate options in this section.", "type": "boolean", "default": false}, "ca-certificate": {"description": "The local servers CA bundle.", "type": "string"}, "certificate": {"description": "The local servers certificate.", "type": "string"}, "private-key": {"description": "The local servers private key/", "type": "string"}, "private-key-password": {"description": "The password required to read the private key.", "type": "string"}}}, "interface.ssid.pass-point": {"description": "Enable Hotspot 2.0 support.", "type": "object", "properties": {"venue-name": {"description": "This parameter can be used to configure one or more Venue Name Duples for Venue Name ANQP information.", "type": "array", "items": {"type": "string"}}, "venue-group": {"description": "The available values are defined in 802.11u.", "type": "integer", "maximum": 32}, "venue-type": {"description": "The available values are defined in IEEE Std 802.11u-2011, ********", "type": "integer", "maximum": 32}, "venue-url": {"description": "This parameter can be used to configure one or more Venue URL Duples to provide additional information corresponding to Venue Name information.", "type": "array", "items": {"type": "string", "format": "uri"}}, "auth-type": {"description": "This parameter indicates what type of network authentication is used in the network.", "type": "object", "properties": {"type": {"description": "Specifies the specific network authentication type in use.", "type": "string", "enum": ["terms-and-conditions", "online-enrollment", "http-redirection", "dns-redirection"]}, "uri": {"description": "Specifies the redirect URL applicable to the indicated authentication type.", "type": "string", "format": "uri", "examples": ["https://operator.example.org/wireless-access/terms-and-conditions.html", "http://www.example.com/redirect/me/here/"]}}, "minLength": 2, "maxLength": 2}, "domain-name": {"description": "The IEEE 802.11u Domain Name.", "type": "array", "items": {"type": "string", "format": "hostname"}}, "nai-realm": {"description": "NAI Realm information", "type": "array", "items": {"type": "string"}}, "osen": {"description": "OSU Server-Only Authenticated L2 Encryption Network;", "type": "boolean"}, "anqp-domain": {"description": "ANQP Domain ID, An identifier for a set of APs in an ESS that share the same common ANQP information.", "type": "integer", "maximum": 65535, "minimum": 0}, "anqp-3gpp-cell-net": {"description": "The ANQP 3GPP Cellular Network information.", "type": "array", "items": {"type": "string"}}, "friendly-name": {"description": "This parameter can be used to configure one or more Operator Friendly Name Duples.", "type": "array", "items": {"type": "string"}}, "access-network-type": {"description": "Indicate the type of network. This is part of the interworking IE.", "type": "integer", "maximum": 15, "default": 0}, "internet": {"description": "Whether the network provides connectivity to the Internet", "type": "boolean", "default": true}, "asra": {"description": "Additional Step Required for Access.", "type": "boolean", "default": false}, "esr": {"description": "Emergency services reachable.", "type": "boolean", "default": false}, "uesa": {"description": "Unauthenticated emergency service accessible.", "type": "boolean", "default": false}, "hessid": {"description": "Homogeneous ESS identifier", "type": "string", "example": "00:11:22:33:44:55"}, "roaming-consortium": {"description": "Roaming Consortium OIs can be configured here. Each OI is between 3 and 15 octets and is configured as a hexstring.", "type": "array", "items": {"type": "string"}}, "disable-dgaf": {"description": "Disable Downstream Group-Addressed Forwarding. This can be used to configure a network where no group-addressed frames are allowed.", "type": "boolean", "default": false}, "ipaddr-type-available": {"description": "IP Address Type Availability.", "type": "integer", "maximum": 255}, "connection-capability": {"description": "This can be used to advertise what type of IP traffic can be sent through the hotspot.", "type": "array", "items": {"type": "string"}}, "icons": {"description": "The operator icons.", "type": "array", "items": {"type": "object", "properties": {"width": {"type": "integer", "description": "The width of the operator icon in pixel", "examples": [64]}, "height": {"type": "integer", "description": "The height of the operator icon in pixel", "examples": [64]}, "type": {"type": "string", "description": "The mimetype of the operator icon", "examples": ["image/png"]}, "icon": {"type": "string", "format": "uc-base64", "description": "The base64 encoded image"}, "language": {"type": "string", "description": "ISO 639-2 language code of the icon", "pattern": "^[a-z][a-z][a-z]$", "examples": ["eng", "fre", "ger", "ita"]}}, "examples": [{"width": 32, "height": 32, "type": "image/png", "language": "eng", "icon": "R0lGODlhEAAQAMQAAORHHOVSKudfOulrSOp3WOyDZu6QdvCchPGolfO0o/XBs/fNwfjZ0frl3/zy7////wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAkAABAALAAAAAAQABAAAAVVICSOZGlCQAosJ6mu7fiyZeKqNKToQGDsM8hBADgUXoGAiqhSvp5QAnQKGIgUhwFUYLCVDFCrKUE1lBavAViFIDlTImbKC5Gm2hB0SlBCBMQiB0UjIQA7"}]}}, "wan-metrics": {"description": "A description of the wan metric offered by this device.", "type": "object", "properties": {"info": {"description": "The state of the devices uplink", "type": "string", "enum": ["up", "down", "testing"]}, "downlink": {"description": "Estimate of WAN backhaul link current downlink speed in kbps.", "type": "integer"}, "uplink": {"description": "Estimate of WAN backhaul link current uplink speed in kbps.", "type": "integer"}}}}}, "interface.ssid.quality-thresholds": {"description": "The thresholds that need to be meet for a clien association to be allowed.", "type": "object", "properties": {"probe-request-rssi": {"description": "Probe requests will be ignored if the rssi is below this threshold.", "type": "integer"}, "association-request-rssi": {"description": "Association requests will be denied if the rssi is below this threshold.", "type": "integer"}, "client-kick-rssi": {"description": "Clients will get kicked if their SNR drops below this value.", "type": "integer"}, "client-kick-ban-time": {"description": "The duration that a client is banned from re-joining after it was kicked.", "type": "integer", "default": 0}}}, "interface.ssid.acl": {"description": "The MAC ACL that defines which clients are allowed or denied to associations.", "type": "object", "properties": {"mode": {"description": "Defines if this is an allow or deny list.", "type": "string", "enum": ["allow", "deny"]}, "mac-address": {"description": "Association requests will be denied if the rssi is below this threshold.", "type": "array", "items": {"type": "string", "format": "uc-mac"}}}}, "service.captive.click": {"description": "This section can be used to setup a captive portal on the AP with a click-to-continue splash page.", "type": "object", "properties": {"auth-mode": {"description": "This field must be set to 'click-to-continue'", "type": "string", "const": "click-to-continue"}}}, "service.captive.radius": {"description": "This section can be used to setup a captive portal on the AP with a click-to-continue splash page.", "type": "object", "properties": {"auth-mode": {"description": "This field must be set to 'radius'", "type": "string", "const": "radius"}, "auth-server": {"description": "The URI of our <PERSON>dius Authentication server.", "type": "string", "format": "uc-host", "examples": ["***********0"]}, "auth-port": {"description": "The network port of our Radius Authentication server.", "type": "integer", "maximum": 65535, "minimum": 1024, "default": 1812}, "auth-secret": {"description": "The shared Radius authentication Authentication secret.", "type": "string", "examples": ["secret"]}, "acct-server": {"description": "The URI of our <PERSON>dius Authentication server.", "type": "string", "format": "uc-host", "examples": ["***********0"]}, "acct-port": {"description": "The network port of our Radius Authentication server.", "type": "integer", "maximum": 65535, "minimum": 1024, "default": 1812}, "acct-secret": {"description": "The shared Radius authentication Authentication secret.", "type": "string", "examples": ["secret"]}, "acct-interval": {"description": "The timeout used for interim messages.", "type": "integer", "default": 600}}}, "service.captive.credentials": {"description": "This section can be used to setup a captive portal on the AP with a credentials splash page.", "type": "object", "properties": {"auth-mode": {"description": "This field must be set to 'credentials'", "type": "string", "const": "credentials"}, "credentials": {"description": "The list of local username/password pairs that can be used to login.", "type": "array", "items": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}}}}}, "service.captive.uam": {"description": "This section can be used to setup a captive portal on the AP with a remote UAM server.", "type": "object", "properties": {"auth-mode": {"description": "This field must be set to 'uam'", "type": "string", "const": "uam"}, "uam-port": {"description": "The local UAM port.", "type": "integer", "maximum": 65535, "minimum": 1024, "default": 3990}, "uam-secret": {"description": "The pre-shared UAM secret.", "type": "string"}, "uam-server": {"description": "The fqdn of the UAM server.", "type": "string"}, "nasid": {"description": "The NASID that gets sent to the UAM server.", "type": "string"}, "nasmac": {"description": "The NAS MAC that gets send to the UAM server. The devices serial is used if this value is not provided.", "type": "string"}, "auth-server": {"description": "The URI of our <PERSON>dius Authentication server.", "type": "string", "format": "uc-host", "examples": ["***********0"]}, "auth-port": {"description": "The network port of our Radius Authentication server.", "type": "integer", "maximum": 65535, "minimum": 1024, "default": 1812}, "auth-secret": {"description": "The shared Radius authentication Authentication secret.", "type": "string", "examples": ["secret"]}, "acct-server": {"description": "The URI of our <PERSON>dius Authentication server.", "type": "string", "format": "uc-host", "examples": ["***********0"]}, "acct-port": {"description": "The network port of our Radius Authentication server.", "type": "integer", "maximum": 65535, "minimum": 1024, "default": 1812}, "acct-secret": {"description": "The shared Radius authentication Authentication secret.", "type": "string", "examples": ["secret"]}, "acct-interval": {"description": "The timeout used for interim messages.", "type": "integer", "default": 600}, "ssid": {"description": "The name of the SSID that shall be sent as part of the UAM redirect.", "type": "string"}, "mac-format": {"description": "Defines the format used to send the MAC address inside AAA frames.", "type": "string", "enum": ["a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aa-bb-cc-dd-ee-ff", "aa:bb:cc:dd:ee:ff", "AABBCCDDEEFF", "AA:BB:CC:DD:EE:FF", "AA-BB-CC-DD-EE-FF"]}, "final-redirect-url": {"description": "Define the behaviour off the final redirect. Default will honour \"userurl\" and fallback to \"local\". Alternatively it is possible to force a redirect to the \"UAM\" or \"local\" URL.", "type": "string", "enum": ["default", "uam"]}, "mac-auth": {"description": "Try to authenticate new clients using macauth.", "type": "boolean", "default": "default"}, "radius-gw-proxy": {"description": "Tunnel all radius traffic via the radius-gw-proxy.", "type": "boolean", "default": false}}}, "service.captive": {"allOf": [{"oneOf": [{"$ref": "#/$defs/service.captive.click"}, {"$ref": "#/$defs/service.captive.radius"}, {"$ref": "#/$defs/service.captive.credentials"}, {"$ref": "#/$defs/service.captive.uam"}]}, {"type": "object", "properties": {"walled-garden-fqdn": {"description": "The list of FQDNs that a non-authenticated client is allowed to connect to.", "type": "array", "items": {"type": "string"}}, "walled-garden-ipaddr": {"description": "The list of IP addresses that a non-authenticated client is allowed to connect to.", "type": "array", "items": {"type": "string", "format": "uc-ip"}}, "web-root": {"description": "A base64 encoded TAR file with the custom web-root.", "type": "string", "format": "uc-base64"}, "web-root-url": {"description": "A URL where the webroot should be downloaded from.", "type": "string"}, "web-root-checksum": {"description": "The SHA256 of the file located at web-root-url.", "type": "string"}, "idle-timeout": {"description": "How long may a client be idle before getting removed.", "type": "integer", "default": 600}, "session-timeout": {"description": "How long may a client be idle before getting removed.", "type": "integer"}}}]}, "interface.ssid": {"description": "A device has certain properties that describe its identity and location. These properties are described inside this object.", "type": "object", "properties": {"purpose": {"description": "An SSID can have a special purpose such as the hidden on-boarding BSS. All purposes other than \"user-defined\" are static pre-defined configurations.", "type": "string", "enum": ["user-defined", "onboarding-ap", "onboarding-sta"], "default": "user-defined"}, "name": {"description": "The broadcasted SSID of the wireless network and for for managed mode the SSID of the network you’re connecting to", "type": "string", "maxLength": 32, "minLength": 1}, "wifi-bands": {"description": "The band that the SSID should be broadcasted on. The configuration layer will use the first matching band.", "type": "array", "items": {"type": "string", "enum": ["2G", "5G", "5G-lower", "5G-upper", "6G", "HaLow"]}}, "bss-mode": {"description": "Selects the operation mode of the wireless network interface controller.", "type": "string", "enum": ["ap", "sta", "mesh", "wds-ap", "wds-sta", "wds-repeater"], "default": "ap"}, "bssid": {"description": "Override the BSSID of the network, only applicable in adhoc or sta mode.", "type": "string", "format": "uc-mac"}, "hidden-ssid": {"description": "Disables the broadcasting of beacon frames if set to 1 and,in doing so, hides the ESSID.", "type": "boolean"}, "isolate-clients": {"description": "Isolates wireless clients from each other on this BSS.", "type": "boolean"}, "strict-forwarding": {"description": "Isolate the BSS from all other members on the bridge apart from the first wired port.", "type": "boolean", "default": false}, "power-save": {"description": "Unscheduled Automatic Power Save Delivery.", "type": "boolean"}, "rts-threshold": {"description": "Set the RTS/CTS threshold of the BSS.", "type": "integer", "maximum": 65535, "minimum": 1}, "max-inactivity": {"description": "Set the Maximum Inactivity in seconds", "type": "integer", "default": 300}, "broadcast-time": {"description": "This option will make the unit braodcast the time inside its beacons.", "type": "boolean"}, "unicast-conversion": {"description": "Convert multicast traffic to unicast on this BSS.", "type": "boolean", "default": true}, "services": {"description": "The services that shall be offered on this logical interface. These are just strings such as \"wifi-steering\"", "type": "array", "items": {"type": "string", "examples": ["wifi-steering"]}}, "dtim-period": {"description": "Set the DTIM (delivery traffic information message) period. There will be one DTIM per this many beacon frames. This may be set between 1 and 255. This option only has an effect on ap wifi-ifaces.", "type": "integer", "default": 2, "maximum": 255, "minimum": 1}, "maximum-clients": {"description": "Set the maximum number of clients that may connect to this VAP.", "type": "integer", "example": 64}, "proxy-arp": {"description": "Proxy ARP is the technique in which the host router, answers ARP requests intended for another machine.", "type": "boolean", "default": true}, "disassoc-low-ack": {"decription": "Disassociate stations based on excessive transmission failures or other indications of connection loss.", "type": "boolean", "default": false}, "vendor-elements": {"decription": "This option allows embedding custom vendor specific IEs inside the beacons of a BSS in AP mode.", "type": "string"}, "tip-information-element": {"decription": "The device will broadcast the TIP vendor IE inside its beacons if this option is enabled.", "type": "boolean", "default": true}, "fils-discovery-interval": {"description": "The maximum interval for FILS discovery announcement frames. This is a condensed beacon used in 6GHz channels for passive BSS discovery.", "type": "integer", "default": 20, "maximum": 20}, "encryption": {"$ref": "#/$defs/interface.ssid.encryption"}, "enhanced-mpsk": {"description": "Optinally disable MPSK", "type": "boolean", "default": true}, "multi-psk": {"anyOf": [{"type": "array", "items": {"$ref": "#/$defs/interface.ssid.multi-psk"}}, {"type": "boolean"}]}, "rrm": {"$ref": "#/$defs/interface.ssid.rrm"}, "rate-limit": {"$ref": "#/$defs/interface.ssid.rate-limit"}, "roaming": {"anyOf": [{"$ref": "#/$defs/interface.ssid.roaming"}, {"description": "Enable 802.11r Fast Roaming for this BSS. This will enable \"auto\" mode which will work for most scenarios.", "type": "boolean"}]}, "radius": {"$ref": "#/$defs/interface.ssid.radius"}, "certificates": {"$ref": "#/$defs/interface.ssid.certificates"}, "pass-point": {"$ref": "#/$defs/interface.ssid.pass-point"}, "quality-thresholds": {"$ref": "#/$defs/interface.ssid.quality-thresholds"}, "access-control-list": {"$ref": "#/$defs/interface.ssid.acl"}, "captive": {"$ref": "#/$defs/service.captive"}, "vlan-awareness": {"description": "Setup additional VLANs inside the bridge", "type": "object", "properties": {"first": {"type": "integer"}, "last": {"type": "integer"}}}, "hostapd-bss-raw": {"description": "This array allows passing raw hostapd.conf lines.", "type": "array", "items": {"type": "string", "examples": ["ap_table_expiration_time=3600", "device_type=6-0050F204-1", "ieee80211h=1", "rssi_ignore_probe_request=-75", "time_zone=EST5", "uuid=12345678-9abc-def0-1234-56789abcdef0", "venue_url=1:http://www.example.com/info-eng", "wpa_deny_ptk0_rekey=0"]}}}}, "interface.tunnel.mesh": {"description": "This Object defines the properties of a mesh interface overlay.", "type": "object", "properties": {"proto": {"description": "This field must be set to mesh.", "type": "string", "const": "mesh"}}}, "interface.tunnel.vxlan": {"description": "This Object defines the properties of a vxlan tunnel.", "type": "object", "properties": {"proto": {"description": "This field must be set to vxlan.", "type": "string", "const": "vxlan"}, "peer-address": {"description": "This is the IP address of the remote host, that the VXLAN tunnel shall be established with.", "type": "string", "format": "ipv4", "example": "*************"}, "peer-port": {"description": "The network port that shall be used to establish the VXLAN tunnel.", "type": "integer", "maximum": 65535, "minimum": 1, "examples": [4789]}}}, "interface.tunnel.l2tp": {"description": "This Object defines the properties of a l2tp tunnel.", "type": "object", "properties": {"proto": {"description": "This field must be set to vxlan.", "type": "string", "const": "l2tp"}, "server": {"description": "This is the IP address of the remote host, that the L2TP tunnel shall be established with.", "type": "string", "format": "ipv4", "example": "*************"}, "user-name": {"description": "The username used to authenticate.", "type": "string"}, "password": {"description": "The password used to authenticate.", "type": "string"}}}, "interface.tunnel.gre": {"description": "This Object defines the properties of a GRE tunnel.", "type": "object", "properties": {"mtu": {"description": "The maximum transmission unit (MTU) size for the GRE tunnel interface. The default value is 1280 bytes to reflect OpenWRT GRE Package Defaults.", "type": "integer", "minimum": 68, "maximum": 1500, "default": 1280}, "proto": {"description": "This field must be set to gre.", "type": "string", "const": "gre"}, "peer-address": {"description": "This is the IP address of the remote host, that the GRE tunnel shall be established with.", "type": "string", "format": "ipv4", "example": "*************"}, "dhcp-healthcheck": {"description": "Healthcheck will probe if the remote peer replies to DHCP discovery without sending an ACK.", "type": "boolean", "default": false}, "dont-fragment": {"description": "Set “Don't Fragment” flag on encapsulated packets.", "type": "boolean", "default": false}}}, "interface.tunnel.gre6": {"description": "This Object defines the properties of a GREv6 tunnel.", "type": "object", "properties": {"mtu": {"description": "The maximum transmission unit (MTU) size for the GRE tunnel interface. The default value is 1280 bytes to reflect OpenWRT GRE Package Defaults.", "type": "integer", "minimum": 1280, "maximum": 1500, "default": 1280}, "proto": {"description": "This field must be set to gre6.", "type": "string", "const": "gre6"}, "peer-address": {"description": "This is the IPv6 address of the remote host, that the GRE tunnel shall be established with.", "type": "string", "format": "ipv6", "example": "2405:200:802:600:61::1"}, "dhcp-healthcheck": {"description": "Healthcheck will probe if the remote peer replies to DHCP discovery without sending an ACK.", "type": "boolean", "default": false}}}, "interface.tunnel": {"oneOf": [{"$ref": "#/$defs/interface.tunnel.mesh"}, {"$ref": "#/$defs/interface.tunnel.vxlan"}, {"$ref": "#/$defs/interface.tunnel.l2tp"}, {"$ref": "#/$defs/interface.tunnel.gre"}, {"$ref": "#/$defs/interface.tunnel.gre6"}]}, "interface": {"description": "This section describes the logical network interfaces of the device. Interfaces as their primary have a role that is upstream, downstream, guest, ....", "type": "object", "properties": {"name": {"description": "This is a free text field, stating the administrative name of the interface. It may contain spaces and special characters.", "type": "string", "examples": ["LAN"]}, "role": {"description": "The role defines if the interface is upstream or downstream facing.", "type": "string", "enum": ["upstream", "downstream"]}, "isolate-hosts": {"description": "This option makes sure that any traffic leaving this interface is isolated and all local IP ranges are blocked. It essentially enforces \"guest network\" firewall settings.", "type": "boolean"}, "metric": {"description": "The routing metric of this logical interface. Lower values have higher priority.", "type": "integer", "maximum": **********, "minimum": 0}, "mtu": {"description": "The MTU of this logical interface.", "type": "integer", "maximum": 1600, "minimum": 1280}, "services": {"description": "The services that shall be offered on this logical interface. These are just strings such as \"ssh\", \"lldp\", \"mdns\"", "type": "array", "items": {"type": "string", "examples": ["ssh", "lldp"]}}, "vlan-awareness": {"description": "Setup additional VLANs inside the bridge", "type": "object", "properties": {"first": {"type": "integer"}, "last": {"type": "integer"}}}, "ieee8021x-ports": {"description": "The list of physical network devices that shall serve .1x for this interface.", "type": "array", "items": {"type": "string", "examples": ["LAN1", "LAN2", "LAN3", "LAN4", "LAN*", "WAN*", "*"]}}, "vlan": {"$ref": "#/$defs/interface.vlan"}, "bridge": {"$ref": "#/$defs/interface.bridge"}, "ethernet": {"type": "array", "items": {"$ref": "#/$defs/interface.ethernet"}}, "ipv4": {"$ref": "#/$defs/interface.ipv4"}, "ipv6": {"$ref": "#/$defs/interface.ipv6"}, "broad-band": {"$ref": "#/$defs/interface.broad-band"}, "ssids": {"type": "array", "items": {"$ref": "#/$defs/interface.ssid"}}, "tunnel": {"$ref": "#/$defs/interface.tunnel"}}}, "service.lldp": {"type": "object", "properties": {"describe": {"description": "The LLDP description field. If set to \"auto\" it will be derived from unit.name.", "type": "string", "default": "uCentral Access Point"}, "location": {"description": "The LLDP location field. If set to \"auto\" it will be derived from unit.location.", "type": "string", "default": "uCentral Network"}}}, "service.ssh": {"description": "This section can be used to setup a SSH server on the AP.", "type": "object", "properties": {"port": {"description": "This option defines which port the SSH server shall be available on.", "type": "integer", "maximum": 65535, "default": 22}, "authorized-keys": {"description": "This allows the upload of public ssh keys. Keys need to be seperated by a newline.", "type": "array", "items": {"type": "string", "examples": ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAAAgQC0ghdSd2D2y08TFowZLMZn3x1/Djw3BkNsIeHt/Z+RaXwvfV1NQAnNdaOngMT/3uf5jZtYxhpl+dbZtRhoUPRvKflKBeFHYBqjZVzD3r4ns2Ofm2UpHlbdOpMuy9oeTSCeF0IKZZ6szpkvSirQogeP2fe9KRkzQpiza6YxxaJlWw== user@example", "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJ4FDjyCsg+1Mh2C5G7ibR3z0Kw1dU57kfXebLRwS6CL bob@work", "ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBP/JpJ/KHtKKImzISBDwLO0/EwytIr4pGZQXcP6GCSHchLMyfjf147KNlF9gC+3FibzqKH02EiQspVhRgfuK6y0= alice@home"]}}, "password-authentication": {"description": "This option defines if password authentication shall be enabled. If set to false, only ssh key based authentication is possible.", "type": "boolean", "default": true}, "idle-timeout": {"description": "This option defines the idle timeout of an ssh connection, set to 0 to disable this feature. Default to 60 seconds if this field is not specified.", "type": "integer", "default": 60, "maximum": 600}}}, "service.ntp": {"type": "object", "description": "This section can be used to setup the upstream NTP servers.", "properties": {"servers": {"description": "This is an array of URL/IP of the upstream NTP servers that the unit shall use to acquire its current time.", "type": "array", "items": {"type": "string", "format": "uc-host"}, "examples": ["0.openwrt.pool.ntp.org"]}}}, "service.mdns": {"description": "This section can be used to configure the MDNS server.", "type": "object", "properties": {"enable": {"description": "Enable this option if you would like to enable the MDNS server on the unit.", "type": "boolean", "default": false}}}, "service.rtty": {"description": "This section can be used to setup a persistent connection to a rTTY server.", "type": "object", "properties": {"host": {"description": "The server that the device shall connect to.", "type": "string", "format": "uc-host", "examples": ["***********0"]}, "port": {"description": "This option defines the port that device shall connect to.", "type": "integer", "maximum": 65535, "default": 5912}, "token": {"description": "The security token that shall be used to authenticate with the server.", "type": "string", "maxLength": 32, "minLength": 32, "examples": ["01234567890123456789012345678901"]}, "mutual-tls": {"description": "Shall the connection enforce mTLS", "type": "boolean", "default": true}}}, "service.log": {"description": "This section can be used to configure remote syslog support.", "type": "object", "properties": {"host": {"description": "IP address of a syslog server to which the log messages should be sent in addition to the local destination.", "type": "string", "format": "uc-host", "examples": ["***********0"]}, "port": {"description": "Port number of the remote syslog server specified with log_ip.", "type": "integer", "maximum": 65535, "minimum": 100, "examples": [2000]}, "proto": {"description": "Sets the protocol to use for the connection, either tcp or udp.", "type": "string", "enum": ["tcp", "udp"], "default": "udp"}, "size": {"description": "Size of the file based log buffer in KiB. This value is used as the fallback value for log_buffer_size if the latter is not specified.", "type": "integer", "minimum": 32, "default": 1000}, "priority": {"description": "Filter messages by their log priority. the value maps directly to the 0-7 range used by syslog.", "type": "integer", "minimum": 0, "default": 7}}}, "service.http": {"description": "Enable the webserver with the on-boarding webui", "type": "object", "properties": {"http-port": {"description": "The port that the HTTP server should run on.", "type": "integer", "maximum": 65535, "minimum": 1, "default": 80}}}, "service.igmp": {"description": "This section allows enabling the IGMP/Multicast proxy", "type": "object", "properties": {"enable": {"description": "This option defines if the IGMP/Multicast proxy shall be enabled on the device.", "type": "boolean", "default": false}}}, "service.ieee8021x": {"description": "This section allows enabling wired ieee802.1X", "type": "object", "properties": {"mode": {"description": "This field must be set to 'radius or user'", "type": "string", "enum": ["radius", "user"]}, "select-ports": {"description": "Specifies a list of ports that we want to filter.", "type": "array", "items": {"type": "string", "examples": [{"LAN1": null}]}}, "users": {"description": "Specifies a collection of local EAP user/psk/vid triplets.", "type": "array", "items": {"$ref": "#/$defs/interface.ssid.radius.local-user"}}, "radius": {"description": "Specifies the information about radius account authentication and accounting", "type": "object", "properties": {"nas-identifier": {"description": "NAS-Identifier string for RADIUS messages. When used, this should be unique to the NAS within the scope of the RADIUS server.", "type": "string"}, "auth-server-addr": {"description": "The URI of our Radius server.", "type": "string", "format": "uc-host", "examples": ["***********0"]}, "auth-server-port": {"description": "The network port of our Radius server.", "type": "integer", "maximum": 65535, "minimum": 1024, "examples": [1812]}, "auth-server-secret": {"description": "The shared Radius authentication secret.", "type": "string", "examples": ["secret"]}, "acct-server-addr": {"description": "The URI of our Radius server.", "type": "string", "format": "uc-host", "examples": ["***********0"]}, "acct-server-port": {"description": "The network port of our Radius server.", "type": "integer", "maximum": 65535, "minimum": 1024, "examples": [1813]}, "acct-server-secret": {"description": "The shared Radius accounting secret.", "type": "string", "examples": ["secret"]}, "coa-server-addr": {"description": "The URI of our Radius server.", "type": "string", "format": "uc-host", "examples": ["***********0"]}, "coa-server-port": {"description": "The network port of our Radius server.", "type": "integer", "maximum": 65535, "minimum": 1024, "examples": [1814]}, "coa-server-secret": {"description": "The shared Radius accounting secret.", "type": "string", "examples": ["secret"]}, "mac-address-bypass": {"description": "Trigger mac-auth when a new ARP is learned.", "type": "boolean"}}}}}, "service.radius-proxy": {"description": "This section can be used to setup a radius security proxy instance (radsecproxy).", "type": "object", "properties": {"proxy-secret": {"description": "The radius secret used to communicate with the proxy.", "type": "string", "default": "secret"}, "realms": {"description": "The various realms that we can proxy to.", "type": "array", "items": {"anyOf": [{"type": "object", "properties": {"protocol": {"description": "Defines whether the real should use radsec or normal radius.", "type": "string", "enum": ["radsec"], "default": "radsec"}, "realm": {"description": "The realm that that this server shall be used for.", "type": "array", "items": {"type": "string", "default": "*"}}, "auto-discover": {"description": "Auto discover radsec server address via realm DNS NAPTR record.", "type": "boolean", "default": false}, "host": {"description": "The remote proxy server that the device shall connect to.", "type": "string", "format": "uc-host", "examples": ["***********0"]}, "port": {"description": "The remote proxy port that the device shall connect to.", "type": "integer", "maximum": 65535, "default": 2083}, "secret": {"description": "The radius secret that will be used for the connection.", "type": "string"}, "use-local-certificates": {"description": "The device will use its local certificate bundle for the TLS setup and ignores all other certificate options in this section.", "type": "boolean", "default": false}, "ca-certificate": {"description": "The local servers CA bundle.", "type": "string"}, "certificate": {"description": "The local servers certificate.", "type": "string"}, "private-key": {"description": "The local servers private key/", "type": "string"}, "private-key-password": {"description": "The password required to read the private key.", "type": "string"}}}, {"type": "object", "properties": {"protocol": {"description": "Defines whether the real should use radsec or normal radius.", "type": "string", "enum": ["radius"]}, "realm": {"description": "The realm that that this server shall be used for.", "type": "array", "items": {"type": "string", "default": "*"}}, "auth-server": {"description": "The URI of our Radius server.", "type": "string", "format": "uc-host", "examples": ["***********0"]}, "auth-port": {"description": "The network port of our Radius server.", "type": "integer", "maximum": 65535, "minimum": 1024, "examples": [1812]}, "auth-secret": {"description": "The shared Radius authentication secret.", "type": "string", "examples": ["secret"]}, "acct-server": {"description": "The URI of our Radius server.", "type": "string", "format": "uc-host", "examples": ["***********0"]}, "acct-port": {"description": "The network port of our Radius server.", "type": "integer", "maximum": 65535, "minimum": 1024, "examples": [1812]}, "acct-secret": {"description": "The shared Radius authentication secret.", "type": "string", "examples": ["secret"]}}}, {"type": "object", "properties": {"protocol": {"description": "Defines whether the real should use radsec or normal radius.", "type": "string", "enum": ["block"]}, "realm": {"description": "The realm that that this server shall be used for.", "type": "array", "items": {"type": "string", "default": "*"}}, "message": {"description": "The message that is sent when a realm is blocked.", "type": "string", "items": {"type": "string", "default": "blocked"}}}}]}}}}, "service.online-check": {"description": "This section can be used to configure the online check service.", "type": "object", "properties": {"ping-hosts": {"description": "Hosts that shall be pinged to find out if we are online.", "type": "array", "items": {"type": "string", "format": "uc-host", "examples": ["***********0"]}}, "download-hosts": {"description": "URLs to which a http/s connection shall be established to find out if we are online. The service will try to download http://$string/online.txt and expects the content of that file to be \"Ok\". HTTP 30x is support allowing https redirects.", "type": "array", "items": {"type": "string", "examples": ["www.example.org"]}}, "check-interval": {"description": "The interval in seconds in between each online-check.", "type": "number", "default": 60}, "check-threshold": {"description": "How often does the online check need to fail until the system assumes that it has lost online connectivity.", "type": "number", "default": 1}, "action": {"description": "The action that the device shall execute when it has detected that it is not online.", "type": "array", "items": {"type": "string", "enum": ["wifi", "leds"]}}}}, "service.data-plane": {"description": "This section can be used to define eBPF and cBPF blobs that shall be loaded for virtual data-planes and SDN.", "type": "object", "properties": {"ingress-filters": {"description": "A list of programs that can be loaded as ingress filters on interfaces.", "type": "array", "items": {"type": "object", "properties": {"name": {"description": "The name of the ingress filter.", "type": "string"}, "program": {"description": "The base64 encoded xBPF.", "type": "string", "format": "uc-base64"}}}}}}, "service.wifi-steering": {"description": "This section describes the band steering behaviour of the unit.", "type": "object", "properties": {"mode": {"description": "Wifi sterring can happen either locally or via the backend gateway.", "type": "string", "enum": ["local", "none"], "examples": ["local"]}, "assoc-steering": {"description": "Allow rejecting assoc requests for steering purposes.", "type": "boolean", "default": false}, "required-snr": {"description": "Minimum required signal level (dBm) for connected clients. If the client will be kicked if the SNR drops below this value.", "type": "integer", "default": 0}, "required-probe-snr": {"description": "Minimum required signal level (dBm) to allow connections. If the SNR is below this value, probe requests will not be replied to.", "type": "integer", "default": 0}, "required-roam-snr": {"description": "Minimum required signal level (dBm) before an attempt is made to roam the client to a better AP.", "type": "integer", "default": 0}, "load-kick-threshold": {"description": "Minimum channel load (%) before kicking clients", "type": "integer", "default": 0}, "auto-channel": {"description": "Allow multiple instances of the steering daemon to coordinate the best channel usage amongst eachother.", "type": "boolean", "default": false}, "ipv6": {"description": "Use IPv6 multicast to communicate with remote usteerd instances, rather than IPv4 broadcast.", "type": "boolean", "default": false}}}, "service.quality-of-service.class-selector": {"type": "string", "enum": ["CS0", "CS1", "CS2", "CS3", "CS4", "CS5", "CS6", "CS7", "AF11", "AF12", "AF13", "AF21", "AF22", "AF23", "AF31", "AF32", "AF33", "AF41", "AF42", "AF43", "DF", "EF", "VA", "LE"]}, "service.quality-of-service": {"description": "This section describes the QoS behaviour of the unit.", "type": "object", "properties": {"select-ports": {"description": "The physical network devices that shall be considered the primary uplink interface. All classification and shaping will happen on this device.", "type": "array", "items": {"type": "string", "default": "WAN"}}, "bandwidth-up": {"description": "Defines the upload bandwidth of this device. If it is not known or the device is attached to a shared medium, this value needs to be 0.", "type": "integer", "default": 0}, "bandwidth-down": {"description": "Defines the download bandwidth of this device. If it is not known or the device is attached to a shared medium, this value needs to be 0.", "type": "integer", "default": 0}, "bulk-detection": {"description": "The QoS feature can automatically detect and classify bulk flows. This is based on average packet size and PPS.", "type": "object", "properties": {"dscp": {"description": "The differentiated services code point that shall be assigned to packets that belong to a bulk flow.", "$ref": "#/$defs/service.quality-of-service.class-selector", "default": "CS0"}, "packets-per-second": {"description": "The required PPS rate that will cause a flow to be classified as bulk.", "type": "number", "default": 0}}}, "services": {"description": "A list of predefined named services that shall be classified according to the communities DB.", "type": "array", "items": {"type": "string"}}, "classifier": {"description": "A list of classifiers. Each classifier will map certain traffic to specific ToS/DSCP values based upon the defined constraints.", "type": "array", "items": {"type": "object", "properties": {"dscp": {"description": "The differentiated services code point that shall be assigned to packet that match the rules of this entry.", "$ref": "#/$defs/service.quality-of-service.class-selector", "default": "CS1"}, "ports": {"description": "Each entry defines a layer3 protocol and a port(range) that will be used to match packets.", "type": "array", "items": {"type": "object", "properties": {"protocol": {"description": "The port match can apply for TCP, UDP or any IP protocol.", "type": "string", "enum": ["any", "tcp", "udp"], "default": "any"}, "port": {"description": "The port of this match rule.", "type": "integer"}, "range-end": {"description": "The last port of this match rule if it is a port range.", "type": "integer"}, "reclassify": {"description": "Ignore the ToS/DSCP of packets and reclassify them.", "type": "boolean", "default": true}}}}, "dns": {"description": "Each entry defines a wildcard FQDN. The IP that this resolves to will be used to match packets.", "type": "array", "items": {"type": "object", "properties": {"fqdn": {"type": "string", "format": "uc-fqdn"}, "suffix-matching": {"description": "Match for all suffixes of the FQDN.", "type": "boolean", "default": true}, "reclassify": {"description": "Ignore the ToS/DSCP of packets and reclassify them.", "type": "boolean", "default": true}}}}}}}}}, "service.facebook-wifi": {"description": "This section describes the FaceBook Wifi behaviour of the unit.", "type": "object", "properties": {"vendor-id": {"description": "The Vendors ID.", "type": "string"}, "gateway-id": {"description": "The Gateways ID.", "type": "string"}, "secret": {"description": "The Device specific secret", "type": "string"}}}, "service.airtime-fairness": {"description": "This section describes the vlan behaviour of a logical network interface.", "type": "object", "properties": {"voice-weight": {"description": "Voice traffic does not get aggregated. As voice and video are both considered priotity voice is considered to have a heavier weight when calculation priority average.", "type": "number", "default": 4}, "packet-threshold": {"description": "The amount of packets that need to be received for a specific type of traffic before new averageg is calculated.", "type": "number", "default": 100}, "bulk-threshold": {"description": "This option is a percentual value. If more the X% of the traffic is bulk, we assign the bulk weight.", "type": "number", "default": 50}, "priority-threshold": {"description": "This option is a percentual value. If more the X% of the traffic is priority, we assign the priority weight. Priority classification will take precedence over bulk.", "type": "number", "default": 30}, "weight-normal": {"description": "The default ATF weight that UEs get assigned.", "type": "number", "default": 256}, "weight-priority": {"description": "The default ATF weight that UEs get assigned when priority traffic above the configured percentage is detected.", "type": "number", "default": 394}, "weight-bulk": {"description": "The default ATF weight that UEs get assigned when bulk traffic above the configured percentage is detected.", "type": "number", "default": 128}}}, "service.wireguard-overlay": {"description": "This Object defines the properties of a wireguard-overlay.", "type": "object", "properties": {"proto": {"description": "This field must be set to wireguard-overlay.", "type": "string", "const": "wireguard-overlay"}, "private-key": {"description": "The private key of the device. This key is used to lookup the host entry inside the config.", "type": "string"}, "peer-port": {"description": "The network port that shall be used to establish the wireguard tunnel.", "type": "integer", "maximum": 65535, "minimum": 1, "default": 3456}, "peer-exchange-port": {"description": "The network port that shall be used to exchange peer data inside the tunnel.", "type": "integer", "maximum": 65535, "minimum": 1, "default": 3458}, "root-node": {"description": "The descritption of the root node of the overlay.", "type": "object", "properties": {"key": {"description": "The public key of the host.", "type": "string"}, "endpoint": {"description": "The public IP of the host (optional).", "type": "string", "format": "uc-ip"}, "ipaddr": {"description": "The list of private IPs that a host is reachable on inside the overlay.", "type": "array", "items": {"type": "string", "format": "uc-ip"}}}}, "hosts": {"description": "The list of all known hosts inside the overlay.", "type": "array", "items": {"type": "object", "properties": {"name": {"description": "The unique name of the host.", "type": "string"}, "key": {"description": "The public key of the host.", "type": "string"}, "endpoint": {"description": "The public IP of the host (optional).", "type": "string", "format": "uc-ip"}, "subnet": {"description": "The list of subnets that shall be routed to this host.", "type": "array", "items": {"type": "string", "format": "uc-cidr"}}, "ipaddr": {"description": "The list of private IPs that a host is reachable on inside the overlay.", "type": "array", "items": {"type": "string", "format": "uc-ip"}}}}}, "vxlan": {"description": "The descritption of the root node of the overlay.", "type": "object", "properties": {"port": {"description": "The network port that shall be used to establish the vxlan overlay.", "type": "integer", "maximum": 65535, "minimum": 1, "default": 4789}, "mtu": {"description": "The MTU that shall be used by the vxlan tunnel.", "type": "integer", "maximum": 65535, "minimum": 256, "default": 1420}, "isolate": {"description": "If set to true hosts will only be able to talk with the root node and not forward L@ traffic between each other.", "type": "boolean", "default": true}}}}}, "service.gps": {"description": "This section can be used to configure a GPS dongle", "type": "object", "properties": {"adjust-time": {"description": "Adjust the systems clock upon a successful GPS lock.", "type": "boolean", "default": false}, "baud-rate": {"description": "The baudrate used by the attached GPS dongle", "type": "integer", "enum": [2400, 4800, 9600, 19200]}}}, "service.dhcp-relay": {"description": "Define the vlans on which the dhcp shall be relayed.", "type": "object", "properties": {"select-ports": {"description": "The list of physical network devices that shall be used to fwd the DHCP frames.", "type": "array", "items": {"type": "string"}}, "vlans": {"description": "The list of all vlans", "type": "array", "items": {"type": "object", "properties": {"vlan": {"description": "The vlan id.", "type": "number"}, "relay-server": {"description": "The unicast target DHCP pool server where frames get relayed to.", "type": "string", "format": "uc-ip"}, "circuit-id-format": {"description": "This option selects what info shall be contained within a relayed frame's circuit ID.", "type": "string", "enum": ["vlan-id", "ap-mac", "ssid"], "default": "vlan-id"}, "remote-id-format": {"description": "This option selects what info shall be contained within a relayed frame's remote ID.", "type": "string", "enum": ["vlan-id", "ap-mac", "ssid"], "default": "ap-mac"}}}}}}, "service.admin-ui": {"type": "object", "properties": {"wifi-ssid": {"description": "The name of the admin ssid.", "type": "string", "default": "Ma<PERSON><PERSON>", "maxLength": 32, "minLength": 1}, "wifi-key": {"description": "The Pre Shared Key (PSK) that is used for encryption on the BSS.", "type": "string", "maxLength": 63, "minLength": 8}, "wifi-bands": {"description": "The band that the SSID should be broadcasted on. The configuration layer will use the first matching band.", "type": "array", "items": {"type": "string", "enum": ["2G", "5G", "5G-lower", "5G-upper", "6G", "HaLow"]}}, "offline-trigger": {"description": "The admin-ui will be spawned when this offline threshold was exceeded.", "type": "number"}}}, "service.rrm.chanutil": {"description": "RRM policy based on Channel Utilization for optimization.", "type": "object", "properties": {"interval": {"description": "The interval to check channel utilization (in seconds).", "type": "number", "minimum": 240}, "threshold": {"description": "The airtime utilization threshold.", "type": "number", "minimum": 0, "maximum": 99, "examples": [50]}, "consecutive-threshold-breach": {"description": "The number of times the Channel Utilization is higher than the threshold before triggering channel optimization.", "type": "integer", "minimum": 1}, "algo": {"description": "The algorithm for channel optimization.", "type": "string", "examples": ["rcs", "acs"]}}}, "service.rrm": {"description": "This section describes the band steering behaviour of the unit.", "type": "object", "properties": {"beacon-request-assoc": {"description": "Tell stations to send a beacon request scan when they associate.", "type": "boolean", "default": true}, "station-stats-interval": {"description": "Periodically send station statistics every N seconds.", "type": "number"}, "chanutil": {"$ref": "#/$defs/service.rrm.chanutil"}}}, "service.fingerprint": {"description": "This section can be used to configure device fingerprinting.", "type": "object", "properties": {"mode": {"description": "Enable this option if you would like to enable the MDNS server on the unit.", "type": "string", "enum": ["polled", "final", "raw-data"], "default": "final"}, "minimum-age": {"description": "The minimum age a fingerprint must have before it is reported.", "type": "number", "default": 60}, "maximum-age": {"description": "The age at which fingerprints get flushed from the local state.", "type": "number", "default": 60}, "periodicity": {"description": "This value defines the period at which entries get reported.", "type": "number", "default": 600}, "allow-wan": {"description": "Allow fingerprinting devices found on the WAN port.", "type": "boolean", "default": false}}}, "service.snmpd.agent": {"description": "Configure the SNMP agent.", "type": "object", "properties": {"agentaddress": {"description": "Define the agent configuration.", "type": "string", "default": "UDP:161"}}}, "service.snmpd.access": {"description": "List of access types for SNMP.", "type": "object", "properties": {"public_access": {"type": "object", "description": "Configuration of public access.", "properties": {"context": {"description": "A collection of management information accessible by an SNMP entity.", "type": "string"}, "group": {"description": "Group related to the access.", "type": "string"}, "level": {"description": "Level of authorization.", "type": "string"}, "notify": {"description": "Specifies the view to be used for GET*, SET and TRAP/INFORM requests.", "type": "string"}, "prefix": {"description": "Specifies how CONTEXT should be matched against the context of the incoming request.", "type": "string"}, "read": {"description": "Specifies the view to be used for GET*, SET and TRAP/INFORM requests.", "type": "string"}, "version": {"description": "SNMP version.", "type": "string"}, "write": {"description": "Specifies the view to be used for GET*, SET and TRAP/INFORM requests.", "type": "string"}}}, "private_access": {"type": "object", "description": "Configuration of public access.", "properties": {"context": {"description": "A collection of management information accessible by an SNMP entity.", "type": "string"}, "group": {"description": "Group related to the access.", "type": "string"}, "level": {"description": "Level of authorization.", "type": "string"}, "notify": {"description": "Specifies the view to be used for GET*, SET and TRAP/INFORM requests.", "type": "string"}, "prefix": {"description": "Specifies how CONTEXT should be matched against the context of the incoming request.", "type": "string"}, "read": {"description": "Specifies the view to be used for GET*, SET and TRAP/INFORM requests.", "type": "string"}, "version": {"description": "SNMP version.", "type": "string"}, "write": {"description": "Specifies the view to be used for GET*, SET and TRAP/INFORM requests.", "type": "string"}}}}}, "service.snmpd.agentx": {"description": "Configure the role in AgentX protocol.", "type": "object", "properties": {"type": {"description": "AgentX protocol role.", "type": "string", "default": "master"}}}, "service.snmpd.com2sec": {"description": "Map an SNMPv1 or SNMPv2c community string to a security name..", "type": "object", "properties": {"public": {"description": "Public com2sec.", "type": "object", "properties": {"community": {"description": "Community name.", "type": "string"}, "secname": {"description": "Security name.", "type": "string"}, "source": {"description": "A restricted source can either be a specific hostname or a subnet.", "type": "string"}}}, "private": {"description": "Private com2sec.", "type": "object", "properties": {"community": {"description": "Community name.", "type": "string"}, "secname": {"description": "Security name.", "type": "string"}, "source": {"description": "A restricted source can either be a specific hostname or a subnet.", "type": "string"}}}}}, "service.snmpd.general": {"description": "General options for SNMP service.", "type": "object", "properties": {"enabled": {"description": "Enable or disable the service", "type": "boolean", "default": false}}}, "service.snmpd.pass": {"description": "List of community permissions.", "type": "array", "items": {"type": "object", "properties": {"miboid": {"description": "OID used by pass protocol.", "type": "string"}, "name": {"description": "Name of the MIB.", "type": "string"}, "prog": {"description": "MIB script.", "type": "string"}}}}, "service.snmpd.group": {"description": "List of pass sections for SNMP.", "type": "object", "properties": {"public_v1": {"type": "object", "properties": {"group": {"type": "string", "description": "Group name."}, "secname": {"description": "Related security name.", "type": "string"}, "version": {"description": "SNMP version.", "type": "string"}}}, "private_v1": {"type": "object", "properties": {"group": {"type": "string", "description": "Group name."}, "secname": {"description": "Related security name.", "type": "string"}, "version": {"description": "SNMP version.", "type": "string"}}}, "private_v2c": {"type": "object", "properties": {"group": {"type": "string", "description": "Group name."}, "secname": {"description": "Related security name.", "type": "string"}, "version": {"description": "SNMP version.", "type": "string"}}}, "public_v2c": {"type": "object", "properties": {"group": {"type": "string", "description": "Group name."}, "secname": {"description": "Related security name.", "type": "string"}, "version": {"description": "SNMP version.", "type": "string"}}}}}, "service.snmpd.system": {"description": "System information used by SNMP service.", "type": "object", "properties": {"sysContact": {"description": "Contact information.", "type": "string"}, "sysLocation": {"description": "Location information.", "type": "string"}, "sysName": {"description": "System name.", "type": "string"}}}, "service.snmpd.view": {"description": "View configuration.", "type": "object", "properties": {"oid": {"description": "Define the source oid tree for the view.", "type": "string"}, "type": {"description": "Type is either included or excluded.", "type": "string"}, "viewname": {"description": "View name.", "type": "string"}}}, "service.snmpd": {"description": "SNMP sections.", "type": "object", "properties": {"agent": {"$ref": "#/$defs/service.snmpd.agent"}, "access": {"$ref": "#/$defs/service.snmpd.access"}, "agentx": {"$ref": "#/$defs/service.snmpd.agentx"}, "com2sec": {"$ref": "#/$defs/service.snmpd.com2sec"}, "general": {"$ref": "#/$defs/service.snmpd.general"}, "pass": {"$ref": "#/$defs/service.snmpd.pass"}, "group": {"$ref": "#/$defs/service.snmpd.group"}, "system": {"$ref": "#/$defs/service.snmpd.system"}, "view": {"$ref": "#/$defs/service.snmpd.view"}}}, "service.dhcp-inject": {"description": "Define the interfaces on which the dhcp shall be relayed.", "type": "object", "properties": {"select-ports": {"description": "The list of physical network devices that shall be used to fwd the DHCP frames.", "type": "array", "items": {"type": "string"}}}}, "service": {"description": "This section describes all of the services that may be present on the AP. Each service is then referenced via its name inside an interface, ssid, ...", "type": "object", "properties": {"lldp": {"$ref": "#/$defs/service.lldp"}, "ssh": {"$ref": "#/$defs/service.ssh"}, "ntp": {"$ref": "#/$defs/service.ntp"}, "mdns": {"$ref": "#/$defs/service.mdns"}, "rtty": {"$ref": "#/$defs/service.rtty"}, "log": {"$ref": "#/$defs/service.log"}, "http": {"$ref": "#/$defs/service.http"}, "igmp": {"$ref": "#/$defs/service.igmp"}, "ieee8021x": {"$ref": "#/$defs/service.ieee8021x"}, "radius-proxy": {"$ref": "#/$defs/service.radius-proxy"}, "online-check": {"$ref": "#/$defs/service.online-check"}, "data-plane": {"$ref": "#/$defs/service.data-plane"}, "wifi-steering": {"$ref": "#/$defs/service.wifi-steering"}, "quality-of-service": {"$ref": "#/$defs/service.quality-of-service"}, "facebook-wifi": {"$ref": "#/$defs/service.facebook-wifi"}, "airtime-fairness": {"$ref": "#/$defs/service.airtime-fairness"}, "wireguard-overlay": {"$ref": "#/$defs/service.wireguard-overlay"}, "captive": {"$ref": "#/$defs/service.captive"}, "gps": {"$ref": "#/$defs/service.gps"}, "dhcp-relay": {"$ref": "#/$defs/service.dhcp-relay"}, "admin-ui": {"$ref": "#/$defs/service.admin-ui"}, "rrm": {"$ref": "#/$defs/service.rrm"}, "fingerprint": {"$ref": "#/$defs/service.fingerprint"}, "snmpd": {"$ref": "#/$defs/service.snmpd"}, "dhcp-inject": {"$ref": "#/$defs/service.dhcp-inject"}}}, "metrics.statistics": {"description": "Statistics are traffic counters, neighbor tables, ...", "type": "object", "properties": {"interval": {"description": "The reporting interval defined in seconds.", "type": "integer", "minimum": 60}, "types": {"description": "A list of names of subsystems that shall be reported periodically.", "type": "array", "items": {"type": "string", "enum": ["ssids", "lldp", "clients", "tid-stats"]}}}}, "metrics.health": {"description": "Health check gets executed periodically and will report a health value between 0-100 indicating how healthy the device thinks it is", "type": "object", "properties": {"interval": {"description": "The reporting interval defined in seconds.", "type": "integer", "minimum": 60}, "dhcp-local": {"description": "This is makes the AP probe local downstream DHCP servers.", "type": "boolean", "default": true}, "dhcp-remote": {"description": "This is makes the AP probe remote upstream DHCP servers.", "type": "boolean", "default": false}, "dns-local": {"description": "This is makes the AP probe DNS servers.", "type": "boolean", "default": true}, "dns-remote": {"description": "This is makes the AP probe DNS servers.", "type": "boolean", "default": true}}}, "metrics.wifi-frames": {"description": "Define which types of ieee802.11 management frames shall be sent up to the controller.", "type": "object", "properties": {"filters": {"description": "A list of the management frames types that shall be sent to the backend.", "type": "array", "items": {"type": "string", "enum": ["probe", "auth", "assoc", "disassoc", "<PERSON><PERSON><PERSON>", "local-deauth", "inactive-<PERSON><PERSON><PERSON>", "key-mismatch", "beacon-report", "radar-detected", "sta-authorized", "ft-finish"]}}}}, "metrics.dhcp-snooping": {"description": "DHCP snooping allows us to intercept DHCP packages on interface that are bridged, where DHCP is not offered as a service by the AP.", "type": "object", "properties": {"filters": {"description": "A list of the message types that shall be sent to the backend.", "type": "array", "items": {"type": "string", "enum": ["ack", "discover", "offer", "request", "solicit", "reply", "renew"]}}}}, "metrics.wifi-scan": {"description": "Define the behaviour of the periodic wifi scanning interface.", "type": "object", "properties": {"interval": {"description": "The periodicity at which the scan shall be performed.", "type": "integer"}, "verbose": {"description": "Add capabilities, v/ht_oper, ... to the resulting scan info.", "type": "boolean"}, "information-elements": {"description": "Add all IEs to the resulting scan info.", "type": "boolean"}}}, "metrics.telemetry": {"description": "Configure the unsolicited telemetry stream.", "type": "object", "properties": {"interval": {"description": "The reporting interval defined in seconds.", "type": "integer"}, "types": {"description": "The event types that get added to telemetry.", "type": "array", "items": {"type": "string"}}}}, "metrics.realtime": {"description": "Configure the realtime events that get sent to the cloud.", "type": "object", "properties": {"types": {"description": "The event types that get added to telemetry.", "type": "array", "items": {"type": "string"}}}}, "metrics": {"description": "There are several types of mertics that shall be reported in certain intervals. This section provides a granual configuration.", "type": "object", "properties": {"statistics": {"$ref": "#/$defs/metrics.statistics"}, "health": {"$ref": "#/$defs/metrics.health"}, "wifi-frames": {"$ref": "#/$defs/metrics.wifi-frames"}, "dhcp-snooping": {"$ref": "#/$defs/metrics.dhcp-snooping"}, "wifi-scan": {"$ref": "#/$defs/metrics.wifi-scan"}, "telemetry": {"$ref": "#/$defs/metrics.telemetry"}, "realtime": {"$ref": "#/$defs/metrics.realtime"}}}, "config-raw": {"description": "This object allows passing raw uci commands, that get applied after all the other configuration was ben generated.", "type": "array", "items": {"type": "array", "minItems": 2, "items": {"type": "string"}, "examples": [["set", "system.@system[0].timezone", "GMT0"], ["delete", "firewall.@zone[0]"], ["delete", "dhcp.wan"], ["add", "dhcp", "dhcp"], ["add-list", "system.ntp.server", "0.pool.example.org"], ["del-list", "system.ntp.server", "1.openwrt.pool.ntp.org"]]}}, "timeouts": {"description": "A device has certain properties that describe its identity and location. These properties are described inside this object.", "type": "object", "properties": {"offline": {"description": "How long can the device be offline before it enters orphan state.", "type": "integer"}, "orphan": {"description": "The interval at which an orphaned device will try to discover the cloud.", "type": "integer"}, "validate": {"description": "How long the client has time to connect to the cloud before discovery is restarted.", "type": "integer"}}}}}