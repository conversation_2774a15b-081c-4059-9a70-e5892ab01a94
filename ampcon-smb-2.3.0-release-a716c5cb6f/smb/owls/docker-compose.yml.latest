volumes:
  kafka_data:
    driver: local

networks:
  owls:

services:
  owsec:
    image: "harbor.ampcon.com/openwifi/darius-ucentralsec:noauth"
    networks:
      owls:
        aliases:
          - ${INTERNAL_OWSEC_HOSTNAME}
    env_file:
      - owsec.env
    depends_on:
      init-kafka:
        condition: service_completed_successfully
    restart: unless-stopped
    volumes:
      - "./owsec_data:${OWSEC_ROOT}"
      - "../certs:/${OWSEC_ROOT}/certs"
    ports:
      - "16001:16001"
      - "16101:16101"

  owls:
    image: "harbor.ampcon.com/openwifi/smb-owls-dev:latest"
    networks:
      owls:
        aliases:
          - ${INTERNAL_OWLS_HOSTNAME}
    env_file:
      - owls.env
    depends_on:
      owsec:
        condition: service_started
      init-kafka:
        condition: service_completed_successfully
    restart: unless-stopped
    volumes:
      - "./owls_data:${OWLS_ROOT}"
      - "../certs:/${OWLS_ROOT}/certs"
    ports:
      - "16007:16007"
      - "16107:16107"

  owls-ui:
    image: "harbor.ampcon.com/openwifi/darius-owls-ui:master"
    networks:
      owls:
    env_file:
      - owls-ui.env
    depends_on:
      - owsec
      - owls
    restart: unless-stopped
    volumes:
      - "./owls-ui/default.conf:/etc/nginx/conf.d/default.conf"
      - "../certs/restapi-cert.pem:/etc/nginx/restapi-cert.pem"
      - "../certs/restapi-key.pem:/etc/nginx/restapi-key.pem"
    ports:
      - "8044:80"
      - "4443:443"

  kafka:
    image: "harbor.ampcon.com/openwifi/kafka:3.7-debian-12"
    networks:
      owls:
    env_file:
      - kafka.env
    restart: unless-stopped
    volumes:
      - kafka_data:/bitnami/kafka

  init-kafka:
    image: "harbor.ampcon.com/openwifi/kafka:3.7-debian-12"
    networks:
      owls:
    depends_on:
      - kafka
    env_file:
      - kafka.env
    entrypoint:
      - /bin/sh
      - -c
      - |
        echo "Sleeping to allow kafka to start up..."
        sleep 10
        echo "Creating all required Kafka topics..."
        for topic in $$TOPICS; do
          /opt/bitnami/kafka/bin/kafka-topics.sh \
          --create --if-not-exists --topic $$topic --replication-factor 1 \
          --partitions 1 --bootstrap-server kafka:9092
        done && echo "Successfully created Kafka topics, exiting." && exit 0
