RUN_CHOWN=true
TEMPLATE_CONFIG=true
SELFSIGNED_CERTS=true

OWFMS_ROOT=/owfms-data
OWFMS_CONFIG=/owfms-data

#RESTAPI_HOST_ROOTCA=$OWFMS_ROOT/certs/restapi-ca.pem
#RESTAPI_HOST_PORT=16004
#RESTAPI_HOST_CERT=$OWFMS_ROOT/certs/restapi-cert.pem
#RESTAPI_HOST_KEY=$OWFMS_ROOT/certs/restapi-key.pem
#RESTAPI_HOST_KEY_PASSWORD=mypassword
#INTERNAL_RESTAPI_HOST_ROOTCA=$OWFMS_ROOT/certs/restapi-ca.pem
#INTERNAL_RESTAPI_HOST_PORT=17004
#INTERNAL_RESTAPI_HOST_CERT=$OWFMS_ROOT/certs/restapi-cert.pem
#INTERNAL_RESTAPI_HOST_KEY=$OWFMS_ROOT/certs/restapi-key.pem
#INTERNAL_RESTAPI_HOST_KEY_PASSWORD=mypassword
#SERVICE_KEY=$OWFMS_ROOT/certs/restapi-key.pem
#SERVICE_KEY_PASSWORD=mypassword
SYSTEM_DATA=$OWFMS_ROOT/persist
SYSTEM_URI_PRIVATE=https://owfms.wlan.local:17004
SYSTEM_URI_PUBLIC=https://openwifi.wlan.local:16004
SYSTEM_URI_UI=https://openwifi.wlan.local
#SECURITY_RESTAPI_DISABLE=false
#S3_BUCKETNAME=ucentral-ap-firmware
#S3_REGION=us-east-1
S3_SECRET=b0S6EiR5RLIxoe7Xvz9YXPPdxQCoZ6ze37qunTAI
S3_KEY=********************
#S3_BUCKET_URI=ucentral-ap-firmware.s3.amazonaws.com
#KAFKA_ENABLE=true
KAFKA_BROKERLIST=kafka:9092
STORAGE_TYPE=postgresql
#STORAGE_TYPE_POSTGRESQL_HOST=postgresql
#STORAGE_TYPE_POSTGRESQL_USERNAME=owfms
#STORAGE_TYPE_POSTGRESQL_PASSWORD=owfms
#STORAGE_TYPE_POSTGRESQL_DATABASE=owfms
#STORAGE_TYPE_POSTGRESQL_PORT=5432
#STORAGE_TYPE_MYSQL_HOST=localhost
#STORAGE_TYPE_MYSQL_USERNAME=owfms
#STORAGE_TYPE_MYSQL_PASSWORD=owfms
#STORAGE_TYPE_MYSQL_DATABASE=owfms
#STORAGE_TYPE_MYSQL_PORT=3306
#STORAGE_TYPE=sqlite
