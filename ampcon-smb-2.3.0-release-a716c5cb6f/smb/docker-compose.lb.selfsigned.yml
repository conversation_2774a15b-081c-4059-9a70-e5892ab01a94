volumes:
  kafka_data:
    driver: local
  postgresql_data:
    driver: local

networks:
  openwifi:

services:
  owgw:
    image: "harbor.ampcon.com/openwifi/owgw-dev:latest"
    networks:
      openwifi:
        aliases:
          - ${INTERNAL_OWGW_HOSTNAME}
    env_file:
      - .env.selfsigned
      - owgw.env
    depends_on:
      init-kafka:
        condition: service_completed_successfully
      postgresql:
        condition: service_healthy
    command: ["./wait-for-postgres.sh", "postgresql", "/openwifi/owgw"]
    restart: unless-stopped
    volumes:
      - "./owgw_data:${OWGW_ROOT}"
      - "./certs:/${OWGW_ROOT}/certs"
    sysctls:
      - net.ipv4.tcp_keepalive_intvl=5
      - net.ipv4.tcp_keepalive_probes=2
      - net.ipv4.tcp_keepalive_time=45

  owsec:
    image: "harbor.ampcon.com/openwifi/smb-owsec-dev:latest"
    networks:
      openwifi:
        aliases:
          - ${INTERNAL_OWSEC_HOSTNAME}
    env_file:
      - .env.selfsigned
      - owsec.env
    depends_on:
      init-kafka:
        condition: service_completed_successfully
      postgresql:
        condition: service_healthy
    command: ["./wait-for-postgres.sh", "postgresql", "/openwifi/owsec"]
    restart: unless-stopped
    volumes:
      - "./owsec_data:${OWSEC_ROOT}"
      - "./certs:/${OWSEC_ROOT}/certs"

  owfms:
    image: "harbor.ampcon.com/openwifi/darius-ucentralfms:latest"
    networks:
      openwifi:
        aliases:
          - ${INTERNAL_OWFMS_HOSTNAME}
    env_file:
      - .env.selfsigned
      - owfms.env
    depends_on:
      init-kafka:
        condition: service_completed_successfully
      postgresql:
        condition: service_healthy
    command: ["./wait-for-postgres.sh", "postgresql", "/openwifi/owfms"]
    restart: unless-stopped
    volumes:
      - "./owfms_data:${OWFMS_ROOT}"
      - "./certs:/${OWFMS_ROOT}/certs"
  owprov:
    image: "harbor.ampcon.com/openwifi/smb-owprov-dev:latest"
    networks:
      openwifi:
        aliases:
          - ${INTERNAL_OWPROV_HOSTNAME}
    env_file:
      - .env.selfsigned
      - owprov.env
    depends_on:
      init-kafka:
        condition: service_completed_successfully
      postgresql:
        condition: service_healthy
    command: ["./wait-for-postgres.sh", "postgresql", "/openwifi/owprov"]
    restart: unless-stopped
    volumes:
      - "./owprov_data:${OWPROV_ROOT}"
      - "./certs:/${OWPROV_ROOT}/certs"

  owprov-ui:
    image: "harbor.ampcon.com/openwifi/smb-owunion-ui-dev:latest"
    networks:
      openwifi:
        aliases:
          - ${INTERNAL_OWPROVUI_HOSTNAME}
    env_file:
      - owprov-ui.env
    depends_on:
      - owsec
      - owgw
      - owfms
      - owprov
      - owanalytics
      - owsub
    restart: unless-stopped
  owrrm:
    image: "harbor.ampcon.com/openwifi/darius-rrm:latest"
    networks:
      openwifi:
        aliases:
          - ${INTERNAL_OWRRM_HOSTNAME}
    env_file:
      - owrrm.env
    depends_on:
      mysql:
        condition: service_healthy
      init-kafka:
        condition: service_completed_successfully
    restart: unless-stopped
    volumes:
      - "./certs:/owrrm-data/certs"    
      - "./owrrm_data:/owrrm-data"
      - "./owrrm_data/runner.sh:/runner.sh"
#    ports:
#      - "16789:16789"     
      
  mysql:
    image: "harbor.ampcon.com/openwifi/mysql:${MYSQL_TAG}"
    networks:
      openwifi:
    env_file:
      - mysql.env
    restart: unless-stopped
    volumes:
      - "./mysql_data:/var/lib/mysql"
    healthcheck:
      # owsub is the last DB created in init-db.sh
      test: ["CMD-SHELL", "mysqladmin ping -u root -popenwifi"]
      interval: 30s
      retries: 3
      start_period: 10s
      timeout: 10s     

  owanalytics:
    image: "harbor.ampcon.com/openwifi/darius-analytics:latest"
    networks:
      openwifi:
        aliases:
          - ${INTERNAL_OWANALYTICS_HOSTNAME}
    env_file:
      - .env.selfsigned
      - owanalytics.env
    depends_on:
      init-kafka:
        condition: service_completed_successfully
      postgresql:
        condition: service_healthy
    command: ["./wait-for-postgres.sh", "postgresql", "/openwifi/owanalytics"]
    restart: unless-stopped
    volumes:
      - "./owanalytics_data:${OWANALYTICS_ROOT}"
      - "./certs:/${OWANALYTICS_ROOT}/certs"

  owsub:
    image: "harbor.ampcon.com/openwifi/darius-userportal:latest"
    networks:
      openwifi:
        aliases:
          - ${INTERNAL_OWSUB_HOSTNAME}
    env_file:
      - .env.selfsigned
      - owsub.env
    depends_on:
      init-kafka:
        condition: service_completed_successfully
      postgresql:
        condition: service_healthy
    command: ["./wait-for-postgres.sh", "postgresql", "/openwifi/owsub"]
    restart: unless-stopped
    volumes:
      - "./owsub_data:${OWSUB_ROOT}"
      - "./certs:/${OWSUB_ROOT}/certs"

  kafka:
    image: "harbor.ampcon.com/openwifi/kafka:${KAFKA_TAG}"
    networks:
      openwifi:
    env_file:
      - kafka.env
    restart: unless-stopped
    volumes:
      - kafka_data:/bitnami/kafka

  init-kafka:
    image: "harbor.ampcon.com/openwifi/kafka:${KAFKA_TAG}"
    networks:
      openwifi:
    depends_on:
      - kafka
    env_file:
      - kafka.env
    entrypoint:
      - /bin/sh
      - -c
      - |
        echo "Sleeping to allow kafka to start up..."
        sleep 10
        echo "Creating all required Kafka topics..."
        for topic in $$TOPICS; do
          /opt/bitnami/kafka/bin/kafka-topics.sh \
          --create --if-not-exists --topic $$topic --replication-factor 1 \
          --partitions 1 --bootstrap-server kafka:9092
        done && echo "Successfully created Kafka topics, exiting." && exit 0

  postgresql:
    image: "harbor.ampcon.com/openwifi/postgres:${POSTGRESQL_TAG}"
    networks:
      openwifi:
    command:
      - "postgres"
      - "-c"
      - "max_connections=400"
      - "-c"
      - "shared_buffers=20MB"
    env_file:
      - postgresql.env
    restart: unless-stopped
    volumes:
      - postgresql_data:/var/lib/postgresql/data
      - ./postgresql/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
    healthcheck:
      # owsub is the last DB created in init-db.sh
      test: ["CMD-SHELL", "pg_isready -U postgres -d owsub"]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s

  traefik:
    image: "harbor.ampcon.com/openwifi/traefik:${TRAEFIK_TAG}"
    networks:
      openwifi:
    env_file:
      - traefik.env
    depends_on:
      - owsec
      - owgw
      - owfms
      - owprov
      - owprov-ui
      - owanalytics
      - owsub
      - owrrm
    restart: unless-stopped
    volumes:
      - "./traefik/openwifi_selfsigned.yaml:/etc/traefik/openwifi.yaml"
      - "./certs/restapi-ca.pem:/certs/restapi-ca.pem"
      - "./certs/restapi-cert.pem:/certs/restapi-cert.pem"
      - "./certs/restapi-key.pem:/certs/restapi-key.pem"
    ports:
      - "15002:15002"
      - "16002:16002"
      - "16003:16003"
      - "80:80"
      - "8080:8080"
      - "443:443"
      - "8443:8443"
      - "16001:16001"
      - "16004:16004"
      - "16005:16005"
      - "16009:16009"
      - "16006:16006"
      - "5912:5912"
      - "5913:5913"
      - "16789:16789"
      - "1812:1812/udp"
      - "1813:1813/udp"
      - "3799:3799/udp"
