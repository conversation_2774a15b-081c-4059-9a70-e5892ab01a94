# OpenWiFi Cloud SDK deployment with CloudFormation
With the YAML template included in this directory you can create an OpenWiFi Cloud SDK deployment with the help of AWS CloudFormation (https://aws.amazon.com/cloudformation).  
The template creates a CloudFormation stack based on the Docker Compose Let's Encrypt deployment (https://github.com/Telecominfraproject/wlan-cloud-ucentral-deploy/tree/main/docker-compose#lb-deployment-with-letsencrypt-certificates). The created stack consists of an EC2 instance, and depending on the input parameters, also adds a Route53 hosted zone and a DNS record.  
⚠️**Note**: Please be aware that you will be billed for the AWS resources if you create a stack from this template.
1. Login into the AWS Management Console (https://aws.amazon.com/de/console).
2. Go to the AWS Systems Manager Parameter Store page and create two parameters according to these instructions https://docs.aws.amazon.com/systems-manager/latest/userguide/parameter-create-console.html: one for your Digicert-signed websocket certificate and the other one for the corresponding key. You can leave the default parameter details: you need two standard parameters with type `String` and data type `text`. Just copy and paste your certificate and key into the `Value` field of the respective parameter and remember the parameter names.
3. Go to the CloudFormation service page and follow the instructions described here https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/cfn-using-console-create-stack-template.html to upload a template file and choose the template included in this repository.
4. In the next step you have to enter multiple input parameters required for a successful deployment. Here's an explanation of all parameters:

**InstanceType**: Choose an AWS EC2 instance type (https://aws.amazon.com/ec2/instance-types). The smallest instance type you can choose is t2.small.  
**KeyName**: Specify the name of the SSH key pair you want to use to connect the instance. If you don't have a key pair yet, please create or import one according to these instructions https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ec2-key-pairs.html.  
**SSHLocation**: If you want to restrict the IP range which is allowed to connect to the instance via SSH, please specify a valid CIDR IP range here.  
**CreateRoute53Record**: To expose your SDK installation to the public you need a valid DNS entry for your SDK hostname. This is also required to pass the Let's Encrypt HTTP-01 challenge (https://letsencrypt.org/de/docs/challenge-types/#http-01-challenge). If you set this to `True`, an Amazon Route53 entry (https://aws.amazon.com/route53) for the hostname defined in the **SDKHostname** parameter is automatically created. This Route53 entry will resolve to the public IP address of the EC2 instance. You can also set this to `False` and create a DNS entry manually afterwards.  
**ExistingHostedZoneId**: If you decide to create a Route53 record and already have an existing hosted zone which you want to use, please specify the according hosted zone ID. You can get the ID by listing your public hosted zones (https://docs.aws.amazon.com/Route53/latest/DeveloperGuide/ListInfoOnHostedZone.html).  
**HostedZoneName**: If you didn't create the hosted zone yet which you want to use for your Route53 record, please specify the domain name of the hosted zone you want to create. Be aware that if you set **CreateRoute53Record** to `True`, you only have to specify either `ExistingHostedZoneId` or `HostedZoneName`. If you decide to create the DNS record yourself, you can leave both parameters empty.  
**SDKVersion**: The SDK version you want to use for your deployment. You can either use release names (e.g. `v2.6.0`) or Git branch names (for example `release/v2.6.0`).  
**SDKHostname**: Enter a valid public hostname which you want to use for your deployment. This has to resolve to the public IP address of the created EC2 instance. If you set **CreateRoute53Record** to `False`, don't forget to create a DNS entry manually afterwards.  
**WebsocketCertParameter**: The name of the AWS Systems Manager parameter containing your Digicert-signed websocket certificate.  
**WebsocketKeyParameter**: The name of the AWS Systems Manager parameter containing the key to your Digicert-signed websocket certificate.  
**TraefikAcmeEmail**: Enter a valid email address to complete Let's Encrypt ACME registration.  
