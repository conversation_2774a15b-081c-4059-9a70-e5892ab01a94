#!/bin/bash
#Execute this shell script in the same level directory of all downloaded TIP-OPENWIFI code repositories
modules=()
current_dir=$(pwd)
#echo $current_dir
build_gw_image() {
	echo "Build gw image start!"
	cd $current_dir/wlan-cloud-ucentralgw/
	git pull
	docker build --no-cache -t campus-smb-owgw-dev:latest -f Dockerfile.app .
	docker tag campus-smb-owgw-dev:latest harbor.ampcon.com/openwifi/campus-smb-owgw-dev:latest
	docker push harbor.ampcon.com/openwifi/campus-smb-owgw-dev:latest
	echo "Build gw image end!"
}

build_sec_image() {
	echo "Build sec image start!"
	cd $current_dir/wlan-cloud-ucentralsec/
	git pull
	docker build --no-cache -t campus-smb-owsec-dev:latest -f Dockerfile.app .
	docker tag campus-smb-owsec-dev:latest harbor.ampcon.com/openwifi/campus-smb-owsec-dev:latest
	docker push harbor.ampcon.com/openwifi/campus-smb-owsec-dev:latest
	echo "Build sec image end!"
}

build_prov_image() {
	echo "Build prov image start!"
	cd $current_dir/wlan-cloud-owprov/
	git pull
	docker build --no-cache -t campus-smb-owprov-dev:latest -f Dockerfile.app .
	docker tag campus-smb-owprov-dev:latest harbor.ampcon.com/openwifi/campus-smb-owprov-dev:latest
	docker push harbor.ampcon.com/openwifi/campus-smb-owprov-dev:latest
	echo "Build prov image end!"
}

build_analytics_image() {
	echo "Build analytics image start!"
	cd $current_dir/wlan-cloud-analytics/
	git pull
	docker build --no-cache -t campus-smb-owanalytics-dev:latest -f Dockerfile.app .
	docker tag campus-smb-owanalytics-dev:latest harbor.ampcon.com/openwifi/campus-smb-owanalytics-dev:latest
	docker push harbor.ampcon.com/openwifi/campus-smb-owanalytics-dev:latest
	echo "Build analytics image end!"
}

build_rrm_image() {
	echo "Build rrm image start!"
	cd $current_dir/wlan-cloud-rrm/
	git pull
	docker build --no-cache -t campus-smb-owrrm-dev:latest -f Dockerfile.app .
	docker tag campus-smb-owrrm-dev:latest harbor.ampcon.com/openwifi/campus-smb-owrrm-dev:latest
	docker push harbor.ampcon.com/openwifi/campus-smb-owrrm-dev:latest
	echo "Build rrm image end!"
}

check_build_module_type() {
	if [ "$#" -lt 1 ]; then
		echo "Usage: $0 {--all|--sec|--gw|--prov|--analytics|--rrm}"
		exit 1
	fi
	for i in $@; do
		case "$i" in
		--all | --sec | --gw | --prov | --analytics | --rrm)
			if [[ "$i" == "--all" ]]; then
				modules+=("sec" "gw" "prov" "analytics" "rrm")
			else
				modules+=(${i:2})
			fi
			;;
		*)
			echo "Invalid argument: $1"
			echo "Usage: $0 {--all|--sec|--gw|--prov|--analytics|--rrm}"
			exit 1
			;;
		esac
	done
	echo "check_build_module_type:"${modules[@]}
}

build_images() {
	for i in ${modules[@]}; do
		case "$i" in
		sec)
			build_sec_image
			;;
		gw)
			build_gw_image
			;;
		prov)
			build_prov_image
			;;
		analytics)
			build_analytics_image
			;;
		rrm)
			build_rrm_image
			;;
		*)
			echo "Invalid argument: $i"
			exit 1
			;;
		esac
	done
}

main() {
	start_time=$(date +%s)
	check_build_module_type "$@"
	build_images $?
	echo ">>>>>>>>>>>>>>>>>>>  Build tip-openwifi image start   <<<<<<<<<<<<<<<<<<<<<"
	echo ">>>>>>>>>>>>>>>>>>>   Build tip-openwifi image successfully   <<<<<<<<<<<<<<<<"

	end_time=$(date +%s)
	duration=$((end_time - start_time))
	hours=$((duration / 3600))
	minutes=$(((duration % 3600) / 60))
	seconds=$((duration % 60))
	time_taken=$(printf "%02d:%02d:%02d" $hours $minutes $seconds)
	echo ">>>>>>>>>>>>>>>>>>> Congratulations! build image took time $time_taken      <<<<<<<<<<<<<<<<"
}

main "$@"
