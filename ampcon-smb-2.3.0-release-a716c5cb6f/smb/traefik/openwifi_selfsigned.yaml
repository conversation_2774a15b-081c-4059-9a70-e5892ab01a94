tls:
  certificates:
    - certFile: /certs/restapi-cert.pem
      keyFile: /certs/restapi-key.pem

http:
  services:
    owgw-ui:
      loadBalancer:
        servers:
          - url: "http://owgw-ui.wlan.local:80/"

    owprov-ui:
      loadBalancer:
        servers:
          - url: "http://owprov-ui.wlan.local:80/"

    owrrm-openapi:
      loadBalancer:
        servers:
          - url: "http://owrrm.wlan.local:16789/"

  routers:
    owgw-ui-http:
      entryPoints: "owgwuihttp"
      service: "owgw-ui"
      rule: "PathPrefix(`/`)"

    owgw-ui-https:
      entryPoints: "owgwuihttps"
      service: "owgw-ui"
      rule: "PathPrefix(`/`)"
      tls: {}

    owprov-ui-http:
      entryPoints: "owprovuihttp"
      service: "owprov-ui"
      rule: "PathPrefix(`/`)"

    owprov-ui-https:
      entryPoints: "owprovuihttps"
      service: "owprov-ui"
      rule: "PathPrefix(`/`)"
      tls: {}

    owrrm-openapi:
      entryPoints: "owrrmopenapi"
      service: "owrrm-openapi"
      rule: "PathPrefix(`/`)"
      tls: {}

tcp:
  services:
    # owgw-websocket:
    #   loadBalancer:
    #     servers:
    #       - address: "owgw.wlan.local:15002"
    owgw-restapi:
      loadBalancer:
        servers:
          - address: "owgw.wlan.local:16002"
    owgw-fileupload:
      loadBalancer:
        servers:
          - address: "owgw.wlan.local:16003"
    owgw-rttys:
      loadBalancer:
        servers:
          - address: "owgw.wlan.local:5912"
    owgw-rttys-view:
      loadBalancer:
        servers:
          - address: "owgw.wlan.local:5913"
    owsec-restapi:
      loadBalancer:
        servers:
          - address: "owsec.wlan.local:16001"
    # owfms-restapi:
    #   loadBalancer:
    #     servers:
    #       - address: "owfms.wlan.local:16004"
    owprov-restapi:
      loadBalancer:
        servers:
          - address: "owprov.wlan.local:16005"
    owanalytics-restapi:
      loadBalancer:
        servers:
          - address: "owanalytics.wlan.local:16009"
    owsub-restapi:
      loadBalancer:
        servers:
          - address: "owsub.wlan.local:16006"

  routers:
    # owgw-websocket:
    #   entryPoints: "owgwwebsocket"
    #   service: "owgw-websocket"
    #   rule: "HostSNI(`*`)"
    #   tls:
    #     passthrough: true
    owgw-restapi:
      entryPoints: "owgwrestapi"
      service: "owgw-restapi"
      rule: "HostSNI(`*`)"
      tls:
        passthrough: true
    owgw-fileupload:
      entryPoints: "owgwfileupload"
      service: "owgw-fileupload"
      rule: "HostSNI(`*`)"
      tls:
        passthrough: true
    owgw-rttys:
      entryPoints: "owgwrttys"
      service: "owgw-rttys"
      rule: "HostSNI(`*`)"
      tls:
        passthrough: true
    owgw-rttys-view:
      entryPoints: "owgwrttysview"
      service: "owgw-rttys-view"
      rule: "HostSNI(`*`)"
      tls:
        passthrough: true
    owsec-restapi:
      entryPoints: "owsecrestapi"
      service: "owsec-restapi"
      rule: "HostSNI(`*`)"
      tls:
        passthrough: true
    # owfms-restapi:
    #   entryPoints: "owfmsrestapi"
    #   service: "owfms-restapi"
    #   rule: "HostSNI(`*`)"
    #   tls:
    #     passthrough: true
    owprov-restapi:
      entryPoints: "owprovrestapi"
      service: "owprov-restapi"
      rule: "HostSNI(`*`)"
      tls:
        passthrough: true
    owanalytics-restapi:
      entryPoints: "owanalyticsrestapi"
      service: "owanalytics-restapi"
      rule: "HostSNI(`*`)"
      tls:
        passthrough: true
    owsub-restapi:
      entryPoints: "owsubrestapi"
      service: "owsub-restapi"
      rule: "HostSNI(`*`)"
      tls:
        passthrough: true

udp:
  services:
    owgw-radius-acc:
      loadBalancer:
        servers:
          - address: "owgw.wlan.local:1813"
    owgw-radius-auth:
      loadBalancer:
        servers:
          - address: "owgw.wlan.local:1812"
    owgw-radius-coa:
      loadBalancer:
        servers:
          - address: "owgw.wlan.local:3799"

  routers:
    owgw-radius-acc:
      entryPoints: "owgwradacc"
      service: "owgw-radius-acc"
    owgw-radius-auth:
      entryPoints: "owgwradauth"
      service: "owgw-radius-auth"
    owgw-radius-coa:
      entryPoints: "owgwradcoa"
      service: "owgw-radius-coa"
