server {
    listen       8080;
    listen  [::]:8080;

    # Disable emitting nginx version
    server_tokens off;

    return 301 https://$host:8443$request_uri;
}

server {
    listen       8443 ssl;
    listen       [::]:8443 ssl;

    # Disable emitting nginx version
    server_tokens off;

    ssl_certificate     /etc/nginx/restapi-cert.pem;
    ssl_certificate_key /etc/nginx/restapi-key.pem;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
    }

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
