COMPOSE_PROJECT_NAME=openwifi

# Image tags
OWGW_TAG=v3.2.1-RC2
OWGWUI_TAG=v3.2.1-RC2
OWSEC_TAG=v3.2.0
OWFMS_TAG=v3.2.0
OWPROV_TAG=v3.2.1-RC1
OWPROVUI_TAG=v3.2.0
OWANALYTICS_TAG=v3.2.0
OWSUB_TAG=v3.2.0

KAFKA_TAG=3.7-debian-12
POSTGRESQL_TAG=15.0
ACMESH_TAG=latest
TRAEFIK_TAG=v3.1.0

# Microservice root/config directories
OWGW_ROOT=/owgw-data
OWGW_CONFIG=/owgw-data
OWSEC_ROOT=/owsec-data
OWSEC_CONFIG=/owsec-data
OWFMS_ROOT=/owfms-data
OWFMS_CONFIG=/owfms-data
OWPROV_ROOT=/owprov-data
OWPROV_CONFIG=/owprov-data
OWANALYTICS_ROOT=/owanalytics-data
OWANALYTICS_CONFIG=/owanalytics-data
OWSUB_ROOT=/owsub-data
OWSUB_CONFIG=/owsub-data

# Microservice hostnames
INTERNAL_OWGW_HOSTNAME=owgw.wlan.local
INTERNAL_OWGWUI_HOSTNAME=owgw-ui.wlan.local
INTERNAL_OWSEC_HOSTNAME=owsec.wlan.local
INTERNAL_OWFMS_HOSTNAME=owfms.wlan.local
INTERNAL_OWPROV_HOSTNAME=owprov.wlan.local
INTERNAL_OWPROVUI_HOSTNAME=owprov-ui.wlan.local
INTERNAL_OWANALYTICS_HOSTNAME=owanalytics.wlan.local
INTERNAL_RTTYS_HOSTNAME=rttys.wlan.local
INTERNAL_OWSUB_HOSTNAME=owsub.wlan.local
SDKHOSTNAME=
#SDKHOSTNAME=openwifi.example.com
