name: N3132PX-ON-DEFAULT
description: N3132PX-ON default template
platform: N3132PX-ON
content_start:


{#::::: Set Hostname ::::#}
set system hostname {{ Hostname }}

{#::::: Set Management Vlan ::::#}
set vlan-interface interface vlan4001 vif vlan4001 address {{Management_IP_Address_Mask.split('/')[0]}} prefix-length {{Management_IP_Address_Mask.split('/')[1]}}
set vlans vlan-id 4001 vlan-name "Management"
set vlans vlan-id 4001 l3-interface vlan4001

{#::::: Set Default Gateway ::::#}
set protocols static route 0.0.0.0/0 next-hop {{ Default_gateway }}


{#::::: Set the SNMP Strings::::#}
set protocols snmp contact {{ SNMP_contact }}
set protocols snmp location {{ SNMP_location }}
set protocols snmp community {{RO_SNMP_String}} authorization read-only
set protocols snmp community {{RW_SNMP_String}} authorization read-write
set protocols snmp trap-group targets ************ security-name {{Trap_SNMP_String}}
set protocols snmp trap-group targets ************* security-name {{Trap_SNMP_String}}
set protocols snmp trap-group targets ************ security-name {{Trap_SNMP_String}}
set protocols snmp trap-group targets ************ security-name {{Trap_SNMP_String}}
set protocols snmp trap-group targets ************** security-name {{Trap_SNMP_String}}



{#::::: Basic PoE config::::#}
set poe interface all enable true
set poe power management-mode 6



{#:::::Configure pvst VLANs:::::#}
set protocols spanning-tree force-version 4
set protocols spanning-tree pvst vlan 4001
{% for data_vlan in Data_VLAN_list.split(';') %}
set protocols spanning-tree pvst vlan {{ data_vlan }} enable true
{% endfor %}


{#:::::Configure DHCP Snooping :::::#}
set protocols dhcp snooping disable false
set protocols dhcp snooping port ae1 trust true
{% for data_vlan in Data_VLAN_list.split(';') %}
set protocols dhcp snooping vlan {{ data_vlan }} 
{% endfor %}



{#:::::For ports 1/1/1 through 1/1/24:data vlan 301 voice 302 ::::#}
{% for i in range(1,33) %}
set interface gigabit-ethernet ge-1/1/{{ i }} speed "auto"
set interface gigabit-ethernet ge-1/1/{{ i }} family ethernet-switching native-vlan-id 301
set interface gigabit-ethernet ge-1/1/{{ i }} family ethernet-switching port-mode "trunk"
set interface gigabit-ethernet ge-1/1/{{ i }} voice-vlan vlan-id 302
set protocols dot1x interface ge-1/1/{{ i }} host-mode "multiple"
set protocols dot1x interface ge-1/1/{{ i }} auth-mode 802.1x
set protocols dot1x interface ge-1/1/{{ i }} auth-mode mac-radius
set interface gigabit-ethernet ge-1/1/{{ i }} snmp-trap false
set interface gigabit-ethernet ge-1/1/{{ i }} storm-control broadcast ratio 1
set interface gigabit-ethernet ge-1/1/{{ i }} storm-control multicast ratio 1
set protocols spanning-tree pvst interface ge-1/1/{{ i }} bpdu-guard true
{% endfor %}



{#:::::For uplink Ports te-1/1/1-2:::::#}
set interface aggregate-ethernet ae1 aggregated-ether-options lacp enable true
set interface aggregate-ethernet ae1 family ethernet-switching port-mode "trunk"
set vlans vlan-id {{ Data_VLAN_list.split(';')|join(',') }}
set interface aggregate-ethernet ae1 family ethernet-switching vlan members {{ Data_VLAN_list.split(';')|join(',') }}
{% for i in range(1,3) %}
set interface gigabit-ethernet te-1/1/{{ i }} ether-options 802.3ad "ae1"
set interface gigabit-ethernet te-1/1/{{ i }} family ethernet-switching port-mode "trunk"
{% endfor %}



{#::::: DOT1x:::::#}
set protocols dot1x aaa radius nas-ip {{Management_IP_Address_Mask.split('/')[0]}}
set protocols dot1x aaa radius authentication server-ip ********** shared-key {{RADIUS_Key}}
set protocols dot1x aaa radius dynamic-author client ***********  shared-key {{RADIUS_Key}}
set protocols dot1x aaa radius authentication server-ip *********** shared-key {{RADIUS_Key}}
set protocols dot1x aaa radius dynamic-author client *********** shared-key {{RADIUS_Key}}


{#::::: Tacacs:::::#}
set system aaa tacacs-plus key {{TACACS_Key}}     
set system aaa tacacs-plus server-ip **********
set system aaa tacacs-plus server-ip **********
set system aaa tacacs-plus source-interface "vlan4001"
set system aaa local-auth-fallback disable false



set system log-level "trace"
set system snmp-acl network *********/8
set system login-acl network *************/24


set system login user automation authentication plain-text-password "Pica8pa1!"
set system login user automation class "super-user"

set system services ssh idle-timeout 300

set ip routing enable true

set system ntp-server-ip ************
set system ntp-server-ip ************

set system inband enable true

set system syslog server-ip *********** source-interface "vlan4001"
set system syslog server-ip ********* source-interface "vlan4001"



{{ Other_Set_Commands }}


content_end$
param_start:
{
	"Hostname": {
		"param_default": "",
		"type": "text",
		"required": "not required",
		"description": "Configure the hostname",
		"param_check": ""
	},
	"Management_IP_Address_Mask": {
		"param_default": "**********/27",
		"type": "text",
		"required": "required",
		"description": "Configure management IP mask.e.g.**********/27",
		"param_check": ""
	},
	"Default_gateway": {
		"param_default": "",
		"type": "IPv4",
		"required": "required",
		"description": "Configure the default gateway",
		"param_check": ""
	},
	"SNMP_contact": {
		"param_default": "Infosys",
		"type": "text",
		"required": "required",
		"description": "snmp contact string",
		"param_check": ""
	},
	"SNMP_location": {
		"param_default": "INDBLRSEZH01",
		"type": "text",
		"required": "required",
		"description": "snmp location string",
		"param_check": ""
	},
       "RO_SNMP_String": {
		"param_default": "",
		"type": "text",
		"required": "required",
		"description": "Enter Desired ReadOnly String for SNMP",
		"param_check": ""
	},
	"RW_SNMP_String": {
		"param_default": "",
		"type": "text",
		"required": "required",
		"description": "Enter Desired ReadWrite String for SNMP",
		"param_check": ""
	},
       "Trap_SNMP_String": {
		"param_default": "",
		"type": "text",
		"required": "required",
		"description": "Enter Desired Trap String for SNMP Servers",
		"param_check": ""
	},
	"Data_VLAN_list": {
		"param_default": "301;302;303;400;401;402;304;305;306;410;350",
		"type": "text",
		"required": "required",
		"description": "Configure Data VLAN will be connected by VXLAN, e.g. 300;301;302",
		"param_check": ""
	},
	"Dot1X_NAS_Server_IP": {
		"param_default": "",
		"type": "text",
		"required": "required",
		"description": "dot1x nas ip server address",
		"param_check": ""
	},
	"RADIUS_Key": {
		"param_default": "",
		"type": "text",
		"required": "required",
		"description": "Enter the new RADIUS authentication key",
		"param_check": ""
	},
       "TACACS_Key": {
		"param_default": "",
		"type": "text",
		"required": "not required",
		"description": "Enter the TACACS authentication key, or leave blank to remove it",
		"param_check": ""
	},
	"Other_Set_Commands": {
		"param_default": "",
		"type": "textarea",
		"required": "not required",
		"description": "Any set commands you wish to apply to the switch configuration",
		"param_check": ""
	}
}

param_end$