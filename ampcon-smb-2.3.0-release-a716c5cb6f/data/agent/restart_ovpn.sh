#!/bin/bash

# used to starting openvpn with vrf
# restart_ovpn.sh mgmt-vrf : running in mgmt-vrf
# restart_ovpn.sh default  : no vrf used
ampcon_config="/opt/auto-deploy/auto-deploy.conf"
ovpn_service="/lib/systemd/system/openvpn@.service"
port="tun0"

check_vpn_enable_status()
{
    # check whether the config file is existed
    if [ ! -f $ampcon_config ]; then
        exit 0
    fi
    # check whether the vpn is enabled
    vpn_row=`grep vpn_enable $ampcon_config`
    vpn_status=`echo $vpn_row | grep -c "True"`
    if [ $vpn_status -eq 0 ]; then
        exit 0
    fi
}


# update service files with specified vrf
update_service_with_new_vrf()
{
    local new_cmd="/sbin/ip vrf exec $vrf_name"

    if ! grep -q $vrf_name $ovpn_service; then
        sed -i "s#\(/usr/sbin/openvpn\)#$new_cmd \1#g" $ovpn_service
    fi
    sync
    systemctl daemon-reload
}

# clean service files to default vrf
restore_service_with_default_vrf()
{
    sed -i "s#=.*\(/usr/sbin/openvpn.*\)#=\1#g" $ovpn_service
    sync
    systemctl daemon-reload
}

vrf_name="$1"
if [ ! -n "$vrf_name" ]; then
    exit 1
fi

system_option="$2"
if [ -n "$system_option" ] && [ $system_option == "system" ]; then
    check_vpn_enable_status
fi

restore_service_with_default_vrf
if [ $vrf_name != "default" ]; then
    update_service_with_new_vrf
fi

if [ -f /etc/init.d/openvpn ]; then
    /etc/init.d/openvpn restart
else
    systemctl restart openvpn@client
fi

exit 0
