#!/bin/bash

log_warn() {
    echo "  $1"
    logger -t " [Auto-deploy]" -p user.warning "$1"
}

log_warn "Manual deployment starting..."

log_warn "Write 'deployed' into /home/<USER>/auto_flag"
if [ ! -d /home/<USER>/ ]; then
    sudo mkdir -m 777 /home/<USER>/
else
    sudo chmod 777 /home/<USER>/
fi
sudo echo "deployed" > /home/<USER>/auto_flag

log_warn "Restarting auto-deploy agent..."
sudo /opt/auto-deploy/agent.sh restart

log_warn "Manual deployment has done!"
