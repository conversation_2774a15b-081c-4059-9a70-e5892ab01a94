#!/bin/sh

DAEMON=/opt/auto-deploy/auto-deploy.py
NAME=auto-deploy.py

log_daemon_msg() {
    echo "$@" 
}

log_end_msg() {
    if [ "$1" -eq 0 ]; then
        echo "[ OK ]" 
    else
        echo "[ FAIL ]"
    fi
}

if [ -f /lib/lsb/init-functions ]; then
	. /lib/lsb/init-functions
fi

case $1 in
	start)
		log_daemon_msg "Starting" "auto-deploy"
		start-stop-daemon --start --background --quiet --oknodo --name $NAME --startas $DAEMON
		status=$?
		log_end_msg $status
		;;
	stop)
		log_daemon_msg "Stopping" "auto-deploy"
		start-stop-daemon --stop --quiet --oknodo --name $NAME
		log_end_msg $?
		;;
	restart|force-reload)
		$0 stop && sleep 2 && $0 start
		;;
	try-restart)
		if $0 status >/dev/null; then
			$0 restart
		else
			exit 0
		fi
		;;
	reload)
		exit 3
		;;
	status)
		status_of_proc $DAEMON "auto-deploy"
		;;
	*)
		echo "Usage: $0 {start|stop|restart|try-restart|force-reload|status}"
		exit 2
		;;
esac
