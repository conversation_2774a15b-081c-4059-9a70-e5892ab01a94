import{ac as E,ah as q,r as c,R as G,j as t,C as J,cc as K,B as Q,W as U,aq as V,$ as X,c as Y,cd as F}from"./index-CHCmiRmn.js";import{W as Z}from"./CustomTable-CM5Sdauq.js";const ee="/ampcon/wireless/monitor";function y({status:p,searchValue:k,sortBy:S,sortType:B,pageNum:m,pageSize:R}){return E({url:`${ee}/client`,method:"GET",params:{status:p,searchValue:k,sortBy:S,sortType:B,pageNum:m,pageSize:R}}).then(C=>C)}const le=()=>{const p=q(),k=c.useRef(),S=c.useRef(),[B,m]=c.useState(!1),[R,C]=c.useState([]),[_,w]=c.useState([]),[T,$]=c.useState(""),[b,z]=c.useState("all"),[A,v]=c.useState({current:1,pageSize:10,total:0}),[D,j]=c.useState({all:0,online:0,offline:0}),g=async({pageNum:n=A.current,pageSize:e=A.pageSize,sortBy:l,sortType:h,searchValue:f=T,status:o=b}={})=>{var H,P;m(!0);try{if(o==="all"){const[x,s,d]=await Promise.all([y({status:0,pageNum:n,pageSize:e,searchValue:f,sortBy:l,sortType:h}),y({status:1,pageNum:1,pageSize:1,searchValue:f}),y({status:2,pageNum:1,pageSize:1,searchValue:f})]),a=x.info||[],I=a.map(r=>({...r,mac:r.mac||"",ip:r.ip||"",sn:r.sn||"",ssid:r.ssid||"",vendor:r.vendor||"",venue:r.venue||""}));C(I),w(I),v(r=>({...r,current:n,pageSize:e,total:x.total||a.length})),j({all:x.total||a.length,online:s.total||(((H=s.info)==null?void 0:H.length)??0),offline:d.total||(((P=d.info)==null?void 0:P.length)??0)})}else if(Array.isArray(o)){const x=o.map(i=>y({status:i==="online"?1:2,pageNum:1,pageSize:1e4,searchValue:f,sortBy:l,sortType:h})),s=await Promise.all(x),d=s.flatMap(i=>i.info||[]).map(i=>({...i,mac:i.mac||"",ip:i.ip||"",sn:i.sn||"",ssid:i.ssid||"",vendor:i.vendor||"",venue:i.venue||""}));C(d);const a=(n-1)*e,I=a+e;w(d.slice(a,I)),v(i=>({...i,current:n,pageSize:e,total:d.length}));const r=s.find((i,u)=>o[u]==="online"),L=s.find((i,u)=>o[u]==="offline");j(i=>{var u,W;return{...i,online:r?r.total||(((u=r.info)==null?void 0:u.length)??0):i.online,offline:L?L.total||(((W=L.info)==null?void 0:W.length)??0):i.offline}})}else{const s=await y({status:o==="online"?1:2,pageNum:n,pageSize:e,searchValue:f,sortBy:l,sortType:h}),d=s.info||[];w(d),v(a=>({...a,current:n,pageSize:e,total:s.total||d.length})),o==="online"?j(a=>({...a,online:s.total||d.length})):o==="offline"&&j(a=>({...a,offline:s.total||d.length}))}}catch{w([]),v(s=>({...s,total:0}))}finally{m(!1)}};c.useEffect(()=>{g()},[]);const M=n=>{z(n),g({status:n,pageNum:1})},O=n=>{$(n),g({searchValue:n,pageNum:1})},N=G.useMemo(()=>[{key:"status",title:"Status",dataIndex:"status",columnsFix:!0,filters:[{text:"Online",value:1},{text:"Offline",value:2}],render:e=>{const l=e===1;return t.jsx(X,{color:l?"green":"default",style:{margin:0,borderRadius:"2px",fontFamily:"Lato, sans-serif",fontWeight:400,fontSize:"14px",lineHeight:"17px",textAlign:"left",fontStyle:"normal",textTransform:"none",minWidth:"59px",height:"24px",padding:0,display:"inline-flex",alignItems:"center",justifyContent:"center",...l?{color:"#2BC174",backgroundColor:"rgba(43, 193, 116, 0.1)",border:"1px solid #2BC174"}:{backgroundColor:"#F4F5F7",color:"#B3BBC8",border:"1px solid #DADCE1"}},children:l?"Online":"Offline"})}},{key:"host_name",title:"Hostname",dataIndex:"host_name",sorter:!0,render:(e,l)=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:"17px",lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e||l.mac||"-"})},{key:"mac",title:"MAC Address",dataIndex:"mac",sorter:!0,render:(e,l)=>t.jsx("div",{style:{width:"100%"},children:e?t.jsx("a",{onClick:()=>{const h=e.replace(/:/g,"");p(`/wireless/manage/Monitor#${l.siteId}`,{state:{scrollToClientLifecycle:!0,targetMac:h}})},style:{height:"17px",fontFamily:"Lato, sans-serif",fontWeight:400,fontSize:"14px",color:"#14C9BB",lineHeight:"17px",textAlign:"left",fontStyle:"normal",textDecoration:"underline",textDecorationColor:"#14C9BB",textTransform:"none",display:"inline-block",cursor:"pointer"},children:e}):t.jsx("span",{style:{display:"inline-block",height:"17px",lineHeight:"17px",color:"#B3BBC8"},children:"-"})})},{key:"vendor",title:"Vendor",dataIndex:"vendor",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%"},children:e||"-"})},{key:"sn",title:"Connect AP",dataIndex:"sn",sorter:!0,filterDropdown:null,filterIcon:!1,render:(e,l)=>t.jsx("div",{style:{width:"100%"},children:e?t.jsx(Y.Link,{onClick:()=>p(`/wireless/devices/${e}`),className:F["ap-link"],style:{height:"17px",fontFamily:"Lato, sans-serif",fontWeight:400,fontSize:"14px",color:"#14C9BB",lineHeight:"17px",textAlign:"left",fontStyle:"normal",textDecoration:"underline",textDecorationColor:"#14C9BB",textTransform:"none",display:"inline-block",cursor:"pointer"},children:e}):t.jsx("span",{style:{display:"inline-block",height:"17px",lineHeight:"17px",color:"#B3BBC8"},children:"-"})})},{key:"venue",title:"Site",dataIndex:"venue",sorter:!0,filterDropdown:null,filterIcon:!1,render:(e,l)=>!e&&e!==0?t.jsx("div",{style:{textAlign:"left",width:"100%",height:"17px",lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:"-"}):t.jsx("div",{style:{height:"17px",fontFamily:"Lato, sans-serif",fontWeight:400,fontSize:"14px",color:"#14C9BB",lineHeight:"17px",textAlign:"left",fontStyle:"normal",textDecoration:"underline",textDecorationColor:"#14C9BB",textTransform:"none",display:"inline-block",cursor:"pointer"},children:e?t.jsx("a",{onClick:()=>p(`/wireless/manage/Monitor#${l.siteId}`),className:F["venue-link"],style:{display:"inline-block",color:"inherit !important",textDecoration:"inherit !important"},children:e}):t.jsx("span",{style:{display:"inline-block"},children:"-"})})},{key:"ssid",title:"SSID",dataIndex:"ssid",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>t.jsx("div",{style:{textAlign:"left"},children:e||"-"})},{key:"rssi",title:"RSSI",dataIndex:"rssi",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>!e&&e!==0?t.jsx("div",{style:{textAlign:"left",width:"100%"},children:"-"}):t.jsx("div",{style:{width:"100%"},children:t.jsxs("span",{className:F["rssi-value"],children:[e," dBm"]})})},{key:"band",title:"Band",dataIndex:"band",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>t.jsx("div",{children:e||"-"})},{key:"channel",title:"Channel",dataIndex:"channel",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>t.jsx("div",{children:e||"-"})},{key:"channel_width",title:"Channel Width",dataIndex:"channel_width",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:"17px",lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e||"-"})},{key:"ip",title:"IP",dataIndex:"ip",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:"17px",lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e||"-"})},{key:"vlan",title:"VLAN",dataIndex:"vlan",sorter:!0,filterDropdown:null,filterIcon:!1,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:"17px",lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e||"-"})},{title:"Rx",dataIndex:"rx",key:"rx",sorter:!0,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:17,lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e??"-"})},{title:"Tx",dataIndex:"tx",key:"tx",sorter:!0,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:17,lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e??"-"})},{title:"Tx Packets",dataIndex:"tx_packets",key:"tx_packets",sorter:!0,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:17,lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e??"-"})},{title:"Rx Packets",dataIndex:"rx_packets",key:"rx_packets",sorter:!0,render:e=>t.jsx("div",{style:{textAlign:"left",width:"100%",height:17,lineHeight:"17px",fontFamily:"Lato, sans-serif"},children:e??"-"})}],[p]);return t.jsxs(J,{style:{display:"flex",flex:1},children:[t.jsx(K,{ref:k,saveCallback:()=>{g()}}),t.jsx("h2",{style:{margin:"8px 0 20px"},children:"Wireless Clients"}),t.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:16},children:[t.jsx("div",{style:{marginBottom:-10},children:["all","online","offline"].map((n,e,l)=>t.jsxs(Q,{type:"default",className:`tab-button ${b===n?"active":""}`,onClick:()=>M(n),children:[n==="all"&&`All (${D.all})`,n==="online"&&`Online (${D.online})`,n==="offline"&&`Offline (${D.offline})`]},n))}),t.jsx(U,{placeholder:"Search by STA, IP, AP, SSID or Vendor",allowClear:!0,enterButton:!1,size:"large",prefix:t.jsx(V,{style:{color:"#B8BFBF",width:16,height:16}}),style:{width:270,height:32,borderRadius:2,fontSize:13,fontWeight:400,marginBottom:-14},value:T,onChange:n=>O(n.target.value)})]}),t.jsx(Z,{ref:S,loading:B,columns:N,dataSource:_,rowKey:"id",showColumnSelector:"true",pagination:{...A,showTotal:n=>`Total ${n} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],showLessItems:!1},onChange:(n,e,l)=>{let h=b;if(e!=null&&e.status&&e.status.length>0){const f=e.status.map(o=>o===1?"online":o===2?"offline":"all");h=f.length===1?f[0]:f}g({pageNum:n.current,pageSize:n.pageSize,sortBy:l.field,sortType:l.order==="ascend"?"asc":l.order==="descend"?"desc":void 0,status:h})}})]})};export{le as default};
