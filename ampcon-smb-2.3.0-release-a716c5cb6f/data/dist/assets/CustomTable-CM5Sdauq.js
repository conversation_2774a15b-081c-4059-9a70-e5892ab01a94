import{d6 as ho,bO as Dr,r as v,dm as bo,_ as Ye,dK as Sr,dL as Dt,dM as ns,R as H,dN as rs,j as G,aD as os,W as yo,aq as is,an as wr,aE as ss,B as as,dO as ls,al as vo,ao as cs,dP as us,dQ as ds,dR as fs,dS as ps,dT as gs,dU as ms,F as Cr,a5 as xo,dV as hs,dW as bs}from"./index-CHCmiRmn.js";var Do={exports:{}},ys="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",vs=ys,xs=vs;function So(){}function wo(){}wo.resetWarningCache=So;var Ds=function(){function e(r,o,i,s,a,l){if(l!==xs){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}e.isRequired=e;function t(){return e}var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:wo,resetWarningCache:So};return n.PropTypes=n,n};Do.exports=Ds();var St=Do.exports;const ep=ho(St);function le(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var Ir=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),Er=function(){return Math.random().toString(36).substring(7).split("").join(".")},Or={INIT:"@@redux/INIT"+Er(),REPLACE:"@@redux/REPLACE"+Er()};function Ss(e){if(typeof e!="object"||e===null)return!1;for(var t=e;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function Co(e,t,n){var r;if(typeof t=="function"&&typeof n=="function"||typeof n=="function"&&typeof arguments[3]=="function")throw new Error(le(0));if(typeof t=="function"&&typeof n>"u"&&(n=t,t=void 0),typeof n<"u"){if(typeof n!="function")throw new Error(le(1));return n(Co)(e,t)}if(typeof e!="function")throw new Error(le(2));var o=e,i=t,s=[],a=s,l=!1;function c(){a===s&&(a=s.slice())}function f(){if(l)throw new Error(le(3));return i}function u(b){if(typeof b!="function")throw new Error(le(4));if(l)throw new Error(le(5));var g=!0;return c(),a.push(b),function(){if(g){if(l)throw new Error(le(6));g=!1,c();var h=a.indexOf(b);a.splice(h,1),s=null}}}function d(b){if(!Ss(b))throw new Error(le(7));if(typeof b.type>"u")throw new Error(le(8));if(l)throw new Error(le(9));try{l=!0,i=o(i,b)}finally{l=!1}for(var g=s=a,y=0;y<g.length;y++){var h=g[y];h()}return b}function p(b){if(typeof b!="function")throw new Error(le(10));o=b,d({type:Or.REPLACE})}function m(){var b,g=u;return b={subscribe:function(h){if(typeof h!="object"||h===null)throw new Error(le(11));function D(){h.next&&h.next(f())}D();var x=g(D);return{unsubscribe:x}}},b[Ir]=function(){return this},b}return d({type:Or.INIT}),r={dispatch:d,subscribe:u,getState:f,replaceReducer:p},r[Ir]=m,r}function Pr(e,t){return function(){return t(e.apply(this,arguments))}}function Rr(e,t){if(typeof e=="function")return Pr(e,t);if(typeof e!="object"||e===null)throw new Error(le(16));var n={};for(var r in e){var o=e[r];typeof o=="function"&&(n[r]=Pr(o,t))}return n}function Io(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.length===0?function(r){return r}:t.length===1?t[0]:t.reduce(function(r,o){return function(){return r(o.apply(void 0,arguments))}})}function ws(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(r){return function(){var o=r.apply(void 0,arguments),i=function(){throw new Error(le(15))},s={getState:o.getState,dispatch:function(){return i.apply(void 0,arguments)}},a=t.map(function(l){return l(s)});return i=Io.apply(void 0,a)(o.dispatch),Dr(Dr({},o),{},{dispatch:i})}}}function Cs(e){e()}let Eo=Cs;const Is=e=>Eo=e,Es=()=>Eo,Ar=Symbol.for("react-redux-context"),Nr=typeof globalThis<"u"?globalThis:{};function Os(){var e;if(!v.createContext)return{};const t=(e=Nr[Ar])!=null?e:Nr[Ar]=new Map;let n=t.get(v.createContext);return n||(n=v.createContext(null),t.set(v.createContext,n)),n}const Oo=Os(),Ps=()=>{throw new Error("uSES not initialized!")};var Po={exports:{}},j={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gn=Symbol.for("react.element"),zn=Symbol.for("react.portal"),zt=Symbol.for("react.fragment"),Ht=Symbol.for("react.strict_mode"),jt=Symbol.for("react.profiler"),Ut=Symbol.for("react.provider"),Vt=Symbol.for("react.context"),Rs=Symbol.for("react.server_context"),qt=Symbol.for("react.forward_ref"),Yt=Symbol.for("react.suspense"),Xt=Symbol.for("react.suspense_list"),Kt=Symbol.for("react.memo"),Jt=Symbol.for("react.lazy"),As=Symbol.for("react.offscreen"),Ro;Ro=Symbol.for("react.module.reference");function ye(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case Gn:switch(e=e.type,e){case zt:case jt:case Ht:case Yt:case Xt:return e;default:switch(e=e&&e.$$typeof,e){case Rs:case Vt:case qt:case Jt:case Kt:case Ut:return e;default:return t}}case zn:return t}}}j.ContextConsumer=Vt;j.ContextProvider=Ut;j.Element=Gn;j.ForwardRef=qt;j.Fragment=zt;j.Lazy=Jt;j.Memo=Kt;j.Portal=zn;j.Profiler=jt;j.StrictMode=Ht;j.Suspense=Yt;j.SuspenseList=Xt;j.isAsyncMode=function(){return!1};j.isConcurrentMode=function(){return!1};j.isContextConsumer=function(e){return ye(e)===Vt};j.isContextProvider=function(e){return ye(e)===Ut};j.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===Gn};j.isForwardRef=function(e){return ye(e)===qt};j.isFragment=function(e){return ye(e)===zt};j.isLazy=function(e){return ye(e)===Jt};j.isMemo=function(e){return ye(e)===Kt};j.isPortal=function(e){return ye(e)===zn};j.isProfiler=function(e){return ye(e)===jt};j.isStrictMode=function(e){return ye(e)===Ht};j.isSuspense=function(e){return ye(e)===Yt};j.isSuspenseList=function(e){return ye(e)===Xt};j.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===zt||e===jt||e===Ht||e===Yt||e===Xt||e===As||typeof e=="object"&&e!==null&&(e.$$typeof===Jt||e.$$typeof===Kt||e.$$typeof===Ut||e.$$typeof===Vt||e.$$typeof===qt||e.$$typeof===Ro||e.getModuleId!==void 0)};j.typeOf=ye;Po.exports=j;var Ns=Po.exports;const Ts=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function Ms(e,t,n,r,{areStatesEqual:o,areOwnPropsEqual:i,areStatePropsEqual:s}){let a=!1,l,c,f,u,d;function p(h,D){return l=h,c=D,f=e(l,c),u=t(r,c),d=n(f,u,c),a=!0,d}function m(){return f=e(l,c),t.dependsOnOwnProps&&(u=t(r,c)),d=n(f,u,c),d}function b(){return e.dependsOnOwnProps&&(f=e(l,c)),t.dependsOnOwnProps&&(u=t(r,c)),d=n(f,u,c),d}function g(){const h=e(l,c),D=!s(h,f);return f=h,D&&(d=n(f,u,c)),d}function y(h,D){const x=!i(D,c),S=!o(h,l,D,c);return l=h,c=D,x&&S?m():x?b():S?g():d}return function(D,x){return a?y(D,x):p(D,x)}}function Bs(e,t){let{initMapStateToProps:n,initMapDispatchToProps:r,initMergeProps:o}=t,i=bo(t,Ts);const s=n(e,i),a=r(e,i),l=o(e,i);return Ms(s,a,l,e,i)}function _s(e,t){const n={};for(const r in e){const o=e[r];typeof o=="function"&&(n[r]=(...i)=>t(o(...i)))}return n}function Rn(e){return function(n){const r=e(n);function o(){return r}return o.dependsOnOwnProps=!1,o}}function Tr(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:e.length!==1}function Ao(e,t){return function(r,{displayName:o}){const i=function(a,l){return i.dependsOnOwnProps?i.mapToProps(a,l):i.mapToProps(a,void 0)};return i.dependsOnOwnProps=!0,i.mapToProps=function(a,l){i.mapToProps=e,i.dependsOnOwnProps=Tr(e);let c=i(a,l);return typeof c=="function"&&(i.mapToProps=c,i.dependsOnOwnProps=Tr(c),c=i(a,l)),c},i}}function Hn(e,t){return(n,r)=>{throw new Error(`Invalid value of type ${typeof e} for ${t} argument when connecting component ${r.wrappedComponentName}.`)}}function Ls(e){return e&&typeof e=="object"?Rn(t=>_s(e,t)):e?typeof e=="function"?Ao(e):Hn(e,"mapDispatchToProps"):Rn(t=>({dispatch:t}))}function $s(e){return e?typeof e=="function"?Ao(e):Hn(e,"mapStateToProps"):Rn(()=>({}))}function Fs(e,t,n){return Ye({},n,e,t)}function Ws(e){return function(n,{displayName:r,areMergedPropsEqual:o}){let i=!1,s;return function(l,c,f){const u=e(l,c,f);return i?o(u,s)||(s=u):(i=!0,s=u),s}}}function ks(e){return e?typeof e=="function"?Ws(e):Hn(e,"mergeProps"):()=>Fs}function Gs(){const e=Es();let t=null,n=null;return{clear(){t=null,n=null},notify(){e(()=>{let r=t;for(;r;)r.callback(),r=r.next})},get(){let r=[],o=t;for(;o;)r.push(o),o=o.next;return r},subscribe(r){let o=!0,i=n={callback:r,next:null,prev:n};return i.prev?i.prev.next=i:t=i,function(){!o||t===null||(o=!1,i.next?i.next.prev=i.prev:n=i.prev,i.prev?i.prev.next=i.next:t=i.next)}}}}const Mr={notify(){},get:()=>[]};function No(e,t){let n,r=Mr,o=0,i=!1;function s(b){f();const g=r.subscribe(b);let y=!1;return()=>{y||(y=!0,g(),u())}}function a(){r.notify()}function l(){m.onStateChange&&m.onStateChange()}function c(){return i}function f(){o++,n||(n=t?t.addNestedSub(l):e.subscribe(l),r=Gs())}function u(){o--,n&&o===0&&(n(),n=void 0,r.clear(),r=Mr)}function d(){i||(i=!0,f())}function p(){i&&(i=!1,u())}const m={addNestedSub:s,notifyNestedSubs:a,handleChangeWrapper:l,isSubscribed:c,trySubscribe:d,tryUnsubscribe:p,getListeners:()=>r};return m}const zs=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Mt=zs?v.useLayoutEffect:v.useEffect;function Br(e,t){return e===t?e!==0||t!==0||1/e===1/t:e!==e&&t!==t}function gn(e,t){if(Br(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;const n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(let o=0;o<n.length;o++)if(!Object.prototype.hasOwnProperty.call(t,n[o])||!Br(e[n[o]],t[n[o]]))return!1;return!0}const Hs=["reactReduxForwardedRef"];let To=Ps;const js=e=>{To=e},Us=[null,null];function Vs(e,t,n){Mt(()=>e(...t),n)}function qs(e,t,n,r,o,i){e.current=r,n.current=!1,o.current&&(o.current=null,i())}function Ys(e,t,n,r,o,i,s,a,l,c,f){if(!e)return()=>{};let u=!1,d=null;const p=()=>{if(u||!a.current)return;const b=t.getState();let g,y;try{g=r(b,o.current)}catch(h){y=h,d=h}y||(d=null),g===i.current?s.current||c():(i.current=g,l.current=g,s.current=!0,f())};return n.onStateChange=p,n.trySubscribe(),p(),()=>{if(u=!0,n.tryUnsubscribe(),n.onStateChange=null,d)throw d}}function Xs(e,t){return e===t}function Mo(e,t,n,{pure:r,areStatesEqual:o=Xs,areOwnPropsEqual:i=gn,areStatePropsEqual:s=gn,areMergedPropsEqual:a=gn,forwardRef:l=!1,context:c=Oo}={}){const f=c,u=$s(e),d=Ls(t),p=ks(n),m=!!e;return g=>{const y=g.displayName||g.name||"Component",h=`Connect(${y})`,D={shouldHandleStateChanges:m,displayName:h,wrappedComponentName:y,WrappedComponent:g,initMapStateToProps:u,initMapDispatchToProps:d,initMergeProps:p,areStatesEqual:o,areStatePropsEqual:s,areOwnPropsEqual:i,areMergedPropsEqual:a};function x(T){const[B,M,P]=v.useMemo(()=>{const{reactReduxForwardedRef:K}=T,Q=bo(T,Hs);return[T.context,K,Q]},[T]),k=v.useMemo(()=>B&&B.Consumer&&Ns.isContextConsumer(v.createElement(B.Consumer,null))?B:f,[B,f]),V=v.useContext(k),J=!!T.store&&!!T.store.getState&&!!T.store.dispatch,Oe=!!V&&!!V.store,ae=J?T.store:V.store,Me=Oe?V.getServerState:ae.getState,Se=v.useMemo(()=>Bs(ae.dispatch,D),[ae]),[we,tt]=v.useMemo(()=>{if(!m)return Us;const K=No(ae,J?void 0:V.subscription),Q=K.notifyNestedSubs.bind(K);return[K,Q]},[ae,J,V]),dn=v.useMemo(()=>J?V:Ye({},V,{subscription:we}),[J,V,we]),ve=v.useRef(),je=v.useRef(P),Pe=v.useRef(),nt=v.useRef(!1);v.useRef(!1);const rt=v.useRef(!1),Re=v.useRef();Mt(()=>(rt.current=!0,()=>{rt.current=!1}),[]);const ge=v.useMemo(()=>()=>Pe.current&&P===je.current?Pe.current:Se(ae.getState(),P),[ae,P]),Ot=v.useMemo(()=>Q=>we?Ys(m,ae,we,Se,je,ve,nt,rt,Pe,tt,Q):()=>{},[we]);Vs(qs,[je,ve,nt,P,Pe,tt]);let Ae;try{Ae=To(Ot,ge,Me?()=>Se(Me(),P):ge)}catch(K){throw Re.current&&(K.message+=`
The error may be correlated with this previous error:
${Re.current.stack}

`),K}Mt(()=>{Re.current=void 0,Pe.current=void 0,ve.current=Ae});const We=v.useMemo(()=>v.createElement(g,Ye({},Ae,{ref:M})),[M,g,Ae]);return v.useMemo(()=>m?v.createElement(k.Provider,{value:dn},We):We,[k,We,dn])}const w=v.memo(x);if(w.WrappedComponent=g,w.displayName=x.displayName=h,l){const B=v.forwardRef(function(P,k){return v.createElement(w,Ye({},P,{reactReduxForwardedRef:k}))});return B.displayName=h,B.WrappedComponent=g,Sr(B,g)}return Sr(w,g)}}function Ks({store:e,context:t,children:n,serverState:r,stabilityCheck:o="once",noopCheck:i="once"}){const s=v.useMemo(()=>{const c=No(e);return{store:e,subscription:c,getServerState:r?()=>r:void 0,stabilityCheck:o,noopCheck:i}},[e,r,o,i]),a=v.useMemo(()=>e.getState(),[e]);Mt(()=>{const{subscription:c}=s;return c.onStateChange=c.notifyNestedSubs,c.trySubscribe(),a!==e.getState()&&c.notifyNestedSubs(),()=>{c.tryUnsubscribe(),c.onStateChange=void 0}},[s,a]);const l=t||Oo;return v.createElement(l.Provider,{value:s},n)}js(ns.useSyncExternalStore);Is(Dt.unstable_batchedUpdates);function Js(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function Bo(e,t){var n=v.useState(function(){return{inputs:t,result:e()}})[0],r=v.useRef(!0),o=v.useRef(n),i=r.current||!!(t&&o.current.inputs&&Js(t,o.current.inputs)),s=i?o.current:{inputs:t,result:e()};return v.useEffect(function(){r.current=!1,o.current=s},[s]),s.result}function Qs(e,t){return Bo(function(){return e},t)}var W=Bo,N=Qs,Zs="Invariant failed";function ea(e,t){throw new Error(Zs)}var De=function(t){var n=t.top,r=t.right,o=t.bottom,i=t.left,s=r-i,a=o-n,l={top:n,right:r,bottom:o,left:i,width:s,height:a,x:i,y:n,center:{x:(r+i)/2,y:(o+n)/2}};return l},jn=function(t,n){return{top:t.top-n.top,left:t.left-n.left,bottom:t.bottom+n.bottom,right:t.right+n.right}},_r=function(t,n){return{top:t.top+n.top,left:t.left+n.left,bottom:t.bottom-n.bottom,right:t.right-n.right}},ta=function(t,n){return{top:t.top+n.y,left:t.left+n.x,bottom:t.bottom+n.y,right:t.right+n.x}},mn={top:0,right:0,bottom:0,left:0},Un=function(t){var n=t.borderBox,r=t.margin,o=r===void 0?mn:r,i=t.border,s=i===void 0?mn:i,a=t.padding,l=a===void 0?mn:a,c=De(jn(n,o)),f=De(_r(n,s)),u=De(_r(f,l));return{marginBox:c,borderBox:De(n),paddingBox:f,contentBox:u,margin:o,border:s,padding:l}},me=function(t){var n=t.slice(0,-2),r=t.slice(-2);if(r!=="px")return 0;var o=Number(n);return isNaN(o)&&ea(),o},na=function(){return{x:window.pageXOffset,y:window.pageYOffset}},Bt=function(t,n){var r=t.borderBox,o=t.border,i=t.margin,s=t.padding,a=ta(r,n);return Un({borderBox:a,border:o,margin:i,padding:s})},_t=function(t,n){return n===void 0&&(n=na()),Bt(t,n)},_o=function(t,n){var r={top:me(n.marginTop),right:me(n.marginRight),bottom:me(n.marginBottom),left:me(n.marginLeft)},o={top:me(n.paddingTop),right:me(n.paddingRight),bottom:me(n.paddingBottom),left:me(n.paddingLeft)},i={top:me(n.borderTopWidth),right:me(n.borderRightWidth),bottom:me(n.borderBottomWidth),left:me(n.borderLeftWidth)};return Un({borderBox:t,margin:r,padding:o,border:i})},Lo=function(t){var n=t.getBoundingClientRect(),r=window.getComputedStyle(t);return _o(n,r)},Lr=Number.isNaN||function(t){return typeof t=="number"&&t!==t};function ra(e,t){return!!(e===t||Lr(e)&&Lr(t))}function oa(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!ra(e[n],t[n]))return!1;return!0}function Z(e,t){t===void 0&&(t=oa);var n=null;function r(){for(var o=[],i=0;i<arguments.length;i++)o[i]=arguments[i];if(n&&n.lastThis===this&&t(o,n.lastArgs))return n.lastResult;var s=e.apply(this,o);return n={lastResult:s,lastArgs:o,lastThis:this},s}return r.clear=function(){n=null},r}var gt=function(t){var n=[],r=null,o=function(){for(var s=arguments.length,a=new Array(s),l=0;l<s;l++)a[l]=arguments[l];n=a,!r&&(r=requestAnimationFrame(function(){r=null,t.apply(void 0,n)}))};return o.cancel=function(){r&&(cancelAnimationFrame(r),r=null)},o};function $o(e,t){}$o.bind(null,"warn");$o.bind(null,"error");function Le(){}function ia(e,t){return{...e,...t}}function he(e,t,n){const r=t.map(o=>{const i=ia(n,o.options);return e.addEventListener(o.eventName,o.fn,i),function(){e.removeEventListener(o.eventName,o.fn,i)}});return function(){r.forEach(i=>{i()})}}const sa="Invariant failed";class Lt extends Error{}Lt.prototype.toString=function(){return this.message};function C(e,t){throw new Lt(sa)}class aa extends H.Component{constructor(...t){super(...t),this.callbacks=null,this.unbind=Le,this.onWindowError=n=>{const r=this.getCallbacks();r.isDragging()&&r.tryAbort(),n.error instanceof Lt&&n.preventDefault()},this.getCallbacks=()=>{if(!this.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return this.callbacks},this.setCallbacks=n=>{this.callbacks=n}}componentDidMount(){this.unbind=he(window,[{eventName:"error",fn:this.onWindowError}])}componentDidCatch(t){if(t instanceof Lt){this.setState({});return}throw t}componentWillUnmount(){this.unbind()}render(){return this.props.children(this.setCallbacks)}}const la=`
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
`,$t=e=>e+1,ca=e=>`
  You have lifted an item in position ${$t(e.source.index)}
`,Fo=(e,t)=>{const n=e.droppableId===t.droppableId,r=$t(e.index),o=$t(t.index);return n?`
      You have moved the item from position ${r}
      to position ${o}
    `:`
    You have moved the item from position ${r}
    in list ${e.droppableId}
    to list ${t.droppableId}
    in position ${o}
  `},Wo=(e,t,n)=>t.droppableId===n.droppableId?`
      The item ${e}
      has been combined with ${n.draggableId}`:`
      The item ${e}
      in list ${t.droppableId}
      has been combined with ${n.draggableId}
      in list ${n.droppableId}
    `,ua=e=>{const t=e.destination;if(t)return Fo(e.source,t);const n=e.combine;return n?Wo(e.draggableId,e.source,n):"You are over an area that cannot be dropped on"},$r=e=>`
  The item has returned to its starting position
  of ${$t(e.index)}
`,da=e=>{if(e.reason==="CANCEL")return`
      Movement cancelled.
      ${$r(e.source)}
    `;const t=e.destination,n=e.combine;return t?`
      You have dropped the item.
      ${Fo(e.source,t)}
    `:n?`
      You have dropped the item.
      ${Wo(e.draggableId,e.source,n)}
    `:`
    The item has been dropped while not over a drop area.
    ${$r(e.source)}
  `},fa={dragHandleUsageInstructions:la,onDragStart:ca,onDragUpdate:ua,onDragEnd:da};var Tt=fa;const ee={x:0,y:0},te=(e,t)=>({x:e.x+t.x,y:e.y+t.y}),ue=(e,t)=>({x:e.x-t.x,y:e.y-t.y}),$e=(e,t)=>e.x===t.x&&e.y===t.y,Qe=e=>({x:e.x!==0?-e.x:0,y:e.y!==0?-e.y:0}),ze=(e,t,n=0)=>e==="x"?{x:t,y:n}:{x:n,y:t},mt=(e,t)=>Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2),Fr=(e,t)=>Math.min(...t.map(n=>mt(e,n))),ko=e=>t=>({x:e(t.x),y:e(t.y)});var pa=(e,t)=>{const n=De({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return n.width<=0||n.height<=0?null:n};const wt=(e,t)=>({top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}),Wr=e=>[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}],ga={top:0,right:0,bottom:0,left:0},ma=(e,t)=>t?wt(e,t.scroll.diff.displacement):e,ha=(e,t,n)=>n&&n.increasedBy?{...e,[t.end]:e[t.end]+n.increasedBy[t.line]}:e,ba=(e,t)=>t&&t.shouldClipSubject?pa(t.pageMarginBox,e):De(e);var Xe=({page:e,withPlaceholder:t,axis:n,frame:r})=>{const o=ma(e.marginBox,r),i=ha(o,n,t),s=ba(i,r);return{page:e,withPlaceholder:t,active:s}},Vn=(e,t)=>{e.frame||C();const n=e.frame,r=ue(t,n.scroll.initial),o=Qe(r),i={...n,scroll:{initial:n.scroll.initial,current:t,diff:{value:r,displacement:o},max:n.scroll.max}},s=Xe({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:i});return{...e,frame:i,subject:s}};const Go=Z(e=>e.reduce((t,n)=>(t[n.descriptor.id]=n,t),{})),zo=Z(e=>e.reduce((t,n)=>(t[n.descriptor.id]=n,t),{})),Qt=Z(e=>Object.values(e)),ya=Z(e=>Object.values(e));var Ze=Z((e,t)=>ya(t).filter(r=>e===r.descriptor.droppableId).sort((r,o)=>r.descriptor.index-o.descriptor.index));function qn(e){return e.at&&e.at.type==="REORDER"?e.at.destination:null}function Zt(e){return e.at&&e.at.type==="COMBINE"?e.at.combine:null}var en=Z((e,t)=>t.filter(n=>n.descriptor.id!==e.descriptor.id)),va=({isMovingForward:e,draggable:t,destination:n,insideDestination:r,previousImpact:o})=>{if(!n.isCombineEnabled||!qn(o))return null;function s(p){const m={type:"COMBINE",combine:{draggableId:p,droppableId:n.descriptor.id}};return{...o,at:m}}const a=o.displaced.all,l=a.length?a[0]:null;if(e)return l?s(l):null;const c=en(t,r);if(!l){if(!c.length)return null;const p=c[c.length-1];return s(p.descriptor.id)}const f=c.findIndex(p=>p.descriptor.id===l);f===-1&&C();const u=f-1;if(u<0)return null;const d=c[u];return s(d.descriptor.id)},et=(e,t)=>e.descriptor.droppableId===t.descriptor.id;const Ho={point:ee,value:0},ht={invisible:{},visible:{},all:[]},xa={displaced:ht,displacedBy:Ho,at:null};var Da=xa,be=(e,t)=>n=>e<=n&&n<=t,jo=e=>{const t=be(e.top,e.bottom),n=be(e.left,e.right);return r=>{if(t(r.top)&&t(r.bottom)&&n(r.left)&&n(r.right))return!0;const i=t(r.top)||t(r.bottom),s=n(r.left)||n(r.right);if(i&&s)return!0;const l=r.top<e.top&&r.bottom>e.bottom,c=r.left<e.left&&r.right>e.right;return l&&c?!0:l&&s||c&&i}},Sa=e=>{const t=be(e.top,e.bottom),n=be(e.left,e.right);return r=>t(r.top)&&t(r.bottom)&&n(r.left)&&n(r.right)};const Yn={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},Uo={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"};var wa=e=>t=>{const n=be(t.top,t.bottom),r=be(t.left,t.right);return o=>e===Yn?n(o.top)&&n(o.bottom):r(o.left)&&r(o.right)};const Ca=(e,t)=>{const n=t.frame?t.frame.scroll.diff.displacement:ee;return wt(e,n)},Ia=(e,t,n)=>t.subject.active?n(t.subject.active)(e):!1,Ea=(e,t,n)=>n(t)(e),Xn=({target:e,destination:t,viewport:n,withDroppableDisplacement:r,isVisibleThroughFrameFn:o})=>{const i=r?Ca(e,t):e;return Ia(i,t,o)&&Ea(i,n,o)},Oa=e=>Xn({...e,isVisibleThroughFrameFn:jo}),Vo=e=>Xn({...e,isVisibleThroughFrameFn:Sa}),Pa=e=>Xn({...e,isVisibleThroughFrameFn:wa(e.destination.axis)}),Ra=(e,t,n)=>{if(typeof n=="boolean")return n;if(!t)return!0;const{invisible:r,visible:o}=t;if(r[e])return!1;const i=o[e];return i?i.shouldAnimate:!0};function Aa(e,t){const n=e.page.marginBox,r={top:t.point.y,right:0,bottom:0,left:t.point.x};return De(jn(n,r))}function bt({afterDragging:e,destination:t,displacedBy:n,viewport:r,forceShouldAnimate:o,last:i}){return e.reduce(function(a,l){const c=Aa(l,n),f=l.descriptor.id;if(a.all.push(f),!Oa({target:c,destination:t,viewport:r,withDroppableDisplacement:!0}))return a.invisible[l.descriptor.id]=!0,a;const d=Ra(f,i,o),p={draggableId:f,shouldAnimate:d};return a.visible[f]=p,a},{all:[],visible:{},invisible:{}})}function Na(e,t){if(!e.length)return 0;const n=e[e.length-1].descriptor.index;return t.inHomeList?n:n+1}function kr({insideDestination:e,inHomeList:t,displacedBy:n,destination:r}){const o=Na(e,{inHomeList:t});return{displaced:ht,displacedBy:n,at:{type:"REORDER",destination:{droppableId:r.descriptor.id,index:o}}}}function Ft({draggable:e,insideDestination:t,destination:n,viewport:r,displacedBy:o,last:i,index:s,forceShouldAnimate:a}){const l=et(e,n);if(s==null)return kr({insideDestination:t,inHomeList:l,displacedBy:o,destination:n});const c=t.find(m=>m.descriptor.index===s);if(!c)return kr({insideDestination:t,inHomeList:l,displacedBy:o,destination:n});const f=en(e,t),u=t.indexOf(c),d=f.slice(u);return{displaced:bt({afterDragging:d,destination:n,displacedBy:o,last:i,viewport:r.frame,forceShouldAnimate:a}),displacedBy:o,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:s}}}}function Fe(e,t){return!!t.effected[e]}var Ta=({isMovingForward:e,destination:t,draggables:n,combine:r,afterCritical:o})=>{if(!t.isCombineEnabled)return null;const i=r.draggableId,a=n[i].descriptor.index;return Fe(i,o)?e?a:a-1:e?a+1:a},Ma=({isMovingForward:e,isInHomeList:t,insideDestination:n,location:r})=>{if(!n.length)return null;const o=r.index,i=e?o+1:o-1,s=n[0].descriptor.index,a=n[n.length-1].descriptor.index,l=t?a:a+1;return i<s||i>l?null:i},Ba=({isMovingForward:e,isInHomeList:t,draggable:n,draggables:r,destination:o,insideDestination:i,previousImpact:s,viewport:a,afterCritical:l})=>{const c=s.at;if(c||C(),c.type==="REORDER"){const u=Ma({isMovingForward:e,isInHomeList:t,location:c.destination,insideDestination:i});return u==null?null:Ft({draggable:n,insideDestination:i,destination:o,viewport:a,last:s.displaced,displacedBy:s.displacedBy,index:u})}const f=Ta({isMovingForward:e,destination:o,displaced:s.displaced,draggables:r,combine:c.combine,afterCritical:l});return f==null?null:Ft({draggable:n,insideDestination:i,destination:o,viewport:a,last:s.displaced,displacedBy:s.displacedBy,index:f})},_a=({displaced:e,afterCritical:t,combineWith:n,displacedBy:r})=>{const o=!!(e.visible[n]||e.invisible[n]);return Fe(n,t)?o?ee:Qe(r.point):o?r.point:ee},La=({afterCritical:e,impact:t,draggables:n})=>{const r=Zt(t);r||C();const o=r.draggableId,i=n[o].page.borderBox.center,s=_a({displaced:t.displaced,afterCritical:e,combineWith:o,displacedBy:t.displacedBy});return te(i,s)};const qo=(e,t)=>t.margin[e.start]+t.borderBox[e.size]/2,$a=(e,t)=>t.margin[e.end]+t.borderBox[e.size]/2,Kn=(e,t,n)=>t[e.crossAxisStart]+n.margin[e.crossAxisStart]+n.borderBox[e.crossAxisSize]/2,Gr=({axis:e,moveRelativeTo:t,isMoving:n})=>ze(e.line,t.marginBox[e.end]+qo(e,n),Kn(e,t.marginBox,n)),zr=({axis:e,moveRelativeTo:t,isMoving:n})=>ze(e.line,t.marginBox[e.start]-$a(e,n),Kn(e,t.marginBox,n)),Fa=({axis:e,moveInto:t,isMoving:n})=>ze(e.line,t.contentBox[e.start]+qo(e,n),Kn(e,t.contentBox,n));var Wa=({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:o})=>{const i=Ze(r.descriptor.id,n),s=t.page,a=r.axis;if(!i.length)return Fa({axis:a,moveInto:r.page,isMoving:s});const{displaced:l,displacedBy:c}=e,f=l.all[0];if(f){const d=n[f];if(Fe(f,o))return zr({axis:a,moveRelativeTo:d.page,isMoving:s});const p=Bt(d.page,c.point);return zr({axis:a,moveRelativeTo:p,isMoving:s})}const u=i[i.length-1];if(u.descriptor.id===t.descriptor.id)return s.borderBox.center;if(Fe(u.descriptor.id,o)){const d=Bt(u.page,Qe(o.displacedBy.point));return Gr({axis:a,moveRelativeTo:d,isMoving:s})}return Gr({axis:a,moveRelativeTo:u.page,isMoving:s})},An=(e,t)=>{const n=e.frame;return n?te(t,n.scroll.diff.displacement):t};const ka=({impact:e,draggable:t,droppable:n,draggables:r,afterCritical:o})=>{const i=t.page.borderBox.center,s=e.at;return!n||!s?i:s.type==="REORDER"?Wa({impact:e,draggable:t,draggables:r,droppable:n,afterCritical:o}):La({impact:e,draggables:r,afterCritical:o})};var tn=e=>{const t=ka(e),n=e.droppable;return n?An(n,t):t},Yo=(e,t)=>{const n=ue(t,e.scroll.initial),r=Qe(n);return{frame:De({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:n,displacement:r}}}};function Hr(e,t){return e.map(n=>t[n])}function Ga(e,t){for(let n=0;n<t.length;n++){const r=t[n].visible[e];if(r)return r}return null}var za=({impact:e,viewport:t,destination:n,draggables:r,maxScrollChange:o})=>{const i=Yo(t,te(t.scroll.current,o)),s=n.frame?Vn(n,te(n.frame.scroll.current,o)):n,a=e.displaced,l=bt({afterDragging:Hr(a.all,r),destination:n,displacedBy:e.displacedBy,viewport:i.frame,last:a,forceShouldAnimate:!1}),c=bt({afterDragging:Hr(a.all,r),destination:s,displacedBy:e.displacedBy,viewport:t.frame,last:a,forceShouldAnimate:!1}),f={},u={},d=[a,l,c];return a.all.forEach(m=>{const b=Ga(m,d);if(b){u[m]=b;return}f[m]=!0}),{...e,displaced:{all:a.all,invisible:f,visible:u}}},Ha=(e,t)=>te(e.scroll.diff.displacement,t),Jn=({pageBorderBoxCenter:e,draggable:t,viewport:n})=>{const r=Ha(n,e),o=ue(r,t.page.borderBox.center);return te(t.client.borderBox.center,o)},Xo=({draggable:e,destination:t,newPageBorderBoxCenter:n,viewport:r,withDroppableDisplacement:o,onlyOnMainAxis:i=!1})=>{const s=ue(n,e.page.borderBox.center),l={target:wt(e.page.borderBox,s),destination:t,withDroppableDisplacement:o,viewport:r};return i?Pa(l):Vo(l)},ja=({isMovingForward:e,draggable:t,destination:n,draggables:r,previousImpact:o,viewport:i,previousPageBorderBoxCenter:s,previousClientSelection:a,afterCritical:l})=>{if(!n.isEnabled)return null;const c=Ze(n.descriptor.id,r),f=et(t,n),u=va({isMovingForward:e,draggable:t,destination:n,insideDestination:c,previousImpact:o})||Ba({isMovingForward:e,isInHomeList:f,draggable:t,draggables:r,destination:n,insideDestination:c,previousImpact:o,viewport:i,afterCritical:l});if(!u)return null;const d=tn({impact:u,draggable:t,droppable:n,draggables:r,afterCritical:l});if(Xo({draggable:t,destination:n,newPageBorderBoxCenter:d,viewport:i.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0}))return{clientSelection:Jn({pageBorderBoxCenter:d,draggable:t,viewport:i}),impact:u,scrollJumpRequest:null};const m=ue(d,s),b=za({impact:u,viewport:i,destination:n,draggables:r,maxScrollChange:m});return{clientSelection:a,impact:b,scrollJumpRequest:m}};const oe=e=>{const t=e.subject.active;return t||C(),t};var Ua=({isMovingForward:e,pageBorderBoxCenter:t,source:n,droppables:r,viewport:o})=>{const i=n.subject.active;if(!i)return null;const s=n.axis,a=be(i[s.start],i[s.end]),l=Qt(r).filter(f=>f!==n).filter(f=>f.isEnabled).filter(f=>!!f.subject.active).filter(f=>jo(o.frame)(oe(f))).filter(f=>{const u=oe(f);return e?i[s.crossAxisEnd]<u[s.crossAxisEnd]:u[s.crossAxisStart]<i[s.crossAxisStart]}).filter(f=>{const u=oe(f),d=be(u[s.start],u[s.end]);return a(u[s.start])||a(u[s.end])||d(i[s.start])||d(i[s.end])}).sort((f,u)=>{const d=oe(f)[s.crossAxisStart],p=oe(u)[s.crossAxisStart];return e?d-p:p-d}).filter((f,u,d)=>oe(f)[s.crossAxisStart]===oe(d[0])[s.crossAxisStart]);if(!l.length)return null;if(l.length===1)return l[0];const c=l.filter(f=>be(oe(f)[s.start],oe(f)[s.end])(t[s.line]));return c.length===1?c[0]:c.length>1?c.sort((f,u)=>oe(f)[s.start]-oe(u)[s.start])[0]:l.sort((f,u)=>{const d=Fr(t,Wr(oe(f))),p=Fr(t,Wr(oe(u)));return d!==p?d-p:oe(f)[s.start]-oe(u)[s.start]})[0]};const jr=(e,t)=>{const n=e.page.borderBox.center;return Fe(e.descriptor.id,t)?ue(n,t.displacedBy.point):n},Va=(e,t)=>{const n=e.page.borderBox;return Fe(e.descriptor.id,t)?wt(n,Qe(t.displacedBy.point)):n};var qa=({pageBorderBoxCenter:e,viewport:t,destination:n,insideDestination:r,afterCritical:o})=>r.filter(s=>Vo({target:Va(s,o),destination:n,viewport:t.frame,withDroppableDisplacement:!0})).sort((s,a)=>{const l=mt(e,An(n,jr(s,o))),c=mt(e,An(n,jr(a,o)));return l<c?-1:c<l?1:s.descriptor.index-a.descriptor.index})[0]||null,Ct=Z(function(t,n){const r=n[t.line];return{value:r,point:ze(t.line,r)}});const Ya=(e,t,n)=>{const r=e.axis;if(e.descriptor.mode==="virtual")return ze(r.line,t[r.line]);const o=e.subject.page.contentBox[r.size],l=Ze(e.descriptor.id,n).reduce((c,f)=>c+f.client.marginBox[r.size],0)+t[r.line]-o;return l<=0?null:ze(r.line,l)},Ko=(e,t)=>({...e,scroll:{...e.scroll,max:t}}),Jo=(e,t,n)=>{const r=e.frame;et(t,e)&&C(),e.subject.withPlaceholder&&C();const o=Ct(e.axis,t.displaceBy).point,i=Ya(e,o,n),s={placeholderSize:o,increasedBy:i,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};if(!r){const f=Xe({page:e.subject.page,withPlaceholder:s,axis:e.axis,frame:e.frame});return{...e,subject:f}}const a=i?te(r.scroll.max,i):r.scroll.max,l=Ko(r,a),c=Xe({page:e.subject.page,withPlaceholder:s,axis:e.axis,frame:l});return{...e,subject:c,frame:l}},Xa=e=>{const t=e.subject.withPlaceholder;t||C();const n=e.frame;if(!n){const s=Xe({page:e.subject.page,axis:e.axis,frame:null,withPlaceholder:null});return{...e,subject:s}}const r=t.oldFrameMaxScroll;r||C();const o=Ko(n,r),i=Xe({page:e.subject.page,axis:e.axis,frame:o,withPlaceholder:null});return{...e,subject:i,frame:o}};var Ka=({previousPageBorderBoxCenter:e,moveRelativeTo:t,insideDestination:n,draggable:r,draggables:o,destination:i,viewport:s,afterCritical:a})=>{if(!t){if(n.length)return null;const u={displaced:ht,displacedBy:Ho,at:{type:"REORDER",destination:{droppableId:i.descriptor.id,index:0}}},d=tn({impact:u,draggable:r,droppable:i,draggables:o,afterCritical:a}),p=et(r,i)?i:Jo(i,r,o);return Xo({draggable:r,destination:p,newPageBorderBoxCenter:d,viewport:s.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?u:null}const l=e[i.axis.line]<=t.page.borderBox.center[i.axis.line],c=(()=>{const u=t.descriptor.index;return t.descriptor.id===r.descriptor.id||l?u:u+1})(),f=Ct(i.axis,r.displaceBy);return Ft({draggable:r,insideDestination:n,destination:i,viewport:s,displacedBy:f,last:ht,index:c})},Ja=({isMovingForward:e,previousPageBorderBoxCenter:t,draggable:n,isOver:r,draggables:o,droppables:i,viewport:s,afterCritical:a})=>{const l=Ua({isMovingForward:e,pageBorderBoxCenter:t,source:r,droppables:i,viewport:s});if(!l)return null;const c=Ze(l.descriptor.id,o),f=qa({pageBorderBoxCenter:t,viewport:s,destination:l,insideDestination:c,afterCritical:a}),u=Ka({previousPageBorderBoxCenter:t,destination:l,draggable:n,draggables:o,moveRelativeTo:f,insideDestination:c,viewport:s,afterCritical:a});if(!u)return null;const d=tn({impact:u,draggable:n,droppable:l,draggables:o,afterCritical:a});return{clientSelection:Jn({pageBorderBoxCenter:d,draggable:n,viewport:s}),impact:u,scrollJumpRequest:null}},de=e=>{const t=e.at;return t?t.type==="REORDER"?t.destination.droppableId:t.combine.droppableId:null};const Qa=(e,t)=>{const n=de(e);return n?t[n]:null};var Za=({state:e,type:t})=>{const n=Qa(e.impact,e.dimensions.droppables),r=!!n,o=e.dimensions.droppables[e.critical.droppable.id],i=n||o,s=i.axis.direction,a=s==="vertical"&&(t==="MOVE_UP"||t==="MOVE_DOWN")||s==="horizontal"&&(t==="MOVE_LEFT"||t==="MOVE_RIGHT");if(a&&!r)return null;const l=t==="MOVE_DOWN"||t==="MOVE_RIGHT",c=e.dimensions.draggables[e.critical.draggable.id],f=e.current.page.borderBoxCenter,{draggables:u,droppables:d}=e.dimensions;return a?ja({isMovingForward:l,previousPageBorderBoxCenter:f,draggable:c,destination:i,draggables:u,viewport:e.viewport,previousClientSelection:e.current.client.selection,previousImpact:e.impact,afterCritical:e.afterCritical}):Ja({isMovingForward:l,previousPageBorderBoxCenter:f,draggable:c,isOver:i,draggables:u,droppables:d,viewport:e.viewport,afterCritical:e.afterCritical})};function Ge(e){return e.phase==="DRAGGING"||e.phase==="COLLECTING"}function Qo(e){const t=be(e.top,e.bottom),n=be(e.left,e.right);return function(o){return t(o.y)&&n(o.x)}}function el(e,t){return e.left<t.right&&e.right>t.left&&e.top<t.bottom&&e.bottom>t.top}function tl({pageBorderBox:e,draggable:t,candidates:n}){const r=t.page.borderBox.center,o=n.map(i=>{const s=i.axis,a=ze(i.axis.line,e.center[s.line],i.page.borderBox.center[s.crossAxisLine]);return{id:i.descriptor.id,distance:mt(r,a)}}).sort((i,s)=>s.distance-i.distance);return o[0]?o[0].id:null}function nl({pageBorderBox:e,draggable:t,droppables:n}){const r=Qt(n).filter(o=>{if(!o.isEnabled)return!1;const i=o.subject.active;if(!i||!el(e,i))return!1;if(Qo(i)(e.center))return!0;const s=o.axis,a=i.center[s.crossAxisLine],l=e[s.crossAxisStart],c=e[s.crossAxisEnd],f=be(i[s.crossAxisStart],i[s.crossAxisEnd]),u=f(l),d=f(c);return!u&&!d?!0:u?l<a:c>a});return r.length?r.length===1?r[0].descriptor.id:tl({pageBorderBox:e,draggable:t,candidates:r}):null}const Zo=(e,t)=>De(wt(e,t));var rl=(e,t)=>{const n=e.frame;return n?Zo(t,n.scroll.diff.value):t};function ei({displaced:e,id:t}){return!!(e.visible[t]||e.invisible[t])}function ol({draggable:e,closest:t,inHomeList:n}){return t?n&&t.descriptor.index>e.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}var il=({pageBorderBoxWithDroppableScroll:e,draggable:t,destination:n,insideDestination:r,last:o,viewport:i,afterCritical:s})=>{const a=n.axis,l=Ct(n.axis,t.displaceBy),c=l.value,f=e[a.start],u=e[a.end],p=en(t,r).find(b=>{const g=b.descriptor.id,y=b.page.borderBox.center[a.line],h=Fe(g,s),D=ei({displaced:o,id:g});return h?D?u<=y:f<y-c:D?u<=y+c:f<y})||null,m=ol({draggable:t,closest:p,inHomeList:et(t,n)});return Ft({draggable:t,insideDestination:r,destination:n,viewport:i,last:o,displacedBy:l,index:m})};const sl=4;var al=({draggable:e,pageBorderBoxWithDroppableScroll:t,previousImpact:n,destination:r,insideDestination:o,afterCritical:i})=>{if(!r.isCombineEnabled)return null;const s=r.axis,a=Ct(r.axis,e.displaceBy),l=a.value,c=t[s.start],f=t[s.end],d=en(e,o).find(m=>{const b=m.descriptor.id,g=m.page.borderBox,h=g[s.size]/sl,D=Fe(b,i),x=ei({displaced:n.displaced,id:b});return D?x?f>g[s.start]+h&&f<g[s.end]-h:c>g[s.start]-l+h&&c<g[s.end]-l-h:x?f>g[s.start]+l+h&&f<g[s.end]+l-h:c>g[s.start]+h&&c<g[s.end]-h});return d?{displacedBy:a,displaced:n.displaced,at:{type:"COMBINE",combine:{draggableId:d.descriptor.id,droppableId:r.descriptor.id}}}:null},ti=({pageOffset:e,draggable:t,draggables:n,droppables:r,previousImpact:o,viewport:i,afterCritical:s})=>{const a=Zo(t.page.borderBox,e),l=nl({pageBorderBox:a,draggable:t,droppables:r});if(!l)return Da;const c=r[l],f=Ze(c.descriptor.id,n),u=rl(c,a);return al({pageBorderBoxWithDroppableScroll:u,draggable:t,previousImpact:o,destination:c,insideDestination:f,afterCritical:s})||il({pageBorderBoxWithDroppableScroll:u,draggable:t,destination:c,insideDestination:f,last:o.displaced,viewport:i,afterCritical:s})},Qn=(e,t)=>({...e,[t.descriptor.id]:t});const ll=({previousImpact:e,impact:t,droppables:n})=>{const r=de(e),o=de(t);if(!r||r===o)return n;const i=n[r];if(!i.subject.withPlaceholder)return n;const s=Xa(i);return Qn(n,s)};var cl=({draggable:e,draggables:t,droppables:n,previousImpact:r,impact:o})=>{const i=ll({previousImpact:r,impact:o,droppables:n}),s=de(o);if(!s)return i;const a=n[s];if(et(e,a)||a.subject.withPlaceholder)return i;const l=Jo(a,e,t);return Qn(i,l)},ft=({state:e,clientSelection:t,dimensions:n,viewport:r,impact:o,scrollJumpRequest:i})=>{const s=r||e.viewport,a=n||e.dimensions,l=t||e.current.client.selection,c=ue(l,e.initial.client.selection),f={offset:c,selection:l,borderBoxCenter:te(e.initial.client.borderBoxCenter,c)},u={selection:te(f.selection,s.scroll.current),borderBoxCenter:te(f.borderBoxCenter,s.scroll.current),offset:te(f.offset,s.scroll.diff.value)},d={client:f,page:u};if(e.phase==="COLLECTING")return{...e,dimensions:a,viewport:s,current:d};const p=a.draggables[e.critical.draggable.id],m=o||ti({pageOffset:u.offset,draggable:p,draggables:a.draggables,droppables:a.droppables,previousImpact:e.impact,viewport:s,afterCritical:e.afterCritical}),b=cl({draggable:p,impact:m,previousImpact:e.impact,draggables:a.draggables,droppables:a.droppables});return{...e,current:d,dimensions:{draggables:a.draggables,droppables:b},impact:m,viewport:s,scrollJumpRequest:i||null,forceShouldAnimate:i?!1:null}};function ul(e,t){return e.map(n=>t[n])}var ni=({impact:e,viewport:t,draggables:n,destination:r,forceShouldAnimate:o})=>{const i=e.displaced,s=ul(i.all,n),a=bt({afterDragging:s,destination:r,displacedBy:e.displacedBy,viewport:t.frame,forceShouldAnimate:o,last:i});return{...e,displaced:a}},ri=({impact:e,draggable:t,droppable:n,draggables:r,viewport:o,afterCritical:i})=>{const s=tn({impact:e,draggable:t,draggables:r,droppable:n,afterCritical:i});return Jn({pageBorderBoxCenter:s,draggable:t,viewport:o})},oi=({state:e,dimensions:t,viewport:n})=>{e.movementMode!=="SNAP"&&C();const r=e.impact,o=n||e.viewport,i=t||e.dimensions,{draggables:s,droppables:a}=i,l=s[e.critical.draggable.id],c=de(r);c||C();const f=a[c],u=ni({impact:r,viewport:o,destination:f,draggables:s}),d=ri({impact:u,draggable:l,droppable:f,draggables:s,viewport:o,afterCritical:e.afterCritical});return ft({impact:u,clientSelection:d,state:e,dimensions:i,viewport:o})},dl=e=>({index:e.index,droppableId:e.droppableId}),ii=({draggable:e,home:t,draggables:n,viewport:r})=>{const o=Ct(t.axis,e.displaceBy),i=Ze(t.descriptor.id,n),s=i.indexOf(e);s===-1&&C();const a=i.slice(s+1),l=a.reduce((d,p)=>(d[p.descriptor.id]=!0,d),{}),c={inVirtualList:t.descriptor.mode==="virtual",displacedBy:o,effected:l};return{impact:{displaced:bt({afterDragging:a,destination:t,displacedBy:o,last:null,viewport:r.frame,forceShouldAnimate:!1}),displacedBy:o,at:{type:"REORDER",destination:dl(e.descriptor)}},afterCritical:c}},fl=(e,t)=>({draggables:e.draggables,droppables:Qn(e.droppables,t)}),pl=({draggable:e,offset:t,initialWindowScroll:n})=>{const r=Bt(e.client,t),o=_t(r,n);return{...e,placeholder:{...e.placeholder,client:r},client:r,page:o}},gl=e=>{const t=e.frame;return t||C(),t},ml=({additions:e,updatedDroppables:t,viewport:n})=>{const r=n.scroll.diff.value;return e.map(o=>{const i=o.descriptor.droppableId,s=t[i],l=gl(s).scroll.diff.value,c=te(r,l);return pl({draggable:o,offset:c,initialWindowScroll:n.scroll.initial})})},hl=({state:e,published:t})=>{const n=t.modified.map(y=>{const h=e.dimensions.droppables[y.droppableId];return Vn(h,y.scroll)}),r={...e.dimensions.droppables,...Go(n)},o=zo(ml({additions:t.additions,updatedDroppables:r,viewport:e.viewport})),i={...e.dimensions.draggables,...o};t.removals.forEach(y=>{delete i[y]});const s={droppables:r,draggables:i},a=de(e.impact),l=a?s.droppables[a]:null,c=s.draggables[e.critical.draggable.id],f=s.droppables[e.critical.droppable.id],{impact:u,afterCritical:d}=ii({draggable:c,home:f,draggables:i,viewport:e.viewport}),p=l&&l.isCombineEnabled?e.impact:u,m=ti({pageOffset:e.current.page.offset,draggable:s.draggables[e.critical.draggable.id],draggables:s.draggables,droppables:s.droppables,previousImpact:p,viewport:e.viewport,afterCritical:d}),b={...e,phase:"DRAGGING",impact:m,onLiftImpact:u,dimensions:s,afterCritical:d,forceShouldAnimate:!1};return e.phase==="COLLECTING"?b:{...b,phase:"DROP_PENDING",reason:e.reason,isWaiting:!1}};const Nn=e=>e.movementMode==="SNAP",hn=(e,t,n)=>{const r=fl(e.dimensions,t);return!Nn(e)||n?ft({state:e,dimensions:r}):oi({state:e,dimensions:r})};function bn(e){return e.isDragging&&e.movementMode==="SNAP"?{...e,scrollJumpRequest:null}:e}const Ur={phase:"IDLE",completed:null,shouldFlush:!1};var bl=(e=Ur,t)=>{if(t.type==="FLUSH")return{...Ur,shouldFlush:!0};if(t.type==="INITIAL_PUBLISH"){e.phase!=="IDLE"&&C();const{critical:n,clientSelection:r,viewport:o,dimensions:i,movementMode:s}=t.payload,a=i.draggables[n.draggable.id],l=i.droppables[n.droppable.id],c={selection:r,borderBoxCenter:a.client.borderBox.center,offset:ee},f={client:c,page:{selection:te(c.selection,o.scroll.initial),borderBoxCenter:te(c.selection,o.scroll.initial),offset:te(c.selection,o.scroll.diff.value)}},u=Qt(i.droppables).every(b=>!b.isFixedOnPage),{impact:d,afterCritical:p}=ii({draggable:a,home:l,draggables:i.draggables,viewport:o});return{phase:"DRAGGING",isDragging:!0,critical:n,movementMode:s,dimensions:i,initial:f,current:f,isWindowScrollAllowed:u,impact:d,afterCritical:p,onLiftImpact:d,viewport:o,scrollJumpRequest:null,forceShouldAnimate:null}}if(t.type==="COLLECTION_STARTING")return e.phase==="COLLECTING"||e.phase==="DROP_PENDING"?e:(e.phase!=="DRAGGING"&&C(),{...e,phase:"COLLECTING"});if(t.type==="PUBLISH_WHILE_DRAGGING")return e.phase==="COLLECTING"||e.phase==="DROP_PENDING"||C(),hl({state:e,published:t.payload});if(t.type==="MOVE"){if(e.phase==="DROP_PENDING")return e;Ge(e)||C();const{client:n}=t.payload;return $e(n,e.current.client.selection)?e:ft({state:e,clientSelection:n,impact:Nn(e)?e.impact:null})}if(t.type==="UPDATE_DROPPABLE_SCROLL"){if(e.phase==="DROP_PENDING"||e.phase==="COLLECTING")return bn(e);Ge(e)||C();const{id:n,newScroll:r}=t.payload,o=e.dimensions.droppables[n];if(!o)return e;const i=Vn(o,r);return hn(e,i,!1)}if(t.type==="UPDATE_DROPPABLE_IS_ENABLED"){if(e.phase==="DROP_PENDING")return e;Ge(e)||C();const{id:n,isEnabled:r}=t.payload,o=e.dimensions.droppables[n];o||C(),o.isEnabled===r&&C();const i={...o,isEnabled:r};return hn(e,i,!0)}if(t.type==="UPDATE_DROPPABLE_IS_COMBINE_ENABLED"){if(e.phase==="DROP_PENDING")return e;Ge(e)||C();const{id:n,isCombineEnabled:r}=t.payload,o=e.dimensions.droppables[n];o||C(),o.isCombineEnabled===r&&C();const i={...o,isCombineEnabled:r};return hn(e,i,!0)}if(t.type==="MOVE_BY_WINDOW_SCROLL"){if(e.phase==="DROP_PENDING"||e.phase==="DROP_ANIMATING")return e;Ge(e)||C(),e.isWindowScrollAllowed||C();const n=t.payload.newScroll;if($e(e.viewport.scroll.current,n))return bn(e);const r=Yo(e.viewport,n);return Nn(e)?oi({state:e,viewport:r}):ft({state:e,viewport:r})}if(t.type==="UPDATE_VIEWPORT_MAX_SCROLL"){if(!Ge(e))return e;const n=t.payload.maxScroll;if($e(n,e.viewport.scroll.max))return e;const r={...e.viewport,scroll:{...e.viewport.scroll,max:n}};return{...e,viewport:r}}if(t.type==="MOVE_UP"||t.type==="MOVE_DOWN"||t.type==="MOVE_LEFT"||t.type==="MOVE_RIGHT"){if(e.phase==="COLLECTING"||e.phase==="DROP_PENDING")return e;e.phase!=="DRAGGING"&&C();const n=Za({state:e,type:t.type});return n?ft({state:e,impact:n.impact,clientSelection:n.clientSelection,scrollJumpRequest:n.scrollJumpRequest}):e}if(t.type==="DROP_PENDING"){const n=t.payload.reason;return e.phase!=="COLLECTING"&&C(),{...e,phase:"DROP_PENDING",isWaiting:!0,reason:n}}if(t.type==="DROP_ANIMATE"){const{completed:n,dropDuration:r,newHomeClientOffset:o}=t.payload;return e.phase==="DRAGGING"||e.phase==="DROP_PENDING"||C(),{phase:"DROP_ANIMATING",completed:n,dropDuration:r,newHomeClientOffset:o,dimensions:e.dimensions}}if(t.type==="DROP_COMPLETE"){const{completed:n}=t.payload;return{phase:"IDLE",completed:n,shouldFlush:!1}}return e};const yl=e=>({type:"BEFORE_INITIAL_CAPTURE",payload:e}),vl=e=>({type:"LIFT",payload:e}),xl=e=>({type:"INITIAL_PUBLISH",payload:e}),Dl=e=>({type:"PUBLISH_WHILE_DRAGGING",payload:e}),Sl=()=>({type:"COLLECTION_STARTING",payload:null}),wl=e=>({type:"UPDATE_DROPPABLE_SCROLL",payload:e}),Cl=e=>({type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}),Il=e=>({type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}),si=e=>({type:"MOVE",payload:e}),El=e=>({type:"MOVE_BY_WINDOW_SCROLL",payload:e}),Ol=e=>({type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e}),Pl=()=>({type:"MOVE_UP",payload:null}),Rl=()=>({type:"MOVE_DOWN",payload:null}),Al=()=>({type:"MOVE_RIGHT",payload:null}),Nl=()=>({type:"MOVE_LEFT",payload:null}),Zn=()=>({type:"FLUSH",payload:null}),Tl=e=>({type:"DROP_ANIMATE",payload:e}),er=e=>({type:"DROP_COMPLETE",payload:e}),ai=e=>({type:"DROP",payload:e}),Ml=e=>({type:"DROP_PENDING",payload:e}),li=()=>({type:"DROP_ANIMATION_FINISHED",payload:null});var Bl=e=>({getState:t,dispatch:n})=>r=>o=>{if(o.type!=="LIFT"){r(o);return}const{id:i,clientSelection:s,movementMode:a}=o.payload,l=t();l.phase==="DROP_ANIMATING"&&n(er({completed:l.completed})),t().phase!=="IDLE"&&C(),n(Zn()),n(yl({draggableId:i,movementMode:a}));const f={draggableId:i,scrollOptions:{shouldPublishImmediately:a==="SNAP"}},{critical:u,dimensions:d,viewport:p}=e.startPublishing(f);n(xl({critical:u,dimensions:d,clientSelection:s,movementMode:a,viewport:p}))},_l=e=>()=>t=>n=>{n.type==="INITIAL_PUBLISH"&&e.dragging(),n.type==="DROP_ANIMATE"&&e.dropping(n.payload.completed.result.reason),(n.type==="FLUSH"||n.type==="DROP_COMPLETE")&&e.resting(),t(n)};const tr={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},yt={opacity:{drop:0,combining:.7},scale:{drop:.75}},ci={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},ke=`${ci.outOfTheWay}s ${tr.outOfTheWay}`,pt={fluid:`opacity ${ke}`,snap:`transform ${ke}, opacity ${ke}`,drop:e=>{const t=`${e}s ${tr.drop}`;return`transform ${t}, opacity ${t}`},outOfTheWay:`transform ${ke}`,placeholder:`height ${ke}, width ${ke}, margin ${ke}`},Vr=e=>$e(e,ee)?void 0:`translate(${e.x}px, ${e.y}px)`,Tn={moveTo:Vr,drop:(e,t)=>{const n=Vr(e);if(n)return t?`${n} scale(${yt.scale.drop})`:n}},{minDropTime:Mn,maxDropTime:ui}=ci,Ll=ui-Mn,qr=1500,$l=.6;var Fl=({current:e,destination:t,reason:n})=>{const r=mt(e,t);if(r<=0)return Mn;if(r>=qr)return ui;const o=r/qr,i=Mn+Ll*o,s=n==="CANCEL"?i*$l:i;return Number(s.toFixed(2))},Wl=({impact:e,draggable:t,dimensions:n,viewport:r,afterCritical:o})=>{const{draggables:i,droppables:s}=n,a=de(e),l=a?s[a]:null,c=s[t.descriptor.droppableId],f=ri({impact:e,draggable:t,draggables:i,afterCritical:o,droppable:l||c,viewport:r});return ue(f,t.client.borderBox.center)},kl=({draggables:e,reason:t,lastImpact:n,home:r,viewport:o,onLiftImpact:i})=>!n.at||t!=="DROP"?{impact:ni({draggables:e,impact:i,destination:r,viewport:o,forceShouldAnimate:!0}),didDropInsideDroppable:!1}:n.at.type==="REORDER"?{impact:n,didDropInsideDroppable:!0}:{impact:{...n,displaced:ht},didDropInsideDroppable:!0};const Gl=({getState:e,dispatch:t})=>n=>r=>{if(r.type!=="DROP"){n(r);return}const o=e(),i=r.payload.reason;if(o.phase==="COLLECTING"){t(Ml({reason:i}));return}if(o.phase==="IDLE")return;o.phase==="DROP_PENDING"&&o.isWaiting&&C(),o.phase==="DRAGGING"||o.phase==="DROP_PENDING"||C();const a=o.critical,l=o.dimensions,c=l.draggables[o.critical.draggable.id],{impact:f,didDropInsideDroppable:u}=kl({reason:i,lastImpact:o.impact,afterCritical:o.afterCritical,onLiftImpact:o.onLiftImpact,home:o.dimensions.droppables[o.critical.droppable.id],viewport:o.viewport,draggables:o.dimensions.draggables}),d=u?qn(f):null,p=u?Zt(f):null,m={index:a.draggable.index,droppableId:a.droppable.id},b={draggableId:c.descriptor.id,type:c.descriptor.type,source:m,reason:i,mode:o.movementMode,destination:d,combine:p},g=Wl({impact:f,draggable:c,dimensions:l,viewport:o.viewport,afterCritical:o.afterCritical}),y={critical:o.critical,afterCritical:o.afterCritical,result:b,impact:f};if(!(!$e(o.current.client.offset,g)||!!b.combine)){t(er({completed:y}));return}const D=Fl({current:o.current.client.offset,destination:g,reason:i});t(Tl({newHomeClientOffset:g,dropDuration:D,completed:y}))};var zl=Gl,di=()=>({x:window.pageXOffset,y:window.pageYOffset});function Hl(e){return{eventName:"scroll",options:{passive:!0,capture:!1},fn:t=>{t.target!==window&&t.target!==window.document||e()}}}function jl({onWindowScroll:e}){function t(){e(di())}const n=gt(t),r=Hl(n);let o=Le;function i(){return o!==Le}function s(){i()&&C(),o=he(window,[r])}function a(){i()||C(),n.cancel(),o(),o=Le}return{start:s,stop:a,isActive:i}}const Ul=e=>e.type==="DROP_COMPLETE"||e.type==="DROP_ANIMATE"||e.type==="FLUSH",Vl=e=>{const t=jl({onWindowScroll:n=>{e.dispatch(El({newScroll:n}))}});return n=>r=>{!t.isActive()&&r.type==="INITIAL_PUBLISH"&&t.start(),t.isActive()&&Ul(r)&&t.stop(),n(r)}};var ql=Vl,Yl=e=>{let t=!1,n=!1;const r=setTimeout(()=>{n=!0}),o=i=>{t||n||(t=!0,e(i),clearTimeout(r))};return o.wasCalled=()=>t,o},Xl=()=>{const e=[],t=o=>{const i=e.findIndex(a=>a.timerId===o);i===-1&&C();const[s]=e.splice(i,1);s.callback()};return{add:o=>{const i=setTimeout(()=>t(i)),s={timerId:i,callback:o};e.push(s)},flush:()=>{if(!e.length)return;const o=[...e];e.length=0,o.forEach(i=>{clearTimeout(i.timerId),i.callback()})}}};const Kl=(e,t)=>e==null&&t==null?!0:e==null||t==null?!1:e.droppableId===t.droppableId&&e.index===t.index,Jl=(e,t)=>e==null&&t==null?!0:e==null||t==null?!1:e.draggableId===t.draggableId&&e.droppableId===t.droppableId,Ql=(e,t)=>{if(e===t)return!0;const n=e.draggable.id===t.draggable.id&&e.draggable.droppableId===t.draggable.droppableId&&e.draggable.type===t.draggable.type&&e.draggable.index===t.draggable.index,r=e.droppable.id===t.droppable.id&&e.droppable.type===t.droppable.type;return n&&r},at=(e,t)=>{t()},Pt=(e,t)=>({draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t});function yn(e,t,n,r){if(!e){n(r(t));return}const o=Yl(n);e(t,{announce:o}),o.wasCalled()||n(r(t))}var Zl=(e,t)=>{const n=Xl();let r=null;const o=(u,d)=>{r&&C(),at("onBeforeCapture",()=>{const p=e().onBeforeCapture;p&&p({draggableId:u,mode:d})})},i=(u,d)=>{r&&C(),at("onBeforeDragStart",()=>{const p=e().onBeforeDragStart;p&&p(Pt(u,d))})},s=(u,d)=>{r&&C();const p=Pt(u,d);r={mode:d,lastCritical:u,lastLocation:p.source,lastCombine:null},n.add(()=>{at("onDragStart",()=>yn(e().onDragStart,p,t,Tt.onDragStart))})},a=(u,d)=>{const p=qn(d),m=Zt(d);r||C();const b=!Ql(u,r.lastCritical);b&&(r.lastCritical=u);const g=!Kl(r.lastLocation,p);g&&(r.lastLocation=p);const y=!Jl(r.lastCombine,m);if(y&&(r.lastCombine=m),!b&&!g&&!y)return;const h={...Pt(u,r.mode),combine:m,destination:p};n.add(()=>{at("onDragUpdate",()=>yn(e().onDragUpdate,h,t,Tt.onDragUpdate))})},l=()=>{r||C(),n.flush()},c=u=>{r||C(),r=null,at("onDragEnd",()=>yn(e().onDragEnd,u,t,Tt.onDragEnd))};return{beforeCapture:o,beforeStart:i,start:s,update:a,flush:l,drop:c,abort:()=>{if(!r)return;const u={...Pt(r.lastCritical,r.mode),combine:null,destination:null,reason:"CANCEL"};c(u)}}},ec=(e,t)=>{const n=Zl(e,t);return r=>o=>i=>{if(i.type==="BEFORE_INITIAL_CAPTURE"){n.beforeCapture(i.payload.draggableId,i.payload.movementMode);return}if(i.type==="INITIAL_PUBLISH"){const a=i.payload.critical;n.beforeStart(a,i.payload.movementMode),o(i),n.start(a,i.payload.movementMode);return}if(i.type==="DROP_COMPLETE"){const a=i.payload.completed.result;n.flush(),o(i),n.drop(a);return}if(o(i),i.type==="FLUSH"){n.abort();return}const s=r.getState();s.phase==="DRAGGING"&&n.update(s.critical,s.impact)}};const tc=e=>t=>n=>{if(n.type!=="DROP_ANIMATION_FINISHED"){t(n);return}const r=e.getState();r.phase!=="DROP_ANIMATING"&&C(),e.dispatch(er({completed:r.completed}))};var nc=tc;const rc=e=>{let t=null,n=null;function r(){n&&(cancelAnimationFrame(n),n=null),t&&(t(),t=null)}return o=>i=>{if((i.type==="FLUSH"||i.type==="DROP_COMPLETE"||i.type==="DROP_ANIMATION_FINISHED")&&r(),o(i),i.type!=="DROP_ANIMATE")return;const s={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){e.getState().phase==="DROP_ANIMATING"&&e.dispatch(li())}};n=requestAnimationFrame(()=>{n=null,t=he(window,[s])})}};var oc=rc,ic=e=>()=>t=>n=>{(n.type==="DROP_COMPLETE"||n.type==="FLUSH"||n.type==="DROP_ANIMATE")&&e.stopPublishing(),t(n)},sc=e=>{let t=!1;return()=>n=>r=>{if(r.type==="INITIAL_PUBLISH"){t=!0,e.tryRecordFocus(r.payload.critical.draggable.id),n(r),e.tryRestoreFocusRecorded();return}if(n(r),!!t){if(r.type==="FLUSH"){t=!1,e.tryRestoreFocusRecorded();return}if(r.type==="DROP_COMPLETE"){t=!1;const o=r.payload.completed.result;o.combine&&e.tryShiftRecord(o.draggableId,o.combine.draggableId),e.tryRestoreFocusRecorded()}}}};const ac=e=>e.type==="DROP_COMPLETE"||e.type==="DROP_ANIMATE"||e.type==="FLUSH";var lc=e=>t=>n=>r=>{if(ac(r)){e.stop(),n(r);return}if(r.type==="INITIAL_PUBLISH"){n(r);const o=t.getState();o.phase!=="DRAGGING"&&C(),e.start(o);return}n(r),e.scroll(t.getState())};const cc=e=>t=>n=>{if(t(n),n.type!=="PUBLISH_WHILE_DRAGGING")return;const r=e.getState();r.phase==="DROP_PENDING"&&(r.isWaiting||e.dispatch(ai({reason:r.reason})))};var uc=cc;const dc=Io;var fc=({dimensionMarshal:e,focusMarshal:t,styleMarshal:n,getResponders:r,announce:o,autoScroller:i})=>Co(bl,dc(ws(_l(n),ic(e),Bl(e),zl,nc,oc,uc,lc(i),ql,sc(t),ec(r,o))));const vn=()=>({additions:{},removals:{},modified:{}});function pc({registry:e,callbacks:t}){let n=vn(),r=null;const o=()=>{r||(t.collectionStarting(),r=requestAnimationFrame(()=>{r=null;const{additions:l,removals:c,modified:f}=n,u=Object.keys(l).map(m=>e.draggable.getById(m).getDimension(ee)).sort((m,b)=>m.descriptor.index-b.descriptor.index),d=Object.keys(f).map(m=>{const g=e.droppable.getById(m).callbacks.getScrollWhileDragging();return{droppableId:m,scroll:g}}),p={additions:u,removals:Object.keys(c),modified:d};n=vn(),t.publish(p)}))};return{add:l=>{const c=l.descriptor.id;n.additions[c]=l,n.modified[l.descriptor.droppableId]=!0,n.removals[c]&&delete n.removals[c],o()},remove:l=>{const c=l.descriptor;n.removals[c.id]=!0,n.modified[c.droppableId]=!0,n.additions[c.id]&&delete n.additions[c.id],o()},stop:()=>{r&&(cancelAnimationFrame(r),r=null,n=vn())}}}var fi=({scrollHeight:e,scrollWidth:t,height:n,width:r})=>{const o=ue({x:t,y:e},{x:r,y:n});return{x:Math.max(0,o.x),y:Math.max(0,o.y)}},pi=()=>{const e=document.documentElement;return e||C(),e},gi=()=>{const e=pi();return fi({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})},gc=()=>{const e=di(),t=gi(),n=e.y,r=e.x,o=pi(),i=o.clientWidth,s=o.clientHeight,a=r+i,l=n+s;return{frame:De({top:n,left:r,right:a,bottom:l}),scroll:{initial:e,current:e,max:t,diff:{value:ee,displacement:ee}}}},mc=({critical:e,scrollOptions:t,registry:n})=>{const r=gc(),o=r.scroll.current,i=e.droppable,s=n.droppable.getAllByType(i.type).map(f=>f.callbacks.getDimensionAndWatchScroll(o,t)),a=n.draggable.getAllByType(e.draggable.type).map(f=>f.getDimension(o));return{dimensions:{draggables:zo(a),droppables:Go(s)},critical:e,viewport:r}};function Yr(e,t,n){return!(n.descriptor.id===t.id||n.descriptor.type!==t.type||e.droppable.getById(n.descriptor.droppableId).descriptor.mode!=="virtual")}var hc=(e,t)=>{let n=null;const r=pc({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),o=(d,p)=>{e.droppable.exists(d)||C(),n&&t.updateDroppableIsEnabled({id:d,isEnabled:p})},i=(d,p)=>{n&&(e.droppable.exists(d)||C(),t.updateDroppableIsCombineEnabled({id:d,isCombineEnabled:p}))},s=(d,p)=>{n&&(e.droppable.exists(d)||C(),t.updateDroppableScroll({id:d,newScroll:p}))},a=(d,p)=>{n&&e.droppable.getById(d).callbacks.scroll(p)},l=()=>{if(!n)return;r.stop();const d=n.critical.droppable;e.droppable.getAllByType(d.type).forEach(p=>p.callbacks.dragStopped()),n.unsubscribe(),n=null},c=d=>{n||C();const p=n.critical.draggable;d.type==="ADDITION"&&Yr(e,p,d.value)&&r.add(d.value),d.type==="REMOVAL"&&Yr(e,p,d.value)&&r.remove(d.value)};return{updateDroppableIsEnabled:o,updateDroppableIsCombineEnabled:i,scrollDroppable:a,updateDroppableScroll:s,startPublishing:d=>{n&&C();const p=e.draggable.getById(d.draggableId),m=e.droppable.getById(p.descriptor.droppableId),b={draggable:p.descriptor,droppable:m.descriptor},g=e.subscribe(c);return n={critical:b,unsubscribe:g},mc({critical:b,registry:e,scrollOptions:d.scrollOptions})},stopPublishing:l}},mi=(e,t)=>e.phase==="IDLE"?!0:e.phase!=="DROP_ANIMATING"||e.completed.result.draggableId===t?!1:e.completed.result.reason==="DROP",bc=e=>{window.scrollBy(e.x,e.y)};const yc=Z(e=>Qt(e).filter(t=>!(!t.isEnabled||!t.frame))),vc=(e,t)=>yc(t).find(r=>(r.frame||C(),Qo(r.frame.pageMarginBox)(e)))||null;var xc=({center:e,destination:t,droppables:n})=>{if(t){const o=n[t];return o.frame?o:null}return vc(e,n)};const vt={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:e=>e**2,durationDampening:{stopDampeningAt:1200,accelerateAt:360},disabled:!1};var Dc=(e,t,n=()=>vt)=>{const r=n(),o=e[t.size]*r.startFromPercentage,i=e[t.size]*r.maxScrollAtPercentage;return{startScrollingFrom:o,maxScrollValueAt:i}},hi=({startOfRange:e,endOfRange:t,current:n})=>{const r=t-e;return r===0?0:(n-e)/r},nr=1,Sc=(e,t,n=()=>vt)=>{const r=n();if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return r.maxPixelScroll;if(e===t.startScrollingFrom)return nr;const i=1-hi({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e}),s=r.maxPixelScroll*r.ease(i);return Math.ceil(s)},wc=(e,t,n)=>{const r=n(),o=r.durationDampening.accelerateAt,i=r.durationDampening.stopDampeningAt,s=t,a=i,c=Date.now()-s;if(c>=i)return e;if(c<o)return nr;const f=hi({startOfRange:o,endOfRange:a,current:c}),u=e*r.ease(f);return Math.ceil(u)},Xr=({distanceToEdge:e,thresholds:t,dragStartTime:n,shouldUseTimeDampening:r,getAutoScrollerOptions:o})=>{const i=Sc(e,t,o);return i===0?0:r?Math.max(wc(i,n,o),nr):i},Kr=({container:e,distanceToEdges:t,dragStartTime:n,axis:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const s=Dc(e,r,i);return t[r.end]<t[r.start]?Xr({distanceToEdge:t[r.end],thresholds:s,dragStartTime:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i}):-1*Xr({distanceToEdge:t[r.start],thresholds:s,dragStartTime:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})},Cc=({container:e,subject:t,proposedScroll:n})=>{const r=t.height>e.height,o=t.width>e.width;return!o&&!r?n:o&&r?null:{x:o?0:n.x,y:r?0:n.y}};const Ic=ko(e=>e===0?0:e);var bi=({dragStartTime:e,container:t,subject:n,center:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const s={top:r.y-t.top,right:t.right-r.x,bottom:t.bottom-r.y,left:r.x-t.left},a=Kr({container:t,distanceToEdges:s,dragStartTime:e,axis:Yn,shouldUseTimeDampening:o,getAutoScrollerOptions:i}),l=Kr({container:t,distanceToEdges:s,dragStartTime:e,axis:Uo,shouldUseTimeDampening:o,getAutoScrollerOptions:i}),c=Ic({x:l,y:a});if($e(c,ee))return null;const f=Cc({container:t,subject:n,proposedScroll:c});return f?$e(f,ee)?null:f:null};const Ec=ko(e=>e===0?0:e>0?1:-1),rr=(()=>{const e=(t,n)=>t<0?t:t>n?t-n:0;return({current:t,max:n,change:r})=>{const o=te(t,r),i={x:e(o.x,n.x),y:e(o.y,n.y)};return $e(i,ee)?null:i}})(),yi=({max:e,current:t,change:n})=>{const r={x:Math.max(t.x,e.x),y:Math.max(t.y,e.y)},o=Ec(n),i=rr({max:r,current:t,change:o});return!i||o.x!==0&&i.x===0||o.y!==0&&i.y===0},or=(e,t)=>yi({current:e.scroll.current,max:e.scroll.max,change:t}),Oc=(e,t)=>{if(!or(e,t))return null;const n=e.scroll.max,r=e.scroll.current;return rr({current:r,max:n,change:t})},ir=(e,t)=>{const n=e.frame;return n?yi({current:n.scroll.current,max:n.scroll.max,change:t}):!1},Pc=(e,t)=>{const n=e.frame;return!n||!ir(e,t)?null:rr({current:n.scroll.current,max:n.scroll.max,change:t})};var Rc=({viewport:e,subject:t,center:n,dragStartTime:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const s=bi({dragStartTime:r,container:e.frame,subject:t,center:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i});return s&&or(e,s)?s:null},Ac=({droppable:e,subject:t,center:n,dragStartTime:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const s=e.frame;if(!s)return null;const a=bi({dragStartTime:r,container:s.pageMarginBox,subject:t,center:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i});return a&&ir(e,a)?a:null},Jr=({state:e,dragStartTime:t,shouldUseTimeDampening:n,scrollWindow:r,scrollDroppable:o,getAutoScrollerOptions:i})=>{const s=e.current.page.borderBoxCenter,l=e.dimensions.draggables[e.critical.draggable.id].page.marginBox;if(e.isWindowScrollAllowed){const u=e.viewport,d=Rc({dragStartTime:t,viewport:u,subject:l,center:s,shouldUseTimeDampening:n,getAutoScrollerOptions:i});if(d){r(d);return}}const c=xc({center:s,destination:de(e.impact),droppables:e.dimensions.droppables});if(!c)return;const f=Ac({dragStartTime:t,droppable:c,subject:l,center:s,shouldUseTimeDampening:n,getAutoScrollerOptions:i});f&&o(c.descriptor.id,f)},Nc=({scrollWindow:e,scrollDroppable:t,getAutoScrollerOptions:n=()=>vt})=>{const r=gt(e),o=gt(t);let i=null;const s=c=>{i||C();const{shouldUseTimeDampening:f,dragStartTime:u}=i;Jr({state:c,scrollWindow:r,scrollDroppable:o,dragStartTime:u,shouldUseTimeDampening:f,getAutoScrollerOptions:n})};return{start:c=>{i&&C();const f=Date.now();let u=!1;const d=()=>{u=!0};Jr({state:c,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:d,scrollDroppable:d,getAutoScrollerOptions:n}),i={dragStartTime:f,shouldUseTimeDampening:u},u&&s(c)},stop:()=>{i&&(r.cancel(),o.cancel(),i=null)},scroll:s}},Tc=({move:e,scrollDroppable:t,scrollWindow:n})=>{const r=(a,l)=>{const c=te(a.current.client.selection,l);e({client:c})},o=(a,l)=>{if(!ir(a,l))return l;const c=Pc(a,l);if(!c)return t(a.descriptor.id,l),null;const f=ue(l,c);return t(a.descriptor.id,f),ue(l,f)},i=(a,l,c)=>{if(!a||!or(l,c))return c;const f=Oc(l,c);if(!f)return n(c),null;const u=ue(c,f);return n(u),ue(c,u)};return a=>{const l=a.scrollJumpRequest;if(!l)return;const c=de(a.impact);c||C();const f=o(a.dimensions.droppables[c],l);if(!f)return;const u=a.viewport,d=i(a.isWindowScrollAllowed,u,f);d&&r(a,d)}},Mc=({scrollDroppable:e,scrollWindow:t,move:n,getAutoScrollerOptions:r})=>{const o=Nc({scrollWindow:t,scrollDroppable:e,getAutoScrollerOptions:r}),i=Tc({move:n,scrollWindow:t,scrollDroppable:e});return{scroll:l=>{if(!(r().disabled||l.phase!=="DRAGGING")){if(l.movementMode==="FLUID"){o.scroll(l);return}l.scrollJumpRequest&&i(l)}},start:o.start,stop:o.stop}};const Ke="data-rfd",Je=(()=>{const e=`${Ke}-drag-handle`;return{base:e,draggableId:`${e}-draggable-id`,contextId:`${e}-context-id`}})(),Bn=(()=>{const e=`${Ke}-draggable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),Bc=(()=>{const e=`${Ke}-droppable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),Qr={contextId:`${Ke}-scroll-container-context-id`},_c=e=>t=>`[${t}="${e}"]`,lt=(e,t)=>e.map(n=>{const r=n.styles[t];return r?`${n.selector} { ${r} }`:""}).join(" "),Lc="pointer-events: none;";var $c=e=>{const t=_c(e),n=(()=>{const a=`
      cursor: -webkit-grab;
      cursor: grab;
    `;return{selector:t(Je.contextId),styles:{always:`
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
        `,resting:a,dragging:Lc,dropAnimating:a}}})(),r=(()=>{const a=`
      transition: ${pt.outOfTheWay};
    `;return{selector:t(Bn.contextId),styles:{dragging:a,dropAnimating:a,userCancel:a}}})(),o={selector:t(Bc.contextId),styles:{always:"overflow-anchor: none;"}},s=[r,n,o,{selector:"body",styles:{dragging:`
        cursor: grabbing;
        cursor: -webkit-grabbing;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        overflow-anchor: none;
      `}}];return{always:lt(s,"always"),resting:lt(s,"resting"),dragging:lt(s,"dragging"),dropAnimating:lt(s,"dropAnimating"),userCancel:lt(s,"userCancel")}};const Fc=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?v.useLayoutEffect:v.useEffect;var fe=Fc;const xn=()=>{const e=document.querySelector("head");return e||C(),e},Zr=e=>{const t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t};function Wc(e,t){const n=W(()=>$c(e),[e]),r=v.useRef(null),o=v.useRef(null),i=N(Z(u=>{const d=o.current;d||C(),d.textContent=u}),[]),s=N(u=>{const d=r.current;d||C(),d.textContent=u},[]);fe(()=>{!r.current&&!o.current||C();const u=Zr(t),d=Zr(t);return r.current=u,o.current=d,u.setAttribute(`${Ke}-always`,e),d.setAttribute(`${Ke}-dynamic`,e),xn().appendChild(u),xn().appendChild(d),s(n.always),i(n.resting),()=>{const p=m=>{const b=m.current;b||C(),xn().removeChild(b),m.current=null};p(r),p(o)}},[t,s,i,n.always,n.resting,e]);const a=N(()=>i(n.dragging),[i,n.dragging]),l=N(u=>{if(u==="DROP"){i(n.dropAnimating);return}i(n.userCancel)},[i,n.dropAnimating,n.userCancel]),c=N(()=>{o.current&&i(n.resting)},[i,n.resting]);return W(()=>({dragging:a,dropping:l,resting:c}),[a,l,c])}function vi(e,t){return Array.from(e.querySelectorAll(t))}var xi=e=>e&&e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView:window;function nn(e){return e instanceof xi(e).HTMLElement}function kc(e,t){const n=`[${Je.contextId}="${e}"]`,r=vi(document,n);if(!r.length)return null;const o=r.find(i=>i.getAttribute(Je.draggableId)===t);return!o||!nn(o)?null:o}function Gc(e){const t=v.useRef({}),n=v.useRef(null),r=v.useRef(null),o=v.useRef(!1),i=N(function(d,p){const m={id:d,focus:p};return t.current[d]=m,function(){const g=t.current;g[d]!==m&&delete g[d]}},[]),s=N(function(d){const p=kc(e,d);p&&p!==document.activeElement&&p.focus()},[e]),a=N(function(d,p){n.current===d&&(n.current=p)},[]),l=N(function(){r.current||o.current&&(r.current=requestAnimationFrame(()=>{r.current=null;const d=n.current;d&&s(d)}))},[s]),c=N(function(d){n.current=null;const p=document.activeElement;p&&p.getAttribute(Je.draggableId)===d&&(n.current=d)},[]);return fe(()=>(o.current=!0,function(){o.current=!1;const d=r.current;d&&cancelAnimationFrame(d)}),[]),W(()=>({register:i,tryRecordFocus:c,tryRestoreFocusRecorded:l,tryShiftRecord:a}),[i,c,l,a])}function zc(){const e={draggables:{},droppables:{}},t=[];function n(u){return t.push(u),function(){const p=t.indexOf(u);p!==-1&&t.splice(p,1)}}function r(u){t.length&&t.forEach(d=>d(u))}function o(u){return e.draggables[u]||null}function i(u){const d=o(u);return d||C(),d}const s={register:u=>{e.draggables[u.descriptor.id]=u,r({type:"ADDITION",value:u})},update:(u,d)=>{const p=e.draggables[d.descriptor.id];p&&p.uniqueId===u.uniqueId&&(delete e.draggables[d.descriptor.id],e.draggables[u.descriptor.id]=u)},unregister:u=>{const d=u.descriptor.id,p=o(d);p&&u.uniqueId===p.uniqueId&&(delete e.draggables[d],e.droppables[u.descriptor.droppableId]&&r({type:"REMOVAL",value:u}))},getById:i,findById:o,exists:u=>!!o(u),getAllByType:u=>Object.values(e.draggables).filter(d=>d.descriptor.type===u)};function a(u){return e.droppables[u]||null}function l(u){const d=a(u);return d||C(),d}const c={register:u=>{e.droppables[u.descriptor.id]=u},unregister:u=>{const d=a(u.descriptor.id);d&&u.uniqueId===d.uniqueId&&delete e.droppables[u.descriptor.id]},getById:l,findById:a,exists:u=>!!a(u),getAllByType:u=>Object.values(e.droppables).filter(d=>d.descriptor.type===u)};function f(){e.draggables={},e.droppables={},t.length=0}return{draggable:s,droppable:c,subscribe:n,clean:f}}function Hc(){const e=W(zc,[]);return v.useEffect(()=>function(){H.version.startsWith("16")||H.version.startsWith("17")?requestAnimationFrame(e.clean):e.clean()},[e]),e}var sr=H.createContext(null),Wt=()=>{const e=document.body;return e||C(),e};const jc={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"};var Uc=jc;const Vc=e=>`rfd-announcement-${e}`;function qc(e){const t=W(()=>Vc(e),[e]),n=v.useRef(null);return v.useEffect(function(){const i=document.createElement("div");return n.current=i,i.id=t,i.setAttribute("aria-live","assertive"),i.setAttribute("aria-atomic","true"),Ye(i.style,Uc),Wt().appendChild(i),function(){setTimeout(function(){const l=Wt();l.contains(i)&&l.removeChild(i),i===n.current&&(n.current=null)})}},[t]),N(o=>{const i=n.current;if(i){i.textContent=o;return}},[])}let Yc=0;const Di={separator:"::"};function Xc(e,t=Di){return W(()=>`${e}${t.separator}${Yc++}`,[t.separator,e])}function Kc(e,t=Di){const n=H.useId();return W(()=>`${e}${t.separator}${n}`,[t.separator,e,n])}var ar="useId"in H?Kc:Xc;function Jc({contextId:e,uniqueId:t}){return`rfd-hidden-text-${e}-${t}`}function Qc({contextId:e,text:t}){const n=ar("hidden-text",{separator:"-"}),r=W(()=>Jc({contextId:e,uniqueId:n}),[n,e]);return v.useEffect(function(){const i=document.createElement("div");return i.id=r,i.textContent=t,i.style.display="none",Wt().appendChild(i),function(){const a=Wt();a.contains(i)&&a.removeChild(i)}},[r,t]),r}var rn=H.createContext(null);function Si(e){const t=v.useRef(e);return v.useEffect(()=>{t.current=e}),t}function Zc(){let e=null;function t(){return!!e}function n(s){return s===e}function r(s){e&&C();const a={abandon:s};return e=a,a}function o(){e||C(),e=null}function i(){e&&(e.abandon(),o())}return{isClaimed:t,isActive:n,claim:r,release:o,tryAbandon:i}}function xt(e){return e.phase==="IDLE"||e.phase==="DROP_ANIMATING"?!1:e.isDragging}const eu=9,tu=13,lr=27,wi=32,nu=33,ru=34,ou=35,iu=36,su=37,au=38,lu=39,cu=40,uu={[tu]:!0,[eu]:!0};var Ci=e=>{uu[e.keyCode]&&e.preventDefault()};const du=(()=>{const e="visibilitychange";return typeof document>"u"?e:[e,`ms${e}`,`webkit${e}`,`moz${e}`,`o${e}`].find(r=>`on${r}`in document)||e})();var on=du;const Ii=0,eo=5;function fu(e,t){return Math.abs(t.x-e.x)>=eo||Math.abs(t.y-e.y)>=eo}const to={type:"IDLE"};function pu({cancel:e,completed:t,getPhase:n,setPhase:r}){return[{eventName:"mousemove",fn:o=>{const{button:i,clientX:s,clientY:a}=o;if(i!==Ii)return;const l={x:s,y:a},c=n();if(c.type==="DRAGGING"){o.preventDefault(),c.actions.move(l);return}c.type!=="PENDING"&&C();const f=c.point;if(!fu(f,l))return;o.preventDefault();const u=c.actions.fluidLift(l);r({type:"DRAGGING",actions:u})}},{eventName:"mouseup",fn:o=>{const i=n();if(i.type!=="DRAGGING"){e();return}o.preventDefault(),i.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"mousedown",fn:o=>{n().type==="DRAGGING"&&o.preventDefault(),e()}},{eventName:"keydown",fn:o=>{if(n().type==="PENDING"){e();return}if(o.keyCode===lr){o.preventDefault(),e();return}Ci(o)}},{eventName:"resize",fn:e},{eventName:"scroll",options:{passive:!0,capture:!1},fn:()=>{n().type==="PENDING"&&e()}},{eventName:"webkitmouseforcedown",fn:o=>{const i=n();if(i.type==="IDLE"&&C(),i.actions.shouldRespectForcePress()){e();return}o.preventDefault()}},{eventName:on,fn:e}]}function gu(e){const t=v.useRef(to),n=v.useRef(Le),r=W(()=>({eventName:"mousedown",fn:function(u){if(u.defaultPrevented||u.button!==Ii||u.ctrlKey||u.metaKey||u.shiftKey||u.altKey)return;const d=e.findClosestDraggableId(u);if(!d)return;const p=e.tryGetLock(d,s,{sourceEvent:u});if(!p)return;u.preventDefault();const m={x:u.clientX,y:u.clientY};n.current(),c(p,m)}}),[e]),o=W(()=>({eventName:"webkitmouseforcewillbegin",fn:f=>{if(f.defaultPrevented)return;const u=e.findClosestDraggableId(f);if(!u)return;const d=e.findOptionsForDraggable(u);d&&(d.shouldRespectForcePress||e.canGetLock(u)&&f.preventDefault())}}),[e]),i=N(function(){const u={passive:!1,capture:!0};n.current=he(window,[o,r],u)},[o,r]),s=N(()=>{t.current.type!=="IDLE"&&(t.current=to,n.current(),i())},[i]),a=N(()=>{const f=t.current;s(),f.type==="DRAGGING"&&f.actions.cancel({shouldBlockNextClick:!0}),f.type==="PENDING"&&f.actions.abort()},[s]),l=N(function(){const u={capture:!0,passive:!1},d=pu({cancel:a,completed:s,getPhase:()=>t.current,setPhase:p=>{t.current=p}});n.current=he(window,d,u)},[a,s]),c=N(function(u,d){t.current.type!=="IDLE"&&C(),t.current={type:"PENDING",point:d,actions:u},l()},[l]);fe(function(){return i(),function(){n.current()}},[i])}function mu(){}const hu={[ru]:!0,[nu]:!0,[iu]:!0,[ou]:!0};function bu(e,t){function n(){t(),e.cancel()}function r(){t(),e.drop()}return[{eventName:"keydown",fn:o=>{if(o.keyCode===lr){o.preventDefault(),n();return}if(o.keyCode===wi){o.preventDefault(),r();return}if(o.keyCode===cu){o.preventDefault(),e.moveDown();return}if(o.keyCode===au){o.preventDefault(),e.moveUp();return}if(o.keyCode===lu){o.preventDefault(),e.moveRight();return}if(o.keyCode===su){o.preventDefault(),e.moveLeft();return}if(hu[o.keyCode]){o.preventDefault();return}Ci(o)}},{eventName:"mousedown",fn:n},{eventName:"mouseup",fn:n},{eventName:"click",fn:n},{eventName:"touchstart",fn:n},{eventName:"resize",fn:n},{eventName:"wheel",fn:n,options:{passive:!0}},{eventName:on,fn:n}]}function yu(e){const t=v.useRef(mu),n=W(()=>({eventName:"keydown",fn:function(i){if(i.defaultPrevented||i.keyCode!==wi)return;const s=e.findClosestDraggableId(i);if(!s)return;const a=e.tryGetLock(s,f,{sourceEvent:i});if(!a)return;i.preventDefault();let l=!0;const c=a.snapLift();t.current();function f(){l||C(),l=!1,t.current(),r()}t.current=he(window,bu(c,f),{capture:!0,passive:!1})}}),[e]),r=N(function(){const i={passive:!1,capture:!0};t.current=he(window,[n],i)},[n]);fe(function(){return r(),function(){t.current()}},[r])}const Dn={type:"IDLE"},vu=120,xu=.15;function Du({cancel:e,getPhase:t}){return[{eventName:"orientationchange",fn:e},{eventName:"resize",fn:e},{eventName:"contextmenu",fn:n=>{n.preventDefault()}},{eventName:"keydown",fn:n=>{if(t().type!=="DRAGGING"){e();return}n.keyCode===lr&&n.preventDefault(),e()}},{eventName:on,fn:e}]}function Su({cancel:e,completed:t,getPhase:n}){return[{eventName:"touchmove",options:{capture:!1},fn:r=>{const o=n();if(o.type!=="DRAGGING"){e();return}o.hasMoved=!0;const{clientX:i,clientY:s}=r.touches[0],a={x:i,y:s};r.preventDefault(),o.actions.move(a)}},{eventName:"touchend",fn:r=>{const o=n();if(o.type!=="DRAGGING"){e();return}r.preventDefault(),o.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"touchcancel",fn:r=>{if(n().type!=="DRAGGING"){e();return}r.preventDefault(),e()}},{eventName:"touchforcechange",fn:r=>{const o=n();o.type==="IDLE"&&C();const i=r.touches[0];if(!i||!(i.force>=xu))return;const a=o.actions.shouldRespectForcePress();if(o.type==="PENDING"){a&&e();return}if(a){if(o.hasMoved){r.preventDefault();return}e();return}r.preventDefault()}},{eventName:on,fn:e}]}function wu(e){const t=v.useRef(Dn),n=v.useRef(Le),r=N(function(){return t.current},[]),o=N(function(p){t.current=p},[]),i=W(()=>({eventName:"touchstart",fn:function(p){if(p.defaultPrevented)return;const m=e.findClosestDraggableId(p);if(!m)return;const b=e.tryGetLock(m,a,{sourceEvent:p});if(!b)return;const g=p.touches[0],{clientX:y,clientY:h}=g,D={x:y,y:h};n.current(),u(b,D)}}),[e]),s=N(function(){const p={capture:!0,passive:!1};n.current=he(window,[i],p)},[i]),a=N(()=>{const d=t.current;d.type!=="IDLE"&&(d.type==="PENDING"&&clearTimeout(d.longPressTimerId),o(Dn),n.current(),s())},[s,o]),l=N(()=>{const d=t.current;a(),d.type==="DRAGGING"&&d.actions.cancel({shouldBlockNextClick:!0}),d.type==="PENDING"&&d.actions.abort()},[a]),c=N(function(){const p={capture:!0,passive:!1},m={cancel:l,completed:a,getPhase:r},b=he(window,Su(m),p),g=he(window,Du(m),p);n.current=function(){b(),g()}},[l,r,a]),f=N(function(){const p=r();p.type!=="PENDING"&&C();const m=p.actions.fluidLift(p.point);o({type:"DRAGGING",actions:m,hasMoved:!1})},[r,o]),u=N(function(p,m){r().type!=="IDLE"&&C();const b=setTimeout(f,vu);o({type:"PENDING",point:m,actions:p,longPressTimerId:b}),c()},[c,r,o,f]);fe(function(){return s(),function(){n.current();const m=r();m.type==="PENDING"&&(clearTimeout(m.longPressTimerId),o(Dn))}},[r,s,o]),fe(function(){return he(window,[{eventName:"touchmove",fn:()=>{},options:{capture:!1,passive:!1}}])},[])}const Cu=["input","button","textarea","select","option","optgroup","video","audio"];function Ei(e,t){if(t==null)return!1;if(Cu.includes(t.tagName.toLowerCase()))return!0;const r=t.getAttribute("contenteditable");return r==="true"||r===""?!0:t===e?!1:Ei(e,t.parentElement)}function Iu(e,t){const n=t.target;return nn(n)?Ei(e,n):!1}var Eu=e=>De(e.getBoundingClientRect()).center;function Ou(e){return e instanceof xi(e).Element}const Pu=(()=>{const e="matches";return typeof document>"u"?e:[e,"msMatchesSelector","webkitMatchesSelector"].find(r=>r in Element.prototype)||e})();function Oi(e,t){return e==null?null:e[Pu](t)?e:Oi(e.parentElement,t)}function Ru(e,t){return e.closest?e.closest(t):Oi(e,t)}function Au(e){return`[${Je.contextId}="${e}"]`}function Nu(e,t){const n=t.target;if(!Ou(n))return null;const r=Au(e),o=Ru(n,r);return!o||!nn(o)?null:o}function Tu(e,t){const n=Nu(e,t);return n?n.getAttribute(Je.draggableId):null}function Mu(e,t){const n=`[${Bn.contextId}="${e}"]`,o=vi(document,n).find(i=>i.getAttribute(Bn.id)===t);return!o||!nn(o)?null:o}function Bu(e){e.preventDefault()}function Rt({expected:e,phase:t,isLockActive:n,shouldWarn:r}){return!(!n()||e!==t)}function Pi({lockAPI:e,store:t,registry:n,draggableId:r}){if(e.isClaimed())return!1;const o=n.draggable.findById(r);return!(!o||!o.options.isEnabled||!mi(t.getState(),r))}function _u({lockAPI:e,contextId:t,store:n,registry:r,draggableId:o,forceSensorStop:i,sourceEvent:s}){if(!Pi({lockAPI:e,store:n,registry:r,draggableId:o}))return null;const l=r.draggable.getById(o),c=Mu(t,l.descriptor.id);if(!c||s&&!l.options.canDragInteractiveElements&&Iu(c,s))return null;const f=e.claim(i||Le);let u="PRE_DRAG";function d(){return l.options.shouldRespectForcePress}function p(){return e.isActive(f)}function m(S,w){Rt({expected:S,phase:u,isLockActive:p,shouldWarn:!0})&&n.dispatch(w())}const b=m.bind(null,"DRAGGING");function g(S){function w(){e.release(),u="COMPLETED"}u!=="PRE_DRAG"&&(w(),C()),n.dispatch(vl(S.liftActionArgs)),u="DRAGGING";function T(B,M={shouldBlockNextClick:!1}){if(S.cleanup(),M.shouldBlockNextClick){const P=he(window,[{eventName:"click",fn:Bu,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(P)}w(),n.dispatch(ai({reason:B}))}return{isActive:()=>Rt({expected:"DRAGGING",phase:u,isLockActive:p,shouldWarn:!1}),shouldRespectForcePress:d,drop:B=>T("DROP",B),cancel:B=>T("CANCEL",B),...S.actions}}function y(S){const w=gt(B=>{b(()=>si({client:B}))});return{...g({liftActionArgs:{id:o,clientSelection:S,movementMode:"FLUID"},cleanup:()=>w.cancel(),actions:{move:w}}),move:w}}function h(){const S={moveUp:()=>b(Pl),moveRight:()=>b(Al),moveDown:()=>b(Rl),moveLeft:()=>b(Nl)};return g({liftActionArgs:{id:o,clientSelection:Eu(c),movementMode:"SNAP"},cleanup:Le,actions:S})}function D(){Rt({expected:"PRE_DRAG",phase:u,isLockActive:p,shouldWarn:!0})&&e.release()}return{isActive:()=>Rt({expected:"PRE_DRAG",phase:u,isLockActive:p,shouldWarn:!1}),shouldRespectForcePress:d,fluidLift:y,snapLift:h,abort:D}}const Lu=[gu,yu,wu];function $u({contextId:e,store:t,registry:n,customSensors:r,enableDefaultSensors:o}){const i=[...o?Lu:[],...r||[]],s=v.useState(()=>Zc())[0],a=N(function(g,y){xt(g)&&!xt(y)&&s.tryAbandon()},[s]);fe(function(){let g=t.getState();return t.subscribe(()=>{const h=t.getState();a(g,h),g=h})},[s,t,a]),fe(()=>s.tryAbandon,[s.tryAbandon]);const l=N(b=>Pi({lockAPI:s,registry:n,store:t,draggableId:b}),[s,n,t]),c=N((b,g,y)=>_u({lockAPI:s,registry:n,contextId:e,store:t,draggableId:b,forceSensorStop:g||null,sourceEvent:y&&y.sourceEvent?y.sourceEvent:null}),[e,s,n,t]),f=N(b=>Tu(e,b),[e]),u=N(b=>{const g=n.draggable.findById(b);return g?g.options:null},[n.draggable]),d=N(function(){s.isClaimed()&&(s.tryAbandon(),t.getState().phase!=="IDLE"&&t.dispatch(Zn()))},[s,t]),p=N(()=>s.isClaimed(),[s]),m=W(()=>({canGetLock:l,tryGetLock:c,findClosestDraggableId:f,findOptionsForDraggable:u,tryReleaseLock:d,isLockClaimed:p}),[l,c,f,u,d,p]);for(let b=0;b<i.length;b++)i[b](m)}const Fu=e=>({onBeforeCapture:t=>{const n=()=>{e.onBeforeCapture&&e.onBeforeCapture(t)};H.version.startsWith("16")||H.version.startsWith("17")?n():Dt.flushSync(n)},onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}),Wu=e=>({...vt,...e.autoScrollerOptions,durationDampening:{...vt.durationDampening,...e.autoScrollerOptions}});function ct(e){return e.current||C(),e.current}function ku(e){const{contextId:t,setCallbacks:n,sensors:r,nonce:o,dragHandleUsageInstructions:i}=e,s=v.useRef(null),a=Si(e),l=N(()=>Fu(a.current),[a]),c=N(()=>Wu(a.current),[a]),f=qc(t),u=Qc({contextId:t,text:i}),d=Wc(t,o),p=N(P=>{ct(s).dispatch(P)},[]),m=W(()=>Rr({publishWhileDragging:Dl,updateDroppableScroll:wl,updateDroppableIsEnabled:Cl,updateDroppableIsCombineEnabled:Il,collectionStarting:Sl},p),[p]),b=Hc(),g=W(()=>hc(b,m),[b,m]),y=W(()=>Mc({scrollWindow:bc,scrollDroppable:g.scrollDroppable,getAutoScrollerOptions:c,...Rr({move:si},p)}),[g.scrollDroppable,p,c]),h=Gc(t),D=W(()=>fc({announce:f,autoScroller:y,dimensionMarshal:g,focusMarshal:h,getResponders:l,styleMarshal:d}),[f,y,g,h,l,d]);s.current=D;const x=N(()=>{const P=ct(s);P.getState().phase!=="IDLE"&&P.dispatch(Zn())},[]),S=N(()=>{const P=ct(s).getState();return P.phase==="DROP_ANIMATING"?!0:P.phase==="IDLE"?!1:P.isDragging},[]),w=W(()=>({isDragging:S,tryAbort:x}),[S,x]);n(w);const T=N(P=>mi(ct(s).getState(),P),[]),B=N(()=>Ge(ct(s).getState()),[]),M=W(()=>({marshal:g,focus:h,contextId:t,canLift:T,isMovementAllowed:B,dragHandleUsageInstructionsId:u,registry:b}),[t,g,u,h,T,B,b]);return $u({contextId:t,store:D,registry:b,customSensors:r||null,enableDefaultSensors:e.enableDefaultSensors!==!1}),v.useEffect(()=>x,[x]),H.createElement(rn.Provider,{value:M},H.createElement(Ks,{context:sr,store:D},e.children))}let Gu=0;function zu(){return W(()=>`${Gu++}`,[])}function Hu(){return H.useId()}var ju="useId"in H?Hu:zu;function Uu(e){const t=ju(),n=e.dragHandleUsageInstructions||Tt.dragHandleUsageInstructions;return H.createElement(aa,null,r=>H.createElement(ku,{nonce:e.nonce,contextId:t,setCallbacks:r,dragHandleUsageInstructions:n,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd,autoScrollerOptions:e.autoScrollerOptions},e.children))}const no={dragging:5e3,dropAnimating:4500},Vu=(e,t)=>t?pt.drop(t.duration):e?pt.snap:pt.fluid,qu=(e,t)=>{if(e)return t?yt.opacity.drop:yt.opacity.combining},Yu=e=>e.forceShouldAnimate!=null?e.forceShouldAnimate:e.mode==="SNAP";function Xu(e){const n=e.dimension.client,{offset:r,combineWith:o,dropping:i}=e,s=!!o,a=Yu(e),l=!!i,c=l?Tn.drop(r,s):Tn.moveTo(r);return{position:"fixed",top:n.marginBox.top,left:n.marginBox.left,boxSizing:"border-box",width:n.borderBox.width,height:n.borderBox.height,transition:Vu(a,i),transform:c,opacity:qu(s,l),zIndex:l?no.dropAnimating:no.dragging,pointerEvents:"none"}}function Ku(e){return{transform:Tn.moveTo(e.offset),transition:e.shouldAnimateDisplacement?void 0:"none"}}function Ju(e){return e.type==="DRAGGING"?Xu(e):Ku(e)}function Qu(e,t,n=ee){const r=window.getComputedStyle(t),o=t.getBoundingClientRect(),i=_o(o,r),s=_t(i,n),a={client:i,tagName:t.tagName.toLowerCase(),display:r.display},l={x:i.marginBox.width,y:i.marginBox.height};return{descriptor:e,placeholder:a,displaceBy:l,client:i,page:s}}function Zu(e){const t=ar("draggable"),{descriptor:n,registry:r,getDraggableRef:o,canDragInteractiveElements:i,shouldRespectForcePress:s,isEnabled:a}=e,l=W(()=>({canDragInteractiveElements:i,shouldRespectForcePress:s,isEnabled:a}),[i,a,s]),c=N(p=>{const m=o();return m||C(),Qu(n,m,p)},[n,o]),f=W(()=>({uniqueId:t,descriptor:n,options:l,getDimension:c}),[n,c,l,t]),u=v.useRef(f),d=v.useRef(!0);fe(()=>(r.draggable.register(u.current),()=>r.draggable.unregister(u.current)),[r.draggable]),fe(()=>{if(d.current){d.current=!1;return}const p=u.current;u.current=f,r.draggable.update(f,p)},[f,r.draggable])}var cr=H.createContext(null);function kt(e){const t=v.useContext(e);return t||C(),t}function ed(e){e.preventDefault()}const td=e=>{const t=v.useRef(null),n=N((w=null)=>{t.current=w},[]),r=N(()=>t.current,[]),{contextId:o,dragHandleUsageInstructionsId:i,registry:s}=kt(rn),{type:a,droppableId:l}=kt(cr),c=W(()=>({id:e.draggableId,index:e.index,type:a,droppableId:l}),[e.draggableId,e.index,a,l]),{children:f,draggableId:u,isEnabled:d,shouldRespectForcePress:p,canDragInteractiveElements:m,isClone:b,mapped:g,dropAnimationFinished:y}=e;if(!b){const w=W(()=>({descriptor:c,registry:s,getDraggableRef:r,canDragInteractiveElements:m,shouldRespectForcePress:p,isEnabled:d}),[c,s,r,m,p,d]);Zu(w)}const h=W(()=>d?{tabIndex:0,role:"button","aria-describedby":i,"data-rfd-drag-handle-draggable-id":u,"data-rfd-drag-handle-context-id":o,draggable:!1,onDragStart:ed}:null,[o,i,u,d]),D=N(w=>{g.type==="DRAGGING"&&g.dropping&&w.propertyName==="transform"&&(H.version.startsWith("16")||H.version.startsWith("17")?y():Dt.flushSync(y))},[y,g]),x=W(()=>{const w=Ju(g),T=g.type==="DRAGGING"&&g.dropping?D:void 0;return{innerRef:n,draggableProps:{"data-rfd-draggable-context-id":o,"data-rfd-draggable-id":u,style:w,onTransitionEnd:T},dragHandleProps:h}},[o,h,u,g,D,n]),S=W(()=>({draggableId:c.id,type:c.type,source:{index:c.index,droppableId:c.droppableId}}),[c.droppableId,c.id,c.index,c.type]);return H.createElement(H.Fragment,null,f(x,g.snapshot,S))};var nd=td,Ri=(e,t)=>e===t,Ai=e=>{const{combine:t,destination:n}=e;return n?n.droppableId:t?t.droppableId:null};const rd=e=>e.combine?e.combine.draggableId:null,od=e=>e.at&&e.at.type==="COMBINE"?e.at.combine.draggableId:null;function id(){const e=Z((o,i)=>({x:o,y:i})),t=Z((o,i,s=null,a=null,l=null)=>({isDragging:!0,isClone:i,isDropAnimating:!!l,dropAnimation:l,mode:o,draggingOver:s,combineWith:a,combineTargetFor:null})),n=Z((o,i,s,a,l=null,c=null,f=null)=>({mapped:{type:"DRAGGING",dropping:null,draggingOver:l,combineWith:c,mode:i,offset:o,dimension:s,forceShouldAnimate:f,snapshot:t(i,a,l,c,null)}}));return(o,i)=>{if(xt(o)){if(o.critical.draggable.id!==i.draggableId)return null;const s=o.current.client.offset,a=o.dimensions.draggables[i.draggableId],l=de(o.impact),c=od(o.impact),f=o.forceShouldAnimate;return n(e(s.x,s.y),o.movementMode,a,i.isClone,l,c,f)}if(o.phase==="DROP_ANIMATING"){const s=o.completed;if(s.result.draggableId!==i.draggableId)return null;const a=i.isClone,l=o.dimensions.draggables[i.draggableId],c=s.result,f=c.mode,u=Ai(c),d=rd(c),m={duration:o.dropDuration,curve:tr.drop,moveTo:o.newHomeClientOffset,opacity:d?yt.opacity.drop:null,scale:d?yt.scale.drop:null};return{mapped:{type:"DRAGGING",offset:o.newHomeClientOffset,dimension:l,dropping:m,draggingOver:u,combineWith:d,mode:f,forceShouldAnimate:null,snapshot:t(f,a,u,d,m)}}}return null}}function Ni(e=null){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}const sd={mapped:{type:"SECONDARY",offset:ee,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:Ni(null)}};function ad(){const e=Z((s,a)=>({x:s,y:a})),t=Z(Ni),n=Z((s,a=null,l)=>({mapped:{type:"SECONDARY",offset:s,combineTargetFor:a,shouldAnimateDisplacement:l,snapshot:t(a)}})),r=s=>s?n(ee,s,!0):null,o=(s,a,l,c)=>{const f=l.displaced.visible[s],u=!!(c.inVirtualList&&c.effected[s]),d=Zt(l),p=d&&d.draggableId===s?a:null;if(!f){if(!u)return r(p);if(l.displaced.invisible[s])return null;const g=Qe(c.displacedBy.point),y=e(g.x,g.y);return n(y,p,!0)}if(u)return r(p);const m=l.displacedBy.point,b=e(m.x,m.y);return n(b,p,f.shouldAnimate)};return(s,a)=>{if(xt(s))return s.critical.draggable.id===a.draggableId?null:o(a.draggableId,s.critical.draggable.id,s.impact,s.afterCritical);if(s.phase==="DROP_ANIMATING"){const l=s.completed;return l.result.draggableId===a.draggableId?null:o(a.draggableId,l.result.draggableId,l.impact,l.afterCritical)}return null}}const ld=()=>{const e=id(),t=ad();return(r,o)=>e(r,o)||t(r,o)||sd},cd={dropAnimationFinished:li},ud=Mo(ld,cd,null,{context:sr,areStatePropsEqual:Ri})(nd);var dd=ud;function Ti(e){return kt(cr).isUsingCloneFor===e.draggableId&&!e.isClone?null:H.createElement(dd,e)}function fd(e){const t=typeof e.isDragDisabled=="boolean"?!e.isDragDisabled:!0,n=!!e.disableInteractiveElementBlocking,r=!!e.shouldRespectForcePress;return H.createElement(Ti,Ye({},e,{isClone:!1,isEnabled:t,canDragInteractiveElements:n,shouldRespectForcePress:r}))}const Mi=e=>t=>e===t,pd=Mi("scroll"),gd=Mi("auto"),ro=(e,t)=>t(e.overflowX)||t(e.overflowY),md=e=>{const t=window.getComputedStyle(e),n={overflowX:t.overflowX,overflowY:t.overflowY};return ro(n,pd)||ro(n,gd)},hd=()=>!1,Bi=e=>e==null?null:e===document.body?hd()?e:null:e===document.documentElement?null:md(e)?e:Bi(e.parentElement);var _n=e=>({x:e.scrollLeft,y:e.scrollTop});const _i=e=>e?window.getComputedStyle(e).position==="fixed"?!0:_i(e.parentElement):!1;var bd=e=>{const t=Bi(e),n=_i(e);return{closestScrollable:t,isFixedOnPage:n}},yd=({descriptor:e,isEnabled:t,isCombineEnabled:n,isFixedOnPage:r,direction:o,client:i,page:s,closest:a})=>{const l=(()=>{if(!a)return null;const{scrollSize:d,client:p}=a,m=fi({scrollHeight:d.scrollHeight,scrollWidth:d.scrollWidth,height:p.paddingBox.height,width:p.paddingBox.width});return{pageMarginBox:a.page.marginBox,frameClient:p,scrollSize:d,shouldClipSubject:a.shouldClipSubject,scroll:{initial:a.scroll,current:a.scroll,max:m,diff:{value:ee,displacement:ee}}}})(),c=o==="vertical"?Yn:Uo,f=Xe({page:s,withPlaceholder:null,axis:c,frame:l});return{descriptor:e,isCombineEnabled:n,isFixedOnPage:r,axis:c,isEnabled:t,client:i,page:s,frame:l,subject:f}};const vd=(e,t)=>{const n=Lo(e);if(!t||e!==t)return n;const r=n.paddingBox.top-t.scrollTop,o=n.paddingBox.left-t.scrollLeft,i=r+t.scrollHeight,s=o+t.scrollWidth,l=jn({top:r,right:s,bottom:i,left:o},n.border);return Un({borderBox:l,margin:n.margin,border:n.border,padding:n.padding})};var xd=({ref:e,descriptor:t,env:n,windowScroll:r,direction:o,isDropDisabled:i,isCombineEnabled:s,shouldClipSubject:a})=>{const l=n.closestScrollable,c=vd(e,l),f=_t(c,r),u=(()=>{if(!l)return null;const p=Lo(l),m={scrollHeight:l.scrollHeight,scrollWidth:l.scrollWidth};return{client:p,page:_t(p,r),scroll:_n(l),scrollSize:m,shouldClipSubject:a}})();return yd({descriptor:t,isEnabled:!i,isCombineEnabled:s,isFixedOnPage:n.isFixedOnPage,direction:o,client:c,page:f,closest:u})};const Dd={passive:!1},Sd={passive:!0};var oo=e=>e.shouldPublishImmediately?Dd:Sd;const At=e=>e&&e.env.closestScrollable||null;function wd(e){const t=v.useRef(null),n=kt(rn),r=ar("droppable"),{registry:o,marshal:i}=n,s=Si(e),a=W(()=>({id:e.droppableId,type:e.type,mode:e.mode}),[e.droppableId,e.mode,e.type]),l=v.useRef(a),c=W(()=>Z((x,S)=>{t.current||C();const w={x,y:S};i.updateDroppableScroll(a.id,w)}),[a.id,i]),f=N(()=>{const x=t.current;return!x||!x.env.closestScrollable?ee:_n(x.env.closestScrollable)},[]),u=N(()=>{const x=f();c(x.x,x.y)},[f,c]),d=W(()=>gt(u),[u]),p=N(()=>{const x=t.current,S=At(x);if(x&&S||C(),x.scrollOptions.shouldPublishImmediately){u();return}d()},[d,u]),m=N((x,S)=>{t.current&&C();const w=s.current,T=w.getDroppableRef();T||C();const B=bd(T),M={ref:T,descriptor:a,env:B,scrollOptions:S};t.current=M;const P=xd({ref:T,descriptor:a,env:B,windowScroll:x,direction:w.direction,isDropDisabled:w.isDropDisabled,isCombineEnabled:w.isCombineEnabled,shouldClipSubject:!w.ignoreContainerClipping}),k=B.closestScrollable;return k&&(k.setAttribute(Qr.contextId,n.contextId),k.addEventListener("scroll",p,oo(M.scrollOptions))),P},[n.contextId,a,p,s]),b=N(()=>{const x=t.current,S=At(x);return x&&S||C(),_n(S)},[]),g=N(()=>{const x=t.current;x||C();const S=At(x);t.current=null,S&&(d.cancel(),S.removeAttribute(Qr.contextId),S.removeEventListener("scroll",p,oo(x.scrollOptions)))},[p,d]),y=N(x=>{const S=t.current;S||C();const w=At(S);w||C(),w.scrollTop+=x.y,w.scrollLeft+=x.x},[]),h=W(()=>({getDimensionAndWatchScroll:m,getScrollWhileDragging:b,dragStopped:g,scroll:y}),[g,m,b,y]),D=W(()=>({uniqueId:r,descriptor:a,callbacks:h}),[h,a,r]);fe(()=>(l.current=D.descriptor,o.droppable.register(D),()=>{t.current&&g(),o.droppable.unregister(D)}),[h,a,g,D,i,o.droppable]),fe(()=>{t.current&&i.updateDroppableIsEnabled(l.current.id,!e.isDropDisabled)},[e.isDropDisabled,i]),fe(()=>{t.current&&i.updateDroppableIsCombineEnabled(l.current.id,e.isCombineEnabled)},[e.isCombineEnabled,i])}function Sn(){}const io={width:0,height:0,margin:ga},Cd=({isAnimatingOpenOnMount:e,placeholder:t,animate:n})=>e||n==="close"?io:{height:t.client.borderBox.height,width:t.client.borderBox.width,margin:t.client.margin},Id=({isAnimatingOpenOnMount:e,placeholder:t,animate:n})=>{const r=Cd({isAnimatingOpenOnMount:e,placeholder:t,animate:n});return{display:t.display,boxSizing:"border-box",width:r.width,height:r.height,marginTop:r.margin.top,marginRight:r.margin.right,marginBottom:r.margin.bottom,marginLeft:r.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:n!=="none"?pt.placeholder:null}},Ed=e=>{const t=v.useRef(null),n=N(()=>{t.current&&(clearTimeout(t.current),t.current=null)},[]),{animate:r,onTransitionEnd:o,onClose:i,contextId:s}=e,[a,l]=v.useState(e.animate==="open");v.useEffect(()=>a?r!=="open"?(n(),l(!1),Sn):t.current?Sn:(t.current=setTimeout(()=>{t.current=null,l(!1)}),n):Sn,[r,a,n]);const c=N(u=>{u.propertyName==="height"&&(o(),r==="close"&&i())},[r,i,o]),f=Id({isAnimatingOpenOnMount:a,animate:e.animate,placeholder:e.placeholder});return H.createElement(e.placeholder.tagName,{style:f,"data-rfd-placeholder-context-id":s,onTransitionEnd:c,ref:e.innerRef})};var Od=H.memo(Ed);class Pd extends H.PureComponent{constructor(...t){super(...t),this.state={isVisible:!!this.props.on,data:this.props.on,animate:this.props.shouldAnimate&&this.props.on?"open":"none"},this.onClose=()=>{this.state.animate==="close"&&this.setState({isVisible:!1})}}static getDerivedStateFromProps(t,n){return t.shouldAnimate?t.on?{isVisible:!0,data:t.on,animate:"open"}:n.isVisible?{isVisible:!0,data:n.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:!!t.on,data:t.on,animate:"none"}}render(){if(!this.state.isVisible)return null;const t={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(t)}}const Rd=e=>{const t=v.useContext(rn);t||C();const{contextId:n,isMovementAllowed:r}=t,o=v.useRef(null),i=v.useRef(null),{children:s,droppableId:a,type:l,mode:c,direction:f,ignoreContainerClipping:u,isDropDisabled:d,isCombineEnabled:p,snapshot:m,useClone:b,updateViewportMaxScroll:g,getContainerForClone:y}=e,h=N(()=>o.current,[]),D=N((k=null)=>{o.current=k},[]);N(()=>i.current,[]);const x=N((k=null)=>{i.current=k},[]),S=N(()=>{r()&&g({maxScroll:gi()})},[r,g]);wd({droppableId:a,type:l,mode:c,direction:f,isDropDisabled:d,isCombineEnabled:p,ignoreContainerClipping:u,getDroppableRef:h});const w=W(()=>H.createElement(Pd,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},({onClose:k,data:V,animate:J})=>H.createElement(Od,{placeholder:V,onClose:k,innerRef:x,animate:J,contextId:n,onTransitionEnd:S})),[n,S,e.placeholder,e.shouldAnimatePlaceholder,x]),T=W(()=>({innerRef:D,placeholder:w,droppableProps:{"data-rfd-droppable-id":a,"data-rfd-droppable-context-id":n}}),[n,a,w,D]),B=b?b.dragging.draggableId:null,M=W(()=>({droppableId:a,type:l,isUsingCloneFor:B}),[a,B,l]);function P(){if(!b)return null;const{dragging:k,render:V}=b,J=H.createElement(Ti,{draggableId:k.draggableId,index:k.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},(Oe,ae)=>V(Oe,ae,k));return rs.createPortal(J,y())}return H.createElement(cr.Provider,{value:M},s(T,m),P())};var Ad=Rd;function Nd(){return document.body||C(),document.body}const so={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:Nd},Li=e=>{let t={...e},n;for(n in so)e[n]===void 0&&(t={...t,[n]:so[n]});return t},wn=(e,t)=>e===t.droppable.type,ao=(e,t)=>t.draggables[e.draggable.id],Td=()=>{const e={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t={...e,shouldAnimatePlaceholder:!1},n=Z(i=>({draggableId:i.id,type:i.type,source:{index:i.index,droppableId:i.droppableId}})),r=Z((i,s,a,l,c,f)=>{const u=c.descriptor.id;if(c.descriptor.droppableId===i){const m=f?{render:f,dragging:n(c.descriptor)}:null,b={isDraggingOver:a,draggingOverWith:a?u:null,draggingFromThisWith:u,isUsingPlaceholder:!0};return{placeholder:c.placeholder,shouldAnimatePlaceholder:!1,snapshot:b,useClone:m}}if(!s)return t;if(!l)return e;const p={isDraggingOver:a,draggingOverWith:u,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:c.placeholder,shouldAnimatePlaceholder:!0,snapshot:p,useClone:null}});return(i,s)=>{const a=Li(s),l=a.droppableId,c=a.type,f=!a.isDropDisabled,u=a.renderClone;if(xt(i)){const d=i.critical;if(!wn(c,d))return t;const p=ao(d,i.dimensions),m=de(i.impact)===l;return r(l,f,m,m,p,u)}if(i.phase==="DROP_ANIMATING"){const d=i.completed;if(!wn(c,d.critical))return t;const p=ao(d.critical,i.dimensions);return r(l,f,Ai(d.result)===l,de(d.impact)===l,p,u)}if(i.phase==="IDLE"&&i.completed&&!i.shouldFlush){const d=i.completed;if(!wn(c,d.critical))return t;const p=de(d.impact)===l,m=!!(d.impact.at&&d.impact.at.type==="COMBINE"),b=d.critical.droppable.id===l;return p?m?e:t:b?e:t}return t}},Md={updateViewportMaxScroll:Ol},Bd=Mo(Td,Md,(e,t,n)=>({...Li(n),...e,...t}),{context:sr,areStatePropsEqual:Ri})(Ad);var _d=Bd;const Ld=({columns:e,visibleColumns:t,onChange:n,draggable:r=!0,onDragEnd:o})=>{const i=e.length>1,[s,a]=v.useState(!1),[l,c]=v.useState(""),u=[...e.filter(g=>String(g.title).toLowerCase().includes(l.toLowerCase()))].sort((g,y)=>{const h=e.find(w=>w.key===g.key),D=e.find(w=>w.key===y.key);if((h==null?void 0:h.fixed)==="left"&&(D==null?void 0:D.fixed)!=="left")return-1;if((h==null?void 0:h.fixed)!=="left"&&(D==null?void 0:D.fixed)==="left"||(h==null?void 0:h.fixed)==="right"&&(D==null?void 0:D.fixed)!=="right")return 1;if((h==null?void 0:h.fixed)!=="right"&&(D==null?void 0:D.fixed)==="right")return-1;if((h==null?void 0:h.fixed)==="left"&&(D==null?void 0:D.fixed)==="left"||(h==null?void 0:h.fixed)==="right"&&(D==null?void 0:D.fixed)==="right"){const w=t.indexOf(g.key),T=t.indexOf(y.key);return w-T}const x=t.indexOf(g.key),S=t.indexOf(y.key);return x===-1?1:S===-1?-1:x-S}),d=g=>{const y=e.find(S=>S.key===g);if(y!=null&&y.columnsFix)return;const h=t.includes(g)?t.filter(S=>S!==g):[...t,g],D=e.filter(S=>S.columnsFix).map(S=>S.key),x=Array.from(new Set([...h,...D]));n(x)},p=()=>{const g=e.filter(x=>x.columnsFix).map(x=>x.key),y=e.filter(x=>!x.columnsFix).map(x=>x.key),D=y.every(x=>t.includes(x))?g:[...g,...y];n(D)},m=g=>{if(!r)return;console.log("ColumnSelector handleDragEnd triggered:",g);const{source:y,destination:h}=g;if(!h||y.index===h.index)return;const D=u[y.index],x=e.find(P=>P.key===D.key);if(x&&x.fixed)return;const S=Array.from(u);S.splice(y.index,1),S.splice(h.index,0,D);let w=S.map(P=>P.key);const T=e.filter(P=>P.fixed==="left").map(P=>P.key),B=e.filter(P=>P.fixed==="right").map(P=>P.key);w=w.filter(P=>!T.includes(P)&&!B.includes(P)),w=[...T,...w,...B];const M=[...t];M.sort((P,k)=>{const V=w.indexOf(P),J=w.indexOf(k);return V===-1?1:J===-1?-1:V-J}),n(M),o&&o(w)},b=G.jsxs(os,{style:{padding:"16px",width:"250px",maxHeight:"434px",overflowY:"auto",border:"none",boxShadow:"0 4px 16px rgba(0, 0, 0, 0.1)"},children:[i&&G.jsx(yo,{prefix:G.jsx(is,{}),value:l,onChange:g=>c(g.target.value),style:{marginBottom:"12px"}}),G.jsx("div",{style:{marginBottom:"8px"},children:G.jsx(wr,{checked:e.filter(g=>!g.columnsFix).every(g=>t.includes(g.key)),indeterminate:e.filter(g=>!g.columnsFix).some(g=>t.includes(g.key))&&e.filter(g=>!g.columnsFix).some(g=>!t.includes(g.key)),onChange:p,children:"ALL"})}),G.jsx(Uu,{onDragEnd:m,children:G.jsx(_d,{droppableId:"columnSelector",children:g=>G.jsxs("div",{...g.droppableProps,ref:g.innerRef,children:[u.map((y,h)=>{const D=e.find(S=>S.key===y.key),x=D&&D.fixed;return G.jsx(fd,{draggableId:y.key,index:h,isDragDisabled:!r||x,children:(S,w)=>G.jsxs("div",{ref:S.innerRef,...S.draggableProps,style:{...S.draggableProps.style,display:"flex",alignItems:"center",padding:"4px 0",background:w.isDragging?"#e6f4ff":"transparent",borderRadius:"4px"},children:[G.jsx(wr,{checked:t.includes(y.key),onChange:()=>d(y.key),disabled:y.columnsFix,style:{marginRight:"8px"}}),G.jsx("span",{style:{flex:1},children:y.title}),G.jsx("div",{...S.dragHandleProps,style:{cursor:r&&!x?"grab":"not-allowed",padding:"0 8px",color:"inherit",opacity:r&&!x?1:.5},children:r?"≡":""})]})},y.key)}),g.placeholder]})})})]});return G.jsx(ss,{overlay:b,visible:s,onVisibleChange:g=>a(g),trigger:["click"],placement:"bottomRight",align:{offset:[6,12]},overlayStyle:{zIndex:1001,position:"fixed"},getPopupContainer:()=>document.body,children:G.jsx(as,{type:"text",icon:G.jsx("img",{src:ls,style:{width:20,height:20,verticalAlign:"middle"}}),style:{background:"transparent"}})})};var ur={exports:{}},It={},sn={exports:{}},$i={},Ln={exports:{}};function Fi(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Fi(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function lo(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Fi(e))&&(r&&(r+=" "),r+=t);return r}Ln.exports=lo,Ln.exports.clsx=lo;var Wi=Ln.exports;const tp=ho(Wi);var q={},Ie={};Object.defineProperty(Ie,"__esModule",{value:!0});Ie.dontSetMe=Gd;Ie.findInArray=$d;Ie.int=kd;Ie.isFunction=Fd;Ie.isNum=Wd;function $d(e,t){for(let n=0,r=e.length;n<r;n++)if(t.apply(t,[e[n],n,e]))return e[n]}function Fd(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Function]"}function Wd(e){return typeof e=="number"&&!isNaN(e)}function kd(e){return parseInt(e,10)}function Gd(e,t,n){if(e[t])return new Error(`Invalid prop ${t} passed to ${n} - do not set this, set it on the child.`)}var He={};Object.defineProperty(He,"__esModule",{value:!0});He.browserPrefixToKey=Gi;He.browserPrefixToStyle=zd;He.default=void 0;He.getPrefix=ki;const Cn=["Moz","Webkit","O","ms"];function ki(){var n,r;let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"transform";if(typeof window>"u")return"";const t=(r=(n=window.document)==null?void 0:n.documentElement)==null?void 0:r.style;if(!t||e in t)return"";for(let o=0;o<Cn.length;o++)if(Gi(e,Cn[o])in t)return Cn[o];return""}function Gi(e,t){return t?`${t}${Hd(e)}`:e}function zd(e,t){return t?`-${t.toLowerCase()}-${e}`:e}function Hd(e){let t="",n=!0;for(let r=0;r<e.length;r++)n?(t+=e[r].toUpperCase(),n=!1):e[r]==="-"?n=!0:t+=e[r];return t}He.default=ki();Object.defineProperty(q,"__esModule",{value:!0});q.addClassName=ji;q.addEvent=Ud;q.addUserSelectStyles=nf;q.createCSSTransform=Qd;q.createSVGTransform=Zd;q.getTouch=ef;q.getTouchIdentifier=tf;q.getTranslation=dr;q.innerHeight=Xd;q.innerWidth=Kd;q.matchesSelector=Hi;q.matchesSelectorAndParentsTo=jd;q.offsetXYFromParent=Jd;q.outerHeight=qd;q.outerWidth=Yd;q.removeClassName=Ui;q.removeEvent=Vd;q.scheduleRemoveUserSelectStyles=rf;var pe=Ie,co=zi(He);function zi(e,t){if(typeof WeakMap=="function")var n=new WeakMap,r=new WeakMap;return(zi=function(o,i){if(!i&&o&&o.__esModule)return o;var s,a,l={__proto__:null,default:o};if(o===null||typeof o!="object"&&typeof o!="function")return l;if(s=i?r:n){if(s.has(o))return s.get(o);s.set(o,l)}for(const c in o)c!=="default"&&{}.hasOwnProperty.call(o,c)&&((a=(s=Object.defineProperty)&&Object.getOwnPropertyDescriptor(o,c))&&(a.get||a.set)?s(l,c,a):l[c]=o[c]);return l})(e,t)}let Nt="";function Hi(e,t){return Nt||(Nt=(0,pe.findInArray)(["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"],function(n){return(0,pe.isFunction)(e[n])})),(0,pe.isFunction)(e[Nt])?e[Nt](t):!1}function jd(e,t,n){let r=e;do{if(Hi(r,t))return!0;if(r===n)return!1;r=r.parentNode}while(r);return!1}function Ud(e,t,n,r){if(!e)return;const o={capture:!0,...r};e.addEventListener?e.addEventListener(t,n,o):e.attachEvent?e.attachEvent("on"+t,n):e["on"+t]=n}function Vd(e,t,n,r){if(!e)return;const o={capture:!0,...r};e.removeEventListener?e.removeEventListener(t,n,o):e.detachEvent?e.detachEvent("on"+t,n):e["on"+t]=null}function qd(e){let t=e.clientHeight;const n=e.ownerDocument.defaultView.getComputedStyle(e);return t+=(0,pe.int)(n.borderTopWidth),t+=(0,pe.int)(n.borderBottomWidth),t}function Yd(e){let t=e.clientWidth;const n=e.ownerDocument.defaultView.getComputedStyle(e);return t+=(0,pe.int)(n.borderLeftWidth),t+=(0,pe.int)(n.borderRightWidth),t}function Xd(e){let t=e.clientHeight;const n=e.ownerDocument.defaultView.getComputedStyle(e);return t-=(0,pe.int)(n.paddingTop),t-=(0,pe.int)(n.paddingBottom),t}function Kd(e){let t=e.clientWidth;const n=e.ownerDocument.defaultView.getComputedStyle(e);return t-=(0,pe.int)(n.paddingLeft),t-=(0,pe.int)(n.paddingRight),t}function Jd(e,t,n){const o=t===t.ownerDocument.body?{left:0,top:0}:t.getBoundingClientRect(),i=(e.clientX+t.scrollLeft-o.left)/n,s=(e.clientY+t.scrollTop-o.top)/n;return{x:i,y:s}}function Qd(e,t){const n=dr(e,t,"px");return{[(0,co.browserPrefixToKey)("transform",co.default)]:n}}function Zd(e,t){return dr(e,t,"")}function dr(e,t,n){let{x:r,y:o}=e,i=`translate(${r}${n},${o}${n})`;if(t){const s=`${typeof t.x=="string"?t.x:t.x+n}`,a=`${typeof t.y=="string"?t.y:t.y+n}`;i=`translate(${s}, ${a})`+i}return i}function ef(e,t){return e.targetTouches&&(0,pe.findInArray)(e.targetTouches,n=>t===n.identifier)||e.changedTouches&&(0,pe.findInArray)(e.changedTouches,n=>t===n.identifier)}function tf(e){if(e.targetTouches&&e.targetTouches[0])return e.targetTouches[0].identifier;if(e.changedTouches&&e.changedTouches[0])return e.changedTouches[0].identifier}function nf(e){if(!e)return;let t=e.getElementById("react-draggable-style-el");t||(t=e.createElement("style"),t.type="text/css",t.id="react-draggable-style-el",t.innerHTML=`.react-draggable-transparent-selection *::-moz-selection {all: inherit;}
`,t.innerHTML+=`.react-draggable-transparent-selection *::selection {all: inherit;}
`,e.getElementsByTagName("head")[0].appendChild(t)),e.body&&ji(e.body,"react-draggable-transparent-selection")}function rf(e){window.requestAnimationFrame?window.requestAnimationFrame(()=>{uo(e)}):uo(e)}function uo(e){if(e)try{if(e.body&&Ui(e.body,"react-draggable-transparent-selection"),e.selection)e.selection.empty();else{const t=(e.defaultView||window).getSelection();t&&t.type!=="Caret"&&t.removeAllRanges()}}catch{}}function ji(e,t){e.classList?e.classList.add(t):e.className.match(new RegExp(`(?:^|\\s)${t}(?!\\S)`))||(e.className+=` ${t}`)}function Ui(e,t){e.classList?e.classList.remove(t):e.className=e.className.replace(new RegExp(`(?:^|\\s)${t}(?!\\S)`,"g"),"")}var Ee={};Object.defineProperty(Ee,"__esModule",{value:!0});Ee.canDragX=af;Ee.canDragY=lf;Ee.createCoreData=uf;Ee.createDraggableData=df;Ee.getBoundPosition=of;Ee.getControlPosition=cf;Ee.snapToGrid=sf;var ce=Ie,qe=q;function of(e,t,n){if(!e.props.bounds)return[t,n];let{bounds:r}=e.props;r=typeof r=="string"?r:ff(r);const o=fr(e);if(typeof r=="string"){const{ownerDocument:i}=o,s=i.defaultView;let a;if(r==="parent"?a=o.parentNode:a=o.getRootNode().querySelector(r),!(a instanceof s.HTMLElement))throw new Error('Bounds selector "'+r+'" could not find an element.');const l=a,c=s.getComputedStyle(o),f=s.getComputedStyle(l);r={left:-o.offsetLeft+(0,ce.int)(f.paddingLeft)+(0,ce.int)(c.marginLeft),top:-o.offsetTop+(0,ce.int)(f.paddingTop)+(0,ce.int)(c.marginTop),right:(0,qe.innerWidth)(l)-(0,qe.outerWidth)(o)-o.offsetLeft+(0,ce.int)(f.paddingRight)-(0,ce.int)(c.marginRight),bottom:(0,qe.innerHeight)(l)-(0,qe.outerHeight)(o)-o.offsetTop+(0,ce.int)(f.paddingBottom)-(0,ce.int)(c.marginBottom)}}return(0,ce.isNum)(r.right)&&(t=Math.min(t,r.right)),(0,ce.isNum)(r.bottom)&&(n=Math.min(n,r.bottom)),(0,ce.isNum)(r.left)&&(t=Math.max(t,r.left)),(0,ce.isNum)(r.top)&&(n=Math.max(n,r.top)),[t,n]}function sf(e,t,n){const r=Math.round(t/e[0])*e[0],o=Math.round(n/e[1])*e[1];return[r,o]}function af(e){return e.props.axis==="both"||e.props.axis==="x"}function lf(e){return e.props.axis==="both"||e.props.axis==="y"}function cf(e,t,n){const r=typeof t=="number"?(0,qe.getTouch)(e,t):null;if(typeof t=="number"&&!r)return null;const o=fr(n),i=n.props.offsetParent||o.offsetParent||o.ownerDocument.body;return(0,qe.offsetXYFromParent)(r||e,i,n.props.scale)}function uf(e,t,n){const r=!(0,ce.isNum)(e.lastX),o=fr(e);return r?{node:o,deltaX:0,deltaY:0,lastX:t,lastY:n,x:t,y:n}:{node:o,deltaX:t-e.lastX,deltaY:n-e.lastY,lastX:e.lastX,lastY:e.lastY,x:t,y:n}}function df(e,t){const n=e.props.scale;return{node:t.node,x:e.state.x+t.deltaX/n,y:e.state.y+t.deltaY/n,deltaX:t.deltaX/n,deltaY:t.deltaY/n,lastX:e.state.x,lastY:e.state.y}}function ff(e){return{left:e.left,top:e.top,right:e.right,bottom:e.bottom}}function fr(e){const t=e.findDOMNode();if(!t)throw new Error("<DraggableCore>: Unmounted during event!");return t}var an={},ln={};Object.defineProperty(ln,"__esModule",{value:!0});ln.default=pf;function pf(){}Object.defineProperty(an,"__esModule",{value:!0});an.default=void 0;var In=Vi(v),ie=pr(St),gf=pr(Dt),ne=q,Be=Ee,En=Ie,ut=pr(ln);function pr(e){return e&&e.__esModule?e:{default:e}}function Vi(e,t){if(typeof WeakMap=="function")var n=new WeakMap,r=new WeakMap;return(Vi=function(o,i){if(!i&&o&&o.__esModule)return o;var s,a,l={__proto__:null,default:o};if(o===null||typeof o!="object"&&typeof o!="function")return l;if(s=i?r:n){if(s.has(o))return s.get(o);s.set(o,l)}for(const c in o)c!=="default"&&{}.hasOwnProperty.call(o,c)&&((a=(s=Object.defineProperty)&&Object.getOwnPropertyDescriptor(o,c))&&(a.get||a.set)?s(l,c,a):l[c]=o[c]);return l})(e,t)}function se(e,t,n){return(t=mf(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function mf(e){var t=hf(e,"string");return typeof t=="symbol"?t:t+""}function hf(e,t){if(typeof e!="object"||!e)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t);if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}const xe={touch:{start:"touchstart",move:"touchmove",stop:"touchend"},mouse:{start:"mousedown",move:"mousemove",stop:"mouseup"}};let _e=xe.mouse,cn=class extends In.Component{constructor(){super(...arguments),se(this,"dragging",!1),se(this,"lastX",NaN),se(this,"lastY",NaN),se(this,"touchIdentifier",null),se(this,"mounted",!1),se(this,"handleDragStart",t=>{if(this.props.onMouseDown(t),!this.props.allowAnyClick&&typeof t.button=="number"&&t.button!==0)return!1;const n=this.findDOMNode();if(!n||!n.ownerDocument||!n.ownerDocument.body)throw new Error("<DraggableCore> not mounted on DragStart!");const{ownerDocument:r}=n;if(this.props.disabled||!(t.target instanceof r.defaultView.Node)||this.props.handle&&!(0,ne.matchesSelectorAndParentsTo)(t.target,this.props.handle,n)||this.props.cancel&&(0,ne.matchesSelectorAndParentsTo)(t.target,this.props.cancel,n))return;t.type==="touchstart"&&!this.props.allowMobileScroll&&t.preventDefault();const o=(0,ne.getTouchIdentifier)(t);this.touchIdentifier=o;const i=(0,Be.getControlPosition)(t,o,this);if(i==null)return;const{x:s,y:a}=i,l=(0,Be.createCoreData)(this,s,a);(0,ut.default)("DraggableCore: handleDragStart: %j",l),(0,ut.default)("calling",this.props.onStart),!(this.props.onStart(t,l)===!1||this.mounted===!1)&&(this.props.enableUserSelectHack&&(0,ne.addUserSelectStyles)(r),this.dragging=!0,this.lastX=s,this.lastY=a,(0,ne.addEvent)(r,_e.move,this.handleDrag),(0,ne.addEvent)(r,_e.stop,this.handleDragStop))}),se(this,"handleDrag",t=>{const n=(0,Be.getControlPosition)(t,this.touchIdentifier,this);if(n==null)return;let{x:r,y:o}=n;if(Array.isArray(this.props.grid)){let a=r-this.lastX,l=o-this.lastY;if([a,l]=(0,Be.snapToGrid)(this.props.grid,a,l),!a&&!l)return;r=this.lastX+a,o=this.lastY+l}const i=(0,Be.createCoreData)(this,r,o);if((0,ut.default)("DraggableCore: handleDrag: %j",i),this.props.onDrag(t,i)===!1||this.mounted===!1){try{this.handleDragStop(new MouseEvent("mouseup"))}catch{const l=document.createEvent("MouseEvents");l.initMouseEvent("mouseup",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),this.handleDragStop(l)}return}this.lastX=r,this.lastY=o}),se(this,"handleDragStop",t=>{if(!this.dragging)return;const n=(0,Be.getControlPosition)(t,this.touchIdentifier,this);if(n==null)return;let{x:r,y:o}=n;if(Array.isArray(this.props.grid)){let l=r-this.lastX||0,c=o-this.lastY||0;[l,c]=(0,Be.snapToGrid)(this.props.grid,l,c),r=this.lastX+l,o=this.lastY+c}const i=(0,Be.createCoreData)(this,r,o);if(this.props.onStop(t,i)===!1||this.mounted===!1)return!1;const a=this.findDOMNode();a&&this.props.enableUserSelectHack&&(0,ne.scheduleRemoveUserSelectStyles)(a.ownerDocument),(0,ut.default)("DraggableCore: handleDragStop: %j",i),this.dragging=!1,this.lastX=NaN,this.lastY=NaN,a&&((0,ut.default)("DraggableCore: Removing handlers"),(0,ne.removeEvent)(a.ownerDocument,_e.move,this.handleDrag),(0,ne.removeEvent)(a.ownerDocument,_e.stop,this.handleDragStop))}),se(this,"onMouseDown",t=>(_e=xe.mouse,this.handleDragStart(t))),se(this,"onMouseUp",t=>(_e=xe.mouse,this.handleDragStop(t))),se(this,"onTouchStart",t=>(_e=xe.touch,this.handleDragStart(t))),se(this,"onTouchEnd",t=>(_e=xe.touch,this.handleDragStop(t)))}componentDidMount(){this.mounted=!0;const t=this.findDOMNode();t&&(0,ne.addEvent)(t,xe.touch.start,this.onTouchStart,{passive:!1})}componentWillUnmount(){this.mounted=!1;const t=this.findDOMNode();if(t){const{ownerDocument:n}=t;(0,ne.removeEvent)(n,xe.mouse.move,this.handleDrag),(0,ne.removeEvent)(n,xe.touch.move,this.handleDrag),(0,ne.removeEvent)(n,xe.mouse.stop,this.handleDragStop),(0,ne.removeEvent)(n,xe.touch.stop,this.handleDragStop),(0,ne.removeEvent)(t,xe.touch.start,this.onTouchStart,{passive:!1}),this.props.enableUserSelectHack&&(0,ne.scheduleRemoveUserSelectStyles)(n)}}findDOMNode(){var t,n,r;return(t=this.props)!=null&&t.nodeRef?(r=(n=this.props)==null?void 0:n.nodeRef)==null?void 0:r.current:gf.default.findDOMNode(this)}render(){return In.cloneElement(In.Children.only(this.props.children),{onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp,onTouchEnd:this.onTouchEnd})}};an.default=cn;se(cn,"displayName","DraggableCore");se(cn,"propTypes",{allowAnyClick:ie.default.bool,allowMobileScroll:ie.default.bool,children:ie.default.node.isRequired,disabled:ie.default.bool,enableUserSelectHack:ie.default.bool,offsetParent:function(e,t){if(e[t]&&e[t].nodeType!==1)throw new Error("Draggable's offsetParent must be a DOM Node.")},grid:ie.default.arrayOf(ie.default.number),handle:ie.default.string,cancel:ie.default.string,nodeRef:ie.default.object,onStart:ie.default.func,onDrag:ie.default.func,onStop:ie.default.func,onMouseDown:ie.default.func,scale:ie.default.number,className:En.dontSetMe,style:En.dontSetMe,transform:En.dontSetMe});se(cn,"defaultProps",{allowAnyClick:!1,allowMobileScroll:!1,disabled:!1,enableUserSelectHack:!0,onStart:function(){},onDrag:function(){},onStop:function(){},onMouseDown:function(){},scale:1});(function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"DraggableCore",{enumerable:!0,get:function(){return l.default}}),e.default=void 0;var t=u(v),n=f(St),r=f(Dt),o=Wi,i=q,s=Ee,a=Ie,l=f(an),c=f(ln);function f(y){return y&&y.__esModule?y:{default:y}}function u(y,h){if(typeof WeakMap=="function")var D=new WeakMap,x=new WeakMap;return(u=function(S,w){if(!w&&S&&S.__esModule)return S;var T,B,M={__proto__:null,default:S};if(S===null||typeof S!="object"&&typeof S!="function")return M;if(T=w?x:D){if(T.has(S))return T.get(S);T.set(S,M)}for(const P in S)P!=="default"&&{}.hasOwnProperty.call(S,P)&&((B=(T=Object.defineProperty)&&Object.getOwnPropertyDescriptor(S,P))&&(B.get||B.set)?T(M,P,B):M[P]=S[P]);return M})(y,h)}function d(){return d=Object.assign?Object.assign.bind():function(y){for(var h=1;h<arguments.length;h++){var D=arguments[h];for(var x in D)({}).hasOwnProperty.call(D,x)&&(y[x]=D[x])}return y},d.apply(null,arguments)}function p(y,h,D){return(h=m(h))in y?Object.defineProperty(y,h,{value:D,enumerable:!0,configurable:!0,writable:!0}):y[h]=D,y}function m(y){var h=b(y,"string");return typeof h=="symbol"?h:h+""}function b(y,h){if(typeof y!="object"||!y)return y;var D=y[Symbol.toPrimitive];if(D!==void 0){var x=D.call(y,h);if(typeof x!="object")return x;throw new TypeError("@@toPrimitive must return a primitive value.")}return(h==="string"?String:Number)(y)}class g extends t.Component{static getDerivedStateFromProps(h,D){let{position:x}=h,{prevPropsPosition:S}=D;return x&&(!S||x.x!==S.x||x.y!==S.y)?((0,c.default)("Draggable: getDerivedStateFromProps %j",{position:x,prevPropsPosition:S}),{x:x.x,y:x.y,prevPropsPosition:{...x}}):null}constructor(h){super(h),p(this,"onDragStart",(D,x)=>{if((0,c.default)("Draggable: onDragStart: %j",x),this.props.onStart(D,(0,s.createDraggableData)(this,x))===!1)return!1;this.setState({dragging:!0,dragged:!0})}),p(this,"onDrag",(D,x)=>{if(!this.state.dragging)return!1;(0,c.default)("Draggable: onDrag: %j",x);const S=(0,s.createDraggableData)(this,x),w={x:S.x,y:S.y,slackX:0,slackY:0};if(this.props.bounds){const{x:B,y:M}=w;w.x+=this.state.slackX,w.y+=this.state.slackY;const[P,k]=(0,s.getBoundPosition)(this,w.x,w.y);w.x=P,w.y=k,w.slackX=this.state.slackX+(B-w.x),w.slackY=this.state.slackY+(M-w.y),S.x=w.x,S.y=w.y,S.deltaX=w.x-this.state.x,S.deltaY=w.y-this.state.y}if(this.props.onDrag(D,S)===!1)return!1;this.setState(w)}),p(this,"onDragStop",(D,x)=>{if(!this.state.dragging||this.props.onStop(D,(0,s.createDraggableData)(this,x))===!1)return!1;(0,c.default)("Draggable: onDragStop: %j",x);const w={dragging:!1,slackX:0,slackY:0};if(!!this.props.position){const{x:B,y:M}=this.props.position;w.x=B,w.y=M}this.setState(w)}),this.state={dragging:!1,dragged:!1,x:h.position?h.position.x:h.defaultPosition.x,y:h.position?h.position.y:h.defaultPosition.y,prevPropsPosition:{...h.position},slackX:0,slackY:0,isElementSVG:!1},h.position&&!(h.onDrag||h.onStop)&&console.warn("A `position` was applied to this <Draggable>, without drag handlers. This will make this component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the `position` of this element.")}componentDidMount(){typeof window.SVGElement<"u"&&this.findDOMNode()instanceof window.SVGElement&&this.setState({isElementSVG:!0})}componentWillUnmount(){this.state.dragging&&this.setState({dragging:!1})}findDOMNode(){var h,D;return((D=(h=this.props)==null?void 0:h.nodeRef)==null?void 0:D.current)??r.default.findDOMNode(this)}render(){const{axis:h,bounds:D,children:x,defaultPosition:S,defaultClassName:w,defaultClassNameDragging:T,defaultClassNameDragged:B,position:M,positionOffset:P,scale:k,...V}=this.props;let J={},Oe=null;const Me=!!!M||this.state.dragging,Se=M||S,we={x:(0,s.canDragX)(this)&&Me?this.state.x:Se.x,y:(0,s.canDragY)(this)&&Me?this.state.y:Se.y};this.state.isElementSVG?Oe=(0,i.createSVGTransform)(we,P):J=(0,i.createCSSTransform)(we,P);const tt=(0,o.clsx)(x.props.className||"",w,{[T]:this.state.dragging,[B]:this.state.dragged});return t.createElement(l.default,d({},V,{onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop}),t.cloneElement(t.Children.only(x),{className:tt,style:{...x.props.style,...J},transform:Oe}))}}e.default=g,p(g,"displayName","Draggable"),p(g,"propTypes",{...l.default.propTypes,axis:n.default.oneOf(["both","x","y","none"]),bounds:n.default.oneOfType([n.default.shape({left:n.default.number,right:n.default.number,top:n.default.number,bottom:n.default.number}),n.default.string,n.default.oneOf([!1])]),defaultClassName:n.default.string,defaultClassNameDragging:n.default.string,defaultClassNameDragged:n.default.string,defaultPosition:n.default.shape({x:n.default.number,y:n.default.number}),positionOffset:n.default.shape({x:n.default.oneOfType([n.default.number,n.default.string]),y:n.default.oneOfType([n.default.number,n.default.string])}),position:n.default.shape({x:n.default.number,y:n.default.number}),className:a.dontSetMe,style:a.dontSetMe,transform:a.dontSetMe}),p(g,"defaultProps",{...l.default.defaultProps,axis:"both",bounds:!1,defaultClassName:"react-draggable",defaultClassNameDragging:"react-draggable-dragging",defaultClassNameDragged:"react-draggable-dragged",defaultPosition:{x:0,y:0},scale:1})})($i);const{default:qi,DraggableCore:bf}=$i;sn.exports=qi;sn.exports.default=qi;sn.exports.DraggableCore=bf;var yf=sn.exports,gr={};gr.__esModule=!0;gr.cloneElement=Cf;var vf=xf(v);function xf(e){return e&&e.__esModule?e:{default:e}}function fo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function po(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?fo(Object(n),!0).forEach(function(r){Df(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fo(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function Df(e,t,n){return t=Sf(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Sf(e){var t=wf(e,"string");return typeof t=="symbol"?t:String(t)}function wf(e,t){if(typeof e!="object"||e===null)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t);if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Cf(e,t){return t.style&&e.props.style&&(t.style=po(po({},e.props.style),t.style)),t.className&&e.props.className&&(t.className=e.props.className+" "+t.className),vf.default.cloneElement(e,t)}var Et={};Et.__esModule=!0;Et.resizableProps=void 0;var L=If(St);function If(e){return e&&e.__esModule?e:{default:e}}var Ef={axis:L.default.oneOf(["both","x","y","none"]),className:L.default.string,children:L.default.element.isRequired,draggableOpts:L.default.shape({allowAnyClick:L.default.bool,cancel:L.default.string,children:L.default.node,disabled:L.default.bool,enableUserSelectHack:L.default.bool,offsetParent:L.default.node,grid:L.default.arrayOf(L.default.number),handle:L.default.string,nodeRef:L.default.object,onStart:L.default.func,onDrag:L.default.func,onStop:L.default.func,onMouseDown:L.default.func,scale:L.default.number}),height:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];var o=n[0];if(o.axis==="both"||o.axis==="y"){var i;return(i=L.default.number).isRequired.apply(i,n)}return L.default.number.apply(L.default,n)},handle:L.default.oneOfType([L.default.node,L.default.func]),handleSize:L.default.arrayOf(L.default.number),lockAspectRatio:L.default.bool,maxConstraints:L.default.arrayOf(L.default.number),minConstraints:L.default.arrayOf(L.default.number),onResizeStop:L.default.func,onResizeStart:L.default.func,onResize:L.default.func,resizeHandles:L.default.arrayOf(L.default.oneOf(["s","w","e","n","sw","nw","se","ne"])),transformScale:L.default.number,width:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];var o=n[0];if(o.axis==="both"||o.axis==="x"){var i;return(i=L.default.number).isRequired.apply(i,n)}return L.default.number.apply(L.default,n)}};Et.resizableProps=Ef;It.__esModule=!0;It.default=void 0;var dt=Nf(v),Of=yf,Pf=gr,Rf=Et,Af=["children","className","draggableOpts","width","height","handle","handleSize","lockAspectRatio","axis","minConstraints","maxConstraints","onResize","onResizeStop","onResizeStart","resizeHandles","transformScale"];function Yi(e){if(typeof WeakMap!="function")return null;var t=new WeakMap,n=new WeakMap;return(Yi=function(o){return o?n:t})(e)}function Nf(e,t){if(e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var n=Yi(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(i!=="default"&&Object.prototype.hasOwnProperty.call(e,i)){var s=o?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(r,i,s):r[i]=e[i]}return r.default=e,n&&n.set(e,r),r}function $n(){return $n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$n.apply(this,arguments)}function Tf(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function go(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function On(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?go(Object(n),!0).forEach(function(r){Mf(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):go(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function Mf(e,t,n){return t=Bf(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Bf(e){var t=_f(e,"string");return typeof t=="symbol"?t:String(t)}function _f(e,t){if(typeof e!="object"||e===null)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t);if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Lf(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,Fn(e,t)}function Fn(e,t){return Fn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,o){return r.__proto__=o,r},Fn(e,t)}var mr=function(e){Lf(t,e);function t(){for(var r,o=arguments.length,i=new Array(o),s=0;s<o;s++)i[s]=arguments[s];return r=e.call.apply(e,[this].concat(i))||this,r.handleRefs={},r.lastHandleRect=null,r.slack=null,r}var n=t.prototype;return n.componentWillUnmount=function(){this.resetData()},n.resetData=function(){this.lastHandleRect=this.slack=null},n.runConstraints=function(o,i){var s=this.props,a=s.minConstraints,l=s.maxConstraints,c=s.lockAspectRatio;if(!a&&!l&&!c)return[o,i];if(c){var f=this.props.width/this.props.height,u=o-this.props.width,d=i-this.props.height;Math.abs(u)>Math.abs(d*f)?i=o/f:o=i*f}var p=o,m=i,b=this.slack||[0,0],g=b[0],y=b[1];return o+=g,i+=y,a&&(o=Math.max(a[0],o),i=Math.max(a[1],i)),l&&(o=Math.min(l[0],o),i=Math.min(l[1],i)),this.slack=[g+(p-o),y+(m-i)],[o,i]},n.resizeHandler=function(o,i){var s=this;return function(a,l){var c=l.node,f=l.deltaX,u=l.deltaY;o==="onResizeStart"&&s.resetData();var d=(s.props.axis==="both"||s.props.axis==="x")&&i!=="n"&&i!=="s",p=(s.props.axis==="both"||s.props.axis==="y")&&i!=="e"&&i!=="w";if(!(!d&&!p)){var m=i[0],b=i[i.length-1],g=c.getBoundingClientRect();if(s.lastHandleRect!=null){if(b==="w"){var y=g.left-s.lastHandleRect.left;f+=y}if(m==="n"){var h=g.top-s.lastHandleRect.top;u+=h}}s.lastHandleRect=g,b==="w"&&(f=-f),m==="n"&&(u=-u);var D=s.props.width+(d?f/s.props.transformScale:0),x=s.props.height+(p?u/s.props.transformScale:0),S=s.runConstraints(D,x);D=S[0],x=S[1];var w=D!==s.props.width||x!==s.props.height,T=typeof s.props[o]=="function"?s.props[o]:null,B=o==="onResize"&&!w;T&&!B&&(a.persist==null||a.persist(),T(a,{node:c,size:{width:D,height:x},handle:i})),o==="onResizeStop"&&s.resetData()}}},n.renderResizeHandle=function(o,i){var s=this.props.handle;if(!s)return dt.createElement("span",{className:"react-resizable-handle react-resizable-handle-"+o,ref:i});if(typeof s=="function")return s(o,i);var a=typeof s.type=="string",l=On({ref:i},a?{}:{handleAxis:o});return dt.cloneElement(s,l)},n.render=function(){var o=this,i=this.props,s=i.children,a=i.className,l=i.draggableOpts;i.width,i.height,i.handle,i.handleSize,i.lockAspectRatio,i.axis,i.minConstraints,i.maxConstraints,i.onResize,i.onResizeStop,i.onResizeStart;var c=i.resizeHandles;i.transformScale;var f=Tf(i,Af);return(0,Pf.cloneElement)(s,On(On({},f),{},{className:(a?a+" ":"")+"react-resizable",children:[].concat(s.props.children,c.map(function(u){var d,p=(d=o.handleRefs[u])!=null?d:o.handleRefs[u]=dt.createRef();return dt.createElement(Of.DraggableCore,$n({},l,{nodeRef:p,key:"resizableHandle-"+u,onStop:o.resizeHandler("onResizeStop",u),onStart:o.resizeHandler("onResizeStart",u),onDrag:o.resizeHandler("onResize",u)}),o.renderResizeHandle(u,p))}))}))},t}(dt.Component);It.default=mr;mr.propTypes=Rf.resizableProps;mr.defaultProps={axis:"both",handleSize:[20,20],lockAspectRatio:!1,minConstraints:[20,20],maxConstraints:[1/0,1/0],resizeHandles:["se"],transformScale:1};var un={};un.__esModule=!0;un.default=void 0;var Pn=Gf(v),$f=Xi(St),Ff=Xi(It),Wf=Et,kf=["handle","handleSize","onResize","onResizeStart","onResizeStop","draggableOpts","minConstraints","maxConstraints","lockAspectRatio","axis","width","height","resizeHandles","style","transformScale"];function Xi(e){return e&&e.__esModule?e:{default:e}}function Ki(e){if(typeof WeakMap!="function")return null;var t=new WeakMap,n=new WeakMap;return(Ki=function(o){return o?n:t})(e)}function Gf(e,t){if(e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var n=Ki(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(i!=="default"&&Object.prototype.hasOwnProperty.call(e,i)){var s=o?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(r,i,s):r[i]=e[i]}return r.default=e,n&&n.set(e,r),r}function Wn(){return Wn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Wn.apply(this,arguments)}function mo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function Gt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?mo(Object(n),!0).forEach(function(r){zf(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):mo(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function zf(e,t,n){return t=Hf(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Hf(e){var t=jf(e,"string");return typeof t=="symbol"?t:String(t)}function jf(e,t){if(typeof e!="object"||e===null)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t);if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Uf(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function Vf(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,kn(e,t)}function kn(e,t){return kn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,o){return r.__proto__=o,r},kn(e,t)}var Ji=function(e){Vf(t,e);function t(){for(var r,o=arguments.length,i=new Array(o),s=0;s<o;s++)i[s]=arguments[s];return r=e.call.apply(e,[this].concat(i))||this,r.state={width:r.props.width,height:r.props.height,propsWidth:r.props.width,propsHeight:r.props.height},r.onResize=function(a,l){var c=l.size;r.props.onResize?(a.persist==null||a.persist(),r.setState(c,function(){return r.props.onResize&&r.props.onResize(a,l)})):r.setState(c)},r}t.getDerivedStateFromProps=function(o,i){return i.propsWidth!==o.width||i.propsHeight!==o.height?{width:o.width,height:o.height,propsWidth:o.width,propsHeight:o.height}:null};var n=t.prototype;return n.render=function(){var o=this.props,i=o.handle,s=o.handleSize;o.onResize;var a=o.onResizeStart,l=o.onResizeStop,c=o.draggableOpts,f=o.minConstraints,u=o.maxConstraints,d=o.lockAspectRatio,p=o.axis;o.width,o.height;var m=o.resizeHandles,b=o.style,g=o.transformScale,y=Uf(o,kf);return Pn.createElement(Ff.default,{axis:p,draggableOpts:c,handle:i,handleSize:s,height:this.state.height,lockAspectRatio:d,maxConstraints:u,minConstraints:f,onResizeStart:a,onResize:this.onResize,onResizeStop:l,resizeHandles:m,transformScale:g,width:this.state.width},Pn.createElement("div",Wn({},y,{style:Gt(Gt({},b),{},{width:this.state.width+"px",height:this.state.height+"px"})})))},t}(Pn.Component);un.default=Ji;Ji.propTypes=Gt(Gt({},Wf.resizableProps),{},{children:$f.default.element});ur.exports=function(){throw new Error("Don't instantiate Resizable directly! Use require('react-resizable').Resizable")};var qf=ur.exports.Resizable=It.default;ur.exports.ResizableBox=un.default;const Yf=e=>{const{onResize:t,width:n,onResizeStart:r,onResizeStop:o,resizable:i,...s}=e;if(!t&&i!==!0)return G.jsx("th",{...s});const a=typeof n=="number"?n:1;return G.jsx(qf,{width:a,height:0,handle:G.jsx("span",{className:"react-resizable-handle",style:{position:"absolute",right:0,top:0,height:"100%",width:0,marginRight:0,cursor:"col-resize",userSelect:"none"},onClick:l=>{l.stopPropagation()},onMouseDown:l=>{l.stopPropagation();try{document.body.style.cursor="col-resize",document.body.style.userSelect="none"}catch{}r&&r(l,{size:{width:0,height:0}})}}),onResize:t,onResizeStart:(l,c)=>{try{document.body.style.cursor="col-resize",document.body.style.userSelect="none"}catch{}r&&r(l,c)},onResizeStop:(l,c)=>{try{document.body.style.cursor="",document.body.style.userSelect=""}catch{}o&&o(l,c)},draggableOpts:{enableUserSelectHack:!1},children:G.jsx("th",{...s})})},Xf=({columns:e,resizableColumns:t=!1,...n})=>{const[r,o]=v.useState(()=>t?e.map(c=>{const f=c&&c.width!=null?parseInt(String(c.width),10):void 0;return{...c,width:typeof c.width=="number"?c.width:Number.isFinite(f)?f:void 0,minWidth:typeof c.minWidth=="number"?c.minWidth:void 0,resizable:c.resizable!==!1}}):e);v.useEffect(()=>{if(!t){o(e);return}const c=e.map(f=>{const u=f&&(f.key??f.dataIndex),d=r.find(m=>(m.key??m.dataIndex)===u),p=f&&f.width!=null?parseInt(String(f.width),10):void 0;return{...f,width:typeof(d==null?void 0:d.width)=="number"?d.width:typeof f.width=="number"?f.width:Number.isFinite(p)?p:void 0,minWidth:typeof f.minWidth=="number"?f.minWidth:void 0,resizable:f.resizable!==!1}});o(c)},[e,t]);const i=v.useCallback(c=>(f,{size:u})=>{o(d=>{const p=[...d];if(!p[c])return d;const m=typeof p[c].minWidth=="number"?p[c].minWidth:0,b=Math.max(u.width,m);return p[c].width!==b&&(p[c]={...p[c],width:b}),p})},[]),s=v.useCallback(c=>f=>{const u=f==null?void 0:f.target,d=u!=null&&u.closest?u.closest("th"):null,p=d?d.getBoundingClientRect().width:void 0;p&&o(m=>{const b=[...m];if(!b[c])return m;const g=typeof b[c].minWidth=="number"?b[c].minWidth:0,y=Math.max(p,g);return b[c].width!==y&&(b[c]={...b[c],width:y}),b})},[]),a=v.useMemo(()=>t?r.map((c,f)=>{if(c.resizable===!1)return c;const u=typeof c.width=="number"?c.width:void 0;return{...c,resizable:!0,onHeaderCell:d=>({width:u,onResize:i(f),onResizeStart:s(f),...c.onHeaderCell?c.onHeaderCell(d):{}})}}):e,[r,i,t]),l=v.useMemo(()=>{if(t)return{header:{cell:Yf}}},[t]);return G.jsx(vo,{...n,columns:a,components:l,className:`${n.className||""} ${t?"resizable-table":""}`})};xo.extend(hs);xo.extend(bs);const Kf=e=>{const t={};return e.forEach(n=>{t[n.name]=n.matchMode}),t},Jf=(e,t)=>Object.keys(e).map(n=>{const r=t[n]||"exact",o=e[n]||[];return{field:n,filters:o.map(i=>({value:i,matchMode:r}))}}),Qf=({onChange:e})=>G.jsx(yo,{placeholder:"Search",allowClear:!0,onChange:e,style:{width:280,height:"32px",float:"right",borderRadius:"2px"}}),rp=v.forwardRef(({quantity:e,columns:t,rowSelection:n,matchFieldsList:r,searchFieldsList:o,extraButton:i,helpDraw:s,fetchAPIInfo:a,fetchAPIParams:l,isShowPagination:c,disableInternalRowSelection:f,showColumnSelector:u=!1,columnsOrder:d=!1,resizableColumns:p=!1,tableId:m,...b},g)=>{const{setPref:y}=cs(),[h,D]=v.useState([]),[x,S]=v.useState(!1),w=!!(m&&(d||u)),{data:T,isLoading:B}=us({enabled:w}),M=v.useMemo(()=>t,[JSON.stringify(t==null?void 0:t.map(I=>({key:I.key,dataIndex:I.dataIndex})))]),P=v.useCallback(I=>{if(!T)return null;for(const _ of T)if(_.tag===I)return _.value;return null},[T]),k=v.useCallback(()=>M.map(I=>I.key||I.dataIndex),[M]);v.useEffect(()=>{if(!w){!x&&M&&(D(k()),S(!0));return}if(!(!m||!M||x||B)){try{const I=P(`table_${m}_visible_columns`),_=P(`table_${m}_column_order`);let R=[];if(I?R=JSON.parse(I):_?R=JSON.parse(_):R=k(),_){const $=JSON.parse(_),E=[];$.forEach(O=>{R.includes(O)&&E.push(O)}),R.forEach(O=>{E.includes(O)||E.push(O)}),R=E}D(R)}catch{D(k())}S(!0)}},[m,M,T,B,x,w,P,k]);const V=v.useMemo(()=>{if(!M)return[];const I=E=>{const O=new Set(M.filter(U=>U.fixed==="left").map(U=>U.key||U.dataIndex)),F=new Set(M.filter(U=>U.fixed==="right").map(U=>U.key||U.dataIndex)),z=[],Y=[],A=[];return E.forEach(U=>{O.has(U)?z.push(U):F.has(U)?A.push(U):Y.push(U)}),[...z,...Y,...A]},_=h.length>0?h:M.map(E=>E.key||E.dataIndex),R=I(_);return M.filter(E=>{const O=E.key||E.dataIndex;return R.includes(O)}).sort((E,O)=>{const F=E.key||E.dataIndex,z=O.key||O.dataIndex,Y=R.indexOf(F),A=R.indexOf(z);return Y-A})},[M,h]),J=v.useCallback(async I=>{if(D(I),!!m)try{await y({preference:`table_${m}_visible_columns`,value:JSON.stringify(I)})}catch{}},[m,y]),Oe=v.useCallback(async I=>{if(m)try{await y({preference:`table_${m}_column_order`,value:JSON.stringify(I)})}catch{}},[m,y]),ae=v.useCallback(I=>{const _=M.filter(A=>A.columnsFix).map(A=>A.key||A.dataIndex),R=Array.from(new Set([...I,..._])),$=new Set(M.filter(A=>A.fixed==="left").map(A=>A.key||A.dataIndex)),E=new Set(M.filter(A=>A.fixed==="right").map(A=>A.key||A.dataIndex)),O=[],F=[],z=[];R.forEach(A=>{$.has(A)?O.push(A):E.has(A)?z.push(A):F.push(A)});const Y=[...O,...F,...z];J(Y)},[M,J]),Me=v.useCallback(I=>{const _=M.filter(O=>O.fixed==="left").map(O=>O.key||O.dataIndex),R=M.filter(O=>O.fixed==="right").map(O=>O.key||O.dataIndex);let $=I.filter(O=>!_.includes(O)&&!R.includes(O));const E=[..._,...$,...R];Oe(E)},[Oe,M]),Se=v.useMemo(()=>p?V.map(I=>{const _=I.key||I.dataIndex,R=M.find(O=>(O.key||O.dataIndex)===_)||I,$=R.resizable!==!1,E={...R,...I};return E.key=R.key||R.dataIndex||_,$?(E.resizable=!0,E.minWidth=R.minWidth!==void 0?R.minWidth:E.minWidth,E.width=E.width!==void 0?E.width:R.width):E.resizable=!1,E}):V,[V,p,M]),we=v.useMemo(()=>u?[...Se,{title:G.jsx("div",{children:G.jsx(Ld,{columns:t,visibleColumns:h,onChange:ae,draggable:d,onDragEnd:Me})}),key:"columnSelector",width:44,fixed:"right",resizable:!1,onHeaderCell:()=>({style:{width:"100%",padding:0,height:"54px",display:"flex",alignItems:"center",justifyContent:"center"}})}]:Se,[Se,u,t,h,ae,d,Me]),[tt,dn,ve,je,Pe,nt,rt,Re,ge,Ot]=ds(o,!1),Ae=Kf(r||[]),We=fs(),[X,K]=v.useState(n?n.selectedRowKeys||[]:[]),[Q,Ne]=v.useState(n?n.selectedRows||[]:[]),[re,Ue]=v.useState([]),[Ce,Ve]=v.useState([]),[Te,fn]=v.useState({}),[hr,Qi]=v.useState({}),[ot,br]=v.useState({}),[pn,Zi]=v.useState({}),yr=v.useCallback(I=>{for(const _ in I)if(Object.prototype.hasOwnProperty.call(I,_)){const R=I[_];if(R.defaultSortOrder!==null)return[R.dataIndex,R.defaultSortOrder]}return[void 0,void 0]},[]),vr=v.useCallback((I,_)=>{const R=_?X.concat([I.id]):X.filter(E=>E!==I.id);_?(Ue(re.filter(E=>E!==I.id)),Ve(Ce.filter(E=>E.id!==I.id))):(Ue([...re,I.id]),Ve([...Ce,I]));const $=_?[...Q,I]:Q.filter(E=>E.id!==I.id);if(Object.prototype.hasOwnProperty.call(I,"selected")){const E={...Te};I.selected===!1&&_?E[I.id]="add":I.selected===!1&&!_||I.selected===!0&&_?delete E[I.id]:I.selected===!0&&!_&&(E[I.id]="remove"),fn(E)}$.forEach(E=>{if(E.children&&E.children.some(F=>F.id===I.id)){const F=$.findIndex(Y=>Y.id===E.id);F>-1&&$.splice(F,1);const z=R.findIndex(Y=>Y===E.id);z>-1&&R.splice(z,1)}}),Object.prototype.hasOwnProperty.call(I,"children")&&(_?I.children.forEach(E=>{R.includes(E.id)||R.push(E.id),$.some(O=>O.id===E.id)||$.push(E)}):I.children.map(O=>O.id).forEach(O=>{const F=$.findIndex(Y=>Y.id===O);F>-1&&$.splice(F,1);const z=R.findIndex(Y=>Y===O);z>-1&&R.splice(z,1)})),K(R),Ne($),n&&n.onChange&&n.onChange(R,$)},[X,re,Ce,Q,Te,n]),xr=v.useCallback((I,_,R)=>{if(e){const $=X.length,E=e-$;if(I&&E<=0)return;const O=I?R.slice(0,E):R,F=O.map(A=>A.id),z=I?Array.from(new Set([...X,...F])):X.filter(A=>!F.includes(A));K(z);const Y=I?Array.from(new Set([...Q,...O])):Q.filter(A=>!F.includes(A.id));if(Ne(Y),I?(Ue(re.filter(A=>!F.includes(A))),Ve(Ce.filter(A=>!F.includes(A.id)))):(Ue([...re,...F]),Ve([...Ce,...O])),R.length>0&&Object.prototype.hasOwnProperty.call(R[0],"selected")){const A={...Te};O.forEach(U=>{U.selected===!1&&I?A[U.id]="add":U.selected===!1&&!I||U.selected===!0&&I?delete A[U.id]:U.selected===!0&&!I&&(A[U.id]="remove")}),fn(A)}n&&n.onChange&&n.onChange(z,Y)}else{const $=R.map(F=>F.id),E=I?X.concat($):X.filter(F=>!$.includes(F));K(E),I?(Ue(re.filter(F=>!$.includes(F))),Ve(Ce.filter(F=>!$.includes(F.id)))):(Ue([...re,...$]),Ve([...Ce,...R]));const O=I?[...Q,...R]:Q.filter(F=>!$.includes(F.id));if(Ne(O),R.length>0&&Object.prototype.hasOwnProperty.call(R[0],"selected")){const F={...Te};R.forEach(z=>{z.selected===!1&&I?F[z.id]="add":z.selected===!1&&!I||z.selected===!0&&I?delete F[z.id]:z.selected===!0&&!I&&(F[z.id]="remove"),Qi(Y=>({...Y,[z.id]:z}))}),fn(F)}n&&n.onChange&&n.onChange(E,O)}},[e,X,Q,re,Ce,Te,n]),it=v.useMemo(()=>(n==null?void 0:n.type)==="radio"||f&&n?n:{selectedRowKeys:X,onSelect:vr,onSelectAll:xr,getCheckboxProps:n==null?void 0:n.getCheckboxProps,fixed:n==null?void 0:n.fixed,checkStrictly:n==null?void 0:n.checkStrictly},[f,n==null?void 0:n.type,n==null?void 0:n.getCheckboxProps,n==null?void 0:n.fixed,n==null?void 0:n.checkStrictly,X,vr,xr]);v.useImperativeHandle(g,()=>({refreshTable(){st()},setTableLoading(I){Re(I)},getSelectedRow:()=>({tableSelectedRowKey:X,tableSelectedRows:Q}),getRemovedRow:()=>({tableRemovedRowKey:re,tableRemovedRows:Ce}),clearSelectedRow:()=>{K([]),Ne([])},getOperations:()=>Te,getOperationRowsMappings:()=>hr,getTableData:()=>Pe,clearAndRefresh:()=>{K([]),Ne([]),st(!0)},refreshAndSaveSelectedRow:()=>{st(!0)}}),[X,Q,re,Ce,Te,hr,Pe,Re,K,Ne]);const st=v.useCallback(async(I=!1)=>{Re(!0);try{const _=pn?Jf(pn,Ae):[],R=[];ot.field&&ot.order&&R.push({field:ot.field,order:ot.order==="ascend"?"asc":"desc"});let $=await a(...l?[...l,ge.current,ge.pageSize,_,R,ve]:[ge.current,ge.pageSize,_,R,ve]);if($.data.length===0&&$.total!==0){const O=Math.ceil($.total/$.pageSize);$=await a(...l?[...l,O,ge.pageSize,[],[],ve]:[O,ge.pageSize,[],[],ve])}const E=JSON.parse(JSON.stringify($.data));if(!I&&(E.forEach(O=>{O.selected=Te[O.id]==="add"?!0:O.selected}),E.every(O=>"selected"in O))){const O=E.filter(A=>A.selected).map(A=>A.id),F=X?E.filter(A=>X.indexOf(A.id)>-1).map(A=>A.id):[],z=re?E.filter(A=>re.indexOf(A.id)>-1).map(A=>A.id):[],Y=Array.from(new Set([...X||[],...O,...F])).filter(A=>z.indexOf(A)===-1);K(Y),Ne(E.filter(A=>A.selected))}nt(E),Ot(O=>({...O,total:$.total,current:$.page,pageSize:$.pageSize}))}catch{}finally{Re(!1)}},[pn,Ae,ot,a,l,ge.current,ge.pageSize,ve,Te,X,re]);v.useEffect(()=>{const[I,_]=yr(t);I&&br({field:I,order:_})},[t,yr]),v.useEffect(()=>{st()},[st]);const es=v.useCallback(I=>{We(ps(!1)),We(gs("")),it&&it.selectedRowKeys&&(it.selectedRowKeys=[]),Ne([]),K([]),je({fields:o,value:I.target.value})},[We,it,o,je]),ts=v.useCallback(async(I,_,R)=>{await new Promise($=>setTimeout($,100)),br(R),Zi(_),await ms(I,_,R,Ot,ve,a,l,nt,Ae,Re,X,Q,K,Ne,re)},[ve,a,l,Ae,X,Q,re]);return G.jsx("div",{children:G.jsxs(Cr,{vertical:!0,children:[G.jsxs(Cr,{gap:"middle",style:{marginBottom:"20px"},children:[i,G.jsx("div",{style:{flexGrow:1}}),o?G.jsx(Qf,{onChange:es}):null,s]}),(()=>{const I=p?Xf:vo,_={rowSelection:n?it:null,columns:we,bordered:!0,rowKey:R=>R.id,loading:rt,dataSource:Pe,pagination:o||c?ge:!1,onChange:ts,scroll:{x:"max-content"},...p?{resizableColumns:!0}:{},...b};return G.jsx(I,{..._})})()]})})});export{_d as C,Uu as D,ep as P,rp as W,fd as a,tp as c,St as p};
