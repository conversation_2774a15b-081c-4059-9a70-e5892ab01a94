import{r as o,m as Ca,k as xs,l as _t,n as Mt,o as bs,q as Ke,s as vs,t as ys,j as e,v as $e,w as We,x as wa,I as Sa,_ as Ta,y as Ut,b as K,z as Je,u as Oe,E as Se,G as js,H as D,J as ke,K as qe,N as Dt,M as Ie,D as oe,O as k,P as ne,B as Z,Q as Ee,U as Cs,V as Xe,F as De,W as ae,X as Fe,Y as Aa,Z as Ze,c as ve,T as ue,$ as Ot,a0 as ws,R as G,a1 as Ce,C as pt,a2 as Q,a3 as ta,a4 as gt,a5 as ut,g as U,h as F,a6 as Ss,a7 as Ts,a8 as As,i as ks,a9 as Is,aa as X,ab as te,ac as de,ad as _e,ae as Es,af as Fs,ag as Ps,ah as xt,S as ka,ai as Ye,aj as Ms,ak as bt,al as et,am as Rs,an as Nt,ao as _s,ap as Bt,aq as Us,ar as Ds,as as Os,at as Ns,au as Bs,av as Ls,aw as qs,ax as zs,ay as aa,az as Ia,A as Ea,aA as Vs,aB as Qs,aC as Lt,aD as Tt,aE as Gs,aF as Hs,aG as Ks,aH as $s,aI as At}from"./index-CHCmiRmn.js";import{i as Ws,c as H,a as W,D as sa,C as Js,b as Y,d as z,e as B}from"./CustomWirelessRangePicker-DYVOqnGv.js";import{u as Fa,F as qt,a as Pa,b as vt,c as ye}from"./index-LkKwRvEU.js";import{f as zt,g as Ge,a as Be,T as Ma,s as He,R as Ra,W as Xs,F as Zs,v as Ys,b as ht,c as en,d as tn,V as an}from"./tabs-Crg4tuJR.js";import{W as Vt,P}from"./CustomTable-CM5Sdauq.js";import{m as sn,s as nn,g as rn}from"./dateFormatting-yHFQ8l7H.js";import{F as yt,f as ln,u as on,a as dn}from"./Venues-DpPldi0z.js";import{p as _a,b as he,f as na}from"./Form-CYdW6Qd_.js";import{F as Qt}from"./FormModal-BzU8SPYc.js";import{u as cn,a as se}from"./useFastField-DAY0E64C.js";import{i as un}from"./icon_details-DTb3-MwP.js";import{c as hn}from"./use-descendant-B2Cex6zj.js";function fn(t){const{wasSelected:a,enabled:s,isSelected:n,mode:i="unmount"}=t;return!!(!s||n||i==="keepMounted"&&a)}function mn(){const t=o.useRef(new Map),a=t.current,s=o.useCallback((i,r,l,d)=>{t.current.set(l,{type:r,el:i,options:d}),i.addEventListener(r,l,d)},[]),n=o.useCallback((i,r,l,d)=>{i.removeEventListener(r,l,d),t.current.delete(l)},[]);return o.useEffect(()=>()=>{a.forEach((i,r)=>{n(i.el,i.type,r,i.options)})},[n,a]),{add:s,remove:n}}function kt(t){var i,r;const a=((r=(i=t.composedPath)==null?void 0:i.call(t))==null?void 0:r[0])??t.target,{tagName:s,isContentEditable:n}=a;return s!=="INPUT"&&s!=="TEXTAREA"&&n!==!0}function pn(t={}){const{ref:a,isDisabled:s,isFocusable:n,clickOnEnter:i=!0,clickOnSpace:r=!0,onMouseDown:l,onMouseUp:d,onClick:f,onKeyDown:c,onKeyUp:x,tabIndex:g,onMouseOver:h,onMouseLeave:j,...A}=t,[u,p]=o.useState(!0),[v,b]=o.useState(!1),m=mn(),I=_=>{_&&_.tagName!=="BUTTON"&&p(!1)},S=u?g:g||0,y=s&&!n,C=o.useCallback(_=>{if(s){_.stopPropagation(),_.preventDefault();return}_.currentTarget.focus(),f==null||f(_)},[s,f]),w=o.useCallback(_=>{v&&kt(_)&&(_.preventDefault(),_.stopPropagation(),b(!1),m.remove(document,"keyup",w,!1))},[v,m]),E=o.useCallback(_=>{if(c==null||c(_),s||_.defaultPrevented||_.metaKey||!kt(_.nativeEvent)||u)return;const ie=i&&_.key==="Enter";r&&_.key===" "&&(_.preventDefault(),b(!0)),ie&&(_.preventDefault(),_.currentTarget.click()),m.add(document,"keyup",w,!1)},[s,u,c,i,r,m,w]),T=o.useCallback(_=>{if(x==null||x(_),s||_.defaultPrevented||_.metaKey||!kt(_.nativeEvent)||u)return;r&&_.key===" "&&(_.preventDefault(),b(!1),_.currentTarget.click())},[r,u,s,x]),L=o.useCallback(_=>{_.button===0&&(b(!1),m.remove(document,"mouseup",L,!1))},[m]),N=o.useCallback(_=>{if(_.button!==0)return;if(s){_.stopPropagation(),_.preventDefault();return}u||b(!0),_.currentTarget.focus({preventScroll:!0}),m.add(document,"mouseup",L,!1),l==null||l(_)},[s,u,l,m,L]),M=o.useCallback(_=>{_.button===0&&(u||b(!1),d==null||d(_))},[d,u]),R=o.useCallback(_=>{if(s){_.preventDefault();return}h==null||h(_)},[s,h]),O=o.useCallback(_=>{v&&(_.preventDefault(),b(!1)),j==null||j(_)},[v,j]),q=Ca(a,I);return u?{...A,ref:q,type:"button","aria-disabled":y?void 0:s,disabled:y,onClick:C,onMouseDown:l,onMouseUp:d,onKeyUp:x,onKeyDown:c,onMouseOver:h,onMouseLeave:j}:{...A,ref:q,role:"button","data-active":xs(v),"aria-disabled":s?"true":void 0,tabIndex:y?void 0:S,onClick:C,onMouseDown:N,onMouseUp:M,onKeyUp:T,onKeyDown:E,onMouseOver:R,onMouseLeave:O}}const[gn,xn,bn,vn]=hn();function yn(t){const{defaultIndex:a,onChange:s,index:n,isManual:i,isLazy:r,lazyBehavior:l="unmount",orientation:d="horizontal",direction:f="ltr",...c}=t,[x,g]=o.useState(a??0),[h,j]=cn({defaultValue:a??0,value:n,onChange:s});o.useEffect(()=>{n!=null&&g(n)},[n]);const A=bn(),u=o.useId();return{id:`tabs-${t.id??u}`,selectedIndex:h,focusedIndex:x,setSelectedIndex:j,setFocusedIndex:g,isManual:i,isLazy:r,lazyBehavior:l,orientation:d,descendants:A,direction:f,htmlProps:c}}const[jn,jt]=_t({name:"TabsContext",errorMessage:"useTabsContext: `context` is undefined. Seems you forgot to wrap all tabs components within <Tabs />"});function Cn(t){const{focusedIndex:a,orientation:s,direction:n}=jt(),i=xn(),r=o.useCallback(l=>{const d=()=>{var m;const b=i.nextEnabled(a);b&&((m=b.node)==null||m.focus())},f=()=>{var m;const b=i.prevEnabled(a);b&&((m=b.node)==null||m.focus())},c=()=>{var m;const b=i.firstEnabled();b&&((m=b.node)==null||m.focus())},x=()=>{var m;const b=i.lastEnabled();b&&((m=b.node)==null||m.focus())},g=s==="horizontal",h=s==="vertical",j=l.key,A=n==="ltr"?"ArrowLeft":"ArrowRight",u=n==="ltr"?"ArrowRight":"ArrowLeft",v={[A]:()=>g&&f(),[u]:()=>g&&d(),ArrowDown:()=>h&&d(),ArrowUp:()=>h&&f(),Home:c,End:x}[j];v&&(l.preventDefault(),v(l))},[i,a,s,n]);return{...t,role:"tablist","aria-orientation":s,onKeyDown:Mt(t.onKeyDown,r)}}function wn(t){const{isDisabled:a=!1,isFocusable:s=!1,...n}=t,{setSelectedIndex:i,isManual:r,id:l,setFocusedIndex:d,selectedIndex:f}=jt(),{index:c,register:x}=vn({disabled:a&&!s}),g=c===f,h=()=>{i(c)},j=()=>{d(c),!r&&!(a&&s)&&i(c)};return{...pn({...n,ref:Ca(x,t.ref),isDisabled:a,isFocusable:s,onClick:Mt(t.onClick,h)}),id:Ua(l,c),role:"tab",tabIndex:g?0:-1,type:"button","aria-selected":g,"aria-controls":Da(l,c),onFocus:a?void 0:Mt(t.onFocus,j)}}const[Sn,Tn]=_t({});function An(t){const a=jt(),{id:s,selectedIndex:n}=a,r=bs(t.children).map((l,d)=>o.createElement(Sn,{key:l.key??d,value:{isSelected:d===n,id:Da(s,d),tabId:Ua(s,d),selectedIndex:n}},l));return{...t,children:r}}function kn(t){const{children:a,...s}=t,{isLazy:n,lazyBehavior:i}=jt(),{isSelected:r,id:l,tabId:d}=Tn(),f=o.useRef(!1);r&&(f.current=!0);const c=fn({wasSelected:f.current,isSelected:r,enabled:n,mode:i});return{tabIndex:0,...s,children:c?a:null,role:"tabpanel","aria-labelledby":d,hidden:!r,id:l}}function Ua(t,a){return`${t}--tab-${a}`}function Da(t,a){return`${t}--tabpanel-${a}`}const[In,Ct]=_t({name:"TabsStylesContext",errorMessage:`useTabsStyles returned is 'undefined'. Seems you forgot to wrap the components in "<Tabs />" `}),Oa=Ke(function(a,s){const n=vs("Tabs",a),{children:i,className:r,...l}=ys(a),{htmlProps:d,descendants:f,...c}=yn(l),x=o.useMemo(()=>c,[c]),{isFitted:g,...h}=d,j={position:"relative",...n.root};return e.jsx(gn,{value:f,children:e.jsx(jn,{value:x,children:e.jsx(In,{value:n,children:e.jsx($e.div,{className:We("chakra-tabs",r),ref:s,...h,__css:j,children:i})})})})});Oa.displayName="Tabs";const ot=Ke(function(a,s){const n=Ct(),i=wn({...a,ref:s}),r=wa({outline:"0",display:"flex",alignItems:"center",justifyContent:"center",...n.tab});return e.jsx($e.button,{...i,className:We("chakra-tabs__tab",a.className),__css:r})});ot.displayName="Tab";const Na=Ke(function(a,s){const n=Cn({...a,ref:s}),i=Ct(),r=wa({display:"flex",...i.tablist});return e.jsx($e.div,{...n,className:We("chakra-tabs__tablist",a.className),__css:r})});Na.displayName="TabList";const dt=Ke(function(a,s){const n=kn({...a,ref:s}),i=Ct();return e.jsx($e.div,{outline:"0",...n,className:We("chakra-tabs__tab-panel",a.className),__css:i.tabpanel})});dt.displayName="TabPanel";const Ba=Ke(function(a,s){const n=An(a),i=Ct();return e.jsx($e.div,{...n,width:"100%",ref:s,className:We("chakra-tabs__tab-panels",a.className),__css:i.tabpanels})});Ba.displayName="TabPanels";var En={icon:function(a,s){return{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M81.8 537.8a60.3 60.3 0 010-51.5C176.6 286.5 319.8 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c-192.1 0-335.4-100.5-430.2-300.2z",fill:s}},{tag:"path",attrs:{d:"M512 258c-161.3 0-279.4 81.8-362.7 254C232.6 684.2 350.7 766 512 766c161.4 0 279.5-81.8 362.7-254C791.4 339.8 673.3 258 512 258zm-4 430c-97.2 0-176-78.8-176-176s78.8-176 176-176 176 78.8 176 176-78.8 176-176 176z",fill:s}},{tag:"path",attrs:{d:"M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258s279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766z",fill:a}},{tag:"path",attrs:{d:"M508 336c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z",fill:a}}]}},name:"eye",theme:"twotone"},Fn=function(a,s){return o.createElement(Sa,Ta({},a,{ref:s,icon:En}))},Pn=o.forwardRef(Fn),Mn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 708c-22.1 0-40-17.9-40-40s17.9-40 40-40 40 17.9 40 40-17.9 40-40 40zm62.9-219.5a48.3 48.3 0 00-30.9 44.8V620c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8v-21.5c0-23.1 6.7-45.9 19.9-64.9 12.9-18.6 30.9-32.8 52.1-40.9 34-13.1 56-41.6 56-72.7 0-44.1-43.1-80-96-80s-96 35.9-96 80v7.6c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V420c0-39.3 17.2-76 48.4-103.3C430.4 290.4 470 276 512 276s81.6 14.5 111.6 40.7C654.8 344 672 380.7 672 420c0 57.8-38.1 109.8-97.1 132.5z"}}]},name:"question-circle",theme:"filled"},Rn=function(a,s){return o.createElement(Sa,Ta({},a,{ref:s,icon:Mn}))},_n=o.forwardRef(Rn);function Un(t){return new Dn(t)}class Dn{constructor(a){this.type="lazy",this.__isYupSchema__=!0,this.__inputType=void 0,this.__outputType=void 0,this._resolve=(s,n={})=>{let i=this.builder(s,n);if(!Ws(i))throw new TypeError("lazy() functions must return a valid schema");return i.resolve(n)},this.builder=a}resolve(a){return this._resolve(a.value,a)}cast(a,s){return this._resolve(a,s).cast(a,s)}validate(a,s,n){return this._resolve(a,s).validate(a,s,n)}validateSync(a,s){return this._resolve(a,s).validateSync(a,s)}validateAt(a,s,n){return this._resolve(s,n).validateAt(a,s,n)}validateSyncAt(a,s,n){return this._resolve(s,n).validateSyncAt(a,s,n)}describe(){return null}isValid(a,s){return this._resolve(a,s).isValid(a,s)}isValidSync(a,s){return this._resolve(a,s).isValidSync(a,s)}}const On=({id:t})=>{const{t:a}=K(),s=Je();return Oe(["get-board",t],()=>Se.get(`board/${t}`).then(({data:n})=>n),{enabled:t!=null&&t.length>0,onError:n=>{var i,r,l;((i=n.response)==null?void 0:i.status)!==404&&!s.isActive("board-fetching-error")&&s({id:"board-fetching-error",title:a("common.error"),description:a("crud.error_fetching_obj",{obj:a("analytics.board"),e:(l=(r=n==null?void 0:n.response)==null?void 0:r.data)==null?void 0:l.ErrorDescription}),status:"error",duration:5e3,isClosable:!0,position:"top-right"})}})},Nn=({id:t})=>{const{t:a}=K();return Je(),Oe(["get-board-devices",t],()=>Se.get(`board/${t}/devices`).then(({data:s})=>s.devices),{enabled:t!=null&&t.length>0,onError:s=>{var n,i,r;((n=s.response)==null?void 0:n.status)!==404&&D.error(a("crud.error_fetching_obj",{obj:a("analytics.board"),e:(r=(i=s==null?void 0:s.response)==null?void 0:i.data)==null?void 0:r.ErrorDescription}))}})},Bn=({venueId:t,mac:a,fromDate:s,endDate:n,refreshId:i})=>{const{t:r}=K(),l=Je();return Oe(["get-lifecycles",t,a,s,n,i],()=>Se.get(`wifiClientHistory/${a}?venue=${t}&countOnly=true&fromDate=${s}&endDate=${n}`).then(({data:d})=>d.count),{enabled:a!==void 0,onError:d=>{var f,c;l.isActive("lifecycle-count-fetching-error")||l({id:"lifecycle-count-fetching-error",title:r("common.error"),description:r("crud.error_fetching_obj",{obj:r("analytics.board"),e:(c=(f=d==null?void 0:d.response)==null?void 0:f.data)==null?void 0:c.ErrorDescription}),status:"error",duration:5e3,isClosable:!0,position:"top-right"})}})},ia=({pageInfo:t,venueId:a,mac:s,count:n,sortInfo:i,fromDate:r,endDate:l,refreshId:d})=>{let f="";return i&&i.length>0&&(f=`&orderBy=${i.map(c=>`${c.id}:${c.sort.charAt(0)}`).join(",")}`),Oe(["get-lifecycles-with-pagination",t,n,i,r,l,d],()=>Se.get(`wifiClientHistory/${s}?venue=${a}&limit=${(t==null?void 0:t.limit)??10}&offset=${((t==null?void 0:t.limit)??10)*((t==null?void 0:t.index)??1)}${f}&fromDate=${r}&endDate=${l}`).then(({data:c})=>c.entries),{keepPreviousData:!0,enabled:n!==void 0&&t!==void 0,onError:()=>[]})},Ln=({id:t,startTime:a,endTime:s,enabled:n=!0})=>{const{t:i}=K();return Je(),Oe(["get-venue-timepoints",t,a.toString(),s==null?void 0:s.toString()],()=>Se.get(`board/${t}/timepoints?maxRecords=1000&fromDate=${Math.floor(a.getTime()/1e3)}${s?`&endDate=${Math.floor(s.getTime()/1e3)}`:""}`).then(({data:r})=>r.points),{enabled:t!==void 0&&t!==""&&n,keepPreviousData:!0,staleTime:1/0,onError:r=>{var l,d,f;((l=r.response)==null?void 0:l.status)!==404&&D.error(i("crud.error_fetching_obj",{obj:i("analytics.board"),e:(f=(d=r==null?void 0:r.response)==null?void 0:d.data)==null?void 0:f.ErrorDescription}),5)}})},qn=()=>Ut(t=>Se.post("board/0",t)),zn=({id:t})=>{const a=js();return Ut(s=>Se.put(`board/${t}`,s),{onSuccess:()=>{a.invalidateQueries(["get-board",t])}})},Vn=()=>Ut(t=>Se.delete(`board/${t}`,{})),Qn=({id:t,visible:a,onClose:s})=>{var u;const{t:n}=K(),[i,r]=o.useState(ke()),[l,d]=o.useState(!1),f=qe({id:t}),c=qn(),x=Dt({id:t});o.useEffect(()=>{a&&r(ke())},[a]);const g=H().shape({interval:W().required(n("form.required")).moreThan(0).integer(),retention:W().required(n("form.required")).moreThan(0).integer()}),h=Fa({enableReinitialize:!0,validationSchema:g,validateOnMount:!0,initialValues:{name:((u=f.data)==null?void 0:u.name)??"",interval:60,retention:7,monitorSubVenues:!0},onSubmit:async(p,{setSubmitting:v,resetForm:b})=>{var m,I,S;try{const{name:y,interval:C,retention:w,monitorSubVenues:E}=p,T=w*24*60*60,L=await c.mutateAsync({name:y,venueList:[{id:t,name:y,interval:C,retention:T,monitorSubVenues:E}]}),N=(m=L==null?void 0:L.data)==null?void 0:m.id;await x.mutateAsync({params:{boards:[N]}}),D.success(n("crud.success_update_obj",{obj:n("venues.one")})),b(),s()}catch(y){D.error(n("crud.error_create_obj",{obj:n("analytics.board"),e:(S=(I=y==null?void 0:y.response)==null?void 0:I.data)==null?void 0:S.ErrorDescription}))}finally{v(!1)}}}),j=()=>{h.dirty?d(!0):(h.resetForm(),s())},A=()=>{h.resetForm(),d(!1),s()};return e.jsxs(e.Fragment,{children:[e.jsxs(Ie,{title:n("analytics.create_board"),open:a,onCancel:j,footer:[e.jsx(Z,{onClick:j,style:{width:100,height:36},children:n("common.cancel")},"cancel"),e.jsx(Z,{type:"primary",onClick:()=>h.submitForm(),disabled:h.isSubmitting,loading:h.isSubmitting,style:{width:100,height:36,backgroundColor:"#14C9BB",borderColor:"#14C9BB"},children:n("Apply")},"submit")],width:680,children:[e.jsx(oe,{style:{margin:"0px 0px 16px -24px",width:"calc(100% + 48px)"}}),e.jsxs(k,{layout:"horizontal",labelAlign:"left",style:{marginBottom:100},children:[e.jsx(k.Item,{label:e.jsxs("span",{style:{width:100,display:"inline-block"},children:[n("analytics.interval"),e.jsx("span",{style:{color:"red",marginLeft:4},children:"*"})]}),labelCol:{span:4},wrapperCol:{span:18},style:{marginBottom:24},validateStatus:h.errors.interval&&h.touched.interval?"error":"",help:h.errors.interval&&h.touched.interval&&h.errors.interval,children:e.jsxs("div",{className:"custom-input-wrapper",style:{position:"relative",width:200},children:[e.jsx(ne,{name:"interval",value:h.values.interval,onChange:p=>h.setFieldValue("interval",p??0),onBlur:()=>h.setFieldTouched("interval",!0),style:{width:"100%"},controls:!0,className:"always-show-controls"}),e.jsx("span",{className:"unit-label",style:{position:"absolute",right:30,top:"50%",transform:"translateY(-50%)",color:"#929A9E",fontSize:14},children:n("common.seconds")})]})}),e.jsx(k.Item,{label:e.jsxs("span",{style:{width:100,display:"inline-block"},children:[n("analytics.retention"),e.jsx("span",{style:{color:"red",marginLeft:4},children:"*"})]}),labelCol:{span:4},wrapperCol:{span:18},style:{marginBottom:24},validateStatus:h.errors.retention&&h.touched.retention?"error":"",help:h.errors.retention&&h.touched.retention&&h.errors.retention,children:e.jsxs("div",{className:"custom-input-wrapper",style:{position:"relative",width:200},children:[e.jsx(ne,{name:"retention",value:h.values.retention,onChange:p=>h.setFieldValue("retention",p??0),onBlur:()=>h.setFieldTouched("retention",!0),style:{width:"100%"},controls:!0,className:"always-show-controls"}),e.jsx("span",{className:"unit-label",style:{position:"absolute",right:30,top:"50%",transform:"translateY(-50%)",color:"#929A9E",fontSize:14},children:n("common.days")})]})})]}),e.jsx(oe,{style:{margin:"0px 0px 16px -24px",width:"calc(100% + 48px)"}})]},i),e.jsx(Ie,{title:n("Confirm Discard"),open:l,onOk:A,onCancel:()=>d(!1),okText:n("common.yes"),cancelText:n("common.cancel"),zIndex:2e3,getContainer:!1,children:e.jsx("p",{children:n("Are you sure you want to discard the changes?")})})]})},Gn=({boardId:t,venueId:a,onSuccess:s})=>{const{t:n}=K(),i=Vn(),r=Dt({id:a}),l=()=>{Ee(n("Are you sure? This will erase all recorded monitoring data for this venue."),async()=>{var d,f;try{await r.mutateAsync({params:{boards:[]}}),await i.mutateAsync(t),D.success(n("analytics.stop_monitoring_success")),s==null||s()}catch(c){D.error(n("analytics.stop_monitoring_error",{e:(f=(d=c==null?void 0:c.response)==null?void 0:d.data)==null?void 0:f.ErrorDescription}))}},()=>{},!0)};return e.jsx(Z,{className:"stop-button",onClick:l,style:{width:"133px",height:"36px"},loading:i.isLoading||r.isLoading,children:n("Stop Monitoring")})},Hn=({boardId:t,venueId:a,visible:s,onClose:n,onApplySuccess:i})=>{var A,u,p,v,b,m,I;const{t:r}=K(),[l,d]=o.useState(ke()),f=On({id:t}),c=zn({id:t}),x=qe({id:a});o.useEffect(()=>{s&&d(ke())},[s]);const g=H().shape({interval:W().required(r("form.required")).moreThan(0).integer(),retention:W().required(r("form.required")).moreThan(0).integer()}),h=Fa({enableReinitialize:!0,validationSchema:g,validateOnMount:!0,initialValues:{name:((A=x.data)==null?void 0:A.name)??"",interval:((p=(u=f.data)==null?void 0:u.venueList[0])==null?void 0:p.interval)??60,retention:(((b=(v=f.data)==null?void 0:v.venueList[0])==null?void 0:b.retention)??604800)/(24*60*60),monitorSubVenues:((I=(m=f.data)==null?void 0:m.venueList[0])==null?void 0:I.monitorSubVenues)??!0},onSubmit:async(S,{setSubmitting:y})=>{var C,w;try{const{name:E,interval:T,retention:L,monitorSubVenues:N}=S;await c.mutateAsync({name:E,venueList:[{id:a,name:E,interval:T,retention:L*24*60*60,monitorSubVenues:N}]}),D.success(r("crud.success_update_obj",{obj:r("analytics.board")})),h.resetForm(),n()}catch(E){D.error(r("crud.error_update_obj",{obj:r("analytics.board"),e:(w=(C=E==null?void 0:E.response)==null?void 0:C.data)==null?void 0:w.ErrorDescription}))}finally{y(!1)}}}),j=()=>{h.resetForm(),n()};return e.jsxs(Ie,{title:r("Monitoring"),open:s,onCancel:j,footer:[e.jsx(Z,{onClick:j,style:{color:"#14C9BB",borderColor:"#14C9BB",width:"100px",height:"36px"},children:r("common.cancel")},"cancel"),e.jsx(Z,{type:"primary",onClick:()=>h.submitForm(),disabled:h.isSubmitting,loading:h.isSubmitting,style:{width:"100px",height:"36px",backgroundColor:"#14C9BB",borderColor:"#14C9BB"},children:r("Apply")},"apply")],width:680,children:[e.jsx("style",{children:`
                  .always-show-controls .ant-input-number-handler-wrap {
                    opacity: 1 !important;
                    display: flex !important;
                    pointer-events: auto !important;
                  }
                  .custom-input-wrapper {
                    position: relative;
                    width: 200px;
                  }
                  .unit-label {
                    position: absolute;
                    right: 30px; /* 放在调整按钮左侧 */
                    top: 50%;
                    transform: translateY(-50%);
                    color: #929A9E;
                    font-size: 14px;
                  }
                  .ant-input-number-input {
                    padding-right: 50px !important; 
                  }
                `}),e.jsx(oe,{style:{margin:"0px 0px 16px -24px",width:"calc(100% + 48px)"}}),e.jsx("div",{style:{marginBottom:24},children:e.jsx(Gn,{boardId:t,venueId:a,onSuccess:()=>{n(),i==null||i(!0)}})}),e.jsxs(k,{layout:"horizontal",labelAlign:"left",style:{marginBottom:80},children:[e.jsx(k.Item,{label:e.jsxs("span",{style:{width:100,display:"inline-block"},children:[r("analytics.interval"),e.jsx("span",{style:{color:"red",marginLeft:4},children:"*"})]}),labelCol:{span:4},wrapperCol:{span:18},style:{marginBottom:24},validateStatus:h.errors.interval&&h.touched.interval?"error":"",help:h.errors.interval&&h.touched.interval?h.errors.interval:"",children:e.jsxs("div",{className:"custom-input-wrapper",children:[e.jsx(ne,{name:"interval",value:h.values.interval,onChange:S=>h.setFieldValue("interval",S??0),onBlur:()=>h.setFieldTouched("interval",!0),style:{width:"100%"},controls:!0,className:"always-show-controls"}),e.jsx("span",{className:"unit-label",children:r("common.seconds")})]})}),e.jsx(k.Item,{label:e.jsxs("span",{style:{width:100,display:"inline-block"},children:[r("analytics.retention"),e.jsx("span",{style:{color:"red",marginLeft:4},children:"*"})]}),labelCol:{span:4},wrapperCol:{span:18},style:{marginBottom:24},validateStatus:h.errors.retention&&h.touched.retention?"error":"",help:h.errors.retention&&h.touched.retention?h.errors.retention:"",children:e.jsxs("div",{className:"custom-input-wrapper",children:[e.jsx(ne,{name:"retention",value:h.values.retention,onChange:S=>h.setFieldValue("retention",S??0),onBlur:()=>h.setFieldTouched("retention",!0),style:{width:"100%"},controls:!0,className:"always-show-controls"}),e.jsx("span",{className:"unit-label",children:r("common.days")})]})})]}),e.jsx(oe,{style:{margin:"0px 0px 16px -24px",width:"calc(100% + 48px)"}})]},l)},Kn=({id:t,type:a})=>{const s=Cs(),[n,i]=o.useState(!1),r=!s.isLoading,l=o.useMemo(()=>r?s.entityFavorites.favorites.some(({id:c})=>c===t):!1,[t,r,s.entityFavorites.favorites]);return{isFavorite:l,onFavoriteClick:async()=>{i(!0),l?await s.entityFavorites.remove({id:t,type:a}):await s.entityFavorites.add({id:t,type:a}),i(!1)},isLoading:n,getFirstVenueFavoriteId:()=>{const c=s.entityFavorites.favorites.find(x=>x.type==="venue");return c?c.id:null},isReady:r}},{Title:rd,Link:$n}=ve,fe=(t,a,s)=>{const n=t[s],i=a[s];return n==null?1:i==null?-1:n===i?0:typeof n=="number"&&typeof i=="number"?n-i:typeof n=="boolean"&&typeof i=="boolean"?(i?1:0)-(n?1:0):typeof n=="string"&&!isNaN(Date.parse(n))&&typeof i=="string"&&!isNaN(Date.parse(i))?new Date(n).getTime()-new Date(i).getTime():String(n).localeCompare(String(i),void 0,{sensitivity:"base"})},Wn={data:P.instanceOf(Object),isOpen:P.bool.isRequired,onClose:P.func.isRequired,tableOptions:P.shape({prioritizedColumns:P.arrayOf(P.string),sortBy:P.arrayOf(P.shape({id:P.string,desc:P.bool}))})},Jn={data:null,tableOptions:null},Gt=({data:t,isOpen:a,onClose:s,tableOptions:n})=>{const{t:i}=K(),[r,l]=o.useState(""),d=window.location.origin,f=T=>window.open(`${d}/wireless/devices/${T}#/devices/${T}`,"_blank"),c=o.useCallback(T=>T?e.jsx(yt,{date:T},ke()):"--",[]),x=o.useCallback(T=>`${T}%`,[]),g=o.useCallback(T=>i(T?"common.connected":"common.disconnected"),[i]),h=o.useCallback(T=>`${Math.floor(T*100)/100}%`,[]),j=o.useCallback(T=>T!==void 0?sn(T,i):"",[i]),A=o.useCallback(T=>l(T.target.value)),u=o.useMemo(()=>t?r.trim().length===0?[...t.devices,...t.ignoredDevices]:t.devices.filter(L=>L.serialNumber.includes(r)).concat(t.ignoredDevices.filter(L=>L.serialNumber.includes(r))):[],[t,r]),p=()=>{const T=[{key:"serialNumber",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:i("inventory.serial_number")}),dataIndex:"serialNumber",sorter:(M,R)=>fe(M,R,"serialNumber"),render:(M,R)=>e.jsx($n,{onClick:()=>f(R.serialNumber),style:{color:"#14C9BB",textDecorationLine:"underline"},children:R.serialNumber}),columnsFix:!0,fixed:"left"}],L=[{key:"connected",title:i("common.status"),dataIndex:"connected",sorter:(M,R)=>fe(M,R,"connected"),render:g},{key:"health",title:i("analytics.health"),dataIndex:"health",sorter:(M,R)=>fe(M,R,"health"),render:x},{key:"lastHealth",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:i("analytics.last_health")}),dataIndex:"lastHealth",sorter:(M,R)=>fe(M,R,"lastHealth"),render:c},{key:"lastPing",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:i("analytics.last_ping")}),dataIndex:"lastPing",sorter:(M,R)=>fe(M,R,"lastPing"),render:c},{key:"memory",title:i("analytics.memory"),dataIndex:"memory",sorter:(M,R)=>fe(M,R,"memory"),render:h},{key:"lastConnection",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:i("analytics.last_connection")}),dataIndex:"lastConnection",sorter:(M,R)=>fe(M,R,"lastConnection"),render:c},{key:"lastContact",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:i("analytics.last_contact")}),dataIndex:"lastContact",sorter:(M,R)=>fe(M,R,"lastContact"),render:c},{key:"2g",title:"2G",dataIndex:"associations_2g",sorter:(M,R)=>fe(M,R,"associations_2g")},{key:"5g",title:"5G",dataIndex:"associations_5g",sorter:(M,R)=>fe(M,R,"associations_5g")},{key:"6g",title:"6G",dataIndex:"associations_6g",sorter:(M,R)=>fe(M,R,"associations_6g")},{key:"uptime",title:i("analytics.uptime"),dataIndex:"uptime",sorter:(M,R)=>fe(M,R,"uptime"),render:j},{key:"deviceType",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:i("inventory.device_type")}),dataIndex:"deviceType",sorter:(M,R)=>fe(M,R,"deviceType")},{key:"lastFirmware",title:i("analytics.firmware"),dataIndex:"lastFirmware",sorter:(M,R)=>fe(M,R,"lastFirmware")}],N=(n==null?void 0:n.prioritizedColumns)??[];if(N.length>0){const M=L.filter(O=>!N.includes(O.key)),R=N.map(O=>{const q=L.find(_=>_.key===O);return q?{...q,columnsFix:!0}:null}).filter(Boolean);return[...T,...R,...M]}return[...T,...L]},v=o.useMemo(()=>p(),[n,i,c,x,g,h,j]),b=o.useCallback(()=>n&&n.sortBy&&n.sortBy.length>0?n.sortBy.map(T=>({field:T.id,order:T.desc?"descend":"ascend"})):[],[n]),[m,I]=o.useState({current:1,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],showTotal:T=>`Total ${T} items`}),[S,y]=o.useState(b());o.useEffect(()=>{a&&(l(""),I({current:1,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],showTotal:T=>`Total ${T} items`}),y(b()))},[a,n]);const C=o.useCallback((T,L,N)=>{I(M=>({...M,...T})),N.field?y([{field:N.field,order:N.order}]):y([])},[]),w=o.useMemo(()=>{let T=[...u];S&&S.length>0&&T.sort((M,R)=>{for(const O of S){const q=v.find(_=>_.dataIndex===O.field);if(q!=null&&q.sorter){const _=q.sorter(M,R);if(_!==0)return O.order==="ascend"?_:-_}}return 0});const L=(m.current-1)*m.pageSize,N=L+m.pageSize;return T.slice(L,N)},[u,m,S,v]),E=e.jsxs(Xe,{direction:"vertical",style:{width:"100%"},children:[e.jsx(De,{justify:"flex-end",align:"center",children:e.jsx(ae,{value:r,onChange:A,allowClear:!0,prefix:e.jsx(Fe,{component:Aa}),placeholder:i("analytics.search_serials"),style:{width:280,height:"32px",float:"right",borderRadius:"2px"}})}),e.jsx("div",{style:{overflowX:"auto",width:"100%"},children:e.jsx(Vt,{columns:v,dataSource:w,rowKey:"serialNumber",pagination:{...m,total:u.length},onChange:C,showColumnSelector:!0},JSON.stringify([a,n]))})]});return e.jsx(Ze,{title:i("analytics.raw_analytics_data"),childItems:E,isModalOpen:a,onCancel:s,modalClass:"ampcon-max-modal",footer:null})};Gt.propTypes=Wn;Gt.defaultProps=Jn;const Xn="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAcpJREFUSEvtlj9oFEEUxr9v9s6gnprsXkAbURELG0HIJQoqISatBDWVhVgLonbGUhC7NIGUIqRO0ljsngQs5HZTWIpYpLLKRROJSQjrfLLrH864dy53Kiky9cz7ve99b94M8Y8XDy1UTxStnko6T9LpjKdPEiaXB0bGASiJxXIYvARwobPA206TN+qVy9MpwKv5cZL5lnTKbHxe6gTk7C3dJ/EQxES9Mnz3h4JUSry+1rMyOLrSCcCNqneMNLELaFrF3ujFRSs7R/L2T5PLYfDXPMgiJ226cwFe6D+geBWU+S17cY3GjLetwA2r5wz0qlVbS3jfNsCrBVdIzAqY/xLrXiOIsgecopNMiHRUtOVBA2B2uX94tBHQPT/TXdhX+vi/AP4qwIMCnpH48MdRIb2t949M5VbghcEjAsl4zb3spj3KLnP2uwetS5T44EbBdSMMgGBLinQrURvb+LjDwpm8gNyZe6G/SPDYzgd4kX8TFpe2SyN4DUTpFwVSCPBx415L7HeAby9aVn3KNf8dyJNNaqcNrR8psutwAc7rZjHSs8KbTIBbe36a3NOXBTDk4lJlKL2lbhSM0WIMRMZnQauwfNK6a3Lb33zjV9AVKPZnhGycAAAAAElFTkSuQmCC",Zn=t=>t>90?"neutral":t>70?"warning":"error",Ue=t=>t==="-"||t>-45?"neutral":t>-60?"warning":"error",Ht=t=>e.jsx(ue,{title:`${t} clients`,children:e.jsx("div",{style:{width:"41px",height:"24px",background:"rgba(20, 201, 187, 0.1)",borderRadius:"2px",border:"1px solid #14C9BB",display:"flex",alignItems:"center",justifyContent:"center",marginLeft:"12px"},children:e.jsxs(Ot,{color:"cyan",style:{borderRadius:"2px",border:"none",backgroundColor:"transparent",display:"flex",alignItems:"center",marginLeft:"10px",color:"#14C9BB"},children:[e.jsx("img",{src:Xn,width:14,height:14,style:{marginRight:4}}),t]})})}),La=t=>t==="-"?null:e.jsx(ue,{title:"Average level of noise",children:e.jsxs(Ot,{color:Ue(t)==="neutral"?"success":Ue(t)==="warning"?"warning":"error",style:{...Ue(t)==="neutral"?{height:"24px",background:"rgba(43,193,116,0.1)",borderRadius:"2px",border:"1px solid #2BC174",color:"#2BC174",display:"inline-flex"}:{height:"24px",background:"rgba(245,63,63,0.1)",borderRadius:"2px",border:"1px solid #F53F3F",color:"#F53F3F"}},children:[t,"dB"]})}),Yn=t=>{const{t:a}=K(),s=Zn(t),n={neutral:{bg:"rgba(43,193,116,0.1)",border:"1px solid #2BC174",color:"#2BC174"},warning:{bg:"rgba(250,173,20,0.1)",border:"1px solid #FABD14",color:"#FABD14"},error:{bg:"rgba(245,63,63,0.1)",border:"1px solid #F53F3F",color:"#F53F3F"}},{bg:i,border:r,color:l}=n[s];return e.jsx(ue,{title:a("Overall Health"),children:e.jsxs(Ot,{style:{width:"47px",height:"24px",background:i,borderRadius:"2px",border:r,color:l,display:"inline-flex",alignItems:"center",justifyContent:"center"},children:[t,"%"]})})},ei=t=>{const a={devices:[],ignoredDevices:[],totalDevices:0,connectedPercentage:0,connectedDevices:0,disconnectedDevices:0,avgMemoryUsed:0,avgHealth:0,avgUptime:0,twoGAssociations:0,fiveGAssociations:0,sixGAssociations:0,deviceTypeTotals:{Unknown:0},deviceFirmwareTotals:{Unknown:0}};try{const s=[],n=[];let i=0,r=0,l=0;for(const d of t)if(d.deviceType!==""){const f=d.lastFirmware.split(" / ");let c=f.length>1?f[1]:d.lastFirmware;(!c||d.lastFirmware.length===0)&&(c="Unknown"),a.deviceFirmwareTotals[c]?a.deviceFirmwareTotals[c]+=1:a.deviceFirmwareTotals[c]=1,a.deviceTypeTotals[d.deviceType]?a.deviceTypeTotals[d.deviceType]+=1:a.deviceTypeTotals[d.deviceType]=1,d.associations_2g>0&&(a.twoGAssociations+=d.associations_2g),d.associations_5g>0&&(a.fiveGAssociations+=d.associations_5g),d.associations_6g>0&&(a.sixGAssociations+=d.associations_6g),d.memory=Math.round(d.memory),d.connected?(a.connectedDevices+=1,i+=d.health,l+=d.memory,r+=d.uptime):a.disconnectedDevices+=1,s.push(d)}else n.push(d),d.connected?(a.connectedDevices+=1,l+=Math.round(d.memory),r+=d.uptime):a.disconnectedDevices+=1,a.deviceFirmwareTotals.Unknown>0?a.deviceFirmwareTotals.Unknown+=1:a.deviceFirmwareTotals.Unknown=1,a.deviceTypeTotals.Unknown>0?a.deviceTypeTotals.Unknown+=1:a.deviceTypeTotals.Unknown=1;return a.totalDevices=s.length+n.length,a.connectedPercentage=Math.round(a.connectedDevices/Math.max(1,a.totalDevices)*100),a.devices=s,a.avgHealth=Math.round(i/Math.max(1,a.connectedDevices)),a.avgUptime=Math.round(r/Math.max(1,a.connectedDevices)),a.avgMemoryUsed=Math.round(l/Math.max(1,a.connectedDevices)),a.devices=s,a.ignoredDevices=n,a}catch{return a}},ti=t=>{var s;const a={};for(const n of t)for(const i of n)a[i.serialNumber]||(a[i.serialNumber]=[]),(s=a[i.serialNumber])==null||s.push(i);for(const[n,i]of Object.entries(a)){const r=i.sort((l,d)=>l.timestamp>d.timestamp?1:-1);a[n]=r}return a},ai=(t,a)=>{var n,i,r,l,d,f,c,x,g;const s=[];for(const[h,j]of Object.entries(t)){const A=a.find(b=>b.serialNumber===h);if(!A)continue;const u={serialNumber:h,dashboardData:A,timepoints:j,deltas:{rxBytes:0,txBytes:0,rxPackets:0,txPackets:0},ues:0,rssiStatus:"neutral",averageRssi:"-",radios:{}};for(const b of j)for(const m of b.radio_data)u.radios[m.band]?(n=u.radios[m.band])==null||n.timepoints.push(m):u.radios[m.band]={band:m.band,timepoints:[m],deltas:{rxBytes:0,txBytes:0,rxPackets:0,txPackets:0},amountOfUes:0,rssiStatus:"neutral",averageRssi:"-",ssids:{}};for(const b of j)for(const m of b.ssid_data)if(u.radios[m.band]){(i=u.radios[m.band])!=null&&i.ssids[m.bssid]?(l=(r=u.radios[m.band])==null?void 0:r.ssids[m.bssid])==null||l.timepoints.push(m):u.radios[m.band].ssids[m.bssid]={bssid:m.bssid,ssid:m.ssid,timepoints:[m],deltas:{rxBytes:0,txBytes:0,rxPackets:0,txPackets:0},amountOfUes:0,rssiStatus:"neutral",averageRssi:"-",ues:{},serialNumber:u.serialNumber,band:m.band};for(const I of m.associations)if(u.radios[m.band].ssids[m.bssid]){if((f=(d=u.radios[m.band])==null?void 0:d.ssids[m.bssid])!=null&&f.ues[I.station])(g=(x=(c=u.radios[m.band])==null?void 0:c.ssids[m.bssid])==null?void 0:x.ues[I.station])==null||g.timepoints.push(I);else{const S=_a(I.rssi);u.radios[m.band].ssids[m.bssid].ues[I.station]={station:I.station,timepoints:[I],deltas:{rxBytes:0,txBytes:0,rxPackets:0,txPackets:0},rssiStatus:Ue(S),rssi:S,serialNumber:u.serialNumber,band:m.band,ssid:m.ssid}}u.radios[m.band].ssids[m.bssid].ues[I.station].deltas.rxBytes+=I.rx_bytes_delta,u.radios[m.band].ssids[m.bssid].ues[I.station].deltas.txBytes+=I.tx_bytes_delta,u.radios[m.band].ssids[m.bssid].ues[I.station].deltas.rxPackets+=I.rx_packets_delta,u.radios[m.band].ssids[m.bssid].ues[I.station].deltas.txPackets+=I.tx_packets_delta,u.radios[m.band].ssids[m.bssid].deltas.rxBytes+=I.rx_bytes_delta,u.radios[m.band].ssids[m.bssid].deltas.txBytes+=I.tx_bytes_delta,u.radios[m.band].ssids[m.bssid].deltas.rxPackets+=I.rx_packets_delta,u.radios[m.band].ssids[m.bssid].deltas.txPackets+=I.tx_packets_delta,u.radios[m.band].deltas.rxBytes+=I.rx_bytes_delta,u.radios[m.band].deltas.txBytes+=I.tx_bytes_delta,u.radios[m.band].deltas.rxPackets+=I.rx_packets_delta,u.radios[m.band].deltas.txPackets+=I.tx_packets_delta,u.deltas.rxBytes+=I.rx_bytes_delta,u.deltas.txBytes+=I.tx_bytes_delta,u.deltas.rxPackets+=I.rx_packets_delta,u.deltas.txPackets+=I.tx_packets_delta}}let p=0,v=0;for(const[b,m]of Object.entries(u.radios)){let I=0,S=0;for(const[y,C]of Object.entries(m.ssids)){let w=0,E=0;for(const T of Object.values(C.ues))typeof T.rssi=="number"&&(w+=T.rssi,I+=T.rssi,S+=1,E+=1);if(E>0){const T=Math.round(w/E);u.radios[b].ssids[y].averageRssi=T,u.radios[b].ssids[y].rssiStatus=Ue(T),u.radios[b].ssids[y].amountOfUes=E}}if(S>0){p+=I,v+=S;const y=Math.round(I/S);u.radios[b].averageRssi=y,u.radios[b].rssiStatus=Ue(y),u.radios[b].amountOfUes=S}}if(v>0){const b=Math.round(p/v);u.averageRssi=b,u.rssiStatus=Ue(b),u.ues=v}s.push(u)}return s.sort((h,j)=>h.serialNumber.localeCompare(j.serialNumber))},si=()=>{const t=new Date;return t.setMinutes(t.getMinutes()-10),t},qa=G.createContext({venueId:""}),ni=({venueId:t,children:a})=>{var m;const s=ws(),[n,i]=G.useState(),l=(m=qe({id:t}).data)==null?void 0:m.boards[0],d=Nn({id:l}),[f]=G.useState(si()),c=Ln({id:l,startTime:f}),[x,g]=G.useState(),h=G.useCallback(I=>{g(I)},[]),j=G.useCallback(()=>{g(void 0)},[]),A=G.useCallback(I=>{i(I),s.onOpen()},[]),u=G.useMemo(()=>{if(d.data)return ei(d.data)},[d.data]),p=G.useMemo(()=>{if(c.data)return ti(c.data)},[c.data]),v=G.useMemo(()=>{if(!(!p||!u))return ai(p,u.devices)},[p,u]),b=G.useMemo(()=>({venueId:t,dashboard:u,timepoints:p,monitoring:v,handleDashboardModalOpen:A,selectedItem:x,onSelectItem:h,onUnselectItem:j}),[t,u,p,v,A,x]);return G.useEffect(()=>{g(void 0)},[t]),e.jsx(qa.Provider,{value:b,children:e.jsxs(e.Fragment,{children:[a,e.jsx(Gt,{data:b.dashboard,tableOptions:n,...s})]})})},je=()=>G.useContext(qa),ii=(t,a=2)=>{if(!t||t===0)return"0 MB";const s=1024,n=a<0?0:a,i=t/(s*s);return`${parseFloat(i.toFixed(n))} MB`},ra=({bytes:t,style:a})=>{const{Text:s}=ve,n=o.useMemo(()=>t===void 0?"-":ii(t),[t]);return e.jsx(s,{style:a,children:n})},ri=()=>{const{t}=K(),[a,s]=G.useState("associations"),{monitoring:n}=je(),i=G.useMemo(()=>[{title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:t("inventory.serial_number")}),dataIndex:"serialNumber",key:"serialNumber",sorter:(d,f)=>d.serialNumber.localeCompare(f.serialNumber),columnsFix:!0},{title:"RX(MB)",dataIndex:"rxBytes",key:"rxBytes",render:d=>e.jsx(ra,{bytes:d}),sorter:(d,f)=>d.rxBytes-f.rxBytes},{title:"TX(MB)",dataIndex:"txBytes",key:"txBytes",render:d=>e.jsx(ra,{bytes:d}),sorter:(d,f)=>d.txBytes-f.txBytes},{title:"2G",dataIndex:"associations_2g",key:"associations_2g",sorter:(d,f)=>d.associations_2g-f.associations_2g},{title:"5G",dataIndex:"associations_5g",key:"associations_5g",sorter:(d,f)=>d.associations_5g-f.associations_5g},{title:"6G",dataIndex:"associations_6g",key:"associations_6g",sorter:(d,f)=>d.associations_6g-f.associations_6g},{title:t("analytics.health"),dataIndex:"health",key:"health",render:d=>`${Math.floor(d*100)/100}%`,sorter:(d,f)=>d.health-f.health},{title:t("analytics.memory"),dataIndex:"memory",key:"memory",render:d=>`${Math.floor(d*100)/100}%`,sorter:(d,f)=>d.memory-f.memory}],[t]),r=G.useMemo(()=>n==null?void 0:n.map(d=>({...d.dashboardData,totalAssociations:d.dashboardData.associations_2g+d.dashboardData.associations_5g+d.dashboardData.associations_6g,totalTraffic:d.deltas.rxBytes+d.deltas.txBytes,rxBytes:d.deltas.rxBytes,txBytes:d.deltas.txBytes})).sort((d,f)=>a==="associations"?f.totalAssociations-d.totalAssociations:f.totalTraffic-d.totalTraffic).slice(0,10),[n,a]),l=e.jsxs("div",{style:{display:"flex",alignItems:"center",position:"relative",marginTop:"20px"},children:[e.jsx("span",{style:{width:"45px",height:"17px",fontFamily:"Lato, sans-serif",fontWeight:500,fontSize:"14px",color:"#212519",lineHeight:"17px",marginRight:"56px"},children:"Sort by"}),e.jsxs(Ce.Group,{value:a,onChange:d=>s(d.target.value),style:{display:"flex",gap:"56px"},children:[e.jsx(Ce,{value:"associations",style:{width:"43px",height:"17px",fontWeight:400,lineHeight:"17px",margin:0},children:"Clients"}),e.jsx(Ce,{value:"traffic",style:{width:"41px",height:"17px",fontWeight:400,lineHeight:"17px",margin:0},children:"Traffic"})]})]});return e.jsxs(pt,{title:e.jsx("div",{style:{fontSize:"18px"},children:"Top 10 Busiest Devices"}),style:{height:"100%",border:"none"},bodyStyle:{padding:"16px",display:"flex",flexDirection:"column",height:"calc(100% - 57px)",marginBottom:"20px"},children:[l,e.jsx(Vt,{columns:i,dataSource:r,isShowPagination:!1,showColumnSelector:!0,style:{maxHeight:"245px",overflowY:"auto"},bordered:!0})]})},{Option:li}=Q,{Text:ld,Title:od}=ve,oi=({macs:t,setMac:a,value:s})=>{const{t:n}=K(),[i,r]=o.useState(s),l=o.useCallback((g,h)=>g?h.value.includes(g.replace("*","")):!0,[t]),d=g=>{a(g),r(g)},f=g=>{r(g)},c=o.useCallback(()=>{r("")},[]);o.useEffect(()=>{r(s)},[s]);const x=g=>{g===void 0&&r("")};return e.jsx(Q,{showSearch:!0,style:{width:280,borderRadius:36},placeholder:n("common.search"),value:i||void 0,onSelect:d,onSearch:f,onFocus:c,onChange:x,filterOption:l,allowClear:!0,bordered:!0,dropdownMatchSelectWidth:!0,getPopupContainer:g=>g.parentElement,optionFilterProp:"value",children:t==null?void 0:t.map(g=>e.jsx(li,{value:g,children:g},g))})},di=({isTrue:t})=>{const a=o.useMemo(()=>t===void 0?"-":t?ta("common.yes"):ta("common.no"),[t]);return e.jsx("div",{children:a})},la=G.memo(di),ci=({db:t})=>{const a=o.useMemo(()=>t===void 0?"-":_a(t),[t]);return e.jsx("div",{children:a})},st=G.memo(ci),ui=({seconds:t})=>{const{t:a}=K(),s=o.useMemo(()=>t===void 0?"-":nn(t,a),[t]);return e.jsx("div",{children:s})},Re=G.memo(ui),hi=({value:t})=>{const a=o.useMemo(()=>t===void 0?"-":t.toLocaleString(),[t]);return e.jsx("div",{children:a})},nt=G.memo(hi),fi=({useCount:t,useGet:a,countParams:s={},getParams:n={}})=>{const[i,r]=o.useState(void 0),{data:l,isFetching:d,refetch:f}=t(s),{data:c,isFetching:x,refetch:g}=a({pageInfo:i,enabled:i!==null,count:l,...n});return o.useMemo(()=>({count:l,data:c,isFetching:d||x,refetchCount:f,refetchData:g,pageInfo:i,setPageInfo:r}),[l,c,d,x])},{Title:mi}=ve,pi=({venueId:t,mac:a,fromDate:s,endDate:n,timePickers:i,searchBar:r})=>{const{t:l}=K(),[d,f]=o.useState([{id:"timestamp",sort:"dsc"}]),[c,x]=o.useState({index:0,limit:10});o.useEffect(()=>{a&&(x({index:0,limit:10}),f([{id:"timestamp",sort:"dsc"}]))},[a]);const{count:g,data:h,isFetching:j}=fi({useCount:Bn,useGet:ia,countParams:{venueId:t,mac:a,sortInfo:d,fromDate:s,endDate:n},getParams:{venueId:t,mac:a,sortInfo:d,fromDate:s,endDate:n,pageInfo:c}}),A=(p,v,b)=>{x({index:p.current-1,limit:p.pageSize}),b.field&&f([{id:b.field,sort:b.order==="ascend"?"asc":"desc"}])},u=o.useMemo(()=>[{key:"timestamp",title:l("common.timestamp"),dataIndex:"timestamp",render:p=>e.jsx(yt,{date:p}),fixed:"left",sorter:!0,isMonospace:!0,columnsFix:!0},{key:"bssid",title:"BSSID",dataIndex:"bssid",sorter:!0,isMonospace:!0},{key:"ssid",title:"SSID",dataIndex:"ssid",sorter:!0,isMonospace:!0},{key:"rssi",title:"RSSI(db)",dataIndex:"rssi",render:p=>e.jsx(st,{db:p}),sorter:!0,isMonospace:!0},{key:"noise",title:`${l("analytics.noise")}(db)`,dataIndex:"noise",render:p=>e.jsx(st,{db:p}),sorter:!0,isMonospace:!0},{key:"channel",title:l("analytics.channel"),dataIndex:"channel",sorter:!0,isMonospace:!0},{key:"tx_power",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX Power"}),dataIndex:"tx_power",sorter:!0,isMonospace:!0},{key:"tx_retries",title:e.jsxs("span",{style:{whiteSpace:"nowrap"},children:["TX ",l("analytics.retries")]}),dataIndex:"tx_retries",sorter:!0,isMonospace:!0},{key:"connected",title:l("analytics.connected"),dataIndex:"connected",render:p=>e.jsx(Re,{seconds:p}),sorter:!0,isMonospace:!0},{key:"inactive",title:l("analytics.inactive"),dataIndex:"inactive",render:p=>e.jsx(Re,{seconds:p}),sorter:!0,isMonospace:!0},{key:"ack_signal",title:e.jsxs("span",{style:{whiteSpace:"nowrap"},children:[l("analytics.ack_signal"),"(db)"]}),dataIndex:"ack_signal",render:p=>e.jsx(st,{db:p}),sorter:!0,isMonospace:!0},{key:"ack_signal_avg",title:e.jsxs("span",{style:{whiteSpace:"nowrap"},children:[l("analytics.ack_signal"),l("common.avg"),"(db)"]}),dataIndex:"ack_signal_avg",render:p=>e.jsx(st,{db:p}),sorter:!0,isMonospace:!0},{key:"rx_bytes",title:"RX",dataIndex:"rx_bytes",render:p=>e.jsx(sa,{bytes:p}),sorter:!0,isMonospace:!0},{key:"rx_mcs",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"RX MCS"}),dataIndex:"rx_mcs",sorter:!0,isMonospace:!0},{key:"rx_nss",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"RX NSS"}),dataIndex:"rx_nss",sorter:!0,isMonospace:!0},{key:"tx_bytes",title:"TX",dataIndex:"tx_bytes",render:p=>e.jsx(sa,{bytes:p}),sorter:!0,isMonospace:!0},{key:"tx_mcs",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX MCS"}),dataIndex:"tx_mcs",sorter:!0,isMonospace:!0},{key:"tx_nss",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX NSS"}),dataIndex:"tx_nss",sorter:!0,isMonospace:!0},{key:"rx_bitrate",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"RX Bitrate"}),dataIndex:"rx_bitrate",render:p=>e.jsx(nt,{value:p}),sorter:!0,isMonospace:!0},{key:"rx_chwidth",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"RX Ch Width"}),dataIndex:"rx_chwidth",sorter:!0,isMonospace:!0},{key:"rx_duration",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"RX Duration"}),dataIndex:"rx_duration",render:p=>e.jsx(Re,{seconds:p/1e3}),sorter:!0,isMonospace:!0},{key:"rx_packets",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"RX Packets"}),dataIndex:"rx_packets",render:p=>e.jsx(nt,{value:p}),sorter:!0,isMonospace:!0},{key:"rx_vht",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"RX VHT"}),dataIndex:"rx_vht",render:p=>e.jsx(la,{isTrue:p}),sorter:!0,isMonospace:!0},{key:"tx_bitrate",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX Bitrate"}),dataIndex:"tx_bitrate",render:p=>e.jsx(nt,{value:p}),sorter:!0,isMonospace:!0},{key:"tx_chwidth",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX Ch Width"}),dataIndex:"tx_chwidth",sorter:!0,isMonospace:!0},{key:"tx_vht",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX VHT"}),dataIndex:"tx_vht",render:p=>e.jsx(la,{isTrue:p}),sorter:!0,isMonospace:!0},{key:"tx_duration",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX Duration"}),dataIndex:"tx_duration",render:p=>e.jsx(Re,{seconds:p/1e3}),sorter:!0,isMonospace:!0},{key:"tx_packets",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"TX Packets"}),dataIndex:"tx_packets",render:p=>e.jsx(nt,{value:p}),sorter:!0,isMonospace:!0},{key:"ipv4",title:"IPv4",dataIndex:"ipv4",sorter:!0,isMonospace:!0},{key:"ipv6",title:"IPv6",dataIndex:"ipv6",sorter:!0,isMonospace:!0},{key:"channel_width",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"Ch Width"}),dataIndex:"channel_width",sorter:!0,isMonospace:!0},{key:"active_ms",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"Active MS"}),dataIndex:"active_ms",render:p=>e.jsx(Re,{seconds:p/1e3}),sorter:!0,isMonospace:!0},{key:"busy_ms",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"Busy MS"}),dataIndex:"busy_ms",render:p=>e.jsx(Re,{seconds:p/1e3}),sorter:!0,isMonospace:!0},{key:"receive_ms",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:"Receive MS"}),dataIndex:"receive_ms",render:p=>e.jsx(Re,{seconds:p/1e3}),sorter:!0,isMonospace:!0},{key:"mode",title:l("analytics.mode"),dataIndex:"mode",sorter:!0,isMonospace:!0}],[l]);return e.jsxs(pt,{title:e.jsx(mi,{level:4,style:{margin:0,fontSize:"18px"},children:l("analytics.client_lifecycle")}),style:{height:"100%",border:"none"},bodyStyle:{padding:"16px",minWidth:"100%",overflow:"auto",maxWidth:"70vw",marginBottom:"6px"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginTop:"20px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx("span",{style:{marginLeft:"12px",marginRight:"32px",fontSize:"14px"},children:"Time"}),e.jsx("div",{children:i})]}),e.jsx("div",{style:{marginRight:"2px"},children:r})]}),e.jsx(Vt,{columns:u,dataSource:h||[],loading:j,onChange:A,showColumnSelector:!0,fetchAPIInfo:ia,pagination:{current:c.index+1,pageSize:c.limit,total:g,showSizeChanger:!0,showQuickJumper:!0,showTotal:p=>`Total ${p} items`,pageSizeOptions:["10","20","50","100"]},scroll:{x:"max-content"}})]})},gi=async(t,a)=>Se.get(`wifiClientHistory?macsOnly=true&venue=${t}&limit=500&offset=${a}`).then(({data:s})=>s.entries),xi=async t=>{const a=[];let s=!0,n=0;for(;s;){const i=await gi(t,n);(!i||i.length<500||n>=5e4)&&(s=!1),a.push(...i),n+=500}return a},bi=({venueId:t})=>{var A,u,p;const{t:a}=K(),s=gt(),[n,i]=o.useState(),[r,l]=o.useState(),[d,f]=o.useState([ut(rn(5*24)),ut()]),c=o.useRef(null);o.useEffect(()=>{var v;if((v=s.state)!=null&&v.targetMac){const b=s.state.targetMac.replace(/[:\\-]/g,"");l(b)}},[s.state]),o.useEffect(()=>{var v;(v=s.state)!=null&&v.scrollToClientLifecycle&&c.current&&c.current.scrollIntoView({behavior:"smooth"})},[s.state]);const x=o.useCallback(async()=>{try{return await xi(t)}catch{return}},[t]);o.useEffect(()=>{x().then(v=>{var b;if(i(v),(b=s.state)!=null&&b.targetMac){const m=s.state.targetMac.replace(/[:\-]/g,"");l(m)}else v&&v.length>0?l(v[0]):l(void 0)})},[x,(A=s.state)==null?void 0:A.targetMac]);const g=v=>{f(v)},h=d&&(u=d[0])!=null&&u.valueOf()?Math.floor(d[0].valueOf()/1e3):0,j=d&&(p=d[1])!=null&&p.valueOf()?Math.floor(d[1].valueOf()/1e3):0;return e.jsx("div",{ref:c,children:e.jsx(pi,{fromDate:h,endDate:j,venueId:t,mac:r,timePickers:e.jsx(Js,{value:d,onChange:g,tooltipText:a("controller.crud.choose_time")}),searchBar:e.jsx(oi,{macs:n,setMac:l,value:r})})})},{Title:vi,Text:It}=ve,yi=()=>{const{t}=K(),{selectedItem:a,onUnselectItem:s}=je(),n=()=>{s()};if(!a)return null;const i=(c,x,g)=>{const h=g%2===0;return e.jsx(F,{span:12,style:{padding:"0 4px",height:"48px",display:"flex",alignItems:"center"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",width:"100%",height:"100%",backgroundColor:h?"#F8FAFB":"#FFFFFF"},children:[e.jsx(It,{style:{fontSize:"14px",color:"#474747",whiteSpace:"nowrap",paddingLeft:"16px",width:"160px",flexShrink:0},children:c}),e.jsx("span",{style:{fontWeight:600,fontSize:"14px",color:"#212519",whiteSpace:"nowrap"},children:x})]})})},r=(c,x,g)=>e.jsx(F,{span:24,style:{padding:"0 4px",height:"48px",display:"flex",alignItems:"center"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",width:"100%",height:"100%",backgroundColor:"#F8FAFB"},children:[e.jsx(It,{style:{fontSize:"14px",color:"#474747",whiteSpace:"nowrap",paddingLeft:"16px",width:"160px",flexShrink:0},children:c}),e.jsx("span",{style:{fontWeight:600,fontSize:"14px",color:"#212519",whiteSpace:"nowrap"},children:x})]})}),l=()=>{var c;if(a.type==="AP")return e.jsxs("div",{style:{width:"100%"},children:[e.jsxs(U,{gutter:8,children:[i(t("analytics.health"),`${a.data.dashboardData.health}%`,0),i(t("analytics.memory_used"),`${Math.floor(a.data.dashboardData.memory)}%`,0)]}),e.jsxs(U,{gutter:8,children:[i(t("common.type"),a.data.dashboardData.deviceType,1),i(t("analytics.firmware"),((c=a.data.dashboardData.lastFirmware)==null?void 0:c.split("/")[1])??t("common.unknown"),1)]}),e.jsxs(U,{gutter:8,children:[i("2G Clients",a.data.dashboardData.associations_2g,2),i("5G Clients",a.data.dashboardData.associations_5g,2)]}),e.jsx(U,{gutter:8,children:i("6G Clients",a.data.dashboardData.associations_6g,3)}),e.jsx(U,{style:{marginTop:8},children:e.jsx(F,{span:24,children:e.jsx(vi,{level:5,style:{marginBottom:16},children:"Last 10 Minutes"})})}),e.jsxs(U,{gutter:8,children:[i("Tx",he(a.data.deltas.txBytes),4),i("Tx Packets",a.data.deltas.txPackets.toLocaleString(),4)]}),e.jsxs(U,{gutter:8,children:[i("Rx",he(a.data.deltas.rxBytes),5),i("Rx Packets",a.data.deltas.rxPackets.toLocaleString(),5)]})]});if(a.type==="RADIO"){const x=a.data.timepoints[a.data.timepoints.length-1];return e.jsxs("div",{style:{width:"100%"},children:[e.jsxs(U,{gutter:8,children:[i(t("analytics.noise"),`${a.data.averageRssi} dB`,0),i(t("analytics.channel"),x.channel,0)]}),e.jsxs(U,{gutter:8,children:[i(t("analytics.temperature"),`${x.temperature}°C`,1),i(t("analytics.airtime"),`${x.transmit_pct.toFixed(2)}%`,1)]}),e.jsxs(U,{gutter:8,children:[i(t("analytics.active"),`${x.active_pct.toFixed(2)}%`,2),i(t("analytics.busy"),`${x.busy_pct.toFixed(2)}%`,2)]}),e.jsx(U,{gutter:8,children:i(t("analytics.receive"),`${x.receive_pct.toFixed(2)}%`,3)})]})}if(a.type==="SSID"){const x=a.data.timepoints[a.data.timepoints.length-1];return e.jsxs("div",{style:{width:"100%"},children:[e.jsxs(U,{gutter:8,children:[i("Bssid",a.data.bssid,0),i("Noise",`${a.data.averageRssi} dB`,0)]}),e.jsxs(U,{gutter:8,children:[i("TX Bandwidth (avg)",he(x.tx_bytes_bw.avg),1),i("TX Bandwidth (min)",he(x.tx_bytes_bw.min),1)]}),e.jsxs(U,{gutter:8,children:[i("TX Bandwidth (max)",he(x.tx_bytes_bw.max),2),i("TX Packets/s",`${x.tx_packets_bw.avg.toFixed(2)} / ${x.tx_packets_bw.min.toFixed(2)} / ${x.tx_packets_bw.max.toFixed(2)}`,2)]}),e.jsxs(U,{gutter:8,children:[i("RX Bandwidth (avg)",he(x.rx_bytes_bw.avg),3),i("RX Bandwidth (min)",he(x.rx_bytes_bw.min),3)]}),e.jsxs(U,{gutter:8,children:[i("RX Bandwidth (max)",he(x.rx_bytes_bw.max),4),i("RX Packets/s",`${x.rx_packets_bw.avg.toFixed(2)} / ${x.rx_packets_bw.min.toFixed(2)} / ${x.rx_packets_bw.max.toFixed(2)}`,4)]})]})}if(a.type==="UE"){const x=a.data.timepoints[a.data.timepoints.length-1];return e.jsxs("div",{style:{width:"100%"},children:[e.jsxs(U,{gutter:8,children:[r("Connected to",`${a.data.band}G - ${a.data.ssid}`),e.jsx(F,{span:24,style:{padding:"12px 16px",backgroundColor:"#FFFFFF",height:"48px",display:"flex",alignItems:"center",marginLeft:-12},children:e.jsx(It,{strong:!0,children:"Data (TX / RX)"})})]}),e.jsxs(U,{gutter:8,children:[i(t("analytics.total_data"),`${he(x.tx_bytes)} / ${he(x.rx_bytes)}`,1),i(t("analytics.delta"),`${he(a.data.deltas.txBytes)} / ${he(a.data.deltas.rxBytes)}`,1)]}),e.jsxs(U,{gutter:8,children:[i(t("analytics.bandwidth"),`${he(x.tx_bytes_bw)} / ${he(x.rx_bytes_bw)}`,2),i(`${t("analytics.packets")}/s`,`${na(x.tx_packets_bw)} / ${na(x.rx_packets_bw)}`,2)]}),e.jsxs(U,{gutter:8,children:[i("MCS",`${x.tx_rate.mcs} / ${x.rx_rate.mcs}`,3),i("NSS",`${x.tx_rate.nss} / ${x.rx_rate.nss}`,3)]})]})}return e.jsx("pre",{children:JSON.stringify(a,null,2)})},d=()=>{const c={margin:0,display:"inline-block"};switch(a.type){case"AP":return e.jsx("span",{style:c,children:a.data.serialNumber});case"RADIO":return e.jsxs("span",{style:c,children:[a.serialNumber," - ",a.data.band,"G"]});case"SSID":return e.jsx("span",{style:c,children:a.data.ssid});case"UE":return e.jsx("span",{style:c,children:a.data.station});default:return e.jsx("span",{style:c,children:"Details"})}},f=e.jsx("div",{style:{width:"100%",height:"auto",overflowY:"auto",paddingRight:"8px"},children:l()});return e.jsx(Ze,{title:e.jsxs("div",{children:[d(),e.jsx(oe,{style:{marginBottom:0,marginLeft:-24,marginRight:-24,width:"calc(100% + 48px)"}})]}),childItems:f,isModalOpen:!!a,onCancel:n,footer:null,modalClass:"ampcon-middle-modal"})},ji="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAVRJREFUWEftl61OA0EUhc9tQ1J+EqhD4BDlASDBgKjfUZOVk6DwFTwEAo8iWdxmMTu+AgwJPAAIHALFT0KhSdNeMmS3tATBbCeZilk1m9w555sj7twheP7Isz+mAM4yvVUHHwNoA1h2DNcD0B2Cjg5kdFdqjwGMeQ2jawKtOjaekmPw2wi13RJiDJBkeQ4gMtXMeCLCo0sQZmwQYb3Q1EoKYdaTAO8mdmPeb9DmYRR9uAQ41Xqp0eeHAqKnpFj5DcCF4a2SYseleamVZPkNgG3zr6T4PvxkAgEgJBASmM8ETtJ0sYmFFgF1m97AwPAFg/tOHH/O1AeSTF8CvGdj/lNLV0pG+5UBmJnOL/QzgLVqAHhVUjQrA5iNSZq3mRATsdXswExMjFTFojsTQMWT/7kt3AUhgZDA3CfgfSj1O5Z7f5iY3un1aeay59toWd1wNsL/rf0CyIoyML8htJMAAAAASUVORK5CYII=",Ci="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAY5JREFUWEftl9FNwzAQhs+xeC8blA3KBu0CgUgGiZe0mQAxAWIC2CBtX5AgUtouQEZgA7IBeY+OwEV25FSBppajCCl+TOy7z//Zv20GPTfWc36oAYTP8Zif8HsAuASAkWW4DAASzPEuuPFSFbsCkMnfAGBsOfF+uBRznCmICmAd7WI5cxpAtO+WQSaaqktfuAHF1wE+ZYcMOZ4FnkcQ1loYxyOO/EPl8IV7ug9QyGyJL9yZtcxaoHW0oxJP6ZMv3HLyugL/C0BbtKAvqr+Us6rAKtrOGbAlJSygWMzFxepQ2QaAQQFjBcLXuNy7enPAmTLG6NygzfyEX7itdSggC669mpsaAeiDDq3yhv81UzMFIPs0PaAyZbkEZwQQvsQT7vDbhtkRlCpNAgDVESvLklFZgiuP/pXNCOA32QcjGhToXYHacczxvM0NyuouMDAlu9uwCwB1KU2xpaTHQLS5lIY/braQQbu4lpNzKkvf+ML1apfS3h8mRCMhHqXHd/E022COD41Ps2PqabNv76/jbwuaeTBZWLHEAAAAAElFTkSuQmCC",wi="data:image/png;base64,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",Si="data:image/png;base64,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",Ti="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAopJREFUWEftlk9u2kAUxr/Hn6pZlS4qwSqDBOuSG5ATNDkBuUHhBIQTkJwg9AThCM4NnLWpmE0LUhf1qkEy4UUzxu50sMEGpKhSLCEhmHnzm+997z0TXvmhVz4fbwD/rwJCVAXKzxUERV/KudzXS5kUCA9DG4wOEQkA6mM/PgCXmR8AOHIyd7JAbQUQzeoVgb4CaGUJZq3xmfkWy8WNlL6CS3wSAUTzUwtcHBJRO+mmzOwC5IM4DMxUIdKQicowYyAns5skgg0A0aheE1HfXMzMDojGwPJBer/ctNsIUang3fsvWNEFES6sdQ4HT5e2GjGA2lwondzxvxslgwfSm4/ypmDtmz6Broy9klG4lN6P+BIxQL1Zm5oSMqOXJpsOXjLkXkICCz8p16JRbRPRnRHb54DPosrRAOr2VD75vSbdoNRrGtU2CB2C9kV6FYC/2YopYCprCO0pBrrSm92q739T0Kh1QfwZAQZmXadIuSsjialT/gLhFMGiF6m1vQwbtS4RhpvlBQfgx/h3wmmSMgQarYI/8WGZqiBalFANOw2pyrfApb5lZHfqzc5y9QG1uN6sKU9UdM6YB3Iyv96luwGvjHdv7D9P64ypKRCh/B1e0UB+/zk2D9e+KBZaKK4+hI3o+dHuD5HxGCzNnNuXyDQL4puFM8Gu7ehvySvq2bC7VMsMsK7nWNa0wHnTlQOgdm+0V8nAGMSungPQbTeeGxw8fdw2gEz4HADhjGDwyO4VYaPSnlEzRLn+fJf00f+ZAbIEVB01680PBohfUlB0zeGSBXSvFNiBRbM2JKALQE69WT3vwYcrEL4tqQHj5Mn5QX1gQ4U9cn5UgH1lP4oHjnG4inHUMtwH6g3gBYM/JDB6E3/cAAAAAElFTkSuQmCC",Ai="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAa5JREFUWEftl09OwlAQxr8xkuAOFybFjY9I99xAuAGeQDyBcgLkBOAJ8AjeQLwB+2L6VraJC7vXdPQ9IFDoH+E1EmPfsu915teZb95MCXtetGf/KAASIyDqVtM4PZ+QUvoyzc4GgDg/bdMBDwAIY4CZgTF/8HUSyAZAza6+A6jk5FybYWAoHa8bZzMOgPVLjC7AExMQIup9R6DJzGM59VtbAnBLTv2xCYCwrRGBOgXAPiPQIdBoRxGaa0DpR9gnDem8JYo5pQryAcgScW4AQlQqKJUHSvUJTgNm9OXUG67u5wdgV28IiBiPA3EdL+IzP4C6dTe/eAImvo04D1Gb7+E3AKTreLVVANXYiOhJPfs7AAAkM6e2Uv2VRBPVaMQyBcYRUKHaahbgkC5BYWOeZzMAXU6HZSWii9RBgki17IbunMQdLIVmBpB1cSz2hbAElcj9HwDCrg4IaMdER49taykImPk+cpZwtrghM8tw3YnSBJWO1JiWuJi5BYJQnS8jhYHreMepV3GcAT0hE64SjD9Lx3/QnW9WiknnAg6pL19eH7cG+KkwdzlX/BkVEfgCM+guMMvWbsIAAAAASUVORK5CYII=",{DirectoryTree:ki}=Ss,Ii=({expanded:t,isLeaf:a})=>a?e.jsx("span",{style:{display:"inline-block",width:16}}):e.jsx("img",{src:t?ji:Ci,alt:"Expand/Collapse Icon",style:{width:"16px",height:"16px",marginRight:"4px",verticalAlign:"middle"}}),Ei=()=>e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e.jsx(Ts,{image:As,description:"No Data",imageStyle:{marginTop:16,marginBottom:0}})}),Fi=()=>{const t=je(),a=t.monitoring,s=a?a.map(r=>({key:`ap-${r.serialNumber}`,title:e.jsx(Pi,{data:r}),children:Object.values(r.radios).map(l=>({key:`radio-${r.serialNumber}-${l.band}`,title:e.jsx(Mi,{data:l,serialNumber:r.serialNumber}),children:Object.values(l.ssids).map(d=>({key:`ssid-${d.bssid}`,title:e.jsx(Ri,{data:d}),children:Object.values(d.ues).map(f=>({key:`ue-${f.station}`,title:e.jsx(_i,{data:f}),isLeaf:!0}))}))}))})):[],n=(r,{node:l})=>{const d=r[0];if(d){if(d.startsWith("ap-")){const f=d.replace("ap-",""),c=a.find(x=>x.serialNumber===f);c&&t.onSelectItem({type:"AP",data:c})}else if(d.startsWith("radio-")){const[f,c]=d.replace("radio-","").split("-"),x=a.find(g=>g.serialNumber===f);if(x){const g=Object.values(x.radios).find(h=>h.band==c);g&&t.onSelectItem({type:"RADIO",data:g,serialNumber:f})}}else if(d.startsWith("ssid-")){const f=d.replace("ssid-","");let c=null;a.forEach(x=>{Object.values(x.radios).forEach(g=>{Object.values(g.ssids).forEach(h=>{h.bssid===f&&(c=h)})})}),c&&t.onSelectItem({type:"SSID",data:c})}else if(d.startsWith("ue-")){const f=d.replace("ue-","");let c=null;a.forEach(x=>{Object.values(x.radios).forEach(g=>{Object.values(g.ssids).forEach(h=>{Object.values(h.ues).forEach(j=>{j.station===f&&(c=j)})})})}),c&&t.onSelectItem({type:"UE",data:c})}}},i=s.length===0;return e.jsx(pt,{title:e.jsx("div",{style:{fontSize:"18px"},children:"Live Data"}),style:{height:"100%",display:"flex",flexDirection:"column",border:"none"},bodyStyle:{flex:1,overflow:"hidden",padding:"16px"},children:e.jsx(U,{gutter:16,style:{height:"100%"},children:e.jsx(F,{span:24,style:{height:"100%"},children:e.jsxs("div",{style:{height:"100%",overflowY:i?"hidden":"auto"},children:[s.length>0?e.jsx(ki,{multiple:!1,defaultExpandAll:!1,onSelect:n,treeData:s,showIcon:!1,switcherIcon:Ii,showLine:!0,expandAction:"click",style:{fontFamily:"Lato",lineHeight:"32px",minHeight:"100%"}}):e.jsx(Ei,{}),e.jsx(F,{span:8,children:e.jsx(yi,{})})]})})})})},Pi=({data:t})=>{var r;const{t:a}=K(),s=je(),n=((r=s.selectedItem)==null?void 0:r.type)==="AP"&&s.selectedItem.data.dashboardData.serialNumber===t.dashboardData.serialNumber,i=window.location.origin;return e.jsxs("div",{style:{display:"inline-flex",alignItems:"center",fontWeight:n?"bold":"normal",backgroundColor:n?"#f0f0f0":"transparent",borderRadius:"10px",padding:"2px 4px",verticalAlign:"middle"},children:[e.jsx("img",{src:wi,alt:"MAC Address Logo",width:"20",height:"20"}),e.jsx("span",{style:{fontFamily:"Lato",marginRight:"16px",marginLeft:"4px"},children:t.serialNumber}),e.jsx(ue,{title:a("common.view_in_gateway"),placement:"top",children:e.jsx(Z,{style:{width:"48px",height:"24px"},type:"primary",onClick:()=>window.open(`${i}/wireless/devices/${t.serialNumber}#/devices/${t.serialNumber}`,"_blank"),children:"View"})}),e.jsxs("div",{style:{display:"flex",alignItems:"center",height:"24px",gap:"8px",marginLeft:"4px"},children:[Ht(t.ues),Yn(t.dashboardData.health)]}),La(t.averageRssi)]})},Mi=({data:t,serialNumber:a})=>{var i;const s=je(),n=((i=s.selectedItem)==null?void 0:i.type)==="RADIO"&&s.selectedItem.data.band===t.band&&s.selectedItem.serialNumber===a;return e.jsxs("div",{style:{display:"flex",alignItems:"center",fontWeight:n?"bold":"normal",backgroundColor:n?"#f0f0f0":"transparent",borderRadius:"10px",padding:"2px 4px"},children:[e.jsx("img",{src:Ti,alt:"WiFi Signal",width:"20",height:"20",style:{marginRight:"4px"}}),e.jsxs("span",{style:{fontFamily:"Lato",marginRight:"4px"},children:[t.band,"G"]}),e.jsx("span",{style:{marginRight:"49px"},children:Ht(t.amountOfUes)})]})},Ri=({data:t})=>{var n;const a=je(),s=((n=a.selectedItem)==null?void 0:n.type)==="SSID"&&a.selectedItem.data.bssid===t.bssid;return e.jsxs("div",{style:{display:"flex",alignItems:"center",fontWeight:s?"bold":"normal",backgroundColor:s?"#f0f0f0":"transparent",borderRadius:"10px",padding:"2px 4px"},children:[e.jsx("img",{src:Si,alt:"Wireless Signal",width:"20",height:"20",style:{marginRight:"4px"}}),e.jsx("span",{style:{fontFamily:"Lato",marginRight:"6px",whiteSpace:"nowrap"},children:t.ssid}),Ht(t.amountOfUes)]})},_i=({data:t})=>{var n;const a=je(),s=((n=a.selectedItem)==null?void 0:n.type)==="UE"&&a.selectedItem.data.station===t.station;return e.jsxs("div",{style:{display:"flex",alignItems:"center",fontWeight:s?"bold":"normal",backgroundColor:s?"#f0f0f0":"transparent",borderRadius:"10px",padding:"2px 4px"},children:[e.jsx("img",{src:Ai,alt:"User Icon",width:"20",height:"20",style:{marginRight:"4px"}}),e.jsx("span",{style:{fontFamily:"Lato",marginRight:"4px"},children:t.station}),La(t.rssi)]})},Ui="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAA1VJREFUWEftV0toE1EUvffNpB+1ChrNTJs2v0nB70IriCCiKEIXXYmiogu3IijiRtciKChSPwsXIi787VxqKS5UqJ+FKP5m4oytSSa21kVLNU3yriR04uQnrTPFFvKW99177plz77z7HsIcXzjH+UGdoNMKzT8FAwGvzBrEXiLsRqAxArhqaOYZAMg6VeNf4ssVZEFFeoqIm+xgRPyUoaXOWjav19uyaIlnHzBo/pektWI4hyeDseQr+34JwfaIb43I2JtyAALSDdUMW/agIt1FxD1ukpvCmkznfkUTn38MWtglBDsi8gaBwcsqBBOGarZZ9lBUfgQAO2aBIOSIVg9q5ruqBBUFGrMovUfAUElypKv6J/OIZeuI+sIM8CQANLlJEgn7dS15q2aJ8xsBxbuegec2IHQSAQHAg3Ehc3Dk48iYm2Smi1XrmBFDIW8kzfhEIjY6NF2w2fCbf+fgbKjgBLOuoBP18rHzUkEhEPXtZcC6ifNxDux6+fhxqspM4isUDCryBUQ4bgNJA2GPriUe2oH9/sVLPZ6FLTNJ9jffSSGdi2ujCQDgNQ9qf+eyNg95DAAU7U4ENGCoZvECEYpIJwjhPCK62iJE8MzQklvtN6eSBIHIis2MCU+rfOmIriaXF2exIr0AxC631LPjZHg2+jU2rFWdxa2drd4GTl8QYUFZ8j5dTe60bIFo635G/BogLnaPJGUJ8L6hJg8AFEZsYVWUKBTxHSXEi4goTPkMT2ZzO+P6t9fukZk+UtUeag/LXQxhFzI+Bhl+zzCGzelDuuvpapO7S61GiWcjiRPMuoJO1Ju3sxj8fn+zKGbWcRHHBzXzffn4carKTOIrz8GQvIVEuoOArXkgInqc+Snsjsfj32cC7JZvCcGCcs05FQGKT8wCSaAbhmoetpJ2KNIqhngaqGLiOOKFxPv1WKrXDlJCMByWNpKAzyuyEJm6ZsqWPahI/Yi4zRGbGsFZztcOxVJvq466QEBeyRqg+GguYhCoupbs/ENQvokIh9wmSAQTuXQmOjQ0kr92FVZ5D2JQkfoQcbs9OQE/ZqipS5bN74dmsdHXAwK6+nCHSTZgGIkPNUuc3yhcRJsWniOkbiQcI+RXDDV1+X/9yfVJ4rRP6wo6VfA381ATOPL5SfMAAAAASUVORK5CYII=",Di="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAdJJREFUOE+NVLtxwkAQ3T2JgAx3AB3gTJDYCgSRwFQA7sBUAFQAHUAHfBMDM5AhR6YDXAKRCRBas7KkEZIOsYlm7t4+7b59twiSmC13LUSsA0AeAIoMI4IfIWALgNuz8jtt6Poxmo7RAyYCwA6iS3QvjkTUrlVKozDohnD6uesKgZ0UoptrAhjUDK3tHwaEs5XVR4CPBDJuaw8AOa99/t6E41CvXi11+dAl9PQayoDjzSbHevFXsbOdpB8T0Tu3jwxS7ezBqyDgvGo4OiunduaS7V+H0XKHAk67ZpQHs6V1iGrMA6tVtAIu1l9NIroRlpNtujyrQnkBgkG4ctPQcLGy+pQgD1eJ85U1vmr0FtPOcRogRDN6Z6unp4yd7SQRXu01waTyZVP2xb+Ts+cK6RGbBGRyN7g0jxJOTENrLNbW0B+QrAgm/PaflgzkDogwD0Kw3tLgScuHEkqzbSqoKvYThxfCsdVQZupHdE1yhvdS4kYNgY82XXQVFX7jcXt5wMDY956eh92ahqanLw7STaO0DZZDSgIviNhS8LuILQf/Ir2KuLJhsmDbhGHz1e6VCIdpC5Y1A6De3QUbJUbEJhEUif7bRYQjIuzp4kzNanmS5IQ/6ZXqaIgQp5cAAAAASUVORK5CYII=",Oi={width:"100%",height:"180px",borderRadius:"8px",boxSizing:"border-box",border:"none",padding:0,margin:0,overflow:"hidden"},Ni={height:"100%",width:"100%",padding:0,margin:0,boxSizing:"border-box"},Bi={display:"flex",justifyContent:"space-between",alignItems:"center",padding:"0 16px",boxSizing:"border-box",height:"60px",margin:0},Li="120px",qi={margin:0,borderColor:"#DEEAF3",padding:0},zi={border:"none",outline:"none",background:"transparent",padding:0},Vi={marginLeft:2,width:12,height:12},za=document.createElement("style");za.textContent=`
  .custom-metric-card .ant-card-body {
    padding: 0 !important;
    margin: 0 !important;
  }
  .custom-metric-card .ant-card-head {
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: none !important;
  }
  .custom-metric-card {
    padding: 0 !important;
    margin: 0 !important;
  }
`;document.head.appendChild(za);const{Title:Qi}=ve,wt=({title:t,explanation:a,bgGradient:s,content:n,onDetailClick:i,buttonColor:r})=>{const{t:l}=K(),[d,f]=o.useState(!1),c=typeof a=="string"?a:a.key,x=typeof a=="object"?a.params||{}:{},g={...zi,backgroundColor:d?r:"transparent",border:"none",padding:"6px 6px",display:"flex",borderRadius:"2px"};return e.jsx(pt,{style:{...Oi,background:s,boxShadow:"none"},bodyStyle:{padding:0,margin:0,border:0,boxShadow:"none"},bordered:!1,className:"custom-metric-card",children:e.jsxs("div",{style:Ni,children:[e.jsxs("div",{style:Bi,children:[e.jsxs(Qi,{level:4,style:{margin:0,paddingLeft:0,whiteSpace:"nowrap"},children:[l(t),e.jsx(ue,{title:l(c,x),children:e.jsx("img",{src:Di,style:Vi})})]}),e.jsx(ue,{title:l("common.view_details"),children:e.jsx("div",{"aria-label":l("common.view_details"),onClick:i,style:g,onMouseEnter:()=>f(!0),onMouseLeave:()=>f(!1),children:e.jsx("img",{src:Ui,style:{height:20,width:20}})})})]}),e.jsx(oe,{style:qi}),e.jsx("div",{style:{height:Li,padding:0,boxSizing:"border-box",overflow:"hidden"},children:n})]})})},Gi="/assets/Logo_Health-CXJwZwbP.png",{Text:Hi}=ve,Ki=()=>{const{t}=K(),{dashboard:a,handleDashboardModalOpen:s}=je(),n=`${(a==null?void 0:a.avgHealth)??0}%`;return e.jsx(wt,{title:"analytics.average_health",explanation:"analytics.average_health_explanation",bgGradient:"linear-gradient(180deg, #F2F9FE 0%, #E6F4FE 100%)",buttonColor:"#D9EFFC",content:e.jsxs("div",{style:{display:"flex",alignItems:"center",height:"100%",padding:0},children:[e.jsx(Hi,{style:{fontSize:"24px",fontWeight:"bold",marginLeft:"32px",whiteSpace:"nowrap"},children:n}),e.jsx("div",{style:{flex:1}}),e.jsx("img",{src:Gi,alt:"Health Curve Icon",style:{marginRight:"32px",width:"115px",height:"70px"}})]}),onDetailClick:()=>s({prioritizedColumns:["lastHealth","health"],sortBy:[{id:"health",desc:!1}]})})},$i=o.memo(({value:t,color:a="#52c41a",bgColor:s="#ccc",height:n="120px",width:i="120px"})=>{const r=o.useRef();return o.useEffect(()=>{const l=ks(r.current),d={series:[{type:"pie",radius:["45%","65%"],center:["50%","50%"],data:[{value:t,name:"",itemStyle:{color:a}},{value:100-t,name:"",itemStyle:{color:s}}],label:{show:!1},emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};l.setOption(d);const f=()=>{l.resize()};return window.addEventListener("resize",f),()=>{window.removeEventListener("resize",f),l.dispose()}},[t,a,s]),e.jsx("div",{style:{height:n,width:i,marginLeft:"auto"},ref:r})}),{Text:Wi}=ve,Ji=()=>{const{t}=K(),{dashboard:a,handleDashboardModalOpen:s}=je(),n=`${(a==null?void 0:a.avgMemoryUsed)??0}%`;return e.jsx(wt,{title:"analytics.average_memory",explanation:"analytics.average_memory_explanation",bgGradient:"linear-gradient(180deg, #F5FEF2 0%, #E6FEEE 100%)",buttonColor:"#DDF6D9 ",content:e.jsxs("div",{style:{display:"flex",alignItems:"center",height:"100%",padding:0},children:[e.jsx(Wi,{style:{fontSize:"24px",fontWeight:"bold",marginLeft:"32px",whiteSpace:"nowrap"},children:n}),e.jsx("div",{style:{flex:1}}),e.jsx($i,{value:(a==null?void 0:a.avgMemoryUsed)??0,style:{marginRight:"32px"}})]}),onDetailClick:()=>s({prioritizedColumns:["lastPing","memory"],sortBy:[{id:"memory",desc:!0}]})})},Xi="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAYAAADhu0ooAAAAAXNSR0IArs4c6QAAAlFJREFUaEPtWz1PG0EQnY89USAcKQpKEZfpEBiliGiwcISVgjhABb8ALCpk/kCUUKGQJjRJHaTocBPZ0CTgiAJooMcY/wIEDkgpbIyju7OlFMbRrdCxvoztK1e65zdv5u3OLA5mEzWF7HzBe2h3byr/AkL2waFs4pqQWTWBEtLP/al8ImQ4wQMKzIo8RsMNVBgNTwCLRsPDpYdEGBVGu/QfkDraibiZ7/OTCvkpMYMF5BiNRqU/srYx8KZqKuFajM78SOcU0ituuimFXP/dU324PvLx0mygPp1R9wL16XW7F6gw2l5xwqjxyUg0GrbQFUaFUXFGZltA0aho1LdGlw9XR5DoJYECRc5DgDf1r4uxheMgwl3rzEjHAr47+rBEQO/dtkfrsPwGU4vDc/nggAagUTOABrB7MQOoMHp3WVcYDTQZiUbvN3Rt22Z+Hon1IKIFFjg/Zuti/PFo2W9JMrqOfjnZivRacOFWXmRwm9WkcuNPRl/rATU063pA8ZyBuAWUkPPJaDylB9RQjd490JAxWqyUPxHSM4XkhLozaaPXHw3K6+oyWqyUC4Q41pq0UazqgfVedAyDPtBSgZDbAA2ZRoVRv5NjotEOLQnRaAdTr5+MJOv6m+4UjYpGAYJNRqJR0WjbbZ/5yUi8rr+ZemHUmPLyn2fdmnu9p3nvBZF2D/5xwWd2O/2NkSZaa5Qz3Vm9erSR/PzrtkOrt0erGQa14u763W4aQaPRmM7E0rnb1tin9gOL+84YyX0/Zy0hbyaj8clOh2PHldKOIhX/+/3+ALGCM2mKKzofAAAAAElFTkSuQmCC",{Text:oa}=ve,Zi=()=>{const{t}=K(),{dashboard:a,handleDashboardModalOpen:s}=je(),n=[{label:"2G",value:(a==null?void 0:a.twoGAssociations)??0},{label:"5G",value:(a==null?void 0:a.fiveGAssociations)??0},{label:"6G",value:(a==null?void 0:a.sixGAssociations)??0}],i=e.jsx("div",{style:{display:"flex",justifyContent:"space-around",padding:"16px",height:"100%",marginTop:12},children:n.map(({label:r,value:l})=>e.jsxs("div",{style:{display:"flex",width:"33.33%",justifyContent:"center"},children:[e.jsx("img",{src:Xi,alt:`${r} Associations`,style:{height:24,width:24,marginRight:8,marginTop:24}}),e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"flex-start"},children:[e.jsx(oa,{style:{fontSize:"24px",fontWeight:"900"},children:l}),e.jsx(oa,{style:{fontSize:"16px",color:"#777",marginTop:4},children:r})]})]},r))});return e.jsx(wt,{title:"analytics.associations",explanation:"analytics.associations_explanation",bgGradient:"linear-gradient(180deg, #F2F9FE 0%, #E6F4FE 100%)",content:i,buttonColor:"#D9EFFC",onDetailClick:()=>s({prioritizedColumns:["2g","5g","6g"],sortBy:[{id:"2g",desc:!0},{id:"5g",desc:!0},{id:"6g",desc:!0}]})})},{Title:dd,Text:it}=ve,Yi=()=>{const{t}=K(),{dashboard:a,handleDashboardModalOpen:s}=je(),n=e.jsxs("div",{style:{display:"flex",justifyContent:"space-around",alignItems:"center",padding:"24px 8px",height:"100%"},children:[e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",marginRight:32},children:[e.jsx(it,{style:{fontSize:"24px",fontWeight:"bold"},children:(a==null?void 0:a.connectedDevices)??0}),e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx("div",{style:{width:10,height:10,borderRadius:"50%",background:"#52c41a",marginRight:4}}),e.jsx(it,{style:{fontSize:"16px",whiteSpace:"nowrap"},children:t("analytics.connected")})]})]}),e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center"},children:[e.jsx(it,{style:{fontSize:"24px",fontWeight:"bold"},children:(a==null?void 0:a.disconnectedDevices)??0}),e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx("div",{style:{width:10,height:10,borderRadius:"50%",background:"#ccc",marginRight:4}}),e.jsx(it,{style:{fontSize:"16px",color:"#999",whiteSpace:"nowrap"},children:t("analytics.disconnected")})]})]})]});return e.jsx(wt,{title:"common.status",explanation:{key:"analytics.total_devices_explanation",params:{connectedCount:(a==null?void 0:a.connectedDevices)??0,disconnectedCount:(a==null?void 0:a.disconnectedDevices)??0}},bgGradient:"linear-gradient(180deg, #F6F7FF 0%, #ECECFF 100%)",content:n,buttonColor:"#DCE0FD",onDetailClick:()=>s({prioritizedColumns:["connected"],sortBy:[{id:"connected",desc:!0}]})})},er=()=>e.jsxs("div",{style:{width:"100%",margin:"0 auto ",display:"flex",gap:"32px"},children:[e.jsx("div",{style:{flex:1,minWidth:0},children:e.jsx(Ki,{})}),e.jsx("div",{style:{flex:1,minWidth:0},children:e.jsx(Ji,{})}),e.jsx("div",{style:{flex:1,minWidth:0},children:e.jsx(Yi,{})}),e.jsx("div",{style:{flex:1.22,minWidth:0},children:e.jsx(Zi,{})})]}),tr=({venueId:t="0"})=>{if(window.location.hash){const s=window.location.hash.replace("#","");/^\d+$/.test(s)&&(t=parseInt(s,10))}const a=gt();return o.useEffect(()=>{var s;if((s=a.state)!=null&&s.scrollToClientLifecycle){const n=document.getElementById("client-lifecycle-card");n&&n.scrollIntoView({behavior:"smooth"})}},[a]),e.jsx(ni,{venueId:t,children:e.jsxs("div",{style:nr,children:[e.jsx(er,{}),e.jsxs("div",{style:ir,children:[e.jsx("div",{style:rr,children:e.jsx(ri,{})}),e.jsx("div",{style:lr,children:e.jsx(Fi,{})})]}),e.jsx("div",{id:"client-lifecycle-card",style:or,children:e.jsx(bi,{venueId:t})})]})})},ar=32,sr=16,nr={display:"flex",flexDirection:"column",gap:"24px",width:"100%",padding:`${sr}px`,boxSizing:"border-box"},ir={display:"grid",gridTemplateColumns:"1fr 1fr 1fr 1.22fr",gap:`${ar}px`,width:"100%",boxSizing:"border-box"},rr={gridColumn:"1 / span 3",minHeight:"360px",background:"#FFFFFF",border:"1px solid #E7E7E7",borderRadius:"8px",overflow:"hidden"},lr={gridColumn:"4",minHeight:"360px",background:"#FFFFFF",border:"1px solid #E7E7E7",borderRadius:"8px",overflow:"hidden"},or={height:"auto",minHeight:"380px",background:"#FFFFFF",border:"1px solid #E7E7E7",borderRadius:"8px",overflow:"hidden",width:"100%",boxSizing:"border-box"};function J(t){var n,i;let a=t;const s=Is;if(t&&s){const r=t.split("."),{length:l}=r;if(l>=2){const d=r.slice(0,l-1),f=r[l-1];a=(i=(n=s[d.slice(0,l-1).join(".")])==null?void 0:n.properties[f??""])==null?void 0:i.description}}return{title:a,icon:e.jsx(_n,{style:{color:"#B3BBC8",cursor:"pointer",paddingLeft:0,marginLeft:2,width:10,height:10}})}}const{Option:dr}=Q,Ve={labelCol:{style:{width:150}},wrapperCol:{style:{width:300}}},cr=({namePrefix:t,isDisabled:a,values:s,setFieldValue:n})=>{const{t:i}=K(),[r,l]=G.useState(""),[d,f]=G.useState(""),c=X.get(s,[t,"band"]),x=X.get(s,[t,"legacy-rates"]),g=o.useMemo(()=>c==="2G"&&x?[{value:1e3,label:"1000"},{value:2e3,label:"2000"},{value:5500,label:"5500"},{value:6e3,label:"6000"},{value:9e3,label:"9000"},{value:11e3,label:"11000"},{value:12e3,label:"12000"},{value:18e3,label:"18000"},{value:24e3,label:"24000"},{value:36e3,label:"36000"},{value:48e3,label:"48000"},{value:54e3,label:"54000"}]:[{value:6e3,label:"6000"},{value:9e3,label:"9000"},{value:12e3,label:"12000"},{value:18e3,label:"18000"},{value:24e3,label:"24000"},{value:36e3,label:"36000"},{value:48e3,label:"48000"},{value:54e3,label:"54000"}],[c,x,i]);return o.useEffect(()=>{const h=X.get(s,[t,"he"]);h&&typeof h=="object"&&Object.keys(h).length===0&&n([t,"he"],void 0);const j=X.get(s,[t,"rates"]);j&&typeof j=="object"&&Object.keys(j).length===0&&n([t,"rates"],void 0)},[s,t,n]),e.jsxs(e.Fragment,{children:[e.jsxs(U,{gutter:64,children:[e.jsx(F,{span:10,children:e.jsx(k.Item,{label:"Beacon-Rate",tooltip:J("radio.rates.beacon"),...Ve,children:e.jsx(Q,{style:{width:280},value:X.get(s,[t,"rates","beacon"],6e3),onChange:h=>{n([t,"rates","beacon"],h)},placeholder:i("common.none"),children:g.map(h=>e.jsx(Q.Option,{value:h.value,children:h.label},h.value))})})}),e.jsx(F,{span:10,children:e.jsx(k.Item,{label:"Beacon-Interval",required:!0,...Ve,validateStatus:X.get(s,[t,"beacon-interval"],100)<=14||X.get(s,[t,"beacon-interval"],100)>=65535?"error":"",help:X.get(s,[t,"beacon-interval"],100)<=14?"Beacon-Interval must be greater than 14":X.get(s,[t,"beacon-interval"],100)>=65535?"Beacon-Interval must be less than 65535":"",children:e.jsx(ne,{value:X.get(s,[t,"beacon-interval"],100),onChange:h=>n([t,"beacon-interval"],h??100),disabled:a,style:{width:140}})})})]}),e.jsxs(U,{gutter:64,children:[e.jsx(F,{span:10,children:e.jsx(k.Item,{label:"Multicast",tooltip:J("radio.rates.multicast"),...Ve,style:{marginBottom:4},children:e.jsx(Q,{style:{width:280},value:X.get(s,[t,"rates","multicast"],24e3),onChange:h=>{n([t,"rates","multicast"],h)},placeholder:"Select multicast rate",children:g.map(h=>e.jsx(dr,{value:h.value,children:h.label},h.value))})})}),e.jsx(F,{span:10,children:e.jsx(k.Item,{label:"BSS-Color",tooltip:J("radio.he.bss-color"),...Ve,style:{marginBottom:4},className:"inline-error-fixed",required:!0,validateStatus:typeof X.get(s,[t,"he","bss-color"])=="number"&&X.get(s,[t,"he","bss-color"])<0?"error":"",help:typeof X.get(s,[t,"he","bss-color"])=="number"&&X.get(s,[t,"he","bss-color"])<0?"BSS-Color cannot be negative":"",children:e.jsx(ne,{disabled:a,max:63,style:{width:140},value:a?0:X.get(s,[t,"he","bss-color"],0),onChange:h=>{a||n([t,"he","bss-color"],h??0)}})})})]}),e.jsx(U,{gutter:64,children:c!=="2G"&&e.jsx(F,{span:10,children:e.jsx(k.Item,{label:"Allow-DFS",tooltip:J("radio.allow-dfs"),...Ve,style:{marginTop:20,marginBottom:4},children:e.jsx(te,{checked:X.get(s,[t,"allow-dfs"]),onChange:h=>n([t,"allow-dfs"],h),disabled:a})})})})]})},Pe="/ampcon/wireless/configure";function ur({site_id:t,name:a,security:s,radio:n,network_name:i,ssid_configure:r,labels_name:l,network_type:d,vlan_or_dhcp_name:f}){return de({url:`${Pe}/ssid`,method:"POST",data:{site_id:t,name:a,security:s,radio:n,network_name:i,ssid_configure:r,labels_name:l,network_type:d,vlan_or_dhcp_name:f}})}function hr({id:t,name:a,security:s,radio:n,network_name:i,ssid_configure:r,labels_name:l,network_type:d,vlan_or_dhcp_name:f}){return de({url:`${Pe}/ssid`,method:"PUT",data:{id:t,name:a,security:s,radio:n,network_name:i,ssid_configure:r,labels_name:l,network_type:d,vlan_or_dhcp_name:f}})}function fr({id:t,is_enable:a}){return de({url:`${Pe}/ssid`,method:"PUT",data:{id:t,is_enable:a}})}function mr({id:t}){return de({url:`${Pe}/ssid`,method:"DELETE",data:{id:t}})}function pr(t,a,s,n=[],i=[],r={}){return de({url:`${Pe}/ssid/list`,method:"POST",data:{site_id:t,filterFields:n,sortFields:i,searchFields:r,page:a,pageSize:s}})}function da(t){return de({url:`${Pe}/ssid`,method:"GET",params:{id:t}})}function gr(t){return de({url:`${Pe}/channel`,method:"GET",params:{countryCode:t}})}function Va(t,a,s){return de({url:`${Pe}/general`,method:"PUT",data:{id:t,site_id:a,config:s}})}const{Option:Et}=Q,Qe={labelCol:{style:{width:150}},wrapperCol:{style:{width:300}}},Ft=(t,a,s,n,i,r=[])=>{var A;const l=`${t.toLowerCase()}_channel`,d=Object.keys((n==null?void 0:n[l])||{}).filter(u=>u!=="dfs"),f=String(X.get(a,[t,"channel-width"])),c=d.map(u=>({label:`${u} MHz`,value:String(u)})),x=f?((A=n==null?void 0:n[l])==null?void 0:A[f])||[]:[],g=o.useRef([]);o.useEffect(()=>{const u=r??[],p=X.get(a,[t,"maximum-clients"]);g.current.includes(t)&&!u.includes(t)&&p===void 0&&s([t,"maximum-clients"],64),g.current=u},[t,r,s,a]);const h=i?64:X.get(a,[t,"maximum-clients"]),j=typeof h=="number"&&h<1;return e.jsx(k,{layout:"horizontal",labelAlign:"left",disabled:i,children:e.jsxs("div",{children:[e.jsxs(U,{gutter:64,children:[e.jsx(F,{span:10,children:e.jsx(k.Item,{label:"TX-Power",...Qe,children:e.jsxs(Ce.Group,{value:typeof X.get(a,[t,"tx-power"])=="number"?"Set Power":"Default",onChange:u=>{u.target.value==="Default"?s([t,"tx-power"],void 0):typeof X.get(a,[t,"tx-power"])!="number"&&s([t,"tx-power"],1)},children:[e.jsx(Ce,{value:"Default",children:"Default"}),e.jsx(Ce,{value:"Set Power",children:e.jsxs("span",{style:{display:"inline-flex",alignItems:"center"},children:["Set Power",typeof X.get(a,[t,"tx-power"])=="number"&&e.jsx(ne,{min:1,max:30,value:X.get(a,[t,"tx-power"]),onChange:u=>s([t,"tx-power"],u),style:{width:110,marginLeft:8},addonAfter:"dBm"})]})})]})})}),e.jsx(F,{span:10,children:e.jsx(k.Item,{label:"Channel-Width",tooltip:J("radio.channel-width"),required:!0,...Qe,children:e.jsx(Q,{style:{width:280},value:c.some(u=>u.value===f)?f:"None",onChange:u=>{s([t,"channel-width"],u),s([t,"channel"],"auto")},children:c.map(u=>e.jsx(Et,{value:u.value,children:u.label},u.value))})})})]}),e.jsxs(U,{gutter:64,children:[e.jsx(F,{span:10,children:e.jsx(k.Item,{label:"Channel",...Qe,children:e.jsxs(Q,{style:{width:280},value:x.length===0?"None":X.get(a,[t,"channel"]),onChange:u=>s([t,"channel"],u),disabled:i||!f,placeholder:"Select Channel",children:[e.jsx(Et,{value:"auto",children:"auto"}),(x||[]).map(u=>e.jsx(Et,{value:u,children:u},u))]})})}),e.jsx(F,{span:10,children:e.jsx(k.Item,{...Qe,label:"Maximum-Clients",tooltip:J("radio.maximum-clients"),validateStatus:j?"error":"",help:j?"Maximum-Clients must be a positive number":"",children:e.jsx(ne,{style:{width:140},value:h,onChange:u=>s([t,"maximum-clients"],u)})})})]}),e.jsx(U,{gutter:64,children:t==="2G"&&e.jsx(F,{span:10,children:e.jsx(k.Item,{label:"Legacy-Rates",tooltip:J("radio.legacy-rates"),...Qe,children:e.jsx(te,{checked:X.get(a,[t,"legacy-rates"]),onChange:u=>{s([t,"legacy-rates"],u?!0:void 0),s([t,"rates","multicast"],24e3),s([t,"rates","beacon"],6e3)}})})})}),e.jsx(cr,{namePrefix:t,isDisabled:i,values:a,setFieldValue:s})]})})},xr=o.forwardRef(({initialValues:t,onApply:a,countryCode:s,onChange:n},i)=>{const r=o.useRef(null),[l,d]=o.useState({}),[f,c]=o.useState(t),[x,g]=o.useState([]);return o.useImperativeHandle(i,()=>({submit:()=>{const h=r.current;if(!h)return;const j=X.omit(h.values,x);for(const A in j)if(j[A]=X.omitBy(j[A],X.isNil),X.has(j[A],"channel-width")){const u=j[A]["channel-width"];u!=null&&u!=="auto"&&u!=="None"&&(j[A]["channel-width"]=Number(u))}a(j)},resetForm:()=>{const h=r.current;h&&h.resetForm({values:t})}})),o.useEffect(()=>{gr(s).then(h=>{const j=h||{};d(j);const A=[];["2G","5G","6G"].forEach(p=>{const v=`${p.toLowerCase()}_channel`,b=j[v];b&&Object.keys(b).length>0||A.push(p)}),g(A);const u=X.cloneDeep(t);["2G","5G","6G"].forEach(p=>{const v=`${p.toLowerCase()}_channel`,b=j[v],m=X.get(u,[p,"channel-width"]),I=X.get(u,[p,"channel"]),S=Object.keys(b||{}),y=m&&S.includes(String(m)),w=(y?b[m]||[]:[]).includes(I);if(y)w||X.set(u,[p,"channel"],"auto");else{const E=p==="2G"?"20":"40",T=S.includes(E)?E:S[0]||"none";X.set(u,[p,"channel-width"],T),X.set(u,[p,"channel"],"auto")}["2G","5G","6G"].forEach(E=>{(A.includes(E)||!X.has(u,E))&&(X.set(u,[E,"rates","multicast"],24e3),X.set(u,[E,"rates","beacon"],6e3))})}),c(u)})},[s,t]),e.jsx("div",{children:e.jsx(qt,{innerRef:r,enableReinitialize:!0,initialValues:f,onSubmit:h=>{a(h)},children:({values:h,setFieldValue:j})=>{const A=(u,p)=>{u[u.length-1]==="channel-width"&&p!=="None"&&(p=Number(p));const v=u.join("."),b=X.set(X.cloneDeep(h),v,p),m=zt(X.omitBy(b,I=>typeof I["channel-width"]=="string"));n==null||n(m),j(v,p)};return e.jsx(Pa,{children:e.jsxs(_e,{className:"SetRadioForm",style:{marginBottom:0,border:"none"},expandIconPosition:"right",children:[e.jsx(_e.Panel,{header:e.jsx("h3",{style:{fontSize:"16px",margin:0,border:"none"},children:"2G Radio"}),children:Ft("2G",h,A,l,x.includes("2G"),x)},"2g"),e.jsx("div",{style:{height:20,backgroundColor:"#ffff"}}),e.jsx(_e.Panel,{header:e.jsx("h3",{style:{fontSize:"16px",margin:0,border:"none"},children:"5G Radio"}),children:Ft("5G",h,A,l,x.includes("5G"),x)},"5g"),e.jsx("div",{style:{height:20,backgroundColor:"#ffff"}}),e.jsx(_e.Panel,{header:e.jsx("h3",{style:{fontSize:"16px",margin:0,border:"none"},children:"6G Radio"}),children:Ft("6G",h,A,l,x.includes("6G"),x)},"6g")]})})}})})}),br=(t,a=!1,s="2G")=>{const n=H().shape({band:B().required(t("form.required")).default(s),channel:B().required(t("form.required")).default("auto"),"channel-width":W().required(t("form.required")).integer().default(s==="2G"?20:40),"legacy-rates":z().default(s==="2G"?!0:void 0),"allow-dfs":z().default(s!=="2G"?!0:void 0),"beacon-interval":W().required(t("form.required")).moreThan(14).lessThan(65535).integer().default(100),"maximum-clients":W().nullable().positive().integer().default(64),"hostadp-iface-raw":Y().of(B()).default(void 0),rates:H().shape({beacon:W().positive().integer().default(void 0),multicast:W().positive().integer().default(void 0)}).default(void 0),he:H().shape({"multiple-bssid":z().default(void 0),ema:z().default(void 0),"bss-color":W().min(0).integer().default(0)}).default(void 0)});return a?n:n.nullable().default(void 0)},Qa=({id:t,onSuccess:a=()=>{}})=>{const{t:s}=K(),n=Je(),i=Es();return Oe(["get-configuration",t],()=>Fs.get(`configuration/${t}?withExtendedInfo=true`).then(({data:r})=>r),{enabled:t!=null&&t!=="",keepPreviousData:!0,staleTime:10*1e3,onSuccess:a,onError:r=>{var l,d;n.isActive("configuration-fetching-error")||n({id:"configuration-fetching-error",title:s("common.error"),description:s("crud.error_fetching_obj",{obj:s("configurations.one"),e:(d=(l=r==null?void 0:r.response)==null?void 0:l.data)==null?void 0:d.ErrorDescription}),status:"error",duration:5e3,isClosable:!0,position:"top-right"}),r.code==="404"&&i()}})},vr=[{value:"AL",label:"Albania"},{value:"DZ",label:"Algeria"},{value:"AS",label:"American Samoa"},{value:"AD",label:"Andorra"},{value:"AO",label:"Angola"},{value:"AI",label:"Anguilla"},{value:"AG",label:"Antigua And Barbuda"},{value:"AR",label:"Argentina"},{value:"AM",label:"Armenia"},{value:"AN",label:"Netherlands Antilles"},{value:"AW",label:"Aruba"},{value:"AU",label:"Australia"},{value:"AT",label:"Austria"},{value:"AZ",label:"Azerbaijan"},{value:"BS",label:"Bahamas"},{value:"BH",label:"Bahrain"},{value:"BD",label:"Bangladesh"},{value:"BB",label:"Barbados"},{value:"BY",label:"Belarus"},{value:"BE",label:"Belgium"},{value:"BZ",label:"Belize"},{value:"BJ",label:"Benin"},{value:"BM",label:"Bermuda"},{value:"BO",label:"Bolivia"},{value:"BA",label:"Bosnia And Herzegovina"},{value:"BR",label:"Brazil"},{value:"BN",label:"Brunei Darussalam"},{value:"BG",label:"Bulgaria"},{value:"KH",label:"Cambodia"},{value:"CM",label:"Cameroon"},{value:"CA",label:"Canada"},{value:"CV",label:"Cape Verde"},{value:"KY",label:"Cayman Islands"},{value:"CL",label:"Chile"},{value:"CN",label:"China"},{value:"CO",label:"Colombia"},{value:"CR",label:"Costa Rica"},{value:"CI",label:"Cote D'Ivoire"},{value:"HR",label:"Croatia"},{value:"CY",label:"Cyprus"},{value:"CZ",label:"Czech Republic"},{value:"DK",label:"Denmark"},{value:"DO",label:"Dominican Republic"},{value:"EC",label:"Ecuador"},{value:"EG",label:"Egypt"},{value:"SV",label:"El Salvador"},{value:"EE",label:"Estonia"},{value:"ET",label:"Ethiopia"},{value:"FO",label:"Faroe Islands"},{value:"FJ",label:"Fiji"},{value:"FI",label:"Finland"},{value:"FR",label:"France"},{value:"PF",label:"French Polynesia"},{value:"TF",label:"French Southern Territories"},{value:"GA",label:"Gabon"},{value:"GE",label:"Georgia"},{value:"DE",label:"Germany"},{value:"GH",label:"Ghana"},{value:"GI",label:"Gibraltar"},{value:"GR",label:"Greece"},{value:"GL",label:"Greenland"},{value:"GD",label:"Grenada"},{value:"GU",label:"Guam"},{value:"GT",label:"Guatemala"},{value:"GG",label:"Guernsey"},{value:"GY",label:"Guyana"},{value:"HT",label:"Haiti"},{value:"VA",label:"Holy See (Vatican City State)"},{value:"HN",label:"Honduras"},{value:"HK",label:"Hong Kong"},{value:"HU",label:"Hungary"},{value:"IS",label:"Iceland"},{value:"IN",label:"India"},{value:"ID",label:"Indonesia"},{value:"IQ",label:"Iraq"},{value:"IE",label:"Ireland"},{value:"IL",label:"Israel"},{value:"IT",label:"Italy"},{value:"JM",label:"Jamaica"},{value:"JP",label:"Japan"},{value:"JO",label:"Jordan"},{value:"KZ",label:"Kazakhstan"},{value:"KE",label:"Kenya"},{value:"KR",label:"Korea"},{value:"KW",label:"Kuwait"},{value:"LA",label:"Lao People's Democratic Republic"},{value:"LV",label:"Latvia"},{value:"LB",label:"Lebanon"},{value:"LY",label:"Libyan Arab Jamahiriya"},{value:"LI",label:"Liechtenstein"},{value:"LT",label:"Lithuania"},{value:"LU",label:"Luxembourg"},{value:"MO",label:"Macao"},{value:"MK",label:"Macedonia"},{value:"MY",label:"Malaysia"},{value:"MV",label:"Maldives"},{value:"ML",label:"Mali"},{value:"MT",label:"Malta"},{value:"MU",label:"Mauritius"},{value:"MX",label:"Mexico"},{value:"FM",label:"Micronesia, Federated States Of"},{value:"MD",label:"Moldova"},{value:"MC",label:"Monaco"},{value:"ME",label:"Montenegro"},{value:"MA",label:"Morocco"},{value:"MZ",label:"Mozambique"},{value:"MM",label:"Myanmar"},{value:"NA",label:"Namibia"},{value:"NP",label:"Nepal"},{value:"NL",label:"Netherlands"},{value:"AN",label:"Netherlands Antilles"},{value:"NZ",label:"New Zealand"},{value:"NI",label:"Nicaragua"},{value:"NG",label:"Nigeria"},{value:"MP",label:"Northern Mariana Islands"},{value:"NO",label:"Norway"},{value:"OM",label:"Oman"},{value:"PK",label:"Pakistan"},{value:"PW",label:"Palau"},{value:"PA",label:"Panama"},{value:"PG",label:"Papua New Guinea"},{value:"PY",label:"Paraguay"},{value:"PE",label:"Peru"},{value:"PH",label:"Philippines"},{value:"PL",label:"Poland"},{value:"PT",label:"Portugal"},{value:"PR",label:"Puerto Rico"},{value:"QA",label:"Qatar"},{value:"RE",label:"Reunion"},{value:"RO",label:"Romania"},{value:"RU",label:"Russian Federation"},{value:"RW",label:"Rwanda"},{value:"KN",label:"Saint Kitts And Nevis"},{value:"LC",label:"Saint Lucia"},{value:"MF",label:"Saint Martin"},{value:"SM",label:"San Marino"},{value:"SA",label:"Saudi Arabia"},{value:"RS",label:"Serbia"},{value:"SG",label:"Singapore"},{value:"SK",label:"Slovakia"},{value:"SI",label:"Slovenia"},{value:"ZA",label:"South Africa"},{value:"ES",label:"Spain"},{value:"LK",label:"Sri Lanka"},{value:"SZ",label:"Swaziland"},{value:"SE",label:"Sweden"},{value:"CH",label:"Switzerland"},{value:"TW",label:"Taiwan"},{value:"TZ",label:"Tanzania"},{value:"TH",label:"Thailand"},{value:"TG",label:"Togo"},{value:"TT",label:"Trinidad And Tobago"},{value:"TN",label:"Tunisia"},{value:"TR",label:"Turkey"},{value:"TC",label:"Turks And Caicos Islands"},{value:"UG",label:"Uganda"},{value:"UA",label:"Ukraine"},{value:"AE",label:"United Arab Emirates"},{value:"GB",label:"United Kingdom"},{value:"US",label:"United States"},{value:"UY",label:"Uruguay"},{value:"UZ",label:"Uzbekistan"},{value:"VE",label:"Venezuela"},{value:"VN",label:"Viet Nam"},{value:"VI",label:"Virgin Islands, U.S."},{value:"WF",label:"Wallis And Futuna"},{value:"YE",label:"Yemen"},{value:"ZW",label:"Zimbabwe"}],{Option:yr}=Q,jr=({value:t,onChange:a,formItemProps:s,style:n})=>{const[i,r]=o.useState(""),l=vr.filter(f=>f.label.toLowerCase().includes(i.toLowerCase())),d=f=>e.jsxs("div",{children:[e.jsx(ae,{value:i,onChange:c=>r(c.target.value),placeholder:"Search",prefix:e.jsx(Ps,{component:Aa}),allowClear:!0,style:{width:"100%",height:"32px",marginBottom:"3px"}}),f]});return e.jsx(Q,{value:t,onChange:a,dropdownRender:d,style:n,onDropdownVisibleChange:f=>{f&&r("")},children:l.map(f=>e.jsx(yr,{value:f.value,children:f.label},f.value))})},Cr={labelCol:{style:{width:160}},wrapperCol:{style:{width:300}}};function wr(t){const a={};return(t.radios||[]).forEach(s=>{const n=s.band;n&&(a[n]=s)}),a}function Sr(t,a){const[s,n]=o.useState([]),[i,r]=o.useState(!0);return o.useEffect(()=>{t!=null&&(r(!0),Be(a,t,1,1e4).then(l=>(l==null?void 0:l.status)===200&&n(l.info||[])).catch(()=>D.error("Failed to fetch time range profiles")).finally(()=>r(!1)))},[t,a]),{profiles:s,setProfiles:n,loading:i}}function Tr(t){var d,f;const a=t!=null?`${t}-radio`:void 0,{data:s=[],refetch:n,isLoading:i}=Qa({id:a});o.useEffect(()=>{t&&n()},[t,n]);const r=(f=(d=s==null?void 0:s.configuration)==null?void 0:d[0])==null?void 0:f.configuration;return{radioValues:o.useMemo(()=>{if(!r)return null;try{return wr(JSON.parse(r))}catch{return null}},[r]),configId:a,isLoading:i,refetch:n}}const Ar=o.forwardRef(({onDirtyChange:t},a)=>{const[s]=k.useForm(),[n,i]=o.useState(!1),r=o.useRef(null),l=xt(),{t:d}=K(),f=4,c=["2G","5G","6G"],[x,g]=o.useState(!1),[h,j]=o.useState(!1),A=x||h;o.useEffect(()=>{t==null||t(A)},[A,t]),o.useImperativeHandle(a,()=>({reset:()=>ce(),apply:()=>q()}));const[u,p]=o.useState(null);o.useEffect(()=>{const $=location.hash.replace("#",""),V=Number($);p(V)},[location.hash]),o.useEffect(()=>{g(!1),j(!1)},[u]);const{profiles:v,setProfiles:b,loading:m}=Sr(u,f),{radioValues:I,configId:S,isLoading:y,refetch:C}=Tr(u),w=u==null||m||y,E=o.useMemo(()=>{const $=(I==null?void 0:I["2G"])||{};return{country:$.country||"US","radio-schedule-enable":$["radio-schedule-enable"]===1,"radio-schedule-radio-mode":$["radio-schedule-radio-mode"]??0,"time-range-index":$["time-range-index"]?String(Ge($["time-range-index"])):void 0,"time-range-name":$["time-range-name"]||void 0}},[I]),T=o.useMemo(()=>{if(!I)return null;const $=JSON.parse(JSON.stringify(I));return Object.entries($).forEach(([V,re])=>{delete re.country,delete re["radio-schedule-enable"],delete re["radio-schedule-radio-mode"],delete re["time-range-name"],delete re["time-range-index"]}),$},[I]),L=o.useRef(E),N=o.useRef(T);o.useEffect(()=>{w||(s.setFieldsValue(E),L.current=E,g(!1),T&&(N.current=T,j(!1)))},[w,E,T]);const M=k.useWatch("country",s),[R,O]=o.useState(E.country);o.useEffect(()=>{M&&O(M)},[M]);const q=()=>{s.validateFields().then(()=>{var $;($=r.current)==null||$.submit()}).catch(()=>{D.error("Please check your input")})},_=($,V)=>{const re=V["radio-schedule-enable"],pe={"radio-schedule-enable":re?1:0,country:V.country};return re&&(V["time-range-index"]&&(pe["time-range-index"]=He(V["time-range-index"])),V["time-range-name"]&&(pe["time-range-name"]=V["time-range-name"]),typeof V["radio-schedule-radio-mode"]<"u"&&(pe["radio-schedule-radio-mode"]=V["radio-schedule-radio-mode"])),{radios:c.map(be=>{const Ae=$[be];if(!Ae||Ae.enabled===!1)return null;const Ne={...Ae};return re||(delete Ne["radio-schedule-radio-mode"],delete Ne["time-range-name"],delete Ne["time-range-index"]),{...Ne,band:be,...pe}}).filter(Boolean)}},ie=async $=>{try{for(const be of Object.keys($)){const Ae=$[be];Ae&&await br(d).validate(Ae,{abortEarly:!1})}}catch{D.error("Please check your input");return}const V=s.getFieldsValue(),re=_($,V),pe=[{name:"Radio",description:"Radio configuration",weight:0,configuration:JSON.stringify(re)}];try{await Va(S,u,JSON.stringify(pe)),g(!1),j(!1),D.success("Successfully applied radio configuration")}catch{D.error("Failed to apply radio configuration");return}(await C()).error&&D.warning("Radio configuration saved, but refresh failed")},ce=async()=>{var V;(await C()).error?D.error("Failed to reset changes"):(s.resetFields(),(V=r.current)==null||V.resetForm(),g(!1),j(!1),D.info("Changes have been reset."))},me=k.useWatch("radio-schedule-enable",s);return e.jsx(ka,{spinning:w,children:e.jsxs("div",{style:{minWidth:1050},children:[e.jsxs(k,{form:s,layout:"horizontal",initialValues:E,labelAlign:"left",...Cr,onValuesChange:()=>g(!0),children:[e.jsx(U,{gutter:16,align:"middle",children:e.jsx(F,{span:10,style:{marginLeft:12},children:e.jsx(k.Item,{label:"Country",tooltip:J("radio.country"),required:!0,name:"country",noStyle:!1,children:e.jsx(jr,{value:s.getFieldValue("country"),onChange:$=>s.setFieldsValue({country:$}),style:{width:280}})})})}),e.jsx(U,{gutter:16,align:"middle",children:e.jsx(F,{span:10,style:{marginLeft:12},children:e.jsx(k.Item,{label:"Radio Scheduled",name:"radio-schedule-enable",valuePropName:"checked",noStyle:!1,children:e.jsx(te,{onChange:$=>{s.setFieldsValue({"radio-schedule-enable":$,"time-range-index":void 0,"time-range-name":void 0}),$&&u!=null&&Be(f,u,1,1e4).then(V=>(V==null?void 0:V.status)===200&&b(V.info||[])).catch(()=>D.error("Failed to reload time range profiles"))}})})})}),me&&e.jsxs(e.Fragment,{children:[e.jsx(U,{gutter:16,children:e.jsx(F,{span:10,style:{marginLeft:12},children:e.jsx(k.Item,{label:"Radio Mode",name:"radio-schedule-radio-mode",noStyle:!1,children:e.jsxs(Ce.Group,{children:[e.jsx(Ce,{value:1,children:"radio on"}),e.jsx(Ce,{value:0,children:"radio off"})]})})})}),e.jsx(k.Item,{name:"time-range-name",noStyle:!0,children:e.jsx(ae,{type:"hidden"})}),e.jsx(U,{gutter:16,children:e.jsx(F,{span:10,style:{marginLeft:12},children:e.jsxs("div",{style:{display:"flex",alignItems:"flex-start",gap:162},children:[e.jsx("div",{style:{width:280},children:e.jsx(k.Item,{label:"Time Range",name:"time-range-index",rules:[{required:!0,message:"Required!"}],validateTrigger:"onBlur",noStyle:!1,children:e.jsx(Q,{placeholder:"Select a time range Profile",value:s.getFieldValue("time-range-index"),style:{width:280},onChange:$=>{if($==="custom"){s.setFieldsValue({"time-range-index":null}),i(!0);return}const V=v.find(re=>String(re.variable_id)===String($));V&&s.setFieldsValue({"time-range-index":String(V.variable_id),"time-range-name":V.name})},dropdownRender:$=>e.jsxs("div",{children:[$,e.jsx(oe,{style:{margin:0}}),e.jsx(Z,{type:"link",icon:e.jsx(Ye,{}),onClick:()=>i(!0),style:{width:"100%",borderTop:"1px solid #E7E7E7"},children:"Create time range profile"})]}),children:v.map($=>e.jsx(Q.Option,{value:$.variable_id,children:$.name},$.id))})})}),e.jsx(Z,{type:"link",style:{padding:4},onClick:()=>l("/wireless/profile/TimeRange"),children:"Manage Time Range Profile"})]})})})]}),!w&&T&&e.jsx(xr,{ref:r,initialValues:T,onApply:ie,countryCode:R,onChange:$=>{!Ms($,N.current)&&j(!0)}})]}),e.jsx(Ma,{siteId:u,visible:n,dataSource:null,onClose:()=>i(!1),onSuccess:()=>{i(!1),u!=null&&Be(f,u,1,1e4).then($=>{if(($==null?void 0:$.status)===200){const V=$.info||[];if(b(V),V.length>0){const re=V[V.length-1];s.setFieldsValue({"time-range-index":String(re.variable_id),"time-range-name":re.name})}}}).catch(()=>{D.error("Failed to load time range profiles")})}})]})})}),Rt=t=>{const a=/^(([0-9])|([1-9][0-9])|(1([0-9]{2}))|(2[0-4][0-9])|(25[0-5]))((\.(([0-9])|([1-9][0-9])|(1([0-9]{2}))|(2[0-4][0-9])|(25[0-5]))){3})(\/(([0-9])|([12][0-9])|(3[0-2])))?$/gi;return t?a.test(t):!0},kr=t=>{const a=/^(?!:\/\/)(?=.{1,255}$)((.{1,63}\.){1,127}(?![0-9]*$)[a-z0-9-]+\.?)$/;return t!==void 0?t.length===0?!0:a.test(t):!1},Ir=({val:t,min:a,max:s})=>{if(t){const{length:n}=t;return!(n<a||n>s)}return!1},Ga=t=>t?t.match("^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$"):!1,Er=["m","h","d"],Fr=t=>{let a=!0;try{const s=t.match(/\D+|\d+/g);if(s&&s.length>0&&s.length<=6&&s.length%2===0)for(let n=0;n<s.length;n+=1)if(n%2===0){const i=s[n]??"";if(i.length===0||i==="0"){a=!1;break}}else{const i=s[n]??"";if(!Er.includes(i)){a=!1;break}}else a=!1}catch{a=!1}return a},ca=t=>{if(typeof t!="string")return!1;if(!t)return!0;const a=t.split(".")[0];if(a)try{const s=Number(a);if(s>=1&&(s<=223||s>239))return!0}catch{return!1}return!1},Pr=t=>{if(typeof t!="string")return!1;if(!t)return!0;const a=t.split(".")[0];if(a)try{const s=Number(a);if(s>=1&&s<=223&&s<=239)return!0}catch{return!1}return!1},Mr=["psk","psk2","psk-mixed","psk2-radius","sae","sae-mixed"],Ha=[{value:"none",label:"None"},{value:"psk",label:"WPA-PSK"},{value:"psk2",label:"WPA2-PSK"},{value:"psk2-radius",label:"PSK2-RADIUS"},{value:"psk-mixed",label:"WPA-PSK/WPA2-PSK Personal Mixed"},{value:"wpa",label:"WPA-Enterprise"},{value:"wpa2",label:"WPA2-Enterprise EAP-TLS"},{value:"wpa-mixed",label:"WPA-Enterprise-Mixed"},{value:"sae",label:"WPA3-SAE"},{value:"sae-mixed",label:"WPA2/WPA3 Transitional"},{value:"wpa3",label:"WPA3-Enterprise EAP-TLS"},{value:"wpa3-192",label:"WPA3-192-Enterprise EAP-TLS"},{value:"wpa3-mixed",label:"WPA3-Enterprise-Mixed"},{value:"owe",label:"OWE"},{value:"owe-transition",label:"OWE-Transition"}],Rr=["owe-transition"],_r=(t,a=!1)=>{const s=H().shape({"venue-name":Y().of(B()).default(void 0),"venue-url":Y().of(B()).default(void 0),"venue-group":W().moreThan(-1).lessThan(33).integer().default(1),"venue-type":W().moreThan(-1).lessThan(33).integer().default(1),"auth-type":H().shape({type:B().default("terms-and-condition"),url:B().default(void 0)}).default({type:"terms-and-condition",url:void 0}),"domain-name":Y().of(B()).default(void 0),"nai-realm":Y().of(B()).default(void 0),osen:z().default(void 0),"anqp-domain":W().moreThan(-1).lessThan(65535).integer().default(8888),"anqp-3gpp-cell-net":Y().of(B()).default(void 0),"friendly-name":Y().of(B()).default(void 0),"access-network-type":W().moreThan(-1).lessThan(15).integer().default(0),internet:z().default(!0),asra:z().default(void 0),esr:z().default(void 0),uesa:z().default(void 0),hessid:B().default(void 0),"roaming-consortium":Y().of(B()).default(void 0),"disable-dgaf":z().default(void 0),"ipaddr-type-available":W().moreThan(-1).lessThan(256).integer().default(void 0),"connection-capability":Y().of(B()).default(void 0),icons:Y().of(H()).default(void 0),"wan-metrics":H().shape({type:B().default("up"),downlink:W().moreThan(-1).integer().default(2e4),uplink:W().moreThan(-1).integer().default(2e4)}).default({type:"terms-and-condition",url:void 0})}).default({"venue-name":void 0,"venue-url":void 0,"venue-group":1,"venue-type":1,"auth-type":{type:"terms-and-condition",url:void 0},"domain-name":void 0,"nai-realm":void 0,osen:void 0,"anqp-domain":8888,"anqp-3gpp-cell-net":void 0,"friendly-name":void 0,"access-network-type":0,internet:!0,asra:void 0,esr:void 0,uesa:void 0,hessid:void 0,"roaming-consortium":void 0,"disable-dgaf":void 0,"ipaddr-type-available":void 0,"connection-capability":void 0,icons:void 0,"wan-metrics":{type:"up",downlink:2e4,uplink:2e4}});return a?s:s.nullable().default(void 0)},Ur=(t,a=!1)=>{const s=H().shape({"ingress-rate":W().required(t("form.required")).moreThan(-1).lessThan(65535).integer().default(0),"egress-rate":W().required(t("form.required")).moreThan(-1).lessThan(65535).integer().default(0)}).default({"ingress-rate":0,"egress-rate":0});return a?s:s.nullable().default(void 0)},Dr=(t,a=!1)=>{const s=H().shape({proto:B().required(t("form.required")).oneOf(Ha.map(({value:n})=>n)).test("encryption-6g-test",t("form.invalid_proto_6g"),(n,{from:i})=>{const r=i[1].value["wifi-bands"];return!(r&&r.includes("6G")&&Rr.includes(i[0].value.proto))}).test("encryption-passpoint-proto",t("form.invalid_proto_passpoint"),(n,{from:i})=>!0).default("psk"),ieee80211w:B().test("encryptionIeeeTest",t("form.invalid_ieee"),(n,{from:i})=>{const{proto:r}=i[0].value;return!((r==="owe"||r==="owe-transition")&&n==="disabled")}).test("encryptionRequiredIeee",t("form.invalid_ieee_required"),(n,{from:i})=>{const{proto:r}=i[0].value;return!((r==="wpa3"||r==="wpa3-192"||r==="wpa3-mixed"||r==="sae")&&n!=="required")}).default("disabled"),key:B().test("encryptionKeyTest",t("form.min_max_string",{min:8,max:63}),(n,{from:i})=>!Mr.includes(i[0].value.proto)||i[1].value.radius!==void 0?!0:n.length>=8&&n.length<=63).default(""),"key-caching":z().default(!0)}).default({proto:"psk2",ieee80211w:"required",key:"YOUR_SECRET"});return a?s:s.nullable().default(void 0)},Or=t=>Un(s=>typeof s=="object"?H().shape({"message-exchange":B().required(t("form.required")).default("ds"),"generate-psk":z().required(t("form.required")).default(!1),"domain-identifier":B().length(4).default(void 0),"pmk-r0-key-holder":B().default(void 0),"pmk-r1-key-holder":B().default(void 0)}).nullable().default(void 0):z().default(void 0)),Nr=(t,a=!1)=>{const s=H().shape({mode:B().required(t("form.required")).default("allow"),"mac-address":Y().of(B().test("ssid.access-control-list.mac",t("form.invalid_mac_uc"),Ga)).required(t("form.required")).min(1,t("form.required")).default([])}).default({mode:"allow","mac-address":[]});return a?s:s.nullable().default(void 0)},Br=(t,a=!1)=>{const s=H().shape({"neighbor-reporting":z().default(!0),lci:B().default(""),"civic-location":B().default(""),"ftm-responder":z().default(!1),"stationary-ap":z().default(!0)}).default({"neighbor-reporting":!0,"ftm-responder":!1,"stationary-ap":!0});return a?s:s.nullable().default(void 0)},Lr=(t,a=!1)=>{const s=H().shape({"auth-mode":B().required(t("form.required")).default("off"),"walled-garden-fqdn":Y().default(void 0),"walled-garden-ipaddr":Y().of(B()).default(void 0),"web-root":B().default(void 0),"idle-timeout":W().required(t("form.required")).positive().lessThan(65535).integer().default(600),"session-timeout":W().positive().lessThan(65535).integer().default(void 0),credentials:Y().default(void 0),"auth-server":B().required(t("form.required")).default("************"),"auth-secret":B().required(t("form.required")).default("secret"),"auth-port":W().required(t("form.required")).moreThan(1023).lessThan(65535).integer().default(1812),"acct-server":B().default(void 0),"acct-secret":B().default(void 0),"acct-port":W().moreThan(1023).lessThan(65535).integer().default(void 0),"acct-interval":W().positive().lessThan(65535).integer().default(void 0),"uam-server":B().required(t("form.required")).default("https://YOUR-LOGIN-ADDRESS.YOURS"),"uam-secret":B().required(t("form.required")).default("secret"),"uam-port":W().required(t("form.required")).moreThan(1023).lessThan(65535).integer().default(3990),ssid:B().default(void 0),"mac-format":B().required(t("form.required")).default("aabbccddeeff"),nasid:B().required(t("form.required")).default("TestLab"),nasmac:B().default(void 0)}).default({});return a?s:s.nullable().default(void 0)},qr=(t,a=!1)=>{const s=H().shape({name:B().test("ssid-name-test",t("form.min_max_string",{min:1,max:32}),(n,{from:i})=>{var r,l;return(l=(r=i[0])==null?void 0:r.value)!=null&&l.__variableBlock?!0:n.length>=1&&n.length<=32}).default(""),purpose:B().default(void 0),"ssid-schedule-enable":W().integer().default(0),"wifi-bands":Y().of(B()).required(t("form.required")).min(1,t("form.required")).test("ssid-bands-test",t("configurations.wifi_bands_max"),(n,{from:i})=>{var l,d;const r={};for(const f of((d=(l=i[2])==null?void 0:l.value)==null?void 0:d.configuration)??[])for(const c of f.ssids??[])for(const x of c["wifi-bands"]??[])if(r[x]||(r[x]=0),r[x]+=1,r[x]>16&&n.includes(x))return!1;return!0}).default(["2G","5G"]),"bss-mode":B().required(t("form.required")).default("ap"),"hidden-ssid":z().required(t("form.required")).default(!1),"isolate-clients":z().required(t("form.required")).default(!1),"power-save":z().default(void 0),"broadcast-time":z().default(void 0),"unicast-conversion":z().default(!1),services:Y().of(B()).default([]),"maximum-clients":W().required(t("form.required")).moreThan(0).lessThan(65535).integer().default(64),"proxy-arp":z().default(void 0),"disassoc-low-ack":z().default(void 0),"fils-discovery-interval":W().integer().moreThan(0).lessThan(21).default(20),"vendor-elements":B(),encryption:Dr(t,a),"rate-limit":Ur(t,a),rrm:Br(t,a),captive:Lr(t,a),"access-control-list":Nr(t,a),roaming:Or(t),"pass-point":_r(),"dtim-period":W().moreThan(0).lessThan(256).integer().default(2),"tip-information-element":z().default(!0)});return a?s:s.nullable().default(void 0)},ua=["psk","psk2","psk2-radius","psk-mixed","wpa","wpa2","wpa-mixed","sae","sae-mixed","wpa3","wpa3-192","wpa3-mixed","owe","owe-transition"],ha=["psk2-radius","wpa","wpa2","wpa-mixed","wpa3","wpa3-192","wpa3-mixed"],zr={1:{modalComponent:Ra,navigateUrl:"/wireless/profile/SSIDRadius"}},Vr=({label:t,formName:a,type:s,siteId:n,edit:i,title:r,onProfileChange:l,proto:d})=>{const{t:f}=K(),[c,x]=o.useState(!1),[g,h]=o.useState([]),j=k.useFormInstance(),A=xt(),[u,p]=o.useState(!1),v=zr[s],b=v.modalComponent,m=async(C=!1)=>{try{const w=[];d==="psk2-radius"&&w.push({field:"type",value:"External"});const E=await Be(s,n,1,1e3,[],[{field:"id",order:"asc"}],w);if((E==null?void 0:E.status)===200){if(h(E.info||[]),C)if(i)p(!0);else{const T=E.info[E.info.length-1];T&&y(T.variable_id)}}else D.error((E==null?void 0:E.info)||"fetch profile list fail")}catch(w){w instanceof Error?D.error(w.message):D.error("An unknown error occurred")}};o.useEffect(()=>{if(i&&g.length!==0)if(u){const C=g[g.length-1];y(C.variable_id),p(!1)}else{const C=g.find(w=>w.variable_id===i);C&&j.setFieldValue(a,i),l&&l(C)}},[i,g,s]);const I=()=>{x(!0)},S=C=>{x(!1),m(C)},y=C=>{j.setFieldValue(a,C);const w=g.find(E=>E.variable_id===C);l&&l(w)};return o.useEffect(()=>{m(),j.setFieldValue(a,void 0),l&&l(void 0)},[n,d]),e.jsxs(e.Fragment,{children:[e.jsxs(De,{children:[e.jsx(k.Item,{name:a,label:t,rules:[{required:!0,message:f("form.required")}],children:e.jsx(Q,{dropdownRender:C=>e.jsxs(e.Fragment,{children:[C,e.jsxs(Z,{type:"link",icon:e.jsx(Ye,{}),onClick:I,style:{width:"100%",borderTop:"1px solid #E7E7E7"},children:["Create ",r||t," Profile"]})]}),onChange:y,children:g.map(C=>e.jsx(Q.Option,{value:C.variable_id,children:C.name},C.variable_id))})}),e.jsxs(Z,{type:"text",style:{backgroundColor:"#fff"},onClick:()=>A(v.navigateUrl+"#"+n),children:["Manage ",r||t," Profile"]})]}),e.jsx(b,{onClose:S,siteId:n,...d==="psk2-radius"?{disableMode:!0}:{},...s!==4?{open:c}:{visible:c}},Date.now())]})},Qr={1:{modalComponent:Ra,navigateUrl:"/wireless/profile/SSIDRadius"},2:{modalComponent:Zs,navigateUrl:"/wireless/profile/MPSKUser"},3:{modalComponent:Xs,navigateUrl:"/wireless/profile/CaptivePortal"},4:{modalComponent:Ma,navigateUrl:"/wireless/profile/TimeRange"}},Kt=({label:t,formName:a,switchEnabled:s,onSwitchChange:n,type:i,siteId:r,edit:l,mode:d,title:f,onProfileChange:c,enableName:x,selectName:g})=>{const{t:h}=K(),[j,A]=o.useState(!1),[u,p]=o.useState([]),v=k.useFormInstance(),b=xt(),[m,I]=o.useState(void 0),[S,y]=o.useState(!1),C=Qr[i],w=C.modalComponent,E=async(O=!1)=>{try{let q,_;if(d?(d==="radius"?_="Radius":d==="credentials"?_="Credentials":_="Click",I(_),q=await Be(i,r,1,1e3,[],[{field:"id",order:"asc"}],[{field:"mode",value:_}])):(I(void 0),q=await Be(i,r,1,1e3,[],[{field:"id",order:"asc"}])),(q==null?void 0:q.status)===200){if(p(q.info||[]),O)if(l)y(!0);else{const ie=q.info[q.info.length-1];ie&&R(ie.variable_id)}}else D.error((q==null?void 0:q.info)||"fetch profile list fail")}catch(q){q instanceof Error?D.error(q.message):D.error("An unknown error occurred")}};o.useEffect(()=>{s&&(E(),v.setFieldValue(a,void 0),c&&c(void 0))},[s,d,r]),o.useEffect(()=>{if(l&&u.length!==0)if(s||n(!0),S){const O=u[u.length-1];R(O.variable_id),y(!1)}else{const O=u.find(q=>q.variable_id===l);O&&v.setFieldValue(a,l),c&&c(O)}},[l,u,i]);const T=()=>{A(!0)},L=()=>{A(!1)},N=O=>{A(!1),E(O)},M=O=>{n(O),!O&&v&&(v.setFieldValue(a,void 0),g&&v.setFieldValue(g,void 0))},R=O=>{v.setFieldValue(a,O);const q=u.find(_=>_.variable_id===O);g&&v.setFieldValue(g,q?q.name:O),c&&c(q)};return e.jsxs(e.Fragment,{children:[e.jsx(k.Item,{label:t,style:{marginBottom:10},name:x||void 0,valuePropName:x?void 0:"checked",getValueFromEvent:x?O=>O?1:0:void 0,children:e.jsx(te,{checked:s,onChange:M})}),g&&e.jsx(k.Item,{name:g,style:{display:"none"},children:e.jsx(ae,{type:"hidden"})}),s&&e.jsxs(De,{children:[e.jsx(k.Item,{name:a,label:" ",required:!1,rules:[{required:!0,message:h("form.required")}],children:e.jsx(Q,{dropdownRender:O=>e.jsxs(e.Fragment,{children:[O,e.jsxs(Z,{type:"link",icon:e.jsx(Ye,{}),onClick:T,style:{width:"100%",borderTop:"1px solid #E7E7E7"},children:["Create ",f||t," Profile"]})]}),onChange:R,children:u.map(O=>e.jsx(Q.Option,{value:O.variable_id,children:O.name},O.variable_id))})}),e.jsxs(Z,{type:"text",style:{backgroundColor:"#fff"},onClick:()=>b(C.navigateUrl+"#"+r),children:["Manage ",f||t," Profile"]})]}),i!==2?e.jsx(w,{onClose:N,siteId:r,...i!==4?{open:j}:{visible:j},...m?{parameterMode:m}:{}},Date.now()):e.jsx(Ie,{title:e.jsxs("div",{children:[`Create ${f||t} Profile`,e.jsx(oe,{style:{marginTop:8,marginBottom:0}})]}),open:j,onCancel:L,footer:null,destroyOnClose:!0,className:"ampcon-max-modal",children:w&&e.jsx(w,{onClose:N,siteId:r,...m?{parameterMode:m}:{}},Date.now())})]})},rt={Enterprise:[{value:"wpa",label:"WPA-Enterprise"},{value:"wpa2",label:"WPA2-Enterprise EAP-TLS"},{value:"wpa-mixed",label:"WPA-Enterprise-Mixed"},{value:"wpa3",label:"WPA3-Enterprise EAP-TLS"},{value:"wpa3-192",label:"WPA3-192-Enterprise EAP-TLS"},{value:"wpa3-mixed",label:"WPA3-Enterprise-Mixed"}],Personal:[{value:"psk",label:"WPA-PSK"},{value:"psk2",label:"WPA2-PSK"},{value:"psk2-radius",label:"PSK2-RADIUS"},{value:"psk-mixed",label:"WPA-PSK/WPA2-PSK Personal Mixed"},{value:"sae",label:"WPA3-SAE"},{value:"sae-mixed",label:"WPA2/WPA3 Transitional"}],Open:[{value:"none",label:"None"},{value:"owe",label:"OWE"},{value:"owe-transition",label:"OWE-Transition"}]},Gr=["psk","psk2","psk-mixed"],fa=["psk","psk2","psk-mixed","sae","sae-mixed"],Hr=({resource:t,siteId:a})=>{var I;const{t:s}=K(),[n,i]=o.useState(!!(t!=null&&t["multi-psk"])),r=k.useFormInstance(),l=k.useWatch("wifi-bands",r),d=((I=t==null?void 0:t.encryption)==null?void 0:I.proto)||"psk2";function f(S){for(const y of Object.keys(rt))if(rt[y].some(C=>C.value===S))return y;return"Personal"}const c=k.useWatch(["encryption","proto"],r)||d,[x,g]=o.useState(f(c)),h=G.useRef(!0);G.useEffect(()=>{var y;if(h.current){h.current=!1;return}const S=(y=rt[x][0])==null?void 0:y.value;S&&r.setFieldsValue({encryption:{...r.getFieldValue("encryption"),proto:S}})},[x]);const[j,A]=o.useState(fa.includes(c)),[u,p]=o.useState(ua.includes(c)),[v,b]=o.useState(ha.includes(c));G.useEffect(()=>{A(fa.includes(c)),p(ua.includes(c)),b(ha.includes(c));const S=f(c);S!==x&&g(S);const y=r.getFieldValue("encryption")||{};r.setFieldsValue({encryption:{...y,ieee80211w:"required"}})},[c]),G.useEffect(()=>{l&&r.validateFields([["encryption","proto"]])},[l]);function m(S,y){let C=S.replace("#","");C.length===8&&(C=C.slice(0,6)),C.length===3&&(C=C.split("").map(N=>N+N).join(""));const w=parseInt(C,16),E=w>>16&255,T=w>>8&255,L=w&255;return`rgba(${E},${T},${L},${y})`}return e.jsxs(e.Fragment,{children:[e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{label:"Security Level",children:e.jsx("div",{style:{width:"100%",display:"flex"},children:e.jsx("div",{style:{display:"flex",gap:2},children:[{key:"Enterprise",color:"#2BC174FF"},{key:"Personal",color:"#F8961EFF"},{key:"Open",color:"#F53F3FFF"}].map((S,y,C)=>e.jsxs("div",{onClick:()=>g(S.key),style:{cursor:"pointer",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},children:[e.jsx("div",{style:{width:90,height:8,background:S.color,borderTopLeftRadius:y===0?24:0,borderBottomLeftRadius:y===0?24:0,borderTopRightRadius:y===C.length-1?24:0,borderBottomRightRadius:y===C.length-1?24:0,position:"relative",display:"flex",alignItems:"center",justifyContent:"center",marginBottom:4},children:x===S.key&&e.jsx("span",{style:{position:"absolute",right:"40%",top:"50%",transform:"translateY(-50%)",width:12,height:12,borderRadius:"50%",border:"3px solid #fff",background:S.color,boxShadow:`0px 0px 6px 0px ${m(S.color,.3)}`,display:"inline-block"}})}),e.jsx("div",{style:{color:"#333",fontSize:12,fontWeight:500,marginTop:2},children:S.key})]},S.key))})})})})}),e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["encryption","proto"],label:"Protocol",tooltip:J("interface.ssid.encryption.proto"),rules:[{required:!0,message:s("form.required")},{validator:(S,y)=>{const C=r.getFieldValue("wifi-bands");return y==="owe-transition"&&C&&C.includes("6G")?Promise.reject(new Error(s("form.invalid_proto_6g"))):Promise.resolve()}}],children:e.jsx(Q,{children:rt[x].map(S=>e.jsx(Q.Option,{value:S.value,children:S.label},S.value))})})})}),e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:u&&e.jsx(k.Item,{name:["encryption","ieee80211w"],label:"ieee80211w",tooltip:J("interface.ssid.encryption.ieee80211w"),rules:[{required:!0,message:s("form.required")},{validator:(S,y)=>(c==="owe"||c==="owe-transition")&&y==="disabled"?Promise.reject(new Error(s("form.invalid_ieee"))):Promise.resolve()},{validator:(S,y)=>(c==="wpa3"||c==="wpa3-192"||c==="wpa3-mixed"||c==="sae")&&y!=="required"?Promise.reject(new Error(s("form.invalid_ieee_required"))):Promise.resolve()}],children:e.jsxs(Q,{placeholder:"ieee80211w",children:[e.jsx(Q.Option,{value:"disabled",children:"Disabled"}),e.jsx(Q.Option,{value:"optional",children:"Optional"}),e.jsx(Q.Option,{value:"required",children:"Required"})]})})}),e.jsx(F,{span:12,children:j&&e.jsx(k.Item,{name:["encryption","key"],label:"Key",tooltip:J("interface.ssid.encryption.key"),rules:[{required:!0,message:s("form.min_max_string",{min:8,max:63})},{min:8,max:63,message:s("form.min_max_string",{min:8,max:63})}],children:e.jsx(ae.Password,{placeholder:"key"})})})]}),v&&e.jsx(U,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx(Vr,{label:"Radius",formName:["radius"],type:1,siteId:a,proto:c,edit:t==null?void 0:t.radius})})}),Gr.includes(c)&&e.jsx(U,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx(Kt,{label:"MPSK",formName:["multi-psk"],switchEnabled:n,onSwitchChange:i,type:2,siteId:a,edit:t==null?void 0:t["multi-psk"]})})})]})},ma={username:"",password:""},Kr=({value:t=[],onChange:a})=>{const{t:s}=K(),[n,i]=o.useState(t),[r,l]=o.useState(!1),[d]=k.useForm(),[f,c]=o.useState(1),[x,g]=o.useState(10),h=o.useCallback(p=>{c(p.current),g(p.pageSize)},[]),j=()=>{l(!0),d.setFieldsValue(ma)},A=p=>{const v=n.filter(b=>b.username!==p.username||b.password!==p.password);i(v),a&&a(v)},u=[{title:"Username",dataIndex:"username",key:"username"},{title:"Password",dataIndex:"password",key:"password"},{title:"Operation",key:"action",render:(p,v)=>e.jsx(Z,{type:"text",onClick:()=>A(v),children:"Delete"})}];return e.jsxs("div",{children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:24},children:e.jsx(Z,{type:"primary",icon:e.jsx(Fe,{component:bt}),onClick:j,children:"Add Credentials"})}),e.jsx(et,{columns:u,dataSource:n,pagination:{current:f,pageSize:x,showTotal:p=>`Total ${p} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["5","10","20","50"],onChange:h},rowKey:(p,v)=>typeof v=="number"?v.toString():"",size:"middle",bordered:!0}),e.jsx(Qt,{open:r,title:"Add Credentials",onCancel:()=>l(!1),onFinish:p=>{const v=[p,...n];i(v),a&&a(v),l(!1),d.resetFields()},initialValues:ma,form:d,modalClass:"ampcon-middle-modal",children:e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:24,children:e.jsx(k.Item,{label:"Username",name:"username",rules:[{required:!0,message:"Username is required"},{validator:(p,v)=>v&&!/^[a-zA-Z][a-zA-Z0-9]*$/.test(v)?Promise.reject(new Error("Username must start with a letter and contain only letters and numbers")):Promise.resolve()},{validator:(p,v)=>v&&n.some(b=>b.username===v)?Promise.reject(new Error("Username already exists")):Promise.resolve()}],children:e.jsx(ae,{})})}),e.jsx(F,{span:24,children:e.jsx(k.Item,{label:"Password",name:"password",rules:[{required:!0,message:s("form.required")}],children:e.jsx(ae.Password,{})})})]})})]})},$r=[{value:"aabbccddeeff",label:"aabbccddeeff"},{value:"aa-bb-cc-dd-ee-ff",label:"aa-bb-cc-dd-ee-ff"},{value:"aa:bb:cc:dd:ee:ff",label:"aa:bb:cc:dd:ee:ff"},{value:"AABBCCDDEEFF",label:"AABBCCDDEEFF"},{value:"AA:BB:CC:DD:EE:FF",label:"AA:BB:CC:DD:EE:FF"},{value:"AA-BB-CC-DD-EE-FF",label:"AA-BB-CC-DD-EE-FF"}],Wr={"walled-garden-fqdn":void 0,"walled-garden-ipaddr":void 0,"web-root":void 0,"idle-timeout":600,"session-timeout":void 0,credentials:void 0,"auth-server":"************","auth-secret":"secret","auth-port":1812,"acct-server":void 0,"acct-secret":void 0,"acct-port":1813,"acct-interval":60,"uam-server":"https://YOUR-LOGIN-ADDRESS.YOURS","uam-secret":"secret","uam-port":3990,ssid:void 0,"mac-format":"aabbccddeeff",nasid:"TestLab",nasmac:void 0},Jr=({resource:t,siteId:a,onAuthModeChange:s})=>{var h,j,A;const{t:n}=K(),i=k.useFormInstance(),r=o.useRef(!0),[l,d]=o.useState(((h=t==null?void 0:t.captive)==null?void 0:h["auth-mode"])||"off"),[f,c]=o.useState(!!((j=t==null?void 0:t.captive)!=null&&j["web-root"])),[x,g]=o.useState(((A=t==null?void 0:t.captive)==null?void 0:A["web-root"])||void 0);return o.useEffect(()=>{s&&s(l),!(r.current&&(r.current=!1,t))&&(i.setFieldsValue({captive:Wr}),g(void 0),c(!1))},[l]),e.jsxs(e.Fragment,{children:[e.jsx("h3",{className:"header2",children:"Captive Portal"}),e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","auth-mode"],label:"Auth Mode",rules:[{required:!0,message:n("form.required")}],children:e.jsxs(Q,{value:l,onChange:u=>d(u),children:[e.jsx(Q.Option,{value:"off",children:"Off"}),e.jsx(Q.Option,{value:"click-to-continue",children:"Click"}),e.jsx(Q.Option,{value:"radius",children:"Radius"}),e.jsx(Q.Option,{value:"credentials",children:"Credentials"}),e.jsx(Q.Option,{value:"uam",children:"UAM"})]})})})}),l!=="off"&&e.jsxs(e.Fragment,{children:[e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","walled-garden-fqdn"],label:"Walled Garden FQDN",children:e.jsx(Q,{mode:"tags",tokenSeparators:[","]})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","walled-garden-ipaddr"],label:"Walled Garden IP Address",rules:[{validator:(u,p)=>Ys(p)}],children:e.jsx(Q,{mode:"tags",tokenSeparators:[","]})})})]}),e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","idle-timeout"],label:"Idle Timeout",rules:[{required:!0,message:n("form.required")},{type:"number",max:65534,message:"idle-timeout must be less than 65535"},{type:"number",min:1,message:"idle-timeout must be greater than 0"}],children:e.jsx(ne,{min:0})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","session-timeout"],label:"Session Timeout",rules:[{type:"number",max:65534,message:"session-timeout must be less than 65535"},{type:"number",min:1,message:"session-timeout must be greater than 0"}],children:e.jsx(ne,{min:0})})})]}),l==="credentials"&&e.jsx(U,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx(k.Item,{label:"Credentials",name:["captive","credentials"],rules:[{required:!0,message:n("form.required")}],children:e.jsx(Kr,{})})})}),l==="uam"&&e.jsxs(e.Fragment,{children:[e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","uam-server"],label:"UAM Server",rules:[{required:!0,message:n("form.required")}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","uam-secret"],label:"UAM Secret",rules:[{required:!0,message:n("form.required")}],children:e.jsx(ae.Password,{})})})]}),e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","uam-port"],label:"UAM Port",rules:[{required:!0,message:n("form.required")},{type:"number",min:1024,message:"captive.uam-port must be greater than 1023"},{type:"number",max:65534,message:"captive.uam-port must be less than 65535"}],children:e.jsx(ne,{})})})}),e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","nasid"],label:"NAS ID",rules:[{required:!0,message:n("form.required")}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","nasmac"],label:"NAS MAC",children:e.jsx(ae,{})})})]}),e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","mac-format"],label:"MAC Format",rules:[{required:!0,message:n("form.required")}],children:e.jsx(Q,{options:$r})})})}),e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","ssid"],label:"SSID",children:e.jsx(ae,{})})})})]}),(l==="radius"||l==="uam")&&e.jsxs(e.Fragment,{children:[e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","auth-server"],label:"Auth Server",rules:[{required:!0,message:n("form.required")},{validator:(u,p)=>ht(p)}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","auth-secret"],label:"Auth Secret",rules:[{required:!0,message:n("form.required")}],children:e.jsx(ae.Password,{})})})]}),e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","auth-port"],label:"Auth Port",rules:[{required:!0,message:n("form.required")},{type:"number",min:1024,message:"captive.auth-port must be greater than 1023"},{type:"number",max:65534,message:"captive.auth-port must be less than 65535"}],children:e.jsx(ne,{})})})}),e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","acct-server"],label:"Acct Server",rules:[{validator:(u,p)=>ht(p)}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","acct-secret"],label:"Acct Secret",children:e.jsx(ae.Password,{})})})]}),e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","acct-port"],label:"Acct Port",rules:[{type:"number",min:1024,message:"captive.acct-port must be greater than 1023"},{type:"number",max:65534,message:"captive.acct-port must be less than 65535"}],children:e.jsx(ne,{})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["captive","acct-interval"],label:"Acct Interval",rules:[{type:"number",min:1,message:"acct-interval must be a positive number"},{type:"number",max:65534,message:"acct-interval must be less than 65535"}],children:e.jsx(ne,{})})})]})]}),l!=="uam"&&e.jsx(U,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx(Kt,{label:"Web Root",formName:["captive","web-root"],switchEnabled:f,onSwitchChange:c,type:3,siteId:a,mode:l,edit:x})})})]})]})},Xr=({resource:t,siteId:a})=>{const{t:s}=K(),n=t||{},[i,r]=o.useState(!!n["rate-limit"]),[l,d]=o.useState(!!n["access-control-list"]),[f,c]=o.useState(n.roaming&&n.roaming["message-exchange"]?"custom":n.roaming===!0?"on":"off"),x=k.useFormInstance(),g=k.useWatch(["encryption","proto"],x),h=["none","owe","owe-transition"];G.useEffect(()=>{if(g&&h.includes(g)){const p=x.getFieldValue("roaming");p!=null&&p!==!1&&x.setFieldsValue({roaming:void 0}),f!=="off"&&c("off")}},[g]);const j=(u,p)=>{const v=p.getFieldValue("services")||[];u?v.includes("wifi-steering")||p.setFieldValue("services",[...v,"wifi-steering"]):p.setFieldValue("services",v.filter(b=>b!=="wifi-steering"))},A=u=>{const p=u.getFieldValue("services")||[];return e.jsx(te,{checked:p.includes("wifi-steering"),onChange:v=>j(v,u)})};return e.jsx(k.Item,{noStyle:!0,shouldUpdate:!0,children:u=>{const p=v=>{const b=u.getFieldValue("services")||[];v!=="off"?b.includes("captive")||u.setFieldValue("services",[...b,"captive"]):b.includes("captive")&&u.setFieldValue("services",b.filter(m=>m!=="captive"))};return e.jsxs(e.Fragment,{children:[e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["hidden-ssid"],label:"Hidden SSID",tooltip:J("interface.ssid.hidden-ssid"),valuePropName:"checked",children:e.jsx(te,{})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{label:"WIFI Steering",valuePropName:"checked",children:A(u)})})]}),e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["services"],label:"Services",style:{display:"none"},children:e.jsx(Q,{mode:"multiple",options:[]})})}),e.jsx(F,{span:12})]}),e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["maximum-clients"],label:"Maximum Clients",tooltip:J("interface.ssid.maximum-clients"),rules:[{required:!0,message:s("form.required")},{type:"number",min:1,max:65535,message:"maximum-clients must be 1 ~ 65535"}],children:e.jsx(ne,{})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["fils-discovery-interval"],label:"Fils-Discovery-Interval",tooltip:J("interface.ssid.fils-discovery-interval"),rules:[{required:!0,message:s("form.required")},{type:"number",min:1,max:20,message:"fils-discovery-interval must be 1 ~ 20"}],children:e.jsx(ne,{})})})]}),e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["dtim-period"],label:"Dtim-Period",rules:[{required:!0,message:s("form.required")},{type:"number",min:1,max:255,message:"dtim-period must be 1 ~ 255"}],children:e.jsx(ne,{})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["isolate-clients"],label:"Isolate Clients",tooltip:J("interface.ssid.isolate-clients"),valuePropName:"checked",children:e.jsx(te,{})})})]}),e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["power-save"],label:"Power Save",tooltip:J("interface.ssid.power-save"),valuePropName:"checked",children:e.jsx(te,{})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["broadcast-time"],label:"Broadcast Time",tooltip:J("interface.ssid.broadcast-time"),valuePropName:"checked",children:e.jsx(te,{})})})]}),e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["unicast-conversion"],label:"Unicast Conversion",tooltip:J("interface.ssid.unicast-conversion"),valuePropName:"checked",children:e.jsx(te,{})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["proxy-arp"],label:"Proxy ARP",tooltip:J("interface.ssid.proxy-arp"),valuePropName:"checked",children:e.jsx(te,{})})})]}),e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["disassoc-low-ack"],label:"Disassoc Low Ack",valuePropName:"checked",children:e.jsx(te,{})})})}),e.jsx("h3",{className:"header2",children:"Roaming"}),e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["encryption","key-caching"],label:"Key-Caching",tooltip:J("interface.ssid.encryption.key-caching"),valuePropName:"checked",children:e.jsx(te,{defaultChecked:!0})})})}),!g||g&&!h.includes(g)?e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{label:"FT Roaming",children:e.jsxs(Q,{value:f,onChange:v=>{c(v),v==="custom"?(u.getFieldValue("roaming")||{})["message-exchange"]||u.setFieldValue(["roaming","message-exchange"],"ds"):v==="on"&&u.setFieldValue("roaming",!0)},children:[e.jsx(Q.Option,{value:"on",children:"Auto"}),e.jsx(Q.Option,{value:"custom",children:"Custom"}),e.jsx(Q.Option,{value:"off",children:"Off"})]})})}),e.jsx(F,{span:12})]}):null,f==="on"&&e.jsx(k.Item,{name:["roaming"],style:{display:"none"},children:e.jsx(te,{})}),f==="custom"&&e.jsxs(e.Fragment,{children:[e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["roaming","message-exchange"],label:"Message Exchange",tooltip:J("interface.ssid.roaming.message-exchange"),rules:[{required:!0,message:s("form.required")}],children:e.jsxs(Q,{placeholder:"message-exchange",children:[e.jsx(Q.Option,{value:"air",children:"air"}),e.jsx(Q.Option,{value:"ds",children:"ds"})]})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["roaming","generate-psk"],label:"Generate PSK",tooltip:J("interface.ssid.roaming.generate-psk"),valuePropName:"checked",children:e.jsx(te,{})})})]}),e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["roaming","domain-identifier"],label:"Domain Identifier",tooltip:J("interface.ssid.roaming.domain-identifier"),rules:[{validator:(v,b)=>b==null||b===""||/^[0-9A-Fa-f]{4}$/.test(String(b))?Promise.resolve():Promise.reject("domain-identifier must be exactly 4 hexadecimal characters")}],children:e.jsx(ae,{})})})})]}),e.jsxs("div",{style:{display:"none"},children:[e.jsx(k.Item,{name:["rrm","neighbor-reporting"],label:"Neighbor Reporting",tooltip:J("interface.ssid.rrm.neighbor-reporting"),valuePropName:"checked",children:e.jsx(te,{})}),e.jsx(k.Item,{name:["rrm","ftm-responder"],label:"FTM Responder",tooltip:J("interface.ssid.rrm.ftm-responder"),valuePropName:"checked",children:e.jsx(te,{})}),e.jsx(k.Item,{name:["rrm","stationary-ap"],label:"Stationary AP",tooltip:J("interface.ssid.rrm.stationary-ap"),valuePropName:"checked",children:e.jsx(te,{})})]}),e.jsx("h3",{className:"header2",children:"Rate Limit"}),e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{label:"Rate Limit",children:e.jsx(te,{checked:i,onChange:r})})}),e.jsx(F,{span:12})]}),i&&e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["rate-limit","ingress-rate"],label:"Ingress Rate",tooltip:J("interface.ssid.rate-limit.ingress-rate"),rules:[{required:!0,message:s("form.required")},{type:"number",min:0,max:65535,message:"ingress-rate must be less than 65535"}],children:e.jsx(ne,{min:0,addonAfter:"Mb/s",style:{width:280}})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["rate-limit","egress-rate"],label:"Egress Rate",tooltip:J("interface.ssid.rate-limit.egress-rate"),rules:[{required:!0,message:s("form.required")},{type:"number",min:0,max:65535,message:"egress-rate must be less than 65535"}],children:e.jsx(ne,{min:0,addonAfter:"Mb/s",style:{width:280}})})})]}),e.jsx("h3",{className:"header2",children:"Access Control List"}),e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{label:"Access-Control-List",children:e.jsx(te,{checked:l,onChange:d})})}),e.jsx(F,{span:12})]}),l&&e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["access-control-list","mode"],label:"Mode",rules:[{required:!0,message:s("form.required")}],children:e.jsxs(Q,{children:[e.jsx(Q.Option,{value:"allow",children:"Allow"}),e.jsx(Q.Option,{value:"deny",children:"Deny"})]})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:["access-control-list","mac-address"],label:"MAC Address",validateTrigger:["onChange","onBlur"],rules:[{required:!0,message:s("form.required")},{validator:(v,b)=>en(b)}],children:e.jsx(Q,{mode:"tags",tokenSeparators:[","]})})})]}),e.jsx(Jr,{resource:t,siteId:a,onAuthModeChange:p})]})}})},tt="/ampcon/wireless/configure";function $t({siteId:t,pageNum:a,pageSize:s,sortBy:n,sortType:i}){return de({url:`${tt}/dhcp_service_list`,method:"GET",params:{siteId:t,pageNum:a,pageSize:s,sortBy:n,sortType:i}})}function Zr({id:t}){return de({url:`${tt}/dhcp_service`,method:"GET",params:{id:t}})}function Yr({site_id:t,name:a,subnet:s,vlan:n,dhcp_configure:i,description:r}){return de({url:`${tt}/dhcp_service`,method:"POST",data:{site_id:t,name:a,subnet:s,vlan:n,dhcp_configure:i,description:r}})}function el({id:t,name:a,subnet:s,vlan:n,dhcp_configure:i,description:r}){return de({url:`${tt}/dhcp_service`,method:"PUT",data:{id:t,name:a,subnet:s,vlan:n,dhcp_configure:i,description:r}})}function tl({id:t}){return de({url:`${tt}/dhcp_service`,method:"DELETE",data:{id:t}})}const lt=({value:t,onChange:a,...s})=>{const n=t==null||t===""||t==="-"?void 0:parseInt(String(t),10),i=r=>{r===null?a==null||a(""):Number.isInteger(r)&&(a==null||a(r.toString()))};return e.jsx(ne,{value:n,onChange:i,precision:0,...s})},Wt=({resource:t,onClose:a,refresh:s,siteId:n})=>{const{t:i}=K(),[r]=k.useForm(),[l,d]=o.useState(!1),[f,c]=o.useState(!1),[x,g]=o.useState(!1),h=(b,m,I)=>(S,y)=>{if(y==null||y===""||y==="-")return Promise.resolve();const C=Number(y);return Number.isInteger(C)?b===1&&C<b?Promise.reject(`configuration.${I} must be a positive number`):b!==void 0&&b!==1&&C<b?Promise.reject(`configuration.${I} must be greater than ${b}`):m!==void 0&&C>m?Promise.reject(`configuration.${I} must be less than ${m}`):Promise.resolve():Promise.reject(`configuration.${I} must be an integer`)},j=o.useMemo(()=>({subnet:"***********/24",gateway:"***********","send-hostname":!0,"lease-first":1,"lease-count":128,"lease-time":"6h"}),[]),A=o.useMemo(()=>({"ipv6-subnet":"","ipv6-gateway":"","ipv6-prefix-size":64,"ipv6-dhcp-mode":"hybrid","ipv6-announce-dns":void 0,"ipv6-filter-prefix":"::/0"}),[]);o.useEffect(()=>{var b,m;if(t){const I={name:t.name||"",description:t.description||"",vlan_id:(m=(b=t.dhcp_configure)==null?void 0:b.vlan)==null?void 0:m.id,subnet:t.subnet||""};let S={};if(t.dhcp_configure&&typeof t.dhcp_configure=="string")try{S=JSON.parse(t.dhcp_configure)}catch{D.error("Failed to parse DHCP configure"),S={}}else S=t.dhcp_configure||{};const y=S.ipv4||{},C=y.dhcp||{},w=S.ipv6||{},E=w.dhcpv6||{},T=!!w.addressing;c(T),g(!!E.mode);const L={...I,ipv6:!!w.addressing,dhcp_v6:!!E.mode,gateway:y.gateway||"","send-hostname":y["send-hostname"]||!0,"lease-first":C["lease-first"],"lease-count":C["lease-count"],"lease-time":C["lease-time"],"ipv6-subnet":w.subnet||"","ipv6-gateway":w.gateway||"","ipv6-prefix-size":w["prefix-size"],"ipv6-dhcp-mode":E.mode||"hybrid","ipv6-announce-dns":E["announce-dns"]||void 0,"ipv6-filter-prefix":E["filter-prefix"]||""};r.setFieldsValue(L)}},[t,r]);const u=async b=>{d(!0);try{const{name:m,description:I,vlan_id:S,subnet:y,ipv6:C,...w}=b;let E;["-","",void 0,null].includes(S)?E=null:E=Number(S);const L={role:"downstream",ipv4:{addressing:"static",dhcp:{"lease-count":Number(w["lease-count"]),"lease-first":Number(w["lease-first"]),"lease-time":w["lease-time"]},gateway:w.gateway,"send-hostname":!0,subnet:y},...E!==null&&{vlan:{id:E}},name:m};if(f){const R=w["ipv6-prefix-size"],O=R===""||R===void 0||R===null?void 0:Number(R);L.ipv6={addressing:"static",subnet:w["ipv6-subnet"],gateway:w["ipv6-gateway"],...O!==void 0&&{"prefix-size":O}},x&&(L.ipv6.dhcpv6={mode:w["ipv6-dhcp-mode"],"announce-dns":w["ipv6-announce-dns"],"filter-prefix":w["ipv6-filter-prefix"]})}const N={site_id:n,name:m,subnet:y,dhcp_configure:zt(L),description:I||"",vlan:E??"-"};let M;if(t!=null&&t.id?M=await el({...N,id:t.id}):M=await Yr(N),(M==null?void 0:M.status)!==200){D.error((M==null?void 0:M.info)||i("crud.error_create_obj",{obj:"DHCP Service"}));return}D.success(t!=null&&t.id?i("crud.success_update_obj",{obj:"DHCP Service"}):i("crud.success_create_obj",{obj:"DHCP Service"})),s&&s(),a&&a(!0)}catch{D.error(i("crud.error_create_obj",{obj:"DHCP Service"}))}finally{d(!1)}},p=b=>{c(b),b?(g(!0),r.setFieldsValue({ipv6:b,dhcp_v6:!0,"ipv6-subnet":A["ipv6-subnet"],"ipv6-gateway":A["ipv6-gateway"],"ipv6-prefix-size":A["ipv6-prefix-size"],"ipv6-dhcp-mode":A["ipv6-dhcp-mode"],"ipv6-filter-prefix":A["ipv6-filter-prefix"]})):(g(!1),r.setFieldsValue({ipv6:b,dhcp_v6:!1,"ipv6-subnet":void 0,"ipv6-gateway":void 0,"ipv6-prefix-size":void 0,"ipv6-dhcp-mode":void 0,"ipv6-announce-dns":void 0,"ipv6-filter-prefix":void 0}))},v=b=>{g(b),b?r.setFieldsValue({"ipv6-dhcp-mode":A["ipv6-dhcp-mode"],"ipv6-filter-prefix":A["ipv6-filter-prefix"]}):r.setFieldsValue({"ipv6-dhcp-mode":void 0,"ipv6-announce-dns":void 0,"ipv6-filter-prefix":void 0})};return e.jsxs(k,{form:r,name:"networkForm",onFinish:u,initialValues:{name:"",description:"",vlan_id:"",ipv6:!1,...j,...A},className:"wirelessFormCreate",children:[e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"name",label:i("common.name"),rules:[{required:!0,message:i("form.required")},{validator:(b,m)=>m&&m&&new Blob([m]).size>64?Promise.reject("name must be less than 64 characters"):Promise.resolve()}],children:e.jsx(ae,{disabled:!!(t!=null&&t.id)})})})}),e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"description",label:"Description",rules:[{validator:(b,m)=>m&&m&&new Blob([m]).size>128?Promise.reject("description must be less than 128 characters"):Promise.resolve()}],children:e.jsx(Rs,{rows:2})})})}),e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:24,children:e.jsx("h3",{className:"header2",children:"IPv4"})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"subnet",label:"Subnet",tooltip:J("interface.ipv4.subnet"),rules:[{required:!0,message:i("form.required")},{validator:(b,m)=>m==="auto/24"?Promise.resolve():Rt(m)?/^127\.0\.0\.0\/8$/.test(m)?Promise.reject(i("form.invalid_static_ipv4_loopback")):ca(m)?Pr(m)?Promise.resolve():Promise.reject(i("form.invalid_static_ipv4_e")):Promise.reject(i("form.invalid_static_ipv4_d")):Promise.reject(i("form.invalid_ipv4"))}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"gateway",label:"Gateway",tooltip:J("interface.ipv4.gateway"),rules:[{required:!0,message:i("form.required")},{validator:(b,m)=>/^127\.\d+\.\d+\.\d+(\/\d+)?$/.test(m)?Promise.reject(i("form.invalid_static_ipv4_loopback")):ca(m)?Rt(m)?Promise.resolve():Promise.reject(i("form.invalid_ipv4")):Promise.reject(i("form.invalid_static_ipv4_d"))}],children:e.jsx(ae,{})})}),e.jsx(F,{span:24,children:e.jsx("h3",{className:"header2",children:"DHCP V4"})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"lease-first",label:"Lease-first",tooltip:J("interface.ipv4.dhcp.lease-first"),rules:[{required:!0,message:i("form.required")},{validator:h(1,void 0,"ipv4.dhcp.lease-first")}],children:e.jsx(lt,{})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"lease-count",label:"Lease-count",tooltip:J("interface.ipv4.dhcp.lease-count"),rules:[{required:!0,message:i("form.required")},{validator:h(1,void 0,"ipv4.dhcp.lease-count")}],children:e.jsx(lt,{})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"lease-time",label:"Lease-time",tooltip:J("interface.ipv4.dhcp.lease-time"),rules:[{validator:(b,m)=>Fr(m)?Promise.resolve():Promise.reject(i("form.invalid_lease_time"))}],children:e.jsx(ae,{})})})]}),e.jsx(U,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx(k.Item,{name:"ipv6",label:"IPv6",valuePropName:"checked",children:e.jsx(te,{onChange:p})})})}),f&&e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"ipv6-subnet",label:"Subnet",tooltip:J("interface.ipv6.subnet"),rules:[{required:!0,message:i("form.required")}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"ipv6-gateway",label:"Gateway",tooltip:J("interface.ipv6.gateway"),rules:[{required:!0,message:i("form.required")}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"ipv6-prefix-size",label:"Prefix-size",tooltip:J("interface.ipv6.prefix-size"),rules:[{validator:h(0,64,"ipv6.prefix-size")}],children:e.jsx(lt,{})})}),e.jsx(F,{span:24,children:e.jsx(k.Item,{name:"dhcp_v6",label:"DHCP v6",valuePropName:"checked",children:e.jsx(te,{onChange:v,checked:x})})}),x&&e.jsxs(e.Fragment,{children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"ipv6-dhcp-mode",label:"Mode",tooltip:J("interface.ipv6.dhcpv6.mode"),children:e.jsx(Q,{options:[{value:"hybrid",label:"hybrid"},{value:"stateless",label:"stateless"},{value:"stateful",label:"stateful"}]})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"ipv6-announce-dns",label:"Announce-dns",tooltip:J("interface.ipv6.dhcpv6.announce-dns"),children:e.jsx(Q,{mode:"tags",allowClear:!0,style:{width:"100%"},tokenSeparators:[","," "],notFoundContent:"Type the value you need to create..."})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"ipv6-filter-prefix",label:"Filter-prefix",tooltip:J("interface.ipv6.dhcpv6.filter-prefix"),children:e.jsx(ae,{})})})]})]}),e.jsx(U,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx("h3",{className:"header2",children:"VLAN"})})}),e.jsx(U,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx(k.Item,{name:"vlan_id",label:"VLAN ID",rules:[{validator:h(1,4050,"vlan.id")}],children:e.jsx(lt,{})})})}),e.jsx(oe,{}),e.jsx(U,{children:e.jsxs(F,{span:24,style:{textAlign:"right"},children:[e.jsx(Z,{onClick:()=>{r.resetFields(),a&&a()},disabled:l,style:{marginRight:8},children:"Cancel"}),e.jsx(Z,{type:"primary",htmlType:"submit",disabled:l,loading:l,children:"Apply"})]})})]})},pa=async t=>{try{const a=await $t({siteId:t,pageNum:1,pageSize:100});return(a==null?void 0:a.status)===200&&Array.isArray(a.info)?a.info.map(s=>({label:s.name,value:s.name})):[]}catch{return[]}},al=({isDisabled:t=!1,resource:a,onClose:s,refresh:n,siteId:i,modalOpen:r})=>{var L;const{t:l}=K(),[d]=k.useForm(),[f,c]=o.useState((a==null?void 0:a.network_type)===2),[x,g]=o.useState((a==null?void 0:a.network_type)===3),[h,j]=o.useState([]),[A,u]=o.useState(!1),[p,v]=o.useState(!1),b=async N=>{v(!1),u(!0);const M=await pa(i);if(j(M),u(!1),N&&M&&M.length>0){const R=M[M.length-1];d.setFieldValue(["dhcp_name"],R==null?void 0:R.value)}},m=qr(l,!0).cast();let I,S={};a&&a.ssid_configure?(S={...a.ssid_configure},S.labels_name=a.labels_name,S.radius&&(S.radius=Ge(S.radius)),S["time-range-index"]&&(S["time-range-index"]=Ge(S["time-range-index"])),S["multi-psk"]&&(S["multi-psk"]=Ge(S["multi-psk"])),(L=S.captive)!=null&&L["web-root"]&&(S.captive["web-root"]=Ge(S.captive["web-root"])),a.network_type===2?S.vlan=parseInt(a.vlan_or_dhcp_name):a.network_type===3&&(S.dhcp_name=a.vlan_or_dhcp_name),I=S):I=m;const[y,C]=o.useState(!!(I!=null&&I["time-range-index"])),[w,E]=o.useState([]);G.useEffect(()=>{ln({siteId:i}).then(N=>{(N==null?void 0:N.status)===200&&Array.isArray(N.info)?E(N.info.map(M=>({label:M.name,value:M.name}))):E([])}).catch(()=>E([]))},[i]);const T=N=>{var me,$;const M=zt(N);delete M.labels_name,delete M.network;const R=M.name,O=(me=Ha.find(V=>V.value===M.encryption.proto))==null?void 0:me.label;let q="",_=1;f&&(q=M.vlan,_=2,delete M.vlan),x&&(q=M.dhcp_name,_=3,delete M.dhcp_name),M.radius&&(M.radius=He(M.radius)),M["time-range-index"]&&(M["time-range-index"]=He(M["time-range-index"])),M["time-range-name"]||delete M["time-range-name"],M["multi-psk"]&&(M["multi-psk"]=He(M["multi-psk"])),($=M.captive)!=null&&$["web-root"]&&(M.captive["web-root"]=He(M.captive["web-root"]));const ie=(M["wifi-bands"]||[]).join(","),ce=JSON.stringify(M);a&&a.id?hr({id:a.id,name:R,security:O,radio:ie,vlan_or_dhcp_name:q,network_type:_,ssid_configure:ce,labels_name:N.labels_name||[]}).then(V=>{if((V==null?void 0:V.status)!==200){D.error(V==null?void 0:V.info);return}D.success(V.info),n&&n(),s&&s()}).catch(()=>D.error("Failed to create Configure")):ur({site_id:i,name:R,security:O,radio:ie,vlan_or_dhcp_name:q,network_type:_,ssid_configure:ce,labels_name:N.labels_name||[]}).then(V=>{if((V==null?void 0:V.status)!==200){D.error(V==null?void 0:V.info);return}D.success(V.info),n&&n(),s&&s()}).catch(()=>D.error("Failed to create Configure"))};return G.useEffect(()=>{x&&(u(!0),pa(i).then(N=>{j(N),u(!1)}))},[x,i]),e.jsxs(Qt,{open:r,title:a?"Edit SSID":"Create New SSID",onCancel:s,onFinish:T,initialValues:I,form:d,children:[e.jsxs(U,{gutter:24,children:[e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"name",label:"SSID",tooltip:J("interface.ssid.name"),rules:[{required:!0,message:l("form.min_max_string",{min:1,max:32})},{min:1,max:32,message:l("form.min_max_string",{min:1,max:32})}],children:e.jsx(ae,{})})}),e.jsx(F,{span:12,style:{display:"none"},children:e.jsx(k.Item,{name:"bss-mode",label:"Bss Mode",tooltip:J("interface.ssid.bss-mode"),rules:[{required:!0,message:l("form.required")}],children:e.jsxs(Q,{placeholder:"bss-mode",children:[e.jsx(Q.Option,{value:"ap",children:"ap"}),e.jsx(Q.Option,{value:"sta",children:"sta"}),e.jsx(Q.Option,{value:"mesh",children:"mesh"}),e.jsx(Q.Option,{value:"wds-ap",children:"wds-ap"}),e.jsx(Q.Option,{value:"wds-sta",children:"wds-sta"})]})})}),e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"wifi-bands",label:"WIFI Bands",tooltip:J("interface.ssid.wifi-bands"),rules:[{required:!0,message:l("form.required")}],children:e.jsx(Nt.Group,{options:["2G","5G","6G"],value:["2G","5G","6G"]})})})]}),e.jsx(U,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx(Kt,{label:"Schedule switch-on",title:"Time Range",formName:["time-range-index"],enableName:["ssid-schedule-enable"],selectName:["time-range-name"],switchEnabled:y,onSwitchChange:C,type:4,siteId:i,edit:S==null?void 0:S["time-range-index"]})})}),e.jsx(U,{gutter:24,children:e.jsx(F,{span:24,children:e.jsx(k.Item,{name:"labels_name",label:"AP Label",children:e.jsx(Q,{mode:"multiple",options:w,allowClear:!0})})})}),!x&&e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{label:"VLAN",children:e.jsx(te,{checked:f,onChange:N=>{c(N),N&&g(!1)},style:{marginRight:8}})})})}),f&&e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"vlan",label:"VLAN",rules:[{required:!0,message:l("form.required")},{type:"number",max:4094,message:"vlan must be less than 4095"},{type:"number",min:0,message:"vlan must be greater than -1"}],children:e.jsx(ne,{})})})}),!f&&e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{label:"AP Assign IP",tooltip:J("The AP acts as a DHCP server to assign IP addresses to clients"),children:e.jsx(te,{checked:x,onChange:N=>{g(N),N&&c(!1)},style:{marginRight:8}})})})}),x&&e.jsxs(e.Fragment,{children:[e.jsx(U,{gutter:24,children:e.jsx(F,{span:12,children:e.jsx(k.Item,{name:"dhcp_name",label:"DHCP Service Name",rules:[{required:!0,message:l("form.required")}],children:e.jsx(Q,{options:h,loading:A,dropdownRender:N=>e.jsxs(e.Fragment,{children:[N,e.jsx(Z,{type:"link",icon:e.jsx(Ye,{}),style:{width:"100%",borderTop:"1px solid #E7E7E7"},onClick:()=>v(!0),children:"Create New DHCP Service"})]})})})})}),p&&e.jsx(Ze,{isModalOpen:p,title:"Create DHCP Service",onCancel:()=>v(!1),modalClass:"ampcon-max-modal",childItems:e.jsx(Wt,{resource:void 0,onClose:b,siteId:i})})]}),e.jsx("h3",{className:"header2",children:"Authentication"}),e.jsx(Hr,{resource:S,siteId:i}),e.jsx("div",{style:{borderTop:"1px solid #eee",marginTop:22}}),e.jsx(_e,{bordered:!1,defaultActiveKey:[],expandIconPosition:"end",style:{background:"#FFFFFF",marginBottom:20},children:e.jsx(_e.Panel,{header:e.jsx("h3",{style:{fontSize:16,margin:0},children:"Advanced Settings"}),forceRender:!0,children:e.jsx(Xr,{resource:S,siteId:i})},"advanced")})]})},sl=({siteId:t=0})=>{if(window.location.hash){const y=window.location.hash.replace("#","");/^\d+$/.test(y)&&(t=parseInt(y,10))}const[a,s]=o.useState([]),[n,i]=o.useState(!1),[r,l]=o.useState(!1),[d,f]=o.useState(null),[c,x]=o.useState({current:1,pageSize:10,total:0}),[g,h]=o.useState({}),j=(y=1,C=10,w=g)=>{i(!0);const E=w.field?[{field:w.field,order:w.order}]:[];pr(t,y,C,[],E).then(T=>{if((T==null?void 0:T.status)!==200){D.error(T==null?void 0:T.info);return}s((T==null?void 0:T.info)||[]),x({current:y,pageSize:C,total:(T==null?void 0:T.total)||0})}).catch(()=>D.error("Failed to fetch list")).finally(()=>i(!1))};o.useEffect(()=>{j()},[t]);const A=y=>{Ee("Are you sure you want to delete?",()=>{mr({id:y.key}).then(C=>{if((C==null?void 0:C.status)!==200){D.error(C==null?void 0:C.info);return}D.success("Deleted successfully"),j(c.current,c.pageSize)}).catch(()=>D.error("Delete failed"))})},u=y=>{da(y.key).then(C=>{if((C==null?void 0:C.status)!==200){D.error(C==null?void 0:C.info);return}let w=(C==null?void 0:C.info)||null;if(w&&typeof w.ssid_configure=="string")try{w.ssid_configure=JSON.parse(w.ssid_configure)}catch{w.ssid_configure={}}f(w),l(!0)}).catch(()=>D.error("Failed to fetch detail"))},p=()=>{f(null),l(!0)},v=()=>{l(!1),f(null)},b=(y,C)=>{fr({id:C.key,is_enable:y?1:0}).then(w=>{if((w==null?void 0:w.status)!==200){D.error(w==null?void 0:w.info);return}D.success("Status updated"),s(E=>E.map(T=>T.id===C.key?{...T,is_enable:y?1:0}:T))}).catch(()=>D.error("Failed to update status"))},m=y=>{da(y.key).then(C=>{if((C==null?void 0:C.status)!==200){D.error(C==null?void 0:C.info);return}let w=(C==null?void 0:C.info)||null;if(w&&typeof w.ssid_configure=="string"){w.name=w.name+"_Copy";try{w.ssid_configure=JSON.parse(w.ssid_configure),w.ssid_configure.name=w.ssid_configure.name+"_Copy",delete w.id}catch{w.ssid_configure={}}}f(w),l(!0)}).catch(()=>D.error("Failed to fetch detail"))},I=(a||[]).map(y=>({key:y.id,name:y.name,security:y.security,radio:y.radio,vlan_or_dhcp_name:y.network_type===2?y.vlan_or_dhcp_name:"-",labels_name:y.labels_name,is_enable:y.is_enable,originResource:y})),S=[{title:"SSID Name",dataIndex:"name",key:"name",sorter:!0},{title:"Security",dataIndex:"security",key:"security",sorter:!0},{title:"Radio",dataIndex:"radio",key:"radio",sorter:!0},{title:"VLAN",dataIndex:"vlan_or_dhcp_name",key:"vlan_or_dhcp_name",sorter:!1},{title:"AP Label",dataIndex:"labels_name",key:"labels_name",sorter:!0,render:y=>Array.isArray(y)?y.join(", "):y||""},{title:"WLAN Status",dataIndex:"is_enable",key:"is_enable",sorter:!0,render:(y,C)=>e.jsx(te,{checked:y===1,onChange:w=>b(w,C)})},{title:"Operation",key:"operation",render:(y,C)=>e.jsxs(Xe,{size:24,children:[e.jsx(Z,{style:{padding:0},type:"link",onClick:()=>u(C),children:"Edit"}),e.jsx(Z,{style:{padding:0},type:"link",onClick:()=>m(C),children:"Copy"}),e.jsx(Z,{style:{padding:0},type:"link",onClick:()=>A(C),children:"Delete"})]})}];return e.jsxs("div",{children:[e.jsx("div",{style:{marginBottom:16},children:e.jsxs(Z,{type:"primary",onClick:p,children:[e.jsx(Fe,{component:bt}),"Create New SSID"]})}),e.jsx(et,{columns:S,dataSource:I,loading:n,pagination:{current:c.current,pageSize:c.pageSize,total:c.total,showTotal:(y,C)=>`${C[0]}-${C[1]} of ${y} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],onChange:(y,C)=>j(y,C)},rowKey:"key",bordered:!0,onChange:(y,C,w)=>{let E="",T="";Array.isArray(w)||(w.order==="ascend"?E="asc":w.order==="descend"&&(E="desc"),typeof w.field=="string"?T=w.field:T=""),h({field:T,order:E}),j(y.current,y.pageSize,{field:T,order:E})}}),e.jsx(al,{modalOpen:r,onClose:v,refresh:()=>j(c.current,c.pageSize),resource:d,isDisabled:!1,siteId:t},(d==null?void 0:d.id)||String(r))]})},nl=(t,a)=>{var s,n,i;try{if(!t||!a)return null;const r=t.split("."),{length:l}=r;if(l<2)return null;const d=r.slice(0,l-1),f=r[l-1];return((i=(n=(s=a[d.slice(0,l-1).join(".")])==null?void 0:s.properties)==null?void 0:n[f??""])==null?void 0:i.description)??null}catch{return null}},il=({definitionKey:t})=>{const{configurationDescriptions:a}=_s();if(!o.useMemo(()=>nl(t,a),[a]))return null;const{title:n,icon:i}=J(t);return e.jsx(ue,{title:n,children:i})},we=G.memo(il),{Option:rl}=Q,ll=({options:t,label:a,value:s="",onChange:n,onBlur:i,error:r=void 0,touched:l=!1,isRequired:d=!1,isDisabled:f=!1,isHidden:c=!1,isLabelHidden:x=!1,w:g=void 0,definitionKey:h=void 0,dropdownRender:j,onDropdownVisibleChange:A})=>c?null:e.jsx(k.Item,{label:!x&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:a}),e.jsx(we,{definitionKey:h}),d&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]}),validateStatus:r&&l?"error":"",help:r&&l?r:null,children:e.jsx(Q,{value:s,onChange:u=>n==null?void 0:n({target:{value:u}}),onBlur:i,disabled:f,style:{borderRadius:8,width:typeof g=="number"?`${g}px`:g||"100%"},popupMatchSelectWidth:!0,dropdownRender:j,onDropdownVisibleChange:A,children:t.map(u=>e.jsx(rl,{value:u.value,children:u.label},ke()))})}),ol=G.memo(ll,Bt),dl=({options:t,name:a,isDisabled:s,label:n,isRequired:i,onChange:r,onChangeEffect:l,isHidden:d,isLabelHidden:f,emptyIsUndefined:c,isInt:x,w:g,definitionKey:h,dropdownRender:j,onDropdownVisibleChange:A})=>{const[{value:u},{touched:p,error:v},{setValue:b,setTouched:m}]=vt(a),I=o.useCallback(y=>{r?r(y):(c&&y.target.value===""?b(void 0):b(x?parseInt(y.target.value,10):y.target.value),l!==void 0&&l(y),setTimeout(()=>{m(!0)},200))},[r]),S=o.useCallback(()=>{m(!0)},[]);return e.jsx(ol,{label:n,value:u,onChange:I,onBlur:S,error:v,touched:p,options:t,isDisabled:s,isRequired:i,isHidden:d,isLabelHidden:f,w:g,definitionKey:h,dropdownRender:j,onDropdownVisibleChange:A})},ft=G.memo(dl),cl=({element:t,label:a,value:s,onChange:n,onBlur:i,error:r,isError:l,isRequired:d,isDisabled:f,definitionKey:c})=>(console.log("value",s),e.jsx(k.Item,{label:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:a}),e.jsx(we,{definitionKey:c}),d&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]}),help:l?r:null,validateStatus:l?"error":"",labelCol:{style:{display:"flex",alignItems:"center"}},style:{marginBottom:a==="VLAN"&&s?8:24},children:t??e.jsx(te,{checked:s,onChange:n,onBlur:i,disabled:f})})),ul=G.memo(cl),hl=({name:t,isDisabled:a=!1,label:s,isRequired:n=!1,defaultValue:i,element:r,falseIsUndefined:l,definitionKey:d,onChangeCallback:f})=>{const{value:c,error:x,isError:g,onChange:h,onBlur:j}=se({name:t}),A=o.useCallback(u=>{h(l&&!u?void 0:u),f&&f(u)},[l,f,h]);return e.jsx(ul,{label:s??t,value:c===void 0&&i!==void 0?i:c!==void 0&&c,onChange:A,error:x,onBlur:j,isError:g,isDisabled:a,isRequired:n,element:r,definitionKey:d})},le=G.memo(hl),fl=({editing:t})=>{const{t:a}=K(),{values:s,setFieldValue:n,errors:i}=ye(),r=[{value:"",label:a("common.none")},{value:"UTC-11:00",label:"Midway Islands Time (UTC-11:00)"},{value:"UTC-10:00",label:"Hawaii Standard Time (UTC-10:00)"},{value:"UTC-8:00",label:"Pacific Standard Time (UTC-8:00)"},{value:"UTC-7:00",label:"Mountain Standard Time (UTC-7:00)"},{value:"UTC-6:00",label:"Central Standard Time (UTC-6:00)"},{value:"UTC-5:00",label:"Eastern Standard Time (UTC-5:00)"},{value:"UTC-4:00",label:"Puerto Rico and US Virgin Islands Time (UTC-4:00)"},{value:"UTC-3:30",label:"Canada Newfoundland Time (UTC-3:30)"},{value:"UTC-3:00",label:"Brazil Eastern Time (UTC-3:00)"},{value:"UTC-1:00",label:"Central African Time (UTC-1:00)"},{value:"UTC",label:"Universal Coordinated Time (UTC)"},{value:"UTC+1:00",label:"European Central Time (UTC+1:00)"},{value:"UTC+2:00",label:"Eastern European Time (UTC+2:00)"},{value:"UTC+2:00",label:"(Arabic) Egypt Standard Time (UTC+2:00)"},{value:"UTC+3:00",label:"Eastern African Time (UTC+3:00)"},{value:"UTC+3:30",label:"Middle East Time (UTC+3:30)"},{value:"UTC+4:00",label:"Near East Time (UTC+4:00)"},{value:"UTC+5:00",label:"Pakistan Lahore Time (UTC+5:00)"},{value:"UTC+5:30",label:"India Standard Time (UTC+5:30)"},{value:"UTC+6:00",label:"Bangladesh Standard Time (UTC+6:00)"},{value:"UTC+7:00",label:"Vietnam Standard Time (UTC+7:00)"},{value:"UTC+8:00",label:"China Taiwan Time (UTC+8:00)"},{value:"UTC+9:00",label:"Japan Standard Time (UTC+9:00)"},{value:"UTC+9:30",label:"Australia Central Time (UTC+9:30)"},{value:"UTC+10:00",label:"Australia Eastern Time (UTC+10:00)"},{value:"UTC+11:00",label:"Solomon Standard Time (UTC+11:00)"},{value:"UTC+12:00",label:"New Zealand Standard Time (UTC+12:00)"},{value:"UTC",label:"UTC,Canary Islands (UTC)"},{value:"UTC",label:"Casablanca (UTC)"},{value:"UTC",label:"Monrovia, Reykjavik (UTC)"},{value:"UTC",label:"Dublin, Edinburgh, Lisbon, London (UTC)"},{value:"UTC+01:00",label:"Western Central Africa (UTC+01:00)"},{value:"UTC+01:00",label:"Brussels, Copenhagen, Madrid, Paris (UTC+01:00)"},{value:"UTC+01:00",label:"Windhoek (UTC+01:00)"},{value:"UTC+01:00",label:"Sarajevo, Skopje, Warsaw, Zagreb (UTC+01:00)"},{value:"UTC+01:00",label:"Belgrade, Bratislava, Budapest, Ljubljana, Prague (UTC+01:00)"},{value:"UTC+01:00",label:"Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna (UTC+01:00)"},{value:"UTC+02:00",label:"Harare, Pretoria (UTC+02:00)"},{value:"UTC+02:00",label:"Damascus (UTC+02:00)"},{value:"UTC+02:00",label:"Amman (UTC+02:00)"},{value:"UTC+02:00",label:"Cairo (UTC+02:00)"},{value:"UTC+02:00",label:"Minsk (UTC+02:00)"},{value:"UTC+02:00",label:"Jerusalem (UTC+02:00)"},{value:"UTC+02:00",label:"Beirut (UTC+02:00)"},{value:"UTC+02:00",label:"Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius (UTC+02:00)"},{value:"UTC+02:00",label:"Athens, Bucharest (UTC+02:00)"},{value:"UTC+03:00",label:"Istanbul (UTC+03:00)"},{value:"UTC+03:00",label:"Nairobi (UTC+03:00)"},{value:"UTC+03:00",label:"Kaliningrad (UTC+03:00)"},{value:"UTC+03:00",label:"Baghdad (UTC+03:00)"},{value:"UTC+03:00",label:"Kuwait, Riyadh (UTC+03:00)"},{value:"UTC+03:00",label:"Moscow, St. Petersburg (UTC+03:00)"},{value:"UTC+03:30",label:"Tehran (UTC+03:30)"},{value:"UTC+04:00",label:"Volgograd (UTC+04:00)"},{value:"UTC+04:00",label:"Yerevan (UTC+04:00)"},{value:"UTC+04:00",label:"Baku (UTC+04:00)"},{value:"UTC+04:00",label:"Tbilisi (UTC+04:00)"},{value:"UTC+04:00",label:"Port Louis (UTC+04:00)"},{value:"UTC+04:00",label:"Abu Dhabi, Muscat (UTC+04:00)"},{value:"UTC+04:30",label:"Kabul (UTC+04:30)"},{value:"UTC+05:00",label:"Islamabad, Karachi (UTC+05:00)"},{value:"UTC+05:00",label:"Tashkent (UTC+05:00)"},{value:"UTC+05:30",label:"Chennai, Kolkata, Mumbai, New Delhi (UTC+05:30)"},{value:"UTC+05:30",label:"Sri Lanka Standard Time (UTC+05:30)"},{value:"UTC+05:45",label:"Kathmandu (UTC+05:45)"},{value:"UTC+06:00",label:"Ekaterinburg (UTC+06:00)"},{value:"UTC+06:00",label:"Dhaka (UTC+06:00)"},{value:"UTC+06:00",label:"Astana (UTC+06:00)"},{value:"UTC+06:30",label:"Yangon (UTC+06:30)"},{value:"UTC+07:00",label:"Novosibirsk (UTC+07:00)"},{value:"UTC+07:00",label:"Bangkok, Hanoi, Jakarta (UTC+07:00)"},{value:"UTC+08:00",label:"Ulaanbaatar (UTC+08:00)"},{value:"UTC+08:00",label:"Irkutsk (UTC+08:00)"},{value:"UTC+08:00",label:"Krasnoyarsk (UTC+08:00)"},{value:"UTC+08:00",label:"Beijing, Chongqing, Hong Kong, Urumqi (UTC+08:00)"},{value:"UTC+08:00",label:"Taipei (UTC+08:00)"},{value:"UTC+08:00",label:"Kuala Lumpur, Singapore (UTC+08:00)"},{value:"UTC+08:00",label:"Perth (UTC+08:00)"},{value:"UTC+09:00",label:"Osaka, Sapporo, Tokyo (UTC+09:00)"},{value:"UTC+09:00",label:"Yakutsk (UTC+09:00)"},{value:"UTC+09:00",label:"Seoul (UTC+09:00)"},{value:"UTC+09:30",label:"Darwin (UTC+09:30)"},{value:"UTC+09:30",label:"Adelaide (UTC+09:30)"},{value:"UTC+10:00",label:"Guam, Port Moresby (UTC+10:00)"},{value:"UTC+10:00",label:"Canberra, Melbourne, Sydney (UTC+10:00)"},{value:"UTC+10:00",label:"Brisbane (UTC+10:00)"},{value:"UTC+10:00",label:"Hobart (UTC+10:00)"},{value:"UTC+11:00",label:"Solomon Islands, New Caledonia (UTC+11:00)"},{value:"UTC+11:00",label:"Vladivostok (UTC+11:00)"},{value:"UTC+12:00",label:"UTC+12 (UTC+12:00)"},{value:"UTC+12:00",label:"Auckland, Wellington (UTC+12:00)"},{value:"UTC+12:00",label:"Fiji (UTC+12:00)"},{value:"UTC+12:00",label:"Magadan (UTC+12:00)"},{value:"UTC+13:00",label:"Nukualofa (UTC+13:00)"},{value:"UTC+14:00",label:"Kiritimati (UTC+14:00)"},{value:"UTC-01:00",label:"Azores Islands (UTC-01:00)"},{value:"UTC-01:00",label:"Cape Verde Islands (UTC-01:00)"},{value:"UTC-02:00",label:"Mid-Atlantic (UTC-02:00)"},{value:"UTC-02:00",label:"UTC-02 (UTC-02:00)"},{value:"UTC-03:00",label:"Cayenne, Fortaleza (UTC-03:00)"},{value:"UTC-03:00",label:"Brasilia (UTC-03:00)"},{value:"UTC-03:00",label:"Buenos Aires (UTC-03:00)"},{value:"UTC-03:00",label:"Greenland (UTC-03:00)"},{value:"UTC-03:00",label:"Montevideo (UTC-03:00)"},{value:"UTC-03:30",label:"Newfoundland (UTC-03:30)"},{value:"UTC-04:00",label:"Georgetown, La Paz, Manaus, San Juan (UTC-04:00)"},{value:"UTC-04:00",label:"Asuncion (UTC-04:00)"},{value:"UTC-04:00",label:"Santiago (UTC-04:00)"},{value:"UTC-04:00",label:"Atlantic Time (Canada) (UTC-04:00)"},{value:"UTC-04:00",label:"Cuiaba (UTC-04:00)"},{value:"UTC-04:30",label:"Caracas (UTC-04:30)"},{value:"UTC-05:00",label:"Eastern Time (US and Canada) (UTC-05:00)"},{value:"UTC-05:00",label:"Indiana (East) (UTC-05:00)"},{value:"UTC-05:00",label:"Bogota, Lima, Quito (UTC-05:00)"},{value:"UTC-06:00",label:"Central America (UTC-06:00)"},{value:"UTC-06:00",label:"Central Time (US and Canada) (UTC-06:00)"},{value:"UTC-06:00",label:"Guadalajara, Mexico City, Monterrey (UTC-06:00)"},{value:"UTC-06:00",label:"Saskatchewan (UTC-06:00)"},{value:"UTC-07:00",label:"Arizona (UTC-07:00)"},{value:"UTC-07:00",label:"Chihuahua, La Paz, Mazatlan (UTC-07:00)"},{value:"UTC-07:00",label:"Mountain Time (US and Canada) (UTC-07:00)"},{value:"UTC-08:00",label:"Baja California (UTC-08:00)"},{value:"UTC-08:00",label:"Pacific Time (US and Canada) (UTC-08:00)"},{value:"UTC-09:00",label:"Alaska (UTC-09:00)"},{value:"UTC-10:00",label:"Hawaii (UTC-10:00)"},{value:"UTC-11:00",label:"Samoa (UTC-11:00)"},{value:"UTC-11:00",label:"UTC-11 (UTC-11:00)"},{value:"UTC-12:00",label:"Dateline Standard Time (UTC-12:00)"}].map((l,d)=>({value:l.value!==""?`${l.value}#${d}`:l.value,label:l.label}));return e.jsxs(U,{gutter:[16,0],children:[e.jsx(F,{span:12,children:e.jsx(le,{name:"configuration.unit.hostname_enable",label:"Hostname",definitionKey:"unit.hostname",isDisabled:!t})}),e.jsx(F,{span:12,children:e.jsx(ft,{w:280,name:"configuration.unit.timezone",label:"Timezone",definitionKey:"unit.timezone",emptyIsUndefined:!0,isDisabled:!t,options:r})}),e.jsx(F,{span:12,children:e.jsx(le,{name:"configuration.unit.leds-active",label:"Leds-Active",definitionKey:"unit.leds-active",isDisabled:!t,isRequired:!0})}),e.jsx(F,{span:12,children:e.jsx(le,{name:"configuration.unit.random-password",label:"Random-Password",definitionKey:"unit.random-password",isDisabled:!t,isRequired:!0})})]})},ml=({siteId:t})=>{if(window.location.hash){const S=window.location.hash.replace("#","");/^\d+$/.test(S)&&(t=parseInt(S,10))}const[a,s]=o.useState([]),[n,i]=o.useState(!1),[r,l]=o.useState(!1),[d,f]=o.useState(null),[c,x]=o.useState({current:1,pageSize:10,total:0}),[g,h]=o.useState([]),j=o.useCallback(S=>{const y=Math.floor(new Date(S).getTime()/1e3);return e.jsx(yt,{date:y})},[]),A=(S,y)=>{if(i(!0),t==null||t==null){i(!1);return}const C=y.length>0?y[0].field:"",w=y.length>0?y[0].order:"",E={siteId:t,pageNum:S.current,pageSize:S.pageSize};C&&(E.sortBy=C,E.sortType=w),$t(E).then(T=>{if((T==null?void 0:T.status)!==200){D.error(T==null?void 0:T.info);return}s((T==null?void 0:T.info)||[]),x({...S,total:(T==null?void 0:T.total)||0})}).catch(()=>D.error("Failed to fetch list")).finally(()=>i(!1))};o.useEffect(()=>{A(c,g)},[t]);const u=(S,y,C)=>{const w={current:S.current||1,pageSize:S.pageSize||10,total:S.total};let E=C.field;const T=C.field?[{field:E,order:C.order==="ascend"?"asc":"desc"}]:[];h(T),A(w,T)},p=S=>{Ee(`Are you sure you want to delete this ${S.name}?`,()=>{tl({id:S.id}).then(y=>{if((y==null?void 0:y.status)!==200){D.error(y==null?void 0:y.info);return}D.success("Deleted successfully"),A(c,g)}).catch(()=>D.error("Delete failed"))})},v=S=>{console.log(S.id),Zr({id:S.id}).then(y=>{if((y==null?void 0:y.status)!==200){D.error(y==null?void 0:y.info);return}console.log(y);let C=(y==null?void 0:y.info)||null;f(C),l(!0)}).catch(()=>D.error("Failed to fetch detail"))},b=()=>{f(null),l(!0)},m=()=>{l(!1),f(null)},I=[{title:"Name",dataIndex:"name",key:"name",sorter:!0},{title:"IPv4 subNet",dataIndex:"subnet",key:"subnet",sorter:!0},{title:"VLAN",dataIndex:"vlan",key:"vlan",sorter:!0},{title:"Modified",dataIndex:"modified_time",key:"modified_time",render:S=>j(S),sorter:!0},{title:"Description",dataIndex:"description",key:"description",sorter:!0},{title:"Operation",key:"operation",render:(S,y)=>e.jsxs(Xe,{size:24,children:[e.jsx(Z,{style:{padding:0},type:"link",onClick:()=>v(y),children:"Edit"}),e.jsx(Z,{style:{padding:0},type:"link",onClick:()=>p(y),children:"Delete"})]})}];return e.jsxs("div",{children:[e.jsx("div",{style:{marginBottom:24},children:e.jsxs(Z,{type:"primary",onClick:b,children:[e.jsx(Fe,{component:bt}),"Create"]})}),e.jsx(De,{vertical:!0,style:{position:"relative",width:"100%",marginBottom:a&&a.length>0?"8px":"24px"},children:e.jsx(et,{columns:I,dataSource:a,loading:n,onChange:u,pagination:{current:c.current,pageSize:c.pageSize,total:c.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:S=>`Total ${S} items`,pageSizeOptions:["10","20","50","100"]},scroll:{x:1e3},rowKey:"key",bordered:!0})}),e.jsx(Ze,{isModalOpen:r,title:d?"Edit":"Create",onCancel:m,modalClass:"ampcon-max-modal",childItems:e.jsx(Wt,{onClose:m,refresh:()=>A(c,g),resource:d,siteId:t})})]})},pl={value:P.arrayOf(P.oneOfType([P.string,P.number])),label:P.string.isRequired,onChange:P.func.isRequired,options:P.arrayOf(P.shape({label:P.string.isRequired,value:P.oneOfType([P.string,P.number]).isRequired})).isRequired,onBlur:P.func.isRequired,error:P.oneOfType([P.string,P.bool]),touched:P.bool,isDisabled:P.bool,canSelectAll:P.bool,isRequired:P.bool,isHidden:P.bool,isPortal:P.bool.isRequired,definitionKey:P.string,w:P.oneOfType([P.string,P.number])},gl={value:[],error:!1,touched:!1,isRequired:!1,canSelectAll:!1,isDisabled:!1,isHidden:!1,definitionKey:null},Jt=({options:t,label:a,value:s,onChange:n,onBlur:i,error:r,touched:l,canSelectAll:d,isRequired:f,isDisabled:c,isHidden:x,isPortal:g,definitionKey:h,w:j})=>{const{t:A}=K(),[u,p]=o.useState(!1),[v,b]=o.useState(s??[]),[m,I]=o.useState(""),S=v.includes("LAN*"),y=d?[{value:"*",label:A("common.all")},...t]:t,C=y.filter(L=>L.label.toLowerCase().includes(m.toLowerCase())||L.value.toString().toLowerCase().includes(m.toLowerCase()));o.useEffect(()=>{b(s??[])},[s]);const w=()=>{n(v),p(!1),I("")},E=()=>{b([]),n([]),p(!1),I("")},T=L=>{I(L.target.value)};return e.jsx(k.Item,{label:!x&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:a}),e.jsx(we,{definitionKey:h}),f&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]}),validateStatus:r&&l?"error":"",help:r&&l?r:null,hidden:x,children:e.jsx(Q,{placeholder:A("common.select"),mode:"multiple",open:u,showSearch:!1,onDropdownVisibleChange:p,allowClear:!0,value:v,options:y,onChange:L=>{b(L),n(L)},onBlur:i,disabled:c,getPopupContainer:L=>L.parentElement||document.body,style:{width:typeof j=="number"?`${j}px`:j||"100%"},dropdownRender:L=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{style:{padding:4,maxHeight:200,overflowY:"auto"},children:[e.jsx(ae,{prefix:e.jsx(Us,{style:{color:"rgba(0,0,0,.25)"}}),value:m,onChange:T}),C.map(N=>e.jsx("div",{children:e.jsx(Nt,{style:{marginTop:"18px"},disabled:S&&N.value!=="LAN*"||!S&&N.value==="LAN*"&&v.length>0,checked:v.includes(N.value),onChange:M=>{let R;M.target.checked?R=[...v,N.value]:R=v.filter(O=>O!==N.value),b(R),n(R)},children:N.label})},N.value))]}),e.jsx(oe,{style:{margin:"8px -4px"}}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",padding:"0 0px 4px",marginLeft:"-4px"},children:[e.jsx(Z,{type:"link",size:"small",onClick:E,children:"Reset"}),e.jsx(Z,{type:"link",size:"small",onClick:w,children:"Ok"})]})]})})})};Jt.propTypes=pl;Jt.defaultProps=gl;const xl=G.memo(Jt,Bt),bl={name:P.string.isRequired,label:P.string.isRequired,options:P.arrayOf(P.shape({label:P.string.isRequired,value:P.string.isRequired})).isRequired,isDisabled:P.bool,isRequired:P.bool,isHidden:P.bool,emptyIsUndefined:P.bool,hasVirtualAll:P.bool,canSelectAll:P.bool,isPortal:P.bool,definitionKey:P.string},vl={isRequired:!1,isDisabled:!1,isHidden:!1,emptyIsUndefined:!1,hasVirtualAll:!1,canSelectAll:!1,isPortal:!1,definitionKey:null},Xt=({options:t,name:a,isDisabled:s,label:n,isRequired:i,isHidden:r,emptyIsUndefined:l,canSelectAll:d,hasVirtualAll:f,isPortal:c,definitionKey:x,w:g})=>{const[{value:h},{touched:j,error:A},{setValue:u,setTouched:p}]=vt(a),v=o.useCallback(m=>{m.length===0&&l?u(void 0):m.includes("*")?u(f?t.map(I=>I.value):["*"]):u(m),p(!0)},[l,f,t]),b=o.useCallback(()=>{p(!0)},[]);return e.jsx(xl,{canSelectAll:d,label:n,value:h,onChange:v,onBlur:b,error:A,touched:j,options:t,isDisabled:s,isRequired:i,isHidden:r,isPortal:c,definitionKey:x,w:g})};Xt.propTypes=bl;Xt.defaultProps=vl;const yl=G.memo(Xt),jl=({label:t,value:a,onChange:s,onBlur:n,isError:i,error:r,hideButton:l,isRequired:d,element:f,isArea:c,isDisabled:x,definitionKey:g,explanation:h,placeholder:j,autoComplete:A,h:u,w:p})=>{const{t:v}=K(),[b,m]=o.useState(!1),I=e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:t}),h&&e.jsx(ue,{title:h,children:e.jsx("img",{src:un,style:{marginLeft:3}})}),g&&e.jsx(we,{definitionKey:g}),d&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]});if(f)return e.jsx(k.Item,{label:I,validateStatus:i?"error":"",help:i?r:"",className:"input-item-error",children:f});const S={value:a,onChange:s,onBlur:n,disabled:x,placeholder:j,autoComplete:A??"off"};return e.jsx(k.Item,{label:I,validateStatus:i?"error":"",help:i?r:"",className:"input-item-error",children:c?e.jsx(ae.TextArea,{...S,rows:4,style:{height:u}}):l?e.jsx(ae.Password,{...S,iconRender:y=>y?e.jsx(Pn,{}):e.jsx(Ds,{})}):e.jsx(ae,{...S,style:{width:typeof p=="number"?`${p}px`:p||"100%"}})})},Cl=G.memo(jl),wl=({name:t,isDisabled:a=!1,label:s,hideButton:n=!1,isRequired:i=!1,element:r,isArea:l=!1,emptyIsUndefined:d=!1,definitionKey:f,explanation:c,placeholder:x,autoComplete:g,...h})=>{const{value:j,error:A,isError:u,onChange:p,onBlur:v}=se({name:t}),b=o.useCallback(m=>{d&&m.target.value.length===0?p(void 0):p(m.target.value)},[p]);return e.jsx(Cl,{label:s??t,value:j,onChange:b,onBlur:v,isError:u,error:A,hideButton:n,isRequired:i,element:r,isArea:l,isDisabled:a,definitionKey:f,explanation:c,placeholder:x,autoComplete:g||"off",...h})},Le=G.memo(wl),Zt="/ampcon/wireless/configure";function Sl({site_id:t,port:a,mac:s,network_type:n,vlan_or_dhcp_name:i=null,vlan_tag:r=1}){return de({url:`${Zt}/ethernet_ports`,method:"POST",data:{site_id:t,port:a,mac:s,network_type:n,vlan_or_dhcp_name:i,vlan_tag:r}})}function Tl({siteId:t,pageNum:a,pageSize:s,sortBy:n,sortType:i}){return de({url:`${Zt}/ethernet_ports`,method:"GET",params:{siteId:t,pageNum:a,pageSize:s,sortBy:n,sortType:i}}).then(r=>r)}function Al({id:t}){return de({url:`${Zt}/ethernet_ports`,method:"DELETE",data:{id:t}})}const kl=({label:t,value:a,unit:s,onChange:n,onBlur:i,error:r,isError:l,isRequired:d,hideArrows:f,element:c,isDisabled:x,w:g,min:h,max:j,definitionKey:A})=>{const u=typeof a=="string"?parseFloat(a):a,[p,v]=o.useState(!1);if(c)return e.jsx(k.Item,{label:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:t}),e.jsx(we,{definitionKey:A}),d&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]}),required:d,validateStatus:l?"error":"",help:l?r:"",className:"input-item-error",children:c});const b=e.jsx(ne,{value:u,onChange:m=>{t==" "&&(m==null||m==="")?n("1"):n((m==null?void 0:m.toString())??"")},onBlur:i,disabled:x,style:{width:typeof g=="number"?`${g}px`:g||"100%"},min:h,max:j,controls:!f,onKeyPress:m=>{const I=String(u??"");if(/[0-9\-]/.test(m.key)||m.preventDefault(),m.key==="-"){const S=m.target.selectionStart??0;(I.includes("-")||S!==0)&&m.preventDefault()}},onCompositionStart:()=>v(!0),onCompositionEnd:m=>{v(!1);const I=m.currentTarget.value;/^-?\d*\.?\d*$/.test(I)||(m.currentTarget.value="",n("0"))},onInput:m=>{if(p)return;const I=m.target;/^-?\d*\.?\d*$/.test(I.value)||(I.value=I.value.replace(/[^0-9.-]/g,""))}});return e.jsx(k.Item,{label:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:t}),e.jsx(we,{definitionKey:A}),d&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]}),validateStatus:l?"error":"",help:l?r:"",className:"input-item-error",children:s?e.jsx(ne,{addonAfter:s,value:u,onChange:m=>n((m==null?void 0:m.toString())??""),onBlur:i,disabled:x,style:{width:typeof g=="number"?`${g}px`:g||"100%"},min:h,max:j,controls:!f}):b})},Il=G.memo(kl),ga=(t,a)=>{if(a&&t==="")return;const s=parseInt(t,10);return Number.isNaN(s)?0:s},El=({name:t,unit:a,isDisabled:s=!1,label:n,isRequired:i=!1,hideArrows:r=!1,w:l,acceptEmptyValue:d=!1,definitionKey:f,conversionFactor:c,min:x,max:g})=>{const{value:h,error:j,isError:A,onChange:u,onBlur:p}=se({name:t}),v=o.useCallback(b=>{if(c){const m=ga(b,d),I=m!==void 0?m*c:void 0;u(I)}else u(ga(b,d))},[u]);return e.jsx(Il,{label:n??t,value:c?Math.ceil(h/c):h,unit:a,isError:A,onChange:v,onBlur:p,error:j,hideArrows:r,isRequired:i,isDisabled:s,w:l,min:x,max:g,definitionKey:f})},xe=G.memo(El),Fl=(t,a=!1,s="")=>{const n=s==="upstream"?["WAN*"]:[],i=H().shape({"select-ports":Y().of(B()).min(1,t("form.required")).default(n),multicast:z().default(!0),learning:z().default(!0),isolate:z().default(!1),macaddr:B().test("interface.ethernet.mac.length",t("form.invalid_mac_uc"),r=>r===void 0?!0:Ga(r)).default(void 0),"reverse-path":z().default(!1),"vlan-tag":B().default("auto"),vlan:z().default(!1),vlan_id:W().nullable().when("vlan",{is:!0,then:r=>r.required(t("form.required")).moreThan(0).lessThan(4050).default(1080),otherwise:r=>r.notRequired().nullable()})});return a?i:i.nullable().default({"select-ports":n,multicast:!0,learning:!0,isolate:!1,macaddr:void 0,"reverse-path":!1,"vlan-tag":"auto","ap-assign-ip":!1,vlan:!1,vlan_id:null})},xa=async t=>{try{const a=await $t({siteId:t,pageNum:1,pageSize:100});return(a==null?void 0:a.status)===200&&Array.isArray(a.info)?a.info.map(s=>({label:s.name,value:s.name})):[]}catch{return[]}},Pl=({title:t,okText:a,isModalOpen:s,onCancel:n,role:i="",modalClass:r="",siteId:l,onSuccess:d})=>{const{t:f}=K(),{values:c,setFieldValue:x,errors:g}=ye();c.vlan;const[h,j]=o.useState(1),[A,u]=o.useState([]),[p,v]=o.useState(!1),[b,m]=o.useState(!1),[I,S]=o.useState(!1),[y,C]=o.useState(ke()),w=G.useMemo(()=>Fl(f,!1,i),[f,i]),E=w.getDefault(),T=[{value:"LAN*",label:"LAN*"},{value:"LAN1",label:"LAN1"},{value:"LAN2",label:"LAN2"},{value:"LAN3",label:"LAN3"},{value:"LAN4",label:"LAN4"}],L=async()=>{m(!1),v(!0);const q=await xa(l);u(q),v(!1),S(!0)},N=async()=>{if(l&&!I){v(!0);const q=await xa(l);u(q),v(!1),S(!0)}},M=q=>{q&&N()},R={auto:1,tagged:2,"un-tagged":3},O=async q=>{if(!q["select-ports"]||q["select-ports"].length===0){D.error("ports can not be empty");return}const _=JSON.stringify(q["select-ports"]),ie={site_id:l,port:_,mac:q.macaddr||"",network_type:h,vlan_tag:R[q["vlan-tag"]]||1,vlan_or_dhcp_name:q["ap-assign-ip"]?q["dhcp-service-name"]:q.vlan?q.vlan_id:null},ce=await Sl(ie);if(ce.status===200)D.success("Added successfully"),d&&d(),n(),C(ke());else{const me=ce.info?`Failed to add: ${ce.info}`:"Failed to add.";D.error(me)}};return e.jsx(qt,{initialValues:E,validationSchema:w,onSubmit:async(q,{setSubmitting:_})=>{try{D.success("Added successfully"),n(),C(ce=>ce+1)}catch{D.error("Network error.")}finally{_(!1)}},children:({resetForm:q,values:_,setFieldValue:ie,validateForm:ce,setTouched:me})=>{o.useEffect(()=>{_.vlan&&!_.vlan_id?ie("vlan_id",1080):_.vlan||ie("vlan_id",null)},[_.vlan]),o.useEffect(()=>{_.vlan?j(2):_["ap-assign-ip"]?j(3):j(1)},[_.vlan,_["ap-assign-ip"]]);const $=V=>{const re={};for(const pe in V)typeof V[pe]=="object"&&V[pe]!==null?re[pe]=$(V[pe]):re[pe]=!0;return re};return e.jsx(e.Fragment,{children:e.jsxs(Ie,{className:r||"",title:e.jsxs("div",{children:[t,e.jsx(oe,{style:{marginTop:8,marginBottom:0}})]}),open:s,footer:[e.jsx(oe,{style:{marginTop:0,marginBottom:20}},"divider"),e.jsx(Z,{onClick:()=>{q(),n()},children:"Cancel"},"cancel"),e.jsx(Z,{type:"primary",onClick:()=>{ce().then(V=>{Object.keys(V).length===0?O(_):(me($(_)),D.error("Please correct the errors before saving"))})},children:"Apply"},"ok")],onCancel:()=>{q(),n()},destroyOnClose:!0,children:[e.jsxs(Pa,{children:[e.jsx(yl,{name:"select-ports",label:"Ports",options:T,isRequired:!0,w:280}),!_["ap-assign-ip"]&&e.jsxs(e.Fragment,{children:[e.jsx(le,{name:"vlan",label:"VLAN"}),_.vlan&&e.jsx(xe,{name:"vlan_id",label:" ",w:280})]}),!_.vlan&&e.jsxs(e.Fragment,{children:[e.jsx(le,{name:"ap-assign-ip",label:"AP Assign IP"}),_["ap-assign-ip"]&&e.jsx(ft,{name:"dhcp-service-name",label:"DHCP Service Name",options:A,isRequired:!0,w:280,dropdownRender:V=>e.jsxs(e.Fragment,{children:[V,e.jsx(Z,{type:"link",icon:e.jsx(Ye,{}),style:{width:"100%",borderTop:"1px solid #E7E7E7"},onClick:()=>m(!0),children:"Create New DHCP Service"})]}),onDropdownVisibleChange:M})]}),e.jsx(Le,{name:"macaddr",label:"Mac Address",w:280,emptyIsUndefined:!0}),e.jsx(ft,{name:"vlan-tag",label:"Vlan Tag",options:[{label:"Auto",value:"auto"},{label:"Tagged",value:"tagged"},{label:"Un-tagged",value:"un-tagged"}],w:280})]}),b&&e.jsx(Ze,{isModalOpen:b,title:"Create DHCP Service",onCancel:()=>m(!1),modalClass:"ampcon-max-modal",childItems:e.jsx(Wt,{resource:void 0,onClose:L,siteId:l})})]})})}},y)},Ml=({siteId:t})=>{if(window.location.hash){const E=window.location.hash.replace("#","");/^\d+$/.test(E)&&(t=parseInt(E,10))}const{t:a}=K(),[s,n]=o.useState(!1),i=o.useRef(null),[r,l]=o.useState(!1),[d,f]=o.useState([]),[c,x]=o.useState([]),[g,h]=o.useState({index:0,limit:10}),[j,A]=o.useState([]),[u,p]=o.useState([]),[v,b]=o.useState(0),[m,I]=o.useState(!1);o.useEffect(()=>{h({index:0,limit:g.limit})},[t]),o.useEffect(()=>{t!=null&&S()},[t,g,j]);const S=async()=>{var E,T;I(!0);try{const L=(E=j==null?void 0:j[0])==null?void 0:E.id,N=(T=j==null?void 0:j[0])==null?void 0:T.sort,M=await Tl({siteId:t,pageNum:g.index+1,pageSize:g.limit,sortBy:L,sortType:N});M.status===200?(p(M.info),b(M.total)):(D.error(M.info||"Failed to fetch Ethernet port list"),p([]),b(0))}catch{D.error("Error fetching Ethernet port list"),p([]),b(0)}finally{I(!1)}},y=()=>{S()};o.useEffect(()=>{i.current&&(i.current.refreshTable=y)},[y]);const C=(E,T,L)=>{h({index:E.current-1,limit:E.pageSize}),L.field?A([{id:L.field,sort:L.order==="ascend"?"asc":"desc"}]):A([])},w=[{key:"port",title:"Ports",dataIndex:"port",sorter:!0,render:E=>JSON.parse(E).join(","),width:"15%"},{key:"mac",title:"Mac Address",dataIndex:"mac",render:E=>E==""?"--":E,width:"15%"},{key:"vlan_or_dhcp_name",title:"VLAN/DHCP Service",dataIndex:"vlan_or_dhcp_name",render:E=>E??"-",width:"15%"},{key:"vlan_tag",title:"Vlan Tag",dataIndex:"vlan_tag",render:E=>({1:"auto",2:"tagged",3:"un-tagged"})[E]||"unkown",width:"15%"},{key:"operation",title:"Operation",render:(E,T)=>e.jsx(De,{style:{flexWrap:"wrap",columnGap:"24px",rowGap:"5px"},className:"actionLink",children:e.jsx("a",{onClick:()=>{Ee(`This action will delete ethernet: ${T.port}, Do you want to continue?`,()=>{l(!0);try{Al({id:T.id}).then(L=>{L.status!==200?D.error(L.info):(D.success(L.info),y())})}catch{D.error("An error occurred during the process of delete")}finally{l(!1)}})},children:"Delete"})}),width:"15%"}];return e.jsxs(e.Fragment,{children:[e.jsx(Pl,{title:"Create",onText:"Apply",isModalOpen:s,onCancel:()=>n(!1),modalClass:"ampcon-middle-modal",siteId:t,onSuccess:y}),e.jsx(Xe,{size:16,style:{marginTop:"8px"},children:e.jsxs(Z,{type:"primary",block:!0,onClick:()=>n(!0),style:{marginBottom:"4px"},children:[e.jsx(Fe,{component:bt}),"Create"]})}),e.jsx(De,{vertical:!0,style:{position:"relative",width:"100%",marginBottom:u&&u.length>0?"8px":"24px"},children:e.jsx(Os,{ref:i,columns:w,dataSource:u,loading:m,onChange:C,disableInternalRowSelection:!0,pagination:{current:g.index+1,pageSize:g.limit,total:v,showSizeChanger:!0,showQuickJumper:!0,showTotal:E=>`Total ${E} items`,pageSizeOptions:["10","20","50","100"]}})})]})},Ka=(t,a=!1)=>{const s=H().shape({enabled:z().default(!1),describe:B().required(t("form.required")).default(""),location:B().required(t("form.required")).default("")});return a?s:s.nullable().default(void 0)},$a=(t,a=!1)=>{const s=H().shape({enabled:z().default(!0),port:W().required(t("form.required")).moreThan(-1).lessThan(65536).integer().default(22),"password-authentication":z().default(!0),"authorized-keys":Y().of(B()).when("password-authentication",{is:!1,then:Y().of(B()).required(t("form.required")).min(1,t("form.required")).default([]),otherwise:Y().of(B())}).default(void 0)});return a?s:s.nullable().default(void 0)},Wa=(t,a=!1)=>{const s=H().shape({enabled:z().default(!1),servers:Y().of(B().required(t("form.required")).test("ntp-servers",t("form.invalid_domain_or_ip"),async n=>{if(!n)return!1;try{return await ht(n),!0}catch{return!1}})).required(t("form.required")).min(1,t("form.required")).default([]),"local-server":z().default(!1)});return a?s:s.nullable().default(void 0)},Ja=(t,a=!1)=>{const s=H().shape({enable:z().default(!1)});return a?s:s.nullable().default(void 0)},Xa=(t,a=!1)=>{const s=H().shape({enabled:z().default(!1),host:B().required(t("form.required")).test("rtty.host.value",t("form.invalid_fqdn_host"),kr).default(""),port:W().required(t("form.required")).moreThan(-1).lessThan(65536).integer().default(5912),token:B().required(t("form.required")).test("rtty.token.length",t("form.min_max_string",{min:32,max:32}),n=>Ir({val:n,min:32,max:32})).default("")});return a?s:s.nullable().default(void 0)},Za=(t,a=!1)=>{const s=H().shape({enabled:z().default(!1),host:B().required(t("form.required")).test("log.host.value",t("form.invalid_cidr"),Rt).default(""),port:W().required(t("form.required")).moreThan(-1).lessThan(65536).integer().default(5912),proto:B().required(t("form.required")).default("udp"),size:W().required(t("form.required")).moreThan(31).lessThan(65536).integer().default(1e3)});return a?s:s.nullable().default(void 0)},Ya=(t,a=!1)=>{const s=H().shape({enabled:z().default(!0),"http-port":W().required(t("form.required")).moreThan(0).lessThan(65536).integer().default(80)});return a?s:s.nullable().default(void 0)},es=(t,a=!1)=>{const s=H().shape({enable:z().default(!1)});return a?s:s.nullable().default(void 0)},ts=(t,a=!1)=>{const s=H().shape({enabled:z().default(!1),"ping-hosts":Y().of(B().required(t("form.required")).test("online-check-servers",t("form.invalid_domain_or_ip"),async n=>{if(!n)return!1;try{return await ht(n),!0}catch{return!1}})).required(t("form.required")).min(1,t("form.required")).default([]),"download-hosts":Y().of(B()).required(t("form.required")).min(1,t("form.required")).default([]),"check-interval":W().required(t("form.required")).moreThan(-1).lessThan(65536).integer().default(60),"check-threshold":W().required(t("form.required")).moreThan(-1).lessThan(65536).integer().default(1),action:Y().of(B()).required(t("form.required")).min(1,t("form.required")).default([])});return a?s:s.nullable().default(void 0)},as=(t,a=!1)=>{const s=H().shape({mode:B().required(t("form.required")).default("local"),"assoc-steering":z().default(!1),"auto-channel":z().default(!1),"required-snr":W().required(t("form.required")).integer().default(0),"required-probe-snr":W().required(t("form.required")).moreThan(-1).lessThan(101).integer().default(0),"required-roam-snr":W().required(t("form.required")).moreThan(-1).lessThan(101).integer().default(0),"load-kick-threshold":W().required(t("form.required")).moreThan(-1).lessThan(101).integer().default(75)});return a?s:s.nullable().default(void 0)},Rl=(t,a=!1)=>H().shape({configuration:H().shape({services:H().shape({lldp:Ka(t,a),ssh:$a(t,a),ntp:Wa(t,a),http:Ya(t,a),mdns:Ja(t,a),rtty:Xa(t,a),log:Za(t,a),igmp:es(t,a),"online-check":ts(t,a),"wifi-steering":as(t,a)})})}),Me=(t,a)=>{switch(a){case"lldp":return Ka(t,!0).cast();case"ssh":return $a(t,!0).cast();case"ntp":return Wa(t,!0).cast();case"mdns":return Ja(t,!0).cast();case"rtty":return Xa(t,!0).cast();case"log":return Za(t,!0).cast();case"http":return Ya(t,!0).cast();case"igmp":return es(t,!0).cast();case"online-check":return ts(t,!0).cast();case"wifi-steering":return as(t,!0).cast();default:return null}},_l=()=>{const{value:t}=se({name:"configuration.services.http.enabled"}),{value:a}=se({name:"configuration.services.http"}),{t:s}=K(),{values:n,setFieldValue:i,errors:r}=ye();return o.useEffect(()=>{if(t){if(!a||a&&Object.keys(a).length<=1&&a.enabled===!0){const l=Me(s,"http");i("configuration.services.http",{...l})}}else i("configuration.services.http",void 0)},[t]),e.jsx(e.Fragment,{children:e.jsx(U,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.http.enabled",label:"Http"}),t&&e.jsx(xe,{name:"configuration.services.http.http-port",label:"Http-Port",definitionKey:"service.http.http-port",isRequired:!0,w:140})]})})})},Ul=G.memo(_l),Dl={value:P.arrayOf(P.string),label:P.string.isRequired,onChange:P.func.isRequired,onBlur:P.func.isRequired,error:P.oneOfType([P.string,P.bool]),touched:P.bool,isDisabled:P.bool,isRequired:P.bool,isHidden:P.bool,definitionKey:P.string,placeholder:P.string,width:P.oneOfType([P.string,P.number])},Ol={value:[],error:!1,touched:!1,isRequired:!1,isDisabled:!1,isHidden:!1,definitionKey:null,placeholder:""},Yt=({label:t,value:a,onChange:s,onBlur:n,error:i,touched:r,isRequired:l,isDisabled:d,isHidden:f,definitionKey:c,placeholder:x,w:g})=>{const{t:h}=K(),[j,A]=o.useState(""),u=a||[],p=m=>{A(m)},v=m=>{s(m),A("")},b=[...u.map(m=>({label:m,value:m})),...j&&!u.includes(j)?[{label:`Create "${j}"`,value:j}]:[]];return e.jsx(k.Item,{label:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:t}),e.jsx(we,{definitionKey:c}),l&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]}),hidden:f,validateStatus:i&&r?"error":"",help:r&&i?i:null,className:"input-item-error",children:e.jsx(Q,{mode:"tags",style:{width:typeof g=="number"?`${g}px`:g||"100%"},placeholder:x,value:u,onChange:v,onBlur:n,disabled:d,options:b,onSearch:p,showSearch:!0,notFoundContent:j?null:h("common.type_for_options"),tagRender:m=>{const{label:I,closable:S,onClose:y}=m,C=String(I);return e.jsxs("span",{style:{display:"inline-flex",alignItems:"center",maxWidth:250,background:"#f0f0f0",borderRadius:4,padding:"0 6px",marginRight:4},children:[e.jsx(ue,{title:C,overlayStyle:{maxWidth:400},children:e.jsx("span",{style:{flex:1,minWidth:0,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:C})}),S&&e.jsx(Ns,{onClick:y,style:{marginLeft:6,fontSize:12,color:"rgba(0,0,0,.45)",cursor:"pointer",flexShrink:0}})]})}})})};Yt.propTypes=Dl;Yt.defaultProps=Ol;const Nl=G.memo(Yt),Bl={name:P.string.isRequired,label:P.string.isRequired,isDisabled:P.bool,isRequired:P.bool,isHidden:P.bool,emptyIsUndefined:P.bool,placeholder:P.string,definitionKey:P.string},Ll={isRequired:!1,isDisabled:!1,isHidden:!1,emptyIsUndefined:!1,placeholder:"",definitionKey:null},ea=({name:t,isDisabled:a,label:s,isRequired:n,isHidden:i,emptyIsUndefined:r,placeholder:l,definitionKey:d,w:f})=>{const[{value:c},{touched:x,error:g},{setValue:h,setTouched:j}]=vt(t),A=o.useCallback(p=>{r&&p.length===0?h(void 0):h(p),j(!0)},[r]),u=o.useCallback(()=>{j(!0)},[]);return e.jsx(Nl,{label:s,value:c,onChange:A,onBlur:u,error:g,touched:x,placeholder:l,isDisabled:a,isRequired:n,isHidden:i,definitionKey:d,w:f})};ea.propTypes=Bl;ea.defaultProps=Ll;const mt=G.memo(ea),ql=()=>{const{value:t}=se({name:"configuration.services.ssh.enabled"}),{value:a}=se({name:"configuration.services.ssh"}),{value:s}=se({name:"configuration.services.ssh.password-authentication"}),{onChange:n,onBlur:i}=se({name:"configuration.services.ssh.authorized-keys"}),{t:r}=K(),{values:l,setFieldValue:d,errors:f}=ye();o.useEffect(()=>{if(t){if(!a||a&&Object.keys(a).length<=1&&a.enabled===!0){const x=Me(r,"ssh");d("configuration.services.ssh",{...x,enabled:!0})}}else d("configuration.services.ssh",void 0)},[t]);const c=o.useCallback(x=>{x?(n(void 0),setTimeout(()=>{i()},200)):(n([]),setTimeout(()=>{i()},200))},[]);return e.jsx(U,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.ssh.enabled",label:"SSH"}),t&&e.jsxs(e.Fragment,{children:[e.jsx(xe,{name:"configuration.services.ssh.port",label:"Port",definitionKey:"service.ssh.port",isRequired:!0,w:140}),e.jsx(le,{name:"configuration.services.ssh.password-authentication",label:"Password-Authentication",definitionKey:"service.ssh.password-authentication",onChangeCallback:c,isRequired:!0,defaultValue:!0}),s!==void 0&&!s&&e.jsx(mt,{name:"configuration.services.ssh.authorized-keys",label:"authorized-keys",definitionKey:"service.ssh.authorized-keys",w:280,isRequired:!0})]})]})})},zl=G.memo(ql),Vl=()=>e.jsx(U,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsx(F,{children:e.jsx(le,{name:"configuration.services.mdns.enable",definitionKey:"service.mdns.enable",label:"MDNS"})})}),Ql=G.memo(Vl),Gl=()=>e.jsx(U,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsx(F,{children:e.jsx(le,{name:"configuration.services.igmp.enable",label:"IGMP",definitionKey:"service.igmp.enable"})})}),Hl=G.memo(Gl),Kl=()=>{const{value:t}=se({name:"configuration.services.lldp.enabled"}),{value:a}=se({name:"configuration.services.lldp"}),{t:s}=K(),{values:n,setFieldValue:i,errors:r}=ye();return o.useEffect(()=>{if(t){if(!a||a&&Object.keys(a).length<=1&&a.enabled===!0){const l=Me(s,"lldp");i("configuration.services.lldp",{...l,describe:"auto",location:"auto",enabled:!0})}}else i("configuration.services.lldp",void 0)},[t]),e.jsx(e.Fragment,{children:e.jsx(U,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.lldp.enabled",label:"LLDP"}),t&&e.jsxs(e.Fragment,{children:[e.jsx(Le,{name:"configuration.services.lldp.describe",label:"Describe",definitionKey:"service.lldp.describe",isRequired:!0,w:280}),e.jsx(Le,{name:"configuration.services.lldp.location",label:"Location",definitionKey:"service.lldp.location",isRequired:!0,w:280})]})]})})})},$l=G.memo(Kl),Wl=()=>{const{value:t}=se({name:"configuration.services.ntp.enabled"}),{value:a}=se({name:"configuration.services.ntp"}),{t:s}=K(),{values:n,setFieldValue:i,errors:r}=ye();return o.useEffect(()=>{if(t){if(!a||a&&Object.keys(a).length<=1&&a.enabled===!0){const l=Me(s,"ntp");i("configuration.services.ntp",{...l,enabled:!0})}}else i("configuration.services.ntp",void 0)},[t]),e.jsx(U,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.ntp.enabled",label:"NTP"}),t&&e.jsxs(e.Fragment,{children:[e.jsx(mt,{name:"configuration.services.ntp.servers",label:"Servers",definitionKey:"service.ntp.servers",isRequired:!0,w:280}),e.jsx(le,{name:"configuration.services.ntp.local-server",label:"Local-Server",definitionKey:"service.ntp.local-server",isRequired:!0})]})]})})},Jl=G.memo(Wl),Xl=()=>{const{value:t}=se({name:"configuration.services.log.enabled"}),{value:a}=se({name:"configuration.services.log"}),{t:s}=K(),{values:n,setFieldValue:i,errors:r}=ye();return o.useEffect(()=>{if(t){if(!a||a&&Object.keys(a).length<=1&&a.enabled===!0){const l=Me(s,"log");i("configuration.services.log",{...l,enabled:!0})}}else i("configuration.services.log",void 0)},[t]),e.jsx(U,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.log.enabled",label:"Log"}),t&&e.jsxs(e.Fragment,{children:[e.jsx(Le,{name:"configuration.services.log.host",label:"Host",definitionKey:"service.log.host",isRequired:!0,w:280}),e.jsx(xe,{name:"configuration.services.log.port",label:"Port",definitionKey:"service.log.port",isRequired:!0,w:140}),e.jsx(ft,{name:"configuration.services.log.proto",label:"Proto",definitionKey:"service.log.proto",isRequired:!0,w:280,options:[{value:"udp",label:"udp"},{value:"tcp",label:"tcp"}]}),e.jsx(xe,{name:"configuration.services.log.size",label:"Size",definitionKey:"service.log.size",isRequired:!0,w:140})]})]})})},Zl=G.memo(Xl),Yl=()=>{const{value:t}=se({name:"configuration.services.rtty.enabled"}),{value:a}=se({name:"configuration.services.rtty"}),{t:s}=K(),{values:n,setFieldValue:i,errors:r}=ye();return console.log("rtty values",n),o.useEffect(()=>{if(t){if(!a||a&&Object.keys(a).length<=1&&a.enabled===!0){const l=Me(s,"rtty");i("configuration.services.rtty",{...l,enabled:!0})}}else i("configuration.services.rtty",void 0)},[t]),e.jsx(U,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.rtty.enabled",label:"RTTY"}),t&&e.jsxs(e.Fragment,{children:[e.jsx(Le,{name:"configuration.services.rtty.host",label:"Host",definitionKey:"service.rtty.host",isRequired:!0,w:280}),e.jsx(xe,{name:"configuration.services.rtty.port",label:"Port",definitionKey:"service.rtty.port",isRequired:!0,w:140}),e.jsx(Le,{name:"configuration.services.rtty.token",label:"Token",definitionKey:"service.rtty.token",isRequired:!0,w:280})]})]})})},eo=G.memo(Yl),to=()=>{const{value:t}=se({name:"configuration.services.wifi-steering.enabled"}),{value:a}=se({name:"configuration.services.wifi-steering"}),{t:s}=K(),{values:n,setFieldValue:i,errors:r}=ye();return o.useEffect(()=>{if(t){if(!a||a&&Object.keys(a).length<=1&&a.enabled===!0){const l=Me(s,"wifi-steering");i("configuration.services.wifi-steering",{...l,enabled:!0})}}else i("configuration.services.wifi-steering",void 0)},[t]),e.jsx(U,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.wifi-steering.enabled",label:"Wifi Steering"}),t&&e.jsxs(e.Fragment,{children:[e.jsx(le,{name:"configuration.services.wifi-steering.assoc-steering",label:"Assoc-Steering",definitionKey:"service.wifi-steering.assoc-steering",isRequired:!0}),e.jsx(le,{name:"configuration.services.wifi-steering.auto-channel",label:"Auto-Channel",definitionKey:"service.wifi-steering.auto-channel",isRequired:!0}),e.jsx(xe,{name:"configuration.services.wifi-steering.required-probe-snr",label:"Required-Probe-Snr",definitionKey:"service.wifi-steering.required-probe-snr",isRequired:!0,w:140}),e.jsx(xe,{name:"configuration.services.wifi-steering.required-roam-snr",label:"Required-Roam-Snr",definitionKey:"service.wifi-steering.required-roam-snr",isRequired:!0,w:140}),e.jsx(xe,{name:"configuration.services.wifi-steering.load-kick-threshold",label:"load-kick-threshold",definitionKey:"service.wifi-steering.load-kick-threshold",isRequired:!0,w:140})]})]})})},ao=G.memo(to),so={value:P.arrayOf(P.oneOfType([P.string,P.number])),label:P.string.isRequired,onChange:P.func.isRequired,options:P.arrayOf(P.shape({label:P.string.isRequired,value:P.oneOfType([P.string,P.number]).isRequired})).isRequired,onBlur:P.func.isRequired,error:P.oneOfType([P.string,P.bool]),touched:P.bool,isDisabled:P.bool,canSelectAll:P.bool,isRequired:P.bool,isHidden:P.bool,isPortal:P.bool.isRequired,definitionKey:P.string,w:P.oneOfType([P.string,P.number]),placeholder:P.string},ss=({options:t,label:a,value:s=[],onChange:n,onBlur:i,error:r=!1,touched:l=!1,canSelectAll:d=!1,isRequired:f=!1,isDisabled:c=!1,isHidden:x=!1,isPortal:g=!1,definitionKey:h=null,w:j,placeholder:A})=>{const{t:u}=K(),p=d?[{value:"*",label:u("common.all")},...t]:t;return e.jsx(k.Item,{label:!x&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:a}),e.jsx(we,{definitionKey:h}),f&&e.jsx("span",{style:{color:"#ff4d4f",fontFamily:"SimSun, sans-serif",marginLeft:"2px",marginRight:"4px",display:"inline-block"},children:"*"})]}),validateStatus:r&&l?"error":"",help:r&&l?r:null,hidden:x,children:e.jsx(Q,{mode:"multiple",allowClear:!0,placeholder:A,value:s??[],options:p,onChange:n,onBlur:i,disabled:c,getPopupContainer:v=>v.parentElement||document.body,style:{width:typeof j=="number"?`${j}px`:j||"100%"}})})};ss.propTypes=so;const no=G.memo(ss,Bt),io={name:P.string.isRequired,label:P.string.isRequired,options:P.arrayOf(P.shape({label:P.string.isRequired,value:P.string.isRequired})).isRequired,isDisabled:P.bool,isRequired:P.bool,isHidden:P.bool,emptyIsUndefined:P.bool,hasVirtualAll:P.bool,canSelectAll:P.bool,isPortal:P.bool,definitionKey:P.string,placeholder:P.string},ns=({options:t,name:a,isDisabled:s=!1,label:n,isRequired:i=!1,isHidden:r=!1,emptyIsUndefined:l=!1,canSelectAll:d=!1,hasVirtualAll:f=!1,isPortal:c=!1,definitionKey:x=null,w:g,placeholder:h})=>{const[{value:j},{touched:A,error:u},{setValue:p,setTouched:v}]=vt(a),b=o.useCallback(I=>{I.length===0&&l?p(void 0):I.includes("*")?p(f?t.map(S=>S.value):["*"]):p(I),v(!0)},[l,f,t]),m=o.useCallback(()=>{v(!0)},[]);return e.jsx(no,{canSelectAll:d,label:n,value:j,onChange:b,onBlur:m,error:u,touched:A,options:t,isDisabled:s,isRequired:i,isHidden:r,isPortal:c,definitionKey:x,w:g,placeholder:h})};ns.propTypes=io;const ro=G.memo(ns),lo=()=>{const{value:t}=se({name:"configuration.services.online-check.enabled"}),{t:a}=K(),{value:s}=se({name:"configuration.services.online-check"}),{values:n,setFieldValue:i,errors:r}=ye();return o.useEffect(()=>{if(t){if(!s||s&&Object.keys(s).length<=1&&s.enabled===!0){const l=Me(a,"online-check");i("configuration.services.online-check",{...l,enabled:!0})}}else i("configuration.services.online-check",void 0)},[t]),e.jsx(U,{gutter:[20,0],style:{marginBottom:0,width:"100%"},children:e.jsxs(F,{children:[e.jsx(le,{name:"configuration.services.online-check.enabled",label:"Online Check"}),t&&e.jsxs(e.Fragment,{children:[e.jsx(ro,{name:"configuration.services.online-check.action",label:"Action",definitionKey:"service.online-check.action",w:280,isRequired:!0,placeholder:a("common.select"),options:[{value:"wifi",label:"wifi"},{value:"leds",label:"leds"}]}),e.jsx(mt,{name:"configuration.services.online-check.ping-hosts",label:"Ping-Hosts",definitionKey:"service.online-check.ping-hosts",isRequired:!0,w:280}),e.jsx(mt,{name:"configuration.services.online-check.download-hosts",label:"Download-Hosts",definitionKey:"service.online-check.download-hosts",isRequired:!0,w:280}),e.jsx(xe,{name:"configuration.services.online-check.check-interval",label:"Check-Interval",definitionKey:"service.online-check.check-interval",isRequired:!0,w:140}),e.jsx(xe,{name:"configuration.services.online-check.check-threshold",label:"Check-Threshold",definitionKey:"service.online-check.check-threshold",isRequired:!0,w:140})]})]})})},oo=G.memo(lo),co=()=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{style:{display:"flex"},children:[e.jsx(zl,{}),e.jsx(oe,{type:"vertical",style:{height:"auto",margin:"0 90px"}}),e.jsx(Ul,{})]}),e.jsxs("div",{style:{display:"flex"},children:[e.jsx(Ql,{}),e.jsx(oe,{type:"vertical",style:{height:"auto",margin:"0 90px"}}),e.jsx(Hl,{})]}),e.jsxs("div",{style:{display:"flex"},children:[e.jsx($l,{}),e.jsx(oe,{type:"vertical",style:{height:"auto",margin:"0 90px"}}),e.jsx(Jl,{})]}),e.jsxs("div",{style:{display:"flex"},children:[e.jsx(Zl,{}),e.jsx(oe,{type:"vertical",style:{height:"auto",margin:"0 90px"}}),e.jsx(eo,{})]}),e.jsxs("div",{style:{display:"flex"},children:[e.jsx(ao,{}),e.jsx(oe,{type:"vertical",style:{height:"auto",margin:"0 90px"}}),e.jsx(oo,{})]})]}),uo=()=>{const{value:t}=se({name:"configuration['IP address'].vlan.enabled"}),{value:a}=se({name:"configuration['IP address'].vlan"}),{value:s}=se({name:"configuration['IP address'].ipv6.enabled"}),{value:n}=se({name:"configuration['IP address'].ipv6"}),{values:i,setFieldValue:r,errors:l}=ye();return o.useEffect(()=>{t?((!a||a&&Object.keys(a).length<=1&&a.enabled===!0)&&r("configuration['IP address'].vlan.id",1080),r("configuration['IP address'].vlan.enabled",!0)):r("configuration['IP address'].vlan",void 0)},[t]),o.useEffect(()=>{s?((!n||n&&Object.keys(n).length<=1&&n.enabled===!0)&&r("configuration['IP address'].ipv6.addressing","dynamic"),r("configuration['IP address'].ipv6.enabled",!0)):r("configuration['IP address'].ipv6",void 0)},[s]),e.jsxs(e.Fragment,{children:[e.jsxs("div",{style:{display:"flex"},children:[e.jsx(U,{gutter:[20,0],style:{marginBottom:0,marginTop:0,width:"100%"},children:e.jsx(F,{children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:2,width:"180px"},children:[e.jsx("span",{children:"IPv4"}),e.jsx(Ce,{style:{marginLeft:"160px"},name:"configuration['IP address'].ipv4.addressing",defaultChecked:!0,disabled:!1,children:"Dynamic"})]})})}),e.jsx(U,{gutter:[20,20],style:{marginBottom:0,marginTop:-6,width:"100%"},children:e.jsx(F,{children:e.jsx(le,{name:"configuration['IP address'].ipv6.enabled",label:"IPv6",definitionKey:"interface.ipv6.port-forward.protocol"})})})]}),e.jsx(le,{name:"configuration['IP address'].vlan.enabled",label:"VLAN",definitionKey:"interface.vlan"}),t&&e.jsx(xe,{name:"configuration['IP address'].vlan.id",label:" ",w:140})]})},is=(t,a=!1)=>a?H().shape({enabled:z().default(!1),interval:W().required(t("form.required")).moreThan(59).lessThan(1e3).default(60)}):H().shape({enabled:z().default(!1),interval:W().required(t("form.required")).moreThan(59).lessThan(1e3).default(60)}).nullable().default(void 0),rs=(t,a=!1)=>a?H().shape({enabled:z().default(!1),types:Y().of(B()).required(t("form.required")).min(1,t("form.required")).default([])}):H().shape({enabled:z().default(!1),types:Y().of(B()).required(t("form.required")).min(1,t("form.required")).default([])}).nullable().default(void 0),ls=(t,a=!1)=>a?H().shape({enabled:z().default(!1),interval:W().required(t("form.required")).moreThan(59).lessThan(1e3).default(60),types:Y().of(B()).required(t("form.required")).min(1,t("form.required")).default([])}):H().shape({enabled:z().default(!1),interval:W().required(t("form.required")).moreThan(59).lessThan(1e3).default(60),types:Y().of(B()).required(t("form.required")).min(1,t("form.required")).default([])}).nullable().default(void 0),os=(t,a=!1)=>a?H().shape({enabled:z().default(!0),interval:W().required(t("form.required")).moreThan(59).lessThan(1e3).default(60),types:Y().of(B()).required(t("form.required")).min(1,t("form.required")).default(["ssids","lldp","clients"])}):H().shape({enabled:z().default(!0),interval:W().required(t("form.required")).moreThan(59).lessThan(1e3).default(60),types:Y().of(B()).required(t("form.required")).min(1,t("form.required")).default(["ssids","lldp","clients"])}).nullable().default(void 0),ds=(t,a=!1)=>a?H().shape({enabled:z().default(!1),interval:W().required(t("form.required")).moreThan(59).lessThan(1e3).default(60),"dhcp-local":z().default(!0),"dhcp-remote":z().default(!1),"dns-local":z().default(!0),"dns-remote":z().default(!0)}):H().shape({interval:W().required(t("form.required")).moreThan(59).lessThan(1e3).default(60),"dhcp-local":z().default(!0),"dhcp-remote":z().default(!1),"dns-local":z().default(!0),"dns-remote":z().default(!0)}).nullable().default(void 0),cs=(t,a=!1)=>a?H().shape({enabled:z().default(!1),filters:Y().of(B()).required(t("form.required")).min(1,t("form.required")).default([])}):H().shape({enabled:z().default(!1),filters:Y().of(B()).required(t("form.required")).min(1,t("form.required")).default([])}).nullable().default(void 0),us=(t,a=!1)=>a?H().shape({enabled:z().default(!1),filters:Y().of(B()).required(t("form.required")).min(1,t("form.required")).default([])}):H().shape({enabled:z().default(!1),filters:Y().of(B()).required(t("form.required")).min(1,t("form.required")).default([])}).nullable().default(void 0),ho=(t,a=!1)=>H().shape({configuration:H().shape({metrics:H().shape({statistics:os(t,a),health:ds(t,a),"wifi-frames":cs(t,a),"dhcp-snooping":us(t,a),realtime:rs(t,a),telemetry:ls(t,a),"wifi-scan":is(t,a)}).default(()=>({statistics:fo(t,"statistics")}))})}),fo=(t,a)=>{switch(a){case"statistics":return os(t,!0).cast();case"health":return ds(t,!0).cast();case"wifi-frames":return cs(t,!0).cast();case"dhcp-snooping":return us(t,!0).cast();case"telemetry":return ls(t,!0).cast();case"realtime":return rs(t,!0).cast();case"wifi-scan":return is(t,!0).cast();default:return null}},mo=t=>H().shape({configuration:H().shape({unit:po()})}),po=t=>H().shape({hostname_enable:z().default(!1),timezone:B().default(void 0),"leds-active":z().default(!0),"random-password":z().default(!1)}),go=(t,a=!1,s="")=>H().shape({configuration:H().shape({"IP address":xo(t,a)})}),xo=(t,a=!1,s="")=>{const n=H().shape({ipv4:H().shape({addressing:B()}).default({addressing:"dynamic"}),ipv6:H().shape({enabled:z().default(!1),addressing:B().default("dynamic")}),vlan:H().shape({enabled:z().default(!1),id:W().required(t("form.required")).moreThan(0).lessThan(4050).default(1080)}).nullable()});return a?n:n.nullable().default({vlan:!1})},bo=o.forwardRef(({onDirtyChange:t},a)=>{const{t:s}=K(),[n,i]=o.useState(null),r=o.useRef(null),[l,d]=o.useState(!1);o.useEffect(()=>{const C=location.hash.replace("#",""),w=Number(C);w!==n&&(i(w),d(!1),b({}),r.current&&r.current.resetForm())},[location.hash]);const f=["system","ethernet","services","metrics","DHCP Service","IP address"],[c,x]=o.useState([]),[g,h]=o.useState(null),j=n!=null?`${n}-advance`:void 0,{data:A=[],refetch:u,isLoading:p}=Qa({id:j}),[v,b]=o.useState({}),m=o.useMemo(()=>!g||!r.current?!1:l||Object.values(v).some(C=>C),[l,v,g]);o.useEffect(()=>{t&&t(m)},[m,t]),o.useEffect(()=>{n&&u()},[n,u]),o.useEffect(()=>{d(!1),b({})},[n]),o.useEffect(()=>{x(f)},[]),o.useEffect(()=>{const C=A==null?void 0:A.configuration;if(!C||!Array.isArray(C))return;const w={configuration:{}};for(const E of C){const T=JSON.parse(E.configuration||"{}");E.name==="System"&&T.unit&&(w.configuration.unit=T.unit),E.name==="Services"&&T.services&&(w.configuration.services=T.services,["ssh","http","lldp","ntp","log","rtty","wifi-steering","online-check"].forEach(N=>{var M;((M=w.configuration.services)==null?void 0:M[N])!==void 0&&(w.configuration.services[N]={...w.configuration.services[N],enabled:!0})})),E.name==="Metrics"&&T.metrics&&(w.configuration.metrics=T.metrics),E.name==="IP address"&&T["IP address"]&&(w.configuration["IP address"]=T["IP address"],["vlan","ipv6"].forEach(N=>{var M;((M=w.configuration["IP address"])==null?void 0:M[N])!==void 0&&(w.configuration["IP address"][N]={...w.configuration["IP address"][N],enabled:!0})}))}h(w),r.current&&r.current.resetForm({values:w}),d(!1),b({})},[A==null?void 0:A.configuration]);const I=o.useMemo(()=>{let C=mo();return C=C.concat(Rl(s)).concat(ho(s)).concat(go(s)),C},[s]);if(!g)return null;const S=[{key:"1",label:e.jsx("span",{className:"collapse-title",children:"System"}),children:e.jsx(fl,{editing:!0})},{key:"2",label:e.jsxs("div",{children:[e.jsx("span",{className:"collapse-title",children:"IP Address"}),e.jsx(we,{definitionKey:"interface.ipAddress"})]}),children:e.jsx(uo,{})},{key:"3",label:e.jsxs("div",{children:[e.jsx("span",{className:"collapse-title",children:"DHCP Service"}),e.jsx(we,{definitionKey:"interface.DhcpService"})]}),children:e.jsx(ml,{})},{key:"4",label:e.jsx("span",{className:"collapse-title",children:"Manage Ethernet Ports"}),children:e.jsx(Ml,{})},{key:"5",label:e.jsx("span",{className:"collapse-title",children:"Services"}),children:e.jsx(co,{})}].filter(C=>C.key==="1"?c.includes("system"):C.key==="2"?c.includes("IP address"):C.key==="3"?c.includes("DHCP Service"):C.key==="4"?c.includes("ethernet"):C.key==="5"?c.includes("services"):C.key==="6"?c.includes("metrics"):!0),y=async C=>{const w=[],E=R=>{if(Array.isArray(R))return R.map(E);if(typeof R=="object"&&R!==null){if(Object.keys(R).length===1&&R.hasOwnProperty("enable")&&(R.enable===!1||R.enable==="false"))return;const O={};for(const q in R){if(q==="enabled")continue;const _=E(R[q]);_!==void 0&&(O[q]=_)}return O}return R},T=E(C.configuration.services),L=E(C.configuration.metrics),N=E(C.configuration["IP address"]);c.includes("services")&&C.configuration.services&&w.push({configuration:JSON.stringify({services:T}),name:"Services"}),c.includes("metrics")&&C.configuration.metrics&&w.push({configuration:JSON.stringify({metrics:L}),name:"Metrics"}),c.includes("system")&&C.configuration.unit&&w.push({configuration:JSON.stringify({unit:C.configuration.unit}),name:"System"}),c.includes("IP address")&&C.configuration["IP address"]&&w.push({configuration:JSON.stringify({"IP address":N}),name:"IP address"});try{await Va(j,n,JSON.stringify(w)),D.success("Successfully applied advance configuration"),d(!1),b({})}catch(R){console.error(R),D.error("Failed to apply settings.");return}(await u()).error&&D.warning("Settings saved, but failed to refresh config.")};return e.jsx("div",{style:{width:"100%",overflowX:"auto"},children:e.jsx(qt,{innerRef:r,initialValues:g,enableReinitialize:!0,onSubmit:()=>{},validationSchema:I,children:({resetForm:C,values:w,validateForm:E,setTouched:T,dirty:L})=>{o.useEffect(()=>{r.current&&g&&!Bs.isEqual(w,g)&&d(!0)},[w,g]);const N=M=>{const R={};for(const O in M)typeof M[O]=="object"&&M[O]!==null?R[O]=N(M[O]):R[O]=!0;return R};return o.useImperativeHandle(a,()=>({reset:()=>{C(),d(!1),b({}),D.info("Changes have been reset.")},apply:()=>{E().then(M=>{Object.keys(M).length===0?(y(w),b({})):(T(N(w)),D.error("Please correct the errors before saving"))})}})),e.jsx(e.Fragment,{children:e.jsx(_e,{size:"large",items:S,defaultActiveKey:[],expandIconPosition:"right",className:"no-collapse-border",style:{marginTop:12,marginBottom:0,border:"none",background:"#ffffff"}})})}})})});function vo(t){const a=Ls(t),s=o.useRef(!1);o.useEffect(()=>{a.state==="blocked"&&!s.current&&(s.current=!0,Ee("You have unsaved changes. If you leave or switch to another site, your changes will be lost. Do you want to continue?",()=>{a.proceed(),s.current=!1},()=>{a.reset(),s.current=!1}))},[a])}const ct="configurepage.tabIndex",ba=()=>{const t=localStorage.getItem(ct);try{if(t){const a=parseInt(t,10);if(a>=0&&a<=2)return a}return 0}catch{return 0}},yo=({id:t})=>{const[a,s]=o.useState(ba()),{colorMode:n}=qs(),r={textColor:n==="light"?"var(--chakra-colors-blue-600)":"var(--chakra-colors-blue-300)",fontWeight:"semibold",borderWidth:"0px",marginBottom:"-1px",borderBottom:"2px solid"},[l,d]=o.useState(!1),[f,c]=o.useState(!1),x=o.useRef(null),g=o.useRef(null),h=o.useRef(null);vo(l||f);const A=v=>{v!==a&&(l?(h.current=v,Ee("You have unsaved changes. If you leave or switch to another site, your changes will be lost. Do you want to continue?",()=>{var b;h.current!==null&&((b=x.current)==null||b.reset(),console.log("radios reset"),s(h.current),localStorage.setItem(ct,h.current.toString()),h.current=null)},()=>{h.current=null})):f?(h.current=v,Ee("You have unsaved changes. If you leave or switch to another site, your changes will be lost. Do you want to continue?",()=>{var b;h.current!==null&&((b=g.current)==null||b.reset(),console.log("advance reset"),s(h.current),localStorage.setItem(ct,h.current.toString()),h.current=null)},()=>{h.current=null})):(s(v),localStorage.setItem(ct,v.toString())))};o.useEffect(()=>{s(ba())},[t]);const u=a===1||a===2,p=a===1&&!l||a===2&&!f;return e.jsxs(zs,{direction:"column",minH:"100%",children:[e.jsx(aa,{flex:"1",children:e.jsxs(Oa,{index:a,isManual:!0,children:[e.jsxs(Na,{children:[e.jsx(ot,{_selected:r,onClick:()=>A(0),children:"SSID Configuration"}),e.jsx(ot,{_selected:r,onClick:()=>A(1),children:"Radio Configuration"}),e.jsx(ot,{_selected:r,onClick:()=>A(2),children:"Advanced Configuration"})]}),e.jsxs(Ba,{children:[e.jsx(dt,{px:0,children:e.jsx(sl,{})}),e.jsx(dt,{px:0,children:e.jsx(Ar,{ref:x,onDirtyChange:d})}),e.jsx(dt,{px:0,children:e.jsx(bo,{ref:g,onDirtyChange:c})})]})]})}),u&&e.jsxs(aa,{position:"sticky",bottom:0,bg:"white",py:4,display:"flex",justifyContent:"flex-end",zIndex:1,sx:{_before:{content:'""',position:"absolute",top:0,left:"50%",transform:"translateX(-50%)",width:"calc(100% + 48px)",height:"1px",backgroundColor:"#E2E8F0"}},children:[e.jsx(Z,{onClick:()=>{var v,b;a===1&&((v=x.current)==null||v.reset()),a===2&&((b=g.current)==null||b.reset())},style:{marginRight:16,width:100},disabled:p,children:"Cancel"}),e.jsx(Z,{onClick:()=>{var v,b;a===1&&((v=x.current)==null||v.apply()),a===2&&((b=g.current)==null||b.apply())},type:"primary",style:{width:100},disabled:p,children:"Apply"})]})]})},jo=()=>e.jsx(yo,{id:"1"}),Co=({visible:t,onClose:a,config:s,onApply:n,loading:i=!1})=>{const[r]=k.useForm();o.useEffect(()=>{t&&(s.enableChannelOptimization=s.channelMode==="unmanaged_aware",s.enableTxPowerOptimization=s.txPowerMode==="measure_ap_ap",(s.coverageThreshold===void 0||s.coverageThreshold===null)&&(s.coverageThreshold=-70),r.setFieldsValue(s))},[t,s,r]);const l=async f=>{const c={...f};if(c.enableChannelOptimization&&(c.channelMode="unmanaged_aware"),c.enableTxPowerOptimization&&(c.txPowerMode="measure_ap_ap",c.nthSmallestRssi=0),!c.channelMode&&!c.txPowerMode)return D.error("Please enable at least one optimization method");await n(c)},d=()=>{r.resetFields(),a()};return e.jsxs(Qt,{title:"Optimization Config",open:t,onCancel:d,onFinish:f=>l(f),form:r,initialValues:s,modalClass:"ampcon-middle-modal",children:[e.jsx("style",{children:`
          .coverage-threshold-form-item .ant-form-item-explain {
            margin-left: 50px;
          }
        `}),e.jsxs("div",{style:{display:"flex",alignItems:"center",marginTop:"10px"},children:[e.jsx("span",{children:"Automatic Channel Optimization"}),e.jsx(k.Item,{style:{marginBottom:0,marginLeft:"50px"},name:"enableChannelOptimization",valuePropName:"checked",children:e.jsx(te,{})})]}),e.jsxs("div",{children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",marginTop:"16px"},children:[e.jsx("span",{children:"Optimize TX Power Configuration"}),e.jsx(k.Item,{style:{marginBottom:0,marginLeft:"44.5px"},name:"enableTxPowerOptimization",valuePropName:"checked",children:e.jsx(te,{})})]}),e.jsx(k.Item,{noStyle:!0,shouldUpdate:(f,c)=>f.txPowerMode!==c.txPowerMode||f.enableTxPowerOptimization!==c.enableTxPowerOptimization,children:()=>e.jsx(e.Fragment,{children:r.getFieldValue("enableTxPowerOptimization")&&e.jsx("div",{children:e.jsxs("div",{style:{display:"flex",alignItems:"center",marginTop:"20px"},children:[e.jsx("span",{style:{position:"relative"}}),e.jsx(k.Item,{label:"Coverage Threshold",name:"coverageThreshold",rules:[{required:!0,message:""},{validator:(f,c)=>c==null||c===""?Promise.reject(new Error("Coverage Threshold Rssi is required")):c<-80||c>-60?Promise.reject(new Error("values:  -80 ～ -60")):Promise.resolve()}],style:{marginBottom:0,marginLeft:"0px",minHeight:"35px"},className:"coverage-threshold-form-item",children:e.jsx(ne,{style:{width:"280px",marginLeft:"50px"}})})]})})})})]})]})},wo=({visible:t,onOk:a,onCancel:s,confirmLoading:n})=>(t&&Ee("Channel switching may occur during optimization, which may lead to user disconnection. The whole process is estimated to take 10 minutes. You are advised to avoid peak hours. Are you sure you want to start?",a,s),null),So=[{label:"Monday",value:"1"},{label:"Tuesday",value:"2"},{label:"Wednesday",value:"3"},{label:"Thursday",value:"4"},{label:"Friday",value:"5"},{label:"Saturday",value:"6"},{label:"Sunday",value:"0"}],To=({visible:t,onClose:a,config:s,onApply:n})=>{const[i]=k.useForm();o.useEffect(()=>{t&&i.setFieldsValue({...s,executeTime:s.executeTime?ut(s.executeTime,"HH:mm"):null})},[t,s,i]);const r=async()=>{try{const l=await i.validateFields();await n({...l,executeTime:l.executeTime?l.executeTime.format("HH:mm"):""})!==!1&&a()}catch{D.error("Failed to save the scheduled task")}};return e.jsxs(Ie,{open:t,onCancel:a,onOk:r,okText:"Apply",cancelText:"Cancel",width:680,destroyOnClose:!0,className:"scheduler-modal",title:e.jsx("span",{className:"scheduler-title",children:"Optimization Scheduler"}),children:[e.jsx(oe,{style:{margin:"-10px 0px 0px 0px"}}),e.jsx(k,{form:i,layout:"vertical",initialValues:{...s,executeTime:s.executeTime?ut(s.executeTime,"HH:mm"):null},children:e.jsxs("div",{className:"scheduler-modal-body-row",children:[e.jsxs("div",{className:"scheduler-row scheduler-row-switch",children:[e.jsx("span",{className:"scheduler-label",children:"Scheduled Optimization"}),e.jsx(k.Item,{name:"enabled",valuePropName:"checked",noStyle:!0,children:e.jsx(te,{})})]}),e.jsx(k.Item,{noStyle:!0,shouldUpdate:(l,d)=>l.enabled!==d.enabled,children:()=>i.getFieldValue("enabled")&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"scheduler-row scheduler-row-time",children:[e.jsx("span",{className:"scheduler-label",children:"Network Segment Execution Time"}),e.jsx(k.Item,{name:"executeTime",noStyle:!0,children:e.jsx(tn,{format:"HH:mm",allowClear:!1,getPopupContainer:l=>l.parentElement,minuteStep:1})})]}),e.jsx(k.Item,{name:"days",noStyle:!0,children:e.jsx(Nt.Group,{options:So,className:"scheduler-week-group"})})]})})]})}),e.jsx(oe,{style:{margin:"0px 0px 0px 0px"}})]})},hs="/ampcon/wireless";async function Ao(t){return de({url:`${hs}/rrm/task/record`,method:"GET",params:{siteId:t.siteId,sortBy:t.sortBy||"create_time",sortType:t.sortType||"desc",pageNum:t.pageNum,pageSize:t.pageSize}})}async function ko(t){const a=await de({url:`${hs}/rrm/task/result`,method:"GET",params:{siteId:t.siteId,taskId:t.taskId,sortBy:t.sortBy,sortType:t.sortType,pageNum:t.pageNum,pageSize:t.pageSize}});return a&&Array.isArray(a.info)&&(a.info=a.info.map(s=>({...s,device_name:typeof s.device_name=="string"?s.device_name:"",device_mode:typeof s.device_mode=="string"?s.device_mode:""}))),a}async function Io(t){return de({url:"/smb/owrrm/api/v1/startRRM",method:"POST",data:t})}const Eo=[{title:"Device Name",dataIndex:"device_name",key:"device_name",width:200},{title:"Device SN",dataIndex:"sn",key:"sn",width:220},{title:"Device Mode",dataIndex:"device_mode",key:"device_mode",width:180}],Fo=({visible:t,historyId:a,onClose:s})=>{const[n,i]=o.useState([]),[r,l]=o.useState(!1),[d,f]=o.useState({current:1,pageSize:10,total:0});o.useRef(null);const[c,x]=o.useState(!1),[g,h]=o.useState({}),j=window.location.hash?window.location.hash.replace("#",""):"",A=(p=1,v=10,b,m)=>{l(!0),ko({siteId:j,taskId:a,sortBy:b,sortType:m==="ascend"?"asc":"desc",pageNum:p,pageSize:v}).then(I=>{i(I.info||[]),f(S=>({...S,current:p,pageSize:v,total:I.total||0}))}).finally(()=>l(!1))};o.useEffect(()=>{t&&a&&A(1,d.pageSize)},[t,a]);const u=(p,v,b)=>{let m=b.field,I=b.order;h({field:m,order:I}),A(p.current||1,p.pageSize||10,m,I)};return e.jsx(Ie,{title:"Failed Device List",open:t,onCancel:s,footer:null,width:1360,style:{minHeight:"500px",maxHeight:"calc(100vh - 100px)"},destroyOnClose:!0,className:"failed-device-modal",wrapClassName:"failed-device-modal-wrapper",children:e.jsxs("div",{className:"failed-device-modal-content",children:[e.jsx("div",{className:"table-container",children:e.jsx(et,{rowKey:"id",loading:r,dataSource:n,pagination:!1,columns:Eo,size:"middle",bordered:!0,showSorterTooltip:!1,onChange:u,scroll:{y:300,scrollToFirstRowOnChange:!1}})}),d.total>fs&&e.jsx("div",{className:"pagination-container",children:e.jsx(Ia,{current:d.current,pageSize:d.pageSize,total:d.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(p,v)=>`${v[0]}-${v[1]} of ${p} items`,onChange:(p,v)=>A(p,v,g.field,g.order),onShowSizeChange:(p,v)=>A(1,v,g.field,g.order),style:{marginTop:"16px",textAlign:"right"},pageSizeOptions:["10","20","50","100"]})})]})})},Po=t=>[{title:e.jsx(ue,{children:"Trigger Time"}),dataIndex:"trigger_time",key:"trigger_time",sorter:!0,multiple:3,width:"25%",defaultSortOrder:"descend"},{title:e.jsx(ue,{children:"Update Time"}),dataIndex:"modified_time",key:"modified_time",sorter:!0,multiple:2,width:"25%"},{title:e.jsx(ue,{children:"Schedule Task"}),dataIndex:"is_schedule_task",key:"is_schedule_task",sorter:!0,multiple:1,width:"25%",render:a=>a===2?"Yes":"No"},{title:e.jsx(ue,{children:"Result"}),key:"result",width:"25%",render:(a,s)=>{const{online_num:n,success_num:i,failed_num:r,id:l}=s;return n===null||i===null||r===null?e.jsx("span",{children:"task running"}):e.jsxs("span",{children:["Online Device:",n||0,",Succeed:",i||0,",",r>0?e.jsxs("a",{className:"history-failed-link",onClick:()=>t(l),children:["Failed:",r]}):e.jsxs("span",{children:["Failed:",r||0]})]})}}],Mo=({data:t,total:a,page:s,pageSize:n,onPageChange:i,onSortChange:r,onShowFailed:l})=>{const d=(f,c,x)=>{r&&r({field:x.field,order:x.order})};return e.jsxs("div",{children:[e.jsx(et,{bordered:!0,rowKey:"id",columns:Po(l),dataSource:t,pagination:!1,size:"middle",onChange:d,showSorterTooltip:!1}),a>0&&e.jsx(Ia,{total:a,current:s,pageSize:n,onChange:i,showSizeChanger:!0,showQuickJumper:!0,showTotal:(f,c)=>`${c[0]}-${c[1]} of ${f} items`})]})};function Ro(t){if(!t||t.trim()===""||t==="0 0 0 * * *")return t;const a=t.split(" ");let s=parseInt(a[1]||"0"),n=parseInt(a[2]||"0");const i=a[5]||"*";let r=n*60+s;const l=new Date().getTimezoneOffset();r+=l;let d=0;r<0?(r+=24*60,d=-1):r>=24*60&&(r-=24*60,d=1),n=Math.floor(r/60),s=r%60;let f=i;if(i!=="*"){const x=i.split(",").map(g=>parseInt(g)).map(g=>{let h=g+d;return h<0&&(h+=7),h>6&&(h-=7),h.toString()});f=[...new Set(x)].sort((g,h)=>parseInt(g)-parseInt(h)).join(",")}return`0 ${s} ${n} * * ${f}`}function _o(t){if(!t||t.trim()===""||t==="0 0 0 * * *")return t;const a=t.split(" ");let s=parseInt(a[1]||"0"),n=parseInt(a[2]||"0");const i=a[5]||"*";let r=n*60+s;const l=new Date().getTimezoneOffset();r-=l;let d=0;r<0?(r+=24*60,d=-1):r>=24*60&&(r-=24*60,d=1),n=Math.floor(r/60),s=r%60;let f=i;if(i!=="*"){const x=i.split(",").map(g=>parseInt(g)).map(g=>{let h=g+d;return h<0&&(h+=7),h>6&&(h-=7),h.toString()});f=[...new Set(x)].sort((g,h)=>parseInt(g)-parseInt(h)).join(",")}return`0 ${s} ${n} * * ${f}`}function Uo(t,a=!1){if(!t||t.trim()===""||t==="0 0 0 * * *")return{enabled:!1,executeTime:"00:00",days:["1","2","3","4","5","6","0"]};const n=(a?_o(t):t).split(" "),i=n[1]||"00",r=n[2]||"00",l=n[5]&&n[5]!=="*"?n[5].split(","):["1","2","3","4","5","6","0"];return{enabled:!0,executeTime:`${r.padStart(2,"0")}:${i.padStart(2,"0")}`,days:l}}function va(t,a=!1){if(!t.enabled)return"";const[s,n]=t.executeTime.split(":"),i=t.days&&t.days.length===7?"*":t.days.join(","),r=`0 ${n||"0"} ${s||"0"} * * ${i}`;return a?Ro(r):r}const fs=10,ya=()=>window.location.hash?window.location.hash.replace("#",""):"";function Do(t){try{return JSON.parse(t)}catch{return{algorithms:[],schedule:"",vendor:""}}}function Oo(t){const a={};return t.forEach(s=>{var n,i;if(s.name==="OptimizeTxPower"){const r=Object.fromEntries(s.parameters.split(",").map(l=>l.split("=")));a.txPowerMode=((n=r.mode)==null?void 0:n.replace(/"/g,""))||"measure_ap_ap",a.setDifferentTxPowerPerAp=r.setDifferentTxPowerPerAp==="true",a.targetMcs=r.targetMcs?Number(r.targetMcs):"8",a.coverageThreshold=r.coverageThreshold?Number(r.coverageThreshold):void 0,a.nthSmallestRssi=r.nthSmallestRssi?Number(r.nthSmallestRssi):void 0}if(s.name==="OptimizeChannel"){const r=Object.fromEntries(s.parameters.split(",").map(l=>l.split("=")));a.channelMode=((i=r.mode)==null?void 0:i.replace(/"/g,""))||"least_used",a.setDifferentChannelPerAp=r.setDifferentChannelPerAp==="true"}}),a.channelMode&&!a.txPowerMode?{channelMode:a.channelMode||"least_used",setDifferentChannelPerAp:a.setDifferentChannelPerAp??!1}:!a.channelMode&&a.txPowerMode?{txPowerMode:a.txPowerMode||"measure_ap_ap",setDifferentTxPowerPerAp:a.setDifferentTxPowerPerAp??!1,coverageThreshold:a.coverageThreshold,nthSmallestRssi:a.nthSmallestRssi,targetMcs:a.targetMcs}:{channelMode:a.channelMode||"least_used",setDifferentChannelPerAp:a.setDifferentChannelPerAp??!1,txPowerMode:a.txPowerMode||"measure_ap_ap",setDifferentTxPowerPerAp:a.setDifferentTxPowerPerAp??!1,coverageThreshold:a.coverageThreshold,nthSmallestRssi:a.nthSmallestRssi,targetMcs:a.targetMcs}}function ja(t){let a=`mode=${t.channelMode}`;t.channelMode==="random"&&(a+=`,setDifferentChannelPerAp=${t.setDifferentChannelPerAp}`);let s=`mode=${t.txPowerMode}`;return t.txPowerMode==="random"?s+=`,setDifferentTxPowerPerAp=${t.setDifferentTxPowerPerAp}`:t.txPowerMode==="measure_ap_client"?s+=`,targetMcs=${t.targetMcs}`:t.txPowerMode==="measure_ap_ap"?s+=`,coverageThreshold=${t.coverageThreshold},nthSmallestRssi=${t.nthSmallestRssi}`:t.txPowerMode,t.channelMode&&!t.txPowerMode?[{name:"OptimizeChannel",parameters:a}]:!t.channelMode&&t.txPowerMode?[{name:"OptimizeTxPower",parameters:s}]:[{name:"OptimizeChannel",parameters:a},{name:"OptimizeTxPower",parameters:s}]}function No(t){let a=`mode=${t.channelMode}`;t.channelMode==="random"&&(a+=`,setDifferentChannelPerAp=${t.setDifferentChannelPerAp}`);let s=`mode=${t.txPowerMode}`;return t.txPowerMode==="random"?s+=`,setDifferentTxPowerPerAp=${t.setDifferentTxPowerPerAp}`:t.txPowerMode==="measure_ap_client"?s+=`,targetMcs=${t.targetMcs}`:t.txPowerMode==="measure_ap_ap"?s+=`,coverageThreshold=${t.coverageThreshold},nthSmallestRssi=${t.nthSmallestRssi}`:t.txPowerMode,t.channelMode&&!t.txPowerMode?[{algorithm:"OptimizeChannel",args:a}]:!t.channelMode&&t.txPowerMode?[{algorithm:"OptimizeTxPower",args:s}]:[{algorithm:"OptimizeChannel",args:a},{algorithm:"OptimizeTxPower",args:s}]}const Bo=()=>{const{t}=K(),a=gt(),[s,n]=o.useState(!1),[i,r]=o.useState(!1),[l,d]=o.useState(!1),[f,c]=o.useState({visible:!1,historyId:null}),[x,g]=o.useState([]),[h,j]=o.useState(0),[A,u]=o.useState(1),[p,v]=o.useState(fs),[b,m]=o.useState(!1),[I,S]=o.useState("create_time"),[y,C]=o.useState("descend"),[w,E]=o.useState(()=>ya()),{data:T,refetch:L,isLoading:N}=qe({id:w}),M=Dt({id:w}),R=o.useCallback(async(ee=A,ge=p,Te=I,ze=y)=>{m(!0);try{const at=await Ao({siteId:w,sortBy:Te||"create_time",sortType:ze==="ascend"?"asc":"desc",pageNum:ee,pageSize:ge});g(at.info||[]),j(at.total||0),u(ee),v(ge)}catch{D.error("Failed to fetch optimization history")}finally{m(!1)}},[w,A,p,I,y]);o.useEffect(()=>{const ee=ya();ee!==w&&(console.log(`VenueId changed from ${w} to ${ee}`),E(ee))},[a.hash,w]),o.useEffect(()=>{w&&(console.log(`Loading history data for venueId: ${w}`),R(1,p))},[w,p,L]);const O=G.useMemo(()=>{var ee;return!((ee=T==null?void 0:T.deviceRules)!=null&&ee.rrm)||T.deviceRules.rrm==="inherit"||T.deviceRules.rrm==="off"?{algorithms:[],schedule:"",vendor:""}:Do(T.deviceRules.rrm)},[T]),q=G.useMemo(()=>Oo(O.algorithms||[]),[O]),_=G.useMemo(()=>Uo(O.schedule||"",!0),[O]),[ie,ce]=o.useState(q),[me,$]=o.useState(_),[V,re]=o.useState(O.algorithms||[]),[pe,St]=o.useState(O.schedule||""),[be,Ae]=o.useState(O.vendor||"Meta");o.useEffect(()=>{re(O.algorithms||[]),St(O.schedule||""),Ae(O.vendor||"Meta"),ce(q),$(_)},[O,q,_]);const Ne=async ee=>{const ge=ja(ee),Te=va(me,!0),ze=me.enabled?{algorithms:ge,schedule:Te,vendor:be}:{algorithms:ge,vendor:be};try{return await M.mutateAsync({params:{deviceRules:{...T.deviceRules,rrm:ze}}}),L(),D.success("save successfully"),n(!1),!0}catch{return D.error("Failed to save the configuration"),!1}},ms=async ee=>{const ge=ja(ie),Te=va(ee,!0),ze=ee.enabled?{algorithms:ge,schedule:Te,vendor:be}:{algorithms:ge,vendor:be};try{return ee.enabled&&(!ee.days||ee.days.length===0)?(D.error("Please select at least one week of execution!"),!1):(await M.mutateAsync({params:{deviceRules:{...T.deviceRules,rrm:ze}}}),L(),D.success("save successfully"),d(!1),!0)}catch{return D.error("Failed to save the configuration"),!1}},ps=ee=>{S(ee.field),C(ee.order),R(1,p,ee.field,ee.order)},gs=async()=>{r(!1);try{const ge={parameter:No(q),siteId:w},Te=await Io(ge);console.log("Optimization result:",Te),Te.status==300?D.error("Optimization in progress..."):Te.status==200?D.success("The task was submitted successfully"):D.error("Failed to submit the task")}catch(ee){console.error("Failed to start optimization:",ee)}finally{setTimeout(()=>{R(1,p)},2e3)}};return e.jsxs("div",{className:"rrm-optimize-root",children:[e.jsxs("div",{className:"rrm-optimize-header",children:[e.jsx("div",{children:e.jsx("h2",{children:e.jsx("strong",{children:"WLAN Optimization"})})}),e.jsxs("a",{href:"#",onClick:ee=>{ee.preventDefault(),n(!0)},children:[e.jsx("span",{children:"☰"})," Optimization Config"]})]}),e.jsx("div",{className:"rrm-optimize-desc",children:"With the WLAN optimization service, the organization will determine the optimum operation channels and power concluded from the scanning, considering the traffic, deployment size, and client factors."}),e.jsx(Ea,{className:"custom-trace-alert",message:"Note: The connection to internet will be lost for several minutes during the scanning and optimization. Please select a spare time of network to start scanning.",type:"info",showIcon:!0,closable:!0}),e.jsxs("div",{className:"rrm-optimize-actions",children:[e.jsx(Z,{type:"primary",onClick:()=>r(!0),children:"Optimization Now"}),e.jsx(Z,{type:"default",onClick:()=>d(!0),children:"Optimization Scheduler"})]}),e.jsx("hr",{}),e.jsxs("div",{className:"rrm-optimize-history",children:[e.jsx("h3",{children:"Optimization History"}),e.jsx(Mo,{data:x,total:h,page:A,pageSize:p,onPageChange:(ee,ge)=>R(ee,ge),onSortChange:ps,onShowFailed:ee=>c({visible:!0,historyId:ee})})]}),e.jsx(Co,{visible:s,onClose:()=>n(!1),config:ie,onApply:Ne}),e.jsx(To,{visible:l,onClose:()=>d(!1),config:me,onApply:ms}),e.jsx(wo,{visible:i,onOk:gs,onCancel:()=>r(!1)}),e.jsx(Fo,{visible:f.visible,historyId:f.historyId||"",onClose:()=>c({visible:!1,historyId:null})})]})},Lo=({venueId:t})=>{const{t:a}=K();return Oe(["get-analytics-boards",t],async()=>{const{data:s}=await Se.get(`boards?forVenue=${t}`);return s},{enabled:t!=null,onError:s=>{var n,i;D.error(a("crud.error_fetching_obj",{obj:a("analytics.board"),e:((i=(n=s.response)==null?void 0:n.data)==null?void 0:i.ErrorDescription)||a("common.error")}),5)}})},{TabPane:Pt}=Lt,qo=({visible:t,onClose:a,venueId:s})=>{const{t:n}=K(),i=Vs({id:s,enabled:t}),r=Qs(),[l,d]=o.useState();o.useEffect(()=>{t&&(d(void 0),i.refetch())},[t]);const f=()=>{l&&r.mutateAsync({revision:l,id:s},{onSuccess:a})},c=g=>e.jsx("ul",{style:{listStyle:"none",padding:0},children:g.sort((h,j)=>j.date-h.date).map(h=>e.jsxs("li",{onClick:()=>d(h.revision),style:{padding:8,border:"1px solid #eee",borderRadius:4,marginBottom:6,background:l===h.revision?"#f0f0f0":"#fff",cursor:"pointer"},children:[e.jsx(yt,{date:h.date}),e.jsx("div",{children:h.revision})]},h.revision))}),x=o.useMemo(()=>{if(i.isFetching)return e.jsx(ka,{});if(i.isError)return e.jsx(Ea,{message:"Error",description:"Failed to load firmware list",type:"error"});const g=i.data;return e.jsxs(e.Fragment,{children:[e.jsx(ve.Text,{strong:!0,children:n("venues.upgrade_options_available")}),e.jsxs(Lt,{children:[e.jsx(Pt,{tab:"Official Releases",children:c((g==null?void 0:g.releases)||[])},"official"),e.jsx(Pt,{tab:"Release Candidates",children:c((g==null?void 0:g.releasesCandidates)||[])},"rc"),e.jsx(Pt,{tab:"Dev Releases",children:c((g==null?void 0:g.developmentReleases)||[])},"dev")]})]})},[i,l]);return e.jsx(Ie,{title:n("venues.upgrade_all_devices"),visible:t,onCancel:a,onOk:f,okButtonProps:{disabled:!l,loading:r.isLoading},children:x})},zo=t=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:32,height:32,viewBox:"0 0 32 32",...t},o.createElement("g",null,o.createElement("g",null,o.createElement("rect",{x:0,y:0,width:32,height:32,rx:4,fill:"#FFFFFF",fillOpacity:1})),o.createElement("g",null,o.createElement("path",{d:"M8.999086984558105,25.333366984558104C8.376126984558105,25.333366984558104,7.7904469845581055,25.090766984558105,7.349697984558105,24.650366984558104C6.909259984558106,24.209766984558104,6.6666669845581055,23.623866984558106,6.6666669845581055,23.000866984558105C6.6666669845581055,22.377966984558107,6.909260984558106,21.792266984558104,7.349697984558105,21.351866984558107L14.557616984558106,14.143896984558104C13.803976984558105,12.127816984558105,14.288846984558106,9.819476984558106,15.823506984558106,8.284976984558105C16.866966984558104,7.241331984558105,18.255266984558105,6.6666669845581055,19.732366984558105,6.6666669845581055C20.532966984558108,6.6666669845581055,21.339066984558105,6.843613984558106,22.063366984558105,7.178302984558106C22.236466984558106,7.258188984558106,22.359466984558104,7.4176009845581055,22.392766984558108,7.605379984558105C22.426366984558108,7.792996984558106,22.366066984558106,7.985206984558106,22.231066984558105,8.119876984558106L19.783966984558106,10.566846984558106L20.058966984558104,11.941196984558106L21.433066984558106,12.216216984558105L23.859166984558104,9.790146984558106C23.994566984558105,9.653886984558106,24.187966984558106,9.593806984558105,24.378666984558105,9.629266984558106C24.567766984558105,9.664696984558105,24.727166984558107,9.790996984558106,24.804966984558106,9.967086984558104C25.735366984558105,12.076566984558106,25.284966984558107,14.491516984558105,23.657566984558105,16.118996984558105C22.613366984558105,17.163066984558107,21.224566984558106,17.738166984558106,19.747266984558106,17.738166984558106C19.105466984558106,17.738166984558106,18.479666984558108,17.629966984558106,17.881966984558105,17.416466984558106L10.648196984558105,24.649966984558105C10.207696984558105,25.090766984558105,9.622066984558106,25.333366984558104,8.999086984558105,25.333366984558104ZM19.732366984558105,7.832856984558106C18.566766984558107,7.832856984558106,17.471466984558106,8.286246984558105,16.648026984558108,9.109526984558105C15.359986984558105,10.397576984558105,15.009176984558106,12.377386984558106,15.775076984558105,14.035986984558106C15.877596984558105,14.257496984558106,15.830866984558105,14.519996984558105,15.658016984558106,14.692566984558106L8.174266984558106,22.176366984558108C7.954176984558106,22.396766984558106,7.832896984558106,22.689466984558106,7.832896984558106,23.000866984558105C7.832896984558106,23.312366984558107,7.954176984558106,23.605366984558106,8.174266984558106,23.825466984558105C8.394656984558106,24.045766984558107,8.687636984558106,24.167166984558104,8.999086984558105,24.167166984558104C9.310576984558105,24.167166984558104,9.603266984558106,24.045766984558107,9.823636984558105,23.825466984558105L17.328466984558105,16.320856984558105C17.498466984558107,16.150876984558103,17.754766984558106,16.102206984558105,17.975866984558106,16.199516984558105C18.536266984558104,16.446686984558106,19.132166984558104,16.571976984558106,19.747166984558106,16.571976984558106C20.913066984558107,16.571976984558106,22.008966984558107,16.118136984558106,22.832966984558105,15.294466984558106C23.903766984558107,14.223336984558106,24.328866984558104,12.719746984558105,24.014466984558105,11.283906984558104L22.036866984558106,13.261446984558106C21.898766984558108,13.399376984558106,21.701666984558106,13.459446984558106,21.510166984558104,13.421016984558104L19.448766984558105,13.008446984558105C19.218166984558106,12.962316984558106,19.037566984558104,12.781986984558106,18.991466984558105,12.551066984558105L18.579266984558103,10.489586984558105C18.540666984558108,10.298376984558105,18.600866984558106,10.100776984558106,18.738666984558105,9.963006984558106L20.749066984558105,7.952436984558106C20.416266984558106,7.8735669845581056,20.073766984558105,7.832856984558106,19.732366984558105,7.832856984558106Z",fill:"#929A9E",fillOpacity:1}),o.createElement("path",{d:"M10.789616984558105,24.791466984558106L10.789666984558107,24.791366984558106L17.934966984558105,17.646266984558103Q18.812266984558107,17.938166984558105,19.747266984558106,17.938166984558105Q22.121166984558105,17.938166984558105,23.798966984558106,16.260416984558105Q25.063366984558105,14.995996984558106,25.387066984558107,13.260466984558105Q25.710766984558106,11.525096984558106,24.987866984558107,9.886296984558106Q24.821166984558104,9.508706984558106,24.415266984558105,9.432626984558105Q24.007866984558106,9.356916984558104,23.717666984558104,9.648716984558106L21.367266984558107,11.999096984558104L20.228866984558106,11.771246984558106L20.001066984558108,10.632576984558106L22.372366984558106,8.261446984558106Q22.661666984558103,7.972686984558106,22.589666984558107,7.570450984558105Q22.518266984558103,7.168018984558105,22.147266984558108,6.996745984558105Q21.000066984558103,6.466666984558105,19.732366984558105,6.466666984558105Q17.358666984558106,6.466666984558105,15.682086984558106,8.143556984558106Q14.492496984558105,9.333016984558105,14.136886984558107,10.991926984558106Q13.796666984558104,12.579066984558105,14.326296984558105,14.092386984558106L7.208276984558106,21.210466984558103Q6.466666984558105,21.952066984558105,6.466666984558105,23.000866984558105Q6.466666984558105,24.049966984558104,7.208323984558105,24.791766984558105Q7.9503569845581055,25.533366984558107,8.999086984558105,25.533366984558107Q10.048146984558105,25.533366984558107,10.789616984558105,24.791466984558106ZM17.830066984558105,17.185566984558108L10.506786984558106,24.508566984558104L10.506736984558106,24.508666984558104Q9.882416984558105,25.133366984558105,8.999086984558105,25.133366984558105Q8.115966984558106,25.133366984558105,7.491071984558106,24.508866984558104Q6.866666984558106,23.884366984558106,6.866666984558106,23.000866984558105Q6.866666984558106,22.117666984558106,7.491118984558105,21.493266984558105L14.789996984558105,14.194366984558105L14.744956984558106,14.073866984558105Q14.198616984558106,12.612356984558105,14.528006984558106,11.075766984558104Q14.858856984558106,9.532356984558106,15.964916984558105,8.426406984558106Q17.524366984558107,6.866664984558105,19.732366984558105,6.866666984558106Q20.912066984558106,6.866666984558106,21.979466984558105,7.359858984558105Q22.160966984558108,7.443620984558105,22.195866984558105,7.6403099845581055Q22.231066984558105,7.8372969845581055,22.089766984558104,7.978316984558106L19.566766984558107,10.501116984558106L19.888966984558106,12.111146984558106L21.498766984558106,12.433336984558107L24.000566984558105,9.931566984558106Q24.142466984558105,9.788796984558106,24.342166984558105,9.825896984558106Q24.540366984558105,9.863046984558105,24.621966984558107,10.047886984558104Q25.294866984558105,11.573346984558105,24.993866984558107,13.187126984558105Q24.692866984558105,14.800796984558106,23.516066984558105,15.977576984558105Q21.955466984558107,17.538166984558103,19.747266984558106,17.538166984558103Q18.817266984558106,17.538166984558103,17.949266984558108,17.228166984558108L17.830066984558105,17.185566984558108ZM18.880066984558106,10.104436984558106L21.143866984558105,7.840466984558105L20.795166984558108,7.757826984558106Q20.267866984558104,7.632852984558106,19.732366984558105,7.632852984558106Q17.842066984558105,7.632853984558105,16.506626984558103,8.968096984558105Q15.496056984558106,9.978656984558105,15.244396984558106,11.398906984558106Q14.992776984558105,12.818916984558106,15.593496984558106,14.119826984558106Q15.705726984558105,14.362316984558106,15.516716984558105,14.551036984558106L8.032846984558105,22.034966984558103Q7.632898984558105,22.435366984558108,7.632898984558105,23.000866984558105Q7.632898984558105,23.566866984558107,8.032826984558106,23.966866984558106Q8.433186984558105,24.367166984558107,8.999086984558105,24.367166984558107Q9.564756984558105,24.367166984558107,9.965036984558106,23.966966984558105L17.469966984558106,16.462276984558105Q17.655266984558104,16.276936984558105,17.895366984558105,16.382576984558106Q18.778166984558105,16.771966984558105,19.747166984558106,16.771966984558105Q21.637866984558105,16.771966984558105,22.974366984558106,15.435916984558105Q23.809166984558104,14.600866984558106,24.135166984558104,13.481896984558105Q24.457966984558105,12.374196984558106,24.209866984558104,11.241136984558105L24.131666984558105,10.883916984558105L21.895466984558105,13.120016984558106Q21.750066984558103,13.265166984558105,21.549466984558105,13.224926984558106L19.487966984558106,12.812336984558105Q19.237566984558107,12.762256984558105,19.187566984558103,12.511906984558106L18.775366984558104,10.450366984558105Q18.734866984558103,10.249566984558106,18.880066984558106,10.104436984558106ZM20.341166984558107,8.077536984558105Q20.038166984558103,8.032856984558105,19.732366984558105,8.032856984558105Q18.007766984558106,8.032856984558105,16.789466984558103,9.250966984558104Q15.867946984558106,10.172446984558105,15.638256984558106,11.468696984558106Q15.408526984558106,12.765146984558106,15.956656984558105,13.952136984558106Q16.186116984558105,14.447926984558105,15.799336984558105,14.834096984558105L8.315686984558106,22.317766984558105Q8.032896984558105,22.600966984558106,8.032896984558105,23.000866984558105Q8.032896984558105,23.401166984558106,8.315706984558105,23.684066984558104Q8.598836984558105,23.967166984558105,8.999086984558105,23.967166984558105Q9.399096984558106,23.967166984558105,9.682226984558106,23.684066984558104L17.187066984558108,16.179426984558106Q17.565866984558106,15.800616984558106,18.056466984558107,16.016446984558108Q18.862466984558104,16.371976984558106,19.747166984558106,16.371976984558106Q21.472166984558108,16.371976984558106,22.691566984558104,15.153026984558105Q24.167966984558106,13.676196984558105,23.885866984558106,11.695346984558105L22.178266984558107,13.402866984558106Q21.881266984558106,13.699456984558106,21.470766984558104,13.617106984558106L19.409466984558108,13.204556984558106Q18.897566984558104,13.102166984558107,18.795366984558108,12.590216984558104L18.383066984558106,10.528796984558106Q18.300466984558106,10.118336984558105,18.597266984558104,9.821576984558106L20.341166984558107,8.077536984558105Z",fillRule:"evenodd",fill:"#929A9E",fillOpacity:1})))),Vo=({venueId:t,isDisabled:a})=>{var x,g;const[s,n]=o.useState(!1),i=on({id:t}),r=dn({id:t});(g=(x=qe({id:t}).data)==null?void 0:x.boards)!=null&&g.length;const d=(h,j,A)=>{const u=h==="success"?e.jsx(Hs,{style:{color:"#2BC174 ",marginRight:8}}):e.jsx(Ks,{style:{color:"#FF5145",marginRight:8}}),p=h==="success"?`Successfully ${j} ${A} Devices`:`Failed ${j} ${A} Devices`;D.open({content:e.jsxs("span",{style:{color:h==="success"?"#2BC174 ":"#FF5145"},children:[u,p]}),duration:3})},f=async({key:h})=>{var j,A,u,p;try{if(h==="reboot"){const b=((j=(await i.mutateAsync()).data.serialNumbers)==null?void 0:j.length)||0;d("success","Reboot",b)}else if(h==="update"){const b=((A=(await r.mutateAsync()).data.serialNumbers)==null?void 0:A.length)||0;d("success","Update",b)}else h==="upgrade"&&n(!0)}catch(v){const b=((p=(u=v.response)==null?void 0:u.data)==null?void 0:p.failedCount)||0;h==="reboot"?d("error","Reboot",b):h==="update"&&d("error","Update",b)}},c=e.jsxs(Tt,{onClick:f,children:[e.jsx(Tt.Item,{children:"Reboot All Devices"},"reboot"),e.jsx(Tt.Item,{children:"Update All Device Configurations"},"update")]});return e.jsxs(e.Fragment,{children:[e.jsx(Gs,{overlay:c,trigger:["click"],placement:"bottomRight",disabled:!t,children:e.jsx(ue,{title:"Actions",placement:"bottom",children:e.jsx(Z,{icon:e.jsx(Fe,{component:zo}),type:"text",style:{width:40,height:40,borderRadius:4},className:"actions-button"})})}),e.jsx(qo,{visible:s,onClose:()=>n(!1),venueId:t})]})},Qo=t=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:32,height:32,viewBox:"0 0 32 32",...t},o.createElement("g",null,o.createElement("g",null,o.createElement("rect",{x:0,y:0,width:32,height:32,rx:4,fill:"#14C9BB",fillOpacity:1})),o.createElement("g",null,o.createElement("g",null,o.createElement("path",{d:"M15.999783277511597,11.000216960906982C14.263573277511597,11.000216960906982,12.631283277511596,11.676341960906983,11.403593277511597,12.904026960906982C10.175908277511597,14.131696960906982,9.499783277511597,15.763986960906983,9.499783277511597,17.500196960906983C9.499783277511597,17.86838696090698,9.798262277511597,18.166866960906983,10.166450277511597,18.166866960906983C10.534633277511597,18.166866960906983,10.833113277511597,17.86838696090698,10.833113277511597,17.500196960906983C10.833113277511597,16.120136960906983,11.370533277511596,14.822676960906982,12.346413277511598,13.846826960906983C13.322263277511597,12.870966960906982,14.619723277511596,12.333526960906983,15.999783277511597,12.333526960906983C17.379843277511597,12.333526960906983,18.677303277511598,12.870946960906982,19.653183277511594,13.846826960906983C20.628983277511598,14.822676960906982,21.166483277511595,16.120136960906983,21.166483277511595,17.500196960906983C21.166483277511595,17.86838696090698,21.464883277511596,18.166866960906983,21.833083277511598,18.166866960906983C22.201283277511596,18.166866960906983,22.499783277511597,17.86838696090698,22.499783277511597,17.500196960906983C22.499783277511597,15.763986960906983,21.8236832775116,14.131696960906982,20.595983277511596,12.904006960906983C19.368283277511594,11.676320960906983,17.735993277511596,11.000216960906982,15.999783277511597,11.000216960906982Z",fill:"#FFFFFF",fillOpacity:1,style:{mixBlendMode:"passthrough"}})),o.createElement("g",null,o.createElement("path",{d:"M15.999996984558106,8C10.845336984558106,8,6.6666669845581055,12.17867,6.6666669845581055,17.33333C6.6666669845581055,19.2083,7.219687984558106,20.9541,8.171336984558106,22.4167L23.828666984558105,22.4167C24.780266984558107,20.9541,25.333366984558104,19.2083,25.333366984558104,17.33333C25.333366984558104,12.17867,21.154666984558105,8,15.999996984558106,8ZM23.068466984558107,21.0833L8.931496984558105,21.0833C8.320266984558106,19.9336,7.999996984558106,18.6506,7.999996984558106,17.33333C7.999996984558106,16.25275,8.211336984558105,15.205210000000001,8.628126984558106,14.21977C9.030976984558105,13.26729,9.607996984558106,12.4116,10.343146984558105,11.67648C11.078266984558105,10.94133,11.933956984558105,10.364329999999999,12.886436984558106,9.96146C13.871876984558106,9.54467,14.919416984558106,9.33333,15.999996984558106,9.33333C17.080566984558104,9.33333,18.128166984558106,9.54467,19.113566984558105,9.96146C20.066066984558105,10.36431,20.921766984558104,10.94133,21.656866984558107,11.67648C22.391966984558106,12.4116,22.968966984558104,13.26729,23.371866984558107,14.21977C23.788666984558105,15.205210000000001,23.999966984558107,16.25275,23.999966984558107,17.33333C23.999966984558107,18.6506,23.679766984558107,19.9336,23.068466984558107,21.0833Z",fill:"#FFFFFF",fillOpacity:1,style:{mixBlendMode:"passthrough"}})),o.createElement("g",null,o.createElement("path",{d:"M16.42132332046509,16.92652784362793C16.213003320465088,16.92641784362793,16.00651332046509,16.96549784362793,15.812633320465087,17.04171784362793L14.373323320465088,15.60240084362793C14.112967320465089,15.34200544362793,13.690863320465088,15.342046743627929,13.430508320465087,15.60238084362793C13.170154420465089,15.86273484362793,13.170154420465089,16.28485984362793,13.430508320465087,16.54519784362793L14.869843320465089,17.98452784362793C14.793623320465088,18.17840784362793,14.754553320465089,18.38488784362793,14.754653320465088,18.59321784362793C14.754653320465088,19.51369784362793,15.500843320465087,20.25987784362793,16.42132332046509,20.25987784362793C17.34180332046509,20.25987784362793,18.08799332046509,19.51369784362793,18.08799332046509,18.59321784362793C18.08799332046509,17.672737843627928,17.341783320465087,16.92652784362793,16.42132332046509,16.92652784362793Z",fill:"#FFFFFF",fillOpacity:1,style:{mixBlendMode:"passthrough"}}))))),Go=t=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:32,height:32,viewBox:"0 0 32 32",...t},o.createElement("g",null,o.createElement("g",null,o.createElement("rect",{x:0,y:0,width:32,height:32,rx:4,fill:"#FFFFFF",fillOpacity:1})),o.createElement("g",null,o.createElement("g",null,o.createElement("path",{d:"M23.2494,13.75005L19.9997,13.75005C19.5854,13.75005,19.249499999999998,13.41419,19.249499999999998,12.99987C19.249499999999998,12.585560000000001,19.5854,12.249690000000001,19.9997,12.249690000000001L21.3049,12.249690000000001C20.089199999999998,10.52464,18.1102,9.49901,15.99979,9.50036C12.41066,9.50051,9.50117,12.41036,9.50117,15.9998C9.50131,19.589399999999998,12.411159999999999,22.499200000000002,16.0006,22.499200000000002C19.5902,22.4991,22.5,19.589199999999998,22.5,15.9998C22.4862,15.57609,22.8259,15.22508,23.2498,15.22508C23.6737,15.22508,24.0135,15.57609,23.9996,15.9998C23.9996,20.4174,20.4174,23.9996,15.99979,23.9996C11.58224,23.9996,8,20.4174,8,15.9998C8,11.58224,11.58224,8.000000919181,15.99979,8.000000919181C18.5805,7.99878595,21.002299999999998,9.24607,22.5,11.34771L22.5,9.99995C22.4862,9.57625,22.8259,9.22523,23.2498,9.22523C23.6737,9.22523,24.0135,9.57625,23.9996,9.99995L23.9996,12.99987C23.9996,13.41419,23.6637,13.75005,23.2494,13.75005Z",fill:"#929A9E",fillOpacity:1}),o.createElement("path",{d:"M23.8506,13.601040000000001Q24.0996,13.35203,24.0996,12.99987L24.0996,10.00155Q24.1107,9.642479999999999,23.8607,9.38416Q23.6101,9.12523,23.2498,9.12523Q22.889499999999998,9.12523,22.6389,9.38416Q22.3889,9.64247,22.4,10.00151L22.4,11.04452Q21.308,9.62357,19.7018,8.796382Q17.959519999999998,7.899078,15.9998,7.900001Q14.35221,7.900001,12.84721,8.536650999999999Q11.39367,9.15153,10.2726,10.2726Q9.15154,11.39367,8.536650999999999,12.84721Q7.9,14.3522,7.9000005,15.9998Q7.9000005,17.64741,8.536650999999999,19.1524Q9.15155,20.6059,10.2726,21.727Q11.39369,22.848100000000002,12.84721,23.462899999999998Q14.35221,24.0996,15.9998,24.0996Q17.64738,24.0996,19.1524,23.462899999999998Q20.6059,22.848100000000002,21.727,21.727Q22.848,20.6059,23.462899999999998,19.1524Q24.0993,17.64812,24.0996,16.0014Q24.1107,15.642330000000001,23.8607,15.384Q23.6101,15.12508,23.2498,15.12508Q22.889499999999998,15.12508,22.6389,15.384Q22.3889,15.64231,22.4,16.001350000000002Q22.3997,17.302509999999998,21.897199999999998,18.4906Q21.4114,19.639,20.5257,20.5248Q19.6399,21.4105,18.491500000000002,21.8963Q17.30264,22.3992,16.0006,22.3992Q14.698599999999999,22.3992,13.50974,21.8964Q12.36137,21.4107,11.47561,20.524900000000002Q10.58986,19.639200000000002,10.104099999999999,18.4907Q9.60122,17.30184,9.60116,15.9998Q9.60116,14.6978,10.10396,13.508939999999999Q10.58963,12.36056,11.47527,11.4748Q12.36091,10.58905,13.50918,10.1033Q14.69792,9.60042,15.99986,9.60036Q17.55822,9.59937,18.9418,10.31641Q20.238599999999998,10.98847,21.1086,12.14969L19.9997,12.14969Q19.6475,12.14969,19.3985,12.3987Q19.1495,12.64772,19.1495,12.99987Q19.1495,13.35203,19.3985,13.601040000000001Q19.6475,13.85005,19.9997,13.85006L23.2494,13.85006Q23.601599999999998,13.85006,23.8506,13.601040000000001ZM23.8996,9.99832L23.8996,12.99987Q23.8996,13.26919,23.7092,13.459620000000001Q23.518700000000003,13.65006,23.2494,13.65006L19.9997,13.65006Q19.7304,13.65006,19.5399,13.459620000000001Q19.3495,13.26919,19.3495,12.99987Q19.3495,12.73056,19.5399,12.540130000000001Q19.7304,12.349689999999999,19.9997,12.349689999999999L21.497700000000002,12.349689999999999L21.3866,12.19209Q20.4608,10.87834,19.0339,10.13884Q17.606920000000002,9.399329999999999,15.99973,9.40036Q14.657350000000001,9.40042,13.43126,9.9191Q12.247060000000001,10.42006,11.33384,11.33339Q10.42061,12.24672,9.91975,13.43104Q9.40116,14.65726,9.40116,15.9998Q9.40122,17.342399999999998,9.9199,18.5686Q10.42086,19.753,11.33419,20.6663Q12.24754,21.579700000000003,13.43183,22.0806Q14.65805,22.5992,16.0006,22.5992Q17.3432,22.5992,18.5694,22.0805Q19.7538,21.5795,20.667099999999998,20.6662Q21.5805,19.7529,22.081400000000002,18.5686Q22.6,17.34234,22.6,15.9998L22.6,15.99816L22.6,15.99653Q22.591,15.72111,22.782600000000002,15.52309Q22.9742,15.32508,23.2498,15.32508Q23.525399999999998,15.32508,23.717,15.52309Q23.9087,15.72111,23.8996,15.99653L23.8996,15.99816L23.8996,15.9998Q23.8996,17.606830000000002,23.2787,19.0745Q22.6791,20.4921,21.5856,21.5856Q20.4921,22.6791,19.0745,23.2787Q17.60684,23.8996,15.9998,23.8996Q14.392769999999999,23.8996,12.92513,23.2787Q11.50753,22.6791,10.41402,21.5856Q9.32055,20.4921,8.720847,19.0745Q8.1,17.60684,8.100001,15.9998Q8.1,14.392769999999999,8.720847,12.92513Q9.32054,11.50751,10.41402,10.41403Q11.50751,9.32054,12.92513,8.720848Q14.392769999999999,8.100001,15.9998,8.100001Q17.911099999999998,8.0991011,19.610300000000002,8.974187Q21.3094,9.84927,22.418599999999998,11.40574L22.6,11.66034L22.6,9.99832L22.6,9.99668Q22.591,9.721260000000001,22.782600000000002,9.52325Q22.9742,9.32523,23.2498,9.32523Q23.525399999999998,9.32523,23.717,9.52325Q23.9087,9.721260000000001,23.8996,9.99668L23.8996,9.99832Z",fillRule:"evenodd",fill:"#929A9E",fillOpacity:1}))))),Ho="/assets/Logo_NOMoitor-DT8EvDS5.png",Ko=({onTurnOn:t})=>e.jsxs("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -70%)",textAlign:"center",backgroundColor:"#fff",padding:"40px",zIndex:999},children:[e.jsx("img",{src:Ho,alt:"No Data",style:{marginBottom:"0px",width:"96px",height:"96px"}}),e.jsx("div",{style:{fontSize:"12px",color:"#9DA6B3",height:"18px",marginBottom:"22px"},children:"No Data"}),e.jsxs("p",{style:{marginBottom:"40px",width:"739px",height:"auto",fontFamily:"Lato, Lato",fontWeight:600,fontSize:"16px",color:"#212519"},children:["Monitoring not activated on this site.",e.jsx("br",{}),'Please activate it by using the "Monitor" button at the top right of the screen or the button at the bottom']}),e.jsx(Z,{type:"primary",onClick:t,style:{width:"175px",height:"36px"},children:"Turn on the monitoring"})]}),cd=()=>{var T,L,N,M;const t=$s(R=>R.user.userInfo),a=gt(),s=xt(),[n,i]=o.useState(""),{getFirstVenueFavoriteId:r}=Kn({id:"",type:"venue"}),[l,d]=o.useState(()=>window.location.hash&&window.location.hash.startsWith("#")?window.location.hash.substring(1):(r==null?void 0:r())||"0"),[f,c]=o.useState(0),[x,g]=o.useState(!1),[h,j]=o.useState(!1),[A,u]=o.useState(!1),p=qe({id:l||"0"}),v=Lo({venueId:l||"0"});((L=(T=p.data)==null?void 0:T.boards)==null?void 0:L.length)>0;const b=((M=(N=p.data)==null?void 0:N.boards)==null?void 0:M[0])||"",m=()=>{g(!0)};o.useEffect(()=>{var R;v.data&&u(!(((R=v.data.boards)==null?void 0:R.length)>0))},[v.data]);const I=R=>{const O=Array.isArray(R)?R[0]:R;d(O);const q=a.pathname;s(`${q}#${R}`)},S=()=>{var O,q;if(!l){D.warning("Please select a venue first");return}p.refetch(),((q=(O=p.data)==null?void 0:O.boards)==null?void 0:q.length)>0?j(!0):g(!0)},y=()=>{c(R=>R+1),p.refetch(),D.success("Successfully refreshed")};o.useEffect(()=>{const R=()=>{const O=window.location.hash.substring(1);O&&d(O)};return window.addEventListener("hashchange",R),R(),()=>{window.removeEventListener("hashchange",R)}},[]),o.useEffect(()=>{v.refetch()});const C=[{key:"Monitor",label:"Monitor",children:A?e.jsx(Ko,{onTurnOn:m}):e.jsx(At,{component:()=>e.jsx(tr,{venueId:l})})},{key:"Configure",label:"Configure",children:e.jsx(At,{component:jo})},{key:"RRM-Optimize",label:"RRM Optimize",children:e.jsx(At,{component:Bo})}],w=t.type==="readonly"?[]:C;o.useEffect(()=>{const R=a.pathname.match(/(Monitor|Configure|RRM-Optimize)$/);if(R)i(R[0]);else if(w.length>0){i(w[0].key);let O=`${a.pathname.replace(/\/$/,"")}/${w[0].key}`;a.hash&&(O+=a.hash),s(O)}},[a.pathname,w]);const E=R=>{let O=a.pathname.replace(/(Monitor|Configure|RRM-Optimize)$/,"");O=`${O.replace(/\/$/,"")}/${R}`,a.hash&&(O+=a.hash),s(O)};return e.jsxs(De,{className:"div-main",children:[e.jsxs("div",{className:"div-header",children:[e.jsx(an,{onChange:I}),e.jsxs(Xe,{className:"action-buttons",style:{paddingTop:10},children:[e.jsx(ue,{title:"Monitoring",placement:"bottom",children:e.jsx(Z,{icon:e.jsx(Fe,{component:Qo}),type:"text",onClick:S,style:{width:40,height:40,borderRadius:4},className:"monitor-button"})}),e.jsx(Vo,{venueId:l,isDisabled:!p.data}),e.jsx(ue,{title:"Refresh",placement:"bottom",children:e.jsx(Z,{icon:e.jsx(Fe,{component:Go}),type:"text",onClick:y,style:{width:40,height:40,borderRadius:4},className:"refresh-button"})})]})]}),e.jsxs("div",{className:"div-tabs",children:[e.jsx(Lt,{activeKey:n,onChange:E,destroyInactiveTabPane:!0,items:w},f),l&&e.jsxs(e.Fragment,{children:[e.jsx(Qn,{id:l,visible:x,onClose:()=>{g(!1),u(!1)}}),e.jsx(Hn,{boardId:b,venueId:l,visible:h,onClose:()=>j(!1),onApplySuccess:u})]})]})]})};export{cd as default};
