import{r as i,I as kt,_ as It,y as ge,b as _,H as M,a as Ae,u as Tt,R as oe,j as e,aE as qe,B as q,S as Ne,aV as Et,A as ue,c as P,D as te,Z as it,a0 as de,bt as Dt,bu as Ot,bv as lt,V as re,Q as ye,af as Y,T as ct,M as Ce,br as ze,b7 as dt,b1 as Ft,bb as ut,bh as nt,O as E,ab as pe,bw as Lt,W as Q,aq as Bt,aD as Le,ah as mt,b6 as Pt,b8 as Mt,bx as _t,ba as $t,bf as qt,bg as zt,bm as Be,ay as Pe,a2 as J,an as Rt,ai as Vt,by as Re,bz as Ve,g as X,h as z,bA as Ut,F as Wt,X as Se,bs as pt,ao as ke,P as rt,ak as ht,al as Ie,J as be,a1 as fe,bB as se,ac as Ue,bC as ee,bD as Gt,bE as Jt,C as Qt}from"./index-CHCmiRmn.js";import{u as ot,S as Kt}from"./useMutationResult-f-NPTDLR.js";import{P as D,W as Xt}from"./CustomTable-CM5Sdauq.js";import{r as Ht}from"./Form-CYdW6Qd_.js";import{C as Zt,A as Yt,a as es,M as ts,u as ss}from"./useCommandModal-my1Kwhjw.js";import{d as ns}from"./dateFormatting-yHFQ8l7H.js";import{f as gt,c as rs,d as os,F as as,b as is}from"./Venues-DpPldi0z.js";import{F as he,u as ls}from"./FormModal-BzU8SPYc.js";var cs={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"},ds=function(o,s){return i.createElement(kt,It({},o,{ref:s,icon:cs}))},us=i.forwardRef(ds);const ms=({serialNumber:t})=>ge(()=>Ae.post(`device/${t}/reboot`,{serialNumber:t,when:0})),ps=({serialNumber:t})=>ge(()=>Ae.post(`device/${t}/leds`,{serialNumber:t,when:0,pattern:"blink",duration:30})),hs=({serialNumber:t,keepRedirector:o,onClose:s})=>{const{t:r}=_();return ge(()=>Ae.post(`device/${t}/factory`,{serialNumber:t,keepRedirector:o}),{onSuccess:()=>{M.success(r("commands.factory_reset_success"),5),s()},onError:n=>{var a,u;M.error(r("commands.factory_reset_error",{e:((u=(a=n==null?void 0:n.response)==null?void 0:a.data)==null?void 0:u.ErrorDescription)||"unknown error"}),5)}})},gs=({serialNumber:t,extraId:o})=>{const{t:s}=_();return Tt(["get-gateway-device-rtty",t,o],()=>Ae.get(`device/${t}/rtty`).then(({data:r})=>r),{enabled:!1,onSuccess:({server:r,viewport:n,connectionId:a})=>{var c;const u=`https://${r}:${n}/connect/${a}`;(c=window.open(u,"_blank"))==null||c.focus()},onError:r=>{var a,u,c;const n=((a=r==null?void 0:r.response)==null?void 0:a.status)===404;M.error(n?s("devices.not_found_gateway"):s("devices.error_rtty",{e:(c=(u=r==null?void 0:r.response)==null?void 0:u.data)==null?void 0:c.ErrorDescription}),5)}})},xs=({device:t,refresh:o,isDisabled:s,onOpenScan:r,onOpenFactoryReset:n,onOpenUpgradeModal:a})=>{const{t:u}=_(),{refetch:c,isInitialLoading:p}=gs({serialNumber:t.serialNumber,extraId:"inventory-modal"}),{mutateAsync:d,isLoading:g}=ms({serialNumber:t.serialNumber}),{mutateAsync:l}=ps({serialNumber:t.serialNumber}),{onSuccess:f,onError:j}=ot({objName:u("devices.one"),operationType:"reboot",refresh:o}),{onSuccess:w,onError:O}=ot({objName:u("devices.one"),operationType:"blink",refresh:o}),F=()=>d(void 0,{onSuccess:f,onError:j}),b=()=>l(void 0,{onSuccess:()=>{w()},onError:y=>{O(y)}}),v=()=>n(t.serialNumber),B=()=>c(),h=[{key:"reboot",label:u("commands.reboot"),onClick:F},{key:"blink",label:u("commands.blink"),onClick:b},{key:"rtty",label:u("commands.rtty"),onClick:B},{key:"factory_reset",label:u("commands.factory_reset"),onClick:v}];return e.jsx(qe,{menu:{items:h},disabled:s,trigger:["click"],children:e.jsxs(q,{type:"link",style:{margin:0,padding:"0 4px"},children:[g||p?e.jsx(Ne,{size:"small"}):u("common.actions"),e.jsx(Et,{style:{marginLeft:"8px"}})]})})},fs=oe.memo(xs),ys="/assets/error-D_ymsMcw.svg",bs="/assets/success-Cweqjn-W.svg",{Title:Me}=P,vs={isOpen:D.bool.isRequired,onClose:D.func.isRequired,pushResult:D.instanceOf(Object)},js={pushResult:null},We=({isOpen:t,onClose:o,pushResult:s})=>{const{t:r}=_(),n=(s==null?void 0:s.errorCode)===0,a=(s==null?void 0:s.errorCode)!==0,u=()=>{if(!(s!=null&&s.errors)||s.errors.length===0)return null;const d=s.errors[0],[g,...l]=d.split(":"),f=l.join(":").trim();return`Configuration not pushed, error code: ${g.trim()}, detail: ${f}`},c=()=>(s==null?void 0:s.errorCode)===-1?s==null?void 0:s.msg:a?u()||r("configurations.push_configuration_explanation",{code:(s==null?void 0:s.errorCode)??0}):r("configurations.push_success"),p=e.jsxs(e.Fragment,{children:[e.jsx(ue,{style:{marginTop:8,marginBottom:24,backgroundColor:n?"rgba(43, 193, 116, 0.1)":"rgba(245, 63, 63, 0.1)",border:`1px solid ${n?"#2BC174":"#F53F3F"}`},type:n?"success":"error",showIcon:!0,icon:e.jsx("img",{src:n?bs:ys,alt:n?"success":"error",style:{width:20,height:20}}),message:e.jsxs("span",{style:{color:a?"#F53F3F":"#2BC174",fontWeight:500},children:[e.jsx("strong",{children:"Note:"})," ",c()]})}),n&&e.jsxs("div",{style:{marginTop:24},children:[e.jsx(Me,{level:5,children:r("configurations.applied_configuration")}),e.jsx("div",{style:_e.scrollBox,children:e.jsx("pre",{children:JSON.stringify(JSON.parse((s==null?void 0:s.appliedConfiguration)||"{}"),null,2)})}),e.jsx(te,{style:{marginLeft:"0px",width:"616px"}}),e.jsx(Me,{level:5,children:r("common.errors")}),e.jsx("div",{style:_e.scrollBox,children:e.jsx("pre",{children:JSON.stringify(s==null?void 0:s.errors,null,2)})}),e.jsx(te,{style:{marginLeft:"0px",width:"616px"}}),e.jsx(Me,{level:5,children:r("common.warnings")}),e.jsx("div",{style:_e.scrollBox,children:e.jsx("pre",{children:JSON.stringify(s==null?void 0:s.warnings,null,2)})})]})]});return e.jsx(it,{title:r("configurations.configuration_push_result"),isModalOpen:t,onCancel:o,modalClass:"ampcon-max-modal",footer:null,childItems:p})},_e={scrollBox:{border:"1px solid #d9d9d9",borderRadius:4,padding:"0px 16px",height:"25vh",overflowY:"auto",backgroundColor:"#fafafa",fontSize:12,margin:"32px 0"}};We.propTypes=vs;We.defaultProps=js;const ws=({cell:{original:t},refreshTable:o,openEditModal:s,onOpenFactoryReset:r,onOpenUpgradeModal:n})=>{const{t:a}=_(),{onOpen:u,onClose:c}=de(),{isOpen:p,onOpen:d,onClose:g}=de(),[l,f]=oe.useState(!1),{data:j}=Dt(),{mutateAsync:w}=Ot({name:t.name,refreshTable:o,onClose:c}),O=lt({onSuccess:()=>{l&&(d(),f(!1))}}),F=t.venue===void 0||t.venue===null||t.venue==="",b=()=>w(t.serialNumber),v=()=>s(t),B=()=>{ye(e.jsx(P.Paragraph,{style:{display:"flex",alignItems:"center"},children:e.jsxs("span",{children:["Are you sure you want to push the configuration to device ",e.jsx("br",{}),e.jsxs("b",{children:["#",t.serialNumber]}),"?",e.jsx("br",{}),e.jsx("br",{}),"You cannot undo this action afterwards."]})}),()=>(f(!0),O.mutateAsync(t.serialNumber)),()=>{f(!1)},{confirmLoading:O.isLoading})},h=()=>{ye("Are you sure you want to delete this Device?",b)};return e.jsxs(re,{size:24,children:[e.jsx(q,{type:"link",onClick:v,style:{padding:0},children:a("common.edit")}),e.jsx(q,{type:"link",onClick:B,style:{margin:0,padding:"0 ",color:F?"#B3BBC8":void 0},disabled:F,children:a("configurations.push_configuration")}),e.jsx(fs,{device:t,refresh:o,onOpenFactoryReset:r,onOpenUpgradeModal:n}),e.jsx(q,{type:"link",onClick:h,style:{padding:0},children:a("crud.delete")}),e.jsx(We,{isOpen:p,onClose:g,pushResult:O.data})]})},Cs=(t,o,s)=>Y.get(`inventory?withExtendedInfo=true&limit=${t}&offset=${o}${s?`&venue=${s}`:""}`).then(r=>r.data),As=async(t,o,s,r)=>{const n=(90-o)/Math.ceil(t/100);let a=o,u=0,c=[],p;do p=await Cs(100,u,r),c=c.concat(p.taglist),s(a+=n),u+=100;while(p.taglist.length===100);return c},Ns=async(t,o)=>{t(0);const s=await Y.get(`inventory?countOnly=true${o?`&venue=${o}`:""}`).then(u=>u.data.count);if(t(10),s===0)return t(100),[];const r=await As(s,10,t,o);t(95);const n=u=>{try{return new Date(u*1e3).toISOString()}catch{return""}},a=r.map(u=>{var c,p;return{serialNumber:u.serialNumber,name:u.name,site:(p=(c=u==null?void 0:u.extendedInfo)==null?void 0:c.venue)==null?void 0:p.name,description:u.description,label:u.labelsName,modified:n(u.modified)}});return t(100),a},Ss=(t,o)=>Y.get(`inventory?withExtendedInfo=true&select=${t.join(",")}${o?`&venue=${o}`:""}`).then(s=>s.data),ks=async(t,o,s)=>{o(0);const r=t.length;if(o(10),r===0)return o(100),[];const n=(await Ss(t,s)).taglist;o(95);const a=c=>{try{return new Date(c*1e3).toISOString()}catch{return""}},u=n.map(c=>{var p,d;return{serialNumber:c.serialNumber,name:c.name,site:(d=(p=c==null?void 0:c.extendedInfo)==null?void 0:p.venue)==null?void 0:d.name,description:c.description,label:c.labelsName,modified:a(c.modified)}});return o(100),u},Is="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAe9JREFUWEftlkFOwkAUht8rxRBWTahr4Qa4AdnRRCXsPALeAE8gnkA8gUfQHREWZWMKK7gB7C1JV0igMDpFsNSh02khRsPs2k7f/8289/4ZhF8eGFVfMRppmCQsS9OsMLEiAajGSw0k6dYRXizuzEKpJgoRGmBDfKUaAiIUAFM8JIQwgK94CAghgEDighCBAbziBMgQiNRGJBVHE6EOBIoAkF0XYoCaCATAEp8v5poMUsXdBfbkqC4nbV0EggugdlpVQLhfrYqunIpbhfKQ1YaKrisiEL4AX8EGAKBQALc4fd7mAwwIyx7LGZZZcXcg1W0OEDDtFfcDoN/cEPTfUf4ywzIpLgANFEtOi6N86dkbIIgTHr82s7NpfLjNqrkAftYaBIBnzQcA5g7QvAc5XsOkwBt7A2BZcLMeAioEFteswnPnVBQgZTQrKOEjILTN3IW2NFDXoBVLZOw5rwi0zbPlpG1DFEDttp4A4Gp5f7A1s1BubwCoRqMIkkytdD8AnZb+uXp6XvwTgHVOaS4Rq2+58wfflO16B1Z2TDB2Mn+P3fA6R90HAM/pNrrmbwEAWECgL7JC7lx0bkvO0c5vQ260iBNYPuBywnTE8L6/O3eLcfyUFu2Ps8C5SCQm3xfLPZDYk0R/1TGRjuNdsB0APgDlroowDeDQuAAAAABJRU5ErkJggg==",Ts=[{key:"serialNumber",label:"Serial Number"},{key:"name",label:"Name"},{key:"site",label:"Site"},{key:"description",label:"Description"},{key:"label",label:"Label"},{key:"modified",label:"Modified"}],Es=()=>{if(window.location.hash){const t=window.location.hash.replace("#","");return t==="all"?"":t}return""},Ds=({serialNumbers:t})=>{const{t:o}=_(),[s,r]=i.useState(!1),[n,a]=i.useState({progress:0,status:"idle"}),u=d=>{a(g=>({...g,progress:d}))},c=()=>{const d=Es();t?(a(g=>({...g,error:void 0,lastResults:void 0,status:"loading-select",progress:0})),ks(t,u,d).then(g=>{a(l=>({...l,status:"success",lastResults:g}))}).catch(g=>{a(l=>({...l,status:"error",error:g}))})):(a(g=>({...g,error:void 0,lastResults:void 0,status:"loading-all",progress:0})),Ns(u,d).then(g=>{a(l=>({...l,status:"success",lastResults:g}))}).catch(g=>{a(l=>({...l,status:"error",error:g}))})),r(!0)},p=()=>{r(!1)};return e.jsxs(e.Fragment,{children:[e.jsx(ct,{title:o("common.export"),children:e.jsx(q,{htmlType:"button",onClick:c,style:{display:"flex",alignItems:"center"},icon:e.jsx("img",{src:Is,alt:"export",style:{width:16,height:16}}),children:"Export"})}),e.jsx(Ce,{title:o("common.export"),open:s,onCancel:p,footer:null,width:680,style:{height:450},children:e.jsxs("div",{style:{padding:0},children:[(n.status.includes("loading")||n.status==="success")&&e.jsxs(e.Fragment,{children:[e.jsx(te,{style:{margin:"15px 0 45px 0",width:"calc(100% + 48px)",marginLeft:"-24px"}}),e.jsxs("div",{style:{margin:"139px 0 139px 0"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx(ze,{style:{flex:1,marginBottom:0},percent:Math.round(n.progress),status:n.progress!==100?"active":"success",showInfo:!1,strokeColor:n.progress===100?"#14C9BB":void 0,strokeWidth:12}),e.jsxs(P.Title,{level:5,style:{margin:"0 0 0px 0",width:50,fontWeight:"bold"},children:[Math.round(n.progress),"%"]})]}),e.jsx(P.Text,{style:{textAlign:"center",marginBottom:25,fontWeight:"bold"},children:`Exporting to ${n.progress}%, Please Wait...`})]}),n.lastResults&&e.jsx(te,{style:{margin:"45px 0 0 0",width:"calc(100% + 48px)",marginLeft:"-24px"}}),n.lastResults&&e.jsx("div",{style:{textAlign:"right",margin:"20px 0 0 0"},children:e.jsx(Zt,{filename:`devices_export_${ns(new Date().getTime()/1e3)}.csv`,data:n.lastResults??[],headers:Ts,children:e.jsx(q,{type:"primary",children:o("common.download")})})})]}),n.status.includes("error")&&e.jsx(P.Text,{type:"danger",style:{display:"block",textAlign:"center",marginTop:32},children:JSON.stringify(n.error,null,2)})]})})]})},Os=({modalProps:{isOpen:t},confirm:o,cancel:s})=>{const{t:r}=_(),n=i.useRef(null);return e.jsx(Yt,{isOpen:t,onClose:s,leastDestructiveRef:n,isCentered:!0,children:e.jsx(dt,{children:e.jsxs(es,{children:[e.jsx(Ft,{children:r("commands.abort_command_title")}),e.jsx(ut,{children:r("commands.abort_command_explanation")}),e.jsxs(ts,{children:[e.jsx(nt,{ref:n,onClick:s,mr:4,children:r("common.cancel")}),e.jsx(nt,{onClick:o,colorScheme:"red",children:r("common.confirm")})]})]})})})},{Text:Ln}=P,Fs=({modalProps:{isOpen:t,onClose:o},serialNumber:s})=>{const{t:r}=_(),[n,a]=oe.useState(!1),{mutateAsync:u,isLoading:c}=hs({serialNumber:s,keepRedirector:n,onClose:o}),{isConfirmOpen:p,closeConfirm:d,closeModal:g,closeCancelAndForm:l}=ss({isLoading:c,onModalClose:o}),f=()=>{u()};return e.jsxs(e.Fragment,{children:[e.jsx(it,{title:r("commands.factory_reset"),isModalOpen:t,onCancel:g,modalClass:"ampcon-middle-modal",footer:null,childItems:c?e.jsx("div",{style:{textAlign:"center",padding:"40px 0"},children:e.jsx(Ne,{size:"large"})}):e.jsxs(e.Fragment,{children:[e.jsx(ue,{message:e.jsxs(e.Fragment,{children:[e.jsx("strong",{children:"Note:"})," Are you sure you want to factory reset this device? This action is not reversible"]}),type:"info",showIcon:!0,className:"custom-trace-alert",closable:!0,style:{marginTop:8,marginBottom:24}}),e.jsxs(E,{style:{display:"flex",flexDirection:"column"},children:[e.jsx(E.Item,{label:r("commands.keep_redirector"),children:e.jsx(pe,{checked:n,onChange:a})}),e.jsx(E.Item,{children:e.jsx(q,{size:"large",type:"primary",onClick:f,loading:c,children:r("commands.confirm_reset",{serialNumber:s})})})]})]})}),e.jsx(Os,{modalProps:{isOpen:p,onOpen:()=>{},onClose:d},confirm:l,cancel:d})]})},Ls=(t,o=1e3)=>{let s;return(...r)=>{clearTimeout(s),s=setTimeout(()=>{t.apply(void 0,r)},o)}},Bs=({callback:t})=>{const{isOpen:o,webSocket:s,lastMessage:r}=Lt(p=>({isOpen:p.isWebSocketOpen,webSocket:p.webSocket,lastMessage:p.lastMessage})),[n,a]=i.useState([]),u=i.useCallback(p=>{if(o&&s){const d=Ht();a([...n,d]);const g={...p,id:d};s.send(JSON.stringify(g))}},[o,s,n]);return i.useEffect(()=>{var p;r&&r.type==="COMMAND"&&n.includes((p=r.data)==null?void 0:p.command_response_id)&&t(r.data)},[r,n]),i.useMemo(()=>({isOpen:o,send:u}),[u])},Ps=({minLength:t=4,operatorId:o})=>{const[s,r]=i.useState(""),[n,a]=i.useState(void 0),[u,c]=i.useState([]),p=F=>{F.response.serialNumbers&&c(F.response.serialNumbers)},{isOpen:d,send:g}=Bs({callback:p}),l=i.useCallback(F=>{F.length>=t&&a({command:"serial_number_search",serial_prefix:F,operatorId:o})},[a]),f=i.useCallback(Ls(F=>{l(F)},300),[a]),j=i.useCallback(F=>{F!==s&&(r(F),f(F))},[s,f,r,a]),w=()=>{c([]),r("")};return i.useEffect(()=>{n&&g(n)},[n,d]),i.useMemo(()=>({inputValue:s,results:u,onInputChange:j,isOpen:d,resetSearch:w}),[s,u,j,d])},{Title:Ms}=P,_s=({onClick:t,isDisabled:o})=>{const{t:s}=_(),{inputValue:r,results:n,onInputChange:a,isOpen:u,resetSearch:c}=Ps({minLength:2}),[p,d]=i.useState(r),g=i.useCallback(()=>e.jsx(Ms,{level:5,style:{textAlign:"center",padding:"8px 0",margin:0},children:s("common.no_devices_found")}),[s]),l=j=>{c(),t(j)},f=j=>{d(j),(j.length===0||j.match("^[a-fA-F0-9-*]+$"))&&a(j)};return e.jsx(qe,{trigger:["click"],overlay:e.jsx(Le,{children:n.length>0?n.map(j=>e.jsx(Le.Item,{onClick:()=>l(j),children:j},j)):e.jsx(Le.Item,{disabled:!0,style:{border:"none",padding:0},children:e.jsx(g,{})})}),children:e.jsx(Q,{placeholder:s("common.search"),value:p,onChange:j=>f(j.target.value),prefix:e.jsx(Bt,{style:{color:"rgba(0,0,0,.25)"}})})})},$s={venueName:D.string,venueId:D.string},qs={venueName:"",venueId:""},Ge=({venueName:t,venueId:o})=>{const s=mt(),r=()=>{s(`/wireless/manage/Monitor#${o}`)};return t!==""&&o!==""?e.jsx("a",{href:"#",onClick:n=>{n.preventDefault(),r()},style:{textDecoration:"underline",color:"#14C9BB",cursor:"pointer"},children:t}):null};Ge.propTypes=$s;Ge.defaultProps=qs;const zs=oe.memo(Ge),Rs={isOpen:D.bool.isRequired,onClose:D.func.isRequired,pushResult:D.instanceOf(Object)},Vs={pushResult:null},Je=({isOpen:t,onClose:o,pushResult:s})=>{const{t:r}=_();return e.jsxs(Pt,{onClose:o,isOpen:t,size:"xl",children:[e.jsx(dt,{}),e.jsxs(Mt,{maxWidth:{sm:"600px",md:"700px",lg:"800px",xl:"50%"},children:[e.jsx(_t,{title:r("configurations.configuration_push_result"),right:e.jsx($t,{ml:2,onClick:o})}),e.jsxs(ut,{children:[e.jsxs(qt,{status:(s==null?void 0:s.errorCode)!==0?"error":"success",maxWidth:"96%",children:[e.jsx(zt,{}),(s==null?void 0:s.errorCode)===-1?s==null?void 0:s.msg:(s==null?void 0:s.errorCode)!==0?r("configurations.push_configuration_explanation",{code:(s==null?void 0:s.errorCode)??0}):r("configurations.push_success")]}),(s==null?void 0:s.errorCode)===0&&e.jsxs(e.Fragment,{children:[e.jsx(Be,{size:"md",mt:4,children:r("configurations.applied_configuration")}),e.jsx(Pe,{border:"1px",borderRadius:"5px",h:"calc(20vh)",overflowY:"auto",children:e.jsx("pre",{children:JSON.stringify(s==null?void 0:s.appliedConfiguration,null,2)})}),e.jsx(Be,{size:"md",mt:4,children:r("common.errors")}),e.jsx(Pe,{border:"1px",borderRadius:"5px",h:"calc(20vh)",overflowY:"auto",children:e.jsx("pre",{children:JSON.stringify(s==null?void 0:s.errors,null,2)})}),e.jsx(Be,{size:"md",mt:4,children:r("common.warnings")}),e.jsx(Pe,{border:"1px",borderRadius:"5px",h:"calc(20vh)",overflowY:"auto",children:e.jsx("pre",{children:JSON.stringify(s==null?void 0:s.warnings,null,2)})})]})]})]})]})};Je.propTypes=Rs;Je.defaultProps=Vs;const Us="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAaFJREFUWEftVk1OwkAU/p5C4pINSSubITL7cgM5gXgDuIGcADgB3gBvIJ4AbiD7Vjsr28RNd5qAfWbAYqKlHX+JprObzJvvfe+bN+89wo4X7dg/CgJ/SwEhLIESRGbeLB/nSkWRaW4ZKyCk1SHQ2AA44pi66uZuYmBrnoR1afsABDPPtgETUQWAA0D5blD/bgKsAX03yFStLm0ju4RcKpgQlQrKByPw63sT0bG+lKWAPk+1I5orN+ilKZJOQFYdQunaREJTG8ayqdz7+Vv7rXIKWXXA+/pNv77oKUpzvlIsD13ImkOIxxzTMMls0bCmAF0pLzjX90XDPiPCie8GrdVe1hxw3MeSe0qFKstHPoGGNSCiPoMvlBt2dS2gMukfscn0zQ9ZcF07FNIaE6jDzEPlhYOCQKFAocD/UkBHo/89g2e6LqwLjz0ioM2Lh6aeBX60DuRVzjWhHyxEv0/g6LBNe3wJIGLmd90stcUS6bFNDy8t5YVbBxijZvTy5lMAq3nAdBFjcusFp3n2uc0oAfhoe86LPME1JpAXyWfPCwKFAs8Li4swnaqJqQAAAABJRU5ErkJggg==",Ws=({siteId:t,value:o,onChange:s})=>{const{t:r}=_(),[n,a]=i.useState([]),[u,c]=i.useState(!1),[p,d]=i.useState(""),[g,l]=i.useState(!1),[f]=E.useForm(),[j,w]=i.useState(!1);i.useEffect(()=>{t!=null&&O(p)},[t,p]);const O=async(A="")=>{try{c(!0);const m={siteId:t};A.trim()&&(m.key=A);const k=await gt(m);a(k.info||[])}catch{M.error("Failed to load labels")}finally{c(!1)}},F=async A=>{try{if(w(!0),n.some(k=>k.name===A.name)){M.error("Label name already exists");return}const m=await rs({site_id:t,name:A.name});(m==null?void 0:m.status)===200?(M.success("Create label success"),O(),l(!1),f.resetFields()):M.error((m==null?void 0:m.info)||"Create label failed")}catch{M.error("Failed to create label")}finally{w(!1)}},b=(A,m)=>{m.stopPropagation();const k=n.find(L=>L.name===A);k&&ye(`Are you sure to delete this label ${A}?`,async()=>{try{const L=await os(k.id);if((L==null?void 0:L.status)===200){M.success("Delete label success");const N=(o||[]).filter(C=>C!==A);s(N),O()}}catch{M.error("Failed to delete label")}})},v=A=>{d(A)},B=A=>{console.log("newValues",A),s(A),p.trim()&&(d(""),O(""))},h=A=>{const m=(o||[]).includes(A.value);return e.jsxs(re,{style:{width:"100%",justifyContent:"space-between",alignItems:"center"},children:[e.jsxs(re,{children:[e.jsx(Rt,{checked:m}),e.jsx("span",{className:"optionLabel",children:A.label})]}),e.jsx("span",{onClick:k=>b(A.value,k),style:{cursor:"pointer"},children:e.jsx("img",{src:Us,alt:"Delete",style:{width:16,height:16,verticalAlign:"middle"}})})]})},y=A=>e.jsxs("div",{children:[A,e.jsx(q,{type:"link",icon:e.jsx(Vt,{}),style:{width:"100%",borderTop:"1px solid #E7E7E7"},onClick:()=>l(!0),children:r("inventory.create_label")})]}),T=n.map(A=>({key:A.id,value:A.name,label:A.name}));return e.jsxs(e.Fragment,{children:[e.jsx(J,{mode:"multiple",style:{width:"100%"},value:o||[],options:T,onChange:B,onSearch:v,loading:u,dropdownRender:y,optionRender:h,disabled:t==null,filterOption:!1,showSearch:!0,popupClassName:"mySelect"}),e.jsx(he,{open:g,title:r("inventory.create_label"),onCancel:()=>{l(!1),f.resetFields()},onFinish:F,form:f,modalClass:"ampcon-middle-modal",children:e.jsx(E.Item,{name:"name",label:r("common.name"),rules:[{required:!0,message:r("form.required")},{validator:(A,m)=>m?/^[a-zA-Z0-9]+$/.test(m)?Promise.resolve():Promise.reject(r("form.invalid_label_name")):Promise.resolve()}],children:e.jsx(Q,{})})})]})},Gs="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAL1JREFUWEftl1ENAkEMRN84QAI4QAIWUAAoAJQACgAFSAAJOOAk4KDQy0L4IbnbS24hab+7nensx0tF4VJhfbINmNkYOAF3YCqpylmmi4HNU3yVRLeS1n0b2APzJHqQtAgDkUAkEAn8dwJmNgSWwKDhJhPA33g5By4N3zk7jpKu3v9mgZndPgY2nJXdVkka/ZwBx+uspy/YvfDdBcdBw0ggEogEIoHih4mj+Jx46KdZjde2lc2CtkLf+osbeAAnJJMhge1VGgAAAABJRU5ErkJggg==",{Option:$e}=J,Js={refresh:D.func.isRequired,entityId:D.string,subId:D.string,deviceClass:D.string,venueId:D.string},Qs={entityId:"",subId:"",deviceClass:"",venueId:""},Qe=({refresh:t,entityId:o,venueId:s,subId:r,deviceClass:n})=>{const{t:a}=_(),[u]=E.useForm(),[c,p]=i.useState(!1),[d,g]=i.useState(!1),[l,f]=i.useState([]),[j,w]=i.useState([]),[O,F]=i.useState(!!s),{data:b}=Re(),{data:v}=Ve();i.useEffect(()=>{b&&f(b)},[b]),i.useEffect(()=>{v&&w(v)},[v]),i.useEffect(()=>{const m=s!=null&&s!=="";F(m),u.setFieldsValue({entity:m?`ven:${s}`:""})},[s,u]);const B=ge(m=>Y.post(`inventory/${m.serialNumber}`,m)),h=m=>{var k;return{serialNumber:m.serialNumber.toLowerCase(),name:m.name,deviceRules:m.deviceRules,deviceType:m.deviceType,devClass:n!==""?n:m.devClass,description:m.description||void 0,notes:m.note?[{note:m.note}]:void 0,entity:m.entity===""||m.entity.split(":")[0]!=="ent"?"":m.entity.split(":")[1],venue:m.entity===""||m.entity.split(":")[0]!=="ven"?"":m.entity.split(":")[1],doNotAllowOverrides:m.doNotAllowOverrides,subscriber:r!==""?r:"",labelsName:(k=m.labelsName)!=null&&k.length?m.labelsName.join(","):""}},y=async m=>{try{g(!0);const k={...m,deviceRules:{rrm:"inherit",rcOnly:"inherit",firmwareUpgrade:"inherit"},devClass:n||"any"},L=h(k);await B.mutateAsync(L,{onSuccess:()=>{M.success(a("crud.success_create_obj",{obj:a("certificates.device")})),t(),p(!1),u.resetFields()},onError:N=>{var C,$;M.error(a("crud.error_create_obj",{obj:a("certificates.device"),e:($=(C=N==null?void 0:N.response)==null?void 0:C.data)==null?void 0:$.ErrorDescription}))}})}finally{g(!1)}},T=()=>{u.resetFields(),p(!1)},A={serialNumber:"",name:"",description:"",deviceType:l[0]||"",deviceRules:{rrm:"inherit",rcOnly:"inherit",firmwareUpgrade:"inherit"},devClass:n||"any",note:"",entity:s!==""&&s!==void 0&&s!==null?`ven:${s}`:"",doNotAllowOverrides:!1,labelsName:""};return e.jsxs(e.Fragment,{children:[e.jsx(q,{type:"primary",onClick:()=>p(!0),style:{display:"flex",alignItems:"center"},icon:e.jsx("img",{src:Gs,alt:"Add",style:{width:16,height:16,marginRight:4}}),children:a("common.create")}),e.jsx(he,{open:c,title:a("common.create"),onCancel:T,onFinish:y,initialValues:A,form:u,modalClass:"ampcon-middle-modal",children:e.jsxs(X,{className:"CreateTagModal",children:[e.jsx(z,{xs:24,children:e.jsx(E.Item,{name:"serialNumber",label:a("inventory.serial_number"),rules:[{required:!0,message:a("form.required")},{validator:(m,k)=>k&&(k.length!==12||!/^[a-fA-F0-9]+$/.test(k))?Promise.reject(new Error(a("inventory.invalid_serial_number"))):Promise.resolve()}],children:e.jsx(Q,{style:{width:"100%"}})})}),e.jsx(z,{xs:24,children:e.jsx(E.Item,{name:"name",label:a("common.name"),rules:[{required:!0,message:a("form.required")},{validator:(m,k)=>k?/^([a-zA-Z0-9]{1,2}|[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])$/.test(k)?Promise.resolve():Promise.reject(new Error(a("inventory.invalid_device_name"))):Promise.resolve()}],children:e.jsx(Q,{style:{width:"100%"}})})}),e.jsx(z,{xs:24,children:e.jsx(E.Item,{name:"deviceType",label:a("inventory.device_type"),rules:[{required:!0,message:a("form.required")}],children:e.jsx(J,{style:{width:"100%"},children:l.map(m=>e.jsx($e,{value:m,children:m},m))})})}),e.jsx(z,{xs:24,children:e.jsx(E.Item,{name:"entity",label:a("inventory.site"),children:e.jsxs(J,{disabled:O,style:{width:"100%"},children:[e.jsx($e,{value:"",children:a("common.none")}),j.map(m=>e.jsx($e,{value:`ven:${m.id}`,children:`${m.name}${m.description?`: ${m.description}`:""}`},`ven:${m.id}`))]})})}),e.jsx(z,{xs:24,children:e.jsx(E.Item,{noStyle:!0,shouldUpdate:(m,k)=>m.entity!==k.entity,children:({getFieldValue:m})=>{const k=m("entity"),L=k!=null&&k.startsWith("ven:")?parseInt(k.split(":")[1]):null;return e.jsx(E.Item,{name:"labelsName",label:a("inventory.label"),children:e.jsx(Ws,{siteId:L,value:u.getFieldValue("labelsName")||[],onChange:N=>u.setFieldsValue({labelsName:N}),className:"custom-label-select",style:{width:"100%"}})})}})}),e.jsx(z,{xs:24,children:e.jsx(E.Item,{name:"doNotAllowOverrides",label:a("overrides.ignore_overrides"),valuePropName:"checked",rules:[{required:!0,message:a("form.required")}],children:e.jsx(pe,{})})}),e.jsx(z,{xs:24,children:e.jsx(E.Item,{name:"description",label:a("common.description"),children:e.jsx(Q,{style:{width:"100%"}})})}),e.jsx(z,{xs:24,children:e.jsx(E.Item,{name:"note",label:a("common.note"),children:e.jsx(Q,{style:{width:"100%"}})})})]})})]})};Qe.propTypes=Js;Qe.defaultProps=Qs;const Ks=({value:t,onChange:o})=>{const{t:s}=_(),{data:r}=Re(),{data:n}=Ve(),[a,u]=i.useState([]);i.useEffect(()=>{(async()=>{if(!(t!=null&&t.venue)){u([]);return}try{const g=await gt({siteId:parseInt(t.venue)});u(((g==null?void 0:g.info)||[]).map(l=>({label:l.name,value:l.name})))}catch{u([])}})()},[t==null?void 0:t.venue]);const c=t!=null&&t.labelsName?t.labelsName.split(",").filter(Boolean):[],p=(d,g)=>{Array.isArray(g.labelsName)&&(g.labelsName=g.labelsName.join(",")),o(g)};return e.jsxs(E,{initialValues:{...t,labelsName:c},onValuesChange:p,className:"wirelessForm InventoryMainForm",children:[e.jsxs(X,{gutter:24,children:[e.jsx(z,{span:12,children:e.jsx(E.Item,{label:s("inventory.serial_number"),name:"serialNumber",rules:[{required:!0,message:s("form.required")}],children:e.jsx(Q,{disabled:!0})})}),e.jsx(z,{span:12,children:e.jsx(E.Item,{label:s("common.name"),name:"name",rules:[{required:!0,message:s("form.required")},{type:"string",max:50,message:s("form.max_length",{max:50})}],children:e.jsx(Q,{})})})]}),e.jsxs(X,{gutter:24,children:[e.jsx(z,{span:12,children:e.jsx(E.Item,{label:s("common.description"),name:"description",rules:[{max:128,message:s("form.max_length",{max:128})}],children:e.jsx(Q,{})})}),e.jsx(z,{span:12,children:e.jsx(E.Item,{label:s("inventory.device_type"),name:"deviceType",rules:[{required:!0,message:s("form.required")}],children:e.jsx(J,{options:r.map(d=>({label:d,value:d})),showSearch:!0})})})]}),e.jsxs(X,{gutter:24,children:[e.jsx(z,{span:12,children:e.jsx(E.Item,{label:"Site",name:"venue",children:e.jsx(J,{options:Array.isArray(n)?n.map(d=>({label:d.name,value:String(d.id),key:d.id})):[],showSearch:!0,optionFilterProp:"label"})})}),e.jsx(z,{span:12,children:e.jsx(E.Item,{label:"Label",name:"labelsName",children:e.jsx(J,{mode:"multiple",options:a,showSearch:!0,optionFilterProp:"label"})})})]}),e.jsx(X,{gutter:24,style:{display:"none"},children:e.jsx(z,{span:12,children:e.jsx(E.Item,{label:s("overrides.ignore_overrides"),name:"doNotAllowOverrides",valuePropName:"checked",rules:[{required:!0,message:s("form.required")}],children:e.jsx(pe,{})})})})]})},{Title:Xs}=P,Hs=({serialNumber:t})=>{const{t:o}=_(),{data:s,isFetching:r,refetch:n}=Ut({serialNumber:t,enabled:!0});return r?e.jsx(Ne,{}):!s||s.config==="none"?e.jsx(ue,{message:o("inventory.no_computed"),type:"info",showIcon:!0,style:{color:"#1677ff",borderColor:"#1677ff"}}):e.jsxs("div",{children:[e.jsxs(Wt,{style:{flexDirection:"column",marginBottom:8},children:[e.jsx(Xs,{level:5,style:{margin:0},children:"Configuration"}),e.jsx(q,{icon:e.jsx(Se,{component:pt}),size:"small",loading:r,onClick:()=>n(),style:{width:100,height:32,marginTop:20,marginBottom:10},children:"Refresh"})]}),e.jsx("div",{style:{border:"1px solid #eee",borderRadius:16,height:"30vh",overflowY:"auto"},children:e.jsx("pre",{style:{margin:0,padding:8},children:JSON.stringify(s.config,null,2)})})]})},Zs={"2G":[1,6,11],"5G":{40:[36,44,52,60,100,108,116,124,132,149,157,165,173,184,192],80:[36,52,100,116,132,149]},"5G-lower":{20:[36,40,44,48,52,56,60,64,100,104,108,112,116,120,124,128,132,136,140,144],40:[38,46,54,63,102,110,118,126,134,142],80:[42,58,106,122,138],160:[50,114]},"5G-upper":{20:[149,153,157,161,165],40:[151,159],80:[155]},"6G":{20:[1,5,9,13,17,21,25,29,33,37,41,45,49,53,57,61,65,69,73,77,81,85,89,93,97,101,105,109,113,117,121,125,129,133,137,141,145,149,153,157,161,165,169,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,233],40:[3,11,19,27,35,43,51,59,67,75,83,91,99,107,115,123,131,139,147,155,163,171,17,187,195,203,211,219,227],80:[7,23,39,55,71,87,103,119,135,151,167,183,199,215],160:[15,47,79,143]}},Ys=()=>{const t=[];for(const[,o]of Object.entries(Zs))if(Array.isArray(o))for(const s of o){const r=s.toString();t.find(n=>n.value===r)||t.push({value:r,label:r})}else for(const[,s]of Object.entries(o))for(const r of s){const n=r.toString();t.find(a=>a.value===n)||t.push({value:n,label:n})}return t.sort((o,s)=>parseInt(o.value,10)-parseInt(s.value,10))},Z={radios:{channel:{name:"channel",label:"overrides.channel",type:"select",defaultValue:"auto",options:[{value:"auto",label:"Auto"},...Ys()],test:()=>{}},"tx-power":{name:"tx-power",label:"overrides.tx_power",type:"integer",defaultValue:10,test:t=>t<1||t>32?"overrides.tx_power_error":void 0}}},en=async t=>Y.get(`configurationOverrides/${t}`).then(({data:o})=>o).catch(o=>{var s;if(((s=o.response)==null?void 0:s.status)===404)return{serialNumber:t,managementPolicy:"",overrides:[]};throw o}),tn=async({data:t,source:o})=>Y.put(`configurationOverrides/${t.serialNumber}?source=${o}`,t),sn=()=>ge(tn),nn=({serialNumber:t,overrides:o,setOverrides:s})=>{const{t:r}=_(),[n,a]=i.useState(null),[u,c]=i.useState(!1),[p,d]=i.useState(!1),[g,l]=i.useState(!1),[f,j]=i.useState(null),[w,O]=i.useState(""),[F,b]=i.useState(""),[v,B]=i.useState({value:"",reason:""}),{user:h}=ke(),[y,T]=i.useState(1),[A,m]=i.useState(10),k=i.useCallback(x=>{T(x.current),m(x.pageSize)},[]),L=[{label:"radios",value:"radios"}],N=[{label:"2G",value:"2G"},{label:"5G",value:"5G"},{label:"6G",value:"6G"}],C=[{label:"channel",value:"channel"},{label:"tx-power",value:"tx-power"}],$={startName:"radios",nameIndex:"2G",endName:"channel",value:Z.radios.channel.defaultValue,reason:""},[V,me]=i.useState({...$}),[H]=E.useForm(),K=i.useCallback(x=>`${x.startName}.${x.nameIndex}.${x.endName}`,[]),ae=i.useCallback(()=>{const x=o.map(I=>I.parameterName);return{startName:[{required:!0,message:r("form.required")},{validator:async()=>{const I=K(V);if(x.includes(I))throw new Error("")}}],nameIndex:[{required:!0,message:r("form.required")},{validator:async()=>{const I=K(V);if(x.includes(I))throw new Error("")}}],endName:[{required:!0,message:r("form.required")},{validator:async()=>{const I=K(V);if(x.includes(I))throw new Error(r("overrides.name_error"))}}],value:[{required:!0,message:r("form.required")},{validator:async(I,W)=>{var U;if(V.endName&&W!==void 0){const ve=(U=Z.radios[V.endName])==null?void 0:U.test;if(ve){const xe=ve(W);if(xe)throw new Error(r(xe)||xe)}}}}],reason:[{max:64,message:r("overrides.reason_error")}]}},[V,o,r,K]),ne=i.useCallback((x,I)=>{me(W=>{const U={...W,[I]:x};return I==="endName"&&(x==="channel"?(U.value=Z.radios.channel.defaultValue,H.setFieldValue("value",U.value)):x==="tx-power"&&(U.value=Z.radios["tx-power"].defaultValue,H.setFieldValue("value",U.value))),U})},[H]);oe.useEffect(()=>{H.validateFields(["startName","nameIndex","endName"])},[V.startName,V.nameIndex,V.endName,H]);const ie=i.useCallback(async x=>{const U=[{parameterName:`${x.startName}.${x.nameIndex}.${x.endName}`,parameterValue:x.value,source:h==null?void 0:h.userRole,reason:x.reason,modified:Math.floor(new Date().getTime()/1e3),parameterType:x.endName==="tx-power"?"integer":"string"},...o];s(U),d(!1),me({...$}),H.resetFields()},[o,h==null?void 0:h.userRole,H,s]),Te=i.useCallback(async x=>{if(!f)return;const I=o.map(W=>W.parameterName===f.parameterName&&W.source===f.source?{...W,parameterValue:w,reason:F}:W);s(I),l(!1)},[f,w,F,o,s,r]),Ee=i.useCallback(x=>{ye("Are you sure you want to delete this Configuration Override?",()=>{const I=o.filter(W=>W.parameterName!==x.parameterName||W.source!==x.source);s(I)})},[o,s,r]);i.useEffect(()=>{let x=!0;return t&&(c(!0),en(t).then(I=>{x&&(a(I),I!=null&&I.overrides&&s(I.overrides))}).finally(()=>{x&&c(!1)})),()=>{x=!1}},[t,s]);const De=[{title:r("overrides.source"),dataIndex:"source",key:"source",sorter:(x,I)=>(x.source||"").localeCompare(I.source||"")},{title:r("common.name"),dataIndex:"parameterName",key:"parameterName",sorter:(x,I)=>(x.parameterName||"").localeCompare(I.parameterName||"")},{title:r("overrides.value"),dataIndex:"parameterValue",key:"parameterValue",sorter:(x,I)=>(x.parameterValue||"").toString().localeCompare((I.parameterValue||"").toString())},{title:r("overrides.reason"),dataIndex:"reason",key:"reason",sorter:(x,I)=>(x.reason||"").localeCompare(I.reason||"")},{title:"Operation",key:"action",render:(x,I)=>e.jsxs(re,{size:24,children:[e.jsx(q,{type:"text",style:{padding:0},onClick:()=>{j(I),O(I.parameterValue),b(I.reason),B({value:"",reason:""}),l(!0)},children:r("crud.edit")}),e.jsx(q,{type:"text",style:{padding:0},onClick:()=>Ee(I),children:r("crud.delete")})]})}],Oe=i.useCallback((x,I,W)=>W==="channel"?e.jsx(J,{value:x,style:{minWidth:100},onChange:I,children:Z.radios.channel.options.map(U=>e.jsx(J.Option,{value:U.value,children:U.label},U.value))}):W==="tx-power"?e.jsx(rt,{value:x,min:Z.radios["tx-power"].min,max:Z.radios["tx-power"].max,style:{minWidth:100},onChange:I}):e.jsx(Q,{value:x,style:{minWidth:100},onChange:U=>I(U.target.value)}),[]);return e.jsxs("div",{children:[e.jsx(re,{style:{marginBottom:20},children:e.jsx(q,{type:"primary",icon:e.jsx(Se,{component:ht}),onClick:()=>d(!0),children:"Create"})}),u?e.jsx(Ne,{}):e.jsx(Ie,{columns:De,dataSource:o,rowKey:x=>x.parameterName+x.source+(x.modified||""),pagination:{current:y,pageSize:A,showTotal:x=>`Total ${x} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["5","10","20","50"],onChange:k},size:"small",scroll:{y:300}}),e.jsx(he,{open:g,title:"Edit",onCancel:()=>l(!1),onFinish:Te,modalClass:"ampcon-middle-modal configOverridesForm",children:f&&e.jsxs(e.Fragment,{children:[e.jsx(X,{gutter:24,children:e.jsx(z,{span:18,children:e.jsx(E.Item,{label:r("overrides.parameter"),children:e.jsx(Q,{value:f.parameterName,type:"hidden"})})})}),e.jsx(X,{gutter:24,style:{marginBottom:16,marginTop:-20},children:e.jsx(z,{span:18,children:f.parameterName})}),e.jsx(X,{gutter:24,children:e.jsx(z,{span:18,children:e.jsxs(E.Item,{label:r("overrides.value"),required:!0,children:[Oe(w,O,f.parameterName.split(".").pop()||""),v.value&&e.jsx("div",{style:{color:"red",fontSize:12},children:v.value})]})})}),e.jsx(X,{gutter:24,children:e.jsx(z,{span:18,children:e.jsxs(E.Item,{label:r("overrides.reason"),children:[e.jsx(Q.TextArea,{value:F,rows:2,onChange:x=>b(x.target.value)}),v.reason&&e.jsx("div",{style:{color:"red",fontSize:12},children:v.reason})]})})})]})}),e.jsxs(he,{open:p,title:"Create",onCancel:()=>{d(!1),me({...$}),H.resetFields()},onFinish:ie,initialValues:$,form:H,modalClass:"ampcon-middle-modal configOverridesForm",children:[e.jsx(X,{gutter:24,children:e.jsx(z,{span:18,children:e.jsx(E.Item,{label:r("overrides.parameter"),required:!0,children:e.jsxs(re.Compact,{block:!0,children:[e.jsx(E.Item,{noStyle:!0,name:"startName",rules:ae().startName,children:e.jsx(J,{options:L,style:{width:88,marginRight:8},onChange:x=>ne(x,"startName")})}),e.jsx(E.Item,{noStyle:!0,name:"nameIndex",rules:ae().nameIndex,children:e.jsx(J,{options:N,style:{width:78,marginRight:8},onChange:x=>ne(x,"nameIndex")})}),e.jsx(E.Item,{noStyle:!0,name:"endName",rules:ae().endName,children:e.jsx(J,{options:C,style:{width:98},onChange:x=>ne(x,"endName")})})]})})})}),e.jsx(X,{gutter:24,children:e.jsx(z,{span:18,children:e.jsx(E.Item,{label:r("overrides.value"),name:"value",rules:ae().value,required:!0,children:V.endName==="channel"?e.jsx(J,{onChange:x=>ne(x,"value"),children:Z.radios.channel.options.map(x=>e.jsx(J.Option,{value:x.value,children:x.label},x.value))}):V.endName==="tx-power"?e.jsx(rt,{min:Z.radios["tx-power"].min,max:Z.radios["tx-power"].max,onChange:x=>ne(x,"value")}):null})})}),e.jsx(X,{gutter:24,children:e.jsx(z,{span:18,children:e.jsx(E.Item,{label:r("overrides.reason"),name:"reason",rules:ae().reason,children:e.jsx(Q.TextArea,{rows:2,onChange:x=>ne(x.target.value,"reason")})})})})]})]})},rn=({oldNotes:t,setNotes:o})=>{const{t:s}=_(),{user:r}=ke(),[n,a]=i.useState(""),[u,c]=i.useState(!1),[p,d]=i.useState([...t||[]]),[g,l]=i.useState([]),f=()=>{if(!n)return;const b={note:n,isNew:!0,createdBy:r.email,created:Math.floor(new Date().getTime()/1e3)};l(v=>{const B=[...v,b];return o(B),B}),d(v=>[...v,b]),a(""),c(!1)},j=[{title:"Date",dataIndex:"created",key:"created",render:b=>b?new Date(b*1e3).toLocaleString():"",sorter:(b,v)=>b.created-v.created},{title:"Note",dataIndex:"note",key:"note",sorter:(b,v)=>(b.note||"").localeCompare(v.note||"")},{title:"By",dataIndex:"createdBy",key:"createdBy",sorter:(b,v)=>(b.createdBy||"").localeCompare(v.createdBy||"")}],[w,O]=i.useState({current:1,pageSize:10}),F=p.sort((b,v)=>v.created-b.created).slice((w.current-1)*w.pageSize,w.current*w.pageSize);return e.jsxs("div",{children:[e.jsx(re,{style:{marginBottom:20},children:e.jsx(q,{type:"primary",icon:e.jsx(Se,{component:ht}),onClick:()=>c(!0),children:"Create Note"})}),e.jsx(Ie,{columns:j,dataSource:F,rowKey:()=>be(),pagination:{current:w.current,pageSize:w.pageSize,total:p.length,onChange:(b,v)=>O({current:b,pageSize:v}),showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:[5,10,20,50],showTotal:b=>`Total ${b} items`},size:"small",scroll:{y:200},className:"NoteTable"}),e.jsx(he,{open:u,title:"Create Note ",onCancel:()=>{c(!1),a("")},onFinish:f,modalClass:"ampcon-middle-modal",children:e.jsx(E.Item,{label:"Note",name:"note",rules:[{required:!0,message:s("form.required")}],children:e.jsx(Q.TextArea,{value:n,onChange:b=>a(b.target.value),style:{width:220}})})})]})},on=({tag:t,refresh:o,onClose:s,open:r=!0})=>{const[n,a]=i.useState("main"),[u,c]=i.useState({...t}),[p,d]=i.useState([]),[g,l]=i.useState([]);oe.useEffect(()=>{a("main"),c({...t}),d([]),l([])},[t]);const f=ge(O=>Y.put(`inventory/${t==null?void 0:t.serialNumber}`,O)),j=sn(),w=async()=>{var O;await f.mutateAsync({...u,devClass:"any",notes:p}),g&&g.length>0&&await j.mutateAsync({data:{serialNumber:t==null?void 0:t.serialNumber,overrides:g,managementPolicy:(t==null?void 0:t.managementPolicy)||""},source:((O=g[0])==null?void 0:O.source)||"User"}),M.success("Update successfully"),o(),s()};return e.jsxs(Ce,{open:r,onCancel:s,destroyOnClose:!0,title:e.jsxs("div",{children:[`Edit ${t==null?void 0:t.serialNumber}`," ",e.jsx(te,{style:{marginTop:8,marginBottom:0}})]}),footer:e.jsxs(e.Fragment,{children:[e.jsx(te,{}),e.jsxs("div",{className:"foot-btns",children:[e.jsx(q,{onClick:s,children:"Cancel"}),e.jsx(q,{type:"primary",onClick:w,children:"Apply"})]})]}),className:"ampcon-max-modal",children:[e.jsxs(fe.Group,{value:n,onChange:O=>a(O.target.value),className:"inventoryEditFormTabs",style:{marginTop:8},children:[e.jsx(fe.Button,{value:"main",children:"Main"}),e.jsx(fe.Button,{value:"computed",children:"Computed Config"}),e.jsx(fe.Button,{value:"overrides",children:"Config Overrides"}),e.jsx(fe.Button,{value:"notes",children:"Notes"})]}),e.jsxs("div",{style:{marginTop:24},children:[e.jsx("div",{style:{display:n==="main"?"block":"none"},children:e.jsx(Ks,{value:u,onChange:c},(t==null?void 0:t.serialNumber)+"-main")}),e.jsx("div",{style:{display:n==="computed"?"block":"none"},children:e.jsx(Hs,{serialNumber:t==null?void 0:t.serialNumber},(t==null?void 0:t.serialNumber)+"-computed")}),e.jsx("div",{style:{display:n==="overrides"?"block":"none"},children:e.jsx(nn,{serialNumber:t==null?void 0:t.serialNumber,overrides:g,setOverrides:l},(t==null?void 0:t.serialNumber)+"-overrides")}),e.jsx("div",{style:{display:n==="notes"?"block":"none"},children:e.jsx(rn,{oldNotes:t==null?void 0:t.notes,setNotes:d},(t==null?void 0:t.serialNumber)+"-notes")})]})]})};function an(t,o){return se.parse(t,o)}function ln(t,o){se.parse(t,Object.assign({},{download:!0},o))}function cn(t,o){return o===void 0&&(o={}),se.unparse(t,o)}function dn(){return{readString:an,readRemoteFile:ln,jsonToCSV:cn}}se.BAD_DELIMITERS;se.RECORD_SEP;se.UNIT_SEP;se.WORKERS_SUPPORTED;se.LocalChunkSize;se.DefaultDelimiter;const un="/ampcon/wireless";function mn(){return Ue({url:`${un}/inventory/import_template`,method:"GET",responseType:"blob"})}const pn=t=>t.replace(/"/g,"").trim(),hn=async t=>new Promise(o=>{const s=new FileReader;s.readAsText(t),s.onload=({target:{result:r=null}})=>o(r),s.onerror=()=>o(null)}),gn={setPhase:D.func.isRequired,setDevices:D.func.isRequired,setIsCloseable:D.func.isRequired,refreshId:D.string.isRequired},xt=({setPhase:t,setDevices:o,setIsCloseable:s,refreshId:r})=>{const{t:n}=_(),[a,u]=i.useState(null),{readString:c}=dn(),p=async l=>{u({isLoading:!0});const f=await hn(l);if(f===null)u({error:n("generalErrorParsingFile")||"General error while parsing file"});else{const w=c(f,{header:!0,transformHeader:pn,skipEmptyLines:!0,quoteChar:'"'});w.errors.length>0?u({error:`Error on row ${w.errors[0].row}: ${w.errors[0].message}`}):u({deviceList:w.data})}},d=l=>{var f;s(!1),((f=l.target.files)==null?void 0:f.length)>0&&p(l.target.files[0])},g=l=>{l.preventDefault(),mn().then(f=>{const j=window.URL.createObjectURL(f),w=document.createElement("a");w.href=j,w.download="inventory_import_template.csv",document.body.appendChild(w),w.click(),w.remove(),window.URL.revokeObjectURL(j)}).catch(f=>{if(f.response&&f.response.data){const j=new FileReader;j.onload=function(){try{const w=JSON.parse(j.result);M.error(w.info||j.result)}catch{M.error(j.result)}},j.readAsText(f.response.data)}else M.error(f.message||"Network Error")})};return e.jsxs("div",{style:{padding:"0 0 0 0"},children:[e.jsx("style",{jsx:!0,global:!0,children:`
        .custom-alert .ant-alert-icon {
          color: #367EFF !important;
        }
      `}),e.jsx(ue,{className:"custom-trace-alert",message:"Note:"+n("devices.import_device_warning"),type:"info",showIcon:!0,closable:!0,style:{marginBottom:30}}),e.jsxs("div",{style:{marginBottom:"30px",fontSize:"14px",textAlign:"left",paddingLeft:0},children:[e.jsx(P.Text,{children:"To bulk import devices, you need to use a CSV file with the following columns: "}),e.jsx(P.Text,{strong:!0,children:"SerialNumber，DeviceType，Name，Label，Description， Note. "}),e.jsx(P.Text,{children:"Download device import template "}),e.jsx(P.Text,{}),e.jsxs("a",{href:"#",onClick:g,style:{color:"#14C9BB",textDecoration:"underline"},children:["(","template.csv",")"]}),e.jsx(P.Text,{})]}),e.jsx(Q,{style:{width:"25%",marginBottom:"20px",textAlign:"left",paddingLeft:5},type:"file",accept:".csv",onChange:d,placeholder:n("noFileSelected")||"No file selected"},r),(a==null?void 0:a.error)&&e.jsx(ue,{message:a.error,type:"error",showIcon:!0,style:{marginTop:"20px"}}),e.jsx("br",{}),e.jsx(q,{type:"primary",style:{marginTop:"20px",textAlign:"left",width:"136px"},onClick:()=>{o(a.deviceList),t(1)},disabled:!(a!=null&&a.deviceList),children:n("devices.test_batch")||"Test Import Data"})]})};xt.propTypes=gn;const xn={devices:D.shape({newDevices:D.instanceOf(Array).isRequired,devicesToUpdate:D.instanceOf(Array).isRequired}).isRequired,refresh:D.func.isRequired,deviceClass:D.string.isRequired,parent:D.instanceOf(Object).isRequired,setIsCloseable:D.func.isRequired,venueId:D.string.isRequired,handleRefresh:D.func.isRequired},je=({dataSource:t,columns:o,showPagination:s=!0,rowKey:r="SerialNumber"})=>{const{t:n}=_(),a=s?{total:t.length,pageSizeOptions:["10","20","30","40","50"],showSizeChanger:!0,showQuickJumper:!0,showTotal:(u,c)=>`${c[0]}-${c[1]} of ${u} items`}:!1;return e.jsx(Ie,{style:{maxHeight:"200px",overflow:"auto"},dataSource:t,columns:o,pagination:a,rowKey:r,siaze:"small"})},ft=({devices:t,refresh:o,deviceClass:s,parent:r,setIsCloseable:n,venueId:a,handleRefresh:u})=>{const{t:c}=_(),{token:p}=ke(),[d,g]=i.useState({isLoading:!1}),l=(b,v)=>{const B={headers:{Accept:"application/json",Authorization:`Bearer ${p}`},cancelToken:v.token};return ee.post(`${Y.defaults.baseURL}/inventory/${b.serialNumber}`,b,B).then(()=>({success:!0})).catch(h=>{var y,T;return ee.isCancel(h)?{success:!1,stop:!0}:{success:!1,error:((T=(y=h.response)==null?void 0:y.data)==null?void 0:T.ErrorDescription)??"Unknown Error"}})},f=(b,v)=>{const B={headers:{Accept:"application/json",Authorization:`Bearer ${p}`},cancelToken:v.token};return ee.put(`${Y.defaults.baseURL}/inventory/${b.serialNumber}`,b,B).then(()=>({success:!0})).catch(h=>{var y,T;return ee.isCancel(h)?{success:!1,stop:!0}:{success:!1,error:((T=(y=h.response)==null?void 0:y.data)==null?void 0:T.ErrorDescription)??"Unknown Error"}})},j=async(b,v)=>{const B={headers:{Accept:"application/json",Authorization:`Bearer ${p}`}},h={site_id:b,name:v};try{return(await ee.post("/ampcon/wireless/site/labels",h,B)).data}catch(y){return console.error("Failed to create labels:",y),{status:500,info:"Failed to create labels"}}},w=async b=>{g({isLoading:!0});const v={entity:"",venue:a=="all"?"":a,subscriber:"",devClass:s,...r},B=t.newDevices.length+t.devicesToUpdate.length,h=[],y=[],T=[],A=[],m=new Set,k=L=>{var C;const N=(C=L.Label)==null?void 0:C.replace(/\,/g,"$");m.add(N)};if(t.newDevices.forEach(k),t.devicesToUpdate.forEach(k),m.size>0&&a!=="all"){const L=Array.from(m);await j(a,L)}for(let L=0;L<t.newDevices.length;L+=1){const N=t.newDevices[L];g({isLoading:!0,treating:N.SerialNumber,percentTreated:Math.floor(Math.max(L-1,0)/B*100)});const C={...v,serialNumber:N.SerialNumber,deviceType:N.DeviceType,name:N.Name.length>0?N.Name:N.SerialNumber,description:N.Description,notes:N.Note!==""?[{note:N.Note}]:void 0,labelsName:N.Label},$=await l(C,b);if($.stop)break;$.success?h.push(N):$.success||y.push({...N,error:$.error})}for(let L=0;L<t.devicesToUpdate.length;L+=1){const N=t.devicesToUpdate[L];g({isLoading:!0,treating:N.SerialNumber,percentTreated:Math.floor(Math.max(L-1,0)/B*100)});const C={...v,serialNumber:N.SerialNumber,name:N.Name.length>0?N.Name:N.SerialNumber,labelsName:N.Label,description:N.Description,notes:N.Note!==""?[{note:N.Note}]:void 0},$=await f(C,b);if($.stop)break;$.success?T.push(N):$.success||A.push({...N,error:$.error})}g({isLoading:!1,isFinished:!0,successPost:h,errorPost:y,successPut:T,errorPut:A}),n(!0),o(),u()};i.useEffect(()=>{var B,h;const v=ee.CancelToken.source();return(((B=t==null?void 0:t.newDevices)==null?void 0:B.length)>0||((h=t==null?void 0:t.devicesToUpdate)==null?void 0:h.length)>0)&&w(v),()=>{v.cancel("axios request cancelled")}},[t]);const O=i.useMemo(()=>[{title:c("inventory.serial_number")||"Serial Number",dataIndex:"SerialNumber",key:"SerialNumber",sorter:(b,v)=>b.SerialNumber.localeCompare(v.SerialNumber)}],[c]),F=i.useMemo(()=>[...O,{title:c("common.error")||"Error",dataIndex:"error",key:"error",sorter:(b,v)=>b.error.localeCompare(v.error),render:b=>e.jsx(P.Text,{type:"danger",children:b})}],[O,c]);return d!=null&&d.isLoading?e.jsxs("div",{style:{padding:"24px",marginTop:"130px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx(ze,{percent:(d==null?void 0:d.percentTreated)??0,showInfo:!1,style:{flex:1,marginBottom:0},status:(d==null?void 0:d.percentTreated)!==100?"active":"success",strokeColor:(d==null?void 0:d.percentTreated)===100?"#14C9BB":void 0,strokeWidth:12}),e.jsxs(P.Title,{level:5,style:{margin:"0 0 0px 0",width:50,fontWeight:"bold",marginLeft:10},children:[Math.round(d.percentTreated||0),"%"]})]}),e.jsxs(P.Title,{level:5,style:{marginTop:"16px",marginBottom:"0",fontWeight:"bold"},children:[c("devices.treating")," ",d==null?void 0:d.treating]})]}):d!=null&&d.isFinished?e.jsxs("div",{style:{padding:0},children:[d.successPost.length>0&&e.jsxs("div",{style:{border:"2px solid #36d399",borderRadius:5,padding:8,marginBottom:16},children:[e.jsxs(P.Text,{strong:!0,style:{display:"block",marginBottom:12},children:[d.successPost.length," ",c("devices.create_success")]}),e.jsx(je,{dataSource:d.successPost,columns:O,showPagination:!0})]}),d.errorPost.length>0&&e.jsxs("div",{style:{border:"2px solid #f87171",borderRadius:5,padding:8,marginBottom:16},children:[e.jsxs(P.Text,{strong:!0,style:{display:"block",marginBottom:12},children:[d.errorPost.length," ",c("devices.create_errors")]}),e.jsx(je,{dataSource:d.errorPost,columns:F,showPagination:!0})]}),d.successPut.length>0&&e.jsxs("div",{style:{border:"2px solid #36d399",borderRadius:5,padding:8,marginBottom:16},children:[e.jsxs(P.Text,{strong:!0,style:{display:"block",marginBottom:12},children:[d.successPut.length," ",c("devices.update_success")]}),e.jsx(je,{dataSource:d.successPut,columns:O,showPagination:!0})]}),d.errorPut.length>0&&e.jsxs("div",{style:{border:"2px solid #f87171",borderRadius:5,padding:8,marginBottom:16},children:[e.jsxs(P.Text,{strong:!0,style:{display:"block",marginBottom:12},children:[d.errorPut.length," ",c("devices.update_error")]}),e.jsx(je,{dataSource:d.errorPut,columns:F,showPagination:!0})]})]}):null};ft.propTypes=xn;const fn={setPhase:D.func.isRequired,setDevicesToImport:D.func.isRequired,devicesToTest:D.arrayOf(D.instanceOf(Object)).isRequired,venueId:D.string.isRequired},we=({dataSource:t,columns:o,showPagination:s=!0,rowKey:r="SerialNumber"})=>{const{t:n}=_(),a=s?{total:t.length,pageSizeOptions:["10","20","30","40","50"],showSizeChanger:!0,showQuickJumper:!0,showTotal:(u,c)=>`${c[0]}-${c[1]} of ${u} items`}:!1;return e.jsx(Ie,{dataSource:t,columns:o,pagination:a,rowKey:r,style:{maxHeight:"200px",overflow:"auto"},siaze:"small"})},yt=({devicesToTest:t,setPhase:o,setDevicesToImport:s,venueId:r})=>{const{t:n}=_(),{token:a}=ke(),{data:u}=Re(),[c,p]=i.useState(!1),[d,g]=i.useState(!1),[l,f]=i.useState({isLoading:!1});console.log(l);const j=i.useMemo(()=>[{title:n("inventory.serial_number"),dataIndex:"SerialNumber",key:"SerialNumber",sorter:(h,y)=>h.SerialNumber.localeCompare(y.SerialNumber)},{title:n("inventory.device_type"),dataIndex:"DeviceType",key:"DeviceType",sorter:(h,y)=>h.DeviceType.localeCompare(y.DeviceType)},{title:n("Name")||"Name",dataIndex:"Name",key:"Name",sorter:(h,y)=>h.Name.localeCompare(y.Name)},{title:n("inventory.label"),dataIndex:"Label",key:"Label",sorter:(h,y)=>h.Label.localeCompare(y.Label)},{title:n("Description"),dataIndex:"Description",key:"Description",sorter:(h,y)=>h.Description.localeCompare(y.Description)},{title:n("Note"),dataIndex:"Note",key:"Note",sorter:(h,y)=>h.Note.localeCompare(y.Note)}],[n]),w=i.useMemo(()=>[{title:n("inventory.serial_number"),dataIndex:"SerialNumber",key:"SerialNumber",sorter:(h,y)=>h.SerialNumber.localeCompare(y.SerialNumber)},{title:n("inventory.device_type"),dataIndex:"DeviceType",key:"DeviceType",sorter:(h,y)=>h.DeviceType.localeCompare(y.DeviceType)},{title:n("Name"),dataIndex:"Name",key:"Name",sorter:(h,y)=>h.Name.localeCompare(y.Name)},{title:n("Label"),dataIndex:"Label",key:"Label",sorter:(h,y)=>h.Label.localeCompare(y.Label)},{title:n("common.error"),dataIndex:"error",key:"error",sorter:(h,y)=>h.error.localeCompare(y.error)}],[n]),O=(h,y)=>{const T={found:!1,alreadyAssigned:!1},A={headers:{Accept:"application/json",Authorization:`Bearer ${a}`},cancelToken:y.token};return ee.get(`${Y.defaults.baseURL}/inventory/${h.SerialNumber}`,A).then(m=>(m.data.venue!==""||m.data.entity!==""||m.data.subscriber!==""?T.alreadyAssigned=!0:T.foundUnassigned=!0,T)).catch(m=>ee.isCancel(m)?{stop:!0}:T)},F=(h,y)=>{if(h.SerialNumber===""||h.SerialNumber.length!==12||!h.SerialNumber.match("^[a-fA-F0-9]+$"))return n("devices.invalid_serial_number");if(!u.find(T=>T===h.DeviceType))return n("devices.device_type_not_found");if(y.find(T=>h.SerialNumber===T))return n("devices.duplicate_serial");if(!h.Name||h.Name.trim()==="")return n("Name not found");if(h.Label){const T=h.Label.replace(/,/g,"");if(!/^[a-zA-Z0-9]+$/.test(T))return n("Invalid Label (only numbers and letters are allowed)")}return null},b=async h=>{var L;if(f({isLoading:!0}),t.length>0){const N=t[0];if(!["Serial Number","Device Type","Name","Label","Description","Note"].every(V=>Object.prototype.hasOwnProperty.call(N,V))){f({isLoading:!1,isFinished:!0,newDevices:[],foundNotAssigned:[],foundAssigned:[],fileErrors:[{"Serial Number":"","Device Type":"",Name:"",error:n("Error importing template")}]});return}}const y=[],T=[],A=[],m=[],k=[];for(let N=0;N<t.length;N+=1){const C=t[N];C.SerialNumber||(C.SerialNumber=C["Serial Number"]),C.DeviceType||(C.DeviceType=C["Device Type"]),C.Name||(C.Name=C.Name),C.Label||(C.Label=C.Label||""),C.Label&&(C.Label=(L=C.Label)==null?void 0:L.replace(/\$/g,",")),C.Description||(C.Description=C.Description||""),C.Note||(C.Note=C.Note||""),f({isLoading:!0,treating:C.SerialNumber,percentTreated:Math.floor(Math.max(N-1,0)/t.length*100)});const $=F(C,y);if($)k.push({...C,error:$});else{const V=await O(C,h);if(V.stop)break;V.alreadyAssigned?m.push(C):V.foundUnassigned?A.push(C):T.push(C)}y.push(C.SerialNumber)}f({isLoading:!1,isFinished:!0,newDevices:T,foundNotAssigned:A,foundAssigned:m,fileErrors:k})},v=()=>{var A,m,k;const h=((A=l==null?void 0:l.newDevices)==null?void 0:A.length)??-1,y=((m=l==null?void 0:l.foundNotAssigned)==null?void 0:m.length)??-1,T=((k=l==null?void 0:l.foundAssigned)==null?void 0:k.length)??-1;return h>0||y>0&&d||T>0&&c},B=()=>{const h=c?l.foundAssigned:[],y=d?l.foundNotAssigned:[];s({newDevices:l.newDevices,devicesToUpdate:[...h,...y]}),o(2)};return i.useEffect(()=>{const y=ee.CancelToken.source();return t.length>0&&b(y),()=>{y.cancel("axios request cancelled")}},[t]),l!=null&&l.isLoading?e.jsxs("div",{style:{padding:"24px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",marginTop:"130px"},children:[e.jsx(ze,{percent:(l==null?void 0:l.percentTreated)??0,showInfo:!1,style:{flex:1,marginBottom:0},status:(l==null?void 0:l.percentTreated)!==100?"active":"success",strokeColor:(l==null?void 0:l.percentTreated)===100?"#14C9BB":void 0,strokeWidth:12}),e.jsxs(P.Title,{level:5,style:{margin:"0 0 0px 0",width:50,fontWeight:"bold",marginLeft:10},children:[Math.round(l.percentTreated||0),"%"]})]}),e.jsxs(P.Title,{level:5,style:{marginTop:"16px",marginBottom:"0",fontWeight:"bold"},children:[n("devices.treating")," ",l==null?void 0:l.treating]})]}):l!=null&&l.isFinished?e.jsxs("div",{style:{padding:"0"},children:[e.jsx(P.Title,{level:5,style:{margin:"0 0 16px 0"},children:n("devices.test_results")}),l.newDevices.length>0&&e.jsxs("div",{style:{border:"2px solid #36d399",borderRadius:"5px",padding:"8px",marginBottom:"16px"},children:[e.jsxs(P.Text,{strong:!0,style:{display:"block",marginBottom:"8px"},children:[l.newDevices.length," ",n("devices.new_devices")]}),e.jsx(we,{dataSource:l.newDevices,columns:j,showPagination:!0})]}),l.foundNotAssigned.length>0&&e.jsxs("div",{style:{border:"2px solid #fbbf24",borderRadius:"5px",padding:"8px",marginBottom:"16px"},children:[e.jsxs(P.Text,{strong:!0,style:{display:"block",marginBottom:"8px"},children:[l.foundNotAssigned.length," ",n("devices.found_not_assigned")]}),e.jsx(we,{dataSource:l.foundNotAssigned,columns:j,showPagination:!0})]}),l.foundAssigned.length>0&&e.jsxs("div",{style:{border:"2px solid #fbbf24",borderRadius:"5px",padding:"8px",marginBottom:"16px"},children:[e.jsxs(P.Text,{strong:!0,style:{display:"block",marginBottom:"8px"},children:[l.foundAssigned.length," ",n("devices.found_assigned")]}),e.jsx(we,{dataSource:l.foundAssigned,columns:j,showPagination:!0})]}),l.fileErrors.length>0&&e.jsxs("div",{style:{border:"2px solid #f87171",borderRadius:"5px",padding:"8px",marginBottom:"16px"},children:[e.jsxs(P.Text,{strong:!0,style:{display:"block",marginBottom:"8px"},children:[l.fileErrors.length," ",n("devices.file_errors")]}),e.jsx(we,{dataSource:l.fileErrors,columns:w,showPagination:!0})]}),l.foundNotAssigned.length>0&&e.jsxs("div",{style:{marginTop:"0px"},children:[e.jsx("div",{children:e.jsx(P.Text,{style:{color:"red"},children:"Reassign devices which already exist and are owned by another site?"})}),e.jsx("div",{style:{marginTop:"5px",marginBottom:"67px"},children:e.jsx(pe,{checked:d,onChange:g})})]}),l.foundAssigned.length>0&&e.jsxs("div",{style:{marginTop:"16px"},children:[e.jsx("div",{children:e.jsx(P.Text,{style:{color:"red"},children:n("devices.reassign_already_owned")})}),e.jsx("div",{style:{marginTop:"5px",marginBottom:"67px"},children:e.jsx(pe,{checked:c,onChange:p})})]}),e.jsx("div",{style:{display:"flex",justifyContent:"left",gap:"8px",marginTop:"40px",marginBottom:"46px"},children:e.jsx(q,{type:"primary",onClick:B,disabled:!v(),style:{minWidth:"160px"},children:n("devices.start_import")})})]}):null};yt.propTypes=fn;const yn="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAQCAYAAADwMZRfAAAAAXNSR0IArs4c6QAAAlJJREFUOE+tlE9IFFEcx7+/N2ttrCjam4pELTpGhWTOWhRM7bgUeegiBXUQicpTIEWd0ktQeAki6yAGQYGXgiBwZ2WSstxpMSIiEKQsEFZn/UdrbjrzKyVtyuYQ9E4P3uf3+b3fe7/3CP9hUKDDskJyXX4/QBsBzijTZGfi8dzf+NUSywqpkYVLzHwBoKLlIALyHtCenQ21QtcX/LLfJek7BdLd2gnQKWZOCEGdAA+xiwoW1ExAHOB7jvKhCdVn5n0JfjnlQPI4iB8w841stO78n1uXqcQ1gC7+KO+co9XdXi3p7lbUypIhBkKOZlQGnZVMmcMAIs7IZBkaGtxFbqmcUruvnLx8GxE1Auh1NCMWKLFNE4wYMW7Nu+L61L5DI6S+MKtYwVMmhInxWgilZWzPwf4gifqy9zCT2wqi3QBy7CFKMmUmAdSwBy1ba7z3Bxf292wIK3RXQDSORWMZ/5r6yqxiD30AD5IcML+CKOlosXo/VGr3lAsWbwEUA5j2yNsxURP/7GdkynwEUB3JVGKUQSNZzahdBjanH8tv7tphf58APLNmNr9tVK93ljmZMgcB3kTr7cRVYroM4D4xOsajxvPSgSdFgpQOl0WHQngGphMgPurxfPNE9MiMavceYM89DaKTIL5CsLrCMlx2E4KafmZoczSjdXFekjaLFRdTroudk3uNxdIg7WQLmNuXWI87nbmCsysdW5I2KxRP7KKIsMa361+CJOo7q5Bznu4K781ktfFppU+CrnOL1RXORcofRnLKsY+6PhfEBb/if/givgP+4O/KewNXegAAAABJRU5ErkJggg==",bn={refresh:D.func.isRequired,handleRefresh:D.func.isRequired,deviceClass:D.string.isRequired,parent:D.shape({entity:D.string,venue:D.string})},vn={parent:{}},at=()=>window.location.hash?window.location.hash.replace("#",""):"",Ke=({refresh:t,handleRefresh:o,deviceClass:s,parent:r})=>{const{t:n}=_(),[a,u]=i.useState(be()),[c,p]=i.useState(0),[d,g]=i.useState(!0),[l,f]=i.useState(!1),[j,w]=i.useState(!1),[O,F]=i.useState([]),[b,v]=i.useState({}),B=()=>{switch(c){case 0:return e.jsx(xt,{setPhase:p,setDevices:F,setIsCloseable:g,refreshId:a});case 1:return e.jsx(yt,{setPhase:p,devicesToTest:O,setDevicesToImport:v,setIsCloseable:g,venueId:at()});case 2:return e.jsx(ft,{devices:b,refresh:t,deviceClass:s,parent:r,setIsCloseable:g,venueId:at(),handleRefresh:o});default:return null}},h=()=>{p(0),u(be()),g(!0),F([]),v([]),f(!0)},y=()=>d?f(!1):w(!0),T=()=>{w(!1),f(!1)},A=()=>{w(!1)};return e.jsxs(e.Fragment,{children:[e.jsx(ct,{title:n("devices.import_batch_tags")||"Import Batch Tags",placement:"top",mouseEnterDelay:.3,children:e.jsx(q,{htmlType:"button",onClick:h,style:{display:"flex",alignItems:"center"},icon:e.jsx("img",{src:yn,alt:"upload",style:{width:16,height:16}}),children:"Upload"})}),e.jsxs(Ce,{style:{borderRadius:"8px",minHeight:"500px",padding:"15px 20px"},title:e.jsx("div",{style:{fontSize:"20px",fontWeight:"bold",color:"#222",marginTop:"0px !important",height:"27px",display:"flex",alignItems:"center",margin:"0px 0px 15px 0"},children:e.jsx("span",{children:n("devices.import_batch_tags")||"Import Batch Tags"})}),open:l,onCancel:y,width:1360,footer:null,children:[e.jsx(te,{style:{margin:"16 0 16px 0",width:"calc(100% + 48px)",marginLeft:"-24px"}}),e.jsx("div",{style:{padding:"0px",minHeight:"420px",marginTop:"30px",maxHeight:"calc(100vh - 350px)",overflowY:"auto"},children:B()})]}),e.jsxs(Ce,{open:j,onOk:T,onCancel:A,zIndex:1001,title:e.jsx("div",{style:{fontSize:"20px",fontWeight:"bold",color:"#222",marginTop:"0px !important",height:"27px",display:"flex",alignItems:"center",margin:"0px 0px 15px 0"},children:e.jsx("span",{children:n("common.discard_changes")})}),cancelText:n("common.cancel"),okText:n("common.confirm"),width:480,footer:[e.jsx(q,{onClick:A,children:n("common.cancel")},"cancel"),e.jsx(q,{type:"primary",danger:!0,onClick:T,children:n("common.confirm")},"ok")],children:[e.jsx(te,{style:{margin:"0 0 32px 0",width:"calc(100% + 50px)",marginLeft:"-25px"}}),e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx(ue,{type:"warning",showIcon:!0,style:{color:"rgba(0, 0, 0, 0.88)",backgroundColor:"transparent",border:"none",padding:0}}),n("crud.confirm_cancel")||"Are you sure you want to discard the changes you have made?"]}),e.jsx(te,{style:{margin:"64px 0px 16px 0px",width:"calc(100% + 50px)",marginLeft:"-25px"}})]})]})};Ke.propTypes=bn;Ke.defaultProps=vn;const jn=()=>{const{getFirstVenueFavoriteId:t}=ls(),o=Ve(),s=()=>{const a=window.location.hash;return a?a.substring(1):null};let n=t();return o.data&&(n=n??o.data[0].id,n=s()||n),{defaultValue:n}},wn="/ampcon/wireless/inventory/batch_delete";function Cn(t){return Ue({url:`${wn}`,method:"DELETE",data:{idList:t}})}function An({idList:t,siteId:o}){return Ue({url:"/ampcon/wireless/inventory/batch_switch_site",method:"PUT",data:{idList:t,siteId:o}})}const Nn=i.forwardRef(({venueId:t,onlyUnassigned:o},s)=>{const{defaultValue:r}=jn();let n=null;const a=window.location.hash.replace("#","");a&&/^\d+$/.test(a)&&(n=a);let u=null;r&&/^\d+$/.test(r)&&(u=r),t=t||n||u;const{t:c}=_(),[p,d]=i.useState(""),[g,l]=i.useState(void 0),{isOpen:f,onOpen:j,onClose:w}=de(),{isOpen:O,onOpen:F,onClose:b}=de(),[v,B]=i.useState(!1),[h,y]=i.useState([{id:"serialNumber",sort:"asc"}]),[T,A]=i.useState({index:0,limit:10}),[m,k]=i.useState([]),[L,N]=i.useState([]);de();const C=de(),$=de(),V=lt({onSuccess:()=>F()}),{data:me,isFetching:H,refetch:K}=Gt({enabled:!0,onlyUnassigned:o,venueId:t}),{data:ae,isFetching:ne,refetch:ie}=Jt({pageInfo:T,sortInfo:h,enabled:!0,count:me,onlyUnassigned:o,venueId:t}),Te={selectedRowKeys:m,selectedRows:L,onChange:(S,G)=>{k(S),N(G)}},Ee=async S=>{var G,R;if(S.length){B(!0);try{await Cn(S),M.success("Batch delete inventory success."),K(),ie(),k([])}catch(le){M.error(`Failed to delete devices: ${((R=(G=le==null?void 0:le.response)==null?void 0:G.data)==null?void 0:R.ErrorDescription)||"Unknown error"}`)}finally{B(!1)}}},De=oe.useCallback(S=>e.jsx("a",{href:`/wireless/devices/${S.serialNumber}#/devices/${S.serialNumber}`,style:{color:"#14C9BB ",textDecoration:"underline"},children:e.jsx("pre",{children:S.serialNumber})}),[]),Oe=()=>{if(m.length===0){M.warning("Please select at least one piece of data");return}ye("Are you sure you need to delete the selected device?",()=>{Ee(m)})},x=(S,G,R)=>{console.log("pagination",S),A({index:S.current-1,limit:S.pageSize}),R.field&&y([{id:R.field,sort:R.order==="ascend"?"asc":"desc"}])},I=S=>{d(S),C.onOpen()},W=S=>{d(S),$.onOpen()},U=S=>{l(S),j()},ve=i.useCallback(S=>e.jsx(ws,{cell:S.row,refreshTable:K,openEditModal:U,onOpenFactoryReset:I,onOpenUpgradeModal:W},be()),[]),xe=i.useCallback((S,G)=>e.jsx(as,{date:S.row.original[G]},be()),[]),bt=i.useCallback(S=>{var G,R;return e.jsx(zs,{venueName:((R=(G=S.row.original.extendedInfo)==null?void 0:G.venue)==null?void 0:R.name)??"",venueId:S.row.original.venue})},[]),vt=i.useCallback(S=>{U({serialNumber:S})},[]),Xe=oe.useMemo(()=>[{key:"serialNumber",title:e.jsx("span",{style:{whiteSpace:"nowrap"},children:c("inventory.serial_number")}),dataIndex:"serialNumber",render:(G,R)=>De(R),sorter:!0,columnsFix:!0},{key:"name",title:c("common.name"),dataIndex:"name",sorter:!0},{key:"venue",title:c("inventory.site"),dataIndex:["venue"],render:(G,R)=>bt({row:{original:R}}),sorter:!0},{key:"description",title:c("common.description"),dataIndex:"description",sorter:!0},{key:"label",title:c("inventory.label"),dataIndex:"labelsName",sorter:!0},{key:"modified",title:c("common.modified"),dataIndex:"modified",render:(G,R)=>xe({row:{original:R}},"modified"),sorter:!0},{key:"operation",title:"Operation",dataIndex:"operation",render:(G,R)=>ve({row:{original:R}},"operation"),columnsFix:!0,fixed:"right"}],[c]),He=()=>{K(),ie()};i.useImperativeHandle(s,()=>({refreshTable:()=>{K(),ie()}})),i.useEffect(()=>{A(S=>({...S,index:0})),k([]),N([])},[t,o]);const[jt,Ze]=i.useState(!1),[wt,Ye]=i.useState(""),{data:Ct=[]}=is(),[At]=E.useForm(),Nt=()=>{if(m.length===0){M.warning("Please select at least one piece of data");return}Ze(!0)},St=async S=>{var R,le,tt,st;const G=S.site;if(!G){M.error("Please select a site");return}try{const Fe=await An({idList:m,siteId:G==="none"?null:G});Fe.status===200||Fe.status===201?(M.success(`Successfully moved ${m.length} devices`),et(),k([]),ie(),K()):M.error(((R=Fe.data)==null?void 0:R.info)||"Failed to move devices")}catch(ce){console.error("Error moving devices:",ce),(tt=(le=ce.response)==null?void 0:le.data)!=null&&tt.info?M.error(`Failed to move devices: ${ce.response.data.info}`):(st=ce.response)!=null&&st.status?M.error(`Failed to move devices: Server returned status ${ce.response.status}`):M.error(`Failed to move devices: ${ce.message}`)}},et=()=>{Ze(!1),Ye("")};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("div",{style:{display:"flex",alignItems:"center"},children:e.jsxs(re,{size:16,align:"middle",children:[e.jsx(Qe,{refresh:K,venueId:t}),e.jsx(Ds,{}),e.jsx(Ke,{refresh:K,handleRefresh:He,deviceClass:"venue"}),e.jsx(q,{htmlType:"button",style:{display:"flex",alignItems:"center"},onClick:()=>{He(),M.success("Inventory table refresh success.")},icon:e.jsx(Se,{component:pt}),children:"Refresh"}),e.jsx(qe,{trigger:["click"],overlayStyle:{width:"150px"},menu:{items:[{key:"move",label:"Move",onClick:Nt},{key:"delete",label:"Delete",onClick:Oe}]},children:e.jsx(q,{htmlType:"button",icon:e.jsx(us,{style:{marginTop:"5px"}}),disabled:m.length===0||v,loading:v,children:"Actions"})})]})}),e.jsx("div",{style:{minWidth:"280px"},children:e.jsx(_s,{onClick:vt})})]}),e.jsx(Xt,{columnsOrder:!0,resizableColumns:!0,tableId:"inventory-table",ref:s,columns:o?Xe.filter(S=>S.key!=="entity"&&S.key!=="venue"):Xe,dataSource:ae||[],loading:H||ne,onChange:x,showColumnSelector:"true",rowSelection:Te,disableInternalRowSelection:!0,pagination:{current:T.index+1,pageSize:T.limit,total:me,showSizeChanger:!0,showQuickJumper:!0,showTotal:S=>`Total ${S} items`,pageSizeOptions:["10","20","50","100"]}}),e.jsx(on,{tag:g,refresh:ie,onClose:w,open:f},g==null?void 0:g.serialNumber),e.jsx(Je,{isOpen:O,onClose:b,pushResult:V.data}),e.jsx(Fs,{modalProps:C,serialNumber:p}),e.jsxs(he,{title:"Move",open:jt,onCancel:et,onFinish:S=>{St(S)},form:At,modalClass:"ampcon-middle-modal",children:[e.jsx("style",{children:`
            .ant-form-item-explain {
              margin-left: -90px;
            }
          `}),e.jsx(ue,{className:"custom-trace-alert",message:c("Note: After the device is moved, the target site configuration will be delivered to the device."),type:"info",showIcon:!0,closable:!0,style:{marginTop:10,marginBottom:20}}),e.jsx(E.Item,{label:"Select Site",name:"site",rules:[{required:!0,message:"Please select site"}],className:"site-selection-form-item",children:e.jsxs(J,{value:wt,onChange:S=>Ye(S),placeholder:"Please select site",allowClear:!0,style:{width:"280px",height:"36px",marginLeft:"-90px"},children:[e.jsx(J.Option,{value:"none",children:"All Sites"}),Ct.map(S=>e.jsx(J.Option,{value:S.id,children:S.name},S.id))]})})]})]})}),Bn=()=>{const{t}=_(),[o,s]=i.useState(null),[r,n]=i.useState(!1),a=mt();i.useEffect(()=>{const p=window.location.hash.replace("#","");/^\d+$/.test(p)&&s(parseInt(p,10))},[]);const u=p=>{const d=window.location.pathname;a(`${d}#${p}`),typeof p=="string"&&/^\d+$/.test(p)?s(parseInt(p,10)):s(null)},c=p=>{n(p)};return e.jsxs(Qt,{style:{width:"100%",minHeight:"100%",borderRadius:"8px",boxShadow:"none",padding:"20px 24px",overflowX:"auto"},bodyStyle:{padding:0},children:[e.jsx("span",{className:"text-title",children:"Inventory"}),e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:16},children:[e.jsx(Kt,{onChange:u}),o===null&&e.jsx(E.Item,{label:t("devices.unassigned_only"),style:{marginBottom:0,marginLeft:"80px"},children:e.jsx(pe,{checked:r,onChange:c})})]}),e.jsx(Nn,{venueId:o,onlyUnassigned:r})]})};export{Bn as default};
