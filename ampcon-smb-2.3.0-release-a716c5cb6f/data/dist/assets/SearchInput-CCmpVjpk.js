import{r as u,f as Er,d8 as Ae,v as K,q as Q,j as d,l as St,s as $t,t as ct,w as ye,m as Ye,k as N,ck as at,n as oe,ci as ha,cg as Fn,d9 as Gr,aJ as ht,ch as _i,da as va,aR as Ri,cj as ya,db as Sa,cl as ba,aP as xa,p as dt,b as pe,bh as on,bF as qe,cN as st,u as Re,z as qt,y as ft,G as bt,a as ae,H as sn,b7 as Ca,b1 as wa,bb as _a,R as Ie,ao as <PERSON>,dc as <PERSON>i,dd as <PERSON>,de as Pi,df as Ea,dg as Ma,dh as Pa,di as $a,dj as Fa,dk as Zr,dl as ka,aQ as ja,_ as I,dm as $e,dn as Ia,aW as xt,dp as $i,dq as Aa,dr as Va,ds as <PERSON>,dt as <PERSON>,du as <PERSON>,ax as Mt,ay as lt,J as an,b0 as Gt,bm as Ue,a0 as Oa,be as La,aw as ki,b5 as ji,cP as Ii,dv as <PERSON>,b3 as Mr,b2 as xo,dw as Vi,cH as Ha,bf as za,dx as Na,bC as Ti,S as Wr,A as Di,g as Ba,h as wn,O as Et,a2 as we,ab as Oi,B as kn,Z as Li,af as Ga,ah as Za,dy as Wa,T as qa,aE as Ua,W as Ka,aq as Ya,L as Co}from"./index-CHCmiRmn.js";import{u as Xa,a as Ja}from"./useFastField-DAY0E64C.js";import{A as Qa,a as el,M as tl}from"./useCommandModal-my1Kwhjw.js";import{c as ln,a as nl,C as rl,D as ol}from"./CustomTable-CM5Sdauq.js";import{u as il}from"./useDataGrid-DNbMJCLu.js";import"./Form-CYdW6Qd_.js";import{d as sl}from"./index-LkKwRvEU.js";function al(e,t,n,r){return e.addEventListener(t,n,r),()=>{e.removeEventListener(t,n,r)}}function ll(e){return e.view??window}function ul(e){const t=ll(e);return typeof t.PointerEvent<"u"&&e instanceof t.PointerEvent?e.pointerType==="mouse":e instanceof t.MouseEvent}function Hi(e){return!!e.touches}function cl(e){return Hi(e)&&e.touches.length>1}function dl(e,t="page"){const n=e.touches[0]||e.changedTouches[0];return{x:n[`${t}X`],y:n[`${t}Y`]}}function fl(e,t="page"){return{x:e[`${t}X`],y:e[`${t}Y`]}}function zi(e,t="page"){return Hi(e)?dl(e,t):fl(e,t)}function gl(e){return t=>{const n=ul(t);(!n||n&&t.button===0)&&e(t)}}function ml(e,t=!1){function n(o){e(o,{point:zi(o)})}return t?gl(n):n}function jn(e,t,n,r){return al(e,t,ml(n,t==="pointerdown"),r)}function pl(e){const t=parseFloat(e);return typeof t!="number"||Number.isNaN(t)?0:t}function Ni(e,t){let n=pl(e);const r=10**(t??10);return n=Math.round(n*r)/r,t?n.toFixed(t):n.toString()}function wo(e){if(!Number.isFinite(e))return 0;let t=1,n=0;for(;Math.round(e*t)/t!==e;)t*=10,n+=1;return n}function hl(e,t,n){return e==null?e:(n<t&&console.warn("clamp: max cannot be less than min"),Math.min(Math.max(e,t),n))}function i1(e,t={}){const[n,r]=u.useState(!1),[o,i]=u.useState(e);u.useEffect(()=>i(e),[e]);const{timeout:s=1500,...a}=typeof t=="number"?{timeout:t}:t,l=u.useCallback(c=>{const f=typeof c=="string"?c:o;"clipboard"in navigator?navigator.clipboard.writeText(f).then(()=>r(!0)).catch(()=>r(Er(f,a))):r(Er(f,a))},[o,a]);return u.useEffect(()=>{let c=null;return n&&(c=window.setTimeout(()=>{r(!1)},s)),()=>{c&&window.clearTimeout(c)}},[s,n]),{value:o,setValue:i,onCopy:l,hasCopied:n}}function vl(e={}){const{onChange:t,precision:n,defaultValue:r,value:o,step:i=1,min:s=Number.MIN_SAFE_INTEGER,max:a=Number.MAX_SAFE_INTEGER,keepWithinRange:l=!0}=e,c=Ae(t),[f,p]=u.useState(()=>r==null?"":dr(r,i,n)??""),g=typeof o<"u",m=g?o:f,h=Bi(gt(m),i),v=n??h,y=u.useCallback(D=>{D!==m&&(g||p(D.toString()),c==null||c(D.toString(),gt(D)))},[c,g,m]),S=u.useCallback(D=>{let P=D;return l&&(P=hl(P,s,a)),Ni(P,v)},[v,l,a,s]),x=u.useCallback((D=i)=>{let P;m===""?P=gt(D):P=gt(m)+D,P=S(P),y(P)},[S,i,y,m]),C=u.useCallback((D=i)=>{let P;m===""?P=gt(-D):P=gt(m)-D,P=S(P),y(P)},[S,i,y,m]),_=u.useCallback(()=>{let D;r==null?D="":D=dr(r,i,n)??s,y(D)},[r,n,i,y,s]),F=u.useCallback(D=>{const P=dr(D,i,v)??s;y(P)},[v,i,y,s]),E=gt(m);return{isOutOfRange:E>a||E<s,isAtMax:E===a,isAtMin:E===s,precision:v,value:m,valueAsNumber:E,update:y,reset:_,increment:x,decrement:C,clamp:S,cast:F,setValue:p}}function gt(e){return parseFloat(e.toString().replace(/[^\w.-]+/g,""))}function Bi(e,t){return Math.max(wo(t),wo(e))}function dr(e,t,n){const r=gt(e);if(Number.isNaN(r))return;const o=Bi(r,t);return Ni(r,n??o)}function yl(e,t){const n=Ae(e);u.useEffect(()=>{let r=null;const o=()=>n();return t!==null&&(r=window.setInterval(o,t)),()=>{r&&window.clearInterval(r)}},[t,n])}function Gi(e){const t=u.useRef(null);return t.current=e,t}const Zi=1/60*1e3,Sl=typeof performance<"u"?()=>performance.now():()=>Date.now(),Wi=typeof window<"u"?e=>window.requestAnimationFrame(e):e=>setTimeout(()=>e(Sl()),Zi);function bl(e){let t=[],n=[],r=0,o=!1,i=!1;const s=new WeakSet,a={schedule:(l,c=!1,f=!1)=>{const p=f&&o,g=p?t:n;return c&&s.add(l),g.indexOf(l)===-1&&(g.push(l),p&&o&&(r=t.length)),l},cancel:l=>{const c=n.indexOf(l);c!==-1&&n.splice(c,1),s.delete(l)},process:l=>{if(o){i=!0;return}if(o=!0,[t,n]=[n,t],n.length=0,r=t.length,r)for(let c=0;c<r;c++){const f=t[c];f(l),s.has(f)&&(a.schedule(f),e())}o=!1,i&&(i=!1,a.process(l))}};return a}const xl=40;let Pr=!0,un=!1,$r=!1;const Nt={delta:0,timestamp:0},fn=["read","update","preRender","render","postRender"],Ln=fn.reduce((e,t)=>(e[t]=bl(()=>un=!0),e),{}),Cl=fn.reduce((e,t)=>{const n=Ln[t];return e[t]=(r,o=!1,i=!1)=>(un||Rl(),n.schedule(r,o,i)),e},{}),wl=fn.reduce((e,t)=>(e[t]=Ln[t].cancel,e),{});fn.reduce((e,t)=>(e[t]=()=>Ln[t].process(Nt),e),{});const _l=e=>Ln[e].process(Nt),qi=e=>{un=!1,Nt.delta=Pr?Zi:Math.max(Math.min(e-Nt.timestamp,xl),1),Nt.timestamp=e,$r=!0,fn.forEach(_l),$r=!1,un&&(Pr=!1,Wi(qi))},Rl=()=>{un=!0,Pr=!0,$r||Wi(qi)},_o=()=>Nt;var El=Object.defineProperty,Ml=(e,t,n)=>t in e?El(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,We=(e,t,n)=>(Ml(e,typeof t!="symbol"?t+"":t,n),n);class Pl{constructor(t,n,r){if(We(this,"history",[]),We(this,"startEvent",null),We(this,"lastEvent",null),We(this,"lastEventInfo",null),We(this,"handlers",{}),We(this,"removeListeners",()=>{}),We(this,"threshold",3),We(this,"win"),We(this,"updatePoint",()=>{if(!(this.lastEvent&&this.lastEventInfo))return;const a=fr(this.lastEventInfo,this.history),l=this.startEvent!==null,c=jl(a.offset,{x:0,y:0})>=this.threshold;if(!l&&!c)return;const{timestamp:f}=_o();this.history.push({...a.point,timestamp:f});const{onStart:p,onMove:g}=this.handlers;l||(p==null||p(this.lastEvent,a),this.startEvent=this.lastEvent),g==null||g(this.lastEvent,a)}),We(this,"onPointerMove",(a,l)=>{this.lastEvent=a,this.lastEventInfo=l,Cl.update(this.updatePoint,!0)}),We(this,"onPointerUp",(a,l)=>{const c=fr(l,this.history),{onEnd:f,onSessionEnd:p}=this.handlers;p==null||p(a,c),this.end(),!(!f||!this.startEvent)&&(f==null||f(a,c))}),this.win=t.view??window,cl(t))return;this.handlers=n,r&&(this.threshold=r),t.stopPropagation(),t.preventDefault();const o={point:zi(t)},{timestamp:i}=_o();this.history=[{...o.point,timestamp:i}];const{onSessionStart:s}=n;s==null||s(t,fr(o,this.history)),this.removeListeners=kl(jn(this.win,"pointermove",this.onPointerMove),jn(this.win,"pointerup",this.onPointerUp),jn(this.win,"pointercancel",this.onPointerUp))}updateHandlers(t){this.handlers=t}end(){var t;(t=this.removeListeners)==null||t.call(this),wl.update(this.updatePoint)}}function Ro(e,t){return{x:e.x-t.x,y:e.y-t.y}}function fr(e,t){return{point:e.point,delta:Ro(e.point,t[t.length-1]),offset:Ro(e.point,t[0]),velocity:Fl(t,.1)}}const $l=e=>e*1e3;function Fl(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const o=e[e.length-1];for(;n>=0&&(r=e[n],!(o.timestamp-r.timestamp>$l(t)));)n--;if(!r)return{x:0,y:0};const i=(o.timestamp-r.timestamp)/1e3;if(i===0)return{x:0,y:0};const s={x:(o.x-r.x)/i,y:(o.y-r.y)/i};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function kl(...e){return t=>e.reduce((n,r)=>r(n),t)}function gr(e,t){return Math.abs(e-t)}function Eo(e){return"x"in e&&"y"in e}function jl(e,t){if(typeof e=="number"&&typeof t=="number")return gr(e,t);if(Eo(e)&&Eo(t)){const n=gr(e.x,t.x),r=gr(e.y,t.y);return Math.sqrt(n**2+r**2)}return 0}function Il(e,t){const{onPan:n,onPanStart:r,onPanEnd:o,onPanSessionStart:i,onPanSessionEnd:s,threshold:a}=t,l=!!(n||r||o||i||s),c=u.useRef(null),f=Gi({onSessionStart:i,onSessionEnd:s,onStart:r,onMove:n,onEnd(p,g){c.current=null,o==null||o(p,g)}});u.useEffect(()=>{var p;(p=c.current)==null||p.updateHandlers(f.current)}),u.useEffect(()=>{const p=e.current;if(!p||!l)return;function g(m){c.current=new Pl(m,f.current,a)}return jn(p,"pointerdown",g)},[e,l,f,a]),u.useEffect(()=>()=>{var p;(p=c.current)==null||p.end(),c.current=null},[])}function Al(e){const t=parseFloat(e);return typeof t!="number"||Number.isNaN(t)?0:t}function Vl(e,t){let n=Al(e);const r=10**(t??10);return n=Math.round(n*r)/r,t?n.toFixed(t):n.toString()}function Tl(e){if(!Number.isFinite(e))return 0;let t=1,n=0;for(;Math.round(e*t)/t!==e;)t*=10,n+=1;return n}function Mo(e,t,n){return(e-t)*100/(n-t)}function Dl(e,t,n){return(n-t)*e+t}function Po(e,t,n){const r=Math.round((e-t)/n)*n+t,o=Tl(n);return Vl(r,o)}function mr(e,t,n){return e==null?e:(n<t&&console.warn("clamp: max cannot be less than min"),Math.min(Math.max(e,t),n))}function Ol(e,t){const n={},r={};for(const[o,i]of Object.entries(e))t.includes(o)?n[o]=i:r[o]=i;return[n,r]}const tn=K("div",{baseStyle:{display:"flex",alignItems:"center",justifyContent:"center"}});tn.displayName="Center";const Ll={horizontal:{insetStart:"50%",transform:"translateX(-50%)"},vertical:{top:"50%",transform:"translateY(-50%)"},both:{insetStart:"50%",top:"50%",transform:"translate(-50%, -50%)"}};Q(function(t,n){const{axis:r="both",...o}=t;return d.jsx(K.div,{ref:n,__css:Ll[r],...o,position:"absolute"})});var Hl=()=>typeof document<"u",$o=!1,gn=null,Pt=!1,Fr=!1,kr=new Set;function qr(e,t){kr.forEach(n=>n(e,t))}var zl=typeof window<"u"&&window.navigator!=null?/^Mac/.test(window.navigator.platform):!1;function Nl(e){return!(e.metaKey||!zl&&e.altKey||e.ctrlKey||e.key==="Control"||e.key==="Shift"||e.key==="Meta")}function Fo(e){Pt=!0,Nl(e)&&(gn="keyboard",qr("keyboard",e))}function At(e){if(gn="pointer",e.type==="mousedown"||e.type==="pointerdown"){Pt=!0;const t=e.composedPath?e.composedPath()[0]:e.target;let n=!1;try{n=t.matches(":focus-visible")}catch{}if(n)return;qr("pointer",e)}}function Bl(e){return e.mozInputSource===0&&e.isTrusted?!0:e.detail===0&&!e.pointerType}function Gl(e){Bl(e)&&(Pt=!0,gn="virtual")}function Zl(e){e.target===window||e.target===document||e.target instanceof Element&&e.target.hasAttribute("tabindex")||(!Pt&&!Fr&&(gn="virtual",qr("virtual",e)),Pt=!1,Fr=!1)}function Wl(){Pt=!1,Fr=!0}function ko(){return gn!=="pointer"}function ql(){if(!Hl()||$o)return;const{focus:e}=HTMLElement.prototype;HTMLElement.prototype.focus=function(...n){Pt=!0,e.apply(this,n)},document.addEventListener("keydown",Fo,!0),document.addEventListener("keyup",Fo,!0),document.addEventListener("click",Gl,!0),window.addEventListener("focus",Zl,!0),window.addEventListener("blur",Wl,!1),typeof PointerEvent<"u"?(document.addEventListener("pointerdown",At,!0),document.addEventListener("pointermove",At,!0),document.addEventListener("pointerup",At,!0)):(document.addEventListener("mousedown",At,!0),document.addEventListener("mousemove",At,!0),document.addEventListener("mouseup",At,!0)),$o=!0}function Ul(e){ql(),e(ko());const t=()=>e(ko());return kr.add(t),()=>{kr.delete(t)}}const[Kl,Ui]=St({name:"FormControlStylesContext",errorMessage:`useFormControlStyles returned is 'undefined'. Seems you forgot to wrap the components in "<FormControl />" `}),[Yl,Ut]=St({strict:!1,name:"FormControlContext"});function Xl(e){const{id:t,isRequired:n,isInvalid:r,isDisabled:o,isReadOnly:i,...s}=e,a=u.useId(),l=t||`field-${a}`,c=`${l}-label`,f=`${l}-feedback`,p=`${l}-helptext`,[g,m]=u.useState(!1),[h,v]=u.useState(!1),[y,S]=u.useState(!1),x=u.useCallback(($={},V=null)=>({id:p,...$,ref:Ye(V,z=>{z&&v(!0)})}),[p]),C=u.useCallback(($={},V=null)=>({...$,ref:V,"data-focus":N(y),"data-disabled":N(o),"data-invalid":N(r),"data-readonly":N(i),id:$.id!==void 0?$.id:c,htmlFor:$.htmlFor!==void 0?$.htmlFor:l}),[l,o,y,r,i,c]),_=u.useCallback(($={},V=null)=>({id:f,...$,ref:Ye(V,z=>{z&&m(!0)}),"aria-live":"polite"}),[f]),F=u.useCallback(($={},V=null)=>({...$,...s,ref:V,role:"group","data-focus":N(y),"data-disabled":N(o),"data-invalid":N(r),"data-readonly":N(i)}),[s,o,y,r,i]),E=u.useCallback(($={},V=null)=>({...$,ref:V,role:"presentation","aria-hidden":!0,children:$.children||"*"}),[]);return{isRequired:!!n,isInvalid:!!r,isReadOnly:!!i,isDisabled:!!o,isFocused:!!y,onFocus:()=>S(!0),onBlur:()=>S(!1),hasFeedbackText:g,setHasFeedbackText:m,hasHelpText:h,setHasHelpText:v,id:l,labelId:c,feedbackId:f,helpTextId:p,htmlProps:s,getHelpTextProps:x,getErrorMessageProps:_,getRootProps:F,getLabelProps:C,getRequiredIndicatorProps:E}}const Ki=Q(function(t,n){const r=$t("Form",t),o=ct(t),{getRootProps:i,htmlProps:s,...a}=Xl(o),l=ye("chakra-form-control",t.className);return d.jsx(Yl,{value:a,children:d.jsx(Kl,{value:r,children:d.jsx(K.div,{...i({},n),className:l,__css:r.container})})})});Ki.displayName="FormControl";const Jl=Q(function(t,n){const r=Ut(),o=Ui(),i=ye("chakra-form__helper-text",t.className);return d.jsx(K.div,{...r==null?void 0:r.getHelpTextProps(t,n),__css:o.helperText,className:i})});Jl.displayName="FormHelperText";function Ql(e){const{isDisabled:t,isInvalid:n,isReadOnly:r,isRequired:o,...i}=Ur(e);return{...i,disabled:t,readOnly:r,required:o,"aria-invalid":at(n),"aria-required":at(o),"aria-readonly":at(r)}}function Ur(e){const t=Ut(),{id:n,disabled:r,readOnly:o,required:i,isRequired:s,isInvalid:a,isReadOnly:l,isDisabled:c,onFocus:f,onBlur:p,...g}=e,m=e["aria-describedby"]?[e["aria-describedby"]]:[];return t!=null&&t.hasFeedbackText&&(t!=null&&t.isInvalid)&&m.push(t.feedbackId),t!=null&&t.hasHelpText&&m.push(t.helpTextId),{...g,"aria-describedby":m.join(" ")||void 0,id:n??(t==null?void 0:t.id),isDisabled:r??c??(t==null?void 0:t.isDisabled),isReadOnly:o??l??(t==null?void 0:t.isReadOnly),isRequired:i??s??(t==null?void 0:t.isRequired),isInvalid:a??(t==null?void 0:t.isInvalid),onFocus:oe(t==null?void 0:t.onFocus,f),onBlur:oe(t==null?void 0:t.onBlur,p)}}const eu={border:"0",clip:"rect(0, 0, 0, 0)",height:"1px",width:"1px",margin:"-1px",padding:"0",overflow:"hidden",whiteSpace:"nowrap",position:"absolute"};function tu(e={}){const t=Ur(e),{isDisabled:n,isReadOnly:r,isRequired:o,isInvalid:i,id:s,onBlur:a,onFocus:l,"aria-describedby":c}=t,{defaultChecked:f,isChecked:p,isFocusable:g,onChange:m,isIndeterminate:h,name:v,value:y,tabIndex:S=void 0,"aria-label":x,"aria-labelledby":C,"aria-invalid":_,...F}=e,E=ha(F,["isDisabled","isReadOnly","isRequired","isInvalid","id","onBlur","onFocus","aria-describedby"]),$=Ae(m),V=Ae(a),z=Ae(l),[D,P]=u.useState(!1),[k,H]=u.useState(!1),[Z,G]=u.useState(!1),ee=u.useRef(!1);u.useEffect(()=>Ul(R=>{ee.current=R}),[]);const Y=u.useRef(null),[A,q]=u.useState(!0),[Ee,Me]=u.useState(!!f),Fe=p!==void 0,X=Fe?p:Ee,U=u.useCallback(R=>{if(r||n){R.preventDefault();return}Fe||Me(X?R.currentTarget.checked:h?!0:R.currentTarget.checked),$==null||$(R)},[r,n,X,Fe,h,$]);Fn(()=>{Y.current&&(Y.current.indeterminate=!!h)},[h]),Gr(()=>{n&&P(!1)},[n,P]),Fn(()=>{const R=Y.current;if(!(R!=null&&R.form))return;const ie=()=>{Me(!!f)};return R.form.addEventListener("reset",ie),()=>{var ve;return(ve=R.form)==null?void 0:ve.removeEventListener("reset",ie)}},[]);const le=n&&!g,me=u.useCallback(R=>{R.key===" "&&G(!0)},[G]),xe=u.useCallback(R=>{R.key===" "&&G(!1)},[G]);Fn(()=>{if(!Y.current)return;Y.current.checked!==X&&Me(Y.current.checked)},[Y.current]);const De=u.useCallback((R={},ie=null)=>{const ve=Oe=>{D&&Oe.preventDefault(),G(!0)};return{...R,ref:ie,"data-active":N(Z),"data-hover":N(k),"data-checked":N(X),"data-focus":N(D),"data-focus-visible":N(D&&ee.current),"data-indeterminate":N(h),"data-disabled":N(n),"data-invalid":N(i),"data-readonly":N(r),"aria-hidden":!0,onMouseDown:oe(R.onMouseDown,ve),onMouseUp:oe(R.onMouseUp,()=>G(!1)),onMouseEnter:oe(R.onMouseEnter,()=>H(!0)),onMouseLeave:oe(R.onMouseLeave,()=>H(!1))}},[Z,X,n,D,k,h,i,r]),Se=u.useCallback((R={},ie=null)=>({...R,ref:ie,"data-active":N(Z),"data-hover":N(k),"data-checked":N(X),"data-focus":N(D),"data-focus-visible":N(D&&ee.current),"data-indeterminate":N(h),"data-disabled":N(n),"data-invalid":N(i),"data-readonly":N(r)}),[Z,X,n,D,k,h,i,r]),he=u.useCallback((R={},ie=null)=>({...E,...R,ref:Ye(ie,ve=>{ve&&q(ve.tagName==="LABEL")}),onClick:oe(R.onClick,()=>{var ve;A||((ve=Y.current)==null||ve.click(),requestAnimationFrame(()=>{var Oe;(Oe=Y.current)==null||Oe.focus({preventScroll:!0})}))}),"data-disabled":N(n),"data-checked":N(X),"data-invalid":N(i)}),[E,n,X,i,A]),W=u.useCallback((R={},ie=null)=>({...R,ref:Ye(Y,ie),type:"checkbox",name:v,value:y,id:s,tabIndex:S,onChange:oe(R.onChange,U),onBlur:oe(R.onBlur,V,()=>P(!1)),onFocus:oe(R.onFocus,z,()=>P(!0)),onKeyDown:oe(R.onKeyDown,me),onKeyUp:oe(R.onKeyUp,xe),required:o,checked:X,disabled:le,readOnly:r,"aria-label":x,"aria-labelledby":C,"aria-invalid":_?!!_:i,"aria-describedby":c,"aria-disabled":n,"aria-checked":h?"mixed":X,style:eu}),[v,y,s,S,U,V,z,me,xe,o,X,le,r,x,C,_,i,c,n,h]),te=u.useCallback((R={},ie=null)=>({...R,ref:ie,onMouseDown:oe(R.onMouseDown,nu),"data-disabled":N(n),"data-checked":N(X),"data-invalid":N(i)}),[X,n,i]);return{state:{isInvalid:i,isFocused:D,isChecked:X,isActive:Z,isHovered:k,isIndeterminate:h,isDisabled:n,isReadOnly:r,isRequired:o},getRootProps:he,getCheckboxProps:De,getIndicatorProps:Se,getInputProps:W,getLabelProps:te,htmlProps:E}}function nu(e){e.preventDefault(),e.stopPropagation()}const[ru,ou]=St({name:"FormErrorStylesContext",errorMessage:`useFormErrorStyles returned is 'undefined'. Seems you forgot to wrap the components in "<FormError />" `}),Yi=Q((e,t)=>{const n=$t("FormError",e),r=ct(e),o=Ut();return o!=null&&o.isInvalid?d.jsx(ru,{value:n,children:d.jsx(K.div,{...o==null?void 0:o.getErrorMessageProps(r,t),className:ye("chakra-form__error-message",e.className),__css:{display:"flex",alignItems:"center",...n.text}})}):null});Yi.displayName="FormErrorMessage";const iu=Q((e,t)=>{const n=ou(),r=Ut();if(!(r!=null&&r.isInvalid))return null;const o=ye("chakra-form__error-icon",e.className);return d.jsx(ht,{ref:t,"aria-hidden":!0,...e,__css:n.icon,className:o,children:d.jsx("path",{fill:"currentColor",d:"M11.983,0a12.206,12.206,0,0,0-8.51,3.653A11.8,11.8,0,0,0,0,12.207,11.779,11.779,0,0,0,11.8,24h.214A12.111,12.111,0,0,0,24,11.791h0A11.766,11.766,0,0,0,11.983,0ZM10.5,16.542a1.476,1.476,0,0,1,1.449-1.53h.027a1.527,1.527,0,0,1,1.523,1.47,1.475,1.475,0,0,1-1.449,1.53h-.027A1.529,1.529,0,0,1,10.5,16.542ZM11,12.5v-6a1,1,0,0,1,2,0v6a1,1,0,1,1-2,0Z"})})});iu.displayName="FormErrorIcon";const Xi=Q(function(t,n){const r=_i("FormLabel",t),o=ct(t),{className:i,children:s,requiredIndicator:a=d.jsx(Ji,{}),optionalIndicator:l=null,...c}=o,f=Ut(),p=(f==null?void 0:f.getLabelProps(c,n))??{ref:n,...c};return d.jsxs(K.label,{...p,className:ye("chakra-form__label",o.className),__css:{display:"block",textAlign:"start",...r},children:[s,f!=null&&f.isRequired?a:l]})});Xi.displayName="FormLabel";const Ji=Q(function(t,n){const r=Ut(),o=Ui();if(!(r!=null&&r.isRequired))return null;const i=ye("chakra-form__required-indicator",t.className);return d.jsx(K.span,{...r==null?void 0:r.getRequiredIndicatorProps(t,n),__css:o.requiredIndicator,className:i})});Ji.displayName="RequiredIndicator";function Kt(e){const{viewBox:t="0 0 24 24",d:n,displayName:r,defaultProps:o={}}=e,i=u.Children.toArray(e.path),s=Q((a,l)=>d.jsx(ht,{ref:l,viewBox:t,...o,...a,children:i.length?i:d.jsx("path",{fill:"currentColor",d:n})}));return s.displayName=r,s}function su(e,t={}){const{ssr:n=!0,fallback:r}=t,{getWindow:o}=va(),i=Array.isArray(e)?e:[e];let s=Array.isArray(r)?r:[r];s=s.filter(c=>c!=null);const[a,l]=u.useState(()=>i.map((c,f)=>({media:c,matches:n?!!s[f]:o().matchMedia(c).matches})));return u.useEffect(()=>{const c=o();l(i.map(g=>({media:g,matches:c.matchMedia(g).matches})));const f=i.map(g=>c.matchMedia(g)),p=g=>{l(m=>m.slice().map(h=>h.media===g.media?{...h,matches:g.matches}:h))};return f.forEach(g=>{typeof g.addListener=="function"?g.addListener(p):g.addEventListener("change",p)}),()=>{f.forEach(g=>{typeof g.removeListener=="function"?g.removeListener(p):g.removeEventListener("change",p)})}},[o]),a.map(c=>c.matches)}function Qi(e){var a;const t=ya(e)?e:{fallback:"base"},r=Ri().__breakpoints.details.map(({minMaxQuery:l,breakpoint:c})=>({breakpoint:c,query:l.replace("@media screen and ","")})),o=r.map(l=>l.breakpoint===t.fallback),s=su(r.map(l=>l.query),{fallback:o,ssr:t.ssr}).findIndex(l=>l==!0);return((a=r[s])==null?void 0:a.breakpoint)??t.fallback}const au=e=>d.jsx(ht,{viewBox:"0 0 24 24",...e,children:d.jsx("path",{fill:"currentColor",d:"M21,5H3C2.621,5,2.275,5.214,2.105,5.553C1.937,5.892,1.973,6.297,2.2,6.6l9,12 c0.188,0.252,0.485,0.4,0.8,0.4s0.611-0.148,0.8-0.4l9-12c0.228-0.303,0.264-0.708,0.095-1.047C21.725,5.214,21.379,5,21,5z"})}),lu=e=>d.jsx(ht,{viewBox:"0 0 24 24",...e,children:d.jsx("path",{fill:"currentColor",d:"M12.8,5.4c-0.377-0.504-1.223-0.504-1.6,0l-9,12c-0.228,0.303-0.264,0.708-0.095,1.047 C2.275,18.786,2.621,19,3,19h18c0.379,0,0.725-0.214,0.895-0.553c0.169-0.339,0.133-0.744-0.095-1.047L12.8,5.4z"})});function jo(e,t,n,r){u.useEffect(()=>{if(!e.current||!r)return;const o=e.current.ownerDocument.defaultView??window,i=Array.isArray(t)?t:[t],s=new o.MutationObserver(a=>{for(const l of a)l.type==="attributes"&&l.attributeName&&i.includes(l.attributeName)&&n(l)});return s.observe(e.current,{attributes:!0,attributeFilter:i}),()=>s.disconnect()})}const uu=50,Io=300;function cu(e,t){const[n,r]=u.useState(!1),[o,i]=u.useState(null),[s,a]=u.useState(!0),l=u.useRef(null),c=()=>clearTimeout(l.current);yl(()=>{o==="increment"&&e(),o==="decrement"&&t()},n?uu:null);const f=u.useCallback(()=>{s&&e(),l.current=setTimeout(()=>{a(!1),r(!0),i("increment")},Io)},[e,s]),p=u.useCallback(()=>{s&&t(),l.current=setTimeout(()=>{a(!1),r(!0),i("decrement")},Io)},[t,s]),g=u.useCallback(()=>{a(!0),r(!1),c()},[]);return u.useEffect(()=>()=>c(),[]),{up:f,down:p,stop:g,isSpinning:n}}const du=/^[Ee0-9+\-.]$/;function fu(e){return du.test(e)}function gu(e,t){if(e.key==null)return!0;const n=e.ctrlKey||e.altKey||e.metaKey;return!(e.key.length===1)||n?!0:t(e.key)}function mu(e={}){const{focusInputOnChange:t=!0,clampValueOnBlur:n=!0,keepWithinRange:r=!0,min:o=Number.MIN_SAFE_INTEGER,max:i=Number.MAX_SAFE_INTEGER,step:s=1,isReadOnly:a,isDisabled:l,isRequired:c,isInvalid:f,pattern:p="[0-9]*(.[0-9]+)?",inputMode:g="decimal",allowMouseWheel:m,id:h,onChange:v,precision:y,name:S,"aria-describedby":x,"aria-label":C,"aria-labelledby":_,onFocus:F,onBlur:E,onInvalid:$,getAriaValueText:V,isValidCharacter:z,format:D,parse:P,...k}=e,H=Ae(F),Z=Ae(E),G=Ae($),ee=Ae(z??fu),Y=Ae(V),A=vl(e),{update:q,increment:Ee,decrement:Me}=A,[Fe,X]=u.useState(!1),U=!(a||l),le=u.useRef(null),me=u.useRef(null),xe=u.useRef(null),De=u.useRef(null),Se=u.useCallback(b=>b.split("").filter(ee).join(""),[ee]),he=u.useCallback(b=>(P==null?void 0:P(b))??b,[P]),W=u.useCallback(b=>((D==null?void 0:D(b))??b).toString(),[D]);Gr(()=>{(A.valueAsNumber>i||A.valueAsNumber<o)&&(G==null||G("rangeOverflow",W(A.value),A.valueAsNumber))},[A.valueAsNumber,A.value,W,G]),Fn(()=>{if(!le.current)return;if(le.current.value!=A.value){const ne=he(le.current.value);A.setValue(Se(ne))}},[he,Se]);const te=u.useCallback((b=s)=>{U&&Ee(b)},[Ee,U,s]),T=u.useCallback((b=s)=>{U&&Me(b)},[Me,U,s]),R=cu(te,T);jo(xe,"disabled",R.stop,R.isSpinning),jo(De,"disabled",R.stop,R.isSpinning);const ie=u.useCallback(b=>{if(b.nativeEvent.isComposing)return;const w=he(b.currentTarget.value);q(Se(w)),me.current={start:b.currentTarget.selectionStart,end:b.currentTarget.selectionEnd}},[q,Se,he]),ve=u.useCallback(b=>{var ne;H==null||H(b),me.current&&(b.currentTarget.selectionStart=me.current.start??((ne=b.currentTarget.value)==null?void 0:ne.length),b.currentTarget.selectionEnd=me.current.end??b.currentTarget.selectionStart)},[H]),Oe=u.useCallback(b=>{if(b.nativeEvent.isComposing)return;gu(b,ee)||b.preventDefault();const ne=Ct(b)*s,w=b.key,ke={ArrowUp:()=>te(ne),ArrowDown:()=>T(ne),Home:()=>q(o),End:()=>q(i)}[w];ke&&(b.preventDefault(),ke(b))},[ee,s,te,T,q,o,i]),Ct=b=>{let ne=1;return(b.metaKey||b.ctrlKey)&&(ne=.1),b.shiftKey&&(ne=10),ne},wt=u.useMemo(()=>{const b=Y==null?void 0:Y(A.value);if(b!=null)return b;const ne=A.value.toString();return ne||void 0},[A.value,Y]),et=u.useCallback(()=>{let b=A.value;if(A.value==="")return;/^[eE]/.test(A.value.toString())?A.setValue(""):(A.valueAsNumber<o&&(b=o),A.valueAsNumber>i&&(b=i),A.cast(b))},[A,i,o]),Ce=u.useCallback(()=>{X(!1),n&&et()},[n,X,et]),tt=u.useCallback(()=>{t&&requestAnimationFrame(()=>{var b;(b=le.current)==null||b.focus()})},[t]),nt=u.useCallback(b=>{b.preventDefault(),R.up(),tt()},[tt,R]),_t=u.useCallback(b=>{b.preventDefault(),R.down(),tt()},[tt,R]);Sa(()=>le.current,"wheel",b=>{var rt;const w=(((rt=le.current)==null?void 0:rt.ownerDocument)??document).activeElement===le.current;if(!m||!w)return;b.preventDefault();const j=Ct(b)*s,ke=Math.sign(b.deltaY);ke===-1?te(j):ke===1&&T(j)},{passive:!1});const kt=u.useCallback((b={},ne=null)=>{const w=l||r&&A.isAtMax;return{...b,ref:Ye(ne,xe),role:"button",tabIndex:-1,onPointerDown:oe(b.onPointerDown,j=>{j.button!==0||w||nt(j)}),onPointerLeave:oe(b.onPointerLeave,R.stop),onPointerUp:oe(b.onPointerUp,R.stop),disabled:w,"aria-disabled":at(w)}},[A.isAtMax,r,nt,R.stop,l]),Rt=u.useCallback((b={},ne=null)=>{const w=l||r&&A.isAtMin;return{...b,ref:Ye(ne,De),role:"button",tabIndex:-1,onPointerDown:oe(b.onPointerDown,j=>{j.button!==0||w||_t(j)}),onPointerLeave:oe(b.onPointerLeave,R.stop),onPointerUp:oe(b.onPointerUp,R.stop),disabled:w,"aria-disabled":at(w)}},[A.isAtMin,r,_t,R.stop,l]),jt=u.useCallback((b={},ne=null)=>({name:S,inputMode:g,type:"text",pattern:p,"aria-labelledby":_,"aria-label":C,"aria-describedby":x,id:h,disabled:l,role:"spinbutton",...b,readOnly:b.readOnly??a,"aria-readonly":b.readOnly??a,"aria-required":b.required??c,required:b.required??c,ref:Ye(le,ne),value:W(A.value),"aria-valuemin":o,"aria-valuemax":i,"aria-valuenow":Number.isNaN(A.valueAsNumber)?void 0:A.valueAsNumber,"aria-invalid":at(f??A.isOutOfRange),"aria-valuetext":wt,autoComplete:"off",autoCorrect:"off",onChange:oe(b.onChange,ie),onKeyDown:oe(b.onKeyDown,Oe),onFocus:oe(b.onFocus,ve,()=>X(!0)),onBlur:oe(b.onBlur,Z,Ce)}),[S,g,p,_,C,W,x,h,l,c,a,f,A.value,A.valueAsNumber,A.isOutOfRange,o,i,wt,ie,Oe,ve,Z,Ce]);return{value:W(A.value),valueAsNumber:A.valueAsNumber,isFocused:Fe,isDisabled:l,isReadOnly:a,getIncrementButtonProps:kt,getDecrementButtonProps:Rt,getInputProps:jt,htmlProps:k}}const[pu,Hn]=St({name:"NumberInputStylesContext",errorMessage:`useNumberInputStyles returned is 'undefined'. Seems you forgot to wrap the components in "<NumberInput />" `}),[hu,Kr]=St({name:"NumberInputContext",errorMessage:"useNumberInputContext: `context` is undefined. Seems you forgot to wrap number-input's components within <NumberInput />"}),es=Q(function(t,n){const r=$t("NumberInput",t),o=ct(t),i=Ur(o),{htmlProps:s,...a}=mu(i),l=u.useMemo(()=>a,[a]);return d.jsx(hu,{value:l,children:d.jsx(pu,{value:r,children:d.jsx(K.div,{...s,ref:n,className:ye("chakra-numberinput",t.className),__css:{position:"relative",zIndex:0,...r.root}})})})});es.displayName="NumberInput";const ts=Q(function(t,n){const r=Hn();return d.jsx(K.div,{"aria-hidden":!0,ref:n,...t,__css:{display:"flex",flexDirection:"column",position:"absolute",top:"0",insetEnd:"0px",margin:"1px",height:"calc(100% - 2px)",zIndex:1,...r.stepperGroup}})});ts.displayName="NumberInputStepper";const ns=Q(function(t,n){const{getInputProps:r}=Kr(),o=r(t,n),i=Hn();return d.jsx(K.input,{...o,className:ye("chakra-numberinput__field",t.className),__css:{width:"100%",...i.field}})});ns.displayName="NumberInputField";const rs=K("div",{baseStyle:{display:"flex",justifyContent:"center",alignItems:"center",flex:1,transitionProperty:"common",transitionDuration:"normal",userSelect:"none",cursor:"pointer",lineHeight:"normal"}}),os=Q(function(t,n){const r=Hn(),{getDecrementButtonProps:o}=Kr(),i=o(t,n);return d.jsx(rs,{...i,__css:r.stepper,children:t.children??d.jsx(au,{})})});os.displayName="NumberDecrementStepper";const is=Q(function(t,n){const{getIncrementButtonProps:r}=Kr(),o=r(t,n),i=Hn();return d.jsx(rs,{...o,__css:i.stepper,children:t.children??d.jsx(lu,{})})});is.displayName="NumberIncrementStepper";const ss=Q(function(t,n){const{children:r,placeholder:o,className:i,...s}=t;return d.jsxs(K.select,{...s,ref:n,className:ye("chakra-select",i),children:[o&&d.jsx("option",{value:"",children:o}),r]})});ss.displayName="SelectField";const as=Q((e,t)=>{var C;const n=$t("Select",e),{rootProps:r,placeholder:o,icon:i,color:s,height:a,h:l,minH:c,minHeight:f,iconColor:p,iconSize:g,...m}=ct(e),[h,v]=Ol(m,ba),y=Ql(v),S={width:"100%",height:"fit-content",position:"relative",color:s},x={paddingEnd:"2rem",...n.field,_focus:{zIndex:"unset",...(C=n.field)==null?void 0:C._focus}};return d.jsxs(K.div,{className:"chakra-select__wrapper",__css:S,...h,...r,children:[d.jsx(ss,{ref:t,height:l??a,minH:c??f,placeholder:o,...y,__css:x,children:e.children}),d.jsx(ls,{"data-disabled":N(y.disabled),...(p||s)&&{color:p||s},__css:n.icon,...g&&{fontSize:g},children:i})]})});as.displayName="Select";const vu=e=>d.jsx("svg",{viewBox:"0 0 24 24",...e,children:d.jsx("path",{fill:"currentColor",d:"M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"})}),yu=K("div",{baseStyle:{position:"absolute",display:"inline-flex",alignItems:"center",justifyContent:"center",pointerEvents:"none",top:"50%",transform:"translateY(-50%)"}}),ls=e=>{const{children:t=d.jsx(vu,{}),...n}=e,r=u.cloneElement(t,{role:"presentation",className:"chakra-select__icon",focusable:!1,"aria-hidden":!0,style:{width:"1em",height:"1em",color:"currentColor"}});return d.jsx(yu,{...n,className:"chakra-select__icon-wrapper",children:u.isValidElement(t)?r:null})};ls.displayName="SelectIcon";function pr(e){const{orientation:t,vertical:n,horizontal:r}=e;return t==="vertical"?n:r}function Su(e){const{orientation:t,thumbPercents:n,isReversed:r}=e,o=m=>({position:"absolute",userSelect:"none",WebkitUserSelect:"none",MozUserSelect:"none",msUserSelect:"none",touchAction:"none",...pr({orientation:t,vertical:{bottom:`${n[m]}%`,transform:"translate(-50%, 50%) scale(var(--slider-thumb-scale, 1))"},horizontal:{left:`${n[m]}%`,transform:"translate(-50%, -50%) scale(var(--slider-thumb-scale, 1))"}})}),i={position:"relative",touchAction:"none",WebkitTapHighlightColor:"rgba(0,0,0,0)",userSelect:"none",outline:0},s={position:"absolute",...pr({orientation:t,vertical:{left:"50%",transform:"translateX(-50%)",height:"100%"},horizontal:{top:"50%",transform:"translateY(-50%)",width:"100%"}})},a=n.length===1,l=[0,r?100-n[0]:n[0]],c=a?l:n;let f=c[0];!a&&r&&(f=100-f);const p=Math.abs(c[c.length-1]-c[0]),g={...s,...pr({orientation:t,vertical:r?{height:`${p}%`,top:`${f}%`}:{height:`${p}%`,bottom:`${f}%`},horizontal:r?{width:`${p}%`,right:`${f}%`}:{width:`${p}%`,left:`${f}%`}})};return{trackStyle:s,innerTrackStyle:g,rootStyle:i,getThumbStyle:o}}function bu(e){const{isReversed:t,direction:n,orientation:r}=e;return n==="ltr"||r==="vertical"?t:!t}function xu(e){const{min:t=0,max:n=100,onChange:r,value:o,defaultValue:i,isReversed:s,direction:a="ltr",orientation:l="horizontal",id:c,isDisabled:f,isReadOnly:p,onChangeStart:g,onChangeEnd:m,step:h=1,getAriaValueText:v,"aria-valuetext":y,"aria-label":S,"aria-labelledby":x,name:C,focusThumbOnChange:_=!0,...F}=e,E=Ae(g),$=Ae(m),V=Ae(v),z=bu({isReversed:s,direction:a,orientation:l}),[D,P]=Xa({value:o,defaultValue:i??wu(t,n),onChange:r}),[k,H]=u.useState(!1),[Z,G]=u.useState(!1),ee=!(f||p),Y=(n-t)/10,A=h||(n-t)/100,q=mr(D,t,n),Ee=n-q+t,Fe=Mo(z?Ee:q,t,n),X=l==="vertical",U=Gi({min:t,max:n,step:h,isDisabled:f,value:q,isInteractive:ee,isReversed:z,isVertical:X,eventSource:null,focusThumbOnChange:_,orientation:l}),le=u.useRef(null),me=u.useRef(null),xe=u.useRef(null),De=u.useId(),Se=c??De,[he,W]=[`slider-thumb-${Se}`,`slider-track-${Se}`],te=u.useCallback(w=>{var bo;if(!le.current)return;const j=U.current;j.eventSource="pointer";const ke=le.current.getBoundingClientRect(),{clientX:rt,clientY:It}=((bo=w.touches)==null?void 0:bo[0])??w,ur=X?ke.bottom-It:rt-ke.left,pa=X?ke.height:ke.width;let cr=ur/pa;z&&(cr=1-cr);let Jt=Dl(cr,j.min,j.max);return j.step&&(Jt=parseFloat(Po(Jt,j.min,j.step))),Jt=mr(Jt,j.min,j.max),Jt},[X,z,U]),T=u.useCallback(w=>{const j=U.current;j.isInteractive&&(w=parseFloat(Po(w,j.min,A)),w=mr(w,j.min,j.max),P(w))},[A,P,U]),R=u.useMemo(()=>({stepUp(w=A){const j=z?q-w:q+w;T(j)},stepDown(w=A){const j=z?q+w:q-w;T(j)},reset(){T(i||0)},stepTo(w){T(w)}}),[T,z,q,A,i]),ie=u.useCallback(w=>{const j=U.current,rt={ArrowRight:()=>R.stepUp(),ArrowUp:()=>R.stepUp(),ArrowLeft:()=>R.stepDown(),ArrowDown:()=>R.stepDown(),PageUp:()=>R.stepUp(Y),PageDown:()=>R.stepDown(Y),Home:()=>T(j.min),End:()=>T(j.max)}[w.key];rt&&(w.preventDefault(),w.stopPropagation(),rt(w),j.eventSource="keyboard")},[R,T,Y,U]),ve=(V==null?void 0:V(q))??y,{getThumbStyle:Oe,rootStyle:Ct,trackStyle:wt,innerTrackStyle:et}=u.useMemo(()=>{const w=U.current;return Su({isReversed:z,orientation:w.orientation,thumbPercents:[Fe]})},[z,Fe,U]),Ce=u.useCallback(()=>{U.current.focusThumbOnChange&&setTimeout(()=>{var j;return(j=me.current)==null?void 0:j.focus()})},[U]);Gr(()=>{const w=U.current;Ce(),w.eventSource==="keyboard"&&($==null||$(w.value))},[q,$]);function tt(w){const j=te(w);j!=null&&j!==U.current.value&&P(j)}Il(xe,{onPanSessionStart(w){const j=U.current;j.isInteractive&&(H(!0),Ce(),tt(w),E==null||E(j.value))},onPanSessionEnd(){const w=U.current;w.isInteractive&&(H(!1),$==null||$(w.value))},onPan(w){U.current.isInteractive&&tt(w)}});const nt=u.useCallback((w={},j=null)=>({...w,...F,ref:Ye(j,xe),tabIndex:-1,"aria-disabled":at(f),"data-focused":N(Z),style:{...w.style,...Ct}}),[F,f,Z,Ct]),_t=u.useCallback((w={},j=null)=>({...w,ref:Ye(j,le),id:W,"data-disabled":N(f),style:{...w.style,...wt}}),[f,W,wt]),kt=u.useCallback((w={},j=null)=>({...w,ref:j,style:{...w.style,...et}}),[et]),Rt=u.useCallback((w={},j=null)=>({...w,ref:Ye(j,me),role:"slider",tabIndex:ee?0:void 0,id:he,"data-active":N(k),"aria-valuetext":ve,"aria-valuemin":t,"aria-valuemax":n,"aria-valuenow":q,"aria-orientation":l,"aria-disabled":at(f),"aria-readonly":at(p),"aria-label":S,"aria-labelledby":S?void 0:x,style:{...w.style,...Oe(0)},onKeyDown:oe(w.onKeyDown,ie),onFocus:oe(w.onFocus,()=>G(!0)),onBlur:oe(w.onBlur,()=>G(!1))}),[ee,he,k,ve,t,n,q,l,f,p,S,x,Oe,ie]),jt=u.useCallback((w,j=null)=>{const ke=!(w.value<t||w.value>n),rt=q>=w.value,It=Mo(w.value,t,n),ur={position:"absolute",pointerEvents:"none",...Cu({orientation:l,vertical:{bottom:z?`${100-It}%`:`${It}%`},horizontal:{left:z?`${100-It}%`:`${It}%`}})};return{...w,ref:j,role:"presentation","aria-hidden":!0,"data-disabled":N(f),"data-invalid":N(!ke),"data-highlighted":N(rt),style:{...w.style,...ur}}},[f,z,n,t,l,q]),b=u.useCallback((w={},j=null)=>({...w,ref:j,type:"hidden",value:q,name:C}),[C,q]);return{state:{value:q,isFocused:Z,isDragging:k},actions:R,getRootProps:nt,getTrackProps:_t,getInnerTrackProps:kt,getThumbProps:Rt,getMarkerProps:jt,getInputProps:b}}function Cu(e){const{orientation:t,vertical:n,horizontal:r}=e;return t==="vertical"?n:r}function wu(e,t){return t<e?e:e+(t-e)/2}const[_u,zn]=St({name:"SliderContext",hookName:"useSliderContext",providerName:"<Slider />"}),[Ru,Nn]=St({name:"SliderStylesContext",hookName:"useSliderStyles",providerName:"<Slider />"}),Eu=Q((e,t)=>{const n={...e,orientation:(e==null?void 0:e.orientation)??"horizontal"},r=$t("Slider",n),o=ct(n),{direction:i}=Ri();o.direction=i;const{getInputProps:s,getRootProps:a,...l}=xu(o),c=a(),f=s({},t);return d.jsx(_u,{value:l,children:d.jsx(Ru,{value:r,children:d.jsxs(K.div,{...c,className:ye("chakra-slider",n.className),__css:r.container,children:[n.children,d.jsx("input",{...f})]})})})});Eu.displayName="Slider";const Mu=Q((e,t)=>{const{getThumbProps:n}=zn(),r=Nn(),o=n(e,t);return d.jsx(K.div,{...o,className:ye("chakra-slider__thumb",e.className),__css:r.thumb})});Mu.displayName="SliderThumb";const Pu=Q((e,t)=>{const{getTrackProps:n}=zn(),r=Nn(),o=n(e,t);return d.jsx(K.div,{...o,className:ye("chakra-slider__track",e.className),__css:r.track})});Pu.displayName="SliderTrack";const $u=Q((e,t)=>{const{getInnerTrackProps:n}=zn(),r=Nn(),o=n(e,t);return d.jsx(K.div,{...o,className:ye("chakra-slider__filled-track",e.className),__css:r.filledTrack})});$u.displayName="SliderFilledTrack";const Fu=Q((e,t)=>{const{getMarkerProps:n}=zn(),r=Nn(),o=n(e,t);return d.jsx(K.div,{...o,className:ye("chakra-slider__marker",e.className),__css:r.mark})});Fu.displayName="SliderMark";const us=Q(function(t,n){const r=$t("Switch",t),{spacing:o="0.5rem",children:i,...s}=ct(t),{getIndicatorProps:a,getInputProps:l,getCheckboxProps:c,getRootProps:f,getLabelProps:p}=tu(s),g=u.useMemo(()=>({display:"inline-block",position:"relative",verticalAlign:"middle",lineHeight:0,...r.container}),[r.container]),m=u.useMemo(()=>({display:"inline-flex",flexShrink:0,justifyContent:"flex-start",boxSizing:"content-box",cursor:"pointer",...r.track}),[r.track]),h=u.useMemo(()=>({userSelect:"none",marginStart:o,...r.label}),[o,r.label]);return d.jsxs(K.label,{...f(),className:ye("chakra-switch",t.className),__css:g,children:[d.jsx("input",{className:"chakra-switch__input",...l({},n)}),d.jsx(K.span,{...c(),className:"chakra-switch__track",__css:m,children:d.jsx(K.span,{__css:r.thumb,className:"chakra-switch__thumb",...a()})}),i&&d.jsx(K.span,{className:"chakra-switch__label",...p(),__css:h,children:i})]})});us.displayName="Switch";const[ku,mn]=St({name:"TableStylesContext",errorMessage:`useTableStyles returned is 'undefined'. Seems you forgot to wrap the components in "<Table />" `}),jr=Q((e,t)=>{const n=$t("Table",e),{className:r,layout:o,...i}=ct(e);return d.jsx(ku,{value:n,children:d.jsx(K.table,{ref:t,__css:{tableLayout:o,...n.table},className:ye("chakra-table",r),...i})})});jr.displayName="Table";const Ao=Q((e,t)=>{const{overflow:n,overflowX:r,className:o,...i}=e;return d.jsx(K.div,{ref:t,className:ye("chakra-table__container",o),...i,__css:{display:"block",whiteSpace:"nowrap",WebkitOverflowScrolling:"touch",overflowX:n??r??"auto",overflowY:"hidden",maxWidth:"100%"}})}),Vo=Q((e,t)=>{const n=mn();return d.jsx(K.tbody,{...e,ref:t,__css:n.tbody})}),ju=Q(({isNumeric:e,...t},n)=>{const r=mn();return d.jsx(K.td,{...t,ref:n,__css:r.td,"data-is-numeric":e})}),Iu=Q(({isNumeric:e,...t},n)=>{const r=mn();return d.jsx(K.th,{...t,ref:n,__css:r.th,"data-is-numeric":e})}),To=Q((e,t)=>{const n=mn();return d.jsx(K.thead,{...e,ref:t,__css:n.thead})}),cs=Q((e,t)=>{const n=mn();return d.jsx(K.tr,{...e,ref:t,__css:n.tr})}),zt=Q(function(t,n){const r=_i("Text",t),{className:o,align:i,decoration:s,casing:a,...l}=ct(t),c=xa({textAlign:t.align,textDecoration:t.decoration,textTransform:t.casing});return d.jsx(K.p,{ref:n,className:ye("chakra-text",t.className),...c,...l,__css:r})});zt.displayName="Text";const Au=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M208.49,152.49l-72,72a12,12,0,0,1-17,0l-72-72a12,12,0,0,1,17-17L116,187V40a12,12,0,0,1,24,0V187l51.51-51.52a12,12,0,0,1,17,17Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M200,144l-72,72L56,144Z",opacity:"0.2"}),u.createElement("path",{d:"M207.39,140.94A8,8,0,0,0,200,136H136V40a8,8,0,0,0-16,0v96H56a8,8,0,0,0-5.66,13.66l72,72a8,8,0,0,0,11.32,0l72-72A8,8,0,0,0,207.39,140.94ZM128,204.69,75.31,152H180.69Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M205.66,149.66l-72,72a8,8,0,0,1-11.32,0l-72-72A8,8,0,0,1,56,136h64V40a8,8,0,0,1,16,0v96h64a8,8,0,0,1,5.66,13.66Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M204.24,148.24l-72,72a6,6,0,0,1-8.48,0l-72-72a6,6,0,0,1,8.48-8.48L122,201.51V40a6,6,0,0,1,12,0V201.51l61.76-61.75a6,6,0,0,1,8.48,8.48Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M205.66,149.66l-72,72a8,8,0,0,1-11.32,0l-72-72a8,8,0,0,1,11.32-11.32L120,196.69V40a8,8,0,0,1,16,0V196.69l58.34-58.35a8,8,0,0,1,11.32,11.32Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M202.83,146.83l-72,72a4,4,0,0,1-5.66,0l-72-72a4,4,0,0,1,5.66-5.66L124,206.34V40a4,4,0,0,1,8,0V206.34l65.17-65.17a4,4,0,0,1,5.66,5.66Z"}))]]),Vu=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M228,128a12,12,0,0,1-12,12H69l51.52,51.51a12,12,0,0,1-17,17l-72-72a12,12,0,0,1,0-17l72-72a12,12,0,0,1,17,17L69,116H216A12,12,0,0,1,228,128Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M112,56V200L40,128Z",opacity:"0.2"}),u.createElement("path",{d:"M216,120H120V56a8,8,0,0,0-13.66-5.66l-72,72a8,8,0,0,0,0,11.32l72,72A8,8,0,0,0,120,200V136h96a8,8,0,0,0,0-16ZM104,180.69,51.31,128,104,75.31Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M224,128a8,8,0,0,1-8,8H120v64a8,8,0,0,1-13.66,5.66l-72-72a8,8,0,0,1,0-11.32l72-72A8,8,0,0,1,120,56v64h96A8,8,0,0,1,224,128Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M222,128a6,6,0,0,1-6,6H54.49l61.75,61.76a6,6,0,1,1-8.48,8.48l-72-72a6,6,0,0,1,0-8.48l72-72a6,6,0,0,1,8.48,8.48L54.49,122H216A6,6,0,0,1,222,128Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M220,128a4,4,0,0,1-4,4H49.66l65.17,65.17a4,4,0,0,1-5.66,5.66l-72-72a4,4,0,0,1,0-5.66l72-72a4,4,0,0,1,5.66,5.66L49.66,124H216A4,4,0,0,1,220,128Z"}))]]),Tu=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M208.49,120.49a12,12,0,0,1-17,0L140,69V216a12,12,0,0,1-24,0V69L64.49,120.49a12,12,0,0,1-17-17l72-72a12,12,0,0,1,17,0l72,72A12,12,0,0,1,208.49,120.49Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M200,112H56l72-72Z",opacity:"0.2"}),u.createElement("path",{d:"M205.66,106.34l-72-72a8,8,0,0,0-11.32,0l-72,72A8,8,0,0,0,56,120h64v96a8,8,0,0,0,16,0V120h64a8,8,0,0,0,5.66-13.66ZM75.31,104,128,51.31,180.69,104Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M207.39,115.06A8,8,0,0,1,200,120H136v96a8,8,0,0,1-16,0V120H56a8,8,0,0,1-5.66-13.66l72-72a8,8,0,0,1,11.32,0l72,72A8,8,0,0,1,207.39,115.06Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M204.24,116.24a6,6,0,0,1-8.48,0L134,54.49V216a6,6,0,0,1-12,0V54.49L60.24,116.24a6,6,0,0,1-8.48-8.48l72-72a6,6,0,0,1,8.48,0l72,72A6,6,0,0,1,204.24,116.24Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M205.66,117.66a8,8,0,0,1-11.32,0L136,59.31V216a8,8,0,0,1-16,0V59.31L61.66,117.66a8,8,0,0,1-11.32-11.32l72-72a8,8,0,0,1,11.32,0l72,72A8,8,0,0,1,205.66,117.66Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M202.83,114.83a4,4,0,0,1-5.66,0L132,49.66V216a4,4,0,0,1-8,0V49.66L58.83,114.83a4,4,0,0,1-5.66-5.66l72-72a4,4,0,0,1,5.66,0l72,72A4,4,0,0,1,202.83,114.83Z"}))]]),Du=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M228,48V96a12,12,0,0,1-12,12H168a12,12,0,0,1,0-24h19l-7.8-7.8a75.55,75.55,0,0,0-53.32-22.26h-.43A75.49,75.49,0,0,0,72.39,75.57,12,12,0,1,1,55.61,58.41a99.38,99.38,0,0,1,69.87-28.47H126A99.42,99.42,0,0,1,196.2,59.23L204,67V48a12,12,0,0,1,24,0ZM183.61,180.43a75.49,75.49,0,0,1-53.09,21.63h-.43A75.55,75.55,0,0,1,76.77,179.8L69,172H88a12,12,0,0,0,0-24H40a12,12,0,0,0-12,12v48a12,12,0,0,0,24,0V189l7.8,7.8A99.42,99.42,0,0,0,130,226.06h.56a99.38,99.38,0,0,0,69.87-28.47,12,12,0,0,0-16.78-17.16Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M216,128a88,88,0,1,1-88-88A88,88,0,0,1,216,128Z",opacity:"0.2"}),u.createElement("path",{d:"M224,48V96a8,8,0,0,1-8,8H168a8,8,0,0,1,0-16h28.69L182.06,73.37a79.56,79.56,0,0,0-56.13-23.43h-.45A79.52,79.52,0,0,0,69.59,72.71,8,8,0,0,1,58.41,61.27a96,96,0,0,1,135,.79L208,76.69V48a8,8,0,0,1,16,0ZM186.41,183.29a80,80,0,0,1-112.47-.66L59.31,168H88a8,8,0,0,0,0-16H40a8,8,0,0,0-8,8v48a8,8,0,0,0,16,0V179.31l14.63,14.63A95.43,95.43,0,0,0,130,222.06h.53a95.36,95.36,0,0,0,67.07-27.33,8,8,0,0,0-11.18-11.44Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M224,48V96a8,8,0,0,1-8,8H168a8,8,0,0,1-5.66-13.66L180.65,72a79.48,79.48,0,0,0-54.72-22.09h-.45A79.52,79.52,0,0,0,69.59,72.71,8,8,0,0,1,58.41,61.27,96,96,0,0,1,192,60.7l18.36-18.36A8,8,0,0,1,224,48ZM186.41,183.29A80,80,0,0,1,75.35,184l18.31-18.31A8,8,0,0,0,88,152H40a8,8,0,0,0-8,8v48a8,8,0,0,0,13.66,5.66L64,195.3a95.42,95.42,0,0,0,66,26.76h.53a95.36,95.36,0,0,0,67.07-27.33,8,8,0,0,0-11.18-11.44Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M222,48V96a6,6,0,0,1-6,6H168a6,6,0,0,1,0-12h33.52L183.47,72a81.51,81.51,0,0,0-57.53-24h-.46A81.5,81.5,0,0,0,68.19,71.28a6,6,0,1,1-8.38-8.58,93.38,93.38,0,0,1,65.67-26.76H126a93.45,93.45,0,0,1,66,27.53l18,18V48a6,6,0,0,1,12,0ZM187.81,184.72a81.5,81.5,0,0,1-57.29,23.34h-.46a81.51,81.51,0,0,1-57.53-24L54.48,166H88a6,6,0,0,0,0-12H40a6,6,0,0,0-6,6v48a6,6,0,0,0,12,0V174.48l18,18.05a93.45,93.45,0,0,0,66,27.53h.52a93.38,93.38,0,0,0,65.67-26.76,6,6,0,1,0-8.38-8.58Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M224,48V96a8,8,0,0,1-8,8H168a8,8,0,0,1,0-16h28.69L182.06,73.37a79.56,79.56,0,0,0-56.13-23.43h-.45A79.52,79.52,0,0,0,69.59,72.71,8,8,0,0,1,58.41,61.27a96,96,0,0,1,135,.79L208,76.69V48a8,8,0,0,1,16,0ZM186.41,183.29a80,80,0,0,1-112.47-.66L59.31,168H88a8,8,0,0,0,0-16H40a8,8,0,0,0-8,8v48a8,8,0,0,0,16,0V179.31l14.63,14.63A95.43,95.43,0,0,0,130,222.06h.53a95.36,95.36,0,0,0,67.07-27.33,8,8,0,0,0-11.18-11.44Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M220,48V96a4,4,0,0,1-4,4H168a4,4,0,0,1,0-8h38.34L184.89,70.54A84,84,0,0,0,66.8,69.85a4,4,0,1,1-5.6-5.72,92,92,0,0,1,129.34.76L212,86.34V48a4,4,0,0,1,8,0ZM189.2,186.15a83.44,83.44,0,0,1-58.68,23.91h-.47a83.52,83.52,0,0,1-58.94-24.6L49.66,164H88a4,4,0,0,0,0-8H40a4,4,0,0,0-4,4v48a4,4,0,0,0,8,0V169.66l21.46,21.45A91.43,91.43,0,0,0,130,218.06h.51a91.45,91.45,0,0,0,64.28-26.19,4,4,0,1,0-5.6-5.72Z"}))]]),Ou=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M120.49,167.51a12,12,0,0,1,0,17l-32,32a12,12,0,0,1-17,0l-32-32a12,12,0,1,1,17-17L68,179V48a12,12,0,0,1,24,0V179l11.51-11.52A12,12,0,0,1,120.49,167.51Zm96-96-32-32a12,12,0,0,0-17,0l-32,32a12,12,0,0,0,17,17L164,77V208a12,12,0,0,0,24,0V77l11.51,11.52a12,12,0,0,0,17-17Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M176,48V208H80V48Z",opacity:"0.2"}),u.createElement("path",{d:"M117.66,170.34a8,8,0,0,1,0,11.32l-32,32a8,8,0,0,1-11.32,0l-32-32a8,8,0,0,1,11.32-11.32L72,188.69V48a8,8,0,0,1,16,0V188.69l18.34-18.35A8,8,0,0,1,117.66,170.34Zm96-96-32-32a8,8,0,0,0-11.32,0l-32,32a8,8,0,0,0,11.32,11.32L168,67.31V208a8,8,0,0,0,16,0V67.31l18.34,18.35a8,8,0,0,0,11.32-11.32Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M119.39,172.94a8,8,0,0,1-1.73,8.72l-32,32a8,8,0,0,1-11.32,0l-32-32A8,8,0,0,1,48,168H72V48a8,8,0,0,1,16,0V168h24A8,8,0,0,1,119.39,172.94Zm94.27-98.6-32-32a8,8,0,0,0-11.32,0l-32,32A8,8,0,0,0,144,88h24V208a8,8,0,0,0,16,0V88h24a8,8,0,0,0,5.66-13.66Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M116.24,171.76a6,6,0,0,1,0,8.48l-32,32a6,6,0,0,1-8.48,0l-32-32a6,6,0,0,1,8.48-8.48L74,193.51V48a6,6,0,0,1,12,0V193.51l21.76-21.75A6,6,0,0,1,116.24,171.76Zm96-96-32-32a6,6,0,0,0-8.48,0l-32,32a6,6,0,0,0,8.48,8.48L170,62.49V208a6,6,0,0,0,12,0V62.49l21.76,21.75a6,6,0,0,0,8.48-8.48Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M117.66,170.34a8,8,0,0,1,0,11.32l-32,32a8,8,0,0,1-11.32,0l-32-32a8,8,0,0,1,11.32-11.32L72,188.69V48a8,8,0,0,1,16,0V188.69l18.34-18.35A8,8,0,0,1,117.66,170.34Zm96-96-32-32a8,8,0,0,0-11.32,0l-32,32a8,8,0,0,0,11.32,11.32L168,67.31V208a8,8,0,0,0,16,0V67.31l18.34,18.35a8,8,0,0,0,11.32-11.32Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M114.83,173.17a4,4,0,0,1,0,5.66l-32,32a4,4,0,0,1-5.66,0l-32-32a4,4,0,0,1,5.66-5.66L76,198.34V48a4,4,0,0,1,8,0V198.34l25.17-25.17A4,4,0,0,1,114.83,173.17Zm96-96-32-32a4,4,0,0,0-5.66,0l-32,32a4,4,0,0,0,5.66,5.66L172,57.66V208a4,4,0,0,0,8,0V57.66l25.17,25.17a4,4,0,1,0,5.66-5.66Z"}))]]),Lu=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M128,20A108,108,0,1,0,236,128,108.12,108.12,0,0,0,128,20Zm0,192a84,84,0,1,1,84-84A84.09,84.09,0,0,1,128,212Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M224,128a96,96,0,1,1-96-96A96,96,0,0,1,224,128Z",opacity:"0.2"}),u.createElement("path",{d:"M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M232,128A104,104,0,1,1,128,24,104.13,104.13,0,0,1,232,128Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M128,26A102,102,0,1,0,230,128,102.12,102.12,0,0,0,128,26Zm0,192a90,90,0,1,1,90-90A90.1,90.1,0,0,1,128,218Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M128,28A100,100,0,1,0,228,128,100.11,100.11,0,0,0,128,28Zm0,192a92,92,0,1,1,92-92A92.1,92.1,0,0,1,128,220Z"}))]]),Hu=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M140,80v41.21l34.17,20.5a12,12,0,1,1-12.34,20.58l-40-24A12,12,0,0,1,116,128V80a12,12,0,0,1,24,0ZM128,28A99.38,99.38,0,0,0,57.24,57.34c-4.69,4.74-9,9.37-13.24,14V64a12,12,0,0,0-24,0v40a12,12,0,0,0,12,12H72a12,12,0,0,0,0-24H57.77C63,86,68.37,80.22,74.26,74.26a76,76,0,1,1,1.58,109,12,12,0,0,0-16.48,17.46A100,100,0,1,0,128,28Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M216,128a88,88,0,1,1-88-88A88,88,0,0,1,216,128Z",opacity:"0.2"}),u.createElement("path",{d:"M136,80v43.47l36.12,21.67a8,8,0,0,1-8.24,13.72l-40-24A8,8,0,0,1,120,128V80a8,8,0,0,1,16,0Zm-8-48A95.44,95.44,0,0,0,60.08,60.15C52.81,67.51,46.35,74.59,40,82V64a8,8,0,0,0-16,0v40a8,8,0,0,0,8,8H72a8,8,0,0,0,0-16H49c7.15-8.42,14.27-16.35,22.39-24.57a80,80,0,1,1,1.66,114.75,8,8,0,1,0-11,11.64A96,96,0,1,0,128,32Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M224,128A96,96,0,0,1,62.11,197.82a8,8,0,1,1,11-11.64A80,80,0,1,0,71.43,71.43C67.9,75,64.58,78.51,61.35,82L77.66,98.34A8,8,0,0,1,72,112H32a8,8,0,0,1-8-8V64a8,8,0,0,1,13.66-5.66L50,70.7c3.22-3.49,6.54-7,10.06-10.55A96,96,0,0,1,224,128ZM128,72a8,8,0,0,0-8,8v48a8,8,0,0,0,3.88,6.86l40,24a8,8,0,1,0,8.24-13.72L136,123.47V80A8,8,0,0,0,128,72Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M134,80v44.6l37.09,22.25a6,6,0,0,1-6.18,10.3l-40-24A6,6,0,0,1,122,128V80a6,6,0,0,1,12,0Zm-6-46A93.4,93.4,0,0,0,61.51,61.56c-8.58,8.68-16,17-23.51,25.8V64a6,6,0,0,0-12,0v40a6,6,0,0,0,6,6H72a6,6,0,0,0,0-12H44.73C52.86,88.29,60.79,79.35,70,70a82,82,0,1,1,1.7,117.62,6,6,0,1,0-8.24,8.72A94,94,0,1,0,128,34Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M136,80v43.47l36.12,21.67a8,8,0,0,1-8.24,13.72l-40-24A8,8,0,0,1,120,128V80a8,8,0,0,1,16,0Zm-8-48A95.44,95.44,0,0,0,60.08,60.15C52.81,67.51,46.35,74.59,40,82V64a8,8,0,0,0-16,0v40a8,8,0,0,0,8,8H72a8,8,0,0,0,0-16H49c7.15-8.42,14.27-16.35,22.39-24.57a80,80,0,1,1,1.66,114.75,8,8,0,1,0-11,11.64A96,96,0,1,0,128,32Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M132,80v45.74l38.06,22.83a4,4,0,0,1-4.12,6.86l-40-24A4,4,0,0,1,124,128V80a4,4,0,0,1,8,0Zm-4-44A91.42,91.42,0,0,0,62.93,63C53.05,73,44.66,82.47,36,92.86V64a4,4,0,0,0-8,0v40a4,4,0,0,0,4,4H72a4,4,0,0,0,0-8H40.47C49.61,89,58.3,79,68.6,68.6a84,84,0,1,1,1.75,120.49,4,4,0,1,0-5.5,5.82A92,92,0,1,0,128,36Z"}))]]),zu=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M71.51,88.49a12,12,0,0,1,17-17L116,99V24a12,12,0,0,1,24,0V99l27.51-27.52a12,12,0,0,1,17,17l-48,48a12,12,0,0,1-17,0ZM224,116H188a12,12,0,0,0,0,24h32v56H36V140H68a12,12,0,0,0,0-24H32a20,20,0,0,0-20,20v64a20,20,0,0,0,20,20H224a20,20,0,0,0,20-20V136A20,20,0,0,0,224,116Zm-20,52a16,16,0,1,0-16,16A16,16,0,0,0,204,168Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M232,136v64a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V136a8,8,0,0,1,8-8H224A8,8,0,0,1,232,136Z",opacity:"0.2"}),u.createElement("path",{d:"M240,136v64a16,16,0,0,1-16,16H32a16,16,0,0,1-16-16V136a16,16,0,0,1,16-16H72a8,8,0,0,1,0,16H32v64H224V136H184a8,8,0,0,1,0-16h40A16,16,0,0,1,240,136Zm-117.66-2.34a8,8,0,0,0,11.32,0l48-48a8,8,0,0,0-11.32-11.32L136,108.69V24a8,8,0,0,0-16,0v84.69L85.66,74.34A8,8,0,0,0,74.34,85.66ZM200,168a12,12,0,1,0-12,12A12,12,0,0,0,200,168Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M74.34,85.66A8,8,0,0,1,85.66,74.34L120,108.69V24a8,8,0,0,1,16,0v84.69l34.34-34.35a8,8,0,0,1,11.32,11.32l-48,48a8,8,0,0,1-11.32,0ZM240,136v64a16,16,0,0,1-16,16H32a16,16,0,0,1-16-16V136a16,16,0,0,1,16-16H84.4a4,4,0,0,1,2.83,1.17L111,145A24,24,0,0,0,145,145l23.8-23.8A4,4,0,0,1,171.6,120H224A16,16,0,0,1,240,136Zm-40,32a12,12,0,1,0-12,12A12,12,0,0,0,200,168Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M238,136v64a14,14,0,0,1-14,14H32a14,14,0,0,1-14-14V136a14,14,0,0,1,14-14H72a6,6,0,0,1,0,12H32a2,2,0,0,0-2,2v64a2,2,0,0,0,2,2H224a2,2,0,0,0,2-2V136a2,2,0,0,0-2-2H184a6,6,0,0,1,0-12h40A14,14,0,0,1,238,136Zm-114.24-3.76a6,6,0,0,0,8.48,0l48-48a6,6,0,0,0-8.48-8.48L134,113.51V24a6,6,0,0,0-12,0v89.51L84.24,75.76a6,6,0,0,0-8.48,8.48ZM198,168a10,10,0,1,0-10,10A10,10,0,0,0,198,168Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M240,136v64a16,16,0,0,1-16,16H32a16,16,0,0,1-16-16V136a16,16,0,0,1,16-16H72a8,8,0,0,1,0,16H32v64H224V136H184a8,8,0,0,1,0-16h40A16,16,0,0,1,240,136Zm-117.66-2.34a8,8,0,0,0,11.32,0l48-48a8,8,0,0,0-11.32-11.32L136,108.69V24a8,8,0,0,0-16,0v84.69L85.66,74.34A8,8,0,0,0,74.34,85.66ZM200,168a12,12,0,1,0-12,12A12,12,0,0,0,200,168Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M236,136v64a12,12,0,0,1-12,12H32a12,12,0,0,1-12-12V136a12,12,0,0,1,12-12H72a4,4,0,0,1,0,8H32a4,4,0,0,0-4,4v64a4,4,0,0,0,4,4H224a4,4,0,0,0,4-4V136a4,4,0,0,0-4-4H184a4,4,0,0,1,0-8h40A12,12,0,0,1,236,136Zm-110.83-5.17a4,4,0,0,0,5.66,0l48-48a4,4,0,1,0-5.66-5.66L132,118.34V24a4,4,0,0,0-8,0v94.34L82.83,77.17a4,4,0,0,0-5.66,5.66ZM196,168a8,8,0,1,0-8,8A8,8,0,0,0,196,168Z"}))]]),Nu=new Map([["bold",u.createElement(u.Fragment,null,u.createElement("path",{d:"M208,76H180V56A52,52,0,0,0,76,56V76H48A20,20,0,0,0,28,96V208a20,20,0,0,0,20,20H208a20,20,0,0,0,20-20V96A20,20,0,0,0,208,76ZM100,56a28,28,0,0,1,56,0V76H100ZM204,204H52V100H204Zm-60-52a16,16,0,1,1-16-16A16,16,0,0,1,144,152Z"}))],["duotone",u.createElement(u.Fragment,null,u.createElement("path",{d:"M216,96V208a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V96a8,8,0,0,1,8-8H208A8,8,0,0,1,216,96Z",opacity:"0.2"}),u.createElement("path",{d:"M208,80H176V56a48,48,0,0,0-96,0V80H48A16,16,0,0,0,32,96V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V96A16,16,0,0,0,208,80ZM96,56a32,32,0,0,1,64,0V80H96ZM208,208H48V96H208V208Zm-68-56a12,12,0,1,1-12-12A12,12,0,0,1,140,152Z"}))],["fill",u.createElement(u.Fragment,null,u.createElement("path",{d:"M208,80H176V56a48,48,0,0,0-96,0V80H48A16,16,0,0,0,32,96V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V96A16,16,0,0,0,208,80Zm-80,84a12,12,0,1,1,12-12A12,12,0,0,1,128,164Zm32-84H96V56a32,32,0,0,1,64,0Z"}))],["light",u.createElement(u.Fragment,null,u.createElement("path",{d:"M208,82H174V56a46,46,0,0,0-92,0V82H48A14,14,0,0,0,34,96V208a14,14,0,0,0,14,14H208a14,14,0,0,0,14-14V96A14,14,0,0,0,208,82ZM94,56a34,34,0,0,1,68,0V82H94ZM210,208a2,2,0,0,1-2,2H48a2,2,0,0,1-2-2V96a2,2,0,0,1,2-2H208a2,2,0,0,1,2,2Zm-72-56a10,10,0,1,1-10-10A10,10,0,0,1,138,152Z"}))],["regular",u.createElement(u.Fragment,null,u.createElement("path",{d:"M208,80H176V56a48,48,0,0,0-96,0V80H48A16,16,0,0,0,32,96V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V96A16,16,0,0,0,208,80ZM96,56a32,32,0,0,1,64,0V80H96ZM208,208H48V96H208V208Zm-68-56a12,12,0,1,1-12-12A12,12,0,0,1,140,152Z"}))],["thin",u.createElement(u.Fragment,null,u.createElement("path",{d:"M208,84H172V56a44,44,0,0,0-88,0V84H48A12,12,0,0,0,36,96V208a12,12,0,0,0,12,12H208a12,12,0,0,0,12-12V96A12,12,0,0,0,208,84ZM92,56a36,36,0,0,1,72,0V84H92ZM212,208a4,4,0,0,1-4,4H48a4,4,0,0,1-4-4V96a4,4,0,0,1,4-4H208a4,4,0,0,1,4,4Zm-76-56a8,8,0,1,1-8-8A8,8,0,0,1,136,152Z"}))]]),ds=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:Au}));ds.displayName="ArrowDownIcon";const Bu=ds,fs=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:Vu}));fs.displayName="ArrowLeftIcon";const Gu=fs,gs=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:Tu}));gs.displayName="ArrowUpIcon";const Zu=gs,ms=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:Du}));ms.displayName="ArrowsClockwiseIcon";const Do=ms,ps=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:Ou}));ps.displayName="ArrowsDownUpIcon";const Wu=ps,hs=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:Lu}));hs.displayName="CircleIcon";const qu=hs,vs=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:Hu}));vs.displayName="ClockCounterClockwiseIcon";const Uu=vs,ys=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:zu}));ys.displayName="DownloadIcon";const s1=ys,Ss=u.forwardRef((e,t)=>u.createElement(dt,{ref:t,...e,weights:Nu}));Ss.displayName="LockIcon";const Ku=Ss,Yu=Kt({viewBox:"0 0 14 14",d:"M14,7.77 L14,6.17 L12.06,5.53 L11.61,4.44 L12.49,2.6 L11.36,1.47 L9.55,2.38 L8.46,1.93 L7.77,0.01 L6.17,0.01 L5.54,1.95 L4.43,2.4 L2.59,1.52 L1.46,2.65 L2.37,4.46 L1.92,5.55 L0,6.23 L0,7.82 L1.94,8.46 L2.39,9.55 L1.51,11.39 L2.64,12.52 L4.45,11.61 L5.54,12.06 L6.23,13.98 L7.82,13.98 L8.45,12.04 L9.56,11.59 L11.4,12.47 L12.53,11.34 L11.61,9.53 L12.08,8.44 L14,7.75 L14,7.77 Z M7,10 C5.34,10 4,8.66 4,7 C4,5.34 5.34,4 7,4 C8.66,4 10,5.34 10,7 C10,8.66 8.66,10 7,10 Z",displayName:"SettingsIcon"}),Xu=Kt({d:"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z",displayName:"ChevronLeftIcon"}),Ju=Kt({d:"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z",displayName:"ChevronRightIcon"}),Qu=Kt({displayName:"ArrowRightIcon",path:d.jsxs("g",{fill:"currentColor",children:[d.jsx("path",{d:"M13.584,12a2.643,2.643,0,0,1-.775,1.875L3.268,23.416a1.768,1.768,0,0,1-2.5-2.5l8.739-8.739a.25.25,0,0,0,0-.354L.768,3.084a1.768,1.768,0,0,1,2.5-2.5l9.541,9.541A2.643,2.643,0,0,1,13.584,12Z"}),d.jsx("path",{d:"M23.75,12a2.643,2.643,0,0,1-.775,1.875l-9.541,9.541a1.768,1.768,0,0,1-2.5-2.5l8.739-8.739a.25.25,0,0,0,0-.354L10.934,3.084a1.768,1.768,0,0,1,2.5-2.5l9.541,9.541A2.643,2.643,0,0,1,23.75,12Z"})]})}),ec=Kt({displayName:"ArrowLeftIcon",path:d.jsxs("g",{fill:"currentColor",children:[d.jsx("path",{d:"M10.416,12a2.643,2.643,0,0,1,.775-1.875L20.732.584a1.768,1.768,0,0,1,2.5,2.5l-8.739,8.739a.25.25,0,0,0,0,.354l8.739,8.739a1.768,1.768,0,0,1-2.5,2.5l-9.541-9.541A2.643,2.643,0,0,1,10.416,12Z"}),d.jsx("path",{d:"M.25,12a2.643,2.643,0,0,1,.775-1.875L10.566.584a1.768,1.768,0,0,1,2.5,2.5L4.327,11.823a.25.25,0,0,0,0,.354l8.739,8.739a1.768,1.768,0,0,1-2.5,2.5L1.025,13.875A2.643,2.643,0,0,1,.25,12Z"})]})}),tc=Kt({d:"M12,0A12,12,0,1,0,24,12,12.013,12.013,0,0,0,12,0Zm.25,5a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,12.25,5ZM14.5,18.5h-4a1,1,0,0,1,0-2h.75a.25.25,0,0,0,.25-.25v-4.5a.25.25,0,0,0-.25-.25H10.5a1,1,0,0,1,0-2h1a2,2,0,0,1,2,2v4.75a.25.25,0,0,0,.25.25h.75a1,1,0,1,1,0,2Z"}),Oo=({onClick:e=Object.create(null),isDisabled:t=!1,isFetching:n=!1,isCompact:r=!0,ml:o=void 0,...i})=>{const{t:s}=pe(),a=Qi();return!r&&a!=="base"&&a!=="sm"?d.jsx(on,{minWidth:"112px",colorScheme:"gray",onClick:e,rightIcon:d.jsx(Do,{size:20}),isDisabled:t,isLoading:n,ml:o,...i,children:s("common.refresh")}):d.jsx(qe,{label:s("common.refresh"),children:d.jsx(st,{"aria-label":"refresh",colorScheme:"gray",onClick:e,icon:d.jsx(Do,{size:20}),isDisabled:t,isLoading:n,ml:o,...i})})},nc=async(e,t)=>{try{const n={countOnly:!0,platform:e,siteId:t};return(await ae.get("devices",{params:n})).data}catch(n){throw n}},a1=({enabled:e,platform:t="all",siteId:n})=>{const{t:r}=pe(),o=qt();return Re(["devices","count",{platform:t,siteId:n}],()=>nc(t,n),{enabled:e,onError:i=>{var s,a;o.isActive("inventory-fetching-error")||o({id:"inventory-fetching-error",title:r("common.error"),description:r("crud.error_fetching_obj",{obj:r("devices.one"),e:(a=(s=i==null?void 0:i.response)==null?void 0:s.data)==null?void 0:a.ErrorDescription}),status:"error",duration:3,isClosable:!0,position:"top-right"})}})},rc=async(e,t,n,r,o,i)=>{console.log("getDevices start");let s="";i&&i.length>0&&(s=i.map(a=>`${a.id}:${a.sort.charAt(0)}`).join(","));try{const a={deviceWithStatus:!0,limit:e,offset:t,platform:n,...r?{model:r}:{},...o!=null?{siteId:o}:{},...s?{orderBy:s}:{}},l=await ae.get("devices",{params:a});return console.log("Fetched getDevices data:",l.data),l.data}catch(a){throw console.error("Error fetching getDevices:",a),a}},l1=({pageInfo:e,sortInfo:t,enabled:n,onError:r,platform:o,model:i,siteId:s})=>{const a=(e==null?void 0:e.limit)!==void 0?e.limit*e.index:0;return Re(["devices","all",{limit:e==null?void 0:e.limit,offset:a,platform:o,model:i,siteId:s,sortInfo:t}],()=>rc((e==null?void 0:e.limit)||0,a,o??"all",i,s,t),{keepPreviousData:!0,enabled:n&&e!==void 0,staleTime:3e4,onError:r})},oc=e=>ae.get(`device/${e}/status`).then(t=>t.data),u1=({serialNumber:e,onError:t})=>Re(["device",e,"status"],()=>oc(e),{enabled:e!==void 0&&e!=="",onError:t}),ic=async(e,t)=>{console.log("getDeviceHealthChecks start");try{const n=await ae.get(`device/${e}/healthchecks?newest=true&limit=${t??50}`);return console.log("Fetched getDeviceHealthChecks data:",n.data),n.data}catch(n){throw console.error("Error fetching getDeviceHealthChecks:",n),n}},c1=({serialNumber:e,onError:t,limit:n})=>Re(["device",e,"healthchecks",{limit:n}],()=>ic(e,n),{enabled:e!==void 0&&e!=="",keepPreviousData:!0,onError:t}),sc=async e=>{console.log("getSingleDevice start");try{const t=await ae.get(`device/${e}`);return console.log("Fetched getSingleDevice data:",t.data),t.data}catch(t){throw console.error("Error fetching getSingleDevice:",t),t}},d1=({serialNumber:e,onClose:t,disableToast:n=!1})=>{const{t:r}=pe();return Re(["device",e],()=>sc(e),{staleTime:60*1e3,enabled:e!==void 0&&e!=="",onError:o=>{var i,s,a;n||sn.error({content:((i=o==null?void 0:o.response)==null?void 0:i.status)===404?r("devices.not_found_gateway"):r("crud.error_fetching_obj",{e:(a=(s=o==null?void 0:o.response)==null?void 0:s.data)==null?void 0:a.ErrorDescription,obj:r("devices.one")}),duration:3}),t&&t()}})},ac=async e=>ae.delete(`device/${e}`),f1=({serialNumber:e})=>{const t=bt();return ft(ac,{onSuccess:()=>{t.invalidateQueries(["devices"]),t.invalidateQueries(["device",e])}})},g1=({serialNumber:e})=>ft(()=>ae.post(`device/${e}/leds`,{serialNumber:e,when:0,pattern:"blink",duration:30})),lc=({serialNumber:e,onClose:t})=>{const{t:n}=pe();return ft(r=>ae.post(`device/${e}/factory`,{serialNumber:e,keepRedirector:r.keepRedirector}),{onSuccess:()=>{sn.success(`${n("common.success")}: ${n("commands.factory_reset_success")}`,5),t()},onError:r=>{var o,i;sn.error(`${n("common.error")}: ${n("commands.factory_reset_error",{e:((i=(o=r==null?void 0:r.response)==null?void 0:o.data)==null?void 0:i.ErrorDescription)||r.message})}`,5)}})},m1=({serialNumber:e,extraId:t})=>{const{t:n}=pe(),r=qt();return Re(["get-gateway-device-rtty",e,t],()=>ae.get(`device/${e}/rtty`).then(({data:o})=>o),{enabled:!1,onSuccess:({server:o,viewport:i,connectionId:s})=>{var l;const a=`https://${o}:${i}/connect/${s}`;(l=window.open(a,"_blank"))==null||l.focus()},onError:o=>{var i,s,a;r.isActive("get-gateway-device-rtty-error")||r({id:"get-gateway-device-rtty",title:n("common.error"),description:((i=o==null?void 0:o.response)==null?void 0:i.status)===404?n("devices.not_found_gateway"):n("devices.error_rtty",{e:(a=(s=o==null?void 0:o.response)==null?void 0:s.data)==null?void 0:a.ErrorDescription}),status:"error",duration:3,isClosable:!0,position:"top-right"})}})},uc=async({serialNumber:e,notes:t})=>ae.put(`device/${e}`,{notes:t}),p1=({serialNumber:e})=>{const t=bt();return ft(uc,{onSuccess:()=>{t.invalidateQueries(["devices"]),t.invalidateQueries(["device",e])}})},cc=e=>ae.get(`device/${e}/statistics?lastOnly=true`).then(t=>t.data),h1=({serialNumber:e,onError:t})=>Re(["device",e,"last-statistics"],()=>cc(e),{enabled:e!==void 0&&e!=="",staleTime:1e3*60,refetchInterval:1e3*60,onError:t}),dc=(e,t)=>async()=>ae.get(`device/${t}/statistics?newest=true&limit=${e}`).then(n=>n.data),v1=({serialNumber:e,limit:t,onError:n})=>Re(["deviceStatistics",e,"newest",{limit:t}],dc(t,e),{enabled:e!==void 0&&e!=="",staleTime:1e3*60,onError:n}),fc=e=>async()=>ae.get(`/ouis?macList=${e==null?void 0:e.join(",")}`).then(t=>t.data),y1=({macs:e,onError:t})=>Re(["ouis",e],fc(e),{enabled:e!==void 0&&e.length>0,staleTime:1e3*60,onError:t}),S1=({modalProps:{isOpen:e},confirm:t,cancel:n})=>{const{t:r}=pe(),o=u.useRef(null);return d.jsx(Qa,{isOpen:e,onClose:n,leastDestructiveRef:o,isCentered:!0,children:d.jsx(Ca,{children:d.jsxs(el,{children:[d.jsx(wa,{children:r("commands.abort_command_title")}),d.jsx(_a,{children:r("commands.abort_command_explanation")}),d.jsxs(tl,{children:[d.jsx(on,{ref:o,onClick:n,mr:4,children:r("common.cancel")}),d.jsx(on,{onClick:t,colorScheme:"red",children:r("common.confirm")})]})]})})})},gc=(e,t)=>{var s,a;if(!e||!t)return null;const n=e.split("."),{length:r}=n;if(r<2)return null;const o=n.slice(0,r-1),i=n[r-1];return((a=(s=t[o.slice(0,r-1).join(".")])==null?void 0:s.properties[i??""])==null?void 0:a.description)??null},mc=({definitionKey:e})=>{const{configurationDescriptions:t}=Ra(),n=u.useMemo(()=>gc(e,t),[t]);return n?d.jsx(qe,{hasArrow:!0,label:n,children:d.jsx(tc,{ml:2,mb:"2px"})}):null},pc=Ie.memo(mc),hc=({element:e,label:t,value:n,onChange:r,onBlur:o,error:i,isError:s,isRequired:a,isDisabled:l,definitionKey:c})=>d.jsxs(Ki,{isInvalid:s,isRequired:a,isDisabled:l,_disabled:{opacity:.8},children:[d.jsxs(Xi,{ms:"4px",fontSize:"md",fontWeight:"normal",_disabled:{opacity:.8},children:[t," ",d.jsx(pc,{definitionKey:c})]}),e??d.jsx(us,{isChecked:n,onChange:r,onBlur:o,borderRadius:"15px",size:"lg",isDisabled:l,_disabled:{opacity:.8,cursor:"not-allowed"}}),d.jsx(Yi,{children:i})]}),vc=Ie.memo(hc),yc=({name:e,isDisabled:t=!1,label:n,isRequired:r=!1,defaultValue:o,element:i,falseIsUndefined:s,definitionKey:a,onChangeCallback:l})=>{const{value:c,error:f,isError:p,onChange:g,onBlur:m}=Ja({name:e}),h=u.useCallback(v=>{s&&!v.target.checked?g(void 0):g(v.target.checked),l&&l(v.target.checked)},[l]);return d.jsx(vc,{label:n??e,value:c===void 0&&o!==void 0?o:c!==void 0&&c,onChange:h,error:f,onBlur:m,isError:p,isDisabled:t,isRequired:r,element:i,definitionKey:a})},b1=Ie.memo(yc);var He=d.Fragment,M=function(t,n,r){return Ei.call(n,"css")?d.jsx(Mi,Pi(t,n),r):d.jsx(t,n,r)},_e=function(t,n,r){return Ei.call(n,"css")?d.jsxs(Mi,Pi(t,n),r):d.jsxs(t,n,r)};const cn={black:"#000",white:"#fff"},Vt={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},Tt={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},Dt={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},Ot={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},Lt={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"},Qt={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},Sc={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"};function Zt(e){let t="https://mui.com/production-error/?code="+e;for(let n=1;n<arguments.length;n+=1)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified MUI error #"+e+"; visit "+t+" for the full message."}const bc=Object.freeze(Object.defineProperty({__proto__:null,default:Zt},Symbol.toStringTag,{value:"Module"})),dn="$$material";function xc(e,t){const n=Ma({key:"css",prepend:e});if(t){const r=n.insert;n.insert=(...o)=>(o[1].styles.match(/^@layer\s+[^{]*$/)||(o[1].styles=`@layer mui {${o[1].styles}}`),r(...o))}return n}const hr=new Map;function Cc(e){const{injectFirst:t,enableCssLayer:n,children:r}=e,o=u.useMemo(()=>{const i=`${t}-${n}`;if(typeof document=="object"&&hr.has(i))return hr.get(i);const s=xc(t,n);return hr.set(i,s),s},[t,n]);return t||n?d.jsx(Ea,{value:o,children:r}):r}function wc(e){return e==null||Object.keys(e).length===0}function bs(e){const{styles:t,defaultTheme:n={}}=e,r=typeof t=="function"?o=>t(wc(o)?n:o):t;return d.jsx(Pa,{styles:r})}function xs(e,t){return $a(e,t)}const _c=(e,t)=>{Array.isArray(e.__emotion_styles)&&(e.__emotion_styles=t(e.__emotion_styles))},Lo=[];function Cs(e){return Lo[0]=e,Fa(Lo)}const Rc=Object.freeze(Object.defineProperty({__proto__:null,GlobalStyles:bs,StyledEngineProvider:Cc,ThemeContext:Zr,css:ka,default:xs,internal_processStyles:_c,internal_serializeStyles:Cs,keyframes:ja},Symbol.toStringTag,{value:"Module"}));function mt(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function ws(e){if(u.isValidElement(e)||!mt(e))return e;const t={};return Object.keys(e).forEach(n=>{t[n]=ws(e[n])}),t}function Xe(e,t,n={clone:!0}){const r=n.clone?I({},e):e;return mt(e)&&mt(t)&&Object.keys(t).forEach(o=>{u.isValidElement(t[o])?r[o]=t[o]:mt(t[o])&&Object.prototype.hasOwnProperty.call(e,o)&&mt(e[o])?r[o]=Xe(e[o],t[o],n):n.clone?r[o]=mt(t[o])?ws(t[o]):t[o]:r[o]=t[o]}),r}const Ec=Object.freeze(Object.defineProperty({__proto__:null,default:Xe,isPlainObject:mt},Symbol.toStringTag,{value:"Module"})),Mc=["values","unit","step"],Pc=e=>{const t=Object.keys(e).map(n=>({key:n,val:e[n]}))||[];return t.sort((n,r)=>n.val-r.val),t.reduce((n,r)=>I({},n,{[r.key]:r.val}),{})};function _s(e){const{values:t={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:n="px",step:r=5}=e,o=$e(e,Mc),i=Pc(t),s=Object.keys(i);function a(g){return`@media (min-width:${typeof t[g]=="number"?t[g]:g}${n})`}function l(g){return`@media (max-width:${(typeof t[g]=="number"?t[g]:g)-r/100}${n})`}function c(g,m){const h=s.indexOf(m);return`@media (min-width:${typeof t[g]=="number"?t[g]:g}${n}) and (max-width:${(h!==-1&&typeof t[s[h]]=="number"?t[s[h]]:m)-r/100}${n})`}function f(g){return s.indexOf(g)+1<s.length?c(g,s[s.indexOf(g)+1]):a(g)}function p(g){const m=s.indexOf(g);return m===0?a(s[1]):m===s.length-1?l(s[m]):c(g,s[s.indexOf(g)+1]).replace("@media","@media not all and")}return I({keys:s,values:i,up:a,down:l,between:c,only:f,not:p,unit:n},o)}const $c={borderRadius:4};function nn(e,t){return t?Xe(e,t,{clone:!1}):e}const Yr={xs:0,sm:600,md:900,lg:1200,xl:1536},Ho={keys:["xs","sm","md","lg","xl"],up:e=>`@media (min-width:${Yr[e]}px)`};function ut(e,t,n){const r=e.theme||{};if(Array.isArray(t)){const i=r.breakpoints||Ho;return t.reduce((s,a,l)=>(s[i.up(i.keys[l])]=n(t[l]),s),{})}if(typeof t=="object"){const i=r.breakpoints||Ho;return Object.keys(t).reduce((s,a)=>{if(Object.keys(i.values||Yr).indexOf(a)!==-1){const l=i.up(a);s[l]=n(t[a],a)}else{const l=a;s[l]=t[l]}return s},{})}return n(t)}function Fc(e={}){var t;return((t=e.keys)==null?void 0:t.reduce((r,o)=>{const i=e.up(o);return r[i]={},r},{}))||{}}function zo(e,t){return e.reduce((n,r)=>{const o=n[r];return(!o||Object.keys(o).length===0)&&delete n[r],n},t)}function Qe(e){if(typeof e!="string")throw new Error(Zt(7));return e.charAt(0).toUpperCase()+e.slice(1)}const kc=Object.freeze(Object.defineProperty({__proto__:null,default:Qe},Symbol.toStringTag,{value:"Module"}));function Bn(e,t,n=!0){if(!t||typeof t!="string")return null;if(e&&e.vars&&n){const r=`vars.${t}`.split(".").reduce((o,i)=>o&&o[i]?o[i]:null,e);if(r!=null)return r}return t.split(".").reduce((r,o)=>r&&r[o]!=null?r[o]:null,e)}function Vn(e,t,n,r=n){let o;return typeof e=="function"?o=e(n):Array.isArray(e)?o=e[n]||r:o=Bn(e,n)||r,t&&(o=t(o,r,e)),o}function fe(e){const{prop:t,cssProperty:n=e.prop,themeKey:r,transform:o}=e,i=s=>{if(s[t]==null)return null;const a=s[t],l=s.theme,c=Bn(l,r)||{};return ut(s,a,p=>{let g=Vn(c,o,p);return p===g&&typeof p=="string"&&(g=Vn(c,o,`${t}${p==="default"?"":Qe(p)}`,p)),n===!1?g:{[n]:g}})};return i.propTypes={},i.filterProps=[t],i}function jc(e){const t={};return n=>(t[n]===void 0&&(t[n]=e(n)),t[n])}const Ic={m:"margin",p:"padding"},Ac={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},No={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Vc=jc(e=>{if(e.length>2)if(No[e])e=No[e];else return[e];const[t,n]=e.split(""),r=Ic[t],o=Ac[n]||"";return Array.isArray(o)?o.map(i=>r+i):[r+o]}),Xr=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],Jr=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...Xr,...Jr];function pn(e,t,n,r){var o;const i=(o=Bn(e,t,!1))!=null?o:n;return typeof i=="number"?s=>typeof s=="string"?s:i*s:Array.isArray(i)?s=>typeof s=="string"?s:i[s]:typeof i=="function"?i:()=>{}}function Rs(e){return pn(e,"spacing",8)}function hn(e,t){if(typeof t=="string"||t==null)return t;const n=Math.abs(t),r=e(n);return t>=0?r:typeof r=="number"?-r:`-${r}`}function Tc(e,t){return n=>e.reduce((r,o)=>(r[o]=hn(t,n),r),{})}function Dc(e,t,n,r){if(t.indexOf(n)===-1)return null;const o=Vc(n),i=Tc(o,r),s=e[n];return ut(e,s,i)}function Es(e,t){const n=Rs(e.theme);return Object.keys(e).map(r=>Dc(e,t,r,n)).reduce(nn,{})}function ue(e){return Es(e,Xr)}ue.propTypes={};ue.filterProps=Xr;function ce(e){return Es(e,Jr)}ce.propTypes={};ce.filterProps=Jr;function Oc(e=8){if(e.mui)return e;const t=Rs({spacing:e}),n=(...r)=>(r.length===0?[1]:r).map(i=>{const s=t(i);return typeof s=="number"?`${s}px`:s}).join(" ");return n.mui=!0,n}function Gn(...e){const t=e.reduce((r,o)=>(o.filterProps.forEach(i=>{r[i]=o}),r),{}),n=r=>Object.keys(r).reduce((o,i)=>t[i]?nn(o,t[i](r)):o,{});return n.propTypes={},n.filterProps=e.reduce((r,o)=>r.concat(o.filterProps),[]),n}function ze(e){return typeof e!="number"?e:`${e}px solid`}function Ge(e,t){return fe({prop:e,themeKey:"borders",transform:t})}const Lc=Ge("border",ze),Hc=Ge("borderTop",ze),zc=Ge("borderRight",ze),Nc=Ge("borderBottom",ze),Bc=Ge("borderLeft",ze),Gc=Ge("borderColor"),Zc=Ge("borderTopColor"),Wc=Ge("borderRightColor"),qc=Ge("borderBottomColor"),Uc=Ge("borderLeftColor"),Kc=Ge("outline",ze),Yc=Ge("outlineColor"),Zn=e=>{if(e.borderRadius!==void 0&&e.borderRadius!==null){const t=pn(e.theme,"shape.borderRadius",4),n=r=>({borderRadius:hn(t,r)});return ut(e,e.borderRadius,n)}return null};Zn.propTypes={};Zn.filterProps=["borderRadius"];Gn(Lc,Hc,zc,Nc,Bc,Gc,Zc,Wc,qc,Uc,Zn,Kc,Yc);const Wn=e=>{if(e.gap!==void 0&&e.gap!==null){const t=pn(e.theme,"spacing",8),n=r=>({gap:hn(t,r)});return ut(e,e.gap,n)}return null};Wn.propTypes={};Wn.filterProps=["gap"];const qn=e=>{if(e.columnGap!==void 0&&e.columnGap!==null){const t=pn(e.theme,"spacing",8),n=r=>({columnGap:hn(t,r)});return ut(e,e.columnGap,n)}return null};qn.propTypes={};qn.filterProps=["columnGap"];const Un=e=>{if(e.rowGap!==void 0&&e.rowGap!==null){const t=pn(e.theme,"spacing",8),n=r=>({rowGap:hn(t,r)});return ut(e,e.rowGap,n)}return null};Un.propTypes={};Un.filterProps=["rowGap"];const Xc=fe({prop:"gridColumn"}),Jc=fe({prop:"gridRow"}),Qc=fe({prop:"gridAutoFlow"}),ed=fe({prop:"gridAutoColumns"}),td=fe({prop:"gridAutoRows"}),nd=fe({prop:"gridTemplateColumns"}),rd=fe({prop:"gridTemplateRows"}),od=fe({prop:"gridTemplateAreas"}),id=fe({prop:"gridArea"});Gn(Wn,qn,Un,Xc,Jc,Qc,ed,td,nd,rd,od,id);function Bt(e,t){return t==="grey"?t:e}const sd=fe({prop:"color",themeKey:"palette",transform:Bt}),ad=fe({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:Bt}),ld=fe({prop:"backgroundColor",themeKey:"palette",transform:Bt});Gn(sd,ad,ld);function Ve(e){return e<=1&&e!==0?`${e*100}%`:e}const ud=fe({prop:"width",transform:Ve}),Qr=e=>{if(e.maxWidth!==void 0&&e.maxWidth!==null){const t=n=>{var r,o;const i=((r=e.theme)==null||(r=r.breakpoints)==null||(r=r.values)==null?void 0:r[n])||Yr[n];return i?((o=e.theme)==null||(o=o.breakpoints)==null?void 0:o.unit)!=="px"?{maxWidth:`${i}${e.theme.breakpoints.unit}`}:{maxWidth:i}:{maxWidth:Ve(n)}};return ut(e,e.maxWidth,t)}return null};Qr.filterProps=["maxWidth"];const cd=fe({prop:"minWidth",transform:Ve}),dd=fe({prop:"height",transform:Ve}),fd=fe({prop:"maxHeight",transform:Ve}),gd=fe({prop:"minHeight",transform:Ve});fe({prop:"size",cssProperty:"width",transform:Ve});fe({prop:"size",cssProperty:"height",transform:Ve});const md=fe({prop:"boxSizing"});Gn(ud,Qr,cd,dd,fd,gd,md);const vn={border:{themeKey:"borders",transform:ze},borderTop:{themeKey:"borders",transform:ze},borderRight:{themeKey:"borders",transform:ze},borderBottom:{themeKey:"borders",transform:ze},borderLeft:{themeKey:"borders",transform:ze},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:ze},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Zn},color:{themeKey:"palette",transform:Bt},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:Bt},backgroundColor:{themeKey:"palette",transform:Bt},p:{style:ce},pt:{style:ce},pr:{style:ce},pb:{style:ce},pl:{style:ce},px:{style:ce},py:{style:ce},padding:{style:ce},paddingTop:{style:ce},paddingRight:{style:ce},paddingBottom:{style:ce},paddingLeft:{style:ce},paddingX:{style:ce},paddingY:{style:ce},paddingInline:{style:ce},paddingInlineStart:{style:ce},paddingInlineEnd:{style:ce},paddingBlock:{style:ce},paddingBlockStart:{style:ce},paddingBlockEnd:{style:ce},m:{style:ue},mt:{style:ue},mr:{style:ue},mb:{style:ue},ml:{style:ue},mx:{style:ue},my:{style:ue},margin:{style:ue},marginTop:{style:ue},marginRight:{style:ue},marginBottom:{style:ue},marginLeft:{style:ue},marginX:{style:ue},marginY:{style:ue},marginInline:{style:ue},marginInlineStart:{style:ue},marginInlineEnd:{style:ue},marginBlock:{style:ue},marginBlockStart:{style:ue},marginBlockEnd:{style:ue},displayPrint:{cssProperty:!1,transform:e=>({"@media print":{display:e}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:Wn},rowGap:{style:Un},columnGap:{style:qn},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:Ve},maxWidth:{style:Qr},minWidth:{transform:Ve},height:{transform:Ve},maxHeight:{transform:Ve},minHeight:{transform:Ve},boxSizing:{},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function pd(...e){const t=e.reduce((r,o)=>r.concat(Object.keys(o)),[]),n=new Set(t);return e.every(r=>n.size===Object.keys(r).length)}function hd(e,t){return typeof e=="function"?e(t):e}function Ms(){function e(n,r,o,i){const s={[n]:r,theme:o},a=i[n];if(!a)return{[n]:r};const{cssProperty:l=n,themeKey:c,transform:f,style:p}=a;if(r==null)return null;if(c==="typography"&&r==="inherit")return{[n]:r};const g=Bn(o,c)||{};return p?p(s):ut(s,r,h=>{let v=Vn(g,f,h);return h===v&&typeof h=="string"&&(v=Vn(g,f,`${n}${h==="default"?"":Qe(h)}`,h)),l===!1?v:{[l]:v}})}function t(n){var r;const{sx:o,theme:i={},nested:s}=n||{};if(!o)return null;const a=(r=i.unstable_sxConfig)!=null?r:vn;function l(c){let f=c;if(typeof c=="function")f=c(i);else if(typeof c!="object")return c;if(!f)return null;const p=Fc(i.breakpoints),g=Object.keys(p);let m=p;return Object.keys(f).forEach(h=>{const v=hd(f[h],i);if(v!=null)if(typeof v=="object")if(a[h])m=nn(m,e(h,v,i,a));else{const y=ut({theme:i},v,S=>({[h]:S}));pd(y,v)?m[h]=t({sx:v,theme:i,nested:!0}):m=nn(m,y)}else m=nn(m,e(h,v,i,a))}),!s&&i.modularCssLayers?{"@layer sx":zo(g,m)}:zo(g,m)}return Array.isArray(o)?o.map(l):l(o)}return t}const yn=Ms();yn.filterProps=["sx"];function Ps(e,t){const n=this;return n.vars&&typeof n.getColorSchemeSelector=="function"?{[n.getColorSchemeSelector(e).replace(/(\[[^\]]+\])/,"*:where($1)")]:t}:n.palette.mode===e?t:{}}const vd=["breakpoints","palette","spacing","shape"];function eo(e={},...t){const{breakpoints:n={},palette:r={},spacing:o,shape:i={}}=e,s=$e(e,vd),a=_s(n),l=Oc(o);let c=Xe({breakpoints:a,direction:"ltr",components:{},palette:I({mode:"light"},r),spacing:l,shape:I({},$c,i)},s);return c.applyStyles=Ps,c=t.reduce((f,p)=>Xe(f,p),c),c.unstable_sxConfig=I({},vn,s==null?void 0:s.unstable_sxConfig),c.unstable_sx=function(p){return yn({sx:p,theme:this})},c}const yd=Object.freeze(Object.defineProperty({__proto__:null,default:eo,private_createBreakpoints:_s,unstable_applyStyles:Ps},Symbol.toStringTag,{value:"Module"}));function Sd(e){return Object.keys(e).length===0}function to(e=null){const t=u.useContext(Zr);return!t||Sd(t)?e:t}const bd=eo();function $s(e=bd){return to(e)}function vr(e){const t=Cs(e);return e!==t&&t.styles?(t.styles.match(/^@layer\s+[^{]*$/)||(t.styles=`@layer global{${t.styles}}`),t):e}function Fs({styles:e,themeId:t,defaultTheme:n={}}){const r=$s(n),o=t&&r[t]||r;let i=typeof e=="function"?e(o):e;return o.modularCssLayers&&(Array.isArray(i)?i=i.map(s=>vr(typeof s=="function"?s(o):s)):i=vr(i)),d.jsx(bs,{styles:i})}const xd=["sx"],Cd=e=>{var t,n;const r={systemProps:{},otherProps:{}},o=(t=e==null||(n=e.theme)==null?void 0:n.unstable_sxConfig)!=null?t:vn;return Object.keys(e).forEach(i=>{o[i]?r.systemProps[i]=e[i]:r.otherProps[i]=e[i]}),r};function ks(e){const{sx:t}=e,n=$e(e,xd),{systemProps:r,otherProps:o}=Cd(n);let i;return Array.isArray(t)?i=[r,...t]:typeof t=="function"?i=(...s)=>{const a=t(...s);return mt(a)?I({},r,a):r}:i=I({},r,t),I({},o,{sx:i})}const wd=Object.freeze(Object.defineProperty({__proto__:null,default:yn,extendSxProp:ks,unstable_createStyleFunctionSx:Ms,unstable_defaultSxConfig:vn},Symbol.toStringTag,{value:"Module"})),Bo=e=>e,_d=()=>{let e=Bo;return{configure(t){e=t},generate(t){return e(t)},reset(){e=Bo}}},js=_d(),Rd=["className","component"];function Ed(e={}){const{themeId:t,defaultTheme:n,defaultClassName:r="MuiBox-root",generateClassName:o}=e,i=xs("div",{shouldForwardProp:a=>a!=="theme"&&a!=="sx"&&a!=="as"})(yn);return u.forwardRef(function(l,c){const f=$s(n),p=ks(l),{className:g,component:m="div"}=p,h=$e(p,Rd);return d.jsx(i,I({as:m,ref:c,className:ln(g,o?o(r):r),theme:t&&f[t]||f},h))})}const Md={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function Kn(e,t,n="Mui"){const r=Md[t];return r?`${n}-${r}`:`${js.generate(e)}-${t}`}function Yn(e,t,n="Mui"){const r={};return t.forEach(o=>{r[o]=Kn(e,o,n)}),r}var Is={exports:{}},re={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var no=Symbol.for("react.transitional.element"),ro=Symbol.for("react.portal"),Xn=Symbol.for("react.fragment"),Jn=Symbol.for("react.strict_mode"),Qn=Symbol.for("react.profiler"),er=Symbol.for("react.consumer"),tr=Symbol.for("react.context"),nr=Symbol.for("react.forward_ref"),rr=Symbol.for("react.suspense"),or=Symbol.for("react.suspense_list"),ir=Symbol.for("react.memo"),sr=Symbol.for("react.lazy"),Pd=Symbol.for("react.view_transition"),$d=Symbol.for("react.client.reference");function Ze(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case no:switch(e=e.type,e){case Xn:case Qn:case Jn:case rr:case or:case Pd:return e;default:switch(e=e&&e.$$typeof,e){case tr:case nr:case sr:case ir:return e;case er:return e;default:return t}}case ro:return t}}}re.ContextConsumer=er;re.ContextProvider=tr;re.Element=no;re.ForwardRef=nr;re.Fragment=Xn;re.Lazy=sr;re.Memo=ir;re.Portal=ro;re.Profiler=Qn;re.StrictMode=Jn;re.Suspense=rr;re.SuspenseList=or;re.isContextConsumer=function(e){return Ze(e)===er};re.isContextProvider=function(e){return Ze(e)===tr};re.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===no};re.isForwardRef=function(e){return Ze(e)===nr};re.isFragment=function(e){return Ze(e)===Xn};re.isLazy=function(e){return Ze(e)===sr};re.isMemo=function(e){return Ze(e)===ir};re.isPortal=function(e){return Ze(e)===ro};re.isProfiler=function(e){return Ze(e)===Qn};re.isStrictMode=function(e){return Ze(e)===Jn};re.isSuspense=function(e){return Ze(e)===rr};re.isSuspenseList=function(e){return Ze(e)===or};re.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===Xn||e===Qn||e===Jn||e===rr||e===or||typeof e=="object"&&e!==null&&(e.$$typeof===sr||e.$$typeof===ir||e.$$typeof===tr||e.$$typeof===er||e.$$typeof===nr||e.$$typeof===$d||e.getModuleId!==void 0)};re.typeOf=Ze;Is.exports=re;var Go=Is.exports;const Fd=/^\s*function(?:\s|\s*\/\*.*\*\/\s*)+([^(\s/]*)\s*/;function As(e){const t=`${e}`.match(Fd);return t&&t[1]||""}function Vs(e,t=""){return e.displayName||e.name||As(e)||t}function Zo(e,t,n){const r=Vs(t);return e.displayName||(r!==""?`${n}(${r})`:n)}function kd(e){if(e!=null){if(typeof e=="string")return e;if(typeof e=="function")return Vs(e,"Component");if(typeof e=="object")switch(e.$$typeof){case Go.ForwardRef:return Zo(e,e.render,"ForwardRef");case Go.Memo:return Zo(e,e.type,"memo");default:return}}}const jd=Object.freeze(Object.defineProperty({__proto__:null,default:kd,getFunctionName:As},Symbol.toStringTag,{value:"Module"}));function Ir(e,t){const n=I({},t);return Object.keys(e).forEach(r=>{if(r.toString().match(/^(components|slots)$/))n[r]=I({},e[r],n[r]);else if(r.toString().match(/^(componentsProps|slotProps)$/)){const o=e[r]||{},i=t[r];n[r]={},!i||!Object.keys(i)?n[r]=o:!o||!Object.keys(o)?n[r]=i:(n[r]=I({},i),Object.keys(o).forEach(s=>{n[r][s]=Ir(o[s],i[s])}))}else n[r]===void 0&&(n[r]=e[r])}),n}const Wt=typeof window<"u"?u.useLayoutEffect:u.useEffect;function Id(e,t=Number.MIN_SAFE_INTEGER,n=Number.MAX_SAFE_INTEGER){return Math.max(t,Math.min(e,n))}const Ad=Object.freeze(Object.defineProperty({__proto__:null,default:Id},Symbol.toStringTag,{value:"Module"}));function Vd(e,t=166){let n;function r(...o){const i=()=>{e.apply(this,o)};clearTimeout(n),n=setTimeout(i,t)}return r.clear=()=>{clearTimeout(n)},r}function Td(e){return e&&e.ownerDocument||document}function Wo(e){return Td(e).defaultView||window}function Dd(e,t){typeof e=="function"?e(t):e&&(e.current=t)}let qo=0;function Od(e){const[t,n]=u.useState(e),r=e||t;return u.useEffect(()=>{t==null&&(qo+=1,n(`mui-${qo}`))},[t]),r}const Uo=Ia.useId;function Ld(e){return Uo!==void 0?Uo():Od(e)}function Hd(e){const t=u.useRef(e);return Wt(()=>{t.current=e}),u.useRef((...n)=>(0,t.current)(...n)).current}function Ts(...e){return u.useMemo(()=>e.every(t=>t==null)?null:t=>{e.forEach(n=>{Dd(n,t)})},e)}function oo(e,t,n=void 0){const r={};return Object.keys(e).forEach(o=>{r[o]=e[o].reduce((i,s)=>{if(s){const a=t(s);a!==""&&i.push(a),n&&n[s]&&i.push(n[s])}return i},[]).join(" ")}),r}function Ko(e){return typeof e=="string"}const Ds=u.createContext(null);function Os(){return u.useContext(Ds)}const zd=typeof Symbol=="function"&&Symbol.for,Nd=zd?Symbol.for("mui.nested"):"__THEME_NESTED__";function Bd(e,t){return typeof t=="function"?t(e):I({},e,t)}function Gd(e){const{children:t,theme:n}=e,r=Os(),o=u.useMemo(()=>{const i=r===null?n:Bd(r,n);return i!=null&&(i[Nd]=r!==null),i},[n,r]);return d.jsx(Ds.Provider,{value:o,children:t})}const Zd=["value"],Wd=u.createContext();function qd(e){let{value:t}=e,n=$e(e,Zd);return d.jsx(Wd.Provider,I({value:t??!0},n))}const Ls=u.createContext(void 0);function Ud({value:e,children:t}){return d.jsx(Ls.Provider,{value:e,children:t})}function Kd(e){const{theme:t,name:n,props:r}=e;if(!t||!t.components||!t.components[n])return r;const o=t.components[n];return o.defaultProps?Ir(o.defaultProps,r):!o.styleOverrides&&!o.variants?Ir(o,r):r}function Yd({props:e,name:t}){const n=u.useContext(Ls);return Kd({props:e,name:t,theme:{components:n}})}function Xd(e){const t=to(),n=Ld()||"",{modularCssLayers:r}=e;let o="mui.global, mui.components, mui.theme, mui.custom, mui.sx";return!r||t!==null?o="":typeof r=="string"?o=r.replace(/mui(?!\.)/g,o):o=`@layer ${o};`,Wt(()=>{const i=document.querySelector("head");if(!i)return;const s=i.firstChild;if(o){var a;if(s&&(a=s.hasAttribute)!=null&&a.call(s,"data-mui-layer-order")&&s.getAttribute("data-mui-layer-order")===n)return;const c=document.createElement("style");c.setAttribute("data-mui-layer-order",n),c.textContent=o,i.prepend(c)}else{var l;(l=i.querySelector(`style[data-mui-layer-order="${n}"]`))==null||l.remove()}},[o,n]),o?d.jsx(Fs,{styles:o}):null}const Yo={};function Xo(e,t,n,r=!1){return u.useMemo(()=>{const o=e&&t[e]||t;if(typeof n=="function"){const i=n(o),s=e?I({},t,{[e]:i}):i;return r?()=>s:s}return e?I({},t,{[e]:n}):I({},t,n)},[e,t,n,r])}function Jd(e){const{children:t,theme:n,themeId:r}=e,o=to(Yo),i=Os()||Yo,s=Xo(r,o,n),a=Xo(r,i,n,!0),l=s.direction==="rtl",c=Xd(s);return d.jsx(Gd,{theme:a,children:d.jsx(Zr.Provider,{value:s,children:d.jsx(qd,{value:l,children:d.jsxs(Ud,{value:s==null?void 0:s.components,children:[c,t]})})})})}function Qd(e,t){return I({toolbar:{minHeight:56,[e.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[e.up("sm")]:{minHeight:64}}},t)}var ge={};const e0=xt(bc),t0=xt(Ad);var Hs=$i;Object.defineProperty(ge,"__esModule",{value:!0});var Jo=ge.alpha=Gs;ge.blend=m0;ge.colorChannel=void 0;var n0=ge.darken=so;ge.decomposeColor=Be;ge.emphasize=Zs;var r0=ge.getContrastRatio=u0;ge.getLuminance=Tn;ge.hexToRgb=zs;ge.hslToRgb=Bs;var o0=ge.lighten=ao;ge.private_safeAlpha=c0;ge.private_safeColorChannel=void 0;ge.private_safeDarken=d0;ge.private_safeEmphasize=g0;ge.private_safeLighten=f0;ge.recomposeColor=Yt;ge.rgbToHex=l0;var Qo=Hs(e0),i0=Hs(t0);function io(e,t=0,n=1){return(0,i0.default)(e,t,n)}function zs(e){e=e.slice(1);const t=new RegExp(`.{1,${e.length>=6?2:1}}`,"g");let n=e.match(t);return n&&n[0].length===1&&(n=n.map(r=>r+r)),n?`rgb${n.length===4?"a":""}(${n.map((r,o)=>o<3?parseInt(r,16):Math.round(parseInt(r,16)/255*1e3)/1e3).join(", ")})`:""}function s0(e){const t=e.toString(16);return t.length===1?`0${t}`:t}function Be(e){if(e.type)return e;if(e.charAt(0)==="#")return Be(zs(e));const t=e.indexOf("("),n=e.substring(0,t);if(["rgb","rgba","hsl","hsla","color"].indexOf(n)===-1)throw new Error((0,Qo.default)(9,e));let r=e.substring(t+1,e.length-1),o;if(n==="color"){if(r=r.split(" "),o=r.shift(),r.length===4&&r[3].charAt(0)==="/"&&(r[3]=r[3].slice(1)),["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].indexOf(o)===-1)throw new Error((0,Qo.default)(10,o))}else r=r.split(",");return r=r.map(i=>parseFloat(i)),{type:n,values:r,colorSpace:o}}const Ns=e=>{const t=Be(e);return t.values.slice(0,3).map((n,r)=>t.type.indexOf("hsl")!==-1&&r!==0?`${n}%`:n).join(" ")};ge.colorChannel=Ns;const a0=(e,t)=>{try{return Ns(e)}catch{return e}};ge.private_safeColorChannel=a0;function Yt(e){const{type:t,colorSpace:n}=e;let{values:r}=e;return t.indexOf("rgb")!==-1?r=r.map((o,i)=>i<3?parseInt(o,10):o):t.indexOf("hsl")!==-1&&(r[1]=`${r[1]}%`,r[2]=`${r[2]}%`),t.indexOf("color")!==-1?r=`${n} ${r.join(" ")}`:r=`${r.join(", ")}`,`${t}(${r})`}function l0(e){if(e.indexOf("#")===0)return e;const{values:t}=Be(e);return`#${t.map((n,r)=>s0(r===3?Math.round(255*n):n)).join("")}`}function Bs(e){e=Be(e);const{values:t}=e,n=t[0],r=t[1]/100,o=t[2]/100,i=r*Math.min(o,1-o),s=(c,f=(c+n/30)%12)=>o-i*Math.max(Math.min(f-3,9-f,1),-1);let a="rgb";const l=[Math.round(s(0)*255),Math.round(s(8)*255),Math.round(s(4)*255)];return e.type==="hsla"&&(a+="a",l.push(t[3])),Yt({type:a,values:l})}function Tn(e){e=Be(e);let t=e.type==="hsl"||e.type==="hsla"?Be(Bs(e)).values:e.values;return t=t.map(n=>(e.type!=="color"&&(n/=255),n<=.03928?n/12.92:((n+.055)/1.055)**2.4)),Number((.2126*t[0]+.7152*t[1]+.0722*t[2]).toFixed(3))}function u0(e,t){const n=Tn(e),r=Tn(t);return(Math.max(n,r)+.05)/(Math.min(n,r)+.05)}function Gs(e,t){return e=Be(e),t=io(t),(e.type==="rgb"||e.type==="hsl")&&(e.type+="a"),e.type==="color"?e.values[3]=`/${t}`:e.values[3]=t,Yt(e)}function c0(e,t,n){try{return Gs(e,t)}catch{return e}}function so(e,t){if(e=Be(e),t=io(t),e.type.indexOf("hsl")!==-1)e.values[2]*=1-t;else if(e.type.indexOf("rgb")!==-1||e.type.indexOf("color")!==-1)for(let n=0;n<3;n+=1)e.values[n]*=1-t;return Yt(e)}function d0(e,t,n){try{return so(e,t)}catch{return e}}function ao(e,t){if(e=Be(e),t=io(t),e.type.indexOf("hsl")!==-1)e.values[2]+=(100-e.values[2])*t;else if(e.type.indexOf("rgb")!==-1)for(let n=0;n<3;n+=1)e.values[n]+=(255-e.values[n])*t;else if(e.type.indexOf("color")!==-1)for(let n=0;n<3;n+=1)e.values[n]+=(1-e.values[n])*t;return Yt(e)}function f0(e,t,n){try{return ao(e,t)}catch{return e}}function Zs(e,t=.15){return Tn(e)>.5?so(e,t):ao(e,t)}function g0(e,t,n){try{return Zs(e,t)}catch{return e}}function m0(e,t,n,r=1){const o=(l,c)=>Math.round((l**(1/r)*(1-n)+c**(1/r)*n)**r),i=Be(e),s=Be(t),a=[o(i.values[0],s.values[0]),o(i.values[1],s.values[1]),o(i.values[2],s.values[2])];return Yt({type:"rgb",values:a})}const p0=["mode","contrastThreshold","tonalOffset"],ei={text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:cn.white,default:cn.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}},yr={text:{primary:cn.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:cn.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}};function ti(e,t,n,r){const o=r.light||r,i=r.dark||r*1.5;e[t]||(e.hasOwnProperty(n)?e[t]=e[n]:t==="light"?e.light=o0(e.main,o):t==="dark"&&(e.dark=n0(e.main,i)))}function h0(e="light"){return e==="dark"?{main:Dt[200],light:Dt[50],dark:Dt[400]}:{main:Dt[700],light:Dt[400],dark:Dt[800]}}function v0(e="light"){return e==="dark"?{main:Tt[200],light:Tt[50],dark:Tt[400]}:{main:Tt[500],light:Tt[300],dark:Tt[700]}}function y0(e="light"){return e==="dark"?{main:Vt[500],light:Vt[300],dark:Vt[700]}:{main:Vt[700],light:Vt[400],dark:Vt[800]}}function S0(e="light"){return e==="dark"?{main:Ot[400],light:Ot[300],dark:Ot[700]}:{main:Ot[700],light:Ot[500],dark:Ot[900]}}function b0(e="light"){return e==="dark"?{main:Lt[400],light:Lt[300],dark:Lt[700]}:{main:Lt[800],light:Lt[500],dark:Lt[900]}}function x0(e="light"){return e==="dark"?{main:Qt[400],light:Qt[300],dark:Qt[700]}:{main:"#ed6c02",light:Qt[500],dark:Qt[900]}}function C0(e){const{mode:t="light",contrastThreshold:n=3,tonalOffset:r=.2}=e,o=$e(e,p0),i=e.primary||h0(t),s=e.secondary||v0(t),a=e.error||y0(t),l=e.info||S0(t),c=e.success||b0(t),f=e.warning||x0(t);function p(v){return r0(v,yr.text.primary)>=n?yr.text.primary:ei.text.primary}const g=({color:v,name:y,mainShade:S=500,lightShade:x=300,darkShade:C=700})=>{if(v=I({},v),!v.main&&v[S]&&(v.main=v[S]),!v.hasOwnProperty("main"))throw new Error(Zt(11,y?` (${y})`:"",S));if(typeof v.main!="string")throw new Error(Zt(12,y?` (${y})`:"",JSON.stringify(v.main)));return ti(v,"light",x,r),ti(v,"dark",C,r),v.contrastText||(v.contrastText=p(v.main)),v},m={dark:yr,light:ei};return Xe(I({common:I({},cn),mode:t,primary:g({color:i,name:"primary"}),secondary:g({color:s,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:g({color:a,name:"error"}),warning:g({color:f,name:"warning"}),info:g({color:l,name:"info"}),success:g({color:c,name:"success"}),grey:Sc,contrastThreshold:n,getContrastText:p,augmentColor:g,tonalOffset:r},m[t]),o)}const w0=["fontFamily","fontSize","fontWeightLight","fontWeightRegular","fontWeightMedium","fontWeightBold","htmlFontSize","allVariants","pxToRem"];function _0(e){return Math.round(e*1e5)/1e5}const ni={textTransform:"uppercase"},ri='"Roboto", "Helvetica", "Arial", sans-serif';function R0(e,t){const n=typeof t=="function"?t(e):t,{fontFamily:r=ri,fontSize:o=14,fontWeightLight:i=300,fontWeightRegular:s=400,fontWeightMedium:a=500,fontWeightBold:l=700,htmlFontSize:c=16,allVariants:f,pxToRem:p}=n,g=$e(n,w0),m=o/14,h=p||(S=>`${S/c*m}rem`),v=(S,x,C,_,F)=>I({fontFamily:r,fontWeight:S,fontSize:h(x),lineHeight:C},r===ri?{letterSpacing:`${_0(_/x)}em`}:{},F,f),y={h1:v(i,96,1.167,-1.5),h2:v(i,60,1.2,-.5),h3:v(s,48,1.167,0),h4:v(s,34,1.235,.25),h5:v(s,24,1.334,0),h6:v(a,20,1.6,.15),subtitle1:v(s,16,1.75,.15),subtitle2:v(a,14,1.57,.1),body1:v(s,16,1.5,.15),body2:v(s,14,1.43,.15),button:v(a,14,1.75,.4,ni),caption:v(s,12,1.66,.4),overline:v(s,12,2.66,1,ni),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return Xe(I({htmlFontSize:c,pxToRem:h,fontFamily:r,fontSize:o,fontWeightLight:i,fontWeightRegular:s,fontWeightMedium:a,fontWeightBold:l},y),g,{clone:!1})}const E0=.2,M0=.14,P0=.12;function se(...e){return[`${e[0]}px ${e[1]}px ${e[2]}px ${e[3]}px rgba(0,0,0,${E0})`,`${e[4]}px ${e[5]}px ${e[6]}px ${e[7]}px rgba(0,0,0,${M0})`,`${e[8]}px ${e[9]}px ${e[10]}px ${e[11]}px rgba(0,0,0,${P0})`].join(",")}const $0=["none",se(0,2,1,-1,0,1,1,0,0,1,3,0),se(0,3,1,-2,0,2,2,0,0,1,5,0),se(0,3,3,-2,0,3,4,0,0,1,8,0),se(0,2,4,-1,0,4,5,0,0,1,10,0),se(0,3,5,-1,0,5,8,0,0,1,14,0),se(0,3,5,-1,0,6,10,0,0,1,18,0),se(0,4,5,-2,0,7,10,1,0,2,16,1),se(0,5,5,-3,0,8,10,1,0,3,14,2),se(0,5,6,-3,0,9,12,1,0,3,16,2),se(0,6,6,-3,0,10,14,1,0,4,18,3),se(0,6,7,-4,0,11,15,1,0,4,20,3),se(0,7,8,-4,0,12,17,2,0,5,22,4),se(0,7,8,-4,0,13,19,2,0,5,24,4),se(0,7,9,-4,0,14,21,2,0,5,26,4),se(0,8,9,-5,0,15,22,2,0,6,28,5),se(0,8,10,-5,0,16,24,2,0,6,30,5),se(0,8,11,-5,0,17,26,2,0,6,32,5),se(0,9,11,-5,0,18,28,2,0,7,34,6),se(0,9,12,-6,0,19,29,2,0,7,36,6),se(0,10,13,-6,0,20,31,3,0,8,38,7),se(0,10,13,-6,0,21,33,3,0,8,40,7),se(0,10,14,-6,0,22,35,3,0,8,42,7),se(0,11,14,-7,0,23,36,3,0,9,44,8),se(0,11,15,-7,0,24,38,3,0,9,46,8)],F0=["duration","easing","delay"],k0={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},j0={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function oi(e){return`${Math.round(e)}ms`}function I0(e){if(!e)return 0;const t=e/36;return Math.round((4+15*t**.25+t/5)*10)}function A0(e){const t=I({},k0,e.easing),n=I({},j0,e.duration);return I({getAutoHeightDuration:I0,create:(o=["all"],i={})=>{const{duration:s=n.standard,easing:a=t.easeInOut,delay:l=0}=i;return $e(i,F0),(Array.isArray(o)?o:[o]).map(c=>`${c} ${typeof s=="string"?s:oi(s)} ${a} ${typeof l=="string"?l:oi(l)}`).join(",")}},e,{easing:t,duration:n})}const V0={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500},T0=["breakpoints","mixins","spacing","palette","transitions","typography","shape"];function lo(e={},...t){const{mixins:n={},palette:r={},transitions:o={},typography:i={}}=e,s=$e(e,T0);if(e.vars&&e.generateCssVars===void 0)throw new Error(Zt(18));const a=C0(r),l=eo(e);let c=Xe(l,{mixins:Qd(l.breakpoints,n),palette:a,shadows:$0.slice(),typography:R0(a,i),transitions:A0(o),zIndex:I({},V0)});return c=Xe(c,s),c=t.reduce((f,p)=>Xe(f,p),c),c.unstable_sxConfig=I({},vn,s==null?void 0:s.unstable_sxConfig),c.unstable_sx=function(p){return yn({sx:p,theme:this})},c}const Ws=lo();var Sn={};const D0=xt(Rc),O0=xt(Ec),L0=xt(kc),H0=xt(jd),z0=xt(yd),N0=xt(wd);var Xt=$i;Object.defineProperty(Sn,"__esModule",{value:!0});var B0=Sn.default=nf;Sn.shouldForwardProp=In;Sn.systemDefaultTheme=void 0;var Le=Xt(Va),Ar=Xt(Aa),Dn=Y0(D0),G0=O0;Xt(L0);Xt(H0);var Z0=Xt(z0),W0=Xt(N0);const q0=["ownerState"],U0=["variants"],K0=["name","slot","skipVariantsResolver","skipSx","overridesResolver"];function qs(e){if(typeof WeakMap!="function")return null;var t=new WeakMap,n=new WeakMap;return(qs=function(r){return r?n:t})(e)}function Y0(e,t){if(e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var n=qs(t);if(n&&n.has(e))return n.get(e);var r={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(i!=="default"&&Object.prototype.hasOwnProperty.call(e,i)){var s=o?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(r,i,s):r[i]=e[i]}return r.default=e,n&&n.set(e,r),r}function X0(e){return Object.keys(e).length===0}function J0(e){return typeof e=="string"&&e.charCodeAt(0)>96}function In(e){return e!=="ownerState"&&e!=="theme"&&e!=="sx"&&e!=="as"}function ii(e,t){return t&&e&&typeof e=="object"&&e.styles&&!e.styles.startsWith("@layer")&&(e.styles=`@layer ${t}{${String(e.styles)}}`),e}const Q0=Sn.systemDefaultTheme=(0,Z0.default)(),ef=e=>e&&e.charAt(0).toLowerCase()+e.slice(1);function _n({defaultTheme:e,theme:t,themeId:n}){return X0(t)?e:t[n]||t}function tf(e){return e?(t,n)=>n[e]:null}function An(e,t,n){let{ownerState:r}=t,o=(0,Ar.default)(t,q0);const i=typeof e=="function"?e((0,Le.default)({ownerState:r},o)):e;if(Array.isArray(i))return i.flatMap(s=>An(s,(0,Le.default)({ownerState:r},o),n));if(i&&typeof i=="object"&&Array.isArray(i.variants)){const{variants:s=[]}=i;let l=(0,Ar.default)(i,U0);return s.forEach(c=>{let f=!0;if(typeof c.props=="function"?f=c.props((0,Le.default)({ownerState:r},o,r)):Object.keys(c.props).forEach(p=>{(r==null?void 0:r[p])!==c.props[p]&&o[p]!==c.props[p]&&(f=!1)}),f){Array.isArray(l)||(l=[l]);const p=typeof c.style=="function"?c.style((0,Le.default)({ownerState:r},o,r)):c.style;l.push(n?ii((0,Dn.internal_serializeStyles)(p),n):p)}}),l}return n?ii((0,Dn.internal_serializeStyles)(i),n):i}function nf(e={}){const{themeId:t,defaultTheme:n=Q0,rootShouldForwardProp:r=In,slotShouldForwardProp:o=In}=e,i=s=>(0,W0.default)((0,Le.default)({},s,{theme:_n((0,Le.default)({},s,{defaultTheme:n,themeId:t}))}));return i.__mui_systemSx=!0,(s,a={})=>{(0,Dn.internal_processStyles)(s,E=>E.filter($=>!($!=null&&$.__mui_systemSx)));const{name:l,slot:c,skipVariantsResolver:f,skipSx:p,overridesResolver:g=tf(ef(c))}=a,m=(0,Ar.default)(a,K0),h=l&&l.startsWith("Mui")||c?"components":"custom",v=f!==void 0?f:c&&c!=="Root"&&c!=="root"||!1,y=p||!1;let S,x=In;c==="Root"||c==="root"?x=r:c?x=o:J0(s)&&(x=void 0);const C=(0,Dn.default)(s,(0,Le.default)({shouldForwardProp:x,label:S},m)),_=E=>typeof E=="function"&&E.__emotion_real!==E||(0,G0.isPlainObject)(E)?$=>{const V=_n({theme:$.theme,defaultTheme:n,themeId:t});return An(E,(0,Le.default)({},$,{theme:V}),V.modularCssLayers?h:void 0)}:E,F=(E,...$)=>{let V=_(E);const z=$?$.map(_):[];l&&g&&z.push(k=>{const H=_n((0,Le.default)({},k,{defaultTheme:n,themeId:t}));if(!H.components||!H.components[l]||!H.components[l].styleOverrides)return null;const Z=H.components[l].styleOverrides,G={};return Object.entries(Z).forEach(([ee,Y])=>{G[ee]=An(Y,(0,Le.default)({},k,{theme:H}),H.modularCssLayers?"theme":void 0)}),g(k,G)}),l&&!v&&z.push(k=>{var H;const Z=_n((0,Le.default)({},k,{defaultTheme:n,themeId:t})),G=Z==null||(H=Z.components)==null||(H=H[l])==null?void 0:H.variants;return An({variants:G},(0,Le.default)({},k,{theme:Z}),Z.modularCssLayers?"theme":void 0)}),y||z.push(i);const D=z.length-$.length;if(Array.isArray(E)&&D>0){const k=new Array(D).fill("");V=[...E,...k],V.raw=[...E.raw,...k]}const P=C(V,...z);return s.muiName&&(P.muiName=s.muiName),P};return C.withConfig&&(F.withConfig=C.withConfig),F}}function rf(e){return e!=="ownerState"&&e!=="theme"&&e!=="sx"&&e!=="as"}const of=e=>rf(e)&&e!=="classes",ar=B0({themeId:dn,defaultTheme:Ws,rootShouldForwardProp:of}),sf=["theme"];function af(e){let{theme:t}=e,n=$e(e,sf);const r=t[dn];let o=r||t;return typeof t!="function"&&(r&&!r.vars?o=I({},r,{vars:null}):t&&!t.vars&&(o=I({},t,{vars:null}))),d.jsx(Jd,I({},n,{themeId:r?dn:void 0,theme:o}))}const si=e=>{let t;return e<1?t=5.11916*e**2:t=4.5*Math.log(e+1)+2,(t/100).toFixed(2)};function uo(e){return Yd(e)}function lf(e){return Kn("MuiSvgIcon",e)}Yn("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const uf=["children","className","color","component","fontSize","htmlColor","inheritViewBox","titleAccess","viewBox"],cf=e=>{const{color:t,fontSize:n,classes:r}=e,o={root:["root",t!=="inherit"&&`color${Qe(t)}`,`fontSize${Qe(n)}`]};return oo(o,lf,r)},df=ar("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.color!=="inherit"&&t[`color${Qe(n.color)}`],t[`fontSize${Qe(n.fontSize)}`]]}})(({theme:e,ownerState:t})=>{var n,r,o,i,s,a,l,c,f,p,g,m,h;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",fill:t.hasSvgAsChild?void 0:"currentColor",flexShrink:0,transition:(n=e.transitions)==null||(r=n.create)==null?void 0:r.call(n,"fill",{duration:(o=e.transitions)==null||(o=o.duration)==null?void 0:o.shorter}),fontSize:{inherit:"inherit",small:((i=e.typography)==null||(s=i.pxToRem)==null?void 0:s.call(i,20))||"1.25rem",medium:((a=e.typography)==null||(l=a.pxToRem)==null?void 0:l.call(a,24))||"1.5rem",large:((c=e.typography)==null||(f=c.pxToRem)==null?void 0:f.call(c,35))||"2.1875rem"}[t.fontSize],color:(p=(g=(e.vars||e).palette)==null||(g=g[t.color])==null?void 0:g.main)!=null?p:{action:(m=(e.vars||e).palette)==null||(m=m.action)==null?void 0:m.active,disabled:(h=(e.vars||e).palette)==null||(h=h.action)==null?void 0:h.disabled,inherit:void 0}[t.color]}}),Us=u.forwardRef(function(t,n){const r=uo({props:t,name:"MuiSvgIcon"}),{children:o,className:i,color:s="inherit",component:a="svg",fontSize:l="medium",htmlColor:c,inheritViewBox:f=!1,titleAccess:p,viewBox:g="0 0 24 24"}=r,m=$e(r,uf),h=u.isValidElement(o)&&o.type==="svg",v=I({},r,{color:s,component:a,fontSize:l,instanceFontSize:t.fontSize,inheritViewBox:f,viewBox:g,hasSvgAsChild:h}),y={};f||(y.viewBox=g);const S=cf(v);return d.jsxs(df,I({as:a,className:ln(S.root,i),focusable:"false",color:c,"aria-hidden":p?void 0:!0,role:p?"img":void 0,ref:n},y,m,h&&o.props,{ownerState:v,children:[h?o.props.children:o,p?d.jsx("title",{children:p}):null]}))});Us.muiName="SvgIcon";function ff(e){return Kn("MuiPaper",e)}Yn("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);const gf=["className","component","elevation","square","variant"],mf=e=>{const{square:t,elevation:n,variant:r,classes:o}=e,i={root:["root",r,!t&&"rounded",r==="elevation"&&`elevation${n}`]};return oo(i,ff,o)},pf=ar("div",{name:"MuiPaper",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[n.variant],!n.square&&t.rounded,n.variant==="elevation"&&t[`elevation${n.elevation}`]]}})(({theme:e,ownerState:t})=>{var n;return I({backgroundColor:(e.vars||e).palette.background.paper,color:(e.vars||e).palette.text.primary,transition:e.transitions.create("box-shadow")},!t.square&&{borderRadius:e.shape.borderRadius},t.variant==="outlined"&&{border:`1px solid ${(e.vars||e).palette.divider}`},t.variant==="elevation"&&I({boxShadow:(e.vars||e).shadows[t.elevation]},!e.vars&&e.palette.mode==="dark"&&{backgroundImage:`linear-gradient(${Jo("#fff",si(t.elevation))}, ${Jo("#fff",si(t.elevation))})`},e.vars&&{backgroundImage:(n=e.vars.overlays)==null?void 0:n[t.elevation]}))}),hf=u.forwardRef(function(t,n){const r=uo({props:t,name:"MuiPaper"}),{className:o,component:i="div",elevation:s=1,square:a=!1,variant:l="elevation"}=r,c=$e(r,gf),f=I({},r,{component:i,elevation:s,square:a,variant:l}),p=mf(f);return d.jsx(pf,I({as:i,ownerState:f,className:ln(p.root,o),ref:n},c))}),vf=["onChange","maxRows","minRows","style","value"];function Rn(e){return parseInt(e,10)||0}const yf={shadow:{visibility:"hidden",position:"absolute",overflow:"hidden",height:0,top:0,left:0,transform:"translateZ(0)"}};function Sf(e){for(const t in e)return!1;return!0}function ai(e){return Sf(e)||e.outerHeightStyle===0&&!e.overflowing}const bf=u.forwardRef(function(t,n){const{onChange:r,maxRows:o,minRows:i=1,style:s,value:a}=t,l=$e(t,vf),{current:c}=u.useRef(a!=null),f=u.useRef(null),p=Ts(n,f),g=u.useRef(null),m=u.useRef(null),h=u.useCallback(()=>{const C=f.current,_=m.current;if(!C||!_)return;const E=Wo(C).getComputedStyle(C);if(E.width==="0px")return{outerHeightStyle:0,overflowing:!1};_.style.width=E.width,_.value=C.value||t.placeholder||"x",_.value.slice(-1)===`
`&&(_.value+=" ");const $=E.boxSizing,V=Rn(E.paddingBottom)+Rn(E.paddingTop),z=Rn(E.borderBottomWidth)+Rn(E.borderTopWidth),D=_.scrollHeight;_.value="x";const P=_.scrollHeight;let k=D;i&&(k=Math.max(Number(i)*P,k)),o&&(k=Math.min(Number(o)*P,k)),k=Math.max(k,P);const H=k+($==="border-box"?V+z:0),Z=Math.abs(k-D)<=1;return{outerHeightStyle:H,overflowing:Z}},[o,i,t.placeholder]),v=Hd(()=>{const C=f.current,_=h();if(!C||!_||ai(_))return!1;const F=_.outerHeightStyle;return g.current!=null&&g.current!==F}),y=u.useCallback(()=>{const C=f.current,_=h();if(!C||!_||ai(_))return;const F=_.outerHeightStyle;g.current!==F&&(g.current=F,C.style.height=`${F}px`),C.style.overflow=_.overflowing?"hidden":""},[h]),S=u.useRef(-1);Wt(()=>{const C=Vd(y),_=f==null?void 0:f.current;if(!_)return;const F=Wo(_);F.addEventListener("resize",C);let E;return typeof ResizeObserver<"u"&&(E=new ResizeObserver(()=>{v()&&(E.unobserve(_),cancelAnimationFrame(S.current),y(),S.current=requestAnimationFrame(()=>{E.observe(_)}))}),E.observe(_)),()=>{C.clear(),cancelAnimationFrame(S.current),F.removeEventListener("resize",C),E&&E.disconnect()}},[h,y,v]),Wt(()=>{y()});const x=C=>{c||y(),r&&r(C)};return d.jsxs(u.Fragment,{children:[d.jsx("textarea",I({value:a,onChange:x,ref:p,rows:i,style:s},l)),d.jsx("textarea",{"aria-hidden":!0,className:t.className,readOnly:!0,ref:m,tabIndex:-1,style:I({},yf.shadow,s,{paddingTop:0,paddingBottom:0})})]})});function xf({props:e,states:t,muiFormControl:n}){return t.reduce((r,o)=>(r[o]=e[o],n&&typeof e[o]>"u"&&(r[o]=n[o]),r),{})}const Ks=u.createContext(void 0);function Cf(){return u.useContext(Ks)}function wf(e){return d.jsx(Fs,I({},e,{defaultTheme:Ws,themeId:dn}))}function li(e){return e!=null&&!(Array.isArray(e)&&e.length===0)}function _f(e,t=!1){return e&&(li(e.value)&&e.value!==""||t&&li(e.defaultValue)&&e.defaultValue!=="")}function Rf(e){return Kn("MuiInputBase",e)}const Vr=Yn("MuiInputBase",["root","formControl","focused","disabled","adornedStart","adornedEnd","error","sizeSmall","multiline","colorSecondary","fullWidth","hiddenLabel","readOnly","input","inputSizeSmall","inputMultiline","inputTypeSearch","inputAdornedStart","inputAdornedEnd","inputHiddenLabel"]),Ef=["aria-describedby","autoComplete","autoFocus","className","color","components","componentsProps","defaultValue","disabled","disableInjectingGlobalStyles","endAdornment","error","fullWidth","id","inputComponent","inputProps","inputRef","margin","maxRows","minRows","multiline","name","onBlur","onChange","onClick","onFocus","onKeyDown","onKeyUp","placeholder","readOnly","renderSuffix","rows","size","slotProps","slots","startAdornment","type","value"],Mf=(e,t)=>{const{ownerState:n}=e;return[t.root,n.formControl&&t.formControl,n.startAdornment&&t.adornedStart,n.endAdornment&&t.adornedEnd,n.error&&t.error,n.size==="small"&&t.sizeSmall,n.multiline&&t.multiline,n.color&&t[`color${Qe(n.color)}`],n.fullWidth&&t.fullWidth,n.hiddenLabel&&t.hiddenLabel]},Pf=(e,t)=>{const{ownerState:n}=e;return[t.input,n.size==="small"&&t.inputSizeSmall,n.multiline&&t.inputMultiline,n.type==="search"&&t.inputTypeSearch,n.startAdornment&&t.inputAdornedStart,n.endAdornment&&t.inputAdornedEnd,n.hiddenLabel&&t.inputHiddenLabel]},$f=e=>{const{classes:t,color:n,disabled:r,error:o,endAdornment:i,focused:s,formControl:a,fullWidth:l,hiddenLabel:c,multiline:f,readOnly:p,size:g,startAdornment:m,type:h}=e,v={root:["root",`color${Qe(n)}`,r&&"disabled",o&&"error",l&&"fullWidth",s&&"focused",a&&"formControl",g&&g!=="medium"&&`size${Qe(g)}`,f&&"multiline",m&&"adornedStart",i&&"adornedEnd",c&&"hiddenLabel",p&&"readOnly"],input:["input",r&&"disabled",h==="search"&&"inputTypeSearch",f&&"inputMultiline",g==="small"&&"inputSizeSmall",c&&"inputHiddenLabel",m&&"inputAdornedStart",i&&"inputAdornedEnd",p&&"readOnly"]};return oo(v,Rf,t)},Ff=ar("div",{name:"MuiInputBase",slot:"Root",overridesResolver:Mf})(({theme:e,ownerState:t})=>I({},e.typography.body1,{color:(e.vars||e).palette.text.primary,lineHeight:"1.4375em",boxSizing:"border-box",position:"relative",cursor:"text",display:"inline-flex",alignItems:"center",[`&.${Vr.disabled}`]:{color:(e.vars||e).palette.text.disabled,cursor:"default"}},t.multiline&&I({padding:"4px 0 5px"},t.size==="small"&&{paddingTop:1}),t.fullWidth&&{width:"100%"})),kf=ar("input",{name:"MuiInputBase",slot:"Input",overridesResolver:Pf})(({theme:e,ownerState:t})=>{const n=e.palette.mode==="light",r=I({color:"currentColor"},e.vars?{opacity:e.vars.opacity.inputPlaceholder}:{opacity:n?.42:.5},{transition:e.transitions.create("opacity",{duration:e.transitions.duration.shorter})}),o={opacity:"0 !important"},i=e.vars?{opacity:e.vars.opacity.inputPlaceholder}:{opacity:n?.42:.5};return I({font:"inherit",letterSpacing:"inherit",color:"currentColor",padding:"4px 0 5px",border:0,boxSizing:"content-box",background:"none",height:"1.4375em",margin:0,WebkitTapHighlightColor:"transparent",display:"block",minWidth:0,width:"100%",animationName:"mui-auto-fill-cancel",animationDuration:"10ms","&::-webkit-input-placeholder":r,"&::-moz-placeholder":r,"&:-ms-input-placeholder":r,"&::-ms-input-placeholder":r,"&:focus":{outline:0},"&:invalid":{boxShadow:"none"},"&::-webkit-search-decoration":{WebkitAppearance:"none"},[`label[data-shrink=false] + .${Vr.formControl} &`]:{"&::-webkit-input-placeholder":o,"&::-moz-placeholder":o,"&:-ms-input-placeholder":o,"&::-ms-input-placeholder":o,"&:focus::-webkit-input-placeholder":i,"&:focus::-moz-placeholder":i,"&:focus:-ms-input-placeholder":i,"&:focus::-ms-input-placeholder":i},[`&.${Vr.disabled}`]:{opacity:1,WebkitTextFillColor:(e.vars||e).palette.text.disabled},"&:-webkit-autofill":{animationDuration:"5000s",animationName:"mui-auto-fill"}},t.size==="small"&&{paddingTop:1},t.multiline&&{height:"auto",resize:"none",padding:0,paddingTop:0},t.type==="search"&&{MozAppearance:"textfield"})}),jf=d.jsx(wf,{styles:{"@keyframes mui-auto-fill":{from:{display:"block"}},"@keyframes mui-auto-fill-cancel":{from:{display:"block"}}}}),If=u.forwardRef(function(t,n){var r;const o=uo({props:t,name:"MuiInputBase"}),{"aria-describedby":i,autoComplete:s,autoFocus:a,className:l,components:c={},componentsProps:f={},defaultValue:p,disabled:g,disableInjectingGlobalStyles:m,endAdornment:h,fullWidth:v=!1,id:y,inputComponent:S="input",inputProps:x={},inputRef:C,maxRows:_,minRows:F,multiline:E=!1,name:$,onBlur:V,onChange:z,onClick:D,onFocus:P,onKeyDown:k,onKeyUp:H,placeholder:Z,readOnly:G,renderSuffix:ee,rows:Y,slotProps:A={},slots:q={},startAdornment:Ee,type:Me="text",value:Fe}=o,X=$e(o,Ef),U=x.value!=null?x.value:Fe,{current:le}=u.useRef(U!=null),me=u.useRef(),xe=u.useCallback(b=>{},[]),De=Ts(me,C,x.ref,xe),[Se,he]=u.useState(!1),W=Cf(),te=xf({props:o,muiFormControl:W,states:["color","disabled","error","hiddenLabel","size","required","filled"]});te.focused=W?W.focused:Se,u.useEffect(()=>{!W&&g&&Se&&(he(!1),V&&V())},[W,g,Se,V]);const T=W&&W.onFilled,R=W&&W.onEmpty,ie=u.useCallback(b=>{_f(b)?T&&T():R&&R()},[T,R]);Wt(()=>{le&&ie({value:U})},[U,ie,le]);const ve=b=>{if(te.disabled){b.stopPropagation();return}P&&P(b),x.onFocus&&x.onFocus(b),W&&W.onFocus?W.onFocus(b):he(!0)},Oe=b=>{V&&V(b),x.onBlur&&x.onBlur(b),W&&W.onBlur?W.onBlur(b):he(!1)},Ct=(b,...ne)=>{if(!le){const w=b.target||me.current;if(w==null)throw new Error(Zt(1));ie({value:w.value})}x.onChange&&x.onChange(b,...ne),z&&z(b,...ne)};u.useEffect(()=>{ie(me.current)},[]);const wt=b=>{me.current&&b.currentTarget===b.target&&me.current.focus(),D&&D(b)};let et=S,Ce=x;E&&et==="input"&&(Y?Ce=I({type:void 0,minRows:Y,maxRows:Y},Ce):Ce=I({type:void 0,maxRows:_,minRows:F},Ce),et=bf);const tt=b=>{ie(b.animationName==="mui-auto-fill-cancel"?me.current:{value:"x"})};u.useEffect(()=>{W&&W.setAdornedStart(!!Ee)},[W,Ee]);const nt=I({},o,{color:te.color||"primary",disabled:te.disabled,endAdornment:h,error:te.error,focused:te.focused,formControl:W,fullWidth:v,hiddenLabel:te.hiddenLabel,multiline:E,size:te.size,startAdornment:Ee,type:Me}),_t=$f(nt),kt=q.root||c.Root||Ff,Rt=A.root||f.root||{},jt=q.input||c.Input||kf;return Ce=I({},Ce,(r=A.input)!=null?r:f.input),d.jsxs(u.Fragment,{children:[!m&&jf,d.jsxs(kt,I({},Rt,!Ko(kt)&&{ownerState:I({},nt,Rt.ownerState)},{ref:n,onClick:wt},X,{className:ln(_t.root,Rt.className,l,G&&"MuiInputBase-readOnly"),children:[Ee,d.jsx(Ks.Provider,{value:null,children:d.jsx(jt,I({ownerState:nt,"aria-invalid":te.error,"aria-describedby":i,autoComplete:s,autoFocus:a,defaultValue:p,disabled:te.disabled,id:y,onAnimationStart:tt,name:$,placeholder:Z,readOnly:G,required:te.required,rows:Y,value:U,onKeyDown:k,onKeyUp:H,type:Me},Ce,!Ko(jt)&&{as:et,ownerState:I({},nt,Ce.ownerState)},{ref:De,className:ln(_t.input,Ce.className,G&&"MuiInputBase-readOnly"),onBlur:Oe,onChange:Ct,onFocus:ve}))}),h,ee?ee(I({},te,{startAdornment:Ee})):null]}))]})}),Af=Yn("MuiBox",["root"]),Vf=lo(),de=Ed({themeId:dn,defaultTheme:Vf,defaultClassName:Af.root,generateClassName:js.generate});function co(e){const{children:t,defer:n=!1,fallback:r=null}=e,[o,i]=u.useState(!1);return Wt(()=>{n||i(!0)},[n]),u.useEffect(()=>{n&&i(!0)},[n]),d.jsx(u.Fragment,{children:o?t:r})}function fo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function J(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),r.forEach(function(o){fo(e,o,n[o])})}return e}function Tf(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}function bn(e,t){return t=t??{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Tf(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function Tr(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Df(e){if(Array.isArray(e))return Tr(e)}function Of(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Lf(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ys(e,t){if(e){if(typeof e=="string")return Tr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(n);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Tr(e,t)}}function Ne(e){return Df(e)||Of(e)||Ys(e)||Lf()}function Xs(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=Xs(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function Hf(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=Xs(e))&&(r&&(r+=" "),r+=t);return r}function zf(e){if(Array.isArray(e))return e}function Nf(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r=[],o=!0,i=!1,s,a;try{for(n=n.call(e);!(o=(s=n.next()).done)&&(r.push(s.value),!(t&&r.length===t));o=!0);}catch(l){i=!0,a=l}finally{try{!o&&n.return!=null&&n.return()}finally{if(i)throw a}}return r}}function Bf(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Pe(e,t){return zf(e)||Nf(e,t)||Ys(e,t)||Bf()}function On(e){"@swc/helpers - typeof";return e&&typeof Symbol<"u"&&e.constructor===Symbol?"symbol":typeof e}var go={scheme:"Light Theme",author:"mac gainor (https://github.com/mac-s-g)",base00:"rgba(0, 0, 0, 0)",base01:"rgb(245, 245, 245)",base02:"rgb(235, 235, 235)",base03:"#93a1a1",base04:"rgba(0, 0, 0, 0.3)",base05:"#586e75",base06:"#073642",base07:"#002b36",base08:"#d33682",base09:"#cb4b16",base0A:"#ffd500",base0B:"#859900",base0C:"#6c71c4",base0D:"#586e75",base0E:"#2aa198",base0F:"#268bd2"},Js={scheme:"Dark Theme",author:"Chris Kempson (http://chriskempson.com)",base00:"#181818",base01:"#282828",base02:"#383838",base03:"#585858",base04:"#b8b8b8",base05:"#d8d8d8",base06:"#e8e8e8",base07:"#f8f8f8",base08:"#ab4642",base09:"#dc9656",base0A:"#f7ca88",base0B:"#a1b56c",base0C:"#86c1b9",base0D:"#7cafc2",base0E:"#ba8baf",base0F:"#a16946"},Qs=function(){return null};Qs.when=function(){return!1};var Gf=function(e){var t,n,r,o,i,s,a,l,c,f,p,g,m,h,v,y,S;return Ta()(function(x,C){return{enableClipboard:(t=e.enableClipboard)!==null&&t!==void 0?t:!0,highlightUpdates:(n=e.highlightUpdates)!==null&&n!==void 0?n:!1,indentWidth:(r=e.indentWidth)!==null&&r!==void 0?r:3,groupArraysAfterLength:(o=e.groupArraysAfterLength)!==null&&o!==void 0?o:100,collapseStringsAfterLength:e.collapseStringsAfterLength===!1?Number.MAX_VALUE:(i=e.collapseStringsAfterLength)!==null&&i!==void 0?i:50,maxDisplayLength:(s=e.maxDisplayLength)!==null&&s!==void 0?s:30,rootName:(a=e.rootName)!==null&&a!==void 0?a:"root",onChange:(l=e.onChange)!==null&&l!==void 0?l:function(){},onCopy:(c=e.onCopy)!==null&&c!==void 0?c:void 0,onSelect:(f=e.onSelect)!==null&&f!==void 0?f:void 0,keyRenderer:(p=e.keyRenderer)!==null&&p!==void 0?p:Qs,editable:(g=e.editable)!==null&&g!==void 0?g:!1,defaultInspectDepth:(m=e.defaultInspectDepth)!==null&&m!==void 0?m:5,objectSortKeys:(h=e.objectSortKeys)!==null&&h!==void 0?h:!1,quotesOnKeys:(v=e.quotesOnKeys)!==null&&v!==void 0?v:!0,displayDataTypes:(y=e.displayDataTypes)!==null&&y!==void 0?y:!0,inspectCache:{},hoverPath:null,colorspace:go,value:e.value,prevValue:void 0,displayObjectSize:(S=e.displayObjectSize)!==null&&S!==void 0?S:!0,getInspectCache:function(_,F){var E=F!==void 0?_.join(".")+"[".concat(F,"]nt"):_.join(".");return C().inspectCache[E]},setInspectCache:function(_,F,E){var $=E!==void 0?_.join(".")+"[".concat(E,"]nt"):_.join(".");x(function(V){return{inspectCache:bn(J({},V.inspectCache),fo({},$,typeof F=="function"?F(V.inspectCache[$]):F))}})},setHover:function(_,F){x({hoverPath:_?{path:_,nestedIndex:F}:null})}}})},xn=u.createContext(void 0);xn.Provider;var B=function(e,t){var n=u.useContext(xn);return Fi(n,e,t)},mo=function(){return B(function(e){return e.colorspace.base07})};function ui(e,t,n,r,o,i,s){try{var a=e[i](s),l=a.value}catch(c){n(c);return}a.done?t(l):Promise.resolve(l).then(r,o)}function ea(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function s(l){ui(i,r,o,s,a,"next",l)}function a(l){ui(i,r,o,s,a,"throw",l)}s(void 0)})}}function ta(e,t){var n={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},r,o,i,s;return s={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function a(c){return function(f){return l([c,f])}}function l(c){if(r)throw new TypeError("Generator is already executing.");for(;s&&(s=0,c[0]&&(n=0)),n;)try{if(r=1,o&&(i=c[0]&2?o.return:c[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,c[1])).done)return i;switch(o=0,i&&(c=[c[0]&2,i.value]),c[0]){case 0:case 1:i=c;break;case 4:return n.label++,{value:c[1],done:!1};case 5:n.label++,o=c[1],c=[0];continue;case 7:c=n.ops.pop(),n.trys.pop();continue;default:if(i=n.trys,!(i=i.length>0&&i[i.length-1])&&(c[0]===6||c[0]===2)){n=0;continue}if(c[0]===3&&(!i||c[1]>i[0]&&c[1]<i[3])){n.label=c[1];break}if(c[0]===6&&n.label<i[1]){n.label=i[1],i=c;break}if(i&&n.label<i[2]){n.label=i[2],n.ops.push(c);break}i[2]&&n.ops.pop(),n.trys.pop();continue}c=t.call(e,n)}catch(f){c=[6,f],o=0}finally{r=i=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}function Je(e,t){return t!=null&&typeof Symbol<"u"&&t[Symbol.hasInstance]?!!t[Symbol.hasInstance](e):e instanceof t}Object.prototype.constructor.toString();var Zf=function(e,t,n){if(e===null||n===null||typeof e!="object"||typeof n!="object")return!1;if(Object.is(e,n)&&t.length!==0)return"";for(var r=[],o=Ne(t),i=e;i!==n||o.length!==0;){if(typeof i!="object"||i===null)return!1;if(Object.is(i,n))return r.reduce(function(a,l,c){return typeof l=="number"?a+"[".concat(l,"]"):a+"".concat(c===0?"":".").concat(l)},"");var s=o.shift();r.push(s),i=i[s]}return!1};function Cn(e){return e===null?0:Array.isArray(e)?e.length:Je(e,Map)||Je(e,Set)?e.size:Je(e,Date)?1:typeof e=="object"?Object.keys(e).length:typeof e=="string"?e.length:1}function ci(e,t){for(var n=[],r=0;r<e.length;)n.push(e.slice(r,r+t)),r+=t;return n}function Wf(e,t){var n=function(i,s){if((typeof s>"u"?"undefined":On(s))==="bigint")return s.toString();if(Je(s,Map)){if("toJSON"in s&&typeof s.toJSON=="function")return s.toJSON();if(s.size===0)return{};if(r.includes(s))return"[Circular]";r.push(s);var a=Array.from(s.entries());return a.every(function(f){var p=Pe(f,1),g=p[0];return typeof g=="string"||typeof g=="number"})?Object.fromEntries(a):{}}if(Je(s,Set))return"toJSON"in s&&typeof s.toJSON=="function"?s.toJSON():r.includes(s)?"[Circular]":(r.push(s),Array.from(s.values()));if(typeof s=="object"&&s!==null&&Object.keys(s).length){var l=r.length;if(l){for(var c=l-1;c>=0&&r[c][i]!==s;--c)r.pop();if(r.includes(s))return"[Circular]"}r.push(s)}return s},r=[];return JSON.stringify(e,n,t)}function Dr(e){return Or.apply(this,arguments)}function Or(){return Or=ea(function(e){return ta(this,function(t){switch(t.label){case 0:if(!("clipboard"in navigator))return[3,4];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,navigator.clipboard.writeText(e)];case 2:return t.sent(),[3,4];case 3:return t.sent(),[3,4];case 4:return Er(e),[2]}})}),Or.apply(this,arguments)}function qf(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=e.timeout,n=t===void 0?2e3:t,r=Pe(u.useState(!1),2),o=r[0],i=r[1],s=u.useRef(null),a=u.useCallback(function(p){var g=s.current;g&&window.clearTimeout(g),s.current=window.setTimeout(function(){return i(!1)},n),i(p)},[n]),l=B(function(p){return p.onCopy}),c=u.useCallback(function(){var p=ea(function(g,m){var h,v,y;return ta(this,function(S){switch(S.label){case 0:if(typeof l!="function")return[3,5];S.label=1;case 1:return S.trys.push([1,3,,4]),[4,l(g,m,Dr)];case 2:return S.sent(),a(!0),[3,4];case 3:return h=S.sent(),console.error("error when copy ".concat(g.length===0?"src":"src[".concat(g.join(".")),"]"),h),[3,4];case 4:return[3,8];case 5:return S.trys.push([5,7,,8]),v=Wf(typeof m=="function"?m.toString():m,"  "),[4,Dr(v)];case 6:return S.sent(),a(!0),[3,8];case 7:return y=S.sent(),console.error("error when copy ".concat(g.length===0?"src":"src[".concat(g.join(".")),"]"),y),[3,8];case 8:return[2]}})});return function(g,m){return p.apply(this,arguments)}}(),[a,l]),f=u.useCallback(function(){i(!1),s.current&&clearTimeout(s.current)},[]);return{copy:c,reset:f,copied:o}}function po(e,t){var n=B(function(r){return r.value});return u.useMemo(function(){return Zf(n,e,t)},[e,t,n])}function Uf(e,t,n){var r=e.length,o=po(e,t),i=B(function(g){return g.getInspectCache}),s=B(function(g){return g.setInspectCache}),a=B(function(g){return g.defaultInspectDepth});u.useEffect(function(){var g=i(e,n);if(g===void 0)if(n!==void 0)s(e,!1,n);else{var m=o?!1:r<a;s(e,m)}},[a,r,i,o,n,e,s]);var l=Pe(u.useState(function(){var g=i(e,n);return g!==void 0?g:n!==void 0||o?!1:r<a}),2),c=l[0],f=l[1],p=u.useCallback(function(g){f(function(m){var h=typeof g=="boolean"?g:g(m);return s(e,h,n),h})},[n,e,s]);return[c,p]}var vt=function(e){return M(de,bn(J({component:"div"},e),{sx:J({display:"inline-block"},e.sx)}))},na=function(e){var t=e.dataType,n=e.enable,r=n===void 0?!0:n;return r?M(vt,{className:"data-type-label",sx:{mx:.5,fontSize:"0.7rem",opacity:.8,userSelect:"none"},children:t}):null};function ot(e,t,n){var r=n.fromString,o=n.colorKey,i=n.displayTypeLabel,s=i===void 0?!0:i,a=u.memo(t),l=function(f){var p=B(function(h){return h.displayDataTypes}),g=B(function(h){return h.colorspace[o]}),m=B(function(h){return h.onSelect});return _e(vt,{onClick:function(){return m==null?void 0:m(f.path,f.value)},sx:{color:g},children:[s&&p&&M(na,{dataType:e}),M(vt,{className:"".concat(e,"-value"),children:M(a,{value:f.value})})]})};if(l.displayName="easy-".concat(e,"-type"),!r)return{Component:l};var c=function(f){var p=f.value,g=f.setValue,m=B(function(h){return h.colorspace[o]});return M(If,{value:p,onChange:u.useCallback(function(h){var v=r(h.target.value);g(v)},[g]),size:"small",multiline:!0,sx:{color:m,padding:.5,borderStyle:"solid",borderColor:"black",borderWidth:1,fontSize:"0.8rem",fontFamily:"monospace",display:"inline-flex"}})};return c.displayName="easy-".concat(e,"-type-editor"),{Component:l,Editor:c}}var Kf=function(e){var t=e.toString(),n=!0,r=t.indexOf(")"),o=t.indexOf("=>");return o!==-1&&o>r&&(n=!1),n?t.substring(t.indexOf("{",r)+1,t.lastIndexOf("}")):t.substring(t.indexOf("=>")+2)},Yf=function(e){var t=e.toString(),n=t.indexOf("function")!==-1;return n?t.substring(8,t.indexOf("{")).trim():t.substring(0,t.indexOf("=>")+2).trim()},Xf="{",Jf="}",Qf=function(e){return _e(co,{children:[M(na,{dataType:"function"}),_e(de,{component:"span",className:"data-function-start",sx:{letterSpacing:.5},children:[Yf(e.value)," ",Xf]})]})},eg=function(){return M(co,{children:M(de,{component:"span",className:"data-function-end",children:Jf})})},tg=function(e){var t=B(function(n){return n.colorspace.base05});return M(co,{children:M(de,{className:"data-function",sx:{display:e.inspect?"block":"inline-block",pl:e.inspect?2:0,color:t},children:e.inspect?Kf(e.value):M(de,{component:"span",className:"data-function-body",onClick:function(){return e.setInspect(!0)},sx:{"&:hover":{cursor:"pointer"},padding:.5},children:"…"})})})};function ng(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function rg(e,t){if(e==null)return{};var n=ng(e,t),r,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}var Ft=function(e){var t=e.d,n=rg(e,["d"]);return M(Us,bn(J({},n),{children:M("path",{d:t})}))},og="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z",ig="M10 6 8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z",sg="M 12 2 C 10.615 1.998 9.214625 2.2867656 7.890625 2.8847656 L 8.9003906 4.6328125 C 9.9043906 4.2098125 10.957 3.998 12 4 C 15.080783 4 17.738521 5.7633175 19.074219 8.3222656 L 17.125 9 L 21.25 11 L 22.875 7 L 20.998047 7.6523438 C 19.377701 4.3110398 15.95585 2 12 2 z M 6.5097656 4.4882812 L 2.2324219 5.0820312 L 3.734375 6.3808594 C 1.6515335 9.4550558 1.3615962 13.574578 3.3398438 17 C 4.0308437 18.201 4.9801562 19.268234 6.1601562 20.115234 L 7.1699219 18.367188 C 6.3019219 17.710187 5.5922656 16.904 5.0722656 16 C 3.5320014 13.332354 3.729203 10.148679 5.2773438 7.7128906 L 6.8398438 9.0625 L 6.5097656 4.4882812 z M 19.929688 13 C 19.794687 14.08 19.450734 15.098 18.927734 16 C 17.386985 18.668487 14.531361 20.090637 11.646484 19.966797 L 12.035156 17.9375 L 8.2402344 20.511719 L 10.892578 23.917969 L 11.265625 21.966797 C 14.968963 22.233766 18.681899 20.426323 20.660156 17 C 21.355156 15.801 21.805219 14.445 21.949219 13 L 19.929688 13 z",ag="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z",lg="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z",ug="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.9959.9959 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z",cg="M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z",di=function(e){return M(Ft,J({d:og},e))},dg=function(e){return M(Ft,J({d:ig},e))},fg=function(e){return M(Ft,J({d:sg},e))},gg=function(e){return M(Ft,J({d:ag},e))},mg=function(e){return M(Ft,J({d:lg},e))},pg=function(e){return M(Ft,J({d:ug},e))},hg=function(e){return M(Ft,J({d:cg},e))},vg="{",yg="[",Sg="}",bg="]";function ra(e){var t=Cn(e),n="";return(Je(e,Map)||Je(e,Set))&&(n=e[Symbol.toStringTag]),Object.prototype.hasOwnProperty.call(e,Symbol.toStringTag)&&(n=e[Symbol.toStringTag]),"".concat(t," Items").concat(n?" (".concat(n,")"):"")}var xg=function(e){var t=B(function(l){return l.colorspace.base04}),n=mo(),r=u.useMemo(function(){return Array.isArray(e.value)},[e.value]),o=u.useMemo(function(){return Cn(e.value)===0},[e.value]),i=u.useMemo(function(){return ra(e.value)},[e.value]),s=B(function(l){return l.displayObjectSize}),a=po(e.path,e.value);return _e(de,{component:"span",className:"data-object-start",sx:{letterSpacing:.5},children:[r?yg:vg,s&&e.inspect&&!o&&M(de,{component:"span",sx:{pl:.5,fontStyle:"italic",color:t,userSelect:"none"},children:i}),a&&!e.inspect&&_e(He,{children:[M(fg,{sx:{fontSize:12,color:n,mx:.5}}),a]})]})},Cg=function(e){var t=B(function(s){return s.colorspace.base04}),n=u.useMemo(function(){return Array.isArray(e.value)},[e.value]),r=B(function(s){return s.displayObjectSize}),o=u.useMemo(function(){return Cn(e.value)===0},[e.value]),i=u.useMemo(function(){return ra(e.value)},[e.value]);return _e(de,{component:"span",className:"data-object-end",children:[n?bg:Sg,r&&(o||!e.inspect)?M(de,{component:"span",sx:{pl:.5,fontStyle:"italic",color:t,userSelect:"none"},children:i}):null]})};function wg(e){return typeof(e==null?void 0:e[Symbol.iterator])=="function"}var _g=function(e){var t=mo(),n=B(function(h){return h.colorspace.base02}),r=B(function(h){return h.groupArraysAfterLength}),o=po(e.path,e.value),i=Pe(u.useState(B(function(h){return h.maxDisplayLength})),2),s=i[0],a=i[1],l=B(function(h){return h.objectSortKeys}),c=u.useMemo(function(){if(!e.inspect)return null;var h=e.value,v=wg(h);if(v&&!Array.isArray(h)){var y=[];if(Je(h,Map))h.forEach(function(P,k){var H=k.toString(),Z=Ne(e.path).concat([H]);y.push(M(Ht,{path:Z,value:P,prevValue:Je(e.prevValue,Map)?e.prevValue.get(k):void 0,editable:!1},H))});else for(var S=h[Symbol.iterator](),x=S.next(),C=0;!x.done;)y.push(M(Ht,{path:Ne(e.path).concat(["iterator:".concat(C)]),value:x.value,nestedIndex:C,editable:!1},C)),C++,x=S.next();return y}if(Array.isArray(h)){if(h.length<=r){var _=h.slice(0,s).map(function(P,k){var H=Ne(e.path).concat([k]);return M(Ht,{path:H,value:P,prevValue:Array.isArray(e.prevValue)?e.prevValue[k]:void 0},k)});if(h.length>s){var F=h.length-s;_.push(_e(vt,{sx:{cursor:"pointer",lineHeight:1.5,color:t,letterSpacing:.5,opacity:.8,userSelect:"none"},onClick:function(){return a(function(P){return P*2})},children:["hidden ",F," items…"]},"last"))}return _}var E=ci(h,r),$=Array.isArray(e.prevValue)?ci(e.prevValue,r):void 0;return E.map(function(P,k){var H=Ne(e.path);return M(Ht,{path:H,value:P,nestedIndex:k,prevValue:$==null?void 0:$[k]},k)})}var V=Object.entries(h);l&&(V=l===!0?V.sort(function(P,k){var H=Pe(P,1),Z=H[0],G=Pe(k,1),ee=G[0];return Z.localeCompare(ee)}):V.sort(function(P,k){var H=Pe(P,1),Z=H[0],G=Pe(k,1),ee=G[0];return l(Z,ee)}));var z=V.slice(0,s).map(function(P){var k=Pe(P,2),H=k[0],Z=k[1],G,ee=Ne(e.path).concat([H]);return M(Ht,{path:ee,value:Z,prevValue:(G=e.prevValue)===null||G===void 0?void 0:G[H]},H)});if(V.length>s){var D=V.length-s;z.push(_e(vt,{sx:{cursor:"pointer",lineHeight:1.5,color:t,letterSpacing:.5,opacity:.8,userSelect:"none"},onClick:function(){return a(function(P){return P*2})},children:["hidden ",D," items…"]},"last"))}return z},[e.inspect,e.value,e.prevValue,e.path,r,s,t,l]),f=e.inspect?.6:0,p=B(function(h){return h.indentWidth}),g=e.inspect?p-f:p,m=u.useMemo(function(){return Cn(e.value)===0},[e.value]);return m?null:M(de,{className:"data-object",sx:{display:e.inspect?"block":"inline-block",pl:e.inspect?g-.6:0,marginLeft:f,color:t,borderLeft:e.inspect?"1px solid ".concat(n):"none"},children:e.inspect?c:!o&&M(de,{component:"span",className:"data-object-body",onClick:function(){return e.setInspect(!0)},sx:{"&:hover":{cursor:"pointer"},padding:.5,userSelect:"none"},children:"…"})})},Rg=function(){return Da()(function(e){return{registry:[],registerTypes:function(t){e(function(n){return{registry:typeof t=="function"?t(n.registry):t}})}}})},ho=u.createContext(void 0);ho.Provider;var oa=function(e,t){var n=u.useContext(ho);return Fi(n,e,t)},Eg={is:function(e){return typeof e=="object"},Component:_g,PreComponent:xg,PostComponent:Cg};function Mg(e,t,n){var r,o=!0,i=!1,s=void 0;try{for(var a=n[Symbol.iterator](),l;!(o=(l=a.next()).done);o=!0){var c=l.value;if(c.is(e,t)&&(r=c,typeof e=="object"))return c}}catch(f){i=!0,s=f}finally{try{!o&&a.return!=null&&a.return()}finally{if(i)throw s}}if(r===void 0){if(typeof e=="object")return Eg;throw new Error("this is not possible")}return r}function Pg(e,t){var n=oa(function(r){return r.registry});return u.useMemo(function(){return Mg(e,t,n)},[e,t,n])}function $g(){var e=function(i){function s(a,l){var c,f;return Object.is(a.value,l.value)&&a.inspect&&l.inspect&&((c=a.path)===null||c===void 0?void 0:c.join("."))===((f=l.path)===null||f===void 0?void 0:f.join("."))}i.Component=u.memo(i.Component,s),i.Editor&&(i.Editor=u.memo(i.Editor,function(l,c){return Object.is(l.value,c.value)})),i.PreComponent&&(i.PreComponent=u.memo(i.PreComponent,s)),i.PostComponent&&(i.PostComponent=u.memo(i.PostComponent,s)),t.push(i)},t=[];e(J({is:function(o){return typeof o=="boolean"}},ot("bool",function(o){var i=o.value;return M(He,{children:i?"true":"false"})},{colorKey:"base0E",fromString:function(o){return!!o}})));var n={weekday:"short",year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"};e(J({is:function(o){return Je(o,Date)}},ot("date",function(o){var i=o.value;return M(He,{children:i.toLocaleTimeString("en-us",n)})},{colorKey:"base0D"}))),e(J({is:function(o){return o===null}},ot("null",function(){var o=B(function(i){return i.colorspace.base02});return M(de,{sx:{fontSize:"0.8rem",backgroundColor:o,fontWeight:"bold",borderRadius:"3px",padding:"0.5px 2px"},children:"NULL"})},{colorKey:"base08",displayTypeLabel:!1}))),e(J({is:function(o){return o===void 0}},ot("undefined",function(){var o=B(function(i){return i.colorspace.base02});return M(de,{sx:{fontSize:"0.7rem",backgroundColor:o,borderRadius:"3px",padding:"0.5px 2px"},children:"undefined"})},{colorKey:"base05",displayTypeLabel:!1}))),e(J({is:function(o){return typeof o=="string"}},ot("string",function(o){var i=Pe(u.useState(!1),2),s=i[0],a=i[1],l=B(function(p){return p.collapseStringsAfterLength}),c=s?o.value:o.value.slice(0,l),f=o.value.length>l;return _e(de,{component:"span",sx:{overflowWrap:"anywhere",cursor:f?"pointer":"inherit"},onClick:function(){f&&a(function(p){return!p})},children:['"',c,f&&!s&&M(de,{component:"span",sx:{padding:.5},children:"…"}),'"']})},{colorKey:"base09",fromString:function(o){return o}}))),e({is:function(o){return typeof o=="function"},Component:tg,PreComponent:Qf,PostComponent:eg});var r=function(o){return o%1===0};return e(J({is:function(o){return typeof o=="number"&&isNaN(o)}},ot("NaN",function(){var o=B(function(i){return i.colorspace.base02});return M(de,{sx:{backgroundColor:o,fontSize:"0.8rem",fontWeight:"bold",borderRadius:"3px"},children:"NaN"})},{colorKey:"base08",displayTypeLabel:!1}))),e(J({is:function(o){return typeof o=="number"&&!r(o)}},ot("float",function(o){var i=o.value;return M(He,{children:i})},{colorKey:"base0B",fromString:function(o){return parseFloat(o)}}))),e(J({is:function(o){return typeof o=="number"&&r(o)}},ot("int",function(o){var i=o.value;return M(He,{children:i})},{colorKey:"base0F",fromString:function(o){return parseInt(o)}}))),e(J({is:function(o){return(typeof o>"u"?"undefined":On(o))==="bigint"}},ot("bigint",function(o){var i=o.value;return M(He,{children:"".concat(i,"n")})},{colorKey:"base0F",fromString:function(o){return BigInt(o.replace(/\D/g,""))}}))),t}var En=function(e){return M(de,bn(J({component:"span"},e),{sx:J({cursor:"pointer",paddingLeft:"0.7rem"},e.sx)}))},Ht=function(e){var t=e.value,n=e.prevValue,r=e.path,o=e.nestedIndex,i,s=(i=e.editable)!==null&&i!==void 0?i:void 0,a=B(function(T){return T.editable}),l=u.useMemo(function(){return a===!1||s===!1?!1:typeof a=="function"?!!a(r,t):a},[r,s,a,t]),c=Pe(u.useState(typeof t=="function"?function(){return t}:t),2),f=c[0],p=c[1],g=r.length,m=r[g-1],h=B(function(T){return T.hoverPath}),v=u.useMemo(function(){return h&&r.every(function(T,R){return T===h.path[R]&&o===h.nestedIndex})},[h,r,o]),y=B(function(T){return T.setHover}),S=B(function(T){return T.value}),x=Pe(Uf(r,t,o),2),C=x[0],_=x[1],F=Pe(u.useState(!1),2),E=F[0],$=F[1],V=B(function(T){return T.onChange}),z=mo(),D=B(function(T){return T.colorspace.base0C}),P=B(function(T){return T.colorspace.base0A}),k=Pg(t,r),H=k.Component,Z=k.PreComponent,G=k.PostComponent,ee=k.Editor,Y=B(function(T){return T.quotesOnKeys}),A=B(function(T){return T.rootName}),q=S===t,Ee=Number.isInteger(Number(m)),Me=B(function(T){return T.enableClipboard}),Fe=qf(),X=Fe.copy,U=Fe.copied,le=B(function(T){return T.highlightUpdates}),me=u.useMemo(function(){return!le||n===void 0?!1:(typeof t>"u"?"undefined":On(t))!==(typeof n>"u"?"undefined":On(n))?!0:typeof t=="number"?isNaN(t)&&isNaN(n)?!1:t!==n:Array.isArray(t)!==Array.isArray(n)?!0:typeof t=="object"||typeof t=="function"?!1:t!==n},[le,n,t]),xe=u.useRef();u.useEffect(function(){xe.current&&me&&"animate"in xe.current&&xe.current.animate([{backgroundColor:P},{backgroundColor:""}],{duration:1e3,easing:"ease-in"})},[P,me,n,t]);var De=u.useMemo(function(){return E?_e(He,{children:[M(En,{children:M(gg,{sx:{fontSize:".8rem"},onClick:function(){$(!1),p(t)}})}),M(En,{children:M(di,{sx:{fontSize:".8rem"},onClick:function(){$(!1),V(r,t,f)}})})]}):_e(He,{children:[Me&&M(En,{onClick:function(T){T.preventDefault();try{X(r,t,Dr)}catch(R){console.error(R)}},children:U?M(di,{sx:{fontSize:".8rem"}}):M(mg,{sx:{fontSize:".8rem"}})}),ee&&l&&M(En,{onClick:function(T){T.preventDefault(),$(!0),p(t)},children:M(pg,{sx:{fontSize:".8rem"}})})]})},[ee,U,X,l,E,Me,V,r,f,t]),Se=u.useMemo(function(){return Cn(t)===0},[t]),he=!Se&&!!(Z&&G),W=B(function(T){return T.keyRenderer}),te=u.useMemo(function(){return{path:r,inspect:C,setInspect:_,value:t,prevValue:n}},[C,r,_,t,n]);return _e(de,{className:"data-key-pair","data-testid":"data-key-pair"+r.join("."),sx:{userSelect:"text"},onMouseEnter:u.useCallback(function(){return y(r,o)},[y,r,o]),children:[_e(vt,{component:"span",className:"data-key",sx:{lineHeight:1.5,color:z,letterSpacing:.5,opacity:.8},onClick:u.useCallback(function(T){T.isDefaultPrevented()||Se||_(function(R){return!R})},[Se,_]),children:[he?C?M(hg,{sx:{fontSize:".8rem","&:hover":{cursor:"pointer"}}}):M(dg,{sx:{fontSize:".8rem","&:hover":{cursor:"pointer"}}}):null,M(de,{ref:xe,component:"span",children:q?A!==!1?Y?_e(He,{children:['"',A,'"']}):M(He,{children:A}):null:W.when(te)?M(W,J({},te)):o===void 0&&(Ee?M(de,{component:"span",style:{color:D},children:m}):Y?_e(He,{children:['"',m,'"']}):M(He,{children:m}))}),q?A!==!1&&M(vt,{sx:{mr:.5},children:":"}):o===void 0&&M(vt,{sx:{mr:.5},children:":"}),Z&&M(Z,J({},te)),v&&he&&C&&De]}),E&&l?ee&&M(ee,{value:f,setValue:p}):H?M(H,J({},te)):M(de,{component:"span",className:"data-value-fallback",children:"fallback: ".concat(t)}),G&&M(G,J({},te)),v&&he&&!C&&De,v&&!he&&De]})},fi="(prefers-color-scheme: dark)";function Fg(){var e=Pe(u.useState(!1),2),t=e[0],n=e[1];return u.useEffect(function(){var r=function(i){n(i.matches)};n(window.matchMedia(fi).matches);var o=window.matchMedia(fi);return o.addEventListener("change",r),function(){return o.removeEventListener("change",r)}},[]),t}function je(e,t){var n=u.useContext(xn).setState;u.useEffect(function(){t!==void 0&&n(fo({},e,t))},[e,t,n])}var kg=function(e){var t=u.useContext(xn).setState;u.useEffect(function(){t(function(p){return{prevValue:p.value,value:e.value}})},[e.value,t]),je("editable",e.editable),je("indentWidth",e.indentWidth),je("onChange",e.onChange),je("groupArraysAfterLength",e.groupArraysAfterLength),je("keyRenderer",e.keyRenderer),je("maxDisplayLength",e.maxDisplayLength),je("enableClipboard",e.enableClipboard),je("highlightUpdates",e.highlightUpdates),je("rootName",e.rootName),je("displayDataTypes",e.displayDataTypes),je("displayObjectSize",e.displayObjectSize),je("onCopy",e.onCopy),je("onSelect",e.onSelect),u.useEffect(function(){e.theme==="light"?t({colorspace:go}):e.theme==="dark"?t({colorspace:Js}):typeof e.theme=="object"&&t({colorspace:e.theme})},[t,e.theme]);var n=u.useMemo(function(){return typeof e.theme=="object"?"json-viewer-theme-custom":e.theme==="dark"?"json-viewer-theme-dark":"json-viewer-theme-light"},[e.theme]),r=u.useRef(!0),o=u.useMemo(function(){return $g()},[]),i=oa(function(p){return p.registerTypes});if(r.current){var s=e.valueTypes?Ne(o).concat(Ne(e.valueTypes)):Ne(o);i(s),r.current=!1}u.useEffect(function(){var p=e.valueTypes?Ne(o).concat(Ne(e.valueTypes)):Ne(o);i(p)},[e.valueTypes,o,i]);var a=B(function(p){return p.value}),l=B(function(p){return p.prevValue}),c=B(function(p){return p.setHover}),f=u.useCallback(function(){return c(null)},[c]);return M(hf,{elevation:0,className:Hf(n,e.className),style:e.style,sx:J({fontFamily:"monospace",userSelect:"none",contentVisibility:"auto"},e.sx),onMouseLeave:f,children:M(Ht,{value:a,prevValue:l,path:u.useMemo(function(){return[]},[])})})},jg=function(t){var n=Fg(),r,o=u.useMemo(function(){return t.theme==="auto"?n?"light":"dark":(r=t.theme)!==null&&r!==void 0?r:"light"},[n,t.theme]),i=u.useMemo(function(){var c=typeof o=="object"?o.base00:o==="dark"?Js.base00:go.base00;return lo({components:{MuiPaper:{styleOverrides:{root:{backgroundColor:c}}}},palette:{mode:o==="dark"?"dark":"light",background:{default:c}}})},[o]),s=bn(J({},t),{theme:o}),a=u.useMemo(function(){return Gf(t)},[]),l=u.useMemo(function(){return Rg()},[]);return M(af,{theme:i,children:M(ho.Provider,{value:l,children:M(xn.Provider,{value:a,children:M(kg,J({},s))})})})};/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function pt(e,t){return typeof e=="function"?e(t):e}function Te(e,t){return n=>{t.setState(r=>({...r,[e]:pt(n,r[e])}))}}function lr(e){return e instanceof Function}function Ig(e){return Array.isArray(e)&&e.every(t=>typeof t=="number")}function Ag(e,t){const n=[],r=o=>{o.forEach(i=>{n.push(i);const s=t(i);s!=null&&s.length&&r(s)})};return r(e),n}function O(e,t,n){let r=[],o;return i=>{let s;n.key&&n.debug&&(s=Date.now());const a=e(i);if(!(a.length!==r.length||a.some((f,p)=>r[p]!==f)))return o;r=a;let c;if(n.key&&n.debug&&(c=Date.now()),o=t(...a),n==null||n.onChange==null||n.onChange(o),n.key&&n.debug&&n!=null&&n.debug()){const f=Math.round((Date.now()-s)*100)/100,p=Math.round((Date.now()-c)*100)/100,g=p/16,m=(h,v)=>{for(h=String(h);h.length<v;)h=" "+h;return h};console.info(`%c⏱ ${m(p,5)} /${m(f,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*g,120))}deg 100% 31%);`,n==null?void 0:n.key)}return o}}function L(e,t,n,r){return{debug:()=>{var o;return(o=e==null?void 0:e.debugAll)!=null?o:e[t]},key:!1,onChange:r}}function Vg(e,t,n,r){const o=()=>{var s;return(s=i.getValue())!=null?s:e.options.renderFallbackValue},i={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(r),renderValue:o,getContext:O(()=>[e,n,t,i],(s,a,l,c)=>({table:s,column:a,row:l,cell:c,getValue:c.getValue,renderValue:c.renderValue}),L(e.options,"debugCells"))};return e._features.forEach(s=>{s.createCell==null||s.createCell(i,n,t,e)},{}),i}function Tg(e,t,n,r){var o,i;const a={...e._getDefaultColumnDef(),...t},l=a.accessorKey;let c=(o=(i=a.id)!=null?i:l?typeof String.prototype.replaceAll=="function"?l.replaceAll(".","_"):l.replace(/\./g,"_"):void 0)!=null?o:typeof a.header=="string"?a.header:void 0,f;if(a.accessorFn?f=a.accessorFn:l&&(l.includes(".")?f=g=>{let m=g;for(const v of l.split(".")){var h;m=(h=m)==null?void 0:h[v]}return m}:f=g=>g[a.accessorKey]),!c)throw new Error;let p={id:`${String(c)}`,accessorFn:f,parent:r,depth:n,columnDef:a,columns:[],getFlatColumns:O(()=>[!0],()=>{var g;return[p,...(g=p.columns)==null?void 0:g.flatMap(m=>m.getFlatColumns())]},L(e.options,"debugColumns")),getLeafColumns:O(()=>[e._getOrderColumnsFn()],g=>{var m;if((m=p.columns)!=null&&m.length){let h=p.columns.flatMap(v=>v.getLeafColumns());return g(h)}return[p]},L(e.options,"debugColumns"))};for(const g of e._features)g.createColumn==null||g.createColumn(p,e);return p}const be="debugHeaders";function gi(e,t,n){var r;let i={id:(r=n.id)!=null?r:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const s=[],a=l=>{l.subHeaders&&l.subHeaders.length&&l.subHeaders.map(a),s.push(l)};return a(i),s},getContext:()=>({table:e,header:i,column:t})};return e._features.forEach(s=>{s.createHeader==null||s.createHeader(i,e)}),i}const Dg={createTable:e=>{e.getHeaderGroups=O(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,o)=>{var i,s;const a=(i=r==null?void 0:r.map(p=>n.find(g=>g.id===p)).filter(Boolean))!=null?i:[],l=(s=o==null?void 0:o.map(p=>n.find(g=>g.id===p)).filter(Boolean))!=null?s:[],c=n.filter(p=>!(r!=null&&r.includes(p.id))&&!(o!=null&&o.includes(p.id)));return Mn(t,[...a,...c,...l],e)},L(e.options,be)),e.getCenterHeaderGroups=O(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,o)=>(n=n.filter(i=>!(r!=null&&r.includes(i.id))&&!(o!=null&&o.includes(i.id))),Mn(t,n,e,"center")),L(e.options,be)),e.getLeftHeaderGroups=O(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,r)=>{var o;const i=(o=r==null?void 0:r.map(s=>n.find(a=>a.id===s)).filter(Boolean))!=null?o:[];return Mn(t,i,e,"left")},L(e.options,be)),e.getRightHeaderGroups=O(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,r)=>{var o;const i=(o=r==null?void 0:r.map(s=>n.find(a=>a.id===s)).filter(Boolean))!=null?o:[];return Mn(t,i,e,"right")},L(e.options,be)),e.getFooterGroups=O(()=>[e.getHeaderGroups()],t=>[...t].reverse(),L(e.options,be)),e.getLeftFooterGroups=O(()=>[e.getLeftHeaderGroups()],t=>[...t].reverse(),L(e.options,be)),e.getCenterFooterGroups=O(()=>[e.getCenterHeaderGroups()],t=>[...t].reverse(),L(e.options,be)),e.getRightFooterGroups=O(()=>[e.getRightHeaderGroups()],t=>[...t].reverse(),L(e.options,be)),e.getFlatHeaders=O(()=>[e.getHeaderGroups()],t=>t.map(n=>n.headers).flat(),L(e.options,be)),e.getLeftFlatHeaders=O(()=>[e.getLeftHeaderGroups()],t=>t.map(n=>n.headers).flat(),L(e.options,be)),e.getCenterFlatHeaders=O(()=>[e.getCenterHeaderGroups()],t=>t.map(n=>n.headers).flat(),L(e.options,be)),e.getRightFlatHeaders=O(()=>[e.getRightHeaderGroups()],t=>t.map(n=>n.headers).flat(),L(e.options,be)),e.getCenterLeafHeaders=O(()=>[e.getCenterFlatHeaders()],t=>t.filter(n=>{var r;return!((r=n.subHeaders)!=null&&r.length)}),L(e.options,be)),e.getLeftLeafHeaders=O(()=>[e.getLeftFlatHeaders()],t=>t.filter(n=>{var r;return!((r=n.subHeaders)!=null&&r.length)}),L(e.options,be)),e.getRightLeafHeaders=O(()=>[e.getRightFlatHeaders()],t=>t.filter(n=>{var r;return!((r=n.subHeaders)!=null&&r.length)}),L(e.options,be)),e.getLeafHeaders=O(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(t,n,r)=>{var o,i,s,a,l,c;return[...(o=(i=t[0])==null?void 0:i.headers)!=null?o:[],...(s=(a=n[0])==null?void 0:a.headers)!=null?s:[],...(l=(c=r[0])==null?void 0:c.headers)!=null?l:[]].map(f=>f.getLeafHeaders()).flat()},L(e.options,be))}};function Mn(e,t,n,r){var o,i;let s=0;const a=function(g,m){m===void 0&&(m=1),s=Math.max(s,m),g.filter(h=>h.getIsVisible()).forEach(h=>{var v;(v=h.columns)!=null&&v.length&&a(h.columns,m+1)},0)};a(e);let l=[];const c=(g,m)=>{const h={depth:m,id:[r,`${m}`].filter(Boolean).join("_"),headers:[]},v=[];g.forEach(y=>{const S=[...v].reverse()[0],x=y.column.depth===h.depth;let C,_=!1;if(x&&y.column.parent?C=y.column.parent:(C=y.column,_=!0),S&&(S==null?void 0:S.column)===C)S.subHeaders.push(y);else{const F=gi(n,C,{id:[r,m,C.id,y==null?void 0:y.id].filter(Boolean).join("_"),isPlaceholder:_,placeholderId:_?`${v.filter(E=>E.column===C).length}`:void 0,depth:m,index:v.length});F.subHeaders.push(y),v.push(F)}h.headers.push(y),y.headerGroup=h}),l.push(h),m>0&&c(v,m-1)},f=t.map((g,m)=>gi(n,g,{depth:s,index:m}));c(f,s-1),l.reverse();const p=g=>g.filter(h=>h.column.getIsVisible()).map(h=>{let v=0,y=0,S=[0];h.subHeaders&&h.subHeaders.length?(S=[],p(h.subHeaders).forEach(C=>{let{colSpan:_,rowSpan:F}=C;v+=_,S.push(F)})):v=1;const x=Math.min(...S);return y=y+x,h.colSpan=v,h.rowSpan=y,{colSpan:v,rowSpan:y}});return p((o=(i=l[0])==null?void 0:i.headers)!=null?o:[]),l}const Og=(e,t,n,r,o,i,s)=>{let a={id:t,index:r,original:n,depth:o,parentId:s,_valuesCache:{},_uniqueValuesCache:{},getValue:l=>{if(a._valuesCache.hasOwnProperty(l))return a._valuesCache[l];const c=e.getColumn(l);if(c!=null&&c.accessorFn)return a._valuesCache[l]=c.accessorFn(a.original,r),a._valuesCache[l]},getUniqueValues:l=>{if(a._uniqueValuesCache.hasOwnProperty(l))return a._uniqueValuesCache[l];const c=e.getColumn(l);if(c!=null&&c.accessorFn)return c.columnDef.getUniqueValues?(a._uniqueValuesCache[l]=c.columnDef.getUniqueValues(a.original,r),a._uniqueValuesCache[l]):(a._uniqueValuesCache[l]=[a.getValue(l)],a._uniqueValuesCache[l])},renderValue:l=>{var c;return(c=a.getValue(l))!=null?c:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>Ag(a.subRows,l=>l.subRows),getParentRow:()=>a.parentId?e.getRow(a.parentId,!0):void 0,getParentRows:()=>{let l=[],c=a;for(;;){const f=c.getParentRow();if(!f)break;l.push(f),c=f}return l.reverse()},getAllCells:O(()=>[e.getAllLeafColumns()],l=>l.map(c=>Vg(e,a,c,c.id)),L(e.options,"debugRows")),_getAllCellsByColumnId:O(()=>[a.getAllCells()],l=>l.reduce((c,f)=>(c[f.column.id]=f,c),{}),L(e.options,"debugRows"))};for(let l=0;l<e._features.length;l++){const c=e._features[l];c==null||c.createRow==null||c.createRow(a,e)}return a},Lg={createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},ia=(e,t,n)=>{var r,o;const i=n==null||(r=n.toString())==null?void 0:r.toLowerCase();return!!(!((o=e.getValue(t))==null||(o=o.toString())==null||(o=o.toLowerCase())==null)&&o.includes(i))};ia.autoRemove=e=>Ke(e);const sa=(e,t,n)=>{var r;return!!(!((r=e.getValue(t))==null||(r=r.toString())==null)&&r.includes(n))};sa.autoRemove=e=>Ke(e);const aa=(e,t,n)=>{var r;return((r=e.getValue(t))==null||(r=r.toString())==null?void 0:r.toLowerCase())===(n==null?void 0:n.toLowerCase())};aa.autoRemove=e=>Ke(e);const la=(e,t,n)=>{var r;return(r=e.getValue(t))==null?void 0:r.includes(n)};la.autoRemove=e=>Ke(e);const ua=(e,t,n)=>!n.some(r=>{var o;return!((o=e.getValue(t))!=null&&o.includes(r))});ua.autoRemove=e=>Ke(e)||!(e!=null&&e.length);const ca=(e,t,n)=>n.some(r=>{var o;return(o=e.getValue(t))==null?void 0:o.includes(r)});ca.autoRemove=e=>Ke(e)||!(e!=null&&e.length);const da=(e,t,n)=>e.getValue(t)===n;da.autoRemove=e=>Ke(e);const fa=(e,t,n)=>e.getValue(t)==n;fa.autoRemove=e=>Ke(e);const vo=(e,t,n)=>{let[r,o]=n;const i=e.getValue(t);return i>=r&&i<=o};vo.resolveFilterValue=e=>{let[t,n]=e,r=typeof t!="number"?parseFloat(t):t,o=typeof n!="number"?parseFloat(n):n,i=t===null||Number.isNaN(r)?-1/0:r,s=n===null||Number.isNaN(o)?1/0:o;if(i>s){const a=i;i=s,s=a}return[i,s]};vo.autoRemove=e=>Ke(e)||Ke(e[0])&&Ke(e[1]);const it={includesString:ia,includesStringSensitive:sa,equalsString:aa,arrIncludes:la,arrIncludesAll:ua,arrIncludesSome:ca,equals:da,weakEquals:fa,inNumberRange:vo};function Ke(e){return e==null||e===""}const Hg={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:Te("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{const n=t.getCoreRowModel().flatRows[0],r=n==null?void 0:n.getValue(e.id);return typeof r=="string"?it.includesString:typeof r=="number"?it.inNumberRange:typeof r=="boolean"||r!==null&&typeof r=="object"?it.equals:Array.isArray(r)?it.arrIncludes:it.weakEquals},e.getFilterFn=()=>{var n,r;return lr(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(n=(r=t.options.filterFns)==null?void 0:r[e.columnDef.filterFn])!=null?n:it[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,r,o;return((n=e.columnDef.enableColumnFilter)!=null?n:!0)&&((r=t.options.enableColumnFilters)!=null?r:!0)&&((o=t.options.enableFilters)!=null?o:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return(n=t.getState().columnFilters)==null||(n=n.find(r=>r.id===e.id))==null?void 0:n.value},e.getFilterIndex=()=>{var n,r;return(n=(r=t.getState().columnFilters)==null?void 0:r.findIndex(o=>o.id===e.id))!=null?n:-1},e.setFilterValue=n=>{t.setColumnFilters(r=>{const o=e.getFilterFn(),i=r==null?void 0:r.find(f=>f.id===e.id),s=pt(n,i?i.value:void 0);if(mi(o,s,e)){var a;return(a=r==null?void 0:r.filter(f=>f.id!==e.id))!=null?a:[]}const l={id:e.id,value:s};if(i){var c;return(c=r==null?void 0:r.map(f=>f.id===e.id?l:f))!=null?c:[]}return r!=null&&r.length?[...r,l]:[l]})}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{const n=e.getAllLeafColumns(),r=o=>{var i;return(i=pt(t,o))==null?void 0:i.filter(s=>{const a=n.find(l=>l.id===s.id);if(a){const l=a.getFilterFn();if(mi(l,s.value,a))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(r)},e.resetColumnFilters=t=>{var n,r;e.setColumnFilters(t?[]:(n=(r=e.initialState)==null?void 0:r.columnFilters)!=null?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function mi(e,t,n){return(e&&e.autoRemove?e.autoRemove(t,n):!1)||typeof t>"u"||typeof t=="string"&&!t}const zg=(e,t,n)=>n.reduce((r,o)=>{const i=o.getValue(e);return r+(typeof i=="number"?i:0)},0),Ng=(e,t,n)=>{let r;return n.forEach(o=>{const i=o.getValue(e);i!=null&&(r>i||r===void 0&&i>=i)&&(r=i)}),r},Bg=(e,t,n)=>{let r;return n.forEach(o=>{const i=o.getValue(e);i!=null&&(r<i||r===void 0&&i>=i)&&(r=i)}),r},Gg=(e,t,n)=>{let r,o;return n.forEach(i=>{const s=i.getValue(e);s!=null&&(r===void 0?s>=s&&(r=o=s):(r>s&&(r=s),o<s&&(o=s)))}),[r,o]},Zg=(e,t)=>{let n=0,r=0;if(t.forEach(o=>{let i=o.getValue(e);i!=null&&(i=+i)>=i&&(++n,r+=i)}),n)return r/n},Wg=(e,t)=>{if(!t.length)return;const n=t.map(i=>i.getValue(e));if(!Ig(n))return;if(n.length===1)return n[0];const r=Math.floor(n.length/2),o=n.sort((i,s)=>i-s);return n.length%2!==0?o[r]:(o[r-1]+o[r])/2},qg=(e,t)=>Array.from(new Set(t.map(n=>n.getValue(e))).values()),Ug=(e,t)=>new Set(t.map(n=>n.getValue(e))).size,Kg=(e,t)=>t.length,Sr={sum:zg,min:Ng,max:Bg,extent:Gg,mean:Zg,median:Wg,unique:qg,uniqueCount:Ug,count:Kg},Yg={getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return(t=(n=e.getValue())==null||n.toString==null?void 0:n.toString())!=null?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:Te("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(n=>n!=null&&n.includes(e.id)?n.filter(r=>r!==e.id):[...n??[],e.id])},e.getCanGroup=()=>{var n,r;return((n=e.columnDef.enableGrouping)!=null?n:!0)&&((r=t.options.enableGrouping)!=null?r:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var n;return(n=t.getState().grouping)==null?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return(n=t.getState().grouping)==null?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const n=e.getCanGroup();return()=>{n&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const n=t.getCoreRowModel().flatRows[0],r=n==null?void 0:n.getValue(e.id);if(typeof r=="number")return Sr.sum;if(Object.prototype.toString.call(r)==="[object Date]")return Sr.extent},e.getAggregationFn=()=>{var n,r;if(!e)throw new Error;return lr(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(n=(r=t.options.aggregationFns)==null?void 0:r[e.columnDef.aggregationFn])!=null?n:Sr[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var n,r;e.setGrouping(t?[]:(n=(r=e.initialState)==null?void 0:r.grouping)!=null?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];const r=t.getColumn(n);return r!=null&&r.columnDef.getGroupingValue?(e._groupingValuesCache[n]=r.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,t,n,r)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var o;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((o=n.subRows)!=null&&o.length)}}};function Xg(e,t,n){if(!(t!=null&&t.length)||!n)return e;const r=e.filter(i=>!t.includes(i.id));return n==="remove"?r:[...t.map(i=>e.find(s=>s.id===i)).filter(Boolean),...r]}const Jg={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:Te("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=O(n=>[rn(t,n)],n=>n.findIndex(r=>r.id===e.id),L(t.options,"debugColumns")),e.getIsFirstColumn=n=>{var r;return((r=rn(t,n)[0])==null?void 0:r.id)===e.id},e.getIsLastColumn=n=>{var r;const o=rn(t,n);return((r=o[o.length-1])==null?void 0:r.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var n;e.setColumnOrder(t?[]:(n=e.initialState.columnOrder)!=null?n:[])},e._getOrderColumnsFn=O(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(t,n,r)=>o=>{let i=[];if(!(t!=null&&t.length))i=o;else{const s=[...t],a=[...o];for(;a.length&&s.length;){const l=s.shift(),c=a.findIndex(f=>f.id===l);c>-1&&i.push(a.splice(c,1)[0])}i=[...i,...a]}return Xg(i,n,r)},L(e.options,"debugTable"))}},br=()=>({left:[],right:[]}),Qg={getInitialState:e=>({columnPinning:br(),...e}),getDefaultOptions:e=>({onColumnPinningChange:Te("columnPinning",e)}),createColumn:(e,t)=>{e.pin=n=>{const r=e.getLeafColumns().map(o=>o.id).filter(Boolean);t.setColumnPinning(o=>{var i,s;if(n==="right"){var a,l;return{left:((a=o==null?void 0:o.left)!=null?a:[]).filter(p=>!(r!=null&&r.includes(p))),right:[...((l=o==null?void 0:o.right)!=null?l:[]).filter(p=>!(r!=null&&r.includes(p))),...r]}}if(n==="left"){var c,f;return{left:[...((c=o==null?void 0:o.left)!=null?c:[]).filter(p=>!(r!=null&&r.includes(p))),...r],right:((f=o==null?void 0:o.right)!=null?f:[]).filter(p=>!(r!=null&&r.includes(p)))}}return{left:((i=o==null?void 0:o.left)!=null?i:[]).filter(p=>!(r!=null&&r.includes(p))),right:((s=o==null?void 0:o.right)!=null?s:[]).filter(p=>!(r!=null&&r.includes(p)))}})},e.getCanPin=()=>e.getLeafColumns().some(r=>{var o,i,s;return((o=r.columnDef.enablePinning)!=null?o:!0)&&((i=(s=t.options.enableColumnPinning)!=null?s:t.options.enablePinning)!=null?i:!0)}),e.getIsPinned=()=>{const n=e.getLeafColumns().map(a=>a.id),{left:r,right:o}=t.getState().columnPinning,i=n.some(a=>r==null?void 0:r.includes(a)),s=n.some(a=>o==null?void 0:o.includes(a));return i?"left":s?"right":!1},e.getPinnedIndex=()=>{var n,r;const o=e.getIsPinned();return o?(n=(r=t.getState().columnPinning)==null||(r=r[o])==null?void 0:r.indexOf(e.id))!=null?n:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=O(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(n,r,o)=>{const i=[...r??[],...o??[]];return n.filter(s=>!i.includes(s.column.id))},L(t.options,"debugRows")),e.getLeftVisibleCells=O(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left],(n,r)=>(r??[]).map(i=>n.find(s=>s.column.id===i)).filter(Boolean).map(i=>({...i,position:"left"})),L(t.options,"debugRows")),e.getRightVisibleCells=O(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(n,r)=>(r??[]).map(i=>n.find(s=>s.column.id===i)).filter(Boolean).map(i=>({...i,position:"right"})),L(t.options,"debugRows"))},createTable:e=>{e.setColumnPinning=t=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var n,r;return e.setColumnPinning(t?br():(n=(r=e.initialState)==null?void 0:r.columnPinning)!=null?n:br())},e.getIsSomeColumnsPinned=t=>{var n;const r=e.getState().columnPinning;if(!t){var o,i;return!!((o=r.left)!=null&&o.length||(i=r.right)!=null&&i.length)}return!!((n=r[t])!=null&&n.length)},e.getLeftLeafColumns=O(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(t,n)=>(n??[]).map(r=>t.find(o=>o.id===r)).filter(Boolean),L(e.options,"debugColumns")),e.getRightLeafColumns=O(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(t,n)=>(n??[]).map(r=>t.find(o=>o.id===r)).filter(Boolean),L(e.options,"debugColumns")),e.getCenterLeafColumns=O(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r)=>{const o=[...n??[],...r??[]];return t.filter(i=>!o.includes(i.id))},L(e.options,"debugColumns"))}};function em(e){return e||(typeof document<"u"?document:null)}const Pn={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},xr=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),tm={getDefaultColumnDef:()=>Pn,getInitialState:e=>({columnSizing:{},columnSizingInfo:xr(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:Te("columnSizing",e),onColumnSizingInfoChange:Te("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var n,r,o;const i=t.getState().columnSizing[e.id];return Math.min(Math.max((n=e.columnDef.minSize)!=null?n:Pn.minSize,(r=i??e.columnDef.size)!=null?r:Pn.size),(o=e.columnDef.maxSize)!=null?o:Pn.maxSize)},e.getStart=O(n=>[n,rn(t,n),t.getState().columnSizing],(n,r)=>r.slice(0,e.getIndex(n)).reduce((o,i)=>o+i.getSize(),0),L(t.options,"debugColumns")),e.getAfter=O(n=>[n,rn(t,n),t.getState().columnSizing],(n,r)=>r.slice(e.getIndex(n)+1).reduce((o,i)=>o+i.getSize(),0),L(t.options,"debugColumns")),e.resetSize=()=>{t.setColumnSizing(n=>{let{[e.id]:r,...o}=n;return o})},e.getCanResize=()=>{var n,r;return((n=e.columnDef.enableResizing)!=null?n:!0)&&((r=t.options.enableColumnResizing)!=null?r:!0)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let n=0;const r=o=>{if(o.subHeaders.length)o.subHeaders.forEach(r);else{var i;n+=(i=o.column.getSize())!=null?i:0}};return r(e),n},e.getStart=()=>{if(e.index>0){const n=e.headerGroup.headers[e.index-1];return n.getStart()+n.getSize()}return 0},e.getResizeHandler=n=>{const r=t.getColumn(e.column.id),o=r==null?void 0:r.getCanResize();return i=>{if(!r||!o||(i.persist==null||i.persist(),Cr(i)&&i.touches&&i.touches.length>1))return;const s=e.getSize(),a=e?e.getLeafHeaders().map(S=>[S.column.id,S.column.getSize()]):[[r.id,r.getSize()]],l=Cr(i)?Math.round(i.touches[0].clientX):i.clientX,c={},f=(S,x)=>{typeof x=="number"&&(t.setColumnSizingInfo(C=>{var _,F;const E=t.options.columnResizeDirection==="rtl"?-1:1,$=(x-((_=C==null?void 0:C.startOffset)!=null?_:0))*E,V=Math.max($/((F=C==null?void 0:C.startSize)!=null?F:0),-.999999);return C.columnSizingStart.forEach(z=>{let[D,P]=z;c[D]=Math.round(Math.max(P+P*V,0)*100)/100}),{...C,deltaOffset:$,deltaPercentage:V}}),(t.options.columnResizeMode==="onChange"||S==="end")&&t.setColumnSizing(C=>({...C,...c})))},p=S=>f("move",S),g=S=>{f("end",S),t.setColumnSizingInfo(x=>({...x,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},m=em(n),h={moveHandler:S=>p(S.clientX),upHandler:S=>{m==null||m.removeEventListener("mousemove",h.moveHandler),m==null||m.removeEventListener("mouseup",h.upHandler),g(S.clientX)}},v={moveHandler:S=>(S.cancelable&&(S.preventDefault(),S.stopPropagation()),p(S.touches[0].clientX),!1),upHandler:S=>{var x;m==null||m.removeEventListener("touchmove",v.moveHandler),m==null||m.removeEventListener("touchend",v.upHandler),S.cancelable&&(S.preventDefault(),S.stopPropagation()),g((x=S.touches[0])==null?void 0:x.clientX)}},y=nm()?{passive:!1}:!1;Cr(i)?(m==null||m.addEventListener("touchmove",v.moveHandler,y),m==null||m.addEventListener("touchend",v.upHandler,y)):(m==null||m.addEventListener("mousemove",h.moveHandler,y),m==null||m.addEventListener("mouseup",h.upHandler,y)),t.setColumnSizingInfo(S=>({...S,startOffset:l,startSize:s,deltaOffset:0,deltaPercentage:0,columnSizingStart:a,isResizingColumn:r.id}))}}},createTable:e=>{e.setColumnSizing=t=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var n;e.setColumnSizing(t?{}:(n=e.initialState.columnSizing)!=null?n:{})},e.resetHeaderSizeInfo=t=>{var n;e.setColumnSizingInfo(t?xr():(n=e.initialState.columnSizingInfo)!=null?n:xr())},e.getTotalSize=()=>{var t,n;return(t=(n=e.getHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0},e.getLeftTotalSize=()=>{var t,n;return(t=(n=e.getLeftHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0},e.getCenterTotalSize=()=>{var t,n;return(t=(n=e.getCenterHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0},e.getRightTotalSize=()=>{var t,n;return(t=(n=e.getRightHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0}}};let $n=null;function nm(){if(typeof $n=="boolean")return $n;let e=!1;try{const t={get passive(){return e=!0,!1}},n=()=>{};window.addEventListener("test",n,t),window.removeEventListener("test",n)}catch{e=!1}return $n=e,$n}function Cr(e){return e.type==="touchstart"}const rm={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:Te("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=n=>{e.getCanHide()&&t.setColumnVisibility(r=>({...r,[e.id]:n??!e.getIsVisible()}))},e.getIsVisible=()=>{var n,r;const o=e.columns;return(n=o.length?o.some(i=>i.getIsVisible()):(r=t.getState().columnVisibility)==null?void 0:r[e.id])!=null?n:!0},e.getCanHide=()=>{var n,r;return((n=e.columnDef.enableHiding)!=null?n:!0)&&((r=t.options.enableHiding)!=null?r:!0)},e.getToggleVisibilityHandler=()=>n=>{e.toggleVisibility==null||e.toggleVisibility(n.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=O(()=>[e.getAllCells(),t.getState().columnVisibility],n=>n.filter(r=>r.column.getIsVisible()),L(t.options,"debugRows")),e.getVisibleCells=O(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(n,r,o)=>[...n,...r,...o],L(t.options,"debugRows"))},createTable:e=>{const t=(n,r)=>O(()=>[r(),r().filter(o=>o.getIsVisible()).map(o=>o.id).join("_")],o=>o.filter(i=>i.getIsVisible==null?void 0:i.getIsVisible()),L(e.options,"debugColumns"));e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=n=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(n),e.resetColumnVisibility=n=>{var r;e.setColumnVisibility(n?{}:(r=e.initialState.columnVisibility)!=null?r:{})},e.toggleAllColumnsVisible=n=>{var r;n=(r=n)!=null?r:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((o,i)=>({...o,[i.id]:n||!(i.getCanHide!=null&&i.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(n=>!(n.getIsVisible!=null&&n.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(n=>n.getIsVisible==null?void 0:n.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>n=>{var r;e.toggleAllColumnsVisible((r=n.target)==null?void 0:r.checked)}}};function rn(e,t){return t?t==="center"?e.getCenterVisibleLeafColumns():t==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const om={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},im={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:Te("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n;const r=(n=e.getCoreRowModel().flatRows[0])==null||(n=n._getAllCellsByColumnId()[t.id])==null?void 0:n.getValue();return typeof r=="string"||typeof r=="number"}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var n,r,o,i;return((n=e.columnDef.enableGlobalFilter)!=null?n:!0)&&((r=t.options.enableGlobalFilter)!=null?r:!0)&&((o=t.options.enableFilters)!=null?o:!0)&&((i=t.options.getColumnCanGlobalFilter==null?void 0:t.options.getColumnCanGlobalFilter(e))!=null?i:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>it.includesString,e.getGlobalFilterFn=()=>{var t,n;const{globalFilterFn:r}=e.options;return lr(r)?r:r==="auto"?e.getGlobalAutoFilterFn():(t=(n=e.options.filterFns)==null?void 0:n[r])!=null?t:it[r]},e.setGlobalFilter=t=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},sm={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:Te("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;e._autoResetExpanded=()=>{var r,o;if(!t){e._queue(()=>{t=!0});return}if((r=(o=e.options.autoResetAll)!=null?o:e.options.autoResetExpanded)!=null?r:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=r=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(r),e.toggleAllRowsExpanded=r=>{r??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=r=>{var o,i;e.setExpanded(r?{}:(o=(i=e.initialState)==null?void 0:i.expanded)!=null?o:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(r=>r.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>r=>{r.persist==null||r.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const r=e.getState().expanded;return r===!0||Object.values(r).some(Boolean)},e.getIsAllRowsExpanded=()=>{const r=e.getState().expanded;return typeof r=="boolean"?r===!0:!(!Object.keys(r).length||e.getRowModel().flatRows.some(o=>!o.getIsExpanded()))},e.getExpandedDepth=()=>{let r=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(i=>{const s=i.split(".");r=Math.max(r,s.length)}),r},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,t)=>{e.toggleExpanded=n=>{t.setExpanded(r=>{var o;const i=r===!0?!0:!!(r!=null&&r[e.id]);let s={};if(r===!0?Object.keys(t.getRowModel().rowsById).forEach(a=>{s[a]=!0}):s=r,n=(o=n)!=null?o:!i,!i&&n)return{...s,[e.id]:!0};if(i&&!n){const{[e.id]:a,...l}=s;return l}return r})},e.getIsExpanded=()=>{var n;const r=t.getState().expanded;return!!((n=t.options.getIsRowExpanded==null?void 0:t.options.getIsRowExpanded(e))!=null?n:r===!0||r!=null&&r[e.id])},e.getCanExpand=()=>{var n,r,o;return(n=t.options.getRowCanExpand==null?void 0:t.options.getRowCanExpand(e))!=null?n:((r=t.options.enableExpanding)!=null?r:!0)&&!!((o=e.subRows)!=null&&o.length)},e.getIsAllParentsExpanded=()=>{let n=!0,r=e;for(;n&&r.parentId;)r=t.getRow(r.parentId,!0),n=r.getIsExpanded();return n},e.getToggleExpandedHandler=()=>{const n=e.getCanExpand();return()=>{n&&e.toggleExpanded()}}}},Lr=0,Hr=10,wr=()=>({pageIndex:Lr,pageSize:Hr}),am={getInitialState:e=>({...e,pagination:{...wr(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:Te("pagination",e)}),createTable:e=>{let t=!1,n=!1;e._autoResetPageIndex=()=>{var r,o;if(!t){e._queue(()=>{t=!0});return}if((r=(o=e.options.autoResetAll)!=null?o:e.options.autoResetPageIndex)!=null?r:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=r=>{const o=i=>pt(r,i);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(o)},e.resetPagination=r=>{var o;e.setPagination(r?wr():(o=e.initialState.pagination)!=null?o:wr())},e.setPageIndex=r=>{e.setPagination(o=>{let i=pt(r,o.pageIndex);const s=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return i=Math.max(0,Math.min(i,s)),{...o,pageIndex:i}})},e.resetPageIndex=r=>{var o,i;e.setPageIndex(r?Lr:(o=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageIndex)!=null?o:Lr)},e.resetPageSize=r=>{var o,i;e.setPageSize(r?Hr:(o=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageSize)!=null?o:Hr)},e.setPageSize=r=>{e.setPagination(o=>{const i=Math.max(1,pt(r,o.pageSize)),s=o.pageSize*o.pageIndex,a=Math.floor(s/i);return{...o,pageIndex:a,pageSize:i}})},e.setPageCount=r=>e.setPagination(o=>{var i;let s=pt(r,(i=e.options.pageCount)!=null?i:-1);return typeof s=="number"&&(s=Math.max(-1,s)),{...o,pageCount:s}}),e.getPageOptions=O(()=>[e.getPageCount()],r=>{let o=[];return r&&r>0&&(o=[...new Array(r)].fill(null).map((i,s)=>s)),o},L(e.options,"debugTable")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:r}=e.getState().pagination,o=e.getPageCount();return o===-1?!0:o===0?!1:r<o-1},e.previousPage=()=>e.setPageIndex(r=>r-1),e.nextPage=()=>e.setPageIndex(r=>r+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var r;return(r=e.options.pageCount)!=null?r:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var r;return(r=e.options.rowCount)!=null?r:e.getPrePaginationRowModel().rows.length}}},_r=()=>({top:[],bottom:[]}),lm={getInitialState:e=>({rowPinning:_r(),...e}),getDefaultOptions:e=>({onRowPinningChange:Te("rowPinning",e)}),createRow:(e,t)=>{e.pin=(n,r,o)=>{const i=r?e.getLeafRows().map(l=>{let{id:c}=l;return c}):[],s=o?e.getParentRows().map(l=>{let{id:c}=l;return c}):[],a=new Set([...s,e.id,...i]);t.setRowPinning(l=>{var c,f;if(n==="bottom"){var p,g;return{top:((p=l==null?void 0:l.top)!=null?p:[]).filter(v=>!(a!=null&&a.has(v))),bottom:[...((g=l==null?void 0:l.bottom)!=null?g:[]).filter(v=>!(a!=null&&a.has(v))),...Array.from(a)]}}if(n==="top"){var m,h;return{top:[...((m=l==null?void 0:l.top)!=null?m:[]).filter(v=>!(a!=null&&a.has(v))),...Array.from(a)],bottom:((h=l==null?void 0:l.bottom)!=null?h:[]).filter(v=>!(a!=null&&a.has(v)))}}return{top:((c=l==null?void 0:l.top)!=null?c:[]).filter(v=>!(a!=null&&a.has(v))),bottom:((f=l==null?void 0:l.bottom)!=null?f:[]).filter(v=>!(a!=null&&a.has(v)))}})},e.getCanPin=()=>{var n;const{enableRowPinning:r,enablePinning:o}=t.options;return typeof r=="function"?r(e):(n=r??o)!=null?n:!0},e.getIsPinned=()=>{const n=[e.id],{top:r,bottom:o}=t.getState().rowPinning,i=n.some(a=>r==null?void 0:r.includes(a)),s=n.some(a=>o==null?void 0:o.includes(a));return i?"top":s?"bottom":!1},e.getPinnedIndex=()=>{var n,r;const o=e.getIsPinned();if(!o)return-1;const i=(n=o==="top"?t.getTopRows():t.getBottomRows())==null?void 0:n.map(s=>{let{id:a}=s;return a});return(r=i==null?void 0:i.indexOf(e.id))!=null?r:-1}},createTable:e=>{e.setRowPinning=t=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var n,r;return e.setRowPinning(t?_r():(n=(r=e.initialState)==null?void 0:r.rowPinning)!=null?n:_r())},e.getIsSomeRowsPinned=t=>{var n;const r=e.getState().rowPinning;if(!t){var o,i;return!!((o=r.top)!=null&&o.length||(i=r.bottom)!=null&&i.length)}return!!((n=r[t])!=null&&n.length)},e._getPinnedRows=(t,n,r)=>{var o;return((o=e.options.keepPinnedRows)==null||o?(n??[]).map(s=>{const a=e.getRow(s,!0);return a.getIsAllParentsExpanded()?a:null}):(n??[]).map(s=>t.find(a=>a.id===s))).filter(Boolean).map(s=>({...s,position:r}))},e.getTopRows=O(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(t,n)=>e._getPinnedRows(t,n,"top"),L(e.options,"debugRows")),e.getBottomRows=O(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(t,n)=>e._getPinnedRows(t,n,"bottom"),L(e.options,"debugRows")),e.getCenterRows=O(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(t,n,r)=>{const o=new Set([...n??[],...r??[]]);return t.filter(i=>!o.has(i.id))},L(e.options,"debugRows"))}},um={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:Te("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var n;return e.setRowSelection(t?{}:(n=e.initialState.rowSelection)!=null?n:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(n=>{t=typeof t<"u"?t:!e.getIsAllRowsSelected();const r={...n},o=e.getPreGroupedRowModel().flatRows;return t?o.forEach(i=>{i.getCanSelect()&&(r[i.id]=!0)}):o.forEach(i=>{delete r[i.id]}),r})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(n=>{const r=typeof t<"u"?t:!e.getIsAllPageRowsSelected(),o={...n};return e.getRowModel().rows.forEach(i=>{zr(o,i.id,r,!0,e)}),o}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=O(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?Rr(e,n):{rows:[],flatRows:[],rowsById:{}},L(e.options,"debugTable")),e.getFilteredSelectedRowModel=O(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?Rr(e,n):{rows:[],flatRows:[],rowsById:{}},L(e.options,"debugTable")),e.getGroupedSelectedRowModel=O(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?Rr(e,n):{rows:[],flatRows:[],rowsById:{}},L(e.options,"debugTable")),e.getIsAllRowsSelected=()=>{const t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState();let r=!!(t.length&&Object.keys(n).length);return r&&t.some(o=>o.getCanSelect()&&!n[o.id])&&(r=!1),r},e.getIsAllPageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows.filter(o=>o.getCanSelect()),{rowSelection:n}=e.getState();let r=!!t.length;return r&&t.some(o=>!n[o.id])&&(r=!1),r},e.getIsSomeRowsSelected=()=>{var t;const n=Object.keys((t=e.getState().rowSelection)!=null?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:t.filter(n=>n.getCanSelect()).some(n=>n.getIsSelected()||n.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(n,r)=>{const o=e.getIsSelected();t.setRowSelection(i=>{var s;if(n=typeof n<"u"?n:!o,e.getCanSelect()&&o===n)return i;const a={...i};return zr(a,e.id,n,(s=r==null?void 0:r.selectChildren)!=null?s:!0,t),a})},e.getIsSelected=()=>{const{rowSelection:n}=t.getState();return yo(e,n)},e.getIsSomeSelected=()=>{const{rowSelection:n}=t.getState();return Nr(e,n)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:n}=t.getState();return Nr(e,n)==="all"},e.getCanSelect=()=>{var n;return typeof t.options.enableRowSelection=="function"?t.options.enableRowSelection(e):(n=t.options.enableRowSelection)!=null?n:!0},e.getCanSelectSubRows=()=>{var n;return typeof t.options.enableSubRowSelection=="function"?t.options.enableSubRowSelection(e):(n=t.options.enableSubRowSelection)!=null?n:!0},e.getCanMultiSelect=()=>{var n;return typeof t.options.enableMultiRowSelection=="function"?t.options.enableMultiRowSelection(e):(n=t.options.enableMultiRowSelection)!=null?n:!0},e.getToggleSelectedHandler=()=>{const n=e.getCanSelect();return r=>{var o;n&&e.toggleSelected((o=r.target)==null?void 0:o.checked)}}}},zr=(e,t,n,r,o)=>{var i;const s=o.getRow(t,!0);n?(s.getCanMultiSelect()||Object.keys(e).forEach(a=>delete e[a]),s.getCanSelect()&&(e[t]=!0)):delete e[t],r&&(i=s.subRows)!=null&&i.length&&s.getCanSelectSubRows()&&s.subRows.forEach(a=>zr(e,a.id,n,r,o))};function Rr(e,t){const n=e.getState().rowSelection,r=[],o={},i=function(s,a){return s.map(l=>{var c;const f=yo(l,n);if(f&&(r.push(l),o[l.id]=l),(c=l.subRows)!=null&&c.length&&(l={...l,subRows:i(l.subRows)}),f)return l}).filter(Boolean)};return{rows:i(t.rows),flatRows:r,rowsById:o}}function yo(e,t){var n;return(n=t[e.id])!=null?n:!1}function Nr(e,t,n){var r;if(!((r=e.subRows)!=null&&r.length))return!1;let o=!0,i=!1;return e.subRows.forEach(s=>{if(!(i&&!o)&&(s.getCanSelect()&&(yo(s,t)?i=!0:o=!1),s.subRows&&s.subRows.length)){const a=Nr(s,t);a==="all"?i=!0:(a==="some"&&(i=!0),o=!1)}}),o?"all":i?"some":!1}const Br=/([0-9]+)/gm,cm=(e,t,n)=>ga(yt(e.getValue(n)).toLowerCase(),yt(t.getValue(n)).toLowerCase()),dm=(e,t,n)=>ga(yt(e.getValue(n)),yt(t.getValue(n))),fm=(e,t,n)=>So(yt(e.getValue(n)).toLowerCase(),yt(t.getValue(n)).toLowerCase()),gm=(e,t,n)=>So(yt(e.getValue(n)),yt(t.getValue(n))),mm=(e,t,n)=>{const r=e.getValue(n),o=t.getValue(n);return r>o?1:r<o?-1:0},pm=(e,t,n)=>So(e.getValue(n),t.getValue(n));function So(e,t){return e===t?0:e>t?1:-1}function yt(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function ga(e,t){const n=e.split(Br).filter(Boolean),r=t.split(Br).filter(Boolean);for(;n.length&&r.length;){const o=n.shift(),i=r.shift(),s=parseInt(o,10),a=parseInt(i,10),l=[s,a].sort();if(isNaN(l[0])){if(o>i)return 1;if(i>o)return-1;continue}if(isNaN(l[1]))return isNaN(s)?-1:1;if(s>a)return 1;if(a>s)return-1}return n.length-r.length}const en={alphanumeric:cm,alphanumericCaseSensitive:dm,text:fm,textCaseSensitive:gm,datetime:mm,basic:pm},hm={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:Te("sorting",e),isMultiSortEvent:t=>t.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{const n=t.getFilteredRowModel().flatRows.slice(10);let r=!1;for(const o of n){const i=o==null?void 0:o.getValue(e.id);if(Object.prototype.toString.call(i)==="[object Date]")return en.datetime;if(typeof i=="string"&&(r=!0,i.split(Br).length>1))return en.alphanumeric}return r?en.text:en.basic},e.getAutoSortDir=()=>{const n=t.getFilteredRowModel().flatRows[0];return typeof(n==null?void 0:n.getValue(e.id))=="string"?"asc":"desc"},e.getSortingFn=()=>{var n,r;if(!e)throw new Error;return lr(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(n=(r=t.options.sortingFns)==null?void 0:r[e.columnDef.sortingFn])!=null?n:en[e.columnDef.sortingFn]},e.toggleSorting=(n,r)=>{const o=e.getNextSortingOrder(),i=typeof n<"u"&&n!==null;t.setSorting(s=>{const a=s==null?void 0:s.find(m=>m.id===e.id),l=s==null?void 0:s.findIndex(m=>m.id===e.id);let c=[],f,p=i?n:o==="desc";if(s!=null&&s.length&&e.getCanMultiSort()&&r?a?f="toggle":f="add":s!=null&&s.length&&l!==s.length-1?f="replace":a?f="toggle":f="replace",f==="toggle"&&(i||o||(f="remove")),f==="add"){var g;c=[...s,{id:e.id,desc:p}],c.splice(0,c.length-((g=t.options.maxMultiSortColCount)!=null?g:Number.MAX_SAFE_INTEGER))}else f==="toggle"?c=s.map(m=>m.id===e.id?{...m,desc:p}:m):f==="remove"?c=s.filter(m=>m.id!==e.id):c=[{id:e.id,desc:p}];return c})},e.getFirstSortDir=()=>{var n,r;return((n=(r=e.columnDef.sortDescFirst)!=null?r:t.options.sortDescFirst)!=null?n:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=n=>{var r,o;const i=e.getFirstSortDir(),s=e.getIsSorted();return s?s!==i&&((r=t.options.enableSortingRemoval)==null||r)&&(!(n&&(o=t.options.enableMultiRemove)!=null)||o)?!1:s==="desc"?"asc":"desc":i},e.getCanSort=()=>{var n,r;return((n=e.columnDef.enableSorting)!=null?n:!0)&&((r=t.options.enableSorting)!=null?r:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,r;return(n=(r=e.columnDef.enableMultiSort)!=null?r:t.options.enableMultiSort)!=null?n:!!e.accessorFn},e.getIsSorted=()=>{var n;const r=(n=t.getState().sorting)==null?void 0:n.find(o=>o.id===e.id);return r?r.desc?"desc":"asc":!1},e.getSortIndex=()=>{var n,r;return(n=(r=t.getState().sorting)==null?void 0:r.findIndex(o=>o.id===e.id))!=null?n:-1},e.clearSorting=()=>{t.setSorting(n=>n!=null&&n.length?n.filter(r=>r.id!==e.id):[])},e.getToggleSortingHandler=()=>{const n=e.getCanSort();return r=>{n&&(r.persist==null||r.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?t.options.isMultiSortEvent==null?void 0:t.options.isMultiSortEvent(r):!1))}}},createTable:e=>{e.setSorting=t=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var n,r;e.setSorting(t?[]:(n=(r=e.initialState)==null?void 0:r.sorting)!=null?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},vm=[Dg,rm,Jg,Qg,Lg,Hg,om,im,hm,Yg,sm,am,lm,um,tm];function ym(e){var t,n;const r=[...vm,...(t=e._features)!=null?t:[]];let o={_features:r};const i=o._features.reduce((g,m)=>Object.assign(g,m.getDefaultOptions==null?void 0:m.getDefaultOptions(o)),{}),s=g=>o.options.mergeOptions?o.options.mergeOptions(i,g):{...i,...g};let l={...{},...(n=e.initialState)!=null?n:{}};o._features.forEach(g=>{var m;l=(m=g.getInitialState==null?void 0:g.getInitialState(l))!=null?m:l});const c=[];let f=!1;const p={_features:r,options:{...i,...e},initialState:l,_queue:g=>{c.push(g),f||(f=!0,Promise.resolve().then(()=>{for(;c.length;)c.shift()();f=!1}).catch(m=>setTimeout(()=>{throw m})))},reset:()=>{o.setState(o.initialState)},setOptions:g=>{const m=pt(g,o.options);o.options=s(m)},getState:()=>o.options.state,setState:g=>{o.options.onStateChange==null||o.options.onStateChange(g)},_getRowId:(g,m,h)=>{var v;return(v=o.options.getRowId==null?void 0:o.options.getRowId(g,m,h))!=null?v:`${h?[h.id,m].join("."):m}`},getCoreRowModel:()=>(o._getCoreRowModel||(o._getCoreRowModel=o.options.getCoreRowModel(o)),o._getCoreRowModel()),getRowModel:()=>o.getPaginationRowModel(),getRow:(g,m)=>{let h=(m?o.getPrePaginationRowModel():o.getRowModel()).rowsById[g];if(!h&&(h=o.getCoreRowModel().rowsById[g],!h))throw new Error;return h},_getDefaultColumnDef:O(()=>[o.options.defaultColumn],g=>{var m;return g=(m=g)!=null?m:{},{header:h=>{const v=h.header.column.columnDef;return v.accessorKey?v.accessorKey:v.accessorFn?v.id:null},cell:h=>{var v,y;return(v=(y=h.renderValue())==null||y.toString==null?void 0:y.toString())!=null?v:null},...o._features.reduce((h,v)=>Object.assign(h,v.getDefaultColumnDef==null?void 0:v.getDefaultColumnDef()),{}),...g}},L(e,"debugColumns")),_getColumnDefs:()=>o.options.columns,getAllColumns:O(()=>[o._getColumnDefs()],g=>{const m=function(h,v,y){return y===void 0&&(y=0),h.map(S=>{const x=Tg(o,S,y,v),C=S;return x.columns=C.columns?m(C.columns,x,y+1):[],x})};return m(g)},L(e,"debugColumns")),getAllFlatColumns:O(()=>[o.getAllColumns()],g=>g.flatMap(m=>m.getFlatColumns()),L(e,"debugColumns")),_getAllFlatColumnsById:O(()=>[o.getAllFlatColumns()],g=>g.reduce((m,h)=>(m[h.id]=h,m),{}),L(e,"debugColumns")),getAllLeafColumns:O(()=>[o.getAllColumns(),o._getOrderColumnsFn()],(g,m)=>{let h=g.flatMap(v=>v.getLeafColumns());return m(h)},L(e,"debugColumns")),getColumn:g=>o._getAllFlatColumnsById()[g]};Object.assign(o,p);for(let g=0;g<o._features.length;g++){const m=o._features[g];m==null||m.createTable==null||m.createTable(o)}return o}function Sm(){return e=>O(()=>[e.options.data],t=>{const n={rows:[],flatRows:[],rowsById:{}},r=function(o,i,s){i===void 0&&(i=0);const a=[];for(let c=0;c<o.length;c++){const f=Og(e,e._getRowId(o[c],c,s),o[c],c,i,void 0,s==null?void 0:s.id);if(n.flatRows.push(f),n.rowsById[f.id]=f,a.push(f),e.options.getSubRows){var l;f.originalSubRows=e.options.getSubRows(o[c],c),(l=f.originalSubRows)!=null&&l.length&&(f.subRows=r(f.originalSubRows,i+1,f))}}return a};return n.rows=r(t),n},L(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function bm(e){const t=[],n=r=>{var o;t.push(r),(o=r.subRows)!=null&&o.length&&r.getIsExpanded()&&r.subRows.forEach(n)};return e.rows.forEach(n),{rows:t,flatRows:e.flatRows,rowsById:e.rowsById}}function xm(e){return t=>O(()=>[t.getState().pagination,t.getPrePaginationRowModel(),t.options.paginateExpandedRows?void 0:t.getState().expanded],(n,r)=>{if(!r.rows.length)return r;const{pageSize:o,pageIndex:i}=n;let{rows:s,flatRows:a,rowsById:l}=r;const c=o*i,f=c+o;s=s.slice(c,f);let p;t.options.paginateExpandedRows?p={rows:s,flatRows:a,rowsById:l}:p=bm({rows:s,flatRows:a,rowsById:l}),p.flatRows=[];const g=m=>{p.flatRows.push(m),m.subRows.length&&m.subRows.forEach(g)};return p.rows.forEach(g),p},L(t.options,"debugTable"))}function Cm(){return e=>O(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,n)=>{if(!n.rows.length||!(t!=null&&t.length))return n;const r=e.getState().sorting,o=[],i=r.filter(l=>{var c;return(c=e.getColumn(l.id))==null?void 0:c.getCanSort()}),s={};i.forEach(l=>{const c=e.getColumn(l.id);c&&(s[l.id]={sortUndefined:c.columnDef.sortUndefined,invertSorting:c.columnDef.invertSorting,sortingFn:c.getSortingFn()})});const a=l=>{const c=l.map(f=>({...f}));return c.sort((f,p)=>{for(let m=0;m<i.length;m+=1){var g;const h=i[m],v=s[h.id],y=v.sortUndefined,S=(g=h==null?void 0:h.desc)!=null?g:!1;let x=0;if(y){const C=f.getValue(h.id),_=p.getValue(h.id),F=C===void 0,E=_===void 0;if(F||E){if(y==="first")return F?-1:1;if(y==="last")return F?1:-1;x=F&&E?0:F?y:-y}}if(x===0&&(x=v.sortingFn(f,p,h.id)),x!==0)return S&&(x*=-1),v.invertSorting&&(x*=-1),x}return f.index-p.index}),c.forEach(f=>{var p;o.push(f),(p=f.subRows)!=null&&p.length&&(f.subRows=a(f.subRows))}),c};return{rows:a(n.rows),flatRows:o,rowsById:n.rowsById}},L(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function ma(e,t){return e?wm(e)?u.createElement(e,t):e:null}function wm(e){return _m(e)||typeof e=="function"||Rm(e)}function _m(e){return typeof e=="function"&&(()=>{const t=Object.getPrototypeOf(e);return t.prototype&&t.prototype.isReactComponent})()}function Rm(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function Em(e){const t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=u.useState(()=>({current:ym(t)})),[r,o]=u.useState(()=>n.current.initialState);return n.current.setOptions(i=>({...i,...e,state:{...r,...e.state},onStateChange:s=>{o(s),e.onStateChange==null||e.onStateChange(s)}})),n.current}const pi=({row:e,rowStyle:{hoveredRowBg:t},onRowClick:n})=>{const r=n?n(e.original):void 0;return d.jsx(cs,{_hover:{backgroundColor:t},onClick:r,children:e.getVisibleCells().map(o=>{var i,s,a,l,c,f,p,g,m;return d.jsx(ju,{px:1,textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",minWidth:((i=o.column.columnDef.meta)==null?void 0:i.customMinWidth)??void 0,maxWidth:((s=o.column.columnDef.meta)==null?void 0:s.customMaxWidth)??void 0,width:(a=o.column.columnDef.meta)==null?void 0:a.customWidth,textAlign:(l=o.column.columnDef.meta)!=null&&l.isCentered?"center":void 0,fontFamily:(c=o.column.columnDef.meta)!=null&&c.isMonospace?"Inter, SFMono-Regular, Menlo, Monaco, Consolas, monospace":void 0,onClick:(f=o.column.columnDef.meta)!=null&&f.stopPropagation||o.column.id==="actions"&&r?h=>{h.stopPropagation()}:void 0,cursor:!((p=o.column.columnDef.meta)!=null&&p.stopPropagation)&&o.column.id!=="actions"&&r?"pointer":void 0,border:"0.5px solid gray",style:(m=(g=o.column.columnDef.meta)==null?void 0:g.rowContentOptions)==null?void 0:m.style,children:ma(o.column.columnDef.cell,o.getContext())},o.id)})},e.id)},Mm=({sortInfo:e,canSort:t})=>t?e?e==="desc"?d.jsx(ht,{ml:1,boxSize:3,as:Bu}):d.jsx(ht,{ml:1,boxSize:3,as:Zu}):d.jsx(ht,{ml:1,boxSize:3,as:qu}):null,hi=({headerGroup:e})=>d.jsx(cs,{p:0,children:e.headers.map(t=>{var n,r,o,i,s,a;return d.jsx(Iu,{color:"gray.400",colSpan:t.colSpan,minWidth:((n=t.column.columnDef.meta)==null?void 0:n.customMinWidth)??void 0,maxWidth:((r=t.column.columnDef.meta)==null?void 0:r.customMaxWidth)??void 0,width:(o=t.column.columnDef.meta)==null?void 0:o.customWidth,fontSize:"sm",onClick:t.column.getCanSort()?t.column.getToggleSortingHandler():void 0,cursor:t.column.getCanSort()?"pointer":void 0,border:"0.5px solid gray",px:1,children:d.jsxs(Mt,{display:"flex",alignItems:"center",children:[t.isPlaceholder?null:d.jsx(qe,{label:(s=(i=t.column.columnDef.meta)==null?void 0:i.headerOptions)==null?void 0:s.tooltip,children:d.jsx(lt,{overflow:"hidden",whiteSpace:"nowrap",alignContent:"center",width:"100%",...(a=t.column.columnDef.meta)==null?void 0:a.headerStyleProps,children:ma(t.column.columnDef.header,t.getContext())})}),d.jsx(Mm,{sortInfo:t.column.getIsSorted(),canSort:t.column.getCanSort()})]})},t.id)})}),vi=(e,t)=>{const n=1/t;return Math.round(e*n)/n},Pm=({precision:e})=>{const t=u.useRef(null),[n,r]=u.useState({width:0,height:0});return u.useEffect(()=>{const o=()=>{var s,a;return{width:t&&((s=t.current)==null?void 0:s.offsetWidth)||0,height:t&&((a=t.current)==null?void 0:a.offsetHeight)||0}},i=()=>{const{width:s,height:a}=o();{const l={width:s,height:a};l.width=vi(l.width,e),l.height=vi(l.height,e),(l.width!==n.width||l.height!==n.height)&&r(l)}};return t.current&&i(),window.addEventListener("resize",i),()=>{window.removeEventListener("resize",i)}},[t,n]),u.useMemo(()=>({dimensions:n,ref:t}),[n.height,n.width])},yi=({table:e,isDisabled:t})=>{const{t:n}=pe(),{ref:r,dimensions:o}=Pm({precision:100}),i=o.width!==0&&o.width<=800;return d.jsxs(Mt,{ref:r,justifyContent:"space-between",m:4,alignItems:"center",children:[d.jsxs(Mt,{children:[d.jsx(qe,{label:n("table.first_page"),children:d.jsx(st,{"aria-label":"Go to first page",onClick:()=>e.setPageIndex(0),isDisabled:t||!e.getCanPreviousPage(),icon:d.jsx(ec,{h:3,w:3}),mr:4})}),d.jsx(qe,{label:n("table.previous_page"),children:d.jsx(st,{"aria-label":"Previous page",onClick:()=>e.previousPage(),isDisabled:t||!e.getCanPreviousPage(),icon:d.jsx(Xu,{h:6,w:6})})})]}),d.jsxs(Mt,{alignItems:"center",children:[i?null:d.jsxs(d.Fragment,{children:[d.jsxs(zt,{flexShrink:0,mr:8,children:[n("table.page")," ",d.jsx(zt,{fontWeight:"bold",as:"span",children:e.getState().pagination.pageIndex+1})," ",n("common.of")," ",d.jsx(zt,{fontWeight:"bold",as:"span",children:e.getPageCount()})]}),d.jsx(zt,{flexShrink:0,children:n("table.go_to_page")})," ",d.jsxs(es,{ml:2,mr:8,w:28,min:1,max:e.getPageCount(),onChange:(s,a)=>{const l=a?a-1:0;e.setPageIndex(l)},value:e.getState().pagination.pageIndex+1,children:[d.jsx(ns,{}),d.jsxs(ts,{children:[d.jsx(is,{}),d.jsx(os,{})]})]})]}),d.jsx(as,{w:32,value:e.getState().pagination.pageSize,onChange:s=>{e.setPageSize(Number(s.target.value))},children:[10,20,30,40,50].map(s=>d.jsxs("option",{value:s,children:[n("common.show")," ",s]},an()))})]}),d.jsxs(Mt,{children:[d.jsx(qe,{label:n("table.next_page"),children:d.jsx(st,{"aria-label":"Go to next page",onClick:()=>e.nextPage(),isDisabled:t||!e.getCanNextPage(),icon:d.jsx(Ju,{h:6,w:6})})}),d.jsx(qe,{label:n("table.last_page"),children:d.jsx(st,{"aria-label":"Go to last page",onClick:()=>e.setPageIndex(e.getPageCount()-1),isDisabled:t||!e.getCanNextPage(),icon:d.jsx(Qu,{h:3,w:3}),ml:4})})]})]})},$m=({draggableId:e,index:t,column:n})=>{var l,c,f,p;const{t:r}=pe(),o=Gt("blue.100","blue.600"),i=Gt("gray.50","gray.700");let s=((l=n.header)==null?void 0:l.toString())??"Unrecognized column";(f=(c=n.meta)==null?void 0:c.columnSelectorOptions)!=null&&f.label&&(s=n.meta.columnSelectorOptions.label);const a=()=>{var g,m;return(g=n.meta)!=null&&g.anchored?r("table.drag_locked"):(m=n.meta)!=null&&m.alwaysShow?r("table.drag_always_show"):r("table.drag_explanation")};return d.jsx(nl,{draggableId:e,index:t,isDragDisabled:(p=n.meta)==null?void 0:p.anchored,children:(g,m)=>{var h,v;return d.jsx(qe,{label:a(),children:d.jsxs(lt,{ref:g.innerRef,...g.draggableProps,...g.dragHandleProps,display:"flex",backgroundColor:m.isDragging?o:i,px:6,py:2,my:2,borderRadius:15,cursor:(h=n.meta)!=null&&h.anchored?"not-allowed":void 0,children:[d.jsx(ht,{as:(v=n.meta)!=null&&v.anchored?Ku:Wu,boxSize:5,ml:.5,mr:2,my:"auto"}),d.jsx(zt,{my:"auto",children:s})]})})}})},Fm=({items:e,columns:t,droppableId:n,isDropDisabled:r})=>{const o=Gt("gray.200","gray.600"),i=Gt("blue.300","blue.500");return d.jsx(rl,{droppableId:n,direction:"vertical",isCombineEnabled:!1,isDropDisabled:r,children:(s,a)=>d.jsxs(lt,{ref:s.innerRef,backgroundColor:a.isDraggingOver?i:o,padding:2,borderRadius:15,children:[e.map((l,c)=>{const f=t.find(p=>p.id===l);return f?d.jsx($m,{draggableId:l,index:c,column:f},l):null}),s.placeholder]})})},Si=u.memo(Fm),km=(e,t,n)=>{const r=Array.from(e),[o]=r.splice(t,1);return o&&r.splice(n,0,o),r},bi=(e,t)=>{const n=[...t];for(const r of e)n.includes(r.id)||n.push(r.id);return n},jm=({controller:e,shownColumns:t,hiddenColumns:n})=>{var m;const{t:r}=pe(),[o,i]=u.useState(bi(t,e.columnOrder)),[s,a]=u.useState(n.map(h=>h.id)),[l,c]=u.useState(),f=u.useCallback(h=>{const v=t.find(({id:y})=>y===h.draggableId)??n.find(({id:y})=>y===h.draggableId);c(v)},[t,n]),p=u.useMemo(()=>{var v;let h=0;for(const[y,S]of t.entries())(v=S.meta)!=null&&v.anchored&&(h=y);return h+1},[t]),g=u.useCallback(h=>{const{source:v,destination:y,draggableId:S}=h;if(y!==null){if(v.droppableId===y.droppableId){const x=km(o,v.index,Math.max(y.index,p));y.droppableId==="displayed-columns"?(e.setColumnOrder(x),i(x)):a(x)}else if(v.droppableId==="displayed-columns"){const x=e.hideColumn(S);x&&(a([...x.hiddenColumns]),i([...x.columnOrder]))}else if(v.droppableId==="hidden-columns"){const x=Array.from(o);x.splice(Math.max(y.index,p),0,S);const C=e.unhideColumn(S,x);C&&(a(C.hiddenColumns),i([...C.columnOrder]),a([...C.hiddenColumns]))}c(void 0)}},[t,n,e.hideColumn,e.unhideColumn,p]);return u.useEffect(()=>{console.log("Table settings ID changed, resetting TableDragDrop state:",e.tableSettingsId);const h=bi(t,e.columnOrder);i(h);const v=n.map(y=>y.id);a(v)},[e.tableSettingsId,t,n]),d.jsxs(d.Fragment,{children:[d.jsx(Ue,{size:"md",children:r("table.columns")}),d.jsx(ol,{onDragStart:f,onDragEnd:g,children:d.jsxs(Mt,{mt:4,children:[d.jsxs(lt,{w:"50%",mr:2,children:[d.jsxs(Ue,{size:"sm",mb:4,children:["Visible (",o.length,")"]}),d.jsx(Si,{droppableId:"displayed-columns",items:o,columns:t})]}),d.jsxs(lt,{ml:2,w:"50%",children:[d.jsxs(Ue,{size:"sm",mb:4,children:["Hidden (",n.length,")"]}),d.jsx(Si,{droppableId:"hidden-columns",items:s,columns:n,isDropDisabled:(m=l==null?void 0:l.meta)==null?void 0:m.alwaysShow})]})]})})]})},Im=({controller:e,columns:t})=>{const{t:n}=pe(),r=Oa();return d.jsxs(d.Fragment,{children:[d.jsx(qe,{label:n("table.preferences"),children:d.jsx(st,{"aria-label":n("table.preferences"),icon:d.jsx(Yu,{weight:"bold"}),onClick:r.onOpen})}),d.jsx(La,{title:n("table.preferences"),topRightButtons:d.jsx(qe,{label:n("table.reset"),children:d.jsx(st,{"aria-label":n("table.reset"),icon:d.jsx(Uu,{size:20}),onClick:e.resetPreferences})}),options:{modalSize:"md",maxWidth:{sm:"600px",md:"600px",lg:"600px",xl:"600px"}},...r,children:d.jsx(lt,{w:"100%",children:d.jsx(jm,{shownColumns:t.filter(o=>e.columnVisibility[o.id]!==!1),hiddenColumns:t.filter(o=>e.columnVisibility[o.id]===!1),controller:e})})})]})},xi=u.memo(Im),Ci=({isLoading:e,children:t})=>{const{colorMode:n}=ki(),r={top:"0px",right:"0px",position:"absolute",width:"100%",height:"100%",backgroundColor:n==="light"?"var(--chakra-colors-gray-200)":"var(--chakra-colors-gray-900)",zIndex:1100,opacity:"0.4",filter:"alpha(opacity=40)",textAlign:"center",verticalAlign:"middle",borderRadius:"5px"};return d.jsxs(d.Fragment,{children:[e&&d.jsx("div",{style:r,children:d.jsx(ji,{position:"absolute",top:"45%",size:"xl"})}),t]})},Am=({innerTableKey:e,controller:t,columns:n,header:r,data:o=[],options:i={},isLoading:s=!1})=>{const{t:a}=pe(),l=Gt("gray.700","white"),c=Gt("gray.100","gray.600"),f=Ie.useMemo(()=>i.isFullScreen?{base:"calc(100vh - 360px)",md:"calc(100vh - 288px)"}:i.minimumHeight??"300px",[i.isFullScreen,i.minimumHeight]),p=Ie.useMemo(()=>i.onRowClick,[i.onRowClick]),g=Ie.useMemo(()=>({pageIndex:t.pageInfo.pageIndex,pageSize:t.pageInfo.pageSize}),[t.pageInfo.pageIndex,t.pageInfo.pageSize]),m=Ie.useMemo(()=>i.isManual&&i.count?Math.ceil(i.count/g.pageSize):Math.ceil(((o==null?void 0:o.length)??0)/g.pageSize),[i.count,i.isManual,o==null?void 0:o.length,g.pageSize]),h=Ie.useMemo(()=>({pageCount:m>0?m:1,initialState:{sorting:t.sortBy,pagination:g},manualPagination:i.isManual,manualSorting:i.isManual,autoResetPageIndex:!1}),[i.isManual,t.sortBy,m]),v=Ie.useMemo(()=>{const S=t.columnOrder.filter(x=>n.find(C=>C.id===x));if(S.length!==n.length)for(const x of n)S.includes(x.id)||S.push(x.id);return n.slice().sort((x,C)=>S.indexOf(x.id)-S.indexOf(C.id))},[n,t.columnOrder]),y=Em({getCoreRowModel:Sm(),getPaginationRowModel:xm(),getSortedRowModel:Cm(),data:o,columns:v,state:{sorting:t.sortBy,columnVisibility:t.columnVisibility,pagination:g},onSortingChange:t.setSortBy,onPaginationChange:t.onPaginationChange,...h});return Ie.useEffect(()=>{i.isManual&&!s&&o&&g.pageIndex>0&&i.count!==void 0&&Math.ceil(i.count/g.pageSize)-1<g.pageIndex&&t.onPaginationChange({pageIndex:0,pageSize:g.pageSize})},[i.count,s,g,o]),s&&!i.showAsCard&&o.length===0?d.jsx(tn,{children:d.jsx(ji,{size:"xl"})}):i.showAsCard?d.jsxs(Ii,{children:[d.jsxs(Ai,{children:[typeof r.title=="string"?d.jsx(Ue,{size:"md",my:"auto",mr:2,children:r.title}):r.title,r.leftContent,d.jsx(Mr,{}),d.jsxs(xo,{spacing:2,children:[r.otherButtons,r.addButton,i.hideTablePreferences?null:d.jsx(xi,{controller:t,columns:n}),i.refetch?d.jsx(Oo,{onClick:i.refetch,isCompact:!0,isFetching:s}):null]})]}),d.jsxs(Vi,{display:"flex",flexDirection:"column",children:[d.jsx(Ci,{isLoading:s,children:d.jsxs(Ao,{minH:f,children:[d.jsxs(jr,{size:"small",variant:"simple",textColor:l,w:"100%",fontSize:"14px",children:[d.jsx(To,{children:y.getHeaderGroups().map(S=>d.jsx(hi,{headerGroup:S},S.id))}),d.jsx(Vo,{children:y.getRowModel().rows.map(S=>d.jsx(pi,{row:S,onRowClick:p,rowStyle:{hoveredRowBg:c}},S.id))})]},e),(o==null?void 0:o.length)===0?d.jsx(tn,{mt:8,children:d.jsx(Ue,{size:"md",children:r.objectListed?a("common.no_obj_found",{obj:r.objectListed}):a("common.empty_list")})}):null]})}),i.isHidingControls?null:d.jsx(yi,{table:y,isDisabled:s})]})]}):d.jsxs(lt,{w:"100%",children:[d.jsxs(Mt,{mb:2,hidden:i.hideTableTitleRow,children:[d.jsx(Ue,{size:"md",my:"auto",mr:2,children:r.title}),r.leftContent,d.jsx(Mr,{}),d.jsxs(xo,{spacing:2,children:[r.otherButtons,r.addButton,i.hideTablePreferences?null:d.jsx(xi,{controller:t,columns:n}),i.refetch?d.jsx(Oo,{onClick:i.refetch,isCompact:!0,isFetching:s}):null]})]}),d.jsx(Ci,{isLoading:s,children:d.jsxs(Ao,{minH:f,children:[d.jsxs(jr,{size:"small",variant:"simple",textColor:l,w:"100%",fontSize:"14px",children:[d.jsx(To,{children:y.getHeaderGroups().map(S=>d.jsx(hi,{headerGroup:S},S.id))}),d.jsx(Vo,{children:y.getRowModel().rows.map(S=>d.jsx(pi,{row:S,onRowClick:p,rowStyle:{hoveredRowBg:c}},S.id))})]},e),(o==null?void 0:o.length)===0?d.jsx(tn,{mt:8,children:d.jsx(Ue,{size:"md",children:r.objectListed?a("common.no_obj_found",{obj:r.objectListed}):a("common.empty_list")})}):null]})}),i.isHidingControls?null:d.jsx(yi,{table:y,isDisabled:s})]})},Vm=(e,t)=>d.jsx(on,{size:"sm",colorScheme:"blue",onClick:()=>t(e),w:"100%",children:e.length}),wi=(e,t)=>e!==void 0?`${e}${t?`${t}`:""}`:d.jsx(tn,{children:"-"}),Tm=({channelInfo:{channel:e,devices:t}})=>{const{t:n}=pe(),{colorMode:r}=ki(),[o,i]=Ie.useState(),s=il({tableSettingsId:"wifiscan.devices.table",defaultOrder:["ssid","signal","actions"],defaultSortBy:[{desc:!1,id:"ssid"}]}),a=Ie.useMemo(()=>[{id:"ssid",header:"SSID",footer:"",accessorKey:"ssid",meta:{anchored:!0,alwaysShow:!0}},{id:"signal",header:"Signal",footer:"",accessorKey:"signal",cell:l=>`${l.cell.row.original.signal} db`,meta:{anchored:!0,customWidth:"80px",alwaysShow:!0,rowContentOptions:{style:{textAlign:"right"}}}},{id:"station",header:"UEs",accessorKey:"sta_count",cell:l=>wi(l.cell.row.original.sta_count),meta:{anchored:!0,customWidth:"40px",alwaysShow:!0,rowContentOptions:{style:{textAlign:"right"}}}},{id:"utilization",header:"Ch. Util.",accessorKey:"ch_util",cell:l=>wi(l.cell.row.original.ch_util,"%"),meta:{anchored:!0,customWidth:"60px",alwaysShow:!0,headerOptions:{tooltip:"Channel Utilization (%)"},rowContentOptions:{style:{textAlign:"right"}}}},{id:"ies",header:"Ies",footer:"",accessorKey:"actions",cell:l=>Vm(l.cell.row.original.ies??[],i),meta:{customWidth:"50px",isCentered:!0,alwaysShow:!0}}],[n]);return d.jsxs(Ii,{children:[d.jsxs(Ai,{display:"flex",children:[d.jsxs(Ue,{size:"md",my:"auto",children:[n("commands.channel")," #",e," (",t.length," ",t.length===1?n("devices.one"):n("devices.title"),")"]}),d.jsx(Mr,{}),o&&d.jsx(st,{size:"sm",my:"auto","aria-label":n("common.back"),colorScheme:"blue",icon:d.jsx(Gu,{size:20}),onClick:()=>i(void 0)})]}),d.jsx(Vi,{children:o?d.jsx(lt,{w:"800px",children:o.map(({content:l,name:c,type:f})=>d.jsxs(lt,{my:2,children:[d.jsxs(Ue,{size:"sm",mb:2,textDecor:"underline",children:[c," (",f,")"]}),d.jsx(jg,{rootName:!1,displayDataTypes:!1,enableClipboard:!0,theme:r==="light"?void 0:"dark",value:l,style:{background:"unset",display:"unset"}})]},an()))}):d.jsx(Am,{controller:s,header:{title:"",objectListed:n("devices.title")},columns:a,data:t,options:{count:t.length,onRowClick:l=>()=>i(l.ies??[]),hideTablePreferences:!0,isHidingControls:!0,minimumHeight:"0px",hideTableTitleRow:!0}})})]})},x1=({results:e,setCsvData:t})=>{const{t:n}=pe(),r=u.useMemo(()=>{try{const o={},i=[];for(const s of e.results.status.scan)if(!o[s.channel]){const a={channel:s.channel,devices:[]};for(const l of e.results.status.scan)if(l.channel===s.channel){let c="";const f=Ha(l.signal);l.ssid&&l.ssid.length>0?c=l.ssid:c=l.meshid&&l.meshid.length>0?l.meshid:"N/A",a.devices.push({...l,ssid:c,signal:f}),i.push({...l,ssid:c,signal:f,ies:JSON.stringify(l.ies)})}o[s.channel]=a}return{scanList:Object.keys(o).map(s=>o[s]),listCsv:i}}catch{return}},[e]);return u.useEffect(()=>{r&&t(r.listCsv)},[r]),d.jsxs(d.Fragment,{children:[e.errorCode===1&&d.jsx(Ue,{size:"md",children:d.jsx(za,{colorScheme:"red",children:n("commands.wifiscan_error_1")})}),d.jsxs(Ue,{size:"md",mb:2,children:[n("commands.execution_time"),": ",Math.floor(e.executionTime/1e3),"s"]}),d.jsx(Na,{spacing:4,align:"stretch",children:r==null?void 0:r.scanList.map(o=>d.jsx(Tm,{channelInfo:o},an()))})]})},Dm=({onClick:e,isDisabled:t,isLoading:n,isCompact:r=!0,color:o,label:i,icon:s,...a})=>{const l=Qi();return!r&&l!=="base"&&l!=="sm"?d.jsx(on,{colorScheme:o,type:"button",onClick:e,rightIcon:s,isLoading:n,isDisabled:t,...a,children:i}):d.jsx(qe,{label:i,children:d.jsx(st,{"aria-label":i,colorScheme:o,type:"button",onClick:e,icon:s,isLoading:n,isDisabled:t,...a})})},C1=Ie.memo(Dm),w1=()=>{const[e,t]=u.useState({submitForm:()=>{},isSubmitting:!1,isValid:!0,dirty:!1}),n=u.useCallback(o=>{o&&(e.submitForm!==o.submitForm||e.isSubmitting!==o.isSubmitting||e.isValid!==o.isValid||e.dirty!==o.dirty)&&t(o)},[e]);return u.useMemo(()=>({form:e,formRef:n}),[e])},Om=(e,t)=>async()=>ae.get(`commands?serialNumber=${t}&newest=true&limit=${e}`).then(n=>n.data),_1=({serialNumber:e,limit:t,onError:n})=>Re(["commands",e,{limit:t}],Om(t,e),{keepPreviousData:!0,enabled:e!==void 0&&e!=="",staleTime:3e4,onError:n}),Lm=(e,t,n,r,o)=>ae.get(`commands?serialNumber=${e}&startDate=${t}&endDate=${n}&limit=${r}&offset=${o}`).then(i=>i.data),Hm=(e,t,n)=>async()=>{let r=0;const o=100;let i=[],s;do s=await Lm(e,t,n,o,r),i=i.concat(s.commands),r+=o;while(s.commands.length===o);return{commands:i}},R1=({serialNumber:e,start:t,end:n,onError:r})=>Re(["commands",e,{start:t,end:n}],Hm(e,t,n),{enabled:e!==void 0&&e!==""&&t!==void 0&&n!==void 0,staleTime:1e3*60,onError:r}),zm=async e=>ae.delete(`command/${e}`),E1=()=>{const e=bt();return ft(zm,{onSuccess:()=>{e.invalidateQueries(["commands"])}})},M1=({serialNumber:e,commandId:t})=>Re(["commands",e,t],()=>ae.get(`command/${t}?serialNumber=${e}`).then(n=>n.data),{enabled:e!==void 0&&e!==""&&t!==void 0&&t!==""}),Nm=async e=>ae.post(`device/${e}/eventqueue`,{types:["dhcp-snooping","wifi-frames"],serialNumber:e}).then(t=>t.data),P1=()=>{const e=bt();return ft(Nm,{onSuccess:()=>{e.invalidateQueries(["commands"])}})},Bm=e=>async t=>ae.post(`device/${e}/configure`,{when:0,UUID:1,ForceInitDeviceConfig:!0,serialNumber:e,configuration:t}).then(n=>n.data),$1=({serialNumber:e})=>{const t=bt();return ft(Bm(e),{onSuccess:()=>{t.invalidateQueries(["commands",e]),t.invalidateQueries(["device",e]),t.invalidateQueries(["devices"])}})},Gm=e=>ae.post(`device/${e.serialNumber}/script`,e,{timeout:e.timeout?e.timeout*1e3+10:5*60*1e3}).then(t=>t.data),F1=({serialNumber:e})=>{const t=bt();return ft(Gm,{onSuccess:()=>{t.invalidateQueries(["commands",e])},onError:()=>{t.invalidateQueries(["commands",e])}})},Zm=(e,t)=>ae.get(`file/${t}?serialNumber=${e}`,{responseType:"arraybuffer"}),k1=({serialNumber:e,commandId:t})=>{const{t:n}=pe(),r=qt();return Re(["download-script",e,t],()=>Zm(e,t),{enabled:!1,onSuccess:o=>{var c;const i=new Blob([o.data],{type:"application/octet-stream"}),s=document.createElement("a");s.href=window.URL.createObjectURL(i);const a=o.headers["content-disposition"]??o.headers["content-disposition"],l=((c=a==null?void 0:a.split("filename=")[1])==null?void 0:c.split(",")[0])??`Script_${t}.tar.gz`;s.download=l,s.click()},onError:o=>{var i;if(Ti.isAxiosError(o)){const s=(i=o.response)==null?void 0:i.data;let a="";if(s instanceof ArrayBuffer){const l=new TextDecoder("utf-8");a=JSON.parse(l.decode(s)).ErrorDescription}r({id:`script-download-error-${e}`,title:n("common.error"),description:a,status:"error",duration:5e3,isClosable:!0,position:"top-right"})}}})},Wm={refresh:()=>{},onClose:()=>{},queryToInvalidate:null},qm=({objName:e,operationType:t,refresh:n,onClose:r,queryToInvalidate:o})=>{const{t:i}=pe(),s=qt(),a=bt(),l=()=>t==="update"?i("crud.success_update_obj",{obj:e}):t==="delete"?i("crud.success_delete_obj",{obj:e}):t==="blink"?i("commands.blink_success",{obj:e}):t==="reboot"?i("commands.reboot_success",{obj:e}):i("crud.success_create_obj",{obj:e}),c=m=>{var h,v,y,S,x,C,_,F,E,$;return t==="update"?i("crud.error_update_obj",{obj:e,e:(v=(h=m==null?void 0:m.response)==null?void 0:h.data)==null?void 0:v.ErrorDescription}):(t==="delete"&&i("crud.error_delete_obj",{obj:e,e:(S=(y=m==null?void 0:m.response)==null?void 0:y.data)==null?void 0:S.ErrorDescription}),t==="blink"?i("commands.blink_error",{obj:e,e:(C=(x=m==null?void 0:m.response)==null?void 0:x.data)==null?void 0:C.ErrorDescription}):t==="reboot"?i("commands.reboot_error",{obj:e,e:(F=(_=m==null?void 0:m.response)==null?void 0:_.data)==null?void 0:F.ErrorDescription}):i("crud.error_create_obj",{obj:e,e:($=(E=m==null?void 0:m.response)==null?void 0:E.data)==null?void 0:$.ErrorDescription}))},f=u.useCallback(({setSubmitting:m,resetForm:h}={setSubmitting:null,resetForm:null})=>{n&&n(),m&&m(!1),h&&h(),s({id:`${e}-${t}-success-${an()}`,title:i("common.success"),description:l(),status:"success",duration:5e3,isClosable:!0,position:"top-right"}),r&&r(),o&&a.invalidateQueries(o)},[o]),p=u.useCallback((m,{setSubmitting:h}={setSubmitting:null})=>{s({id:an(),title:i("common.error"),description:c(m),status:"error",duration:5e3,isClosable:!0,position:"top-right"}),h&&h(!1)},[]);return u.useMemo(()=>({onSuccess:f,onError:p}),[])};qm.defaultProps=Wm;const Um=async e=>ae.post(`device/${e.serialNumber}/trace`,{...e,when:e.when??0}),Km=({serialNumber:e,alertOnCompletion:t})=>{const n=bt(),{t:r}=pe();return qt(),ft(Um,{onSuccess:()=>{n.invalidateQueries(["commands",e]),t&&sn.success(`${r("common.success")}: ${r("controller.trace.success",{serialNumber:e})}`,5)}})},Ym=(e,t)=>ae.get(`file/${t}?serialNumber=${e}`,{responseType:"arraybuffer"}),Xm=({serialNumber:e,commandId:t})=>{const{t:n}=pe();return qt(),Re(["download-trace",e,t],()=>Ym(e,t),{enabled:!1,onSuccess:r=>{var l;const o=new Blob([r.data],{type:"application/octet-stream"}),i=document.createElement("a");i.href=window.URL.createObjectURL(o);const s=r.headers["content-disposition"]??r.headers["content-disposition"],a=((l=s==null?void 0:s.split("filename=")[1])==null?void 0:l.split(",")[0])??`Trace_${t}.pcap`;i.download=a,i.click()},onError:r=>{var o;if(Ti.isAxiosError(r)){const i=(o=r.response)==null?void 0:o.data;let s="";if(i instanceof ArrayBuffer){const a=new TextDecoder("utf-8");s=JSON.parse(a.decode(i)).ErrorDescription}sn.error(`${n("common.error")}: ${s}`,5)}}})},j1=({serialNumber:e,modalProps:t})=>{var g;const{t:n}=pe(),[r,o]=u.useState({type:"duration",network:"up",waitForResponse:!0,duration:"20",packets:"100"}),i=Km({serialNumber:e,alertOnCompletion:!r.waitForResponse}),s=Xm({serialNumber:e,commandId:((g=i.data)==null?void 0:g.data.UUID)??""}),a=(m,h)=>{o({...r,[m]:h})},l=(m,h)=>{o({...r,[m]:h})},c=()=>{i.mutate({serialNumber:e,type:r.type,network:r.network,waitForResponse:r.waitForResponse,duration:r.type==="duration"?parseInt(r.duration,10):void 0,packets:r.type==="packets"?parseInt(r.packets,10):void 0}),r.waitForResponse||t.onClose()},f=()=>{s.refetch()},p=d.jsxs("div",{className:"TraceModalCreate",children:[i.isLoading&&d.jsx("div",{style:{textAlign:"center",padding:"100px 0"},children:d.jsx(Wr,{size:"large"})}),!i.isLoading&&!i.data&&!i.error&&d.jsxs(d.Fragment,{children:[d.jsx("div",{children:d.jsx(Di,{message:n("controller.devices.trace_description"),type:"info",showIcon:!0,closable:!0,className:"custom-trace-alert",style:{marginTop:14,marginBottom:32,whiteSpace:"normal"}})}),d.jsxs(Ba,{children:[d.jsx(wn,{xs:24,children:d.jsx(Et.Item,{label:n("common.type"),children:d.jsxs(we,{value:r.type,onChange:m=>a("type",m),style:{width:"100%"},children:[d.jsx(we.Option,{value:"duration",children:n("controller.trace.duration")}),d.jsx(we.Option,{value:"packets",children:n("controller.trace.packets")})]})})}),d.jsx(wn,{xs:24,children:r.type==="duration"?d.jsx(Et.Item,{label:n("controller.trace.duration"),children:d.jsxs(we,{value:r.duration,onChange:m=>a("duration",m),style:{width:"100%"},children:[d.jsx(we.Option,{value:"20",children:"20s"}),d.jsx(we.Option,{value:"40",children:"40s"}),d.jsx(we.Option,{value:"60",children:"60s"}),d.jsx(we.Option,{value:"120",children:"120s"})]})}):d.jsx(Et.Item,{label:n("controller.trace.packets"),children:d.jsxs(we,{value:r.packets,onChange:m=>a("packets",m),style:{width:"100%"},children:[d.jsx(we.Option,{value:"100",children:"100"}),d.jsx(we.Option,{value:"250",children:"250"}),d.jsx(we.Option,{value:"500",children:"500"}),d.jsx(we.Option,{value:"1000",children:"1000"})]})})}),d.jsx(wn,{xs:24,children:d.jsx(Et.Item,{label:n("controller.trace.network"),children:d.jsx(we,{value:r.network,onChange:m=>a("network",m),style:{width:"100%"},children:d.jsx(we.Option,{value:"up",children:n("controller.trace.up")})})})}),d.jsx(wn,{xs:24,children:d.jsx(Et.Item,{label:n("controller.trace.wait"),children:d.jsx(Oi,{size:"large",checked:r.waitForResponse,onChange:m=>l("waitForResponse",m)})})})]})]}),d.jsx("div",{style:{marginTop:16,marginBottom:16},children:i.data?d.jsxs(d.Fragment,{children:[d.jsx(kn,{type:"primary",onClick:f,loading:s.isFetching,style:{marginRight:8},children:n("controller.trace.download")}),d.jsx(kn,{type:"primary",onClick:i.reset,children:n("common.go_back")})]}):d.jsx(kn,{type:"primary",onClick:c,loading:i.isLoading,style:{width:"100px"},children:n("common.start")})})]});return d.jsx(Li,{title:n("controller.devices.trace"),childItems:p,isModalOpen:t.isOpen,onCancel:t.onClose,footer:null,modalClass:"ampcon-middle-modal"})},I1=({modalProps:{isOpen:e,onClose:t},serialNumber:n})=>{const{t:r}=pe(),[o,i]=u.useState(!1),{mutateAsync:s,isLoading:a}=lc({serialNumber:n,onClose:t}),l=()=>{s({keepRedirector:o})};u.useEffect(()=>{e&&i(!1)},[e]);const c=()=>a?d.jsx("div",{style:{textAlign:"center",padding:"30px 0"},children:d.jsx(Wr,{size:"large"})}):d.jsxs(d.Fragment,{children:[d.jsx(Di,{className:"custom-trace-alert",message:r("commands.factory_reset_warning"),type:"info",showIcon:!0,closable:!0,style:{marginTop:14,marginBottom:24}}),d.jsx(Et,{initialValues:{keepRedirector:!1},children:d.jsx(Et.Item,{name:"keepRedirector",label:r("commands.keep_redirector"),valuePropName:"checked",children:d.jsx(Oi,{checked:o,onChange:i})})}),d.jsx("div",{style:{margin:"20px 0 10px"},children:d.jsx(kn,{size:"large",type:"primary",onClick:l,loading:a,children:r("commands.confirm_reset",{serialNumber:n})})})]});return d.jsx(Li,{title:r("commands.factory_reset"),childItems:c(),isModalOpen:e,onCancel:t,footer:null,modalClass:"ampcon-middle-modal"})},A1=({enabled:e,serialNumber:t})=>Re(["get-inventory-tag",t],()=>Ga.get(`inventory/${t}?withExtendedInfo=true`).then(({data:n})=>n),{enabled:e,onError:n=>{console.error("get sn: "+t+" inventory info fail!")}}),V1=()=>{var h;const e=Za(),[t,n]=u.useState(""),[r,o]=u.useState(!1),[i,s]=u.useState([]),[a,l]=u.useState(!1),{data:c}=Wa({pageInfo:{index:0,limit:1e3},enabled:!0,platform:"ALL"}),f=((h=c==null?void 0:c.devicesWithStatus)==null?void 0:h.map(v=>v.serialNumber))||[],p=sl(v=>{if(v.length<2){s([]),l(!1);return}o(!0);const y=f.filter(S=>S.toLowerCase().includes(v.toLowerCase()));s(y),l(y.length>0),o(!1)},300),g=v=>{const y=v.target.value;n(y),p(y)},m=v=>{n(""),s([]),l(!1),e(`/wireless/devices/${v}`)};return d.jsx(qa,{title:`Search serial numbers and radius clients. For radius clients you can either use the client's username (rad:<EMAIL>)
       or use the client's station ID (rad:11:22:33:44:55:66)`,placement:"left",children:d.jsx(Ua,{open:a,dropdownRender:()=>r?d.jsx("div",{style:{background:"#fff",padding:10},children:d.jsx(Wr,{style:{padding:10}})}):d.jsx("div",{style:{background:"#fff"},children:d.jsx(Co,{className:"input-search-hover",dataSource:i,renderItem:v=>d.jsx(Co.Item,{style:{cursor:"pointer",padding:"4px 12px"},onClick:()=>m(v),children:v}),style:{maxHeight:200,overflowY:"auto",minWidth:250}})}),trigger:["click"],children:d.jsx(Ka,{style:{justifyContent:"flex-end",width:"250px"},value:t,onChange:g,placeholder:"Search Serial Numbers",prefix:d.jsx(Ya,{style:{color:"rgba(0,0,0,.25)"}}),allowClear:!0})})})};export{p1 as $,Ut as A,Ul as B,tn as C,eu as D,Ol as E,Xi as F,Kt as G,v1 as H,y1 as I,Ki as J,Yi as K,pc as L,ns as M,es as N,ts as O,is as P,os as Q,C1 as R,Eu as S,b1 as T,zt as U,us as V,x1 as W,k1 as X,F1 as Y,u1 as Z,Ci as _,g1 as a,M1 as a0,jg as a1,R1 as a2,_1 as a3,E1 as a4,f1 as a5,c1 as a6,m1 as b,h1 as c,w1 as d,Gu as e,S1 as f,$1 as g,d1 as h,qm as i,P1 as j,i1 as k,Oo as l,Fu as m,Pu as n,$u as o,Mu as p,as as q,A1 as r,s1 as s,a1 as t,tu as u,l1 as v,V1 as w,I1 as x,j1 as y,Ql as z};
