import{r as a,I as O,_ as Z,p as G,u as ee,a as te,b as y,R as J,j as e,C as N,T as S,B as Y,c as R,M as X,D as ae,i as L,F as oe,d as k,S as P,A as W,L as Q,e as re,f as ne,g as se,h as j}from"./index-CHCmiRmn.js";import{i as V}from"./icon_details-DTb3-MwP.js";import{u as le}from"./HealthChecks-CsGXfWi1.js";var ie={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M689 165.1L308.2 493.5c-10.9 9.4-10.9 27.5 0 37L689 858.9c14.2 12.2 35 1.2 35-18.5V183.6c0-19.7-20.8-30.7-35-18.5z"}}]},name:"caret-left",theme:"outlined"},ce=function(t,i){return a.createElement(O,Z({},t,{ref:i,icon:ie}))},de=a.forwardRef(ce),he={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M715.8 493.5L335 165.1c-14.2-12.2-35-1.2-35 18.5v656.8c0 19.7 20.8 30.7 35 18.5l380.8-328.4c10.9-9.4 10.9-27.6 0-37z"}}]},name:"caret-right",theme:"outlined"},Ae=function(t,i){return a.createElement(O,Z({},t,{ref:i,icon:he}))},ge=a.forwardRef(Ae);const ue=new Map([["bold",a.createElement(a.Fragment,null,a.createElement("path",{d:"M178,36c-20.09,0-37.92,7.93-50,21.56C115.92,43.93,98.09,36,78,36a66.08,66.08,0,0,0-66,66c0,72.34,105.81,130.14,110.31,132.57a12,12,0,0,0,11.38,0C138.19,232.14,244,174.34,244,102A66.08,66.08,0,0,0,178,36Zm-5.49,142.36A328.69,328.69,0,0,1,128,210.16a328.69,328.69,0,0,1-44.51-31.8C61.82,159.77,36,131.42,36,102A42,42,0,0,1,78,60c17.8,0,32.7,9.4,38.89,24.54a12,12,0,0,0,22.22,0C145.3,69.4,160.2,60,178,60a42,42,0,0,1,42,42C220,131.42,194.18,159.77,172.51,178.36Z"}))],["duotone",a.createElement(a.Fragment,null,a.createElement("path",{d:"M232,102c0,66-104,122-104,122S24,168,24,102A54,54,0,0,1,78,48c22.59,0,41.94,12.31,50,32,8.06-19.69,27.41-32,50-32A54,54,0,0,1,232,102Z",opacity:"0.2"}),a.createElement("path",{d:"M178,40c-20.65,0-38.73,8.88-50,23.89C116.73,48.88,98.65,40,78,40a62.07,62.07,0,0,0-62,62c0,70,103.79,126.66,108.21,129a8,8,0,0,0,7.58,0C136.21,228.66,240,172,240,102A62.07,62.07,0,0,0,178,40ZM128,214.8C109.74,204.16,32,155.69,32,102A46.06,46.06,0,0,1,78,56c19.45,0,35.78,10.36,42.6,27a8,8,0,0,0,14.8,0c6.82-16.67,23.15-27,42.6-27a46.06,46.06,0,0,1,46,46C224,155.61,146.24,204.15,128,214.8Z"}))],["fill",a.createElement(a.Fragment,null,a.createElement("path",{d:"M240,102c0,70-103.79,126.66-108.21,129a8,8,0,0,1-7.58,0C119.79,228.66,16,172,16,102A62.07,62.07,0,0,1,78,40c20.65,0,38.73,8.88,50,23.89C139.27,48.88,157.35,40,178,40A62.07,62.07,0,0,1,240,102Z"}))],["light",a.createElement(a.Fragment,null,a.createElement("path",{d:"M178,42c-21,0-39.26,9.47-50,25.34C117.26,51.47,99,42,78,42a60.07,60.07,0,0,0-60,60c0,29.2,18.2,59.59,54.1,90.31a334.68,334.68,0,0,0,53.06,37,6,6,0,0,0,5.68,0,334.68,334.68,0,0,0,53.06-37C219.8,161.59,238,131.2,238,102A60.07,60.07,0,0,0,178,42ZM128,217.11C111.59,207.64,30,157.72,30,102A48.05,48.05,0,0,1,78,54c20.28,0,37.31,10.83,44.45,28.27a6,6,0,0,0,11.1,0C140.69,64.83,157.72,54,178,54a48.05,48.05,0,0,1,48,48C226,157.72,144.41,207.64,128,217.11Z"}))],["regular",a.createElement(a.Fragment,null,a.createElement("path",{d:"M178,40c-20.65,0-38.73,8.88-50,23.89C116.73,48.88,98.65,40,78,40a62.07,62.07,0,0,0-62,62c0,70,103.79,126.66,108.21,129a8,8,0,0,0,7.58,0C136.21,228.66,240,172,240,102A62.07,62.07,0,0,0,178,40ZM128,214.8C109.74,204.16,32,155.69,32,102A46.06,46.06,0,0,1,78,56c19.45,0,35.78,10.36,42.6,27a8,8,0,0,0,14.8,0c6.82-16.67,23.15-27,42.6-27a46.06,46.06,0,0,1,46,46C224,155.61,146.24,204.15,128,214.8Z"}))],["thin",a.createElement(a.Fragment,null,a.createElement("path",{d:"M178,44c-21.44,0-39.92,10.19-50,27.07C117.92,54.19,99.44,44,78,44a58.07,58.07,0,0,0-58,58c0,28.59,18,58.47,53.4,88.79a333.81,333.81,0,0,0,52.7,36.73,4,4,0,0,0,3.8,0,333.81,333.81,0,0,0,52.7-36.73C218,160.47,236,130.59,236,102A58.07,58.07,0,0,0,178,44ZM128,219.42c-14-8-100-59.35-100-117.42A50.06,50.06,0,0,1,78,52c21.11,0,38.85,11.31,46.3,29.51a4,4,0,0,0,7.4,0C139.15,63.31,156.89,52,178,52a50.06,50.06,0,0,1,50,50C228,160,142,211.46,128,219.42Z"}))]]),pe=new Map([["bold",a.createElement(a.Fragment,null,a.createElement("path",{d:"M240.26,186.1,152.81,34.23h0a28.74,28.74,0,0,0-49.62,0L15.74,186.1a27.45,27.45,0,0,0,0,27.71A28.31,28.31,0,0,0,40.55,228h174.9a28.31,28.31,0,0,0,24.79-14.19A27.45,27.45,0,0,0,240.26,186.1Zm-20.8,15.7a4.46,4.46,0,0,1-4,2.2H40.55a4.46,4.46,0,0,1-4-2.2,3.56,3.56,0,0,1,0-3.73L124,46.2a4.77,4.77,0,0,1,8,0l87.44,151.87A3.56,3.56,0,0,1,219.46,201.8ZM116,136V104a12,12,0,0,1,24,0v32a12,12,0,0,1-24,0Zm28,40a16,16,0,1,1-16-16A16,16,0,0,1,144,176Z"}))],["duotone",a.createElement(a.Fragment,null,a.createElement("path",{d:"M215.46,216H40.54C27.92,216,20,202.79,26.13,192.09L113.59,40.22c6.3-11,22.52-11,28.82,0l87.46,151.87C236,202.79,228.08,216,215.46,216Z",opacity:"0.2"}),a.createElement("path",{d:"M236.8,188.09,149.35,36.22h0a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.35,24.35,0,0,0,40.55,224h174.9a24.35,24.35,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM222.93,203.8a8.5,8.5,0,0,1-7.48,4.2H40.55a8.5,8.5,0,0,1-7.48-4.2,7.59,7.59,0,0,1,0-7.72L120.52,44.21a8.75,8.75,0,0,1,15,0l87.45,151.87A7.59,7.59,0,0,1,222.93,203.8ZM120,144V104a8,8,0,0,1,16,0v40a8,8,0,0,1-16,0Zm20,36a12,12,0,1,1-12-12A12,12,0,0,1,140,180Z"}))],["fill",a.createElement(a.Fragment,null,a.createElement("path",{d:"M236.8,188.09,149.35,36.22h0a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.35,24.35,0,0,0,40.55,224h174.9a24.35,24.35,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM120,104a8,8,0,0,1,16,0v40a8,8,0,0,1-16,0Zm8,88a12,12,0,1,1,12-12A12,12,0,0,1,128,192Z"}))],["light",a.createElement(a.Fragment,null,a.createElement("path",{d:"M235.07,189.09,147.61,37.22h0a22.75,22.75,0,0,0-39.22,0L20.93,189.09a21.53,21.53,0,0,0,0,21.72A22.35,22.35,0,0,0,40.55,222h174.9a22.35,22.35,0,0,0,19.6-11.19A21.53,21.53,0,0,0,235.07,189.09ZM224.66,204.8a10.46,10.46,0,0,1-9.21,5.2H40.55a10.46,10.46,0,0,1-9.21-5.2,9.51,9.51,0,0,1,0-9.72L118.79,43.21a10.75,10.75,0,0,1,18.42,0l87.46,151.87A9.51,9.51,0,0,1,224.66,204.8ZM122,144V104a6,6,0,0,1,12,0v40a6,6,0,0,1-12,0Zm16,36a10,10,0,1,1-10-10A10,10,0,0,1,138,180Z"}))],["regular",a.createElement(a.Fragment,null,a.createElement("path",{d:"M236.8,188.09,149.35,36.22h0a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.35,24.35,0,0,0,40.55,224h174.9a24.35,24.35,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM222.93,203.8a8.5,8.5,0,0,1-7.48,4.2H40.55a8.5,8.5,0,0,1-7.48-4.2,7.59,7.59,0,0,1,0-7.72L120.52,44.21a8.75,8.75,0,0,1,15,0l87.45,151.87A7.59,7.59,0,0,1,222.93,203.8ZM120,144V104a8,8,0,0,1,16,0v40a8,8,0,0,1-16,0Zm20,36a12,12,0,1,1-12-12A12,12,0,0,1,140,180Z"}))],["thin",a.createElement(a.Fragment,null,a.createElement("path",{d:"M233.34,190.09,145.88,38.22h0a20.75,20.75,0,0,0-35.76,0L22.66,190.09a19.52,19.52,0,0,0,0,19.71A20.36,20.36,0,0,0,40.54,220H215.46a20.36,20.36,0,0,0,17.86-10.2A19.52,19.52,0,0,0,233.34,190.09ZM226.4,205.8a12.47,12.47,0,0,1-10.94,6.2H40.54a12.47,12.47,0,0,1-10.94-6.2,11.45,11.45,0,0,1,0-11.72L117.05,42.21a12.76,12.76,0,0,1,21.9,0L226.4,194.08A11.45,11.45,0,0,1,226.4,205.8ZM124,144V104a4,4,0,0,1,8,0v40a4,4,0,0,1-8,0Zm12,36a8,8,0,1,1-8-8A8,8,0,0,1,136,180Z"}))]]),K=a.forwardRef((o,t)=>a.createElement(G,{ref:t,...o,weights:ue}));K.displayName="HeartIcon";const z=K,q=a.forwardRef((o,t)=>a.createElement(G,{ref:t,...o,weights:pe}));q.displayName="WarningIcon";const H=q,xe=async()=>{console.log("getDashboard start");try{const o=await te.get("deviceDashboard");return console.log("Fetched controller dashboard data:",o.data),o.data}catch(o){throw console.error("Error fetching controller dashboard:",o),o}console.log("getDashboard end")},me=()=>ee(["controller","dashboard"],xe,{keepPreviousData:!0,refetchInterval:3e4}),$="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAoxJREFUOE+NlVtIVFEUhv9/jyM4VDRnjg9FaERkBEovToLQgziQFJJUIyRRb2WvKQ3dnCDIaOih62tCRQzdKEjwGD4JTkIhPaRSYFpBMJfIVBids3LG23g8Z2w/Hfb+17f/tddZexN2Ixot1rZ7DytBI4BqCLbmZMRPAIMm8To5lnqFYDBtDad1QhswjihIJ8idtpstTYp8EcXzCX/gRb5uBRiNurQyb6ci2gqCLIumIJIcT4UQDGYWklgc2kBvRFHO2cDiAgwR4gawF+AmqyYHrQm0LwNzaRLPLEIRSCgx7b6lb3GXxF1/0qXf/7rFo0UAOW0DPZqsCTwnotFivdz7GcCOfJGQ9xJTE226Z9tLAAcEmAVxJeEP3NBjxocFt6vG1/i31B5q73ublchT646ZDKqU4n5S7q7UAXOJmZTXV+K9SCK0xiXRTD1mPAZwfA0QaHEBJ7Lu8oHmJHS1ESE7IIAnWeDI/Meu/6msUK4nxn5f9pV5P5KotIkZpR7rmQLoWQ9oAjeT00UXfJ652wRaHfRT1AeMSRAbCgFF+CgxnjzlK/d2EWhx1AomsykPA6go6DCdrkCxuw7gg3UyGaEv1vOQ4MlCwowLm10Z3Lcr3qpfDdJFX+xdE2Gu6sf1ztNpXcAmoq+vSC+Z/QRyt4NwNCNzxxSLWgmccT4/GY7PuCtzvazHjIMA3uT3dl5gX7y7v97XUHuWwB0HoGSU61Cquu7t8uWgx4wwgA7bAMEvoZQSVA7AcHxf4Ory5bAoyrbhJSWSXVhzTzo5M8mOpL/+2rwZsQJzMdpgb0CZEgFQVbg4MmQq1Z6srjfydfZOwmGlNdQGKGgk4V96AoT4AcGgZJ+A7n4D4bBp3fQfvszqL6Dbc90AAAAASUVORK5CYII=",ve="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA35JREFUWEftl89vE0cUx7/fWXsNHICqRKKUCqn8CHJQ8QYHQp1QRRw40Au9UnGmFQcEQumBIwcQCDigwh8AVzjQHjilkFhyIY0dlWxrUpCQCjkEqQ2HlF179qExXsdeQoiTOFy6F1uz8/Z9Zua977xHAMjl3a1l4hwgewl8DDBuxpf+kZIIX4DIxQT93U5ynMa5Bh6AWLP0Duf4omDKAro4VHBvEjgkkNtaeDKAN9nnOP+2AmYgn1+rkGiLEZcAHBTglgGYILC+LNj2lZMcb4Xj6Dfv5t2tMeKRCCaYLYz55sx98T5q1cqjAGYnbCb+AaTEbMEVMyGTSnI5Vl/1YfwG5v+cALnR8Y1Uej3IUghnxfEs3d7+YjbY4WJxnS7h0/BduVSyfU9P9HXv/Ds6P1z4OwHu3v/9s1jcKoJY2WAseKnl1ZZ9nZ2T9eP3RkbaLK74C8TqxvkyrTW37EsnJ+rH3wuQzT/sAtX9t1baLACAQJfTvbu++K1JgLEUyPyMEbVATgXBq+vR1YdzzC4oteJbgucBsWq2Ik7G6SgsBsDEQBwi00Lr657U9oHZYmCo8GcfRf8EchWANzbmWSyAQI4SPAdgjYEIGDvQm2ofqocYLBR7lJTvVJ1PCaSf4LUFA9SCCoAJOlqJz5XwzhsIXM04ye8btjTv/gjiOwBTAeWAaO9JJSir9tFje28QGkMDYX5D42x+LCVQhylyJdOZfNoAMOJuEvIYEdwIzztq31QMtFqU5rUDrYT4H2BeO2C03RxDqP1Do243AxyhLRe/7OioRHj4ZEfcTSD6FdSNvc72rBmP2jcVhNE0VGrVjprIzJaGhT8uAHIyFKsgmH64qDQ0KRdKsQAnKHKmIjJGDRX39+xM5hp1oDL/l1CshDxN4OKChageAKCuavuUEnUw3OJolgyOju2pidWMzeKluOqoRMVjdkLdnKse8L3gGwnkSu0eWPhdUOwC9ZJcx5Bgd8bZ8aCpIBzIjW60E7Fi9XKZsW22HhCZ9r1ye7Qqmlca3ht2PyHLG5SydEigxXs2Vz1gMVEryYJAWyKx59FqyHxrXgAtlOL6ovQDl+UfojEZ/LWwTSXsYqUxCVszAD8zbh+3/+NkOr35pSmklvgIODz8eLW/Utqk5F+utWZVGpNuy9ucmsrJ83dXuiEDwYR9FoJuUtYtR3sunv9D757Uo9eOQ4QFS5MHSwAAAABJRU5ErkJggg==",fe="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA4tJREFUWEftV89vVFUU/s69d95AhVJmpgkixsQgblhAmLZjUJNGpjXUDWw1rtWwIBJSFyxZQCDAgqh/gGxhoSW206SKNs6UGl24KRISErGLDrQUGOj7cY85fX3t4wGlU2aqC+9mJvfdc893z/3Od88hAGgtD72RIpwk0FsMZAlIyXyjBwMegasMlD1G/2yh508KndNVAjY12uFy+zFw12PuoGxl6CKBDjDoW4/tEfXwwdRM94GZZoBpG7nUZte/1G6Iziqgj8GXKFcpTQLY4jLvkJA0w3FyT4m6Q3QN4EnKVkqu3Llfu7+5WSdPApBImJYN0/OcyFVKLAuqXUVai9Mv+BC/Vv4vCyBTHtmmyNviw3gRuEDTrXv57urTwG4cH8npgF+JvmmyDlt38k5h/1/J9dHBnwkg8+PgqyqtJkBY/7gxzz6a87fff3f/VHx+w5XL7evS5jpArYn1NWi7vZp/X7i2OJ4LIDs63EGGx548ab0AALDNVwu9v9YFoG10aJcx9FtkxEDAwFF3zvsmefpojUTBSac+IuAUATqa933ePbO35/dVAwgVTNSRa9Dqg2p+38jTOJAbH+5GYL8DqGXJBnhhAEHAnyhNJ0O15Box9U4Vij/HQbSXS28z8eCC87s24H6t6WtZsyoAS6QChHTplH4dSg0KCAa+ut1V/CwOIFspfUnApyKxsLZ3zgtuhKQM7ZPX9lwSiqGAkN/IWHihUvSh9dzzM3v7bsYBtI0OvKZSziHr8YXovpP2dXGg2aK0ogg0E8T/AFYUAdF2uYZI+zPlUoEIH3vGnJnd0z3P8GgICbVx+q3iC9MdPaMyn7Svi4TJNFzX4uyMicwTaZgpD59WxEcisXpUc/94oTSMS7EFPlfg4yIy4sAyvXenUCw/HoGhXdrQD5FYWdAxBZxZtRDFAcg7INouImMV90UhTmZJ9pfBrphYzds0BIBsItpumQ75Rl9crh4wfnBQEZ+PV9arkuLs2HAHcWOeYybqvN2572pdJMyUL29TZCbCe4+PeusB4Yz/ZrIqWlEa5sa/fxk+bfUDChbf9sC/tVw9YLRZLMmMZg3DfyerIdlrRQCaKMVLRel/oSxf88Zk408DO9KOM7HQmIStmQUGfGMOa/anpvPF2TDzGjpo83ipNSDTbnz/3GJrJmgcxxn7N5pT13U757uhEETqBAEFBuXWoj13Xe+Le+/0XfsHyY10Pa6QQ/sAAAAASUVORK5CYII=",{Title:we,Paragraph:Ce}=R,b=({chart:o,title:t,explanation:i})=>{const{t:r}=y(),[s,n]=J.useState(!1),[l,g]=J.useState(!1),[c,d]=J.useState(!1),h=()=>n(!0),A=()=>n(!1),u=p=>typeof o=="function"?o(p):o;return a.useEffect(()=>{s&&setTimeout(()=>{window.dispatchEvent(new Event("resize"))},100)},[s]),e.jsxs(e.Fragment,{children:[e.jsx(N,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:8},children:[e.jsx(we,{level:5,style:{margin:0},children:t}),e.jsx(S,{title:i,children:e.jsx("img",{src:c?$:V,style:{marginLeft:-4,width:10,height:10},onMouseEnter:()=>d(!0),onMouseLeave:()=>d(!1)})})]}),extra:e.jsx(S,{children:e.jsx(Y,{type:"text",icon:e.jsx("img",{src:l?fe:ve,style:{width:20,height:20}}),onClick:h,style:{backgroundColor:"transparent"},onMouseEnter:p=>{g(!0),p.currentTarget.style.backgroundColor="transparent"},onMouseLeave:p=>{g(!1),p.currentTarget.style.backgroundColor="transparent"}})}),style:{width:"100%",height:"100%"},bordered:!1,children:u(!1)}),e.jsxs(X,{title:typeof t=="string"?e.jsxs("div",{children:[t,e.jsx(ae,{style:{margin:"8px -24px 24px -24px",width:"680px"}})]}):t,open:s,onCancel:A,footer:null,width:680,styles:{content:{height:450},body:{display:"flex",flexDirection:"column"}},children:[e.jsx(Ce,{type:"secondary",style:{marginBottom:16},children:i}),e.jsx("div",{children:u(!0)})]})]})},ye=({seriesData:o,name:t,chartType:i="pie",colorList:r=[],height:s="28vh",maxWidth:n="450px",onClicked:l=null,showPercent:g=!0,legendDirection:c})=>{const d=a.useRef(null),h=a.useMemo(()=>{switch(i){case"ring":return["45%","65%"];case"pie":default:return["0%","70%"]}},[i]),A=a.useMemo(()=>o.reduce((u,p)=>u+p.value,0),[o]);return a.useEffect(()=>{if(!d.current)return;const u=L(d.current),p={tooltip:{trigger:"item",backgroundColor:"rgba(0, 0, 0, 0.6)",textStyle:{color:"#fff"},formatter:m=>{const v=A===0?0:(m.value/A*100).toFixed(2);return`<div style="line-height:1.5">
          <div>${m.name}</div>
          <div>
            <span style="
              display: inline-block;
              width: 10px;
              height: 10px;
              background: ${m.color};
              margin-right: 5px;
              border-radius: 2px;
            "></span>
            ${m.name}: ${m.value} (${v}%)
          </div>
        </div>`}},legend:{orient:c==="horizontal"?"horizontal":"vertical",bottom:c==="horizontal"?10:"auto",top:c==="horizontal"?"auto":"center",left:c==="horizontal"?"center":10,itemGap:10,itemWidth:12,itemHeight:12,formatter(m){const v=m.split(/\s+/);return["Usage","Free","Used","Unused","Normal","Abnormal","Expired"].includes(v[0])?g?`{name|${v[0]}}{count|${v[1]}%}`:`{name|${v[0]}}{count|${v[1]}}`:m}},textStyle:{rich:{name:{color:"#929A9E",lineHeight:20,textAlign:"center",display:"inline-block",width:n==="450px"?65:40},count:{fontSize:14,fontWeight:700,lineHeight:20,textAlign:"center",display:"inline-block",width:10}}},series:[{name:t,type:"pie",center:i==="pie"?c==="horizontal"?["40%","40%"]:["60%","40%"]:c==="horizontal"?["50%","40%"]:["75%","40%"],radius:h,label:{show:i==="ring",position:"center",formatter:()=>g?"":`{value|${A}}
{total|Total}`,rich:{value:{color:"#333",fontSize:n==="450px"?20:16,fontWeight:"bold",lineHeight:30},total:{color:"#333",fontSize:n==="450px"?16:14,lineHeight:20}}},data:o,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};r.length&&(p.color=r),u.setOption(p),l&&u.on("click",l);const w=()=>u.resize();return window.addEventListener("resize",w),()=>{window.removeEventListener("resize",w),u.dispose()}},[o,h,r,t,l,g,n,A,i]),(o==null?void 0:o.length)>0?e.jsx(oe,{justify:"center",align:"center",children:e.jsx("div",{style:{height:s,width:"100%",maxWidth:n},ref:d})}):null},B=a.memo(ye),x=["#14C9BB","#93BEFF","#FC8281","#F8961E","#90BE6D","#FAC858","#2970DC","#F8BC74","#68D290","#F8BE78","#FF8F95","#63B4EC","#CE7D79","#8A33C4"],Ee=({data:o})=>{const{t}=y(),i=a.useMemo(()=>{const r={"2G":0,"5G":0,"6G":0};return o.forEach(({tag:s,value:n})=>{(s==="2G"||s==="5G"||s==="6G")&&(r[s]+=n)}),[{name:"2G",value:r["2G"]},{name:"5G",value:r["5G"]},{name:"6G",value:r["6G"]}]},[o]);return e.jsx(b,{title:t("controller.dashboard.associations"),explanation:t("controller.dashboard.associations_explanation"),chart:r=>e.jsx(B,{seriesData:i,name:t("controller.dashboard.associations"),chartType:"ring",height:r?"300px":"31vh",maxWidth:"100%",showPercent:!0,colorList:[x[0],x[11],x[7]],legendDirection:"horizontal"})})},_=({xAxis:o,yAxisData:t,colorList:i=[],width:r="",onClicked:s,tooltipStyle:n={},title:l="",height:g="300px"})=>{const c=a.useRef(null);return a.useEffect(()=>{if(!c.current)return;const d=L(c.current),h={trigger:"axis",backgroundColor:"rgba(0, 0, 0, 0.6)",textStyle:{color:"#fff"},formatter:p=>{const{name:w,value:m,color:v}=p[0];return`<div style="line-height:1.5">
                  <div>${w}</div>
                  <div>
                    <span style="
                      display: inline-block;
                      width: 10px;
                      height: 10px;
                      background: ${v};
                      margin-right: 5px;
                      border-radius: 2px;
                    "></span>
                    ${w}: ${m}
                  </div>
                </div>`}},A={title:l?{text:l,left:"left",top:24,textStyle:{fontSize:12,fontWeight:400,color:"#212519"}}:void 0,tooltip:n.trigger?n:h,xAxis:{type:"category",data:o,axisLabel:{interval:0,fontSize:12}},yAxis:{type:"value"},grid:{left:"10%",right:"3%",top:l?"14%":"8%",bottom:"10%"},series:[{type:"bar",barMaxWidth:30,barMinWidth:6,data:t,emphasis:{focus:"series",itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}],color:i.length?i:void 0};d.setOption(A),s&&d.on("click",s);const u=()=>{d.resize()};return window.addEventListener("resize",u),()=>{window.removeEventListener("resize",u),d.dispose()}},[o,t,i,r,s,n,l]),e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e.jsx("div",{ref:c,style:{height:g,width:"100%"}})})},{Title:ke,Paragraph:Pe}=R,je=({data:o,itemsPerPage:t=5,height:i="300px"})=>{const[r,s]=a.useState(1),n=Math.ceil(o.length/t),l=a.useMemo(()=>{const h=(r-1)*t,A=h+t;return o.slice(h,A)},[o,r,t]),g=h=>{s(h)},{xAxis:c,yAxisData:d}=a.useMemo(()=>{const h=[],A=[],u=(r-1)*t;for(let p=0;p<l.length;p++){const{tag:w,value:m,color:v}=l[p],I=w==="rtty"?"RTTY":k(w),f=u+p;h.push(I),A.push({value:m,itemStyle:{color:v||x[f%x.length]}})}return{xAxis:h,yAxisData:A}},[l]);return e.jsxs("div",{style:{width:"100%",position:"relative"},children:[e.jsx(_,{title:"",xAxis:c,yAxisData:d,width:"100%",height:i,grid:{bottom:50,containLabel:!0},tooltipStyle:{trigger:"axis",backgroundColor:"rgba(0, 0, 0, 0.6)",textStyle:{color:"#fff"},borderWidth:1,borderColor:h=>{var A;return((A=h[0])==null?void 0:A.color)||"#ccc"},formatter:h=>{const{name:A,value:u,color:p}=h[0],w=o.reduce((v,I)=>v+I.value,0),m=w>0?(u/w*100).toFixed(2):"0";return`<div style="line-height:1.5">
                      <div>${A}</div>
                      <div>
                        <span style="
                          display: inline-block;
                          width: 10px;
                          height: 10px;
                          background: ${p};
                          margin-right: 5px;
                          border-radius: 2px;
                        "></span>
                        ${A}: ${u} (${m}%)
                      </div>
                    </div>`}}}),n>1&&e.jsxs("div",{style:{display:"flex",justifyContent:"flex-end",alignItems:"center",gap:8},children:[e.jsx(de,{style:{fontSize:10,color:r===1?"#ccc":"black",cursor:r===1?"not-allowed":"pointer"},disabled:r===1,onClick:()=>g(r-1)}),e.jsxs("span",{style:{fontSize:10},children:[r," / ",n]}),e.jsx(ge,{style:{fontSize:10,marginRight:5,color:r===n?"#ccc":"black",cursor:r===n?"not-allowed":"pointer"},disabled:r===n,onClick:()=>g(r+1)})]})]})},be=({data:o})=>{const{t}=y(),{xAxis:i,yAxisData:r}=a.useMemo(()=>{const n=[],l=[],g=o.sort((c,d)=>c.tag.localeCompare(d.tag));for(let c=0;c<g.length;c++){const{tag:d,value:h}=g[c],A=d==="rtty"?"RTTY":k(d);n.push(A),l.push({value:h,itemStyle:{color:x[c%x.length]}})}return{xAxis:n,yAxisData:l}},[o]),s=a.useMemo(()=>!o||!Array.isArray(o)?[]:o.map((n,l)=>({tag:n.tag,value:n.value,color:x[l%x.length]})).sort((n,l)=>(n.tag||"").localeCompare(l.tag||"")),[o]);return e.jsx(b,{title:t("controller.dashboard.commands"),explanation:t("controller.dashboard.commands_explanation"),chart:n=>e.jsx(je,{data:s,width:"40%",height:n?"300px":"31vh"})})},Ie=({data:o})=>{const{t}=y(),i=a.useMemo(()=>{let r=0,s=0;return o.status.forEach(n=>{n.tag==="connected"?r=n.value:n.tag==="not connected"&&(s=n.value)}),[{name:t("common.connected"),value:r},{name:t("controller.dashboard.not_connected"),value:s}]},[o,t]);return e.jsx(b,{title:t("controller.dashboard.status"),explanation:t("controller.dashboard.status_explanation"),chart:r=>e.jsx(B,{seriesData:i,name:t("controller.dashboard.status"),chartType:"ring",height:r?"300px":"31vh",maxWidth:"100%",showPercent:!0,colorList:[x[8],x[11]],legendDirection:"horizontal"})})},Me=({data:o})=>{const{t}=y(),i=a.useMemo(()=>{const r=[...o].sort((l,g)=>g.value-l.value),s=[];let n=0;for(let l=0;l<r.length;l++)l<5?s.push({name:r[l].tag,value:r[l].value}):n+=r[l].value;return n>0&&s.push({name:t("controller.dashboard.others"),value:n}),s},[o,t]);return e.jsx(b,{title:t("controller.dashboard.device_types"),explanation:t("controller.dashboard.device_types_explanation"),chart:r=>e.jsx(B,{seriesData:i,name:t("controller.dashboard.device_types"),chartType:"pie",height:r?"300px":"31vh",maxWidth:"100%",showPercent:!0,colorList:x,legendDirection:"vertical"})})},Be=({data:o})=>{const{t}=y(),i=a.useMemo(()=>o.map(({tag:r,value:s})=>({name:r,value:s})),[o]);return e.jsx(b,{title:t("controller.dashboard.memory"),explanation:t("controller.dashboard.memory_explanation"),chart:r=>e.jsx(B,{seriesData:i,name:t("controller.dashboard.memory"),chartType:"ring",height:r?"300px":"31vh",maxWidth:"100%",showPercent:!0,colorList:[x[8],x[11]],legendDirection:"horizontal"})})},De="_statCard_21ll4_1",Je="_statCardContent_21ll4_8",Se="_value_21ll4_15",U={statCard:De,statCardContent:Je,value:Se},{Title:Re}=R,F=({title:o,description:t,value:i,icon:r})=>{const[s,n]=J.useState(!1);return e.jsx(N,{className:U.statCard,bordered:!1,title:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:8},children:[e.jsx(Re,{level:5,style:{margin:0},children:o}),e.jsx(S,{title:t,children:e.jsx("img",{src:s?$:V,style:{marginLeft:-4,width:10,height:10},onMouseEnter:()=>n(!0),onMouseLeave:()=>n(!1)})})]}),children:e.jsxs("div",{className:U.statCardContent,children:[e.jsx("div",{className:U.value,children:i}),e.jsx("div",{children:r})]})})},Ue="data:image/png;base64,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",Fe=({data:o})=>{const{t}=y(),i=a.useMemo(()=>o.reduce((r,s)=>s.tag==="> 75%"?r+s.value:r,0),[o]);return e.jsx(F,{title:"High Memory Devices (>90%)",value:i,description:t("controller.dashboard.memory_explanation"),icon:e.jsx("img",{src:Ue,style:{width:30,height:30}}),color:i===0?["teal.300","teal.300"]:["red.300","red.300"]})},Qe="data:image/png;base64,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",ze=({data:o})=>{const{t}=y(),i=a.useMemo(()=>{const r=o.reduce((c,d)=>{let h=0;return d.tag==="100%"?h=d.value*100:d.tag===">90%"?h=d.value*95:d.tag===">60%"?h=d.value*75:d.tag==="<60%"&&(h=d.value*30),{totalDevices:c.totalDevices+d.value,totalHealth:c.totalHealth+h}},{totalHealth:0,totalDevices:0}),s=r.totalDevices===0?-1:Math.floor(r.totalHealth/r.totalDevices);let n=["green.300","green.300"],l=z;const g=s===-1?"-":`${s}%`;return s===-1?(l=z,n=["gray.300","gray.300"]):s>=80&&s<100?(l=H,n=["yellow.300","yellow.300"]):s<80&&(l=H,n=["red.300","red.300"]),{title:g,color:n,icon:l}},[o]);return e.jsx(F,{title:t("controller.dashboard.overall_health"),value:i.title,description:t("controller.dashboard.overall_health_explanation"),icon:e.jsx("img",{src:Qe,style:{width:30,height:30}}),color:i.color})},{Title:He,Text:Te,Link:Oe}=R,T={"100%":{lowerLimit:100,upperLimit:100,label:"With 100% Health"},">=90%":{lowerLimit:90,upperLimit:99,label:"Between 90% and 99%"},">=60%":{lowerLimit:60,upperLimit:89,label:"Between 60% and 89%"},"<60%":{lowerLimit:0,upperLimit:59,label:"Between 0% and 59%"}},Ze=({data:o})=>{var u,p,w,m,v,I;const{t}=y(),[i,r]=a.useState(!1),[s,n]=a.useState(!1),[l,g]=a.useState({lowerLimit:0,upperLimit:100,label:"Between 0% and 100%"}),c=le(l),d=a.useMemo(()=>{const f=o.reduce((D,C)=>{let M=0;return C.tag==="100%"?M=C.value*100:C.tag===">90%"?M=C.value*95:C.tag===">60%"?M=C.value*75:C.tag==="<60%"&&(M=C.value*30),D.totalHealth+=M,D.totalDevices+=C.value,D[C.tag]=C.value,D},{totalHealth:0,totalDevices:0,"100%":0,">90%":0,">60%":0,"<60%":0}),E=[];return E.push({name:"100%",value:f["100%"]}),E.push({name:">=90%",value:f[">90%"]}),E.push({name:">=60%",value:f[">60%"]}),E.push({name:"<60%",value:f["<60%"]}),E},[o]),h=f=>{const E=f.name;T[E]&&(g(T[E]),r(!0))},A=()=>{c.data&&(ne(c.data.join(",")),n(!0),setTimeout(()=>n(!1),1500))};return e.jsxs(e.Fragment,{children:[e.jsx(b,{title:t("controller.dashboard.overall_health"),explanation:t("controller.dashboard.overall_health_explanation_pie"),chart:f=>e.jsx(B,{seriesData:d,name:t("controller.dashboard.overall_health"),chartType:"ring",height:f?"300px":"31vh",maxWidth:"100%",showPercent:!0,colorList:[x[8],x[11],x[9],x[10]],onClicked:h,legendDirection:"horizontal"})}),e.jsx(X,{open:i,onCancel:()=>r(!1),footer:null,width:500,title:t("controller.dashboard.overall_health"),extra:(u=c.data)!=null&&u.length?e.jsx(S,{title:s?`${t("common.copied")}!`:t("common.copy"),children:e.jsx(Y,{type:"primary",icon:e.jsx(re,{}),onClick:A})}):null,children:c.isFetching?e.jsx(P,{size:"large",style:{display:"block",margin:"40px auto"}}):c.error?e.jsx(W,{message:t("common.error"),description:(m=(w=(p=c.error)==null?void 0:p.response)==null?void 0:w.data)==null?void 0:m.ErrorDescription,type:"error",showIcon:!0}):e.jsxs(e.Fragment,{children:[e.jsxs(He,{level:5,children:[(v=c.data)==null?void 0:v.length," ",t("devices.title")," ",l.label]}),e.jsx(Q,{size:"small",bordered:!0,dataSource:(I=c.data)==null?void 0:I.sort((f,E)=>f.localeCompare(E)),style:{maxHeight:"60vh",overflowY:"auto"},renderItem:f=>e.jsx(Q.Item,{children:e.jsx(Te,{code:!0,children:e.jsx(Oe,{href:`/wireless/devices/${f}#/device/${f}`,children:f})})})})]})})]})},Ge=({data:o})=>{const{t}=y(),{labels:i,values:r}=a.useMemo(()=>{const s={now:0,">hour":0,">day":0,">week":0,">month":0};for(const{tag:n,value:l}of o)n in s&&(s[n]=l);return{labels:["now",">day",">week",">month",">hour"],values:[s.now,s[">day"],s[">week"],s[">month"],s[">hour"]]}},[o]);return e.jsx(b,{title:t("controller.dashboard.uptimes"),explanation:t("controller.dashboard.uptimes_explanation"),chart:s=>e.jsx(_,{xAxis:i,yAxisData:r,colorList:x[0],height:s?"300px":"31vh",width:"45%",tooltipStyle:{trigger:"axis",backgroundColor:"rgba(0, 0, 0, 0.6)",textStyle:{color:"#fff"},formatter:n=>{const{name:l,value:g,color:c}=n[0],d=r.reduce((A,u)=>A+u,0),h=d?(g/d*100).toFixed(2):0;return`<div style="line-height:1.5">
                          <div>${l}</div>
                          <div>
                            <span style="
                              display: inline-block;
                              width: 10px;
                              height: 10px;
                              background: ${c};
                              margin-right: 5px;
                              border-radius: 2px;
                            "></span>
                            ${l}: ${g}(${h}%)
                          </div>
                        </div>`}}})})},Ne="data:image/png;base64,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",We=()=>{var i,r,s,n;const{t:o}=y(),t=me();return console.log("getDashboard.data",t.data),e.jsx(e.Fragment,{children:t?e.jsxs(a.Suspense,{fallback:e.jsx("div",{children:"Loading..."}),children:[t.isLoading&&e.jsx("div",{style:{textAlign:"center",marginTop:100},children:e.jsx(P,{size:"large"})}),t.error&&e.jsx("div",{style:{textAlign:"center",marginTop:100},children:e.jsx(W,{type:"error",message:o("controller.dashboard.error_fetching"),description:((s=(r=(i=t.error)==null?void 0:i.response)==null?void 0:r.data)==null?void 0:s.ErrorDescription)||"Unknown error",showIcon:!0})}),t.data&&e.jsxs(se,{gutter:[16,16],style:{margin:0,width:"100%",overflowX:"hidden",display:"flex",alignItems:"stretch"},children:[e.jsx(j,{span:8,children:e.jsx(F,{title:o("devices.title"),value:((n=t.data)==null?void 0:n.numberOfDevices)??0,description:o("controller.dashboard.devices_explanation"),icon:e.jsx("img",{src:Ne,style:{width:30,height:30}}),color:"#1677ff"})}),e.jsx(j,{span:8,children:e.jsx(ze,{data:t.data.healths})}),e.jsx(j,{span:8,children:e.jsx(Fe,{data:t.data.memoryUsed})}),e.jsx(j,{span:6,children:e.jsx(Ee,{data:t.data.associations})}),e.jsx(j,{span:6,children:e.jsx(Be,{data:t.data.memoryUsed})}),e.jsx(j,{span:6,children:e.jsx(Ze,{data:t.data.healths})}),e.jsx(j,{span:6,children:e.jsx(Ie,{data:t.data})}),e.jsx(j,{span:8,children:e.jsx(be,{data:t.data.commands})}),e.jsx(j,{span:8,children:e.jsx(Me,{data:t.data.deviceType})}),e.jsx(j,{span:8,children:e.jsx(Ge,{data:t.data.upTimes})})]})]}):"error"})};export{We as default};
