const u={year:31536e6,month:2628e6,day:864e5,hour:36e5,minute:6e4,second:1e3},i=new Intl.RelativeTimeFormat("en",{localeMatcher:"best fit",style:"long"}),n=e=>e>=10?e:`0${e}`,$=e=>e*1e3,l=e=>{if(!e||e===null)return"-";const s=$(e),t=new Date(s);return`${t.getFullYear()}-${n(t.getMonth()+1)}-${n(t.getDate())}
  ${n(t.getHours())}:${n(t.getMinutes())}:${n(t.getSeconds())}`},m=(e,s=new Date().getTime())=>{try{const t=$(e),a=new Date(t).getTime(),c=a-s;for(const r of Object.keys(u))if(Math.abs(c)>u[r]||r==="second")return i.format(Math.round(c/u[r]),r);return l(a)}catch{return"-"}},g=(e,s)=>{if(!e||e===0)return`0 ${s("common.seconds")}`;let t=e;const a=Math.floor(t/(3600*24));t-=a*(3600*24);const c=Math.floor(t/3600);t-=c*3600;const r=Math.floor(t/60);t-=r*60;let o="";return o=`${o}${n(a)}d`,o=`${o}${n(c)}h`,o=`${o}${n(r)}m`,o=`${o}${n(t)}s`,o},f=(e,s)=>{if(!e||e===0)return`0 ${s("common.seconds")}`;let t=e;const a=Math.floor(t/(3600*24));t-=a*(3600*24);const c=Math.floor(t/3600);t-=c*3600;const r=Math.floor(t/60);t-=r*60;let o="";return o=`${o}${n(a)}:`,o=`${o}${n(c)}:`,o=`${o}${n(r)}:`,o=`${o}${n(t)}`,o},d=(e=1,s=new Date)=>{const t=new Date(s.getTime());return t.setHours(s.getHours()-e),t},h=e=>{const s=$(e),t=new Date(s);return`${t.getFullYear()}_${n(t.getMonth()+1)}_${n(t.getDate())}_${n(t.getHours())}h${n(t.getMinutes())}m${n(t.getSeconds())}s`};export{l as c,h as d,m as f,d as g,f as m,g as s};
