var j=Object.defineProperty;var x=(t,e,s)=>e in t?j(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s;var p=(t,e,s)=>x(t,typeof e!="symbol"?e+"":e,s);import{q as g,w as E,dz as S,x as U,j as y,v as L,b6 as $,b8 as D,R as C,a0 as m,r as I}from"./index-CHCmiRmn.js";import{p as c}from"./CustomTable-CM5Sdauq.js";const _=g((t,e)=>{const{className:s,...n}=t,r=E("chakra-modal__footer",s),o=S(),a=U({display:"flex",alignItems:"center",justifyContent:"flex-end",...o.footer});return y.jsx(<PERSON><PERSON>footer,{ref:e,...n,__css:a,className:r})});_.displayName="ModalFooter";function Y(t){const{leastDestructiveRef:e,...s}=t;return y.jsx($,{...s,initialFocusRef:e})}const Z=g((t,e)=>y.jsx(D,{ref:e,role:"alertdialog",...t})),M=()=>/^((?!chrome|android).)*safari/i.test(navigator.userAgent),b=t=>Array.isArray(t)&&t.every(e=>typeof e=="object"&&!(e instanceof Array)),N=t=>Array.isArray(t)&&t.every(e=>Array.isArray(e)),T=t=>Array.from(t.map(e=>Object.keys(e)).reduce((e,s)=>new Set([...e,...s]),[])),B=(t,e)=>{e=e||T(t);let s=e,n=e;b(e)&&(s=e.map(o=>o.label),n=e.map(o=>o.key));const r=t.map(o=>n.map(a=>V(a,o)));return[s,...r]},V=(t,e)=>{const s=t.replace(/\[([^\]]+)]/g,".$1").split(".").reduce(function(n,r,o,a){const i=n[r];if(i==null)a.splice(1);else return i},e);return s===void 0?t in e?e[t]:"":s},P=t=>typeof t>"u"||t===null?"":t,k=(t,e=",",s='"')=>t.filter(n=>n).map(n=>n.map(r=>P(r)).map(r=>`${s}${r}${s}`).join(e)).join(`
`),q=(t,e,s,n)=>k(e?[e,...t]:t,s,n),H=(t,e,s,n)=>k(B(t,e),s,n),z=(t,e,s,n)=>e?`${e.join(s)}
${t}`:t.replace(/"/g,'""'),F=(t,e,s,n)=>{if(b(t))return H(t,e,s,n);if(N(t))return q(t,e,s,n);if(typeof t=="string")return z(t,e,s);throw new TypeError('Data should be a "String", "Array of arrays" OR "Array of objects" ')},R=(t,e,s,n,r)=>{const o=F(t,s,n,r),a=M()?"application/csv":"text/csv",i=new Blob([e?"\uFEFF":"",o],{type:a}),l=`data:${a};charset=utf-8,${e?"\uFEFF":""}${o}`,u=window.URL||window.webkitURL;return typeof u.createObjectURL>"u"?l:u.createObjectURL(i)},v={data:c.oneOfType([c.string,c.array,c.func]).isRequired,headers:c.array,target:c.string,separator:c.string,filename:c.string,uFEFF:c.bool,onClick:c.func,asyncOnClick:c.bool,enclosingCharacter:c.string},w={separator:",",filename:"generatedBy_react-csv.csv",uFEFF:!0,asyncOnClick:!1,enclosingCharacter:'"'},J={target:"_blank"};class h extends C.Component{constructor(e){super(e),this.state={}}buildURI(){return R(...arguments)}componentDidMount(){const{data:e,headers:s,separator:n,enclosingCharacter:r,uFEFF:o,target:a,specs:i,replace:l}=this.props;this.state.page=window.open(this.buildURI(e,o,s,n,r),a,i,l)}getWindow(){return this.state.page}render(){return null}}p(h,"defaultProps",Object.assign(w,J)),p(h,"propTypes",v);var d;let K=(d=class extends C.Component{constructor(e){super(e),this.buildURI=this.buildURI.bind(this)}buildURI(){return R(...arguments)}handleLegacy(e,s=!1){if(window.navigator.msSaveOrOpenBlob){e.preventDefault();const{data:n,headers:r,separator:o,filename:a,enclosingCharacter:i,uFEFF:l}=this.props,u=s&&typeof n=="function"?n():n;let f=new Blob([l?"\uFEFF":"",F(u,r,o,i)]);return window.navigator.msSaveBlob(f,a),!1}}handleAsyncClick(e){const s=n=>{if(n===!1){e.preventDefault();return}this.handleLegacy(e,!0)};this.props.onClick(e,s)}handleSyncClick(e){if(this.props.onClick(e)===!1){e.preventDefault();return}this.handleLegacy(e)}handleClick(){return e=>{if(typeof this.props.onClick=="function")return this.props.asyncOnClick?this.handleAsyncClick(e):this.handleSyncClick(e);this.handleLegacy(e)}}render(){const{data:e,headers:s,separator:n,filename:r,uFEFF:o,children:a,onClick:i,asyncOnClick:l,enclosingCharacter:u,...f}=this.props,A=typeof window>"u"?"":this.buildURI(e,o,s,n,u);return y.jsx("a",{download:r,...f,ref:O=>this.link=O,target:"_self",href:A,onClick:this.handleClick(),children:a})}},p(d,"defaultProps",w),p(d,"propTypes",v),d);const te=K,se=({isLoading:t,onModalClose:e})=>{const{isOpen:s,onOpen:n,onClose:r}=m(),{isOpen:o,onOpen:a,onClose:i}=m(),l=()=>{t?a():e?e():r()},u=()=>{i(),e?e():r()};return I.useMemo(()=>({onOpen:n,isOpen:s,isConfirmOpen:o,closeModal:l,closeConfirm:i,closeCancelAndForm:u}),[s,o,t])};export{Y as A,te as C,_ as M,Z as a,se as u};
