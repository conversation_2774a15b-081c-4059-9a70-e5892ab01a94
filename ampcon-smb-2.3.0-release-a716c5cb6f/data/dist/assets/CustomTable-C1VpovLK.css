.react-resizable{position:relative}.react-resizable-handle{position:absolute;width:20px;height:20px;background-repeat:no-repeat;background-origin:content-box;box-sizing:border-box;background-image:url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2IDYiIHN0eWxlPSJiYWNrZ3JvdW5kLWNvbG9yOiNmZmZmZmYwMCIgeD0iMHB4IiB5PSIwcHgiIHdpZHRoPSI2cHgiIGhlaWdodD0iNnB4Ij48ZyBvcGFjaXR5PSIwLjMwMiI+PHBhdGggZD0iTSA2IDYgTCAwIDYgTCAwIDQuMiBMIDQgNC4yIEwgNC4yIDQuMiBMIDQuMiAwIEwgNiAwIEwgNiA2IEwgNiA2IFoiIGZpbGw9IiMwMDAwMDAiLz48L2c+PC9zdmc+);background-position:bottom right;padding:0 3px 3px 0}.react-resizable-handle-sw{bottom:0;left:0;cursor:sw-resize;transform:rotate(90deg)}.react-resizable-handle-se{bottom:0;right:0;cursor:se-resize}.react-resizable-handle-nw{top:0;left:0;cursor:nw-resize;transform:rotate(180deg)}.react-resizable-handle-ne{top:0;right:0;cursor:ne-resize;transform:rotate(270deg)}.react-resizable-handle-w,.react-resizable-handle-e{top:50%;margin-top:-10px;cursor:ew-resize}.react-resizable-handle-w{left:0;transform:rotate(135deg)}.react-resizable-handle-e{right:0;transform:rotate(315deg)}.react-resizable-handle-n,.react-resizable-handle-s{left:50%;margin-left:-10px;cursor:ns-resize}.react-resizable-handle-n{top:0;transform:rotate(225deg)}.react-resizable-handle-s{bottom:0;transform:rotate(45deg)}.resizable-table .react-resizable{position:relative;background-clip:padding-box}.resizable-table .react-resizable-handle{position:absolute;right:-5px;bottom:0;top:0;width:10px;height:100%;cursor:col-resize;z-index:1;transition:border-color .2s ease}.resizable-table .react-resizable-handle:hover{border-right:2px solid #1890ff;transition:border-color .2s ease}.resizable-table th.ant-table-cell{position:relative;overflow:hidden}.resizable-table .react-resizable-handle:before{content:"";position:absolute;right:3px;top:50%;transform:translateY(-50%);width:2px;height:16px;background-color:#d9d9d9;transition:background-color .2s}.resizable-table .react-resizable-handle:hover:before{background-color:#1890ff}.resizable-table .react-resizable-handle{user-select:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none}.resizable-table .ant-dropdown,.resizable-table .ant-dropdown *{-webkit-user-select:auto;user-select:auto}.react-resizable-resizing{user-select:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none}.resizable-table .react-resizable-handle:active{border-right:2px solid #1890ff}.resizable-table .react-resizable-handle:active:before{background-color:#1890ff}
