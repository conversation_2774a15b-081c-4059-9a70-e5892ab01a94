import{R as m,j as d,bF as f,ac as r,b as n,z as a,y as l,u as b,af as i}from"./index-CHCmiRmn.js";import{P as h}from"./CustomTable-CM5Sdauq.js";import{f as v,c as g}from"./dateFormatting-yHFQ8l7H.js";const D={date:h.number.isRequired},p=({date:e})=>d.jsx(f,{hasArrow:!0,placement:"top",label:g(e),children:e===0?"-":v(e)});p.propTypes=D;const A=m.memo(p),u="/ampcon/wireless/site/label";function R(e){return r({url:`${u}`,method:"GET",params:e})}function j(e){return r({url:`${u}`,method:"POST",data:e})}function w(e){return r({url:`${u}`,method:"DELETE",data:{id:e}})}const T=async(e,t)=>i.get(`venue?withExtendedInfo=true&offset=${t}&limit=${e}`).then(({data:s})=>s.venues),y=async()=>{let t=0,s=[],o=[];do o=await T(500,t),s=s.concat(o),t+=500;while(o.length===500);return s},V=()=>{const{t:e}=n(),t=a();return b(["get-venues"],()=>y(),{staleTime:3e4,onError:s=>{var o,c;t.isActive("venues-fetching-error")||t({id:"venues-fetching-error",title:e("common.error"),description:e("crud.error_fetching_obj",{obj:e("venues.title"),e:(c=(o=s==null?void 0:s.response)==null?void 0:o.data)==null?void 0:c.ErrorDescription}),status:"error",duration:5e3,isClosable:!0,position:"top-right"})}})},F=({id:e})=>{const{t}=n();return a(),l(()=>i.put(`venue/${e}?updateAllDevices=true`,{}),{})},L=({id:e})=>{const{t}=n();return a(),l(()=>i.put(`venue/${e}?rebootAllDevices=true`,{}),{})};export{A as F,F as a,V as b,j as c,w as d,R as f,L as u};
