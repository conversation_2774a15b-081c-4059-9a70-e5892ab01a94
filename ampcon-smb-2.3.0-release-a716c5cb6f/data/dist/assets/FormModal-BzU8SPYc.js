import{U as y,r as u,O as v,j as s,M as m,D as c,B as d}from"./index-CHCmiRmn.js";const p=()=>{const r=y(),l=u.useCallback((t,i)=>{let e=a();i(e&&e===t)},[r.entityFavorites.favorites]),o=u.useCallback((t,i)=>{let e=a(),n=e&&e===t;n?r.entityFavorites.remove({id:t,type:"venue"}):r.entityFavorites.add({id:t,type:"venue"}),i(!n)},[r.entityFavorites.favorites]),a=()=>{const t=r.entityFavorites.favorites.find(i=>i.type==="venue");return t?t.id:null};return{isFavorited:l,toggleFavorite:o,getFirstVenueFavoriteId:a}},j=({open:r,title:l,onCancel:o,onFinish:a,initialValues:t,modalClass:i="ampcon-max-modal",form:e,onValuesChange:n,children:F})=>r?(e||([e]=v.useForm()),s.jsx(m,{open:r,title:s.jsxs("div",{children:[l,s.jsx(c,{style:{marginTop:8,marginBottom:0}})]}),onCancel:o,footer:s.jsxs(s.Fragment,{children:[s.jsx(c,{style:{marginTop:0,marginBottom:20}}),s.jsx(d,{onClick:o,style:{width:100},children:"Cancel"},"cancel"),s.jsx(d,{type:"primary",onClick:()=>e==null?void 0:e.submit(),style:{width:100},children:"Apply"},"ok")]}),destroyOnClose:!0,className:`wirelessModal${i?" "+i:""}`,children:s.jsx(v,{form:e,initialValues:t,onFinish:a,onValuesChange:n,className:"wirelessForm",children:F})})):null;export{j as F,p as u};
