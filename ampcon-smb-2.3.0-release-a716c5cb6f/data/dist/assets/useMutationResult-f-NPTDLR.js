import{bz as w,r as o,j as a,a2 as C,W as y,aq as V,b as k,G as E,H as v}from"./index-CHCmiRmn.js";import{u as D}from"./FormModal-BzU8SPYc.js";import"./Form-CYdW6Qd_.js";const A=({onChange:t})=>{const{getFirstVenueFavoriteId:h}=D(),c=w(),[u,d]=o.useState(""),[s,p]=o.useState(""),[g,j]=o.useState(""),l=o.useMemo(()=>{if(!c.data)return[];const e=c.data.sort((f,i)=>String(f.id)==="0"?-1:String(i.id)==="0"?1:f.name.localeCompare(i.name)).map(f=>({label:f.name,value:f.id,type:"venue"}));return e.unshift({label:"All Sites",value:"all",type:"venue"}),e},[c.data]),b=o.useMemo(()=>u?l.filter(e=>e.label.toLowerCase().includes(u.toLowerCase())):l,[l,u]);o.useEffect(()=>{typeof c.refetch=="function"&&c.refetch()},[]),o.useEffect(()=>{var e;if(l.length>0){let i=h()||((e=l[0])==null?void 0:e.value);const S=window.location.hash;S&&S.startsWith("#")&&(i=S.substring(1));const _=l.find(x=>x.value===i);j((_==null?void 0:_.label)||""),p(i),window.history.replaceState({},"",`#${i}`)}},[l,h]);const n=e=>{p(e),t&&t(e)},r=e=>{d(e.target.value)},m=e=>{e||d("")};return a.jsxs("div",{className:"site-select-container",children:[a.jsx("span",{className:"site-title",children:"Site"}),a.jsx(C,{className:"site-select",placeholder:g,options:b,value:s,onChange:n,style:{width:260},onDropdownVisibleChange:m,dropdownRender:e=>a.jsxs(a.Fragment,{children:[a.jsx("div",{style:{padding:8},children:a.jsx(y,{prefix:a.jsx(V,{style:{color:"rgba(0,0,0,.25)"}}),placeholder:"Search sites...",value:u,onChange:r,allowClear:!0})}),a.jsx("div",{children:b.length>0?e:a.jsx("div",{style:{padding:8,textAlign:"center"},children:"No results found"})})]})})]})},F={refresh:()=>{},onClose:()=>{},queryToInvalidate:null},M=({objName:t,operationType:h,refresh:c,onClose:u,queryToInvalidate:d})=>{const{t:s}=k(),p=E(),g=()=>{switch(h){case"update":return s("crud.success_update_obj",{obj:t});case"delete":return s("crud.success_delete_obj",{obj:t});case"blink":return s("commands.blink_success",{obj:t});case"reboot":return s("commands.reboot_success",{obj:t});default:return s("crud.success_create_obj",{obj:t})}},j=n=>{var m,e;const r=((e=(m=n==null?void 0:n.response)==null?void 0:m.data)==null?void 0:e.ErrorDescription)||s("common.unknown_error");switch(h){case"update":return s("crud.error_update_obj",{obj:t,e:r});case"delete":return s("crud.error_delete_obj",{obj:t,e:r});case"blink":return s("commands.blink_error",{obj:t,e:r});case"reboot":return s("commands.reboot_error",{obj:t,e:r});default:return s("crud.error_create_obj",{obj:t,e:r})}},l=o.useCallback(({setSubmitting:n,resetForm:r}={setSubmitting:null,resetForm:null})=>{c&&c(),n&&n(!1),r&&r(),v.success(g()),u&&u(),d&&p.invalidateQueries(d)},[d]),b=o.useCallback((n,{setSubmitting:r}={setSubmitting:null})=>{v.error(j(n)),r&&r(!1)},[]);return o.useMemo(()=>({onSuccess:l,onError:b}),[])};M.defaultProps=F;export{A as S,M as u};
