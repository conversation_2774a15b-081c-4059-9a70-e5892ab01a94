import{ao as k,r as s}from"./index-CHCmiRmn.js";const J=({settings:e,showAllRows:m})=>{if(m)return{pageSize:1e3,pageIndex:0};let l=10,c=0;if(e){const r=localStorage.getItem(e);if(r)try{l=parseInt(r,10)}catch{l=10}const a=localStorage.getItem(`${e}.page`);if(a)try{c=parseInt(a,10)}catch{c=0}}return{pageSize:l,pageIndex:c}},A=(e,m)=>{if(m){const l=localStorage.getItem(`${m}.order`);if(l)try{const c=JSON.parse(l);return c.length>0?c:e}catch{return e}}return e},B=({tableSettingsId:e,defaultSortBy:m,defaultOrder:l,showAllRows:c})=>{const r=`${e}.order`,a=`${e}.hiddenColumns`,v=`${e}.page`,{getPref:O,setPref:h,setPrefs:C,deletePref:j}=k(),[I,$]=s.useState(m??[]),[g,d]=s.useState({}),[S,p]=s.useState(A(l??[],e)),[f,z]=s.useState(J({settings:e,showAllRows:c})),P=s.useCallback(i=>{p(i),e&&(localStorage.setItem(r,JSON.stringify(i)),h({preference:r,value:i.join(",")}))},[h]),N=s.useCallback(async()=>{e&&(localStorage.removeItem(r),localStorage.removeItem(a),await j([r,a])),p(l??[]),d({})},[j]),w=s.useCallback(i=>{const u={...g};u[i]=!1;let t=Object.entries(u).filter(([,o])=>!o).map(([o])=>o);t=[...new Set(t)];let n=S.filter(o=>!t.includes(o));return n=[...new Set(n)],C([{tag:a,value:t.join(",")},{tag:r,value:n.join(",")}]),d({...u}),p(n),localStorage.setItem(r,JSON.stringify(n)),localStorage.setItem(a,t.join(",")),{hiddenColumns:t,columnOrder:n}},[S,g,C]),x=s.useCallback((i,u)=>{const t={...g};t[i]=!0;let n=Object.entries(t).filter(([,y])=>!y).map(([y])=>y);n=[...new Set(n)];const o=[...new Set(u)];return C([{tag:a,value:n.join(",")},{tag:r,value:o.join(",")}]),d({...t}),p(o),localStorage.setItem(r,JSON.stringify(o)),localStorage.setItem(a,n.join(",")),{hiddenColumns:n,columnOrder:o}},[S,g,C]);return s.useEffect(()=>{const i=O(a);if(i){const t=i.split(",");d(t.reduce((n,o)=>({...n,[o]:!1}),{}))}else d({});const u=O(r);if(u){const t=u.split(",");p(t)}},[e]),s.useEffect(()=>{e&&(localStorage.setItem(v,String(f.pageIndex)),e&&localStorage.setItem(`${e}`,String(f.pageSize)))},[f.pageIndex,f.pageSize]),s.useMemo(()=>({tableSettingsId:e,pageInfo:f,sortBy:I,setSortBy:$,columnOrder:S,setColumnOrder:P,hideColumn:w,unhideColumn:x,columnVisibility:g,setColumnVisibility:d,onPaginationChange:z,resetPreferences:N}),[f,w,x,g,I,S,P])};export{B as u};
