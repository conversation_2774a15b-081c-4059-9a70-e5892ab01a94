import{r,cg as No,q as le,ch as nn,t as tt,j as t,v as he,w as Ge,ci as Gn,s as Pt,o as $o,aP as Bo,l as on,m as zo,cj as wn,n as Fe,k as me,ck as Vt,aO as Vo,cl as Ho,aJ as sn,cm as Zo,bR as ie,cn as Wo,co as Uo,bO as ee,cp as Yn,bS as Ie,cq as it,bQ as Me,cr as Go,cs as Yo,ct as Zt,bN as ct,R as V,_ as we,cu as Xo,cv as qo,bM as Xn,bU as Wt,cw as Ko,I as St,bY as Jo,bP as Ke,bZ as Ln,cx as es,cy as ts,cz as ns,cA as os,bX as qn,cB as ss,cC as as,at as rs,b_ as Kn,c2 as Jn,cD as eo,cE as Et,cF as kn,cG as is,p as to,c4 as ls,B as q,c as ke,L as Mt,T as Ae,e as cs,H as Le,b as H,V as At,ce as Ze,cH as an,y as no,cI as ds,a as Ne,bc as oo,cJ as us,cK as ms,aD as Ut,X as fe,aE as hs,aV as fs,bC as xt,J as Ve,ax as Pe,bh as He,bn as so,ay as be,bm as pt,b3 as gs,bf as Gt,bg as Yt,bi as ao,bj as Xt,u as mt,z as rn,ao as ps,G as ro,be as io,b5 as xs,a0 as ln,$ as vt,bo as vs,cL as ys,aZ as Ft,d as yt,bp as Cs,ah as lo,W as co,aw as cn,cM as dt,i as dn,a5 as Se,g as Rn,h as bs,F as In,a2 as ht,bs as nt,S as uo,a7 as Ss,a8 as js,ak as ws,al as Ls,M as ks,D as Mn,b9 as Rs,Z as _t,A as un,ad as Ct,c7 as Nt,an as Is,cN as Tn,aC as mo,cO as Ms,bd as Ts,cf as Qs,cP as Qn,Q as En,cQ as Es}from"./index-CHCmiRmn.js";import{W as ot}from"./CustomTable-CM5Sdauq.js";import{D as Fn,c as Fs,e as ft,a as Ht,d as _s,C as Ds}from"./CustomWirelessRangePicker-DYVOqnGv.js";import{z as ho,A as Os,B as Ps,D as As,E as Ns,G as $s,H as Bs,I as zs,S as Vs,n as Hs,o as Zs,p as Ws,i as fo,J as Je,F as et,K as ut,L as go,N as _n,M as Dn,O as On,P as Pn,Q as An,q as po,U as We,k as mn,V as Us,T as Gs,X as xo,C as Ue,Y as Ys,d as Xs,e as qs,f as Ks,h as $t,Z as vo,c as Js,r as ea,_ as ta,$ as na,R as hn,s as fn,a0 as oa,W as sa,a1 as aa,a2 as ra,a3 as ia,a4 as la,a5 as ca,a6 as da,w as ua,y as ma,x as ha}from"./SearchInput-CCmpVjpk.js";import{F as fa,a as ga}from"./index-LkKwRvEU.js";import{a as jt}from"./useFastField-DAY0E64C.js";import{u as pa,C as xa}from"./useCommandModal-my1Kwhjw.js";import"./Form-CYdW6Qd_.js";import{a as va,b as ya,c as Ca}from"./HealthChecks-CsGXfWi1.js";import"./useDataGrid-DNbMJCLu.js";function yo(e=!1){const[n,o]=r.useState(e),i=r.useMemo(()=>({on:()=>o(!0),off:()=>o(!1),toggle:()=>o(s=>!s)}),[]);return[n,i]}function ba(e){const{loading:n,src:o,srcSet:i,onLoad:s,onError:l,crossOrigin:d,sizes:a,ignoreFallback:c}=e,[u,h]=r.useState("pending");r.useEffect(()=>{h(o?"loading":"pending")},[o]);const g=r.useRef(null),p=r.useCallback(()=>{if(!o)return;f();const x=new Image;x.src=o,d&&(x.crossOrigin=d),i&&(x.srcset=i),a&&(x.sizes=a),n&&(x.loading=n),x.onload=C=>{f(),h("loaded"),s==null||s(C)},x.onerror=C=>{f(),h("failed"),l==null||l(C)},g.current=x},[o,d,i,a,s,l,n]),f=()=>{g.current&&(g.current.onload=null,g.current.onerror=null,g.current=null)};return No(()=>{if(!c)return u==="loading"&&p(),()=>{f()}},[u,p,c]),c?"loaded":u}const Sa=(e,n)=>e!=="loaded"&&n==="beforeLoadOrError"||e==="failed"&&n==="onError",Bt=le(function(n,o){const i=nn("Code",n),{className:s,...l}=tt(n);return t.jsx(he.code,{ref:o,className:Ge("chakra-code",n.className),...l,__css:{display:"inline-block",...i}})});Bt.displayName="Code";const Co=le(function(n,o){const{borderLeftWidth:i,borderBottomWidth:s,borderTopWidth:l,borderRightWidth:d,borderWidth:a,borderStyle:c,borderColor:u,...h}=nn("Divider",n),{className:g,orientation:p="horizontal",__css:f,...x}=tt(n),C={vertical:{borderLeftWidth:i||d||a||"1px",height:"100%"},horizontal:{borderBottomWidth:s||l||a||"1px",width:"100%"}};return t.jsx(he.hr,{ref:o,"aria-orientation":p,...x,__css:{...h,border:"0",borderColor:u,borderStyle:c,...C[p],...f},className:Ge("chakra-divider",g)})});Co.displayName="Divider";const qt=le(function(n,o){const{htmlWidth:i,htmlHeight:s,alt:l,...d}=n;return t.jsx("img",{width:i,height:s,ref:o,alt:l,...d})});qt.displayName="NativeImage";const bo=le(function(n,o){const{fallbackSrc:i,fallback:s,src:l,srcSet:d,align:a,fit:c,loading:u,ignoreFallback:h,crossOrigin:g,fallbackStrategy:p="beforeLoadOrError",referrerPolicy:f,...x}=n,C=i!==void 0||s!==void 0,m=u!=null||h||!C,b=ba({...n,crossOrigin:g,ignoreFallback:m}),L=Sa(b,p),w={ref:o,objectFit:c,objectPosition:a,...m?x:Gn(x,["onError","onLoad"])};return L?s||t.jsx(he.img,{as:qt,className:"chakra-image__placeholder",src:i,...w}):t.jsx(he.img,{as:qt,src:l,srcSet:d,crossOrigin:g,loading:u,referrerPolicy:f,className:"chakra-image",...w})});bo.displayName="Image";const wt=le(function(n,o){const{htmlSize:i,...s}=n,l=Pt("Input",s),d=tt(s),a=ho(d),c=Ge("chakra-input",n.className);return t.jsx(he.input,{size:i,...a,__css:l.field,ref:o,className:c})});wt.displayName="Input";wt.id="Input";const[ja,wa]=on({name:"InputGroupStylesContext",errorMessage:`useInputGroupStyles returned is 'undefined'. Seems you forgot to wrap the components in "<InputGroup />" `}),Lt=le(function(n,o){const i=Pt("Input",n),{children:s,className:l,...d}=tt(n),a=Ge("chakra-input__group",l),c={},u=$o(s),h=i.field;u.forEach(p=>{i&&(h&&p.type.id==="InputLeftElement"&&(c.paddingStart=h.height??h.h),h&&p.type.id==="InputRightElement"&&(c.paddingEnd=h.height??h.h),p.type.id==="InputRightAddon"&&(c.borderEndRadius=0),p.type.id==="InputLeftAddon"&&(c.borderStartRadius=0))});const g=u.map(p=>{var x,C;const f=Bo({size:((x=p.props)==null?void 0:x.size)||n.size,variant:((C=p.props)==null?void 0:C.variant)||n.variant});return p.type.id!=="Input"?r.cloneElement(p,f):r.cloneElement(p,Object.assign(f,c,p.props))});return t.jsx(he.div,{className:a,ref:o,__css:{width:"100%",display:"flex",position:"relative",isolation:"isolate",...i.group},"data-group":!0,...d,children:t.jsx(ja,{value:i,children:g})})});Lt.displayName="InputGroup";const La={left:{marginEnd:"-1px",borderEndRadius:0,borderEndColor:"transparent"},right:{marginStart:"-1px",borderStartRadius:0,borderStartColor:"transparent"}},ka=he("div",{baseStyle:{flex:"0 0 auto",width:"auto",display:"flex",alignItems:"center",whiteSpace:"nowrap"}}),gn=le(function(n,o){const{placement:i="left",...s}=n,l=La[i]??{},d=wa();return t.jsx(ka,{ref:o,...s,__css:{...d.addon,...l}})});gn.displayName="InputAddon";const So=le(function(n,o){return t.jsx(gn,{ref:o,placement:"left",...n,className:Ge("chakra-input__left-addon",n.className)})});So.displayName="InputLeftAddon";So.id="InputLeftAddon";const pn=le(function(n,o){return t.jsx(gn,{ref:o,placement:"right",...n,className:Ge("chakra-input__right-addon",n.className)})});pn.displayName="InputRightAddon";pn.id="InputRightAddon";function Ra(e){return e&&wn(e)&&wn(e.target)}function Ia(e={}){const{onChange:n,value:o,defaultValue:i,name:s,isDisabled:l,isFocusable:d,isNative:a,...c}=e,[u,h]=r.useState(i||""),g=typeof o<"u",p=g?o:u,f=r.useRef(null),x=r.useCallback(()=>{const v=f.current;if(!v)return;let j="input:not(:disabled):checked";const k=v.querySelector(j);if(k){k.focus();return}j="input:not(:disabled)";const R=v.querySelector(j);R==null||R.focus()},[]),m=`radio-${r.useId()}`,b=s||m,L=r.useCallback(v=>{const j=Ra(v)?v.target.value:v;g||h(j),n==null||n(String(j))},[n,g]),w=r.useCallback((v={},j=null)=>({...v,ref:zo(j,f),role:"radiogroup"}),[]),y=r.useCallback((v={},j=null)=>({...v,ref:j,name:b,[a?"checked":"isChecked"]:p!=null?v.value===p:void 0,onChange(R){L(R)},"data-radiogroup":!0}),[a,b,L,p]);return{getRootProps:w,getRadioProps:y,name:b,ref:f,focus:x,setValue:h,value:p,onChange:L,isDisabled:l,isFocusable:d,htmlProps:c}}const[Ma,jo]=on({name:"RadioGroupContext",strict:!1}),xn=le((e,n)=>{const{colorScheme:o,size:i,variant:s,children:l,className:d,isDisabled:a,isFocusable:c,...u}=e,{value:h,onChange:g,getRootProps:p,name:f,htmlProps:x}=Ia(u),C=r.useMemo(()=>({name:f,size:i,onChange:g,colorScheme:o,value:h,variant:s,isDisabled:a,isFocusable:c}),[f,i,g,o,h,s,a,c]);return t.jsx(Ma,{value:C,children:t.jsx(he.div,{...p(x,n),className:Ge("chakra-radio-group",d),children:l})})});xn.displayName="RadioGroup";function Ta(e={}){const{defaultChecked:n,isChecked:o,isFocusable:i,isDisabled:s,isReadOnly:l,isRequired:d,onChange:a,isInvalid:c,name:u,value:h,id:g,"data-radiogroup":p,"aria-describedby":f,...x}=e,C=`radio-${r.useId()}`,m=Os(),L=!!jo()||!!p;let y=!!m&&!L?m.id:C;y=g??y;const v=s??(m==null?void 0:m.isDisabled),j=l??(m==null?void 0:m.isReadOnly),k=d??(m==null?void 0:m.isRequired),R=c??(m==null?void 0:m.isInvalid),[I,M]=r.useState(!1),[T,Q]=r.useState(!1),[F,_]=r.useState(!1),[$,B]=r.useState(!!n),D=typeof o<"u",O=D?o:$,W=r.useRef(!1);r.useEffect(()=>Ps(N=>{W.current=N}),[]);const E=r.useCallback(N=>{if(j||v){N.preventDefault();return}D||B(N.currentTarget.checked),a==null||a(N)},[D,v,j,a]),P=r.useCallback(N=>{N.key===" "&&_(!0)},[_]),G=r.useCallback(N=>{N.key===" "&&_(!1)},[_]),re=r.useCallback((N={},X=null)=>({...N,ref:X,"data-active":me(F),"data-hover":me(T),"data-disabled":me(v),"data-invalid":me(R),"data-checked":me(O),"data-focus":me(I),"data-focus-visible":me(I&&W.current),"data-readonly":me(j),"aria-hidden":!0,onMouseDown:Fe(N.onMouseDown,()=>_(!0)),onMouseUp:Fe(N.onMouseUp,()=>_(!1)),onMouseEnter:Fe(N.onMouseEnter,()=>Q(!0)),onMouseLeave:Fe(N.onMouseLeave,()=>Q(!1))}),[F,T,v,R,O,I,j]),{onFocus:oe,onBlur:K}=m??{},se=r.useCallback((N={},X=null)=>{const te=v&&!i;return{...N,id:y,ref:X,type:"radio",name:u,value:h,onChange:Fe(N.onChange,E),onBlur:Fe(K,N.onBlur,()=>M(!1)),onFocus:Fe(oe,N.onFocus,()=>M(!0)),onKeyDown:Fe(N.onKeyDown,P),onKeyUp:Fe(N.onKeyUp,G),checked:O,disabled:te,readOnly:j,required:k,"aria-invalid":Vt(R),"aria-disabled":Vt(te),"aria-required":Vt(k),"data-readonly":me(j),"aria-describedby":f,style:As}},[v,i,y,u,h,E,K,oe,P,G,O,j,k,R,f]);return{state:{isInvalid:R,isFocused:I,isChecked:O,isActive:F,isHovered:T,isDisabled:v,isReadOnly:j,isRequired:k},getRadioProps:re,getInputProps:se,getLabelProps:(N={},X=null)=>({...N,ref:X,onMouseDown:Fe(N.onMouseDown,Qa),"data-disabled":me(v),"data-checked":me(O),"data-invalid":me(R)}),getRootProps:(N,X=null)=>({htmlFor:y,...N,ref:X,"data-disabled":me(v),"data-checked":me(O),"data-invalid":me(R)}),htmlProps:x}}function Qa(e){e.preventDefault(),e.stopPropagation()}const bt=le((e,n)=>{const o=jo(),{onChange:i,value:s}=e,l=Pt("Radio",{...o,...e}),d=tt(e),{spacing:a="0.5rem",children:c,isDisabled:u=o==null?void 0:o.isDisabled,isFocusable:h=o==null?void 0:o.isFocusable,inputProps:g,...p}=d;let f=e.isChecked;(o==null?void 0:o.value)!=null&&s!=null&&(f=o.value===s);let x=i;o!=null&&o.onChange&&s!=null&&(x=Vo(o.onChange,i));const C=(e==null?void 0:e.name)??(o==null?void 0:o.name),{getInputProps:m,getRadioProps:b,getLabelProps:L,getRootProps:w,htmlProps:y}=Ta({...p,isChecked:f,isFocusable:h,isDisabled:u,onChange:x,name:C}),[v,j]=Ns(y,Ho),k=b(j),R=m(g,n),I=L(),M=Object.assign({},v,w()),T={display:"inline-flex",alignItems:"center",verticalAlign:"top",cursor:"pointer",position:"relative",...l.container},Q={display:"inline-flex",alignItems:"center",justifyContent:"center",flexShrink:0,...l.control},F={userSelect:"none",marginStart:a,...l.label};return t.jsxs(he.label,{className:"chakra-radio",...M,__css:T,children:[t.jsx("input",{className:"chakra-radio__input",...R}),t.jsx(he.span,{className:"chakra-radio__control",...k,__css:Q}),c&&t.jsx(he.span,{className:"chakra-radio__label",...I,__css:F,children:c})]})});bt.displayName="Radio";const[Ea,wo]=on({name:"TagStylesContext",errorMessage:`useTagStyles returned is 'undefined'. Seems you forgot to wrap the components in "<Tag />" `}),Kt=le((e,n)=>{const o=Pt("Tag",e),i=tt(e),s={display:"inline-flex",verticalAlign:"top",alignItems:"center",maxWidth:"100%",...o.container};return t.jsx(Ea,{value:o,children:t.jsx(he.span,{ref:n,...i,__css:s})})});Kt.displayName="Tag";const Fa=le((e,n)=>{const o=wo();return t.jsx(he.span,{ref:n,noOfLines:1,...e,__css:o.label})});Fa.displayName="TagLabel";const _a=le((e,n)=>t.jsx(sn,{ref:n,verticalAlign:"top",marginEnd:"0.5rem",...e}));_a.displayName="TagLeftIcon";const Da=le((e,n)=>t.jsx(sn,{ref:n,verticalAlign:"top",marginStart:"0.5rem",...e}));Da.displayName="TagRightIcon";const Lo=e=>t.jsx(sn,{verticalAlign:"inherit",viewBox:"0 0 512 512",...e,children:t.jsx("path",{fill:"currentColor",d:"M289.94 256l95-95A24 24 0 00351 127l-95 95-95-95a24 24 0 00-34 34l95 95-95 95a24 24 0 1034 34l95-95 95 95a24 24 0 0034-34z"})});Lo.displayName="TagCloseIcon";const Oa=le((e,n)=>{const{isDisabled:o,children:i,...s}=e,d={display:"flex",alignItems:"center",justifyContent:"center",outline:"0",...wo().closeButton};return t.jsx(he.button,{ref:n,"aria-label":"close",...s,type:"button",disabled:o,__css:d,children:i||t.jsx(Lo,{})})});Oa.displayName="TagCloseButton";const Pa=["h","minH","height","minHeight"],ko=le((e,n)=>{const o=nn("Textarea",e),{className:i,rows:s,...l}=tt(e),d=ho(l),a=s?Gn(o,Pa):o;return t.jsx(he.textarea,{ref:n,rows:s,...d,className:Ge("chakra-textarea",i),__css:a})});ko.displayName="Textarea";function Aa(){return Zo()}const vn={useBreakpoint:Aa};var Jt=["crossOrigin","decoding","draggable","loading","referrerPolicy","sizes","srcSet","useMap","alt"],kt=r.createContext(null),Nn=0;function Na(e,n){var o=r.useState(function(){return Nn+=1,String(Nn)}),i=ie(o,1),s=i[0],l=r.useContext(kt),d={data:n,canPreview:e};return r.useEffect(function(){if(l)return l.register(s,d)},[]),r.useEffect(function(){l&&l.register(s,d)},[e,n]),s}function $a(e){return new Promise(function(n){var o=document.createElement("img");o.onerror=function(){return n(!1)},o.onload=function(){return n(!0)},o.src=e})}function Ro(e){var n=e.src,o=e.isCustomPlaceholder,i=e.fallback,s=r.useState(o?"loading":"normal"),l=ie(s,2),d=l[0],a=l[1],c=r.useRef(!1),u=d==="error";r.useEffect(function(){var f=!0;return $a(n).then(function(x){!x&&f&&a("error")}),function(){f=!1}},[n]),r.useEffect(function(){o&&!c.current?a("loading"):u&&a("normal")},[n]);var h=function(){a("normal")},g=function(x){c.current=!1,d==="loading"&&x!==null&&x!==void 0&&x.complete&&(x.naturalWidth||x.naturalHeight)&&(c.current=!0,h())},p=u&&i?{src:i}:{onLoad:h,src:n};return[g,p,d]}var Tt={x:0,y:0,rotate:0,scale:1,flipX:!1,flipY:!1};function Ba(e,n,o,i){var s=r.useRef(null),l=r.useRef([]),d=r.useState(Tt),a=ie(d,2),c=a[0],u=a[1],h=function(x){u(Tt),i&&!Wo(Tt,c)&&i({transform:Tt,action:x})},g=function(x,C){s.current===null&&(l.current=[],s.current=Uo(function(){u(function(m){var b=m;return l.current.forEach(function(L){b=ee(ee({},b),L)}),s.current=null,i==null||i({transform:b,action:C}),b})})),l.current.push(ee(ee({},c),x))},p=function(x,C,m,b,L){var w=e.current,y=w.width,v=w.height,j=w.offsetWidth,k=w.offsetHeight,R=w.offsetLeft,I=w.offsetTop,M=x,T=c.scale*x;T>o?(T=o,M=o/c.scale):T<n&&(T=L?T:n,M=T/c.scale);var Q=m??innerWidth/2,F=b??innerHeight/2,_=M-1,$=_*y*.5,B=_*v*.5,D=_*(Q-c.x-R),O=_*(F-c.y-I),W=c.x-(D-$),E=c.y-(O-B);if(x<1&&T===1){var P=j*T,G=k*T,re=Yn(),oe=re.width,K=re.height;P<=oe&&G<=K&&(W=0,E=0)}g({x:W,y:E,scale:T},C)};return{transform:c,resetTransform:h,updateTransform:g,dispatchZoomChange:p}}function $n(e,n,o,i){var s=n+o,l=(o-i)/2;if(o>i){if(n>0)return Ie({},e,l);if(n<0&&s<i)return Ie({},e,-l)}else if(n<0||s>i)return Ie({},e,n<0?l:-l);return{}}function Io(e,n,o,i){var s=Yn(),l=s.width,d=s.height,a=null;return e<=l&&n<=d?a={x:0,y:0}:(e>l||n>d)&&(a=ee(ee({},$n("x",o,e,l)),$n("y",i,n,d))),a}var lt=1,za=1;function Va(e,n,o,i,s,l,d){var a=s.rotate,c=s.scale,u=s.x,h=s.y,g=r.useState(!1),p=ie(g,2),f=p[0],x=p[1],C=r.useRef({diffX:0,diffY:0,transformX:0,transformY:0}),m=function(v){!n||v.button!==0||(v.preventDefault(),v.stopPropagation(),C.current={diffX:v.pageX-u,diffY:v.pageY-h,transformX:u,transformY:h},x(!0))},b=function(v){o&&f&&l({x:v.pageX-C.current.diffX,y:v.pageY-C.current.diffY},"move")},L=function(){if(o&&f){x(!1);var v=C.current,j=v.transformX,k=v.transformY,R=u!==j&&h!==k;if(!R)return;var I=e.current.offsetWidth*c,M=e.current.offsetHeight*c,T=e.current.getBoundingClientRect(),Q=T.left,F=T.top,_=a%180!==0,$=Io(_?M:I,_?I:M,Q,F);$&&l(ee({},$),"dragRebound")}},w=function(v){if(!(!o||v.deltaY==0)){var j=Math.abs(v.deltaY/100),k=Math.min(j,za),R=lt+k*i;v.deltaY>0&&(R=lt/R),d(R,"wheel",v.clientX,v.clientY)}};return r.useEffect(function(){var y,v,j,k;if(n){j=it(window,"mouseup",L,!1),k=it(window,"mousemove",b,!1);try{window.top!==window.self&&(y=it(window.top,"mouseup",L,!1),v=it(window.top,"mousemove",b,!1))}catch{}}return function(){var R,I,M,T;(R=j)===null||R===void 0||R.remove(),(I=k)===null||I===void 0||I.remove(),(M=y)===null||M===void 0||M.remove(),(T=v)===null||T===void 0||T.remove()}},[o,f,u,h,a,n]),{isMoving:f,onMouseDown:m,onMouseMove:b,onMouseUp:L,onWheel:w}}function Dt(e,n){var o=e.x-n.x,i=e.y-n.y;return Math.hypot(o,i)}function Ha(e,n,o,i){var s=Dt(e,o),l=Dt(n,i);if(s===0&&l===0)return[e.x,e.y];var d=s/(s+l),a=e.x+d*(n.x-e.x),c=e.y+d*(n.y-e.y);return[a,c]}function Za(e,n,o,i,s,l,d){var a=s.rotate,c=s.scale,u=s.x,h=s.y,g=r.useState(!1),p=ie(g,2),f=p[0],x=p[1],C=r.useRef({point1:{x:0,y:0},point2:{x:0,y:0},eventType:"none"}),m=function(v){C.current=ee(ee({},C.current),v)},b=function(v){if(n){v.stopPropagation(),x(!0);var j=v.touches,k=j===void 0?[]:j;k.length>1?m({point1:{x:k[0].clientX,y:k[0].clientY},point2:{x:k[1].clientX,y:k[1].clientY},eventType:"touchZoom"}):m({point1:{x:k[0].clientX-u,y:k[0].clientY-h},eventType:"move"})}},L=function(v){var j=v.touches,k=j===void 0?[]:j,R=C.current,I=R.point1,M=R.point2,T=R.eventType;if(k.length>1&&T==="touchZoom"){var Q={x:k[0].clientX,y:k[0].clientY},F={x:k[1].clientX,y:k[1].clientY},_=Ha(I,M,Q,F),$=ie(_,2),B=$[0],D=$[1],O=Dt(Q,F)/Dt(I,M);d(O,"touchZoom",B,D,!0),m({point1:Q,point2:F,eventType:"touchZoom"})}else T==="move"&&(l({x:k[0].clientX-I.x,y:k[0].clientY-I.y},"move"),m({eventType:"move"}))},w=function(){if(o){if(f&&x(!1),m({eventType:"none"}),i>c)return l({x:0,y:0,scale:i},"touchZoom");var v=e.current.offsetWidth*c,j=e.current.offsetHeight*c,k=e.current.getBoundingClientRect(),R=k.left,I=k.top,M=a%180!==0,T=Io(M?j:v,M?v:j,R,I);T&&l(ee({},T),"dragRebound")}};return r.useEffect(function(){var y;return o&&n&&(y=it(window,"touchmove",function(v){return v.preventDefault()},{passive:!1})),function(){var v;(v=y)===null||v===void 0||v.remove()}},[o,n]),{isTouching:f,onTouchStart:b,onTouchMove:L,onTouchEnd:w}}var Wa=function(n){var o=n.visible,i=n.maskTransitionName,s=n.getContainer,l=n.prefixCls,d=n.rootClassName,a=n.icons,c=n.countRender,u=n.showSwitch,h=n.showProgress,g=n.current,p=n.transform,f=n.count,x=n.scale,C=n.minScale,m=n.maxScale,b=n.closeIcon,L=n.onSwitchLeft,w=n.onSwitchRight,y=n.onClose,v=n.onZoomIn,j=n.onZoomOut,k=n.onRotateRight,R=n.onRotateLeft,I=n.onFlipX,M=n.onFlipY,T=n.toolbarRender,Q=n.zIndex,F=r.useContext(kt),_=a.rotateLeft,$=a.rotateRight,B=a.zoomIn,D=a.zoomOut,O=a.close,W=a.left,E=a.right,P=a.flipX,G=a.flipY,re="".concat(l,"-operations-operation");r.useEffect(function(){var Y=function(U){U.keyCode===Zt.ESC&&y()};return o&&window.addEventListener("keydown",Y),function(){window.removeEventListener("keydown",Y)}},[o]);var oe=[{icon:G,onClick:M,type:"flipY"},{icon:P,onClick:I,type:"flipX"},{icon:_,onClick:R,type:"rotateLeft"},{icon:$,onClick:k,type:"rotateRight"},{icon:D,onClick:j,type:"zoomOut",disabled:x<=C},{icon:B,onClick:v,type:"zoomIn",disabled:x===m}],K=oe.map(function(Y){var J,U=Y.icon,N=Y.onClick,X=Y.type,te=Y.disabled;return r.createElement("div",{className:Me(re,(J={},Ie(J,"".concat(l,"-operations-operation-").concat(X),!0),Ie(J,"".concat(l,"-operations-operation-disabled"),!!te),J)),onClick:N,key:X},U)}),se=r.createElement("div",{className:"".concat(l,"-operations")},K);return r.createElement(Go,{visible:o,motionName:i},function(Y){var J=Y.className,U=Y.style;return r.createElement(Yo,{open:!0,getContainer:s??document.body},r.createElement("div",{className:Me("".concat(l,"-operations-wrapper"),J,d),style:ee(ee({},U),{},{zIndex:Q})},b===null?null:r.createElement("button",{className:"".concat(l,"-close"),onClick:y},b||O),u&&r.createElement(r.Fragment,null,r.createElement("div",{className:Me("".concat(l,"-switch-left"),Ie({},"".concat(l,"-switch-left-disabled"),g===0)),onClick:L},W),r.createElement("div",{className:Me("".concat(l,"-switch-right"),Ie({},"".concat(l,"-switch-right-disabled"),g===f-1)),onClick:w},E)),r.createElement("div",{className:"".concat(l,"-footer")},h&&r.createElement("div",{className:"".concat(l,"-progress")},c?c(g+1,f):"".concat(g+1," / ").concat(f)),T?T(se,ee({icons:{flipYIcon:K[0],flipXIcon:K[1],rotateLeftIcon:K[2],rotateRightIcon:K[3],zoomOutIcon:K[4],zoomInIcon:K[5]},actions:{onFlipY:M,onFlipX:I,onRotateLeft:R,onRotateRight:k,onZoomOut:j,onZoomIn:v},transform:p},F?{current:g,total:f}:{})):se)))})},Ua=["fallback","src","imgRef"],Ga=["prefixCls","src","alt","fallback","movable","onClose","visible","icons","rootClassName","closeIcon","getContainer","current","count","countRender","scaleStep","minScale","maxScale","transitionName","maskTransitionName","imageRender","imgCommonProps","toolbarRender","onTransform","onChange"],Ya=function(n){var o=n.fallback,i=n.src,s=n.imgRef,l=ct(n,Ua),d=Ro({src:i,fallback:o}),a=ie(d,2),c=a[0],u=a[1];return V.createElement("img",we({ref:function(g){s.current=g,c(g)}},l,u))},Mo=function(n){var o=n.prefixCls,i=n.src,s=n.alt,l=n.fallback,d=n.movable,a=d===void 0?!0:d,c=n.onClose,u=n.visible,h=n.icons,g=h===void 0?{}:h,p=n.rootClassName,f=n.closeIcon,x=n.getContainer,C=n.current,m=C===void 0?0:C,b=n.count,L=b===void 0?1:b,w=n.countRender,y=n.scaleStep,v=y===void 0?.5:y,j=n.minScale,k=j===void 0?1:j,R=n.maxScale,I=R===void 0?50:R,M=n.transitionName,T=M===void 0?"zoom":M,Q=n.maskTransitionName,F=Q===void 0?"fade":Q,_=n.imageRender,$=n.imgCommonProps,B=n.toolbarRender,D=n.onTransform,O=n.onChange,W=ct(n,Ga),E=r.useRef(),P=r.useContext(kt),G=P&&L>1,re=P&&L>=1,oe=r.useState(!0),K=ie(oe,2),se=K[0],Y=K[1],J=Ba(E,k,I,D),U=J.transform,N=J.resetTransform,X=J.updateTransform,te=J.dispatchZoomChange,Re=Va(E,a,u,v,U,X,te),$e=Re.isMoving,_e=Re.onMouseDown,Be=Re.onWheel,ce=Za(E,a,u,k,U,X,te),A=ce.isTouching,ae=ce.onTouchStart,z=ce.onTouchMove,ye=ce.onTouchEnd,De=U.rotate,Te=U.scale,Qe=Me(Ie({},"".concat(o,"-moving"),$e));r.useEffect(function(){se||Y(!0)},[se]);var Ye=function(){N("close")},Xe=function(){te(lt+v,"zoomIn")},qe=function(){te(lt/(lt+v),"zoomOut")},je=function(){X({rotate:De+90},"rotateRight")},ge=function(){X({rotate:De-90},"rotateLeft")},st=function(){X({flipX:!U.flipX},"flipX")},de=function(){X({flipY:!U.flipY},"flipY")},pe=function(Z){Z==null||Z.preventDefault(),Z==null||Z.stopPropagation(),m>0&&(Y(!1),N("prev"),O==null||O(m-1,m))},S=function(Z){Z==null||Z.preventDefault(),Z==null||Z.stopPropagation(),m<L-1&&(Y(!1),N("next"),O==null||O(m+1,m))},at=function(Z){!u||!G||(Z.keyCode===Zt.LEFT?pe():Z.keyCode===Zt.RIGHT&&S())},Oe=function(Z){u&&(Te!==1?X({x:0,y:0,scale:1},"doubleClick"):te(lt+v,"doubleClick",Z.clientX,Z.clientY))};r.useEffect(function(){var ne=it(window,"keydown",at,!1);return function(){ne.remove()}},[u,G,m]);var Ee=V.createElement(Ya,we({},$,{width:n.width,height:n.height,imgRef:E,className:"".concat(o,"-img"),alt:s,style:{transform:"translate3d(".concat(U.x,"px, ").concat(U.y,"px, 0) scale3d(").concat(U.flipX?"-":"").concat(Te,", ").concat(U.flipY?"-":"").concat(Te,", 1) rotate(").concat(De,"deg)"),transitionDuration:(!se||A)&&"0s"},fallback:l,src:i,onWheel:Be,onMouseDown:_e,onDoubleClick:Oe,onTouchStart:ae,onTouchMove:z,onTouchEnd:ye,onTouchCancel:ye}));return V.createElement(V.Fragment,null,V.createElement(Xo,we({transitionName:T,maskTransitionName:F,closable:!1,keyboard:!0,prefixCls:o,onClose:c,visible:u,classNames:{wrapper:Qe},rootClassName:p,getContainer:x},W,{afterClose:Ye}),V.createElement("div",{className:"".concat(o,"-img-wrapper")},_?_(Ee,ee({transform:U},P?{current:m}:{})):Ee)),V.createElement(Wa,{visible:u,transform:U,maskTransitionName:F,closeIcon:f,getContainer:x,prefixCls:o,rootClassName:p,icons:g,countRender:w,showSwitch:G,showProgress:re,current:m,count:L,scale:Te,minScale:k,maxScale:I,toolbarRender:B,onSwitchLeft:pe,onSwitchRight:S,onZoomIn:Xe,onZoomOut:qe,onRotateRight:je,onRotateLeft:ge,onFlipX:st,onFlipY:de,onClose:c,zIndex:W.zIndex!==void 0?W.zIndex+1:void 0}))};function Xa(e){var n=r.useState({}),o=ie(n,2),i=o[0],s=o[1],l=r.useCallback(function(a,c){return s(function(u){return ee(ee({},u),{},Ie({},a,c))}),function(){s(function(u){var h=ee({},u);return delete h[a],h})}},[]),d=r.useMemo(function(){return e?e.map(function(a){if(typeof a=="string")return{data:{src:a}};var c={};return Object.keys(a).forEach(function(u){["src"].concat(qo(Jt)).includes(u)&&(c[u]=a[u])}),{data:c}}):Object.keys(i).reduce(function(a,c){var u=i[c],h=u.canPreview,g=u.data;return h&&a.push({data:g,id:c}),a},[])},[e,i]);return[d,l]}var qa=["visible","onVisibleChange","getContainer","current","movable","minScale","maxScale","countRender","closeIcon","onChange","onTransform","toolbarRender","imageRender"],Ka=["src"],Ja=function(n){var o,i=n.previewPrefixCls,s=i===void 0?"rc-image-preview":i,l=n.children,d=n.icons,a=d===void 0?{}:d,c=n.items,u=n.preview,h=n.fallback,g=Xn(u)==="object"?u:{},p=g.visible,f=g.onVisibleChange,x=g.getContainer,C=g.current,m=g.movable,b=g.minScale,L=g.maxScale,w=g.countRender,y=g.closeIcon,v=g.onChange,j=g.onTransform,k=g.toolbarRender,R=g.imageRender,I=ct(g,qa),M=Xa(c),T=ie(M,2),Q=T[0],F=T[1],_=Wt(0,{value:C}),$=ie(_,2),B=$[0],D=$[1],O=r.useState(!1),W=ie(O,2),E=W[0],P=W[1],G=((o=Q[B])===null||o===void 0?void 0:o.data)||{},re=G.src,oe=ct(G,Ka),K=Wt(!!p,{value:p,onChange:function(A,ae){f==null||f(A,ae,B)}}),se=ie(K,2),Y=se[0],J=se[1],U=r.useState(null),N=ie(U,2),X=N[0],te=N[1],Re=r.useCallback(function(ce,A,ae){var z=Q.findIndex(function(ye){return ye.id===ce});J(!0),te({x:A,y:ae}),D(z<0?0:z),P(!0)},[Q]);r.useEffect(function(){Y?E||D(0):P(!1)},[Y]);var $e=function(A,ae){D(A),v==null||v(A,ae)},_e=function(){J(!1),te(null)},Be=r.useMemo(function(){return{register:F,onPreview:Re}},[F,Re]);return r.createElement(kt.Provider,{value:Be},l,r.createElement(Mo,we({"aria-hidden":!Y,movable:m,visible:Y,prefixCls:s,closeIcon:y,onClose:_e,mousePosition:X,imgCommonProps:oe,src:re,fallback:h,icons:a,minScale:b,maxScale:L,getContainer:x,current:B,count:Q.length,countRender:w,onTransform:j,toolbarRender:k,imageRender:R,onChange:$e},I)))},er=["src","alt","onPreviewClose","prefixCls","previewPrefixCls","placeholder","fallback","width","height","style","preview","className","onClick","onError","wrapperClassName","wrapperStyle","rootClassName"],tr=["src","visible","onVisibleChange","getContainer","mask","maskClassName","movable","icons","scaleStep","minScale","maxScale","imageRender","toolbarRender"],zt=function(n){var o=n.src,i=n.alt,s=n.onPreviewClose,l=n.prefixCls,d=l===void 0?"rc-image":l,a=n.previewPrefixCls,c=a===void 0?"".concat(d,"-preview"):a,u=n.placeholder,h=n.fallback,g=n.width,p=n.height,f=n.style,x=n.preview,C=x===void 0?!0:x,m=n.className,b=n.onClick,L=n.onError,w=n.wrapperClassName,y=n.wrapperStyle,v=n.rootClassName,j=ct(n,er),k=u&&u!==!0,R=Xn(C)==="object"?C:{},I=R.src,M=R.visible,T=M===void 0?void 0:M,Q=R.onVisibleChange,F=Q===void 0?s:Q,_=R.getContainer,$=_===void 0?void 0:_,B=R.mask,D=R.maskClassName,O=R.movable,W=R.icons,E=R.scaleStep,P=R.minScale,G=R.maxScale,re=R.imageRender,oe=R.toolbarRender,K=ct(R,tr),se=I??o,Y=Wt(!!T,{value:T,onChange:F}),J=ie(Y,2),U=J[0],N=J[1],X=Ro({src:o,isCustomPlaceholder:k,fallback:h}),te=ie(X,3),Re=te[0],$e=te[1],_e=te[2],Be=r.useState(null),ce=ie(Be,2),A=ce[0],ae=ce[1],z=r.useContext(kt),ye=!!C,De=function(){N(!1),ae(null)},Te=Me(d,w,v,Ie({},"".concat(d,"-error"),_e==="error")),Qe=r.useMemo(function(){var je={};return Jt.forEach(function(ge){n[ge]!==void 0&&(je[ge]=n[ge])}),je},Jt.map(function(je){return n[je]})),Ye=r.useMemo(function(){return ee(ee({},Qe),{},{src:se})},[se,Qe]),Xe=Na(ye,Ye),qe=function(ge){var st=Ko(ge.target),de=st.left,pe=st.top;z?z.onPreview(Xe,de,pe):(ae({x:de,y:pe}),N(!0)),b==null||b(ge)};return r.createElement(r.Fragment,null,r.createElement("div",we({},j,{className:Te,onClick:ye?qe:b,style:ee({width:g,height:p},y)}),r.createElement("img",we({},Qe,{className:Me("".concat(d,"-img"),Ie({},"".concat(d,"-img-placeholder"),u===!0),m),style:ee({height:p},f),ref:Re},$e,{width:g,height:p,onError:L})),_e==="loading"&&r.createElement("div",{"aria-hidden":"true",className:"".concat(d,"-placeholder")},u),B&&ye&&r.createElement("div",{className:Me("".concat(d,"-mask"),D),style:{display:(f==null?void 0:f.display)==="none"?"none":void 0}},B)),!z&&ye&&r.createElement(Mo,we({"aria-hidden":!U,visible:U,prefixCls:c,onClose:De,mousePosition:A,src:se,alt:i,fallback:h,getContainer:$,icons:W,movable:O,scaleStep:E,minScale:P,maxScale:G,rootClassName:v,imageRender:re,imgCommonProps:Qe,toolbarRender:oe},K)))};zt.PreviewGroup=Ja;zt.displayName="Image";var nr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H188V494h440v326z"}},{tag:"path",attrs:{d:"M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7.4 12.6-6.1v-63.9c12.9.1 25.9.9 38.8 2.5 42.1 5.2 82.1 18.2 119 38.7 38.1 21.2 71.2 49.7 98.4 84.3 27.1 34.7 46.7 73.7 58.1 115.8a325.95 325.95 0 016.5 140.9h74.9c14.8-103.6-11.3-213-81-302.3z"}}]},name:"rotate-left",theme:"outlined"},or=function(n,o){return r.createElement(St,we({},n,{ref:o,icon:nr}))},sr=r.forwardRef(or),ar={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-.4-12.6 6.1l-.2 64c-118.6.5-235.8 53.4-314.6 154.2A399.75 399.75 0 00123.5 631h74.9c-.9-5.3-1.7-10.7-2.4-16.1-5.1-42.1-2.1-84.1 8.9-124.8 11.4-42.2 31-81.1 58.1-115.8 27.2-34.7 60.3-63.2 98.4-84.3 37-20.6 76.9-33.6 119.1-38.8z"}},{tag:"path",attrs:{d:"M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H396V494h440v326z"}}]},name:"rotate-right",theme:"outlined"},rr=function(n,o){return r.createElement(St,we({},n,{ref:o,icon:ar}))},ir=r.forwardRef(rr),lr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"},cr=function(n,o){return r.createElement(St,we({},n,{ref:o,icon:lr}))},Bn=r.forwardRef(cr),dr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-in",theme:"outlined"},ur=function(n,o){return r.createElement(St,we({},n,{ref:o,icon:dr}))},To=r.forwardRef(ur),mr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h312c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-out",theme:"outlined"},hr=function(n,o){return r.createElement(St,we({},n,{ref:o,icon:mr}))},fr=r.forwardRef(hr);const en=e=>({position:e||"absolute",inset:0}),gr=e=>{const{iconCls:n,motionDurationSlow:o,paddingXXS:i,marginXXS:s,prefixCls:l,colorTextLightSolid:d}=e;return{position:"absolute",inset:0,display:"flex",alignItems:"center",justifyContent:"center",color:d,background:new Ke("#000").setAlpha(.5).toRgbString(),cursor:"pointer",opacity:0,transition:`opacity ${o}`,[`.${l}-mask-info`]:Object.assign(Object.assign({},os),{padding:`0 ${qn(i)}`,[n]:{marginInlineEnd:s,svg:{verticalAlign:"baseline"}}})}},pr=e=>{const{previewCls:n,modalMaskBg:o,paddingSM:i,marginXL:s,margin:l,paddingLG:d,previewOperationColorDisabled:a,previewOperationHoverColor:c,motionDurationSlow:u,iconCls:h,colorTextLightSolid:g}=e,p=new Ke(o).setAlpha(.1),f=p.clone().setAlpha(.2);return{[`${n}-footer`]:{position:"fixed",bottom:s,left:{_skip_check_:!0,value:0},width:"100%",display:"flex",flexDirection:"column",alignItems:"center",color:e.previewOperationColor},[`${n}-progress`]:{marginBottom:l},[`${n}-close`]:{position:"fixed",top:s,right:{_skip_check_:!0,value:s},display:"flex",color:g,backgroundColor:p.toRgbString(),borderRadius:"50%",padding:i,outline:0,border:0,cursor:"pointer",transition:`all ${u}`,"&:hover":{backgroundColor:f.toRgbString()},[`& > ${h}`]:{fontSize:e.previewOperationSize}},[`${n}-operations`]:{display:"flex",alignItems:"center",padding:`0 ${qn(d)}`,backgroundColor:p.toRgbString(),borderRadius:100,"&-operation":{marginInlineStart:i,padding:i,cursor:"pointer",transition:`all ${u}`,userSelect:"none",[`&:not(${n}-operations-operation-disabled):hover > ${h}`]:{color:c},"&-disabled":{color:a,cursor:"not-allowed"},"&:first-of-type":{marginInlineStart:0},[`& > ${h}`]:{fontSize:e.previewOperationSize}}}}},xr=e=>{const{modalMaskBg:n,iconCls:o,previewOperationColorDisabled:i,previewCls:s,zIndexPopup:l,motionDurationSlow:d}=e,a=new Ke(n).setAlpha(.1),c=a.clone().setAlpha(.2);return{[`${s}-switch-left, ${s}-switch-right`]:{position:"fixed",insetBlockStart:"50%",zIndex:e.calc(l).add(1).equal({unit:!1}),display:"flex",alignItems:"center",justifyContent:"center",width:e.imagePreviewSwitchSize,height:e.imagePreviewSwitchSize,marginTop:e.calc(e.imagePreviewSwitchSize).mul(-1).div(2).equal(),color:e.previewOperationColor,background:a.toRgbString(),borderRadius:"50%",transform:"translateY(-50%)",cursor:"pointer",transition:`all ${d}`,userSelect:"none","&:hover":{background:c.toRgbString()},"&-disabled":{"&, &:hover":{color:i,background:"transparent",cursor:"not-allowed",[`> ${o}`]:{cursor:"not-allowed"}}},[`> ${o}`]:{fontSize:e.previewOperationSize}},[`${s}-switch-left`]:{insetInlineStart:e.marginSM},[`${s}-switch-right`]:{insetInlineEnd:e.marginSM}}},vr=e=>{const{motionEaseOut:n,previewCls:o,motionDurationSlow:i,componentCls:s}=e;return[{[`${s}-preview-root`]:{[o]:{height:"100%",textAlign:"center",pointerEvents:"none"},[`${o}-body`]:Object.assign(Object.assign({},en()),{overflow:"hidden"}),[`${o}-img`]:{maxWidth:"100%",maxHeight:"70%",verticalAlign:"middle",transform:"scale3d(1, 1, 1)",cursor:"grab",transition:`transform ${i} ${n} 0s`,userSelect:"none","&-wrapper":Object.assign(Object.assign({},en()),{transition:`transform ${i} ${n} 0s`,display:"flex",justifyContent:"center",alignItems:"center","& > *":{pointerEvents:"auto"},"&::before":{display:"inline-block",width:1,height:"50%",marginInlineEnd:-1,content:'""'}})},[`${o}-moving`]:{[`${o}-preview-img`]:{cursor:"grabbing","&-wrapper":{transitionDuration:"0s"}}}}},{[`${s}-preview-root`]:{[`${o}-wrap`]:{zIndex:e.zIndexPopup}}},{[`${s}-preview-operations-wrapper`]:{position:"fixed",zIndex:e.calc(e.zIndexPopup).add(1).equal({unit:!1})},"&":[pr(e),xr(e)]}]},yr=e=>{const{componentCls:n}=e;return{[n]:{position:"relative",display:"inline-block",[`${n}-img`]:{width:"100%",height:"auto",verticalAlign:"middle"},[`${n}-img-placeholder`]:{backgroundColor:e.colorBgContainerDisabled,backgroundImage:"url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQuNSAyLjVoLTEzQS41LjUgMCAwIDAgMSAzdjEwYS41LjUgMCAwIDAgLjUuNWgxM2EuNS41IDAgMCAwIC41LS41VjNhLjUuNSAwIDAgMC0uNS0uNXpNNS4yODEgNC43NWExIDEgMCAwIDEgMCAyIDEgMSAwIDAgMSAwLTJ6bTguMDMgNi44M2EuMTI3LjEyNyAwIDAgMS0uMDgxLjAzSDIuNzY5YS4xMjUuMTI1IDAgMCAxLS4wOTYtLjIwN2wyLjY2MS0zLjE1NmEuMTI2LjEyNiAwIDAgMSAuMTc3LS4wMTZsLjAxNi4wMTZMNy4wOCAxMC4wOWwyLjQ3LTIuOTNhLjEyNi4xMjYgMCAwIDEgLjE3Ny0uMDE2bC4wMTUuMDE2IDMuNTg4IDQuMjQ0YS4xMjcuMTI3IDAgMCAxLS4wMi4xNzV6IiBmaWxsPSIjOEM4QzhDIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=')",backgroundRepeat:"no-repeat",backgroundPosition:"center center",backgroundSize:"30%"},[`${n}-mask`]:Object.assign({},gr(e)),[`${n}-mask:hover`]:{opacity:1},[`${n}-placeholder`]:Object.assign({},en())}}},Cr=e=>{const{previewCls:n}=e;return{[`${n}-root`]:ns(e,"zoom"),"&":ts(e,!0)}},br=e=>({zIndexPopup:e.zIndexPopupBase+80,previewOperationColor:new Ke(e.colorTextLightSolid).setAlpha(.65).toRgbString(),previewOperationHoverColor:new Ke(e.colorTextLightSolid).setAlpha(.85).toRgbString(),previewOperationColorDisabled:new Ke(e.colorTextLightSolid).setAlpha(.25).toRgbString(),previewOperationSize:e.fontSizeIcon*1.5}),Qo=Jo("Image",e=>{const n=`${e.componentCls}-preview`,o=Ln(e,{previewCls:n,modalMaskBg:new Ke("#000").setAlpha(.45).toRgbString(),imagePreviewSwitchSize:e.controlHeightLG});return[yr(o),vr(o),es(Ln(o,{componentCls:n})),Cr(o)]},br);var Sr=function(e,n){var o={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0&&(o[i]=e[i]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,i=Object.getOwnPropertySymbols(e);s<i.length;s++)n.indexOf(i[s])<0&&Object.prototype.propertyIsEnumerable.call(e,i[s])&&(o[i[s]]=e[i[s]]);return o};const Eo={rotateLeft:r.createElement(sr,null),rotateRight:r.createElement(ir,null),zoomIn:r.createElement(To,null),zoomOut:r.createElement(fr,null),close:r.createElement(rs,null),left:r.createElement(as,null),right:r.createElement(ss,null),flipX:r.createElement(Bn,null),flipY:r.createElement(Bn,{rotate:90})},jr=e=>{var{previewPrefixCls:n,preview:o}=e,i=Sr(e,["previewPrefixCls","preview"]);const{getPrefixCls:s}=r.useContext(Kn),l=s("image",n),d=`${l}-preview`,a=s(),c=Jn(l),[u,h,g]=Qo(l,c),[p]=eo("ImagePreview",typeof o=="object"?o.zIndex:void 0),f=r.useMemo(()=>{var x;if(o===!1)return o;const C=typeof o=="object"?o:{},m=Me(h,g,c,(x=C.rootClassName)!==null&&x!==void 0?x:"");return Object.assign(Object.assign({},C),{transitionName:Et(a,"zoom",C.transitionName),maskTransitionName:Et(a,"fade",C.maskTransitionName),rootClassName:m,zIndex:p})},[o]);return u(r.createElement(zt.PreviewGroup,Object.assign({preview:f,previewPrefixCls:d,icons:Eo},i)))};var zn=function(e,n){var o={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0&&(o[i]=e[i]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,i=Object.getOwnPropertySymbols(e);s<i.length;s++)n.indexOf(i[s])<0&&Object.prototype.propertyIsEnumerable.call(e,i[s])&&(o[i[s]]=e[i[s]]);return o};const Fo=e=>{const{prefixCls:n,preview:o,className:i,rootClassName:s,style:l}=e,d=zn(e,["prefixCls","preview","className","rootClassName","style"]),{getPrefixCls:a,locale:c=kn,getPopupContainer:u,image:h}=r.useContext(Kn),g=a("image",n),p=a(),f=c.Image||kn.Image,x=Jn(g),[C,m,b]=Qo(g,x),L=Me(s,m,b,x),w=Me(i,m,h==null?void 0:h.className),[y]=eo("ImagePreview",typeof o=="object"?o.zIndex:void 0),v=r.useMemo(()=>{if(o===!1)return o;const k=typeof o=="object"?o:{},{getContainer:R}=k,I=zn(k,["getContainer"]);return Object.assign(Object.assign({mask:r.createElement("div",{className:`${g}-mask-info`},r.createElement(is,null),f==null?void 0:f.preview),icons:Eo},I),{getContainer:R||u,transitionName:Et(p,"zoom",k.transitionName),maskTransitionName:Et(p,"fade",k.maskTransitionName),zIndex:y})},[o,f]),j=Object.assign(Object.assign({},h==null?void 0:h.style),l);return C(r.createElement(zt,Object.assign({prefixCls:g,preview:v,rootClassName:L,className:w,style:j},d)))};Fo.PreviewGroup=jr;const wr=new Map([["bold",r.createElement(r.Fragment,null,r.createElement("path",{d:"M232.49,215.51,185,168a92.12,92.12,0,1,0-17,17l47.53,47.54a12,12,0,0,0,17-17ZM44,112a68,68,0,1,1,68,68A68.07,68.07,0,0,1,44,112Z"}))],["duotone",r.createElement(r.Fragment,null,r.createElement("path",{d:"M192,112a80,80,0,1,1-80-80A80,80,0,0,1,192,112Z",opacity:"0.2"}),r.createElement("path",{d:"M229.66,218.34,179.6,168.28a88.21,88.21,0,1,0-11.32,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"}))],["fill",r.createElement(r.Fragment,null,r.createElement("path",{d:"M168,112a56,56,0,1,1-56-56A56,56,0,0,1,168,112Zm61.66,117.66a8,8,0,0,1-11.32,0l-50.06-50.07a88,88,0,1,1,11.32-11.31l50.06,50.06A8,8,0,0,1,229.66,229.66ZM112,184a72,72,0,1,0-72-72A72.08,72.08,0,0,0,112,184Z"}))],["light",r.createElement(r.Fragment,null,r.createElement("path",{d:"M228.24,219.76l-51.38-51.38a86.15,86.15,0,1,0-8.48,8.48l51.38,51.38a6,6,0,0,0,8.48-8.48ZM38,112a74,74,0,1,1,74,74A74.09,74.09,0,0,1,38,112Z"}))],["regular",r.createElement(r.Fragment,null,r.createElement("path",{d:"M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"}))],["thin",r.createElement(r.Fragment,null,r.createElement("path",{d:"M226.83,221.17l-52.7-52.7a84.1,84.1,0,1,0-5.66,5.66l52.7,52.7a4,4,0,0,0,5.66-5.66ZM36,112a76,76,0,1,1,76,76A76.08,76.08,0,0,1,36,112Z"}))]]),Lr=new Map([["bold",r.createElement(r.Fragment,null,r.createElement("path",{d:"M228,144v64a12,12,0,0,1-12,12H40a12,12,0,0,1-12-12V144a12,12,0,0,1,24,0v52H204V144a12,12,0,0,1,24,0ZM96.49,80.49,116,61v83a12,12,0,0,0,24,0V61l19.51,19.52a12,12,0,1,0,17-17l-40-40a12,12,0,0,0-17,0l-40,40a12,12,0,1,0,17,17Z"}))],["duotone",r.createElement(r.Fragment,null,r.createElement("path",{d:"M216,48V208H40V48A16,16,0,0,1,56,32H200A16,16,0,0,1,216,48Z",opacity:"0.2"}),r.createElement("path",{d:"M224,144v64a8,8,0,0,1-8,8H40a8,8,0,0,1-8-8V144a8,8,0,0,1,16,0v56H208V144a8,8,0,0,1,16,0ZM93.66,77.66,120,51.31V144a8,8,0,0,0,16,0V51.31l26.34,26.35a8,8,0,0,0,11.32-11.32l-40-40a8,8,0,0,0-11.32,0l-40,40A8,8,0,0,0,93.66,77.66Z"}))],["fill",r.createElement(r.Fragment,null,r.createElement("path",{d:"M224,144v64a8,8,0,0,1-8,8H40a8,8,0,0,1-8-8V144a8,8,0,0,1,16,0v56H208V144a8,8,0,0,1,16,0ZM88,80h32v64a8,8,0,0,0,16,0V80h32a8,8,0,0,0,5.66-13.66l-40-40a8,8,0,0,0-11.32,0l-40,40A8,8,0,0,0,88,80Z"}))],["light",r.createElement(r.Fragment,null,r.createElement("path",{d:"M222,144v64a6,6,0,0,1-6,6H40a6,6,0,0,1-6-6V144a6,6,0,0,1,12,0v58H210V144a6,6,0,0,1,12,0ZM92.24,76.24,122,46.49V144a6,6,0,0,0,12,0V46.49l29.76,29.75a6,6,0,0,0,8.48-8.48l-40-40a6,6,0,0,0-8.48,0l-40,40a6,6,0,0,0,8.48,8.48Z"}))],["regular",r.createElement(r.Fragment,null,r.createElement("path",{d:"M224,144v64a8,8,0,0,1-8,8H40a8,8,0,0,1-8-8V144a8,8,0,0,1,16,0v56H208V144a8,8,0,0,1,16,0ZM93.66,77.66,120,51.31V144a8,8,0,0,0,16,0V51.31l26.34,26.35a8,8,0,0,0,11.32-11.32l-40-40a8,8,0,0,0-11.32,0l-40,40A8,8,0,0,0,93.66,77.66Z"}))],["thin",r.createElement(r.Fragment,null,r.createElement("path",{d:"M220,144v64a4,4,0,0,1-4,4H40a4,4,0,0,1-4-4V144a4,4,0,0,1,8,0v60H212V144a4,4,0,0,1,8,0ZM90.83,74.83,124,41.66V144a4,4,0,0,0,8,0V41.66l33.17,33.17a4,4,0,1,0,5.66-5.66l-40-40a4,4,0,0,0-5.66,0l-40,40a4,4,0,0,0,5.66,5.66Z"}))]]),_o=r.forwardRef((e,n)=>r.createElement(to,{ref:n,...e,weights:wr}));_o.displayName="MagnifyingGlassIcon";const Vn=_o,Do=r.forwardRef((e,n)=>r.createElement(to,{ref:n,...e,weights:Lr}));Do.displayName="UploadSimpleIcon";const kr=Do,Rr=$s({displayName:"ExternalLinkIcon",path:t.jsxs("g",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeWidth:"2",children:[t.jsx("path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"}),t.jsx("path",{d:"M15 3h6v6"}),t.jsx("path",{d:"M10 14L21 3"})]})}),{Text:Hn}=ke,Zn=({str:e})=>{const[n,o]=V.useState(!1),i=()=>{navigator.clipboard.writeText(e),o(!0),Le.success("Copied!"),setTimeout(()=>o(!1),1500)};return t.jsxs("div",{style:{display:"flex",alignItems:"center",gap:8},children:[t.jsx("span",{children:e}),t.jsx(Ae,{title:n?"Copied!":"Copy",children:t.jsx(q,{size:"small",type:"link",icon:t.jsx(cs,{style:{color:"#14C9BB"}}),onClick:i})})]})},Ir=({ipv4:e,ipv6:n})=>{const o=e.length+n.length;return t.jsx(ls,{title:t.jsx("div",{style:{marginBottom:16},children:`${o} ${o===1?"IP":"IPs"}`}),trigger:"click",placement:"top",content:t.jsxs("div",{children:[t.jsxs("div",{children:[t.jsxs(Hn,{strong:!0,style:{fontSize:14},children:["IPv4 (",e.length,")"]}),e.length>0&&t.jsx(Mt,{size:"small",dataSource:e,renderItem:i=>t.jsx(Mt.Item,{style:{paddingLeft:0,paddingRight:0},children:t.jsx(Zn,{str:i})})})]}),t.jsxs("div",{children:[t.jsxs(Hn,{strong:!0,style:{fontSize:14},children:["IPv6 (",n.length,")"]}),n.length>0&&t.jsx(Mt,{size:"small",dataSource:n,renderItem:i=>t.jsx(Mt.Item,{style:{paddingLeft:0,paddingRight:0},children:t.jsx(Zn,{str:i})})})]})]}),children:t.jsx(q,{size:"small",type:"primary",children:o})})},{Title:Mr}=ke,Tr=r.forwardRef(({data:e=[],ouis:n={},isSingle:o},i)=>{const{t:s}=H(),[l,d]=r.useState([]),a=r.useMemo(()=>[{title:"",dataIndex:["radio","index"],key:"index",render:(u,h)=>{var g,p;return((g=h.radio)==null?void 0:g.band)??((p=h.radio)==null?void 0:p.deductedBand)??""},fixed:"left",defaultSortOrder:"descend",columnsFix:!0},{title:s("controller.wifi.station"),dataIndex:"station",key:"station",sorter:(u,h)=>u.station.localeCompare(h.station),columnsFix:!0},{title:"SSID",dataIndex:"ssid",key:"ssid",sorter:(u,h)=>u.ssid.localeCompare(h.ssid),columnsFix:!0},{title:"IPs",key:"ips",render:(u,h)=>t.jsx(Ir,{ipv4:h.ips.ipv4,ipv6:h.ips.ipv6})},{title:"Fingerprint",key:"fingerprint",render:(u,h)=>Object.values(h.fingerprint??{}).join(", ")},{title:s("controller.wifi.vendor"),key:"vendor",render:(u,h)=>n[h.station]??""},{title:"VLAN",key:"dynamicVlan",dataIndex:"dynamicVlan",render:u=>u!==void 0?`${u}`:"-",sorter:(u,h)=>u.dynamicVlan-h.dynamicVlan},{title:s("controller.wifi.mode"),dataIndex:"mode",key:"mode"},{title:"RSSI",dataIndex:"rssi",key:"rssi",sorter:(u,h)=>u.rssi-h.rssi},{title:s("controller.wifi.rx_rate"),dataIndex:"rxRate",key:"rxRate",sorter:(u,h)=>u.rxRate-h.rxRate},{title:"Rx",dataIndex:"rxBytes",key:"rxBytes",render:u=>t.jsx(Fn,{bytes:u}),sorter:(u,h)=>u.rxBytes-h.rxBytes},{title:"Rx MCS",dataIndex:"rxMcs",key:"rxMcs",sorter:(u,h)=>u.rxMcs-h.rxMcs},{title:"Rx NSS",dataIndex:"rxNss",key:"rxNss",sorter:(u,h)=>u.rxNss-h.rxNss},{title:s("controller.wifi.tx_rate"),dataIndex:"txRate",key:"txRate",sorter:(u,h)=>u.txRate-h.txRate},{title:"Tx",dataIndex:"txBytes",key:"txBytes",render:u=>t.jsx(Fn,{bytes:u}),sorter:(u,h)=>u.txBytes-h.txBytes}],[s,n]),c=a.map(u=>u.key);return t.jsxs(t.Fragment,{children:[t.jsx(At,{align:"center",style:{marginTop:24,marginBottom:4,width:"100%"},children:t.jsx(Mr,{level:5,style:{margin:0},children:o?"Association":`${s("devices.associations")} (${e.length})`})}),t.jsx(ot,{ref:i,tableId:"wifianalysis-associations-table",columns:a,dataSource:e,showColumnSelector:!0,visibleColumns:l.length?l:c,onVisibleColumnsChange:d,pagination:!1})]})}),{Title:Qr}=ke,Er=r.forwardRef(({data:e=[],isSingle:n},o)=>{const{t:i}=H(),s=r.useMemo(()=>[{title:"",dataIndex:"index",key:"index",render:(a,c)=>c.band??c.deductedBand,fixed:"left",columnsFix:!0},{title:"Ch.",dataIndex:"channel",key:"channel"},{title:"Ch. W",dataIndex:"channelWidth",key:"channelWidth"},{title:"Tx Pow.",dataIndex:"txPower",key:"txPower"},{title:i("controller.wifi.noise"),dataIndex:"noise",key:"noise"},{title:"Active (MS)",dataIndex:"activeMs",key:"activeMs"},{title:"Busy (MS)",dataIndex:"busyMs",key:"busyMs"},{title:"Receive (MS)",dataIndex:"receiveMs",key:"receiveMs"},{title:"Send (MS)",dataIndex:"sendMs",key:"sendMs"},{title:"Frequency",dataIndex:"frequency",key:"frequency"}],[i]),[l,d]=r.useState(s.map(a=>a.key));return t.jsxs(t.Fragment,{children:[t.jsx(At,{align:"center",style:{marginTop:24,marginBottom:4,width:"100%"},children:t.jsx(Qr,{level:5,style:{margin:0},children:n?"Radio":`${i("configurations.radios")} (${e.length})`})}),t.jsx(ot,{ref:o,tableId:"wifianalysis-radio-table",columns:s,dataSource:e,showColumnSelector:!0,visibleColumns:l,onVisibleColumnsChange:d,pagination:!1})]})}),Fr="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAoCAYAAACFFRgXAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAHnSURBVFiF1ZkxjhMxFIY/uwG6FLRIwwl2ucFwgz1COAF7g11OEDhBjpCKioKRqOiSEySCjmIzuw2sktVPMR5miDJJPPGu40+yIuV5lC/Oi+X3bOiJpBw4A3Igc2PQMb10YwoUwMwYU/T97IORlEkaS5rreJaSriV1fckgoo/BUtIomLikocKs6CHiF8fKjp5AdJNRH9GBpEkE2ZqxfFIksmzN5JTToIvd6SHpIrbhFoZtR9POW2BO9+YfiwXw1hizALCtwBUesrcPa37c/w4a6yBzbg2qdoWlz+/0ZvpNL79/0eebX8Fie8igWeFLPFPh5/0fAO4eVsFie7iCRvi979MRyAGsqlPXqf3RtpFJyi3VETEVzixuqRMht8B5bAsPMksa+VuTnPDA7p9zWliq4jAVSkt1uEiF5ISnlqpPkAqFBWaxLTyYWdeBWUQWOYTSGFPU21oR0+RAPkFzvPzg+/SrZ8/d64tgsR2UwMf/3pFnK6pcr1SuV0FjO/hXPbeL0Az4SlVDnRIl8NoYU0KrCHVVqXdqPAHvatmtKKVGSks6nVaVE47dDJyoT79YqbRbN6SH8myy9GSujT7aMdIDPe5qj+U6O0Fx4tcKs+LzPqJm/5RO+Zzm2uucqjbcde21cKPgiGuvv4t7oSrmGRhmAAAAAElFTkSuQmCC",{Title:Cl,Text:_r}=ke,Dr=(e,n)=>{var i,s,l,d,a,c,u,h,g,p,f,x;const o=[];if(n.data.radios)for(let C=0;C<n.data.radios.length;C+=1){const m=n.data.radios[C];let b=m==null?void 0:m.temperature;b&&(b=b>1e3?Math.round(b/1e3):b);const L=(m==null?void 0:m.noise)??((s=(i=m==null?void 0:m.survey)==null?void 0:i[0])==null?void 0:s.noise),w=L?an(L):"-",y=((d=(l=m==null?void 0:m.survey)==null?void 0:l[0])==null?void 0:d.time)??(m==null?void 0:m.active_ms),v=(y==null?void 0:y.toLocaleString())??"-",j=((c=(a=m==null?void 0:m.survey)==null?void 0:a[0])==null?void 0:c.busy)??(m==null?void 0:m.busy_ms),k=(j==null?void 0:j.toLocaleString())??"-",R=((h=(u=m==null?void 0:m.survey)==null?void 0:u[0])==null?void 0:h.time_rx)??(m==null?void 0:m.receive_ms),I=(R==null?void 0:R.toLocaleString())??"-",M=(p=(g=m==null?void 0:m.survey)==null?void 0:g[0])==null?void 0:p.time_tx,T=(M==null?void 0:M.toLocaleString())??"-";m&&o.push({recorded:n.recorded,index:C,band:(f=m.band)==null?void 0:f[0],deductedBand:m.channel&&m.channel>16?"5G":"2G",channel:m.channel,channelWidth:m.channel_width,noise:w,txPower:m.tx_power??"-",activeMs:v,busyMs:k,receiveMs:I,sendMs:T,phy:m.phy,temperature:b?b.toString():"-",frequency:((x=m.frequency)==null?void 0:x.join(", "))??"-"})}return o},Or=(e,n)=>{var i,s,l;const o=[];for(const d of e.data.interfaces??[])for(const a of d.ssids??[]){let c;if(a.phy){const u=n.find(h=>h.phy===a.phy);u&&(c=u)}if(!c){const u=(s=(i=a.radio)==null?void 0:i.$ref)==null?void 0:s.split("/").pop();if(u){const h=n[parseInt(u,10)];h&&(c=h)}}for(const u of a.associations??[]){const h=(l=d.clients)==null?void 0:l.find(({mac:g})=>g===u.station);o.push({radio:c,ips:{ipv4:(h==null?void 0:h.ipv4_addresses)??[],ipv6:(h==null?void 0:h.ipv6_addresses)??[]},station:u.station,ssid:a.ssid,rssi:u.rssi?an(u.rssi):"-",mode:a.mode,rxBytes:u.rx_bytes,rxRate:u.rx_rate.bitrate,rxMcs:u.rx_rate.mcs??"-",rxNss:u.rx_rate.nss??"-",txBytes:u.tx_bytes,txRate:u.tx_rate.bitrate,txMcs:u.tx_rate.mcs??"-",txNss:u.tx_rate.nss??"-",recorded:e.recorded,dynamicVlan:u.dynamic_vlan,fingerprint:u.fingerprint})}}return o},Pr=({serialNumber:e})=>{var u,h,g,p,f,x,C,m;const{t:n}=H(),[o,i]=V.useState(0),s=Bs({serialNumber:e,limit:30}),l=V.useMemo(()=>{if(!s.data)return;const b=[];for(const L of s.data.data){const w=Dr(n,L),y=Or(L,w);b.push({radios:w,associations:y})}return b.reverse()},[s.data]),d=zs({macs:(h=(u=l==null?void 0:l[o])==null?void 0:u.associations)==null?void 0:h.map(b=>b.station)}),a=V.useMemo(()=>{if(!d.data)return;const b={};for(const L of d.data.tagList)b[L.tag]=L.value;return b},[l,d.data]),c=b=>{i(b)};return V.useEffect(()=>{l&&i(l.length-1)},[l]),t.jsx(t.Fragment,{children:t.jsxs("div",{children:[t.jsxs(_r,{children:["When:"," ",l&&((p=(g=l[o])==null?void 0:g.radios[0])==null?void 0:p.recorded)!==void 0?t.jsx(Ze,{date:(x=(f=l[o])==null?void 0:f.radios[0])==null?void 0:x.recorded}):"-"]}),l&&t.jsxs(Vs,{mt:"20px",step:1,value:o,max:l.length===0?0:l.length-1,onChange:c,focusThumbOnChange:!1,children:[t.jsx(Hs,{h:"14px",borderRadius:"mini",bg:"#F0F2F5",children:t.jsx(Zs,{background:"linear-gradient(180deg, rgba(254,255,255,0.0784) 0%, rgba(206,224,255,0.9531) 0%, rgba(157,247,240,0.302) 99%)"})}),t.jsx(Ws,{boxSize:5,zIndex:0,boxShadow:"0 4px 8px rgba(0, 0, 0, 0.3)",children:t.jsx(bo,{src:Fr,alt:"thumb",boxSize:"100%",objectFit:"cover",borderRadius:"full"})})]}),t.jsxs("div",{style:{width:"100%"},children:[t.jsx(Er,{data:(C=l==null?void 0:l[o])==null?void 0:C.radios}),t.jsx(Tr,{data:(m=l==null?void 0:l[o])==null?void 0:m.associations,ouis:a})]})]})})},Ar=()=>no(({serialNumber:e,compatible:n,keepRedirector:o})=>ds.get(`firmwares?deviceType=${n}&latestOnly=true`).then(i=>{const s=i.data.uri;return Ne.post(`device/${e}/upgrade`,{serialNumber:e,when:0,keepRedirector:o,uri:s})})),Nr=e=>r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:16,height:16,viewBox:"0 0 16 16",...e},r.createElement("defs",null,r.createElement("clipPath",{id:"master_svg0_464_13374"},r.createElement("rect",{x:0,y:0,width:16,height:16,rx:0}))),r.createElement("g",{clipPath:"url(#master_svg0_464_13374)"},r.createElement("g",null,r.createElement("g",null,r.createElement("path",{d:"M2.4523809,2.5L13.5,2.5Q14.452381,2.5,14.452381,3.4523809Q14.452381,4.404762,13.5,4.404762L2.4523809,4.404762Q1.5,4.404762,1.5,3.45238113Q1.5,2.50000023841858,2.4523809,2.5Z",fill:"#14C9BB",fillOpacity:1,style:{mixBlendMode:"passthrough"}})),r.createElement("g",null,r.createElement("path",{d:"M2.4523809,7L13.5,7Q14.452381,7,14.452381,7.95238113Q14.452381,8.9047618,13.5,8.9047618L2.4523809,8.9047618Q1.5,8.9047618,1.5,7.95238066Q1.5,6.99999952316284,2.4523809,7Z",fill:"#14C9BB",fillOpacity:1,style:{mixBlendMode:"passthrough"}})),r.createElement("g",null,r.createElement("path",{d:"M2.4523809,11.5L13.5,11.5Q14.452381,11.5,14.452381,12.45238113Q14.452381,13.4047623,13.5,13.4047623L2.4523809,13.4047623Q1.5,13.4047623,1.5,12.45238113Q1.5,11.5,2.4523809,11.5Z",fill:"#14C9BB",fillOpacity:1,style:{mixBlendMode:"passthrough"}}))))),$r=e=>r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:16,height:16,viewBox:"0 0 16 16",...e},r.createElement("defs",null,r.createElement("clipPath",{id:"master_svg0_464_13379"},r.createElement("rect",{x:0,y:0,width:16,height:16,rx:0}))),r.createElement("g",{clipPath:"url(#master_svg0_464_13379)"},r.createElement("g",null,r.createElement("g",null,r.createElement("g",null,r.createElement("path",{d:"M12.625,3.37500024C14.075,4.5750003,15,6.3750005000000005,15,8.4000001C15,12,12.1,14.9,8.5,14.9C4.8999996,14.9,2,12,2,8.4000001C2,6.375,2.92499995,4.5750003,4.375,3.375C4.4749999,3.299999952,4.5749998000000005,3.25,4.6999998000000005,3.25C4.9749999,3.25,5.1999998000000005,3.4749999,5.1999998000000005,3.75C5.1999998000000005,3.92499995,5.0999999,4.07500005,4.9749999,4.17499995C3.75,5.1749997,2.9749999000000003,6.7000003,2.9749999000000003,8.4249997C2.9749999000000003,11.4750004,5.4250001999999995,13.924999,8.4749999,13.924999C11.5249996,13.924999,13.975,11.4750004,13.975,8.4249997C13.975,6.6999998000000005,13.175,5.1499996,11.9250002,4.12499952C11.8249998,4.02499962,11.7750006,3.89999962,11.7750006,3.77499962C11.7750006,3.49999952,12.000001,3.274999619,12.275001,3.274999619C12.425,3.24999952316284,12.55,3.299999714,12.625,3.37500024Z",fill:"#14C9BB",fillOpacity:1,style:{mixBlendMode:"passthrough"}}),r.createElement("path",{d:"M12.245879,2.92543343Q11.9133739,2.93540087,11.674387,3.174387164Q11.4250002,3.42377448,11.4250002,3.7749995Q11.4250002,4.11997586,11.6775131,4.3724875L11.6897221,4.3846958L11.7030735,4.3956438Q13.624999,5.9716227,13.624999,8.4249978Q13.624999,10.5675244,12.121262,12.0712614Q10.6175251,13.574999,8.4749999,13.574998Q6.3324733,13.574998,4.8287373,12.0712614Q3.3249998,10.5675244,3.3249997000000002,8.4249992Q3.3249999,5.9737489,5.1936431,4.4483041Q5.5499995,4.1632190300000005,5.5499995,3.74999997Q5.5499995,3.39877526,5.300612,3.14938766Q5.0512245,2.90000001,4.6999996,2.90000001Q4.4249995,2.90000004,4.1649997,3.09500021L4.1583014,3.10002384L4.151851199999999,3.1053629799999998Q1.65000001,5.17586,1.65000001,8.400002Q1.6500000400000001,11.2449756,3.6525124,13.2474871Q5.6550248,15.249999,8.500001000000001,15.249998Q11.3449764,15.249998,13.347487,13.2474861Q15.349998,11.244974599999999,15.349998,8.399999600000001Q15.349998,5.1830432,12.859284,3.1145943Q12.609348,2.87536195,12.245879,2.92543343ZM12.303967,3.62499958L12.332541,3.62023738Q12.369159,3.61413428,12.377511,3.62248692L12.38916,3.63413534L12.401852,3.64463821Q14.65,5.5051749,14.65,8.399999600000001Q14.65,10.9550233,12.852512,12.752511Q11.0550261,14.549998,8.500001000000001,14.549998Q5.9449739,14.549998,4.1474869000000005,12.752512Q2.35000014,10.955026100000001,2.34999999,8.400002Q2.34999999,5.5097203,4.5910962,3.65048286Q4.6600807,3.59999999,4.6999996,3.59999999Q4.8499997,3.59999999,4.8499997,3.74999997Q4.8499997,3.82678157,4.7563566999999995,3.90169585Q2.625,5.6415565,2.62499982,8.4249992Q2.62499988,10.857473899999999,4.3337626,12.5662365Q6.0425243,14.274999,8.4749999,14.274999Q10.9074745,14.274999,12.616237,12.5662365Q14.324999,10.8574748,14.324999,8.4249978Q14.324999,5.6509995,12.163519,3.86800086Q12.125001,3.82455844,12.125001,3.7749995Q12.125001,3.62499964,12.275,3.62499964L12.303967,3.62499958Z",fillRule:"evenodd",fill:"#14C9BB",fillOpacity:1})),r.createElement("g",null,r.createElement("path",{d:"M8.5,2C8.77499962,2,9,2.22500002,9,2.5L9,9.5C9,9.774999600000001,8.77499962,10,8.5,10C8.22500038,10,8,9.774999600000001,8,9.5L8,2.50000024C8,2.22500026,8.2249999,2.00000023841858,8.5,2Z",fill:"#14C9BB",fillOpacity:1,style:{mixBlendMode:"passthrough"}}),r.createElement("path",{d:"M7.89938754,1.8993879Q7.65000001,2.14877558,7.65000001,2.50000024L7.65000001,9.5Q7.64999998,9.8512239,7.89938778,10.1006117Q8.14877647,10.3500004,8.5,10.3500004Q8.85122347,10.3500004,9.1006122,10.1006117Q9.35,9.8512239,9.35,9.5L9.35,2.5Q9.35,2.14877531,9.1006122,1.89938758Q8.85122442,1.65000001,8.4999997,1.65000001Q8.148775,1.6500003300000001,7.89938754,1.8993879ZM8.34999999,9.5L8.34999999,2.50000024Q8.34999999,2.43872494,8.39436239,2.39436251Q8.43872485,2.3500000500000002,8.5000003,2.34999999Q8.56127506,2.34999999,8.60563755,2.39436245Q8.64999998,2.43872485,8.64999998,2.5L8.64999998,9.5Q8.64999998,9.5612745,8.60563749,9.6056371Q8.56127447,9.6500001,8.5,9.6500001Q8.43872553,9.6500001,8.39436251,9.6056371Q8.34999999,9.5612745,8.34999999,9.5Z",fillRule:"evenodd",fill:"#14C9BB",fillOpacity:1})))))),Br=e=>r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:16,height:16,viewBox:"0 0 16 16",...e},r.createElement("defs",null,r.createElement("clipPath",{id:"master_svg0_464_13384"},r.createElement("rect",{x:0,y:0,width:16,height:16,rx:0}))),r.createElement("g",{clipPath:"url(#master_svg0_464_13384)"},r.createElement("g",null,r.createElement("g",null,r.createElement("path",{d:"M14.249594,8C14.940765,8,15.5,8.5591602,15.5,9.2504053L15.5,12.375195C15.5,13.065208,14.940751,13.624399,14.249594,13.624399L12.37521,13.624399L12.37521,14.249579L13.624384,14.249579C13.969678,14.249579,14.249595,14.529496,14.249595,14.87479C14.249595,15.220084,13.969678,15.5,13.624384,15.5L9.8756084,15.5C9.5303183,15.5,9.2504053,15.220087,9.2504053,14.874797C9.2504053,14.529507,9.5303183,14.249594,9.8756084,14.249594L11.12479,14.249594L11.12479,13.625541L9.2504053,13.625541C8.9187899,13.625563,8.6007481,13.493842,8.366255800000001,13.25936C8.1317496,13.024928,8,12.706879,8,12.37521L8,9.2504053C8,8.5591602,8.5591602,8,9.2504053,8L14.249594,8ZM14.249594,9.2504044L9.2504053,9.2504044L9.2504053,12.375195L14.24958,12.375195L14.249594,9.2504044ZM6.7495947,0.5C7.4407516,0.5,8,1.05924803,8,1.7504199L8,4.8752098C8,5.5651951,7.4407516,6.1256151,6.7495947,6.1256151L4.8752098,6.1256151L4.8752098,6.7495942L6.124392,6.7495942C6.4696817,6.7495942,6.7495947,7.0295067,6.7495947,7.3747969C6.7495947,7.7200871,6.4696817,8,6.1243916,8L2.3756152,8C2.0303294999999997,8,1.7504199,7.7200904,1.7504199,7.3748045C1.7504199,7.0295186,2.0303294999999997,6.749609,2.3756154,6.749609L3.62479,6.749609L3.62479,6.1256304L1.7504199,6.1256304C1.05980468,6.1256299,0.5,5.5657516,0.5,4.8752241L0.5,1.7504199C0.5,1.05924803,1.05924803,0.5,1.7504199,0.5L6.7495947,0.5ZM13.000376,1.1239795099999998C13.992748,1.1239795099999998,14.813544,1.8991895,14.872343,2.8903759L14.874745,2.9996386L14.874745,6.1264153C14.874745,6.4705954,14.595732,6.749609,14.251552,6.749609C13.907679,6.749609,13.628793,6.4710746,13.628359,6.1272016L13.624413,2.9996388C13.624662,2.6826468,13.387225,2.4158106,13.072387,2.3792044L13.000375,2.3755862L9.8755989,2.3755862C9.5303135,2.3755862,9.2504053,2.0956774,9.2504053,1.7503924C9.2504053,1.40651655,9.5281181,1.127195,9.8719883,1.1252091499999999L9.8743849,1.12519532L13.000376,1.12519532L13.000376,1.1239795099999998ZM6.7495947,1.7504199L1.7504199,1.7504199L1.7504199,4.8752098L6.7495799,4.8752098L6.7495947,1.7504199Z",fill:"#FFFFFF",fillOpacity:1,style:{mixBlendMode:"passthrough"}}),r.createElement("path",{d:"M6.7495952,0.39999998999999997L1.75042,0.39999998999999997Q1.19061971,0.39999996,0.79530984,0.79530978Q0.39999998999999997,1.1906195899999998,0.39999998999999997,1.75042L0.39999998999999997,4.8752246Q0.400000028,5.4345546,0.79551741,5.8300848Q1.19104946,6.2256298,1.75042,6.2256303L3.52479,6.2256303L3.52479,6.6496091L2.3756154,6.6496091Q2.0752295,6.6496091,1.8628247,6.8620143Q1.65042,7.074419,1.65042,7.374805Q1.65042,7.6751904,1.8628248,7.8875957Q2.0752298,8.100000399999999,2.3756152,8.100000399999999L6.124392,8.100000399999999Q6.4247813,8.100000399999999,6.637188,7.8875933Q6.8495951,7.6751866,6.8495951,7.3747973Q6.8495951,7.0744085,6.637188,6.8620014Q6.4247813,6.6495943,6.1243925,6.6495943L4.9752097,6.6495943L4.9752097,6.225615L6.7495952,6.225615Q7.3092008,6.225615,7.7047153,5.829845Q8.100000399999999,5.4343042,8.100000399999999,4.8752098L8.100000399999999,1.75042Q8.100000399999999,1.1906212,7.7046924,0.79531011Q7.3093853,0.39999998999999997,6.7495952,0.39999998999999997ZM0.93673116,0.93673116Q1.27346241,0.599999942,1.75042,0.599999875L6.7495952,0.60000001Q7.2265425,0.60000001,7.563271,0.93673092Q7.9000006,1.27346301,7.9000006,1.75042L7.9000006,4.8752098Q7.9000006,5.3514986,7.5632482,5.6884694Q7.2263203,6.0256152,6.7495952,6.0256152L4.7752099,6.0256152L4.7752099,6.8495946L6.1243925,6.8495946Q6.341938,6.8495946,6.4957666,7.0034227Q6.6495948,7.1572514,6.6495948,7.3747973Q6.6495948,7.5923433,6.4957666,7.746172Q6.341938,7.9000006,6.124392,7.9000006L2.3756152,7.9000006Q2.1580725000000003,7.9000006,2.0042461,7.7461739Q1.85042,7.5923476,1.85042,7.374805Q1.85042,7.1572618,2.0042462,7.0034356Q2.1580725000000003,6.8496094,2.3756154,6.8496094L3.7247901,6.8496094L3.7247901,6.0256305L1.75042,6.0256305Q1.27389467,6.02563,0.93694112,5.6886654Q0.60000001,5.3517132,0.60000001,4.8752246L0.60000001,1.75042Q0.60000001,1.27346236,0.93673116,0.93673116ZM13.740499,6.6381125Q13.952263,6.8496094,14.251552,6.8496094Q14.551108,6.8496094,14.762927,6.6377907Q14.974747,6.425972,14.974747,6.1264153L14.974722,2.9974401L14.972279,2.8863142L14.972168,2.8844543Q14.925713,2.1013519,14.35509,1.5627605Q13.784266,1.02397954,13.000376,1.02397954L12.900376,1.02397954L12.900376,1.02519542L9.873807,1.02519703L9.8714104,1.02521086Q9.5722561,1.02693844,9.361331,1.2390857899999999Q9.1504049,1.45123285,9.1504049,1.7503924Q9.1504049,2.0507776,9.3628101,2.2631819Q9.5752144,2.4755863,9.8755999,2.4755863L12.997865,2.4755863L13.06397,2.4789076999999997Q13.260183,2.5027704,13.39185,2.6507499Q13.52457,2.7999129,13.524414,2.9997652L13.528359,6.1273279Q13.528737,6.4266167,13.740499,6.6381125ZM13.070488,1.22519535Q13.730493,1.24824315,14.21781,1.7082052Q14.730088,2.191727,14.772409,2.8944511L14.774771,3.0018373L14.774747,6.1264153Q14.774747,6.3431292,14.621507,6.4963694Q14.468266,6.6496091,14.251552,6.6496091Q14.035032,6.6496091,13.881832,6.4966025Q13.728632,6.3435955,13.728359,6.1270757L13.724414,2.9995129Q13.72463,2.7238853,13.541266,2.5178039Q13.357908,2.3117281,13.083936,2.2798735L13.08068,2.2794951L13.002886,2.2755862000000002L9.8755999,2.2755862000000002Q9.6580563,2.2755862000000002,9.5042305,2.1217606Q9.3504057,1.9679358,9.3504057,1.7503924Q9.3504057,1.5337378,9.5031605,1.38009828Q9.6559153,1.22645873,9.8725662,1.22520757L9.8749628,1.2251936799999998L13.070488,1.22519535ZM1.65042,4.9752097L6.8495798,4.9752097L6.8495803,4.8752103L6.8495955,1.65042L1.65042,1.65042L1.65042,4.9752097ZM6.6495805,4.7752099L1.85042,4.7752099L1.85042,1.85042L6.6495943,1.85042L6.6495805,4.7752099ZM14.249595,7.9000006L9.2504053,7.9000006Q8.6905499,7.9000006,8.295275199999999,8.295275199999999Q7.9000006,8.6905499,7.9000006,9.2504053L7.9000006,12.37521Q7.9000006,12.93465,8.295548,13.330073Q8.6910696,13.725577,9.2504053,13.725542L11.024791,13.725542L11.024791,14.149595L9.8756084,14.149595Q9.5752201,14.149595,9.362813,14.362002Q9.1504049,14.574409,9.1504049,14.874797Q9.1504049,15.175186,9.362813,15.387593Q9.5752201,15.6,9.8756084,15.6L13.624384,15.6Q13.924776,15.6,14.137185,15.387592Q14.349596,15.175183,14.349596,14.874791Q14.349596,14.574401,14.137185,14.36199Q13.924776,14.149581,13.624384,14.149581L12.47521,14.149581L12.47521,13.7244L14.249595,13.7244Q14.809282,13.7244,15.204668,13.329288Q15.6,12.934231,15.6,12.375195L15.6,9.2504053Q15.6,8.6905622,15.204697,8.2952738Q14.809406,7.9000006,14.249595,7.9000006ZM8.436696999999999,8.436696999999999Q8.7733936,8.100000399999999,9.2504053,8.100000399999999L14.249595,8.100000399999999Q14.726566,8.100000399999999,15.063278,8.436698400000001Q15.400001,8.773407,15.400001,9.2504053L15.400001,12.375195Q15.400001,12.851346,15.063296,13.187817Q14.72648,13.5244,14.249595,13.5244L12.27521,13.5244L12.27521,14.349581L13.624384,14.349581Q13.841935,14.349581,13.995765,14.50341Q14.149596,14.657242,14.149596,14.874791Q14.149596,15.09234,13.995765,15.24617Q13.841933,15.400001,13.624384,15.400001L9.8756084,15.400001Q9.658061,15.400001,9.5042334,15.246173Q9.3504057,15.092345,9.3504057,14.874797Q9.3504057,14.65725,9.5042334,14.503423Q9.658061,14.349595,9.8756084,14.349595L11.224791,14.349595L11.224791,13.525542L9.2504053,13.525542Q8.7739048,13.525573,8.4369664,13.188649Q8.100000399999999,12.85179,8.100000399999999,12.37521L8.100000399999999,9.2504053Q8.100000399999999,8.7733936,8.436696999999999,8.436696999999999ZM9.1504049,12.475195L14.349582,12.475195L14.349582,12.375195L14.349595,9.1504049L9.1504049,9.1504049L9.1504049,12.475195ZM14.149582,12.275195L9.3504057,12.275195L9.3504057,9.3504057L14.149595,9.3504057L14.149582,12.275195Z",fillRule:"evenodd",fill:"#FFFFFF",fillOpacity:1})),r.createElement("g",{transform:"matrix(-1,0,0,-1,13.857142448425293,31)"},r.createElement("path",{d:"M10.678541424212646,15.5C11.670913724212646,15.5,12.491709724212647,16.27520996,12.550508524212646,17.2663964L12.552910824212645,17.3756591L12.552910824212645,20.5024366C12.552910824212645,20.8466167,12.273897624212648,21.1256299,11.929717524212647,21.1256299C11.585844524212646,21.1256299,11.306958224212647,20.847095500000002,11.306524324212646,20.5032225L11.302578424212648,17.3756593C11.302827824212645,17.0586673,11.065390624212647,16.7918311,10.750552424212646,16.7552248L10.678540424212645,16.7516067L7.553764884212646,16.7516067C7.208480004212647,16.7516067,6.9285712242126465,16.47169793,6.9285712242126465,16.12641293C6.9285712242126465,15.7825371,7.206284434212646,15.5032154946,7.550154504212647,15.5012296417L7.552550074212647,15.501215807L10.678541424212646,15.501215807L10.678541424212646,15.5Z",fill:"#FFFFFF",fillOpacity:1,style:{mixBlendMode:"passthrough"}}),r.createElement("path",{d:"M10.742133124212646,16.854928L10.676030424212646,16.8516068L7.553765054212646,16.8516068Q7.2533799442126465,16.8516068,7.040975574212647,16.6392025Q6.828571214212647,16.42679805,6.828571214212647,16.12641305Q6.828571204212646,15.82725382,7.039497024212647,15.61510637Q7.250422864212647,15.40295893,7.549577054212646,15.401231311L7.552550134212646,15.401215799L10.578541724212647,15.401215799L10.578541724212647,15.39999999L10.678541924212647,15.39999986Q11.462431924212646,15.39999993,12.033255624212646,15.93878093Q12.603878024212648,16.47737247,12.650332924212647,17.2604748L12.650443524212648,17.2623351L12.652911224212646,17.3756591L12.652911224212646,20.5024371Q12.652911224212646,20.801992900000002,12.441092924212647,21.0138116Q12.229274224212647,21.2256298,11.929718024212647,21.2256298Q11.630428324212646,21.2256298,11.418665424212646,21.0141344Q11.206902024212646,20.8026385,11.206524324212648,20.5033498L11.202578524212647,17.3755808Q11.202735924212647,17.175934,11.070014924212646,17.0267705Q10.938347324212646,16.8787901,10.742133124212646,16.854928ZM10.748657924212647,15.60121581L7.552550134212646,15.60121581L7.550732014212646,15.60122798Q7.334081054212646,15.60247914,7.1813261542126465,15.75611877Q7.028571234212646,15.90975842,7.028571234212646,16.12641305Q7.028571234212646,16.3439554,7.1823969442126465,16.4977811Q7.336222644212646,16.6516068,7.553765054212646,16.6516068L10.681051224212647,16.6516068L10.758845124212646,16.6555154L10.762101924212647,16.655894Q11.036072724212646,16.6877487,11.219431924212646,16.8938245Q11.402795324212647,17.0999053,11.402578824212647,17.375738L11.406524624212647,20.503097099999998Q11.406797924212647,20.7196174,11.559997524212648,20.872623400000002Q11.713197224212646,21.02563,11.929718024212647,21.02563Q12.146431424212647,21.02563,12.299671124212647,20.8723898Q12.452911424212648,20.7191501,12.452911424212648,20.5024371L12.452911424212648,17.3756591L12.450575324212647,17.2704957Q12.408261824212646,16.5677551,11.895975124212647,16.08422554Q11.408659424212647,15.62426481,10.748657924212647,15.60121581Z",fillRule:"evenodd",fill:"#FFFFFF",fillOpacity:1}))))),zr=({device:e,refresh:n,isDisabled:o,onOpenScan:i,onOpenFactoryReset:s,onOpenTrace:l,onOpenUpgradeModal:d,onOpenEventQueue:a,onOpenTelemetryModal:c,onOpenConfigureModal:u,onOpenScriptModal:h,onOpenRebootModal:g,isCompact:p})=>{const{t:f}=H(),x=(e==null?void 0:e.deviceType)??"ap";oo(M=>M.addEventListeners);const{refetch:C,isFetching:m}=us({serialNumber:e.serialNumber,extraId:"inventory-modal"}),{mutateAsync:b}=ms({serialNumber:e.serialNumber}),{onSuccess:L,onError:w}=fo({objName:f("devices.one"),operationType:"blink",refresh:n});Ar({serialNumber:e.serialNumber,compatible:e.compatible});const y=()=>{b(void 0,{onSuccess:()=>{Le.success(f("commands.blink_success")),n==null||n()},onError:M=>{var T,Q;Le.error(xt.isAxiosError(M)&&f("commands.blink_error",{e:(Q=(T=M.response)==null?void 0:T.data)==null?void 0:Q.ErrorDescription})||f("common.error"))}})},v=()=>s(e.serialNumber),j=()=>l(e.serialNumber),k=()=>u(e.serialNumber),R=()=>g(e.serialNumber),I=t.jsx(Ut,{items:[{key:"blink",label:f("commands.blink"),onClick:y},{key:"configure",label:f("controller.configure.title"),onClick:k,hidden:!p||x!=="ap"},{key:"rtty",label:f("commands.rtty"),onClick:C,hidden:!p},{key:"factory",label:f("commands.factory_reset"),onClick:v},{key:"reboot",label:f("commands.reboot"),onClick:R,hidden:!p},{key:"trace",label:f("controller.devices.trace"),onClick:j}].filter(M=>!M.hidden)});return t.jsxs(t.Fragment,{children:[t.jsx(Ae,{title:f("commands.rtty"),children:t.jsx(q,{type:"primary",icon:t.jsx(fe,{component:Br}),disabled:o,loading:m,onClick:C,children:"Rtty"})}),t.jsx(Ae,{title:f("commands.reboot"),children:t.jsx(q,{icon:t.jsx(fe,{component:$r}),disabled:o,onClick:R,hidden:p,children:"Reboot"})}),t.jsx(hs,{overlay:I,trigger:["click"],placement:"bottomRight",children:t.jsx(Ae,{title:f("common.actions"),children:t.jsxs(q,{icon:t.jsx(fe,{component:Nr}),disabled:o,children:["Actions",t.jsx(fs,{})]})})})]})},Vr=V.memo(zr),Hr=({label:e,value:n,unit:o,onChange:i,onBlur:s,error:l,isError:d,isRequired:a,hideArrows:c,element:u,isDisabled:h,w:g,definitionKey:p})=>u?t.jsxs(Je,{isInvalid:d,isRequired:a,isDisabled:h,children:[t.jsx(et,{ms:"4px",fontSize:"md",fontWeight:"normal",_disabled:{opacity:.8},children:e}),u,t.jsx(ut,{children:l})]}):t.jsxs(Je,{isInvalid:d,isRequired:a,isDisabled:h,children:[t.jsxs(et,{ms:"4px",fontSize:"md",fontWeight:"normal",_disabled:{opacity:.8},children:[e,t.jsx(go,{definitionKey:p})]}),o?t.jsxs(Lt,{children:[t.jsxs(_n,{allowMouseWheel:!0,value:n,onChange:i,onBlur:s,borderRadius:"15px",fontSize:"sm",w:g,_disabled:{opacity:.8,cursor:"not-allowed"},children:[t.jsx(Dn,{border:"2px solid"}),t.jsxs(On,{hidden:c,children:[t.jsx(Pn,{}),t.jsx(An,{})]})]}),t.jsx(pn,{children:o})]}):t.jsxs(_n,{allowMouseWheel:!0,value:n,onChange:i,onBlur:s,borderRadius:"15px",fontSize:"sm",w:g,children:[t.jsx(Dn,{border:"2px solid"}),t.jsxs(On,{hidden:c,children:[t.jsx(Pn,{}),t.jsx(An,{})]})]}),t.jsx(ut,{children:l})]}),Zr=V.memo(Hr),Wn=(e,n)=>{if(n&&e==="")return;const o=parseInt(e,10);return Number.isNaN(o)?0:o},Wr=({name:e,unit:n,isDisabled:o=!1,label:i,isRequired:s=!1,hideArrows:l=!1,w:d,acceptEmptyValue:a=!1,definitionKey:c,conversionFactor:u})=>{const{value:h,error:g,isError:p,onChange:f,onBlur:x}=jt({name:e}),C=r.useCallback(m=>{if(u){const b=Wn(m,a),L=b!==void 0?b*u:void 0;f(L)}else f(Wn(m,a))},[f]);return t.jsx(Zr,{label:i??e,value:u?Math.ceil(h/u):h,unit:n,isError:p,onChange:C,onBlur:x,error:g,hideArrows:l,isRequired:s,isDisabled:o,w:d,definitionKey:c})},Ur=V.memo(Wr),Gr=({options:e,label:n,value:o,onChange:i,onBlur:s,error:l,touched:d,isRequired:a,isDisabled:c,isHidden:u,isLabelHidden:h,w:g,definitionKey:p})=>t.jsxs(Je,{isInvalid:(l!==void 0||l)&&d,isRequired:a,isDisabled:c,hidden:u,children:[t.jsxs(et,{ms:"4px",fontSize:"md",fontWeight:"normal",_disabled:{opacity:.8},hidden:h,children:[n," ",t.jsx(go,{definitionKey:p})]}),t.jsx(po,{value:o,onChange:i,onBlur:s,borderRadius:"15px",fontSize:"sm",_disabled:{opacity:.8,cursor:"not-allowed"},border:"2px solid",w:g,children:e.map(f=>t.jsx("option",{value:f.value,children:f.label},Ve()))}),t.jsx(ut,{children:l})]}),Yr=V.memo(Gr),Xr=({options:e,name:n,isDisabled:o,label:i,isRequired:s,onChange:l,onChangeEffect:d,isHidden:a,isLabelHidden:c,emptyIsUndefined:u,isInt:h,w:g,definitionKey:p})=>{const{value:f,error:x,onChange:C,onBlur:m,touched:b}=jt({name:n}),L=r.useCallback(y=>{l?l(y):(u&&y.target.value===""?C(void 0):C(h?parseInt(y.target.value,10):y.target.value),d!==void 0&&d(y),setTimeout(()=>{m()},200))},[l]),w=r.useCallback(()=>{m()},[]);return t.jsx(Yr,{label:i,value:f,onChange:L,onBlur:w,error:x,touched:b,options:e,isDisabled:o,isRequired:s,isHidden:a,isLabelHidden:c,w:g,definitionKey:p})},qr=V.memo(Xr),Kr=({isDisabled:e})=>{const{t:n}=H(),[o,i]=r.useState(Ve()),s=r.useRef(),{onChange:l,error:d,isError:a,value:c,onBlur:u}=jt({name:"script"}),[h,{on:g,off:p}]=yo();let f;const x=()=>{if(f){const L=f.result;L&&(i(Ve()),l(L))}},C=()=>{var L;s.current&&((L=s==null?void 0:s.current)==null||L.click())},m=L=>{const w=L.target.files?L.target.files[0]:void 0;p(),w&&w.size<500*1024?(f=new FileReader,f.onloadend=x,f.readAsText(w)):g()},b=L=>{l(L.target.value)};return t.jsxs(Je,{isInvalid:a,isDisabled:e,mt:2,children:[t.jsxs(et,{ms:"4px",fontSize:"md",fontWeight:"normal",_disabled:{opacity:.8},display:"flex",children:[t.jsx(We,{my:"auto",children:n("script.one")}),t.jsxs(Pe,{children:[t.jsx(wt,{borderRadius:"15px",pt:1,fontSize:"sm",type:"file",onChange:m,isDisabled:e,w:"300px",mb:2,ref:s,hidden:!0},o),t.jsx(He,{onClick:C,rightIcon:t.jsx(kr,{}),size:"sm",my:"auto",ml:2,children:n("script.upload_file")}),h&&t.jsx(We,{ml:2,fontWeight:"bold",textColor:"red",my:"auto",children:n("script.file_too_large")})]})]}),t.jsx(Lt,{size:"md",children:t.jsx(ko,{value:c,onChange:b,onBlur:u,borderRadius:"15px",fontSize:"sm",minH:"200px",_disabled:{opacity:.8,cursor:"not-allowed"}})}),t.jsx(ut,{children:d})]})},Jr=({isDisabled:e})=>{const{t:n}=H(),{value:o,onChange:i,isError:s,error:l}=jt({name:"uri"}),d=a=>{i(a==="0"?void 0:"")};return t.jsxs(Je,{isInvalid:s,isRequired:!0,isDisabled:e,children:[t.jsx(et,{ms:0,mb:0,fontSize:"md",fontWeight:"normal",_disabled:{opacity:.8},children:n("script.upload_destination")}),t.jsx(Pe,{h:"40px",children:t.jsx(xn,{onChange:d,defaultValue:o===void 0?"0":"1",_disabled:{opacity:.8},children:t.jsxs(so,{spacing:5,direction:"row",children:[t.jsx(bt,{colorScheme:"blue",value:"0",children:n("script.automatic")}),t.jsx(bt,{colorScheme:"green",value:"1",children:t.jsxs(Pe,{children:[t.jsx(We,{my:"auto",mr:2,w:"180px",children:n("script.custom_domain")}),t.jsx(Lt,{children:t.jsx(wt,{value:o,onChange:a=>i(a.target.value),isDisabled:e||o===void 0,onClick:a=>{a.target.select(),a.stopPropagation()},w:"100%",_disabled:{opacity:.8}})})]})})]})})}),t.jsx(ut,{children:l})]})},ei=({name:e,isDisabled:n,controlProps:o})=>{const{t:i}=H(),{value:s,error:l,isError:d,onChange:a}=jt({name:e}),c=u=>{a(u==="0"?void 0:"")};return t.jsxs(Je,{...o,isInvalid:d,isRequired:!0,isDisabled:n,children:[t.jsx(et,{ms:0,mb:0,fontSize:"md",fontWeight:"normal",_disabled:{opacity:.8},children:i("script.signature")}),t.jsx(Pe,{h:"40px",children:t.jsx(xn,{onChange:c,defaultValue:s===void 0?"0":"1",children:t.jsxs(so,{spacing:5,direction:"row",children:[t.jsx(bt,{colorScheme:"blue",value:"0",children:i("script.automatic")}),t.jsx(bt,{colorScheme:"green",value:"1",children:t.jsxs(Pe,{children:[t.jsx(We,{my:"auto",mr:2,children:i("common.custom")}),t.jsx(Lt,{children:t.jsx(wt,{value:s,onChange:u=>a(u.target.value),isDisabled:n||s===void 0,onClick:u=>{u.target.select(),u.stopPropagation()},w:"100%",_disabled:{opacity:.8}})})]})})]})})}),t.jsx(ut,{mt:0,children:l})]})},ti=r.memo(ei),ni=e=>Fs().shape({serialNumber:ft().required(e("form.required")),deferred:_s().required(e("form.required")),type:ft().required(e("form.required")),timeout:Ht().when("deferred",{is:!1,then:Ht().required(e("form.required")).moreThan(10).lessThan(5*60)}),script:ft().required(e("form.required")),when:Ht().required(e("form.required")),uri:ft().min(1,e("form.required")),signature:ft()}),oi=e=>({serialNumber:e,deferred:!0,type:"shell",script:"",when:0}),si=({onStart:e,formRef:n,formKey:o,areFieldsDisabled:i,waitForResponse:s,onToggleWaitForResponse:l,device:d,script:a,isDiagnostics:c})=>{const{t:u}=H(),{hasCopied:h,onCopy:g,setValue:p}=mn((a==null?void 0:a.content)??"");return r.useEffect(()=>{p((a==null?void 0:a.content)??"")},[a==null?void 0:a.content]),t.jsx(fa,{innerRef:n,enableReinitialize:!0,initialValues:a?{...a,serialNumber:(d==null?void 0:d.serialNumber)??"",script:a.content,uri:a.defaultUploadURI.length===0?void 0:a.defaultUploadURI,when:0}:oi((d==null?void 0:d.serialNumber)??""),validationSchema:c?void 0:ni(u),validateOnMount:!0,onSubmit:async f=>e(f),children:f=>{var x;return t.jsxs(ga,{children:[t.jsxs(Pe,{mt:2,children:[t.jsxs(Je,{w:"180px",children:[t.jsx(et,{mb:"12px",children:u("controller.trace.wait")}),t.jsx(Us,{size:"lg",isChecked:s,onChange:l})]}),t.jsx(be,{w:"120px",mr:2,mb:4,children:t.jsx(Gs,{name:"deferred",label:u("script.deferred"),onChangeCallback:C=>{setTimeout(C?()=>f.setFieldValue("timeout",void 0):()=>f.setFieldValue("timeout",120),100)},isDisabled:i||c})}),t.jsx(be,{children:!f.values.deferred&&t.jsx(Ur,{name:"timeout",label:u("script.timeout"),isDisabled:i,unit:"s",isRequired:!0,w:"100px"})})]}),t.jsx(Co,{mt:2,mb:4,border:"1px",borderColor:"gray"}),!c&&a&&t.jsxs(t.Fragment,{children:[t.jsxs(Pe,{mt:2,children:[t.jsx(pt,{size:"md",my:"auto",children:a.name}),t.jsx(Kt,{colorScheme:"teal",size:"lg",my:"auto",mx:2,children:a.type}),t.jsx(Kt,{colorScheme:"blue",size:"lg",my:"auto",children:a.author}),a.uri.length>0&&t.jsx(He,{onClick:()=>{var C;return(C=window.open(a.uri,"_blank"))==null?void 0:C.focus()},colorScheme:"blue",variant:"link",rightIcon:t.jsx(Rr,{}),ml:2,children:u("script.helper")})]}),t.jsx(We,{fontStyle:"italic",mt:2,children:a.description}),t.jsxs(We,{mt:2,children:[u("script.upload_destination"),":"," ",t.jsx("b",{children:a.defaultUploadURI===""?u("script.automatic"):a.defaultUploadURI})]})]}),!a&&t.jsx(be,{mt:2,children:t.jsx(Jr,{isDisabled:i||a!==void 0})}),!c&&t.jsxs(t.Fragment,{children:[t.jsx(Pe,{children:t.jsx(be,{children:(d==null?void 0:d.restrictedDevice)&&!((x=d==null?void 0:d.restrictionDetails)!=null&&x.developer)&&t.jsx(ti,{name:"signature",isDisabled:i})})}),t.jsx(qr,{name:"type",label:u("common.type"),options:[{value:"shell",label:"Shell"},{value:"bundle",label:"Bundle"}],isRequired:!0,isDisabled:i||a!==void 0,isHidden:a!==void 0,w:"120px"}),a&&t.jsxs(Pe,{children:[t.jsx(pt,{my:"auto",size:"sm",children:u("script.one")}),t.jsx(gs,{}),t.jsx(He,{onClick:g,size:"sm",colorScheme:"teal",children:u(h?"common.copied":"common.copy")})]}),t.jsx(be,{children:a?t.jsx(Bt,{whiteSpace:"pre-line",w:"100%",mt:2,children:a.content.replace(/^\n/,"")}):t.jsx(Kr,{isDisabled:i||a!==void 0})})]})]})}},o)},ai=({result:e})=>{var s,l,d,a,c;const{t:n}=H(),o=xo({serialNumber:e.serialNumber,commandId:e.UUID}),i=()=>{o.refetch()};return e.errorCode!==0?t.jsx(Ue,{my:"100px",children:t.jsxs(Gt,{status:"error",children:[t.jsx(Yt,{}),t.jsxs(be,{children:[t.jsx(ao,{children:n("common.error")}),t.jsxs(Xt,{children:[e.errorCode,": ",(l=(s=e.results)==null?void 0:s.status)==null?void 0:l.result]})]})]})}):((d=e.details)==null?void 0:d.uri)!==void 0?t.jsx(Ue,{my:"100px",children:t.jsx(He,{onClick:i,colorScheme:"blue",isLoading:o.isFetching,isDisabled:e.waitingForFile!==0,children:e.waitingForFile===0?n("common.download"):n("script.file_not_ready")})}):t.jsx(be,{children:t.jsx(be,{maxH:"500px",overflowY:"auto",mt:2,children:t.jsx(Bt,{whiteSpace:"pre-line",children:((c=(a=e.results)==null?void 0:a.status)==null?void 0:c.result)??JSON.stringify(e.results,null,2)})})})},ri=(e,n)=>Ne.get(`scripts?limit=${e}&offset=${n}`).then(o=>o.data),ii=async()=>{let e=0,n=[],o;do o=await ri(100,e),n=n.concat(o.scripts),e+=100;while(o.scripts.length===500);return n},li=()=>mt(["deviceScripts","all"],ii,{staleTime:3e4,keepPreviousData:!0}),ci=({device:e,modalProps:n})=>{const{t:o}=H(),i=rn(),{user:s}=ps(),l=li(),[d,a]=r.useState(""),c=ro(),[u,h]=r.useState(Ve()),g=Ys({serialNumber:(e==null?void 0:e.serialNumber)??""}),{onCopy:p,hasCopied:f,setValue:x}=mn(""),{form:C,formRef:m}=Xs(),{isConfirmOpen:b,closeConfirm:L,closeModal:w,closeCancelAndForm:y}=pa({isLoading:g.isLoading,onModalClose:n.onClose}),[v,{toggle:j}]=yo(),k=s==null?void 0:s.userRole,R=async Q=>{var _,$,B;let F=Q;F.script&&(F.script=btoa(F.script)),d==="diagnostics"?F={serialNumber:(e==null?void 0:e.serialNumber)??"",when:0,deferred:!0,uri:Q.defaultUploadURI&&((_=Q.defaultUploadURI)==null?void 0:_.length)>0?Q.defaultUploadURI:void 0,type:"diagnostic"}:d!==""&&(F={serialNumber:(e==null?void 0:e.serialNumber)??"",when:0,deferred:Q.deferred,timeout:Q.timeout,signature:e!=null&&e.restrictedDevice&&!(($=e==null?void 0:e.restrictionDetails)!=null&&$.developer)?Q.signature:void 0,uri:Q.defaultUploadURI&&((B=Q.defaultUploadURI)==null?void 0:B.length)>0?Q.defaultUploadURI:void 0,scriptId:d,type:Q.type}),g.mutate(F,{onSuccess:D=>{var O,W;x(((W=(O=D.results)==null?void 0:O.status)==null?void 0:W.result)??JSON.stringify(D.results??{},null,2)),c.invalidateQueries(["commands",(e==null?void 0:e.serialNumber)??""])},onError:D=>{var O,W,E,P;xt.isAxiosError(D)&&((W=(O=D.response)==null?void 0:O.data)!=null&&W.ErrorDescription)&&i({id:"script-update-error",title:o("common.error"),description:(P=(E=D.response)==null?void 0:E.data)==null?void 0:P.ErrorDescription,status:"error",duration:5e3,isClosable:!0,position:"top-right"})}}),v||(i({id:"script-update-success",title:o("common.success"),description:o("script.started_execution"),status:"success",duration:5e3,isClosable:!0,position:"top-right"}),y())},I=r.useMemo(()=>{var Q;return k?((Q=l.data)==null?void 0:Q.filter(F=>F.restricted.includes(k??"")))??[]:[]},[k,l.data]),M=g.isLoading||!e,T=()=>{var Q,F,_,$;return g.isLoading?t.jsx(Ue,{my:"100px",children:t.jsx(xs,{size:"xl"})}):g.data?t.jsx(ai,{result:g.data}):g.error?t.jsxs(Gt,{mb:6,status:"error",children:[t.jsx(Yt,{}),t.jsx(ao,{children:o("common.error")}),t.jsx(Xt,{children:xt.isAxiosError(g.error)&&((_=(F=(Q=g.error)==null?void 0:Q.response)==null?void 0:F.data)==null?void 0:_.ErrorDescription)})]}):!g.isLoading&&!g.data?t.jsxs(t.Fragment,{children:[k==="root"||I.length>0?t.jsxs(po,{value:d,onChange:B=>{a(B.target.value),h(Ve())},w:"max-content",children:[t.jsx("option",{value:"",children:k==="root"?`${o("common.custom")} ${o("script.one")}`:o("common.none")}),t.jsx("option",{value:"diagnostics",children:o("script.diagnostics")}),I==null?void 0:I.sort((B,D)=>B.name.localeCompare(D.name)).map(B=>t.jsx("option",{value:B.id,children:B.name},B.id))]}):t.jsx(Ue,{mt:2,children:t.jsxs(Gt,{status:"error",w:"max-content",children:[t.jsx(Yt,{}),t.jsx(Xt,{children:o("script.no_script_available")})]})}),e&&(k==="root"||d!=="")&&t.jsx(si,{onStart:R,formKey:u,formRef:m,waitForResponse:v,onToggleWaitForResponse:j,areFieldsDisabled:M,device:e,isDiagnostics:d==="diagnostics",script:d!==""?($=l.data)==null?void 0:$.find(B=>B.id===d):void 0})]}):null};return r.useEffect(()=>{h(Ve()),g.reset(),a("")},[e,n.isOpen]),t.jsxs(t.Fragment,{children:[t.jsx(io,{...n,onClose:w,title:o("script.device_title"),topRightButtons:g.data?t.jsxs(t.Fragment,{children:[t.jsx(He,{onClick:p,size:"md",colorScheme:"teal",children:f?`${o("common.copied")}!`:o("common.copy")}),t.jsx(He,{rightIcon:t.jsx(qs,{}),onClick:g.reset,children:o("common.go_back")})]}):t.jsx(He,{colorScheme:"blue",onClick:C.submitForm,isDisabled:!C.isValid||k!=="root"&&d==="",isLoading:g.isLoading||C.isSubmitting,children:o("common.start")}),children:t.jsx(be,{children:T()})}),t.jsx(Ks,{modalProps:{isOpen:b,onOpen:()=>{},onClose:L},confirm:y,cancel:L})]})},di=()=>{const[e,n]=r.useState(),o=ln(),i=s=>{n(s),o.onOpen()};return r.useMemo(()=>({modalProps:o,device:e,openModal:i,modal:t.jsx(ci,{device:e,modalProps:o})}),[e,o.isOpen])},tn=e=>r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:16,height:16,viewBox:"0 0 16 16",...e},r.createElement("defs",null,r.createElement("clipPath",{id:"master_svg0_298_34149"},r.createElement("rect",{x:0,y:0,width:16,height:16,rx:0}))),r.createElement("g",{clipPath:"url(#master_svg0_298_34149)"},r.createElement("g",null,r.createElement("path",{d:"M9.4999447,11.999972C9.2234621,11.999972,9.000021,11.776513,9.000021,11.500048L9.000021,5.9999766C9.000021,5.7234945,9.2234621,5.4999428,9.4999447,5.4999428C9.7764273,5.4999428,9.999979,5.7235103,9.999979,5.9999766L9.999979,11.500001C9.999979,11.776513,9.7764425,11.999956,9.4999447,11.999972L9.4999447,11.999972ZM6.5000095,11.999972C6.223433,11.999972,5.9999757,11.776513,5.9999757,11.500048L5.9999757,5.9999766C5.9999757,5.7234945,6.223433,5.4999428,6.5000095,5.4999428C6.7764912,5.4999428,6.9999328,5.7235103,6.9999328,5.9999766L6.9999328,11.500001C6.9999328,11.776513,6.7764916,11.999956,6.5000095,11.999972L6.5000095,11.999972ZM14.499935,3.5000114L11.99994,3.5000114L11.99994,2.5000071999999998C11.99994,1.6729806699999998,11.332441,1.00000035959604,10.5114479,1.00000035959604L5.4999261,1.00000035959604C4.6729934,1.00000035959604,3.9999976,1.67299628,3.9999976,2.5000071999999998L3.9999976,3.5000114L1.5000337400000001,3.5000114C1.22345752,3.5000114,1,3.7234535,1,4.0000295999999995C1,4.2765119,1.22345738,4.4999533,1.50003362,4.4999533L14.49995,4.4999533C14.776433,4.4999533,14.999985,4.2765119,14.999985,4.0000293C14.999985,3.723453,14.776448,3.4999957,14.49995,3.4999957L14.499935,3.5000114ZM4.9999862,2.5000071999999998C4.9999862,2.2244992999999997,5.2244496,1.99998903,5.4999261,1.99998903L10.5114641,1.99998903C10.7855272,1.99998903,10.9999828,2.219503,10.9999828,2.5000071999999998L10.9999828,3.5000114L4.9999862,3.5000114L4.9999862,2.5000071999999998ZM11.501979,15L4.5009425,15C3.6739948,15,3.0010145,14.327003,3.0010145,13.499993L3.0010145,5.9925132C3.0010145,5.7164874,3.2250061,5.4924955,3.5009694,5.4924955C3.7770116,5.4924955,4.0009875,5.7164874,4.0009875,5.9925132L4.0009875,13.50001C4.0009875,13.776037,4.2254663,14.000028,4.5009584,14.000028L11.501979,14.000028C11.777942,14.000028,12.00195,13.776037,12.00195,13.50001L12.00195,6.012969C12.00195,5.7370052,12.225487,5.5129986,12.501967,5.5129986C12.778464,5.5129986,13.002001,5.7370052,13.002001,6.012969L13.002001,13.499995C13.002001,14.327022,12.328928,15.000001,11.501979,15Z",fill:"#14C9BB",fillOpacity:1}),r.createElement("path",{d:"M15.030421,3.4695101Q14.810883,3.2499957,14.49995,3.2499957L14.49995,3.7499957Q14.749985,3.7499957,14.749985,4.0000293Q14.749985,4.2499533,14.49995,4.2499533L1.50003362,4.2499533Q1.39614782,4.2499533,1.32306206,4.1768804Q1.24999999,4.1038311,1.25,4.0000295999999995Q1.25,3.7500114,1.5000337400000001,3.7500114L4.2499976,3.7500114L4.2499976,2.5000071999999998Q4.2499976,1.98330557,4.6166418,1.61664808Q4.983276399999999,1.25000012,5.4999266,1.25L10.5114479,1.25000036Q11.02301,1.25000036,11.386098,1.61595011Q11.749941,1.98266101,11.749941,2.5000074999999997L11.749941,3.7500114L14.499935,3.7500114L14.499935,3.2500114L12.249941,3.2500114L12.249941,2.5000074999999997Q12.249941,1.77670288,11.741037,1.26378757Q11.231268,0.75000036,10.5114479,0.75000036L5.4999266,0.75Q4.7761645,0.75000019,4.263082300000001,1.26310107Q3.7499976,1.77620435,3.7499976,2.5000071999999998L3.7499976,3.2500114L1.5000337400000001,3.2500114Q1.18904975,3.2500114,0.96952788,3.4695239Q0.75,3.6890426,0.75,4.0000295999999995Q0.7499999399999999,4.3109634,0.969539383,4.5304646Q1.18906638,4.7499533,1.50003362,4.7499533L14.49995,4.7499533Q14.810855,4.7499533,15.030399,4.5304761Q15.249985,4.3109573999999995,15.249985,4.0000293Q15.249985,3.6890495,15.030421,3.4695101ZM4.7499862,2.5000071999999998L4.7499862,3.7500114L11.249983,3.7500114L11.249983,2.5000071999999998Q11.249983,2.1877743,11.03732,1.97010201Q10.8222723,1.74998903,10.5114651,1.74998903L5.4999266,1.74998903Q5.1897554,1.74998903,4.9698608,1.96992111Q4.7499862,2.1898336,4.7499862,2.5000071999999998ZM5.2499862,3.2500114L10.7499828,3.2500114L10.7499828,2.5000071999999998Q10.7499828,2.2499890000000002,10.5114651,2.2499890000000002L5.4999266,2.2499890000000002Q5.2499862,2.2499890000000002,5.2499862,2.5000071999999998L5.2499862,3.2500114ZM12.738868,14.736914Q13.252002,14.223806,13.252002,13.499995L13.252002,6.012969Q13.252002,5.7023973,13.032497,5.48277Q12.812849,5.2629986,12.501967,5.2629986Q12.191095,5.2629986,11.97145,5.4827728Q11.751951,5.7024002,11.751951,6.012969L11.751951,13.50001Q11.751951,13.750029,11.501979,13.750029L4.5009584,13.750029Q4.2509875,13.750029,4.2509875,13.50001L4.2509875,5.9925132Q4.2509875,5.6819396,4.031272599999999,5.4622202Q3.811553,5.2424955,3.5009694,5.2424955Q3.1904325,5.2424955,2.9707212,5.4622259Q2.7510145,5.6819515,2.7510145,5.9925132L2.7510145,13.499994Q2.7510145,14.223799,3.2640924,14.736899Q3.7771711,15.25,4.5009426999999995,15.25L11.501978,15.25Q12.225754,15.250002,12.738868,14.736914ZM7.0304346,12.030444Q7.2499328,11.810931,7.2499328,11.500002L7.2499328,5.9999766Q7.2499328,5.6890845,7.0304575,5.4695373Q6.810935,5.2499428,6.5000095,5.2499428Q6.1890306,5.2499428,5.9694881,5.4695139Q5.7499762,5.689055,5.7499762,5.9999766L5.7499762,11.500048Q5.7499762,11.81097,5.9695129,12.030475Q6.189043,12.249972,6.5000238,12.249972Q6.8109388,12.249956,7.0304346,12.030444ZM10.030407,12.03047Q10.249979,11.810944,10.249979,11.500002L10.249979,5.9999766Q10.249979,5.6890726,10.0304213,5.4695101Q9.8108587,5.2499428,9.4999447,5.2499428Q9.1890182,5.2499428,8.9694986,5.4695292Q8.750021,5.6890736,8.750021,5.9999766L8.750021,11.500048Q8.750021,11.810951,8.9695234,12.030459Q9.1890306,12.249972,9.499959,12.249972Q9.810873,12.249954,10.030407,12.03047ZM12.752002,6.012969L12.752002,13.499995Q12.752002,14.016693,12.385324,14.383352Q12.018657,14.750002,11.50198,14.75L4.5009426999999995,14.75Q3.9842849,14.75,3.6176536,14.383353Q3.2510145,14.016699,3.2510145,13.499994L3.2510145,5.9925132Q3.2510145,5.7424955,3.5009694,5.7424955Q3.7509875,5.7424955,3.7509875,5.9925132L3.7509875,13.50001Q3.7509875,13.810539,3.9709406,14.030359Q4.1907439,14.250029,4.5009584,14.250029L11.501979,14.250029Q11.812514,14.250029,12.032232,14.030302Q12.251951,13.810574,12.251951,13.50001L12.251951,6.012969Q12.251951,5.7629986,12.501967,5.7629986Q12.752002,5.7629986,12.752002,6.012969ZM6.7499328,5.9999766L6.7499328,11.500002Q6.7499328,11.749958,6.4999957,11.749972Q6.2499762,11.749972,6.2499762,11.500048L6.2499762,5.9999766Q6.2499762,5.7499428,6.5000095,5.7499428Q6.7499328,5.7499428,6.7499328,5.9999766ZM9.749979,5.9999766L9.749979,11.500002Q9.749979,11.749958,9.4999313,11.749972Q9.250021,11.749972,9.250021,11.500048L9.250021,5.9999766Q9.250021,5.7499428,9.4999447,5.7499428Q9.749979,5.7499428,9.749979,5.9999766Z",fillRule:"evenodd",fill:"#14C9BB",fillOpacity:1})))),Qt=V.memo(({label:e,icon:n,tooltip:o,isCompact:i,style:s,...l})=>{const{useBreakpoint:d}=vn,a=d();return i||!a.md&&!a.lg&&!a.xl?t.jsx(Ae,{title:o??e,children:t.jsx(vt,{...l,style:{display:"inline-flex",alignItems:"center",justifyContent:"center",...s},children:n})}):t.jsx(Ae,{title:o??e,children:t.jsxs(vt,{...l,style:{display:"inline-flex",alignItems:"center",gap:4,...s},children:[n,t.jsx("span",{style:{fontSize:12,fontWeight:400,borderRadius:"2px"},children:e})]})})}),{Text:Un}=ke,ze=({label:e,value:n,width:o=315})=>{const i=typeof n=="string"||typeof n=="number"?String(n):"";return t.jsxs("div",{style:{width:o,display:"flex",alignItems:"center",gap:10},children:[t.jsx(Un,{type:"secondary",style:{fontWeight:400,fontSize:14,color:"#929A9E",minWidth:120},children:e}),t.jsx(Ae,{title:i||n||"----",placement:"top",children:t.jsx(Un,{ellipsis:!0,style:{fontWeight:600,fontSize:14,color:"#212519",flex:1},children:n||"----"})})]})},{Title:bl,Text:Sl}=ke,ui=({serialNumber:e})=>{var x,C,m,b,L,w,y,v,j,k,R,I,M,T,Q,F,_,$,B;const{t:n}=H(),o=$t({serialNumber:e,disableToast:!0}),i=vo({serialNumber:e,onError:()=>{}}),s=Js({serialNumber:e,onError:()=>{}}),[l,d]=r.useState({x:0,y:0}),[a,c]=r.useState(!1),u=D=>{const O=D.currentTarget.getBoundingClientRect();d({x:D.clientX-O.left,y:D.clientY-O.top})},h=()=>c(!0),g=()=>c(!1),p=()=>{var D,O,W,E;if((O=(D=s.data)==null?void 0:D.unit)!=null&&O.memory){const P=s.data.unit.memory.total-s.data.unit.memory.free,G=((E=(W=s.data)==null?void 0:W.unit)==null?void 0:E.memory.total)>0?P/s.data.unit.memory.total*100:0,re={green:{color:"#2BC174",backgroundColor:"rgba(43, 193, 116, 0.1)",border:"1px solid #2BC174"},yellow:{color:"#FFB020",backgroundColor:"rgba(255, 176, 32, 0.1)",border:"1px solid #FFB020"},red:{color:"#F54E45",backgroundColor:"rgba(245, 78, 69, 0.1)",border:"1px solid #F54E45"}};let oe="red";return G<60?oe="green":G<85&&(oe="yellow"),t.jsxs(t.Fragment,{children:[Cs(s.data.unit.memory.total),t.jsxs(vt,{style:{marginLeft:8,...re[oe]},children:[Math.floor(G*100)/100,"% ",n("controller.stats.used")]})]})}return"-"},f=()=>{var D,O;if((D=o.data)!=null&&D.compatible)return o.data.compatible.includes(" ")?o.data.compatible.replaceAll(" ","_"):(O=o.data)==null?void 0:O.compatible};return t.jsxs("div",{style:{display:"flex",alignItems:"center",gap:10,marginTop:-24},children:[t.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",width:100},children:[t.jsx("div",{className:"device-image-wrapper",onMouseMove:u,onMouseEnter:h,onMouseLeave:g,children:t.jsx(Fo,{src:`/devices/${f()}.png`,alt:(x=o==null?void 0:o.data)==null?void 0:x.compatible,width:140,height:140,fallback:"devices/generic_ap.png",preview:{mask:a&&t.jsx("div",{className:"device-preview-mask",style:{left:l.x,top:l.y},children:t.jsx(To,{})})}})}),t.jsxs("div",{style:{display:"flex",textAlign:"center",alignItems:"center"},children:[(C=o.data)!=null&&C.blackListed?t.jsx(Qt,{label:n("Denied"),tooltip:n("devices.blacklisted_description"),colorScheme:"red",style:{background:"rgba(245,63,63,0.1)",border:"1px solid #F53F3F",color:"#F53F3F"}}):(m=i==null?void 0:i.data)!=null&&m.connected?t.jsx(Qt,{label:n("Connected"),colorScheme:"green",style:{background:"rgba(43,193,116,0.1)",border:"1px solid #2BC174",color:"#2BC174"},icon:null}):t.jsx(Qt,{label:n("Disconnected"),colorScheme:"red",style:{background:"#F4F5F7",border:"1px solid #DADCE1",color:"#B3BBC8"},icon:null}),((b=o.data)==null?void 0:b.restrictedDevice)&&t.jsx(Qt,{label:`${n("devices.restricted")} (Dev Mode)`,tooltip:n("devices.restricted_overriden"),colorScheme:"green"})]})]}),t.jsx("div",{style:{width:1,height:100,background:"#F2F2F2",marginLeft:20,marginRight:20}}),t.jsxs("div",{style:{flex:1,display:"flex",flexWrap:"wrap",gap:24},children:[t.jsx(ze,{label:n("certificates.model"),value:(L=o.data)==null?void 0:L.manufacturer}),t.jsx(ze,{label:n("commands.revision"),value:vs((w=o.data)==null?void 0:w.firmware)}),t.jsx(ze,{label:n("system.uptime"),value:(v=(y=s.data)==null?void 0:y.unit)!=null&&v.uptime?ys((j=s.data)==null?void 0:j.unit.uptime,n):void 0}),t.jsx(ze,{label:n("controller.stats.load"),value:(R=(k=s.data)==null?void 0:k.unit)!=null&&R.load?s.data.unit.load.map(D=>D.toFixed(2)).join(" | "):void 0}),t.jsx(ze,{label:n("controller.devices.localtime"),value:(M=(I=s.data)==null?void 0:I.unit)!=null&&M.localtime?Ft((T=s.data)==null?void 0:T.unit.localtime):void 0}),t.jsx(ze,{label:n("analytics.last_contact"),value:(Q=i==null?void 0:i.data)!=null&&Q.lastContact&&(i==null?void 0:i.data.lastContact)!==0?t.jsx(Ze,{date:i.data.lastContact}):t.jsx(Ze,{date:(F=o.data)==null?void 0:F.lastRecordedContact})}),t.jsx(ze,{label:n("analytics.memory"),value:p()}),t.jsx(ze,{label:"Connect Reason",value:(_=i.data)!=null&&_.connectReason&&(($=i.data)==null?void 0:$.connectReason.length)>0?yt((B=i.data)==null?void 0:B.connectReason):void 0})]})]})},mi=V.memo(ui),hi=e=>r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:16,height:16,viewBox:"0 0 16 16",...e},r.createElement("g",null,r.createElement("g",null),r.createElement("g",null,r.createElement("g",null,r.createElement("g",null,r.createElement("path",{d:"M1.7538202953674316,5.980725054632568Q1.6500000953674316,5.804517884632569,1.6500000953674316,5.599999904632568Q1.6500000953674316,5.526131361632569,1.6644111853674315,5.4536821846325685Q1.6788222153674317,5.381233024632568,1.7070904953674315,5.312987384632568Q1.7353587753674318,5.244741734632568,1.7763979453674317,5.183322284632569Q1.8174371153674316,5.121902824632568,1.8696700353674316,5.0696698446325685Q1.9219030153674317,5.017436924632569,1.9833224753674317,4.976397754632568Q2.0447419253674317,4.935358584632568,2.1129875753674314,4.907090304632568Q2.1812332153674316,4.878822024632568,2.2536823753674318,4.864410994632569Q2.3261315523674315,4.849999904632568,2.4000000953674316,4.849999904632568Q2.4978984523674317,4.849999904632568,2.592516635367432,4.875129344632568Q2.6871348353674316,4.900258774632569,2.7721323353674316,4.948833584632569Q2.857129815367432,4.997408454632568,2.9268107453674315,5.0661736746325685Q2.9964917253674317,5.134938834632568,3.0461867453674314,5.219286324632568Q3.9143363953674317,6.692770204632568,5.617108595367432,7.459737204632568Q6.714384995367432,7.953975904632568,7.936992195367432,8.034892104632569Q9.12031079536743,8.113208104632568,10.254956295367432,7.7957432046325685Q11.382836795367432,7.480171304632568,12.283577395367432,6.823578204632568Q13.203831095367432,6.152761814632568,13.753817095367431,5.219287574632569L15.046183095367432,5.980726654632568Q14.338480095367432,7.1818863046325685,13.167163095367432,8.035716104632568Q12.047682295367432,8.85175900463257,10.659121995367432,9.240267004632567Q9.277329895367432,9.626881604632569,7.837933995367432,9.531617604632569Q6.345195795367432,9.432823704632568,5.001080795367431,8.827403104632568Q2.8639563353674315,7.8647942046325685,1.7539291353674318,5.980909764632568L1.7538533253674315,5.9807811346325686L1.7538202953674316,5.980725054632568Z",fillRule:"evenodd",fill:"#929A9E",fillOpacity:1}))),r.createElement("g",null,r.createElement("g",{transform:"matrix(-1,0,0,1,27.2000013589859,0)"},r.createElement("path",{d:"M16.13448097949295,8.73773146Q16.25246987949295,8.63108528,16.31701997949295,8.4857302Q16.38157017949295,8.3403751,16.38157017949295,8.18133163Q16.38157017949295,8.10746308,16.36715917949295,8.035013899Q16.35274797949295,7.962564722,16.32447967949295,7.89431909Q16.29621127949295,7.82607345,16.25517207949295,7.764654Q16.21413307949295,7.70323452,16.161900079492952,7.65100157Q16.10966707949295,7.5987686199999995,16.04824737949295,7.55772948Q15.98682817949295,7.5166903099999995,15.918582479492951,7.48842204Q15.85033687949295,7.46015376,15.77788767949295,7.44574273Q15.705438479492951,7.43133163,15.63157017949295,7.43133163Q15.492195679492951,7.43133163,15.36212617949295,7.4814031100000005Q15.23205667949295,7.53147459,15.12865937949295,7.62493175L15.12834307949295,7.62521797L13.09708994949295,9.461198Q12.97910087949295,9.5678443,12.91455077949295,9.7131994Q12.85000067949295,9.8585545,12.85000067949295,10.0175979Q12.85000067949295,10.0914664,12.86441176949295,10.1639154Q12.87882279949295,10.2363646,12.90709107949295,10.3046103Q12.935359359492951,10.3728561,12.97639852949295,10.4342756Q13.01743769949295,10.495695099999999,13.06967061949295,10.5479279Q13.121903599492951,10.6001606,13.18332305949295,10.641199799999999Q13.24474250949295,10.6822388,13.312988159492951,10.7105072Q13.38123379949295,10.7387755,13.45368295949295,10.7531867Q13.52613213649295,10.7675977,13.60000067949295,10.7675979Q13.739375159492951,10.7675977,13.869444729492951,10.7175264Q13.999514279492951,10.6674547,14.10291152949295,10.5739977L14.10331373949295,10.5736339L16.13448097949295,8.7377314L16.13448097949295,8.73773146Z",fillRule:"evenodd",fill:"#929A9E",fillOpacity:1})),r.createElement("g",null,r.createElement("path",{d:"M5.734480407288361,8.73773146Q5.852469307288361,8.63108528,5.917019407288361,8.4857302Q5.98156960728836,8.3403751,5.98156960728836,8.18133163Q5.98156960728836,8.10746308,5.9671586072883605,8.035013899Q5.95274740728836,7.962564722,5.924479107288361,7.89431909Q5.896210707288361,7.82607345,5.855171507288361,7.764654Q5.8141325072883605,7.70323452,5.76189950728836,7.65100157Q5.709666507288361,7.5987686199999995,5.648246807288361,7.55772948Q5.5868276072883605,7.5166903099999995,5.518581907288361,7.48842204Q5.45033630728836,7.46015376,5.377887107288361,7.44574273Q5.30543790728836,7.43133163,5.23156960728836,7.43133163Q5.092195107288361,7.43133163,4.96212560728836,7.4814031100000005Q4.832056107288361,7.53147459,4.728658807288361,7.62493175L4.72834250728836,7.62521797L2.6970893772883606,9.461198Q2.5791003072883605,9.5678443,2.5145502072883605,9.7131994Q2.4500001072883606,9.8585545,2.4500001072883606,10.0175979Q2.4500001072883606,10.0914664,2.4644111972883604,10.1639154Q2.4788222272883607,10.2363646,2.5070905072883605,10.3046103Q2.5353587872883607,10.3728561,2.5763979572883606,10.4342756Q2.6174371272883605,10.495695099999999,2.6696700472883608,10.5479279Q2.7219030272883606,10.6001606,2.7833224872883604,10.641199799999999Q2.8447419372883607,10.6822388,2.9129875872883604,10.7105072Q2.9812332272883606,10.7387755,3.0536823872883607,10.7531867Q3.1261315642883605,10.7675977,3.2000001072883606,10.7675979Q3.3393745872883605,10.7675977,3.4694441572883608,10.7175264Q3.5995137072883607,10.6674547,3.7029109572883607,10.5739977L3.7033131672883606,10.5736339L5.734480407288361,8.7377314L5.734480407288361,8.73773146Z",fillRule:"evenodd",fill:"#929A9E",fillOpacity:1})),r.createElement("g",{transform:"matrix(0.8231573104858398,-0.5678133964538574,0.5678133964538574,0.8231573104858398,-4.147649012455986,5.7333218789854925)"},r.createElement("path",{d:"M9.032640288217927,9.94057810435028Q9.158053888217927,9.75191854435028,9.158053888217927,9.52537715435028Q9.158053888217927,9.451508611350281,9.143642688217927,9.37905943435028Q9.129231488217926,9.30661027435028,9.100963388217925,9.238364634350281Q9.072695088217927,9.17011898435028,9.031655988217926,9.108699534350281Q8.990616888217925,9.047280074350281,8.938383988217925,8.99504709435028Q8.886150888217927,8.94281417435028,8.824731388217927,8.90177500435028Q8.763311888217926,8.860735834350281,8.695066288217927,8.83246755435028Q8.626820788217927,8.80419927435028,8.554371688217927,8.78978824435028Q8.481922488217926,8.77537715435028,8.408053888217927,8.77537715435028Q8.315325088217925,8.77537715435028,8.225388488217925,8.79796159435028Q8.135451888217926,8.82054609435028,8.053724048217926,8.86435496435028Q7.971996308217926,8.90816378435028,7.903399348217926,8.970558524350281Q7.834802388217926,9.032953264350281,7.783467528217926,9.110176174350281L6.506083728217926,11.031743554350282L6.505975488217926,11.03190635435028Q6.380561888217926,11.220565954350281,6.380561888217926,11.447107154350281Q6.380561888217926,11.52097575435028,6.394972978217926,11.593424954350281Q6.409384008217926,11.66587385435028,6.437652288217926,11.734119554350281Q6.465920568217926,11.80236515435028,6.506959738217926,11.863784654350281Q6.547998908217926,11.925204154350281,6.600231828217926,11.97743715435028Q6.652464808217926,12.029670154350281,6.713884268217926,12.070709154350281Q6.775303718217926,12.111748354350281,6.843549368217926,12.14001665435028Q6.911795008217926,12.168285054350282,6.984244168217926,12.182695954350281Q7.056693345217926,12.197107154350281,7.130561888217926,12.197107154350281Q7.223290756217926,12.197107154350281,7.313227328217926,12.174522754350281Q7.403163878217926,12.15193835435028,7.484891648217926,12.10812935435028Q7.566619458217926,12.064320954350281,7.635216418217926,12.001926054350282Q7.703813378217926,11.93953145435028,7.755148168217926,11.862308354350281L9.032640388217926,9.94057813435028L9.032640288217927,9.94057810435028Z",fillRule:"evenodd",fill:"#929A9E",fillOpacity:1})))))),{Text:ve}=ke,fi=({serialNumber:e})=>{var h,g,p,f,x,C,m,b,L,w;const{t:n}=H(),o=$t({serialNumber:e,disableToast:!0}),i=ea({serialNumber:e}),s=lo(),[l,d]=V.useState(!1),a=(h=o.data)!=null&&h.devicePassword&&((g=o.data)==null?void 0:g.devicePassword)!==""?o.data.devicePassword:"adminwifi",c=async()=>{try{await navigator.clipboard.writeText(a),Le.success(n("Successfully Copied"))}catch{Le.error(n("common.copyFailed")||"Copy failed")}},u=y=>()=>s(`/wireless/${y}`);return t.jsxs("div",{children:[t.jsxs("div",{className:"password-section",style:{display:"flex",alignItems:"center",marginBottom:22,marginTop:-13,marginLeft:-24},children:[t.jsxs("div",{style:{display:"flex",alignItems:"center",marginRight:20,marginLeft:26,marginTop:20,fontWeight:400,fontSize:14,color:"#212519"},children:[t.jsx(ve,{children:n("common.password")}),t.jsx("span",{style:{color:"#F53F3F",fontSize:18,marginLeft:4},children:"*"})]}),t.jsxs("div",{style:{display:"flex",alignItems:"center",gap:14},children:[t.jsx(co.Password,{value:a,readOnly:!0,visibilityToggle:!0,iconRender:y=>t.jsx(fe,{component:hi,style:{fontSize:16}}),style:{marginTop:20,width:280,height:36,borderRadius:"2px",border:"1px solid #B2B2B2"}}),t.jsx(q,{onClick:c,type:"primary",style:{background:"#14C9BB",borderRadius:2,width:100,height:36,fontSize:15,marginTop:20},children:n("common.copy")})]})]}),t.jsxs("div",{className:"device-info",style:{display:"grid",gap:26,gridTemplateColumns:"repeat(3, 1fr)",marginLeft:-25},children:[t.jsxs("div",{style:{background:"#F8FAFB",borderRadius:"0px",lineHeight:"48px",padding:"0 16px",marginLeft:30},children:[t.jsx(ve,{style:{marginRight:60,fontWeight:400,fontSize:14},children:"MAC"}),t.jsx(ve,{style:{fontWeight:600,fontSize:14},children:((p=o.data)==null?void 0:p.macAddress)||"-"})]}),t.jsxs("div",{style:{background:"#F8FAFB",borderRadius:"0px",lineHeight:"48px",padding:"0 16px"},children:[t.jsx(ve,{style:{marginRight:50,fontWeight:400,fontSize:14},children:n("common.manufacturer")}),t.jsx(ve,{style:{fontWeight:600,fontSize:14},children:"fs.com"})]}),t.jsxs("div",{style:{background:"#F8FAFB",borderRadius:"0px",lineHeight:"48px",padding:"0 16px"},children:[t.jsx(ve,{style:{marginRight:135,fontWeight:400,fontSize:14},children:n("common.type")}),t.jsx(ve,{style:{fontWeight:600,fontSize:14,marginLeft:-66},children:((f=o.data)==null?void 0:f.deviceType)||"-"})]}),t.jsxs("div",{style:{borderRadius:"0px",lineHeight:"48px",padding:"0 16px",marginLeft:30},children:[t.jsx(ve,{style:{marginRight:44,fontWeight:400,fontSize:14},children:n("common.created")}),t.jsx(ve,{style:{fontWeight:600,fontSize:14},children:(x=o.data)!=null&&x.createdTimestamp?Ft(o.data.createdTimestamp):"-"})]}),t.jsxs("div",{style:{borderRadius:"0px",lineHeight:"48px",padding:"0 16px"},children:[t.jsx(ve,{style:{marginRight:80,fontWeight:400,fontSize:14},children:n("common.modified")}),t.jsx(ve,{style:{fontWeight:600,fontSize:14},children:(C=o.data)!=null&&C.modified?Ft(o.data.modified):"-"})]}),t.jsxs("div",{style:{borderRadius:"0px",lineHeight:"48px",padding:"0 16px"},children:[t.jsx(ve,{style:{marginRight:75,fontWeight:400,fontSize:14},children:n("Site")}),t.jsx(ve,{style:{fontWeight:600,fontSize:14},children:(L=(b=(m=i.data)==null?void 0:m.extendedInfo)==null?void 0:b.venue)!=null&&L.name?t.jsx(ve,{style:{cursor:"pointer",color:"#14C9BB",textDecoration:"underline",textDecorationColor:"#14C9BB"},onClick:u(`manage/Monitor#${(w=i.data)==null?void 0:w.venue}`),children:i.data.extendedInfo.venue.name}):"Default"})]})]})]})},gi=V.memo(fi),yn=e=>{const n=new Date(e*1e3),o=n.getMonth()+1,i=n.getDate(),s=n.getHours().toString().padStart(2,"0"),l=n.getMinutes().toString().padStart(2,"0");return`${o}/${i} ${s}:${l}`},Cn=e=>{const n=new Date(e*1e3),o=n.getMonth()+1,i=n.getDate(),s=n.getHours().toString().padStart(2,"0"),l=n.getMinutes().toString().padStart(2,"0");return`${o}/${i} ${s}:${l}`},bn=e=>{if(e<=6)return Array.from({length:e},(i,s)=>s);const n=Math.ceil(e/5),o=[];for(let i=0;i<e;i+=n)o.push(i);return o.includes(e-1)||o.push(e-1),o},Oo=e=>e<1024?{factor:1,unit:"B"}:e<1024*1024?{factor:1024,unit:"KB"}:e<1024*1024*1024?{factor:1024*1024,unit:"MB"}:{factor:1024*1024*1024,unit:"GB"},Po=e=>e<1e3?{factor:1,unit:""}:e<1e3*1e3?{factor:1e3,unit:"K"}:e<1e3*1e3*1e3?{factor:1e3*1e3,unit:"M"}:{factor:1e3*1e3*1e3,unit:"G"},pi=({data:e,format:n})=>{const{colorMode:o}=cn(),i=o==="dark",s=r.useRef(null);let l=null;const d="#14C9BB",a="#21CCFF",{factor:c,unit:u}=Oo(e.maxTx),h=Po(e.maxPacketsTx>e.maxPacketsRx?e.maxPacketsTx:e.maxPacketsRx),g=bn(e.recorded.length),p=()=>{const x=i?"rgba(20, 201, 187, 0.2)":"rgba(20, 201, 187, 0.3)",C=i?"rgba(33, 204, 255, 0.2)":"rgba(33, 204, 255, 0.3)";return n==="bytes"?[{name:"RX",data:e.tx.map(m=>Math.floor(m/c*100)/100),type:"line",smooth:!0,symbol:"none",lineStyle:{color:d},areaStyle:{color:x},itemStyle:{color:d}},{name:"TX",data:e.rx.map(m=>Math.floor(m/c*100)/100),type:"line",smooth:!0,symbol:"none",lineStyle:{color:a},areaStyle:{color:C},itemStyle:{color:a}}]:[{name:"RX",data:e.packetsTx,type:"line",smooth:!0,symbol:"none",lineStyle:{color:d},areaStyle:{color:x},itemStyle:{color:d}},{name:"TX",data:e.packetsRx,type:"line",smooth:!0,symbol:"none",lineStyle:{color:a},areaStyle:{color:C},itemStyle:{color:a}}]},f=()=>{const x=i?"white":"#333",C=i?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)";return{backgroundColor:"transparent",tooltip:{trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:i?"#333":"#fff",color:x}},formatter:m=>{const b=e.recorded[m[0].dataIndex];let L=Cn(b)+"<br/>";return m.forEach(w=>{var j;const y=((j=w.seriesItemStyle)==null?void 0:j.color)||w.color,v=n==="bytes"?`${w.value.toFixed(2)} ${u}`:`${h.factor===1?w.value.toLocaleString():(w.value/h.factor).toFixed(1)+h.unit}`;L+=`<span style="display:inline-block;width:10px;height:10px;background-color:${y};margin-right:5px;"></span>${w.seriesName}: ${v}<br/>`}),L}},legend:{data:[{name:"RX",itemStyle:{color:d}},{name:"TX",itemStyle:{color:a}}],bottom:0,left:"center",textStyle:{color:x},icon:"roundRect",itemWidth:10,itemHeight:10,itemGap:15},grid:{left:"1%",right:"2%",top:"32px",bottom:"10%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:e.recorded.map((m,b)=>g.includes(b)?yn(m):""),axisLine:{lineStyle:{color:C}},axisTick:{show:!0,alignWithLabel:!0,interval:m=>g.includes(m)},axisLabel:{color:x,rotate:0,padding:[0,0,0,15],interval:m=>g.includes(m)},splitLine:{lineStyle:{color:C}}},yAxis:{type:"value",min:0,axisLine:{lineStyle:{color:C}},axisLabel:{color:x,margin:32,formatter:m=>{if(n==="bytes"){const b=String(m);return`${b.includes(".")?Number(b).toFixed(1):b} ${u}`}else return h.factor===1?m.toLocaleString():`${(m/h.factor).toFixed(1)}${h.unit}`}},splitLine:{lineStyle:{color:C}}},series:p()}};return r.useEffect(()=>{s.current&&(l&&dt(l),l=dn(s.current),l.setOption(f()));const x=()=>{l==null||l.resize()};return window.addEventListener("resize",x),()=>{window.removeEventListener("resize",x),l&&(dt(l),l=null)}},[e,n,i]),r.useEffect(()=>{l&&l.setOption(f())},[e,n,i]),t.jsx("div",{ref:s,style:{width:"100%",height:"100%",minHeight:"300px"}})},xi=r.memo(pi),vi=({data:e})=>{const{colorMode:n}=cn(),o=n==="dark",i=r.useRef(null);let s=null;const l="#249EFF",d="#21CCFF",a="#756FF3",c=bn(e.recorded.length),u=()=>{const g=`${l}33`,p=`${d}33`,f=`${a}33`,x=C=>C.map(m=>Math.floor(m/1024/1024));return[{name:"Free",data:x(e.free),type:"line",smooth:!0,symbol:"none",lineStyle:{color:l},areaStyle:{color:g},itemStyle:{color:l}},{name:"Cached",data:x(e.cached),type:"line",smooth:!0,symbol:"none",lineStyle:{color:d},areaStyle:{color:p},itemStyle:{color:d}},{name:"Buffered",data:x(e.buffered),type:"line",smooth:!0,symbol:"none",lineStyle:{color:a},areaStyle:{color:f},itemStyle:{color:a}}]},h=()=>{const g=o?"white":"#333",p=o?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)";return{backgroundColor:"transparent",tooltip:{trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:o?"#333":"#fff",color:g}},formatter:f=>{const x=e.recorded[f[0].dataIndex];let C=Cn(x)+"<br/>";return f.forEach(m=>{C+=`<span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${m.color};margin-right:5px;"></span>${m.seriesName}: ${m.value} MB<br/>`}),C}},legend:{bottom:0,left:"center",textStyle:{color:g},data:[{name:"Free",itemStyle:{color:l}},{name:"Cached",itemStyle:{color:d}},{name:"Buffered",itemStyle:{color:a}}],icon:"roundRect",itemWidth:10,itemHeight:10,itemGap:15},grid:{left:"1%",right:"2%",top:"32px",bottom:"10%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:e.recorded.map((f,x)=>c.includes(x)?yn(f):""),axisLine:{lineStyle:{color:p}},axisTick:{show:!0,alignWithLabel:!0,interval:f=>c.includes(f)},axisLabel:{color:g,rotate:0,padding:[0,0,0,15],interval:f=>c.includes(f)},splitLine:{lineStyle:{color:p}}},yAxis:{type:"value",axisLine:{lineStyle:{color:p}},axisLabel:{color:g,margin:32,formatter:f=>`${f} MB`},splitLine:{lineStyle:{color:p}}},series:u()}};return r.useEffect(()=>{i.current&&(s&&dt(s),s=dn(i.current),s.setOption(h()));const g=()=>{s==null||s.resize()};return window.addEventListener("resize",g),()=>{window.removeEventListener("resize",g),s&&(dt(s),s=null)}},[e,o]),r.useEffect(()=>{s&&s.setOption(h())},[e,o]),t.jsx("div",{ref:i,style:{width:"100%",height:"100%",minHeight:"300px"}})},yi=r.memo(vi),Ci=(e,n)=>async()=>Ne.get(`device/${n}/statistics?newest=true&limit=${e}`).then(o=>o.data),bi=({serialNumber:e,limit:n,onError:o})=>mt(["deviceStatistics",e,"newest",{limit:n}],Ci(n,e),{enabled:e!==void 0&&e!=="",staleTime:1e3*60,onError:o}),Si=async e=>Ne.get(`device/${e.serialNumber}/statistics?startDate=${e.start}&endDate=${e.end}&offset=${e.offset}&limit=100`).then(n=>n.data.data),ji=async e=>Ne.get(`device/${e.serialNumber}/statistics?startDate=${e.start}&endDate=${e.end}&countOnly=true`).then(n=>n.data.count).catch(()=>0),wi=(e,n)=>async()=>{const{start:o,end:i,serialNumber:s}=e;n&&n(0);const l=await ji(e);let d=[],a=0,c;do c=await Si({start:o,end:i,serialNumber:s,offset:a}),d=d.concat(c),a+=100,n&&l>0&&n(d.length/l*100);while(c.length===100);return n&&n(100),d.sort((u,h)=>u.recorded-h.recorded)},Li=({serialNumber:e,start:n,end:o,onError:i,setProgress:s})=>mt(["deviceStatistics",e,{start:n,end:o}],wi({start:n,end:o,serialNumber:e},s),{enabled:e!==void 0&&e!=="",staleTime:1e3*60,onError:i}),ki=e=>{var o;let n;return e.unit&&e.unit.memory&&(n=e.unit.memory.total-e.unit.memory.free),{...(o=e.unit)==null?void 0:o.memory,used:n}},Ri=({serialNumber:e})=>{const[n,o]=V.useState("memory"),[i,s]=V.useState(0),[l,d]=V.useState(!1),[a,c]=V.useState(),u=V.useCallback(m=>{s(m)},[]),h=bi({serialNumber:e,limit:30}),g=Li({serialNumber:e,start:(a==null?void 0:a.start)===0?0:a!=null&&a.start?Math.floor(a.start.getTime()/1e3):Math.floor((new Date().getTime()-60*60*1e3)/1e3),end:(a==null?void 0:a.end)===0?0:a!=null&&a.end?Math.floor(a.end.getTime()/1e3):Math.floor(new Date().getTime()/1e3),setProgress:u}),p=m=>{d(!0),o(m)},f=V.useMemo(()=>{var m,b,L,w,y,v,j,k,R,I,M,T,Q,F,_,$,B,D,O,W,E,P,G,re,oe,K,se,Y,J,U,N,X,te,Re,$e,_e,Be,ce;if(!(!h.data&&!g.data))try{const A={},ae={used:[],buffered:[],cached:[],free:[],total:[],recorded:[]},z={},ye={},De={},Te={},Qe={},Ye={},Xe={},qe={},je={};let ge=g.data??((m=h.data)==null?void 0:m.data);ge&&!g.data&&(ge=[...ge].reverse());for(const[st,de]of ge?ge.entries():[])if(st===0){let pe=!1;for(const S of de.data.interfaces??[])!l&&!pe&&n==="memory"&&(pe=!0,o(S.name)),ye[S.name]=((b=S.counters)==null?void 0:b.rx_bytes)??0,De[S.name]=((L=S.counters)==null?void 0:L.tx_bytes)??0,Te[S.name]=((w=S.counters)==null?void 0:w.rx_packets)??0,Qe[S.name]=((y=S.counters)==null?void 0:y.tx_packets)??0;for(const S of de.data.dynamic_vlans??[])Ye[S.vid]=S.rx_bytes??0,Xe[S.vid]=S.tx_bytes??0,qe[S.vid]=S.rx_packets??0,je[S.vid]=S.tx_packets??0}else{const pe=ki(de.data);ae.used.push(pe.used??0),ae.buffered.push(pe.buffered??0),ae.cached.push(pe.cached??0),ae.free.push(pe.free??0),ae.total.push(pe.total??0),ae.recorded.push(de.recorded);for(const S of de.data.dynamic_vlans??[]){const at=S.rx_bytes??0,Oe=S.tx_bytes??0,Ee=S.rx_packets??0,ne=(S==null?void 0:S.tx_packets)??0;let Z=at-(Ye[S.vid]??0);Z<0&&(Z=0);let ue=Oe-(Xe[S.vid]??0);ue<0&&(ue=0);let xe=Ee-(qe[S.vid]??0);xe<0&&(xe=0);let Ce=ne-(je[S.vid]??0);Ce<0&&(Ce=0),z[S.vid]===void 0?z[S.vid]={rx:[Z],tx:[ue],packetsRx:[xe],packetsTx:[Ce],recorded:[de.recorded],maxTx:0,maxRx:0,maxPacketsRx:0,maxPacketsTx:0}:(z[S.vid]&&!((v=z[S.vid])!=null&&v.removed)&&((j=z[S.vid])==null?void 0:j.recorded.length)===1&&((k=z[S.vid])==null||k.tx.shift(),(R=z[S.vid])==null||R.rx.shift(),(I=z[S.vid])==null||I.packetsTx.shift(),(M=z[S.vid])==null||M.packetsRx.shift(),(T=z[S.vid])==null||T.recorded.shift(),z[S.vid].maxRx=Z,z[S.vid].maxTx=ue,z[S.vid].removed=!0),(Q=z[S.vid])==null||Q.rx.push(Z),(F=z[S.vid])==null||F.tx.push(ue),(_=z[S.vid])==null||_.packetsRx.push(xe),($=z[S.vid])==null||$.packetsTx.push(Ce),(B=z[S.vid])==null||B.recorded.push(de.recorded),z[S.vid]!==void 0&&ue>z[S.vid].maxTx&&(z[S.vid].maxTx=ue),z[S.vid]!==void 0&&Z>z[S.vid].maxRx&&(z[S.vid].maxRx=Z),z[S.vid]!==void 0&&Ce>z[S.vid].maxPacketsTx&&(z[S.vid].maxPacketsTx=Ce),z[S.vid]!==void 0&&xe>z[S.vid].maxPacketsRx&&(z[S.vid].maxPacketsRx=xe)),Ye[S.vid]=at,Xe[S.vid]=Oe,qe[S.vid]=Ee,je[S.vid]=ne}for(const S of de.data.interfaces??[]){const at=((D=S.name)==null?void 0:D.substring(0,2))==="up";let Oe=((O=S.counters)==null?void 0:O.rx_bytes)??0,Ee=((W=S.counters)==null?void 0:W.tx_bytes)??0,ne=((E=S.counters)==null?void 0:E.rx_packets)??0,Z=((P=S.counters)==null?void 0:P.tx_packets)??0;if(S["counters-aggregate"])Oe=S["counters-aggregate"].rx_bytes,Ee=S["counters-aggregate"].tx_bytes,ne=S["counters-aggregate"].rx_packets,Z=S["counters-aggregate"].tx_packets;else if(at)for(const It of S.ssids??[])Oe+=((G=It.counters)==null?void 0:G.rx_bytes)??0,Ee+=((re=It.counters)==null?void 0:re.tx_bytes)??0,ne+=((oe=It.counters)==null?void 0:oe.rx_packets)??0,Z+=((K=It.counters)==null?void 0:K.tx_packets)??0;let ue=Oe-(ye[S.name]??0);ue<0&&(ue=0);let xe=Ee-(De[S.name]??0);xe<0&&(xe=0);let Ce=ne-(Te[S.name]??0);Ce<0&&(Ce=0);let rt=Z-(Qe[S.name]??0);rt<0&&(rt=0),A[S.name]===void 0?A[S.name]={rx:[ue],tx:[xe],packetsRx:[Ce],packetsTx:[rt],recorded:[de.recorded],maxTx:0,maxRx:0,maxPacketsRx:0,maxPacketsTx:0}:(A[S.name]&&!((se=A[S.name])!=null&&se.removed)&&((Y=A[S.name])==null?void 0:Y.recorded.length)===1&&((J=A[S.name])==null||J.tx.shift(),(U=A[S.name])==null||U.rx.shift(),(N=A[S.name])==null||N.packetsTx.shift(),(X=A[S.name])==null||X.packetsRx.shift(),(te=A[S.name])==null||te.recorded.shift(),A[S.name].maxRx=ue,A[S.name].maxTx=xe,A[S.name].removed=!0),(Re=A[S.name])==null||Re.rx.push(ue),($e=A[S.name])==null||$e.tx.push(xe),(_e=A[S.name])==null||_e.packetsRx.push(Ce),(Be=A[S.name])==null||Be.packetsTx.push(rt),(ce=A[S.name])==null||ce.recorded.push(de.recorded),A[S.name]!==void 0&&xe>A[S.name].maxTx&&(A[S.name].maxTx=xe),A[S.name]!==void 0&&ue>A[S.name].maxRx&&(A[S.name].maxRx=ue),A[S.name]!==void 0&&rt>A[S.name].maxPacketsTx&&(A[S.name].maxPacketsTx=rt),A[S.name]!==void 0&&Ce>A[S.name].maxPacketsRx&&(A[S.name].maxPacketsRx=Ce)),ye[S.name]=Oe,De[S.name]=Ee,Te[S.name]=ne,Qe[S.name]=Z}}return{interfaces:A,memory:ae,vlans:z}}catch{return}},[h.data,g.data]),x=V.useCallback(()=>{const m=(a==null?void 0:a.start)===void 0||(a==null?void 0:a.start)===null,b=(a==null?void 0:a.end)===void 0||(a==null?void 0:a.end)===null;m&&b?h.refetch():g.refetch()},[a]),C=V.useMemo(()=>!a&&(h!=null&&h.isFetching)?{isLoading:!0}:a&&g.isFetching?{isLoading:!0,progress:i}:{isLoading:!1},[h,g,a,i]);return V.useMemo(()=>({parsedData:f,isLoading:C,onSelectInterface:p,selected:n,time:a,setTime:c,refresh:x}),[f,C,n,a])},Ii=({data:e,format:n})=>{const{colorMode:o}=cn(),i=o==="dark",s=r.useRef(null);let l=null;const d="#14C9BB",a="#21CCFF",{factor:c,unit:u}=Oo(e.maxTx),h=Po(e.maxPacketsTx>e.maxPacketsRx?e.maxPacketsTx:e.maxPacketsRx),g=bn(e.recorded.length),p=()=>{const x=i?"rgba(20, 201, 187, 0.2)":"rgba(20, 201, 187, 0.3)",C=i?"rgba(33, 204, 255, 0.2)":"rgba(33, 204, 255, 0.3)";return n==="bytes"?[{name:"Tx",data:e.rx.map(m=>Math.floor(m/c*100)/100),type:"line",smooth:!0,symbol:"none",lineStyle:{color:a},areaStyle:{color:C},itemStyle:{color:a},z:1},{name:"Rx",data:e.tx.map(m=>Math.floor(m/c*100)/100),type:"line",smooth:!0,symbol:"none",lineStyle:{color:d},areaStyle:{color:x},itemStyle:{color:d},z:2}]:[{name:"Tx",data:e.packetsRx,type:"line",smooth:!0,symbol:"none",lineStyle:{color:a},areaStyle:{color:C},itemStyle:{color:a},z:1},{name:"Rx",data:e.packetsTx,type:"line",smooth:!0,symbol:"none",lineStyle:{color:d},areaStyle:{color:x},itemStyle:{color:d},z:2}]},f=()=>{const x=i?"white":"#333",C=i?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)";return{backgroundColor:"transparent",tooltip:{trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:i?"#333":"#fff",color:x}},formatter:m=>{const b=e.recorded[m[0].dataIndex];let L=Cn(b)+"<br/>";return m.forEach(w=>{var j;const y=((j=w.seriesItemStyle)==null?void 0:j.color)||w.color,v=n==="bytes"?`${w.value.toFixed(2)} ${u}`:`${h.factor===1?w.value.toLocaleString():(w.value/h.factor).toFixed(1)+h.unit}`;L+=`<span style="display:inline-block;width:10px;height:10px;background-color:${y};margin-right:5px;"></span>${w.seriesName}: ${v}<br/>`}),L}},legend:{data:[{name:"Tx",itemStyle:{color:a}},{name:"Rx",itemStyle:{color:d}}],bottom:0,left:"center",textStyle:{color:x},icon:"roundRect",itemWidth:10,itemHeight:10,itemGap:15},grid:{left:"1%",right:"2%",top:"32px",bottom:"10%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:e.recorded.map((m,b)=>g.includes(b)?yn(m):""),axisLine:{lineStyle:{color:C}},axisTick:{show:!0,alignWithLabel:!0,interval:m=>g.includes(m)},axisLabel:{color:x,rotate:0,padding:[0,0,0,15],interval:m=>g.includes(m)},splitLine:{lineStyle:{color:C}}},yAxis:{type:"value",min:0,axisLine:{lineStyle:{color:C}},axisLabel:{color:x,margin:32,formatter:m=>{if(n==="bytes"){const b=String(m);return`${b.includes(".")?Number(b).toFixed(1):b} ${u}`}else return h.factor===1?m.toLocaleString():`${(m/h.factor).toFixed(1)}${h.unit}`}},splitLine:{lineStyle:{color:C}}},series:p()}};return r.useEffect(()=>{s.current&&(l&&dt(l),l=dn(s.current),l.setOption(f()));const x=()=>{l==null||l.resize()};return window.addEventListener("resize",x),()=>{window.removeEventListener("resize",x),l&&(dt(l),l=null)}},[e,n,i]),r.useEffect(()=>{l&&l.setOption(f())},[e,n,i]),t.jsx("div",{ref:s,style:{width:"100%",height:"100%",minHeight:"300px"}})},Mi=r.memo(Ii),{Text:Ti}=ke,Qi=e=>{if(!e)return"";if(e.startsWith("up")){const n=e.split("v"),o=n[n.length-1];return o==="0"?"Upstream":`Upstream - Vlan ${o}`}if(e.startsWith("down")){const n=e.split("v"),o=n[n.length-1];return o==="0"?"Downstream":`Downstream - Vlan ${o}`}return e},Ei=()=>t.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:t.jsx(Ss,{image:js,description:"No Data",imageStyle:{marginTop:16,marginBottom:0}})}),Fi=({serialNumber:e})=>{const{t:n}=H(),{time:o,setTime:i,parsedData:s,isLoading:l,selected:d,onSelectInterface:a,refresh:c}=Ri({serialNumber:e}),[u,h]=r.useState("bytes"),g=[Se().subtract(1,"hour"),Se()],p=r.useMemo(()=>{var M,T,Q,F,_;const y=[];if(!s)return null;if(d==="memory"){const $=((M=s.memory)==null?void 0:M.recorded)||[];y.push(...$)}else if(d.startsWith("VLAN-")){const $=d.replace("VLAN-",""),B=((Q=(T=s.vlans)==null?void 0:T[$])==null?void 0:Q.recorded)||[];y.push(...B)}else{const $=((_=(F=s.interfaces)==null?void 0:F[d])==null?void 0:_.recorded)||[];y.push(...$)}const v=[...new Set(y.filter($=>$>0))];if(v.length===0)return null;const j=Math.min(...v),k=Math.max(...v),R=new Date(j*1e3),I=new Date(k*1e3);return{realStart:R,realEnd:I}},[s,d]),f=y=>{const v=y&&y[0]?y[0].toDate():0,j=y&&y[1]?y[1].toDate():0;i({start:v,end:j})},x=y=>{h(y)},C=r.useMemo(()=>{if(!s||!s.interfaces||!s.interfaces[d])return null;const y=s.interfaces[d];return!y.recorded||y.recorded.length===0?null:t.jsx(xi,{data:y,format:u})},[s,d,u]),m=r.useMemo(()=>{if(!s||!s.vlans)return null;const y=d.startsWith("VLAN-")?d.replace("VLAN-",""):null;if(!y||!s.vlans[y])return null;const v=s.vlans[y];return!v.recorded||v.recorded.length===0?null:t.jsx(Mi,{data:v,format:u})},[s,d,u]),b=r.useMemo(()=>!s||!s.memory||!s.memory.recorded||s.memory.recorded.length===0?null:t.jsx(yi,{data:s.memory}),[s]),L=r.useMemo(()=>{if(!s)return!0;if(d==="memory")return!b||!s.memory||!s.memory.recorded||s.memory.recorded.length===0;if(d.startsWith("VLAN-")){const y=d.replace("VLAN-","");return!m||!s.vlans||!s.vlans[y]||!s.vlans[y].recorded||s.vlans[y].recorded.length===0}return!C||!s.interfaces||!s.interfaces[d]||!s.interfaces[d].recorded||s.interfaces[d].recorded.length===0},[s,d,C,m,b]),w=r.useMemo(()=>o?[o.start?Se(o.start):void 0,o.end?Se(o.end):void 0]:g,[o]);return t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{padding:"8px 0"},children:t.jsx(Rn,{gutter:[16,16],children:t.jsx(bs,{span:24,children:t.jsxs(In,{wrap:!0,children:[t.jsx("div",{style:{display:"flex",alignItems:"center",fontSize:"14px",fontWeight:400},children:"Time"}),t.jsx("div",{style:{marginLeft:"32px"},children:t.jsx(Ds,{value:w,onChange:f,tooltipText:n("controller.crud.choose_time")})}),d!=="memory"&&t.jsx("div",{style:{marginLeft:"80px"},children:t.jsx(ht,{value:u,onChange:x,style:{width:280},options:[{value:"bytes",label:"Data"},{value:"packets",label:"Packets"}]})}),t.jsx("div",{style:{marginLeft:d!=="memory"?"8px":"80px"},children:t.jsxs(ht,{value:d,onChange:a,style:{width:280},children:[(s==null?void 0:s.interfaces)&&Object.keys(s.interfaces).map(y=>t.jsx(ht.Option,{value:y,children:Qi(y)},Ve())),(s==null?void 0:s.vlans)&&Object.keys(s.vlans).map(y=>t.jsxs(ht.Option,{value:`VLAN-${y}`,children:["VLAN - ",y]},Ve())),t.jsx(ht.Option,{value:"memory",children:n("statistics.memory")})]})}),t.jsx("div",{style:{marginLeft:"16px"},children:t.jsx(q,{htmlType:"button",style:{display:"flex",alignItems:"center"},onClick:c,icon:t.jsx(fe,{component:nt}),children:"Refresh"})})]})})})}),p&&!l.isLoading&&t.jsx(Rn,{style:{padding:"16px 0px 0px 0px"},children:t.jsx(Ti,{children:n("controller.devices.from_to",{from:`${p.realStart.toLocaleDateString()} ${p.realStart.toLocaleTimeString()}`,to:`${p.realEnd.toLocaleDateString()} ${p.realEnd.toLocaleTimeString()}`})})}),t.jsx("div",{style:{marginLeft:"-16px"},children:!s&&l.isLoading||l.isLoading&&l.progress!==void 0?t.jsxs(In,{justify:"center",align:"center",style:{padding:0},children:[l.progress!==void 0&&t.jsxs(ke.Title,{level:4,style:{marginRight:8,margin:0},children:[l.progress.toFixed(2),"%"]}),t.jsx(uo,{size:"large"})]}):t.jsx(ta,{isLoading:l.isLoading,children:t.jsxs("div",{style:{padding:0},children:[d==="memory"?b:C||m,L&&!l.isLoading&&t.jsx(Ei,{})]})})})]})},_i=({serialNumber:e})=>{var p;const{t:n}=H(),o=$t({serialNumber:e,disableToast:!0}),[i,s]=V.useState(""),[l,d]=V.useState(!1),a=na({serialNumber:e}),c=V.useCallback(f=>{s(f.target.value)},[]),u=V.useCallback(()=>{a.mutateAsync({serialNumber:e,notes:[{note:i,created:0}]},{onSuccess:()=>{Le.success(n("controller.devices.update_success")),d(!1),s("")}})},[i,e,n,a]),h=V.useMemo(()=>{var f,x;return((x=(f=o.data)==null?void 0:f.notes)==null?void 0:x.sort(({created:C},{created:m})=>m-C))??[]},[(p=o.data)==null?void 0:p.notes]),g=[{title:n("common.date"),dataIndex:"created",key:"created",width:"20%",sorter:(f,x)=>new Date(f.created).getTime()-new Date(x.created).getTime(),render:f=>t.jsx(Ze,{date:f})},{title:n("common.note"),dataIndex:"note",key:"note",width:"20%",sorter:(f,x)=>f.note.localeCompare(x.note),render:f=>t.jsx("div",{style:{whiteSpace:"pre-wrap"},children:f})},{title:n("common.by"),dataIndex:"createdBy",key:"createdBy",width:"25%"}];return t.jsxs("div",{children:[t.jsx("div",{style:{textAlign:"left"},children:t.jsxs(q,{type:"primary",onClick:()=>d(!0),style:{borderRadius:"2px",marginTop:8,marginBottom:22,marginLeft:-8},children:[t.jsx(fe,{component:ws}),"Create"]})}),t.jsx(Ls,{rowKey:f=>String(f.created),columns:g,dataSource:h,pagination:!1,style:{width:"100%",textAlign:"center",marginLeft:-8},bordered:!0}),t.jsxs(ks,{title:t.jsx("div",{style:{fontSize:20,fontWeight:700,color:"#212519",lineHeight:"24px"},children:n("common.create")}),open:l,onOk:u,onCancel:()=>{d(!1),s("")},okText:n("Apply"),cancelText:n("common.cancel"),okButtonProps:{disabled:i.trim().length===0,loading:a.isLoading,style:{backgroundColor:"#14C9BB",border:"#14C9BB",color:"#FFFFFF",fontWeight:400,fontSize:14,borderRadius:"2px",width:100}},cancelButtonProps:{style:{border:"1px solid #14C9BB",color:"#14C9BB",borderRadius:"2px",width:100,fontWeight:400,fontSize:14}},width:680,style:{height:450,borderRadius:"8px"},closable:!0,children:[t.jsx(Mn,{style:{margin:"0px 0px 16px -24px",width:"calc(100% + 48px)"}}),t.jsxs("div",{style:{marginBottom:24,display:"flex",alignItems:"flex-start"},children:[t.jsx("label",{style:{fontWeight:400,fontSize:14,color:"#212519",textAlign:"left",marginRight:18},children:n("common.note")}),t.jsx(co.TextArea,{value:i,onChange:c,style:{width:280,height:56,borderRadius:"2px",border:"1px solid #B2B2B2"},maxLength:500})]}),t.jsx(Mn,{style:{margin:"100px 0px 16px -24px",width:"calc(100% + 48px)"}})]})]})},Di=({command:e})=>{var i,s;const{t:n}=H(),o=xo({serialNumber:(e==null?void 0:e.serialNumber)??"",commandId:(e==null?void 0:e.UUID)??""});return!e||e.command!=="script"||((i=e.details)==null?void 0:i.uri)===void 0||((s=e.details)==null?void 0:s.uri)===""||e.status!=="completed"||e.errorCode!==0?null:t.jsx(hn,{color:"gray",icon:t.jsx(fn,{size:20}),isCompact:!0,label:n("common.download"),isLoading:o.isFetching,onClick:o.refetch,isDisabled:e.waitingForFile===1})},Oi=(e,n)=>Ne.get(`file/${n}?serialNumber=${e}`,{responseType:"arraybuffer"}),Pi=({serialNumber:e,commandId:n})=>{const{t:o}=H(),i=rn();return mt(["download-trace",e,n],()=>Oi(e,n),{enabled:!1,onSuccess:s=>{var u;const l=new Blob([s.data],{type:"application/octet-stream"}),d=document.createElement("a");d.href=window.URL.createObjectURL(l);const a=s.headers["content-disposition"]??s.headers["content-disposition"],c=((u=a==null?void 0:a.split("filename=")[1])==null?void 0:u.split(",")[0])??`Trace_${n}.pcap`;d.download=c,d.click()},onError:s=>{var l;if(xt.isAxiosError(s)){const d=(l=s.response)==null?void 0:l.data;let a="";if(d instanceof ArrayBuffer){const c=new TextDecoder("utf-8");a=JSON.parse(c.decode(d)).ErrorDescription}i({id:`trace-download-error-${e}`,title:o("common.error"),description:a,status:"error",duration:5e3,isClosable:!0,position:"top-right"})}}})},Ai=({command:e})=>{const{t:n}=H(),o=Pi({serialNumber:(e==null?void 0:e.serialNumber)??"",commandId:(e==null?void 0:e.UUID)??""});return!e||e.command!=="trace"||e.status!=="completed"||e.errorCode!==0?null:t.jsx(hn,{color:"gray",icon:t.jsx(fn,{size:20}),isCompact:!0,label:n("common.download"),isLoading:o.isFetching,onClick:o.refetch,isDisabled:e.waitingForFile===1})},Ni=({command:e})=>{const{t:n}=H(),o=r.useMemo(()=>{if(e)try{const i={},s=[];for(const l of e.results.status.scan)if(!i[l.channel]){const d={channel:l.channel,devices:[]};for(const a of e.results.status.scan)if(a.channel===l.channel){let c="";const u=an(a.signal);a.ssid&&a.ssid.length>0?c=a.ssid:c=a.meshid&&a.meshid.length>0?a.meshid:"N/A",d.devices.push({...a,ssid:c,signal:u}),s.push({...a,ssid:c,signal:u,ies:JSON.stringify(a.ies)})}i[l.channel]=d}return s}catch{return}},[e==null?void 0:e.results]);return!o||!e?null:t.jsx(xa,{filename:`wifi_scan_${e.serialNumber}_${Rs(new Date().getTime()/1e3)}.csv`,data:o,children:t.jsx(hn,{color:"gray",icon:t.jsx(fn,{size:20}),isCompact:!0,label:n("common.download"),onClick:()=>{}})})},{Panel:jl}=Ct,{Text:$i,Paragraph:Bi}=ke,zi=({modalProps:e,command:n})=>{const{t:o}=H(),{data:i,isLoading:s}=oa({commandId:(n==null?void 0:n.UUID)??"",serialNumber:(n==null?void 0:n.serialNumber)??""});if(s)return t.jsx(_t,{title:o("common.loading"),isModalOpen:e.isOpen,onCancel:e.onClose,childItems:t.jsx("div",{style:{textAlign:"center",padding:"100px 0"},children:t.jsx(uo,{size:"large"})}),footer:null});if(!i)return null;const l=(()=>{var d,a,c,u,h;return i.status==="failed"?t.jsx("div",{style:{margin:"40px 0",textAlign:"center"},children:t.jsx(un,{message:yt(i.status),description:i.errorText,type:"error",showIcon:!0})}):i.command==="wifiscan"?t.jsx(sa,{results:i,setCsvData:()=>{}}):i.command==="script"&&(((d=i.details)==null?void 0:d.uri)===void 0||((a=i.details)==null?void 0:a.uri)==="")&&i.status==="completed"?t.jsx(Bi,{children:t.jsx($i,{code:!0,children:((u=(c=i.results)==null?void 0:c.status)==null?void 0:u.result)??JSON.stringify(i.results,null,2)})}):t.jsx("div",{className:"collapse-modal",children:t.jsx(Ct,{expandIconPosition:"right",bordered:!1,defaultActiveKey:"1",style:{background:"#FFFFFF",marginTop:"14px"},children:t.jsx(Ct.Panel,{header:t.jsxs("h3",{style:{fontSize:"16px",margin:0,border:"none",fontWeight:600},children:["  ",o("common.preview")]}),children:t.jsx(aa,{rootName:!1,displayDataTypes:!1,enableClipboard:!1,theme:"light",defaultInspectDepth:1,value:(h=i.results)==null?void 0:h.status,style:{background:"unset",display:"unset"}})},"1")})})})();return t.jsx(_t,{title:`${yt(i.command)} - ${Ft(i.submitted)} `,childItems:l,isModalOpen:e.isOpen,onCancel:e.onClose,footer:t.jsxs(t.Fragment,{children:[t.jsx(Ni,{command:i}),t.jsx(Ai,{command:i}),t.jsx(Di,{command:i})]}),modalClass:"ampcon-middle-modal"})},Vi=({serialNumber:e,limit:n})=>{const{t:o}=H(),[i,s]=r.useState(),l=ra({serialNumber:e,start:i?Math.floor(i.start.getTime()/1e3):void 0,end:i?Math.floor(i.end.getTime()/1e3):void 0}),d=ia({serialNumber:e,limit:n}),a=la(),[c,u]=r.useState(),h=ln(),[g,p]=r.useState();rn();const f=r.useCallback(v=>()=>{u(v),h.onOpen()},[]),x=r.useCallback(v=>()=>{p(v.UUID),a.mutate(v.UUID,{onSuccess:()=>{p(void 0),Le.success(o("controller.crud.delete_success_obj",{obj:yt(v.command)}))},onError:j=>{var R,I;const k=j;p(void 0),Le.error(((I=(R=k==null?void 0:k.response)==null?void 0:R.data)==null?void 0:I.ErrorDescription)||o("common.error"))}})},[]),C=r.useCallback(v=>t.jsx(be,{children:t.jsx(Ze,{date:v})}),[]),m=(v,j)=>{const k=parseInt(v.slice(1,3),16),R=parseInt(v.slice(3,5),16),I=parseInt(v.slice(5,7),16);return`rgba(${k}, ${R}, ${I}, ${j})`},b=r.useCallback(v=>t.jsx(t.Fragment,{children:yt(v)}),[]),L=r.useCallback(v=>{let j="#F53F3F",k=v.status;k==="completed"&&v.errorCode===0?j="#2BC174":k==="completed"&&v.errorCode!==0&&(k="failed");const R=k.charAt(0).toUpperCase()+k.slice(1);return t.jsx(vt,{style:{border:`1px solid ${j}`,color:j,background:m(j,.1),fontSize:"14px"},children:R})},[]),w=r.useCallback(v=>t.jsxs("div",{style:{display:"flex",gap:24,alignItems:"center"},children:[t.jsx("a",{type:"link",style:{display:"flex",alignItems:"center",color:"#14C9BB"},onClick:x(v),onFocus:j=>j.target.blur(),children:"Delete"}),t.jsx("a",{type:"link",style:{display:"flex",alignItems:"center",color:"#14C9BB"},onClick:f(v),onFocus:j=>j.target.blur(),children:"View"})]}),[g]);return{columns:r.useMemo(()=>[{key:"submitted",title:o("common.submitted"),dataIndex:"submitted",render:(v,j)=>j?C(j.submitted):null,fixed:"left",sorter:!1,isMonospace:!0},{key:"command",title:o("controller.devices.command_one"),Footer:"",dataIndex:"command",render:(v,j)=>j?b(j.command):null,sorter:!1,isMonospace:!0,columnsFix:!0},{key:"status",title:o("common.status"),dataIndex:"status",render:(v,j)=>j?L(j):null,sorter:!1,isMonospace:!0},{key:"executed",title:o("controller.devices.executed"),dataIndex:"executed",render:(v,j)=>j?C(j.executed):null,sorter:!1,isMonospace:!0},{key:"completed",title:o("common.completed"),dataIndex:"completed",render:(v,j)=>j?C(j.completed):null,sorter:!1,isMonospace:!0},{key:"errorCode",title:t.jsx("span",{style:{whiteSpace:"nowrap"},children:o("controller.devices.error_code")}),dataIndex:"errorCode",sorter:!1,width:100,isMonospace:!0},{key:"actions",title:"Operation",dataIndex:"actions",render:(v,j)=>j?w(j):null}],[o,w]),getCommands:d,getCustomCommands:l,selectedCommand:c,detailsModalProps:h,time:i,setTime:s}},{RangePicker:Hi}=Nt,{Title:wl}=ke,Rt=r.forwardRef(({value:e,onChange:n,onCalendarChange:o,placeholder:i,onFocus:s,onOpenChange:l,open:d,style:a,format:c="YYYY-MM-DD HH:mm:ss",showTime:u={format:"HH:mm:ss"},t:h,...g},p)=>{const[f,x]=r.useState(e),[C,m]=r.useState(null);r.useEffect(()=>{x(e)},[e]);const b=y=>{x(y),n&&n(y)},L=y=>{const v=y.target.placeholder===h("common.start_time")?0:1;m(v),s&&s(y)},w=()=>{if(C===null)return;const y=Se(),v=f||[Se(),Se()],j=[v[0],v[1]];j[C]=y,x(j),n&&n(j)};return t.jsxs("div",{children:[t.jsx("span",{style:{marginRight:32},children:h("controller.crud.choose_time")}),t.jsx(Ae,{children:t.jsx(Hi,{ref:p,value:f,onChange:b,onCalendarChange:o,showTime:u,format:c,placeholder:i,style:a,onFocus:L,onOpenChange:l,open:d,renderExtraFooter:()=>t.jsx("div",{className:"ant-picker-footer",children:t.jsx(q,{type:"text",className:"ant-picker-now-btn",onClick:w,children:"Now"})}),...g})})]})}),{RangePicker:Ll}=Nt,{Title:kl}=ke,Zi=({serialNumber:e})=>{const{t:n}=H(),[o,i]=r.useState(25),[s,l]=r.useState([]),{setTime:d,getCustomCommands:a,getCommands:c,columns:u,selectedCommand:h,detailsModalProps:g}=Vi({serialNumber:e,limit:o}),[p,f]=r.useState(null),[x,C]=r.useState(null),[m,b]=r.useState(!1);r.useRef(null);const L=()=>{i(o+25)};console.log("columns",u);const w=I=>{I?f(I):(f(null),d(void 0))},y=I=>{b(I),!I&&p&&d({start:p[0].toDate(),end:p[1].toDate()})},v=a.data||c.data!==void 0&&c.data.commands.length<o,j=r.useMemo(()=>a.data?a.data.commands.sort((I,M)=>M.submitted-I.submitted):c.data?c.data.commands:[],[a.data,c.data]),k=()=>a.data?a.data.commands:c.data?c.data.commands:[];Ut,u.map(I=>t.jsx(Ut.Item,{children:t.jsx(Is,{checked:!s.includes(I.id),onChange:M=>{M.target.checked?l(s.filter(T=>T!==I.id)):l([...s,I.id])},children:I.Header})},I.id));const R=(I,M,T)=>{};return t.jsxs(t.Fragment,{children:[t.jsx(At,{children:t.jsx(Rt,{value:p,onChange:w,placeholder:[n("common.start_time"),n("common.end_time")],style:{width:380},onOpenChange:y,open:m,t:n})}),t.jsx("div",{style:{display:"flex",gap:10,marginTop:32,marginBottom:4},children:t.jsx(q,{icon:t.jsx(fe,{component:nt}),onClick:c.refetch,loading:c.isFetching,children:n("common.refresh")})}),t.jsx(ot,{columns:u,dataSource:k(),loading:c.isFetching||a.isFetching,onChange:R,showColumnSelector:"true",pagination:!1,disableInternalRowSelection:!0,scroll:{x:"max-content",y:k().length>6?300:void 0}}),j.length>0&&t.jsx("div",{style:{textAlign:"center",padding:"16px"},children:!v||c.isFetching?t.jsx(q,{onClick:L,loading:c.isFetching,type:"primary",children:n("controller.devices.show_more")}):t.jsx("span",{})}),t.jsx(zi,{command:h,modalProps:g})]})},Wi={isDisabled:!1,isLoading:!1,isCompact:!0,label:void 0,ml:void 0},Ao=({onClick:e,isDisabled:n,isLoading:o,isCompact:i,label:s,ml:l,...d})=>{const{t:a}=H(),u=!vn.useBreakpoint().md;return!i&&!u?t.jsx(q,{type:"primary",danger:!0,onClick:e,icon:t.jsx(fe,{component:tn}),loading:o,disabled:n,style:{marginLeft:l},...d,children:s??a("crud.delete")}):t.jsx(Ae,{title:s??a("crud.delete"),children:t.jsx(q,{onClick:e,icon:t.jsx(fe,{component:tn}),loading:o,disabled:n,style:{marginLeft:l},...d,children:s??a("crud.delete")})})};Ao.defaultProps=Wi;const Ot=V.memo(Ao),Ui=({serialNumber:e})=>{const{t:n}=H(),o=va(),[i,s]=r.useState(Se()),[l,d]=r.useState(!1),a=()=>{o.mutate({endDate:i.unix(),serialNumber:e},{onSuccess:()=>{d(!1),Le.success(n("controller.crud.delete_success_obj",{obj:n("controller.devices.healthchecks")}))},onError:g=>{var f,x;const p=g;Le.error(((x=(f=p==null?void 0:p.response)==null?void 0:f.data)==null?void 0:x.ErrorDescription)||n("common.error"))}})},c=g=>{s(g)},u=g=>g&&g>Se().endOf("day"),h=t.jsxs("div",{children:[t.jsx(un,{type:"warning",showIcon:!0,message:n("controller.devices.delete_health_explanation"),style:{borderRadius:8,marginBottom:16}}),t.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",gap:10},children:[t.jsx(Nt,{popupClassName:"picker-split-footer",value:i,onChange:c,format:"YYYY-MM-DD HH:mm:ss",disabledDate:u,showTime:{defaultValue:Se("00:00:00","HH:mm:ss")},disabled:o.isLoading}),t.jsx(Ot,{onClick:a,isLoading:o.isLoading})]})]});return t.jsxs(t.Fragment,{children:[t.jsx(Ot,{onClick:()=>d(!0),isCompact:!0}),t.jsx(_t,{title:`${n("crud.delete")} ${n("controller.devices.logs")}`,childItems:h,isModalOpen:l,onCancel:()=>d(!1),footer:null,modalClass:"ampcon-middle-modal"})]})},Gi=({serialNumber:e,limit:n})=>{const{t:o}=H(),i=ya({serialNumber:e,limit:n}),[s,l]=r.useState(),d=Ca({serialNumber:e,start:s?Math.floor(s.start.getTime()/1e3):void 0,end:s?Math.floor(s.end.getTime()/1e3):void 0}),a=r.useCallback(p=>t.jsx("div",{children:t.jsx(Ze,{date:p})}),[]),c=(p,f)=>{const x=parseInt(p.slice(1,3),16),C=parseInt(p.slice(3,5),16),m=parseInt(p.slice(5,7),16);return`rgba(${x}, ${C}, ${m}, ${f})`},u=r.useCallback(p=>{let f="#F53F3F";return p===100?f="#2BC174":p>=80&&(f="yellow"),t.jsx(vt,{style:{border:`1px solid ${f}`,color:f,background:c(f,.1)},children:p})},[]),h=r.useCallback(p=>t.jsx("div",{style:{whiteSpace:"pre-wrap",wordBreak:"break-word",width:"100%",maxWidth:"100%"},children:JSON.stringify(p,null,0)}),[]);return{columns:r.useMemo(()=>[{key:"submitted",title:o("common.submitted"),dataIndex:"submitted",render:(p,f)=>a(f.recorded),fixed:"left",sorter:!1},{key:"UUID",title:o("controller.devices.config_id"),dataIndex:"UUID",sorter:!1,columnsFix:!0},{key:"sanity",title:o("devices.sanity"),dataIndex:"sanity",render:(p,f)=>u(f.sanity),sorter:!1},{key:"values",title:o("common.details"),dataIndex:"values",render:(p,f)=>h(f.values),sorter:!1,width:500}],[o]),getHealthChecks:i,getCustomHealthChecks:d,time:s,setTime:l}},Yi=({serialNumber:e})=>{const{t:n}=H(),[o,i]=r.useState(25),[s,l]=r.useState([]),{setTime:d,getCustomHealthChecks:a,getHealthChecks:c,columns:u}=Gi({serialNumber:e,limit:o}),[h,g]=r.useState(null),[p,f]=r.useState(!1),x=w=>{w?g(w):(g(null),d(void 0))},C=w=>{f(w),!w&&h&&d({start:h[0].toDate(),end:h[1].toDate()})},m=()=>{i(o+25)},b=c.data!==void 0&&c.data.values.length<o;r.useMemo(()=>a.data?a.data.values.sort((w,y)=>y.recorded-w.recorded):c.data?c.data.values:[],[c.data,a.data]);const L=()=>a.data?a.data.values:c.data?c.data.values:[];return t.jsxs(t.Fragment,{children:[t.jsx(At,{children:t.jsx(Rt,{value:h,onChange:x,placeholder:[n("common.start_time"),n("common.end_time")],style:{width:380},onOpenChange:C,open:p,t:n})}),t.jsxs("div",{style:{display:"flex",gap:10,marginTop:32,marginBottom:4},children:[t.jsx(Ui,{serialNumber:e}),t.jsx(q,{icon:t.jsx(fe,{component:nt}),onClick:c.refetch,loading:c.isFetching||a.isFetching,children:n("common.refresh")})]}),t.jsx(ot,{columns:u,dataSource:L(),loading:c.isFetching||a.isFetching,showColumnSelector:!0,pagination:!1,disableInternalRowSelection:!0,style:{width:"100%"},scroll:{x:"max-content",y:L().length>6?300:void 0}}),c.data!==void 0&&t.jsx(Ue,{mt:2,hidden:a.data!==void 0,children:!b||c.isFetching?t.jsx(q,{onClick:m,loading:c.isFetching,type:"primary",children:n("controller.devices.show_more")}):t.jsx("span",{})})]})},Xi=(e,n,o)=>async()=>Ne.get(`device/${n}/logs?newest=true&limit=${e}&logType=${o}`).then(i=>i.data),qi=({serialNumber:e,limit:n,onError:o,logType:i})=>mt(["devicelogs",e,{limit:n,logType:i}],Xi(n,e,i??0),{keepPreviousData:!0,enabled:e!==void 0&&e!=="",staleTime:3e4,onError:o}),Ki=async({serialNumber:e,endDate:n,logType:o})=>Ne.delete(`device/${e}/logs?endDate=${n}&logType=${o}`),Ji=()=>{const e=ro();return no(Ki,{onSuccess:()=>{e.invalidateQueries(["devicelogs"])}})},el=(e,n,o,i,s,l)=>Ne.get(`device/${e}/logs?startDate=${n}&endDate=${o}&limit=${i}&offset=${s}&logType=${l}`).then(d=>d.data),tl=(e,n,o,i)=>async()=>{let s=0;const l=100;let d=[],a;do a=await el(e,n,o,l,s,i),d=d.concat(a.values),s+=l;while(a.values.length===l);return{values:d}},nl=({serialNumber:e,start:n,end:o,onError:i,logType:s})=>mt(["devicelogs",e,{start:n,end:o,logType:s}],tl(e,n,o,s??0),{enabled:e!==void 0&&e!==""&&n!==void 0&&o!==void 0,staleTime:1e3*60,onError:i}),Sn=({serialNumber:e,logType:n})=>{const{t:o}=H(),i=Ji(),[s,l]=r.useState(Se()),[d,a]=r.useState(!1),c=()=>{i.mutate({endDate:s.unix(),serialNumber:e,logType:n},{onSuccess:()=>{a(!1),Le.success(o("controller.crud.delete_success_obj",{obj:o("controller.devices.logs")}))},onError:p=>{var x,C;const f=p;Le.error(((C=(x=f==null?void 0:f.response)==null?void 0:x.data)==null?void 0:C.ErrorDescription)||o("common.error"))}})},u=p=>{l(p)},h=p=>p&&p>Se().endOf("day"),g=t.jsxs("div",{children:[t.jsx(un,{type:"warning",showIcon:!0,message:o("controller.devices.delete_logs_explanation"),style:{borderRadius:8,marginBottom:16}}),t.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",gap:10},children:[t.jsx(Nt,{popupClassName:"picker-split-footer",value:s,onChange:u,format:"YYYY-MM-DD HH:mm:ss",disabledDate:h,showTime:{defaultValue:Se("00:00:00","HH:mm:ss")},disabled:i.isLoading}),t.jsx(Ot,{onClick:c,isLoading:i.isLoading})]})]});return t.jsxs(t.Fragment,{children:[t.jsx(Ot,{onClick:()=>a(!0),isCompact:!0}),t.jsx(_t,{title:`${o("crud.delete")} ${o("controller.devices.logs")}`,childItems:g,isModalOpen:d,onCancel:()=>a(!1),footer:null,modalClass:"ampcon-middle-modal"})]})},ol=({modalProps:e,log:n})=>{const{t:o}=H(),{hasCopied:i,onCopy:s,setValue:l}=mn(JSON.stringify((n==null?void 0:n.log)??{},null,2));if(r.useEffect(()=>{(n==null?void 0:n.logType)===2?l(JSON.stringify(n.data??{},null,2)):l(JSON.stringify((n==null?void 0:n.log)??{},null,2))},[n]),!n)return null;const d=()=>n.logType===2?n.data.info!==void 0&&Array.isArray(n.data.info)?n.data.info.map(a=>a).join(`
`):JSON.stringify(n.data,null,2):n.log;return t.jsx(io,{isOpen:e.isOpen,onClose:e.onClose,title:o("devices.logs_one"),topRightButtons:t.jsx(He,{onClick:s,size:"md",colorScheme:"teal",children:i?`${o("common.copied")}!`:o("common.copy")}),children:t.jsxs(be,{children:[t.jsx(pt,{size:"sm",children:t.jsx(Ze,{date:n.recorded})}),t.jsxs(pt,{size:"sm",children:[o("controller.devices.severity"),": ",n.severity]}),t.jsxs(pt,{size:"sm",children:[o("controller.devices.config_id"),": ",n.UUID]}),t.jsx(Bt,{whiteSpace:"pre-line",mt:2,children:d()})]})})},jn=({serialNumber:e,limit:n,logType:o})=>{const{t:i}=H(),s=qi({serialNumber:e,limit:n,logType:o}),l=ln(),[d,a]=r.useState(),[c,u]=r.useState(),h=nl({serialNumber:e,start:c?Math.floor(c.start.getTime()/1e3):void 0,end:c?Math.floor(c.end.getTime()/1e3):void 0,logType:o}),g=r.useCallback(m=>{a(m),l.onOpen()},[]),p=r.useCallback(m=>o===1?t.jsxs("div",{style:{display:"flex"},children:[t.jsx(Tn,{"aria-label":"Open Log Details",onClick:()=>g(m),colorScheme:"blue",icon:t.jsx(Vn,{size:16}),size:"xs",mr:2}),t.jsx(We,{my:"auto",maxW:"calc(20vw)",textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",children:m.log})]}):m.log,[g]),f=r.useCallback(m=>o===2?t.jsxs(be,{display:"flex",children:[t.jsx(Tn,{"aria-label":"Open Log Details",onClick:()=>g(m),colorScheme:"blue",icon:t.jsx(Vn,{size:16}),size:"xs",mr:2}),t.jsx(We,{my:"auto",textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",children:JSON.stringify(m.data,null,0)})]}):t.jsx("pre",{children:JSON.stringify(m.data,null,0)}),[]),x=r.useCallback(m=>t.jsx(be,{children:t.jsx(Ze,{date:m})}),[]),C=r.useMemo(()=>[{key:"submitted",title:i("common.submitted"),dataIndex:"submitted",render:(m,b)=>x(b.recorded),fixed:"left",sorter:!1},{key:"UUID",title:i("controller.devices.config_id"),dataIndex:"UUID",sorter:!1,columnsFix:!0},{key:"severity",title:i("controller.devices.severity"),dataIndex:"severity",sorter:!1},{key:"log",title:"Log",dataIndex:"log",render:(m,b)=>p(b),sorter:!1},{key:"data",title:i("common.details"),dataIndex:"data",render:(m,b)=>f(b),sorter:!1}],[i]);return{columns:o===2?C.filter(m=>m.id!=="severity").map(m=>m.id==="log"?{...m,Header:"Type"}:m):C,getLogs:s,getCustomLogs:h,time:c,setTime:u,modal:t.jsx(ol,{modalProps:l,log:d})}},sl=({serialNumber:e})=>{const{t:n}=H(),[o,i]=r.useState(25),[s,l]=r.useState([]),{setTime:d,getCustomLogs:a,getLogs:c,columns:u}=jn({serialNumber:e,limit:o,logType:0}),[h,g]=r.useState(null),[p,f]=r.useState(!1),x=()=>{i(o+25)},C=c.data!==void 0&&c.data.values.length<o;r.useMemo(()=>a.data?a.data.values.sort((w,y)=>y.recorded-w.recorded):c.data?c.data.values:[],[c.data,a.data]);const m=()=>a.data?a.data.values.sort((w,y)=>y.recorded-w.recorded):c.data?c.data.values:[],b=w=>{w?g(w):(g(null),d(void 0))},L=w=>{f(w),!w&&h&&d({start:h[0].toDate(),end:h[1].toDate()})};return t.jsxs(t.Fragment,{children:[t.jsx(Rt,{value:h,onChange:b,placeholder:[n("common.start_time"),n("common.end_time")],style:{width:380},onOpenChange:L,open:p,t:n}),t.jsxs("div",{style:{display:"flex",gap:10,marginTop:32,marginBottom:4},children:[t.jsx(Sn,{serialNumber:e,logType:0}),t.jsx(q,{icon:t.jsx(fe,{component:nt}),onClick:c.refetch,loading:c.isFetching,children:n("common.refresh")})]}),t.jsx(ot,{columns:u,dataSource:m(),loading:c.isFetching||a.isFetching,showColumnSelector:!0,disableInternalRowSelection:!0,pagination:!1,scroll:{y:m().length>6?300:void 0}}),c.data!==void 0&&t.jsx(Ue,{mt:2,hidden:a.data!==void 0,children:!C||c.isFetching?t.jsx(q,{onClick:x,loading:c.isFetching,type:"primary",children:n("controller.devices.show_more")}):t.jsx("span",{})})]})},al=({serialNumber:e})=>{const{t:n}=H(),[o,i]=r.useState(25),[s,l]=r.useState([]),{setTime:d,getCustomLogs:a,getLogs:c,columns:u,modal:h}=jn({serialNumber:e,limit:o,logType:1}),[g,p]=r.useState(null),[f,x]=r.useState(!1),C=()=>{i(o+25)},m=c.data!==void 0&&c.data.values.length<o;r.useMemo(()=>a.data?a.data.values.sort((y,v)=>v.recorded-y.recorded):c.data?c.data.values:[],[c.data,a.data]);const b=()=>a.data?a.data.values.sort((y,v)=>v.recorded-y.recorded):c.data?c.data.values:[],L=y=>{y?p(y):(p(null),d(void 0))},w=y=>{x(y),!y&&g&&d({start:g[0].toDate(),end:g[1].toDate()})};return t.jsxs(t.Fragment,{children:[t.jsx(Rt,{value:g,onChange:L,placeholder:[n("common.start_time"),n("common.end_time")],style:{width:380},onOpenChange:w,open:f,t:n}),t.jsxs("div",{style:{display:"flex",gap:10,marginTop:32,marginBottom:4},children:[t.jsx(Sn,{serialNumber:e,logType:1}),t.jsx(q,{icon:t.jsx(fe,{component:nt}),onClick:c.refetch,loading:c.isFetching,children:n("common.refresh")})]}),t.jsx(ot,{columnsOrder:!0,columns:u,dataSource:b(),loading:c.isFetching||a.isFetching,showColumnSelector:!0,pagination:!1,disableInternalRowSelection:!0,scroll:{y:b().length>6?300:void 0}}),c.data!==void 0&&t.jsx(Ue,{mt:1,hidden:a.data!==void 0,children:!m||c.isFetching?t.jsx(q,{onClick:C,loading:c.isFetching,type:"primary",children:n("controller.devices.show_more")}):t.jsx("span",{})}),h]})},rl=({serialNumber:e})=>{const{t:n}=H(),[o,i]=r.useState(25),[s,l]=r.useState([]),{setTime:d,getCustomLogs:a,getLogs:c,columns:u,modal:h}=jn({serialNumber:e,limit:o,logType:2}),[g,p]=r.useState(null),[f,x]=r.useState(!1),C=()=>{i(o+25)},m=c.data!==void 0&&c.data.values.length<o;r.useMemo(()=>a.data?a.data.values.sort((y,v)=>v.recorded-y.recorded):c.data?c.data.values:[],[c.data,a.data]);const b=()=>a.data?a.data.values.sort((y,v)=>v.recorded-y.recorded):c.data?c.data.values:[],L=y=>{y?p(y):(p(null),d(void 0))},w=y=>{x(y),!y&&g&&d({start:g[0].toDate(),end:g[1].toDate()})};return t.jsxs(t.Fragment,{children:[t.jsx(Rt,{value:g,onChange:L,placeholder:[n("common.start_time"),n("common.end_time")],style:{width:380},onOpenChange:w,open:f,t:n}),t.jsxs("div",{style:{display:"flex",gap:10,marginTop:32,marginBottom:4},children:[t.jsx(Sn,{serialNumber:e,logType:2}),t.jsx(q,{icon:t.jsx(fe,{component:nt}),onClick:c.refetch,loading:c.isFetching,children:n("common.refresh")})]}),t.jsx(ot,{columnsOrder:!0,columns:u,dataSource:b(),loading:c.isFetching||a.isFetching,showColumnSelector:!0,disableInternalRowSelection:!0,pagination:!1,scroll:{y:b().length>6?300:void 0}}),c.data!==void 0&&t.jsx(Ue,{mt:1,hidden:a.data!==void 0,children:!m||c.isFetching?t.jsx(q,{onClick:C,loading:c.isFetching,type:"primary",children:n("controller.devices.show_more")}):t.jsx("span",{})}),h]})},{useBreakpoint:il}=vn,{TabPane:gt}=mo,ll=({serialNumber:e})=>{const{t:n}=H(),o=il(),[i,s]=r.useState("0"),l=r.useCallback(a=>{s(a)},[]),d=!o.lg;return t.jsx("div",{children:t.jsxs(mo,{activeKey:i,onChange:l,className:"custom-logs-tabs",children:[t.jsx(gt,{style:{width:"100%"},tab:t.jsx("span",{children:n("controller.devices.commands")}),children:t.jsx(Zi,{serialNumber:e})},"0"),t.jsx(gt,{style:{width:"100%"},tab:t.jsx("span",{children:n("controller.devices.healthchecks")}),children:t.jsx(Yi,{serialNumber:e})},"1"),t.jsx(gt,{style:{width:"100%"},tab:t.jsx("span",{children:n("controller.devices.logs")}),children:t.jsx(sl,{serialNumber:e})},"2"),t.jsx(gt,{style:{width:"100%"},tab:t.jsx("span",{children:d?"Crashes":n("devices.crash_logs")}),children:t.jsx(al,{serialNumber:e})},"3"),t.jsx(gt,{style:{width:"100%"},tab:t.jsx("span",{children:d?"Reboots":n("devices.reboot_logs")}),children:t.jsx(rl,{serialNumber:e})},"4")]})})},cl=({serialNumber:e})=>{var W;const{t:n}=H(),{message:o}=Ms.useApp(),i=lo(),{mutateAsync:s,isLoading:l}=ca({serialNumber:e}),[d,a]=r.useState(!1),c=$t({serialNumber:e}),u=vo({serialNumber:e}),h=da({serialNumber:e,limit:1}),[g,p]=r.useState(!1),[f,x]=r.useState(!1),C=r.useRef({}),m=r.useRef({}),b=r.useRef({}),L=r.useRef({}),w=r.useRef({}),y=di(),[v,j]=r.useState([]),k=oo(E=>E.addEventListeners),{mutateAsync:R,isLoading:I}=Ts({serialNumber:e}),{onSuccess:M,onError:T}=fo({objName:n("devices.one"),operationType:"reboot",refresh:()=>{k([{id:`device-connection-${e}`,type:"DEVICE_CONNECTION",serialNumber:e,callback:()=>{o.success({content:n("controller.devices.finished_reboot",{serialNumber:e}),duration:5,placement:"topRight"})}},{id:`device-disconnected-${e}`,type:"DEVICE_DISCONNECTION",serialNumber:e,callback:()=>{o.success({content:n("controller.devices.started_reboot",{serialNumber:e}),duration:5,placement:"topRight"})}}])}}),Q=()=>{En(`Are you sure you want to reboot this ${e}?`,async()=>{try{await R(void 0),M()}catch(E){T(E)}})};r.useEffect(()=>{if(c.data){const E=B.map(P=>P.key);j(E)}},[c.data]);const F=()=>{s(e,{onError:E=>{var P,G;xt.isAxiosError(E)&&o.error(((G=(P=E.response)==null?void 0:P.data)==null?void 0:G.ErrorDescription)||n("common.error"))}}),o.success(n("common.success")),i("/"),a(!1)},_=()=>{c.refetch(),u.refetch(),h.refetch()},$=()=>{En(`Are you sure you want to delete this ${e}?`,F)},B=[{key:"1",label:t.jsx("span",{className:"collapse-title",children:"Status"}),children:t.jsx(mi,{serialNumber:e})},{key:"2",label:t.jsx("div",{children:t.jsx("span",{className:"collapse-title",children:"Details"})}),children:t.jsx(gi,{serialNumber:e})},{key:"3",label:t.jsx("div",{children:t.jsx("span",{className:"collapse-title",children:"Statistics"})}),children:t.jsx(Fi,{serialNumber:e})},...((W=c.data)==null?void 0:W.deviceType)==="ap"?[{key:"4",label:t.jsx("div",{children:t.jsx("span",{className:"collapse-title",children:"WiFi Analysis"})}),children:t.jsx(Pr,{serialNumber:e})}]:[]],D=[{key:"1",label:t.jsx("span",{className:"collapse-title",children:"Notes"}),children:t.jsx(_i,{serialNumber:e})}],O=()=>i(-1);return t.jsxs(t.Fragment,{children:[t.jsx("div",{style:{marginTop:1,marginBottom:16},children:t.jsx(q,{type:"text",onClick:O,style:{display:"flex",alignItems:"center",fontFamily:"Lato, sans-serif",fontWeight:500,fontSize:14,color:"#929A9E",lineHeight:"16px",padding:0,height:"auto"},icon:t.jsx(Qs,{style:{marginRight:8,fontSize:14}}),children:"Back"})}),t.jsxs(Qn,{style:{width:"100%",minHeight:"auto",backgroundColor:"#ffffff",borderRadius:8,boxShadow:"none",padding:"32px 24px 24px 24px",marginBottom:32},children:[t.jsxs("div",{style:{display:"flex",gap:10},children:[c.data&&t.jsx(Vr,{device:c==null?void 0:c.data,refresh:_,onOpenScan:()=>{var E,P;return(P=(E=C.current).onOpen)==null?void 0:P.call(E)},onOpenFactoryReset:()=>x(!0),onOpenUpgradeModal:()=>{var E,P;return(P=(E=L.current).onOpen)==null?void 0:P.call(E)},onOpenTrace:()=>p(!0),onOpenEventQueue:()=>{var E,P;return(P=(E=m.current).onOpen)==null?void 0:P.call(E)},onOpenConfigureModal:()=>{var E,P;return(P=(E=b.current).onOpen)==null?void 0:P.call(E)},onOpenTelemetryModal:()=>{var E,P;return(P=(E=w.current).onOpen)==null?void 0:P.call(E)},onOpenRebootModal:()=>Q(),onOpenScriptModal:y.openModal}),t.jsx(q,{icon:t.jsx(fe,{component:tn}),onClick:$,children:"Delete"}),t.jsx(q,{icon:t.jsx(fe,{component:nt}),onClick:_,loading:c.isFetching||h.isFetching||u.isFetching,children:"Refresh"}),t.jsx("div",{style:{display:"flex",justifyContent:"flex-end",width:"100%"},children:t.jsx(ua,{})})]}),t.jsx(Ct,{size:"large",items:B,activeKey:v,onChange:E=>j(E),expandIconPosition:"right",className:"no-collapse-border",style:{marginTop:32,marginBottom:0,paddingBottom:0,border:"none",background:"#ffffff"}})]}),t.jsx(ll,{serialNumber:e}),t.jsx(Qn,{style:{width:"100%",minHeight:"auto",borderRadius:8,boxShadow:"none",backgroundColor:"#ffffff",padding:"32px 24px 24px 24px",marginTop:32},children:t.jsx(Ct,{size:"large",items:D,defaultActiveKey:D.map(E=>E.key),expandIconPosition:"right",className:"no-collapse-border",style:{marginTop:12,marginBottom:0,border:"none",background:"#ffffff"}})}),t.jsx(ma,{serialNumber:e,modalProps:{isOpen:g,onClose:()=>p(!1)}}),t.jsx(ha,{serialNumber:e,modalProps:{isOpen:f,onClose:()=>x(!1)}})]})},Rl=()=>{const{id:e}=Es();return e!==void 0?t.jsx(cl,{serialNumber:e.toLowerCase()}):null};export{Rl as default};
