import{r as l,j as o,B as k,X as T,ak as $,al as A,H as d,V as E,Q as N,M as V,D as W,bG as _,bH as B,aH as H,a4 as Q,ah as U,F as G,aC as X,aI as R}from"./index-CHCmiRmn.js";import{u as K,R as q,a as L,e as O,h as J,F as Y,W as Z,T as ee,V as te}from"./tabs-Crg4tuJR.js";import"./FormModal-BzU8SPYc.js";import"./index-LkKwRvEU.js";const ae=({siteId:h=0})=>{if(window.location.hash){const a=window.location.hash.replace("#","");/^\d+$/.test(a)&&(h=parseInt(a,10))}const[m,j]=l.useState([]),[z,s]=l.useState(!1),[w,u]=l.useState(!1),[f,x]=l.useState(null),[r,y]=l.useState({current:1,pageSize:10,total:0}),[I,P]=l.useState({}),S=(a=1,e=10,t=I)=>{s(!0);const n=t.field?[{field:t.field,order:t.order}]:[];L(1,h,a,e,[],n).then(i=>{if((i==null?void 0:i.status)!==200){d.error(i==null?void 0:i.info);return}j((i==null?void 0:i.info)||[]),y({current:a,pageSize:e,total:(i==null?void 0:i.total)||0})}).catch(()=>d.error("Failed to fetch list")).finally(()=>s(!1))};l.useEffect(()=>{S()},[h]);const D=a=>{N("Are you sure you want to delete?",()=>{J({id:a.key}).then(e=>{if((e==null?void 0:e.status)!==200){d.error(e==null?void 0:e.info);return}d.success("Deleted successfully"),S(r.current,r.pageSize)}).catch(()=>d.error("Delete failed"))})},b=a=>{O(a.key).then(e=>{if((e==null?void 0:e.status)!==200){d.error(e==null?void 0:e.info);return}let t=(e==null?void 0:e.info)||null;if(t&&typeof t.parameter=="string")try{t.parameter=JSON.parse(t.parameter)}catch{t.parameter={}}x(t),u(!0)}).catch(()=>d.error("Failed to fetch detail"))},F=()=>{x(null),u(!0)},C=()=>{u(!1),x(null)},M=(m||[]).map(a=>{let e={};if(typeof a.parameter=="string")try{e=JSON.parse(a.parameter)}catch{e={}}else typeof a.parameter=="object"&&a.parameter!==null&&(e=a.parameter);return{key:a.id,name:a.name,type:e.type||"",authHost:e.auth_server_host||"",port:e.port||"",modified_time:a.modified_time?K(a.modified_time):"",description:a.description||"",originResource:a}}),v=[{title:"Name",dataIndex:"name",key:"name",sorter:!0},{title:"Type",dataIndex:"type",key:"type",sorter:!0},{title:"Auth server host",dataIndex:"authHost",key:"authHost",sorter:!0},{title:"Port",dataIndex:"port",key:"port",sorter:!0},{title:"Modified",dataIndex:"modified_time",key:"modified_time",sorter:!0},{title:"Description",dataIndex:"description",key:"description",sorter:!0},{title:"Operation",key:"operation",render:(a,e)=>o.jsxs(E,{size:24,children:[o.jsx(k,{style:{padding:0},type:"link",onClick:()=>b(e),children:"Edit"}),o.jsx(k,{style:{padding:0},type:"link",onClick:()=>D(e),children:"Delete"})]})}];return o.jsxs("div",{children:[o.jsx("div",{style:{marginBottom:16},children:o.jsxs(k,{type:"primary",onClick:F,children:[o.jsx(T,{component:$}),"Create New SSID Radius Profile"]})}),o.jsx(A,{columns:v,dataSource:M,loading:z,pagination:{current:r.current,pageSize:r.pageSize,total:r.total,showTotal:(a,e)=>`${e[0]}-${e[1]} of ${a} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],onChange:(a,e)=>S(a,e)},rowKey:"key",bordered:!0,onChange:(a,e,t)=>{let n="",i="";if(!Array.isArray(t))if(t.order==="ascend"?n="asc":t.order==="descend"&&(n="desc"),typeof t.field=="string")switch(t.field){case"type":i="parameter.type";break;case"authHost":i="parameter.auth_server_host";break;case"port":i="parameter.port";break;default:i=t.field}else i="";P({field:i,order:n}),S(a.current,a.pageSize,{field:i,order:n})}}),o.jsx(q,{open:w,onClose:C,refresh:()=>S(r.current,r.pageSize),resource:f,isDisabled:!1,siteId:h},(f==null?void 0:f.id)||String(w))]})},ie=()=>{const[h,m]=l.useState([]),[j,z]=l.useState(!1),[s,w]=l.useState({current:1,pageSize:10,total:0}),[u,f]=l.useState({}),[x,r]=l.useState(null),[y,I]=l.useState(!1);let P=null;const S=window.location.hash.replace("#","");S&&/^\d+$/.test(S)&&(P=parseInt(S,10));const D=P||0,b=async(t=s.current,n=s.pageSize,i=u)=>{z(!0);try{const p=i.field?[{field:i.field,order:i.order}]:[],c=await L(2,D,t,n,[],p);c.status===200?(m((c.info||[]).map(g=>({...g,parameter:typeof g.parameter=="string"?JSON.parse(g.parameter):g.parameter||{},modified_time:g.modified_time?K(g.modified_time):""}))),w(g=>({...g,current:t,pageSize:n,total:(c==null?void 0:c.total)||0}))):d.error((c==null?void 0:c.info)||"Failed to fetch profile list")}catch{d.error("Failed to fetch profile list")}finally{z(!1)}},F=(t,n,i)=>{w({...s,current:t.current,pageSize:t.pageSize});let p="",c="";!Array.isArray(i)&&(i!=null&&i.order)&&(p=i.order==="ascend"?"asc":"desc",c=i.field||""),f({field:c,order:p}),b(t.current,t.pageSize,{field:c,order:p})};l.useEffect(()=>{D!=null&&b(s.current,s.pageSize,u)},[D,s.current,s.pageSize,u]);const C=()=>{r(null),I(!0)},M=t=>{r(t),I(!0)},v=t=>{N("Are you sure you want to delete?",async()=>{try{const n=await J({id:t.id});if((n==null?void 0:n.status)!==200){d.error((n==null?void 0:n.info)||"Delete failed");return}d.success("Successfully Deleted");const i=s.total-1,p=Math.ceil(i/s.pageSize),c=p===0?1:Math.min(s.current,p);w(g=>({...g,current:c,total:i})),b(c,s.pageSize)}catch{d.error("Delete failed")}})},a=t=>{I(!1),t&&w(n=>{const i=n.total+1;Math.ceil(i/n.pageSize);const c=n.total!==0&&n.total%n.pageSize===0?n.current+1:n.current;return b(c,n.pageSize,u),{...n,current:c,total:i}})},e=[{title:"Name",dataIndex:"name",key:"name",sorter:!0,width:"25%"},{title:"Modified",dataIndex:"modified_time",key:"modified",sorter:!0,width:"25%"},{title:"Description",dataIndex:"description",key:"description",sorter:!0,width:"30%"},{title:"Operation",key:"operate",width:"20%",render:(t,n)=>o.jsxs(E,{size:16,children:[o.jsx(k,{type:"link",onClick:()=>M(n),style:{marginLeft:-10,marginRight:-20},children:"Edit"}),o.jsx(k,{type:"link",onClick:()=>v(n),children:"Delete"})]})}];return o.jsxs("div",{style:{flex:1},children:[o.jsx("div",{style:{marginBottom:16},children:o.jsxs(k,{type:"primary",onClick:C,children:[o.jsx(T,{component:$}),"Create New MPSK Profile"]})}),o.jsx(A,{columns:e,dataSource:h,rowKey:"id",loading:j,onChange:F,pagination:{current:s.current,pageSize:s.pageSize,total:s.total,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],showTotal:t=>`Total ${t} items`},scroll:{x:1e3},bordered:!0}),o.jsx(V,{title:x?"Edit MPSK Profile":"Create MPSK Profile",visible:y,onCancel:()=>a(!1),footer:null,width:1360,destroyOnClose:!0,children:o.jsxs("div",{onKeyDown:t=>{t.key==="Enter"&&t.preventDefault()},children:[o.jsx(W,{style:{margin:"0px 0px 16px -24px",width:"calc(100% + 48px)"}}),o.jsx(Y,{editingProfile:x,onClose:a,siteId:D})]})})]})},ne=({siteId:h=0})=>{if(window.location.hash){const a=window.location.hash.replace("#","");/^\d+$/.test(a)&&(h=parseInt(a,10))}const[m,j]=l.useState([]),[z,s]=l.useState(!1),[w,u]=l.useState(!1),[f,x]=l.useState(null),[r,y]=l.useState({current:1,pageSize:10,total:0}),[I,P]=l.useState({}),S=(a=1,e=10,t=I)=>{s(!0);const n=t.field?[{field:t.field,order:t.order}]:[];L(3,h,a,e,[],n).then(i=>{if((i==null?void 0:i.status)!==200){d.error(i==null?void 0:i.info);return}j((i==null?void 0:i.info)||[]),y({current:a,pageSize:e,total:(i==null?void 0:i.total)||0})}).catch(()=>d.error("Failed to fetch list")).finally(()=>s(!1))};l.useEffect(()=>{S()},[h]);const D=a=>{N("Are you sure you want to delete?",()=>{J({id:a.key}).then(e=>{if((e==null?void 0:e.status)!==200){d.error(e==null?void 0:e.info);return}d.success("Deleted successfully"),S(r.current,r.pageSize)}).catch(()=>d.error("Delete failed"))})},b=a=>{O(a.key).then(e=>{if((e==null?void 0:e.status)!==200){d.error(e==null?void 0:e.info);return}let t=(e==null?void 0:e.info)||null;if(t&&typeof t.parameter=="string")try{t.parameter=JSON.parse(t.parameter)}catch{t.parameter={}}x(t),u(!0)}).catch(()=>d.error("Failed to fetch detail"))},F=()=>{x(null),u(!0)},C=()=>{u(!1),x(null)},M=(m||[]).map(a=>{let e={};if(typeof a.parameter=="string")try{e=JSON.parse(a.parameter)}catch{e={}}else typeof a.parameter=="object"&&a.parameter!==null&&(e=a.parameter);return{key:a.id,name:a.name,mode:e.mode,modified_time:a.modified_time?K(a.modified_time):"",description:a.description||"",originResource:a}}),v=[{title:"Name",dataIndex:"name",key:"name",sorter:!0},{title:"Mode",dataIndex:"mode",key:"mode",sorter:!0},{title:"Modified",dataIndex:"modified_time",key:"modified_time",sorter:!0},{title:"Description",dataIndex:"description",key:"description",sorter:!0},{title:"Operation",key:"operation",render:(a,e)=>o.jsxs(E,{size:24,children:[o.jsx(k,{style:{padding:0},type:"link",onClick:()=>b(e),children:"Edit"}),o.jsx(k,{style:{padding:0},type:"link",onClick:()=>D(e),children:"Delete"})]})}];return o.jsxs("div",{children:[o.jsx("div",{style:{marginBottom:16},children:o.jsxs(k,{type:"primary",onClick:F,children:[o.jsx(T,{component:$}),"Create New Portal Webroot Profile"]})}),o.jsx(A,{columns:v,dataSource:M,loading:z,pagination:{current:r.current,pageSize:r.pageSize,total:r.total,showTotal:(a,e)=>`${e[0]}-${e[1]} of ${a} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],onChange:(a,e)=>S(a,e)},rowKey:"key",bordered:!0,onChange:(a,e,t)=>{let n="",i="";if(!Array.isArray(t))if(t.order==="ascend"?n="asc":t.order==="descend"&&(n="desc"),typeof t.field=="string")switch(t.field){case"mode":i="parameter.mode";break;default:i=t.field}else i="";P({field:i,order:n}),S(a.current,a.pageSize,{field:i,order:n})}}),o.jsx(Z,{open:w,onClose:C,refresh:()=>S(r.current,r.pageSize),resource:f,isDisabled:!1,siteId:h},(f==null?void 0:f.id)||String(w))]})},oe=({siteId:h})=>{const[m,j]=l.useState([]),[z,s]=l.useState(!1),[w,u]=l.useState(!1),[f,x]=l.useState(null),[r,y]=l.useState({current:1,pageSize:10,total:0}),[I,P]=l.useState(""),[S,D]=l.useState({}),b=4;if(window.location.hash){const e=window.location.hash.replace("#","");/^\d+$/.test(e)&&(h=parseInt(e,10))}const F=e=>{try{const t=typeof e=="string"?JSON.parse(e):e,n=t==null?void 0:t.time_range;return Array.isArray(n)?n.join(`
`):"No time range"}catch{return"Invalid time range"}},C=async(e=1,t=10,n=S)=>{s(!0);try{const i=n.field?[{field:n.field,order:n.order}]:[],p=await L(b,h,e,t,[],i);if(p.status===200&&Array.isArray(p.info)){let c=p.info.map(g=>({...g,key:g.id,timeRange:F(g.parameter),modified_time:g.modified_time?K(g.modified_time):""}));j(c),y({current:e,pageSize:t,total:p.total||0})}else d.error("Failed to fetch profile list")}catch{d.error("Failed to fetch profile list")}finally{s(!1)}};l.useEffect(()=>{C()},[h]);const M=e=>{N("Are you sure you want to delete?",()=>{J({id:e.key}).then(t=>{if((t==null?void 0:t.status)!==200){d.error(t==null?void 0:t.info);return}d.success("Successfully Deleted"),C(r.current,r.pageSize)}).catch(()=>d.error("Delete Failed"))})},v=(e,t,n)=>{y({...r,current:e.current,pageSize:e.pageSize});let i="",p="";!Array.isArray(n)&&(n!=null&&n.order)&&(i=n.order==="ascend"?"asc":"desc",p=n.field||""),D({field:p,order:i}),C(e.current,e.pageSize,{field:p,order:i})},a=[_({title:"Name",dataIndex:"name",enableSorter:!0,enableFilter:!1,filterDropdownComponent:B,defaultValue:"",width:"15%"}),_({title:"Time Range",dataIndex:"timeRange",enableSorter:!1,enableFilter:!1,render:e=>o.jsx("div",{style:{padding:"13px 0"},children:e.split(`
`).filter(Boolean).map(t=>{const[n,i]=t.split(" ");return o.jsxs("div",{children:[o.jsx("span",{style:{display:"inline-block",width:"100px",textAlign:"left"},children:n}),o.jsx("span",{children:i})]})})}),width:"15%"}),_({title:"Modify",dataIndex:"modified_time",enableSorter:!0,enableFilter:!1,width:"15%"}),_({title:"Description",dataIndex:"description",enableSorter:!0,enableFilter:!1,width:"30%",render:e=>o.jsx("div",{style:{padding:"13px 0",whiteSpace:"normal",wordBreak:"break-word"},children:e})}),{title:"Operation",width:"15%",render:(e,t)=>o.jsxs(E,{size:24,children:[o.jsx(k,{style:{padding:0},type:"link",onClick:()=>{x(t),u(!0)},children:"Edit"}),o.jsx(k,{type:"link",style:{padding:0},onClick:()=>M(t),children:"Delete"})]})}];return o.jsxs("div",{children:[o.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:16},children:o.jsxs(k,{type:"primary",onClick:()=>{x(null),u(!0)},children:[o.jsx(T,{component:$}),"Create New Time Range Profile"]})}),o.jsx(A,{columns:a,dataSource:m,loading:z,onChange:v,pagination:{current:r.current,pageSize:r.pageSize,total:r.total,showTotal:(e,t)=>`${t[0]}-${t[1]} of ${e} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"]},scroll:{x:1e3},rowKey:"key",bordered:!0}),o.jsx(ee,{visible:w,resource:f,siteId:h,onClose:()=>u(!1),onSuccess:()=>{u(!1),C()}})]})},ce=()=>{const h=H(r=>r.user.userInfo),m=Q(),j=U(),[z,s]=l.useState(""),w=r=>{const y=m.pathname;j(`${y}#${r}`)},u=[{key:"SSIDRadius",label:"SSID Radius",children:o.jsx(R,{component:ae})},{key:"MPSKUser",label:"MPSK",children:o.jsx(R,{component:ie})},{key:"CaptivePortal",label:"Portal",children:o.jsx(R,{component:ne})},{key:"TimeRange",label:"Time Range",children:o.jsx(R,{component:oe})}],f=h.type==="readonly"?[]:u;l.useEffect(()=>{const r=m.pathname.match(/(SSIDRadius|MPSKUser|CaptivePortal|TimeRange)$/);if(r)s(r[0]);else if(f.length>0){s(f[0].key);let y=`${m.pathname.replace(/\/$/,"")}/${f[0].key}`;m.hash&&(y+=m.hash),j(y)}},[m.pathname,f]);const x=r=>{let y=m.pathname.replace(/(SSIDRadius|MPSKUser|CaptivePortal|TimeRange)$/,"");y=`${y.replace(/\/$/,"")}/${r}`,m.hash&&(y+=m.hash),j(y)};return o.jsxs(G,{className:"div-main",children:[o.jsx("div",{className:"div-header",children:o.jsx(te,{onChange:w})}),o.jsx("div",{className:"div-tabs",children:o.jsx(X,{activeKey:z,onChange:x,destroyInactiveTabPane:!0,items:f})})]})};export{ce as default};
