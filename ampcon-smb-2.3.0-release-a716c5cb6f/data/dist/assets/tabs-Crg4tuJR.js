import{bI as Kt,bJ as Ut,bK as pt,bL as It,bM as At,bN as bt,bO as ee,bP as Jt,R as A,bQ as K,r as c,bR as he,bS as Ge,bT as xt,_ as He,P as ge,W as M,bU as je,a2 as se,bV as qt,bW as vt,ad as er,D as de,bX as Y,bY as tr,bZ as rr,b_ as ar,b$ as nr,c0 as or,c1 as lr,c2 as sr,c3 as ir,c4 as cr,c5 as gr,c6 as yt,c7 as dr,bz as ur,ah as mr,j as a,aD as be,X as ie,ai as tt,B as X,aE as Cr,ac as fe,O as E,a5 as ce,M as Fe,b as pe,g as F,h as B,an as hr,c8 as fr,H as D,ak as wt,al as jt,ab as xe,c9 as pr,T as Ir,ca as Be,V as Ar,cb as br}from"./index-CHCmiRmn.js";import{u as xr,F as Ze}from"./FormModal-BzU8SPYc.js";import{d as $e,F as vr}from"./index-LkKwRvEU.js";var yr=["b"],wr=["v"],Te=function(t){return Math.round(Number(t||0))},jr=function(t){if(t&&At(t)==="object"&&"h"in t&&"b"in t){var n=t,r=n.b,o=bt(n,yr);return ee(ee({},o),{},{v:r})}return typeof t=="string"&&/hsb/.test(t)?t.replace(/hsb/,"hsv"):t},Se=function(e){Kt(n,e);var t=Ut(n);function n(r){return pt(this,n),t.call(this,jr(r))}return It(n,[{key:"toHsbString",value:function(){var o=this.toHsb(),l=Te(o.s*100),i=Te(o.b*100),s=Te(o.h),d=o.a,m="hsb(".concat(s,", ").concat(l,"%, ").concat(i,"%)"),f="hsba(".concat(s,", ").concat(l,"%, ").concat(i,"%, ").concat(d.toFixed(d===0?0:2),")");return d===1?m:f}},{key:"toHsb",value:function(){var o=this.toHsv();At(this.originalInput)==="object"&&this.originalInput&&"h"in this.originalInput&&(o=this.originalInput);var l=o;l.v;var i=bt(l,wr);return ee(ee({},i),{},{b:o.v,a:this.a})}}]),n}(Jt),Sr="rc-color-picker",ne=function(t){return t instanceof Se?t:new Se(t)},Er=ne("#1677ff"),St=function(t){var n=t.offset,r=t.targetRef,o=t.containerRef,l=t.color,i=t.type,s=o.current.getBoundingClientRect(),d=s.width,m=s.height,f=r.current.getBoundingClientRect(),C=f.width,I=f.height,u=C/2,p=I/2,h=(n.x+u)/d,j=1-(n.y+p)/m,S=l.toHsb(),P=h,R=(n.x+u)/d*360;if(i)switch(i){case"hue":return ne(ee(ee({},S),{},{h:R<=0?0:R}));case"alpha":return ne(ee(ee({},S),{},{a:P<=0?0:P}))}return ne({h:S.h,s:h<=0?0:h,b:j>=1?1:j,a:S.a})},Et=function(t,n,r,o){var l=t.current.getBoundingClientRect(),i=l.width,s=l.height,d=n.current.getBoundingClientRect(),m=d.width,f=d.height,C=m/2,I=f/2,u=r.toHsb();if(!(m===0&&f===0||m!==f)){if(o)switch(o){case"hue":return{x:u.h/360*i-C,y:-I/3};case"alpha":return{x:u.a/1*i-C,y:-I/3}}return{x:u.s*i-C,y:(1-u.b)*s-I}}},Qe=function(t){var n=t.color,r=t.prefixCls,o=t.className,l=t.style,i=t.onClick,s="".concat(r,"-color-block");return A.createElement("div",{className:K(s,o),style:l,onClick:i},A.createElement("div",{className:"".concat(s,"-inner"),style:{background:n}}))};function Pr(e){var t="touches"in e?e.touches[0]:e,n=document.documentElement.scrollLeft||document.body.scrollLeft||window.pageXOffset,r=document.documentElement.scrollTop||document.body.scrollTop||window.pageYOffset;return{pageX:t.pageX-n,pageY:t.pageY-r}}function Pt(e){var t=e.offset,n=e.targetRef,r=e.containerRef,o=e.direction,l=e.onDragChange,i=e.onDragChangeComplete,s=e.calculate,d=e.color,m=e.disabledDrag,f=c.useState(t||{x:0,y:0}),C=he(f,2),I=C[0],u=C[1],p=c.useRef(null),h=c.useRef(null),j=c.useRef({flag:!1});c.useEffect(function(){if(j.current.flag===!1){var L=s==null?void 0:s(r);L&&u(L)}},[d,r]),c.useEffect(function(){return function(){document.removeEventListener("mousemove",p.current),document.removeEventListener("mouseup",h.current),document.removeEventListener("touchmove",p.current),document.removeEventListener("touchend",h.current),p.current=null,h.current=null}},[]);var S=function(k){var _=Pr(k),G=_.pageX,w=_.pageY,T=r.current.getBoundingClientRect(),Z=T.x,J=T.y,U=T.width,x=T.height,v=n.current.getBoundingClientRect(),N=v.width,$=v.height,H=N/2,g=$/2,b=Math.max(0,Math.min(G-Z,U))-H,O=Math.max(0,Math.min(w-J,x))-g,W={x:b,y:o==="x"?I.y:O};if(N===0&&$===0||N!==$)return!1;u(W),l==null||l(W)},P=function(k){k.preventDefault(),S(k)},R=function(k){k.preventDefault(),j.current.flag=!1,document.removeEventListener("mousemove",p.current),document.removeEventListener("mouseup",h.current),document.removeEventListener("touchmove",p.current),document.removeEventListener("touchend",h.current),p.current=null,h.current=null,i==null||i()},y=function(k){document.removeEventListener("mousemove",p.current),document.removeEventListener("mouseup",h.current),!m&&(S(k),j.current.flag=!0,document.addEventListener("mousemove",P),document.addEventListener("mouseup",R),document.addEventListener("touchmove",P),document.addEventListener("touchend",R),p.current=P,h.current=R)};return[I,y]}var Lt=function(t){var n=t.size,r=n===void 0?"default":n,o=t.color,l=t.prefixCls;return A.createElement("div",{className:K("".concat(l,"-handler"),Ge({},"".concat(l,"-handler-sm"),r==="small")),style:{backgroundColor:o}})},Rt=function(t){var n=t.children,r=t.style,o=t.prefixCls;return A.createElement("div",{className:"".concat(o,"-palette"),style:ee({position:"relative"},r)},n)},kt=c.forwardRef(function(e,t){var n=e.children,r=e.offset;return A.createElement("div",{ref:t,style:{position:"absolute",left:r.x,top:r.y,zIndex:1}},n)}),Lr=function(t){var n=t.color,r=t.onChange,o=t.prefixCls,l=t.onChangeComplete,i=t.disabled,s=c.useRef(),d=c.useRef(),m=c.useRef(n),f=xt(function(h){var j=St({offset:h,targetRef:d,containerRef:s,color:n});m.current=j,r(j)}),C=Pt({color:n,containerRef:s,targetRef:d,calculate:function(j){return Et(j,d,n)},onDragChange:f,onDragChangeComplete:function(){return l==null?void 0:l(m.current)},disabledDrag:i}),I=he(C,2),u=I[0],p=I[1];return A.createElement("div",{ref:s,className:"".concat(o,"-select"),onMouseDown:p,onTouchStart:p},A.createElement(Rt,{prefixCls:o},A.createElement(kt,{offset:u,ref:d},A.createElement(Lt,{color:n.toRgbString(),prefixCls:o})),A.createElement("div",{className:"".concat(o,"-saturation"),style:{backgroundColor:"hsl(".concat(n.toHsb().h,",100%, 50%)"),backgroundImage:"linear-gradient(0deg, #000, transparent),linear-gradient(90deg, #fff, hsla(0, 0%, 100%, 0))"}})))},Rr=function(t){var n=t.colors,r=t.children,o=t.direction,l=o===void 0?"to right":o,i=t.type,s=t.prefixCls,d=c.useMemo(function(){return n.map(function(m,f){var C=ne(m);return i==="alpha"&&f===n.length-1&&C.setAlpha(1),C.toRgbString()}).join(",")},[n,i]);return A.createElement("div",{className:"".concat(s,"-gradient"),style:{position:"absolute",inset:0,background:"linear-gradient(".concat(l,", ").concat(d,")")}},r)},rt=function(t){var n=t.gradientColors,r=t.direction,o=t.type,l=o===void 0?"hue":o,i=t.color,s=t.value,d=t.onChange,m=t.onChangeComplete,f=t.disabled,C=t.prefixCls,I=c.useRef(),u=c.useRef(),p=c.useRef(i),h=xt(function(y){var L=St({offset:y,targetRef:u,containerRef:I,color:i,type:l});p.current=L,d(L)}),j=Pt({color:i,targetRef:u,containerRef:I,calculate:function(L){return Et(L,u,i,l)},onDragChange:h,onDragChangeComplete:function(){m==null||m(p.current,l)},direction:"x",disabledDrag:f}),S=he(j,2),P=S[0],R=S[1];return A.createElement("div",{ref:I,className:K("".concat(C,"-slider"),"".concat(C,"-slider-").concat(l)),onMouseDown:R,onTouchStart:R},A.createElement(Rt,{prefixCls:C},A.createElement(kt,{offset:P,ref:u},A.createElement(Lt,{size:"small",color:s,prefixCls:C})),A.createElement(Rr,{colors:n,direction:r,type:l,prefixCls:C})))};function at(e){return e!==void 0}var kr=function(t,n){var r=n.defaultValue,o=n.value,l=c.useState(function(){var m;return at(o)?m=o:at(r)?m=r:m=t,ne(m)}),i=he(l,2),s=i[0],d=i[1];return c.useEffect(function(){o&&d(ne(o))},[o]),[s,d]},Nr=["rgb(255, 0, 0) 0%","rgb(255, 255, 0) 17%","rgb(0, 255, 0) 33%","rgb(0, 255, 255) 50%","rgb(0, 0, 255) 67%","rgb(255, 0, 255) 83%","rgb(255, 0, 0) 100%"];const Or=c.forwardRef(function(e,t){var n=e.value,r=e.defaultValue,o=e.prefixCls,l=o===void 0?Sr:o,i=e.onChange,s=e.onChangeComplete,d=e.className,m=e.style,f=e.panelRender,C=e.disabledAlpha,I=C===void 0?!1:C,u=e.disabled,p=u===void 0?!1:u,h=kr(Er,{value:n,defaultValue:r}),j=he(h,2),S=j[0],P=j[1],R=c.useMemo(function(){var G=ne(S.toRgbString());return G.setAlpha(1),G.toRgbString()},[S]),y=K("".concat(l,"-panel"),d,Ge({},"".concat(l,"-panel-disabled"),p)),L={prefixCls:l,onChangeComplete:s,disabled:p},k=function(w,T){n||P(w),i==null||i(w,T)},_=A.createElement(A.Fragment,null,A.createElement(Lr,He({color:S,onChange:k},L)),A.createElement("div",{className:"".concat(l,"-slider-container")},A.createElement("div",{className:K("".concat(l,"-slider-group"),Ge({},"".concat(l,"-slider-group-disabled-alpha"),I))},A.createElement(rt,He({gradientColors:Nr,color:S,value:"hsl(".concat(S.toHsb().h,",100%, 50%)"),onChange:function(w){return k(w,"hue")}},L)),!I&&A.createElement(rt,He({type:"alpha",gradientColors:["rgba(255, 0, 4, 0) 0%",R],color:S,value:S.toRgbString(),onChange:function(w){return k(w,"alpha")}},L))),A.createElement(Qe,{color:S.toRgbString(),prefixCls:l})));return A.createElement("div",{className:y,style:m,ref:t},typeof f=="function"?f(_):_)}),Nt=A.createContext({}),Ot=A.createContext({}),{Provider:Hr}=Nt,{Provider:Br}=Ot,ye=(e,t)=>(e==null?void 0:e.replace(/[^\w/]/gi,"").slice(0,t?8:6))||"",$r=(e,t)=>e?ye(e,t):"";let nt=function(){function e(t){pt(this,e),this.metaColor=new Se(t),t||this.metaColor.setAlpha(0)}return It(e,[{key:"toHsb",value:function(){return this.metaColor.toHsb()}},{key:"toHsbString",value:function(){return this.metaColor.toHsbString()}},{key:"toHex",value:function(){return $r(this.toHexString(),this.metaColor.getAlpha()<1)}},{key:"toHexString",value:function(){return this.metaColor.getAlpha()===1?this.metaColor.toHexString():this.metaColor.toHex8String()}},{key:"toRgb",value:function(){return this.metaColor.toRgb()}},{key:"toRgbString",value:function(){return this.metaColor.toRgbString()}}]),e}();const Q=e=>e instanceof nt?e:new nt(e),we=e=>Math.round(Number(e||0)),Ee=e=>we(e.toHsb().a*100),We=(e,t)=>{const n=e.toHsb();return n.a=1,Q(n)},Ht=e=>{let{prefixCls:t,value:n,colorCleared:r,onChange:o}=e;const l=()=>{if(n&&!r){const i=n.toHsb();i.a=0;const s=Q(i);o==null||o(s)}};return A.createElement("div",{className:`${t}-clear`,onClick:l})};var te;(function(e){e.hex="hex",e.rgb="rgb",e.hsb="hsb"})(te||(te={}));const le=e=>{let{prefixCls:t,min:n=0,max:r=100,value:o,onChange:l,className:i,formatter:s}=e;const d=`${t}-steppers`,[m,f]=c.useState(o);return c.useEffect(()=>{Number.isNaN(o)||f(o)},[o]),A.createElement(ge,{className:K(d,i),min:n,max:r,value:m,formatter:s,size:"small",onChange:C=>{o||f(C||0),l==null||l(C)}})},Tr=e=>{let{prefixCls:t,value:n,onChange:r}=e;const o=`${t}-alpha-input`,[l,i]=c.useState(Q(n||"#000"));c.useEffect(()=>{n&&i(n)},[n]);const s=d=>{const m=l.toHsb();m.a=(d||0)/100;const f=Q(m);n||i(f),r==null||r(f)};return A.createElement(le,{value:Ee(l),prefixCls:t,formatter:d=>`${d}%`,className:o,onChange:s})},Wr=/(^#[\da-f]{6}$)|(^#[\da-f]{8}$)/i,ot=e=>Wr.test(`#${e}`),Dr=e=>{let{prefixCls:t,value:n,onChange:r}=e;const o=`${t}-hex-input`,[l,i]=c.useState(n==null?void 0:n.toHex());c.useEffect(()=>{const d=n==null?void 0:n.toHex();ot(d)&&n&&i(ye(d))},[n]);const s=d=>{const m=d.target.value;i(ye(m)),ot(ye(m,!0))&&(r==null||r(Q(m)))};return A.createElement(M,{className:o,value:l,prefix:"#",onChange:s,size:"small"})},Mr=e=>{let{prefixCls:t,value:n,onChange:r}=e;const o=`${t}-hsb-input`,[l,i]=c.useState(Q(n||"#000"));c.useEffect(()=>{n&&i(n)},[n]);const s=(d,m)=>{const f=l.toHsb();f[m]=m==="h"?d:(d||0)/100;const C=Q(f);n||i(C),r==null||r(C)};return A.createElement("div",{className:o},A.createElement(le,{max:360,min:0,value:Number(l.toHsb().h),prefixCls:t,className:o,formatter:d=>we(d||0).toString(),onChange:d=>s(Number(d),"h")}),A.createElement(le,{max:100,min:0,value:Number(l.toHsb().s)*100,prefixCls:t,className:o,formatter:d=>`${we(d||0)}%`,onChange:d=>s(Number(d),"s")}),A.createElement(le,{max:100,min:0,value:Number(l.toHsb().b)*100,prefixCls:t,className:o,formatter:d=>`${we(d||0)}%`,onChange:d=>s(Number(d),"b")}))},Gr=e=>{let{prefixCls:t,value:n,onChange:r}=e;const o=`${t}-rgb-input`,[l,i]=c.useState(Q(n||"#000"));c.useEffect(()=>{n&&i(n)},[n]);const s=(d,m)=>{const f=l.toRgb();f[m]=d||0;const C=Q(f);n||i(C),r==null||r(C)};return A.createElement("div",{className:o},A.createElement(le,{max:255,min:0,value:Number(l.toRgb().r),prefixCls:t,className:o,onChange:d=>s(Number(d),"r")}),A.createElement(le,{max:255,min:0,value:Number(l.toRgb().g),prefixCls:t,className:o,onChange:d=>s(Number(d),"g")}),A.createElement(le,{max:255,min:0,value:Number(l.toRgb().b),prefixCls:t,className:o,onChange:d=>s(Number(d),"b")}))},Fr=[te.hex,te.hsb,te.rgb].map(e=>({value:e,label:e.toLocaleUpperCase()})),_r=e=>{const{prefixCls:t,format:n,value:r,disabledAlpha:o,onFormatChange:l,onChange:i}=e,[s,d]=je(te.hex,{value:n,onChange:l}),m=`${t}-input`,f=I=>{d(I)},C=c.useMemo(()=>{const I={value:r,prefixCls:t,onChange:i};switch(s){case te.hsb:return A.createElement(Mr,Object.assign({},I));case te.rgb:return A.createElement(Gr,Object.assign({},I));case te.hex:default:return A.createElement(Dr,Object.assign({},I))}},[s,t,r,i]);return A.createElement("div",{className:`${m}-container`},A.createElement(se,{value:s,bordered:!1,getPopupContainer:I=>I,popupMatchSelectWidth:68,placement:"bottomRight",onChange:f,className:`${t}-format-select`,size:"small",options:Fr}),A.createElement("div",{className:m},C),!o&&A.createElement(Tr,{prefixCls:t,value:r,onChange:i}))};var Vr=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const lt=()=>{const e=c.useContext(Nt),{prefixCls:t,colorCleared:n,allowClear:r,value:o,disabledAlpha:l,onChange:i,onClear:s,onChangeComplete:d}=e,m=Vr(e,["prefixCls","colorCleared","allowClear","value","disabledAlpha","onChange","onClear","onChangeComplete"]);return A.createElement(A.Fragment,null,r&&A.createElement(Ht,Object.assign({prefixCls:t,value:o,colorCleared:n,onChange:f=>{i==null||i(f),s==null||s()}},m)),A.createElement(Or,{prefixCls:t,value:o==null?void 0:o.toHsb(),disabledAlpha:l,onChange:(f,C)=>i==null?void 0:i(f,C,!0),onChangeComplete:d}),A.createElement(_r,Object.assign({value:o,onChange:i,prefixCls:t,disabledAlpha:l},m)))},De=e=>e.map(t=>(t.colors=t.colors.map(Q),t)),Zr=(e,t)=>{const{r:n,g:r,b:o,a:l}=e.toRgb(),i=new Se(e.toRgbString()).onBackground(t).toHsv();return l<=.5?i.v>.5:n*.299+r*.587+o*.114>192},st=e=>{let{label:t}=e;return`panel-${t}`},Qr=e=>{let{prefixCls:t,presets:n,value:r,onChange:o}=e;const[l]=qt("ColorPicker"),[,i]=vt(),[s]=je(De(n),{value:De(n),postState:De}),d=`${t}-presets`,m=c.useMemo(()=>s.reduce((I,u)=>{const{defaultOpen:p=!0}=u;return p&&I.push(st(u)),I},[]),[s]),f=I=>{o==null||o(I)},C=s.map(I=>{var u;return{key:st(I),label:A.createElement("div",{className:`${d}-label`},I==null?void 0:I.label),children:A.createElement("div",{className:`${d}-items`},Array.isArray(I==null?void 0:I.colors)&&((u=I.colors)===null||u===void 0?void 0:u.length)>0?I.colors.map((p,h)=>A.createElement(Qe,{key:`preset-${h}-${p.toHexString()}`,color:Q(p).toRgbString(),prefixCls:t,className:K(`${d}-color`,{[`${d}-color-checked`]:p.toHexString()===(r==null?void 0:r.toHexString()),[`${d}-color-bright`]:Zr(p,i.colorBgElevated)}),onClick:()=>f(p)})):A.createElement("span",{className:`${d}-empty`},l.presetEmpty))}});return A.createElement("div",{className:d},A.createElement(er,{defaultActiveKey:m,ghost:!0,items:C}))},it=()=>{const{prefixCls:e,value:t,presets:n,onChange:r}=c.useContext(Ot);return Array.isArray(n)?A.createElement(Qr,{value:t,presets:n,prefixCls:e,onChange:r}):null};var Xr=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Yr=e=>{const{prefixCls:t,presets:n,panelRender:r,color:o,onChange:l,onClear:i}=e,s=Xr(e,["prefixCls","presets","panelRender","color","onChange","onClear"]),d=`${t}-inner-content`,m=Object.assign({prefixCls:t,value:o,onChange:l,onClear:i},s),f=A.useMemo(()=>({prefixCls:t,value:o,presets:n,onChange:l}),[t,o,n,l]),C=A.createElement(A.Fragment,null,A.createElement(lt,null),Array.isArray(n)&&A.createElement(de,{className:`${d}-divider`}),A.createElement(it,null));return A.createElement(Hr,{value:m},A.createElement(Br,{value:f},A.createElement("div",{className:d},typeof r=="function"?r(C,{components:{Picker:lt,Presets:it}}):C)))};var zr=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Kr=c.forwardRef((e,t)=>{const{color:n,prefixCls:r,open:o,colorCleared:l,disabled:i,format:s,className:d,showText:m}=e,f=zr(e,["color","prefixCls","open","colorCleared","disabled","format","className","showText"]),C=`${r}-trigger`,I=c.useMemo(()=>l?A.createElement(Ht,{prefixCls:r}):A.createElement(Qe,{prefixCls:r,color:n.toRgbString()}),[n,l,r]),u=()=>{const h=n.toHexString().toUpperCase(),j=Ee(n);switch(s){case"rgb":return n.toRgbString();case"hsb":return n.toHsbString();case"hex":default:return j<100?`${h.slice(0,7)},${j}%`:h}},p=()=>{if(typeof m=="function")return m(n);if(m)return u()};return A.createElement("div",Object.assign({ref:t,className:K(C,d,{[`${C}-active`]:o,[`${C}-disabled`]:i})},f),I,m&&A.createElement("div",{className:`${C}-text`},p()))});function ct(e){return e!==void 0}const Ur=(e,t)=>{const{defaultValue:n,value:r}=t,[o,l]=c.useState(()=>{let i;return ct(r)?i=r:ct(n)?i=n:i=e,Q(i||"")});return c.useEffect(()=>{r&&l(Q(r))},[r]),[o,l]},Bt=(e,t)=>({backgroundImage:`conic-gradient(${t} 0 25%, transparent 0 50%, ${t} 0 75%, transparent 0)`,backgroundSize:`${e} ${e}`}),gt=(e,t)=>{const{componentCls:n,borderRadiusSM:r,colorPickerInsetShadow:o,lineWidth:l,colorFillSecondary:i}=e;return{[`${n}-color-block`]:Object.assign(Object.assign({position:"relative",borderRadius:r,width:t,height:t,boxShadow:o},Bt("50%",e.colorFillSecondary)),{[`${n}-color-block-inner`]:{width:"100%",height:"100%",border:`${Y(l)} solid ${i}`,borderRadius:"inherit"}})}},Jr=e=>{const{componentCls:t,antCls:n,fontSizeSM:r,lineHeightSM:o,colorPickerAlphaInputWidth:l,marginXXS:i,paddingXXS:s,controlHeightSM:d,marginXS:m,fontSizeIcon:f,paddingXS:C,colorTextPlaceholder:I,colorPickerInputNumberHandleWidth:u,lineWidth:p}=e;return{[`${t}-input-container`]:{display:"flex",[`${t}-steppers${n}-input-number`]:{fontSize:r,lineHeight:o,[`${n}-input-number-input`]:{paddingInlineStart:s,paddingInlineEnd:0},[`${n}-input-number-handler-wrap`]:{width:u}},[`${t}-steppers${t}-alpha-input`]:{flex:`0 0 ${Y(l)}`,marginInlineStart:i},[`${t}-format-select${n}-select`]:{marginInlineEnd:m,width:"auto","&-single":{[`${n}-select-selector`]:{padding:0,border:0},[`${n}-select-arrow`]:{insetInlineEnd:0},[`${n}-select-selection-item`]:{paddingInlineEnd:e.calc(f).add(i).equal(),fontSize:r,lineHeight:`${Y(d)}`},[`${n}-select-item-option-content`]:{fontSize:r,lineHeight:o},[`${n}-select-dropdown`]:{[`${n}-select-item`]:{minHeight:"auto"}}}},[`${t}-input`]:{gap:i,alignItems:"center",flex:1,width:0,[`${t}-hsb-input,${t}-rgb-input`]:{display:"flex",gap:i,alignItems:"center"},[`${t}-steppers`]:{flex:1},[`${t}-hex-input${n}-input-affix-wrapper`]:{flex:1,padding:`0 ${Y(C)}`,[`${n}-input`]:{fontSize:r,textTransform:"uppercase",lineHeight:Y(e.calc(d).sub(e.calc(p).mul(2)).equal())},[`${n}-input-prefix`]:{color:I}}}}}},qr=e=>{const{componentCls:t,controlHeightLG:n,borderRadiusSM:r,colorPickerInsetShadow:o,marginSM:l,colorBgElevated:i,colorFillSecondary:s,lineWidthBold:d,colorPickerHandlerSize:m,colorPickerHandlerSizeSM:f,colorPickerSliderHeight:C}=e;return{[`${t}-select`]:{[`${t}-palette`]:{minHeight:e.calc(n).mul(4).equal(),overflow:"hidden",borderRadius:r},[`${t}-saturation`]:{position:"absolute",borderRadius:"inherit",boxShadow:o,inset:0},marginBottom:l},[`${t}-handler`]:{width:m,height:m,border:`${Y(d)} solid ${i}`,position:"relative",borderRadius:"50%",cursor:"pointer",boxShadow:`${o}, 0 0 0 1px ${s}`,"&-sm":{width:f,height:f}},[`${t}-slider`]:{borderRadius:e.calc(C).div(2).equal(),[`${t}-palette`]:{height:C},[`${t}-gradient`]:{borderRadius:e.calc(C).div(2).equal(),boxShadow:o},"&-alpha":Bt(`${Y(C)}`,e.colorFillSecondary),"&-hue":{marginBottom:l}},[`${t}-slider-container`]:{display:"flex",gap:l,marginBottom:l,[`${t}-slider-group`]:{flex:1,"&-disabled-alpha":{display:"flex",alignItems:"center",[`${t}-slider`]:{flex:1,marginBottom:0}}}}}},ea=e=>{const{componentCls:t,antCls:n,colorTextQuaternary:r,paddingXXS:o,colorPickerPresetColorSize:l,fontSizeSM:i,colorText:s,lineHeightSM:d,lineWidth:m,borderRadius:f,colorFill:C,colorWhite:I,marginXXS:u,paddingXS:p,fontHeightSM:h}=e;return{[`${t}-presets`]:{[`${n}-collapse-item > ${n}-collapse-header`]:{padding:0,[`${n}-collapse-expand-icon`]:{height:h,color:r,paddingInlineEnd:o}},[`${n}-collapse`]:{display:"flex",flexDirection:"column",gap:u},[`${n}-collapse-item > ${n}-collapse-content > ${n}-collapse-content-box`]:{padding:`${Y(p)} 0`},"&-label":{fontSize:i,color:s,lineHeight:d},"&-items":{display:"flex",flexWrap:"wrap",gap:e.calc(u).mul(1.5).equal(),[`${t}-presets-color`]:{position:"relative",cursor:"pointer",width:l,height:l,"&::before":{content:'""',pointerEvents:"none",width:e.calc(l).add(e.calc(m).mul(4)).equal(),height:e.calc(l).add(e.calc(m).mul(4)).equal(),position:"absolute",top:e.calc(m).mul(-2).equal(),insetInlineStart:e.calc(m).mul(-2).equal(),borderRadius:f,border:`${Y(m)} solid transparent`,transition:`border-color ${e.motionDurationMid} ${e.motionEaseInBack}`},"&:hover::before":{borderColor:C},"&::after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"21.5%",display:"table",width:e.calc(l).div(13).mul(5).equal(),height:e.calc(l).div(13).mul(8).equal(),border:`${Y(e.lineWidthBold)} solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`},[`&${t}-presets-color-checked`]:{"&::after":{opacity:1,borderColor:I,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`transform ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`},[`&${t}-presets-color-bright`]:{"&::after":{borderColor:"rgba(0, 0, 0, 0.45)"}}}}},"&-empty":{fontSize:i,color:r}}}},_e=(e,t,n)=>({borderInlineEndWidth:e.lineWidth,borderColor:t,boxShadow:`0 0 0 ${Y(e.controlOutlineWidth)} ${n}`,outline:0}),ta=e=>{const{componentCls:t}=e;return{"&-rtl":{[`${t}-presets-color`]:{"&::after":{direction:"ltr"}},[`${t}-clear`]:{"&::after":{direction:"ltr"}}}}},dt=(e,t,n)=>{const{componentCls:r,borderRadiusSM:o,lineWidth:l,colorSplit:i,red6:s}=e;return{[`${r}-clear`]:Object.assign(Object.assign({width:t,height:t,borderRadius:o,border:`${Y(l)} solid ${i}`,position:"relative",cursor:"pointer",overflow:"hidden"},n),{"&::after":{content:'""',position:"absolute",insetInlineEnd:l,top:0,display:"block",width:40,height:2,transformOrigin:"right",transform:"rotate(-45deg)",backgroundColor:s}})}},ra=e=>{const{componentCls:t,colorError:n,colorWarning:r,colorErrorHover:o,colorWarningHover:l,colorErrorOutline:i,colorWarningOutline:s}=e;return{[`&${t}-status-error`]:{borderColor:n,"&:hover":{borderColor:o},[`&${t}-trigger-active`]:Object.assign({},_e(e,n,i))},[`&${t}-status-warning`]:{borderColor:r,"&:hover":{borderColor:l},[`&${t}-trigger-active`]:Object.assign({},_e(e,r,s))}}},aa=e=>{const{componentCls:t,controlHeightLG:n,controlHeightSM:r,controlHeight:o,controlHeightXS:l,borderRadius:i,borderRadiusSM:s,borderRadiusXS:d,borderRadiusLG:m,fontSizeLG:f}=e;return{[`&${t}-lg`]:{minWidth:n,height:n,borderRadius:m,[`${t}-color-block, ${t}-clear`]:{width:o,height:o,borderRadius:i},[`${t}-trigger-text`]:{fontSize:f}},[`&${t}-sm`]:{minWidth:r,height:r,borderRadius:s,[`${t}-color-block, ${t}-clear`]:{width:l,height:l,borderRadius:d}}}},na=e=>{const{componentCls:t,colorPickerWidth:n,colorPrimary:r,motionDurationMid:o,colorBgElevated:l,colorTextDisabled:i,colorText:s,colorBgContainerDisabled:d,borderRadius:m,marginXS:f,marginSM:C,controlHeight:I,controlHeightSM:u,colorBgTextActive:p,colorPickerPresetColorSize:h,colorPickerPreviewSize:j,lineWidth:S,colorBorder:P,paddingXXS:R,fontSize:y,colorPrimaryHover:L,controlOutline:k}=e;return[{[t]:Object.assign({[`${t}-inner-content`]:Object.assign(Object.assign(Object.assign(Object.assign({display:"flex",flexDirection:"column",width:n,"&-divider":{margin:`${Y(C)} 0 ${Y(f)}`},[`${t}-panel`]:Object.assign({},qr(e))},gt(e,j)),Jr(e)),ea(e)),dt(e,h,{marginInlineStart:"auto",marginBottom:f})),"&-trigger":Object.assign(Object.assign(Object.assign(Object.assign({minWidth:I,height:I,borderRadius:m,border:`${Y(S)} solid ${P}`,cursor:"pointer",display:"inline-flex",alignItems:"center",justifyContent:"center",transition:`all ${o}`,background:l,padding:e.calc(R).sub(S).equal(),[`${t}-trigger-text`]:{marginInlineStart:f,marginInlineEnd:e.calc(f).sub(e.calc(R).sub(S)).equal(),fontSize:y,color:s},"&:hover":{borderColor:L},[`&${t}-trigger-active`]:Object.assign({},_e(e,r,k)),"&-disabled":{color:i,background:d,cursor:"not-allowed","&:hover":{borderColor:p},[`${t}-trigger-text`]:{color:i}}},dt(e,u)),gt(e,u)),ra(e)),aa(e))},ta(e))}]},oa=tr("ColorPicker",e=>{const{colorTextQuaternary:t,marginSM:n}=e,r=8,o=rr(e,{colorPickerWidth:234,colorPickerHandlerSize:16,colorPickerHandlerSizeSM:12,colorPickerAlphaInputWidth:44,colorPickerInputNumberHandleWidth:16,colorPickerPresetColorSize:18,colorPickerInsetShadow:`inset 0 0 1px 0 ${t}`,colorPickerSliderHeight:r,colorPickerPreviewSize:e.calc(r).mul(2).add(n).equal()});return[na(o)]});var la=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Xe=e=>{const{value:t,defaultValue:n,format:r,defaultFormat:o,allowClear:l=!1,presets:i,children:s,trigger:d="click",open:m,disabled:f,placement:C="bottomLeft",arrow:I=!0,panelRender:u,showText:p,style:h,className:j,size:S,rootClassName:P,prefixCls:R,styles:y,disabledAlpha:L=!1,onFormatChange:k,onChange:_,onClear:G,onOpenChange:w,onChangeComplete:T,getPopupContainer:Z,autoAdjustOverflow:J=!0,destroyTooltipOnHide:U}=e,x=la(e,["value","defaultValue","format","defaultFormat","allowClear","presets","children","trigger","open","disabled","placement","arrow","panelRender","showText","style","className","size","rootClassName","prefixCls","styles","disabledAlpha","onFormatChange","onChange","onClear","onOpenChange","onChangeComplete","getPopupContainer","autoAdjustOverflow","destroyTooltipOnHide"]),{getPrefixCls:v,direction:N,colorPicker:$}=c.useContext(ar),H=c.useContext(nr),g=f??H,[,b]=vt(),[O,W]=Ur(b.colorPrimary,{value:t,defaultValue:n}),[z,Ye]=je(!1,{value:m,postState:re=>!g&&re,onChange:w}),[Ne,ze]=je(r,{value:r,defaultValue:o,onChange:k}),[me,Ce]=c.useState(!1),V=v("color-picker",R),Ke=c.useMemo(()=>Ee(O)<100,[O]),{status:Tt}=A.useContext(or),Ue=lr(S),Je=sr(V),[Wt,Dt,Mt]=oa(V,Je),Gt={[`${V}-rtl`]:N},qe=K(P,Mt,Je,Gt),Ft=K(ir(V,Tt),{[`${V}-sm`]:Ue==="small",[`${V}-lg`]:Ue==="large"},$==null?void 0:$.className,qe,j,Dt),_t=K(V,qe),Oe=c.useRef(!0),Vt=(re,Ae,zt)=>{let ae=Q(re);(me||(t===null||!t&&n===null))&&(Ce(!1),Ee(O)===0&&Ae!=="alpha"&&(ae=We(ae))),L&&Ke&&(ae=We(ae)),zt?Oe.current=!1:T==null||T(ae),W(ae),_==null||_(ae,ae.toHexString())},Zt=()=>{Ce(!0),G==null||G()},et=re=>{Oe.current=!0;let Ae=Q(re);L&&Ke&&(Ae=We(re)),T==null||T(Ae)},Qt={open:z,trigger:d,placement:C,arrow:I,rootClassName:P,getPopupContainer:Z,autoAdjustOverflow:J,destroyTooltipOnHide:U},Xt={prefixCls:V,color:O,allowClear:l,colorCleared:me,disabled:g,disabledAlpha:L,presets:i,panelRender:u,format:Ne,onFormatChange:ze,onChangeComplete:et},Yt=Object.assign(Object.assign({},$==null?void 0:$.style),h);return Wt(A.createElement(cr,Object.assign({style:y==null?void 0:y.popup,overlayInnerStyle:y==null?void 0:y.popupOverlayInner,onOpenChange:re=>{Oe.current&&!g&&Ye(re)},content:A.createElement(gr,{override:!0,status:!0},A.createElement(Yr,Object.assign({},Xt,{onChange:Vt,onChangeComplete:et,onClear:Zt}))),overlayClassName:_t},Qt),s||A.createElement(Kr,Object.assign({open:z,className:Ft,style:Yt,color:t?Q(t):O,prefixCls:V,disabled:g,colorCleared:me,showText:p,format:Ne},x))))},sa=yt(Xe,"color-picker",e=>e,e=>Object.assign(Object.assign({},e),{placement:"bottom",autoAdjustOverflow:!1}));Xe._InternalPanelDoNotUseOrYouWillBeFired=sa;var ia=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const{TimePicker:ca,RangePicker:ga}=dr,da=c.forwardRef((e,t)=>c.createElement(ga,Object.assign({},e,{picker:"time",mode:void 0,ref:t}))),ue=c.forwardRef((e,t)=>{var{addon:n,renderExtraFooter:r}=e,o=ia(e,["addon","renderExtraFooter"]);const l=c.useMemo(()=>{if(r)return r;if(n)return n},[n,r]);return c.createElement(ca,Object.assign({},o,{mode:void 0,ref:t,renderExtraFooter:l}))}),$t=yt(ue,"picker");ue._InternalPanelDoNotUseOrYouWillBeFired=$t;ue.RangePicker=da;ue._InternalPanelDoNotUseOrYouWillBeFired=$t;const ua=e=>c.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:20,height:20,viewBox:"0 0 20 20",...e},c.createElement("defs",null,c.createElement("clipPath",{id:"master_svg0_195_66473"},c.createElement("rect",{x:0,y:0,width:20,height:20,rx:0}))),c.createElement("g",{clipPath:"url(#master_svg0_195_66473)"},c.createElement("g",null,c.createElement("path",{d:"M3.2309200000000002,7.81229C2.843441,7.81231,2.682339,8.30813,2.995804,8.53589L3.5694,8.95266L3.65144,9.01228L6.70891,11.23384C6.84909,11.33569,6.90775,11.51623,6.85421,11.68103L5.68647,15.2754L5.65513,15.3719L5.4360599999999994,16.0462C5.31634,16.4147,5.73811,16.7212,6.05159,16.4934L6.62521,16.076700000000002L6.70726,16.0171L9.7649,13.7958C9.905090000000001,13.6939,10.09491,13.6939,10.2351,13.7958L13.2927,16.0171L13.3748,16.076700000000002L13.9484,16.4934C14.2619,16.7212,14.6837,16.4147,14.5639,16.0462L14.3449,15.3719L14.3135,15.2754L13.1458,11.68103C13.0922,11.51623,13.1509,11.33569,13.2911,11.23384L16.348599999999998,9.01228L16.4306,8.95266L17.0042,8.53589C17.317700000000002,8.30813,17.156599999999997,7.81231,16.7691,7.81229L16.0601,7.81227L15.9587,7.81226L12.1793,7.81212C12.006,7.81211,11.85246,7.70054,11.7989,7.53574L10.63089,3.9414100000000003L10.59954,3.84496L10.38042,3.17066C10.26067,2.80215,9.739329999999999,2.80215,9.61958,3.17066L9.400459999999999,3.84496L9.369119999999999,3.9414100000000003L8.2011,7.53574C8.14754,7.70054,7.99397,7.81211,7.82069,7.81212L4.0413499999999996,7.81226L3.93993,7.81227L3.2309200000000002,7.81229ZM7.41429,10.26304L5.6927900000000005,9.0122L7.82074,9.01212Q8.98312,9.012080000000001,9.34235,7.90661L10,5.88283L10.65765,7.9066Q11.01688,9.012080000000001,12.1793,9.01212L14.3072,9.0122L12.5857,10.26304Q11.64535,10.9463,12.0045,12.0518L12.662,14.0756L10.9404,12.8249Q10,12.1417,9.05959,12.8249L7.338,14.0756L7.9955,12.0518Q8.35465,10.9463,7.41429,10.26304Z",fillRule:"evenodd",fill:"#000000",fillOpacity:1})))),ut=e=>c.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:20,height:20,viewBox:"0 0 20 20",...e},c.createElement("defs",null,c.createElement("clipPath",{id:"master_svg0_101_30935"},c.createElement("rect",{x:0,y:0,width:20,height:20,rx:0}))),c.createElement("g",{clipPath:"url(#master_svg0_101_30935)"},c.createElement("g",null,c.createElement("path",{d:"M3.2309200000000002,7.81229C2.843441,7.81231,2.682339,8.30813,2.995804,8.53589L6.70891,11.23384C6.84909,11.33569,6.90775,11.51623,6.85421,11.68103L5.4360599999999994,16.0462C5.31634,16.4147,5.73811,16.7212,6.05159,16.4934L9.7649,13.7958C9.905090000000001,13.6939,10.09491,13.6939,10.2351,13.7958L13.9484,16.4934C14.2619,16.7212,14.6837,16.4147,14.5639,16.0462L13.1458,11.68103C13.0922,11.51623,13.1509,11.33569,13.2911,11.23384L17.0042,8.53589C17.317700000000002,8.30813,17.156599999999997,7.81231,16.7691,7.81229L12.1793,7.81212C12.006,7.81211,11.85246,7.70054,11.7989,7.53574L10.38042,3.17066C10.26067,2.80215,9.739329999999999,2.80215,9.61958,3.17066L8.2011,7.53574C8.14754,7.70054,7.99397,7.81211,7.82069,7.81212L3.2309200000000002,7.81229Z",fill:"#14C9BB",fillOpacity:1})))),ma=e=>c.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:20,height:20,viewBox:"0 0 20 20",...e},c.createElement("defs",null,c.createElement("clipPath",{id:"master_svg0_195_65048"},c.createElement("rect",{x:0,y:0,width:20,height:20,rx:0}))),c.createElement("g",{clipPath:"url(#master_svg0_195_65048)"},c.createElement("g",null,c.createElement("path",{d:"M3.2309200000000002,7.81229C2.843441,7.81231,2.682339,8.30813,2.995804,8.53589L3.5694,8.95266L3.65144,9.01228L6.70891,11.23384C6.84909,11.33569,6.90775,11.51623,6.85421,11.68103L5.68647,15.2754L5.65513,15.3719L5.4360599999999994,16.0462C5.31634,16.4147,5.73811,16.7212,6.05159,16.4934L6.62521,16.076700000000002L6.70726,16.0171L9.7649,13.7958C9.905090000000001,13.6939,10.09491,13.6939,10.2351,13.7958L13.2927,16.0171L13.3748,16.076700000000002L13.9484,16.4934C14.2619,16.7212,14.6837,16.4147,14.5639,16.0462L14.3449,15.3719L14.3135,15.2754L13.1458,11.68103C13.0922,11.51623,13.1509,11.33569,13.2911,11.23384L16.348599999999998,9.01228L16.4306,8.95266L17.0042,8.53589C17.317700000000002,8.30813,17.156599999999997,7.81231,16.7691,7.81229L16.0601,7.81227L15.9587,7.81226L12.1793,7.81212C12.006,7.81211,11.85246,7.70054,11.7989,7.53574L10.63089,3.9414100000000003L10.59954,3.84496L10.38042,3.17066C10.26067,2.80215,9.739329999999999,2.80215,9.61958,3.17066L9.400459999999999,3.84496L9.369119999999999,3.9414100000000003L8.2011,7.53574C8.14754,7.70054,7.99397,7.81211,7.82069,7.81212L4.0413499999999996,7.81226L3.93993,7.81227L3.2309200000000002,7.81229ZM7.41429,10.26304L5.6927900000000005,9.0122L7.82074,9.01212Q8.98312,9.012080000000001,9.34235,7.90661L10,5.88283L10.65765,7.9066Q11.01688,9.012080000000001,12.1793,9.01212L14.3072,9.0122L12.5857,10.26304Q11.64535,10.9463,12.0045,12.0518L12.662,14.0756L10.9404,12.8249Q10,12.1417,9.05959,12.8249L7.338,14.0756L7.9955,12.0518Q8.35465,10.9463,7.41429,10.26304Z",fillRule:"evenodd",fill:"#14C9BB",fillOpacity:1})))),Ca=e=>c.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:16,height:16,viewBox:"0 0 16 16",...e},c.createElement("defs",null,c.createElement("clipPath",{id:"master_svg0_101_30924"},c.createElement("rect",{x:0,y:0,width:16,height:16,rx:0}))),c.createElement("g",null,c.createElement("g",{clipPath:"url(#master_svg0_101_30924)"},c.createElement("g",null,c.createElement("path",{d:"M8.79152,10.846689999999999L12.79579,5.6000499999999995C13.0256,5.29887,13.0647,4.893348,12.89652,4.553846C12.72835,4.214342,12.38211,3.999667786,12.00324,4.000000197864L3.995932,4.000000197864C3.617212,3.9999837752,3.271259,4.214779,3.1033,4.554218C2.9353417,4.893657,2.9744491,5.29898,3.204206,5.6000499999999995L7.20848,10.846689999999999C7.60707,11.36869,8.39293,11.36869,8.79152,10.846689999999999Z",fill:"#212519",fillOpacity:1}))))),ha=e=>c.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",fill:"none",width:24,height:24,viewBox:"0 0 24 24",...e},c.createElement("defs",null,c.createElement("clipPath",{id:"master_svg0_195_65388"},c.createElement("rect",{x:0,y:0,width:24,height:24,rx:0}))),c.createElement("g",{clipPath:"url(#master_svg0_195_65388)"},c.createElement("g",null,c.createElement("path",{d:"M20.81583984375,7.087728515625L14.93983984375,2.615729515625C14.22023984375,2.067530515625,13.34043984375,1.770986437625,12.43583984375,1.771729946135C11.55183984375,1.771729946135,10.67183984375,2.051730515625,9.93183984375,2.615729515625L4.05183984375,7.087728515625C3.53583984375,7.479728515625,3.23583984375,8.087728515624999,3.23583984375,8.735728515625L3.23583984375,17.351728515625C3.23583984375,19.447728515625,4.93583984375,21.143728515625,7.02783984375,21.143728515625L17.83983984375,21.143728515625C19.93583984375,21.143728515625,21.63183984375,19.443728515625,21.63183984375,17.351728515625L21.63183984375,8.735728515625C21.63583984375,8.087728515624999,21.33183984375,7.479728515625,20.81583984375,7.087728515625ZM20.03583984375,17.347728515625C20.03583984375,18.555728515625,19.051839843750003,19.539728515625,17.84383984375,19.539728515625L7.02783984375,19.539728515625C5.81983984375,19.539728515625,4.83583984375,18.555728515625,4.83583984375,17.347728515625L4.83583984375,8.735728515625C4.83583984375,8.591728515625,4.90383984375,8.451728515625,5.01983984375,8.363728515624999L10.89983984375,3.891728515625C11.34383984375,3.5517285156250002,11.87583984375,3.371728515625,12.43583984375,3.371728515625C12.99583984375,3.371728515625,13.52783984375,3.5517285156250002,13.97183984375,3.891728515625L19.85183984375,8.363728515624999C19.96783984375,8.451728515625,20.03583984375,8.591728515625,20.03583984375,8.735728515625L20.03583984375,17.347728515625Z",fill:"#212519",fillOpacity:1,style:{mixBlendMode:"passthrough"}}),c.createElement("path",{d:"M21.83183984375,17.351728515625L21.83183984375,8.736338515625L21.83183984375,8.735728515625Q21.83823984375,7.613298515625,20.93683984375,6.928478515625L15.06093984375,2.456579515625Q13.89793984375,1.570527515625,12.43567984375,1.571729515625Q10.97168984375,1.571729515625,9.810609843750001,2.456663515625L3.93076884375,6.928538515625Q3.03583984375,7.608408515625,3.03583984375,8.735728515625L3.03583984375,17.351728515625Q3.03583984375,19.005928515625,4.20596984375,20.175228515625Q5.37534984375,21.343728515625,7.02783984375,21.343728515625L17.83983984375,21.343728515625Q19.49403984375,21.343728515625,20.66333984375,20.173628515625Q21.83183984375,19.004228515625,21.83183984375,17.351728515625ZM20.69483984375,7.246988515625Q21.43753984375,7.811198515625,21.43183984375,8.734498515624999L21.43183984375,8.735728515625L21.43183984375,17.351728515625Q21.43183984375,18.838628515625,20.38033984375,19.890828515625Q19.32823984375,20.943728515625,17.83983984375,20.943728515625L7.02783984375,20.943728515625Q5.54094984375,20.943728515625,4.48870984375,19.892228515625Q3.43583984375,18.840128515625,3.43583984375,17.351728515625L3.43583984375,8.735728515625Q3.43583984375,7.806868515625,4.17291184375,7.246918515625L10.05307984375,2.774798515625Q11.10674984375,1.971730515625,12.43600984375,1.971729515625Q13.76303984375,1.970639515625,14.81873984375,2.7748785156249998L20.69483984375,7.246988515625ZM20.23583984375,17.347728515625L20.23583984375,8.735728515625Q20.23583984375,8.403998515625,19.97293984375,8.204538515625L14.09343984375,3.732938515625Q13.36053984375,3.171728515625,12.43583984375,3.171728515625Q11.51111984375,3.171728515625,10.77876984375,3.732538515625L4.89895984375,8.204388515625Q4.63583984375,8.403998515625,4.63583984375,8.735728515625L4.63583984375,17.347728515625Q4.63583984375,18.336628515625,5.33741984375,19.038128515625Q6.03899984375,19.739728515625,7.02783984375,19.739728515625L17.84383984375,19.739728515625Q18.83263984375,19.739728515625,19.53423984375,19.038128515625Q20.23583984375,18.336528515625,20.23583984375,17.347728515625ZM19.73073984375,8.522918515625001Q19.83583984375,8.602628515625,19.83583984375,8.735728515625L19.83583984375,17.347728515625Q19.83583984375,18.170928515625,19.25143984375,18.755328515625Q18.66703984375,19.339728515625,17.84383984375,19.339728515625L7.02783984375,19.339728515625Q6.20467984375,19.339728515625,5.62025984375,18.755328515625Q5.03583984375,18.170928515625,5.03583984375,17.347728515625L5.03583984375,8.735728515625Q5.03583984375,8.602628515625,5.14071984375,8.523068515624999L11.020909843750001,4.050918515625Q11.64667984375,3.571728515625,12.43583984375,3.571728515625Q13.22499984375,3.571728515625,13.85023984375,4.050518515625L19.73073984375,8.522918515625001Z",fillRule:"evenodd",fill:"#212519",fillOpacity:1})),c.createElement("g",null,c.createElement("path",{d:"M12.432265625,10.688232421875C10.304265625,10.688232421875,8.572265625,12.420232421875,8.572265625,14.548232421875L8.572265625,17.232232421875C8.572265625,17.672232421875,8.932265625,18.032232421875,9.372265625,18.032232421875C9.812265625,18.032232421875,10.172265625,17.672232421875,10.172265625,17.232232421875L10.172265625,14.548232421875C10.172265625,13.304232421875,11.184265625,12.288232421875,12.432265625,12.288232421875C13.680265625,12.288232421875,14.692265625000001,13.300232421875,14.692265625000001,14.548232421875L14.692265625000001,17.232232421875C14.692265625000001,17.672232421875,15.052265625,18.032232421875,15.492265625,18.032232421875C15.932265625,18.032232421875,16.292265625,17.672232421875,16.292265625,17.232232421875L16.292265625,14.548232421875C16.292265625,12.420232421875,14.564265625,10.688233852385,12.432265625,10.688232421875Z",fill:"#212519",fillOpacity:1,style:{mixBlendMode:"passthrough"}}),c.createElement("path",{d:"M16.492265625,17.232232421875L16.492265625,14.548232421875Q16.492265625,12.868742421875,15.303235625,11.678862421875Q14.113455625,10.488233421875,12.432265625,10.488232421875Q10.753425625,10.488232421875,9.562844625,11.678811421875Q8.372265625,12.869392421875,8.372265625,14.548232421875L8.372265625,17.232232421875Q8.372265625,17.645072421875,8.665843925,17.938652421875Q8.959422625,18.232232421875,9.372265625,18.232232421875Q9.785105625,18.232232421875,10.078685625,17.938652421875Q10.372265625,17.645072421875,10.372265625,17.232232421875L10.372265625,14.548232421875Q10.372265625,13.697312421875,10.975775625,13.093072421875Q11.579865625,12.488232421875,12.432265625,12.488232421875Q13.285425625,12.488232421875,13.888845625,13.091652421875Q14.492265625,13.695072421875,14.492265625,14.548232421875L14.492265625,17.232232421875Q14.492265625,17.645072421875,14.785845625,17.938652421875Q15.079425624999999,18.232232421875,15.492265625,18.232232421875Q15.905105625000001,18.232232421875,16.198685625,17.938652421875Q16.492265625,17.645072421875,16.492265625,17.232232421875ZM15.020295625,11.961602421875Q16.092265625,13.034342421875,16.092265625,14.548232421875L16.092265625,17.232232421875Q16.092265625,17.479392421874998,15.915845625,17.655812421874998Q15.739415625,17.832232421875,15.492265625,17.832232421875Q15.245105625,17.832232421875,15.068685625,17.655812421874998Q14.892265625,17.479392421874998,14.892265625,17.232232421875L14.892265625,14.548232421875Q14.892265625,13.529392421875,14.171685625,12.808812421875Q13.451105625,12.088232421875,12.432265625,12.088232421875Q11.414045625,12.088232421875,10.692755625,12.810402421875Q9.972265625,13.531772421875,9.972265625,14.548232421875L9.972265625,17.232232421875Q9.972265625,17.479392421874998,9.795845625,17.655812421874998Q9.619425625,17.832232421875,9.372265625,17.832232421875Q9.125107625,17.832232421875,8.948686625,17.655812421874998Q8.772265625,17.479392421874998,8.772265625,17.232232421875L8.772265625,14.548232421875Q8.772265625,13.035072421875,9.845685625,11.961652421875Q10.919105625,10.888232421875,12.432265625,10.888232421875Q13.947695625,10.888233421875,15.020295625,11.961602421875Z",fillRule:"evenodd",fill:"#212519",fillOpacity:1})))),Na=({onChange:e})=>{var G;const{isFavorited:t,toggleFavorite:n,getFirstVenueFavoriteId:r}=xr(),o=ur(),[l,i]=c.useState(!1),s=mr(),[d,m]=c.useState(null),[f,C]=c.useState(!1),I=o.data?[...o.data].sort((w,T)=>String(w.id)==="0"?-1:String(T.id)==="0"?1:w.name.localeCompare(T.name)).map(w=>({label:w.name,value:w.id,type:"venue"})):[],u=()=>{const w=window.location.hash;return w?w.substring(1):null};let p=r(),h=p,j="";o.data&&(h=h??o.data[0].id,h=u()||h,j=((G=I.find(w=>w.value===h))==null?void 0:G.label)||"",window.history.replaceState({},"",`#${h}`));const[S,P]=c.useState(h),[R,y]=c.useState(p&&p===h);A.useEffect(()=>{typeof o.refetch=="function"&&o.refetch(),h!=null&&h!==S&&(P(h),y(p&&p===h))},[h]);const L=w=>{P(w),t(w,y),e&&e(w)},k=w=>r()===w,_=a.jsxs(be,{style:{minWidth:300,borderRadius:8,padding:"10px 0 10px 0"},children:[I.map(w=>a.jsxs(be.Item,{onClick:()=>L(w.value),onMouseEnter:()=>m(w.value),onMouseLeave:()=>m(null),style:{height:30,lineHeight:"30px",fontSize:16,backgroundColor:S===w.value?"rgba(20, 201, 187, 0.1)":d===w.value?"#F4F5F7":"transparent",color:S===w.value?"#14C9BB":"#000",margin:"0 8px",borderRadius:4,transition:"background-color 0.3s",position:"relative",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[w.label,k(w.value)&&a.jsx(ie,{component:ut,style:{width:16,height:16,color:"#14C9BB",marginLeft:220}})]},w.value)),a.jsx(be.Divider,{style:{borderTop:"1px solid #E7E7E7",marginBottom:-6,marginTop:8}}),a.jsx(be.Item,{onClick:()=>s("/resource/site_management"),style:{display:"flex",justifyContent:"center",width:"280px",height:30,color:f?"#34DCCF":"#14C9BB",background:"transparent",margin:0,borderRadius:0},onMouseEnter:()=>C(!0),onMouseLeave:()=>C(!1),children:a.jsxs("div",{style:{display:"flex",alignItems:"center",gap:-10,whiteSpace:"nowrap",textAlign:"center",color:f?"#34DCCF":"#14C9BB"},children:[a.jsx(tt,{style:{fontSize:15,color:f?"#34DCCF":"#14C9BB",textAlign:"center",marginLeft:70,marginRight:5,marginBottom:-13}}),a.jsx("span",{style:{fontWeight:400,fontSize:15,marginBottom:-13},children:"Create New Site"})]})},"create")]});return!o.data||o.data.length===0?a.jsx(X,{type:"primary",onClick:()=>s("/resource/site_management"),icon:a.jsx(tt,{}),style:{backgroundColor:"#14C9BB",borderColor:"#14C9BB"},children:"Create New Site"}):a.jsx(Cr,{overlay:_,trigger:["click"],overlayStyle:{minWidth:200},children:a.jsxs("div",{style:{display:"flex",alignItems:"center",position:"relative",cursor:"pointer"},children:[a.jsx(ie,{component:ha,style:{height:32,color:"#212519",marginRight:4,fontSize:26}}),a.jsx("span",{style:{marginRight:4,height:"24px",fontFamily:"Lato,Lato",fontWeight:700,fontSize:"20px",color:"#212529",textAlign:"left",fontStyle:"normal",lineHeight:"24px",textTransform:"none",whiteSpace:"nowrap"},children:j}),a.jsx("div",{style:{display:"flex",alignItems:"center",marginRight:8,cursor:"pointer",position:"relative",padding:"1px"},onMouseEnter:()=>i(!0),onMouseLeave:()=>i(!1),onClick:w=>{w.preventDefault(),w.stopPropagation();const T=!R;y(T),n(S,T)},children:a.jsx(ie,{component:R?ut:l?ma:ua,style:{fontSize:380,transition:"all 0.3s",width:"22px",height:"22px",marginRight:-5}})}),a.jsx(ie,{component:Ca,style:{fontSize:"18px",color:"#666",marginLeft:4,verticalAlign:"middle"}})]})})},mt=/^(?=.{1,254}$)((?=[a-z0-9-]{1,63}\.)(xn--+)?[a-z0-9]+(-[a-z0-9]+)*\.)+[a-z]{2,63}$/i,Pe=/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,Le=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:))$/,Ct=/^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$/,Oa=e=>`__variableBlock__<<${e}>>`;function Ha(e){const t=e.match(/__variableBlock__<<(.+?)>>/);return t?t[1]:e}function Me(e){return e?Array.isArray(e)?new Promise((t,n)=>{for(const r of e){const o=r.trim();if(o&&!(mt.test(o)||Pe.test(o)||Le.test(o))){n(new Error(`Invalid URL or IP format: ${o}`));return}}t()}):mt.test(e)||Pe.test(e)||Le.test(e)?Promise.resolve():Promise.reject(new Error("Invalid URL or IP format")):Promise.resolve()}function Ba(e){return e?Array.isArray(e)?new Promise((t,n)=>{for(const r of e){const o=r.trim();if(o&&!(Pe.test(o)||Le.test(o))){n(new Error(`Invalid IP format: ${o}`));return}}t()}):Pe.test(e)||Le.test(e)?Promise.resolve():Promise.reject(new Error("Invalid IP format")):Promise.resolve()}function fa(e){return e?Array.isArray(e)?new Promise((t,n)=>{for(const r of e){const o=r.trim();if(o&&!Ct.test(o)){n(new Error(`${o} Invalid MAC value, for example: 00:00:5e:00:53:af`));return}}t()}):Ct.test(e)?Promise.resolve():Promise.reject(new Error("Invalid MAC value, for example: 00:00:5e:00:53:af")):Promise.resolve()}function $a(e){if(e==null||e==="")return"";let t;if(typeof e=="number")t=e<1e12?e*1e3:e;else{const m=e.trim();if(!/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(m))throw new Error('Input must be a timestamp or a string in format "YYYY-MM-DD HH:mm:ss"');const C=m.substring(0,10),I=m.substring(11),u=C.split("-");if(u.length!==3)throw new Error(`Invalid UTC time string: ${e}`);const p=Number(u[0]),h=Number(u[1]),j=Number(u[2]);if(Number.isNaN(p)||Number.isNaN(h)||Number.isNaN(j))throw new Error(`Invalid UTC time string: ${e}`);const S=I.split(":"),P=Number(S[0]||0),R=Number(S[1]||0),y=Number(S[2]||0);if(Number.isNaN(P)||Number.isNaN(R)||Number.isNaN(y))throw new Error(`Invalid UTC time string: ${e}`);t=Date.UTC(p,h-1,j,P,R,y)}const n=new Date(t),r=n.getFullYear(),o=String(n.getMonth()+1).padStart(2,"0"),l=String(n.getDate()).padStart(2,"0"),i=String(n.getHours()).padStart(2,"0"),s=String(n.getMinutes()).padStart(2,"0"),d=String(n.getSeconds()).padStart(2,"0");return`${r}-${o}-${l} ${i}:${s}:${d}`}const Ve=e=>{if(e===null||typeof e!="object")return e;if(Array.isArray(e))return e.map(n=>Ve(n));const t={};for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)){const r=Ve(e[n]);r!=null&&r!==""&&(t[n]=r)}return t},Ie="/ampcon/wireless";function Re({site_id:e,name:t,description:n,parameter:r,config_variables:o,type:l}){return fe({url:`${Ie}/profile`,method:"POST",data:{site_id:e,type:l,name:t,parameter:r,description:n,config_variables:o}})}function ke({id:e,site_id:t,name:n,description:r,parameter:o,config_variables:l,type:i}){return fe({url:`${Ie}/profile`,method:"PUT",data:{id:e,site_id:t,type:i,name:n,parameter:o,description:r,config_variables:l}})}function Ta({id:e}){return fe({url:`${Ie}/profile`,method:"DELETE",data:{id:e}})}function Wa(e,t,n,r,o=[],l=[],i=[],s={}){return fe({url:`${Ie}/profile/list`,method:"POST",data:{type:e,site_id:t,filterFields:o,parameterFilter:i,sortFields:l,searchFields:s,page:n,pageSize:r}})}function Da(e){return fe({url:`${Ie}/profile/${e}`,method:"GET"})}const ve=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],pa={Monday:1,Tuesday:2,Wednesday:3,Thursday:4,Friday:5,Saturday:6,Sunday:7};function Ia(e){const t=new Map;return e.forEach(({day:n,start:r,end:o})=>{const l=`${r}-${o}`;t.has(l)||t.set(l,{days:[],start:r,end:o}),t.get(l).days.push(n)}),Array.from(t.values()).map(n=>({days:n.days.sort((r,o)=>r-o),start:n.start,end:n.end}))}const Ma=({visible:e,resource:t,siteId:n,onClose:r,onSuccess:o})=>{const[l]=E.useForm(),[i,s]=c.useState(ve.map(u=>({day:u,checked:!1,startTime:null,endTime:null})));c.useEffect(()=>{if(t&&e){l.setFieldsValue({name:t.name||"",description:t.description||""});let u={};if(t.parameter)try{u=typeof t.parameter=="string"?JSON.parse(t.parameter):t.parameter}catch{}if(Array.isArray(u.time_range)){const p=ve.map(h=>{const j=u.time_range.find(S=>S.startsWith(h));if(j){const S=j.match(/^(\w+)\s+(\d{2}:\d{2})-(\d{2}:\d{2})$/);if(S){const[,,P,R]=S;return{day:h,checked:!0,startTime:ce(P,"HH:mm"),endTime:ce(R,"HH:mm")}}}return{day:h,checked:!1,startTime:null,endTime:null}});s(p);return}}else e||(l.resetFields(),s(ve.map(u=>({day:u,checked:!1,startTime:null,endTime:null}))))},[t,l,e]);const d=(u,p)=>{const h=[...i];h[u].checked=p,p?(h[u].startTime=h[u].startTime||ce("08:00","HH:mm"),h[u].endTime=h[u].endTime||ce("18:00","HH:mm")):(h[u].startTime=null,h[u].endTime=null),s(h)},m=(u,p)=>{const h=[...i];h[u].startTime=p,p&&h[u].endTime&&p.isAfter(h[u].endTime)&&(h[u].endTime=p),s(h)},f=(u,p)=>{const h=[...i];h[u].endTime=p,p&&h[u].startTime&&p.isBefore(h[u].startTime)&&(h[u].startTime=p),s(h)},C=async()=>{if(!i.some(h=>h.checked)){D.error("Please select at least one day.");return}if(i.some(h=>h.checked&&(!h.startTime||!h.endTime))){D.error("Please select a valid time");return}try{const h=await l.validateFields(),j=i.filter(k=>k.checked).map(k=>`${k.day} ${k.startTime.format("HH:mm")}-${k.endTime.format("HH:mm")}`),S=j.map(k=>{const[_,G]=k.split(" "),[w,T]=G.split("-");return{day:pa[_],start:w,end:T}}),P=Ia(S),R={site_id:n,type:4,name:h.name,description:h.description||"",parameter:{time_range:j},config_variables:JSON.stringify(P)};let y;t&&t.id?y=await ke({...R,id:t.id}):y=await Re(R);const L=y.data||y;y.status===200?(D.success(t?"Updated successfully":"Created successfully"),o==null||o(),r(!0),l.resetFields(),s(ve.map(k=>({day:k,checked:!1,startTime:null,endTime:null})))):D.error(t?`Update failed: ${L.info||"Unknown error"}`:`Create failed: ${L.info||"Unknown error"}`)}catch{}},I=()=>{l.resetFields(),r()};return a.jsx(Fe,{title:a.jsxs("div",{children:[t?"Edit Time Range Profile":"Create Time Range Profile",a.jsx(de,{style:{marginTop:8,marginBottom:0}})]}),open:e,onCancel:I,className:"ampcon-max-modal",footer:[a.jsx(de,{style:{margin:"0px 0px 16px -24px",width:"calc(100% + 48px)"}}),a.jsx(X,{onClick:I,style:{width:100},children:"Cancel"},"cancel"),a.jsx(X,{type:"primary",onClick:C,style:{width:100},children:"Apply"},"apply")],zIndex:1060,destroyOnClose:!0,children:a.jsx(E,{form:l,layout:"horizontal",labelAlign:"left",labelCol:{span:4},wrapperCol:{span:10},children:a.jsx(Aa,{form:l,timeRanges:i,onCheckChange:d,onStartTimeChange:m,onEndTimeChange:f})})})},Aa=({form:e,timeRanges:t,onCheckChange:n,onStartTimeChange:r,onEndTimeChange:o})=>{const{t:s}=pe(),d=C=>C?C.hour()*60+C.minute():0,m=C=>ce().hour(Math.floor(C/60)).minute(C%60),f=Array.from({length:13}).reduce((C,I,u)=>{const p=u*2;return C[p*60]={label:a.jsxs("div",{style:{display:"flex",flexDirection:"column",marginBottom:0,alignItems:"center"},children:[a.jsx("div",{style:{width:1,height:8,backgroundColor:"#DADCE1",marginBottom:0,marginTop:-8}}),a.jsx("span",{style:{fontSize:12,color:"#474747"},children:p})]})},C},{});return a.jsxs(a.Fragment,{children:[a.jsx(E.Item,{label:"Name",name:"name",rules:[{required:!0,message:"Required!"},{max:32,message:"Name cannot exceed 32 characters"}],labelCol:{span:2,style:{marginRight:24}},wrapperCol:{span:5},children:a.jsx(M,{placeholder:"Enter name"})}),a.jsx(E.Item,{label:"Description",name:"description",rules:[{max:128,message:s("form.max_length",{max:128})}],labelCol:{span:2,style:{marginRight:24}},wrapperCol:{span:5},children:a.jsx(M.TextArea,{rows:2,placeholder:"Enter description"})}),a.jsx("div",{style:{marginBottom:69},children:t.map((C,I)=>a.jsxs(F,{align:"middle",children:[a.jsx(B,{span:2,style:{marginRight:24},children:a.jsx(hr,{checked:C.checked,onChange:u=>n(I,u.target.checked),children:C.day})}),a.jsx(B,{span:2,children:a.jsx(ue,{value:C.startTime,onChange:u=>{const p=C.endTime;u&&p&&!p.isAfter(u)&&o(I,u.add(1,"minute")),r(I,u)},disabled:!C.checked,format:"HH:mm",style:{width:"100%"},allowClear:!1,inputReadOnly:!0,placeholder:""})}),a.jsx(B,{span:1,style:{textAlign:"center"},children:a.jsx("div",{style:{width:"18px",borderBottom:"1px solid #B2B2B2 ",margin:"0 auto"}})}),a.jsx(B,{span:2,children:a.jsx(ue,{value:C.endTime,onChange:u=>{const p=C.startTime;u&&d(u)>1439&&(u=ce().hour(23).minute(59)),u&&p&&!u.isAfter(p)&&(u=p.add(1,"minute")),o(I,u)},disabled:!C.checked,format:"HH:mm",style:{width:"100%"},allowClear:!1,inputReadOnly:!0,placeholder:""})}),a.jsx(B,{span:7,style:{paddingLeft:16},children:a.jsx("div",{style:{width:"100%",padding:"0 4px"},children:a.jsx(fr,{range:!0,min:0,max:1439,step:1,disabled:!C.checked,value:C.checked?[d(C.startTime),d(C.endTime)]:[0,0],onChange:([u,p])=>{if(!C.checked)return;p>1439&&(p=1439);let h=m(u),j=m(p);j.isAfter(h)||(j=h.add(1,"minute")),r(I,h),o(I,j)},marks:f,dotStyle:{display:"none"},activeDotStyle:{display:"none"},trackStyle:C.checked?[{backgroundColor:"#52c41a",height:8}]:[{backgroundColor:"transparent",height:8}],railStyle:{backgroundColor:"#eee",height:8},handleStyle:[{display:"none"},{display:"none"}]})})})]},C.day))})]})},ht={mac:"","user-name":"",password:"","vlan-id":1},ba=({open:e,onCancel:t,onOk:n,isDisabled:r,existingUsers:o=[]})=>{const{t:l}=pe(),[i]=E.useForm();return c.useEffect(()=>{e&&i.setFieldsValue(ht)},[e]),a.jsxs(Ze,{open:e,title:"Add User",onCancel:t,onFinish:async s=>{const{mac:d,"user-name":m,password:f}=s;if(o.some(I=>{var u;return((u=I.mac)==null?void 0:u.toLowerCase())===d.toLowerCase()&&I["user-name"]===m&&I.password===f})){D.error("User with the same MAC, username and password already exists.");return}n(s),i.resetFields()},initialValues:ht,form:i,modalClass:"ampcon-middle-modal",children:[a.jsx(E.Item,{label:"MAC",name:"mac",rules:[{validator:(s,d)=>fa(d)}],children:a.jsx(M,{disabled:r})}),a.jsx(E.Item,{label:"User Name",name:"user-name",rules:[{required:!0,message:l("form.required")}],children:a.jsx(M,{disabled:r})}),a.jsx(E.Item,{label:"Password",name:"password",rules:[{required:!0,message:l("form.required")},{min:8,max:63,message:l("form.min_max_string",{min:8,max:63})}],children:a.jsx(M.Password,{disabled:r})}),a.jsx(E.Item,{label:"VLAN ID",name:"vlan-id",rules:[{required:!0,message:l("form.required")},{type:"number",max:4094,message:"vlan-id must be less than 4095"},{type:"number",min:0,message:"vlan-id must be greater than -1"}],children:a.jsx(ge,{disabled:r})})]})},xa={name:"",description:"",mode:"External",authentication:{host:"***************",port:1812,secret:"YOUR_SECRET","mac-filter":!1},accounting:void 0,"dynamic-authorization":void 0,"nas-identifier":"","chargeable-user-id":!1,local:{users:[]}},va=e=>{if(!e)return{...xa};let t={},n="External";try{if(e.config_variables&&(t=JSON.parse(e.config_variables)),e.parameter){let r=e.parameter;if(typeof r=="string")try{r=JSON.parse(r)}catch{}r&&r.type&&(n=r.type)}}catch{}return{...t,name:e.name,description:e.description,mode:n}},Ga=({isDisabled:e=!1,resource:t,onClose:n,refresh:r,siteId:o,open:l=!1,disableMode:i=!1})=>{const{t:s}=pe(),[d]=E.useForm(),m=va(t),[f,C]=c.useState(m.mode),[I,u]=c.useState(!1),[p,h]=c.useState(!!(m&&m.accounting)),[j,S]=c.useState(!!(m&&m["dynamic-authorization"])),P=async g=>{var O,W;u(!0);const b=Ve(g);try{const z={type:g.mode||"External",auth_server_host:((O=g.authentication)==null?void 0:O.host)||"-",port:((W=g.authentication)==null?void 0:W.port)||"-"},{name:Ye,mode:Ne,description:ze,...me}=b,Ce=JSON.stringify(me);let V;if(t&&t.id){if(V=await ke({id:t.id,site_id:o,type:1,name:g.name,parameter:z,description:g.description,config_variables:Ce}),(V==null?void 0:V.status)!==200){D.error((V==null?void 0:V.info)||s("crud.error_update_obj",{obj:s("resources.configuration_resource")})),u(!1);return}D.success(s("crud.success_update_obj",{obj:s("resources.configuration_resource")}))}else{if(V=await Re({site_id:o,type:1,name:g.name,parameter:z,description:g.description,config_variables:Ce}),(V==null?void 0:V.status)!==200){D.error((V==null?void 0:V.info)||s("crud.error_create_obj",{obj:s("resources.configuration_resource")})),u(!1);return}D.success(s("crud.success_create_obj",{obj:s("resources.configuration_resource")}))}r&&r(),n&&n(!0)}catch{D.error(s("crud.error_create_obj",{obj:s("resources.configuration_resource")}))}finally{u(!1)}},R=f==="Local",y=R||e,[L,k]=c.useState(1),[_,G]=c.useState(10),w=E.useWatch(["local","users"],d)||[],[T,Z]=c.useState(!1),J=()=>Z(!0),U=g=>{const b={...g};b.mac&&(b.mac=b.mac.toLowerCase()),d.setFieldValue(["local","users"],[b,...w]),Z(!1),k(1)},x=g=>{const b=[...w];b.splice(g,1),d.setFieldValue(["local","users"],b),(L-1)*_>=b.length&&L>1&&k(L-1)},v=w.slice((L-1)*_,L*_),N=[{title:"MAC",dataIndex:"mac",key:"mac"},{title:"User Name",dataIndex:"user-name",key:"user-name"},{title:"Password",dataIndex:"password",key:"password"},{title:"VLAN ID",dataIndex:"vlan-id",key:"vlan-id"},{title:"Operation",key:"action",render:(g,b,O)=>a.jsx(X,{type:"text",onClick:()=>x((L-1)*_+O),children:"Delete"})}],$=()=>a.jsxs(a.Fragment,{children:[a.jsxs(F,{gutter:24,children:[a.jsx(B,{span:8,style:{display:"none"},children:a.jsx(E.Item,{name:["local","server-identity"],label:"Server Identity",initialValue:"uCentral",children:a.jsx(M,{disabled:e})})}),a.jsx(B,{span:24,style:{display:"none"},children:a.jsx(E.List,{name:["local","users"],children:g=>a.jsx(a.Fragment,{children:g.map(()=>null)})})})]}),a.jsx(F,{gutter:24,children:a.jsxs(B,{span:24,children:[a.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:24},children:a.jsx(X,{type:"primary",icon:a.jsx(ie,{component:wt}),onClick:J,disabled:e,children:"User"})}),a.jsx(jt,{columns:N,dataSource:v,pagination:{current:L,pageSize:_,total:w.length,showTotal:(g,b)=>`${b[0]}-${b[1]} of ${g} items`,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["5","10","20","50"],onChange:(g,b)=>{k(g),G(b)}},rowKey:(g,b)=>typeof b=="number"?b.toString():"",size:"middle",bordered:!0}),a.jsx(ba,{open:T,onCancel:()=>Z(!1),onOk:U,isDisabled:e,existingUsers:w})]})})]}),H=()=>a.jsxs(a.Fragment,{children:[a.jsxs(F,{gutter:24,children:[a.jsx(B,{span:12,children:a.jsx(E.Item,{name:["authentication","host"],label:"Authentication Host",rules:[{required:!0,message:s("form.required")},{validator:(g,b)=>Me(b)}],children:a.jsx(M,{disabled:y})})}),a.jsx(B,{span:12,children:a.jsx(E.Item,{name:["authentication","port"],label:"Authentication Port",rules:[{required:!0,message:s("form.required")},{type:"number",min:1,message:"radius.authentication.port must be a positive number"},{type:"number",max:65534,message:"radius.authentication.port must be less than 65535"}],children:a.jsx(ge,{disabled:y})})})]}),a.jsx(F,{gutter:24,children:a.jsx(B,{span:12,children:a.jsx(E.Item,{name:["authentication","secret"],label:"Authentication Secret",rules:[{required:!0,message:s("form.required")}],children:a.jsx(M.Password,{disabled:y})})})}),a.jsx(F,{gutter:24,children:a.jsx(B,{span:12,children:a.jsx(E.Item,{name:["authentication","mac-filter"],label:"MAC Filter",valuePropName:"checked",children:a.jsx(xe,{disabled:y})})})}),a.jsx("h3",{className:"header2",style:{marginBottom:0}}),a.jsx(F,{gutter:24,children:a.jsx(B,{span:12,children:a.jsx(E.Item,{label:"Enable Accounting",children:a.jsx(xe,{checked:p,disabled:y,onChange:g=>{h(g),g?d.setFieldsValue({accounting:{host:"***************",port:1813,secret:"YOUR_SECRET"}}):d.setFieldsValue({accounting:void 0})}})})})}),p&&a.jsxs(a.Fragment,{children:[a.jsxs(F,{gutter:24,children:[a.jsx(B,{span:12,children:a.jsx(E.Item,{name:["accounting","host"],label:"Accounting Host",rules:[{required:!0,message:s("form.required")},{validator:(g,b)=>Me(b)}],children:a.jsx(M,{disabled:y})})}),a.jsx(B,{span:12,children:a.jsx(E.Item,{name:["accounting","port"],label:"Accounting Port",rules:[{required:!0,message:s("form.required")},{type:"number",min:1,message:"accounting.port must be a positive number"},{type:"number",max:65534,message:"accounting.port must be less than 65535"}],children:a.jsx(ge,{disabled:y})})})]}),a.jsx(F,{gutter:24,children:a.jsx(B,{span:12,children:a.jsx(E.Item,{name:["accounting","secret"],label:"Accounting Secret",rules:[{required:!0,message:s("form.required")}],children:a.jsx(M.Password,{disabled:y})})})})]}),a.jsx("h3",{className:"header2",style:{marginBottom:0}}),a.jsx(F,{gutter:24,children:a.jsx(B,{span:12,children:a.jsx(E.Item,{label:"Enable Dynamic Auth",children:a.jsx(xe,{checked:j,disabled:y,onChange:g=>{S(g),g?d.setFieldsValue({"dynamic-authorization":{host:"***************",port:1814,secret:"YOUR_SECRET"}}):d.setFieldsValue({"dynamic-authorization":void 0})}})})})}),j&&a.jsxs(a.Fragment,{children:[a.jsxs(F,{gutter:24,children:[a.jsx(B,{span:12,children:a.jsx(E.Item,{name:["dynamic-authorization","host"],label:"Dynamic Auth Host",rules:[{required:!0,message:s("form.required")},{validator:(g,b)=>Me(b)}],children:a.jsx(M,{disabled:y})})}),a.jsx(B,{span:12,children:a.jsx(E.Item,{name:["dynamic-authorization","port"],label:"Dynamic Auth Port",rules:[{required:!0,message:s("form.required")},{type:"number",min:1,message:"dynamic-authorization.port must be a positive number"},{type:"number",max:65534,message:"dynamic-authorization.port must be less than 65535"}],children:a.jsx(ge,{disabled:y})})})]}),a.jsx(F,{gutter:24,children:a.jsx(B,{span:12,children:a.jsx(E.Item,{name:["dynamic-authorization","secret"],label:"Dynamic Auth Secret",rules:[{required:!0,message:s("form.required")}],children:a.jsx(M.Password,{disabled:y})})})})]}),a.jsx("h3",{className:"header2",style:{marginBottom:0}}),a.jsxs(F,{gutter:24,children:[a.jsx(B,{span:12,children:a.jsx(E.Item,{name:"nas-identifier",label:"NAS Identifier",children:a.jsx(M,{disabled:y})})}),a.jsx(B,{span:12,children:a.jsx(E.Item,{name:"chargeable-user-id",label:"Chargeable User ID",valuePropName:"checked",children:a.jsx(xe,{disabled:y})})})]})]});return a.jsxs(Ze,{open:l,title:t?"Edit SSID Radius Profile":"Create SSID Radius Profile",onCancel:()=>n(!1),onFinish:P,initialValues:m,form:d,onValuesChange:(g,b)=>{b.mode!==f&&C(b.mode)},children:[a.jsx(F,{gutter:24,children:a.jsx(B,{span:12,children:a.jsx(E.Item,{name:"name",label:s("common.name"),rules:[{required:!0,message:s("form.required")},{type:"string",max:32,message:s("form.max_length",{max:32})}],children:a.jsx(M,{disabled:e})})})}),a.jsx(F,{gutter:24,children:a.jsx(B,{span:12,children:a.jsx(E.Item,{name:"mode",label:"Mode",rules:[{required:!0,message:s("form.required")}],children:a.jsx(se,{disabled:e||i,options:[{label:"External",value:"External"},{label:"Local",value:"Local"}]})})})}),a.jsx(F,{gutter:24,children:a.jsx(B,{span:12,children:a.jsx(E.Item,{name:"description",label:s("common.description"),rules:[{max:128,message:s("form.max_length",{max:128})}],children:a.jsx(M.TextArea,{disabled:e,rows:2})})})}),R?$():H()]})},ft="/assets/click-B3Odzm4Q.htm",ya="/assets/radius-mIi39hed.htm",wa="/assets/radius-mIi39hed.htm",ja="data:text/html;base64,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",Sa="data:text/html;base64,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";async function Ea(e,t,n=!1){let r="click.html";t=t==null?void 0:t.toLowerCase(),t==="credentials"?r="credentials.html":t==="radius"&&(r="radius.html");try{const o=[{html:e,filename:r}];if(n){const[i,s]=await Promise.all([fetch(ja).then(d=>d.text()),fetch(Sa).then(d=>d.text())]);o.push({html:i,filename:"allow.html"},{html:s,filename:"connected.html"})}const l=await pr({data:o});if(l.status===200)return l.data;throw new Error(l.info||"Failed to convert HTML to Base64")}catch(o){throw console.error(o),o}}const oe={name:"",description:"",mode:"Click",backgroundColor:"#F0F8F9",logoBase64:"",welcomeMessage:"Welcome to use Wi-Fi",termsOfService:"",corporateInfo:"© 2025 FS.COM INC. All rights reserved"},q={welcomeMessage:31,termsOfService:200,corporateInfo:50,logo:10},Fa=({isDisabled:e=!1,resource:t,onClose:n,refresh:r,siteId:o,parameterMode:l,open:i})=>{const{t:s}=pe(),[d,m]=c.useState(""),[f,C]=c.useState(""),[I,u]=c.useState(oe.mode),[p,h]=c.useState(oe.welcomeMessage),[j,S]=c.useState(oe.termsOfService),[P,R]=c.useState(oe.corporateInfo),[y,L]=c.useState(oe.backgroundColor),[k,_]=c.useState(oe.logoBase64),G=c.useRef(null),[w,T]=c.useState(!1),[Z]=E.useForm();c.useEffect(()=>{l&&l!==I&&(u(l),Z.setFieldsValue({mode:l}))},[l]);const J=g=>{switch(g){case"Click":return ft;case"Radius":return wa;case"Credentials":return ya;default:return ft}},U=(g,b)=>{let O=g;return b.backgroundColor&&(O=O.replace(/background-color:\s*(?:unset|#[a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)\s*;?/i,`background-color: ${b.backgroundColor};`)),b.logoBase64&&(O=O.replace(/<img id="logo"[^>]*src="[^"]*"[^>]*>/,`<img id="logo" src="${b.logoBase64}" alt="logo" />`)),O=O.replace(/<p class="lead" id="title">.*?<\/p>/,`<p class="lead" id="title">${b.welcomeMessage}</p>`),O=O.replace(/<div[^>]*id="readmeTxt"[^>]*>([\s\S]*?)<\/div>/,`<div id="readmeTxt">${b.termsOfService}</div>`),O=O.replace(/<div[^>]*id="corporate-info"[^>]*>([\s\S]*?)<\/div>/,`<div id="corporate-info">${b.corporateInfo}</div>`),O},x=c.useCallback($e(g=>h(g),400),[]),v=c.useCallback($e(g=>S(g),400),[]),N=c.useCallback($e(g=>R(g),400),[]);c.useEffect(()=>{const g=J(I);fetch(g).then(b=>b.text()).then(b=>{C(b);const O=Z.getFieldsValue();m(U(b,{...O,backgroundColor:y,logoBase64:k,welcomeMessage:p,termsOfService:j,corporateInfo:P}))}).catch(b=>console.error("Failed to load defaultHtml:",b))},[I]),c.useEffect(()=>{if(!f)return;const g=Z.getFieldsValue();m(U(f,{...g,backgroundColor:y,logoBase64:k,welcomeMessage:p,termsOfService:j,corporateInfo:P}))},[p,j,P,f,y,k]),c.useEffect(()=>{if(t){const g=t.parameter||{};u(g.mode),L(g.background),_(g.logo),Z.setFieldsValue({name:t.name,description:t.description,mode:g.mode,backgroundColor:g.background,logoBase64:g.logo,welcomeMessage:g.welcome,termsOfService:g.terms_of_service,corporateInfo:g.copyright}),h(g.welcome),S(g.terms_of_service),R(g.copyright)}},[t]),c.useEffect(()=>{G.current&&d&&(G.current.srcdoc=d,setTimeout(()=>{var g;try{const b=(g=G.current)==null?void 0:g.contentDocument,O=b==null?void 0:b.querySelector("form");O&&(O.onsubmit=W=>(W.preventDefault(),!1))}catch{}},50))},[d]);const $=(g,b)=>["image/png","image/jpeg","image/jpg","image/gif"].includes(g.type)?g.size>b?(D.error(`Choose a picture that is no more than ${b/1024}KB.`),!1):!0:(D.error("Only PNG, JPG, JPEG, or GIF file types are supported."),!1),H=async g=>{T(!0);try{let b="";try{b=await Ea(d,g.mode)}catch{D.error("Failed to convert HTML"),T(!1);return}const O={mode:g.mode,background:g.backgroundColor,logo:g.logoBase64||"",welcome:g.welcomeMessage,terms_of_service:g.termsOfService,copyright:g.corporateInfo};let W;if(t){if(W=await ke({id:t.id,site_id:o,type:3,name:g.name,description:g.description,parameter:O,config_variables:b}),(W==null?void 0:W.status)!==200){D.error((W==null?void 0:W.info)||"Failed to update resource"),T(!1);return}D.success("Resource updated successfully")}else{if(W=await Re({site_id:o,type:3,name:g.name,description:g.description,parameter:O,config_variables:b}),(W==null?void 0:W.status)!==200){D.error((W==null?void 0:W.info)||"Failed to create resource"),T(!1);return}D.success("Resource created successfully")}r&&r(),n&&n(!0)}catch{D.error("Failed to create resource")}finally{T(!1)}};return a.jsxs(Ze,{open:i,title:t?"Edit Portal Webroot Profile":"Create Portal Webroot Profile",onCancel:()=>n(!1),onFinish:H,initialValues:oe,form:Z,children:[a.jsx(F,{gutter:24,className:"webroot-row",children:a.jsx(B,{span:12,children:a.jsx(E.Item,{name:"name",label:s("common.name"),rules:[{required:!0,message:s("form.required")},{type:"string",max:32,message:s("form.max_length",{max:32})}],children:a.jsx(M,{disabled:e})})})}),a.jsx(F,{gutter:24,className:"webroot-row",children:a.jsx(B,{span:12,children:a.jsx(E.Item,{name:"mode",label:"Mode",rules:[{required:!0,message:s("form.required")}],children:a.jsxs(se,{disabled:e||!!l||t&&t.usage_count>0,value:I,onChange:g=>{u(g),Z.setFieldsValue({mode:g})},children:[a.jsx(se.Option,{value:"Click",children:"Click"}),a.jsx(se.Option,{value:"Radius",children:"Radius"}),a.jsx(se.Option,{value:"Credentials",children:"Credentials"})]})})})}),a.jsx(F,{gutter:24,className:"webroot-row",children:a.jsx(B,{span:12,children:a.jsx(E.Item,{name:"description",label:s("common.description"),rules:[{max:128,message:s("form.max_length",{max:128})}],children:a.jsx(M.TextArea,{disabled:e,rows:2})})})}),a.jsxs(F,{gutter:24,style:{borderRadius:"16px",border:"1px solid #E7E7E7",padding:"16px",margin:0,marginBottom:68},children:[a.jsxs(B,{span:11,className:"webroot-form",children:[a.jsx("h4",{style:{marginTop:0,fontSize:"16px"},children:"Configuration"}),a.jsx(F,{gutter:24,children:a.jsxs(B,{span:24,style:{display:"flex"},children:[a.jsx(E.Item,{name:"backgroundColor",label:"Background",rules:[{required:!0,message:"Background is required."}],children:a.jsx(M,{type:"text",disabled:!0,value:y,readOnly:!0})}),a.jsx("div",{style:{marginLeft:16,position:"relative"},children:a.jsx(Xe,{value:y,disabled:e,onChange:g=>{const b=typeof g=="string"?g:g!=null&&g.toHexString?g.toHexString():String(g);L(b),Z.setFieldsValue({backgroundColor:b})},children:a.jsx(X,{className:"upload-button",disabled:e,children:"Color"})})})]})}),a.jsx(F,{gutter:24,children:a.jsxs(B,{span:24,style:{display:"flex"},children:[a.jsx(E.Item,{name:"logoBase64",label:"Upload Logo",children:a.jsx(Ir,{title:`PNG, JPG, JPEG and GIF, Must be less than ${q.logo}kB`,children:a.jsx(M,{type:"text",disabled:!0,readOnly:!0,value:k,placeholder:`PNG, JPG, JPEG and GIF, Must be less than ${q.logo}kB`})})}),a.jsx("div",{style:{marginLeft:16},children:a.jsx(Be,{accept:"image/*",showUploadList:!1,disabled:e,style:{marginLeft:16},beforeUpload:g=>{if(!$(g,q.logo*1024))return Be.LIST_IGNORE;const b=new FileReader;return b.onload=()=>{_(b.result),Z.setFieldsValue({logoBase64:b.result})},b.readAsDataURL(g),Be.LIST_IGNORE},children:a.jsx(X,{className:"upload-button",disabled:e,children:"Upload Logo"})})})]})}),a.jsx(F,{gutter:24,children:a.jsx(B,{span:18,children:a.jsx(E.Item,{name:"welcomeMessage",label:"Welcome Message",rules:[{max:q.welcomeMessage,message:`This length should be no more than ${q.welcomeMessage}.`}],children:a.jsx(M,{disabled:e,onChange:g=>x(g.target.value)})})})}),a.jsx(F,{gutter:24,children:a.jsx(B,{span:18,children:a.jsx(E.Item,{name:"termsOfService",label:"Terms of Service",rules:[{max:q.termsOfService,message:`This length should be no more than ${q.termsOfService}.`}],children:a.jsx(M.TextArea,{disabled:e,rows:5,onChange:g=>v(g.target.value)})})})}),a.jsx(F,{gutter:24,children:a.jsx(B,{span:18,children:a.jsx(E.Item,{name:"corporateInfo",label:"Copyright",rules:[{max:q.corporateInfo,message:`This length should be no more than ${q.corporateInfo}.`}],className:"form-item-uniform",children:a.jsx(M.TextArea,{disabled:e,rows:5,onChange:g=>N(g.target.value)})})})})]}),a.jsxs(B,{span:13,style:{display:"flex",flexDirection:"column"},children:[a.jsx("h4",{style:{marginTop:0,marginLeft:18,fontSize:"16px"},children:"Preview"}),a.jsx("div",{className:"previewBox",children:a.jsx("iframe",{ref:G,className:"previewContent"})})]})]})]})},_a=({editingProfile:e,onClose:t,siteId:n})=>{const{t:r}=pe(),[o,l]=c.useState({name:"",description:""}),[i,s]=c.useState(!1),[d,m]=c.useState(!1),[f,C]=c.useState({mac:"",key:""}),[I,u]=c.useState(!1),[p,h]=c.useState(null),[j,S]=c.useState({field:"",order:""}),[P,R]=c.useState(()=>{if(!(e!=null&&e.parameter))return[];try{let x=e.parameter;if(typeof x=="string"){if(x.startsWith("(")&&x.endsWith("}")){const $=`{"${x.slice(1,-1).replace(/::/g,'":"').replace(/,/g,'","').replace(/:/g,'":"')}"}`,H=JSON.parse($);return Object.keys(H).filter(g=>g.startsWith("entry_")).map(g=>({mac:H[g].mac||"",key:H[g].key||"",vlan_id:H[g].vlan_id||void 0}))}x=x.replace(/\\"/g,'"'),x.startsWith('"')&&x.endsWith('"')&&(x=x.slice(1,-1));const v=JSON.parse(x);return Object.keys(v).filter(N=>N.startsWith("entry_")).map(N=>({mac:v[N].mac||"",key:v[N].key||"",vlan_id:v[N].vlan_id||void 0}))}return[]}catch{return[]}}),y=()=>{C({mac:"",key:"",vlan_id:1813}),m(!0)},L=()=>{m(!1)},k=()=>{var N;if(!f.key){D.error(r("Key is required"));return}if(f.key.length<8||f.key.length>63){D.error(r("Value needs to be of a length between 8 (inclusive) and 63 (inclusive)"));return}if(f.vlan_id===void 0||f.vlan_id===null||f.vlan_id<=0){D.error("VLAN-ID must be a positive number");return}if(f.vlan_id>=4096){D.error(r("vlan-id must be less than 4097"));return}let x="";f.mac&&(x=((N=f.mac.toLowerCase().replace(/[^a-f0-9]/g,"").match(/.{1,2}/g))==null?void 0:N.join(":"))||f.mac);const v={...f,mac:x};if(v.mac){if(P.find(H=>H.mac===v.mac&&H.key===v.key)){D.error(r("The key already exists"));return}}else if(P.find(H=>!H.mac&&H.key===v.key)){D.error(r("The user already exists"));return}R($=>[...$,v]),m(!1)},_=x=>{if(!x)return Promise.resolve();const v=x.toLowerCase();return/^([0-9a-f]{2}:){5}([0-9a-f]{2})$/.test(v)?Promise.resolve():Promise.reject(r('The legal Mac format can only contain lowercase letters and numbers, with a delimiter of ":". Please use the format: 00:00:5e:00:53:af'))},G=x=>{h(x),u(!0)},w=()=>{if(p!==null)try{const x=[...P];x.splice(p,1),R(x),D.success(r("Successfully Deleted"))}catch{D.error(r("Delete failed"))}u(!1)},[T,Z]=c.useState({current:1,pageSize:10,total:P.length});c.useEffect(()=>{Z(x=>({...x,total:P.length}))},[P]);const J=(x,v,N)=>{if(Z($=>({...$,current:x.current,pageSize:x.pageSize})),!Array.isArray(N)&&(N!=null&&N.order)){const{field:$,order:H}=N,g=[...P].sort((b,O)=>{const W=b[$],z=O[$];return W===void 0||z===void 0?0:typeof W=="number"&&typeof z=="number"?H==="ascend"?W-z:z-W:H==="ascend"?String(W).localeCompare(String(z)):String(z).localeCompare(String(W))});R(g)}};c.useEffect(()=>{if(e){l({name:e.name||"",description:e.description||""});try{const x=e.parameter||{},v=Object.keys(x).filter(N=>N.startsWith("entry_")).map(N=>{var $,H,g;return{mac:(($=x[N])==null?void 0:$.mac)||"",key:((H=x[N])==null?void 0:H.key)||"",vlan_id:((g=x[N])==null?void 0:g.vlan_id)||void 0}});R(v)}catch{R([])}}},[e]);const U=[{title:r("Mac"),dataIndex:"mac",key:"mac",sorter:!0,render:x=>a.jsx("div",{children:x})},{title:r("Key"),dataIndex:"key",key:"key",sorter:!0,render:x=>a.jsx("div",{children:x})},{title:r("VLAN-ID"),dataIndex:"vlan_id",key:"vlan_id",sorter:!0,render:x=>a.jsx("div",{children:x})},{title:"Operation",key:"actions",align:"left",render:(x,v,N)=>a.jsx(Ar,{style:{display:"flex",justifyContent:"flex-start",width:"100%",marginLeft:-12},children:a.jsx(X,{type:"link",onClick:()=>G(N),children:"Delete"})})}];return a.jsxs(a.Fragment,{children:[a.jsx("h2",{style:{margin:"0 0 24px 0",color:"rgba(0, 0, 0, 0.85)",fontWeight:500,fontSize:20}}),a.jsx(vr,{initialValues:o,enableReinitialize:!0,validateOnChange:!0,validateOnBlur:!0,validate:x=>{const v={};return x.name?x.name.length>32&&(v.name=r("Profile name cannot exceed 32 characters")):v.name=r("Please enter profile name"),x.description&&x.description.length>128&&(v.description=r("form.max_length",{max:128})),v},onSubmit:async x=>{if(x.name.length>32){D.error(r("Profile name cannot exceed 32 characters"));return}s(!0);try{const v=P.reduce((g,b,O)=>(g[`entry_${O}`]={mac:b.mac,key:b.key,vlan_id:b.vlan_id},g),{}),N=P.map(g=>({...g.mac?{mac:g.mac}:{},"vlan-id":g.vlan_id,key:g.key})),$={site_id:n,type:2,name:x.name,parameter:v,description:x.description,config_variables:JSON.stringify(N)};let H;if(e!=null&&e.id?H=await ke({id:e.id,...$}):H=await Re($),(H==null?void 0:H.status)!==200){D.error((H==null?void 0:H.info)||r("Operation failed"));return}D.success(r(e?"Profile updated successfully":"Profile created successfully")),t(!0)}catch(v){D.error(r("Operation failed: ")+v.message)}finally{s(!1)}},children:({values:x,errors:v,touched:N,handleSubmit:$,handleBlur:H,handleChange:g})=>a.jsxs(E,{onFinish:$,style:{flex:1,display:"flex",flexDirection:"column"},children:[a.jsxs("div",{style:{marginBottom:24,width:"100%",paddingLeft:0},children:[a.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:16,width:"100%"},children:[a.jsxs("span",{style:{fontWeight:500,minWidth:"140px",marginRight:-30},children:[r("Name")," ",a.jsx("span",{style:{color:"red"},children:"*"})]}),a.jsxs("div",{style:{width:"280px"},children:[a.jsx(M,{name:"name",value:x.name,onChange:g,onBlur:H,placeholder:r("Enter name"),style:{width:"100%"},status:N.name&&v.name?"error":""}),N.name&&v.name&&a.jsx("div",{style:{color:"#ff4d4f",marginTop:4},children:v.name})]})]}),a.jsxs("div",{style:{display:"flex",alignItems:"flex-start",width:"100%",marginBottom:16},children:[a.jsx("span",{style:{fontWeight:500,minWidth:"140px",marginRight:-30,paddingTop:8},children:r("Description")}),a.jsxs("div",{style:{width:"280px",marginBottom:-8},children:[a.jsx(M.TextArea,{name:"description",value:x.description,onChange:g,placeholder:r("Enter description"),rows:2}),v.description&&a.jsx("div",{style:{color:"#ff4d4f",marginTop:4},children:v.description})]})]})]}),a.jsx("div",{children:a.jsxs(X,{type:"primary",onClick:y,style:{marginBottom:24,marginTop:2},children:[a.jsx(ie,{component:wt}),r("Create")]})}),a.jsx("div",{style:{flex:1,marginBottom:24},children:a.jsx(jt,{columns:U,dataSource:P,rowKey:(b,O)=>O.toString(),pagination:{...T,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50"],showTotal:b=>`Total ${b} items`,position:["bottomRight"]},onChange:J,scroll:{x:"max-content",y:300},bordered:!0})}),a.jsx(de,{style:{margin:"20px 0px 16px -24px",width:"calc(100% + 48px)"}}),a.jsxs("div",{style:{display:"flex",justifyContent:"flex-end",gap:16},children:[a.jsx(X,{onClick:()=>t(!1),disabled:i,style:{width:100},children:r("Cancel")}),a.jsx(X,{type:"primary",htmlType:"submit",loading:i,style:{width:100},children:r("Apply")})]})]})}),a.jsxs(Fe,{title:a.jsx("div",{style:{fontFamily:"Lato, sans-serif",fontWeight:700,fontSize:"20px",color:"#212519",lineHeight:"24px",textAlign:"left"},children:r("Create")}),visible:d,onCancel:L,footer:[a.jsx(X,{onClick:L,style:{width:100},children:r("Cancel")},"back"),a.jsx(X,{type:"primary",onClick:k,style:{width:100},children:r("Apply")},"submit")],destroyOnClose:!0,width:680,style:{height:"450px",borderRadius:"8px"},children:[a.jsx(de,{style:{margin:"0px 0px 16px -24px",width:"calc(100% + 48px)"}}),a.jsxs(E,{layout:"horizontal",labelCol:{span:6},wrapperCol:{span:18},initialValues:f,onValuesChange:(x,v)=>C(v),validateTrigger:["onChange","onBlur"],onKeyDown:x=>{x.key==="Enter"&&x.preventDefault()},children:[a.jsx(E.Item,{name:"mac",label:a.jsx("span",{children:r("Mac")}),rules:[{validator:(x,v)=>_(v)}],validateTrigger:["onChange","onBlur"],labelAlign:"left",className:"left-error",children:a.jsx(M,{autoComplete:"off",style:{width:"280px",marginLeft:-70}})}),a.jsx(E.Item,{name:"key",label:a.jsx("span",{children:r("Key")}),rules:[{required:!0,message:r("Required")},{validator:(x,v)=>v&&(v.length<8||v.length>63)?Promise.reject(r("Value needs to be of a length between 8 (inclusive) and 63 (inclusive)")):Promise.resolve(),validateTrigger:["onChange","onBlur"]}],labelAlign:"left",validateTrigger:["onChange","onBlur"],className:"left-error",children:a.jsx(M,{autoComplete:"new-password",style:{width:"280px",marginLeft:-70}})}),a.jsx(E.Item,{name:"vlan_id",label:a.jsx("span",{children:r("VLAN-ID")}),rules:[{required:!0,message:r("VLAN-ID must be a positive number")},{validator:(x,v)=>v<=0?Promise.reject(r("vlan-id must be greater than 0")):v>=4096?Promise.reject(r("VLAN-ID must be less than 4097")):Promise.resolve(),validateTrigger:["onChange","onBlur"]}],labelAlign:"left",validateTrigger:["onChange","onBlur"],className:"left-error",children:a.jsx(ge,{value:f.vlan_id,style:{width:"140px",marginLeft:-70},parser:x=>{const v=parseInt(x||"0",10);return isNaN(v)?"":v},formatter:x=>x?x.toString():""})})]}),a.jsx(de,{style:{margin:"0px 0px 16px -24px",width:"calc(100% + 48px)"}})]}),a.jsx(Fe,{title:a.jsxs("span",{style:{fontWeight:500},children:[a.jsx(br,{style:{color:"#faad14",marginRight:8}}),r("Note")]}),visible:I,onCancel:()=>{u(!1)},footer:[a.jsx(X,{onClick:()=>{u(!1)},children:r("No")},"cancel"),a.jsx(X,{type:"primary",onClick:w,children:r("Yes")},"confirm")],width:416,children:a.jsx("p",{style:{margin:"16px 0"},children:r("Are you sure you want to delete?")})})]})};export{_a as F,Ga as R,Ma as T,Na as V,Fa as W,Wa as a,Me as b,fa as c,ue as d,Da as e,Ve as f,Ha as g,Ta as h,Oa as s,$a as u,Ba as v};
