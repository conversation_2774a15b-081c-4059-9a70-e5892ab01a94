import{d8 as C,r as d}from"./index-CHCmiRmn.js";import{c as v,b}from"./index-LkKwRvEU.js";function k(i){const{value:u,defaultValue:a,onChange:t,shouldUpdate:o=(n,h)=>n!==h}=i,r=C(t),s=C(o),[l,f]=d.useState(a),c=u!==void 0,e=c?u:l,m=C(n=>{const p=typeof n=="function"?n(e):n;s(e,p)&&(c||f(p),r(p))},[c,r,e,s]);return[e,m]}const S=({name:i})=>{const{setFieldValue:u}=v(),[{value:a},{touched:t,error:o},{setValue:r,setTouched:s}]=b(i),l=d.useCallback(e=>{r(e,!0),setTimeout(()=>{s(!0,!1)},200)},[]),f=d.useCallback(()=>{s(!0)},[]);return d.useMemo(()=>({value:a,touched:t,error:o,isError:o!==void 0&&t,setFieldValue:u,onChange:l,onBlur:f}),[a,t,o,l])};export{S as a,k as u};
