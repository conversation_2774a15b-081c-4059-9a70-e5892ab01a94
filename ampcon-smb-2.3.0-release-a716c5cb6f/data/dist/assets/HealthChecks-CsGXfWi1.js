import{u as n,G as o,y as u,a as l}from"./index-CHCmiRmn.js";const d=(e,t)=>async()=>l.get(`device/${t}/healthchecks?newest=true&limit=${e}`).then(s=>s.data),g=({serialNumber:e,limit:t,onError:s})=>n(["healthchecks",e,{limit:t}],d(t,e),{keepPreviousData:!0,enabled:e!==void 0&&e!=="",staleTime:3e4,onError:s}),v=(e,t,s,a,h)=>l.get(`device/${e}/healthchecks?startDate=${t}&endDate=${s}&limit=${a}&offset=${h}`).then(c=>c.data),k=(e,t,s)=>async()=>{let a=0;const h=100;let c=[],i;do i=await v(e,t,s,h,a),c=c.concat(i.values),a+=h;while(i.values.length===h);return{values:c,serialNumber:i.serialNumber}},m=({serialNumber:e,start:t,end:s,onError:a})=>n(["healthchecks",e,{start:t,end:s}],k(e,t,s),{enabled:e!==void 0&&e!==""&&t!==void 0&&s!==void 0,staleTime:1e3*60,onError:a}),r=async({serialNumber:e,endDate:t})=>l.delete(`device/${e}/healthchecks?endDate=${t}`),C=()=>{const e=o();return u(r,{onSuccess:()=>{e.invalidateQueries(["healthchecks"])}})},y=e=>l.get(`devices?health=true&lowLimit=${e.queryKey[2].lowerLimit}&highLimit=${e.queryKey[2].upperLimit}`).then(t=>t.data.serialNumbers),H=({lowerLimit:e,upperLimit:t})=>n(["devices","health",{lowerLimit:e,upperLimit:t}],y);export{C as a,g as b,m as c,H as u};
