import{r as u,l as m,m as E}from"./index-CHCmiRmn.js";function l(r){return r.sort((e,t)=>{const n=e.compareDocumentPosition(t);if(n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY)return-1;if(n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS)return 1;if(n&Node.DOCUMENT_POSITION_DISCONNECTED||n&Node.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC)throw Error("Cannot sort the given nodes.");return 0})}const O=r=>typeof r=="object"&&"nodeType"in r&&r.nodeType===Node.ELEMENT_NODE;function f(r,e,t){let n=r+1;return t&&n>=e&&(n=0),n}function x(r,e,t){let n=r-1;return t&&n<0&&(n=e),n}const o=typeof window<"u"?u.useLayoutEffect:u.useEffect,I=r=>r;var C=Object.defineProperty,g=(r,e,t)=>e in r?C(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,s=(r,e,t)=>(g(r,typeof e!="symbol"?e+"":e,t),t);class D{constructor(){s(this,"descendants",new Map),s(this,"register",e=>{if(e!=null)return O(e)?this.registerNode(e):t=>{this.registerNode(t,e)}}),s(this,"unregister",e=>{this.descendants.delete(e);const t=l(Array.from(this.descendants.keys()));this.assignIndex(t)}),s(this,"destroy",()=>{this.descendants.clear()}),s(this,"assignIndex",e=>{this.descendants.forEach(t=>{const n=e.indexOf(t.node);t.index=n,t.node.dataset.index=t.index.toString()})}),s(this,"count",()=>this.descendants.size),s(this,"enabledCount",()=>this.enabledValues().length),s(this,"values",()=>Array.from(this.descendants.values()).sort((t,n)=>t.index-n.index)),s(this,"enabledValues",()=>this.values().filter(e=>!e.disabled)),s(this,"item",e=>{if(this.count()!==0)return this.values()[e]}),s(this,"enabledItem",e=>{if(this.enabledCount()!==0)return this.enabledValues()[e]}),s(this,"first",()=>this.item(0)),s(this,"firstEnabled",()=>this.enabledItem(0)),s(this,"last",()=>this.item(this.descendants.size-1)),s(this,"lastEnabled",()=>{const e=this.enabledValues().length-1;return this.enabledItem(e)}),s(this,"indexOf",e=>{var t;return e?((t=this.descendants.get(e))==null?void 0:t.index)??-1:-1}),s(this,"enabledIndexOf",e=>e==null?-1:this.enabledValues().findIndex(t=>t.node.isSameNode(e))),s(this,"next",(e,t=!0)=>{const n=f(e,this.count(),t);return this.item(n)}),s(this,"nextEnabled",(e,t=!0)=>{const n=this.item(e);if(!n)return;const i=this.enabledIndexOf(n.node),d=f(i,this.enabledCount(),t);return this.enabledItem(d)}),s(this,"prev",(e,t=!0)=>{const n=x(e,this.count()-1,t);return this.item(n)}),s(this,"prevEnabled",(e,t=!0)=>{const n=this.item(e);if(!n)return;const i=this.enabledIndexOf(n.node),d=x(i,this.enabledCount()-1,t);return this.enabledItem(d)}),s(this,"registerNode",(e,t)=>{if(!e||this.descendants.has(e))return;const n=Array.from(this.descendants.keys()).concat(e),i=l(n);t!=null&&t.disabled&&(t.disabled=!!t.disabled);const d={node:e,index:-1,...t};this.descendants.set(e,d),this.assignIndex(i)})}}function T(){const[r,e]=m({name:"DescendantsProvider",errorMessage:"useDescendantsContext must be used within DescendantsProvider"});return[r,e,()=>{const i=u.useRef(new D);return o(()=>()=>i.current.destroy()),i.current},i=>{const d=e(),[h,N]=u.useState(-1),a=u.useRef(null);o(()=>()=>{a.current&&d.unregister(a.current)},[]),o(()=>{if(!a.current)return;const c=Number(a.current.dataset.index);h!=c&&!Number.isNaN(c)&&N(c)});const b=I(i?d.register(i):d.register);return{descendants:d,index:h,enabledIndex:d.enabledIndexOf(a.current),register:E(b,a)}}]}export{T as c};
