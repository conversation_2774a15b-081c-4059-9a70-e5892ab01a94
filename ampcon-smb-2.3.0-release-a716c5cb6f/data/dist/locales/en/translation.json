{"account": {"account": "Account", "activating_google_authenticator": "Activating Google Authenticator", "activating_sms_mfa": "Phone Number Validation", "avatar": "Avatar", "error_fetching_qr": "Error fetching QR code: {{e}}", "error_phone_verif": "Error with your validation code, please try again.", "google_authenticator": "Google Authenticator", "google_authenticator_intro": "To use Google Authenticator as your account's double-authentication method, you first need to install the app on your iOS or Android device", "google_authenticator_ready": "Once you have the app ready to use, you can proceed", "google_authenticator_scan_qr_code_explanation": "Scan the following QR Code using the \"Scan a QR Code\" in the Google Authenticator App", "google_authenticator_scanned_qr_code": "Once the QR code has been successfully scanned on your phone, you can proceed to the next step", "google_authenticator_success_explanation": "You have now successfully setup Google Authenticator with your account. Do not forget to save your changes to confirm!", "google_authenticator_type_code": "Please enter the 6 digit code from your Google Authenticator app below", "google_authenticator_wait_for_code": "Wait for the next code (not {{old}})", "google_authenticator_wrong_code": "Invalid Code! Please try again, or wait for the next code to be generated in the Google Authenticator app", "mfa": "Multi-Factor Authentication", "phone": "Phone", "phone_number": "Phone Number", "phone_number_add_introduction": "Please enter the phone number you would like to use to secure your account on login", "phone_required": "To activate SMS verification, you need to enter a phone number", "phone_validation_success_explanation": "Phone number successfully verified! Click 'Save' to add this phone number to your account", "proceed_to_activation": "Start Activation Process", "resend": "Resend", "sms": "SMS", "success_phone_verif": "Phone number successfully verified! You can now save your profile", "title": "My Account", "verify_phone_instructions": "You should receive a code on your phone number in the next few seconds. Please enter it below to verify your phone number", "verify_phone_number": "Verify your phone number"}, "analytics": {"ack_signal": "ACK Signal", "active": "Active", "airtime": "Airtime", "analyze_sub_venues": "Monitor Sub Venues", "associations": "Associations", "associations_explanation": "Total associations", "average_health": "Overall Health", "average_health_explanation": "Average sanity of all connected devices providing the health check information", "average_memory": "Used Memory", "average_memory_explanation": "Average percentage of used memory", "average_uptime": "Average Uptime", "average_uptime_explanation": "Average device uptime (DD:HH:MM:SS)", "band": "Band", "bandwidth": "Bandwidth", "board": "Analytics Collection", "busy": "Busy", "channel": "Channel", "client_lifecycle": "Client Lifecycle", "client_lifecycle_one": "{{count}} Client Lifecycle", "client_lifecycle_other": "{{count}} Client Lifecycles", "connected": "Connected", "connection_explanation": "{{connectedCount}} connected, {{disconnectedCount}} not connected", "connection_percentage": "{{count}}% connected", "connection_percentage_explanation": "Percentage of all devices under this venue which are connected ({{connectedCount}} connected, {{disconnectedCount}} not connected)", "create_board": "Start Monitoring", "dashboard": "Dashboard", "delta": "Delta", "device_types": "Types", "device_types_explanation": "Device types of all avaialable devices", "disconnected": "Disconnected", "firmware": "Firmware", "health": "Health", "inactive": "Inactive", "interval": "Interval", "last_connected": "Last Connected", "last_connected_tooltip": "Last time this device was connected to the controller. This can be used to estimate when a device disconnected", "last_connection": "Last Connection", "last_contact": "Last Contact", "last_disconnection": "Last Disconnection", "last_firmware_explanation": "Most common firmware running on the devices analyzed", "last_health": "Last Health", "last_ping": "Last Ping", "live_view": "Live View", "live_view_explanation_five": "You can also click on any of the circles to zoom-in", "live_view_explanation_four": "You can hover over any of the objects with your mouse to see detailed information ", "live_view_explanation_one": "The 'Live View' graph is a visual representation of your venue. ", "live_view_explanation_three": "Venue -> AP -> Radio -> SSID -> UEs", "live_view_explanation_two": "From outside to inside: ", "live_view_help": "Live View Help", "memory": "Memory", "memory_used": "Memory Used", "missing_board": "Analytics monitoring on this venue is no longer active. Click here to restart monitoring", "mode": "Mode", "monitoring": "Monitoring", "no_board": "No Monitoring", "no_board_description": "You are not monitoring this Venue at the moment, click here to start", "noise": "Noise", "packets": "Packets", "radio": "Radio", "raw_analytics_data": "Raw Analytics Data", "raw_data": "Raw Data", "receive": "Receive", "retention": "Retention", "retries": "Retries", "search_serials": "Search Serials", "stop_monitoring": "Stop Monitoring", "stop_monitoring_success": "Stopped Monitoring Venue!", "stop_monitoring_warning": "Are you sure? This will erase all recorded monitoring data for this venue", "temperature": "Temperature", "title": "Analytics", "total_data": "Total Data", "total_devices_explanation": "All devices under this venue ({{connectedCount}} connected, {{disconnectedCount}} not connected)", "total_devices_one": "{{count}} device", "total_devices_other": "{{count}} devices", "uptime": "Uptime"}, "batch": {"batches": "Batches", "cannot_edit_macs": "Because jobs have already been run on this batch, you cannot edit its MAC addresses", "change_warning": "WARNING: you have updated either the model or manufacturer. We highly suggest updating your certificates to have them stay consistent with this batch by choosing the \"Save and Update Certs\" option", "create": "Create Certificates", "create_certificates": "Create Certificates", "create_certificates_explanation": "Are you sure you want to create this batch's {{nbCerts}} certificates?", "create_certificates_title": "Create {{name}}'s Certificates", "delete_explanation": "Are you sure you want to delete this batch? This will revoke all its {{nbCerts}} certificates and delete them. This operation is not revertible", "delete_title": "Delete Batch {{name}}", "duplicate_in_file": "Duplicate MAC found in row {{firstRow}} and {{secondRow}}: {{mac}}", "emails_to_notify": "Emails to notify when this task is completed", "error_push": "Error starting push changes job: {{e}}", "general_error_treating_file": "General error while treating file: please make sure it is in .CSV format, contains only one column which has no header.", "invalid_mac": "Invalid MAC on row {{row}}: {{mac}}", "mac_count_title": "{{nb}} MACs are currently part of this batch", "nb_macs": "{{nb}} MACs", "need_devices": "You need to have at least one certificate to create!", "parsing_error": "Parsing error on row {{row}}: {{e}}", "phones_to_notify": "Phone numbers to notify when the task is completed", "push_changes": "Push Changes", "push_changes_explanation": "Are you sure you want to push the batch information to all of this batche's certificates? ", "revoke_explanation": "Are you sure you want to revoke this batch? This will revoke all its {{nbCerts}} certificates. This operation is not revertible", "revoke_title": "<PERSON>oke Batch {{name}}", "save_and_change": "Save and Update Certs", "success_push": "Successfully started push changes job! Job number: {{job}}", "title": "<PERSON><PERSON>"}, "certificates": {"certificate": "Certificate", "common_names_explanation": "Need a .CSV file of only one unnamed column containing 12 HEX digits device MACs.", "device": "<PERSON><PERSON>", "device_macs": "Device MACs", "domain_name": "Domain Name", "error_fetching": "Error fetching certificates: {{e}}", "error_revoke": "Error while trying to revoke certificate: {{e}}", "expires_on": "Expires On", "filename": "Filename", "invalid_domain": "Accepted formats are: domain.top_level_domain or sub_domain.domain._top_level_domain", "invalid_mac": "Needs to be 12 HEX characters", "invalid_redirector": "Accepted formats are: example.com, example.com:16000", "mac_address": "MAC Address", "macs": "MACs", "manufacturer": "Manufacturer", "model": "Model", "redirector": "Redirector", "revoke": "Revoke", "revoke_count": "Revoke Count", "revoke_warning": "Are you sure you want to revoke this certificate?", "server": "Server", "successful_revoke": "Successfully Revoked Certificate!", "title": "Certificates"}, "commands": {"abort_command_explanation": "Are you sure you want to stop waiting for this command's result?", "abort_command_title": "Abort Command", "active_scan": "Active Scan", "blink": "Blink", "blink_error": "Error while sending blink command: {{e}}", "blink_success": "Blink command successfully sent!", "channel": "Channel", "confirm_reset": "Start reset of Device  #{{serialNumber}}", "connect": "Connect", "rtty": "Rtty", "execution_time": "Execution Time", "factory_reset": "Factory Reset Device", "factory_reset_error": "Error while trying to factory reset device: {{e}}", "factory_reset_success": "Successfully started device factory reset!", "factory_reset_warning": "Note: Are you sure you want to factory reset this device? This action is not reversible", "firmware_upgrade": "Firmware Upgrade", "firmware_upgrade_error": "Error while trying to upgrade device firmware: {{e}}", "firmware_upgrade_success": "Successfully started device upgrade!", "image_date": "Image Date", "keep_redirector": "Keep Redirector?", "other": "Commands", "override_dfs": "Override DFS", "reboot": "Reboot", "reboot_description": "Do you want to reboot this device?", "reboot_error": "Error while sending reboot command: {{e}}", "reboot_success": "Successfully sent reboot command!", "revision": "Revision", "scan": "<PERSON><PERSON>", "signal": "Signal", "upgrade": "Upgrade", "wifiscan": "<PERSON><PERSON><PERSON><PERSON>", "wifiscan_error": "Error while trying to scan device: {{e}}", "wifiscan_error_1": "Your 5G radio is on a radar channel, you must enable “Override DFS” to allow scanning of all 5G channels"}, "common": {"actions": "Actions", "address_search_autofill": "Search locations to auto-fill the fields below", "alert": "<PERSON><PERSON>", "all": "All", "assign": "Assign", "avg": "Avg", "back": "Back", "base_information": "Base Information", "by": "By", "cancel": "Cancel", "claim": "<PERSON><PERSON><PERSON>", "close": "Close", "columns": "Columns", "command": "Command", "completed": "Completed", "confirm": "Confirm", "connected": "Connected", "copied": "<PERSON>pied", "copy": "Copy", "create": "Create", "create_new": "Create New", "created": "Created", "creator": "Creator", "custom": "Custom", "daily": "Daily", "date": "Date", "day": "Day", "days": "Days", "default": "<PERSON><PERSON><PERSON>", "defaults": "De<PERSON>ults", "description": "Description", "details": "Details", "device_details": "<PERSON>ce Det<PERSON>", "discard_changes": "Discard Changes?", "disconnected": "Disconnected", "display_name": "Display Name", "download": "Download", "download_instructions": "Download Successful! If you cannot find the file, please confirm that you are allowing downloads from this website", "duplicate": "Duplicate", "edit": "Edit", "email": "Email", "empty_list": "Empty List", "end": "End", "entries_one": "Entry", "entries_other": "Entries", "error": "Error", "error_claiming_obj": "Error while claiming {{obj}}", "error_download": "Error while trying to download: {{e}}", "errors": "Errors", "exit_fullscreen": "Exit", "export": "Export", "finished": "Finished", "fullscreen": "Fullscreen", "general_error": "Error connecting to the server. Please consult your administrator.", "general_info": "General Information", "go_back": "Go Back", "go_to_map": "Go to Map", "hide": "<PERSON>de", "hourly": "Hourly", "identification": "Identification", "inherit": "Inherit", "language": "Language", "last_use": "Last Use", "lifetime": "Lifetime", "locale": "Locale", "logout": "Logout", "main": "Main", "make_higher_priority": "Make Higher Priority", "make_lower_priority": "Make Lower Priority", "manage": "Manage", "manual": "Manual", "manufacturer": "Manufacturer", "map": "Map", "max": "Max", "min": "Min", "miscellaneous": "Miscellaneous", "mode": "Mode", "model": "Model", "modified": "Modified", "monthly": "Monthly", "months": "Months", "my_account": "My Account", "name": "Name", "name_error": "Name must be less than 50 characters long", "next": "Next", "no": "No", "no_addresses_found": "No Addresses Found", "no_clients_found": "No Clients Found", "no_devices_found": "No Devices Found", "no_items_yet": "No items yet", "no_obj_found": "No {{obj}} Found", "no_records_found": "No Records Found", "no_statistics_found": "No Statistics Found", "no_last_statistics_found": " No Last Statistics Found", "none": "None", "not_found": "404 - Not Found", "note": "Note", "notes": "Notes", "of": "of", "password": "Password", "preview": "Preview", "quarterly": "Quarterly", "redirector": "Redirector", "refresh": "Refresh", "remove": "Remove", "remove_claim": "<PERSON><PERSON><PERSON>", "reset": "Reset", "revoked": "Revoked", "save": "Save", "search": "Search", "seconds": "Seconds", "select_all": "Show All", "select_value": "Select Value", "sending": "Sending", "sent_code": "Sent Code!", "show": "Show", "size": "Size", "start": "Start", "start_time": "Start Time", "end_time": "End Time", "started": "Started", "state": "State", "status": "Status", "stop_editing": "Stop Editing", "submitted": "Submitted", "success": "Success", "successfully_claimed_obj": "Successfully claimed {{count}} {{obj}}", "sync": "Sync", "test": "Test", "theme": "Theme", "time": "Time", "timestamp": "Timestamp", "type": "Type", "type_for_options": "Type the value you need to create...", "select": "Select...", "unknown": "Unknown", "use_file": "Use File", "value": "Value", "variable": "Variable", "view": "View", "view_details": "View Details", "view_in_gateway": "View In Controller", "view_json": "View JSON", "warning": "Warning", "warnings": "Warnings", "yearly": "Yearly", "yes": "Yes", "your_new_note": "Your new note"}, "configurations": {"add_interface": "Add Interface", "add_radio": "Add Radio", "add_ssid": "Add SSID", "add_subsection": "Add Subsection", "advanced_settings": "Advanced Settings", "affected_explanation_one": "There are {{count}} device affected by this configuration", "affected_explanation_other": "There are {{count}} devices affected by this configuration", "applied_configuration": "Applied Configuration", "cant_delete_explanation": "Cannot delete this configuration because it is being used by at least one device, venue or entity. You can see what they are by clicking on the button next to \"In Use\" on this configuration's form", "cant_delete_explanation_simple": "Cannot delete this configuration because it is being used by at least one device, venue or entity. You can see what they are by going on the configuration page", "configuration_json": "Configuration JSON", "configuration_push_result": "Configuration <PERSON><PERSON> Result", "configuration_sections": "Configuration Sections", "delete_interface": "Delete Interface", "delete_radio": "Delete Radio", "delete_ssid": "Delete SSID", "device_configurations": "Device Configurations", "device_types": "Device Types", "dhcp_snooping": "DHCP Snooping", "error_pushes_one": "Error (could be because of bad configuration): {{count}}", "error_pushes_other": "Errors (could be because of bad configuration): {{count}}", "expert_name": "Expert Mode", "expert_name_explanation": "You can use expert mode to directly modify your configuration, including adding values that are not currently supported by the UI. Please use this format: { \"interfaces\": [ ... ], \"radios\": { ... }, ...etc }", "explanation": "Explanation", "firewall": "Firewall", "firmware_upgrade": "Firmware Upgrade", "globals": "Globals", "health": "Health", "hostapd_warning": "URL param, ex.: test=value", "import_file": "Import Configuration from File", "import_file_explanation": "You can use the option below to import a configuration JSON file, with a content of this format: \n{\n     \"interfaces\": [ ... ],\n     \"radios\": { ... },\n     ...etc\n}", "import_warning": "WARNING: This operation will overwrite all the configuration sections you might have already created. ", "imported_configuration": "Imported Configuration", "in_use_title": "{{name}} In Use", "interfaces": "Interfaces", "network": "Network", "interfaces_instruction": "Please use a valid JSON string in the following form: { \"interfaces\": [] }. ", "invalid_resource": "Invalid or Deleted Resource", "metrics": "Metrics", "no_resource_selected": "No Resource Selected", "notification_details": "Updated: {{success}}, Waiting for connection: {{warning}}, Errors: {{error}}", "one": "Configuration", "push_configuration": "Push Configuration", "push_configuration_error": "Error while trying to push configuration to device: {{e}}", "push_configuration_explanation": "Configuration not pushed, error code {{code}}", "push_success": "Configuration was verified and a \"Configure\" command was now initiated by the controller!", "radio_limit": "You have reached the maximum amount of radios (5). You need to delete one of the activated bands to add a new one", "radios": "Radios", "rc_only": "Release Candidates Only", "save_warnings": "Are you sure you want to save your configuration? ", "services": "Services", "special_configuration": "Device-Specific Configuration", "start_special_creation": "Create configuration for this device", "statistics": "Statistics", "successful_pushes_one": "Successful Push: {{count}}", "successful_pushes_other": "Successful Pushes: {{count}}", "third_party": "Third Party", "third_party_instructions": "Please use a valid JSON string in the following form: { \"value_name\": \"value\" }. ", "title": "Configurations", "unit": "Unit", "system": "System", "view_affected_devices": "View Affected Devices", "view_in_use": "View In Use", "warning_pushes_one": "Waiting for devices to connect: {{count}}", "warning_pushes_other": "Waiting for devices to connect: {{count}}", "weight": "Weight", "wifi_bands_max": "There cannot be more than 16 SSIDs using this wifi-band", "wifi_frames": "<PERSON><PERSON><PERSON><PERSON>", "multi_psk_key_exsist": "The key already exists"}, "contacts": {"access_pin": "Access PIN", "claim_explanation": "To claim contacts you can use the table below", "first_name": "First Name", "identifier": "Identifier", "initials": "Initials", "last_name": "Last Name", "mobiles": "Mobiles", "one": "Contact", "other": "Contacts", "phones": "Phones", "primary_email": "Primary Email", "salutation": "Salutation", "secondary_email": "Secondary Email", "title": "Title", "to_claim": "Contacts to claim", "visual": "Visual"}, "controller": {"configurations": {"create_success": "Created Configuration!", "delete_success": "Configuration is now deleted!", "title": "Default Configurations", "update_success": "Updated Configuration!"}, "configure": {"invalid": "Your new configuration needs to be valid JSON", "success": "New configuration is now deploying to the device", "title": "Configure via CLI", "warning": "Note: there will only be minimal tests done on this configuration"}, "crud": {"choose_time": "Custom Time-Frame", "clear_time": "Clear Time", "delete_success_obj": "Deleted {{obj}}"}, "dashboard": {"associations": "Associations", "associations_explanation": "All current connected associations (or UEs)", "certificates": "Certificates", "certificates_explanation": "Status of certificates of currently connected devices", "commands": "Commands", "commands_explanation": "All executed commands", "device_dashboard_refresh": "New Connection Statistics", "device_types": "Device Types", "device_types_explanation": "Device types of all devices pointing to this controller", "devices_explanation": "All devices are pointing towards this controller ", "error_fetching": "Error fetching dashboard", "expand": "Expand", "last_ping_explanation": "When this data was gathered ", "memory": "Memory Use", "memory_explanation": "Currently connected devices with the corresponding amount of used memory", "no_certificate": "No Certificate", "not_connected": "Not Connected", "others": "Others", "overall_health": "Overall Health", "overall_health_explanation": "Average health of all currently connected devices from which we receive health data. The exact calculation is: (Devices=100% * 100 + Devices>=90 * 95 + Devices>=60 * 75 + Devices<60 * 30) / Connected Devices", "overall_health_explanation_pie": "The number of devices with a health percentage within these categories", "serial_mismatch": "Serial Mismatch", "status": "Status", "status_explanation": "Status of all devices pointing at this controller ", "unknown_status": "Unrecognized Status", "unrecognized": "Unrecognized", "uptimes": "Uptimes", "uptimes_explanation": "Currently connected devices with the corresponding uptimes", "vendors": "Vend<PERSON>", "vendors_explanation": "Vendors of the devices pointing to this controller", "verified": "Verified"}, "devices": {"add_blacklist": "Add Serial Number", "added": "Added", "added_blacklist": "Added serial number to blacklist!", "average_uptime": "Average Uptime", "blacklist": "Blacklist", "blacklist_update": "Update {{serialNumber}} Record", "by": "By", "capabilities": "Capabilities", "command_one": "Command", "commands": "Commands", "complete_data": "Complete Data", "config_id": "Config ID", "connecting": "Connecting", "connection_changes": "Connection Statuses", "delete_blacklist": "Removed serial number from blacklist!", "delete_health_explanation": "This will permanently delete all of the health checks before the date you choose", "delete_logs_explanation": "This will permanently delete all of the logs before the date you choose", "error_code": "Error Code", "executed": "Executed", "finished_reboot": "{{serialNumber}} just finished rebooting!", "finished_upgrade": "{{serialNumber}} has finished upgrading!", "from_to": "From {{from}} to {{to}}", "healthchecks": "Health Checks", "last_modified": "Last Modified: ", "last_upgrade": "Last Upgrade", "localtime": "Local Time", "logs": "Logs", "new_statistics": "New Statistics", "no_more_available": "All Retrieved", "reason": "Reason", "results": "Results", "sent_upgrade_to_latest": "Sent 'Upgrade to Latest' command to device", "severity": "Severity", "show_more": "Show More", "started_reboot": "{{serialNumber}} shut off to reboot!", "started_upgrade": "{{serialNumber}} just shut down to start the upgrade!", "trace": "Trace", "trace_description": "Note: Launch a remote trace of this device for either a specific duration or a number of packets", "update_success": "Device updated!", "updated_blacklist": "Updated Blacklist!"}, "firmware": {"devices_explanation": "Devices that have pointed towards this firmware server. This could explain discrepancies between this number and the device dashboard's ", "endpoints": "Endpoints", "endpoints_explanation": "All endpoints that will point towards this firmware server", "firmware_age": "Firmware Age", "firmware_age_explanation": "Average firmware age for the devices for which we have that data", "latest": "Latest Firmware Installed", "old_firmware": "Old Firmware", "ouis_explanation": "OUIs of devices that have connected to this firmware server", "outdated_one": "Firmware {{count}} day old", "outdated_other": "Firmware {{count}} days old", "outdated_unknown": "Firmware of unknown age", "release": "Release", "show_dev_releases": "Dev Releases", "status_explanation": "Connection status of devices that have connected to this firmware server", "unrecognized": "Unrecognized Firmware", "unrecognized_firmware": "Unrecognized Firmware", "unrecognized_firmware_explanation": "Firmware that is currently used by devices and is not recognized by this firmware server", "up_to_date": "Up To Date Devices", "up_to_date_explanation": "Devices using the latest available software available to them"}, "provisioning": {"title": "Provisioning"}, "queue": {"title": "Event Queue"}, "radius": {"calling_station_id": "Station", "disconnect": "Disconnect", "disconnect_success": "Radius session disconnected!", "input_octets": "Input", "output_octets": "Output", "radius_clients": "Radius Clients", "session_time": "Session Time", "username": "Username"}, "stats": {"load": "Load (1 | 5 | 15 m.)", "seconds_ago": "{{s}} seconds ago", "used": "used"}, "telemetry": {"duration": "Duration", "interval": "Interval", "kafka": "Kafka", "kafka_success": "Kafka telemetry is now started!", "last_update": "Last Update", "minutes": "Minutes", "need_types": "You need to select at least one type", "output": "Output Mode", "seconds_ago": "{{seconds}} seconds ago", "title": "Telemetry", "types": "Types", "websocket": "WebSocket"}, "trace": {"down": "Down", "download": "Download Trace", "duration": "Duration", "network": "Network", "packets": "Packets", "success": "Completed trace on device #{{serialNumber}}. You can now download the result", "up": "Up", "wait": "Wait for the results?"}, "wifi": {"active_ms": "Active", "busy_ms": "Busy", "channel_width": "<PERSON>", "mode": "Mode", "noise": "Noise", "receive_ms": "Receive", "rx_rate": "Rx Rate", "station": "Station", "tx_rate": "Tx Rate", "vendor": "<PERSON><PERSON><PERSON>", "wifi_analysis": "Wi-Fi Analysis "}}, "crud": {"add": "Add", "confirm_cancel": "Are you sure you want to discard the changes you have made?", "confirm_delete_obj": "Are you sure you want to delete this {{obj}}?", "create": "Create", "create_object": "Create {{obj}}", "delete": "Delete", "delete_confirm": "Are you sure you want to delete this {{obj}}?", "delete_obj": "Delete {{obj}}", "edit": "Edit", "edit_obj": "Edit {{obj}}", "error_create_obj": "Error creating {{obj}}: {{e}}", "error_delete_obj": "Error deleting {{obj}}: {{e}}", "error_fetching_obj": "Error fetching {{obj}}: {{e}}", "error_revoke_obj": "Error revoking  {{obj}}: {{e}}", "error_update_obj": "Error updating {{obj}}: {{e}}", "success_create_obj": "Successfully Created {{obj}}!", "success_delete_obj": "Successfully deleted {{obj}}!", "success_revoke_obj": "Successfully revoked {{obj}}!", "success_update_obj": "Successfully updated {{obj}}!"}, "devices": {"all": "All", "associations": "Associations", "certificate_expires_in": "Certificate Expiry", "certificate_expiry": "Cert. Expires In", "connected": "Connected", "crash_logs": "Crash Logs", "create_errors": "errors while trying to create devices", "create_success": " devices successfully created", "current_firmware": "Current Firmware", "device_type_not_found": "Unrecognized device type", "duplicate_serial": "Duplicate serial number within the file", "error_rtty": "Error trying to connect to the device: {{e}}", "file_errors": "problematic devices", "found_assigned": "already assigned devices", "found_not_assigned": "already existing but now owned devices", "import_batch_tags": "Import Devices", "import_device_warning": "Please make sure there are no extra spaces at the start or end of any values unless it is part of the value desired", "import_explanation": "To bulk import devices, you need to use a CSV file with the following columns: SerialNumber, DeviceType, Name, Description, Note, Group. Download device import template", "invalid_serial_number": "Invalid Serial Number (needs to be 12 HEX chars)", "logs_one": "Log", "new_devices": "new devices", "no_model_image": "No Model Image Found", "not_connected": "Not Connected", "not_found_gateway": "Error: device has not yet connected to the controller", "notifications": "Device Notifications", "one": "<PERSON><PERSON>", "reassign_already_owned": "Reassign devices which already exist and are owned by another entity/venue/subscriber?", "reboot_logs": "Reboot Logs", "restricted": "Restricted", "restricted_overriden": "This is a restricted device, but it is in development mode. All restrictions are currently ignored", "restrictions_overriden_title": "Dev Mode", "sanity": "Sanity", "start_import": "Start Device Importation", "test_batch": "Test Import Data", "test_results": "Test Results", "title": "Devices", "treating": "Testing: ", "unassigned_only": "Unassigned Only", "update_error": "errors while trying to update devices", "update_success": "devices successfully updated"}, "entities": {"active": "Active", "add_configurations": "Add Configurations", "add_ips": "Add IPs", "add_ips_explanation": "You can add IPv4 or IPv6 addresses in the following formats", "api_key": "API Key", "cant_delete_explanation": "Cannot delete this entity because it has child entities and/or venues. You need to delete all of this entity's children before deleting it ", "claim_device_explanation": "To claim devices you can use the table below. If a device was already claimed by another entity or venue we will also unassign them before assigning them to this entity", "client_enrollment_profile": "Client Enrollment Profile", "create_child_entity": "Create Child Entity", "create_root": "Create Root Entity", "create_root_explanation": "Please enter the information necessary to create the root entity of your provisioning service. This information can be modified after creation", "current_state": "Current State", "default_redirector": "<PERSON><PERSON><PERSON>ire<PERSON>", "devices_to_claim": "New Devices to Claim", "devices_under_root": "Devices cannot be created directly under the root entity. Please create new entities or venues and create devices under them.", "enter_ips": "Enter the IP(s) you'd like to add here", "entity": "Entity", "error_sync": "Error while trying to start synchronization of {{name}}: {{e}}", "failed_test": "Failed tests with DigiCert credentials, please verify your entity information", "initial_state": "Initial State", "ip_cidr": "IP/number (example: 10.0.0.0/8)", "ip_detection": "IP Detection", "ip_list": "List: IP,IP IP", "ip_range": "Range: IP-IP", "ip_single_address": "Single Address: IP", "one": "Entity", "organization": "Organization", "server_enrollment_profile": "Server Enrollment Profile", "status": "Status", "sub_one": "Sub-Entity", "sub_other": "Sub-Entities", "success_sync": "Successfully started synchronization of {{name}}!", "success_test": "Test of this entity's DigiCert credentials was successful!", "suspended": "Suspended", "sync_explanation": "Would you like to synchronize this entity? This may take a while depending on the amount of certificates belonging to this entity.", "sync_title": "Sync {{name}}", "test_digicert_creds": "Test Credentials", "title": "Entities", "tree": "Entity Tree", "update_success": "Entity updated!", "venues_under_root": "Venues cannot be created directly under the root entity"}, "firmware": {"confirm_default_data": "Please confirm the information below and click 'Confirm' once you are ready to start the process", "create_success": "Created new default firmware settings!", "db_update_warning": "This operation is done daily automatically without need to use this manual update. Updating this database can take up to 25 minutes", "default_created_error_one": "{{count}} error while trying to create new setting", "default_created_error_other": "{{count}} errors while trying to create new setting", "default_created_one": "{{count}} default firmware setting created", "default_created_other": "{{count}} default firmware settings created", "default_found_one": "Found valid revision for {{count}} device type", "default_found_other": "Found valid revisions for {{count}} device types", "default_mass_delete_success_one": "Deleted {{count}} default firmware setting!", "default_mass_delete_success_other": "Deleted {{count}} default firmware settings!", "default_not_found_one": "No valid firmware versions for {{count}} device type", "default_not_found_other": "No valid firmware versions for {{count}} device types", "default_title": "Default Firmware", "default_update_success": "Updated default firmware for {{deviceType}}!", "delete_success": "Deleted default firmware setting!", "edit_default_title": "This is the current firmware that is used as the minimum version for new APs of type {{deviceType}}. If a new {{deviceType}} AP connects to the gateway, it will be automatically upgraded to this version.", "fetching_defaults": "Fetching all available firmware for selected device types...", "last_db_update_modal": "Firmware Database", "last_db_update_title": "Database", "one": "Firmware", "select_default_device_types": "Please select all device types that you want to target with this new default firmware rule. If you cannot find your desired device type, it means they already have an applied rule.", "select_default_revision": "You can now select the minimum revision you want your device types to target", "start_db_update": "Start Database Update", "started_db_update": "Started database update, this operation should take up to 25 minutes to complete", "update_success": "Saved default firmware information!"}, "footer": {"powered_by": "Powered By", "version": "Version"}, "form": {"captive_web_root_explanation": "Please use .tar files only (no compressed files like .targz, for example)", "certificate_file_explanation": "Please use a .pem file that starts with \"-----BEGIN CERTIFICATE-----\" and ends with \"-----END CERTIFICATE-----\"", "invalid_alphanumeric_with_dash": "Accepted chars. are only alphanumeric (letters & numbers)", "invalid_cidr": "Invalid CIDR IPv4 address. Example: ***********/12", "invalid_email": "<PERSON><PERSON><PERSON>", "invalid_file_content": "Invalid file content, please confirm that it is of the valid format", "invalid_fqdn_host": "Invalid FQDN hostname", "invalid_hostname": "Invalid hostname: it needs to be composed of alphanumeric characters and dashes only", "invalid_icon_lang": "Invalid language, it should be in a 3-letter format (eng, fre, ger, ita, etc.)", "invalid_ieee": "For this encryption protocol,  ieee80211w needs to be either 'optional' or 'required'", "invalid_ieee_required": "ieee80211w needs to be 'required' for this encryption protocol", "invalid_interfaces": "Invalid Interfaces JSON string. Please confirm that your value is: valid JSON and has interfaces as its only key and that the interfaces value is an array. Example: {\"interfaces\": []}", "invalid_ipv4": "Invalid IPv4 address (ex.: ***********/16)", "invalid_ipv6": "Invalid IPv6 address (ex.: fd00::/64)", "invalid_json": "Invalid JSON string", "invalid_lease_time": "Invalid lease time value! They need to be in the digitUnit format. For example: 6d2h5m, which means 6 days, 2 hours and 5 minutes. Here are the accepted units: m, h, d. If you do not want to use a unit, omit it completely. So instead of saying 0d2h0m, use 2h", "invalid_mac_uc": "Invalid MAC value, for example: 00:00:5e:00:53:af", "duplicate_mac": "This MAC address already exists in the list", "duplicate_ip": "This IP address already exists in the list", "invalid_password": "Invalid password, please look at the password policy", "invalid_phone_number": "Invalid Phone Number", "invalid_phone_numbers": "One or more of the phone numbers are invalid. Please provide them without symbols and spaces, or in this format: +1(123)123-1234", "invalid_port_range": "Invalid port value. It needs to be more than 0 and less than 65 535. If using a port range, please make sure the second port is a higher number than the first one.", "invalid_port_ranges": "Invalid port range combination! Please make sure both port values are the same type (single or range). If they are ranges, make sure they are both covering the same amount of ports", "invalid_proto_6g": "This encryption protocol cannot be used on an SSID which uses 6G", "invalid_proto_passpoint": "", "invalid_select_ports": "Incompatible values between interfaces! Please make sure that there is no duplicate PORT/VLAN ID combination between your interfaces", "invalid_static_ipv4_d": "Invalid address, this range reserved for multicasting (class D). The first octet should be 223 or lower", "invalid_static_ipv4_e": "Invalid address, this range reserved for experiments (class E). The first octet should be 223 or lower", "invalid_third_party": "Invalid Third Party JSON string. Please confirm that your value is a valid JSON", "key_file_explanation": "Please use a .pem file that starts with \"-----BEGIN PRIVATE KEY-----\" and ends with \"-----END PRIVATE KEY-----\"", "max_length": "Maximum length of {{max}} chars.", "max_value": "Maximum value of {{max}}", "min_length": "Minimum length of {{min}} chars.", "min_max_string": "Value needs to be of a length between {{min}} (inclusive) and {{max}} (inclusive)", "min_value": "Minimum value of {{min}}", "missing_interface_upstream": "You need to have at least one Bridged Mode (Layer 2 bridging) interface. At the moment, all your interfaces are Routing Mode (NAT)", "new_email_to_notify": "New email to notify", "new_phone_to_notify": "New phone to notify", "not_selected": "Not Selected", "not_uploaded_yet": "Not Uploaded Yet", "pem_file_explanation": "Please use a .pem file", "required": "Required", "using_file": "(using file: {{filename}})", "value_recorded_no_filename": "Recorded value, no filename", "invalid_mac_format": "The legal Mac format can only contain lowercase letters and numbers, with a delimiter of ':'. Please use the format: 00:00:5e:00:53:af", "must_be_integer": "Must be an integer", "range_min": "Value must be at least {min}", "range_max": "Value must be no more than {max}", "range_both": "Value must be between {min} and {max}", "invalid_label_name": "Label name can only contain letters and numbers", "invalid_static_ipv4_loopback": "Invalid address, loopback address range cannot be used", "invalid_domain_or_ip": "It can only be an IP or domain name, for example:  www.fs.com"}, "inventory": {"computed_configuration": "Computed Configuration", "dev_class": "Device Class", "device_type": "Device Type", "error_reboots": "Error while sending command: {{count}}", "error_remove_claim": "Error while removing claim: {{e}}", "error_upgrades": "Error while sending upgrade command: {{count}}", "invalid_serial_number": "Invalid serial number. A serial number should only be 12 HEX chars (A-F, 0-9)", "invalid_device_name": "Invalid device name. Must be 1-2 alphanumeric characters, or 3-63 alphanumeric characters with hyphens (cannot start or end with a hyphen)", "no_computed": "No Computed Configuration: you will need to assign a valid configuration to see it", "no_firmware": "No firmware available for device type: {{count}}", "not_connected": "Device not connected: {{count}}", "parent": "Parent", "serial_number": "Serial Number", "skipped_upgrades": "Skipped upgrades: {{count}}", "success_remove_claim": "Successfully removed claim on: {{serial}}", "successful_reboots": "Started Rebooting: {{count}}", "successful_upgrades": "Successful upgrades: {{count}}", "tag_one": "<PERSON><PERSON>", "tags": "Inventory Devices", "title": "Inventory", "warning_reboots": "Not connected: {{count}}", "warning_upgrades": "Devices not connected: {{count}}", "label": "Label", "site": "Site", "create_label": "Create Label"}, "jobs": {"error_macs": "Error MACs", "job": "Job", "job_details": "Job Details", "notify_emails": "Notify Emails", "notify_sms": "Notify SMS", "successful_macs": "Successful MACs", "title": "Jobs"}, "keys": {"description_error": "Description needs to be less than 64 characters long", "expire_error": "The expiry cannot be more than one year in the future", "expires": "Expires", "max_keys": "Max keys reached (10)", "name_error": "Name should be unique and be between 6 and 20 alphanumeric characters", "one": "API Key", "other": "API Keys"}, "locations": {"address_line_one": "Address Line One", "address_line_two": "Address Line Two", "building_name": "Building Name", "city": "City", "claim_explanation": "To claim locations you can use the table below", "country": "Country", "elevation": "Elevation", "geocode": "Geo Code", "lat": "Latitude", "longitude": "Longitude", "one": "Location", "other": "Locations", "postal": "ZIP/Postal Code", "state": "State/Province", "title": "Locations", "to_claim": "Locations to claim", "view_gps": "View GPS Location"}, "login": {"access_policy": "Access Policy", "change_password_error": "Rejected password, this maybe an old password", "change_password_explanation": "Enter and confirm your new password", "change_your_password": "Change Password", "confirm_new_password": "Confirm new password", "email_instructions": "You should soon receive a 6-digit code at your email address. If you cannot find it please verify your spam folder. ", "error_sending_code": "Error while trying to send code: {{e}}", "forgot_password": "Forgot Password?", "forgot_password_instructions": "Enter your email address to receive an email containing the instructions to reset your password", "forgot_password_successful": "You should soon receive an email containing the instructions to reset your password. Please make sure to check your spam if you can't find the email", "forgot_password_title": "Forgot Password", "google_instructions": "Please input the 6-digit code from your Google Authenticator app. If it is close to expiring, you can wait for a new one", "invalid_credentials": "Invalid Credentials, please confirm that you are using the right email and password.", "invalid_mfa": "Invalid Code! Please try again", "login_explanation": "Enter your email and password to sign in", "new_password": "New password", "password_policy": "Password Policy", "remember_me": "Remember Me", "resend": "Resend", "resent_code": "Successfully Resent Code!", "reset_password": "Reset Password", "sign_in": "Sign In", "sms_instructions": "You should receive a 6-digit code on your phone soon. Please enter it below to login", "suspended_error": "Suspended account, please contact your administrator", "verification": "Verify your login", "waiting_for_email_verification": "Account not yet email validated. Please look at your inbox or ask your administrator to resend a validation", "welcome_back": "Welcome Back!", "your_email": "Your email address", "your_new_password": "Your new password", "your_password": "Your password"}, "logs": {"configuration_upgrade": "Configuration Update", "device_firmware_upgrade": "Firmware Upgrade", "device_statistics": "Device Statistics", "export": "Export", "filter": "Filter", "firmware": "Firmware", "global_connections": "Global Connections", "level": "Level", "message": "Message", "one": "Log", "receiving_types": "Notifications Filter", "security": "Security", "source": "Source", "thread": "<PERSON><PERSON><PERSON>", "venue_config": "Configuration", "venue_reboot": "Reboot", "venue_upgrade": "Upgrade"}, "map": {"auto_align": "Auto Align", "auto_map": "Auto Map", "by_others": "Maps by others", "cumulative_devices": "Cumulative Devices", "default_map": "Default Map", "delete_warning": "Are you sure you want to delete this map? This operation is not reversible", "duplicating": "Duplicating Map", "my_maps": "My Maps", "other": "Maps", "root": "Root", "root_node": "Root Node", "set_as_default": "Set as <PERSON><PERSON><PERSON>", "title": "Map", "visibility": "Visibility"}, "notification": {"one": "Notification", "other": "Notifications"}, "openroaming": {"pool_strategy": "Pool Strategy", "radius_endpoint_one": "<PERSON><PERSON> Endpoint", "radius_endpoint_other": "<PERSON><PERSON> Endpoints"}, "operator": {"delete_explanation": "Are you sure you want to delete this operator? This operation is not reversible", "delete_operator": "Delete Operator", "import_location_from_device": "Import from other Device", "one": "Operator", "operator_one": "Operator", "operator_other": "Operators", "other": "Operators", "registration_id": "Registration ID"}, "organization": {"my_organization": "My Organization", "title": "Organization"}, "overrides": {"delete_source": "Delete all overrides from {{source}}", "ignore_overrides": "Ignore Configuration Overrides", "name_error": "Parameter is already defined by your source", "one": "Configuration Override", "other": "Configuration Overrides", "param_name": "Parameter", "param_value": "Value", "parameter": "Parameter", "reason": "Reason", "reason_error": "Your reason needs to be less than 64 chars. long", "source": "Source", "tx_power_error": "Tx power needs to be between 1 and 32", "update_success": "Updated Configuration Overrides!", "value": "Value"}, "profile": {"about_me": "About Me", "activate": "Activate", "add_new_note": "Add Note", "deactivate": "Deactivate", "delete_account": "Delete my Profile", "delete_account_confirm": "Delete all of my information", "delete_warning": "This action is non-reversible. All of your profile information and your API keys will be removed", "deleted_success": "Your profile is now deleted, we will now log you out...", "disabled": "Disabled", "enabled": "Enabled", "manage_avatar": "Manage Avatar", "new_password": "New Password", "new_password_confirmation": "Confirm New Password", "your_profile": "Your Profile"}, "resources": {"configuration_resource": "Resource", "title": "Resources", "variable": "Variable"}, "restrictions": {"algo": "Signing Algorithm", "allowed": "Allowed", "countries": "Allowed Countries", "developer": "Developer Mode", "dfs": "DFS Override", "gw_commands": "Gateway Commands", "identifier": "Identifier", "key_verification": "Signing Key Information", "restricted": "Restricted", "signed_upgrade": "Signed Upgrade Only", "title": "Restrictions", "tty": "TTY Access"}, "roaming": {"account_created": "New account created!", "account_deleted": "Deleted account!", "account_one": "Account", "account_other": "Accounts", "certificate_deleted": "Deleted certificate!", "certificate_one": "Certificate", "certificate_other": "Certificates", "city": "City", "common_name": "Common Name", "country": "Country", "global_reach": "GlobalReach", "global_reach_account_id": " Account ID", "invalid_certificate": "Invalid certificate", "invalid_key": "Invalid private key", "location_details_title": "Location", "organization": "Organization", "private_key": "Private Key", "province": "Province", "state": "State"}, "rrm": {"algorithm": "Algorithm", "algorithm_other": "Algorithms", "cant_save_custom": "Cannot create or edit custom RRM configurations until RRM server is reachable. Please consult with your administrator", "cron_error": "Error while parsing CRON expression: please confirm that it is valid", "cron_scheduler": "CRON Scheduler", "cron_templates": "Templates", "no_algos": "Unable to fetch RRM algorithms at the moment", "no_providers": "Unable to fetch RRM providers at the moment", "param_error": "Your parameters do not respect the rules of this algorithm. Please look at the algorithm examples and details", "parameters": "Parameters", "vendor": "<PERSON><PERSON><PERSON>", "version": "Version"}, "script": {"author": "Creator", "automatic": "Automatic", "create_success": "Script is now created and ready to use!", "custom_domain": "Custom Domain", "deferred": "Deferred", "device_title": "<PERSON>", "diagnostics": "Diagnostics", "explanation": "Run a custom script on this device and download its results", "file_not_ready": "Result is not uploaded yet, please come back later", "file_too_large": "Please select a file that is less than 500KB", "helper": "Documentation", "no_script_available": "No script available for your user role", "now": "Now", "one": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>", "restricted": "Users allowed to run this script", "schedule_success": "Scheduled script execution!", "signature": "Signature", "started_execution": "Started script execution, come later for the results!", "timeout": "Timeout", "update_success": "Script updated!", "upload_destination": "Results Upload Destination", "upload_file": "Upload File", "visit_external_website": "View Documentation", "when": "Schedule Execution"}, "service": {"billing_code": "Billing Code", "billing_frequency": "Billing Frequency", "class_one": "Service Class", "class_other": "Service Classes", "cost": "Cost", "one": "Service Class", "other": "Service Classes"}, "simulation": {"cancel": "Cancel Simulation", "cancel_explanation": "Stop the simulation and erase its record", "cancel_success": "Stopped simulation and erased its record!", "client_interval": "Client Interval", "concurrent_devices": "Concurrent Devices", "controller": "Controller", "current_live_devices": "Current Live Devices", "currently_running_one": "There is currently {{count}} simulation running", "currently_running_other": "There are currently {{count}} simulations running", "delete_devices_confirm": "Are you sure you want to remove all devices and their statistics from the gateway? This action is not reversible", "delete_devices_loading": "This process may take up to 5 minutes", "delete_simulation_devices": "Delete Devices", "delete_success": "Deleted Simulation!", "duration": "Duration", "error_devices": "Error <PERSON>", "healthcheck_interval": "Healthcheck Interval", "infinite": "Infinite", "keep_alive": "Keep Alive", "mac_prefix": "MAC Prefix", "mac_prefix_length": "Your MAC prefix needs to be valid 6 HEX digits (ex.: 00112233)", "max_associations": "Max. Associations", "max_clients": "Max. Clients", "min_associations": "Min. Associations", "min_clients": "Min. <PERSON>", "no_sim_running": "No simulation currently running", "one": "Simulation", "other": "Simulations", "owner": "Owner", "realtime_data": "Real-Time Data", "realtime_messages": "Real-Time Messages", "reconnect_interval": "Reconnect Interval", "result_delete_success": "Deleted Result!", "rx": "Received", "rx_messages": "Rx Messages", "sim_currently_running": "Simulation \"{{sim}}\" in progress", "sim_history": "{{sim}} Previous Runs", "simulated": "Simulated", "start": "Start Simulation", "start_success": "Started simulation run!", "state_interval": "State Interval", "stop": "Stop Simulation", "stop_success": "Stopped simulation!", "threads": "Threads", "time_to_full": "Time to full devices", "tx": "Transmitted", "tx_messages": "Tx Messages", "view_previous_runs": "View Previous Runs"}, "statistics": {"last_stats": "Last Statistics", "latest": "Latest Statistics", "memory": "Memory"}, "subscribers": {"billing_contact_info": "Billing and Contact Details", "claim_device_explanation": "To claim devices you can use the table below. If a device was already claimed by a user, you will need to go to to their details and unassign it before claiming it.", "devices_claimed_one": "{{count}} <PERSON><PERSON>", "devices_claimed_other": "{{count}} Devi<PERSON> Claimed", "devices_to_claim_one": "{{count}} Device to <PERSON><PERSON>m", "devices_to_claim_other": "{{count}} Devices to <PERSON><PERSON><PERSON>", "error_claiming": "Error claiming: {{serials}}", "error_removing_claim": "Error removing claim(s) on: {{serials}}", "no_subscribers_found": "No Subscribers Found", "one": "Subscriber", "other": "Subscribers", "reactivate_explanation": "Are you sure you want to reactivate this subscriber?", "reactivate_title": "Reactivate Subscriber", "title": "Subscribers"}, "system": {"advanced": "Advanced", "backend_logs": "Back-End Logs", "configuration": "Configuration", "could_not_retrieve": "Error: could not retrieve {{name}} system information", "endpoint": "Endpoint", "hostname": "Host Name", "info": "System Info", "level": "Log Level", "logging": "Logging", "no_log_levels": "No Reported Log Levels ", "os": "Operating System", "processors": "Processors", "reload_chosen_subsystems": "Reload Chosen Subsystems", "secrets": "Secrets", "secrets_create": "Create Secret", "secrets_one": "Secret", "services": "Services", "start": "Start", "subsystems": "Subsystems", "success_reload": "Successfully sent reload command!", "systems_to_reload": "Choose systems to reload", "title": "System", "update_level_success": "Updated log levels!", "update_levels": "Update", "uptime": "Uptime", "version": "Version"}, "table": {"columns": "Columns", "columns_hidden_one": "{{count}} Column Hidden", "columns_hidden_other": "{{count}} Columns Hidden", "display_column": "Display", "drag_always_show": "You cannot hide this column but can change its position ", "drag_explanation": "Drag and drop to reorder and change column visibility", "drag_locked": "This column is locked in its position", "export_current_page": "Current Page Only", "first_page": "First Page", "go_to_page": "Go to page", "hide_column": "<PERSON>de", "last_page": "Last Page", "next_page": "Next Page", "page": "Page", "preferences": "Table Preferences", "previous_page": "Previous Page", "reset": "Reset Preferences", "settings": "Settings"}, "user": {"email_not_validated": "email not validated", "error_fetching": "Error fetching user information: {{e}}", "password": "Password", "role": "Role", "suspended": "suspended", "title": "User"}, "users": {"change_password": "Force Password Change", "email_validation": "Email Validation", "error_fetching": "Error fetching users: {{e}}", "error_sending_validation": "Error while sending email validation: {{e}}", "last_login": "Last Login", "login_id": "Login Id", "one": "User", "re_validate_email": "Re-validate Email", "reactivate_user": "Reactivate User", "reset_mfa": "Reset MFA", "reset_mfa_success": "Successfully reset user MFA!", "reset_password": "Reset Password", "reset_password_error": "Error trying to reset user password: {{e}}", "reset_password_success": "Successfully sent reset password email to user email address", "role": "Role", "send_validation": "Send Email Validation", "send_validation_explanation": "Do you want to resend the email verification link?", "stop_suspension": "Re-activate", "success_sending_validation": "Validation email sent!", "suspend": "Suspend", "suspend_success": "User is now suspended", "suspended": "Suspended", "title": "Users", "waitiing_for_email_verification": "Email Not Verified"}, "venues": {"confirm_remove_contact": "Do you want to remove this contact from this venue?", "create_child": "Create Child Venue", "error_remove_contact": "Error while trying to remove contact: {{e}}", "error_update_devices": "Error starting device update: {{e}}", "go_to_page": "Go to page", "one": "Venue", "reboot_all_devices": "Reboot All Devices", "sub_one": "Sub-Venue", "sub_other": "Sub-Venues", "subvenues": "Sub-Venues", "successfully_reboot_devices": "Rebooting {{num}} devices!", "successfully_removed_contact": "Successfully removed contact!", "successfully_update_devices": "Updating {{num}} devices!", "title": "Venues", "update_all_devices": "Update All Device Configurations", "update_success": "Venue updated!", "upgrade_all_devices": "Upgrade All Devices Firmware", "upgrade_all_devices_error": "Error upgrading devices: {{e}}", "upgrade_all_devices_success": "Successfully started upgrading devices!", "upgrade_options_available": "Here are all available revisions, please select the one you want ALL of this venue's devices to be upgrade to", "use_existing": "Use Existing", "use_existing_contacts": "Use Existing Contacts", "use_this_contact": "Use this contact"}}