{"account": {"account": "Conta", "activating_google_authenticator": "Ativando o Google Authenticator", "activating_sms_mfa": "Validação do número de telefone", "avatar": "Avatar", "error_fetching_qr": "Erro ao buscar o código QR: {{e}}", "error_phone_verif": "Erro com seu código de validação, tente novamente.", "google_authenticator": "Autenticador do Google", "google_authenticator_intro": "Para usar o Google Authenticator como método de autenticação dupla da sua conta, primeiro você precisa instalar o aplicativo em seu dispositivo iOS ou Android", "google_authenticator_ready": "Depois de ter o aplicativo pronto para uso, você pode prosseguir", "google_authenticator_scan_qr_code_explanation": "Digitalize o seguinte QR Code usando o \"Scan a QR Code\" no aplicativo Google Authenticator", "google_authenticator_scanned_qr_code": "Depois que o código QR for digitalizado com sucesso em seu telefone, você poderá prosseguir para a próxima etapa", "google_authenticator_success_explanation": "Agora você configurou com sucesso o Google Authenticator com sua conta. Não se esqueça de salvar suas alterações para confirmar!", "google_authenticator_type_code": "Insira o código de 6 dígitos do seu aplicativo Google Authenticator abaixo", "google_authenticator_wait_for_code": "Aguarde o próximo código (não {{old}})", "google_authenticator_wrong_code": "Código inválido! Tente novamente ou aguarde a geração do próximo código no aplicativo Google Authenticator", "mfa": "Autenticação multifatorial", "phone": "telefone", "phone_number": "Número de telefone", "phone_number_add_introduction": "Digite o número de telefone que você gostaria de usar para proteger sua conta no login", "phone_required": "Para ativar a verificação por SMS, você precisa inserir um número de telefone", "phone_validation_success_explanation": "Número de telefone verificado com sucesso! Clique em 'Salvar' para adicionar este número de telefone à sua conta", "proceed_to_activation": "Iniciar processo de ativação", "resend": "REENVIAR", "sms": "SMS", "success_phone_verif": "Número de telefone verificado com sucesso! Agora você pode salvar seu perfil", "title": "Minha conta", "verify_phone_instructions": "Você deve receber um código no seu número de telefone nos próximos segundos. Insira-o abaixo para verificar seu número de telefone", "verify_phone_number": "Verifique seu número de telefone"}, "analytics": {"ack_signal": "Sinal ACK", "active": "Ativo", "airtime": "Tempo de antena", "analyze_sub_venues": "Monitorar Sub-Locais", "associations": "Associações", "associations_explanation": "Total de associações", "average_health": "Sa<PERSON><PERSON> geral", "average_health_explanation": "Sanidade média de todos os dispositivos conectados que fornecem as informações de verificação de integridade", "average_memory": "Memória Usada", "average_memory_explanation": "Porcentagem média de memória usada", "average_uptime": "Tempo de atividade médio", "average_uptime_explanation": "Tempo médio de atividade do dispositivo (DD:HH:MM:SS)", "band": "Banda", "bandwidth": "Largura de banda", "board": "Coleta de análises", "busy": "Ocupado", "channel": "Canal", "client_lifecycle": "Ciclo de vida do cliente", "client_lifecycle_one": "{{count}} Ciclo de vida do cliente", "client_lifecycle_other": "{{count}} Ciclos de vida do cliente", "connected": "Conectado", "connection_explanation": "{{connectedCount}} conectado, {{disconnectedCount}} não conectado", "connection_percentage": "{{count}}% conectado", "connection_percentage_explanation": "Porcentagem de todos os dispositivos neste local que estão conectados ({{connectedCount}} conectados, {{disconnectedCount}} não conectados)", "create_board": "Iniciar monitor<PERSON>o", "dashboard": "painel de controle", "delta": "Delta", "device_types": "Tipos", "device_types_explanation": "Tipos de dispositivos de todos os dispositivos disponíveis", "disconnected": "Desconectado", "firmware": "Firmware", "health": "<PERSON><PERSON><PERSON>", "inactive": "Inativo", "interval": "intervalo", "last_connected": "<PERSON><PERSON><PERSON> cone<PERSON>", "last_connected_tooltip": "Última vez que este dispositivo foi conectado ao controlador. <PERSON><PERSON> pode ser usado para estimar quando um dispositivo desconectado", "last_connection": "<PERSON><PERSON><PERSON> cone<PERSON>", "last_contact": "Último contato", "last_disconnection": "Última desconexão", "last_firmware_explanation": "Firmware mais comum em execução nos dispositivos analisados", "last_health": "Última Saúde", "last_ping": "<PERSON><PERSON><PERSON> ping", "live_view": "Visualização ao vivo", "live_view_explanation_five": "Você também pode clicar em qualquer um dos círculos para aumentar o zoom", "live_view_explanation_four": "Você pode passar o mouse sobre qualquer um dos objetos com o mouse para ver informações detalhadas", "live_view_explanation_one": "O gráfico 'Live View' é uma representação visual do seu local.", "live_view_explanation_three": "Local -> AP -> Rádio -> SSID -> UEs", "live_view_explanation_two": "De fora para dentro:", "live_view_help": "Ajuda da visualização ao vivo", "memory": "Memória", "memory_used": "Memória Usada", "missing_board": "O monitoramento analítico neste local não está mais ativo. Clique aqui para reiniciar o monitoramento", "mode": "Modo", "monitoring": "Monitoramento", "no_board": "Sem monitoramento", "no_board_description": "Você não está monitorando este local no momento, clique aqui para começar", "noise": "Barul<PERSON>", "packets": "<PERSON><PERSON>", "radio": "<PERSON><PERSON><PERSON>", "raw_analytics_data": "Dados brutos de análise", "raw_data": "Dados não tratados", "receive": "<PERSON><PERSON><PERSON>", "retention": "Retenção", "retries": "Novas tentativas", "search_serials": "Pesquisar séries", "stop_monitoring": "Parar o monitoramento", "stop_monitoring_success": "Local de monitoramento interrompido!", "stop_monitoring_warning": "Tem certeza? Is<PERSON> a<PERSON> todos os dados de monitoramento gravados para este local", "temperature": "Temperatura", "title": "Analytics", "total_data": "<PERSON><PERSON> to<PERSON>", "total_devices_explanation": "Todos os dispositivos neste local ({{connectedCount}} conectado, {{disconnectedCount}} não conectado)", "total_devices_one": "{{count}} Dispositivo", "total_devices_other": "{{count}} dispositivos", "uptime": "Tempo de atividade"}, "batch": {"batches": "Lotes", "cannot_edit_macs": "Como os trabalhos já foram executados neste lote, você não pode editar seus endereços MAC", "change_warning": "AVISO: você atualizou o modelo ou o fabricante. É altamente recomendável atualizar seus certificados para que eles permaneçam consistentes com este lote escolhendo a opção \"Salvar e atualizar certificados\"", "create": "Criar certificados", "create_certificates": "Criar certificados", "create_certificates_explanation": "Tem certeza de que deseja criar os {{nbCerts}} certificados deste lote?", "create_certificates_title": "Criar certificados de {{name}}", "delete_explanation": "Tem certeza de que deseja excluir este lote? Is<PERSON> revo<PERSON> todos os certificados {{nbCerts}} e os excluirá. Esta operação não é reversível", "delete_title": "Excluir lote {{name}}", "duplicate_in_file": "MAC duplicado encontrado na linha {{firstRow}} e {{secondRow}}: {{mac}}", "emails_to_notify": "E-mails para notificar quando esta tarefa for concluída", "error_push": "Erro ao iniciar o job de alterações push: {{e}}", "general_error_treating_file": "Erro geral ao tratar o arquivo: verifique se ele está no formato .CSV, contém apenas uma coluna sem cabeçalho.", "invalid_mac": "MAC inválido na linha {{row}}: {{mac}}", "mac_count_title": "{{nb}} MACs atualmente fazem parte deste lote", "nb_macs": "{{nb}} MAC", "need_devices": "Você precisa ter pelo menos um certificado para criar!", "parsing_error": "<PERSON>rro de análise na linha {{row}}: {{e}}", "phones_to_notify": "Números de telefone para notificar quando a tarefa for concluída", "push_changes": "Alterações de envio", "push_changes_explanation": "Tem certeza de que deseja enviar as informações do lote para todos os certificados deste lote?", "revoke_explanation": "Tem certeza de que deseja revogar este lote? Is<PERSON> revogará todos os seus {{nbCerts}} certificados. Esta operação não é reversível", "revoke_title": "<PERSON><PERSON><PERSON> {{name}}", "save_and_change": "Salvar e atualizar certificados", "success_push": "Job de alterações push iniciado com sucesso! Número do trabalho: {{job}}", "title": "Lote"}, "certificates": {"certificate": "Certificado", "common_names_explanation": "Precisa de um arquivo .CSV de apenas uma coluna sem nome contendo 12 MACs de dispositivo de dígitos HEX.", "device": "Dispositivo", "device_macs": "MACs do dispositivo", "domain_name": "Nome do domínio", "error_fetching": "Erro ao buscar certificados: {{e}}", "error_revoke": "Erro ao tentar revogar o certificado: {{e}}", "expires_on": "Expira em", "filename": "Nome do arquivo", "invalid_domain": "Os formatos aceitos são: domain.top_level_domain ou sub_domain.domain._top_level_domain", "invalid_mac": "Precisa ter 12 caracteres HEX", "invalid_redirector": "Os formatos aceitos são: example.com, example.com:16000", "mac_address": "Endereço MAC", "macs": "<PERSON>", "manufacturer": "Fabricante", "model": "<PERSON><PERSON>", "redirector": "Redirecionador", "revoke": "REVOGAR", "revoke_count": "<PERSON><PERSON><PERSON> contagem", "revoke_warning": "Tem certeza de que deseja revogar este certificado?", "server": "<PERSON><PERSON><PERSON>", "successful_revoke": "Certificado revogado com sucesso!", "title": "Certificados"}, "commands": {"abort_command_explanation": "Tem certeza de que deseja parar de esperar pelo resultado deste comando?", "abort_command_title": "Comand<PERSON>", "active_scan": "Verificação ativa", "blink": "Piscar", "blink_error": "Erro ao enviar o comando de piscar: {{e}}", "blink_success": "Comando Blink enviado com sucesso!", "channel": "Canal", "confirm_reset": "Iniciar redefinição do dispositivo #{{serialNumber}}", "connect": "Conectar", "rtty": "Rtty", "execution_time": "tempo de execução", "factory_reset": "Dispositivo de redefinição de fábrica", "factory_reset_error": "Erro ao tentar redefinir o dispositivo de fábrica: {{e}}", "factory_reset_success": "Redefinição de fábrica do dispositivo iniciada com sucesso!", "factory_reset_warning": "Nota: Tem certeza de que deseja redefinir a configuração original deste dispositivo? Esta ação não é reversível", "firmware_upgrade": "Atualização de firmware", "firmware_upgrade_error": "Erro ao tentar atualizar o firmware do dispositivo: {{e}}", "firmware_upgrade_success": "Atualização do dispositivo iniciada com sucesso!", "image_date": "<PERSON> da Imagem", "keep_redirector": "Manter redirecionador?", "other": "comandos", "override_dfs": "Substituir DFS", "reboot": "Reiniciar", "reboot_description": "Deseja reiniciar este dispositivo?", "reboot_error": "Erro ao enviar o comando de reinicialização: {{e}}", "reboot_success": "Comando de reinicialização enviado com sucesso!", "revision": "revisão", "scan": "<PERSON><PERSON><PERSON><PERSON>", "signal": "sinal", "upgrade": "<PERSON><PERSON><PERSON>", "wifiscan": "<PERSON><PERSON><PERSON>", "wifiscan_error": "Erro ao tentar verificar o dispositivo: {{e}}", "wifiscan_error_1": "Seu rádio 5G está em um canal de radar, você deve habilitar “Override DFS” para permitir a varredura de todos os canais 5G"}, "common": {"actions": "Ações", "address_search_autofill": "Pesquise locais para preencher automaticamente os campos abaixo", "alert": "<PERSON><PERSON><PERSON>", "all": "Todos", "assign": "Atribuir", "avg": "média", "back": "de volta", "base_information": "Informações básicas", "by": "Por", "cancel": "<PERSON><PERSON><PERSON>", "claim": "Afirmação", "close": "<PERSON><PERSON>", "columns": "Colunas", "command": "Comand<PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm": "confirme", "connected": "Conectado", "copied": "copiado", "copy": "cópia de", "create": "Crio", "create_new": "Crie um novo", "created": "<PERSON><PERSON><PERSON>", "creator": "<PERSON>", "custom": "personalizadas", "daily": "diariamente", "date": "Encontro", "day": "<PERSON>a", "days": "<PERSON><PERSON>", "default": "Padrão", "defaults": "Predefinições", "description": "Descrição", "details": "<PERSON><PERSON><PERSON>", "device_details": "Detalhes do dispositivo", "discard_changes": "Descartar <PERSON>?", "disconnected": "Desconectado", "display_name": "Mostrar nome", "download": "Baixar", "download_instructions": "Baixe com sucesso! Se você não conseguir encontrar o arquivo, confirme se está permitindo downloads deste site", "duplicate": "Dup<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "email": "O email", "empty_list": "Lista Vazia", "end": "Fim", "entries_one": "Entrada", "entries_other": "Entradas", "error": "Erro", "error_claiming_obj": "Erro ao reivindicar {{obj}}", "error_download": "Erro ao tentar fazer o download: {{e}}", "errors": "<PERSON><PERSON><PERSON>", "exit_fullscreen": "<PERSON><PERSON><PERSON>", "export": "Exportar", "finished": "acabado", "fullscreen": "Tela cheia", "general_error": "Erro ao se conectar ao servidor. Consulte seu administrador.", "general_info": "Informação geral", "go_back": "Volte", "go_to_map": "Ir para o mapa", "hide": "Ocultar", "hourly": "Por hora", "identification": "Identificação", "inherit": "<PERSON><PERSON>", "language": "Língua", "last_use": "Usado por último", "lifetime": "Tempo de vida", "locale": "Localidade", "logout": "<PERSON><PERSON>", "main": "a Principal", "make_higher_priority": "Dê maior prioridade", "make_lower_priority": "Faça menor prioridade", "manage": "gerir", "manual": "Manual", "manufacturer": "Fabricante", "map": "Mapa", "max": "máxi<PERSON>", "min": "minuto", "miscellaneous": "Diversos", "mode": "Modo", "model": "<PERSON><PERSON>", "modified": "Modificado", "monthly": "<PERSON><PERSON> mês", "months": "Meses", "my_account": "Minha conta", "name": "Nome", "name_error": "O nome deve ter menos de 50 caracteres", "next": "Próximo", "no": "Não", "no_addresses_found": "Nenhum endereço encontrado", "no_clients_found": "Nenhum cliente encontrado", "no_devices_found": "Nenhum dispositivo encontrado", "no_items_yet": "Nenhum item ainda", "no_obj_found": "Nenhum {{obj}} encontrado", "no_records_found": "Nenhum registro foi encontrado", "no_statistics_found": "Nenhuma estatística encontrada", "no_last_statistics_found": "Nenhuma estatística recente encontrada", "none": "<PERSON><PERSON><PERSON>", "not_found": "404 não encontrado", "note": "<PERSON>a", "notes": "notas", "of": "Do", "password": "<PERSON><PERSON>", "preview": "Visualizar", "quarterly": "Trimestral", "redirector": "Redirecionador", "refresh": "REFRESH", "remove": "Remover", "remove_claim": "Remover reivindicação", "reset": "<PERSON><PERSON><PERSON><PERSON>", "revoked": "Rev<PERSON><PERSON>", "save": "Salve ", "search": "Procurar", "seconds": "<PERSON><PERSON><PERSON>", "select_all": "mostre tudo", "select_value": "Selecione o valor", "sending": "Enviando", "sent_code": "Código enviado!", "show": "exposição", "size": "<PERSON><PERSON><PERSON>", "start": "<PERSON><PERSON><PERSON>", "start_time": "Início", "end_time": "Fim", "started": "Começado", "state": "Estado", "status": "Status", "stop_editing": "<PERSON><PERSON> de editar", "submitted": "Submetido", "success": "Sucesso", "successfully_claimed_obj": "Reivindicado com sucesso {{count}} {{obj}}", "sync": "Sincronizar", "test": "<PERSON>e", "theme": "<PERSON><PERSON>", "time": "tempo", "timestamp": "timestamp", "type": "Tipo", "type_for_options": "Digite o valor que você precisa criar ...", "select": "Selecionar...", "unknown": "Desconhecido", "use_file": "Usar arquivo", "value": "Valor", "variable": "Variável", "view": "Visão", "view_details": "VER DETALHES", "view_in_gateway": "Ver no controlador", "view_json": "Ver JSON", "warning": "Aviso", "warnings": "Advertências", "yearly": "<PERSON><PERSON>", "yes": "sim", "your_new_note": "Sua nova nota"}, "configurations": {"add_interface": "Adicionar interface", "add_radio": "<PERSON><PERSON><PERSON><PERSON>", "add_ssid": "Adicionar SSID", "add_subsection": "Adicionar subseção", "advanced_settings": "Configurações avançadas", "affected_explanation_one": "Existem {{count}} dispositivos afetados por esta configuração", "affected_explanation_other": "Existem {{count}} dispositivos afetados por esta configuração", "applied_configuration": "Configuração aplicada", "cant_delete_explanation": "Não é possível excluir esta configuração porque ela está sendo usada por pelo menos um dispositivo, local ou entidade. Você pode ver quais são clicando no botão ao lado de \"Em uso\" no formulário desta configuração", "cant_delete_explanation_simple": "Não é possível excluir esta configuração porque ela está sendo usada por pelo menos um dispositivo, local ou entidade. Você pode ver quais são indo na página de configuração", "configuration_json": "Configuração JSON", "configuration_push_result": "Resultado de envio de configuração", "configuration_sections": "Seções de configuração", "delete_interface": "Excluir interface", "delete_radio": "Excluir <PERSON>", "delete_ssid": "Excluir SSID", "device_configurations": "Configurações do dispositivo", "device_types": "Tipos de dispositivos", "dhcp_snooping": "Espionagem DHCP", "error_pushes_one": "<PERSON>rro (pode ser devido à configuração incorreta): {{count}}", "error_pushes_other": "<PERSON><PERSON><PERSON> (podem ser devido à configuração incorreta): {{count}}", "expert_name": "MODO EXPERT", "expert_name_explanation": "Você pode usar o modo especialista para modificar diretamente sua configuração, incluindo a adição de valores que não são atualmente suportados pela interface do usuário. Use este formato: { \"interfaces\": [ ... ], \"radios\": { ... }, ...etc }", "explanation": "Explicação", "firewall": "Firewall", "firmware_upgrade": "Atualização de firmware", "globals": "Globals", "health": "<PERSON><PERSON><PERSON>", "hostapd_warning": "Parâmetro de URL, ex.: teste=valor", "import_file": "Importar configuração do arquivo", "import_file_explanation": "Você pode usar a opção abaixo para importar um arquivo JSON de configuração, com conteúdo neste formato:\n{\n     \"interfaces\": [ ... ],\n     \"radios\": { ... },\n     ...etc\n}", "import_warning": "AVISO: Esta operação substituirá todas as seções de configuração que você já criou.", "imported_configuration": "Configuração importada", "in_use_title": "{{name}} Em uso", "interfaces": "Interfaces", "network": "Rede", "interfaces_instruction": "Use uma string JSON válida no seguinte formato: { \"interfaces\": [] }.", "invalid_resource": "Inv<PERSON><PERSON><PERSON> ou Excluir Recurso", "metrics": "Métricas", "no_resource_selected": "Nenhum recurso selecionado", "notification_details": "Atualizado: {{success}}, <PERSON><PERSON><PERSON><PERSON>: {{warning}}, <PERSON><PERSON><PERSON>: {{error}}", "one": "Configuração", "push_configuration": "Configuração de envio", "push_configuration_error": "Erro ao tentar enviar a configuração para o dispositivo: {{e}}", "push_configuration_explanation": "Configuração não enviada, código de erro {{code}}", "push_success": "A configuração foi verificada e um comando \"Configure\" foi iniciado pelo controlador!", "radio_limit": "Você atingiu a quantidade máxima de rádios (5). Você precisa excluir uma das bandas ativadas para adicionar uma nova", "radios": "Rá<PERSON>s", "rc_only": "Liberar apenas candidatos", "save_warnings": "Tem certeza de que deseja salvar sua configuração?", "services": "Serviços", "special_configuration": "Configuração específica do dispositivo", "start_special_creation": "Criar configuração para este dispositivo", "statistics": "Estatisticas", "successful_pushes_one": "Empurrão bem-sucedido: {{count}}", "successful_pushes_other": "<PERSON><PERSON><PERSON><PERSON><PERSON> bem-sucedido<PERSON>: {{count}}", "third_party": "<PERSON><PERSON><PERSON>", "third_party_instructions": "Use uma string JSON válida no seguinte formato: { \"value_name\": \"value\" }.", "title": "configuraç<PERSON><PERSON>", "unit": "Unidade", "system": "Sistema", "view_affected_devices": "Exibir dispositivos afetados", "view_in_use": "Visualização em uso", "warning_pushes_one": "Aguardando a conexão dos dispositivos: {{count}}", "warning_pushes_other": "Aguardando a conexão dos dispositivos: {{count}}", "weight": "Peso", "wifi_bands_max": "<PERSON><PERSON> pode haver mais de 16 SSIDs usando esta banda wi-fi", "wifi_frames": "Quadros WiFi", "multi_psk_key_exsist": "A chave já existe"}, "contacts": {"access_pin": "PIN de acesso", "claim_explanation": "Para reivindicar contatos, você pode usar a tabela abaixo", "first_name": "Primeiro nome", "identifier": "Identificador", "initials": "Iniciais", "last_name": "Último nome", "mobiles": "<PERSON><PERSON><PERSON><PERSON>", "one": "Contato", "other": "Contatos", "phones": "Telefones", "primary_email": "e-mail primário", "salutation": "Saudação", "secondary_email": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>", "to_claim": "Contatos para reivindicar", "visual": "Visual"}, "controller": {"configurations": {"create_success": "Configuração criada!", "delete_success": "A configuração agora foi excluída!", "title": "Configurações padrão", "update_success": "Configuração atualizada!"}, "configure": {"invalid": "Sua nova configuração precisa ser um JSON válido", "success": "A nova configuração está sendo implantada no dispositivo", "title": "Configure via CLI", "warning": "Nota: <PERSON><PERSON><PERSON> a<PERSON>: haver<PERSON> apenas testes mínimos feitos nesta configuração"}, "crud": {"choose_time": "<PERSON><PERSON>do personalizado", "clear_time": "Tempo de limpeza", "delete_success_obj": "Exclu<PERSON><PERSON> {{obj}}"}, "dashboard": {"associations": "Associações", "associations_explanation": "<PERSON><PERSON> as associações conectadas atuais (ou UEs)", "certificates": "Certificados", "certificates_explanation": "Status dos certificados dos dispositivos atualmente conectados", "commands": "comandos", "commands_explanation": "Todos os comandos executados", "device_dashboard_refresh": "Novas estatísticas de conexão", "device_types": "Tipos de dispositivos", "device_types_explanation": "Tipos de dispositivos de todos os dispositivos que apontam para este controlador", "devices_explanation": "Todos os dispositivos estão apontando para este endpoint do controlador", "error_fetching": "Erro ao buscar o painel", "expand": "Expandir", "last_ping_explanation": "Quando esses dados foram coletados", "memory": "Uso de memória", "memory_explanation": "Dispositivos atualmente conectados com a quantidade correspondente de memória usada", "no_certificate": "Sem certificado", "not_connected": "Não conectado", "others": "outras", "overall_health": "Sa<PERSON><PERSON> geral", "overall_health_explanation": "Saúde média de todos os dispositivos atualmente conectados dos quais recebemos dados de saúde. O cálculo exato é: (Dispositivos = 100% * 100 + Dispositivos>= 90 * 95 + Dispositivos>= 60 * 75 + Dispositivos <60 * 30) / Dispositivos conectados", "overall_health_explanation_pie": "O número de dispositivos com uma porcentagem de integridade nessas categorias", "serial_mismatch": "Incompatibilidade de série", "status": "Status", "status_explanation": "Status de todos os dispositivos que apontam para este endpoint do controlador", "unknown_status": "Status não reconhecido", "unrecognized": "Não reconhecido", "uptimes": "", "uptimes_explanation": "Dispositivos atualmente conectados com os tempos de atividade correspondentes", "vendors": "Vendedores", "vendors_explanation": "Fornecedores dos dispositivos que apontam para este controlador", "verified": "Verificado"}, "devices": {"add_blacklist": "Adicionar número de série", "added": "<PERSON><PERSON><PERSON><PERSON>", "added_blacklist": "Número de série adicionado à lista negra!", "average_uptime": "Tempo de atividade médio", "blacklist": "Lista negra", "blacklist_update": "Atualizar registro {{serialNumber}} ", "by": "Por", "capabilities": "Recursos", "command_one": "Comand<PERSON>", "commands": "comandos", "complete_data": "Dados completos", "config_id": "ID de configuração", "connecting": "<PERSON><PERSON><PERSON><PERSON>", "connection_changes": "Status de conexão", "delete_blacklist": "Removido o número de série da lista negra!", "delete_health_explanation": "Isso excluirá permanentemente todas as verificações de integridade antes da data escolhida", "delete_logs_explanation": "Isso excluirá permanentemente todos os logs antes da data que você escolher", "error_code": "Erro de código", "executed": "Executado", "finished_reboot": "{{serialNumber}} acabou de reiniciar!", "finished_upgrade": "{{serialNumber}} concluiu o upgrade!", "from_to": "De {{from}} a {{to}}", "healthchecks": "Verificações de integridade", "last_modified": "Última modificação:", "last_upgrade": "Última atualização", "localtime": "Horário local", "logs": "toras", "new_statistics": "Novas estatísticas", "no_more_available": "Todos recuperados", "reason": "RAZÃO", "results": "resultados", "sent_upgrade_to_latest": "Enviado o comando 'Atualizar para o mais recente' para o dispositivo", "severity": "Gravidade", "show_more": "<PERSON><PERSON> mais", "started_reboot": "{{serialNumber}} desligue para reiniciar!", "started_upgrade": "{{serialNumber}} apenas desligue para iniciar o upgrade!", "trace": "Vestígio", "trace_description": "Nota: Lançar um rastreamento remoto deste dispositivo para uma duração específica ou um número de pacotes", "update_success": "Dispositivo atualizado!", "updated_blacklist": "Lista negra atualizada!"}, "firmware": {"devices_explanation": "Dispositivos que apontaram para este servidor de firmware. <PERSON><PERSON> pode explicar as discrepâncias entre esse número e o do painel do dispositivo", "endpoints": "Pontos finais", "endpoints_explanation": "Todos os endpoints que apontarão para este servidor de firmware", "firmware_age": "Idade do Firmware", "firmware_age_explanation": "Idade média do firmware para os dispositivos para os quais temos esses dados", "latest": "Último firmware instalado", "old_firmware": "Firmware antigo", "ouis_explanation": "OUIs de dispositivos que se conectaram a este servidor de firmware", "outdated_one": "Firmware com {{count}} dias", "outdated_other": "Firmware com {{count}} dias", "outdated_unknown": "Firmware de idade desconhecida", "release": "LANÇAMENTO", "show_dev_releases": "Lançamentos do desenvolvedor", "status_explanation": "Status da conexão dos dispositivos que se conectaram a este servidor de firmware", "unrecognized": "Firmware não reconhecido", "unrecognized_firmware": "Firmware não reconhecido", "unrecognized_firmware_explanation": "Firmware que é usado atualmente por dispositivos e não é reconhecido por este servidor de firmware", "up_to_date": "Dispositivos atualizados", "up_to_date_explanation": "Dispositivos usando o software mais recente disponível para eles"}, "provisioning": {"title": "Provisioning"}, "queue": {"title": "Fila de Eventos"}, "radius": {"calling_station_id": "estação", "disconnect": "Desconectar", "disconnect_success": "Sessão Radius desconectada!", "input_octets": "Entrada", "output_octets": "<PERSON><PERSON><PERSON>", "radius_clients": "C<PERSON><PERSON>", "session_time": "Tempo de sessão", "username": "Nome de usuário"}, "stats": {"load": "Carga (1 | 5 | 15 m.)", "seconds_ago": "{{s}} segundos atrás", "used": "Usava"}, "telemetry": {"duration": "Duração", "interval": "intervalo", "kafka": "Kafka", "kafka_success": "A telemetria Kafka está agora iniciada!", "last_update": "Última atualização", "minutes": "<PERSON><PERSON><PERSON>", "need_types": "Você precisa selecionar pelo menos um tipo", "output": "<PERSON><PERSON>", "seconds_ago": "{{seconds}} segundos atrás", "title": "Telemetria", "types": "Tipos", "websocket": "WebSocket"}, "trace": {"down": "BAIXA", "download": "Baixar rastreamento", "duration": "Duração", "network": "Rede", "packets": "<PERSON><PERSON>", "success": "Rastreamento concluído no dispositivo #{{serialNumber}}. Agora você pode baixar o resultado", "up": "acima", "wait": "Esperar os resultados?"}, "wifi": {"active_ms": "Ativo", "busy_ms": "Ocupado", "channel_width": "Largura do canal", "mode": "Modo", "noise": "Barul<PERSON>", "receive_ms": "<PERSON><PERSON><PERSON>", "rx_rate": "Taxa Rx", "station": "estação", "tx_rate": "Taxa Tx", "vendor": "forne<PERSON><PERSON>", "wifi_analysis": "<PERSON><PERSON><PERSON><PERSON> de Wi-Fi"}}, "crud": {"add": "<PERSON><PERSON><PERSON><PERSON>", "confirm_cancel": "Tem certeza de que deseja descartar as alterações feitas?", "confirm_delete_obj": "Tem certeza de que deseja excluir este {{obj}}?", "create": "Crio", "create_object": "<PERSON><PERSON> {{obj}}", "delete": "Excluir", "delete_confirm": "Tem certeza de que deseja excluir este {{obj}}?", "delete_obj": "Excluir {{obj}}", "edit": "<PERSON><PERSON>", "edit_obj": "Editar {{obj}}", "error_create_obj": "Erro ao criar {{obj}}: {{e}}", "error_delete_obj": "Erro ao excluir {{obj}}: {{e}}", "error_fetching_obj": "Erro ao buscar {{obj}}: {{e}}", "error_revoke_obj": "Erro ao revogar {{obj}}: {{e}}", "error_update_obj": "Erro ao atualizar {{obj}}: {{e}}", "success_create_obj": " {{obj}}criado com sucesso!", "success_delete_obj": "Excluído com sucesso {{obj}}!", "success_revoke_obj": " {{obj}}revogado com sucesso!", "success_update_obj": " {{obj}}atualizado com sucesso!"}, "devices": {"all": "Todos", "associations": "Associações", "certificate_expires_in": "Certificado expira em", "certificate_expiry": "Certificado expira em", "connected": "Conectado", "crash_logs": "Registros de falhas", "create_errors": "erros ao tentar criar dispositivos", "create_success": " dispositivos criados com sucesso", "current_firmware": "Firmware atual", "device_type_not_found": "Tipo de dispositivo não reconhecido", "duplicate_serial": "Número de série duplicado no arquivo", "error_rtty": "Erro ao tentar se conectar ao dispositivo: {{e}}", "file_errors": "dispositivos problemáticos", "found_assigned": "dispositivos já atribuídos", "found_not_assigned": "dispositivos já existentes, mas agora pertencentes", "import_batch_tags": "Dispositivos de importação", "import_device_warning": "Certifique-se de que não há espaços extras no início ou no final de nenhum valor, a menos que faça parte do valor desejado", "import_explanation": "Para importar dispositivos em massa, você precisa usar um arquivo CSV com as seguintes colunas: SerialNumber, DeviceType, Name, Description, Note", "invalid_serial_number": "Número de série inválido (precisa ter 12 caracteres HEX)", "logs_one": "Registro", "new_devices": "novos dispositivos", "no_model_image": "Nenhuma imagem de modelo encontrada", "not_connected": "Não conectado", "not_found_gateway": "Erro: o dispositivo ainda não se conectou ao gateway", "notifications": "Notificações do dispositivo", "one": "Dispositivo", "reassign_already_owned": "Reatribuir dispositivos que já existem e são de propriedade de outra entidade/local/assinante?", "reboot_logs": "Registros de reinicialização", "restricted": "Restrito", "restricted_overriden": "Este é um dispositivo restrito, mas está em modo de desenvolvimento. <PERSON><PERSON> as restrições são atualmente ignoradas", "restrictions_overriden_title": "<PERSON><PERSON>", "sanity": "Sanidade", "start_import": "Iniciar importação de dispositivos", "test_batch": "Dados de importação de teste", "test_results": "Resul<PERSON><PERSON> dos testes", "title": "Devices", "treating": "Teste:", "unassigned_only": "Apenas não atribuído", "update_error": "erros ao tentar atualizar dispositivos", "update_success": "dispositivos atualizados com sucesso"}, "entities": {"active": "Ativo", "add_configurations": "Adicionar configura<PERSON>", "add_ips": "Adicionar IPs", "add_ips_explanation": "Você pode adicionar endereços IPv4 ou IPv6 nos seguintes formatos", "api_key": "Chave API", "cant_delete_explanation": "Não é possível excluir esta entidade porque ela possui entidades filhas e/ou locais. Você precisa excluir todos os filhos desta entidade antes de excluí-la", "claim_device_explanation": "Para reivindicar dispositivos, você pode usar a tabela abaixo. Se um dispositivo já foi reivindicado por outra entidade ou local, também cancelaremos a atribuição antes de atribuí-lo a essa entidade", "client_enrollment_profile": "Perfil de inscrição do cliente", "create_child_entity": "Criar entidade filha", "create_root": "Criar Entidade Raiz", "create_root_explanation": "Insira as informações necessárias para criar a entidade raiz do seu serviço de provisionamento. Esta informação pode ser modificada após a criação", "current_state": "Estado atual", "default_redirector": "Redirecionador padrão", "devices_to_claim": "Novos dispositivos para reivindicar", "devices_under_root": "Os dispositivos não podem ser criados diretamente na entidade raiz. Crie novas entidades ou locais e crie dispositivos sob eles.", "enter_ips": "Digite o (s) IP (s) que deseja adicionar aqui", "entity": "Entidade", "error_sync": "Erro ao tentar iniciar a sincronização de {{name}}: {{e}}", "failed_test": "Falha nos testes com credenciais DigiCert, verifique as informações da sua entidade", "initial_state": "Estado inicial", "ip_cidr": "IP/número (exemplo: 10.0.0.0/8)", "ip_detection": "Detecção de IP", "ip_list": "Lista: IP, IP IP", "ip_range": "Faixa: IP-IP", "ip_single_address": "Endereço único: IP", "one": "Entidade", "organization": "Organização", "server_enrollment_profile": "Perfil de inscrição do servidor", "status": "Status", "sub_one": "Subentidade", "sub_other": "Subentidades", "success_sync": "Sincronização de {{name}}iniciada com sucesso!", "success_test": "O teste das credenciais DigiCert desta entidade foi bem-sucedido!", "suspended": "Suspenso", "sync_explanation": "Deseja sincronizar esta entidade? Isso pode demorar um pouco dependendo da quantidade de certificados pertencentes a esta entidade.", "sync_title": "Sincronizar {{name}}", "test_digicert_creds": "Credenciais de teste", "title": "Entidades", "tree": "Árvore de entidades", "update_success": "Entidade atualizada!", "venues_under_root": "Os locais não podem ser criados diretamente na entidade raiz"}, "firmware": {"confirm_default_data": "Confirme as informações abaixo e clique em 'Confirmar' quando estiver pronto para iniciar o processo", "create_success": "Criou novas configurações de firmware padrão!", "db_update_warning": "Esta operação é feita automaticamente diariamente sem necessidade de usar esta atualização manual. A atualização deste banco de dados pode levar até 25 minutos", "default_created_error_one": "{{count}} erro ao tentar criar uma nova configuração", "default_created_error_other": "{{count}} erros ao tentar criar uma nova configuração", "default_created_one": "{{count}} configuração de firmware padr<PERSON> criada", "default_created_other": "{{count}} configurações de firmware padr<PERSON> criadas", "default_found_one": "Revisão válida encontrada para {{count}} tipo de dispositivo", "default_found_other": "Foram encontradas revisões válidas para {{count}} tipos de dispositivo", "default_mass_delete_success_one": "Configuração de firmware padrão {{count}} excluída!", "default_mass_delete_success_other": "Excluídas {{count}} configurações de firmware padrão!", "default_not_found_one": "Nenhuma versão de firmware válida para {{count}} tipo de dispositivo", "default_not_found_other": "Nenhuma versão de firmware válida para {{count}} tipos de dispositivo", "default_title": "", "default_update_success": "Firmware padrão atualizado para {{deviceType}}!", "delete_success": "Configuração de firmware padrão excluída!", "edit_default_title": "Este é o firmware atual usado como versão mínima para novos APs do tipo {{deviceType}}. Se um novo AP {{deviceType}} se conectar ao gateway, ele será atualizado automaticamente para esta versão.", "fetching_defaults": "Buscando todo o firmware disponível para os tipos de dispositivos selecionados...", "last_db_update_modal": "banco de dados de firmware", "last_db_update_title": "base de dados", "one": "Firmware", "select_default_device_types": "Selecione todos os tipos de dispositivos que deseja segmentar com esta nova regra de firmware padrão. Se você não conseguir encontrar o tipo de dispositivo desejado, significa que eles já têm uma regra aplicada.", "select_default_revision": "Agora você pode selecionar a revisão mínima para a qual deseja que seus tipos de dispositivo sejam direcionados", "start_db_update": "Iniciar atualização do banco de dados", "started_db_update": "Atualização do banco de dados iniciada, esta operação deve levar até 25 minutos para ser concluída", "update_success": "Informações de firmware padrão salvas!"}, "footer": {"powered_by": "Distribuído por", "version": "Vers<PERSON>"}, "form": {"captive_web_root_explanation": "Por favor, use apenas arquivos .tar (sem arquivos compactados como .targz, por exemplo)", "certificate_file_explanation": "Use um arquivo .pem que comece com \"-----BEGIN CERTIFICATE-----\" e termine com \"-----END CERTIFICATE-----\"", "invalid_alphanumeric_with_dash": "Caracteres aceitos. são apenas alfanuméricos (letras e números)", "invalid_cidr": "Endereço CIDR IPv4 inválido. Exemplo: ***********/12", "invalid_email": "E-mail inválido", "invalid_file_content": "Conteúdo de arquivo inválido. Confirme se está no formato válido", "invalid_fqdn_host": "Nome de host FQDN inválido", "invalid_hostname": "Nome de host inválido: precisa ser composto apenas de caracteres alfanuméricos e traços", "invalid_icon_lang": "Idioma inválido, deve estar em formato de 3 letras (eng, fre, ger, ita, etc.)", "invalid_ieee": "Para este protocolo de criptografia, ieee80211w precisa ser 'opcional' ou 'obrigatório'", "invalid_ieee_required": "ieee80211w precisa ser 'obrigatório' para este protocolo de criptografia", "invalid_interfaces": "Sequência JSON de interfaces inválida. Confirme se seu valor é: JSON válido e tem interfaces como sua única chave e que o valor de interfaces é uma matriz. Exemplo: {\"interfaces\": []}", "invalid_ipv4": "Endereço IPv4 inválido (ex.: ***********/16)", "invalid_ipv6": "Endereço IPv6 inválido (ex.: fd00::/64)", "invalid_json": "Sequência JSON inválida", "invalid_lease_time": "Valor de tempo de locação inválido! Eles precisam estar no formato digitUnit. Por exemplo: 6d2h5m, que significa 6 dias, 2 horas e 5 minutos. Aqui estão as unidades aceitas: m, h, d. Se você não quiser usar uma unidade, omita-a completamente. Então, em vez de dizer 0d2h0m, use 2h", "invalid_mac_uc": "Valor MAC inválido, por exemplo: 00:00:5e:00:53:af", "duplicate_mac": "Este endereço MAC já existe na lista", "duplicate_ip": "Este endereço IP já existe na lista", "invalid_password": "<PERSON><PERSON>, consulte a política de senha", "invalid_phone_number": "Número de telefone inválido", "invalid_phone_numbers": "Um ou mais números de telefone são inválidos. Forneça-os sem símbolos e espaços ou neste formato: +1(123)123-1234", "invalid_port_range": "Valor de porta inválido. Ele precisa ser maior que 0 e menor que 65.535. Se estiver usando um intervalo de portas, certifique-se de que a segunda porta seja um número maior que o primeiro.", "invalid_port_ranges": "Combinação de intervalo de portas inválida! Certifique-se de que ambos os valores de porta sejam do mesmo tipo (único ou intervalo). Se forem intervalos, certifique-se de que ambos estejam cobrindo a mesma quantidade de portas", "invalid_proto_6g": "Este protocolo de criptografia não pode ser usado em um SSID que usa 6G", "invalid_proto_passpoint": "Este protocolo de criptografia não pode ser usado com um SSID de ponto de acesso. Por favor, selecione um protocolo que pode usar Radius", "invalid_select_ports": "Valores incompatíveis entre interfaces! Certifique-se de que não há combinação duplicada de PORT/VLAN ID entre suas interfaces", "invalid_static_ipv4_d": "Endereço inválido, este intervalo está reservado para multicasting (classe D). O primeiro octeto deve ser 223 ou inferior", "invalid_static_ipv4_e": "Endereço inválido, este intervalo é reservado para experimentos (classe E). O primeiro octeto deve ser 223 ou inferior", "invalid_third_party": "String JSON de terceiros inválida. Confirme se seu valor é um JSON válido", "key_file_explanation": "Use um arquivo .pem que comece com \"-----BEGIN PRIVATE KEY-----\" e termine com \"-----END PRIVATE KEY-----\"", "max_length": "Comprimento máximo de {{max}} caracteres.", "max_value": "Valor máximo de {{max}}", "min_length": "Comprimento mínimo de {{min}} caracteres.", "min_max_string": "O valor precisa ter um comprimento entre {{min}} (inclusive) e {{max}} (inclusive)", "min_value": "<PERSON><PERSON> mínimo de {{min}}", "missing_interface_upstream": "Você precisa ter pelo menos uma interface de modo ponte (ponte de camada 2). No momento, todas as suas interfaces estão no modo de roteamento (NAT)", "new_email_to_notify": "Novo e-mail para notificar", "new_phone_to_notify": "Novo telefone para notificar", "not_selected": "não selecionado", "not_uploaded_yet": "ainda não carregado", "pem_file_explanation": "Por favor, use um arquivo .pem", "required": "Requeridos", "using_file": "(usando o arquivo: {{filename}})", "value_recorded_no_filename": "Valor registrado, sem nome de arquivo", "invalid_mac_format": "O formato legal do Mac só pode conter letras minúsculas e números, com um delimitador de ':'. Por favor, use o formato: 00:00:5e:00:53:af", "must_be_integer": "Deve ser um número inteiro", "range_min": "O valor deve ser pelo menos {{min}}", "range_max": "O valor não deve ser maior que {{max}}", "range_both": "O valor deve estar entre {{min}} e {{max}}", "invalid_label_name": "O nome da etiqueta só pode conter letras e números", "invalid_static_ipv4_loopback": "Endereço inválido, o intervalo de endereços loopback não pode ser usado", "invalid_domain_or_ip": "<PERSON>ó pode ser um IP ou nome de domínio, por exemplo: www.fs.com"}, "inventory": {"computed_configuration": "Configuração computada", "dev_class": "Classe do dispositivo", "device_type": "Tipo de dispositivo", "error_reboots": "Erro ao enviar comando: {{count}}", "error_remove_claim": "Erro ao remover a reivindicação: {{e}}", "error_upgrades": "Erro ao enviar comando de atualização: {{count}}", "invalid_serial_number": "Número de série inválido. Um número de série deve ter apenas 12 caracteres HEX (A-F, 0-9)", "invalid_device_name": "Nome de dispositivo inválido. Deve ter 1-2 caracteres alfanuméricos, ou 3-63 caracteres alfanuméricos com hífens (não pode começar ou terminar com hífen)", "no_computed": "Nenhuma configuração computada: você precisará atribuir uma configuração válida para vê-la", "no_firmware": "Nenhum firmware disponível para o tipo de dispositivo: {{count}}", "not_connected": "Dispositivo não conectado: {{count}}", "parent": "<PERSON><PERSON>", "serial_number": "Número de série", "skipped_upgrades": "Atualizações ignoradas: {{count}}", "success_remove_claim": "Reivindicação removida com sucesso em: {{serial}}", "successful_reboots": "Reinicialização iniciada: {{count}}", "successful_upgrades": "Atualizações bem-suced<PERSON>: {{count}}", "tag_one": "Tag", "tags": "Tags de inventário", "title": "Inventário", "warning_reboots": "Não conectado: {{count}}", "warning_upgrades": "Dispositivos não conectados: {{count}}", "label": "Etiqueta", "site": "Site", "create_label": "Criar etiqueta"}, "jobs": {"error_macs": "Erro de MAC", "job": "<PERSON><PERSON><PERSON><PERSON>", "job_details": "Detalhes do trabalho", "notify_emails": "Notificar e-mails", "notify_sms": "Notificar SMS", "successful_macs": "MACs de sucesso", "title": "Empregos"}, "keys": {"description_error": "A descrição precisa ter menos de 64 caracteres", "expire_error": "A expiração não pode ser superior a um ano no futuro", "expires": "expira", "max_keys": "Teclas máxi<PERSON> (10)", "name_error": "O nome deve ser único e ter entre 6 e 20 caracteres alfanuméricos", "one": "Chave API", "other": "<PERSON><PERSON>"}, "locations": {"address_line_one": "Linha de endereço um", "address_line_two": "Linha de endereço dois", "building_name": "nome do edifício", "city": "Cidade", "claim_explanation": "Para reivindicar locais, você pode usar a tabela abaixo", "country": "<PERSON><PERSON>", "elevation": "elevação", "geocode": "Código geográ<PERSON>o", "lat": "Latitude", "longitude": "Longitude", "one": "Localização", "other": "Localizações", "postal": "CEP / Código Postal", "state": "Estado / Província", "title": "Localizações", "to_claim": "Locais para reivindicar", "view_gps": ""}, "login": {"access_policy": "Política de Acesso", "change_password_error": "<PERSON><PERSON> reje<PERSON>, talve<PERSON> seja uma senha antiga", "change_password_explanation": "Digite e confirme sua nova senha", "change_your_password": "<PERSON><PERSON>", "confirm_new_password": "confirme a nova senha", "email_instructions": "Você deve receber em breve um código de 6 dígitos em seu endereço de e-mail. Se você não conseguir encontrá-lo, verifique sua pasta de spam.", "error_sending_code": "Erro ao tentar enviar código: {{e}}", "forgot_password": "Esque<PERSON>u a senha?", "forgot_password_instructions": "Digite seu endereço de e-mail para receber um e-mail contendo as instruções para redefinir sua senha", "forgot_password_successful": "Em breve, você receberá um e-mail com as instruções para redefinir sua senha. Certifique-se de verificar o seu spam se você não conseguir encontrar o e-mail", "forgot_password_title": "Esqueceu a senha", "google_instructions": "Insira o código de 6 dígitos do seu aplicativo Google Authenticator. Se estiver perto de expirar, você pode esperar por um novo", "invalid_credentials": "Credenciais inválidas, confirme se você está usando o e-mail e a senha corretos.", "invalid_mfa": "Código inválido! Por favor, tente novamente", "login_explanation": "Digite seu e-mail e senha para entrar", "new_password": "Nova senha", "password_policy": "Política de Senha", "remember_me": "Lembre de mim", "resend": "REENVIAR", "resent_code": "Código reenviado com sucesso!", "reset_password": "<PERSON><PERSON><PERSON><PERSON>", "sign_in": "assinar em", "sms_instructions": "Você deve receber um código de 6 dígitos em seu telefone em breve. Por favor, insira-o abaixo para fazer login", "suspended_error": "Conta suspensa, entre em contato com seu administrador", "verification": "Verifique seu login", "waiting_for_email_verification": "Conta ainda não validada por e-mail. Verifique sua caixa de entrada ou peça ao administrador para reenviar uma validação", "welcome_back": "Bem vindo de volta!", "your_email": "Seu endereço de email", "your_new_password": "Sua nova senha", "your_password": "<PERSON><PERSON> se<PERSON>a"}, "logs": {"configuration_upgrade": "Atualização de configuração", "device_firmware_upgrade": "Atualização de firmware", "device_statistics": "Estatísticas do dispositivo", "export": "Exportar", "filter": "Filtro", "firmware": "Firmware", "global_connections": "Conexões Glo<PERSON>is", "level": "Nível", "message": "mensagem", "one": "Registro", "receiving_types": "Filtro de notificações", "security": "SEGURANÇA", "source": "Fonte", "thread": "FIO", "venue_config": "Configuração", "venue_reboot": "Reiniciar", "venue_upgrade": "<PERSON><PERSON><PERSON>"}, "map": {"auto_align": "Alinhamento Automático", "auto_map": "Mapa automático", "by_others": "Mapas de outros", "cumulative_devices": "Dispositivos cumulativos", "default_map": "Mapa Padrão", "delete_warning": "Tem certeza de que deseja excluir este mapa? Esta operação não é reversível", "duplicating": "Mapa duplicado", "my_maps": "<PERSON><PERSON> mapas", "other": "Mapas", "root": "Raiz", "root_node": "<PERSON><PERSON>", "set_as_default": "Definir como padrão", "title": "Mapa", "visibility": "visibilidade"}, "notification": {"one": "Notificação", "other": "Notificações"}, "openroaming": {"pool_strategy": "Estratégia de pool", "radius_endpoint_one": "Ponto final do raio", "radius_endpoint_other": "Pontos finais de raio"}, "operator": {"delete_explanation": "Tem certeza de que deseja excluir este operador? Esta operação não é reversível", "delete_operator": "Excluir operador", "import_location_from_device": "Importar de outro dispositivo", "one": "OPERADOR", "operator_one": "OPERADOR", "operator_other": "Operadores", "other": "Operadores", "registration_id": "ID do registro"}, "organization": {"my_organization": "Minha organização", "title": "Organização"}, "overrides": {"delete_source": "Excluir todas as substituições de {{source}}", "ignore_overrides": "Ignorar substituições de configuração", "name_error": "O parâmetro já está definido pela sua fonte", "one": "Substituição de configuração", "other": "Substituições de configuração", "param_name": "parâmetro", "param_value": "Valor", "parameter": "parâmetro", "reason": "RAZÃO", "reason_error": "Seu motivo precisa ter menos de 64 caracteres. grandes", "source": "Fonte", "tx_power_error": "A potência Tx precisa estar entre 1 e 32", "update_success": "Substituições de configuração atualizadas!", "value": "Valor"}, "profile": {"about_me": "Sobre mim", "activate": "", "add_new_note": "<PERSON><PERSON><PERSON><PERSON> nota", "deactivate": "Desativar", "delete_account": "Excluir meu perfil", "delete_account_confirm": "<PERSON><PERSON><PERSON><PERSON> to<PERSON> as minhas informações", "delete_warning": "Esta ação é irreversível. Todas as suas informações de perfil e suas chaves de API serão removidas", "deleted_success": "Seu perfil agora foi excluído, agora vamos desconectar você...", "disabled": "Desativado", "enabled": "ativado", "manage_avatar": "Gerenciar Avatar", "new_password": "Nova senha", "new_password_confirmation": "confirme a nova senha", "your_profile": "Seu perfil"}, "resources": {"configuration_resource": "recurso", "title": "Recursos", "variable": "Variável"}, "restrictions": {"algo": "Algoritmo de Assinatura", "allowed": "Permitido", "countries": "países permitidos", "developer": "<PERSON><PERSON>", "dfs": "Substituição DFS", "gw_commands": "Comandos de gateway", "identifier": "Identificador", "key_verification": "Informações Chave de Assinatura", "restricted": "Restrito", "signed_upgrade": "Somente atualização assinada", "title": "RESTRIÇÕES", "tty": "Acesso TTY"}, "roaming": {"account_created": "Nova conta criada!", "account_deleted": "Conta excluída!", "account_one": "Conta", "account_other": "<PERSON><PERSON>", "certificate_deleted": "Certificado excluído!", "certificate_one": "Certificado", "certificate_other": "Certificados", "city": "Cidade", "common_name": "Nome comum", "country": "<PERSON><PERSON>", "global_reach": "Alcance global", "global_reach_account_id": "ID da conta", "invalid_certificate": "Certificado inválido", "invalid_key": "Chave privada inválida", "location_details_title": "Localização", "organization": "Organização", "private_key": "Chave privada", "province": "<PERSON><PERSON><PERSON><PERSON>", "state": "Estado"}, "rrm": {"algorithm": "Algoritmo", "algorithm_other": "Algoritmos", "cant_save_custom": "Não é possível criar ou editar configurações personalizadas de RRM até que o servidor RRM esteja acessível. Consulte seu administrador", "cron_error": "Erro ao analisar a expressão CRON: confirme se é válida", "cron_scheduler": "Agendador CRON", "cron_templates": "modelos", "no_algos": "Não é possível buscar algoritmos RRM no momento", "no_providers": "Não podemos buscar provedores de RRM no momento", "param_error": "Seus parâmetros não respeitam as regras deste algoritmo. Por favor, veja os exemplos e detalhes do algoritmo", "parameters": "Parâmetros", "vendor": "forne<PERSON><PERSON>", "version": "Vers<PERSON>"}, "script": {"author": "<PERSON>", "automatic": "Automático", "create_success": "O script agora está criado e pronto para uso!", "custom_domain": "<PERSON><PERSON><PERSON>", "deferred": "Diferido", "device_title": "Executar script", "diagnostics": "Diagnós<PERSON><PERSON>", "explanation": "Execute um script personalizado neste dispositivo e baixe seus resultados", "file_not_ready": "O resultado ainda não foi carregado, volte mais tarde", "file_too_large": "Selecione um arquivo com menos de 500 KB", "helper": "Documentação", "no_script_available": "Nenhum script disponível para sua função de usuário", "now": "agora", "one": "Roteiro", "other": "<PERSON><PERSON><PERSON>", "restricted": "Usuários autorizados a executar este script", "schedule_success": "Execução de script agendada!", "signature": "Assinatura", "started_execution": "Execução do script iniciada, venha mais tarde para os resultados!", "timeout": "Tempo esgotado", "update_success": "Roteiro atualizado!", "upload_destination": "Destino de upload de resultados", "upload_file": "Subir arquivo", "visit_external_website": "VER DOCUMENTAÇÃO", "when": "Agendar Execução"}, "service": {"billing_code": "código de cobrança", "billing_frequency": "Freqüência de cobrança", "class_one": "Classe de serviço", "class_other": "Classes de serviço", "cost": "Custo", "one": "Classe de serviço", "other": "Classes de serviço"}, "simulation": {"cancel": "Cancelar simula<PERSON>", "cancel_explanation": "Pare a simulação e apague seu registro", "cancel_success": "Parou a simulação e apagou seu registro!", "client_interval": "Intervalo do Cliente", "concurrent_devices": "Dispositivos Simultâneos", "controller": "Controlador", "current_live_devices": "Dispositivos ativos atuais", "currently_running_one": "Atualment<PERSON>, há {{count}} simulação em execução", "currently_running_other": "Existem atualmente {{count}} simulações em execução", "delete_devices_confirm": "Tem certeza de que deseja remover todos os dispositivos e suas estatísticas do gateway? Esta ação não é reversível", "delete_devices_loading": "Este processo pode levar até 5 minutos", "delete_simulation_devices": "Apagar dispositivos", "delete_success": "Simulação excluída!", "duration": "Duração", "error_devices": "Dispositivos de Erro", "healthcheck_interval": "Intervalo de verificação de saúde", "infinite": "Infinito", "keep_alive": "Mantenha vivo", "mac_prefix": "Prefixo MAC", "mac_prefix_length": "Seu prefixo MAC precisa ter 6 dígitos HEX válidos (ex.: 00112233)", "max_associations": "Máx. Associações", "max_clients": "Máx. Clientes", "min_associations": "<PERSON><PERSON>", "min_clients": "<PERSON><PERSON>", "no_sim_running": "Nenhuma simulação em execução no momento", "one": "Simulação", "other": "Simulações", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realtime_data": "Dados em tempo real", "realtime_messages": "Mensagens em tempo real", "reconnect_interval": "Intervalo de reconexão", "result_delete_success": "Resultado del<PERSON>do!", "rx": "recebido", "rx_messages": "Mensagens Rx", "sim_currently_running": "Simulação \"{{sim}}\" em andamento", "sim_history": "{{sim}} execuções anteriores", "simulated": "<PERSON><PERSON><PERSON><PERSON>", "start": "Iniciar simula<PERSON>", "start_success": "Corrida de simulação iniciada!", "state_interval": "Intervalo de estado", "stop": "Parar simula<PERSON>", "stop_success": "Simulação parada!", "threads": "Tópicos", "time_to_full": "Tempo para dispositivos completos", "tx": "Transmitido", "tx_messages": "Mensagens Tx", "view_previous_runs": "Ver execuções anteriores"}, "statistics": {"last_stats": "Últimas estatísticas", "latest": "Estatísticas mais recentes", "memory": "Memória"}, "subscribers": {"billing_contact_info": "Detalhes de cobrança e contato", "claim_device_explanation": "Para reivindicar dispositivos, você pode usar a tabela abaixo. Se um dispositivo já foi reivindicado por um usuário, você precisará acessar os detalhes dele e cancelar a atribuição antes de reivindicá-lo.", "devices_claimed_one": "{{count}} Dispositivo reivindicado", "devices_claimed_other": "{{count}} Dispositivos reivindicados", "devices_to_claim_one": "{{count}} Dispositivo para reivindicar", "devices_to_claim_other": "{{count}} Dispositivos para reivindicar", "error_claiming": "Erro ao reivindicar: {{serials}}", "error_removing_claim": "Erro ao remover reivindicações em: {{serials}}", "no_subscribers_found": "Nenhum assinante encontrado", "one": "<PERSON><PERSON><PERSON>", "other": "Inscritos", "reactivate_explanation": "Tem certeza de que deseja reativar este assinante?", "reactivate_title": "Reativar assinante", "title": "Inscritos"}, "system": {"advanced": "Avançado", "backend_logs": "Registros de back-end", "configuration": "Configuração", "could_not_retrieve": "Erro: não foi possível recuperar {{name}} informações do sistema", "endpoint": "Ponto final", "hostname": "Nome de anfitrião", "info": "Informação do sistema", "level": "nível de log", "logging": "Exploração madeireira", "no_log_levels": "Nenhum nível de registro relatado", "os": "Sistema Operacional", "processors": "Processadores", "reload_chosen_subsystems": "Recarregar Subsist<PERSON><PERSON>", "secrets": "<PERSON><PERSON><PERSON><PERSON>", "secrets_create": "<PERSON><PERSON><PERSON>", "secrets_one": "<PERSON><PERSON><PERSON>", "services": "Serviços", "start": "<PERSON><PERSON><PERSON>", "subsystems": "Subsistemas", "success_reload": "Comando de recarga enviado com sucesso!", "systems_to_reload": "Escolha sistemas para recarregar", "title": "Sistema", "update_level_success": "Níveis de log atualizados!", "update_levels": "<PERSON><PERSON><PERSON><PERSON>", "uptime": "Tempo de atividade", "version": "Vers<PERSON>"}, "table": {"columns": "Colunas", "columns_hidden_one": "{{count}} Coluna oculta", "columns_hidden_other": "{{count}} Colunas ocultas", "display_column": "Exibição", "drag_always_show": "Você não pode ocultar esta coluna, mas pode alterar sua posição", "drag_explanation": "Arraste e solte para reordenar e alterar a visibilidade da coluna", "drag_locked": "Esta coluna está travada em sua posição", "export_current_page": "<PERSON><PERSON> página atual", "first_page": "Primeira Página", "go_to_page": "Vá para página", "hide_column": "Ocultar", "last_page": "Última Página", "next_page": "Próxima página", "page": "<PERSON><PERSON><PERSON><PERSON>", "preferences": "Preferências de Tabela", "previous_page": "Página anterior", "reset": "Reiniciar preferências", "settings": "Definições"}, "user": {"email_not_validated": "e-mail não validado", "error_fetching": "Erro ao buscar informações do usuário: {{e}}", "password": "<PERSON><PERSON>", "role": "Função", "suspended": "Suspenso", "title": "Do utilizador"}, "users": {"change_password": "Forçar <PERSON>", "email_validation": "Validação de E-mail", "error_fetching": "Erro ao buscar usuários: {{e}}", "error_sending_validation": "Erro ao enviar validação de e-mail: {{e}}", "last_login": "<PERSON><PERSON>imo login", "login_id": "Identificação de usuário", "one": "Do utilizador", "re_validate_email": "Revalidar e-mail", "reactivate_user": "Reati<PERSON> usuá<PERSON>", "reset_mfa": "Redefinir MFA", "reset_mfa_success": "Redefinir o MFA do usuário com sucesso!", "reset_password": "<PERSON><PERSON><PERSON><PERSON>", "reset_password_error": "Erro ao tentar redefinir a senha do usuário: {{e}}", "reset_password_success": "E-mail de redefinição de senha enviado com sucesso para o endereço de e-mail do usuário", "role": "Função", "send_validation": "Enviar validação de e-mail", "send_validation_explanation": "Deseja reenviar o link de verificação de e-mail?", "stop_suspension": "<PERSON><PERSON><PERSON>", "success_sending_validation": "Email de validação enviado!", "suspend": "Suspender", "suspend_success": "O usuário agora está suspenso", "suspended": "Suspenso", "title": "Comercial", "waitiing_for_email_verification": "Email não verificado"}, "venues": {"confirm_remove_contact": "Deseja remover este contato deste local?", "create_child": "Criar local filho", "error_remove_contact": "Erro ao tentar remover o contato: {{e}}", "error_update_devices": "Erro ao iniciar a atualização do dispositivo: {{e}}", "go_to_page": "Vá para página", "one": "Local", "reboot_all_devices": "Reiniciar todos os dispositivos", "sub_one": "Sublocal", "sub_other": "Sub-locais", "subvenues": "Sub-locais", "successfully_reboot_devices": "Reiniciando {{num}} dispositivos!", "successfully_removed_contact": "Contato removido com sucesso!", "successfully_update_devices": "Atualizando {{num}} dispositivos!", "title": "Locais", "update_all_devices": "<PERSON><PERSON><PERSON><PERSON> to<PERSON> as configurações do dispositivo", "update_success": "Local atualizado!", "upgrade_all_devices": "Atualize o firmware de todos os dispositivos", "upgrade_all_devices_error": "Erro ao atualizar dispositivos: {{e}}", "upgrade_all_devices_success": "Atualização de dispositivos iniciada com sucesso!", "upgrade_options_available": "Aqui estão todas as revisões disponíveis, selecione aquela para a qual você deseja que TODOS os dispositivos deste local sejam atualizados", "use_existing": "Usar existente", "use_existing_contacts": "Usar contatos existentes", "use_this_contact": "Use este contato"}}