INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('CPU Used Percentage', 'S5800-48T4S', '*******.4.1.52642.*******.0', 'get', NULL,
                    'metric', '%', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Memory Used Percentage', 'S5800-48T4S', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Memory Usage Size', 'S5800-48T4S', '*******.4.1.52642.********.0', 'get', NULL,
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Memory Size', 'S5800-48T4S', '*******.4.1.52642.*******.0', 'get', NULL,
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Name', 'S5800-48T4S', '*******.*******.1.2', 'get', 'ifIndex',
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Out Packets Discarded', 'S5800-48T4S', '*******.*******.1.19', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('In Packets with Errors', 'S5800-48T4S', '*******.*******.1.14', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('In Packets Discarded', 'S5800-48T4S', '*******.*******.1.13', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Out Packets with Errors', 'S5800-48T4S', '*******.*******.1.20', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bits Sent', 'S5800-48T4S', '*******.2.********.1.10', 'get', 'ifIndex',
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bits Received', 'S5800-48T4S', '*******.2.********.1.6', 'get', 'ifIndex',
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Speed', 'S5800-48T4S', '*******.*******.1.5', 'get', 'ifIndex',
                    'metric', 'bps', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Status', 'S5800-48T4S', '*******.*******.1.8', 'get', 'ifIndex',
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface MTU', 'S5800-48T4S', '*******.*******.1.4', 'get', 'ifIndex',
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Physical Address', 'S5800-48T4S', '*******.*******.1.6', 'get', 'ifIndex',
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Device Temperature', 'S5800-48T4S', '*******.4.1.52642.********.*******.1', 'get', NULL,
                    'metric', 'Celsius', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('MAC Address', 'S5800-48T4S', '*******.********.1.0', 'get', NULL,
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Flash Usage Size', 'S5800-48T4S', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Flash Free Size', 'S5800-48T4S', '*******.4.1.52642.*******.*******.1', 'get', NULL,
                    'metric', 'GB', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Flash Size', 'S5800-48T4S', '*******.4.1.52642.*******.1.*******', 'get', NULL,
                    'metric', 'GB', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('PoE Power', 'S5800-48T4S', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Rx Power', 'S5800-48T4S', '*******.4.1.52642.*********.6.1.5', 'get', 'ifIndex',
                    'metric', 'dBm', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Tx Power', 'S5800-48T4S', '*******.4.1.52642.*********.5.1.5', 'get', 'ifIndex',
                    'metric', 'dBm', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bias Current', 'S5800-48T4S', '*******.4.1.52642.*********.4.1.5', 'get', 'ifIndex',
                    'metric', 'mA', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bias Voltage', 'S5800-48T4S', '*******.4.1.52642.*********.3.1.5', 'get', 'ifIndex',
                    'metric', 'Voltage', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Optical Module Temperature', 'S5800-48T4S', '*******.4.1.52642.*********.2.1.5', 'get', 'ifIndex',
                    'metric', 'Celsius', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Fan Name', 'S5800-48T4S', NULL, 'get', NULL,
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Fan Speed', 'S5800-48T4S', '*******.4.1.52642.1.37.1.*******.1.1', 'get', 'fanIndex',
                    'metric', '%', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Fan Status', 'S5800-48T4S', '*******.4.1.52642.********.1.*******', 'get', 'fanIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Supply Name', 'S5800-48T4S', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Supply Status', 'S5800-48T4S', '*******.4.1.52642.********.1.2', 'get', 'supplyIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('CPU Used Percentage', 'S5850C-24XMG2C', '*******.4.1.52642.*******.0', 'get', NULL,
                    'metric', '%', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Memory Used Percentage', 'S5850C-24XMG2C', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Memory Usage Size', 'S5850C-24XMG2C', '*******.4.1.52642.********.0', 'get', NULL,
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Memory Size', 'S5850C-24XMG2C', '*******.4.1.52642.*******.0', 'get', NULL,
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Name', 'S5850C-24XMG2C', '*******.*******.1.2', 'get', 'ifIndex',
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Out Packets Discarded', 'S5850C-24XMG2C', '*******.*******.1.19', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('In Packets with Errors', 'S5850C-24XMG2C', '*******.*******.1.14', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('In Packets Discarded', 'S5850C-24XMG2C', '*******.*******.1.13', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Out Packets with Errors', 'S5850C-24XMG2C', '*******.*******.1.20', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bits Sent', 'S5850C-24XMG2C', '*******.2.********.1.10', 'get', 'ifIndex',
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bits Received', 'S5850C-24XMG2C', '*******.2.********.1.6', 'get', 'ifIndex',
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Speed', 'S5850C-24XMG2C', '*******.*******.1.5', 'get', 'ifIndex',
                    'metric', 'bps', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Status', 'S5850C-24XMG2C', '*******.*******.1.8', 'get', 'ifIndex',
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface MTU', 'S5850C-24XMG2C', '*******.*******.1.4', 'get', 'ifIndex',
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Physical Address', 'S5850C-24XMG2C', '*******.*******.1.6', 'get', 'ifIndex',
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Device Temperature', 'S5850C-24XMG2C', '*******.4.1.52642.********.*******.1', 'get', NULL,
                    'metric', 'Celsius', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('MAC Address', 'S5850C-24XMG2C', '*******.********.1.0', 'get', NULL,
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Flash Usage Size', 'S5850C-24XMG2C', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Flash Free Size', 'S5850C-24XMG2C', '*******.4.1.52642.*******.*******.1', 'get', NULL,
                    'metric', 'GB', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Flash Size', 'S5850C-24XMG2C', '*******.4.1.52642.*******.1.*******', 'get', NULL,
                    'metric', 'GB', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('PoE Power', 'S5850C-24XMG2C', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Rx Power', 'S5850C-24XMG2C', '*******.4.1.52642.*********.6.1.5', 'get', 'ifIndex',
                    'metric', 'dBm', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Tx Power', 'S5850C-24XMG2C', '*******.4.1.52642.*********.5.1.5', 'get', 'ifIndex',
                    'metric', 'dBm', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bias Current', 'S5850C-24XMG2C', '*******.4.1.52642.*********.4.1.5', 'get', 'ifIndex',
                    'metric', 'mA', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bias Voltage', 'S5850C-24XMG2C', '*******.4.1.52642.*********.3.1.5', 'get', 'ifIndex',
                    'metric', 'Voltage', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Optical Module Temperature', 'S5850C-24XMG2C', '*******.4.1.52642.*********.2.1.5', 'get', 'ifIndex',
                    'metric', 'Celsius', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Fan Name', 'S5850C-24XMG2C', NULL, 'get', NULL,
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Fan Speed', 'S5850C-24XMG2C', '*******.4.1.52642.1.37.1.*******.1.1', 'get', 'fanIndex',
                    'metric', '%', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Fan Status', 'S5850C-24XMG2C', '*******.4.1.52642.********.1.*******', 'get', 'fanIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Supply Name', 'S5850C-24XMG2C', NULL, 'get', NULL,
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Supply Status', 'S5850C-24XMG2C', '*******.4.1.52642.********.1.2', 'get', 'supplyIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('CPU Used Percentage', 'S3410-48TS-P', '*******.4.1.52642.********.********.0', 'get', NULL,
                    'metric', '%', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Memory Used Percentage', 'S3410-48TS-P', '*******.4.1.52642.********.35.*******.1', 'get', NULL,
                    'metric', '%', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Memory Usage Size', 'S3410-48TS-P', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Memory Size', 'S3410-48TS-P', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Name', 'S3410-48TS-P', '*******.*******.1.2', 'get', 'ifIndex',
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Out Packets Discarded', 'S3410-48TS-P', '*******.*******.1.19', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('In Packets with Errors', 'S3410-48TS-P', '*******.*******.1.14', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('In Packets Discarded', 'S3410-48TS-P', '*******.*******.1.13', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Out Packets with Errors', 'S3410-48TS-P', '*******.*******.1.20', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bits Sent', 'S3410-48TS-P', '*******.2.********.1.10', 'get', 'ifIndex',
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bits Received', 'S3410-48TS-P', '*******.2.********.1.6', 'get', 'ifIndex',
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Speed', 'S3410-48TS-P', '*******.*******.1.5', 'get', 'ifIndex',
                    'metric', 'bps', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Status', 'S3410-48TS-P', '*******.*******.1.8', 'get', 'ifIndex',
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface MTU', 'S3410-48TS-P', '*******.*******.1.4', 'get', 'ifIndex',
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Physical Address', 'S3410-48TS-P', '*******.*******.1.6', 'get', 'ifIndex',
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Device Temperature', 'S3410-48TS-P', '*******.4.1.52642.********.********', 'get', NULL,
                    'metric', 'Celsius', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('MAC Address', 'S3410-48TS-P', '*******.4.1.52642.********.********.21.2', 'get', NULL,
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Flash Usage Size', 'S3410-48TS-P', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Flash Free Size', 'S3410-48TS-P', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Flash Size', 'S3410-48TS-P', '*******.4.1.52642.********.********', 'get', NULL,
                    'metric', 'MB', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('PoE Power', 'S3410-48TS-P', '1.0.8802.*******.5.4795.********.1', 'get', 'ifIndex',
                    'metric', 'W', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Rx Power', 'S3410-48TS-P', '*******.4.1.52642.********.*********.31', 'get', 'ifIndex',
                    'metric', 'dBm', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Tx Power', 'S3410-48TS-P', '*******.4.1.52642.********.*********.51', 'get', 'ifIndex',
                    'metric', 'dBm', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bias Current', 'S3410-48TS-P', '*******.4.1.52642.********.*********.21', 'get', 'ifIndex',
                    'metric', 'uA', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bias Voltage', 'S3410-48TS-P', '*******.4.1.52642.********.*********.19', 'get', 'ifIndex',
                    'metric', 'mVolts', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Optical Module Temperature', 'S3410-48TS-P', '*******.4.1.52642.********.*********.17', 'get', 'ifIndex',
                    'metric', 'Celsius', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Fan Name', 'S3410-48TS-P', '*******.4.1.52642.********.********.3', 'get', 'fanIndex',
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Fan Speed', 'S3410-48TS-P', '*******.4.1.52642.********.********.6.1', 'walk', 'fanIndex',
                    'metric', 'rpm', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Fan Status', 'S3410-48TS-P', '*******.4.1.52642.********.********.4.1', 'walk', 'fanIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Supply Name', 'S3410-48TS-P', '*******.4.1.52642.********.********.3', 'get', 'supplyIndex',
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Supply Status', 'S3410-48TS-P', '*******.4.1.52642.********.********.3.1', 'get', 'supplyIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('CPU Used Percentage', 'S5500-48T6SP-R', '*******.4.1.52642.********.11', 'walk', NULL,
                    'metric', '%', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Memory Used Percentage', 'S5500-48T6SP-R', '*******.4.1.52642.********.12', 'walk', NULL,
                    'metric', '%', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Memory Usage Size', 'S5500-48T6SP-R', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Memory Size', 'S5500-48T6SP-R', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Name', 'S5500-48T6SP-R', '*******.*******.1.2', 'get', 'ifIndex',
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Out Packets Discarded', 'S5500-48T6SP-R', '*******.*******.1.19', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('In Packets with Errors', 'S5500-48T6SP-R', '*******.*******.1.14', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('In Packets Discarded', 'S5500-48T6SP-R', '*******.*******.1.13', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Out Packets with Errors', 'S5500-48T6SP-R', '*******.*******.1.20', 'get', 'ifIndex',
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bits Sent', 'S5500-48T6SP-R', '*******.2.********.1.10', 'get', 'ifIndex',
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bits Received', 'S5500-48T6SP-R', '*******.2.********.1.6', 'get', 'ifIndex',
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Speed', 'S5500-48T6SP-R', '*******.*******.1.5', 'get', 'ifIndex',
                    'metric', 'bps', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Status', 'S5500-48T6SP-R', '*******.*******.1.8', 'get', 'ifIndex',
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface MTU', 'S5500-48T6SP-R', '*******.*******.1.4', 'get', 'ifIndex',
                    'metric', 'bytes', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Interface Physical Address', 'S5500-48T6SP-R', '*******.*******.1.6', 'get', 'ifIndex',
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Device Temperature', 'S5500-48T6SP-R', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('MAC Address', 'S5500-48T6SP-R', '*******.4.1.52642.*********.0', 'get', NULL,
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Flash Usage Size', 'S5500-48T6SP-R', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Flash Free Size', 'S5500-48T6SP-R', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Flash Size', 'S5500-48T6SP-R', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('PoE Power', 'S5500-48T6SP-R', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Rx Power', 'S5500-48T6SP-R', '*******.4.1.52642.********.1.3', 'get', 'ifIndex',
                    'metric', '0.01dBm', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Tx Power', 'S5500-48T6SP-R', '*******.4.1.52642.********.1.2', 'get', 'ifIndex',
                    'metric', '0.01dBm', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bias Current', 'S5500-48T6SP-R', '*******.4.1.52642.********.1.6', 'get', 'ifIndex',
                    'metric', '2uA', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Bias Voltage', 'S5500-48T6SP-R', '*******.4.1.52642.********.1.5', 'get', 'ifIndex',
                    'metric', '100uV', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Optical Module Temperature', 'S5500-48T6SP-R', '*******.4.1.52642.********.1.4', 'get', 'ifIndex',
                    'metric', '0.00390625Celsius', '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Fan Name', 'S5500-48T6SP-R', NULL, 'get', NULL,
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Fan Speed', 'S5500-48T6SP-R', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Fan Status', 'S5500-48T6SP-R', NULL, 'get', NULL,
                    'metric', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Supply Name', 'S5500-48T6SP-R', NULL, 'get', NULL,
                    'dimension', NULL, '', 1);
INSERT IGNORE INTO snmp_metric
            (metric_name, model, base_oid, method, index_from_name, data_type, unit, description, is_internal)
            VALUES ('Supply Status', 'S5500-48T6SP-R', '*******.4.1.52642.9.189.1', 'get', 'supplyIndex',
                    'metric', NULL, '', 1);
