/*
 Navicat Premium Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 100321
 Source Host           : ************:3306
 Source Schema         : automation

 Target Server Type    : MySQL
 Target Server Version : 100321
 File Encoding         : 65001

 Date: 05/03/2024 11:15:38
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for alembic_version
-- ----------------------------
DROP TABLE IF EXISTS `alembic_version`;
CREATE TABLE `alembic_version`  (
  `version_num` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`version_num`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for allowed_source_ip_policy
-- ----------------------------
DROP TABLE IF EXISTS `allowed_source_ip_policy`;
CREATE TABLE `allowed_source_ip_policy`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `ip` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ansible_device
-- ----------------------------
DROP TABLE IF EXISTS `ansible_device`;
CREATE TABLE `ansible_device`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `device_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `device_user` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `device_pwd` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_device_name`(`device_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ansible_job
-- ----------------------------
DROP TABLE IF EXISTS `ansible_job`;
CREATE TABLE `ansible_job`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `playbook_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `schedule_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `schedule_param` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `status` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `playbook_path` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name` ASC) USING BTREE,
  INDEX `playbook_name`(`playbook_name` ASC) USING BTREE,
  CONSTRAINT `ansible_job_ibfk_1` FOREIGN KEY (`playbook_name`) REFERENCES `ansible_playbook` (`name`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ansible_job_result
-- ----------------------------
DROP TABLE IF EXISTS `ansible_job_result`;
CREATE TABLE `ansible_job_result`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `task_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `switch_sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `job_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `start_time` datetime NULL DEFAULT NULL,
  `duration` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `state` tinyint(1) NULL DEFAULT NULL,
  `result` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `job_name`(`job_name` ASC) USING BTREE,
  INDEX `switch_sn`(`switch_sn` ASC) USING BTREE,
  CONSTRAINT `ansible_job_result_ibfk_1` FOREIGN KEY (`job_name`) REFERENCES `ansible_job` (`name`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ansible_playbook
-- ----------------------------
DROP TABLE IF EXISTS `ansible_playbook`;
CREATE TABLE `ansible_playbook`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `create_user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `description` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `internal` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for application_config
-- ----------------------------
DROP TABLE IF EXISTS `application_config`;
CREATE TABLE `application_config`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `application_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `configuration` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `application_status` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `application_name`(`application_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for association_group
-- ----------------------------
DROP TABLE IF EXISTS `association_group`;
CREATE TABLE `association_group`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `switch_sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `group_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `audit` tinyint(1) NULL DEFAULT NULL,
  `action` tinyint(1) NULL DEFAULT NULL,
  `upgrading` tinyint(1) NULL DEFAULT NULL,
  `retrieve_config` tinyint(1) NULL DEFAULT NULL,
  `switch_type` int NULL DEFAULT NULL,
  `delete` tinyint(1) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `group_name`(`group_name` ASC) USING BTREE,
  CONSTRAINT `association_group_ibfk_1` FOREIGN KEY (`group_name`) REFERENCES `group` (`group_name`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for automation_task
-- ----------------------------
DROP TABLE IF EXISTS `automation_task`;
CREATE TABLE `automation_task`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `task_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `schedule_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `sn` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `task_status` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `task_process` int NULL DEFAULT NULL,
  `retry_times` int NULL DEFAULT NULL,
  `args` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `kwargs` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_automation_task_id`(`task_id` ASC) USING BTREE,
  INDEX `ix_automation_task_sn`(`sn` ASC) USING BTREE,
  INDEX `ix_automation_task_name`(`task_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cli_tree
-- ----------------------------
DROP TABLE IF EXISTS `cli_tree`;
CREATE TABLE `cli_tree`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `alias` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `pid` int NULL DEFAULT NULL,
  `level` int NULL DEFAULT NULL,
  `path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `is_param` tinyint(1) NULL DEFAULT NULL,
  `param_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `default` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `param_check` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `viewable` tinyint(1) NULL DEFAULT NULL,
  `checkable` tinyint(1) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `pid`(`pid` ASC) USING BTREE,
  INDEX `ix_cli_tree_alias`(`alias` ASC) USING BTREE,
  INDEX `ix_cli_tree_name`(`name` ASC) USING BTREE,
  CONSTRAINT `cli_tree_ibfk_1` FOREIGN KEY (`pid`) REFERENCES `cli_tree` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for compatibility
-- ----------------------------
DROP TABLE IF EXISTS `compatibility`;
CREATE TABLE `compatibility`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `platform` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `version` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `cli_node` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `cli_node`(`cli_node` ASC) USING BTREE,
  INDEX `platform`(`platform` ASC) USING BTREE,
  CONSTRAINT `compatibility_ibfk_1` FOREIGN KEY (`cli_node`) REFERENCES `cli_tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `compatibility_ibfk_2` FOREIGN KEY (`platform`) REFERENCES `switch_systeminfo` (`model`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 2345702 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for db_backup
-- ----------------------------
DROP TABLE IF EXISTS `db_backup`;
CREATE TABLE `db_backup`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `version` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `backup_file_path` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for deployed_security_switch
-- ----------------------------
DROP TABLE IF EXISTS `deployed_security_switch`;
CREATE TABLE `deployed_security_switch`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `invalid_times` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_deployed_security_switch_sn`(`sn` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for encrypt_key
-- ----------------------------
DROP TABLE IF EXISTS `encrypt_key`;
CREATE TABLE `encrypt_key`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `encrypt_key` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `used` int NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for event
-- ----------------------------
DROP TABLE IF EXISTS `event`;
CREATE TABLE `event`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `type` enum('info','warn','error') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `msg` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `count` int NULL DEFAULT NULL,
  `status` enum('unread','read','ignore','handled') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `history_time` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for general_config
-- ----------------------------
DROP TABLE IF EXISTS `general_config`;
CREATE TABLE `general_config`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `pid` int NULL DEFAULT NULL,
  `platform` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `level` int NULL DEFAULT NULL,
  `content` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_general_config_name`(`name` ASC) USING BTREE,
  INDEX `pid`(`pid` ASC) USING BTREE,
  INDEX `platform`(`platform` ASC) USING BTREE,
  CONSTRAINT `general_config_ibfk_1` FOREIGN KEY (`pid`) REFERENCES `general_config` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `general_config_ibfk_2` FOREIGN KEY (`platform`) REFERENCES `switch_systeminfo` (`model`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for general_config_attach
-- ----------------------------
DROP TABLE IF EXISTS `general_config_attach`;
CREATE TABLE `general_config_attach`  (
  `switch_sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `general_config_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  INDEX `general_config_name`(`general_config_name` ASC) USING BTREE,
  INDEX `switch_sn`(`switch_sn` ASC) USING BTREE,
  CONSTRAINT `general_config_attach_ibfk_1` FOREIGN KEY (`general_config_name`) REFERENCES `general_config` (`name`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `general_config_attach_ibfk_2` FOREIGN KEY (`switch_sn`) REFERENCES `switch` (`sn`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for general_config_params
-- ----------------------------
DROP TABLE IF EXISTS `general_config_params`;
CREATE TABLE `general_config_params`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `config_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `template_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `params` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `config_name`(`config_name` ASC) USING BTREE,
  INDEX `template_name`(`template_name` ASC) USING BTREE,
  CONSTRAINT `general_config_params_ibfk_1` FOREIGN KEY (`config_name`) REFERENCES `general_config` (`name`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `general_config_params_ibfk_2` FOREIGN KEY (`template_name`) REFERENCES `general_template` (`name`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for general_template
-- ----------------------------
DROP TABLE IF EXISTS `general_template`;
CREATE TABLE `general_template`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `content` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `j2_template` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `params` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `internal` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_general_template_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for group
-- ----------------------------
DROP TABLE IF EXISTS `group`;
CREATE TABLE `group`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `group_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `audit` tinyint(1) NULL DEFAULT NULL,
  `action` tinyint(1) NULL DEFAULT NULL,
  `upgrading` tinyint(1) NULL DEFAULT NULL,
  `retrieve_config` tinyint(1) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_group_group_name`(`group_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for group_task
-- ----------------------------
DROP TABLE IF EXISTS `group_task`;
CREATE TABLE `group_task`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `start_date` datetime NULL DEFAULT NULL,
  `end_date` datetime NULL DEFAULT NULL,
  `status` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ix_group_task_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hardware_mapping
-- ----------------------------
DROP TABLE IF EXISTS `hardware_mapping`;
CREATE TABLE `hardware_mapping`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `hardware_model` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `switch_model` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `hardware_model`(`hardware_model` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 86 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for host_access_control
-- ----------------------------
DROP TABLE IF EXISTS `host_access_control`;
CREATE TABLE `host_access_control`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `smac` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `sip` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `source_host_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `located_switch` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `online` tinyint(1) NULL DEFAULT NULL,
  `assigned_vlan_id` int NULL DEFAULT NULL,
  `port_num` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `map_queue` enum('0','1','2','3','4','5','6','7') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `bandwidth` bigint NULL DEFAULT NULL,
  `counter_byte` bigint NULL DEFAULT NULL,
  `counter_pkt` bigint NULL DEFAULT NULL,
  `processed_host` tinyint(1) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `smac`(`smac` ASC) USING BTREE,
  INDEX `host_access_control_ibfk_1`(`located_switch` ASC) USING BTREE,
  CONSTRAINT `host_access_control_ibfk_1` FOREIGN KEY (`located_switch`) REFERENCES `switch` (`sn`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for license
-- ----------------------------
DROP TABLE IF EXISTS `license`;
CREATE TABLE `license`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `local_lic` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `portal_lic` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `license_expired` datetime NULL DEFAULT NULL,
  `lic_feature` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `lic_speed` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `lic_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `sn_num` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `status` enum('Active','Expiring','No License','Expired','Unreachable','Unknown') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `upgrate` tinyint(1) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sn_num`(`sn_num` ASC) USING BTREE,
  CONSTRAINT `license_ibfk_1` FOREIGN KEY (`sn_num`) REFERENCES `switch` (`sn`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for license_count
-- ----------------------------
DROP TABLE IF EXISTS `license_count`;
CREATE TABLE `license_count`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `remain` int NOT NULL,
  `total` int NOT NULL,
  `feature_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `speed_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for license_statistic
-- ----------------------------
DROP TABLE IF EXISTS `license_statistic`;
CREATE TABLE `license_statistic`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `remain` int NOT NULL,
  `total` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mac_vtep_mapping
-- ----------------------------
DROP TABLE IF EXISTS `mac_vtep_mapping`;
CREATE TABLE `mac_vtep_mapping`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `mac_vni` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `vtep_ip` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `source` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for model_physic_port
-- ----------------------------
DROP TABLE IF EXISTS `model_physic_port`;
CREATE TABLE `model_physic_port`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `platform_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `port_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `port_type` enum('ge','te','qe','xe') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ix_model_physic_port_platform_name`(`platform_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10516 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for operation_log
-- ----------------------------
DROP TABLE IF EXISTS `operation_log`;
CREATE TABLE `operation_log`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `user` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `method` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `params` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `content` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `status` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user`(`user` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for port_vlan_binding
-- ----------------------------
DROP TABLE IF EXISTS `port_vlan_binding`;
CREATE TABLE `port_vlan_binding`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `port_binding` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sn`(`sn` ASC) USING BTREE,
  CONSTRAINT `port_vlan_binding_ibfk_1` FOREIGN KEY (`sn`) REFERENCES `switch` (`sn`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for push_config_task
-- ----------------------------
DROP TABLE IF EXISTS `push_config_task`;
CREATE TABLE `push_config_task`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `task_content` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `task_status` int NULL DEFAULT NULL,
  `read_tag` int NULL DEFAULT NULL,
  `create_user` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_task_name_1`(`task_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for push_config_task_details
-- ----------------------------
DROP TABLE IF EXISTS `push_config_task_details`;
CREATE TABLE `push_config_task_details`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `sn` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `push_status` int NULL DEFAULT NULL,
  `push_ret` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `push_time` datetime NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `fk_push_config_name_sn`(`name` ASC, `sn` ASC) USING BTREE,
  INDEX `fk_push_config_name`(`name` ASC) USING BTREE,
  CONSTRAINT `fk_push_config_name` FOREIGN KEY (`name`) REFERENCES `push_config_task` (`task_name`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sdn_access_switch_policy
-- ----------------------------
DROP TABLE IF EXISTS `sdn_access_switch_policy`;
CREATE TABLE `sdn_access_switch_policy`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `flow_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `flow_string` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `flow_action` enum('drop','forward') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `priority` int NULL DEFAULT NULL,
  `map_queue` enum('0','1','2','3','4','5','6','7') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `counter` bigint NULL DEFAULT NULL,
  `meter` bigint NULL DEFAULT NULL,
  `cookie` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `flow_name`(`flow_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch
-- ----------------------------
DROP TABLE IF EXISTS `switch`;
CREATE TABLE `switch`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `status` enum('Init','Registered Not-staged','Configured','Staged','Registered','Provisioning Success','Provisioning Failed','Imported','DECOM','DECOM-Init','DECOM-Pending','DECOM-Manual','RMA') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'Configured',
  `version` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `revision` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `hwid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `tmp_ip` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `mgt_ip` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `topology` enum('1','2','3') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `step` smallint NULL DEFAULT NULL,
  `enable` tinyint(1) NULL DEFAULT NULL,
  `current_user` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `current_password` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `host_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `domain` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `remark` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `upgrade_status` smallint NULL DEFAULT NULL,
  `platform_model` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `import_type` smallint NULL DEFAULT NULL,
  `address` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `reachable_status` smallint NULL DEFAULT NULL,
  `post_deployed_config` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `config_parameters` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `link_ip_addr` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `system_config_id` int NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_switch_sn`(`sn` ASC) USING BTREE,
  INDEX `platform_model`(`platform_model` ASC) USING BTREE,
  INDEX `system_config_id_fk`(`system_config_id` ASC) USING BTREE,
  CONSTRAINT `switch_ibfk_1` FOREIGN KEY (`platform_model`) REFERENCES `switch_systeminfo` (`model`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `system_config_id_fk` FOREIGN KEY (`system_config_id`) REFERENCES `system_config` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch_agent_conf
-- ----------------------------
DROP TABLE IF EXISTS `switch_agent_conf`;
CREATE TABLE `switch_agent_conf`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `conf` blob NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sn`(`sn` ASC) USING BTREE,
  CONSTRAINT `switch_agent_conf_ibfk_1` FOREIGN KEY (`sn`) REFERENCES `switch` (`sn`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch_arp_mac
-- ----------------------------
DROP TABLE IF EXISTS `switch_arp_mac`;
CREATE TABLE `switch_arp_mac`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `local_vtep_ip` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `mac` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sn`(`sn` ASC) USING BTREE,
  CONSTRAINT `switch_arp_mac_ibfk_1` FOREIGN KEY (`sn`) REFERENCES `switch` (`sn`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch_autoconfig
-- ----------------------------
DROP TABLE IF EXISTS `switch_autoconfig`;
CREATE TABLE `switch_autoconfig`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `config` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `type` enum('global','regional','site','switch','mgt_ip') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `parent_id` int NULL DEFAULT NULL,
  `system_model` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `default` tinyint(1) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name` ASC) USING BTREE,
  INDEX `system_model`(`system_model` ASC) USING BTREE,
  CONSTRAINT `switch_autoconfig_ibfk_1` FOREIGN KEY (`system_model`) REFERENCES `switch_systeminfo` (`model`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch_config_backup
-- ----------------------------
DROP TABLE IF EXISTS `switch_config_backup`;
CREATE TABLE `switch_config_backup`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `ip` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `config` mediumblob NULL,
  `back_up_type` smallint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_switch_config_backup_ip`(`ip` ASC) USING BTREE,
  INDEX `ix_switch_config_backup_sn`(`sn` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch_config_snapshot
-- ----------------------------
DROP TABLE IF EXISTS `switch_config_snapshot`;
CREATE TABLE `switch_config_snapshot`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `snapshot_time` datetime NULL DEFAULT NULL,
  `archive_config` mediumblob NULL,
  `config_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `tag` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sn`(`sn` ASC) USING BTREE,
  CONSTRAINT `switch_config_snapshot_ibfk_1` FOREIGN KEY (`sn`) REFERENCES `switch` (`sn`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch_configured
-- ----------------------------
DROP TABLE IF EXISTS `switch_configured`;
CREATE TABLE `switch_configured`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sn`(`sn` ASC) USING BTREE,
  CONSTRAINT `switch_configured_ibfk_1` FOREIGN KEY (`sn`) REFERENCES `switch` (`sn`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch_gis
-- ----------------------------
DROP TABLE IF EXISTS `switch_gis`;
CREATE TABLE `switch_gis`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `longitude` float NULL DEFAULT NULL,
  `latitude` float NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sn`(`sn` ASC) USING BTREE,
  CONSTRAINT `switch_gis_ibfk_1` FOREIGN KEY (`sn`) REFERENCES `switch` (`sn`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch_global_status
-- ----------------------------
DROP TABLE IF EXISTS `switch_global_status`;
CREATE TABLE `switch_global_status`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `content` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `type` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `upload_time` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sn`(`sn` ASC) USING BTREE,
  CONSTRAINT `switch_global_status_ibfk_1` FOREIGN KEY (`sn`) REFERENCES `switch` (`sn`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch_image_info
-- ----------------------------
DROP TABLE IF EXISTS `switch_image_info`;
CREATE TABLE `switch_image_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `image_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `image_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `image_md5_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `platform` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `version` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `revision` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch_log
-- ----------------------------
DROP TABLE IF EXISTS `switch_log`;
CREATE TABLE `switch_log`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `switch_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `level` enum('debug','info','warn','error') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `content` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `report_action` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_log_switch_id`(`switch_id` ASC) USING BTREE,
  INDEX `idx_log_report_action`(`report_action` ASC) USING BTREE,
  INDEX `idx_log_id_action`(`switch_id` ASC, `report_action` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch_parking
-- ----------------------------
DROP TABLE IF EXISTS `switch_parking`;
CREATE TABLE `switch_parking`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `address` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `model` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ip` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `investigate` tinyint(1) NULL DEFAULT NULL,
  `register_count` smallint NULL DEFAULT NULL,
  `last_register` datetime NULL DEFAULT NULL,
  `history_time` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `remark` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_switch_parking_sn`(`sn` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch_port
-- ----------------------------
DROP TABLE IF EXISTS `switch_port`;
CREATE TABLE `switch_port`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `switch_sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `port_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `speed` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `status` enum('UP','DOWN') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `type` varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `management` enum('enabled','disabled') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `flow_control` enum('enabled','disabled') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `duplex` enum('full') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `switch_sn`(`switch_sn` ASC) USING BTREE,
  CONSTRAINT `switch_port_ibfk_1` FOREIGN KEY (`switch_sn`) REFERENCES `switch` (`sn`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch_status
-- ----------------------------
DROP TABLE IF EXISTS `switch_status`;
CREATE TABLE `switch_status`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `port_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `rc_count` bigint UNSIGNED NULL DEFAULT NULL,
  `tx_count` bigint UNSIGNED NULL DEFAULT NULL,
  `rc_drop` bigint UNSIGNED NULL DEFAULT NULL,
  `tx_drop` bigint UNSIGNED NULL DEFAULT NULL,
  `lldp_neighbor` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `link_status` enum('Down','Up') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `upload_time` datetime NOT NULL,
  `rx_rate` bigint UNSIGNED NULL DEFAULT NULL,
  `tx_rate` bigint UNSIGNED NULL DEFAULT NULL,
  `admin_status` enum('Enabled','Disabled') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `flow_control` enum('Enabled','Disabled') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `link_speed` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `port_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sn`(`sn` ASC) USING BTREE,
  CONSTRAINT `switch_status_ibfk_1` FOREIGN KEY (`sn`) REFERENCES `switch` (`sn`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch_switch_autoconfig
-- ----------------------------
DROP TABLE IF EXISTS `switch_switch_autoconfig`;
CREATE TABLE `switch_switch_autoconfig`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `switch_id` int NULL DEFAULT NULL,
  `switchAutoConfig_id` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `switchAutoConfig_id`(`switchAutoConfig_id` ASC) USING BTREE,
  INDEX `switch_id`(`switch_id` ASC) USING BTREE,
  CONSTRAINT `switch_switch_autoconfig_ibfk_1` FOREIGN KEY (`switchAutoConfig_id`) REFERENCES `switch_autoconfig` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `switch_switch_autoconfig_ibfk_2` FOREIGN KEY (`switch_id`) REFERENCES `switch` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for switch_systeminfo
-- ----------------------------
DROP TABLE IF EXISTS `switch_systeminfo`;
CREATE TABLE `switch_systeminfo`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `platform` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `model` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `speed_for_license` enum('1G','10G') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `feature` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `up_to_date_version` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `up_to_date_image_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `up_to_date_image_md5_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `patched_tar_file` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `patched_install_script` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `script_file_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `manual_upgrade_scripts` varchar(511) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `up_to_date_onie_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`model`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for switch_yamlconfig
-- ----------------------------
DROP TABLE IF EXISTS `switch_yamlconfig`;
CREATE TABLE `switch_yamlconfig`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `autoconfig_id` int NULL DEFAULT NULL,
  `pushconfig` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `autoconfig_id`(`autoconfig_id` ASC) USING BTREE,
  CONSTRAINT `switch_yamlconfig_ibfk_1` FOREIGN KEY (`autoconfig_id`) REFERENCES `switch_autoconfig` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `switch_op_user` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `switch_op_password` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `license_portal_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `license_portal_user` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `license_portal_password` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `license_portal_token` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `security_config` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `parking_security_config` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `templates_set` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `customized_fields` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `retrieve_config_num` int NULL DEFAULT NULL,
  `db_config_num` int NULL DEFAULT 3,
  `config_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `config_name`(`config_name` ASC) USING BTREE,
  UNIQUE INDEX `UQ_config_name`(`config_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tacacs_config
-- ----------------------------
DROP TABLE IF EXISTS `tacacs_config`;
CREATE TABLE `tacacs_config`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `enable` tinyint(1) NULL DEFAULT NULL,
  `server_host` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `server_host_ii` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `server_secret` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `session_timeout` int NULL DEFAULT NULL,
  `auth_protocol` int NULL DEFAULT NULL,
  `user_mapping` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tag
-- ----------------------------
DROP TABLE IF EXISTS `tag`;
CREATE TABLE `tag`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `record_id` int NOT NULL,
  `record_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `tag_content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `record_id`(`record_id` ASC, `record_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for task_lock
-- ----------------------------
DROP TABLE IF EXISTS `task_lock`;
CREATE TABLE `task_lock`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `host` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_task_lock_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for task_status
-- ----------------------------
DROP TABLE IF EXISTS `task_status`;
CREATE TABLE `task_status`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `start_date` datetime NULL DEFAULT NULL,
  `end_date` datetime NULL DEFAULT NULL,
  `status` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_task_status_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for template
-- ----------------------------
DROP TABLE IF EXISTS `template`;
CREATE TABLE `template`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `template_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `type` enum('global','regional','site','global sec') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `model` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`template_name`) USING BTREE,
  INDEX `model`(`model` ASC) USING BTREE,
  CONSTRAINT `template_ibfk_1` FOREIGN KEY (`model`) REFERENCES `switch_systeminfo` (`model`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `passwd` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ctime` datetime NULL DEFAULT NULL,
  `type` enum('superadmin','admin','superuser','readonly') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `email` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `user_type` enum('global','group') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'global',
  `is_lock` smallint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_user_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_group_mapping
-- ----------------------------
DROP TABLE IF EXISTS `user_group_mapping`;
CREATE TABLE `user_group_mapping`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `user_id` int NOT NULL,
  `group_id` int NOT NULL,
  PRIMARY KEY (`user_id`, `group_id`) USING BTREE,
  INDEX `user_group_id_fk`(`group_id` ASC) USING BTREE,
  CONSTRAINT `user_group_id_fk` FOREIGN KEY (`group_id`) REFERENCES `group` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_switch_lock
-- ----------------------------
DROP TABLE IF EXISTS `user_switch_lock`;
CREATE TABLE `user_switch_lock`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `user` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_user_switch_lock_sn`(`sn` ASC) USING BTREE,
  INDEX `ix_user_switch_lock_user`(`user` ASC) USING BTREE,
  CONSTRAINT `user_switch_lock_ibfk_2` FOREIGN KEY (`user`) REFERENCES `user` (`name`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for vlan_vxlan_list
-- ----------------------------
DROP TABLE IF EXISTS `vlan_vxlan_list`;
CREATE TABLE `vlan_vxlan_list`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `vlan` int NOT NULL,
  `vlan_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `vni` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `vlan`(`vlan` ASC) USING BTREE,
  UNIQUE INDEX `vni`(`vni` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for vpn_config
-- ----------------------------
DROP TABLE IF EXISTS `vpn_config`;
CREATE TABLE `vpn_config`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `client_key` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `client_crt` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `ca_crt` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `vpn_ip` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `vpn_online_status` tinyint(1) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sn`(`sn` ASC) USING BTREE,
  CONSTRAINT `vpn_config_sn` FOREIGN KEY (`sn`) REFERENCES `switch` (`sn`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for vtep_control_switch_list
-- ----------------------------
DROP TABLE IF EXISTS `vtep_control_switch_list`;
CREATE TABLE `vtep_control_switch_list`  (
  `create_time` datetime NULL DEFAULT NULL,
  `modified_time` datetime NULL DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT,
  `sn` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `local_vtep_ip` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `enable` tinyint(1) NULL DEFAULT NULL,
  `ovsdb_status` enum('connected','disconnected','error') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `seq` enum('1','2','3','4','5','6','7','8','9','10') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `config_error` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `config_status` enum('normal','error') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sn`(`sn` ASC) USING BTREE,
  CONSTRAINT `vtep_control_switch_list_ibfk_1` FOREIGN KEY (`sn`) REFERENCES `switch` (`sn`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- View structure for ansible_playbook_with_tag
-- ----------------------------
DROP VIEW IF EXISTS `ansible_playbook_with_tag`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `ansible_playbook_with_tag` AS select `a`.`create_time` AS `create_time`,`a`.`modified_time` AS `modified_time`,`a`.`id` AS `id`,`a`.`create_user` AS `create_user`,`a`.`name` AS `name`,`a`.`description` AS `description`,`a`.`internal` AS `internal`,`b`.`tag_content` AS `tag` from (`automation`.`ansible_playbook` `a` left join (select `automation`.`tag`.`record_id` AS `record_id`,`automation`.`tag`.`record_type` AS `record_type`,`automation`.`tag`.`tag_content` AS `tag_content` from `automation`.`tag` where `automation`.`tag`.`record_type` = 'playbook') `b` on(`a`.`id` = `b`.`record_id`));

-- ----------------------------
-- View structure for general_template_with_tag
-- ----------------------------
DROP VIEW IF EXISTS `general_template_with_tag`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `general_template_with_tag` AS select `a`.`create_time` AS `create_time`,`a`.`modified_time` AS `modified_time`,`a`.`id` AS `id`,`a`.`name` AS `name`,`a`.`description` AS `description`,`a`.`content` AS `content`,`a`.`j2_template` AS `j2_template`,`a`.`params` AS `params`,`a`.`internal` AS `internal`,`b`.`tag_content` AS `tag` from (`automation`.`general_template` `a` left join (select `automation`.`tag`.`record_id` AS `record_id`,`automation`.`tag`.`record_type` AS `record_type`,`automation`.`tag`.`tag_content` AS `tag_content` from `automation`.`tag` where `automation`.`tag`.`record_type` = 'template') `b` on(`a`.`id` = `b`.`record_id`));

-- ----------------------------
-- View structure for switch_config_snapshot_with_tag
-- ----------------------------
DROP VIEW IF EXISTS `switch_config_snapshot_with_tag`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `switch_config_snapshot_with_tag` AS (select `a`.`create_time` AS `create_time`,`a`.`modified_time` AS `modified_time`,`a`.`id` AS `id`,`a`.`sn` AS `sn`,`a`.`snapshot_time` AS `snapshot_time`,`a`.`archive_config` AS `archive_config`,`a`.`config_type` AS `config_type`,`a`.`description` AS `description`,`a`.`tag` AS `tag`,`b`.`tag_content` AS `tag_content` from (`automation`.`switch_config_snapshot` `a` left join (select `automation`.`tag`.`record_id` AS `record_id`,`automation`.`tag`.`record_type` AS `record_type`,`automation`.`tag`.`tag_content` AS `tag_content` from `automation`.`tag` where `automation`.`tag`.`record_type` = 'snapshot') `b` on(`a`.`id` = `b`.`record_id`)));

SET FOREIGN_KEY_CHECKS = 1;
