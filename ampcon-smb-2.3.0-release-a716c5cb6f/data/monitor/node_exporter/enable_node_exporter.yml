---
- name: Setup Ampcon Exporter
  hosts: all
  become: yes
  tasks:
    - name: Copy ampcon_exporter binary
      copy:
        src: /usr/share/automation/server/monitor/node_exporter/node_exporter
        dest: /usr/local/bin/ampcon_exporter
        mode: "0755"

    - name: Copy ampcon_exporter_agent binary
      copy:
        src: /usr/share/automation/server/monitor/node_exporter/ampcon_exporter_agent
        dest: /usr/local/bin/ampcon_exporter_agent
        mode: "0755"

    - name: Create textfile.directory
      file:
        path: /etc/ampcon_exporter/textfile
        state: directory
        mode: "0755"

    - name: Create systemd service file for ampcon_exporter
      copy:
        dest: /lib/systemd/system/ampcon_exporter.service
        content: |
          [Unit]
          Description=Ampcon Exporter
          After=network.target

          [Service]
          User=root
          ExecStartPre=/etc/ampcon_exporter/nic_info.sh
          ExecStart=/usr/local/bin/ampcon_exporter \
              --collector.disable-defaults \
              --collector.uname \
              --collector.netclass --collector.netdev \
              --collector.textfile --collector.textfile.directory=/etc/ampcon_exporter/textfile \
              --collector.ethtool  --collector.ethtool.metrics-include=(ethtool_info|prio|ecn|pri) \
              --collector.infiniband

          [Install]
          WantedBy=multi-user.target

    - name: Create systemd service file for ampcon_exporter_agent
      copy:
        dest: /lib/systemd/system/ampcon_exporter_agent.service
        content: |
          [Unit]
          Description=Ampcon Exporter Agent
          After=network.target

          [Service]
          User=root
          ExecStart=/usr/local/bin/ampcon_exporter_agent \
              --collector.disable-defaults \
              --collector.ecn \
              --collector.sfp \
              --collector.interval 15s

          [Install]
          WantedBy=multi-user.target

    - name: Copy the nic info script
      copy:
        src: /usr/share/automation/server/monitor/node_exporter/nic_info.sh
        dest: /etc/ampcon_exporter/nic_info.sh
        mode: "0755"

    - name: Reload systemd to pick up new service
      systemd:
        daemon_reload: yes

    - name: Start ampcon_exporter service
      systemd:
        name: ampcon_exporter
        state: restarted
        enabled: yes

    - name: Start ampcon_exporter_agent service
      systemd:
        name: ampcon_exporter_agent
        state: restarted
        enabled: yes
