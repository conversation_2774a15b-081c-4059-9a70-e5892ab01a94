#!/bin/bash

ethernet_ids=($(lspci | grep "Ethernet controller" | awk '{print $1}'))

declare -a nic_details

for id in "${ethernet_ids[@]}"; do
    port_name=$(ls -l /sys/class/net/ | grep "$id" | awk '{print $9}')
    name=$(lspci -s "$id" -vv |grep "Part number" | awk '{print $NF}')
    chip=$(lspci -s "$id" | awk -F ":" '{print $NF}' | sed 's/^ *//')
    nic_details+=("$name|$chip|$port_name")

done


output_file="/etc/ampcon_exporter/textfile/nic_info_metrics.prom"
echo -n > "$output_file"

echo "# HELP node_nic_info value is always 1." >> "$output_file"
echo "# TYPE node_nic_info gauge" >> "$output_file"

for detail in "${nic_details[@]}"; do
    IFS='|' read -r name chip interface <<< "$detail"
    echo "node_nic_info{name=\"$name\", chip_number=\"$chip\", device=\"$interface\"} 1" >> "$output_file"
done

hostname=$(hostname)
os_version=$(lsb_release -a | grep 'Description' | awk -F':' '{print $2}' | xargs)
cpu=$(echo "$(lscpu | grep 'Model name' | cut -d':' -f2 | xargs) ($(lscpu | grep 'Core(s)' | awk '{print $4}')C/$(nproc)T)")
storage=$(df -h --total | grep "total" | awk -F' ' '{print $2}'| sed 's/^ *//')
memory=$(free -h |grep Mem |awk -F' ' '{print $2}'| sed 's/^ *//')

echo "# HELP node_host_info value is always 1." >> "$output_file"
echo "# TYPE node_host_info gauge" >> "$output_file"
echo "node_host_info{hostname=\"$hostname\", os_version=\"$os_version\", cpu=\"$cpu\", storage=\"$storage\", memory=\"$memory\"} 1" >> "$output_file"
