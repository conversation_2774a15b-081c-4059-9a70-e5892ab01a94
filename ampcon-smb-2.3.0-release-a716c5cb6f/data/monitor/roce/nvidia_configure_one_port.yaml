# configure_one_port.yml
- name: Step 1 - Query RoCE device using ibdev2netdev
  shell: ibdev2netdev | grep -w "{{ port }}" | awk '{print $5}'
  register: ibdev_output
  failed_when: ibdev_output.rc != 0

- name: Step 1a - Extract RoCE device name
  set_fact:
    roce_device: "{{ ibdev_output.stdout_lines[0].split()[0] | default('') }}"
  when: ibdev_output.stdout_lines | length > 0

- name: Step 2 - Enable RoCEv2
  shell: cma_roce_mode -d {{ port }} -p 1 -m 2
  register: roce_mode
  failed_when: roce_mode.rc != 0

- name: Step 3 - Enable ECN if enabled
  shell: mlxconfig -d {{ port }} -y set ROCE_CC_PRIO_MASK_P1=0xFF
  when: script_params.ecn == "enable"
  register: ecn_result
  failed_when: ecn_result.rc != 0

- name: Step 4 - Firmware restart
  shell: mlxfwreset -d {{ port }} -l 3 -y r
  when: script_params.ecn == "enable"
  register: fw_reset
  failed_when: fw_reset.rc != 0

- name: Step 5 - Configure trust mode
  shell: mlnx_qos -i {{ roce_device }} --trust={{ script_params.trust_mode }}
  register: trust_result
  failed_when: trust_result.rc != 0

- name: Step 6 - Configure TC
  shell: >
    mlnx_qos -i {{ roce_device }}
    --prio_tc={{ script_params.up_to_tc | join(',') }}
    --tsa={{ script_params.tc_algorithm | join(',') }}
    --tcbw={{ script_params.tc_bw | join(',') }}
  register: tc_result
  failed_when: tc_result.rc != 0

- name: Step 7 - Configure PFC
  shell: mlnx_qos -i {{ roce_device }} --pfc={{ script_params.pfc_enabled_tc | join(',') }}
  register: pfc_result
  failed_when: pfc_result.rc != 0

- name: Step 8 - Configure CNP mode based on trust_mode
  shell: >
    mlxconfig -d {{ port }} -y set
    {% if script_params.trust_mode == 'pcp' %}
    CNP_802P_PRIO_P1={{ script_params.cnp_802p_prio }}
    {% elif script_params.trust_mode == 'dscp' %}
    CNP_DSCP_P1={{ script_params.cnp_dscp }}
    {% endif %}
  when: script_params.trust_mode in ['pcp', 'dscp']
  register: cnp_config_result
  failed_when: cnp_config_result.rc != 0

