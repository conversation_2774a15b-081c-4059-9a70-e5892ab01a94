#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: cc_test
@file: snmp_trap_task.py
@function:
@time: 2024/11/25 18:15
"""
import logging
from celery_app import my_celery_app
from celery_app.automation_task import AmpConBaseTask
from server.util.snmp_alarm_trap_util import handle_alarm_trap

LOG = logging.getLogger(__name__)


@my_celery_app.task(name="snmp_trap_fmt_alarm", base=AmpConBaseTask)
def handle_snmp_trap_fmt_alarm(trap_info, **kwargs):
    try:
        # add db operations by @2024-11-26
        LOG.info("Get alarm messages: %s" % trap_info)
        handle_alarm_trap(trap_info)
    except Exception as e:
        LOG.error(f"Failed to decode JSON: {e}")
