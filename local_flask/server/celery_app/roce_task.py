#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: cc_test
@file: snmp_trap_task.py
@function:
@time: 2024/11/25 18:15
"""
import json
import logging
from abc import ABC
import datetime as dt
from datetime import timezone
from celery_app import my_celery_app
from celery import Task
from server.db.db_common import DBCommon
from server.db.models.automation import RoceTask
from server.util.ssh_util import get_interactive_session
# from celery_app.automation_task import AmpConBaseTask
from server.util.snmp_alarm_trap_util import handle_alarm_trap
from server.db.models.inventory import inven_db
from server.util.roce_check import roce_function_inspection

LOG = logging.getLogger(__name__)


class ROCETask(Task, ABC):

    def __init__(self, *args, **kwargs):
        super(ROCETask, self).__init__(*args, **kwargs)

    def before_start(self, task_id, args, kwargs):
        """Hand<PERSON> called before the task starts.

        .. versionadded:: 5.2

        Arguments:
            task_id (str): Unique id of the task to execute.
            args (Tuple): Original arguments for the task to execute.
            kwargs (Dict): Original keyword arguments for the task to execute.

        Returns:
            None: The return value of this handler is ignored.

            id = Column(Integer, primary_key=True, autoincrement=True)
            task_name = Column(String(256))
            device_id = Column(Integer, ForeignKey('ansible_device.id', ondelete='CASCADE'))
            type = Column(String(32), default="check")  # check config
            status = Column(String(32), default="RUNNING") # RUNNING SUCCESS FAILED
            result = Column(Text)
        """
        # from celery.contrib import rdb; rdb.set_trace()
        task_name = kwargs.get("task_name", "")
        device_id = kwargs.get("device_id", "")
        type = kwargs.get("type", "check")
        status = kwargs.get("status", "RUNNING")
        result = kwargs.get("result", "")

        session = DBCommon.get_session()
        with session.begin(subtransactions=True):
            model = RoceTask(task_name=task_name if task_name else self.name,
                             device_id=device_id,
                             type=type, status=status, result=result)
            session.add(model)

    def on_success(self, retval, task_id, args, kwargs):
        """Success handler.

        Run by the worker if the task executes successfully.

        Arguments:
            retval (Any): The return value of the task.
            task_id (str): Unique id of the executed task.
            args (Tuple): Original arguments for the executed task.
            kwargs (Dict): Original keyword arguments for the executed task.

        Returns:
            None: The return value of this handler is ignored.
        """
        task_name = kwargs.get("task_name", "")
        status, result = retval
        session = DBCommon.get_session()
        with session.begin(subtransactions=True):
            session.query(RoceTask).filter(RoceTask.task_name == task_name).update(
                {RoceTask.status: "SUCCESS" if status else "FAILED", RoceTask.result: result})

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Error handler.

        This is run by the worker when the task fails.

        Arguments:
            exc (Exception): The exception raised by the task.
            task_id (str): Unique id of the failed task.
            args (Tuple): Original arguments for the task that failed.
            kwargs (Dict): Original keyword arguments for the task that failed.
            einfo (~billiard.einfo.ExceptionInfo): Exception information.

        Returns:
            None: The return value of this handler is ignored.
        """
        result = kwargs.get("result", str(exc))
        task_name = kwargs.get("task_name", "")
        session = DBCommon.get_session()
        with session.begin(subtransactions=True):
            session.query(RoceTask).filter(RoceTask.task_name == task_name).update(
                {RoceTask.status: "FAILED", RoceTask.result: result})


@my_celery_app.task(name="roce_check_task2", base=ROCETask)
def roce_check_task(*args, **kwargs):
    """
    roce巡检
    Args:
        info_dict:

    Returns:

    """
    LOG.info(f"roce check task start..")
    return roce_function_inspection(kwargs)
