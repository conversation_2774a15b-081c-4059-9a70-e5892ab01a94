from server.db.models.inventory import TaskStatus, GroupTask
from server.db.models.inventory import inven_db
from server.util import utils


def stat_group_tasks(name):
    task_type = utils.conform_task_type()
    if task_type == 'import':
        return
    switches = inven_db.get_group_switchs(name)

    task_names = [switch.sn + '_upgrade_' + name for switch in switches]

    session = inven_db.get_session()
    query = session.query(TaskStatus)
    executed_num = query.filter(TaskStatus.name.in_(task_names), TaskStatus.status == 'executed').count()
    running_num = query.filter(TaskStatus.name.in_(task_names), TaskStatus.status == 'running').count()
    error_num = query.filter(TaskStatus.name.in_(task_names), TaskStatus.status == 'error').count()
    missed_num = query.filter(TaskStatus.name.in_(task_names), TaskStatus.status == 'missed').count()

    task_statuses = query.filter(TaskStatus.name.in_(task_names)).all()
    marked_task_names = [task.name for task in task_statuses]
    missed_task_names = set(task_names) - set(marked_task_names)
    for miss_task_name in missed_task_names:
        task_status = TaskStatus(name=miss_task_name, status='missed')
        db.insert_or_update(task_status, 'name')

    db.update_model(GroupTask, filters={'name': [name]}, updates={'status': 'finished'})
    missed_num += len(missed_task_names)
    db.add_event(name, 'info', 'group image upgrade result {success: %s, running: %s, error: %s, missed: %s}'
                 % (executed_num, running_num, error_num, missed_num))

