from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    ForeignKey,
    Boolean,
    Table,
    UniqueConstraint
)
from sqlalchemy.orm import relationship, backref
from sqlalchemy.dialects.mysql import LONGTEXT

from server.db.db_common import DBCommon
from server.db.models.base import Base
from server.util.encrypt_util import aes_cipher


class CliTree(Base):
    __tablename__ = 'cli_tree'
    id = Column(Integer, primary_key=True, autoincrement=False)
    name = Column(String(255), index=True, nullable=False)
    alias = Column(String(32), index=True)
    description = Column(String(255), default='')
    pid = Column(Integer, ForeignKey('cli_tree.id'))
    level = Column(Integer)
    path = Column(String(255), default='')
    is_param = Column(Boolean, default=False)
    param_type = Column(String(32))
    default = Column(String(32))
    param_check = Column(String(32))
    viewable = Column(Boolean, default=True)
    checkable = Column(Boolean, default=True)

    children = relationship("CliTree", backref=backref('parent', remote_side=[id], uselist=False))


class Compatibility(Base):
    __tablename__ = 'compatibility'
    id = Column(Integer, primary_key=True, autoincrement=True)
    platform = Column(String(32), ForeignKey('switch_systeminfo.model', ondelete='CASCADE', onupdate='CASCADE'))
    version = Column(String(32))
    cli_node = Column(Integer, ForeignKey('cli_tree.id', ondelete='CASCADE', onupdate='CASCADE'))

    uix = UniqueConstraint('platform', 'cli_node', name='uix_1')


class GeneralTemplate(Base):
    __tablename__ = 'general_template'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(32), nullable=False, index=True, unique=True)
    description = Column(String(255), nullable=False)
    content = Column(LONGTEXT)
    j2_template_encrypted = Column('j2_template', LONGTEXT)
    params_encrypted = Column('params', LONGTEXT)
    internal = Column(Boolean, default=False)

    @property
    def params(self):
        return aes_cipher.decrypt(self.params_encrypted)

    @params.setter
    def params(self, params_str: str):
        self.params_encrypted = aes_cipher.encrypt(params_str)

    @property
    def j2_template(self):
        return aes_cipher.decrypt(self.j2_template_encrypted)

    @j2_template.setter
    def j2_template(self, template: str):
        self.j2_template_encrypted = aes_cipher.encrypt(template)


class GeneralTemplateWithTag(Base):
    __tablename__ = 'general_template_with_tag'
    id = Column(Integer, primary_key=True)
    name = Column(String(32))
    description = Column(String(255))
    content = Column(LONGTEXT)
    j2_template_encrypted = Column('j2_template', LONGTEXT)
    params_encrypted = Column('params', LONGTEXT)
    internal = Column(Boolean, default=False)
    tag = Column(String(255))

    @property
    def params(self):
        return aes_cipher.decrypt(self.params_encrypted)

    @params.setter
    def params(self, params_str: str):
        self.params_encrypted = aes_cipher.encrypt(params_str)

    @property
    def j2_template(self):
        return aes_cipher.decrypt(self.j2_template_encrypted)

    @j2_template.setter
    def j2_template(self, template: str):
        self.j2_template_encrypted = aes_cipher.encrypt(template)


class Tag(Base):
    __tablename__ = 'tag'
    id = Column(Integer, primary_key=True, autoincrement=True)
    record_id = Column(Integer, nullable=False, unique=True)
    record_type = Column(String(32), nullable=False, unique=True)
    tag_content = Column(String(255))


general_config_attach = Table('general_config_attach', Base.metadata,
Column('switch_sn', String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE')),
Column('general_config_name', String(32), ForeignKey('general_config.name', ondelete='CASCADE', onupdate='CASCADE')))


# general_config_attach_group = Table('general_config_attach_group', Base.metadata,
# Column('group_name', String(32), ForeignKey('group.name', ondelete='CASCADE', onupdate='CASCADE')),
# Column('general_config_name', String(32), ForeignKey('general_config.name', ondelete='CASCADE', onupdate='CASCADE')))


class GeneralConfig(Base):
    __tablename__ = 'general_config'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(32), index=True, unique=True, nullable=False)
    description = Column(String(255), default='')
    pid = Column(Integer, ForeignKey('general_config.id'))
    platform = Column(String(32), ForeignKey('switch_systeminfo.model'))
    level = Column(Integer)
    content_encrypted = Column('content', LONGTEXT)

    children = relationship("GeneralConfig", backref=backref('parent', remote_side=[id]))

    attach_switches = relationship('Switch', secondary=general_config_attach, backref='general_configs')
    # attach_groups = relationship('Group', secondary=general_config_attach_group, backref='configs')

    @property
    def content(self):
        return aes_cipher.decrypt(self.content_encrypted)

    @content.setter
    def content(self, content_str: str):
        self.content_encrypted = aes_cipher.encrypt(content_str)


class GeneralConfigParams(Base):
    __tablename__ = 'general_config_params'
    id = Column(Integer, primary_key=True, autoincrement=True)
    config_name = Column(String(32), ForeignKey('general_config.name', ondelete='CASCADE', onupdate='CASCADE'))
    template_name = Column(String(32), ForeignKey('general_template.name'))
    params = Column(Text)

    config = relationship('GeneralConfig', uselist=False, backref=backref('param', uselist=False))
    template = relationship('GeneralTemplate', backref='config_params')


class EmailServerSetting(Base):
    __tablename__ = 'email_server_setting'
    id = Column(Integer, primary_key=True, autoincrement=True)
    host = Column(String(128))
    port = Column(Integer)
    username = Column(String(128))
    password_encrypted = Column('password', String(256))
    ssl = Column(Boolean, default=False)
    tls = Column(Boolean, default=False)
    sender_email = Column(String(128))
    is_authentication = Column(Boolean, default=True)

    @property
    def password(self):
        return aes_cipher.decrypt(self.password_encrypted)

    @password.setter
    def password(self, password: str):
        self.password_encrypted = aes_cipher.encrypt(password)


class EmailRuleSettings(Base):
    __tablename__ = 'email_rule_settings'
    id = Column(Integer, primary_key=True, autoincrement=True)
    rule_name = Column(String(128))
    fabric_name_list = Column(Text)
    site_name_list = Column(Text)
    silent_time = Column(Integer)
    create_user = Column(String(128))
    email = Column(Text)
    enable = Column(Boolean, default=True)
    alarm_level_settings = Column(Text)
    alarm_type_settings = Column(Text)


class EmailRuleLogs(Base):
    __tablename__ = 'email_rule_logs'
    id = Column(Integer, primary_key=True, autoincrement=True)
    email_rule_name = Column(Integer)
    email_search_key = Column(Text)
    target_sn = Column(Text)
    receivers = Column(Text)
    subject = Column(Text)
    content = Column(Text)
    status = Column(String(128))
    error_message = Column(Text)


class GeneralDB(DBCommon):

    def get_params_by_name(self, name, session=None):
        session = session or self.get_session()
        template_param = session.query(GeneralTemplate).filter(GeneralTemplate.name == name).one()
        return template_param.params

    def get_template_by_name(self, name, session=None):
        session = session or self.get_session()
        template = session.query(GeneralTemplate).filter(GeneralTemplate.name == name).one()
        return template

    def update_entire_template_by_name(self, name, j2_template, params, session=None):
        session = session or self.get_session()
        session.begin()
        template = session.query(GeneralTemplate).filter(GeneralTemplate.name == name).one()
        template.j2_template = j2_template
        template.params = params
        session.commit()

    def update_template_by_name(self, name, content, type, session=None):
        session = session or self.get_session()
        session.begin()
        template = session.query(GeneralTemplate).filter(GeneralTemplate.name == name).one()
        if type == 'j2_template':
            template.j2_template = content
        elif type == 'template_var':
            template.params = content
        session.commit()

    def add_platform_support(self, node_id, platforms, version='2.11', session=None):
        session = session or self.get_session()
        for platform in platforms:
            com = session.query(Compatibility).filter(Compatibility.cli_node == node_id,
                                                      Compatibility.version == version,
                                                      Compatibility.platform == platform).first()
            if not com:
                session.add(Compatibility(cli_node=node_id, platform=platform, version=version))

        # if this support platform, this parents must support this platform
        cli_node = session.query(CliTree).filter(CliTree.id == node_id).first()
        if cli_node and cli_node.parent:
            self.add_platform_support(cli_node.parent.id, platforms, version, session=session)

    def del_platform_support(self, node_id, platforms, version='2.11', session=None):
        session = session or self.get_session()
        for platform in platforms:
            session.query(Compatibility).filter(Compatibility.cli_node == node_id,
                                                Compatibility.version == version,
                                                Compatibility.platform == platform).delete()

        # if this node doesn't support this platform, his children doesn't support yet
        cli_node = session.query(CliTree).filter(CliTree.id == node_id).first()
        if cli_node and cli_node.children:
            for child_node in cli_node.children:
                self.del_platform_support(child_node.id, platforms, version, session=session)

    def get_cli_nodes(self, session=None):
        session = session or self.get_session()
        cli_nodes = session.query(CliTree).all()
        return cli_nodes

    def get_cli_nodes_next_id(self, session=None):
        session = session or self.get_session()
        result = session.execute('select max(id) as max from cli_tree').first()
        return result['max'] + 1

    def get_platform_supports_clis(self, platform, version='2.11', session=None):
        session = session or self.get_session()
        cli_nodes = session.query(CliTree). \
            join(Compatibility, CliTree.id == Compatibility.cli_node).filter(Compatibility.platform == platform,
                                                                             Compatibility.version == version,
                                                                             CliTree.viewable == True).all()
        return cli_nodes

    def get_clinode_param_check(self, paths, line):
        session = self.get_session()
        cur_node = session.query(CliTree).filter(CliTree.name == paths[0], CliTree.level == 0).one()
        for i in range(1, len(paths)):
            if len(cur_node.children) == 1:
                cur_node = cur_node.children[0]
            elif '{{' in paths[i] and '}}' in paths[i]:
                param_type = line[i].split(':')[1]
                for child_node in cur_node.children:
                    if child_node.param_type == param_type or param_type in child_node.name:
                        cur_node = child_node
                        break
            else:
                for child_node in cur_node.children:
                    if child_node.name == paths[i]:
                        cur_node = child_node
                        break

        return cur_node

    def get_site_templates(self, session=None):
        session = session or self.get_session()
        templates = session.query(GeneralTemplate).filter(GeneralTemplate.internal==False).all()
        # GeneralTemplate.name.like('%site%')).all()
        return templates

    def create_root_config_node_if_no_exit(self):
        session = self.get_session()
        root_config = session.query(GeneralConfig).filter(GeneralConfig.name == 'root').first()
        if not root_config:
            session.add(GeneralConfig(id=0, name='root', description='root', level=0))
            session.flush()
            session.query(GeneralConfig).filter_by(name='root').update({GeneralConfig.id: 0})
            session.flush()

    def get_node_platforms(self, node_id):
        session = self.get_session()
        platforms = session.query(Compatibility.platform).filter_by(cli_node=node_id).distinct().all()
        return [platform.platform for platform in platforms]


general_db = GeneralDB()


if __name__ == '__main__':
    from server import cfg

    version = 0.1

    cfg.CONF(default_config_files=['../../automation.ini'])
    # session = session_factory.get_session()
    # db.create()
    # template = session.query(GeneralTemplate).filter(GeneralTemplate.name == 'inband').first()
    # print template
    # new_template = GeneralTemplate()
    # new_template.name = '1148_inband'
    # new_template.platform = 'N1148T-ON'
    # new_template.content = template.content
    # new_template.description = template.description
    # new_template.j2_template = template.j2_template
    # new_template.params = template.params
    # session.add(new_template)
    # template = session.query(GeneralTemplate).filter(GeneralTemplate.name == '1148_inband').first()
    # print template
    from server.db.models.inventory import Switch
    session = general_db.get_session()
    # platforms = ['as4610_54p', 'as4610_54t', 'as4610_54t_b', 'as4610_30p', 'as4610_30t', 'as5812_54x', 'N3024EP-ON',
    #              'N3024ET-ON', 'N3048EP-ON', 'N3048ET-ON', 'S4048-ON', 'S4148T-ON', 'S4148F-ON', 'P5101']
    # platforms = ['S4128F-ON', 'S5296F-ON', 'S5224F-ON']
    # versions = ['2.11', '3.6']
    # cli_nodes = general_db.get_cli_nodes()
    # print len(cli_nodes)
    # for cli_node in cli_nodes:
    #     for platform in platforms:
    #         for version in versions:
    #             session.add(Compatibility(platform=platform, cli_node=cli_node.id, version=version))
    #             print platform + ':' + str(cli_node.id) + ':' + version
    #             try:
    #                 session.flush()
    #             except:
    #                 pass


    # session.query(Compatibility).update({Compatibility.version: '2.11'})
    # compatibilities = session.query(Compatibility).all()
    # for compatibility in compatibilities:
    #     session.add(Compatibility(platform=compatibility.platform, version='3.6', cli_node=compatibility.cli_node))
    #
    # session.flush()
    # coms = session.query(Compatibility).filter_by(cli_node=3).all()
    # for com in coms:
    #     print com.platform
    # platforms = general_db.get_node_platforms(1)
    # print platforms
    nodes = session.query(Compatibility).filter_by(cli_node=2947, version='2.11').all()