# -*- coding: utf-8 -*-
import time
from datetime import datetime, timedelta
from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    Enum,
    DateTime,
    Boolean,
    JSON,
    ForeignKey,
    or_,
    and_,
    func
)
from sqlalchemy.orm import aliased
import json
import traceback

from server.db.db_common import DBCommon
from server.db.models.base import Base
import logging
LOG = logging.getLogger(__name__)


class RoceEasyDeployConfiguration(Base):
    __tablename__ = 'roce_easy_deploy_configuration'
    id = Column(Integer, autoincrement=True, primary_key=True)
    sysname = Column(String(64), nullable=False)
    switch_sn = Column(String(64), ForeignKey('switch.sn'), nullable=False)
    port = Column(JSON, nullable=False)
    queue = Column(JSON, nullable=False)
    enabled = Column(Boolean, nullable=False)
    mode = Column(Enum('lossy', 'lossless'))
    config_data = Column(Text)
    basic_info = Column(Text)
    pcp_dscp_info = Column(Text)
    lp_info = Column(Text)

class DLBConfiguration(Base):
    __tablename__ = 'dlb_configuration'
    id = Column(Integer, autoincrement=True, primary_key=True)
    sysname = Column(String(64), nullable=False)
    switch_sn = Column(String(64), ForeignKey('switch.sn'), nullable=False)
    dlb_enabled = Column(Boolean, nullable=False)
    mode = Column(Enum('dlb-normal', 'dlb-optimal', 'dlb-assigned'))
    config_data = Column(Text)
    
class PfcConfiguration(Base):
    __tablename__ = 'pfc_configuration'
    id = Column(Integer, autoincrement=True, primary_key=True)
    sysname = Column(String(64), nullable=False)
    switch_sn = Column(String(64), ForeignKey('switch.sn'), nullable=False)
    profile_name = Column(String(64), nullable=False)
    port = Column(JSON, nullable=False)
    queue = Column(JSON, nullable=False)
    enabled = Column(Boolean, nullable=False)
    is_all_ports = Column(Integer, nullable=False, default=False)
    is_all_queues = Column(Integer, nullable=False, default=False)
    config_data = Column(Text)
  
class PfcBufferIngressConfiguration(Base):
    __tablename__ = 'pfc_buffer_ingress_configuration'
    id = Column(Integer, autoincrement=True, primary_key=True)
    sysname = Column(String(64), nullable=False)
    switch_sn = Column(String(64), ForeignKey('switch.sn'), nullable=False)
    port = Column(JSON, nullable=False)
    queue = Column(JSON, nullable=False)
    shared_ratio = Column(String(64))
    threshold = Column(String(64))
    guaranteed = Column(String(64))
    reset_offset = Column(String(64))
    headroom = Column(String(64))
    is_all_ports = Column(Integer, nullable=False, default=False)
    is_all_queues = Column(Integer, nullable=False, default=False)
    config_data = Column(Text)
    
class PfcBufferEgressConfiguration(Base):
    __tablename__ = 'pfc_buffer_egress_configuration'
    id = Column(Integer, autoincrement=True, primary_key=True)
    sysname = Column(String(64), nullable=False)
    switch_sn = Column(String(64), ForeignKey('switch.sn'), nullable=False)
    port = Column(JSON, nullable=False)
    queue = Column(JSON, nullable=False)
    shared_ratio = Column(String(64))
    threshold = Column(String(64))
    is_all_ports = Column(Integer, nullable=False, default=False)
    is_all_queues = Column(Integer, nullable=False, default=False)
    config_data = Column(Text)
    
class PfcWdConfiguration(Base):
    __tablename__ = 'pfc_wd_configuration'
    id = Column(Integer, autoincrement=True, primary_key=True)
    sysname = Column(String(64), nullable=False)
    switch_sn = Column(String(64), ForeignKey('switch.sn'), nullable=False)
    port = Column(JSON, nullable=False)
    queue = Column(JSON, nullable=False)
    enabled = Column(Boolean, nullable=False)
    granularity = Column(String(64))
    detection_interval = Column(String(64))
    restore_interval = Column(String(64))
    threshold_period = Column(String(64))
    threshold_count = Column(String(64))
    restore_mode = Column(Enum('manual', 'auto'))
    restore_action = Column(Enum('forward', 'drop'))
    is_all_ports = Column(Integer, nullable=False, default=False)
    is_all_queues = Column(Integer, nullable=False, default=False)
    config_data = Column(Text)
    
class EcnConfiguration(Base):
    __tablename__ = 'ecn_configuration'
    id = Column(Integer, autoincrement=True, primary_key=True)
    sysname = Column(String(64), nullable=False)
    switch_sn = Column(String(64), ForeignKey('switch.sn'), nullable=False)
    enabled = Column(Boolean, default=True)
    mode = Column(Enum('latency-first', 'throughput-first'))
    config_data = Column(Text)
    
class EcnConfigurationDetail(Base):
    __tablename__ = 'ecn_configuration_detail'
    id = Column(Integer, autoincrement=True, primary_key=True)
    ecn_config_id = Column(Integer, nullable=False)
    sysname = Column(String(64), nullable=False)
    switch_sn = Column(String(64), ForeignKey('switch.sn'), nullable=False)
    port = Column(JSON, nullable=False)
    queue = Column(JSON, nullable=False)
    max_threshold = Column(Integer)
    min_threshold = Column(Integer)
    drop_probability = Column(Integer)
    ecn_threshold = Column(Integer)
    wred_enable = Column(Boolean)
    is_all_ports = Column(Integer, nullable=False, default=False)
    is_all_queues = Column(Integer, nullable=False, default=False)
       
class QosConfiguration(Base):
    __tablename__ = 'qos_configuration'
    id = Column(Integer, autoincrement=True, primary_key=True)
    sysname = Column(String(64), nullable=False)
    switch_sn = Column(String(64), ForeignKey('switch.sn'), nullable=False)
    forwarding_class = Column(String(64), nullable=False)
    local_priority = Column(Integer, nullable=False)

    scheduler = Column(String(64), nullable=False)

    mode = Column(Enum('SP', 'WRR', 'WFQ'), nullable=False, default='SP')
    weight = Column(Integer)
    guaranteed_rate = Column(Integer)
    config_data = Column(Text)
    
class QosIngressConfiguration(Base):
    __tablename__ = 'qos_ingress_configuration'
    id = Column(Integer, autoincrement=True, primary_key=True)

    sysname = Column(String(64), nullable=False)
    switch_sn = Column(String(64), ForeignKey('switch.sn'), nullable=False)
    classifier = Column(String(64), nullable=False)
    trust_mode = Column(Enum('dscp', 'ieee-802.1', 'inet-precedence'), nullable=False, default='dscp')
    port = Column(JSON, nullable=False)
    
    forwarding_class = Column(String(64), nullable=False)
    queue = Column(JSON, nullable=False)
    is_all_ports = Column(Integer, nullable=False, default=False)
    is_all_queues = Column(Integer, nullable=False, default=False)
    config_data = Column(Text)
    

class QosEgressConfiguration(Base):
    __tablename__ = 'qos_egress_configuration'
    id = Column(Integer, autoincrement=True, primary_key=True)

    sysname = Column(String(64), nullable=False)
    switch_sn = Column(String(64), ForeignKey('switch.sn'), nullable=False)
    scheduler_profile = Column(String(64), nullable=False)
    scheduler = Column(String(64), nullable=False)
    port = Column(JSON, nullable=False)

    forwarding_class = Column(String(64), nullable=False)
    local_priority = Column(Integer, nullable=False)
    is_all_ports = Column(Integer, nullable=False, default=False)
    is_all_queues = Column(Integer, nullable=False, default=False)
    config_data = Column(Text)

class ConfigurationOverview(Base):
    __tablename__ = 'configuration_overview'
    id = Column(Integer, autoincrement=True, primary_key=True)
    sysname = Column(String(64), nullable=False)
    switch_sn = Column(String(64), ForeignKey('switch.sn'), nullable=False)
    basic_info = Column(JSON)
    pcp_dscp_info = Column(JSON)
    lp_info = Column(JSON)
    

class RoceDBCommon(DBCommon):

    # ========== ORM Layer (Private Methods) ==========
    def _get_all_fabrics(self, session):
        """
        Private method to get all fabrics from database
        Args:
            session: Database session
        Returns:
            list: List of Fabric objects
        """
        from server.db.models.inventory import Fabric
        return session.query(Fabric).all()
    
    def _get_switches_by_fabric_id(self, session, fabric_id):
        """
        Private method to get switches by fabric ID
        Args:
            session: Database session
            fabric_id: Fabric ID
        Returns:
            list: List of Switch objects
        """
        from server.db.models.inventory import Switch, AssociationFabric
        return session.query(Switch).join(AssociationFabric).filter(
            AssociationFabric.fabric_id == fabric_id
        ).all()
    
    def _get_switch_by_sn(self, session, switch_sn):
        """
        Private method to get switch by serial number
        Args:
            session: Database session
            switch_sn: str, Switch serial number
        Returns:
            Switch object or None
        """
        from server.db.models.inventory import Switch
        return session.query(Switch).filter(Switch.sn == switch_sn).first()
    
    def _get_ports_by_platform_model(self, session, platform_model):
        """
        Private method to get ports by platform model
        Args:
            session: Database session
            platform_model: str, Platform model name
        Returns:
            list: List of ModelPhysicPort objects
        """
        from server.db.models.inventory import ModelPhysicPort
        return session.query(ModelPhysicPort).filter(
            ModelPhysicPort.platform_name == platform_model
        ).all()
    
    def _get_configs_by_model_and_switch_sn(self, session, model_class, switch_sn):
        """
        Private method to get configurations by model class and switch SN
        Args:
            session: Database session
            model_class: SQLAlchemy model class
            switch_sn: str, Switch serial number
        Returns:
            list: List of configuration objects
        """
        return session.query(model_class).filter(
            model_class.switch_sn == switch_sn
        ).all()
    
    def _get_pfc_config_by_id(self, session, config_id):
        """
        Private method to get PFC configuration by ID
        Args:
            session: Database session
            config_id: int, Configuration ID
        Returns:
            PfcConfiguration object or None
        """
        return session.query(PfcConfiguration).filter(PfcConfiguration.id == config_id).first()

    def _insert_pfc_config(self, session, config_data):
        """
        Private method to insert new PFC configuration
        Args:
            session: Database session
            config_data: dict, PFC configuration data
        Returns:
            int: New configuration ID
        """
        pfc_config = PfcConfiguration(**config_data)
        session.add(pfc_config)
        session.flush()
        return pfc_config.id

    def _update_pfc_config(self, session, config_id, updates):
        """
        Private method to update PFC configuration
        Args:
            session: Database session
            config_id: int, Configuration ID
            updates: dict, Update data
        Returns:
            bool: Whether update successful
        """
        result = session.query(PfcConfiguration).filter(PfcConfiguration.id == config_id).update(updates)
        return result > 0

    def _get_switch_by_sn_or_raise(self, session, switch_sn):
        """
        Private method to get switch by SN with error handling
        Args:
            session: Database session
            switch_sn: str, Switch serial number
        Returns:
            Switch object
        Raises:
            ValueError: If switch not found
        """
        switch = self._get_switch_by_sn(session, switch_sn)
        if not switch:
            raise ValueError(f"Switch with SN {switch_sn} not found")
        return switch

    def _get_config_by_id_or_raise(self, session, model_class, config_id):
        """
        Private method to get configuration by ID with error handling
        Args:
            session: Database session
            model_class: SQLAlchemy model class
            config_id: int, Configuration ID
        Returns:
            Configuration object
        Raises:
            ValueError: If configuration not found
        """
        config = session.query(model_class).filter_by(id=config_id).first()
        if not config:
            raise ValueError(f"Configuration with ID {config_id} not found")
        return config

    def _build_fabric_join_query(self, session, model_class):
        """
        Private method to build common fabric join query with aliases
        Args:
            session: Database session
            model_class: SQLAlchemy model class
        Returns:
            tuple: (query_object, SwitchAlias, AssocAlias, FabricAlias)
        """
        from server.db.models.inventory import Switch, AssociationFabric, Fabric
        from sqlalchemy.orm import aliased
        
        SwitchAlias = aliased(Switch)
        AssocAlias = aliased(AssociationFabric)
        FabricAlias = aliased(Fabric)
        
        query = (
            session.query(model_class, SwitchAlias, AssocAlias, FabricAlias)
            .outerjoin(SwitchAlias, model_class.switch_sn == SwitchAlias.sn)
            .outerjoin(AssocAlias, SwitchAlias.id == AssocAlias.switch_id)
            .outerjoin(FabricAlias, AssocAlias.fabric_id == FabricAlias.id)
        )
        
        return query, SwitchAlias, AssocAlias, FabricAlias

    def _serialize_config_with_fabric(self, config, switch, fabric):
        """
        Private method to serialize configuration with fabric information
        Args:
            config: Configuration object with make_dict() method
            fabric: Fabric object or None
        Returns:
            dict: Serialized configuration data
        """
        config_dict = config.make_dict()
        if fabric:
            config_dict.update({"fabric": fabric.fabric_name})
        if switch:
            config_dict.update({"sysname": switch.host_name})
        return config_dict

    # ========== Service Layer (Public Methods) ==========
    def get_fabric_switches_data(self, query_model=None):
        """
        Service method to get fabric switches data with business logic
        Args:
            query_model: str, Optional. Configuration model name to filter switches
        Returns:
            dict: Formatted fabric switches data
        """
        session = self.get_session()
        
        # Define enabled switch models for business logic
        enabled_models = [
            "N8560-32C",
            "N8550-32C", 
            "N8550-24CD8D",
            "N8550-64C",
            "N9550-32D",
            "N9550-64D",
            "N9600-64OD (2U)",
            "AS7726-32X",
            "AS7326-56X",
            "AS9716-32D"
        ]
        
        # Define supported model mapping for query_model filtering
        model_mapping = {
            "PfcConfiguration": PfcConfiguration,
            "EcnConfiguration": EcnConfiguration,
            "QosConfiguration": QosConfiguration,
            "PfcWdConfiguration": PfcWdConfiguration,
            "DLBConfiguration": DLBConfiguration,
            "RoceEasyDeployConfiguration": RoceEasyDeployConfiguration,
            "PfcBufferIngressConfiguration": PfcBufferIngressConfiguration,
            "PfcBufferEgressConfiguration": PfcBufferEgressConfiguration
        }
        
        # Get all fabrics
        fabrics = self._get_all_fabrics(session)
        response_data = {}
        
        # Get configured switch SNs using SQL if query_model is provided
        configured_switch_sns = set()
        if query_model and query_model in model_mapping:
            model_class = model_mapping[query_model]
            
            # Special handling for PFC Buffer configurations
            if query_model in ["PfcBufferIngressConfiguration", "PfcBufferEgressConfiguration"]:
                # Use SQL UNION to get configured switches from both tables in a single query
                ingress_query = session.query(PfcBufferIngressConfiguration.switch_sn).distinct().filter(
                    PfcBufferIngressConfiguration.switch_sn.isnot(None)
                )
                egress_query = session.query(PfcBufferEgressConfiguration.switch_sn).distinct().filter(
                    PfcBufferEgressConfiguration.switch_sn.isnot(None)
                )
                
                # Combine queries using UNION
                combined_query = ingress_query.union(egress_query)
                configured_sns = combined_query.all()
                configured_switch_sns = {sn[0] for sn in configured_sns}
            else:
                # Use SQL to get distinct switch_sn values for other models
                configured_sns = session.query(model_class.switch_sn).distinct().filter(
                    model_class.switch_sn.isnot(None)
                ).all()
                configured_switch_sns = {sn[0] for sn in configured_sns}
        
        # Process each fabric
        for fabric in fabrics:
            switches = self._get_switches_by_fabric_id(session, fabric.id)
            
            # Transform switch data with business logic
            switch_list = []
            for switch in switches:
                # Check if switch is enabled based on platform model
                base_enabled = switch.platform_model in enabled_models
                
                # If query_model is provided, check if switch is already configured
                if (query_model and switch.sn in configured_switch_sns) or switch.reachable_status != 0:
                    enabled = False  # Disable if already configured
                else:
                    enabled = base_enabled
                
                switch_data = {
                    "switch_sn": switch.sn,
                    "sysname": switch.host_name,
                    "enabled": enabled
                }
                switch_list.append(switch_data)
            
            response_data[fabric.fabric_name] = switch_list
        
        session.close()
        return response_data
    
    def get_ports_by_switch_sn_data(self, switch_sn):
        """
        Service method to get ports by switch serial number
        Args:
            switch_sn: str, Switch serial number
        Returns:
            list: Formatted port data
        Raises:
            ValueError: If switch_sn is empty or switch not found
        """
        if not switch_sn:
            raise ValueError("switch_sn is required")
        
        session = self.get_session()
        
        # Find switch by serial number
        switch = self._get_switch_by_sn(session, switch_sn)
        if not switch:
            raise ValueError("Switch not found")
        
        # Get ports by platform model
        ports = self._get_ports_by_platform_model(session, switch.platform_model)
        
        # Transform data format
        port_list = []
        for port in ports:
            port_data = {
                "port_name": port.port_name,
                "port_type": port.port_type
            }
            port_list.append(port_data)
        
        return port_list
    
    def get_filter_ports_by_switch_sn_data(self, switch_sn, query_model):
        """
        Service method to get filtered (unused) ports by switch SN and model
        Args:
            switch_sn: str, Switch serial number
            query_model: str, Configuration model name
        Returns:
            list: Filtered port data
        Raises:
            ValueError: If parameters are invalid or switch not found
        """
        if not switch_sn:
            raise ValueError("switch_sn is required")
        
        if not query_model:
            raise ValueError("query_model is required")
        
        # Define supported model mapping
        model_mapping = {
            "PfcConfiguration": PfcConfiguration,
            "EcnConfigurationDetail": EcnConfigurationDetail,
            "QosIngressConfiguration": QosIngressConfiguration,
            "QosEgressConfiguration": QosEgressConfiguration,
            "PfcWdConfiguration": PfcWdConfiguration,
            "DLBConfiguration": DLBConfiguration,
            "RoceEasyDeployConfiguration": RoceEasyDeployConfiguration,
            "PfcBufferIngressConfiguration": PfcBufferIngressConfiguration,
            "PfcBufferEgressConfiguration": PfcBufferEgressConfiguration
        }
        
        if query_model not in model_mapping:
            raise ValueError(f"Unsupported query_model: {query_model}")
        
        model_class = model_mapping[query_model]
        session = self.get_session()
        
        # Find switch by serial number
        switch = self._get_switch_by_sn(session, switch_sn)
        if not switch:
            raise ValueError("Switch not found")
        
        # Get all ports for this switch
        ports = self._get_ports_by_platform_model(session, switch.platform_model)
        
        # Get configurations that use ports
        configs = self._get_configs_by_model_and_switch_sn(session, model_class, switch_sn)
        
        # Collect used ports from configurations
        used_ports = set()
        for config in configs:
            if hasattr(config, 'port') and config.port:
                if isinstance(config.port, list):
                    used_ports.update(config.port)
                else:
                    used_ports.add(config.port)
        
        # Filter out used ports
        port_list = []
        for port in ports:
            
            port_data = {
                "port_name": port.port_name,
                "port_type": port.port_type,
                "enabled": port.port_name not in used_ports
            }
            port_list.append(port_data)
        
        return port_list

    def get_filter_queues_by_switch_sn_data(self, switch_sn, query_model):
        if not switch_sn:
            raise ValueError("switch_sn is required")
        if not query_model:
            raise ValueError("query_model is required")
        model_mapping = {
            "PfcConfiguration": PfcConfiguration,
            "EcnConfigurationDetail": EcnConfigurationDetail,
            "QosIngressConfiguration": QosIngressConfiguration,
            "QosEgressConfiguration": QosEgressConfiguration,
            "PfcWdConfiguration": PfcWdConfiguration,
            "DLBConfiguration": DLBConfiguration,
            "RoceEasyDeployConfiguration": RoceEasyDeployConfiguration,
            "PfcBufferIngressConfiguration": PfcBufferIngressConfiguration,
            "PfcBufferEgressConfiguration": PfcBufferEgressConfiguration
        }
        if query_model not in model_mapping:
            raise ValueError(f"Unsupported query_model: {query_model}")
        model_class = model_mapping[query_model]
        session = self.get_session()
        switch = self._get_switch_by_sn(session, switch_sn)
        if not switch:
            raise ValueError("Switch not found")
        configs = self._get_configs_by_model_and_switch_sn(session, model_class, switch_sn)
        used_queues = set()
        for config in configs:
            if hasattr(config, 'queue') and config.queue:
                if isinstance(config.queue, list):
                    used_queues.update(config.queue)
                else:
                    used_queues.add(config.queue)
        queue_list = []
        for queue in ['0', '1', '2', '3', '4', '5', '6', '7']:
            queue_data = {
                "queue_name": queue,
                "enabled": queue not in used_queues
            }
            queue_list.append(queue_data)
        return queue_list
    
    def get_switch_by_sn(self, switch_sn):
        """
        Service method to get switch by serial number
        Args:
            switch_sn: str, Switch serial number
        Returns:
            Switch object or None
        """
        session = self.get_session()
        return self._get_switch_by_sn(session, switch_sn)

    def check_ecn_main_config_exists(self, switch_sn):
        """
        Check if ECN main configuration already exists for a switch (Service layer)
        Args:
            switch_sn: str, Switch serial number
        Returns:
            bool: True if ECN main configuration exists, False otherwise
        """
        if not switch_sn:
            return False
        
        session = self.get_session()
        existing_config = session.query(EcnConfiguration).filter_by(switch_sn=switch_sn).first()
        return existing_config is not None

    def get_pfc_configuration_by_id(self, config_id):
        """
        Service method to get PFC configuration by ID
        Args:
            config_id: int, Configuration ID
        Returns:
            PfcConfiguration object or None
        """
        session = self.get_session()
        return self._get_pfc_config_by_id(session, config_id)
    
    def get_pfc_configuration_by_sn(self, switch_sn):
        session = self.get_session()
        return session.query(PfcConfiguration).filter_by(switch_sn=switch_sn).all()

    def check_pfc_profile_exists(self, switch_sn, profile_name):
        """
        Service method to check if PFC profile exists for a specific switch
        Args:
            switch_sn: str, Switch serial number
            profile_name: str, PFC profile name
        Returns:
            bool: True if profile exists, False otherwise
        """
        session = self.get_session()
        existing_config = session.query(PfcConfiguration).filter_by(
            switch_sn=switch_sn,
            profile_name=profile_name
        ).first()
        return existing_config is not None

    def save_pfc_configurations(self, configurations_data):
        """
        Service method to save multiple PFC configurations to database
        Args:
            configurations_data: list, List of prepared database configuration data
        Returns:
            list: List of new configuration IDs
        Raises:
            ValueError: If validation fails
        """
        if not configurations_data:
            raise ValueError("No configurations provided")
        
        session = self.get_session()
        config_ids = []
        
        with session.begin(subtransactions=True):
            for db_config in configurations_data:
                # Validate switch exists using reusable method
                switch_sn = db_config.get("switch_sn")
                if not switch_sn:
                    raise ValueError("switch_sn is required")
                
                self._get_switch_by_sn_or_raise(session, switch_sn)
                
                # Save to database
                new_config_id = self._insert_pfc_config(session, db_config)
                config_ids.append(new_config_id)
                
        return config_ids

    def save_ecn_configurations(self, configurations_data):
        """
        Service method to save multiple ECN configurations to database
        Args:
            configurations_data: list, List of prepared database configuration data
                [
                    {
                        "sysname": str,
                        "switch_sn": str,
                        "enabled": bool,
                        "mode": str,
                        "details": list,  # List of detail configurations
                        "config_data": str  # JSON string of config data
                    }
                ]
        Returns:
            list: List of new configuration IDs
        Raises:
            ValueError: If validation fails
        """
        if not configurations_data:
            raise ValueError("No configurations provided")
        
        session = self.get_session()
        
        with session.begin(subtransactions=True):
            for db_config in configurations_data:
                # Validate switch exists using reusable method
                switch_sn = db_config.get("switch_sn")
                if not switch_sn:
                    raise ValueError("switch_sn is required")
                
                self._get_switch_by_sn_or_raise(session, switch_sn)
                
                # Create main ECN configuration
                main_config = EcnConfiguration(
                    sysname=db_config.get("sysname"),
                    switch_sn=switch_sn,
                    enabled=db_config.get("enabled", True),
                    mode=db_config.get("mode"),
                    config_data=db_config.get("config_data")
                )
                session.add(main_config)
                session.flush()
                
                main_config_id = main_config.id
                
                
                # Create detail configurations if provided
                details = db_config.get("details", [])
                for detail in details:
                    detail_config = EcnConfigurationDetail(
                        ecn_config_id=main_config_id,
                        sysname=db_config.get("sysname"),
                        switch_sn=switch_sn,
                        port=detail.get("port", []),
                        queue=detail.get("queue", []),
                        max_threshold=detail.get("max_threshold"),
                        min_threshold=detail.get("min_threshold"),
                        drop_probability=detail.get("drop_probability"),
                        ecn_threshold=detail.get("ecn_threshold"),
                        wred_enable=detail.get("wred_enable", False),
                        is_all_ports=detail.get("is_all_ports", False),
                        is_all_queues=detail.get("is_all_queues", False)
                    )
                    session.add(detail_config)

    def update_ecn_configurations(self, switch_sn, configurations):
        """
        Update ECN configurations - simplified version
        Business Logic: Each switch has only ONE main ECN configuration that can only be updated,
        but EcnConfigurationDetail records can be added/updated/deleted.
        Args:
            switch_sn: str, Switch serial number
            configurations: list, New configuration list from API (should contain only one main config)
        """
        session = self.get_session()
        
        with session.begin(subtransactions=True):
            # Process the main configuration (only one expected)
            config = configurations[0]  # Take first configuration
            
            # Update existing main configuration attributes only
            update_data = {
                "sysname": config.get("sysname"),
                "enabled": config.get("enabled", True),
                "mode": config.get("mode"),
                "config_data": config.get("config_data")
            }

            main_config = session.query(EcnConfiguration).filter_by(switch_sn=switch_sn).first()
            if not main_config:
                raise ValueError(f"ECN main configuration not found for switch {switch_sn}")
            
            main_config_id = main_config.id

            session.query(EcnConfiguration).filter_by(switch_sn=switch_sn).update(update_data)
                        
            # Process EcnConfigurationDetail: Create/Update/Delete
            # Get existing detail configurations
            existing_details = session.query(EcnConfigurationDetail).filter_by(ecn_config_id=main_config_id).all()
            existing_detail_map = {detail.id: detail for detail in existing_details}
            updated_detail_ids = set()
            
            # Process details from configurations
            for detail in config.get("details", []):
                detail_id = detail.get("detail_id")
                
                if detail_id and int(detail_id) in existing_detail_map:
                    # Update existing detail configuration
                    updated_detail_ids.add(int(detail_id))
                    detail_config = existing_detail_map[int(detail_id)]
                    
                    # Update detail fields
                    detail_config.sysname = config.get("sysname")
                    detail_config.port = detail.get("port", [])
                    detail_config.queue = detail.get("queue", [])
                    detail_config.max_threshold = detail.get("max_threshold")
                    detail_config.min_threshold = detail.get("min_threshold")
                    detail_config.drop_probability = detail.get("drop_probability")
                    detail_config.ecn_threshold = detail.get("ecn_threshold")
                    detail_config.wred_enable = detail.get("wred_enable", False)
                    detail_config.is_all_ports = detail.get("is_all_ports", False)
                    detail_config.is_all_queues = detail.get("is_all_queues", False)
                    detail_config.config_data = config.get("config_data")
                else:
                    # Create new detail configuration
                    new_detail = EcnConfigurationDetail(
                        ecn_config_id=main_config_id,
                        sysname=config.get("sysname"),
                        switch_sn=switch_sn,
                        port=detail.get("port", []),
                        queue=detail.get("queue", []),
                        max_threshold=detail.get("max_threshold"),
                        min_threshold=detail.get("min_threshold"),
                        drop_probability=detail.get("drop_probability"),
                        ecn_threshold=detail.get("ecn_threshold"),
                        wred_enable=detail.get("wred_enable", False),
                        is_all_ports=detail.get("is_all_ports", False),
                        is_all_queues=detail.get("is_all_queues", False)
                    )
                    session.add(new_detail)
            
            # Delete unused detail configurations
            for detail_id in existing_detail_map.keys():
                if detail_id not in updated_detail_ids:
                    session.query(EcnConfigurationDetail).filter_by(id=detail_id).delete()
                
    def get_pfc_configurations_by_switch_sn(self, switch_sn):
        """
        Service method to get all PFC configurations for a specific switch
        Args:
            switch_sn: str, Switch serial number
        Returns:
            list: List of PfcConfiguration objects
        """
        session = self.get_session()
        return session.query(PfcConfiguration).filter_by(switch_sn=switch_sn).all()

    def update_pfc_configurations(self, switch_sn, configurations):
        """
        Update PFC configurations - simplified version
        Args:
            switch_sn: str, Switch serial number
            configurations: list, New configuration list from API
        """
        session = self.get_session()
        
        # Get existing configurations
        existing_configs = session.query(PfcConfiguration).filter_by(switch_sn=switch_sn).all()
        existing_map = {config.id: config for config in existing_configs}
        
        with session.begin(subtransactions=True):
            updated_ids = set()
            
            # Update/Create operations
            for config in configurations:
                config_id = config.get("config_id")
                
                if config_id and int(config_id) in existing_map:
                    # Update existing configuration
                    updated_ids.add(int(config_id))
                    update_data = {
                        "sysname": config.get("sysname"),
                        "switch_sn": switch_sn,
                        "profile_name": config.get("profile_name"),
                        "port": config.get("port"),
                        "queue": config.get("queue"),
                        "enabled": config.get("enabled"),
                        "is_all_ports": config.get("is_all_ports", False),
                        "is_all_queues": config.get("is_all_queues", False),
                        "config_data": config.get("config_data")
                    }
                    self._update_pfc_configuration(int(config_id), update_data)
                else:
                    # Create new configuration
                    db_config = {
                        "sysname": config.get("sysname"),
                        "switch_sn": switch_sn,
                        "profile_name": config.get("profile_name"),
                        "port": config.get("port"),
                        "queue": config.get("queue"),
                        "enabled": config.get("enabled"),
                        "is_all_ports": config.get("is_all_ports", False),
                        "is_all_queues": config.get("is_all_queues", False),
                        "config_data": config.get("config_data")
                    }
                    self._insert_pfc_config(session, db_config)
            
            # Delete unused configurations
            for config_id in existing_map.keys():
                if config_id not in updated_ids:
                    session.query(PfcConfiguration).filter_by(id=config_id).delete()

    def update_pfc_buffer_configurations(self, switch_sn, configurations):
        """
        Update PFC Buffer configurations - simplified version
        Args:
            switch_sn: str, Switch serial number
            configurations: list, New configuration list from API
        """
        session = self.get_session()
        
        # Separate configurations by traffic type
        ingress_configs = [c for c in configurations if c.get("traffic_type") == "ingress"]
        egress_configs = [c for c in configurations if c.get("traffic_type") == "egress"]
        
        with session.begin(subtransactions=True):
            # Process ingress configurations
            if ingress_configs:
                existing_ingress = session.query(PfcBufferIngressConfiguration).filter_by(switch_sn=switch_sn).all()
                existing_map = {config.id: config for config in existing_ingress}
                updated_ids = set()
                
                # Update/Create operations
                for config in ingress_configs:
                    config_id = config.get("config_id")
                    if config_id and int(config_id) in existing_map:
                        # Update existing
                        updated_ids.add(int(config_id))
                        update_data = {
                            "sysname": config.get("sysname"),
                            "port": config.get("port"),
                            "queue": config.get("queue"),
                            "shared_ratio": config.get("shared_ratio"),
                            "threshold": config.get("threshold"),
                            "guaranteed": config.get("guaranteed"),
                            "reset_offset": config.get("reset_offset"),
                            "headroom": config.get("headroom"),
                            "is_all_ports": config.get("is_all_ports", False),
                            "is_all_queues": config.get("is_all_queues", False),
                            "config_data": config.get("config_data")
                        }
                        self._update_pfc_buffer_ingress_configuration(int(config_id), update_data)
                    else:
                        # Create new
                        self._insert_pfc_buffer_ingress_configuration(
                            config.get("sysname"), switch_sn,
                            config.get("port"), config.get("queue"),
                            config.get("shared_ratio"), config.get("threshold"),
                            config.get("guaranteed"), config.get("reset_offset"),
                            config.get("headroom"), config.get("is_all_ports", False),
                            config.get("is_all_queues", False), config.get("config_data")
                        )
                
                # Delete unused configurations
                for config_id in existing_map.keys():
                    if config_id not in updated_ids:
                        session.query(PfcBufferIngressConfiguration).filter_by(id=config_id).delete()
            
            # Process egress configurations
            if egress_configs:
                existing_egress = session.query(PfcBufferEgressConfiguration).filter_by(switch_sn=switch_sn).all()
                existing_map = {config.id: config for config in existing_egress}
                updated_ids = set()
                
                # Update/Create operations
                for config in egress_configs:
                    config_id = config.get("config_id")
                    if config_id and int(config_id) in existing_map:
                        # Update existing
                        updated_ids.add(int(config_id))
                        update_data = {
                            "sysname": config.get("sysname"),
                            "port": config.get("port"),
                            "queue": config.get("queue"),
                            "shared_ratio": config.get("shared_ratio"),
                            "threshold": config.get("threshold"),
                            "is_all_ports": config.get("is_all_ports", False),
                            "is_all_queues": config.get("is_all_queues", False),
                            "config_data": config.get("config_data")
                        }
                        self._update_pfc_buffer_egress_configuration(int(config_id), update_data)
                    else:
                        # Create new
                        self._insert_pfc_buffer_egress_configuration(
                            config.get("sysname"), switch_sn,
                            config.get("port"), config.get("queue"),
                            config.get("shared_ratio"), config.get("threshold"),
                            config.get("is_all_ports", False), config.get("is_all_queues", False),
                            config.get("config_data")
                        )
                
                # Delete unused configurations
                for config_id in existing_map.keys():
                    if config_id not in updated_ids:
                        session.query(PfcBufferEgressConfiguration).filter_by(id=config_id).delete()


    def update_pfc_config_data(self, config_id, config_data):
        """
        Service method to update PFC configuration data field
        Args:
            config_id: int, Configuration ID
            config_data: str, JSON string of configuration data
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            self._update_pfc_configuration(config_id, {"config_data": config_data})

    def get_pfc_configs_detail_by_switch_sn(self, switch_sn):
        """
        Get PFC configuration details by switch SN with multi-table joins.
        
        Args:
            switch_sn (str): Switch serial number
            
        Returns:
            list: List of PFC configurations with related fabric information
        """
        session = self.get_session()
        
        # Use reusable fabric join query builder
        query, SwitchAlias, AssocAlias, FabricAlias = self._build_fabric_join_query(session, PfcConfiguration)
        
        query_result = (
            query
            .filter(PfcConfiguration.switch_sn == switch_sn)
            .order_by(PfcConfiguration.id)
            .all()
        )
        
        result_data = []
        for config, switch, assoc, fabric in query_result:
            # Use reusable serialization method
            config_dict = self._serialize_config_with_fabric(config, switch, fabric)
            result_data.append(config_dict)
        
        return result_data

    def get_pfc_configs_list_data(self, request_data):
        """
        Get PFC configurations list with search, pagination and tree structure.
        
        Args:
            request_data (dict): Request data containing search and pagination parameters
            
        Returns:
            dict: Paginated tree-structured PFC configuration data
        """
        session = self.get_session()
        
        # Import required modules
        from server.util import utils
        from sqlalchemy import func, text, asc, desc
        
        # Use reusable fabric join query builder
        pre_query, SwitchAlias, AssocAlias, FabricAlias = self._build_fabric_join_query(session, PfcConfiguration)

        # Handle custom sorting with window function
        sort_fields = request_data.get("sortFields", [])
        window_order_by = []
        has_sysname_sort = False
        sysname_order = ""
        
        if sort_fields:
            # Build order_by list for window function
            for field in sort_fields:
                field_name = field.get("field")
                field_order = field.get("order", "asc")
                
                if hasattr(PfcConfiguration, field_name):
                    column = getattr(PfcConfiguration, field_name)
                    
                    # Handle JSON field sorting by casting to Text
                    if field_name == "port" or field_name == "queue":
                        from sqlalchemy import Text
                        column = column.cast(Text)
                    
                    if field_order == "asc":
                        window_order_by.append(asc(column))
                    elif field_order == "desc":
                        window_order_by.append(desc(column))
                    
                    # Check if sysname is in sort fields
                    if field_name == "sysname":
                        has_sysname_sort = True
                        sysname_order = field_order

            pre_query = pre_query.add_columns(
                func.row_number().over(
                    partition_by=PfcConfiguration.switch_sn,
                    order_by=window_order_by
                ).label('row_num')
            )
        else:
            # Default sorting by id if no custom sort fields provided
            pre_query = pre_query.add_columns(
                func.row_number().over(
                    partition_by=PfcConfiguration.switch_sn,
                    order_by=PfcConfiguration.id
                ).label('row_num')
            )
        
        # Apply different ordering based on whether sysname is in sort fields
        if has_sysname_sort:
            # For sysname sorting: maintain switch_sn grouping while sorting by sysname
            if sysname_order == "asc":
                pre_query = pre_query.order_by(PfcConfiguration.sysname.asc(), PfcConfiguration.switch_sn, text('row_num'))
            else:
                pre_query = pre_query.order_by(PfcConfiguration.sysname.desc(), PfcConfiguration.switch_sn, text('row_num'))
        else:
            # For non-sysname sorting: order by switch_sn, then row_num
            pre_query = pre_query.order_by(PfcConfiguration.switch_sn, text('row_num'))

        # # Handle search conditions
        # search_fields_data = request_data.get("searchFields", {})
        # search_fields = search_fields_data.get("fields", [])
        # search_value = search_fields_data.get("value", "")
        
        # if "port" in search_fields and search_value:
        #     from sqlalchemy import Text, or_
        #     column = getattr(PfcConfiguration, "port")
        #     search_condition = column.cast(Text).ilike(f'%{search_value}%')
        #     pre_query = pre_query.filter(or_(search_condition))

        # Get paginated results using query_helper
        page_num, page_size, total_count, query_result = utils.query_helper(
            PfcConfiguration,
            pre_query=pre_query,
            data=request_data
        )

        # Build tree structure organized by switch_sn
        switch_groups = {}
        current_switch = None
        tree_data = []

        for config, switch, assoc, fabric, row_num in query_result:
            # Use reusable serialization method
            config_dict = self._serialize_config_with_fabric(config, switch, fabric)
            
            switch_sn = config_dict.get("switch_sn")
            
            if switch_sn != current_switch:
                # New switch encountered, create new root node
                current_switch = switch_sn
                
                # Start with serialized data and update with additional fields
                root_node = config_dict.copy()
                root_node.update({"children": []})
                
                tree_data.append(root_node)
                switch_groups[switch_sn] = root_node
            else:
                # Subsequent config for same switch, add as child node
                child_node = config_dict.copy()
                child_node.update(sysname="")
                
                current_root = switch_groups.get(switch_sn)
                if current_root:
                    current_root.get("children", []).append(child_node)
        
        return {
            "data": tree_data,
            "page": page_num,
            "pageSize": page_size,
            "total": total_count
        }

    def get_pfc_configuration_for_deletion(self, config_id):
        """
        Get PFC configuration and switch information for deletion deployment.
        
        Args:
            config_id (int): PFC configuration ID
            
        Returns:
            dict: Result containing config data and switch object for deployment
        """
        session = self.get_session()
        
        # Get PFC configuration by ID using reusable method
        config = self._get_config_by_id_or_raise(session, PfcConfiguration, config_id)
        
        # Get switch information using reusable method
        switch = self._get_switch_by_sn_or_raise(session, config.switch_sn)
        
        # Return both config object and switch object for proper function calls
        return {
            "config": config,      # Original object for _build_pfc_config_data
            "switch": switch,             # Switch object for deploy_roce_configuration
        }

    def save_pfc_buffer_configurations(self, configurations):
        """
        Service method to save PFC Buffer configurations
        Args:
            configurations: list, List of PFC Buffer configuration data
        Returns:
            dict: Operation result containing saved config IDs and switch info
        Raises:
            ValueError: If validation fails or configuration creation fails
        """
        if not configurations:
            raise ValueError("No configurations provided")
        
        session = self.get_session()
        
        with session.begin(subtransactions=True):
            # Phase 1: Save all configurations to database
            for config in configurations:
                switch_sn = config.get("switch_sn")
                sysname = config.get("sysname")
                traffic_type = config.get("traffic_type", "").lower()
                
                if not switch_sn:
                    raise ValueError("switch_sn is required")
                if not sysname:
                    raise ValueError("sysname is required")
                if not traffic_type:
                    raise ValueError("traffic_type is required")
                
                # Validate traffic_type
                if traffic_type not in ["ingress", "egress"]:
                    raise ValueError(f"Invalid traffic_type: {traffic_type}")
                
                # Insert configuration based on traffic type
                if traffic_type == "ingress":
                    self._insert_pfc_buffer_ingress_configuration(
                        sysname=sysname,
                        switch_sn=switch_sn,
                        port=config.get("port", []),
                        queue=config.get("queue", []),
                        shared_ratio=config.get("shared_ratio"),
                        threshold=config.get("threshold"),
                        guaranteed=config.get("guaranteed"),
                        reset_offset=config.get("reset_offset"),
                        headroom=config.get("headroom"),
                        is_all_ports=config.get("is_all_ports", False),
                        is_all_queues=config.get("is_all_queues", False),
                        config_data=config.get("config_data")
                    )
                else:  # egress
                    self._insert_pfc_buffer_egress_configuration(
                        sysname=sysname,
                        switch_sn=switch_sn,
                        port=config.get("port", []),
                        queue=config.get("queue", []),
                        shared_ratio=config.get("shared_ratio"),
                        threshold=config.get("threshold"),
                        is_all_ports=config.get("is_all_ports", False),
                        is_all_queues=config.get("is_all_queues", False),
                        config_data=config.get("config_data")
                    )

    def update_pfc_buffer_config_data(self, config_id, traffic_type, config_data):
        """
        Service method to update PFC Buffer configuration data field after deployment
        Args:
            config_id: int, Configuration ID
            traffic_type: str, "ingress" or "egress"
            config_data: str, JSON string of configuration data
        """
        session = self.get_session()
        
        with session.begin(subtransactions=True):
            if traffic_type == "ingress":
                self._update_pfc_buffer_ingress_configuration(
                    config_id, 
                    {"config_data": config_data}
                )
            else:  # egress
                self._update_pfc_buffer_egress_configuration(
                    config_id,
                    {"config_data": config_data}
                )

    def get_pfc_buffer_configurations_by_switch_sn(self, switch_sn):
        """
        Service method to get PFC Buffer configurations by switch SN
        Args:
            switch_sn: str, Switch serial number
        Returns:
            list: List of PFC Buffer configurations
        """
        session = self.get_session()
        return session.query(PfcBufferIngressConfiguration).filter_by(switch_sn=switch_sn).all() + \
               session.query(PfcBufferEgressConfiguration).filter_by(switch_sn=switch_sn).all()

    def bulk_update_pfc_buffer_configurations(self, switch_sn, configurations):
        """
        Service method to analyze and prepare bulk update operations for PFC Buffer configurations
        Args:
            switch_sn: str, Switch serial number
            configurations: list, List of new configuration data
        Returns:
            dict: Analysis result containing operations to perform
        """
        if not switch_sn:
            raise ValueError("switch_sn is required")
        
        session = self.get_session()
        
        # Validate switch exists using private method
        switch = self._get_switch_by_sn_or_raise(session, switch_sn)
        
        # Get existing configurations for this switch
        existing_ingress_configs = session.query(PfcBufferIngressConfiguration).filter_by(
            switch_sn=switch_sn
        ).all()
        existing_egress_configs = session.query(PfcBufferEgressConfiguration).filter_by(
            switch_sn=switch_sn
        ).all()
        
        # Build lookup maps
        existing_ingress_map = {str(config.id): config for config in existing_ingress_configs}
        existing_egress_map = {str(config.id): config for config in existing_egress_configs}
        
        existing_ingress_ids = set(existing_ingress_map.keys())
        existing_egress_ids = set(existing_egress_map.keys())
        
        # Analyze incoming configurations
        incoming_ingress_ids = set()
        incoming_egress_ids = set()
        configs_to_update = []
        configs_to_create = []
        
        for config in configurations:
            config_id = config.get("config_id")
            traffic_type = config.get("traffic_type", "").lower()
            
            if config_id and str(config_id) != "":
                # Has config_id, check if it exists
                if traffic_type == "ingress" and str(config_id) in existing_ingress_ids:
                    configs_to_update.append(config)
                    incoming_ingress_ids.add(str(config_id))
                elif traffic_type == "egress" and str(config_id) in existing_egress_ids:
                    configs_to_update.append(config)
                    incoming_egress_ids.add(str(config_id))
                else:
                    # config_id exists but not found in existing configs, treat as create
                    configs_to_create.append(config)
            else:
                # No config_id, treat as create
                configs_to_create.append(config)
        
        # Find configurations to delete
        ingress_ids_to_delete = existing_ingress_ids - incoming_ingress_ids
        egress_ids_to_delete = existing_egress_ids - incoming_egress_ids
        
        return {
            "switch": switch,
            "existing_ingress_map": existing_ingress_map,
            "existing_egress_map": existing_egress_map,
            "configs_to_update": configs_to_update,
            "configs_to_create": configs_to_create,
            "ingress_ids_to_delete": list(ingress_ids_to_delete),
            "egress_ids_to_delete": list(egress_ids_to_delete)
        }

    def execute_pfc_buffer_bulk_operations(self, operations_data):
        """
        Service method to execute bulk PFC Buffer operations (delete, update, create)
        Args:
            operations_data: dict, Contains operations to execute
        """
        session = self.get_session()
        
        with session.begin(subtransactions=True):
            # Execute delete operations for ingress
            for config_id in operations_data.get("ingress_ids_to_delete", []):
                session.query(PfcBufferIngressConfiguration).filter_by(id=int(config_id)).delete()
            
            # Execute delete operations for egress
            for config_id in operations_data.get("egress_ids_to_delete", []):
                session.query(PfcBufferEgressConfiguration).filter_by(id=int(config_id)).delete()
            
            # Execute update operations
            for update_data in operations_data.get("update_operations", []):
                config_id = update_data.get("config_id")
                traffic_type = update_data.get("traffic_type")
                updates = update_data.get("updates")
                
                if traffic_type == "ingress":
                    self._update_pfc_buffer_ingress_configuration(int(config_id), updates)
                else:
                    self._update_pfc_buffer_egress_configuration(int(config_id), updates)
            
            # Execute create operations
            for create_data in operations_data.get("create_operations", []):
                traffic_type = create_data.get("traffic_type")
                config_data = create_data.get("config_data")
                
                if traffic_type == "ingress":
                    self._insert_pfc_buffer_ingress_configuration(
                        sysname=config_data.get("sysname"),
                        switch_sn=config_data.get("switch_sn"),
                        port=config_data.get("port", []),
                        queue=config_data.get("queue", []),
                        shared_ratio=config_data.get("shared_ratio"),
                        threshold=config_data.get("threshold"),
                        guaranteed=config_data.get("guaranteed"),
                        reset_offset=config_data.get("reset_offset"),
                        headroom=config_data.get("headroom"),
                        is_all_ports=config_data.get("is_all_ports", False),
                        is_all_queues=config_data.get("is_all_queues", False),
                        config_data=config_data.get("config_data")
                    )
                else:
                    self._insert_pfc_buffer_egress_configuration(
                        sysname=config_data.get("sysname"),
                        switch_sn=config_data.get("switch_sn"),
                        port=config_data.get("port", []),
                        queue=config_data.get("queue", []),
                        shared_ratio=config_data.get("shared_ratio"),
                        threshold=config_data.get("threshold"),
                        is_all_ports=config_data.get("is_all_ports", False),
                        is_all_queues=config_data.get("is_all_queues", False),
                                                 config_data=config_data.get("config_data")
                     )

    def get_pfc_buffer_config_for_deletion(self, config_id, traffic_type):
        """
        Service method to get PFC Buffer configuration and switch for deletion
        Args:
            config_id: int, Configuration ID
            traffic_type: str, "ingress" or "egress"
        Returns:
            dict: Result containing config object and switch object for deployment
        """
        session = self.get_session()
        
        if traffic_type == "ingress":
            config = self._get_config_by_id_or_raise(session, PfcBufferIngressConfiguration, config_id)
        else:
            config = self._get_config_by_id_or_raise(session, PfcBufferEgressConfiguration, config_id)
        
        # Get switch for deployment
        switch = self._get_switch_by_sn_or_raise(session, config.switch_sn)
        
        return {
            "config": config,
            "switch": switch
        }

    def get_pfc_buffer_config_for_update(self, config_id, traffic_type):
        """
        Service method to get PFC Buffer configuration data for update
        Args:
            config_id: int, Configuration ID
            traffic_type: str, "ingress" or "egress"
        Returns:
            dict: Configuration object and old_val for update deployment
        """
        session = self.get_session()
        
        if traffic_type == "ingress":
            existing_config = self._get_config_by_id_or_raise(session, PfcBufferIngressConfiguration, config_id)
        else:
            existing_config = self._get_config_by_id_or_raise(session, PfcBufferEgressConfiguration, config_id)
        
        # Get old configuration data for update
        old_val = {}
        if existing_config.config_data:
            try:
                old_val = json.loads(existing_config.config_data)
            except (json.JSONDecodeError, TypeError):
                old_val = {}
        
        return {
            "existing_config": existing_config,
            "old_val": old_val
        }

    def get_pfc_buffer_configs_list_data(self, request_data):
        """
        Service method to get PFC Buffer configurations list with complex filtering and tree structure
        Args:
            request_data: dict, Request parameters including traffic_type, filters, pagination
        Returns:
            dict: Result containing tree data, pagination info
        """
        session = self.get_session()
        
        # Import required modules
        from sqlalchemy import Text, or_, func, text, asc, desc
        from server.util import utils
        
        # Get parameters with safe access
        traffic_type = request_data.get("traffic_type", "ingress")
        switch_sn_list = request_data.get("switch_sn", [])
        port_list = request_data.get("port", [])
        
        # Select query model based on traffic type
        if traffic_type == "ingress":
            query_model = PfcBufferIngressConfiguration
        else:
            query_model = PfcBufferEgressConfiguration
        
        # Use reusable fabric join query builder
        pre_query, SwitchAlias, AssocAlias, FabricAlias = self._build_fabric_join_query(session, query_model)
        
        # Handle custom sorting with window function
        sort_fields = request_data.get("sortFields", [])
        window_order_by = []
        has_sysname_sort = False
        sysname_order = ""
        
        if sort_fields:
            # Build order_by list for window function
            for field in sort_fields:
                field_name = field.get("field")
                field_order = field.get("order", "asc")
                
                if hasattr(query_model, field_name):
                    column = getattr(query_model, field_name)
                    
                    # Handle JSON field sorting by casting to Text
                    if field_name == "port" or field_name == "queue":
                        column = column.cast(Text)
                    
                    if field_order == "asc":
                        window_order_by.append(asc(column))
                    elif field_order == "desc":
                        window_order_by.append(desc(column))
                    
                    # Check if sysname is in sort fields
                    if field_name == "sysname":
                        has_sysname_sort = True
                        sysname_order = field_order

            pre_query = pre_query.add_columns(
                func.row_number().over(
                    partition_by=query_model.switch_sn,
                    order_by=window_order_by
                ).label('row_num')
            )
        else:
            # Default sorting by id if no custom sort fields provided
            pre_query = pre_query.add_columns(
                func.row_number().over(
                    partition_by=query_model.switch_sn,
                    order_by=query_model.id
                ).label('row_num')
            )
        
        # Apply different ordering based on whether sysname is in sort fields
        if has_sysname_sort:
            # For sysname sorting: maintain switch_sn grouping while sorting by sysname
            if sysname_order == "asc":
                pre_query = pre_query.order_by(query_model.sysname.asc(), query_model.switch_sn, text('row_num'))
            else:
                pre_query = pre_query.order_by(query_model.sysname.desc(), query_model.switch_sn, text('row_num'))
        else:
            # For non-sysname sorting: order by switch_sn, then row_num
            pre_query = pre_query.order_by(query_model.switch_sn, text('row_num'))
        
        # Apply switch_sn filter if provided
        if switch_sn_list:
            pre_query = pre_query.filter(SwitchAlias.sn.in_(switch_sn_list))
        
        # Apply port filter if provided
        if port_list:
            port_conditions = []
            for port in port_list:
                # Cast JSON column to text for pattern matching
                port_conditions.append(
                    query_model.port.cast(Text).ilike(f'%{port}%')
                )
            # Combine conditions with OR
            pre_query = pre_query.filter(or_(*port_conditions))
        
        # # Apply search conditions
        # search_fields = request_data.get("searchFields", {})
        # search_field_list = search_fields.get("fields", [])
        # search_value = search_fields.get("value", "")
        
        # if "port" in search_field_list and search_value:
        #     column = getattr(query_model, "port")
        #     search_condition = column.cast(Text).ilike(f'%{search_value}%')
        #     pre_query = pre_query.filter(search_condition)
        
        # Execute query with pagination using existing utility
        # Skip default sorting to avoid issues with JSON field sorting
        page_num, page_size, total_count, query_result = utils.query_helper(
            query_model,
            pre_query=pre_query,
            data=request_data,
            skip_sort=True  # Skip default sorting to avoid JSON field sorting issues
        )
        
        # Build tree structure grouped by switch_sn
        switch_groups = {}
        current_switch = None
        tree_data = []
        
        for config, switch, assoc, fabric, row_num in query_result:
            config_dict = self._serialize_config_with_fabric(config, switch, fabric)
            
            switch_sn = config_dict.get("switch_sn")
            
            if switch_sn != current_switch:
                # Create new root node for new switch
                current_switch = switch_sn
                config_dict.update({"children": []})
                switch_groups[switch_sn] = config_dict
                tree_data.append(config_dict)
            else:
                config_dict.update({"sysname": ""})
                # Add as child node to existing switch
                switch_groups.get(switch_sn, {}).get("children", []).append(config_dict)
        
        return {
            "tree_data": tree_data,
            "page": page_num,
            "pageSize": page_size,
            "total": total_count
        }

    def delete_pfc_buffer_configuration_and_get_switch(self, config_id, traffic_type):
        """
        Service method to get PFC Buffer configuration for deletion and switch information
        Args:
            config_id: int, Configuration ID
            traffic_type: str, "ingress" or "egress"
        Returns:
            dict: Result containing config data and switch object for deployment
        """
        if not config_id:
            raise ValueError("config_id is required")
        
        if traffic_type not in ["ingress", "egress"]:
            raise ValueError("traffic_type must be 'ingress' or 'egress'")
        
        session = self.get_session()
        
        # Import required models within the method to avoid circular imports
        from server.db.models.inventory import Switch
        
        # Select query model based on traffic type
        if traffic_type == "ingress":
            query_model = PfcBufferIngressConfiguration
        else:
            query_model = PfcBufferEgressConfiguration
        
        # Get configuration by ID
        config = session.query(query_model).filter_by(id=config_id).first()
        
        if not config:
            raise ValueError(f"Configuration with ID {config_id} not found")
        
        # Get switch information for deployment
        switch_sn = config.switch_sn
        switch = session.query(Switch).filter(Switch.sn == switch_sn).first()
        
        if not switch:
            raise ValueError(f"Switch with SN {switch_sn} not found")
        
        # Get old configuration data for deletion deployment
        old_val = {}
        if config.config_data:
            try:
                old_val = json.loads(config.config_data)
            except (json.JSONDecodeError, TypeError):
                old_val = {}
        
        return {
            "config": config,
            "switch": switch,
            "old_val": old_val,
            "query_model": query_model
        }

    def execute_pfc_buffer_config_deletion(self, config_id, traffic_type, updated_pfc_buffer_data=None):
        """
        Service method to execute PFC Buffer configuration deletion from database and update remaining configurations
        Args:
            config_id: int, Configuration ID
            traffic_type: str, "ingress" or "egress"
            updated_pfc_buffer_data: dict, Updated pfc_buffer configuration data to sync to remaining configs
        """
        session = self.get_session()
        
        # Select query model based on traffic type
        if traffic_type == "ingress":
            query_model = PfcBufferIngressConfiguration
        else:
            query_model = PfcBufferEgressConfiguration
        
        
        with session.begin(subtransactions=True):
            # Get the configuration to be deleted to extract switch_sn
            config_to_delete = session.query(query_model).filter_by(id=config_id).first()
            if not config_to_delete:
                raise ValueError(f"Configuration with ID {config_id} not found for deletion")
            
            switch_sn = config_to_delete.switch_sn
            
            # Delete configuration from database
            result = session.query(query_model).filter_by(id=config_id).delete()
            if result == 0:
                raise ValueError(f"Configuration with ID {config_id} not found for deletion")
            
            # Update remaining configurations with new pfc_buffer_data if provided
            if updated_pfc_buffer_data:
                self._update_remaining_pfc_buffer_configs(session, switch_sn, updated_pfc_buffer_data)
                
    def _update_remaining_pfc_buffer_configs(self, session, switch_sn, updated_pfc_buffer_data):
        """
        Private method to update pfc_buffer_data in all remaining ingress and egress configurations for a switch
        Args:
            session: Database session
            switch_sn: str, Switch serial number
            updated_pfc_buffer_data: dict, Updated pfc_buffer configuration data
        """
        # Update all ingress configurations for this switch
        session.query(PfcBufferIngressConfiguration).filter(
            PfcBufferIngressConfiguration.switch_sn == switch_sn
        ).update({"config_data": json.dumps(updated_pfc_buffer_data)})

        session.query(PfcBufferEgressConfiguration).filter(
            PfcBufferEgressConfiguration.switch_sn == switch_sn
        ).update({"config_data": json.dumps(updated_pfc_buffer_data)})

    def get_pfc_buffer_configs_detail_by_switch_sn(self, switch_sn):
        """
        Service method to get PFC Buffer configuration details by switch SN
        Args:
            switch_sn: str, Switch serial number
        Returns:
            dict: Result containing ingress and egress configurations
        """
        if not switch_sn:
            raise ValueError("switch_sn is required")
        
        session = self.get_session()
        
        # Import required models within the method to avoid circular imports
        from server.db.models.inventory import Switch, AssociationFabric, Fabric
        from sqlalchemy.orm import aliased
        
        # Use reusable fabric join query builder for ingress
        ingress_query, SwitchAlias, AssocAlias, FabricAlias = self._build_fabric_join_query(session, PfcBufferIngressConfiguration)
        ingress_query_result = (
            ingress_query
            .filter(PfcBufferIngressConfiguration.switch_sn == switch_sn)
            .order_by(PfcBufferIngressConfiguration.id)
            .all()
        )
        
        # Use reusable fabric join query builder for egress
        egress_query, _, _, _ = self._build_fabric_join_query(session, PfcBufferEgressConfiguration)
        egress_query_result = (
            egress_query
            .filter(PfcBufferEgressConfiguration.switch_sn == switch_sn)
            .order_by(PfcBufferEgressConfiguration.id)
            .all()
        )
        
        # Process Ingress configurations using reusable serialization method
        ingress_configs = []
        for config, switch, assoc, fabric in ingress_query_result:
            config_dict = self._serialize_config_with_fabric(config, switch, fabric)
            config_dict.update({"traffic_type": "ingress"})
            ingress_configs.append(config_dict)
        
        # Process Egress configurations using reusable serialization method
        egress_configs = []
        for config, switch, assoc, fabric in egress_query_result:
            config_dict = self._serialize_config_with_fabric(config, switch, fabric)
            config_dict.update({"traffic_type": "egress"})
            egress_configs.append(config_dict)
        
        return {
            "ingress": ingress_configs,
            "egress": egress_configs
        }
    
    def validate_pfc_wd_configurations(self, configurations):
        """
        Service method to validate PFC Watchdog configurations before deployment
        Args:
            configurations: list, List of PFC Watchdog configuration data
        Returns:
            list: List of validated configuration data
        """
        if not configurations:
            raise ValueError("No configurations provided")
        
        session = self.get_session()
        
        # Import required models within the method to avoid circular imports  
        from server.db.models.inventory import Switch
        
        validated_configs = []
        
        for config in configurations:
            # Validate configuration has no config_id (create operation)
            if config.get("config_id"):
                raise ValueError("Create operation should not include config_id. Use update endpoint for existing configurations.")
            
            switch_sn = config.get("switch_sn")
            if not switch_sn:
                raise ValueError("switch_sn is required")
            
            # Validate switch exists
            switch = session.query(Switch).filter(Switch.sn == switch_sn).first()
            if not switch:
                raise ValueError(f"Switch with SN {switch_sn} not found")
            
            # Build main configuration data for validation
            wd_config = {
                "sysname": config.get("sysname"),
                "switch_sn": config.get("switch_sn"),
                "port": config.get("port", []),
                "queue": config.get("queue", []),
                "enabled": config.get("enabled"),
                "granularity": config.get("granularity", ""),
                "restore_mode": config.get("restore_mode", None),
                "restore_action": config.get("restore_action", None),
                "detection_interval": config.get("detection_interval", ""),
                "restore_interval": config.get("restore_interval", ""),
                "threshold_period": config.get("threshold_period", ""),
                "threshold_count": config.get("threshold_count", ""),
                "is_all_ports": config.get("is_all_ports"),
                "is_all_queues": config.get("is_all_queues")
            }
            
            validated_configs.append(wd_config)
        
        return validated_configs

    def save_pfc_wd_configurations(self, validated_configs, config_data_list):
        """
        Service method to save PFC Watchdog configurations to database
        Args:
            validated_configs: list, List of validated configuration data
            config_data_list: list, List of configuration data for storage
        Returns:
            list: List of created configuration IDs
        """
        import json
        session = self.get_session()
        created_ids = []
        
        with session.begin(subtransactions=True):
            for i, wd_config in enumerate(validated_configs):
                # Create new configuration in database
                config_id = self._insert_pfc_wd_configuration(**wd_config)
                
                # Update config_data field with deployment result
                config_data = config_data_list[i] if i < len(config_data_list) else None
                if config_data:
                    self._update_pfc_wd_configuration(
                        config_id,
                        {"config_data": json.dumps(config_data.get("new_val"))}
                    )
                
                created_ids.append(config_id)
        
        return created_ids

    def bulk_update_pfc_wd_configurations(self, switch_sn, configurations):
        """
        Service method to handle bulk update of PFC Watchdog configurations for a switch
        Args:
            switch_sn: str, Switch serial number
            configurations: list, List of configuration data
        Returns:
            dict: Operations data for deployment
        """
        if not switch_sn:
            raise ValueError("switch_sn is required")
        
        session = self.get_session()
        
        # Validate switch exists using private method
        switch = self._get_switch_by_sn_or_raise(session, switch_sn)
        
        # Get existing configurations for this switch
        existing_configs = session.query(PfcWdConfiguration).filter_by(
            switch_sn=switch_sn
        ).all()
        existing_config_ids = {str(config.id) for config in existing_configs}
        
        # Classify operations
        incoming_config_ids = set()
        configs_to_update = []
        configs_to_create = []
        
        for config in configurations:
            config_id = config.get("config_id")
            if config_id and str(config_id) != "" and str(config_id) in existing_config_ids:
                # Has config_id and exists, add to update list
                configs_to_update.append(config)
                incoming_config_ids.add(str(config_id))
            else:
                # No config_id or doesn't exist, add to create list
                configs_to_create.append(config)
        
        # Find configurations to delete (existing but not in incoming)
        config_ids_to_delete = existing_config_ids - incoming_config_ids
        
        # Prepare operations data
        operations_data = {
            "switch": switch,
            "delete_ids": list(config_ids_to_delete),
            "update_configs": configs_to_update,
            "create_configs": configs_to_create,
            "existing_configs": {str(config.id): config for config in existing_configs}
        }
        
        return operations_data

    def update_pfc_wd_configurations(self, switch_sn, configurations):
        """
        Update PFC Watchdog configurations - simplified version
        Args:
            switch_sn: str, Switch serial number
            configurations: list, New configuration list from API
        """
        session = self.get_session()
        
        # Get existing configurations
        existing_configs = session.query(PfcWdConfiguration).filter_by(switch_sn=switch_sn).all()
        existing_map = {config.id: config for config in existing_configs}
        
        with session.begin(subtransactions=True):
            updated_ids = set()
            
            # Update/Create operations
            for config in configurations:
                config_id = config.get("config_id")
                
                if config_id and int(config_id) in existing_map:
                    # Update existing configuration
                    updated_ids.add(int(config_id))
                    update_data = {
                        "sysname": config.get("sysname"),
                        "switch_sn": switch_sn,
                        "port": config.get("port"),
                        "queue": config.get("queue"),
                        "enabled": config.get("enabled"),
                        "granularity": config.get("granularity"),
                        "detection_interval": config.get("detection_interval"),
                        "restore_interval": config.get("restore_interval"),
                        "threshold_period": config.get("threshold_period"),
                        "threshold_count": config.get("threshold_count"),
                        "restore_mode": config.get("restore_mode"),
                        "restore_action": config.get("restore_action"),
                        "is_all_ports": config.get("is_all_ports", False),
                        "is_all_queues": config.get("is_all_queues", False),
                        "config_data": config.get("config_data")
                    }
                    self._update_pfc_wd_configuration(int(config_id), update_data)
                else:
                    # Create new configuration
                    db_config = {
                        "sysname": config.get("sysname"),
                        "switch_sn": switch_sn,
                        "port": config.get("port"),
                        "queue": config.get("queue"),
                        "enabled": config.get("enabled"),
                        "granularity": config.get("granularity"),
                        "detection_interval": config.get("detection_interval"),
                        "restore_interval": config.get("restore_interval"),
                        "threshold_period": config.get("threshold_period"),
                        "threshold_count": config.get("threshold_count"),
                        "restore_mode": config.get("restore_mode"),
                        "restore_action": config.get("restore_action"),
                        "is_all_ports": config.get("is_all_ports", False),
                        "is_all_queues": config.get("is_all_queues", False),
                        "config_data": config.get("config_data")
                    }
                    self._insert_pfc_wd_configuration(**db_config)
            
            # Delete configurations that are not in the update list
            for existing_id in existing_map.keys():
                if existing_id not in updated_ids:
                    self._delete_pfc_wd_configuration(session, existing_id)

    def get_pfc_wd_configurations_by_switch_sn(self, switch_sn):
        """
        Get PFC Watchdog configurations by switch serial number - simplified version
        Args:
            switch_sn: str, Switch serial number
        Returns:
            list: List of PFC Watchdog configurations
        """
        session = self.get_session()
        return session.query(PfcWdConfiguration).filter_by(switch_sn=switch_sn).all()

    def execute_pfc_wd_bulk_operations(self, operations_data):
        """
        Service method to execute PFC Watchdog bulk operations after successful deployment
        Args:
            operations_data: dict, Operations data including all changes
        """
        import json
        session = self.get_session()
        
        with session.begin(subtransactions=True):
            # 1. Handle delete operations
            if operations_data.get("delete_ids"):
                session.query(PfcWdConfiguration).filter(
                    PfcWdConfiguration.id.in_(operations_data["delete_ids"])
                ).delete(synchronize_session=False)
            
            # 2. Handle update operations
            for config in operations_data.get("update_configs", []):
                config_id = config.get("config_id")
                if config_id:
                    # Build update data
                    wd_config = {
                        "sysname": config.get("sysname"),
                        "switch_sn": config.get("switch_sn"),
                        "port": config.get("port", []),
                        "queue": config.get("queue", []),
                        "enabled": config.get("enabled"),
                        "granularity": config.get("granularity", ""),
                        "restore_mode": config.get("restore_mode", None),
                        "restore_action": config.get("restore_action", None),
                        "detection_interval": config.get("detection_interval", ""),
                        "restore_interval": config.get("restore_interval", ""),
                        "threshold_period": config.get("threshold_period", ""),
                        "threshold_count": config.get("threshold_count", ""),
                        "is_all_ports": config.get("is_all_ports"),
                        "is_all_queues": config.get("is_all_queues"),
                        "config_data": config.get("config_data")  # Include deployed config data
                    }
                    
                    # Update configuration
                    self._update_pfc_wd_configuration(config_id, wd_config)
            
            # 3. Handle create operations
            for config in operations_data.get("create_configs", []):
                # Build create data
                wd_config = {
                    "sysname": config.get("sysname"),
                    "switch_sn": config.get("switch_sn"),
                    "port": config.get("port", []),
                    "queue": config.get("queue", []),
                    "enabled": config.get("enabled"),
                    "granularity": config.get("granularity", ""),
                    "restore_mode": config.get("restore_mode", None),
                    "restore_action": config.get("restore_action", None),
                    "detection_interval": config.get("detection_interval", ""),
                    "restore_interval": config.get("restore_interval", ""),
                    "threshold_period": config.get("threshold_period", ""),
                    "threshold_count": config.get("threshold_count", ""),
                    "is_all_ports": config.get("is_all_ports"),
                    "is_all_queues": config.get("is_all_queues"),
                    "config_data": config.get("config_data")  # Include deployed config data
                }
                
                # Create new configuration
                self._insert_pfc_wd_configuration(**wd_config)

    def get_pfc_wd_configs_list_data(self, request_data):
        """
        Service method to get PFC Watchdog configurations list with tree structure
        Args:
            request_data: dict, Request data containing search and pagination parameters
        Returns:
            dict: Paginated and organized configuration data
        """
        from server.db.models.inventory import Switch, AssociationFabric, Fabric
        from sqlalchemy.orm import aliased
        from sqlalchemy import Text, or_, func, text, asc, desc
        from server.util import utils
        
        session = self.get_session()
        
        # Use reusable fabric join query builder
        pre_query, SwitchAlias, AssocAlias, FabricAlias = self._build_fabric_join_query(session, PfcWdConfiguration)
        
        # Handle custom sorting with window function
        sort_fields = request_data.get("sortFields", [])
        window_order_by = []
        has_sysname_sort = False
        sysname_order = ""
        
        if sort_fields:
            # Build order_by list for window function
            for field in sort_fields:
                field_name = field.get("field")
                field_order = field.get("order", "asc")
                
                if hasattr(PfcWdConfiguration, field_name):
                    column = getattr(PfcWdConfiguration, field_name)
                    
                    # Handle JSON field sorting by casting to Text
                    if field_name == "port" or field_name == "queue":
                        column = column.cast(Text)
                    
                    if field_order == "asc":
                        window_order_by.append(asc(column))
                    elif field_order == "desc":
                        window_order_by.append(desc(column))
                    
                    # Check if sysname is in sort fields
                    if field_name == "sysname":
                        has_sysname_sort = True
                        sysname_order = field_order

            pre_query = pre_query.add_columns(
                func.row_number().over(
                    partition_by=PfcWdConfiguration.switch_sn,
                    order_by=window_order_by
                ).label('row_num')
            )
        else:
            # Default sorting by id if no custom sort fields provided
            pre_query = pre_query.add_columns(
                func.row_number().over(
                    partition_by=PfcWdConfiguration.switch_sn,
                    order_by=PfcWdConfiguration.id
                ).label('row_num')
            )
        
        # Apply different ordering based on whether sysname is in sort fields
        if has_sysname_sort:
            # For sysname sorting: maintain switch_sn grouping while sorting by sysname
            if sysname_order == "asc":
                pre_query = pre_query.order_by(PfcWdConfiguration.sysname.asc(), PfcWdConfiguration.switch_sn, text('row_num'))
            else:
                pre_query = pre_query.order_by(PfcWdConfiguration.sysname.desc(), PfcWdConfiguration.switch_sn, text('row_num'))
        else:
            # For non-sysname sorting: order by switch_sn, then row_num
            pre_query = pre_query.order_by(PfcWdConfiguration.switch_sn, text('row_num'))
        
        # Apply search conditions
        # search_conditions = []
        # searchFields = request_data.get("searchFields", {}).get("fields", [])
        # search_value = request_data.get("searchFields", {}).get("value", "")
        # if "port" in searchFields:
        #     column = getattr(PfcWdConfiguration, "port")
        #     search_conditions.append(column.cast(Text).ilike(f'%{search_value}%'))
        
        # if search_conditions:
        #     pre_query = pre_query.filter(or_(*search_conditions))
        
        # Get paginated results
        # Skip default sorting to avoid issues with JSON field sorting
        page_num, page_size, total_count, query_result = utils.query_helper(
            PfcWdConfiguration,
            pre_query=pre_query,
            data=request_data,
            skip_sort=True  # Skip default sorting to avoid JSON field sorting issues
        )
        
        # Build tree structure
        tree_data = []
        current_switch = None
        
        for config, switch, assoc, fabric, row_num in query_result:
            # Use reusable serialization method
            config_dict = self._serialize_config_with_fabric(config, switch, fabric)
            
            # Group by switch_sn for tree structure
            if not current_switch or current_switch["switch_sn"] != config.switch_sn:
                current_switch = config_dict.copy()
                current_switch["children"] = []
                tree_data.append(current_switch)
            else:
                config_dict.update({"sysname": ""})
                current_switch["children"].append(config_dict)
        
        return {
            "tree_data": tree_data,
            "page": page_num,
            "pageSize": page_size,
            "total": total_count
        }

    def get_pfc_wd_configs_detail_by_switch_sn(self, switch_sn):
        """
        Service method to get PFC Watchdog configuration details by switch SN
        Args:
            switch_sn: str, Switch serial number
        Returns:
            list: List of PFC Watchdog configurations with related fabric information
        """
        if not switch_sn:
            raise ValueError("switch_sn is required")
        
        session = self.get_session()
        
        # Use reusable fabric join query builder
        query, SwitchAlias, AssocAlias, FabricAlias = self._build_fabric_join_query(session, PfcWdConfiguration)
        query_result = (
            query
            .filter(PfcWdConfiguration.switch_sn == switch_sn)
            .order_by(PfcWdConfiguration.id)
            .all()
        )
        
        # Process configurations using reusable serialization method
        configs = []
        for config, switch, assoc, fabric in query_result:
            config_dict = self._serialize_config_with_fabric(config, switch, fabric)
            configs.append(config_dict)
        
        return configs

    def get_pfc_wd_configuration_for_deletion(self, config_id):
        """
        Service method to get PFC Watchdog configuration and switch information for deletion
        Args:
            config_id: int, Configuration ID
        Returns:
            dict: Result containing config data and switch object for deployment
        Raises:
            ValueError: If configuration or switch not found
        """
        if not config_id:
            raise ValueError("config_id is required")
        
        session = self.get_session()
        from server.db.models.inventory import Switch
        
        # Get PFC Watchdog configuration by ID using reusable method
        config = self._get_config_by_id_or_raise(session, PfcWdConfiguration, config_id)
        
        # Get switch for deployment using reusable method  
        switch = self._get_switch_by_sn_or_raise(session, config.switch_sn)
        
        return {
            "config": config,
            "switch": switch
        }

    def delete_pfc_wd_configuration(self, config_id, switch_sn, updated_pfc_wd_config_data=None):
        """
        Service method to delete PFC Watchdog configuration from database
        Args:
            config_id: int, Configuration ID
            switch_sn: Switch SN
            updated_pfc_wd_config_data: dict, Updated PFC Watchdog configuration data
        Returns:
            bool: Whether deletion successful
        Raises:
            ValueError: If configuration not found
        """
        if not config_id:
            raise ValueError("config_id is required")
        
        session = self.get_session()
        with session.begin(subtransactions=True):
            
            # Use existing private method for deletion
            self._delete_pfc_wd_configuration(session, config_id)
            
            if updated_pfc_wd_config_data:
                self._update_remaining_pfc_wd_configs(session, switch_sn, updated_pfc_wd_config_data)

    def _update_remaining_pfc_wd_configs(self, session, switch_sn, updated_pfc_wd_config_data):

        """
        Private method: Update remaining PFC Watchdog configurations
        Args:
            session: Database session
            switch_sn: Switch SN
            updated_pfc_wd_config_data: dict, Updated PFC Watchdog configuration data
        """
        session.query(PfcWdConfiguration).filter(
            PfcWdConfiguration.switch_sn == switch_sn
        ).update({"config_data": json.dumps(updated_pfc_wd_config_data)})

    def add_roce_easy_deploy_configuration(self, config):
        """
        Add RoCE easy deploy configuration
        Args:
            config: dict, RoCE easy deploy configuration information
                {
                    'sysname': str,
                    'port': list,  # Port list
                    'queue': list,  # Queue list
                    'enabled': bool,
                    'mode': str  # 'lossy' or 'lossless'
                }
        Returns:
            int: New configuration ID
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            configuration = RoceEasyDeployConfiguration(
                sysname=config.get('sysname'),
                switch_sn=config.get('switch_sn'),
                port=config.get('port'),
                queue=config.get('queue'),
                enabled=config.get('enabled', False),
                mode=config.get('mode', 'lossless'),
                config_data=config.get('config_data')
            )
            session.add(configuration)
            session.flush()
            return configuration.id

    def update_roce_easy_deploy_configuration(self, config_id, config):
        """
        Update RoCE easy deploy configuration
        Args:
            config_id: Configuration ID
            config: dict, RoCE easy deploy configuration information
                {
                    'sysname': str,      # optional
                    'port': list,        # optional
                    'queue': list,       # optional
                    'enabled': bool,     # optional
                    'mode': str          # optional
                }
        Returns:
            bool: Whether update successful
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            result = session.query(RoceEasyDeployConfiguration).filter_by(id=config_id).update(config)
            return result > 0

    def delete_roce_easy_deploy_configuration(self, config_id):
        """
        Delete RoCE easy deploy configuration
        Args:
            config_id: Configuration ID
        Returns:
            bool: Whether delete successful
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            result = session.query(RoceEasyDeployConfiguration).filter_by(id=config_id).delete()
            return result > 0
        
    def get_roce_easy_deploy_configuration_by_sysname(self, sysname):
        """
        Get RoCE easy deploy configuration by sysname
        Args:
            sysname: Switch name
        Returns:
            RoceEasyDeployConfiguration or None: Configuration object if found, None otherwise
        """
        session = self.get_session()
        return session.query(RoceEasyDeployConfiguration).filter_by(
            sysname=sysname
        ).first()
    
    def add_dlb_configuration(self, config):
        """
        添加 DLB 配置
        Args:
            config: dict, DLB 配置信息
                {
                    'sysname': str,      # 交换机名称
                    'switch_sn': str,    # 交换机SN，新增
                    'dlb_enabled': bool, # 是否启用
                    'mode': str,        # 模式 ('dlb-normal', 'dlb-optimal', 'dlb-assigned')
                    'config_data': str   # 新增字段
                }
        Returns:
            int: 新配置的ID
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            configuration = DLBConfiguration(
                sysname=config.get('sysname'),
                switch_sn=config.get('switch_sn'),  # 新增字段
                dlb_enabled=config.get('dlb_enabled', False),
                mode=config.get('mode', 'dlb-normal'),
                config_data=config.get('config_data')  # 新增字段
            )
            session.add(configuration)
            session.flush()
            return configuration.id

    def update_dlb_configuration(self, config_id, config):
        """
        更新 DLB 配置
        Args:
            config_id: int, 配置ID
            config: dict, DLB 配置信息
                {
                    'sysname': str,      # optional
                    'switch_sn': str,    # optional, 新增
                    'dlb_enabled': bool, # optional
                    'mode': str,        # optional
                    'config_data': str   # optional
                }
        Returns:
            bool: 是否更新成功
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            result = session.query(DLBConfiguration).filter_by(id=config_id).update(config)
            return result > 0

    def delete_dlb_configuration(self, config_id):
        """
        删除 DLB 配置
        Args:
            config_id: int, 配置ID
        Returns:
            bool: 是否删除成功
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            result = session.query(DLBConfiguration).filter_by(id=config_id).delete()
            return result > 0

    def get_dlb_configuration_by_sn(self, sn):
        session = self.get_session()
        return session.query(DLBConfiguration).filter_by(
            switch_sn=sn
        ).first()

    def _insert_pfc_configuration(self, sysname, switch_sn, profile_name, port, queue, enabled, 
                                is_all_ports=False, is_all_queues=False, config_data=None):
        """
        Private method: Insert PFC configuration record
        Args:
            sysname: Switch name
            switch_sn: Switch SN
            profile_name: Profile name
            port: Port list in JSON format
            queue: Queue list in JSON format
            enabled: Whether enabled
            is_all_ports: Whether to apply to all ports
            is_all_queues: Whether to apply to all queues
            config_data: Configuration data in JSON format
        Returns:
            int: New configuration ID
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            pfc_configuration = PfcConfiguration(
                sysname=sysname,
                switch_sn=switch_sn,
                profile_name=profile_name,
                port=port,
                queue=queue,
                enabled=enabled,
                is_all_ports=is_all_ports,
                is_all_queues=is_all_queues,
                config_data=config_data
            )
            session.add(pfc_configuration)
            session.flush()
            return pfc_configuration.id

    def add_pfc_configuration(self, pfc_config):
        """
        Business method: Add PFC configuration
        Args:
            pfc_config: dict, PFC configuration information
                {
                    'sysname': str,
                    'switch_sn': str,
                    'profile_name': str,
                    'port': list,  # Port list
                    'queue': list,  # Queue list
                    'enabled': bool,
                    'is_all_ports': bool,  # 新增参数
                    'is_all_queues': bool,  # 新增参数
                    'config_data': str
                }
        Returns:
            int: New configuration ID
        """
        return self._insert_pfc_configuration(
            sysname=pfc_config.get('sysname'),
            switch_sn=pfc_config.get('switch_sn'),
            profile_name=pfc_config.get('profile_name'),
            port=pfc_config.get('port'),
            queue=pfc_config.get('queue'),
            enabled=pfc_config.get('enabled'),
            is_all_ports=pfc_config.get('is_all_ports', False),  # 新增参数
            is_all_queues=pfc_config.get('is_all_queues', False),  # 新增参数
            config_data=pfc_config.get('config_data')
        )

    def _update_pfc_configuration(self, config_id, updates):
        """
        Private method: Update PFC configuration record
        Args:
            config_id: PFC configuration ID
            updates: dict, Attributes to update
                {
                    'sysname': str,          # optional
                    'switch_sn': str,        # optional
                    'profile_name': str,     # optional
                    'port': list,            # optional
                    'queue': list,           # optional
                    'enabled': bool,         # optional
                    'config_data': str       # optional
                }
        Returns:
            bool: Whether update successful
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            result = session.query(PfcConfiguration).filter_by(id=config_id).update(updates)
            return result > 0

    def update_pfc_configuration(self, config_id, pfc_config):
        """
        Business method: Update PFC configuration
        Args:
            config_id: PFC configuration ID
            pfc_config: dict, PFC configuration information
                {
                    'sysname': str,
                    'switch_sn': str,
                    'profile_name': str,
                    'port': list,
                    'queue': list,
                    'enabled': bool
                }
        Returns:
            bool: Whether update successful
        """
        return self._update_pfc_configuration(config_id, pfc_config)

    def _delete_pfc_configuration(self, session, config_id):
        """
        Private method: Delete PFC configuration record
        Args:
            config_id: PFC configuration ID
        """
        session.query(PfcConfiguration).filter_by(id=config_id).delete()

    def _update_remaining_pfc_configs(self, session, switch_sn, updated_pfc_config_data):
        """
        Private method: Update remaining PFC configurations
        Args:
            session: Database session
            switch_sn: Switch SN
            updated_pfc_config_data: dict, Updated PFC configuration data
        """
        session.query(PfcConfiguration).filter(
            PfcConfiguration.switch_sn == switch_sn
        ).update({"config_data": json.dumps(updated_pfc_config_data)})
            
    def delete_pfc_configuration(self, config_id, switch_sn, updated_pfc_config_data=None):
        """
        Business method: Delete PFC configuration
        Args:
            config_id: PFC configuration ID
            switch_sn: Switch SN
            updated_pfc_config_data: dict, Updated PFC configuration data
        Returns:
            bool: Whether delete successful
        """
        session = self.get_session()
        with session.begin(subtransactions=True):

            self._delete_pfc_configuration(session, config_id)

            if updated_pfc_config_data:
                self._update_remaining_pfc_configs(session, switch_sn, updated_pfc_config_data)

    def _insert_pfc_buffer_ingress_configuration(self, sysname, switch_sn, port, queue, shared_ratio, threshold, 
                                                guaranteed, reset_offset, headroom, is_all_ports=False, 
                                                is_all_queues=False, config_data=None):
        """
        Insert PFC buffer ingress configuration
        Args:
            sysname: str, switch system name
            switch_sn: str, switch serial number
            port: list, port list
            queue: list, queue list
            shared_ratio: str, shared ratio
            threshold: int, threshold value
            guaranteed: int, guaranteed value
            reset_offset: int, reset offset value
            headroom: int, headroom value
            is_all_ports: bool, whether to apply to all ports
            is_all_queues: bool, whether to apply to all queues
            config_data: str, configuration data
        Returns:
            int: New configuration ID
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            configuration = PfcBufferIngressConfiguration(
                sysname=sysname,
                switch_sn=switch_sn,
                port=port,
                queue=queue,
                shared_ratio=shared_ratio,
                threshold=threshold,
                guaranteed=guaranteed,
                reset_offset=reset_offset,
                headroom=headroom,
                is_all_ports=is_all_ports,
                is_all_queues=is_all_queues,
                config_data=config_data
            )
            session.add(configuration)
            session.flush()
            return configuration.id

    def _insert_pfc_buffer_egress_configuration(self, sysname, switch_sn, port, queue, shared_ratio, threshold, 
                                              is_all_ports=False, is_all_queues=False, config_data=None):
        """
        Insert PFC buffer egress configuration
        Args:
            sysname: str, switch system name
            switch_sn: str, switch serial number
            port: list, port list
            queue: list, queue list
            shared_ratio: str, shared ratio
            threshold: int, threshold value
            is_all_ports: bool, whether to apply to all ports
            is_all_queues: bool, whether to apply to all queues
            config_data: str, configuration data
        Returns:
            int: New configuration ID
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            configuration = PfcBufferEgressConfiguration(
                sysname=sysname,
                switch_sn=switch_sn,
                port=port,
                queue=queue,
                shared_ratio=shared_ratio,
                threshold=threshold,
                is_all_ports=is_all_ports,
                is_all_queues=is_all_queues,
                config_data=config_data
            )
            session.add(configuration)
            session.flush()
            return configuration.id

    # def add_pfc_buffer_configuration(self, buffer_config, ingress_configs, egress_configs):
    #     """
    #     添加 PFC Buffer 配置
    #     Args:
    #         buffer_config: dict, 主配置信息
    #             {
    #                 'sysname': str,  # 交换机名称
    #                 'switch_sn': str,  # 交换机SN
    #                 'config_data': str  # 配置数据
    #             }
    #         ingress_configs: list, 入站配置列表
    #         egress_configs: list, 出站配置列表
    #     Returns:
    #         tuple: (ingress_ids, egress_ids)
    #     """
    #     session = self.get_session()
    #     try:
    #         with session.begin():
    #             # 创建入站配置
    #             ingress_ids = []
    #             for ingress in ingress_configs:
    #                 ingress_id = self._insert_pfc_buffer_ingress_configuration(
    #                     sysname=buffer_config.get('sysname', ''),
    #                     switch_sn=buffer_config.get('switch_sn', ''),
    #                     port=json.dumps(ingress.get('port', [])),
    #                     queue=json.dumps(ingress.get('queue', [])),
    #                     shared_ratio=ingress.get('shared_ratio'),
    #                     threshold=ingress.get('threshold'),
    #                     guaranteed=ingress.get('guaranteed'),
    #                     reset_offset=ingress.get('reset_offset'),
    #                     headroom=ingress.get('headroom'),
    #                     is_all_ports=ingress.get('is_all_ports', False),
    #                     is_all_queues=ingress.get('is_all_queues', False),
    #                     config_data=buffer_config.get('config_data')
    #                 )
    #                 ingress_ids.append(ingress_id)
                
    #             # 创建出站配置
    #             egress_ids = []
    #             for egress in egress_configs:
    #                 egress_id = self._insert_pfc_buffer_egress_configuration(
    #                     sysname=buffer_config.get('sysname', ''),
    #                     switch_sn=buffer_config.get('switch_sn', ''),
    #                     port=json.dumps(egress.get('port', [])),
    #                     queue=json.dumps(egress.get('queue', [])),
    #                     shared_ratio=egress.get('shared_ratio'),
    #                     threshold=egress.get('threshold'),
    #                     is_all_ports=egress.get('is_all_ports', False),
    #                     is_all_queues=egress.get('is_all_queues', False),
    #                     config_data=buffer_config.get('config_data')
    #                 )
    #                 egress_ids.append(egress_id)
                
    #             return ingress_ids, egress_ids
    #     except Exception as e:
    #         session.rollback()
    #         raise e

    def _update_pfc_buffer_ingress_configuration(self, config_id, updates):
        """
        Update PFC buffer ingress configuration
        Args:
            config_id: int, configuration ID
            updates: dict, update fields
                {
                    'port': list,
                    'queue': list,
                    'shared_ratio': str,
                    'threshold': int,
                    'guaranteed': int,
                    'reset_offset': int,
                    'headroom': int,
                    'is_all_ports': bool,
                    'is_all_queues': bool,
                    'config_data': str
                }
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            session.query(PfcBufferIngressConfiguration).filter(
                PfcBufferIngressConfiguration.id == config_id
            ).update(updates)

    def _update_pfc_buffer_egress_configuration(self, config_id, updates):
        """
        Update PFC buffer egress configuration
        Args:
            config_id: int, configuration ID
            updates: dict, update fields
                {
                    'port': list,
                    'queue': list,
                    'shared_ratio': str,
                    'threshold': int,
                    'is_all_ports': bool,
                    'is_all_queues': bool,
                    'config_data': str
                }
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            session.query(PfcBufferEgressConfiguration).filter(
                PfcBufferEgressConfiguration.id == config_id
            ).update(updates)

    # def update_pfc_buffer_configuration(self, sysname, switch_sn, buffer_config, ingress_configs, egress_configs):
    #     """
    #     更新 PFC Buffer 配置
    #     Args:
    #         sysname: str, 交换机名称
    #         switch_sn: str, 交换机SN
    #         buffer_config: dict, 主配置信息
    #             {
    #                 'config_data': str  # 配置数据
    #             }
    #         ingress_configs: list, 入站配置列表
    #         egress_configs: list, 出站配置列表
    #     Returns:
    #         bool: 是否更新成功
    #     """
    #     session = self.get_session()
    #     try:
    #         with session.begin():
    #             # 删除旧的入站配置
    #             session.query(PfcBufferIngressConfiguration).filter(
    #                 and_(
    #                     PfcBufferIngressConfiguration.sysname == sysname,
    #                     PfcBufferIngressConfiguration.switch_sn == switch_sn
    #                 )
    #             ).delete()

    #             # 插入新的入站配置
    #             for ingress in ingress_configs:
    #                 self._insert_pfc_buffer_ingress_configuration(
    #                     sysname=sysname,
    #                     switch_sn=switch_sn,
    #                     port=json.dumps(ingress.get('port', [])),
    #                     queue=json.dumps(ingress.get('queue', [])),
    #                     shared_ratio=ingress.get('shared_ratio'),
    #                     threshold=ingress.get('threshold'),
    #                     guaranteed=ingress.get('guaranteed'),
    #                     reset_offset=ingress.get('reset_offset'),
    #                     headroom=ingress.get('headroom'),
    #                     is_all_ports=ingress.get('is_all_ports', False),
    #                     is_all_queues=ingress.get('is_all_queues', False),
    #                     config_data=buffer_config.get('config_data')
    #                 )

    #             # 删除旧的出站配置
    #             session.query(PfcBufferEgressConfiguration).filter(
    #                 and_(
    #                     PfcBufferEgressConfiguration.sysname == sysname,
    #                     PfcBufferEgressConfiguration.switch_sn == switch_sn
    #                 )
    #             ).delete()

    #             # 插入新的出站配置
    #             for egress in egress_configs:
    #                 self._insert_pfc_buffer_egress_configuration(
    #                     sysname=sysname,
    #                     switch_sn=switch_sn,
    #                     port=json.dumps(egress.get('port', [])),
    #                     queue=json.dumps(egress.get('queue', [])),
    #                     shared_ratio=egress.get('shared_ratio'),
    #                     threshold=egress.get('threshold'),
    #                     is_all_ports=egress.get('is_all_ports', False),
    #                     is_all_queues=egress.get('is_all_queues', False),
    #                     config_data=buffer_config.get('config_data')
    #                 )

    #             return True
    #     except Exception as e:
    #         session.rollback()
    #         raise e

    def delete_pfc_buffer_configuration(self, sysname, switch_sn):
        """
        Business method: Delete PFC buffer configuration by sysname and switch_sn
        Args:
            sysname: str, Switch name
            switch_sn: str, Switch SN
        Returns:
            bool: Whether delete successful
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            # 删除所有相关的入站配置记录
            ingress_result = session.query(PfcBufferIngressConfiguration).filter(
                and_(
                    PfcBufferIngressConfiguration.sysname == sysname,
                    PfcBufferIngressConfiguration.switch_sn == switch_sn
                )
            ).delete()
            
            # 删除所有相关的出站配置记录
            egress_result = session.query(PfcBufferEgressConfiguration).filter(
                and_(
                    PfcBufferEgressConfiguration.sysname == sysname,
                    PfcBufferEgressConfiguration.switch_sn == switch_sn
                )
            ).delete()
            
            return (ingress_result + egress_result) > 0

    def _insert_pfc_wd_configuration(self, sysname, switch_sn, port, queue, enabled, granularity, 
                                   detection_interval, restore_interval, threshold_period, 
                                   threshold_count, restore_mode, restore_action, 
                                   is_all_ports=False, is_all_queues=False, config_data=None):
        """
        Private method: Insert PFC watchdog configuration record
        Args:
            sysname: str, Switch name
            switch_sn: str, Switch SN
            port: JSON, Port list
            queue: JSON, Queue list
            enabled: bool, Whether watchdog is enabled
            granularity: int, Granularity value
            detection_interval: int, Detection interval
            restore_interval: int, Restore interval
            threshold_period: int, Threshold period
            threshold_count: int, Threshold count
            restore_mode: str, Restore mode ('immediate' or 'delayed')
            restore_action: str, Restore action ('clear' or 'discard')
            is_all_ports: bool, Whether to apply to all ports
            is_all_queues: bool, Whether to apply to all queues
            config_data: str, Configuration data
        Returns:
            int: New configuration ID
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            wd_configuration = PfcWdConfiguration(
                sysname=sysname,
                switch_sn=switch_sn,
                port=port,
                queue=queue,
                enabled=enabled,
                granularity=granularity,
                detection_interval=detection_interval,
                restore_interval=restore_interval,
                threshold_period=threshold_period,
                threshold_count=threshold_count,
                restore_mode=restore_mode,
                restore_action=restore_action,
                is_all_ports=is_all_ports,
                is_all_queues=is_all_queues,
                config_data=config_data
            )
            session.add(wd_configuration)
            session.flush()
            return wd_configuration.id

    def _update_pfc_wd_configuration(self, config_id, updates):
        """
        Private method: Update PFC watchdog configuration record
        Args:
            config_id: int, Configuration ID
            updates: dict, Attributes to update
                {
                    'sysname': str,          # optional
                    'switch_sn': str,        # optional
                    'port': JSON,            # optional
                    'queue': JSON,           # optional
                    'enabled': bool,         # optional
                    'granularity': int,      # optional
                    'detection_interval': int,# optional
                    'restore_interval': int,  # optional
                    'threshold_period': int,  # optional
                    'threshold_count': int,   # optional
                    'restore_mode': str,      # optional
                    'restore_action': str,    # optional
                    'is_all_ports': bool,    # optional
                    'is_all_queues': bool,   # optional
                    'config_data': str       # optional
                }
        Returns:
            bool: Whether update successful
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            result = session.query(PfcWdConfiguration).filter_by(id=config_id).update(updates)
            return result > 0

    def _delete_pfc_wd_configuration(self, session, config_id):
        """
        Private method: Delete PFC watchdog configuration record
        Args:
            config_id: int, Configuration ID
        Returns:
            bool: Whether delete successful
        """
        session.query(PfcWdConfiguration).filter_by(id=config_id).delete()

    def add_ecn_configuration(self, sysname, switch_sn, enabled, mode):
        session = self.get_session()
        with session.begin(subtransactions=True):
            ecn_configuration = EcnConfiguration(
                sysname=sysname,
                switch_sn=switch_sn,
                enabled=enabled,
                mode=mode
            )
            session.add(ecn_configuration)
            session.flush()
            return ecn_configuration.id

    def add_ecn_configuration_detail(self, ecn_config_id, detail_config):
        """
        Add new detail configuration for existing ECN configuration
        Args:
            ecn_config_id: int, ECN main configuration ID
            detail_config: dict, 详细配置数据
                {
                    'sysname': str,
                    'switch_sn': str,
                    'port': list,
                    'queue': list,
                    'max_threshold': int,
                    'min_threshold': int,
                    'drop_probability': int,
                    'ecn_threshold': int,
                    'wred_enable': bool,
                    'is_all_ports': bool,
                    'is_all_queues': bool
                }
        Returns:
            int: 新创建的详细配置ID
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            # 验证主配置是否存在
            main_config = session.query(EcnConfiguration).filter_by(id=ecn_config_id).first()
            if not main_config:
                raise ValueError(f"ECN configuration with ID {ecn_config_id} not found")
            
            # 创建详细配置
            config_detail = EcnConfigurationDetail(
                ecn_config_id=ecn_config_id,
                sysname=detail_config.get("sysname"),
                switch_sn=detail_config.get("switch_sn"),
                port=detail_config.get("port", []),
                queue=detail_config.get("queue", []),
                max_threshold=detail_config.get("max_threshold"),
                min_threshold=detail_config.get("min_threshold"),
                drop_probability=detail_config.get("drop_probability"),
                ecn_threshold=detail_config.get("ecn_threshold"),
                wred_enable=detail_config.get("wred_enable"),
                is_all_ports=detail_config.get("is_all_ports", False),
                is_all_queues=detail_config.get("is_all_queues", False),
                config_data=detail_config.get("config_data")
            )
            session.add(config_detail)
            session.flush()
            
            session.commit()
            return config_detail.id

    def update_ecn_configuration(self, config_id, updates):
        """
        Update ECN main configuration record
        Args:
            config_id: int, ECN configuration ID
            updates: dict, 要更新的字段数据
                {
                    'sysname': str,          # optional
                    'switch_sn': str,        # optional
                    'enabled': bool,         # optional
                    'mode': str,            # optional
                    'config_data': str       # optional
                }
        Returns:
            bool: 是否成功
        """
        session = self.get_session()
        with session.begin(subtransactions=True):

            config = session.query(EcnConfiguration).filter_by(id=config_id).first()
            if not config:
                raise ValueError(f"ECN configuration with ID {config_id} not found")
            
            # 更新指定字段
            for key, value in updates.items():
                if hasattr(config, key):
                    setattr(config, key, value)
            
            session.commit()
            return True
            


    def update_ecn_configuration_detail(self, detail_id, updates):
        """
        Update ECN detail configuration record
        Args:
            detail_id: int, ECN detail configuration ID
            updates: dict, 要更新的字段数据
                {
                    'sysname': str,              # optional
                    'switch_sn': str,            # optional
                    'port': list,                # optional
                    'queue': list,               # optional
                    'max_threshold': int,        # optional
                    'min_threshold': int,        # optional
                    'drop_probability': int,     # optional
                    'ecn_threshold': int,        # optional
                    'wred_enable': bool,         # optional
                    'is_all_ports': bool,        # optional
                    'is_all_queues': bool        # optional
                }
        Returns:
            bool: 是否成功
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            config_detail = session.query(EcnConfigurationDetail).filter_by(id=detail_id).first()
            if not config_detail:
                raise ValueError(f"ECN configuration detail with ID {detail_id} not found")
            
            # 处理JSON字段
            processed_updates = {}
            for key, value in updates.items():
                    processed_updates[key] = value
            
            # 更新指定字段
            for key, value in processed_updates.items():
                if hasattr(config_detail, key):
                    setattr(config_detail, key, value)
            
            session.commit()
            return True

    def delete_ecn_configuration(self, config_id):
        """
        Private method: Delete ECN configuration record
        Args:
            config_id: ECN configuration ID
        Returns:
            bool: Whether delete successful
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            session.query(EcnConfigurationDetail).filter_by(ecn_config_id=config_id).delete()
            session.query(EcnConfiguration).filter_by(id=config_id).delete()
            

    def _insert_qos_configuration(self, session, sysname, switch_sn, forwarding_class, local_priority, scheduler, mode, weight, guaranteed_rate, config_data=None):
        """
        Private method: Insert QoS configuration record
        """
        qos_configuration = QosConfiguration(
            sysname=sysname,
            switch_sn=switch_sn,
            forwarding_class=forwarding_class,
            local_priority=local_priority,
            scheduler=scheduler,
            mode=mode,
            weight=weight,
            guaranteed_rate=guaranteed_rate,
            config_data=config_data
        )
        session.add(qos_configuration)


    def _insert_qos_ingress_configuration(self, session, sysname, switch_sn, forwarding_class, 
                                        classifier, port, queue, trust_mode, is_all_ports=False, 
                                        is_all_queues=False, config_data=None):
        """
        Private method: Insert QoS ingress configuration record
        Args:
            sysname: str, Switch name
            switch_sn: str, Switch SN
            forwarding_class: str, Forwarding class
            classifier: str, Classifier
            port: JSON, Port list
            queue: JSON, Queue list
            trust_mode: str, Trust mode
            is_all_ports: bool, Whether to apply to all ports
            is_all_queues: bool, Whether to apply to all queues
            config_data: str, Configuration data
        Returns:
            int: New configuration ID
        """
        ingress_config = QosIngressConfiguration(
            sysname=sysname,
            switch_sn=switch_sn,
            forwarding_class=forwarding_class,
            classifier=classifier,
            port=port,
            queue=queue,
            trust_mode=trust_mode,
            is_all_ports=is_all_ports,
            is_all_queues=is_all_queues,
            config_data=config_data
        )
        session.add(ingress_config)


    def _insert_qos_egress_configuration(self, session, sysname, switch_sn, scheduler, 
                                       scheduler_profile, port, local_priority, forwarding_class,
                                       is_all_ports=False, is_all_queues=False, config_data=None):
        """
        Private method: Insert QoS egress configuration record
        Args:
            sysname: str, Switch name
            switch_sn: str, Switch SN
            scheduler: str, Scheduler
            scheduler_profile: str, Scheduler profile
            port: JSON, Port list
            local_priority: int, Local priority
            forwarding_class: str, Forwarding class
            is_all_ports: bool, Whether to apply to all ports
            is_all_queues: bool, Whether to apply to all queues
            config_data: str, Configuration data
        Returns:
            int: New configuration ID
        """
        egress_config = QosEgressConfiguration(
            sysname=sysname,
            switch_sn=switch_sn,
            scheduler=scheduler,
            scheduler_profile=scheduler_profile,
            port=port,
            local_priority=local_priority,
            forwarding_class=forwarding_class,
            is_all_ports=is_all_ports,
            is_all_queues=is_all_queues,
            config_data=config_data
        )
        session.add(egress_config)
        
    def _get_qos_ingress_configs_by_forwarding_class(self, switch_sn, forwarding_class):
        """
        Get ingress configurations for a specific forwarding class on a switch (ORM layer)
        Args:
            switch_sn: str, Switch serial number
            forwarding_class: str, Forwarding class to match
        Returns:
            list: List of ingress configurations for the forwarding class
        """
        session = self.get_session()
        ingress_configs = session.query(QosIngressConfiguration).filter_by(
            switch_sn=switch_sn,
            forwarding_class=forwarding_class
        ).all()
        return [ingress.make_dict() for ingress in ingress_configs]
    
    def _get_qos_egress_configs_by_forwarding_class(self, switch_sn, forwarding_class):
        """
        Get egress configurations for a specific forwarding class on a switch (ORM layer)
        Args:
            switch_sn: str, Switch serial number
            forwarding_class: str, Forwarding class to match
        Returns:
            list: List of egress configurations for the forwarding class
        """
        session = self.get_session()
        egress_configs = session.query(QosEgressConfiguration).filter_by(
            switch_sn=switch_sn,
            forwarding_class=forwarding_class
        ).all()
        return [egress.make_dict() for egress in egress_configs]

    # def add_qos_configuration(self, qos_config, ingress_configs, egress_configs):
    #     """
    #     Business method: Add complete QoS configuration
    #     Args:
    #         qos_config: dict, QoS configuration information
    #             {
    #                 'sysname': str,
    #                 'switch_sn': str,
    #                 'forwarding_class': str,
    #                 'local_priority': int,
    #                 'scheduler': str,
    #                 'mode': str,
    #                 'weight': int,
    #                 'guaranteed_rate': int
    #             }
    #         ingress_configs: list of dict, Ingress configuration list
    #             [
    #                 {
    #                     'classifier': str,
    #                     'port': list,
    #                     'queue': list,
    #                     'trust_mode': str,
    #                     'forwarding_class': str,
    #                     'is_all_ports': bool,
    #                     'is_all_queues': bool,
    #                     'config_data': str
    #                 },
    #                 ...
    #             ]
    #         egress_configs: list of dict, Egress configuration list
    #             [
    #                 {
    #                     'scheduler': str,
    #                     'scheduler_profile': str,
    #                     'port': list,
    #                     'local_priority': int,
    #                     'forwarding_class': str,
    #                     'is_all_ports': bool,
    #                     'is_all_queues': bool,
    #                     'config_data': str
    #                 },
    #                 ...
    #             ]
    #     Returns:
    #         int: QoS configuration ID
    #     """
    #     config_id = self._insert_qos_configuration(
    #         sysname=qos_config.get('sysname'),
    #         switch_sn=qos_config.get('switch_sn'),
    #         forwarding_class=qos_config.get('forwarding_class'),
    #         local_priority=qos_config.get('local_priority'),
    #         scheduler=qos_config.get('scheduler'),
    #         mode=qos_config.get('mode'),
    #         weight=qos_config.get('weight'),
    #         guaranteed_rate=qos_config.get('guaranteed_rate')
    #     )
        
    #     for ingress in ingress_configs:
    #         self._insert_qos_ingress_configuration(
    #             sysname=qos_config.get('sysname'),
    #             switch_sn=qos_config.get('switch_sn'),
    #             forwarding_class=ingress.get('forwarding_class'),
    #             classifier=ingress.get('classifier'),
    #             port=ingress.get('port', []),
    #             queue=ingress.get('queue', []),
    #             trust_mode=ingress.get('trust_mode'),
    #             is_all_ports=ingress.get('is_all_ports', False),
    #             is_all_queues=ingress.get('is_all_queues', False),
    #             config_data=ingress.get('config_data')
    #         )
        
    #     for egress in egress_configs:
    #         self._insert_qos_egress_configuration(
    #             sysname=qos_config.get('sysname'),
    #             switch_sn=qos_config.get('switch_sn'),
    #             scheduler=egress.get('scheduler'),
    #             scheduler_profile=egress.get('scheduler_profile'),
    #             port=egress.get('port', []),
    #             local_priority=egress.get('local_priority'),
    #             forwarding_class=egress.get('forwarding_class'),
    #             is_all_ports=egress.get('is_all_ports', False),
    #             is_all_queues=egress.get('is_all_queues', False),
    #             config_data=egress.get('config_data')
    #         )
        
    #     return config_id

    def _update_qos_configuration(self, config_id, updates):
        """
        Private method: Update QoS configuration record
        Args:
            config_id: QoS configuration ID
            updates: dict, Attributes to update
        Returns:
            bool: Whether update successful
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            result = session.query(QosConfiguration).filter_by(id=config_id).update(updates)
            return result > 0
        
    def update_qos_configuration(self, config_id, qos_config, ingress_configs=None, egress_configs=None):
        """
        更新 QoS 配置及其关联的 ingress 和 egress 配置
        Args:
            config_id: int, QoS配置ID
            qos_config: dict, 主配置数据
                {
                    'sysname': str,
                    'switch_sn': str,
                    'forwarding_class': str,
                    'local_priority': str,
                    'scheduler': str,
                    'mode': int,
                    'weight': str,
                    'guaranteed_rate': str
                }
            ingress_configs: list, 入站配置列表
            egress_configs: list, 出站配置列表
        Returns:
            bool: 是否成功
        """
        session = self.get_session()
        try:
            with session.begin(subtransactions=True):
                # 1. 更新主配置
                main_config = session.query(QosConfiguration).filter_by(id=config_id).first()
                if not main_config:
                    return False
                    
                # 更新主配置字段
                for key, value in qos_config.items():
                    if hasattr(main_config, key):
                        setattr(main_config, key, value)
                
                # 2. 删除现有的 ingress 配置
                session.query(QosIngressConfiguration).filter_by(switch_sn=main_config.switch_sn).delete()
                
                # 3. 删除现有的 egress 配置  
                session.query(QosEgressConfiguration).filter_by(switch_sn=main_config.switch_sn).delete()
                
                # 4. 添加新的 ingress 配置
                if ingress_configs:
                    for ingress_config in ingress_configs:
                        new_ingress = QosIngressConfiguration(
                            sysname=ingress_config.get('sysname'),
                            switch_sn=ingress_config.get('switch_sn'),
                            classifier=ingress_config.get('classifier'),
                            trust_mode=ingress_config.get('trust_mode'),
                            port=ingress_config.get('port', []),
                            forwarding_class=ingress_config.get('forwarding_class'),
                            queue=ingress_config.get('queue', []),
                            is_all_ports=ingress_config.get('is_all_ports', False),
                            is_all_queues=ingress_config.get('is_all_queues', False)
                        )
                        session.add(new_ingress)
                
                # 5. 添加新的 egress 配置
                if egress_configs:
                    for egress_config in egress_configs:
                        new_egress = QosEgressConfiguration(
                            sysname=egress_config.get('sysname'),
                            switch_sn=egress_config.get('switch_sn'),
                            scheduler_profile=egress_config.get('scheduler_profile'),
                            scheduler=egress_config.get('scheduler'),
                            port=egress_config.get('port', []),
                            forwarding_class=egress_config.get('forwarding_class'),
                            local_priority=egress_config.get('local_priority'),
                            is_all_ports=egress_config.get('is_all_ports', False),
                            is_all_queues=egress_config.get('is_all_queues', False)
                        )
                        session.add(new_egress)
            
            # session.commit()
            return True
            
        except Exception as e:
            # session.rollback()
            LOG.error(f"Error updating QoS configuration: {str(e)}")
            return False
        finally:
            session.close()

    def delete_qos_configuration(self, config, updated_qos_config_data=None):
        """
        删除 QoS 配置及其关联的入站和出站配置
        
        Args:
            config: QosConfiguration, QoS 配置
            switch_sn: str, 交换机SN
            updated_qos_config_data: dict, 更新后的QoS配置数据
            
        Returns:
            bool: 删除是否成功
        """
        session = self.get_session()

        with session.begin(subtransactions=True):

            count = session.query(QosConfiguration).filter_by(switch_sn=config.switch_sn, scheduler=config.scheduler).count()
                
            session.query(QosIngressConfiguration).filter_by(switch_sn=config.switch_sn, forwarding_class=config.forwarding_class).delete()
            
            session.query(QosEgressConfiguration).filter_by(switch_sn=config.switch_sn, forwarding_class=config.forwarding_class).delete()
            
            session.query(QosConfiguration).filter_by(id=config.id).delete()

            if count == 1:
                session.query(QosEgressConfiguration).filter_by(switch_sn=config.switch_sn, scheduler=config.scheduler).delete()

            if updated_qos_config_data:
                self._update_remaining_qos_configs(session, config.switch_sn, updated_qos_config_data)
    
    def _update_remaining_qos_configs(self, session, switch_sn, updated_qos_config_data):
        """
        Private method: Update remaining QoS configurations
        Args:
            session: Database session
            switch_sn: Switch SN
            updated_qos_config_data: dict, Updated QoS configuration data
        """
        session.query(QosConfiguration).filter(
            QosConfiguration.switch_sn == switch_sn
        ).update({"config_data": json.dumps(updated_qos_config_data)})

        session.query(QosIngressConfiguration).filter(
            QosIngressConfiguration.switch_sn == switch_sn
        ).update({"config_data": json.dumps(updated_qos_config_data)})
        
        session.query(QosEgressConfiguration).filter(
            QosEgressConfiguration.switch_sn == switch_sn
        ).update({"config_data": json.dumps(updated_qos_config_data)})




    def add_configuration_overview(self, sysname, switch_sn, basic_info=None, pcp_dscp_info=None, lp_info=None):
        """
        添加配置概览信息
        Args:
            sysname: str, 交换机名称
            switch_sn: str, 交换机SN
            basic_info: dict, 基本配置信息
            pcp_dscp_info: dict, PCP/DSCP 映射信息
            lp_info: dict, 本地优先级信息
        Returns:
            int: 新配置概览记录的ID
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            overview = ConfigurationOverview(
                sysname=sysname,
                switch_sn=switch_sn,
                basic_info=basic_info,
                pcp_dscp_info=pcp_dscp_info,
                lp_info=lp_info
            )
            session.add(overview)
            session.flush()
            return overview.id
        
    def update_configuration_overview(self, config_id, config):
        """
        更新配置概览信息
        Args:
            sysname: str, 交换机名称
            switch_sn: str, 交换机SN
            basic_info: dict, 基本配置信息
            pcp_dscp_info: dict, PCP/DSCP 映射信息
            lp_info: dict, 本地优先级信息
        Returns:
            bool: 是否更新成功
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            result = session.query(ConfigurationOverview).filter_by(id=config_id).update(config)
            return result > 0
        
    def delete_configuration_overview(self, config_id):
        """
        删除配置概览信息
        Args:
            config_id: int, 配置概览记录ID
        Returns:
            bool: 是否删除成功
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            result = session.query(ConfigurationOverview).filter_by(id=config_id).delete()
            return result > 0
    
    def get_configuration_overview_by_sn(self, switch_sn):
        session = self.get_session()
        return session.query(ConfigurationOverview).filter_by(switch_sn=switch_sn).first()

    def update_global_param_for_all_data(self, switch_sn, query_model, config):
        model_mapping = {
            "PfcConfiguration": PfcConfiguration,
            "EcnConfigurationDetail": EcnConfigurationDetail,
            "QosIngressConfiguration": QosIngressConfiguration,
            "QosEgressConfiguration": QosEgressConfiguration,
            "PfcWdConfiguration": PfcWdConfiguration,
            "DLBConfiguration": DLBConfiguration,
            "RoceEasyDeployConfiguration": RoceEasyDeployConfiguration,
            "PfcBufferIngressConfiguration": PfcBufferIngressConfiguration,
            "PfcBufferEgressConfiguration": PfcBufferEgressConfiguration
        }

        if query_model not in model_mapping:
            raise ValueError(f"Unsupported query_model: {query_model}")
        
        model_class = model_mapping[query_model]
        session = self.get_session()
        session.query(model_class).filter_by(switch_sn=switch_sn).update(config)

    def validate_ecn_configurations(self, configurations):
        """
        Validate ECN configurations with business rules (Service layer)
        Note: Uniqueness validation is handled by @validate_ecn_uniqueness decorator
        Args:
            configurations: list, List of ECN configuration data
        Returns:
            dict: Validated configurations ready for deployment
        Raises:
            ValueError: When validation fails
        """
        if not configurations:
            raise ValueError("No configurations provided")
        
        session = self.get_session()
        validated_configs = []
        processed_switch_sns = set()
        
        # Process each configuration
        for conf in configurations:
            sysname = conf.get("sysname")
            switch_sn = conf.get("switch_sn")
            enabled = conf.get("enabled", True)
            mode = conf.get("mode")
            details = conf.get("details", [])
            
            if not sysname or not switch_sn:
                raise ValueError("sysname and switch_sn are required")
            
            # Business Rule 1: Prevent duplicate switch_sn in single request
            if switch_sn in processed_switch_sns:
                raise ValueError(f"Duplicate switch_sn '{switch_sn}' found in request. Each switch can only have one ECN configuration per request.")
            processed_switch_sns.add(switch_sn)
            
            # Business Rule 2: ECN enabled status and detail configuration constraints
            if enabled and details:
                raise ValueError(f"When ECN is enabled (enabled=true), details should be empty for switch {switch_sn}")
            elif not enabled and not details:
                raise ValueError(f"When ECN is disabled (enabled=false), at least one detail configuration is required for switch {switch_sn}")
            
            # Get switch information using generic method
            switch = self._get_switch_by_sn_or_raise(session, switch_sn)
            
            # Check if main configuration already exists (for proper linking)
            existing_main_config = session.query(EcnConfiguration).filter_by(switch_sn=switch_sn).first()
            
            # Prepare main configuration data
            ecn_config = {
                "sysname": sysname,
                "switch_sn": switch_sn,
                "enabled": enabled,
                "mode": mode,
                "existing_config_id": existing_main_config.id if existing_main_config else None
            }
            
            # Prepare detail configurations
            prepared_details = []
            for detail in details:
                ecn_config_detail = {
                    "sysname": sysname,
                    "switch_sn": switch_sn,
                    "port": detail.get("port", []),
                    "queue": detail.get("queue", []),
                    "max_threshold": detail.get("max_threshold"),
                    "min_threshold": detail.get("min_threshold"),
                    "drop_probability": detail.get("drop_probability"),
                    "ecn_threshold": detail.get("ecn_threshold"),
                    "wred_enable": detail.get("wred_enable"),
                    "enabled": detail.get("enabled", enabled),
                    "mode": detail.get("mode", mode),
                    "is_all_ports": detail.get("is_all_ports", False),
                    "is_all_queues": detail.get("is_all_queues", False)
                }
                prepared_details.append(ecn_config_detail)
            
            validated_configs.append({
                "switch": switch,
                "main_config": ecn_config,
                "details": prepared_details
            })
        
        return {
            "validated_configs": validated_configs
        }

    def save_ecn_detail_to_db(self, main_config, detail_config, config_data):
        """
        Save single ECN detail configuration to database after successful deployment (Service layer)
        主配置不存在则创建，存在则直接插入detail记录
        Args:
            main_config: dict, Main configuration data
            detail_config: dict, Detail configuration data
            config_data: dict, Deployment config data
        Returns:
            dict: Operation result with config_id and detail_id
        """
        existing_config_id = main_config.get("existing_config_id")
        
        if existing_config_id:
            # 主配置已存在，直接插入 detail 记录
            main_config_id = existing_config_id
        else:
            # 主配置不存在，先创建主配置
            main_config_clean = {k: v for k, v in main_config.items() if k != "existing_config_id"}
            main_config_id = self.add_ecn_configuration(**main_config_clean)
        
        # 插入 detail 配置记录
        detail_with_config = {
            **detail_config,
            "config_data": json.dumps(config_data.get("new_val", {}))
        }
        detail_id = self.add_ecn_configuration_detail(main_config_id, detail_with_config)
        
        return {
            "main_config_id": main_config_id,
            "detail_id": detail_id,
            "is_new_main_config": existing_config_id is None
        }
    
    def save_ecn_main_config_only(self, main_config):
        """
        Save ECN main configuration only (when enabled=true with no details)
        Args:
            main_config: dict, Main configuration data
        Returns:
            int: Created main configuration ID
        """
        existing_config_id = main_config.get("existing_config_id")
        
        if existing_config_id:
            # 主配置已存在，更新它
            main_config_clean = {k: v for k, v in main_config.items() if k != "existing_config_id"}
            self.update_ecn_configuration(existing_config_id, main_config_clean)
            return existing_config_id
        else:
            # 主配置不存在，创建新的
            main_config_clean = {k: v for k, v in main_config.items() if k != "existing_config_id"}
            return self.add_ecn_configuration(**main_config_clean)

    def bulk_update_ecn_configurations(self, switch_sn, configurations):
        """
        Analyze and prepare ECN configuration bulk update operations with business rules
        Args:
            switch_sn: str, Switch serial number 
            configurations: list, List of configuration updates
                [
                    {
                        "config_id": str,     # optional, update if exists, create if not
                        "sysname": str,
                        "switch_sn": str,
                        "enabled": bool,
                        "mode": str,
                        "details": [
                            {
                                "detail_id": str,  # optional, update if exists, create if not
                                "port": list,
                                "queue": list,
                                "max_threshold": int,
                                "min_threshold": int,
                                "drop_probability": int,
                                "ecn_threshold": int,
                                "wred_enable": bool,
                                "is_all_ports": bool,
                                "is_all_queues": bool
                            }
                        ]
                    }
                ]
        Returns:
            dict: Operations analysis result
                {
                    "switch": Switch,
                    "main_ids_to_delete": list,
                    "detail_ids_to_delete": list, 
                    "configs_to_update": list,
                    "details_to_update": list,
                    "configs_to_create": list,
                    "details_to_create": list,
                    "existing_configs": dict,
                    "existing_details": dict
                }
        """
        session = self.get_session()
        
        # Get switch information
        switch = self._get_switch_by_sn_or_raise(session, switch_sn)
        
        # Get existing configurations
        existing_main_configs = session.query(EcnConfiguration).filter_by(switch_sn=switch_sn).all()
        existing_detail_configs = session.query(EcnConfigurationDetail).filter_by(switch_sn=switch_sn).all()
        
        existing_main_config_ids = {str(config.id) for config in existing_main_configs}
        existing_detail_config_ids = {str(detail.id) for detail in existing_detail_configs}
        
        # Build maps for easy access
        existing_configs_map = {str(config.id): config for config in existing_main_configs}
        existing_details_map = {str(detail.id): detail for detail in existing_detail_configs}
        
        # Parse incoming configurations
        incoming_main_config_ids = set()
        incoming_detail_config_ids = set()
        configs_to_update = []
        configs_to_create = []
        details_to_update = []
        details_to_create = []
        
        for config in configurations:
            main_config_id = config.get("config_id")
            
            # Apply business rule: each switch_sn can only have one main config
            if main_config_id and str(main_config_id) in existing_main_config_ids:
                # Validate business rules
                enabled = config.get("enabled", True)
                details = config.get("details", [])
                
                if not enabled and not details:
                    raise ValueError(f"When enabled=false, at least one detail configuration is required")
                if enabled and details:
                    raise ValueError(f"When enabled=true, no detail configurations should be provided")
                
                configs_to_update.append(config)
                incoming_main_config_ids.add(str(main_config_id))
            else:
                # Update operation requires config_id
                raise ValueError(f"config_id is required for update operation on switch {switch_sn}. Use save endpoint for creating new configurations.")
            
            # Process detail configurations
            for detail in config.get("details", []):
                detail_id = detail.get("detail_id")
                detail["main_config"] = config  # Add main config reference
                
                if detail_id and str(detail_id) in existing_detail_config_ids:
                    details_to_update.append(detail)
                    incoming_detail_config_ids.add(str(detail_id))
                else:
                    # Allow creating new detail configurations in update operation
                    # (e.g., when changing from enabled=true to enabled=false)
                    details_to_create.append(detail)
        
        # Find configurations to delete
        main_ids_to_delete = existing_main_config_ids - incoming_main_config_ids
        detail_ids_to_delete = existing_detail_config_ids - incoming_detail_config_ids
        
        return {
            "switch": switch,
            "main_ids_to_delete": list(main_ids_to_delete),
            "detail_ids_to_delete": list(detail_ids_to_delete),
            "configs_to_update": configs_to_update,
            "details_to_update": details_to_update,
            "configs_to_create": configs_to_create,
            "details_to_create": details_to_create,
            "existing_configs": existing_configs_map,
            "existing_details": existing_details_map
        }

    def execute_ecn_bulk_operations(self, operations_data):
        """
        Execute ECN configuration bulk database operations after deployment
        Args:
            operations_data: dict, Database operations data
                {
                    "main_ids_to_delete": list,
                    "detail_ids_to_delete": list,
                    "update_operations": list,
                    "detail_update_operations": list,
                    "create_operations": list,
                    "detail_create_operations": list
                }
        Returns:
            bool: Whether operations successful
        """
        session = self.get_session()
        
        with session.begin(subtransactions=True):
            # Delete operations
            for main_id in operations_data.get("main_ids_to_delete", []):
                session.query(EcnConfiguration).filter_by(id=main_id).delete()
            
            for detail_id in operations_data.get("detail_ids_to_delete", []):
                session.query(EcnConfigurationDetail).filter_by(id=detail_id).delete()
                
            created_config_map = {}
            # Update operations - main configs
            for operation in operations_data.get("update_operations", []):
                config_id = operation.get("config_id")
                updates = operation.get("updates")
                if config_id and updates:
                    session.query(EcnConfiguration).filter_by(id=config_id).update(updates)
                    main_config_key = f"{operation.get('sysname')}_{updates.get('switch_sn')}"
                    created_config_map[main_config_key] = config_id
            
            # Update operations - detail configs
            for operation in operations_data.get("detail_update_operations", []):
                detail_id = operation.get("detail_id")
                updates = operation.get("updates")
                if detail_id and updates:
                    session.query(EcnConfigurationDetail).filter_by(id=detail_id).update(updates)
            
            # Create operations - main configs
            for config_data in operations_data.get("create_operations", []):
                new_config = EcnConfiguration(**config_data)
                session.add(new_config)
                session.flush()
                # Store mapping for detail config creation
                main_config_key = f"{config_data.get('sysname')}_{config_data.get('switch_sn')}"
                created_config_map[main_config_key] = new_config.id
            
            # Create operations - detail configs (link to main configs)
            for detail_operation in operations_data.get("detail_create_operations", []):
                main_config = detail_operation.get("main_config")
                detail_data = detail_operation.get("detail_data")
                
                if main_config and detail_data:
                    # First check if main config was newly created
                    main_config_key = f"{main_config.get('sysname')}_{main_config.get('switch_sn')}"
                    main_config_id = created_config_map.get(main_config_key)
                    
                    # If not newly created, check if it's an existing main config being updated
                    if not main_config_id:
                        main_config_id = main_config.get("config_id")
                    
                    if main_config_id:
                        # Add ecn_config_id to link with main config
                        detail_data["ecn_config_id"] = main_config_id
                        new_detail = EcnConfigurationDetail(**detail_data)
                        session.add(new_detail)
            

    def get_ecn_configs_list_data(self, request_data):
        """
        Get ECN configurations list data with pagination (Service layer)
        Args:
            request_data: dict, Request parameters including pagination and search
        Returns:
            dict: Flat list data with pagination info
        """
        session = self.get_session()
        
        if not request_data:
            raise ValueError("No query parameters provided")

        # Import required modules
        from sqlalchemy import Text, or_, func, text, asc, desc
        from server.util import utils

        # Use reusable fabric join query builder
        pre_query, SwitchAlias, AssocAlias, FabricAlias = self._build_fabric_join_query(session, EcnConfiguration)
        
        # Handle custom sorting with window function
        sort_fields = request_data.get("sortFields", [])
        window_order_by = []
        has_sysname_sort = False
        sysname_order = ""
        
        if sort_fields:
            # Build order_by list for window function
            for field in sort_fields:
                field_name = field.get("field")
                field_order = field.get("order", "asc")
                
                if hasattr(EcnConfiguration, field_name):
                    column = getattr(EcnConfiguration, field_name)
                    
                    # Handle JSON field sorting by casting to Text
                    if field_name == "port" or field_name == "queue":
                        column = column.cast(Text)
                    
                    if field_order == "asc":
                        window_order_by.append(asc(column))
                    elif field_order == "desc":
                        window_order_by.append(desc(column))
                    
                    # Check if sysname is in sort fields
                    if field_name == "sysname":
                        has_sysname_sort = True
                        sysname_order = field_order

            pre_query = pre_query.add_columns(
                func.row_number().over(
                    partition_by=EcnConfiguration.switch_sn,
                    order_by=window_order_by
                ).label('row_num')
            )
        else:
            # Default sorting by id if no custom sort fields provided
            pre_query = pre_query.add_columns(
                func.row_number().over(
                    partition_by=EcnConfiguration.switch_sn,
                    order_by=EcnConfiguration.id
                ).label('row_num')
            )
        
        # Apply different ordering based on whether sysname is in sort fields
        if has_sysname_sort:
            # For sysname sorting: maintain switch_sn grouping while sorting by sysname
            if sysname_order == "asc":
                pre_query = pre_query.order_by(EcnConfiguration.sysname.asc(), EcnConfiguration.switch_sn, text('row_num'))
            else:
                pre_query = pre_query.order_by(EcnConfiguration.sysname.desc(), EcnConfiguration.switch_sn, text('row_num'))
        else:
            # For non-sysname sorting: order by switch_sn, then row_num
            pre_query = pre_query.order_by(EcnConfiguration.switch_sn, text('row_num'))

        # Handle search conditions
        # if request_data.get("search"):
        #     search_value = request_data["search"]
        #     conditions = []
        #     for field in ["sysname", "switch_sn", "mode"]:
        #         if hasattr(EcnConfiguration, field):
        #             column = getattr(EcnConfiguration, field)
        #             conditions.append(column.cast(Text).ilike(f'%{search_value}%'))
        #     if conditions:
        #         pre_query = pre_query.filter(or_(*conditions))

        # Get pagination and search results
        # Skip default sorting to avoid issues with JSON field sorting
        page_num, page_size, total_count, query_result = utils.query_helper(
            EcnConfiguration,
            pre_query=pre_query,
            data=request_data,
            skip_sort=True  # Skip default sorting to avoid JSON field sorting issues
        )

        # Build flat configuration list
        configs_list = []
        processed_config_ids = set()

        for config, switch, assoc, fabric, row_num in query_result:
            # Avoid duplicate configs due to JOIN
            if config.id in processed_config_ids:
                continue
            processed_config_ids.add(config.id)
            
            # Use reusable serialization method
            config_dict = self._serialize_config_with_fabric(config, switch, fabric)
            
            # Get associated detail configurations
            detail_configs = session.query(EcnConfigurationDetail).filter_by(
                ecn_config_id=config.id
            ).all()
            
            config_dict["details"] = [detail.make_dict() for detail in detail_configs]
            configs_list.append(config_dict)

        return {
            "data": configs_list,
            "page": page_num,
            "pageSize": page_size,
            "total": total_count
        }

    def get_ecn_port_configs_list_data(self, request_data):
        """
        Get ECN port configurations list data with tree structure and pagination (Service layer)
        Args:
            request_data: dict, Request parameters including pagination and search
        Returns:
            dict: Tree-structured data with pagination info
        """
        session = self.get_session()
        
        if not request_data:
            raise ValueError("No query parameters provided")

        # Import required modules
        from sqlalchemy import Text, or_, func, text, asc, desc
        from server.util import utils

        # Build fabric join query using private method for EcnConfigurationDetail
        fabric_query, SwitchAlias, AssocAlias, FabricAlias = self._build_fabric_join_query(session, EcnConfigurationDetail)

        # Build base query with ECN configuration join
        pre_query = (
            fabric_query
            .join(EcnConfiguration, EcnConfigurationDetail.ecn_config_id == EcnConfiguration.id)
        )
        
        # Handle custom sorting with window function
        sort_fields = request_data.get("sortFields", [])
        window_order_by = []
        has_sysname_sort = False
        sysname_order = ""
        
        if sort_fields:
            # Build order_by list for window function
            for field in sort_fields:
                field_name = field.get("field")
                field_order = field.get("order", "asc")
                
                if hasattr(EcnConfigurationDetail, field_name):
                    column = getattr(EcnConfigurationDetail, field_name)
                    
                    # Handle JSON field sorting by casting to Text
                    if field_name == "port" or field_name == "queue":
                        column = column.cast(Text)
                    
                    if field_order == "asc":
                        window_order_by.append(asc(column))
                    elif field_order == "desc":
                        window_order_by.append(desc(column))
                    
                    # Check if sysname is in sort fields
                    if field_name == "sysname":
                        has_sysname_sort = True
                        sysname_order = field_order

            pre_query = pre_query.add_columns(
                func.row_number().over(
                    partition_by=EcnConfigurationDetail.switch_sn,
                    order_by=window_order_by
                ).label('row_num')
            )
        else:
            # Default sorting by id if no custom sort fields provided
            pre_query = pre_query.add_columns(
                func.row_number().over(
                    partition_by=EcnConfigurationDetail.switch_sn,
                    order_by=EcnConfigurationDetail.id
                ).label('row_num')
            )
        
        # Apply different ordering based on whether sysname is in sort fields
        if has_sysname_sort:
            # For sysname sorting: maintain switch_sn grouping while sorting by sysname
            if sysname_order == "asc":
                pre_query = pre_query.order_by(EcnConfigurationDetail.sysname.asc(), EcnConfigurationDetail.switch_sn, text('row_num'))
            else:
                pre_query = pre_query.order_by(EcnConfigurationDetail.sysname.desc(), EcnConfigurationDetail.switch_sn, text('row_num'))
        else:
            # For non-sysname sorting: order by switch_sn, then row_num
            pre_query = pre_query.order_by(EcnConfigurationDetail.switch_sn, text('row_num'))

        # Get query results
        # Skip default sorting to avoid issues with JSON field sorting
        page_num, page_size, total_count, query_result = utils.query_helper(
            EcnConfigurationDetail,
            pre_query=pre_query,
            data=request_data,
            skip_sort=True  # Skip default sorting to avoid JSON field sorting issues
        )
            
        tree_data = []
        current_switch = None

        for config_detail, switch, assoc, fabric, row_num in query_result:
            # Serialize configuration with fabric information using private method
            config_dict = self._serialize_config_with_fabric(config_detail, switch, fabric)
            
            if not current_switch or current_switch.get("switch_sn") != config_detail.switch_sn:
                current_switch = self._serialize_config_with_fabric(config_detail, switch, fabric)
                current_switch["children"] = []
                tree_data.append(current_switch)
            else:
                config_dict["sysname"] = ""
                current_switch["children"].append(config_dict)

        return {
            "tree_data": tree_data,
            "page": page_num,
            "pageSize": page_size,
            "total": total_count
        }

    def get_ecn_configuration_for_deletion(self, config_id):
        """
        Get ECN configuration and switch information for deletion (Service layer)
        Args:
            config_id: str, Configuration ID to delete
        Returns:
            dict: Configuration data including main config, detail configs, and switch info
        """
        session = self.get_session()
        
        if not config_id:
            raise ValueError("config_id is required")
        
        # Get main configuration using private method with error handling
        config = self._get_config_by_id_or_raise(session, EcnConfiguration, config_id)
        
        # Get associated detail configurations for building delete configuration
        detail_configs = session.query(EcnConfigurationDetail).filter_by(ecn_config_id=config_id).all()
        
        # Get switch information using private method with error handling
        switch = self._get_switch_by_sn_or_raise(session, config.switch_sn)
        
        return {
            "config": config,
            "detail_configs": detail_configs,
            "switch": switch
        }
    
    def get_ecn_main_config_by_switch_sn(self, switch_sn):
        """
        Get ECN main configuration by switch_sn (Service layer)
        Args:
            switch_sn: str, Switch serial number
        Returns:
            list: List of ECN configurations with details and fabric information
        
        """
        session = self.get_session()
        return session.query(EcnConfiguration).filter_by(switch_sn=switch_sn).first()

    def get_ecn_configs_detail_by_switch_sn(self, switch_sn):
        """
        Get all ECN configuration records for a specific switch by switch_sn (Service layer)
        Args:
            switch_sn: str, Switch serial number
        Returns:
            list: List of ECN configurations with details and fabric information
        """
        session = self.get_session()
        
        if not switch_sn:
            raise ValueError("switch_sn is required")
        
        # Build fabric join query using private method
        query, SwitchAlias, AssocAlias, FabricAlias = self._build_fabric_join_query(session, EcnConfiguration)
        
        # Query all ECN configurations for this switch with joined tables
        query_result = (
            query
            .filter(EcnConfiguration.switch_sn == switch_sn)
            .order_by(EcnConfiguration.id)
            .all()
        )

        # Build configuration list with details
        configs = []
        for config, switch, assoc, fabric in query_result:
            # Serialize configuration with fabric information using private method
            config_dict = self._serialize_config_with_fabric(config, switch, fabric)
            
            # Get associated detail configurations
            detail_configs = session.query(EcnConfigurationDetail).filter_by(
                ecn_config_id=config.id
            ).all()
            
            config_dict["details"] = [detail.make_dict() for detail in detail_configs]
            configs.append(config_dict)

        return configs

    def save_qos_configurations(self, configurations_data):
        """
        Service method to save QoS configurations with validation and processing
        Note: Configurations are validated and processed, ready for deployment first, then save to database
        Args:
            configurations_data: dict, Request data containing configurations
                {
                    "configurations": [...],
                    "ingress_configurations": [...],
                    "egress_configurations": [...]
                }
        Returns:
            dict: Processed configurations ready for deployment
        Raises:
            ValueError: When validation fails
        """
        if not configurations_data:
            raise ValueError("No configuration data provided")
        
        configurations = configurations_data.get("configurations", [])
        ingress_configs = configurations_data.get("ingress_configurations", [])
        egress_configs = configurations_data.get("egress_configurations", [])
        
        if not configurations:
            raise ValueError("No configurations provided")
        
        # Validate all configurations are for creation (no config_id)
        for config in configurations:
            if config.get("config_id"):
                raise ValueError("Create operation should not include config_id. Use update endpoint for existing configurations.")
        
        session = self.get_session()
        
        # Group configurations by sysname and forwarding_class
        grouped_configs = {}
        for config in configurations:
            key = (config.get("sysname"), config.get("forwarding_class"))
            if key not in grouped_configs:
                grouped_configs[key] = {
                    "sysname": config.get("sysname"),
                    "switch_sn": config.get("switch_sn"),
                    "forwarding_class": config.get("forwarding_class"),
                    "local_priority": config.get("local_priority"),
                    "scheduler": config.get("scheduler"),
                    "mode": config.get("mode"),
                    "weight": config.get("weight"),
                    "guaranteed_rate": config.get("guaranteed_rate"),
                    "ingress_configurations": [],
                    "egress_configurations": []
                }
        
        # Add ingress configurations
        for ingress in ingress_configs:
            key = (ingress.get("sysname"), ingress.get("forwarding_class"))
            if key in grouped_configs:
                grouped_configs[key]["ingress_configurations"].append({
                    "sysname": ingress.get("sysname"),
                    "switch_sn": ingress.get("switch_sn"),
                    "classifier": ingress.get("classifier"),
                    "trust_mode": ingress.get("trust_mode"),
                    "port": ingress.get("port"),
                    "forwarding_class": ingress.get("forwarding_class"),
                    "queue": ingress.get("queue"),
                    "is_all_ports": ingress.get("is_all_ports"),
                    "is_all_queues": ingress.get("is_all_queues")
                })
        
        # Add egress configurations
        for egress in egress_configs:
            key = (egress.get("sysname"), egress.get("forwarding_class"))
            if key in grouped_configs:
                grouped_configs[key]["egress_configurations"].append({
                    "sysname": egress.get("sysname"),
                    "switch_sn": egress.get("switch_sn"),
                    "scheduler_profile": egress.get("scheduler_profile"),
                    "scheduler": egress.get("scheduler"),
                    "port": egress.get("port"),
                    "forwarding_class": egress.get("forwarding_class"),
                    "local_priority": egress.get("local_priority"),
                    "is_all_ports": egress.get("is_all_ports")
                })
        
        # Process each grouped configuration and validate switch exists
        processed_configs = []
        for config in grouped_configs.values():
            switch_sn = config.get("switch_sn")
            
            # Validate switch exists using reusable method
            switch = self._get_switch_by_sn_or_raise(session, switch_sn)
            
            processed_configs.append({
                "config": config,
                "switch": switch
            })
        
        return {
            "processed_configs": processed_configs
        }
    
    def create_qos_configs_in_db(self, processed_configs):
        """
        Service method to create QoS configurations in database after successful deployment
        Args:
            processed_configs: list, List of processed configuration data
        Returns:
            list: List of configuration IDs
        """
        config_ids = []
        session = self.get_session()
        
        with session.begin(subtransactions=True):
            for item in processed_configs:
                config = item.get("config")
                
                # Prepare main configuration data
                qos_config = {
                    "sysname": config.get("sysname"),
                    "switch_sn": config.get("switch_sn"),
                    "forwarding_class": config.get("forwarding_class"),
                    "local_priority": config.get("local_priority"),
                    "scheduler": config.get("scheduler"),
                    "mode": config.get("mode"),
                    "weight": config.get("weight"),
                    "guaranteed_rate": config.get("guaranteed_rate")
                }
                
                # Create new configuration
                config_id = self.add_qos_configuration(
                    qos_config=qos_config,
                    ingress_configs=config.get("ingress_configurations", []),
                    egress_configs=config.get("egress_configurations", [])
                )
                
                if not config_id:
                    raise ValueError("Failed to create QoS configuration")
                
                config_ids.append({
                    "config_id": config_id,
                    "config": config
                })
        
        return config_ids
    
    def update_qos_config_data_after_deployment(self, config_id, config_data):
        """
        Service method to update QoS configuration data field after successful deployment
        Args:
            config_id: int, Configuration ID
            config_data: str, JSON string of configuration data
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            self._update_qos_configuration(
                config_id,
                {"config_data": config_data}
            )

    def save_qos_configurations_with_config_data(self, switch_config_data):
        """
        Service method to save QoS configurations with config_data already included
        Similar to save_pfc_configurations pattern - one-step save with complete data
        Args:
            switch_config_data: dict, Configuration data for the switch including config_data
                {
                    "configurations": [...],
                    "ingress_configurations": [...],
                    "egress_configurations": [...],
                    "config_data": "json_string"  # Already includes deployment data
                }
        Returns:
            list: List of created configuration IDs
        Raises:
            ValueError: When validation fails
        """
        if not switch_config_data:
            raise ValueError("No configuration data provided")
        
        configurations = switch_config_data.get("configurations", [])
        ingress_configurations = switch_config_data.get("ingress_configurations", [])
        egress_configurations = switch_config_data.get("egress_configurations", [])
        config_data = switch_config_data.get("config_data")

        # Create configurations in database with config_data already included
        session = self.get_session()
        
        with session.begin(subtransactions=True):
            for config in configurations:
                self._insert_qos_configuration(session=session, 
                                               sysname=config.get("sysname"), 
                                               switch_sn=config.get("switch_sn"), 
                                               forwarding_class=config.get("forwarding_class"), 
                                               local_priority=config.get("local_priority"), 
                                               scheduler=config.get("scheduler"), 
                                               mode=config.get("mode"), 
                                               weight=config.get("weight"), 
                                               guaranteed_rate=config.get("guaranteed_rate"), 
                                               config_data=config_data)
            
            for ingress in ingress_configurations:
                self._insert_qos_ingress_configuration(session=session, 
                                                       sysname=ingress.get("sysname"), 
                                                       switch_sn=ingress.get("switch_sn"), 
                                                       classifier=ingress.get("classifier"), 
                                                       trust_mode=ingress.get("trust_mode"), 
                                                       port=ingress.get("port"), 
                                                       forwarding_class=ingress.get("forwarding_class"), 
                                                       queue=ingress.get("queue"), 
                                                       is_all_ports=ingress.get("is_all_ports"), 
                                                       is_all_queues=ingress.get("is_all_queues"), 
                                                       config_data=config_data)
            
            for egress in egress_configurations:
                self._insert_qos_egress_configuration(session=session, 
                                                       sysname=egress.get("sysname"), 
                                                       switch_sn=egress.get("switch_sn"), 
                                                       scheduler_profile=egress.get("scheduler_profile"), 
                                                       scheduler=egress.get("scheduler"), 
                                                       port=egress.get("port"), 
                                                       local_priority=egress.get("local_priority"), 
                                                       forwarding_class=egress.get("forwarding_class"), 
                                                       is_all_ports=egress.get("is_all_ports"), 
                                                       config_data=config_data)
                

    def update_qos_configurations_with_config_data(self, switch_sn, update_data):
        """
        Service method to update QoS configurations with config_data already included
        Similar to save pattern - one-step update with complete data
        Args:
            switch_sn: str, Switch serial number
            update_data: dict, Configuration data including config_data
                {
                    "configurations": [...],
                    "ingress_configurations": [...],
                    "egress_configurations": [...],
                    "config_data": "json_string"  # Already includes deployment data
                }
        Returns:
            dict: Update operation results
        Raises:
            ValueError: When validation fails
        """
        if not switch_sn:
            raise ValueError("switch_sn is required")
        
        if not update_data:
            raise ValueError("No update data provided")
        
        # Use the existing update method but ensure config_data is included in each configuration
        configurations = update_data.get("configurations", [])
        ingress_configurations = update_data.get("ingress_configurations", [])
        egress_configurations = update_data.get("egress_configurations", [])
        config_data_str = update_data.get("config_data")
        
        # Add config_data to each configuration for consistency
        for config in configurations:
            config["config_data"] = config_data_str
        for config in ingress_configurations:
            config["config_data"] = config_data_str
        for config in egress_configurations:
            config["config_data"] = config_data_str
        
        # Prepare data for the existing update method
        prepared_update_data = {
            "configurations": configurations,
            "ingress_configurations": ingress_configurations,
            "egress_configurations": egress_configurations
        }
        
        # Execute bulk update through existing service layer method
        return self.update_qos_configurations(switch_sn, prepared_update_data)

    def update_qos_configurations(self, switch_sn, update_data):
        """
        Update QoS configurations for a specific switch (simplified version)
        Args:
            switch_sn: str, Switch serial number
            update_data: dict, Contains configurations, ingress_configurations, egress_configurations
        """
        session = self.get_session()
        
        configurations = update_data.get("configurations", [])
        ingress_configurations = update_data.get("ingress_configurations", [])
        egress_configurations = update_data.get("egress_configurations", [])
        
        with session.begin(subtransactions=True):
            # Process main configurations
            if configurations:
                existing_configs = session.query(QosConfiguration).filter_by(switch_sn=switch_sn).all()
                existing_map = {config.id: config for config in existing_configs}
                updated_ids = set()
                
                # Update/Create operations
                for config in configurations:
                    config_id = config.get("config_id")
                    if config_id and int(config_id) in existing_map:
                        # Update existing configuration
                        updated_ids.add(int(config_id))
                        main_config = existing_map[int(config_id)]
                        main_config.sysname = config.get("sysname")
                        main_config.forwarding_class = config.get("forwarding_class")
                        main_config.local_priority = config.get("local_priority")
                        main_config.scheduler = config.get("scheduler")
                        main_config.mode = config.get("mode")
                        main_config.weight = config.get("weight")
                        main_config.guaranteed_rate = config.get("guaranteed_rate")
                        main_config.config_data = config.get("config_data")
                    else:
                        # Create new configuration
                        new_config = QosConfiguration(
                            sysname=config.get("sysname"),
                            switch_sn=switch_sn,
                            forwarding_class=config.get("forwarding_class"),
                            local_priority=config.get("local_priority"),
                            scheduler=config.get("scheduler"),
                            mode=config.get("mode"),
                            weight=config.get("weight"),
                            guaranteed_rate=config.get("guaranteed_rate"),
                            config_data=config.get("config_data")
                        )
                        session.add(new_config)
                
                # Delete unused configurations
                for config_id in existing_map.keys():
                    if config_id not in updated_ids:
                        session.query(QosConfiguration).filter_by(id=config_id).delete()
            
            # Process ingress configurations
            if 'ingress_configurations' in update_data:
                existing_ingress = session.query(QosIngressConfiguration).filter_by(switch_sn=switch_sn).all()
                existing_map = {config.id: config for config in existing_ingress}
                updated_ids = set()

                # Update/Create operations
                for config in (ingress_configurations or []):
                    ingress_id = config.get("ingress_id")
                    if ingress_id and int(ingress_id) in existing_map:
                        # Update existing ingress configuration
                        updated_ids.add(int(ingress_id))
                        ingress_config = existing_map[int(ingress_id)]
                        ingress_config.sysname = config.get("sysname")
                        ingress_config.classifier = config.get("classifier")
                        ingress_config.trust_mode = config.get("trust_mode")
                        ingress_config.port = config.get("port")
                        ingress_config.forwarding_class = config.get("forwarding_class")
                        ingress_config.queue = config.get("queue")
                        ingress_config.is_all_ports = config.get("is_all_ports", False)
                        ingress_config.is_all_queues = config.get("is_all_queues", False)
                        ingress_config.config_data = config.get("config_data")
                    else:
                        # Create new ingress configuration
                        if ingress_configurations:
                            new_ingress = QosIngressConfiguration(
                                sysname=config.get("sysname"),
                                switch_sn=switch_sn,
                                classifier=config.get("classifier"),
                                trust_mode=config.get("trust_mode"),
                                port=config.get("port"),
                                forwarding_class=config.get("forwarding_class"),
                                queue=config.get("queue"),
                                is_all_ports=config.get("is_all_ports", False),
                                is_all_queues=config.get("is_all_queues", False),
                                config_data=config.get("config_data")
                            )
                            session.add(new_ingress)

                # Delete unused ingress configurations
                for config_id in existing_map.keys():
                    if config_id not in updated_ids:
                        session.query(QosIngressConfiguration).filter_by(id=config_id).delete()
            
            # Process egress configurations
            if 'egress_configurations' in update_data:
                existing_egress = session.query(QosEgressConfiguration).filter_by(switch_sn=switch_sn).all()
                existing_map = {config.id: config for config in existing_egress}
                updated_ids = set()

                # Update/Create operations
                for config in (egress_configurations or []):
                    egress_id = config.get("egress_id")
                    if egress_id and int(egress_id) in existing_map:
                        # Update existing egress configuration
                        updated_ids.add(int(egress_id))
                        egress_config = existing_map[int(egress_id)]
                        egress_config.sysname = config.get("sysname")
                        egress_config.scheduler_profile = config.get("scheduler_profile")
                        egress_config.scheduler = config.get("scheduler")
                        egress_config.port = config.get("port")
                        egress_config.forwarding_class = config.get("forwarding_class")
                        egress_config.local_priority = config.get("local_priority")
                        egress_config.is_all_ports = config.get("is_all_ports", False)
                        egress_config.config_data = config.get("config_data")
                    else:
                        # Create new egress configuration
                        if egress_configurations:
                            new_egress = QosEgressConfiguration(
                                sysname=config.get("sysname"),
                                switch_sn=switch_sn,
                                scheduler_profile=config.get("scheduler_profile"),
                                scheduler=config.get("scheduler"),
                                port=config.get("port"),
                                forwarding_class=config.get("forwarding_class"),
                                local_priority=config.get("local_priority"),
                                is_all_ports=config.get("is_all_ports", False),
                                config_data=config.get("config_data")
                            )
                            session.add(new_egress)

                # Delete unused egress configurations
                for config_id in existing_map.keys():
                    if config_id not in updated_ids:
                        session.query(QosEgressConfiguration).filter_by(id=config_id).delete()

    def get_qos_configs_list_data(self, request_data):
        """
        Get QoS configurations list data with tree structure and pagination (Service layer)
        Args:
            request_data: dict, Request parameters including pagination, search, and filters
        Returns:
            dict: Tree-structured data with pagination info
        """
        if not request_data:
            raise ValueError("No query parameters provided")
        
        session = self.get_session()
        
        # Import required models within the method to avoid circular imports
        from server.db.models.inventory import Switch, AssociationFabric, Fabric, ModelPhysicPort
        from sqlalchemy.orm import aliased
        from sqlalchemy import func, text, asc, desc
        
        # Associated table aliases
        SwitchAlias = aliased(Switch)
        AssocAlias = aliased(AssociationFabric)
        FabricAlias = aliased(Fabric)
        
        # Build base query with fabric join and port aggregation
        # Use subqueries to avoid duplicate JSON_ARRAYAGG calls
        ingress_ports_subquery = (
            session.query(
                QosIngressConfiguration.forwarding_class,
                QosIngressConfiguration.switch_sn,
                func.json_arrayagg(QosIngressConfiguration.port).label('ingress_ports')
            )
            .group_by(QosIngressConfiguration.forwarding_class, QosIngressConfiguration.switch_sn)
            .subquery()
        )
        
        egress_ports_subquery = (
            session.query(
                QosEgressConfiguration.forwarding_class,
                QosEgressConfiguration.switch_sn,
                func.json_arrayagg(QosEgressConfiguration.port).label('egress_ports')
            )
            .group_by(QosEgressConfiguration.forwarding_class, QosEgressConfiguration.switch_sn)
            .subquery()
        )
        
        # Handle custom sorting with window function
        sort_fields = request_data.get("sortFields", [])
        window_order_by = []
        has_sysname_sort = False
        sysname_order = ""
        
        if sort_fields:
            # Build order_by list for window function
            for field in sort_fields:
                field_name = field.get("field")
                field_order = field.get("order", "asc")
                
                if hasattr(QosConfiguration, field_name):
                    column = getattr(QosConfiguration, field_name)
                    
                    # Handle JSON field sorting by casting to Text
                    if field_name == "port" or field_name == "queue":
                        column = column.cast(Text)
                    
                    if field_order == "asc":
                        window_order_by.append(asc(column))
                    elif field_order == "desc":
                        window_order_by.append(desc(column))
                    
                    # Check if sysname is in sort fields
                    if field_name == "sysname":
                        has_sysname_sort = True
                        sysname_order = field_order

        pre_query = (
            session.query(
                QosConfiguration,
                SwitchAlias,
                AssocAlias,
                FabricAlias,
                # Ingress port aggregation from subquery
                ingress_ports_subquery.c.ingress_ports.label('ingress_aggregated_ports'),
                # Egress port aggregation from subquery
                egress_ports_subquery.c.egress_ports.label('egress_aggregated_ports'),
                # Switch port count for is_all_ports calculation
                func.count(ModelPhysicPort.id).label('total_port_count')
            )
            .outerjoin(SwitchAlias, QosConfiguration.switch_sn == SwitchAlias.sn)
            .outerjoin(AssocAlias, SwitchAlias.id == AssocAlias.switch_id)
            .outerjoin(FabricAlias, AssocAlias.fabric_id == FabricAlias.id)
            # Left join with ingress ports subquery
            .outerjoin(
                ingress_ports_subquery,
                (QosConfiguration.forwarding_class == ingress_ports_subquery.c.forwarding_class) &
                (QosConfiguration.switch_sn == ingress_ports_subquery.c.switch_sn)
            )
            # Left join with egress ports subquery
            .outerjoin(
                egress_ports_subquery,
                (QosConfiguration.forwarding_class == egress_ports_subquery.c.forwarding_class) &
                (QosConfiguration.switch_sn == egress_ports_subquery.c.switch_sn)
            )
            # Left join with physical ports for total count
            .outerjoin(
                ModelPhysicPort,
                SwitchAlias.platform_model == ModelPhysicPort.platform_name
            )
            .group_by(
                QosConfiguration.id,
                SwitchAlias.id,
                AssocAlias.id,
                FabricAlias.id
            )
        )
        
        # Add window function for sorting
        if sort_fields and window_order_by:
            pre_query = pre_query.add_columns(
                func.row_number().over(
                    partition_by=QosConfiguration.switch_sn,
                    order_by=window_order_by
                ).label('row_num')
            )
        else:
            # Default sorting by id if no custom sort fields provided
            pre_query = pre_query.add_columns(
                func.row_number().over(
                    partition_by=QosConfiguration.switch_sn,
                    order_by=QosConfiguration.id
                ).label('row_num')
            )
        
        # Apply different ordering based on whether sysname is in sort fields
        if has_sysname_sort:
            # For sysname sorting: maintain switch_sn grouping while sorting by sysname
            if sysname_order == "asc":
                pre_query = pre_query.order_by(QosConfiguration.sysname.asc(), QosConfiguration.switch_sn, text('row_num'))
            else:
                pre_query = pre_query.order_by(QosConfiguration.sysname.desc(), QosConfiguration.switch_sn, text('row_num'))
        else:
            # For non-sysname sorting: order by switch_sn, then row_num
            pre_query = pre_query.order_by(QosConfiguration.switch_sn, text('row_num'))
        
        # Add switch serial number filter
        switch_sn_list = request_data.get("switch_sn", [])
        if switch_sn_list:
            pre_query = pre_query.filter(SwitchAlias.sn.in_(switch_sn_list))
        
        # Get paginated results using reusable utility
        # Skip default sorting to avoid issues with JSON field sorting
        from server.util import utils
        page_num, page_size, total_count, query_result = utils.query_helper(
            QosConfiguration,
            pre_query=pre_query,
            data=request_data,
            skip_sort=True  # Skip default sorting to avoid JSON field sorting issues
        )
        
        if not query_result:
            return {
                "tree_data": [],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count
            }
        
        # Build tree structure directly from SQL results
        # Query result format: (config, switch, assoc, fabric, ingress_aggregated_ports, egress_aggregated_ports, total_port_count, row_num)
        tree_data = self._build_qos_tree_structure_from_sql_results(query_result)
        
        return {
            "tree_data": tree_data,
            "page": page_num,
            "pageSize": page_size,
            "total": total_count
        }
    
    def _build_qos_tree_structure_from_sql_results(self, query_result):
        """
        Build tree structure for QoS configurations directly from SQL results (ORM layer)
        Args:
            query_result: list, Query result tuples with all fields from SQL
        Returns:
            list: Tree-structured configuration data
        """
        tree_data = []
        current_switch = None
        switch_groups = {}
        
        for config, switch, assoc, fabric, ingress_aggregated_ports, egress_aggregated_ports, total_port_count, row_num in query_result:
            config_dict = self._serialize_config_with_fabric(config, switch, fabric)

            # Convert JSON strings to arrays for frontend consumption
            try:
                parsed_ingress = json.loads(ingress_aggregated_ports) if ingress_aggregated_ports else []
                # Flatten the nested arrays
                flattened_ingress = []
                if isinstance(parsed_ingress, list):
                    for port_array in parsed_ingress:
                        if isinstance(port_array, list):
                            flattened_ingress.extend(port_array)
                        else:
                            flattened_ingress.append(port_array)
                config_dict["ingress_ports"] = flattened_ingress
            except (json.JSONDecodeError, TypeError):
                config_dict["ingress_ports"] = []
            
            try:
                parsed_egress = json.loads(egress_aggregated_ports) if egress_aggregated_ports else []
                # Flatten the nested arrays
                flattened_egress = []
                if isinstance(parsed_egress, list):
                    for port_array in parsed_egress:
                        if isinstance(port_array, list):
                            flattened_egress.extend(port_array)
                        else:
                            flattened_egress.append(port_array)
                config_dict["egress_ports"] = flattened_egress
            except (json.JSONDecodeError, TypeError):
                config_dict["egress_ports"] = []
            
            # Calculate is_all_ports in Python
            ingress_port_count = len(self._flatten_json_arrays(ingress_aggregated_ports)) if ingress_aggregated_ports else 0
            egress_port_count = len(self._flatten_json_arrays(egress_aggregated_ports)) if egress_aggregated_ports else 0
            
            config_dict["is_ingress_all_ports"] = ingress_port_count == total_port_count and total_port_count > 0
            config_dict["is_egress_all_ports"] = egress_port_count == total_port_count and total_port_count > 0
            

            
            switch_sn = config_dict.get("switch_sn")
            
            # Build tree structure grouped by switch
            if switch_sn != current_switch:
                current_switch = switch_sn
                config_dict["children"] = []
                tree_data.append(config_dict)
                switch_groups[switch_sn] = config_dict
            else:
                config_dict.update({"sysname": ""})
                switch_groups.get(switch_sn, {}).get("children", []).append(config_dict)
        
        return tree_data
    
    def _build_qos_config_mapping(self, configs):
        """
        Build configuration mapping for QoS ingress/egress configs (ORM layer)
        Args:
            configs: list, List of configuration objects
        Returns:
            dict: Mapping of forwarding_class to configuration data and ports
        """
        config_map = {}
        
        for config in configs:
            forwarding_class = config.forwarding_class
            if forwarding_class not in config_map:
                config_map[forwarding_class] = {'configs': [], 'ports': set()}
            
            # Use make_dict() for consistent serialization
            config_dict = config.make_dict()
            config_map[forwarding_class]['configs'].append(config_dict)
            
            # Extract port information for is_all_ports calculation
            ports = config_dict.get('port', [])
            if isinstance(ports, list):
                config_map[forwarding_class]['ports'].update(ports)
            elif ports:
                config_map[forwarding_class]['ports'].add(ports)
        
        return config_map
    
    def _get_switch_port_counts(self, switch_sns):
        """
        Get port counts for switches for is_all_ports calculation (ORM layer)
        Args:
            switch_sns: list, List of switch serial numbers
        Returns:
            dict: Mapping of switch_sn to total port count
        """
        if not switch_sns:
            return {}
        
        from server.db.models.inventory import Switch as InventorySwitch, ModelPhysicPort, inven_db
        from sqlalchemy import func
        
        # Use inventory database session for port count query
        inven_session = inven_db.get_session()
        try:
            port_counts = (
                inven_session.query(
                    InventorySwitch.sn,
                    func.count(ModelPhysicPort.id).label('port_count')
                )
                .join(ModelPhysicPort, 
                      InventorySwitch.platform_model == ModelPhysicPort.platform_name)
                .filter(InventorySwitch.sn.in_(switch_sns))
                .group_by(InventorySwitch.sn, InventorySwitch.platform_model)
                .all()
            )
            return {sn: count for sn, count in port_counts}
        finally:
            inven_session.close()
    
    def _flatten_json_arrays(self, json_arrays):
        """
        Flatten JSON arrays of ports into a single set of unique ports (ORM layer)
        Args:
            json_arrays: str or list, JSON string from JSON_ARRAYAGG or list of JSON arrays
        Returns:
            set: Set of unique port values
        """
        if not json_arrays:
            return set()
        
        import json
        
        unique_ports = set()
        
        # Handle JSON string from JSON_ARRAYAGG
        if isinstance(json_arrays, str):
            try:
                # Parse JSON string to get the array of arrays
                parsed_arrays = json.loads(json_arrays)
                if isinstance(parsed_arrays, list):
                    for port_array in parsed_arrays:
                        if isinstance(port_array, list):
                            # Flatten the nested array
                            unique_ports.update(port_array)
                        elif port_array:
                            unique_ports.add(port_array)
            except (json.JSONDecodeError, TypeError):
                # If parsing fails, treat as single value
                unique_ports.add(json_arrays)
        # Handle list of arrays (original behavior)
        elif isinstance(json_arrays, list):
            for port_array in json_arrays:
                if isinstance(port_array, list):
                    unique_ports.update(port_array)
                elif port_array:
                    unique_ports.add(port_array)
        else:
            # Handle single value
            unique_ports.add(json_arrays)
        
        return unique_ports

    def get_qos_port_configs_list_data(self, request_data):
        """
        Get QoS port configurations list data with tree structure and pagination (Service layer)
        Args:
            request_data: dict, Request parameters including traffic_type, pagination, search, and filters
        Returns:
            dict: Tree-structured data with pagination info
        """
        if not request_data:
            raise ValueError("No query parameters provided")
        
        traffic_type = request_data.get("traffic_type")
        if not traffic_type:
            raise ValueError("No traffic type provided")
        
        if traffic_type not in ["ingress", "egress"]:
            raise ValueError("Invalid traffic type. Must be 'ingress' or 'egress'")
        
        session = self.get_session()
        
        # Import required models within the method to avoid circular imports
        from server.db.models.inventory import Switch
        from sqlalchemy.orm import aliased
        from sqlalchemy import func, text, asc, desc
        
        # Determine query class based on traffic type
        if traffic_type == "ingress":
            query_class = QosIngressConfiguration
        else:
            query_class = QosEgressConfiguration
        
        # Build base query with joins
        SwitchAlias = aliased(Switch)
        
        # Handle custom sorting with window function
        sort_fields = request_data.get("sortFields", [])
        window_order_by = []
        has_sysname_sort = False
        sysname_order = ""
        
        if sort_fields:
            # Build order_by list for window function
            for field in sort_fields:
                field_name = field.get("field")
                field_order = field.get("order", "asc")
                
                if hasattr(query_class, field_name):
                    column = getattr(query_class, field_name)
                    
                    # Handle JSON field sorting by casting to Text
                    if field_name == "port" or field_name == "queue":
                        column = column.cast(Text)
                    
                    if field_order == "asc":
                        window_order_by.append(asc(column))
                    elif field_order == "desc":
                        window_order_by.append(desc(column))
                    
                    # Check if sysname is in sort fields
                    if field_name == "sysname":
                        has_sysname_sort = True
                        sysname_order = field_order

        pre_query = (
            session.query(
                query_class,
                SwitchAlias,
            )
            .outerjoin(SwitchAlias, query_class.switch_sn == SwitchAlias.sn)
        )
        
        # Add window function for sorting
        if sort_fields and window_order_by:
            pre_query = pre_query.add_columns(
                func.row_number().over(
                    partition_by=query_class.switch_sn,
                    order_by=window_order_by
                ).label('row_num')
            )
        else:
            # Default sorting by id if no custom sort fields provided
            pre_query = pre_query.add_columns(
                func.row_number().over(
                    partition_by=query_class.switch_sn,
                    order_by=query_class.id
                ).label('row_num')
            )
        
        # Apply different ordering based on whether sysname is in sort fields
        if has_sysname_sort:
            # For sysname sorting: maintain switch_sn grouping while sorting by sysname
            if sysname_order == "asc":
                pre_query = pre_query.order_by(query_class.sysname.asc(), query_class.switch_sn, text('row_num'))
            else:
                pre_query = pre_query.order_by(query_class.sysname.desc(), query_class.switch_sn, text('row_num'))
        else:
            # For non-sysname sorting: order by switch_sn, then row_num
            pre_query = pre_query.order_by(query_class.switch_sn, text('row_num'))
        
        # Add switch serial number filter
        switch_sn_list = request_data.get("switch_sn", [])
        if switch_sn_list:
            pre_query = pre_query.filter(SwitchAlias.sn.in_(switch_sn_list))
        
        # Get paginated results using reusable utility
        # Skip default sorting to avoid issues with JSON field sorting
        from server.util import utils
        page_num, page_size, total_count, query_result = utils.query_helper(
            query_class,
            pre_query=pre_query,
            data=request_data,
            skip_sort=True  # Skip default sorting to avoid JSON field sorting issues
        )
        
        # Build tree structure using reusable method
        tree_data = self._build_qos_port_tree_structure(query_result)
        
        return {
            "tree_data": tree_data,
            "page": page_num,
            "pageSize": page_size,
            "total": total_count
        }
    
    def _build_qos_port_tree_structure(self, query_result):
        """
        Build tree structure for QoS port configurations (ORM layer)
        Args:
            query_result: list, Query result tuples (config, switch, row_num)
        Returns:
            list: Tree-structured configuration data grouped by switch
        """
        tree_data = []
        
        for config, switch, row_num in query_result:
            # Use make_dict() for consistent serialization
            config_dict = config.make_dict()
            tree_data.append(config_dict)
        
        return tree_data

    def get_pfc_forwarding_class_configs_list_data(self, request_data):

        session = self.get_session()
        
        # Import required modules
        from server.util import utils

        switch_sn = request_data.get("switch_sn")
        
        # Use reusable fabric join query builder
        pre_query, SwitchAlias, AssocAlias, FabricAlias = self._build_fabric_join_query(session, QosConfiguration)
        pre_query = pre_query.order_by(QosConfiguration.switch_sn, QosConfiguration.id)

        if switch_sn:
            pre_query = pre_query.filter(QosConfiguration.switch_sn.in_(switch_sn))

        # # Handle search conditions
        # search_fields_data = request_data.get("searchFields", {})
        # search_fields = search_fields_data.get("fields", [])
        # search_value = search_fields_data.get("value", "")
        
        # if "port" in search_fields and search_value:
        #     from sqlalchemy import Text, or_
        #     column = getattr(QosConfiguration, "port")
        #     search_condition = column.cast(Text).ilike(f'%{search_value}%')
        #     pre_query = pre_query.filter(or_(search_condition))

        # Get paginated results using query_helper
        page_num, page_size, total_count, query_result = utils.query_helper(
            QosConfiguration,
            pre_query=pre_query,
            data=request_data
        )

        # Build tree structure organized by switch_sn
        switch_groups = {}
        current_switch = None
        tree_data = []

        for config, switch, assoc, fabric in query_result:
            # Use reusable serialization method
            config_dict = self._serialize_config_with_fabric(config, switch, fabric)
            tree_data.append(config_dict)

        
        return {
            "data": tree_data,
            "page": page_num,
            "pageSize": page_size,
            "total": total_count
        }
    def _get_ingress_configurations_by_forwarding_class(self, switch_sn, forwarding_class):
        session = self.get_session()
        return session.query(QosIngressConfiguration).filter_by(switch_sn=switch_sn, forwarding_class=forwarding_class).all()
    
    def _get_egress_configurations_by_forwarding_class(self, switch_sn, forwarding_class):
        session = self.get_session()
        return session.query(QosEgressConfiguration).filter_by(switch_sn=switch_sn, forwarding_class=forwarding_class).all()

    def get_qos_configuration_for_deletion(self, config_id):
        """
        Get QoS configuration and switch information for deletion deployment.
        
        Args:
            config_id (int): QoS configuration ID
            
        Returns:
            dict: Result containing config data and switch object for deployment
        """
        session = self.get_session()
        
        # Get QoS configuration by ID using reusable method
        configuration = self._get_config_by_id_or_raise(session, QosConfiguration, config_id)
        ingress_configurations = self._get_ingress_configurations_by_forwarding_class(configuration.switch_sn, configuration.forwarding_class)
        egress_configurations = self._get_egress_configurations_by_forwarding_class(configuration.switch_sn, configuration.forwarding_class)
        
        # Use make_dict() for data serialization

        
        # Get switch information using reusable method
        switch = self._get_switch_by_sn_or_raise(session, configuration.switch_sn)
        
        # Return both config object and switch object for proper function calls
        return {
            "configuration": configuration,
            "ingress_configurations": ingress_configurations,
            "egress_configurations": egress_configurations,
            "switch": switch,      # Switch object for deploy_roce_configuration
        }

    def delete_qos_configuration_with_deployment(self, config_id):
        """
        Delete QoS configuration with deployment (Service layer)
        Business Rules:
        1. Deploy delete configuration first, then remove from database
        2. Validate configuration exists before deletion
        3. Ensure switch is available for deployment
        4. Handle configuration data properly for deletion
        
        Args:
            config_id: str, Configuration ID to delete
        Returns:
            dict: Deletion result information
        Raises:
            ValueError: When validation fails
            Exception: When deployment or database operation fails
        """
        if not config_id:
            raise ValueError("config_id is required")
        
        session = self.get_session()
        
        # Get configuration and validate existence
        config = session.query(QosConfiguration).filter_by(id=config_id).first()
        if not config:
            raise ValueError(f"Configuration with ID {config_id} not found")
        
        # Get switch information for deployment
        switch_sn = config.switch_sn
        switch = self._get_switch_by_sn_or_raise(session, switch_sn)
        
        # Validate configuration data exists
        # config_data = config.config_data
        # if not config_data:
        #     raise ValueError("Configuration data is empty, cannot perform deletion")
        
        return {
            "config": config,
            "switch": switch,
            "config_data": config.config_data if config.config_data else {},
            "config_id": config_id
        }
    
    def execute_qos_configuration_deletion(self, config_id):
        """
        Execute QoS configuration deletion from database with cascading delete (ORM layer)
        Business Rules:
        1. Delete related ingress configurations first
        2. Delete related egress configurations second
        3. Delete main configuration last
        4. All operations in transaction
        
        Args:
            config_id: str, Configuration ID to delete
        Returns:
            bool: True if deletion successful
        """
        session = self.get_session()
        
        with session.begin():
            config = session.query(QosConfiguration).filter_by(id=config_id).first()
            if not config:
                raise ValueError(f"Configuration with ID {config_id} not found")
            
            # 1. Delete related ingress configurations
            session.query(QosIngressConfiguration).filter_by(forwarding_class=config.forwarding_class, switch_sn=config.switch_sn).delete()
            
            # 2. Delete related egress configurations  
            session.query(QosEgressConfiguration).filter_by(forwarding_class=config.forwarding_class, switch_sn=config.switch_sn).delete()
            
            # 3. Delete main configuration
            session.query(QosConfiguration).filter_by(id=config_id).delete()
    
    def get_qos_config_by_switch_sn(self, switch_sn):
        session = self.get_session()
        return session.query(QosConfiguration).filter_by(switch_sn=switch_sn).first()

    def get_qos_configs_detail_by_switch_sn(self, switch_sn):
        """
        Get QoS configuration details for a specific switch (Service layer)
        Business Rules:
        1. Query all QoS configurations with fabric information
        2. Include related ingress and egress configurations
        3. Organize data for easy frontend consumption
        4. Include fabric information when available
        
        Args:
            switch_sn: str, Switch serial number
        Returns:
            list: Complete QoS configuration details for the switch
        Raises:
            ValueError: When switch_sn is not provided
        """
        if not switch_sn:
            raise ValueError("switch_sn is required")
        
        session = self.get_session()
        
        # Import required models within the method to avoid circular imports
        from server.db.models.inventory import Switch, AssociationFabric, Fabric
        from sqlalchemy.orm import aliased
        
        # Associated table aliases
        SwitchAlias = aliased(Switch)
        AssocAlias = aliased(AssociationFabric)
        FabricAlias = aliased(Fabric)

        # Query all QoS configurations for the switch with fabric information
        query_result = (
            session.query(
                QosConfiguration,
                SwitchAlias,
                AssocAlias,
                FabricAlias
            )
            .outerjoin(SwitchAlias, QosConfiguration.switch_sn == SwitchAlias.sn)
            .outerjoin(AssocAlias, SwitchAlias.id == AssocAlias.switch_id)
            .outerjoin(FabricAlias, AssocAlias.fabric_id == FabricAlias.id)
            .filter(QosConfiguration.switch_sn == switch_sn)
            .order_by(QosConfiguration.id)
            .all()
        )

        # Build configuration details list
        configs = []
        for config, switch, assoc, fabric in query_result:
            # Serialize main configuration with fabric info
            config_dict = self._serialize_qos_config_with_fabric(config, fabric)
            
            # Get related ingress configurations for this forwarding class
            config_dict["ingress_configurations"] = self._get_qos_ingress_configs_by_forwarding_class(switch_sn, config.forwarding_class)
            
            # Get related egress configurations for this forwarding class
            config_dict["egress_configurations"] = self._get_qos_egress_configs_by_forwarding_class(switch_sn, config.forwarding_class)
            
            configs.append(config_dict)

        return configs
    
    def _serialize_qos_config_with_fabric(self, config, fabric):
        """
        Serialize QoS configuration with fabric information (ORM layer)
        Args:
            config: QosConfiguration object
            fabric: Fabric object or None
        Returns:
            dict: Serialized configuration with fabric info
        """
        config_dict = config.make_dict()
        if fabric:
            config_dict["fabric"] = fabric.fabric_name
        else:
            config_dict["fabric"] = None
        return config_dict
    


    def bulk_update_qos_configurations(self, switch_sn, configurations):
        """
        Bulk update QoS configurations for a switch (Service layer)
        Business Rules:
        1. Process main configs, ingress configs, and egress configs separately
        2. Analyze existing vs incoming configurations to determine operations
        3. Return operation plans for deployment and database updates
        4. Deploy first, then update database (deploy-first pattern)
        
        Args:
            switch_sn: str, Switch serial number
            configurations: list, New configuration data
        Returns:
            dict: Operations plan for deletions, updates, and creations
        Raises:
            ValueError: When validation fails
        """
        if not switch_sn:
            raise ValueError("switch_sn is required")
        
        if not configurations:
            raise ValueError("No configurations provided")
        
        session = self.get_session()
        
        # Get switch for deployment
        switch = self._get_switch_by_sn_or_raise(session, switch_sn)
        
        # Get existing configurations
        existing_main_configs = session.query(QosConfiguration).filter_by(switch_sn=switch_sn).all()
        existing_ingress_configs = session.query(QosIngressConfiguration).filter_by(switch_sn=switch_sn).all()
        existing_egress_configs = session.query(QosEgressConfiguration).filter_by(switch_sn=switch_sn).all()
        
        existing_main_config_ids = {str(config.id) for config in existing_main_configs}
        existing_ingress_config_ids = {str(config.id) for config in existing_ingress_configs}
        existing_egress_config_ids = {str(config.id) for config in existing_egress_configs}
        
        # Analyze incoming configurations to determine operations
        operations = self._analyze_qos_bulk_operations(
            configurations, existing_main_config_ids, 
            existing_ingress_config_ids, existing_egress_config_ids
        )
        
        # Add metadata to operations
        operations.update({
            "switch": switch,
            "switch_sn": switch_sn,
            "existing_main_configs": {str(c.id): c for c in existing_main_configs},
            "existing_ingress_configs": {str(c.id): c for c in existing_ingress_configs},
            "existing_egress_configs": {str(c.id): c for c in existing_egress_configs}
        })
        
        return operations
    
    def _analyze_qos_bulk_operations(self, configurations, existing_main_ids, existing_ingress_ids, existing_egress_ids):
        """
        Analyze QoS configurations to determine bulk operations (ORM layer)
        Args:
            configurations: list, Incoming configurations
            existing_main_ids: set, Existing main configuration IDs
            existing_ingress_ids: set, Existing ingress configuration IDs
            existing_egress_ids: set, Existing egress configuration IDs
        Returns:
            dict: Operation plan with configs to delete, update, and create
        """
        # Track incoming configuration IDs
        incoming_main_ids = set()
        incoming_ingress_ids = set()
        incoming_egress_ids = set()
        
        # Separate configurations by operation type
        main_configs_to_update = []
        main_configs_to_create = []
        ingress_configs_to_update = []
        ingress_configs_to_create = []
        egress_configs_to_update = []
        egress_configs_to_create = []
        
        for config in configurations:
            main_config_id = config.get("config_id")
            
            # Process main configuration
            if main_config_id and str(main_config_id) in existing_main_ids:
                main_configs_to_update.append(config)
                incoming_main_ids.add(str(main_config_id))
            else:
                main_configs_to_create.append(config)
            
            # Process ingress configurations
            for ingress in config.get("ingress_configurations", []):
                ingress_id = ingress.get("ingress_id")
                ingress["main_config"] = config  # Add main config reference
                
                if ingress_id and str(ingress_id) in existing_ingress_ids:
                    ingress_configs_to_update.append(ingress)
                    incoming_ingress_ids.add(str(ingress_id))
                else:
                    ingress_configs_to_create.append(ingress)
            
            # Process egress configurations
            for egress in config.get("egress_configurations", []):
                egress_id = egress.get("egress_id")
                egress["main_config"] = config  # Add main config reference
                
                if egress_id and str(egress_id) in existing_egress_ids:
                    egress_configs_to_update.append(egress)
                    incoming_egress_ids.add(str(egress_id))
                else:
                    egress_configs_to_create.append(egress)
        
        # Determine configurations to delete
        main_ids_to_delete = existing_main_ids - incoming_main_ids
        ingress_ids_to_delete = existing_ingress_ids - incoming_ingress_ids
        egress_ids_to_delete = existing_egress_ids - incoming_egress_ids
        
        return {
            "main_ids_to_delete": list(main_ids_to_delete),
            "ingress_ids_to_delete": list(ingress_ids_to_delete),
            "egress_ids_to_delete": list(egress_ids_to_delete),
            "main_configs_to_update": main_configs_to_update,
            "main_configs_to_create": main_configs_to_create,
            "ingress_configs_to_update": ingress_configs_to_update,
            "ingress_configs_to_create": ingress_configs_to_create,
            "egress_configs_to_update": egress_configs_to_update,
            "egress_configs_to_create": egress_configs_to_create
        }
    
    def execute_qos_bulk_operations(self, operations_data):
        """
        Execute QoS bulk operations in database after successful deployment (ORM layer)
        Args:
            operations_data: dict, Operations data including all updates, creates, and deletes
        Returns:
            bool: True if all operations successful
        """
        session = self.get_session()
        
        with session.begin(subtransactions=True):
            # 1. Execute delete operations
            for main_id in operations_data.get("main_ids_to_delete", []):
                session.query(QosConfiguration).filter_by(id=main_id).delete()
            
            for ingress_id in operations_data.get("ingress_ids_to_delete", []):
                session.query(QosIngressConfiguration).filter_by(id=ingress_id).delete()
            
            for egress_id in operations_data.get("egress_ids_to_delete", []):
                session.query(QosEgressConfiguration).filter_by(id=egress_id).delete()
            
            # 2. Execute update operations
            for update_op in operations_data.get("main_update_operations", []):
                config_id = update_op.get("config_id")
                updates = update_op.get("updates")
                self._update_qos_configuration(config_id, updates)
            
            for update_op in operations_data.get("ingress_update_operations", []):
                config_id = update_op.get("config_id")
                updates = update_op.get("updates")
                session.query(QosIngressConfiguration).filter_by(id=config_id).update(updates)
            
            for update_op in operations_data.get("egress_update_operations", []):
                config_id = update_op.get("config_id")
                updates = update_op.get("updates")
                session.query(QosEgressConfiguration).filter_by(id=config_id).update(updates)
            
            # 3. Execute create operations
            for create_op in operations_data.get("main_create_operations", []):
                config = create_op.get("config")
                main_config_id = self.add_qos_configuration(
                    qos_config=config,
                    ingress_configs=[],
                    egress_configs=[]
                )
                # Store the created ID for reference
                create_op["created_id"] = main_config_id
            
            for create_op in operations_data.get("ingress_create_operations", []):
                config_data = create_op.get("config_data")
                self._insert_qos_ingress_configuration(**config_data)
            
            for create_op in operations_data.get("egress_create_operations", []):
                config_data = create_op.get("config_data")
                self._insert_qos_egress_configuration(**config_data)

    def check_qos_forwarding_class_exists(self, switch_sn, forwarding_class, exclude_config_id=None):
        """
        Check if QoS forwarding class already exists on a switch (Service layer)
        Args:
            switch_sn: str, Switch serial number
            forwarding_class: str, Forwarding class name
            exclude_config_id: int, Optional config ID to exclude from check (for updates)
        Returns:
            bool: True if forwarding class exists, False otherwise
        """
        if not switch_sn or not forwarding_class:
            return False
        
        session = self.get_session()
        query = session.query(QosConfiguration).filter_by(
            switch_sn=switch_sn,
            forwarding_class=forwarding_class
        )
        
        if exclude_config_id:
            query = query.filter(QosConfiguration.id != exclude_config_id)
        
        return query.first() is not None
    
    def check_qos_classifier_forwarding_class_exists(self, switch_sn, classifier, forwarding_class, exclude_config_id=None):
        """
        Check if QoS ingress classifier and forwarding_class combination already exists on a switch (Service layer)
        Note: One classifier can correspond to multiple forwarding_classes (one-to-many relationship)
        Args:
            switch_sn: str, Switch serial number
            classifier: str, Classifier name
            forwarding_class: str, Forwarding class name
            exclude_config_id: int, Optional config ID to exclude from check (for updates)
        Returns:
            bool: True if classifier-forwarding_class combination exists, False otherwise
        """
        if not switch_sn or not classifier or not forwarding_class:
            return False
        
        session = self.get_session()
        query = session.query(QosIngressConfiguration).filter_by(
            switch_sn=switch_sn,
            classifier=classifier,
            forwarding_class=forwarding_class
        )
        
        if exclude_config_id:
            query = query.filter(QosIngressConfiguration.id != exclude_config_id)
        
        return query.first() is not None
    
    def check_qos_scheduler_profile_exists(self, switch_sn, scheduler_profile, exclude_config_id=None):
        """
        Check if QoS egress scheduler profile already exists on a switch (Service layer)
        Args:
            switch_sn: str, Switch serial number
            scheduler_profile: str, Scheduler profile name
            exclude_config_id: int, Optional config ID to exclude from check (for updates)
        Returns:
            bool: True if scheduler profile exists, False otherwise
        """
        if not switch_sn or not scheduler_profile:
            return False
        
        session = self.get_session()
        query = session.query(QosEgressConfiguration).filter_by(
            switch_sn=switch_sn,
            scheduler_profile=scheduler_profile
        )
        
        if exclude_config_id:
            query = query.filter(QosEgressConfiguration.id != exclude_config_id)
        
        return query.first() is not None

    def check_qos_scheduler_profile_forwarding_class_exists(self, switch_sn, scheduler_profile, forwarding_class, scheduler, exclude_config_id=None):
        """
        Check if QoS egress scheduler_profile and forwarding_class combination already exists on a switch (Service layer)
        Note: In the same scheduler_profile, one forwarding_class can only correspond to one scheduler
        Args:
            switch_sn: str, Switch serial number
            scheduler_profile: str, Scheduler profile name
            forwarding_class: str, Forwarding class name
            scheduler: str, Scheduler name
            exclude_config_id: int, Optional config ID to exclude from check (for updates)
        Returns:
            bool: True if scheduler_profile-forwarding_class-scheduler combination exists, False otherwise
        """
        if not switch_sn or not scheduler_profile or not forwarding_class or not scheduler:
            return False
        
        session = self.get_session()
        query = session.query(QosEgressConfiguration).filter_by(
            switch_sn=switch_sn,
            scheduler_profile=scheduler_profile,
            forwarding_class=forwarding_class,
            scheduler=scheduler
        )
        
        if exclude_config_id:
            query = query.filter(QosEgressConfiguration.id != exclude_config_id)
        
        return query.first() is not None

roce_db = RoceDBCommon()

