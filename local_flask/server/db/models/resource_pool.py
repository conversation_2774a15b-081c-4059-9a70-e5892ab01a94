import ipaddress
from flask import jsonify, request
from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    ForeignKey,
    Boolean,
    DateTime,
    Enum,
    JSON,
    Table,
    and_
)
from sqlalchemy.orm import relationship, exc
from abc import ABC, abstractmethod

from server.util import utils

from server.db.db_common import DBCommon
from server.db.models.base import Base
from server.db.models import inventory


def is_ranges_conflict(ranges):
    if len(ranges) == 1:
        return False
    for i in range(len(ranges)):
        if ranges[i]['start'] > ranges[i]['end']:
            return True
    for i in range(len(ranges)):
        for j in range(i + 1, len(ranges)):
            if ranges[i]['start'] <= ranges[j]['start'] <= ranges[i]['end'] or ranges[i]['start'] <= ranges[j]['end'] <= ranges[i]['end'] or (ranges[j]['start'] < ranges[i]['start'] and ranges[j]['end'] > ranges[i]['end']):
                return True
    return False


def is_resource_pool_in_use(resource_pool_ranges):
    for resource_range in resource_pool_ranges:
        if resource_range.is_in_use:
            return True
    return False


def is_value_in_ranges(value, all_ranges_list):
    return any(start <= value <= end for start, end, _ in all_ranges_list)


# Vni start
class ResourcePoolVni(Base):
    __tablename__ = "resource_pool_vni"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), index=True, unique=True, nullable=False)
    resource_pool_ranges = relationship("ResourcePoolVniRanges", backref="resource_pool_vni", cascade="all, delete-orphan", uselist=True)
    use = Column(Enum("default", "reserve"), default="default", nullable=False)

class ResourcePoolVniRanges(Base):
    __tablename__ = "resource_pool_vni_ranges"
    id = Column(Integer, primary_key=True, autoincrement=True)
    start_value = Column(Integer, nullable=False)
    end_value = Column(Integer, nullable=False)
    resource_pool_id = Column("resource_pool_vni_id", Integer, ForeignKey("resource_pool_vni.id", ondelete='CASCADE', onupdate='CASCADE'))
    is_in_use = Column(Boolean, default=False)
    resource_pool_use_detail = relationship("ResourcePoolVniUseDetail", backref="resource_pool_vni_ranges", cascade="all, delete-orphan", uselist=True)


class ResourcePoolVniUseDetail(Base):
    __tablename__ = "resource_pool_vni_use_detail"
    id = Column(Integer, primary_key=True, autoincrement=True)
    value = Column(Integer, nullable=False)
    resource_pool_ranges_id = Column("resource_pool_vni_ranges_id", Integer, ForeignKey("resource_pool_vni_ranges.id", ondelete='CASCADE', onupdate='CASCADE'))


# Asn start
class ResourcePoolAsn(Base):
    __tablename__ = "resource_pool_asn"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), index=True, unique=True, nullable=False)
    resource_pool_ranges = relationship("ResourcePoolAsnRanges", backref="resource_pool_asn", cascade="all, delete-orphan", uselist=True)


class ResourcePoolAsnRanges(Base):
    __tablename__ = "resource_pool_asn_ranges"
    id = Column(Integer, primary_key=True, autoincrement=True)
    start_value = Column(Integer, nullable=False)
    end_value = Column(Integer, nullable=False)
    resource_pool_id = Column("resource_pool_asn_id", Integer, ForeignKey("resource_pool_asn.id", ondelete='CASCADE', onupdate='CASCADE'))
    is_in_use = Column(Boolean, default=False)
    resource_pool_use_detail = relationship("ResourcePoolAsnUseDetail", backref="resource_pool_asn_ranges", cascade="all, delete-orphan", uselist=True)


class ResourcePoolAsnUseDetail(Base):
    __tablename__ = "resource_pool_asn_use_detail"
    id = Column(Integer, primary_key=True, autoincrement=True)
    value = Column(Integer, nullable=False)
    resource_pool_ranges_id = Column("resource_pool_asn_ranges_id", Integer, ForeignKey("resource_pool_asn_ranges.id", ondelete='CASCADE', onupdate='CASCADE'))


# Area start
class ResourcePoolArea(Base):
    __tablename__ = "resource_pool_area"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), index=True, unique=True, nullable=False)
    resource_pool_ranges = relationship("ResourcePoolAreaRanges", backref="resource_pool_area", cascade="all, delete-orphan", uselist=True)


class ResourcePoolAreaRanges(Base):
    __tablename__ = "resource_pool_area_ranges"
    id = Column(Integer, primary_key=True, autoincrement=True)
    start_value = Column(Integer, nullable=False)
    end_value = Column(Integer, nullable=False)
    resource_pool_id = Column("resource_pool_area_id", Integer, ForeignKey("resource_pool_area.id", ondelete='CASCADE', onupdate='CASCADE'))
    is_in_use = Column(Boolean, default=False)
    resource_pool_use_detail = relationship("ResourcePoolAreaUseDetail", backref="resource_pool_area_ranges", cascade="all, delete-orphan", uselist=True)


class ResourcePoolAreaUseDetail(Base):
    __tablename__ = "resource_pool_area_use_detail"
    id = Column(Integer, primary_key=True, autoincrement=True)
    value = Column(Integer, nullable=False)
    resource_pool_ranges_id = Column("resource_pool_area_ranges_id", Integer, ForeignKey("resource_pool_area_ranges.id", ondelete='CASCADE', onupdate='CASCADE'))
# Area end


class ResourcePoolIp(Base):
    __tablename__ = "resource_pool_ip"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), index=True, unique=True, nullable=False)
    resource_pool_ranges = relationship("ResourcePoolIpRanges", backref="resource_pool_ip", cascade="all, delete-orphan", uselist=True)


class ResourcePoolIpRanges(Base):
    __tablename__ = "resource_pool_ip_ranges"
    id = Column(Integer, primary_key=True, autoincrement=True)
    start_value = Column(Integer, nullable=False)
    end_value = Column(Integer, nullable=False)
    resource_pool_id = Column("resource_pool_ip_id", Integer, ForeignKey("resource_pool_ip.id", ondelete='CASCADE', onupdate='CASCADE'))
    is_in_use = Column(Boolean, default=False)
    resource_pool_use_detail = relationship("ResourcePoolIpUseDetail", backref="resource_pool_ip_ranges", cascade="all, delete-orphan", uselist=True)


class ResourcePoolIpUseDetail(Base):
    __tablename__ = "resource_pool_ip_use_detail"
    id = Column(Integer, primary_key=True, autoincrement=True)
    value = Column(Integer, nullable=False)
    resource_pool_ranges_id = Column("resource_pool_ip_ranges_id", Integer, ForeignKey("resource_pool_ip_ranges.id", ondelete='CASCADE', onupdate='CASCADE'))


class ResourcePoolVlanDomain(Base):
    __tablename__ = "resource_pool_vlan_domain"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), index=True, nullable=False)
    fabric_id = Column(Integer, ForeignKey('fabric.id', ondelete='CASCADE', onupdate='CASCADE'))
    device_type = Column(String(32), default="server") # server || service || border || service border
    

class ResourcePoolBridgeDomain(Base):
    __tablename__ = "resource_pool_bridge_domain"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), index=True, nullable=False)
    vlan_domain_id = Column(Integer, ForeignKey('resource_pool_vlan_domain.id', ondelete='CASCADE', onupdate='CASCADE'))
    resource_pool_ranges = relationship("ResourcePoolBridgeDomainRanges", backref="resource_pool_bridge_domain", cascade="all, delete-orphan", uselist=True)


class ResourcePoolBridgeDomainRanges(Base):
    __tablename__ = "resource_pool_bridge_domain_ranges"
    id = Column(Integer, primary_key=True, autoincrement=True)
    start_value = Column(Integer, nullable=False)
    end_value = Column(Integer, nullable=False)
    resource_pool_id = Column("resource_pool_bridge_domain_id", Integer, ForeignKey("resource_pool_bridge_domain.id", ondelete='CASCADE', onupdate='CASCADE'))
    is_in_use = Column(Boolean, default=False)
    resource_pool_use_detail = relationship("ResourcePoolBridgeDomainUseDetail", backref="resource_pool_bridge_domain_ranges", cascade="all, delete-orphan", uselist=True)


class ResourcePoolBridgeDomainUseDetail(Base):
    __tablename__ = "resource_pool_bridge_domain_use_detail"
    id = Column(Integer, primary_key=True, autoincrement=True)
    value = Column(Integer, nullable=False)
    resource_pool_ranges_id = Column("resource_pool_bridge_domain_ranges_id", Integer, ForeignKey("resource_pool_bridge_domain_ranges.id", ondelete='CASCADE', onupdate='CASCADE'))
    ls_id = Column(Integer, ForeignKey("dc_logical_switch.id", ondelete='CASCADE', onupdate='CASCADE'), nullable=False)

class ResourcePoolVRFVlan(Base):
    __tablename__ = "resource_pool_vrf_vlan"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), index=True, nullable=False)
    vlan_domain_id = Column(Integer, ForeignKey('resource_pool_vlan_domain.id', ondelete='CASCADE', onupdate='CASCADE'))
    resource_pool_ranges = relationship("ResourcePoolVRFVlanRanges", backref="resource_pool_vrf_vlan", cascade="all, delete-orphan", uselist=True)


class ResourcePoolVRFVlanRanges(Base):
    __tablename__ = "resource_pool_vrf_vlan_ranges"
    id = Column(Integer, primary_key=True, autoincrement=True)
    start_value = Column(Integer, nullable=False)
    end_value = Column(Integer, nullable=False)
    resource_pool_id = Column("resource_pool_vrf_vlan_id", Integer, ForeignKey("resource_pool_vrf_vlan.id", ondelete='CASCADE', onupdate='CASCADE'))
    is_in_use = Column(Boolean, default=False)
    resource_pool_use_detail = relationship("ResourcePoolVRFVlanUseDetail", backref="resource_pool_vrf_vlan_ranges", cascade="all, delete-orphan", uselist=True)


class ResourcePoolVRFVlanUseDetail(Base):
    __tablename__ = "resource_pool_vrf_vlan_use_detail"
    id = Column(Integer, primary_key=True, autoincrement=True)
    value = Column(Integer, nullable=False)
    resource_pool_ranges_id = Column("resource_pool_vrf_vlan_ranges_id", Integer, ForeignKey("resource_pool_vrf_vlan_ranges.id", ondelete='CASCADE', onupdate='CASCADE'))
    lr_id = Column(Integer, ForeignKey("dc_logical_router.id", ondelete='CASCADE', onupdate='CASCADE'), nullable=False)

class ResourcePoolInterface(object):
    @abstractmethod
    def edit_resource_pool(self, pool_id, new_name, ranges):
        pass

    @abstractmethod
    def add_resource_pool(self, name, ranges):
        pass

    @abstractmethod
    def clone_resource_pool(self, pool_id, new_name):
        pass

    @abstractmethod
    def query_all_resource_pool(self):
        pass

    @abstractmethod
    def query_resource_pool_ranges_detail(self, pool_name):
        pass

    @abstractmethod
    def query_resource_pool_by_id(self, to_be_query_record_id):
        pass

    @abstractmethod
    def delete_resource_pool_by_id(self, to_be_deleted_record_id):
        pass

    @abstractmethod
    def generate_resource_from_pool(self, pool_id, count):
        pass


class ResourcePoolBase(ResourcePoolInterface, DBCommon, ABC):

    def __init__(self):
        self.pool_class = None
        self.pool_ranges_class = None
        self.pool_use_detail_class = None

    @staticmethod
    def _check_is_range_start_end_value_valid(start_value, end_value, resource_range_id=""):
        start_value_greater_than_end_value_error_msg = f"Resource pool range id {resource_range_id} start value {start_value} must less than end value {end_value}" if resource_range_id else f"Resource pool range start value {start_value} must less than end value {end_value}"
        if start_value > end_value:
            raise ValueError(start_value_greater_than_end_value_error_msg)
        start_value_less_than_zero_error_msg = f"Resource pool range id {resource_range_id} start value {start_value} must greater than 0" if resource_range_id else f"Resource pool range start value {start_value} must greater than 0"
        if start_value <= 0:
            raise ValueError(start_value_less_than_zero_error_msg)
        start_value_greater_than_max_error_msg = f"Resource pool range id {resource_range_id} start value {start_value} must less than 4294967295" if resource_range_id else f"Resource pool range start value {start_value} must less than 4294967295"
        if start_value >= 4294967295:
            raise ValueError(start_value_greater_than_max_error_msg)
        end_value_less_than_zero_error_msg = f"Resource pool range id {resource_range_id} end value {end_value} must greater than 0" if resource_range_id else f"Resource pool range end value {end_value} must greater than 0"
        if end_value <= 0:
            raise ValueError(end_value_less_than_zero_error_msg)
        end_value_greater_than_max_error_msg = f"Resource pool range id {resource_range_id} end value {end_value} must less than 4294967295" if resource_range_id else f"Resource pool range end value {end_value} must less than 4294967295"
        if end_value >= 4294967295:
            raise ValueError(end_value_greater_than_max_error_msg)

    def edit_resource_pool(self, pool_id, new_name, ranges, session=None, **kwargs):
        if not session:
            session = self.get_session()
        try:
            with session.begin(subtransactions=True):
                if self.pool_class == ResourcePoolVni:
                    new_use = kwargs.get('use', None)
                    self.query_resource_pool_by_id(pool_id).update({"use": new_use, "name": new_name})
                else:
                    self.query_resource_pool_by_id(pool_id).update({"name": new_name})
                if ranges.get('delete') != []:
                    for resource_range_delete in ranges.get('delete', []):
                        query_range = session.query(self.pool_ranges_class).filter(self.pool_ranges_class.id == resource_range_delete['rangeId'])
                        range_data = query_range.first()
                        if not range_data:
                            raise ValueError(f"Resource pool range not found")
                        if range_data.is_in_use:
                            raise ValueError(f"Resource pool range is in use, cannot delete")
                        query_range.delete()
                    session.flush()
                if ranges.get('modify')!= []:
                    for resource_range_modify in ranges.get('modify', []):
                        if resource_range_modify.get('rangeId', None):
                            query_range = session.query(self.pool_ranges_class).filter(self.pool_ranges_class.id == resource_range_modify['rangeId'])
                            range_data = query_range.first()
                            if range_data:
                                self._check_is_range_start_end_value_valid(resource_range_modify['start'], resource_range_modify['end'], resource_range_modify['rangeId'])
                                if range_data.is_in_use and (range_data.start_value < resource_range_modify['start'] or range_data.end_value > resource_range_modify['end']):
                                    raise ValueError(f"The Resource pool range in Used status can be expanded but can't be narrowed down.")
                                query_range.update({"start_value": resource_range_modify['start'], "end_value": resource_range_modify['end']})
                            else:
                                raise ValueError(f"Resource pool range not found")
                    session.flush()
                if ranges.get('add')!= []:
                    for resource_range_add in ranges.get('add', []):
                        self._check_is_range_start_end_value_valid(resource_range_add['start'], resource_range_add['end'])
                        new_range = self.pool_ranges_class(start_value=resource_range_add['start'], end_value=resource_range_add['end'], resource_pool_id=pool_id)
                        session.add(new_range)
                    session.flush()
                return True
        except Exception as e:
            session.rollback()
            raise e

    def add_resource_pool(self, name, ranges, session=None, **kwargs):
        # add asn resource pool and asn ranges
        if not session:
            session = self.get_session()
        try:
            with session.begin(subtransactions=True):
                new_pool = self.pool_class(name=name, **kwargs)
                session.add(new_pool)
                session.flush()
                for resource_range in ranges:
                    new_range = self.pool_ranges_class(start_value=resource_range['start'], end_value=resource_range['end'], resource_pool_id=new_pool.id)
                    session.add(new_range)
                session.flush()
                return True
        except Exception as e:
            session.rollback()
            return False

    def clone_resource_pool(self, pool_id, new_name):
        session = self.get_session()
        try:
            with session.begin():
                pool_query = self.query_resource_pool_by_id(pool_id)
                pool_data = pool_query.first()
                if not pool_data:
                    raise ValueError(f"Resource pool id {pool_id} not found")
                new_pool = self.pool_class(name=new_name)
                session.add(new_pool)
                session.flush()
                for resource_range in pool_data.resource_pool_ranges:
                    new_range = self.pool_ranges_class(start_value=resource_range.start_value, end_value=resource_range.end_value, resource_pool_id=new_pool.id)
                    session.add(new_range)
                session.flush()
                return True
        except Exception as e:
            session.rollback()
            raise e

    def query_all_resource_pool(self):
        data = request.get_json()
        sort_fields = data.get("sortFields", [])
        sort_field = sort_fields[0].get("field") if sort_fields else "name"
        sort_order = sort_fields[0].get("order") if sort_fields else "asc"
        if sort_field in ["used_count", "ranges_count"]:
            data["sortFields"] = []

        pre_query = None
        is_in_use_list = list(filter(lambda x: True if x['field'] == 'is_in_use' else False, data['filterFields']))
        if is_in_use_list:
            data['filterFields'].remove(is_in_use_list[0])
            is_in_use = is_in_use_list[0].get('filters')[0].get('value', None) if is_in_use_list[0].get('filters') else None
            query_in_use = self.get_session().query(self.pool_class).join(self.pool_ranges_class, self.pool_class.id == self.pool_ranges_class.resource_pool_id).filter(self.pool_ranges_class.is_in_use == True).all()
            in_use_pool = [pool.id for pool in query_in_use]
            if is_in_use == 'used':
                pre_query = self.get_session().query(self.pool_class).filter(self.pool_class.id.in_(in_use_pool))
            elif is_in_use == 'unused':
                pre_query = self.get_session().query(self.pool_class).filter(self.pool_class.id.notin_(in_use_pool))
        
        page_num, page_size, total_count, query_obj = utils.query_helper(self.pool_class, pre_query, data=data)

        response_data = []
        for pool in query_obj:
            pool_info = {
            "id": pool.id,
            "name": pool.name,
            "is_in_use": is_resource_pool_in_use(pool.resource_pool_ranges),
            "ranges": [{
                "id": pool_range.id,
                "start_value": pool_range.start_value,
                "end_value": pool_range.end_value,
                "used_count": len(pool_range.resource_pool_use_detail),
                "ranges_count": pool_range.end_value - pool_range.start_value + 1,
                "is_in_use": pool_range.is_in_use
            } for pool_range in pool.resource_pool_ranges],
            "used_count": sum(len(pool_range.resource_pool_use_detail) for pool_range in pool.resource_pool_ranges),
            "ranges_count": sum(pool_range.end_value - pool_range.start_value + 1 for pool_range in pool.resource_pool_ranges)
            }
            if hasattr(pool, "use"):
                pool_info["use"] = pool.use
                db_session = inventory.inven_db.get_session()
                fabric_ids = list(set(map(lambda x: x.fabric_id, db_session.query(inventory.FabricVniMapping).filter(inventory.FabricVniMapping.vni_id == pool.id).all())))
                pool_info["fabric"] = [{
                    "id": fabric.id,
                    "name": fabric.fabric_name
                } for fabric in db_session.query(inventory.Fabric).filter(inventory.Fabric.id.in_(fabric_ids)).all()]
            response_data.append(pool_info)

        if sort_field in ["used_count", "ranges_count"]:
            response_data.sort(key=lambda x: x[sort_field], reverse=(sort_order == "desc"))

        response = {
            "data": response_data,
            "page": page_num,
            "pageSize": page_size,
            "total": total_count,
            "status": 200
        }
        return jsonify(response)
    
    def get_resource_pool_info_by_id(self, pool_ids, session=None):
        if not session:
            session = self.get_session()     
        query_obj = session.query(self.pool_class).filter(self.pool_class.id.in_(pool_ids)) \
                                      .join(self.pool_ranges_class, self.pool_class.id == self.pool_ranges_class.resource_pool_id)

        response_data = {pool.id: {
            "id": pool.id,
            "name": pool.name,
            "is_in_use": is_resource_pool_in_use(pool.resource_pool_ranges),
            "ranges": [{
                "id": pool_range.id,
                "start_value": pool_range.start_value,
                "end_value": pool_range.end_value,
                "used_count": len(pool_range.resource_pool_use_detail),
                "ranges_count": pool_range.end_value - pool_range.start_value + 1,
                "is_in_use": pool_range.is_in_use
            } for pool_range in pool.resource_pool_ranges],
            "used_count": sum(len(pool_range.resource_pool_use_detail) for pool_range in pool.resource_pool_ranges),
            "ranges_count": sum(pool_range.end_value - pool_range.start_value + 1 for pool_range in pool.resource_pool_ranges)
        } for pool in query_obj}
        
        return response_data

    def query_resource_pool_data_list(self):
        session = self.get_session()
        try:
            pool_data = session.query(self.pool_class).all()
            return pool_data
        except exc.NoResultFound:
            return None

    def query_resource_pool_by_id(self, to_be_deleted_record_id, session=None):
        if not session:
            session = self.get_session()
        query_pool = session.query(self.pool_class).filter(self.pool_class.id == to_be_deleted_record_id)
        return query_pool

    def query_resource_pool_by_name(self, pool_name):
        session = self.get_session()
        query_pool = session.query(self.pool_class).filter(self.pool_class.name == pool_name)
        return query_pool

    def delete_resource_pool_by_id(self, to_be_deleted_record_id):
        target_pool_query = self.query_resource_pool_by_id(to_be_deleted_record_id)
        target_pool = target_pool_query.first()
        if is_resource_pool_in_use(target_pool.resource_pool_ranges):
            return jsonify({"status": 500, "msg": f"Resource pool id {to_be_deleted_record_id} is in use"})
        if target_pool is None:
            return jsonify({"status": 500, "msg": f"Resource pool id {to_be_deleted_record_id} not found"})
        return True if target_pool_query.delete() != 0 else False

    def query_resource_pool_ranges_detail(self, pool_name):
        session = self.get_session()
        query_pool = session.query(self.pool_class).filter(self.pool_class.name == pool_name).first()
        return query_pool

    def delete_used_records_from_pool(self, records, session=None):
        """
        :param records: list of dict, each dict contains pool_range_id and record_value
        """
        if not session:
            session = self.get_session()
        records_id_and_records_mapping = {}
        for record in records:
            pool_range_id = record.get('pool_range_id', None)
            record_value = record.get('record_value', None)
            if pool_range_id is None or record_value is None:
                continue
            if records_id_and_records_mapping.get(pool_range_id, None) is None:
                records_id_and_records_mapping[pool_range_id] = [record_value]
            else:
                records_id_and_records_mapping[pool_range_id].append(record_value)
            records_id_and_records_mapping.setdefault(pool_range_id, []).append(record_value)
        with session.begin(subtransactions=True):
            for record_mapping_key, record_mapping_values in records_id_and_records_mapping.items():
                session.query(self.pool_use_detail_class).filter(self.pool_use_detail_class.value.in_(record_mapping_values), self.pool_use_detail_class.resource_pool_ranges_id == record_mapping_key).delete()
                session.flush()
                if session.query(self.pool_use_detail_class).filter(self.pool_use_detail_class.resource_pool_ranges_id == record_mapping_key).count() == 0:
                    session.query(self.pool_ranges_class).filter(
                        self.pool_ranges_class.id == record_mapping_key).update({"is_in_use": False})
        return True

    # todo for test
    def delete_first_n_used_record(self, pool_id, record_num):
        session = self.get_session()
        pool = session.query(self.pool_class).filter(self.pool_class.id == pool_id).first()
        records = []
        if not pool:
            raise ValueError(f"Resource pool id {pool_id} not found")
        for resource_range in pool.resource_pool_ranges:
            for use_detail in resource_range.resource_pool_use_detail:
                records.append({"pool_range_id": resource_range.id, "record_value": use_detail.value})
        return self.delete_used_records_from_pool(records[:record_num])
    
    def delete_record_by_value(self, pool_id, values, session=None):
        if not session:
            session = self.get_session()
        pool = session.query(self.pool_class).filter(self.pool_class.id == pool_id).first()
        records = []
        ranges_ids = []
        if not pool:
            raise ValueError(f"Resource pool id {pool_id} not found")

        ranges_ids = [resource_range.id for resource_range in pool.resource_pool_ranges]
        query_record = session.query(self.pool_use_detail_class).filter(
                            and_(self.pool_use_detail_class.value.in_(values), self.pool_use_detail_class.resource_pool_ranges_id.in_(ranges_ids))
                            ).all() 
        for record in query_record:
            records.append({"pool_range_id": record.resource_pool_ranges_id, "record_value": record.value})     
        
        return self.delete_used_records_from_pool(records, session)

    @staticmethod
    def is_have_enough_range_space(pool_data, count):
        try:
            pool_ranges = pool_data.resource_pool_ranges
            resource_ranges = sorted(pool_ranges, key=lambda x: x.start_value)
            range_total_num_count = 0
            range_used_total_num_count = 0
            for resource_range in resource_ranges:
                range_total_num_count += resource_range.end_value - resource_range.start_value + 1
                range_used_total_num_count += len(resource_range.resource_pool_use_detail)
            if range_total_num_count - range_used_total_num_count < count:
                return False
            return True
        except Exception as e:
            raise e

    @staticmethod
    def generate_numbers(start, all_used_num_list, total, ranges):
        all_used_num_list_copy = all_used_num_list.copy()
        numbers = []
        current = start
        current_range_index = 0
        count = 0
        while len(numbers) < total:
            current_range = ranges[current_range_index]
            if current_range[0] <= current <= current_range[1]:
                if current not in all_used_num_list_copy[current_range_index]:
                    numbers.append([current_range[2], current])
                    all_used_num_list_copy.append(current)
                current += 1
            else:
                current_range_index = (current_range_index + 1) % len(ranges)
                count += 1
                if count >= len(ranges) or len(all_used_num_list[current_range_index]) == 0:
                    current = ranges[current_range_index][0]
                else:
                    current = max(all_used_num_list[current_range_index]) + 1
        return numbers

    def generate_resource_num(self, pool_data, count):
        resource_ranges = sorted(pool_data.resource_pool_ranges, key=lambda x: x.start_value)
        all_ranges_list = list(map(lambda x: [x.start_value, x.end_value, x.id], resource_ranges))
        all_used_num_list = list(map(lambda x: [y.value for y in x.resource_pool_use_detail], resource_ranges))
        used_num_list = []
        for used_num in all_used_num_list:
            used_num_list.extend(used_num)
        if not used_num_list:
            max_used_num = all_ranges_list[0][0] - 1
        else:
            max_used_num = max(used_num_list)
        target_num_start = max_used_num + 1
        return self.generate_numbers(target_num_start, all_used_num_list, count, all_ranges_list) 

    def generate_resource_by_value(self, pool_data, value):
        resource_ranges = sorted(pool_data.resource_pool_ranges, key=lambda x: x.start_value)
        all_ranges_list = list(map(lambda x: [x.start_value, x.end_value, x.id], resource_ranges))
        all_used_num_list = list(map(lambda x: [y.value for y in x.resource_pool_use_detail], resource_ranges))
        used_num_list = []
        for used_num in all_used_num_list:
            used_num_list.extend(used_num)
        
        for index, ranges in enumerate(all_ranges_list):
             if ranges[0] <= value <= ranges[1]:
                 if value not in all_used_num_list[index]:
                     return [[ranges[2], value]]         
        return []
    
    def query_pool_range_info(self, pool_data):
        resource_ranges = sorted(pool_data.resource_pool_ranges, key=lambda x: x.start_value)
        all_ranges_list = list(map(lambda x: [x.start_value, x.end_value, x.id], resource_ranges))
        all_used_num_list = list(map(lambda x: [y.value for y in x.resource_pool_use_detail], resource_ranges))
        used_num_list = []
        for used_num in all_used_num_list:
            used_num_list.extend(used_num)
            
        return all_ranges_list, all_used_num_list

    @utils.retry(3)
    def generate_resource_from_pool(self, pool_id, count, session=None):
        if not session:
            session = self.get_session()
        try:
            with session.begin():
                pool_query = self.query_resource_pool_by_id(pool_id)
                pool_data = pool_query.first()
                if not pool_data:
                    raise ValueError(f"Resource pool id {pool_id} not found")
                if not self.is_have_enough_range_space(pool_data, count):
                    raise ValueError(f"Resource pool id {pool_id} has no available ranges")

                to_be_add_numbers = self.generate_resource_num(pool_data, count)

                session.query(self.pool_ranges_class).filter(self.pool_ranges_class.id.in_(list(set(map(lambda x: x[0], to_be_add_numbers))))).update({"is_in_use": True})

                res = []
                for num in to_be_add_numbers:
                    new_use_detail = self.pool_use_detail_class(value=num[1], resource_pool_ranges_id=num[0])
                    res.append(num[1])
                    session.add(new_use_detail)
                session.flush()
                return res
        except Exception as e:
            session.rollback()
            raise


class ResourcePoolVniDB(ResourcePoolBase, ABC):
    def __init__(self):
        super().__init__()
        self.pool_class = ResourcePoolVni
        self.pool_ranges_class = ResourcePoolVniRanges
        self.pool_use_detail_class = ResourcePoolVniUseDetail
        self.session = self.get_session()
    
    def check_vni_ranges_conflict(self, ranges, pool_id=None):
        session = self.get_session()
        existing_ranges = session.query(self.pool_ranges_class).all()
        conflict_pool_ids = []

        for new_range in ranges:
            for existing_range in existing_ranges:
                if not (new_range['end'] < existing_range.start_value or new_range['start'] > existing_range.end_value):
                    if pool_id and existing_range.resource_pool_id == pool_id:
                        continue
                    conflict_pool_ids.append(existing_range.resource_pool_id)

        if conflict_pool_ids:
            conflict_pool_names = list(set(
                map(lambda x: x.name,
                    session.query(self.pool_class)
                    .filter(self.pool_class.id.in_(conflict_pool_ids))
                    .all())
            ))
            return True, conflict_pool_names
        return False, []
    
    def exceeds_max_range_count(self, ranges, pool_id=None):
        max_range_count = 1024
        session = self.get_session()
        if pool_id:
            current_count = session.query(self.pool_ranges_class).filter(self.pool_ranges_class.resource_pool_id != pool_id).count()
        else:
            current_count = session.query(self.pool_ranges_class).count()
        new_ranges_count = len(ranges)
        if current_count + new_ranges_count > max_range_count:
            return True
        return False
    
    def is_vni_ranges_valid(self, ranges):
        for resource_range in ranges:
            start_value = resource_range['start']
            end_value = resource_range['end']
            if start_value < 1 or end_value > 16777215 or start_value > end_value:
                return False
        return True
    
    def generate_resource_by_fabric(self, fabric_id, count=1):
        try:
            session = self.get_session()
            # TODO 当前vni pool和fabric是一对多 后续改为多对多 分配时需要考虑池range
            vni_pool_mapping = session.query(inventory.FabricVniMapping).filter(inventory.FabricVniMapping.fabric_id == fabric_id).first()
            if not vni_pool_mapping:
                raise ValueError(f"No available vni pool for current fabric. Fabric id: {fabric_id}")
            res = self.generate_resource_from_pool(vni_pool_mapping.vni_id, count, session=session)
            return vni_pool_mapping.vni_id, res  
        except Exception as e:
            raise ValueError(f"Generate vni fail: {str(e)}")
        
    def delete_resource_by_fabric(self, fabric_id, value):
        try:
            session = self.get_session()
            # TODO 当前vni pool和fabric是一对多 后续改为多对多 分配时需要考虑池range
            vni_pool_mapping = session.query(inventory.FabricVniMapping).filter(inventory.FabricVniMapping.fabric_id == fabric_id).first()
            if not vni_pool_mapping:
                raise ValueError(f"No available vni pool for current fabric. Fabric id: {fabric_id}")
            self.delete_record_by_value(vni_pool_mapping.vni_id, [value])
        except Exception as e:
            raise ValueError(f"Generate vni fail: {str(e)}")


class ResourcePoolAsnDB(ResourcePoolBase, ABC):
    def __init__(self):
        super().__init__()
        self.pool_class = ResourcePoolAsn
        self.pool_ranges_class = ResourcePoolAsnRanges
        self.pool_use_detail_class = ResourcePoolAsnUseDetail
        self.session = self.get_session()


class ResourcePoolIpDB(ResourcePoolBase, ABC):
    def __init__(self):
        super().__init__()
        self.pool_class = ResourcePoolIp
        self.pool_ranges_class = ResourcePoolIpRanges
        self.pool_use_detail_class = ResourcePoolIpUseDetail
        self.session = self.get_session()

    @utils.retry(3)
    def generate_resource_from_pool(self, pool_id, count, session=None):
        if not session:
            session = self.get_session()
        try:
            with session.begin():
                pool_query = self.query_resource_pool_by_id(pool_id)
                pool_data = pool_query.first()
                if not pool_data:
                    raise ValueError(f"Resource pool id {pool_id} not found")
                if not self.is_have_enough_range_space(pool_data, count):
                    raise ValueError(f"Resource pool id {pool_id} has no available ranges")

                to_be_add_numbers = self.generate_resource_num(pool_data, count)

                session.query(self.pool_ranges_class).filter(
                    self.pool_ranges_class.id.in_(list(set(map(lambda x: x[0], to_be_add_numbers))))).update(
                    {"is_in_use": True})

                res = []
                for num in to_be_add_numbers:
                    new_use_detail = self.pool_use_detail_class(value=num[1], resource_pool_ranges_id=num[0])
                    session.add(new_use_detail)
                    res.append(str(ipaddress.IPv4Address(num[1])))
                session.flush()
                return res
        except Exception as e:
            session.rollback()
            raise e

    def generate_resource_from_pool_by_value(self, pool_id, value):
        session = self.get_session()
        try:
            with session.begin():
                pool_query = self.query_resource_pool_by_id(pool_id)
                pool_data = pool_query.first()
                if not pool_data:
                    raise ValueError(f"Resource pool id {pool_id} not found")

                to_be_add_numbers = self.generate_resource_by_value(pool_data, value)

                session.query(self.pool_ranges_class).filter(
                    self.pool_ranges_class.id.in_(list(set(map(lambda x: x[0], to_be_add_numbers))))).update(
                    {"is_in_use": True})

                res = []
                for num in to_be_add_numbers:
                    new_use_detail = self.pool_use_detail_class(value=num[1], resource_pool_ranges_id=num[0])
                    session.add(new_use_detail)
                    res.append(str(ipaddress.IPv4Address(num[1])))
                session.flush()
                return res
        except Exception as e:
            session.rollback()
            raise e


class ResourcePoolAreaDB(ResourcePoolBase, ABC):
    def __init__(self):
        super().__init__()
        self.pool_class = ResourcePoolArea
        self.pool_ranges_class = ResourcePoolAreaRanges
        self.pool_use_detail_class = ResourcePoolAreaUseDetail
        self.session = self.get_session()


class ResourcePoolBridgeDomainDB(ResourcePoolBase, ABC):
    def __init__(self):
        super().__init__()
        self.pool_class = ResourcePoolBridgeDomain
        self.pool_ranges_class = ResourcePoolBridgeDomainRanges
        self.pool_use_detail_class = ResourcePoolBridgeDomainUseDetail
        self.session = self.get_session()
        

class ResourcePoolVRFVlanDB(ResourcePoolBase, ABC):
    def __init__(self):
        super().__init__()
        self.pool_class = ResourcePoolVRFVlan
        self.pool_ranges_class = ResourcePoolVRFVlanRanges
        self.pool_use_detail_class = ResourcePoolVRFVlanUseDetail
        self.session = self.get_session()


class ResourcePoolVlanDomainDB(DBCommon):
    def __init__(self):
        self.pool_class = ResourcePoolVlanDomain
        self.bridge_domain_pool = ResourcePoolBridgeDomainDB()
        self.vrf_vlan_pool = ResourcePoolVRFVlanDB()
        self.session = self.get_session()
        
        
    def add_vlan_domain_pool(self, name, fabric_id, device_type, bd_ranges, vrf_ranges, session=None):
        if not session:
            session = self.get_session()
        try:
            with session.begin(subtransactions=True):
                new_pool = self.pool_class(name=name, fabric_id=fabric_id, device_type=device_type)
                session.add(new_pool)
                session.flush()

            self.bridge_domain_pool.add_resource_pool(name=name, ranges=bd_ranges, vlan_domain_id=new_pool.id, session=session)
            self.vrf_vlan_pool.add_resource_pool(name=name, ranges=vrf_ranges, vlan_domain_id=new_pool.id, session=session)
            return True, new_pool.id
        except Exception as e:
            import traceback
            print(traceback.format_exc())
            session.rollback()
            return False, 0
        
    def list_vlan_domain_pool(self, fabric_id, session=None):
        if not session:
            session=self.get_session()
        vlan_domains = session.query(self.pool_class).filter(self.pool_class.fabric_id == fabric_id).all()
        vlan_domain_ids = [vd.id for vd in vlan_domains]

        bridge_domain_pool = session.query(self.bridge_domain_pool.pool_class).filter(self.bridge_domain_pool.pool_class.vlan_domain_id.in_(vlan_domain_ids)).all()
        vrf_vlan_pool = session.query(self.vrf_vlan_pool.pool_class).filter(self.vrf_vlan_pool.pool_class.vlan_domain_id.in_(vlan_domain_ids)).all()
        
        bd_pool_ids = {pool.vlan_domain_id: pool.id for pool in bridge_domain_pool}
        vrf_pool_ids = {pool.vlan_domain_id: pool.id for pool in vrf_vlan_pool}
        
        bd_info = self.bridge_domain_pool.get_resource_pool_info_by_id(list(bd_pool_ids.values()), session)
        vrf_info = self.vrf_vlan_pool.get_resource_pool_info_by_id(list(vrf_pool_ids.values()), session)
        
        res = {}
        for vd in vlan_domains:
            info = {}
            if vd.id in bd_pool_ids:
               info["bridge_domain"] = bd_info.get(bd_pool_ids[vd.id], {}) 
            if vd.id in vrf_pool_ids:
               info["var_vlan"] = vrf_info.get(vrf_pool_ids[vd.id], {}) 
               
            info["vlan_domain_name"] = vd.name
               
            res[vd.id] = info
        
        return res
    
    def edit_vlan_domain_pool(self, vlan_domain_id, new_name, bd_ranges, vrf_ranges, session=None):
        if not session:
            session = self.get_session()
        
        session.query(self.pool_class).filter(self.pool_class.id == vlan_domain_id).update({"name": new_name})

        bd_pool = session.query(self.bridge_domain_pool.pool_class).filter(self.bridge_domain_pool.pool_class.vlan_domain_id == vlan_domain_id).first()
        vrf_pool = session.query(self.vrf_vlan_pool.pool_class).filter(self.vrf_vlan_pool.pool_class.vlan_domain_id == vlan_domain_id).first()
             
        if not self.bridge_domain_pool.edit_resource_pool(bd_pool.id, new_name, bd_ranges, session):
            raise Exception('Failed to edit bridge domain range')
        
        if not self.vrf_vlan_pool.edit_resource_pool(vrf_pool.id, new_name, vrf_ranges, session):
            raise Exception('Failed to edit resource vrf vlan range')
        
        return
    
    def generate_bdvlan_from_pool_by_value(self, vlan_domain_id, value, ls_id):
        session = self.get_session()
        try:
            with session.begin():
                
                bd_pool = session.query(self.bridge_domain_pool.pool_class).filter(self.bridge_domain_pool.pool_class.vlan_domain_id == vlan_domain_id).first()
                pool_query = self.bridge_domain_pool.query_resource_pool_by_id(bd_pool.id)
                pool_data = pool_query.first()
                if not pool_data:
                    raise ValueError(f"Resource pool id {bd_pool.id} not found")

                to_be_add_numbers = self.bridge_domain_pool.generate_resource_by_value(pool_data, value)

                session.query(self.bridge_domain_pool.pool_ranges_class).filter(
                    self.bridge_domain_pool.pool_ranges_class.id.in_(list(set(map(lambda x: x[0], to_be_add_numbers))))).update(
                    {"is_in_use": True})

                res = []
                for num in to_be_add_numbers:
                    new_use_detail = self.bridge_domain_pool.pool_use_detail_class(value=num[1], resource_pool_ranges_id=num[0], ls_id=ls_id)
                    session.add(new_use_detail)
                    res.append(num[1])
                session.flush()
                return res
        except Exception as e:
            session.rollback()
            raise e
        
    def delete_bdvlan_from_pool_by_value(self, vlan_domain_id, values):
        session = self.get_session()
        try:
            bd_pool = session.query(self.bridge_domain_pool.pool_class).filter(self.bridge_domain_pool.pool_class.vlan_domain_id == vlan_domain_id).first()
            pool_query = self.bridge_domain_pool.query_resource_pool_by_id(bd_pool.id)
            pool_data = pool_query.first()
            if not pool_data:
                raise ValueError(f"Resource pool id {bd_pool.id} not found")
            self.bridge_domain_pool.delete_record_by_value(bd_pool.id, values)
        except Exception as e:
            raise e
        
    def query_vrf_vlan_range_info(self, vlan_domain_id):
        session = self.get_session()
        try:
            vrf_pool = session.query(self.vrf_vlan_pool.pool_class).filter(self.vrf_vlan_pool.pool_class.vlan_domain_id == vlan_domain_id).first()
            pool_query = self.vrf_vlan_pool.query_resource_pool_by_id(vrf_pool.id)
            pool_data = pool_query.first()
            if not pool_data:
                raise ValueError(f"Resource pool id {vrf_pool.id} not found")
            return self.vrf_vlan_pool.query_pool_range_info(pool_data)
        except Exception as e:
            raise e
        
    def generate_vrfvlan_from_pool_by_value(self, vlan_domain_id, value, lr_id):
        session = self.get_session()
        try:
            with session.begin():
                
                vrf_pool = session.query(self.vrf_vlan_pool.pool_class).filter(self.vrf_vlan_pool.pool_class.vlan_domain_id == vlan_domain_id).first()
                pool_query = self.vrf_vlan_pool.query_resource_pool_by_id(vrf_pool.id)
                pool_data = pool_query.first()
                if not pool_data:
                    raise ValueError(f"Resource pool id {vrf_pool.id} not found")

                to_be_add_numbers = self.vrf_vlan_pool.generate_resource_by_value(pool_data, value)

                session.query(self.vrf_vlan_pool.pool_ranges_class).filter(
                    self.vrf_vlan_pool.pool_ranges_class.id.in_(list(set(map(lambda x: x[0], to_be_add_numbers))))).update(
                    {"is_in_use": True})

                res = []
                for num in to_be_add_numbers:
                    new_use_detail = self.vrf_vlan_pool.pool_use_detail_class(value=num[1], resource_pool_ranges_id=num[0], lr_id=lr_id)
                    session.add(new_use_detail)
                    res.append(num[1])
                session.flush()
                return res
        except Exception as e:
            session.rollback()
            raise e
        
    def delete_vrfvlan_from_pool_by_value(self, vlan_domain_id, values):
        session = self.get_session()
        try:
            vrf_pool = session.query(self.vrf_vlan_pool.pool_class).filter(self.vrf_vlan_pool.pool_class.vlan_domain_id == vlan_domain_id).first()
            pool_query = self.vrf_vlan_pool.query_resource_pool_by_id(vrf_pool.id)
            pool_data = pool_query.first()
            if not pool_data:
                raise ValueError(f"Resource pool id {vrf_pool.id} not found")
            self.vrf_vlan_pool.delete_record_by_value(vrf_pool.id, values)
        except Exception as e:
            raise e
        
    def delete_bdvlan_from_pool_by_logical_switch(self, value, ls_id):
        session = self.get_session()
        try:
            bd_pool_use_detail = session.query(self.bridge_domain_pool.pool_use_detail_class).filter(self.bridge_domain_pool.pool_use_detail_class.value == value, 
                                                                                                     self.bridge_domain_pool.pool_use_detail_class.ls_id == ls_id).all()

            range_ids = [use_detail.resource_pool_ranges_id for use_detail in bd_pool_use_detail]
            
            bd_pool_range = session.query(self.bridge_domain_pool.pool_ranges_class).filter(self.bridge_domain_pool.pool_ranges_class.id.in_(range_ids)).all()

            pool_ids = [pool_range.resource_pool_id for pool_range in bd_pool_range]

            for pool_id in pool_ids:
                self.bridge_domain_pool.delete_record_by_value(pool_id, [value])
        except Exception as e:
            raise e
        
    def delete_vrfvlan_from_pool_by_logical_router(self, value, lr_id):
        session = self.get_session()
        try:
            vrf_pool_use_detail = session.query(self.vrf_vlan_pool.pool_use_detail_class).filter(self.vrf_vlan_pool.pool_use_detail_class.value == value, 
                                                                                                 self.vrf_vlan_pool.pool_use_detail_class.lr_id == lr_id).all()

            range_ids = [use_detail.resource_pool_ranges_id for use_detail in vrf_pool_use_detail]
            
            vrf_pool_range = session.query(self.vrf_vlan_pool.pool_ranges_class).filter(self.vrf_vlan_pool.pool_ranges_class.id.in_(range_ids)).all()

            pool_ids = [pool_range.resource_pool_id for pool_range in vrf_pool_range]

            for pool_id in pool_ids:
                self.vrf_vlan_pool.delete_record_by_value(pool_id, [value])
        except Exception as e:
            raise e
        
    def check_vrf_vlan_value_with_vlan_domain(self, value, vlan_domain_id):
        '''
            检查vrf pool中给定值是否存在 ，如果存在返回出对应的lr_id
        '''
        session = self.get_session()
        try:
            vrf_pool = session.query(self.vrf_vlan_pool.pool_class).filter(self.vrf_vlan_pool.pool_class.vlan_domain_id == vlan_domain_id).first()
            ranges_ids = [pool_ranges.id for pool_ranges in vrf_pool.resource_pool_ranges]
            
            vrf_pool_use_detail = session.query(self.vrf_vlan_pool.pool_use_detail_class).filter(self.vrf_vlan_pool.pool_use_detail_class.value == value,
                                                                                                 self.vrf_vlan_pool.pool_use_detail_class.resource_pool_ranges_id.in_(ranges_ids)).first()

            if vrf_pool_use_detail:
                return {"res": True, "lr_id": vrf_pool_use_detail.lr_id}
            else:
                return {"res": False}
        except Exception as e:
            raise e
        
    def check_bd_vlan_value_in_range(self, value, vlan_domain_id):
        '''
            检查bd vlan是否在pool范围内
        '''
        session = self.get_session()
        try:
            bd_pool = session.query(self.bridge_domain_pool.pool_class).filter(self.bridge_domain_pool.pool_class.vlan_domain_id == vlan_domain_id).first()
            pool_query = self.bridge_domain_pool.query_resource_pool_by_id(bd_pool.id)
            pool_data = pool_query.first()
            
            # resource_ranges = sorted(pool_data.resource_pool_ranges, key=lambda x: x.start_value)
            all_ranges_list, all_used_num_list = self.bridge_domain_pool.query_pool_range_info(pool_data)
            if not is_value_in_ranges(value, all_ranges_list):
                return False, f"vlan:{value} is not within bridge domain range"
            
            if value in all_used_num_list:
                return False, f"vlan:{value} is already in use"

            return True, ""
        except Exception as e:
            raise e    


resource_pool_asn_db = ResourcePoolAsnDB()
resource_pool_area_db = ResourcePoolAreaDB()
resource_pool_ip_db = ResourcePoolIpDB()
resource_pool_vni_db = ResourcePoolVniDB()
resource_pool_vlandomain_db = ResourcePoolVlanDomainDB()

