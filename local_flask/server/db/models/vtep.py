# -*- coding: utf-8 -*-
import yaml
from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    ForeignKey,
    Boolean,
    Enum,
    func
)

from server.db.models.inventory import Group, Switch
from server.db.db_common import DBCommon
from server.db.models.base import Base


class MacVtepMapping(Base):
    __tablename__ = 'mac_vtep_mapping'
    id = Column(Integer, autoincrement=True, primary_key=True)
    # should be "22:22:22:22:22:22.10100"
    mac_vni = Column(String(64), nullable=False)
    vtep_ip = Column(String(64), nullable=False)
    # if the entry come from ovsdb local, then it will be "controller", otherwise, it will be ip address for BGP-EVPN
    source = Column(String(64))


class VtepControlSwitch(Base):
    __tablename__ = 'vtep_control_switch_list'
    id = Column(Integer, primary_key=True, autoincrement=True)
    sn = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'), unique=True)
    seq = Column(Enum('1', '2', '3', '4', '5', '6', '7', '8', '9', '10'), nullable=False)
    local_vtep_ip = Column(String(64))
    enable = Column(Boolean, default=False)
    ovsdb_status = Column(Enum('connected', 'disconnected', 'error'), default='disconnected')
    config_status = Column(Enum('normal', 'error'), default='normal')
    config_error = Column(String(255))


class VlanVxlanList(Base):
    __tablename__ = 'vlan_vxlan_list'
    id = Column(Integer, primary_key=True, autoincrement=True)
    vlan = Column(Integer, unique=True, nullable=False)
    vlan_name = Column(String(64))
    vni = Column(Integer, unique=True, nullable=False)


class PortVlanBinding(Base):
    __tablename__ = 'port_vlan_binding'
    id = Column(Integer, primary_key=True, autoincrement=True)
    sn = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'), unique=True)
    # should be {"te-1/1/1": {100: 10100},"te-1/1/2": {100: 10100}}
    port_binding = Column(Text(65535), nullable=False)


class SwitchArpMac(Base):
    __tablename__ = 'switch_arp_mac'
    id = Column(Integer, primary_key=True, autoincrement=True)
    sn = Column(String(64), ForeignKey('switch.sn', ondelete='CASCADE', onupdate='CASCADE'), unique=True)
    local_vtep_ip = Column(String(64))
    mac = Column(String(32), nullable=False)


class VtepDB(DBCommon):

    def get_vxlan_statics(self, session=None):
        session = session or self.get_session()
        statics = session.query(func.substring_index(MacVtepMapping.mac_vni, '.', -1).label('vni'),
                                func.count('*').label('count')).group_by('vni').all()
        return statics

    def get_vtep_groups(self, session=None):
        session = session or self.get_session()
        prefix = 'vtep_tab'
        with session.begin():
            groups = session.query(Group).filter(Group.group_name.like('%' + prefix + '%')).all()
            group_dicts = []
            group_names = []
            if not groups:
                for i in range(1, 13):
                    group_name = prefix + str(i)
                    group_dicts.append({'group_name': group_name,
                                        'description': 'only for vtep sdn app'})
                    group_names.append({'name': group_name})
                session.bulk_insert_mappings(Group, group_dicts)
            else:

                for group in groups:
                    switch_list = {}
                    slots = []
                    for ass_group in group.association_group:
                        switch = session.query(Switch).filter(Switch.sn == ass_group.switch_sn).first()
                        port_binding = session.query(PortVlanBinding).filter_by(sn=ass_group.switch_sn).first()
                        vtep_switch = session.query(VtepControlSwitch).filter(VtepControlSwitch.sn == ass_group.switch_sn).first()
                        if switch and vtep_switch and port_binding:
                            port_binding_dict = yaml.full_load(port_binding.port_binding)
                            port_binding_value = port_binding_dict.values()[0] if port_binding_dict else {}
                            mac_num = session.query(MacVtepMapping).filter(MacVtepMapping.vtep_ip == vtep_switch.local_vtep_ip).count()
                            switch_list[ass_group.switch_sn] = {'ip': switch.mgt_ip, 'name': switch.host_name,
                                                                'status': vtep_switch.ovsdb_status,
                                                                'seq': vtep_switch.seq,
                                                                'vtep': vtep_switch.local_vtep_ip,
                                                                'mac_num': mac_num,
                                                                'binding': port_binding_value,
                                                                'sn': switch.sn,
                                                                'platform_model': switch.platform_model}
                            slots.append(int(vtep_switch.seq))
                    group_names.append({'name': group.group_name, 'switch_list': switch_list, 'slots': slots})

        return group_names

    def get_all_vteps(self, session=None):
        session = session or self.get_session()
        switches = session.query(VtepControlSwitch).filter(VtepControlSwitch.local_vtep_ip != None).all()
        return switches

    def get_vtep_switches(self, session=None):
        session = session or self.get_session()
        switches = session.query(VtepControlSwitch).all()
        return switches

    def del_vlan_binding(self, vlan, session=None):
        session = session or self.get_session()
        with session.begin(subtransactions=True):
            switches = session.query(VtepControlSwitch).all()
            for switch in switches:
                port_bind = session.query(PortVlanBinding).filter(PortVlanBinding.sn == switch.sn).first()
                if port_bind and port_bind.port_binding != '':
                    bind_dict = yaml.full_load(port_bind.port_binding)
                    for port_bind_dict in bind_dict.values():
                        port_bind_dict.pop(vlan, None)
                    port_bind.port_binding = str(bind_dict)
    
    def del_vlan_binding_by_sn(self, vlan, sn,session=None):
        session = session or self.get_session()
        with session.begin(subtransactions=True):
            port_bind = session.query(PortVlanBinding).filter(PortVlanBinding.sn == sn).first()
            if port_bind and port_bind.port_binding != '':
                bind_dict = yaml.full_load(port_bind.port_binding)
                for port_bind_dict in bind_dict.values():
                    port_bind_dict.pop(vlan, None)
                port_bind.port_binding = str(bind_dict)
    
    def get_all_binding_vlans(self, sn, session=None):
        session = session or self.get_session()
        port_bind = session.query(PortVlanBinding).filter(PortVlanBinding.sn == sn).first()
        if port_bind:
            vlans = set()
            port_bind_dict = yaml.full_load(port_bind.port_binding)
            for vlan_mapping in port_bind_dict.values():
                [vlans.add(vlan) for vlan in vlan_mapping.keys()]
            return list(vlans)

    def get_all_vtep_switches(self, session=None):
        session = session or self.get_session()
        switch_list = session.query(VtepControlSwitch.sn, Switch.mgt_ip) \
            .outerjoin(Switch, VtepControlSwitch.sn == Switch.sn).filter(VtepControlSwitch.enable == True).all()
        return switch_list

    def save_mac_vtep_to_db(self, mac_vtep=None, session=None):
        session = session or self.get_session()
        session.bulk_insert_mappings(MacVtepMapping, mac_vtep)

    def remove_mac_vtep_to_db(self, mac_vtep=None, session=None):
        session = session or self.get_session()
        session.query(MacVtepMapping).filter(MacVtepMapping.mac_vni.in_(mac_vtep)).delete(synchronize_session=False)

    def update_mac_vtep_to_db(self, mac_vtep=None, session=None):
        session = session or self.get_session()
        session.bulk_update_mappings(MacVtepMapping, mac_vtep)

    def get_switch_macs(self, session=None):
        session = session or self.get_session()
        mappings = session.query(MacVtepMapping.id, MacVtepMapping.mac_vni,
                                 MacVtepMapping.vtep_ip, MacVtepMapping.source).all()
        macs = [entry.mac_vni for entry in mappings]
        entries = [(entry.mac_vni, entry.vtep_ip, entry.source) for entry in mappings]
        id_mapping = dict([(entry.mac_vni, entry.id) for entry in mappings])
        return entries, id_mapping, macs

    def get_all_configed_vlans(self, session=None):
        session = session or self.get_session()
        vlans = session.query(VlanVxlanList.vlan).all()
        return [vlan[0] for vlan in vlans]

    def get_vlan_vni_binding(self, sn, session=None):
        session = session or self.get_session()
        port_bind = session.query(PortVlanBinding.port_binding).filter_by(sn=sn).first()
        if port_bind:
            return yaml.full_load(port_bind.port_binding)
        # return {"PCA3226K02C": {"ge-1/1/1": {"100": "10100", "200": "10200"}}}
        # return {"PCA3226K02C": {"te-1/1/1": {100: 10100},"te-1/1/2": {100: 10100}},
        #  "581254X1836043": {"te-1/1/1": {100: 10100, 200: 10200},"te-1/1/2": {}}}
        return None

    def del_physical_port(self, sn, ports, session=None):
        session = session or self.get_session()
        with session.begin(subtransactions=True):
            db_port_entry = session.query(PortVlanBinding).filter(PortVlanBinding.sn == sn).first()
            binding_dict = yaml.full_load(db_port_entry.port_binding)
            for port in ports:
                if port in binding_dict.keys():
                    binding_dict.pop(str(port))
            db_port_entry.port_binding = str(binding_dict)

    def add_new_physical_port(self, sn, ports, session=None):
        session = session or self.get_session()
        with session.begin(subtransactions=True):
            db_port_entry = session.query(PortVlanBinding).filter(PortVlanBinding.sn == sn).first()
            binding_dict = yaml.full_load(db_port_entry.port_binding)
            
            full_enabled_vlan = []
            for port in binding_dict:
                full_enabled_vlan = list(set(full_enabled_vlan) | set(binding_dict[port]))
            full_enabled_vlan_obj = {}
            for vlan in full_enabled_vlan:
                full_enabled_vlan_obj[vlan] = int(vlan) + 10000

            for port, value in ports:
                if value:
                    binding_dict[str(port)] = value
                else:
                    binding_dict[str(port)] = full_enabled_vlan_obj


            db_port_entry.port_binding = str(binding_dict)

    def update_switch_vtep_ip(self, sn, vtep_ip, session=None):
        session = session or self.get_session()
        with session.begin(subtransactions=True):
            switch = session.query(VtepControlSwitch).filter_by(sn=sn).first()
            if switch:
                switch.local_vtep_ip = vtep_ip

    def update_switch_config_status(self, sn, status, reason='', session=None):
        session = session or self.get_session()
        with session.begin(subtransactions=True):
            switch = session.query(VtepControlSwitch).filter_by(sn=sn).first()
            if switch:
                switch.config_status = status
                switch.config_error = reason

    def get_switch_local_arp_macs(self, sn, session=None):
        session = session or self.get_session()
        macs = session.query(SwitchArpMac).filter_by(sn=sn).all()
        return macs

    def get_other_arps_macs(self, sn, session=None):
        session = session or self.get_session()
        macs = session.query(SwitchArpMac).filter(SwitchArpMac.sn != sn).all()
        return macs

    def batch_add_local_macs(self, macs, session=None):
        session = session or self.get_session()
        session.bulk_insert_mappings(SwitchArpMac, macs)

    def del_age_arp_macs(self, sn, macs, session=None):
        session = session or self.get_session()
        session.query(SwitchArpMac).filter(SwitchArpMac.sn==sn,
                                           SwitchArpMac.mac.in_(macs)).delete(synchronize_session=False)


vtep_db = VtepDB()


if __name__ == '__main__':
    from server import cfg

    cfg.CONF(default_config_files=['../../automation.ini'])
    from server.db.models.inventory import Switch
    # print vtep_db.get_vxlan_statics()
    # vlans = vtep_db.get_all_binding_vlans('CN0VK93CCES008AI0001')
    # print type(vlans[0])
    # # vtep_db.del_vlan_binding(100)
    # entries, id_mapping, macs = vtep_db.get_switch_macs()
    # print entries
    # print id_mapping
    # print macs
    # print vtep_db.get_all_configed_vlans()
    # print vtep_db.get_vlan_vni_binding()
    # vtep_db.update_model(VtepControlSwitch, filters={'sn': ['581254X1611057']},
    #                      updates={VtepControlSwitch.local_vtep_ip: '**********'})
    # print vtep_db.get_all_binding_vlans('CN0VK93CCES008AI0001')
    vtep_db.del_vlan_binding(788)
