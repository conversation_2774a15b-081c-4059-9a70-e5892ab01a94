from server.db.models.base import Base
from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    DateTime,
    Float,
    ForeignKey
)


class OtnTempData(Base):
    __tablename__ = 'otn_temp_data'
    id = Column(String(128), autoincrement=False, primary_key=True)
    ip = Column(String(48), nullable=False)
    nmu = Column(String(64), nullable=True)
    data = Column(Text(65535), nullable=True)


class DistributedLock(Base):
    __tablename__ = 'distributed_lock'
    lock_key = Column(String(128), autoincrement=False, primary_key=True)
    lock_expiry = Column(DateTime(), nullable=False)
    process_id = Column(String(255), nullable=True)
    thread_id = Column(String(255), nullable=True)
    host_name = Column(String(255), nullable=True)
    acquired_time = Column(DateTime(), nullable=False)


class OtnDeviceBasic(Base):
    __tablename__ = 'otn_device_basic'
    id = Column(Integer(), nullable=False, primary_key=True, autoincrement=True)
    name = Column(String(64), nullable=False)
    model = Column(String(32), nullable=False)
    ip = Column(String(48), nullable=False)
    reachable_status = Column(Integer(), nullable=False, default=0)
    longitude = Column(Float)
    latitude = Column(Float)
    series = Column(Integer(), nullable=True)


class FmtDeviceBasic(Base):
    __tablename__ = 'fmt_device_basic'
    device_id = Column(Integer(), ForeignKey('otn_device_basic.id', ondelete='CASCADE'), nullable=False,
                       primary_key=True)
    serial_number = Column(String(48), nullable=True)
    slot_number = Column(Integer(), nullable=False, default=0)
    mask = Column(String(48), nullable=True)
    gateway = Column(String(48), nullable=True)
    mac = Column(String(48), nullable=True)
    key_lock_status = Column(String(10), nullable=False, default="0")
    bzc_status = Column(String(10), nullable=False, default="0") # 蜂鸣器控制 锁定状态
    bzs_status = Column(String(10), nullable=False, default="0") # 蜂鸣器控制 开关状态
    fnc_status = Column(String(10), nullable=False, default="0") # 风扇控制 开关状态
    fns_status = Column(String(10), nullable=False, default="0") # 风扇控制 工作状态
    pwr_status = Column(String(10), nullable=False, default="0") # 电源控制 开关状态
    production_date = Column(String(48), nullable=True)
    hardware_version = Column(String(48), nullable=True)
    software_version = Column(String(48), nullable=True)
    firmware_version = Column(String(48), nullable=True)
    temperature = Column(String(10), nullable=True)


class FmtDeviceCards(Base):
    __tablename__ = 'fmt_device_cards'
    card_id = Column(String(32), nullable=False, autoincrement=False, primary_key=True)
    device_id = Column(Integer(), ForeignKey('fmt_device_basic.device_id', ondelete='CASCADE'), nullable=False)
    slot_index = Column(Integer(), nullable=False)
    type = Column(String(32), nullable=False)
    model = Column(String(32), nullable=True)
    serial_number = Column(String(48), nullable=True)
    production_date = Column(String(48), nullable=True)
    hardware_version = Column(String(48), nullable=True)
    software_version = Column(String(48), nullable=True)
    firmware_version = Column(String(48), nullable=True)
    temperature = Column(String(10), nullable=True)
    ports_data = Column(Text(65535), nullable=True)

class DcsDeviceBasic(Base):
    __tablename__ = 'dcs_device_basic'
    device_id = Column(Integer(), ForeignKey('otn_device_basic.id', ondelete='CASCADE'), nullable=False,
                       primary_key=True)
    serial_number = Column(String(48), nullable=True)
    slot_number = Column(Integer(), nullable=False, default=0)
    mask = Column(String(48), nullable=True)
    gateway = Column(String(48), nullable=True)
    mac = Column(String(48), nullable=True)
    key_lock_status = Column(String(10), nullable=False, default="0")
    bzc_status = Column(String(10), nullable=False, default="0") # 蜂鸣器控制 锁定状态
    bzs_status = Column(String(10), nullable=False, default="0") # 蜂鸣器控制 开关状态
    fnc_status = Column(String(10), nullable=False, default="0") # 风扇控制 开关状态
    fns_status = Column(String(10), nullable=False, default="0") # 风扇控制 工作状态
    pwr_status = Column(String(10), nullable=False, default="0") # 电源控制 开关状态
    production_date = Column(String(48), nullable=True)
    hardware_version = Column(String(48), nullable=True)
    software_version = Column(String(48), nullable=True)
    firmware_version = Column(String(48), nullable=True)
    temperature = Column(String(10), nullable=True)


class DcsDeviceCards(Base):
    __tablename__ = 'dcs_device_cards'
    card_id = Column(String(32), nullable=False, autoincrement=False, primary_key=True)
    device_id = Column(Integer(), ForeignKey('dcs_device_basic.device_id', ondelete='CASCADE'), nullable=False)
    slot_index = Column(Integer(), nullable=False)
    type = Column(String(32), nullable=False)
    model = Column(String(32), nullable=True)
    serial_number = Column(String(48), nullable=True)
    production_date = Column(String(48), nullable=True)
    hardware_version = Column(String(48), nullable=True)
    software_version = Column(String(48), nullable=True)
    firmware_version = Column(String(48), nullable=True)
    temperature = Column(String(10), nullable=True)
    ports_data = Column(Text(65535), nullable=True)
