from server.db.models.base import Base
from server.db.db_common import DBCommon
from sqlalchemy import (
    Column,
    SmallInteger,
    Integer,
    String,
    Text,
    ForeignKey,
    DateTime,
    Boolean,
    Enum,
    Float,
    BigInteger,
    func,
    select, case, literal_column,
    Index
)
class Devices(Base):
    __tablename__ = 'devices'

    serialnumber = Column(String(30), primary_key=True)
    devicetype = Column(String(32))
    macaddress = Column(String(30))
    manufacturer = Column(String(64))
    configuration = Column(Text)
    notes = Column(Text)
    owner = Column(String(64), index=True)
    location = Column(Text, index=True)
    venue = Column(String(64))
    devicepassword = Column(String(64))
    firmware = Column(String(128))
    compatible = Column(String(128))
    fwupdatepolicy = Column(String(128))
    uuid = Column(BigInteger)
    creationtimestamp = Column(BigInteger)
    lastconfigurationchange = Column(BigInteger)
    lastconfigurationdownload = Column(BigInteger)
    lastfwupdate = Column(BigInteger)
    subscriber = Column(String(64))
    entity = Column(String(64))  # 有索引
    modified = Column(BigInteger)
    locale = Column(String(32))
    restricteddevice = Column(Boolean)
    pendingconfiguration = Column(Text)
    pendingconfigurationcmd = Column(String(64))
    restrictiondetails = Column(Text)
    pendinguuid = Column(BigInteger)
    simulated = Column(Boolean)
    lastrecordedcontact = Column(BigInteger)
    certificateexpirydate = Column(BigInteger)
    connectreason = Column(Text)

    # 定义索引
    __table_args__ = (
        Index('devicelocation', 'location'),
        Index('deviceowner', 'owner'),
    )
