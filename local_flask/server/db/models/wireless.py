# -*- coding: utf-8 -*-

from sqlalchemy import (
    Column,
    Integer,
    BigInteger,
    String,
    Text,
    UniqueConstraint,
    DateTime,
    Index,
    text
)
from sqlalchemy.dialects.postgresql import JSONB, JSON

from server.db.models.base import Base


class WirelessProfile(Base):
    __tablename__ = 'wireless_profile'
    id = Column(Integer, primary_key=True)
    site_id = Column(Integer, nullable=False, index=True)
    variable_id = Column(String(64), nullable=False)
    type = Column(Integer, nullable=False, index=True, default=0)
    name = Column(String(64), nullable=False)
    parameter = Column(JSONB)
    description = Column(String(128))

    __table_args__ = (
        UniqueConstraint('site_id', 'name', 'type', name='uq_site_id_name_type'),
    )


class WirelessProfileUsage(Base):
    __tablename__ = 'wireless_profile_usage'
    id = Column(Integer, primary_key=True)
    site_id = Column(Integer, nullable=False)
    referee_id = Column(String(64), nullable=False)
    profile_id = Column(Integer, nullable=False)

    __table_args__ = (
        UniqueConstraint('referee_id', 'profile_id', name='uq_referee_id_profile_id'),
    )


class WirelessConfigureSsid(Base):
    __tablename__ = 'wireless_configure_ssid'
    id = Column(Integer, primary_key=True)
    site_id = Column(Integer, nullable=False, index=True)
    name = Column(String(64), nullable=False)
    security = Column(String(64))
    radio = Column(String(64))
    network_type = Column(Integer, default=1)
    labels_name = Column(JSONB, default=list, server_default=text("'[]'::jsonb"), nullable=False)
    vlan_or_dhcp_name = Column(String(64))
    is_enable = Column(Integer, default=1)
    ssid_configure = Column(Text)


class WirelessSiteLabel(Base):
    __tablename__ = 'wireless_site_label'

    id = Column(Integer, primary_key=True, nullable=False)
    site_id = Column(Integer, nullable=False)
    name = Column(String(64), nullable=False)

    __table_args__ = (
        UniqueConstraint('site_id', 'name', name='uq_wireless_site_label_site_id_name'),
    )


class WirelessRrmTaskLog(Base):
    __tablename__ = 'wireless_rrm_task_log'
    id = Column(String(64), primary_key=True, nullable=False)
    create_time = Column(DateTime, nullable=False)
    modified_time = Column(DateTime, nullable=False)
    site_id = Column(Integer, nullable=False, index=True)
    trigger_time = Column(DateTime)
    is_schedule_task = Column(Integer, nullable=False, default=1)
    online_num = Column(Integer, nullable=True)
    success_num = Column(Integer, nullable=True)
    failed_num = Column(Integer, nullable=True)
    __table_args__ = (
        UniqueConstraint('id', name='uq_wireless_rrm_task_log_id'),
        Index('idx_wireless_rrm_task_log_site_id', 'site_id'),
    )


class WirelessRrmTaskResult(Base):
    __tablename__ = 'wireless_rrm_task_result'
    task_id = Column(String(64), nullable=False, index=True)
    sn = Column(String(64), primary_key=True, nullable=False)
    create_time = Column(DateTime, nullable=False)
    modified_time = Column(DateTime, nullable=False)
    result_type = Column(Integer, nullable=False, default=1)
    __table_args__ = (
        Index('idx_wireless_rrm_task_result_task_id', 'task_id'),
    )

class WirelessChannel(Base):
    __tablename__ = 'wireless_channel'
    country_code = Column(String(16), primary_key=True, nullable=False)
    _2g_channel = Column("2g_channel", JSON, nullable=True)
    _5g_channel = Column("5g_channel", JSON, nullable=True)
    _6g_channel = Column("6g_channel", JSON, nullable=True)


class WirelessDhcpService(Base):
    __tablename__ = 'wireless_dhcp_service'
    id = Column(Integer, primary_key=True)
    site_id = Column(Integer, nullable=False, index=True)
    name = Column(String(64), nullable=False)
    subnet = Column(String(64), nullable=False)
    vlan = Column(String(16), nullable=False, default='-')
    dhcp_configure = Column(JSON, nullable=False)
    description = Column(String(128), nullable=True)

    __table_args__ = (
        UniqueConstraint('site_id', 'name', name='uq_site_id_name'),
        Index('idx_wireless_dhcp_service_site_id', 'site_id'),
    )

# class WirelessDeviceChannel(Base):
#     __tablename__ = 'wireless_device_channel'
#     sn = Column(String(64), primary_key=True, nullable=False)
#     _2g_channel = Column('2g_channel', String(256))
#     _5g_channel = Column('5g_channel', String(256))
#     _6g_channel = Column('6g_channel', String(256))
#     __table_args__ = (
#         UniqueConstraint('sn', name='uq_wireless_device_channel_sn'),
#     )

class WirelessEthernetPorts(Base):
    __tablename__ = 'wireless_ethernet_ports'

    create_time = Column(DateTime)
    modified_time = Column(DateTime)
    id = Column(Integer, primary_key=True)
    site_id = Column(Integer)
    port = Column(String(64))  
    mac = Column(String(64))   
    network_type = Column(Integer, nullable=False, default=1)  
    vlan_or_dhcp_name = Column(String(64), nullable=False, default="") 
    vlan_tag = Column(Integer)
    
    __table_args__ = (
        UniqueConstraint('site_id', 'network_type', 'vlan_or_dhcp_name', name='uq_site_net_vlan'),
        Index('idx_wireless_ethernet_ports_site_id', 'site_id'),
    )


class WirelessClient(Base):
    __tablename__ = 'wireless_client'

    id = Column(Integer,primary_key=True,autoincrement=True)
    status = Column(Integer,nullable=False,default=1)
    host_name = Column(String(64),index=True)
    mac = Column(String(64),unique=True,index=True)
    vendor = Column(String(64),index=True)
    sn = Column(String(64),nullable=False,index=True)
    ssid = Column(String(64),nullable=False,index=True)
    rssi = Column(Integer,nullable=True)
    band = Column(String(16),nullable=False)
    channel = Column(Integer,nullable=True)
    channel_width = Column(Integer,nullable=True)
    ip = Column(String(64),nullable=False,index=True)
    vlan = Column(Integer,nullable=True)
    rx = Column(BigInteger,nullable=True)
    tx = Column(BigInteger,nullable=True)
    rx_packets = Column(BigInteger,nullable=True)
    tx_packets = Column(BigInteger,nullable=True)
    join_time = Column(DateTime,nullable=False)
    leave_time = Column(DateTime)

    __table_args__ = (
        Index('idx_wireless_client_host_name', 'host_name'),
        Index('idx_wireless_client_mac', 'mac'),
        Index('idx_wireless_client_vendor', 'vendor'),
        Index('idx_wireless_client_sn', 'sn'),
        Index('idx_wireless_client_ssid', 'ssid'),
        Index('idx_wireless_client_ip', 'ip'),
    )