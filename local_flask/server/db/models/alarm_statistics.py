# -*- coding: utf-8 -*-
from sqlalchemy import (
    Column,
    Integer,
    DateTime
)
from server.db.models.base import Base
class AlarmStatisticsHourly(Base):
    __tablename__ = 'alarm_statistics_hourly'
    id = Column(Integer, primary_key=True, autoincrement=True)
    time = Column(DateTime)
    info = Column(Integer, default=0, nullable=False)
    warn = Column(Integer, default=0, nullable=False)
    error = Column(Integer, default=0, nullable=False)