from server import cfg
from server.db.session_factory import EngineFacade

_PG_FACADE = None

def _create_pg_facade_lazily():
    global _PG_FACADE
    if _PG_FACADE is None:
        sql_connection = getattr(cfg.CONF, 'pg_database', None)
        if sql_connection is None or not hasattr(sql_connection, 'connection'):
            raise Exception('PostgreSQL connection string not configured in cfg.CONF.pg_database.connection')
        _PG_FACADE = EngineFacade(
            sql_connection=cfg.CONF.pg_database.connection,
            idle_timeout=cfg.CONF.pg_database.idle_timeout,
            connection_debug=cfg.CONF.pg_database.connection_debug,
            max_pool_size=cfg.CONF.pg_database.max_pool_size,
            max_overflow=cfg.CONF.pg_database.max_overflow,
            pool_timeout=cfg.CONF.pg_database.pool_timeout
        )
    return _PG_FACADE

def get_pg_engine():
    facade = _create_pg_facade_lazily()
    return facade.get_engine()

def get_pg_session(autoflush=True, autocommit=True, expire_on_commit=False):
    facade = _create_pg_facade_lazily()
    return facade.get_session(autoflush=autoflush, autocommit=autocommit, expire_on_commit=expire_on_commit) 