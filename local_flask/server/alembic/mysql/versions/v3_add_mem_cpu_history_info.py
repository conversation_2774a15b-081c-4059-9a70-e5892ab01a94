# revision identifiers, used by Alembic.
revision = 'v3'
down_revision = 'v2'
branch_labels = None
depends_on = None

from datetime import datetime
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

def upgrade():
    op.create_table('machine_history_info',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.<PERSON>('mem', sa.String(length=11)),
                    sa.<PERSON>('cpu', sa.String(length=11)),
                    sa.<PERSON>('disk', sa.String(length=11)),
                    sa.<PERSON>umn('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
    )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('machine_history_info')
    # ### end Alembic commands ###
