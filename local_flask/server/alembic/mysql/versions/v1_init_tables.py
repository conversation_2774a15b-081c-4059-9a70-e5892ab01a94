# revision identifiers, used by Alembic.
from server.util.encrypt_util import aes_cipher

revision = 'v1'
down_revision = None
branch_labels = None
depends_on = None

import os, re
import json
from datetime import datetime
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

def parse_template(content_lines):
    content = ''
    param = ''
    sw_platform = ''
    content_start = False
    param_start = False
    for line in content_lines:

        if 'content_start' in line:
            content_start = True
            continue
        if 'content_end$' in line:
            content_start = False
            continue
        if 'param_start' in line:
            param_start = True
            continue
        if 'param_end$' in line:
            param_start = False
            continue

        if content_start:
            content += line
            continue
        if param_start:
            param += line
            continue
        if 'name:' in line:
            continue
        if 'description:' in line:
            continue
        if 'platform:' in line:
            sw_platform = line.split(':')[1].strip()
            continue
    return sw_platform, content, param


# for parse image info
IMAGE_NAME_REGEX_MAPPING = {
    "4.6.0E_x86_or_x86h": re.compile(r'^PicOS-(?P<version>[a-zA-Z\d.]+)-EC\d+-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>x86h|x86)\.bin$'),
    "4.6.0E_S5860_or_S5810": re.compile(r'^(?P<platform>S5860|S5810)-PicOS-(?P<version>[a-zA-Z\d.]+)-EC\d+-(?P<revision>[a-zA-Z\d]+)\.bin$'),
    "4.6.0E_S3410_or_S3270": re.compile(r'^(?P<platform>S3410|S3270)-PicOS-(?P<version>[a-zA-Z\d.]+)-EC\d+-(?P<revision>[a-zA-Z\d]+)\.bin$'),
    "4.6.0E_DCN_x86_or_x86h": re.compile(r'^PicOS-(?P<version>[a-zA-Z\d.]+)-DCN-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>x86h|x86)\.bin$'),
    "4.6.0E_common": re.compile(r'^PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+)-(?P<platform>x86h|x86|as4610|N3000|N3100)\.bin$'),
    'white_box_stable_release': re.compile(r'^onie-installer-PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+)-(?P<platform>x86h|as4610|x86)\.bin$'),
    'black_box_stable_release': re.compile(r'^(?P<platform>[a-zA-Z\d]+)-PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+).bin$'),
    'white_box_stable_x86h_release': re.compile(r'^PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>x86h)\.bin$'),
    'black_box_x86_stable_release': re.compile(r'^PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>x86)\.bin$'),
    'old_white_box': re.compile(r'^onie-installer-picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-(?P<platform>x86|as4610|n3100|n3000)\.bin$'),
    'new_white_box_stable_release': re.compile(r'^onie-installer-picos-(?P<version>[\d.]+)-(?P<platform>[a-zA-Z\d]+)\.bin'),
    'new_white_box_transition_release': re.compile(r'^onie-installer-PICOS-(?P<version>[\d.]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
    'new_white_box_transition_research': re.compile(r'^onie-installer-PICOS-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
    'new_black_box_data_center_research': re.compile(r'^picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
    'new_black_box_campus_research': re.compile(r'^(?P<platform>[a-zA-Z\d]+)_picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs.bin$'),
    'new_black_box_data_center_release': re.compile(r'^picos-(?P<version>[\d.]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
    'new_black_box_campus_release': re.compile(r'^(?P<platform>[a-zA-Z\d]+)_picos-(?P<version>[\d.]+)-fs.bin$'),
    'new_s3410_busy_box_release': re.compile(r'^(?P<platform>[a-zA-Z\d]+)_picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+).bin$')
}


def get_image_type(image_name):
    for image_type, regex in IMAGE_NAME_REGEX_MAPPING.items():
        match = regex.match(image_name)
        if match:
            return image_type
    return None


def get_image_info(image_name):
    image_type = get_image_type(image_name)
    if not image_type:
        return None
    else:
        match = IMAGE_NAME_REGEX_MAPPING[image_type].match(image_name)
        if match:
            temp = match.groupdict()
            if temp['platform'] == 'x86h':
                temp['platform'] = 'x86'
            return {
                'version': temp['version'],
                'revision': temp['revision'] if 'revision' in temp else '',
                'platform': temp['platform'].lower(),
            }
        else:
            return None

def upgrade():
    ### commands auto generated by Alembic - please adjust! ###
    with open("default_imgs.json", "r") as f2:
        di = json.load(f2)

    with open("model_platform_mapping.json", "r") as f:
        mp = json.load(f)

    today = datetime.now()
    re_str = r'.*-(?P<version>(\d+\.){2,3}\d+)(-.*)?-(?P<revision>[\da-f]{7,}).*'
    connection = op.get_bind()

    for k, v in di.items():
        image_name = v["img"].split("/")[-1]
        image_path = "img/%s" % image_name
        image_md5_path = "img/%s" % v["md5"].split("/")[-1]
        # for black box x86 use different bin file
        if k == 'x86-fs':
            k = 'x86'
        elif k == 'x86h':
            k = 'x86'
        platform = k
        v_dict = get_image_info(image_name)
        version = v_dict['version']
        revision = v_dict['revision']
        init_switch_image_sql = "insert into switch_image_info(image_name, image_path, image_md5_path, platform, version, revision, create_time, modified_time) values ('{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}');"
        op.execute(init_switch_image_sql.format(image_name, image_path, image_md5_path, platform, version, revision, today, today))

    ret = connection.execute("select * from switch_systeminfo")
    for row in ret.fetchall():
        if di.get(mp[row["model"]], None) is None:
            continue
        up_to_date_image_path = "img/%s" % (di[mp[row["model"]]]["img"].split("/")[-1])
        up_to_date_image_md5_path = "img/%s" % (di[mp[row["model"]]]["md5"].split("/")[-1])
        v_dict = get_image_info(image_name)
        update_to_date_version = "%s/%s" % (v_dict['version'], v_dict['revision'])
        update_sql = "update switch_systeminfo set platform = '{}', up_to_date_version='{}', up_to_date_image_path='{}', up_to_date_image_md5_path='{}', up_to_date_onie_path='{}' where model='{}'"
        op.execute(update_sql.format(
            'x86' if mp[row["model"]] == 'x86-fs' or mp[row["model"]] == 'x86h' else mp[row["model"]], update_to_date_version, up_to_date_image_path, up_to_date_image_md5_path,
            up_to_date_image_path, row["model"]))
    
    source_path = os.path.join(os.path.abspath(__file__).split('server')[0], 'pre-built', 'template', 'default_prebuilt_template')
    for f in list(os.walk(source_path))[0][2]:
        with open(os.path.join(source_path, f)) as t:
            lines = t.readlines()
        sw_platform, content, param = parse_template(lines)
        try:
            op.execute(
                "INSERT INTO automation.general_template (create_time, modified_time, name, description, content, j2_template, params, internal) VALUES ('{}', '{}', '{}', '{}', null, '{}', '{}', 1);".format(
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"), datetime.now().strftime("%Y-%m-%d %H:%M:%S"), f,
                    f, aes_cipher.encrypt(content.replace('\'', r'\'')), aes_cipher.encrypt(param.replace('\'', r'\''))))
        except:
            pass

    source_path = os.path.join(os.path.abspath(__file__).split('server')[0], 'pre-built', 'playbook')
    for f in list(os.walk(source_path))[0][1]:
        os.system('cp -r {} {}'.format(os.path.join(source_path, f), '/home/<USER>/server/ansible_playbook/'))
        try:
            op.execute(
                "INSERT INTO automation.ansible_playbook (create_time, modified_time, create_user, name, description, internal) VALUES ('{}', '{}', '', '{}', '{}', 1);".format(
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"), datetime.now().strftime("%Y-%m-%d %H:%M:%S"), f, f))
        except:
            pass
    op.execute('commit')
    ### end Alembic commands ###


def downgrade():
    ### commands auto generated by Alembic - please adjust! ###
    pass
    ### end Alembic commands ###
