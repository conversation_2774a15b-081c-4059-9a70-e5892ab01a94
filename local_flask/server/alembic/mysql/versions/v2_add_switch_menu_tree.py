# revision identifiers, used by Alembic.
revision = 'v2'
down_revision = 'v1'
branch_labels = None
depends_on = None

from datetime import datetime
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

def upgrade():
    op.create_table('switch_ne_info',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('sn', sa.String(length=128), nullable=True),
                    sa.<PERSON>umn('switch_menu_tree_id', sa.Integer(), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )
    op.create_table('switch_menu_tree_info',
                    sa.Column('group_id', sa.Integer(), primary_key=True),
                    sa.Column('group_name', sa.String(length=128), nullable=True),
                    sa.Column('parent_group_id', sa.Integer(), nullable=True),
                    sa.Column('longitude', sa.Float(), nullable=True),
                    sa.Column('latitude', sa.Float(), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )
    op.execute("INSERT INTO switch_menu_tree_info(group_id, group_name, create_time, modified_time) VALUES (1, 'switchRoot', '{}','{}')".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S"), datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    op.create_index('idx_switch_sn', 'switch_ne_info', ['sn'], unique=False)
    op.create_foreign_key('switch_sn_fk', 'switch_ne_info', 'switch', ['sn'], ['sn'], ondelete='CASCADE')
    op.add_column('event', sa.Column('operator_name', sa.String(length=32), nullable=True))
    op.add_column('event', sa.Column('operator_text', sa.Text(), nullable=True))
    op.add_column('event', sa.Column('operator_time', sa.DateTime(), nullable=True))



def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('switch_menu_tree_info')
    op.drop_table('switch_ne_info')
    op.drop_column('event', 'operator_name')
    op.drop_column('event', 'operator_text')
    op.drop_column('event', 'operator_time')
    # ### end Alembic commands ###
