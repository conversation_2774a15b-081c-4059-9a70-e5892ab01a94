# revision identifiers, used by Alembic.
revision = 'v7'
down_revision = 'v6'
branch_labels = None
depends_on = None

from datetime import datetime
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

def upgrade():
    op.add_column('ansible_device', sa.Column('device_port', sa.String(length=32), server_default="22", nullable=True))
    op.add_column('ansible_device', sa.Column('device_ssh_key_path', sa.String(length=255), nullable=True))


def downgrade():
    op.drop_column('ansible_device', 'device_port')
    op.drop_column('ansible_device', 'device_ssh_key_path')
