Generic single-database configuration.

docs: http://docs.alembic.io/search.html

usage: alembic [-h] [-c CONFIG] [-n NAME] [-x X] [--raiseerr]
               {branches,current,downgrade,heads,history,init,list_templates,merge,revision,show,stamp,upgrade}
               ...

positional arguments:
  {branches,current,downgrade,heads,history,init,list_templates,merge,revision,show,stamp,upgrade}
    branches            Show current branch points
    current             Display the current revision for a database.
    downgrade           Revert to a previous version.
    heads               Show current available heads in the script directory
    history             List changeset scripts in chronological order.
    init                Initialize a new scripts directory.
    list_templates      List available templates
    merge               Merge two revisions together. Creates a new migration
                        file. .. versionadded:: 0.7.0 .. seealso::
                        :ref:`branches`
    revision            Create a new revision file.
    show                Show the revision(s) denoted by the given symbol.
    stamp               'stamp' the revision table with the given revision;
                        don't run any migrations.
    upgrade             Upgrade to a later version.

optional arguments:
  -h, --help            show this help message and exit
  -c CONFIG, --config CONFIG
                        Alternate config file
  -n NAME, --name NAME  Name of section in .ini file to use for Alembic config
  -x X                  Additional arguments consumed by custom env.py
                        scripts, e.g. -x setting1=somesetting -x
                        setting2=somesetting
  --raiseerr            Raise a full stack trace on error


steps:
1.
create a new version
alembic revision --autogenerate -m "${msg}"

2.
modify new generate script to adapt your change

3.
in new env
alembic upgrade head

4.downgrade last version
alembic downgrade -1

