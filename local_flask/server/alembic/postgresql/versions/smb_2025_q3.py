# revision identifiers, used by Alembic.

revision = 'smb_2025_q3'
down_revision = None
branch_labels = None
depends_on = None

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSON, JSONB
# from sqlalchemy.dialects import mysql
from sqlalchemy import inspect
from sqlalchemy.orm import Session
import os
import json

connection = op.get_bind()
inspector = inspect(connection)

def upgrade():
    print("Running upgrade: ampcon_smb_q3 started.")
    op.create_table(
        'wireless_profile',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('site_id', sa.Integer(), nullable=False),
        sa.Column('variable_id', sa.String(64), nullable=False),
        sa.Column('type', sa.Integer(), nullable=False, default=0),
        sa.Column('name', sa.String(64), nullable=False),
        sa.Column('parameter', sa.dialects.postgresql.JSONB()),
        sa.Column('description', sa.String(128)),
        sa.UniqueConstraint('site_id', 'name', 'type', name='uq_site_id_name_type'),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )

    op.create_index('idx_wireless_profile_type', 'wireless_profile', ['type'])
    op.create_index('idx_wireless_profile_site_id', 'wireless_profile', ['site_id'])

    op.create_table(
        'wireless_profile_usage',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('site_id', sa.Integer(), nullable=False),
        sa.Column('referee_id', sa.String(64), nullable=False),
        sa.Column('profile_id', sa.Integer(), nullable=False),
        sa.UniqueConstraint('referee_id', 'profile_id', name='uq_referee_id_profile_id'),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )

    op.create_table(
        'wireless_configure_ssid',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('site_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(64), nullable=False),
        sa.Column('security', sa.String(64)),
        sa.Column('radio', sa.String(64)),
        sa.Column('network_type', sa.Integer(), default=1),
        sa.Column('labels_name', sa.dialects.postgresql.JSONB(), default=list, server_default='[]', nullable=False),
        sa.Column('vlan_or_dhcp_name', sa.String(64)),
        sa.Column('is_enable', sa.Integer(), default=1),
        sa.Column('ssid_configure', sa.Text()),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )

    op.create_index('idx_wireless_configure_ssid_site_id', 'wireless_configure_ssid', ['site_id'])

    op.create_table('wireless_site_label',
        sa.Column('id', sa.Integer(), nullable=False, primary_key=True, autoincrement=True),
        sa.Column('site_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(64), nullable=False),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True),
        sa.UniqueConstraint('site_id', 'name', name='uq_wireless_site_label_site_id_name')
    )
    op.create_index('wireless_site_label_site_id', 'wireless_site_label', ['site_id'])

    # op.create_table(
    #     'wireless_rrm_task_log',
    #     sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    #     sa.Column('create_time', sa.DateTime(), nullable=False),
    #     sa.Column('modified_time', sa.DateTime(), nullable=False),
    #     sa.Column('site_id', sa.Integer(), nullable=False),
    #     sa.Column('trigger_time', sa.DateTime()),
    #     sa.Column('is_schedule_task', sa.Integer(), nullable=False, server_default='1'),
    #     sa.Column('online_num', sa.Integer(), nullable=False),
    #     sa.Column('success_num', sa.Integer(), nullable=False),
    #     sa.Column('failed_num', sa.Integer(), nullable=False),
    # )
    # op.create_unique_constraint('uq_wireless_rrm_task_log_id', 'wireless_rrm_task_log', ['id'])
    # op.create_index('idx_wireless_rrm_task_log_site_id', 'wireless_rrm_task_log', ['site_id'])

    # op.create_table(
    #     'wireless_rrm_task_result',
    #     sa.Column('task_id', sa.Integer(), nullable=False),
    #     sa.Column('sn', sa.String(64), nullable=False),
    #     sa.Column('create_time', sa.DateTime(), nullable=False),
    #     sa.Column('modified_time', sa.DateTime(), nullable=False),
    #     sa.Column('result_type', sa.Integer(), nullable=False, server_default='1'),
    # )
    # op.create_index('idx_wireless_rrm_task_result_task_id', 'wireless_rrm_task_result', ['task_id'])
    
    op.create_table(
        'wireless_channel',
        sa.Column('country_code', sa.String(16), nullable=False, primary_key=True),
        sa.Column('2g_channel', JSON, nullable=True),
        sa.Column('5g_channel', JSON, nullable=True),
        sa.Column('6g_channel', JSON, nullable=True)
    )
    # 设置表对象
    wireless_channel = sa.Table(
        'wireless_channel',
        sa.MetaData(),
        sa.Column('country_code', sa.String(16), primary_key=True),
        sa.Column('2g_channel', sa.JSON),
        sa.Column('5g_channel', sa.JSON),
        sa.Column('6g_channel', sa.JSON)
    )

    # load json file
    json_path = '/home/<USER>/server/postgresql/init-data/channel_info_by_country_2025_q3.json'
    if not os.path.exists(json_path):
        raise FileNotFoundError(f"File not found: {json_path}")

    with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    # bantch insert
    with Session(bind=connection) as session:
        session.execute(wireless_channel.insert(), data)
        session.commit()

    # op.create_table(
    #     'wireless_device_channel',
    #     sa.Column('sn', sa.String(64), nullable=False),
    #     # 以数字开头的列名需要用双引号括起来，或者使用下划线前缀。
    #     sa.Column('"2g_channel"', sa.String(256)),
    #     sa.Column('"5g_channel"', sa.String(256)),
    #     sa.Column('"6g_channel"', sa.String(256)),
    #     sa.PrimaryKeyConstraint('sn'),
    # )
    # op.create_unique_constraint('uq_wireless_device_channel_sn', 'wireless_device_channel', ['sn'])
    # op.create_index('idx_wireless_device_channel_sn', 'wireless_device_channel', ['sn'])
    
    op.create_table(
        'wireless_dhcp_service',
        sa.Column('create_time', sa.DateTime(), nullable=False),
        sa.Column('modified_time', sa.DateTime(), nullable=False),
        sa.Column('id', sa.Integer(), primary_key=True,nullable=False, autoincrement=True),
        sa.Column('site_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(64), nullable=False),
        sa.Column('subnet', sa.String(64), nullable=False),
        sa.Column('vlan', sa.String(64), nullable=False, default='-', server_default='-'),
        sa.Column('dhcp_configure', sa.JSON, nullable=False),
        sa.Column('description', sa.String(128), nullable=True),
        sa.UniqueConstraint('site_id', 'name', name='uq_site_id_name')

    )

    op.create_index('idx_wireless_dhcp_service_site_id', 'wireless_dhcp_service', ['site_id'])
    
    op.create_table(
        'wireless_ethernet_ports',
        sa.Column('create_time', sa.DateTime(), nullable=False),
        sa.Column('modified_time', sa.DateTime(), nullable=False),
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('site_id', sa.Integer(), nullable=False, index=True),
        sa.Column('port', sa.String(64), nullable=False),
        sa.Column('mac', sa.String(64)),
        sa.Column('network_type', sa.Integer(), nullable=False, server_default='1'),
        sa.Column('vlan_or_dhcp_name', sa.String(64), nullable=False, server_default=''),
        sa.Column('vlan_tag', sa.Integer(), nullable=False, server_default='1'),
    )

    op.create_index('idx_WireLess_ethernet_ports_site_id', 'wireless_ethernet_ports', ['site_id'])

    op.create_table(
        'wireless_client',
        sa.Column('create_time', sa.DateTime(), nullable=False),
        sa.Column('modified_time', sa.DateTime(), nullable=False),
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('status', sa.Integer(), nullable=False,default=1),
        sa.Column('host_name', sa.String(64),nullable=True),
        sa.Column('mac', sa.String(64),unique=True,nullable=False),
        sa.Column('vendor', sa.String(64),nullable=True),
        sa.Column('sn', sa.String(64), nullable=False),
        sa.Column('ssid', sa.String(64), nullable=False),
        sa.Column('rssi', sa.Integer(), nullable=True),
        sa.Column('band', sa.String(16), nullable=False),
        sa.Column('channel', sa.Integer(), nullable=True),
        sa.Column('channel_width', sa.Integer(), nullable=True),
        sa.Column('ip', sa.String(64), nullable=False),
        sa.Column('vlan', sa.Integer(), nullable=True),
        sa.Column('rx', sa.BigInteger(), nullable=True),
        sa.Column('tx', sa.BigInteger(), nullable=True),
        sa.Column('rx_packets', sa.BigInteger(), nullable=True),
        sa.Column('tx_packets', sa.BigInteger(), nullable=True),
        sa.Column('join_time', sa.DateTime(), nullable=False),
        sa.Column('leave_time', sa.DateTime(),nullable=True),
    )

    op.create_index('idx_wireless_client_host_name', 'wireless_client', ['host_name'])
    op.create_index('idx_wireless_client_mac', 'wireless_client', ['mac'])
    op.create_index('idx_wireless_client_vendor', 'wireless_client', ['vendor'])
    op.create_index('idx_wireless_client_sn', 'wireless_client', ['sn'])
    op.create_index('idx_wireless_client_ssid', 'wireless_client', ['ssid'])
    op.create_index('idx_wireless_client_ip', 'wireless_client', ['ip'])

    print("Running upgrade: ampcon_smb_q3 ended.")

def downgrade():
    print("Running downgrade: ampcon_smb_q3 started.")
    op.drop_table('wireless_configure_ssid')
    op.drop_table('wireless_profile_usage')
    op.drop_table('wireless_profile')
    op.drop_table('wireless_site_label')
    # op.drop_table('wireless_rrm_task_log')
    # op.drop_table('wireless_rrm_task_result')
    op.drop_table('wireless_channel')
    op.drop_table('wireless_dhcp_service')
    op.drop_table('wireless_ethernet_ports')
    

    print("Running downgrade: ampcon_smb_q3 ended.")

