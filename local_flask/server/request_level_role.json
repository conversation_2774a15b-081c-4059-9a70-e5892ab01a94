{"admin": {"permissions_exclude": []}, "operator": {"permissions_exclude": []}, "readonly": {"permissions_exclude": ["^/otn/api/ne/createFiber(/|\\?[^/]*|)$", "^/otn/api/ne/rpc(/|\\?[^/]*|)$", "^/otn/api/ne/change(/|\\?[^/]*|)$", "^/otn/api/ne/change_xml(/|\\?[^/]*|)$", "^/otn/api/data/add(/|\\?[^/]*|)$", "^/otn/api/data/arr_add(/|\\?[^/]*|)$", "^/otn/api/data/del(/|\\?[^/]*|)$", "^/otn/api/data/edit(/|\\?[^/]*|)$", "^/otn/api/data/update(/|\\?[^/]*|)$", "^/otn/api/nelist/add(/|\\?[^/]*|)$", "^/otn/api/nelist/del(/|\\?[^/]*|)$", "^/otn/api/file/upload(/|\\?[^/]*|)$", "^/otn/api/file/del(/|\\?[^/]*|)$", "^/otn/api/upgrade/run(/|\\?[^/]*|)$", "^/otn/api/upgrade/del(/|\\?[^/]*|)$", "^/otn/api/map/set_token(/|\\?[^/]*|)$", "^/otn/api/map/set_location(/|\\?[^/]*|)$", "^/otn/api/telemetry/clear_telemetry_data(/|\\?[^/]*|)$", "^/otn/api/telemetry/create_destination_group(/|\\?[^/]*|)$", "^/otn/api/clearHistoryAlarm(/|\\?[^/]*|)$"]}}