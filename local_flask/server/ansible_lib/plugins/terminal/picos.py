from __future__ import (absolute_import, division, print_function)
__metaclass__ = type

import re

from ansible.plugins.terminal import TerminalBase
from ansible.errors import AnsibleConnectionFailure


try:
    from __main__ import display
except ImportError:
    from ansible.utils.display import Display
    display = Display()


class TerminalModule(TerminalBase):

    terminal_stdout_re = [
        re.compile(br"[\r\n]?[\w@+\-\.:\/\[\]~]+[>#%$] ?$"),
    ]

    terminal_stderr_re = [
        re.compile(br"unknown command"),
        re.compile(br"syntax error"),
        re.compile(br"[\r\n]error:")
    ]

    #: terminal initial prompt
    terminal_initial_prompt = None

    #: terminal initial answer
    terminal_initial_answer = None

    #: Send newline after prompt match
    terminal_inital_prompt_newline = True

    def on_open_shell(self):
        try:
            prompt = self._get_prompt()
            if prompt.strip().endswith(b'>'):
                display.vvv('starting shell sh', self._connection._play_context.remote_addr)
                self._exec_cli_command('start shell sh')
            # for c in (b'set cli timestamp disable', b'set cli screen-length 0', b'set cli screen-width 1024'):
            #     self._exec_cli_command(c)
        except AnsibleConnectionFailure:
            raise AnsibleConnectionFailure('unable to set terminal parameters')

    def on_close_shell(self):
        """Called before the connection is closed

        This method gets called once the connection close has been requested
        but before the connection is actually closed.  It provides an
        opportunity to clean up any terminal resources before the shell is
        actually closed
        """
        pass

    def on_become(self, passwd=None):
        """Called when privilege escalation is requested

        :kwarg passwd: String containing the password

        This method is called when the privilege is requested to be elevated
        in the play context by setting become to True.  It is the responsibility
        of the terminal plugin to actually do the privilege escalation such
        as entering `enable` mode for instance
        """
        pass

    def on_unbecome(self):
        """Called when privilege deescalation is requested

        This method is called when the privilege changed from escalated
        (become=True) to non escalated (become=False).  It is the responsibility
        of this method to actually perform the deauthorization procedure
        """
        pass

    def on_authorize(self, passwd=None):
        """Deprecated method for privilege escalation

        :kwarg passwd: String containing the password
        """
        return self.on_become(passwd)

    def on_deauthorize(self):
        """Deprecated method for privilege deescalation
        """
        return self.on_unbecome()
