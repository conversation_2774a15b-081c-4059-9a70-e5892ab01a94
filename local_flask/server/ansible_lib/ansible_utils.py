import json
import logging
import os
from datetime import datetime

from server.ansible_lib.ansible_common import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GeneralCallback
from server.celery_app import my_celery_app
from server.celery_app.automation_task import AmpConBaseTask
from server.celery_app.roce_task import ROCETask
from server.constants import PICOS_V_USERNAME, PICOS_V_PASSWORD, AUTOMATION_BASE_DIR
from server.db.models import automation
from server.db.models.automation import AnsibleJob, AnsibleDevice
from server.db.models.automation import AnsibleJobResult
from server.db.models.inventory import inven_db, Switch, SystemConfig

automation_db = automation.automation_db

LOG = logging.getLogger(__name__)


def init_start_check(playbook_name, inventory_pool, username, passwd):
    if len(inventory_pool) == 0:
        LOG.error(":::No hosts is selected for playbook %s", playbook_name)
        return False

    playbook_entry = automation_db.get_collection(automation.Playbook, filters={'name': [playbook_name]})
    if not playbook_entry:
        LOG.error(":::Can not find the playbook %s", playbook_name)
        return False

    if username == '' or passwd == '':
        LOG.error(":::Can not find the username / password settings")
        return False

    return True


def generate_inventory_list(switch_list, group_list):
    inventory_pool = set()
    # handle switch_list
    for sw_sn in switch_list:
        inventory_pool.add(sw_sn)

    # handle group_list
    for group_name in group_list:
        groupQuery = inven_db.get_group_switchs(group_name)
        group_switch_sn = [i.switch_sn for i in groupQuery]
        for sw_sn in group_switch_sn:
            inventory_pool.add(sw_sn)

    inventory_pool = list(inventory_pool)
    return inventory_pool


def generate_job_name(playbook_name):
    return '{0}:::{1}'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'), playbook_name)


def get_username_passwd():
    session = automation_db.get_session()
    # todo system_config
    system_config = session.query(SystemConfig).first()
    if not system_config:
        # TODO: handle no username & password settings
        return '', ''
    username = system_config.switch_op_user
    passwd = system_config.switch_op_password
    return username, passwd


def add_job_record(job_name, playbook_name, playbook_path, schedule_type, schedule_params, create_user):
    new_job = AnsibleJob()
    new_job.name = job_name
    new_job.create_user = create_user
    new_job.playbook_name = playbook_name
    new_job.playbook_path = playbook_path
    new_job.schedule_type = schedule_type
    new_job.schedule_param = json.dumps(schedule_params)
    automation_db.insert(new_job)


@my_celery_app.task(name="ansible_job_start", base=AmpConBaseTask)
def ansible_job_start(playbook_name='', playbook_path='', job_name='', schedule_type='DIRECT', schedule_params={},
                      switch_list=[], group_list=[], _vars={}, **kwargs):
    other_device_info = kwargs.get("other_device", [])
    tmp_devices = dict()
    ip_name_dict = dict()
    if other_device_info:
        for device in other_device_info:
            device_name = device.get("device_name", "")
            username = device.get("device_user", "")
            passwd = device.get("device_pwd", "")
            device_ssh_key_path = device.get("device_ssh_key_path", "")
            device_ip = device.get("device_ip", "")
            device_port = device.get("device_port", "")
            if not device_ssh_key_path:
                if f"{username}&&{passwd}" in tmp_devices:
                    tmp_devices[f"{username}&&{passwd}&&{device_port}"].append(device_ip)
                else:
                    tmp_devices[f"{username}&&{passwd}&&{device_port}"] = [device_ip]
            else:
                if f"{username}&=&{device_ssh_key_path}&=&{device_port}" in tmp_devices:
                    tmp_devices[f"{username}&=&{device_ssh_key_path}&=&{device_port}"].append(device_ip)
                else:
                    tmp_devices[f"{username}&=&{device_ssh_key_path}&=&{device_port}"] = [device_ip]
            ip_name_dict[device_ip] = device_name

        playbook_entry = automation_db.get_collection(automation.Playbook, filters={'name': [playbook_name]})
        if not playbook_entry:
            LOG.error(":::Can not find the playbook %s", playbook_name)
            return False
        for user_info, hosts in tmp_devices.items():
            username, passwd, device_port = "", "", ""
            runner = PicaPlayBookRunner(playbook_name=playbook_name, job_name=job_name, host_sn_dict=dict((host, ip_name_dict[host]) for host in hosts))
            if "&=&" in user_info:
                username, key_file, device_port = user_info.split("&=&")
                _vars["private_key_file"] = key_file
            else:
                username, passwd, device_port = user_info.split("&&")
            _vars["ansible_ssh_port"] = device_port
            runner.run_playbook_file(username, passwd, playbook_path, hosts, _vars, use_local=False)
    if switch_list or group_list:
        # initialization
        inventory_pool = generate_inventory_list(switch_list=switch_list, group_list=group_list)
        username, passwd = get_username_passwd()
        # check status
        if not init_start_check(playbook_name=playbook_name, inventory_pool=inventory_pool, username=username,
                                passwd=passwd):
            # TODO: handle the check status not passed
            return

        # host should get from database from serice table
        inventory_pool = automation_db.get_collection(Switch, filters={'sn': inventory_pool})
        hosts = []
        host_sn_dict = {}
        hosts_picos_v = []
        host_sn_dict_picos_v = {}
        host_sn_dict_all = {}
        for switch in inventory_pool:
            host_sn_dict_all.update({switch.mgt_ip: switch.sn})
            if switch.sn != 'PICOS-V':
                hosts.append(switch.mgt_ip)
                host_sn_dict.update({switch.mgt_ip: switch.sn})
            else:
                hosts_picos_v.append(switch.mgt_ip)
                host_sn_dict_picos_v.update({switch.mgt_ip: switch.sn})

        _vars.update({'running_tag': job_name})

        runner = PicaPlayBookRunner(playbook_name=playbook_name, job_name=job_name, host_sn_dict=host_sn_dict_all)

        if hosts:
            runner.host_sn_dict = host_sn_dict
            runner.run_playbook_file(None, None, playbook_path, hosts, _vars)

        if hosts_picos_v:
            runner.host_sn_dict = host_sn_dict_picos_v
            runner.run_playbook_file(PICOS_V_USERNAME, PICOS_V_PASSWORD, playbook_path, hosts_picos_v, _vars)

    LOG.info(":::Finish to start a task to run playbook")


@my_celery_app.task(name="deploy_node_exporter", base=AmpConBaseTask)
def deploy_node_exporter(devices):
    tmp_devices = dict()
    ip_name_dict = dict()
    new_target = []
    _vars = {}
    job_name = '{0}:::{1}'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "enable_monitor")
    playbook_path = f"{AUTOMATION_BASE_DIR}/server/monitor/node_exporter/enable_node_exporter.yml"
    target_json = f"{AUTOMATION_BASE_DIR}/server/monitor/settings/node_target.json"
    for device in devices:
        device_name = device.get("device_name", "")
        username = device.get("device_user", "")
        passwd = device.get("device_pwd", "")
        device_ssh_key_path = device.get("device_ssh_key_path", "")
        device_ip = device.get("device_ip", "")
        device_port = device.get("device_port", "")
        device_sudo_pass = device.get("device_sudo_pass", "")
        if not device_ssh_key_path:
            if f"{username}&&{passwd}" in tmp_devices:
                tmp_devices[f"{username}&&{passwd}&&{device_port}"].append(device_ip)
            else:
                tmp_devices[f"{username}&&{passwd}&&{device_port}"] = [device_ip]
        else:
            if f"{username}&=&{device_ssh_key_path}&=&{device_port}" in tmp_devices:
                tmp_devices[f"{username}&=&{device_ssh_key_path}&=&{device_port}"].append(device_ip)
            else:
                tmp_devices[f"{username}&=&{device_ssh_key_path}&=&{device_port}"] = [device_ip]
        ip_name_dict[device_ip] = device_name

    for user_info, hosts in tmp_devices.items():
        username, passwd, device_port = "", "", ""
        runner = PicaPlayBookRunner(playbook_name="", job_name=job_name, host_sn_dict=dict((host, ip_name_dict[host]) for host in hosts))
        if "&=&" in user_info:
            username, key_file, device_port = user_info.split("&=&")
            _vars["private_key_file"] = key_file
        else:
            username, passwd, device_port = user_info.split("&&")
        _vars["ansible_ssh_port"] = device_port
        _vars["ansible_become_pass"] = device_sudo_pass
        runner.run_playbook_file(username, passwd, playbook_path, hosts, _vars, use_local=False)

    LOG.info(":::Finish enable monitor")

    for host in ip_name_dict.keys():
        new_target.append(host + ":9100")

    if not os.path.exists(target_json):
        data = [
            {
                "targets": list(set(new_target))
            }
        ]
        with open(target_json, 'w') as json_file:
            json.dump(data, json_file, indent=4)
    else:
        with open(target_json, 'r') as json_file:
            data = json.load(json_file)

        existing_targets = set()
        if data and "targets" in data[0]:
            existing_targets = set(data[0]["targets"])
        updated_targets = existing_targets.union(new_target)
        data[0]["targets"] = list(updated_targets)

        with open(target_json, 'w') as json_file:
            json.dump(data, json_file, indent=4)

    LOG.info(":::Finish reload prometheus")

    for device in devices:
        device_ip = device.get("device_ip", "")
        inven_db.update_switch_montior(name=device_ip, device_type=3)

    LOG.info(":::Finish update mysql ")


@my_celery_app.task(name="delete_deploy_node_exporter", base=AmpConBaseTask)
def delete_deploy_node_exporter(devices):
    tmp_devices = dict()
    ip_name_dict = dict()
    new_target = []
    _vars = {}
    job_name = '{0}:::{1}'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "delete_monitor")
    playbook_path = f"{AUTOMATION_BASE_DIR}/server/monitor/node_exporter/delete_node_exporter.yml"
    target_json = f"{AUTOMATION_BASE_DIR}/server/monitor/settings/node_target.json"
    for device in devices:
        device_name = device.get("device_name", "")
        username = device.get("device_user", "")
        passwd = device.get("device_pwd", "")
        device_ssh_key_path = device.get("device_ssh_key_path", "")
        device_ip = device.get("device_ip", "")
        device_port = device.get("device_port", "")
        device_sudo_pass = device.get("device_sudo_pass", "")
        if not device_ssh_key_path:
            if f"{username}&&{passwd}" in tmp_devices:
                tmp_devices[f"{username}&&{passwd}&&{device_port}"].append(device_ip)
            else:
                tmp_devices[f"{username}&&{passwd}&&{device_port}"] = [device_ip]
        else:
            if f"{username}&=&{device_ssh_key_path}&=&{device_port}" in tmp_devices:
                tmp_devices[f"{username}&=&{device_ssh_key_path}&=&{device_port}"].append(device_ip)
            else:
                tmp_devices[f"{username}&=&{device_ssh_key_path}&=&{device_port}"] = [device_ip]
        ip_name_dict[device_ip] = device_name

    for user_info, hosts in tmp_devices.items():
        username, passwd, device_port = "", "", ""
        runner = PicaPlayBookRunner(playbook_name="", job_name=job_name, host_sn_dict=dict((host, ip_name_dict[host]) for host in hosts))
        if "&=&" in user_info:
            username, key_file, device_port = user_info.split("&=&")
            _vars["private_key_file"] = key_file
        else:
            username, passwd, device_port = user_info.split("&&")
        _vars["ansible_ssh_port"] = device_port
        _vars["ansible_become_pass"] = device_sudo_pass
        runner.run_playbook_file(username, passwd, playbook_path, hosts, _vars, use_local=False)

    LOG.info(":::Finish delete monitor")

    for host in ip_name_dict.keys():
        new_target.append(host + ":9100")

    with open(target_json, 'r') as json_file:
        data = json.load(json_file)

    existing_targets = set()
    if data and "targets" in data[0]:
        existing_targets = set(data[0]["targets"])
    updated_targets = existing_targets - set(new_target)
    data[0]["targets"] = list(updated_targets)

    with open(target_json, 'w') as json_file:
        json.dump(data, json_file, indent=4)

    LOG.info(":::Finish reload prometheus")

    for device in devices:
        device_ip = device.get("device_ip", "")
        inven_db.delete_switch_montior(name=device_ip, device_type=3)

    LOG.info(":::Finish update mysql ")


@my_celery_app.task(name="execute_roce_script", base=ROCETask)
def execute_roce_script(*args, **kwargs):
    try:
        device_id = kwargs.get("device_id", "")
        if not device_id:
            raise Exception("Device id not found")

        scripts = kwargs.get("scripts", "")
        if not scripts:
            raise Exception("Scripts not found")

        task_name = kwargs.get("task_name", "")
        if not task_name:
            raise Exception("Task name not found")

        shell_env = kwargs.get("shell_env", "")

        from server.db.models.inventory import inven_db
        session = inven_db.get_session()
        device = session.query(AnsibleDevice).filter(AnsibleDevice.id == device_id).first()
        if not device:
            raise Exception("Device not found")

        from server.util.ssh_util import get_interactive_session, interactive_scripts_linux_with_conn
        from server.db.db_common import DBCommon
        from server import constants as C
        from server.celery_app.roce_task import RoceTask

        # 创建SSH连接
        ssh_session, status, ssh = get_interactive_session(device.ip,
                                                           username=device.device_user,
                                                           password=device.device_pwd,
                                                           timeout=60)
        if not ssh_session:
            raise Exception("Failed to get interactive session")

        result = ""
        shell_name = f"{task_name}.sh"
        try:
            # 使用SFTP将脚本传输到远程主机
            sftp = ssh.open_sftp()
            with sftp.file(shell_name, 'w+') as f:
                f.write(scripts)
            sftp.chmod(shell_name, 0o755)

            # 执行脚本
            res, status_code = interactive_scripts_linux_with_conn(ssh_session, shell_name, env=shell_env)
            result += res

            # 清理临时文件
            sftp.remove(shell_name)

            if status_code == C.RMA_ACTIVE and res:
                return True, res
            else:
                raise Exception("Failed to execute scripts: %s" % res)
        finally:
            if ssh:
                ssh.close()

    except Exception as e:
        LOG.error(f"Failed to execute scripts: {e}")
        raise e


@my_celery_app.task(name="roce_start_task_ansible", base=ROCETask)
def roce_start_task_ansible(*args, **kwargs):
    device_type = kwargs.get("device_type", "").lower()
    device_ip = kwargs.get('device_ip')
    username = kwargs.get('device_user')
    password = kwargs.get('device_pwd')
    device_name = kwargs.get("device_name", "")
    device_ssh_key_path = kwargs.get("device_ssh_key_path", "")
    device_port = kwargs.get("device_port", "")
    target_ports = kwargs.get("target_ports", "")
    device_sudo_pass = kwargs.get("device_sudo_pass", "")
    create_user = kwargs.get("create_user", "")
    script_params = kwargs.get("script_params", {})

    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    playbook_map = {
        "nvidia": {
            "playbook_name": "nvidia_roce_configure",
            "playbook_path": os.path.join(BASE_DIR, "monitor", "roce", "nvidia_configure_playbook.yaml")
        },
        "broadcom": {
            "playbook_name": "broadcom_roce_configure",
            "playbook_path": os.path.join(BASE_DIR, "monitor", "roce", "broadcom_configure_playbook.yaml")
        }
    }

    if device_type not in playbook_map:
        return False, f"Unknown vendor: {device_type}"

    playbook = playbook_map[device_type]
    playbook_name = playbook["playbook_name"]
    playbook_path = playbook["playbook_path"]

    _vars = {
        "target_ports": target_ports,
        "ansible_ssh_port": device_port,
        "ansible_become_pass": device_sudo_pass,
        "script_params": script_params
    }
    if device_ssh_key_path:
        _vars["private_key_file"] = device_ssh_key_path

    try:
        callback = GeneralCallback({device_ip: device_name})
        runner = PicaPlayBookRunner(
            playbook_name="",
            job_name="",
            host_sn_dict={device_ip: device_name},
            results_callback_class=lambda host_map: callback
        )
        runner.run_playbook_file(username, password, playbook_path, [device_ip], _vars, use_local=False)
        result_dict = callback.result_dict
    except Exception as e:
        return False, f"Ansible execution failed: {str(e)}"
    try:
        status, result_str = format_result_dict_with_prefix(callback.result_dict, device_ip, username)
        return status, result_str
    except Exception as e:
        LOG.error(f"Failed to package result for {device_ip}: {str(e)}")
        return False, f"Failed to package result: {str(e)}"


@my_celery_app.task(name="roce_check_task_ansible", base=ROCETask)
def roce_check_task_ansible(*args, **kwargs):
    # from celery.contrib import rdb; rdb.set_trace()
    return_info_list = []
    device_ip = kwargs.get('device_ip')
    username = kwargs.get('device_user')
    password = kwargs.get('device_pwd')
    device_name = kwargs.get("device_name", "")
    device_ssh_key_path = kwargs.get("device_ssh_key_path", "")
    device_port = kwargs.get("device_port", "")
    device_sudo_pass = kwargs.get("device_sudo_pass", "")
    create_user = kwargs.get("create_user", "")

    playbook_list = [
        {
            "playbook_name": "nvidia_roce_check",
            "playbook_path": f"{AUTOMATION_BASE_DIR}/server/monitor/roce/nvidia_roce_check.yml"
        },
        {
            "playbook_name": "broadcom_roce_check",
            "playbook_path": f"{AUTOMATION_BASE_DIR}/server/monitor/roce/broadcom_roce_check.yml"
        }
    ]
    LOG.info(f"-----------{playbook_list}")

    _vars = {
        "ansible_ssh_port": device_port,
        "ansible_become_pass": device_sudo_pass
    }
    if device_ssh_key_path:
        _vars["private_key_file"] = device_ssh_key_path

    for playbook in playbook_list:
        playbook_name = playbook.get("playbook_name")
        playbook_path = playbook.get("playbook_path")

        job_name = '{0}:::{1}'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'), playbook_name)

        add_job_record(
            job_name=job_name,
            playbook_name=playbook_name,
            playbook_path=playbook_path,
            schedule_type='DIRECT',
            schedule_params={},
            create_user=create_user
        )

        runner = PicaPlayBookRunner(
            playbook_name=playbook_name,
            job_name=job_name,
            host_sn_dict={device_ip: device_name}
        )
        runner.run_playbook_file(username, password, playbook_path, [device_ip], _vars, use_local=False)

        LOG.info(":::Finish roce check")

        status, result_str = package_roce_check_result_and_insert(job_name, device_ip, username)
        return_info_list.append({
            "status": status,
            "result_str": result_str
        })
    nvidia_roce_check_status = return_info_list[0].get("status")
    nvidia_roce_check_result = return_info_list[0].get("result_str")
    broadcom_roce_check_status = return_info_list[1].get("status")
    broadcom_roce_check_result = return_info_list[1].get("result_str")
    if nvidia_roce_check_status:
        return nvidia_roce_check_status, nvidia_roce_check_result
    if broadcom_roce_check_status:
        return broadcom_roce_check_status, broadcom_roce_check_result
    if not nvidia_roce_check_status and not broadcom_roce_check_status:
        res_str = f"{nvidia_roce_check_result}\n{broadcom_roce_check_result}"
        return False, res_str


def package_roce_check_result_and_insert(job_name, device_ip, username):
    """
    整理result数据
    """
    prefix = f"{device_ip}@{username}:/$ "
    check_result_str_list = []
    check_status = 1
    db_session = inven_db.get_session()
    try:
        ansible_job_result_list = db_session.query(AnsibleJobResult).filter(AnsibleJobResult.job_name == job_name).all()
        for item in ansible_job_result_list:
            check_status &= item.state
            result_dict = json.loads(item.result)
            if not isinstance(result_dict, dict):
                continue
            if "unreachable" in result_dict and result_dict["unreachable"]:
                return False, result_dict.get("msg")
            if "results" in result_dict:  # 处理loop循环
                for result in result_dict["results"]:
                    if isinstance(result, dict) and result.get("cmd"):
                        prefix_cmd = f"{prefix}{result.get('cmd')}"
                        check_result_str_list.append(prefix_cmd)
                        check_result_str_list.append(result.get("stdout"))
            elif result_dict.get("cmd"):
                prefix_cmd = f"{prefix}{result_dict.get('cmd')}"
                check_result_str_list.append(prefix_cmd)
                check_result_str_list.append(result_dict.get("stdout"))
        check_result_str = "\n".join(check_result_str_list)
        LOG.info("-------------------------------")
        LOG.info(f"---------- {bool(check_status)} :: {check_result_str}")
        LOG.info("-------------------------------")
        print(f"---------- {bool(check_status)} :: {check_result_str}")
        return bool(check_status), check_result_str
    except Exception as e:
        print(f"Error: device info query failed.\n{str(e)}")
        LOG.info(f"Error: device info query failed.\n{str(e)}")
        raise
    finally:
        db_session.close()


def format_result_dict_with_prefix(result_dict, device_ip, username):
    prefix = f"{device_ip}@{username}:/$ "
    output_lines = []

    try:
        for host, task_dict in result_dict.items():
            for task_name, task_info_list in task_dict.items():
                for idx, task_info in enumerate(task_info_list):
                    result_raw = task_info.get("result", "{}")
                    try:
                        result_data = json.loads(result_raw)
                    except Exception:
                        result_data = {}
                    if isinstance(result_data, dict) and "results" in result_data:
                        for res in result_data["results"]:
                            cmd = res.get("cmd")
                            stdout = res.get("stdout", "")
                            if cmd:
                                output_lines.append(f"{prefix}{cmd}")
                            if stdout:
                                output_lines.append(stdout)
                    else:
                        cmd = None
                        stdout = None

                        if isinstance(result_data, dict):
                            cmd = result_data.get("cmd")
                            stdout = result_data.get("stdout")
                        if not cmd:
                            cmd = task_info.get("cmd")

                        if cmd:
                            output_lines.append(f"{prefix}{cmd}")
                        if stdout:
                            output_lines.append(stdout)

    except Exception as e:
        return False, f"Error while formatting result dict: {str(e)}"
    return True, "\n".join(output_lines)
