#!/usr/bin/env python
import datetime
import json
import logging
import os
import time
from collections.abc import Iterable

import ansible.constants as C
from ansible import context
from ansible.errors import AnsibleError
from ansible.executor import playbook_executor
from ansible.executor.task_queue_manager import TaskQueueManager
from ansible.inventory.manager import InventoryManager
from ansible.module_utils.common.collections import ImmutableDict
from ansible.parsing.dataloader import DataLoader
from ansible.playbook.block import Block
from ansible.playbook.play import Play
from ansible.playbook.play_context import PlayContext
from ansible.plugins.callback import CallbackBase
from ansible.plugins.loader import inventory_loader
from ansible.vars.manager import VariableManager

from server import constants
from server.constants import AUTOMATION_BASE_DIR
from server.db.models.automation import automation_db
from server.db.models.inventory import Switch, SwitchConfigSnapshot, SwitchParking
from server.db.models.inventory import inven_db as db
from server.db.models.monitor import monitor_db
from server.util import str_helper, utils

C.INVENTORY_ENABLED = ('host_list_args', 'auto')
C.HOST_KEY_CHECKING = False
inventory_loader.add_directory(os.path.join(os.path.dirname(__file__), 'plugins/inventory'))

LOG = logging.getLogger(__name__)


def run_ping(user, password, hosts, sn):
    play_source = dict(
        name="PicOS switch play",
        hosts="all",
        gather_facts='no',
        tasks=[dict(name='ping test', action=dict(module='ping'))]
    )
    callback = ResultCallback(sn)
    run_playbook(user, password, play_source, hosts, callback=callback)
    return callback.result['ping test']['category'] == 'ok'


def service_task(name, service, action, become='yes', tags=[]):
    return dict(name=name, action=dict(module='service', args=dict(name=service, state=action)), become=become,
                tags=tags)


def copy_task(name, src, dest, user, mode='0777', become='no', tags=[]):
    return dict(name=name, action=dict(module='copy', args=dict(src=src, dest=dest, owner=user, mode=mode)),
                become=become, tags=tags)


def shell_task(name, cmd, tags=[], **kwargs):
    return dict(name=name, action=dict(module='shell', args=cmd), tags=tags, **kwargs)


def command_task(name, cmd, tags=[]):
    return dict(name=name, action=dict(module='command', args=cmd), tags=tags)


def lineinfile_task(name, args, tags=[]):
    return dict(name=name, action=dict(module='lineinfile', args=args), tags=tags)


def blockinfile_task(name, args, tags=[]):
    return dict(name=name, action=dict(module='blockinfile', args=args), tags=tags)


def file_task(name, args, tags=[], **kwargs):
    return dict(name=name, action=dict(module='file', args=args), tags=tags, **kwargs)


def fetch_task(name, src, dest, tags=[]):
    return dict(name=name, action=dict(module='fetch', args=dict(src=src, dest=dest, flat='yes')), tags=tags)


def meta_task(name, cmd, tags=[]):
    return dict(name=name, action=dict(module='meta', args=cmd), tags=tags)


def run_tasks(user, password, tasks, hosts, callback=CallbackBase()):
    play_source = dict(
        name="PicOS switch play",
        hosts="all",
        gather_facts='no',
        tasks=tasks
    )
    return run_playbook(user, password, play_source, hosts, callback=callback)


def run_playbook(user, password, play_source, hosts, callback=CallbackBase()):
    context.CLIARGS = ImmutableDict(connection='ssh', module_path=[''], forks=1, become=None, become_method=None,
                                    become_user=None,
                                    remote_user=user,
                                    check=False, diff=False,
                                    ssh_common_args='-o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no',
                                    extra_vars=[{'ansible_ssh_user': user, 'ansible_ssh_pass': password,
                                                 'StrictHostKeyChecking': 'no',
                                                 'ansible_remote_tmp': '/home/<USER>/.ansible/tmp',
                                                 'ansible_python_interpreter': '/usr/bin/python'}],
                                    verbosity=3
                                    )

    play_source['tasks'].insert(1, shell_task('modify ansible temp dircroty RW', "sudo chmod -R 777 %s" % constants.ANSIBLE_TMP))
    loader = DataLoader()
    passwords = dict(vault_pass='secret')

    inventory = InventoryManager(loader=loader, sources=','.join(hosts) + ',')

    variable_manager = VariableManager(loader=loader, inventory=inventory)

    play = Play().load(play_source, variable_manager=variable_manager, loader=loader)

    tqm = None
    try:
        tqm = TaskQueueManager(
            inventory=inventory,
            variable_manager=variable_manager,
            loader=loader,
            passwords=passwords,
            stdout_callback=callback,
        )
        result = tqm.run(play)
        return result
    finally:
        if tqm is not None:
            tqm.cleanup()

        if loader:
            loader.cleanup_all_tmp_files()


class ResultCallback(CallbackBase):
    """A sample callback plugin used for performing an action as results come in
    If you want to collect all results into a single object for processing at
    the end of the execution, look into utilizing the ``json`` callback plugin
    or writing your own custom callback plugin
    """

    TIME_FORMAT = "%b %d %Y %H:%M:%S"

    BEFORE_LONG_TIME_TASKS = {'restart picos': 'start to restart picos, will be finished by 2-10 minutes',
                              'push image file': 'start to push image file, will be finished by 10-30 minutes',
                              'run upgrade task': 'start to upgrade image, will be finished by 3-15 minutes',
                              'push full config task': 'start to execute config full configs, will be finished by 2-10 minutes',
                              'restart picos after exec full config': 'start to restart picos, will be finished by 2-10 minutes'}

    def __init__(self, sn):
        super(ResultCallback, self).__init__()
        self.sn = sn
        self.result = {}
        self.VALIDATE_TASKS = {'security validate': self.validate_security,
                               'pt security validate': self.validate_parking_lot_security,
                               'show license': self.validate_license,
                               'patch tar validate': self.validate_tar_file,
                               'full config validate': self.validate_full_config,
                               'rma full config validate': self.validate_rma_full_config,
                               'retrieve config': self.retrive_config,
                               'show hostname': self.update_hostname}

    def add_long_task_log(self, key, value):
        self.BEFORE_LONG_TIME_TASKS.update({key: value})

    def _add_result(self, result, category):
        log_result = {
            'task_name': result.task_name,
            'timestamp': time.strftime(self.TIME_FORMAT, time.localtime()),
            'category': category,
            'host': result._host,
            'result': result._result,
            'task_fields': result._task_fields
        }
        self.result[result.task_name] = log_result
        return log_result

    def v2_runner_on_ok(self, result, **kwargs):
        """Print a json representation of the result
        This method could store the result in an instance attribute for retrieval later
        """
        try:
            self._add_result(result, 'ok')
            self.handle_ok(result)
        except Exception as e:
            LOG.exception(e)

    def v2_runner_on_failed(self, result, **kwargs):
        """Print a json representation of the result
        This method could store the result in an instance attribute for retrieval later
        """
        try:
            self._add_result(result, 'failed')
            self.handle_failed(result)
        except Exception as e:
            LOG.exception(e)

    def v2_runner_on_unreachable(self, result, **kwargs):
        """Print a json representation of the result
        This method could store the result in an instance attribute for retrieval later
        """
        try:
            self._add_result(result, 'unreachable')
            self.handle_unreachable(result)
        except Exception as e:
            LOG.exception(e)

    def handle_ok(self, result):
        task_name = result.task_name
        task_fields = result._task_fields or {}
        task_result = result._result
        LOG.debug('%s %s result:[%s]', self.sn, task_name, task_result)
        db.clear_deployed_security(self.sn)
        try:
            stdout = task_result.get('stdout', None)
            stderr = task_result.get('stderr', None)
            if stdout or stderr:
                stdout_str = 'stdout:\n%s \n' % stdout if stdout else ''
                stderr_str = 'stderr:\n%s \n' % stderr if stderr else ''
                log = '%s [%s] %s %s' % (self.sn, task_name, stdout_str, stderr_str)
                log = log[:60000]
                LOG.debug(log)
                db.add_switch_log(self.sn, log, level='debug')

            log = '%s finish [%s]' % (self.sn, task_name)
            LOG.info(log)
            db.add_switch_log(self.sn, log)
        except Exception as e:
            LOG.exception(e)

        if task_name in ['security validate', 'pt security validate']:
            db.add_deployed_security(self.sn)

        try:
            # validate tasks
            if task_name in self.VALIDATE_TASKS.keys():
                db.add_switch_log(self.sn,
                                  'start to validate %s' % task_name.replace('show', '').replace('validate', ''))
                if not self.VALIDATE_TASKS[task_name](task_result):
                    db.update_status(self.sn, 'Provisioning Failed')
                    os._exit(1)
        except Exception as e:
            LOG.exception(e)

        if task_name == 'save_config after complete':
            db.update_status(self.sn, 'Provisioning Success')
            return

        try:
            # update step
            tag = task_fields.get('tags', None)
            if tag:
                step = int(tag[0])
                db.update_step(self.sn, step)
                if step == 6:
                    db.update_status(self.sn, 'Provisioning Success')
                    flag = False
                    while not flag:
                        switch = db.get_model(Switch, filters={'sn': [self.sn]})
                        if switch.status != 'Provisioning Success':
                            db.update_status(self.sn, 'Provisioning Success')
                        else:
                            flag = True
                    db_session = db.get_session()
                    with db_session.begin():
                        switch = db.get_model(Switch, filters={'sn': [self.sn]})
                        parking = db_session.query(SwitchParking).filter(SwitchParking.sn == self.sn).first()
                        if parking:
                            switch.hwid = parking.hardware_id
                    monitor_db.add_event(self.sn, 'info', 'switch %s deploy success' % self.sn)
                    db.add_switch_log(self.sn, 'Task: End of Deployed    ::: Result: Success', level='info')
                    utils.send_email_with_event(self.sn, 'success')
        except Exception as e:
            LOG.exception(e)

        db.add_switch_log(self.sn, 'Task: [%s]    ::: Result: Success' % task_name, level='info')

    def handle_failed(self, result):
        task_result = result._result
        task_name = result.task_name
        log = '%s %s stdout:\n %s, stderr:\n %s' % (self.sn, task_name,
                                                    task_result.get('stdout', ''),
                                                    task_result.get('stderr', ''))
        LOG.error(log)
        if task_name == 'ping test':
            return
        db.add_switch_log(self.sn, log, level='error')
        db.add_switch_log(self.sn, 'Task: [%s]     ::: Result: Failed' % task_name, level='error')
        log = '%s [%s] failed' % (self.sn, task_name)
        LOG.error('%s [%s] failed result[%s]', self.sn, task_name, task_result)
        db.update_status(self.sn, 'Provisioning Failed')
        monitor_db.add_event(self.sn, 'error', log)

    def handle_unreachable(self, result):
        task_result = result._result
        task_name = result.task_name
        msg = task_result.get('msg', '')
        if 'Authentication' in msg:
            db.invalid_deployed_security(self.sn)
            log = '%s [%s] failed with switch authentication failed' % (self.sn, task_name)
        else:
            log = '%s [%s] failed with switch unreachable' % (self.sn, task_name)
        LOG.error('%s result[%s]', self.sn, task_result)
        if task_name == 'ping test':
            return
        db.update_status(self.sn, 'Provisioning Failed')
        monitor_db.add_event(self.sn, 'error', '%s task unreachable %s' % (self.sn, task_name))
        db.add_switch_log(self.sn, log, level='error')

    def playbook_on_task_start(self, name, is_conditional):

        if name in self.BEFORE_LONG_TIME_TASKS.keys():
            LOG.info('%s %s', self.sn, self.BEFORE_LONG_TIME_TASKS[name])
            db.add_switch_log(self.sn, '%s %s' % (self.sn, self.BEFORE_LONG_TIME_TASKS[name]), level='debug')
        else:
            LOG.info('%s start to run [%s]', self.sn, name)

    def validate_security(self, result):
        if not self.ensure_stdout('validate security', result):
            return False

        stdout = result['stdout']

        mgt_sys_config = db.get_system_config_by_sn(self.sn)
        # switch = db.get_switch_info_by_sn(self.sn)
        # db_configs = switch.configs
        # regional_config = filter(lambda config: config.type == 'regional', db_configs)
        # need_config_str = regional_config[0].config + '\n'
        # update switch user
        need_config_str = mgt_sys_config.get_security_config_content()

        correct, diffs = str_helper.compare_cmd_lines(need_config_str, stdout)
        diff_str = '\n'.join(diffs)
        if not correct:
            LOG.error('validate switch %s security config failed: miss following configs:\n %s', self.sn,
                      diff_str)
            db.add_switch_log(self.sn,
                              'validate switch security config failed: miss following configs:\n %s' %
                              diff_str, level='error')
            db.add_switch_log(self.sn, 'Task: Validate security config    ::: Result: Failed', level='error')
            monitor_db.add_event(self.sn, 'error',
                                 'validate switch security config failed: miss following configs:\n %s' % diff_str)
            return False

        LOG.info('%s validate security config success, no missed configs', self.sn)
        db.add_switch_log(self.sn,
                          'validate security config success, no missed configs', level='info')
        db.add_switch_log(self.sn, 'Task: Validate security config    ::: Result: Success', level='info')
        return True

    def validate_parking_lot_security(self, result):
        if not self.ensure_stdout('validate parking lot security', result):
            return False

        stdout = result['stdout']

        mgt_sys_config = db.get_system_config_by_sn(self.sn)
        need_config_str = mgt_sys_config.get_parking_security_config_content()
        need_config_str.strip()
        correct, diffs = str_helper.compare_cmd_lines(need_config_str, stdout)
        diff_str = '\n'.join(diffs)
        if not correct:
            LOG.error('validate switch %s parking lot security config failed: miss following configs:\n %s', self.sn,
                      diff_str)
            db.add_switch_log(self.sn,
                              'validate switch parking lot security config failed: miss following configs:\n %s' %
                              diff_str, level='error')
            monitor_db.add_event(self.sn, 'error',
                                 'validate switch parking lot security failed: miss following configs:\n %s' % diff_str)
            return False

        LOG.info('%s validate parking lot security config success, no missed configs', self.sn)
        db.add_switch_log(self.sn,
                          'validate parking lot security config success, no missed configs',
                          level='info')
        return True

    def validate_license(self, result):
        if 'stderr' not in result or result['stderr'] == '':

            if 'stdout' not in result:
                msg = result['msg'] if 'msg' in result else 'unreachable'
                LOG.error('validate switch %s license failed: %s', self.sn, msg)
                db.add_switch_log(self.sn, 'validate switch license failed: %s' %
                                  msg, level='error')
                db.add_switch_log(self.sn, 'Task: Validate license    ::: Result: Failed', level='error')
                monitor_db.add_event(self.sn, 'error', 'validate switch license failed: %s' % msg)
                return False

            result = result['stdout']
            result = json.loads(result)
            expire_date = result['Support End Date']
            expr_date = expire_date.split('-')
            if datetime.datetime(int(expr_date[0]), int(expr_date[1]), int(expr_date[2])) > datetime.datetime.now():
                LOG.info('validate switch %s license success [%s]', self.sn, result)
                db.add_switch_log(self.sn, 'validate license success, switch license info: \n %s' % result,
                                  level='info')
                db.add_switch_log(self.sn, 'Task: Validate license    ::: Result: Success', level='info')
                return True
            else:
                LOG.error('validate switch %s license failed: license had expired %s', self.sn, expire_date)
                db.add_switch_log(self.sn, 'Task: Validate license    ::: Result: Failed', level='error')
                db.add_switch_log(self.sn, 'validate switch license failed: %s' %
                                  result['stderr'], level='error')
                return False
        elif result['stderr'] != '':
            LOG.error('validate switch %s license failed: %s', self.sn, result['stderr'])
            db.add_switch_log(self.sn, 'validate switch license failed: %s' %
                              result['stderr'], level='error')
            db.add_switch_log(self.sn, 'Task: Validate license    ::: Result: Failed', level='error')
            return False

    def validate_tar_file(self, result):
        stdout = result['stdout']

        if result['rc'] != 0:
            LOG.error('%s validate patch tar install failed, stdout:[%s]', self.sn, result['stdout'])
            db.add_switch_log(self.sn, 'validate switch patch tar install failed: %s' %
                              stdout, level='error')
            db.add_switch_log(self.sn, 'Task: Validate patch tar install    ::: Result: Failed', level='error')
            monitor_db.add_event(self.sn, 'error', 'validate switch patch tar install failed: %s' % stdout)
            return False

        db.add_switch_log(self.sn,
                          'validate patch tar install success, stdout [%s]' % stdout, level='info')
        db.add_switch_log(self.sn, 'Task: Validate patch tar install    ::: Result: Success', level='info')

        return True

    def validate_full_config(self, result):
        if not self.ensure_stdout('validate full config', result):
            return False

        stdout = result['stdout']
        switch = db.get_switch_info_by_sn(self.sn)
        db_configs = switch.configs
        config_str = ''
        for config in db_configs:
            config_str += config.config + '\n'

        general_configs = switch.general_configs
        for config in general_configs:
            config_str += config.content

        correct, diffs = str_helper.compare_cmd_lines(config_str, stdout)
        if not correct:
            diff_str = '\n'.join(diffs)
            LOG.error('validate switch %s full config failed: miss following configs:\n %s', self.sn,
                      diff_str)
            db.add_switch_log(self.sn,
                              'validate switch full config failed: miss following configs:\n %s' %
                              diff_str, level='error')
            db.add_switch_log(self.sn, 'Task: Validate full config    ::: Result: Failed', level='error')
            monitor_db.add_event(self.sn, 'error',
                                 'validate switch full config failed: miss following configs:\n %s' % diff_str)
            return False

        LOG.info('%s validate full config success, no missed configs', self.sn)

        db.add_switch_log(self.sn,
                          'validate full config success, no missed configs', level='info')
        db.add_switch_log(self.sn, 'Task: Validate full config    ::: Result: Success', level='info')
        return True

    def validate_rma_full_config(self, result):
        if not self.ensure_stdout('validate rma full config', result):
            return False

        stdout = result['stdout']

        config = db.get_switch_back_sn(self.sn)
        stdout = stdout.replace('\r', '')
        correct, diff_lines = str_helper.compare_tree_configs(config, stdout)
        if not correct:
            diff_str = '\n'.join(diff_lines) if len(diff_lines) < 100 else 'different line number is ' + str(
                len(diff_lines))
            LOG.error('validate switch %s rma full config failed: miss following configs:\n %s', self.sn,
                      diff_str)
            db.add_switch_log(self.sn,
                              'validate switch rma full config failed: miss following configs:\n %s' %
                              diff_str, level='error')
            db.add_switch_log(self.sn, 'Task: Validate rma full config    ::: Result: Failed', level='error')
            monitor_db.add_event(self.sn, 'error',
                                 'validate switch rma full config failed: miss following configs:\n %s' % diff_str)
            return True

        LOG.info('%s validate rma full config success, no missed configs:', self.sn)

        db.add_switch_log(self.sn,
                          'validate rma full config success, no missed configs', level='info')
        db.add_switch_log(self.sn, 'Task: Validate rma full config    ::: Result: Success', level='info')
        return True

    def retrive_config(self, result):
        if not self.ensure_stdout('retrive_config', result):
            return False

        stdout = result['stdout']
        content = stdout.replace('\r\n', '\n')
        switch = db.get_switch_info_by_sn(self.sn)
        ip = switch.mgt_ip if switch.mgt_ip else switch.tmp_ip
        db.insert_or_update_back(ip, content, constants.RMA_BACK_AUTO, self.sn)
        # need also add in snapshot
        current_time = datetime.datetime.utcnow()
        db_session = db.get_session()
        with db_session.begin():
            uptodate_snapshot_list = db_session.query(SwitchConfigSnapshot).filter(
                SwitchConfigSnapshot.sn == self.sn).order_by(SwitchConfigSnapshot.snapshot_time.desc()).all()
            if uptodate_snapshot_list:
                if uptodate_snapshot_list[0].archive_config != content:
                    db_session.add(SwitchConfigSnapshot(sn=self.sn,
                                                        snapshot_time=current_time,
                                                        archive_config=str(content).encode()))
            else:
                db_session.add(SwitchConfigSnapshot(sn=self.sn,
                                                    snapshot_time=current_time, archive_config=str(content).encode()))
        return True

    def ensure_stdout(self, task_name, result):
        if 'stdout' not in result:
            msg = result['msg'] if 'msg' in result else 'unreachable'
            LOG.error('%s %s failed: %s', self.sn, task_name, msg)
            db.add_switch_log(self.sn, '%s failed: %s' %
                              (task_name, msg), level='error')
            db.add_switch_log(self.sn, 'Task: %s    ::: Result: Failed' % task_name, level='error')
            monitor_db.add_event(self.sn, 'error', '%s failed: %s' % (task_name, msg))
            return False
        return True

    def update_hostname(self, result):
        if not self.ensure_stdout('show hostname', result):
            return True

        stdout = result['stdout']
        db.update_model(Switch, filters={'sn': [self.sn]}, updates={'host_name': stdout.strip()})
        return True


class CollectCallback(CallbackBase):
    def __init__(self, host_map):
        super(CollectCallback, self).__init__()
        self.host_map = host_map
        self.handler = dict()

    def register_handler(self, task_name, handler):
        self.handler.update({task_name: handler})

    def v2_runner_on_ok(self, result, **kwargs):
        host = result._host
        sn = self.host_map[host]
        LOG.info('collect switch [sn:%s,host:%s] configs success', sn, host)
        handler = self.handler.get(result.task_name)
        if callable(handler):
            handler(sn, result)

    def v2_runner_on_failed(self, result, **kwargs):
        host = result._host
        sn = self.host_map[host]
        msg = result._result['msg'] or result._result['stderr']
        LOG.info('collect switch [sn:%s,host:%s] configs failed [%s]', sn, host, msg)

    def v2_runner_on_unreachable(self, result, **kwargs):
        host = result._host
        sn = self.host_map[host]
        LOG.info('collect switch [sn:%s,host:%s] configs failed, switch unreachable', sn, host)


class SwitchOperCallback(CallbackBase):
    def __init__(self, host_map):
        super(SwitchOperCallback, self).__init__()
        self.host_map = host_map
        self.handler = dict()

    def register_handler(self, task_name, handler):
        self.handler.update({task_name: handler})

    def v2_runner_on_ok(self, result, **kwargs):
        host = result._host
        sn = self.host_map[host]
        LOG.info('Operate switch [sn:%s,host:%s] success', sn, host)
        handler = self.handler.get(result.task_name)
        if callable(handler):
            handler(sn, result)

    def v2_runner_on_failed(self, result, **kwargs):
        host = result._host
        sn = self.host_map[host]
        msg = result._result['msg'] or result._result['stderr']
        LOG.info('Operate switch [sn:%s,host:%s] failed [%s]', sn, host, msg)

    def v2_runner_on_unreachable(self, result, **kwargs):
        host = result._host
        sn = self.host_map[host]
        LOG.info('Operate switch [sn:%s,host:%s] failed, switch unreachable', sn, host)


class SwitchGeneralCallback(CallbackBase):

    def __init__(self, host_map, playbook_name, job_name):
        super(SwitchGeneralCallback, self).__init__()
        self.host_map = host_map
        self.handler = dict()
        self.playbook_name = playbook_name
        self.job_name = job_name

    def _add_ansible_result(self, result, result_type):  # result_type: ok, failed, unreachable
        task_name = result.task_name
        task_result = result._result

        LOG.info('%s %s result:[%s]', self.host_map[result._host.name], task_name, task_result)

        stdout = task_result.get('stdout', None)
        stderr = task_result.get('stderr', None)
        if stdout or stderr:
            stdout_str = 'stdout:\n%s \n' % stdout if stdout else ''
            stderr_str = 'stderr:\n%s \n' % stderr if stderr else ''
            log = '%s [%s] %s %s' % (self.host_map[result._host.name], task_name, stdout_str, stderr_str)
            log = log[:60000]

        log = '%s finish [%s]' % (self.host_map[result._host.name], task_name)

        if 'delta' in task_result.keys():
            duration = task_result['delta']
        else:
            duration = "0:00:00.000000"

        if 'start' in task_result.keys():
            runtime = datetime.datetime.strptime(task_result['start'], '%Y-%m-%d %H:%M:%S.%f')
        else:
            runtime = datetime.datetime.strptime('{0}'.format(datetime.datetime.now()), '%Y-%m-%d %H:%M:%S.%f')

        task_state = True if result_type == 'ok' else False
        if result.task_name != 'Gathering Facts':
            try:
                automation_db.add_ansible_result(task_name=result.task_name, switch_sn=self.host_map[result._host.name],
                                                 job_name=self.job_name,
                                                 start_time=runtime, duration=duration, state=task_state,
                                                 result=json.dumps(task_result, sort_keys=True, indent=4))
            except Exception as e:
                LOG.error(e)
        elif result_type != 'ok':
            try:
                automation_db.add_ansible_result(task_name='job_initialization',
                                                 switch_sn=self.host_map[result._host.name], job_name=self.job_name,
                                                 start_time=runtime, duration=duration, state=task_state,
                                                 result=json.dumps(task_result, sort_keys=True, indent=4))
            except Exception as e:
                LOG.error(e)

    def register_handler(self, task_name, handler):
        self.handler.update({task_name: handler})

    def v2_runner_on_ok(self, result, **kwargs):
        host = result._host
        sn = self.host_map[host.name]
        LOG.info('Operate switch [sn:%s,host:%s] success' % (sn, host))
        self._add_ansible_result(result, 'ok')
        handler = self.handler.get(result.task_name)
        if callable(handler):
            handler(sn, result)

    def v2_runner_on_failed(self, result, **kwargs):
        host = result._host
        sn = self.host_map[host.name]
        msg = result._result['msg'] or result._result['stderr']
        LOG.info('Operate switch [sn:%s,host:%s] failed [%s]' % (sn, host, msg))
        self._add_ansible_result(result, 'failed')

    def v2_runner_on_unreachable(self, result, **kwargs):
        host = result._host
        sn = self.host_map[host.name]
        LOG.info('Operate switch [sn:%s,host:%s] failed, switch unreachable' % (sn, host))
        self._add_ansible_result(result, 'unreachable')


class GeneralCallback(CallbackBase):

    def __init__(self, host_map):
        super(GeneralCallback, self).__init__()
        self.host_map = host_map
        self.handler = dict()
        self.result_dict = {}

    def _add_ansible_result(self, result, result_type):  # result_type: ok, failed, unreachable
        task_name = result.task_name
        task_result = result._result

        LOG.info('%s %s result:[%s]', self.host_map[result._host.name], task_name, task_result)

        stdout = task_result.get('stdout', None)
        stderr = task_result.get('stderr', None)
        if stdout or stderr:
            stdout_str = 'stdout:\n%s \n' % stdout if stdout else ''
            stderr_str = 'stderr:\n%s \n' % stderr if stderr else ''
            log = '%s [%s] %s %s' % (self.host_map[result._host.name], task_name, stdout_str, stderr_str)
            log = log[:60000]

        log = '%s finish [%s]' % (self.host_map[result._host.name], task_name)

        if 'delta' in task_result.keys():
            duration = task_result['delta']
        else:
            duration = "0:00:00.000000"

        if 'start' in task_result.keys():
            runtime = datetime.datetime.strptime(task_result['start'], '%Y-%m-%d %H:%M:%S.%f')
        else:
            runtime = datetime.datetime.strptime('{0}'.format(datetime.datetime.now()), '%Y-%m-%d %H:%M:%S.%f')

        sn = self.host_map[result._host.name]
        task_state = True if result_type == 'ok' else False

        if sn not in self.result_dict:
            self.result_dict[sn] = {}

        if task_name not in self.result_dict[sn]:
            self.result_dict[sn][task_name] = []

        self.result_dict[sn][task_name].append({
            "state": task_state,
            "duration": duration,
            "runtime": runtime.strftime('%Y-%m-%d %H:%M:%S'),
            "result": json.dumps(task_result, sort_keys=True, indent=4)
        })

    def register_handler(self, task_name, handler):
        self.handler.update({task_name: handler})

    def v2_runner_on_ok(self, result, **kwargs):
        host = result._host
        sn = self.host_map[host.name]
        LOG.info('Operate switch [sn:%s,host:%s] success' % (sn, host))
        self._add_ansible_result(result, 'ok')
        handler = self.handler.get(result.task_name)
        if callable(handler):
            handler(sn, result)

    def v2_runner_on_failed(self, result, **kwargs):
        host = result._host
        sn = self.host_map[host.name]
        msg = result._result.get('msg') or result._result.get('stderr') or 'No error message available'
        LOG.info('Operate switch [sn:%s,host:%s] failed [%s]' % (sn, host, msg))
        self._add_ansible_result(result, 'failed')

    def v2_runner_on_unreachable(self, result, **kwargs):
        host = result._host
        sn = self.host_map[host.name]
        LOG.info('Operate switch [sn:%s,host:%s] failed, switch unreachable' % (sn, host))
        self._add_ansible_result(result, 'unreachable')


class PicaPlayBookRunner:

    def __init__(self, playbook_name, job_name, host_sn_dict, results_callback_class=None):
        self.playbook_name = playbook_name
        self.job_name = job_name
        self.host_sn_dict = host_sn_dict
        if results_callback_class:
            self.results_callback_class = results_callback_class(host_sn_dict)
        else:
            self.results_callback_class = SwitchGeneralCallback(host_sn_dict, playbook_name, job_name)

    def run_playbook_file(self, user, password, playbooks, hosts, vars, use_local=True):
        use_system_config_credentials = True
        private_key_file = vars.get("private_key_file")
        ansible_ssh_port = vars.get("ansible_ssh_port")
        # user and password for switch
        if user and password:
            use_system_config_credentials = False
        if use_system_config_credentials:
            if private_key_file:
                extra_vars = [{'ansible_ssh_user': user, "ansible_ssh_private_key_file": private_key_file, 'StrictHostKeyChecking': 'no'}]
            else:
                extra_vars = [{'StrictHostKeyChecking': 'no'}]
        else:
            extra_vars = [{'ansible_ssh_user': user, 'ansible_ssh_pass': password, 'StrictHostKeyChecking': 'no'}]
        C.DEFAULT_ROLES_PATH = [f'{AUTOMATION_BASE_DIR}/server/ansible_playbook/']

        if use_local:
            extra_vars[0]['ansible_python_interpreter'] = '/usr/bin/python'
            extra_vars[0]['ansible_remote_tmp'] = '/home/<USER>/.ansible/tmp'

        if ansible_ssh_port:
            extra_vars[0]['ansible_ssh_port'] = ansible_ssh_port

        extra_vars[0].update(vars)

        context.CLIARGS = ImmutableDict(
            verbosity=vars.get('verbosity', 4),
            listhosts=vars.get('listhosts', None),
            subset=vars.get('subset', None),
            module_path=vars.get('module_path', [f'{AUTOMATION_BASE_DIR}/server/ansible_lib/']),
            forks=vars.get('forks', 5),
            ask_vault_pass=vars.get('ask_vault_pass', False),
            vault_password_files=vars.get('vault_password_files', []),
            new_vault_password_files=vars.get('new_vault_password_files', []),
            vault_ids=vars.get('vault_ids', []),
            new_vault_id=vars.get('new_vault_id', None),
            tags=vars.get('tags', ['all']),
            skip_tags=vars.get('skip_tags', []),
            ask_pass=vars.get('ask_pass', False),
            private_key_file=vars.get('private_key_file', ''),
            remote_user=user,
            connection=vars.get('connection', 'smart'),
            timeout=vars.get('timeout', 10),
            ssh_common_args=vars.get('ssh_common_args', '-o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no'),
            sftp_extra_args=vars.get('sftp_extra_args', ''),
            scp_extra_args=vars.get('scp_extra_args', ''),
            ssh_extra_args=vars.get('ssh_extra_args', ''),
            sudo=vars.get('sudo', False),
            sudo_user=vars.get('sudo_user', None),
            su=vars.get('su', False),
            su_user=vars.get('su_user', None),
            become=vars.get('become', False),
            become_method=vars.get('become_method', 'sudo'),
            become_user=vars.get('become_user', 'root'),
            ask_sudo_pass=vars.get('ask_sudo_pass', False),
            ask_su_pass=vars.get('ask_su_pass', False),
            become_ask_pass=vars.get('become_ask_pass', False),
            check=vars.get('check', False),
            syntax=vars.get('syntax', None),
            diff=vars.get('diff', False),
            force_handlers=vars.get('force_handlers', False),
            flush_cache=vars.get('flush_cache', None),
            listtasks=vars.get('listtasks', None),
            listtags=vars.get('listtags', None),
            step=vars.get('step', None),
            start_at_task=vars.get('start_at_task', None),
            passwords=vars.get('passwords', None),
            host_key_checking=vars.get('host_key_checking', False),
            command_warnings=vars.get('command_warnings', False),
            gathering=vars.get('gathering', "smart"),
            extra_vars=extra_vars
        )

        playbooks = playbooks if type(playbooks) == list else [playbooks]
        loader = DataLoader()
        passwords = dict(vault_pass='secret')
        inventory = InventoryManager(loader=loader, sources=','.join(hosts) + ',')
        variable_manager = VariableManager(loader=loader, inventory=inventory)

        if use_system_config_credentials:
            for host in inventory.get_hosts():
                system_config_used = db.get_system_config_by_mgt_ip(host.get_vars().get('inventory_hostname', None))
                host.set_variable('ansible_ssh_user', system_config_used.switch_op_user)
                host.set_variable('ansible_ssh_pass', system_config_used.switch_op_password)

        executor = playbook_executor.PlaybookExecutor(
            playbooks=playbooks,
            inventory=inventory,
            variable_manager=variable_manager,
            loader=loader,
            passwords=passwords
        )
        if executor._tqm and self.results_callback_class:
            executor._tqm._stdout_callback = self.results_callback_class

        automation_db.update_ansible_job_status(self.job_name, 'RUNNING')
        try:
            results = executor.run()
            automation_db.update_ansible_job_status(self.job_name, 'FINISHED')
            if not isinstance(results, Iterable):
                return self.results_callback_class
            for p in results:
                for _, play in enumerate(p['plays']):
                    mytags = set(play.tags)
                    if play._included_path is not None:
                        loader.set_basedir(play._included_path)
                    else:
                        pb_dir = os.path.realpath(os.path.dirname(p['playbook']))
                        loader.set_basedir(pb_dir)

                    if context.CLIARGS['listtags'] or context.CLIARGS['listtasks']:
                        all_vars = variable_manager.get_vars()
                        play_context = PlayContext(play=play)
                        all_tags = set()

                        def _process_block(b):
                            taskmsg = ''
                            for task in b.block:
                                if isinstance(task, Block):
                                    taskmsg += _process_block(task)
                                else:
                                    if task.action == 'meta':
                                        continue

                                    all_tags.update(task.tags)
                                    if context.CLIARGS['listtasks']:
                                        cur_tags = list(mytags.union(set(task.tags)))
                                        cur_tags.sort()
                                        if task.name:
                                            taskmsg += "      %s" % task.get_name()
                                        else:
                                            taskmsg += "      %s" % task.action
                                        taskmsg += "\tTAGS: [%s]\n" % ', '.join(cur_tags)

                            return taskmsg

                        for block in play.compile():
                            block = block.filter_tagged_tasks(play_context, all_vars)
                            if not block.has_tasks():
                                continue
                            _process_block(block)

        except AnsibleError as e:
            automation_db.update_ansible_job_status(self.job_name, 'FINISHED')
            for _, sn in self.host_sn_dict.items():
                duration = "0:00:00.000000"
                runtime = datetime.datetime.strptime('{0}'.format(datetime.datetime.now()), '%Y-%m-%d %H:%M:%S.%f')
                automation_db.add_ansible_result(task_name='playbook_initialization', switch_sn=sn,
                                                 job_name=self.job_name,
                                                 start_time=runtime, duration=duration, state=False,
                                                 result='Error in running playbook: \n' + str(e))

            executor._tqm.cleanup()
            loader.cleanup_all_tmp_files()
            raise e

    @staticmethod
    def check_palybook_host_is_all(playbook_path):

        if not os.path.isfile(playbook_path):
            return False, 'Error in running playbook: playbook file {0} not existed'.format(playbook_path)

        content_list = ''
        with open(playbook_path) as f:
            content_list = f.readlines()

        for line in content_list:
            if 'hosts' in line and ':' in line:
                if [i.strip() for i in line.split(':')] == ['hosts', 'all']:
                    return True, ''

        return False, 'Error in running playbook: \n the field "hosts" is required and the value should be "all"'
