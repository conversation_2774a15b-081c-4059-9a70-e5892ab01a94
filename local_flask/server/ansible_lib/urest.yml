---
- hosts: debian

  vars:
    licurl: license.pica8.com
    user_name: "ans"
    user_pswd: "ans"
    user_auth: {"username": "{{ user_name }}","password": "{{ user_pswd }}","description": "license user"}
    lic_10g_of: {
        "type":"2",
        "feature":"3",
        "hardware_id":"FA2E-795C-63FB-0001",
        "name":"API license",
        "expiry_date_flag": "true",
        "purge_flag": "false"
    }

  tasks:  
    - name: Authenticate & retrieve APIKEY
      uri:
        url: https://{{ licurl }}/auth
        method: POST
        headers:
          Content-Type: "application/json"
        #body: "{{ lookup('file','user.json') }}"
        body: "{{ user_auth }}"
        body_format: json
        validate_certs: no
        return_content: yes
        follow_redirects: yes
      register: restdata

    #- debug: var=restdata.content
    - debug: 
        msg: "API Key: {{ restdata.content }}"

    - set_fact:
        token: "{{ restdata.content }}"
      when: "not restdata.failed"

    # skip inventory - server error

    - name: License Count
      uri:
        url: https://{{ licurl }}/license_count
        method: GET
        headers:
          Content-Type: "application/json"
          Authorization: "Bearer {{ token }}"
        body: {"speed_type":"2","feature_type":"3","mode":"switch"}
        body_format: json
        validate_certs: no
        return_content: yes
        follow_redirects: yes
      register: restdata

    - debug:
        msg: "Account has {{ restdata.content }} x 10G Openflow licenses"

    - name: License Exists
      uri:
        url: https://{{ licurl }}/license_exists
        method: GET
        headers:
          Content-Type: "application/json"
          Authorization: "Bearer {{ token }}"
        body: {"hardware_id": "{{ lic_10g_of.hardware_id }}"}
        body_format: json
        validate_certs: no
        return_content: yes
        follow_redirects: yes
      register: restdata

    - debug: 
        msg: "License {{ lic_10g_of.hardware_id }} exists? {{ restdata.content }}"

    - name: Create license
      uri:
        url: https://{{ licurl }}/get_license_key
        method: POST
        headers:
          Content-Type: "application/json"
          Authorization: "Bearer {{ token }}"
        #body: "{{ lookup('file','lic.json') }}"
        body: "{{ lic_10g_of }}"
        body_format: json
        validate_certs: no
        return_content: yes
        follow_redirects: yes
      register: restdata

    - debug: var=restdata

    - set_fact:
        key: "{{ restdata.content }}"
      when: "'OK' in restdata.msg"

    - debug: 
        msg: "Created license key: {{ key }}"
      when: "'OK' in restdata.msg"

    - name: License Details
      uri:
        url: https://{{ licurl }}/get_license_details
        method: GET
        headers:
          Content-Type: "application/json"
          Authorization: "Bearer {{ token }}"
        body: {"hardware_id": "{{ lic_10g_of.hardware_id }}"}
        body_format: json
        validate_certs: no
        return_content: yes
        follow_redirects: yes
      register: restdata

    #- debug: var=restdata

    - set_fact:
        lic_info: "{{ restdata.json }}"
      when: "not restdata.failed"

    - debug:
        msg: "License details: {{ lic_info }}"
      when: "not restdata.failed"

    - name: Check License Key Status
      uri:
        url: https://{{ licurl }}/check_license
        method: GET
        headers:
          Content-Type: "application/json"
          Authorization: "Bearer {{ token }}"
        #body: {"hardware_id": "011B-7030-EE95-3010"}
        body: {"hardware_id": "{{ lic_10g_of.hardware_id }}"}
        body_format: json
        validate_certs: no
        return_content: yes
        follow_redirects: yes
      register: restdata

    - debug: var=restdata
        #msg: "License {{ lic_10g_of.hardware_id }} status: {{ restdata.content }}"
      when: "not restdata.failed"

