import jwt
import datetime
import hashlib

from server.util import utils
from server.db.models.monitor import monitor_db
from server.db.models.user import user_db


def web_auth(username, password):
	if username and password:
		tac_status = utils.tacacs_auth(username, password)
		if tac_status[0]:
			if tac_status[1][0]:
				# tacacs+ authentication success
				return True, {}, tac_status[1][1], tac_status[2][0]
			else:
				msg = {'msg': 'Username or password is incorrect for TACACS+ authentication'}
				return False, msg, ''

		user = user_db.query_user(user=username)
		if user and user.check_password_hash(password):
			return True, {}, user.type, user.user_type
		else:
			msg = {'msg': 'Username or Password is incorrect'}
			return False, msg, '', ''
	else:
		msg = {'msg': 'Username or Password is empty'}
		return False, msg, '', ''


###
# Authorization class

class Auth_Handles(object):
	__instance = None
	
	def __init__(self, randomkey):
		self.j_algorithm = 'HS256'
		self.randomkey = randomkey
		self.other_lock = False
	
	def __new__(cls, *args, **kwargs):
		if cls.__instance is None:
			cls.__instance = super(Auth_Handles, cls).__new__(cls)
		return cls.__instance
	
	def get_jwt_token(self, user, password):
		res_msg = {}
		timenow = datetime.datetime.utcnow()
		j_timestamp = timenow + datetime.timedelta(seconds=1800)
		j_role = 0
		password = hashlib.sha256(password.encode()).hexdigest()
		j_payload = {
			'exp': j_timestamp,
			'name': user,
			'role': j_role,
			'password': password
		}
		res_msg['exp'] = j_timestamp
		j_encoded = jwt.encode(j_payload, self.randomkey[0], algorithm=self.j_algorithm)
		return j_encoded, res_msg

	def auth_jwt_token(self, token):	
		ret = False
		msg = "Authorization fail"
		try:
			j_decoded = jwt.decode(token, self.randomkey[0], algorithms=[self.j_algorithm])
			if j_decoded['password'] != '':
				return True, "Authorized"
			else:
				return ret, msg
		except jwt.ExpiredSignatureError as err:
			ret = False
			msg = str(err)
		except jwt.DecodeError as err:
			ret = False
			msg = str(err)
				
		return ret, msg
	
	def decode_name(self, token):
		try:
			j_decoded = jwt.decode(token, self.randomkey[0], algorithms=[self.j_algorithm])
			name = j_decoded['name'] if j_decoded['name'] else ''
			return name
		except jwt.ExpiredSignatureError as err:
			return ''
		except jwt.DecodeError as err:
			return ''
