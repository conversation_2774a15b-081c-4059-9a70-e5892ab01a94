
from ryu.base import app_manager
from ryu.controller import ofp_event
from ryu.controller.handler import MAIN_DISPATCHER,DEAD_DISPATCHER
from ryu.controller.handler import set_ev_cls
from ryu.ofproto import ofproto_v1_3
from ryu.lib.packet import packet
from ryu.lib.packet import ethernet
from ryu.lib.packet import vlan
from ryuswitch import RyuSwitch
from ryu.controller import dpset
from ryu.lib import hub
import re, json
import sys, os
sys.path.append('/home/<USER>/automation/')
from server import cfg
cfg.CONF(args=[], default_config_files=['/home/<USER>/automation/server/automation.ini'])

from server.db.models.sdn_access import HostAccessControl, SdnAccessSwitch, SdnAccessPolicy
from server.db.models.inventory import Switch, SystemConfig, inven_db
from server.db.models.sdn_access import sdn_access_db
from server.db.models.monitor import monitor_db
from server.util.switch_paramiko_util import ssh_execute_with_host


class UserHost(object):

    def __init__(self, smac=None, dpid=None, port=None):
        self.smac = smac
        # configured_vlan will be VLAN-ID, if VLAN =0, means it will be drop
        self.learned_vlan = ''
        self.prcessed = False
        #assigned_vlan_id, if 0 means drop, '' means do nothing, just forwarding, .1-4094. means will change to configured VLAN
        self.assigned_vlan_id = ''
        self.portName = ''
        self.host_pkt = 0
        self.host_byte = 0
        self.queue = 0
        self.meter = 0


class HostAccessController(app_manager.RyuApp):

    def __init__(self):
        self.switch_configured = self.get_all_switch_from_db()
        # get all configured MAC from DB
        self.host_configured = {}
        self.update_configured_mac_from_db()

    def update_configured_mac_from_db(self):
        # read all configured host from DB
        rt_host = sdn_access_db.get_collection(HostAccessControl, filters={'known_host': [True]})
        # host_configured_dict = {}
        for host in rt_host:
            self.host_configured.update({host.smac: host})

    # smac will be a inst of UserHost
    def add_new_host(self, host=None):
        self.update_configured_mac_from_db()
        self.update_single_host_flow(host)

    def del_host(self, host=None):
        self.host_configured.pop(host)
        sdn_access_db.delete_collection(HostAccessControl, filters={'smac': [host['smac']]})

    def get_all_switch_from_db(self):
        # read all configured host from DB, should be dict, with key dpid
        rt_switch = sdn_access_db.get_collection(SdnAccessSwitch, filters={})
        switch_configured_dict = {}
        for sw in rt_switch:
            switch_configured_dict.update({sw:sw})
        return switch_configured_dict

    # switch will be sn
    def add_new_configured_switch(self, switch=None):
        self.switch_configured.append(switch)

    def sync_configured_switch(self):
        rt_switch = sdn_access_db.get_collection(SdnAccessSwitch, filters={})
        for sw in rt_switch:
            if sw.dpid not in self.switch_configured.keys():
                self.switch_configured.update({sw:sw})

    def del_configured_switch(self, switch=None):
        self.switch_configured.remove(switch)

    def add_bypass_policy(self, policy=None):
        for sn in self.sdnSwitch.keys():
            if self.sdnSwitch[sn].connected:
                self.sdnSwitch[sn].add_flow(policy)

    def del_bypass_policy(self, policy=None):
        for sn in self.sdnSwitch.keys():
            if self.sdnSwitch[sn].connected:
                # add the dpid in policy flow ruls
                self.sdnSwitch[sn].delete_flow(policy)

    def sync_bypass_policy(self):
        # get from db
        configured_policy_list = sdn_access_db.get_collection(SdnAccessPolicy)
        for dpid in self.sdnSwitch.keys():
            if self.sdnSwitch[dpid].connected:
                # start to sync the policy flows
                filter = '{"priority": 300}'
                policy_bypass_in_switch_list = self.sdnSwitch[dpid].get_flows(filters=filter)
                for configured_policy in configured_policy_list:
                    if not self.compare_flows(configured_policy, policy_bypass_in_switch_list):
                        match = json.loads(configured_policy.flow_string)
                        # process the protocol fields
                        actions = []
                        if configured_policy.flow_action == 'drop':
                            flow_rule = {
                                "dpid": self.sdnSwitch[dpid].DPID,
                                "idle_timeout": 0, "hard_timeout": 0, "priority": 300,
                                "flags": 1,
                                "match": match,
                                "actions": actions
                            }
                        else:
                            actions.append({"type": "OUTPUT", "port": "normal"})
                        flow_rule = {
                            "dpid": self.sdnSwitch[dpid].DPID,
                            "idle_timeout": 0, "hard_timeout": 0, "priority": 300,
                            "flags": 1,
                            "match": match,
                            "actions": actions
                        }
                        self.sdnSwitch[dpid].add_flow(flow_rule)
                        self.logger.info('::::install a flow not existing in switch %016x , flow: %s', dpid, flow_rule)
                for switch_policy in policy_bypass_in_switch_list.values()[0]:
                    if not self.compare_flows_reverse(switch_policy, configured_policy_list):
                        flow_rule = {
                            "dpid": self.sdnSwitch[dpid].DPID,
                            "idle_timeout": 0, "hard_timeout": 0, "priority": 300,
                            "flags": 1,
                            "match": switch_policy['match']
                        }
                        self.sdnSwitch[dpid].delete_flow(flow_rule)
                        self.logger.info('::::delete a flow not existing in DB %016x , flow: %s', dpid, flow_rule)

    def compare_flows(self, db_flow, switch_flow_list):
        # compare the flow with databse format and switch respond,
        # true means have flows, false means need reinstall flow
        switch_match_list = []
        for flow in switch_flow_list.values()[0]:
            if 'dl_type' in flow['match'].keys():
                flow['match'].update({'eth_type': str(flow['match']['dl_type'])})
                flow['match'].pop('dl_type')
            switch_match_list.append(flow['match'])
        if json.loads(db_flow.flow_string) in switch_match_list:
            return True
        else:
            return False

    def compare_flows_reverse(self, switch_flow, db_flow_list):
        # compare the flow with databse format and switch respond, true means have flows, false means need remove flow
        db_match_list = []
        for flow in db_flow_list:
            db_match_list.append(json.loads(flow.flow_string))
        if switch_flow['match'] in db_match_list:
            return True
        else:
            return False

    def update_single_host_flow(self, host):
        self.update_configured_mac_from_db()
        for dp in self.sdnSwitch.values():
            if dp.connected:
                filter = '{"priority": 100, "match": {"dl_src": "'+ host['smac'] + '"}}'
                flow_rt = dp.get_flows(filters=filter)
                if flow_rt[str(dp.DPID)]:
                    # delete and reinstall flow
                    flow_rule = {
                        "dpid": dp.DPID,
                        "priority": 100,
                        "flags": 1,
                        "match": {"dl_src": host['smac']}
                    }
                    dp.delete_flow(flow_rule)
                    self.install_mac_flow(dp.DPID, host['smac'])

    def get_all_host_flows(self):
        for switch in self.sdnSwitch.values():
            if switch.connected:
                flows = switch.get_flows({"priority": 100})

    def sync_switch(self, dpid=None):
        self.install_default_drop_flow(dpid)
        # get the sn
        switch = sdn_access_db.get_collection(SdnAccessSwitch, filters={'dpid': [dpid]})
        if switch:
            sn = switch[0].controlled_switch
        # get the mgt-ip
        switch_info = inven_db.get_collection(Switch, filters={'sn': [sn]})
        system_config = inven_db.get_system_config_by_sn(sn=sn)
        user = system_config.switch_op_user
        pw = system_config.switch_op_password
        if switch_info:
            mgt_ip = switch_info[0].mgt_ip
        # get the MAC table string
        o, e, e_s = ssh_execute_with_host(mgt_ip, user, pw, '/pica/bin/lcmgr/tools/print_fdb -f 2 | grep xorp')
        mac_list = re.findall("[a-z0-9][a-z0-9]:[a-z0-9][a-z0-9]:[a-z0-9][a-z0-9]:[a-z0-9][a-z0-9]:[a-z0-9][a-z0-9]:[a-z0-9][a-z0-9]", o)
        # start to sync the mac to flow, firstly, get the flow mac and compare with MAC learning
        filter = '{"priority": 100}'
        flows_rt = self.sdnSwitch[dpid].get_flows(filter)
        mac_flow = []
        for single_flow in flows_rt[str(dpid)]:
            mac_flow.append(single_flow['match'])
        for mac in mac_list:
            if mac in self.host_configured.keys():
                mac_match_dict = {"dl_src": mac}
                ip = self.host_configured[mac].sip
                if ip and ip != '':
                    mac_match_dict.update({"nw_src": ip})
                    # because return string is dl_type, not eth_type
                    mac_match_dict.update({"dl_type": 2048})
                if mac_match_dict not in mac_flow:
                    # install flow for that mac
                    self.install_mac_flow(dpid,mac)
        self.logger.info('finished sync with datapath: %016x', dpid)

    def del_configured_switch(self, switch=None):
        self.switch_configured.pop(switch)
        pass

    def add_bypass_policy(self, policy=None, dpid=None):
        pass

    def del_bypass_policy(self, switch=None, dpid=None):
        pass

    @set_ev_cls(ofp_event.EventOFPStateChange, [MAIN_DISPATCHER, DEAD_DISPATCHER])
    def state_change_handler(self, ev):
        datapath = ev.datapath
        # sync the configured switch firstly
        self.sync_configured_switch()
        if ev.state == MAIN_DISPATCHER:
            if datapath.id in self.switch_configured.keys():
                if datapath.id not in self.datapaths.keys():
                    self.logger.info('register datapath: %016x', datapath.id)
                    monitor_db.add_event('sdn_access_control', 'info', 'New Switch '+ str(datapath.id) + ' connected')
                    self.datapaths[datapath.id] = datapath
                    self.sdnSwitch[datapath.id] = sdnAccessSwitch(dpid=datapath.id, connected=True)
                    # send a bridge dump-desc query to get the sn,maybe we need not this
                    self._request_desc(datapath=datapath)
                    # TBD: need to do sync
                    self.install_default_drop_flow(datapath.id)
                    self.sync_switch(datapath.id)
                else:
                    self.sdnSwitch[datapath.id].connected = True
                    self.logger.info('existing register datapath: %016x(Reconnected)', datapath.id)
                    self.install_default_drop_flow(datapath.id)
                    # TBD: need to do sync
                    self.sync_switch(datapath.id)
            else:
                self.logger.info('datapath: %016x NOT in configured switch', datapath.id)
        elif ev.state == DEAD_DISPATCHER:
            if datapath.id in self.datapaths:
                if datapath.id not in self.switch_configured.keys():
                    self.logger.info('unregister datapath: %016x', datapath.id)
                    monitor_db.add_event('sdn_access_control', 'info', 'Switch '+ str(datapath.id) + ' connected')
                    del self.datapaths[datapath.id]
                    del self.sdnSwitch[datapath.id]
                else:
                    # set the switch as disconnected
                    self.sdnSwitch[datapath.id].connected = False
                    self.logger.info('unregister datapath: %016x (Disconnected)', datapath.id)

    # Will sync the switch with DB each 60s
    def _monitor(self):
        while True:
            self.logger.info('::: start to sync the switch')
            for dp in self.datapaths.values():
                self._request_stats(dp)
            # add the function to check is there any added switch still not connected to controller
            for dp in self.sdnSwitch.values():
                if dp.connected:
                    self.sync_switch(dp.DPID)
            hub.sleep(60)

    # because gevent in RYU hub can not work with multiprocess in my testing, make this sync to a process in framework
    def sync_all_switch_status(self):
        self.update_configured_mac_from_db()
        self.logger.info('::: start to sync the switch')
        for dp in self.datapaths.values():
            self._request_stats(dp)
        # add the function to check is there any added switch still not connected to controller
        self.sync_bypass_policy()
        for dp in self.sdnSwitch.values():
            if dp.connected:
                self.sync_switch(dp.DPID)

    def sync_all_switch_mac(self):
        # add the function to check is there any added switch still not connected to controller
        for dp in self.sdnSwitch.values():
            if dp.connected:
                self.sync_switch(dp.DPID)

    def _request_stats(self, datapath):
        self.logger.debug('send stats request: %016x', datapath.id)
        ofproto = datapath.ofproto
        parser = datapath.ofproto_parser
        req = parser.OFPFlowStatsRequest(datapath)
        datapath.send_msg(req)

    def _request_desc(self, datapath):
        self.logger.debug('send bridge description request: %016x', datapath.id)
        ofproto = datapath.ofproto
        parser = datapath.ofproto_parser
        req = parser.OFPDescStatsRequest(datapath)
        datapath.send_msg(req)

    @set_ev_cls(ofp_event.EventOFPFlowStatsReply, MAIN_DISPATCHER)
    def _flow_stats_reply_handler(self, ev):
        body = ev.msg.body
        for stat in sorted([flow for flow in body if flow.priority == 100],
                           key=lambda flow: (flow.match['eth_src'])):
            self.logger.info('%016x %8s %8d %8d',
                             ev.msg.datapath.id, stat.match['eth_src'],
                             stat.packet_count, stat.byte_count)
        # need update the stat to DB

    @set_ev_cls(ofp_event.EventOFPDescStatsReply, MAIN_DISPATCHER)
    def _bridge_stats_reply_handler(self, ev):
        body = ev.msg.body
        datapath = ev.msg.datapath
        sn = body.serial_num
        # update databse and inistance
        self.sdnSwitch[datapath.id].sn = sn
        self.logger.info('::get bridge Description and update DPID: %016x with SN: %s', datapath.id, sn)


    def install_mac_flow(self, sn=None, mac=None):
        if mac in self.host_configured.keys():
            configured_vlan = self.host_configured[mac].assigned_vlan_id
            queue = self.host_configured[mac].map_queue
            ip = self.host_configured[mac].sip
            match = {}
            match.update({"dl_src": mac})
            if ip and ip != '':
                match.update({"nw_src": ip})
                match.update({"eth_type": 2048})
            actions = []
            actions.append({"type": "SET_QUEUE", "queue_id": queue})
            if configured_vlan != 0:
                actions.append({"type": "SET_FIELD", "field": "vlan_vid", "value": configured_vlan})
                # if meter != 0:
                # actions.append({"type": "METER", "meter_id": meter})
                actions.append({"type": "OUTPUT", "port": "normal"})
            else:
                actions.append({"type": "OUTPUT", "port": 0})
            flow_rule = {
               "dpid": self.sdnSwitch[dpid].DPID,
                "idle_timeout": 300, "hard_timeout": 0, "priority": 100,
                "flags": 1,
                "match": match,
                "actions": actions
            }
            if self.sdnSwitch[dpid].add_flow(flow_rule):
                self.logger.info(":::add MAC %s in switch successfull", mac)
            else:
                self.logger.warn(":::Failed to add MAC %s in switch", mac)

    def set_mac_online_db(self, sn=None, mac=None, online=True):
        if mac in self.host_configured.keys():
            host = HostAccessControl()
            if online == True:
                host.located_switch = sn
                host.online = True
                host.smac = mac
                sdn_access_db.insert_or_update(host, primary_key='smac')
            else:
                host.online = False
                host.smac = mac
                sdn_access_db.insert_or_update(host, primary_key='smac')

