# from server.vtep_management.vtep_manager import Vtep_manager, bgp_evpn
import os
import sys
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(BASE_DIR)

import logging
logging.basicConfig(format='%(asctime)s - %(pathname)s[line:%(lineno)d] - %(levelname)s: %(message)s', level=logging.DEBUG)

from server.vtep_management.vtep_sync_manager import VtepSyncManager


def vtep_management_service():
    vtep_serice = VtepSyncManager()
    vtep_serice.run()


def bgp_evpn_service():
    pass
    #bgp_evpn_service = bgp_evpn.run()


def vtep_management_sync_service():
    pass
    #sync_url = 'http://localhost:8080/v1.0/HostAccessController/sync_all_switch_status'



if __name__ == '__main__':
    from server.db.models.automation import AnsibleJobResult
    vtep_management_service()
