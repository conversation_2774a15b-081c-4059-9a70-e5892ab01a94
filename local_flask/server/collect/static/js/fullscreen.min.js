!function(e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("../../xterm")):"function"==typeof define?define(["../../xterm"],e):e(window.Terminal)}(function(e){var t={};return t.toggleFullScreen=function(e,t){var n;n=void 0===t?e.element.classList.contains("fullscreen")?"remove":"add":t?"add":"remove",e.element.classList[n]("fullscreen")},e.prototype.toggleFullscreen=function(e){t.toggleFullScreen(this,e)},t});