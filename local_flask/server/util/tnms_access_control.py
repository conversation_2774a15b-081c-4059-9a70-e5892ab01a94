#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON>
@project: tnms_access_control
@file: __init__.py
@function:
@time: 2023/12/6 18:14
"""
import json
import re

from server.constants import AUTOMATION_BASE_DIR

config_file = f"{AUTOMATION_BASE_DIR}/server/request_level_role.json"


class AccessControl:
    def __init__(self, config_file):
        self.roles = self.load_config(config_file)

    def load_config(self, config_file):
        with open(config_file, 'r') as file:
            config = json.load(file)
        return config

    def check_access(self, role, url):
        if role in self.roles:
            for not_allow_url in self.roles[role]["permissions_exclude"]:
                if re.match(not_allow_url, url):
                    return False
        return True


access_control = AccessControl(config_file)

if __name__ == '__main__':
    print(access_control.check_access("admin", "/test"))  # True
    print(access_control.check_access("admin", "/test3"))  # True
    print(access_control.check_access("operator", "/test6"))  # True
    print(access_control.check_access("readonly", "/test4"))  # False
    print(access_control.check_access("guest", "/test5"))  # False
