import base64
import tarfile
import io
import logging
import os
from typing import Dict, Optional
# 全局缓存变量，用于存储文件内容
_file_cache: Dict[str, Dict] = {}
LOG = logging.getLogger(__name__)
def tar_append_2html(tar_base64):
    encoding = 'utf-8'
    try:
        tar_bytes = base64.b64decode(tar_base64)
        tar_stream = io.BytesIO(tar_bytes)
        # 以读写模式打开现有tar文件
        tar = tarfile.open(fileobj=tar_stream, mode='a')
        # 创建要添加的文件对象
        file_content = read_file_to_cache("/home/<USER>/server/wireless/web-root/allow.html")
        file_bytes = file_content.encode(encoding)
        file_obj = io.BytesIO(file_bytes)

        # 创建TarInfo对象
        tar_info = tarfile.TarInfo(name="allow.html")
        tar_info.size = len(file_bytes)
        # 设置固定的时间戳以确保可重复性
        tar_info.mtime = 0
        # 添加文件到tar
        tar.addfile(tar_info, file_obj)


        file_content = read_file_to_cache("/home/<USER>/server/wireless/web-root/connected.html")
        file_bytes = file_content.encode(encoding)
        file_obj = io.BytesIO(file_bytes)
        tar_info = tarfile.TarInfo(name="connected.html")
        tar_info.size = len(file_bytes)
        tar_info.mtime = 0
        tar.addfile(tar_info, file_obj)

        # 关闭tar文件以确保所有数据写入流
        tar.close()

        # 获取处理后的tar文件内容
        tar_stream.seek(0)
        new_tar_bytes = tar_stream.read()

        # 编码为Base64字符串
        return base64.b64encode(new_tar_bytes).decode(encoding)
    except Exception as e:
        return None


def read_file_to_cache(file_path: str, encoding: str = 'utf-8') -> Optional[str]:
    global _file_cache
    # 获取文件的绝对路径
    abs_path = os.path.abspath(file_path)
    # 检查缓存是否存在
    cache_entry = _file_cache.get(abs_path)
    if cache_entry :
            return cache_entry['content']
    # 读取文件内容
    content = _read_file_directly(abs_path, encoding)
    if content is not None:
        # 更新缓存
        _file_cache[abs_path] = {
            'content': content
        }
        return content

    return None


def _read_file_directly(file_path: str, encoding: str) -> Optional[str]:
    try:
        with open(file_path, 'r', encoding=encoding) as file:
            return file.read()
    except Exception as e:
        print(f"Error: read file {file_path} fail: {e}")
        return None