import base64
import logging
import os
import shutil
import threading
import time


import requests
from requests_toolbelt.multipart.encoder import MultipartEncoder

from server import cfg
from server.db.models import user as user_db_model
from server.util import utils

LOG = logging.getLogger(__name__)


FILE_TRANSFER_API = '/file/transfer'
SEVER_AVAILABLE = '/monitor/server/available'
MIN_INTERVAL = 60
MAX_INTERVAL = 86400


def start_transfer_file(*args):
    if not transfer_file(*args, retry_times=1):
        thread = threading.Thread(target=transfer_file, args=args, kwargs={'retry_times': 10})
        thread.setDaemon(True)
        thread.start()


def transfer_file(user, sources, retry_times=1):
    """
    :param user:
    :param sources: sources = [{
        filename: a.txt,
        path: config_gen/upconfig/a.txt,
        dest: config_gen/upconfig/a.txt
    }]
    :param retry_times:
    :return:
    """

    try:
        if not sources:
            return True

        interval = MIN_INTERVAL
        host, _, running = utils.get_db_sync_ip()
        if not host or not running:
            return True

        url = 'https://%s:%s%s' % (host, cfg.CONF.port, FILE_TRANSFER_API)
        temp_file_path_list = []

        while retry_times > 0:

            fields = dict()

            for source in sources:
                # if source is a folder, will zip it and transfer
                if os.path.isdir(source['path']):
                    upper_dir = os.path.dirname(source['path'])
                    # temp_file_path will save in /tmp
                    zip_temp_file_path_without_tar_flag = os.path.join('/tmp', source['filename'])
                    # make temp zip file
                    zip_file_path = shutil.make_archive(zip_temp_file_path_without_tar_flag, 'tar', upper_dir, source['filename'])
                    dest_path = os.path.join(os.path.dirname(source['dest']), source['filename'] + '.tar')
                    fields[source['filename'] + '.tar'] = dest_path
                    fields['by' + source['filename'] + '.tar'] = (source['filename'] + '.tar', open(zip_file_path, 'rb'))
                    fields[source['filename'] + '.tar' + '_zip_type'] = 'tar'
                    temp_file_path_list.append(zip_file_path)
                else:
                    fields[source['filename']] = source['dest']
                    fields['by' + source['filename']] = (source['filename'], open(source['path'], 'rb'))

            m = MultipartEncoder(fields=fields)

            headers = {'content-type': m.content_type}
            LOG.info('start push file to sync server')
            response = requests.post(url, data=m, headers=headers, verify=False)
            if response.status_code == 200:
                return True

            LOG.info('push file failed, reason %s, will sleep %s seconds to try again', response.text, interval)
            retry_times -= 1
            time.sleep(interval)
            interval = min(interval*2, MAX_INTERVAL)
        # for clear temp file
        for path in temp_file_path_list:
            os.remove(path)
    except Exception as e:
        LOG.exception(e)
        return False


def conform_sync_server_ok(user, host):
    url = 'https://%s:%s%s' % (host, cfg.CONF.port, SEVER_AVAILABLE)
    response = requests.get(url, verify=False)
    if response.status_code == 200 and response.text == 'Application and database are up and functioning':
        return True
    return False


def test():
    # auth = get_basic_auth_str('admin', 'admin')
    #
    # url = 'https://%s:%s%s' % ('127.0.0.1', 80, FILE_TRANSFER_API)
    #
    # m = MultipartEncoder(
    #     fields={'test1': 'config_gen/test/test1.config',
    #             'test22': 'config_gen/test/test22.config',
    #             'file': ('test1', open('../config_gen/upconfig/test1.config', 'rb')),
    #             'file2': ('test22', open('../config_gen/upconfig/test22.config', 'rb'))}
    # )
    #
    # headers = {'Authorization': auth}
    #
    # response = requests.post(url, data=m, headers=headers, verify=False)

    url = 'https://%s:%s%s' % ('127.0.0.1', 80, SEVER_AVAILABLE)
    response = requests.get(url, verify=False)

if __name__ == '__main__':
    test()
