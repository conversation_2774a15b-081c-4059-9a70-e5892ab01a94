import redis
import time


class DistributedLock:
    def __init__(self, name, expire_time=10, redis_host='localhost', redis_port=6379):
        self.redis = redis.StrictRedis(host=redis_host, port=redis_port, db=0)
        self.lock_name = name
        self.expire_time = expire_time
        self.locked = False

    def acquire(self):
        if self.redis.ttl(self.lock_name) == -1:
            self.redis.delete(self.lock_name)
            self.locked = False
        while not self.locked:
            now = time.time()
            expire = now + self.expire_time
            if self.redis.setnx(self.lock_name, expire):
                self.redis.expire(self.lock_name, self.expire_time)
                self.locked = True
                return True
            else:
                current_expire = self.redis.get(self.lock_name)
                if current_expire and float(current_expire) < now:
                    current_expire = self.redis.getset(self.lock_name, expire)
                    if current_expire and float(current_expire) < now:
                        return False
                time.sleep(0.5)

        return False

    def release(self):
        if self.locked:
            self.redis.delete(self.lock_name)
            self.locked = False
