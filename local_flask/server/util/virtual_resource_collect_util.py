import logging
import traceback
import threading
from server.constants import CloudPlatform
from server.util import virtual_resource_vsphere_util
from server.util import virtual_resource_openstack_util
from server.db.models.dc_virtual_resource import dc_virtual_resource_db, DCVirtualResourcePoolAZ, DCVirtualResourcesVM, DCVirtualResourceNetwork, DCVirtualResourceVPC
import datetime

LOG = logging.getLogger(__name__)

def sycn_cloud_resources():
    try:
        cloud_controllers = dc_virtual_resource_db.get_cloud_virtual_resource_pool_az()
        threads = []
        for controller in cloud_controllers:
            thread = threading.Thread(target=cloud_handler, args=(controller,))
            threads.append(thread)
            thread.start()
        for thread in threads:
            thread.join()
    except Exception as e:
        LOG.info(e)
        LOG.info(traceback.print_exc())
    
def cloud_handler(controller):
    full_info = None
    try:
        if controller.resource_type == CloudPlatform.OPENSTACK:
            auth_url = controller.auth_info_dict.get("auth_url")
            username= controller.auth_info_dict.get("username")
            password= controller.auth_info_dict.get("password")
            user_domain_name= controller.auth_info_dict.get("user_domain_name")
            project_name= controller.auth_info_dict.get("project_name")
            project_domain_name= controller.auth_info_dict.get("project_domain_name")
            LOG.info(f"Openstack: {auth_url}  {username}  {password} {user_domain_name} {project_name} {project_domain_name}")
            o_ins = virtual_resource_openstack_util.OpenStackDriver(
                auth_url=auth_url,
                username=username,
                password=password,
                user_domain_name=user_domain_name,
                project_name=project_name,
                project_domain_name=project_domain_name
            )
            LOG.info(o_ins.get_full_required_info())

            full_info = o_ins.get_full_required_info()
        elif controller.resource_type == CloudPlatform.VSPHERE:
            host_ip = controller.auth_info_dict.get("server_ip")
            username = controller.auth_info_dict.get("username")
            password = controller.auth_info_dict.get("password")
            port = controller.auth_info_dict.get("server_port_num")
            LOG.info(f"vSphere: {host_ip}  {username} {password} {port}")
            v_ins = virtual_resource_vsphere_util.VSphereDriver(
                host=host_ip,
                username=username,
                password=password,
                port=port
            )
            LOG.info(v_ins.get_full_required_info())
            full_info = v_ins.get_full_required_info()
    except Exception as e:
        LOG.info("get cloud full info failed")
        LOG.error(traceback.format_exc())
        dc_virtual_resource_db.update_virtual_resource_pool_az_connect_status(
            az_id=controller.id,
            connect_status= False
        )
        dc_virtual_resource_db.update_virtual_resource_pool_host_connect_status(
            az_id=controller.id,
            connect_status= False
        )
        return
    
    dc_virtual_resource_db.update_virtual_resource_pool_az_connect_status(
        az_id=controller.id,
        connect_status= True,
        connect_active_time=datetime.datetime.now()
    )
    if full_info:
        database_info_update(controller, full_info['projects_info'], full_info['networks_info'], full_info['vms_info'], 
                             full_info['hosts_info'], full_info['used_network'], controller.resource_type)

def database_info_update(az, vpc_list, network_list, vm_list, host_list, used_network_dict, resource_type):
    dc_virtual_resource_db.update_virtual_resource_vpcs(vpc_list, az)
    dc_virtual_resource_db.update_virtual_resource_networks(network_list, az, resource_type)
    dc_virtual_resource_db.update_virtual_resources_vms(vm_list, az)
    dc_virtual_resource_db.update_virtual_resources_cloud_host(host_list, az)
    ## 更新hostlink与network的关联关系
    dc_virtual_resource_db.update_virtual_resource_host_link_network_mapping(used_network_dict, az)