from openstack import connection

class OpenStackDriver():
    def __init__(self, auth_url, username, password, project_name, user_domain_name="default", project_domain_name="default"):
        self.auth_url = auth_url
        self.username = username
        self.password = password
        self.project_name = project_name
        self.user_domain_name = user_domain_name
        self.project_name = project_name
        self.conn = connection.Connection(
            auth_url=auth_url,
            username=username,
            password=password,
            user_domain_name=user_domain_name,
            project_name=project_name,
            project_domain_name=project_domain_name
        )

    def get_full_required_info(self):
        vms = self.getVMs()
        used_network = {}
        for item in vms:
            host_name = item['hypervisor_ip']
            if host_name not in used_network:
                used_network[host_name] = []
            used_network[host_name].append(item)
        
        return {
            "projects_info": self.getProjects(),
            "vms_info": vms,
            "networks_info": self.getNetworks(),
            "hosts_info": self.getHosts(),
            "used_network": used_network
        }

    def _getHypervisors(self):
        return list(self.conn.compute.hypervisors())
    
    def _getNetworks(self):
        return self.conn.network.networks()
    
    def _getProjects(self):
        return list(self.conn.identity.projects())

    def getProjects(self):
        resProjects = []
        for project in self.conn.identity.projects():
            resProjects.append({
                "name": project.name,
                "id": project.id
            })
        return resProjects
    
    def getNetworks(self):
        resNetworks = []
        networks = self._getNetworks()
        for network in networks:
            tenant_id = network.tenant_id
            project = self.conn.identity.get_project(tenant_id)
            # Get VMs belong to network
            vm_counter = 0
            ports = self.conn.network.ports(network_id=network.id)
            for port in ports:
                if port.device_owner == "compute:nova" and port.device_id:
                    device_id = port.device_id
                    try:
                        server = self.conn.compute.get_server(device_id)
                        vm_counter += 1
                    except Exception as e:
                        print(e)
            resNetworks.append({
                "name": network.name,
                "id": network.id,
                "is_shared": network.is_shared,
                "project_name": project.name,
                "project_id": project.id,
                "vm_counter": vm_counter,
                "provider_network_type": network.provider_network_type,
                "vlan_id": network.provider_segmentation_id
            })
        return resNetworks
        
    def getVMs(self):
        resVMS = [] 
        hypervisors = self._getHypervisors()
        projects = list(self.conn.identity.projects())
        for project in projects:
            try:
                project_conn = connection.Connection(
                    auth_url=self.auth_url,
                    username=self.username,
                    password=self.password,
                    user_domain_name=self.user_domain_name,
                    project_id=project.id,
                    project_domain_id=project.domain_id
                )
                # Get server
                for server in project_conn.compute.servers():
                    hypervisor_hostname = server.hypervisor_hostname
                    hypervisor_ip = None
                    for h in hypervisors:
                        if h['hypervisor_hostname'] == hypervisor_hostname:
                            hypervisor_detail = self.conn.compute.get_hypervisor(h.id)
                            hypervisor_ip = hypervisor_detail.host_ip
                    vm_ips = []
                    network_names = []
                    for network_name, addresses in server.addresses.items():
                        network_names.append(network_name)
                        for address in addresses:
                            if address['OS-EXT-IPS:type'] == 'fixed':
                                vm_ips.append(address['addr'])
                    resVMS.append({
                        'id': server.id,
                        'name': server.name,
                        'status': server.status,
                        'image_name': server.image.name,
                        'project_name': project.name,
                        'networks': network_names,
                        'vm_ip': vm_ips,
                        'hypervisor_hostname': server.hypervisor_hostname,
                        'hypervisor_ip': hypervisor_ip
                    })
            except Exception as e:
                print(e)
        return resVMS
    
    def getHosts(self):
        resHosts = []
        for hypervisor in self._getHypervisors():
            # 获取更详细的hypervisor信息
            hypervisor_detail = self.conn.compute.get_hypervisor(hypervisor.id)
            
            resHosts.append({
                "host_name": hypervisor.id,
                "host_ip": hypervisor_detail.host_ip,
            })
        return resHosts

if __name__ == "__main__":
    auth_url = "http://***********:5000"
    username="admin"
    password="admin"
    user_domain_name="default"
    project_name="test"
    project_domain_name="default"
    testInsta = OpenStackDriver(auth_url=auth_url, username=username, password=password, user_domain_name=user_domain_name, project_name=project_name, project_domain_name=project_domain_name)
    # print("### Test Get VMs")
    # print(testInsta.getVMs())
    # print("### Test Get Networks")
    # print(testInsta.getNetworks())
    # print("### Test Get Projects")
    # print(testInsta.getProjects())
    print(testInsta.get_full_required_info())