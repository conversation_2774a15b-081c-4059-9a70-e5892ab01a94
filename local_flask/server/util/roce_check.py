import logging
import re
from datetime import datetime
from server.util.ssh_util import get_interactive_session, interactive_shell_linux_with_conn, SUPERUSER_PROMPT
from server import constants as C
from server.db.models.inventory import InventoryDB
from server.db.models.automation import RoceTask, AnsibleDevice

LOG = logging.getLogger(__name__)

inven_db = InventoryDB()


def roce_function_inspection(info_dict):
    try:
        status_sc = False
        result_list = []

        # 获取连接对象
        ip = info_dict.get('device_ip')
        username = info_dict.get('device_user')
        password = info_dict.get('device_pwd')
        ssh_session, status, _ = get_interactive_session(ip, username=username, password=password, timeout=60)
        head_str = f"{ip}@{username}:"
        if isinstance(ssh_session, str):
            res_str = f"Failed to connect to {ip}. Please check your IP address, account, and password and try again later."
            LOG.info(f"{ip} {ssh_session}")
            return False, res_str

        # ib工具包检测
        status_itd, result_str_itd = ib_toolkit_detection(ssh_session, head_str)
        result_list.append(result_str_itd)
        if status_itd:
            nvidia_result_list = []
            broadcom_result_list = []
            status_nsc = nvidia_series_check(ssh_session, nvidia_result_list, password, head_str)
            status_bsc = broadcom_series_check(ssh_session, broadcom_result_list, password, head_str)
            if status_nsc:
                result_list.extend(nvidia_result_list)
            if status_bsc:
                result_list.extend(broadcom_result_list)
            if not status_nsc and not status_bsc:
                result_list.extend(nvidia_result_list)
                result_list.extend(broadcom_result_list)
            status_sc = status_nsc or status_bsc

        return status_sc, "\n".join(result_list)
    except Exception as e:
        print(f"Error: roce function check failed.\n{str(e)}")
        LOG.info(f"Error: roce function check failed.\n{str(e)}")
        return False, "The check function is abnormal, please try again later."


def nvidia_series_check(ssh_session, result_list, password, head_str):
    """
    1.Nvidia驱动检测
    2.指令集检查
    3.ib设备查询
    4.所有链路状态为以太网的RoCE设备
    5.检查设备pci
    6.roce功能检查
    7.roce配置查询
    8.最终展示巡检
    Args:
        ssh_session:
        result_list:
        password:
    Returns:

    """
    try:
        # Nvidia驱动检测
        status_ndc, result_str_ndc = nvidia_driver_check(ssh_session, head_str)
        result_list.append(result_str_ndc)
        if status_ndc:
            # 指令集检查
            status_isc, result_str_isc = instruction_set_check(ssh_session, head_str)
            result_list.append(result_str_isc)
            if status_isc:
                # ib 设备查询
                status_idq, result_str_idq, ens_list = ib_device_query(ssh_session, head_str)
                result_list.append(result_str_idq)
                if status_idq:
                    # 所有链路状态为以太网的RoCE设备
                    status_ied, result_str_ied, device_name_list = is_ethernet_device(
                        ssh_session, password, head_str)
                    result_list.append(result_str_ied)
                    if status_ied:
                        # 检查设备pci
                        status_pci, result_pci_str, _ = device_pci_check(ssh_session, device_name_list, head_str)
                        result_list.append(result_pci_str)
                        if status_pci:
                            # roce功能检查
                            status_rfc, result_str_rfc = roce_function_check(ssh_session, device_name_list, head_str)
                            result_list.append(result_str_rfc)
                            if status_rfc:
                                # roce配置查询
                                status_rcq, result_str_rcq = roce_configuration_query(ssh_session, ens_list, head_str)
                                result_list.append(result_str_rcq)
                                if status_rcq:
                                    # 最终展示巡检
                                    status_fd, result_str_fd = final_display(ssh_session, device_name_list, head_str)
                                    result_list.append(result_str_fd)
                                    return True
                            else:
                                status_fd, result_str_fd = final_display(ssh_session, device_name_list, head_str)
                                result_list.append(result_str_fd)
        return False
    except Exception as e:
        raise e


def broadcom_series_check(ssh_session, result_list, password, head_str):
    try:
        # 博通驱动检测
        status_bdc, result_str_bdc = broadcom_driver_check(ssh_session, head_str)
        result_list.append(result_str_bdc)
        if status_bdc:
            status_tsd, result_str_tsd = tool_set_detection(ssh_session, head_str)
            result_list.append(result_str_tsd)
            if status_tsd:
                # 所有链路状态为以太网的RoCE设备
                status_ied, result_str_ied, device_name_list = is_ethernet_device(
                    ssh_session, password, head_str)
                result_list.append(result_str_ied)
                if status_ied:
                    # 检查设备pci
                    status_pci, result_pci_str, pci_address_list = device_pci_check(ssh_session, device_name_list,
                                                                                    head_str)
                    result_list.append(result_pci_str)
                    if status_pci:
                        # roce功能检查
                        status_rfc, result_str_rfc = roce_function_check(ssh_session, device_name_list, head_str)
                        result_list.append(result_str_rfc)
                        if status_rfc:
                            # roce配置
                            status_brcq, result_str_brcq = broadcom_roce_configuration_query(ssh_session, head_str,
                                                                                             pci_address_list, password)
                            result_list.append(result_str_brcq)
                            if status_brcq:
                                status_bfd, result_str_bfd = broadcom_final_display(ssh_session, head_str,
                                                                                    pci_address_list, password)
                                result_list.append(result_str_bfd)
                                return True
        return False
    except Exception as e:
        raise e


def device_info_query(id):
    db_session = inven_db.get_session()
    try:
        ansibleDevice = db_session.query(AnsibleDevice).filter(AnsibleDevice.id == id).first()
        return ansibleDevice
    except Exception as e:
        print(f"Error: device info query failed.\n{str(e)}")
        LOG.info(f"Error: device info query failed.\n{str(e)}")
        raise
    finally:
        db_session.close()


def data_insert(device_info):
    db_session = inven_db.get_session()
    try:
        roce_task = RoceTask()
        roce_task.task_name = f"check_{device_info.device_name}_{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        roce_task.device_id = device_info.id
        roce_task.type = "check"
        roce_task.status = "RUNNING"
        roce_task.result = ""
        with db_session.begin():
            db_session.add(roce_task)
        LOG.info("Data insertion successful.")
        return roce_task.id
    except Exception as e:
        print(f"Error: insert into table (roce_task) failed.\n{str(e)}")
        LOG.info(f"Error: insert into table (roce_task) failed.\n{str(e)}")
        raise
    finally:
        db_session.close()


def nvidia_driver_check(ssh_session, head_str):
    """
    检查是否安装网卡驱动和 Roce 驱动
    Args:
        ssh_session: 已建立的 SSH 会话

    Returns:
        tuple: (bool, str) - (是否安装成功, 错误信息)
    """
    try:
        command_str1 = "lsmod | grep mlx5_ib"
        LOG.info(f"Execute Command: {command_str1}")
        mlx5_ib_res, status1 = interactive_shell_linux_with_conn(ssh_session, command_str1, prompt=SUPERUSER_PROMPT)

        command_str2 = "lsmod | grep mlx5_core"
        LOG.info(f"Execute Command: {command_str2}")
        mlx5_core_res, status2 = interactive_shell_linux_with_conn(ssh_session, command_str2, prompt=SUPERUSER_PROMPT)

        result_str = f"{head_str}{command_str1}\n{mlx5_ib_res}\n{head_str}{command_str2}\n{mlx5_core_res}"

        # 命令执行失败
        if status1 != C.RMA_ACTIVE or status2 != C.RMA_ACTIVE:
            error_str = "Execution of inspection driver command failed."
            LOG.info(error_str)
            return False, f"{error_str}\n{result_str}"

        result_status = False

        # 两个模块都存在才表示驱动安装成功
        if mlx5_ib_res and mlx5_core_res:
            status_str = "Nvidia check result: The ROCE driver and network card driver have been installed correctly."
            result_status = True
        elif mlx5_ib_res and not mlx5_core_res:
            status_str = "Nvidia check result: ROCE driver installed, network card driver not installed."
        elif mlx5_core_res and not mlx5_ib_res:
            status_str = "Nvidia check result: Network card driver installed, ROCE driver not installed."
        else:
            status_str = "Nvidia check result: The ROCE driver and network card driver are not installed correctly."

        LOG.info(f"{status_str}\n{result_str}")
        return result_status, f"{status_str}\n{result_str}"
    except Exception as e:
        return False, f"Error in detecting driver status: {str(e)}"


def instruction_set_check(ssh_session, head_str):
    """
    指令集检查
    Args:
        ssh_session: 已建立的 SSH 会话

    Returns:
        tuple: (bool, str) - (是否检查成功, 结果信息)
    """
    try:
        command_str_list = [
            "dpkg -l | grep mlnx-ofed-kernel-dkms",
            "dpkg -l | grep -i mft",
            "dpkg -l | grep -i mlnx-tools"
        ]

        result_status = True
        result_str_list = []
        for command_str in command_str_list:
            LOG.info(f"Execute Command: {command_str}")
            result_info, status = interactive_shell_linux_with_conn(ssh_session, command_str, prompt=SUPERUSER_PROMPT)

            if status != C.RMA_ACTIVE:
                result_status = False
                erro_str = f"Command {command_str} execution failed."
                LOG.info(erro_str)
                result_str_list.append(erro_str)

            if not result_info:
                result_status = False
                status_str = f"The instruction set check result is empty. => ({command_str})"
                LOG.info(status_str)
                result_str_list.append(status_str)

            result_str_list.append(head_str + command_str)
            result_str_list.append(result_info)

        return result_status, "\n".join(result_str_list)
    except Exception as e:
        return False, f"Error in instruction set check: {str(e)}"


def ib_device_query(ssh_session, head_str):
    """
    ib设备查询
    Args:
        ssh_session: 已建立的SSH会话

    Returns:
        tuple: (bool, str, list) - (是否查询成功, 错误信息, 网络接口列表)
    """
    try:
        command_str = "ibdev2netdev"
        LOG.info(f"Execute Command: {command_str}")
        result_info, status = interactive_shell_linux_with_conn(ssh_session, command_str, prompt=SUPERUSER_PROMPT)

        result_str = f"{head_str}{command_str}\n{result_info}"

        if status != C.RMA_ACTIVE:
            error_str = f"Command {command_str} execution failed."
            return False, f"{error_str}\n{result_str}", None

        if not result_info:
            return False, f"The IB device query result is empty. => ({command_str})", None

        # 取出设备端口号
        pattern = r"==> (\w+) \("
        result_list = list(re.findall(pattern, result_info))

        return True, result_str, result_list
    except Exception as e:
        return False, f"Error in ib device query: {str(e)}", None


def roce_function_check(ssh_session, device_name_list, head_str):
    """
    roce功能检查
    Args:
        ssh_session: 已建立的SSH会话
        device_name_list: mlx5设备名称

    Returns:
        tuple: (bool, str) - (是否检查成功, 检查信息)
    """
    try:
        return_status = True
        return_str_list = []
        for device_name in device_name_list:
            command_str = f"cat /sys/class/infiniband/{device_name}/ports/1/gid_attrs/types/3"
            LOG.info(f"Execute Command: {command_str}")
            result_info, status = interactive_shell_linux_with_conn(ssh_session, command_str, prompt=SUPERUSER_PROMPT)

            if status == C.RMA_ACTIVE:
                if not result_info:
                    result_status = False
                    status_str = f"{device_name} roce function check failed."
                    return_str_list.append(status_str)
                # else:
                #     if result_info != "RoCE v2":
                #         return_status = False
                #         status_str = f"{device_name} - The execution result is not RoCE v2."
                #         return_str_list.append(status_str)
            else:
                status_str = f"Command {command_str} execution failed."
                return_status = False
                return_str_list.append(status_str)

            return_str_list.append(head_str + command_str)
            return_str_list.append(result_info)

        return return_status, "\n".join(return_str_list)
    except Exception as e:
        return False, f"Error in roce function check: {str(e)}"


def roce_configuration_query(ssh_session, ens_list, head_str):
    """
    roce配置查询
    Args:
        ssh_session: 已建立的SSH会话

    Returns:
        tuple: (bool, str) - (是否查询成功, 查询结果信息)
    """
    try:
        command_str_list = [
            "sysctl net.ipv4.tcp_ecn",
        ]
        for ens in ens_list:
            command_str_list.append(f"mlnx_qos -i {ens}")

        return_status = True
        return_str_list = []
        for command_str in command_str_list:
            LOG.info(f"Execute Command: {command_str}")
            result_info, status = interactive_shell_linux_with_conn(ssh_session, command_str, prompt=SUPERUSER_PROMPT)

            if status != C.RMA_ACTIVE:
                return_status = False
                error_str = f"Command {command_str} execution failed."
                return_str_list.append(error_str)

            if not result_info:
                return_status = False
                status_str = f"The roce configuration query result is empty. => ({command_str})"
                return_str_list.append(status_str)

            return_str_list.append(head_str + command_str)
            return_str_list.append(result_info)

        return return_status, "\n".join(return_str_list)
    except Exception as e:
        return False, f"Error in roce configuration query: {str(e)}"


def update_roce_task_by_id(id, status, result_list):
    """
    根据id更新roce_task记录status字段和result字段
    Args:
        id: 主键
        status: True / False
        result_list: 检测结果列表

    Returns:

    """
    db_session = inven_db.get_session()
    try:
        with db_session.begin():
            roce_task = db_session.query(RoceTask).filter(RoceTask.id == id).first()
            if roce_task:
                roce_task.status = "SUCCESS" if status else "FAILED"
                roce_task.result = "\n".join(result_list)
    except Exception as e:
        print(f"Error: insert into table (roce_task) failed.\n{str(e)}")
        LOG.info(f"Error: insert into table (roce_task) failed.\n{str(e)}")
        raise
    finally:
        db_session.close()


def ib_toolkit_detection(ssh_session, head_str):
    """
    ib工具包检测
    Args:
        ssh_session: 已建立的SSH会话

    Returns:
        tuple: (bool, str) - (是否检测成功, 检测结果信息)
    """
    try:
        command_str_list = [
            "dpkg -l |grep -i infiniband-diags",
            "dpkg -l |grep -i ibutils",
            "dpkg -l |grep -i rdma-core"
        ]

        return_status = True
        return_str_list = []
        for command_str in command_str_list:
            LOG.info(f"Execute Command: {command_str}")
            result_info, status = interactive_shell_linux_with_conn(ssh_session, command_str, prompt=SUPERUSER_PROMPT)

            if status != C.RMA_ACTIVE:
                return_status = False
                error_str = f"Command {command_str} execution failed. status code is {status}."
                return_str_list.append(error_str)

            if not result_info:
                return_status = False
                status_str = f"The ib toolkit detection result is empty. => ({command_str})"
                return_str_list.append(status_str)

            return_str_list.append(head_str + command_str)
            return_str_list.append(result_info)
        return return_status, "\n".join(return_str_list)
    except Exception as e:
        return False, f"Error in ib toolkit detection: {str(e)}"


def is_ethernet_device(ssh_session, password, head_str):
    """
    所有链路状态为以太网的RoCE设备
    Args:
        ssh_session: 已建立的SSH会话

    Returns:
        tuple: (bool, str, list) - (是否检测成功, 检测结果信息, 设备名称列表)
    """
    try:
        command_str = "ibstatus|grep -i -B 8 'ethernet' |grep -i \"infiniband device\"|awk -F \"'\" '{print $2}'"
        LOG.info(f"Execute Command: {command_str}")
        result_info, status = interactive_shell_linux_with_conn(ssh_session, command_str, prompt=SUPERUSER_PROMPT)
        # result_info, status = interactive_shell_linux_with_conn(ssh_session, password)

        result_str = f"{head_str}{command_str}\n{result_info}"
        if status != C.RMA_ACTIVE:
            return_status = False
            error_str = f"Command {command_str} execution failed."
            return False, f"{error_str}\n{result_str}", None

        if not result_info:
            return_status = False
            status_str = f"The is ethernet device result is empty. => ({command_str})"
            return False, f"{status_str}\n{result_str}", None

        result_list = result_info.split("\n")
        return True, result_str, result_list

    except Exception as e:
        return False, f"Error in is ethernet device: {str(e)}"


def device_pci_check(ssh_session, device_name_list, head_str):
    """
    检查设备pci
    Args:
        ssh_session: 已建立的SSH会话
        device_name_list: 设备名称列表

    Returns:
        tuple: (bool, str, list) - (是否检测成功, 检测结果信息, pci地址)
    """
    try:
        command_str_list = []
        for device_name in device_name_list:
            command_str_list.append(f"readlink /sys/class/infiniband/{device_name}/device")
        pci_address_list = []
        return_status = True
        return_str_list = []
        for command_str in command_str_list:
            LOG.info(f"Execute Command: {command_str}")
            result_info, status = interactive_shell_linux_with_conn(ssh_session, command_str, prompt=SUPERUSER_PROMPT)

            if status != C.RMA_ACTIVE:
                return_status = False
                error_str = f"Command {command_str} execution failed."
                return_str_list.append(error_str)

            if not result_info:
                return_status = False
                status_str = f"The device pci check result is empty. => ({command_str})"
                return_str_list.append(status_str)
            else:
                pci_address_list.append(result_info.split("/")[-1])

            return_str_list.append(head_str + command_str)
            return_str_list.append(result_info)
        return return_status, "\n".join(return_str_list), pci_address_list
    except Exception as e:
        return False, f"Error in device pci check: {str(e)}"


def final_display(ssh_session, device_name_list, head_str):
    """
    展示巡检设备：网卡名+ib设备名
    Args:
        ssh_session: 已建立的SSH会话
        device_name_list: 设备名称列表

    Returns:
        tuple: (bool, str) - (是否检测成功, 检测结果信息)
    """
    try:
        readlink_command_str_list = []
        for device_name in device_name_list:
            readlink_command_str_list.append(f"readlink /sys/class/infiniband/{device_name}/device")

        return_status = False
        return_str_list = []
        readlink_info_list = []
        for readlink_command_str in readlink_command_str_list:
            LOG.info(f"Execute Command: {readlink_command_str}")
            result_info, status = interactive_shell_linux_with_conn(ssh_session, readlink_command_str, prompt=SUPERUSER_PROMPT)

            if status != C.RMA_ACTIVE:
                return_status = False
                error_str = f"Command {readlink_command_str} execution failed."
                return_str_list.append(error_str)

            if not result_info:
                return_status = False
                status_str = f"The device pci check result is empty. => ({readlink_command_str})"
                return_str_list.append(status_str)
            else:
                result_info_split_list = result_info.split("/")
                readlink_info_list.append(result_info_split_list[len(result_info_split_list) - 1])

            return_str_list.append(head_str + readlink_command_str)
            return_str_list.append(result_info)

        for readlink_info in readlink_info_list:
            lspci_command_str = f"sudo lspci -vvvs {readlink_info} |grep -i \"part number\""
            LOG.info(f"Execute Command: {lspci_command_str}")
            result_info, status = interactive_shell_linux_with_conn(ssh_session, lspci_command_str, prompt=SUPERUSER_PROMPT)

            if status != C.RMA_ACTIVE:
                return_status = False
                error_str = f"Command {lspci_command_str} execution failed."
                return_str_list.append(error_str)

            if not result_info:
                return_status = False
                status_str = f"Command {lspci_command_str} execution result is empty."
                return_str_list.append(status_str)

            return_str_list.append(head_str + lspci_command_str)
            return_str_list.append(result_info)

        return return_status, "\n".join(return_str_list)
    except Exception as e:
        return False, f"Error in final display: {str(e)}"


def insert_ansible_device():
    db_session = inven_db.get_session()
    try:
        ansible_device = AnsibleDevice()
        ansible_device.ip = "***********"  # "***********"
        ansible_device.device_name = "test0204"
        ansible_device.device_user = "fs"
        ansible_device.device_pwd = "admin@123"
        with db_session.begin():
            db_session.add(ansible_device)
        LOG.info("ansible_device insertion successful.")
    except Exception as e:
        print(f"Error: insert into table (ansible_device) failed.\n{str(e)}")
        LOG.info(f"Error: insert into table (ansible_device) failed.\n{str(e)}")
        raise
    finally:
        db_session.close()


def broadcom_driver_check(ssh_session, head_str):
    """
        博通：检查是否安装网卡驱动和 Roce 驱动
        Args:
            ssh_session: 已建立的 SSH 会话

        Returns:
            tuple: (bool, str) - (是否安装成功, 错误信息)
    """
    function_name = "broadcom_driver_check"
    command_str_list = [
        "lsmod |grep -i bnxt_en",
        "lsmod |grep -i bnxt_re"
    ]
    status_str_list = [
        "Broadcom check result: ROCE driver installed, network card driver not installed.",
        "Broadcom check result: Network card driver installed, ROCE driver not installed.",
        "Broadcom check result: The ROCE driver and network card driver have been installed correctly.",
        "Broadcom check result: The ROCE driver and network card driver are not installed correctly."
    ]
    return general_tools(ssh_session, head_str, function_name, command_str_list, status_str_list)


def tool_set_detection(ssh_session, head_str):
    """
    博通：工具集检查
    Args:
        ssh_session: 已建立的 SSH 会话

    Returns:
        tuple: (bool, str) - (是否安装成功, 错误信息)
    """
    function_name = "tool_set_detection"
    command_str_list = [
        "niccli --version|awk '{print $3}'",
        "dpkg -l|grep -i bnxt-re-conf",
        "dpkg -l|grep -i bcm-sosreport"
    ]
    return general_tools(ssh_session, head_str, function_name, command_str_list)


def broadcom_roce_configuration_query(ssh_session, head_str, pci_address_list, password):
    """
    博通 roce 配置查询
    Args:
        ssh_session:
        pci_address_list:
        password:

    Returns:

    """
    function_name = "broadcom_roce_configuration_query"
    command_str_list = [
        "sysctl net.ipv4.tcp_ecn"
    ]
    for address in pci_address_list:
        command_str_list.append(f"sudo niccli -pci {address} getqos")

    return general_tools(ssh_session, head_str, function_name, command_str_list, password=password)


def broadcom_final_display(ssh_session, head_str, pci_address_list, password):
    """
    博通：最终展示巡检了哪些设备
    Args:
        ssh_session:
        pci_address_list:
        password:

    Returns:

    """
    function_name = "broadcom_final_display"
    command_str_list = []
    for address in pci_address_list:
        command_str_list.append(f'sudo lspci -vvvs {address}|grep -i "part number"')

    return general_tools(ssh_session, head_str, function_name, command_str_list, password=password)


def general_tools(ssh_session, head_str, function_name, command_str_list, status_str_list=None, password=None):
    """
    公共方法，批量执行命令
    Args:
        ssh_session:
        function_name:
        command_str_list:
        status_str_list:
        password:

    Returns:

    """
    if status_str_list is None:
        status_str_list = []
    try:
        return_status = True
        return_str_list = []
        for i, command_str in enumerate(command_str_list):
            LOG.info(f"Execute Command: {command_str}")
            result_info, status = interactive_shell_linux_with_conn(ssh_session, command_str, prompt=SUPERUSER_PROMPT)
            # if command_str.startswith("sudo ") and password:
            #     result_info, status = interactive_shell_linux_with_conn(ssh_session, password)

            if status != C.RMA_ACTIVE:
                return_status = False
                error_str = f"Command {command_str} execution failed."
                return_str_list.append(error_str)

            if not result_info:
                return_status = False
                if i < len(status_str_list) - 1:
                    return_str_list.append(status_str_list[i])

            return_str_list.append(head_str + command_str)
            return_str_list.append(result_info)

        if len(status_str_list) - len(command_str_list) == 2:
            return_str_list.append(status_str_list[-2] if return_status else status_str_list[-1])

        return return_status, "\n".join(return_str_list)
    except Exception as e:
        return False, f"Error in {function_name}: {str(e)}"


if __name__ == '__main__':
    roce_function_inspection(3)
