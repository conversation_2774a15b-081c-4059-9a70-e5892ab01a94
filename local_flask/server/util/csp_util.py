import json
import logging

import requests
from requests.exceptions import RequestException

from server import cfg
from server.db.models.inventory import inven_db, SystemConfig
from server.db.redis_common import RedisSessionFactory

Log = logging.getLogger(__name__)


class CSPDownloader(object):
    def __init__(self):
        self.username = None
        self.password = None
        self.auth_url = "https://pica8-gateway.fs.com/pica8-sso/auth/account/login"
        self.proxy = cfg.CONF.license_portal_proxy
        self.redis_session = RedisSessionFactory.get_client()
        self.download_image_json_url = "https://pica8-gateway.fs.com/attachment"
        self.download_image_json_payload = {"attachmentId": "Ampcon/AmpCon-Campus/images/images.json"}

    def _load_credentials(self):
        try:
            session = inven_db.get_session()
            system_config = session.query(SystemConfig).first()
            if not system_config:
                return None, None, "System Config not found in the database."
            return system_config.license_portal_user, system_config.license_portal_password, None
        except Exception as e:
            return None, None, f"Exception when loading credentials: {e}"

    def _perform_auth_request(self, username, password):
        headers = {"Content-Type": "text/plain"}
        body = {"username": username, "password": password}
        try:
            proxies = {"https": self.proxy.get("https")} if self.proxy.get("https") else None
            response = requests.post(
                self.auth_url, data=json.dumps(body), headers=headers,
                proxies=proxies, timeout=20)

            if response.status_code != 200:
                return None, f"Auth request failed: HTTP {response.status_code}"

            return response.json(), None
        except RequestException:
            return None, f"Auth request failed: Network connection error"
        except Exception as e:
            return None, f"Auth request exception: {e}"

    def _cache_token(self, token):
        try:
            self.redis_session.setex(f"csp_{self.username}_token", 3600, token)
        except Exception as e:
            Log.warning(f"Failed to cache token: {e}")

    def is_auth_valid(self):
        self.username, self.password, err = self._load_credentials()
        if err:
            Log.error(err)
            return False, err

        response_detail, err = self._perform_auth_request(self.username, self.password)
        if err:
            Log.warning(err)
            return False, err

        if response_detail.get('code', -1) != 200:
            return False, f"Auth failed: {response_detail.get('message', 'Unknown error')}"

        token = response_detail.get("data", {}).get("accessToken")
        if not token:
            return False, "Auth failed: No accessToken received."

        return True, None

    def _auth(self):
        self.username, self.password, err = self._load_credentials()
        if err:
            Log.error(err)
            return False, err

        response_detail, err = self._perform_auth_request(self.username, self.password)
        if err:
            Log.error(err)
            return False, err

        if response_detail.get('code', -1) != 200:
            return False, f"Authentication failed: {response_detail.get('message', 'Unknown error')}"

        token = response_detail.get("data", {}).get("accessToken")
        if not token:
            return False, "Authentication failed: No token received."

        self._cache_token(token)
        return True, None

    def get_token(self):
        try:
            token = self.redis_session.get(f"csp_{self.username}_token")
            if not token:
                success, err_msg = self._auth()
                if not success:
                    return None, err_msg
                token = self.redis_session.get(f"csp_{self.username}_token")

            if token:
                return token.decode("utf-8"), None
            return None, "Token not found after authentication"
        except Exception as e:
            return None, f"Exception during token retrieval: {e}"

    def get_csp_image_json(self):
        try:
            cache = self.redis_session.get(f"csp_{self.username}_image_json")
            if cache:
                Log.info("CSP image JSON retrieved from cache.")
                return cache.decode("utf-8"), None

            token, err_msg = self.get_token()
            if not token:
                return None, err_msg

            headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
            proxies = {"https": self.proxy.get("https")} if self.proxy.get("https") else None
            response = requests.get(
                self.download_image_json_url,
                params=self.download_image_json_payload,
                headers=headers,
                proxies=proxies,
                timeout=20
            )

            if response.status_code != 200:
                return None, f"Fetch image JSON failed: HTTP {response.status_code}"

            response_detail = response.json()
            redirect_url = response_detail.get("data")
            if response_detail.get("code", -1) != 200 or not redirect_url:
                return None, f"CSP service error: {response_detail.get('message', 'Missing data')}"

            redirect_response = requests.get(redirect_url, headers={"Content-Type": "application/json"}, proxies=proxies, timeout=20)
            if redirect_response.status_code != 200:
                return None, f"Redirect failed: HTTP {redirect_response.status_code}"

            self.redis_session.setex(f"csp_{self.username}_image_json", 3600, redirect_response.text)
            return redirect_response.text, None

        except RequestException:
            return None, f"Auth request failed: Network connection error"
        except Exception as e:
            return None, f"Exception fetching CSP image JSON: {e}"

    def get_csp_image_json_from_cache(self):
        try:
            token, err_msg = self.get_token()
            if not token:
                return None

            cached_json = self.redis_session.get(f"csp_{self.username}_image_json")
            if cached_json:
                return cached_json.decode("utf-8")
            else:
                return self.get_csp_image_json()[0]
        except Exception as e:
            Log.error(f"Error accessing CSP image JSON cache: {e}")
            return None

    def _get_image_redirect_url_by_image_name(self, image_name):
        try:
            csp_image_json = self.get_csp_image_json_from_cache()
            if not csp_image_json:
                Log.error("Failed to get CSP image JSON.")
                return ""

            csp_image_dict = json.loads(csp_image_json)
            for version_data in csp_image_dict.get("data", []):
                for image_data in version_data.get("images", []):
                    if image_data.get("name") == image_name:
                        return image_data.get("download_url")
            return ""
        except Exception as e:
            Log.error(f"Failed to parse CSP image JSON: {e}")
            return ""

    def download_csp_image(self, image_name, tmp_file_path):
        if not tmp_file_path:
            err_msg = "Temporary file path is not provided."
            Log.error(err_msg)
            return False, err_msg
        try:
            token, err_msg = self.get_token()
            if not token:
                return False, err_msg

            image_url = self._get_image_redirect_url_by_image_name(image_name)
            if not image_url:
                return False, f"Redirect URL not found for {image_name}"

            md5_url = image_url.replace(image_name, f"{image_name}.md5")
            headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
            proxies = {"https": self.proxy.get("https")} if self.proxy.get("https") else None

            bin_resp = requests.get(image_url, headers=headers, proxies=proxies, timeout=20)
            md5_resp = requests.get(md5_url, headers=headers, proxies=proxies, timeout=20)

            if bin_resp.status_code != 200 or md5_resp.status_code != 200:
                return False, f"Download failed: bin={bin_resp.status_code}, md5={md5_resp.status_code}"

            bin_redirect_url = bin_resp.json().get("data")
            md5_redirect_url = md5_resp.json().get("data")
            if not bin_redirect_url or not md5_redirect_url:
                return False, "Redirect URL missing in bin/md5 response"

            bin_stream = requests.get(bin_redirect_url, headers={"Content-Type": "application/json"}, stream=True, proxies=proxies)
            md5_stream = requests.get(md5_redirect_url, headers={"Content-Type": "application/json"}, stream=True, proxies=proxies)

            with open(tmp_file_path + ".md5", "wb") as f_md5:
                for chunk in md5_stream.iter_content(8192):
                    if chunk:
                        f_md5.write(chunk)

            with open(tmp_file_path, "wb") as f_img:
                for chunk in bin_stream.iter_content(8192):
                    if chunk:
                        f_img.write(chunk)

            return True, f"Image saved to {tmp_file_path}"

        except RequestException:
            return None, f"Auth request failed: Network connection error"
        except Exception as e:
            return False, f"Exception during image download: {e}"

    def mark_downloading(self, image_name):
        try:
            self.redis_session.setex(f"image_downloading:{image_name}", 1200, "downloading")
            Log.info(f"Marked downloading: {image_name}")
        except Exception as e:
            Log.warning(f"Failed to mark downloading: {e}")

    def is_downloading(self, image_name):
        try:
            return self.redis_session.get(f"image_downloading:{image_name}") is not None
        except Exception as e:
            Log.warning(f"Check downloading error: {e}")
            return False

    def clear_downloading(self, image_name):
        try:
            self.redis_session.delete(f"image_downloading:{image_name}")
            Log.info(f"Cleared downloading: {image_name}")
        except Exception as e:
            Log.warning(f"Failed to clear downloading: {e}")


csp_downloader = CSPDownloader()
