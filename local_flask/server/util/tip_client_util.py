import logging
import requests

LOG = logging.getLogger(__name__)

from enum import Enum


class ServerType(Enum):
    OWSEC = 1
    OWGW = 2
    OWPROV = 3
    OWANALYTICS = 4
    OWRRM = 5


SERVICES_DICT = {
    ServerType.OWSEC: {"url": "https://owsec.wlan.local:17001",
                       "headers": {"X-API-KEY": "61d136fcac7bc5ce56fb19982b40f92c00e0870375fb1941f9c044ed4d0d9174",
                                   "X-INTERNAL-NAME": "owsec"}},
    ServerType.OWGW: {"url": "https://owgw.wlan.local:17002",
                      "headers": {"X-API-KEY": "47e2d313c4bc1cce7752290427e8fec523f927d3e14c65714f48089007c6eedd",
                                  "X-INTERNAL-NAME": "owgw"}},
    ServerType.OWPROV: {"url": "https://owprov.wlan.local:17005",
                        "headers": {"X-API-KEY": "3cdac1dc4dfa2fd2c1f6170d5042e91aa3ab152bd916e41e71e679fc6543297e",
                                    "X-INTERNAL-NAME": "owprov"}},
    ServerType.OWANALYTICS: {"url": "https://owanalytics.wlan.local:17009", "headers": {
        "X-API-KEY": "ce52f351d03ebb0c78f2c057b67a1828b01d42be8a1e9e696491ec2a601098a6",
        "X-INTERNAL-NAME": "owanalytics"}, },
    ServerType.OWRRM: {"url": "https://owrrm.wlan.local:16789",
                       "headers": {"X-API-KEY": "b3c74172518b4efaa0ce0af403e6bc050ad495b38acd730a9ef9ba74efd85363",
                                   "X-INTERNAL-NAME": "owrrm"}}
}


def get(type, endPoint):
    if type not in SERVICES_DICT:
        LOG.error(f"Type {type} not found in SERVICES_DICT")
        return None
    service = SERVICES_DICT[type]
    url = service["url"] + endPoint
    headers = service["headers"]
    response = requests.get(url, headers=headers, verify=False)
    LOG.debug(f"GET request to {url}: {response.status_code} - {response.text}")
    if response.status_code == 200:
        return response.json()
    return None


def post(type, endPoint, data):
    if type not in SERVICES_DICT:
        LOG.error(f"Type {type} not found in SERVICES_DICT")
        return None
    service = SERVICES_DICT[type]
    url = service["url"] + endPoint
    headers = service["headers"]
    response = requests.post(url, json=data, headers=headers, verify=False)
    LOG.debug(f"POST request to {url} with data {data}: {response.status_code} - {response.text}")
    if response.status_code in [200, 201]:
        return response.json()
    return None


def delete(type, endPoint):
    if type not in SERVICES_DICT:
        LOG.error(f"Type {type} not found in SERVICES_DICT")
        return None
    service = SERVICES_DICT[type]
    url = service["url"] + endPoint
    headers = service["headers"]
    response = requests.delete(url, headers=headers, verify=False)
    LOG.debug(f"DELETE request to {url}: {response.status_code} - {response.text}")
    if response.status_code == 200:
        return response.json()
    return None


def put(type, endPoint, data):
    if type not in SERVICES_DICT:
        LOG.error(f"Type {type} not found in SERVICES_DICT")
        return None
    service = SERVICES_DICT[type]
    url = service["url"] + endPoint
    headers = service["headers"]
    response = requests.put(url, json=data, headers=headers, verify=False)
    LOG.debug(f"PUT request to {url} with data {data}: {response.status_code} - {response.text}")
    if response.status_code == 200:
        return response.json()
    return None
