import paramiko
import logging
import time
import datetime
import re
import socket

from contextlib import contextmanager

from server import constants as C
from server import cfg
from server.util import ssh_helper

LOG = logging.getLogger(__name__)

LINUX_PROMPT = '.*@.*\> '
COMMAND_PROMPT = '.*@.*\>.*\n'
RECV_TIMEOUT = 60


def ssh_execute_retry_until_success(host, username, password, cmd, port=22, retry_interval=10):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh.connect(host, port, username, password)
    try:
        while True:
            stdout, stderr, exit_status = ssh_execute(ssh, cmd)
            if not stderr and exit_status <= 0:
                return stdout, stderr, exit_status

            # sleep 10s before retry
            time.sleep(retry_interval)
    finally:
        if ssh:
            ssh.close()


def ssh_execute_with_host(host, username, password, cmd, port=22, retry_times=0, retry_interval=10, check_error=True):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh.connect(host, port, username, password)
    try:
        while retry_times >= 0:
            stdout, stderr, exit_status = ssh_execute(ssh, cmd)
            if (not check_error or not stderr) and exit_status <= 0:
                return stdout, stderr, exit_status

            retry_times -= 1
            # sleep 10s before retry
            time.sleep(retry_interval)
    finally:
        if ssh:
            ssh.close()


def ssh_execute(ssh, cmd):
    LOG.debug('Running cmd (SSH): %s', cmd)

    stdin_stream, stdout_stream, stderr_stream = ssh.exec_command(cmd)
    channel = stdout_stream.channel

    # NOTE(justinsb): This seems suspicious...
    # ...other SSH clients have buffering issues with this approach
    stdout = stdout_stream.read()
    stderr = stderr_stream.read()
    stdin_stream.close()

    exit_status = channel.recv_exit_status()

    # exit_status == -1 if no exit code was returned
    LOG.debug('Result was %s' % exit_status)

    return stdout, stderr, exit_status


def push_file(dest_host, user, password, local_file, remote_file, retry_time=0, retry_interval=15):
    LOG.debug('Pushing file %s to %s %s', local_file, dest_host, remote_file)
    transport = None
    while retry_time >= 0:
        try:
            transport = paramiko.Transport((dest_host, 22))
            transport.connect(username=user, password=password)
            sftp = paramiko.SFTPClient.from_transport(transport)
            sftp.put(local_file, remote_file)
        except IOError as e:
            LOG.exception('push file failed %s %s %s %s', dest_host, local_file, remote_file, e)
            time.sleep(retry_interval)
            retry_time -= 1
            continue
        finally:
            if transport:
                transport.close()
        return True
    return False


def interactive_push_file(dest_host, user, password, local_file, remote_file, app_user,
                          app_password, retry_times=3, **kwargs):
    LOG.info('push file %s to remote host %s', local_file, remote_file)
    ssh = None
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        kwargs.setdefault('timeout', 30)
        kwargs.setdefault('allow_agent', False)
        kwargs.setdefault('look_for_keys', False)
        try:
            ssh.connect(dest_host, username=user, password=password, **kwargs)
        except paramiko.ssh_exception.AuthenticationException as e:
            LOG.error('can not connect to host %s, auth error %s', dest_host, str(e))
            return 'auth error', C.RMA_UN_REACHABLE
        except Exception as e:
            LOG.error('can not connect to host %s, auth error %s', dest_host, str(e))
            return 'connect failed', C.RMA_UN_REACHABLE
        ssh.get_transport().set_keepalive(3)
        # the copy command need width of teminal
        invoke_shell = ssh.invoke_shell(width=180)
        invoke_shell.settimeout(kwargs['timeout'])
        # local_ip, local_port = invoke_shell.get_transport().sock.getsockname()

        # cmd = "sudo curl -o %s -k -v -u %s:%s https://%s/rma/file/%s" % (remote_file, app_user,
        #                                                                  app_password, local_ip, local_file)
        cmd = "sudo curl -C - -o %s -k -v https://%s/rma/file/%s" % (remote_file, cfg.CONF.global_ip, local_file)
        # invoke_shell.sendall(b'show %s\n' % 'version')
        # invoke_shell.sendall(b'start shell sh\n')
        time.sleep(3)
        while retry_times > 0:
            res, code = interactive_shell_linux_with_conn(invoke_shell, cmd)
            if res != '' and code == C.RMA_ACTIVE:
                return res, code
            time.sleep(5)
            retry_times -= 1
        return res, code
    finally:
        if ssh:
            ssh.close()


def interactive_push_file_with_conn(ssh_session, local_file, remote_file, app_user, app_password, retry_times=3):
    LOG.info('push file %s to remote host %s', local_file, remote_file)
    # local_ip, local_port = ssh_session.get_transport().sock.getsockname()

    # cmd = "sudo curl -o %s -k -v -u %s:%s https://%s/rma/file/%s" % (remote_file, app_user,
    #                                                                 app_password, local_ip, local_file)
    cmd = "sudo curl -C - -o %s -k -v https://%s/rma/file/%s" % (remote_file, cfg.CONF.global_ip, local_file)

    while retry_times > 0:
        res, code = interactive_shell_linux_with_conn(ssh_session, cmd)
        last_res = res[-200:]
        error_prompts = ['ERROR', 'Error', 'error', 'No such file or directory']
        if not any([error_prompt in last_res for error_prompt in error_prompts]) and code == C.RMA_FAILED:
            code = C.RMA_ACTIVE

        if res != '' and code == C.RMA_ACTIVE and 'failed' not in res and 'Failed' not in res:
            return res, code
        retry_times -= 1
        time.sleep(5)
    return res, code


def interactive_shell_linux_with_conn(ssh_session, cmd, error_fn=None):
    LOG.info('execute cmd %s', cmd)

    ssh_session.sendall(b'bash "%s"\n' % cmd)
    res, status_code = _recv(ssh_session, prompt=LINUX_PROMPT, error_fn=error_fn)
    LOG.debug('execute cmd %s, result:[%s], status code:[%s]', cmd, res, status_code)
    time.sleep(2)
    return _format_command_result(res), status_code


def interactive_shell_cli_with_conn(ssh_session, cmd):
    LOG.info('execute cmd %s', cmd)

    ssh_session.sendall(b'%s\n' % cmd)
    res, status_code = _recv(ssh_session, prompt=LINUX_PROMPT)
    return _format_command_result(res), status_code


def get_interactive_session(*args, **kwargs):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    kwargs.setdefault('timeout', 10)
    kwargs.setdefault('allow_agent', False)
    kwargs.setdefault('look_for_keys', False)
    try:
        ssh.connect(*args, **kwargs)
    except paramiko.ssh_exception.AuthenticationException as e:
        sw_host = args[0] if args else kwargs['hostname']
        LOG.error('can not connect to host %s, auth error %s', sw_host, str(e))
        return 'auth error', C.RMA_UN_REACHABLE
    except Exception as e:
        sw_host = args[0] if args else kwargs['hostname']
        LOG.error('can not connect to host %s, auth error %s', sw_host, str(e))
        return 'connect failed', C.RMA_UN_REACHABLE
    ssh.get_transport().set_keepalive(3)
    invoke_shell = ssh.invoke_shell()
    invoke_shell.settimeout(kwargs['timeout'])
    # invoke_shell.sendall(b'start shell sh\n')
    _recv(invoke_shell, prompt=LINUX_PROMPT)
    return invoke_shell, C.RMA_ACTIVE


def interactive_multi_shell_linux(cmds, *args, **kwargs):
    ssh = None
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        kwargs.setdefault('timeout', 10)
        kwargs.setdefault('allow_agent', False)
        kwargs.setdefault('look_for_keys', False)
        try:
            ssh.connect(*args, **kwargs)
        except paramiko.ssh_exception.AuthenticationException as e:
            LOG.error('can not connect to host %s, auth error %s', args[0], str(e))
            return 'auth error', C.RMA_UN_REACHABLE
        except Exception as e:
            LOG.error('can not connect to host %s, auth error %s', args[0], str(e))
            return 'connect failed', C.RMA_UN_REACHABLE
        ssh.get_transport().set_keepalive(3)
        invoke_shell = ssh.invoke_shell()
        invoke_shell.settimeout(10.0)
        # invoke_shell.sendall(b'show %s\n' % 'version')
        # invoke_shell.sendall(b'start shell sh\n')

        _recv(invoke_shell, prompt=LINUX_PROMPT)
        res = []
        for cmd in cmds:

            one_re, status_code = interactive_shell_linux_with_conn(invoke_shell, cmd)
            if status_code != C.RMA_ACTIVE:
                res.append(one_re)
                return res, status_code

            res.append(one_re)

        return res, C.RMA_ACTIVE
    finally:
        if ssh:
            ssh.close()


def interactive_shell_linux(cmd, *args, **kwargs):
    sw_host = args[0] if args else kwargs['hostname']
    LOG.info('%s execute cmd %s', sw_host, cmd)
    ssh = None
    error_fn = kwargs.pop('error_fn', None)
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        kwargs.setdefault('timeout', 10)
        kwargs.setdefault('allow_agent', False)
        kwargs.setdefault('look_for_keys', False)
        try:
            ssh.connect(*args, **kwargs)
        except paramiko.ssh_exception.AuthenticationException as e:
            LOG.error('can not connect to host %s, auth error %s', sw_host, str(e))
            return 'auth error', C.RMA_UN_REACHABLE
        except Exception as e:
            LOG.error('can not connect to host %s, auth error %s', sw_host, str(e))
            return 'connect failed', C.RMA_UN_REACHABLE

        ssh.get_transport().set_keepalive(3)
        invoke_shell = ssh.invoke_shell()
        invoke_shell.settimeout(kwargs['timeout'])
        # invoke_shell.sendall(b'show %s\n' % 'version')
        # invoke_shell.sendall(b'start shell sh\n')

        _recv(invoke_shell, prompt=LINUX_PROMPT)
        return interactive_shell_linux_with_conn(invoke_shell, cmd, error_fn=error_fn)
    finally:
        if ssh:
            ssh.close()


def interactive_shell_cli(cmd, *args, **kwargs):
    sw_host = args[0] if args else kwargs['hostname']
    LOG.info('%s execute cmd %s', sw_host, cmd)
    ssh = None
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        kwargs.setdefault('timeout', 10)
        kwargs.setdefault('allow_agent', False)
        kwargs.setdefault('look_for_keys', False)
        try:
            ssh.connect(*args, **kwargs)
        except paramiko.ssh_exception.AuthenticationException as e:
            LOG.error('can not connect to host %s, auth error %s', sw_host, str(e))
            return 'auth error', C.RMA_UN_REACHABLE
        except Exception as e:
            LOG.error('can not connect to host %s, auth error %s', sw_host, str(e))
            return 'connect failed', C.RMA_UN_REACHABLE

        ssh.get_transport().set_keepalive(3)
        invoke_shell = ssh.invoke_shell()
        invoke_shell.settimeout(kwargs['timeout'])
        # invoke_shell.sendall(b'show %s\n' % 'version')
        # invoke_shell.sendall(b'start shell sh\n')

        _recv(invoke_shell, prompt=LINUX_PROMPT)
        return interactive_shell_cli_with_conn(invoke_shell, cmd)
    finally:
        if ssh:
            ssh.close()


@contextmanager
def open_interactive_connection(*args, **kwargs):
    """
        implements with
        example: with open_interactive_connection('************', username='test', password='12345678') as client:
                        print client.execute(cmd)
                        print client.execute(cmd1)
                        print client.execute(cmd2)
                        print client.execute(cmd3)
    :param args: ('************')
    :param kwargs: {'username': a, 'password': pw}
    :return:
    """
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    kwargs.setdefault('timeout', 10)
    ssh.connect(*args, **kwargs)
    ssh.get_transport().set_keepalive(3)
    invoke_shell = ssh.invoke_shell()
    invoke_shell.settimeout(kwargs['timeout'])
    kwargs.setdefault('allow_agent', False)
    kwargs.setdefault('look_for_keys', False)
    # invoke_shell.sendall(b'show %s\n' % 'version')
    # invoke_shell.sendall(b'start shell sh\n')
    # _recv(invoke_shell, prompt=LINUX_PROMPT)

    class Client:
        def execute(self, cmd):
            return interactive_shell_linux_with_conn(invoke_shell, cmd)

    ssh_client = Client()
    # ssh_client.execute('start shell sh')
    _recv(invoke_shell, prompt=LINUX_PROMPT)

    try:
        yield ssh_client
    finally:
        if ssh:
            ssh.close()


def interactive_upgrade_with_conn(ssh_session, cmd):
    LOG.info('execute cmd %s', cmd)
    ssh_session.sendall(b'bash "%s"\n' % cmd)
    error_prompts = ['ERROR', 'Error', 'error']

    recv = ''
    flag = C.RMA_ACTIVE
    times = 0
    if LINUX_PROMPT:
        regex = re.compile(LINUX_PROMPT, re.I)

    while True:
        time.sleep(0.1)
        try:
            if ssh_session.closed:
                flag = C.RMA_FAILED
                break
            
            if not ssh_session.recv_ready():
                continue

            data = ssh_session.recv(256)
            recv += data
            if regex and regex.search(recv):
                times += 1
                recv = ''
                if times > 1:
                    break
        except socket.timeout:
            LOG.warn('upgrade timeout for msg')
            break
        except Exception as e:
            LOG.exception(e)
            flag = C.RMA_FAILED
            recv += str(e)
            break

    if any([error_prompt in recv for error_prompt in error_prompts]):
        flag = C.RMA_FAILED

    return _format_command_result(recv), flag


def _recv(ssh_shell, prompt=None, error_fn=None):
    recv = ''
    flag = C.RMA_ACTIVE
    if prompt:
        regex = re.compile(prompt, re.I)
        head_regex = re.compile('^' + prompt, re.I)
    exec_time = datetime.datetime.now()
    while True:
        try:
            if ssh_shell.closed:
                LOG.error('SSH session broken')
                flag = C.RMA_FAILED
                return recv, flag
            
            current_time = datetime.datetime.now()
            if (current_time - exec_time).total_seconds() >= 60 * RECV_TIMEOUT :
                assert False, "Error Timeout for {0} min".format(RECV_TIMEOUT)

            data = ssh_shell.recv(1024)
            recv += data
            if head_regex and head_regex.search(recv[:120]):
                recv = head_regex.sub('', recv)
                head_regex = None
            if regex and regex.search(recv[-1200:]):
                break
        except Exception as e:
            LOG.exception(e)
            flag = C.RMA_FAILED
            recv += str(e)
            break

    if not error_fn:
        if not ssh_helper.error_judge(recv):
            flag = C.RMA_FAILED
    else:
        if not error_fn(recv):
            flag = C.RMA_FAILED

    return recv, flag


def _format_command_result(res):
    result_lines = res.split('\r\n')
    return '\n'.join(result_lines[1:-1])


def close_session(channel):
    try:
        if channel:
            channel.close()
            transport = channel.transport
            if transport:
                transport.close()
    except:
        pass


if __name__ == '__main__':
    host = '************'
    user = 'test'
    password = '12345678'
    # interactive_shell_linux('cat /pica/config/pica_startup.boot', '************', username='test', password='12345678')
    # print interactive_shell_linux('/pica/bin/system/fan_status -s | grep MotherBoard', '************', username='admin', password='pica8')
    # print interactive_shell_linux('version', host, username=user, password=password)
    # print ssh_execute_with_host('************', 'admin', 'pica8', 'version')
    # print interactive_shell_linux('version', '************', username='admin', password='pica8')
    # push_file('************', 'admin', 'pica8', 'str_helper.py', '/home/<USER>/str_helper.py')
    # revision = 'e78d529'
    # cmd = "df -lh | awk \'/\/$/ {print $4 }\' | head -n 1"
    # cmd = "/pica/bin/pica_sh -c 'configure;show all | no-more'"
    # cmd = "/pica/bin/system/fan_status -s"
    # cmd = "show running-config | no-more"
    # cmd = "sudo upgrade2 /cftmp/PICOS-2.11.7-hotfix-auto-as4610-d3b1da2.tar.gz"
    # cmd = "license -s"
    # n = 0
    # while n < 100:
    #     res, status = interactive_shell_linux(cmd, host, username=user, password=password, timeout=100)
    #     print status
    #     print (0, res)
    #     print n
    #     if not res or res == '':
    #         break
    #     n += 1
    #     time.sleep(1)
    ssh_session, status = get_interactive_session('***********', username='test',
                                                  password='12345678',
                                                  timeout=120)

    res, status = interactive_upgrade_with_conn(ssh_session,
                                                'sudo upgrade2 /cftmp/PICOS-2.11.21-as4610-812efd2.tar.gz')
