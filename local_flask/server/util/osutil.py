import os
import datetime


def ensure_path(path):

    path = path.rstrip('/')
    if path == '/' or path == '':
        return

    if os.path.exists(path):
        return

    if os.path.isfile(path):
        path = os.path.dirname(path)

    parent_path = os.path.dirname(path)
    if not os.path.exists(parent_path):
        ensure_path(parent_path)

    os.mkdir(path)


def compare_time(start_time, end_time):
    now = datetime.datetime.now()
    d_start = datetime.datetime.strptime(start_time, '%H:%M')
    d_end = datetime.datetime.strptime(end_time, '%H:%M')

    if now.hour == d_start.hour == d_end.hour:
        return d_start.minute <= now.minute <= d_end.minute

    if now.hour == d_start.hour:
        return now.minute >= d_start.minute

    if now.hour == d_end.hour:
        return now.minute <= d_end.minute

    return (d_start.hour < now.hour < d_end.hour) or \
           (d_end.hour < d_start.hour and ((d_start.hour < now.hour <= 23) or (0 <= now.hour < d_end.hour)))


if __name__ == '__main__':
    ensure_path('tmp/EC1731000333')
