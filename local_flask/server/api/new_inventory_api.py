import logging
import datetime, time
import traceback

from munch import Munch

from flask import Blueprint, request, render_template, Response, jsonify
from flask_login import current_user
from sqlalchemy import or_

from server.db.models.inventory import Switch, Group, SwitchPort, ModelPhysicPort, SwitchSystemInfo, SwitchMenuTreeInfo, SwitchNeInfo, SwitchGis
from server.db.models.inventory import inven_db, PushConfigTask, PushConfigTaskDetails
from server.db.models.monitor import monitor_db
from server.db.models.general import GeneralConfig
from server.db.models.general import general_db
from server.south_api import ssh_api
from server.util import utils
from server import constants as C
from server import cfg
from server.util.permission import super_user_permission, super_admin_permission, admin_permission
from celery_app import my_celery_app
from celery_app.automation_task import AmpConBaseTask
from sqlalchemy import not_

new_inventory_mold = Blueprint('new_inventory_mold', __name__, template_folder='templates')
Log = logging.getLogger(__name__)


@new_inventory_mold.route('/switch/<string:sn>/<string:port_type>/ports')
@admin_permission.require(http_exception=403)
def get_switch_ports(sn, port_type):
    ports = inven_db.get_collection(SwitchPort, filters={'switch_sn': [sn]})
    if not ports:
        switch = inven_db.get_model(Switch, filters={'sn': [sn]})
        ports = inven_db.get_collection(ModelPhysicPort, filters={'platform_name': [switch.platform_model]})
    if port_type != 'all':
        if port_type == 'physic':
            ports = filter(
                lambda port: port.type == 'ge' or port.type == 'te' or port.type == 'qe' or port.type == 'xe',
                ports)
        elif port_type == 'ae':
            ports = filter(lambda port: port.type == 'ae', ports)
    res = [port.port_name for port in ports]
    return jsonify({"status": 200, "data": res})


@new_inventory_mold.route('/platform/<string:platform_name>/ports')
@admin_permission.require(http_exception=403)
def get_platform_ports(platform_name):
    ports = inven_db.get_collection(ModelPhysicPort, filters={'platform_name': [platform_name]})
    res = [port.port_name for port in ports]
    return jsonify({"status": 200, "data": res})


@new_inventory_mold.route('/get_switch_tree_info')
def get_switch_tree_info():
    res = dict()
    res["apiResult"] = "success"
    res["apiMessage"] = ""
    res["neInfo"] = list()
    res["groupInfo"] = list()
    try:
        # switches = inven_db.get_collection(Switch, filters={'status': ['Provisioning Success', 'Imported']})
        ne_switches = inven_db.get_collection(SwitchNeInfo)
        for ne_switch in ne_switches:
            switch = inven_db.get_model(Switch, filters={'sn': [ne_switch.sn]})
            if switch:
                ne_dict = dict()
                ne_dict["id"] = f"switch:{switch.sn}"
                ne_dict["value"] = {}
                ne_dict["value"]["host"] = switch.sn
                ne_dict["value"]["port"] = 22
                ne_dict["value"]["name"] = switch.sn
                ne_dict["value"]["username"] = switch.system_config.switch_op_user if switch.system_config else ""
                ne_dict["value"]["password"] = switch.system_config.switch_op_password if switch.system_config else ""
                ne_dict["value"]["ne_id"] = f"{switch.mgt_ip}:22"
                ne_dict["value"]["state"] = ""
                group_id = ne_switch.switch_menu_tree_id
                ne_dict["value"]["group"] = "switchRoot" if group_id == 1 else str(group_id)
                ne_dict["value"]["type"] = "switch"
                ne_dict["value"]["runState"] = switch.reachable_status
                ne_dict["value"]["lng"] = switch.gis.longitude if switch.gis else ""
                ne_dict["value"]["lat"] = switch.gis.latitude if switch.gis else ""
                res["neInfo"].append(ne_dict)
        session = inven_db.get_session()
        groups = session.query(SwitchMenuTreeInfo).filter(SwitchMenuTreeInfo.group_name.notin_(["switchRoot", "otnRoot"]), SwitchMenuTreeInfo.group_id < 10000).all()
        for group in groups:
            group_dict = dict()
            group_dict["id"] = str(group.group_id)
            group_dict["value"] = {}
            group_dict["value"]["name"] = group.group_name
            group_dict["value"]["parentKey"] = "switchRoot" if group.parent_group_id == 1 else str(group.parent_group_id)
            group_dict["value"]["lng"] = group.longitude
            group_dict["value"]["lat"] = group.latitude
            res["groupInfo"].append(group_dict)
    except Exception as e:
        Log.exception(traceback.format_exc())
        res["apiResult"] = "fail"
        res["apiMessage"] = "Get switch map info failed"
    finally:
        return jsonify(res)


@new_inventory_mold.route('/get_avaliable_switch_tree', methods=['GET'])
def get_avaliable_switch_tree():
    try:
        session = inven_db.get_session()
        sn_list = [i.sn for i in session.query(SwitchNeInfo)]
        switch_list = session.query(Switch).filter(not_(Switch.sn.in_(sn_list)), Switch.status.in_(['Provisioning Success', 'Imported'])).all()
        avaliable_sn = [i.sn for i in switch_list]
    except Exception as e:
        Log.exception(traceback.format_exc())
        info={"apiResult": "fail", "apiMessage": "Get avaliable switch list failed"}
    else:
        info = {"apiResult": "complete", "apiMessage": "Get avaliable switch list success", "data": avaliable_sn}
    finally:
        return jsonify(info)


@new_inventory_mold.route('/get_switch_details', methods=['POST'])
def get_switch_details():
    info = {}
    try:
        data = request.get_json()
        sn = data.get("sn", "")
        session = inven_db.get_session()
        switch_details = session.query(Switch).filter(Switch.sn == sn).first()
        switch_detail = switch_details.make_dict()
        port_map = inven_db.get_model_port_mapping(switch_details.platform_model)
        switch_detail.update({"port_map": port_map})
    except Exception as e:
        Log.exception(traceback.format_exc())
        info = {"apiResult": "fail", "apiMessage": "Get switch details failed"}
    else:
        info = {"apiResult": "complete", "apiMessage": "Get switch details success", "data": switch_detail}
    finally:
        return jsonify(info)


@new_inventory_mold.route('/switch_list/add', methods=['POST'])
def add_switch_info():
    info={}
    """
          group: "switchRoot"
          host: "*************" -- sn
          name: "233"
          password: "admin123.."
          port: "822"
          type: "5"
          username: "admin
    """
    try:
        data = request.get_json()
        session = inven_db.get_session()
        sni = SwitchNeInfo()
        sni.sn = data["host"]
        sni.switch_menu_tree_id = 1 if data["group"] == "switchRoot" else data["group"]
        inven_db.insert(sni, session)
    except Exception as e:
        Log.exception(traceback.format_exc())
        info= {"apiResult": "fail", "apiMessage": "Add switch info failed"}
    else:
        info = {"apiResult": "complete", "apiMessage": "Add switch info success"}
    finally:
        return jsonify(info)


@new_inventory_mold.route('/switch_list/edit', methods=['POST'])
def edit_switch_info():
    info={}
    """
          {
              key: "config:ne:*************:830"，
              data: {
                "name": "234",
                "username": "admin",
                "password": "Optical@1tnms"
              }
          }
    """
    try:
        data = request.get_json()
        Log.error(data)
        old_host = data["oldHost"]
        new_host = data["newHost"]
        session = inven_db.get_session()
        switch_ne = session.query(SwitchNeInfo).filter(SwitchNeInfo.sn == old_host).first()
        switch_tree_info = session.query(SwitchNeInfo).filter(SwitchNeInfo.switch_menu_tree_id == switch_ne.switch_menu_tree_id)
        switch_tree_info.update({"sn": new_host})
    except Exception as e:
        Log.exception(traceback.format_exc())
        info= {"apiResult": "fail", "apiMessage": "Update switch info failed"}
    else:
        info = {"apiResult": "complete", "apiMessage": "Update switch info success"}
    finally:
        return jsonify(info)


@new_inventory_mold.route('/switch_list/del', methods=['POST'])
def del_switch_info():
    """
        host: "*************" -- sn
        port: "822"
    """
    try:
        data = request.get_json()
        inven_db.delete_collection(SwitchNeInfo, filters={'sn': [data["host"]]})
    except Exception as e:
        Log.exception(traceback.format_exc())
        info = {"status": 500, "apiResult": "fail", "apiMessage": "Del switch info failed"}
    else:
        info = {"status": 200, "apiResult": "complete", "apiMessage": "Del switch info success"}
    finally:
        return jsonify(info)


@new_inventory_mold.route('/map/set_switch_location', methods=['POST'])
def set_switch_location():
    """
        neList: [neKey1]
        groupList: [groupKey1,...],
        lng: xxx,
        lat: xxx
    """
    try:
        data = request.get_json()
        ne_list = data["neList"]
        group_list = data.get("groupList", [])
        lng = data["lng"]
        lat = data["lat"]
        session = inven_db.get_session()
        with session.begin(subtransactions=True):
            if group_list:
                for group_key in group_list:
                    switch_tree_info = session.query(SwitchMenuTreeInfo).filter(SwitchMenuTreeInfo.group_id == group_key)
                    switch_tree_info.update({"longitude": lng, "latitude": lat})
            for ne_key in ne_list:
                switch = session.query(SwitchGis).filter(SwitchGis.sn == ne_key.replace("switch:", "").strip())
                if switch.first():
                    switch.update({"longitude": lng, "latitude": lat})
                else:
                    sg = SwitchGis()
                    sg.sn = ne_key.replace("switch:", "").strip()
                    sg.longitude = lng
                    sg.latitude = lat
                    inven_db.insert(sg)
    except Exception as e:
        Log.exception(traceback.format_exc())
        info = {"apiResult": "fail", "apiMessage": "Set switch location failed"}
    else:
        info = {"apiResult": "complete", "apiMessage": "Set switch location success"}
    finally:
        return info


@new_inventory_mold.route('/add_tree_switch_group',methods=['POST'])
def add_tree_switch_group():
    info = {}
    try:
        data = request.get_json()
        name = data["name"]
        parent_key = data["parentKey"]
        session = inven_db.get_session()
        smti = SwitchMenuTreeInfo()
        smti.group_name = name

        switch_tree_group_info = session.query(SwitchMenuTreeInfo).filter(SwitchMenuTreeInfo.group_name == name.strip()).first()
        if switch_tree_group_info:
            raise ValueError("The group name already exists")
        smti.parent_group_id = 1 if parent_key == "switchRoot" else parent_key
        inven_db.insert(smti, session)
    except ValueError as v:
        Log.exception(str(v))
        info = {"apiResult": "fail", "apiMessage": str(v)}
    except Exception as e:
        Log.exception(traceback.format_exc())
        info = {"apiResult": "fail", "apiMessage": "Add group info failed"}
    else:
        info = {"apiResult": "complete", "apiMessage": "Add group info success"}
    finally:
        return jsonify(info)


@new_inventory_mold.route('/del_tree_switch_group', methods=['POST'])
def del_tree_switch_group():
    info = {}
    try:
        data = request.get_json()
        group_key = data["key"]
        session = inven_db.get_session()
        switch_tree_info = session.query(SwitchMenuTreeInfo).filter(SwitchMenuTreeInfo.group_id == group_key)
        if not switch_tree_info:
            raise ValueError("Group not found")
        switch_tree_info.delete()
        switch_ne_obj = session.query(SwitchNeInfo).filter(SwitchNeInfo.switch_menu_tree_id == group_key)
        if switch_ne_obj.first():
            switch_ne_obj.update({"switch_menu_tree_id": 1})
    except ValueError as v:
        info = {"apiResult": "fail", "apiMessage": str(v)}
    except Exception as e:
        Log.exception(traceback.format_exc())
        info = {"apiResult": "fail", "apiMessage": "Delete group info failed"}
    else:
        info = {"apiResult": "complete", "apiMessage": "Delete group info success"}
    finally:
        return jsonify(info)


@new_inventory_mold.route('/edit_tree_switch_group', methods=['POST'])
def edit_tree_switch_group():
    info = {}
    try:
        data = request.get_json()
        group_key = data["key"]
        session = inven_db.get_session()
        switch_tree_info = session.query(SwitchMenuTreeInfo).filter(SwitchMenuTreeInfo.group_id == group_key)
        if not switch_tree_info:
            raise ValueError("Group not found")
        group_name = data["name"]
        switch_tree_info.update({"group_name": group_name})
    except ValueError as v:
        info = {"apiResult": "fail", "apiMessage": str(v)}
    except Exception as e:
        Log.exception(traceback.format_exc())
        info = {"apiResult": "fail", "apiMessage": "update group info failed"}
    else:
        info = {"apiResult": "complete", "apiMessage": "update group info success"}
    finally:
        return jsonify(info)


@new_inventory_mold.route('/update_tree_switch_group', methods=['POST'])
def update_tree_switch_group():
    info = {}
    try:
        data = request.get_json()
        source_id = data["sourceId"]
        session = inven_db.get_session()
        if ":" in source_id:
            sn = source_id.split(":")[1]
            new_group_id = 1 if data["targetId"] == "switchRoot" else data["targetId"]
            session.query(SwitchNeInfo).filter(SwitchNeInfo.sn == sn).update({"switch_menu_tree_id": new_group_id})
        else:
            source_id = data["sourceId"]
            sn = data["targetId"].split(":")[1]
            drag_obj = session.query(SwitchNeInfo).filter(SwitchNeInfo.sn == sn).first()
            session.query(SwitchMenuTreeInfo).filter(SwitchMenuTreeInfo.group_id == source_id).update({"parent_group_id": drag_obj.switch_menu_tree_id})
    except ValueError as v:
        info = {"apiResult": "fail", "apiMessage": str(v)}
    except Exception as e:
        Log.exception(traceback.format_exc())
        info = {"apiResult": "fail", "apiMessage": "Update group info failed"}
    else:
        info = {"apiResult": "complete", "apiMessage": "Update group info success"}
    finally:
        return jsonify(info)


@new_inventory_mold.route('/get_user_group', methods=['GET'])
def get_user_group():
    all_group = utils.get_user_group().all()
    res = [{"id": group.id, "name": group.group_name, "group_type": group.group_type} for group in all_group]
    return jsonify({"status": 200, "data": res})


@new_inventory_mold.route('/batch_apply_config', methods=['POST'])
@admin_permission.require(http_exception=403)
@utils.operation_log(method='apply_config', contents='apply config "{config_name}"')
def batch_apply_configs():
    session = inven_db.get_session()
    params = request.get_json()
    configs = params['config']
    config_name = params['configName']
    
    switch_check_info = params.get("switchChecked", [])
    group_check_info = params.get("groupChecked", [])
    # check_all = bool(check_info['checkall'])
    
    now = datetime.datetime.now()
    task_name = f"{now}::{config_name}"
    pct = PushConfigTask(task_name=task_name, task_content=configs, task_status=0, read_tag=0, create_user=current_user.id,
                         create_time=now, modified_time=now)
    with session.begin(subtransactions=True):
        session.add(pct)
    push_configs.delay(switch_check_info, group_check_info, configs, celery_task_name=task_name)
    return jsonify({"status": 200, "info": "Push configs task create success"})


@my_celery_app.task(name="new_push_configs", base=AmpConBaseTask)
def push_configs(*args, **kwargs):
    session = inven_db.get_session()
    switch_check, group_check, configs = args
    task_name = kwargs.get("celery_task_name")
    switches = []
    
    if switch_check:
        switch_sns = [node['sn'] for node in switch_check]
        switches = inven_db.get_collection(Switch, filters={'sn': switch_sns}, session=session)
        
    if group_check:
        group_names = [node['group_name'] for node in group_check]
        groups = inven_db.get_collection(Group, filters={'group_name': group_names}, session=session)
        
        filter_ip = set()
        for group in groups:
            group_switches = inven_db.get_group_switchs_new(group.group_name, session=session)
            for switch in group_switches:
                if switch.mgt_ip in filter_ip:
                    continue
                filter_ip.add(switch.mgt_ip)
                switches.append(switch)

    report_tag = "<push config report> "
    session.query(PushConfigTask).filter(PushConfigTask.task_name == task_name).update({"task_status": 1, "modified_time": datetime.datetime.now()})

    for switch in switches:
        with session.begin(subtransactions=True):
            pctd = PushConfigTaskDetails(name=task_name, sn=switch.sn, push_status=0)
            session.add(pctd)

    for switch in switches:
        try:
            user, password = (None, None) if switch.sn != C.PICOS_V_SN else (C.PICOS_V_USERNAME, C.PICOS_V_PASSWORD)
            session.query(PushConfigTaskDetails).filter(PushConfigTaskDetails.name == task_name, PushConfigTaskDetails.sn == switch.sn).update({"push_status": 1, "push_time" : datetime.datetime.now()})
            res, res2, status = ssh_api.apply_configs_by_push_conf(switch.mgt_ip, configs, sn=switch.sn, user=user, password=password)
            if status != C.RMA_ACTIVE:
                reason = res[-1200:] if len(res) > 1200 else res
                info = 'failed apply config to switch %s, reason %s, Config: %s ....' % (
                switch.sn, reason, configs[0:150])
                session.query(PushConfigTaskDetails).filter(PushConfigTaskDetails.name == task_name, PushConfigTaskDetails.sn == switch.sn).update({"push_status": 3, "push_time" : datetime.datetime.now(), "push_ret": res})
                monitor_db.add_event(switch.sn, 'error', info, session=session)
                inven_db.add_switch_log(switch.sn, report_tag + info, 'push_config', level='error')
            else:
                session.query(PushConfigTaskDetails).filter(PushConfigTaskDetails.name == task_name, PushConfigTaskDetails.sn == switch.sn).update({"push_status": 2, "push_time" : datetime.datetime.now(), "push_ret": res+"\r\n"+res2})
                info = 'apply config to switch %s success, Config: %s ....' % (switch.sn, configs[0:150])
                monitor_db.add_event(switch.sn, 'info', info, session=session)
                inven_db.add_switch_log(switch.sn, report_tag + info, 'push_config', level='info')
        except Exception as e:
            info = 'failed apply config to switch %s, reason %s, Config: %s ....' % (
            switch.sn, str(e), configs[0:150])
            monitor_db.add_event(switch.sn, 'error', info, session=session)
            inven_db.add_switch_log(switch.sn, report_tag + info, 'push_config', level='error')
            session.query(PushConfigTaskDetails).filter(PushConfigTaskDetails.name == task_name, PushConfigTaskDetails.sn == switch.sn).update({"push_status": 3, "push_time" : datetime.datetime.now(), "push_ret": res})

    session.query(PushConfigTask).filter(PushConfigTask.task_name == task_name).update({"task_status": 2, "modified_time": datetime.datetime.now()})

