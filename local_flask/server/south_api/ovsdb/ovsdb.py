from ovs.db import idl
import logging
from ovs.db.idl import Transaction
from ovs import poller
from ovs import vlog

vlog.Vlog.set_level('any', 'any', 'dbg')

schema_path = 'ovsdb.ovsschema'
PROBE_INTERVAL = 30000
LOG = logging.getLogger(__name__)


class OvsdbManager(object):

    _managers = {}

    def __init__(self, sn, ip, port=6640):
        self.sn = sn
        self.ip = ip
        self.remote = 'tcp:' + ip + ':' + str(port)
        self.disconnect = 0
        self.idl = idl.Idl(self.remote, self.init_schema(), PROBE_INTERVAL)
        self.start()

    def start(self):
        seq_no = self.idl.change_seqno
        i = 0
        self.disconnect = 0
        while seq_no == self.idl.change_seqno and i < 20:
            self.idl.run()
            if self.check_disconnect():
                break
            poller_inst = poller.Poller()
            self.idl.wait(poller_inst)
            poller_inst.block()
            i += 1

    def check_disconnect(self):
        if not self.idl._session.is_connected():
            self.disconnect += 1
        else:
            self.disconnect = 0
        return self.disconnect >= 3

    def init_schema(self):
        schema = idl.SchemaHelper(schema_path)
        schema.register_columns("Physical_Switch", ["management_ips", "tunnel_ips"])
        schema.register_columns("Physical_Port", ["name", "vlan_bindings"])
        schema.register_columns("Logical_Switch", ["name", "tunnel_key"])
        schema.register_columns("Physical_Locator", ["dst_ip", "encapsulation_type"])
        schema.register_columns("Ucast_Macs_Remote", ["MAC", "locator", "logical_switch"])
        schema.register_columns("Ucast_Macs_Local", ["MAC", "locator", "logical_switch"])
        schema.register_table("Mac_Vlan")
        schema.register_table("Mac_learning")
        return schema

    @classmethod
    def get_ovsdb_manager(cls, *args, **kwargs):
        if args[0] in cls._managers:
            return cls._managers.get(args[0])
        else:
            _manager = cls(*args, **kwargs)
            cls._managers[args[0]] = _manager
            return _manager

    def handle_commit(self, txn):
        # commit transaction
        seqno = self.idl.change_seqno
        LOG.debug('%s before idl commit seqno %s', self.remote, seqno)
        status = txn.commit_block()

        LOG.debug('%s after idl commit seqno %s', self.remote, seqno)
        if status in (Transaction.SUCCESS, Transaction.UNCHANGED):
            LOG.debug('%s idl commit success, current seqno %s', self.remote, self.idl.change_seqno)
            return True
        # elif status == Transaction.TRY_AGAIN:
        #     LOG.info('idl commit return try_again, current seqno %s', self.idl.change_seqno)
        #     retry_times = 0
        #     while seqno == self.idl.change_seqno and retry_times < 3:
        #         self.idl.run()
        #         retry_times += 1
        #     if seqno == self.idl.change_seqno:
        #         return False
        #     else:
        #         return True
        else:
            LOG.error('%s idl commit failed, status %s, current seqno %s, reason [%s]', self.remote, status,
                      self.idl.change_seqno, txn.get_error())
            return False

    def block_wait(self, seqno):
        # avoid idl lost connect, it can cause lost commit and changes
        self.idl.force_reconnect()
        self.disconnect = 0
        while seqno == self.idl.change_seqno and not self.idl.run():
            self.check_disconnect()
            if self.disconnect >= 3:
                break
            poller_ = poller.Poller()
            self.idl.wait(poller_)
            poller_.block()

    def reinit_idl(self):
        self.idl.close()
        self.idl = idl.Idl(self.remote, self.init_schema(), PROBE_INTERVAL)
        self.start()

    def run(self):
        if self.idl.txn:
            self.idl.txn.abort()
        self.idl.run()

    def get_table(self, table_name):
        self.run()
        return self.idl.tables[table_name].rows

    def close(self):
        if self.idl:
            self.idl.close()


class HostControlOvsdb(OvsdbManager):

    mac_table = 'Mac_Vlan'

    def __init__(self, *args, **kwargs):
        # a index for mac_vlan table
        # self.indexes = {}
        super(HostControlOvsdb, self).__init__(*args, **kwargs)

    def init_schema(self):
        schema = idl.SchemaHelper(schema_path)
        schema.register_table(self.mac_table)
        return schema

    def add_mac_vlan(self, mac, vlan, priority=0):
        """ priority: 1~7"""
        self.block_wait(self.idl.change_seqno)
        self.idl.run()
        txn = idl.Transaction(self.idl)
        mac_vlan = txn.insert(self.idl.tables[self.mac_table])
        mac_vlan.MAC = mac
        mac_vlan.vlan = vlan
        mac_vlan.priority = priority
        return self.handle_commit(txn)

    def del_mac_vlan(self, mac):
        self.block_wait(self.idl.change_seqno)
        txn = idl.Transaction(self.idl)
        rows = self.idl.tables[self.mac_table].rows
        # uuid = self.indexes.get(mac + '_' + str(vlan), None)
        # if uuid:
        #     rows[uuid].delete()
        #     del self.indexes[mac + '_' + str(vlan)]
        # else:
        for row in rows.values():
            if row.MAC == mac:
                row.delete()
                break
        return self.handle_commit(txn)

    def update_mac_vlan(self, mac, new_vlan, priority=None):
        self.block_wait(self.idl.change_seqno)
        txn = idl.Transaction(self.idl)
        txn.add_comment("UPDATE_MAC_VLAN: %s, vlan %s" % (mac, new_vlan))
        rows = self.idl.tables[self.mac_table].rows
        # uuid = self.indexes.get(mac + '_' + str(old_vlan), None)
        # if uuid:
        #     row = rows[uuid]
        #     row.vlan = new_vlan
        #     if priority:
        #         row.priority = priority
        #     del self.indexes[mac + '_' + str(old_vlan)]
        #     self.indexes[mac + '_' + str(new_vlan)] = uuid
        # else:
        for row in rows.values():
            if row.MAC == mac:
                row.delete()
                # self.indexes[mac + '_' + str(new_vlan)] = row.uuid
                break

        mac_vlan = txn.insert(self.idl.tables[self.mac_table])
        mac_vlan.MAC = mac
        mac_vlan.vlan = new_vlan
        mac_vlan.priority = priority
        return self.handle_commit(txn)

    def get_mac_vlans(self):
        self.block_wait(self.idl.change_seqno)
        return self.idl.tables[self.mac_table].rows.values()

    def get_mac_vlan(self, mac):
        self.block_wait(self.idl.change_seqno)
        values = self.idl.tables[self.mac_table].rows.values()
        for value in values:
            if value.MAC == mac:
                return value


class MacLearning(OvsdbManager):

    def init_schema(self):
        schema = idl.SchemaHelper(schema_path)
        schema.register_table("Mac_learning")
        return schema

    def get_mac_learning(self):
        self.block_wait(self.idl.change_seqno)
        return self.idl.tables["Mac_learning"].rows.values()


if __name__ == '__main__':
    import time
    start = time.time()
    hc = MacLearning('test', '10.10.51.170')
    mac_vlans = hc.get_mac_learning()
    print(len(mac_vlans))
    for mac_vlan in mac_vlans:
        print(mac_vlan)

    print('%d' % (time.time() - start))
