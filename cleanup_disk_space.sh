#!/bin/bash

# 磁盘空间清理脚本
# 用于清理 /var 目录下占用大量空间的内容

echo "开始清理磁盘空间..."

# 1. 清理 Docker 未使用的卷
echo "1. 清理 Docker 未使用的卷..."
docker volume rm ampcon-base_web-dist openwifi_kafka_data openwifi_web-dist owls_kafka_data portainer_data 2>/dev/null || true

# 2. 清理悬空的 Docker 镜像和构建缓存
echo "2. 清理悬空的 Docker 镜像和构建缓存..."
docker image prune -f
docker builder prune -f

# 3. 清理旧的 snap 包
echo "3. 清理旧的 snap 包..."
sudo snap remove astral-uv core22 core24 firefox gnome-42-2204 snapd thunderbird 2>/dev/null || true

# 4. 清理已删除但残留的软件包配置
echo "4. 清理已删除但残留的软件包配置..."
sudo dpkg --purge $(dpkg --list | grep ^rc | awk '{ print $2; }') 2>/dev/null || true

# 5. 清理 journal 日志（保留最近一周）
echo "5. 清理 journal 日志..."
sudo journalctl --vacuum-time=7d

# 6. 清理 apt 缓存
echo "6. 清理 apt 缓存..."
sudo apt-get clean

echo "磁盘空间清理完成！"
echo "清理后磁盘使用情况："
df -h /var