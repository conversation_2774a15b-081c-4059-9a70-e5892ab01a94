#!/bin/bash
set -e

# ------------------- 参数配置 -------------------
# 根据type保留license_check
install_type="ampcon-smb" # ampcon-campus | ampcon-dc | ampcon-t | ampcon-smb
# 源代码路径
source_path="/home/<USER>/ampcon-base"
# 本地flask安装路径
dst_path="/home/<USER>"
# 清空安装路径时，忽略的文件
ignore_patterns=""

flask_new_port="5002"
redis_host="localhost"
mysql_ip_port="************:13306"
prometheus_url="localhost:9090"
rabbitmq_url="localhost:5672"
postgresql_ip_port="@************:15432"


# ------------------- 初始化 -------------------
if [[ "$EUID" -ne 0 ]]; then
  echo "This script must be run as root. Try: sudo $0"
  exit 1
fi

cd ${source_path}
git pull
AMPCON_BRANCH_NAME=$(git rev-parse --abbrev-ref HEAD)
version=$(git rev-parse HEAD | cut -c1-10)

mkdir -p ${dst_path}/server
cd ${dst_path}

shopt -s extglob
eval "rm -rf !($ignore_patterns)"

uv init -p 3.11.3
rm -rf .git

# ------------------- 基本镜像构建过程---------------------
cp -r ${source_path}/backend/server-ampcon/pip_libs ${dst_path}/pip_libs
cp -r ${source_path}/data/settings/outer/requirements.txt ${dst_path}/requirements.txt

sed -i 's/^\s*ryu==4\.34/# &/' requirements.txt
uv add --no-index --find-links=pip_libs/ -r requirements.txt --frozen --index https://pypi.tuna.tsinghua.edu.cn/simple
uv sync
uv pip install protobuf==3.20.0 -i https://pypi.tuna.tsinghua.edu.cn/simple

rm -rf pip_libs requirements.txt

# ------------------- main镜像构建过程---------------------
cp -r ${source_path}/backend/server-ampcon/* ${dst_path}
cp -r ${source_path}/data/settings/inner/* ${dst_path}/server/
cp -r ${source_path}/data/settings/outer/requirements.txt ${dst_path}/
mkdir -p /etc/ansible
cp -r ${source_path}/data/settings/outer/ansible.cfg /etc/ansible/ansible.cfg
cp -r ${source_path}/data/settings/outer/server.cnf ${dst_path}/server.cnf
cp -r ${source_path}/data/settings/encrypt_code.py ${dst_path}/encrypt_code.py
cp -r ${source_path}/data/custom_sql ${dst_path}/server/custom_sql

# ------------------- compose卷文件---------------------
cp -r ${source_path}/data/celery_app/celeryconfig.py ${dst_path}/server/celery_app/celeryconfig.py
cp -r ${source_path}/.env ${dst_path}/server/.env
cp -r ${source_path}/data/vpn ${dst_path}/server/vpn
cp -r ${source_path}/data/config_gen ${dst_path}/server/config_gen

# ------------------- 变量替换------------------- 
# 替换 /usr/share/automation 为 $dst_path
files=$(grep -rl '/usr/share/automation' "$dst_path/server")
if [ -z "$files" ]; then
  echo "没有找到包含 /usr/share/automation 的文件"
else
  echo "替换以下文件中的路径 (/usr/share/automation → $dst_path):"
  echo "$files"
  echo "$files" | xargs sed -i "s|/usr/share/automation|$dst_path|g"
  echo "替换完成"
fi

# 替换 mysql-service 为你指定的 IP:PORT
files=$(grep -rl 'mysql-service' "$dst_path/server")
if [ -z "$files" ]; then
  echo "没有找到包含 mysql-service 的文件"
else
  echo "替换以下文件中的路径 (mysql-service → $mysql_ip_port):"
  echo "$files"
  echo "$files" | xargs sed -i "s|mysql-service|${mysql_ip_port}|g"
  echo "替换完成"
fi

# 替换 redis-service 为你指定的 IP
files=$(grep -rl 'redis-service' "$dst_path/server")
if [ -z "$files" ]; then
  echo "没有找到包含 redis-service 的文件"
else
  echo "替换以下文件中的路径 (redis-service → $redis_host):"
  echo "$files"
  echo "$files" | xargs sed -i "s|redis-service|${redis_host}|g"
  echo "替换完成"
fi

# 替换flask port
sed -i "s/^port: .*/port: ${flask_new_port}/" ${dst_path}/server/automation.ini

# 修改 ampcon_engine.py 中的 debug 设置
sed -i "s/app.run(host=cfg.CONF.bind, port=cfg.CONF.port, debug=False)/app.run(host=cfg.CONF.bind, port=cfg.CONF.port, debug=True)/" ${dst_path}/server/ampcon_engine.py

# prometheus
sed -i "s/prometheus_url: prometheus:9090/prometheus_url: ${prometheus_url}/" ${dst_path}/server/automation.ini
sed -i "s/prometheus:9090/${prometheus_url}/" ${dst_path}/server/cfg.py

# rabbitmq
sed -i "s/rabbitmq-service:5672/${rabbitmq_url}/" ${dst_path}/server/celery_app/celeryconfig.py

# ------------------- PostgreSQL 仅 smb 修改 -------------------
if [[ "$install_type" == "ampcon-smb" ]]; then
  files=$(grep -rl '@postgresql' "$dst_path/server/alembic-postgresql.ini" | grep -rl '@postgresql' "$dst_path/server/automation.ini" )
  if [ -z "$files" ]; then
    echo "没有找到包含 @postgresql 的文件"
  else
    echo "替换以下文件中的路径 (@postgresql → $postgresql_ip_port):"
    echo "$files"
    echo "$files" | xargs sed -i "s|@postgresql|${postgresql_ip_port}|g"
    echo "替换完成"
  fi
fi

# ------------------- 文件处理 -------------------
license_dir="${dst_path}/server/license_check"

# 根据 install_type 确定需要保留的文件
case "$install_type" in
  "ampcon-campus")
    keep_file="ampcon_campus_license_check.py"
    ;;
  "ampcon-dc")
    keep_file="ampcon_dc_license_check.py"
    ;;
  "ampcon-t")
    keep_file="ampcon_t_license_check.py"
    ;;
  "ampcon-smb")
    keep_file="ampcon_campus_license_check.py"
    ;;
  *)
    echo "错误：不支持的 install_type: $install_type"
    exit 1
    ;;
esac

# 进入目录
cd "$license_dir" || { echo "目录不存在：$license_dir"; exit 1; }

# 删除不需要的 ampcon_* 文件，但保留目标文件
for file in ampcon_*_license_check.py; do
  if [[ "$file" != "$keep_file" ]]; then
    rm -f "$file"
  fi
done

# 保留 license_check.py 和目标文件
echo "已根据 install_type='$install_type' 保留："
echo "- $keep_file"
echo "- license_check.py"

# ------------------------------ 其他操作 -----------------------

# 替换key
cp "${source_path}/auto-deploy/pica-public-test.key" "${dst_path}/server/pica-public.key"
cp "${source_path}/.env" "${dst_path}/server/.env"
cp "${source_path}/release.json" "${dst_path}/release.json"

# 修改.env
cd ${dst_path}

# smb 的时候用 ampcon-campus 作为 key
version_key="$install_type"
if [[ "$install_type" == "ampcon-smb" ]]; then
  version_key="ampcon-campus"
fi

AMPCON_VERSION=$("${dst_path}/.venv/bin/python3" -c "import json; print(json.load(open('release.json'))['${version_key}']['version'])")
echo ">>>>>>>>>>>>>>> current export package version: ${version}    <<<<<<<<<<<<<<<<<<<<<"
sed -i "s/^PRO_TYPE=.*/PRO_TYPE=${version_key}/g" ${dst_path}/server/.env
sed -i "s/^REACT_APP_VERSION=.*/REACT_APP_VERSION=${version}/g" ${dst_path}/server/.env
sed -i "s/^REACT_APP_BRANCH=.*/REACT_APP_BRANCH=${AMPCON_BRANCH_NAME}/g" ${dst_path}/server/.env
sed -i "s/^REACT_APP_MAJOR_VERSION=.*/REACT_APP_MAJOR_VERSION=${AMPCON_VERSION}/g" ${dst_path}/server/.env

# ------------------- 运行脚本 -------------------
echo "cd ${dst_path}/server && ../.venv/bin/python3 ampcon_engine.py" > ${dst_path}/local_flask.sh
chmod +x ${dst_path}/local_flask.sh
ln -sf ${dst_path}/local_flask.sh /usr/local/bin/local_flask

echo "部署完成，可以执行 local_flask 启动服务"
