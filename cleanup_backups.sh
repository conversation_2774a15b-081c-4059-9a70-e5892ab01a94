#!/bin/bash

# 脚本用于清理 /usr/share/automation 目录下的备份文件以释放磁盘空间
# 备份目录特征为以 _bak 结尾

echo "检查将要删除的目录..."
du -sh /usr/share/automation/*_bak

echo ""
echo "注意：即将删除上述目录以释放磁盘空间"
read -p "确认删除吗? (输入 'yes' 确认): " confirmation

if [[ "$confirmation" != "yes" ]]; then
    echo "取消删除操作"
    exit 1
fi

echo "开始删除备份目录..."

# 删除所有以 _bak 结尾的备份目录
rm -rf /usr/share/automation/*_bak

echo "备份目录已删除"

# 显示删除后的磁盘使用情况
echo ""
echo "删除后磁盘使用情况:"
du -sh /usr/share/automation