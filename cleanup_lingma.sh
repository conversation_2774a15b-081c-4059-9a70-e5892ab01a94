#!/bin/bash

# 脚本用于清理 /root/.lingma 目录下的大文件夹以释放磁盘空间

echo "检查 /root/.lingma 目录下各文件夹的大小..."
du -sh /root/.lingma/*

echo ""
echo "最大的几个目录："
echo "1. index 目录 (7.5G) - 这可能是语言模型索引文件"
echo "2. bin 目录 (183M) - 可能包含二进制可执行文件"
echo "3. cache 目录 (159M) - 缓存文件"
echo "4. logs 目录 (48M) - 日志文件"

echo ""
echo "注意：index 目录是最大的目录 (7.5G)"
echo "在删除任何内容之前，请确认这些文件不是正在使用的"

read -p "是否要清理 index 目录以释放 7.5G 空间? (输入 'yes' 确认): " confirmation

if [[ "$confirmation" == "yes" ]]; then
    echo "开始删除 /root/.lingma/index 目录..."
    rm -rf /root/.lingma/index
    echo "index 目录已删除"
else
    echo "跳过删除操作"
fi

echo ""
echo "是否要清理其他缓存和日志文件以进一步释放空间?"
echo "这将清理 cache, logs, 和 tmp 目录"
read -p "确认清理? (输入 'yes' 确认): " confirmation2

if [[ "$confirmation2" == "yes" ]]; then
    echo "开始清理缓存和日志文件..."
    rm -rf /root/.lingma/cache
    rm -rf /root/.lingma/logs
    rm -rf /root/.lingma/tmp
    echo "缓存和日志文件已清理"
else
    echo "跳过清理缓存和日志文件"
fi

echo ""
echo "清理完成后剩余文件大小:"
du -sh /root/.lingma/* 2>/dev/null || echo "没有剩余文件或目录"